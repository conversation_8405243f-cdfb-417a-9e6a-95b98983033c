<?php

namespace TF\Application\Providers;

use Exception;
use PDO;
use Prado\Data\ActiveRecord\TActiveRecordCriteria;
use TF\Application\Entity\ObjectPermissions;

class ObjectPermissionsProvider
{
    private $finder;

    public function __construct()
    {
        $this->finder = ObjectPermissions::finder();
        $this->finder->getDbConnection()->setAttribute(PDO::ATTR_STRINGIFY_FETCHES, false);
    }

    /**
     * @return ObjectPermissions[]
     */
    public function getUserClassPermissions(string $className, int $userId, array $permissions = [ObjectPermissions::PERMISSION_READ, ObjectPermissions::PERMISSION_WRITE]): iterable
    {
        $data = $this->finder->findAll(
            'class = ? and user_id = ?',
            [
                $className,
                $userId,
            ]
        );

        return array_filter($data, function ($permission) use ($permissions) {
            return in_array($permission->permission, $permissions);
        });
    }

    public function hasPermission(int $permission, string $className, int $userId, int $objectId)
    {
        $criteria = new TActiveRecordCriteria();
        $criteria->Condition = 'permission = :permission and class = :className and user_id = :userId and object_id = :objectId';
        $criteria->Parameters[':permission'] = $permission;
        $criteria->Parameters[':className'] = $className;
        $criteria->Parameters[':userId'] = $userId;
        $criteria->Parameters[':objectId'] = $objectId;

        $permission = $this->finder->find($criteria);

        return null === $permission ? false : true;
    }

    public function grantPermissions(string $className, int $userId, array $permissions, int $objectId): void
    {
        foreach ($permissions as $permissionId) {
            if (!in_array($permissionId, ObjectPermissions::$permisionsMap)) {
                throw new Exception('Invalid object permisson provided');
            }

            $this->grantPermission($className, $userId, $permissionId, $objectId);
        }
    }

    public function grantPermission(string $className, int $userId, int $permissionId, int $objectId): void
    {
        $permission = new ObjectPermissions();
        $permission->class = $className;
        $permission->user_id = $userId;
        $permission->object_id = $objectId;
        $permission->permission = $permissionId;
        $permission->save();
    }

    public function getObjectPermissionedUsers(string $className, int $objectId, int $permission)
    {
        $criteria = new TActiveRecordCriteria();
        $criteria->Condition = 'permission = :permission and class = :className and object_id = :objectId';
        $criteria->Parameters[':permission'] = $permission;
        $criteria->Parameters[':className'] = $className;
        $criteria->Parameters[':objectId'] = $objectId;

        return $this->finder->findAll($criteria);
    }

    public function revokePermission(ObjectPermissions $permission)
    {
        $this->deletePermission($permission->id);
    }

    public function getUserObjectPermissions(string $className, int $userId, int $object_id): array
    {
        $criteria = new TActiveRecordCriteria();
        $criteria->Condition = 'user_id = :user_id and class = :className and object_id = :objectId';
        $criteria->Parameters[':className'] = $className;
        $criteria->Parameters[':objectId'] = $object_id;
        $criteria->Parameters[':user_id'] = $userId;

        return $this->finder->findAll($criteria);
    }

    /**
     * @param int $id
     *
     * @return int number of records deleted
     *
     * @soapmethod
     */
    private function deletePermission($id)
    {
        return $this->finder->deleteByPk($id);
    }
}
