<?php

namespace TF\Application\Providers;

use TF\Application\Entity\User;

class UserProvider
{
    private $finder;

    public function __construct()
    {
        $this->finder = User::finder();
    }

    public function getFinder()
    {
        return $this->finder;
    }

    public function getOrganization(string $countryCode, int $organizationId): ?User
    {
        return $this->finder->find(
            'username = :name',
            [
                ':name' => strtolower($countryCode) . '_' . $organizationId,
            ]
        );
    }

    public function getOrganizationUsers($groupId)
    {
        return $this->finder->findAll(
            'group_id = :group_id',
            [
                ':group_id' => $groupId,
            ]
        );
    }
}
