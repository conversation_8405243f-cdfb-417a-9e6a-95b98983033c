<?php

namespace TF\Application\Providers;

use Prado\Data\ActiveRecord\TActiveRecordCriteria;
use TF\Application\Entity\UserFarmings;

class UserFarmingsProvider
{
    private $finder;

    public function __construct()
    {
        $this->finder = UserFarmings::finder();
    }

    public function getAllFarmingIds()
    {
        $sql = 'SELECT farmings.id FROM su_users_farming as farmings';
        $farmings = $this->finder->findAllBySql($sql);

        return array_column($farmings, 'id');
    }

    public function findBy(array $params): ?UserFarmings
    {
        $conditionStr = '';
        $conditionParams = [];
        $conditionCounter = 0;

        foreach ($params as $column => $value) {
            if ($conditionCounter > 0) {
                $conditionStr .= ' AND ';
            }

            $conditionStr .= "{$column} = :{$column}";
            $conditionParams[":{$column}"] = $value;
            $conditionCounter++;
        }

        return $this->finder->find(
            $conditionStr,
            $conditionParams
        );
    }

    public function findAllBy(array $filter): array
    {
        $criteria = new TActiveRecordCriteria();
        $criteria->Condition = 'name = :name AND user_id = :userId';
        $criteria->Parameters[':name'] = $filter['name'];
        $criteria->Parameters[':userId'] = $filter['user_id'];

        return $this->finder->findAll($criteria);
    }
}
