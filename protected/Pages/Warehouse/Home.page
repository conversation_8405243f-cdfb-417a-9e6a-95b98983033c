<com:TContent ID="Content">
    <script data-main="lib/js/warehouse/index.js" src="lib/js_external/require.js"></script>
    <script type="text/javascript">
        define('global-config', [], function () {
            return {
                isSuperAdmin: '<%=$this->User->IsSuperAdmin%>',
                hasWarehouseRights: '<%=$this->User->hasWarehouseRights%>',
                userid: '<%=$this->User->UserID%>',
                groupid: '<%=$this->User->GroupID%>',
                sessionid: '<%=session_id()%>'
            }
        });
    </script>

    <com:TRepeater ID="Repeater" EnableViewState="false">
        <prop:ItemTemplate>
            <script type="text/javascript" src="<%#$this->Data%>"></script>
        </prop:ItemTemplate>
    </com:TRepeater>

    <div id="cc1" class="easyui-layout warehouse-style"  data-options="fit:true">
         <com:TConditional Visible="<%= LEGACY_MODE %>">
			<prop:TrueTemplate>
				<div data-options="region:'north'" style="height:40px; border-top: none;">
					<com:Application.Portlets.NavigationMenu ID="Navigation" />            
				</div>
			</prop:TrueTemplate>
        </com:TConditional>
        <div id="progress-win" style="width:400px;padding:5px;">
            <div id="progress" style="width:373px;height:40px;"></div>
        </div>

        <div data-options="region:'west',title:'Складове',split:true, iconCls:'icon-edit-geometry'" style="width:250px;padding:5px;">
            <div class="panel-nav">
                <span class="easyui-tooltip tooltip-f" data-options="position: 'right', content: 'Добавяне на склад'">
                    <a id="btn-add-warehouse" href="javaScript:void(0)" class="easyui-linkbutton l-btn l-btn-small l-btn-plain" data-options="iconCls: 'icon-add', plain: true"></a>
                </span>
                <span class="easyui-tooltip tooltip-f" data-options="position: 'right', content: 'Редактиране на склад'">
                    <a id="btn-edit-warehouse" href="javaScript:void(0)" class="easyui-linkbutton l-btn l-btn-small l-btn-plain" data-options="iconCls: 'icon-edit', plain: true"></a>
                </span>
                <span class="easyui-tooltip tooltip-f" data-options="position: 'right', content: 'Изтриване на склад'">
                    <a id="btn-delete-warehouse" href="javaScript:void(0)" class="easyui-linkbutton l-btn l-btn-small l-btn-plain" data-options="iconCls: 'icon-delete', plain: true"></a>
                </span>
                <span class="easyui-tooltip tooltip-f" data-options="position: 'right', content: 'Документи'">
                    <a id="btn-manage-documents" href="javaScript:void(0)" class="easyui-linkbutton l-btn l-btn-small l-btn-plain" data-options="iconCls: 'icon-documents', plain: true"></a>
                </span>
                <a href="javascript:void(0)" class="easyui-menubutton" data-options="menu:'#warehouse-settings-menu', iconCls: 'icon-settings', plain: true, hasDownArrow:false"></a>
                <div id="warehouse-settings-menu">
                    <div id="btn-manage-measures" iconCls="icon-measure-line">Мерни единици</div>
                    <div id="btn-manage-contragents" iconCls="icon-users">Контрагенти</div>
                    <div id="btn-manage-farms" iconCls="icon-users">Стопанства</div>
                    <div id="btn-manage-machines" iconCls="icon-users">Активи</div>
                    <div id="btn-manage-items" iconCls="icon-box">Артикули</div>
                    <div id="btn-manage-user-permissions" iconCls="icon-users">Права за работа</div>
                    <div id="btn-manage-warehouse-config-params" iconCls="icon-users">Кофигурационни параметри</div>
                    <div id="sub-nav-btn-manage-contragents-groups" iconCls="icon-measure-line">Групи контрагенти</div>
                    <div id="sub-nav-btn-manage-items-groups" iconCls="icon-users">Групи артикули</div>
                </div>
            </div>
            <div id="warehouses-tree"></div>
        </div>
        <div data-options="region:'center',border:false" style="padding:5px;">
            <div class="easyui-layout" data-options="fit: true, border: false">
                <div data-options="region:'center', fit: true">
                    <com:Application.Portlets.Warehouse.TransactionsGrid />
                </div>
            </div>
        </div>

    </div>

    <com:Application.Portlets.Warehouse.WarehouseModals ID="WarehouseModals"/>


    <div id="win-choose-sub-doc-export-type" class="easyui-window" title="Избор на експорт" data-options="iconCls:'icon-data', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true" style="padding: 10px;">
        <com:Application.Portlets.Warehouse.ChooseSubDocExportType />
    </div>

</com:TContent>
