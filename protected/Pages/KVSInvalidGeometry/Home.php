<?php

use TF\Application\Common\MTPage;
use TF\Engine\Plugins\Core\Layers\LayersController;

class Home extends MTPage
{
    public function onInit($param)
    {
        parent::onInit($param);

        $this->securePage();
    }

    /**
     * Secure Page - if the user try to open file_id on other user we redirect to Files.Home.
     */
    private function securePage()
    {
        $fileId = $this->Request['file_id'];

        if ($fileId) {
            $LayersController = new LayersController('Layers');

            $options = [
                'return' => ['t.user_id'],
                'where' => [
                    'id' => ['column' => 'id', 'compare' => '=', 'prefix' => 't', 'value' => $fileId],
                ],
            ];

            $result = $LayersController->getFiles($options, false, false);

            $userId = $result[0]['user_id'];

            if (!$userId || $userId != $this->User->GroupID) {
                $this->Response->redirect($this->Service->constructUrl('Files.Home'));
            }
        } else {
            $this->Response->redirect($this->Service->constructUrl('Files.Home'));
        }
    }
}
