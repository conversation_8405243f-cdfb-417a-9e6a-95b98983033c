<com:TContent ID="Content">
    <script type="text/javascript">
        var isSuperAdmin = '<%=$this->User->IsSuperAdmin%>';
        var hasContractsOwnWriteRights = '<%=$this->User->hasContractsOwnWriteRights%>';
        var userid = '<%=$this->User->UserID%>';
        var groupID = '<%=$this->User->GroupID%>';
        var wmsServer = '<%=WMS_SERVER%>';
        var mapPath = '<%=WMS_MAP_PATH%>';
        var isAdmin = '<%=$this->User->UserLevel == TF\Application\Common\Config::USERS_ADMIN_FLAG%>';
        var sessionid = '<%=session_id()%>';
    </script>
    <style>
        .icon-edit-geometry {
            background: url('themes/Main/icons/edit_geometry.png') no-repeat
        }

        .icon-event {
            background: url('themes/Main/icons/event.png') no-repeat
        }

        .icon-contract {
            background: url('themes/Main/icons/contract.png') no-repeat
        }

        .icon-datagrid {
            background: url('themes/Main/icons/datagrid.png') no-repeat
        }
    </style>
    <div class="easyui-layout" data-options="fit:true">
        <com:TConditional Visible="<%= LEGACY_MODE %>">
			<prop:TrueTemplate>
				<div data-options="region:'north'" style="height:40px; border-top: none;">
					<com:Application.Portlets.NavigationMenu ID="Navigation" />            
				</div>
			</prop:TrueTemplate>
        </com:TConditional>
        <!-- MainPage -->
        <div id='left_pane' data-options="region:'west'" style="width:330px; padding:5px;">
            <div class="easyui-layout" data-options="fit: true">
                <div data-options="region: 'north'" style="height: 62px;">
                    <div class="easyui-panel"
                         data-options="title: 'Справки', border: false, iconCls: 'icon-reports'">
                        <div style="background:#fafafa;height:27px;">
                            <hr style="margin: 0; border: none; height: 1px; background: #ddd;">
                        </div>
                    </div>
                </div>
                <div data-options="region: 'center'" style="border-top: none; ">
                    <div id="reports-tree" style="padding: 3px;"></div>
                </div>
            </div>
        </div>
        <div id='central_region' data-options="region:'center'" style="padding:5px;">
            <div class="easyui-layout" data-options="fit: true">
                <div data-options="region: 'north'" style="height: 62px;">
                    <div class="easyui-panel" data-options="title: 'Справки', border: false, iconCls: 'icon-datagrid'">
                        <div id="generic-toolbar" style="background:#fafafa;">
                            <div id="generic-toolbar-overlay" style="
                                display:none;
                                position: absolute;
                                background: #d5d5d5a6;
                                width: 100%;
                                height: 100%;
                                z-index: 999;">
                            </div>
                            <a href="javascript:void(0)" id="export-report" class="easyui-linkbutton" data-options="iconCls: 'icon-csv', plain: true">Експорт(xls)</a>
                            <a href="javascript:void(0)" id="map-report" class="easyui-linkbutton" data-options="iconCls:'icon-map',plain:true">Карта</a>
                            <a href="javascript:void(0)" id="filter-report" class="easyui-linkbutton" data-options="iconCls:'icon-filter',plain:true">Филтър</a>
                            <a href="javascript:void(0)" id="clear-filter-report" class="easyui-linkbutton" data-options="iconCls:'icon-clear-filter',plain:true">Покажи всички</a>
                            <div style="display:inline" id="include-subleased-in-report-menu">
                                <input type="checkbox" id="include-subleased-in-report"><label
                                    for="include-subleased-in-report">Включи преотдадени</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="main_layout" data-options="region: 'center'">
                    <!-- Plots Grids and layout -->
                    <div id="plots-report-layout" class="easyui-layout" data-options="fit:true">
                        <div data-options="region:'center', border:false">
                            <table id="plots-report-tables"></table>
                        </div>
                    </div>
                    <div id="plots-report-layout-detail" class="easyui-layout" data-options="fit:true">
                        <div data-options="region:'center', border:false">
                            <table id="plots-report-tables-detail"></table>
                        </div>
                    </div>
                    <div id="total-report-layout" class="easyui-layout" data-options="fit:true">
                        <div data-options="region:'center', border:false">
                            <table id="report-tables"></table>
                        </div>
                    </div>
                    <!-- Diary Grids and layout -->
                    <div id="diary-report-layout" class="easyui-layout" data-options="fit:true">
                        <div data-options="region:'center', border:false">
                            <table id="diary-report-tables"></table>
                        </div>
                    </div>
                    <div id="diary-report-detailed-layout" class="easyui-layout" data-options="fit:true">
                        <div data-options="region:'center', border:false">
                            <table id="diary-detailed-report-table"></table>
                        </div>
                    </div>
                    <div id="diary-plots-report-layout" class="easyui-layout" data-options="fit:true">
                        <div data-options="region:'center', border:false">
                            <table id="diary-plots-report-tables"></table>
                        </div>
                    </div>
                    <div id="diary-plots-report-by-fuel-layout" class="easyui-layout" data-options="fit:true">
                        <div data-options="region:'center', border:false">
                            <table id="diary-plots-fuel-report-tables"></table>
                        </div>
                    </div>

                    <div id="diary-plots-report-by-products-layout" class="easyui-layout" data-options="fit:true">
                        <div data-options="region:'center', border:false">
                            <table id="diary-plots-report-by-products-tables"></table>
                        </div>
                    </div>

                    <!-- Payments Grids and layout -->
                    <div id="paid-by-bank-report-tables-layout" class="easyui-layout" data-options="fit:true">
                        <div data-options="region:'center', border:false">
                            <table id="paid-by-bank-report-tables"></table>
                        </div>
                    </div>
                    <div id="paid-by-bank-and-natura-report-tables-layout" class="easyui-layout"
                         data-options="fit:true">
                        <div data-options="region:'center', border:false">
                            <table id="paid-by-bank-and-natura-report-tables"></table>
                        </div>
                    </div>
                    <div id="personal-use-report-tables-layout" class="easyui-layout" data-options="fit:true">
                        <div data-options="region:'center', border:false">
                            <table id="personal-use-report-tables"></table>
                        </div>
                    </div>

                    <div id="rest-report-tables-layout" class="easyui-layout"
                                             data-options="fit:true">
                                            <div data-options="region:'center', border:false">
                                                <table id="rest-report-tables"></table>
                                            </div>
                                        </div>

                    <div id="rest-report-stopped-layout" class="easyui-layout"
                            data-options="fit:true">
                        <div data-options="region:'center', border:false">
                            <div class="test" style="padding: 20px; text-align: center;">
                                <p>Справката е временно недостъпна. За да се сдобиете със същата информация, моля посетете подмодул <a target="_blank" href="index.php?page=Payroll.Home">Ведомост</a>. За повече информация се свържете с наш сътрудник.</p>
                                <p>Извиняваме се за причиненото неудобство!</p>
                            </div>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
    <!-- Report Filters -->
    <div id="win-filter-report" class="easyui-window" title="Филтриране" data-options="iconCls:'icon-filter', closed: true, resizable: false, minimizable: false,
        collapsible: false, maximizable: false, modal: true, closable:true">
        <div id="filter_container">
            <!-- Plots's Filters -->
            <div id="plots_total_filters">
                <com:Application.Portlets.Plots.PlotsFilter/>
            </div>
            <div id="plots_filters">
                <com:Application.Portlets.Plots.ReportFilter/>
            </div>
            <!-- Diary's Filters -->
            <div id="diary_summary_filters">
                <com:Application.Portlets.Diary.SummaryReportByPerformerFilter/>
            </div>
            <div id="diary_detailed_filters">
                <com:Application.Portlets.Diary.DetailedReportByPerformerFilter/>
            </div>
            <div id="diary_plots_filters">
                <com:Application.Portlets.Diary.PlotsReportsFilter/>
            </div>
            <!-- OwnerPayments Filters -->
            <div id="payments_by_bank_filters">
                <com:Application.Portlets.Payments.SummaryPaidByBankFilter/>
            </div>
            <div id="payments_by_bank_and_nat_filters">
                <com:Application.Portlets.Payments.SummaryPaidByBankFilter/>
            </div>
            <div id="personal_use_filters">
                <com:Application.Portlets.Payments.PersonalUseFilter/>
            </div>
            <div id="rest_filters">
                <com:Application.Portlets.Payments.RestFilter/>
            </div>
        </div>
    </div>

    <div id="win-report-map" class="easyui-window" title="Карта"
         data-options="iconCls:'icon-map',
		 closed: true,
		 resizable: false,
		 minimizable: false,
		 collapsible: false,
		 maximizable: true,
		 modal: true,
		 onMaximize:function (){map.render('report-map')},
		 closable:true"
         style="width:1024px; height:600px;" >

        <div id="layout-test" class="easyui-layout" data-options="fit:true">
            <div data-options="region:'center'" style="width:100%; height:100%;border: 0px">
                <div id="report-map" style="width:100%; height:100%;"></div>

            </div>
        </div>
    </div>

    <div id="win-download" class="easyui-dialog" title="Изтегляне на файл" style="width:260px;height:100px;"
         data-options="iconCls:'icon-files',resizable:true,modal:true,closed:true">
        <div style="text-align: center;padding:10px;">
            <a style="margin-top: 10px;" id="btn-download-file" href="" class="easyui-linkbutton"
               data-options="iconCls:'icon-files'" target="_blank" onClick="jQuery('#win-download').window('close');">Свали</a>
            <a style="margin-top: 10px;" id="btn-download-file-close" href="javaScript:void(0)"
               class="easyui-linkbutton" onClick="jQuery('#win-download').window('close');"
               data-options="iconCls:'icon-cancel'">Откажи</a>
        </div>
    </div>

    <div id="progress-win" style="width:400px;padding:5px;">
        <div id="progress" style="width:373px;height:40px;"></div>
    </div>


     <!-- typeahead targets must be bound to the body -->
    <div id="search-owner-name-typeahead-target" class="tt-menu" style="position: absolute;"></div>

    <com:TRepeater ID="Repeater" EnableViewState="false">
        <prop:ItemTemplate>
            <script type="text/javascript" src="<%#$this->Data%>"></script>
        </prop:ItemTemplate>
    </com:TRepeater>
</com:TContent>
