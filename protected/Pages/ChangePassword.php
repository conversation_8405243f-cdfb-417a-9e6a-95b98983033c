<?php

use TF\Application\Common\MTPage;
use TF\Engine\Plugins\Core\Users\UsersController;

class ChangePassword extends MTPage
{
    public function onPreInit($param)
    {
        parent::onPreInit($param);
        $this->MasterClass = 'LoginLayout';
    }

    public function onInit($param)
    {
        parent::onInit($param);
        $this->UsersController = new UsersController();

        if (!isset($_GET['user_id']) || !isset($_GET['hash'])) {
            $this->Response->redirect($this->Service->constructUrl('InvalidInfo'));
        } else {
            $check = $this->UsersController->CheckHashData((int)$_GET['user_id'], $_GET['hash']);
            if (false == $check) {
                $this->Response->redirect($this->Service->constructUrl('InvalidInfo'));
            }
        }
    }
}
