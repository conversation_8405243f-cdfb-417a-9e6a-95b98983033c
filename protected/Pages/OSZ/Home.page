<com:TContent ID="Content">
    
    <style>

        .osz-filter {
            display: inline-block;
            padding: 0 10px 10px 0;
            width: 300px;
        }

        .osz-filter input {
            width: 120px;
        }
        
        .osz-filter-checkbox {
            display: block;
        }
        
        .osz-filter-checkbox input {
            vertical-align: middle;
        }

    </style>

    <script type="text/javascript">
        var userid = '<%=$this->User->UserID%>';
        var sessionid = '<%=session_id()%>';
    </script>
    <div class="easyui-layout" data-options="fit:true">
		<com:TConditional Visible="<%= LEGACY_MODE %>">
			<prop:TrueTemplate>
				<div data-options="region:'north'" style="height:40px; border-top: none;">
					<com:Application.Portlets.NavigationMenu ID="Navigation" />            
				</div>
			</prop:TrueTemplate>
        </com:TConditional>
        <div data-options="region:'west'" style="width:300px;height:500px; padding:5px;">
			<div class="easyui-layout" data-options="fit: true">
				<div data-options="region: 'north'" style="height: 62px;">
					<div class="easyui-panel" data-options="title: 'Файлове от ОСЗ', border: false, iconCls: 'icon-files'">
						<div style="background:#fafafa">
							<span class="easyui-tooltip" data-options="position: 'right', content: 'Добавяне'">
								<a id="add-osz-file-btn" class="easyui-linkbutton" data-options="iconCls: 'icon-add', plain: true"></a>
							</span>
							<span class="easyui-tooltip" data-options="position: 'right', content: 'Изтриване'">
								<a id="delete-osz-file-btn" class="easyui-linkbutton" data-options="iconCls: 'icon-delete', plain: true"></a>
							</span>
							<span class="easyui-tooltip" data-options="position: 'right', content: 'Филтриране'">
								<a id="filter-osz-files-btn" class="easyui-linkbutton" data-options="iconCls: 'icon-filter', plain: true"></a>
							</span>
							<span class="easyui-tooltip" data-options="position: 'right', content: 'Покажи всички'">
								<a id="clear-filter-osz-files-btn" class="easyui-linkbutton" data-options="iconCls: 'icon-clear-filter', plain: true"></a>
							</span>
						</div>
					</div>
				</div>
				<div data-options="region: 'center'" style="border-bottom: 0px; border-top: none;">
					<div id="osz-files-tree"></div>
				</div>
				<div data-options="region: 'south'" style="height: 33px; border-top: none; background: #fafafa">
					<hr style="margin: 0px; border: none; height: 2px; background: #ddd;">
					<div id="osz-files-tree-pagination"></div>
				</div>
			</div>
        </div>  
        <div data-options="region:'center'">
			<div id="contract-info-layout" class="easyui-layout" data-options="fit: true, border: false">
				<div data-options="region: 'north', border: false, split: true" style="height: 320px; padding: 5px;">
                    <div class="easyui-panel" data-options="title: 'Филтър', iconCls: 'icon-filter', fit:true">
                        
                        
                        
                        <fieldset style="border: 1px solid #000; padding: 10px; margin: 5px;">
                            <legend style="font-weight: bold; font-style: italic;">Информация</legend>
                            
                            <div class="osz-filter">
                                <input type="text" id="search-kad-no" />
                                <label for="search-kad-no">Идентификатор на имот</label>
                            </div>
                            <div class="osz-filter">
                                <input type="text" id="search-ime-subekt" />
                                <label for="search-ime-subekt">Име на ползвател</label>
                            </div>
                            <div class="osz-filter">
                                <input type="text" id="search-egn-subekt" />
                                <label for="search-egn-subekt">Идентификатор на ползвател</label>
                            </div>
                            <div class="osz-filter">
                                <input type="text" id="search-kategoria" />
                                <label for="search-kategoria">Категория</label>
                            </div>
                            <div class="osz-filter">
                                <input type="text" id="search-txt-ntp" />
                                <label for="search-txt-ntp">НТП</label>
                            </div>

                            <div class="osz-filter-checkbox">
                                <input type="checkbox" id="search-not-in-kvs" style="float: left;"/>
                                <label for="search-not-in-kvs">
                                <div style="width: 15px; height: 15px; border: 1px solid #000; background-color: #ffff00; float: left; margin-right: 5px; margin-left: 5px;"></div>
                                Покажи само имотите, които не фигурират в системно КВС</label>
                            </div>
                            <div class="osz-filter-checkbox">
                                <input type="checkbox" id="search-only-in-kvs" style="float: left;" />
                                 <div style="width: 15px; height: 15px; border: 1px solid #000; background-color: #0000ff; float: left; margin-right: 5px; margin-left: 5px;"></div>
                                <label for="search-only-in-kvs">Покажи само имотите от системното КВС, които не фигурират във файла</label>
                            </div>
                            <div class="osz-filter-checkbox">
                                <input type="checkbox" id="search-different-subekt" style="float: left;" />
                                 <div style="width: 15px; height: 15px; border: 1px solid #000; background-color: #db3333; float: left; margin-right: 5px; margin-left: 5px;"></div>
                                <label for="search-different-subekt">Покажи само имотите с договор, за които ползвателя от файла е различен</label>
                            </div>
                            
                            
                        </fieldset>
                        
                        <table width="100%" cellspacing="0" cellpadding="0" style="margin: 10px 0px;">
                            <tr>       
                                <td colspan="2" style="text-align:center;">
                                    <a id="filter-osz-files-plots-btn" class="easyui-linkbutton" data-options="iconCls:'icon-filter'">Филтрирай</a>
                                    <a id="add-filter-osz-files-plots-btn" class="easyui-linkbutton" data-options="iconCls:'icon-add-filter'">Добави към филтър</a>
                                    <a id="clear-filter-osz-files-plots-btn" class="easyui-linkbutton" data-options="iconCls:'icon-clear-filter'">Покажи всички</a>
                                </td>
                            </tr>
                        </table>
                        
                        <fieldset style="border: 1px solid #000; padding: 10px; margin: 5px;">
                            <legend style="font-weight: bold; font-style: italic;">Текущ филтър</legend>
                            
                            <div id="osz-current-filter">
                                
                            </div>
                            
                        </fieldset>
                        
                    </div>
				</div>
				<div data-options="region: 'center', border: false, split: true" style="padding: 5px;">
                    <div id="hypothecs-plots-grid"></div>
				</div>
			</div>
        </div>  
    </div>
    
    <div id="win-osz-files-filter" class="easyui-window" title="Филтър"
		 data-options="iconCls:'icon-filter', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
        <div style="padding: 5px 5px 5px 10px;">
            <fieldset style="border: 1px solid #000; padding: 11px 10px 5px 10px;">
                <legend style="font-weight: bold; font-style: italic;">Информация за файла</legend>
                <table class="filters">
                    <tr>
                        <td>Землище:</td>
                        <td>
                            <input type="text" id="search-land" style="width: 120px"/>
                        </td>
                    </tr>
                    <tr>
                        <td>ЕКАТТЕ:</td>
                        <td>
                            <input type="text" id="search-ekatte" style="width: 120px"/>
                        </td>
                    </tr>
                    <tr>
                        <td>Дата:</td>
                        <td>
                            <input type="text" id="search-date" style="width: 120px"/>
                        </td>
                    </tr>
                </table>
            </fieldset>
        </div>
        
        <table width="100%" cellspacing="0" cellpadding="0" style="margin: 5px 0px;">
            <tr>       
                <td colspan="2" style="text-align:center;">
                    <a id="search-osz-files-btn" class="easyui-linkbutton" data-options="iconCls:'icon-search'">Търси</a>
                    <a class="easyui-linkbutton" onClick="jQuery('#win-osz-files-filter').window('close');" data-options="iconCls:'icon-cancel'">Откажи</a>
                </td>
            </tr>
        </table>
    </div>
    
    <div id="win-add-file" class="easyui-window" title="Добавяне на файл" style="width:480px;height:450px;"
         data-options="iconCls:'icon-add', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true">
        
        <div style="padding: 5px 5px 5px 10px;">
            <fieldset style="border: 1px solid #000; padding: 11px 10px 5px 10px;">
                <legend style="font-weight: bold; font-style: italic;">Информация за файла</legend>
                <table class="filters">
                    <tr>
                        <td>Землище:</td>
                        <td>
                            <input type="text" id="land" style="width: 120px"/>
                        </td>
                        <td>ЕКАТТЕ:</td>
                        <td>
                            <input type="text" id="ekatte" style="width: 120px"/>
                        </td>
                    </tr>
                </table>
            </fieldset>
        </div>
        
        <div id="uploader">
            <p>You browser doesn't have Flash, Silverlight, Gears, BrowserPlus or HTML5 support.</p>	    
        </div>
        
        <table width="100%" cellspacing="0" cellpadding="0" style="margin-top: 5px;margin-bottom: 7px;">
            <tr>       
                <td colspan="2" style="text-align:center;">
                    <a id="save-osz-file-btn" class="easyui-linkbutton" data-options="iconCls:'icon-save'">Запази</a>
                    <a class="easyui-linkbutton" onClick="jQuery('#win-add-file').window('close');" data-options="iconCls:'icon-cancel'">Откажи</a>
                </td>
            </tr>
        </table>
    </div>

    <div id="win-download" class="easyui-dialog" title="Изтегляне на файл" style="width:260px;height:100px;"  
         data-options="iconCls:'icon-files',resizable:true,modal:true,closed:true"> 
        <div style="text-align: center;padding:10px;">
            <a style="margin-top: 10px;" id="btn-download-file" href="" class="easyui-linkbutton" data-options="iconCls:'icon-files'" target="_blank" onClick="jQuery('#win-download').window('close');">Свали</a>
            <a style="margin-top: 10px;" id="btn-download-file-close" href="javaScript:void(0)" class="easyui-linkbutton" onClick="jQuery('#win-download').window('close');" data-options="iconCls:'icon-cancel'">Откажи</a> 
        </div>  
    </div>

    <com:TRepeater ID="Repeater" EnableViewState="false">
        <prop:ItemTemplate>
            <script type="text/javascript" src="<%#$this->Data%>"></script>
        </prop:ItemTemplate>
    </com:TRepeater>

</com:TContent>