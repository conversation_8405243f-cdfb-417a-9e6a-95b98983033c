<com:TContent ID="Content">

    <com:TRepeater ID="Repeater" EnableViewState="false">
        <prop:ItemTemplate>
            <script type="text/javascript" src="<%#$this->Data%>"></script>
        </prop:ItemTemplate>
    </com:TRepeater>

    <script type="text/javascript">
        var userid = '<%=$this->User->UserID%>';
        var groupid = '<%=$this->User->GroupID%>';
        var sessionid = '<%=session_id()%>';
    </script>  

    <div class="easyui-layout" data-options="fit:true">
        <com:TConditional Visible="<%= LEGACY_MODE %>">
			<prop:TrueTemplate>
				<div data-options="region:'north'" style="height:40px; border-top: none;">
					<com:Application.Portlets.NavigationMenu ID="Navigation" />            
				</div>
			</prop:TrueTemplate>
        </com:TConditional>
        <div data-options="region:'west'" style="padding:5px;width:300px;">
            <div id="search-field" class="easyui-panel" title="Филтри"
                 style="width:288px;padding:10px;" data-options="iconCls:'icon-filter'">

                Име<br/>
                <input type="text" id="search-layer" class="easyui-validatebox" style="width:265px; margin-bottom: 0px;"/>
                <p></p>
                Стопанство<br/>
                <input type="text" id="search-farming" style="width: 265px;"/>
                <p></p>
                Стопанска година<br/>
                <input type="text" id="search-farming-year" style="width: 265px;"/>
                <p></p>
                <div style="margin-top: 10px">
	            	<a id="btn-search" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-search'" style="margin-left: 35px">Търси</a>
	            	<a id="btn-clear" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-cancel'" style="margin: 0px;">Откажи</a>
	            </div>
            </div>
        </div>
        <div data-options="region:'center'" style="padding:5px;">

            <table id="files-tables" ></table>

        </div>
    </div>

    <div id="win-srid" class="easyui-window" title="Данни" data-options="iconCls:'icon-data', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true" style="width:365px;">
        <com:Application.Portlets.Files.LayersProjection />
    </div>

    <div id="win-add-file" class="easyui-window" title="Добавяне на файл" data-options="iconCls:'icon-add', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true" style="width:450px;height:345px;">
        <com:Application.Portlets.Layers.LayersAdd />
    </div>

    <div id="win-definition" class="easyui-window" title="Дефиниция на КВС" data-options="iconCls:'icon-data', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true" style="width:420px;height:350px;">
        <com:Application.Portlets.Files.Definition />
    </div>

    <div id="win-kms-definition" class="easyui-window" title="Дефиниция на слой комасации" data-options="iconCls:'icon-data', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true" style="width:420px;height:220px;">
        <com:Application.Portlets.Files.KMSDefinition />
    </div>

    <div id="win-for-isak-definition" class="easyui-window" title='Дефиниция на слой "за ИСАК"' data-options="iconCls:'icon-data', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true" style="width:420px;height:220px;">
        <com:Application.Portlets.Files.ForIsakDefinition />
    </div>

    <div id="win-for-work-definition" class="easyui-window" title='Дефиниция на "Работен слой"' data-options="iconCls:'icon-data', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true" style="width:420px;height:220px;">
            <com:Application.Portlets.Files.WorkDefinition />
    </div>

    <div id="win-for-gps-definition" class="easyui-window" title='Дефиниция на слой "Временни данни"' data-options="iconCls:'icon-data', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true" style="width:420px;height:220px;">
            <com:Application.Portlets.Files.GspDefinition />
    </div>

    <div id="progress-win" style="width:400px;padding:5px;">
        <div id="progress" style="width:373px;height:40px;"></div>
    </div>

    <script type="text/javascript">
        setValidateSearchInput();
    </script>
</com:TContent>
