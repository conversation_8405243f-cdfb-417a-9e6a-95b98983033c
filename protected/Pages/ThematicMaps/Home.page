<com:TContent ID="Content">

	<script src="https://maps.google.com/maps/api/js?v=<%=GOOGLE_VERSION%>&amp;key=<%=GOOGLE_KEY%>"></script>
    <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>

    <com:TRepeater ID="Repeater" EnableViewState="false">
        <prop:ItemTemplate>
            <script type="text/javascript" src="<%#$this->Data%>"></script>
        </prop:ItemTemplate>
    </com:TRepeater>

    <script type="text/javascript">
        var isSuperAdmin = '<%=$this->User->IsSuperAdmin%>',
            hasSubsidyRights = '<%=$this->User->HasSubsidyRights%>',
            hasMapRightsRW = '<%=$this->User->HasMapRightsRW%>',
            userid = '<%=$this->User->GroupID%>',
            sessionid = '<%=session_id()%>',
            wmsServer = '<%=WMS_SERVER%>',
            mapPath = '<%=WMS_MAP_PATH%>',
            login3WmsServer = '<%=LOGIN3_WMS_SERVER%>',
            login3MapPath = '<%=LOGIN3_WMS_MAP_PATH%>',
            groupID = '<%=$this->User->GroupID%>',
            mapType = '<%=$_SESSION["map_type"]%>';
    </script>

    <div id="layout-test" class="easyui-layout" data-options="fit:true">
        <com:TConditional Visible="<%= LEGACY_MODE %>">
			<prop:TrueTemplate>
				<div data-options="region:'north'" style="height:40px; border-top: none;">
					<com:Application.Portlets.NavigationMenu ID="Navigation" />            
				</div>
			</prop:TrueTemplate>
        </com:TConditional>
        <div data-options="region:'center'" style="padding:5px;">
            <div id="map-layout" class="easyui-layout" data-options="fit:true" style="border:0px;">
                <div data-options="region: 'north', height: 32" style="border-bottom: none; background-color: #fafafa; padding: 1px 2px;overflow-y: inherit;" >
                    <div style="float: right;">
                        <span id="map-types-combobox" style="padding-top: 3px; padding-right: 3px; float: left;">
                            <input type="text" style="width: 200px;" />
                        </span>
                    </div>
                </div>
                <div data-options="region:'center', border: false">
                    <div id="inner-thematic-layout" class="easyui-layout" data-options="fit:true">
                        <div data-options="region:'center'" style="border-right: none;">
                            <div class="easyui-layout" data-options="fit: true">
                                <div data-options="region: 'center'">
                                    <div style="width:100%; height:100%; color:#000000;" id="map">
                                        <div class="logo"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div data-options="region:'east', collapsible: true, collapsed: true, title: 'Настройки'" style="width:270px;">
                            <table style="margin: 10px; width: 250px;">
                                <tr style="padding: 5px">
                                    <td>
                                        <label class="switch switch-left-right">
                                            <input class="switch-input" type="checkbox" id="open-thematic-maps-tools-button" checked />
                                            <span class="switch-label" data-on="Вкл." data-off="Изкл."></span>
                                            <span class="switch-handle"></span>
                                        </label>
                                    </td>
                                    <td>Покажи прозорец с инструменти</td>
                                </tr>
                                <tr>
                                    <td colspan="2">
                                        <hr class="fading-line">
                                    </td>
                                </tr>
                                <tr style="padding: 5px">
                                    <td>
                                        <label class="switch switch-left-right">
                                            <input class="switch-input" type="checkbox" id="open-thematic-maps-button" checked />
                                            <span class="switch-label" data-on="Вкл." data-off="Изкл."></span>
                                            <span class="switch-handle"></span>
                                        </label>
                                    </td>
                                    <td>Покажи тематични карти</td>
                                </tr>
                                <tr>
                                    <td colspan="2">
                                        <hr class="fading-line">
                                    </td>
                                </tr>
                                <tr style="padding: 5px">
                                    <td>Име:</td>
                                    <td><p id="thematic-map-name"></p></td>
                                </tr>
                                <tr style="padding: 5px">
                                    <td>Критерий:</td>
                                    <td><p id="thematic-map-criteria"></p></td>
                                </tr>
                                <tr>
                                    <td colspan="2">
                                        <hr class="fading-line">
                                    </td>
                                </tr>
                                <tr class="filters-in-settings-panel" style="padding: 5px">
                                    <td colspan="2">
                                         <fieldset style="border: 1px solid #000;">
                                            <legend style="font-weight: bold; font-style: italic; margin-left: 5px;">Филтри</legend>
                                            <div id='settings-panel-container' style="max-width: 250px;"></div>
                                        </fieldset>
                                    </td>
                                </tr>
                                <tr class="filters-in-settings-panel">
                                    <td colspan="2">
                                        <hr class="fading-line">
                                    </td>
                                </tr>
                                <tr style="padding: 5px">
                                    <td>
                                        <label class="switch switch-left-right">
                                            <input class="switch-input" type="checkbox" id="open-diagram-button"/>
                                            <span class="switch-label" data-on="Вкл." data-off="Изкл."></span>
                                            <span class="switch-handle"></span>
                                        </label>
                                    </td>
                                    <td>Покажи диаграма</td>
                                </tr>
                                <tr>
                                    <td colspan="2" style="padding-top: 10px; padding-bottom: 5px; vertical-align: middle;">
                                        <fieldset style="border: 1px solid #000;">
                                            <legend style="font-weight: bold; font-style: italic; margin-left: 5px;">Тип диаграма</legend>
                                            <input style="margin: 3px 20px 0 40px;" type="radio" name="diagram-type" id="circular-diagram-type" checked>
                                            <label for="circular-diagram-type">Кръгова диаграма</label>
                                            <br/>
                                            <input style="margin: 3px 20px 0 40px;" type="radio" name="diagram-type" id="bar-diagram-type">
                                            <label for="bar-diagram-type">Стълбчета</label>
                                        </fieldset>

                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="2" style="padding-top: 10px; padding-bottom: 5px;">
                                        <fieldset style="border: 1px solid #000;">
                                            <legend style="font-weight: bold; font-style: italic; margin-left: 5px;">Критерий за групиране</legend>
                                            <input style="margin: 3px 20px 0 40px;" type="radio" name="diagram-criteria" id="total-area-diagram-criteria" checked>
                                            <label for="total-area-diagram-criteria">По площ</label>
                                            <br/>
                                            <input style="margin: 3px 20px 0 40px;" type="radio" name="diagram-criteria" id="plot-count-diagram-criteria">
                                            <label for="plot-count-diagram-criteria">По брой</label>
                                        </fieldset>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="2">
                                        <hr class="fading-line">
                                    </td>
                                </tr>
                                <tr style="padding: 5px">
                                    <td>
                                        <label class="switch switch-left-right">
                                            <input class="switch-input" type="checkbox" id="open-legend-button"/>
                                            <span class="switch-label" data-on="Вкл." data-off="Изкл."></span>
                                            <span class="switch-handle"></span>
                                        </label>
                                    </td>
                                    <td>Покажи легенда</td>
                                </tr>
                                <tr>
                                    <td colspan="2">
                                        <hr class="fading-line">
                                    </td>
                                </tr>
                                    <td colspan="2" style="text-align: center;">
                                        <a href="javaScript:void(0)" class="easyui-menubutton" data-options="menu:'#printThematicMapsMenu', plain: false, iconCls: 'icon-print'">Отпечатай картата</a>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div data-options="region:'south'" style="border-top: none; padding:0;height:30px;overflow-y: inherit;">
                            <div style="float:left; padding: 5px">EPSG: 900913</div>
                            <div id="coords" style="float:right;width:250px;text-align: right;border-left: 1px solid #ccc; padding: 5px">x: ,y: </div>
                            <div id="scale" style="float: right; margin-right: 10px; ">
                                Мащаб 1:
                                <span id="scale-denominator">
                                    <input type="text" style="width: 75px; margin: 4px 0px 3px 3px" />
                                </span>
                                <span class="easyui-tooltip" data-options="position: 'top', content: 'Мащабиране'">
                                    <a style="margin: 0px 3px 0px 0px" href="javaScript:void(0)" id="tool-set-scale" class="easyui-linkbutton" data-options="iconCls: 'icon-zoom-full', plain: true"></a>
                                </span>
                                <input id="select-coord-system-for-map" type="text" style="width: 75px; text-align: left; margin-right: 5px;">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- typeahead targets must be bound to the body -->
    <div id="search-company-name-typeahead-target" class="tt-menu" style="position: absolute;"></div>
    <div id="search-owner-name-typeahead-target" class="tt-menu" style="position: absolute;"></div>

    <div id="win-thematic-maps-accordion" class="easyui-window" title="&nbsp;Инструменти за тематични карти" style="width:400px;max-height:670px;"
        data-options="iconCls:'icon-layers',resizable:false,modal:false,closed:true, closable: true, minimizable: false, maximizable:false, collapsed: true, draggable:false, onBeforeOpen:function(){}">
        <div id="thematic-maps-accordion">
            <div title="Тематични карти" data-options="iconCls:'icon-layers', selected:true, height: 200">
                <div id="thematic-maps-grid" style="height: 220px;"></div>
            </div>
            <div title="Диаграма" data-options="iconCls:'icon-chart'">
                <div id="thematic-map-chart-datagrid" style="width: 390px; max-height: 270px; overflow: hidden"></div>
            </div>
            <div title="Легенда" data-options="iconCls:'icon-legend', selected:true">
                <div id="thematic-map-legend-datagrid" style="max-height: 200px"></div>
            </div>
        </div>
    </div>

    <div id="win-new-thematic-maps" class="easyui-window" title="Нова тематична карта" style="width:400px; height:190px;padding: 10px"
        data-options="iconCls:'icon-add',resizable:false,modal:true,closed:true, closable: true, aminimizable: false, maximizable:false, collapsed: false, draggable:true, collapsible: false">
        <div class="easyui-layout" data-options='fit:true'>
            <div data-options="region:'north', border: false" style="height: 80px;">
                <input type="text" id="new-thematic-maps-name" style="margin-bottom: 10px; width: 200px" />
                <label for="new-thematic-maps-name">Наименование на картата</label>
                <input type="text" id="new-thematic-maps-main-layer" style="width: 200px" />
                <label for="new-thematic-maps-main-layer">Основен слой</label>
            </div>
            <div data-options="region:'south', border: false" style="text-align: right;padding:10px 10px 0 10px;">
                <a href="javaScript:void(0)" class="easyui-linkbutton" id="close-win-new-thematic-maps" data-options="iconCls:'icon-cancel'">Откажи</a>
                <a href="javaScript:void(0)" class="easyui-linkbutton" id="thematic-maps-proceed-to-step-2" data-options="iconCls:'icon-forward', iconAlign: 'right'">Напред</a>
            </div>
        </div>
    </div>

    <div id="win-new-thematic-maps-step2" class="easyui-window" title="Нова тематична карта" style="width:415px; height:320px; padding: 10px"
        data-options="iconCls:'icon-filter',resizable:false,modal:true,closed:true, closable: true, minimizable: false, maximizable:false, collapsed: false, draggable:true, collapsible: false">
        <div class="easyui-layout" data-options='fit:true'>
            <div data-options="region:'north', border: false">
                <fieldset style="border: 1px solid 000; padding: 5px; margin-bottom: 10px;">
                    <legend style="font-weight: bold; font-style: italic; margin-left: 5px; font-size: 16px">Филтър</legend>
                    <p class="thematic-subtitle">Изберете полетата за филтриране на данните, които ще бъдат визуализирани на тематичната карта</p>
                </fieldset>
                <input type="text" id="main-layer-columns" style="margin-bottom: 10px; width: 140px" /> <span id="equals-sign-span">= </span> <input type="text" id="main-layer-columns-value" style="width: 90px" /> <span class="easyui-tooltip" id="thematic-filter-tooltip" data-options="position: 'top', content: 'Поле за допълнителна информация, относно избраните полета за филтриране.'">
                        <a href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls: 'icon-info', plain: true"></a>
                    </span>
                <a href="javaScript:void(0)" id="add-new-thematic-maps-filter" class="easyui-linkbutton" data-options="iconCls:'icon-add'">Добави</a>
            </div>
            <div data-options="region:'center', border: false">
                <fieldset style="border: 1px solid 000; height: 98px; overflow-y: auto;">
                    <legend style="font-weight: bold; font-style: italic; margin-left: 5px;">Текущ филтър</legend>
                    <p id='thematic-maps-currently-applied-filters'></p>
                </fieldset>
            </div>
            <div data-options="region:'south', border: false" style="text-align: right;padding:10px 10px 0 10px;">
                <a href="javaScript:void(0)" class="easyui-linkbutton" id="close-win-new-thematic-maps-step2" data-options="iconCls:'icon-back'">Назад</a>
                <a href="javaScript:void(0)" class="easyui-linkbutton" id="thematic-maps-proceed-to-step-3" data-options="iconCls:'icon-forward', iconAlign: 'right'">Напред</a>
            </div>
        </div>
    </div>

    <div id="win-new-thematic-maps-step3" class="easyui-window" title="Нова тематична карта" style="width:400px; height:225px; padding: 10px"
        data-options="iconCls:'icon-colorfull-menu',resizable:false,modal:true,closed:true, closable: true, minimizable: false, maximizable:false, collapsed: false, draggable:true, collapsible: false">
        <div class="easyui-layout" data-options='fit:true'>
            <div data-options="region:'north', border: false" style="height: 130px;">
                <fieldset style="border: 1px solid 000; padding: 5px; margin-bottom: 10px;">
                    <legend style="font-weight: bold; font-style: italic; margin-left: 5px; font-size: 16px">Стойност за оцветяване</legend>
                    <p class="thematic-subtitle">Изберете колона,стойностите на която да бъдат оцветени на картата</p>
                </fieldset>
                <input type="text" id="new-thematic-map-color-criteria" style="width: 200px" />
            </div>
            <div data-options="region:'center', border: false" style="text-align: right;padding:0px 10px 0 10px;">
                <a href="javaScript:void(0)" class="easyui-linkbutton" id="close-win-new-thematic-maps-step3" data-options="iconCls:'icon-back'">Назад</a>
                <a href="javaScript:void(0)" class="easyui-linkbutton" id="thematic-maps-proceed-to-step-4" data-options="iconCls:'icon-forward', iconAlign: 'right'">Напред</a>
            </div>
        </div>
    </div>

    <div id="win-new-thematic-maps-step4" class="easyui-window" title="Нова тематична карта" style="width:400px; padding: 0 2px 10px 2px; height: 620px"
        data-options="iconCls:'icon-colorfull-menu',resizable:false,modal:true,closed:true, closable: true, minimizable: false, maximizable:false, collapsed: false, draggable:true, collapsible: false">
        <div class="easyui-layout" id="thematic-results-grid-layout" data-options='fit:true'>
            <div data-options="region:'north', border: false" style="padding: 10px 10px 0 10px; height: 135px;">
                <fieldset style="border: 1px solid 000; padding: 5px;">
                    <legend style="font-weight: bold; font-style: italic; margin-left: 5px; font-size: 16px">Редакция на цветовете</legend>
                    <p class="thematic-subtitle">Цветовете за всяка стойност са подбрани автоматично за най-добър изглед. Ако желаете може да промените темата на оцветяване или цвета на отделната категория.</p>
                    <p style="margin: 5px 0"><input type="text" id="new-thematic-maps-color-palettes" style="width: 200px" /> Тема</p>
                </fieldset>
            </div>
            <div data-options="region:'center', border: false">
                <div id="new-thematic-maps-results-grid" class="hide-input"></div>
            </div>
            <div data-options="region:'south', border: false" style="text-align: right;padding:10px 10px 0 10px;">
                <p id="warning-msg-for-many-results" style="display: none; text-align: left;">Избраната характеристика за оцветяване съдържа твърде много уникални стойности, препоръчваме Ви да филтрирате данните или да изберете по-подходящ критерий за оцветяване</p>
                <a href="javaScript:void(0)" class="easyui-linkbutton" id="close-win-new-thematic-maps-step4" data-options="iconCls:'icon-back'">Назад</a>
                <a href="javaScript:void(0)" class="easyui-linkbutton" id="thematic-maps-proceed-to-step-5" data-options="iconCls:'icon-forward', iconAlign: 'right'">Напред</a>
            </div>
        </div>
    </div>

    <div id="win-new-thematic-maps-step5" class="easyui-window" title="Нова тематична карта" style="width:600px; padding: 2px; height: 410px"
        data-options="iconCls:'icon-chart',resizable:false,modal:true,closed:true, closable: true, minimizable: false, maximizable:false, collapsed: false, draggable:true, collapsible: false">
        <div class="easyui-layout" data-options='fit:true'>
            <div data-options="region:'north', border: false" style="padding: 10px">
                <h2 class="thematic-title">Добавяне на диаграма</h2>
                <p class="thematic-subtitle">Избраните резултати могат да бъдат представени и като графика на общия брой или общата площ на полигоните от всеки цвят. Начинът на визуализация може да бъде сменен и по-късно.</p>
            </div>
            <div data-options="region:'center', border: false">
                <table>
                    <tr style="padding: 20px; text-align: center;">
                        <td style="width: 200px">
                            <label for="new-diagram-pie-chart-radio">
                                <img src="themes/Main/images/piechart.png" />
                            </label>
                        </td>
                        <td style="width: 270px">
                            <label for="new-diagram-bar-chart-radio">
                                <img src="themes/Main/images/barchart.png" />
                            </label>
                        </td>
                        <td style="vertical-align: middle; width: 100px; text-align: left;">
                            <input type="radio" name="newDiagramTypeCriteria" id="new-diagram-bar-chart-plot-count-radio" checked>
                            <label for="new-diagram-bar-chart-plot-count-radio">Общ брой</label>
                            <br />
                            <input type="radio" name="newDiagramTypeCriteria" id="new-diagram-bar-chart-area-radio">
                            <label for="new-diagram-bar-chart-area-radio">Обща площ</label>
                        </td>
                    </tr>
                    <tr style="padding: 20px; text-align: center;">
                        <td style="width: 200px">
                            <input type="radio" name="newDiagramType" id="new-diagram-pie-chart-radio" checked>
                            <label for="new-diagram-pie-chart-radio">Кръгова диаграма</label>
                        </td>
                        <td style="width: 270px">
                            <input type="radio" name="newDiagramType" id="new-diagram-bar-chart-radio">
                            <label for="new-diagram-bar-chart-radio">Стълбчета</label>
                        </td>
                        <td style="vertical-align: middle; width: 100px; text-align: left;">
                        </td>
                    </tr>
                </table>
            </div>
            <div data-options="region:'south', border: false" style="text-align: right;padding:10px;">
                <a href="javaScript:void(0)" class="easyui-linkbutton" id="close-win-new-thematic-maps-step5" data-options="iconCls:'icon-back'">Назад</a>
                <a href="javaScript:void(0)" class="easyui-linkbutton" id="thematic-maps-proceed-to-step-6" data-options="iconCls:'icon-ok'">Създай</a>
            </div>
        </div>
    </div>

    <div id="win-download" class="easyui-dialog" title="Изтегляне на файл" style="width:260px;height:100px;"
         data-options="iconCls:'icon-files',resizable:true,modal:true,closed:true">
        <div style="text-align: center;padding:10px;">
            <a style="margin-top: 10px;" id="btn-download-file" href="javaScript:void(0)" class="easyui-linkbutton" onClick="jQuery('#win-download').window('close');" data-options="iconCls:'icon-files'" target="_blank">Изтегли</a>
            <a style="margin-top: 10px;" href="javaScript:void(0)" class="easyui-linkbutton" onClick="jQuery('#win-download').window('close');" data-options="iconCls:'icon-cancel'">Откажи</a>
        </div>
    </div>

    <div id="printThematicMapsMenu" class="easyui-menu" style="width: 190px">
            <div>
                <span>Отпечатай видима част</span>
                <div id="print-thematic-map-button" style="width: 70px;">
                    <div style="width: 100px;" data-options="iconCls: 'icon-tree-document', plain: true" data-size="A0">Размер А0</div>
                    <div style="width: 100px;" data-options="iconCls: 'icon-tree-document', plain: true" data-size="A1">Размер А1</div>
                    <div style="width: 100px;" data-options="iconCls: 'icon-tree-document', plain: true" data-size="A2">Размер А2</div>
                    <div style="width: 100px;" data-options="iconCls: 'icon-tree-document', plain: true" data-size="A3">Размер А3</div>
                    <div style="width: 100px;" data-options="iconCls: 'icon-tree-document', plain: true" data-size="A4">Размер А4</div>
                </div>
            </div>
            <div>
                <span>Отпечатай цялата карта</span>
                <div id="print-thematic-map-button-full" style="width: 70px;">
                    <div style="width: 100px;" data-options="iconCls: 'icon-tree-document', plain: true" data-size="A0">Размер А0</div>
                    <div style="width: 100px;" data-options="iconCls: 'icon-tree-document', plain: true" data-size="A1">Размер А1</div>
                    <div style="width: 100px;" data-options="iconCls: 'icon-tree-document', plain: true" data-size="A2">Размер А2</div>
                    <div style="width: 100px;" data-options="iconCls: 'icon-tree-document', plain: true" data-size="A3">Размер А3</div>
                    <div style="width: 100px;" data-options="iconCls: 'icon-tree-document', plain: true" data-size="A4">Размер А4</div>
                </div>
            </div>
    </div>
</com:TContent>
