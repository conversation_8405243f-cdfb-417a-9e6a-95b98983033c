<?php

use TF\Application\Common\MTPage;
use TF\Engine\Plugins\Core\Layers\LayersController;

class Home extends MTPage
{
    public function onInit($param)
    {
        parent::onInit($param);

        $this->securePage();
    }

    /**
     * Secure Page - if the user try to open file_id on other user we redirect to Files.Home.
     */
    private function securePage()
    {
        $fileId = $this->Request['file_id'];

        if ($fileId) {
            $LayersController = new LayersController('Layers');

            $options = [
                'return' => ['t.group_id'],
                'where' => [
                    'id' => ['column' => 'id', 'compare' => '=', 'prefix' => 't', 'value' => $fileId],
                ],
            ];

            $result = $LayersController->getFiles($options, false, false);

            $groupId = $result[0]['group_id'];

            if (!$groupId || $groupId != $this->User->getGroupID()) {
                $this->Response->redirect($this->Service->constructUrl('Files.Home'));
            }
        } else {
            $this->Response->redirect($this->Service->constructUrl('Files.Home'));
        }
    }
}
