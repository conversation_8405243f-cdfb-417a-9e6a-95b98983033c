<?php

use TF\Application\Common\Config;
use TF\Application\Common\MTPage;
use TF\Engine\Plugins\Core\Users\UsersController;

class KeycloakLogin extends MTPage
{
    /**
     * onInit event.
     */
    public function onPreInit($param)
    {
        parent::onPreInit($param);

        $this->MasterClass = 'KeycloakLayout';
    }

    /**
     * onInit event.
     */
    public function onInit($param)
    {
        parent::onInit($param);

        $this->MasterClass = 'KeycloakLayout';

        $redirect = '';

        if (!$this->User->IsGuest) {
            if ($this->User->HasMapRightsR) {
                $redirect = 'index.php?page=Map.Home';
            } elseif ($this->User->HasPlotRightsR) {
                $redirect = 'index.php?page=Plots.Home';
            } elseif ($this->User->HasSubsidyRights) {
                $redirect = 'index.php?page=ZPlots.Home';
            } else {
                $this->Application->getModule(Config::AUTH_MODULE)->logout();
                $redirect = 'index.php?page=Home';
            }
            $this->Response->redirect($redirect);
        }

        $this->UsersController = new UsersController();
    }
}
