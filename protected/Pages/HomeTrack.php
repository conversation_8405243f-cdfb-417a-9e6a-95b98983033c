<?php

/**
 * Home class file.
 *
 * <AUTHOR>
 */

use TF\Application\Common\MTPage;

/**
 * Home class.
 *
 * Home page of Users plugin
 */
class HomeTrack extends MTPage
{
    public $cashiers = [];

    /**
     * onInit event.
     */
    public function onPreInit($param)
    {
        parent::onPreInit($param);

        // $this->MasterClass = 'LoginLayout';
    }

    /**
     * onInit event.
     */
    public function onInit($param)
    {
        parent::onInit($param);
    }
}
