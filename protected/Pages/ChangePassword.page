<com:TContent ID="Content">
    <com:TRepeater ID="Repeater" EnableViewState="false">
        <prop:ItemTemplate>
            <script type="text/javascript" src="<%#$this->Data%>"></script>
        </prop:ItemTemplate>
    </com:TRepeater>
    <script type="text/javascript" src="lib/js/users/users-login.js"></script>

    <div id="login-win"
         class="easyui-panel"
         title="Вход"
         style="width:450px;"
         cls="login-win"
         data-options="iconCls:'icon-password',closable:false,
         collapsible:false,minimizable:false,maximizable:false">
         <div class="login-form">
            <div class="login-form-logo"><img src="themes/Main/tf_logo.svg"></div>
            <div class="new-password-container">
                <label for="new-password">Нова парола:</label>
                <input type="password" id="new-password">
            </div>
            <div class="re-password-container">
                <label for="re-password">Потвърди паролата:</label>
                <input type="password" id="re-password">
            </div>
            <div id="btn-change-password-container">
                <a id="btn-change-password" href="javaScript:void(0)" class="easyui-linkbutton">Изпрати</a>
            </div>
         </div>
    </div>
    <script type="text/javascript">
    	var isGuest = '<%=$this->User->isGuest%>';
    </script>
</com:TContent>
