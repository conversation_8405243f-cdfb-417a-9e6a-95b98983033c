<?php

use TF\Application\Common\MTPage;

/**
 * Home class file.
 *
 * <AUTHOR>
 */

/**
 * Home class.
 *
 * Home page of Users plugin
 */
class Home extends MTPage
{
    public $cashiers = [];

    /**
     * onInit event.
     */
    public function onPreInit($param)
    {
        parent::onPreInit($param);

        $this->MasterClass = $this->getLoginLayout();
    }

    /**
     * Return default login form layout.
     */
    private function getLoginLayout(): string
    {
        if (true == getenv('KEYKLOACK_LOGIN_REQUIRED')) {
            return 'KeycloakLayout';
        }

        return 'LoginLayout';
    }
}
