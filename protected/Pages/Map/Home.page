<com:TContent ID="Content">
    <script type="text/javascript">
		var isSuperAdmin = '<%=$this->User->IsSuperAdmin%>';
		var hasSubsidyRights = '<%=$this->User->HasSubsidyRights%>';
		var hasMapRightsRW = '<%=$this->User->HasMapRightsRW%>';
		var userid = '<%=$this->User->GroupID%>';
		var sessionid = '<%=session_id()%>';
		var wmsServer = '<%=WMS_SERVER%>';
		var mapPath = '<%=WMS_MAP_PATH%>';
		var login3WmsServer = '<%=LOGIN3_WMS_SERVER%>';
		var login3MapPath = '<%=LOGIN3_WMS_MAP_PATH%>';
		var groupID = '<%=$this->User->GroupID%>';
		var mapType = '<%=$_SESSION["map_type"]%>';
		var hasKVSCuttingRights = '<%=$this->User->HasKVSCuttingRights%>';
    </script>

	<script src="https://maps.google.com/maps/api/js?v=<%=GOOGLE_VERSION%>&amp;key=<%=GOOGLE_KEY%>"></script>

    <com:TRepeater ID="Repeater" EnableViewState="false">
        <prop:ItemTemplate>
            <script type="text/javascript" src="<%#$this->Data%>"></script>
        </prop:ItemTemplate>
    </com:TRepeater>
    <div id="layout-test" class="easyui-layout" data-options="fit:true">
		<div data-options="region:'center'">
			<div class="easyui-layout" data-options="fit:true">
				<com:TConditional Visible="<%= LEGACY_MODE %>">
					<prop:TrueTemplate>
						<div data-options="region:'north'" style="height:40px; border-top: none;">
							<com:Application.Portlets.NavigationMenu ID="Navigation" />            
						</div>
					</prop:TrueTemplate>
				</com:TConditional>

				<div data-options="region:'center'" style="padding:5px;">
					<div id="map-layout" class="easyui-layout" data-options="fit:true" style="border:0;">

						<div data-options="region: 'north', height: 36" style="border-bottom: none; background-color: #fafafa; padding: 1px 2px;" >
							<div style="float: left; margin-top:3px;">
								<span style="padding: 0 0 0 5px;" data-options="iconCls: 'icon-edit-geometry'">Активен слой:</span>
								<div id="all-layers-tree"></div>
							</div>

							<div id="all-layers-tree-cm" class="easyui-menu" style="width:120px;">
								<div onclick="zoomToLayerExtent()" data-options="iconCls:'icon-zoom-full'">Мащабиране</div>
								<div onclick="showAttrInfo()" data-options="iconCls:'icon-datagrid'">Атрибутна информация</div>
								<div onclick="initLayerEdit()" data-options="iconCls:'icon-edit'">Персонализация</div>
							</div>

							<span class="easyui-tooltip" data-options="position: 'bottom', content: 'Персонализация'"  style="float:left; margin-top:2px;">
								<a id="tool-edit-layer" class="easyui-linkbutton" data-options="iconCls: 'icon-edit', plain: true"></a>
							</span>
							<span class="easyui-tooltip" data-options="position: 'bottom', content: 'Информация'"  style="float:left; margin-top:2px;">
								<a id="tool-choose-layer-object" class="easyui-linkbutton" data-options="iconCls: 'icon-select-info', plain: true"></a>
							</span>
							<span class="easyui-tooltip" data-options="position: 'bottom', content: 'Мащабиране'"  style="float:left; margin-top:2px;">
								<a id="tool-zoom-layer" class="easyui-linkbutton" data-options="iconCls: 'icon-zoom-full', plain: true"></a>
							</span>
							<span class="easyui-tooltip" data-options="position: 'bottom', content: 'Атрибутна информация'"  style="float:left; margin-top:2px;">
								<a id="tool-attr-info" class="easyui-linkbutton" data-options="iconCls: 'icon-datagrid', plain: true"></a>
							</span>
							
							<a id="menubutton-export-map-graphically" class="easyui-menubutton" data-options="menu:'#tool-export-map-submenu',iconCls:'icon-print'" style="float: left"></a>
							<div id="tool-export-map-submenu" style="width:200px; display:none">
								<div id="tool-preview-map-export">
									<span>Отпечатай карта</span>
								</div>
								<div id="tool-export-map-view-submenu" style="display:none;">
									<span>Отпечатай видима част</span>
									<div>
										<div onclick="exportMapView('A0');" id="tool-export-map-view-a0" class="easyui-linkbutton" data-options="plain: true">Експорт изображение на карта (формат А0)</div>
										<div onclick="exportMapView('A1');" id="tool-export-map-view-a1" class="easyui-linkbutton" data-options="plain: true">Експорт изображение на карта (формат А1)</div>
										<div onclick="exportMapView('A2');" id="tool-export-map-view-a2" class="easyui-linkbutton" data-options="plain: true">Експорт изображение на карта (формат А2)</div>
										<div onclick="exportMapView('A3');" id="tool-export-map-view-a3" class="easyui-linkbutton" data-options="plain: true">Експорт изображение на карта (формат А3)</div>
										<div onclick="exportMapView('A4');" id="tool-export-map-view-a4" class="easyui-linkbutton" data-options="plain: true">Експорт изображение на карта (формат А4)</div>
									</div>
								</div>
							</div>

							<span class="easyui-tooltip" data-options="position: 'right', content: 'Изтриване на всички обекти в избрания активен слой'"  style="float: left; margin-top:2px;">
								<a href="javaScript:void(0)" id="tool-delete-all" class="easyui-linkbutton" data-options="iconCls: 'icon-delete', plain: true"></a>
							</span>

							<div style="float: right;">
								<span id="map-types-combobox" style="padding-top: 2px; padding-right: 3px; float: left;">
									<input type="text" style="width: 200px;" />
								</span>
							</div>
						</div>
						<div data-options="region:'center', border: false">
							<div  class="easyui-layout" data-options="fit:true">
								<div data-options="region:'center'" style="border-right: none;">
									<div class="easyui-layout" data-options="fit: true">
										<div data-options="region: 'west', width: 30, border: false" style="background-color: #fafafa; overflow: hidden;">
											<span class="easyui-tooltip" data-options="position: 'right', content: 'Преместване'"  style="float: left">
												<a href="javaScript:void(0)" id="tool-panzoom" class="easyui-linkbutton" data-options="iconCls: 'icon-nav-pan', plain: true"></a>
											</span>
											<span class="easyui-tooltip" data-options="position: 'right', content: 'Увеличение'"  style="float: left">
												<a href="javaScript:void(0)" id="tool-zoomin" class="easyui-linkbutton" data-options="iconCls: 'icon-zoom-in', plain: true"></a>
											</span>
											<span class="easyui-tooltip" data-options="position: 'right', content: 'Намаление'"  style="float: left">
												<a href="javaScript:void(0)" id="tool-zoomout" class="easyui-linkbutton" data-options="iconCls: 'icon-zoom-out', plain: true"></a>
											</span>
											<span class="easyui-tooltip" data-options="position: 'right', content: 'Измерване на площ'"  style="float: left">
												<a href="javaScript:void(0)" id="tool-measure-polygon" class="easyui-linkbutton" data-options="iconCls: 'icon-measure-polygon', plain: true"></a>
											</span>
											<span class="easyui-tooltip" data-options="position: 'right', content: 'Измерване на разстояние'"  style="float: left">
												<a href="javaScript:void(0)" id="tool-measure-line" class="easyui-linkbutton" data-options="iconCls: 'icon-measure-line', plain: true"></a>
											</span>
											<span class="easyui-tooltip" data-options="position: 'right', content: 'Изчистване на селекция'"  style="float: left">
												<a href="javaScript:void(0)" id="tool-clear-selection" class="easyui-linkbutton" data-options="iconCls: 'icon-clear', plain: true"></a>
											</span>

											<div style="float: left; height: 1px; width: 25px; border-top: 1px solid #ccc; border-bottom: 1px solid #fff; margin: 1px 2px;"></div>

											<span class="easyui-tooltip" data-options="position: 'right', content: 'Селектиране на обект'"  style="float: left">
												<a href="javaScript:void(0)" id="tool-select" class="easyui-linkbutton" data-options="iconCls: 'icon-select', plain: true"></a>
											</span>
											<span class="easyui-tooltip" data-options="position: 'right', content: 'Редакция на геометрия'"  style="float: left">
												<a href="javaScript:void(0)" id="tool-edit-geometry" class="easyui-linkbutton" data-options="iconCls: 'icon-edit-geometry', plain: true"></a>
											</span>
											<span class="easyui-tooltip" data-options="position: 'right', content: 'Запазване на обекти'"  style="float: left">
												<a href="javaScript:void(0)" id="tool-save" class="easyui-linkbutton" data-options="iconCls: 'icon-save', plain: true"></a>
											</span>
											<span class="easyui-tooltip" data-options="position: 'right', content: 'Изтриване на обекти'"  style="float: left">
												<a href="javaScript:void(0)" id="tool-delete" class="easyui-linkbutton" data-options="iconCls: 'icon-delete', plain: true"></a>
											</span>
											<span class="easyui-tooltip" data-options="position: 'right', content: 'Добавяне на обект'"  style="float: left">
												<a href="javaScript:void(0)" id="tool-draw" class="easyui-linkbutton" data-options="iconCls: 'icon-draw', plain: true"></a>
											</span>
											<span class="easyui-tooltip" data-options="position: 'right', content: 'Очертаване на дупка'"  style="float: left">
												<a href="javaScript:void(0)" id="tool-draw-hole" class="easyui-linkbutton" data-options="iconCls: 'icon-polygon-hole', plain: true"></a>
											</span>
											<span class="easyui-tooltip" data-options="position: 'right', content: 'Обединение на обекти'"  style="float: left">
												<a href="javaScript:void(0)" id="tool-merge" class="easyui-linkbutton" data-options="iconCls: 'icon-intersection', plain: true"></a>
											</span>
											<span class="easyui-tooltip" data-options="position: 'right', content: 'Премахване на дупки'"  style="float: left">
												<a href="javaScript:void(0)" id="tool-remove-holes" class="easyui-linkbutton" data-options="iconCls: 'icon-remove-holes', plain: true"></a>
											</span>

											<span class="easyui-tooltip" data-options="position: 'right', content: 'Разделяне на обект'"  style="float: left">
												<a href="javaScript:void(0)" id="tool-split-polygon" class="easyui-menubutton" data-options="iconCls: 'icon-split-polygon', menu:'#map-tool-split-submenu', plain: true"></a>
												<span id="map-tool-split-submenu" >
													<div class="easyui-linkbutton" id="tool-split" data-options="plain: true">
														Ръчно разделяне
													</div>
													<div class="easyui-linkbutton" id="tool-auto-split" data-options="plain: true">
														Автоматично разделяне
													</div>
												</span>
											</span>

											<span class="easyui-tooltip" data-options="position: 'right', content: 'Пресичане на слоеве'"  style="float: left">
												<a href="javaScript:void(0)" id="tool-cross-layers" class="easyui-linkbutton" data-options="iconCls: 'icon-clipping', plain: true"></a>
											</span>
											<span class="easyui-tooltip" data-options="position: 'right', content: 'Копиране на селектирани обекти към слой'"  style="float: left">
												<a href="javaScript:void(0)" id="tool-copy-to-layer" class="easyui-menubutton" data-options="iconCls: 'icon-copy', menu:'#map-copy-submenu', plain: true"></a>
												<span id="map-copy-submenu" >
													<div class="easyui-linkbutton js-copy-layer-btn" data-options="plain: true">В слой "Временни данни"</div>
													<div class="easyui-linkbutton js-copy-layer-btn-for-isak" data-options="plain: true">В слой "За ИСАК"</div>
													<div class="easyui-linkbutton js-copy-layer-btn-work-layer" data-options="plain: true">В слой "Работен слой"</div>
													<div class="easyui-linkbutton js-copy-layer-btn-zp" data-options="plain: true">В слой "Земеделски парцели"</div>
												</span>
											</span>
										</div>
										<div data-options="region: 'center'">
											<div style="width:100%; height:100%; color:#000000;" id="map">
												<div class="logo"></div>
											</div>
										</div>
									</div>

								</div>
								<div data-options="region:'south'" style="border-top: none; padding:0px;height:37px; overflow: hidden;">
									<div style="float:left; padding: 10px">EPSG: 900913</div>


									<div id="coords" style="float:right;width:250px;text-align: right;border-left: 1px solid #ccc; padding: 10px">x: ,y: </div>
									<div id="scale" style="float: right; margin-right: 10px;">
										Мащаб 1:
										<span id="scale-denominator">
											<input type="text" style="width: 75px; margin: 4px 0px 3px 3px" />
										</span>
										<span class="easyui-tooltip" data-options="position: 'top', content: 'Мащабиране'">
											<a style="margin: 0px 3px 0px 0px" href="javaScript:void(0)" id="tool-set-scale" class="easyui-linkbutton" data-options="iconCls: 'icon-zoom-full', plain: true"></a>
										</span>
										<input id="select-coord-system-for-map" type="text" style="width: 75px; text-align: left; margin-right: 5px;">
									</div>
									<div style="float: right; margin-right: 10px; padding: 10px">
										Обща площ: <span id="total-selected-area"></span>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
    </div>

	<div id="attr-tables-toolbar">
		<a id="attr-tables-kvs-filter-btn" href="javaScript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-filter', plain: true">Филтри</a>
		<a id="attr-tables-edit-btn" onclick="initManualLayerDataEdit();" class="easyui-linkbutton js-attr-tables-edit" data-options="iconCls: 'icon-edit', plain: true">Редакция</a>
		<a id="attr-cols-edit-btn" class="easyui-linkbutton" data-options="iconCls: 'icon-tree-add-column', plain: true">Редакция на колоните</a>
		<a id="att-tables-multi-edit-btn" onclick="initMultiEditLayer();" href="javaScript:void(0)" class="easyui-linkbutton js-attr-tables-edit" data-options="iconCls: 'icon-multi-edit', plain: true">Мултиредакция</a>
		<a id="attr-tables-multiedit-btn" onclick="initManualLayerDataMultiEdit();jQuery('#win-for-isak-multi-edit').window('open');" class="easyui-linkbutton js-attr-tables-edit" data-options="iconCls: 'icon-multi-edit', plain: true">Мултиредакция</a>
		<a onclick="zoomToFilteredObjects();" class="easyui-linkbutton" data-options="iconCls: 'icon-zoom-full', plain: true">Мащабиране</a>
		<a id="attr-tables-export-btn" class="easyui-menubutton" data-options="menu: '#attr-tables-export-submenu', iconCls: 'icon-export'">Експорт</a>
		<span id="attr-tables-export-submenu">
			<div onclick="exportLayer('exportISAK', true);" id="tool-global-export-isak" class="easyui-linkbutton" data-options="plain: true">Експорт за ИСАК</div>
			<div onclick="exportLayer('exportTrimbleAg', true,  false, 'TrimbleAgGPS');" class="easyui-linkbutton" data-options="plain: true">Експорт за Trimble AgGPS (Общ файл)</div>
			<div onclick="exportLayer('exportTrimbleAg', false, false, 'TrimbleAgGPS');" class="easyui-linkbutton" data-options="plain: true">Експорт за Trimble AgGPS (Отделни файлове)</div>
			<div onclick="exportLayer('exportTrimbleAg', true,  false, 'TrimbleAgData');" class="easyui-linkbutton" data-options="plain: true">Експорт за Trimble AgData (Общ файл)</div>
			<div onclick="exportLayer('exportTrimbleAg', false, false, 'TrimbleAgData');" class="easyui-linkbutton" data-options="plain: true">Експорт за Trimble AgData (Отделни файлове)</div>
			<div onclick="exportLayer('exportTopcon', true);" id="tool-global-export-gps" class="easyui-linkbutton" data-options="plain: true">Експорт за Topcon (Общ файл)</div>
			<div onclick="exportLayer('exportTopcon', false);" id="tool-global-export-gps" class="easyui-linkbutton" data-options="plain: true">Експорт за Topcon (Отделни файлове)</div>
			<div onclick="exportLayer('exportMueller', true, true, 'Muller', 0);" id="tool-global-export-gps" class="easyui-linkbutton" data-options="plain: true">Експорт за Mueller Track Guide I и II (преди 10.2014) (Общ файл)</div>
			<div onclick="exportLayer('exportMueller', false, true, 'Muller', 0);" id="tool-global-export-gps" class="easyui-linkbutton" data-options="plain: true">Експорт за Mueller Track Guide I и II (преди 10.2014) (Отделни файлове)</div>
			<div onclick="exportLayer('exportMueller', true, false, 'Muller', 1);" id="tool-global-export-gps" class="easyui-linkbutton" data-options="plain: true">Експорт за Mueller Track Guide II и III (след 10.2014 и преди 2017) (Общ файл)</div>
			<div onclick="exportLayer('exportMueller', false, false, 'Muller', 1);" id="tool-global-export-gps" class="easyui-linkbutton" data-options="plain: true">Експорт за Mueller Track Guide II и III (след 10.2014 и преди 2017) (Отделни файлове)</div>
			<div onclick="exportLayer('exportMueller', true, false, 'Muller', 2);" id="tool-global-export-gps" class="easyui-linkbutton" data-options="plain: true">Експорт за Mueller Track Guide II и III (след 2017) (Общ файл)</div>
			<div onclick="exportLayer('exportMueller', false, false, 'Muller', 2);" id="tool-global-export-gps" class="easyui-linkbutton" data-options="plain: true">Експорт за Mueller Track Guide II и III (след 2017) (Отделни файлове)</div>
			<div onclick="exportLayer('exportKML', true);" id="tool-global-export-kml-attr" class="easyui-linkbutton" data-options="plain: true">Експорт в KML</div>
			<div onclick="exportLayer('exportKMLfarmTrack', true);" id="tool-global-export-kml-ft-attr" class="easyui-linkbutton" data-options="plain: true">Експорт в KML (за Farm Track)</div>
			<div onclick="exportLayer('exportGps', true);" id="tool-global-export-gps" class="easyui-linkbutton" data-options="plain: true">Експорт за друг GPS</div>
			<div onclick="showExportToModemWin();" id="tool-global-export-tfmodem" class="easyui-linkbutton" data-options="plain: true">Експорт за TF Connect</div>
            <div>
                <span>Експорт за John Deere(Общ файл)</span>
                <div>
                    <div onclick="exportLayer('exportJohnDeere', true, false, 'GS2_CommandCenter');" id="tool-global-export-john-deere-gs2-command-center-general-file" class="easyui-linkbutton" data-options="plain: true" style="margin-right: 150px;">GS2_CommandCenter</div>
                    <div onclick="exportLayer('exportJohnDeere', true, false, 'GS2_1800');" id="tool-global-export-john-deere-gs2-1800-general-file" class="easyui-linkbutton" data-options="plain: true" style="margin-right: 150px;">GS2_1800</div>
                    <div onclick="exportLayer('exportJohnDeere', true, false, 'GS2_2600');" id="tool-global-export-john-deere-gs2-2600-general-file" class="easyui-linkbutton" data-options="plain: true" style="margin-right: 150px;">GS2_2600</div>
                    <div onclick="exportLayer('exportJohnDeere', true, false, 'GS3_2630');" id="tool-global-export-john-deere-gs3-2630-general-file" class="easyui-linkbutton" data-options="plain: true">GS3_2630 / GS4600Plugin</div>
                </div>
            </div>
            <div>
                <span>Експорт за John Deere(Отделни файлове)</span>
                <div>
                    <div onclick="exportLayer('exportJohnDeere', false, false, 'GS2_CommandCenter');" id="tool-global-export-john-deere-gs2-command-center-separate-files" class="easyui-linkbutton" data-options="plain: true">GS2_CommandCenter</div>
                    <div onclick="exportLayer('exportJohnDeere', false, false, 'GS2_1800');" id="tool-global-export-john-deere-gs2-1800-separate-files" class="easyui-linkbutton" data-options="plain: true" style="margin-right: 150px;">GS2_1800</div>
                    <div onclick="exportLayer('exportJohnDeere', false, false, 'GS2_2600');" id="tool-global-export-john-deere-gs2-2600-separate-files" class="easyui-linkbutton" data-options="plain: true">GS2_2600</div>
                    <div onclick="exportLayer('exportJohnDeere', false, false, 'GS3_2630');" id="tool-global-export-john-deere-gs3-2630-separate-files" class="easyui-linkbutton" data-options="plain: true">GS3_2630 / GS4600Plugin</div>
                </div>
            </div>
		</span>
		<a class="easyui-menubutton" id="copy-items" style="display: none;" data-options="menu: '#attr-tables-copy-submenu', iconCls: 'icon-copy'">Копиране</a>
		<span id="attr-tables-copy-submenu">
			<div class="easyui-linkbutton js-copy-layer-btn" data-options="plain: true">В слой "Временни данни"</div>
			<div class="easyui-linkbutton js-copy-layer-btn-filtered-for-isak" data-filtered="true" data-options="plain: true">В слой "За ИСАК"</div>
			<div class="easyui-linkbutton js-copy-layer-btn-filtered-zp" data-filtered="true" data-options="plain: true">В слой "Земеделски парцели"</div>
			<div class="easyui-linkbutton js-copy-layer-btn-filtered-work-layer" data-filtered="true" data-options="plain: true">В "Работен слой"</div>
		</span>
		<a class="easyui-menubutton" id="export-xls-btn" style="display: none;" data-options="menu: '#attr-export-xls-submenu', iconCls: 'icon-csv', plain: true">Export(XLS)</a>
        <span id="attr-export-xls-submenu">
            <div onclick="exportToExcelData();" class="easyui-linkbutton js-attr-tables-edit" style="background: none;">Export(XLS)</div>
            <div id="attr-tables-export-excel-cadastral-map-btn" style="display: none;" onclick="exportToExcelCadastralMapData();" class="easyui-linkbutton" data-options="plain: true" style="margin-right: 150px;">Експорт с формат за кадастрална карта</div>
        </span>
		<div id="select-filtered-plots-group" style="display: inline; padding: 5px 0 0 0;">
			<input type="checkbox" id="extended-select-filtered-plots" style="vertical-align: middle"/>
			<label for="extended-select-filtered-plots" style="vertical-align: text-top;" >Подсветни филтрираните</label>
		</div>
		<span style="display: inline;" id="clip-with-kvs-btn-wrapper">
			<a id="attr-tables-clipping-with-kvs" onclick="ClippingWithKVS()" class="easyui-linkbutton" data-options="iconCls: 'icon-clipping', plain: true">Пресичане с КВС</a>
		</span>

		<span style="display: inline;" id="clip-kvs-btn-wrapper">
            <%%
                if($this->User->hasKVSCuttingRights) {
                echo '<a id="attr-tables-clipping-kvs" onclick="KVSClipping()" class="easyui-linkbutton" data-options="iconCls: \'icon-clipping\', plain: true">Пресичане на КВС</a>';
            }
            %>
        </span>
        <div class="choose-all" style="display: inline;">
                <input type="checkbox" id="chooseAllCheckBox" onclick="chooseAll(this);" style="vertical-align: middle; margin-right:3px;"><span style="position: relative; top:2px">Избери всички обекти</span></input>
        </div>
	</div>

	<div id="property-win" class="easyui-window" data-options="closed: true" style="border:0px;">
		<div id="property-tabs" class="easyui-tabs" >
			<div title="КВС Информация">
				<table id="property-grid" style="border:0px;"></table>
				<div style="margin: 5px 0 0 0; text-align:center;">
					<a id="btn-save-propertygrid" style="" class="easyui-linkbutton" data-options="iconCls: 'icon-save'">Запази</a>
					<a id="btn-close-propertygrid" style="" class="easyui-linkbutton" data-options="iconCls: 'icon-cancel'" onclick="jQuery('#property-win').window('close');">Затвори</a>
				</div>
			</div>
			<div title="ОСЗ Данни" data-options="disabled:true">
				<table id="osz-property-grid" style="border:0px;"></table>
				<div style="margin: 5px 0 0 0; text-align:center;">
					<a id="btn-close-propertygrid" style="" class="easyui-linkbutton" data-options="iconCls: 'icon-cancel'" onclick="jQuery('#property-win').window('close');">Затвори</a>
				</div>
			</div>
		</div>
	</div>

    <div id="progress-win" style="width:400px;padding:5px;">
        <div id="progress" style="width:373px;height:40px;"></div>
    </div>

    <div id="info-panel" class="easyui-window" title="Информация" style="width:290px; height: 150px; padding:10px;" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-info', maximizable: false, minimizable: false">
    </div>

    <div id="win-polygon-area" class="easyui-window" title="Площ" style="width:100px; height: 68px; padding:2px; position:relative;" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-info', maximizable: false, minimizable: false">
    	<input id="win-polygon-area-text" style="width: 80px;" />
    </div>

    <div id="layer-change-win" class="easyui-window" title="Промяна на слой" data-options="iconCls:'icon-users', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true, onClose:function(){jQuery('.sp-picker-container').css('display', 'none');}" style="width:420px;height:510px; padding: 5px;">
        <com:Application.Portlets.Map.LayerChange />
    </div>

    <div id="topic-layer-legend" class="easyui-window" title="Легенда" data-options="iconCls:'icon-legend', closed: true, resizable: false,  minimizable: false, collapsible: true, collapsed: true, maximizable: false, modal: false, closable:true, onClose:function(){removeTopicKVSLayers()}" style="width:230px;height:300px; padding: 5px;">
        <com:Application.Portlets.Map.TopicLayerLegend />
    </div>
    <div id="layer-select-win" class="easyui-window" title="Избиране на слой" data-options="iconCls:'icon-plot', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" style="width:400px;height:180px;">
        <com:Application.Portlets.Map.ChooseLayer />
    </div>
    <div id="copy-layer-win" class="easyui-window" title="Копиране на слой" data-options="iconCls:'icon-copy', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" style="width:350px;height:330px;">
        <com:Application.Portlets.Map.SelectLayer />
    </div>
    <div id="win-layer-tmp" class="easyui-window" title="Копиране във временен слой" data-options="iconCls:'icon-data', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true" style="width:350px;height:260px;">
        <com:Application.Portlets.Map.ChooseTmpLayer />
    </div>
	<div id="win-choose-export-type" class="easyui-window" title="Избор на експорт" data-options="iconCls:'icon-data', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true" style="padding: 10px;">
        <com:Application.Portlets.Map.ChooseExportType />
    </div>
	<div id="win-layer-attr-info" class="easyui-window" title="Атрибутна информация" style="height: 630px; width: 1080px"
		 data-options="iconCls:'icon-datagrid', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: false">
        <com:Application.Portlets.Map.AttrInfo />
    </div>

	<div id="win-map-export-preview" class="easyui-window" title="Отпечатване на карта" style="height: 740px; width: 1280px"
		 data-options="iconCls:'icon-legend', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true">
        <com:Application.Portlets.Map.MapExportPreview />
    </div>

    <div id="win-kvs-split" class="easyui-window" title="Разделяне на имот" style="height: 500px; width: 300px"
		 data-options="iconCls:'icon-datagrid', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true">
        <com:Application.Portlets.Map.KVSSplit />
    </div>

    <div id="win-kvs-merge" class="easyui-window" title="Обединение на имоти" style="height: 525px; width: 380px"
		 data-options="iconCls:'icon-datagrid', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true">
        <com:Application.Portlets.Map.KVSMerge />
    </div>

    <div id="win-download" class="easyui-dialog" title="Изтегляне на файл" style="width:260px;height:100px;"
		 data-options="iconCls:'icon-files',resizable:true,modal:true,closed:true">
        <div style="text-align: center;padding:10px;">
            <a style="margin-top: 10px;" id="btn-download-file" href="javaScript:void(0)" class="easyui-linkbutton" onClick="jQuery('#win-download').window('close');" data-options="iconCls:'icon-files'" target="_blank">Изтегли</a>
            <a style="margin-top: 10px;" href="javaScript:void(0)" class="easyui-linkbutton" onClick="jQuery('#win-download').window('close');" data-options="iconCls:'icon-cancel'">Откажи</a>
        </div>
    </div>

    <div id="win-clipping" class="easyui-window" title="Пресичане" style="height: 310px; width: 390px; top: 0; left: 0"
		 data-options="iconCls:'icon-clipping', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: false">
        <com:Application.Portlets.Map.Clipping />
    </div>

    <div id="win-operation-cut-help" class="easyui-window" title="Пояснение за операция: Изрязване" style="height: 440px; width: 800px; top: 0; left: 0"
		 data-options="iconCls:'icon-help', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: false">
        <com:Application.Portlets.Map.ClippingHelpCut />
    </div>

    <div id="win-operation-split-help" class="easyui-window" title="Пояснение за операция: Разцепване" style="height: 480px; width: 800px; top: 0; left: 0"
		 data-options="iconCls:'icon-help', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: false">
        <com:Application.Portlets.Map.ClippingHelpSplit />
    </div>

    <div id="win-operation-delete-help" class="easyui-window" title="Пояснение за операция: Отрязване" style="height: 495px; width: 800px; top: 0; left: 0"
		 data-options="iconCls:'icon-help', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: false">
        <com:Application.Portlets.Map.ClippingHelpDelete />
    </div>

    <div id="win-add-for-isak-info" class="easyui-window" title="Нов парцел" style="height: 245px; width: 290px; top: 0; left: 0"
		 data-options="iconCls:'icon-save', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: false">
        <com:Application.Portlets.Map.AddInfoForIsak />
    </div>

    <div id="win-add-temp-data-info" class="easyui-window" title="Създаване на нов парцел" style="height: 225px; width: 290px; top: 0; left: 0"
		 data-options="iconCls:'icon-save', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: false">
        <com:Application.Portlets.Map.AddInfoTempData />
    </div>

    <div id="win-layer-copy" class="easyui-window" title="Избор на слой" style="height: 200px; width: 290px; top: 0; left: 0"
		 data-options="iconCls:'icon-save', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: false">
        <com:Application.Portlets.Map.ForIsakCopy />
    </div>

    <div id="win-plots-filter" class="easyui-window" title="Филтър" style="width:429px;"
		 data-options="iconCls:'icon-filter', closed: true, resizable: false,minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >

		<com:Application.Portlets.Plots.PlotsFilter />
	</div>

    <div id="win-for-isak-multi-edit" class="easyui-window" title="Мултиредакция" style="width:350px; height:165px; padding-top: 10px;" data-options="iconCls:'icon-edit', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
        <com:Application.Portlets.Map.MultiEdit />
    </div>

    <div id="win-multi-zp-edit" class="easyui-window" title="Мултиредакция" style="width:620px; height:275px; padding-top: 10px;" data-options="iconCls:'icon-edit', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
        <com:Application.Portlets.Map.ZpMultiEdit />
	</div>
	<div id="win-choose-modem-devices" class="easyui-window" title="Избор на устройство" style="height: 200px; width: 290px; top: 0; left: 0" data-options="iconCls:'icon-edit', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
        <com:Application.Portlets.Map.ChooseModemDevice />
	</div>

	<div id="win-kvs-clipping" class="easyui-window" title="Пресичане на КВС" style="height: 200px; width: 290px; top: 0; left: 0" data-options="iconCls:'icon-clipping', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
		<com:Application.Portlets.Map.KVSClipping />
	</div>

	<div id="win-work-layer-col-editing" class="easyui-window" title="Редактиране на колоните в работния слой" style="max-height: 600px; width: 395px;" data-options="iconCls:'icon-clipping', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
    		<com:Application.Portlets.Map.WorkLayerColEditing />
    	</div>
</com:TContent>
