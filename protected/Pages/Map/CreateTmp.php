<?php

use TF\Application\Common\Config;
use TF\Application\Common\MTPage;
use TF\Engine\Plugins\Core\Layers\LayersController;

/**
 * Home class.
 *
 * Home page of Users plugin
 */
class CreateTmp extends MTPage
{
    public $Layers;

    /**
     * onInit event.
     */
    public function onInit($param)
    {
        parent::onInit($param);

        // $this->initController('Plugins.Core.Layers');
        $this->LayersController = new LayersController();

        $return['error'] = 0;

        try {
            $options['return'] = ['*'];
            $options['whereFields'] = ['id', 'user_id'];
            $options['whereValues'] = [(int)$_POST['layer1'], $this->User->UserID];
            $data = $this->Page->LayersController->getItem($options);
            $layer1 = $data['table_name'];
            $extent = $data['extent'];

            switch ($_POST['type']) {
                case Config::REPORT_NATURA:
                    $layer2 = 'natura2000';
                    $res = $this->Page->LayersController->getIntersectionCount($layer1, $layer2, $this->User->Database);
                    $return['plotcount'] = $res;

                    break;
                case Config::REPORT_GASKI:
                    $layer2 = 'guski';
                    $res = $this->Page->LayersController->getIntersectionCount($layer1, $layer2, $this->User->Database);
                    $return['plotcount'] = $res;

                    break;
                case Config::REPORT_PARKOVE:
                    $layer2 = 'parkove';
                    $res = $this->Page->LayersController->getIntersectionCount($layer1, $layer2, $this->User->Database);
                    $return['plotcount'] = $res;

                    break;
                case Config::REPORT_REZERVATI:
                    $layer2 = 'rezervati';
                    $res = $this->Page->LayersController->getIntersectionCount($layer1, $layer2, $this->User->Database);
                    $return['plotcount'] = $res;

                    break;
                case Config::REPORT_PTICI:
                    $layer2 = 'raptors';
                    $res = $this->Page->LayersController->getIntersectionCount($layer1, $layer2, $this->User->Database);
                    $return['plotcount'] = $res;

                    break;
                default:
                    $options['return'] = ['*'];
                    $options['whereFields'] = ['id', 'user_id'];
                    $options['whereValues'] = [(int)$_POST['layer2'], $this->User->UserID];
                    $data = $this->Page->LayersController->getItem($options);
                    $layer2 = $data['table_name'];
            }

            $options = [];
            $options['database'] = $this->User->Database;
            $options['user_id'] = $this->User->UserID;
            $options['layer1'] = $layer1;
            $options['layer2'] = $layer2;
            $options['extent'] = str_replace(',', ' ', $extent);
            $this->Page->LayersController->generateTempMapFile($options);
        } catch (Exception $e) {
            $return['error'] = 1;
        }

        echo json_encode($return);
    }
}
