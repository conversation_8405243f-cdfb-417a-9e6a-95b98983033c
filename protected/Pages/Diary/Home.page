<com:TContent ID="Content">
    <script type="text/javascript">
        var isSuperAdmin = '<%=$this->User->IsSuperAdmin%>';
        var userid = '<%=$this->User->UserID%>';
        var sessionid = '<%=session_id()%>';
        var wmsServer = '<%=WMS_SERVER%>';
        var mapPath = '<%=WMS_MAP_PATH%>';
        var groupID = '<%=$this->User->GroupID%>';
        var isAdmin = '<%=$this->User->UserLevel == TF\Application\Common\Config::USERS_ADMIN_FLAG%>';
        var login3WmsServer = '<%=LOGIN3_WMS_SERVER%>';
        var login3MapPath = '<%=LOGIN3_WMS_MAP_PATH%>';
        var wialonServer = '<%=WIALON_SERVER%>';
        var trackToken = '<%=$_SESSION["track_token"]%>';
    </script>

    <script type="text/javascript" src="https://login.farmtrack.bg/wsdk/script/wialon.js"></script>

    <com:TRepeater ID="Repeater" EnableViewState="false">
        <prop:ItemTemplate>
            <script type="text/javascript" src="<%#$this->Data%>"></script>
        </prop:ItemTemplate>
    </com:TRepeater>

    <script src="https://maps.google.com/maps/api/js?v=<%=GOOGLE_VERSION%>&amp;key=<%=GOOGLE_KEY%>"></script>
    <div class="easyui-layout" data-options="fit:true">
       <com:TConditional Visible="<%= LEGACY_MODE %>">
			<prop:TrueTemplate>
				<div data-options="region:'north'" style="height:40px; border-top: none;">
					<com:Application.Portlets.NavigationMenu ID="Navigation" />            
				</div>
			</prop:TrueTemplate>
        </com:TConditional>
        <div data-options="region:'west'" style="padding:5px; width:250px;">
            <div class="easyui-layout" data-options="fit: true">
                <div data-options="region: 'north', collapsible: false" style="height: 94px; border-bottom: none;overflow-y: inherit;">
                    <div class="easyui-panel" data-options="title: 'Земеделски парцели', iconCls: 'icon-edit-geometry', border: false">
                        <div style="background:#fafafa;">

                            <span id="zp-comboTree" class="combotree-f" style="min-width: 235px;"></span>
                            <span class="easyui-tooltip" data-options="position: 'right', content: 'Филтриране'">
								<a id="btn-filter-event" href="javaScript:void(0)" class="easyui-linkbutton" data-options="iconCls: 'icon-filter', plain: true"></a>
							</span>
                            <span class="easyui-tooltip" data-options="position: 'right', content: 'Покажи всички'">
								<a id="btn-clear-filter-event" href="javaScript:void(0)" class="easyui-linkbutton" data-options="iconCls: 'icon-clear-filter', plain: true"></a>
							</span>
                            <span class="easyui-tooltip" data-options="position: 'right', content: 'Редактиране на земеделски парцел'">
								<a id="btn-edit-zplot" href="javaScript:void(0)" class="easyui-linkbutton" data-options="iconCls: 'icon-edit-geometry', plain: true"></a>
							</span>
                            <hr style="margin: 0px; border: none; height: 1px; background: #ddd;">
                        </div>
                    </div>
                </div>
                <div data-options="region: 'center'" style="border-top: none;">
                    <div id="zp-tree" style="min-width: 235px;"></div>
                </div>
            </div>
        </div>
        <div data-options="region:'center'" style="padding:5px;">

            <div id="right-layout" class="easyui-layout" data-options="fit:true" style="">

                <div data-options="region: 'north', border: false" style="height: 35px; padding-bottom: 5px;">
                    <div class="easyui-panel" data-options="fit:true">
                        <div style="background:#fafafa; padding: 1px 2px;">

                            <a href="javascript:void(0)" id="btn-choose-diary-type" class="easyui-linkbutton" data-options="iconCls: 'icon-agreements', plain: true">Дневници</a>
                            <a href="javascript:void(0)" id="btn-diary-reports" class="easyui-linkbutton" data-options="iconCls: 'icon-reports', plain: true">Справки</a>

                        </div>
                    </div>
                </div>

                <div data-options="region:'center', collapsible:true, onMaximize:function (){map.render('map')}" style="">
                    <div class="easyui-layout" data-options="fit: true">
                        <div style="background:#fafafa; padding: 1px 2px; border-bottom: 1px solid #ddd">
                            <span class="textbox-addon-left" style="left: 0px;">
                            <span style="float: left;">
								<span style="padding: 0px 0px 0px 5px;">Активен слой:</span>
								<a id="active-layer-menubutton" class="easyui-menubutton" data-options="menu: '#layers-custom-menu', iconCls: 'icon-edit-geometry', plain: true" style="">Не е избран</a>
								<div id="layers-custom-menu" class="menu-content" style="background-color: #fafafa;">
									<div style="overflow: auto;">
										<div id="all-layers-tree" style="overflow-y:auto;
										 height:auto !important;"></div>
									</div>
								</div>
							</span>
							</span>
                            <div id="all-layers-tree-cm" class="easyui-menu" style="width:120px;">
                                <div onclick="zoomToLayerExtent()" data-options="iconCls:'icon-zoom-full'">Мащабиране</div>
                            </div>
                            <!-- TS-3278 -->
                            <!-- menu for filter for KVS layer -->
                            <a id="kvs-filter-menu-btn" href="javascript:void(0)" title="Филтриране" class="easyui-menubutton"
                               data-options="disabled:true, iconCls:'icon-filter', plain:true, hasDownArrow:false" style="float:left;">
                            </a>
                            <!-- end menu for filter for KVS layer -->
                            <span class="easyui-tooltip" data-options="position: 'right', content: 'Избор на земеделски парцел'"  style="float: left">
								<a id="tool-select-zplot" href="javaScript:void(0)" class="easyui-linkbutton" data-options="iconCls: 'icon-select', plain: true"></a>
							</span>
                            <span class="easyui-tooltip" data-options="position: 'right', content: 'Мащабиране на активен слой'"  style="float: left">
								<a id="tool-zoom-to-layer" href="javaScript:void(0)" class="easyui-linkbutton" data-options="iconCls: 'icon-zoom-full', plain: true"></a>
							</span>
                            <span class="easyui-tooltip" data-options="position: 'right', content: 'Преместване'"  style="float: left">
								<a id="tool-panzoom" href="javaScript:void(0)" class="easyui-linkbutton" data-options="iconCls: 'icon-nav-pan', plain: true"></a>
							</span>
                            <span class="easyui-tooltip" data-options="position: 'right', content: 'Увеличение'" style="float: left">
								<a id="tool-zoomin" href="javaScript:void(0)" class="easyui-linkbutton" data-options="iconCls: 'icon-zoom-in', plain: true"></a>
							</span>
                            <span class="easyui-tooltip" data-options="position: 'right', content: 'Намаление'" style="float: left">
								<a id="tool-zoomout" href="javaScript:void(0)" class="easyui-linkbutton" data-options="iconCls: 'icon-zoom-out', plain: true"></a>
							</span>
                            <span class="easyui-tooltip" data-options="position: 'right', content: 'Измерване на площ'" style="float: left">
								<a id="tool-measure-polygon" href="javaScript:void(0)" class="easyui-linkbutton" data-options="iconCls: 'icon-measure-polygon', plain: true"></a>
							</span>
                            <span class="easyui-tooltip" data-options="position: 'right', content: 'Измерване на разстояние'" style="float: left">
								<a id="tool-measure-line" href="javaScript:void(0)" class="easyui-linkbutton" data-options="iconCls: 'icon-measure-line', plain: true"></a>
							</span>
                            <span class="easyui-tooltip" data-options="position: 'right', content: 'Изчистване на селекция'" style="float: left">
								<a id="tool-clear-selection" href="javaScript:void(0)" class="easyui-linkbutton" data-options="iconCls: 'icon-clear', plain: true"></a>
							</span>

                            <span class="datagrid-btn-separator"></span>

                            <span class="easyui-tooltip" data-options="position: 'right', content: 'Машини'" style="float: left">
								<a id="tool-units" href="javaScript:void(0)" class="easyui-linkbutton" data-options="iconCls: 'icon-tractor', toggle: true, plain: true"></a>
							</span>
                            <span class="easyui-tooltip" data-options="position: 'right', content: 'Следа на машините'" style="float: left">
								<a id="tool-units-truck" href="javaScript:void(0)" class="easyui-linkbutton" data-options="iconCls: 'icon-curve', disabled: true, toggle: true, plain: true"></a>
							</span>

                            <span class="datagrid-btn-separator"></span>

                            <span class="easyui-tooltip" data-options="position: 'right', content: 'Номенклатури'"
                                  style="float: left">

							<a href="javascript:void(0)" class="easyui-menubutton" onClick="" data-options="iconCls: 'icon-settings', menu: '#diary-settings-buttons'"></a>
							<div id="diary-settings-buttons" style="float: left;">
								<div data-options="" id="diary-settings-event-types">Типове дейности</div>
								<div data-options="" id="diary-settings-event-subtypes">Видове дейности</div>
								<div class="menu-sep"></div>
								<div data-options="" id="diary-settings-machine-types">Типове машини</div>
								<div data-options="" id="diary-settings-machines">Машини</div>
								<div class="menu-sep"></div>
								<div data-options="" id="diary-settings-attachment-types">Типове прикачен инвентар</div>
								<div data-options="" id="diary-settings-attachments">Прикачени инвентари</div>
								<div class="menu-sep"></div>
								<div data-options="" id="diary-settings-substance-types">Продукти</div>
								<div data-options="iconCls:'icon-notification'" id="diary-settings-pending-substance-types">Чакащи продукти</div>
								<div data-options="" id="diary-settings-substance-technics">Техники за прилагане на препарати</div>
								<div class="menu-sep"></div>
								<div data-options="" id="diary-settings-performers">Изпълнители</div>
								<div class="menu-sep"></div>
								<div data-options="" id="diary-settings-expenses">Разходи</div>
                                <div class="menu-sep"></div>
								<div data-options="" id="diary-settings-units">Мерни единици</div>
							</div>
                             </span>
                            <span class="datagrid-btn-separator"></span>
                            <span>
								<a id="btn-get-tracks" href="javascript:void(0)" class="easyui-linkbutton"
                                   data-options="iconCls:'icon-tractor'">Данни от FarmTrack</a>
							</span>
                        </div>

                        <div id="map" style="width:100%; height:100%;"></div>
                    </div>
                </div>
                <div data-options="region:'south', collapsed: true, collapsible: true, title: 'Детайли', iconCls: 'icon-info', onCollapse: function() { }"
                     style="height: 275px;">
                    <div id="zplot-events-tables"></div>
                </div>
            </div>

        </div>
    </div>

    <div id="fuel-diary-toolbar">
        <a href="javascript:void(0)" id="print-fuel-diary" class="easyui-linkbutton"
           data-options="iconCls:'icon-print',plain:true">Отпечатай</a>
        <span style="padding: 2px 25px 0px 0px; float: right;">
			Година <input id="choose-fuel-diary-year" style="width: 100px;">
		</span>
        <span style="padding: 2px 16px 0px 0px; float: right;">
			Стопанство <input id="choose-fuel-diary-farming" style="width: 200px;">
		</span>
    </div>

    <div id="win-diary-plot-tracks" class="easyui-window" style="width:340px; height: 200px; padding:0;" title="Данни от FarmTrack"
         data-options="closed: true, resizable: true, collapsible: false, shadow:true, maximizable: false, minimizable: false, modal: false">
        <div id="diary-configs-plot-tracks"></div>
    </div>
        <div id="win-diary-settings" class="easyui-window" style="width: 900px; height: 500px; padding:0;"
             data-options="closed: true, resizable: true, collapsible: false, iconCls: 'icon-settings', maximizable: false, minimizable: false, modal: false">
            <div id="diary-configs-tables"></div>
        </div>

        <div id="win-diary-expenses" class="easyui-window" style="width: 600px;  height: 400px; padding: 0;"
             data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-money', maximizable: false, minimizable: false, modal: true">
            <div id="diary-configs-expenses"></div>
        </div>

        <div id="win-add-edit-event-type" class="easyui-window" title="Добавяне/Редакция"
             style="width:290px; padding-bottom: 5px;"
             data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-add', maximizable: false, minimizable: false, modal: true">
            <com:Application.Portlets.Diary.AddEditEventType/>
        </div>

        <div id="win-add-edit-event-subtype" class="easyui-window" title="Добавяне/Редакция"
             style="width:290px; padding-bottom: 5px;"
             data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-add', maximizable: false, minimizable: false, modal: true">
            <com:Application.Portlets.Diary.AddEditEventSubtype/>
        </div>

        <div id="win-add-edit-machine-type" class="easyui-window" title="Добавяне/Редакция"
             style="width:290px; height: 140px; padding: 0px;"
             data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-add', maximizable: false, minimizable: false, modal: true">
            <com:Application.Portlets.Diary.AddEditMachineType/>
        </div>

        <div id="win-add-edit-machine" class="easyui-window" title="Добавяне/Редакция"
             style="width:330px; height: 320px; padding: 0px;"
             data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-add', maximizable: false, minimizable: false, modal: true">
            <com:Application.Portlets.Diary.AddEditMachine/>
        </div>

        <div id="win-add-edit-attachment-type" class="easyui-window" title="Добавяне/Редакция"
             style="width:290px; height: 140px; padding: 0px;"
             data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-add', maximizable: false, minimizable: false, modal: true">
            <com:Application.Portlets.Diary.AddEditAttachmentType/>
        </div>

        <div id="win-add-edit-attachment" class="easyui-window" title="Добавяне/Редакция"
             style="width:330px; height: 310px; padding: 0px;"
             data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-add', maximizable: false, minimizable: false, modal: true">
            <com:Application.Portlets.Diary.AddEditAttachment/>
        </div>

        <div id="win-add-edit-substance-type" class="easyui-window" title="Добавяне/Редакция"
             style="width:330px; height: 140px; padding: 0;"
             data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-add', maximizable: false, minimizable: false, modal: true">
            <com:Application.Portlets.Diary.AddEditSubstanceType/>
        </div>

        <div id="win-warehouse-items-sync" class="easyui-window" title="Синхронизиране със склада"
             style="width:330px; height: 140px; padding: 0;"
             data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-add', maximizable: false, minimizable: false, modal: true">
            <com:Application.Portlets.Diary.WarehouseItemsSync/>
        </div>

        <div id="win-add-edit-substance-technic" class="easyui-window" title="Добавяне/Редакция"
             style="width:290px; height: 130px; padding: 0;"
             data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-add', maximizable: false, minimizable: false, modal: true">
            <com:Application.Portlets.Diary.AddEditSubstanceTechnic/>
        </div>

        <div id="win-add-edit-performer" class="easyui-window" title="Добавяне/Редакция"
             style="width:300px; height: 230px; padding: 0;"
             data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-add', maximizable: false, minimizable: false, modal: true">
            <com:Application.Portlets.Diary.AddEditPerformer/>
        </div>

        <div id="win-add-edit-units" class="easyui-window" title="Добавяне/Редакция"
             style="width:300px; height: 80px; padding: 0;"
             data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-add', maximizable: false, minimizable: false, modal: true">
            <com:Application.Portlets.Diary.AddEditUnits/>
        </div>

        <div id="win-add-edit-event" class="easyui-window" title="Добавяне/Редакция"
             style="padding-bottom: 5px;width:440px;overflow: hidden;"
             data-options="closed: true, resizable: true, collapsible: false, iconCls: 'icon-add', maximizable: false, minimizable: false, modal: true">
            <com:Application.Portlets.Diary.AddEditEvent/>
        </div>

        <div id="win-tree-filter" class="easyui-window" title="Филтър" style="padding-bottom: 5px"
             data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-filter', maximizable: false, minimizable: false, modal: true">
            <com:Application.Portlets.Diary.TreeFilter/>
        </div>

        <div id="win-kvs-filter" class="easyui-window" title="Филтър" style="padding-bottom: 5px; width:915px;"
             data-options="closed:true, resizable:false, collapsible: false, iconCls:'icon-filter', maximizable:false, minimizable:false, modal:false">
            <com:Application.Portlets.Diary.KvsFilter/>
        </div>

        <div id="win-event-info" class="easyui-window" title="Информация" style="padding-bottom: 5px"
             data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-filter', maximizable: false, minimizable: false, modal: true">
            <com:Application.Portlets.Diary.EventInfo/>
        </div>

        <div id="win-set-message-params" class="easyui-window" title="Избор на следа"
             style="padding-bottom: 5px; width: 400px;"
             data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-edit-geometry', maximizable: false, minimizable: false, modal: true">
            <com:Application.Portlets.Diary.SetWialonMessageParams/>
        </div>

        <div id="win-choose-diary-type" class="easyui-window" title="Избор на дневник" style=""
             data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-agreements', maximizable: false, minimizable: false, modal: true">
            <com:Application.Portlets.Diary.ChooseDiaryType/>
        </div>

        <div id="win-fuel-diary" class="easyui-window" style="height: 600px; width: 1200px; padding: 0;"
             data-options="title: 'Дневник за горива', closed: true, collapsible: false, maximizable: false, minimizable: false">
            <div id="fuel-diary-tables"></div>
        </div>

        <div id="win-add-edit-expenses" class="easyui-window" style="height: 320px; width: 300px; padding: 5px;"
             data-options="title: 'Добавяне на разход', closed: true, collapsible: false, maximizable: false, minimizable: false">
            <com:Application.Portlets.Diary.AddEditExpenses/>
        </div>

        <div id="win-chemical-diary" class="easyui-window" style="height: 600px; width: 1200px; padding: 5px;"
             data-options="title:'Дневник за агрохимични обработки', closed: true, collapsible: false, maximizable: false, minimizable: false">
            <div class="easyui-layout" data-options="fit: true">
                <div id="chemical-diary-toolbar" data-options="region: 'north'"
                     style="height: 35px; background-color: #fafafa">
                    <a href="javascript:void(0)" id="export-chemical-diary-word" class="easyui-linkbutton"
                       data-options="iconCls:'icon-word',plain:true">Експорт (doc)</a>
                    <span style="padding: 2px 25px 0px 0px; float: right;">
					Година <input id="choose-chemical-diary-year" style="width: 100px;">
				</span>
                    <span style="padding: 2px 16px 0px 0px; float: right;">
					Стопанство <input id="choose-chemical-diary-farming" style="width: 200px;">
				</span>
                </div>
                <div data-options="region: 'center', split: true">
                    <div id="substances-diary-tables"></div>
                </div>
                <div data-options="region: 'south', split: true, height: 260">
                    <div id="fertilizers-diary-tables"></div>
                </div>
            </div>
        </div>

        <div id="win-edit-zp" class="easyui-window" title="Редактиране на земеделски парцел"
             style="width:350px; height:330px;"
             data-options="iconCls:'icon-edit', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true">
            <com:Application.Portlets.Diary.EditZP/>
        </div>

        <div id="win-choose-diary-report" class="easyui-window" title="Избор на справка" style=""
             data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-reports', maximizable: false, minimizable: false, modal: true">
            <div class="row">
                <a href="javascript:void(0)" class="easyui-linkbutton" onClick="displaySummaryReportByPerformer();"
                   style="width: 135px; height:135px; margin: 5px; text-align:center; float: left;">
                    <img src="themes/Main/reports.png" width="60" height="60">
                    <div style="padding-top: 0; font-weight: bold; font-style: italic; line-height: 14px;">Обобщена справка<br/>по механизатор/<br/>служител</div>
                </a>
                <a href="javascript:void(0)" class="easyui-linkbutton" onClick="displayDetailedReportByPerformer();"
                   style="width: 135px; height:135px; margin: 5px; text-align:center; float: left;">
                    <img src="themes/Main/reports.png" width="60" height="60">
                    <div style="padding-top: 0; font-weight: bold; font-style: italic; line-height: 14px;">Подробна справка<br/>по механизатор/<br/>служител</div>
                </a>
                <a id="diary-expenses-report" href="javascript:void(0)" class="easyui-linkbutton"
                   onClick="displayDetailedReportByPerformer(null, true);"
                   style="width: 135px; height:135px; margin: 5px; text-align:center; float: left;">
                    <img src="themes/Main/reports.png" width="60" height="60">
                    <div style="padding-top: 0; font-weight: bold; font-style: italic; line-height: 14px;">Подробна справка<br/> за заработки по механизатор/<br/>служител</div>
                </a>
            </div>
            <div class="row">
                <a id="diary-plots-report" href="javascript:void(0)" class="easyui-linkbutton"
                   onClick="displayDiaryPlotsReport();"
                   style="width: 135px; height:135px; margin: 5px; text-align:center; float: left;">
                    <img src="themes/Main/reports.png" width="60" height="60">
                    <div style="padding-top: 0; font-weight: bold; font-style: italic; line-height: 14px;">Справка по парцел</div>
                </a>
                <a id="diary-plots-report-by-fuel" href="javascript:void(0)" class="easyui-linkbutton"
                   style="width: 135px; height:135px; margin: 5px; text-align:center; float: left;">
                    <img src="themes/Main/reports.png" width="60" height="60">
                    <div style="padding-top: 0; font-weight: bold; font-style: italic; line-height: 14px;">Справка по гориво</div>
                </a>
                <a id="diary-plots-report-by-seeds" href="javascript:void(0)" class="easyui-linkbutton"
                   style="width: 135px; height:135px; margin: 5px; text-align:center; float: left;">
                    <img src="themes/Main/reports.png" width="60" height="60">
                    <div style="padding-top: 0; font-weight: bold; font-style: italic; line-height: 14px;">Справка по семена</div>
                </a>
            </div>
            <div class="row">
                <a id="diary-plots-report-by-substance" href="javascript:void(0)" class="easyui-linkbutton"
                style="width: 135px; height:135px; margin: 5px; text-align:center; float: left;">
                    <img src="themes/Main/reports.png" width="60" height="60">
                    <div style="padding-top: 0; font-weight: bold; font-style: italic; line-height: 14px;">Справка по препарати</div>
                </a>
                <a id="diary-plots-report-by-other-products" href="javascript:void(0)" class="easyui-linkbutton"
                    style="width: 135px; height:135px; margin: 5px; text-align:center; float: left;">
                    <img src="themes/Main/reports.png" width="60" height="60">
                    <div style="padding-top: 0; font-weight: bold; font-style: italic; line-height: 14px;">Справка по други продукти</div>
                </a>
                <a id="diary-plots-report-by-produces" href="javascript:void(0)" class="easyui-linkbutton"
                   style="width: 135px; height:135px; margin: 5px; text-align:center; float: left;">
                    <img src="themes/Main/reports.png" width="60" height="60">
                    <div style="padding-top: 0; font-weight: bold; font-style: italic; line-height: 14px;">Справка по добиви</div>
                </a>
            </div>
        </div>

        <div id="win-diary-plots-report" class="easyui-window" style="height: 780px; width: 1000px;" data-options="title: 'Справка по парцел',
         iconCls: 'icon-reports', closed: true, collapsible: false, maximizable: false, minimizable: false, resizable: false, modal: true">
            <com:Application.Portlets.Diary.PlotsReport/>
        </div>

        <div id="win-diary-reports" class="easyui-window" style="height: 604px; width: 1000px;" data-options="title: 'Обобщена справка по механизатор/служител',
         iconCls: 'icon-reports', closed: true, collapsible: false, maximizable: false, minimizable: false, resizable: false, modal: true">
            <com:Application.Portlets.Diary.SummaryReportByPerformer/>
        </div>

        <div id="win-diary-detailed-report" class="easyui-window" style="height: 604px; width: 1200px;" data-options="title: 'Подробна справка по механизатор/служител',
         iconCls: 'icon-reports', closed: true, collapsible: false, maximizable: false, minimizable: false, resizable: false, modal: true">
            <com:Application.Portlets.Diary.DetailedReportByPerformer/>

            <div id="diary-detailed-report-toolbar">
                <a href="javascript:void(0)" onclick="printDetailedReport()" class="easyui-linkbutton"
                   data-options="iconCls:'icon-print',plain:true">Отпечатай</a>
                <a href="javascript:void(0)" class="easyui-menubutton"
                   data-options="menu:'#export-detailed-xls-menu',iconCls:'icon-csv',plain:true">Експорт(xls)</a>
                <div id="export-detailed-xls-menu" style="width:150px;">
                    <div onclick="exportXLSDetailedReport()">Всички</div>
                    <div onclick="exportXLSDetailedReport(true)">Текущата страница</div>
                </div>

                <a href="javaScript:void(0)" class="easyui-menubutton"
                   data-options="menu:'#detailed-report-by-menu', iconCls: 'icon-reports', plain: true">Справка по</a>
                <div id="detailed-report-by-menu">
                    <div onclick="displayDetailedReportByDate('date', 'Дата')">Дата</div>
                    <div class="menu-sep"></div>
                    <div onclick="displayDetailedReportByDate('machine', 'Машина')">Машина</div>
                    <div class="menu-sep"></div>
                    <div onclick="displayDetailedReportByDate('attachment', 'Прикачен инвентар')">Прикачен инвентар</div>
                    <div class="menu-sep"></div>
                    <div onclick="displayDetailedReportByDate('zp_name', 'Парцел')">Парцел</div>
                    <div class="menu-sep"></div>
                    <div onclick="displayDetailedReportByDate('event_type', 'Тип обработка')">Тип обработка</div>
                </div>
            </div>
        </div>

        <div id="win-diary-detailed-reports-by" class="easyui-window" style="height: 460px; width: 570px;" data-options="title: 'Индивидуална справка',
         iconCls: 'icon-reports', closed: true, collapsible: false, maximizable: false, minimizable: false, resizable: false, modal: true">
            <div id="detailed-reports-by-grid"></div>
        </div>

        <div id="progress-win" style="width:400px;padding:5px;">
            <div id="progress" style="width:373px;height:40px;"></div>
        </div>

        <div id="win-download" class="easyui-dialog" title="Изтегляне на файл" style="width:260px;height:100px;"
             data-options="iconCls:'icon-files',resizable:true,modal:true,closed:true">
            <div style="text-align: center;padding:10px;">
                <a style="margin-top: 10px;" id="btn-download-file" href="" class="easyui-linkbutton"
                   data-options="iconCls:'icon-files'" target="_blank" onClick="jQuery('#win-download').window('close');">Свали</a>
                <a style="margin-top: 10px;" id="btn-download-file-close" href="javaScript:void(0)"
                   class="easyui-linkbutton" onClick="jQuery('#win-download').window('close');"
                   data-options="iconCls:'icon-cancel'">Откажи</a>
            </div>
        </div>

        <div id="diary_reports_toolbar">
            <a href="javascript:void(0)" id="print-report" class="easyui-linkbutton"
               data-options="iconCls: 'icon-print', plain: true">Отпечатай</a>
            <a href="javascript:void(0)" class="easyui-menubutton"
               data-options="menu:'#export-xls-menu',iconCls:'icon-csv',plain:true">Експорт(xls)</a>
            <div id="export-xls-menu" style="width:150px;">
                <div id="export-xls-report">Всички</div>
                <div id="export-xls-report-page">Текущата страница</div>
            </div>
        </div>

        <div id="summary-report-performer-toolbar">
            <a href="javascript:void(0)" onClick="printSummaryDiaryReport()" class="easyui-linkbutton"
               data-options="iconCls: 'icon-print', plain: true">Отпечатай</a>
            <a href="javascript:void(0)" class="easyui-menubutton"
               data-options="menu:'#summary-export-xls-menu',iconCls:'icon-csv',plain:true">Експорт(xls)</a>
            <div id="summary-export-xls-menu" style="width:150px;">
                <div onClick="exportXLSSummaryDiaryReport()">Всички</div>
                <div onClick="exportXLSSummaryDiaryReport(true)">Текущата страница</div>
            </div>
            <a href="javascript:void(0)" onClick="viewDetailedReportByPerformer()" class="easyui-linkbutton"
               data-options="iconCls: 'icon-reports', plain: true">Подробна справка</a>
        </div>

        <div id="detailed-reports-by-toolbar">
            <a href="javascript:void(0)" onClick="printDetailedReportsBy()" class="easyui-linkbutton"
               data-options="iconCls: 'icon-print', plain: true">Отпечатай</a>
            <a href="javascript:void(0)" class="easyui-menubutton"
               data-options="menu:'#detailed-reports-by-export-xls-menu',iconCls:'icon-csv',plain:true">Експорт(xls)</a>
            <div id="detailed-reports-by-export-xls-menu" style="width:150px;">
                <div onClick="exportXLSDetailedReportsBy()">Всички</div>
                <div onClick="exportXLSDetailedReportsBy(true)">Текущата страница</div>
            </div>
        </div>
        <div id="win-get-wialon-token" class="easyui-window" title="Упълномощаване" style="width: 250px; padding: 10px;"
             data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-password', maximizable: false, minimizable: false, modal: true">

            <div class="messager-icon messager-info"></div>
            <p>Нужен ни е достъп до Вашия акаунт за проследяване.</p>

            <table width="100%" style="margin-top: 10px;" cellspacing="0" cellpadding="0">
                <tr>
                    <td style="text-align:center;">
                        <a href="javascript:void(0)" onClick="getWialonToken()" class="easyui-linkbutton">Вход</a>
                    </td>
                </tr>
            </table>
        </div>
</com:TContent>
