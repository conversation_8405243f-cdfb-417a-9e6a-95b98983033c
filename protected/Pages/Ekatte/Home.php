<?php

use TF\Application\Common\MTPage;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Home class.
 *
 * Home page of Ekatte plugin
 */
class Home extends MTPage
{
    /**
     * onInit event.
     */
    public function onInit($param)
    {
        parent::onInit($param);

        // $this->initController('Plugins.Core.Users');
        $this->UsersController = new UsersController();
    }

    public function deleteSelectedEkatte($sender, $params)
    {
        if ($this->User->isGuest) {
            return [];
        }

        $data = $params->CallbackParameter;
        $id_array = [];
        for ($i = 0; $i < count($data); $i++) {
            $id_array[] = $data[$i]->id;
        }

        $this->UsersController->deleteEkatteRelation($this->User->UserID, $id_array);
    }
}
