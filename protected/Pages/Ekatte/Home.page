<com:TContent ID="Content">
	<script type="text/javascript" src="lib/js/ekatte/ekatte-home.js"></script>
	
	<script type="text/javascript">          
        var requestDelete = <%= $this->DeleteCallBack->ActiveControl->Javascript %>;
        var isSuperAdmin = '<%=$this->User->IsSuperAdmin%>';
        var userid = '<%=$this->User->UserID%>';
        var sessionid = '<%=session_id()%>';
    </script> 
    
    <com:TCallback ID="DeleteCallBack" OnCallback="deleteSelectedEkatte" />
    
    <div class="easyui-layout" data-options="fit:true">
    	<com:TConditional Visible="<%= LEGACY_MODE %>">
			<prop:TrueTemplate>
				<div data-options="region:'north'" style="height:40px; border: 0px;">
					<com:Application.Portlets.NavigationMenu ID="Navigation" />            
				</div>
			</prop:TrueTemplate>
        </com:TConditional>
        <div data-options="region:'west'" style="padding:5px;border-left: 0px;width:300px;">     
            <div id="property-win" style="border:0px;">
		        <table id="property-grid" style="width:350px;border:0px;"></table>  
		    </div> 
        </div>
        <div data-options="region:'center'" style="padding:5px;border-left: 0px;">

            <table id="ekatte-tables" ></table>

        </div>  
    </div>
    
    <div id="win-add" class="easyui-window" title="Парцел" data-options="iconCls:'icon-users', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" style="width:350px;height:220px;">
        <com:Application.Pages.Ekatte.AddEkatte />
    </div>
    
    <script>
    	var requestDelete = <%= $this->DeleteCallBack->ActiveControl->Javascript %>;
    </script>
</com:TContent>