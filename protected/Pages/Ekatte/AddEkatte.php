<?php

use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Add class.
 *
 * Add page of Ekatte plugin.
 */
// Prado::using('Plugins.Core.Users.conf');
// Prado::using('Plugins.Core.Users.*');

class AddEkatte extends Portlet
{
    private $UsersController = false;

    /**
     * onInit event.
     */
    public function onInit($param)
    {
        parent::onInit($param);

        $this->UsersController = new UsersController('Users');
    }

    public function getRequiredAddData()
    {
        return [];
    }

    public function AddButtonClicked()
    {
        $this->UsersController->addNewUserEkatteRelation($this->User->UserID, $this->Ekatte->Text);
    }
}
