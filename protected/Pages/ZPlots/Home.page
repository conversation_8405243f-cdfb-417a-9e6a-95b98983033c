<com:TContent ID="Content">

    <script type="text/javascript">
        var isSuperAdmin = '<%=$this->User->IsSuperAdmin%>';
        var userid = '<%=$this->User->UserID%>';
        var sessionid = '<%=session_id()%>';
        var layers = [];
        var wmsServer = '<%=WMS_SERVER%>';
        var mapPath = '<%=WMS_MAP_PATH%>';
        var layersKvs = [];
        var groupID = '<%=$this->User->GroupID%>';
    </script>

    <com:TRepeater ID="Repeater" EnableViewState="false">
        <prop:ItemTemplate>
            <script type="text/javascript" src="<%#$this->Data%>"></script>
        </prop:ItemTemplate>
    </com:TRepeater>

    <div class="easyui-layout" data-options="fit:true">
        <com:TConditional Visible="<%= LEGACY_MODE %>">
			<prop:TrueTemplate>
				<div data-options="region:'north'" style="height:40px; border-top: none;">
					<com:Application.Portlets.NavigationMenu ID="Navigation" />            
				</div>
			</prop:TrueTemplate>
        </com:TConditional>
        <div data-options="region:'west'" style="padding:5px; width:280px;"> 
            
            <div class="easyui-layout" data-options="fit:true">
                <div data-options="region:'center', collapsible:false, iconCls: 'icon-edit-geometry', title: 'Земеделски парцели'"> 
                    <div id="layers-tree"></div>
                </div>

                <div data-options="region:'south', collapsible:false, split: true" style="height: 300px;"> 
                    <div id="report-tables"></div>
                </div>
            </div>
        </div>
        <div data-options="region:'center'" style="padding:5px;">

            <div class="easyui-layout" data-options="fit:true" style="" >
                <div data-options="region:'center', collapsible:true, onMaximize:function (){map.render('map')}">
                    <div id="map" style="width:100%; height:100%;"></div>
                    
                    <div class="easyui-window" title="Инструменти" data-options="iconCls:'icon-tools', inline:true, maximizable:false, minimizable:false, resizable:false, closable:false, onBeforeOpen:function(){}" style="top:5px; left: 5px; padding: 7px 7px; width: 170px;">
                        <a href="#" id="map-filter" style="margin: 3px 0px; width: 120px" class="easyui-linkbutton" data-options="" iconCls="icon-zoom-full">По филтър</a>
                        <a href="#" id="map-culture" style="margin: 3px 0px; width: 120px" class="easyui-linkbutton" data-options="plain: false" iconCls="icon-zoom-full">По култура</a>
                        <a id="map-choose-plot" style="margin: 3px 0px; width: 120px" class="easyui-linkbutton" data-options="iconCls: 'icon-info'">Информация</a>
                    </div>
                    
                    <div class="easyui-window" title="Легенда" data-options="iconCls:'icon-legend', inline:true, maximizable:false, minimizable:false, resizable:false, closable:false, onBeforeOpen:function(){}" style="top:155px; left: 5px; padding: 7px 7px; width: 170px; max-height: 195px;">
                        <ul id="legend-tree"></ul>
                    </div>
                </div>
                <div data-options="region:'south', collapsible:true, iconCls: 'icon-intersection', border: false" style="height: 300px; padding-top: 5px;"> 
                    <table id="zp-tables"></table>
                </div>
            </div>

        </div>  
    </div>
    
    <div id="win-map-kvs" class="easyui-window" title="Карта" 
         data-options="iconCls:'icon-map', 
         closed: true, 
         resizable: false,  
         minimizable: false, 
         collapsible: false, 
         maximizable: true, 
         modal: true,
         onOpen: function(){mapKVS.render('map-kvs')},
         onMaximize:function (){mapKVS.render('map-kvs')},
         closable:true" 
         style="width:1024px; height:600px;" >
         
        <div id="layout-test" class="easyui-layout" data-options="fit:true">
        	<div data-options="region: 'north'" style="width: 100%; height: 34px">
        		 <a id="map-select-kvs" style="margin: 3px 3px 3px 0px;" class="easyui-linkbutton" data-options="iconCls: 'icon-info', plain: true">Подробна информация</a>
        	</div>
            <div data-options="region:'center'" style="width:100%; border: 0px">
                <div id="map-kvs" style="width:100%; height:100%;"></div>
            </div>
            <div data-options="region:'east'" style="width:240px; border: 0px">
                <ul class="easyui-tree"> 
                    <li data-options="iconCls:'no-background no-padding'">
                        <span>Правно основание</span>
                    </li>
                </ul>
                <ul id="legend-kvs-tree" style="margin-left:15px;"></ul>
                <ul class="easyui-tree">
                    <li data-options="iconCls:'no-background no-padding'">
                        <span><div style='width:13px;height:13px;background-color:none;margin-top:3px;float:left;margin-right:3px;border:2px;border-color:#00ff00;border-style:solid;'></div>Земеделски парцели</span>
                    </li>
                    
                    <li data-options="iconCls:'no-background no-padding'">
                        <span><div id="zp-kvs-legend-color" style='width:13px;height:13px;background-color:none;margin-top:3px;float:left;margin-right:3px;border:2px;border-color:#000000;border-style:solid;'></div>КВС имоти</span>
                    </li>
                </ul>
            </div>
        </div>
    </div>
    
    <div id="win-kvs-plots" class="easyui-window" title="Пресичане с имоти" style="width:1124px; height:550px;" data-options="iconCls:'icon-edit-geometry', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
       <table id="kvs-plots-tables"></table>
    </div>
    
    <div id="win-edit-zp" class="easyui-window" title="Редакция" style="width:350px; height:480px;" data-options="iconCls:'icon-edit', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
       <com:Application.Portlets.ZPlots.EditZP />
    </div>
    
    <div id="win-info-pg" class="easyui-window" title="Информация" style="width:350px; height:360px; border: none;" data-options="iconCls:'icon-info', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
       <table id="zp-propertygrid"></table>
    </div>
    
    <div id="win-multi-edit" class="easyui-window" title="Мултиредакция" style="width:310px; height:170px; padding-top: 10px;" data-options="iconCls:'icon-edit', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
        <com:Application.Portlets.ZPlots.MultiEdit />
    </div>
    
    <div id="win-kvs-info" class="easyui-window" title="Подробна информация" style="width:1024px; height:600px;" data-options="iconCls:'icon-info', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
        <com:Application.Portlets.ZPlots.KVSInfo />
    </div>
    
    <div id="owner-info-panel" class="easyui-window" title="Информация" style="width:420px; height: 405px; padding:10px;" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-info', maximizable: false, minimizable: false" >
    </div>
    
    <div id="contract-info-panel" class="easyui-window" title="Информация" style="width:420px; height: 545px; padding: 5px 10px 10px 10px;" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-info', maximizable: false, minimizable: false" >
    </div>
    
    <div id="win-report-grid-panel" class="easyui-window" title="Справка" style="width:550px; height: 450px;" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-reports', maximizable: false, minimizable: false" >
    	
    </div>
    
    <div id="win-kvs-intersect-filter" class="easyui-window" title="Филтриране" style="width:270px; height: 195px; padding: 0px;" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-filter', maximizable: false, minimizable: false" >
    	<com:Application.Portlets.ZPlots.KVSIntersectFilter />
    </div>
    
    <div id="win-filter-zplots" class="easyui-window" title="Филтриране" style="width:330px; height: 210px; padding: 0px;" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-filter', maximizable: false, minimizable: false">
    	<com:Application.Portlets.ZPlots.ZPlotsFilter />
    </div>
    
    <div id="progress-win" style="width:400px;padding:5px;">
        <div id="progress" style="width:373px;height:40px;"></div>
    </div>
    
    <div id="win-download" class="easyui-dialog" title="Изтегляне на файл" style="width:260px;height:100px;" data-options="iconCls:'icon-files',resizable:true,modal:true,closed:true"> 
        <div style="text-align: center;padding:10px;">
            <a style="margin-top: 10px;" id="btn-download-file" href="" class="easyui-linkbutton" data-options="iconCls:'icon-files'" target="_blank" onClick="jQuery('#win-download').window('close');">Свали</a>
            <a style="margin-top: 10px;" id="btn-download-file-close" href="javaScript:void(0)" class="easyui-linkbutton" onClick="jQuery('#win-download').window('close');" data-options="iconCls:'icon-cancel'">Откажи</a> 
        </div>  
    </div> 
</com:TContent>
