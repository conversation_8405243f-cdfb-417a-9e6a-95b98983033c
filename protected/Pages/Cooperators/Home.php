<?php

use TF\Application\Common\MTPage;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

// Prado::using('Plugins.Core.Layers.conf');
// Prado::using('Plugins.Core.Layers.*');

class Home extends MTPage
{
    /**
     * onInit event.
     */
    public function onInit($param)
    {
        parent::onInit($param);

        if ($this->Request['attached']) {
            $data = (object) [];
            $data->file_id = $this->Request['file_id'];
            $this->downloadAttached($data);
        }
    }

    public function downloadAttached($fileData)
    {
        $UserDbController = new UserDbController($this->User->Database);

        if ($this->User->isGuest) {
            return [];
        }

        $options = [
            'tablename' => $UserDbController->DbHandler->tableCooperatorsFiles,
            'where' => [
                'file_id' => ['column' => 'id', 'compare' => '=', 'value' => $fileData->file_id],
            ],
        ];

        $results = $UserDbController->getItemsByParams($options, false, false);
        if (!count($results)) {
            return [];
        }

        $this->getResponse()->setContentType('application/force-download');

        $filePath = COOPERATORS_DOCUMENTS_PATH . $results[0]['group_id'] . '/' . $results[0]['user_id'] . '/' . $results[0]['id'] . '_' . $results[0]['filename'];

        if (file_exists($filePath)) {
            $this->Response->writeFile($filePath);
        } else {
            $this->Response->redirect($this->Service->constructUrl('Cooperators.Home'));
        }
    }
}
