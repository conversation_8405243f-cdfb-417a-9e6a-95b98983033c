<com:TContent ID="Content">

    <script type="text/javascript">
        var sessionid = '<%=session_id()%>';
        var groupID = '<%=$this->User->GroupID%>';
        var userID = '<%=$this->User->UserID%>';
        var userid = '<%=$this->User->UserID%>';
        var wmsServer = '<%=WMS_SERVER%>';
        var mapPath = '<%=WMS_MAP_PATH%>';
        var hasAgroRightsRW = '<%=$this->User->HasAgroRightsRW%>';
    </script>

    <com:TRepeater ID="Repeater" EnableViewState="false">
        <prop:ItemTemplate>
            <script type="text/javascript" src="<%#$this->Data%>"></script>
        </prop:ItemTemplate>
    </com:TRepeater>

    <script src="https://maps.google.com/maps/api/js?v=<%=GOOGLE_VERSION%>&amp;key=<%=GOOGLE_KEY%>"></script>
    <div class="easyui-layout" data-options="fit:true">
        <!-- MAIN NAVIGATION MENU -->
        <com:TConditional Visible="<%= LEGACY_MODE %>">
			<prop:TrueTemplate>
				<div data-options="region:'north'" style="height:40px; border-top: none;">
					<com:Application.Portlets.NavigationMenu ID="Navigation" />            
				</div>
			</prop:TrueTemplate>
        </com:TConditional>
        <!-- LEFT SIDE -->
        <div data-options="region:'west'" style="padding:5px; width:280px;">
            <div class="easyui-layout" data-options="fit:true" style="" >
                <!-- UPPER LEFT SIDE -->
                <div data-options="region:'north', iconCls: 'icon-planting', title: 'Обработки', collapsible:false, split: true, minHeight: (0.3 * window.innerHeight)" style="">
                    <!-- FILES TOOLBAR -->
                    <div style="border-bottom: 1px solid #ddd; background:#fafafa">
                        <span id="tooltip-add-coverage-layer">
                            <a href="#" id="add-coverage-layer"></a>
                        </span>
                        <span id="tooltip-add-coverage-layer">
                            <a href="javscript: void(0)" id="add-coverage-layer-usb"></a>
                        </span>
                        <span id="tooltip-delete-coverage-file">
                            <a href="#" id="delete-coverage-file"></a>
                        </span>
                    </div>
                    <!-- FILES TREE -->
                    <div id="files-tree"></div>
                </div>
                <!-- MIDDLE LEFT SIDE -->
                <div data-options="region:'center', collapsible:false, split: false" style="border: 0">
                    <div id="coverage-structure-panel" style="">
                        <div style="border-bottom: 1px solid #ddd; background:#fafafa">
                            <span id="tooltip-edit-event">
                                <a href="#" id="edit-event"></a>
                            </span>
                        </div>
                        <!-- STRUCTURE TREE -->
                        <div id="structure-tree"></div>
                    </div>
                </div>
            </div>
        </div>
        <!-- RIGHT SIDE -->
        <div data-options="region:'center'" style="padding: 5px;">

            <div class="easyui-panel" data-options="fit:true">
                <!-- MAP CONTAINER -->
                <div id="map" style="width:100%; height:100%;"></div>

                <!-- MAP LEGEND -->
                <com:TConditional Condition="filter_var(LEGACY_MODE, FILTER_VALIDATE_BOOLEAN)">
                    <prop:TrueTemplate>
                        <!-- Add more top offset in legacy mode because of the agrimi bar-->
                        <div id="map-tools" class="easyui-window" data-options="onBeforeOpen:function(){}" style="top: 100px; left: 300px; padding: 7px 7px 7px 10px;"> <a href="#" id="tool-panzoom" title="Мащабиране с ръка" class="easyui-linkbutton" iconCls="icon-nav-pan"></a>
                    </prop:TrueTemplate>
                    <prop:FalseTemplate>
                        <div id="map-tools" class="easyui-window" data-options="onBeforeOpen:function(){}" style="top: 15px; left: 350px; padding: 7px 7px 7px 10px;"> <a href="#" id="tool-panzoom" title="Мащабиране с ръка" class="easyui-linkbutton" iconCls="icon-nav-pan"></a>
                    </prop:FalseTemplate>
                </com:TConditional>
                    <a href="#" id="tool-zoomin" title="Мащабиране с увеличение" class="easyui-linkbutton"  iconCls="icon-zoom-in"></a>
                    <a href="#" id="tool-zoomout" title="Мащабиране с намаление" class="easyui-linkbutton" iconCls="icon-zoom-out"></a>
                    <a href="#" id="tool-measure-polygon" title="Измерване на площ" class="easyui-linkbutton" iconCls="icon-measure-polygon"></a>
                    <a href="#" id="tool-measure-line" title="Измерване на разстояние" class="easyui-linkbutton" iconCls="icon-measure-line"></a>
                </div>

                <!-- MAP TYPES WINDOW -->
                <com:TConditional Condition="filter_var(LEGACY_MODE, FILTER_VALIDATE_BOOLEAN)">
                    <prop:TrueTemplate>
                        <!-- Add more top offset in legacy mode because of the agrimi bar-->
                        <div id="map-types-window" class="easyui-window" data-options="onBeforeOpen:function(){}" style="top: 100px; left: 600px; padding: 7px 7px 7px 10px;">
                    </prop:TrueTemplate>
                    <prop:FalseTemplate>
                        <div id="map-types-window" class="easyui-window" data-options="onBeforeOpen:function(){}" style="top: 15px; left: 650px; padding: 7px 7px 7px 10px;">
                    </prop:FalseTemplate>
                </com:TConditional>
                    <a href="#" id="map-coverage" class="easyui-linkbutton" iconCls="icon-planting" style="">Обработки</a>
                    <a href="#" id="map-altitude" class="easyui-linkbutton" iconCls="icon-height">Надморска височина</a>
                </div>

                <!-- AREA LEGEND -->
                <com:TConditional Condition="filter_var(LEGACY_MODE, FILTER_VALIDATE_BOOLEAN)">
                    <prop:TrueTemplate>
                        <!-- Add more top offset in legacy mode because of the agrimi bar-->
                        <div id="area-legend-window" class="easyui-window" data-options="onBeforeOpen: function(){}" style="top: 195px; left: 300px;">
                    </prop:TrueTemplate>
                    <prop:FalseTemplate>
                        <div id="area-legend-window" class="easyui-window" data-options="onBeforeOpen: function(){}" style="top: 110px; left: 350px;">
                    </prop:FalseTemplate>
                </com:TConditional>
                    <!-- AREA LEGEND -->
                    <table id="area-legend-grid" style=""></table>
                </div>
            </div>
        </div>
        <div data-options="region:'east'" style="padding:5px; width: 225px">
            <div class="easyui-layout" data-options="fit:true" style="" >
                <!-- UPPER LEFT SIDE -->
                <div data-options="region:'north', title: 'Земеделски парцели(слоеве)', iconCls: 'icon-edit-geometry', collapsible:false, split: true, minHeight: (0.3 * window.innerHeight)" style="">
                    <!-- ZP TREE -->
                    <div style="border-bottom: 1px solid #ddd; background:#fafafa">
                        <span id="tooltip-edit-zp">
                            <a href="#" id="edit-zp"></a>
                        </span>
                    </div>
                    <div id="zp-layers-tree"></div>
                </div>
                <!-- MIDDLE LEFT SIDE -->
                <div data-options="region:'center', title: 'Земеделски парцели', iconCls: 'icon-edit-geometry', collapsible:false, split: false" style="">
                    <div id="zp-data-tree" style="">

                    </div>
                </div>
                <div data-options="region:'south', collapsible:false, split: true, minHeight: (0.3 * window.innerHeight), title: 'Легенда', iconCls: 'icon-legend'" style="">
                    <!-- ALTITUDE LEGEND -->
                    <div id="height-legend"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- OUTER WINDOWS -->
    <!-- progress windows - loading item -->
    <div id="progress-win" style="width:400px;padding:5px;">
        <div id="progress" style="width:373px;height:40px;"></div>
    </div>
    <!-- coverage file upload window -->
    <div id="win-add-file" title="Добавяне на файл" style="width: 450px; height: 320px;">
        <div id="uploader">
            <p>You browser doesn't have Flash, Silverlight, Gears, BrowserPlus or HTML5 support.</p>
        </div>
    </div>
    <div id="win-add-file-usb" style="width: 450px; height: 310px;">
        <com:Application.Portlets.Coverage.UploadFilesFromModem/>
    </div>

    <div id="win-edit-event" class="easyui-window" style="width:405px; height:220px; padding: 5px;">
        <com:Application.Portlets.Coverage.EditEvent />
    </div>

    <div id="win-edit-zp" class="easyui-window" style="width:405px; height:320px; padding: 5px;">
        <com:Application.Portlets.Coverage.EditZP />
    </div>

</com:TContent>
