<com:TContent ID="Content">
    <script type="text/javascript">
		var isSuperAdmin = '<%=$this->User->IsSuperAdmin%>';
        var hasPlotRightsRW = '<%=$this->User->HasPlotRightsRW%>';
		var userid = '<%=$this->User->UserID%>';
		var groupid = '<%=$this->User->GroupID%>';
		var sessionid = '<%=session_id()%>';
    </script>

    <com:TRepeater ID="Repeater" EnableViewState="false">
        <prop:ItemTemplate>
            <script type="text/javascript" src="<%#$this->Data%>"></script>
        </prop:ItemTemplate>
    </com:TRepeater>

    <div class="easyui-layout" data-options="fit:true">
		<com:TConditional Visible="<%= LEGACY_MODE %>">
			<prop:TrueTemplate>
				<div data-options="region:'north'" style="height:40px; border-top: none;">
					<com:Application.Portlets.NavigationMenu ID="Navigation" />            
				</div>
			</prop:TrueTemplate>
        </com:TConditional>

       
        
        <div data-options="region:'center'" style="padding:5px;">
            <div class="easyui-layout" data-options="fit:true">
               <div>Страница Анекси е преместена в договори!</div>
            </div>
        </div>  
    </div>

	<div id="progress-win" style="width:400px;padding:5px;">
        <div id="progress" style="width:373px;height:40px;"></div>
    </div>

    <div id="renta-error-win">
	    <div style="text-align:center;margin-top:30px;font-size:15px;">
	    	Вече имате рента от този тип!
	    	<a style=";margin-top:20px;" id="btn-close-renta-error-win" href="javaScript:void(0)" class="easyui-linkbutton" onclick="jQuery('#renta-error-win').window('close');" data-options="iconCls: 'icon-ok'">Затвори</a>
	    </div>
    </div>
    
	<div id="win-add-file" class="easyui-window" title="Добавяне на файл" data-options="iconCls:'icon-add', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true" style="width:450px;height:310px;">
		<div id="uploader">
			<p>You browser doesn't have Flash, Silverlight, Gears, BrowserPlus or HTML5 support.</p>	    
		</div>
	</div>
	
	<div id="win-plots-add" class="easyui-window" title="Имоти от договор" style="width:675px; height:530px;"
		 data-options="iconCls:'icon-add', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
		<table id="annex-plots-add-tables" ></table>
	</div>
	
	<div id="win-add-edit-annex" class="easyui-window" title="Редактиране на анекс" style="height:585px;width:465px;"
		 data-options="iconCls:'icon-agreements', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
		<com:Application.Portlets.Annexes.AddEditAnnex />
	</div>

    <div id="win-filter-annexes" class="easyui-window" title="Филтър" style="overflow: hidden"
		 data-options="iconCls:'icon-filter', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
        <com:Application.Portlets.Contracts.ContractsFilter />
    </div>
	
	<div id="win-add-plots-filter" class="easyui-window" title="Филтриране" style="width:300px; height:175px;"
         data-options="iconCls:'icon-filter', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
        <com:Application.Portlets.Annexes.PlotsFilter />
    </div>
    
    <div id="win-plot-owner-add" class="easyui-window" title="Добавяне на собственици към анекс" style="width:1007px; height:645px;"
         data-options="iconCls:'icon-add', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true"  >
        <com:Application.Portlets.Contracts.SetContractOwnerData />
    </div>
    
    <div id="win-add-owner-filter" class="easyui-window" title="Филтриране" style="width:310px; height:250px;"
         data-options="iconCls:'icon-filter', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
        <com:Application.Portlets.Contracts.OwnersFilter />
    </div>
    
    <div id="win-add-new-owner" class="easyui-window" title="Добавяне на собственик" style="width:775px; height:540px;"
		 data-options="iconCls:'icon-users', closed: true, resizable: false,minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
		<com:Application.Portlets.Contracts.AddOwners />
	</div>
    
    <div id="win-owner-reps-filter" class="easyui-window" title="Филтриране" style="width:280px; height:190px;"
         data-options="iconCls:'icon-filter', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
        <com:Application.Portlets.Contracts.OwnersRepsFilter />
    </div>
    
    <div id="win-edit-contract-owner-data" class="easyui-window" title="Редакция на собственост"" 
         data-options="iconCls:'icon-edit', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
        <com:Application.Portlets.Contracts.EditOwnerPercent />
    </div>

    <div id="win-download" class="easyui-dialog" title="Изтегляне на файл" style="width:260px;height:100px;" data-options="iconCls:'icon-files',resizable:true,modal:true,closed:true"> 
        <div style="text-align: center;padding:10px;">
            <a style="margin-top: 10px;" id="btn-download-file" href="" class="easyui-linkbutton" data-options="iconCls:'icon-files'" target="_blank" onclick="jQuery('#win-download').window('close');">Свали</a>
            <a style="margin-top: 10px;" id="btn-download-file-close" href="javaScript:void(0)" class="easyui-linkbutton" onClick="jQuery('#win-download').window('close');" data-options="iconCls:'icon-cancel'">Откажи</a> 
        </div>  
    </div> 

    <div id="win-choose-heritor" class="easyui-window" title="Избери наследник" 
        data-options="iconCls:'icon-users', closed: true, resizable: false,minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" 
        style="padding: 10px;" >
        <com:Application.Portlets.Owners.AddOwnersHeritors />
    </div>

    <div id="win-edit-owner-representative" class="easyui-window" title="Избери представител"
         data-options="iconCls:'icon-users', closed: true, resizable: false,minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true"
         style="padding: 10px; width: 650px;height: 590px;" >
        <com:Application.Portlets.Owners.EditOwnerRepresentative />
    </div>


    <div id="contracts-owners-toolbar">
        <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-add',plain:true" onclick="addOwnerToAnnex();">Добавяне</a>
        <a href="javaScript:void(0)" title="Филтър" class="easyui-menubutton" data-options="menu:'#edit-owner-submenu', iconCls: 'icon-edit', plain: true">Редактиране</a>
        <div id="edit-owner-submenu">
            <div data-options="iconCls: 'icon-edit'" onclick="editAnnexOwnerPercentage()">Собственост</div>
            <div data-options="iconCls: 'icon-edit'" onclick="editAnnexOwnerData();">Собственик</div>
            <div data-options="iconCls: 'icon-edit'" onclick="editAnnexOwnerRepresentative();">Представител</div>
        </div>
        <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-delete',plain:true" onclick="deleteAnnexPlotOwnerRelation();">Премахване</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-copy',plain:true" onclick="copyAnnexPlotOwnerRelation();">Копиране</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-info',plain:true" onclick="displayAnnexOwnerInformation();">Информация</a>
    </div>
</com:TContent>