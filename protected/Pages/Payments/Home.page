<com:TContent ID="Content">
    <script type="text/javascript">
		var isSuperAdmin = '<%=$this->User->IsSuperAdmin%>';
		var userid = '<%=$this->User->UserID%>';
		var sessionid = '<%=session_id()%>';
    </script>

    <com:TRepeater ID="Repeater" EnableViewState="false">
        <prop:ItemTemplate>
            <script type="text/javascript" src="<%#$this->Data%>"></script>
        </prop:ItemTemplate>
    </com:TRepeater>

	<div class="easyui-layout" data-options="fit:true">
		<com:TConditional Visible="<%= LEGACY_MODE %>">
			<prop:TrueTemplate>
				<div data-options="region:'north'" style="height:40px; border-top: none;">
					<com:Application.Portlets.NavigationMenu ID="Navigation" />            
				</div>
			</prop:TrueTemplate>
        </com:TConditional>
		<div data-options="region:'west'" style="width:300px; height:500px; padding:5px;">
			<div class="easyui-layout" data-options="fit: true" >
				<div data-options="region: 'north'" style="height:63px;border-bottom: none;overflow-y: inherit;">
					<div class="easyui-panel" data-options="title: 'Договори', border: false, iconCls: 'icon-contract'">
						<div style="background:#fafafa">
							<span class="easyui-tooltip" data-options="position: 'right', content: 'Филтриране'">
								<a id="btn-open-contracts-filter" href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls: 'icon-filter', plain: true"></a>
							</span>
							<span class="easyui-tooltip" data-options="position: 'right', content: 'Покажи всички'">
								<a id="btn-clear-contracts-filter" href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls: 'icon-clear-filter', plain: true"></a>
							</span>
							<span style="font-size: 15px; color: #ccc;">|</span>
							<span>
								<input type="text" id="search-year" style="width: 160px;" />
							</span>
							<span class="easyui-tooltip" data-options="position: 'right', content: 'Информация за избран договор'">
                                <a href="javascript:void(0)" id="contract-info-button" class="easyui-linkbutton" data-options="iconCls: 'icon-info', plain: true"></a>
                            </span>
							<hr style="margin: 0px; border: none; height: 1px; background: #ddd;">
						</div>
					</div>
				</div>
				<div data-options="region: 'center'" style="border-bottom: 0; border-top: 0;">
					<div id="contracts-tree"></div>
				</div>
				<div data-options="region: 'south'" style="height: 37px; border-top: none; background: #fafafa">
					<hr style="margin: 0px; border: none; height: 2px; background: #ddd;">
					<div id="contracts-tree-pagination"></div>
				</div>

			</div>
		</div>  
		<div data-options="region:'center'" style="padding: 5px;">
			<div class="easyui-layout" data-options="fit: true, border: false" style="">

				<div data-options="region: 'north', border: false" style="height: 210px;">
					
					<div class="easyui-layout" data-options="fit: true, border: false">
						<div data-options="region: 'north'" style="height: 32px; border: none;">
                            <div class="easyui-panel" data-options="fit:true">
                                <div style="background:#fafafa; padding: 1px 2px;">
                                    <a id="btn-view-transactions" href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls: 'icon-dolar', plain: true">Транзакции</a>
									<a id="show-payroll-grid" href="index.php?page=Payroll.Home" class="easyui-linkbutton" data-options="iconCls: 'icon-datagrid', plain: true">Ведомост</a>
									<a id="btnweghingnote" href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls: 'icon-weighing-machine', plain: true">Кантарна бележка</a>
									<a id="btn-payments-reports" href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls: 'icon-reports', plain: true">Справки</a>
									<a id="btn-add-charged-renta" href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls: 'icon-calculator', plain: true">Начисли рента</a>

                                    <a href="javascript:void(0)" class="easyui-linkbutton" id="renta-type"  data-options="iconCls: 'icon-rents', plain: true">Типове рента</a>
                                    <a href="javascript:void(0)" class="easyui-linkbutton" id="payment-subjects"  data-options="iconCls: 'icon-documents', plain: true">Основания в РКО</a>
                                    <a href="javascript:void(0)" class="easyui-linkbutton" id="payment-numbering"  data-options="iconCls: 'icon-template', plain: true">Номерация РКО</a>
								</div>
                            </div>
                        </div>
						<div data-options="region: 'west', border: false" style="width: 504px; padding:5px 5px 5px 0px;">
                            <div class="easyui-tabs" data-options="fit:true, border: true">
                                <div title="Информация" data-options="iconCls:'icon-info'">
                                    <fieldset style="border: 1px solid #000; margin: 5px;" >
                                        <legend style="margin-left: 10px; font-style: italic; font-weight: bold;">Основна информация</legend>
                                        <table style="padding:5px;margin: 5px 10px 0px 10px;">
                                            <tr>
                                                <td style="width:100px;"><span style=" font-style: italic; font-weight: bold;">Тип:</span></td>
                                                <td><span id="info-contract-type"></span></td>
                                                <td style="width:100px;padding-left: 50px;"><span style=" font-style: italic; font-weight: bold;">Влизане в сила:</span></td>
                                                <td><span id="info-start-date"></span></td>
                                            </tr>
                                            <tr>
                                                <td style="width:100px;"><span style=" font-style: italic; font-weight: bold;">Номер:</span></td>
                                                <td><span id="info-contract-number"></span></td>
                                                <td style="width:100px;padding-left: 50px;"><span style=" font-style: italic; font-weight: bold;">Крайна дата:</span></td>
                                                <td><span id="info-due-date"></span></td>
                                            </tr>

                                            <tr>
                                                <td style="width:100px;"><span style=" font-style: italic; font-weight: bold;">Група:</span></td>
                                                <td><span id="info-contract-group"></span></td>
                                                <td style="width:100px;padding-left: 50px;"><span style=" font-style: italic; font-weight: bold;">Стопанство:</span></td>
                                                <td><span id="info-farming"></span></td>
                                            </tr>
											<tr>
                                                <td style="width:100px;"><span style=" font-style: italic; font-weight: bold;">Сключване:</span></td>
                                                <td><span id="info-contract-date" ></span></td>
                                            </tr>											<tr>
                                                <td style="width:100px;"><span style=" font-style: italic; font-weight: bold;">Дата падеж:</span></td>
                                               <td><span id="info-contract-pd-date" ></span></td>
                                            </tr>
                                            <tr>
                                                <td style="width:100px;"><span style=" font-style: italic; font-weight: bold;">Забележка:</span></td>
                                                <td colspan="3"><span id="info-comment"></span></td>
                                            </tr>
                                            <tr id="info-is-closed-for-editing-row">
                                                <td style="padding-right: 20px;"><span style=" font-style: italic; font-weight: bold;">Заключен за редакция:</span></td>
                                                <td colspan="3"><span id="info-is-closed-for-editing"></span></td>
                                            </tr>
                                        </table>
                                    </fieldset>
                                    <fieldset style="border: 1px solid #000; margin: 5px;" >
                                        <legend style="margin-left: 10px; font-style: italic; font-weight: bold;">Служба по вписване</legend>
                                        <table style="margin: 4px 10px 4px 10px;">
                                            <tr>
                                                <td style="width:100px;"><span style=" font-style: italic; font-weight: bold;">Номер:</span></td>
                                                <td><span id="info-contract-sv-num"></span></td>
                                                <td style="width:100px; padding: 1px 0px 1px 100px;"><span style=" font-style: italic; font-weight: bold;">Дата:</span></td>
                                                <td><span id="info-contract-sv-date"></span></td>
                                            </tr>
                                        </table>
                                    </fieldset>
                                </div>
                                <div title="Рента/дка. по договор" data-options="iconCls:'icon-rents'">
                                    <fieldset style="border: 1px solid #000; margin: 5px;" >
                                        <legend style="margin-left: 10px; font-style: italic; font-weight: bold;">Рента в пари</legend>
                                        <table style="margin: 10px">
                                            <tr>
                                                <td style="width:100px; padding: 1px 0px;"><span id="info-contract-renta-label" style=" font-style: italic; font-weight: bold;">Сума:</span></td>
                                                <td style="padding-left:200px;"><span id="info-contract-renta"></span></td>
                                            </tr>
                                            <tr>
                                                <td colspan=2> <span id='rent-per-plot-info' style=" font-style: italic; font-weight: bold;">* В договора има имоти с индивидуална рента</span></td>
                                            </tr>
                                        </table>
                                    </fieldset>
                                    <fieldset style="border: 1px solid #000; margin: 5px;" >
                                        <legend style="margin-left: 10px; font-style: italic; font-weight: bold;">Рента в натура</legend>
                                        <table id="js-payment-rents-table" style="margin: 10px">
                                            <tr>
                                                <td style="width:100px; padding: 1px 0px;"><span style=" font-style: italic; font-weight: bold;">Натура - тип:</span></td>
                                                <td style="width:100px; padding: 1px 0px 1px 200px;"><span style=" font-style: italic; font-weight: bold;">Натура - кол.:</span></td>
                                            </tr>
                                        </table>
                                    </fieldset>
                                </div>
							</div>
						</div>
						<div data-options="region: 'center', border: false" style="padding: 5px 0px;">
							<div id="personal-use-tables" ></div>
						</div>
					</div>
					
				</div>
				<div data-options="region: 'center', border: false" style="">
					<div id="contract-payments-tables" data-options="fit: true" style=""></div>
				</div>

			</div>
		</div>

	</div> 

    <!-- typeahead targets must be bound to the body -->
    <div id="search-company-name-typeahead-target" class="tt-menu" style="position: absolute;"></div>
    <div id="search-represent-name-typeahead-target" class="tt-menu" style="position: absolute;"></div>
    <div id="search-owner-name-typeahead-target" class="tt-menu" style="position: absolute;"></div>

	<div id="progress-win" style="width:400px;padding:5px;">
		<div id="progress" style="width:373px;height:40px;"></div>
	</div>

    <div id="win-personal-use-owners-list" class="easyui-window" title="Добавяне на собственик с лично ползване" style="width:860px; height: 520px"
        data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-filter', maximizable: false, minimizable: false, modal: true">
        
        <div class="easyui-layout" data-options="fit: true" style="border:0px;">
            <div data-options="region:'center', border: false, split: true" style="width: 625px;">
                <div id="personal-use-owners-table" ></div>
            </div>
            <div data-options="region:'south', border: false" style="">
                <fieldset style="border: 1px solid #000; padding:3px;">
                    <legend style="font-style: italic; font-weight: bold; margin-left: 10px;">Важно</legend>
                    <div style="height:50px; padding: 5px;">
                        Ако не виждате наследник на когото искате да добавите лично ползване, проверете датата на смърт на собственика. Лично ползване не може да се добавя за стопанската година, в която е починал собственикът.
                    </div>
                </fieldset>
            </div>
        </div>

    </div>

	<div id="win-contracts-filter" class="easyui-window" title="Филтър" style="" 
		 data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-filter', maximizable: false, minimizable: false, modal: true">
		<com:Application.Portlets.Payments.ContractsFilter />
	</div> 

	<div id="win-transactions-filter" class="easyui-window" title="Филтър" style="" 
		 data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-filter', maximizable: false, minimizable: false, modal: true">
		<com:Application.Portlets.Payments.TransactionsFilter />
	</div>
	
	<div id="win-choose-year" class="easyui-window" title="Избор на година" style="width:260px; height: 120px; padding:10px;" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-info', maximizable: false, minimizable: false, modal: true">
		<com:Application.Portlets.Payments.ChooseYear />
	</div> 

	<div id="win-add-charged-renta" class="easyui-window" title="Добавяне на начислена рента" style="width:700px; height: 600px; padding:10px;" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-add', maximizable: false, minimizable: false, modal: true">
		<com:Application.Portlets.Payments.AddChargedRenta />
	</div>

	<div id="win-filter-renta-history" class="easyui-window" title="Филтриране на начислена рента" style="padding:10px;" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-add', maximizable: false, minimizable: false, modal: true">
		<com:Application.Portlets.Payments.FilterRentaHistory />
	</div>  

    <div id="charged-renta-prev-win" class="easyui-window" title="Предварителен преглед на начислена рента" style="padding:10px; width: 980px; height: 600px;" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-add', maximizable: false, minimizable: false, modal: true">
        <com:Application.Portlets.Payments.ChargedRentaPrev />
    </div>

	<div id="win-info-charged-renta" class="easyui-window" title="Информация" style="padding:10px;" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-info', maximizable: false, minimizable: false, modal: true">
		<com:Application.Portlets.Payments.InfoChargedRenta />
	</div> 

	<div id="win-add-charged-renta-history" class="easyui-window" title="История на начислените ренти" style="padding:10px; width: 70%; height: 70%;" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-box', maximizable: false, minimizable: false, modal: true">
		<com:Application.Portlets.Payments.ChargedRentaHistory />
	</div> 
	
	<div id="win-add-payment" class="easyui-window" title="Добавяне на плащане" style="width:800px; height: 670px; padding: 0px 10px;" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-add', maximizable: false, minimizable: false, modal: true, onClose:function(){jQuery('#contract-payments-tables').treegrid('clearSelections')}">
		<com:Application.Portlets.Payments.AddPayment />
	</div>

	<div id="win-add-nat-payment" class="easyui-window" title="Изплащане на рента" style="width:830px; height: 930px; padding: 0px 10px;" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-add', maximizable: false, minimizable: false, modal: true, onClose:function(){jQuery('#contract-payments-tables').treegrid('clearSelections')}">
		<com:Application.Portlets.Payments.AddNatPayment />
	</div>

	<div id="win-transactions" class="easyui-window" title="Транзакции" style="width:1040px; height: 520px;" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-dolar', maximizable: false, minimizable: false, modal: true">
		<table id="transactions-tables"></table>
	</div> 

	<div id="win-transaction-payments" class="easyui-window" title="Подробна инфромация за транзакция" style="width:950px; height: 420px;" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-dolar', maximizable: false, minimizable: false, modal: true">
		<table id="transaction-payments-tables"></table>
	</div>
	
	<div id="win-weighing-note" class="easyui-window" title="Генериране на кантарна бележка" style="width:980px; height: 420px;" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-weighing-machine', maximizable: false, minimizable: false, modal: true">
		<table id="weighing-note-table"></table>
	</div>

    <div id="win-personal-use-plots" class="easyui-window" title="Имоти" style="width:635px; height: 320px;" data-options="closed: true, resizable: false, collapsible: false, maximizable: false, minimizable: false, modal: true">
        <table id="personal-use-plots-grid"></table>
    </div>

    <div id="win-personal-use-rents" class="easyui-window" title="Лично ползване" style="width:1240px; height: 600px;" data-options="closed: true, resizable: false, collapsible: false, maximizable: false, minimizable: false, modal: true">
        <com:Application.Portlets.Payments.EditPersonalUse />
    </div>
	
	<div id="win-choose-report-type" class="easyui-window" title="Избор на вид справка" 
		 data-options="iconCls:'icon-documents', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true"
		 style="" >
		<com:Application.Portlets.Payments.ChooseReportType />
	</div>
	
	<div id="win-payments-report-renta-grid-panel" class="easyui-window" style="height: 480px; width: 1260px;" data-options="title: 'Обобщена справка - Ренти в натура по землище',
         iconCls: 'icon-reports', closed: true, collapsible: false, maximizable: false, minimizable: false, resizable: false, modal: true">
        <com:Application.Portlets.Payments.SummaryReportRentaNatura />
    </div> 

    <div id="win-report-renta-natura-detailed-report" class="easyui-window" style="height: 480px; width: 1260px;" data-options="title: 'Подробна справка - Ренти в натура',
         iconCls: 'icon-reports', closed: true, collapsible: false, maximizable: false, minimizable: false, resizable: false, modal: true">
        <com:Application.Portlets.Payments.DetailedReportRentaNatura />
    </div> 

    <div id="win-payments-report-grid-panel" class="easyui-window" style="height: 480px; width: 1260px;" data-options="title: 'Обобщена справка - Ренти в лева по землище',
         iconCls: 'icon-reports', closed: true, collapsible: false, maximizable: false, minimizable: false, resizable: false, modal: true">
        <com:Application.Portlets.Payments.SummaryReportRentaLeva />
    </div> 

    <div id="win-payments-report-by-date-grid-panel" class="easyui-window" style="height: 480px; width: 1260px;" data-options="title: 'Справка - Изплатени ренти в лева по дата',
         iconCls: 'icon-reports', closed: true, collapsible: false, maximizable: false, minimizable: false, resizable: false, modal: true">
        <com:Application.Portlets.Payments.SummaryReportRentaLevaByDate />
    </div> 

    <div id="win-payments-paid-by-bank" class="easyui-window" style="height: 480px; width: 1260px;" data-options="title: 'Справка - Изплатено в брой/по банков път',
         iconCls: 'icon-reports', closed: true, collapsible: false, maximizable: false, minimizable: false, resizable: false, modal: true">
        <com:Application.Portlets.Payments.SummaryPaidByBank />
    </div> 


    <div id="win-payments-paid-by-bank-and-natura" class="easyui-window" style="height: 480px; width: 1260px;" data-options="title: 'Справка - Изплатено в брой/по банков път, и в натура',
         iconCls: 'icon-reports', closed: true, collapsible: false, maximizable: false, minimizable: false, resizable: false, modal: true">
        <com:Application.Portlets.Payments.SummaryPaidByBankAndNatura />
    </div>


    <div id="win-report-renta-by-date-detailed" class="easyui-window" style="height: 480px; width: 1260px;" data-options="title: 'Подробна справка - Изплатени ренти в лева по дата',
         iconCls: 'icon-reports', closed: true, collapsible: false, maximizable: false, minimizable: false, resizable: false, modal: true">
        <div class="easyui-layout" data-options="fit: true">
		    <div data-options="region: 'center', border: false" style="padding: 8px 5px 5px 5px;">
				<div id="detailed-payment-report-by-date-tables"></div>
			</div>
		</div>
    </div> 
	
	<div id="win-payments-reports-filter" class="easyui-window" title="Филтър"
		 data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-filter', maximizable: false, minimizable: false, modal: true"
		 style="padding:10px;">
		<com:Application.Portlets.Payments.PaymentsReportsFilter />
	</div> 

	<div id="win-download" class="easyui-dialog" title="Изтегляне на файл" style="width:260px;height:100px;"  
         data-options="iconCls:'icon-files',resizable:true,modal:true,closed:true"> 
        <div style="text-align: center;padding:10px;">
            <a style="margin-top: 10px;" id="btn-download-file" href="" class="easyui-linkbutton" data-options="iconCls:'icon-files'" target="_blank" onClick="jQuery('#win-download').window('close');">Свали</a>
            <a style="margin-top: 10px;" id="btn-download-file-close" href="javaScript:void(0)" class="easyui-linkbutton" onClick="jQuery('#win-download').window('close');" data-options="iconCls:'icon-cancel'">Откажи</a> 
        </div>  
    </div>

    <div id="win-info-charged-renta-btn" class="easyui-dialog" title="Информация" style="width:290px; height:132px;"  
         data-options="iconCls:'icon-info',resizable:true,modal:true,closed:true"> 
        <div style="text-align:left; padding:5px;">
            <div style="margin-bottom: 10px">
            	За да <b>премахнете</b> съществуваща стойност в "Лева/дка", в полето въведете  "-".
            </div>
			За да <b>премахнете</b> съществуваща стойност в "Количество натура/дка", в полето въведете 0.”

        </div>  
    </div>

    <div id="toolbar_summary-report-by-ekate-money">
    	<div style="float:left;">
			<a id="print_summary-report-by-ekate-money" class="easyui-linkbutton" data-options="iconCls: 'icon-print', plain: true">Отпечатай</a>
			<a id="export_summary-report-by-ekate-money" class="easyui-linkbutton" data-options="iconCls: 'icon-csv', plain: true">Експорт(xls)</a>
		</div>
		<div style="float:right;">
			<span class="easyui-tooltip" data-options="position: 'left', content: 'Обнови справката'">
				<a id="refresh_summary-report-by-ekate-money" class="easyui-linkbutton" data-options="iconCls: 'icon-reload'"></a>
			</span>	
			Актуално към: <span id="current_date"></span>
		</div>
		<div style="clear:both;"></div>
	</div>

    <div id="report_bank_and_natura_payment_toolbar">
        <a href="javascript:void(0)" class="easyui-menubutton" data-options="menu:'#print-menu-natura',iconCls:'icon-print',plain:true">Отпечатай</a>
        <div id="print-menu-natura" style="width:150px;">
            <div id="print-report-bank-and-natura-payment">Всички</div>
            <div id="print-report-bank-and-natura-payment-page">Текущата страница</div>
        </div>
        <a href="javascript:void(0)" id="export-payment-bank-natura-excel" class="easyui-linkbutton" data-options="iconCls:'icon-csv',plain:true">Експорт(xls)</a>
    </div>

	<div id="report_bank_payment_toolbar">
		<a href="javascript:void(0)" class="easyui-menubutton" data-options="menu:'#print-menu',iconCls:'icon-print',plain:true">Отпечатай</a>
		<div id="print-menu" style="width:150px;">
			<div id="print-report-bank-payment">Всички</div>
			<div id="print-report-bank-payment-page">Текущата страница</div>
		</div>
		<a href="javascript:void(0)" id="export-payment-bank-excel" class="easyui-linkbutton" data-options="iconCls:'icon-csv',plain:true">Експорт(xls)</a>
	</div>

    <div id="report_personal_use_toolbar">
        <a href="javascript:void(0)" class="easyui-menubutton" data-options="menu:'#print-menu',iconCls:'icon-print',plain:true">Отпечатай</a>
        <div id="personal-use-print-menu" style="width:150px;">
            <div id="print-report-personal-use">Всички</div>
            <div id="print-report-personal-use-page">Текущата страница</div>
        </div>
        <a href="javascript:void(0)" id="export-personal-use-excel" class="easyui-linkbutton" data-options="iconCls:'icon-csv',plain:true">Експорт(xls)</a>
    </div>

	<div id="win-generate-payment-document-from-transaction" class="easyui-window" data-options="title: 'Генериране на платежен документ',
		iconCls: 'icon-payments', closed: true, collapsible: false, maximizable: false, minimizable: false, resizable: false, modal: true">
		<com:Application.Portlets.Payments.GeneratePaymentDocumentFromTransaction />
	</div>


</com:TContent>

