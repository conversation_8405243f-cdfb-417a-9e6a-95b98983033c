<com:TContent ID="Content">
    <script type="text/javascript">
        var isSuperAdmin = '<%=$this->User->IsSuperAdmin%>';
        var userid = '<%=$this->User->UserID%>';
        var sessionid = '<%=session_id()%>';
        var wmsServer = '<%=WMS_SERVER%>';
        var mapPath = '<%=WMS_MAP_PATH%>';
        var groupID = '<%=$this->User->GroupID%>';
        var isAdmin = '<%=$this->User->UserLevel == TF\Application\Common\Config::USERS_ADMIN_FLAG%>';
        var login3WmsServer = '<%=LOGIN3_WMS_SERVER%>';
        var login3MapPath = '<%=LOGIN3_WMS_MAP_PATH%>';
        var wialonServer = '<%=WIALON_SERVER%>';
        var trackToken = '<%=$_SESSION["track_token"]%>';
    </script>

    <com:TRepeater ID="Repeater" EnableViewState="false">
        <prop:ItemTemplate>
            <script type="text/javascript" src="<%#$this->Data%>"></script>
        </prop:ItemTemplate>
    </com:TRepeater>
    <div class="easyui-layout" data-options="fit:true">
        <com:TConditional Visible="<%= LEGACY_MODE %>">
			<prop:TrueTemplate>
				<div data-options="region:'north'" style="height:40px; border-top: none;">
					<com:Application.Portlets.NavigationMenu ID="Navigation" />            
				</div>
			</prop:TrueTemplate>
        </com:TConditional>

        <div data-options="region:'west'" style="width:260px; height:500px; padding:5px;">
                    <div id="search-field" class="easyui-panel" title="Филтри"
                         style="width:248px;padding:10px;" data-options="iconCls:'icon-filter'">

                        <div id="search-fields">
                            Начална дата:<br/>
                            <input type="text" id="search-start-date" class="easyui-validatebox" style="width:200px;"/><br/>
                            Крайна дата:<br/>
                            <input type="text" id="search-end-date" class="easyui-validatebox" style="width:200px;"/>
                        </div>
        				<div style="margin: 10px 10px 0px 15px">
        					<a id="btn-search" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-search'">Търси</a>
                    		<a id="btn-clear" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-cancel'" style="margin: 0px;">Откажи</a>
        				</div>
                    </div>
                </div>
                <div data-options="region:'center'" style="padding:5px;">

                    <table id="global-notifications-table"></table>

                </div>
            </div>
    </div>
    <div id="win-add" class="easyui-window" title="Нотификация" data-options="iconCls:'icon-users', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true">
            <com:Application.Pages.GlobalNotifications.AddEdit />
        </div>
    <div id="progress-win" style="width:400px;padding:5px;">
        <div id="progress" style="width:373px;height:40px;"></div>
    </div>
</com:TContent>
