<com:TContent ID="Content">
    <script type="text/javascript">
		var isSuperAdmin = '<%=$this->User->IsSuperAdmin%>';
		var userid = '<%=$this->User->UserID%>';
		var sessionid = '<%=session_id()%>';
        var hasExportMassPaymentRights = '<%=$this->User->HasExportMassPaymentRights%>';
    </script>

    <com:TRepeater ID="Repeater" EnableViewState="false">
        <prop:ItemTemplate>
            <script type="text/javascript" src="<%#$this->Data%>"></script>
        </prop:ItemTemplate>
    </com:TRepeater>

	<div class="easyui-layout" data-options="fit:true">
		<com:TConditional Visible="<%= LEGACY_MODE %>">
			<prop:TrueTemplate>
				<div data-options="region:'north'" style="height:40px; border-top: none;">
					<com:Application.Portlets.NavigationMenu ID="Navigation" />            
				</div>
			</prop:TrueTemplate>
        </com:TConditional>
        <div data-options="region: 'center'" style="border-top: none; border-right: none; height: 60%;">
            <div id="payroll-tables"></div>
        </div>
        <div data-options="region: 'south', split: true" style="border: none; margin-top: 1px; height: 40%;">
            <div id="payroll-by-owner-tables"></div>
        </div>
	</div>

	<div id="progress-win" style="width:400px;padding:5px;">
		<div id="progress" style="width:373px;height:40px;"></div>
	</div>

	<div id="win-download" class="easyui-dialog" title="Изтегляне на файл" style="width:260px;height:100px;"
         data-options="iconCls:'icon-files',resizable:true,modal:true,closed:true">
        <div style="text-align: center;padding:10px;">
            <a style="margin-top: 10px;" id="btn-download-file" href="" class="easyui-linkbutton" data-options="iconCls:'icon-files'" target="_blank" onClick="jQuery('#win-download').window('close');">Свали</a>
            <a style="margin-top: 10px;" id="btn-download-file-close" href="javaScript:void(0)" class="easyui-linkbutton" onClick="jQuery('#win-download').window('close');" data-options="iconCls:'icon-cancel'">Откажи</a>
        </div>
    </div>

    <div id="win-owners-payroll-total-grid" class="easyui-window" title="Общо за Ведомост по хора" style="width:1100px; height: 450px;"
         data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-sum', maximizable: false, minimizable: false">
         <table id="owners-payroll-total-grid"></table>
    </div>

    <div id="win-plots-payroll-total-grid" class="easyui-window" title="Общо за Ведомост по имот" style="width:900px; height: 220px;"
         data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-sum', maximizable: false, minimizable: false">
         <table id="plots-payroll-total-grid"></table>
    </div>

    <div id="win-choose-payroll-print" class="easyui-window" style="width:900px;"
         data-options="title: 'Експорт на ведомост: избор на колони', closed: true, resizable: false, collapsible: false, maximizable: false, minimizable: false, modal: true">
        <com:Application.Portlets.Payroll.ChoosePayrollExportColumns />
    </div>

    <div id="win-payroll-filter" class="easyui-window" data-options="title: 'Филтър',
         iconCls: 'icon-filter', closed: true, collapsible: false, maximizable: false, minimizable: false, resizable: false, modal: true">
        <com:Application.Portlets.Payroll.PayrollFilter />
    </div>

    <div id="win-payroll-egn-filter" class="easyui-window" style="width: 380px; height: 380px;" data-options="title: 'Филтър Име или ЕГН',
         iconCls: 'icon-filter', closed: true, collapsible: false, maximizable: false, minimizable: false, resizable: false, modal: true">
        <com:Application.Portlets.Payroll.PayrollEGNFilter />
    </div>

    <div id="win-payroll-eik-filter" class="easyui-window" style="width: 370px; height: 352px;" data-options="title: 'Филтър Фирма или ЕИК',
         iconCls: 'icon-filter', closed: true, collapsible: false, maximizable: false, minimizable: false, resizable: false, modal: true">
        <com:Application.Portlets.Payroll.PayrollEIKFilter />
    </div>

    <div id="win-payroll-export-tables" class="easyui-window" style="width: 665px; height: 403px;" data-options="title: 'История на ведомостта',
         iconCls: 'icon-export', closed: true, collapsible: false, maximizable: false, minimizable: false, resizable: false, modal: true">
        <div id="payroll-export-tables-layout" class="easyui-layout" data-options="fit:true">
            <div data-options="region:'center', border:false">
                <div id="payroll-export-tables-grid"></div>
            </div>
        </div>
    </div>

    <div id="win-payroll-export-info" class="easyui-window" style="width: 485px; height: 423px;" data-options="title: 'История на ведомостта',
         iconCls: 'icon-export', closed: true, collapsible: false, maximizable: false, minimizable: false, resizable: false, modal: true">
        <com:Application.Portlets.Payroll.PayrollExportInfo />
    </div>

    <div id="win-payroll-export-mass-payment" class="easyui-window" style="width: 435px" data-options="title: 'Експорт на масов файл',
        iconCls: 'icon-export', closed: true, collapsible: false, maximizable: false, minimizable: false, resizable: false, modal: true">
    <com:Application.Portlets.Payroll.PayrollExportMassPayment />

    <div id="payroll-toolbar">
        <span>
            <input type="text" id="payroll-search-year" style="width: 200px;" />
        </span>
    <span>
            <a id="btnfilterpayroll" href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls: 'icon-filter', plain: true">Филтър</a>
        </span>
        <span>
            <a id="btnclearfilterpayroll" href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls: 'icon-clear-filter', plain: true">Покажи всички</a>
        </span>
        <span>
            <a id="btnexportpayroll" href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls: 'icon-csv', plain: true">Експорт</a>
        </span>
        <span>
            <a id="btntotalpayroll" href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls: 'icon-sum', plain: true">Общо за Ведомост по хора</a>
        </span>
    </div>

</div>
</com:TContent>

