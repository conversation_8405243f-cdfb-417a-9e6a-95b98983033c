<com:TContent ID="Content">

    <script src="https://maps.google.com/maps/api/js?v=<%=GOOGLE_VERSION%>&amp;key=<%=GOOGLE_KEY%>"></script>
    <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>


    <com:TRepeater ID="Repeater" EnableViewState="false">
        <prop:ItemTemplate>
            <script type="text/javascript" src="<%#$this->Data%>"></script>
        </prop:ItemTemplate>
    </com:TRepeater>

    <script type="text/javascript">
        var isSuperAdmin = '<%=$this->User->IsSuperAdmin%>',
            userid = '<%=$this->User->GroupID%>',
            sessionid = '<%=session_id()%>',
            groupID = '<%=$this->User->GroupID%>'
            hasCollectionsRightsRW = '<%=$this->User->HasCollectionsRightsRW%>';
        var selectedContractRowId;
    </script>

    <div id="layout-test" class="easyui-layout" data-options="fit:true">
       <com:TConditional Visible="<%= LEGACY_MODE %>">
			<prop:TrueTemplate>
				<div data-options="region:'north'" style="height:40px; border-top: none;">
					<com:Application.Portlets.NavigationMenu ID="Navigation" />            
				</div>
			</prop:TrueTemplate>
        </com:TConditional>
        <div data-options="region: 'center'" style="border-top: none; border-right: none; height: 50%;">
            <div id="collections-tabs" class="easyui-tabs" data-options="fit:true, border: true">
                <div title="Ренти" data-options="iconCls:'icon-rents'">
                    <div id="collection-contracts-table"></div>
                </div>
                <div title="Обработки" data-options="iconCls:'icon-rents'">
                    <div id="collect-treatments-tables"></div>
                </div>
            </div>
        </div>
        <div data-options="region: 'south', split: true" style="border: none; margin-top: 1px; height: 40%;">
            <div id='collections-payments-tabs' class="easyui-tabs" data-options="fit:true, border: true">
                <div title="Плащания от ренти" data-options="iconCls:'icon-rents'">
                    <div style="background:#fafafa">
                        <span>
                            <a id="btn-pay" href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls: 'icon-money', plain: true">Изплащане</a>
                        </span>
                        <span>
                            <a id="btn-unpay" href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls: 'icon-money', plain: true">Сторниране</a>
                        </span>
                    </div>
                    <div id="collection-payments-table"></div>
                </div>
                <div title="Плащания от обработки" data-options="iconCls:'icon-rents'">
                    <div id="collection-personal-use-table"></div>
                </div>
            </div>
        </div>
    </div>

    <div id="win-add-collection" class="easyui-window" title="Добавяне на плащане" style="width:395px; height: 550px; padding: 0px 10px;" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-add', maximizable: false, minimizable: false, modal: true">
        <com:Application.Portlets.Collections.AddCollection />
    </div>

    <div id="win-collections-filter" class="easyui-window" title="Филтър" style="width:610px;"
         data-options="iconCls:'icon-filter', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
        <com:Application.Portlets.Collections.CollectionsFilter />
    </div>

    <div id="win-download" class="easyui-dialog" title="Изтегляне на файл" style="width:260px;height:100px;" data-options="iconCls:'icon-files',resizable:true,modal:true,closed:true">
        <div style="text-align: center;padding:10px;">
            <a style="margin-top: 10px;" id="btn-download-file" href="" class="easyui-linkbutton" data-options="iconCls:'icon-files'" target="_blank" onclick="jQuery('#win-download').window('close');">Свали</a>
            <a style="margin-top: 10px;" id="btn-download-file-close" href="javaScript:void(0)" class="easyui-linkbutton" onClick="jQuery('#win-download').window('close');" data-options="iconCls:'icon-cancel'">Откажи</a>
        </div>
    </div>

    <div id="collections-contracts-toolbar">
        <div class="easyui-panel" data-options="title: 'Договори', border: false, iconCls: 'icon-contract'">
            <div style="background:#fafafa">
               <span>
                    <input type="text" id="collections-search-year" style="width: 200px;" />
                </span>
                <span>
                    <a id="btn-open-contracts-filter" href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls: 'icon-filter', plain: true">Филтър</a>
                </span>
                <span>
                    <a id="btn-clear-contracts-filter" href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls: 'icon-clear-filter', plain: true">Покажи всички</a>
                </span>
                <span>
                    <a id="btn-print-contracts" href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls: 'icon-print', plain: true">Отпечатай</a>
                </span>
                <span>
                    <a id="btn-export-contracts" href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls: 'icon-csv', plain: true">Експорт</a>
                </span>
            </div>
        </div>
    </div>

    <div id="collections-personal-use-toolbar">
            <div class="easyui-panel" data-options="title: 'Договори', border: false, iconCls: 'icon-contract'">
                <div style="background:#fafafa">
                   <span>
                        <input type="text" id="pu-collections-search-year" style="width: 160px;" />
                    </span>
                    <span>
                        <a id="btn-pu-open-contracts-filter" href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls: 'icon-filter', plain: true">Филтър</a>
                    </span>
                    <span>
                        <a id="btn-pu-clear-contracts-filter" href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls: 'icon-clear-filter', plain: true">Покажи всички</a>
                    </span>
                    <span>
                        <a id="btn-pu-export-contracts" href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls: 'icon-csv', plain: true">Експорт</a>
                    </span>
                </div>
            </div>
        </div>
    <div id="win-add-nat-payment" class="easyui-window" title="Изплащане на лично ползване" style="width:810px; height: 530px; padding: 0px 10px;" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-add', maximizable: false, minimizable: false, modal: true, onClose:function(){jQuery('#contract-payments-tables').treegrid('clearSelections')}">
    	<com:Application.Portlets.Payments.AddNatPayment />
    </div>
    <div
        id="win-personal-use-filters"
        class="easyui-window"
        title="Филтриране"
        data-options="iconCls:'icon-filter', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true"
    >
        <com:Application.Portlets.Collections.PersonalUseCollectionsFilter/>
    </div>
    <div id="win-cancel-personal-use-collection" class="easyui-window" title="Изплащане на лично ползване" style="width:385px; height: 300px;" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-add', maximizable: false, minimizable: false, modal: true">
        <div style="text-align: left;padding:10px; padding-bottom:0;">
            <label for="cancel-personal-use-collection-reason">Въведете причина за анулирането:</label>
            <textarea name="cancel-personal-use-collection-reason" id="cancel-personal-use-collection-reason" rows="3" style="width: 350px; height: 150px; padding:5px;" ></textarea>
        </div>
        <div style="text-align: center;padding:10px;">
           <a style="margin-top: 10px;" id="btn-cancel-personal-use-collection" href="javascript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-files'" onclick="cancellationPersonalUseCollection();">Анулирай</a>
           <a style="margin-top: 10px;" id="btn-cancel-personal-use-collection-close" href="javaScript:void(0)" class="easyui-linkbutton" onClick="jQuery('#win-cancel-personal-use-collection').window('close');" data-options="iconCls:'icon-cancel'">Откажи</a>
        </div>
    </div>

</com:TContent>
