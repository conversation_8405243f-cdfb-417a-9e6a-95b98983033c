<com:TContent ID="Content">

    <com:TRepeater ID="Repeater" EnableViewState="false">
        <prop:ItemTemplate>
            <script type="text/javascript" src="<%#$this->Data%>"></script>
        </prop:ItemTemplate>
    </com:TRepeater>

    <script type="text/javascript">
        var isSuperAdmin = '<%=$this->User->IsSuperAdmin%>';
        var userid = '<%=$this->User->UserID%>';
        var sessionid = '<%=session_id()%>';
        var wmsServer = '<%=WMS_SERVER%>';
        var mapPath = '<%=WMS_MAP_PATH%>';
        var login3WmsServer = '<%=LOGIN3_WMS_SERVER%>';
        var login3MapPath = '<%=LOGIN3_WMS_MAP_PATH%>';
        var groupID = '<%=$this->User->GroupID%>';
    </script>

    <script src="https://maps.google.com/maps/api/js?v=<%=GOOGLE_VERSION%>&amp;key=<%=GOOGLE_KEY%>"></script>
    <div class="easyui-layout" data-options="fit:true">
       <com:TConditional Visible="<%= LEGACY_MODE %>">
			<prop:TrueTemplate>
				<div data-options="region:'north'" style="height:40px; border-top: none;">
					<com:Application.Portlets.NavigationMenu ID="Navigation" />            
				</div>
			</prop:TrueTemplate>
        </com:TConditional>
        <div data-options="region:'west'" style="padding:5px; width:280px;">

            <div class="easyui-layout" data-options="fit:true">
                <div data-options="region:'center', collapsible:false, iconCls: 'icon-edit-geometry', title: 'ИСАК'">
                    <div id="layers-tree"></div>
                </div>

                <div data-options="region:'south', collapsible:false, split: true" style="height: 300px;">
                    <div id="report-tables"></div>
                </div>
            </div>
        </div>
        <div data-options="region:'center'" style="padding:5px;">

            <div class="easyui-layout" data-options="fit:true" style="" >



                <div data-options="region: 'north', height: 28" style="border-bottom: none; background-color: #fafafa;" >
					<div style="float: left; padding: 4px 10px;">
                        <input id="layer-allowable" type="checkbox" style="margin-top:3px; float:left; vertical-align: text-bottom;"/>
                        <label for="layer-allowable">
                            <div style="width:10px;height:10px;background-color:#fafafa;margin:3px;float:left;border: 1px solid #0000ff"></div>
                            Допустим слой - преди възражения
                        </label>
                    </div>
                    <div style="padding: 4px 10px;">
                        <input id="isak-diff-allowable" type="checkbox" style="margin-top:3px; float:left; vertical-align: text-bottom;"/>
                        <label for="isak-diff-allowable">
                            <div style="width:10px;height:10px;background: repeating-linear-gradient(135deg,#ff0000,#ff0000 1px,#fafafa 2px,#fafafa 4px);margin:3px;float:left;border: 1px solid #fafafa"></div>
                            Площи извън допустим слой
                        </label>
                    </div>
				</div>



                <div data-options="region:'center', collapsible:true, onMaximize:function (){map.render('map')}">
                    <div id="map" style="width:100%; height:100%;"></div>
                </div>
                <div data-options="region:'south', collapsible:true, iconCls: 'icon-intersection', border: false" style="height: 300px; padding-top: 5px;">
                    <table id="isak-table"></table>
                </div>
            </div>

        </div>
    </div>

    <div id="win-isak-diff-allowable" class="easyui-window" title="Парцели с площ извън допустим слой" style="width:450px; height:350px;" data-options="iconCls:'icon-intersection', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: false, closable:true" >
       <table id="isak-diff-allowable-tables"></table>
    </div>

    <div id="win-filter-isak" class="easyui-window" title="Филтриране" style="width:330px; height: 250px; padding: 0px;" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-filter', maximizable: false, minimizable: false">
    	<com:Application.Portlets.Isak.IsakFilter />
    </div>

    <div id="progress-win" style="width:400px;padding:5px;">
        <div id="progress" style="width:373px;height:40px;"></div>
    </div>
</com:TContent>
