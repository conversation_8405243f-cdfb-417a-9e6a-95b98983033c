<?php

use Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Application\Common\MTPage;

class Home extends MTPage
{
    /**
     * onInit event.
     */
    public function onInit($param)
    {
        parent::onInit($param);

        if ($this->Request['export']) {
            $data = (object) [];
            $data->rep_name = $this->Request['rep_name'];
            $data->rep_egn = $this->Request['rep_egn'];
            $data->eik = $this->Request['eik'];
            $data->name = $this->Request['name'];
            $this->exportExcel($data);
        }
    }

    public function exportExcel($data)
    {
        $this->getResponse()->setContentType('application/force-download');
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $params = [
            'where' => [
                'name' => ['column' => 'name', 'prefix' => 'o', 'compare' => 'ILIKE', 'value' => $data->name],
                'eik' => ['column' => 'eik', 'prefix' => 'o', 'compare' => '=', 'value' => $data->eik],
                'rep_egn' => ['column' => 'rep_egn', 'prefix' => 'o', 'compare' => '=', 'value' => $data->rep_egn],
                'company_name' => ['column' => 'company_name', 'prefix' => 'o', 'compare' => 'ILIKE', 'value' => $data->company_name],
                'rep_name' => ['column' => 'rep_name', 'prefix' => 'o', 'compare' => 'ILIKE', 'value' => $data->rep_name],
            ],
        ];

        $results = $UserDbOwnersController->getOwnersData($params, false, false);
        if (0 != count($results)) {
            for ($i = 0; $i < count($results); $i++) {
                unset($results[$i]['id']);
            }
        }

        $path = SITE_PATH . 'uploads/';
        $name = 'owners_' . $this->User->GroupID;
        $fp = fopen($path . '/' . $name . '.csv', 'w');

        for ($i = 0; $i < count($results); $i++) {
            fputcsv($fp, $results[$i]);
        }

        fclose($fp);

        $this->Response->writeFile(SITE_PATH . 'uploads/owners_' . $this->User->GroupID . '.csv');
        unlink(SITE_PATH . 'uploads/owners_' . $this->User->GroupID . '.csv');
    }
}
