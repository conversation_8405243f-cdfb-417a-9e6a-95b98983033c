<com:TContent ID="Content">
    <script data-main="lib/js/warehouseReports/index.js" src="lib/js_external/require.js"></script>

    <script type="text/javascript">
        define('global-config', [], function () {
            return {
                isSuperAdmin: '<%=$this->User->IsSuperAdmin%>',
                hasWarehouseRights: '<%=$this->User->hasWarehouseRights%>',
                userid: '<%=$this->User->UserID%>',
                groupid: '<%=$this->User->GroupID%>',
                sessionid: '<%=session_id()%>'
            }
        });
    </script>
    <style>
        .icon-datagrid {
            background: url('themes/Main/icons/datagrid.png') no-repeat
        }
    </style>
    <div class="easyui-layout" data-options="fit:true">
        <com:TConditional Visible="<%= LEGACY_MODE %>">
			<prop:TrueTemplate>
				<div data-options="region:'north'" style="height:40px; border-top: none;">
					<com:Application.Portlets.NavigationMenu ID="Navigation" />            
				</div>
			</prop:TrueTemplate>
        </com:TConditional>
        <!-- MainPage -->
        <div id='left_pane' data-options="region:'west'" style="width:330px; padding:5px;">
            <div class="easyui-layout" data-options="fit: true">
                <div data-options="region: 'center',title:'Справки', iconCls: 'icon-reports'" style="border-top: none">
                    <div id="reports-tree" style="padding: 3px;"></div>
                </div>
            </div>
        </div>
        <div id='central_region' data-options="region:'center'" style="padding:5px;">
            <div class="easyui-layout" data-options="fit: true">
                <div data-options="region: 'north'" style="height: 62px;">
                    <div class="easyui-panel" data-options="title: 'Справки', border: false, iconCls: 'icon-datagrid'">
                        <div id="generic-toolbar" style="background:#fafafa;">
                            <a id="export-btn" href="javascript:void(0)" class="easyui-menubutton"
                               data-options="menu:'#export-menu',iconCls:'icon-csv',plain:true">Експорт(xls)</a>
                            <div id="export-menu" style="width:150px;">
                                <div id="export-xls-report">Всички</div>
                                <div id="export-xls-report-page">Текущата страница</div>
                            </div>
                            <a id="export-pdf-report" class="easyui-linkbutton" data-options="iconCls:'icon-pdf',plain:true">Експорт(pdf)</a>
                            <a id="filter-report" class="easyui-linkbutton" data-options="iconCls:'icon-filter',plain:true">Филтър</a>
                            <a id="clear-filter-report" class="easyui-linkbutton" data-options="iconCls:'icon-clear-filter',plain:true">Покажи всички</a>
                            <a id="legend-report" class="easyui-linkbutton" data-options="iconCls:'icon-legend',plain:true" style="display: none">Легенда</a>
                        </div>
                    </div>
                </div>
                <div id="main_layout" data-options="region: 'center'">
                    <!-- Sub Transactions -->
                    <div id="report-simple-transactions-layout" class="easyui-layout" data-options="fit:true">
                        <div data-options="region:'center', border:false">
                            <table id="simple-transactions-table"></table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div id="win-history-log" class="easyui-window" title="История" data-options="iconCls:'icon-filter', closed: true, resizable: false, minimizable: false,
        collapsible: false, maximizable: false, modal: true, closable:true" style="height: 600px; width: 1150px;">
        <table id="history-log-table"></table>
    </div>

    <!-- Report Filters -->
    <div id="win-filter-report" class="easyui-window" title="Филтриране" data-options="iconCls:'icon-filter', closed: true, resizable: false, minimizable: false,
        collapsible: false, maximizable: false, modal: true, closable:true">
        <div id="filter_container">
            <fieldset id="reports_filter_company_in">
                <legend>Фирми</legend>
                <div class="container-box">
                    <div class="col-6">
                        <label>Стопанство:</label>
                        <input type="text" class="easyui-combobox js-company-in">
                    </div>
                </div>
            </fieldset>
            <fieldset id="reports_filter_document_dates">
                <legend>Период</legend>
                <div class="container-box">
                        <div class="col-6">
                            <label for="date_from">От дата:</label>
                            <input id="date_from" type="text" class="easyui-datebox">
                        </div>
                    <div class="col-6">
                        <label for="date_to">До дата:</label>
                        <input id="date_to" type="text" class="easyui-datebox">
                    </div>
                </div>
            </fieldset>
            <fieldset id="reports_filter_expiry_date">
                <legend>Срок на годност</legend>
                <div class="container-box">
                    <div class="col-6">
                        <label for="expiry_date_from">От дата:</label>
                        <input id="expiry_date_from" type="text" class="easyui-datebox">
                    </div>
                    <div class="col-6">
                        <label for="expiry_date_to">До дата:</label>
                        <input id="expiry_date_to" type="text" class="easyui-datebox">
                    </div>
                </div>
            </fieldset>
            <fieldset id="reports_filter_companies_group_trasfer">
                <legend>Стопанства</legend>
                <div class="container-box">
                    <div class="col-6">
                        <label>Получател:</label>
                        <input type="text" class="easyui-combobox js-company-in">
                    </div>
                    <div class="col-6">
                        <label for="company-out-transfer">Доставчик:</label>
                        <input type="text" id="company-out-transfer" class="easyui-combobox ">
                    </div>
                </div>
            </fieldset>
            <fieldset id="reports_filter_companies_group">
                <legend>Фирми</legend>
                <div class="container-box">
                    <div class="col-6">
                        <label>Стопанство:</label>
                        <input type="text" class="easyui-combobox js-company-in">
                    </div>
                    <div class="col-6">
                        <label>Контрагент:</label>
                        <input type="text" class="easyui-combobox js-company-out">
                    </div>
                </div>
            </fieldset>
            <fieldset id="reports_filter_companies_group_inversed">
                <legend>Фирми</legend>
                <div class="container-box">
                    <div class="col-6">
                        <label>Стопанство:</label>
                        <input type="text" class="easyui-combobox js-company-in js-inversed">
                    </div>
                    <div class="col-6">
                        <label>Контрагент:</label>
                        <input type="text" class="easyui-combobox js-company-out  js-inversed">
                    </div>
                </div>
            </fieldset>
            <fieldset id="reports_filter_machines">
                <legend>Активи</legend>
                <div class="container-box">
                    <div class="col-6">
                        <label>Актив:</label>
                        <input type="text" class="easyui-combobox js-company-in" id="machine">
                    </div>
                    <div class="col-6">
                        <label for="active-farm">Стопанство:</label>
                        <input type="text" id="active-farm" class="easyui-combobox">
                    </div>
                </div>
            </fieldset>
            <fieldset  id="reports_filter_warehouses_and_items">
                <legend>Складове и артикули</legend>
                <div class="container-box">
                    <div class="col-6">
                        <label for="warehouses">Складове:</label>
                        <input id="warehouses" class="easyui-combobox">
                    </div>
                    <div class="col-6">
                        <label for="items">Артикул:</label>
                        <input type="text" class="easyui-textbox" id="items">
                        <div class="code-include-group">
                            <input type="checkbox" id="code-include">
                            <label for="code-include">Търси и по артикулен код</label>
                        </div>
                    </div>
                </div>
            </fieldset>
            <fieldset  id="reports_filter_note">
                <legend>Бележка</legend>
                <div class="container-box">
                    <div class="col-12">
                        <input id="note" class="easyui-textbox">
                    </div>
                </div>
            </fieldset>

            <fieldset id="reports_filter_items_code">
                <legend>Код на актив</legend>
                <div class="container-box">
                    <div class="col-6">
                        <input id="company-code" class="easyui-textbox">
                    </div>
                    <div class="col-6">
                        
                    </div>
                </div>
            </fieldset>

            <fieldset id="warehouseFields"  class="warehouseFieldsList" style="display: none">
                <legend>Допълнителни полета</legend>
                <div class="fields-list">
                    <div class="container-box">

                    </div>
                </div>
            </fieldset>

            <fieldset  id="reports_filter_document_plot">
                <legend>Парцел и Землище</legend>
                <div class="container-box">
                    <div class="col-6">
                        <label for="plot-name">Име на парцела:</label>
                        <input type="text" class="easyui-textbox" id="plot-name">
                    </div>
                    <div class="col-6">
                        <label for="area">Землище:</label>
                        <input type="text" class="easyui-textbox" id="area">
                    </div>
                </div>
            </fieldset>
            <fieldset id="reports_filter_batch">
                <legend>Партиди</legend>
                <div class="container-box">
                    <div class="col-6">
                        <label for="batch">Партида:</label>
                        <input type="text" id="batch" class="easyui-textbox">
                    </div>
                    <div class="col-6" style="margin-top: 20px">
                        <input type="checkbox" id="noBatch" style="vertical-align:text-top">
                        <label for="noBatch" style="display: inline-block">Покажи без партида</label>
                    </div>
                </div>
            </fieldset>
            <fieldset id="reports_filter_groups">
                <legend>Групи</legend>
                <div class="container-box">
                    <div class="col-6">
                        <label for="companies-groups">Контрагенти:</label>
                        <input type="text" id="companies-groups" class="easyui-combobox">
                    </div>
                    <div class="col-6">
                        <label>Артикули:</label>
                        <input type="text" class="easyui-combobox js-items-groups">
                    </div>
                </div>
            </fieldset>
            <fieldset id="reports_filter_groups_items">
                <legend>Групи</legend>
                <div class="container-box">
                    <div class="col-6">
                        <label>Артикули:</label>
                        <input type="text" class="easyui-combobox js-items-groups">
                    </div>
                </div>
            </fieldset>
            <div class="action-btns">
            <span>
                <a class="easyui-linkbutton js-filter" id="btn-report-filter" data-options="iconCls:'icon-search'">Търси</a>
            </span>
                <span>
                <a class="easyui-linkbutton js-filter-clear closeBtn" data-options="iconCls:'icon-cancel'">Откажи</a>
            </span>
            </div>
        </div>
    </div>
    <!--Manage Contragents Modal-->
    <div id="win-manage-contragents" class="easyui-window" title="Управление на доставчици"
        data-options="iconCls:'icon-users', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true">
        <div style="height: 600px; width: 1150px;">
            <com:Application.Portlets.Warehouse.ManageContragents />
        </div>
    </div>
    <div id="win-download" class="easyui-dialog" title="Изтегляне на файл" style="width:260px;height:100px;"
         data-options="iconCls:'icon-files',resizable:true,modal:true,closed:true">
        <div style="text-align: center;padding:10px;">
            <a style="margin-top: 10px;" id="btn-download-file" href="" class="easyui-linkbutton"
               data-options="iconCls:'icon-files'" target="_blank" onClick="jQuery('#win-download').window('close');">Свали</a>
            <a style="margin-top: 10px;" id="btn-download-file-close" href="javaScript:void(0)"
               class="easyui-linkbutton" onClick="jQuery('#win-download').window('close');"
               data-options="iconCls:'icon-cancel'">Откажи</a>
        </div>
    </div>

    <div id="progress-win" style="width:400px;padding:5px;">
        <div id="progress" style="width:373px;height:40px;"></div>
    </div>

    <com:TRepeater ID="Repeater" EnableViewState="false">
        <prop:ItemTemplate>
            <script type="text/javascript" src="<%#$this->Data%>"></script>
        </prop:ItemTemplate>
    </com:TRepeater>

    <com:Application.Portlets.Warehouse.WarehouseModals ID="WarehouseModals"/>

    <div id="win-choose-sub-doc-export-type" class="easyui-window" title="Избор на експорт" data-options="iconCls:'icon-data', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true" style="padding: 10px;">
        <com:Application.Portlets.Warehouse.ChooseSubDocExportType />
    </div>

</com:TContent>
