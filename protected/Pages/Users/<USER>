<com:TContent ID="Content">
    <script type="text/javascript">          
        var userid = '<%=$this->User->UserID%>';
        var sessionid = '<%=session_id()%>';
        var groupID = '<%=$this->User->GroupID%>';
    </script>

    <com:TRepeater ID="Repeater" EnableViewState="false">
        <prop:ItemTemplate>
            <script type="text/javascript" src="<%#$this->Data%>"></script>
        </prop:ItemTemplate>
    </com:TRepeater>

    <div class="easyui-layout" data-options="fit:true">
		<com:TConditional Visible="<%= LEGACY_MODE %>">
			<prop:TrueTemplate>
				<div data-options="region:'north'" style="height:40px; border-top: none;">
					<com:Application.Portlets.NavigationMenu ID="Navigation" />            
				</div>
			</prop:TrueTemplate>
        </com:TConditional>
        <div data-options="region:'west'" style="width:260px; height:500px; padding:5px;">
            <div id="search-field" class="easyui-panel" title="Филтри"   
                 style="width:248px;padding:10px;" data-options="iconCls:'icon-filter'"> 

                Потребител:<br/>
                <input type="text" id="search-username" class="easyui-validatebox" style="width:225px;"/>
                Email:<br/>
                <input type="text" id="search-email" class="easyui-validatebox" validType="email" style="width:225px;"/>
                Име:<br/>
                <input type="text" id="search-names" class="easyui-validatebox" style="width:225px;"/>
                Модули:<br/>
                <input type="text" id="search-modules" class="easyui-validatebox" style="width:225px;"/>
                Отговорник:<br/>
                <input type="text" id="account-sales-person" class="easyui-validatebox" style="width:225px;"/>
                Статус:<br/>
                <input type="text" id="search-status" class="easyui-validatebox" style="width:225px;"/>
                Тип акаунт:<br/>
                <input type="text" id="search-user-roles" class="easyui-validatebox" style="width:225px;"/>

                <div id="admin-search-fields-1">
                    Временен акаунт:<br/>
                    <input type="text" id="search-trial" class="easyui-validatebox" style="width:225px;"/>
                    Начална дата: &nbsp;&nbsp;&nbsp; Крайна дата:<br/>
                    <input type="text" id="search-start-date" class="easyui-validatebox" style="width:105px;"/> &nbsp;&nbsp;
                    <input type="text" id="search-due-date" class="easyui-validatebox" style="width:105px;"/>
                    Договор изтичащ в периода:<br/>
                    <input type="text" id="search-paid-support-start-date" class="easyui-validatebox" style="width:105px;"/> &nbsp;&nbsp;
                    <input type="text" id="search-paid-support-due-date" class="easyui-validatebox" style="width:105px;"/>
                    <br>
                </div>
                Дата на създаване между:<br/>
                <input type="text" id="search-created-date-from" class="easyui-validatebox" style="width:105px;"/> &nbsp;&nbsp;
                <input type="text" id="search-created-date-to" class="easyui-validatebox" style="width:105px;"/>
                <br>
                <div id="admin-search-fields-2">    
                    Родителски акаунт:<br/>
                    <input type="text" id="search-parent-account" class="easyui-validatebox" style="width:225px;"/>
                    USB модем:<br/>
                    <input type="text" id="search-usb-modem" class="easyui-validatebox" style="width:225px;"/>
                </div>
				<div style="margin: 10px 10px 0px 15px">
					<a id="btn-search" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-search'">Търси</a>
            		<a id="btn-clear" href="#" class="easyui-linkbutton" data-options="iconCls:'icon-cancel'" style="margin: 0px;">Откажи</a>
				</div>
            </div>
        </div>  
        <div data-options="region:'center'" style="padding:5px;">

            <table id="users-tables"></table>

        </div>  
    </div>

    <div id="search-username-typehead" class="tt-menu" style="position: absolute;"></div>
    <div id="search-names-typehead" class="tt-menu" style="position: absolute;"></div>
    <div id="search-email-typehead" class="tt-menu" style="position: absolute;"></div>
    <div id="search-parent-account-typehead" class="tt-menu" style="position: absolute;"></div>

    <div id="progress-win" style="width:400px;padding:5px;">
        <div id="progress" style="width:373px;height:40px;"></div>
    </div>

    <div id="win-add" class="easyui-window" title="Потребител" data-options="iconCls:'icon-users', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true">
        <com:Application.Pages.Users.AddEdit />
    </div>
    
    <div id="win-ekatte" class="easyui-window" title="ЕКАТТЕ" data-options="iconCls:'icon-users', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" style="width:800px; height:638px;">
        <com:Application.Portlets.Users.EkatteGrid />
    </div>

    <div id="win-download" class="easyui-dialog" title="Изтегляне на файл" style="width:260px;height:100px;"
         data-options="iconCls:'icon-files',resizable:true,modal:true,closed:true">
        <div style="text-align: center;padding:10px;">
            <a style="margin-top: 10px;" id="btn-download-file" href="" class="easyui-linkbutton" data-options="iconCls:'icon-files'" target="_blank" onClick="jQuery('#win-download').window('close');">Свали</a>
            <a style="margin-top: 10px;" id="btn-download-file-close" href="javaScript:void(0)" class="easyui-linkbutton" onClick="jQuery('#win-download').window('close');" data-options="iconCls:'icon-cancel'">Откажи</a>
        </div>
    </div>

    <script type="text/javascript">
        setValidateSearchInput();
    </script>
</com:TContent>
