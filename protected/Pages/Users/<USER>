<?php

/**
 * Add class file.
 *
 * <AUTHOR>
 */

use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Add class.
 *
 * Add page of Users plugin.
 */
class AddEdit extends Portlet
{
    /**
     * onInit event.
     */
    public function onInit($param)
    {
        parent::onInit($param);

        // $this->Page->initController('Plugins.Core.Users');
        // $this->Page->initController('Plugins.Core.Farming');
        // $this->Page->initController('Plugins.Core.Layers');
        $this->Page->UsersController = new UsersController();
        $this->Page->FarmingController = new FarmingController();
        $this->Page->LayersController = new LayersController();
    }
}
