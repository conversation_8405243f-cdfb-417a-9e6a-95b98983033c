<com:TContent ID="Content">
    <com:TRepeater ID="Repeater" EnableViewState="false">
        <prop:ItemTemplate>
        </prop:ItemTemplate>
    </com:TRepeater>
    <script src="https://maps.google.com/maps/api/js?v=<%=GOOGLE_VERSION%>&amp;key=<%=GOOGLE_KEY%>"></script>
    <script type="text/javascript" src="lib/js_external/Namespace.js?ver=<%=filemtime('lib/js_external/Namespace.js')%>"></script>
    <script type="text/javascript" src="lib/js/TF/Rpc/ExceptionsList.js?ver=<%=filemtime('lib/js/TF/Rpc/ExceptionsList.js')%>"></script>
    <script type="text/javascript" src="lib/js/TF/Rpc/Exception.js?ver=<%=filemtime('lib/js/TF/Rpc/Exception.js')%>"></script>
    <script type="text/javascript" src="lib/js/TF/Rpc/Rpc.js?ver=<%=filemtime('lib/js/TF/Rpc/Rpc.js')%>"></script>
    <script type="text/javascript" src="lib/js/TF/Rpc/Login/Login.js?ver=<%=filemtime('lib/js/TF/Rpc/Login/Login.js')%>"></script>
    <script type="text/javascript" src="lib/js/TF/Rpc/Login/LoginForm.js?ver=<%=filemtime('lib/js/TF/Rpc/Login/LoginForm.js')%>"></script>
    <script type="text/javascript" src="lib/js/TF/Rpc/Login/ForgottenPassword.js?ver=<%=filemtime('lib/js/TF/Rpc/Login/ForgottenPassword.js')%>"></script>

    <script type="text/javascript" src="lib/js_external/loading-items.js?ver=<%=filemtime('lib/js_external/loading-items.js')%>"></script>
    <script type="text/javascript" src="lib/js/users/users-login.js?ver=<%=filemtime('lib/js/users/users-login.js')%>"></script>

    <div id="login-win"
         class="easyui-panel"
         title="Вход"
         cls="login-win"
         data-options="iconCls:'icon-password',closable:false,
         collapsible:false,minimizable:false,maximizable:false">
         <div class="login-form">
            <div class="login-form-logo"></div>
            <div class="username-container">
                <label>Потребител:</label><br>
                <input type="text" id="username" class="form-field">
            </div>
            <div class="password-container">
                <label>Парола:</label><br>
                <input type="password" id="password" class="form-field">
            </div>
           <div class="forgotten-password-container">
                <a href="javaScript:void(0)" id="link-forgotten-pass-login" style="color: #000; margin: 0; padding: 0px; text-align: center;">Забравена парола?</a>
           </div>
            <div id="login-button-container">
                <a id="btn-login" href="javaScript:void(0)" onClick='doUserLoginActions()' class="easyui-linkbutton" data-options="">Вход</a>
            </div>
         </div>
    </div>

    <div id="win-forgotten-password" class="easyui-window" title="" data-options="closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true, shadow:false, border:false" style="width:350px;">
        <com:Application.Portlets.ForgottenPassword />
    </div>

	<script type="text/javascript">
		var isGuest = '<%=$this->User->isGuest%>';
	</script>
</com:TContent>
