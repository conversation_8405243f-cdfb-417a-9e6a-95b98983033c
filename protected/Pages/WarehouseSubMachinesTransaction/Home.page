<com:TContent ID="Content">
    <script data-main="lib/js/warehouseSubTransaction/index.js" src="lib/js_external/require.js"></script>
    <script type="text/javascript">
        define('global-config', [], function () {
            return {
                isSuperAdmin: '<%=$this->User->IsSuperAdmin%>',
                hasWarehouseRights: '<%=$this->User->hasWarehouseRights%>',
                userid: '<%=$this->User->UserID%>',
                groupid: '<%=$this->User->GroupID%>',
                sessionid: '<%=session_id()%>'
            }
        });
    </script>

    <com:TRepeater ID="Repeater" EnableViewState="false">
        <prop:ItemTemplate>
            <script type="text/javascript" src="<%#$this->Data%>"></script>
        </prop:ItemTemplate>
    </com:TRepeater>

    <div id="cc1" class="easyui-layout warehouse-style" data-options="fit:true,border:false">
       <com:TConditional Visible="<%= LEGACY_MODE %>">
			<prop:TrueTemplate>
				<div data-options="region:'north'" style="height:40px; border-top: none;">
					<com:Application.Portlets.NavigationMenu ID="Navigation" />            
				</div>
			</prop:TrueTemplate>
        </com:TConditional>

        <div data-options="region:'center',border:false">
            <div class="easyui-layout" data-options="fit:true,border:false">
                <div data-options="region:'north',border:false" style="height: 232px;">
                    <div class="easyui-panel" data-options="fit:true,border:false">
                        <div class="easyui-layout" data-options="fit:true,border:false">
                            <div data-options="region:'west',border:false" style="width: 33.33%">
                                <com:Application.Portlets.Warehouse.DocumentInfo ID="DocumentInfo" BlockTitle="Изписване към актив"/>
                            </div>
                            <div data-options="region:'center',border:false" style="width: 33.33%; padding:0 5px 5px;">
                                <com:Application.Portlets.Warehouse.FarmOutInfo ID="FarmOutInfo"/>
                            </div>
                            <div data-options="region:'east',border:false" style="width: 33.33%; padding-bottom: 5px">
                                <com:Application.Portlets.Warehouse.MachineInfo ID="MachineInfo"/>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-options="region:'center'">
                    <com:Application.Portlets.Warehouse.Items ID="Items"/>
                </div>
                <div data-options="region:'south'" style="padding:3px;height: 36px;">
                    <a id="saveTransactionBtn" class="easyui-linkbutton" data-options="iconCls: 'icon-save',plain:true">Запази</a>
                    <a id="saveAndCloseTransactionBtn" class="easyui-linkbutton" data-options="iconCls: 'icon-save',plain:true">Запази и приключи</a>
                </div>
            </div>
        </div>

    </div>

    <!--Add Transactions Modal-->
    <div id="win-sub-transactions" class="easyui-window" title="Изписване"
         data-options="iconCls:'icon-users', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
        <com:Application.Portlets.Warehouse.SubTransaction DisabledPriceField="false"/>
    </div>

    <com:Application.Portlets.Warehouse.RightSlidePanel />
    <com:Application.Portlets.Warehouse.WarehouseModals ID="WarehouseModals"/>

    <!-- hidden field that shows the the type of add transaction-->
    <input type="text" id="transaction_type" value="SUB_MACHINE">

    <div id="win-choose-sub-doc-export-type" class="easyui-window" title="Избор на експорт" data-options="iconCls:'icon-data', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true" style="padding: 10px;">
        <com:Application.Portlets.Warehouse.ChooseSubDocExportType />
    </div>
</com:TContent>
