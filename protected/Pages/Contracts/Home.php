<?php

use Plugins\Core\Farming\FarmingController;
use Plugins\Core\UserDbContracts\UserDbContractsController;
use TF\Application\Common\MTPage;

include_once __DIR__ . '/../../../engine/Plugins/Core/Layers/conf/index.php';

class Home extends MTPage
{
    public function onInit($param)
    {
        parent::onInit($param);

        if ($this->Request['order']) {
            $data[0] = (object) [];
            $data[0]->date = $this->Request['date'];
            $data[0]->rep_name = $this->Request['rep_name'];
            $data[0]->rep_surname = $this->Request['rep_surname'];
            $data[0]->rep_lastname = $this->Request['rep_lastname'];
            $data[0]->address = $this->Request['address'];
            $data[0]->amount = $this->Request['amount'];
            $this->razhodenOrder($data);
        }

        if ($this->Request['export']) {
            $data = (object) [];
            $data->contract = $this->Request['cnumber'];
            $data->date_from = $this->Request['datefrom'];
            $data->date_to = $this->Request['dateto'];
            $data->due_date_from = $this->Request['dateduefrom'];
            $data->due_date_to = $this->Request['datedueto'];
            $data->farming = $this->Request['farming'];
            $data->contract_type = $this->Request['ctype'];
            $this->exportExcel($data);
        }

        if ($this->Request['attached']) {
            $data = (object) [];
            $data->file_id = $this->Request['file_id'];
            $this->downloadAttached($data);
        }
    }

    public function exportExcel($data)
    {
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $FarmingController = new FarmingController('Farming');

        $this->getResponse()->setContentType('application/force-download');

        $name = 'contracts_' . $this->User->UserID;
        $path = SITE_PATH . 'uploads/' . $name;
        @mkdir($path, 0777);

        $params = [
            'return' => [
                '*',
                'date(c_date) as c_converted_date',
                'date(start_date) as converted_start_date',
                'date(due_date) as converted_due_date',
                'date(sv_date) as converted_sv_date',
            ],
            'sort' => 'id',
            'order' => 'DESC',
            'where' => [
                'contract' => ['column' => 'c_num', 'compare' => '=', 'value' => $data->contract],
                'date_from' => ['column' => 'c_date', 'compare' => '>=', 'value' => $data->date_from],
                'date_to' => ['column' => 'c_date', 'compare' => '<=', 'value' => $data->date_to],
                'due_date_from' => ['column' => 'due_date', 'compare' => '>=', 'value' => $data->due_date_from],
                'due_date_to' => ['column' => 'due_date', 'compare' => '<=', 'value' => $data->due_date_to],
                'farming' => ['column' => 'farming_id', 'compare' => '=', 'value' => $data->farming],
                'contract_type' => ['column' => 'nm_usage_rights', 'compare' => '=', 'value' => $data->contract_type],
                'active' => ['column' => 'active', 'compare' => '=', 'value' => 1],
                'parent' => ['column' => 'parent_id', 'compare' => '=', 'value' => 0],
            ],
        ];

        $contract_data = $UserDbContractsController->getContractsData($params);
        $fp = fopen($path . '/' . $name . '.csv', 'w');

        for ($i = 0; $i < count($contract_data); $i++) {
            fputcsv($fp, $contract_data[$i]);
        }

        fclose($fp);

        $archive = new ZipArchive();
        if ($archive->open(SITE_PATH . 'uploads/' . $name . '.zip', ZipArchive::CREATE)) {
            $archive->addFile($path . '/' . $name . '.csv', $name . '.csv');
        }
        $archive->close();

        $FarmingController->File->removeFolder($path);

        if (is_file(SITE_PATH . 'uploads/contracts_' . $this->User->UserID . '.zip')) {
            $this->Response->writeFile(SITE_PATH . 'uploads/contracts_' . $this->User->UserID . '.zip');
            unlink(SITE_PATH . 'uploads/contracts_' . $this->User->UserID . '.zip');
        }
    }

    public function razhodenOrder($dataOrder)
    {
        $FarmingController = new FarmingController('Farming');

        $this->getResponse()->setContentType('application/force-download');

        $userData = $FarmingController->getSystemFarmingDataByUserId($this->User->GroupID);
        $amount = number_format($results[$i]['amount'], 2, '.');
        sscanf($results[$i]['amount'], '%d.%d', $whole, $fraction);
        $data['company'] = $userData[0]['company'];
        $data['bulstat'] = $userData[0]['bulstat'];
        $data['date'] = $dataOrder[0]->date;
        $data['name'] = $dataOrder[0]->rep_name . ' ' . $dataOrder[0]->rep_surname . ' ' . $dataOrder[0]->rep_lastname;
        $data['address'] = $dataOrder[0]->address;
        $data['price'] = $dataOrder[0]->amount . ' лева';
        $data['price_text'] = $FarmingController->StringHelper->numToString(abs($dataOrder[0]->amount)) . ' лева и ' . ((int)$fraction) . ' ст.';
        $ltext = $FarmingController->StringHelper->loadTemplate($GLOBALS['Templates'][4]['template'], $data);

        $ltext = '<page style="font-family: freeserif"><br />' . $ltext . '</page>';

        $this->Page->FarmingController->HtmlToPdf->writeHTML($ltext);
        $this->Page->FarmingController->HtmlToPdf->Output();
    }
}
