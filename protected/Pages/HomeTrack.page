<com:TContent ID="Content">
    <script type="text/javascript">
        var userid = '<%=$this->User->UserID%>';
        var wialonServer = '<%=WIALON_SERVER%>';
        var trackToken = '<%=$_SESSION["track_token"]%>';
    </script>
    <script type="text/javascript" src="https://login.farmtrack.bg/wsdk/script/wialon.js"></script>

    <com:TRepeater ID="Repeater" EnableViewState="false">
        <prop:ItemTemplate>
            <script type="text/javascript" src="<%#$this->Data%>"></script>
        </prop:ItemTemplate>
    </com:TRepeater>
    
    <div class="easyui-layout" data-options="fit:true">
		<com:TConditional Visible="<%= LEGACY_MODE %>">
			<prop:TrueTemplate>
				<div data-options="region:'north'" style="height:40px; border-top: none;">
					<com:Application.Portlets.NavigationMenu ID="Navigation" />            
				</div>
			</prop:TrueTemplate>
        </com:TConditional>

        <div data-options="region:'center'" style="overflow: hidden">
            <iframe src="<%=WIALON_SERVER%>?token=<%=$_SESSION['track_token']%>&lang=bg" frameborder="0" width="100%" height="100%" style="visibility:hidden" id="iframe-wialon"></iframe>
        </div>

        <div id="home-track-get-wialon-token" class="easyui-window" title="Упълномощаване" style="width: 250px; padding: 10px;"
             data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-password', maximizable: false, minimizable: false, modal: true">

            <div class="messager-icon messager-info"></div>
            <p>Нужен ни е достъп до Вашия акаунт за проследяване.</p>

            <table width="100%" style="margin-top: 10px;" cellspacing="0" cellpadding="0">
                <tr>
                    <td style="text-align:center;">
                        <a href="javascript:void(0)" onClick="getWialonToken()" class="easyui-linkbutton">Вход</a>
                    </td>
                </tr>
            </table>
        </div>
    </div>
</com:TContent>
