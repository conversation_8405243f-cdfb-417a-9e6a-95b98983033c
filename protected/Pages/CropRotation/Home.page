<com:TContent ID="Content">

    <script type="text/javascript">          
        var userid = '<%=$this->User->UserID%>';
        var sessionid = '<%=session_id()%>';
        var groupID = '<%=$this->User->GroupID%>';
        var hasSubsidyRightsRW = '<%=$this->User->HasSubsidyRightsRW%>';
    </script>

    <com:TRepeater ID="Repeater" EnableViewState="false">
        <prop:ItemTemplate>
            <script type="text/javascript" src="<%#$this->Data%>"></script>
        </prop:ItemTemplate>
    </com:TRepeater>

    <div class="easyui-layout" data-options="fit:true">
		<com:TConditional Visible="<%= LEGACY_MODE %>">
			<prop:TrueTemplate>
				<div data-options="region:'north'" style="height:40px; border-top: none;">
					<com:Application.Portlets.NavigationMenu ID="Navigation" />            
				</div>
			</prop:TrueTemplate>
        </com:TConditional>
        <div data-options="region:'center', border: false">
        
            <div class="easyui-layout" data-options="fit:true">
           		
                <div data-options="region:'north', iconCls: 'icon-flask', split: true, collapsible: true, animate: true" style="height: 300px; padding:5px;">
               		<div class="easyui-layout" data-options="fit:true">
	                	<div data-options="region:'west', width: 240, border: false" style="padding-right: 5px;">
	                		
	                		<div class="easyui-panel" data-options="title: 'Файлове за почвени проби', iconCls: 'icon-files', fit: true" style="">
	                		
	                			<div style="border-bottom: 1px solid #ddd; background:#fafafa">
                					<span title="Добавяне на файл от лаборатория" class="easyui-tooltip" data-options="position: 'right'">
	                					<a href="#" id="upload-sample-file" class="easyui-linkbutton" data-options="iconCls:'icon-add', plain: true"></a>
	                				</span>
	                				<span title="Премахване на файл от лаборатория" class="easyui-tooltip" data-options="position: 'right'">
	                					<a href="#" id="delete-sample-file" class="easyui-linkbutton" data-options="iconCls:'icon-delete', plain: true"></a>
	                				</span>
	                				<span title="Прилагане на данни към рефентен слой" class="easyui-tooltip" data-options="position: 'right'">
	                					<a href="javaScript:void(0)" id="apply-sample-file" class="easyui-linkbutton" data-options="iconCls:'icon-layers', plain: true"></a>
	                				</span>
                				</div>
                				
                				<div id="sample-files-tree"></div>
                				
                			</div>
	                	</div>
	                	
	                	<div data-options="region:'center', border: false" style="height:200px;">
	                	
	                		<!-- <table id="layer-tables"></table> -->
	                		<table id="soil-sample-contents-tables"></table>
	                		
	                	</div>
	                </div>
                </div>
                
                <div data-options="region:'center', iconCls: 'icon-layers'" style=" padding:5px;">
                	<div class="easyui-layout" data-options="fit:true">
		                	
                		<div data-options="region:'west', border: false" style="width: 240px;padding-right: 5px;">
                		
                			<div class="easyui-panel" data-options="title: 'Референтни слоеве', iconCls: 'icon-layers', fit: true" style="">
                			
                				<div style="border-bottom: 1px solid #ddd; background:#fafafa">
                					<span title="Добавяне на референтен слой" class="easyui-tooltip" data-options="position: 'right'">
	                					<a href="#" id="add-ref-layer" class="easyui-linkbutton" data-options="iconCls:'icon-add', plain: true"></a>
	                				</span>
	                				<span title="Премахване на референтен слой" class="easyui-tooltip" data-options="position: 'right'">
	                					<a href="#" id="delete-ref-layer" class="easyui-linkbutton" data-options="iconCls:'icon-delete', plain: true"></a>
	                				</span>
	                				<span title="Зареждане на данни от файл" class="easyui-tooltip" data-options="position: 'right'">
	                					<a href="#" id="upload-ref-layer-data" class="easyui-linkbutton" data-options="iconCls:'icon-files', plain: true"></a>
	                				</span>
                                    <span title="Копиране на референтен слой" class="easyui-tooltip" data-options="position: 'right'">
                                        <a href="#" id="copy-ref-layer" class="easyui-linkbutton" data-options="iconCls:'icon-copy', plain: true"></a>
                                    </span>
                                     <span title="Преименуване на референтен слой" class="easyui-tooltip" data-options="position: 'right'">
                                        <a href="#" id="named-ref-layer" class="easyui-linkbutton" data-options="iconCls:'icon-edit', plain: true"></a>
                                    </span>
                				</div>
                				
                				<div id="crop-layers-tree"></div>
                				
                			</div>
                		</div>
                		
                		<div data-options="region:'center', border: false">
                			<table id="crop-tables"></table>
                		</div>
                		
                	</div>
                </div>
            </div>
        </div> 
    </div>
    
    <div id="progress-win" style="width:400px;padding:5px;">
        <div id="progress" style="width:373px;height:40px;"></div>
    </div>
    
    <div id="add-window" class="easyui-window" title="Създаване на референтен слой" data-options="iconCls:'icon-users', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" style="width:345px;height: 180px;">
        <com:Application.Portlets.CropRotation.ChooseLayer />
    </div>

    <div id="multiedit-window" class="easyui-window" title="Мултиредакция" data-options="iconCls:'icon-multi-edit', closed: true, resizable: false,modal: true, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" style="width:650px;height:300px;">
        <com:Application.Portlets.CropRotation.MultiEdit />
    </div>
    
    <div id="win-add-file" class="easyui-window" title="Добавяне на файл" data-options="iconCls:'icon-add', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true" style="width:450px;height:310px;">
        <com:Application.Portlets.CropRotation.LayersAdd />
    </div>
    
    <div id="win-add-crop-data" class="easyui-window" title="Добавяне на данни към референтен слой" data-options="iconCls:'icon-add', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true" style="width:330px;height:420px;">
        <com:Application.Portlets.CropRotation.DataAdd />
    </div>
    
    <div id="win-overlap-report" class="easyui-window" title="Проверка за припокриване" data-options="iconCls:'icon-intersection', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true" style="width:800px;height:600px;">
        <table id="tables-overlap-reports"></table>
    </div>
    
    <div id="win-culture-report" class="easyui-window" title="Проверка на въведените култури" data-options="iconCls:'icon-intersection', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true" style="width:800px;height:600px;">
        <table id="tables-culture-reports"></table>
    </div>
    
    <div id="win-soil-samples" class="easyui-window" title="Aнализ на почвите" data-options="iconCls:'icon-planting', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true" style="width:1000px;height:600px;">
        <table id="tables-soil-samples"></table>
    </div>
    
    <div id="win-report-grid-panel" class="easyui-window" title="Справка" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-copy', maximizable: false, minimizable: false"
    		style="width:650px; height: 450px;" >
    	<div id="report-tables"></div>
    </div>
    
    <div id="win-add-soil-sample" class="easyui-window" title="Добавяне на почвена проба" data-options="iconCls:'icon-add', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true" 
    		style="width:415px;height:450px;">
        <com:Application.Portlets.CropRotation.AddEditSoilSample />
    </div>
    <div id="win-soil-sample-filter" class="easyui-window" title="Филтър" data-options="iconCls:'icon-filter', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true" 
    		style="width:290px; height:210px;">
        <com:Application.Portlets.CropRotation.SoilSampleFilter />
    </div>
    
    <div id="win-soil-sample-uploads" class="easyui-window" title="Управление на данни за почвени проби от лаборатория" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-flask', maximizable: false, minimizable: false"
    		style="width:1150px; height: 500px; padding: 5px;" >
    	<div class="easyui-layout" data-options="fit:true, border: false" style="">
                <div data-options="region:'west'" style="width: 400px; border: none; padding-right: 5px;">
                    <table id="soil-sample-uploads-tables"></table>
                </div>
                <div data-options="region:'center'" style="border: none;">
                    <table id="soil-sample-contents-tables"></table>
                </div>
            </div>
    </div>
    <div id="win-soil-sample-uploader" class="easyui-window" title="Качване на файл" 
    		data-options="iconCls:'icon-add', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true" 
    			style="width:490px; height:325px; padding: 0; margin: 0;">
        <div id="ss-uploader">
		    <p>You browser doesn't have Flash, Silverlight, Gears, BrowserPlus or HTML5 support.</p>	    
		</div>
    </div>
    
    <div id="win-edit-ss-file-data" class="easyui-window" title="Редакция на данни от файл" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-edit', maximizable: false, minimizable: false"
    		style="width:375px; height: 120px;" >
    	<com:Application.Portlets.CropRotation.EditSoilSampleFileData />
    </div>
    
    <div id="win-soil-norms" class="easyui-window" title="План за балансирано торене" data-options="iconCls:'icon-plan', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true" style="width:800px;height:600px;">
        <table id="soil-norms-tables"></table>
    </div>
    
    <div id="win-choose-norm-type" class="easyui-window" title="Изготвяне на план за балансирано торене" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-plan', maximizable: false, minimizable: false"
    		style="width:760px; padding-bottom: 10px;" >
    	<com:Application.Portlets.CropRotation.ChooseNormType />
    </div>
    
    <div id="win-soil-norm-filter" class="easyui-window" title="Филтър" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-filter', maximizable: false, minimizable: false"
    		style="width:375px; height: 145px;" >
    	<com:Application.Portlets.CropRotation.SoilNormFilter />
    </div>
    
    <div id="win-avg-soil-norms" class="easyui-window" title="Усреднени почвени норми" data-options="iconCls:'icon-plan', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true" style="width:600px;height:600px;">
        <table id="soil-avg-norms-tables"></table>
    </div>
    
    <div id="win-soil-sample-export" class="easyui-window" title="Изготвяне на анализ на почвите" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-planting', maximizable: false, minimizable: false"
    		style="width:325px; height: 155px;" >
    	<com:Application.Portlets.CropRotation.SoilSampleExport />
    </div>
    
    <div id="win-apply-ss-file" class="easyui-window" title="Избор на референтен слой" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-layers', maximizable: false, minimizable: false"
    		style="width:325px; height: 185px;" >
    	<com:Application.Portlets.CropRotation.ApplySoilSampleFile />
    </div>

    
    <div id="win-download" class="easyui-dialog" title="Изтегляне на файл" style="width:260px;height:100px;" data-options="iconCls:'icon-files',resizable:true,modal:true,closed:true"> 
        <div style="text-align: center;padding:10px;">
            <a style="margin-top: 10px;" id="btn-download-file" href="" class="easyui-linkbutton" data-options="iconCls:'icon-files'" target="_blank" onClick="jQuery('#win-download').window('close');">Свали</a>
            <a style="margin-top: 10px;" id="btn-download-file-close" href="javaScript:void(0)" class="easyui-linkbutton" onClick="jQuery('#win-download').window('close');" data-options="iconCls:'icon-cancel'">Откажи</a> 
        </div>  
    </div> 

</com:TContent>