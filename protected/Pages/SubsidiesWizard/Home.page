<com:TContent ID="Content">
    <script src="https://maps.google.com/maps/api/js?v=<%=GOOGLE_VERSION%>&amp;key=<%=GOOGLE_KEY%>"></script>

    <script type="text/javascript">
        var isSuperAdmin = '<%=$this->User->IsSuperAdmin%>';
        var userid = '<%=$this->User->UserID%>';
        var sessionid = '<%=session_id()%>';
        var wmsServer = '<%=WMS_SERVER%>';
        var mapPath = '<%=WMS_MAP_PATH%>';
        var login3WmsServer = '<%=LOGIN3_WMS_SERVER%>';
        var login3MapPath = '<%=LOGIN3_WMS_MAP_PATH%>';
        var groupID = '<%=$this->User->GroupID%>';
    </script>

    <com:TRepeater ID="Repeater" EnableViewState="false">
        <prop:ItemTemplate>
            <script type="text/javascript" src="<%#$this->Data%>"></script>
        </prop:ItemTemplate>
    </com:TRepeater>

    <div class="easyui-layout" data-options="fit:true">
        <com:TConditional Visible="<%= LEGACY_MODE %>">
			<prop:TrueTemplate>
				<div data-options="region:'north'" style="height:40px; border-top: none;">
					<com:Application.Portlets.NavigationMenu ID="Navigation" />            
				</div>
			</prop:TrueTemplate>
        </com:TConditional>

        <div data-options="region:'center'" style="padding:5px;">

            <div class="easyui-layout" data-options="fit:true" style="" >

                <div data-options="region: 'north', height: 37" style="border-bottom: none; background-color: #fafafa;padding-left:6px;padding-top:5px;padding-bottom:3px;overflow-y: inherit;" >
                    <div style="float: left;">
					   Слой "За ИСАК": <div id="layers-tree"></div>
                    </div>
                    <span class="easyui-tooltip" data-options="position: 'bottom', content: 'Атрибутна информация'"  style="float: left; padding-left: 10px;">
                        <a id="tool-attr-info" class="easyui-linkbutton" data-options="iconCls: 'icon-datagrid', plain: true"></a>
                    </span>
                    <span class="easyui-tooltip" data-options="position: 'bottom', content: 'Мащабиране'"  style="float: left">
						<a id="tool-zoom-layer" class="easyui-linkbutton" data-options="iconCls: 'icon-zoom-full', plain: true"></a>
					</span>
                    <span class="easyui-tooltip" data-options="position: 'bottom', content: 'Пресичане с имоти'"  style="float: left;">
                        <a id="for-isak-kvs-grid" class="easyui-linkbutton" data-options="iconCls: 'icon-intersection', plain: true"></a>
                    </span>
                    <span class="easyui-tooltip" data-options="position: 'bottom', content: 'Избор на обект от карта'"  style="float: left;">
						<a id="tool-choose-layer-object" class="easyui-linkbutton" data-options="iconCls: 'icon-select-info', plain: true"></a>
					</span>
                    <!-- <span class="easyui-tooltip" data-options="position: 'bottom', content: 'Правно основание'"  style="float: left;">
                        <a id="reports-info" class="easyui-linkbutton" data-options="iconCls: 'icon-reports', plain: true"></a>
                    </span> -->
                    <div style="float: left; width: 1px; height: 25px; border-left: 1px solid #ccc; border-right: 1px solid #fff; margin: 1px 2px;"></div>
                    <div style="float: left;">
                        <a id="active-layer-menubutton" href="" class="easyui-menubutton" data-options="menu: '#layers-custom-menu', iconCls: 'icon-edit-geometry', plain: true, duration: 10" style="">Помощни слоеве</a>
                        <div id="layers-custom-menu" class="easyui-menu" style="background-color: #fafafa;width:345px;height:250px;overflow:auto">
                            <div id="all-layers-tree" style="text-align: left;"></div>
                        </div>
                    </div>
                    <div style="float: left; width: 1px; height: 25px; border-left: 1px solid #ccc; border-right: 1px solid #fff; margin: 1px 2px;"></div>
                    <div style="float: left; padding-left: 10px;">
                       Схема/Мярка: <input type="text" id="select-schema" style="width: 120px;"/>
                    </div>
                <!--    <span class="easyui-tooltip" data-options="position: 'bottom', content: 'Провери'"  style="float: left; padding-left: 5px;">
                        <a id="check-schema" class="easyui-linkbutton" data-options="iconCls: 'icon-reports', plain: true"></a>
                    </span> -->
                    <a id="check-schema" class="easyui-linkbutton" data-options="" style="float: left; margin-left: 5px;">Провери</a>
					<div style="float: right;">
						<span id="map-types-combobox" style="padding-top: 3px; padding-right: 3px; float: left;">
							<input type="text" style="width: 200px;" />
						</span>
					</div>
				</div>
                <div data-options="region: 'west', width: 30, border: true" style="background-color: #fafafa;">
                	<span class="easyui-tooltip" data-options="position: 'right', content: 'Преместване'"  style="float: left">
						<a href="javaScript:void(0)" id="tool-panzoom" class="easyui-linkbutton" data-options="iconCls: 'icon-nav-pan', plain: true"></a>
					</span>
					<span class="easyui-tooltip" data-options="position: 'right', content: 'Увеличение'"  style="float: left">
						<a href="javaScript:void(0)" id="tool-zoomin" class="easyui-linkbutton" data-options="iconCls: 'icon-zoom-in', plain: true"></a>
					</span>
					<span class="easyui-tooltip" data-options="position: 'right', content: 'Намаление'"  style="float: left">
						<a href="javaScript:void(0)" id="tool-zoomout" class="easyui-linkbutton" data-options="iconCls: 'icon-zoom-out', plain: true"></a>
					</span>
                	<span class="easyui-tooltip" data-options="position: 'right', content: 'Измерване на площ'"  style="float: left">
						<a href="javaScript:void(0)" id="tool-measure-polygon" class="easyui-linkbutton" data-options="iconCls: 'icon-measure-polygon', plain: true"></a>
					</span>
					<span class="easyui-tooltip" data-options="position: 'right', content: 'Измерване на разстояние'"  style="float: left">
						<a href="javaScript:void(0)" id="tool-measure-line" class="easyui-linkbutton" data-options="iconCls: 'icon-measure-line', plain: true"></a>
					</span>
					<span class="easyui-tooltip" data-options="position: 'right', content: 'Изчистване на селекция'"  style="float: left">
						<a href="javaScript:void(0)" id="tool-clear-selection" class="easyui-linkbutton" data-options="iconCls: 'icon-clear', plain: true"></a>
					</span>

					<div style="float: left; height: 1px; width: 25px; border-top: 1px solid #ccc; border-bottom: 1px solid #fff; margin: 1px 2px;"></div>
                </div>
                <div data-options="region:'center', collapsible:true, onMaximize:function (){map.render('map')}">
                    <div id="map" style="width:100%; height:100%;"></div>
                </div>

               	<div data-options="region:'south'" style="border-top: none; padding:0px;height:30px;">
                	<div style="float:left; padding: 5px">EPSG: 900913</div>

               		<div id="coords" style="float:right;width:250px;text-align: right;border-left: 1px solid #ccc; padding: 5px">x: ,y: </div>
					<div id="scale" style="float: right; ">
						Мащаб 1:
						<span id="scale-denominator">
							<input type="text" style="width: 75px; margin: 4px 0px 3px 3px" />
						</span>
						<span class="easyui-tooltip" data-options="position: 'top', content: 'Мащабиране'">
							<a style="margin: 0px 3px 0px 0px" href="javaScript:void(0)" id="tool-set-scale" class="easyui-linkbutton" data-options="iconCls: 'icon-zoom-full', plain: true"></a>
						</span>
					</div>
           		</div>

            </div>

        </div>
    </div>

    <div id="win-layer-attr-info" class="easyui-window" title="Атрибутна информация" style="height: 615px; width: 1060px"
         data-options="iconCls:'icon-datagrid', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: false">
        <com:Application.Portlets.SubsidiesWizard.AttrInfo />
    </div>

    <div id="property-win" class="easyui-window" title="Информация" style="height: 320px; width: 320px"
         data-options="iconCls:'icon-info', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true">
        <table id="property-grid"></table>
        <div style="margin: 7px 10px 0px 85px;">
            <a onclick="savePropertyGrid()" style="" class="easyui-linkbutton" data-options="iconCls: 'icon-save'">Запази</a>
            <a onclick="jQuery('#property-win').window('close');" style="" class="easyui-linkbutton" data-options="iconCls: 'icon-cancel'">Затвори</a>
        </div>
    </div>

    <div id="progress-win" style="width:400px;padding:5px;">
        <div id="progress" style="width:373px;height:40px;"></div>
    </div>

    <div id="win-download" class="easyui-dialog" title="Изтегляне на файл" style="width:260px;height:100px;"
		 data-options="iconCls:'icon-files',resizable:true,modal:true,closed:true">
        <div style="text-align: center;padding:10px;">
            <a style="margin-top: 10px;" id="btn-download-file" href="javaScript:void(0)" target="_blank" class="easyui-linkbutton" onClick="jQuery('#win-download').window('close');" data-options="iconCls:'icon-files'">Изтегли</a>
            <a style="margin-top: 10px;" href="javaScript:void(0)" class="easyui-linkbutton" onClick="jQuery('#win-download').window('close');" data-options="iconCls:'icon-cancel'">Откажи</a>
        </div>
    </div>

    <div id="win-zplots-copy" class="easyui-window" title="Избор на слой" style="height: 185px; width: 290px; top: 0; left: 0"
		 data-options="iconCls:'icon-save', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: false">
        <com:Application.Portlets.SubsidiesWizard.ZPlotsCopy />
    </div>

    <div id="win-edit-for-isak" class="easyui-window" title="Редакция" style="width:350px;" data-options="iconCls:'icon-edit', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
       <com:Application.Portlets.SubsidiesWizard.EditForIsak />
    </div>

  	<div id="win-for-isak-multi-edit" class="easyui-window" title="Мултиредакция" style="width:350px; height:165px; padding-top: 10px;" data-options="iconCls:'icon-edit', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
        <com:Application.Portlets.SubsidiesWizard.MultiEdit />
    </div>

    <div id="for-isak-diff-allowable-final-tables-toolbar" style="padding: 5px;">
        <a id="btnsave_for_isak_diff" onclick="requestSEPP('true');" class="easyui-linkbutton js-attr-tables-edit" data-options="iconCls: 'icon-add'">Заяви</a>
        &nbsp;<span class="datagrid-btn-separator" style="float: none"></span>&nbsp;
        <a id="btnsave_for_isak_diff" onclick="requestSEPP('false');" class="easyui-linkbutton js-attr-tables-edit" data-options="iconCls: 'icon-remove'">Премахни от схема СЕПП</a>
        &nbsp;<span class="datagrid-btn-separator" style="float: none"></span>&nbsp;
        <a id="btnsave_for_isak_diff" onclick="saveForEditAndCommentSEPP();" class="easyui-linkbutton js-attr-tables-edit" data-options="iconCls: 'icon-save'">Запази</a>
        &nbsp;<span class="datagrid-btn-separator" style="float: none"></span>&nbsp;
        <a id="btn-export-for-isak-diff-xls" onclick="exportReportToDocument(SCHEMA_SEPP, 'xls');" class="easyui-linkbutton js-attr-tables-edit" data-options="iconCls: 'icon-csv'">Export(XLS)</a>
        <a id="btn-export-for-isak-diff-pdf" onclick="exportReportToDocument(SCHEMA_SEPP, 'pdf');" class="easyui-linkbutton js-attr-tables-edit" data-options="iconCls: 'icon-pdf'">Export(PDF)</a>
    </div>

    <div id="for-isak-pndp-tables-toolbar" style="padding: 5px;">
        <a id="btnrequest_for_isak_pndp" onclick="requestPNDP('true');" class="easyui-linkbutton js-attr-tables-edit" data-options="iconCls: 'icon-add'">Заяви</a>
        &nbsp;<span class="datagrid-btn-separator" style="float: none"></span>&nbsp;
        <a id="btnremove_for_isak_pndp" onclick="requestPNDP('false');" class="easyui-linkbutton js-attr-tables-edit" data-options="iconCls: 'icon-remove'">Премахни от схема ПНДП</a>
        &nbsp;<span class="datagrid-btn-separator" style="float: none"></span>&nbsp;
        <a id="btnsave_for_isak_pndp" onclick="saveForIsakPNDPData();" class="easyui-linkbutton js-attr-tables-edit" data-options="iconCls: 'icon-save'">Запази</a>
        &nbsp;<span class="datagrid-btn-separator" style="float: none"></span>&nbsp;
        <a id="btn-export-pndp-xls" onclick="exportReportToDocument(SCHEMA_PNDP, 'xls');" class="easyui-linkbutton js-attr-tables-edit" data-options="iconCls: 'icon-csv'">Export(XLS)</a>
        <a id="btn-export-pndp-pdf" onclick="exportReportToDocument(SCHEMA_PNDP, 'pdf');" class="easyui-linkbutton js-attr-tables-edit" data-options="iconCls: 'icon-pdf'">Export(PDF)</a>
    </div>

    <div id="win-for-isak-diff-allowable-final" class="easyui-window" title="Справка по СЕПП" style="height: 535px; width: 930px;"
        data-options="iconCls:'icon-intersection', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: false, closable:true, onClose:function(){removeLayerByName('for_isak_diff_allowable_final')}" >
        <com:Application.Portlets.SubsidiesWizard.ReportSEPP />
    </div>

    <div id="win-for-isak-diff-lfa" class="easyui-window" title="Справка по НР" style="height: 535px; width: 1000px;"
        data-options="iconCls:'icon-intersection', closed:true, resizable:false,  minimizable:false, collapsible:false, maximizable:false, modal:false, closable:true, onClose:function(){removeLayerByName('for_isak_diff_lfa')}" >
        <com:Application.Portlets.SubsidiesWizard.ReportNR />
    </div>

    <div id="win-for-isak-diff-natura" class="easyui-window" title="Справка по НАТУРА 2000" style="height: 535px; width: 1100px;"
        data-options="iconCls:'icon-intersection', closed:true, resizable:false,  minimizable:false, collapsible:false, maximizable:false, modal:false, closable:true, onClose:function(){removeLayerByName('for_isak_diff_natura')}" >
        <com:Application.Portlets.SubsidiesWizard.ReportNatura />
    </div>

    <div id="win-for-isak-pndp" class="easyui-window" title="Справка по ПНДП" style="height: 585px; width: 1000px;" data-options="iconCls:'icon-intersection', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: false, closable:true" >
        <com:Application.Portlets.SubsidiesWizard.ReportPNDP />
    </div>

    <div id="for-isak-vps-main-tables-toolbar" style="padding: 5px;">
		<span id="vps-types-combobox" style="padding-top: 3px; padding-right: 3px; float: left;">
			<input type="text" style="width: 200px;" />
		</span>
        <a id="btn-for-isak-vps-diff-detailed-check" onclick="vpsDetailedWindow();" class="easyui-linkbutton js-attr-tables-edit" data-options="iconCls: 'icon-info'">Детайлна проверка на ВПС</a>
        &nbsp;<span class="datagrid-btn-separator" style="float: none"></span>&nbsp;
        	<a id="btnsave_for_isak_main_vps" onclick="saveForIsakMainVPSData();" class="easyui-linkbutton js-attr-tables-edit" data-options="iconCls: 'icon-save'">Запази</a>
            <a id="btn-export-vps-main-grid-xls" onclick="exportReportToDocument(SCHEMA_VPS, 'xls');" class="easyui-linkbutton js-attr-tables-edit" data-options="iconCls: 'icon-csv'">Export(XLS)</a>
            <a id="btn-export-vps-main-grid-pdf" onclick="exportReportToDocument(SCHEMA_VPS, 'pdf');" class="easyui-linkbutton js-attr-tables-edit" data-options="iconCls: 'icon-pdf'">Export(PDF)</a>
    </div>

	<div id="win-for-isak-vps-main" class="easyui-window" title="Справка по АЕП - ВПС" style="height: 535px; width: 1100px;"
        data-options="iconCls:'icon-intersection', closed:true, resizable:false,  minimizable:true, collapsible:false, maximizable:false, modal:false, closable:true" >
        <com:Application.Portlets.SubsidiesWizard.ReportVPS />
    </div>

    <div id="for-isak-vps-detailed-toolbar" style="padding: 5px;">
        <div style="float:left;">
            <a id="btnsave_for_isak_diff" onclick="requestVPS('true');" class="easyui-linkbutton js-attr-tables-edit" data-options="iconCls: 'icon-add'">Заяви</a>
            &nbsp;<span class="datagrid-btn-separator" style="float: none"></span>&nbsp;
            <a id="btnsave_for_isak_diff" onclick="requestVPS('false');" class="easyui-linkbutton js-attr-tables-edit" data-options="iconCls: 'icon-remove'">Премахни от схема ВПС</a>
            &nbsp;<span class="datagrid-btn-separator" style="float: none"></span>&nbsp;
        	<a id="btnsave_for_isak_vps" onclick="saveForIsakVPSData();" class="easyui-linkbutton js-attr-tables-edit" data-options="iconCls: 'icon-save'">Запази</a>
            <a id="btn-export-for-isak-detailed-vps-xls" onclick="exportReportToDocument(SCHEMA_VPS_DETAILED, 'xls');" class="easyui-linkbutton js-attr-tables-edit" data-options="iconCls: 'icon-csv'">Export(XLS)</a>
            <a id="btn-export-for-isak-detailed-vps-pdf" onclick="exportReportToDocument(SCHEMA_VPS_DETAILED, 'pdf');" class="easyui-linkbutton js-attr-tables-edit" data-options="iconCls: 'icon-pdf'">Export(PDF)</a>
        </div>
        <div style="clear:both;"></div>
    </div>

    <div id="win-for-isak-detailed-vps-info" class="easyui-window" title="Справка по АЕП - ВПС - подробна" style="height: 535px; width: 1100px;"
        data-options="iconCls:'icon-intersection', closed:true, resizable:false,  minimizable:true, collapsible:false, maximizable:false, modal:false, closable:true, onClose:function(){removeVPSMapLayer()}" >
        <com:Application.Portlets.SubsidiesWizard.ReportDetailedVPS />
    </div>

    <div id="for-isak-report-zdp-toolbar" style="padding: 5px;">
        <div style="float:left;">
            <a id="btnsave_for_isak_diff" onclick="requestZDP('true');" class="easyui-linkbutton js-attr-tables-edit" data-options="iconCls: 'icon-add'">Добави</a>
            &nbsp;<span class="datagrid-btn-separator" style="float: none"></span>&nbsp;
            <a id="btnsave_for_isak_diff" onclick="requestZDP('false');" class="easyui-linkbutton js-attr-tables-edit" data-options="iconCls: 'icon-remove'">Премахни</a>
            &nbsp;<span class="datagrid-btn-separator" style="float: none"></span>&nbsp;
            <a id="btnsave_for_isak_diff" onclick="saveForEditAndCommentZDP();" class="easyui-linkbutton js-attr-tables-edit" data-options="iconCls: 'icon-save'">Запази</a>
            &nbsp;<span class="datagrid-btn-separator" style="float: none"></span>&nbsp;
            <a id="btn-export-for-isak-zdp-xls" onclick="exportReportToDocument(SCHEMA_ZDP, 'xls');" class="easyui-linkbutton js-attr-tables-edit" data-options="iconCls: 'icon-csv'">Export(XLS)</a>
            <a id="btn-export-for-isak-zdp-pdf" onclick="exportReportToDocument(SCHEMA_ZDP, 'pdf');" class="easyui-linkbutton js-attr-tables-edit" data-options="iconCls: 'icon-pdf'">Export(PDF)</a>
        </div>
        <div style="float:right;">
            <a id="for-isak-zdp-help" class="easyui-linkbutton" data-options="iconCls: 'icon-help'">Легенда</a>
        </div>
        <div style="clear:both;"></div>
    </div>

    <div id="for-isak-report-pzp-toolbar" style="padding: 5px;">
        <div style="float:right;">
            <a onclick="saveForEditAndCommentPZP();" class="easyui-linkbutton" data-options="iconCls: 'icon-save'">Запази</a>
        </div>
        <div style="clear:both;"></div>
    </div>

    <div id="for-isak-report-schema-pzp-toolbar" style="padding: 5px;">
        <div style="float:right;">
            <a id="btn-export-for-isak-pzp-xls" onclick="exportReportToDocument(SCHEMA_PZP, 'xls');" class="easyui-linkbutton js-attr-tables-edit" data-options="iconCls: 'icon-csv'">Export(XLS)</a>
            <a id="btn-export-for-isak-pzp-pdf" onclick="exportReportToDocument(SCHEMA_PZP, 'pdf');" class="easyui-linkbutton js-attr-tables-edit" data-options="iconCls: 'icon-pdf'">Export(PDF)</a>
            <a onclick="saveForEditAndCommentSchemaPZP();" class="easyui-linkbutton" data-options="iconCls: 'icon-save'">Запази</a>
        </div>
        <div style="clear:both;"></div>
    </div>

    <div id="win-for-isak-report-zdp" class="easyui-window" title="Справка - Зелени Плащания" style="height: 633px; width: 930px;" data-options="iconCls:'icon-intersection', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: false, closable:true, onClose:function(){removeLayerByName('for_isak_diff_pzp')}" >
        <com:Application.Portlets.SubsidiesWizard.ReportZDP />
    </div>

    <div id="win-for-isak-report-pzp" class="easyui-window" title="Справка - Парцели в ПЗП" style="height: 633px; width: 930px;" data-options="iconCls:'icon-intersection', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: false, closable:true, onClose:function(){removeLayerByName('for_isak_diff_pzp')}" >
        <com:Application.Portlets.SubsidiesWizard.ReportPZP />
    </div>

    <div id="win-kvs-plots" class="easyui-window" title="Пресичане с имоти" style="width:1124px; height:550px;" data-options="iconCls:'icon-edit-geometry', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: false, closable:true" >
       <table id="kvs-plots-tables"></table>
    </div>

    <div id="property-win-intermidiate-cultures" class="easyui-window" title="Смески с междинни култури" style="height: 640px; width: 320px"
         data-options="iconCls:'icon-info', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true">
         <div class="weat-crops-prop-grid" style="height:565px; overflow:auto">
            <table id="property-grid-intermidiate-cultures"></table>
         </div>
        <div style="margin: 7px 10px 0px 85px;">
            <a onclick="savePropertyGridCultures()" style="" class="easyui-linkbutton" data-options="iconCls: 'icon-save'">Запази</a>
            <a onclick="jQuery('#property-win-intermidiate-cultures').window('close');" style="" class="easyui-linkbutton" data-options="iconCls: 'icon-cancel'">Затвори</a>
        </div>
    </div>

    <div id="win-help-zdp-terraces" class="easyui-window" title="Пояснение за: Тераси" style="height: 440px; width: 800px; top: 0; left: 0"
         data-options="iconCls:'icon-help', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: false">
        <com:Application.Portlets.SubsidiesWizard.HelpZdpTexts.HelpZdpTerraces />
    </div>
    <div id="win-help-zdp-hedge" class="easyui-window" title="Пояснение за: Жив плет/обрасла с дървесна растителност ивица" style="height: 440px; width: 800px; top: 0; left: 0"
         data-options="iconCls:'icon-help', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: false">
        <com:Application.Portlets.SubsidiesWizard.HelpZdpTexts.HelpZdpHedge />
    </div>
    <div id="win-help-zdp-isolated_tree" class="easyui-window" title="Пояснение за: Изолирани дървета" style="height: 440px; width: 800px; top: 0; left: 0"
         data-options="iconCls:'icon-help', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: false">
        <com:Application.Portlets.SubsidiesWizard.HelpZdpTexts.HelpZdpIsolatedTree />
    </div>
    <div id="win-help-zdp-rows_tree" class="easyui-window" title="Пояснение за: Редици от дървета" style="height: 440px; width: 800px; top: 0; left: 0"
         data-options="iconCls:'icon-help', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: false">
        <com:Application.Portlets.SubsidiesWizard.HelpZdpTexts.HelpZdpRowsTree />
    </div>
    <div id="win-help-zdp-group_tree" class="easyui-window" title="Пояснение за: Групи от дървета" style="height: 440px; width: 800px; top: 0; left: 0"
         data-options="iconCls:'icon-help', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: false">
        <com:Application.Portlets.SubsidiesWizard.HelpZdpTexts.HelpZdpGroupTree />
    </div>
    <div id="win-help-zdp-field_boundaries" class="easyui-window" title="Пояснение за: Синори (полски граници)" style="height: 440px; width: 800px; top: 0; left: 0"
         data-options="iconCls:'icon-help', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: false">
        <com:Application.Portlets.SubsidiesWizard.HelpZdpTexts.HelpZdpFieldBoundaries />
    </div>
    <div id="win-help-zdp-lakes" class="easyui-window" title="Пояснение за: Езерца" style="height: 440px; width: 800px; top: 0; left: 0"
         data-options="iconCls:'icon-help', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: false">
        <com:Application.Portlets.SubsidiesWizard.HelpZdpTexts.HelpZdpLakes />
    </div>
    <div id="win-help-zdp-ditch" class="easyui-window" title="Пояснение за: Канавки и открити водни течения" style="height: 440px; width: 800px; top: 0; left: 0"
         data-options="iconCls:'icon-help', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: false">
        <com:Application.Portlets.SubsidiesWizard.HelpZdpTexts.HelpZdpDitch />
    </div>
    <div id="win-help-zdp-buffer_strips" class="easyui-window" title="Пояснение за: Буферни ивици" style="height: 440px; width: 800px; top: 0; left: 0"
         data-options="iconCls:'icon-help', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: false">
        <com:Application.Portlets.SubsidiesWizard.HelpZdpTexts.HelpZdpBufferStrips />
    </div>
    <div id="win-help-zdp-strips_area" class="easyui-window" title="Пояснение за: Ивици допустими площи на границата между обработваеми земи и гори без производство" style="height: 440px; width: 800px; top: 0; left: 0"
         data-options="iconCls:'icon-help', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: false">
        <com:Application.Portlets.SubsidiesWizard.HelpZdpTexts.HelpZdpStripsArea />
    </div>
</com:TContent>
