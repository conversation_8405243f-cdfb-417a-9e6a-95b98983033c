<?php

use TF\Application\Common\Config;
use TF\Application\Common\MTPage;
use TF\Engine\Kernel\StringHelper;

/**
 * Home class.
 *
 * Home page of Users plugin
 */
class Home extends MTPage
{
    public $Layers;
    public $LayersKvs;
    public $UserDbForIsakController;
    public $StringHelper;

    /**
     * onInit event.
     */
    public function onInit($param)
    {
        parent::onInit($param);

        $this->StringHelper = new StringHelper();
    }

    public function savePropertyGrid($sender, $params)
    {
        $data = $params->CallbackParameter;

        $options = [];
        $options['tablename'] = $data[0]->tablename;
        $options['where']['gid'] = $data[0]->gid;

        if (Config::LAYER_TYPE_FOR_ISAK == $data[0]->layer_type) {
            $options['mainData'] = $this->Page->UsersController->getMainDatOptionsForIsakPropertyGrid($data);
        }

        $this->Page->UserDbController->editItem($options);

        $this->Page->UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, 'SubsidiesWizard', 'Layer info edited', $options['tablename'], [$data[0]->gid]);
    }
}
