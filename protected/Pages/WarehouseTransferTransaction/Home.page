<com:TContent ID="Content">
    <script data-main="lib/js/warehouseTransferTransaction/index.js" src="lib/js_external/require.js"></script>
    <script type="text/javascript">
        define('global-config', [], function () {
            return {
                isSuperAdmin: '<%=$this->User->IsSuperAdmin%>',
                hasWarehouseRights: '<%=$this->User->hasWarehouseRights%>',
                userid: '<%=$this->User->UserID%>',
                groupid: '<%=$this->User->GroupID%>',
                sessionid: '<%=session_id()%>'
            }
        });
    </script>

    <com:TRepeater ID="Repeater" EnableViewState="false">
        <prop:ItemTemplate>
            <script type="text/javascript" src="<%#$this->Data%>"></script>
        </prop:ItemTemplate>
    </com:TRepeater>

    <div id="cc1" class="easyui-layout warehouse-style" data-options="fit:true,border:false">
       <com:TConditional Visible="<%= LEGACY_MODE %>">
			<prop:TrueTemplate>
				<div data-options="region:'north'" style="height:40px; border-top: none;">
					<com:Application.Portlets.NavigationMenu ID="Navigation" />            
				</div>
			</prop:TrueTemplate>
        </com:TConditional>

        <div data-options="region:'center',border:false">
            <div class="easyui-layout" data-options="fit:true,border:false">
                <div data-options="region:'north',border:false" style="height: 232px;">
                    <div class="easyui-panel" data-options="fit:true,border:false">
                        <div class="easyui-layout" data-options="fit:true,border:false">
                            <div data-options="region:'west',border:false" style="width: 33.33%">
                                <com:Application.Portlets.Warehouse.DocumentInfo  BlockTitle="Прехвърляне между собствени складове" />
                            </div>
                            <div data-options="region:'center',border:false" style="width: 33.33%; padding:0 5px 5px;">
                                <div class="easyui-panel tr-main-info-block" title="Склад за изписване" data-options="iconCls:'icon-users',fit:true">
                                    <a id="selectFarmOutBtn" class="easyui-linkbutton" data-options="iconCls: 'icon-admin'">Избери
                                        стопанство</a>
                                    <span class="selected-farm" id="js-selected-farm-out"></span>
                                    <fieldset class="farm-info">
                                        <legend>Избор на склад</legend>
                                        <div class="container-box">
                                            <div class="col-4">
                                                <label>Склад:</label>
                                                <input type="text" class="easyui-combobox" id="farm-warehouses-combobox-out"
                                                    data-options="disabled:'true'" style="width: 100%">
                                            </div>
                                            <input id="tr-company-id-out" type="hidden">
                                        </div>
                                    </fieldset>
                                </div>
                            </div>
                            <div data-options="region:'east',border:false" style="width: 33.33%; padding-bottom: 5px">
                                <div class="easyui-panel tr-main-info-block" title="Склад за преместване" data-options="iconCls:'icon-users',fit:true">
                                    <a id="selectFarmInBtn" class="easyui-linkbutton" data-options="iconCls: 'icon-admin'">Избери стопанство</a>
                                    <span class="selected-farm" id="js-selected-farm-in"></span>
                                    <fieldset class="farm-info">
                                        <legend>Избор на склад</legend>
                                        <div class="container-box">
                                            <div class="col-4">
                                                <label>Склад:</label>
                                                <input type="text" class="easyui-combobox" id="farm-warehouses-combobox-in"
                                                    data-options="disabled:'true'" style="width: 100%">
                                            </div>
                                            <input id="tr-company-id-in" type="hidden">
                                        </div>
                                    </fieldset>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div data-options="region:'center'">

                    <div class="easyui-panel" title="Артикули" data-options="iconCls:'icon-users',border:false,fit:true">
                        <table id="added-items-grid" data-options="fit:true"></table>
                    </div>


                </div>

                <div data-options="region:'south'" style="padding:3px;height: 36px;">
                    <a class="l-btn l-btn-small l-btn-plain" id="saveTransactionBtn">
                        <span class="l-btn-left l-btn-icon-left">
                            <span class="l-btn-text">Запази</span>
                            <span class="l-btn-icon icon-save"></span>
                        </span>
                    </a>

                    <a class="l-btn l-btn-small l-btn-plain" id="saveAndCloseTransactionBtn">
                        <span class="l-btn-left l-btn-icon-left">
                            <span class="l-btn-text">Запази и приключи</span>
                            <span class="l-btn-icon icon-save"></span>
                        </span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!--Add Transactions Modal-->
    <div id="win-transfer-transactions" class="easyui-window" title="Преместване на артикул"
         data-options="iconCls:'icon-users', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
        <com:Application.Portlets.Warehouse.TransferTransaction />
    </div>

    <com:Application.Portlets.Warehouse.RightSlidePanel />
    <com:Application.Portlets.Warehouse.WarehouseModals ID="WarehouseModals"/>

    <!-- hidden field that shows the the type of add transaction-->
    <input type="text" id="transaction_type" value="TRANSFER">

    <div id="win-choose-sub-doc-export-type" class="easyui-window" title="Избор на експорт" data-options="iconCls:'icon-data', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true" style="padding: 10px;">
        <com:Application.Portlets.Warehouse.ChooseSubDocExportType />
    </div>

</com:TContent>
