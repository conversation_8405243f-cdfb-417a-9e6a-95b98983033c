<div>
	<div style="margin: 9px 10px 5px 9px; width: 595px; height: 260px; float: left;">
		<table id="owners-add-tables" ></table>
	</div>

	<div style="width: 361px; height: 260px; float: left;">
		<fieldset style="border: 1px solid #000; padding: 10px;">
            <legend style="margin-left: 10px; font-weight: bold; font-style: italic;">Документ за собственост</legend>
			<div id="owner-document">
				<input type="textbox" style="width: 335px;" />
			</div>
		</fieldset>
		
		<fieldset style="border: 1px solid #000; padding: 10px;">
            <legend style="margin-left: 10px; font-weight: bold; font-style: italic;">Cобственост</legend>
			<div id="ownage-type">
				<span id="sublease-ownage-fraction">
					<com:TRadioButton ID="SubleaseOwnageFraction" Checked="true" GroupName="ownage-type" Style="" />
					<com:TActiveLabel Text="идеални части&nbsp;&nbsp;&nbsp;" ForControl="SubleaseOwnageFraction" />
				</span>
				<span id="sublease-ownage-percent">
					<com:TRadioButton ID="SubleaseOwnagePercent" GroupName="ownage-type" Style="" />
					<com:TActiveLabel Text="%&nbsp;&nbsp;&nbsp;" ForControl="SubleaseOwnagePercent" />
				</span>
				<span id="sublease-ownage-area">
					<com:TRadioButton ID="SubleaseOwnageArea" GroupName="ownage-type" Style="" />
					<com:TActiveLabel Text="площ(дка)&nbsp;&nbsp;&nbsp;" ForControl="SubleaseOwnageArea" />
				</span>

				<span id="sublease-ownage-fraction-fields">
					<span id="ownage-numerator">
						<input type="textbox" style="width: 40px;" />
					</span>
					<span style="font-size: 21px; font-weight: bold;">
						/
					</span>
					<span id="ownage-denominator" >
						<input type="textbox" style="width: 40px;" />
					</span>
				</span>
				<span id="sublease-ownage-percent-field" style="display: none;">
					<input type="textbox" style="width: 97px;" />
				</span>
				<span id="sublease-ownage-area-field" style="display: none;">
					<input type="textbox" style="width: 97px;" />
				</span>
			</div>
		</fieldset>

		<fieldset style="border: 1px solid #000; padding: 10px;">
            <legend style="margin-left: 10px; font-weight: bold; font-style: italic;">Представлява се</legend>
			<span id="sublease-owner-rep-by-another">
				<com:TRadioButton ID="SubleaseOwnerRepByRep" Checked="true" GroupName="owner-rep" Style="" />
				<com:TActiveLabel Text="от представител&nbsp;&nbsp;" ForControl="SubleaseOwnerRepByRep" />
			</span>
			<span id="sublease-owner-rep-by-himself">
				<com:TRadioButton ID="SubleaseOwnerRepByHimself" GroupName="owner-rep" Style="" />
				<com:TActiveLabel Text="лично" ForControl="SubleaseOwnerRepByHimself" />
			</span>
		</fieldset>
		
		<fieldset style="border: 1px solid #000; padding: 10px; height: 68px;">
            <legend style="margin-left: 10px; font-weight: bold; font-style: italic;">Данни за пълномощно</legend>

			<div id="sublease-rep-proxy-fields">
				<table>
					<tr>
						<td>Номер:</td>
						<td style="width: 193px;"></td>
						<td>Дата: </td>
					</tr>
					<tr>
						<td id="sublease-rep-proxy-num" style="padding-right: 10px;" colspan="2">
							<input type="textbox" style="width: 200px;" />
						</td>
						
						<td id="sublease-rep-proxy-date">
							<input type="textbox" style="width: 100px;" />
						</td>
					</tr>
				</table>
			</div>

			<div id="sublease-rep-proxy-message" style="display: none;">
				Когато собственикът се представлява лично, не се попълват данни за представител и пълномощно.
			</div>

		</fieldset>

	</div>
	<div style="clear: both"></div>
	<div style="margin: 5px 10px 7px 9px; height: 245px;">
		<table id="sublease-owners-reps-tables" ></table>
	</div>
</div>

<table width="100%" cellspacing="0" cellpadding="0">
    <tr>       
        <td colspan="2"  style="text-align:center;">
            <a id="btnsave" href="javaScript:void(0)" class="easyui-linkbutton" onClick="validateNewPCToOwnerRel();" data-options="iconCls:'icon-save'">Запази</a>
            <a id="btnclose" href="javaScript:void(0)" class="easyui-linkbutton" onClick="jQuery('#win-plot-owner-add').window('close');" data-options="iconCls:'icon-cancel'">Откажи</a> 
        </td>
    </tr>
</table>