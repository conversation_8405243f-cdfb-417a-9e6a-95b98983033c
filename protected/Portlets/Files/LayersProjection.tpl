<table width="100%" class="filters" cellspacing="0" cellpadding="0" style="padding:10px 10px 0 10px;">
    <tr>
        <td style="text-align:right">Източник:</td>
        <td id="layer-projection">
    		<com:TTextBox ID="LayerProjection" Style="width: 250px;"/>
    		<com:TRequiredFieldValidator ControlToValidate="LayerProjection" ValidationGroup="SubmitGroupLayerPR" Text="" Display="Dynamic" FocusOnError="true" />
    	</td>
    </tr>
    <tr>
        <td style="text-align:right">Устройство:</td>
        <td id="layer-device">
        	<com:TTextBox ID="LayerDevice" Style="width: 250px;" />	
        </td>
    </tr>
    <tr>
        <td style="text-align:right">Стопанство:</td>
        <td id="layer-farming">
        	<com:TTextBox ID="LayerFarming" Style="width: 250px;" />	
        </td>
    </tr>
    <tr>
        <td style="text-align:right">Година:</td>
        <td id="layer-year">
        	<com:TTextBox ID="LayerYear" Style="width: 250px;"/>	
        </td>
    </tr>
</table>

<table width="100%" cellspacing="0" cellpadding="0" style="margin-bottom: 10px; margin-top: 10px;">
    <tr>       
        <td colspan="2"  style="text-align:center">
            <span id="projection-button">
                <a id="NextButton" class="easyui-linkbutton">Продължи</a>
            </span>
            <a id="btn" href="javaScript:void(0)" class="easyui-linkbutton" onClick="jQuery('#win-srid').window('close');" data-options="iconCls:'icon-cancel'">Откажи</a> 
        </td>
    </tr>
</table>
