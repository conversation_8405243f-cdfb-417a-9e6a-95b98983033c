<div>
    <table class="filters" cellspacing="0" cellpadding="0" style="padding:10px 10px 0 10px; margin-left: 10px;">
        <tr>
            <td style="text-align:right;" id="definition-for-isak-number-check">
                <input type="checkbox" ID="ForIsakNumberCheck" onClick="enableDefinition(this,'for-isak-definition-number')" checked="true">
            </td>
            <td style="text-align:left">Име на парцел:</td>
            <td id="for-isak-definition-number">
                <input type="text "style="width: 250px;">
            </td>
        </tr>
        <tr>
            <td style="text-align:right" id="for-isak-definition-ekatte-check">
                <input type="checkbox" ID="ForIsakEkatteCheck" onClick="enableDefinition(this,'for-isak-definition-ekatte')" checked="true">
            </td>
            <td style="text-align:left">EKATTE:</td>
            <td id="for-isak-definition-ekatte">
                <input type="text "style="width: 250px;">
            </td>
        </tr>
        <tr>
            <td style="text-align:right" id="for-isak-definition-crop-name-check">
                <input type="checkbox" ID="ForIsakCropNameMasivCheck" onClick="enableDefinition(this,'for-isak-definition-crop-name')" checked="true">
            </td>
            <td style="text-align:left">Култура:</td>
            <td id="for-isak-definition-crop-name">
                <input type="text "style="width: 250px;">
            </td>
        </tr>
        <tr>
            <td style="text-align:right" id="for-isak-definition-crop-code-check">
                <input type="checkbox" ID="ForIsakCropCodeCheck" onClick="enableDefinition(this,'for-isak-definition-crop-code')" checked="true">
            </td>
            <td style="text-align:left">Код на Култура:</td>
            <td id="for-isak-definition-crop-code">
                <input type="text "style="width: 250px;">
            </td>
        </tr>
    </table>
    <br>
    <table width="100%" cellspacing="0" cellpadding="0" style="padding:10px;">
        <tr>       
            <td colspan="2"  style="text-align:center">
                <a id="btn-save-for-isak-definition" href="javaScript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-save'">Запази</a> 
                <a id="btn" href="javaScript:void(0)" class="easyui-linkbutton" onClick="jQuery('#win-for-isak-definition').window('close');" data-options="iconCls:'icon-cancel'">Откажи</a> 
            </td>
        </tr>
    </table>
</div>