<div>
    <table class="filters" cellspacing="0" cellpadding="0" style="padding:10px 10px 0 10px; margin-left: 10px;">
        <tr>
            <td style="text-align:right;" id="definition-full-number-check">
                <input type="checkbox" ID="FullNumberCheck" onClick="enableDefinition(this,'definition-full-number')">
            </td>
            <td style="text-align:left">Пълен номер:</td>
            <td id="definition-full-number">
                <input type="text "style="width: 250px;">
            </td>
        </tr>
        <tr>
            <td style="text-align:right" id="definition-ekatte-check">
                <input type="checkbox" ID="EkatteCheck" onClick="enableDefinition(this,'definition-ekatte')">
            </td>
            <td style="text-align:left">EKATTE:</td>
            <td id="definition-ekatte">
                <input type="text "style="width: 250px;">
            </td>
        </tr>
        <tr>
            <td style="text-align:right" id="definition-masiv-check">
                <input type="checkbox" ID="MasivCheck" onClick="enableDefinition(this,'definition-masiv')">
            </td>
            <td style="text-align:left">Масив:</td>
            <td id="definition-masiv">
                <input type="text "style="width: 250px;">
            </td>
        </tr>
        <tr>
            <td style="text-align:right" id="definition-number-check">
                <input type="checkbox" ID="NumberCheck" onClick="enableDefinition(this,'definition-number')">
            </td>
            <td style="text-align:left">Номер:</td>
            <td id="definition-number">
                <input type="text "style="width: 250px;">
            </td>
        </tr>
        <tr>
            <td style="text-align:right" id="definition-category-check">
                <input type="checkbox" ID="CategoryCheck" onClick="enableDefinition(this,'definition-cat')">
            </td>
            <td style="text-align:left">Категория земя:</td>
            <td id="definition-cat">
                <input type="text "style="width: 250px;">
            </td>
        </tr>
        <tr>
            <td style="text-align:right" id="definition-waytouse-check">
                <input type="checkbox" ID="WaytoUseCheck" onClick="enableDefinition(this,'definition-way')">
            </td>
            <td style="text-align:left">Начин на ползване:</td>
            <td id="definition-way">
                <input type="text "style="width: 250px;">
            </td>
        </tr>
        <tr>
            <td style="text-align:right" id="definition-mestnost-check">
                <input type="checkbox" ID="MestnostCheck" onClick="enableDefinition(this,'definition-mestnost')">
            </td>
            <td style="text-align:left">Местност:</td>
            <td id="definition-mestnost">
                <input type="text "style="width: 250px;">
            </td>
        </tr>
        <tr>
            <td style="text-align:right" id="definition-document-area-check">
                <input type="checkbox" ID="DocumentAreaCheck" onClick="enableDefinition(this,'definition-document-area')">
            </td>
            <td style="text-align:left">Площ по документ:</td>
            <td id="definition-document-area">
                <input type="text "style="width: 250px;">
            </td>
        </tr>
    </table>
    <br>
    <table width="100%" cellspacing="0" cellpadding="0" style="padding:10px;">
        <tr>       
            <td colspan="2"  style="text-align:center">
                <a id="btn-save-definition" href="javaScript:void(0)" class="easyui-linkbutton">Запази</a> 
                <a id="btn" href="javaScript:void(0)" class="easyui-linkbutton" onClick="jQuery('#win-definition').window('close');" data-options="iconCls:'icon-cancel'">Откажи</a> 
            </td>
        </tr>
    </table>
</div>