<div ID="changeLayerPanel">
	<div id="layerCustomizationTabs">
		<div title="Редактиране">
			<table width="100%"  cellspacing="0" cellpadding="0">
				<tr>
					<td style="padding:5px;" colspan="2">
						ЕКАТТЕ:
						<div id="layer-change-ekatte">
							<input type='combobox' style="" />
						</div>
					</td>
				</tr>
			</table>
			<fieldset style="border: 1px solid #000; margin: 0px 5px 5px 5px; padding: 5px;" id="work-layer-name-fields">
				<legend style="font-style: italic; font-weight: bold; margin-left: 10px;">Наименование на работен слой</legend>
				<table>
					<tr>
						<td style="padding:5px;" colspan="2">
							Име на слоя:
							<span id="work-layer-name">
								<input type='text' />
							</span>
						</td>
					</tr>
					<tr>
						<td style="padding:5px;">
							Стопанство:
								<span id="work-layer-farming">
									<input type='text' />
								</span>
						</td>
						<td style="padding:5px;">
							Година:
								<span id="work-layer-year">
									<input type='text' />
								</span>
						</td>
					</tr>
				</table>
			</fieldset>
			<fieldset style="border: 1px solid #000; margin: 0px 5px 5px 5px; padding: 5px;">
				<legend style="font-style: italic; font-weight: bold; margin-left: 10px;">Цветове</legend>
				<table>
					<tr class="layer-change-color-pickers">
						<td style="padding:5px;">
							Цвят на слоя:
							<span id="layer-color">
								<input type='text' style="display: none;" />
							</span>
						</td>
					
						<td style="padding:5px;padding-left:30px;">
							Цвят на границите:
							<span id="border-color">
								<input type='text' style="display: none;" />
							</span>
						</td>
					</tr>
					<tr>
						<td style="padding:5px; width:150px;" colspan="1">
							<div id="transparency" style="width: 150px;">
								<label for="transparency">Прозрачност:</label>
								<input type="text" style="width: 150px;" />
							</div>
						</td>
			 			<td colspan="1" style="padding-left:30px;">
							<div id="onlyborder" style="float:left;">
								<input type="checkbox" id="OnlyBorder" style="" />        
								<label for="OnlyBorder">Покажи само граници</label>
							</div>
						</td>
					</tr>
				</table>
			</fieldset>
			<fieldset style="border: 1px solid #000; margin: 0px 5px 5px 5px; padding: 5px;">
			<legend style="font-style: italic; font-weight: bold; margin-left: 10px;">Етикети</legend>
				<table>
					<tr>
						<td style="padding:5px;width:150px;">
							<div id="tags" style="float:left">
								<input type="checkbox" id="Tags" style="" />
								<label for="Tags">Покажи етикети</label>
							</div>
						</td>
						<td>
							<div id="label-name" style="padding-left:30px;">
								<label for="labelName">Използвай етикет:</label>
								<input type="combobox" id="labelName" style="width:150px" />
							</div>
						</td>
					</tr>
					<tr>
						<td style="padding:5px;width:150px;">
						</td>
						<td>
							<div id="label-size" style="padding-left:30px;">
								<label for="labelSize">Размер:</label>
								<input type="text" id="labelSize" style="width:150px" />
							</div>
						</td>
					</tr>
				</table>
			</fieldset>
			<table width="100%" cellspacing="0" cellpadding="0" style="padding:10px;margin-top:10px; ">
			    <tr>       
			        <td colspan="2"  style="text-align:center;">
						<a id="btn-save-layer-personalization" href="javaScript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-save'">Запази</a> 
			            <a href="javaScript:void(0)" class="easyui-linkbutton" onClick="jQuery('#layer-change-win').window('close');" data-options="iconCls:'icon-cancel'">Откажи</a> 
			        </td>
			    </tr>
			</table>
		</div>
		<div title="Данни от ОСЗ">
			<fieldset style="border: 1px solid #000; margin: 0px 5px 5px 5px; padding: 5px;">
				<legend style="font-style: italic; font-weight: bold; margin-left: 10px;">Опресняване на данни</legend>
				<table width="100%" cellspacing="0" cellpadding="0" style="padding:10px;">
					<tr>   
						<td>
							<p>В случай, че са правени промени по имоти от слой "КВС имоти" е необходимо ръчно опресняване на данните чрез бутона "Опресни". При натискането на бутона е възможно забавяне (<strong>до няколко минути</strong>), в случай, че има голям обем заредени данни в слой "Данни от ОСЗ" и слой "КВС Имоти".</p>
						</td>
					</tr>
					<tr style="text-align: right;">
						<td colspan="3" style="padding:10px 10px 0 0;">
							<a href="javaScript:void(0)" class="easyui-linkbutton" onClick="manualRefreshTopicLayerKVSData();" data-options="iconCls:'icon-reload'">Опресни</a>
						</td>
					</tr>
				</table>
			</fieldset>
			<fieldset style="border: 1px solid #000; margin: 0px 5px 5px 5px; padding: 5px;">
				<legend style="font-style: italic; font-weight: bold; margin-left: 10px;">Оцветяване по</legend>
				<table width="100%" cellspacing="0" cellpadding="0" style="padding:10px;">
					<tr>   
						<td>
							<input type="text" id="topic-layer-styler" style="width:120px;" />
						</td>
						<td>
							<label for="topic-layer-ekate">ЕКАТТЕ</label>
							<input type="text" id="topic-layer-ekate" style="width: 70px;" />
						</td>
						<td>
							<a id="filterSelected" href="javaScript:void(0)" class="easyui-linkbutton" onClick="initTopicLayerSearch()" data-options="iconCls:'icon-map'">Зареди</a>
						</td>
					</tr>
				</table>
			</fieldset>
			<table id="topic-layer-grid"></table>
		</div>
	</div>
</div>