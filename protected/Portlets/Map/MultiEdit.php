<?php

use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\Users\UsersController;

class MultiEdit extends Portlet
{
    public function onInit($param)
    {
        parent::onInit($param);
        // $this->Page->initController('Plugins.Core.Layers');
        // $this->Page->initController('Plugins.Core.Users');
        $this->Page->initDbController();
        $this->Page->initDbController('Plots');
        $this->Page->initDbController('ForIsak');

        $this->Page->LayersController = new LayersController();
        $this->Page->UsersController = new UsersController();
    }

    public function saveMultiEdit($sender, $params)
    {
        $data = $params->CallbackParameter;

        $tableName = $data[0];
        $ekatte = $data[1];
        $as_intermediate_crop = $data[2];
        $culture = $data[3];
        $plotIds = $data[4];

        if ('null' == $culture) {
            $culture = ' ';
        }
        $tableExists = $this->Page->UserDbController->getTableNameExist($tableName);

        if (!$tableExists) {
            die();
        }

        $id_array = [];

        $crop = $GLOBALS['Farming']['crops'][$culture];

        // set crop_short_type and green_area_factor
        $crop_short_type = '';
        $green_area_factor = 1;

        if ($crop['azot_fixed_crop']) {
            $crop_short_type .= 'АФК, ';
            $green_area_factor = $GLOBALS['Farming']['crops_short_type']['azot_fixed_crop']['factor_enp'];
        }
        if ($crop['is_tree_short_rotation']) {
            $crop_short_type .= 'ДВКЦР, ';
            $green_area_factor = $GLOBALS['Farming']['crops_short_type']['is_tree_short_rotation']['factor_enp'];
        }
        // Угар
        if ('190000' == $culture) {
            $crop_short_type .= 'УГАР, ';
            $green_area_factor = $GLOBALS['Farming']['crops_short_type']['190000']['factor_enp'];
        }

        if (strlen($crop_short_type)) {
            $crop_short_type = substr($crop_short_type, 0, -2);
        }

        if (!empty($_SESSION['filtered_plots'][$tableName]) && empty($plotIds)) {
            $plotIds = $_SESSION['filtered_plots'][$tableName];
        }
        if (empty($plotIds) && empty($_SESSION['filtered_plots'][$tableName])) {
            $plotIds[] = 0;
        }

        $options = [
            'tablename' => $tableName,
            'id_string' => implode(', ', $plotIds),

            'update' => [
                'cropcode' => $culture,
                'ekatte' => 'null' == $ekatte ? ' ' : $ekatte,
                'crop_type' => $crop['crop_type'] ?? null,
                'crop_genus' => $crop['crop_genus'] ?? null,
                'azot_fixed_crop' => (($crop['azot_fixed_crop']) ? 'true' : 'false'),
                'is_intermediate_crop' => (($crop['is_intermediate_crop']) ? 'true' : 'false'),
                'is_intermediate_weat_crop' => (($crop['is_intermediate_weat_crop']) ? 'true' : 'false'),
                'is_tree_short_rotation' => (($crop['is_tree_short_rotation']) ? 'true' : 'false'),
                'no_pndn' => (($crop['no_pndn']) ? 'true' : 'false'),
                'cropname' => $crop['crop_name'] ?? null,
                'crop_short_type' => strlen($crop_short_type) ? $crop_short_type : ' ',
                'green_area_factor' => $green_area_factor,
                'common_cultures' => 'false',
            ],
        ];

        foreach ($options['update'] as $key => $option) {
            if (null == $option) {
                unset($options['update'][$key]);
            }
        }
        $this->Page->UserDbForIsakController->ForIsakMultiEdit($options);

        $this->Page->UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, 'ForIsak', 'Multiedit performed', $tableName, $id_array);
    }
}
