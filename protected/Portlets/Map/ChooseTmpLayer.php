<?php

use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\Users\UsersController;

include_once __DIR__ . '/../../../engine/Plugins/Core/Layers/conf/index.php';
include_once __DIR__ . '/../../../engine/Plugins/Core/Farming/conf.php';

class ChooseTmpLayer extends Portlet
{
    private $LayersController = false;
    private $UsersController = false;

    public function onInit($param)
    {
        parent::onInit($param);
    }

    public function copyButtonClicked()
    {
        if ($this->User->isGuest) {
            return [];
        }

        $this->LayersController = new LayersController('Layers');
        $this->UsersController = new UsersController('Users');

        $options = [
            'return' => ['t.id'],
            'where' => [
                'farming' => ['column' => 'farming', 'compare' => '=', 'prefix' => 't', 'value' => $this->LayerFarmingFrom->Text],
                'year' => ['column' => 'year', 'compare' => '=', 'prefix' => 't', 'value' => $this->LayerYearFrom->Text],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => 1],
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $this->User->GroupID],
            ],
        ];
        $from = $this->LayersController->getLayers($options);

        $options = [
            'group_id' => $this->User->GroupID,
            'layer_type' => 2,
        ];
        $to = $this->LayersController->getLayersIdByLayerType($options);

        $table = DEFAULT_DB_PREFIX . 'layers_copy';
        $custom = [
            'user_id' => $this->User->UserID,
            'from_layer_id' => $from[0]['id'],
            'to_layer_id' => $to,
            'status' => 0,
            'group_id' => $this->User->GroupID,
        ];

        $this->LayersController->addLayerCopyItem($table, $custom);

        $this->UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, 'Map', 'Layer copy requested.', $table, [$from[0]['id']], [$to]);
    }
}
