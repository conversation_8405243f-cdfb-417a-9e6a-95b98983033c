<?php

/**
 * NavigationMenu class file.
 *
 * <AUTHOR>
 */
class NavigationMenu extends Portlet
{
    public $selectedMenu;

    /**
     * OnInit page.
     *
     * @param mixed $param
     */
    public function onInit($params)
    {
        if (true === filter_var(LEGACY_MODE, FILTER_VALIDATE_BOOLEAN)) {
            if (!$this->User->HasMapRightsR) {
                $this->MapLinkButton->Visible = false;
            }
            if (!$this->User->HasPlotRightsR) {
                $this->PlotLinkButton->Display = 'None';
            }
            if (!$this->User->HasSubsidyRights) {
                $this->SubsidyLinkButton->Display = 'None';
            }
            if (!$this->User->HasAgroRights) {
                $this->AgroLinkButton->Display = 'None';
            }
            if (!$this->User->HasEquityRights) {
                $this->EquityLinkButton->Display = 'None';
            }
            if (!$this->User->HasSalesContractsRightsR) {
                $this->SalesContractsLinkButton->Display = 'None';
            }
            if (!$this->User->HasHypothecsRightsR) {
                $this->HypothecsLinkButton->Display = 'None';
            }
            if (!$this->User->HasThematicMapsRightsR) {
                $this->ThemeMapsLinkButton->Display = 'None';
                $this->HideThematicSeparator->Value = 'true';
            }
            if (!$this->User->hasCollectionsRights) {
                $this->CollectionsLinkButton->Display = 'None';
            }
            if (!$this->User->HasWarehouseRights) {
                $this->WarehouseLinkButton->Display = 'None';
            }
        }

        if (!$this->User->HasSalesContractsRightsR && !$this->User->HasHypothecsRightsR) {
            $this->HideBoth->Value = 'true';
        } elseif (!$this->User->HasSalesContractsRightsR || !$this->User->HasHypothecsRightsR) {
            $this->HideBoth->Value = 'single';
        }
        if (!$this->User->HasSalesContractsRightsR && !$this->User->HasHypothecsRightsR) {
            $this->HideBoth->Value = 'true';
        } elseif (!$this->User->HasSalesContractsRightsR || !$this->User->HasHypothecsRightsR) {
            $this->HideBoth->Value = 'single';
        }

        parent::onInit($params);
    }
}
