<div>
	<fieldset style="border: 1px solid #000; margin: 5px; padding: 10px 5px;" id="transactionWindowTop">
		<legend style="font-weight: bold; font-style: italic; margin: 10px;">Информация за платежен документ</legend>
        <table class="filters">
            <tr id = "transaction-date">
                <td style="text-align: right; padding-left: 29px;">
                    <label for="include-date-in-payment-documents-checkbox">Дата:</label>
                    <input type="checkbox" ID="include-date-in-payment-documents-checkbox" style="vertical-align: middle;" checked />
                </td>
                <td>
                    <input type="text" id="include-date-in-payment-documents-date" style="width: 200px;"/>
                    <span class="easyui-tooltip" data-options="position: 'top', content: 'При премахване на отметката пред \'Дата:\' <br/> платежният документ ще бъде генериран без дата.'">
                        <a href="javascript:void(0);" class="easyui-linkbutton" data-options="iconCls: 'icon-info', plain: true"></a>
                    </span>
                </td>
            </tr>
        </table>
        <table class="filters" id="generate-bank-payment-documents" width="295">
            <tr id="generate-bank-payment-documents-bank">
                <td style="text-align: right; width:75px; padding-right: 5px; ">
                    <input type="checkbox" ID="transaction-bank-payment-order" style="vertical-align: middle;" />
                </td>
                <td>
                    <label for="transaction-bank-payment-order">Генерирай платежно нареждане</label>
                </td>
            </tr>
            <tr id="generate-bank-payment-documents-bank-weighing">
                <td style="text-align: right; width:75px; padding-right: 5px; ">
                    <input type="checkbox" ID="transaction-bank-payment-order-weighing-note" style="vertical-align: middle;" />
                </td>
                <td>
                    <label for="transaction-bank-payment-order-weighing-note">Генерирай кантарна бележка</label>
                </td>
            </tr>
            <tr id="generate-post-payment">
                <td style="text-align: right; width:75px; padding-right: 5px; ">
                    <input type="checkbox"  ID="transaction-post-payment" style="vertical-align: middle;"  checked disabled/>
                </td>
                <td>
                    <label for="transaction-post-payment-note">Генерирай пощенски запис</label>
                </td>
            </tr>

            <tr id="post-recipient-iban"> 
                <td style="text-align: left;">Сметка получател:</td>
                <td>
                    <input type="text" id="post-recipient-iban-value" style="width:200px" placeholder="IBAN"/>
                </td>
		    </tr>

            <tr id ="sender-iban">
                <td style="text-align: right;">
                    Сметка на наредител:
                </td>
                <td>
                    <input id="payment-order-farming-iban" type="text" style="width: 200px;" />
                </td>
            </tr>

            <tr id="generate-bank-payment-documents-payment-subjects-free-text-input">
                <td style="text-align: right; width:75px; padding-right: 5px; ">
                    <label for="transaction-payment-subject-free-text">Основание</label>
                </td>
                <td>
                    <textarea ID="transaction-payment-subject-free-text" maxlength="70" style="width: 150px; max-width: 150px; height: 45px; max-height: 45px;"></textarea>
                </td>
            </tr>

        </table>
        <table class="filters" id="generate-non-bank-payment-documents">
            <tr id="generate-bank-payment-documents-rko">
                <td style="text-align: right; width:75px; padding-right: 5px; ">
                    <input type="checkbox" ID="transaction-generate-rko" style="vertical-align: middle;" />
                </td>
                <td>
                    <label for="transaction-generate-rko">Генерирай разходен касов ордер</label>
                </td>
            </tr>
            <tr id="tr-rko-type-row">
                <td style="text-align: right;">Вид на документа</td>
                <td>
                    <span style="width:50px; display: inline-block;">
                        <input type="radio" value="rko_standart" id="tr_rko_standart" name="tr_rko_type" style="" checked />
                        <label for="tr_rko_standart">РКО</label>
                    </span>
                    <span>
                        <input type="radio" value="rko_receipt" id="tr_rko_receipt" name="tr_rko_type" />
                        <label for="tr_rko_receipt" style="margin-right:10px;">Разписка</label>
                    </span>
                </td>
			</tr>
            <tr id="tr_rko_type_declaration_row" style="display:none;">
                <td style="text-align: right; width:65px; padding-right: 5px; ">
                    <input type="checkbox" id="tr_rko_type_declaration" style="" />
                </td>
                <td>
                    <label for="tr_rko_type_declaration">Добави и декларация</label>
                </td>
                <td></td>
                <td></td>
            </tr>
            <tr id="">
                <td style="text-align: right; width:75px; padding-right: 5px; ">
                    <span class="easyui-tooltip" data-options="position: 'top', content: 'В платежния документ ще се генерират всички направени плащания по този договор'">
                        <input type="checkbox" ID="tr-collect-all-payment-amounts" style="vertical-align: middle;" />
                    </span>
                </td>
                <td>
                    <label for="tr-collect-all-payment-amounts">Добави всички плащания</label>
                </td>
            </tr>
            <tr id="generate-bank-payment-documents-rko-weighing">
                <td style="text-align: right; width:75px; padding-right: 5px; ">
                    <input type="checkbox" ID="transaction-generate-rko-weighing-note" style="vertical-align: middle;" />
                </td>
                <td>
                    <label for="transaction-generate-rko-weighing-note">Генерирай кантарна бележка</label>
                </td>
            </tr>

            <tr id="generate-bank-payment-documents-payment-subjects-input">
                <td style="text-align: right; width:75px; padding-right: 5px; ">
                    <label for="transaction-payment-subject-dropdown">Основание</label>
                </td>
                <td>
                    <input type="text" ID="transaction-payment-subject-dropdown" style="width: 200px;" />
                </td>
            </tr>

            <tr id="transactions-payment-subjects-text-row" style="display:none">
                <td style="text-align: right;">
                    <label for="transactions-payment-subjects-text">Основание</label>
                </td>
                <td>
                    <textarea ID="transactions-payment-subjects-text" maxlength="70" style="width: 200px; max-width: 200px; height: 45px; max-height: 45px;" ></textarea>
                </td>
                <td></td>
                <td></td>
            </tr>
		</table>
	</fieldset>

    <fieldset style="border: 1px solid #000; margin: 5px; padding: 10px 5px;" id='rkoTransactionNumberingFields'>
        <legend style="font-weight: bold; font-style: italic; margin-left: 10px;">Номериране на разходен касов ордер</legend>
        <table width="100%" class="filters" cellspacing="0" cellpadding="0" style="padding:10px 10px 0 10px">
            <tr>
                <td style="padding-left: 64px;" colspan="2">
                    <input type="checkbox" id="createDocumentWithoutRkoNumbering">
                    <label for="createDocumentWithoutRkoNumbering">Създай РКО без номерация</label>
                </td>
            </tr>
            <tr>
                <td style="padding: 0px;">
                    <a href="javaScript:void(0);" onClick="generateRkoTransNumbers()" class="easyui-linkbutton" data-options="iconCls:'icon-ok'" id="generateTransRkoNumbers">Генерирай номера</a>
                </td>
                <td>
                    <a href="javaScript:void(0);" onClick="emptyRkoTransNumbers()" class="easyui-linkbutton" data-options="iconCls:'icon-cancel'" id="clearTransRkoNumbers">Изчисти номерация</a>
                </td>
            </tr>
        </table>
        <div style="max-height: 200px; overflow: auto;">
            <table width="100%" class="filters" cellspacing="0" cellpadding="0" style="padding:10px 10px 0 10px; max-height: 200px; overflow: auto;" id="rko-trans-numbering">
            </table>
        </div>
    </fieldset>
	<table width="100%" cellspacing="0" cellpadding="0" style="margin: 10px 0 0 0;">
		<tr>       
			<td style="text-align:center;">            
				<a id="btn-payment-order-generate" href="javaScript:void(0)" class="easyui-linkbutton" onClick="generatePaymentDocumentFromTransactionGrid();" data-options="iconCls:'icon-ok'">Генерирай</a> 
				<a id="btn-close-payment-order-generate" href="javaScript:void(0)" class="easyui-linkbutton" onClick="jQuery('#win-generate-payment-document-from-transaction').window('close');" data-options="iconCls:'icon-cancel'">Откажи</a> 
			</td>
		</tr>
	</table>
</div>