<div id="dead-owner-message" style="display:none;background-color:#F08080; padding:5px; border-radius: 25px; text-align: center; margin-top:5px;">
	<p>Изплащате рента на починал собственик</p>
</div>
<table style="width: 750px;">
	<tr valign="top">
		<td>
			<fieldset style="border: 1px solid #000; margin-top: 5px; padding-bottom: 5px;">
			<legend style="font-weight: bold; font-style: italic; margin-left: 10px;" id="addPaymentBeginningMarker">Информация за плащане</legend>
			<div id="money-to-money">
				<table width="100%" class="filters" cellspacing="0" cellpadding="0" style="padding:10px 10px 0 10px;">
					<tr> 
						<td style="text-align: right; width: 87px;">Сума</td>
				        <td id="payment-amount" colspan="3">
				        	<input type="text" id="payment-amount-text" style="width: 200px;" />
				        </td>
				    </tr>
					<tr> 
						<td colspan="2" style="text-align: right; width: 120px;">
							<p id = "contract-payment-warning" style="display:none; color: red">
							Въведената сума за изплащане е по-малка от сумата, която се дължи по селектираните договори. По част от договорите ще остане дължима сума за изплащане
							</p>
						</td>
				    </tr>
				    <tr> 
				    	<td></td>
				        <td id="payment-method" colspan="3">
				        	<div id="payment-method-cash">
				        		<input type="radio" name="payment-methods" id="payment-method-cash-radio" checked />
								<label for="payment-method-cash-radio">Изплащане в брой</label>
				        	</div>
				        	<div id="payment-method-bank">
				        		<input type="radio" name="payment-methods" id="payment-method-bank-radio" />
								<label for="payment-method-bank-radio">Изплащане по банков път</label>
				        	</div>
							<div id="payment-method-post-order">
				        		<input type="radio" name="payment-methods" id="payment-method-post-order-radio" />
								<label for="payment-method-post-order-radio">Пощенски запис</label>
				        	</div>
				        </td>
				    </tr>
					<tr id="gerate-payment-order-row"> 
						<td style="text-align: right;"></td>
						<td id="payment-order" colspan="3">
							<input type="checkbox" id="payment-order-checkbox" />
							<label for="payment-order-checkbox">Генерирай платежно нареждане</label>
							<br>
							<div id="collect-all-payment-amounts-input">
							<input type="checkbox" id="collect-all-payment-amounts" />
							<label for="collect-all-payment-amounts">Добави предходни плащания</label>
							</div>
							<br>
							<input type="checkbox" id="payment-date-checkbox" />
							<label for="payment-date-checkbox">Без дата</label>
							<br>
							<div id="representative-money-payment-option">
								<input type="checkbox" id="representative-money-payment-checkbox" />
								<label for="representative-money-payment-checkbox">Изплати на представител</label>
							</div>
							<div id="combine-payment-document-container">
								<input type="checkbox" id="combine-payment-document" />
								<label for="combine-payment-document">Генерирай общ документ</label>
							</div>
						</td>
					</tr>
				    <tr id="orderer-bank-account-row"> 
				    	<td style="text-align: left;" colspan="1">Сметка наредител:</td>

				    	<td id="payment-orderer-bank-account" colspan="3">
				    		<input type="text" id="payment-orderer-bank-account-text" style="width: 200px;" />
				        </td>
				    </tr>
					<tr id="post-payment-sender-head">
						<td style="text-align: center; font-weight: bold;" colspan="4">Адрес наредител</td>
					</tr>

					<tr>
						<td style="text-align: right; width: 80px;">
                            Град/с:
                        </td>
                        <td>
                            <input class="sender-city easyui-textbox" type="text" style="width: 70px;"/>
                        </td>

						<td style="text-align: right; width: 60px;">
                            Област:
                        </td>
                        <td>
                            <input class="sender-region easyui-textbox" type="text" style="width: 70px;"/>
                        </td>
					</tr>
					<tr>	
                        <td style="text-align: right; width: 80px;">
                            Улица:
                        </td>
                        <td>
                            <input class="sender-street easyui-textbox" style="width: 70px;"/>
                        </td>
						 <td style="text-align: right; width: 60px;">
                            Блок:
                        </td>
                        <td>
                            <input class="sender-building easyui-textbox" style="width: 70px;"/>
                        </td>
					</tr>
					<tr>	
                        <td style="text-align: right; width: 80px;">
                            Вход:
                        </td>
                        <td>
                            <input class="sender-entrance easyui-textbox" style="width: 70px;"/>
                        </td>
						<td style="text-align: right; width: 60px;">
                            Етаж:
                        </td>
                        <td>
                            <input class="sender-floor easyui-textbox" style="width: 70px;"/>
                        </td>
					</tr>
					<tr>
                        <td style="text-align: right; width: 80px;">
                            Апартамент:
                        </td>
                        <td>
                            <input class="sender-appartment easyui-textbox" style="width: 70px;"/>
                        </td>
						<td style="text-align: right; width: 60px;">
                            Пощенски код:
                        </td>
                        <td>
                            <input class="sender-post-code easyui-textbox" style="width: 70px; text-align:left"/>
                        </td>
					</tr>


				    <tr id="bank-account-row"> 
				    	<td style="text-align: left;" colspan="1">Сметка получател:</td>
				    	<td id="payment-bank-account" colspan="3">
				    		<input type="text" id="payment-bank-account-text" style="width:200px" placeholder="IBAN"/>
				        </td>
				    </tr>
			    </table>
			</div>

			<div>
				<table width="100%" class="filters" cellspacing="0" cellpadding="0" style="padding:10px 10px 0 10px;">
					<tr>
						<td style="text-align: right; width: 120px;">Име получател</td>
				        <td id="payment-recipient" colspan="3">
				        	<input type="text" id="payment-recipient-text"  style ="width: 200px" placeholder="Име Презиме Фамилия"/>
				        </td>
				    </tr>
				    <tr>
						<td style="text-align: right; width: 120px;">ЕГН получател</td>
				        <td id="payment-recipient-egn">
				        	<input type="text" id="payment-recipient-egn-text" style="width: 200px;" placeholder="**********"/>
				        </td>
						<td></td>
						<td></td>
				    </tr>
				    <tr>
						<td style="text-align: right; width: 120px;">Адрес получател</td>
				        <td id="payment-recipient-address">
				        	<input type="text" id="payment-recipient-address-text" style="width: 200px;" placeholder="гр./с. Име, бул./ул. Име, №"/>
				        </td>
						<td></td>
						<td></td>
				    </tr>
				    <tr>
						<td style="text-align: right; width: 120px;">Лична карта получател</td>
				        <td id="payment-recipient-lk">
				        	<input type="text" id="payment-recipient-lk-text" style="width: 200px;" placeholder="Номер, издадена на, издадена от"/>
 				        </td>
						<td></td>
						<td></td>
 				    </tr>
				    <tr>
						<td style="text-align: right; width: 120px;">Пълномощно №/дата</td>
				        <td id="payment-recipient-proxy">
				        	<input type="text" id="payment-recipient-proxy-text" style="width: 200px;" placeholder="№/дата"/>
				        </td>
						<td></td>
						<td></td>
				    </tr>

					<tr id="post-payment-owner-head">
						<td style="text-align: center; font-weight: bold;" colspan="4">Адрес получател</td>
					</tr>

					<tr>
						<td style="text-align: right; width: 120px;">
                            Град/с:
                        </td>
                        <td style="text-align: left;">
                            <input class="owner-city easyui-textbox" type="text" style="width: 70px;"/>
                        </td>
						<td style="text-align: right; width: 60px;">
                            Област:
                        </td>
                        <td>
                            <input class="owner-region easyui-textbox" type="text" style="width: 70px;"/>
                        </td>
					</tr>
					<tr>	
                        <td style="text-align: right; width: 120px;">
                            Улица:
                        </td>
                        <td>
                            <input class="owner-street easyui-textbox" style="width: 70px;"/>
                        </td>
						<td style="text-align: right; width: 60px;">
                            Блок:
                        </td>
                        <td>
                            <input class="owner-building easyui-textbox" style="width: 70px;"/>
                        </td>
					</tr>

					<tr>	
                        <td style="text-align: right; width: 120px;">
                            Вход:
                        </td>
                        <td>
                            <input class="owner-entrance easyui-textbox" style="width: 70px;"/>
                        </td>
						<td style="text-align: right; width: 60px;">
                            Етаж:
                        </td>
                        <td>
                            <input class="owner-floor easyui-textbox" style="width: 70px;"/>
                        </td>
					</tr>

					<tr>
                        <td style="text-align: right; width: 120px;">
                            Апартамент:
                        </td>
                        <td>
                            <input class="owner-appartment easyui-textbox" style="width: 70px;"/>
                        </td>
						<td style="text-align: right; width: 60px;">
                            Пощенски код:
                        </td>
                        <td>
                            <input class="owner-post-code easyui-textbox" style="width: 70px;"/>
                        </td>
					</tr>

				    <tr>
						<td style="text-align: right; width: 100px;">Дата</td>
				        <td id="payment-date" colspan="3">
				        	<input type="text" id="payment-date-text" style="width: 200px;" />
				        </td>
				    </tr>

					<tr id="rko-type-row">
						<td style="text-align: right;">Вид на документа</td>
						<td>
							<span style="width:50px; display: inline-block;">
								<input type="radio" value="rko_standart" id="rko_standart" name="rko_type" style="" checked />
								<label for="rko_standart">РКО</label>
							</span>
							<span>
								<input type="radio" value="rko_receipt" id="rko_receipt" name="rko_type" />
								<label for="rko_receipt" style="margin-right:10px;">Разписка</label>
								
							</span>
							<span id="rko_card_input">
								<input type="radio" value="rko_card" id="rko_card" name="rko_type" style="" />
								<label for="rko_card">Фиш</label>
							</span>
						</td>
						<td></td>
						<td></td>
					</tr>
					<tr id="rko_type_declaration_row" style="display:none;">
						<td></td>
						<td>
							<input type="checkbox" id="rko_type_declaration" style="" />
							<label for="rko_type_declaration">Добави и декларация</label>
						</td>
						<td></td>
						<td></td>
					</tr>
					<tr id="payment-subjects-row">
						<td style="text-align: right;">Основание в РКО</td>
						<td>
							<input type="text" id="payment-subjects-combobox" style="width: 200px;" />
						</td>
						<td></td>
						<td></td>
					</tr>
					<tr id="rko-text-warning" style="display:none">
						<td colspan="2" style="color:red">
							Достигнати са максималният брой символи. Моля въведете основание ръчно.
						</td>
					</tr>
		            <tr id="payment-subjects-text-row" >
		                <td style="text-align: right;">
		                    <label for="payment-subjects-text">Основание в РКО</label>
		                </td>
		                <td>
		                    <textarea ID="payment-subjects-text" style="width: 200px; max-width: 200px; height: 45px; max-height: 45px;" ></textarea>
		                </td>
						<td></td>
						<td></td>
		            </tr>
			    </table>
			</div>
		</fieldset>
		<fieldset id="money-to-natura-weighing-note"  style="border: 1px solid #000; margin-top: 5px; padding-bottom: 5px;">
			<legend style="font-weight: bold; font-style: italic; margin-left: 10px;" id="addPaymentBeginningMarker">Бележки</legend>
				<table  width="100%" class="filters" cellspacing="0" cellpadding="0" style="padding:10px 10px 0 10px;">
					<tr> 
						<td style="text-align: right;"></td>
						<td id="weighing-note">
							<input type="checkbox" id="weighing-note-checkbox-money-to-nat" />
							<label for="weighing-note-checkbox">Генерирай кантарна бележка</label>
							<br>
							<input type="checkbox" id="payment-order-checkbox-money-to-nat" />
							<label for="payment-order-checkbox-money-to-nat">Генерирай платежно нареждане</label>
							<br>
							<input type="checkbox" id="payment-date-nat-checkbox-money-to-nat" />
							<label for="payment-date-nat-checkbox">Без дата</label>
						</td>
					</tr>
				</table>
		</fieldset>
		</td>
		<td>
			<fieldset style="border: 1px solid #000; padding-bottom: 5px; margin-top: 5px; margin-left: 10px">
			<legend style="font-weight: bold; font-style: italic; margin-left: 10px;">Информация за рента в пари</legend>
			<table width="100%" class="filters" cellspacing="0" cellpadding="0" style="padding:10px 10px 0 10px;">
				<tr>
			    	<td style="text-align: center; padding-left: 5px;">Дължима:</td>  
			    	<td style="text-align: center; padding-left: 5px;">Начислена:</td>  
			    	<td style="text-align: center; padding-left: 5px;">Изплатена:</td>  
			    	<td style="text-align: center; padding-left: 5px;">Обработки:</td>
			    	<td style="text-align: center; padding-left: 5px;">Оставаща:</td> 
			    </tr>
			    <tr>
					<td style="text-align: center; padding-left: 5px;" id="payment-renta-by-contract"></td>
					<td style="text-align: center; padding-left: 5px;" id="payment-renta-charged"></td>
					<td style="text-align: center; padding-left: 5px;" id="payment-renta-paid"></td>
					<td style="text-align: center; padding-left: 5px;" id="personal-use-price-sum"></td>
					<td style="text-align: center; padding-left: 5px;" id="payment-renta-unpaid"></td>
				</tr>
		    </table>
		</fieldset>
		
		<fieldset style="border: 1px solid #000; margin-top: 5px; padding: 5px 0px; margin-left: 10px">
			<legend style="font-weight: bold; font-style: italic; margin-left: 10px;">Възможности за изплащане на рента в пари</legend>
			<table width="100%" class="filters" cellspacing="0" cellpadding="0" style="padding:10px 10px 0 10px;">
				<tr> 
			        <td id="payment-type-money" style="padding-left: 30px;">
			        	<input type="radio" name="payment-type" id="payment-type-money-radio" checked />
						<label for="payment-type-money-radio">Изплащане в лева</label>
			        </td>
			        <td id="payment-type-natura" style="padding-left: 0px;">
			        	<input type="radio" name="payment-type" id="payment-type-natura-radio" />
						<label for="payment-type-natura-radio">Изплащане в натура</label> 
			        </td>
			    </tr>
		    </table>
		</fieldset>

		<fieldset style="border: 1px solid #000; margin-top: 5px; padding: 5px 0px; margin-left: 10px" id='rkoNumberingFields'>
			<legend style="font-weight: bold; font-style: italic; margin-left: 10px;">Номериране на разходен касов ордер</legend>
			<table width="100%" class="filters" cellspacing="0" cellpadding="0" style="padding:10px 10px 0 10px">
				<tr>
					<td style="padding-left: 30px;">
						<a href="javaScript:void(0);" onClick="generateRkoNumbers()" class="easyui-linkbutton" data-options="iconCls:'icon-ok'">Генерирай номера</a>
					</td>
					<td>
						<a href="javaScript:void(0);" onClick="clearRkoNumbers()" class="easyui-linkbutton" data-options="iconCls:'icon-cancel'">Премахни номерация</a>
					</td>
				</tr>
			</table>
			<table width="100%" class="filters" cellspacing="0" cellpadding="0" style="padding:10px 10px 0 10px;" id="rko-numbering">
				
		    </table>
		</fieldset>
		<fieldset id="money-to-natura-container" style="border: 1px solid #000; margin-top: 5px; padding: 5px; margin-left: 10px">
			<legend style="font-weight: bold; font-style: italic; margin-left: 10px;"> Натура </legend>
			<div id="money-to-natura">
				<div id="natura-column-headers" style="display: flex; font-weight: bold; padding: 5px; margin-bottom: 5px;">
					<div style="width: 105px;">Тип рента</div>
					<div style="width: 60px;">Eд. цена</div>
					<div style="width: 90px;">Kоличество</div>
					<div style="width: 90px;">Стойност</div>
				</div>
				<div id="payment-container"></div>
			</div>
		</fieldset>
			<fieldset style="border: 1px solid #000; margin-top: 5px; padding: 10px; margin-left: 10px" id="pko_exists">
				<legend style="font-weight: bold; font-style: italic; margin-left: 10px;">Наличнa сумa за получаване</legend>
				<div></div>
				<table width="100%" class="filters" cellspacing="0" cellpadding="0" style="margin-top: 10px;">
					<tr>
						<td style="">
							<label>Име на получателя</label>
							<input type="text" name="collections_recipient_name" id="collections_recipient_name" />
						</td>
						<td style="">
							<label>Адрес на получателя</label>
							<input type="text" name="collections_recipient_address" id="collections_recipient_address" />
						</td>
					</tr>
					<tr>
						<td style="">
							<label>ЕГН на получателя</label>
							<input type="text" name="collections_recipient_egn" id="collections_recipient_egn" />
						</td>
						<td style="">
							<label>№ на л.к. на получателя</label>
							<input type="text" name="collections_recipient_lk" id="collections_recipient_lk" />
						</td>
					</tr>
				</table>
			</fieldset>
		</td>
	</tr>
</table>
<table width="90%" cellspacing="0" cellpadding="0" style="margin: 20px 0 0 10px;">
    <tr>       
        <td colspan="2"  style="text-align:center;">
        	<span id="btn-add-payment">
        		<a href="javaScript:void(0)" class="easyui-linkbutton">Изплати</a> 
        	</span>
            <a href="javaScript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-payments'" id="btn-add-payment-with-deduction">Изплати чрез приспадане</a>
			<a id="btnclose" href="javaScript:void(0)" class="easyui-linkbutton" onClick="jQuery('#win-add-payment').window('close');" data-options="iconCls:'icon-cancel'">Откажи</a> 
        </td>
    </tr>
</table>