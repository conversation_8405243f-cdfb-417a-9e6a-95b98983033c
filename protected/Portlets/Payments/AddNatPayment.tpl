<div id="dead-owner-message-nat" style="display:none;background-color:#F08080; padding:5px; border-radius: 25px; text-align: center; margin-top:5px;">
	<p>Изплащате рента на починал собственик</p>
</div>
<table>
	<tr valign="top">
		<td>
			<fieldset style="border: 1px solid #000; margin-top: 5px; padding-bottom: 5px; width: 360px;" id="payment-info-panel">
				<legend style="font-weight: bold; font-style: italic; margin-left: 10px;">Информация за плащане</legend>
				<div id="natura-to-money">
					<table width="100%" class="filters" cellspacing="0" cellpadding="0" style="padding:10px 10px 0 10px;">
					    <tr> 
					    	<td style="text-align: right; width: 140px;"></td>
					        <td>
					        	<div id="np-method-cash">
					        		<input type="radio" name="payment-methods" id="np-method-cash-radio" checked />
									<label for="np-method-cash-radio">Изплащане в брой</label>
					        	</div>
					        	<div id="np-method-bank">
					        		<input type="radio" name="payment-methods" id="np-method-bank-radio" />
									<label for="np-method-bank-radio">Изплащане по банков път</label>
					        	</div>
					        </td>
					    </tr>
						<tr id="np-gerate-credit-payment-order-row">
							<td style="text-align: right;"></td>
							<td id="np-order">
								<input type="checkbox" id="weighing-note-natura-checkbox" />
								<label for="weighing-note-natura-checkbox">Генерирай кантарна бележка</label>
								<br>
								<input type="checkbox" id="generate-natura-payment-order" />
								<label for="generate-natura-payment-order">Генерирай разходен касов ордер</label>
								<br>
								<input type="checkbox" id="collect-all-payment-amounts" />
								<label for="collect-all-payment-amounts">Добави предходни плащания</label>
								<br>
								<input type="checkbox" id="payment-natura-date-checkbox" />
								<label for="payment-natura-date-checkbox">Без дата</label>
								<br>
								<div id="representative-nat-payment-option">
									<input type="checkbox" id="representative-nat-payment-checkbox" />
									<label for="representative-nat-payment-checkbox">Изплати на представител</label>
								</div>
							</td>
						</tr>
						<tr id="np-orderer-bank-account-row"> 
							<td style="text-align: right;">Сметка на наредител</td>
							<td id="np-orderer-bank-account">
								<input type="text" id="np-orderer-bank-account-text" style="width: 200px;" />
							</td>
						</tr>
						<tr id="np-bank-account-row"> 
							<td style="text-align: right;">Сметка на получател</td>
							<td id="np-bank-account">
								<input type="text" id="np-bank-account-text" style="width: 200px;" />
							</td>
						</tr>
					</table>
				</div>
				
				<div id="natura-to-natura" style="display: none;">
					<table width="100%" class="filters" cellspacing="0" cellpadding="0" style="padding:10px 10px 0 10px;">
						<tr> 
							<td style="text-align: right; width: 140px;"></td>
					        <td id="np-weighing-note">
								<input type="checkbox" id="np-weighing-note-checkbox" />
								<label for="np-weighing-note-checkbox">Генерирай кантарна бележка</label>
					        </td>
					    </tr>
				    </table>
				</div>
				
				<div>
					<table width="100%" class="filters" cellspacing="0" cellpadding="0" style="padding:10px 10px 0 10px;">
						<tr> 
							<td style="text-align: right; width: 140px;">Име получател</td>
					        <td id="np-recipient">
					        	<input type="text" id="np-recipient-text" style="width: 200px;"  placeholder="Име Презиме Фамилия"/>
					        </td>
					    </tr>
					    <tr> 
							<td style="text-align: right;">ЕГН получател</td>
					        <td id="np-recipient-egn">
					        	<input type="text" id="np-recipient-egn-text" style="width: 200px;"  placeholder="0123456789"/>
					        </td>
					    </tr>
					    <tr>
					    	<td style="text-align: right; width: 120px;">Адрес получател</td>
					    	<td id="np-recipient-address">
					    		<input type="text" id="np-recipient-address-text" style="width: 200px;" placeholder="гр./с. Име, бул./ул. Име, №"/>
					    	</td>
					    </tr>
					    <tr>
					    	<td style="text-align: right; width: 120px;">Лична карта получател</td>
					    	<td id="np-recipient-lk">
					    		<input type="text" id="payment-recipient-lk-text" style="width: 200px;" placeholder="Номер, издадена на, издадена от"/>
					    	</td>
					    </tr>
					    <tr> 
							<td style="text-align: right;">Пълномощно №/дата</td>
					        <td id="np-recipient-proxy">
					        	<input type="text" id="np-recipient-proxy-text" style="width: 200px;"  placeholder="№/дата" />
					        </td>
					    </tr>
					    <tr> 
							<td style="text-align: right;">Дата</td>
					        <td id="np-date">
					        	<input type="text" id="np-date-text" style="width: 200px;" />
					        </td>
					    </tr>

						<tr id="nat-rko-type-row">
							<td style="text-align: right;">Вид на документа</td>
							<td>
								<span style="width:50px; display: inline-block;">
									<input type="radio" value="rko_standart" id="nat_rko_standart" name="nat_rko_type" style="" checked />
									<label for="nat_rko_standart">РКО</label>
								</span>
								<span>
									<input type="radio" value="rko_receipt" id="nat_rko_receipt" name="nat_rko_type" />
									<label for="nat_rko_receipt" style="margin-right:10px;">Разписка</label>
									
								</span>
								<span id="nat_rko_card_input">
									<input type="radio" value="rko_card" id="nat_rko_card" name="nat_rko_type" style="" />
									<label for="nat_rko_card">Фиш</label>
								</span>
							</td>
							<td></td>
							<td></td>
						</tr>
						<tr id="nat_rko_type_declaration_row" style="display:none;">
							<td></td>
							<td>
								<input type="checkbox" id="nat_rko_type_declaration" style="" />
								<label for="nat_rko_type_declaration">Добави и декларация</label>
							</td>
							<td></td>
							<td></td>
						</tr>
					    <tr id="np-payment-subjects-row"> 
					    	<td style="text-align: right;">Основание в РКО</td>
					    	<td>
					    		<input type="text" id="np-payment-subjects-combobox" style="width: 200px;" />
					        </td>
					    </tr>

						<tr id="np-payment-subjects-text-row" >
							<td style="text-align: right;">
								<label for="np-payment-subjects-text">Основание в РКО</label>
							</td>
							<td>
								<textarea ID="np-payment-subjects-text" maxlength="70" style="width: 200px; max-width: 200px; height: 45px; max-height: 45px;" ></textarea>
							</td>
						</tr>
				    </table>
				</div>
			</fieldset>
		</td>
		<td id="win-payment-rents-info">
			<fieldset style=" width: 370px; border: 1px solid #000; max-height: 130px; overflow:auto; margin: 5px 10px 0 10px;" id="addNatPaymentBeginningMarker">
				<legend style="font-weight: bold; font-style: italic; margin-left: 10px;">Информация за рента в натура</legend>
				<table id="js-natura-renta-payment-info" width="100%" class="filters" cellspacing="0" cellpadding="0" style="padding:10px 10px 0 10px;">
					<tr>
				        <td style="text-align: center; padding-left: 5px; width: 115px;">Тип на натура:</td>  
				        <td style="text-align: right; padding-left: 5px;">Дължима:</td>  
				        <td class = "add-payment-charged-renta-label" style="text-align: right; padding-left: 5px;">Начислена:</td>  
				        <td style="text-align: right; padding-left: 5px;">Изплатена:</td>  
				        <td style="text-align: right; padding-left: 5px;">Оставаща:</td>  
				    </tr>
			    </table>
			</fieldset>
			
			<fieldset id="paymentQtyFields" style=" width: 370px; border: 1px solid #000; padding: 5px 0px; max-height: 200px; overflow:auto; margin: 5px 10px 0 10px;">
				<legend style="font-weight: bold; font-style: italic; margin-left: 10px;">Възможности за изплащане на рента в натура</legend>
				<table id="paymentQtyOptions" width="100%" class="filters" cellspacing="0" cellpadding="0" style="padding:10px 10px 0 10px;">
					<tr> 
				        <td id="np-type-money" style="padding-left: 30px;">
				        	<input type="radio" name="natura-payment-type" id="np-type-money-radio" checked />
							<label for="np-type-money-radio">Изплащане в лева</label>
				        </td>
				        <td id="np-type-natura" style="padding-left: 0px;">
				        	<input type="radio" name="natura-payment-type" id="np-type-natura-radio" />
							<label for="np-type-natura-radio">Изплащане в натура</label>
				        </td>
				    </tr>
			    </table>
			    <div id="js-renta-nat-nat-payment-div" style="display: none;">
				    <table id="js-renta-nat-nat-payment" class="filters" width="100%" cellspacing="0" cellpadding="0" style="padding:10px 10px 0 10px;">
				    	
				    </table>
			    </div>
			    <table id="js-renta-nat-leva-payment" class="filters" width="100%" cellspacing="0" cellpadding="0" style="padding:10px 10px 0 10px;">
			    	<tr>
				    	<td></td>
				    	<td>Единична цена</td>
				    	<td>Сума</td>
			    	</tr>
			    </table>
			</fieldset>

			<fieldset style=" width: 370px; border: 1px solid #000; padding: 5px 0px;  margin: 5px 10px 0 10px;" id='rkoNatNumberingFields'>
				<legend style="font-weight: bold; font-style: italic; margin-left: 10px;">Номериране на разходен касов ордер</legend>
				<table width="100%" class="filters" cellspacing="0" cellpadding="0" style="padding:10px 10px 0 10px">
					<tr>
						<td style="padding-left: 30px;">
							<a href="javaScript:void(0);" onClick="generateRkoNatNumbers()" class="easyui-linkbutton" data-options="iconCls:'icon-ok'">Генерирай номера</a>
						</td>
						<td>
							<a href="javaScript:void(0);" onClick="clearRkoNatNumbers()" class="easyui-linkbutton" data-options="iconCls:'icon-cancel'">Премахни номерация</a>
						</td>
					</tr>
				</table>
				<table width="100%" class="filters" cellspacing="0" cellpadding="0" style="padding:10px 10px 0 10px; max-height: 80px; overflow: auto;" id="rko-nat-numbering">
			    </table>
			</fieldset>
		</td>
	</tr>
</table>

<table width="100%" cellspacing="0" cellpadding="0" style="margin: 10px 0 0 0;">
    <tr>       
        <td colspan="2"  style="text-align:center;">
            <span id="btn-add-nat-payment">
                <a href="javaScript:void(0)" class="easyui-linkbutton">Изплати</a> 
            </span>
            <a id="btnclose" href="javaScript:void(0)" class="easyui-linkbutton" onClick="jQuery('#win-add-nat-payment').window('close');" data-options="iconCls:'icon-cancel'">Откажи</a> 
        </td>
    </tr>
</table>