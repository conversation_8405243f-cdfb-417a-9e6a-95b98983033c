<div id="data-menu" class="easyui-menu tf-sub-menu">
    <div href="<%=$this->Service->constructUrl('Map.Home')%>" iconCls="icon-map">Карта</div>
    <com:THyperLink
            NavigateUrl="<%=$this->Service->constructUrl('ThematicMaps.Home')%>"
            ID="ThemeMapsLinkButton"
            Text="&nbsp;Тематични карти"
            Style="width: 100%; text-align: left;" />
    <div href="<%=$this->Service->constructUrl('Files.Home')%>" iconCls="icon-upload">Зареждане на данни</div>
    <div href="<%=$this->Service->constructUrl('OSZ.Home')%>"  iconCls="icon-files">Данни от ОСЗ</div>
</div>
<div id="data-menu-kvs" class="easyui-menu tf-sub-menu">
    <div href="<%=$this->Service->constructUrl('Plots.Home')%>" iconCls="icon-edit-geometry">Имоти</div>
    <div href="<%=$this->Service->constructUrl('Owners.Home')%>"  iconCls="icon-owners">Собственици</div>

    <div href="<%=$this->Service->constructUrl('Contracts.Home')%>" iconCls="icon-contract">Договори </div>
    <div iconCls="icon-contract" ID="OwnershipGroup">
        <span>Собственост</span>
        <div style="width: 170px;">
            <com:THyperLink
                    NavigateUrl="<%=$this->Service->constructUrl('SalesContracts.Home')%>"
                    ID="SalesContractsLinkButton"
                    Text="&nbsp;Договори за продажби" />
            <div class="menu-sep" id="ownership-inside-separator"></div>
            <com:THyperLink
                    NavigateUrl="<%=$this->Service->constructUrl('Hypothecs.Home')%>"
                    ID="HypothecsLinkButton"
                    Text="&nbsp;Ипотеки"
                    Style="width: 100%; text-align: left;"/>
        </div>
    </div>
    <div href="<%=$this->Service->constructUrl('Subleases.Home')%>" iconCls="icon-contract" >Преотдадени</div>
    <div href="<%=$this->Service->constructUrl('Agreements.Home')%>" iconCls="icon-agreements">Споразумения</div>
    <div iconCls="icon-data">
        <span>Ренти</span>
        <div>
            <div href="<%=$this->Service->constructUrl('Payments.Home')%>" iconCls="icon-contract">Договори</div>
            <div href="<%=$this->Service->constructUrl('OwnerPayments.Home')%>" iconCls="icon-owners">Собственици</div>
            <div href="<%=$this->Service->constructUrl('Payroll.Home')%>" iconCls="icon-datagrid">Ведомост</div>
        </div>
    </div>
    <com:THyperLink
            NavigateUrl="<%=$this->Service->constructUrl('Collections.Home')%>"
            ID="CollectionsLinkButton"
            Text="&nbsp;Вземания"
            Style="width: 100%; text-align: left;"/>
    <div href="<%=$this->Service->constructUrl('Reports.Home')%>" iconCls="icon-reports">Справки</div>
</div>
<div id="data-menu-equity" class="easyui-menu tf-sub-menu">
    <div href="<%=$this->Service->constructUrl('Cooperators.Home')%>" iconCls="icon-rents">Член - кооператори</div>
    <div href="<%=$this->Service->constructUrl('Dividends.Home')%>" iconCls="icon-rents">Дивиденти</div>
</div>
<div id="data-menu-zp" class="easyui-menu tf-sub-menu">
    <div href="<%=$this->Service->constructUrl('ZPlots.Home')%>" iconCls="icon-edit-geometry">Парцели</div>
    <div href="<%=$this->Service->constructUrl('Isak.Home')%>" iconCls="icon-edit-geometry">ИСАК</div>
    <div href="<%=$this->Service->constructUrl('SubsidiesWizard.Home')%>" iconCls="icon-edit-geometry">Съветник за субсидии</div>
    <div href="<%=$this->Service->constructUrl('CropRotation.Home')%>" iconCls="icon-planting">Сеитбооборот</div>
    <div href="<%=$this->Service->constructUrl('Overlaps.Home')%>" iconCls="icon-intersection">Застъпвания</div>
</div>
<div id="data-menu-cov" class="easyui-menu tf-sub-menu">
    <div href="<%=$this->Service->constructUrl('Coverage.Home')%>" iconCls="icon-data-machine">Данни от машини</div>
    <div href="<%=$this->Service->constructUrl('Diary.Home')%>" iconCls="icon-event">Мероприятия</div>
    <div href="<%=$this->Service->constructUrl('HomeTrack')%>" iconCls="icon-satelite">Проследяване</div>
    <div href="<%=$this->Service->constructUrl('Navigation.Home')%>" iconCls="icon-satelite">Линии за навигация</div>
</div>
<com:TConditional Visible="<%= LEGACY_MODE %>">
    <prop:TrueTemplate>
        <div id="data-menu-warehouse" class="easyui-menu tf-sub-menu">
            <div href="<%=$this->Service->constructUrl('Warehouse.Home')%>" iconCls="icon-warehouse">Склад</div>
            <div id="warehouse-settings" iconCls="icon-settings">
                <span>Заприхождаване</span>
                <div>
                    <div href="<%=$this->Service->constructUrl('WarehouseAddTransaction.Home')%>" iconCls="icon-warehouse-add">Заприхождаване на артикули</div>
                    <div href="<%=$this->Service->constructUrl('WarehouseAddProductionTransaction.Home')%>" iconCls="icon-warehouse-add">Заприхождаване на готова продукция</div>
                    <div href="<%=$this->Service->constructUrl('WarehouseReturnTransaction.Home')%>" iconCls="icon-warehouse-add">Връщане от парцели</div>
                </div>
            </div>

            <div id="warehouse-settings" iconCls="icon-settings">
                <span>Изписване</span>
                <div>
                    <div href="<%=$this->Service->constructUrl('WarehouseSubContragentsTransaction.Home')%>" iconCls="icon-users">Към контрагенти</div>
                    <div href="<%=$this->Service->constructUrl('WarehouseSubPlotsTransaction.Home')%>" iconCls="icon-users">Към парцели</div>
                    <div href="<%=$this->Service->constructUrl('WarehouseSubMachinesTransaction.Home')%>" iconCls="icon-users">Към активи</div>
                </div>
            </div>
            <div href="<%=$this->Service->constructUrl('WarehouseTransferTransaction.Home')%>" iconCls="icon-warehouse-transf">Прехвърляне</div>
            <div href="<%=$this->Service->constructUrl('WarehouseReports.Home')%>" iconCls="icon-reports">Справки</div>
            <div id="btn-manage-documents" iconCls="icon-documents">Документи</div>
            <div id="warehouse-settings" iconCls="icon-settings">
                <span>Настройки</span>
                <div>
                    <div id="btn-manage-measures" iconCls="icon-measure-line">Мерни единици</div>
                    <div id="btn-manage-contragents" iconCls="icon-users">Контрагенти</div>
                    <div id="btn-manage-farms" iconCls="icon-users">Стопанства</div>
                    <div id="btn-manage-machines" iconCls="icon-users">Активи</div>
                    <div id="btn-manage-items" iconCls="icon-box">Артикули</div>
                    <div id="btn-manage-user-permissions" iconCls="icon-users">Права за работа</div>
                    <div id="btn-manage-warehouse-config-params" iconCls="icon-users">Кофигурационни параметри</div>
                    <div id="warehouse-groups" iconCls="icon-settings">
                        <span>Групи</span>
                        <div>
                            <div id="btn-manage-contragents-groups" iconCls="icon-measure-line">Групи контрагенти</div>
                            <div id="btn-manage-items-groups" iconCls="icon-users">Групи артикули</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </prop:TrueTemplate>
</com:TConditional>
<div id="help-menu" class="easyui-menu tf-sub-menu">
    <div id="contact-us" iconCls="icon-users">Връзка с нас</div>
    <div href="https://tfwiki.atlassian.net/wiki/display/TH" id="guidance" iconCls="icon-manual">Ръководствo</div>
    <div href="https://tfwiki.atlassian.net/wiki/pages/viewpage.action?pageId=23756805" id="guidance-video" iconCls="icon-video-manual">Видео ръководствo</div>
    <div href="https://tfwiki.atlassian.net/wiki/pages/viewpage.action?pageId=38895618" id="useful-information" iconCls="icon-manual">Полезна информация</div>
</div>
<com:TConditional Visible="<%= LEGACY_MODE %>">
    <prop:TrueTemplate>
        <div id="settings-menu" class="easyui-menu tf-sub-menu">
            <div href="<%=$this->Service->constructUrl('Farming.Home')%>" iconCls="icon-agriculture">Стопанства</div>
            <div href="<%=$this->Service->constructUrl('Users.Home')%>" iconCls="icon-users" id="user-link-button">Потребители</div>
            <div id="alert-subjects" iconCls="icon-notification">Аларми</div>
            <div id="renta-type" iconCls="icon-rents">Типове рента</div>
            <div id="payment-subjects" iconCls="icon-documents">Основания в РКО</div>
            <div id="payment-numbering" iconCls="icon-template">Номерация РКО</div>
            <div id="contracts-templates" iconCls="icon-print">Бланки</div>
            <div id="password-change" iconCls="icon-password">Парола</div>

            <%%
                if($this->User->isSuperadmin || $this->User->userLevel == 5) {
                    echo '<div class="menu-sep"></div><div href='.$this->Service->constructUrl("GlobalNotifications.Home").' id="global-notifications-link-button" iconCls="icon-notification">Нотификации</div>';
                }
            %>

            <!-- <div id="account-settings" iconCls="icon-settings">Профил</div> -->
        </div>
    </prop:TrueTemplate>
</com:TConditional>
<div id="win-edit-warehouse-config-params" class="easyui-window" title="Склад параметри" data-options="iconCls:'icon-users', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true">
    <com:Application.Portlets.Warehouse.WarehouseConfigParams />
</div>

<div id="win-contracts-templates" class="easyui-window" title="Списък с бланки" style="width:550px; height:400px;"
     data-options="iconCls:'icon-print', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
    <table id="templates-tables"></table>
</div>
<div id="win-add-edit-template" class="easyui-window" title="Добавяне на бланка" style="width:1024px; height:590px;"
     data-options="iconCls:'icon-add', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
    <com:Application.Portlets.Utilities.AddEditTemplate />
</div>
<div id="win-renta-type" class="easyui-window" title="Типове рента" style="width:700px; height: 450px;" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-rents', maximizable: false, minimizable: false, modal: true">
    <table id="renta-type-tables"></table>
</div>

<div id="win-add-renta-type" class="easyui-window" title="Добавяне на тип рента" style="width:370px; height: 350px; padding-top: 5px;" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-add', maximizable: false, minimizable: false, modal: true">
    <com:Application.Portlets.Utilities.AddRentaType />
</div>

<div id="win-payment-numbering" class="easyui-window" title="Начален номер за РКО" style="width:350px; height: 155px; padding-top: 5px;" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-add', maximizable: false, minimizable: false, modal: true">
    <com:Application.Portlets.Utilities.SetRkoNumbering />
</div>

<div id="win-payment-subjects" class="easyui-window" title="Основания в разходен касов ордер" style="width:500px; height: 350px;" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-documents', maximizable: false, minimizable: false, modal: true">
    <table id="payment-subject-tables"></table>
</div>

<div id="win-add-payment-subject" class="easyui-window" title="Добавяне на основание" style="width:350px; height: 190px; padding-top: 5px;" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-add', maximizable: false, minimizable: false, modal: true">
    <com:Application.Portlets.Utilities.AddPaymentSubject />
</div>

<div id="win-plots-detailed" class="easyui-window" title="Имоти подробно" style="width:320px; height: 350px; padding: 5px;" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-add', maximizable: false, minimizable: false, modal: true">
    <com:Application.Portlets.Utilities.AddColumnsPlotsDetailed />
</div>

<div id="win-contract-blank" class="easyui-window" title="Договори" style="width:320px; height: 350px; padding: 5px;" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-add', maximizable: false, minimizable: false, modal: true">
    <com:Application.Portlets.Utilities.AddColumnsContractDetailed />
</div>

<div id="win-owner-detailed" class="easyui-window" title="Декларация за лични данни" style="width:350px; height: 350px; padding: 5px;" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-add', maximizable: false, minimizable: false, modal: true">
    <com:Application.Portlets.Utilities.AddColumnsPersonalInfo />
</div>

<div id="win-contract-signer" class="easyui-window" title="Страна по договор - подписал договора" style="width:350px; height: 350px; padding: 5px;" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-add', maximizable: false, minimizable: false, modal: true">
    <com:Application.Portlets.Utilities.AddColumnsContractSigner />
</div>

<div id="win-renta-natura-detailed" class="easyui-window" title="Рента в натура" style="width:330px; height: 350px; padding: 5px;" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-add', maximizable: false, minimizable: false, modal: true">
    <com:Application.Portlets.Utilities.AddColumnsRentaNaturaDetailed />
</div>

<div id="win-kontragent-detailed" class="easyui-window" title="Контрагент подробно" style="width:330px; height: 350px; padding: 5px;" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-add', maximizable: false, minimizable: false, modal: true">
    <com:Application.Portlets.Utilities.AddColumnsKontragentDetailed />
</div>

<div id="win-kontragent-rep-detailed" class="easyui-window" title="Контрагент подробно" style="width:330px; height: 350px; padding: 5px;" data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-add', maximizable: false, minimizable: false, modal: true">
    <com:Application.Portlets.Utilities.AddColumnsKontragentRepDetailed />
</div>
<div id="win-farm-detailed" class="easyui-window" title="Стопанство подробно"
    style="width:330px; height: 350px; padding: 5px;"
    data-options="closed: true, resizable: false, collapsible: false, iconCls: 'icon-add', maximizable: false, minimizable: false, modal: true">
    <com:Application.Portlets.Utilities.AddColumnsFarmDetails />
</div>
<div id="win-change-password" class="easyui-window" title="Смяна на парола" data-options="iconCls:'icon-password', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true" style="width:400px;height:160px;">
    <com:Application.Portlets.UsersChangePassword />
</div>

<div id="success-changed-password" class="easyui-window" title="Смяна на парола" data-options="iconCls:'icon-password', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true" style="width:370x;height:65px; font-size: 16px; padding: 5px;">
    Вие успешно сменихте паролата си!
</div>

<div id="win-account-settings" class="easyui-window" title="Настройки на профил" data-options="iconCls:'icon-settings', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true" style="width:400px;height:115px;">
    <com:Application.Portlets.AccountSettings />
</div>

<div id="win-about-technofarm" class="easyui-window" title="За софтуера" data-options="iconCls:'icon-info', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true" style="width:400px;height:200px;">
    <com:Application.Portlets.Help.AboutTechnofarm />
</div>

<div id="win-contact-form" class="easyui-window" title="Форма за контакт" data-options="iconCls:'icon-users', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true" style="width:420px;height:530px;">
    <com:Application.Portlets.Help.ContactForm />
</div>

<div id="win-guidance" class="easyui-window" title="Ръководства" data-options="iconCls:'icon-template', closed: true, resizable: false,  minimizable: false, collapsible: false, maximizable: false, modal: true" style="width:400px;height:160px;">
    <com:Application.Portlets.Help.Guidance />
</div>
<div id="win-alert-settings" class="easyui-window" title="Настройки на съобщения" style="width:550px; height:600px;"
     data-options="iconCls:'icon-notification', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
    <table id="alert-tables"></table>
</div>
<div id="win-add-edit-notification" class="easyui-window" title="Добавяне на съобщение" style="width:1024px; height:550px;"
     data-options="iconCls:'icon-add', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
    <com:Application.Portlets.Utilities.AddEditTemplate />
</div>
<div id="win-notifications" class="easyui-window" title="Списък съобщения" style="width:550px; height:400px;"
     data-options="iconCls:'icon-notification', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true">
    <table id="notification-tables"></table>
</div>
<div class="clear"></div>
<com:TConditional Visible="<%= LEGACY_MODE %>">
    <prop:TrueTemplate>
        <div id="tf-main-menu" style="border: 0;">
            <div style="font-weight: bold !important;float:left;font-size: 13px !important;">
                <%%
                    if(CSS_CUSTOM_THEME_NAME) {
                        $themeName = CSS_CUSTOM_THEME_NAME;
                        echo '<img src="themes/'.$themeName.'/'.$themeName.'.svg" height="16" id="mainmenu-logo" style="display: inline; vertical-align: middle; margin-left: 7px"/>';
                    } else {
                        echo '<img src="themes/Main/images/logo.svg" height="24" id="mainmenu-logo" style="display: inline; vertical-align: middle;"/>';
                    }
                %>
                <com:THyperLink ID="MapLinkButton" Text="Карта" />
                <com:THyperLink ID="PlotLinkButton" Text="Имоти" />
                <com:THyperLink ID="EquityLinkButton" Text="Дялов капитал" />
                <com:THyperLink ID="SubsidyLinkButton" Text="Субсидии" />
                <com:THyperLink ID="AgroLinkButton" Text="Агротехника" />
                <com:THyperLink ID="WarehouseLinkButton" Text="Склад" />
                <com:THyperLink ID="SettingLinkButton" Text="Настройки" />
                <com:THyperLink ID="HelpLinkButton" Text="Помощ" />

            </div>

                <a
                        id="btn-user-logout"
                        href="javaScript:void(0)"
                        class="easyui-linkbutton l-btn l-btn-small"
                        data-options="iconCls:'icon-logout', plain:true"
                        style="float:right;margin-right:10px;"
                >
                    Изход
                </a>
            <a id="notification-grid"  class="easyui-linkbutton" data-options="iconCls:'icon-notification', plain:true" style="float:right;"></a>
            <div style="float:right">
                <span id="last-login-tooltip" class="easyui-tooltip" data-options="position: 'bottom', content: ''" style="float: left" title="">
                    <a href="#" onclick="event.preventDefault();" class="easyui-linkbutton" data-options="iconCls: 'icon-info', plain:true"></a>
                </span>
            </div>
            <div class="greet-name" style="float:right;margin:10px 5px 0 0;">
                Здравейте, <%=$this->User->FullName%>
            </div>
            <div class="clear"></div>
        </div>
    </prop:TrueTemplate>
</com:TConditional>
    <com:TActiveHiddenField ID="HideBoth" value="false"/>
    <com:TActiveHiddenField ID="HideThematicSeparator" value="false"/>
<script>
	var legacyMode = JSON.parse('<%=LEGACY_MODE%>');
    if (legacyMode == true) {
        var mapLinkButton = '<%=$this->MapLinkButton->ClientID%>';
        var plotLinkButton = '<%=$this->PlotLinkButton->ClientID%>';
        var EquityLinkButton = '<%=$this->EquityLinkButton->ClientID%>';
        var subsidyLinkButton = '<%=$this->SubsidyLinkButton->ClientID%>';
        var agroLinkButton = '<%=$this->AgroLinkButton->ClientID%>';
        var WarehouseLinkButton = '<%=$this->WarehouseLinkButton->ClientID%>';
        var settingLinkButton = '<%=$this->SettingLinkButton->ClientID%>';
        var helpLinkButton = '<%=$this->HelpLinkButton->ClientID%>';
        var SalesContractsLinkButton = '<%=$this->SalesContractsLinkButton->ClientID%>';
        var HypothecsLinkButton = '<%=$this->HypothecsLinkButton->ClientID%>';
        var ThemeMapsLinkButton = '<%=$this->ThemeMapsLinkButton->ClientID%>';
        var CollectionsLinkButton = '<%=$this->CollectionsLinkButton->ClientID%>';
    }
    var isSuperAdmin = '<%=$this->User->isSuperAdmin%>';
	var hasWarehouseRights = '<%=$this->User->hasWarehouseRights%>';
	var userLevel = '<%=$this->User->UserLevel%>';
	var isGuest = '<%=$this->User->IsGuest%>';
	var isTrial = '<%=$this->User->IsTrial%>';
    var HideBoth = '<%=$this->HideBoth->Value%>';
    var HideThematicSeparator = '<%=$this->HideThematicSeparator->Value%>';
</script>