<div style="margin: 10px;">
    <p style="font-style:italic; font-weight: bold;">Телефон за връзка: +359 889 008 070</p>
</div>
<div style="margin: 10px;">
    <p style="font-style:italic; font-weight: bold;">E-mail: <a href="mailto:<EMAIL>"><EMAIL></a></p>
</div>

<div style="margin: 10px;" class="contact-data">
	<div class="contact-data__mobile">
        <p style="font-style:italic; font-weight: bold;">Вашият мобилен телефон*</p>
        <com:TTextBox ID="ContactDataMobile" Style="width: 360px; resize: none;" />
        <com:TRequiredFieldValidator ValidationGroup="ContactGroup" ControlToValidate="ContactDataMobile" Text="Задължително поле." />
        <br>
        <com:TCustomValidator ValidationGroup="ContactGroup" ControlToValidate="ContactDataMobile" ClientValidationFunction="validateMobileNumber" Text="Невалиден мобилен телефон." />
    </div>
    <div class="contact-data__email">
        <p style="font-style:italic; font-weight: bold;">Вашият имейл адрес</p>
        <com:TTextBox ID="ContactDataEmail" Style="width: 360px; resize: none;" />
        <com:TEmailAddressValidator ValidationGroup="ContactGroup" ControlToValidate="ContactDataEmail" Text="Невалиден имейл адрес." />
    </div>
    <div class="contact-data__message">
        <p style="font-style:italic; font-weight: bold;">Вашето запитване*</p>
        <com:TTextBox TextMode="MultiLine" Rows="11" ID="ContactDataMessage" Style="width: 360px; resize: none;" />
        <com:TRequiredFieldValidator ValidationGroup="ContactGroup" ControlToValidate="ContactDataMessage" Text="Задължително поле." />
        <br>
        <com:TCustomValidator ValidationGroup="ContactGroup" ControlToValidate="ContactDataMessage" ClientValidationFunction="validateMinLength" Text="Запитването Ви трябва да е поне 20 символа." />
    </div>
</div>

<table width="100%" cellspacing="0" cellpadding="0" style="padding:10px; margin-top: 5px;">
    <tr>       
        <td colspan="2"  style="text-align:center">
            <span id="btn-send-contact-data">
                <com:TActiveLinkButton
                    ValidationGroup="ContactGroup"
                    CausesValidation="true"
                    ID="ButtonSendContactData"
                    CssClass="easyui-linkbutton"
                    Attributes.onClick="validateContactData()"
                    Text="Изпрати" >
                    <prop:ClientSide OnLoading="" OnComplete="" />
                </com:TActiveLinkButton>
            </span>

            <a id="btn" href="javaScript:void(0)" class="easyui-linkbutton" onClick="jQuery('#win-contact-form').window('close');" data-options="iconCls:'icon-cancel'">Затвори</a> 
        </td>
    </tr>
</table>