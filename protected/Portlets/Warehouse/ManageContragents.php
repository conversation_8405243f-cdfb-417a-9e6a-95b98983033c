<?php

class ManageContragents extends Portlet
{
    private $gridId = 'contragents-grid';
    private $toolbarId = 'contragents-toolbar';

    public function onInit($param)
    {
        parent::onInit($param);
    }

    public function getGridId()
    {
        return $this->gridId;
    }

    public function setGridId($val)
    {
        $this->gridId = $val;
    }

    public function getToolbarId()
    {
        return $this->toolbarId;
    }

    public function setToolbarId($val)
    {
        $this->toolbarId = $val;
    }
}
