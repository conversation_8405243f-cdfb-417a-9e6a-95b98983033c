<!--Add Warehouse Modal-->
<div id="win-add-edit-warehouses" class="easyui-window" title="Добавяне/редактиране на склад"
     data-options="iconCls:'icon-users', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
    <com:Application.Portlets.Warehouse.AddEditWarehouse />
</div>

<!--Manage Contragents Modal-->
<div id="win-manage-contragents" class="easyui-window" title="Управление на доставчици"
     data-options="iconCls:'icon-users', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
    <div style="height: 600px; width: 1150px;">
        <com:Application.Portlets.Warehouse.ManageContragents />
    </div>
</div>

<!--Manage Items Modal-->
<div id="win-manage-items" class="easyui-window" title="Управление на артикулите"
     data-options="iconCls:'icon-users', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
    <div style="height: 600px; width: 840px;">
        <com:Application.Portlets.Warehouse.ManageItems />
    </div>
</div>

<!--Manage Documents Modal-->
<div id="win-manage-documents" class="easyui-window" title="Управление на документите"
     data-options="iconCls:'icon-users', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
    <div style="height: 600px; width: 1110px;">
        <com:Application.Portlets.Warehouse.ManageDocuments />
    </div>
</div>

<!--Manage Measures Modal-->
<div id="win-manage-measures" class="easyui-window" title="Управление на мерки"
     data-options="iconCls:'icon-users', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
    <div style="height: 500px; width: 350px;">
        <com:Application.Portlets.Warehouse.ManageMeasures />
    </div>
</div>

<!--Manage User Permissions Modal-->
<div id="win-manage-user-permissions" class="easyui-window" title="Управление на правата за работа"
     data-options="iconCls:'icon-users', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
    <div style="height: 500px; width: 650px;">
        <com:Application.Portlets.Warehouse.ManageUserPermissions />
    </div>
</div>

<!--Manage Farms Modal-->
<div id="win-manage-farms" class="easyui-window" title="Управление на стопанствата в склада"
     data-options="iconCls:'icon-users', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
    <div style="height: 500px; width: 800px;">
        <com:Application.Portlets.Warehouse.ManageFarms />
    </div>
</div>

<!--Manage Machines Modal-->
<div id="win-manage-machines" class="easyui-window" title="Управление на активите в склада"
     data-options="iconCls:'icon-users', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
    <div style="height: 500px; width: 800px;">
        <com:Application.Portlets.Warehouse.ManageMachines />
    </div>
</div>

<!--Manage Farms Modal-->
<div id="win-manage-groups" class="easyui-window" title="Управление на групите"
     data-options="iconCls:'icon-users', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
    <div style="height: 500px; width: 500px;">
        <com:Application.Portlets.Warehouse.ManageGroups />
    </div>
</div>

<!--Sync TF farms Modal-->
<div id="win-sync-farms" class="easyui-window" title="Стопанства в Технофарм"
     data-options="iconCls:'icon-users', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
    <div style="height: 400px; width: 500px;">
        <com:Application.Portlets.Warehouse.SyncFarms />
    </div>
</div>

<!--Transaction Details Modal-->
<div id="win-transaction-details" class="easyui-window" title="Детайлна информация"
     data-options="iconCls:'icon-info', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
    <com:Application.Portlets.Warehouse.TransactionDetails />
</div>

<!--Add/Edit Items Modal-->
<div id="win-add-edit-item" class="easyui-window" title="Добавяне/редактиране на артикул"
     data-options="iconCls:'icon-users', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
    <com:Application.Portlets.Warehouse.AddEditItem />
</div>

<!--Add/Edit Contragents Modal-->
<div id="win-add-edit-contragent" class="easyui-window" title="Добавяне/редактиране на доставчик"
     data-options="iconCls:'icon-users', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
    <com:Application.Portlets.Warehouse.AddEditContragent />
</div>

<!--Add/Edit Farms Modal-->
<div style="width: 600px;" id="win-add-edit-farm" class="easyui-window" title="Добавяне/редактиране на стопанство"
     data-options="iconCls:'icon-users', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
    <com:Application.Portlets.Warehouse.AddEditFarms />
</div>

<!--Add/Edit Machines Modal-->
<div style="width: 600px;" id="win-add-edit-machine" class="easyui-window" title="Добавяне/редактиране на актив"
     data-options="iconCls:'icon-users', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true" >
    <com:Application.Portlets.Warehouse.AddEditMachines />
</div>

<!--Manage Measures Modal-->
<div id="win-manage-warehouse-fields" class="easyui-window" title="Управление на допълнителни полета"
     data-options="iconCls:'icon-users', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true">
    <div style="height: 500px; width: 350px;">
        <com:Application.Portlets.Warehouse.WarehouseFieldsEdit />
    </div>
</div>

<!--After save transaction  Modal-->
<div id="win-save-transaction-options" class="easyui-window" title="Успешен запис"
     data-options="iconCls:'icon-users', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:false">
    <div style="height: 80px; width: 450px;">
        <p style="text-align: center; margin: 20px 0 20px 0;">Транзакцията е завършена успешно.</p>
        <div style="text-align: center">
            <a id="newTransactionBtn" class="easyui-linkbutton" data-options="iconCls: 'icon-add'">Нова транзакция</a>
            <a id="newSimilarTransactionBtn" class="easyui-linkbutton" data-options="iconCls: 'icon-reload'">Създай подобна</a>
            <a id="goToHomepageBtn" class="easyui-linkbutton" data-options="iconCls: 'icon-cancel'">Върни се в началото</a>
        </div>
    </div>
</div>

<!--Add invoice to documents  Modal-->
<div id="win-add-invoice" class="easyui-window" title="Добави фактура"
     data-options="iconCls:'icon-users', closed: true, resizable: false, minimizable: false, collapsible: false, maximizable: false, modal: true, closable:true">
    <div style="height: 110px; width: 460px;">
        <div>
            <div style="padding:10px;">
                <table class="filters">
                    <tr>
                        <td>
                            <label for="invoice_number">Номер<span class="color-red">*</span></label>
                            <input id="invoice_number" type="text" class="easyui-textbox"/>
                        </td>
                        <td>
                            <label for="invoice_date">Дата<span class="color-red">*</span></label>
                            <input id="invoice_date" type="text" class="easyui-datebox">
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="action-btns">
        <span>
            <a id="addInvoiceBtn" href="javaScript:void(0)" class="easyui-linkbutton" data-options="iconCls:'icon-save'">Запази</a>
        </span>
            <span>
            <a href="javaScript:void(0)" class="easyui-linkbutton closeBtn" data-options="iconCls:'icon-cancel'">Откажи</a>
        </span>
        </div>

    </div>
</div>