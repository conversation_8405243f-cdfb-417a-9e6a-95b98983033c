<div title="Склад параметри" data-options="closable:false" style="">
    <fieldset style="border: 1px solid #000; margin: 7px 5px;">
        <legend style="font-style: italic; font-weight: bold; margin-left: 10px">Конфигурационни параметри</legend>
        <div style="float: left; padding: 10px; max-width: 320px;">
            <table class="filters" cellspacing="0" cellpadding="0" border="0">

                <tr id="warehouse-auto-generate-codes">
                    <td style="text-align:right;"><b>Автоматично генериране на файлове</b></td>
                    <td nowrap>
                        <span id="yes-warehouse-auto-generate-codes">
                            <label for="AutoGenerateCodes">&nbsp;Да</label>
                            <input type="radio" ID="AutoGenerateCodes" name="AutoGenerateCodes"/>
                        </span>
                        <span id="no-warehouse-auto-generate-codes">
                            <label for="NoAutoGenerateCodes">&nbsp;Не</label>
                            <input type="radio" ID="NoAutoGenerateCodes" name="AutoGenerateCodes" Checked="true"/>
                        </span>
                    </td>
                </tr>

                <tr id="warehouse-item-with-warehouse">
                    <td style="text-align:right;"><b>Въвеждане на склад при добавяне на артикул</b></td>
                    <td nowrap>
                        <span id="yes-warehouse-item-with-warehouse">
                            <label for="ItemWithWarehouse">&nbsp;Да</label>
                            <input type="radio" ID="ItemWithWarehouse" name="ItemWithWarehouse"/>
                        </span>
                        <span id="no-warehouse-item-with-warehouse">
                            <label for=" NoItemWithWarehouse">&nbsp;Не</label>
                            <input type="radio" ID="NoItemWithWarehouse" name="ItemWithWarehouse" Checked="true"/>
                        </span>
                    </td>
                </tr>

                <tr id="warehouse-allow-edit-transactions">
                    <td style="text-align:right;"><b>Позволяване на промяна на операция</b></td>
                    <td nowrap>
                        <span id="yes-warehouse-allow-edit-transactions">
                            <label for="AllowEditTransactions">&nbsp;Да</label>
                            <input type="radio" ID="AllowEditTransactions" name="AllowEditTransactions"/>
                        </span>
                        <span id="no-warehouse-allow-edit-transactions">
                            <label for=" NoAllowEditTransactions">&nbsp;Не</label>
                            <input type="radio" ID="NoAllowEditTransactions" name="AllowEditTransactions" Checked="true"/>
                        </span>
                    </td>
                </tr>

                <tr id="warehouse-allow-warehouse-sum-quantities">
                    <td style="text-align:right;"><b>Позволяване на сумиране на количества</b></td>
                    <td nowrap>
                        <span id="yes-warehouse-allow-warehouse-sum-quantities">
                            <label for="AllowWarehouseSumQuantities">&nbsp;Да</label>
                            <input type="radio" ID="AllowWarehouseSumQuantities" name="AllowWarehouseSumQuantities"/>
                        </span>
                        <span id="no-warehouse-allow-warehouse-sum-quantities">
                            <label for=" NoAllowWarehouseSumQuantities">&nbsp;Не</label>
                            <input type="radio" ID="NoAllowWarehouseSumQuantities" name="AllowWarehouseSumQuantities" Checked="true"/>
                        </span>
                    </td>
                </tr>
            </table>
        </div>
    </fieldset>
</div>

<table width="100%" cellspacing="0" cellpadding="0" style="margin: 10px 0;" border="0">
    <tr>
        <td style="text-align:center;">
            <a id="btn-save-warehouse-config-params" href="javaScript:void(0)" class="easyui-linkbutton"
               data-options="iconCls:'icon-save'">Запази</a>
            <a href="javaScript:void(0)" class="easyui-linkbutton"
               onClick="jQuery('#win-edit-warehouse-config-params').window('close');"
               data-options="iconCls:'icon-cancel'">Откажи</a>
        </td>
    </tr>
</table>