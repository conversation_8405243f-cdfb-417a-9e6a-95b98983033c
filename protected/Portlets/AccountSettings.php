<?php

use TF\Engine\Plugins\Core\Users\UsersController;

class AccountSettings extends Portlet
{
    public function onInit($params)
    {
        parent::onInit($params);

        // $this->Page->initController('Plugins.Core.Users');
        $this->Page->UsersController = new UsersController();
    }

    public function editAccountSettings($sender, $params)
    {
        if ($this->User->isGuest) {
            return [];
        }
        $options = [
            'mainData' => [
                'map_type' => (int) $this->AccountSettingsMap->SafeText,
            ],
            'where' => [
                'user_id' => ['column' => 'id', 'compare' => '=', 'value' => $this->User->UserID],
            ],
        ];

        $this->Page->UsersController->updateUsersData($options);
        $_SESSION['options']['map'] = $this->AccountSettingsMap->SafeText;
        $this->Page->UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, 'Account settings', 'Settings edited.', DEFAULT_DB_PREFIX . 'users', [$this->User->UserID]);
    }
}
