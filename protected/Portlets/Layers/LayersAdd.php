<?php
/**
 * Add class file.
 *
 * <AUTHOR>
 */

use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Add class.
 *
 * Add page of Users plugin.
 */
class LayersAdd extends Portlet
{
    /**
     * onInit event.
     */
    public function onInit($param)
    {
        parent::onInit($param);
    }

    /**
     * Moves to the trash the items that were selected and fills the grid after that.
     */
    public function deleteSelectedFilesClicked($sender, $params)
    {
        $data = $params->CallbackParameter;

        for ($i = 0;$i < count($data);$i++) {
            $arrayID[] = $data[$i]->id;
        }

        if (!count($arrayID)) {
            return;
        }

        $this->Page->LayersController = new LayersController();
        $this->Page->UsersController = new UsersController();

        $this->Page->LayersController->deleteLayersItems($arrayID, $this->User->GroupID, $this->User->Database);

        // $this->Page->LayersController->log(1, $this->User->Name.", files", LoggerMessages::ITEM_DELETE, $arrayID);
        $this->Page->UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, 'Files', ITEM_DELETE, DEFAULT_DB_PREFIX . 'users_files', $arrayID);
    }
}
