<div>
	<fieldset style="float: left; width: 300px; height: 205px; border:1px solid #000; margin: 10px 0px 10px 10px;">
		<legend style="font-weight: bold; font-style: italic; margin-left: 10px;">Основна информация</legend>
		<div style="font-size: 12px; padding: 5px;">
			При извършване на мултиредакция е важно да изберете годината/годините, както и коя култура ще бъде променена с друга култура. При задаване на очакван добив е важно да се знае, че редакцията ще промени очаквания добив, само за избраните парцели, спрямо указаната година или години и при съответните култури обект на промяна. Ако не се уточни изрично количество, то софтуерът ще запази добивите, такива каквито са били до момента на промяната.
		</div>
	</fieldset>	

	<fieldset style="float: left; width: 300px; height: 205px; border:1px solid #000; margin: 10px 0px 10px 10px;">
		
		<table width="100%" class="filters" cellspacing="0" cellpadding="0" style="padding:10px 10px 0 10px; margin-left: 5px; margin-top: 5px">
			<legend style="font-weight: bold; font-style: italic; margin-left: 10px;">Параметри</legend>
			<tr>
				<td>Година</td>
				<td id="multi-edit-year" colspan="2">
					<input type="text" style="width: 150px;" />
				</td>
			</tr>
			<tr>
				<td>Стара култура</td>
				<td id="multi-edit-old-crop" colspan="2">
					<input type="text" style="width: 150px;" />
				</td>
			</tr>
			<tr>
				<td >Нова култура</td>
				<td id="multi-edit-new-crop" colspan="2">
					<input type="text" style="width: 150px;" />
				</td>
			</tr>
			<tr>
				<td>Очакван добив</td>
				<td id="multi-edit-dobiv" colspan="2">
					<input type="text" style="width: 150px;" />
				</td>
			</tr>
		</table>
	</fieldset>	
	
	<div style="clear: both"></div>
	<table width="100%" cellspacing="0" cellpadding="0">
	    <tr>       
	        <td colspan="2" style="text-align:left;">
	            <a id="btn-multiedit" style="margin: 5px 0 0 245px;" href="javaScript:void(0)" class="easyui-linkbutton" onClick="executeMultiEdit();" data-options="iconCls:'icon-save'">Запази</a> 
	            <a id="btnclose" style="margin-bottom: -5px;" href="javaScript:void(0)" class="easyui-linkbutton" onClick="jQuery('#multiedit-window').window('close');" data-options="iconCls:'icon-cancel'">Откажи</a> 
	        </td>
	    </tr>
	</table>
</div>
