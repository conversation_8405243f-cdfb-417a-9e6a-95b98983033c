<com:TActiveHiddenField ID="SelectedUserID" Value="" />
<com:TActivePanel ID="AddEkatteFromGridPanel">
	<table width="100%" class="filters" cellspacing="0" cellpadding="0" style="padding:10px 10px 0 10px;">
		<tr>
			<td style="text-align: right; width: 80px;">Област</td>
			<td id="oblasti">
				<com:TTextBox ID="Oblasti" Style="width: 210px;" />
			</td>
		</tr>
		<tr>
			<td style="text-align:right">Община</td>
			<td id="obshtini">
				<com:TTextBox ID="Obshtini" Style="width: 210px;" />
			</td>
		</tr>
		<tr>
			<td style="text-align:right">Кметство</td>
			<td id="kmetstva">
				<com:TTextBox ID="Kmetstva" Style="width: 210px;" />
			</td>
		</tr>
		<tr>
			<td style="text-align:right">ЕКАТТЕ</td>
			<td id="ekatte">
				<com:TTextBox ID="Ekatte" Style="width: 210px;" />
			</td>
		</tr>
	</table>
</com:TActivePanel>

<table width="100%" cellspacing="0" cellpadding="0" style="padding:10px;">
    <tr>       
        <td colspan="2"  style="text-align:center;">
            <span id="add-ekatte-button">
                <com:TActiveLinkButton 
                    ID="AddButton"
                    OnClick="AddButtonClicked"
                    CssClass="easyui-linkbutton"
                    Attributes.onClick="return validateEkatteAdd();"
                    Attributes.style="margin: 0 0 0 43px"
                    Text="Запази" 
                    >
                    <prop:ClientSide OnLoading="" OnComplete="jQuery('#win-ekatte-add').window('close'); jQuery('#ekatte-tables').datagrid('reload');" />
                </com:TActiveLinkButton>
            </span>
            <a id="btnclose" href="javaScript:void(0)" class="easyui-linkbutton" onClick="jQuery('#win-ekatte-add').window('close');" data-options="iconCls:'icon-cancel'">Откажи</a> 
        </td>
    </tr>
</table>