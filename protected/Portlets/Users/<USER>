<?php
/**
 * Add class file.
 *
 * <AUTHOR>
 */

/*
 * Add class
 *
 * Add page of Ekatte plugin.
 */

use TF\Engine\Plugins\Core\Users\UsersController;

class AddEkatte extends Portlet
{
    private $UsersController = false;

    /**
     * onInit event.
     */
    public function onInit($param)
    {
        parent::onInit($param);

        $this->UsersController = new UsersController('Users');
    }

    public function getRequiredAddData()
    {
        return [];
    }

    public function AddButtonClicked()
    {
        $this->UsersController->addNewUserEkatteRelation($this->SelectedUserID->Value, $this->Ekatte->Text, $this->Oblasti->Text);
    }

    public function setUserID($sender, $param)
    {
        $this->SelectedUserID->Value = $param->CallbackParameter;
    }
}
