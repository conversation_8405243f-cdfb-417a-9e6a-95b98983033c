<?php

use TF\Engine\Plugins\Core\Users\UsersController;

class EkatteGrid extends Portlet
{
    private $UsersController = false;

    public function onInit($param)
    {
        parent::onInit($param);

        $this->UsersController = new UsersController('Users');
    }

    public function deleteSelectedEkatte($sender, $params)
    {
        if ($this->User->isGuest) {
            return [];
        }

        $data = $params->CallbackParameter;
        $id_array = [];
        for ($i = 0; $i < count($data[0]); $i++) {
            $id_array[] = $data[0][$i]->id;
        }

        $selectedUserID = $data[1];
        $this->UsersController->deleteEkatteRelation($selectedUserID, $id_array);
    }
}
