<div style="padding: 5px;">
    <div style=" margin-bottom: 10px; float: left;">
        ЕКАТТЕ <input id="choose-payroll-ekate" style="width: 98px; color:red">
        <span style="margin: 0px 0px 0px 5px; float: left;"></span>
    </div>

    <div style="margin-bottom: 10px;">
        Стопанство:
        <input id="choose-payroll-farming" style="width: 98px;">
    </div>
    <div id="ekatteFilterMsg" style="display: none; margin-bottom: 10px;">
        В случай, че собственикът притежава имоти в различни землища, то поле Платена рента ще визуализира плащанията по договорите за всички ЕКАТТЕ към посочения период, а поле Начислена рента - данни за начисления само за избраното землище.
    </div>

    <fieldset style="margin-top: 0px; padding: 5px 10px 5px 10px;">
        <legend style="font-weight: bold; font-style: italic;">Информация за собственик</legend>
        <div>
            Тип:<br/>
            <input type="text" id="search-payroll-owner-type" style="width: 242px"/>
            <span style="margin: 0px 7px 0px 5px; float: left;"></span>
        </div>
        <div style="margin-top: 5px;">
            <div style="float: left;">
                Имена:
                <input type="text" id="search-payroll-owner-name" style="width: 130px"/>
                <span style="margin: 0px 7px 0px 5px; float: left;"></span>
            </div>
            <div>
                ЕГН:
                <input type="text" id="search-payroll-owner-egn" style="width: 75px"/>
                <a href="javaScript:void(0)" class="easyui-linkbutton" onclick="initPayrollEGNFilters()" data-options="iconCls:'icon-add'"></a>
            </div>
        </div>
        <div style="margin-top: 5px;">
            Място за получаване:
            <input type="text" id="search-payroll-owner-renta-place" style="width: 167px"/>
        </div>
    </fieldset>
    <fieldset style="margin-top: 5px; padding: 5px 10px 5px 10px;">
        <legend style="font-weight: bold; font-style: italic;">Информация за представител</legend>
        <div style="float: left;">
            Имена:
            <input type="text" id="search-payroll-represent-name" style="width: 130px"/>
            <span style="margin: 0px 7px 0px 5px; float: left;"></span>
        </div>
        <div>
            ЕГН:
            <input type="text" id="search-payroll-represent-egn" style="width: 75px"/>
        </div>
        <div style="margin-top: 5px;">
            Място за получаване:
            <input type="text" id="search-payroll-represent-renta-place" style="width: 167px"/>
        </div>
    </fieldset>
    <fieldset style="border: 1px solid #000; padding: 5px 10px 5px 10px;">
        <legend style="font-weight: bold; font-style: italic;">Информация за наследник</legend>
        <div style="float: left;">
            Имена:
            <input type="text" id="search-payroll-heritor-name" style="width: 130px"/>
            <span style="margin: 0px 7px 0px 5px; float: left;"></span>
        </div>
        <div>
            ЕГН:
            <input type="text" id="search-payroll-heritor-egn" style="width: 75px"/>
        </div>
    </fieldset>
    <fieldset style="border: 1px solid #000; padding: 3px 10px 5px 10px; margin-top: 5px;">
        <legend style="font-weight: bold; font-style: italic;">Информация за фирма</legend>
        <div style="float: left;">
            Фирма:
            <input type="text" id="search-payroll-company-name" style="width: 130px"/>
            <span style="margin: 0px 7px 0px 5px; float: left;"></span>
        </div>
        <div>
            ЕИК:
            <input type="text" id="search-payroll-company-eik" style="width: 75px"/>
            <a href="javaScript:void(0)" class="easyui-linkbutton" onclick="initPayrollEIKFilters()" data-options="iconCls:'icon-add'"></a>
        </div>
    </fieldset>
</div>

<table width="100%" cellspacing="0" cellpadding="0" style="margin: 5px 0;">
    <tr>
        <td colspan="2"  style="text-align:center;">
			<a href="javaScript:void(0)" class="easyui-linkbutton" onclick="filterOwnersPayroll(); jQuery('#win-payroll-filter').window('close');"
																										 data-options="iconCls:'icon-search'">Търси</a>
            <a href="javaScript:void(0)" class="easyui-linkbutton" onclick="jQuery('#win-payroll-filter').window('close');"
            																							 data-options="iconCls:'icon-cancel'">Откажи</a>
        </td>
    </tr>
</table>
