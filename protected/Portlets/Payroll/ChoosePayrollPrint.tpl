<fieldset style="width: 300px; border:1px solid #000; margin: 10px; padding: 10px;">
	<legend style="font-weight: bold; font-style: italic;">Начин на отпечатване</legend>
	<table width="100%" class="filters" cellspacing="0" cellpadding="0" style="">
		<tr>
			<td>
				<input type="radio" name="print-payroll" id="summary-payroll" value="summary" checked>
				<label for="summary-payroll">Сумарно (без починалите)</label>
			</td>
		</tr>
			<tr>
			<td>
				<input type="radio" name="print-payroll" id="owners-payroll" value="owners">
				<label for="owners-payroll">Сумарно (без наследници)</label>
			</td>
		</tr>
		<!-- <tr>
			<td>
				<input type="radio" name="print-payroll" id="detailed-payroll" value="detailed">
				<label for="detailed-payroll">Разделно (без починалите)</label>
			</td>
		</tr> -->
	</table>
</fieldset>
<fieldset style="width: 300px; border:1px solid #000; margin: 10px; padding: 10px;">
	<legend style="font-weight: bold; font-style: italic;">Разделяне на натури</legend>
	<table width="100%" class="filters" cellspacing="0" cellpadding="0" style="">
		<tr>
			<td>
				<input type="checkbox" id="export-separated-naturas-chkbox" checked style="vertical-align: middle;">
				<label for="export-separated-naturas-chkbox">Разделяне на натури в отделни колони</label>
			</td>
		</tr>
	</table>
</fieldset>
<fieldset style="width: 300px; border:1px solid #000; margin: 10px; padding: 10px;">
	<legend style="font-weight: bold; font-style: italic;">Допълнителни колони</legend>

	<fieldset style="border: 1px solid #000; margin-bottom: 10px; padding: 2px 0px 2px 10px;">
		<legend style="margin-left: 10px; font-style: italic; font-weight: bold;">Информация</legend>
		<p>За да премахнете колони от експорта е необходимо да ги преместите в десния списък.</p>
	</fieldset>
	<div>
		<div style="float: left;">
			<p style="text-align: center; font-weight: bold;">Включени колони</p>
			<div id="payroll-export-drop-source" style="border:1px solid #ccc;width:130px;height:180px;float:left; padding: 5px; overflow:auto">
			    <div data-position="1" data-column-name="Телефон на собственик" class="drag dragable-el-backg">Телефон на собственик</div>
			    <div data-position="1" data-column-name="Мобилен на собственик" class="drag dragable-el-backg">Мобилен на собственик</div>
			    <div data-position="1" data-column-name="Договори" class="drag dragable-el-backg">Договори</div>
			    <div data-position="3" data-column-name="Имоти" class="drag dragable-el-backg">Имоти</div>
			    <div data-position="2" data-column-name="Представител по договор" class="drag dragable-el-backg">Представител по договор</div>
			    <div data-position="4" data-column-name="Рента в лева по договор (сума)" class="drag dragable-el-backg">Рента в лева по договор (сума)</div>
			    <div data-position="5" data-column-name="Начислена рента в лева (сума)" class="drag dragable-el-backg">Начислена рента в лева (сума)</div>
			    <div data-position="6" data-column-name="Рента в натура по договор (общо)" class="drag dragable-el-backg">Рента в натура по договор (общо)</div>
			    <!-- <div data-position="7" data-column-name="Начислена рента в натура (общо)" class="drag dragable-el-backg">Начислена рента в натура (общо)</div> -->
			    <div data-position="8" data-column-name="Платена рента в лева (сума)" class="drag dragable-el-backg">Платена рента в лева (сума)</div>
			    <div data-position="9" data-column-name="Платена рента в лева чрез" class="drag dragable-el-backg">Платена рента в лева чрез</div>
			    <!-- <div data-position="10" data-column-name="Платена рента в натура (общо)" class="drag dragable-el-backg">Платена рента в натура (общо)</div> -->
			    <div data-position="11" data-column-name="Платена рента в натура чрез" class="drag dragable-el-backg">Платена рента в натура чрез</div>
			    <!-- <div data-position="3" data-column-name="Платена рента в натура детайлно" class="drag dragable-el-backg">Платена рента в натура детайлно</div> -->
			    <div data-position="4" data-column-name="Остатък в лева" class="drag dragable-el-backg">Остатък в лева</div>
			    <div data-position="5" data-column-name="Остатък в натура" class="drag dragable-el-backg">Остатък в натура</div>
			    <div data-position="6" data-column-name="Остатък в натура по ед.ст." class="drag dragable-el-backg">Остатък в натура по ед.ст.</div>
			    <div data-position="7" data-column-name="Надплатена рента в лева" class="drag dragable-el-backg">Надплатена рента в лева</div>
			    <div data-position="8" data-column-name="Надплатена рента в натура" class="drag dragable-el-backg">Надплатена рента в натура</div>
			    <!-- <div data-position="9" data-column-name="Общо платена рента в лева" class="drag dragable-el-backg">Общо платена рента в лева</div> -->
			    <!-- <div data-position="10" data-column-name="Общо платена рента в натура" class="drag dragable-el-backg">Общо платена рента в натура</div> -->
			    <div data-position="11" data-column-name="Място за получаване на рента" class="drag dragable-el-backg">Място за получаване на рента</div>
				<!-- <div data-position="12" data-column-name="Информация от служба по вписвания" class="drag dragable-el-backg">Информация от служба по вписвания</div> -->
				<div data-position="13" data-column-name="IBAN" class="drag dragable-el-backg">IBAN</div>
				<div data-position="14" data-column-name="Площ за лично ползване (дка)" class="drag dragable-el-backg">Площ за лично ползване (дка)</div>
			</div>
		</div>
		<div style="float: right;">
			<p style="text-align: center; font-weight: bold;">Изключени колони</p>
			<div id="payroll-export-drop-target" style="border:1px solid #ccc;width:130px;height:180px;float:left; margin-left: 12px; padding: 5px; overflow:auto">
			</div>
		</div>
		<div style="clear:both"></div>
	</div>
</fieldset>
<table width="100%" cellspacing="0" cellpadding="0" style="margin-bottom: 10px;">
	<tr>       
		<td colspan="2" style="text-align:center;">
			<a href="javascript:void(0)" id="print_export_payroll" class="easyui-linkbutton">Отпечатай</a>
			<a href="javaScript:void(0)" class="easyui-linkbutton" onClick="jQuery('#win-choose-payroll-print').window('close');" data-options="iconCls:'icon-cancel'">Откажи</a> 
		</td>
	</tr>
</table>