<?php

use TF\Engine\Plugins\Core\UserDb\UserDbController;

class SetContractOwnerData extends Portlet
{
    public $UserDbController = false;

    public function onInit($param)
    {
        parent::onInit($param);
    }

    public function saveRep($sender, $params)
    {
        $this->UserDbController = new UserDbController($this->User->Database);
        $data = $params->CallbackParameter;
        if (!$this->UserDbController->StringHelper->validateEgn($data->rep_egn)) {
            throw new Exception('Невалидно ЕГН: ' . $data->rep_egn);
        }

        $options = [
            'tablename' => $this->UserDbController->DbHandler->tableOwnersReps,
            'mainData' => [
                'rep_name' => $data->rep_name,
                'rep_surname' => $data->rep_surname,
                'rep_lastname' => $data->rep_lastname,
                'rep_egn' => $data->rep_egn,
                'rep_lk' => $data->rep_lk,
                'rep_lk_izdavane' => $data->rep_lk_izdavane,
                'rep_address' => $data->rep_address,
            ],
        ];

        if (!$data->id) {
            $item_id = $this->UserDbController->addItem($options);

            $this->Page->UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, 'Plots', ITEM_ADD, $options['tablename'], [$item_id]);
        } else {
            $options['where'] = ['id' => $data->id];
            $this->UserDbController->editItem($options);

            $this->Page->UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, 'Plots', ITEM_EDIT, $options['tablename'], [$data->id]);
        }
    }

    public function deleteRep($sender, $params)
    {
        if ($this->User->isGuest) {
            return [];
        }

        $this->UserDbController = new UserDbController($this->User->Database);

        $data = $params->CallbackParameter;
        $id_array[] = $data->id;
        $options = [
            'tablename' => $this->UserDbController->DbHandler->tableOwnersReps,
            'id_string' => implode(', ', $id_array),
        ];

        if ($data->id) {
            $this->UserDbController->deleteItemsByParams($options);

            $this->Page->UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, 'Plots', ITEM_DELETE, $options['tablename'], [$data->id]);
        }
    }
}
