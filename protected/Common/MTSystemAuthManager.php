<?php

namespace TF\Application\Common;

use League\OAuth2\Client\Token\AccessTokenInterface;
use TF\Application\Common\Interfaces\SystemAuthProviderInterface;

class MTSystemAuthManager
{
    private $authProvider;

    public function __construct(SystemAuthProviderInterface $authProvider)
    {
        $this->authProvider = $authProvider;
    }

    public function introspectToken(string $accessToken)
    {
        return $this->authProvider->introspectToken($accessToken);
    }

    public function getToken(): AccessTokenInterface
    {
        return $this->authProvider->getToken();
    }
}
