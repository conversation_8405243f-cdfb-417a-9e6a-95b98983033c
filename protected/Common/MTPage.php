<?php

namespace TF\Application\Common;

use Plugins\Core\UserDb;
use Prado\Prado;
use Prado\Web\UI\ActiveControls\TCallbackEventParameter;
use Prado\Web\UI\TPage;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * MTpage class file.
 *
 * <AUTHOR>
 */

/**
 * MTpage class.
 *
 * MTpage, each page class extend this class
 * MTpage extend TPage
 * PHP extension.
 */
class MTPage extends TPage
{
    public $ActivePlugins = [];
    public $UsersController;
    public $LayersController;
    public $FarmingController;
    public $OwnersController;
    public $ContractsController;
    public $UserDbController;
    public $UserDbOwnersController;
    public $UserDbContractsController;
    public $UserDbPlotsController;
    public $UserDbCropRotationController;
    public $UserDbOverlapsController;
    public $UserDbZPlotsController;
    public $UserDbIsakController;
    public $UserDbAgreementsController;
    public $UserDbMapController;
    public $UserDbPaymentsController;
    public $UserDbCoverageController;
    public $UserDbSubleasesController;
    public $UserDbDiaryController;

    /**
     * Returns an array with the place.
     *
     * @param string $where
     * @param string $grid
     *
     * @return array
     */
    public function getPlace($where, $grid)
    {
        return [
            'place' => $where,
            'grid' => $grid,
            'page' => $this->getPagePath(),
        ];
    }

    /**
     * Initializes a Controller by plugin path.
     *
     * @param string $plugin - full path to plugin ('Plugins.Extended.Pages')
     */
    //     public function initDbController($database) {
    //
    //         if (!isset($this->UserDbController)) {
    //             Prado::using('Plugins.Core.UserDb.*');
    //             Prado::using('Plugins.Core.UserDb.conf');
    //             $this->UserDbController = new UserDbController($database);
    //         }
    //     }

    public function initDbController($type = '')
    {
        // adding the old init so the old components keep working
        // TODO: remove when separation is over
        if (false !== strpos($type, 'db_')) {
            //    		Prado::using('Plugins.Core.UserDb.*');
            //    		Prado::using('Plugins.Core.UserDb.conf');
            $this->UserDbController = new UserDbController($this->User->Database);
        } else {
            $controller = 'UserDb' . $type . 'Controller';
            if (!isset($this->{$controller}) || false === $this->{$controller}) {
                // add the default userDb conf if main UserDb is requested
                if ('UserDbController' == $controller) {
                    //	    			Prado::using('Plugins.Core.UserDb.conf');
                }
                //	    		Prado::using('Plugins.Core.UserDb'.$type.'.*');
                $controllerClass = Config::$NAMESPACES[$controller];
                $this->{$controller} = new $controllerClass($this->User->Database);
            }
        }
    }

    /**
     * @param null $plugin
     *
     * @throws \Prado\Exceptions\TInvalidDataValueException
     *
     * @return string
     */
    public function initController($plugin = null)
    {
        preg_match('/^(.*)\.([^\.]+)$/', $plugin, $split);
        $pluginPath = $split[1];
        $pluginName = $split[2];
        $controllerName = $pluginName . 'Controller';

        if (!isset($this->$controllerName) && $pluginName) {
            Prado::using($plugin . '.*');
            $this->$controllerName = new $controllerName($pluginName);
            $this->ActivePlugins[] = ['title' => $pluginName];
        }

        return $controllerName;
    }

    /**
     * Validates security image.
     *
     * @param TServerValidateEventParameter $param
     */
    public function securityImageServerValidate($sender, $param)
    {
        if ($param->Value != $_SESSION['security_code']) {
            $param->IsValid = false;
        }
    }

    /**
     * Gets Ids of all selected items on grid.
     *
     * @param mixed $grid, instance of grid
     *
     * @return array
     */
    public function getSelectedGridItems($grid)
    {
        $arrayID = [];
        $items = $grid->getItems();
        foreach ($items as $item) {
            if ('true' == $item->chId->Checked) {
                $id = intval($item->chId->CustomData);
                $arrayID[] = $id;
            }
        }

        return $arrayID;
    }

    /**
     * Is called when clear button for data grid is pressed.
     */
    public function searchButtonClicked($sender, $params)
    {
        if ('ClearSearchButton' == $sender->ID) {
            $this->Keywords->Text = '';
        }
    }

    /**
     * renderGrid after ajax callback.
     */
    public function renderActiveGrid($sender, $param)
    {
        if ($param instanceof TCallbackEventParameter) {
            $this->ActivePanel->render($param->getNewWriter());
        }
    }

    public function setOrderLink($sender)
    {
        $orderby = $_SESSION['backend'][$this->getPagePath()][$sender->ID]['orderby'];
        $ordertype = $_SESSION['backend'][$this->getPagePath()][$sender->ID]['ordertype'];
        if ($sender->Header) {
            $buttons = $sender->Header->findControlsByType('TActiveLinkButton');

            foreach ($buttons as $button) {
                if ($button->CustomData == $orderby) {
                    $button->CssClass = 'ASC' == $ordertype ? 'bold white sort-asc' : 'bold white sort-desc';
                }
            }
        }
    }

    public function onPreLoad($param)
    {
        parent::onPreLoad($param);
        $this->loadJSFilesForPageFromJSON();
    }

    /**
     * Deny request.
     */
    protected function DenyRequest()
    {
        $this->Application->getResponse()->setStatusCode(401);
        $this->Application->completeRequest();
    }

    protected function loadJSFilesForPageFromJSON()
    {
        $jsonData = file_get_contents(SITE_PATH . 'jsSources.json');
        $jsonData = json_decode($jsonData, true);

        $pagePath = $this->getPagePath();
        $modifiedArray = [];

        $pageName = substr($pagePath, 0, -5);
        $pageName = $pageName ? $pageName : $pagePath;
        $jsSourceFiles = $jsonData[$pageName]['src'];

        if (Config::STATE_DEBUG == Prado::getApplication()->getMode()) {
            for ($i = 0; $i < count($jsSourceFiles); $i++) {
                $modifiedArray[$i] = substr($jsSourceFiles[$i], 7);
            }
        } else {
            $file = $jsonData[$pageName]['fileName'] . '.min.js';
            $dest = substr($jsonData[$pageName]['dest'], 7);
            $time = filemtime(PUBLIC_PATH . $dest . $file);
            $modifiedArray[0] = $dest . $file . '?ver=' . $time;
        }

        $this->Repeater->DataSource = $modifiedArray;
        $this->Repeater->dataBind();
    }
}
