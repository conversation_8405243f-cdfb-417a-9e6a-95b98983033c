<?php

namespace TF\Application\Common;

use Exception;
use TF\Engine\APIClasses\Login\LoginForm;
use TF\Engine\Kernel\KeycloakToken;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Users\UsersModel;

/**
 * MTKeycloakUserManager class.
 *
 * Extends MTUserManager.
 * This class performs user authentication by connecting to the database.
 * If given username and password match, user data is stored in the session
 * using MTUser class.
 */
class MTKeycloakUserManager extends MTUserManager
{
    /**
     * Performs DB check for username and password and returns result.
     *
     * @param string $username
     * @param string $password
     */
    public function validateUser($username, $password): bool
    {
        // creating the user model instance
        $dbHandler = new UsersModel($GLOBALS['Users']['tableName'], $GLOBALS['Users']['fieldName']);
        // checking if login info is correct
        $row = $dbHandler->getUserLogin($username);
        // checking the info
        if ($row) {
            // check if account is active
            if (!$row['active']) {
                throw new MTRpcException('Account is not active', LoginForm::ACCOUNT_IS_NOT_ACTIVE);
            }

            if (3 == $row['level']) {
                // prepare options to get parent account data
                $options = [
                    'where' => [
                        'id' => ['column' => 'id', 'compare' => '=', 'value' => $row['group_id']],
                    ],
                ];

                $results = $dbHandler->getUsers($options, false, false);
                $parent = $results[0];
            } else {
                $parent = $row;
            }

            // check and update parent data if necessary
            $inactivate = false;

            if ($parent['date_flag']) {
                if (strtotime($parent['due_date']) <= strtotime(date('Y-m-d'))) {
                    $inactivate = true;
                }
            }

            if ($parent['entry_flag']) {
                if ($parent['entries_left'] <= 0) {
                    $inactivate = true;
                } else {
                    // prepare options for main group user update( entries_left - 1 )
                    $options = [
                        'mainData' => [
                            'entries_left' => $parent['entries_left'] - 1,
                        ],
                        'where' => [
                            'id' => ['column' => 'id', 'compare' => '=', 'value' => $row['group_id']],
                        ],
                    ];

                    $dbHandler->updateUsersData($options);
                }
            }

            // inactivate group if necessary
            if ($inactivate) {
                // prepare update array
                $options = [
                    'mainData' => [
                        'active' => false,
                    ],
                    'where' => [
                        'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $parent['id']],
                    ],
                ];

                $dbHandler->updateUsersData($options);

                return false;
            }

            return ($row['username'] = $username && $row['password']);
        }

        return false;
    }

    /**
     * Load user from token.
     *
     * @param [string] $accessToken
     */
    public function decodeToken(string $accessToken): MTUser
    {
        try {
            $token = new KeycloakToken();

            if ($decoded = $token->decode($accessToken)) {
                return $this->getUser($decoded['preferred_username']);
            }
        } catch (Exception $ex) {
            throw new MTRpcException('ACCESS_TOKEN_ERROR', -34056);
        }
    }
}
