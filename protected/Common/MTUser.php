<?php

namespace TF\Application\Common;

use League\OAuth2\Client\Token\AccessTokenInterface;
use Prado\Security\IUser;
use Prado\Security\IUserManager;
use Prado\TComponent;
use Prado\TPropertyValue;
use TF\Application\Entity\ObjectPermissions;
use TF\Application\Entity\UserFarmings;
use TF\Application\Providers\ObjectPermissionsProvider;

/**
 * MTUser class file.
 *
 * <AUTHOR> Ltd. <<EMAIL>>
 *
 * @link http://www.devision.bg/
 *
 * @copyright Copyright &copy; 2008 Devision Ltd.
 */

/**
 * MTUser class.
 *
 * Extends TComponent and implements IUser.
 * This class stores logged user's data in session.
 * This class is used by MTUserManager.
 *
 * <AUTHOR> Ltd. <<EMAIL>>
 */
class MTUser extends TComponent implements IUser
{
    private $_manager;
    private $_name;
    private $_fullname;
    private $_userID;
    private $_database;
    private $_isGuest = true;
    private $_isSuperAdmin = false;
    private $_parentID;
    private $_userLevel;
    private $_groupID;
    private $_hasMapRightsR = true;
    private $_hasMapRightsRW = false;
    private $_hasPlotRightsR = false;
    private $_hasPlotRightsRW = false;
    private $_hasSubsidyRights = false;
    private $_hasSubsidyRightsRW = false;
    private $_hasAgroRights = false;
    private $_hasAgroRightsRW = false;
    private $_hasSatelliteRights = false;
    private $_hasSatelliteRightsRW = false;
    private $_hasSalesContractsRightsR = false;
    private $_hasSalesContractsRightsRW = false;
    private $_hasGeoscanMapRights = false;
    private $_hasContractsOwnWriteRights = false;
    private $_hasEquityRights = false;
    private $_hasHypothecsRightsR = false;
    private $_hasHypothecsRightsRW = false;
    private $_hasThematicMapsRightsR = false;
    private $_hasThematicMapsRightsRW = false;
    private $_hasCollectionsRights = false;
    private $_hasCollectionsRightsRW = false;
    private $_hasCadastreRights = false;
    private $_hasSlopeRights = false;
    private $_isTrial = false;
    private $_paidSupportYear;
    private $_fullPaidSupportYear;
    private $_appVersion;
    private $_appCriticalUpd = false;
    private $_email = '';
    private $_hasDashboardRights = false;
    private $_hasWarehouseRights = false;
    private $_hasWarehouseAdminRights = false;
    private $_hasWarehouseEditorRights = false;
    private $_hasKVSCuttingRights = false;
    private $_hasExportMassPaymentRights = false;
    private $_accessToken;
    private $_loginToken;
    private $_keycloakUid;

    /**
     * Class constructor.
     *
     * @param IUserManager Instance to MTUserManager class
     */
    public function __construct(IUserManager $manager)
    {
        $this->_manager = $manager;
    }

    /**
     * Returns instance to current MTUserManager class.
     *
     * @return IUserManager
     */
    public function getManager()
    {
        return $this->_manager;
    }

    /**
     * Serializes data in session.
     *
     * @return string
     */
    public function saveToString()
    {
        return serialize([
            $this->_name,
            $this->_fullname,
            $this->_userID,
            $this->_database,
            $this->_isGuest,
            $this->_isSuperAdmin,
            $this->_parentID,
            $this->_userLevel,
            $this->_groupID,
            $this->_hasMapRightsR,
            $this->_hasPlotRightsR,
            $this->_hasSubsidyRights,
            $this->_hasAgroRights,
            $this->_isTrial,
            $this->_hasSatelliteRights,
            $this->_hasGeoscanMapRights,
            $this->_hasContractsOwnWriteRights,
            $this->_hasEquityRights,
            $this->_hasMapRightsRW,
            $this->_hasPlotRightsRW,
            $this->_hasSubsidyRightsRW,
            $this->_hasAgroRightsRW,
            $this->_hasSatelliteRightsRW,
            $this->_hasSalesContractsRightsR,
            $this->_hasSalesContractsRightsRW,
            $this->_hasHypothecsRightsR,
            $this->_hasHypothecsRightsRW,
            $this->_paidSupportYear,
            $this->_fullPaidSupportYear,
            $this->_appVersion,
            $this->_appCriticalUpd,
            $this->_hasThematicMapsRightsR,
            $this->_hasThematicMapsRightsRW,
            $this->_hasCollectionsRights,
            $this->_hasCollectionsRightsRW,
            $this->_email,
            $this->_hasDashboardRights,
            $this->_hasWarehouseRights,
            $this->_hasKVSCuttingRights,
            $this->_hasExportMassPaymentRights,
            $this->_accessToken,
            $this->_loginToken,
            $this->_hasCadastreRights,
            $this->_hasSlopeRights,
            $this->_keycloakUid,
            $this->_hasWarehouseAdminRights,
            $this->_hasWarehouseEditorRights,
        ]);
    }

    /**
     * Unserializes data in session.
     *
     * @param string $data
     *
     * @return IUser
     */
    public function loadFromString($data)
    {
        if (!empty($data)) {
            $array = unserialize($data);

            $this->_name = $array[0];
            $this->_fullname = $array[1];
            $this->_userID = $array[2];
            $this->_database = $array[3];
            $this->_isGuest = $array[4];
            $this->_isSuperAdmin = $array[5];
            $this->_parentID = $array[6];
            $this->_userLevel = $array[7];
            $this->_groupID = $array[8];
            $this->_hasMapRightsR = $array[9];
            $this->_hasPlotRightsR = $array[10];
            $this->_hasSubsidyRights = $array[11];
            $this->_hasAgroRights = $array[12];
            $this->_isTrial = $array[13];
            $this->_hasSatelliteRights = $array[14];
            $this->_hasGeoscanMapRights = $array[15];
            $this->_hasContractsOwnWriteRights = $array[16];
            $this->_hasEquityRights = $array[17];
            $this->_hasMapRightsRW = $array[18];
            $this->_hasPlotRightsRW = $array[19];
            $this->_hasSubsidyRightsRW = $array[20];
            $this->_hasAgroRightsRW = $array[21];
            $this->_hasSatelliteRightsRW = $array[22];
            $this->_hasSalesContractsRightsR = $array[23];
            $this->_hasSalesContractsRightsRW = $array[24];
            $this->_hasHypothecsRightsR = $array[25];
            $this->_hasHypothecsRightsRW = $array[26];
            $this->_paidSupportYear = $array[27];
            $this->_fullPaidSupportYear = $array[27];
            $this->_appVersion = $array[29];
            $this->_appCriticalUpd = $array[30];
            $this->_hasThematicMapsRightsR = $array[31];
            $this->_hasThematicMapsRightsRW = $array[32];
            $this->_hasCollectionsRights = $array[33];
            $this->_hasCollectionsRightsRW = $array[34];
            $this->_email = $array[35];
            $this->_hasDashboardRights = $array[36];
            $this->_hasWarehouseRights = $array[37];
            $this->_hasKVSCuttingRights = $array[38];
            $this->_hasExportMassPaymentRights = $array[39];
            $this->_accessToken = $array[40];
            $this->_loginToken = $array[41];
            $this->_hasCadastreRights = $array[42];
            $this->_hasSlopeRights = $array[43];
            $this->_keycloakUid = $array[44];
            $this->_hasWarehouseAdminRights = $array[45];
            $this->_hasWarehouseEditorRights = $array[46];
        }

        return $this;
    }

    public function getParentID()
    {
        return $this->_parentID;
    }

    public function setParentID($value)
    {
        $this->_parentID = $value;
    }

    public function getGroupID()
    {
        return $this->_groupID;
    }

    public function setGroupID($value)
    {
        $this->_groupID = $value;
    }

    public function getUserLevel()
    {
        return $this->_userLevel;
    }

    public function setUserLevel($value)
    {
        $this->_userLevel = $value;
    }

    public function getHasMapRightsR()
    {
        return $this->_hasMapRightsR;
    }

    public function setHasMapRightsR($value)
    {
        $this->_hasMapRightsR = $value;
    }

    public function getHasMapRightsRW()
    {
        return $this->_hasMapRightsRW;
    }

    public function setHasMapRightsRW($value)
    {
        $this->_hasMapRightsRW = $value;
    }

    public function getHasPlotRightsR()
    {
        return $this->_hasPlotRightsR;
    }

    public function setHasPlotRightsR($value)
    {
        $this->_hasPlotRightsR = $value;
    }

    public function getHasPlotRightsRW()
    {
        return $this->_hasPlotRightsRW;
    }

    public function setHasPlotRightsRW($value)
    {
        $this->_hasPlotRightsRW = $value;
    }

    public function getHasSubsidyRights()
    {
        return $this->_hasSubsidyRights;
    }

    public function setHasSubsidyRights($value)
    {
        $this->_hasSubsidyRights = $value;
    }

    public function getHasSubsidyRightsRW()
    {
        return $this->_hasSubsidyRightsRW;
    }

    public function setHasSubsidyRightsRW($value)
    {
        $this->_hasSubsidyRightsRW = $value;
    }

    public function getHasAgroRights()
    {
        return $this->_hasAgroRights;
    }

    public function setHasAgroRights($value)
    {
        $this->_hasAgroRights = $value;
    }

    public function getHasAgroRightsRW()
    {
        return $this->_hasAgroRightsRW;
    }

    public function setHasAgroRightsRW($value)
    {
        $this->_hasAgroRightsRW = $value;
    }

    public function getHasSatelliteRights()
    {
        return $this->_hasSatelliteRights;
    }

    public function setHasSatelliteRights($value)
    {
        $this->_hasSatelliteRights = $value;
    }

    public function getHasSatelliteRightsRW()
    {
        return $this->_hasSatelliteRightsRW;
    }

    public function setHasSatelliteRightsRW($value)
    {
        $this->_hasSatelliteRightsRW = $value;
    }

    public function getHasSalesContractsRightsR()
    {
        return $this->_hasSalesContractsRightsR;
    }

    public function setHasSalesContractsRightsR($value)
    {
        $this->_hasSalesContractsRightsR = $value;
    }

    public function getHasSalesContractsRightsRW()
    {
        return $this->_hasSalesContractsRightsRW;
    }

    public function setHasSalesContractsRightsRW($value)
    {
        $this->_hasSalesContractsRightsRW = $value;
    }

    public function getHasGeoscanMapRights()
    {
        return $this->_hasGeoscanMapRights;
    }

    public function setHasGeoscanMapRights($value)
    {
        $this->_hasGeoscanMapRights = $value;
    }

    public function getHasContractsOwnWriteRights()
    {
        return $this->_hasContractsOwnWriteRights;
    }

    public function setHasContractsOwnWriteRights($value)
    {
        $this->_hasContractsOwnWriteRights = $value;
    }

    public function getHasEquityRights()
    {
        return $this->_hasEquityRights;
    }

    public function setHasEquityRights($value)
    {
        $this->_hasEquityRights = $value;
    }

    public function getHasDashboardRights()
    {
        return $this->_hasDashboardRights;
    }

    public function setHasDashboardRights($value)
    {
        $this->_hasDashboardRights = $value;
    }

    public function getHasHypothecsRightsR()
    {
        return $this->_hasHypothecsRightsR;
    }

    public function setHasHypothecsRightsR($value)
    {
        $this->_hasHypothecsRightsR = $value;
    }

    public function getHasHypothecsRightsRW()
    {
        return $this->_hasHypothecsRightsRW;
    }

    public function setHasHypothecsRightsRW($value)
    {
        $this->_hasHypothecsRightsRW = $value;
    }

    public function getHasThematicMapsRightsR()
    {
        return $this->_hasThematicMapsRightsR;
    }

    public function setHasThematicMapsRightsR($value)
    {
        $this->_hasThematicMapsRightsR = $value;
    }

    public function getHasThematicMapsRightsRW()
    {
        return $this->_hasThematicMapsRightsRW;
    }

    public function setHasThematicMapsRightsRW($value)
    {
        $this->_hasThematicMapsRightsRW = $value;
    }

    public function getHasCollectionsRights()
    {
        return $this->_hasCollectionsRights;
    }

    public function setHasCollectionsRights($value)
    {
        $this->_hasCollectionsRights = $value;
    }

    public function getHasCollectionsRightsRW()
    {
        return $this->_hasCollectionsRightsRW;
    }

    public function setHasCollectionsRightsRW($value)
    {
        $this->_hasCollectionsRightsRW = $value;
    }

    public function getIsTrial()
    {
        return $this->_isTrial;
    }

    public function setIsTrial($value)
    {
        $this->_isTrial = $value;
    }

    public function getAppVersion()
    {
        return $this->_appVersion;
    }

    public function setAppVersion($value)
    {
        $this->_appVersion = $value;
    }

    public function getAppCriticalUpd()
    {
        return $this->_appCriticalUpd;
    }

    public function setAppCriticalUpd($value)
    {
        $this->_appCriticalUpd = $value;
    }

    public function getPaidSupportYear()
    {
        return $this->_paidSupportYear;
    }

    public function setPaidSupportYear($value)
    {
        $this->_paidSupportYear = (int)$value;
    }

    public function getFullPaidSupportYear()
    {
        return $this->_fullPaidSupportYear;
    }

    public function setFullPaidSupportYear($value)
    {
        $this->_fullPaidSupportYear = $value;
    }

    public function getHasWarehouseRights()
    {
        return $this->_hasWarehouseRights;
    }

    public function setHasWarehouseRights($value)
    {
        $this->_hasWarehouseRights = $value;
    }

    public function getHasWarehouseEditorRights()
    {
        return $this->_hasWarehouseEditorRights;
    }

    public function setHasWarehouseEditorRights($value)
    {
        $this->_hasWarehouseEditorRights = $value;
    }

    public function getHasKVSCuttingRights()
    {
        return $this->_hasKVSCuttingRights;
    }

    public function setHasKVSCuttingRights($value)
    {
        $this->_hasKVSCuttingRights = $value;
    }

    public function getHasExportMassPaymentRights()
    {
        return $this->_hasExportMassPaymentRights;
    }

    public function setHasExportMassPaymentRights($value)
    {
        $this->_hasExportMassPaymentRights = $value;
    }

    public function setHasCadastreRights($value)
    {
        $this->_hasCadastreRights = $value;
    }

    public function getHasCadastreRights()
    {
        return $this->_hasCadastreRights;
    }

    public function setHasSlopeRights($value)
    {
        $this->_hasSlopeRights = $value;
    }

    public function getHasSlopeRights()
    {
        return $this->_hasSlopeRights;
    }

    public function getHasWarehouseAdminRights()
    {
        return $this->_hasWarehouseAdminRights;
    }

    public function setHasWarehouseAdminRights($value)
    {
        $this->_hasWarehouseAdminRights = $value;
    }

    /**
     * Overriding some unnecessary parent methods.
     */
    public function getRoles() {}

    public function setRoles($value) {}

    public function isInRole($role) {}

    /**
     * Returns user's name.
     *
     * @return string
     */
    public function getName()
    {
        return $this->_name;
    }

    /**
     * Sets user's name.
     *
     * @param string $value
     */
    public function setName($value)
    {
        $this->_name = $value;
    }

    /**
     * Returns user's name.
     *
     * @return string
     */
    public function getFullName()
    {
        return $this->_fullname;
    }

    /**
     * Sets user's name.
     *
     * @param string $value
     */
    public function setFullName($value)
    {
        $this->_fullname = $value;
    }

    /**
     * Returns whether user is logged successfully.
     *
     * @return bool
     */
    public function getIsGuest()
    {
        return $this->_isGuest;
    }

    /**
     * Sets boolean value, indicating whether user is logged successfully or not.
     *
     * @param bool $value
     */
    public function setIsGuest($value)
    {
        $this->_isGuest = TPropertyValue::ensureBoolean($value);
    }

    /**
     * Returns user's id.
     *
     * @return int
     */
    public function getUserID()
    {
        return $this->_userID;
    }

    /**
     * Sets user's id.
     *
     * @param int $value
     */
    public function setUserID($value)
    {
        $this->_userID = $value;
    }

    /**
     * Returns user's id.
     *
     * @return int
     */
    public function getDatabase()
    {
        return $this->_database;
    }

    /**
     * Sets user's id.
     *
     * @param int $value
     */
    public function setDatabase($value)
    {
        $this->_database = $value;
    }

    /**
     * Returns whether user is moderator or not.
     *
     * @param char
     */
    public function getIsSuperAdmin()
    {
        return $this->_isSuperAdmin;
    }

    /**
     * Sets a value, indicating whether user is moderator or not.
     *
     * @param char $value
     */
    public function setIsSuperAdmin($value)
    {
        $this->_isSuperAdmin = $value;
    }

    /**
     * Returns the user email.
     */
    public function getEmail()
    {
        return $this->_email;
    }

    /**
     * Sets the user email.
     *
     * @param char $email
     */
    public function setEmail($value)
    {
        $this->_email = $value;
    }

    public function setAccessToken(AccessTokenInterface $token)
    {
        $this->_accessToken = $token;
    }

    public function getAccessToken(): ?AccessTokenInterface
    {
        return $this->_accessToken;
    }

    public function getLoginToken(): ?string
    {
        return $this->_loginToken;
    }

    public function setLoginToken(string $token): void
    {
        $this->_loginToken = $token;
    }

    public function getKeyCloakUID(): ?string
    {
        return $this->_keycloakUid;
    }

    public function setKeyCloakUID(string $uid): void
    {
        $this->_keycloakUid = $uid;
    }

    /**
     * If user currently has permission to farm and farmid is not in $farmingsIds will be revokled
     * If user curenntly has no permission to farm and farmId is i $famingIds will be granted.
     *
     * @param [array] $permissions
     */
    public function updateFarmingUsersPermissions(int $farmingId, array $users, array $permissions): void
    {
        $provider = new ObjectPermissionsProvider();

        foreach ($permissions as $permissionId) {
            $permissionedUsers = $provider->getObjectPermissionedUsers(UserFarmings::class, $farmingId, $permissionId);

            array_walk($permissionedUsers, function ($objectPermission) use ($users) {
                if (in_array($objectPermission->user_id, $users)) {
                    return;
                }

                $this->revokeUserPermission($objectPermission);
            });

            array_walk($users, function ($userId) use ($permissionId, $provider, $farmingId) {
                if ($provider->hasPermission($permissionId, UserFarmings::class, $userId, $farmingId)) {
                    return;
                }

                $this->grantUserPermission(UserFarmings::class, $userId, $permissionId, $farmingId);
            });
        }
    }

    /**
     * If user currently has permission to farm and farmid is not in $farmingsIds will be revokled
     * If user curenntly has no permission to farm and farmId is i $famingIds will be granted.
     */
    public function updateUserFarmingPermissions(array $farmingIds, int $userId, array $permissions): void
    {
        $provider = new ObjectPermissionsProvider();
        $currentPermissions = $provider->getUserClassPermissions(UserFarmings::class, $userId, ObjectPermissions::$permisionsMap);

        array_walk($currentPermissions, function ($objectPermission) use ($farmingIds, $permissions) {
            if (in_array($objectPermission->object_id, $farmingIds) && in_array($objectPermission->permission, $permissions)) {
                // do nothing
                return;
            }
            $this->revokeUserPermission($objectPermission);
        });
        foreach ($farmingIds as $farmingId) {
            foreach ($permissions as $permissionId) {
                if ($provider->hasPermission($permissionId, UserFarmings::class, $userId, $farmingId)) {
                    // do nothnig
                    continue;
                }

                $this->grantUserPermission(UserFarmings::class, $userId, $permissionId, $farmingId);
            }
        }
    }

    /**
     * @param [type] $className
     */
    public function getPermissionObjectIds(int $permission, $className)
    {
        return array_column($this->getPermissions($permission, $className), 'object_id');
    }

    /**
     * Undocumented function.
     *
     * @param [type] $className
     */
    public function getPermissions(int $permission, $className)
    {
        $provider = new ObjectPermissionsProvider();

        return $provider->getUserClassPermissions($className, $this->getUserID(), [$permission]);
    }

    /**
     * To be used when to check current session subject has permission to object.
     *
     * @return bool
     */
    public function hasPermissionTo(int $permission, string $className, int $objectId)
    {
        $provider = new ObjectPermissionsProvider();

        return $provider->hasPermission($permission, $className, $this->getUserID(), $objectId);
    }

    public function userHasPermission(int $userId, string $className, int $objectId, int $permission)
    {
        $provider = new ObjectPermissionsProvider();

        return $provider->hasPermission($permission, $className, $userId, $objectId);
    }

    /**
     * @param [type] $className
     * @param [type] $userId
     * @param [type] $objectId
     */
    public function revokeUserObjectPermission($className, $userId, $objectId)
    {
        $provider = new ObjectPermissionsProvider();
        $permissions = $provider->getUserObjectPermissions($className, $userId, $objectId);
        array_walk($permissions, function ($objectPermission) {
            $this->revokeUserPermission($objectPermission);
        });
    }

    public function grantUserPermission(string $className, int $userId, int $permissionId, int $objectId): void
    {
        $provider = new ObjectPermissionsProvider();
        if ($provider->hasPermission($permissionId, $className, $userId, $objectId)) {
            return;
        }

        $provider->grantPermission($className, $userId, $permissionId, $objectId);
    }

    private function revokeUserPermission(ObjectPermissions $permission): void
    {
        $provider = new ObjectPermissionsProvider();
        $provider->revokePermission($permission);
    }
}
