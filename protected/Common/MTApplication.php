<?php

namespace TF\Application\Common;

use Prado\Prado;
use Prado\TApplication;

class MTApplication extends TApplication
{
    /**
     * @return string an ID that uniquely identifies this Prado application from the others
     */
    public function getUniqueID()
    {
        return APP_UNIQUE_KEY;
    }

    public function onEndRequest()
    {
        parent::onEndRequest();
    }

    protected function initApplication()
    {
        parent::initApplication();
        Prado::getApplication()->setMode(getenv('APPLICATION_MODE'));
    }
}
