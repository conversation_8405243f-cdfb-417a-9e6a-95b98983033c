<?php

namespace TF\Application\Common;

class KvsStoreAuth extends SystemAuth
{
    protected function getCLientId(): string
    {
        return getenv('KEYKLOACK_KVS_STORE_CLIENT_ID');
    }

    protected function getClientSecret(): string
    {
        return getenv('KEY<PERSON><PERSON>OACK_KVS_STORE_CLIENT_SECRET');
    }

    protected function getRealm(): string
    {
        return getenv('KEYKLOACK_KVS_STORE_REALM');
    }

    protected function getAuthServerUrl(): string
    {
        return getenv('KEYKLOACK_KVS_STORE_AUTH_SERVER_URL');
    }

    protected function getTokenUrl()
    {
        return 'realms/' . $this->getRealm() . '/protocol/openid-connect/token';
    }

    protected function getTokenIntrospectUrl(): string
    {
        return $this->getAuthServerUrl() . '/realms/' . $this->getRealm() . '/protocol/openid-connect/token/introspect';
    }
}
