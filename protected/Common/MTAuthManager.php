<?php
/**
 * MTAuthManager class file.
 *
 * <AUTHOR> Ltd. <<EMAIL>>
 *
 * @link http://www.devision.bg/
 *
 * @copyright Copyright &copy; 2008 Devision Ltd.
 */

namespace TF\Application\Common;

use Prado\Security\TAuthManager;
use Prado\Web\UI\ActiveControls\TActivePageAdapter;
use Prado\Web\UI\TPage;
use TF\Engine\Kernel\Sentry\Sentry;

// Prado::using('System.Security.TAuthManager');

/**
 * MTAuthManager class.
 *
 * MTAuthManager represents access level to pages.
 *
 * <AUTHOR> Ltd. <<EMAIL>>
 */
class MTAuthManager extends TAuthManager
{
    public $notSuperAdminPages = [
        'Users.Add', 'Users.Edit', 'Satellite.Admin',
    ];

    public $mapRightsPages = [
        'Map.Home', 'Operations.Home', 'Files.Home', 'OSZ.Home',
    ];

    public $plotRightsPages = [
        'Plots.Home', 'Owners.Home', 'Contracts.Home', 'Subleases.Home', 'Annexes.Home', 'Agreements.Home', 'Payments.Home', 'OwnerPayments.Home',
    ];

    public $subsidyRightsPages = [
        'ZPlots.Home', 'Isak.Home', 'SubsidiesWizard.Home', 'CropRotation.Home', 'Overlaps.Home',
    ];

    public $agroRightsPages = [
        'Coverage.Home', 'Diary.Home',
    ];

    public $notSubUserPages = [
        'Users.Home',
    ];

    public $equityRightsPages = [
        'Cooperators.Home', 'Dividends.Home',
    ];

    public $salesContractsRightsPages = [
        'SalesContracts.Home',
    ];

    public $hypothecsRightsPages = [
        'Hypothecs.Home',
    ];

    public $thematicMapsRightsPages = [
        'ThematicMaps.Home',
    ];

    public $collectionsRightsPages = [
        'Collections.Home',
    ];
    public $dashboardRightsPages = [
        'Dashboard.Home',
    ];

    // Allowed CallBack IDs

    // Имоти Allowed CallBacks
    public $plotAllowedCallBacks = [
        'initMapCallBack',
        'editMarkCallback',
        'clearDeclarationsCallBack',
        'checkKMSAgreementsCallBack',
        'markForEditCallBack',
        'addPanelRequest',
        'getParentContractDataCallback',
        'initKvsMapCallBack',
    ];

    // Субсидии Allowed CallBacks
    public $subsidyAllowedCallBacks = [
        'initMapCallBack',
        'MarkForEditCallBack',
        'initKvsMapCallBack',
        'initIsakDiffAllowableCallBack',
        'initForIsakDiffAllowableFinalCallBack',
        'getReportZDP',
        'initForIsakDiffPZPCallBack',
        'editENPData',
        'initForIsakDiffLfaCallBack',
        'initForIsakDiffNaturaCallBack',
        'initForIsakDiffVPSCallBack',
        'getReportPZP',
        'ButtonChooseKvsZoom',
        'getKvsExtentCallBack',
    ];

    // Агротехника Allowed CallBacks
    public $agroAllowedCallBacks = [
        'zpLayerDataCallBack',
        'ZPlotEditMarkCallBack',
        'eventTypeEditMarkCallBack',
        'eventSubtypeEditMarkCallBack',
        'addEventSubtypeCallBack',
        'machineTypeEditMarkCallBack',
        'machineEditMarkCallBack',
        'addMachineCallBack',
        'attachmentEditMarkCallBack',
        'addAttachmentCallBack',
        'substanceTypeEditMarkCallBack',
        'substanceTechnicEditMarkCallBack',
        'performerEditMarkCallBack',
        'addPerformerCallBack',
        'getMessagesCallBack',
        'singleEditMarkCallBack',
        'clearAddEditEventPanelCallBack',
    ];

    // Allowed Rpc Methods
    public $allowedRpcMethods = [
        'read',
        'load',
    ];

    // Allowed Map Rpc Methods
    public $allowedMapRpcMethods = [
        'read',
        'load',
        'getMaxExtent',
        'export',
        'selectPolygon',
        'getKVSLayerData',
        'getAllZpLayersData',
    ];

    // Allowed Plots Rpc Methods
    public $allowedPlotsRpcMethods = [
        'read',
        'load',
        'initMap',
        'initMapLayers',
        'clearDeclarations',
        'checkKMSAgreements',
        'decl69',
        'decl70',
        'createDecl69',
        'createDecl70',
        'createCSVDecl69',
        'createCSVDecl70',
        'removeFile',
        'mapReports',
        'mapReportsByType',
        'anketnaKarta',
        'createAnketnaKarta',
        'deleteFromDeclaration',
        'addToDeclaration',
        'generateImage',
        'contractInit',
        'masivInit',
        'getTreePlots',
    ];
    // Dashboard Allowed CallBacks
    public $allowedDashboardRpcMethods = [
        'reed',
    ];
    // Allowed Owners Rpc Methods
    public $allowedOwnersRpcMethods = [
        'read',
        'load',
        'getOwnerParents',
    ];

    // Allowed Contracts Rpc Methods
    public $allowedContractsRpcMethods = [
        'read',
        'load',
        'exportContractBlank',
        'deleteFile',
        'download',
        'getOwnerHeritors',
    ];

    // Allowed Subleases Rpc Methods
    public $allowedSubleasesRpcMethods = [
        'read',
        'load',
        'readContragents',
        'deleteFile',
        'exportContractBlank',
        'downloadAttached',
    ];

    // Allowed Annexes Rpc Methods
    public $allowedAnnexesRpcMethods = [
        'read',
        'load',
        'getParentContractData',
        'downloadAttached',
    ];

    // Allowed Agreements Rpc Methods
    public $allowedAgreementsRpcMethods = [
        'read',
        'load',
        'initMap',
        'initKvs',
    ];

    // Allowed Payments Rpc Methods
    public $allowedPaymentsRpcMethods = [
        'read',
        'load',
        'expChargedRentaHistory',
        'exportToPdfPaymentOrder',
        'exportToPdfBankPaymentOrder',
        'exportToPdfWeighingNote',
        'exportToPdfCombined',
        'exportToPdfBankCombined',
        'loadInfo',
        'refreshReport',
        'printPaymentsReportsByData',
        'exportToExcelPaymentsReportsByData',
        'exportToExcelDetailedReportRentaByDate',
        'printPaymentsSummaryReports',
        'exportToExcelPaymentsSummaryReports',
        'printPaymentsReportsRentaNaturaGrid',
        'exportToExcelPaymentsReportsRentaNaturaGrid',
        'exportToDetailedExcelPaymentsReportsRentaNaturaGrid',
        'exportToExcelPayrollGrid',
        'printPayrollGrid',
        'exportToExcel',
    ];

    // Allowed OSZ Rpc Methods
    public $allowedOSZRpcMethods = [
        'read',
        'load',
        'exportExcel',
        'deleteFile',
    ];

    // Allowed HypothecsRpc Methods
    public $allowedHypothecsRpcMethods = [
        'read',
        'export',
        'readForAdding',
        'readCombobox',
    ];

    // Allowed ThematicMapsRpc Methods
    public $allowedThematicMapsRpcMethods = [
        'read',
        'print',
        'load',
    ];

    // Allowed SalesContractsRpc Methods
    public $allowedSalesContractsRpcMethods = [
        'read',
        'load',
        'exportSaleContractBlank',
        'deleteFile',
        'downloadAttached',
    ];

    // Allowed AgroRpc Methods
    public $allowedAgroRpcMethods = [
        'read',
        'load',
        'loadExpenses',
        'getMapZPlotInfo',
        'summaryByPerformer',
        'detailedByPerformer',
        'detailedBy',
        'exportPerformerSummaryReportCSV',
        'exportPerformerDetailedReportCSV',
        'exportPerformerDetailedReportByCSV',
        'deleteFile',
        'getMessages',
        'getWialonReportData',
        'getWialonMachines',
        'getWialonDrivers',
        'getWialonMachineTypes',
        'getEventInfo',
        'initMap',
        'initAltitudeMap',
        'requestZPLayerData',
    ];

    // Allowed subsidies Methods
    public $allowedSubsidiesRpcMethods = [
        'read',
        'load',
        'initMap',
        'initKvs',
        'initCulture',
        'exportExcel',
        'exportIntersectExcel',
        'removeFile',
        'init',
        'getReportZDP',
        'exportGridToXLS',
        'exportGridToPDF',
        'getReportPZP',
        'initForIsakDiffPZP',
        'initForIsakDiffVPS',
        'initForIsakDiffNatura',
        'initForIsakDiffLfa',
        'initForIsakDiffAllowableFinal',
        'exportSoilSample',
        'exportFullSampleNorm',
        'exportOverlapData',
        'exportCultureData',
        'exportSampleNorm',
        'exportAVGSampleNorm',
        'exportCropRotationData',
        'deleteFile',
        'initKVSMap',
        'getKVSExtent',
        'getLayerKVS',
    ];

    // Allowed collections Methods
    public $allowedCollectionsRpcMethods = [
        'read',
        'load',
        'addCollectionPayment',
        'printCollectionContracts',
        'exportCollectionContracts',
    ];

    // Methods in this array will be executed without needed an authentication (Format: service-id.MethodName)
    public $allowedUnAuthorizationMethods = [
        'tests-tools.resetAllData',
        'tests-tools.resetContractData',
    ];

    protected $systemServices = [
        'kvs-client-json',
        'keycloak-system-json',
    ];

    public function init($config)
    {
        parent::init($config);

        $this->getApplication()->attachEventHandler('onAuthorizationComplete', [$this, 'onAuthorizationComplete']);
        $this->getApplication()->attachEventHandler('OnEndRequest', [$this, 'leave']);

        if (get_class() == get_called_class()) {
            $this->setAuthExpire(USER_SESSION_EXPIRATION_TIME);
        }
    }

    /**
     * Performs the real authorization work.
     */
    public function OnAuthorize($param)
    {
        // Get Request Payload
        $requestPayload = json_decode(file_get_contents('php://input'));

        $serviceName = $this->getRequest()->getServiceParameter();

        if ($requestPayload && in_array($serviceName . '.' . $requestPayload->method, $this->allowedUnAuthorizationMethods)) {
            return;
        } // Skip Authorize if the requested method is in the allowedUnAuthorizationMethods array

        if ($this->Service->Enabled && in_array($this->Service->ID, $this->systemServices)) {
            return;
        } // skip Authorize if system end point is called

        if ($this->Service->Enabled && strpos($this->Service->ID, 'rpc') && 'login-rpc' !== $this->Service->ID) {
            if ($this->User->IsGuest) {
                $this->setStatusCodeToUnauthorized();
            }
            $this->menageRpcRightsMessage($this->Service);
        }

        if ($this->Service->Enabled && 'json' === $this->Service->ID) {
            if ($this->User->IsGuest) {
                $this->setStatusCodeToUnauthorized();
            }
        }

        if ($this->Service->Enabled && 'page' === $this->Service->ID && $this->isCallback()) {
            if ($this->User->IsGuest) {
                // Create a callback adapter which counstructor will set up TCallbackReponseAdapter in the HttpResponse class adapter property
                $callbackAdapter = new TActivePageAdapter(new TPage());
                // Redirect (now the adapter is not null)
                $this->setStatusCodeToUnauthorized();
            }

            $this->menageCallbackRightsMessage($this->Service);
        }

        if ($this->Service->Enabled && 'page' === $this->Service->ID) {
            // Handles acces for users to particular pages
            if ((!$this->User->IsSuperAdmin && in_array($this->Service->getRequestedPagePath(), $this->notSuperAdminPages))
                || (!$this->User->HasMapRightsR && in_array($this->Service->getRequestedPagePath(), $this->mapRightsPages))
                || (!$this->User->HasPlotRightsR && in_array($this->Service->getRequestedPagePath(), $this->plotRightsPages))
                || (!$this->User->HasEquityRights && in_array($this->Service->getRequestedPagePath(), $this->equityRightsPages))
                || (!$this->User->HasHypothecsRightsR && in_array($this->Service->getRequestedPagePath(), $this->hypothecsRightsPages))
                || (!$this->User->HasThematicMapsRightsR && in_array($this->Service->getRequestedPagePath(), $this->thematicMapsRightsPages))
                || (!$this->User->HasSubsidyRights && in_array($this->Service->getRequestedPagePath(), $this->subsidyRightsPages))
                || (!$this->User->HasSalesContractsRightsR && in_array($this->Service->getRequestedPagePath(), $this->salesContractsRightsPages))
                || (!$this->User->HasCollectionsRights && in_array($this->Service->getRequestedPagePath(), $this->collectionsRightsPages))
                || (Config::USERS_NORMAL == !$this->User->UserLevel && in_array($this->Service->getRequestedPagePath(), $this->notSubUserPages))
            ) {
                $this->DenyRequest();
            } elseif ($this->User->IsGuest
                    && ('Home' != $this->Service->getRequestedPagePath()
                            && 'ChangePassword' != $this->Service->Service->getRequestedPagePath()
                            && 'InvalidInfo' != $this->Service->Service->getRequestedPagePath())) {
                $this->DenyRequest();
            }
        }

        if ($this->Service->Enabled && 'mapsrv' === $this->Service->ID) {
            if ($this->User->IsGuest) {
                $this->DenyRequest();
            }
        }
    }

    public function onAuthorizationComplete($app)
    {
        Sentry::configureScope($this->User);

        if ($this->Service->Enabled && in_array($this->Service->ID, $this->systemServices)) {
            return;
        } // skip Authorize if system end point is called
    }

    /**
     * Menage json Callbacks Rights - when the user has no "Write" Rights.
     *
     * @param object $service
     */
    protected function menageCallbackRightsMessage($service)
    {
        $postData = $this->getApplication()->getRequest();

        $callback = explode('$', $postData->itemAt(TPage::FIELD_CALLBACK_TARGET));

        $callbackID = end($callback);

        // No MapRightsRW
        if (!$this->User->HasMapRightsRW && in_array($service->getRequestedPagePath(), $this->mapRightsPages)) {
            $this->setStatusCodeNoRights();
        }

        // No PlotRightsRW
        if (!$this->User->HasPlotRightsRW
            && in_array($service->getRequestedPagePath(), $this->plotRightsPages)
            && !in_array($callbackID, $this->plotAllowedCallBacks)) {
            $this->setStatusCodeNoRights();
        }

        // No SubsidyRightsRW
        if (!$this->User->HasSubsidyRightsRW
            && in_array($service->getRequestedPagePath(), $this->subsidyRightsPages)
            && !in_array($callbackID, $this->subsidyAllowedCallBacks)) {
            $this->setStatusCodeNoRights();
        }

        // No AgroRightsRW
        if (!$this->User->HasAgroRightsRW
            && in_array($service->getRequestedPagePath(), $this->agroRightsPages)
            && !in_array($callbackID, $this->agroAllowedCallBacks)) {
            $this->setStatusCodeNoRights();
        }

        // No SalesContractsRightsRW
        if (!$this->User->HasSalesContractsRightsRW
        && in_array($service->getRequestedPagePath(), $this->salesContractsRightsPages)
        && !in_array($callbackID, $this->salesContractsAllowedCallBacks)) {
            $this->setStatusCodeNoRights();
        }

        // No CollectionsRightsRW
        if (!$this->User->HasCollectionsRightsRW
        && in_array($service->getRequestedPagePath(), $this->collectionsRightsPages)
        && !in_array($callbackID, $this->collectionsAllowedCallBacks)) {
            $this->setStatusCodeNoRights();
        }
    }

    /**
     * Menage Rpc Rights - when the user has no "Write" Rights.
     *
     * @param object $service
     *
     * @return [type]          [description]
     */
    protected function menageRpcRightsMessage($service)
    {
        // Get Request Payload
        $requestPayload = json_decode(file_get_contents('php://input'));

        // No MapRights
        if (!$this->User->HasMapRightsR
            && ('map-rpc' === $service->ID
                || 'files-rpc' === $service->ID
                || 'operations-rpc' === $service->ID
                || 'osz-rpc' === $service->ID)) {
            $this->setStatusCodeNoRights();
        }

        // No MapRightsRW
        if (!$this->User->HasMapRightsRW
            && ('map-rpc' === $service->ID
                || 'files-rpc' === $service->ID
                || 'osz-rpc' === $service->ID
                || 'operations-rpc' === $service->ID)
            && !in_array($requestPayload->method, $this->allowedOSZRpcMethods)
            && !in_array($requestPayload->method, $this->allowedMapRpcMethods)
        ) {
            $this->setStatusCodeNoRights();
        }
        // No PlotRights
        if (!$this->User->HasPlotRightsR && ('plots-rpc' === $service->ID
                                            || 'owners-rpc' === $service->ID
                                            || 'subleases-rpc' === $service->ID
                                            || 'annexes-rpc' === $service->ID
                                            || 'agreements-rpc' === $service->ID
                                            || 'payments-rpc' === $service->ID
                                            || 'owner-payments-rpc' === $service->ID
                                            || 'contracts-rpc' === $service->ID)) {
            $this->setStatusCodeNoRights();
        }

        // No PlotRightsRW
        if (!$this->User->HasPlotRightsRW
            && ('plots-rpc' === $service->ID
                || 'owners-rpc' === $service->ID
                || 'subleases-rpc' === $service->ID
                || 'annexes-rpc' === $service->ID
                || 'agreements-rpc' === $service->ID
                || 'payments-rpc' === $service->ID
                || 'owner-payments-rpc' === $service->ID
                || 'contracts-rpc' === $service->ID)
            && !in_array($requestPayload->method, $this->allowedPlotsRpcMethods)
            && !in_array($requestPayload->method, $this->allowedContractsRpcMethods)
            && !in_array($requestPayload->method, $this->allowedSubleasesRpcMethods)
            && !in_array($requestPayload->method, $this->allowedAnnexesRpcMethods)
            && !in_array($requestPayload->method, $this->allowedAgreementsRpcMethods)
            && !in_array($requestPayload->method, $this->allowedPaymentsRpcMethods)
            && !in_array($requestPayload->method, $this->allowedOwnersRpcMethods)) {
            $this->setStatusCodeNoRights();
        }

        // No SalesContractsRightsR
        if (!$this->User->HasSalesContractsRightsR
            && 'sales-contracts-rpc' === $service->ID) {
            $this->setStatusCodeNoRights();
        }

        // No SalesContractsRightsRW
        if (!$this->User->HasSalesContractsRightsRW
            && 'sales-contracts-rpc' === $service->ID
            && !in_array($requestPayload->method, $this->allowedSalesContractsRpcMethods)) {
            $this->setStatusCodeNoRights();
        }

        // No HypothecsRightsR
        if (!$this->User->HasHypothecsRightsR
            && 'hypothecs-rpc' === $service->ID) {
            $this->setStatusCodeNoRights();
        }

        // No HypothecsRightsRW
        if (!$this->User->HasHypothecsRightsRW
            && 'hypothecs-rpc' === $service->ID
            && !in_array($requestPayload->method, $this->allowedHypothecsRpcMethods)) {
            $this->setStatusCodeNoRights();
        }

        // No ThematicMapsRightsR
        if (!$this->User->HasThematicMapsRightsR
            && 'thematic-maps-rpc' === $service->ID) {
            $this->setStatusCodeNoRights();
        }

        // No HypothecsRightsRW
        if (!$this->User->HasThematicMapsRightsRW
            && 'thematic-maps-rpc' === $service->ID
            && !in_array($requestPayload->method, $this->allowedThematicMapsRpcMethods)) {
            $this->setStatusCodeNoRights();
        }

        // No hasSubsidyRights
        if (!$this->User->HasSubsidyRights
            && ('zplots-rpc' === $service->ID
                || 'subsidieswizard-rpc' === $service->ID
                || 'croprotation-rpc' === $service->ID
                || 'overlaps-rpc' === $service->ID
                || 'isak-rpc' === $service->ID)) {
            $this->setStatusCodeNoRights();
        }

        // No SubsidyRightsRW
        if (!$this->User->HasSubsidyRightsRW
            && ('zplots-rpc' === $service->ID
                || 'subsidieswizard-rpc' === $service->ID
                || 'croprotation-rpc' === $service->ID
                || 'overlaps-rpc' === $service->ID
                || 'isak-rpc' === $service->ID)
            && !in_array($requestPayload->method, $this->allowedSubsidiesRpcMethods)) {
            $this->setStatusCodeNoRights();
        }

        // No AgroRightsR
        if (!$this->User->HasAgroRights
            && ('coverage-rpc' === $service->ID
                || 'diary-rpc' === $service->ID)) {
            $this->setStatusCodeNoRights();
        }

        // No AgroRightsRW
        if (!$this->User->HasAgroRightsRW
            && ('coverage-rpc' === $service->ID
                || 'diary-rpc' === $service->ID)
            && !in_array($requestPayload->method, $this->allowedAgroRpcMethods)) {
            $this->setStatusCodeNoRights();
        }

        // No EquityRights
        if (!$this->User->HasEquityRights
            && ('cooperators-rpc' === $service->ID
                || 'dividends-rpc' === $service->ID)) {
            $this->setStatusCodeNoRights();
        }

        // No SatelliteAdminRights
        if (!$this->User->IsSuperAdmin && 'satellite-admin-rpc' === $service->ID) {
            $this->setStatusCodeNoRights();
        }

        // No CollectionsRights
        if (!$this->User->HasCollectionsRights
            && 'collections-rpc' === $service->ID) {
            $this->setStatusCodeNoRights();
        }

        // No CollectionsRW
        if (!$this->User->HasCollectionsRightsRW
            && 'collections-rpc' === $service->ID
            && !in_array($requestPayload->method, $this->allowedCollectionsRpcMethods)) {
            $this->setStatusCodeNoRights();
        }
        // No DashboardRights
        if (!$this->User->HasDashboardRights
            && 'dashboard-rpc' === $service->ID
            // !in_array($requestPayload->method, $this->allowedCollectionsRpcMethods)
        ) {
            $this->setStatusCodeNoRights();
        }
    }

    /**
     * Deny request.
     */
    protected function DenyRequest()
    {
        $this->Application->getModule(Config::AUTH_MODULE)->logout();
        $this->Application->getResponse()->setStatusCode(401);
        $this->Application->completeRequest();
    }

    protected function isCallback()
    {
        $retval = false;
        $postData = $this->getApplication()->getRequest();
        if ($postData->contains(TPage::FIELD_CALLBACK_TARGET)) {
            $retval = true;
        }

        return $retval;
    }

    protected function strPosArray($haystack, $needles, $offset = 0)
    {
        if (is_array($needles)) {
            foreach ($needles as $needle) {
                $pos = $this->strPosArray($haystack, $needle);
                if (false !== $pos) {
                    return $pos;
                }
            }

            return false;
        }

        return strpos($haystack, $needles, $offset);
    }

    /**
     * Set Status Code when the user has No Rights.
     */
    private function setStatusCodeNoRights()
    {
        $this->Response->setStatusCode(499, Config::NO_RIGHTS_MESSAGE);
        $this->getApplication()->flushOutput();
        exit;
    }

    /**
     * Set Status Code when the user is not Unauthorized.
     *
     * https://www.php.net/manual/en/function.exit.php
     * Exit will break script execution.
     * Additional implementation of logout/redirect in js is needed
     */
    private function setStatusCodeToUnauthorized()
    {
        $this->Response->setStatusCode(401);
        $this->getApplication()->flushOutput();
        exit;
    }
}
