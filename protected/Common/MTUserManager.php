<?php

namespace TF\Application\Common;

use DateTime;
use Prado\Prado;
use Prado\Security\IUser;
use Prado\Security\TUserManager;
use TF\Engine\APIClasses\Login\LoginForm;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Users\UsersModel;

/*
 * MTUserManager class file
 *
 * <AUTHOR> Ltd. <<EMAIL>>
 * @link http://www.devision.bg/
 * @copyright Copyright &copy; 2008 Devision Ltd.
 */

Prado::using('System.Security.TUserManager');
Prado::using('Application.Common.MTUser');
Prado::using('Plugins.Core.Users.conf');
Prado::using('Plugins.Core.Users.*');
Prado::using('Kernel.ReverseFile');

/**
 * MTUserManager class.
 *
 * Extends TUserManager.
 * This class performs user authentication by connecting to the database.
 * If given username and password match, user data is stored in the session
 * using MTUser class.
 *
 * <AUTHOR> Ltd. <<EMAIL>>
 */
class MTUserManager extends TUserManager
{
    /**
     * Performs DB check for username and password and returns result.
     *
     * @param string $username
     * @param string $password
     *
     * @return bool
     */
    public function validateUser($username, $password)
    {
        // creating the user model instance
        $dbHandler = new UsersModel($GLOBALS['Users']['tableName'], $GLOBALS['Users']['fieldName']);
        // checking if login info is correct
        $row = $dbHandler->getUserLogin($username);
        // checking the info
        if ($row && (crypt($password, $row['password']) == $row['password'])) {
            // check if account is active
            if (!$row['active']) {
                throw new MTRpcException('Account is not active', LoginForm::ACCOUNT_IS_NOT_ACTIVE);
            }

            if (3 == $row['level']) {
                // prepare options to get parent account data
                $options = [
                    'where' => [
                        'id' => ['column' => 'id', 'compare' => '=', 'value' => $row['group_id']],
                    ],
                ];

                $results = $dbHandler->getUsers($options, false, false);
                $parent = $results[0];
            } else {
                $parent = $row;
            }

            // check and update parent data if necessary
            $inactivate = false;

            if ($parent['date_flag']) {
                if (strtotime($parent['due_date']) <= strtotime(date('Y-m-d'))) {
                    $inactivate = true;
                }
            }
            if ($parent['entry_flag']) {
                if ($parent['entries_left'] <= 0) {
                    $inactivate = true;
                } else {
                    // prepare options for main group user update( entries_left - 1 )
                    $options = [
                        'mainData' => [
                            'entries_left' => $parent['entries_left'] - 1,
                        ],
                        'where' => [
                            'id' => ['column' => 'id', 'compare' => '=', 'value' => $row['group_id']],
                        ],
                    ];

                    $dbHandler->updateUsersData($options);
                }
            }

            // inactivate group if necessary
            if ($inactivate) {
                // prepare update array
                $options = [
                    'mainData' => [
                        'active' => false,
                    ],
                    'where' => [
                        'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $parent['id']],
                    ],
                ];

                $dbHandler->updateUsersData($options);

                return false;
            }

            return ($row['username'] = $username && $row['password']);
        }

        return false;
    }

    /**
     * Stores user data in session if authentication is successfull.
     *
     * @param string $username
     */
    public function getUser($username = null): IUser
    {
        $user = new MTUser($this);

        if (is_null($username)) {
            $user->IsGuest = true;
        } else {
            // creating user model instance
            $dbHandler = new UsersModel($GLOBALS['Users']['tableName'], $GLOBALS['Users']['fieldName']);
            // get user data
            $row = $dbHandler->getUserData($username);

            if ($row) {
                if ($row['is_superadmin']) {
                    $user->IsSuperAdmin = true;
                } else {
                    $user->IsSuperAdmin = false;
                }
                $user->IsGuest = false;
                $user->Name = $row['username'];
                $user->FullName = $row['name'];
                $user->Database = $row['database'];
                $user->GroupID = $row['group_id'];
                $user->ParentID = $row['parent_id'];
                $user->UserLevel = $row['level'];
                $user->UserID = $row['id'];
                $user->IsTrial = $row['is_trial'];
                $user->AppVersion = $row['app_version'];
                $user->AppCriticalUpd = $row['app_critical_upd'];
                $user->PaidSupportYear = $this->extrYearIndexFromDate($row['paid_support']);
                $user->FullPaidSupportYear = $this->extractFullYear($row['paid_support']);
                $user->Email = $row['email'];
                $user->KeyCloakUID = $row['keycloak_uid'];

                // getting user rights
                $rights_results = $dbHandler->getUserRightsByUserID($row['id']);
                $rights_array = [];

                // converting the data rows into array
                if (0 != count($rights_results)) {
                    for ($i = 0; $i < count($rights_results); $i++) {
                        $rights_array[] = $rights_results[$i]['right_id'];
                    }
                }
                // assigning rights to the variables
                if (in_array(Config::MAP_RIGHTS_R, $rights_array)) {
                    $user->HasMapRightsR = true;
                }
                if (in_array(Config::MAP_RIGHTS_RW, $rights_array)) {
                    $user->HasMapRightsRW = true;
                }

                if (in_array(Config::PLOT_RIGHTS_R, $rights_array)) {
                    $user->HasPlotRightsR = true;
                }
                if (in_array(Config::PLOT_RIGHTS_RW, $rights_array)) {
                    $user->HasPlotRightsRW = true;
                }

                if (in_array(Config::HYPOTHECS_RIGHTS_R, $rights_array)) {
                    $user->HasHypothecsRightsR = true;
                }
                if (in_array(Config::HYPOTHECS_RIGHTS_RW, $rights_array)) {
                    $user->HasHypothecsRightsRW = true;
                }

                if (in_array(Config::THEMATIC_MAPS_RIGHTS_R, $rights_array)) {
                    $user->HasThematicMapsRightsR = true;
                }
                if (in_array(Config::THEMATIC_MAPS_RIGHTS_RW, $rights_array)) {
                    $user->HasThematicMapsRightsRW = true;
                }

                if (in_array(Config::SUBSIDY_RIGHTS, $rights_array)) {
                    $user->HasSubsidyRights = true;
                }
                if (in_array(Config::SUBSIDY_RIGHTS_RW, $rights_array)) {
                    $user->HasSubsidyRightsRW = true;
                }

                if (in_array(Config::AGRO_RIGHTS, $rights_array)) {
                    $user->HasAgroRights = true;
                }
                if (in_array(Config::AGRO_RIGHTS_RW, $rights_array)) {
                    $user->HasAgroRightsRW = true;
                }

                if (in_array(Config::SALES_CONTRACTS_RIGHTS_R, $rights_array)) {
                    $user->HasSalesContractsRightsR = true;
                }
                if (in_array(Config::SALES_CONTRACTS_RIGHTS_RW, $rights_array)) {
                    $user->HasSalesContractsRightsRW = true;
                }
                if (in_array(Config::COLLECTIONS_RIGHTS, $rights_array)) {
                    $user->HasCollectionsRights = true;
                }
                if (in_array(Config::COLLECTIONS_RIGHTS_RW, $rights_array)) {
                    $user->HasCollectionsRightsRW = true;
                }

                if (in_array(Config::CONTRACTS_OWN_WRITE_RIGHTS, $rights_array)) {
                    $user->HasContractsOwnWriteRights = true;
                }

                if (in_array(Config::EQUITY_RIGHTS, $rights_array)) {
                    $user->HasEquityRights = true;
                }
                if (in_array(Config::DASHBOARD_RIGHTS, $rights_array)) {
                    $user->HasDashboardRights = true;
                }
                if (array_intersect($rights_array, [Config::WAREHOUSE_USER_RIGHTS, Config::WAREHOUSE_ADMIN_RIGHTS, Config::WAREHOUSE_EDITOR_RIGHTS])) {
                    $user->HasWarehouseRights = true;
                }

                if (in_array(Config::WAREHOUSE_ADMIN_RIGHTS, $rights_array)) {
                    $user->HasWarehouseAdminRights = true;
                }

                if (in_array(Config::WAREHOUSE_EDITOR_RIGHTS, $rights_array)) {
                    $user->HasWarehouseEditorRights = true;
                }

                if (in_array(Config::KVS_CUTTING_RIGHTS, $rights_array)) {
                    $user->HasKVSCuttingRights = true;
                }
                if (in_array(Config::EXPORT_MASS_PAYMENT_RIGHTS, $rights_array)) {
                    $user->HasExportMassPaymentRights = true;
                }
                if (in_array(Config::CADASTRE_RIGHTS, $rights_array)) {
                    $user->HasCadastreRights = true;
                }
                if (in_array(Config::SLOPE_RIGHTS, $rights_array)) {
                    $user->HasSlopeRights = true;
                }
                $_SESSION['user_id'] = $row['id'];
                $_SESSION['track_token'] = $row['track_token'];
                $_SESSION['group_id'] = $row['group_id'];
                $_SESSION['database'] = $row['database'];
                $_SESSION['username'] = $row['username'];
            } else {
                $user->IsGuest = true;
            }
        }

        return $user;
    }

    protected function extrYearIndexFromDate($date)
    {
        $date = DateTime::createFromFormat('Y-m-d', $date);
        if (!$date) {
            return;
        }
        $year = $date->format('Y');

        if (!$year) {
            return;
        }

        return $year[3] - 1;
    }

    protected function extractFullYear($date)
    {
        $date = DateTime::createFromFormat('Y-m-d', $date);
        if (!$date) {
            return '';
        }

        $year = $date->format('Y');
        if (!$year) {
            return '';
        }

        return $year;
    }
}
