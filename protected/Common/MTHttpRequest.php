<?php
/**
 * MTHttpRequest class file.
 *
 * <AUTHOR> Ltd. <<EMAIL>>
 *
 * @link http://www.devision.bg/
 *
 * @copyright Copyright &copy; 2008 Devision Ltd.
 */

use Prado\Web\THttpRequest;

/**
 * MTHttpRequest class.
 *
 * Extends THttpRequest class.
 * Adds session lang to url.
 *
 * <AUTHOR> Ltd. <<EMAIL>>
 */
class MTHttpRequest extends THttpRequest
{
    public function constructUrl($serviceID, $serviceParam, $getItems = null, $encodeAmpersand = false, $encodeGetItems = true)
    {
        return parent::constructUrl($serviceID, $serviceParam, $getItems, $encodeAmpersand, $encodeGetItems);
    }

    /**
     * @param bool $mimetypeOnly whether to return only the mimetype (default: true)
     *
     * @return string content type (e.g. 'application/json' or 'text/html; encoding=gzip') or null if not specified
     */
    public function getContentType($mimetypeOnly = true)
    {
        if (!isset($_SERVER['CONTENT_TYPE'])) {
            return;
        }

        if (true === $mimetypeOnly && ($_pos = strpos(';', $_SERVER['CONTENT_TYPE'])) !== false) {
            return substr($_SERVER['CONTENT_TYPE'], 0, $_pos);
        }

        return $_SERVER['CONTENT_TYPE'];
    }
}
