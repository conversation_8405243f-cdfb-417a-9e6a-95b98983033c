<?php

namespace TF\Application\Common;

use DateInterval;
use DateTime;
use Prado\Prado;
use TF\Engine\Kernel\LoggerMessages;
use TF\Engine\Plugins\Core\Users\UsersController;

trait LoginEvents
{
    /**
     * Redirects the user to the appropriate page after successful login.
     */
    public function onLoginSuccess()
    {
        if ($this->User->isGuest) {
            return;
        }

        $this->logLogin($this->User->UserID, $this->User->Name);

        $redirectMap = $this->getRedirectMap();

        // Requested URI from the application
        $requestedUri = Prado::getApplication()->getModule(Config::AUTH_MODULE)->getRequestedtUrl();

        // Default redirect if no specific rights
        $defaultRedirect = GEOSCAN_APP_URL . '/client/no-subscriptions';

        // Determine the redirect URL
        $redirect = $this->determineRedirect($requestedUri, $redirectMap, $defaultRedirect);

        // Perform the redirect
        $this->getApplication()->getResponse()->redirect($redirect);
    }

    /**
     * Get the redirect map with user rights.
     *
     * @return array
     */
    private function getRedirectMap()
    {
        $legacyMode = filter_var(LEGACY_MODE, FILTER_VALIDATE_BOOLEAN);

        return [
            'index.php?page=Map.Home' => $this->User->HasMapRightsR && $legacyMode,
            'index.php?page=Contracts.Home' => $this->User->HasPlotRightsR,
            'index.php?page=Plots.Home' => $this->User->HasPlotRightsR,
            'index.php?page=Dividends.Home' => $this->User->HasEquityRights,
            'index.php?page=Hypothecs.Home' => $this->User->HasHypothecsRightsR,
            'index.php?page=Collections.Home' => $this->User->HasCollectionsRights,
            'index.php?page=Diary.Home' => $this->User->HasAgroRightsRW,
            'index.php?page=Cooperators.Home' => $this->User->HasEquityRights,
            'index.php?page=Coverage.Home' => $this->User->HasAgroRightsRW,
            'index.php?page=Navigation.Home' => $this->User->HasAgroRightsRW,
            'index.php?page=Subleases.Home' => $this->User->HasPlotRightsR,
            'index.php?page=Payments.Home' => $this->User->HasPlotRightsR,
            'index.php?page=OwnerPayments.Home' => $this->User->HasPlotRightsR,
            'index.php?page=Payroll.Home' => $this->User->HasPlotRightsR,
            'index.php?page=SalesContracts.Home' => $this->User->HasSalesContractsRightsR,
            'index.php?page=Owners.Home' => $this->User->HasPlotRightsR,
            'index.php?page=SubsidiesWizard.Home' => $this->User->HasSubsidyRights,
            'index.php?page=Subsidies.Home' => $this->User->HasSubsidyRights,
            'index.php?page=Warehouse.Home' => $this->User->HasWarehouseRights,
            'index.php?page=WarehouseSubContragentsTransaction.Home' => $this->User->HasWarehouseRights,
            'index.php?page=WarehouseReports.Home' => $this->User->HasWarehouseRights,
            'index.php?page=WarehouseReturnTransaction.Home' => $this->User->HasWarehouseRights,
            'index.php?page=WarehouseAddProductionTransaction.Home' => $this->User->HasWarehouseRights,
            'index.php?page=WarehouseSubMachinesTransaction.Home' => $this->User->HasWarehouseRights,
            'index.php?page=WarehouseSubPlotsTransaction.Home' => $this->User->HasWarehouseRights,
            'index.php?page=WarehouseTransferTransaction.Home' => $this->User->HasWarehouseRights,
            'index.php?page=WarehouseAddTransaction.Home' => $this->User->HasWarehouseRights,
        ];
    }

    /**
     * Determine the redirect URL based on user rights and requested URI.
     *
     * @param null|string $requestedUri
     * @param array $redirectMap
     * @param string $defaultRedirect
     *
     * @return string
     */
    private function determineRedirect($requestedUri, $redirectMap, $defaultRedirect)
    {
        $cleanUrl = strtok($requestedUri, '&');
        // If the requested URI matches and the user has rights, redirect to it
        if ($requestedUri && isset($redirectMap[$cleanUrl]) && $redirectMap[$cleanUrl]) {
            return $requestedUri;
        }

        // Check for the first page the user has rights to and redirect
        foreach ($redirectMap as $page => $hasRights) {
            if ($hasRights) {
                return $page;
            }
        }

        // Fallback to default redirect if no other conditions are met
        return $defaultRedirect;
    }

    /**
     * Logs the user IP and date for his last login.
     *
     * @param int $userId the user ID
     * @param string $username the user name
     */
    private function logLogin($userId, $username)
    {
        $UsersController = new UsersController('Users');
        $pattern = '/(?<date>[\\w]+ [\\d]{2} [\\d]{2}:[\\d]{2}:[\\d]{2}).*User: ' . $username . '.*?\\[IP\\] (?<ip>[\\d]{1,3}\\.[\\d]{1,3}\\.[\\d]{1,3}\\.[\\d]{1,3}|::1)/';
        $logFileName = SITE_PATH . 'logs/logger.log';

        if (!file_exists($logFileName)) {
            $fh = fopen($logFileName, 'w');
            fclose($fh);
        }

        $results = $UsersController->regExpLogsSearch($logFileName, $pattern, true);

        if (0 == count($results)) {
            return;
        }

        $dt = DateTime::createFromFormat('M d H:i:s', $results[0]['date'][0]);
        $timeZoneInterval = new DateInterval('PT2H');
        $dt->add($timeZoneInterval);
        $ip = $results[0]['ip'][0];

        $UsersController->updateUsersData([
            'mainData' => [
                'last_login_date' => $dt->format('Y-m-d H:i:s'),
                'last_login_ip' => $ip,
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $userId],
            ],
        ]);

        $UsersController->log(0, $username, LoggerMessages::USER_LOGGED);
    }
}
