<?php
/**
 * MTPager class file.
 *
 * <AUTHOR>
 */

use Prado\TPropertyValue;
use Prado\Web\UI\ActiveControls\TActivePager;
use Prado\Web\UI\WebControls\TPagerMode;

/**
 * Pager class.
 */
class MTPager extends TActivePager
{
    public function setMode($value)
    {
        $this->setViewState('Mode', TPropertyValue::ensureEnum($value, 'MTPagerMode'), MTPagerMode::NextPrev);
    }

    public function getNextPageText()
    {
        return $this->getViewState('NextPageText', 'следваща');
    }

    public function setNextPageText($value)
    {
        $this->setViewState('NextPageText', $value, '&#155;');
    }

    public function getPrevPageText()
    {
        return $this->getViewState('PrevPageText', 'предишна');
    }

    public function setPrevPageText($value)
    {
        $this->setViewState('PrevPageText', $value, '&#139;');
    }

    public function getFirstPageText()
    {
        return $this->getViewState('FirstPageText', '&#171;');
    }

    public function setFirstPageText($value)
    {
        $this->setViewState('FirstPageText', $value, '&#171;');
    }

    public function getLastPageText()
    {
        return $this->getViewState('LastPageText', '&#187;');
    }

    public function setLastPageText($value)
    {
        $this->setViewState('LastPageText', $value, '&#187;');
    }

    // Builds the pager content based on the pager mode.
    protected function buildPager()
    {
        switch ($this->getMode()) {
            case MTPagerMode::Full:
                $this->buildFullPager();

                break;
            case MTPagerMode::NextPrev:
                $this->buildNextPrevPager();

                break;
            case MTPagerMode::Numeric:
                $this->buildNumericPager();

                break;
            case MTPagerMode::DropDownList:
                $this->buildListPager();

                break;
        }
    }

    // Builds FullPager that includes NextPrevPager and NumbericPager
    protected function buildFullPager()
    {
        $buttonType = $this->getButtonType();
        $controls = $this->getControls();
        $pageCount = $this->getPageCount();
        $pageIndex = $this->getCurrentPageIndex() + 1;
        $maxButtonCount = $this->getPageButtonCount();
        $buttonCount = $maxButtonCount > $pageCount ? $pageCount : $maxButtonCount;
        $startPageIndex = 1;
        $endPageIndex = $buttonCount;
        if ($pageIndex > $endPageIndex) {
            $startPageIndex = ((int)(($pageIndex - 1) / $maxButtonCount)) * $maxButtonCount + 1;
            if (($endPageIndex = $startPageIndex + $maxButtonCount - 1) > $pageCount) {
                $endPageIndex = $pageCount;
            }
            if ($endPageIndex - $startPageIndex + 1 < $maxButtonCount) {
                if (($startPageIndex = $endPageIndex - $maxButtonCount + 1) < 1) {
                    $startPageIndex = 1;
                }
            }
        }

        $controls->add("<div class='pager'><div style='float: left; padding: 4px 10px 0 0;'>Страници</div>");

        if ($this->getIsFirstPage()) {
            if (($text = $this->getFirstPageText()) !== '') {
                $label = $this->createPagerButton($buttonType, false, '', '', '');
                $label->CssClass = 'pages-button pages-left';
                $controls->add($label);
            }
        } else {
            if (($text = $this->getFirstPageText()) !== '') {
                $button = $this->createPagerButton($buttonType, true, '', self::CMD_PAGE_FIRST, 1);
                $button->CssClass = 'pages-button pages-left';
                $controls->add($button);
            }
        }

        // Start building numeric pager

        for ($i = $startPageIndex;$i <= $endPageIndex;++$i) {
            if ($i === $pageIndex) {
                $label = $this->createPagerButton($buttonType, false, "{$i}", '', '');
                $label->CssClass = 'pages-button pages-num';
                $controls->add($label);
            } else {
                $button = $this->createPagerButton($buttonType, true, "{$i}", self::CMD_PAGE, "{$i}");
                $button->CssClass = 'pages-button pages-num';
                $controls->add($button);
            }
        }

        // End building numeric pager

        if ($this->getIsLastPage()) {
            if (($text = $this->getLastPageText()) !== '') {
                $label = $this->createPagerButton($buttonType, false, '', '', '');
                $label->CssClass = 'pages-button pages-right';
                $controls->add($label);
            }
        } else {
            if (($text = $this->getLastPageText()) !== '') {
                $button = $this->createPagerButton($buttonType, true, '', self::CMD_PAGE_LAST, $pageCount);
                $button->CssClass = 'pages-button pages-right';
                $controls->add($button);
            }
        }

        if ($this->getIsLastPage()) {
            $label = $this->createPagerButton($buttonType, false, $this->getNextPageText(), '', '');
            $label->CssClass = 'pages-button-wide pages-wide';
            $controls->add($label);
        } else {
            $button = $this->createPagerButton($buttonType, true, $this->getNextPageText(), self::CMD_PAGE_NEXT, $pageIndex + 1);
            $button->CssClass = 'pages-button-wide pages-wide';
            $controls->add($button);
        }

        if ($this->getIsFirstPage()) {
            $label = $this->createPagerButton($buttonType, false, $this->getPrevPageText(), '', '');
            $label->CssClass = 'pages-button-wide pages-wide';
            $controls->add($label);
        } else {
            $button = $this->createPagerButton($buttonType, true, $this->getPrevPageText(), self::CMD_PAGE_PREV, $pageIndex - 1);
            $button->CssClass = 'pages-button-wide pages-wide';
            $controls->add($button);
        }

        $controls->add('</div>');
    }
}

class MTPagerMode extends TPagerMode
{
    public const Full = 'Full';
}
