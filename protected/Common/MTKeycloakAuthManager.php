<?php
/**
 * MTAuthManager class file.
 *
 * <AUTHOR> Ltd. <<EMAIL>>
 *
 * @link http://www.devision.bg/
 *
 * @copyright Copyright &copy; 2008 Devision Ltd.
 */

namespace TF\Application\Common;

use Exception;
use League\OAuth2\Client\Provider\ResourceOwnerInterface;
use League\OAuth2\Client\Token\AccessToken;
use League\OAuth2\Client\Token\AccessTokenInterface;
use Prado\Exceptions\TConfigurationException;
use Prado\Security\IUser;
use Prado\Security\TUser;
use Prado\TApplication;
use Prado\Web\Services\TPageService;
use Prado\Web\THttpCookie;
use Prado\Web\THttpCookieSameSite;
use TF\Engine\Kernel\KeycloakAuthModule;
use TF\Engine\Kernel\LoggerMessages;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Users\UsersController;

class MTKeycloakAuthManager extends MTAuthManager
{
    use LoginEvents;

    private KeycloakAuthModule $authManager;
    private TApplication $application;
    private MTKeycloakUserManager $userManager;

    public function init($config)
    {
        parent::init($config);

        $this->application = $this->getApplication();
        $this->authManager = $this->application->getModule('keycloak');
        $this->userManager = $this->getUserManager();
    }

    /**
     * Performs the real authentication work.
     * An OnAuthenticate event will be raised if there is any handler attached to it.
     * If the application already has a non-null user, it will return without further authentication.
     * Otherwise, user information will be restored from session data.
     *
     * @param mixed $param parameter to be passed to OnAuthenticate event
     *
     * @throws TConfigurationException if session module does not exist
     */
    public function onAuthenticate($param)
    {
        // restoring user info from session
        if (($session = $this->application->getSession()) === null) {
            throw new TConfigurationException('authmanager_session_required');
        }

        $session->open();
        $sessionInfo = $session->itemAt($this->getUserKey());

        $user = $this->userManager->getUser(null);

        if (array_key_exists('Authorization', $this->getRequest()->getHeaders())) {
            if ($this->Service->Enabled && in_array($this->Service->ID, $this->systemServices)) {
                // auth process will be managed by SystemJsonService or SystemRpcService
            } else {
                $accessToken = new AccessToken([
                    'access_token' => bearerToken($this->getRequest()->getHeaders()['Authorization']),
                ]);

                try {
                    $kcUser = $this->authManager->getKeycloakUser($accessToken);
                    $user = $this->userManager->getUser($kcUser->toArray()['preferred_username']);
                    $user->setAccessToken($accessToken);

                    if (null == $sessionInfo) {
                        $user = $this->regenerateSessionUser($user, $accessToken);
                    }
                } catch (Exception $ex) {
                    $user->setIsGuest(true);
                }
            }
        } else {
            $user = $user->loadFromString($sessionInfo);

            try {
                $accessToken = $user->getAccessToken();
                if ($accessToken instanceof AccessTokenInterface) {
                    $kcUser = $this->authManager->getKeycloakUser($accessToken);
                } else {
                    $user->setIsGuest(true);
                }
            } catch (Exception $ex) {
                $user->setIsGuest(true);
            }
        }

        // try authenticating through cookie if possible
        if ($user->getIsGuest()) {
            // try use refresh token to extend auth
            $refreshCookie = $this->getRequest()->getCookies()->itemAt($this->getUserKey() . '_kcr');
            if ($refreshCookie instanceof THttpCookie) {
                try {
                    $token = $this->authManager->refreshAccessToken($refreshCookie->getValue());
                    $kcUser = $this->authManager->getKeycloakUser($token);

                    $user = $this->userManager->getUser($kcUser->toArray()['preferred_username']);
                    $user->setAccessToken($token);

                    $this->updateSessionUser($user);
                    $this->saveKeycloakTokens($token, false);
                    $session->add('AuthExpireTime', $token->getExpires());
                    $isAuthExpired = false;
                } catch (Exception $ex) {
                    // in parent class we do nothing refresh token is also expired and new login is required
                    // but for me is better to mark $isAuthExpired as true
                    $isAuthExpired = true;
                }
            }
        }

        $this->application->setUser($user);

        // handle authentication expiration or update expiration time
        if (isset($isAuthExpired) && true === $isAuthExpired) {
            $this->onAuthExpire($param);
        }

        // event handler gets a chance to do further auth work
        if ($this->hasEventHandler('OnAuthenticate')) {
            $this->raiseEvent('OnAuthenticate', $this, $this->application);
        }
    }

    /**
     * Returns AccessToken lifespan in seconds.
     *
     * @return null|string
     */
    public function getAuthExpire()
    {
        $cookie = $this->getRequest()->getCookies()->itemAt($this->getUserKey() . '_kce');
        if ($cookie instanceof THttpCookie) {
            return $cookie->getValue();
        }
    }

    public function saveKeycloakTokens(AccessTokenInterface $token, bool $saveUserToCookie): void
    {
        $accessToken = $token->getToken();
        $refreshToken = $token->getRefreshToken();

        $expire = $token->getExpires() - time();

        $this->addTokenCookies($accessToken, $refreshToken, $expire);

        $session = $this->getSession();
        $session->add('AuthExpireTime', $token->getExpires());
    }

    /**
     * @param [type] $cookieExpireTime
     */
    public function addTokenCookies(string $accessToken, string $refreshToken, string $expireCookieValue, $cookieExpireTime = null): void
    {
        $accessCookie = new THttpCookie($this->getUserKey() . '_kca', $accessToken);
        $accessCookie->setHttpOnly(true);
        $accessCookie->setSameSite(THttpCookieSameSite::Strict);
        $accessCookie->setSecure(SECURED_COOKIES);

        $refreshCookie = new THttpCookie($this->getUserKey() . '_kcr', $refreshToken);
        $refreshCookie->setHttpOnly(true);
        $refreshCookie->setSameSite(THttpCookieSameSite::Strict);
        $refreshCookie->setSecure(SECURED_COOKIES);

        $expiresCookie = new THttpCookie($this->getUserKey() . '_kce', $expireCookieValue);
        $expiresCookie->setHttpOnly(true);
        $expiresCookie->setSameSite(THttpCookieSameSite::Strict);
        $expiresCookie->setSecure(SECURED_COOKIES);

        $cookies = $this->getResponse()->getCookies();

        if (null !== $cookieExpireTime) {
            $accessCookie->setExpire($cookieExpireTime);
            $refreshCookie->setExpire($cookieExpireTime);
            $expiresCookie->setExpire($cookieExpireTime);
        }

        $cookies->add($accessCookie);
        $cookies->add($refreshCookie);
        $cookies->add($expiresCookie);
    }

    public function saveAppCookies(IUser $user, $expire): void
    {
        if ($expire > 0) {
            $cookie = new THttpCookie($this->getUserKey(), '');
            $cookie->setHttpOnly(true);
            $cookie->setExpire(time() + $expire);
            $this->userManager->saveUserToCookie($cookie);
            $this->getResponse()->getCookies()->add($cookie);
            $this->setAuthExpire($expire);
        }

        $loginTokenHash = sha1($user->getLoginToken() . getenv('APP_UNIQUE_KEY'));
        $levelHash = sha1($user->getUserLevel() . getenv('APP_UNIQUE_KEY'));

        $cookieLoginToken = new THttpCookie($this->getUserKey() . '_login_hash', $loginTokenHash);
        $cookieLevel = new THttpCookie($this->getUserKey() . '_level_hash', $levelHash);

        $this->getApplication()->getResponse()->getCookies()->add($cookieLoginToken);
        $this->getApplication()->getResponse()->getCookies()->add($cookieLevel);
    }

    /**
     * onLogin event is raised when a user logs in.
     *
     * @param TUser $user user being logged in
     *
     * @since 4.2.0
     */
    public function onLogin($user)
    {
        $this->raiseEvent('onLogin', $this, $user);
        $this->onLoginSuccess();
    }

    /**
     * onLoginFailed event is raised when a user login fails.
     *
     * @param string $username username trying to log in
     *
     * @since 4.2.0
     */
    public function onLoginFailed($username)
    {
        $UsersController = new UsersController();
        $UsersController->log(0, 'Anonymous', LoggerMessages::WRONG_ACCOUNT . ' User:' . strtolower($username));

        throw new MTRpcException('wrong_username_or_password', -33314);
    }

    /**
     * Logs out a user.
     * User session will be destroyed after this method is called.
     *
     * @throws TConfigurationException if session module is not loaded
     */
    public function logout()
    {
        if (($session = $this->getSession()) === null) {
            throw new TConfigurationException('authmanager_session_required');
        }

        $application = $this->getApplication();
        $application->getUser()->setIsGuest(true);
        $session->destroy();

        $cookie = new THttpCookie($this->getUserKey(), '');
        $cookies = $this->getResponse()->getCookies();
        $cookies->add($cookie);

        $this->addTokenCookies($accessToken = '', $refreshToken = '', $expireCookieValue = '', (time() - 3600));
    }

    /**
     * Performs login redirect if authorization fails.
     * This is the event handler attached to application's EndRequest event.
     * Do not call this method directly.
     *
     * @param mixed $sender sender of the event
     * @param mixed $param event parameter
     */
    public function leave($sender, $param)
    {
        $application = $this->getApplication();
        if (401 === $application->getResponse()->getStatusCode()) {
            $service = $application->getService();
            if ($service instanceof TPageService) {
                $returnUrl = $application->getRequest()->getRequestUri();
                $this->setReturnUrl($returnUrl);

                // Adds to cookie requested uri when response is (HTTP) 401 Unauthorized
                // Sets the URL that the browser should be redirected to next time when login succeeds.
                $this->setRequestedUrl($returnUrl);

                $url = 'index.php?page=' . $this->getLoginPage();
                $application->getResponse()->redirect($url);
            }
        }
    }

    /**
     * @param string $url
     */
    public function setRequestedUrl(?string $url)
    {
        $path = strstr($url, 'index');

        $requestedUrlCookie = new THttpCookie($this->getUserKey() . '_kcu', $path);
        $requestedUrlCookie->setHttpOnly(false);
        $cookies = $this->getResponse()->getCookies();
        $cookies->add($requestedUrlCookie);
    }

    /**
     * @return string
     */
    public function getRequestedtUrl(): ?string
    {
        $requestedUri = isset($_COOKIE[$this->getUserKey() . '_kcu']) ? $_COOKIE[$this->getUserKey() . '_kcu'] : null;
        setcookie($this->getUserKey() . '_kcu', '', time() - 3600);

        return $requestedUri;
    }

    public function initUserSession() {}

    /**
     * Logs in a user with username and password.
     * The username and password will be used to validate if login is successful.
     * If yes, a user object will be created for the application.
     *
     * @param string $username username
     * @param string $password password
     * @param int $expire number of seconds that automatic login will remain effective. If 0, it means user logs out when session ends. This parameter is added since 3.1.1.
     *
     * @return bool if login is successful
     */
    public function doLogin(ResourceOwnerInterface $user, AccessTokenInterface $token): bool
    {
        $userDetails = $user->toArray();
        $username = $userDetails['preferred_username'];

        if ($this->userManager->validateUser($username, $password = '')) {
            if (($user = $this->userManager->getUser($username)) === null) {
                return false;
            }

            $this->regenerateSessionUser($user, $token);
            $this->onLogin($user);

            return true;
        }
        $this->onLoginFailed($username);

        return false;
    }

    public function regenerateSessionUser(IUser $user, AccessTokenInterface $accessToken): IUser
    {
        try {
            if (!$accessToken->getRefreshToken()) {
                $accessToken = $this->authManager->exchangeToken($accessToken->getToken());
            }

            $user->setAccessToken($accessToken);
            $this->updateSessionUser($user);
            $user->setAccessToken($accessToken);
            $this->getApplication()->setUser($user);
            $this->saveKeycloakTokens($accessToken, true);
            $this->saveAppCookies($user, $accessToken->getExpires() - time());

            return $user;
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    private function getKeycloakUser(IUser &$user, AccessTokenInterface $accessToken): ?ResourceOwnerInterface
    {
        try {
            // kc will be used for scopes
            $kcUser = $this->authManager->getKeycloakUser(
                $accessToken
            );

            return $kcUser;
        } catch (Exception $ex) {
            // will catch all errors returned by keycloak
            // error="invalid_token", error_description="User session not found or doesn't have client attached on it"
            // error="invalid_token", error_description="Token verification failed"
            $user->setIsGuest(true);
        }
    }
}
