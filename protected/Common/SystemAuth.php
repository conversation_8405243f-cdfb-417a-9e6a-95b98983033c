<?php

namespace TF\Application\Common;

use Exception;
use Guz<PERSON><PERSON>ttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\RequestException;
use Guz<PERSON>Http\Exception\ServerException;
use Guz<PERSON>Http\Exception\TooManyRedirectsException;
use League\OAuth2\Client\Token\AccessToken;
use League\OAuth2\Client\Token\AccessTokenInterface;
use TF\Application\Common\Interfaces\SystemAuthProviderInterface;

abstract class SystemAuth implements SystemAuthProviderInterface
{
    protected $httpClient;

    public function __construct()
    {
        $this->httpClient = new Client([
            'base_uri' => $this->getAuthServerUrl(),
        ]);
    }

    /**
     * @return string
     */
    public function getToken(): AccessTokenInterface
    {
        try {
            $response = $this->httpClient->request('POST', $this->getTokenUrl(), [
                'form_params' => [
                    'grant_type' => 'client_credentials',
                    'client_id' => $this->getCLientId(),
                    'client_secret' => $this->getClientSecret(),
                ],
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            return new AccessToken($data);
        } catch (RequestException $exception) {
            throw $exception;
        }
    }

    /**
     * Exception handling and rethrowing is for debug reason.
     *
     * @see https://datatracker.ietf.org/doc/html/rfc7662
     * @see https://docs.guzzlephp.org/en/latest/quickstart.html#exceptions
     *
     * @throws Exception
     */
    public function introspectToken(string $accessToken)
    {
        $credentials = base64_encode($this->getCLientId() . ':' . $this->getClientSecret());

        try {
            $response = $this->httpClient->request('POST', $this->getTokenIntrospectUrl(), [
                'headers' => [
                    'Authorization' => 'Basic ' . $credentials,
                ],
                'form_params' => [
                    'token' => $accessToken,
                ],
            ]);

            $response = json_decode($response->getBody()->getContents());

            return $response;
        } catch (ClientException|ServerException $exception) {
            // BadResponseException (ServerException status 500 and ClientException status 400)
            $response = $exception->getResponse();

            throw new Exception($response->getReasonPhrase(), $response->getStatusCode(), $exception);
        } catch (TooManyRedirectsException $exception) {
            throw new Exception('Too many redirects', $exception->getCode(), $exception);
        } catch (ConnectException $exception) {
            $handlerContext = $exception->getHandlerContext();
            if ($handlerContext['errno'] ?? 0) {
                // this is the lowlevel error code, not the HTTP status code!!!
                // for example 6 for "Couldn't resolve host" (for libcurl)
                $errno = (int) ($handlerContext['errno']);
            }
            // get a description of the error
            $errorMessage = $handlerContext['error'] ?? $exception->getMessage();

            throw new Exception($errorMessage, $exception->getCode(), $exception);
        } catch (Exception $exception) {
            // fallback, in case of other exception
            throw $exception;
        }
    }

    abstract protected function getCLientId(): string;

    abstract protected function getClientSecret(): string;

    abstract protected function getRealm(): string;

    abstract protected function getAuthServerUrl(): string;

    abstract protected function getTokenIntrospectUrl(): string;
}
