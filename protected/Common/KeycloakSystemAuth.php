<?php

namespace TF\Application\Common;

class KeycloakSystemAuth extends SystemAuth
{
    protected function getCLientId(): string
    {
        return getenv('KEYCLOAK_M2M_CLIENT_ID');
    }

    protected function getClientSecret(): string
    {
        return getenv('KEYCLOAK_M2M_CLIENT_SECRET');
    }

    protected function getRealm(): string
    {
        return getenv('KEYCLOAK_REALM');
    }

    protected function getAuthServerUrl(): string
    {
        return getenv('KEYCLOAK_AUTH_SERVER_URL');
    }

    protected function getTokenUrl()
    {
        return 'realms/' . $this->getRealm() . '/protocol/openid-connect/token';
    }

    protected function getTokenIntrospectUrl(): string
    {
        return $this->getAuthServerUrl() . '/realms/' . $this->getRealm() . '/protocol/openid-connect/token/introspect';
    }
}
