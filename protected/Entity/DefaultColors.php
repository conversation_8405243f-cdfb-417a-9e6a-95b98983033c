<?php

namespace TF\Application\Entity;

use TF\Application\Entity\Traits\Timestampable;

class DefaultColors extends MTActiveRecord
{
    use Timestampable;

    public const TABLE = 'su_default_colors';

    public int $id;
    public string $category;
    public string $value;
    public $colors;

    public static function finder($className = __CLASS__)
    {
        return parent::finder($className);
    }

    public static function getCategories(): array
    {
        $conn = self::getRecordManager()->getDbConnection();

        $sql = 'SELECT DISTINCT category FROM ' . self::TABLE;
        $rowsCmd = $conn->createCommand($sql);

        $rows = $rowsCmd->query()->readAll();

        return array_column($rows, 'category');
    }
}
