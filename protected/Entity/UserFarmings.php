<?php

namespace TF\Application\Entity;

use TF\Application\Entity\Traits\Timestampable;

/**
 * http://www.pradoframework.net/demos/quickstart/?page=Database.ActiveRecord.
 */
class UserFarmings extends MTActiveRecord
{
    use Timestampable;

    public const TABLE = 'su_users_farming';

    public $id;
    public $user_id;
    public $name;
    public $is_system;
    public $company;
    public $bulstat;
    public $group_id;
    public $address;
    public $company_address;
    public $mol;
    public $iban_arr;
    public $mol_egn;
    public $representative_id;
    public $rko_number;
    public $ao_id;
    public $ao_db;
    public $is_from_ao_migration;
    public $post_payment_fields;
    public $farming_mol_phone;
    public $uuid;
    public $company_ekatte;

    public static function finder($className = __CLASS__)
    {
        return parent::finder($className);
    }

    public function getId()
    {
        return $this->id;
    }
}
