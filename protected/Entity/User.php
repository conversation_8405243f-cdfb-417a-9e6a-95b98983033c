<?php

namespace TF\Application\Entity;

use Prado\Data\ActiveRecord\TActiveRecordCriteria;
use TF\Application\Entity\Traits\Timestampable;

/**
 * http://www.pradoframework.net/demos/quickstart/?page=Database.ActiveRecord.
 */
class User extends MTActiveRecord
{
    use Timestampable;

    public const TABLE = 'su_users';
    public const SUB_USER_LEVEL = 3;
    public const ORGANIZATION_USER_LEVEL = 2;

    public $id;
    public $password;
    public $username;
    public $address;
    public $phone;
    public $email;
    public $comment;
    public $is_superadmin;
    public $hash;
    public $can_create;
    public $level;
    public $group_id;
    public $active;
    public $server;
    public $start_date;
    public $due_date;
    public $entry_flag;

    public $entries_left;
    public $date_flag;
    public $map_type;
    public $is_trial;
    public $allowed_farmings;

    public $track_username;
    public $track_password;
    public $creation_date;
    public $last_login_date;
    public $last_login_ip;

    public $app_version;
    public $app_critical_upd;
    public $paid_support;
    public $track_token;
    public $login_token;

    public $salesperson;
    public $salesperson_id;
    public $ekatte_count;
    public $total_plot_area;
    public $paid_support_start_date;
    public $paid_support_due_date;

    public $name;
    public $database;
    public $parent_id;
    public $ao_id;
    public $ao_db;

    public $identity_number;
    public $keycloak_uid;
    public $gs_organization_id;
    public $waiting_gs_integration;
    public $is_from_ao_migration;
    public $create_cms_contracts;

    public static function finder($className = __CLASS__)
    {
        return parent::finder($className);
    }

    public function getId()
    {
        return $this->id;
    }

    public function getGroupId()
    {
        return $this->group_id;
    }

    public function getLevel()
    {
        return $this->level;
    }

    public function getDatabase()
    {
        return $this->database;
    }

    public function getUsername()
    {
        return $this->username;
    }

    /**
     * @return array
     */
    public function getSubUsers()
    {
        $criteria = new TActiveRecordCriteria();
        $criteria->Condition = 'group_id = :group_id AND level = :level';
        $criteria->Parameters[':group_id'] = $this->getGroupId();
        $criteria->Parameters[':level'] = self::SUB_USER_LEVEL;

        return User::finder()->findAll($criteria);
    }

    public function getOrganizationUsers()
    {
        $criteria = new TActiveRecordCriteria();
        $criteria->Condition = 'group_id = :group_id AND keycloak_uid IS NOT NULL AND level != :level';
        $criteria->Parameters[':group_id'] = $this->getGroupId();
        $criteria->Parameters[':level'] = self::ORGANIZATION_USER_LEVEL;

        return User::finder()->findAll($criteria);
    }

    public function getUserRightsForOrganization(array $filter = [])
    {
        $criteria = new TActiveRecordCriteria();
        $criteria->Condition = 'user_id = :user_id AND group_id = :group_id';
        $criteria->Parameters[':user_id'] = $this->getId();
        $criteria->Parameters[':group_id'] = $this->getGroupId();

        if (isset($filter['rights']) && is_array($filter['rights'])) {
            $criteria->Condition .= ' AND right_id IN (:right_ids)';
            $criteria->Parameters[':right_ids'] = implode(',', $filter['rights']);
        }

        return UserRights::finder()->findAll($criteria);
    }

    public function deleteUserRights(array $filter = [])
    {
        $criteria = new TActiveRecordCriteria();
        $criteria->Condition = 'user_id = :user_id AND group_id = :group_id';
        $criteria->Parameters[':user_id'] = $this->getId();
        $criteria->Parameters[':group_id'] = $this->getGroupId();

        if (isset($filter['rights']) && is_array($filter['rights']) && count($filter['rights'])) {
            $rigtsAsStr = implode(',', $filter['rights']);
            $criteria->Condition .= " AND right_id IN ({$rigtsAsStr})";
        }
        UserRights::finder()->deleteAll($criteria);
    }
}
