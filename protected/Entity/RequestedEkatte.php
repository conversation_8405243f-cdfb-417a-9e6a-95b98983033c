<?php

namespace TF\Application\Entity;

use PDO;
use TF\Application\Common\Config;
use TF\Application\Entity\Traits\Timestampable;

/**
 * http://www.pradoframework.net/demos/quickstart/?page=Database.ActiveRecord.
 */
class RequestedEkatte extends MTActiveRecord
{
    use Timestampable;

    public const STATUS_REQUESTED = 'Requested';
    public const STATUS_RECEIVED = 'Received';
    public const STATUS_FOR_SYNC = 'For sync';
    public const STATUS_FAILED = 'Failed';

    public const TABLE = 'su_requested_ekattes';

    public $id;
    public $ekatte_code;
    public $ekatte_name;
    public $user_id;
    public $group_id;
    public $status;
    public $kvs_store_uuid;

    public static function finder($className = __CLASS__)
    {
        return parent::finder($className);
    }

    public static function list(int $groupId, array $filters = [], int $page = null, int $rows = null, string $sort = null, string $order = null)
    {
        $conn = self::getRecordManager()->getDbConnection();

        $fileStatusesSql = 'CASE suf.status ';
        foreach (Config::$FILE_STATUSES_MAP as $key => $value) {
            $fileStatusesSql .= "WHEN {$key} THEN '{$value}' ";
        }
        $fileStatusesSql .= "ELSE 'Processing error' END";

        $sql = "WITH 
            requested_ekattes AS (
                SELECT DISTINCT ON (ekatte_code)
                    ekatte_code,
                    ekatte_name,
                    user_id,
                    group_id,
                    status,
                    kvs_store_uuid,
                    updated_at
                FROM 
                    su_requested_ekattes
                WHERE
                    group_id = :group_id
                ORDER BY 
                    ekatte_code,
                    id DESC
            ),
            user_files AS (
                SELECT DISTINCT ON (suf.ekate)
                    suf.id,
                    re.ekatte_code,
                    {$fileStatusesSql} AS status,
                    suf.date_uploaded
                FROM 
                    requested_ekattes AS re
                JOIN su_users_files AS suf
                    ON suf.group_id = re.group_id
                    AND suf.ekate = re.ekatte_code
                    AND re.status = 'Received'::requested_ekatte_status_enum
                ORDER BY
                    suf.ekate,
                    suf.date_uploaded DESC
            )
            SELECT
                se.ekatte_code,
                se.ekatte_name,
                COALESCE(uf.status, re.status::text) AS status,
                uf.id as file_id,
                re.updated_at as last_sync_date
            FROM 
                su_ekatte AS se
            LEFT JOIN requested_ekattes AS re
                ON re.ekatte_code = se.ekatte_code
            LEFT JOIN user_files AS uf
                ON uf.ekatte_code =se.ekatte_code
            WHERE TRUE
        ";

        $params = [
            [
                'key' => ':group_id',
                'value' => $groupId,
                'type' => PDO::PARAM_INT,
            ],
        ];

        if (isset($filters['in_process'])) {
            if ($filters['in_process']) {
                $sql .= ' AND COALESCE(uf.status, re.status::text) <> :success_status';
                $params[] = [
                    'key' => ':success_status',
                    'value' => Config::$FILE_STATUSES_MAP[SUCCESSFULLY_TREATED],
                    'type' => PDO::PARAM_STR,
                ];
            } else {
                $filters['added'] = true;
            }
        }

        if (isset($filters['added'])) {
            $sql .= ' AND COALESCE(uf.status, re.status::text) ' . ($filters['added'] ? 'NOTNULL' : 'IS NULL');
        }

        if (isset($sort, $order)) {
            $sql .= " ORDER BY {$sort} {$order}";
        } else {
            $sql .= ' ORDER BY
                uf.status ASC NULLS LAST,
                re.ekatte_name ASC NULLS LAST,
                se.ekatte_name ASC
            ';
        }

        $totalSql = "SELECT COUNT(*) FROM ({$sql}) rows";
        $totalSqlParams = $params;

        if (isset($page, $rows)) {
            $offset = ($page - 1) * $rows;
            $sql .= ' LIMIT :rows OFFSET :offset';

            $params[] = [
                'key' => ':rows',
                'value' => $rows,
                'type' => PDO::PARAM_INT,
            ];

            $params[] = [
                'key' => ':offset',
                'value' => $offset,
                'type' => PDO::PARAM_INT,
            ];
        }

        $rowsCmd = $conn->createCommand($sql);
        foreach ($params as $param) {
            $rowsCmd->bindParameter($param['key'], $param['value'], $param['type']);
        }
        $rows = $rowsCmd->query()->readAll();
        $rows = array_map(function ($row) {
            $row['file_id'] = isset($row['file_id']) ? (int) $row['file_id'] : null;

            return $row;
        }, $rows);

        $totalCmd = $conn->createCommand($totalSql);
        foreach ($totalSqlParams as $param) {
            $totalCmd->bindParameter($param['key'], $param['value'], $param['type']);
        }
        $total = $totalCmd->query()->readColumn(0);

        return [
            'rows' => $rows,
            'total' => isset($total) ? (int) $total : 0,
        ];
    }
}
