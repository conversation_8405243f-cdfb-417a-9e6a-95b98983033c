<?php

namespace TF\Application\Entity;

use Exception;
use Prado;
use TF\Application\Common\Config;

class Layer extends MTActiveUserRecord
{
    protected $tableName;
    protected $columns = [];
    protected $userLayer;

    public function __construct($data = [], $connection = null)
    {
        parent::__construct($data, $connection);
    }

    public function __get($name)
    {
        return $this->$name;
    }

    public function __set($name, $value)
    {
        $this->$name = $value;
    }

    public static function getInstance($tableName, $data = [], $connection = null): Layer
    {
        $instance = new self($data, $connection);
        $instance->userLayer = $instance->getUserLayerRelation($tableName);

        if (!$instance->userLayer) {
            throw new Exception('No user layer found for table: ' . $tableName);
        }

        $instance->setTableName($tableName);

        return $instance;
    }

    public function table()
    {
        return $this->getTableName();
    }

    public function mergeAttributes(array $layers, $matchColumns = [])
    {
        $definitions = json_decode($this->userLayer->definitions, true);
        $filteredDefinitions = $this->filterDefinitionForSplit($definitions);

        $mergedAttributes = [];
        if ($matchColumns) {
            $mergedAttributes = array_fill_keys(array_keys($matchColumns), '');
        }

        foreach ($filteredDefinitions as $definition) {
            $colName = $definition['col_name'];
            $colCategory = $definition['col_category'];

            if ($matchColumns && in_array($colName, $matchColumns)) {
                $colName = array_search($colName, $matchColumns);
            }

            $values = array_map(function ($layer) use ($colName) {
                return $layer->$colName;
            }, $layers);

            $isGeneric = in_array($colCategory, $GLOBALS['Layers']['genericColumnCategories']);
            if ($isGeneric || Config::LAYER_COLUMN_CATEGORY_NAME === $colCategory) {
                if (Config::LAYER_COLUMN_CATEGORY_NUMBER === $colCategory) {
                    $mergedAttributes[$colName] = (1 === count(array_unique($values))) ? (float) $values[0] : null;
                } elseif (Config::LAYER_COLUMN_CATEGORY_DATE === $colCategory) {
                    $mergedAttributes[$colName] = (1 === count(array_unique($values))) ? $values[0] : null;
                } elseif (Config::LAYER_COLUMN_CATEGORY_BOOLEAN === $colCategory) {
                    $uniqueValues = array_unique(array_filter($values));
                    if (1 === count($uniqueValues)) {
                        $mergedAttributes[$colName] = $uniqueValues[0];
                    } else {
                        $mergedAttributes[$colName] = false;
                    }
                } else {
                    $maxLength = $this->getColumnMaxLength($this->getTableName(), $definition['col_name']);
                    $mergedAttributes[$colName] = implode(', ', array_unique(array_filter($values)));
                    $mergedAttributes[$colName] = substr($mergedAttributes[$colName], 0, $maxLength);
                }
            } else {
                $uniqueValues = array_unique(array_filter($values));
                if (1 === count($uniqueValues)) {
                    $mergedAttributes[$colName] = $uniqueValues[0];
                } else {
                    $mergedAttributes[$colName] = '';
                }
            }
        }

        return [$mergedAttributes];
    }

    public function splitAttributes(Layer $layer, int $newLayersCount)
    {
        $definitions = json_decode($this->userLayer->definitions, true);

        $filteredDefinitions = $this->filterDefinitionForSplit($definitions);

        $attributes = [];

        for ($index = 0; $index < $newLayersCount; $index++) {
            $layerAttributes = [];

            foreach ($filteredDefinitions as $definition) {
                $colName = $definition['col_name'];
                $colCategory = $definition['col_category'];

                if (Config::LAYER_COLUMN_CATEGORY_NAME === $colCategory) {
                    $maxLength = $this->getColumnMaxLength($this->getTableName(), $colName);
                    $layerAttributes[$colName] = substr($layer->$colName . '_' . ($index + 1), 0, $maxLength);
                } else {
                    $layerAttributes[$colName] = $layer->$colName;
                }
            }

            $attributes[] = $layerAttributes;
        }

        return $attributes;
    }

    public function withDefinitions()
    {
        $definitions = json_decode($this->userLayer->definitions, true);

        foreach ($definitions as $definition) {
            $colName = $definition['col_name'];
            if (property_exists($this, $colName)) {
                $this->$colName = $definition;
            }
        }

        return $this;
    }

    public function filterDefinitionForSplit($definitions)
    {
        return array_filter($definitions, function ($definition) {
            return
                !$definition['col_virtual']
                && Config::LAYER_COLUMN_CATEGORY_SLOPE !== $definition['col_category']
                && Config::LAYER_COLUMN_CATEGORY_GEOM !== $definition['col_category']
                && Config::LAYER_COLUMN_CATEGORY_GID !== $definition['col_category'];
        });
    }

    public function findDefinitionByCategory($definitions, $category)
    {
        foreach ($definitions as $definition) {
            if ($definition['col_category'] === $category) {
                return $definition;
            }
        }

        return;
    }

    public function setTableName($tableName)
    {
        $this->tableName = $tableName;
    }

    public function getTableName()
    {
        return $this->tableName;
    }

    private function getColumnMaxLength($table, $colName): int
    {
        $sql = 'SELECT character_maximum_length FROM information_schema.columns WHERE table_name = :tableName AND column_name = :columnName;';
        $cmd = $this->getDbConnection()->createCommand($sql);
        $cmd->bindValue(':tableName', $table);
        $cmd->bindValue(':columnName', $colName);

        return $cmd->query()->read()['character_maximum_length'] ?? 255;
    }

    private function getUserLayerRelation($tableName): ?UserLayers
    {
        return UserLayers::getLayerByTableName($tableName, Prado::getApplication()->getUser()->getGroupID());
    }
}
