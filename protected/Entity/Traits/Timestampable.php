<?php

namespace TF\Application\Entity\Traits;

use DateTime;

/**
 * Adds created at and updated at timestamps to entities.
 */
trait Timestampable
{
    public $timestamps = true;

    public $created_at;
    public $updated_at;

    /**
     * Get the value of created_at.
     */
    public function getCreatedAt()
    {
        return $this->created_at;
    }

    /**
     * Get the value of updated_at.
     */
    public function getUpdatedAt()
    {
        return $this->updated_at;
    }

    /**
     * Set the value of created_at.
     *
     * @return self
     */
    public function setCreatedAt()
    {
        $this->created_at = (new DateTime())->format(DateTime::ATOM);

        return $this;
    }

    /**
     * Set the value of updated_at.
     *
     * @return self
     */
    public function setUpdatedAt()
    {
        $this->updated_at = (new DateTime())->format(DateTime::ATOM);

        return $this;
    }
}
