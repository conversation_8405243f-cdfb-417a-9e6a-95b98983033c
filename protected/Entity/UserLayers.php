<?php

namespace TF\Application\Entity;

use Exception;
use InvalidArgumentException;
use TF\Application\Common\Config;
use TF\Application\Entity\Traits\Timestampable;
use TF\Engine\Kernel\StringHelper;

class UserLayers extends MTActiveRecord
{
    use Timestampable;

    public const TABLE = 'su_users_layers';

    /** @var int|string $id */
    public $id;
    /** @var int|string $layer_type */
    public $layer_type;
    public ?int $user_id = null;
    public ?int $farming = null;
    public ?int $year = null;
    public ?int $transparency = null; // TODO: GPS-4321 , remove this property
    public ?int $position = null;
    public ?int $group_id = null;
    public ?string $color; // TODO: GPS-4321 , remove this property

    public ?string $style = null; // TODO: GPS-4321 , remove this property
    public ?string $definitions = null;
    public ?string $date_created = null;
    public ?string $name = null;
    public ?string $table_name = null;
    public ?string $label_name = null; // TODO: GPS-4321 , remove this property
    public ?string $border_color = null; // TODO: GPS-4321 , remove this property
    public ?string $extent = null;
    public ?bool $is_from_ao_migration = null;
    public ?bool $tags = null; // TODO: GPS-4321 , remove this property
    public ?bool $border_only = null; // TODO: GPS-4321 , remove this property
    public ?bool $is_exist = null;

    public function relations()
    {
        return [
            'user' => [self::HAS_ONE, User::class, 'user_id'],
        ];
    }

    public function getDatabase()
    {
        return $this->database;
    }

    public function getId()
    {
        return $this->id;
    }

    public function getStyles(): array
    {
        return LayerStyles::finder()->findAll('table_name = :table_name', [':table_name' => $this->table_name]);
    }

    public function isRemote(): bool
    {
        return $GLOBALS['Layers']['remoteTables'][$this->layer_type] ? true : false;
    }

    public function hasPhysicalTable(): bool
    {
        return $GLOBALS['Layers']['customLayers'][$this->layer_type]
            ? $GLOBALS['Layers']['customLayers'][$this->layer_type]['has_physical_table']
            : true;
    }

    public function getUser(): ?User
    {
        return User::finder()->findByPk($this->user_id);
    }

    /**
     * Returns the definitions of the current layer.
     *
     * @return array[array{col_name:string,
     * col_title:string,
     * col_visible:bool,
     * col_personalizable:bool,
     * col_category:string,
     * col_multiedit:bool,
     * col_singleedit:bool,
     * col_sortable:bool,
     * col_exportable:bool,
     * col_copyable:bool,
     * col_virtual:bool,
     * col_expression:string,
     * col_filter_selection_type:string,
     * col_reference:string}]
     */
    public function getDefinitions()
    {
        return $this->definitions ? json_decode($this->definitions, true) : [];
    }

    /**
     * Returns the definition matched by the given category for the current layer.
     *
     * @return array{col_name:string,
     * col_title:string,
     * col_visible:bool,
     * col_personalizable:bool,
     * col_category:string,
     * col_multiedit:bool,
     * col_singleedit:bool,
     * col_sortable:bool,
     * col_exportable:bool,
     * col_copyable:bool,
     * col_virtual:bool,
     * col_expression:string,
     * col_filter_selection_type:string,
     * col_reference:string} | null
     */
    public function getDefinitionByCategory(string $category): ?array
    {
        $definitions = $this->getDefinitions();
        foreach ($definitions as $definition) {
            if (
                $definition['col_category'] === $category
            ) {
                return $definition;
            }
        }

        return null;
    }

    public static function getColumns(array $definitions)
    {
        return array_column($definitions, 'col_name');
    }

    /**
     * This method matches the columns from $definitionsA with the columns from $definitionsB by category.
     *
     * Two columns are considered as matching when:
     *  - they have the same category and the category is not generic (e.g. gid, crop, ekatte)
     *  - they have the same category, the category is generic (e.g. text, number, bool) and the column names are the same
     *
     * @return array an array where the keys are column names of from $definitionsA and the values are the matched column names from $definitionsB
     */
    public static function matchColumns(array $definitionsA, array $definitionsB)
    {
        $genericColumnCategories = $GLOBALS['Layers']['genericColumnCategories'];

        if (!count($definitionsA) || !count($definitionsB)) {
            throw new Exception('Invalid layer definitions');
        }

        $matchingColumns = [];

        foreach ($definitionsA as $definitionA) {
            foreach ($definitionsB as $definitionB) {
                // Categories are different
                if ($definitionA['col_category'] !== $definitionB['col_category']) {
                    continue;
                }

                // Categories are the same but are generic (e.g. text, number) and the column names are different
                if (
                    in_array($definitionA['col_category'], $genericColumnCategories)
                    && $definitionA['col_name'] !== $definitionB['col_name']
                ) {
                    continue;
                }

                // Categories are the same and not generic; Categories are the same and genetric and the column names match
                $definitionAColumnName = $definitionA['col_name'];
                $definitionBColumnName = $definitionB['col_name'];
                $matchingColumns[$definitionAColumnName] = $definitionBColumnName;
            }
        }

        return $matchingColumns;
    }

    /**
     * @param array $definitions The definitions to filter
     * @param array $filters The filters to apply
     *                       The filters array contain multiple associative arrays.
     *                       The logic operation between the the values of each inner array is AND and the logic operation between the outer arrays is OR.
     *                       example: [['col_visible' => 'true', 'col_exportable' => 'false'], ['col_category' => 'number']
     *                       The above example will return all definitions that are visible AND not exportable AND ALL definitions that are of category number.
     */
    public static function filterDefinitions(array $definitions, array $filters)
    {
        $filters ??= [];
        $definitions ??= [];

        if (!count($filters)) {
            return $definitions;
        }

        $definitions = array_filter($definitions, function ($definition) use ($filters) {
            $outerMatch = false;

            foreach ($filters as $filter) {
                $innerMatch = true;
                foreach ($filter as $key => $value) {
                    $innerMatch = $innerMatch && $definition[$key] === $value;
                }

                $outerMatch = $outerMatch || $innerMatch;
            }

            return $outerMatch;
        });

        return array_values($definitions);
    }

    public static function finder($className = __CLASS__)
    {
        return parent::finder($className);
    }

    /**
     * Returns the definition matched by the given layer type and category.
     */
    public static function getDefinitionByTypeAndCategory($layerType, string $category): ?array
    {
        $definitions = self::getDefinitionsByType($layerType);
        foreach ($definitions as $definition) {
            if (
                $definition['col_category'] === $category
            ) {
                return $definition;
            }
        }

        return null;
    }

    /**
     * Get definitions by table name.
     *
     * @return array
     */
    public static function getDefinitionsByTableName(string $tableName, int $groupId)
    {
        $layer = self::getLayerByTableName($tableName, $groupId);

        if (!$layer) {
            return [];
        }

        return json_decode($layer->definitions, true);
    }

    /**
     * Get definitions by layer type.
     *
     * @param int|string $layerType
     *
     * @return array
     */
    public static function getDefinitionsByType($layerType)
    {
        return $GLOBALS['Layers']['definitions'][$layerType] ?? [];
    }

    /**
     * Get layer by id.
     *
     * @param int|string $layerId
     */
    public static function getLayerById($layerId): ?UserLayers
    {
        $physicalBlocksLayer = $GLOBALS['Layers']['customLayers'][Config::LAYER_TYPE_PHYSICAL_BLOCKS];
        $allowableFinalLayer = $GLOBALS['Layers']['customLayers'][Config::LAYER_TYPE_ALLOWABLE_FINAL];
        $cadastreLayer = $GLOBALS['Layers']['customLayers'][Config::LAYER_TYPE_CADASTRE];

        if ($layerId === $physicalBlocksLayer['id']) {
            $layer = new UserLayers();
            $layer->id = $physicalBlocksLayer['id'];
            $layer->layer_type = Config::LAYER_TYPE_PHYSICAL_BLOCKS;
            $layer->table_name = $physicalBlocksLayer['table_name'];
            $layer->definitions = json_encode(self::getDefinitionsByType(Config::LAYER_TYPE_PHYSICAL_BLOCKS));
            $layer->name = $physicalBlocksLayer['layer_name'];

            return $layer;
        }

        if ($layerId === $allowableFinalLayer['id']) {
            $layer = new UserLayers();
            $layer->id = $allowableFinalLayer['id'];
            $layer->layer_type = Config::LAYER_TYPE_ALLOWABLE_FINAL;
            $layer->table_name = $allowableFinalLayer['table_name'];
            $layer->definitions = json_encode(self::getDefinitionsByType(Config::LAYER_TYPE_ALLOWABLE_FINAL));
            $layer->name = $allowableFinalLayer['layer_name'];

            return $layer;
        }

        // Handle cadastre layer - layer with no table but API-based data
        if ($layerId === $cadastreLayer['id']) {
            $layer = new UserLayers();
            $layer->id = $cadastreLayer['id'];
            $layer->layer_type = Config::LAYER_TYPE_CADASTRE;
            $layer->table_name = $cadastreLayer['table_name'];
            $layer->definitions = json_encode(self::getDefinitionsByType(Config::LAYER_TYPE_CADASTRE));
            $layer->name = $cadastreLayer['layer_name'];

            return $layer;
        }

        $layer = UserLayers::finder()->find('id=:id', [':id' => $layerId]);

        return $layer;
    }

    /**
     * Get layer by tableName.
     */
    public static function getLayerByTableName(string $tableName, int $groupId): ?UserLayers
    {
        $physicalBlocksLayer = $GLOBALS['Layers']['customLayers'][Config::LAYER_TYPE_PHYSICAL_BLOCKS];
        $allowableFinalLayer = $GLOBALS['Layers']['customLayers'][Config::LAYER_TYPE_ALLOWABLE_FINAL];
        $cadastreLayer = $GLOBALS['Layers']['customLayers'][Config::LAYER_TYPE_CADASTRE];

        $layer = null;
        switch ($tableName) {
            case $physicalBlocksLayer['table_name']:
                $layer = self::getLayerById($physicalBlocksLayer['id']);

                break;
            case $allowableFinalLayer['table_name']:
                $layer = self::getLayerById($allowableFinalLayer['id']);

                break;
            case $cadastreLayer['table_name']:
                $layer = self::getLayerById($cadastreLayer['id']);

                break;
            default:
                $layer = self::finder()
                    ->find('table_name = :table_name AND group_id = :group_id', [':table_name' => $tableName, ':group_id' => $groupId]);
        }

        return $layer;
    }

    /**
     * This method returns all definitions from $definitionsB that are not present in $definitionsA.
     * The definitions are compared by col_name and col_category:
     *  - if there are definitions with the same col_name in both arrays, the definition is skipped
     *  - if there are definitions with the same non-generic col_category in both arrays, the definition is skipped.
     */
    public static function diffDefinitions(array $definitionsA, array $definitionsB): array
    {
        $defACategories = array_column($definitionsA, 'col_category');
        $defAColumns = array_column($definitionsA, 'col_name');
        $genericColumnCategories = $GLOBALS['Layers']['genericColumnCategories'];
        $diff = [];

        foreach ($definitionsB as $defB) {
            // Definition with the same non generic category already exists
            if (in_array($defB['col_category'], $defACategories) && !in_array($defB['col_category'], $genericColumnCategories)) {
                continue;
            }

            // Definition with same name already exists
            if (in_array($defB['col_name'], $defAColumns)) {
                continue;
            }

            if (!self::validateDefinition($defB)) {
                throw new Exception('Invalid column definition');
            }

            $diff[] = $defB;
        }

        return $diff;
    }

    public static function validateDefinition(
        array $columnDefinition,
        array $keysToSkip = []
    ): bool {
        $keysToCheck = array_diff($GLOBALS['Layers']['requiredDefinitionProps'], $keysToSkip);

        $nullableKeys = ['col_expression', 'col_filter_selection_type', 'col_reference'];
        foreach ($keysToCheck as $key) {
            if (in_array($key, $nullableKeys)) {
                if (!array_key_exists($key, $columnDefinition)) {
                    // The key is not set in the definition
                    return false;
                }

                if (is_null($columnDefinition[$key])) {
                    // Skip validation if null
                    continue;
                }

                if ('col_filter_selection_type' === $key) {
                    if (!in_array(
                        $columnDefinition[$key],
                        [
                            Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
                            Config::LAYER_COLUMN_SELECTION_TYPE_SINGLE,
                        ]
                    )) {
                        return false;
                    }
                }

                continue;
            }

            if ('col_category' === $key && !isset($GLOBALS['Layers']['columnTypesByCategory'][$columnDefinition['col_category']])) {
                // validate col_category property exists and it is a valid category

                return false;
            }

            if ('col_title' === $key && !strlen($columnDefinition[$key])) {
                // validate col_title property exists and its length is greater than 0

                return false;
            }

            if ('col_name' === $key) {
                // validate col_name property exists and its length is greater than 0
                // or col_name is empty but col_title is filled (in this case the transliteration of col_title might be used as col_name).

                if (!strlen($columnDefinition[$key] ?? '') && !strlen($columnDefinition['col_title'] ?? '')) {
                    // col_name is empty and col_title is empty

                    return false;
                }

                // col_name or col_title is filled => skip isset check
                continue;
            }

            if (!isset($columnDefinition[$key])) {
                return false;
            }
        }

        return true;
    }

    public static function validateDefinitions(array $definitions, array $keysToSkip = []): bool
    {
        foreach ($definitions as $definition) {
            if (!self::validateDefinition($definition, $keysToSkip)) {
                return false;
            }
        }

        return true;
    }

    public static function getLayerNameById($layerId)
    {
        return $GLOBALS['Layers']['layerName'][$layerId];
    }

    /**
     * Generate a PostgreSQL VALUES expression from data array and layer definitions.
     * Uses proper SQL escaping to prevent SQL injection attacks.
     *
     * @param array $values array of associative arrays, where keys are column names (matching those in $definitions) and values are the data for those columns
     * @param array $definitions Layer column definitions
     *
     * @return string PostgreSQL VALUES expression that can be used as FROM clause
     */
    public static function generateValuesExprByDefinitions(array $values, array $definitions): string
    {
        if (empty($values) || empty($definitions)) {
            throw new InvalidArgumentException('The $values and $definitions cannot be empty');
        }

        $columnTypesByCategory = $GLOBALS['Layers']['columnTypesByCategory'];
        $columnNames = array_column($definitions, 'col_name');
        $columnDefinitionsMap = array_combine($columnNames, $definitions);
        $stringHelper = new StringHelper();

        $valuesRows = [];

        foreach ($values as $row) {
            $valuesList = [];

            foreach ($columnNames as $columnName) {
                $columnDef = $columnDefinitionsMap[$columnName];
                $value = $row[$columnName] ?? null;

                // Handle NULL values
                if (null === $value) {
                    $valuesList[] = 'NULL';

                    continue;
                }

                // Format value based on column category and expected type
                $colCategory = $columnDef['col_category'];
                $expectedType = $columnTypesByCategory[$colCategory] ?? 'VARCHAR';

                if (Config::LAYER_COLUMN_CATEGORY_GEOM === $colCategory) {
                    // Handle geometry values - assume GeoJSON format
                    $escapedValue = $stringHelper->escapeLiteral($value);
                    $valuesList[] = "ST_GeomFromGeoJSON({$escapedValue})";
                } elseif (Config::LAYER_COLUMN_CATEGORY_BOOLEAN === $colCategory) {
                    // Handle boolean values
                    $valuesList[] = $value ? 'TRUE' : 'FALSE';
                } elseif (in_array($expectedType, ['NUMERIC', 'DOUBLE PRECISION', 'SERIAL PRIMARY KEY'])) {
                    // Handle numeric types
                    if (is_numeric($value) && is_finite((float)$value)) {
                        // Ensure the value is a valid number and cast to prevent injection
                        $numericValue = is_int($value) ? (int)$value : (float)$value;
                        $valuesList[] = (string)$numericValue;
                    } else {
                        $valuesList[] = 'NULL'; // Invalid numeric value
                    }
                } elseif (in_array($expectedType, ['TIMESTAMP'])) {
                    // Handle date/timestamp types
                    $escapedValue = $stringHelper->escapeLiteral($value);
                    $valuesList[] = "{$escapedValue}::{$expectedType}";
                } else {
                    // Handle string/text types (VARCHAR, TEXT, etc.)
                    $escapedValue = $stringHelper->escapeLiteral($value);
                    $valuesList[] = $escapedValue;
                }
            }

            $valuesRows[] = '(' . implode(', ', $valuesList) . ')';
        }

        $quotedColumnNames = array_map(function ($name) {
            return '"' . $name . '"';
        }, $columnNames);

        $columnsList = implode(', ', $quotedColumnNames);
        $valuesExpression = implode(', ', $valuesRows);

        return "(VALUES {$valuesExpression}) AS temp_values ({$columnsList})";
    }

    public function getFarmingName(): ?string
    {
        return UserFarmings::finder()->findByPk($this->farming)->name ?? null;
    }

    public function getFarmingYear(): ?int
    {
        return $GLOBALS['Farming']['years'][$this->year]['year'] ?? null;
    }

    protected function populateObject($data)
    {
        // TODO: remove!
        // unset(
        //     $data['color'],
        //     $data['label_name'],
        //     $data['coloring_type'],
        //     $data['coloring_attribute'],
        //     $data['coloring_layer_name'],
        //     $data['border_color'],
        //     $data['transparency'],
        //     $data['tags'],
        //     $data['border_only'],
        //     $data['style'],
        // );

        return parent::populateObject($data);
    }
}
