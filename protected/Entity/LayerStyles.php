<?php

namespace TF\Application\Entity;

use Exception;
use Prado\Prado;
use TF\Application\Common\Config;
use TF\Application\Common\MTUser;
use TF\Application\Entity\Traits\Timestampable;
use TF\Engine\Kernel\StringHelper;

class LayerStyles extends MTActiveUserRecord
{
    use Timestampable;

    public const BY_ATTRIBUTE_COLORING_TYPE = 'by attribute';
    public const SINGLE_COLORING_TYPE = 'single';

    public const FILL_COLOR = 'fill_color';
    public const BORDER_COLOR = 'border_color';

    public const TABLE = 'su_layer_styles';

    /** @var int $id */
    public $id;
    public $layer_id;
    public string $table_name;
    public string $type;
    public int $transparency;
    public ?string $fill_column_name;
    public ?string $fill_color;
    public ?string $border_column_name;
    public string $border_color;
    public int $border_width;
    public bool $border_only;
    public $labels;
    public int $label_size;
    public bool $tags;

    private $wrapCharacter = "\n";

    public static function finder($className = __CLASS__)
    {
        return parent::finder($className);
    }

    public static function generateDefaultStyle($layerType = null, $arrayFormat = true)
    {
        $stringHelper = new StringHelper();

        $labelName = [];
        if (isset($layerType)) {
            $nameDef = UserLayers::getDefinitionByTypeAndCategory($layerType, Config::LAYER_COLUMN_CATEGORY_NAME);
            $labelName = $nameDef ? [$nameDef['col_name']] : [];
        }

        if ($layerType && in_array($layerType, $GLOBALS['Layers']['defaultColorLayers'])) {
            $borderColor = $GLOBALS['Layers']['defaultBorderColorLayers'][$layerType];
            $fillColor = null;
            $labelName = $GLOBALS['Layers']['defaultLablesLayers'][$layerType];
        } else {
            $borderColor = Config::LAYER_BOUNDARY_DEFAULT_COLOR;
            $fillColor = '#' . $stringHelper->randomColorCode();
        }

        $style = [
            'type' => self::SINGLE_COLORING_TYPE,
            'transparency' => Config::LAYER_COLOR_DEFAULT_TRANSPARENCY,
            'fill_column_name' => null,
            'fill_color' => $fillColor,
            'border_column_name' => null,
            'border_color' => $borderColor,
            'border_width' => Config::LAYER_BOUNDARY_DEFAULT_BORDER_WIDTH,
            'border_only' => false,
            'labels' => $labelName,
            'label_size' => Config::LAYER_LABEL_DEFAULT_SIZE,
            'tags' => false,
        ];

        return $arrayFormat ? $style : new LayerStyles($style);
    }

    public static function getLayerStyleByLayerIdAndType(string $layerId, string $styleType): ?LayerStyles
    {
        self::validateStyleType($styleType);

        return LayerStyles::finder()->find('layer_id = :layer_id and type = :type', [':layer_id' => $layerId, ':type' => $styleType]);
    }

    public static function addUserLayersStyles($organizationId, $subUserId)
    {
        $subUser = User::finder()->findByPk($subUserId);

        /**
         * @var MTUser $currentUser
         */
        $currentUser = Prado::getApplication()->getModule('auth')->getUser();
        
        Prado::getApplication()->getModule('auth')->switchUser($subUser->getUsername());

        $layers = UserLayers::finder()->findAll('group_id = :group_id', [':group_id' => $organizationId]);

        foreach ($layers as $layer) {
            if ($layer->getStyles()) {
                continue;
            }

            $layerStyle = LayerStyles::generateDefaultStyle($layer->layer_type, false);

            $layerStyle->layer_id = $layer->id;
            $layerStyle->table_name = $layer->table_name;
            $layerStyle->save();
        }
        Prado::getApplication()->getModule('auth')->switchUser($currentUser->getName());
    }

    public function save()
    {
        // Override save() to convert labels to a db array when saving an object
        if (is_array($this->labels)) {
            $this->labels = $this->convertPhpArrayToDbArray($this->labels);
        }

        parent::save();

        // Convert labels back to an php array after saving
        if (is_string($this->labels) && '{}' !== $this->labels) {
            $this->labels = $this->convertDbArrayToPhpArray($this->labels);
        } else {
            $this->labels = [];
        }
    }

    public function generateMapLabelSQL(UserLayers $layer): string
    {
        $layerDefinitions = $layer->getDefinitions();
        $labelStr = '';
        foreach ($this->labels as $labelColumn) {
            [$labelDef] = UserLayers::filterDefinitions($layerDefinitions, [['col_name' => $labelColumn]]);

            if (strlen($labelStr) > 0) {
                // When multiple columns are used for label, concatenate them using the $wrapCharacter
                $labelStr .= " || '{$this->wrapCharacter}' || ";
            }

            $customLabelExpression = $this->getCustomLabelExpression($layer, $labelColumn);
            if (strlen($customLabelExpression ?? '') > 0) {
                // Use expression different from the col_name of layer definitions
                $labelStr .= $customLabelExpression;

                continue;
            }

            if (Config::LAYER_COLUMN_CATEGORY_BOOLEAN === $labelDef['col_category']) {
                // Translate label if it is boolean
                $labelStr .= "CASE WHEN COALESCE(\\\"{$labelColumn}\\\", false) THEN 'Да' ELSE 'Не' END";

                continue;
            }

            if (Config::LAYER_COLUMN_CATEGORY_NUMBER === $labelDef['col_category']) {
                // If the number has decimal part, round it to 3 decimal places otherwise return the integer part
                $labelStr .= "CASE WHEN 
                        \"{$labelColumn}\" ISNULL
                            THEN ''
                        WHEN floor(\"{$labelColumn}\") = \"{$labelColumn}\"
                            THEN \"{$labelColumn}\"::TEXT
                        ELSE
                            ROUND(\"{$labelColumn}\"::NUMERIC, 3)::TEXT
                    END";

                continue;
            }

            if (Config::LAYER_TYPE_CSD == $layer->layer_type) {
                $labelStr .= "COALESCE(\"{$labelColumn}\", '')::TEXT";
            } else {
                $labelStr .= "COALESCE({$layer->table_name}.\"{$labelColumn}\", '')::TEXT";
            }
        }

        // Use empty string as label if the labels columns is null or empty
        $labelStr = strlen($labelStr) > 0
            ? $labelStr
            : "''";

        return "({$labelStr})";
    }

    protected function populateObject($data)
    {
        // Override populateObject() to convert labels to an array when loading an object
        if (!empty($data['labels']) && is_string($data['labels'])) {
            $trimmed = trim($data['labels'], '{}');
            $data['labels'] = '' === $trimmed ? [] : $this->convertDbArrayToPhpArray($data['labels']);
        }

        return parent::populateObject($data);
    }

    private static function validateStyleType($styleType)
    {
        if (!in_array($styleType, [self::SINGLE_COLORING_TYPE, self::BY_ATTRIBUTE_COLORING_TYPE])) {
            throw new Exception('Invalid style type');
        }
    }

    private static function validateColorColumn($colorColumn)
    {
        if (!in_array($colorColumn, [self::FILL_COLOR, self::BORDER_COLOR])) {
            throw new Exception('Invalid color type');
        }
    }

    /**
     * This function is used if there is label column that is not in the layer definitions.
     * Mainly used for the old layers labels.
     *
     * @param ?string $label
     *
     * @return null|string
     */
    private function getCustomLabelExpression(UserLayers $layer, ?string $label)
    {
        $label ??= 'default'; // use when the label is null

        $labelExpressionsByLayerType = [
            Config::LAYER_TYPE_KVS => [
                'area_kvs' => 'round((ST_Area(geom)/1000)::numeric, 3)',
                'used_area' => "COALESCE(round(used_area::NUMERIC, 3)::text, '')",
                'area_type' => "COALESCE(virtual_ntp_title, '')",
                'document_area' => '(CASE WHEN document_area IS NULL THEN round((ST_Area(geom)/1000)::numeric, 3) ELSE round(document_area::numeric, 3) END)',
                'irrigated_area' => '(CASE WHEN irrigated_area = TRUE THEN document_area ELSE 0 END)',
                'masiv_imot' => "(masiv || '.' || number)",
                'default' => 'kad_ident',
            ],
            Config::LAYER_TYPE_FOR_ISAK => [
                'land' => 'virtual_ekatte_name',
                'schema' => "left((case WHEN pndp = true THEN 'ПНДП, ' ELSE '' END)||
                    (case WHEN sepp = true THEN 'СЕПП,' ELSE '' END) ||
                    (case WHEN zdp = true THEN 'ЗДП,' ELSE '' END) ||
                    (case WHEN nr1 = true THEN 'НР 1,' ELSE '' END) ||
                    (case WHEN nr2 = true THEN 'НР 2,' ELSE '' END), -1)
                ",
                'area' => 'virtual_area_ha',
                'default' => "(CASE WHEN char_length(prc_name) > 0 OR char_length(prc_name) IS NOT NULL THEN prc_name || ' - ' ELSE '' END) || round(ST_Area(geom)::numeric/10000, 3) || ' ха'",
            ],
            Config::LAYER_TYPE_GPS => [
                'area' => 'virtual_area_dka',
                'default' => "(CASE WHEN char_length(plot_name) > 0 OR char_length(plot_name) IS NOT NULL THEN plot_name || ' - ' ELSE '' END) || round(ST_Area(geom)::numeric/10000, 3) || ' ха'",
            ],
            Config::LAYER_TYPE_ISAK => [
                'area' => 'round((ST_Area(geom) / 10000)::numeric, 3)',
                'culture' => "COALESCE(virtual_crop_name, '')",
                'watering' => "(CASE WHEN watering::int <> 0 THEN 'Да' ELSE 'Не' END)",
                'default' => "(CASE WHEN prc_uin IS NOT NULL THEN prc_uin ELSE '' END)",
            ],
            Config::LAYER_TYPE_KMS => [
                'area' => 'virtual_area_dka',
                'crop_code' => "COALESCE(virtual_crop_name, '')",
                'ekatte_code' => "COALESCE(virtual_ekatte_name, '')",
                'default' => "CASE WHEN ekatte IS NULL THEN (E'-'||name||E'/'|| trunc(cast((st_area(geom)/1000) as numeric),3)||E' дка') ELSE
                    (ekatte||E'-'||name||E'/'|| trunc(cast((st_area(geom)/1000) as numeric),3)||E' дка') END",
            ],
            Config::LAYER_TYPE_ZP => [
                'area_zp' => 'virtual_area_dka',
                'culture' => "COALESCE(virtual_crop_name, '')",
                'default' => "CASE WHEN crop_name IS NOT NULL THEN (crop_name || E' - ' || trunc(cast((st_area(geom)/1000) as numeric),3)||E' дка') ELSE
                    (trunc(cast((st_area(geom)/1000) as numeric),3)||E' дка') END",
            ],
        ];

        if (!isset($labelExpressionsByLayerType[$layer->layer_type][$label])) {
            return null;
        }

        return $labelExpressionsByLayerType[$layer->layer_type][$label];
    }
}
