<?php

namespace TF\Application\Entity;

use Prado\Data\ActiveRecord\TActiveRecord;

/**
 * http://www.pradoframework.net/demos/quickstart/?page=Database.ActiveRecord.
 */
class ObjectPermissions extends TActiveRecord
{
    public const TABLE = 'su_object_permissions';

    public const PERMISSION_READ = 1;
    public const PERMISSION_WRITE = 2;

    public static $permisionsMap = [
        self::PERMISSION_READ,
        self::PERMISSION_WRITE,
    ];

    public $id;
    public $class;
    public $object_id;
    public $user_id;
    public $permission;

    public static function finder($className = __CLASS__)
    {
        return parent::finder($className);
    }
}
