<?php

namespace TF\Application\Entity;

use Prado\Data\ActiveRecord\TActiveRecord;
use Prado\Data\TDbConnection;
use Prado\Prado;

class MTActiveUserRecord extends TActiveRecord
{
    public function getDbConnection()
    {
        static $conn;

        if (null === $conn) {
            $application = Prado::getApplication();

            $driver = constant('DEFAULT_DB_DRIVER');
            $host = constant('DBLINK_HOST');
            $port = constant('DBLINK_PORT');
            $database = $application->getUser()->database;
            $username = constant('DBLINK_USERNAME');
            $password = constant('DBLINK_PASSWORD');
            $socket = constant('DEFAULT_DB_SOCKET');

            if ($socket) {
                $dsn = "{$driver}:unix_socket={$socket};dbname={$database}";
            } else {
                $dsn = "{$driver}:host={$host};port={$port};dbname={$database};";
            }

            $conn = new TDbConnection($dsn, $username, $password);
        }

        return $conn;
    }

    public function findOrNew($criteria, $parameters = [])
    {
        if (!is_null($instance = $this->find($criteria, $parameters))) {
            return $instance;
        }

        return new static();
    }

    protected function convertDbArrayToPhpArray(string $dbArray): array
    {
        return str_getcsv(trim($dbArray, '{}'));
    }

    protected function convertPhpArrayToDbArray(array $phpArray): string
    {
        return '{' . implode(',', $phpArray) . '}';
    }
}
