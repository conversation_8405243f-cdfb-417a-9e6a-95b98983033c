<?php

namespace TF\Application\Entity;

use Prado\Data\ActiveRecord\TActiveRecord;

class MTActiveRecord extends TActiveRecord
{
    public function save()
    {
        if ($this->timestamps) {
            if (self::STATE_NEW === $this->_recordState) {
                $this->setCreatedAt();
                $this->setUpdatedAt();
            } elseif (self::STATE_LOADED === $this->_recordState) {
                $this->setUpdatedAt();
            }
        }

        parent::save();
    }
}
