<!DOCTYPE html>
<html>
    <script type="text/javascript" src="lib/js/agrimi/agrimi.js"></script>
    <com:THead ID="HeadTitle" Title="<%=SITE_TITLE%>">
        <base href="<%=SITE_BASE_HREF%>" />
        <meta name="viewport" content="initial-scale=1.0, user-scalable=no">
        <com:TMetaTag HttpEquiv="Content-Type" Content="text/html; charset=UTF-8" />
        <com:TMetaTag ID="Keywords" Name="keywords" />
        <com:TMetaTag ID="Description" Name="description" />

        <com:TConditional Visible="<%= LEGACY_MODE %>">
			<prop:TrueTemplate>
                <link rel="icon" type="image/x-icon" href="themes/Main/favicon.ico" />
			</prop:TrueTemplate>
            <prop:FalseTemplate>
                <link rel="icon" type="image/x-icon" href="themes/Main/favicon-agrimi.ico" />
            </prop:FalseTemplate>
        </com:TConditional>

        <link rel="stylesheet" type="text/css" href="themes/Main/bootstrap/easyui.css">
        <link rel="stylesheet" type="text/css" href="themes/Main/icon.css">
        <link rel="stylesheet" type="text/css" href="lib/js_external/fileupload/jquery.plupload.queue/css/jquery.plupload.queue.css" />
        <link rel="stylesheet" type="text/css" href="themes/Main/colorpicker/css/colorpicker.css">
        <link rel="stylesheet" type="text/css" href="themes/Main/colorpicker/css/layout.css">
        <link rel="stylesheet" type="text/css" href="themes/Main/openlayer/style.css">
        <link rel="stylesheet" type="text/css" href="themes/Main/FCBKcomplete/style.css">
        <link rel="stylesheet" type="text/css" href="themes/Main/FCBKcomplete/tf-style.css">
        <link rel="stylesheet" type="text/css" href="themes/Main/jquery-ui.min.css">
        <link rel="stylesheet" type="text/css" href="themes/Main/spectrum.css">
        <link rel="stylesheet" type="text/css" href="themes/Main/plots-detailed.css">
        <link rel="stylesheet" type="text/css" href="themes/Main/global-notifications.css">
        <link rel="stylesheet" type="text/css" href="themes/Main/warehouse.css">
        <link rel="stylesheet" type="text/css" href="themes/Main/agrimi-bar.css">

        <%%
            if(CSS_CUSTOM_THEME_NAME) {
                $themeName = CSS_CUSTOM_THEME_NAME;
                $themUrl = 'themes/'.$themeName.'/main.css';
                echo '<link rel="stylesheet" type="text/css" href="'.$themUrl.'">';
            }
        %>
        <script type="text/javascript">
            var Settings = {
                OPEN_LAYERS_PROXY: '<%=OPEN_LAYERS_PROXY%>'
            };
            var accessToken = '<%=$this->User->getAccessToken()->getToken()%>';
            var CMS_API_URL = '<%=CMS_API_URL%>';
            var MAIN_NAVIGATION_INSTANCE = '<%=MAIN_NAVIGATION_INSTANCE%>';
            var keycloakUid =  '<%=$this->User->getKeyCloakUID()%>';
            var name = '<%=$this->User->getFullName()%>';
            var email =  '<%=$this->User->getEmail()%>';
            var postHogАpiKey = ('<%=POSTHOG_API_KEY%>');
        </script>

        <com:TRepeater ID="Repeater" EnableViewState="false">
            <prop:ItemTemplate>
                <script type="text/javascript" src="<%#$this->Data%>"></script>
            </prop:ItemTemplate>
        </com:TRepeater>
        <script type="text/javascript">
            if (postHogАpiKey.length > 0){
                !function(t,e){var o,n,p,r;e.__SV||(window.posthog=e,e._i=[],e.init=function(i,s,a){function g(t,e){var o=e.split(".");2==o.length&&(t=t[o[0]],e=o[1]),t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}}(p=t.createElement("script")).type="text/javascript",p.async=!0,p.src=s.api_host+"/static/array.js",(r=t.getElementsByTagName("script")[0]).parentNode.insertBefore(p,r);var u=e;for(void 0!==a?u=e[a]=[]:a="posthog",u.people=u.people||[],u.toString=function(t){var e="posthog";return"posthog"!==a&&(e+="."+a),t||(e+=" (stub)"),e},u.people.toString=function(){return u.toString(1)+".people (stub)"},o="capture identify alias people.set people.set_once set_config register register_once unregister opt_out_capturing has_opted_out_capturing opt_in_capturing reset isFeatureEnabled onFeatureFlags getFeatureFlag getFeatureFlagPayload reloadFeatureFlags group updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures getActiveMatchingSurveys getSurveys onSessionId".split(" "),n=0;n<o.length;n++)g(u,o[n]);e._i.push([i,s,a])},e.__SV=1)}(document,window.posthog||[]);
                posthog.init(postHogАpiKey, {
                    api_host: 'https://eu.posthog.com',
                    loaded: function(posthog) {
                        if (posthog.get_distinct_id() !== keycloakUid) {
                            var userProperties = {
                                'Name': name,
                                'Email': email
                            };
                            posthog.identify(keycloakUid, userProperties);
                        }
                    }
                });
                
            }
        </script>
    </com:THead>

    <body class="easyui-layout">

    <com:TForm ID="MainFormID">
        <!-- Exclude -->
        <com:TConditional Condition="!$this->excludeJS">
            <prop:TrueTemplate>
                <script type="text/javascript">
                    jQuery.noConflict();

                    jQuery(window).on('load', function(){
                        jQuery('#body-init-loading').hide();
                        jQuery('#bodydiv').css("visibility", "visible");
                    });
                </script>
            </prop:TrueTemplate>
            <prop:FalseTemplate>
            </prop:FalseTemplate>
        </com:TConditional>

        <com:TConditional Condition="filter_var(LEGACY_MODE, FILTER_VALIDATE_BOOLEAN)">
			<prop:FalseTemplate>
                <div data-options="region:'west',split:false, collapsible:false" style="width:50px; overflow:visible;border-width: 0px;">
                    <main-nav style="height: 100%;" expandable="true" expanded="false"></main-nav>
                </div>
            </prop:FalseTemplate>
            <prop:TrueTemplate>
                <!-- Agrimi bar -->
                <div data-options="region:'north',split:false, collapsible:false" class="agrimi-bar" id="agrimiBar">
                    <img class="agrimi-logo" src="themes/Main/images/agrimi-logo.svg" />
                    <div class="agrimi-text">
                        <div class="agrimi-title">Една платформа - много решения!</div>
                        <div class="agrimi-description">Agrimi обединява функционалностите на водещите софтуери в българското земеделие.</div>
                    </div>
                    <a class="agrimi-link-btn" href="https://www.agrimi.com/много-решения-в-една-платформа" target="_blank">Разбери повече</a>
                </div>
            </prop:TrueTemplate>
        </com:TConditional>
        <div data-options="region:'center'" id="bodydiv" style="visibility: hidden">
            <div id="global-notifications-panel"></div>
            <com:TContentPlaceHolder ID="Content" />
        </div>

        <div id="fullscreen-win" style="border:0px;">
            <div style="width:100%; height:100%;color:#000000;" id="map-full"></div>
        </div>
        <com:TConditional Condition="filter_var(LEGACY_MODE, FILTER_VALIDATE_BOOLEAN)">
			<prop:FalseTemplate>
                <com:Application.Portlets.NavigationMenu />
        	</prop:FalseTemplate>
        </com:TConditional>
    </com:TForm>

    <div id="body-init-loading" style="position:absolute; top: 50%; left: 50%;">
        <img src="themes/Main/loading.gif">
    </div>
    <com:Application.Portlets.Login />

    <script>
        (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
        (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
        m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
        })(window,document,'script','//www.google-analytics.com/analytics.js','ga');

        ga('create', 'UA-44073592-3', 'auto');
        ga('send', 'pageview');
    </script>
    <com:TConditional Condition="filter_var(LEGACY_MODE, FILTER_VALIDATE_BOOLEAN)">
		<prop:FalseTemplate>
            <script type="module" src="lib/js/main/web-component-nav/web-components.esm.js" data-stencil></script>
            <script type="module">
                
                var menu = document.querySelector("main-nav");
                var request =  `${CMS_API_URL}/api/main-navigation/${MAIN_NAVIGATION_INSTANCE}?lang=bg`;
                
                window.addEventListener('load', function() {     
                    menu.addEventListener("navigateTo", (event) => {
                        // Impelement page redirection
                        const mainNavItem = event.detail;
                        const url = 
                            mainNavItem.is_active || mainNavItem.level > 1
                                ? mainNavItem.url
                                : mainNavItem.no_data_url;
                        const fullUrl = `${window.location.origin}/${url}`;
                        window.open(fullUrl, mainNavItem.target);
                    });

                    menu.addEventListener("logout", () => {
                        const rpcParams = JSON.stringify({
                            "method": "logout",
                            "params": [],
                            "id": 1,
                            "jsonrpc": "2.0"
                        });
                        
                        const rpcUrl = 'index.php?login-rpc=login-user'
                        jQuery.ajax({
                            url: rpcUrl,
                            data: rpcParams,
                            accepts: 'application/json',
                            contentType: 'application/json',
                            method: 'post',
                            dataType: 'json',
                            processData: false,
                            async: true
                        }).then(function (data) {
                            if (postHogАpiKey.length > 0) {
                                posthog.reset();
                            }
                            window.location.href = "index.php?page=Home";
                        });
                    });

                    var cacheAvailable = 'caches' in window;

                    const cacheNavigation = async () => {
                        const cache = await caches.open('main-nav');
                        const data = await cache.match(request);
                        const body = await data?.json();

                        requestNavigation(); 
                        /*
                        isCacheExpired(body?.miliseconds) 
                            ? requestNavigation() 
                            : menu.init(body.menuItems); 
                        */
                    }

                    const isCacheExpired = (miliseconds) => {
                        return miliseconds 
                            ? (miliseconds + 24 * 60 * 60 * 1000 < new Date().getTime())
                            : true;
                    }

                    const requestNavigation = async () => {
                        const cache = cacheAvailable ? await caches.open('main-nav') : undefined;
                        jQuery.ajax({
                            url: request,
                            type: 'GET',
                            beforeSend: function (xhr) {
                                xhr.setRequestHeader('Authorization', 'Bearer ' + accessToken);
                            },
                            data: {},
                            success: function (menuItems) {
                                const url = new URL(window.location.href);
                                const activeRoute = url.pathname + url.search;
                                const logo = {
                                    normal: "../assets/images/agrimi-logo.svg",
                                    small: "../assets/images/agrimi-logo-small.svg"
                                }
                                menu.setActiveRoute(activeRoute);
                                menu.setDomain(url.origin);
                                menu.setMainNav(menuItems);
                                menu.setLogo(logo)
                                
                                if (cacheAvailable) {
                                    const data = {
                                        miliseconds: new Date().getTime(),
                                        menuItems: menuItems
                                    }
                                    cache.put(request, new Response(JSON.stringify(data)))
                                }
                            },
                            error: function () { 
                                console.log('error');
                            },
                        });
                    }

                    cacheAvailable ? cacheNavigation() : requestNavigation();
                })    
            </script>
        </prop:FalseTemplate>
    </com:TConditional>
    
    <com:TConditional Condition="HELPHERO_ID">
        <prop:TrueTemplate>
            <script src="//app.helphero.co/embed/<%=HELPHERO_ID%>"></script>
            <script type="module">
                if (!'<%=$this->User->KeyCloakUID%>'.length) {
                    HelpHero.anonymous();
                } else {
                    HelpHero.identify('<%=$this->User->KeyCloakUID%>', {
                        user_lang: 'bg',
                        user_name: '<%=$this->User->Name%>',
                        user_country: 'BG'
                    });
                }
            </script> 
        </prop:TrueTemplate>
      </com:TConditional>
    <div id="progress-mask" style="position: fixed; width: 100%; height: 100%; background: rgba(255,255,255,0.69); z-index: 9900; display: none;"></div>
</body>

</html>
