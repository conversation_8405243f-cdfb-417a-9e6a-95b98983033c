<!DOCTYPE html>
<html>
    <com:THead ID="HeadTitle" Title="<%=SITE_TITLE%>">
        <base href="<%=SITE_BASE_HREF%>" />
        <com:TMetaTag HttpEquiv="Content-Type" Content="text/html; charset=UTF-8" />
        <com:TMetaTag ID="Keywords" Name="keywords" />
        <com:TMetaTag ID="Description" Name="description" />
		
        <com:TConditional Visible="<%= LEGACY_MODE %>">
			<prop:TrueTemplate>
                <link rel="icon" type="image/x-icon" href="themes/Main/favicon.ico" />
			</prop:TrueTemplate>
            <prop:FalseTemplate>
                <link rel="icon" type="image/x-icon" href="themes/Main/favicon-agrimi.ico" />
            </prop:FalseTemplate>
        </com:TConditional>
       
        <com:TRepeater ID="LoginRepeater" EnableViewState="false">
            <prop:ItemTemplate>
            </prop:ItemTemplate>
        </com:TRepeater>
        <script type="text/javascript" src="lib/js_external/tinymce/tinymce.min.js"></script>
        <script type="text/javascript">
            var postHogApiKey = ('<%=POSTHOG_API_KEY%>');
            if (postHogApiKey.length > 0) {
                !function(t,e){var o,n,p,r;e.__SV||(window.posthog=e,e._i=[],e.init=function(i,s,a){function g(t,e){var o=e.split(".");2==o.length&&(t=t[o[0]],e=o[1]),t[e]=function(){t.push([e].concat(Array.prototype.slice.call(arguments,0)))}}(p=t.createElement("script")).type="text/javascript",p.async=!0,p.src=s.api_host+"/static/array.js",(r=t.getElementsByTagName("script")[0]).parentNode.insertBefore(p,r);var u=e;for(void 0!==a?u=e[a]=[]:a="posthog",u.people=u.people||[],u.toString=function(t){var e="posthog";return"posthog"!==a&&(e+="."+a),t||(e+=" (stub)"),e},u.people.toString=function(){return u.toString(1)+".people (stub)"},o="capture identify alias people.set people.set_once set_config register register_once unregister opt_out_capturing has_opted_out_capturing opt_in_capturing reset isFeatureEnabled onFeatureFlags getFeatureFlag getFeatureFlagPayload reloadFeatureFlags group updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures getActiveMatchingSurveys getSurveys onSessionId".split(" "),n=0;n<o.length;n++)g(u,o[n]);e._i.push([i,s,a])},e.__SV=1)}(document,window.posthog||[]);
                posthog.init(postHogApiKey, {postHogАpiKey: 'https://eu.posthog.com'})
            }
        </script>
    </com:THead>

    <body >

        <com:TContentPlaceHolder ID="Content" />

        <script>
            (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
                        (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
                    m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
            })(window,document,'script','//www.google-analytics.com/analytics.js','ga');

            ga('create', 'UA-44073592-3', 'auto');
            ga('send', 'pageview');
        </script>
    </body>
</html>