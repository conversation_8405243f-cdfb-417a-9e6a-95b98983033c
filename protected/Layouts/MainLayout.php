<?php

use Prado\Prado;
use Prado\Web\UI\TTemplateControl;
use TF\Application\Common\Config;

/**
 * MainLayout class file.
 *
 * <AUTHOR>
 */

/**
 * MainLayout class.
 */
class MainLayout extends TTemplateControl
{
    public $bodyClass;
    public $excludeJS = false;
    private $pluginPath = 'Plugins.Extended.';
    private $excludeList = [
        'Warehouse',
        'WarehouseAddTransaction',
        'WarehouseAddProductionTransaction',
        'WarehouseReturnTransaction',
        'WarehouseSubContragentsTransaction',
        'WarehouseSubPlotsTransaction',
        'WarehouseSubMachinesTransaction',
        'WarehouseTransferTransaction',
        'WarehouseReports',
    ];

    public function onLoad($param)
    {
        parent::onLoad($param);
        $this->loadJSFilesForPageFromJSON();
    }

    protected function initRecursive($namingContainer = null)
    {
        $this->excludeJS = in_array($this->getPageName(), $this->excludeList);
        parent::initRecursive($namingContainer);
    }

    protected function loadJSFilesForPageFromJSON()
    {
        $jsonData = file_get_contents(SITE_PATH . 'jsSources.json');
        $jsonData = json_decode($jsonData, true);
        $jsonDataPage = $jsonData[__CLASS__ . 'Common'];
        $jsSourceFiles = $jsonData[__CLASS__]['src'];

        $modifiedArray = [];
        $pageName = $this->getPageName();
        if (in_array($pageName, $jsonDataPage['excludePages'])) {
            return;
        }

        if (Config::STATE_DEBUG == Prado::getApplication()->getMode()) {
            $jsSourceFiles = $jsonData[__CLASS__ . 'Common']['src'];
            for ($i = 0; $i < count($jsSourceFiles); $i++) {
                $modifiedArray[$i] = substr($jsSourceFiles[$i], 7);
            }
            $count = count($modifiedArray);
            $jsSourceFiles = $jsonData[__CLASS__]['src'];
            for ($i = 0; $i < count($jsSourceFiles); $i++) {
                $modifiedArray[$count + $i] = substr($jsSourceFiles[$i], 7);
            }
        } else {
            $file = $jsonData[__CLASS__ . 'Common']['fileName'] . '.concat.js';
            $dest = substr($jsonData[__CLASS__ . 'Common']['dest'], 7);
            $time = filemtime(PUBLIC_PATH . $dest . $file);
            $modifiedArray[0] = $dest . $file . '?ver=' . $time;
            $file = $jsonData[__CLASS__]['fileName'] . '.min.js';
            $dest = substr($jsonData[__CLASS__]['dest'], 7);
            $time = filemtime(PUBLIC_PATH . $dest . $file);
            $modifiedArray[1] = $dest . $file . '?ver=' . $time;
        }

        $this->Repeater->DataSource = $modifiedArray;
        $this->Repeater->dataBind();
    }

    /**
     * Returns the name of the loaded page as content of MainLayout.
     *
     * @return string
     */
    protected function getPageName()
    {
        $pagePath = $this->getPage()->getPagePath();
        $pageName = substr($pagePath, 0, -5);

        return $pageName ? $pageName : $pagePath;
    }
}
