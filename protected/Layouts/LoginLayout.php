<?php

/**
 * MainLayout class file.
 *
 * <AUTHOR>
 */

use Prado\Web\UI\TTemplateControl;
use TF\Application\Common\Config;

/**
 * MainLayout class.
 */
class LoginLayout extends TTemplateControl
{
    private $pluginPath = 'Plugins.Extended.';

    public function onLoad($param)
    {
        parent::onLoad($param);

        $this->loadJSFilesForPageFromJSON();
    }

    public function getCurrentYear()
    {
        $dateTyme = new DateTime('now');

        return $dateTyme->format('Y');
    }

    protected function loadJSFilesForPageFromJSON()
    {
        $jsonData = file_get_contents(SITE_PATH . 'jsSources.json');
        $jsonData = json_decode($jsonData, true);
        $jsSourceFiles = $jsonData[__CLASS__]['src'];
        $modifiedArray = [];

        if (Config::STATE_DEBUG == Prado::getApplication()->getMode()) {
            $jsSourceFiles = $jsonData['MainLayoutCommon']['src'];
            for ($i = 0; $i < count($jsSourceFiles); $i++) {
                $modifiedArray[$i] = substr($jsSourceFiles[$i], 7);
            }

            $jsSourceFiles = $jsonData[__CLASS__]['src'];
            $count = count($modifiedArray);
            for ($i = 0; $i < count($jsSourceFiles); $i++) {
                $modifiedArray[$count + $i] = substr($jsSourceFiles[$i], 7);
            }
        } else {
            $file = $jsonData['MainLayoutCommon']['fileName'] . '.concat.js';
            $dest = substr($jsonData['MainLayoutCommon']['dest'], 7);
            $time = filemtime(PUBLIC_PATH . $dest . $file);
            $modifiedArray[0] = $dest . $file . '?ver=' . $time;
            $file = $jsonData[__CLASS__]['fileName'] . '.min.js';
            $dest = substr($jsonData[__CLASS__]['dest'], 7);
            $time = filemtime(PUBLIC_PATH . $dest . $file);
            $modifiedArray[1] = $dest . $file . '?ver=' . $time;
        }

        $this->LoginRepeater->DataSource = $modifiedArray;
        $this->LoginRepeater->dataBind();
    }
}
