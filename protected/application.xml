<?xml version="1.0" encoding="utf-8"?>
<application Mode="Debug" id="web">
    <paths>
        <alias id="BaseApp" path="../" />
        <!-- <alias id="Kernel" path="../engine/Kernel" />
        <alias id="CronsLoadData" path="../engine/Kernel/CronsLoadData" />
        <alias id="ExportData" path="../engine/Kernel/ExportData" />
        <alias id="ThematicMaps" path="../engine/Kernel/ThematicMaps" />
        <alias id="Plugins" path="../engine/Plugins" /> -->
        <alias id="Pages" path="../protected/Pages" />
        <!-- <alias id="APIClasses" path="../engine/APIClasses" /> -->
        <alias id="Public" path="../public" />
        <using namespace="System.Web.UI.ActiveControls.*" />
        <using namespace="vendor.pradosoft.prado.framework.Web.Services.TJsonService" />
        <!-- <using namespace="Kernel.Loader" />
        <using namespace="Kernel.Controller" />
        <using namespace="Kernel.DbHandler" />
        <using namespace="Kernel.DbConnection" />
        <using namespace="Kernel.LoggerMessages" />
        <using namespace="Kernel.LoggerType" /> -->
        <using namespace="Application.Layouts.*" />
        <using namespace="Application.Portlets.*" />
    </paths>
    <modules>
        <module class="TF\Engine\Kernel\DefaultDataSource" id="database">
            <database Active="true" Charset="utf8" />
        </module>
        <module class="TF\Engine\Kernel\RemoteDataSource" id="database2">
            <database Active="true" Charset="utf8" />
        </module>
        <module class="System.Data.ActiveRecord.TActiveRecordConfig" ConnectionID="database2" EnableCache="true"  />

        <module class="TF\Engine\Kernel\Export2XlsClass" id="Export2Xls"/>
        <module class="TF\Engine\Kernel\ExportWordDocClass" id="ExportWordDoc"/>
        <module class="TF\Engine\Kernel\ExportToExcelClass" id="ExportToExcel"/>
        <module class="TF\Engine\Kernel\Validation" id="Validation" />
        <module class="TF\Engine\Kernel\DeviceModuleClass" id="deviceModule" />
        <module class="TF\Engine\Kernel\WarehouseModuleClass" id="warehouseModule" />
        <module class="TF\Engine\Kernel\KvsStoreModule" id="kvsStoreModule" />
        <module class="TF\Engine\Kernel\CmsModule" id="cmsModule" /> 
        <module class="TF\Engine\Kernel\KeycloakAuthModule" id="keycloak" />

        <module LoginPage="Home" UserManager="keycloak-users" class="TF\Application\Common\MTKeycloakAuthManager" id="auth"/>
        <module class="TF\Application\Common\MTUserManager" id="users"/>
        <module class="TF\Application\Common\MTKeycloakUserManager" id="keycloak-users"/>

        <module id="cache" class="System.Caching.TMemCache" >
            <server Host="127.0.0.1" Port="11211" Weight="1" Timeout="300" RetryInterval="15" />
        </module>
        <module ValidationKey="TOSHEL_COOKIE_VALIDATION_KEY" class="TSecurityManager" id="security"/>
        <module class="System.Util.TLogRouter" id="log">
            <route Categories="PHP, SQL, HTTP, CONFIG, INVALID_DATA_TYPE, INVALID_DATA_VALUE, INVALID_DATA_FORMAT, INVALID_OPERATION, SECURITY, IO, NOT_SUPPORTED, APPLICATION" Levels="Error" LogFile="debugger.log" LogPath="BaseApp.logs" MaxFileSize="1024" class="TFileLogRoute" />
            <route Categories="Users, Files, Categories, Base, Deeds, Item, Leases, Neighbours, Owners, Plots, Rents, Seeds, OwnerPayments" Levels="Info" LogFile="logger.log" LogPath="BaseApp.logs" MaxFileSize="1024" class="TFileLogRoute" />
        </module>
        <module class="Application.Common.MTHttpRequest" id="request" />
        <module class="TF\Engine\Kernel\CronsLoadData\KvsProcessingClass" lazy="true" id="KvsProcessingClass" />
        <module class="TF\Engine\Kernel\CronsLoadData\LayerProcessingClass" lazy="true" id="LayerProcessingClass" />
        <module class="TF\Engine\Kernel\CronsLoadData\CoverageProcessingClass" lazy="true" id="CoverageProcessingClass" />
        <module class="TF\Engine\Kernel\CronsLoadData\KvsOszProcessingClass" lazy="true" id="KvsOszProcessingClass" />
        <module class="TF\Engine\Kernel\CronsLoadData\WorkLayerProcessingClass" lazy="true" id="WorkLayerProcessingClass" />
        <module class="TF\Engine\Kernel\CronsLoadData\ImportExcelKvsClass" lazy="true" id="ImportExcelKvsClass" />
        <module class="TF\Engine\Kernel\CronsLoadData\CsdProcessingClass" lazy="true" id="CsdProcessingClass" />     
        <module BasePath="Public.assets" BaseUrl="assets" class="System.Web.TAssetManager" id="asset" />

        <module class="TF\Engine\Kernel\ExportData\ExportGps" id="exportGps" />
        <module class="TF\Engine\Kernel\ExportData\ExportKML" id="exportKML" />
        <module class="TF\Engine\Kernel\ExportData\ExportKMLfarmTrack" id="exportKMLfarmTrack" />
        <module class="TF\Engine\Kernel\ExportData\ExportTrimble" id="exportTrimble" />
        <module class="TF\Engine\Kernel\ExportData\ExportISAK" id="exportISAK" />
        <module class="TF\Engine\Kernel\ExportData\ExportTopcon" id="exportTopcon" />
        <module class="TF\Engine\Kernel\ExportData\ExportMueller" id="exportMueller" />
        <module class="TF\Engine\Kernel\ExportData\ExportJohnDeere" id="exportJohnDeere" />
        <module class="TF\Engine\Kernel\ExportData\ExportTrimbleAg" id="exportTrimbleAg" />
        <module class="TF\Engine\Kernel\ExportData\ExportGPX" id="exportGPX" />
        <module class="TF\Engine\Kernel\ExportData\ExportABlines" id="exportABlines" />
        <module class="TF\Engine\Kernel\ExportData\SmartConvertExport\AbLinesExport" id="exportABLinesSmartConvert" />
        <module class="TF\Engine\Kernel\ExportData\LayersExportFactory" id="layersExportFactory" />


        <module class="TF\Engine\Kernel\PolygonFromMultipleLayers\PolygonFromSystemLayers" id="systemLayers" />
        <module class="TF\Engine\Kernel\PolygonFromMultipleLayers\PolygonFromUserLayers" id="userLayers" />
        <module class="TF\Engine\Kernel\PolygonFromMultipleLayers\PolygonFromKvsLayer" id="layerKVS" />
        <module class="TF\Engine\Kernel\PolygonFromMultipleLayers\PolygonFromLayersFactory" id="polygonFromLayersFactory" />

        <module class="TF\Engine\Kernel\ThematicMaps\LayerKvsThematicMap" id="layerKvsThematicMap" />
        <module class="TF\Engine\Kernel\ThematicMaps\LayerIsakThematicMap" id="layerIsakThematicMap" />
        <module class="TF\Engine\Kernel\DbfEncodingDetector" id="dbfEncodingDetector"></module>

        <module class="TF\Engine\Kernel\CommonServices\CommonServicesModule" id="commonServicesModule" />
    </modules>
    <services>
        <service BasePath="Application.Pages" class="TF\Engine\Kernel\MTPageService" id="page">
            <pages MasterClass="Application.Layouts.MainLayout" Theme="Main" />
            <modules>
                <module id="theme"
                        class="System.Web.UI.TThemeManager"
                        BaseUrl="themes" />
            </modules>
        </service>
        <service class="System.Web.Services.TMapService" id="mapsrv">
            <map id="geo_scan" layers="geo_scan" map_file="/var/www/satellite_processor/maps/geo_scan.map" server="http://api.geoscan.bg/mapcache" />
            <map id="geo_index" layers="*" map_file="/var/www/satellite_processor/maps/" server="http://images.technofarm.bg/cgi-bin/mapserv" />
            <map id="default" layers="*" map_file="*" server="http://212.50.26.190:8081/cgi-bin/mapserv" />
        </service>

        <service id="login-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="login-user" class="TF\Engine\APIClasses\Login\LoginForm" />
            <rpcapi id="forgotten-password" class="TF\Engine\APIClasses\Login\ForgottenPassword" />
        </service>

         <service id="users-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="users" class="TF\Engine\APIClasses\Users\UsersMainGrid" />
        </service>

        <service id="dividends-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="annual-report-tree" class="TF\Engine\APIClasses\Dividends\AnnualReportTree" />
            <rpcapi id="dividends-grid" class="TF\Engine\APIClasses\Dividends\DividendsGrid" />
            <rpcapi id="export-dividend-payments-blanks" class="TF\Engine\APIClasses\Dividends\ExportDividendPaymentsBlanks" />
        </service>

		<service id="cooperators-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="cooperators-tree" class="TF\Engine\APIClasses\Cooperators\CooperatorsTree" />
            <rpcapi id="summary-report-cooperators" class="TF\Engine\APIClasses\Cooperators\SummaryReportCooperatorsGrid" />
            <rpcapi id="excluded-report-cooperators" class="TF\Engine\APIClasses\Cooperators\ExcludedReportCooperatorsGrid" />
            <rpcapi id="cooperators-heritors-combobox" class="TF\Engine\APIClasses\Cooperators\CooperatorHeritorsCombobox" />
            <rpcapi id="cooperator-heritors-tree" class="TF\Engine\APIClasses\Cooperators\CooperatorHeritorsTree" />

            <rpcapi id="cooperator-capital" class="TF\Engine\APIClasses\Cooperators\CooperatorsCapitalGrid" />
            <rpcapi id="cooperator-dividend" class="TF\Engine\APIClasses\Cooperators\CooperatorsDividendGrid" />
            <rpcapi id="cooperators-templates-grid" class="TF\Engine\APIClasses\Cooperators\CooperatorsTemplatesGrid" />
            <rpcapi id="export-cooperators-blank" class="TF\Engine\APIClasses\Cooperators\ExportCooperatorsBlank" />
            <rpcapi id="export-cooperator-payments-blanks" class="TF\Engine\APIClasses\Cooperators\ExportCooperatorPaymentsBlanks" />
            <rpcapi id="cooperators-files-maingrid" class="TF\Engine\APIClasses\Cooperators\CooperatorsFilesGrid" />
        </service>

        <service id="plots-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="declaration-choose-grid" class="TF\Engine\APIClasses\Plots\DeclarationChooseGrid" />
            <rpcapi id="declaration-chosen-grid" class="TF\Engine\APIClasses\Plots\DeclarationChosenGrid" />
            <rpcapi id="for-hypothec-plots-report-grid" class="TF\Engine\APIClasses\Plots\ForHypothecPlotsReportGrid" />
            <rpcapi id="for-sublease-plots-report-grid" class="TF\Engine\APIClasses\Plots\ForSubleasePlotsReportGrid" />
            <rpcapi id="hypothecs-plots-report-grid" class="TF\Engine\APIClasses\Plots\HypothecsPlotsReportGrid" />
            <rpcapi id="own-plots-report-grid" class="TF\Engine\APIClasses\Plots\OwnPlotsReportGrid" />
            <rpcapi id="plot-map" class="TF\Engine\APIClasses\Plots\PlotMap" />
            <rpcapi id="plots-hypothecs-datagrid" class="TF\Engine\APIClasses\Plots\PlotsHypothecsGrid"/>
            <rpcapi id="plots-contracts-datagrid" class="TF\Engine\APIClasses\Plots\PlotsContractsGrid" />
            <rpcapi id="plots-documents-datagrid" class="TF\Engine\APIClasses\Plots\PlotsDocumentsGrid" />
            <rpcapi id="plots-report-grid" class="TF\Engine\APIClasses\Plots\PlotReportsGrid" />
            <rpcapi id="plots-sales-contracts-datagrid" class="TF\Engine\APIClasses\Plots\PlotsSalesContractsGrid"/>
            <rpcapi id="plots-tree" class="TF\Engine\APIClasses\Plots\PlotsTree" />
            <rpcapi id="rented-plots-report-grid" class="TF\Engine\APIClasses\Plots\RentedPlotsReportGrid" />
            <rpcapi id="subleased-plots-report-grid" class="TF\Engine\APIClasses\Plots\SubleasedPlotsReportGrid" />
            <rpcapi id="subleased-rented-plots-report-grid" class="TF\Engine\APIClasses\Plots\SubleasedRentedPlotsReportGrid" />
            <rpcapi id="used-plots-report-grid" class="TF\Engine\APIClasses\Plots\UsedPlotsReportGrid" />
            <rpcapi id="expiring-contracts-report-grid" class="TF\Engine\APIClasses\Plots\ExpiringContractsReportGrid" />
            <rpcapi id="plots-in-many-contracts-report-grid" class="TF\Engine\APIClasses\Plots\PlotsInManyContractsReportGrid" />
            <rpcapi id="plots-declaration-export" class="TF\Engine\APIClasses\Plots\DeclarationExport" />
            <rpcapi id="layer-selection-combobox" class="TF\Engine\APIClasses\Plots\PlotsHistoryGrid"/>
            <rpcapi id="reports-show-map" class="TF\Engine\APIClasses\Plots\ReportsShowMap"/>
            <rpcapi id="plots-history-grid" class="TF\Engine\APIClasses\Plots\PlotsHistoryGrid"/>
            <rpcapi id="contracts-with-ownerless-plots-report-grid" class="TF\Engine\APIClasses\Plots\ContractsWithOwnerlessPlotsReportGrid"/>
            <rpcapi id="historical-plots-report-grid" class="TF\Engine\APIClasses\Plots\HistoricalPlotsReportGrid"/>
            <rpcapi id="plots-image" class="TF\Engine\APIClasses\Plots\PlotsImage"/>
            <rpcapi id="detailed-own-plots-report-grid" class="TF\Engine\APIClasses\Plots\DetailedOwnPlotsReportGrid"/>
        </service>

        <service id="hypothecs-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="hypothecs-tree" class="TF\Engine\APIClasses\Hypothecs\HypothecsTree"/>
            <rpcapi id="hypothecs-creditors" class="TF\Engine\APIClasses\Hypothecs\HypothecsCreditors"/>
            <rpcapi id="hypothecs-plots-grid" class="TF\Engine\APIClasses\Hypothecs\HypothecsPlotsGrid"/>
            <rpcapi id="hypothecs-payments-grid" class="TF\Engine\APIClasses\Hypothecs\HypothecsPaymentsGrid"/>
            <rpcapi id="hypothecs-files-grid" class="TF\Engine\APIClasses\Hypothecs\HypothecsFilesGrid"/>
        </service>

        <service id="osz-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="osz-files" class="TF\Engine\APIClasses\OSZ\OSZFiles"/>
            <rpcapi id="osz-files-plots" class="TF\Engine\APIClasses\OSZ\OSZFilesPlots"/>
        </service>

         <service id="contracts-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="contract-owner-data" class="TF\Engine\APIClasses\Contracts\ContractOwnerData" />
            <rpcapi id="contracts-annexes-grid" class="TF\Engine\APIClasses\Contracts\ContractsAnnexesGrid" />
            <rpcapi id="contracts-annexes-plots-datagrid" class="TF\Engine\APIClasses\Contracts\ContractsAnnexesPlotsGrid" />
            <rpcapi id="contracts-edit-plot-areas" class="TF\Engine\APIClasses\Contracts\EditPlotAreas" />
            <rpcapi id="contracts-farming-datagrid" class="TF\Engine\APIClasses\Contracts\ContractsFarmingGrid"  />
            <rpcapi id="contracts-files-maingrid" class="TF\Engine\APIClasses\Contracts\ContractsFilesGrid" />
            <rpcapi id="contracts-owners-datagrid" class="TF\Engine\APIClasses\Contracts\ContractsOwnersGrid" />
            <rpcapi id="contracts-plots-datagrid" class="TF\Engine\APIClasses\Contracts\ContractsPlotsGrid" />
            <rpcapi id="contracts-tree" class="TF\Engine\APIClasses\Contracts\ContractsTree" />
            <rpcapi id="contracts-groups-datagrid" class="TF\Engine\APIClasses\Contracts\ContractsGroupsGrid" />
            <rpcapi id="contracts-exports" class="TF\Engine\APIClasses\Contracts\ContractsExports" />
            <rpcapi id="owners-reps-grid" class="TF\Engine\APIClasses\Contracts\OwnersRepsGrid" />
            <rpcapi id="contracts-plots-confirmation-datagrid" class="TF\Engine\APIClasses\Contracts\ContractsPlotsConfirmationGrid"/>
            <rpcapi id="subleases-plots-confirmation-datagrid" class="TF\Engine\APIClasses\Contracts\SubleasesPlotsConfirmationGrid"/>
            <rpcapi id="plot-contracts-grid" class="TF\Engine\APIClasses\Contracts\PlotContractsGrid" />
        </service>

        <service id="owners-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="owners-documents" class="TF\Engine\APIClasses\Owners\OwnersDocuments" />
            <rpcapi id="owners-files" class="TF\Engine\APIClasses\Owners\OwnersFiles" />
            <rpcapi id="owners-heritors-combobox" class="TF\Engine\APIClasses\Owners\OwnersHeritorsCombobox" />
            <rpcapi id="owners-heritors-tree" class="TF\Engine\APIClasses\Owners\OwnersHeritorsTree" />
            <rpcapi id="owners-inherited-plots-grid" class="TF\Engine\APIClasses\Owners\OwnersInheritedPlotsGrid" />
            <rpcapi id="owners-payments" class="TF\Engine\APIClasses\Owners\OwnersPaymentsGrid" />
            <rpcapi id="owners-plots-grid" class="TF\Engine\APIClasses\Owners\OwnersPlotsGrid" />
            <rpcapi id="owners-tree" class="TF\Engine\APIClasses\Owners\OwnersTree" />
            <rpcapi id="owners-list" class="TF\Engine\APIClasses\Owners\OwnersList" />
            <rpcapi id="owners-parents-tree" class="TF\Engine\APIClasses\Owners\OwnersParentsTree"/>

            <!--Representatives Related Classes-->
            <rpcapi id="representatives-tree" class="TF\Engine\APIClasses\Owners\RepresentativesTree" />
            <rpcapi id="representatives-plots-grid" class="TF\Engine\APIClasses\Owners\RepresentativesPlotsGrid" />
            <rpcapi id="owners-represented-grid" class="TF\Engine\APIClasses\Owners\OwnersRepresentedGrid"/>
        </service>

        <service id="map-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="all-layers-tree" class="TF\Engine\APIClasses\Diary\MapLayersTree" />
            <rpcapi id="allowable-datagrid" class="TF\Engine\APIClasses\Map\AllowableGrid" />
            <rpcapi id="attribute-tables" class="TF\Engine\APIClasses\Map\AttributeTables" />
            <rpcapi id="export-layer" class="TF\Engine\APIClasses\Map\ExportLayerData" />
            <rpcapi id="navigation-layer-data" class="TF\Engine\APIClasses\Map\NavigationLayerData" />
            <rpcapi id="export-layer-graphically" class="TF\Engine\APIClasses\Map\ExportLayerGraphically" />
            <rpcapi id="for-isak-datagrid" class="TF\Engine\APIClasses\Map\ForIsakGrid" />
            <rpcapi id="isak-datagrid" class="TF\Engine\APIClasses\Map\ISAKGrid" />
            <rpcapi id="kms-datagrid" class="TF\Engine\APIClasses\Map\KmsGrid" />
            <rpcapi id="kvs-datagrid" class="TF\Engine\APIClasses\Map\KVSGrid" />
            <rpcapi id="kvs-merge" class="TF\Engine\APIClasses\Map\KVSMerge" />
            <rpcapi id="kvs-split-propertygrid" class="TF\Engine\APIClasses\Map\KVSSplitPrGrid" />
            <rpcapi id="layer-change" class="TF\Engine\APIClasses\Map\LayerChange" />
            <rpcapi id="layer-pg" class="TF\Engine\APIClasses\Map\LayersPropertyGrid" />
            <rpcapi id="layer-vps-gaski-chervenogushi-datagrid" class="TF\Engine\APIClasses\Map\VPSGaskiChervenogushiLayerGrid" />
            <rpcapi id="layer-vps-gaski-zimni-datagrid" class="TF\Engine\APIClasses\Map\VPSGaskiZimniLayerGrid" />
            <rpcapi id="layer-vps-livaden-blatar-datagrid" class="TF\Engine\APIClasses\Map\VPSLivadenBlatarLayerGrid" />
            <rpcapi id="layer-vps-orli-leshoyadi-datagrid" class="TF\Engine\APIClasses\Map\VPSOrliLeshoyadiLayerGrid" />
            <rpcapi id="map-tools" class="TF\Engine\APIClasses\Map\MapTools" />
            <rpcapi id="remote-layer-datagrid" class="TF\Engine\APIClasses\Map\RemoteLayerGrid" />
            <rpcapi id="zp-datagrid" class="TF\Engine\APIClasses\Map\ZPGrid" />
            <rpcapi id="gps-datagrid" class="TF\Engine\APIClasses\Map\GpsGrid" />
            <rpcapi id="work-layer-datagrid" class="TF\Engine\APIClasses\Map\WorkLayerGrid" />
            <rpcapi id="gps-data" class="TF\Engine\APIClasses\Map\GpsData" />
            <rpcapi id="layer-zp-data" class="TF\Engine\APIClasses\Map\LayerZpData" />
            <rpcapi id="layer-kvs-data" class="TF\Engine\APIClasses\Map\LayerKVSData" />
            <rpcapi id="map-attributes" class="TF\Engine\APIClasses\Map\MapAttributes" />
            <rpcapi id="layer-cadastre" class="TF\Engine\APIClasses\Map\LayerCadastre" />
            <rpcapi id="layer-surveys" class="TF\Engine\APIClasses\Map\LayerSurveys" />
            <rpcapi id="layer-datagrid" class="TF\Engine\APIClasses\Map\LayerDatagrid" />
            <rpcapi id="datagrid-filter" class="TF\Engine\APIClasses\Map\Filters\DatagridFilter" />
            <rpcapi id="csd-datagrid" class="TF\Engine\APIClasses\Map\CsdGrid" />
        </service>

        <service id="thematic-maps-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="thematic-maps-layers"              class="TF\Engine\APIClasses\ThematicMaps\ThematicMapsLayers" />
            <rpcapi id="thematic-maps-results-grid"        class="TF\Engine\APIClasses\ThematicMaps\ThematicMapsResultsGrid" />
            <rpcapi id="thematic-maps-main-layer-combobox" class="TF\Engine\APIClasses\ThematicMaps\ThematicMapsMainLayerCombobox" />
            <rpcapi id="main-layer-columns-combobox"       class="TF\Engine\APIClasses\ThematicMaps\ThematicMapsMainLayerColumns" />
            <rpcapi id="thematic-maps-color-palettes"      class="TF\Engine\APIClasses\ThematicMaps\ThematicMapsColorPalettes" />
        </service>

        <service id="subsidieswizard-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="for-isak-layers-tree" class="TF\Engine\APIClasses\SubsidiesWizard\LayersTree" />
            <rpcapi id="pg-intermidiate-cultures" class="TF\Engine\APIClasses\SubsidiesWizard\PropertyGridIntermidiateCultures" />
            <rpcapi id="for-isak-subsidies-datagrid" class="TF\Engine\APIClasses\SubsidiesWizard\ForIsakSubsidiesGrid"/>
            <rpcapi id="for-isak-diff-allowable-final-grid" class="TF\Engine\APIClasses\SubsidiesWizard\ForIsakDiffAllowableFinalGrid"/>
            <rpcapi id="for-isak-kvs-report" class="TF\Engine\APIClasses\SubsidiesWizard\ForIsakKVSReportGrid"/>
            <rpcapi id="for-isak-report-pzp-grid" class="TF\Engine\APIClasses\SubsidiesWizard\ForIsakPZPReportGrid"/>
            <rpcapi id="for-isak-report-pndp-grid" class="TF\Engine\APIClasses\SubsidiesWizard\ForIsakPNDPReportGrid"/>
            <rpcapi id="for-isak-diff-lfa-grid" class="TF\Engine\APIClasses\SubsidiesWizard\ForIsakDiffLfaGrid"/>
            <rpcapi id="for-isak-diff-natura-grid" class="TF\Engine\APIClasses\SubsidiesWizard\ForIsakDiffNaturaGrid"/>
            <rpcapi id="for-isak-diff-vps-main-grid" class="TF\Engine\APIClasses\SubsidiesWizard\ForIsakDiffVPSMainGrid"/>
            <rpcapi id="for-isak-report-zdp-grid" class="TF\Engine\APIClasses\SubsidiesWizard\ForIsakZDPReportGrid"/>
            <rpcapi id="for-isak-diff-vps-detailed-grid" class="TF\Engine\APIClasses\SubsidiesWizard\ForIsakDiffVPSDetailedGrid"/>
            <rpcapi id="layer-pg" class="TF\Engine\APIClasses\Map\LayersPropertyGrid" />
            <rpcapi id="all-layers-tree" class="TF\Engine\APIClasses\Diary\MapLayersTree" />
        </service>

        <service id="payments-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="payments-contracts-tree" class="TF\Engine\APIClasses\Payments\ContractsTree" />
            <rpcapi id="contract-payments-grid" class="TF\Engine\APIClasses\Payments\ContractPaymentsGrid" />
            <rpcapi id="contract-add-payment" class="TF\Engine\APIClasses\Payments\AddPayment" />
            <rpcapi id="export-payment" class="TF\Engine\APIClasses\Payments\ExportPayment" />
            <rpcapi id="personal-use-grid" class="TF\Engine\APIClasses\Payments\PersonalUseGrid" />
            <rpcapi id="weighing-note-grid" class="TF\Engine\APIClasses\Payments\WeighingNoteGrid" />
            <rpcapi id="charged-renta-history-grid" class="TF\Engine\APIClasses\Payments\ChargedRentaHistoryGrid" />
            <rpcapi id="charged-renta-history-tree" class="TF\Engine\APIClasses\Payments\ChargedRentaHistoryTree" />
            <rpcapi id="info-summary-report-by-ekate-money" class="TF\Engine\APIClasses\Payments\InfoSummaryReportByEkateMoney" />
            <rpcapi id="payments-reports-by-date-grid" class="TF\Engine\APIClasses\Payments\PaymentsReportsByDateGrid" />
            <rpcapi id="payments-reports-by-date-grid-export-print" class="TF\Engine\APIClasses\Payments\PaymentsReportsByDateGridExportAndPrint" />
            <rpcapi id="payments-reports-grid" class="TF\Engine\APIClasses\Payments\PaymentsReportsGrid" />
            <rpcapi id="payments-reports-grid-export-print" class="TF\Engine\APIClasses\Payments\PaymentsReportsGridExportAndPrint" />
            <rpcapi id="payments-reports-renta-natura-grid" class="TF\Engine\APIClasses\Payments\PaymentsReportsRentaNaturaGrid" />
            <rpcapi id="payments-reports-renta-natura-grid-export-print" class="TF\Engine\APIClasses\Payments\PaymentsReportsRentaNaturaGridExportAndPrint" />
            <rpcapi id="transactions-grid" class="TF\Engine\APIClasses\Payments\TransactionsGrid" />
            <rpcapi id="payments-bank-payment-report" class="TF\Engine\APIClasses\Payments\PaymentsBankPaymentReport" />
            <rpcapi id="payments-bank-and-natura-payment-report" class="TF\Engine\APIClasses\Payments\PaymentsBankAndNaturaPaymentReport" />
            <rpcapi id="personal-use-report" class="TF\Engine\APIClasses\Payments\PersonalUseReport" />
            <rpcapi id="transactions-payments-grid" class="TF\Engine\APIClasses\Payments\TransactionPaymentsGrid" />
            <rpcapi id="add-charged-renta" class="TF\Engine\APIClasses\Payments\AddChargedRenta" />
            <rpcapi id="unpaid-rented-report-grid" class="TF\Engine\APIClasses\Payments\UnpaidReantaReportGrid" />
        </service>

        <service id="payroll-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="payroll-grid" class="TF\Engine\APIClasses\Payroll\PayrollGrid" />
            <rpcapi id="payroll-grid-export-print" class="TF\Engine\APIClasses\Payroll\PayrollGridExportAndPrint" />
            <rpcapi id="payroll-exports-grid" class="TF\Engine\APIClasses\Payroll\PayrollExportsGrid" />
            <rpcapi id="light-payroll-grid" class="TF\Engine\APIClasses\Payroll\LightPayrollGrid" />
        </service>

         <service id="owner-payments-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="owners-contracts-tree" class="TF\Engine\APIClasses\OwnerPayments\OwnersTree" />
            <rpcapi id="contracts-owner-payments-grid" class="TF\Engine\APIClasses\OwnerPayments\ContractsByOwnerPaymentsGrid" />
            <rpcapi id="owner-personal-use-grid" class="TF\Engine\APIClasses\OwnerPayments\PersonalUseGrid" />
        </service>

        <service id="annexes-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="annexes-files" class="TF\Engine\APIClasses\Annexes\AnnexesFiles" />
            <rpcapi id="annexes-tree" class="TF\Engine\APIClasses\Annexes\AnnexesTree" />
            <rpcapi id="annexes-plots-grid" class="TF\Engine\APIClasses\Annexes\AnnexesPlotsGrid" />
        </service>

 		<service id="subleases-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="sublease-contragents-grid" class="TF\Engine\APIClasses\Subleases\SubleaseContragentsGrid" />
            <rpcapi id="sublease-farming-contragents-grid" class="TF\Engine\APIClasses\Subleases\SubleaseFarmingContragentsGrid" />
            <rpcapi id="sublease-plots-grid" class="TF\Engine\APIClasses\Subleases\SubleasePlotsGrid" />
            <rpcapi id="sublease-contracts-grid" class="TF\Engine\APIClasses\Subleases\SubleaseContractsGrid" />
            <rpcapi id="sublease-plot-owners-grid" class="TF\Engine\APIClasses\Subleases\SubleasePlotOwnersGrid" />
            <rpcapi id="subleases-files" class="TF\Engine\APIClasses\Subleases\SubleasesFiles" />
            <rpcapi id="subleases-tree" class="TF\Engine\APIClasses\Subleases\SubleasesTree" />
            <rpcapi id="sublease-contragent-data" class="TF\Engine\APIClasses\Subleases\SubleaseContragentData" />
            <rpcapi id="subleases-exports" class="TF\Engine\APIClasses\Subleases\SubleasesExports" />
        </service>

        <service id="collections-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="collections-contracts-grid" class="TF\Engine\APIClasses\Collections\CollectionsContractsGrid" />
            <rpcapi id="collection-payments-grid" class="TF\Engine\APIClasses\Collections\CollectionPaymentsGrid" />
            <rpcapi id="export-collection" class="TF\Engine\APIClasses\Collections\ExportCollection" />
        </service>

        <service id="exports-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="export-payment" class="TF\Engine\APIClasses\Exports\PaymentExports" />
        </service>

        <service id="diary-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="all-layers-tree" class="TF\Engine\APIClasses\Diary\MapLayersTree" />
            <rpcapi id="diary-configs-grid" class="TF\Engine\APIClasses\Diary\DiaryConfigsGrid" />
            <rpcapi id="diary-configs-combobox" class="TF\Engine\APIClasses\Diary\DiaryConfigsCombobox" />
            <rpcapi id="substances-diary-grid" class="TF\Engine\APIClasses\Diary\SubstancesDiaryGrid" />
            <rpcapi id="fertilizers-diary-grid" class="TF\Engine\APIClasses\Diary\FertilizersDiaryGrid" />
            <rpcapi id="fuel-diary-grid" class="TF\Engine\APIClasses\Diary\FuelDiaryGrid"/>
            <rpcapi id="kvs-diary-grid" class="TF\Engine\APIClasses\Diary\KVSDiaryGrid"/>
            <rpcapi id="diary-event-phase-combobox" class="TF\Engine\APIClasses\Diary\DiaryEventPhaseCombobox" />
            <rpcapi id="diary-reports-grid" class="TF\Engine\APIClasses\Diary\DiaryReportsGrid" />
            <rpcapi id="diary-auxiliary-items" class="TF\Engine\APIClasses\Diary\DiaryAuxiliaryItems" />
            <rpcapi id="diary-expenses-grid" class="TF\Engine\APIClasses\Diary\DiaryExpensesGrid" />
            <rpcapi id="diary-zplot-events-grid" class="TF\Engine\APIClasses\Diary\ZPlotEventsGrid" />
            <rpcapi id="diary-zp-tree" class="TF\Engine\APIClasses\Diary\ZPTree" />
            <rpcapi id="diary-map" class="TF\Engine\APIClasses\Diary\DiaryMap" />
            <rpcapi id="wialon-actions" class="TF\Engine\APIClasses\Diary\WialonActions" />
        </service>

        <service id="farming-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="farming-grid" class="TF\Engine\APIClasses\Farming\FarmingGrid" />
            <rpcapi id="farming-iban" class="TF\Engine\APIClasses\Farming\FarmingIban" />
        </service>

        <service id="files-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="files" class="TF\Engine\APIClasses\Files\FilesGrid"/>
            <rpcapi id="definition" class="TF\Engine\APIClasses\Files\KVSDefinition" />
        </service>

        <service id="sales-contracts-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="sales-contracts-tree" class="TF\Engine\APIClasses\SalesContracts\SalesContractsTree" />
            <rpcapi id="sales-contracts-plots-datagrid" class="TF\Engine\APIClasses\SalesContracts\SalesContractsPlotsGrid" />
            <rpcapi id="sales-contracts-files-maingrid" class="TF\Engine\APIClasses\SalesContracts\SalesContractsFilesGrid" />
            <rpcapi id="sales-contracts-add-plots-datagrid" class="TF\Engine\APIClasses\SalesContracts\SalesContractsAddPlotsGrid" />
            <rpcapi id="sales-contracts-subleased-plots-grid" class="TF\Engine\APIClasses\SalesContracts\SalesContractsSubleasedPlotsGrid" />
            <rpcapi id="buyers-sales-contracts-grid" class="TF\Engine\APIClasses\SalesContracts\BuyersGrid" />
            <rpcapi id="buyers-sales-contracts-relation-grid" class="TF\Engine\APIClasses\SalesContracts\BuyersSalesContractsRelationGrid" />
            <rpcapi id="report-sales-contracts-grid" class="TF\Engine\APIClasses\SalesContracts\ReportSalesContractsGrid" />
            <rpcapi id="sales-contracts-exports" class="TF\Engine\APIClasses\SalesContracts\SalesContractsExports" />
        </service>

        <service id="coverage-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="coverage-file-structure-tree" class="TF\Engine\APIClasses\Coverage\FileStructureTree" />
            <rpcapi id="coverage-files-tree" class="TF\Engine\APIClasses\Coverage\FilesTree" />
            <rpcapi id="coverage-zp-data-tree" class="TF\Engine\APIClasses\Coverage\ZPLayerDataTree" />
            <rpcapi id="coverage-zp-layers-tree" class="TF\Engine\APIClasses\Coverage\ZPLayersTree" />
            <rpcapi id="coverage-map" class="TF\Engine\APIClasses\Coverage\CoverageMap" />
        </service>

        <service id="zplots-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="kvs-contracts-datagrid" class="TF\Engine\APIClasses\ZPlots\KVSContractsGrid" />
            <rpcapi id="kvs-owners-datagrid" class="TF\Engine\APIClasses\ZPlots\KVSOwnersGrid" />
            <rpcapi id="kvs-zp-map-info" class="TF\Engine\APIClasses\ZPlots\KVSMapInfo" />
            <rpcapi id="zp-info-pg" class="TF\Engine\APIClasses\ZPlots\InfoPropertyGrid" />
            <rpcapi id="zp-kvs-report" class="TF\Engine\APIClasses\ZPlots\ZPReportKVSGrid" />
            <rpcapi id="zplots-export" class="TF\Engine\APIClasses\ZPlots\ZPlotsExport" />
            <rpcapi id="zplots-layers-tree" class="TF\Engine\APIClasses\ZPlots\LayersTree" />
            <rpcapi id="zplots-maingrid" class="TF\Engine\APIClasses\ZPlots\PlotsGrid" />
            <rpcapi id="zplots-report-grid" class="TF\Engine\APIClasses\ZPlots\ZPlotsReportGrid" />
            <rpcapi id="zp-map-info" class="TF\Engine\APIClasses\ZPlots\MapInfo" />
        </service>

        <service id="isak-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="isak-diff-allowable-grid" class="TF\Engine\APIClasses\Isak\IsakDiffAllowableGrid" />
            <rpcapi id="isak-layers-tree" class="TF\Engine\APIClasses\Isak\LayersTree" />
            <rpcapi id="isak-maingrid" class="TF\Engine\APIClasses\Isak\IsakGrid" />
            <rpcapi id="isak-report-grid" class="TF\Engine\APIClasses\Isak\IsakReportGrid" />
        </service>

        <service id="croprotation-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="crop-layer-isak-combobox" class="TF\Engine\APIClasses\CropRotation\CropLayerIsakCombobox" />
            <rpcapi id="croprotation-overlap-report" class="TF\Engine\APIClasses\CropRotation\OverlapReportGrid" />
            <rpcapi id="croprotation-culture-report" class="TF\Engine\APIClasses\CropRotation\CultureReportGrid" />
            <rpcapi id="layer-data-grid" class="TF\Engine\APIClasses\CropRotation\LayerDataGrid" />
            <rpcapi id="crop-layers-tree" class="TF\Engine\APIClasses\CropRotation\CropLayersTree" />
            <rpcapi id="sample-files-tree" class="TF\Engine\APIClasses\CropRotation\SoilSampleFilesTree" />
            <rpcapi id="apply-ssf-combobox" class="TF\Engine\APIClasses\CropRotation\ApplySoilSampleFileCombobox" />
            <rpcapi id="avg-soil-samples-norm-grid" class="TF\Engine\APIClasses\CropRotation\AVGSoilSamplesNormGrid" />
            <rpcapi id="soil-samples-norm-grid" class="TF\Engine\APIClasses\CropRotation\SoilSamplesNormGrid" />
            <rpcapi id="crop-data-isak-combobox" class="TF\Engine\APIClasses\CropRotation\CropDataIsakCombobox" />
            <rpcapi id="soil-samples-files-grid" class="TF\Engine\APIClasses\CropRotation\SoilSamplesFilesGrid" />
            <rpcapi id="soil-samples-files-datagrid" class="TF\Engine\APIClasses\CropRotation\SoilSamplesFilesDataGrid" />
            <rpcapi id="soil-samples-datagrid" class="TF\Engine\APIClasses\CropRotation\SoilSamplesGrid" />
            <rpcapi id="crop-rotation-exports" class="TF\Engine\APIClasses\CropRotation\CropRotationExports" />
        </service>

        <service id="overlaps-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="overlaps-layers-tree" class="TF\Engine\APIClasses\Overlaps\OverlapsLayersTree" />
            <rpcapi id="overlaps-data-datagrid" class="TF\Engine\APIClasses\Overlaps\OverlapsDataGrid" />
            <rpcapi id="overlaps-area-report-grid" class="TF\Engine\APIClasses\Overlaps\OverlapsAreaReportGrid" />
            <rpcapi id="overlaps-map-tools" class="TF\Engine\APIClasses\Overlaps\OverlapsMapTools" />
        </service>

        <service id="agreements-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="agreements-data-datagrid" class="TF\Engine\APIClasses\Agreements\AgreementsDataGrid" />
            <rpcapi id="agreements-layers-tree" class="TF\Engine\APIClasses\Agreements\AgreementsLayersTree" />
            <rpcapi id="agreements-ekate-combobox" class="TF\Engine\APIClasses\Agreements\AgreementsEkateCombobox" />
            <rpcapi id="agreements-map" class="TF\Engine\APIClasses\Agreements\AgreementsMap" />
            <rpcapi id="agreements-datagrid" class="TF\Engine\APIClasses\Agreements\AgreementsGrid" />
        </service>

        <service id="kvs-contracts-update-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="kvs-contracts-plots-for-update-grid" class="TF\Engine\APIClasses\KVSContractsUpdate\KVSContractsPlotsForUpdateGrid" />
        </service>

        <service id="kvs-invalid-geometry-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="kvs-invalid-plots-grid" class="TF\Engine\APIClasses\KVSInvalidGeometry\KVSInvalidGeometryPlotsGrid" />
            <rpcapi id="kvs-invalid-plots-map-tools" class="TF\Engine\APIClasses\KVSInvalidGeometry\KVSInvalidGeometryMapTools" />
        </service>
        <service id="notifications-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="notification-grid" class="TF\Engine\APIClasses\Notifications\NotificationsMainGrid" />
        </service>
        <service id="dashboard-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="dashboard-payments" class="TF\Engine\APIClasses\Dashboard\DashboardPayments" />
            <rpcapi id="dashboard-contracts" class="TF\Engine\APIClasses\Dashboard\DashboardContracts" />
            <rpcapi id="dashboard-owngrid" class="TF\Engine\APIClasses\Dashboard\DashboardOwnGrid" />
            <rpcapi id="dashboard-owncontracts" class="TF\Engine\APIClasses\Dashboard\DashboardOwnContracts" />
            <rpcapi id="dashboard-subleases" class="TF\Engine\APIClasses\Dashboard\DashboardSubleases" />
            <rpcapi id="dashboard-rents" class="TF\Engine\APIClasses\Dashboard\DashboardRents" />
        </service>
        <service id="common-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="integrations-hooks" class="TF\Engine\APIClasses\Common\IntegrationsHooks" />
            <rpcapi id="combined-combobox-data" class="TF\Engine\APIClasses\Common\CombinedComboboxData" />
            <rpcapi id="allowable-ekate-combobox" class="TF\Engine\APIClasses\Common\AllowableEkateCombobox" />
            <rpcapi id="allowable-layer-ntp-combobox" class="TF\Engine\APIClasses\Common\AllowableLayerNTPCombobox" />
            <rpcapi id="chervenogushi-ekate-combobox" class="TF\Engine\APIClasses\Common\VPSChervenogushiGaskiEkateCombobox" />
            <rpcapi id="contract-status-combobox" class="TF\Engine\APIClasses\Common\ContractStatusCombobox" />
            <rpcapi id="contract-type-combobox" class="TF\Engine\APIClasses\Common\ContractTypeCombobox" />
            <rpcapi id="copy-layer-combobox" class="TF\Engine\APIClasses\Common\CopyLayersCombobox" />
            <rpcapi id="culture-combobox" class="TF\Engine\APIClasses\Common\CultureCombobox" />
            <rpcapi id="isak-culture-combobox" class="TF\Engine\APIClasses\Common\IsakCultureCombobox" />
            <rpcapi id="isak-ekatte-combobox" class="TF\Engine\APIClasses\Common\IsakEkatteCombobox" />
            <rpcapi id="isak-campaign-combobox" class="TF\Engine\APIClasses\Common\IsakCampaignCombobox" />
            <rpcapi id="culture-combobox-by-layername" class="TF\Engine\APIClasses\Common\CultureComboboxByLayerName" />
            <rpcapi id="ds-type-combobox" class="TF\Engine\APIClasses\Common\DSTypeCombobox" />
            <rpcapi id="ekate-combobox" class="TF\Engine\APIClasses\Common\EkateCombobox" />
            <rpcapi id="farming-combobox" class="TF\Engine\APIClasses\Common\FarmingCombobox" />
            <rpcapi id="farming-year-combobox" class="TF\Engine\APIClasses\Common\FarmingYearCombobox" />
            <rpcapi id="farm-name-combobox" class="TF\Engine\APIClasses\Common\FarmNameCombobox" />
            <rpcapi id="device-type-combo" class="TF\Engine\APIClasses\Common\DeviceTypeCombobox" />
            <rpcapi id="kms-ekate-combobox" class="TF\Engine\APIClasses\Common\KmsEkateCombobox" />
            <rpcapi id="kvs-free-numbers" class="TF\Engine\APIClasses\Common\KVSFreeNumbersCombobox" />
            <rpcapi id="layer-selection-combobox" class="TF\Engine\APIClasses\Common\LayerSelectionCombobox" />
            <rpcapi id="projection" class="TF\Engine\APIClasses\Common\LayersProjection" />
            <rpcapi id="lfa-ekate-combobox" class="TF\Engine\APIClasses\Common\LFAEkateCombobox" />
            <rpcapi id="livaden-blatar-name-combobox" class="TF\Engine\APIClasses\Common\VPSLivadenBlatarNameCombobox" />
            <rpcapi id="map-types-combobox" class="TF\Engine\APIClasses\Common\MapTypesCombobox" />
            <rpcapi id="mestnost-combobox" class="TF\Engine\APIClasses\Common\MestnostCombobox" />
            <rpcapi id="natura-2000-name-combobox" class="TF\Engine\APIClasses\Common\Natura2000NameCombobox" />
            <rpcapi id="orli-leshoyadi-ekate-combobox" class="TF\Engine\APIClasses\Common\VPSOrliLeshoyadiEkateCombobox" />
            <rpcapi id="plot-category-combobox" class="TF\Engine\APIClasses\Common\PlotCategoryCombobox" />
            <rpcapi id="plot-ntp-combobox" class="TF\Engine\APIClasses\Common\PlotNTPCombobox" />
            <rpcapi id="pzp-ekate-combobox" class="TF\Engine\APIClasses\Common\PZPEkateCombobox" />
            <rpcapi id="zp-ekate-combobox" class="TF\Engine\APIClasses\Common\ZpEkateCombobox" />
            <rpcapi id="renta-types-combobox" class="TF\Engine\APIClasses\Common\RentaTypesCombobox" />
            <rpcapi id="zimni-ekate-combobox" class="TF\Engine\APIClasses\Common\VPSZimniGaskiEkateCombobox" />
            <rpcapi id="owners-info" class="TF\Engine\APIClasses\Common\OwnersInfo" />
            <rpcapi id="plot-info" class="TF\Engine\APIClasses\Common\PlotInfo" />
            <rpcapi id="contract-info" class="TF\Engine\APIClasses\Common\ContractInfo" />
            <rpcapi id="templates-grid" class="TF\Engine\APIClasses\Common\TemplatesGrid" />
            <rpcapi id="copy-layer-status-combobox" class="TF\Engine\APIClasses\Common\CopyLayerStatusCombobox" />
            <rpcapi id="renta-type-grid" class="TF\Engine\APIClasses\Common\RentaTypeGrid" />
            <rpcapi id="remove-file" class="TF\Engine\APIClasses\Common\RemoveFile" />
            <rpcapi id="payer-names-combobox" class="TF\Engine\APIClasses\Common\PayerNamesCombobox" />
            <rpcapi id="cancelled-by-combobox" class="TF\Engine\APIClasses\Common\CancelledByCombobox" />
            <rpcapi id="transaction-types-combobox" class="TF\Engine\APIClasses\Common\TransactionTypesCombobox" />
            <rpcapi id="payments-rent-places-combobox" class="TF\Engine\APIClasses\Common\PaymentsRentPlacesCombobox" />
            <rpcapi id="renta-units-combobox" class="TF\Engine\APIClasses\Common\RentaUnitsCombobox"/>
            <rpcapi id="contract-agg-types-combo" class="TF\Engine\APIClasses\Common\ContractAggTypesCombobox" />
            <rpcapi id="contract-template-variables" class="TF\Engine\APIClasses\Common\ContractTemplateVariables" />
            <rpcapi id="change-password" class="TF\Engine\APIClasses\Common\ChangeUserPassword" />
            <rpcapi id="for-isak-ekate-combobox" class="TF\Engine\APIClasses\Common\ForIsakEkateCombobox" />
            <rpcapi id="schema-combobox" class="TF\Engine\APIClasses\Common\SchemaCombobox"/>
            <rpcapi id="vps-schema-types-combobox" class="TF\Engine\APIClasses\Common\VPSSchemaCombobox"/>
            <rpcapi id="culture-short-type-combobox" class="TF\Engine\APIClasses\Common\CultureShortTypeCombobox"/>
            <rpcapi id="crop-year-combobox" class="TF\Engine\APIClasses\Common\CropYearCombobox" />
            <rpcapi id="sample-norm-year-combobox" class="TF\Engine\APIClasses\Common\SampleNormYearCombobox" />
            <rpcapi id="soil-types-combobox" class="TF\Engine\APIClasses\Common\SoilTypesCombobox" />
            <rpcapi id="soil-humus-combobox" class="TF\Engine\APIClasses\Common\SoilHumusCombobox" />
            <rpcapi id="kvs-update-action-type-combobox" class="TF\Engine\APIClasses\Common\KVSUpdateActionTypeCombobox" />
            <rpcapi id="salesman-list" class="TF\Engine\APIClasses\Common\SalesmanComboBox" />
            <rpcapi id="roles-list" class="TF\Engine\APIClasses\Common\RolesComboBox" />
            <rpcapi id="roles-list-full" class="TF\Engine\APIClasses\Common\RolesComboBoxFull" />
            <rpcapi id="subleased-contract-type"  class="TF\Engine\APIClasses\Common\SubleasedContractType"/>
            <rpcapi id="user-server-combobox" class="TF\Engine\APIClasses\Common\UserServersCombobox" />
            <rpcapi id="irrigated-area-combobox" class="TF\Engine\APIClasses\Common\IrrigatedAreaCombobox" />
            <rpcapi id="label-names-combobox" class="TF\Engine\APIClasses\Common\LabelNamesCombobox" />
            <rpcapi id="topic-layer-styler-combobox" class="TF\Engine\APIClasses\Common\TopicLayerStylerCombobox" />
            <rpcapi id="osz-ekate-combobox" class="TF\Engine\APIClasses\Common\OSZEkateCombobox" />
            <rpcapi id="sublease-type-combobox" class="TF\Engine\APIClasses\Common\SubleaseTypeCombobox" />
            <rpcapi id="payment-subjects-grid" class="TF\Engine\APIClasses\Common\PaymentSubjectsGrid" />
            <rpcapi id="payment-subjects-combobox" class="TF\Engine\APIClasses\Common\PaymentSubjectsCombobox" />
            <rpcapi id="contact-form" class="TF\Engine\APIClasses\Common\ContactForm" />
            <rpcapi id="modems-combobox" class="TF\Engine\APIClasses\Common\ModemsCombobox" />
            <rpcapi id="obshtini-combobox" class="TF\Engine\APIClasses\Common\ObshtiniCombobox" />
            <rpcapi id="kmetstva-combobox" class="TF\Engine\APIClasses\Common\KmetstvaCombobox" />
            <rpcapi id="tf-modules-combobox" class="TF\Engine\APIClasses\Common\TFModulesCombobox" />
            <rpcapi id="parent-warehouses-combobox" class="TF\Engine\APIClasses\Common\ParentWarehousesCombobox" />
            <rpcapi id="warehouse-contragents-combobox" class="TF\Engine\APIClasses\Common\WarehouseContragentsCombobox" />
            <rpcapi id="warehouse-items-combobox" class="TF\Engine\APIClasses\Common\WarehouseItemsCombobox" />
            <rpcapi id="warehouse-measures-combobox" class="TF\Engine\APIClasses\Common\WarehouseMeasuresCombobox" />
            <rpcapi id="warehouse-available-warehouses-combobox" class="TF\Engine\APIClasses\Common\WarehouseAvailableWarehousesCombobox" />
            <rpcapi id="warehouse-warehouses-combobox" class="TF\Engine\APIClasses\Common\WarehouseWarehousesCombobox" />
            <rpcapi id="warehouse-companies-combobox" class="TF\Engine\APIClasses\Common\WarehouseCompaniesCombobox" />
            <rpcapi id="warehouse-constants-combobox" class="TF\Engine\APIClasses\Common\WarehouseConstantsCombobox" />
            <rpcapi id="warehouse-item-groups-combobox" class="TF\Engine\APIClasses\Common\WarehouseItemGroupsCombobox" />
            <rpcapi id="warehouse-company-groups-combobox" class="TF\Engine\APIClasses\Common\WarehouseCompanyGroupsCombobox" />
            <rpcapi id="countries-combobox" class="TF\Engine\APIClasses\Common\CountriesCombobox" />
            <rpcapi id="contract-groups-combobox" class="TF\Engine\APIClasses\Common\ContractGroupsCombobox" />
            <rpcapi id="allowable-layers-ntp-combobox" class="TF\Engine\APIClasses\Common\AllowableLayersNtpCombobox" />
        </service>
        <service id="reports-rpc" class="TF\Engine\APIClasses\MTRpcService">
        </service>
        <service id="global-notifications-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="global-notifications-grid" class="TF\Engine\APIClasses\GlobalNotifications\GlobalNotificationsMainGrid" />
            <rpcapi id="global-notifications-types" class="TF\Engine\APIClasses\GlobalNotifications\GlobalNotificationsTypes" />
        </service>
        <service id="warehouse-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="warehouses" class="TF\Engine\APIClasses\Warehouse\Warehouses" />
            <rpcapi id="warehouse-transactions" class="TF\Engine\APIClasses\Warehouse\Transactions" />
            <rpcapi id="warehouse-companies" class="TF\Engine\APIClasses\Warehouse\Companies" />
            <rpcapi id="warehouse-farms" class="TF\Engine\APIClasses\Warehouse\Farms" />
            <rpcapi id="warehouse-groups" class="TF\Engine\APIClasses\Warehouse\Groups" />
            <rpcapi id="warehouse-items" class="TF\Engine\APIClasses\Warehouse\Items" />
            <rpcapi id="warehouse-documents" class="TF\Engine\APIClasses\Warehouse\Documents" />
            <rpcapi id="warehouse-measures" class="TF\Engine\APIClasses\Warehouse\Measures" />
            <rpcapi id="warehouse-users" class="TF\Engine\APIClasses\Warehouse\Users" />
            <rpcapi id="warehouse-articles" class="TF\Engine\APIClasses\Warehouse\Articles" />
            <rpcapi id="warehouse-utils" class="TF\Engine\APIClasses\Warehouse\Utils" />
            <rpcapi id="warehouse-history" class="TF\Engine\APIClasses\Warehouse\HistoryLog" />
            <rpcapi id="warehouse-config" class="TF\Engine\APIClasses\Warehouse\Configs" />
        </service>
        <service id="tests-rpc" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="tests-tools" class="TF\Engine\APIClasses\Tests\Tests" />
        </service>

        <service id="kvs-store-service" class="TF\Engine\APIClasses\MTRpcService">
            <rpcapi id="kvs-store-rpc" class="TF\Engine\APIClasses\KvsStoreService\KvsStore" />
        </service>

        <service id="kvs-client-json" class="TF\Engine\Kernel\KvsStoreJsonService">
            <json id="kvs-store" class="TF\Engine\APIClasses\System\KvsStore" />
            <json id="kvs-status" class="TF\Engine\APIClasses\System\KvsStatus" />
        </service>

        <service id="keycloak-system-json" class="TF\Engine\Kernel\KeycloakSystemJsonService">
             <json id="revoke-organization-users-rights" class="TF\Engine\APIClasses\System\RevokeOrganizationUsersRights" />
        </service>

        <service id="json" class="System.Web.Services.TJsonService">
            <json class="TF\Engine\APIClasses\Login\BackchannelLogout" id="logout"/>
            <json class="TF\Engine\APIClasses\Uploads\ContractUpload" id="contract-upload"/>
            <json class="TF\Engine\APIClasses\Uploads\OszUpload" id="osz-upload"/>
            <json class="TF\Engine\APIClasses\Uploads\AgreementsUpload" id="agreements-upload"/>
            <json class="TF\Engine\APIClasses\Uploads\CooperatorsUpload" id="cooperators-upload"/>
            <json class="TF\Engine\APIClasses\Uploads\CoverageUpload" id="coverage-upload"/>
            <json class="TF\Engine\APIClasses\Uploads\CropLayersUpload" id="crop-layers-upload"/>
            <json class="TF\Engine\APIClasses\Uploads\HypothecsUpload" id="hypothecs-upload"/>
            <json class="TF\Engine\APIClasses\Uploads\OverlapsUpload" id="overlaps-upload"/>
            <json class="TF\Engine\APIClasses\Uploads\OwnersUpload" id="owners-upload"/>
            <json class="TF\Engine\APIClasses\Uploads\SoilSamplesUpload" id="soil-samples-upload"/>
            <json class="TF\Engine\APIClasses\Uploads\LayersUpload" id="layers-upload"/>
            <json class="TF\Engine\APIClasses\Uploads\FileUpload" id="file-upload"/>
        </service>
    </services>
</application>