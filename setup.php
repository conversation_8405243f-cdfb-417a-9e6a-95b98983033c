<?php

system('npm -v', $output);
if ($output != 0 ) {
	system('npm run gulp', $output);
	echo "npm not installed".PHP_EOL;
	die;
}

if (!file_exists('composer.json')) {
	die('composer.json not found');
}
if (!file_exists('composer.phar')) {
	@unlink('composer.lock');
	system('composer help install -q', $output);
	if ($output != 0) {
		echo "Installing composer".PHP_EOL;
		system('php -r "copy(\'https://getcomposer.org/installer\', \'composer-setup.php\');"');
		system('php -r "if (hash_file(\'SHA384\', \'composer-setup.php\') === \'c32408bcd017c577ce80605420e5987ce947a5609e8443dd72cd3867cc3a0cf442e5bf4edddbcbe72246a953a6c48e21\') { echo \'Installer verified\'; } else { echo \'Installer corrupt\'; unlink(\'composer-setup.php\'); } echo PHP_EOL;");');
		
		if (file_exists('composer-setup.php')) {
			system("php composer-setup.php");
		} else {
			echo "Composer not properly installed. Aborting setup".PHP_EOL;
			die();
		}

		system('php -r "unlink(\'composer-setup.php\');"');
		echo "Installing composer packages" . PHP_EOL;
		system('php composer.phar install -q');
		echo "Finished packages installation" . PHP_EOL;
	} else {
		echo "Installing composer packages" . PHP_EOL;
		system('composer install -q', $output);
		echo "Finished packages installation" . PHP_EOL;
	}
} else {
	@unlink('composer.lock');
	echo "Installing composer packages" . PHP_EOL;
	system('php composer.phar install -q');
	echo "Finished packages installation" . PHP_EOL;
}

require_once 'vendor/autoload.php';

if (file_exists('.env')) {
	echo ".env file exists. Skipping initial setup!" . PHP_EOL;
} else {
	echo ".env file does not exist. Enter TF config information ... " . PHP_EOL;
	@unlink('.env');
	touch('.env');
	$envFile = fopen('.env', 'w');
	echo 'ENV_NAME: ';
	$handle = fopen ("php://stdin","r");
	$env_name = fgets($handle);
	$env_name = trim($env_name);
	fwrite($envFile, "ENV_NAME='".strtolower($env_name)."'" . PHP_EOL);
	fclose($handle);

	echo "APPLICATION_MODE ['Off', 'Debug', 'Normal', 'Performance']: ";
	$handle = fopen ("php://stdin","r");
	$app_mode = fgets($handle);
	fclose($handle);
	$app_mode = trim($app_mode);
	$allowedModes = array('Off', 'Debug', 'Normal', 'Performance');
	if (in_array($app_mode, $allowedModes)) {
		fwrite($envFile, "APPLICATION_MODE='".$app_mode."'" . PHP_EOL);
	} else {
		$testValue = '';
		do {
			echo "Possible application modes: ['Off', 'Debug', 'Normal', 'Performance']: " . PHP_EOL;
			echo "APPLICATION_MODE: ";
			$handle = fopen ("php://stdin","r");
			$line = fgets($handle);
			fclose($handle);
			$testValue = trim($line);
		} while (!in_array($testValue, $allowedModes));
		fwrite($envFile, "APPLICATION_MODE='".$line."'" . PHP_EOL);
    }

	echo 'DEFAULT_DB_USERNAME: ';
	$handle = fopen ("php://stdin","r");
	$db_username = fgets($handle);
	$db_username = trim($db_username);
	fwrite($envFile, 'DEFAULT_DB_USERNAME='.$db_username . PHP_EOL);
	fclose($handle);

	echo 'DEFAULT_DB_PASSWORD: ';
	$handle = fopen ("php://stdin","r");
	$db_password = fgets($handle);
	$db_password = trim($db_password);
	fwrite($envFile, "DEFAULT_DB_PASSWORD='".$db_password."'" . PHP_EOL);
	fclose($handle);

	echo 'DBLINK_USERNAME: ';
	$handle = fopen ("php://stdin","r");
	$dblink_db_name = fgets($handle);
    $dblink_db_name = trim($dblink_db_name);
	fwrite($envFile, "DBLINK_USERNAME='".$dblink_db_name."'" . PHP_EOL);
	fclose($handle);

	echo 'DBLINK_PASSWORD: ';
	$handle = fopen ("php://stdin","r");
	$dblink_db_password = fgets($handle);
    $dblink_db_password = trim($dblink_db_password);
	fwrite($envFile, "DBLINK_PASSWORD='".$dblink_db_password."'" . PHP_EOL);
	fclose($handle);

	echo 'APP_UNIQUE_KEY: ';
	$handle = fopen ("php://stdin","r");
	$app_unique_key = fgets($handle);
	$app_unique_key = trim($app_unique_key);
	fwrite($envFile, "APP_UNIQUE_KEY='".$app_unique_key."'" . PHP_EOL);
	fclose($handle);

	echo 'CSS_CUSTOM_THEME_NAME: ';
	$handle = fopen ("php://stdin","r");
	$custom_theme = fgets($handle);
	$custom_theme = trim($custom_theme);
	fwrite($envFile, "CSS_CUSTOM_THEME_NAME='".$custom_theme."'" . PHP_EOL);
	fclose($handle);
	fclose($envFile);
}

try {
	$dotenv = new Dotenv\Dotenv(__DIR__);
	$dotenv->overload();
	$dotenv->required('ENV_NAME')->notEmpty();
	$dotenv->required('APPLICATION_MODE')->allowedValues(array('Off', 'Debug', 'Normal', 'Performance'));
	$dotenv->required('DEFAULT_DB_USERNAME')->notEmpty();
	$dotenv->required('DEFAULT_DB_PASSWORD')->notEmpty();
	$dotenv->required('DBLINK_USERNAME')->notEmpty();
	$dotenv->required('DBLINK_PASSWORD')->notEmpty();
} catch (Exception $e) {
	echo $e->getMessage();
	echo PHP_EOL;
	die;
}

echo 'URL Example: https://login3.technofarm.bg (without last / )' . PHP_EOL;
echo 'URL: ';
$handle = fopen ("php://stdin","r");
$siteUrl = fgets($handle);
$siteUrl = trim($siteUrl);
fclose($handle);

require_once('config/global.config.php');

if (DEFAULT_DB_HOST == 'DEFAULT_DB_HOST' || DEFAULT_DB_PORT == 'DEFAULT_DB_PORT' || DEFAULT_DB_USERNAME == 'DEFAULT_DB_USERNAME' || DEFAULT_DB_DATABASE == 'DEFAULT_DB_DATABASE') {
	echo "Configuration file not loaded!";
	echo PHP_EOL;
	die();
}

try {
	$mainDev = new PDO("pgsql:host=" . DEFAULT_DB_HOST . ";port=" . DEFAULT_DB_PORT . ";dbname=susi_main_test;", DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
	echo 'susi_main_test exists. skipping creation';
	echo PHP_EOL;
} catch (Exception $e) {
	echo 'susi_main_test does not exists. starting creation';
	echo PHP_EOL;
	system("gzip -k -d susi_main_test.sql.gz");
	system('psql -h '.DEFAULT_DB_HOST.' -p '.DEFAULT_DB_PORT.' -U postgres -c "CREATE DATABASE susi_main_test TEMPLATE template0"', $return);
	system('psql -h '.DEFAULT_DB_HOST.' -p '.DEFAULT_DB_PORT.' -U postgres -d susi_main_test -f "susi_main_test.sql"', $return);
	@unlink('susi_main_test.sql');
}

try {
	$mainDev = new PDO("pgsql:host=" . DEFAULT_DB_HOST . ";port=" . DEFAULT_DB_PORT . ";dbname=db_codeception;", DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
	echo 'db_codeception exists. skipping creation';
	echo PHP_EOL;
} catch (Exception $e) {
	echo 'db_codeception does not exists. starting creation';
	echo PHP_EOL;

	system("gzip -k -d db_codeception.sql.gz");
	system('psql -h '.DEFAULT_DB_HOST.' -p '.DEFAULT_DB_PORT.' -U postgres -c "CREATE DATABASE db_codeception TEMPLATE template0"', $return);
	system('psql -h '.DEFAULT_DB_HOST.' -p '.DEFAULT_DB_PORT.' -U postgres -d db_codeception -f "db_codeception.sql"', $return);
	@unlink('db_codeception.sql');
}

if (file_exists('tests/tests/_envs/'.getenv('ENV_NAME').'.yml')) {
	echo "Codeception config for " .getenv('ENV_NAME'). 'exists. Skipping configuration' . PHP_EOL;
} else {
	echo "Creating Codeception configuration for " . getenv('ENV_NAME'). "". PHP_EOL;
	$codeceptionOptions['cc_path'] = __DIR__;
	$codeceptionOptions['cc_url'] = $siteUrl;
	$codeceptionOptions['cc_dbase_ip'] = DEFAULT_DB_HOST;
	$codeceptionOptions['cc_dbase_port'] = DEFAULT_DB_PORT;
	$codeceptionOptions['cc_dbase_user'] = DEFAULT_DB_USERNAME;
	$codeceptionOptions['cc_dbase_pass'] = DEFAULT_DB_PASSWORD;

	extract($codeceptionOptions, EXTR_PREFIX_SAME, "wddx");

	ob_start();
	require('template/codeception.php');
	$content = ob_get_contents();
	ob_end_clean();
	$cc_conf = fopen('tests/tests/_envs/'.getenv('ENV_NAME').'.yml', 'w');
	fwrite($cc_conf, $content);
	fclose($cc_conf);

}

chdir(__DIR__.'/tests');

system('php codecept.phar run api --env '. getenv('ENV_NAME'));