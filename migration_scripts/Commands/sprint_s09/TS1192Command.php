<?php

namespace TF\Commands\sprint_s09;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-1192 command run on all databases.
 */
class TS1192Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s09:TS-1192')
            ->setDescription('Dobavqne na kolona "irrigated_area" (boolean) v "layer_kvs".');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $sql = 'ALTER TABLE "public"."layer_kvs" ADD COLUMN "irrigated_area" BOOLEAN DEFAULT FALSE;
		DROP INDEX "public"."layer_kvs_geom_gist";
		CREATE INDEX "layer_kvs_geom_gist" ON "public"."layer_kvs" USING gist ("geom" "public"."gist_geometry_ops_2d")';

        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -c "' . $sql . '"', $return);
    }
}
