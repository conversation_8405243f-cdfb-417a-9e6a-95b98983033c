<?php

namespace TF\Commands\sprint_s09;

use PDO;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Commands\ParamHelper;

/**
 * TS-1493 command run on all databases.
 */
class TS1493Command extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('sprint_s09:TS-1493')
            ->setDescription('setva imoti rezultat ot redakciq na geometriq(update pri kvs ot osz) pogre6no smeneni na istoricheski obratno kym neistoricheski')
            ->addArgument(
                'user_databases',
                InputArgument::IS_ARRAY | InputArgument::OPTIONAL,
                'Databases to run the script on (separate multiple names with a space)?'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $user_databases = $input->getArgument('user_databases');

        $paramHelper = new ParamHelper();
        $userDbString = $paramHelper->createQueryParamString($user_databases);

        $userDbConditions = 'true';
        if ($user_databases) {
            $userDbConditions = 'u.database IN (' . $userDbString . ')';
        }

        $mainDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = $mainDev->prepare(
            "SELECT database FROM su_users u
			WHERE {$userDbConditions} AND u.level = 2 
			ORDER BY database"
        );

        $sql->execute();
        $results = $sql->fetchAll();

        for ($i = 0; $i < count($results); $i++) {
            $sql = "SELECT 1 from pg_database WHERE datname='" . $results[$i]['database'] . "';";
            $sqlSelect = $mainDev->prepare($sql);
            $sqlSelect->execute();
            $result = $sqlSelect->fetchAll();

            // id db does not exists
            if (!count($result)) {
                continue;
            }

            $dbName = $results[$i]['database'];
            $dbhDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $dbName . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
            $output->writeln("Database: {$dbName} ");

            $sql = "
				UPDATE layer_kvs as KN SET
		        ( is_edited ) = ( FALSE ) 
				    WHERE KN.kad_ident in (
				--replaced by user
				    select kvs.kad_ident from layer_kvs_edit_log e INNER JOIN layer_kvs kvs on (e.new_gid = kvs.gid)
					where kvs.ekate = '44505'
					and e.old_gid != e.new_gid
					and kvs.is_edited = TRUE
				UNION
				--automatically replaced
					select kvs.kad_ident from layer_kvs kvs 
					where kvs.ekate='44505' 
					and is_edited = TRUE 
					and kvs.gid not IN (SELECT e.old_gid from layer_kvs_edit_log e)
				 ORDER BY kad_ident
				)";

            $cmd = $dbhDev->prepare($sql);
            $cmd->execute();
            $resultData = $cmd->fetchAll();
        }
    }
}
