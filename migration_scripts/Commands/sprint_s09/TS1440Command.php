<?php

namespace TF\Commands\sprint_s09;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-1440 command run on all databases.
 */
class TS1440Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s09:TS-1440')
            ->setDescription('Syzdavane na materializirani view-ta kum danni ot OSZ.');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-1440.sql', $return);
    }
}
