<?php

namespace TF\Commands\sprint_s09;

use PDO;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * TS-1747 command run on all databases.
 */
class TS1747Command extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('sprint_s09:TS-1747')
            ->setDescription('iztriva dublirashtite se zapisi v su_users_layers(s ednakvi imena na tablicite)');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $table = 'su_users_layers';

        $mainDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        // delete records whith is_exist = false and same names as record with is_exist = true
        $sql = $mainDev->prepare(
            " DELETE from {$table} where table_name in (select table_name from {$table} where layer_type = '9' and is_exist = 't'
			  group by table_name
			  ORDER BY table_name) and is_exist='f'"
        );

        $sql->execute();
        // selects recors with is_exist false and multiple records whith the same table name
        $sql = $mainDev->prepare(
            " select array_agg(id) id_array, table_name, array_agg(is_exist) is_exist,  array_length(array_agg(id), 1) len from {$table} 
		 	where layer_type = '9' and is_exist = 'f'
			group by table_name
			having array_length(array_agg(id), 1) > 1
			ORDER BY len, table_name"
        );

        $sql->execute();
        $results = $sql->fetchAll();

        for ($i = 0; $i < count($results); $i++) {
            $arr = trim($results[$i]['id_array'], '{}');
            $arr = explode(',', $arr);
            $min_element = min($arr);

            for ($j = 0;$j < count($arr);$j++) {
                if ($arr[$j] != $min_element) {
                    $single_record_sql = $mainDev->prepare(
                        " delete from {$table} where id = '{$arr[$j]}'"
                    );

                    $single_record_sql->execute();
                }
            }
        }
    }
}
