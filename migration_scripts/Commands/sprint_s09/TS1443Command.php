<?php

namespace TF\Commands\sprint_s09;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-1443 command run on all databases.
 */
class TS1443Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s09:TS-1443')
            ->setDescription('Промяна на типа на колоните за value във таблиците: su_charged_renta_params и su_charged_renta_natura_params, както и в su_charged_renta се позволява колоната - renta, да бъде NULL');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $sql = 'ALTER TABLE su_charged_renta_params ALTER COLUMN renta TYPE varchar;
		ALTER TABLE su_charged_renta_natura_params ALTER COLUMN amount TYPE varchar;
		ALTER TABLE su_charged_renta ALTER COLUMN renta DROP NOT NULL;';

        $sql = str_replace("\r", ' ', $sql);

        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . $userDb . ' -c "' . $sql . '"', $return);
    }
}
