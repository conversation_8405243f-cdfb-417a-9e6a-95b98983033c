<?php

namespace TF\Commands\sprint_s09;

use PDO;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Commands\ParamHelper;

// define('WIALON_PATH', 'https://gps.nik.bg/wialon/');
/**
 * TS-1470 command run on all databases.
 */
class TS1470Command extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('sprint_s09:TS-1470')
            ->setDescription('Migration script za dobavqne na vremenni koloni old_wialon_id i should_be_removed vav su_diary_configs')
            ->addArgument(
                'user_databases',
                InputArgument::IS_ARRAY | InputArgument::OPTIONAL,
                'Databases to run the script on (separate multiple names with a space)?'
            )
            ->addOption('new_user_db', null, InputOption::VALUE_NONE, 'If set, the task will also  run on the new_user database ')
            ->addOption('create_columns', null, InputOption::VALUE_NONE, 'If set, the new columns will be created and old_wialon_id will be updated(!run only once!)')
            ->addOption('replace_equal', null, InputOption::VALUE_NONE, 'If set, will replace the wialon_id value of records with completely equal names (e.g."TK JD9 8345R" and "TK JD9 8345R")');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $user_databases = $input->getArgument('user_databases');

        $paramHelper = new ParamHelper();
        $userDbString = $paramHelper->createQueryParamString($user_databases);

        $userDbConditions = 'true';
        if ($user_databases) {
            $userDbConditions = 'u.database IN (' . $userDbString . ')';
        }

        // var_export(DBLINK_HOST);
        // var_export(DBLINK_PORT);
        // var_export(DBLINK_PASSWORD);
        $mainDev = new PDO('pgsql:host=' . DBLINK_HOST . ';port=' . DBLINK_PORT . ';dbname=' . DBLINK_DATABASE . ';', DBLINK_USERNAME, DBLINK_PASSWORD);

        $sql = $mainDev->prepare(
            "SELECT database FROM su_users u
		 	WHERE {$userDbConditions} AND u.level = 2 
		 	ORDER BY database"
        );

        $sql->execute();
        $results = $sql->fetchAll();

        if ($input->getOption('new_user_db')) {
            $results[] = ['database' => 'new_users_db'];
        }

        for ($i = 0; $i < count($results); $i++) {
            $sql = "SELECT 1 from pg_database WHERE datname='" . $results[$i]['database'] . "';";
            $sqlSelect = $mainDev->prepare($sql);
            $sqlSelect->execute();
            $result = $sqlSelect->fetchAll();

            // if db does not exist
            if (!count($result)) {
                continue;
            }

            $dbName = $results[$i]['database'];
            $dbhDev = new PDO('pgsql:host=' . DBLINK_HOST . ';port=' . DBLINK_PORT . ';dbname=' . $dbName . ';', DBLINK_USERNAME, DBLINK_PASSWORD);
            $output->writeln("Database: {$dbName} ");

            $track_username = $results[$i]['track_username'];
            $track_password = $results[$i]['track_password'];

            // login
            $url_array = [
                'user' => $track_username,
                'password' => $track_password,
            ];
            $url_string = json_encode($url_array);

            $urlRequest = WIALON_PATH
                    . 'ajax.html?svc=core/login&params='
                    . $url_string;

            $curl = curl_init($urlRequest);
            // var_export($urlRequest);
            curl_setopt($curl, CURLOPT_FAILONERROR, true);
            curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
            $w_results = json_decode(curl_exec($curl));

            $wialon_eid = $w_results->eid;
            if ($wialon_eid) {
                $output->writeln("login successfull with eid {$wialon_eid} ");
            } else {
                var_export($track_username);
                var_export($track_password);
                $output->writeln('LOGIN FAILED!');

                continue;
            }
            // end of login

            // get units
            $url_array = [
                'spec' => [
                    'itemsType' => 'avl_unit',
                    'propName' => 'sys_name',
                    'propValueMask' => '*',
                    'sortType' => 'sys_name',
                ],
                'force' => 1,
                'flags' => 1,
                'from' => 0,
                'to' => 0,
            ];
            $url_string = json_encode($url_array);

            $urlRequest = WIALON_PATH
                    . 'ajax.html?svc=core/search_items&params='
                    . $url_string
                    . '&sid=' . $wialon_eid;

            $curl = curl_init($urlRequest);
            curl_setopt($curl, CURLOPT_FAILONERROR, true);
            curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
            $unit_results = json_decode(curl_exec($curl));

            // create the new columns
            if ($input->getOption('create_columns')) {
                $output->writeln('Creating columns ...');

                $sql = $dbhDev->prepare(
                    'ALTER TABLE su_diary_configs ADD COLUMN old_wialon_id integer;'
                );
                $sql->execute();

                $sql = $dbhDev->prepare(
                    ' ALTER TABLE su_diary_configs ADD COLUMN should_be_removed boolean default false;'
                );
                $sql->execute();

                $sql = $dbhDev->prepare(
                    ' UPDATE su_diary_configs SET old_wialon_id = wialon_id;'
                );
                $sql->execute();
            }

            // replace the wialon_id value of columns with the same names
            if ($input->getOption('replace_equal')) {
                $ids_from_wialon_api = [];

                for ($j = 0; $j < count($unit_results->items); $j++) {
                    $ids_from_wialon_api[] = $unit_results->items[$j]->id;
                }

                $output->writeln('Replacing ids of equal names...');
                $sql = $dbhDev->prepare(
                    ' select * from su_diary_configs where config_type = 4 and wialon_id is not null'
                );

                $sql->execute();
                $select_results = $sql->fetchAll();
                $count = count($select_results);
                // var_export($select_results);
                /*!!!!Ako 2 mashini (ednakwi nomera) sa6testvuvat updatevame wialon_id kolonata na stoitostta, koqto e po-golqma(staro wialonsko id)
                 i setvame za premahvane po-malkata - novoto wialonsko id, za da zapazim na6iq id key, kojto se izpolzwa w drugite tablici*/
                for ($j = 0; $j < $count; $j++) {
                    if (!in_array($select_results[$j]['wialon_id'], $ids_from_wialon_api)) {
                        for ($k = 0; $k < count($unit_results->items); $k++) {
                            $price = levenshtein($unit_results->items[$k]->nm, $machine['number']);
                            if (0 == $price) {
                                $old_record = $select_results[$j];
                                $update_wialonn_id_sql = $dbhDev->prepare(
                                    " UPDATE su_diary_configs SET should_be_removed = true WHERE wialon_id = {$unit_results->items[$k]->id} and id >= {$old_record['id']};"
                                );
                                $update_wialonn_id_sql->execute();

                                $update_wialonn_id_sql = $dbhDev->prepare(
                                    " UPDATE su_diary_configs SET wialon_id = {$unit_results->items[$k]->id} WHERE id = {$old_record['id']};"
                                );
                                $update_wialonn_id_sql->execute();
                            }
                        }
                    }
                }
            }
        }
    }
}
