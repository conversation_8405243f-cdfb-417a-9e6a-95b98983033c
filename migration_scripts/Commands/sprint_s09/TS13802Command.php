<?php

namespace TF\Commands\sprint_s09;

use PDO;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * TS-1380-2 command run on all databases.
 */
class TS13802Command extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('sprint_s09:TS-1380-2')
            ->setDescription('Migration script za drop-vane i suzdavane na tablica su_area_types v susi_main"');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $mainDev = new PDO('pgsql:host=' . DBLINK_HOST . ';port=' . DBLINK_PORT . ';dbname=' . DBLINK_DATABASE . ';', DBLINK_USERNAME, DBLINK_PASSWORD);

        $output->writeln('database=' . DBLINK_DATABASE . '');
        system('psql -h ' . DBLINK_HOST . ' -p ' . DBLINK_PORT . ' -U postgres -d ' . DBLINK_DATABASE . ' -f ' . __DIR__ . '/sql/TS-1380-2.sql', $return);
    }
}
