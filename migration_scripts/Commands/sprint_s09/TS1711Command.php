<?php

namespace TF\Commands\sprint_s09;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-1711 command run on all databases.
 */
class TS1711Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s09:TS-1711')
            ->setDescription('Dobavqne na kolona contract_end_date v su_contract_plots_rel i update na stoinostite i');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-1711.sql', $return);
    }
}
