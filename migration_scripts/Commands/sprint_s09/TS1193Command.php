<?php

namespace TF\Commands\sprint_s09;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-1193 command run on all databases.
 */
class TS1193Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s09:TS-1193')
            ->setDescription('Dobavq kolona is_declaration_subleased v tablicite su_contracts (Pri printirane na dekl. po chl. 70 se vzema stoinostta za popalvane na kolona "Tip na dogovor kod"');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $sql = 'ALTER TABLE su_contracts ADD COLUMN is_declaration_subleased bool DEFAULT false NOT NULL;';
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . $userDb . ' -c "' . $sql . '"', $return);
    }
}
