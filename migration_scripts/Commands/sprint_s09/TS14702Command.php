<?php

namespace TF\Commands\sprint_s09;

use PDO;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Commands\ParamHelper;

// define('WIALON_PATH', 'https://gps.nik.bg/wialon/');
/**
 * TS-14702 command run on all databases.
 */
class TS14702Command extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('sprint_s09:TS-14702')
            ->setDescription('Spravka za mashini sas stari vialonski id-ta, chiito imena ne savpadat s nikoe ot novite.')
            ->addArgument(
                'user_databases',
                InputArgument::IS_ARRAY | InputArgument::OPTIONAL,
                'Databases to run the script on (separate multiple names with a space)?'
            )
            ->addOption('new_user_db', null, InputOption::VALUE_NONE, 'If set, the task will also  run on the new_user database ')
            ->addOption('create_columns', null, InputOption::VALUE_NONE, 'If set, the new columns will be created and old_wialon_id will be updated(!run only once!)')
            ->addOption('replace_equal', null, InputOption::VALUE_NONE, 'If set, will replace the wialon_id value of records with completely equal names (e.g."TK JD9 8345R" and "TK JD9 8345R")');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $user_databases = $input->getArgument('user_databases');

        $paramHelper = new ParamHelper();
        $userDbString = $paramHelper->createQueryParamString($user_databases);

        $userDbConditions = 'true';
        if ($user_databases) {
            $userDbConditions = 'u.database IN (' . $userDbString . ')';
        }

        // var_export(DBLINK_HOST);
        // var_export(DBLINK_PORT);
        // var_export(DBLINK_PASSWORD);
        $mainDev = new PDO('pgsql:host=' . DBLINK_HOST . ';port=' . DBLINK_PORT . ';dbname=' . DBLINK_DATABASE . ';', DBLINK_USERNAME, DBLINK_PASSWORD);

        $sql = $mainDev->prepare(
            "SELECT DISTINCT on(database) * from su_users u 
			where {$userDbConditions} 
				AND u.level = 2 
				AND u.track_username is not NULL 
				AND u.track_username NOT LIKE ''
				ORDER BY database"
        );

        $sql->execute();
        $results = $sql->fetchAll();

        if ($input->getOption('new_user_db')) {
            $results[] = ['database' => 'new_users_db'];
        }
        $new_old_ids_file = fopen('id_status.csv', 'w');
        for ($i = 0; $i < count($results); $i++) {
            $sql = "SELECT 1 from pg_database WHERE datname='" . $results[$i]['database'] . "';";
            $sqlSelect = $mainDev->prepare($sql);
            $sqlSelect->execute();
            $result = $sqlSelect->fetchAll();

            // if db does not exist
            if (!count($result)) {
                continue;
            }

            $dbName = $results[$i]['database'];
            $dbhDev = new PDO('pgsql:host=' . DBLINK_HOST . ';port=' . DBLINK_PORT . ';dbname=' . $dbName . ';', DBLINK_USERNAME, DBLINK_PASSWORD);
            $output->writeln("Database: {$dbName} ");

            $track_username = $results[$i]['track_username'];
            $track_password = $results[$i]['track_password'];

            // login
            $url_array = [
                'user' => $track_username,
                'password' => $track_password,
            ];
            $url_string = json_encode($url_array);

            $urlRequest = WIALON_PATH
                    . 'ajax.html?svc=core/login&params='
                    . $url_string;

            $curl = curl_init($urlRequest);
            // var_export($urlRequest);
            curl_setopt($curl, CURLOPT_FAILONERROR, true);
            curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
            $w_results = json_decode(curl_exec($curl));

            $wialon_eid = $w_results->eid;
            if ($wialon_eid) {
                $output->writeln("login successfull with eid {$wialon_eid} ");
            } else {
                var_export($track_username);
                var_export($track_password);
                $output->writeln('LOGIN FAILED!');

                continue;
            }
            // end of login

            // get units
            $url_array = [
                'spec' => [
                    'itemsType' => 'avl_unit',
                    'propName' => 'sys_name',
                    'propValueMask' => '*',
                    'sortType' => 'sys_name',
                ],
                'force' => 1,
                'flags' => 1,
                'from' => 0,
                'to' => 0,
            ];
            $url_string = json_encode($url_array);

            $urlRequest = WIALON_PATH
                    . 'ajax.html?svc=core/search_items&params='
                    . $url_string
                    . '&sid=' . $wialon_eid;

            $curl = curl_init($urlRequest);
            curl_setopt($curl, CURLOPT_FAILONERROR, true);
            curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
            $unit_results = json_decode(curl_exec($curl));

            $sql = $dbhDev->prepare(
                // " select * from su_diary_configs where config_type = 4 and wialon_id is not null"
                'SELECT
					su_diary_configs.*, (CASE when su_diary_events.id is null then null else true end) as has_events
				FROM
					su_diary_configs
				LEFT JOIN su_diary_events on (su_diary_configs.id = su_diary_events.machine_id)
				WHERE
					config_type = 4
				AND wialon_id IS NOT NULL'
            );

            $sql->execute();
            $machine_db_results = $sql->fetchAll();
            $evaluation_array = [];

            $ids_from_wialon_api = [];

            for ($j = 0; $j < count($unit_results->items); $j++) {
                $ids_from_wialon_api[] = $unit_results->items[$j]->id;
            }
            foreach ($machine_db_results as $key => $machine) {
                if (!in_array($machine['wialon_id'], $ids_from_wialon_api)) {
                    $min_price = 10;
                    for ($j = 0; $j < count($unit_results->items); $j++) {
                        $price = levenshtein($unit_results->items[$j]->nm, $machine['number']);
                        if ($price < $min_price) {
                            $min_price = $price;
                        }
                    }
                    if ($min_price > 0) {
                        fputcsv($new_old_ids_file, [
                            $dbName,
                            $machine['wialon_id'],
                            $machine['number'],
                            $machine['id'],
                            'old',
                            $machine['has_events']]);
                    }
                }
            }
        }
        fclose($new_old_ids_file);
    }
}
