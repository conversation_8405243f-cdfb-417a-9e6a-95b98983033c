ALTER TABLE "public"."su_contracts_plots_rel"
	ADD COLUMN "contract_end_date" timestamp(6);

UPDATE su_contracts_plots_rel cp
SET contract_end_date = (
	SELECT
		CASE
	WHEN cp.annex_action = 'added' THEN
		C .due_date
	ELSE
		(
			SELECT
				(
					SELECT
						CASE
					WHEN (
						SELECT
							COUNT (*)
						FROM
							su_sales_contracts_plots_rel scpr
						WHERE
							contract_id = C .parent_id
					) > 0 THEN
						(
							SELECT
								sc.start_date
							FROM
								su_sales_contracts_plots_rel scpr1
							LEFT JOIN su_sales_contracts sc ON (
								sc. ID = scpr1.sales_contract_id
							)
							WHERE
								scpr1.contract_id = C .parent_id
							LIMIT 1
						)
					ELSE
						due_date
					END
				)
			FROM
				su_contracts
			WHERE
				ID = (
					SELECT
						CASE
					WHEN (
						SELECT
							COUNT (*)
						FROM
							su_sales_contracts_plots_rel
						WHERE
							contract_id = C .parent_id
					) > 0 THEN
						C .parent_id
					ELSE
						C . ID
					END
				)
		)
	END
)
FROM
	su_contracts C
WHERE
	C . ID = cp.contract_id