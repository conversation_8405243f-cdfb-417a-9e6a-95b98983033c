<?php

namespace TF\Commands\sprint_s09;

use PDO;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * TS-1380 command run on all databases.
 */
class TS1380Command extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('sprint_s09:TS-1380')
            ->setDescription('Migration script za dobavqne na kolona label_name v su_users_layers"');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $mainDev = new PDO('pgsql:host=' . DBLINK_HOST . ';port=' . DBLINK_PORT . ';dbname=' . DBLINK_DATABASE . ';', DBLINK_USERNAME, DBLINK_PASSWORD);

        $sql = $mainDev->prepare(
            'ALTER TABLE su_users_layers ADD COLUMN label_name varchar'
        );

        $sql->execute();
        $results = $sql->fetchAll();
    }
}
