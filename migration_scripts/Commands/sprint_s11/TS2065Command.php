<?php

namespace TF\Commands\sprint_s11;

use PDO;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Commands\ParamHelper;

/**
 * TS-2065 command run on all databases.
 */
class TS2065Command extends BaseCommand
{
    protected function configure()
    {
        $this->setName('sprint_s11:TS-2065')
            ->setDescription('setvane na prava za ipoteki i dogovori za sobstvenost na akaunti s prava za imoti ot 2015 i 2016')
            ->addArgument(
                'user_databases',
                InputArgument::IS_ARRAY | InputArgument::OPTIONAL,
                'Databases to run the script on (separate multiple names with a space)?'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $user_databases = $input->getArgument('user_databases');

        $paramHelper = new ParamHelper();
        $userDbString = $paramHelper->createQueryParamString($user_databases);

        $userDbConditions = 'true';
        if ($user_databases) {
            $userDbConditions = 'u.database IN (' . $userDbString . ')';
        }

        $mainDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = $mainDev->prepare(
            "SELECT array_agg(id) as ids FROM su_users u
            WHERE {$userDbConditions} AND u.level = 2 
            AND (paid_support = '2016-01-01' OR paid_support = '2015-01-01')"
        );

        $sql->execute();
        $results = $sql->fetchAll();
        $user_ids = str_replace(['{', '}'], ['', ''], $results[0]['ids']);

        $sql = $mainDev->prepare(
            "SELECT user_id, array_agg(right_id) as rights FROM su_users_rights
            WHERE user_id IN ({$user_ids})
            GROUP BY user_id"
        );

        $sql->execute();
        $userRights = $sql->fetchAll();

        for ($i = 0; $i < count($userRights); $i++) {
            $user_id = $userRights[$i]['user_id'];
            $rights = explode(',', str_replace(['{', '}'], ['', ''], $userRights[$i]['rights']));
            // check for plots read rights
            if (in_array(2, $rights)) {
                $rightsSql = '';

                if (!in_array(SALES_CONTRACTS_RIGHTS_R, $rights)) {
                    $rightsSql .= "({$user_id}, " . SALES_CONTRACTS_RIGHTS_R . ')';
                }
                if (!in_array(SALES_CONTRACTS_RIGHTS_RW, $rights)) {
                    if ('' != $rightsSql) {
                        $rightsSql .= ', ';
                    }
                    $rightsSql .= "({$user_id}, " . SALES_CONTRACTS_RIGHTS_RW . ')';
                }
                if (!in_array(HYPOTHECS_RIGHTS_R, $rights)) {
                    if ('' != $rightsSql) {
                        $rightsSql .= ', ';
                    }
                    $rightsSql .= "({$user_id}, " . HYPOTHECS_RIGHTS_R . ')';
                }
                if (!in_array(HYPOTHECS_RIGHTS_RW, $rights)) {
                    if ('' != $rightsSql) {
                        $rightsSql .= ', ';
                    }
                    $rightsSql .= "({$user_id}, " . HYPOTHECS_RIGHTS_RW . ')';
                }
                if (!in_array(CONTRACTS_OWN_WRITE_RIGHTS, $rights)) {
                    if ('' != $rightsSql) {
                        $rightsSql .= ', ';
                    }
                    $rightsSql .= "({$user_id}, " . CONTRACTS_OWN_WRITE_RIGHTS . ')';
                }

                if ('' == $rightsSql) {
                    continue;
                }
                $rightsSql .= ';';

                $sql = $mainDev->prepare(
                    "INSERT INTO su_users_rights (user_id, right_id) VALUES {$rightsSql}"
                );

                $sql->execute();
                $output->writeln("user id: {$user_id} ");
            }
        }
    }
}
