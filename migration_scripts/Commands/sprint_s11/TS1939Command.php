<?php

namespace TF\Commands\sprint_s11;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-1939 command run on all databases.
 */
class TS1939Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s11:TS-1939')
            ->setDescription('Dobavqne na kolona rko_number v su_payments');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -f ' . __DIR__ . '/sql/TS-1939.sql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . $userDb, $return);
    }
}
