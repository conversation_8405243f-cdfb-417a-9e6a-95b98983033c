<?php

namespace TF\Commands\sprint_s11;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-2034 command run on all databases.
 */
class TS2034Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s11:TS-2034')
            ->setDescription('Dobavqne na kolona contract_price v su_contracts');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql  -f ' . __DIR__ . '/sql/TS-2034.sql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . $userDb, $return);
    }
}
