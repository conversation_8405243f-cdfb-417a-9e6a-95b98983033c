<?php

namespace TF\Commands\sprint_s11;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-1916 command run on all databases.
 */
class TS1916Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s11:TS-1916')
            ->setDescription('Dobavqne na kolona plot_id v su_owners_documents');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-1916.sql', $return);
    }
}
