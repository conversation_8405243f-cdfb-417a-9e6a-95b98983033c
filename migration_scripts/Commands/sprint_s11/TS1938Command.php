<?php

namespace TF\Commands\sprint_s11;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-1938 command run on all databases.
 */
class TS1938Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s11:TS-1938')
            ->setDescription('Dobavqne na tablica su_payment_subjects');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -f "' . __DIR__ . '/sql/TS-1938.sql" -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . $userDb, $return);
    }
}
