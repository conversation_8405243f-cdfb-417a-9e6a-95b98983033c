CREATE TABLE su_payment_subjects (
    "id" int4 NOT NULL,
    "name" varchar(64) NOT NULL,
    "fulltext" varchar(256) NOT NULL
);

ALTER TABLE public.su_payment_subjects OWNER TO postgres;

--
-- Name: su_payment_subjects_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE su_payment_subjects_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.su_payment_subjects_id_seq OWNER TO postgres;

--
-- Name: su_payment_subjects_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE su_payment_subjects_id_seq OWNED BY su_payment_subjects.id;


--
-- Name: id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY su_payment_subjects ALTER COLUMN id SET DEFAULT nextval('su_payment_subjects_id_seq'::regclass);

INSERT INTO su_payment_subjects (name, fulltext) 
	VALUES 
('Рент. плащане по дог.', 'Рентно плащане по договор [[nomer_na_dogovor]] за [[stopanska_godina]] стопанска година'),
('Аванс. плащане за стоп. год.', 'Авансово рентно плащане по договор [[nomer_na_dogovor]] за [[stopanska_godina]] стопанска година'),
('Оконч. плащане за стоп. год.', 'Окончателно рентно плащане по договор [[nomer_na_dogovor]] за [[stopanska_godina]] стопанска година');