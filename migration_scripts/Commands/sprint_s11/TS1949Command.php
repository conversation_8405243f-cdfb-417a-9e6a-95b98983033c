<?php

namespace TF\Commands\sprint_s11;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-1949 command run on all databases.
 */
class TS1949Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s11:TS-1949')
            ->setDescription('Dobavqne na koloni fraction i percent v su_contracts_plots_rel');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -f ' . __DIR__ . '/sql/TS-1949.sql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . $userDb, $return);
    }
}
