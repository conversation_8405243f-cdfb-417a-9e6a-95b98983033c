<?php

namespace TF\Commands\sprint_s10;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-1785 command run on all databases.
 */
class TS1785Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s10:TS-1785')
            ->setDescription('Dobavqne na kolona substance_unit_price v su_diary_events');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-1785.sql', $return);
    }
}
