DROP MATERIALIZED VIEW "public"."topic_layer_kvs_by_owner_name_label_items";

CREATE MATERIALIZED VIEW "topic_layer_kvs_by_owner_name_label_items"
AS 
 SELECT DISTINCT kvs.gid,
    st_centroid(kvs.geom) AS geom,
        CASE
            WHEN ((osz.ime_subekt)::text <> ''::text) THEN (((osz.ime_subekt)::text || ','::text) || (kvs.kad_ident)::text)
            ELSE max((kvs.kad_ident)::text)
        END AS ime_subekt,
        CASE
            WHEN ((osz.ime_subekt)::text <> ''::text) THEN string_agg(DISTINCT (osz.egn_subekt)::text, ','::text)
            ELSE NULL::text
        END AS egn_subekt,
    kvs.ekate,
    max(osz.file_id) AS file_id
   FROM (su_osz_files_plots osz
     LEFT JOIN layer_kvs kvs ON (((osz.kad_no)::text = (kvs.kad_ident)::text)))
  WHERE (((osz.kod_pr_osn)::text = ANY (ARRAY[('1'::character varying)::text, ('6'::character varying)::text])) AND (osz.pl_dka > (0)::numeric))
  GROUP BY kvs.gid, osz.ime_subekt;