DROP MATERIALIZED VIEW IF EXISTS total_plot_area_by_contract_mat_view;
CREATE MATERIALIZED VIEW total_plot_area_by_contract_mat_view as 
SELECT kvs.gid, kvs.masiv,
        CASE
            WHEN (( SELECT count(DISTINCT ipc.plot_id) AS count
               FROM su_contracts_plots_rel ipc
              WHERE (ipc.plot_id = kvs.gid)) = 0) THEN false
            ELSE true
        END AS has_contract,
    sum(st_area(kvs.geom)) AS area,
    sum(
        CASE
            WHEN (kvs.document_area IS NULL) THEN (st_area(kvs.geom) / (1000)::double precision)
            ELSE (kvs.document_area)::double precision
        END) AS document_area,
    kvs.ekate
   FROM layer_kvs kvs
  GROUP BY
        CASE
            WHEN (( SELECT count(DISTINCT ipc.plot_id) AS count
               FROM su_contracts_plots_rel ipc
              WHERE (ipc.plot_id = kvs.gid)) = 0) THEN false
            ELSE true
        END, kvs.ekate, kvs.gid
  ORDER BY sum(st_area(kvs.geom));