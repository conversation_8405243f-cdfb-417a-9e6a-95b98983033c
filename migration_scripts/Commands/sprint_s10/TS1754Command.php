<?php

namespace TF\Commands\sprint_s10;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-1754 command run on all databases.
 */
class TS1754Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s10:TS-1754')
            ->setDescription('Dobavqne na kolona egn_subekt v topic_layer_kvs_by_owner_name_label_items ');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-1754.sql', $return);
    }
}
