<?php

namespace TF\Commands\sprint_s10;

use S<PERSON>fony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\ArrayInput;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Output\StreamOutput;
use TF\Commands\ParamHelper;

/**
 * Runs all scripts required for migration from Sprint 9 to Sprint 10.
 */
class UpdateToSprint10 extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('sprint_s10:update-to-sprint10')
            ->setDescription('Runs all scripts required for migration from Sprint 9 to Sprint 10.')
            ->addArgument(
                'user_databases',
                InputArgument::IS_ARRAY | InputArgument::OPTIONAL,
                'Databases to run the script on (separate multiple names with a space)?'
            )
            ->addOption('new_user_db', null, InputOption::VALUE_NONE, 'If set, the task will also  run on the new_user database ');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $user_databases = $input->getArgument('user_databases');
        $paramHelper = new ParamHelper();
        $userDbString = $paramHelper->createQueryParamString($user_databases, $input->getOption('new_user_db'));

        $commandsArr = [
            // Sprint 8
            new \TF\Commands\sprint_s8\TS1460Command(),
            new \TF\Commands\sprint_s8\TS14601Command(),

            // Sprint 9
            new \TF\Commands\sprint_s9\TS1711Command(),

            // Sprint 10
            new TS1754Command(),
            new TS1778Command(),
            new TS1783Command(),
            new TS1784Command(),
            new TS1785Command(),
            new TS1793Command(),
            new TS1901Command(),
        ];

        $commandOutput = new StreamOutput(fopen('php://memory', 'w', false));
        $commandInput = new ArrayInput([
            'user_databases' => $user_databases,
            '--new_user_db' => true,
        ]);

        for ($i = 0; $i < count($commandsArr); $i++) {
            $command = $commandsArr[$i];
            $hasUserDbsArgs = $command->getDefinition()->hasArgument('user_databases');
            if (!$hasUserDbsArgs) {
                $commandInput = new ArrayInput([]);
            }
            $output->writeln('Executing command: ' . $command->getName());
            $command->run($commandInput, $commandOutput);
        }
    }
}
