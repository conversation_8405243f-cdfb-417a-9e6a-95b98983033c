<?php

namespace TF\Commands\sprint_s10;

use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * Runs all scripts required for migration from Sprint 9 to Sprint 10.
 */
class TS1793Command extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('sprint_s10:TS-1793')
            ->setDescription('Dobavq kolona `su_users.paid_support`.');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . DEFAULT_DB_DATABASE . ' -f "' . __DIR__ . '/sql/TS-1793.sql"', $return);
    }
}
