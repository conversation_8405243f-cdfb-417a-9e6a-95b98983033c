<?php

namespace TF\Commands\sprint_s10;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-1785 Dobavq kolona `su_cooperators.heritor_only`.
 */
class TS1901Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s10:TS-1901')
            ->setDescription('Dobavq kolona `su_cooperators.heritor_only`.');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-1901.sql', $return);
    }
}
