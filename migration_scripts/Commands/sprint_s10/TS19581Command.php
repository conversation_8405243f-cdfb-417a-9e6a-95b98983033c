<?php

namespace TF\Commands\sprint_s10;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-19581 da slaga is_exist = false na prazni sloeve.
 */
class TS19581Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s10:TS-19581')
            ->setDescription('da slaga is_exist = true na sashtestvuvashti sloeve.');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $this->openConnection();

        $sql = $this->mainConnection->prepare(
            "SELECT table_name, is_exist, layer_type FROM su_users_layers ul
            LEFT JOIN su_users u on u.id = ul.user_id
            WHERE u.database='{$userDb}'"
        );

        $sql->execute();
        $layer_results = $sql->fetchAll();

        $userDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        for ($j = 0; $j < count($layer_results); $j++) {
            // const LAYER_TYPE_ZP = 1;
            // const LAYER_TYPE_GPS = 2;
            // const LAYER_TYPE_DSS = 3;
            // const LAYER_TYPE_KMS = 4;
            // const LAYER_TYPE_KVS = 5;
            // const LAYER_TYPE_ISAK = 6;
            // const LAYER_TYPE_SATELLITE_WORK = 8;
            // const LAYER_TYPE_FOR_ISAK = 9;
            // const LAYER_TYPE_LFA = 10;
            // const LAYER_TYPE_NATURA_2000 = 11;
            // const LAYER_TYPE_PERMANETELY_GREEN_AREAS = 12;
            // const LAYER_TYPE_VPS_PASISHTA = 13;
            // const LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI = 14;
            // const LAYER_TYPE_VPS_GASKI_ZIMNI = 15;
            // const LAYER_TYPE_VPS_LIVADEN_BLATAR = 16;
            // const LAYER_TYPE_VPS_ORLI_LESHOYADI  = 17;
            // const LAYER_TYPE_KVS_OSZ = 18;

            if (2 != $layer_results[$j]['layer_type']
                && 5 != $layer_results[$j]['layer_type']
                && 8 != $layer_results[$j]['layer_type']
                && 10 != $layer_results[$j]['layer_type']
                && 11 != $layer_results[$j]['layer_type']
                && 12 != $layer_results[$j]['layer_type']
                && 13 != $layer_results[$j]['layer_type']
                && 14 != $layer_results[$j]['layer_type']
                && 15 != $layer_results[$j]['layer_type']
                && 16 != $layer_results[$j]['layer_type']
                && 17 != $layer_results[$j]['layer_type']
                && 18 != $layer_results[$j]['layer_type']) {
                $layerSql = $userDev->prepare(
                    "SELECT CASE 
                     WHEN EXISTS (SELECT * FROM \"{$layer_results[$j]['table_name']}\" LIMIT 1) THEN 1
                     ELSE 0 
                 END"
                );

                $layerSql->execute();
                $table_exists = $layerSql->fetchAll();

                if (1 == $table_exists[0]['case'] && false == $layer_results[$j]['is_exist']) {
                    $sql = $this->mainConnection->prepare(
                        "UPDATE su_users_layers SET is_exist = true
                         WHERE  table_name = '" . $layer_results[$j]['table_name'] . "'"
                    );

                    $sql->execute();
                    $results = $sql->fetchAll();
                }
            }
        }
    }
}
