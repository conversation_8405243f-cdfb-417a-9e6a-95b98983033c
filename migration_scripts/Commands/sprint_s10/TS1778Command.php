<?php

namespace TF\Commands\sprint_s10;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-1778 command run on all databases.
 */
class TS1778Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s10:TS-1778')
            ->setDescription('<PERSON><PERSON> za views za renti da NE se puska pri insert. ');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-1778.sql', $return);
    }
}
