<?php

namespace TF\Commands\sprint_s10;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-1784 command run on all databases.
 */
class TS1784Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s10:TS-1784')
            ->setDescription('Dobavqne na kolona price_per_acre v su_diary_events');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-1784.sql', $return);
    }
}
