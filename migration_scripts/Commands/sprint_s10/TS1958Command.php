<?php

namespace TF\Commands\sprint_s10;

use PDO;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Commands\ParamHelper;

/**
 * TS-1958 da slaga is_exist = false na prazni sloeve.
 */
class TS1958Command extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('sprint_s10:TS-1958')
            ->setDescription('da slaga is_exist = false na prazni sloeve.')
            ->addArgument(
                'user_databases',
                InputArgument::IS_ARRAY | InputArgument::OPTIONAL,
                'Databases to run the script on (separate multiple names with a space)?'
            )
            ->addOption('new_user_db', null, InputOption::VALUE_NONE, 'If set, the task will also  run on the new_user database ');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $user_databases = $input->getArgument('user_databases');

        $paramHelper = new ParamHelper();
        $userDbString = $paramHelper->createQueryParamString($user_databases);

        $userDbConditions = 'true';
        if ($user_databases) {
            $userDbConditions = 'u.database IN (' . $userDbString . ')';
        }

        // $mainDev = new PDO("pgsql:host=" . DBLINK_HOST . ";port=" . DBLINK_PORT . ";dbname=".DBLINK_DATABASE.";", DBLINK_USERNAME, DBLINK_PASSWORD);
        $mainDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DBLINK_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = $mainDev->prepare(
            "SELECT database, group_id FROM su_users u
            WHERE {$userDbConditions} AND u.level = 2 
            ORDER BY database"
        );

        $sql->execute();
        $results = $sql->fetchAll();

        if ($input->getOption('new_user_db')) {
            $results[] = ['database' => 'new_users_db'];
        }

        for ($i = 0; $i < count($results); $i++) {
            if ('db_bimagro' == $results[$i]['database']
            || 'db_gs_edinstvo' == $results[$i]['database']
            || 'db_nava_agroserviz_test' == $results[$i]['database']
            || 'db_rosa_karchanov' == $results[$i]['database']) {
                continue;
            }

            $sql = $mainDev->prepare(
                "SELECT table_name, is_exist, layer_type FROM su_users_layers ul
                WHERE ul.user_id={$results[$i]['group_id']}"
            );

            $sql->execute();
            $layer_results = $sql->fetchAll();

            $userDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $results[$i]['database'] . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

            for ($j = 0; $j < count($layer_results); $j++) {
                // const LAYER_TYPE_ZP = 1;
                // const LAYER_TYPE_GPS = 2;
                // const LAYER_TYPE_DSS = 3;
                // const LAYER_TYPE_KMS = 4;
                // const LAYER_TYPE_KVS = 5;
                // const LAYER_TYPE_ISAK = 6;
                // const LAYER_TYPE_SATELLITE_WORK = 8;
                // const LAYER_TYPE_FOR_ISAK = 9;
                // const LAYER_TYPE_LFA = 10;
                // const LAYER_TYPE_NATURA_2000 = 11;
                // const LAYER_TYPE_PERMANETELY_GREEN_AREAS = 12;
                // const LAYER_TYPE_VPS_PASISHTA = 13;
                // const LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI = 14;
                // const LAYER_TYPE_VPS_GASKI_ZIMNI = 15;
                // const LAYER_TYPE_VPS_LIVADEN_BLATAR = 16;
                // const LAYER_TYPE_VPS_ORLI_LESHOYADI  = 17;
                // const LAYER_TYPE_KVS_OSZ = 18;

                if (2 != $layer_results[$j]['layer_type']
                    && 5 != $layer_results[$j]['layer_type']
                    && 8 != $layer_results[$j]['layer_type']
                    && 10 != $layer_results[$j]['layer_type']
                    && 11 != $layer_results[$j]['layer_type']
                    && 12 != $layer_results[$j]['layer_type']
                    && 13 != $layer_results[$j]['layer_type']
                    && 14 != $layer_results[$j]['layer_type']
                    && 15 != $layer_results[$j]['layer_type']
                    && 16 != $layer_results[$j]['layer_type']
                    && 17 != $layer_results[$j]['layer_type']
                    && 18 != $layer_results[$j]['layer_type']) {
                    $layerSql = $userDev->prepare(
                        "SELECT CASE 
                         WHEN EXISTS (SELECT * FROM {$layer_results[$j]['table_name']} LIMIT 1) THEN 1
                         ELSE 0 
                     END"
                    );

                    $layerSql->execute();
                    $table_exists = $layerSql->fetchAll();

                    if (1 != $table_exists[0]['case'] && true == $layer_results[$j]['is_exist']) {
                        $filename = 'ts_1958_log.txt';
                        $fd = fopen($filename, 'a');
                        fwrite($fd, $results[$i]['database'] . '  ' . $layer_results[$j]['table_name'] . "\n");
                        fclose($fd);
                        $output->writeln($results[$i]['database'] . '' . $layer_results[$j]['table_name']);

                        $sql = $mainDev->prepare(
                            "UPDATE su_users_layers SET is_exist = false
                             WHERE  table_name = '" . $layer_results[$j]['table_name'] . "'"
                        );

                        $sql->execute();
                        $results = $sql->fetchAll();
                    }
                }
            }
        }
    }
}
