-- Последователност на дропване в нужда !!!
--DROP TABLE IF EXISTS su_cooperators_files;
--DROP TABLE IF EXISTS su_cooperator_heritors;
--DROP TABLE IF EXISTS su_cooperators_capital;
--DROP TABLE IF EXISTS su_devidends_payment;
--DROP TABLE IF EXISTS su_cooperators_annual_dividends;
--DROP TABLE IF EXISTS su_devidends_annual_report;
--DROP TABLE IF EXISTS su_cooperators;

--
-- PostgreSQL database dump
--

SET statement_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SET check_function_bodies = false;
SET client_min_messages = warning;

SET search_path = public, pg_catalog;

SET default_tablespace = '';

SET default_with_oids = false;

--
-- Name: su_cooperators; Type: TABLE; Schema: public; Owner: postgres; Tablespace: 
-- su_cooperators

CREATE TABLE su_cooperators (
    id int4,
    name varcha<PERSON>(255),
    surname varchar(255),
    lastname varchar(255),
    egn varchar(255),
    lk_nomer varchar(255),
    lk_izdavane varchar(63),
    book_number varchar(27),
    partida_number varchar(27),
    date_entry timestamp,
    paid_in_capital float4,
    current_capital float4,
    excluded bool DEFAULT false NOT NULL,
    excluded_reason varchar(1023),
    date_excluded timestamp,
    is_dead bool DEFAULT false NOT NULL,
    date_created timestamp,
    date_dead timestamp
);

ALTER TABLE su_cooperators OWNER TO postgres;

--
-- Name: su_cooperators_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE su_cooperators_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE su_cooperators_id_seq OWNER TO postgres;

--
-- Name: su_cooperators_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE su_cooperators_id_seq OWNED BY su_cooperators.id;


--
-- Name: id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY su_cooperators ALTER COLUMN id SET DEFAULT nextval('su_cooperators_id_seq'::regclass);


--
-- Name: su_subleases_plots_area_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres; Tablespace: 
--

ALTER TABLE ONLY su_cooperators
    ADD CONSTRAINT su_cooperators_id_seq_pkey PRIMARY KEY (id);


-- su_cooperators_files    

CREATE TABLE su_cooperators_files (
id serial,
filename varchar(255),
date timestamp(6) DEFAULT now(),
user_id int4,
group_id int4,
cooperator_id int4
);

ALTER TABLE su_cooperators_files OWNER TO postgres;

ALTER TABLE su_cooperators_files_id_seq OWNER TO postgres;

ALTER SEQUENCE su_cooperators_files_id_seq OWNED BY su_cooperators_files.id;

-- ----------------------------
-- Foreign Key structure for table su_cooperators_documents
-- ----------------------------
ALTER TABLE su_cooperators_files ADD FOREIGN KEY (cooperator_id) REFERENCES su_cooperators (id) ON DELETE CASCADE ON UPDATE NO ACTION;    


-- ----------------------------
-- Table structure for su_cooperator_heritors
-- ----------------------------

CREATE TABLE su_cooperator_heritors (
id serial,
cooperator_id int4 NOT NULL,
path ltree NULL,
heritor_current_capital float8
);

ALTER TABLE su_cooperator_heritors OWNER TO postgres;

ALTER TABLE su_cooperator_heritors_id_seq OWNER TO postgres;

ALTER SEQUENCE su_cooperator_heritors_id_seq OWNED BY su_cooperator_heritors.id;

ALTER TABLE su_cooperator_heritors ADD FOREIGN KEY (cooperator_id) REFERENCES su_cooperators (id) ON DELETE CASCADE ON UPDATE NO ACTION;

-- ----------------------------
-- Table structure for su_cooperators_capital
-- ----------------------------

CREATE TABLE su_cooperators_capital (
id serial,
cooperator_id int4 NOT NULL,
owe_capital float4,
cashout float4,
generate_order bool DEFAULT false NOT NULL,
generate_payment bool DEFAULT false NOT NULL,
representative varchar(255),
iban varchar(63),
pay_date timestamp,
date_created timestamp
);

ALTER TABLE su_cooperators_capital OWNER TO postgres;

ALTER TABLE su_cooperators_capital_id_seq OWNER TO postgres;

ALTER SEQUENCE su_cooperators_capital_id_seq OWNED BY su_cooperators_capital.id;

ALTER TABLE su_cooperators_capital ADD FOREIGN KEY (cooperator_id) REFERENCES su_cooperators (id) ON DELETE CASCADE ON UPDATE NO ACTION;

-- ----------------------------
-- Table structure for su_devidends_annual_report
-- ---------------------------

CREATE TABLE su_devidends_annual_report (
id serial,
win_amount float4,
tax_amount float4,
distribution_amount float4,
increase_capital_amount float4,
start_date timestamp,
end_date timestamp,
active bool DEFAULT true NOT NULL,
date_created timestamp
);

ALTER TABLE su_devidends_annual_report OWNER TO postgres;

ALTER TABLE su_devidends_annual_report_id_seq OWNER TO postgres;

ALTER SEQUENCE su_devidends_annual_report_id_seq OWNED BY su_devidends_annual_report.id;

ALTER TABLE ONLY su_devidends_annual_report ADD CONSTRAINT su_devidends_annual_report_id_seq_pkey PRIMARY KEY (id);

-- ----------------------------
-- Table structure for su_devidends_payment
-- ----------------------------

CREATE TABLE su_devidends_payment (
id serial,
cooperator_id int4 NOT NULL,
annual_report_id int4 NOT NULL,
current_capital float4,
cashout float4,
generate_order bool DEFAULT false NOT NULL,
generate_payment bool DEFAULT false NOT NULL,
representative varchar(255),
iban varchar(63),
pay_date timestamp,
date_created timestamp,
path ltree NULL,
is_heritor bool DEFAULT false NOT NULL
);

ALTER TABLE su_devidends_payment OWNER TO postgres;

ALTER TABLE su_devidends_payment_id_seq OWNER TO postgres;

ALTER SEQUENCE su_devidends_payment_id_seq OWNED BY su_devidends_payment.id;

ALTER TABLE su_devidends_payment ADD FOREIGN KEY (cooperator_id) REFERENCES su_cooperators (id) ON DELETE CASCADE ON UPDATE NO ACTION;

ALTER TABLE su_devidends_payment ADD FOREIGN KEY (annual_report_id) REFERENCES su_devidends_annual_report (id) ON DELETE CASCADE ON UPDATE NO ACTION;

-- ----------------------------
-- Table structure for su_cooperators_annual_dividends
-- ----------------------------

CREATE TABLE su_cooperators_annual_dividends (
id serial,
cooperator_id int4 NOT NULL,
annual_report_id int4 NOT NULL,
dividend float8,
tax float8,
increase_capital float8,
current_capital float8
);

ALTER TABLE su_cooperators_annual_dividends OWNER TO postgres;

ALTER TABLE su_cooperators_annual_dividends_id_seq OWNER TO postgres;

ALTER SEQUENCE su_cooperators_annual_dividends_id_seq OWNED BY su_cooperators_annual_dividends.id;

ALTER TABLE su_cooperators_annual_dividends ADD FOREIGN KEY (cooperator_id) REFERENCES su_cooperators (id) ON DELETE CASCADE ON UPDATE NO ACTION;
ALTER TABLE su_cooperators_annual_dividends ADD FOREIGN KEY (annual_report_id) REFERENCES su_devidends_annual_report (id) ON DELETE CASCADE ON UPDATE NO ACTION;