/*
Navicat PGSQL Data Transfer

Source Server         : techno
Source Server Version : 90112
Source Host           : localhost:5432
Source Database       : db_test_hristo
Source Schema         : public

Target Server Type    : PGSQL
Target Server Version : 90112
File Encoding         : 65001

Date: 2015-07-07 18:50:33
*/


-- ----------------------------
-- Table structure for su_charged_renta_history
-- ----------------------------
CREATE TABLE "public"."su_charged_renta_history" (
"id" serial8 NOT NULL,
"params_id" int8 NOT NULL,
"ekate" int8,
"kad_ident" varchar(255) COLLATE "default",
"owner_id" int8,
"category" varchar(255) COLLATE "default",
"owner_area" varchar(255) COLLATE "default",
"charged_renta" varchar(255) COLLATE "default",
"charged_renta_nat" text COLLATE "default",
"contract_id" int4
)
WITH (OIDS=FALSE)

;

-- ----------------------------
-- Table structure for su_charged_renta_natura_params
-- ----------------------------
CREATE TABLE "public"."su_charged_renta_natura_params" (
"id" serial4 NOT NULL,
"params_id" int8 NOT NULL,
"amount" int8,
"nat_type" int8,
"is_converted" bool,
"price_per_unit" int8
)
WITH (OIDS=FALSE)

;

CREATE TYPE "public"."su_charged_renta_params_natura_enum" AS ENUM ('all', 'with_nat', 'without_nat');

-- ----------------------------
-- Table structure for su_charged_renta_params
-- ----------------------------
CREATE TABLE "public"."su_charged_renta_params" (
"id" serial4 NOT NULL,
"type" varchar(20) COLLATE "default" NOT NULL,
"date" date NOT NULL,
"farming_year" int2,
"owner_id" varchar(255) COLLATE "default",
"owner_egn" varchar(255) COLLATE "default",
"rep_id" varchar(255) COLLATE "default",
"rep_egn" varchar(255) COLLATE "default",
"company_id" varchar(255) COLLATE "default",
"company_eik" varchar(255) COLLATE "default",
"c_num" varchar(255) COLLATE "default",
"c_type" int4,
"farming_id" int4,
"ekate" int4,
"masiv" int4,
"number" int4,
"natura_type" int4,
"category" varchar(255) COLLATE "default",
"area_type" int4,
"renta" float8,
"natura" "public"."su_charged_renta_params_natura_enum"
)
WITH (OIDS=FALSE)

;
ALTER TYPE "public"."su_charged_renta_params_natura_enum" OWNER TO "postgres";
-- ----------------------------
-- Alter Sequences Owned By 
-- ----------------------------

-- ----------------------------
-- Primary Key structure for table su_charged_renta_natura_params
-- ----------------------------
ALTER TABLE "public"."su_charged_renta_natura_params" ADD PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table su_charged_renta_params
-- ----------------------------
ALTER TABLE "public"."su_charged_renta_params" ADD PRIMARY KEY ("id");

-- ----------------------------
-- Foreign Key structure for table "public"."su_charged_renta_natura_params"
-- ----------------------------
ALTER TABLE "public"."su_charged_renta_natura_params" ADD FOREIGN KEY ("params_id") REFERENCES "public"."su_charged_renta_params" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;