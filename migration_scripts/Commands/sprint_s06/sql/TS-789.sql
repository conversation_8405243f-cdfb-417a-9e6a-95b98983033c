ALTER TABLE su_plots_owners_rel ALTER COLUMN percent SET DATA TYPE double precision;
ALTER TABLE su_plots_farming_rel ALTER COLUMN percent SET DATA TYPE double precision;

UPDATE su_plots_owners_rel SET percent = CASE WHEN numerator IS NULL OR denominator IS NULL OR denominator = 0 THEN percent ELSE (numerator::double precision / denominator::double precision * 100)::double precision END;
UPDATE su_plots_farming_rel SET percent = CASE WHEN numerator IS NULL OR denominator IS NULL OR denominator = 0 THEN percent ELSE (numerator::double precision / denominator::double precision * 100)::double precision END;