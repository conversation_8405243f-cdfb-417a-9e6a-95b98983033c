<?php

namespace TF\Commands\sprint_s06;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-739 command run on all databases.
 */
class TS739Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s06:TS-739')
            ->setDescription('Dobavq tablicata "su_diary_expenses", koqto se izpolzva ot agrotehnikata za spravka za razhodi.');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -f ' . __DIR__ . '/sql/TS-739.sql ' . $userDb, $return);
    }
}
