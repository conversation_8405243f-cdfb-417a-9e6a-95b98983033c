<?php

namespace TF\Commands\sprint_s06;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-740 command run on all databases.
 */
class TS740Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s06:TS-740')
            ->setDescription('Dobavq kolonite "notary_name", "notary_number" i "notary_address" kam tablici "su_plots_owners_rel" i "su_contracts_contragents".');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $sql = 'ALTER TABLE su_contracts_contragents ADD COLUMN notary_name varchar;
	    ALTER TABLE su_contracts_contragents ADD COLUMN notary_number int4;
	    ALTER TABLE su_contracts_contragents ADD COLUMN notary_address varchar;
	    ALTER TABLE su_plots_owners_rel ADD COLUMN notary_name varchar;
	    ALTER TABLE su_plots_owners_rel ADD COLUMN notary_number int4;
	    ALTER TABLE su_plots_owners_rel ADD COLUMN notary_address varchar;
	    ALTER TABLE su_contracts_farming_contragents ADD COLUMN notary_name varchar;
	    ALTER TABLE su_contracts_farming_contragents ADD COLUMN notary_number int4;
	    ALTER TABLE su_contracts_farming_contragents ADD COLUMN notary_address varchar';

        $sql = explode(';', $sql);

        for ($j = 0; $j < count($sql); $j++) {
            system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -c "' . $sql[$j] . '"', $return);
        }
    }
}
