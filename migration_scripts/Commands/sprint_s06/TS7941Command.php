<?php

namespace TF\Commands\sprint_s06;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-794-1 command run on all databases.
 */
class TS7941Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s06:TS-794-1')
            ->setDescription('Sazdavane na tablicata su_cooperators_files i dropvaneto na tablica cooperators_documents.');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        // CREATE TABLE su_cooperators_files
        $sql = 'CREATE TABLE su_cooperators_files (
				id serial,
				filename varchar(255),
				date timestamp(6) DEFAULT now(),
				user_id int4,
				group_id int4,
				cooperator_id int4
				);';
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -c "' . $sql . '"', $return);

        $sql = 'ALTER TABLE su_cooperators_files OWNER TO postgres;';
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -c "' . $sql . '"', $return);

        $sql = 'ALTER TABLE su_cooperators_files_id_seq OWNER TO postgres;';
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -c "' . $sql . '"', $return);

        $sql = 'ALTER SEQUENCE su_cooperators_files_id_seq OWNED BY su_cooperators_files.id;';
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -c "' . $sql . '"', $return);

        // DROP TABLE cooperators_documents
        $sql = 'DROP TABLE IF EXISTS cooperators_documents;';
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -c "' . $sql . '"', $return);
    }
}
