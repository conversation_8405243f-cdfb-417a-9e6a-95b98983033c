<?php

namespace TF\Commands\sprint_s06;

use PDO;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Commands\ParamHelper;

/**
 * TS-738 command run on all databases.
 */
class TS738Command extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('sprint_s06:TS-738')
            ->setDescription('Sinhronizira starite prava na potrebitelite sas novata logika za "Sazdavane na podrobni niva za dostap";')
            ->addArgument(
                'user_ids',
                InputArgument::IS_ARRAY | InputArgument::OPTIONAL,
                'Databases to run the script on (separate multiple names with a space)?'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $user_ids = $input->getArgument('user_ids');

        $paramHelper = new ParamHelper();
        $usersIdString = $paramHelper->createQueryParamString($user_ids);

        $userDbConditions = 'true';
        if ($user_ids) {
            $userDbConditions = 'urs.user_id IN (' . $usersIdString . ')';
        }

        $mainDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        // delete the new Rights just in case
        $sql = $mainDev->prepare(
            "DELETE FROM su_users_rights urs WHERE {$userDbConditions} AND urs.right_id IN (
		 		" . Config::EQUITY_RIGHTS . ', 
		 		' . Config::CONTRACTS_READ_RIGHTS . ', 
		 		' . Config::CONTRACTS_WRITE_RIGHTS . ', 
		 		' . Config::CONTRACTS_OWN_WRITE_RIGHTS . ' 
		 		)'
        );

        $sql->execute();

        // select db
        $sql = $mainDev->prepare(
            "SELECT DISTINCT urs.user_id, u.database FROM su_users u
			INNER JOIN su_users_rights urs on urs.user_id = u.id
			WHERE {$userDbConditions}
			ORDER BY urs.user_id desc"
        );

        $sql->execute();
        $results = $sql->fetchAll();

        function checkAndInsertUserRight($userId, $rightId)
        {
            $cmd = $mainDev->prepare('SELECT * FROM su_users_rights urs WHERE user_id = :user_id AND right_id = :right_id');
            $cmd->bindValue(':user_id', $userId);
            $cmd->bindValue(':right_id', $rightId);
            $cmd->execute();
            $resultsByUserAndRight = $cmd->fetchAll();

            // insert if the RIGHT does not exist
            if (!count($resultsByUserAndRight)) {
                $sql = 'INSERT INTO su_users_rights (user_id,right_id) VALUES (:user_id, :right_id);';
                $cmd = $mainDev->prepare($sql);
                $cmd->bindValue(':user_id', $userId);
                $cmd->bindValue(':right_id', $rightId);
                $cmd->execute();
            }
        }

        // insert
        for ($i = 0; $i < count($results); $i++) {
            $cmd = $mainDev->prepare('SELECT 1 from pg_database WHERE datname = :datname');
            $cmd->bindValue(':datname', $results[$i]['database']);
            $cmd->execute();
            $result = $cmd->fetchAll();

            // id db does not exists
            if (!count($result)) {
                continue;
            }

            $output->writeln('database=' . $results[$i]['database'] . '');

            // select user's rights
            $cmd = $mainDev->prepare('SELECT * FROM su_users_rights urs WHERE user_id = :user_id');
            $cmd->bindValue(':user_id', $results[$i]['user_id']);
            $cmd->execute();
            $resultsByUser = $cmd->fetchAll();

            // insert the new rights according to the user's existing rights
            for ($j = 0; $j < count($resultsByUser); $j++) {
                if (Config::MAP_RIGHTS_R == $resultsByUser[$j]['right_id']) {
                    // check and insert user's MAP_RIGHTS_RW if does not exist
                    checkAndInsertUserRight($results[$i]['user_id'], Config::MAP_RIGHTS_RW);
                }

                if (Config::PLOT_RIGHTS_R == $resultsByUser[$j]['right_id']) {
                    // check and insert user's PLOT_RIGHTS_RW if does not exist
                    checkAndInsertUserRight($results[$i]['user_id'], Config::PLOT_RIGHTS_RW);

                    // check and insert user's CONTRACTS_READ_RIGHTS if does not exist
                    checkAndInsertUserRight($results[$i]['user_id'], Config::CONTRACTS_READ_RIGHTS);

                    // check and insert user's CONTRACTS_WRITE_RIGHTS if does not exist
                    checkAndInsertUserRight($results[$i]['user_id'], Config::CONTRACTS_WRITE_RIGHTS);
                }

                if (Config::SUBSIDY_RIGHTS == $resultsByUser[$j]['right_id']) {
                    // check and insert user's SUBSIDY_RIGHTS_RW if does not exist
                    checkAndInsertUserRight($results[$i]['user_id'], Config::SUBSIDY_RIGHTS_RW);
                }

                if (Config::AGRO_RIGHTS == $resultsByUser[$j]['right_id']) {
                    // check and insert user's AGRO_RIGHTS_RW if does not exist
                    checkAndInsertUserRight($results[$i]['user_id'], Config::AGRO_RIGHTS_RW);
                }

                if (Config::STATELLITE_RIGHTS == $resultsByUser[$j]['right_id']) {
                    // check and insert user's STATELLITE_RIGHTS_RW if does not exist
                    checkAndInsertUserRight($results[$i]['user_id'], Config::STATELLITE_RIGHTS_RW);
                }
            }

            // insert the new rights by default

            // check and insert user's PLOT_RIGHTS_RW if does not exist
            checkAndInsertUserRight($results[$i]['user_id'], Config::CONTRACTS_OWN_WRITE_RIGHTS);
        }
    }
}
