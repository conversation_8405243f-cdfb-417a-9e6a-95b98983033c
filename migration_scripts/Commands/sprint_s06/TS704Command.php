<?php

namespace TF\Commands\sprint_s06;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-704 command run on all databases.
 */
class TS704Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s06:TS-704')
            ->setDescription('sazdava tablicite "su_charged_renta_params", "su_charged_renta_natura_params" i "su_charged_renta_history"');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $sql = 'ALTER TABLE su_charged_renta ADD COLUMN owner_id int4;';
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . $userDb . ' -c "' . $sql . '"', $return);
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-704.sql', $return);
    }
}
