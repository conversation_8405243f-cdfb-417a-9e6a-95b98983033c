<?php

namespace TF\Commands\sprint_s06;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-798 command run on all databases.
 */
class TS798Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s06:TS-798')
            ->setDescription('Dobavq kolonite "recipient_egn" i "recipient_proxy" kam su_transactions i "representative_egn" i "representative_proxy" kam "su_devidends_payment"');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $sql = 'ALTER TABLE su_transactions ADD COLUMN recipient_egn varchar, ADD COLUMN recipient_proxy varchar;
	    ALTER TABLE su_devidends_payment ADD COLUMN representative_egn varchar, ADD COLUMN representative_proxy varchar;';

        $sql = str_replace("\r", ' ', $sql);

        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -c "' . $sql . '"', $return);
    }
}
