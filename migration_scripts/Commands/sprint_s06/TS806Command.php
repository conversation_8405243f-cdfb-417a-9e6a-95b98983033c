<?php

namespace TF\Commands\sprint_s06;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-806 command run on all databases.
 */
class TS806Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s06:TS-806')
            ->setDescription("Add-va CONSTRAINT 'su_contracts_plots_rel_contract_id_plot_id_key' UNIQUE ('contract_id, 'plot_id');");
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $sql = 'ALTER TABLE "su_contracts_plots_rel" ADD CONSTRAINT "su_contracts_plots_rel_contract_id_plot_id_key" UNIQUE ("contract_id", "plot_id");';
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -c "' . $sql . '"', $return);
    }
}
