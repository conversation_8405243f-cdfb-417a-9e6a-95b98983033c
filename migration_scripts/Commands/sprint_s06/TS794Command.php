<?php

namespace TF\Commands\sprint_s06;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-794 command run on all databases.
 */
class TS794Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s06:TS-794')
            ->setDescription('Setva TYPE varchar(63) na lk_izdavane v tablica su_cooperators.');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $sql = 'ALTER TABLE su_cooperators ALTER COLUMN lk_izdavane TYPE varchar(63);';
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -c "' . $sql . '"', $return);
    }
}
