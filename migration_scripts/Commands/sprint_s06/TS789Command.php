<?php

namespace TF\Commands\sprint_s06;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-789 command run on all databases.
 */
class TS789Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s06:TS-789')
            ->setDescription('Promenq tipa na kolona "percent" ot "real" na "double precision" v tablici "su_plots_owners_rel" i "su_plots_farming_rel" i preizchislqva procentite kogato ima vavedeni idealni chasti.');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -f ' . __DIR__ . '/sql/TS-789.sql ' . $userDb, $return);
    }
}
