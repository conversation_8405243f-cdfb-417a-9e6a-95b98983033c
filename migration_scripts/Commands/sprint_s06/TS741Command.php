<?php

namespace TF\Commands\sprint_s06;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-741 command run on all databases.
 */
class TS741Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s06:TS-741')
            ->setDescription('Dobavq kolonata za mqsto na poluchavane na renta "rent_place" v tablicite "su_owners" i "su_owners_reps".');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $sql = 'ALTER TABLE su_owners ADD COLUMN rent_place varchar;
	    ALTER TABLE su_owners_reps ADD COLUMN rent_place varchar;';
        $sql = str_replace("\r", ' ', $sql);

        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -c "' . $sql . '"', $return);
    }
}
