<?php

namespace TF\Commands\sprint_s06;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-833 command run on all databases.
 */
class TS833Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s06:TS-833')
            ->setDescription("Dobavq kolona 'payer_name' v transactions otgovarqshta na imeto na akaunta, izvarshil dadeno plashtane i q palni s danni ot logovete;");
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $dbhDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = 'ALTER TABLE su_transactions ADD COLUMN payer_name varchar;';
        $sql = str_replace("\r", ' ', $sql);

        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -c "' . $sql . '"', $return);

        // проверява дали не е взета new_user_db и дали има файл
        if ($databases[$i]['group_id'] && file_exists(LOG_PATH . $databases[$i]['group_id'] . '.log')) {
            $log_text = file_get_contents(LOG_PATH . $databases[$i]['group_id'] . '.log');
            $re = '/\\[Username: (?<username>[^\\]]+?)\\].*?\\[IDs: (?<transaction_id>[\\d]+?)\\]/i';
            preg_match_all($re, $log_text, $matches);

            for ($j = 0;$j < count($matches['username']); $j++) {
                $updateSql = $dbhDev->prepare(
                    'UPDATE su_transactions
	                SET payer_name=:username
	                WHERE id=:transaction_id;'
                );

                $updateSql->bindValue(':username', $matches['username'][$j]);
                $updateSql->bindValue(':transaction_id', $matches['transaction_id'][$j]);
                $updateSql->execute();
            }
        }
    }
}
