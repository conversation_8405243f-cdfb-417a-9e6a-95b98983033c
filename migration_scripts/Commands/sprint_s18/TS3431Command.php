<?php

namespace TF\Commands\sprint_s18;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-3431 command run on all databases.
 */
class TS3431Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s18:TS-3431')
            ->setDescription('dobavq kolona status v su_transactions');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-3431.sql', $return);
    }
}
