<?php

namespace TF\Commands\sprint_s18;

use TF\Commands\Common\UserDbCommand;

/**
 * Runs all scripts required for migration from Sprint 17 to Sprint 18.
 */
class UpdateToSprint18 extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s18:update-to-sprint18')
            ->setDescription('Runs all scripts required for migration from Sprint 17 to Sprint 18.');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $commandsArr = [
            // Sprint 18
            new TS3327Command(),
            new TS3431Command(),
            new TS3468Command(),
        ];

        for ($i = 0; $i < count($commandsArr); $i++) {
            $command = $commandsArr[$i];
            $this->logScript($userDb, $command->getName());
            $output->writeln('Executing command: ' . $command->getName());
            $command->onDbExecute($userDb, $output, $input);
        }
    }

    protected function logScript($user_db, $name)
    {
        $date = date('Y-m-d H:i:s');

        $this->openConnection();

        $sql = $this->mainConnection->prepare("
            INSERT INTO scripts_log (db_name, name, date)
            VALUES ('{$user_db}', '{$name}', '{$date}')
        ");

        return $sql->execute();
    }
}
