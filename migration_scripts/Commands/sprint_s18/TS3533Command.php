<?php

namespace TF\Commands\sprint_s18;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-3533.
 */
class TS3533Command extends UserDbCommand
{
    protected $userDev;

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s18:TS-3533')
            ->setDescription('removes duplicated owners (companies) specifically in nokolay_velikov');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $this->userDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
        $this->userDev->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // sobstvenici (firmi) s povtarqshti se ime i eik
        $sql
            = "SELECT min(id), eik,company_name, count(*)
            FROM su_owners
            where eik is not null
            and eik <> ''
            GROUP BY eik, company_name
          HAVING count(*) > 1;";
        $sql = $this->userDev->prepare($sql);
        $sql->execute();
        $originalRecords = $sql->fetchAll();

        foreach ($originalRecords as $key => $value) {
            $originalId = $value['min'];
            $duplicateIds = $this->getDuplicateRecords($value['company_name'], $value['eik']);

            $output->writeln("updating duplicates for {$value['company_name']} ({$originalId})");

            $this->userDev->beginTransaction();

            try {
                $this->updatePlotsOwners($originalId, $duplicateIds);
                $this->updatePayments($originalId, $duplicateIds);
                $this->updatePersonalUse($originalId, $duplicateIds);
                $this->updateOwnerDocuments($originalId, $duplicateIds);
                $this->deleteDuplicates($duplicateIds);

                $this->userDev->commit();
            } catch (Exception $e) {
                $this->userDev->rollBack();
                var_export($e);
                die;
            }
        }
    }

    private function getDuplicateRecords($companyName, $eik)
    {
        $duplicatesSql = "
             SELECT
              array_agg(id)
             FROM
              (
                SELECT
                  *,
                  ROW_NUMBER () OVER (
                    PARTITION BY eik, company_name
                  ORDER BY
                    ID
                  ) AS rnum
                FROM
                  su_owners
              where eik is NOT NULL
                and eik <> ''
                and eik = :eik
                and company_name = :company_name
             ) t
            WHERE rnum > 1;
            ";

        $duplicatesSql = $this->userDev->prepare($duplicatesSql);
        $duplicatesSql->bindParam(':eik', $eik);
        $duplicatesSql->bindParam(':company_name', $companyName);
        $duplicatesSql->execute();
        $duplicatedRecords = $duplicatesSql->fetch();

        $duplicatedRecords = str_replace(['{', '}'], '', $duplicatedRecords);

        return $duplicatedRecords['array_agg'];
    }

    private function updatePlotsOwners($originalId, $duplicateIds)
    {
        $updatePlotsOwnersSql = "
             Update su_plots_owners_rel set owner_id = :originalId
             where owner_id in ({$duplicateIds})
            ";

        $updatePlotsOwnersSql = $this->userDev->prepare($updatePlotsOwnersSql);
        $updatePlotsOwnersSql->bindParam(':originalId', $originalId);

        $updatePlotsOwnersSql->execute();
    }

    private function updatePayments($originalId, $duplicateIds)
    {
        $updatePaymentsSql = "
             Update su_payments set owner_id = :originalId
             where owner_id in ({$duplicateIds})
            ";

        $updatePaymentsSql = $this->userDev->prepare($updatePaymentsSql);
        $updatePaymentsSql->bindParam(':originalId', $originalId);
        $updatePaymentsSql->execute();
    }

    private function updatePersonalUse($originalId, $duplicateIds)
    {
        $updatePersonalUseSql = "
             Update su_personal_use set owner_id = :originalId
             where owner_id in ({$duplicateIds})
            ";

        $updatePersonalUseSql = $this->userDev->prepare($updatePersonalUseSql);
        $updatePersonalUseSql->bindParam(':originalId', $originalId);
        $updatePersonalUseSql->execute();
    }

    private function updateOwnerDocuments($originalId, $duplicateIds)
    {
        $upadteOwnerDocumentsSql = "
             Update su_owners_documents set owner_id = :originalId
             where owner_id in ({$duplicateIds})
            ";

        $upadteOwnerDocumentsSql = $this->userDev->prepare($upadteOwnerDocumentsSql);
        $upadteOwnerDocumentsSql->bindParam(':originalId', $originalId);
        $upadteOwnerDocumentsSql->execute();
    }

    private function deleteDuplicates($duplicateIds)
    {
        $deleteDuplicatesSql = "
             delete from su_owners
             where id in ({$duplicateIds})
            ";

        $deleteDuplicatesSql = $this->userDev->prepare($deleteDuplicatesSql);
        $deleteDuplicatesSql->execute();
    }
}
