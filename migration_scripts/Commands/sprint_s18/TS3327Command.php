<?php

namespace TF\Commands\sprint_s18;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-3307 command run on all databases.
 */
class TS3327Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s18:TS-3327')
            ->setDescription('Create column folder_id in su_contracts and set folder values');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-3327.sql', $return);
    }
}
