<?php

namespace TF\Commands\sprint_s18;

use PDO;
use Prado;
use Symfony\Component\Console\Input\InputOption;
use TF\Commands\Common\UserDbCommand;

Prado::using('Plugins.Core.UserDb.*');
Prado::using('Plugins.Core.UserDb.conf');
Prado::using('Plugins.Core.Users.*');
Prado::using('Plugins.Core.Users.conf');
Prado::using('Plugins.Core.UserDbOwners.*');

/**
 * TS-3305 command run on all databases.
 */
class TS3468Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s18:TS-3468')
            ->setDescription('Migration script spirane na aneksa za prodajba')
            ->addOption('annex', null, InputOption::VALUE_OPTIONAL, 'annex');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $userDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
        $sql = (
            'update
                su_contracts c
                    set
                        active = :status
                    from
                        (
                            select
                                ca_active,
                                ca_id
                            from
                                (
                                    select
                                        row_number() over(
                                            partition by ca.id
                                        ) as rn,
                                        ca.id as ca_id,
                                        ca.active as ca_active
                                    from
                                        su_contracts ca join su_sales_contracts_plots_rel scpr on
                                        (
                                            ca.parent_id = scpr.contract_id
                                        ) join su_sales_contracts sa on
                                        (
                                            sa.id = scpr.sales_contract_id
                                        )
                                    where
                                        ca.is_annex = true
                                        and ca.nm_usage_rights = 1
                                    order by
                                        ca.id
                                ) as t1
                            where
                                t1.rn = 1
                        ) t2
                    where
	                c.id = t2.ca_id'
        );

        $status = 'true' == $input->getOption('annex') ? true : false;
        $sql = $userDev->prepare($sql);
        $sql->bindParam(':status', $status, PDO::PARAM_BOOL);
        $sql->execute();
    }
}
