<?php

namespace TF\Commands\sprint_s18;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-3499 command run on all databases.
 */
class TS3499Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s18:TS-3499')
            ->setDescription('Check if su_sublease_contract_plots_rel has foreign key to su_contracts');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        // system('psql -h '.DEFAULT_DB_HOST.' -p '.DEFAULT_DB_PORT.' -U postgres -d '.$userDb.' -f '.__DIR__.'/sql/TS-3499.sql', $return);

        $userDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = $userDev->prepare(
            "SELECT * FROM information_schema.table_constraints WHERE 
            constraint_type = 'FOREIGN KEY' and
            table_name='su_subleases_plots_contracts_rel'"
        );

        $sql->execute();
        $records = $sql->fetchAll();

        if (!count($records)) {
            $output->writeln("{$userDb} does not have foreign key ");
        }
    }
}
