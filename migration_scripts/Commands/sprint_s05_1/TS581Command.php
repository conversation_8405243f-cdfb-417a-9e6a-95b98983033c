<?php

namespace TF\Commands\sprint_s05_1;

use PDO;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * TS-581 command run on all databases.
 */
class TS581Command extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('sprint_s05_1:TS-581')
            ->setDescription('prehvarlqne na akaunti ot login1 i login2 kam login3')
            ->addArgument(
                'usernames',
                InputArgument::IS_ARRAY | InputArgument::REQUIRED,
                'usernames to transfer (separate multiple names with a space)?'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $usernames = $input->getArgument('usernames');
        $users = $usernames;

        $dbhLogin = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
        $dbhLogin3 = new PDO('pgsql:host=' . DBLINK_HOST . ';port=' . DBLINK_PORT . ';dbname=' . DBLINK_DATABASE . ';', DBLINK_USERNAME, DBLINK_PASSWORD);
        $dbhLogin3->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        for ($i = 0; $i < count($users); $i++) {
            $user = $users[$i];
            $sql = 'SELECT s_u.username as parent, s_u1.id as child_id, s_u1.username as child_username, s_u1.*
				FROM su_users s_u 
				INNER JOIN su_users s_u1 on (s_u.id = s_u1.parent_id)
				where s_u.username = :user
				ORDER BY parent';

            // login
            $sqlLogin = $dbhLogin->prepare($sql);
            $sqlLogin->bindValue(':user', $user);
            $sqlLogin->execute();

            $sqlLoginCount = $sqlLogin->rowCount();
            $loginResults = $sqlLogin->fetchAll();

            // login3
            $sqlLogin3 = $dbhLogin3->prepare($sql);
            $sqlLogin3->bindValue(':user', $user);
            $sqlLogin3->execute();

            $sqlLogin3Count = $sqlLogin3->rowCount();
            $loginResults3 = $sqlLogin3->fetchAll();

            if ($sqlLoginCount != $sqlLogin3Count && 0 == $sqlLogin3Count) {
                for ($j = 0; $j < $sqlLoginCount; $j++) {
                    $loginResult = $loginResults[$j];
                    $sqlLogin3GetUserId = 'SELECT id
						FROM su_users s_u 
						WHERE s_u.username = :user';

                    $getUserIdLogin3 = $dbhLogin3->prepare($sqlLogin3GetUserId);
                    $getUserIdLogin3->bindValue(':user', $user);
                    $getUserIdLogin3->execute();

                    $userIdLogin3Result = $getUserIdLogin3->fetch();

                    $sqlInsert = 'INSERT INTO su_users
								  (username, password, name, address, phone, email, comment, is_superadmin, database, hash, parent_id, can_create,
								   level, group_id, active, server, start_date, due_date, entry_flag, entries_left, date_flag, map_type, is_trial,
								   allowed_farmings, track_username, track_password, creation_date)
								  VALUES (:username, :password, :name, :address ,:phone ,:email ,:comment ,:is_superadmin ,:database ,:hash
								  ,:parent_id ,:can_create ,:level ,:group_id ,:active ,:server ,:start_date ,:due_date ,:entry_flag ,:entries_left ,:date_flag
								  ,:map_type ,:is_trial ,:allowed_farmings ,:track_username ,:track_password ,:creation_date)
								  ';

                    $dbhLogin3->beginTransaction();

                    try {
                        $sqlLogin3Insert = $dbhLogin3->prepare($sqlInsert);
                        $sqlLogin3Insert->bindValue(':username', $loginResult['username']);
                        $sqlLogin3Insert->bindValue(':password', $loginResult['password']);
                        $sqlLogin3Insert->bindValue(':name', $loginResult['name']);
                        $sqlLogin3Insert->bindValue(':address', $loginResult['address']);
                        $sqlLogin3Insert->bindValue(':phone', $loginResult['phone']);
                        $sqlLogin3Insert->bindValue(':email', $loginResult['email']);
                        $sqlLogin3Insert->bindValue(':comment', $loginResult['comment']);
                        $sqlLogin3Insert->bindValue(':is_superadmin', $loginResult['is_superadmin'], PDO::PARAM_BOOL);
                        $sqlLogin3Insert->bindValue(':database', $loginResult['database']);
                        $sqlLogin3Insert->bindValue(':hash', $loginResult['hash']);
                        $sqlLogin3Insert->bindValue(':parent_id', $userIdLogin3Result['id']);
                        $sqlLogin3Insert->bindValue(':can_create', $loginResult['can_create']);
                        $sqlLogin3Insert->bindValue(':level', $loginResult['level']);
                        $sqlLogin3Insert->bindValue(':group_id', $userIdLogin3Result['id']);
                        $sqlLogin3Insert->bindValue(':active', $loginResult['active'], PDO::PARAM_BOOL);
                        $sqlLogin3Insert->bindValue(':server', $loginResult['server']);

                        if ('' == $loginResult['start_date']) {
                            $startDate = null;
                        } else {
                            $startDate = date('Y-m-d H:i:s', strtotime($loginResult['start_date']));
                        }
                        $sqlLogin3Insert->bindValue(':start_date', $startDate);

                        if ('' == $loginResult['due_date']) {
                            $dueDate = null;
                        } else {
                            $dueDate = date('Y-m-d H:i:s', strtotime($loginResult['due_date']));
                        }
                        $sqlLogin3Insert->bindValue(':due_date', $dueDate);

                        $sqlLogin3Insert->bindValue(':entry_flag', $loginResult['entry_flag'], PDO::PARAM_BOOL);
                        $sqlLogin3Insert->bindValue(':entries_left', $loginResult['entries_left']);
                        $sqlLogin3Insert->bindValue(':date_flag', $loginResult['date_flag'], PDO::PARAM_BOOL);
                        $sqlLogin3Insert->bindValue(':map_type', $loginResult['map_type']);
                        $sqlLogin3Insert->bindValue(':is_trial', $loginResult['is_trial'], PDO::PARAM_BOOL);
                        $sqlLogin3Insert->bindValue(':allowed_farmings', $loginResult['allowed_farmings']);
                        $sqlLogin3Insert->bindValue(':track_username', $loginResult['track_username']);
                        $sqlLogin3Insert->bindValue(':track_password', $loginResult['track_password']);

                        if ('' == $loginResult['creation_date']) {
                            $creationDate = null;
                        } else {
                            $creationDate = date('Y-m-d H:i:s', strtotime($loginResult['creation_date']));
                        }
                        $sqlLogin3Insert->bindValue(':creation_date', $creationDate);

                        var_export('Username: ' . $loginResult['username'] . '');
                        var_export('Parent_id: ' . $userIdLogin3Result['id'] . '');

                        $sqlLogin3Insert->execute();
                        $dbhLogin3->commit();
                    } catch (Exception $e) {
                        $dbhLogin3->rollBack();
                        var_export($e);
                        die;
                    }

                    // work with su_system_users

                    // get last ID cause INSERT by ID in su_system_users, su_users_rights
                    $newUserId = $dbhLogin3->lastInsertId('su_users_id_seq');
                    var_export('New user_id: ' . $newUserId . '');
                    // get username from su_system_users
                    $sqlUserSystemSelect = 'SELECT sys_u.*
							FROM su_users s_u 
							INNER JOIN su_system_users sys_u on (s_u.username = sys_u.username)
							where s_u.username = :username';

                    $sqlLoginSelect = $dbhLogin->prepare($sqlUserSystemSelect);
                    $sqlLoginSelect->bindValue(':username', $loginResult['username']);
                    $sqlLoginSelect->execute();

                    $loginSelectResult = $sqlLoginSelect->fetch();

                    // insert new USER into su_system_users
                    $sqlUserSystemInsert = 'INSERT INTO su_system_users (user_id, username, password)
		    								VALUES (:user_id, :username, :password)';

                    $dbhLogin3->beginTransaction();

                    try {
                        $sqlLogin3Insert = $dbhLogin3->prepare($sqlUserSystemInsert);

                        $sqlLogin3Insert->bindValue(':user_id', $newUserId, PDO::PARAM_INT);
                        $sqlLogin3Insert->bindValue(':username', $loginSelectResult['username']);
                        $sqlLogin3Insert->bindValue(':password', $loginSelectResult['password']);
                        $sqlLogin3Insert->execute();
                        var_export('New user_id - username: ' . $newUserId . '-' . $loginSelectResult['username'] . '');
                        $dbhLogin3->commit();
                    } catch (Exception $e) {
                        $dbhLogin3->rollBack();
                        var_export($e);
                        die;
                    }

                    // work with su_users_rights
                    $sqlUserRightsSelect = 'SELECT u_r.*
											FROM su_users s_u 
											INNER JOIN su_users_rights u_r on (s_u.id = u_r.user_id)
											where s_u.username = :username';

                    $sqlLoginRightsSelect = $dbhLogin->prepare($sqlUserRightsSelect);
                    $sqlLoginRightsSelect->bindValue(':username', $loginResult['username']);
                    $sqlLoginRightsSelect->execute();

                    $loginSelectRightsResults = $sqlLoginRightsSelect->fetchAll();

                    for ($k = 0; $k < count($loginSelectRightsResults); $k++) {
                        $loginSelectRightsResult = $loginSelectRightsResults[$k];

                        $sqlUserRightsInsert = 'INSERT INTO su_users_rights (user_id, right_id)
		    								VALUES (:user_id, :right_id)';

                        $dbhLogin3->beginTransaction();

                        try {
                            $sqlLogin3RightsInsert = $dbhLogin3->prepare($sqlUserRightsInsert);

                            $sqlLogin3RightsInsert->bindValue(':user_id', $newUserId, PDO::PARAM_INT);
                            $sqlLogin3RightsInsert->bindValue(':right_id', $loginSelectRightsResult['right_id']);
                            $sqlLogin3RightsInsert->execute();
                            var_export('New user_id - rights_id: ' . $newUserId . '-' . $loginSelectRightsResult['right_id'] . '');
                            $dbhLogin3->commit();
                        } catch (Exception $e) {
                            $dbhLogin3->rollBack();
                            var_export($e);
                            die;
                        }
                    }
                }
            }
        }
    }
}
