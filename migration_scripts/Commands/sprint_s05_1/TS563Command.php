<?php

namespace TF\Commands\sprint_s05_1;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-563 command run on all databases.
 */
class TS563Command extends UserDbCommand
{
    protected function configure()
    {
        $this
            ->setName('sprint_s05_1:TS-563')
            ->setDescription('Dobavqne na VPS sloevete na potrebitel');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $dbhDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = $dbhDev->prepare('select t.user_id , f.id as farm_id from su_users_layers t join su_users_farming f on (f.user_id = t.user_id)
		left join su_users u on (t.user_id = u.id) where u.database = :userDb AND f.is_system = true GROUP BY t.user_id, f.id ORDER BY user_id');
        $sql->bindParam(':userDb', $userDb);
        $sql->execute();
        $results = $sql->fetchAll();
        foreach ($results as $user) {
            $output->writeln($user[0] . '');

            $sql = 'INSERT INTO su_users_layers
		        (user_id, name, table_name, date_created, color, border_color, extent, farming, year,transparency, position, layer_type, group_id, tags, border_only) values 
		        (' . $user[0] . ", 'ВПС - пасища', 'layer_vps_merg','2015-03-23 16:42:21.372834', 'ffffff', 'A70000', '125190.6162 4573142.7188 631370.3273 4887149.5823', " . $user[1] . ', 6, 100, 0, 13, ' . $user[0] . ', true, true);
		INSERT INTO su_users_layers
		        (user_id, name, table_name, date_created, color, border_color, extent, farming, year,transparency, position, layer_type, group_id, tags, border_only) values 
		        (' . $user[0] . ", 'ВПС - червеногуши гъски', 'layer_vps_gaski_chervenogushi','2015-03-23 16:42:21.372834', 'ffffff', 'FFA41C', '125190.6162 4573142.7188 631370.3273 4887149.5823', " . $user[1] . ', 6, 100, 0, 14, ' . $user[0] . ', true, true);
		INSERT INTO su_users_layers
		        (user_id, name, table_name, date_created, color, border_color, extent, farming, year,transparency, position, layer_type, group_id, tags, border_only) values 
		        (' . $user[0] . ", 'ВПС - зимуващи гъски', 'layer_vps_gaski_zimni','2015-03-23 16:42:21.372834', 'ffffff', 'E54100', '125190.6162 4573142.7188 631370.3273 4887149.5823', " . $user[1] . ', 6, 100, 0, 15, ' . $user[0] . ', true, true);
		INSERT INTO su_users_layers
		        (user_id, name, table_name, date_created, color, border_color, extent, farming, year,transparency, position, layer_type, group_id, tags, border_only) values 
		        (' . $user[0] . ", 'ВПС - ливаден блатар', 'layer_vps_livaden_blatar','2015-03-23 16:42:21.372834', 'ffffff', 'A86C0F', '125190.6162 4573142.7188 631370.3273 4887149.5823', " . $user[1] . ', 6, 100, 0, 16, ' . $user[0] . ', true, true);
		INSERT INTO su_users_layers
		        (user_id, name, table_name, date_created, color, border_color, extent, farming, year,transparency, position, layer_type, group_id, tags, border_only) values
		        (' . $user[0] . ", 'ВПС - Царски орел и Египетски лешояд', 'layer_vps_orli_leshoyadi','2015-03-23 16:42:21.372834', 'ffffff', 'A900E6', '125190.6162 4573142.7188 631370.3273 4887149.5823', " . $user[1] . ', 6, 100, 0, 17, ' . $user[0] . ', true, true);';

            system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . DEFAULT_DB_DATABASE . ' -c "' . $sql . '"', $return);
            $output->writeln($return);
        }
    }
}
