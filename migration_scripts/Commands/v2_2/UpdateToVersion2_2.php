<?php

namespace TF\Commands\v2_2;

use TF\Commands\Common\UserDbCommand;

/**
 * Runs all scripts required for migration from Version 2.1 to 2.2.
 */
class UpdateToVersion2_2 extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v2.2:update-to-version-2.2')
            ->setDescription('Runs all scripts required for migration from version 2.1 to version 2.2.');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $commandsArr = [
            new TS3837Command(),
            new TS4145_B_Command(),
            new TS4060Command(),
            new TS4623Command(),
            new TS4647_1Command(),
            new TS4647Command(),
            new TS4783Command(),
            new TS4992Command(),
        ];

        $count = count($commandsArr);
        for ($i = 0; $i < $count; $i++) {
            $command = $commandsArr[$i];
            $this->logScript($userDb, $command->getName());
            $output->writeln('Executing command: ' . $command->getName());
            $command->onDbExecute($userDb, $output, $input);
        }
    }

    protected function logScript($user_db, $name)
    {
        $date = date('Y-m-d H:i:s');

        $this->openConnection();

        $sql = $this->mainConnection->prepare("
            INSERT INTO scripts_log (db_name, name, date)
            VALUES ('{$user_db}', '{$name}', '{$date}')
        ");

        return $sql->execute();
    }
}
