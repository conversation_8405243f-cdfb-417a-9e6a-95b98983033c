<?php

namespace TF\Commands\v2_2;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-3837 command run on all databases.
 */
class TS3837Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v2.2:TS-3837')
            ->setDescription('Add telephone field on su_owners_rep table');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-3837.sql', $return);
    }
}
