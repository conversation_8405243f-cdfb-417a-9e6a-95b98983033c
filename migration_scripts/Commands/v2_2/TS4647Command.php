<?php

namespace TF\Commands\v2_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-4647 command run on all databases.
 */
class TS4647Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v2.2:TS-4647')
            ->setDescription('Creates representatives out of Farming MOLs; Must run TS-TS4647_1Command first');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $this->userDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
        $this->mainDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $farmingData = $this->getFarmingMols($userDb);

        foreach ($farmingData as $key => $farming) {
            $molNameArr = split(' ', $farming['mol']);
            $molNameArr[0] = $molNameArr[0] ? $molNameArr[0] : '';
            $molNameArr[1] = $molNameArr[1] ? $molNameArr[1] : '';
            $molNameArr[2] = $molNameArr[2] ? $molNameArr[2] : '';
            $molEgn = $farming['mol_egn'];

            $representative_id = $this->insertRepresentative($molNameArr, $molEgn);
            $this->addFarmingRepresentative($farming['id'], $representative_id);
        }
    }

    private function getFarmingMols($database)
    {
        $sql = $this->mainDev->prepare("
            SELECT u.id as user_id, uf.* from su_users u
            join su_users_farming uf on uf.group_id = u.group_id
            where database = '{$database}'
            and level = 2
            ");
        $sql->execute();

        return $sql->fetchAll(PDO::FETCH_ASSOC);
    }

    private function insertRepresentative($molNameArr, $molEgn)
    {
        $checkRepExists = $this->userDev->prepare("select * from su_owners_reps where trim(rep_name) = '{$molNameArr[0]}' AND trim(rep_surname) = '{$molNameArr[1]}' AND trim(rep_lastname) = '{$molNameArr[2]}' AND trim(rep_egn) = '{$molEgn}'");

        $checkRepExists->execute();
        $result = $checkRepExists->fetchAll(PDO::FETCH_ASSOC);
        if (empty($result)) {
            $rep_sql = 'INSERT INTO su_owners_reps (rep_name, rep_surname, rep_lastname, rep_egn, rep_lk, rep_lk_izdavane, rep_address, owner_id, rent_place, iban)';

            $rep_sql .= " VALUES ('{$molNameArr[0]}', '{$molNameArr[1]}', '{$molNameArr[2]}', '{$molEgn}', NULL, NULL, NULL, NULL, NULL, NULL)
                        RETURNING id";
            $create_rep_sql = $this->userDev->prepare($rep_sql);

            $create_rep_sql->execute();
            $representative_id = $create_rep_sql->fetchColumn();
        } else {
            $representative_id = $result[0]['id'];
        }

        return $representative_id;
    }

    private function addFarmingRepresentative($farmingId, $representativeId)
    {
        $sql = $this->mainDev->prepare("
            UPDATE su_users_farming SET representative_id = {$representativeId}
            WHERE id = {$farmingId}
        ");

        $sql->execute();
    }
}
