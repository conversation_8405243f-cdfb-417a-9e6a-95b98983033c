<?php

namespace TF\Commands\v2_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-4832 command run on all databases.
 */
class TS4832Command extends UserDbCommand
{
    private $userDatabase;
    private $mainDatabase;
    private $layers;

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v2.2:TS-4832')
            ->setDescription('Sets is_exist=false in su_users_layers for all missing database');
    }

    protected function onDbExecute($database, $output, $input)
    {
        $this->userDatabase = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $database . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = "SELECT a.layer_id
                FROM dblink(
                        'host=" . DEFAULT_DB_HOST . '
                        port=' . DEFAULT_DB_PORT . '
                        dbname=' . DEFAULT_DB_DATABASE . '
                        user=' . DEFAULT_DB_USERNAME . '
                        password=' . DEFAULT_DB_PASSWORD . "',
                      'SELECT
                        u.id as user_id,
                        u.level as level,
                        l.id as layer_id,
                        l.table_name as table_name,
                        l.is_exist as is_exist
                      FROM su_users u
                      LEFT JOIN su_users_layers l ON (u.id = l.user_id)
                      WHERE u.level = 2 and l.is_exist = true and u.database = ''{$database}'''
                      ) AS a (
                          user_id int4,
                          level int4,
                          layer_id int4,
                          table_name varchar,
                          is_exist bool
                        )
                LEFT JOIN information_schema.tables ist ON a.table_name = ist.table_name
                WHERE ist.table_name ISNULL";

        $cmd = $this->userDatabase->prepare($sql);
        $cmd->execute();
        $this->layers = $cmd->fetchAll(PDO::FETCH_COLUMN);
        $this->layers = implode(',', $this->layers);

        $this->mainDatabase = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql2 = "UPDATE su_users_layers SET is_exist = false
                 WHERE id IN ({$this->layers})";
        $smtp = $this->mainDatabase->prepare($sql2);
        $smtp->execute();
    }
}
