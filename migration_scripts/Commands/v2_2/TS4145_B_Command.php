<?php

namespace TF\Commands\v2_2;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-4145 command run on all databases.
 */
class TS4145_B_Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v2.2:TS-4145_B')
            ->setDescription('Add a new column farming id in su_payments');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-4145_B.sql', $return);
    }
}
