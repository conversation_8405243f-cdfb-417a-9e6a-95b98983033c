<?php

namespace TF\Commands\v2_2;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-4060 command run on all databases.
 */
class TS4864Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v2.2:TS-4864')
            ->setDescription('Changes layer_kvs.document_area column type from float4 to DECIMAL(10, 4).');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-4864.sql', $return);
    }
}
