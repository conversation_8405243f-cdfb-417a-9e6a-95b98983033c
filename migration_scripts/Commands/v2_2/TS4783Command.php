<?php

namespace TF\Commands\v2_2;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-4783 command run on all databases.
 */
class TS4783Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v2.2:TS-4783')
            ->setDescription('Create ab_lines table');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-4783.sql', $return);
    }
}
