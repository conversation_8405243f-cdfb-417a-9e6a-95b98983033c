DROP TRIGGER "LAYER_KVS_TRIGGER" ON "public"."layer_kvs";

ALTER TABLE "public"."layer_kvs"
ALTER COLUMN "document_area" TYPE decimal(10,4) using document_area::FLOAT8;

CREATE TRIGGER "LAYER_KVS_TRIGGER" AFTER INSERT OR UPDATE OF "gid", "geom", "category", "area_type", "has_contracts", "used_area_by", "area_farming", "area_year", "used_area", "usable", "document_area", "is_edited" OR DELETE ON "public"."layer_kvs"
FOR EACH STATEMENT
EXECUTE PROCEDURE "refresh_rentas_materialized_views"();
