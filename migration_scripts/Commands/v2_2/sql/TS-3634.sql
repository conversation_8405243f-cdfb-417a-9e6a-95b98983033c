DROP MATERIALIZED VIEW "public"."renta_nats_mat_view";

CREATE MATERIALIZED VIEW "public"."renta_nats_mat_view"
AS
 SELECT (crt.renta_value * ((((pc.area_for_rent)::double precision * po.percent) / (100)::double precision) - COALESCE(pu.area, (0)::real))) AS renta_nat,
    (COALESCE(sum(pc.rent_per_plot), (c.renta)::double precision)::double precision * ((((pc.area_for_rent)::double precision * po.percent) / (100)::double precision) - COALESCE(pu.area, (0)::real))) AS renta,
    (((pc.area_for_rent)::double precision * (po.percent / (100)::double precision)) - COALESCE(pu.area, (0)::real)) AS contract_area,
    c.c_num,
    c.id AS c_id,
    c.farming_id,
    c.start_date,
    c.due_date,
    po.owner_id,
    o.is_dead,
    kvs.ekate,
    kvs.gid AS plot_id,
    crt.renta_id AS nat_type,
    rt.name,
    rt.unit,
    pu.year
   FROM (((((((su_contracts c
     JOIN su_contracts_plots_rel pc ON ((pc.contract_id = c.id)))
     JOIN su_plots_owners_rel po ON ((po.pc_rel_id = pc.id)))
     JOIN su_owners o ON ((o.id = po.owner_id)))
     JOIN layer_kvs kvs ON ((kvs.gid = pc.plot_id)))
     LEFT JOIN su_personal_use pu ON (((pu.owner_id = po.owner_id) AND (pu.pc_rel_id = pc.id))))
     LEFT JOIN su_contracts_rents crt ON ((c.id = crt.contract_id)))
     LEFT JOIN su_renta_types rt ON ((crt.renta_id = rt.id)))
  WHERE ((pc.annex_action = 'added'::annex_action_enum) AND (c.is_annex = false))
  GROUP BY rt.name, crt.renta_value, crt.renta_id, pc.area_for_rent, po.percent, c.id, po.owner_id, c.farming_id, c.start_date, c.due_date, rt.unit, kvs.ekate, kvs.gid, o.is_dead, pu.area, pu.year
  ORDER BY c.id;;

ALTER MATERIALIZED VIEW "renta_nats_mat_view" OWNER TO "postgres";

DROP MATERIALIZED VIEW "public"."charged_rentas_annexes_mat_view";

---

CREATE MATERIALIZED VIEW "public"."charged_rentas_annexes_mat_view"
AS
 SELECT (COALESCE(sum(pc.rent_per_plot), (c.renta)::double precision)::double precision * ((pc.area_for_rent * (po.percent / (100)::double precision)) - COALESCE(pu.area, (0)::real))) AS charged_renta,
        CASE
            WHEN (crn.nat_is_converted = true) THEN NULL::double precision
            ELSE (crn.amount * ((pc.area_for_rent * (po.percent / (100)::double precision)) - COALESCE(pu.area, (0)::real)))
        END AS charged_renta_nat,
        CASE
            WHEN (crn.nat_is_converted = true) THEN ((crn.amount * crn.nat_unit_price) * ((pc.area_for_rent * (po.percent / (100)::double precision)) - COALESCE(pu.area, (0)::real)))
            ELSE NULL::double precision
        END AS converted_charged_renta_nat,
    a.id AS a_id,
    c.id AS c_id,
    po.owner_id,
    crn.nat_is_converted,
    crn.nat_type,
    pc.plot_id,
    kvs.ekate,
    rt.name,
    rt.unit,
    cr.year,
    a.farming_id,
    a.start_date,
    a.due_date,
    pu.year AS pu_year
   FROM ((((((((su_contracts c
     LEFT JOIN su_contracts a ON (((a.parent_id = c.id) AND (a.active = true))))
     JOIN su_contracts_plots_rel pc ON ((pc.contract_id = a.id)))
     JOIN layer_kvs kvs ON ((kvs.gid = pc.plot_id)))
     LEFT JOIN su_plots_owners_rel po ON ((po.pc_rel_id = pc.id)))
     LEFT JOIN su_personal_use pu ON (((pu.owner_id = po.owner_id) AND (pu.pc_rel_id = pc.id))))
     JOIN su_charged_renta cr ON ((((cr.plot_id = kvs.gid) AND (cr.contract_id = c.id)) AND (cr.owner_id = po.owner_id))))
     LEFT JOIN su_charged_renta_natura crn ON ((crn.renta_id = cr.id)))
     LEFT JOIN su_renta_types rt ON ((crn.nat_type = rt.id)))
  WHERE ((((a.is_annex = true) AND (a.is_sublease = false)) AND (po.is_heritor = false)) AND (pc.annex_action = 'added'::annex_action_enum))
  GROUP BY po.owner_id, crn.nat_type, pc.plot_id, crn.nat_is_converted, crn.amount, pc.area_for_rent, po.percent, a.id, c.id, crn.nat_unit_price, kvs.ekate, rt.name, rt.unit, cr.year, cr.renta, pu.area, pu.year;;

ALTER MATERIALIZED VIEW "charged_rentas_annexes_mat_view" OWNER TO "postgres";

---

DROP MATERIALIZED VIEW "public"."charged_rentas_mat_view";

CREATE MATERIALIZED VIEW "public"."charged_rentas_mat_view"
AS
 SELECT (COALESCE(sum(pc.rent_per_plot), (c.renta)::double precision)::double precision * ((pc.area_for_rent * (po.percent / (100)::double precision)) - COALESCE(pu.area, (0)::real))) AS charged_renta,
        CASE
            WHEN (crn.nat_is_converted = true) THEN NULL::double precision
            ELSE (crn.amount * ((pc.area_for_rent * (po.percent / (100)::double precision)) - COALESCE(pu.area, (0)::real)))
        END AS charged_renta_nat,
        CASE
            WHEN (crn.nat_is_converted = true) THEN ((crn.amount * crn.nat_unit_price) * ((pc.area_for_rent * (po.percent / (100)::double precision)) - COALESCE(pu.area, (0)::real)))
            ELSE NULL::double precision
        END AS converted_charged_renta_nat,
    c.id AS c_id,
    po.owner_id,
    crn.nat_is_converted,
    crn.nat_type,
    pc.plot_id,
    kvs.ekate,
    rt.name,
    rt.unit,
    cr.year,
    c.farming_id,
    c.start_date,
    c.due_date,
    pu.year AS pu_year
   FROM (((((((su_contracts c
     JOIN su_contracts_plots_rel pc ON ((pc.contract_id = c.id)))
     JOIN layer_kvs kvs ON ((kvs.gid = pc.plot_id)))
     LEFT JOIN su_plots_owners_rel po ON ((po.pc_rel_id = pc.id)))
     LEFT JOIN su_personal_use pu ON (((pu.owner_id = po.owner_id) AND (pu.pc_rel_id = pc.id))))
     JOIN su_charged_renta cr ON ((((cr.plot_id = kvs.gid) AND (cr.contract_id = c.id)) AND (cr.owner_id = po.owner_id))))
     LEFT JOIN su_charged_renta_natura crn ON ((crn.renta_id = cr.id)))
     LEFT JOIN su_renta_types rt ON ((crn.nat_type = rt.id)))
  WHERE ((((c.is_annex = false) AND (c.is_sublease = false)) AND (po.is_heritor = false)) AND (pc.annex_action = 'added'::annex_action_enum))
  GROUP BY po.owner_id, crn.nat_type, pc.plot_id, crn.nat_is_converted, crn.amount, pc.area_for_rent, po.percent, c.id, crn.nat_unit_price, kvs.ekate, rt.name, rt.unit, cr.year, cr.renta, pu.area, pu.year;;

ALTER MATERIALIZED VIEW "charged_rentas_mat_view" OWNER TO "postgres";

---

DROP MATERIALIZED VIEW "public"."renta_nats_annexes_mat_view";

CREATE MATERIALIZED VIEW "public"."renta_nats_annexes_mat_view"
AS
 SELECT (crt.renta_value * (((pc.area_for_rent * po.percent) / (100)::double precision) - COALESCE(pu.area, (0)::real))) AS renta_nat,
    (
	COALESCE(sum(pc.rent_per_plot),CASE
            WHEN (a.renta IS NULL) THEN c.renta
            ELSE a.renta
        END )
			* ((pc.area_for_rent * (po.percent / (100)::double precision)) - COALESCE(pu.area, (0)::real))) AS renta,
    a.renta AS annex_renta,
    pc.area_for_rent AS annex_area,
    ((pc.area_for_rent * (po.percent / (100)::double precision)) - COALESCE(pu.area, (0)::real)) AS contract_area,
    c.id AS c_id,
    a.id AS a_id,
    a.farming_id,
    a.start_date,
    a.due_date,
    po.owner_id,
    o.is_dead,
    kvs.ekate,
    kvs.gid AS plot_id,
    crt.renta_id AS nat_type,
    rt.name,
    rt.unit,
    pu.year
   FROM ((((((((su_contracts c
     LEFT JOIN su_contracts a ON (((a.parent_id = c.id) AND (a.active = true))))
     JOIN su_contracts_plots_rel pc ON ((pc.contract_id = a.id)))
     LEFT JOIN su_plots_owners_rel po ON ((po.pc_rel_id = pc.id)))
     JOIN su_owners o ON ((o.id = po.owner_id)))
     LEFT JOIN layer_kvs kvs ON ((kvs.gid = pc.plot_id)))
     LEFT JOIN su_personal_use pu ON (((pu.owner_id = po.owner_id) AND (pu.pc_rel_id = pc.id))))
     LEFT JOIN su_contracts_rents crt ON (
        CASE
            WHEN (a.id IS NULL) THEN (c.id = crt.contract_id)
            ELSE (a.id = crt.contract_id)
        END))
     LEFT JOIN su_renta_types rt ON ((crt.renta_id = rt.id)))
  WHERE ((pc.annex_action = 'added'::annex_action_enum) AND (a.is_annex = true))
  GROUP BY rt.name, crt.renta_value, crt.renta_id, pc.area_for_rent, po.percent, c.id, a.id, po.owner_id, a.farming_id, a.start_date, a.due_date, rt.unit, kvs.ekate, kvs.gid, o.is_dead, pu.area, pu.year
  ORDER BY c.id;;

ALTER MATERIALIZED VIEW "renta_nats_annexes_mat_view" OWNER TO "postgres";

REFRESH MATERIALIZED VIEW charged_rentas_mat_view;
REFRESH MATERIALIZED VIEW charged_rentas_annexes_mat_view;
REFRESH MATERIALIZED VIEW renta_nats_mat_view;
REFRESH MATERIALIZED VIEW renta_nats_annexes_mat_view;
