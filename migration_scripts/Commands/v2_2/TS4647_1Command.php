<?php

namespace TF\Commands\v2_2;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-4647_1 command run on the main database.
 */
class TS4647_1Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v2.2:TS-4647_1')
            ->setDescription('Adds representative_id column in the su_users_farming tables');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . DEFAULT_DB_DATABASE . ' -f ' . __DIR__ . '/sql/TS-4647_1.sql', $return);
    }
}
