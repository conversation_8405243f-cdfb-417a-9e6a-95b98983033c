<?php

namespace TF\Commands\v2_2;

use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * TS-4145 command run on all databases.
 */
class TS4145_A_Command extends BaseCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v2.2:TS-4145_A')
            ->setDescription('Add a new column start_rko (starting rko number for each user\'s farm) in susi_main user_farmings table');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . DEFAULT_DB_DATABASE . ' -f ' . __DIR__ . '/sql/TS-4145_A.sql', $return);
    }
}
