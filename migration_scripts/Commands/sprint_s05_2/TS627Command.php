<?php

namespace TF\Commands\sprint_s05_2;

use PDO;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * TS-627 command run on all databases.
 */
class TS627Command extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('sprint_s05_2:TS-627')
            ->setDescription('sazdava tablica su_crop_codes v susi_main i q pulni s promenlivite ot GLOBALS["Farming"]["crops"]');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $dbhDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        // CREATE TABLE su_crop_codes
        $sql = $dbhDev->prepare(
            'CREATE TABLE su_crop_codes (
			ID serial PRIMARY KEY,
			crop_code VARCHAR(6) NOT NULL,
			crop_name VARCHAR (100) NOT NULL,
			year INTEGER NOT NULL,
			crop_descr VARCHAR (100),
			general_code BOOLEAN DEFAULT FALSE,
			is_active BOOLEAN DEFAULT FALSE,
			crop_type VARCHAR (100),
			crop_family VARCHAR (100),
			crop_genus VARCHAR (100),
			crop_species VARCHAR (100),
			azot_fixed_crop BOOLEAN DEFAULT FALSE,
			is_intermediate_crop BOOLEAN DEFAULT FALSE,
			is_intermediate_weat_crop BOOLEAN DEFAULT FALSE,
			is_tree_short_rotation BOOLEAN DEFAULT FALSE,
			priority INTEGER,
			azot FLOAT,
			fosfor FLOAT,
			kalii FLOAT,
			no_pndn BOOLEAN DEFAULT FALSE,
			crop_rotation BOOLEAN DEFAULT FALSE
			);'
        );

        $sql->execute();

        // INSERT INTO TABLE su_crop_codes everything from $GLOBALS['Farming']['crops']
        foreach ($GLOBALS['Farming']['crops'] as $key => $value) {
            $sqlInsert = $dbhDev->prepare(
                'INSERT INTO
			 su_crop_codes (crop_code, crop_name, year, crop_descr, general_code, is_active, crop_type, crop_family, crop_genus, crop_species,
			 			azot_fixed_crop, is_intermediate_crop, is_intermediate_weat_crop, is_tree_short_rotation, priority,
			 			azot, fosfor, kalii, no_pndn, crop_rotation)
			 VALUES
		     (:cropcode, :cropname, :year, :cropdescr, :general_code, :is_active, :crop_type, :crop_family, :crop_genus, :crop_species,
		     :azot_fixed_crop, :is_intermediate_crop, :is_intermediate_weat_crop, :is_tree_short_rotation, :priority, :azot, :fosfor, :kalii, :no_pndn, :crop_rotation
		     );'
            );

            $year = 5;
            if ($value['is_active']) {
                $year = 6;
            }
            $crop_descr = mb_strtolower($value['crop_descr'], 'UTF-8');
            $crop_descr = $this->mb_ucfirst($crop_descr);

            $sqlInsert->bindValue(':cropcode', $value['crop_code']);
            $sqlInsert->bindValue(':cropname', $value['crop_name']);
            $sqlInsert->bindValue(':year', $year);
            $sqlInsert->bindValue(':cropdescr', $value['crop_descr'] ? $crop_descr : null);
            $sqlInsert->bindValue(':general_code', $value['general_code'] ? true : false, PDO::PARAM_BOOL);
            $sqlInsert->bindValue(':is_active', $value['is_active'] ? true : false, PDO::PARAM_BOOL);
            $sqlInsert->bindValue(':crop_type', $value['crop_type'] ? $value['crop_type'] : null);
            $sqlInsert->bindValue(':crop_family', $value['crop_family'] ? $value['crop_family'] : null);
            $sqlInsert->bindValue(':crop_genus', $value['crop_genus'] ? $value['crop_genus'] : null);
            $sqlInsert->bindValue(':crop_species', $value['crop_species'] ? $value['crop_species'] : null);
            $sqlInsert->bindValue(':azot_fixed_crop', $value['azot_fixed_crop'] ? true : false, PDO::PARAM_BOOL);
            $sqlInsert->bindValue(':is_intermediate_crop', $value['is_intermediate_crop'] ? true : false, PDO::PARAM_BOOL);
            $sqlInsert->bindValue(':is_intermediate_weat_crop', $value['is_intermediate_weat_crop'] ? true : false, PDO::PARAM_BOOL);
            $sqlInsert->bindValue(':is_tree_short_rotation', $value['is_tree_short_rotation'] ? true : false, PDO::PARAM_BOOL);
            $sqlInsert->bindValue(':priority', $value['priority'] ? $value['priority'] : 0);
            $sqlInsert->bindValue(':azot', $value['azot'] ? $value['azot'] : null);
            $sqlInsert->bindValue(':fosfor', $value['fosfor'] ? $value['fosfor'] : null);
            $sqlInsert->bindValue(':kalii', $value['kalii'] ? $value['kalii'] : null);
            $sqlInsert->bindValue(':no_pndn', $value['no_pndn'] ? true : false, PDO::PARAM_BOOL);
            $sqlInsert->bindValue(':crop_rotation', $value['crop_rotation'] ? true : false, PDO::PARAM_BOOL);

            $sqlInsert->execute();
        }

        $sqlSelectNewCrops = $dbhDev->prepare(
            'SELECT *
		     FROM new_crops'
        );

        $sqlSelectNewCrops->execute();
        $resultsNewCrops = $sqlSelectNewCrops->fetchAll();

        foreach ($resultsNewCrops as $newCrop) {
            $sqlUpdateCropCode = $dbhDev->prepare('UPDATE su_crop_codes SET (azot, fosfor, kalii) = (:azot, :fosfor, :kalii)
		  							WHERE crop_code = :cropcode');

            $azot = str_replace(',', '.', $newCrop['azot']);
            $fosfor = str_replace(',', '.', $newCrop['fosfor']);
            $kalii = str_replace(',', '.', $newCrop['kalii']);

            $sqlUpdateCropCode->bindValue(':azot', $azot ? floatval($azot) : null);
            $sqlUpdateCropCode->bindValue(':fosfor', $fosfor ? floatval($fosfor) : null);
            $sqlUpdateCropCode->bindValue(':kalii', $kalii ? floatval($kalii) : null);
            $sqlUpdateCropCode->bindValue(':cropcode', $newCrop['crop_code']);

            // var_export("Azot = " . $azot . "");
            // var_export("fosfor = " . $fosfor . "");
            // var_export("kalii = " . $kalii . "");

            $sqlUpdateCropCode->execute();
        }
    }

    private function mb_ucfirst($string)
    {
        $strlen = mb_strlen($string);
        $firstChar = mb_substr($string, 0, 1);
        $then = mb_substr($string, 1, $strlen - 1);

        return mb_strtoupper($firstChar) . $then;
    }
}
