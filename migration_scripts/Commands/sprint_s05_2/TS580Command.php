<?php

namespace TF\Commands\sprint_s05_2;

use PDO;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Commands\ParamHelper;

/**
 * TS-580 command run on all databases.
 */
class TS580Command extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('sprint_s05_2:TS-580')
            ->setDescription('Dobavq koloni weat_crops i non_weat_crops kam tablici ot tip "za_isak"')
            ->addArgument(
                'user_databases',
                InputArgument::IS_ARRAY | InputArgument::OPTIONAL,
                'Databases to run the script on (separate multiple names with a space)?'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $user_databases = $input->getArgument('user_databases');

        $paramHelper = new ParamHelper();
        $userDbString = $paramHelper->createQueryParamString($user_databases);

        $userDbConditions = 'true';
        if ($user_databases) {
            $userDbConditions = 'u.database IN (' . $userDbString . ')';
        }

        $dbhDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = $dbhDev->prepare(
            "SELECT ul.table_name,u.database FROM su_users_layers ul
		    INNER JOIN su_users u on u.id = ul.user_id
		    WHERE {$userDbConditions} AND ul.layer_type = 9
		    ORDER BY u.database asc"
        );

        $sql->execute();
        $results = $sql->fetchAll();

        for ($i = 0; $i < count($results); $i++) {
            $output->writeln('database=' . $results[$i]['database'] . '');

            $sql = "ALTER TABLE {$results[$i]['table_name']} ADD COLUMN weat_crops varchar(511);
		            ALTER TABLE {$results[$i]['table_name']} ADD COLUMN non_weat_crops varchar(511);
		            ";

            system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . $results[$i]['database'] . ' -c "' . $sql . '"', $return);
        }
    }
}
