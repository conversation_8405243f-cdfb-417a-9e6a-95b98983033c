<?php

namespace TF\Commands\sprint_s05_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-622 command run on all databases.
 */
class TS622Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s05_2:TS-622')
            ->setDescription('Proverqva na koi bazi lipsva kolonata "payday" v tablica su_contracts i q dobavq');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $dbhDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = $dbhDev->prepare(
            "
	        SELECT COUNT(*) as num FROM information_schema.columns 
	        where table_catalog = :userDb and
	                table_schema = 'public' and
	                table_name = 'su_contracts' and
	                LOWER(column_name)= 'payday'"
        );
        $sql->bindParam(':userDb', $userDb);
        $sql->execute();
        $res = $sql->fetchAll();

        if (0 == $res[0]['num']) {
            $sql = $dbhDev->prepare('ALTER TABLE su_contracts ADD COLUMN payday character varying(255)');
            $sql->execute();
            $output->writeln($userDb . '');
        }
    }
}
