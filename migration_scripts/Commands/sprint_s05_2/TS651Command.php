<?php

namespace TF\Commands\sprint_s05_2;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-651 command run on all databases.
 */
class TS651Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s05_2:TS-651')
            ->setDescription('Sazdava dblink extension na posochenata baza');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $sql = 'CREATE extension dblink;';
        $return = [];
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . $userDb . ' -c "' . $sql . '" 2>&1', $return);
        var_dump($return);
    }
}
