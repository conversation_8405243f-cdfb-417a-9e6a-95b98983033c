<?php

namespace TF\Commands\sprint_s05_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-599 command run on all databases.
 */
class TS599Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s05_2:TS-599')
            ->setDescription('dobavq v tablica su_crop_layers kolona s ime layer_name i q popalva');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $dbhDevDB = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
        $sqlSelect = $dbhDevDB->prepare(
            'SELECT * FROM su_crop_layers ORDER BY id asc'
        );

        $sqlSelect->execute();
        $resultsSelect = $sqlSelect->fetchAll();

        $sql = $dbhDevDB->prepare(
            "SELECT TRUE as result
		 FROM information_schema.columns 
	     WHERE table_name='su_crop_layers' and column_name='layer_name'"
        );

        $sql->execute();
        $existColumn = $sql->fetch();

        if ($existColumn) {
            return;
        }

        $sql = 'ALTER TABLE su_crop_layers ADD COLUMN layer_name varchar(255);';
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . $userDb . ' -c "' . $sql . '"', $return);

        for ($j = 1; $j <= count($resultsSelect); $j++) {
            $result = $resultsSelect[$j - 1]['id'];

            $sqlUpdate = "UPDATE su_crop_layers SET layer_name = 'Референтен слой {$j}' WHERE id = {$result}";
            $returnUpdate = [];
            system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . $userDb . ' -c "' . $sqlUpdate . '" 2>&1', $returnUpdate);
        }
    }
}
