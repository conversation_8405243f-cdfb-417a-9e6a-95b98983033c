<?php

namespace TF\Commands\sprint_s05_2;

use PDO;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Commands\ParamHelper;

/**
 * TS-644 command run on all databases.
 */
class TS644Command extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('sprint_s05_2:TS-644')
            ->setDescription('Vav vsichki potrebitelski bazi danni proverqva v su_users_layers dali ima "VPS - Carski orel i Egipetski leshoqd", ako nqma go dobavq za 2015g. Ako nakoi ot VPS sloevete e za 2014 g. go pravi na 2015g.')
            ->addArgument(
                'user_databases',
                InputArgument::IS_ARRAY | InputArgument::OPTIONAL,
                'Databases to run the script on (separate multiple names with a space)?'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $user_databases = $input->getArgument('user_databases');

        $paramHelper = new ParamHelper();
        $userDbString = $paramHelper->createQueryParamString($user_databases);

        $userDbConditions = 'true';
        if ($user_databases) {
            $userDbConditions = 'u.database IN (' . $userDbString . ')';
        }

        $dbhDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = $dbhDev->prepare(
            "SELECT ul.user_id, count(*)
			FROM su_users_layers ul
			LEFT JOIN su_users u on (u.id = ul.user_id)
			WHERE {$userDbConditions} AND ul.layer_type IN (13, 14, 15, 16, 17)
			AND ul.year IN (5,6) 
			GROUP BY user_id
			HAVING count(*) IN (4, 9)"
        );

        $sql->execute();
        $results = $sql->fetchAll();

        foreach ($results as $result) {
            if (4 == $result['count']) {
                $sqlSelect = $dbhDev->prepare(
                    'SELECT *
					FROM su_users_layers
					WHERE layer_type IN (13)
					AND year = 5 AND user_id = ' . $result['user_id']
                );

                $sqlSelect->execute();
                $resultSelect = $sqlSelect->fetch();

                $sqlInsert = $dbhDev->prepare(
                    "INSERT INTO su_users_layers (user_id, name, table_name, date_created, color, border_color, extent, farming, year,
				    								 transparency, position, layer_type, group_id, tags, border_only)
				     VALUES (:user_id, 'ВПС - Царски орел и Египетски лешояд', 'layer_vps_orli_leshoyadi', :date_created, 'ffffff', 'A900E6', 
		    			:extent, :farming, 6, :transparency, :position, 17, :group_id, :tags, :border_only)"
                );

                $date = date('Y-m-d H:i:s', strtotime($resultSelect['date_created']));
                $sqlInsert->bindValue(':user_id', $resultSelect['user_id']);
                $sqlInsert->bindValue(':date_created', $date);
                $sqlInsert->bindValue(':extent', $resultSelect['extent']);
                $sqlInsert->bindValue(':farming', $resultSelect['farming']);
                $sqlInsert->bindValue(':transparency', $resultSelect['transparency']);
                $sqlInsert->bindValue(':position', $resultSelect['position']);
                $sqlInsert->bindValue(':group_id', $resultSelect['group_id']);
                $sqlInsert->bindValue(':tags', $resultSelect['tags'], PDO::PARAM_BOOL);
                $sqlInsert->bindValue(':border_only', $resultSelect['border_only'], PDO::PARAM_BOOL);

                $sqlInsert->execute();

                $sqlUpdate = $dbhDev->prepare(
                    'UPDATE su_users_layers SET year = 6
		  			WHERE layer_type IN (13, 14, 15, 16)
					AND year = 5 AND user_id = ' . $result['user_id']
                );

                $sqlUpdate->execute();
            } elseif (9 == $result['count']) {
                $sqlDelete = $dbhDev->prepare(
                    'DELETE FROM su_users_layers
		  			WHERE layer_type IN (13, 14, 15, 16)
					AND year = 5 AND user_id = ' . $result['user_id']
                );

                $sqlDelete->execute();
            }
        }
    }
}
