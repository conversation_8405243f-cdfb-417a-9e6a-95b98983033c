<?php

namespace TF\Commands\sprint_s05_2;

use PDO;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * TS-623 command run on all databases.
 */
class TS623Command extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('sprint_s05_2:TS-623')
            ->setDescription('Drop-va starata kolona "status" v su_users_coverage v susi_main; sazdava q s defaultna stoinost 0, kakto i dobavq kolona "errors"');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $dbhDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = $dbhDev->prepare('ALTER TABLE su_users_coverage DROP COLUMN status');
        $sql->execute();

        $sql = $dbhDev->prepare("ALTER TABLE su_users_coverage ADD COLUMN status int2 NOT NULL DEFAULT '0'");
        $sql->execute();

        $sql = $dbhDev->prepare('UPDATE su_users_coverage SET status = 1');
        $sql->execute();

        $sql = $dbhDev->prepare("ALTER TABLE su_users_coverage ADD COLUMN errors text NOT NULL DEFAULT ''");
        $sql->execute();
    }
}
