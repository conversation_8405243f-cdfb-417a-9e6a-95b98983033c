<?php

namespace TF\Commands\sprint_s05_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-595 command run on all databases.
 */
class TS595Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s05-2:TS-595')
            ->setDescription('pravi spravka za klientite sas zaredeni danni v sloi za dopustimost za 2014 ili premahva tablica za "sloi za dopustimost" za godinite >= 2014');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $this->createReport($userDb);

        $this->deleteLayers($userDb);
    }

    private function createReport($userDb)
    {
        $fp = fopen('file.csv', 'w');

        for ($i = 0; $i < count($results); $i++) {
            $dbhDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
            if (5 != $results[$i]['year']) {
                continue;
            }
            $output->writeln('database=' . $results[$i]['database'] . ' ' . $results[$i]['table_name'] . '');

            $sql = $dbhDev->prepare("SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = 'public' AND TABLE_NAME = :table");

            $sql->bindValue(':table', $results[$i]['table_name'], PDO::PARAM_STR);

            $sql->execute();
            $foundTables = $sql->fetchAll();
            if (count($foundTables)) {
                print_r($results[$i]);

                $csvData = [$results[$i]['database'], $results[$i]['table_name']];
                fputcsv($fp, $csvData, ';');
            }
        }
        fclose($fp);
    }

    private function deleteLayers($dbhDev, $userDbConditions)
    {
        $sql = $dbhDev->prepare(
            "SELECT array_agg(ul.id) as ids FROM su_users_layers ul
			INNER JOIN su_users u on u.id = ul.user_id
			WHERE {$userDbConditions} AND ul.layer_type = 3"
        );

        $sql->execute();
        $layers = $sql->fetchAll();

        $sql = $dbhDev->prepare("DELETE FROM su_users_layers WHERE id in ({$layers[0]['ids']}) layer_type = 3 and year >= 5");
        $sql->execute();
    }
}
