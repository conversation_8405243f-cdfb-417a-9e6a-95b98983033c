<?php

namespace TF\Commands\sprint_s05_2;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-602 command run on all databases.
 */
class TS602Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s05_2:TS-602')
            ->setDescription('dobavqne na koloni numerator i denominator kam tablicite su_plots_farming_rel i su_plots_owners_rel');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $sql = 'ALTER TABLE su_plots_owners_rel ADD COLUMN numerator integer, ADD COLUMN denominator integer;';
        $return = [];
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . $userDb . ' -c "' . $sql . '" 2>&1', $return);
        var_dump($return);

        $sql = 'ALTER TABLE su_plots_farming_rel ADD COLUMN numerator integer, ADD COLUMN denominator integer;';
        $return = [];
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . $userDb . ' -c "' . $sql . '" 2>&1', $return);
        var_dump($return);
    }
}
