<?php

namespace TF\Commands\v2_1;

use TF\Commands\Common\UserDbCommand;

/**
 * Runs all scripts required for migration from Sprint 21 to Version 2.1.
 */
class UpdateToVersion2_1 extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v2.1:update-to-version-2.1')
            ->setDescription('Runs all scripts required for migration from Sprint 21 to version 2.1.');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $commandsArr = [
            new TS4262Command(),
            new TS4263Command(),
            new TS4109Command(),
            new TS4010Command(),
        ];

        $count = count($commandsArr);
        for ($i = 0; $i < $count; $i++) {
            $command = $commandsArr[$i];
            $this->logScript($userDb, $command->getName());
            $output->writeln('Executing command: ' . $command->getName());
            $command->onDbExecute($userDb, $output, $input);
        }
    }

    protected function logScript($user_db, $name)
    {
        $date = date('Y-m-d H:i:s');

        $this->openConnection();

        $sql = $this->mainConnection->prepare("
            INSERT INTO scripts_log (db_name, name, date)
            VALUES ('{$user_db}', '{$name}', '{$date}')
        ");

        return $sql->execute();
    }
}
