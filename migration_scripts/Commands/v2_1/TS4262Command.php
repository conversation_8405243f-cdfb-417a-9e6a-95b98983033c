<?php

namespace TF\Commands\v2_1;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-4262 command run on all databases.
 */
class TS4262Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v2.1:TS-4262')
            ->setDescription('Add comment text field to su_contracts_plot_rel table.');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-4262.sql', $return);
    }
}
