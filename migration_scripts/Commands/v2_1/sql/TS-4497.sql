CREATE TYPE subscription_type as ENUM ('map', 'plots', 'agro');

CREATE TABLE su_users_subscriptions (
  id SERIAL PRIMARY KEY,
  user_id INTEGER DEFAULT NULL REFERENCES su_users(id) ON DELETE CASCADE,
  subscription_usage_type subscription_type DEFAULT NULL,
  subscription_usage_id INTEGER DEFAULT NULL,
  unique (user_id, subscription_usage_type)
);

CREATE TABLE su_subscription_usage_map (
  id SERIAL PRIMARY KEY,
  min_plots INTEGER DEFAULT NULL,
  max_plots INTEGER DEFAULT NULL
);

CREATE TABLE su_subscription_usage_plots (
  id SERIAL PRIMARY KEY,
  min_area_dka NUMERIC DEFAULT NULL,
  max_area_dka NUMERIC DEFAULT NULL
);

CREATE TABLE su_subscription_usage_agro (
  id SERIAL PRIMARY KEY,
  with_farmtrack BOOL DEFAULT FALSE
);

INSERT INTO su_subscription_usage_map VALUES (1, 1, 2);
INSERT INTO su_subscription_usage_map VALUES (2, 3, 10);
INSERT INTO su_subscription_usage_map VALUES (3, 11, NULL);

INSERT INTO su_subscription_usage_plots VALUES (1, 0, 5000);
INSERT INTO su_subscription_usage_plots VALUES (2, 5001, 15000);
INSERT INTO su_subscription_usage_plots VALUES (3, 15001, NULL);

INSERT INTO su_subscription_usage_agro VALUES (1, TRUE);
INSERT INTO su_subscription_usage_agro VALUES (2, FALSE);
