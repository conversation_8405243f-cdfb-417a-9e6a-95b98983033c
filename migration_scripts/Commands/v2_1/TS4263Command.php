<?php

namespace TF\Commands\v2_1;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-4263 command run on all databases.
 */
class TS4263Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v2.1:TS-4263')
            ->setDescription('Add block text field to all layer kvs tables');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-4263.sql', $return);
    }
}
