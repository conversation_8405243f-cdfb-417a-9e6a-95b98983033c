<?php

namespace TF\Commands\v2_1;

use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * TS-4153 command run on susi_main database.
 */
class TS4153Command extends BaseCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v2.1:TS-4153')
            ->setDescription('In ' . DEFAULT_DB_DATABASE . ' adds ekatte_count, total_plot_area, paid_support_start_date and paid_support_due_date columns to su_users');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . DEFAULT_DB_DATABASE . ' -f ' . __DIR__ . '/sql/TS-4153.sql', $return);
    }
}
