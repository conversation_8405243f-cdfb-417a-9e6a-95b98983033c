<?php

namespace TF\Commands\v2_1;

use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * TS-4566 command run on susi_main database.
 */
class TS4566Command extends BaseCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v2.1:TS-4566')
            ->setDescription('Updates the structure of the new ' . DEFAULT_DB_DATABASE . '.layer_allowable table.');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . DEFAULT_DB_DATABASE . ' -f ' . __DIR__ . '/sql/TS-4566.sql', $return);
    }
}
