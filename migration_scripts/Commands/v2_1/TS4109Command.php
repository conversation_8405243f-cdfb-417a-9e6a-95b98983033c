<?php

namespace TF\Commands\v2_1;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-4109 command run on all databases.
 */
class TS4109Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v2.1:TS-4109')
            ->setDescription('Adds owner_type column in su_charged_renta_params');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-4109.sql', $return);
    }
}
