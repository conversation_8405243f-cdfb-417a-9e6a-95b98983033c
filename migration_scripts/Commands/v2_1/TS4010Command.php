<?php

namespace TF\Commands\v2_1;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-4010 command run on all databases.
 */
class TS4010Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v2.1:TS-4010')
            ->setDescription('Adds is_closed_for_editing column in su_contracts');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-4010.sql', $return);
    }
}
