<?php

namespace TF\Commands\sprint_s21;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-4104 command run on all databases.
 */
class TS4104Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s21:TS-4104')
            ->setDescription('Adds with_overall_renta column in su_charged_renta_params');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-4104.sql', $return);
    }
}
