<?php

namespace TF\Commands\sprint_s21;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-4240 command run on all databases.
 */
class TS4240Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s21:TS-4240')
            ->setDescription('Remove ending string стопанска година from su_payment_subjects template');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-4240.sql', $return);
    }
}
