<?php

namespace TF\Commands\sprint_s21;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-4246 command run on all databases.
 */
class TS4246Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s21:TS-4246')
            ->setDescription('Remove representative if he is the same as owner');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-4246.sql', $return);
    }
}
