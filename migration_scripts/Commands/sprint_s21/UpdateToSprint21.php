<?php

namespace TF\Commands\sprint_s21;

use TF\Commands\Common\UserDbCommand;

/**
 * Runs all scripts required for migration from Sprint 20 to Sprint 21.
 */
class UpdateToSprint21 extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s21:update-to-sprint21')
            ->setDescription('Runs all scripts required for migration from Sprint 20 to Sprint 21.');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $commandsArr = [
            new TS4240Command(),
            new TS4104Command(),
            new TS4246Command(),
        ];

        $count = count($commandsArr);
        for ($i = 0; $i < $count; $i++) {
            $command = $commandsArr[$i];
            $this->logScript($userDb, $command->getName());
            $output->writeln('Executing command: ' . $command->getName());
            $command->onDbExecute($userDb, $output, $input);
        }
    }

    protected function logScript($user_db, $name)
    {
        $date = date('Y-m-d H:i:s');

        $this->openConnection();

        $sql = $this->mainConnection->prepare("
            INSERT INTO scripts_log (db_name, name, date)
            VALUES ('{$user_db}', '{$name}', '{$date}')
        ");

        return $sql->execute();
    }
}
