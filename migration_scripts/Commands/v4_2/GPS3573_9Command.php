<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS3573_9Command run on client databases.
 *
 * This command will create functions for getting plot/contract title by id
 */
class GPS3573_9Command extends UserDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        // Create function get_plot_category_by_id
        $pdo->exec('CREATE OR REPLACE FUNCTION get_plot_category_by_id(category_id VARCHAR)
            RETURNS VARCHAR IMMUTABLE AS
            $$
                -- Note: If you use this function in virtual column, in order to refresh the calculated value
                -- you need to update the virtual column - for example: UPDATE {table} set {virtual_column_name} = {virtual_column_name}
                DECLARE res VARCHAR;
                BEGIN
                    SELECT 
                        title INTO res
                    FROM
                        public.su_plot_categories
                    WHERE
                        category_id ~ \'^[0-9]+$\'
                        AND id = category_id::INT;

                    IF res IS NULL THEN
                        res := category_id;
                    END IF;

                    RETURN res;
                END;
            $$ LANGUAGE plpgsql;
        ');

        // Create function get_contract_type_by_id
        $pdo->exec('CREATE OR REPLACE FUNCTION get_contract_type_by_id(type_id INT)
            RETURNS VARCHAR IMMUTABLE AS
            $$
                -- Note: If you use this function in virtual column, in order to refresh the calculated value
                -- you need to update the virtual column - for example: UPDATE {table} set {virtual_column_name} = {virtual_column_name}
                DECLARE res VARCHAR;
                BEGIN
                    SELECT 
                        title INTO res
                    FROM
                        public.su_contract_types
                    WHERE
                        id = type_id;

                    RETURN res;
                END;
            $$ LANGUAGE plpgsql;
        ');
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3573-9')
            ->setDescription('Seed tables su_plot_categories and su_contract_types.');
    }
}
