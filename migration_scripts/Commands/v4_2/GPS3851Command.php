<?php

namespace TF\Commands\v4_2;

use Exception;
use PDO;
use TF\Application\Entity\User;
use TF\Application\Entity\UserLayers;
use TF\Commands\Common\MainDbCommand;

class GPS3851Command extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3851')
            ->setDescription('Add column definitions "col_filter_selection_type" in su_users_layers');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        /** @var User[] $users */
        $users = User::finder()->findAll('level = :level', [':level' => '2']);

        try {
            foreach ($users as $user) {
                /** @var UserLayers[] $layers */
                $layers = UserLayers::finder()->findAll('group_id = :group_id ', [':group_id' => $user->group_id]);

                if (!count($layers)) {
                    continue;
                }

                foreach ($layers as $layer) {
                    $definitions = $layer->getDefinitions();
                    $defaultDefinitions = UserLayers::getDefinitionsByType($layer->layer_type);

                    foreach ($definitions as &$definition) {
                        foreach ($defaultDefinitions as $defaultDefinition) {
                            if ($definition['col_name'] === $defaultDefinition['col_name']) {
                                $definition['col_filter_selection_type'] = $defaultDefinition['col_filter_selection_type'] ?? null;
                            }
                        }
                    }

                    $layer->definitions = json_encode($definitions);
                    $layer->save();
                }
            }
        } catch (Exception $e) {
            $output->writeln('<error>' . $e->getMessage() . '</error>');
        }
    }
}
