<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\MainDbCommand;

/**
 * GPS3573_1Command run on main database.
 *
 * This command will create column additional_codes in su_area_types
 */
class GPS3573_1Command extends MainDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec('ALTER TABLE su_area_types ADD COLUMN IF NOT EXISTS additional_codes INT[] DEFAULT ARRAY[]::INT[];');
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3573-1')
            ->setDescription('Create column additional_codes');
    }
}
