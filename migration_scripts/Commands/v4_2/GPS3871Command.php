<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS3871Command run on client databases.
 *
 * This command will remove the last '0' from all ntp code columns having 5 or more digits and ends with '0' in all tables of the client databases.
 */
class GPS3871Command extends UserDbCommand
{
    private $excludeTables = [
        '%layer_work_%',
        '%tmp_%',
    ];

    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $output->warn("\nNOTE: The ntp columns containing characters different from digits will not be updated.");
        $output->writeln('');

        $excludeTablesSql = "'" . implode("', '", $this->excludeTables) . "'";

        $ntpCodeColumnsQuery = "
            SELECT
                JSON_OBJECT_AGG(
                    table_name,
                     JSON_BUILD_OBJECT(
                        'ntp_column', column_name,
                        'id_column', CASE 
                                WHEN table_name = 'layer_kvs' THEN 'gid'
                                WHEN table_name = 'su_osz_files_plots' THEN 'id'
                                ELSE NULL
                            END    
                     )
                )
            FROM
                information_schema.columns
            WHERE
                is_generated = 'NEVER' -- column is not virtual
                AND ( -- column name contains 'ntp' or 'area_type'
                    column_name ilike '%ntp%'
                    OR column_name ilike '%area_type%'
                )
            ";
        $ntpCodeColumnsQuery .= count($this->excludeTables) > 0
            ? "AND NOT table_name ILIKE ANY(ARRAY[{$excludeTablesSql}])"
            : '';

        $ntpCodeColumnsStmt = $pdo->prepare($ntpCodeColumnsQuery);
        $ntpCodeColumnsStmt->execute();
        $ntpCodeColumns = json_decode($ntpCodeColumnsStmt->fetch(PDO::FETCH_COLUMN), true);

        foreach ($ntpCodeColumns as $tableName => $data) {
            $ntpColumn = $data['ntp_column'];
            $idColumn = $data['id_column'];
            $keyColumn = $idColumn ?? $ntpColumn;

            $output->writeln("Updating values for column '{$tableName}.{$ntpColumn}'...");

            $updateNtpCodeQuery = "WITH ntp AS (
                    SELECT 
                        \"{$keyColumn}\",
                        substring(\"{$ntpColumn}\" FROM 1 for (length(\"{$ntpColumn}\") - 1)) AS fixed_ntp
                    FROM 
                        \"{$tableName}\"
                    WHERE 
                        \"{$ntpColumn}\" ~ '^\d{4,}0$' -- 5+ digits number that ends with 0
                )
                UPDATE \"{$tableName}\" t
                SET \"{$ntpColumn}\" = ntp.fixed_ntp
                FROM ntp
                WHERE t.\"{$keyColumn}\" = ntp.\"{$keyColumn}\"
            ";

            $updateNtpCodeStmt = $pdo->prepare($updateNtpCodeQuery);
            $result = $updateNtpCodeStmt->execute();

            if (!$result) {
                $output->error("Failed to update column '{$ntpColumn}' of table '{$tableName}'. Continuing with the next table...");

                continue;
            }

            $output->info('Done!');
        }
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3571')
            ->setDescription("This command will remove the last '0' from all ntp code columns having 5 or more digits and ends with '0' in all tables of the client databases.");
    }
}
