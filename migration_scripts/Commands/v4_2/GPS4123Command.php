<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4123Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-4123')
            ->setDescription('Add \'Неизвестно\' to su_consolidation_zd_jelanie and su_consolidation_zd_kod');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec("INSERT INTO su_consolidation_zd_jelanie (jelanie, value) VALUES (0, 'Неизвестно')");
        $pdo->exec("INSERT INTO su_consolidation_zd_kod (kod, value) VALUES (0, 'Неизвестно')");
    }
}
