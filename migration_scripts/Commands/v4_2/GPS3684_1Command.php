<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS3684_1Command run on user databases.
 *
 * This command will replace special symbols with an underscore
 */
class GPS3684_1Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3684-1')
            ->setDescription('Creating a db function to replace special symbols in user databases.');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $sql = "CREATE OR REPLACE FUNCTION replace_special_chars(input_text text) RETURNS text AS $$
                DECLARE
                    output_text text := input_text;
                    chars_to_replace text[] := array['@', '<', '>', ':', '\"', '/', '\', '|', '?', '*', ';', '$', '&', ',', '[', ']', '(', ')', '.', '{', '}', '``', '''', '~', '§', '±', ' ', '`', '!', '#', '%', '^', '+', '='];
                    replace_with text := '';
                BEGIN
                    -- Replace unwanted characters with an underscore
                    FOR i IN array_lower(chars_to_replace, 1)..array_upper(chars_to_replace, 1) LOOP
                        output_text := replace(output_text, chars_to_replace[i], replace_with);
                    END LOOP;

                    -- Removing tabs
                    output_text := replace(output_text, E'\t', '');

                    RETURN output_text;
                END;
                $$ LANGUAGE plpgsql;";

        $stmt = $pdo->prepare($sql);
        $stmt->execute();
    }
}
