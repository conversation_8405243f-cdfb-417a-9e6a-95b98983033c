<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4036_1Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-4036-1')
            ->setDescription('Create virtual column virtual_contract_status in su_sales_contracts');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec('ALTER TABLE su_sales_contracts ADD COLUMN IF NOT EXISTS virtual_contract_status contract_status_enum GENERATED ALWAYS AS (get_contract_status(active, due_date)) STORED;');
    }
}
