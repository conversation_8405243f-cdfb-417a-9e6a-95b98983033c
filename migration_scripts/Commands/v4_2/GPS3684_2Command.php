<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS3684_2Command run on user databases.
 *
 * This command translate Cyrillic letters to Latin
 */
class GPS3684_2Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3684-2')
            ->setDescription('Create db function to translate Cyrillic letters to Latin in user databases.');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $sql = "CREATE OR REPLACE FUNCTION cyr_to_lat(input_text text) RETURNS text AS $$
                DECLARE
                    output_text text := '';
                    cyr char;
                    lat text;
                BEGIN
                    FOR i IN 1..length(input_text) LOOP
                        cyr := substring(input_text, i, 1);
                        
                        lat := CASE cyr
                            WHEN 'А' THEN 'A' WHEN 'Б' THEN 'B' WHEN 'В' THEN 'V' WHEN 'Г' THEN 'G'
                            WHEN 'Д' THEN 'D' WHEN 'Е' THEN 'E' WHEN 'Ж' THEN 'ZH' WHEN 'З' THEN 'Z'
                            WHEN 'И' THEN 'I' WHEN 'Й' THEN 'Y' WHEN 'К' THEN 'K' WHEN 'Л' THEN 'L'
                            WHEN 'М' THEN 'M' WHEN 'Н' THEN 'N' WHEN 'О' THEN 'O' WHEN 'П' THEN 'P'
                            WHEN 'Р' THEN 'R' WHEN 'С' THEN 'S' WHEN 'Т' THEN 'T' WHEN 'У' THEN 'U'
                            WHEN 'Ф' THEN 'F' WHEN 'Х' THEN 'H' WHEN 'Ц' THEN 'TS' WHEN 'Ч' THEN 'CH'
                            WHEN 'Ш' THEN 'SH' WHEN 'Щ' THEN 'SHT' WHEN 'Ъ' THEN 'A' WHEN 'Ь' THEN 'Y'
                            WHEN 'Ю' THEN 'YU' WHEN 'Я' THEN 'YA' WHEN 'а' THEN 'a' WHEN 'б' THEN 'b'
                            WHEN 'в' THEN 'v' WHEN 'г' THEN 'g' WHEN 'д' THEN 'd' WHEN 'е' THEN 'e'
                            WHEN 'ж' THEN 'zh' WHEN 'з' THEN 'z' WHEN 'и' THEN 'i' WHEN 'й' THEN 'y'
                            WHEN 'к' THEN 'k' WHEN 'л' THEN 'l' WHEN 'м' THEN 'm' WHEN 'н' THEN 'n'
                            WHEN 'о' THEN 'o' WHEN 'п' THEN 'p' WHEN 'р' THEN 'r' WHEN 'с' THEN 's'
                            WHEN 'т' THEN 't' WHEN 'у' THEN 'u' WHEN 'ф' THEN 'f' WHEN 'х' THEN 'h'
                            WHEN 'ц' THEN 'ts' WHEN 'ч' THEN 'ch' WHEN 'ш' THEN 'sh' WHEN 'щ' THEN 'sht'
                            WHEN 'ъ' THEN 'a' WHEN 'ь' THEN 'y' WHEN 'ю' THEN 'yu' WHEN 'я' THEN 'ya'
                            ELSE cyr
                        END;
                        
                        output_text := output_text || lat;
                    END LOOP;
                    
                    RETURN output_text;
                END;
                $$ LANGUAGE plpgsql;
                ";

        $stmt = $pdo->prepare($sql);
        $stmt->execute();
    }
}
