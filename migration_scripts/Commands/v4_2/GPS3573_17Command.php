<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Application\Common\Config;
use TF\Commands\Common\MainDbCommand;

/**
 * GPS3573_17Command run on main database.
 *
 * This command will update the definitions for LAYER_TYPE_VPS_LIVADEN_BLATAR.
 * It will make the column 'ime' visible and the column 'obstina' not visible, not sortable and not copyable.
 */
class GPS3573_17Command extends MainDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $layerType = Config::LAYER_TYPE_VPS_LIVADEN_BLATAR;

        $pdo->exec("WITH 
            layers_definitions AS (
                SELECT
                    sul.id AS layer_id,
                    jsonb_agg(
                        CASE 
                            WHEN d->>'col_name' = 'ime'  THEN d || '{\"col_visible\": true}'::jsonb
                            WHEN d->>'col_name' = 'obstina'  THEN d || '{\"col_visible\": false, \"col_sortable\": false, \"col_copyable\": false}'::jsonb
                            ELSE d
                        END
                    ) AS new_definitions
                FROM 
                    su_users_layers AS sul,
                    jsonb_array_elements(definitions) AS d
                WHERE
                    layer_type = {$layerType}
                GROUP BY
                    sul.id
            )
            UPDATE su_users_layers
            SET
                definitions = new_definitions
            FROM 
                layers_definitions
            WHERE
                id = layer_id
        ");
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3573-17')
            ->setDescription('Update definitions for layer of LAYER_TYPE_VPS_LIVADEN_BLATAR');
    }
}
