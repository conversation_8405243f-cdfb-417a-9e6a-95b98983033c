<?php

namespace TF\Commands\v4_2;

use PDO;
use Symfony\Component\Console\Input\InputInterface;
use TF\Commands\Common\Custom\TFConsoleOutput;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS3869Command run on client databases.
 *
 * This command will  kvs_contracts_update_* mat views exist and if empty, drop them.
 */
class GPS3869Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3869')
            ->setDescription('Check if kvs_contracts_update_* mat views exist and if empty, drop them.');
    }

    protected function onDbExecute(PDO $pdo, TFConsoleOutput $output, InputInterface $input)
    {
        $stmt = $pdo->prepare('SELECT
                matviewname
            FROM
                pg_matviews
            WHERE
                matviewname LIKE \'kvs_contracts_update%\'');
        $stmt->execute();
        $kvsContractsUpdateMatViews = $stmt->fetchAll(PDO::FETCH_COLUMN);

        if (empty($kvsContractsUpdateMatViews)) {
            $output->info('No kvs_contracts_update_* mat views found');

            return;
        }

        foreach ($kvsContractsUpdateMatViews as $matView) {
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM {$matView}");
            $stmt->execute();
            $count = $stmt->fetchColumn();
            $ekatte = str_replace('kvs_contracts_update_', '', $matView);
            if (0 === $count) {
                $output->warn("Dropping {$matView} mat view");
                $pdo->exec("DROP MATERIALIZED VIEW {$matView}");
                $updateStm = $this->mainConnection->prepare("UPDATE su_users_files SET status=1 WHERE ekate='{$ekatte}' and group_id={$this->organizationId}");
                $updateStm->execute();
            } else {
                $output->info("{$matView} mat view is not empty");
            }
        }
    }
}
