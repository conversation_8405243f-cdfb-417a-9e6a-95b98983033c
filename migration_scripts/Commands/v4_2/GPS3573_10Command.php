<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Application\Common\Config;
use TF\Application\Entity\UserLayers;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS3573_10Command run on client databases.
 *
 * This command will create and seed column virtual_category_title to layer_kvs tables
 */
class GPS3573_10Command extends UserDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $kvsLayerDefaultDefinitions = UserLayers::getDefinitionsByType(Config::LAYER_TYPE_KVS);
        [$virtualCategoryTitleDef] = UserLayers::filterDefinitions($kvsLayerDefaultDefinitions, [['col_name' => 'virtual_category_title']]);

        $colName = $virtualCategoryTitleDef['col_name'];
        $columnExpression = $virtualCategoryTitleDef['col_expression'];
        $colCategory = $virtualCategoryTitleDef['col_category'];
        $colType = $GLOBALS['Layers']['columnTypesByCategory'][$colCategory];

        $pdo->exec("ALTER TABLE layer_kvs ADD COLUMN IF NOT EXISTS {$colName} {$colType} GENERATED ALWAYS AS ({$columnExpression}) STORED;");
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3573-10')
            ->setDescription('Create and seed column virtual_category_title to layer_kvs tables');
    }
}
