<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4036Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-4036')
            ->setDescription('Create db function number_of_contracts_by_plot.');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec('CREATE OR REPLACE FUNCTION
            number_of_contracts_by_plot(plot_id INT) RETURNS INT AS 
                $$
                    DECLARE number_of_contracts INT;
                    BEGIN 
                        SELECT
                            count(DISTINCT scpr.*) + count(DISTINCT sublease_contracts.id) + count(DISTINCT sales_contracts.id) INTO number_of_contracts
                        FROM
                            layer_kvs AS kvs
                        LEFT JOIN su_contracts_plots_rel AS scpr
                            ON scpr.plot_id = kvs.gid
                        LEFT JOIN LATERAL (
                            SELECT 
                                sspcr.sublease_id
                            FROM 
                                su_subleases_plots_contracts_rel sspcr
                            WHERE
                                sspcr.pc_rel_id = scpr.id
                        ) AS sublease_contracts(id) ON TRUE
                        LEFT JOIN LATERAL (
                            SELECT 
                                sscpr.contract_id
                            FROM 
                                su_sales_contracts_plots_rel sscpr
                            WHERE
                                sscpr.pc_rel_id = scpr.id
                        ) AS sales_contracts(id) ON TRUE
                        WHERE
                            kvs.gid = number_of_contracts_by_plot.plot_id
                        GROUP BY
                            kvs.gid;
                        
                        RETURN number_of_contracts;
                    END;
            $$ LANGUAGE plpgsql;
        ');
    }
}
