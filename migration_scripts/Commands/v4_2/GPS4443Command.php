<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4443Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-4443')
            ->setDescription('Create db function normalize_value which will round value to 3 decimal places (by default) if it is a numeric value, otherwise return value as is');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec("CREATE OR REPLACE 
            FUNCTION normalize_value(input_value TEXT, fractional_digits INT DEFAULT 3)
            RETURNS TEXT AS $$
            BEGIN
                -- This function rounds the input_value up to the specified number of fractional digits if it's a numeric value and has a fractional part.
                -- or it removes the trailing '.0' if it doesn't have a fractional part. If the input_value is not numeric, it returns the input_value as-is.

                -- Check if the value is numeric
                IF input_value ~ '^-?\d+(\.\d+)?$' THEN
                    -- If numeric, check if it has no fractional part
                    IF (input_value::NUMERIC % 1 = 0) THEN
                        -- Remove trailing '.0' if it's an integer
                        RETURN TRIM(TRAILING '.0' FROM input_value::TEXT);
                    ELSE
                        -- Format with the specified number of fractional digits
                        RETURN TO_CHAR(input_value::NUMERIC, 'FM999999999.' || RPAD('', fractional_digits, '9'));
                    END IF;
                ELSE
                    -- If not numeric, return the input value as-is
                    RETURN input_value;
                END IF;
            END;
            $$ LANGUAGE plpgsql IMMUTABLE;
        ");
    }
}
