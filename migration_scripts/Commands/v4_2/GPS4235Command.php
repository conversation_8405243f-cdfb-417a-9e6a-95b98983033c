<?php

namespace TF\Commands\v4_2;

use PDO;
use Symfony\Component\Console\Input\InputOption;
use TF\Commands\Common\MainDbCommand;

/**
 * GPS4235Command run on main database.
 *
 * This command will add missing area types in su_area_types.
 */
class GPS4235Command extends MainDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $addAdditionalCodes = $input->getOption('add_additional_codes');

        $sql = 'SELECT 
                    id, 
                    title, 
                    cat, 
                    additional_codes
                FROM su_area_types 
                WHERE 
                    id = :code
                    or :code = ANY(additional_codes);';
        $ntpCodeExistsSql = $pdo->prepare($sql);

        if ($addAdditionalCodes) {
            $newCodes = [];
            foreach (self::getNewNTPcodes() as $ntp) {
                $ntpCodeExistsSql->bindValue(':code', $ntp['id']);
                $ntpCodeExistsSql->execute();
                $ntpCodeExists = $ntpCodeExistsSql->fetchAll(PDO::FETCH_ASSOC);

                if ($ntpCodeExists) {
                    if (!empty($ntp['additional_codes'])) {
                        foreach ($ntp['additional_codes'] as $additionalCode) {
                            $ntpCodeExistsSql->bindValue(':code', $additionalCode);
                            $ntpCodeExistsSql->execute();
                            $additionalCodeExists = $ntpCodeExistsSql->fetchAll(PDO::FETCH_ASSOC);

                            if (empty($additionalCodeExists)) {
                                if (!array_key_exists($ntp['id'], $newCodes)) {
                                    $newCodes[$ntp['id']] = [
                                        'id' => $additionalCode,
                                        'title' => $ntp['title'],
                                        'cat' => $ntp['cat'],
                                        'additional_codes' => [],
                                    ];
                                } else {
                                    $newCodes[$ntp['id']]['additional_codes'][] = $additionalCode;
                                }
                                $output->writeln('Code:' . $ntp['id'] . ' Additional code: ' . $additionalCode . ' can be added, because it is not exists');
                            }
                        }
                    }
                }
            }

            if ($newCodes) {
                foreach ($newCodes as $newCode) {
                    $pdo->exec("INSERT INTO su_area_types (id, title, cat, additional_codes) VALUES ({$newCode['id']}, '{$newCode['title']}', '{$newCode['cat']}', '{" . implode(', ', $newCode['additional_codes']) . "}'::INT[]);");

                    $output->writeln("INSERT INTO su_area_types (id, title, cat, additional_codes) VALUES ({$newCode['id']}, '{$newCode['title']}', '{$newCode['cat']}', '{" . implode(', ', $newCode['additional_codes']) . "}'::INT[]);");
                }
            }
        } else {
            $counter = 1;
            foreach (self::getNewNTPcodes() as $ntp) {
                $ntpCodeExistsSql->bindValue(':code', $ntp['id']);
                $ntpCodeExistsSql->execute();
                $ntpCodeExists = $ntpCodeExistsSql->fetchAll(PDO::FETCH_ASSOC);

                if ($ntpCodeExists) {
                    foreach ($ntpCodeExists as $ntpCode) {
                        $output->writeln($counter . " : NTP code {$ntp['id']} already exists in su_area_types. INFO: id: " . $ntpCode['id'] . ', title: ' . $ntpCode['title'] . ', additional_codes: ' . $ntpCode['additional_codes']);
                        $counter++;
                    }

                    continue;
                }
                $pdo->exec("INSERT INTO su_area_types (id, title, cat, additional_codes) VALUES ({$ntp['id']}, '{$ntp['title']}', '{$ntp['cat']}', '{" . implode(', ', $ntp['additional_codes']) . "}'::INT[]);");

                $output->writeln("NTP code {$ntp['id']} is successful added in su_area_types. INFO: id: " . $ntp['id'] . ', title: ' . $ntp['title'] . ', additional_codes: ' . implode(', ', $ntp['additional_codes']));
            }
        }
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-4235')
            ->setDescription('Add missing area types in su_area_types. Use option - a true in order check if some of additional codes not exists and add it as well.')
            ->addOption('add_additional_codes', '-a', InputOption::VALUE_OPTIONAL, 'Check if some of additional codes not exist and add it as new code.');
    }

    private static function getNewNTPcodes()
    {
        return [
            ['id' => 1100, 'cat' => 2, 'title' => 'За обект комплекс за здравеопазване', 'additional_codes' => []],
            ['id' => 1110, 'cat' => 2, 'title' => 'За обект комплекс за образование', 'additional_codes' => []],
            ['id' => 1120, 'cat' => 2, 'title' => 'За обект комплекс за култура и изкуство', 'additional_codes' => []],
            ['id' => 1130, 'cat' => 2, 'title' => 'За обект комплекс за социални грижи', 'additional_codes' => []],
            ['id' => 1140, 'cat' => 2, 'title' => 'За административна сграда, комплекс', 'additional_codes' => []],
            ['id' => 1150, 'cat' => 2, 'title' => 'За обект за детско заведение', 'additional_codes' => []],
            ['id' => 1200, 'cat' => 2, 'title' => 'Незастроен имот за обществена сграда, комплекс', 'additional_codes' => []],
            ['id' => 1210, 'cat' => 2, 'title' => 'За друг обществен обект, комплекс', 'additional_codes' => [3200]],
            ['id' => 1300, 'cat' => 3, 'title' => 'Обществен селищен парк, градина', 'additional_codes' => [3410, 3411, 3419]],
            ['id' => 1310, 'cat' => 3, 'title' => 'Обществен извънселищен парк, горски парк', 'additional_codes' => [7130]],
            ['id' => 1320, 'cat' => 3, 'title' => 'Дендрариум', 'additional_codes' => []],
            ['id' => 1330, 'cat' => 3, 'title' => 'Ботаническа градина', 'additional_codes' => [3433]],
            ['id' => 1400, 'cat' => 4, 'title' => 'Стадион', 'additional_codes' => [7310, 7351, 7360, 7371, 7372, 7373]],
            ['id' => 1410, 'cat' => 4, 'title' => 'За спортна зала', 'additional_codes' => [7320]],
            ['id' => 1420, 'cat' => 4, 'title' => 'Спортно игрище', 'additional_codes' => [3431, 7330, 7340]],
            ['id' => 1430, 'cat' => 4, 'title' => 'За трасета за вело- и мотоспорт', 'additional_codes' => [7370, 7379]],
            ['id' => 1500, 'cat' => 4, 'title' => 'За други видове спорт', 'additional_codes' => [7300, 7380, 7389, 7390]],
            ['id' => 1600, 'cat' => 5, 'title' => 'За вилна сграда', 'additional_codes' => [7210, 7211]],
            ['id' => 1610, 'cat' => 5, 'title' => 'За земеделски труд и отдих (съгласно § 4 ПЗРЗСПЗЗ)', 'additional_codes' => []],
            ['id' => 1700, 'cat' => 6, 'title' => 'За електроенергийното производство', 'additional_codes' => [3311]],
            ['id' => 1710, 'cat' => 6, 'title' => 'За топлоенергийното производство', 'additional_codes' => [3312]],
            ['id' => 1720, 'cat' => 6, 'title' => 'За друго производство на продукти от нефт, въглища, газ, шисти', 'additional_codes' => [3310, 3313, 3319]],
            ['id' => 1900, 'cat' => 6, 'title' => 'За животновъдна ферма', 'additional_codes' => [1611, 1612, 1613, 2224]],
            ['id' => 1910, 'cat' => 6, 'title' => 'Незастроен имот за производствен, складов обект', 'additional_codes' => []],
            ['id' => 1920, 'cat' => 6, 'title' => 'За друг вид производствен, складов обект', 'additional_codes' => [1500, 1530, 1590, 1600, 2350, 2390, 3300, 3360, 3369, 3380]],
            ['id' => 2100, 'cat' => 8, 'title' => 'За първостепенна улица', 'additional_codes' => [3511]],
            ['id' => 2110, 'cat' => 8, 'title' => 'За второстепенна улица', 'additional_codes' => [3512, 3513]],
            ['id' => 2120, 'cat' => 8, 'title' => 'За алея', 'additional_codes' => [3514]],
            ['id' => 2200, 'cat' => 8, 'title' => 'За автомагистрала', 'additional_codes' => [6111]],
            ['id' => 2210, 'cat' => 8, 'title' => 'За път от републиканската пътна мрежа', 'additional_codes' => [6112, 6113, 6114]],
            ['id' => 2220, 'cat' => 8, 'title' => 'За местен път', 'additional_codes' => [6115, 6120]],
            ['id' => 2230, 'cat' => 8, 'title' => 'За селскостопански, горски, ведомствен път', 'additional_codes' => [1700, 1710, 1720, 2222, 2310, 6130]],
            ['id' => 2240, 'cat' => 8, 'title' => 'За автогара, автоспирка', 'additional_codes' => [6140]],
            ['id' => 2250, 'cat' => 8, 'title' => 'За бензиностанция, газостанция, метанстанция', 'additional_codes' => [3020]],
            ['id' => 2260, 'cat' => 8, 'title' => 'За летище, аерогара', 'additional_codes' => [1910, 6211, 6212]],
            ['id' => 2290, 'cat' => 8, 'title' => 'За пристанище', 'additional_codes' => [6410, 6411, 6412]],
            ['id' => 2300, 'cat' => 8, 'title' => 'За въжена линия', 'additional_codes' => [6510, 6511, 6512]],
            ['id' => 2310, 'cat' => 8, 'title' => 'За друг поземлен имот за движение и транспорт', 'additional_codes' => [3500, 3510, 3519, 3520, 3529, 6100, 6180, 6190, 6200, 6210, 6219, 6300, 6310, 6319, 6400, 6490, 6500, 6519, 6900]],
            ['id' => 2400, 'cat' => 9, 'title' => 'За съоръжение на водопровод', 'additional_codes' => [6610]],
            ['id' => 2410, 'cat' => 9, 'title' => 'За съоръжение на канализация', 'additional_codes' => [6620]],
            ['id' => 2420, 'cat' => 9, 'title' => 'За съоръжение на електропровод', 'additional_codes' => [6630]],
            ['id' => 2430, 'cat' => 9, 'title' => 'За съоръжение на нефтопровод', 'additional_codes' => [6641]],
            ['id' => 2440, 'cat' => 9, 'title' => 'За съоръжение на газопровод', 'additional_codes' => [6642]],
            ['id' => 2900, 'cat' => 14, 'title' => 'Иглолистна гора', 'additional_codes' => [2111]],
            ['id' => 3020, 'cat' => 15, 'title' => 'Просека', 'additional_codes' => [2223, 2320]],
            ['id' => 3100, 'cat' => 16, 'title' => 'Водно течение, река', 'additional_codes' => [4100, 4110, 4111]],
            ['id' => 3110, 'cat' => 16, 'title' => 'Езеро', 'additional_codes' => [4211]],
            ['id' => 3120, 'cat' => 16, 'title' => 'Блато', 'additional_codes' => [4212]],
            ['id' => 3130, 'cat' => 16, 'title' => 'Мочурище', 'additional_codes' => [8500]],
            ['id' => 3190, 'cat' => 16, 'title' => 'Напоителен канал', 'additional_codes' => [4511]],
            ['id' => 3200, 'cat' => 16, 'title' => 'Отводнителен канал', 'additional_codes' => [4512]],
            ['id' => 3300, 'cat' => 16, 'title' => 'Черноморски териториални води', 'additional_codes' => [4300]],
            ['id' => 3400, 'cat' => 17, 'title' => 'Резерват', 'additional_codes' => [7110]],
            ['id' => 3410, 'cat' => 17, 'title' => 'Природна забележителност', 'additional_codes' => [4113, 7140, 7141, 7142, 7143, 7144, 7145, 7146, 7147, 7149]],
            ['id' => 3420, 'cat' => 17, 'title' => 'Поддържан резерват', 'additional_codes' => []],
            ['id' => 3430, 'cat' => 17, 'title' => 'Защитена местност', 'additional_codes' => []],
            ['id' => 3431, 'cat' => 17, 'title' => 'Природен парк', 'additional_codes' => []],
            ['id' => 3432, 'cat' => 17, 'title' => 'Национален парк', 'additional_codes' => []],
            ['id' => 3500, 'cat' => 18, 'title' => 'За добив на руди', 'additional_codes' => [5211, 5212]],
            ['id' => 3510, 'cat' => 18, 'title' => 'За добив на въглища', 'additional_codes' => [5213, 5214]],
            ['id' => 3520, 'cat' => 18, 'title' => 'За добив на нерудни полезни изкопаеми', 'additional_codes' => [5215, 5216]],
            ['id' => 3900, 'cat' => 22, 'title' => 'Скали', 'additional_codes' => [8100]],
            ['id' => 4100, 'cat' => 22, 'title' => 'Поземлен имот с недефиниран начин на трайно ползване', 'additional_codes' => [9999]],
        ];
    }
}
