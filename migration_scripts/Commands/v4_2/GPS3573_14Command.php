<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS3573_14Command run on client databases.
 *
 * This command will add column farming_name to su_contracts table
 */
class GPS3573_14Command extends UserDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec('ALTER TABLE su_contracts ADD COLUMN IF NOT EXISTS farming_name VARCHAR;');
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3573-14')
            ->setDescription('Add column farming_name to su_contracts table.');
    }
}
