<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS3675_4Command run on client databases.
 *
 * This command will cluster the layer_kvs_borders table by layer_kvs_borders_geom_idx
 */
class GPS3675_4Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3675-4')
            ->setDescription('Cluster the layer_kvs_borders table by layer_kvs_borders_geom_idx');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        // Cluster the table by the spatial index
        $pdo->exec('CLUSTER layer_kvs_borders USING layer_kvs_borders_geom_idx');

        // Analyze the table to update statistics
        $pdo->exec('ANALYZE layer_kvs_borders');
    }
}
