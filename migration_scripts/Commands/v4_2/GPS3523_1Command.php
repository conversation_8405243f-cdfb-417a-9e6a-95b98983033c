<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Application\Common\Config;
use TF\Commands\Common\MainDbCommand;

/**
 * GPS3523_1Command run on susi_main database.
 *
 * This command will remove invalid ekatte keys from the style column (su_users_layer table) for kvs layers
 */
class GPS3523_1Command extends MainDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $kvsLayerType = Config::LAYER_TYPE_KVS;

        $pdo->exec("WITH invalid_kvs_styles AS 
            (
                SELECT
                    sul.id AS layer_id,
                    sul.table_name,
                    array_agg(kvs.ekatte_code) FILTER (WHERE se.id isnull) AS invalid_ekattes
                FROM 
                    su_users_layers AS sul,
                    json_each(sul.\"style\") AS kvs(ekatte_code, \"style\")
                LEFT JOIN su_ekatte AS se
                    ON se.ekatte_code = kvs.ekatte_code
                WHERE
                    layer_type = {$kvsLayerType}
                GROUP BY
                    sul.id
                HAVING
                    jsonb_agg(kvs.ekatte_code) FILTER (WHERE se.id isnull) NOTNULL		
            )
            UPDATE su_users_layers AS sul
            SET \"style\" = (\"style\"::jsonb - invalid_ekattes)::json
            FROM invalid_kvs_styles iks
            WHERE
                sul.id = iks.layer_id
        ");
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3523-1')
            ->setDescription('Remove invalid ekatte keys from the style column (su_users_layer table) for kvs layers.');
    }
}
