<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\MainDbCommand;

class GPS3719Command extends MainDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $this->executeSql($pdo, __DIR__ . '/sql/GPS3719.sql');
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3719')
            ->setDescription('Add error column in scripts_log table.');
    }
}
