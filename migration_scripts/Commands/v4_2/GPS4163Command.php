<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4163Command extends UserDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $this->executeSql($pdo, __DIR__ . '/sql/GPS4163.sql');
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-4163')
            ->setDescription('Add declared_area_status_enum ');
    }
}
