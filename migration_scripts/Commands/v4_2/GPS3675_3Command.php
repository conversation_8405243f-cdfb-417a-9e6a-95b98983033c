<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS3675_3Command run on client databases.
 *
 * This command will create indexes in table layer_kvs_borders
 */
class GPS3675_3Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3675-3')
            ->setDescription('Create indexes in table layer_kvs_borders');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec('CREATE INDEX IF NOT EXISTS layer_kvs_borders_ekatte_idx ON layer_kvs_borders USING btree (ekatte)');
        $pdo->exec('CREATE INDEX IF NOT EXISTS layer_kvs_borders_geom_idx ON layer_kvs_borders USING gist (geom)');
    }
}
