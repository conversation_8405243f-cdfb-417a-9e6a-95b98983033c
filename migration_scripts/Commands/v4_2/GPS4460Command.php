<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\MainDbCommand;

class GPS4460Command extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-4460')
            ->setDescription('Set col_personalizable to false for fill and border color definitions');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec("WITH 
            layers_definitions AS (
                SELECT
                    sul.id AS layer_id,
                    jsonb_agg(
                        CASE 
                            WHEN d->>'col_name' = 'fill_color'  THEN d || '{\"col_personalizable\": false}'::jsonb
                            WHEN d->>'col_name' = 'border_color'  THEN d || '{\"col_personalizable\": false}'::jsonb
                            ELSE d
                        END
                    ) AS new_definitions
                FROM 
                    su_users_layers AS sul,
                    jsonb_array_elements(definitions) AS d
                WHERE
                    sul.definitions::jsonb @> '[{\"col_name\": \"fill_color\"}]'::jsonb
                    AND sul.definitions::jsonb @> '[{\"col_name\": \"border_color\"}]'::jsonb
                GROUP BY
                    sul.id
            )
            UPDATE su_users_layers
            SET
                definitions = new_definitions
            FROM 
                layers_definitions
            WHERE
                id = layer_id");
    }
}
