<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4172_1Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-4172-1')
            ->setDescription('Create new tables su_layer_style and su_layers_styles_column_colors');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        // Create table su_layer_styles
        $pdo->exec("create type layer_styles_type AS ENUM ('by attribute', 'single')");

        $pdo->exec('create table if not exists su_layer_styles (
            id serial4 primary key,
            layer_id varchar(255) unique not null,
            table_name varchar(255) not null,
            type layer_styles_type not null,
            transparency int not null,
            fill_column_name varchar(255),
            fill_color varchar(255),
            border_column_name varchar(255),
            border_color varchar(255) not null,
            border_width int not null,
            border_only bool not null default false,
            labels text[] not null,
            label_size int not null,
            tags bool not null,
            created_at timestamp default current_timestamp NOT NULL,
            updated_at timestamp default current_timestamp NOT NULL
            )');

        $pdo->exec("COMMENT ON COLUMN su_layer_styles.fill_column_name IS 'The attribute column used for fill coloring by attribute. This value should be used in the personalization window for preselecting the dropdown for columns.';");
        $pdo->exec("COMMENT ON COLUMN su_layer_styles.fill_color IS 'The color used for single-color fill. This value should be used for showing which color was used for single coloring';");
        $pdo->exec("COMMENT ON COLUMN su_layer_styles.border_column_name IS 'he attribute column used for border coloring by attribute. This value should be used in the personalization window for preselecting the dropdown for columns.';");
        $pdo->exec("COMMENT ON COLUMN su_layer_styles.border_color IS 'The color used for single-color border. This value should be used for showing which color was used for single coloring.';");

        $pdo->exec('CREATE INDEX IF NOT EXISTS su_layer_styles_layer_id_idx ON su_layer_styles USING btree (layer_id)');

        // Create table su_layers_styles_column_colors
        $pdo->exec(
            'create table if not exists su_layers_styles_column_colors (
            id serial4 primary key,
            style_layers_id int not null,
            layer_type int not null,
            name varchar(255) not null,
            value varchar(255),
            category varchar(255) not null, 
            color varchar(255) not null,
            created_at timestamp default current_timestamp NOT NULL,
            updated_at timestamp default current_timestamp NOT NULL
            )'
        );

        $pdo->exec('CREATE INDEX IF NOT EXISTS su_layers_styles_column_colors_style_layers_id_idx ON su_layers_styles_column_colors USING btree (style_layers_id)');
        $pdo->exec('CREATE INDEX IF NOT EXISTS su_layers_styles_column_colors_category_idx ON su_layers_styles_column_colors USING btree (category)');

        $pdo->exec('ALTER TABLE su_layers_styles_column_colors
                                ADD CONSTRAINT fk_su_layer_styles_id
                                FOREIGN KEY (style_layers_id) REFERENCES su_layer_styles(id)
                                ON DELETE CASCADE;
                                ');
    }
}
