<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Application\Common\Config;
use TF\Commands\Common\MainDbCommand;

class GPS4441Command extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-4441')
            ->setDescription('Set column category to in layer definitions for columns with name \'category\'');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $categoryType = Config::LAYER_COLUMN_CATEGORY_CATEGORY;
        $pdo->exec("WITH 
            layers_definitions AS (
                SELECT
                    sul.id AS layer_id,
                    jsonb_agg(
                        CASE 
                            WHEN d->>'col_name' = 'category'  THEN d || '{\"col_category\": \"{$categoryType}\"}'::jsonb
                            ELSE d
                        END
                    ) AS new_definitions
                FROM 
                    su_users_layers AS sul,
                    jsonb_array_elements(definitions) AS d
                WHERE
                    sul.definitions::jsonb @> '[{\"col_name\": \"category\"}]'::jsonb
                GROUP BY
                    sul.id
            )
            UPDATE su_users_layers
            SET
                definitions = new_definitions
            FROM 
                layers_definitions
            WHERE
                id = layer_id");
    }
}
