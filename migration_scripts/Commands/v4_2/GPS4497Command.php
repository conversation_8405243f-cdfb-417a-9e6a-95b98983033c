<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Application\Common\Config;
use TF\Application\Entity\LayerStyles;
use TF\Application\Entity\User;
use TF\Application\Entity\UserLayers;
use TF\Commands\Common\UserDbCommand;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

class GPS4497Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-4497')
            ->setDescription('Migration to update colors for CSD vies where style is by attribute');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $user = User::finder()->find('database = :database and level = :level', [':database' => $this->userDbName, ':level' => '2']);
        $this->initAuthUser($user->id);

        if (empty($user->group_id)) {
            $output->writeln('No user found for this database. Skipping...');

            return;
        }

        $UserDbController = new UserDbController($this->userDbName);
        /** @var UserLayers[] $csdUserLayers */
        $csdUserLayers = UserLayers::finder()->findAll('group_id = (:groupId) and layer_type = :csdLayerType', [':groupId' => $user->group_id, ':csdLayerType' => Config::LAYER_TYPE_CSD]);

        foreach ($csdUserLayers as $layer) {
            if ($layer->isRemote()) {
                // Skip remote layers
                continue;
            }

            $tableName = $layer->table_name;
            $output->write("Update colors for CSD view in table '{$tableName}'");

            [$style] = $layer->getStyles();

            if (LayerStyles::SINGLE_COLORING_TYPE === $style->type) {
                $output->writeln('Skip single coloring type');

                continue;
            }

            $csdColumnDefinitions = $layer->getDefinitions();
            [$fillColumnDefinition] = UserLayers::filterDefinitions($csdColumnDefinitions, [['col_name' => $style->fill_column_name]]);
            [$borderColumnDefinition] = UserLayers::filterDefinitions($csdColumnDefinitions, [['col_name' => $style->border_column_name]]);

            $labelSql = $style->generateMapLabelSQL($layer);
            $modifiedString = str_replace('layer_decl_69_70_', '', $tableName);
            [$ekatte, $farmYear] = explode('_', $modifiedString);
            $coloringOptions = [
                'fill_column_definition' => $fillColumnDefinition,
                'border_column_definition' => $borderColumnDefinition,
                'label' => $labelSql,
                'style_layers_id' => $style->id,
            ];

            $UserDbController->createCsdMatView($layer->table_name, $ekatte, $farmYear, $coloringOptions, LayerStyles::BY_ATTRIBUTE_COLORING_TYPE);

            $output->writeln('Done');
        }
    }
}
