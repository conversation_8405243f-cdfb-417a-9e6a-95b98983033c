<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS2633Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-2633')
            ->setDescription('Create db functions in_utm(geom GEOMETRY) and simplify_geom(geom GEOMETRY, tolerance DOUBLE PRECISION)');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec('CREATE OR REPLACE FUNCTION in_utm(geom GEOMETRY)
            RETURNS BOOLEAN AS $$
            BEGIN
            -- Check if the SRID of the geometry is within the UTM range for the northern hemisphere (32601-32660)
            -- or the southern hemisphere (32701-32760)
            RETURN (ST_SRID(geom) BETWEEN 32601 AND 32660) OR (ST_SRID(geom) BETWEEN 32701 AND 32760);
            END;
            $$ LANGUAGE plpgsql IMMUTABLE;
        ');

        $pdo->exec("CREATE OR REPLACE FUNCTION simplify_geom(geom GEOMETRY, tolerance DOUBLE PRECISION)
            RETURNS GEOMETRY AS $$
            DECLARE
                src_srid INT;
            BEGIN
                SELECT ST_SRID(geom) INTO src_srid;
                IF in_utm(geom) OR src_srid = 0 THEN
                    -- Simplify geometry in the original SRID
                    RETURN CASE 
                        WHEN ST_GeometryType(geom) LIKE 'ST_Multi%'
                            THEN ST_Multi(ST_SimplifyPreserveTopology(geom, tolerance))
                            ELSE ST_SimplifyPreserveTopology(geom, tolerance)
                        END;
                ELSE
                    -- Transform geometry to Web Mercator (EPSG:3857) before simplifying and then back to the original SRID
                    RETURN CASE 
                        WHEN ST_GeometryType(geom) LIKE 'ST_Multi%'
                            THEN ST_Multi(ST_Transform(ST_SimplifyPreserveTopology(ST_Transform(geom, 3857), tolerance), src_srid))
                            ELSE ST_Transform(ST_SimplifyPreserveTopology(ST_Transform(geom, 3857), tolerance), src_srid)
                        END;       
                   
                END IF;
            END;
            $$ LANGUAGE plpgsql IMMUTABLE;
        ");
    }
}
