<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS3758_1Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3758_1')
            ->setDescription('Add indexes for kad_ident and kad_no in layer_kvs and su_osz_files_plots');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec('CREATE INDEX IF NOT EXISTS layer_kvs_kad_ident_idx ON layer_kvs USING hash (kad_ident)');
        $pdo->exec('CREATE INDEX IF NOT EXISTS su_osz_files_plots_kad_no_idx ON su_osz_files_plots USING hash (kad_no)');
    }
}
