<?php

namespace TF\Commands\v4_2;

use Exception;
use PDO;
use TF\Application\Common\Config;
use TF\Application\Entity\UserLayers;
use TF\Commands\Common\MainDbCommand;

class GPS4172_5Command extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-4172-5')
            ->setDescription('Add new column fill_color, border_color and label in susi_main db layers tables');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $layerTypes = [
            Config::LAYER_TYPE_LFA,
            Config::LAYER_TYPE_NATURA_2000,
            Config::LAYER_TYPE_PERMANETELY_GREEN_AREAS,
            Config::LAYER_TYPE_VPS_PASISHTA,
            Config::LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI,
            Config::LAYER_TYPE_VPS_GASKI_ZIMNI,
            Config::LAYER_TYPE_VPS_LIVADEN_BLATAR,
            Config::LAYER_TYPE_VPS_ORLI_LESHOYADI,
        ];

        $layerTypesDefaultBorderColor = [
            Config::LAYER_TYPE_LFA => '#40c080',
            Config::LAYER_TYPE_NATURA_2000 => '#a0a000',
            Config::LAYER_TYPE_PERMANETELY_GREEN_AREAS => '#00f000',
            Config::LAYER_TYPE_VPS_PASISHTA => '#a70000',
            Config::LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI => '#ffa41c',
            Config::LAYER_TYPE_VPS_GASKI_ZIMNI => '#e54100',
            Config::LAYER_TYPE_VPS_LIVADEN_BLATAR => '#a86c0F',
            Config::LAYER_TYPE_VPS_ORLI_LESHOYADI => '#a900e6',
        ];

        $layerTypesDefaultLabel = [
            Config::LAYER_TYPE_LFA => "(COALESCE(\"nm_lfa_e_1\", '')::TEXT)",
            Config::LAYER_TYPE_NATURA_2000 => "(COALESCE(\"name_bg\", '')::TEXT || '^' || COALESCE(\"sitecode\", '')::TEXT)",
            Config::LAYER_TYPE_PERMANETELY_GREEN_AREAS => "(COALESCE(\"imotcode\", '')::TEXT)",
            Config::LAYER_TYPE_VPS_PASISHTA => null,
            Config::LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI => "(COALESCE(\"name\", '')::TEXT)",
            Config::LAYER_TYPE_VPS_GASKI_ZIMNI => "(COALESCE(\"name\", '')::TEXT)",
            Config::LAYER_TYPE_VPS_LIVADEN_BLATAR => "(COALESCE(\"ime\", '')::TEXT)",
            Config::LAYER_TYPE_VPS_ORLI_LESHOYADI => "(COALESCE(\"blockuin\", '')::TEXT || '^' || COALESCE(\"ekatte\", '')::TEXT)",
        ];

        $layerTypesStr = implode(', ', $layerTypes);
        /** @var UserLayers[] $layers */
        $layers = UserLayers::finder()->findAllBySql(
            "select table_name, layer_type from su_users_layers where layer_type IN ({$layerTypesStr}) group by table_name, layer_type"
        );

        try {
            foreach ($layers as $layer) {
                $tableName = $layer->table_name;

                $output->write("Add columns fill_color, border_color and label in table '{$tableName}'...");

                $defaultBorderColor = $layerTypesDefaultBorderColor[$layer->layer_type];
                $pdo->exec("ALTER TABLE {$tableName} ADD COLUMN IF NOT EXISTS fill_color VARCHAR(255) DEFAULT NULL;");
                $pdo->exec("ALTER TABLE {$tableName} ADD COLUMN IF NOT EXISTS border_color VARCHAR(255) NOT NULL DEFAULT '{$defaultBorderColor}'::VARCHAR;");

                $label = $layerTypesDefaultLabel[$layer->layer_type];
                if ($label) {
                    $pdo->exec("ALTER TABLE {$tableName} ADD COLUMN IF NOT EXISTS label VARCHAR(255) DEFAULT null");
                    $pdo->exec("UPDATE {$tableName} set label = {$label}; ");
                }

                $output->writeln("\tDone!");
            }
        } catch (Exception $e) {
            $output->writeln("\tError: {$e->getMessage()}");
        }
    }
}
