<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS3573_13Command run on main database.
 *
 * This command will create enum 'contract_status_enum', function 'get_contract_status_by_id' and virtual column 'virtual_contract_status' in su_contracts table
 */
class GPS3573_13Command extends UserDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        // Create enum for contract statuses
        $pdo->exec("CREATE TYPE contract_status_enum AS ENUM ('Active', 'Expired', 'Canceled')");

        // Create function for getting contract status
        $pdo->exec("CREATE OR REPLACE FUNCTION get_contract_status_by_id(contract_id INT) RETURNS contract_status_enum IMMUTABLE AS $$
            DECLARE
                contract_status contract_status_enum;
            BEGIN
                SELECT
                    CASE 
                        WHEN active = FALSE THEN 'Canceled'::contract_status_enum
                        WHEN active = TRUE AND (COALESCE(due_date, '9999-01-01'))::date >= now()::date THEN 'Active'::contract_status_enum
                        ELSE 'Expired'::contract_status_enum
                    END INTO contract_status
                FROM
                    su_contracts
                WHERE
                    id = contract_id;

                RETURN contract_status;
            END;
            $$ LANGUAGE plpgsql
        ");

        // Create column virtual_contract_status in su_contracts
        $pdo->exec('ALTER TABLE su_contracts ADD COLUMN virtual_contract_status contract_status_enum GENERATED ALWAYS AS (get_contract_status_by_id(id)) STORED;');
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3573-13')
            ->setDescription("Create enum 'contract_status_enum', function 'get_contract_status_by_id' and virtual column 'virtual_contract_status' in su_contracts table.");
    }
}
