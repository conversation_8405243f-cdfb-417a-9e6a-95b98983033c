<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\MainDbCommand;

class GPS3552Command extends MainDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $this->executeSql($pdo, __DIR__ . '/sql/GPS3552.sql');
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3552')
            ->setDescription('Add flag generate transactions');
    }
}
