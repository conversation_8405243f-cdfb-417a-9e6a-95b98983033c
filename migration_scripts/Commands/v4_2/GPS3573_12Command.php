<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS3573_12Command run on main database.
 *
 * This command will create column virtual_contract_type in su_contracts table
 */
class GPS3573_12Command extends UserDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec('ALTER TABLE su_contracts ADD COLUMN IF NOT EXISTS virtual_contract_type VARCHAR GENERATED ALWAYS AS (get_contract_type_by_id(nm_usage_rights)) STORED;');
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3573-12')
            ->setDescription('Create column virtual_contract_type in su_contracts.');
    }
}
