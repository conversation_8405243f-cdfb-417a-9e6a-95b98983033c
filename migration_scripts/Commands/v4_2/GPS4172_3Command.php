<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Application\Common\Config;
use TF\Commands\Common\UserDbCommand;
use TF\Engine\Kernel\StringHelper;

class GPS4172_3Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-4172-3')
            ->setDescription('Migration to fill su_layer_style and user layers database with fill_color and border_color');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $systemLayerTypes = [
            Config::LAYER_TYPE_LFA,
            Config::LAYER_TYPE_NATURA_2000,
            Config::LAYER_TYPE_PERMANETELY_GREEN_AREAS,
            Config::LAYER_TYPE_VPS_PASISHTA,
            Config::LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI,
            Config::LAYER_TYPE_VPS_GASKI_ZIMNI,
            Config::LAYER_TYPE_VPS_LIVADEN_BLATAR,
            Config::LAYER_TYPE_VPS_ORLI_LESHOYADI,
        ];
        $systemLayerTypesString = implode(',', $systemLayerTypes);

        $stringHelper = new StringHelper();
        $randomFillColor = $stringHelper->randomColorCode();
        $borderWidth = Config::LAYER_BOUNDARY_DEFAULT_BORDER_WIDTH;
        $transparency = Config::LAYER_COLOR_DEFAULT_TRANSPARENCY;
        $labelSize = Config::LAYER_LABEL_DEFAULT_SIZE;
        $defaultBorderColor = Config::LAYER_BOUNDARY_DEFAULT_COLOR;

        $layerTypeKVS = Config::LAYER_TYPE_KVS;
        $dbLink = 'host=' . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD;

        // Fill su_layer_styles with data from su_users_layers for non kvs layers
        $pdo->exec("
            WITH existing_style_ids AS (
                select layer_id
                FROM su_layer_styles
            ),
            style_data AS (
                SELECT *
                    FROM dblink(
                        '{$dbLink}',
                        $$
                            SELECT sul.id,
                                sul.table_name, 
                                CASE WHEN sul.layer_type NOT IN({$systemLayerTypesString}) THEN CONCAT('#',COALESCE((sul.style->>'color'), '{$randomFillColor}')) ELSE null END as fill_color, 
                                CASE WHEN sul.layer_type NOT IN({$systemLayerTypesString}) THEN CONCAT('#',COALESCE((sul.style->>'border_color'), '{$defaultBorderColor}')) ELSE CONCAT('#',sul.border_color) END as border_color, 
                                COALESCE((sul.style->>'border_width')::int, {$borderWidth}::int) as border_width, 
                                CASE WHEN sul.layer_type NOT IN({$systemLayerTypesString}) THEN COALESCE((sul.style->>'border_only')::bool, false) ELSE true END as border_only, 
                                COALESCE((sul.style->>'transparency')::int, {$transparency}) as transparency, 
                                ARRAY(SELECT label from json_array_elements_text(sul.style->'label_name') AS label WHERE label is not null )::text[] as labels, 
                                COALESCE((sul.style->>'label_size')::int, {$labelSize}) as label_size,
                                COALESCE((sul.style->>'tags')::bool, false) as tags
                            FROM su_users_layers as sul 
                            JOIN su_users AS su ON sul.group_id = su.group_id AND su.level = 2
                            WHERE sul.layer_type <> {$layerTypeKVS}
                            AND su.database = '{$this->userDbName}'
                        $$
                    ) AS remote_data(layer_id varchar(255), table_name varchar(255), fill_color varchar(255), border_color varchar(255), border_width INT, border_only BOOL, transparency INT, labels TEXT[], label_size INT, tags BOOL)
                    WHERE remote_data.layer_id NOT IN (SELECT * FROM existing_style_ids)
            )
            INSERT INTO su_layer_styles (layer_id, table_name, type, fill_color, border_color, border_width, border_only, transparency, labels, label_size, tags)
            SELECT 
                layer_id, table_name, 'single'::layer_styles_type, fill_color, border_color, border_width, border_only, transparency, labels, label_size, tags
            FROM style_data;
        ");

        // Fill su_layer_styles with data from su_users_layers for kvs layers
        $pdo->exec("
            WITH existing_style_ids AS (
                select layer_id
                FROM su_layer_styles
            ),
            style_data AS (
                SELECT *
                    FROM dblink(
                        '{$dbLink}',
                        $$
                            SELECT concat(sul.id, '_', kvs.ekatte) as layer_id,
                                sul.table_name,
                                CONCAT('#', COALESCE((kvs.style->>'color'), '{$randomFillColor}')) as fill_colour,
                                CONCAT('#', COALESCE((kvs.style->>'border_color'), '{$defaultBorderColor}')) as border_color,
                                COALESCE((kvs.style->>'border_width')::int, {$borderWidth}) as border_width,
                                COALESCE((kvs.style->>'border_only')::bool, false) as border_only,
                                COALESCE((kvs.style->>'transparency')::int, {$transparency}) as transparency,
                                ARRAY(SELECT label from json_array_elements_text(sul.style->'label_name') AS label WHERE label is not null )::text[] as labels, 
                                COALESCE((kvs.style->>'label_size')::int, {$labelSize}) as label_size,
                                COALESCE((kvs.style->>'tags')::bool, false) as tags
                            FROM su_users_layers sul
                            JOIN su_users AS su ON sul.group_id = su.group_id AND su.level = 2
                            JOIN LATERAL jsonb_each(sul.style::jsonb) AS kvs(ekatte, \"style\") ON true
                            WHERE sul.layer_type = {$layerTypeKVS}
                            AND su.database = '{$this->userDbName}'
                        $$
                    ) AS remote_data(layer_id varchar(255), table_name varchar(255), fill_color varchar(255), border_color varchar(255), border_width INT, border_only BOOL, transparency INT, labels TEXT[], label_size INT, tags BOOL)
                    WHERE remote_data.layer_id NOT IN (SELECT * FROM existing_style_ids)
            )
            INSERT INTO su_layer_styles (layer_id, table_name, type, fill_color, border_color, border_width, border_only, transparency, labels, label_size, tags)
            SELECT 
                layer_id, table_name, 'single'::layer_styles_type, fill_color, border_color, border_width, border_only, transparency,labels,label_size,tags
            FROM style_data;
        ");

        // Fill users db columns fill_color and border_color with data from su_users_layers diff from layer_kvs
        $pdo->exec("
            DO \$do$
                DECLARE
                    rec RECORD;
                BEGIN
                    FOR rec IN
                        SELECT *
                        FROM dblink(
                            '{$dbLink}',
                            '
                                SELECT sul.id,
                                    sul.table_name, 
                                    CONCAT(''#'', COALESCE((sul.style->>''color''), ''{$randomFillColor}'')) as fill_color, 
                                    CONCAT(''#'', COALESCE((sul.style->>''border_color''), ''{$defaultBorderColor}'')) as border_color
                                FROM su_users_layers AS sul 
                                JOIN su_users AS su ON sul.group_id = su.group_id AND su.level = 2
                                WHERE sul.layer_type <> {$layerTypeKVS}
                                    AND su.database = ''{$this->userDbName}''
                                    AND sul.is_exist = true
                            '
                        ) AS remote_data(layer_id varchar(255), table_name varchar(255), fill_color varchar(255), border_color varchar(255))
                    LOOP
                        -- Check if the table exists
                        IF EXISTS (
                            SELECT 1
                            FROM pg_catalog.pg_tables
                            WHERE schemaname = 'public' -- Променете, ако таблицата е в друга схема
                            AND tablename = rec.table_name
                        ) THEN
                            -- Dynamic UPDATE for each table if it exists
                            EXECUTE format('
                                UPDATE %I
                                SET fill_color = %L,
                                    border_color = %L
                            ', rec.table_name, rec.fill_color, rec.border_color);
                        END IF;
                    END LOOP;
                END \$do$;
        ");

        // Fill layer_kvs columns fill_color and border_color with data from su_users_layers
        $pdo->exec("
            WITH style_data AS (
                SELECT *
                FROM dblink(
                    '{$dbLink}',
                    $$
                        SELECT kvs.ekatte,
                            CONCAT('#', COALESCE((kvs.style->>'color'), '{$randomFillColor}')) AS fill_color, 
                            CONCAT('#', COALESCE((kvs.style->>'border_color'), '{$defaultBorderColor}')) AS border_color
                        FROM su_users_layers AS sul 
                        JOIN su_users AS su ON sul.group_id = su.group_id AND su.level = 2
                        JOIN LATERAL jsonb_each(sul.style::jsonb) AS kvs(ekatte, \"style\") ON true
                        WHERE sul.layer_type = {$layerTypeKVS}
                            AND su.database = '{$this->userDbName}'
                            AND sul.is_exist = true
                    $$
                ) AS remote_data(ekatte varchar(255),  fill_color varchar(255), border_color varchar(255))
            )
            UPDATE layer_kvs
            SET fill_color = sd.fill_color, border_color = sd.border_color
            FROM style_data sd
            WHERE ekate = sd.ekatte
        ");
    }
}
