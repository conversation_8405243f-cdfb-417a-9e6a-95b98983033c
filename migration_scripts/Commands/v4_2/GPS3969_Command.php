<?php

namespace TF\Commands\v4_2;

use DateTime;
use Exception;
use PDO;
use PDOException;
use Prado\Prado;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Application\Entity\RequestedEkatte;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

class GPS3969_Command extends BaseCommand
{
    private $currentOrganizationDBName = '';
    private $currentOrganizationDBInstance;
    private $userDbController;

    protected function configure()
    {
        $this
            ->setName('v4.2:GPS-3969')
            ->setDescription('Replace kvs contracts update view with new one');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $mainDB = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $organizations = $this->getRequestedEkattes($input, $mainDB);

        $output->writeln('Find ' . count($organizations) . ' organizations');

        foreach ($organizations as $key => $organization) {
            $output->writeln('Processing organization with database: ' . $organization['database']);

            try {
                $this->currentOrganizationDBInstance = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $organization['database'] . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
                $this->currentOrganizationDBName = $organization['database'];
                $this->userDbController = new UserDbController($organization['database']);

                $dateUploaded = new DateTime($organization['date_uploaded']);
                $ekatte = $organization['ekatte_code'];

                $viewName = "kvs_contracts_update_{$ekatte}";
                $tmpTableName = "layer_tmp_kvs_{$ekatte}";
                $kvsContractsViewExists = $this->getMaterializedViewExists($viewName);

                // remove view and tmp table if exists
                if ($kvsContractsViewExists) {
                    // drop the view so if it is not empty, it will be recreated
                    $dropViewSql = "DROP MATERIALIZED VIEW IF EXISTS {$viewName}";
                    $this->currentOrganizationDBInstance->exec($dropViewSql);

                    $dropTmpTableSql = "DROP TABLE IF EXISTS {$tmpTableName}";
                    $this->currentOrganizationDBInstance->exec($dropTmpTableSql);

                    $LayersController = new LayersController('Layers');
                    $LayersController->setFilesProcessingStatus($organization['file_id'], SUCCESSFULLY_TREATED);
                }

                // update archived plots with edit_date = date of upload
                $this->updateArchivedPlots($dateUploaded->format('Y-m-d'), $ekatte);

                // request new ekatte
                $storeModule = Prado::getApplication()->getModule('kvsStoreModule');
                $response = $storeModule->requestEkatte($organization['ekatte_code']);

                if (!$response['uuid']) {
                    throw new Exception('Missing requested ekatte uuid');
                }

                $requestedEkatte = new RequestedEkatte();
                $requestedEkatte->user_id = $organization['group_id'];
                $requestedEkatte->group_id = $organization['group_id'];
                $requestedEkatte->ekatte_code = $organization['ekatte_code'];
                $requestedEkatte->ekatte_name = $organization['ekatte_name'];
                $requestedEkatte->status = RequestedEkatte::STATUS_REQUESTED;
                $requestedEkatte->kvs_store_uuid = $response['uuid'];
                $requestedEkatte->save();
            } catch (PDOException $PDOException) {
                if (7 !== $PDOException->getCode()) {
                    $output->writeln('ERROR :' . $PDOException->getMessage());
                }

                continue;
            }

            $viewEkattes = $this->getMaterializedViews('kvs_contracts_update_%');
            foreach ($viewEkattes as $viewName) {
                $dropViewSql = "DROP MATERIALIZED VIEW IF EXISTS {$viewName}";
                $this->currentOrganizationDBInstance->exec($dropViewSql);

                $ekatte = str_replace('kvs_contracts_update_', '', $viewName);
                $this->userDbController->createKvsContractsUpdateView(null, $ekatte, null);
            }
        }
    }

    private function getMaterializedViewExists($viewName): bool
    {
        $sql = 'SELECT 1 FROM pg_matviews WHERE matviewname = :view_name';

        $cmd = $this->currentOrganizationDBInstance->prepare($sql);
        $cmd->bindParam(':view_name', $viewName);
        $cmd->execute();

        return $cmd->fetchColumn() ? true : false;
    }

    private function updateArchivedPlots($editDate, $ekatte): void
    {
        $cmd = $this->currentOrganizationDBInstance->prepare('
            UPDATE layer_kvs
            SET is_edited = false, edit_date = null, edit_active_from = null
            WHERE 
                is_edited = true
                AND ekate = :ekatte
                AND DATE(edit_date) = DATE(:edit_date)
        ');

        $cmd->bindParam(':ekatte', $ekatte);
        $cmd->bindValue(':edit_date', $editDate);

        $cmd->execute();

        $cmd->fetchAll(PDO::FETCH_ASSOC);
    }

    private function getRequestedEkattes(InputInterface $input, $mainDb)
    {
        $sql = $mainDb->prepare(
            "SELECT DISTINCT ON (ekatte_code, group_id)
                suf.id as file_id,
                suf.date_uploaded,
                su.database,
                sur.*
            FROM su_requested_ekattes sur
            INNER JOIN su_users_files suf ON suf.group_id = sur.group_id and shape_type = '18' and suf.ekate = sur.ekatte_code AND DATE(suf.date_uploaded) = DATE(sur.updated_at)
            INNER JOIN su_users su ON su.group_id = sur.group_id
            WHERE
                ekatte_code IN ('68134', '80371', '16924', '00697', '00816', '02107', '02810', '06690', '11853', '14026',
                      '14684', '15309', '16122', '17470', '17680', '21097', '22407', '22602', '22736', '24367',
                      '30898', '34028', '39668', '39699', '39743', '39921', '43428', '43726', '44063', '51161',
                      '52180', '55021', '55823', '56201', '58129', '61813', '63427', '66460', '68045', '68850',
                      '73345', '73420', '73540', '81791', '86043', '87223')
                AND kvs_store_uuid is not null      
            "
        );
        $sql->execute();

        return $sql->fetchAll(PDO::FETCH_ASSOC);
    }

    private function getMaterializedViews($pattern): array
    {
        $sql = 'SELECT matviewname FROM pg_matviews WHERE matviewname ILIKE :pattern';
        $cmd = $this->currentOrganizationDBInstance->prepare($sql);
        $cmd->bindParam(':pattern', $pattern);
        $cmd->execute();

        return $cmd->fetchAll(PDO::FETCH_COLUMN);
    }
}
