<?php

namespace TF\Commands\v4_2;

use TF\Commands\Common\UserDbCommand;

/**
 * Runs all scripts required for migration from Version 4.1 to 4.2.
 */
class UpdateToVersion4_2 extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:update-to-version-4.2')
            ->setDescription('Runs all scripts required for migration from version 4.1 to version 4.2');
    }

    protected function onDbExecute($pdo, $output, $input)
    {
        $commandsArr = [
            new GPS3523Command(),
            new GPS3523_1Command(),
            new GPS3523_2Command(),
            new GPS3573Command(),
            new GPS3573_1Command(),
            new GPS3573_2Command(),
            new GPS3573_3Command(),
            new GPS3573_4Command(),
            new GPS3573_5Command(),
            new GPS3573_6Command(),
            new GPS3573_7Command(),
            new GPS3573_8Command(),
            new GPS3573_9Command(),
            new GPS3573_10Command(),
            new GPS3573_11Command(),
            new GPS3573_12Command(),
            new GPS3573_13Command(),
            new GPS3573_14Command(),
            new GPS3573_15Command(),
            new GPS3573_16Command(),
            new GPS3573_17Command(),
            new GPS3573_18Command(),
            new GPS3573_19Command(),
            new GPS3636Command(),
            new GPS3675Command(),
            new GPS3675_1Command(),
            new GPS3675_2Command(),
            new GPS3675_3Command(),
            new GPS3675_4Command(),
            new GPS3675_5Command(),
            new GPS3702_1Command(),
            new GPS3702_2Command(),
            new GPS3719Command(),
            new GPS3684_1Command(),
            new GPS3684_2Command(),
            new GPS3684_3Command(),
            new GPS3684_4Command(),
            new GPS3684_5Command(),
            new GPS3684_6Command(),
            new GPS3758_1Command(),
            new GPS2633Command(),
            new GPS2633_1Command(),
            new GPS3848Command(),
            new GPS3851Command(),
            new GPS3871Command(),
            new GPS4052Command(),
            new GPS3987Command(),
            new GPS3869Command(),
            new GPS4036Command(),
            new GPS4036_1Command(),
            new GPS4014Command(),
            new GPS4040Command(),
            new GPS2906Command(),
            new GPS4123Command(),
            new GPS4179Command(),
            new GPS4184Command(),
            new GPS4441Command(),
            new GPS4443Command(),
        ];

        $count = count($commandsArr);
        for ($i = 0; $i < $count; $i++) {
            $command = $commandsArr[$i];
            $this->logScript($this->userDbName, $command->getName());
            $output->writeln('Executing command: ' . $command->getName());
            $command->onDbExecute($pdo, $output, $input);
        }
    }

    protected function logScript($user_db, $name = '', $status = 'success')
    {
        $date = date('Y-m-d H:i:s');

        $this->openConnection();

        $sql = $this->mainConnection->prepare("
            INSERT INTO scripts_log (db_name, name, date)
            VALUES ('{$user_db}', '{$name}', '{$date}')
        ");

        return $sql->execute();
    }
}
