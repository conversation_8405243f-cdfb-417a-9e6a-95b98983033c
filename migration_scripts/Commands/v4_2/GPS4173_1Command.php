<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Application\Common\Config;
use TF\Commands\Common\MainDbCommand;

class GPS4173_1Command extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-4173-1')
            ->setDescription('Create new table su_default_colors');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $cropType = Config::LAYER_COLUMN_CATEGORY_CROP;
        $contractType = Config::LAYER_COLUMN_CATEGORY_CONTRACT;
        $legalRightsType = Config::LAYER_COLUMN_CATEGORY_LEGAL_RIGHTS;
        $categoryType = Config::LAYER_COLUMN_CATEGORY_CATEGORY;

        $pdo->exec("create type default_color_categories AS ENUM ('{$cropType}', '{$contractType}', '{$legalRightsType}', '{$categoryType}')");

        $pdo->exec('create table if not exists su_default_colors (
                id serial4 primary key,
                category default_color_categories not null,
                value varchar(255),
                color varchar(255) not null,
                created_at timestamp default current_timestamp NOT NULL,
                updated_at timestamp default current_timestamp NOT NULL
            )');

        $pdo->exec('CREATE INDEX IF NOT EXISTS su_default_colors_category_idx ON su_default_colors USING btree (category)');

        $this->executeSql($pdo, __DIR__ . '/sql/GPS4173.sql');
    }
}
