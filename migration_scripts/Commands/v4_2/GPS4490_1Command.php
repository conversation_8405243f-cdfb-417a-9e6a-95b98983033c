<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Application\Common\Config;
use TF\Commands\Common\MainDbCommand;

class GPS4490_1Command extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-4490-1')
            ->setDescription('Update definition for layer type CSD. Change col_category for declared_area_status definition');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $csdType = Config::LAYER_TYPE_CSD;
        $csdDecalredAreaStatusType = Config::LAYER_COLUMN_CATEGORY_DECLARED_AREA_STATUS;
        $pdo->exec("WITH 
            layers_definitions AS (
                SELECT
                    sul.id AS layer_id,
                    jsonb_agg(
                        CASE 
                            WHEN d->>'col_name' = 'declared_area_status'  THEN d || '{\"col_category\": \"{$csdDecalredAreaStatusType}\"}'::jsonb
                            ELSE d
                        END
                    ) AS new_definitions
                FROM 
                    su_users_layers AS sul,
                    jsonb_array_elements(definitions) AS d
                WHERE
                    sul.layer_type = '{$csdType}'
                GROUP BY
                    sul.id
            )
            UPDATE su_users_layers
            SET
                definitions = new_definitions
            FROM 
                layers_definitions
            WHERE
                id = layer_id");
    }
}
