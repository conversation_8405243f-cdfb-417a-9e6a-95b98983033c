<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS3573_13Command run on main database.
 *
 * This command will create enum 'contract_status_enum', function 'get_contract_status_by_id' and virtual column 'virtual_contract_status' in su_contracts table
 */
class GPS3881Command extends UserDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        // Create function for getting contract status
        $pdo->exec("CREATE OR REPLACE FUNCTION get_contract_status(active boolean, due_date timestamp without time zone) RETURNS contract_status_enum IMMUTABLE AS $$       
            DECLARE
                contract_status contract_status_enum;
            BEGIN
                contract_status := CASE 
                    WHEN active = FALSE THEN 'Canceled'::contract_status_enum
                    WHEN active = TRUE AND (COALESCE(due_date, '9999-01-01'))::date >= now()::date THEN 'Active'::contract_status_enum
                    ELSE 'Expired'::contract_status_enum
                END;

                RETURN contract_status;
            END;
            $$ LANGUAGE plpgsql
        ");

        $pdo->exec('ALTER TABLE su_contracts DROP COLUMN IF EXISTS virtual_contract_status;');

        // Create column virtual_contract_status in su_contracts
        $pdo->exec('ALTER TABLE su_contracts ADD COLUMN virtual_contract_status contract_status_enum GENERATED ALWAYS AS (get_contract_status(active, due_date)) STORED;');

        $pdo->exec('DROP FUNCTION IF EXISTS get_contract_status_by_id;');
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3881')
            ->setDescription("Create function 'get_contract_status'and alter virtual column 'virtual_contract_status' in su_contracts table.");
    }
}
