<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Application\Common\Config;
use TF\Application\Entity\UserLayers;
use TF\Commands\Common\UserDbCommand;

class GPS4346_2Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-4346-2')
            ->setDescription('Add virtual_ekatte_name column in layer_kvs table');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        /** @var array $tableDefinitions Source table definitions */
        $definitions = UserLayers::getDefinitionsByType(Config::LAYER_TYPE_KVS);
        [$virtualEkatteDefinition] = UserLayers::filterDefinitions($definitions, [['col_name' => 'virtual_ekatte_name']]);
        $colType = $GLOBALS['Layers']['columnTypesByCategory'][$virtualEkatteDefinition['col_category']];
        $colName = $virtualEkatteDefinition['col_name'];

        $colType = $colType . ' GENERATED ALWAYS AS (' . $virtualEkatteDefinition['col_expression'] . ') STORED';
        $pdo->exec("ALTER TABLE layer_kvs ADD COLUMN {$colName} {$colType}");
    }
}
