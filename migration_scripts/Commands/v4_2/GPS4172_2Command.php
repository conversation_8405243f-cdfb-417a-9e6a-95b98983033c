<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Application\Common\Config;
use TF\Application\Entity\User;
use TF\Application\Entity\UserLayers;
use TF\Commands\Common\UserDbCommand;

class GPS4172_2Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-4172-2')
            ->setDescription('Add new columns fill_color, border_color and label in all user layers tables');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $users = User::finder()->findAll('database = :database and level = :level', [':database' => $this->userDbName, ':level' => '2']);
        $userGroupIds = array_map(fn ($user) => $user->group_id, $users);

        if (empty($userGroupIds)) {
            $output->writeln('No users found for this database. Skipping...');

            return;
        }

        $userGroupIdsStr = implode(', ', $userGroupIds);

        /** @var UserLayers[] $userLayers */
        $userLayers = UserLayers::finder()->findAll("group_id IN ({$userGroupIdsStr})");

        $existingTablesSql = 'SELECT table_name FROM information_schema.tables';
        $existingTablesStmt = $pdo->query($existingTablesSql);
        $existingTables = $existingTablesStmt->fetchAll(PDO::FETCH_COLUMN);

        foreach ($userLayers as $userLayer) {
            $tableName = $userLayer->table_name;

            if ($userLayer->isRemote()) {
                // Skip remote layers
                continue;
            }

            if (!in_array($tableName, $existingTables)) {
                // Skip tables that do not exist
                continue;
            }

            $output->write("Add columns fill_color, border_color and label in table '{$tableName}'...");

            $defaultBorderColor = Config::LAYER_BOUNDARY_DEFAULT_COLOR;
            $pdo->exec("ALTER TABLE {$tableName} ADD COLUMN IF NOT EXISTS fill_color VARCHAR(255) DEFAULT NULL;");
            $pdo->exec("ALTER TABLE {$tableName} ADD COLUMN IF NOT EXISTS border_color VARCHAR(255) NOT NULL DEFAULT '{$defaultBorderColor}'::VARCHAR;");
            $pdo->exec("ALTER TABLE {$tableName} ADD COLUMN IF NOT EXISTS label VARCHAR(255) DEFAULT NULL;");

            $output->writeln("\tDone!");
        }
    }
}
