<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS3675_5Command run on client databases.
 *
 * This command will change get_geom_boundary function
 */
class GPS3969_1Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-36969-1')
            ->setDescription('Create db function get_geom_boundary');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec("CREATE OR REPLACE FUNCTION public.get_geom_boundary(geom geometry, simplify integer DEFAULT NULL)
            RETURNS geometry
            LANGUAGE plpgsql
            AS $$
            DECLARE
                res GEOMETRY;
                simplified GEOMETRY;
                exterior_rings GEOMETRY;
                common_srid INTEGER := 32635; -- Define a common SRID
                geom_dump RECORD;
            BEGIN
                -- Reproject the input geometry to the common SRID if necessary
                IF ST_SRID(geom) != common_srid THEN
                    geom := ST_Transform(geom, common_srid);
                END IF;

                -- Initialize exterior_rings with an empty geometry in the correct SRID
                exterior_rings := ST_SetSRID('MULTILINESTRING EMPTY'::geometry, common_srid);

                IF ST_GeometryType(geom) = 'ST_Polygon' THEN 
                    res := ST_MakePolygon(ST_ExteriorRing(geom));
                ELSIF ST_GeometryType(geom) = 'ST_MultiPolygon' THEN
                    FOR geom_dump IN 
                        SELECT (ST_Dump(geom)).geom AS geom_part 
                        LOOP
                            -- Ensure each part is transformed to the common SRID
                            IF ST_SRID(geom_dump.geom_part) != common_srid THEN
                                geom_dump.geom_part := ST_Transform(geom_dump.geom_part, common_srid);
                            END IF;

                            exterior_rings := ST_Union(exterior_rings, ST_MakePolygon(ST_ExteriorRing(geom_dump.geom_part)));
                        END LOOP;
                    res := exterior_rings;
                ELSE
                    res := geom;
                END IF;

                IF simplify IS NULL THEN
                    RETURN res;
                END IF;

                simplified := ST_Simplify(res, simplify);

                IF simplified IS NULL THEN
                    RETURN res;
                END IF;

                RETURN simplified;
            END
            $$;
        ");
    }
}
