<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4184Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-4184')
            ->setDescription('Drop virtual_contract_status column from su_contracts and su_sales_contracts');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec('ALTER TABLE su_contracts DROP COLUMN IF EXISTS virtual_contract_status');
        $pdo->exec('ALTER TABLE su_sales_contracts DROP COLUMN IF EXISTS virtual_contract_status');
    }
}
