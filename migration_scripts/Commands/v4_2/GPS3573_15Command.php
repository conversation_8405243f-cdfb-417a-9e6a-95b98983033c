<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS3573_15Command run on client database.
 *
 * This command will create function fill_su_contracts_farming_name and triggers tr_fill_farming_name_on_update, tr_fill_farming_name_on_insert
 */
class GPS3573_15Command extends UserDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $dbLink = 'host=' . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD;
        $pdo->exec("CREATE OR REPLACE FUNCTION fill_su_contracts_farming_name()
            RETURNS TRIGGER AS
            $$
            BEGIN
                SELECT \"name\" INTO NEW.farming_name
                FROM 
                    public.dblink(
                        '{$dbLink}',
                        'SELECT \"name\" FROM su_users_farming WHERE id =' || NEW.farming_id
                    ) AS farming(\"name\" VARCHAR);
            
                -- Return the NEW row with updated farming_name
                RETURN NEW;
            END;
            $$
            LANGUAGE plpgsql;
        ");

        $pdo->exec('CREATE OR REPLACE TRIGGER tr_fill_farming_name_on_update
            BEFORE UPDATE OF farming_id ON su_contracts
            FOR EACH ROW
            EXECUTE FUNCTION fill_su_contracts_farming_name();
        ');

        $pdo->exec('CREATE OR REPLACE TRIGGER tr_fill_farming_name_on_insert
            BEFORE INSERT ON su_contracts
            FOR EACH ROW
            EXECUTE FUNCTION fill_su_contracts_farming_name();
        ');
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3573-15')
            ->setDescription('Create function fill_su_contracts_farming_name and triggers tr_fill_farming_name_on_update, tr_fill_farming_name_on_insert');
    }
}
