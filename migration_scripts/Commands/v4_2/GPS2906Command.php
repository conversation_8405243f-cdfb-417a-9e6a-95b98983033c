<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS-2906 command run on all databases.
 */
class GPS2906Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-2906')
            ->setDescription('Add an origin_contract_id column to `subleases_view` to show the origin contract.');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $stmt = $pdo->prepare('
            CREATE OR REPLACE VIEW public.subleases_view
            AS SELECT c.id AS sublease_contract_id,
                jsonb_agg(c2.id) AS origin_contract_id,
                c.c_num AS sublease_c_num,
                c.start_date::date AS sublease_start_date,
                c.due_date::date AS sublease_due_date,
                cpr.plot_id,
                max(spa.contract_area) AS sublease_contract_area,
                sum(sacpr.contract_area_for_sale) AS contract_area_for_sale,
                sum(cpr.contract_area)::numeric(10,3) AS contract_area,
                c.renta,
                c.payday,
                c.nm_usage_rights,
                c.farming_id,
                c.active,
                max(COALESCE(spa.rent_area::double precision, spa.contract_area))::numeric(10,3) AS rent_area,
                max(spa.comment::text) AS comment,
                ssc.start_date AS sales_contract_start_date
            FROM su_contracts c
                LEFT JOIN su_subleases_plots_contracts_rel scpr ON scpr.sublease_id = c.id
                LEFT JOIN su_contracts_plots_rel cpr ON cpr.id = scpr.pc_rel_id
                LEFT JOIN su_contracts c2 ON c2.id = cpr.contract_id AND c2.active = true
                LEFT JOIN su_subleases_plots_area spa ON c.id = spa.sublease_id AND spa.plot_id = cpr.plot_id
                LEFT JOIN su_sales_contracts_plots_rel sacpr ON sacpr.pc_rel_id = cpr.id
                LEFT JOIN su_sales_contracts ssc ON ssc.id = sacpr.sales_contract_id
            WHERE true AND c.is_sublease = true
            GROUP BY c.id, cpr.plot_id, ssc.id
            HAVING count(
                    CASE
                        WHEN c2.id IS NULL THEN 1
                        ELSE NULL::integer
                    END) = 0
            ORDER BY c.id, cpr.plot_id;
        ');
        $stmt->execute();
    }
}
