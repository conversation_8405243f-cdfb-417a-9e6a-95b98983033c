<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS3573Command run on client databases.
 *
 * This command will create the views su_consolidation_zd_info and su_consolidation_do_info
 */
class GPS3573Command extends UserDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec('CREATE OR REPLACE VIEW su_consolidation_zd_info AS
            SELECT
                scz.*,
                sczdk.value AS dog_tp_zd,
                sczk.value AS kod_tp_zd,
                sczj.value AS jelanie_tp_zd
            FROM 
                su_consolidation_zd AS scz 
            LEFT JOIN su_consolidation_zd_dog_kod AS sczdk 
                USING(dog_kod )
            LEFT JOIN su_consolidation_zd_kod AS sczk
                USING (kod)
            LEFT JOIN su_consolidation_zd_jelanie AS sczj
                USING (jelanie)
        ');

        $pdo->exec('CREATE OR REPLACE VIEW su_consolidation_do_info AS
            SELECT
                scd.*,
                scddk.value AS dog_tp_do
            FROM 
                su_consolidation_do AS scd
            LEFT JOIN su_consolidation_do_dog_kod AS scddk 
                USING(dog_kod )
        ');
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3573')
            ->setDescription('Create views su_consolidation_zd_info and su_consolidation_do_info.');
    }
}
