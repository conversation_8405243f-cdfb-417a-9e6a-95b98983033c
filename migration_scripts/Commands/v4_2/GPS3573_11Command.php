<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Application\Common\Config;
use TF\Application\Entity\UserLayers;
use TF\Commands\Common\MainDbCommand;

/**
 * GPS3573_11Command run on main database.
 *
 * This command will set the default definitions for layer of type kvs in main db
 */
class GPS3573_11Command extends MainDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $kvsLayerDefaultDefinitions = UserLayers::getDefinitionsByType(Config::LAYER_TYPE_KVS);
        $kvsLayerDefaultDefinitionsJson = json_encode($kvsLayerDefaultDefinitions);
        $kvsLayerType = Config::LAYER_TYPE_KVS;

        $pdo->exec("UPDATE su_users_layers
            SET definitions = '{$kvsLayerDefaultDefinitionsJson}'::jsonb
            WHERE layer_type = {$kvsLayerType}");
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3573-11')
            ->setDescription('Set the default definitions for layers of type kvs in users layers.');
    }
}
