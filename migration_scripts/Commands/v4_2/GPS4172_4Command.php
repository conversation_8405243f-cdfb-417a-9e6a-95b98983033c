<?php

namespace TF\Commands\v4_2;

use Exception;
use PDO;
use TF\Application\Common\Config;
use TF\Application\Entity\User;
use TF\Application\Entity\UserLayers;
use TF\Commands\Common\MainDbCommand;

class GPS4172_4Command extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-4172-4')
            ->setDescription('Add column definitions "fill_color", "border_color" and "label" in su_users_layers');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        /** @var User[] $users */
        $users = User::finder()->findAll('level = :level', [':level' => '2']);
        $layerTypes = [Config::LAYER_TYPE_ZP, Config::LAYER_TYPE_GPS, Config::LAYER_TYPE_KMS,  Config::LAYER_TYPE_KVS,
            Config::LAYER_TYPE_ISAK, Config::LAYER_TYPE_FOR_ISAK, Config::LAYER_TYPE_WORK_LAYER];

        try {
            foreach ($users as $user) {
                $placeholders = [];
                $params = [':group_id' => $user->group_id];

                foreach ($layerTypes as $index => $type) {
                    $param = ':layer_type_' . $index;
                    $placeholders[] = $param;
                    $params[$param] = $type;
                }

                $inClause = implode(',', $placeholders);

                /** @var UserLayers[] $layers */
                $layers = UserLayers::finder()->findAll(
                    "group_id = :group_id AND layer_type IN({$inClause})",
                    $params
                );

                if (!count($layers)) {
                    continue;
                }

                foreach ($layers as $layer) {
                    $definitions = $layer->getDefinitions();
                    $definitionsColumns = UserLayers::getColumns($definitions);

                    $defaultDefinitions = UserLayers::getDefinitionsByType($layer->layer_type);
                    $colorDefinitions = UserLayers::filterDefinitions($defaultDefinitions, [['col_category' => Config::LAYER_COLUMN_CATEGORY_COLOR]]);
                    $labelDefinitions = UserLayers::filterDefinitions($defaultDefinitions, [['col_category' => Config::LAYER_COLUMN_CATEGORY_LABEL]]);

                    if (!in_array('fill_color', $definitionsColumns)) {
                        foreach ($colorDefinitions as $colorDefinition) {
                            if ('fill_color' == $colorDefinition['col_name']) {
                                $definitions[] = $colorDefinition;
                            }
                        }
                    }

                    if (!in_array('border_color', $definitionsColumns)) {
                        foreach ($colorDefinitions as $colorDefinition) {
                            if ('border_color' == $colorDefinition['col_name']) {
                                $definitions[] = $colorDefinition;
                            }
                        }
                    }

                    if (!in_array('label', $definitionsColumns)) {
                        foreach ($labelDefinitions as $labelDefinition) {
                            if ('label' == $labelDefinition['col_name']) {
                                $definitions[] = $labelDefinition;
                            }
                        }
                    }

                    $layer->definitions = json_encode($definitions);
                    $layer->save();
                }
            }
        } catch (Exception $e) {
            $output->writeln('<error>' . $e->getMessage() . '</error>');
        }
    }
}
