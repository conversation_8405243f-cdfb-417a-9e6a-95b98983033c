<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Application\Common\Config;
use TF\Application\Entity\LayerStyles;
use TF\Application\Entity\UserLayers;
use TF\Commands\Common\UserDbCommand;

class FixStylesCommand extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:fix-styles')
            ->setDescription('Fix styles for layers without style');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        /**
         * @var UserLayers[] $layersWithoutStyle
         */
        $layersWithoutStyle = UserLayers::finder()->findAllBySql(
            '
            SELECT 
                sul.* 
            FROM 
                su_users_layers AS sul
            JOIN su_users AS su
                ON su.id = sul.user_id
            WHERE
                su.database = :database
                and "style"::jsonb = \'{}\'::jsonb
        ',
            [
                ':database' => $this->userDbName,
            ]
        );

        $valuesSql = '';
        /**
         * @var UserLayers $layer
         */
        foreach ($layersWithoutStyle as $layer) {
            if (Config::LAYER_TYPE_KVS == $layer->layer_type) {
                continue;
            }

            $style = LayerStyles::generateDefaultStyle($layer->layer_type, false);
            $fillColor = $style->fill_color ? "'{$style->fill_color}'" : 'null';
            $fillColName = $style->fill_column_name ? "'{$style->fill_column_name}'" : 'null';
            $borderColName = $style->border_column_name ? "'{$style->border_column_name}'" : 'null';
            $labels = "'{}'::TEXT[]";
            $borderOnly = $style->border_only ? 'true' : 'false';
            $tags = $style->tags ? 'true' : 'false';

            $valuesSql .= "(
                '{$layer->id}',
                '{$layer->table_name}',
                '{$style->type}',
                {$style->transparency},
                {$fillColName}, 
                {$fillColor},
                {$borderColName},
                '{$style->border_color}',
                {$style->border_width},
                {$borderOnly},
                {$labels},
                {$style->label_size},
                {$tags},
                NOW(),
                NOW()
            ),";

            if ($this->getTableNameExists($layer->table_name)) {
                $pdo->exec("UPDATE {$layer->table_name} SET fill_color = {$fillColor}, border_color = '{$style->border_color}'");
            }
        }

        $valuesSql = rtrim($valuesSql, ',');

        $sql = "INSERT INTO public.su_layer_styles
        (
            layer_id,
            table_name,
            \"type\",
            transparency,
            fill_column_name,
            fill_color,
             border_column_name,
             border_color,
             border_width,
             border_only,
             labels,
             label_size,
             tags,
             created_at,
             updated_at
        ) VALUES 
            {$valuesSql}
        ON CONFLICT DO NOTHING;
        ";

        $pdo->exec($sql);
    }
}
