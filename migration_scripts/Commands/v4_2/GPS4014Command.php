<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4014Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-4014')
            ->setDescription('Create type kvs_plot_status_enum and db function get_kvs_plot_status');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec('DROP FUNCTION IF EXISTS get_kvs_plot_status(is_edited BOOLEAN, edit_active_from TIMESTAMP)');
        $pdo->exec('DROP TYPE IF EXISTS kvs_plot_status_enum');

        $pdo->exec("CREATE TYPE kvs_plot_status_enum AS ENUM('Active', 'Archived')");
        $pdo->exec("CREATE FUNCTION get_kvs_plot_status(is_edited BOOLEAN, edit_active_from TIMESTAMP)
            RETURNS kvs_plot_status_enum AS 
            $$
                BEGIN

                    IF is_edited = FALSE OR (is_edited = TRUE AND edit_active_from::date >= now()) THEN
                        RETURN 'Active'::kvs_plot_status_enum;
                    ELSIF is_edited = TRUE AND edit_active_from::date < now() THEN
                        RETURN 'Archived'::kvs_plot_status_enum;
                    END IF;

                    RETURN null::kvs_plot_status_enum;

                END;
            $$ LANGUAGE plpgsql IMMUTABLE;");
    }
}
