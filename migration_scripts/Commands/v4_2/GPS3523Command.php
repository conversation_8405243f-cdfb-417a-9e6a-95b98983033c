<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Application\Common\Config;
use TF\Commands\Common\MainDbCommand;

/**
 * GPS3523Command run on susi_main database.
 *
 * This command will remove border_width property from the style column (su_users_layer table) for kvs layers
 * then it will add border_width property for each ekatte object in the style column.
 */
class GPS3523Command extends MainDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $defaultBorderWidth = Config::LAYER_BOUNDARY_DEFAULT_BORDER_WIDTH;
        $kvsLayerType = Config::LAYER_TYPE_KVS;

        // Remove border width from style column for kvs layers
        $pdo->exec("UPDATE su_users_layers 
            SET
                \"style\" = (\"style\"::jsonb - 'border_width')::jsonb
            WHERE 
                style NOTNULL AND style::jsonb <> 'null'::jsonb
                AND layer_type = {$kvsLayerType}
        ");

        // Add border width for each ekatte object in the style column for kvs layers
        $pdo->exec("WITH kvs_fixed_style AS
            (
                SELECT
                    sul.id,
                    jsonb_object_agg( 
                        kvs.ekatte,
                        kvs.\"style\"::jsonb || jsonb_build_object('border_width', {$defaultBorderWidth})
                    )::json AS new_style,
                    sul.\"style\"
                FROM 
                    su_users_layers AS sul,
                    json_each(sul.\"style\") AS kvs(ekatte, \"style\")
                WHERE 
                    sul.\"style\" NOTNULL 
                    AND sul.\"style\"::jsonb <> 'null'::jsonb
                    AND sul.\"style\"::jsonb <> '{}'::jsonb
                    AND sul.layer_type = {$kvsLayerType}
                GROUP BY 
                    sul.id	
            )
            UPDATE su_users_layers AS sul
            SET \"style\" = kfs.new_style
            FROM kvs_fixed_style  AS kfs
            WHERE sul.id = kfs.id
        ");
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3523')
            ->setDescription('Fix border_width property in the style column for kvs layers.');
    }
}
