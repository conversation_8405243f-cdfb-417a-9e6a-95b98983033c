<?php

namespace TF\Commands\v4_2;

use Exception;
use PDO;
use TF\Application\Common\Config;
use TF\Application\Entity\User;
use TF\Application\Entity\UserLayers;
use TF\Commands\Common\MainDbCommand;

class GPS4346_1Command extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-4346-1')
            ->setDescription('Add column definitions "col_reference" in su_users_layers');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        /** @var User[] $users */
        $users = User::finder()->findAll('level = :level', [':level' => '2']);

        try {
            foreach ($users as $user) {
                /** @var UserLayers[] $layers */
                $params = [':group_id' => $user->group_id];

                /** @var UserLayers[] $layers */
                $layers = UserLayers::finder()->findAll(
                    'group_id = :group_id',
                    $params
                );

                if (!count($layers)) {
                    continue;
                }

                foreach ($layers as $layer) {
                    $definitions = $layer->getDefinitions();
                    $defaultDefinitions = UserLayers::getDefinitionsByType($layer->layer_type);

                    // Set default definitions for KVS layer only because new definition virtual_ekatte_name must be after ekate
                    if (Config::LAYER_TYPE_KVS == $layer->layer_type) {
                        $definitions = $defaultDefinitions;
                    }

                    if (Config::LAYER_TYPE_KVS != $layer->layer_type) {
                        foreach ($definitions as &$definition) {
                            foreach ($defaultDefinitions as $defaultDefinition) {
                                if ($definition['col_name'] === $defaultDefinition['col_name']) {
                                    $definition['col_reference'] = $defaultDefinition['col_reference'] ?? null;
                                    $definition['col_copyable'] = $defaultDefinition['col_copyable'];
                                }
                            }
                        }
                    }

                    $layer->definitions = json_encode($definitions);
                    $layer->save();
                }
            }
        } catch (Exception $e) {
            $output->writeln('<error>' . $e->getMessage() . '</error>');
        }
    }
}
