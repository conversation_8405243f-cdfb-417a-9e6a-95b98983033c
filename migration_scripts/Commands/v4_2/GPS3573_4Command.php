<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS3573_4Command run on client databases.
 *
 * This command will refresh the materialized view su_area_types
 */
class GPS3573_4Command extends UserDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec('REFRESH MATERIALIZED VIEW su_area_types');
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3573-4')
            ->setDescription('Refresh materialized view su_area_types.');
    }
}
