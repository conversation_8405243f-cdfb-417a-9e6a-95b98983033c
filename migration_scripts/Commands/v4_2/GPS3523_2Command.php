<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Application\Common\Config;
use TF\Commands\Common\MainDbCommand;

/**
 * GPS3523_2Command run on susi_main database.
 *
 * This command will set the default style for layers with nullable style.
 */
class GPS3523_2Command extends MainDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $kvsLayerType = Config::LAYER_TYPE_KVS;
        $dbLink = 'host=' . DBLINK_HOST . ' port=' . DBLINK_PORT . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD;

        // Set the default style for all layers with nullable style except for kvs layers with user without database
        $pdo->exec("WITH layers_with_nullable_styles AS (
            SELECT
                sul.user_id,
                sul.id AS layer_id,
                sul.layer_type,
                sul.table_name,
                jsonb_build_object(
                    'tags', 1,
                    'color', 'ffffff',
                    'label_name', json_build_array('kad_ident'),
                    'label_size', 8,
                    'border_only', 1,
                    'border_color', 'ffffff',
                    'border_width', 1,
                    'transparency', 0,
                    'coloring_type', 'single',
                    'coloring_attribute', null,
                    'coloring_attribute_layer', null
                ) AS layer_default_style
            FROM 
                su_users_layers AS sul
            WHERE
                \"style\" ISNULL
        ),
        layers_to_update AS (
            SELECT 
                lwns.layer_id,
                layer_default_style
            FROM
                layers_with_nullable_styles lwns
            WHERE
                lwns.layer_type <> {$kvsLayerType}
            UNION
            SELECT
                lwns.layer_id,
                COALESCE(
                    jsonb_object_agg(
                        layer_kvs.ekate, lwns.layer_default_style
                    ) FILTER (WHERE ekate notnull),
                    '{}'::jsonb
                )AS layer_default_style
            FROM
                layers_with_nullable_styles AS lwns
            JOIN su_users AS su 
                ON su.id = lwns.user_id
            JOIN pg_catalog.pg_database AS pgdb
                ON pgdb.datname = su.\"database\"
            LEFT JOIN dblink(
            '{$dbLink}' || ' dbname=' || su.\"database\",
            $$
                SELECT DISTINCT
                  ekate,
                  tabl.table_catalog AS \"database\"
                FROM 
                    layer_kvs
                JOIN information_schema.\"tables\" AS tabl
                    ON tabl.table_name = 'layer_kvs'
                    AND tabl.table_schema = 'public'
            $$) AS layer_kvs(ekate VARCHAR, \"database\" VARCHAR)
                ON  su.\"database\" = layer_kvs.\"database\"
            WHERE
                lwns.layer_type = {$kvsLayerType}
            GROUP BY
                lwns.layer_id,
                lwns.layer_type
        )
        UPDATE su_users_layers
        SET \"style\" = ltu.layer_default_style
        FROM layers_to_update AS ltu
        WHERE id = ltu.layer_id
        ");

        // Set empty json for all layers with  nullable style
        $pdo->exec("UPDATE su_users_layers
            SET \"style\" = '[]'::json
            WHERE \"style\" ISNULL
        ");
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3523-2')
            ->setDescription('This command will set the default style for layers with nullable style');
    }
}
