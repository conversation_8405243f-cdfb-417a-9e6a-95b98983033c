<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS3702_2Command extends UserDbCommand
{
    private $tables = [
        'su_owners',
        'su_contracts',
        'su_owners_reps',
        'su_contracts_plots_rel',
        'su_plots_owners_rel',
        'su_owners_files',
        'su_sales_contracts_files',
        'su_owners_documents',
        'su_contracts_files_rel',
        'su_contracts_farming_contragents',
        'su_sales_contracts',
        'su_sales_contracts_plots_rel',
        'su_subleases_plots_contracts_rel',
        'su_contracts_contragents',
        'su_personal_use',
        'su_personal_use_rents',
        'su_collections',
        'su_payments',
        'su_transactions',
        'su_charged_renta',
        'su_charged_renta_params',
        'su_charged_renta_history',
    ];

    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec(
            'CREATE OR REPLACE FUNCTION update_updated_at_column()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = NOW();
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;'
        );

        foreach ($this->tables as $table) {
            $pdo->exec('ALTER TABLE ' . $table . ' ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL;');
            $pdo->exec('ALTER TABLE ' . $table . ' ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL;');

            $triggerExists = $pdo->query("SELECT 1 FROM pg_trigger WHERE tgname = 'set_updated_at' AND tgrelid = '{$table}'::regclass");
            if (!$triggerExists->fetchColumn()) {
                $pdo->exec(
                    'CREATE TRIGGER set_updated_at
                    BEFORE UPDATE ON ' . $table . '
                    FOR EACH ROW
                    EXECUTE FUNCTION update_updated_at_column();'
                );
            }
        }
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3702_2')
            ->setDescription('Add created_at and updated_at columns in users databases.');
    }
}
