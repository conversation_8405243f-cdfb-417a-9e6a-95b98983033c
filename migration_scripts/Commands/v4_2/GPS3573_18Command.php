<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Application\Common\Config;
use TF\Commands\Common\MainDbCommand;

/**
 * GPS3573_18Command run on main database.
 *
 * This command will update the definitions for column vps_type for all vps layers and column bans for natura_200 layer.
 * It will make:
 *      - column 'vps_type' (vps layers) not visible, not sortable and not exportable
 *      - column 'bans' (natura 2000 layers) not visible.
 *      - column 'watering' (ISAK layers) of type boolean
 */
class GPS3573_18Command extends MainDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $vpsLayerTypes = [
            Config::LAYER_TYPE_VPS_LIVADEN_BLATAR,
            Config::LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI,
            Config::LAYER_TYPE_VPS_GASKI_ZIMNI,
            Config::LAYER_TYPE_VPS_ORLI_LESHOYADI,
        ];
        $vpsLayerTypesStr = implode(',', $vpsLayerTypes);
        $natura2000LayerType = Config::LAYER_TYPE_NATURA_2000;
        $isakLayerType = Config::LAYER_TYPE_ISAK;
        $allLayerTypesStr = implode(',', array_merge($vpsLayerTypes, [$natura2000LayerType, $isakLayerType]));

        $pdo->exec("WITH 
            layers_definitions AS (
                SELECT
                    sul.id AS layer_id,
                    jsonb_agg(
                        CASE WHEN sul.layer_type IN ({$vpsLayerTypesStr}) AND d->>'col_name' = 'vps_type'
                                THEN d || '{\"col_visible\": false, \"col_sortable\": false, \"col_exportable\":false }'::jsonb
                            WHEN sul.layer_type = {$natura2000LayerType} AND d->>'col_name' = 'bans'
                                THEN d || '{\"col_visible\": false}'::jsonb
                            WHEN sul.layer_type = {$isakLayerType} AND d->>'col_name' = 'watering'
                                THEN d || '{\"col_category\": \"boolean\"}'::jsonb
                            ELSE d
                        END
                    ) AS new_definitions
                FROM 
                    su_users_layers AS sul,
                    jsonb_array_elements(definitions) AS d
                WHERE
                    layer_type IN ({$allLayerTypesStr})
                GROUP BY
                    sul.id
            )
            UPDATE su_users_layers
            SET
                definitions = new_definitions
            FROM 
                layers_definitions
            WHERE
                id = layer_id
        ");
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3573-18')
            ->setDescription("
                Update definitions for layer types vps and natura_2000. Make columns \"vps_type\" not visible, not sortable and not exportable, and column \"bans\" not visible.
                Change type of column 'watering' from numeric to boolean'
            ");
    }
}
