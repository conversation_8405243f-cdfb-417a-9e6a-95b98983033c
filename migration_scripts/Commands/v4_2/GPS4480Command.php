<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4480Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-4480')
            ->setDescription('Migration to update transparency column in table su_layer_styles where the value is greater than 80');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec('update su_layer_styles
                                set transparency = (100 - transparency)
                                where transparency >= 80');
    }
}
