<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4163_2Command extends UserDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $this->executeSql($pdo, __DIR__ . '/sql/GPS4163_2.sql');
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-4163-2')
            ->setDescription('Remove column has_consolidation_data in layer_kvs');
    }
}
