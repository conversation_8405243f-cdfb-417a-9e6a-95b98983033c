<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4163_4Command extends UserDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $this->executeSql($pdo, __DIR__ . '/sql/GPS4163_4.sql');
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-4163-4')
            ->setDescription('Add trigger on update process_ekatte_updates function');
    }
}
