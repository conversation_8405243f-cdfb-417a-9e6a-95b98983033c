<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS3573_5Command run on client databases.
 *
 * This command will create function get_ntp_title_by_code
 */
class GPS3573_5Command extends UserDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec('CREATE OR REPLACE FUNCTION get_ntp_title_by_code(code VARCHAR)
            RETURNS VARCHAR IMMUTABLE AS
            $$
                -- Note: If you use this function in virtual column, in order to refresh the calculated value
                -- you need to update the virtual column - for example: UPDATE {table} set {virtual_column_name} = {virtual_column_name}
                DECLARE res VARCHAR;
                BEGIN
                    SELECT 
                        title INTO res
                    FROM
                        public.su_area_types
                    WHERE
                        code ~ \'^[0-9]+$\'
                        AND (
                            id = code::INT
                            OR code::INT = ANY(additional_codes)
                        );

                    IF res IS NULL THEN
                        res := code::VARCHAR;
                    END IF;
                    RETURN res;
                END;
            $$ LANGUAGE plpgsql;');
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3573-5')
            ->setDescription('Create function get_ntp_title_by_code');
    }
}
