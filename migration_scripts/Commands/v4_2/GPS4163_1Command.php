<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4163_1Command extends UserDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec('
            DO $$
            DECLARE
                rec RECORD;
            BEGIN
                -- Check if the table exists
                IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = \'su_consolidation_do\') THEN
                    -- Add the column if it does not exist
                    ALTER TABLE public.su_consolidation_do ADD COLUMN IF NOT EXISTS godina INT4 NULL;

                    -- Loop through the selected data from su_consolidation_zd
                    FOR rec IN
                        SELECT DISTINCT godina, ekatte
                        FROM su_consolidation_zd
                    LOOP
                        -- Update the su_consolidation_do table
                        UPDATE public.su_consolidation_do
                        SET godina = rec.godina
                        WHERE ekatte = rec.ekatte;
                    END LOOP;
                END IF;
            END $$;
        ');
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-4163-1')
            ->setDescription('Add godina column to all existing su_consolidation_do tables and update value from su_consolidation_zd table');
    }
}
