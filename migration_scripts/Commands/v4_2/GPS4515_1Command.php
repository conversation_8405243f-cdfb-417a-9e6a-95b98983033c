<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4515_1Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-4515-1')
            ->setDescription('Migration create function generate_color_from_value');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec("CREATE OR REPLACE FUNCTION generate_color_from_value(input_value TEXT)
                                RETURNS TEXT AS $$
                                DECLARE
                                    input_string TEXT;
                                    hash BIGINT;
                                    red INTEGER;
                                    green INTEGER;
                                    blue INTEGER;
                                    hex_color TEXT;
                                BEGIN
                                    -- If the input is NULL, return #808080
                                    IF input_value IS NULL THEN
                                        RETURN '#808080';
                                    END IF;

                                    -- Convert input to text
                                    input_string := input_value::TEXT;

                                    -- Hash generation
                                    hash := abs(hashtext(input_string)::BIGINT);

                                    -- Extract RGB values from the hash
                                    red := (hash % 256); -- Last 8 bits
                                    green := ((hash / 256) % 256); -- Next 8 bits
                                    blue := ((hash / 65536) % 256); -- Next 8 bits

                                    -- Generate hex color code
                                    hex_color := '#' || lpad(to_hex(red), 2, '0') || lpad(to_hex(green), 2, '0') || lpad(to_hex(blue), 2, '0');

                                    RETURN hex_color;
                                END;
                                $$ LANGUAGE plpgsql IMMUTABLE;
    ");
    }
}
