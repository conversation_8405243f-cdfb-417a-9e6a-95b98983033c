<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Application\Common\Config;
use TF\Application\Entity\User;
use TF\Application\Entity\UserLayers;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS3987Command run on client databases.
 *
 * This command will set the columns of category gid to be primary keys in all user layers tables.
 */
class GPS3987Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3987')
            ->setDescription('Set columns of category gid as primary keys in all user layers tables');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $users = User::finder()->findAll('database = :database', [':database' => $this->userDbName]);
        $userIds = array_map(fn ($user) => $user->id, $users);

        if (empty($userIds)) {
            $output->writeln('No users found for this database. Skipping...');

            return;
        }

        $userIdsStr = implode(', ', $userIds);

        /** @var UserLayers[] $userLayers */
        $userLayers = UserLayers::finder()->findAll("user_id IN ({$userIdsStr})");

        $tablesWithPKSql = "SELECT table_name AS pk FROM information_schema.table_constraints WHERE constraint_type = 'PRIMARY KEY'";
        $tablesWithPKStmt = $pdo->query($tablesWithPKSql);
        $tablesWithPK = $tablesWithPKStmt->fetchAll(PDO::FETCH_COLUMN);

        $existingTablesSql = 'SELECT table_name FROM information_schema.tables';
        $existingTablesStmt = $pdo->query($existingTablesSql);
        $existingTables = $existingTablesStmt->fetchAll(PDO::FETCH_COLUMN);

        foreach ($userLayers as $userLayer) {
            if ($userLayer->isRemote()) {
                // Skip remote layers
                continue;
            }

            if (in_array($userLayer->table_name, $tablesWithPK)) {
                // Skip tables having primary key already
                continue;
            }

            if (!in_array($userLayer->table_name, $existingTables)) {
                // Skip tables that do not exist
                continue;
            }

            $gidColumn = $userLayer->getDefinitionByCategory(Config::LAYER_COLUMN_CATEGORY_GID)['col_name'];
            $tableName = $userLayer->table_name;
            $seqName = "{$tableName}_{$gidColumn}_seq";

            $output->write("Setting '{$gidColumn}' as primary key in table '{$tableName}'...");

            // Create sequence if not exists
            $pdo->exec("CREATE SEQUENCE IF NOT EXISTS {$seqName}");

            // Set next value of sequence to max value of gid column + 1
            $pdo->exec("SELECT setval('{$seqName}', (SELECT MAX({$gidColumn}) FROM {$tableName}) + 1)");

            // Set nextval for duplicated gids
            $this->fixDuplicatedGids($pdo, $gidColumn, $tableName, $seqName);

            // Change column to be bigint and not null, and set default value to next value of sequence
            $pdo->exec("ALTER TABLE {$tableName} ALTER COLUMN {$gidColumn} SET DATA TYPE BIGINT");
            $pdo->exec("ALTER TABLE {$tableName} ALTER COLUMN {$gidColumn} SET NOT NULL");
            $pdo->exec("ALTER TABLE {$tableName} ALTER COLUMN {$gidColumn} SET  DEFAULT nextval('{$seqName}')");
            $pdo->exec("ALTER TABLE {$tableName} ADD PRIMARY KEY ({$gidColumn})");

            $output->writeln("\tDone!");
        }
    }

    private function fixDuplicatedGids(PDO $pdo, string $gidColumn, string $tableName, string $seqName)
    {
        // Fix duplicated gids if any by setting them to the next value of the sequence
        $pdo->exec("
            WITH duplicate_rows AS (
                SELECT 
                    {$gidColumn} AS d_gid,
                    ROW_NUMBER() OVER (PARTITION BY {$gidColumn}) AS row_num
                FROM 
                    {$tableName}
            )
            UPDATE {$tableName}
            SET {$gidColumn} = nextval('{$seqName}')
            FROM duplicate_rows
            WHERE
                d_gid = {$gidColumn}
                AND row_num > 1
        ");
    }
}
