<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4163_3Command extends UserDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $sql = "CREATE OR REPLACE FUNCTION public.process_ekatte_updates()
            <PERSON><PERSON><PERSON><PERSON> trigger
            LANGUAGE plpgsql
            AS $$
            DECLARE
                view_record RECORD;
                su_consolidation_exists BOOLEAN;
                view_exists BOOLEAN;
                view_name TEXT;
            BEGIN
                -- Check if the table su_consolidation_zd exists
                SELECT EXISTS (
                    SELECT 1
                    FROM information_schema.tables 
                    WHERE table_name = 'su_consolidation_zd'
                ) INTO su_consolidation_exists;

                -- If the table does not exist, exit the function
                IF NOT su_consolidation_exists THEN
                    RETURN NULL;
                END IF;

                -- Query su_consolidation_zd for all distinct ekatte and godina combinations based on the aggregated ekatte values from updated records
                FOR view_record IN
                    SELECT DISTINCT scz.ekatte, scz.godina
                    FROM su_consolidation_zd scz
                    WHERE scz.ekatte IN (
                        SELECT DISTINCT ekate
                        FROM updated_layer_kvs_records
                    )
                LOOP
                    -- Construct the materialized view name
                    view_name := format('layer_decl_69_70_%s_%s', view_record.ekatte, view_record.godina);

                    -- Check if the materialized view exists
                    SELECT EXISTS (
                        SELECT 1 
                        FROM pg_matviews 
                        WHERE matviewname = view_name
                    ) INTO view_exists;

                    -- If the view exists, refresh it
                    IF view_exists THEN
                        EXECUTE format('REFRESH MATERIALIZED VIEW %I', view_name);
                    END IF;
                END LOOP;

                RETURN NULL; -- For a statement-level trigger, NULL return is appropriate
            END;
            $$;";

        $stmt = $pdo->prepare($sql);
        $stmt->execute();
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-4163-3')
            ->setDescription('Add process_ekatte_updates function');
    }
}
