<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS3573_3Command run on client databases.
 *
 * This command will create materialized view su_area_types
 */
class GPS3573_3Command extends UserDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $dbLink = 'host=' . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD;

        $pdo->exec('DROP MATERIALIZED VIEW IF EXISTS su_area_types');
        $pdo->exec("CREATE MATERIALIZED VIEW su_area_types AS 
             SELECT 
                id,
                title,
                cat,
                additional_codes
            FROM
                dblink(
                    '{$dbLink}',
                    $$
                        SELECT 
                            id,
                            title,
                            cat,
                            additional_codes
                        FROM
                            su_area_types
                    $$
                ) AS su_area_types(
                    id INT8,
                    title VARCHAR,
                    cat INT4,
                    additional_codes INT[]
                )
            WITH DATA
        ");
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3573-3')
            ->setDescription('Create materialized view su_area_types.');
    }
}
