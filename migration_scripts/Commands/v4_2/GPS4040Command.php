<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS4040Command run on client databases.
 *
 * This command will enable the uuid-ossp extension in all client databases.
 */
class GPS4040Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-4040')
            ->setDescription('Enable the uuid-ossp extension in all client databases');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $stmt = $pdo->prepare('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"');
        $stmt->execute();
    }
}
