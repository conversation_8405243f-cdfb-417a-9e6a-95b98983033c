<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS3573_16Command run on client databases.
 *
 * This command will fill farming_name by farming_id in su_contracts table
 */
class GPS3573_16Command extends UserDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $dbLink = 'host=' . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD;

        $pdo->exec("UPDATE su_contracts
            SET 
                farming_name = farming.\"name\"
            FROM 
                dblink(
                    '{$dbLink}',
                    $$
                        SELECT id, \"name\"
                        FROM su_users_farming
                    $$
                ) AS farming(id INT, \"name\" VARCHAR)
            WHERE
                farming.id = farming_id
            RETURNING su_contracts.id
        ");
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3573-16')
            ->setDescription('Fill farming_name by farming_id in su_contracts table');
    }
}
