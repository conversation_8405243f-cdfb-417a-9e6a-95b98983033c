<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Application\Common\Config;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS3573_19Command run on main database.
 *
 * This command will set the type of column watering to 'boolean' for layer_isak
 */
class GPS3573_19Command extends UserDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $layerTypeIsak = Config::LAYER_TYPE_ISAK;
        $dbLink = 'host=' . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD;

        $isakTablesSql = "SELECT
                ul.table_name
            FROM
                dblink(
                    '{$dbLink}',
                    $$
                        SELECT
                            table_name
                        FROM
                            su_users_layers AS sul
                        JOIN su_users AS su 
                            ON sul.user_id = su.id
                        WHERE
                            su.database = '{$this->userDbName}'
                            AND sul.layer_type = {$layerTypeIsak}
                    $$
                ) AS ul (table_name VARCHAR)
            JOIN information_schema.columns AS cols
                ON cols.table_name = ul.table_name
                AND cols.column_name = 'watering'
                AND cols.data_type = 'numeric'
            ";

        $isakTablesStmt = $pdo->prepare($isakTablesSql);
        $result = $isakTablesStmt->execute();
        $isakTables = $isakTablesStmt->fetchAll(PDO::FETCH_COLUMN);

        foreach ($isakTables as $isakTable) {
            $output->writeln("Altering column watering in table {$isakTable}...");

            $sql = "ALTER TABLE {$isakTable} ALTER COLUMN watering TYPE boolean USING CASE WHEN watering = 0 THEN FALSE ELSE TRUE END;";

            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute();

            if (!$result) {
                $output->error("Failed to alter column watering in table {$isakTable}");

                continue;
            }

            $output->info('Done!');
        }
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3573-19')
            ->setDescription("This command will set the type of column watering to 'boolean' for layer_isak");
    }
}
