<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS3675Command run on client databases.
 *
 * This command will create type border_type_enum
 */
class GPS3675Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3675')
            ->setDescription('Create type border_type_enum');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec("CREATE TYPE border_type_enum AS ENUM('ekatte', 'masiv')");
    }
}
