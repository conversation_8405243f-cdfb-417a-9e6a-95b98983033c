<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\MainDbCommand;

class GPS3702_1Command extends MainDbCommand
{
    private $tables = [
        'su_users',
        'su_users_farming',
        'su_users_layers',
    ];

    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec(
            'CREATE OR REPLACE FUNCTION update_updated_at_column()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = NOW();
                RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;'
        );

        foreach ($this->tables as $table) {
            $pdo->exec('ALTER TABLE ' . $table . ' ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL;');
            $pdo->exec('ALTER TABLE ' . $table . ' ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL;');

            $pdo->exec(
                'CREATE TRIGGER set_updated_at
                BEFORE UPDATE ON ' . $table . '
                FOR EACH ROW
                EXECUTE FUNCTION update_updated_at_column();'
            );
        }
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3702_1')
            ->setDescription('Add created_at and updated_at columns in susi_main tables.');
    }
}
