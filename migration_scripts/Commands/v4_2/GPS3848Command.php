<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Application\Common\Config;
use TF\Commands\Common\MainDbCommand;

/**
 * GPS3848Command run on susi_main database.
 *
 * This command fixes definitions for KVS layers
 */
class GPS3848Command extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3848')
            ->setDescription('This command fixes col_title for definition mestnost for KVS layers');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $layerTypeKVS = Config::LAYER_TYPE_KVS;

        $sql = "WITH layers_fixed_definitions AS 
            (
                SELECT
                    sul.id AS user_layer_id,
                    sul.definitions,
                    jsonb_set(sul.definitions, ARRAY[(idx-1)::TEXT, 'col_title']::TEXT[], '\"Local area\"'::jsonb, false) AS fixed_definitions
                FROM
                    su_users_layers sul,
                    jsonb_array_elements(sul.definitions) WITH ordinality AS def(elem, idx)
                WHERE
                    sul.definitions @> '[{\"col_name\": \"mestnost\"}]'
                    AND def.elem->>'col_name' = 'mestnost'
                    AND sul.layer_type = {$layerTypeKVS}

            )
            UPDATE su_users_layers
            SET definitions = layers_fixed_definitions.fixed_definitions
            FROM layers_fixed_definitions
            WHERE
                id = layers_fixed_definitions.user_layer_id
        ";

        $stmt = $pdo->prepare($sql);
        $stmt->execute();
    }
}
