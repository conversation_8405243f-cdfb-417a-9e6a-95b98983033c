<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS3573_7Command run on client databases.
 *
 * This command will set category to null where the value is 0 or empty stirng in layer_kvs table.
 */
class GPS3573_7Command extends UserDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec('UPDATE layer_kvs SET category = null WHERE category = \'0\' or category = \'\'');
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3573-7')
            ->setDescription('Set category to null where the value is 0 or empty stirng in layer_kvs table.');
    }
}
