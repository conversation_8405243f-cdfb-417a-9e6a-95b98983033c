<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4515_2Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-4515-2')
            ->setDescription('Migration to delete su_layers_styles_column_colors table');
    }

    // !!! Use migration v4.2:GPS-4497 before running this one !!!
    // Because materialized views depend on this table and create roles, it is not possible to delete them..
    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec('ALTER TABLE su_layers_styles_column_colors DROP CONSTRAINT fk_su_layer_styles_id;');
        $pdo->exec('DROP INDEX IF EXISTS su_layers_styles_column_colors_style_layers_id_idx;');
        $pdo->exec('DROP INDEX IF EXISTS su_layers_styles_column_colors_category_idx;');
        $pdo->exec('DROP TABLE IF EXISTS su_layers_styles_column_colors;');
    }
}
