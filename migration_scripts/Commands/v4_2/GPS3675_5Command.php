<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS3675_5Command run on client databases.
 *
 * This command will cluster the layer_kvs_borders table by layer_kvs_borders_geom_idx
 */
class GPS3675_5Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3675-5')
            ->setDescription('Create db function get_geom_boundary');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec("CREATE OR REPLACE FUNCTION get_geom_boundary(geom GEOMETRY, simplify INT DEFAULT NULL)
            RETURNS GEOMETRY
            AS $$
                -- This function returns the exterior ring of a polygon or the exterior ring of the first polygon in a multipolygon
                DECLARE
                    res GEOMETRY;
                    simplified GEOMETRY;
                BEGIN
                    IF ST_GeometryType(geom) = 'ST_Polygon' THEN 
                        res:= ST_MakePolygon(
                                ST_ExteriorRing(
                                    geom
                                )
                        );
                    ELSEIF ST_GeometryType(geom) = 'ST_MultiPolygon' THEN
                        res := ST_MakePolygon(
                                ST_ExteriorRing(
                                    ST_GeometryN(geom , 1)
                                )
                        );
                    ELSE
                        res := geom;
                    END IF;
            
                    IF simplify ISNULL THEN
                        RETURN res;
                    END IF;
                
                    simplified := ST_simplify(res, simplify);
                
                    IF simplified ISNULL THEN
                        RETURN res;
                    END IF;
                
                    RETURN simplified;
                END
            $$
            LANGUAGE plpgsql;
        ");
    }
}
