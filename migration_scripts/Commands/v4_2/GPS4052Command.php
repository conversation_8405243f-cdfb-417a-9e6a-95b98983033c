<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS4052Command run on client databases.
 *
 * This command will update the type of area column for layers of type ISAK. It will set its precision to 3 digits.
 */
class GPS4052Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-4052')
            ->setDescription('Set precision to column \'area\' in layer_isak_* tables');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $stmt = $pdo->prepare("SELECT table_name FROM information_schema.TABLES WHERE table_name ILIKE 'layer_isak_%'");
        $stmt->execute();
        $isakTables = $stmt->fetchAll(PDO::FETCH_COLUMN);

        foreach ($isakTables as $isakTable) {
            $pdo->exec("ALTER TABLE {$isakTable} ALTER COLUMN area TYPE numeric(15,3)");
        }
    }
}
