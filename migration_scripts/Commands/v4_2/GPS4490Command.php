<?php

namespace TF\Commands\v4_2;

use Exception;
use PDO;
use TF\Commands\Common\MainDbCommand;

class GPS4490Command extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-4490')
            ->setDescription('Add declared area status default colors');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        try {
            // Check if the PDO connection is active
            if (!$pdo) {
                throw new Exception('Invalid PDO connection');
            }

            $this->executeSql($pdo, __DIR__ . '/sql/GPS4490.sql');
            // Execute the SQL statement
            $sql = "
                INSERT INTO su_default_colors (category, value, color)
                VALUES
                ('declared area status', 'complete', '#000000'),
                ('declared area status', 'underdeclared', '#faad14'),
                ('declared area status', 'overdeclared', '#ff4d4f')
            ";
            $result = $pdo->exec($sql);

            // Check if the records were inserted
            if (false === $result) {
                throw new Exception('Failed to insert records into su_default_colors');
            }

            $output->writeln('Records inserted successfully.');
        } catch (Exception $ex) {
            $output->writeln('Error: ' . $ex->getMessage());
        }
    }
}
