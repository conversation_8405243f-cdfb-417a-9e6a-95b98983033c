<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS3573_8Command run on client databases.
 *
 * This command will create and seed tables su_plot_categories and su_contract_types.
 *  GPS-3642 Move $GLOBALS['Plots']['category'] from config here
 */
class GPS3573_8Command extends UserDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        // Create table su_plot_categories
        $pdo->exec('CREATE TABLE IF NOT EXISTS su_plot_categories (
                id INT PRIMARY KEY,
                title VARCHAR NOT NULL
        )');

        // Seed table su_plot_categories
        $categories = $this->getPlotCategories();
        foreach ($categories as $category) {
            $pdo->exec("INSERT INTO su_plot_categories (id, title) VALUES ({$category['id']}, '{$category['title']}')");
        }

        // Create table su_contract_types
        $pdo->exec('CREATE TABLE IF NOT EXISTS su_contract_types (
                id INT PRIMARY KEY,
                title VARCHAR NOT NULL
        )');

        // Seed table su_contract_types
        foreach ($GLOBALS['Contracts']['ContractTypes'] as $contractType) {
            $pdo->exec("INSERT INTO su_contract_types (id, title) VALUES ({$contractType['id']}, '{$contractType['name']}')");
        }
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3573-8')
            ->setDescription('Create and seed tables su_plot_categories and su_contract_types.');
    }

    private function getPlotCategories(): array
    {
        return [
            '1' => ['id' => '1', 'title' => 'Категория I'],
            '2' => ['id' => '2', 'title' => 'Категория II'],
            '3' => ['id' => '3', 'title' => 'Категория III'],
            '4' => ['id' => '4', 'title' => 'Категория IV'],
            '5' => ['id' => '5', 'title' => 'Категория V'],
            '6' => ['id' => '6', 'title' => 'Категория VI'],
            '7' => ['id' => '7', 'title' => 'Категория VII'],
            '8' => ['id' => '8', 'title' => 'Категория VIII'],
            '9' => ['id' => '9', 'title' => 'Категория IX'],
            '10' => ['id' => '10', 'title' => 'Категория X'],
        ];
    }
}
