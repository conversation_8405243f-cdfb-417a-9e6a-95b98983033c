<?php

namespace TF\Commands\v4_2;

use Exception;
use PDO;
use TF\Application\Common\Config;
use TF\Application\Entity\UserLayers;
use TF\Commands\Common\UserDbCommand;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

class GPS2633_1Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-2633-1')
            ->setDescription('Simplify the geometries of user layers');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        /**
         * @var UserLayers[] $layers
         */
        $layers = UserLayers::finder()->findAllBySql(
            '
            SELECT 
                sul.* 
            FROM 
                su_users_layers AS sul
            JOIN su_users AS su
                ON su.id = sul.user_id
            WHERE
                su.database = :database
        ',
            [
                ':database' => $this->userDbName,
            ]
        );

        $userDbController = new UserDbController($this->userDbName);

        $simplifyGeomTolerance = $GLOBALS['Layers']['simplifyGeomTolerance'];
        foreach ($layers as $layer) {
            $output->writeln("Simplifying geometry for layer '{$layer->table_name}'");

            if (!$userDbController->getTableNameExist($layer->table_name)) {
                $output->warn("Table '{$layer->table_name}' does not exist. Skipping...");

                continue;
            }

            $gidColumn = $layer->getDefinitionByCategory(Config::LAYER_COLUMN_CATEGORY_GID)['col_name'];

            if (!$gidColumn) {
                $output->warn("GID column not found for layer '{$layer->table_name}'. Skipping...");

                continue;
            }

            $stmt = $pdo->prepare("
                UPDATE {$layer->table_name}
                SET geom = simplify_geom(geom, :tolerance)
                WHERE {$gidColumn} = :id
            ");

            $result = false;

            try {
                $result = $stmt->execute([
                    ':id' => $layer->id,
                    ':tolerance' => $simplifyGeomTolerance,
                ]);

                if (!$result) {
                    throw new Exception($stmt->errorInfo()[2]);
                }
            } catch (Exception $e) {
                $output->error("Error simplifying geometry for layer '{$layer->table_name}': " . $e->getMessage());

                continue;
            }

            $output->info('Done!');
        }
    }
}
