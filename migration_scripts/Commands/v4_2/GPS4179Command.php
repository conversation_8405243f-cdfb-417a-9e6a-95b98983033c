<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Application\Common\Config;
use TF\Application\Entity\UserLayers;
use TF\Commands\Common\MainDbCommand;

class GPS4179Command extends MainDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $workLayerType = Config::LAYER_TYPE_WORK_LAYER;
        $defaultDefColumns = UserLayers::getColumns(UserLayers::getDefinitionsByType($workLayerType));
        $defaultDefColumnsStr = "'" . implode("', '", $defaultDefColumns) . "'";

        $pdo->exec("UPDATE su_users_layers
            SET definitions = t.updated_definitions
            FROM (
                SELECT
                    id AS layer_id,
                    jsonb_agg(
                        CASE
                            WHEN def->>'col_name' NOT IN ({$defaultDefColumnsStr}) THEN
                                jsonb_set(def, '{col_visible}', 'true'::jsonb)
                            ELSE
                                def
                        END
                    ) AS updated_definitions
                FROM
                    su_users_layers,
                    jsonb_array_elements(definitions) AS def
                WHERE 
                    layer_type = {$workLayerType}
                GROUP BY 
                    id
            ) AS t (layer_id, updated_definitions)
            WHERE
                id = t.layer_id;
        ");
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-4179')
            ->setDescription('Update work layers definitions - set col_visible to true for non default columns');
    }
}
