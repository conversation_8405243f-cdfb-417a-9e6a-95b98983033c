<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS3675_2Command run on client databases.
 *
 * This command will seed table layer_kvs_borders from layer_kvs
 */
class GPS3675_2Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3675-2')
            ->setDescription('Seed table layer_kvs_borders from layer_kvs');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $output->write('Inserting ekattes in table layer_kvs_borders from layer_kvs...');
        $pdo->exec("INSERT INTO layer_kvs_borders (geom, border_type, ekatte)
            SELECT
                ST_UNION(geom, 0.0001) AS geom,
                'ekatte'::border_type_enum AS border_type,
                ekate as ekatte
            FROM
                layer_kvs
            where geom is not null
            GROUP BY
                ekate
        ");
        $output->writeln("\tDone!");

        $output->write('Inserting masivs in table layer_kvs_borders from layer_kvs...');
        $pdo->exec("INSERT INTO layer_kvs_borders (geom, border_type, ekatte, masiv)
            SELECT
                ST_UNION(geom, 0.0001) AS geom,
                'masiv'::border_type_enum AS border_type,
                ekate as ekatte,
                masiv
            FROM
                layer_kvs
            where geom is not null
            GROUP BY
                ekate,
                masiv
        ");
        $output->writeln("\tDone!");

        $output->write('Converting multipolygons to polygons by getting only the exterior ring...');
        $pdo->exec("UPDATE
                layer_kvs_borders
            SET
                geom = ST_MakePolygon(
                    ST_ExteriorRing(
                        ST_GeometryN(geom , 1)
                    )
                )
            WHERE
                ST_GeometryType(geom) = 'ST_MultiPolygon'
        ");
        $output->writeln("\tDone!");

        $output->write('Remove holes from polygons by getting only their exterior ring...');

        $pdo->exec("UPDATE
                layer_kvs_borders
            SET
                geom = ST_MakePolygon(
                    ST_ExteriorRing(
                        geom
                    )
                )
            WHERE
                ST_GeometryType(geom) = 'ST_Polygon'
        ");
        $output->writeln("\tDone!");

        $output->write('Simplifying ekatte geometries...');
        $pdo->exec("UPDATE
                    layer_kvs_borders
                SET
                    geom = CASE WHEN ST_Simplify(geom, 30) NOTNULL
                        THEN ST_Simplify(geom, 30) 
                        ELSE geom
                    END
                WHERE
                    border_type = 'ekatte'::border_type_enum
            ");
        $output->writeln("\tDone!");

        $output->write('Simplifying masiv geometries...');
        $pdo->exec("UPDATE
                    layer_kvs_borders
                SET
                    geom = CASE WHEN ST_Simplify(geom, 10) NOTNULL
                        THEN ST_Simplify(geom, 10) 
                        ELSE geom
                    END
                WHERE
                    border_type = 'masiv'::border_type_enum
            ");
        $output->writeln("\tDone!");
    }
}
