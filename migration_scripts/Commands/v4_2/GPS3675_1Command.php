<?php

namespace TF\Commands\v4_2;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS3675_1Command run on client databases.
 *
 * This command will create table layer_kvs_borders
 */
class GPS3675_1Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.2:GPS-3675-1')
            ->setDescription('Create table layer_kvs_borders');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec('CREATE TABLE IF NOT EXISTS layer_kvs_borders (
            gid SERIAL,
            geom  GEOMETRY(Geometry, 32635) NOT NULL,
            border_type border_type_enum NOT NULL,
            ekatte VARCHAR(8) NOT NULL,
            masiv VARCHAR(8)
        )');
    }
}
