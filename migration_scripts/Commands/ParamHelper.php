<?php

namespace TF\Commands;

class ParamHelper
{
    public function createQueryParamString($additional_databases, $add_new_user_db = false, $checkPaidSupport = true)
    {
        $queryParamString = '';
        if ($additional_databases) {
            if ('' != $queryParamString) {
                $queryParamString .= ',';
            }
            $queryParamString .= '\'' . implode('\',\'', $additional_databases) . '\'';
        }
        if ($add_new_user_db) {
            $queryParamString = $this->addNewUserDbCondition($queryParamString);
        }

        return $queryParamString;
    }

    public function addNewUserDbCondition($query)
    {
        return $query . ', \'new_user_db\'';
    }

    public function addPayedSupportCondition($query)
    {
        return $query . " AND EXTRACT (YEAR FROM paid_support)='{APP_SUPPORT_YEAR}' ";
    }
}
