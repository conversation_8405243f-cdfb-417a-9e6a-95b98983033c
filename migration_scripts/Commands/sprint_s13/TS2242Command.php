<?php

namespace TF\Commands\sprint_s13;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-2242 command run on all databases.
 */
class TS2242Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s13:TS-2242')
            ->setDescription('dobavq tablica collections');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-2242.sql', $return);
    }
}
