<?php

namespace TF\Commands\sprint_s13;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-2431 command run on all databases.
 */
class TS2431Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s13:TS-2431')
            ->setDescription('Promqna na tipa na kolonata area_type v su_charged_renta_params');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-2431.sql', $return);
    }
}
