<?php

namespace TF\Commands\sprint_s13;

use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class TS2178Command extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('sprint_s13:TS-2178')
            ->setDescription('Dobavq kolona `su_users.track_token`.');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $sql = 'ALTER TABLE su_users ADD COLUMN track_token varchar(100);';
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . DEFAULT_DB_DATABASE . ' -c "' . $sql . '"', $return);
    }
}
