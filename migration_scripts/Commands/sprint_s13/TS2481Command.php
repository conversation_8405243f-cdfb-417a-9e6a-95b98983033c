<?php

namespace TF\Commands\sprint_s13;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-2481 command run on all databases.
 */
class TS2481Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s13:TS-2481')
            ->setDescription('Update na su_contracts.renta ot null na 0');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $userDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
        $sql = $userDev->prepare(
            'UPDATE su_contracts SET renta = 0 WHERE renta IS NULL'
        );

        $sql->execute();
    }
}
