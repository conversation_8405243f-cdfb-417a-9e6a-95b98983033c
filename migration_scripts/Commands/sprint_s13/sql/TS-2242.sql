--creates collections table
CREATE TABLE su_collections (
  "id" int4 NOT NULL,
  "contract_id" int4 NOT NULL,
  "date" date,
  "amount" float,
  "recieved_from" varchar(255),
  "user_name" varchar(255) NOT NULL,
  "bank_payment" bool NOT NULL,
  "payment_order" varchar(255),
  PRIMARY KEY("id")
);

ALTER TABLE public.su_collections OWNER TO postgres;

CREATE SEQUENCE su_collections_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER TABLE public.su_collections_id_seq OWNER TO postgres;
ALTER TABLE "public"."su_collections"
ALTER COLUMN "id" SET DEFAULT nextval('su_collections_id_seq'::regclass);

ALTER SEQUENCE su_collections_id_seq OWNED BY su_collections.id;

ALTER TABLE "public"."su_collections"
ADD CONSTRAINT "contract_id_use_fkey" FOREIGN KEY ("contract_id") REFERENCES "public"."su_contracts" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;
