DROP MATERIALIZED VIEW IF EXISTS ekate_combobox;
CREATE MATERIALIZED VIEW ekate_combobox AS
SELECT DISTINCT
	kvs.ekate,
	e.ekatte_name
FROM
	(
		layer_kvs kvs
		LEFT JOIN dblink (
			'host=127.0.0.1 port=5432 dbname=susi_main user=postgres password=6nuk23' :: TEXT,
			'SELECT ekatte_name, ekatte_code FROM su_ekatte WHERE true' :: TEXT
		) e (
			ekatte_name CHARACTER VARYING,
			ekatte_code CHARACTER VARYING
		) ON (
			(
				(e.ekatte_code) :: TEXT = (kvs.ekate) :: TEXT
			)
		)
	);

DROP MATERIALIZED VIEW IF EXISTS osz_ekatte_combobox;
CREATE MATERIALIZED VIEW osz_ekatte_combobox AS
SELECT DISTINCT
	kvs.ekatte,
	e.ekatte_name
FROM
	(
		su_osz_files_plots kvs
		LEFT JOIN dblink (
			'host=127.0.0.1 port=5432 dbname=susi_main user=postgres password=6nuk23' :: TEXT,
			'SELECT ekatte_name, ekatte_code FROM su_ekatte WHERE true' :: TEXT
		) e (
			ekatte_name CHARACTER VARYING,
			ekatte_code CHARACTER VARYING
		) ON (
			(
				(e.ekatte_code) :: TEXT = (kvs.ekatte) :: TEXT
			)
		)
	);