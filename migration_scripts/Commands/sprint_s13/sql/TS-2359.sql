DROP TABLE IF EXISTS "scripts_log";
SET statement_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SET check_function_bodies = false;
SET client_min_messages = warning;

SET search_path = public, pg_catalog;

SET default_tablespace = '';

SET default_with_oids = false;

--
-- Name: scripts_log; Type: TABLE; Schema: public; Owner: postgres; Tablespace: 
--

CREATE TABLE scripts_log (
    id integer NOT NULL,
    db_name character varying(255) NOT NULL,
    name character varying(255) NOT NULL,
    date TIMESTAMP DEFAULT now() NOT NULL
);


ALTER TABLE public.scripts_log OWNER TO postgres;

--
-- Name: scripts_log_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE scripts_log_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.scripts_log_id_seq OWNER TO postgres;

--
-- Name: scripts_log_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE scripts_log_id_seq OWNED BY scripts_log.id;


--
-- Name: id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY scripts_log ALTER COLUMN id SET DEFAULT nextval('scripts_log_id_seq'::regclass);


--
-- Name: scripts_log_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres; Tablespace: 
--

ALTER TABLE ONLY scripts_log
    ADD CONSTRAINT scripts_log_pkey PRIMARY KEY (id);

