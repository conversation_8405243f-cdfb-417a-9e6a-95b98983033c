<?php

namespace TF\Commands\sprint_s13;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-23311 command run on all databases.
 */
class TS23311Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s13:TS-23311')
            ->setDescription('Proverka na amount v su_transactions i su_transactions_natura za po-golqm i po-malyk ot 9999999.');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $userDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        // su_transactions
        $sqlAmountMax = $userDev->prepare(
            'SELECT char_length(round(max(amount))::TEXT) as amount FROM su_transactions;'
        );

        $sqlAmountMax->execute();
        $result_amount_max = $sqlAmountMax->fetch();

        $sqlAmountMin = $userDev->prepare(
            'SELECT char_length(round(min(amount))::TEXT) as amount FROM su_transactions WHERE amount < 0;'
        );

        $sqlAmountMin->execute();
        $result_amount_min = $sqlAmountMin->fetch();

        // su_transactions_natura
        $sqlAmountNatMax = $userDev->prepare(
            'SELECT char_length(round(max(amount))::TEXT) as amount_nat FROM su_transactions_natura;'
        );

        $sqlAmountNatMax->execute();
        $result_amount_nat_max = $sqlAmountNatMax->fetch();

        $sqlAmountNatMin = $userDev->prepare(
            'SELECT char_length(round(min(amount))::TEXT) as amount_nat FROM su_transactions_natura WHERE amount < 0;'
        );

        $sqlAmountNatMin->execute();
        $result_amount_nat_min = $sqlAmountNatMin->fetch();

        if ($result_amount_max['amount'] > 7
                || $result_amount_min['amount'] > 8
                    || $result_amount_nat_max['amount_nat'] > 7
                        || $result_amount_nat_min['amount_nat'] > 8) {
            $output->writeln("result_amount_max: {$result_amount_max['amount']} \n");
            $output->writeln("result_amount_min: {$result_amount_min['amount']} \n");
            $output->writeln("result_amount_nat_max: {$result_amount_nat_max['amount_nat']} \n");
            $output->writeln("result_amount_nat_min: {$result_amount_nat_min['amount_nat']} \n");
        }
    }
}
