<?php

namespace TF\Commands\sprint_s13;

use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * TS-2359 command run on all databases.
 */
class TS2359Command extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('sprint_s13:TS-2359')
            ->setDescription('Dobavq tablica scripts_log v susi_main.');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . DEFAULT_DB_DATABASE . ' -f ' . __DIR__ . '/sql/TS-2359.sql', $return);
    }
}
