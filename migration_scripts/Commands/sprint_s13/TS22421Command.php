<?php

namespace TF\Commands\sprint_s13;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-2242 command run on all databases.
 */
class TS22421Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s13:TS-22421')
            ->setDescription('Dobavqne na prava za dostap do podmodul Vzemaniq');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        define(COLLECTIONS_RIGHTS_R, 24);
        define(COLLECTIONS_RIGHTS_RW, 25);

        $this->openConnection();

        $sql = $this->mainConnection->prepare(
            "SELECT id FROM su_users u
            WHERE database = {$userDb} AND u.level = 2
            AND paid_support = '2016-01-01'"
        );

        $sql->execute();
        $results = $sql->fetchAll();

        $sql = $this->mainConnection->prepare(
            "SELECT user_id, array_agg(right_id) as rights FROM su_users_rights
            WHERE user_id IN ({$results[0]['id']})
            GROUP BY user_id"
        );

        $sql->execute();
        $userRights = $sql->fetchAll();

        for ($i = 0; $i < count($userRights); $i++) {
            $id = $userRights[$i]['user_id'];
            $output->writeln("User ID: {$id} \n");

            $rights = explode(',', str_replace(['{', '}'], ['', ''], $userRights[$i]['rights']));

            if (!in_array(COLLECTIONS_RIGHTS_R, $rights)) {
                // Добабяне на права за четене на подмодул Вземания
                $insertSql = '
                INSERT INTO su_users_rights VALUES(:id, 24);
                ';

                $insertCmd = $this->mainConnection->prepare($insertSql);
                $insertCmd->bindParam(':id', $id);
                $insertCmd->execute();
            }

            if (!in_array(COLLECTIONS_RIGHTS_RW, $rights)) {
                // Добавяне на права за писане в подмодул Вземания
                $insertSql = '
                INSERT INTO su_users_rights VALUES(:id, 25);
                ';
                $insertCmd = $this->mainConnection->prepare($insertSql);
                $insertCmd->bindParam(':id', $id);
                $insertCmd->execute();
            }
        }
    }
}
