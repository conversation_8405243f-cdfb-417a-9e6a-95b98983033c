<?php

namespace TF\Commands\sprint_s13;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-2378 command run on all databases.
 */
class TS2378Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s13:TS-2378')
            ->setDescription('dropva trigerite pri add update i delete na tablici charged_renta i charged_renta_nat');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-2378.sql', $return);
    }
}
