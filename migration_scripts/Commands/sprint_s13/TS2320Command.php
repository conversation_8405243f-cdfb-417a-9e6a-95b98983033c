<?php

namespace TF\Commands\sprint_s13;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-2320 command run on all databases.
 */
class TS2320Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s13:TS-2320')
            ->setDescription('dobavq mat.view ekate_combobox i osz_ekatte_combobox, ');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-2320.sql', $return);
    }
}
