<?php

namespace TF\Commands\sprint_s13;

use PDO;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * TS-2351 command run on all databases.
 */
class TS2351Command extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('sprint_s13:TS-2351')
            ->setDescription('popravq greshnite dati na dogovorite na db_ivan_chonlov.');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $dbhDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=db_ivan_chonlov;', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = $dbhDev->prepare('SELECT DISTINCT ON (f2.c_num, f2.start_date, f2.due_date, f2.c_date) f2.*
         from import_table f2');

        $sql->execute();
        $results = $sql->fetchAll();

        foreach ($results as $contract) {
            $bad_c_date = $contract['c_date'];
            $good_c_date = $contract['c_date'];
            if ($contract['c_date']) {
                $bad_c_date = $this->createBadDate($contract['c_date']);
                $good_c_date = date('Y-m-d', strtotime($contract['c_date']));
            }

            $bad_start_date = $contract['start_date'];
            $good_start_date = $contract['start_date'];
            if ($contract['start_date']) {
                $bad_start_date = $this->createBadDate($contract['start_date']);
                $good_start_date = date('Y-m-d', strtotime($contract['start_date']));
            }

            $bad_due_date = $contract['due_date'];
            $good_due_date = $contract['due_date'];
            if ($contract['due_date']) {
                $bad_due_date = $this->createBadDate($contract['due_date']);
                $good_due_date = date('Y-m-d', strtotime($contract['due_date']));
            }

            $bad_sv_date = $contract['sv_date'];
            $good_sv_date = $contract['sv_date'];
            if ($contract['sv_date']) {
                $bad_sv_date = $this->createBadDate($contract['sv_date']);
                $good_sv_date = date('Y-m-d', strtotime($contract['sv_date']));
            }

            $sqlString = 'update su_contracts set ';
            if ($good_c_date) {
                $sqlString .= " c_date = '{$good_c_date}',";
            } else {
                $sqlString .= ' c_date = NULL,';
            }

            if ($good_due_date) {
                $sqlString .= " due_date = '{$good_due_date}',";
            } else {
                $sqlString .= ' due_date = NULL,';
            }

            if ($good_start_date) {
                $sqlString .= " start_date = '{$good_start_date}',";
            } else {
                $sqlString .= ' start_date = NULL,';
            }

            if ($good_sv_date) {
                $sqlString .= " sv_date = '{$good_sv_date}'";
            } else {
                $sqlString .= ' sv_date = NULL';
            }

            $sqlString .= ' where true';

            if ($contract['c_num']) {
                $sqlString .= " AND c_num  = '{$contract['c_num']}'";
            }

            if ($bad_c_date) {
                $sqlString .= " AND c_date  = '{$bad_c_date}'";
            }
            if ($bad_start_date) {
                $sqlString .= " AND start_date  = '{$bad_start_date}'";
            }
            if ($bad_due_date) {
                $sqlString .= " AND due_date  = '{$bad_due_date}'";
            }
            if ($bad_sv_date) {
                $sqlString .= " AND sv_date  = '{$bad_sv_date}'";
            }

            $updateSql = $dbhDev->prepare($sqlString);

            if (!$updateSql->execute()) {
                $error = $updateSql->errorInfo();
                var_export($contract['c_num']);
                var_export($error[2]);
            }
        }
    }

    private function createBadDate($string)
    {
        $piece = explode('-', $string);
        $day = $piece[0];
        $month = $piece[1];
        $year = $piece[2];

        return date('Y-m-d', mktime(0, 0, 0, $month, $day, $year));
    }
}
