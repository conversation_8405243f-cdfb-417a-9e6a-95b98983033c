<?php

namespace TF\Commands\sprint_s13;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-2331 command run on all databases.
 */
class TS2331Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s13:TS-2331')
            ->setDescription('Promqna na tipa na kolonata "amount" v su_transactions, su_transactions_natura, su_payments, su_payments_natura.');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-2331.sql', $return);
    }
}
