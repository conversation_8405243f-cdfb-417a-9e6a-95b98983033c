<?php

namespace TF\Commands\v4_1;

use PDO;
use TF\Application\Common\Config;
use TF\Commands\Common\MainDbCommand;

/**
 * GPS3540_1Command run on susi_main database.
 *
 * This command fixes definitions for isak layers
 */
class GPS3540_1Command extends MainD<PERSON>Command
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.1:GPS-3540-1')
            ->setDescription('This command fixes definitions for isak layers');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $colCategoryNumber = Config::LAYER_COLUMN_CATEGORY_NUMBER;
        $layerTypeIsak = Config::LAYER_TYPE_ISAK;

        $sql = "WITH layers_fixed_definitions AS 
            (
            
                SELECT
                    sul.id AS user_layer_id,
                    sul.definitions,
                    jsonb_set(sul.definitions, ARRAY[(idx-1)::TEXT, 'col_category']::TEXT[], '\"{$colCategoryNumber}\"'::jsonb, false) AS fixed_definitions
                FROM
                    su_users_layers sul,
                    jsonb_array_elements(sul.definitions) WITH ordinality AS def(elem, idx)
                WHERE
                    sul.definitions @> '[{\"col_name\": \"watering\"}]'
                    AND def.elem->>'col_name' = 'watering'
                    AND sul.layer_type = {$layerTypeIsak}

            )
            UPDATE su_users_layers
            SET definitions = layers_fixed_definitions.fixed_definitions
            FROM layers_fixed_definitions
            WHERE
                id = layers_fixed_definitions.user_layer_id
        ";

        $stmt = $pdo->prepare($sql);
        $stmt->execute();
    }
}
