<?php

namespace TF\Commands\v4_1;

use PDO;
use <PERSON>OException;
use TF\Commands\Common\MainDbCommand;

/**
 * GPS2964Command run on susi_main database.
 *
 * This command will add column 'uuid' to table su_users_farming if not exists
 */
class GPS3169_1Command extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.1:GPS-3169_1')
            ->setDescription('NUZ table seeder');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        try {
            $output->writeln("Table 'su_nuz' created successfully.");

            // Open the CSV file
            if (($handle = fopen('public/files/nuz/nuz.csv', 'r')) !== false) {
                // Skip the header row
                fgetcsv($handle);

                // Prepare the INSERT statement
                $stmt = $pdo->prepare('
                    INSERT INTO su_nuz (ekatte, location, district, municipality) 
                    VALUES (:ekatte, :location, :district, :municipality)
                    ');

                // Loop through the file
                while (($data = fgetcsv($handle, 1000, ';')) !== false) {
                    array_splice($data, 1, 1);

                    $data[0] = str_pad($data[0], 5, '0', STR_PAD_LEFT);

                    // Execute the INSERT statement
                    $stmt->bindParam(':ekatte', $data[0]);
                    $stmt->bindParam(':location', $data[1]);
                    $stmt->bindParam(':district', $data[2]);
                    $stmt->bindParam(':municipality', $data[3]);
                    $stmt->execute();
                }

                // Close the file
                fclose($handle);

                $output->writeln('Data imported successfully.');
            } else {
                $output->writeln('Error opening file.');
            }

            $output->writeln("Table 'su_nuz' seeded successfully.");
        } catch (PDOException $e) {
            $output->writeln('Error: ' . $e->getMessage());
        }
    }
}
