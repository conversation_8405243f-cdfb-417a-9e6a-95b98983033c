<?php

namespace TF\Commands\v4_1;

use PDO;
use TF\Commands\Common\MainDbCommand;

/**
 * GPS3481_9Command run on susi_main database.
 *
 * This command will create index for ekatte_code in su_ekatte and crop_code in su_crop_codes
 */
class GPS3481_9Command extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.1:GPS-3481-9')
            ->setDescription('Create index for ekatte_code in su_ekatte and crop_code in su_crop_codes');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $ekatteIdxStmt = $pdo->prepare('CREATE INDEX IF NOT EXISTS su_ekatte_code_idx ON su_ekatte USING hash (ekatte_code)');
        $ekatteIdxStmt->execute();

        $cropIdxStmt = $pdo->prepare('CREATE INDEX IF NOT EXISTS su_crop_code_idx ON su_crop_codes USING hash (crop_code)');
        $cropIdxStmt->execute();
    }
}
