<?php

namespace TF\Commands\v4_1;

use TF\Commands\Common\UserDbCommand;

/**
 * Runs all scripts required for migration from Version 4.0 to 4.1.
 */
class UpdateToVersion4_1 extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.1:update-to-version-4.1')
            ->setDescription('Runs all scripts required for migration from version 4.0 to version 4.1');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $commandsArr = [
            new GPS2453Command(),
            new GPS2618Command(),
            new TS6585_1Command(),
            new GPS2818Command(),
            new GPS2964Command(),
            new GPS2964_1Command(),
            new GPS2942_1Command(),
            new GPS2942_2Command(),
            new GPS2942_3Command(),
            new GPS2942_4Command(),
            new GPS2942_5Command(),
            new TS6711Command(),
            new TS6711_1Command(),
            new GPS3169Command(),
            new GPS3169_1Command(),
            new GPS3227Command(),
            new GPS3255_1Command(),
            new GPS3255_2Command(),
            new GPS3255_3Command(),
            new GPS3255_4Command(),
            new GPS3401Command(),
            new GPS3481_1Command(),
            new GPS3481_2Command(),
            new GPS3481_3Command(),
            new GPS3481_4Command(),
            new GPS3481_5Command(),
            new GPS3481_6Command(),
            new GPS3481_7Command(),
            new GPS3481_8Command(),
            new GPS3481_9Command(),
            new GPS3481_10Command(),
            new GPS3481_11Command(),
            new GPS3437Command(),
            new GPS3519Command(),
            new GPS3254_1Command(),
            new GPS3254_2Command(),
            new GPS3254_3Command(),
            new GPS3540Command(),
            new GPS3540_1Command(),
            new GPS3540_2Command(),
            new GPS3549Command(),
        ];

        $count = count($commandsArr);
        for ($i = 0; $i < $count; $i++) {
            $command = $commandsArr[$i];
            $this->logScript($userDb, $command->getName());
            $output->writeln('Executing command: ' . $command->getName());
            $command->onDbExecute($userDb, $output, $input);
        }
    }

    protected function logScript($user_db, $name = '')
    {
        $date = date('Y-m-d H:i:s');

        $this->openConnection();

        $sql = $this->mainConnection->prepare("
            INSERT INTO scripts_log (db_name, name, date)
            VALUES ('{$user_db}', '{$name}', '{$date}')
        ");

        return $sql->execute();
    }
}
