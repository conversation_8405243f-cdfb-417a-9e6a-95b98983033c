<?php

namespace TF\Commands\v4_1;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS3481_6Command run on user databases.
 *
 * This command will create db function returning crop_name by ekatte_code
 */
class GPS3481_6Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.1:GPS-3481-6')
            ->setDescription('Create db function returning ekatte_name by ekatte_name in user databases');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $sql = "CREATE OR REPLACE FUNCTION get_ekatte_name_by_code(code VARCHAR)
        RETURNS VARCHAR IMMUTABLE AS
        $$
            -- Note: If you use this function in virtual column, in order to refresh the calculated value
            -- you need to update the virtual column - for example: UPDATE {table} set {virtual_column_name} = {virtual_column_name}
            DECLARE res VARCHAR;
            BEGIN
                SELECT 
                    ekatte_name || ' (' || ekatte_code || ')' INTO res
                FROM
                    public.su_ekatte
                WHERE
                    ekatte_code = code;
                RETURN res;
            END;
        $$ LANGUAGE plpgsql;";

        $stmt = $pdo->prepare($sql);
        $stmt->execute();
    }
}
