<?php

namespace TF\Commands\v4_1;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS2942_3Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.1:GPS-2942_3')
            ->setDescription('Create tables su_consolidation_do_dog_kod, su_consolidation_zd_kod, su_consolidation_zd_dog_kod and su_consolidation_zd_jelanie');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $this->executeSql($pdo, __DIR__ . '/sql/GPS-2942_3.sql');
    }
}
