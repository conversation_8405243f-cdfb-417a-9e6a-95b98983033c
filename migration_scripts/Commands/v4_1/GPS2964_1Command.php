<?php

namespace TF\Commands\v4_1;

use PDO;
use TF\Commands\Common\MainDbCommand;

/**
 * GPS2964_1Command run on susi_main database.
 *
 * This command will fill column su_users.gs_organization_id for users with "level" 2 and "database" matching 'db_bg_[0-9]+'
 */
class GPS2964_1Command extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.1:GPS-2964_1')
            ->setDescription("Fill column su_users.gs_organization_id for users with \"level\" 2 and \"database\" matching 'db_bg_[0-9]+'");
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $stmt = $pdo->prepare("UPDATE su_users
            SET gs_organization_id = substring(\"database\" FROM '[0-9]+')::int
            WHERE
                \"level\" = 2
                AND gs_organization_id ISNULL
                AND \"database\" SIMILAR TO 'db_bg_[0-9]+'
        ");
        $stmt->execute();
    }
}
