<?php

namespace TF\Commands\v4_1;

use PDO;
use TF\Application\Common\Config;
use TF\Application\Entity\UserLayers;
use TF\Commands\Common\MainDbCommand;

/**
 * GPS3401Command run on susi_main database.
 *
 * This command will update definitions column in su_users_layers.
 * If the column is empty the definitions are get by layer type,
 * otherwise the column is updated with new column
 */
class GPS3401Command extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.1:GPS-3401')
            ->setDescription('Set layer definitions in su_users_layers');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $output->writeln('Updating non work layer definitions... ');
        $this->updateNonWorkLayers($pdo);
        $output->writeln('Updating work layer definitions... ');
        $this->updateWorkLayers($pdo);
        $output->writeln('Done!');
    }

    private function updateNonWorkLayers(PDO $pdo)
    {
        /**
         * @var array $excludedLayerTypes Layers that will not be updated
         *
         * Layers of type work will be updated separately
         * Layers ALLOWABLE_FINAL and PHYSICAL_BLOCKS are not included in su_users_layers
         * Layers of type DSS and SATELLITE_WORK are not used anymode
         */
        $excludedLayerTypes = [
            Config::LAYER_TYPE_WORK_LAYER,
            Config::LAYER_TYPE_PHYSICAL_BLOCKS,
            Config::LAYER_TYPE_ALLOWABLE_FINAL,
            Config::LAYER_TYPE_DSS,
            Config::LAYER_TYPE_SATELLITE_WORK,
        ];

        $definitionsByType = $GLOBALS['Layers']['definitions'];

        $definitionsValuesSql = '';
        foreach ($definitionsByType as $type => $definitions) {
            if (in_array($type, $excludedLayerTypes)) {
                continue;
            }

            $definitionsJson = json_encode($definitions);
            $definitionsValuesSql .= "({$type}, '{$definitionsJson}'::jsonb),";
        }
        $definitionsValuesSql .= rtrim($definitionsValuesSql, ',');
        $definitionsByTypeSql = "SELECT * FROM (VALUES {$definitionsValuesSql}) as data (layer_type, definitions)";

        $updateDefinitionsSql = "WITH col_definitions AS ({$definitionsByTypeSql})
            UPDATE su_users_layers
            SET
                definitions = col_definitions.definitions
            FROM
                 col_definitions 
            WHERE 
                su_users_layers.layer_type = col_definitions.layer_type
        ";

        $stmt = $pdo->prepare($updateDefinitionsSql);
        $stmt->execute();
    }

    private function updateWorkLayers(PDO $pdo)
    {
        $workLayerType = Config::LAYER_TYPE_WORK_LAYER;
        $layerColumnCategoryGid = Config::LAYER_COLUMN_CATEGORY_GID;
        $layerColumnCategoryName = Config::LAYER_COLUMN_CATEGORY_NAME;
        $layerColumnCategoryGeom = Config::LAYER_COLUMN_CATEGORY_GEOM;
        $layerColumnCategorySlope = Config::LAYER_COLUMN_CATEGORY_SLOPE;
        $layerColumnCategoryText = Config::LAYER_COLUMN_CATEGORY_TEXT;
        $layerColumnCategoryNumber = Config::LAYER_COLUMN_CATEGORY_NUMBER;
        $layerColumnCategoryBoolean = Config::LAYER_COLUMN_CATEGORY_BOOLEAN;
        $layerColumnCategoryDate = Config::LAYER_COLUMN_CATEGORY_DATE;
        $workLayerDefiniton = UserLayers::getDefinitionsByType(Config::LAYER_TYPE_WORK_LAYER);
        $workLayerDefiniton = array_combine(array_column($workLayerDefiniton, 'col_name'), $workLayerDefiniton);
        $workLayerDefinitonJson = json_encode($workLayerDefiniton);
        $multipleSelectionType = Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE;

        $updateDefinitionsSql = "WITH 
            user_layers AS (
                SELECT 
                    sul.id AS layer_id,
                    sul.user_id,
                    sul.table_name,
                    su.\"database\",
                    sul.layer_type,
                    sul.definitions,
                    def.col_name,
                    def.col_title,
                    def.col_visible
                FROM
                    su_users_layers AS sul
                JOIN su_users AS su
                    ON su.id = sul.user_id
                JOIN pg_catalog.pg_database AS pgdb
                    ON pgdb.datname = su.\"database\"
                LEFT JOIN LATERAL (
                        SELECT * FROM JSONB_TO_RECORDSET(sul.definitions) AS def (col_name varchar, col_title varchar, col_visible boolean)
                    ) as def ON TRUE
                WHERE 
                    sul.layer_type = {$workLayerType}
            ),
            layer_table_columns AS (
                SELECT DISTINCT 
                    work_layer_table.table_name,
                    work_layer_table.col_name,
                    work_layer_table.col_type,
                    work_layer_table.col_category,
                    ul.\"database\",
                    ul.layer_id
                FROM
                    user_layers AS ul
                JOIN dblink(
                        'host=" . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . " dbname=' || ul.\"database\",
                        $$
                            SELECT 
                                info_sch.table_name,
                                info_sch.column_name AS col_name,
                                info_sch.udt_name AS col_type,
                                CASE 
                                    WHEN info_sch.column_name = 'slope' 
                                        THEN '{$layerColumnCategorySlope}'
                                    WHEN info_sch.udt_name = 'varchar' AND info_sch.column_name = 'name'
                                        THEN '{$layerColumnCategoryName}'
                                    WHEN info_sch.udt_name = 'numeric' OR info_sch.udt_name ilike 'int%' OR info_sch.udt_name ilike 'float%'
                                        THEN 
                                            CASE WHEN info_constr.constraint_type = 'PRIMARY_KEY'
                                                THEN '{$layerColumnCategoryGid}'
                                                ELSE '{$layerColumnCategoryNumber}'
                                            END
                                    WHEN info_sch.udt_name = 'geometry'
                                        THEN '{$layerColumnCategoryGeom}'
                                    WHEN info_sch.udt_name = 'bool'
                                        THEN '{$layerColumnCategoryBoolean}'
                                    WHEN info_sch.udt_name = 'timestamp'
                                        THEN '{$layerColumnCategoryDate}'
                                    ELSE '{$layerColumnCategoryText}'
                                END AS col_category
                            FROM 
                                information_schema.COLUMNS AS info_sch
                            JOIN information_schema.table_constraints AS info_constr
                            	ON info_constr.table_name = info_sch.table_name
                            WHERE 
                                info_sch.table_name='$$ || ul.table_name || $$'
                        $$
                    ) AS work_layer_table(table_name varchar, col_name varchar, col_type varchar, col_category varchar)
                        ON ul.table_name = work_layer_table.table_name
            ),
            layer_definitions AS  (
                SELECT
                    COALESCE(ul.layer_id, ltc.layer_id) AS user_layer_id,
                    jsonb_agg(
                        CASE WHEN ('{$workLayerDefinitonJson}'::JSONB)->COALESCE(ul.col_name, ltc.col_name) NOTNULL
                            THEN ('{$workLayerDefinitonJson}'::JSONB)->COALESCE(ul.col_name, ltc.col_name)
                            ELSE
                                jsonb_build_object(
                                        'col_name', COALESCE(ul.col_name, ltc.col_name),
                                        'col_title', COALESCE(ul.col_title, ltc.col_name),
                                        'col_visible', COALESCE(ul.col_visible, true),
                                        'col_personalizable', true,
                                        'col_category', COALESCE(ltc.col_category, 'text'),
                                        'col_multiedit', true,
                                        'col_singleedit', true,
                                        'col_sortable', true,
                                        'col_exportable', true,
                                        'col_copyable', true,
                                        'col_virtual', false,
                                        'col_expression', null,
                                        'col_filter_selection_type', '{$multipleSelectionType}'
                                )
                        END
                    )  AS new_definitions
                FROM
                    user_layers AS ul
                FULL JOIN layer_table_columns AS ltc
                    ON ltc.table_name = ul.table_name
                    AND ltc.layer_id = ul.layer_id
                    AND ltc.col_name = ul.col_name
                WHERE
                	ltc.col_name NOTNULL OR ul.col_name NOTNULL
                GROUP BY 
                    COALESCE(ul.layer_id, ltc.layer_id)
            )
            UPDATE su_users_layers
                SET definitions = ld.new_definitions
            FROM
                layer_definitions AS ld
            WHERE id = ld.user_layer_id
        ";

        $stmt = $pdo->prepare($updateDefinitionsSql);
        $stmt->execute();
    }
}
