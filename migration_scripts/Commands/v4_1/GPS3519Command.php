<?php

namespace TF\Commands\v4_1;

use Exception;
use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS3519Command run on susi_main database.
 *
 * The command updates the 'slope' column in user databases,
 * changing its type from 'varchar' to 'double precision'.
 */
class GPS3519Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.1:GPS-3519')
            ->setDescription('This command will update the slope column type to double precision in all layers in the user databases.');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $sql = "SELECT
            string_agg(format('ALTER TABLE %s ALTER COLUMN slope TYPE double precision USING (slope::double precision);', table_name), '\n') \"tables\"
            FROM information_schema.COLUMNS
        WHERE
            column_name='slope' and udt_name='varchar'";

        $output->writeln('Updating slope type...');
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $alterTablesSQL = $stmt->fetchColumn(0);
        if (!$alterTablesSQL) {
            $output->warn('No layers with slope column found.');

            return;
        }
        // Start the transaction
        $pdo->beginTransaction();

        try {
            // Execute the ALTER TABLE queries
            $pdo->exec($alterTablesSQL);

            // Commit the transaction
            $pdo->commit();
        } catch (Exception $e) {
            // An error occurred, rollback the transaction
            $pdo->rollBack();
            $output->error("\tError: " . $e->getMessage());

            return;
        }
        $output->writeln("\tDone!");
    }
}
