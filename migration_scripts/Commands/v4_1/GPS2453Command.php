<?php

namespace TF\Commands\v4_1;

use PDO;
use TF\Commands\Common\MainDbCommand;

/**
 * GPS2453Command run on all databases.
 *
 * This command will add properties 'coloring_type', 'coloring_attribute' and 'coloring_attribute_layer' to the json column 'style' in table su_users_layers
 */
class GPS2453Command extends MainDbCommand
{
    protected function configure()
    {
        /*
         * Update the json structure from:
         *
         *   "00031": {
         *        "tags": 1,
         *        "color": "ecc8ff",
         *        "label_name": [
         *          "kad_ident"
         *        ],
         *        "label_size": 8,
         *        "border_only": 0,
         *        "border_color": "5eb50d",
         *        "transparency": 50
         *   }
         *
         * to:
         *
         *   "00031": {
         *         "tags": 1,
         *         "color": "ecc8ff",
         *         "label_name": [
         *           "kad_ident"
         *         ],
         *         "label_size": 8,
         *         "border_only": 0,
         *         "border_color": "5eb50d",
         *         "transparency": 50
         *         "coloring_type": "single",
         *         "coloring_attribute": null,
         *         "coloring_attribute_layer": null
         *   }
         *
         */
        parent::configure();
        $this
            ->setName('v4.1:GPS-2453')
            ->setDescription('Add properties \'coloring_type\', \'coloring_attribute\' and \'coloring_attribute_layer\' to the json column \'style\' in table su_users_layers');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $sql = "WITH 
            layer_new_style AS (
                SELECT
                    sul.id,
                    CASE WHEN sul.table_name = 'layer_kvs'
                        THEN
                            jsonb_object_agg(
                                kvs.ekate,
                                kvs.\"style\"::jsonb || jsonb_build_object(
                                    'coloring_type', 'single',
                                    'coloring_attribute', NULL,
                                    'coloring_attribute_layer', NULL
                                ) 
                            )
                        ELSE
                            sul.\"style\"::jsonb ||
                            jsonb_build_object(
                                'coloring_type', 'single',
                                'coloring_attribute', NULL,
                                'coloring_attribute_layer', null
                            )
                        END AS \"style\"
                FROM
                    su_users_layers AS sul,
                    json_each(sul.\"style\") AS kvs(ekate, \"style\")
                WHERE
                    sul.\"style\" NOTNULL AND sul.\"style\"::jsonb <> 'null'::jsonb
                GROUP BY
                    sul.id,
                    sul.table_name,
                    sul.\"style\"::jsonb
            )
            UPDATE
                su_users_layers AS sul
            SET 
                \"style\" = lns.\"style\"::json
            FROM
                layer_new_style lns
            WHERE
                sul.id = lns.id
        ";

        $stmt = $pdo->prepare($sql);
        $stmt->execute();
    }
}
