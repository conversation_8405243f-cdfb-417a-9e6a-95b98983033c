<?php

namespace TF\Commands\v4_1;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS3481_4Command Command run on all client databases database.
 *
 * This command will refresh the materialized view su_crop_codes
 */
class GPS3481_4Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.1:GPS-3481-4')
            ->setDescription('Refresh materialized view su_crop_codes');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $sql = 'REFRESH MATERIALIZED VIEW su_crop_codes';
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
    }
}
