<?php

namespace TF\Commands\v4_1;

use PDO;
use TF\Commands\Common\MainDbCommand;

/**
 * GPS3481_1Command  run on susi_main database.
 *
 * This command will create GIN index for the definitions column of the su_users_layers table.
 */
class GPS3481_1Command extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.1:GPS-3481-1')
            ->setDescription('Create GIN index for the definitions column of the su_users_layers table');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $sql = 'CREATE INDEX su_users_layers_definitions_idx  ON su_users_layers USING GIN (definitions)';
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
    }
}
