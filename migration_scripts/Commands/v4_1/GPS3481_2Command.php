<?php

namespace TF\Commands\v4_1;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS3481_2Command run on user databases.
 *
 * This command will create materialized views for tables su_ekatte and su_crop_codes
 */
class GPS3481_2Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.1:GPS-3481-2')
            ->setDescription('Create materialized views for tables su_ekatte and su_crop_codes');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $dbLink = 'host=' . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD;

        $cropsViewSql = "CREATE MATERIALIZED VIEW IF NOT EXISTS su_crop_codes AS 
             SELECT 
                id,
                crop_code,
                crop_name,
                crop_rotation,
                is_tree_short_rotation,
                priority,
                azot,
                fosfor,
                kalii,
                no_pndn,
                year,
                general_code,
                azot_fixed_crop,
                is_intermediate_crop,
                is_intermediate_weat_crop,
                crop_genus,
                crop_descr,
                crop_species,
                crop_type,
                crop_family
            FROM
                dblink(
                    '{$dbLink}',
                    $$
                        SELECT 
                            id,
                            crop_code,
                            crop_name,
                            crop_rotation,
                            is_tree_short_rotation,
                            priority,
                            azot,
                            fosfor,
                            kalii,
                            no_pndn,
                            year,
                            general_code,
                            azot_fixed_crop,
                            is_intermediate_crop,
                            is_intermediate_weat_crop,
                            crop_genus,
                            crop_descr,
                            crop_species,
                            crop_type,
                            crop_family
                        FROM
                            su_crop_codes
                    $$
                ) AS crops(
                    id INT4,
                    crop_code VARCHAR,
                    crop_name VARCHAR,
                    crop_rotation BOOL,
                    is_tree_short_rotation BOOL,
                    priority INT4,
                    azot FLOAT8,
                    fosfor FLOAT8,
                    kalii FLOAT8,
                    no_pndn BOOL,
                    year INT4,
                    general_code BOOL,
                    azot_fixed_crop BOOL,
                    is_intermediate_crop BOOL,
                    is_intermediate_weat_crop BOOL,
                    crop_genus VARCHAR,
                    crop_descr VARCHAR,
                    crop_species VARCHAR,
                    crop_type VARCHAR,
                    crop_family VARCHAR
                )
            WITH DATA";

        $stmt = $pdo->prepare($cropsViewSql);
        $stmt->execute();

        $ekattesViewSql = "CREATE MATERIALIZED VIEW IF NOT EXISTS su_ekatte AS 
            SELECT 
                id,
                ekatte_code,
                ekatte_name,
                nm_ate_cat_id,
                ekatte_kind_code,
                nm_kmetst_id,
                lfa_id,
                nm_altitut_id,
                date_from,
                date_to,
                lfa_1_changed,
                lfa_2_changed,
                ekatte_kati_code,
                pcode,
                descr
            FROM
                dblink(
                    '{$dbLink}',
                    $$
                        SELECT 
                            id,
                            ekatte_code,
                            ekatte_name,
                            nm_ate_cat_id,
                            ekatte_kind_code,
                            nm_kmetst_id,
                            lfa_id,
                            nm_altitut_id,
                            date_from,
                            date_to,
                            lfa_1_changed,
                            lfa_2_changed,
                            ekatte_kati_code,
                            pcode,
                            descr
                        FROM
                            su_ekatte
                    $$
                ) AS ekattes(
                    id INT4,
                    ekatte_code VARCHAR,
                    ekatte_name VARCHAR,
                    nm_ate_cat_id INT4,
                    ekatte_kind_code INT4,
                    nm_kmetst_id INT4,
                    lfa_id INT4,
                    nm_altitut_id INT4,
                    date_from VARCHAR,
                    date_to VARCHAR,
                    lfa_1_changed VARCHAR,
                    lfa_2_changed VARCHAR,
                    ekatte_kati_code VARCHAR,
                    pcode VARCHAR,
                    descr VARCHAR
                )
            WITH DATA";

        $stmt = $pdo->prepare($ekattesViewSql);
        $stmt->execute();

        $ekatteIdxStmt = $pdo->prepare('CREATE INDEX IF NOT EXISTS su_ekatte_code_idx ON su_ekatte USING hash (ekatte_code)');
        $ekatteIdxStmt->execute();

        $cropIdxStmt = $pdo->prepare('CREATE INDEX IF NOT EXISTS su_crop_code_idx ON su_crop_codes USING hash (crop_code)');
        $cropIdxStmt->execute();
    }
}
