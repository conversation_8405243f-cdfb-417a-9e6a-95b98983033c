<?php

namespace TF\Commands\v4_1;

use PDO;
use TF\Application\Common\Config;
use TF\Commands\Common\MainDbCommand;

/**
 * GPS3549Command run on susi_main database.
 *
 * This command will set col_personalizable to false for column slope for work layers in su_users_layers
 */
class GPS3549Command extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.1:GPS-3549')
            ->setDescription('Set col_personalizable to false for column slope for work layers in su_users_layers');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $layerColumnCategorySlope = Config::LAYER_COLUMN_CATEGORY_SLOPE;
        $workLayerType = Config::LAYER_TYPE_WORK_LAYER;

        $sql = "WITH layers_fixed_definitions AS (
                SELECT
                    sul.id AS user_layer_id,
                    jsonb_set(sul.definitions, ARRAY[(idx-1)::TEXT, 'col_personalizable']::TEXT[], 'false'::jsonb, false) AS fixed_definitions
                FROM
                    su_users_layers sul
                JOIN LATERAL (
                            SELECT * FROM jsonb_array_elements(sul.definitions) WITH ordinality AS def(elem, idx)
                        )as def(definition, idx) ON TRUE
                WHERE
                    sul.definitions @> '[{\"col_category\": \"{$layerColumnCategorySlope}\"}]'
                    AND def.definition->>'col_category' = '{$layerColumnCategorySlope}'
                    AND sul.layer_type = {$workLayerType}	
            )
            UPDATE su_users_layers
            SET definitions = layers_fixed_definitions.fixed_definitions
            FROM layers_fixed_definitions
            WHERE
                id = layers_fixed_definitions.user_layer_id
        ";

        $stmt = $pdo->prepare($sql);
        $stmt->execute();
    }
}
