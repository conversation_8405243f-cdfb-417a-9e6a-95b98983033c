<?php

namespace TF\Commands\v4_1;

use PDO;
use TF\Application\Common\Config;
use TF\Commands\Common\MainDbCommand;

/**
 * GPS3437Command run on susi_main database.
 *
 * This command will add property border_width to the style column in user layers table.
 */
class GPS3437Command extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.1:GPS-3437')
            ->setDescription('Add property border_width to the style column in user layers table');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $defaultBorderWidth = Config::LAYER_BOUNDARY_DEFAULT_BORDER_WIDTH;
        $kvsLayerType = Config::LAYER_TYPE_KVS;

        $layersCreateVirtualColumnsSql = "WITH users_layers_styles AS
            (
                SELECT
                    sul.id,
                    CASE WHEN layer_type = {$kvsLayerType}
                        THEN
                            jsonb_object_agg( 
                                kvs.ekatte,
                                kvs.\"style\"::jsonb || jsonb_build_object('border_width', {$defaultBorderWidth})
                            )::json
                        ELSE 
                            (sul.\"style\"::jsonb || jsonb_build_object('border_width', {$defaultBorderWidth}))::json
                    END AS new_style
                FROM 
                    su_users_layers AS sul,
                    json_each(sul.\"style\") AS kvs(ekatte, \"style\")
                WHERE 
                    sul.\"style\" NOTNULL 
                    AND sul.\"style\"::jsonb <> 'null'::jsonb 
                    AND sul.\"style\"::jsonb <> '{}'::jsonb
                GROUP BY 
                    sul.id
            )
            UPDATE su_users_layers AS sul
            SET \"style\" = uls.new_style
            FROM users_layers_styles AS uls
            WHERE sul.id = uls.id
        ";
        $stmt = $pdo->prepare($layersCreateVirtualColumnsSql);
        $stmt->execute();
    }
}
