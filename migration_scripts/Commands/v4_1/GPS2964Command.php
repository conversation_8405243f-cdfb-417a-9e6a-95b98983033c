<?php

namespace TF\Commands\v4_1;

use PDO;
use TF\Commands\Common\MainDbCommand;

/**
 * GPS2964Command run on susi_main database.
 *
 * This command will add column 'uuid' to table su_users_farming if not exists
 */
class GPS2964Command extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.1:GPS-2964')
            ->setDescription("Add column 'uuid' to table su_users_farming if not exists");
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $columnExistsStmt = $pdo->prepare("SELECT
                true
            FROM 
                information_schema.columns
            WHERE 
                table_name = 'su_users_farming'
                AND table_schema = 'public'
                AND column_name = 'uuid'
        ");
        $columnExistsStmt->execute();

        $columnExists = $columnExistsStmt->fetch();

        if ($columnExists) {
            return;
        }

        $addUuidColumnStmt = $pdo->prepare('ALTER TABLE su_users_farming ADD COLUMN "uuid" UUID DEFAULT NULL');
        $addUuidColumnStmt->execute();
    }
}
