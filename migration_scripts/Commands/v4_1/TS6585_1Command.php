<?php

namespace TF\Commands\v4_1;

use PDO;
use TF\Commands\Common\MainDbCommand;

/**
 * TS-6585_1 command run on susi_main database.
 */
class TS6585_1Command extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.1:TS-6585_1')
            ->setDescription('Add latest banks bic codes');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $this->executeSql($pdo, __DIR__ . '/sql/TS-6585_1.sql');
    }
}
