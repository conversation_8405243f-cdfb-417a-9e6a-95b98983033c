<?php

namespace TF\Commands\v4_1;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS-3255 command.
 */
class GPS3255_3Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.1:GPS-3255_3')
            ->setDescription('Create countries view');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $dropViewSql = 'DROP VIEW IF exists countries';
        $stmt = $pdo->prepare($dropViewSql);
        $stmt->execute();

        $dbLink = 'host=' . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD;
        $createViewSql = "CREATE VIEW countries AS
        SELECT * FROM dblink('{$dbLink}', 'SELECT id, name, bg_name, iso_alpha_2_code, iso_alpha_3_code, active FROM countries')
        AS countries(id INTEGER, name VARCHAR(255), bg_name VARCHAR(255), iso_alpha_2_code CHAR(2), iso_alpha_3_code CHAR(3), active BOOLEAN)";

        $stmt = $pdo->prepare($createViewSql);
        $stmt->execute();
    }
}
