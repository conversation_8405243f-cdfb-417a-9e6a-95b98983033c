<?php

namespace TF\Commands\v4_1;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS2942_4Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.1:GPS-2942_4')
            ->setDescription('Add dog_kod 7 for DO files. Fix problem with unknown dog_kod.');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $this->executeSql($pdo, __DIR__ . '/sql/GPS-2942_4.sql');
    }
}
