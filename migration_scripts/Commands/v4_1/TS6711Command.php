<?php

namespace TF\Commands\v4_1;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-6711 command run on all databases.
 */
class TS6711Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.1:TS-6711')
            ->setDescription('Add columns avg_yield, treatment_price and comment in su_renta_types and average_yield, treatments_price and price_sum in su_personal_use');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $this->executeSql($pdo, __DIR__ . '/sql/TS-6711.sql');
    }
}
