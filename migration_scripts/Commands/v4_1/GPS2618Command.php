<?php

namespace TF\Commands\v4_1;

use PDO;
use TF\Commands\Common\MainDbCommand;

/**
 * TS-6035_2 command run on susi_main database.
 */
class GPS2618Command extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.1:GPS-2618')
            ->setDescription('Create create_cms_contracts column');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $this->executeSql($pdo, __DIR__ . '/sql/GPS-2618.sql');
    }
}
