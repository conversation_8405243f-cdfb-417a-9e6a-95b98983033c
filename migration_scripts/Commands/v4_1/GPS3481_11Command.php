<?php

namespace TF\Commands\v4_1;

use PDO;
use TF\Application\Entity\UserLayers;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS3481_11Command run on user databases.
 *
 * This command will create the virtual columns in all layers in the user databases.
 */
class GPS3481_11Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.1:GPS-3481-11')
            ->setDescription('Create the virtual columns in all layers in the user databases');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $output->write('Fetching layers with virtual columns...');

        $remoteTables = "'" . implode("', '", $GLOBALS['Layers']['remoteTables']) . "'";
        $layersWithVirtualColumnsSql = "SELECT 
                id, table_name, definitions
            FROM
                dblink(
                    'host=" . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' dbname=' . DEFAULT_DB_DATABASE . "',
                    $$
                        SELECT
                            sul.id, table_name, definitions
                        FROM
                            su_users_layers AS sul
                        JOIN su_users AS su 
                            ON sul.user_id = su.id
                        WHERE
                            su.database = '{$this->userDbName}'
                            AND sul.table_name NOT IN ({$remoteTables})
                            AND sul.definitions @> '[{\"col_virtual\": true}]'
                    $$
                ) AS ul (id INT, table_name VARCHAR, definitions JSONB);                
            ";

        $layersWithVirtualColumnsStmt = $pdo->prepare($layersWithVirtualColumnsSql);
        $layersWithVirtualColumnsStmt->execute();
        $output->writeln("\tDone!");

        /** @var UserLayers[] $layersWithVirtualColumns */
        $layersWithVirtualColumns = $layersWithVirtualColumnsStmt->fetchAll(PDO::FETCH_CLASS, UserLayers::class);

        if (!count($layersWithVirtualColumns)) {
            $output->writeln("\tNo layers with virtual columns found.");

            return;
        }

        foreach ($layersWithVirtualColumns as $layer) {
            $output->writeln("Creating virtual columns for layer {$layer->table_name} ...");
            $definitions = $layer->getDefinitions();
            $virtualColumnDefinitions = UserLayers::filterDefinitions($definitions, [['col_virtual' => true]]);

            if (!$this->tableExists($pdo, $layer->table_name)) {
                $output->writeln("Table {$layer->table_name} does not exist. Skipping.");

                continue;
            }

            foreach ($virtualColumnDefinitions as $virtualColumnDef) {
                $columnName = $virtualColumnDef['col_name'];
                $columnType = $GLOBALS['Layers']['columnTypesByCategory'][$virtualColumnDef['col_category']] ?? 'VARCHAR';
                $columnExpression = $virtualColumnDef['col_expression'];

                $dropExistingVirtualColumnSql = "ALTER TABLE {$layer->table_name} DROP COLUMN IF EXISTS {$columnName}";
                $stmt = $pdo->prepare($dropExistingVirtualColumnSql);
                $result = $stmt->execute();

                if (!$result) {
                    $output->error("Failed to drop existing virtual column {$columnName} for table {$layer->table_name}.");
                    $output->error($stmt->errorInfo()[2]);

                    return;
                }

                $layersCreateVirtualColumnsSql = "ALTER TABLE {$layer->table_name} ADD COLUMN IF NOT EXISTS {$columnName} {$columnType} GENERATED ALWAYS AS ({$columnExpression}) STORED;";
                $stmt = $pdo->prepare($layersCreateVirtualColumnsSql);
                $result = $stmt->execute();

                if (!$result) {
                    $output->error("Failed to create virtual column {$columnName} for layer {$layer->table_name}.");
                    $output->error($stmt->errorInfo()[2]);

                    return;
                }
            }
            $output->info("\tDone!");
        }
    }

    private function tableExists(PDO $pdo, string $tableName)
    {
        $stmt = $pdo->prepare("SELECT * FROM information_schema.tables WHERE table_name = '{$tableName}'");
        $stmt->execute();

        return $stmt->fetch();
    }
}
