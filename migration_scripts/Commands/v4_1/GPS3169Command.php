<?php

namespace TF\Commands\v4_1;

use PDO;
use PDOException;
use TF\Commands\Common\MainDbCommand;

/**
 * GPS2964Command run on susi_main database.
 *
 * This command will add column 'uuid' to table su_users_farming if not exists
 */
class GPS3169Command extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.1:GPS-3169')
            ->setDescription('NUZ table create');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $query = '
            CREATE TABLE IF NOT EXISTS su_nuz (
                ekatte VARCHAR(255),
                location VARCHAR(255),
                district VARCHAR(255),
                municipality VARCHAR(255)
            )
        ';

        try {
            $pdo->exec($query);
            $output->writeln("Table 'su_nuz' created successfully.");
        } catch (PDOException $e) {
            $output->writeln('Error: ' . $e->getMessage());
        }
    }
}
