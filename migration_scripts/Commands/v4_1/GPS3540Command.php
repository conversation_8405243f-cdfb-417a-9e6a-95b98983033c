<?php

namespace TF\Commands\v4_1;

use PDO;
use TF\Commands\Common\MainDbCommand;

/**
 * GPS3540Command run on susi_main database.
 *
 * The command removes broken definitions (those having null values for col_name and col_title)
 */
class GPS3540Command extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.1:GPS-3540')
            ->setDescription('The command removes broken definitions (those having null values for col_name and col_title)');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $sql = "WITH layers_fixed_definitions AS 
            (
                SELECT
                    sul.id AS user_layer_id, sul.definitions, sul.definitions - (idx - 1)::int AS fixed_definitions
                FROM
                    su_users_layers sul
                JOIN LATERAL (
                            SELECT * FROM jsonb_array_elements(sul.definitions) WITH ordinality AS def(elem, idx)
                        )as def(definition, idx) ON TRUE
                WHERE
                    sul.definitions @> '[{\"col_name\": null}]'
                    AND def.definition->>'col_name' ISNULL
                    AND sul.layer_type = 19
            )
            UPDATE su_users_layers
            SET definitions = layers_fixed_definitions.fixed_definitions
            FROM layers_fixed_definitions
            WHERE
                id = layers_fixed_definitions.user_layer_id
        ";

        $stmt = $pdo->prepare($sql);
        $stmt->execute();
    }
}
