CREATE TABLE su_consolidation_do_dog_kod
(
    id    serial4 PRIMARY KEY,
    dog_kod int4 UNIQUE NOT NULL,
    value varchar(255) NOT NULL
);

ALTER TABLE su_consolidation_do
    ADD CONSTRAINT su_consolidation_do_su_consolidation_do_dog_kod_id_foreign
        FOREIGN KEY (dog_kod) REFERENCES su_consolidation_do_dog_kod (dog_kod)
            ON DELETE CASCADE;

INSERT INTO su_consolidation_do_dog_kod (dog_kod, value)
values (1, 'Договор за аренда'),
       (2, 'Договор за наем'),
       (3, 'Договор за съвместна обработка по чл.31, ал.4, т.3 от ЗК'),
       (4, 'Договор за преарендоване'),
       (5, 'Договор за пренаемане');



CREATE TABLE su_consolidation_zd_kod
(
    id    serial4 PRIMARY KEY,
    kod int4 UNIQUE NOT NULL,
    value varchar(255) NOT NULL
);

ALTER TABLE su_consolidation_zd
    ADD CONSTRAINT su_consolidation_zd_su_consolidation_zd_kod_id_foreign
        FOREIGN KEY (kod) REFERENCES su_consolidation_zd_kod (kod)
            ON DELETE CASCADE;

INSERT INTO su_consolidation_zd_kod (kod, value)
values (1, 'Декларация по чл.69,ал.1'),
       (2, 'Декларация по чл.70,ал.1'),
       (3, 'Решение на комисията по споразумение');



CREATE TABLE su_consolidation_zd_dog_kod
(
    id    serial4 PRIMARY KEY,
    dog_kod int4 UNIQUE NOT NULL,
    value varchar(255) NOT NULL
);

ALTER TABLE su_consolidation_zd
    ADD CONSTRAINT su_consolidation_zd_su_consolidation_zd_dog_kod_id_foreign
        FOREIGN KEY (dog_kod) REFERENCES su_consolidation_zd_dog_kod (dog_kod)
            ON DELETE CASCADE;

INSERT INTO su_consolidation_zd_dog_kod (dog_kod, value)
values (0, 'Неизвестен договор'),
       (1, 'Договор за аренда'),
       (2, 'Договор за наем'),
       (3, 'Договор за съвместна обработка по чл.31, ал.4, т.3 от ЗК'),
       (4, 'Договор за преарендоване'),
       (5, 'Договор за пренаемане'),
       (6, 'Договор за концесия'),
       (7, 'Договор за лизинг');



CREATE TABLE su_consolidation_zd_jelanie
(
    id    serial4 PRIMARY KEY,
    jelanie int4 UNIQUE NOT NULL,
    value varchar(255) NOT NULL
);

ALTER TABLE su_consolidation_zd
    ADD CONSTRAINT su_consolidation_zd_su_consolidation_zd_jelanie_foreign
        FOREIGN KEY (jelanie) REFERENCES su_consolidation_zd_jelanie (jelanie)
            ON DELETE CASCADE;

INSERT INTO su_consolidation_zd_jelanie (jelanie, value)
values (1, 'Желая  да участвам с имота в масиви за ползване по чл.37в от ЗСПЗЗ'),
       (2, 'Не желая имотът да бъде включван в масиви за ползване по чл.37в от ЗСПЗЗ'),
       (3,
        'Желая имотът да бъде включен в масиви за ползване като имот по чл.37б, ал3, т.2 ЗСПЗЗ - бяло  петно'),
       (4, 'Имотът е отдаден с договор за ползване от друго лице и деклараторът не изразява  желание');
