ALTER TABLE public.su_personal_use ADD COLUMN IF NOT EXISTS auto_crops_divide bool DEFAULT true;
ALTER TABLE public.su_personal_use_rents ADD COLUMN IF NOT EXISTS area numeric DEFAULT null;
ALTER TABLE public.su_personal_use_rents ADD COLUMN IF NOT EXISTS average_yield numeric DEFAULT null;
ALTER TABLE public.su_personal_use_rents ADD COLUMN IF NOT EXISTS treatments_price numeric DEFAULT null;
ALTER TABLE public.su_personal_use_rents ADD COLUMN IF NOT EXISTS price_sum numeric DEFAULT null;
ALTER TABLE public.su_transactions ADD type int4 DEFAULT 1;
COMMENT ON COLUMN public.su_transactions.type IS '1 -> payment, 2 -> personal use';
ALTER TABLE public.su_collections ADD type int4 DEFAULT 1;
COMMENT ON COLUMN public.su_collections.type IS '1 -> rents, 2 -> treatments';
INSERT INTO public.su_payment_subjects ("name", fulltext) VALUES  ('Изплащане на обработки','Изплащане на обработки по договор [[nomer_na_dogovor]] за [[stopanska_godina]] стопанска година');
ALTER TABLE public.su_collections ADD status bool DEFAULT true;
COMMENT ON COLUMN public.su_collections.status IS '1 -> active, 0 -> not active';
ALTER TABLE public.su_collections ADD cancelled_info jsonb DEFAULT null;