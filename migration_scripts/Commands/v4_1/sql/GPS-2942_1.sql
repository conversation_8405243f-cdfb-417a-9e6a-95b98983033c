CREATE TABLE su_consolidation_do
(
    id         serial4 PRIMARY KEY,
    ekatte     varchar(255) NOT NULL,
    kad_ident  varchar(255) NOT NULL,
    osz_no     varchar(255),
    data       date,
    dog_kod    int4,
    dog_data   date,
    dog_novp   varchar(255),
    dog_nach   date,
    dog_kraj   date,
    dog_dka    float4,
    polz_ident varchar(255),
    polz_ime   varchar(255),
    sobst_egn  varchar(255),
    sobst_ime  varchar(255),
    sobst_adr  varchar(255),
    sobst_tel  varchar(255)
);
CREATE INDEX kad_ident_do_idx ON su_consolidation_do (kad_ident);

CREATE TABLE su_consolidation_zd
(
    id         serial4 PRIMARY KEY,
    ekatte     varchar(255) NOT NULL,
    kad_ident  varchar(255) NOT NULL,
    godina     int4,
    data       date,
    kod        int4,
    dok_no     varchar(255),
    dok_data   date,
    dog_kod    int4,
    dog_no     varchar(255),
    dog_data   date,
    dog_nach   date,
    dog_srok   int4,
    polz_dka   float4,
    polz_ident varchar(255),
    polz_ime   varchar(255),
    j<PERSON><PERSON>    int4,
    sluj_vkl   varchar(255),
    polz_ekt   varchar(255),
    polz_adr   varchar(255),
    polz_tel   varchar(255)
);
CREATE INDEX kad_ident_zd_idx ON su_consolidation_zd (kad_ident);