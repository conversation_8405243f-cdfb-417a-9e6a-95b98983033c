CREATE TYPE requested_ekatte_status_enum AS ENUM ('Requested', 'Received', 'For sync');
COMMIT;

CREATE TABLE public.su_requested_ekattes (
	id serial4 NOT NULL,
	ekatte_code varchar NOT NULL,
	ekatte_name varchar NOT NULL,
	user_id int4 NOT NULL,
	group_id int4 NOT NULL,
	status public.requested_ekatte_status_enum NOT NULL,
	kvs_store_uuid varchar NULL,
	created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone NOT NULL,
	CONSTRAINT su_requested_ekattes_pk PRIMARY KEY (id),
	CONSTRAINT su_requested_ekattes_user_fk FOREIGN KEY (user_id) REFERENCES public.su_users(id)
	CONSTRAINT su_requested_ekattes_group_fk FOREIGN KEY (group_id) REFERENCES public.su_users(id)
);


