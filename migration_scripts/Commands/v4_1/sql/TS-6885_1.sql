-- ----------------------------
-- Table structure for su_banks
-- ----------------------------
DROP TABLE IF EXISTS "public"."su_banks";
	CREATE TABLE "public"."su_banks" (
		"bank_name" varchar(255) COLLAT<PERSON> "default",
        "bank_code" varchar(255) COLLATE "default",
		"bic_code" varchar(255) COLLATE "default",
		"bank_name_en" varchar(255) COLLATE "default"
	)
WITH (OIDS=FALSE);

-- ----------------------------
-- Records of su_banks
-- ----------------------------

INSERT INTO public.su_banks (bank_name,bank_code,bic_code,bank_name_en) VALUES
	 ('Айкарт АД','INTF4001','INTFBGSF','<PERSON><PERSON><PERSON> AD'),
	 ('Алианц Банк България АД','BUIN9561','BUINBGSF','Alianz Bank Bulgaria AD'),
	 ('Алианц Банк България АД, кл. Благоевград','BUIN7680','BUINBGSF','Alianz Bank Bulgaria AD, kl. Blagoevgrad'),
	 ('Алианц Банк България АД, кл. Бургас','BUIN7855','BUINBGSF','Alianz Bank Bulgaria AD, kl. Burgas'),
	 ('Алианц Банк България АД, кл. Варна','BUIN7006','BUINBGSF','Alianz Bank Bulgaria AD, kl. Varna'),
	 ('Алианц Банк България АД, кл. Велико Търново','BUIN7001','BUINBGSF','Alianz Bank Bulgaria AD, kl. Veliko Turnovo'),
	 ('Алианц Банк България АД, кл. Видин','BUIN7004','BUINBGSF','Alianz Bank Bulgaria AD, kl. Vidin'),
	 ('Алианц Банк България АД, кл. Владислав - Вн','BUIN7750','BUINBGSF','Alianz Bank Bulgaria AD, kl. Vladislav - Vn'),
	 ('Алианц Банк България АД, кл. Враца','BUIN7470','BUINBGSF','Alianz Bank Bulgaria AD, kl. Vraca'),
	 ('Алианц Банк България АД, кл. Габрово','BUIN8012','BUINBGSF','Alianz Bank Bulgaria AD, kl. Gabrovo');
INSERT INTO public.su_banks (bank_name,bank_code,bic_code,bank_name_en) VALUES
	 ('Алианц Банк България АД, кл. Димитровград','BUIN7005','BUINBGSF','Alianz Bank Bulgaria AD, kl. Dimitrovgrad'),
	 ('Алианц Банк България АД, кл. Добрич','BUIN7082','BUINBGSF','Alianz Bank Bulgaria AD, kl. Dobrich'),
	 ('Алианц Банк България АД, кл. Дупница','BUIN7068','BUINBGSF','Alianz Bank Bulgaria AD, kl. Dupnica'),
	 ('Алианц Банк България АД, кл. Европа','BUIN7444','BUINBGSF','Alianz Bank Bulgaria AD, kl. Evropa'),
	 ('Алианц Банк България АД, кл. Журналист - Сф','BUIN7220','BUINBGSF','Alianz Bank Bulgaria AD, kl. Jurnalist - Sf'),
	 ('Алианц Банк България АД, кл. Казанлък','BUIN7145','BUINBGSF','Alianz Bank Bulgaria AD, kl. Kazanluk'),
	 ('Алианц Банк България АД, кл. Княгиня Мария Луиза - Сф','BUIN7604','BUINBGSF','Alianz Bank Bulgaria AD, kl. Knqginq Mariq Luiza - Sf'),
	 ('Алианц Банк България АД, кл. Кърджали','BUIN8035','BUINBGSF','Alianz Bank Bulgaria AD, kl. Kurdjali'),
	 ('Алианц Банк България АД, кл. Кюстендил','BUIN7003','BUINBGSF','Alianz Bank Bulgaria AD, kl. Kiustendil'),
	 ('Алианц Банк България АД, кл. Ловеч','BUIN8092','BUINBGSF','Alianz Bank Bulgaria AD, kl. Lovech');
INSERT INTO public.su_banks (bank_name,bank_code,bic_code,bank_name_en) VALUES
	 ('Алианц Банк България АД, кл. Монтана','BUIN7661','BUINBGSF','Alianz Bank Bulgaria AD, kl. Montana'),
	 ('Алианц Банк България АД, кл. Опълченска - Сф','BUIN7660','BUINBGSF','Alianz Bank Bulgaria AD, kl. Opulchenska - Sf'),
	 ('Алианц Банк България АД, кл. Пазарджик','BUIN7250','BUINBGSF','Alianz Bank Bulgaria AD, kl. Pazardjik'),
	 ('Алианц Банк България АД, кл. Плевен','BUIN7015','BUINBGSF','Alianz Bank Bulgaria AD, kl. Pleven'),
	 ('Алианц Банк България АД, кл. Плиска - Сф','BUIN8090','BUINBGSF','Alianz Bank Bulgaria AD, kl. Pliska - Sf'),
	 ('Алианц Банк България АД, кл. Пловдив','BUIN7009','BUINBGSF','Alianz Bank Bulgaria AD, kl. Plovdiv'),
	 ('Алианц Банк България АД, кл. Престиж - Пд','BUIN7650','BUINBGSF','Alianz Bank Bulgaria AD, kl. Prestij - Pd'),
	 ('Алианц Банк България АД, кл. Разград','BUIN8016','BUINBGSF','Alianz Bank Bulgaria AD, kl. Razgrad'),
	 ('Алианц Банк България АД, кл. Русе','BUIN7007','BUINBGSF','Alianz Bank Bulgaria AD, kl. Ruse'),
	 ('Алианц Банк България АД, кл. Севлиево','BUIN7067','BUINBGSF','Alianz Bank Bulgaria AD, kl. Sevlievo');
INSERT INTO public.su_banks (bank_name,bank_code,bic_code,bank_name_en) VALUES
	 ('Алианц Банк България АД, кл. Силистра','BUIN7069','BUINBGSF','Alianz Bank Bulgaria AD, kl. Silistra'),
	 ('Алианц Банк България АД, кл. Сливен ','BUIN7002','BUINBGSF','Alianz Bank Bulgaria AD, kl. Sliven '),
	 ('Алианц Банк България АД, кл. Стара Загора','BUIN7903','BUINBGSF','Alianz Bank Bulgaria AD, kl. Stara Zagora'),
	 ('Алианц Банк България АД, кл. Троян','BUIN7770','BUINBGSF','Alianz Bank Bulgaria AD, kl. Troqn'),
	 ('Алианц Банк България АД, кл. Търговище','BUIN7008','BUINBGSF','Alianz Bank Bulgaria AD, kl. Turgovishte'),
	 ('Алианц Банк България АД, кл. Хасково','BUIN7545','BUINBGSF','Alianz Bank Bulgaria AD, kl. Haskovo'),
	 ('Алианц Банк България АД, кл. Шумен','BUIN7014','BUINBGSF','Alianz Bank Bulgaria AD, kl. SHumen'),
	 ('Алианц Банк България АД, кл. Ямбол','BUIN7562','BUINBGSF','Alianz Bank Bulgaria AD, kl. Qmbol'),
	 ('БНП Париба Пърсънъл Файненс С.А. - клон БЪЛГАРИЯ','BPEF9290','BPEFBGSF','BNP Pariba Pursunul Faiinens S.A. - klon Bulgaria'),
	 ('БНП Париба С. А. - клон София','BNPA9440','BNPABGSX','BNP Pariba S. A. - klon Sofiq');
INSERT INTO public.su_banks (bank_name,bank_code,bic_code,bank_name_en) VALUES
	 ('Банка ДСК АД','STSA9300','STSABGSF','Banka DSK AD'),
	 ('Виртуален клон на Банка ДСК ЕАД','STSA8300','STSABGSF','Virtualen klon na Banka DSK EAD'),
	 ('Бигбанк АС - клон България ','BIGK9879','BIGKBGSF','Bigbank AS - klon Bulgaria '),
	 ('Българо-американска кредитна банка АД','BGUS9160','BGUSBGSF','Bulgaro-amerikanska kreditna banka AD'),
	 ('Българска банка за развитие АД','NASB9620','NASBBGSF','Bulgarska banka za razvitie AD'),
	 ('Българска народна банка','BNBG9661','BNBGBGSF ','Bulgarska narodna banka'),
	 ('Българска народна банка СЕБРА плащания','BNBG5000','BNBGBGSF ','Bulgarska narodna banka SEBRA plashtaniq'),
	 ('Българска народна банка СЕБРА плащания','BNBG5000','BNBGBGSD','Bulgarska narodna banka SEBRA plashtaniq'),
	 ('Българска народна банка СЕБРА плащания','BNBG5000','BNBGBGSD','Bulgarska narodna banka SEBRA plashtaniq'),
	 ('Варенголд Банк АГ, клон София','VGAG9876','VGAGBGSF','Varengold Bank AG, klon Sofiq');
INSERT INTO public.su_banks (bank_name,bank_code,bic_code,bank_name_en) VALUES
	 ('Вива Пеймънт Сървисис ЕС ЕЙ - клон България АД','VPAY4011','VPAYBGS2','Viva Peiimunt Survisis ES EII - klon Bulgaria AD'),
	 ('Еконт Финансови услуги ООД','ECFE4014','ECFEBG22','Ekont Finansovi uslugi OOD'),
	 ('ИНГ Банк Н.В. - кл. София','INGB9145','INGBBGSF','ING Bank N.V. - kl. Sofiq'),
	 ('Изи пеймънт Сървисиз ООД','EAPS4008','EAPSBGS2','Izi peiimunt Survisiz OOD'),
	 ('Изипей АД','ESPY4004','ESPYBGS1','Izipeii AD'),
	 ('Инвестбанк АД','IORT9120','IORTBGSF','Investbank AD'),
	 ('Инвестбанк АД, кл. Благоевград','IORT8049','IORTBGSF','Investbank AD, kl. Blagoevgrad'),
	 ('Инвестбанк АД, кл. Ботевград','IORT8132','IORTBGSF','Investbank AD, kl. Botevgrad'),
	 ('Инвестбанк АД, кл. Бургас','IORT7378','IORTBGSF','Investbank AD, kl. Burgas'),
	 ('Инвестбанк АД, кл. Бургас - Иван Вазов','IORT8165','IORTBGSF','Investbank AD, kl. Burgas - Ivan Vazov');
INSERT INTO public.su_banks (bank_name,bank_code,bic_code,bank_name_en) VALUES
	 ('Инвестбанк АД, кл. Варна','IORT7377','IORTBGSF','Investbank AD, kl. Varna'),
	 ('Инвестбанк АД, кл. Варна - Сливница','IORT8164','IORTBGSF','Investbank AD, kl. Varna - Slivnica'),
	 ('Инвестбанк АД, кл. Велико Търново','IORT8043','IORTBGSF','Investbank AD, kl. Veliko Turnovo'),
	 ('Инвестбанк АД, кл. Велико Търново 2','IORT8166','IORTBGSF','Investbank AD, kl. Veliko Turnovo 2'),
	 ('Инвестбанк АД, кл. Видин','IORT7373','IORTBGSF','Investbank AD, kl. Vidin'),
	 ('Инвестбанк АД, кл. Враца','IORT8131','IORTBGSF','Investbank AD, kl. Vraca'),
	 ('Инвестбанк АД, кл. Габрово','IORT8095','IORTBGSF','Investbank AD, kl. Gabrovo'),
	 ('Инвестбанк АД, кл. Горна Оряховица','IORT8167','IORTBGSF','Investbank AD, kl. Gorna Orqhovica'),
	 ('Инвестбанк АД, кл. Добрич','IORT8088','IORTBGSF','Investbank AD, kl. Dobrich'),
	 ('Инвестбанк АД, кл. Дойран','IORT8130','IORTBGSF','Investbank AD, kl. Doiiran');
INSERT INTO public.su_banks (bank_name,bank_code,bic_code,bank_name_en) VALUES
	 ('Инвестбанк АД, кл. Казанлък','IORT8023','IORTBGSF','Investbank AD, kl. Kazanluk'),
	 ('Инвестбанк АД, кл. Ловеч','IORT8046','IORTBGSF','Investbank AD, kl. Lovech'),
	 ('Инвестбанк АД, кл. Люлин','IORT8103','IORTBGSF','Investbank AD, kl. Liulin'),
	 ('Инвестбанк АД, кл. Монтана','IORT8266','IORTBGSF','Investbank AD, kl. Montana'),
	 ('Инвестбанк АД, кл. Пазарджик','IORT6091','IORTBGSF','Investbank AD, kl. Pazardjik'),
	 ('Инвестбанк АД, кл. Перник','IORT8111','IORTBGSF','Investbank AD, kl. Pernik'),
	 ('Инвестбанк АД, кл. Плевен','IORT7380','IORTBGSF','Investbank AD, kl. Pleven'),
	 ('Инвестбанк АД, кл. Плиска','IORT8139','IORTBGSF','Investbank AD, kl. Pliska'),
	 ('Инвестбанк АД, кл. Пловдив','IORT7375','IORTBGSF','Investbank AD, kl. Plovdiv'),
	 ('Инвестбанк АД, кл. Прага София','IORT8163','IORTBGSF','Investbank AD, kl. Praga Sofiq');
INSERT INTO public.su_banks (bank_name,bank_code,bic_code,bank_name_en) VALUES
	 ('Инвестбанк АД, кл. Разград','IORT8116','IORTBGSF','Investbank AD, kl. Razgrad'),
	 ('Инвестбанк АД, кл. Русе','IORT7379','IORTBGSF','Investbank AD, kl. Ruse'),
	 ('Инвестбанк АД, кл. Свищов','IORT8137','IORTBGSF','Investbank AD, kl. Svishtov'),
	 ('Инвестбанк АД, кл. Севлиево','IORT8127','IORTBGSF','Investbank AD, kl. Sevlievo'),
	 ('Инвестбанк АД, кл. Силистра','IORT8047','IORTBGSF','Investbank AD, kl. Silistra'),
	 ('Инвестбанк АД, кл. Сливен','IORT8029','IORTBGSF','Investbank AD, kl. Sliven'),
	 ('Инвестбанк АД, кл. Смолян','IORT8019','IORTBGSF','Investbank AD, kl. Smolqn'),
	 ('Инвестбанк АД, кл. София','IORT7371','IORTBGSF','Investbank AD, kl. Sofiq'),
	 ('Инвестбанк АД, кл. София - Ангел Кънчев','IORT8045','IORTBGSF','Investbank AD, kl. Sofiq - Angel Kunchev'),
	 ('Инвестбанк АД, кл. София - Витоша','IORT8094','IORTBGSF','Investbank AD, kl. Sofiq - Vitosha');
INSERT INTO public.su_banks (bank_name,bank_code,bic_code,bank_name_en) VALUES
	 ('Инвестбанк АД, кл. София - Младост','IORT8102','IORTBGSF','Investbank AD, kl. Sofiq - Mladost'),
	 ('Инвестбанк АД, кл. София - Оборище','IORT8168','IORTBGSF','Investbank AD, kl. Sofiq - Oborishte'),
	 ('Инвестбанк АД, кл. София 2','IORT8038','IORTBGSF','Investbank AD, kl. Sofiq 2'),
	 ('Инвестбанк АД, кл. Стамболов','IORT8128','IORTBGSF','Investbank AD, kl. Stambolov'),
	 ('Инвестбанк АД, кл. Стара Загора','IORT7376','IORTBGSF','Investbank AD, kl. Stara Zagora'),
	 ('Инвестбанк АД, кл. Троян','IORT8138','IORTBGSF','Investbank AD, kl. Troqn'),
	 ('Инвестбанк АД, кл. Хасково','IORT8031','IORTBGSF','Investbank AD, kl. Haskovo'),
	 ('Инвестбанк АД, кл. Централен','IORT8048','IORTBGSF','Investbank AD, kl. Centralen'),
	 ('Инвестбанк АД, кл. Шести септември','IORT8129','IORTBGSF','Investbank AD, kl. SHesti septemvri'),
	 ('Инвестбанк АД, кл. Шумен','IORT8006','IORTBGSF','Investbank AD, kl. SHumen');
INSERT INTO public.su_banks (bank_name,bank_code,bic_code,bank_name_en) VALUES
	 ('Интернешънъл Асет Банк АД','IABG9470','IABGBGSF','International Asset Bank AD'),
	 ('Интернешънъл Асет Банк АД, кл. Благоевград','IABG7458','IABGBGSF','International Asset Bank AD, kl. Blagoevgrad'),
	 ('Интернешънъл Асет Банк АД, кл. Бургас','IABG7475','IABGBGSF','International Asset Bank AD, kl. Burgas'),
	 ('Интернешънъл Асет Банк АД, кл. Варна','IABG7479','IABGBGSF','International Asset Bank AD, kl. Varna'),
	 ('Интернешънъл Асет Банк АД, кл. Велико Търново','IABG7431','IABGBGSF','International Asset Bank AD, kl. Veliko Turnovo'),
	 ('Интернешънъл Асет Банк АД, кл. Видин','IABG7496','IABGBGSF','International Asset Bank AD, kl. Vidin'),
	 ('Интернешънъл Асет Банк АД, кл. Враца','IABG7494','IABGBGSF','International Asset Bank AD, kl. Vraca'),
	 ('Интернешънъл Асет Банк АД, кл. Габрово','IABG7498','IABGBGSF','International Asset Bank AD, kl. Gabrovo'),
	 ('Интернешънъл Асет Банк АД, кл. Галакси - София','IABG8123','IABGBGSF','International Asset Bank AD, kl. Galaksi - Sofiq'),
	 ('Интернешънъл Асет Банк АД, кл. Добрич','IABG7495','IABGBGSF','International Asset Bank AD, kl. Dobrich');
INSERT INTO public.su_banks (bank_name,bank_code,bic_code,bank_name_en) VALUES
	 ('Интернешънъл Асет Банк АД, кл. Кърджали','IABG7497','IABGBGSF','International Asset Bank AD, kl. Kurdjali'),
	 ('Интернешънъл Асет Банк АД, кл. Кюстендил','IABG7490','IABGBGSF','International Asset Bank AD, kl. Kiustendil'),
	 ('Интернешънъл Асет Банк АД, кл. Ловеч','IABG7456','IABGBGSF','International Asset Bank AD, kl. Lovech'),
	 ('Интернешънъл Асет Банк АД, кл. Лозенец - Сф','IABG8074','IABGBGSF','International Asset Bank AD, kl. Lozenec - Sf'),
	 ('Интернешънъл Асет Банк АД, кл. Младост - Сф','IABG8118','IABGBGSF','International Asset Bank AD, kl. Mladost - Sf'),
	 ('Интернешънъл Асет Банк АД, кл. Монтана','IABG7474','IABGBGSF','International Asset Bank AD, kl. Montana'),
	 ('Интернешънъл Асет Банк АД, кл. Пазарджик','IABG7096','IABGBGSF','International Asset Bank AD, kl. Pazardjik'),
	 ('Интернешънъл Асет Банк АД, кл. Перник','IABG7478','IABGBGSF','International Asset Bank AD, kl. Pernik'),
	 ('Интернешънъл Асет Банк АД, кл. Плевен','IABG7473','IABGBGSF','International Asset Bank AD, kl. Pleven'),
	 ('Интернешънъл Асет Банк АД, кл. Пловдив','IABG7095','IABGBGSF','International Asset Bank AD, kl. Plovdiv');
INSERT INTO public.su_banks (bank_name,bank_code,bic_code,bank_name_en) VALUES
	 ('Интернешънъл Асет Банк АД, кл. Разград','IABG7460','IABGBGSF','International Asset Bank AD, kl. Razgrad'),
	 ('Интернешънъл Асет Банк АД, кл. Русе','IABG7471','IABGBGSF','International Asset Bank AD, kl. Ruse'),
	 ('Интернешънъл Асет Банк АД, кл. Сандански','IABG7648','IABGBGSF','International Asset Bank AD, kl. Sandanski'),
	 ('Интернешънъл Асет Банк АД, кл. Силистра','IABG7459','IABGBGSF','International Asset Bank AD, kl. Silistra'),
	 ('Интернешънъл Асет Банк АД, кл. Сливен','IABG7488','IABGBGSF','International Asset Bank AD, kl. Sliven'),
	 ('Интернешънъл Асет Банк АД, кл. Смолян','IABG7491','IABGBGSF','International Asset Bank AD, kl. Smolqn'),
	 ('Интернешънъл Асет Банк АД, кл. София','IABG7432','IABGBGSF','International Asset Bank AD, kl. Sofiq'),
	 ('Интернешънъл Асет Банк АД, кл. Стара Загора','IABG7433','IABGBGSF','International Asset Bank AD, kl. Stara Zagora'),
	 ('Интернешънъл Асет Банк АД, кл. Търговище','IABG7093','IABGBGSF','International Asset Bank AD, kl. Turgovishte'),
	 ('Интернешънъл Асет Банк АД, кл. Хасково','IABG7097','IABGBGSF','International Asset Bank AD, kl. Haskovo');
INSERT INTO public.su_banks (bank_name,bank_code,bic_code,bank_name_en) VALUES
	 ('Интернешънъл Асет Банк АД, кл. Централен','IABG8098','IABGBGSF','International Asset Bank AD, kl. Centralen'),
	 ('Интернешънъл Асет Банк АД, кл. Шумен','IABG7477','IABGBGSF','International Asset Bank AD, kl. SHumen'),
	 ('Интернешънъл Асет Банк АД, кл. Ямбол','IABG7094','IABGBGSF','International Asset Bank AD, kl. Qmbol'),
	 ('Интернешънъл Асет Банк АД, кл.Виртуален','IABG8429','IABGBGSF','International Asset Bank AD, kl.Virtualen'),
	 ('Интернешънъл Асет Банк АД, оф. Кремиковци','IABG8428','IABGBGSF','International Asset Bank AD, of. Kremikovci'),
	 ('Майфин ЕАД','MYFN4012','MYFNBGSF','Maiifin EAD'),
	 ('Обединена българска банка (екс Кей Би Си Банк ЕАД) АД','RZBB9155','RZBBBGSF','United Bulgarian Bank (eks Keii Bi Si Bank EAD) AD'),
	 ('Обединена българска банка АД','UBBS9200','UBBSBGSF','United Bulgarian Bank AD'),
	 ('Обединена българска банка АД, кл. Априлци','UBBS7207','UBBSBGSF','United Bulgarian Bank AD, kl. Aprilci'),
	 ('Обединена българска банка АД, кл. Батак','UBBS7242','UBBSBGSF','United Bulgarian Bank AD, kl. Batak');
INSERT INTO public.su_banks (bank_name,bank_code,bic_code,bank_name_en) VALUES
	 ('Обединена българска банка АД, кл. Белово','UBBS7243','UBBSBGSF','United Bulgarian Bank AD, kl. Belovo'),
	 ('Обединена българска банка АД, кл. Берковица','UBBS7222','UBBSBGSF','United Bulgarian Bank AD, kl. Berkovica'),
	 ('Обединена българска банка АД, кл. Благоевград','UBBS7504','UBBSBGSF','United Bulgarian Bank AD, kl. Blagoevgrad'),
	 ('Обединена българска банка АД, кл. Ботевград','UBBS8442','UBBSBGSF','United Bulgarian Bank AD, kl. Botevgrad'),
	 ('Обединена българска банка АД, кл. Бургас ','UBBS7823','UBBSBGSF','United Bulgarian Bank AD, kl. Burgas '),
	 ('Обединена българска банка АД, кл. Бяла','UBBS7342','UBBSBGSF','United Bulgarian Bank AD, kl. Bqla'),
	 ('Обединена българска банка АД, кл. Бяла Слатина','UBBS7122','UBBSBGSF','United Bulgarian Bank AD, kl. Bqla Slatina'),
	 ('Обединена българска банка АД, кл. Варна','UBBS7070','UBBSBGSF','United Bulgarian Bank AD, kl. Varna'),
	 ('Обединена българска банка АД, кл. Велико Търново','UBBS7822','UBBSBGSF','United Bulgarian Bank AD, kl. Veliko Turnovo'),
	 ('Обединена българска банка АД, кл. Видин','UBBS7267','UBBSBGSF','United Bulgarian Bank AD, kl. Vidin');
INSERT INTO public.su_banks (bank_name,bank_code,bic_code,bank_name_en) VALUES
	 ('Обединена българска банка АД, кл. Виртуален','UBBS8505','UBBSBGSF','United Bulgarian Bank AD, kl. Virtualen'),
	 ('Обединена българска банка АД, кл. Враца','UBBS8121','UBBSBGSF','United Bulgarian Bank AD, kl. Vraca'),
	 ('Обединена българска банка АД, кл. Габрово','UBBS8141','UBBSBGSF','United Bulgarian Bank AD, kl. Gabrovo'),
	 ('Обединена българска банка АД, кл. Горна Оряховица','UBBS7284','UBBSBGSF','United Bulgarian Bank AD, kl. Gorna Orqhovica'),
	 ('Обединена българска банка АД, кл. Две Могили','UBBS7343','UBBSBGSF','United Bulgarian Bank AD, kl. Dve Mogili'),
	 ('Обединена българска банка АД, кл. Добрич','UBBS7369','UBBSBGSF','United Bulgarian Bank AD, kl. Dobrich'),
	 ('Обединена българска банка АД, кл. Дряново','UBBS7142','UBBSBGSF','United Bulgarian Bank AD, kl. Drqnovo'),
	 ('Обединена българска банка АД, кл. Експо','UBBS8155','UBBSBGSF','United Bulgarian Bank AD, kl. Ekspo'),
	 ('Обединена българска банка АД, кл. Елхово','UBBS8562','UBBSBGSF','United Bulgarian Bank AD, kl. Elhovo'),
	 ('Обединена българска банка АД, кл. Искър - Сф','UBBS8427','UBBSBGSF','United Bulgarian Bank AD, kl. Iskur - Sf');
INSERT INTO public.su_banks (bank_name,bank_code,bic_code,bank_name_en) VALUES
	 ('Обединена българска банка АД, кл. Казанлък','UBBS7125','UBBSBGSF','United Bulgarian Bank AD, kl. Kazanluk'),
	 ('Обединена българска банка АД, кл. Княгиня Мария Луиза - Сф','UBBS8423','UBBSBGSF','United Bulgarian Bank AD, kl. Knqginq Mariq Luiza - Sf'),
	 ('Обединена българска банка АД, кл. Козлодуй','UBBS7426','UBBSBGSF','United Bulgarian Bank AD, kl. Kozloduii'),
	 ('Обединена българска банка АД, кл. Кърджали','UBBS8161','UBBSBGSF','United Bulgarian Bank AD, kl. Kurdjali'),
	 ('Обединена българска банка АД, кл. Кюстендил','UBBS7270','UBBSBGSF','United Bulgarian Bank AD, kl. Kiustendil'),
	 ('Обединена българска банка АД, кл. Левски','UBBS7282','UBBSBGSF','United Bulgarian Bank AD, kl. Levski'),
	 ('Обединена българска банка АД, кл. Ловеч','UBBS8201','UBBSBGSF','United Bulgarian Bank AD, kl. Lovech'),
	 ('Обединена българска банка АД, кл. Лом','UBBS7224','UBBSBGSF','United Bulgarian Bank AD, kl. Lom'),
	 ('Обединена българска банка АД, кл. Любимец','UBBS7523','UBBSBGSF','United Bulgarian Bank AD, kl. Liubimec'),
	 ('Обединена българска банка АД, кл. Милениум','UBBS8888','UBBSBGSF','United Bulgarian Bank AD, kl. Milenium');
INSERT INTO public.su_banks (bank_name,bank_code,bic_code,bank_name_en) VALUES
	 ('Обединена българска банка АД, кл. Монтана','UBBS8221','UBBSBGSF','United Bulgarian Bank AD, kl. Montana'),
	 ('Обединена българска банка АД, кл. Нова Загора','UBBS8383','UBBSBGSF','United Bulgarian Bank AD, kl. Nova Zagora'),
	 ('Обединена българска банка АД, кл. Оряхово','UBBS7126','UBBSBGSF','United Bulgarian Bank AD, kl. Orqhovo'),
	 ('Обединена българска банка АД, кл. Пазарджик','UBBS8241','UBBSBGSF','United Bulgarian Bank AD, kl. Pazardjik'),
	 ('Обединена българска банка АД, кл. Перник','UBBS8261','UBBSBGSF','United Bulgarian Bank AD, kl. Pernik'),
	 ('Обединена българска банка АД, кл. Пещера','UBBS8246','UBBSBGSF','United Bulgarian Bank AD, kl. Peshtera'),
	 ('Обединена българска банка АД, кл. Плевен','UBBS8281','UBBSBGSF','United Bulgarian Bank AD, kl. Pleven'),
	 ('Обединена българска банка АД, кл. Пловдив','UBBS7821','UBBSBGSF','United Bulgarian Bank AD, kl. Plovdiv'),
	 ('Обединена българска банка АД, кл. Попово','UBBS8503','UBBSBGSF','United Bulgarian Bank AD, kl. Popovo'),
	 ('Обединена българска банка АД, кл. Преслав','UBBS7543','UBBSBGSF','United Bulgarian Bank AD, kl. Preslav');
INSERT INTO public.su_banks (bank_name,bank_code,bic_code,bank_name_en) VALUES
	 ('Обединена българска банка АД, кл. Приста - Сф','UBBS7826','UBBSBGSF','United Bulgarian Bank AD, kl. Prista - Sf'),
	 ('Обединена българска банка АД, кл. Разград','UBBS7370','UBBSBGSF','United Bulgarian Bank AD, kl. Razgrad'),
	 ('Обединена българска банка АД, кл. Русе','UBBS8341','UBBSBGSF','United Bulgarian Bank AD, kl. Ruse'),
	 ('Обединена българска банка АД, кл. Самоков','UBBS8448','UBBSBGSF','United Bulgarian Bank AD, kl. Samokov'),
	 ('Обединена българска банка АД, кл. Света София - Сф','UBBS7827','UBBSBGSF','United Bulgarian Bank AD, kl. Sveta Sofiq - Sf'),
	 ('Обединена българска банка АД, кл. Свиленград','UBBS7525','UBBSBGSF','United Bulgarian Bank AD, kl. Svilengrad'),
	 ('Обединена българска банка АД, кл. Севлиево','UBBS7566','UBBSBGSF','United Bulgarian Bank AD, kl. Sevlievo'),
	 ('Обединена българска банка АД, кл. Силистра','UBBS7269','UBBSBGSF','United Bulgarian Bank AD, kl. Silistra'),
	 ('Обединена българска банка АД, кл. Сливен','UBBS8381','UBBSBGSF','United Bulgarian Bank AD, kl. Sliven'),
	 ('Обединена българска банка АД, кл. Смолян','UBBS7825','UBBSBGSF','United Bulgarian Bank AD, kl. Smolqn');
INSERT INTO public.su_banks (bank_name,bank_code,bic_code,bank_name_en) VALUES
	 ('Обединена българска банка АД, кл. Стара Загора','UBBS7824','UBBSBGSF','United Bulgarian Bank AD, kl. Stara Zagora'),
	 ('Обединена българска банка АД, кл. Тетевен','UBBS7268','UBBSBGSF','United Bulgarian Bank AD, kl. Teteven'),
	 ('Обединена българска банка АД, кл. Троян','UBBS7828','UBBSBGSF','United Bulgarian Bank AD, kl. Troqn'),
	 ('Обединена българска банка АД, кл. Търговище','UBBS8501','UBBSBGSF','United Bulgarian Bank AD, kl. Turgovishte'),
	 ('Обединена българска банка АД, кл. Фритьоф Нансен - Сф','UBBS7820','UBBSBGSF','United Bulgarian Bank AD, kl. Fritioof Nansen - Sf'),
	 ('Обединена българска банка АД, кл. Хасково','UBBS8521','UBBSBGSF','United Bulgarian Bank AD, kl. Haskovo'),
	 ('Обединена българска банка АД, кл. Централен','UBBS8002','UBBSBGSF','United Bulgarian Bank AD, kl. Centralen'),
	 ('Обединена българска банка АД, кл. Шипка - Сф','UBBS7428','UBBSBGSF','United Bulgarian Bank AD, kl. SHipka - Sf'),
	 ('Обединена българска банка АД, кл. Шумен','UBBS8541','UBBSBGSF','United Bulgarian Bank AD, kl. SHumen'),
	 ('Обединена българска банка АД, кл. Ямбол','UBBS7368','UBBSBGSF','United Bulgarian Bank AD, kl. Qmbol');
INSERT INTO public.su_banks (bank_name,bank_code,bic_code,bank_name_en) VALUES
	 ('Общинска банка АД','SOMB9130','SOMBBGSF','Obshtinska banka AD'),
	 ('ПейМен Груп ООД','PYMN4020','PYMNBGS2','PeiiMen Grup OOD'),
	 ('Пейнетикс АД','PATC4002','PATCBGSF','Peiinetiks AD'),
	 ('ПроКредит Банк (България) АД ','PRCB9230','PRCBBGSF','ProKredit Bank (Bulgaria) AD '),
	 ('Първа инвестиционна банка АД','FINV9150','FINVBGSF','Purva investicionna banka AD'),
	 ('Райвъл (ЕУ) ЕАД','TRUD4005','TRUDBG21','Raiivul (EU) EAD'),
	 ('Ситибанк Европа АД, клон България','CITI9250','CITIBGSF','Sitibank Evropa AD, klon Bulgaria'),
	 ('ТИ БИ АЙ Банк ЕАД','TBIB9310','TBIBBGSF','TI BI AII Bank EAD'),
	 ('Те-Дже Зираат Банкасъ - клон София','TCZB9350','TCZBBGSF','Te-Dje Ziraat Bankasu - klon Sofiq'),
	 ('Тексим Банк  АД','TEXI9545','TEXIBGSF','Teksim Bank  AD');
INSERT INTO public.su_banks (bank_name,bank_code,bic_code,bank_name_en) VALUES
	 ('Тенен Пеймънтс АД','TEPJ4013','TEPJBGSF','Tenen Peiimunts AD'),
	 ('Токуда Банк АД','CREX9260','CREXBGSF','Tokuda Bank AD'),
	 ('Транскарт  Файненшъл  Сървисис АД','TRIV4003','TRIVBGS1','Transkart  Faiinenshul  Survisis AD'),
	 ('Търговска банка Д АД','DEMI9240','DEMIBGSF','Turgovska banka D AD'),
	 ('УниКредит Булбанк АД','UNCR9660','UNCRBGSF','UniCredit Bulbank AD'),
	 ('УниКредит Булбанк АД - Виртуален клон ','UNCR8115','UNCRBGSF','UniCredit Bulbank AD - Virtualen klon '),
	 ('УниКредит Булбанк АД - Виртуален клон, Касова дейност','UNCR8135','UNCRBGSF','UniCredit Bulbank AD - Virtualen klon, Kasova deiinost'),
	 ('УниКредит Булбанк АД - кл. Булбанк','UNCR7630','UNCRBGSF','UniCredit Bulbank AD - kl. Bulbank'),
	 ('УниКредит Булбанк АД - кл. Хеброс','UNCR7527','UNCRBGSF','UniCredit Bulbank AD - kl. Hebros'),
	 ('УниКредит Булбанк АД - кл. Централизирана система','UNCR7000','UNCRBGSF','UniCredit Bulbank AD - kl. Centralizirana sistema');
INSERT INTO public.su_banks (bank_name,bank_code,bic_code,bank_name_en) VALUES
	 ('Централна кооперативна банка АД','CECB9790','CECBBGSF','Centralna kooperativna banka AD'),
	 ('Юробанк България АД','BPBI9920','BPBIBGSF','YUROBANK Bulgaria AD'),
	 ('Юробанк България АД, 115','BPBI7115','BPBIBGSF','YUROBANK Bulgaria AD, 115'),
	 ('Юробанк България АД, 170','BPBI8170','BPBIBGSF','YUROBANK Bulgaria AD, 170'),
	 ('Юробанк България АД, 898','BPBI8898','BPBIBGSF','YUROBANK Bulgaria AD, 898'),
	 ('Юробанк България АД, 921','BPBI7921','BPBIBGSF','YUROBANK Bulgaria AD, 921'),
	 ('Юробанк България АД, 922','BPBI7922','BPBIBGSF','YUROBANK Bulgaria AD, 922'),
	 ('Юробанк България АД, 923','BPBI7923','BPBIBGSF','YUROBANK Bulgaria AD, 923'),
	 ('Юробанк България АД, 924','BPBI7924','BPBIBGSF','YUROBANK Bulgaria AD, 924'),
	 ('Юробанк България АД, 925','BPBI7925','BPBIBGSF','YUROBANK Bulgaria AD, 925');
INSERT INTO public.su_banks (bank_name,bank_code,bic_code,bank_name_en) VALUES
	 ('Юробанк България АД, 926','BPBI7926','BPBIBGSF','YUROBANK Bulgaria AD, 926'),
	 ('Юробанк България АД, 927','BPBI7927','BPBIBGSF','YUROBANK Bulgaria AD, 927'),
	 ('Юробанк България АД, 928','BPBI7928','BPBIBGSF','YUROBANK Bulgaria AD, 928'),
	 ('Юробанк България АД, 929','BPBI7929','BPBIBGSF','YUROBANK Bulgaria AD, 929'),
	 ('Юробанк България АД, 930','BPBI7930','BPBIBGSF','YUROBANK Bulgaria AD, 930'),
	 ('Юробанк България АД, 931','BPBI7931','BPBIBGSF','YUROBANK Bulgaria AD, 931'),
	 ('Юробанк България АД, 932','BPBI7932','BPBIBGSF','YUROBANK Bulgaria AD, 932'),
	 ('Юробанк България АД, 933','BPBI7933','BPBIBGSF','YUROBANK Bulgaria AD, 933'),
	 ('Юробанк България АД, 934','BPBI7934','BPBIBGSF','YUROBANK Bulgaria AD, 934'),
	 ('Юробанк България АД, 935','BPBI7935','BPBIBGSF','YUROBANK Bulgaria AD, 935');
INSERT INTO public.su_banks (bank_name,bank_code,bic_code,bank_name_en) VALUES
	 ('Юробанк България АД, 936','BPBI7936','BPBIBGSF','YUROBANK Bulgaria AD, 936'),
	 ('Юробанк България АД, 937','BPBI7937','BPBIBGSF','YUROBANK Bulgaria AD, 937'),
	 ('Юробанк България АД, 938','BPBI7938','BPBIBGSF','YUROBANK Bulgaria AD, 938'),
	 ('Юробанк България АД, 939','BPBI7939','BPBIBGSF','YUROBANK Bulgaria AD, 939'),
	 ('Юробанк България АД, 940','BPBI7940','BPBIBGSF','YUROBANK Bulgaria AD, 940'),
	 ('Юробанк България АД, 941','BPBI7941','BPBIBGSF','YUROBANK Bulgaria AD, 941'),
	 ('Юробанк България АД, 942','BPBI7942','BPBIBGSF','YUROBANK Bulgaria AD, 942'),
	 ('Юробанк България АД, 943','BPBI7943','BPBIBGSF','YUROBANK Bulgaria AD, 943'),
	 ('Юробанк България АД, 944','BPBI7944','BPBIBGSF','YUROBANK Bulgaria AD, 944'),
	 ('Юробанк България АД, 945','BPBI7945','BPBIBGSF','YUROBANK Bulgaria AD, 945');
INSERT INTO public.su_banks (bank_name,bank_code,bic_code,bank_name_en) VALUES
	 ('Юробанк България АД, 946','BPBI7946','BPBIBGSF','YUROBANK Bulgaria AD, 946'),
	 ('Юробанк България АД, 947','BPBI7947','BPBIBGSF','YUROBANK Bulgaria AD, 947'),
	 ('Юробанк България АД, 948','BPBI7948','BPBIBGSF','YUROBANK Bulgaria AD, 948'),
	 ('Юробанк България АД, 949','BPBI7949','BPBIBGSF','YUROBANK Bulgaria AD, 949');
