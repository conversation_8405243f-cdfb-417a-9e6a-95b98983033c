<?php

namespace TF\Commands\v4_1;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-6711_1 command run on all databases.
 */
class TS6711_1Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.1:TS-6711_1')
            ->setDescription('Add columns avg_yield, treatment_price, comment and auto_crops_divide in su_renta_types');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $this->executeSql($pdo, __DIR__ . '/sql/TS-6711_1.sql');
    }
}
