<?php

namespace TF\Commands\v4_1;

use PDO;
use TF\Commands\Common\MainDbCommand;

/**
 * GPS-3520 command.
 */
class GPS3520Command extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.1:GPS-3520')
            ->setDescription('Update requested ekatte status enum');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $this->executeSql($pdo, __DIR__ . '/sql/GPS-3520.sql');
    }
}
