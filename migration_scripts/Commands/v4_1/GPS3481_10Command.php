<?php

namespace TF\Commands\v4_1;

use PDO;
use TF\Application\Entity\UserLayers;
use TF\Commands\Common\MainDbCommand;

/**
 * GPS3481_10Command run on susi_main database.
 *
 * This command will create the virtual columns for all remote layers
 */
class GPS3481_10Command extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.1:GPS-3481-10')
            ->setDescription('This command will create the virtual columns for all remote layers. Note: this command uses the default definitions from the config to get the virtual columns.');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        foreach ($GLOBALS['Layers']['remoteTables'] as $remoteTableType => $remoteTableName) {
            $definitions = UserLayers::getDefinitionsByType($remoteTableType);
            $virtualColumnDefinitions = UserLayers::filterDefinitions($definitions, [['col_virtual' => true]]);

            if (!count($virtualColumnDefinitions)) {
                continue;
            }

            $output->write("Creating virtual columns for {$remoteTableName}...");
            foreach ($virtualColumnDefinitions as $virtualColumnDef) {
                $columnName = $virtualColumnDef['col_name'];
                $columnType = $GLOBALS['Layers']['columnTypesByCategory'][$virtualColumnDef['col_category']] ?? 'VARCHAR';
                $columnExpression = $virtualColumnDef['col_expression'];

                $layersCreateVirtualColumnsSql = "ALTER TABLE {$remoteTableName} ADD COLUMN IF NOT EXISTS {$columnName} {$columnType} GENERATED ALWAYS AS ({$columnExpression}) STORED;\n";
                $stmt = $pdo->prepare($layersCreateVirtualColumnsSql);
                $stmt->execute();
            }
            $output->writeln("\tDone!");
        }
    }
}
