<?php

namespace TF\Commands\v4_1;

use PDO;
use PDOEx<PERSON>;
use TF\Commands\Common\MainDbCommand;

/**
 * GPS3227Command command run on all databases.
 */
class GPS3227Command extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.1:GPS-3227')
            ->setDescription('Fix label_name property in the \'style\' column of su_users_layers table');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $query = "
            WITH fixed_labels_data AS (
                SELECT
                    id,
                    (su_users_layers.\"style\"::jsonb || jsonb_build_object('label_name', jsonb_agg(a.value)))::json AS \"style\"
                FROM 
                    su_users_layers,
                    json_each(\"style\"->'label_name') a
                WHERE
                    \"style\"->>'label_name' ILIKE '%{%'
                GROUP BY
                    su_users_layers.id
            )
            UPDATE su_users_layers
            SET \"style\" = fixed_labels_data.\"style\"
            FROM fixed_labels_data
            WHERE fixed_labels_data.id = su_users_layers.id
        ";

        try {
            $pdo->exec($query);
            $output->writeln("Table 'su_user_layers' updated successfully. Fixed su_user_layers->style->label_name property.");
        } catch (PDOException $e) {
            $output->writeln('Error: ' . $e->getMessage());
        }
    }
}
