<?php

namespace TF\Commands\v4_1;

use PDO;
use TF\Application\Common\Config;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS3540_2Command run on user databases.
 *
 * This command fixes the type of column 'watering' in  isak layer tables
 */
class GPS3540_2Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.1:GPS-3540-2')
            ->setDescription('This command fixes the type of column \'watering\' in  isak layer tables');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $colCategoryNumber = Config::LAYER_COLUMN_CATEGORY_NUMBER;
        $layerTypeIsak = Config::LAYER_TYPE_ISAK;
        $dbLink = 'host=' . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD;

        $isakTablesSql = "SELECT
                ul.table_name
            FROM
                dblink(
                    '{$dbLink}',
                    $$
                        SELECT
                            table_name
                        FROM
                            su_users_layers AS sul
                        JOIN su_users AS su 
                            ON sul.user_id = su.id
                        WHERE
                            su.database = '{$this->userDbName}'
                            AND sul.layer_type = {$layerTypeIsak}
                    $$
                ) AS ul (table_name VARCHAR)
            JOIN information_schema.columns AS cols
                ON cols.table_name = ul.table_name
                AND cols.column_name = 'watering'
                AND cols.data_type = 'boolean'
            ";

        $isakTablesStmt = $pdo->prepare($isakTablesSql);
        $result = $isakTablesStmt->execute();
        $isakTables = $isakTablesStmt->fetchAll(PDO::FETCH_COLUMN);

        foreach ($isakTables as $isakTable) {
            $output->writeln("Altering column watering in table {$isakTable}...");

            $sql = "ALTER TABLE {$isakTable} ALTER COLUMN watering TYPE NUMERIC USING  CASE WHEN watering = TRUE THEN 1 ELSE 0 END;";

            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute();

            if (!$result) {
                $output->error("Failed to alter column watering in table {$isakTable}");

                continue;
            }

            $output->info('Done!');
        }
    }
}
