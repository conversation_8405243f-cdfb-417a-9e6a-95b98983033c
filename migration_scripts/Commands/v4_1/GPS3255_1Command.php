<?php

namespace TF\Commands\v4_1;

use PDO;
use TF\Commands\Common\MainDbCommand;

/**
 * GPS-3255 command.
 */
class GPS3255_1Command extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.1:GPS-3255_1')
            ->setDescription('Create new table countries');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $this->executeSql($pdo, __DIR__ . '/sql/GPS-3255_1.sql');
    }
}
