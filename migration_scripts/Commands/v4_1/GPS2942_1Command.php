<?php

namespace TF\Commands\v4_1;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS2942_1Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.1:GPS-2942_1')
            ->setDescription('Create tables su_consolidation_do and su_consolidation_zd.');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $this->executeSql($pdo, __DIR__ . '/sql/GPS-2942_1.sql');
    }
}
