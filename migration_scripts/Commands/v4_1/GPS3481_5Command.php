<?php

namespace TF\Commands\v4_1;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS3481_5Command run on user databases.
 *
 * This command will create db function returning crop_name by crop_code
 */
class GPS3481_5Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.1:GPS-3481-5')
            ->setDescription('Create db function returning crop_name by crop_code in user databases');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $sql = "CREATE OR REPLACE FUNCTION get_crop_name_by_code(code VARCHAR)
            RETURNS VARCHAR IMMUTABLE AS
            $$
                -- Note: If you use this function in virtual column, in order to refresh the calculated value
                -- you need to update the virtual column - for example: UPDATE {table} set {virtual_column_name} = {virtual_column_name}
                DECLARE res VARCHAR;
                BEGIN
                    SELECT 
                        crop_name || ' (' || crop_code || ')' INTO res
                    FROM
                        public.su_crop_codes
                    WHERE
                        crop_code = code;
                    RETURN res;
                END;
            $$ LANGUAGE plpgsql;";
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
    }
}
