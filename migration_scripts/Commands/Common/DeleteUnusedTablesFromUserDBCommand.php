<?php

namespace TF\Commands\Common;

use PDO;
use PDOException;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Application\Common\Config;
use TF\Commands\ParamHelper;

class DeleteUnusedTablesFromUserDBCommand extends BaseCommand
{
    private $currentUserDBName = '';
    private $currentUserDBInstance;
    private $logsDirPath;
    private $logFilePath;

    protected function configure()
    {
        $this
            ->setName('tf:delete_unused_tables_from_user_db')
            ->setDescription('Delete unused tables from user db')
            ->addArgument(
                'user_databases',
                InputArgument::IS_ARRAY | InputArgument::OPTIONAL,
                'Databases to run the script on (separate multiple names with space)'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $this->logsDirPath = __DIR__ . '/Logs/';
        $classNameWithoutNamespace = basename(str_replace('\\', '/', get_called_class()));
        $this->logFilePath = $this->logsDirPath . $classNameWithoutNamespace . '.log';

        if (file_exists($this->logFilePath)) {
            unlink($this->logFilePath);
        }

        $mainDB = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
        $users = $this->getUsers($input, $mainDB);

        $output->writeln('Find ' . count($users) . ' users');

        foreach ($users as $key => $user) {
            try {
                $this->currentUserDBInstance = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $user['database'] . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
                $this->currentUserDBName = $user['database'];
            } catch (PDOException $PDOException) {
                if (7 !== $PDOException->getCode()) {
                    $output->writeln('ERROR :' . $PDOException->getMessage());
                }

                continue;
            }
            $output->writeln('DELETE KVS TEMP TABLES WHICH ARE NOT USED ANYMORE');
            $kvsTempTablePrefix = 'layer_tmp_kvs_';
            $tmpKVStables = $this->getTables($kvsTempTablePrefix . '%');

            foreach ($tmpKVStables as $table) {
                $kvsContractsMatView = str_replace($kvsTempTablePrefix, 'kvs_contracts_update_', $table['table_name']);

                $contractsForUpdate = $this->getDataFromTable($kvsContractsMatView);

                if (empty($contractsForUpdate)) {
                    $this->deleteMatView($kvsContractsMatView);
                    $this->deleteTable($table['table_name']);
                    $output->writeln('Deleted ' . $table['table_name'] . '  from database : ' . $user['database']);
                } else {
                    $this->logging('Table ' . $table['table_name'] . ' can\'t be deleted. There are ' . count($contractsForUpdate) . ' contracts for update ' . PHP_EOL);
                }
            }

            $output->writeln('DELETE WORK LAYER TABLES WHICH ARE NOT EXISTS IN su_users_layers');
            $workLayerTables = $this->getTables('layer_work_%');
            foreach ($workLayerTables as $workLayerTable) {
                if (!in_array($workLayerTable['table_name'], $user['layer_tables'])) {
                    $this->deleteTable($workLayerTable['table_name']);
                    $output->writeln('Deleted ' . $workLayerTable['table_name'] . '  from database : ' . $user['database']);
                }
            }

            // import_0 is table used for import of data from excel. It is not used anymore and can be deleted
            $this->deleteTable('import_0');

            $output->writeln('' . ($key + 1) . ' / ' . count($users) . ' users processed');
        }
    }

    /**
     * @param PDO $userDb
     * @param string $tableName
     *
     * @return array|bool
     */
    protected function getTables($tableName)
    {
        $cmd = $this->currentUserDBInstance->prepare(
            "SELECT TABLE_NAME as table_name FROM information_schema.COLUMNS WHERE TABLE_NAME LIKE '{$tableName}' GROUP BY TABLE_NAME"
        );
        $cmd->execute();

        return $cmd->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * @param PDO $userDb
     * @param string $tableName
     *
     * @return array|bool
     */
    protected function deleteTable($tableName)
    {
        $cmd = $this->currentUserDBInstance->prepare(
            'DROP TABLE IF EXISTS ' . $tableName . ';'
        );
        $cmd->execute();

        $this->logging('Deleted TABLE:' . $tableName . ' from database : ' . $this->currentUserDBName . PHP_EOL);
    }

    /**
     * @param PDO $userDb
     * @param string $tableName
     *
     * @return array|bool
     */
    protected function deleteMatView($tableName)
    {
        $cmd = $this->currentUserDBInstance->prepare(
            'DROP MATERIALIZED VIEW IF EXISTS ' . $tableName . ';'
        );
        $cmd->execute();

        $this->logging('Deleted MATERIALIZED VIEW:' . $tableName . ' from database : ' . $this->currentUserDBName . PHP_EOL);
    }

    protected function getUsers(InputInterface $input, $mainDev)
    {
        $user_databases = $input->getArgument('user_databases');
        $paramHelper = new ParamHelper();
        $userDbString = $paramHelper->createQueryParamString($user_databases);
        $userDbConditions = '';
        if ($user_databases) {
            $userDbConditions = ' and u.database IN (' . $userDbString . ')';
        }

        $sql = $mainDev->prepare(
            "SELECT 
                u.group_id AS group_id, 
                u.database,
                string_agg(distinct sul.table_name, ',') as layer_tables
            FROM su_users u 
            left join su_users_layers sul on sul.group_id = u.group_id and sul.layer_type in (" . Config::LAYER_TYPE_WORK_LAYER . ")
            WHERE
                u.database is not null
            {$userDbConditions}
            GROUP BY u.group_id, u.database
            ORDER BY database"
        );

        $sql->execute();

        $users = $sql->fetchAll(PDO::FETCH_ASSOC);

        foreach ($users as $key => $user) {
            $users[$key]['layer_tables'] = explode(',', $user['layer_tables']);
        }

        return $users;
    }

    /**
     * @param PDO $userDb
     * @param string $tableName
     *
     * @return array|bool
     */
    protected function getDataFromTable($tableName)
    {
        $sql = $this->currentUserDBInstance->prepare(
            'SELECT * from ' . $tableName . ';'
        );
        $sql->execute();

        return $sql->fetchAll(PDO::FETCH_ASSOC);
    }

    private function logging($data)
    {
        if (!is_dir($this->logsDirPath)) {
            mkdir($this->logsDirPath, 0777, true);
        }

        file_put_contents($this->logFilePath, $data, FILE_APPEND);
    }
}
