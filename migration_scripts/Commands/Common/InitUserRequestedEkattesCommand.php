<?php

namespace TF\Commands\Common;

use PDO;
use PDOException;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Application\Entity\RequestedEkatte;
use TF\Commands\ParamHelper;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

class InitUserRequestedEkattesCommand extends BaseCommand
{
    private $currentOrganizationDBName = '';
    private $currentOrganizationDBInstance;
    private $userDbController;

    protected function configure()
    {
        $this
            ->setName('tf:init_user_requested_ekattes')
            ->setDescription('Fill user requested ekattes table with data from user layer_kvs table')
            ->addArgument('user_databases', InputArgument::IS_ARRAY | InputArgument::OPTIONAL, 'Databases to run the script on (separate multiple names with a space)');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $mainDB = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
        $organizations = $this->getOrganizations($input, $mainDB);

        $output->writeln('Find ' . count($organizations) . ' organizations');

        foreach ($organizations as $key => $organization) {
            $output->writeln('Processing organization with database: ' . $organization['database']);

            try {
                $this->currentOrganizationDBInstance = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $organization['database'] . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
                $this->currentOrganizationDBName = $organization['database'];

                $this->userDbController = new UserDbController($organization['database']);
            } catch (PDOException $PDOException) {
                if (7 !== $PDOException->getCode()) {
                    $output->writeln('ERROR :' . $PDOException->getMessage());
                }

                continue;
            }

            $viewEkattes = $this->getMaterializedViews('kvs_contracts_update_%');
            $kvsEkattes = $this->getOrganziationEkattes();

            $valuesNotInKvsToRemove = array_diff($viewEkattes, array_column($kvsEkattes, 'ekatte_code'));
            $this->dropViewsThatNotInKvs($valuesNotInKvsToRemove);

            foreach ($kvsEkattes as $ekatte) {
                $kvsContractsViewExists = $this->recreateViewIfValid($ekatte['ekatte_code']);

                if (true == $kvsContractsViewExists) {
                    $this->validateFileStatus($organization['group_id'], $ekatte['ekatte_code'], $mainDB);
                }

                $requestedEkatte = new RequestedEkatte();
                $requestedEkatte->user_id = $organization['group_id'];
                $requestedEkatte->group_id = $organization['group_id'];
                $requestedEkatte->ekatte_code = $ekatte['ekatte_code'];
                $requestedEkatte->ekatte_name = $ekatte['ekatte_name'];
                $requestedEkatte->status = (true === $kvsContractsViewExists) ? RequestedEkatte::STATUS_RECEIVED : RequestedEkatte::STATUS_FOR_SYNC;
                $requestedEkatte->kvs_store_uuid = null;
                $requestedEkatte->save();
            }

            $output->writeln('' . ($key + 1) . ' / ' . count($organizations) . ' organizations processed');
        }
    }

    protected function getOrganizations(InputInterface $input, $mainDb)
    {
        $databases = $input->getArgument('user_databases');
        $paramHelper = new ParamHelper();
        $userDbString = $paramHelper->createQueryParamString($databases);
        $whereDatabases = '';
        if ($databases) {
            $whereDatabases = ' and u.database in (' . $userDbString . ')';
        }

        $sql = $mainDb->prepare(
            "SELECT
                u.group_id AS group_id, 
                u.database
            FROM su_users u
            LEFT JOIN su_requested_ekattes on u.group_id = su_requested_ekattes.group_id
            WHERE
                u.database ~ '^db_bg_[0-9]+$'
                and
                u.level = 2
                and su_requested_ekattes.group_id is null
                " . $whereDatabases . '
            GROUP BY u.group_id,u.database
            ORDER BY database'
        );
        $sql->execute();

        return $sql->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * @param PDO $userDb
     * @param string $tableName
     *
     * @return array|bool
     */
    protected function getOrganziationEkattes()
    {
        $sql = $this->currentOrganizationDBInstance->prepare("
        WITH 
        organization_ekates AS (
            SELECT DISTINCT layer_kvs.ekate FROM public.layer_kvs layer_kvs
        ),
        ekattes AS (
            SELECT
                ekattes.ekatte_code,
                ekattes.ekatte_name
            FROM dblink('host=" . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' dbname=' . DEFAULT_DB_DATABASE . "',
                'SELECT ekatte_code, ekatte_name FROM su_ekatte') AS ekattes(ekatte_code VARCHAR, ekatte_name VARCHAR(255))
            INNER JOIN organization_ekates ON ekattes.ekatte_code = organization_ekates.ekate
        )
        SELECT * FROM ekattes
        ");

        $sql->execute();

        return $sql->fetchAll(PDO::FETCH_ASSOC);
    }

    private function validateFileStatus($groupId, $ekatte, $mainDB)
    {
        $updateSql = "
            UPDATE su_users_files
            SET 
                ekate = CASE WHEN (ekate is NULL or ekate = '') THEN :new_ekate ELSE ekate END,
                status = CASE WHEN status != :not_updated_contracts THEN :new_status ELSE status END
            WHERE 
                id = (
                    SELECT id
                    FROM su_users_files
                    WHERE
                        group_id = :group_id
                        AND
                        (ekate = :ekate OR name ilike ('%{$ekatte}') )
                        and
                        (status != :not_updated_contracts or ekate is NULL or ekate = '')
                    ORDER BY id DESC
                    LIMIT 1
                )
        ";
        $cmd = $mainDB->prepare($updateSql);
        $cmd->bindParam(':new_ekate', $ekatte);
        $cmd->bindValue(':not_updated_contracts', NOT_UPDATED_CONTRACTS);
        $cmd->bindValue(':new_status', NOT_UPDATED_CONTRACTS);
        $cmd->bindParam(':group_id', $groupId);
        $cmd->bindParam(':ekate', $ekatte);
        $cmd->execute();
    }

    private function getMaterializedViews($viewName): array
    {
        $sql = "SELECT REPLACE(matviewname, 'kvs_contracts_update_', '') AS name FROM pg_matviews WHERE matviewname ILIKE :pattern";
        $cmd = $this->currentOrganizationDBInstance->prepare($sql);
        $pattern = $viewName . '%'; // Modify pattern for ILIKE search
        $cmd->bindParam(':pattern', $pattern);
        $cmd->execute();

        return $cmd->fetchAll(PDO::FETCH_COLUMN);
    }

    private function getMaterializedViewExists($viewName): bool
    {
        $sql = 'SELECT 1 FROM pg_matviews WHERE matviewname = :view_name';

        $cmd = $this->currentOrganizationDBInstance->prepare($sql);
        $cmd->bindParam(':view_name', $viewName);
        $cmd->execute();

        return $cmd->fetchColumn() ? true : false;
    }

    private function recreateViewIfValid($ekatte): bool
    {
        $viewName = "kvs_contracts_update_{$ekatte}";
        $tmpTableName = "layer_tmp_kvs_{$ekatte}";
        $kvsContractsViewExists = $this->getMaterializedViewExists($viewName);

        if ($kvsContractsViewExists) {
            // Check if the view is empty
            $checkViewSql = "SELECT new_kad_idents FROM {$viewName}";
            $result = $this->currentOrganizationDBInstance->query($checkViewSql)->fetchAll(PDO::FETCH_ASSOC);

            $conflictedNewIdents = [];
            foreach ($result as $row) {
                $conflictedNewIdents = array_merge($conflictedNewIdents, array_map('trim', explode(',', trim($row['new_kad_idents'], '{}'))));
            }
            $conflictedNewIdents = array_unique($conflictedNewIdents);

            $placeholders = implode(',', array_fill(0, count($conflictedNewIdents), '?'));
            $sql = "SELECT kad_ident FROM layer_kvs WHERE kad_ident IN ({$placeholders})";
            $stmt = $this->currentOrganizationDBInstance->prepare($sql);
            foreach ($conflictedNewIdents as $index => $kadIdent) {
                $stmt->bindValue($index + 1, trim($kadIdent));
            }
            $stmt->execute();
            $kvsIdents = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $viewContainsInvalidPlots = false;
            if (count($conflictedNewIdents) != count($kvsIdents)) {
                $viewContainsInvalidPlots = true;
            }

            // drop the view so if it is not empty, it will be recreated
            $dropViewSql = "DROP MATERIALIZED VIEW IF EXISTS {$viewName}";
            $this->currentOrganizationDBInstance->exec($dropViewSql);

            if (0 == count($result) || true == $viewContainsInvalidPlots) {
                $dropTmpTableSql = "DROP TABLE IF EXISTS {$tmpTableName}";
                $this->currentOrganizationDBInstance->exec($dropTmpTableSql);

                $kvsContractsViewExists = false;
            } else {
                // If the view is not empty, drop and recreate it
                $this->userDbController->createKvsContractsUpdateView(null, $ekatte, null);
                $kvsContractsViewExists = true;
            }
        }

        return $kvsContractsViewExists;
    }

    private function dropViewsThatNotInKvs($ekattes)
    {
        foreach ($ekattes as $ekatte) {
            $viewName = "kvs_contracts_update_{$ekatte}";
            $dropViewSql = "DROP MATERIALIZED VIEW IF EXISTS {$viewName}";

            $this->currentOrganizationDBInstance->exec($dropViewSql);

            $tmpTableName = "layer_tmp_kvs_{$ekatte}";
            $dropTmpTableSql = "DROP TABLE IF EXISTS {$tmpTableName}";
            $this->currentOrganizationDBInstance->exec($dropTmpTableSql);
        }
    }
}
