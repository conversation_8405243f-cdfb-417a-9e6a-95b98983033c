<?php

namespace TF\Commands\Common;

use PDO;
use Prado;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Commands\ParamHelper;

Prado::using('Plugins.Core.UserDb.*');

/**
 * TS-5157 command run on all databases.
 */
class MigrateDiaryProductsOptionsCommand extends BaseCommand
{
    public const CONFIG_TYPE = 7;

    protected function configure()
    {
        $this
            ->setName('tf:migrate_diary_products_options')
            ->setDescription('Migrate json format in options (su_diary_config) - TS-5157')
            ->addArgument(
                'user_databases',
                InputArgument::IS_ARRAY | InputArgument::OPTIONAL,
                'Databases to run the script on (separate multiple names with a space)?'
            )
            ->addOption('year', null, InputOption::VALUE_OPTIONAL, 'If set, the task will only on users with paid support for the inpit year');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $user_databases = $input->getArgument('user_databases');
        $paramHelper = new ParamHelper();
        $userDbString = $paramHelper->createQueryParamString($user_databases);

        $userDbConditions = 'true';
        if ($user_databases) {
            $userDbConditions = 'u.database IN (' . $userDbString . ')';
        }

        $mainDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = $mainDev->prepare(
            "SELECT u.id AS id, u.database FROM su_users u
            WHERE 
            {$userDbConditions}
            AND u.level = 2
            ORDER BY database"
        );

        $sql->execute();
        $results = $sql->fetchAll();

        foreach ($results as $result) {
            $pdo = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $result['database'] . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
            $id_array[] = $result['id'];
            $options = $this->getOptions($pdo);
            foreach ($options as $option) {
                $editedOptions = null;
                $optionsArr = json_decode($option['options'], true);

                if (!empty($optionsArr['product_type'])) {
                    continue;
                } elseif (empty($optionsArr)) {
                    $editedOptions = $this->setPendingOption($optionsArr);
                } elseif (!empty($option['warehouse_item_id'])) {
                    $editedOptions = $this->setPendingOption($optionsArr);
                } elseif (isset($optionsArr['fertilizer'], $optionsArr['chemical_treatment'])) {
                    unset($optionsArr['fertilizer'], $optionsArr['chemical_treatment']);
                    $editedOptions = $this->setPendingOption($optionsArr);
                } elseif (!empty($optionsArr) && array_key_exists('', $optionsArr)) {
                    $editedOptions = $this->setPendingOption([]);
                } elseif (1 === count($optionsArr) && isset($optionsArr['fertilizer'])) {
                    $editedOptions = $this->setProductType('fertilizer');
                } elseif (1 === count($optionsArr) && isset($optionsArr['chemical_treatment'])) {
                    $editedOptions = $this->setProductType('chemical_treatment');
                } else {
                    continue;
                }

                if (!empty($editedOptions) && $editedOptions !== $option['options']) {
                    $this->saveOptions($pdo, $option['id'], $editedOptions);
                }
            }
            echo 'Database: ' . $result['database'] . ' - DONE' . PHP_EOL;
        }
    }

    private function getOptions($pdo)
    {
        $sql = 'SELECT id, options, warehouse_item_id FROM public.su_diary_configs as c WHERE  config_type = ' . self::CONFIG_TYPE;

        $results = $pdo->prepare($sql);
        $results->execute();

        return $results->fetchAll();
    }

    private function setPendingOption($options)
    {
        $options['product_type'] = '';
        $options['status'] = 'pending';

        return json_encode($options);
    }

    /**
     * @return false|string
     */
    private function setProductType($productType)
    {
        $options['product_type'] = $productType;
        $options['status'] = 'active';

        return json_encode($options);
    }

    private function saveOptions($pdo, $id, $options)
    {
        $sql = "UPDATE public.su_diary_configs SET options = '{$options}' WHERE  id = " . $id;
        $results = $pdo->prepare($sql);
        $results->execute();
    }
}
