<?php

namespace TF\Commands\Common;

use PDO;
use Prado;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Commands\ParamHelper;
use TJsonRpcProtocol;
use TRpcServer;
use UserDbDiaryController;

Prado::using('Common.Config');
Prado::using('BaseApp.vendor.pradosoft.prado.framework.Web.Services.TRpcService');
Prado::using('Plugins.Core.UserDbDiary.*');

/**
 * Class ClearWialonDataCommand.
 */
class ClearWialonDataCommand extends BaseCommand
{
    private $server;
    private $diaryController;
    private $sid;
    private $accountID;
    private $wialonUserID;

    protected function configure()
    {
        $this->setName('tf:clear_wialon_data')
            ->setDescription('Delete all TF generated reports, geofences and units groups in user\'s FarmTrack accounts.')
            ->addArgument(
                'user_databases',
                InputArgument::IS_ARRAY | InputArgument::OPTIONAL,
                'Databases to run the script on (separate multiple names with a space)?'
            );
    }

    /**
     * @return null|int|void
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $user_databases = $input->getArgument('user_databases');

        $userDbWhere = 'true';

        if ($user_databases) {
            $userDbWhere = 'database IN (' . (new ParamHelper())->createQueryParamString($user_databases) . ')';
        }

        $mainDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = $mainDev->prepare("SELECT id, username, 
        name, track_username, track_password, track_token, database
        FROM su_users u WHERE {$userDbWhere} AND u.level IN(2,3) 
        AND track_token is not null ORDER BY database");

        $sql->execute();

        $results = $sql->fetchAll(PDO::FETCH_ASSOC);

        if (empty($results)) {
            return;
        }

        $this->server = new TRpcServer(new TJsonRpcProtocol());

        foreach ($results as $userDb) {
            $this->diaryController = new UserDbDiaryController($userDb['database']);
            $login_results = $this->diaryController->executeWialonRequest(
                'token/login',
                ['token' => $userDb['track_token']]
            );
            if ($login_results->error && (4 == $login_results->error || 8 == $login_results->error)) {
                // $output->writeln("UserID: {$result['database']} ");
                $output->writeln('Warning: login with user ' . $userDb['username'] . ' failed, skipping.');

                continue;
            }
            $this->sid = $login_results->eid;
            $this->accountID = $login_results->user->bact;
            $this->wialonUserID = $login_results->user->id;

            $_SESSION['wialon_eid'] = $login_results->eid;
            $_SESSION['wialon_accountID'] = $login_results->user->bact;

            $groups = $this->getGroups();
            if (empty($groups)) {
                $output->writeln('INFO: no groups found to delete for user ' . $userDb['username']);
            } else {
                $this->deleteGroups($groups);
                $output->writeln('INFO: deleted ' . count($groups) . ' groups for user ' . $userDb['username']);
            }

            $geofences = $this->getGeofences();
            if (empty($geofences)) {
                $output->writeln('INFO: no geofences found to delete for user ' . $userDb['username']);
            } else {
                $this->deleteGeofences($geofences);
                $output->writeln('INFO: deleted ' . count($geofences) . ' geofences for user ' . $userDb['username']);
            }

            $reports = $this->getReports();
            if (empty($reports)) {
                $output->writeln('INFO: no reports found to delete for user ' . $userDb['username']);
            } else {
                $this->deleteReports($reports);
                $output->writeln('INFO: deleted ' . count($reports) . ' reports for user ' . $userDb['username']);
            }
        }
    }

    /**
     * @return array
     */
    protected function getReports()
    {
        $response = (array)$this->diaryController->executeWialonRequest(
            'core/search_items',
            [
                'spec' => [
                    'itemsType' => 'avl_resource',
                    'propName' => 'reporttemplates',
                    'propValueMask' => '*',
                    'sortType' => 'sys_name',
                ],
                'force' => 1,
                'from' => 0,
                'to' => 0,
                'flags' => 0x00002000],
            $this->sid
        );

        $return = [];

        if (!isset($response['items'])) {
            return $return;
        }
        $response = $response['items'][0]->rep;

        if (!is_array($response)) {
            return $return;
        }

        foreach ($response as $item) {
            if (0 === strpos($item->n, 'TF3333')) {
                $return[] = $item->id;
            }
        }

        return $return;
    }

    /**
     * @param array $reports
     */
    protected function deleteReports($reports)
    {
        foreach ($reports as $reportId) {
            $this->diaryController->executeWialonRequest(
                'report/update_report',
                [
                    'callMode' => 'delete',
                    'itemId' => $this->accountID,
                    'id' => $reportId,
                ],
                $this->sid
            );
        }
    }

    /**
     * @return array
     */
    protected function getGeofences()
    {
        $response = (array)$this->diaryController->executeWialonRequest(
            'core/search_items',
            [
                'spec' => [
                    'itemsType' => 'avl_resource',
                    'propName' => 'zones_library',
                    'propType' => 'propitemname',
                    'propValueMask' => 'TF*',
                    'sortType' => 'sys_name',
                ],
                'force' => 1,
                'from' => 0,
                'to' => 0,
                'flags' => 0x00001001],
            $this->sid
        );

        $return = [];

        if (!isset($response['items'])) {
            return $return;
        }

        $response = $response['items'][0]->zl;

        if (!is_array($response)) {
            return $return;
        }

        foreach ($response as $item) {
            if (0 === strpos($item->n, 'TF_')) {
                $return[] = $item->id;
            }
        }

        return $return;
    }

    /**
     * @param array $geofences
     */
    protected function deleteGeofences($geofences)
    {
        foreach ($geofences as $geofenceID) {
            $this->diaryController->executeWialonRequest(
                'resource/update_zone',
                [
                    'itemId' => $this->accountID, 'id' => $geofenceID,
                    'callMode' => 'delete',
                ],
                $this->sid
            );
        }
    }

    protected function getGroups()
    {
        $response = (array)$this->diaryController->executeWialonRequest(
            'core/search_items',
            [
                'spec' => [
                    'itemsType' => 'avl_unit_group',
                    'propName' => 'sys_name',
                    'propValueMask' => 'MASTER_GROUP',
                    'sortType' => 'sys_name',
                ],
                'force' => 1,
                'from' => 0,
                'to' => 0,
                'flags' => 0x01],
            $this->sid
        );

        $return = [];

        if (!isset($response['items']) || !is_array($response['items'])) {
            return $return;
        }

        foreach ($response['items'] as $item) {
            $return[] = $item->id;
        }

        return $return;
    }

    /**
     * @param array $groups
     */
    protected function deleteGroups($groups)
    {
        foreach ($groups as $group) {
            $this->diaryController->executeWialonRequest(
                'item/delete_item',
                [
                    'itemId' => $group,
                ],
                $this->sid
            );
        }
    }
}
