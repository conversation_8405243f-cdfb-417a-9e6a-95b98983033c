<?php

namespace TF\Commands\Common;

use PDO;

class GenerateStatisticOfInvalidPlotAreasCommand extends UserDbCommand
{
    private static $tmpTableName = 'tmp_invalid_plot_areas';

    public function configure()
    {
        parent::configure();
        $this
            ->setName('tf:generate_invalid_plot_areas_statistic')
            ->setDescription('Generate statistics of contracts with invalid plot areas and publish the results in table ' . self::$tmpTableName . ' in susi_main');
    }

    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $plots = $this->getPlotsWithInvalidAreas($pdo);

        foreach ($plots as $plot) {
            $sql = $this->mainConnection->prepare('
                    INSERT INTO ' . self::$tmpTableName . ' (
                        database,
                        pc_rel_id,
                        c_num,
                        contract_type,
                        start_date,
                        due_date,
                        kad_ident,
                        document_area,
                        contract_area,
                        area_for_rent
                    )
                    VALUES (
                        :database,
                        :pc_rel_id,
                        :c_num,
                        :contract_type,
                        :start_date,
                        :due_date,
                        :kad_ident,
                        :document_area,
                        :contract_area,
                        :area_for_rent
                    );
                ');

            $sql->execute([
                'database' => $this->userDbName,
                'pc_rel_id' => $plot['pc_rel_id'],
                'c_num' => $plot['c_num'],
                'contract_type' => $plot['virtual_contract_type'],
                'start_date' => $plot['start_date'],
                'due_date' => $plot['due_date'],
                'kad_ident' => $plot['kad_ident'],
                'document_area' => $plot['document_area'],
                'contract_area' => $plot['contract_area'],
                'area_for_rent' => $plot['area_for_rent'],
            ]);
        }
    }

    protected function onCommandStart($input, $output)
    {
        $sql = $this->mainConnection->prepare('
            CREATE TABLE IF NOT EXISTS ' . self::$tmpTableName . ' (
                database varchar NOT NULL,
                pc_rel_id int NOT NULL,
                c_num varchar NOT NULL,
                contract_type varchar NOT NULL,
                start_date date DEFAULT NULL,
                due_date date DEFAULT NULL,
                kad_ident varchar NOT NULL,
                document_area numeric,
                contract_area numeric,
                area_for_rent numeric
            );
        ');
        $sql->execute();

        $sql = $this->mainConnection->prepare('
            TRUNCATE ' . self::$tmpTableName . ';
        ');
        $sql->execute();
    }

    /**
     * @param PDO $userDb
     * @param string $tableName
     *
     * @return array|bool
     */
    protected function getPlotsWithInvalidAreas($pdo)
    {
        $cmd = $pdo->prepare('
            select 
                scpr.id as pc_rel_id,
                sc.c_num,
                virtual_contract_type,
                sc.start_date,
                sc.due_date,
                kvs.kad_ident,
                kvs.document_area,
                scpr.contract_area,
                scpr.area_for_rent 
            from su_contracts_plots_rel scpr 
            left join layer_kvs kvs on kvs.gid = scpr.plot_id 
            left join su_contracts sc on sc.id = scpr.contract_id 
            where 
                kvs.document_area < scpr.contract_area 
                or scpr.contract_area < scpr.area_for_rent
                or kvs.document_area < scpr.area_for_rent
        ');
        $cmd->execute();

        return $cmd->fetchAll(PDO::FETCH_ASSOC);
    }
}
