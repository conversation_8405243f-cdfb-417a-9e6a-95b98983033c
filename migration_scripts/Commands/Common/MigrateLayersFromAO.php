<?php

namespace TF\Commands\Common;

use PDO;
use PDOException;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class MigrateLayersFromAO extends BaseCommand
{
    protected $skipLogging = true;

    protected function configure()
    {
        parent::configure();

        $this
            ->setName('tf:migrate_layers_ao')
            ->setDescription('Migrate all GIS layers from AO database.')
            ->addArgument('tf_user_db', InputArgument::REQUIRED, 'Technofarm db name')
            ->addArgument('of_user_db', InputArgument::REQUIRED, 'AO db name')
            ->addOption('source_db_host', 'D', InputOption::VALUE_OPTIONAL, 'Source db address', (getenv('AO_DB_HOST')))
            ->addOption('source_db_user', 'U', InputOption::VALUE_OPTIONAL, 'Source database user', (getenv('AO_DB_USER')))
            ->addOption('source_db_password', 'W', InputOption::VALUE_OPTIONAL, 'Source server password', (getenv('AO_DB_PASS')))

            ->addOption('dest_db_host', 'd', InputOption::VALUE_OPTIONAL, 'Destination db address', DEFAULT_DB_HOST)
            ->addOption('dest_db_port', 'p', InputOption::VALUE_OPTIONAL, 'Destination database server port', DEFAULT_DB_PORT)
            ->addOption('dest_db_user', 'u', InputOption::VALUE_OPTIONAL, 'Destination database user', DEFAULT_DB_USERNAME)
            ->addOption('dest_db_password', 'w', InputOption::VALUE_OPTIONAL, 'Destination server password', DEFAULT_DB_PASSWORD);
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $tf_db = $input->getArgument('tf_user_db');
        $ao_db = $input->getArgument('of_user_db');

        $sourceDbHost = $input->getOption('source_db_host');
        $sourceDbUser = $input->getOption('source_db_user');
        $sourceDbPass = $input->getOption('source_db_password');

        $destDbHost = $input->getOption('dest_db_host');
        $destDbPort = $input->getOption('dest_db_port');
        $destDbUser = $input->getOption('dest_db_user');
        $destDbPass = $input->getOption('dest_db_password');

        if (!isset($tf_db) || !isset($ao_db)) {
            $output->error('NO DATABASE');
            die;
        }

        try {
            $destDbConnection = new PDO('pgsql:dbname=susi_main;host=' . $destDbHost . ';port=' . $destDbPort . ';user=' . $destDbUser, $destDbUser, $destDbPass);
            $sourceDbConnection = new PDO('sqlsrv:Server=' . $sourceDbHost . ';Database=' . $ao_db, $sourceDbUser, $sourceDbPass);
        } catch (PDOException $e) {
            $output->error($e->getMessage());
            exit;
        }

        $stmt = $sourceDbConnection->query('SELECT count(*) FROM AMP_ArableLands;');
        $existingUser = $stmt->fetch();
        print($existingUser);
    }
}
