<?php

namespace TF\Commands\Common;

/**
 * TS-2120 command run on all databases.
 */
class UpdateUserTo2017SupportCommand extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('tf:update_user_to_2017_support')
            ->setDescription('smenq godinata na poddryjka za potrebiteli i pod-potrebiteli na 2017');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $this->openConnection();

        $sql = $this->mainConnection->prepare(
            "SELECT u.id AS id FROM su_users u
            WHERE u.database = '{$userDb}' AND u.level = 2
            ORDER BY database"
        );

        $sql->execute();
        $results = $sql->fetchAll();

        $id_array = [];
        foreach ($results as $result) {
            $id_array[] = $result['id'];
        }

        $id_string = implode(', ', $id_array);

        $sql = "UPDATE su_users SET paid_support = '2017-01-01' WHERE group_id IN ({$id_string})";

        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . DEFAULT_DB_DATABASE . ' -c "' . $sql . '"', $return);
    }
}
