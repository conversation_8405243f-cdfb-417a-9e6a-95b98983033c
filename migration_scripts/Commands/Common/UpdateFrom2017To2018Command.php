<?php

namespace TF\Commands\Common;

/**
 * UpdateFrom2017To2018 command run on all databases.
 */
class UpdateFrom2017To2018Command extends UserDbCommand
{
    protected $skipLogging = true;

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('tf:update-from-2017-to-2018')
            ->setDescription('Update na user database ot 2017 do 2018');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $commandsArr = [
            new \TF\Commands\Common\UpdateUserTo2018SupportCommand(),
            new \TF\Commands\sprint_s17\TS3197Command(),
            new \TF\Commands\sprint_s17\TS3198Command(),
            new \TF\Commands\sprint_s17\TS3198_1_Command(),
            new \TF\Commands\sprint_s19\TS3335Command(),
            new \TF\Commands\sprint_s19\TS3716Command(),
            new \TF\Commands\sprint_s20\UpdateToSprint20(),
            new \TF\Commands\sprint_s21\UpdateToSprint21(),
            new \TF\Commands\v2_1\UpdateToVersion2_1(),
        ];

        foreach ($commandsArr as $command) {
            $output->writeln('Executing command: ' . $command->getName());
            $this->logScript($userDb, $command->getName());
            $command->onDbExecute($userDb, $output, $input);
        }
    }

    protected function logScript($user_db, $name)
    {
        $date = date('Y-m-d H:i:s');
        $this->openConnection();
        $sql = $this->mainConnection->prepare("
            INSERT INTO scripts_log (db_name, name, date)
            VALUES ('{$user_db}', '{$name}', '{$date}')
        ");

        return $sql->execute();
    }
}
