<?php

namespace TF\Commands\Common;

use PDO;

/**
 * command run on all databases.
 */
class ClearDataOfWrongDeletedPlots extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('tf:clear-data-of-wrong-deleted-plots')
            ->setDescription('Clear data from su_subleases_plots_area of wrong deleted plots and contracts');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec(
            'delete from su_subleases_plots_area where id in (
            select 
                sspa.id
            from su_subleases_plots_area sspa 
            inner join su_contracts c on c.id = sspa.sublease_id 
            where not exists (
                    select 1 from su_subleases_plots_contracts_rel sspcr 
                    inner join su_contracts_plots_rel scpr on scpr.id = sspcr.pc_rel_id and scpr.plot_id = sspa.plot_id 
                    where sspcr.sublease_id = sspa.sublease_id
                )
            )'
        );
    }
}
