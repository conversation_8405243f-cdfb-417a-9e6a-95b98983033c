<?php

namespace TF\Commands\Common;

use PDO;
use TF\Application\Common\Config;
use TF\Application\Entity\LayerStyles;
use TF\Application\Entity\User;
use TF\Application\Entity\UserLayers;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

class RecreateConsolidatationViewsLayersCommand extends UserDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $user = User::finder()->find('database = :database and level = :level', [':database' => $this->userDbName, ':level' => '2']);
        $this->initAuthUser($user->id);

        if (empty($user->group_id)) {
            $output->writeln('No user found for this database. Skipping...');

            return;
        }

        $UserDbController = new UserDbController($this->userDbName);
        /** @var UserLayers[] $csdUserLayers */
        $csdUserLayers = UserLayers::finder()->findAll('group_id = (:groupId) and layer_type = :csdLayerType', [':groupId' => $user->group_id, ':csdLayerType' => Config::LAYER_TYPE_CSD]);
        foreach ($csdUserLayers as $layer) {
            $tableName = $layer->table_name;
            $output->write("Update CSD view '{$tableName}'");

            [$style] = $layer->getStyles();
            $labelSql = $style->generateMapLabelSQL($layer);

            if (LayerStyles::SINGLE_COLORING_TYPE === $style->type) {
                $coloringOptions = [
                    'fill_color' => $style->fill_color,
                    'border_color' => $style->border_color,
                    'label' => $labelSql,
                    'style_layers_id' => $style->id,
                ];
            } else {
                $csdColumnDefinitions = $layer->getDefinitions();
                [$fillColumnDefinition] = UserLayers::filterDefinitions($csdColumnDefinitions, [['col_name' => $style->fill_column_name]]);
                [$borderColumnDefinition] = UserLayers::filterDefinitions($csdColumnDefinitions, [['col_name' => $style->border_column_name]]);

                $coloringOptions = [
                    'fill_column_definition' => $fillColumnDefinition,
                    'border_column_definition' => $borderColumnDefinition,
                    'label' => $labelSql,
                    'style_layers_id' => $style->id,
                ];
            }

            $modifiedString = str_replace('layer_decl_69_70_', '', $tableName);
            [$ekatte, $farmYear] = explode('_', $modifiedString);

            $UserDbController->createCsdMatView($layer->table_name, $ekatte, $farmYear, $coloringOptions, LayerStyles::BY_ATTRIBUTE_COLORING_TYPE);
            $output->writeln('Done');
        }
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('tf:recreate_consolidation_layer_views')
            ->setDescription('Recreate consolidation layer views for CSD layers');
    }
}
