<?php

namespace TF\Commands\Common;

use Exception;
use PDO;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Engine\Plugins\Core\Users\UsersController;

class UpdateUserPermissionsCommand extends BaseCommand
{
    private $susiMainConnection;
    private $pradoApp;

    public function setPradoApp($pradoApp)
    {
        $this->pradoApp = $pradoApp;
    }

    protected function configure()
    {
        $this->setName('tf:update_user_permissions')
            ->setDescription('Update migrated user from level 3 to level 2 permissions to parent permissions');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $UsersController = new UsersController('Users');

        $this->susiMainConnection = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $auth = $this->pradoApp->getModule('auth');

        $sql = $this->susiMainConnection->prepare(
            "SELECT 
            u.id,
            u.group_id,
            u.database,
            u.username,
            u.name,
            u.level,
            json_agg(sur.right_id) as rights
         FROM 
            su_users as u
            INNER join su_users_rights as sur on sur.user_id = u.id
         WHERE
            u.level = 2
            AND
            LOWER(u.database) like '%db_bg_%'
         group by u.id    
             "
        );

        if (!$sql->execute()) {
            die(print_r($sql->errorInfo()));
        }

        $sql->execute();
        $parentUsers = $sql->fetchAll();

        try {
            foreach ($parentUsers as $parent) {
                // set app user to parent user
                $auth->switchUser($parent['username']);
                $subUsers = $this->getSubUsers($parent['id']);

                foreach ($subUsers as $subUser) {
                    if ($subUser['name'] == $parent['name']) {
                        $UsersController->deleteUserRightsByUserID($subUser['id']);
                        $rightsToGrant = json_decode($parent['rights'], true);

                        foreach ($rightsToGrant as $rightId) {
                            $options = [
                                'user_id' => $subUser['id'],
                                'group_id' => $parent['id'],
                                'right_id' => $rightId,
                            ];
                            $UsersController->addUserRights($options);
                        }
                    }
                }
            }
        } catch (Exception $e) {
            $output->write($e->getMessage());
        }

        $output->write('end');
    }

    private function getSubUsers($parentId)
    {
        $sql = $this->susiMainConnection->prepare(
            'SELECT 
                u.id,
                u.database,
                u.username,
                u.group_id,
                u.name,
                u.level,
                json_agg(sur.right_id) as rights
            FROM su_users as u
            INNER join su_users_rights as sur on sur.user_id = u.id
            WHERE 
                u.parent_id = :parentId
            group by u.id    
            '
        );

        $sql->execute([':parentId' => $parentId]);

        return $sql->fetchAll();
    }
}
