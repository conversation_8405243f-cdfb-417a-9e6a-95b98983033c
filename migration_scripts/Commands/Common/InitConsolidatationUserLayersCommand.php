<?php

namespace TF\Commands\Common;

use PDO;
use PDOException;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Application\Common\Config;
use TF\Engine\Kernel\CronsLoadData\CsdProcessingClass;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

class InitConsolidatationUserLayersCommand extends BaseCommand
{
    private $currentOrganizationDBName = '';
    private $currentOrganizationDBInstance;
    private $userDbController;

    public function getConsolidationData()
    {
        $sql = '
            SELECT
                DISTINCT ekatte, godina 
            FROM su_consolidation_zd
        ';
        $cmd = $this->currentOrganizationDBInstance->prepare($sql);
        $cmd->execute();

        return $cmd->fetchAll(PDO::FETCH_ASSOC);
    }

    protected function configure()
    {
        $this
            ->setName('tf:init_consolidation_user_layers')
            ->setDescription('Generate csd mat views and create su_users_layers record');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $mainDB = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
        $organizations = $this->getOrganizations($input, $mainDB);
        $output->writeln('Found ' . count($organizations) . ' organizations');

        foreach ($organizations as $key => $organization) {
            $output->writeln('Processing organization with database: ' . $organization['database']);

            try {
                $this->currentOrganizationDBInstance = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $organization['database'] . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
                $this->currentOrganizationDBName = $organization['database'];

                $this->userDbController = new UserDbController($organization['database']);

                $csdData = $this->getConsolidationData();

                $csdProcessingClass = new CsdProcessingClass();
                $csdProcessingClass->setDatabase($organization['database']);
                $csdProcessingClass->initControllers($organization['database']);
                $csdProcessingClass->setGroupId($organization['group_id']);
                $csdProcessingClass->setUserId($organization['group_id']);
                $csdProcessingClass->setLayerType();

                foreach ($csdData as $key => ['ekatte' => $ekatte, 'godina' => $year]) {
                    $ekatte = $this->userDbController->findEkatteByCode($ekatte);

                    if (!$ekatte) {
                        throw new PDOException('Ekatte not found', 7);
                    }

                    $csdProcessingClass->setEkatte($ekatte);
                    $csdProcessingClass->setYear($year);
                    $csdProcessingClass->onAfterProcessing();
                }
            } catch (PDOException $PDOException) {
                if (7 !== $PDOException->getCode()) {
                    $output->writeln('ERROR :' . $PDOException->getMessage());
                }

                continue;
            }
        }
    }

    protected function getOrganizations(InputInterface $input, $mainDb)
    {
        $sql = $mainDb->prepare(
            'SELECT
                u.group_id AS group_id, 
                u.group_id AS user_id,
                u.database
            FROM su_users u
            INNER JOIN su_users_files f ON u.group_id = f.group_id
            WHERE 
                f.status = :status
                and
                f.shape_type = :shape_type
            GROUP BY 
                u.group_id,u.database
            ORDER BY
                database
            '
        );
        $sql->bindValue(':status', SUCCESSFULLY_TREATED);
        $sql->bindValue(':shape_type', Config::LAYER_TYPE_CSD);
        $sql->execute();

        return $sql->fetchAll(PDO::FETCH_ASSOC);
    }

    private function getMaterializedViews($viewName): array
    {
        $sql = "SELECT REPLACE(matviewname, 'kvs_contracts_update_', '') AS name FROM pg_matviews WHERE matviewname ILIKE :pattern";
        $cmd = $this->currentOrganizationDBInstance->prepare($sql);
        $pattern = $viewName . '%'; // Modify pattern for ILIKE search
        $cmd->bindParam(':pattern', $pattern);
        $cmd->execute();

        return $cmd->fetchAll(PDO::FETCH_COLUMN);
    }
}
