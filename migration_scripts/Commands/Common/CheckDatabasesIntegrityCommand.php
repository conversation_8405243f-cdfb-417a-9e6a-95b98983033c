<?php

namespace TF\Commands\Common;

use Exception;
use PDO;
use Prado;
use <PERSON><PERSON>fony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Commands\ParamHelper;

Prado::using('Plugins.Core.UserDb.*');

/**
 * TS-3720 command run on all databases.
 */
class CheckDatabasesIntegrityCommand extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('tf:check_db_integrity')
            ->setDescription('Check if user database has same structore and  same functions as new_users_db.')
            ->addArgument(
                'user_databases',
                InputArgument::IS_ARRAY | InputArgument::OPTIONAL,
                'Databases to run the script on (separate multiple names with a space)?'
            )
            ->addOption('type', '-t', InputOption::VALUE_OPTIONAL, '1 - check only for missing columns and tables, 2 - check only for missing functions, 3 - check only for missing constraints, empty - check for both')
            ->addOption('system_functions', '-s', InputOption::VALUE_OPTIONAL, 'Check for system functions as well')
            ->addOption('mail_format', '-m', InputOption::VALUE_OPTIONAL, 'Format the output for good looking email', false);
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $templateDatabase = 'new_users_db';
        $user_databases = $input->getArgument('user_databases');

        $checkType = $input->getOption('type');
        $checkSystemFunctions = $input->getOption('system_functions');
        $mailFormat = $input->getOption('mail_format');

        $systemFunctions = ['_st_concavehull'];

        $paramHelper = new ParamHelper();
        $userDbString = $paramHelper->createQueryParamString($user_databases);

        $userDbConditions = 'true';
        if ($user_databases) {
            $userDbConditions = 'u.database IN (' . $userDbString . ')';
        }

        $mainDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = $mainDev->prepare(
            "SELECT u.id AS id, u.database FROM su_users u
            WHERE 
            {$userDbConditions}
            AND u.level = 2
            AND u.database like 'db_bg_%'
            ORDER BY database"
        );

        $sql->execute();
        $results = $sql->fetchAll();
        $countResutls = count($results);

        try {
            $templateStructure = $this->getDatabaseStructure($templateDatabase);
            $templateConstraints = $this->getDatabaseConstraints($templateDatabase);
            $templateDatabaseFunctions = $this->getDatabaseFunctions($templateDatabase);
        } catch (Exception $e) {
            if (false !== strpos($e->getMessage(), 'FATAL:  database "' . $templateDatabase . '" does not exist')) {
                $output->writeln('WARNING: Базата данни ' . $templateDatabase . ' не съществува, продължаваме изпълнението...');
                exit;
            }
        }
        $mappedTemplateStructure = array_map(function ($n) {return ($n['table_name'] . '.' . $n['column_name']);}, $templateStructure);
        foreach ($results as $key => $result) {
            try {
                if (empty($checkType) || 1 == $checkType) {
                    $userDbStructure = $this->getDatabaseStructure($result['database']);
                    $mappedUserDbStructure = array_map(function ($n) {return ($n['table_name'] . '.' . $n['column_name']);}, $userDbStructure);
                    $differencesStructure = array_diff($mappedTemplateStructure, $mappedUserDbStructure);
                }
                if (empty($checkType) || 2 == $checkType) {
                    $userDatabaseFunctions = $this->getDatabaseFunctions($result['database']);
                    $differencesFunctions = array_diff($templateDatabaseFunctions['functions'], $userDatabaseFunctions['functions']);
                }
                if (empty($checkType) || 3 == $checkType) {
                    $userDatabaseConstraints = $this->getDatabaseConstraints($result['database']);
                    $differencesConstraints = array_diff($templateConstraints['constraints'], $userDatabaseConstraints['constraints']);
                }
            } catch (Exception $e) {
                if (false !== strpos($e->getMessage(), 'FATAL:  database "' . $result['database'] . '" does not exist')) {
                    // $output->writeln('WARNING: Базата данни ' . $result['database'] . ' не съществува, продължаваме изпълнението...');
                    continue;
                }

                if (!$mailFormat) {
                    throw $e;
                }
            }

            if ($mailFormat) {
                if (empty($checkType) || 3 == $checkType) {
                    if (!empty($differencesConstraints)) {
                        $output->writeln('Database: ' . $result['database'] . ' (' . $key . '/' . $countResutls . ')<br>');
                        $output->writeln('Missing constraint: <br>');
                        $output->writeln($differencesConstraints);
                    }
                }
                if (empty($checkType) || 1 == $checkType) {
                    if (!empty($differencesStructure)) {
                        $output->writeln('Database: ' . $result['database'] . ' (' . $key . '/' . $countResutls . ')<br>');
                        $output->writeln('Missing columns and/or tabbles: <br>');
                        $output->writeln($differencesStructure);
                    }
                }
                if (empty($checkType) || 2 == $checkType) {
                    if (empty($checkSystemFunctions)) {
                        $differencesFunctions = array_diff($differencesFunctions, $systemFunctions);
                    }
                    if (!empty($differencesFunctions)) {
                        $output->writeln('Database: ' . $result['database'] . ' (' . $key . '/' . $countResutls . ')<br>');
                        $output->writeln('Missing functions: <br>');
                        $output->writeln($differencesFunctions);
                    }
                }
                if (!empty($differencesConstraints) || !empty($differencesStructure) || !empty($differencesFunctions)) {
                    $output->writeln('<br><br>==============================================<br><br>');
                }
            } else {
                if (empty($checkType) || 3 == $checkType) {
                    if (!empty($differencesConstraints)) {
                        $output->info('Database: ' . $result['database'] . ' (' . $key . '/' . $countResutls . ')');
                        $output->warn('Missing constraint: ');
                        $output->writeln($differencesConstraints);
                    }
                }
                if (empty($checkType) || 1 == $checkType) {
                    if (!empty($differencesStructure)) {
                        $output->info('Database: ' . $result['database'] . ' (' . $key . '/' . $countResutls . ')');
                        $output->warn('Missing columns and/or tabbles: ');
                        $output->writeln($differencesStructure);
                    }
                }
                if (empty($checkType) || 2 == $checkType) {
                    if (empty($checkSystemFunctions)) {
                        $differencesFunctions = array_diff($differencesFunctions, $systemFunctions);
                    }
                    if (!empty($differencesFunctions)) {
                        $output->info('Database: ' . $result['database'] . ' (' . $key . '/' . $countResutls . ')');
                        $output->warn('Missing functions: ');
                        $output->writeln($differencesFunctions);
                    }
                }
            }
        }
    }

    private function getDatabaseStructure($userDatabase)
    {
        $userDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDatabase . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = "SELECT c.table_name, column_name 
                FROM information_schema.columns as c
            join information_schema.tables as t on (c.table_name = t.table_name)
            WHERE table_type = 'BASE TABLE' 
            AND t.table_schema NOT IN 
                    ('pg_catalog', 'information_schema')
            ORDER BY c.table_name; ";

        $results = $userDev->prepare($sql);
        $results->execute();

        return $results->fetchAll();
    }

    private function getDatabaseConstraints($userDatabase)
    {
        $userDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDatabase . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = "SELECT
                    kcu.table_schema,
                    kcu.table_name,
                    concat(kcu.table_name, '.', kcu.column_name, '.', kcu.table_name, '.', regexp_replace(kcu.constraint_name, '[0-9]+$', '')) as constraint_name,
                    kcu.column_name,
                    tco.constraint_type
                FROM information_schema.table_constraints tco
                JOIN information_schema.key_column_usage kcu  ON kcu.constraint_name = tco.constraint_name AND kcu.constraint_schema = tco.constraint_schema
                WHERE 
                    kcu.table_schema = 'public'
                    and kcu.table_name like 'su_%'
                    and tco.constraint_type = 'PRIMARY KEY'
                    and kcu.column_name = 'id'
                ORDER BY 
                    kcu.table_schema,
                    kcu.table_name,
                    kcu.ordinal_position; ";

        $results = $userDev->prepare($sql);
        $results->execute();

        $constraintsInfo = $results->fetchAll(PDO::FETCH_ASSOC);

        return [
            'constraints' => array_column($constraintsInfo, 'constraint_name'),
            'details' => $constraintsInfo,
        ];
    }

    private function getDatabaseFunctions($userDatabase)
    {
        $userDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDatabase . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = "SELECT 
                p.proname AS function_name,
                pg_catalog.pg_get_function_result(p.oid) AS return_type,
                pg_catalog.pg_get_function_arguments(p.oid) AS arguments,
                CASE
                    WHEN p.prokind = 'a' THEN 'aggregate'
                    WHEN p.prokind = 'w' THEN 'window'
                    WHEN p.prorettype = 'pg_catalog.trigger'::pg_catalog.regtype THEN 'trigger'
                    ELSE 'normal'
                END AS type
                FROM pg_catalog.pg_proc p
                    LEFT JOIN pg_catalog.pg_namespace n ON n.oid = p.pronamespace
                WHERE 
                    n.nspname NOT IN ('pg_catalog', 'information_schema')
                    AND pg_catalog.pg_function_is_visible(p.oid); ";

        $results = $userDev->prepare($sql);
        $results->execute();

        $functionsInfo = $results->fetchAll(PDO::FETCH_ASSOC);

        return [
            'functions' => array_column($functionsInfo, 'function_name'),
            'details' => $functionsInfo,
        ];
    }
}
