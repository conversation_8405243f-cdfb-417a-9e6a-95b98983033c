<?php

namespace TF\Commands\Common;

use PDO;
use Symfony\Component\Console\Input\InputArgument;
use TF\Application\Common\Config;
use TF\Application\Entity\User;
use TF\Application\Entity\UserLayers;

/**
 * This command will get the existing layer definitions and append to them the missing definitions
 * for which there are existing columns in the physical table. If there are missing default definitions, they will be appended as well.
 *
 * NOTE:
 * This command does not work for remote layers
 * The command will update the definitions column in su_users_layers table only if there are missing definitions.
 */
class CreateMissingLayerDefinitionsByTableColumnsCommand extends MainDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $userDatabase = $input->getArgument('userDbName');
        $layerId = $input->getArgument('layerId');

        $excludedTables = array_merge(
            array_values($GLOBALS['Layers']['remoteTables']),
            [
                'layer_dss',
                'layer_satellite_work',
            ]
        );
        $excludedTablesStr = implode('|', $excludedTables);
        $layerColumnCategoryGid = Config::LAYER_COLUMN_CATEGORY_GID;
        $layerColumnCategoryName = Config::LAYER_COLUMN_CATEGORY_NAME;
        $layerColumnCategoryGeom = Config::LAYER_COLUMN_CATEGORY_GEOM;
        $layerColumnCategorySlope = Config::LAYER_COLUMN_CATEGORY_SLOPE;
        $layerColumnCategoryNumber = Config::LAYER_COLUMN_CATEGORY_NUMBER;
        $layerColumnCategoryBoolean = Config::LAYER_COLUMN_CATEGORY_BOOLEAN;
        $layerColumnCategoryDate = Config::LAYER_COLUMN_CATEGORY_DATE;
        $layerColumnCategoryText = Config::LAYER_COLUMN_CATEGORY_TEXT;
        $layerColumnCategoryLabel = Config::LAYER_COLUMN_CATEGORY_LABEL;
        $layerColumnCategoryColor = Config::LAYER_COLUMN_CATEGORY_COLOR;
        $multipleSelectionType = Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE;
        $workLayerType = Config::LAYER_TYPE_WORK_LAYER;

        /**
         * @var User[] $users
         */
        $users = isset($userDatabase)
            ? User::finder()->findAll('level = :level AND "database" = :database', [
                ':level' => 2,
                ':database' => $userDatabase,
            ])
            : User::finder()->findAll('level = :level', [
                ':level' => 2,
            ]);

        foreach ($users as $user) {
            /**
             * @var UserLayers[] $layers
             */
            $layers = isset($layerId)
                ? UserLayers::finder()->findAll("group_id = :group_id AND table_name !~* '({$excludedTablesStr})' AND id = :layer_id", [
                    ':group_id' => $user->group_id,
                    ':layer_id' => $layerId,
                ])
                : UserLayers::finder()->findAll("group_id = :group_id AND table_name !~* '({$excludedTablesStr})'", [
                    ':group_id' => $user->group_id,
                ]);

            $output->warn('-----------------------------------------------');

            if (0 === count($layers)) {
                $output->writeln("No layers found for user '{$user->username}' (database: {$user->database})");

                continue;
            }

            $output->writeln('Found ' . count($layers) . " layers for user '{$user->username}' (database: {$user->database})");

            foreach ($layers as $layer) {
                $output->writeln('===============================================');
                $output->writeln("Checking definitions of layer {$layer->table_name} ...");
                $defaultDefinitions = UserLayers::getDefinitionsByType($layer->layer_type);
                $defaultDefinitionsJson = json_encode($defaultDefinitions);

                $sql = "WITH 
                    user_layers AS (
                        SELECT 
                            sul.id AS layer_id,
                            sul.user_id,
                            sul.table_name,
                            su.\"database\",
                            sul.layer_type,
                            def.col_name,
                            def.col_title,
                            def.col_visible,
                            def.col_personalizable,
                            def.col_category,
                            def.col_multiedit,
                            def.col_singleedit,
                            def.col_sortable,
                            def.col_exportable,
                            def.col_copyable,
                            def.col_virtual,
                            def.col_expression,
                            def.col_filter_selection_type,
                            def.is_default
                        FROM
                            su_users_layers AS sul
                        JOIN su_users AS su
                            ON su.id = sul.user_id
                        JOIN pg_catalog.pg_database AS pgdb
                            ON pgdb.datname = su.\"database\"
                        LEFT JOIN LATERAL (
                            -- merge the default definitions with the existing definitions (the existing overwrites the default)
                            SELECT DISTINCT ON (merged_def.col_name)
                            * 
                            FROM (
                                -- get the default definitioins along with the existing definitions
                                SELECT false AS is_default, * 
                                FROM 
                                    JSONB_TO_RECORDSET(sul.definitions) AS existing_def (
                                    col_name varchar,
                                    col_title varchar,
                                    col_visible boolean,
                                    col_personalizable boolean,
                                    col_category varchar,
                                    col_multiedit boolean,
                                    col_singleedit boolean,
                                    col_sortable boolean,
                                    col_exportable boolean,
                                    col_copyable boolean,
                                    col_virtual boolean,
                                    col_expression varchar,
                                    col_filter_selection_type varchar
                                )
                                UNION ALL
                                SELECT true AS is_default, *
                                FROM JSONB_TO_RECORDSET('{$defaultDefinitionsJson}'::JSONB) AS default_def (
                                    col_name varchar,
                                    col_title varchar,
                                    col_visible boolean,
                                    col_personalizable boolean,
                                    col_category varchar,
                                    col_multiedit boolean,
                                    col_singleedit boolean,
                                    col_sortable boolean,
                                    col_exportable boolean,
                                    col_copyable boolean,
                                    col_virtual boolean,
                                    col_expression varchar,
                                    col_filter_selection_type varchar
                                )
                                ORDER BY is_default ASC
                            ) AS merged_def
                        ) AS def ON TRUE
                        WHERE 
                            sul.id = :layer_id             
                    ),
                    layer_table_columns AS (
                        SELECT DISTINCT 
                            layer_table.table_name,
                            layer_table.col_name,
                            layer_table.col_type,
                            layer_table.col_category,
                            layer_table.col_virtual,
                            ul.\"database\",
                            ul.layer_id
                        FROM
                            user_layers AS ul
                        JOIN dblink(
                                'host=" . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . " dbname=' || ul.\"database\",
                                $$
                                    SELECT 
                                        info_sch.table_name,
                                        info_sch.column_name AS col_name,
                                        info_sch.udt_name AS col_type,
                                        CASE 
                                            WHEN info_sch.column_name = 'slope' 
                                                THEN '{$layerColumnCategorySlope}'
                                            WHEN info_sch.udt_name = 'varchar' AND info_sch.column_name = 'name'
                                                THEN '{$layerColumnCategoryName}'
                                            WHEN info_sch.udt_name = 'varchar' AND (info_sch.column_name = 'fill_color' OR info_sch.column_name = 'border_color')
                                                THEN '{$layerColumnCategoryColor}'
                                            WHEN info_sch.udt_name = 'varchar' AND info_sch.column_name = 'label'
                                                THEN '{$layerColumnCategoryLabel}'
                                            WHEN info_sch.udt_name = 'numeric' OR info_sch.udt_name ilike 'int%' OR info_sch.udt_name ilike 'float%'
                                                THEN 
                                                    CASE WHEN info_constr.constraint_type = 'PRIMARY_KEY'
                                                        THEN '{$layerColumnCategoryGid}'
                                                        ELSE '{$layerColumnCategoryNumber}'
                                                    END
                                            WHEN info_sch.udt_name = 'geometry'
                                                THEN '{$layerColumnCategoryGeom}'
                                            WHEN info_sch.udt_name = 'bool'
                                                THEN '{$layerColumnCategoryBoolean}'
                                            WHEN info_sch.udt_name = 'timestamp'
                                                THEN '{$layerColumnCategoryDate}'
                                            ELSE '{$layerColumnCategoryText}'
                                        END AS col_category,
                                        CASE
                                            WHEN info_sch.is_generated = 'ALWAYS'
                                                THEN TRUE
                                                ELSE FALSE
                                        END as col_virtual
                                    FROM 
                                        information_schema.COLUMNS AS info_sch
                                    JOIN information_schema.table_constraints AS info_constr
                                    ON info_constr.table_name = info_sch.table_name
                                    WHERE 
                                        info_sch.table_name='$$ || ul.table_name || $$'
                                $$
                            ) AS layer_table(table_name varchar, col_name varchar, col_type varchar, col_category varchar, col_virtual bool)
                                ON ul.table_name = layer_table.table_name
                    ),
                    layer_definitions AS  (
                        SELECT
                            COALESCE(ul.layer_id, ltc.layer_id) AS user_layer_id,
                            jsonb_agg(ltc.col_name) FILTER (WHERE ul.col_name ISNULL) AS columns_without_definition,
                            jsonb_agg(ul.col_name) FILTER (WHERE ltc.col_name ISNULL) AS definitions_without_column,
                            jsonb_agg(
                                    -- generate definition using default/existing definition (ul table) or physical table columns (ltc table)
                                    jsonb_build_object(
                                            'col_name', COALESCE(ul.col_name, ltc.col_name),
                                            'col_title', COALESCE(ul.col_title, initcap(replace(ltc.col_name, '_', ' '))),
                                            'col_visible', CASE WHEN ul.layer_type = '{$workLayerType}' THEN COALESCE(ul.col_visible, TRUE) ELSE FALSE END,
                                            'col_personalizable', COALESCE(ul.col_personalizable, TRUE),
                                            'col_category', COALESCE(CASE WHEN ul.is_default THEN ul.col_category ELSE ltc.col_category END, 'text'),
                                            'col_multiedit', COALESCE(ul.col_multiedit, TRUE),
                                            'col_singleedit', COALESCE(ul.col_singleedit, TRUE),
                                            'col_sortable', COALESCE(ul.col_sortable, TRUE),
                                            'col_exportable', COALESCE(ul.col_exportable, TRUE),
                                            'col_copyable', COALESCE(ul.col_copyable, TRUE),
                                            'col_virtual', COALESCE(ul.col_virtual, ltc.col_virtual, FALSE),
                                            'col_expression', COALESCE(ul.col_expression, NULL),
                                            'col_filter_selection_type', COALESCE(ul.col_filter_selection_type, '{$multipleSelectionType}')
                                    )
                            ) AS new_definitions
                        FROM
                            user_layers AS ul
                        FULL JOIN layer_table_columns AS ltc
                            ON ltc.table_name = ul.table_name
                            AND ltc.layer_id = ul.layer_id
                            AND ltc.col_name = ul.col_name
                        GROUP BY 
                            COALESCE(ul.layer_id, ltc.layer_id)
                    )
                    SELECT * FROM layer_definitions
                ";

                $cmd = $pdo->prepare($sql);
                $cmd->execute([
                    ':layer_id' => $layer->id,
                ]);
                $result = $cmd->fetch(PDO::FETCH_ASSOC);
                $columnsWithoutDefinition = json_decode($result['columns_without_definition'], true);
                $definitionsWithoutColumn = json_decode($result['definitions_without_column'], true);

                if (0 === count($columnsWithoutDefinition) && 0 === count($definitionsWithoutColumn)) {
                    $output->info('No missing definitions!');
                    $output->writeln('===============================================');

                    continue;
                }

                if (0 < count($columnsWithoutDefinition)) {
                    $output->writeln("Missing definitions for columns:\n" . implode(', ', $columnsWithoutDefinition));
                }

                if (0 < count($definitionsWithoutColumn)) {
                    $output->warn("The following columns have definitions but don't exist in the physical table:\n" . implode(', ', $definitionsWithoutColumn));
                }

                $output->writeln('Updating...');
                $layer->definitions = $result['new_definitions'];
                $layer->save();
                $output->info('Done!');
                $output->writeln('===============================================');
            }
        }
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('tf:add_missing_layer_definitions_by_table_columns')
            ->setDescription('Create missing layer definitions using the columns in physical tables')
            ->addArgument('userDbName', InputArgument::OPTIONAL, 'The user database name')
            ->addArgument('layerId', InputArgument::OPTIONAL, 'The id of the layer for which to create the missing definitions');
    }
}
