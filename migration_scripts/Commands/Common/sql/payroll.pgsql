-- DROP SCHEMA IF EXISTS tf CASCADE;
-- CREATE SCHEMA tf;

CREATE OR REPLACE FUNCTION "public"."tf_natu_substract"("left" json, "right" json, "min_zero" bool=false)
  RETURNS "pg_catalog"."json" AS $BODY$

SELECT
	json_agg (row_to_json(r.*))
FROM
	(
		SELECT
			l. ID,
			l. NAME,
			(case $3
				when true then
				 GREATEST(COALESCE((l. VALUE - r. VALUE), l.value), 0)
				else
					COALESCE((l. VALUE - r. VALUE), l.value)
			end) "value",

			(case $3
				when true then
				 GREATEST(COALESCE((l.price - r.price), l.price), 0)
				else
					COALESCE((l.price - r.price), l.price)
			end) "price"

		FROM
			json_to_recordset ($1) l (
				"id" INT,
				"name" VARCHAR,
				"value" DECIMAL,
				price DECIMAL
			)
		LEFT JOIN json_to_recordset ($2) r (
			"id" INT,
			"name" VARCHAR,
			"value" DECIMAL,
			price DECIMAL
		) ON l. ID = r. ID
	) r

$BODY$
  LANGUAGE 'sql' VOLATILE COST 100
;

CREATE OR REPLACE FUNCTION tf_json_flat_arr("input" json) RETURNS "pg_catalog"."json" AS $BODY$
		WITH json_array_expanded AS (
			SELECT
				(
					CASE json_typeof (json)
					WHEN 'string' THEN
						json ->> 0
					WHEN 'array' THEN
						json_array_elements_text (json)
					END
				) :: json json_data
			FROM
				json_array_elements (
					input
				) AS json
		) SELECT
			json_agg(json_data)
		FROM
			json_array_expanded
	$BODY$ LANGUAGE 'sql' VOLATILE COST 100 ;

CREATE OR REPLACE FUNCTION tf_nat_distinct("input" json) RETURNS "pg_catalog"."json" AS $BODY$
(with distinct_data as 	(SELECT DISTINCT
		ON (j."id", j."value") j."id",
		j."name",
		j."value"
	FROM
		json_to_recordset ($1) AS j (
			"id" INT,
			"name" VARCHAR,
			"value" DECIMAL
		))
SELECT json_agg(row_to_json(distinct_data.*)) from distinct_data
);
$BODY$ LANGUAGE 'sql' VOLATILE COST 100 ;

CREATE OR REPLACE FUNCTION "public"."tf_json_reduce"("p1" json)
  RETURNS "pg_catalog"."json" AS $BODY$
    WITH renta_grouped AS (
	SELECT
		ID,
		NAME,
		SUM (VALUE) AS
	VALUE
	FROM
		json_to_recordset (COALESCE(p1, '[]'::json)) AS T (
			"name" VARCHAR,
			"value" NUMERIC,
			"id" int4
		)
	where id NOTNULL
	GROUP BY
		ID,
		NAME
) SELECT
	json_agg (
		json_build_object (
			'id',
			ID,
			'name',
			NAME,
			'value',

		VALUE

		)
	)
FROM
	renta_grouped
    $BODY$
  LANGUAGE 'sql'
;

CREATE OR REPLACE FUNCTION "public"."tf_reduce_stf"("agg_state" json, "el" json)
  RETURNS "pg_catalog"."json" AS $BODY$
DECLARE agg_res json ;
BEGIN
IF el ISNULL AND agg_state ISNULL THEN
	agg_res := '[]' :: json ;
ELSEIF agg_state ISNULL THEN
	agg_res := el ;
ELSIF el ISNULL THEN
	agg_res := agg_state ;
ELSE
	agg_res := tf_json_concat(agg_state, el);
END
IF ; RETURN agg_res ;
END ;  $BODY$
  LANGUAGE 'plpgsql'
;

CREATE OR REPLACE FUNCTION tf_reduce_final (agg_state json)
RETURNS json IMMUTABLE LANGUAGE plpgsql AS $$
BEGIN
	RETURN tf_json_reduce( agg_state);
END ; $$;

CREATE OR REPLACE FUNCTION "public"."tf_json_concat"("json1" json, "json2" json)
  RETURNS "pg_catalog"."json" AS $BODY$
    SELECT
        array_to_json (ARRAY_AGG(x)) json
    FROM
        (
            SELECT
                json_array_elements ($1)
            UNION ALL
                SELECT
                    json_array_elements ($2)
        ) AS T (x)
    $BODY$
  LANGUAGE 'sql';

create aggregate natura_agg (json)
(
    sfunc = tf_reduce_stf,
    stype = json,
    finalfunc = tf_reduce_final
);

CREATE OR REPLACE FUNCTION "public"."tf_payroll2"(IN "$start_date" date, IN "$due_date" date, IN "$farming_year" _int4, IN "$farm_id" _int4=NULL::integer[], IN "$ekatte" varchar= NULL::character varying, IN "$owner_is_dead" boolean = false, IN "$owner_name" varchar= NULL::character varying, IN "$owner_egn" varchar= NULL::character varying, IN "$owner_eik" varchar= NULL::character varying)
  RETURNS TABLE("uid" text, "farming_id" int4, "owner_id" int4, "land" varchar, "parent" "public"."ltree", "level" int4, "is_dead" bool, "owner_names" varchar, "egn_eik" text, "iconcls" text, "rep_names" text, "rep_egn" varchar, "paid_leva_with_natura" numeric, "paid_leva_with_natura_detailed" json, "area" numeric, "rent_in_leva" numeric, "charged_renta_leva" numeric, "paid_total_leva" numeric, "unpaid_leva" numeric, "rent_natura" json, "charged_renta_nat" json, "final_rent_natura" json, "unpaid_natura" json, "paid_natura_with_leva_detailed" json, "paid_total_natura" json, "paid_total_virtual_natura" json)
  AS $BODY$BEGIN RETURN QUERY (
    WITH full_contract_data AS (
	SELECT
		C .farming_id,
		o. ID AS owner_id,
		o.is_dead AS is_dead,
		COALESCE (
			subpath (po. PATH, 0, - 1),
			o. ID :: TEXT :: ltree
		) parent,
        COALESCE(po.path, o.id :: TEXT :: ltree) po_path,
		owner_type,
		(
			CASE
			WHEN owner_type = 1 THEN
				concat_ws (
					' ',
					o. NAME,
					o.surname,
					o.lastname
				)
			ELSE
				o.company_name
			END
		) AS owner_names,
		o.rent_place,
		COALESCE (
			NULLIF (o.egn, ''),
			NULLIF (o.eik, '')
		) egn_eik,
		concat_ws (
			' ',
			reps.rep_name,
			reps.rep_surname,
			reps.rep_lastname
		) AS rep_names,
		reps.rep_egn,
		pc.plot_id,
		-- 			string_agg (DISTINCT pc.plot_id :: TEXT, ',') AS plot_id,
		kvs.kad_ident,
		SUM (COALESCE(pu.area, 0)) AS personal_area,
		C . ID AS contract_id,
		COALESCE (A .c_num, C .c_num) AS c_num,
		(
			SUM (
				DISTINCT (
					po.percent * (pc.area_for_rent) / 100
				) :: DECIMAL
			) - SUM (
				DISTINCT COALESCE (pu.area, 0) :: DECIMAL
			)
		) :: DECIMAL AS area,
		SUM (
			DISTINCT (
			(
                    (case
							when pc.rent_per_plot notnull then pc.rent_per_plot * ( ( po.percent * pc.area_for_rent / 100 ) )
							when cr.renta notnull THEN 0
							when A .renta notnull then A .renta * ( ( po.percent * pc.area_for_rent / 100 ) )
							when C .renta notnull then C .renta * ( ( po.percent * pc.area_for_rent / 100 ) )
					end) - COALESCE (pu.area, 0)
				)
			) :: DECIMAL
		) AS rent_in_leva,
		SUM (
			DISTINCT (
				cr.renta * (
					(
						po.percent * pc.area_for_rent / 100
					)
				) - COALESCE (pu.area, 0)
			) :: DECIMAL
		) filter (WHERE pc.rent_per_plot isnull)
		charged_renta_leva,
		tf_json_reduce (
			json_agg (
				json_build_object (
					'name',
					rt. NAME,
					'value',
					(
						ntr.renta_value * (
							(
								po.percent * pc.area_for_rent / 100
							) - COALESCE (pu.area, 0)
						)
					)::NUMERIC(10, 3),
					'id',
					rt. ID,
					'price',
					0
				)
			) FILTER (where crn.amount ISNULL)
		) AS rent_natura,
		tf_json_reduce (
			json_agg (
				json_build_object (
					'name',
					rt. NAME,
					'value',
					(
						CASE
						WHEN (crn.nat_is_converted) THEN
							NULL
						ELSE
							(
								crn.amount * (
									(
										pc.area_for_rent * (po.percent /(100) :: DECIMAL)
									) - COALESCE (pu.area,(0) :: DECIMAL)
								)
							)
						END
					)::NUMERIC(10, 3),
					'id',
					rt. ID,
					'price',
					0
				)
			) filter (where crn.amount NOTNULL)
		) AS charged_renta_nat,
		tf_json_reduce (
			json_agg (
				json_build_object (
					'name',
					rt. NAME,
					'value',
					(
						CASE
						WHEN (crn.nat_is_converted) THEN
							(
								(
									crn.amount * crn.nat_unit_price
								) * (
									(
										pc.area_for_rent * (po.percent /(100) :: DECIMAL)
									) - COALESCE (pu.area,(0) :: DECIMAL)
								)
							)
						ELSE
							NULL
						END
					)::NUMERIC(10, 3),
					'id',
					rt. ID,
					'price',
					0
				)
			) FILTER (WHERE crn.nat_is_converted)
		) AS converted_charged_renta_nat,
		tf_json_reduce (
			json_agg (
				json_build_object (
					'name',
					rt. NAME,
					'value',
					(
						COALESCE (
							-- 								SUM (
							CASE
							WHEN (crn.nat_is_converted) THEN
								NULL
							ELSE
								(
									crn.amount * (
										(
											pc.area_for_rent * (po.percent /(100) :: DECIMAL)
										) - COALESCE (pu.area,(0) :: DECIMAL)
									)
								)
							END -- 								)
							,
							-- 								SUM (
							(
								ntr.renta_value * (
									(
										po.percent * pc.area_for_rent / 100
									) - COALESCE (pu.area, 0)
								)
							) -- 								) :: NUMERIC
						)
					)::NUMERIC(10, 3),
					'id',
					rt. ID,
					'price',
					0
				)
			)
		) AS final_rent_natura
	FROM
		su_contracts C
	LEFT JOIN (
		SELECT
			A . ID,
			A .parent_id,
			A .is_annex,
			A .start_date,
			A .due_date,
			A .c_num,
			A .renta,
			A .farming_id
		FROM
			su_contracts A
		WHERE
			A .start_date < "$start_date"
		AND A .due_date >= "$due_date"
		AND A .is_annex = TRUE
		AND A .active = TRUE
	) A ON (A .parent_id = C . ID)
	LEFT JOIN su_contracts_plots_rel pc ON (
		pc.contract_id = COALESCE (A . ID, C . ID)
	)
	LEFT JOIN su_plots_owners_rel po ON (po.pc_rel_id = pc. ID)
	INNER JOIN su_owners o ON o. ID = po.owner_id
	LEFT JOIN su_owners_reps reps ON reps.owner_id = o. ID
	LEFT JOIN su_personal_use pu ON (
		pu.pc_rel_id = pc. ID
		AND pu.owner_id = po.owner_id
		AND pu. YEAR = ALL ("$farming_year")
	)
	LEFT JOIN su_charged_renta cr ON (
		(
			(
				cr.owner_id = po.owner_id
				OR cr.owner_id :: TEXT :: ltree @> po. PATH
			)
			AND cr.plot_id = pc.plot_id
			AND cr.contract_id = C . ID
		)
		AND cr. YEAR = ALL ("$farming_year")
	)
	LEFT JOIN su_contracts_rents ntr ON ntr.contract_id = COALESCE (A . ID, C . ID)
	AND ntr.renta_id NOTNULL
	LEFT JOIN su_charged_renta_natura crn ON (
		crn.renta_id = cr. ID
		AND crn.nat_type = ntr.renta_id
	)
	LEFT JOIN su_renta_types rt ON rt. ID = ntr.renta_id
	INNER JOIN layer_kvs kvs ON kvs.gid = pc.plot_id
	WHERE
		C .nm_usage_rights NOT IN (1, 4)
	AND (
		(
			C .start_date < "$start_date"
			AND C .due_date >= "$due_date"
		)
		OR (
			A .start_date < "$start_date"
			AND A .due_date >= "$due_date"
		)
	)
	AND C .active = 'TRUE'
	AND C .is_annex = 'FALSE'
	AND C .is_sublease = 'FALSE'
	AND pc.annex_action = 'added'

	AND (COALESCE("$owner_name", '') = '' OR COALESCE(NULLIF(TRIM(concat_ws (' ', o. NAME, o.surname, o.lastname)), ''), o.company_name) ILIKE '%' || "$owner_name" || '%')
	AND (COALESCE("$owner_egn", '') = '' OR o.egn ILIKE '%' || "$owner_egn" || '%')
    AND (COALESCE("$owner_eik", '') = '' OR o.eik ILIKE '%' || "$owner_eik" || '%')
	AND (array_length(COALESCE("$farm_id", '{}'::int4[]), 1) ISNULL OR c.farming_id = ALL("$farm_id"))
	AND (COALESCE("$ekatte", '') = '' OR kvs.ekate = "$ekatte")

	AND (
		COALESCE (NULL, '') = ''
		OR COALESCE (
			NULLIF (
				concat_ws (
					' ',
					o. NAME,
					o.surname,
					o.lastname
				),
				''
			),
			o.company_name
		) ILIKE '%' || NULL || '%'
	)
	AND (
		COALESCE (NULL, '') = ''
		OR o.egn ILIKE '%' || NULL || '%'
	)
	AND (
		array_length(
			COALESCE (NULL, '{}' :: int4 []),
			1
		) ISNULL
		OR C .farming_id = ALL ('{null}')
	)
	AND (
		COALESCE (NULL, '') = ''
		OR kvs.ekate = NULL
	) --  	AND o. ID = 2255
	-- and o.egn='7712237656'
	GROUP BY
		pc.plot_id,
		o. ID,
		COALESCE (A . ID, C . ID),
        C . ID,
		COALESCE (A .c_num, C .c_num),
		C .farming_id,
		kvs.kad_ident,
		po. PATH,
		reps.rep_name,
		reps.rep_surname,
		reps.rep_lastname,
		reps.rep_egn,
		o.rent_place
	ORDER BY
		o. ID
),
 full_payments AS (
	SELECT
		C .farming_id,
		P .owner_id,
		C . ID contract_id,
		COALESCE (
			subpath (P . PATH, 0, - 1),
			P .owner_id :: TEXT :: ltree
		) parent,
        COALESCE(p.path, p.owner_id :: TEXT :: ltree) p_path,
		SUM (
			CASE
			WHEN P .paid_from = 1
			AND P .paid_from = P .paid_in THEN
				P .amount
			ELSE
				0
			END
		)::DECIMAL paid_leva_with_leva,
        COALESCE(SUM(SUM (P .amount) FILTER (WHERE P .paid_from = 1 AND P .paid_in = 2)) OVER w, 0)::DECIMAL paid_leva_with_natura,
		(SUM(SUM (
			CASE
			WHEN P .paid_from = 1
			AND P .paid_from = P .paid_in THEN
				P .amount
			ELSE
				0
			END
		) + SUM (
			CASE
			WHEN P .paid_from = 1
			AND P .paid_in = 2
			THEN
				P .amount
			ELSE
				0
			END
		)) OVER w)::DECIMAL AS paid_total_leva,
		SUM(SUM (
			CASE
			WHEN P .paid_from = 1
			AND P .paid_from = P .paid_in THEN
				P .amount
			ELSE
				0
			END
		) + SUM (
			CASE
			WHEN P .paid_from = 2
			AND P .paid_in = 1
			AND pn.amount != 0 THEN
				P .amount
			ELSE
				0
			END
		) + SUM (
			CASE
			WHEN P .paid_from = 1
			AND P .paid_in = 2 THEN
				P .amount
			ELSE
				0
			END
		)) OVER w paid_total_virtual_leva,
		tf_json_reduce (
			json_agg (
				json_build_object (
					'name',
					rt. NAME,
					'value',
					pn.amount::NUMERIC(10, 3),
					'id',
					rt. ID,
					'price',
					P .amount
				)
			) FILTER (

				WHERE
					P .paid_from = 1
				AND P .paid_in = 2
			)
		) paid_leva_with_natura_detailed,
		tf_json_reduce (
			json_agg (
				json_build_object (
					'name',
					rt. NAME,
					'value',
					pn.amount::NUMERIC(10, 3),
					'id',
					rt. ID,
					'price',
					(Pn.amount * pn.unit_value) :: DECIMAL
				)
			) FILTER (

				WHERE
					(
						P .paid_from = 2
						AND P .paid_in = 2
					)
			)
		) paid_natura_with_natura,
		tf_json_reduce (
			tf_json_concat (
				json_agg (
					json_build_object (
						'name',
						rt. NAME,
						'value',
						pn.amount::NUMERIC(10, 3),
						'id',
						rt. ID,
						'price',
						(Pn.amount * pn.unit_value) :: DECIMAL
					)
				) FILTER (

					WHERE
						(
							P .paid_from = 2
							AND P .paid_in = 2
						)
				),
				json_agg (
				json_build_object (
					'name',
					rt. NAME,
					'value',
					pn.amount::NUMERIC(10, 3),
					'id',
					rt. ID,
					'price',
					P .amount
				)
			) FILTER (

				WHERE
					P .paid_from = 1
				AND P .paid_in = 2
			)
			)
		) paid_total_natura,

		natura_agg (tf_json_reduce (
			tf_json_concat (
				json_agg (
					json_build_object (
						'name',
						rt. NAME,
						'value',
						pn.amount::NUMERIC(10, 3),
						'id',
						rt. ID,
						'price',
						(Pn.amount * pn.unit_value) :: DECIMAL
					)
				) FILTER (

					WHERE
						(
							P .paid_from = 2
							AND P .paid_in = 2
						)
				),
				json_agg (
				json_build_object (
					'name',
					rt. NAME,
					'value',
					pn.amount::NUMERIC(10, 3),
					'id',
					rt. ID,
					'price',
					P .amount
				)
			) FILTER (

				WHERE
					P .paid_from = 2
				AND P .paid_in = 1
			)
			)
		)) OVER w paid_total_virtual_natura,
		tf_json_reduce (
			json_agg (
				json_build_object (
					'name',
					rt. NAME,
					'value',
					pn.amount::NUMERIC(10, 3),
					'id',
					rt. ID,
					'price',
					P .amount
				)
			) FILTER (

				WHERE
					P .paid_from = 2
				AND P .paid_in = 1
			)
		) paid_natura_with_leva_detailed
	FROM
		su_payments P
	INNER JOIN su_transactions T ON P .transaction_id = T . ID
	LEFT JOIN su_payments_natura pn ON pn.payment_id = P . ID
	LEFT JOIN su_renta_types rt ON rt. ID = pn.nat_type
	LEFT JOIN su_contracts C ON C . ID = P .contract_id
	WHERE
		T .status = TRUE
	AND P .farming_year = ALL ("$farming_year") --  	AND P .owner_id = 2255
	-- 	and p.egn_eik='7712237656'
	GROUP BY
		C . ID,
		P .owner_id,
		P . PATH,
		C .farming_id
    WINDOW w as (PARTITION by C .farming_id, P .owner_id ORDER BY p.OWNER_id)
	ORDER BY
		P .owner_id ASC
),
payroll as (
SELECT
	fcd.farming_id,
	fcd.owner_id,
-- 		fcd.contract_id,
		fcd.owner_type,
	fcd.owner_names,
	fcd.egn_eik,
	fcd.rent_place,
		fcd.rep_names,
		fcd.rep_egn,
		fcd.is_dead,
		fcd.parent,
		nlevel (fcd.parent) "level",
-- 		fcd.rent_place,
-- 		fcd.kad_ident,
	SUM (fcd.area) area,
	-- 	fcd.personal_area,
	SUM (fcd.rent_in_leva) rent_in_leva,
	SUM (fcd.charged_renta_leva) charged_renta_leva,
	sum(COALESCE(fcd.charged_renta_leva, fcd.rent_in_leva)) final_renta_in_leva,
	natura_agg (fcd.rent_natura) rent_natura,
	natura_agg (fcd.charged_renta_nat) charged_renta_nat,
	natura_agg (
		fcd.converted_charged_renta_nat
	) converted_charged_renta_nat,
	natura_agg (fcd.final_rent_natura) final_rent_natura,
	SUM (
		DISTINCT fp.paid_leva_with_leva
	) paid_leva_with_leva,
	SUM (DISTINCT fp.paid_leva_with_natura) paid_leva_with_natura,
	SUM (DISTINCT fp.paid_total_leva) paid_total_leva,
	SUM (DISTINCT fp.paid_total_virtual_leva) paid_total_virtual_leva,
 	tf_nat_distinct(
		tf_json_flat_arr(
			json_agg(fp.paid_leva_with_natura_detailed) filter (where fp.paid_leva_with_natura_detailed NOTNULL)
		)
 	) paid_leva_with_natura_detailed,
	natura_agg (fp.paid_natura_with_natura) paid_natura_with_natura,

    tf_nat_distinct(
        tf_json_flat_arr(
            json_agg(fp.paid_total_natura) filter (where fp.paid_total_natura NOTNULL)
        )
    ) paid_total_natura,

    tf_json_reduce(
        tf_nat_distinct (
            tf_json_flat_arr(
                json_agg(
                    fp.paid_natura_with_leva_detailed
                ) filter (where fp.paid_natura_with_leva_detailed NOTNULL)
            )
        )
    ) paid_natura_with_leva_detailed,

    tf_json_reduce(
        tf_nat_distinct(
            tf_json_flat_arr(
                json_agg(fp.paid_total_virtual_natura) filter (where fp.paid_total_virtual_natura NOTNULL)
            )
        )
    ) paid_total_virtual_natura,

    tf_natu_substract(
        tf_json_reduce(tf_json_concat(natura_agg (fcd.rent_natura), natura_agg (fcd.charged_renta_nat))),
        tf_json_reduce(tf_nat_distinct(tf_json_flat_arr(json_agg(fp.paid_total_virtual_natura) FILTER (where fp.paid_total_virtual_natura NOTNULL)))),
        true
    )  unpaid_natura,

    greatest((
        COALESCE(
            COALESCE(SUM(fcd.rent_in_leva), 0)
                + COALESCE(SUM(fcd.charged_renta_leva), 0), 0)
        - COALESCE(SUM (DISTINCT fp.paid_total_leva), 0)
        -- - COALESCE(SUM (DISTINCT fp.paid_leva_with_natura), 0)
    ), 0)::NUMERIC(10,2) unpaid_leva

FROM
	full_contract_data fcd
LEFT JOIN full_payments fp ON (
	fcd.owner_id = fp.owner_id
	AND fcd.contract_id = fp.contract_id
	AND fcd.farming_id = fp.farming_id
    AND fcd.po_path = fp.p_path
)
WHERE
	fcd.is_dead = "$owner_is_dead"
GROUP BY
	fcd.farming_id,
	fcd.owner_id,
	fcd.owner_names,
	fcd.egn_eik,
	fcd.owner_type,
	fcd.is_dead,
	fcd.parent,
	fcd.rent_place,
		fcd.rep_names,
		fcd.rep_egn
ORDER BY
	fcd.owner_names
)

SELECT
concat_ws (
		'.',
		p.farming_id,
		p.parent,
		p.owner_id
	) uid,
p.farming_id,
p.owner_id,
p.rent_place,
p.parent,
p."level",
p.is_dead,
p.owner_names,
p.egn_eik,

	(CASE
	WHEN p.is_dead THEN
		'icon-tree-user-rip'
	WHEN p.owner_type = 1 THEN
		'icon-tree-user'
	WHEN p.owner_type = 0 THEN
		'icon-tree-users'
	END) iconCls,
p.rep_names,
p.rep_egn,
p.paid_leva_with_natura,
p.paid_leva_with_natura_detailed,
p.area,
p.rent_in_leva,
p.charged_renta_leva,
p.paid_total_leva,
p.unpaid_leva,
p.rent_natura,
p.charged_renta_nat,
p.final_rent_natura,
p.unpaid_natura,
p.paid_natura_with_leva_detailed,
p.paid_total_natura,
p.paid_total_virtual_natura

from payroll p
) ;
END $BODY$
  LANGUAGE 'plpgsql' VOLATILE COST 100
 ROWS 1000
;

CREATE OR REPLACE FUNCTION "public"."tf_payroll_tree"("parent" text, "start_date" date, "due_date" date, "farming_year" int4[])
  RETURNS "pg_catalog"."json" AS $BODY$    var rows = plv8.execute('SELECT * FROM tf_payroll($2, $3, $4) WHERE parent <@ $1 ORDER BY nlevel(parent),level', [parent, start_date, due_date, farming_year]);
    var all = {},
        out = [],
        top,r,i;
    for(i=0; i<rows.length; i++){
        r = rows[i];
        r.children = [];
        all[r.owner_id] = r;
        if(r.parent == parent){
            out.push(r);
        }
    }
    for(i=0; i<rows.length; i++){
        r = rows[i];
        if(all[ r.parent_id ]){
            all[ r.parent_id ].children.push(r);
        }
    }
    return JSON.stringify(out,null,4);
$BODY$
  LANGUAGE 'plv8' VOLATILE COST 100
;

CREATE OR REPLACE FUNCTION "public"."tf_smart_oper"("param1" float4, "param2" float4, "operator" VARCHAR(1))
  RETURNS "pg_catalog"."float4" AS $BODY$
DECLARE
    "result" float4;
BEGIN
	IF param1 NOTNULL AND param2 NOTNULL THEN
		EXECUTE concat_ws(' ', 'SELECT', param1, "operator", param2) INTO "result";
	ELSIF param1 ISNULL THEN
		"result" := param2;
	ELSIF param2 ISNULL THEN
		"result" := param1;
	ELSE
		"result" := null;
	END IF;

	RETURN "result";
END
$BODY$
  LANGUAGE 'plpgsql' VOLATILE COST 100
;

CREATE OR REPLACE FUNCTION public.tf_total_nat_val(natura json)
    RETURNS numeric
    LANGUAGE sql
    AS $BODY$
        SELECT sum((x->>'value')::NUMERIC) from json_array_elements($1) x
    $BODY$
;
