<?php

namespace TF\Commands\Common;

use Exception;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class CreateDatabasesCommand extends BaseCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('tf:create_databases')
            ->setDescription('Creates databases needed for an independent run of technofarm docker with a single admin user record');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $user = getenv('POSTGRES_USER', true) ?: getenv('POSTGRES_USER');
        $dumpPath = 'db_dump/';

        try {
            //            system('psql -h ************** -p 25432 -U postgres -d postgres');
            $command = '"ALTER USER ' . DEFAULT_DB_USERNAME . " password '" . DEFAULT_DB_PASSWORD . "';\"";
            system('psql -v ON_ERROR_STOP=1 -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U ' . $user . ' -d postgres -c ' . $command . '', $return);
            system('psql -v ON_ERROR_STOP=1 -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U ' . DEFAULT_DB_USERNAME . ' -d postgres -f ' . $dumpPath . 'db_admin', $return);
            system('psql -v ON_ERROR_STOP=1 -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U ' . DEFAULT_DB_USERNAME . ' -d postgres -f ' . $dumpPath . 'db_maria', $return);
            system('psql -v ON_ERROR_STOP=1 -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U ' . DEFAULT_DB_USERNAME . ' -d postgres -f ' . $dumpPath . 'susi_main', $return);
            system('psql -v ON_ERROR_STOP=1 -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U ' . DEFAULT_DB_USERNAME . ' -d postgres -f ' . $dumpPath . 'new_users_db', $return);
        } catch (Exception $e) {
            echo $e;
        }
    }
}
