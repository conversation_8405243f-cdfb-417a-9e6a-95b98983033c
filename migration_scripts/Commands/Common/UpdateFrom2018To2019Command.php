<?php

namespace TF\Commands\Common;

/**
 * UpdateFrom2018To2019 command run on all databases.
 */
class UpdateFrom2018To2019Command extends UserDbCommand
{
    protected $skipLogging = true;

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('tf:update-from-2018-to-2019')
            ->setDescription('Update na user database ot 2018 do 2019');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $commandsArr = [
            new \TF\Commands\Common\UpdateUserTo2019SupportCommand(),
            new \TF\Commands\v2_2\UpdateToVersion2_2(),
            new \TF\Commands\v3\UpdateToVersion3(),
        ];

        foreach ($commandsArr as $command) {
            $output->writeln('Executing command: ' . $command->getName());
            $this->logScript($userDb, $command->getName());
            $command->onDbExecute($userDb, $output, $input);
        }
    }

    protected function logScript($user_db, $name)
    {
        $date = date('Y-m-d H:i:s');
        $this->openConnection();
        $sql = $this->mainConnection->prepare("
            INSERT INTO scripts_log (db_name, name, date)
            VALUES ('{$user_db}', '{$name}', '{$date}')
        ");

        return $sql->execute();
    }
}
