<?php

namespace TF\Commands\Common\Custom;

use Symfony\Component\Console\Formatter\OutputFormatter;
use Symfony\Component\Console\Formatter\OutputFormatterStyle;
use Symfony\Component\Console\Output\ConsoleOutput;

class TFConsoleOutput extends ConsoleOutput
{
    public function __construct()
    {
        $formatter = new OutputFormatter();
        $formatter->setStyle('tf_warn', new OutputFormatterStyle('black', 'yellow'));
        $formatter->setStyle('tf_info', new OutputFormatterStyle('black', 'green'));

        parent::__construct(self::VERBOSITY_NORMAL, null, $formatter);
    }

    public function warn($message)
    {
        $this->writeln('<tf_warn>' . $message . '</tf_warn>');
    }

    public function info($message)
    {
        $this->writeln('<tf_info>' . $message . '</tf_info>');
    }

    public function error($message)
    {
        $this->writeln('<error>' . $message . '</error>');
    }
}
