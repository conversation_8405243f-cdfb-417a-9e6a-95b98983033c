<?php

namespace TF\Commands\Common;

use PDO;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Application\Entity\LayerStyles;
use TF\Application\Entity\User;
use TF\Commands\ParamHelper;
use TF\Engine\Plugins\Core\Layers\LayersController;

/**
 * TS-515 command run on all databases.
 */
class AddMissingLayersCommand extends BaseCommand
{
    public const LAYER_TYPE_ZP = 1;
    public const LAYER_TYPE_KMS = 4;
    public const LAYER_TYPE_ISAK = 6;
    public const LAYER_TYPE_FOR_ISAK = 9;
    public const DEFAULT_MAX_EXTENT = '125190.6162 4573142.7188 631370.3273 4887149.5823';
    protected $LayersController = false;
    protected $output = false;

    protected function configure()
    {
        $this
            ->setName('tf:add_missing_layers')
            ->setDescription('Dobavq lipsvashtite sloeve kam godina.')
            ->addArgument(
                'user_databases',
                InputArgument::IS_ARRAY | InputArgument::OPTIONAL,
                'Databases to run the script on (separate multiple names with a space)?'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $user_databases = $input->getArgument('user_databases');
        $paramHelper = new ParamHelper();
        $this->output = $output;
        $userDbString = $paramHelper->createQueryParamString($user_databases);

        $this->LayersController = new LayersController('Layers');

        $userDbConditions = 'true';
        if ($user_databases) {
            $userDbConditions = 'u.database IN (' . $userDbString . ')';
        }

        $dbhDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = $dbhDev->prepare("
            SELECT
                DISTINCT(group_id), 
                datname AS database
            FROM
                pg_database d
            INNER JOIN su_users u ON d.datname = u.database
            WHERE
                {$userDbConditions}
            AND datistemplate = FALSE
            AND u. LEVEL = 2
        ");

        $sql->execute();
        $results = $sql->fetchAll();

        $layer_types_array = [
            self::LAYER_TYPE_ZP,
            self::LAYER_TYPE_KMS,
            self::LAYER_TYPE_ISAK,
            self::LAYER_TYPE_FOR_ISAK,
        ];

        // Минава се през всички потребители, за които да се добавят слоеве
        foreach ($results as $user) {
            // Вземат се всички стопанства за текущия потребител
            $farmingSql = $dbhDev->prepare("
                SELECT id FROM su_users_farming 
                WHERE group_id = {$user['group_id']}
                ORDER BY id ASC
            ");

            $farmingSql->execute();
            $farmings = $farmingSql->fetchAll();

            // Минава се през всички стопанства на текущия потребител
            foreach ($farmings as $farming) {
                // Минава се през всяка една стопанска година, добавена в конфига
                foreach ($GLOBALS['Farming']['years'] as $year) {
                    $currentYear = $year['id'];

                    // Минава се през всеки един тип слой, за които да се направи проверката
                    foreach ($layer_types_array as $type) {
                        // Взема се информация за текущия потребител, текущото стопанство, текущата година и текущия тип слой
                        $layerSql = $dbhDev->prepare("
                            SELECT * FROM su_users_layers 
                            WHERE 
                                group_id = {$user['group_id']} 
                                AND layer_type = {$type} 
                                AND year = {$currentYear}
                                AND farming = {$farming['id']}
                            ORDER BY farming ASC, 
                            year ASC, 
                            layer_type ASC
                        ");

                        $layerSql->execute();
                        $currentLayer = $layerSql->fetchAll();

                        /*
                         * В случай, че няма информация за текущия потребител, текущото стопанство, текущата година и текущия тип слой
                         * се добавя нов слой със съответните данни
                         */
                        if (!count($currentLayer)) {
                            $this->addNewLayerItem($user['group_id'], $farming['id'], $currentYear, $type);
                        }
                    } // Край на foreach за типове слой
                } // Край на foreach за стопански години
            } // Край на foreach за стопанства

            /**
             * @var User
             */
            $organization = User::finder()->find('group_id = :group_id', [':group_id' => $user['group_id']]);
            [$subUser] = $organization->getSubUsers();
            LayerStyles::addUserLayersStyles($organization->id, $subUser->getId());
            
        } // Край на foreach за потребители
    }

    protected function addNewLayerItem($group_id, $farming_id, $currentYear, $type)
    {
        switch ($type) {
            case self::LAYER_TYPE_ZP:
                // dobavqne na sloi zemedelski parcel
                $color = $this->LayersController->StringHelper->randomColorCode();
                $tableName = 'layer_zp_' . time();
                $fields = [];
                $style = [
                    'color' => $color,
                    'border_color' => '111111',
                    'transparency' => 100,
                    'border_only' => false,
                    'label_name' => [null],
                    'tags' => true,
                    'label_size' => 8,
                ];
                $fields['name'] = 'Земеделски парцели';
                $fields['user_id'] = $group_id;
                $fields['farming'] = $farming_id;
                $fields['year'] = $currentYear;
                $fields['extent'] = self::DEFAULT_MAX_EXTENT;
                $fields['table_name'] = $tableName;
                $fields['layer_type'] = self::LAYER_TYPE_ZP;
                $fields['style'] = json_encode($style);
                $fields['color'] = $color;
                $fields['group_id'] = $group_id;
                $settings['mainData'] = $fields;
                $this->LayersController->addLayerItem($settings);
                $this->output->writeln('group_id =' . $group_id . ' and farming_id =' . $farming_id . ' and year =' . $currentYear . ' and layer_type =' . $type);
                sleep(1);

                break;

            case self::LAYER_TYPE_KMS:
                // dobavqne na sloi komasaciq
                $color = $this->LayersController->StringHelper->randomColorCode();
                $tableName = 'layer_kms_' . time();
                $fields = [];
                $style = [
                    'color' => $color,
                    'border_color' => '111111',
                    'transparency' => 100,
                    'border_only' => false,
                    'label_name' => [null],
                    'tags' => true,
                    'label_size' => 8,
                ];
                $fields['name'] = 'Данни от комасация';
                $fields['user_id'] = $group_id;
                $fields['farming'] = $farming_id;
                $fields['year'] = $currentYear;
                $fields['extent'] = self::DEFAULT_MAX_EXTENT;
                $fields['table_name'] = $tableName;
                $fields['layer_type'] = self::LAYER_TYPE_KMS;
                $fields['style'] = json_encode($style);
                $fields['color'] = $color;
                $fields['group_id'] = $group_id;
                $settings['mainData'] = $fields;
                $this->LayersController->addLayerItem($settings);
                $this->output->writeln('group_id =' . $group_id . ' and farming_id =' . $farming_id . ' and year =' . $currentYear . ' and layer_type =' . $type);
                sleep(1);

                break;

            case self::LAYER_TYPE_ISAK:
                // dobavqne na ot ISAK
                $color = $this->LayersController->StringHelper->randomColorCode();
                $tableName = 'layer_isak_' . time();
                $fields = [];
                $style = [
                    'color' => $color,
                    'border_color' => '111111',
                    'transparency' => 100,
                    'border_only' => false,
                    'label_name' => [null],
                    'tags' => true,
                    'label_size' => 8,
                ];
                $fields['name'] = 'от ИСАК';
                $fields['user_id'] = $group_id;
                $fields['farming'] = $farming_id;
                $fields['year'] = $currentYear;
                $fields['extent'] = self::DEFAULT_MAX_EXTENT;
                $fields['table_name'] = $tableName;
                $fields['layer_type'] = self::LAYER_TYPE_ISAK;
                $fields['color'] = $color;
                $fields['style'] = json_encode($style);
                $fields['group_id'] = $group_id;
                $settings['mainData'] = $fields;
                $this->LayersController->addLayerItem($settings);
                $this->output->writeln('group_id =' . $group_id . ' and farming_id =' . $farming_id . ' and year =' . $currentYear . ' and layer_type =' . $type);
                sleep(1);

                break;

            case self::LAYER_TYPE_FOR_ISAK:
                if ($currentYear >= 6) {
                    // dobavqne na za ISAK
                    $color = $this->LayersController->StringHelper->randomColorCode();
                    $tableName = 'layer_for_isak_' . time();
                    $fields = [];
                    $style = [
                        'color' => $color,
                        'border_color' => '111111',
                        'transparency' => 100,
                        'border_only' => false,
                        'label_name' => [null],
                        'tags' => true,
                        'label_size' => 8,
                    ];
                    $fields['name'] = 'за ИСАК';
                    $fields['user_id'] = $group_id;
                    $fields['farming'] = $farming_id;
                    $fields['year'] = $currentYear;
                    $fields['extent'] = self::DEFAULT_MAX_EXTENT;
                    $fields['table_name'] = $tableName;
                    $fields['layer_type'] = self::LAYER_TYPE_FOR_ISAK;
                    $fields['color'] = $color;
                    $fields['group_id'] = $group_id;
                    $fields['style'] = json_encode($style);
                    $settings['mainData'] = $fields;
                    $this->LayersController->addLayerItem($settings);
                    $this->output->writeln('group_id =' . $group_id . ' and farming_id =' . $farming_id . ' and year =' . $currentYear . ' and layer_type =' . $type);
                }
                sleep(1);

                break;
        }
    }
}
