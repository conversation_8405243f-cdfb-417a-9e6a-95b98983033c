<?php

namespace TF\Commands\Common;

use PDO;
use Symfony\Component\Console\Input\InputOption;

/**
 * command run on all databases.
 */
class FixAllowableAreaAfterIntersection extends UserDbCommand
{
    private $regions = [
        'blagoevgrad',
        'burgas',
        'dobrich',
        'gabrovo',
        'haskovo',
        'kardzhali',
        'kyustendil',
        'lovech',
        'montana',
        'pazardzhik',
        'pernik',
        'pleven',
        'plovdiv',
        'razgrad',
        'ruse',
        'shumen',
        'silistra',
        'sliven',
        'smolyan',
        'sofia',
        'sofiagrad',
        'starazagora',
        'targovishte',
        'varna',
        'velikotarnovo',
        'vidin',
        'vratsa',
        'yambol',
    ];

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('tf:fix-allowable-area-after-intersection')
            ->setDescription('Fix an allowable area of the plots which are not intersect with layer_allowable or layer_allowable_final')
            ->addOption('regions', '-r', InputOption::VALUE_OPTIONAL, 'If set true, the script will be execute by region kvs tables instead of layer_kvs');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $userDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        if ($input->getOption('regions') && 'true' === $input->getOption('regions')) {
            $currentCount = 1;
            $allRegionsCount = count($this->regions);
            foreach ($this->regions as $region) {
                echo 'Region: ' . $region . ' (' . $currentCount++ . '/' . $allRegionsCount . ")\r\n";
                $this->createGetOutterPlotsPostgreFunction($userDev, $region);
                $sql = $userDev->prepare('UPDATE ' . $region . ' as kvs SET allowable_area = 0.000 from tf_get_plots_out_from_allowable_layer() as outer_plots where kvs.gid = outer_plots.gid;');

                $sql->execute();
            }
        } else {
            $this->createGetOutterPlotsPostgreFunction($userDev);
            $ekattesSql = $userDev->prepare('SELECT kvs.ekate FROM layer_kvs kvs GROUP BY kvs.ekate;');
            $ekattesSql->execute();
            $ekattes = $ekattesSql->fetchAll();
            foreach ($ekattes as $ekatte) {
                if (!empty($ekatte['ekate'])) {
                    $sql = $userDev->prepare('UPDATE layer_kvs SET allowable_area = 0.000 from tf_get_plots_out_from_allowable_layer(' . $ekatte['ekate'] . ') as outer_plots where layer_kvs.gid = outer_plots.gid;');
                    $sql->execute();
                    echo $ekatte['ekate'] . " processed! \r\n";
                }
            }
        }
    }

    private function createGetOutterPlotsPostgreFunction($userDev, $region = 'layer_kvs')
    {
        $userDev->prepare('DROP FUNCTION IF EXISTS tf_get_plots_out_from_allowable_layer();')->execute();

        $createFunctionSql = 'CREATE OR REPLACE FUNCTION public.tf_get_plots_out_from_allowable_layer(_ekate text)
                 RETURNS TABLE(gid integer, kad_ident character varying)
                 LANGUAGE plpgsql
                AS $function$
                    DECLARE
                        reg record;
                    BEGIN
                        FOR reg IN SELECT kvs.ekate FROM public.' . $region . ' kvs WHERE kvs.ekate = _ekate group by ekate
                           loop
                                RETURN QUERY SELECT
                                    kvs.gid, kvs.kad_ident
                                FROM public.' . $region . " kvs
                                inner join dblink (
                                        'host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "'::TEXT,
                                        'SELECT
                                            st_union(geom)
                                        FROM
                                            layer_allowable_final
                                        WHERE geom && ST_GeomFromText(''' || (
                                            SELECT
                                                st_astext (
                                                    st_envelope (st_extent(geom))
                                                )
                                            FROM
                                                " . $region . " where ekate = reg.ekate
                                        ) || ''');'
                                    ) AS A (geom geometry) on not st_intersects(kvs.geom, a.geom)
                                    where kvs.ekate = reg.ekate;
                            END LOOP;
                    END;
                \$function$
        ;";

        $sql = $userDev->prepare($createFunctionSql);
        $sql->execute();
    }
}
