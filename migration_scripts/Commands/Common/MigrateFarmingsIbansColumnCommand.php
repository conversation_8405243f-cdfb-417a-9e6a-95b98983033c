<?php

namespace TF\Commands\Common;

use PDO;
use Prado;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Commands\ParamHelper;

Prado::using('Plugins.Core.UserDb.*');

/**
 * TS-4955 command run on all databases.
 */
class MigrateFarmingsIbansColumnCommand extends BaseCommand
{
    private $susiMainConnection;

    protected function configure()
    {
        $this
            ->setName('tf:migrate_farmings_ibans_column')
            ->setDescription('Change iban_arr format of su_users_farming database (added name, iban and bic fields)')
            ->addArgument(
                'user_databases',
                InputArgument::IS_ARRAY | InputArgument::OPTIONAL,
                'Databases to run the script on (separate multiple names with a space)?'
            )
            ->addOption('year', null, InputOption::VALUE_OPTIONAL, 'If set, the task will only on users with paid support for the inpit year');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $user_databases = $input->getArgument('user_databases');
        $paramHelper = new ParamHelper();
        $userDbString = $paramHelper->createQueryParamString($user_databases);

        $userDbConditions = 'true';
        if ($user_databases) {
            $userDbConditions = 'u.database IN (' . $userDbString . ')';
        }

        $this->susiMainConnection = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = $this->susiMainConnection->prepare(
            "SELECT u.id, u.database FROM su_users u
            WHERE 
            {$userDbConditions}
            AND u.level = 2
            ORDER BY database"
        );

        $sql->execute();
        $results = $sql->fetchAll();
        foreach ($results as $result) {
            $items = $this->getOptions($result['id']);
            foreach ($items as $item) {
                $banksArr = json_decode($item['iban_arr'], true);
                if (empty($banksArr) || !is_array($banksArr)) {
                    $this->saveOptions($item['id'], json_encode([
                        [
                            'name' => '',
                            'iban' => '',
                            'bic' => '',
                        ],
                    ]));
                }

                $newBankArr = [];
                foreach ($banksArr as $bank) {
                    if (!is_array($bank)) {
                        $newBankArr[] = [
                            'name' => '',
                            'iban' => $bank,
                            'bic' => '',
                        ];
                    }
                }

                if ($newBankArr) {
                    $this->saveOptions($item['id'], json_encode($newBankArr));
                }
            }
            echo 'Database: ' . $result['database'] . ' - DONE' . PHP_EOL;
        }
    }

    private function getOptions($user_id)
    {
        $sql = 'SELECT id, iban_arr FROM public.su_users_farming WHERE  user_id = ' . $user_id;
        $results = $this->susiMainConnection->prepare($sql);
        $results->execute();

        return $results->fetchAll();
    }

    private function saveOptions($id, $options)
    {
        $sql = "UPDATE public.su_users_farming SET iban_arr = '{$options}' WHERE  id = " . $id;
        $results = $this->susiMainConnection->prepare($sql);
        $results->execute();
    }
}
