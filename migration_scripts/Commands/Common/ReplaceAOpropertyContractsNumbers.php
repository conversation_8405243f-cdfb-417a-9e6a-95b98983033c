<?php

namespace TF\Commands\Common;

use Exception;
use PDO;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class ReplaceAOpropertyContractsNumbers extends BaseCommand
{
    protected $skipLogging = true;

    private $pradoApp;

    public function setPradoApp($pradoApp)
    {
        $this->pradoApp = $pradoApp;
    }

    public function getPradoApp()
    {
        return $this->pradoApp;
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('tf:replace_property_contract_numbers_in_migrated_users')
            ->setDescription('Copy user account between servers.')
            ->addArgument('user_db', InputArgument::OPTIONAL, 'Username');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $username = $input->getArgument('user_db');

        try {
            $whereUser = '';
            if ($username) {
                $whereUser = ' and su."database" = \'' . $username . '\'';
            }

            $mainDbConnection = new PDO('pgsql:dbname=susi_main;host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';user=' . DEFAULT_DB_USERNAME, DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

            $estmt = $mainDbConnection->query(
                "
                select 
                    string_agg(distinct \"database\", ',') as tf_db, 
                    suf.ao_db 
                from su_users su 
                inner join su_users_farming suf on suf.group_id = su.group_id 
                where 
                    suf.ao_db is not null
                    " . $whereUser . '
                group by suf.ao_db '
            );

            $usersDbs = $estmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            $output->error($e->getMessage());
            exit;
        }

        $countDbs = count($usersDbs);
        $dbCounter = 0;
        foreach ($usersDbs as $userDbs) {
            $dbCounter++;
            if (in_array($userDbs['tf_db'], ['db_bg_1711', 'db_bg_792', 'db_bg_8843', 'db_bg_329', 'db_zk_agrarika', 'db_bg_8843', 'db_bg_2842'])) {
                $output->writeln('User with ao_db: ' . $userDbs['ao_db'] . ' is already migrated. Skip...');

                continue;
            }

            try {
                $aoUserDbConnection = new PDO('sqlsrv:Server=***********;Database=' . $userDbs['ao_db'], 'appadmin', 't}}[ZY3ad');

                $stmtAOContracts = $aoUserDbConnection->query('
                    select 
                        COALESCE (pd.NLActNumber, pd.PropertyDocId) as na_number,
                        pd.PropertyDocId as ao_id
                    from PropertyDoc pd
                    left join Firm f on f.FirmId = pd.FirmId 
                    left join Person p on p.PersonId = f.PersonId 
                    where 
                        pd.DeletedFlag = 0 
                        and pd.PropertyDocKindId is not null;
            ');

                if (empty($stmtAOContracts)) {
                    $this->logging('Empty stmtAOContracts: Wrong data in ao_db: ' . $userDbs['ao_db']);

                    continue;
                }

                $AOpropertyContracts = $stmtAOContracts->fetchAll(PDO::FETCH_ASSOC);
                $countAOPropertyContracts = count($AOpropertyContracts);
                if (0 == $countAOPropertyContracts) {
                    $output->writeln('No contracts found for user with ao_db: ' . $userDbs['ao_db']);

                    continue;
                }
            } catch (Exception $e) {
                $output->error($e->getMessage());
                exit;
            }

            try {
                $userDbConnection = new PDO('pgsql:dbname=' . $userDbs['tf_db'] . ';host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';user=' . DEFAULT_DB_USERNAME, DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

                $this->disableRefreshing($userDbConnection);

                $propertyContractsCounter = 0;
                foreach ($AOpropertyContracts as $AOpropertyContract) {
                    $propertyContractsCounter++;
                    $output->writeln('Set contract number: ' . $AOpropertyContract['na_number'] . ' for contract with ao_id: ' . $AOpropertyContract['ao_id'] . ', ao_db: ' . $userDbs['ao_db'] . ' and tf_db: ' . $userDbs['tf_db'] . '. Contracts: (' . $propertyContractsCounter . '/' . $countAOPropertyContracts . '), Users: (' . $dbCounter . '/' . $countDbs . ')');
                    $stmtUpdate = $userDbConnection->prepare("
                            update 
                                su_contracts
                            set 
                                c_num = :na_number
                            where
                                nm_usage_rights = 1
                                and ao_type = 'property'
                                and ao_db = :ao_db
                                and ao_id = :ao_id;
                    ");
                    $stmtUpdate->execute([
                        'na_number' => $AOpropertyContract['na_number'],
                        'ao_id' => $AOpropertyContract['ao_id'],
                        'ao_db' => $userDbs['ao_db'],
                    ]);
                }
                $this->enableRefreshing($userDbConnection);
            } catch (Exception $e) {
                $this->enableRefreshing($userDbConnection);

                if (7 == $e->getCode()) {
                    $this->logging('No database found: ' . $userDbs['tf_db']);
                    $output->writeln('No database found: ' . $userDbs['tf_db']);

                    continue;
                }
                $output->error($e->getMessage());
                $output->error($e->getCode());
                exit;
            }
        }
    }

    private function logging($data)
    {
        $logsDirPath = __DIR__ . '/Logs/';
        $classNameWithoutNamespace = basename(str_replace('\\', '/', get_called_class()));
        $logFilePath = $logsDirPath . $classNameWithoutNamespace . '.log';

        if (!is_dir($logsDirPath)) {
            mkdir($logsDirPath, 0777, true);
        }

        file_put_contents($logFilePath, $data . PHP_EOL, FILE_APPEND);
    }

    private function disableRefreshing($userDbConnection)
    {
        $cmd = $userDbConnection->prepare('CREATE OR REPLACE FUNCTION "public"."refresh_rentas_materialized_views"()
        RETURNS "pg_catalog"."trigger" AS $BODY$
        BEGIN
            --REFRESH MATERIALIZED VIEW charged_rentas_mat_view;
            --REFRESH MATERIALIZED VIEW charged_rentas_annexes_mat_view;
            --REFRESH MATERIALIZED VIEW renta_nats_mat_view;
            --REFRESH MATERIALIZED VIEW renta_nats_annexes_mat_view;
            RETURN NUll;
        END
        $BODY$
        LANGUAGE \'plpgsql\' VOLATILE COST 100;');
        $cmd->execute();
    }

    private function enableRefreshing($userDbConnection)
    {
        $cmd = $userDbConnection->prepare('CREATE OR REPLACE FUNCTION "public"."refresh_rentas_materialized_views"()
        RETURNS "pg_catalog"."trigger" AS $BODY$
        BEGIN
            REFRESH MATERIALIZED VIEW charged_rentas_mat_view;
            REFRESH MATERIALIZED VIEW charged_rentas_annexes_mat_view;
            REFRESH MATERIALIZED VIEW renta_nats_mat_view;
            REFRESH MATERIALIZED VIEW renta_nats_annexes_mat_view;
            RETURN NUll;
        END
        $BODY$
        LANGUAGE \'plpgsql\' VOLATILE COST 100;');
        $cmd->execute();
    }
}
