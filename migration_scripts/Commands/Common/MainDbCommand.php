<?php

namespace TF\Commands\Common;

use PDO;
use PDOException;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Commands\Common\Custom\TFConsoleOutput;

/**
 * TF class extention on BaseCommand.
 */
abstract class MainDbCommand extends BaseCommand
{
    abstract protected function onDbExecute(PDO $pdo, $output, $input);

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $pdo = $this->openConnection();
        $pdo->setAttribute(PDO::ATTR_EMULATE_PREPARES, 1);

        try {
            $pdo->beginTransaction();
            $this->onDbExecute($pdo, new TFConsoleOutput(), $input);
            $pdo->commit();
        } catch (PDOException $e) {
            $pdo->rollBack();

            if (!$this->skipLogging) {
                $this->logScript($results[$i]['database'], $e->getMessage());
            }
        }
    }

    protected function openConnection()
    {
        return new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
    }

    protected function executeSql(PDO $pdo, string $pathToSql)
    {
        $sql = file_get_contents($pathToSql);
        $queries = explode(';', $sql);
        foreach ($queries as $query) {
            if (empty(trim($query))) {
                continue;
            }
            $sql = $pdo->prepare($query);
            $sql->execute();
        }
    }
}
