<?php

namespace TF\Commands\Common;

use PDO;
use Prado;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Commands\ParamHelper;

Prado::using('Plugins.Core.UserDb.*');

/**
 * TS-2120 command run on all databases.
 */
class FixSystemLayerColorsCommand extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('tf:fix_system_layer_colors')
            ->setDescription('promenq cvetovete na sistemnite sloeve v su_users_layers, koito ne sa deistvitelni')
            ->addArgument(
                'user_databases',
                InputArgument::IS_ARRAY | InputArgument::OPTIONAL,
                'Databases to run the script on (separate multiple names with a space)?'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $user_databases = $input->getArgument('user_databases');

        $paramHelper = new ParamHelper();
        $userDbString = $paramHelper->createQueryParamString($user_databases);

        $userDbConditions = 'true';
        if ($user_databases) {
            $userDbConditions = 'u.database IN (' . $userDbString . ')';
        }

        $mainDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        // Слой: LAYER_TYPE_LFA
        $sql = $mainDev->prepare(
            "SELECT u.id AS id FROM su_users u LEFT JOIN su_users_layers ul ON u.id = ul.user_id
            WHERE {$userDbConditions} AND u.level = 2 AND ul.layer_type = 10 AND ( ul.color <> 'fafafa' OR ul.border_color <> '40C080')
            ORDER BY database"
        );

        $sql->execute();
        $results = $sql->fetchAll();

        $id_array = [];
        foreach ($results as $result) {
            $id_array[] = $result['id'];
        }

        $id_string = implode(', ', $id_array);

        for ($i = 0; $i < count($results); $i++) {
            $userID = $results[$i]['id'];
            $output->writeln("UserID: {$userID} ");

            $sql = "UPDATE su_users_layers SET color = 'fafafa', border_color = '40C080' WHERE user_id IN ({$id_string}) AND layer_type = 10";
            system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . DEFAULT_DB_DATABASE . ' -c "' . $sql . '"', $return);
        }

        // Слой: LAYER_TYPE_NATURA_2000
        $sql = $mainDev->prepare(
            "SELECT u.id AS id FROM su_users u LEFT JOIN su_users_layers ul ON u.id = ul.user_id
            WHERE {$userDbConditions} AND u.level = 2 AND ul.layer_type = 11 AND ( ul.color <> 'fafafa' OR ul.border_color <> 'A0A000')
            ORDER BY database"
        );

        $sql->execute();
        $results = $sql->fetchAll();

        $id_array = [];
        foreach ($results as $result) {
            $id_array[] = $result['id'];
        }

        $id_string = implode(', ', $id_array);

        for ($i = 0; $i < count($results); $i++) {
            $userID = $results[$i]['id'];
            $output->writeln("UserID: {$userID} ");

            $sql = "UPDATE su_users_layers SET color = 'fafafa', border_color = 'A0A000' WHERE user_id IN ({$id_string}) AND layer_type = 11";
            system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . DEFAULT_DB_DATABASE . ' -c "' . $sql . '"', $return);
        }

        // Слой: LAYER_TYPE_PERMANETELY_GREEN_AREAS
        $sql = $mainDev->prepare(
            "SELECT u.id AS id FROM su_users u LEFT JOIN su_users_layers ul ON u.id = ul.user_id
            WHERE {$userDbConditions} AND u.level = 2 AND ul.layer_type = 12 AND ( ul.color <> 'fafafa' OR ul.border_color <> '00f000')
            ORDER BY database"
        );

        $sql->execute();
        $results = $sql->fetchAll();

        $id_array = [];
        foreach ($results as $result) {
            $id_array[] = $result['id'];
        }

        $id_string = implode(', ', $id_array);

        for ($i = 0; $i < count($results); $i++) {
            $userID = $results[$i]['id'];
            $output->writeln("UserID: {$userID} ");

            $sql = "UPDATE su_users_layers SET color = 'fafafa', border_color = '00f000' WHERE user_id IN ({$id_string}) AND layer_type = 12";
            system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . DEFAULT_DB_DATABASE . ' -c "' . $sql . '"', $return);
        }

        // Слой: LAYER_TYPE_VPS_PASISHTA
        $sql = $mainDev->prepare(
            "SELECT u.id AS id FROM su_users u LEFT JOIN su_users_layers ul ON u.id = ul.user_id
            WHERE {$userDbConditions} AND u.level = 2 AND ul.layer_type = 13 AND ( ul.color <> 'ffffff' OR ul.border_color <> 'A70000')
            ORDER BY database"
        );

        $sql->execute();
        $results = $sql->fetchAll();

        $id_array = [];
        foreach ($results as $result) {
            $id_array[] = $result['id'];
        }

        $id_string = implode(', ', $id_array);

        for ($i = 0; $i < count($results); $i++) {
            $userID = $results[$i]['id'];
            $output->writeln("UserID: {$userID} ");

            $sql = "UPDATE su_users_layers SET color = 'ffffff', border_color = 'A70000' WHERE user_id IN ({$id_string}) AND layer_type = 13";
            system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . DEFAULT_DB_DATABASE . ' -c "' . $sql . '"', $return);
        }

        // Слой: LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI
        $sql = $mainDev->prepare(
            "SELECT u.id AS id FROM su_users u LEFT JOIN su_users_layers ul ON u.id = ul.user_id
            WHERE {$userDbConditions} AND u.level = 2 AND ul.layer_type = 14 AND ( ul.color <> 'ffffff' OR ul.border_color <> 'FFA41C')
            ORDER BY database"
        );

        $sql->execute();
        $results = $sql->fetchAll();

        $id_array = [];
        foreach ($results as $result) {
            $id_array[] = $result['id'];
        }

        $id_string = implode(', ', $id_array);

        for ($i = 0; $i < count($results); $i++) {
            $userID = $results[$i]['id'];
            $output->writeln("UserID: {$userID} ");

            $sql = "UPDATE su_users_layers SET color = 'ffffff', border_color = 'FFA41C' WHERE user_id IN ({$id_string}) AND layer_type = 14";
            system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . DEFAULT_DB_DATABASE . ' -c "' . $sql . '"', $return);
        }

        // Слой: LAYER_TYPE_VPS_GASKI_ZIMNI
        $sql = $mainDev->prepare(
            "SELECT u.id AS id FROM su_users u LEFT JOIN su_users_layers ul ON u.id = ul.user_id
            WHERE {$userDbConditions} AND u.level = 2 AND ul.layer_type = 15 AND ( ul.color <> 'ffffff' OR ul.border_color <> 'E54100')
            ORDER BY database"
        );

        $sql->execute();
        $results = $sql->fetchAll();

        $id_array = [];
        foreach ($results as $result) {
            $id_array[] = $result['id'];
        }

        $id_string = implode(', ', $id_array);

        for ($i = 0; $i < count($results); $i++) {
            $userID = $results[$i]['id'];
            $output->writeln("UserID: {$userID} ");

            $sql = "UPDATE su_users_layers SET color = 'ffffff', border_color = 'E54100' WHERE user_id IN ({$id_string}) AND layer_type = 15";
            system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . DEFAULT_DB_DATABASE . ' -c "' . $sql . '"', $return);
        }

        // Слой: LAYER_TYPE_VPS_LIVADEN_BLATAR
        $sql = $mainDev->prepare(
            "SELECT u.id AS id FROM su_users u LEFT JOIN su_users_layers ul ON u.id = ul.user_id
            WHERE {$userDbConditions} AND u.level = 2 AND ul.layer_type = 16 AND ( ul.color <> 'ffffff' OR ul.border_color <> 'A86C0F')
            ORDER BY database"
        );

        $sql->execute();
        $results = $sql->fetchAll();

        $id_array = [];
        foreach ($results as $result) {
            $id_array[] = $result['id'];
        }

        $id_string = implode(', ', $id_array);

        for ($i = 0; $i < count($results); $i++) {
            $userID = $results[$i]['id'];
            $output->writeln("UserID: {$userID} ");

            $sql = "UPDATE su_users_layers SET color = 'ffffff', border_color = 'A86C0F' WHERE user_id IN ({$id_string}) AND layer_type = 16";
            system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . DEFAULT_DB_DATABASE . ' -c "' . $sql . '"', $return);
        }

        // Слой: LAYER_TYPE_VPS_ORLI_LESHOYADI
        $sql = $mainDev->prepare(
            "SELECT u.id AS id FROM su_users u LEFT JOIN su_users_layers ul ON u.id = ul.user_id
            WHERE {$userDbConditions} AND u.level = 2 AND ul.layer_type = 17 AND ( ul.color <> 'ffffff' OR ul.border_color <> 'A900E6')
            ORDER BY database"
        );

        $sql->execute();
        $results = $sql->fetchAll();

        $id_array = [];
        foreach ($results as $result) {
            $id_array[] = $result['id'];
        }

        $id_string = implode(', ', $id_array);

        for ($i = 0; $i < count($results); $i++) {
            $userID = $results[$i]['id'];
            $output->writeln("UserID: {$userID} ");

            $sql = "UPDATE su_users_layers SET color = 'ffffff', border_color = 'A900E6' WHERE user_id IN ({$id_string}) AND layer_type = 17";
            system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . DEFAULT_DB_DATABASE . ' -c "' . $sql . '"', $return);
        }
    }
}
