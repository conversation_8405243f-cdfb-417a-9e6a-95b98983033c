<?php

namespace TF\Commands\Common;

use Exception;
use PDO;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Application\Entity\ObjectPermissions;

class AddUserFarmingsPermissionsCommand extends BaseCommand
{
    private $susiMainConnection;
    private $pradoApp;

    public function setPradoApp($pradoApp)
    {
        $this->pradoApp = $pradoApp;
    }

    protected function configure()
    {
        $this->setName('tf:add_user_farming_permissions')
            ->setDescription('Grant All users permissions to related farmings');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $this->susiMainConnection = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $auth = $this->pradoApp->getModule('auth');

        $sql = $this->susiMainConnection->prepare(
            'SELECT 
                u.id,
                u.database,
                u.username,
                u.level
             FROM 
                su_users as u
             WHERE
                u.level = 2
             '
        );

        if (!$sql->execute()) {
            die(print_r($sql->errorInfo()));
        }

        $sql->execute();

        $parentUsers = $sql->fetchAll();

        try {
            foreach ($parentUsers as $parent) {
                // set app user to parent user
                $auth->switchUser($parent['username']);
                $user = $this->pradoApp->getUser();

                $farmings = array_column($this->getUserFarmings($parent['id']), 'id');
                $usersToGrantPermissions = array_column($this->getSubUsers($parent['id']), 'id');
                array_push($usersToGrantPermissions, $parent['id']);

                foreach ($usersToGrantPermissions as $userId) {
                    $user->updateUserFarmingPermissions($farmings, $userId, ObjectPermissions::$permisionsMap);
                }
            }
        } catch (Exception $e) {
            $output->write($e->getMessage());
        }

        $output->write('end');
    }

    private function getUserFarmings($parentId)
    {
        $sql = $this->susiMainConnection->prepare(
            'SELECT 
                f.id,
                f.name,
                f.group_id
            FROM su_users_farming as f
            WHERE f.group_id = :parentId'
        );

        $sql->execute([':parentId' => $parentId]);

        return $sql->fetchAll();
    }

    private function getSubUsers($parentId)
    {
        $sql = $this->susiMainConnection->prepare(
            'SELECT 
                u.id,
                u.database,
                u.username,
                u.level
            FROM su_users as u
            WHERE u.parent_id = :parentId'
        );

        $sql->execute([':parentId' => $parentId]);

        return $sql->fetchAll();
    }
}
