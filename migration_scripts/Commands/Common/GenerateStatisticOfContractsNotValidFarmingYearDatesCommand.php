<?php

namespace TF\Commands\Common;

use PDO;

class GenerateStatisticOfContractsNotValidFarmingYearDatesCommand extends UserDbCommand
{
    private static $tmpTableName = 'tmp_statisic_of_contracts_not_valid_farming_year_dates';

    public function configure()
    {
        parent::configure();
        $this
            ->setName('tf:generate_statistic_of_contracts_not_valid_farming_year_dates')
            ->setDescription('Generate statistics of contracts with not valid farming year dates and publish the results in table ' . self::$tmpTableName . ' in susi_main');
    }

    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $contracts = $this->getContractWithInvalidDates($pdo);

        foreach ($contracts as $contract) {
            $sql = $this->mainConnection->prepare('
                    INSERT INTO ' . self::$tmpTableName . ' (
                        database,
                        contract_id,
                        nm_usage_rights,
                        c_num,
                        start_date,
                        due_date
                    )
                    VALUES (
                        :database,
                        :contract_id,
                        :nm_usage_rights,
                        :c_num,
                        :start_date,
                        :due_date
                    );
                ');

            $sql->execute([
                'database' => $this->userDbName,
                'contract_id' => $contract['id'],
                'nm_usage_rights' => $contract['nm_usage_rights'],
                'c_num' => $contract['c_num'],
                'start_date' => $contract['start_date'],
                'due_date' => $contract['due_date'],
            ]);
        }
    }

    protected function onCommandStart($input, $output)
    {
        $sql = $this->mainConnection->prepare('
            CREATE TABLE IF NOT EXISTS ' . self::$tmpTableName . ' (
                database varchar NOT NULL,
                contract_id int NOT NULL,
                nm_usage_rights int NOT NULL,
                c_num varchar NOT NULL,
                start_date timestamp without time zone NOT NULL,
                due_date timestamp without time zone NOT NULL
            );
        ');
        $sql->execute();

        $sql = $this->mainConnection->prepare('
            TRUNCATE ' . self::$tmpTableName . ';
        ');
        $sql->execute();
    }

    /**
     * @param PDO $userDb
     * @param string $tableName
     *
     * @return array|bool
     */
    protected function getContractWithInvalidDates($pdo)
    {
        $cmd = $pdo->prepare('
            SELECT
                id,
                c_num,
                nm_usage_rights,
                start_date,
                due_date
            FROM
                su_contracts
            WHERE
                nm_usage_rights != 1
                AND
                (
                    (EXTRACT(MONTH FROM start_date) != 10 AND EXTRACT(DAY FROM start_date) != 1)
                    OR
                    (EXTRACT(MONTH FROM due_date) != 9 AND EXTRACT(DAY FROM due_date) != 30)
                )
                
        ');
        $cmd->execute();

        return $cmd->fetchAll(PDO::FETCH_ASSOC);
    }
}
