<?php

namespace TF\Commands\Common;

use PDO;

class GetUsersWithModuleMortgageCommand extends UserDbCommand
{
    private static $tmpTableName = 'tmp_users_with_used_module_mortgages';

    public function configure()
    {
        parent::configure();
        $this
            ->setName('tf:get_users_with_used_module_mortgages')
            ->setDescription('Get all users who have at least one record in the Mortgages module and log the result in temp table ' . self::$tmpTableName . ' in susi_main');
    }

    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $mortgages = $this->getMortgages($pdo);

        if (!empty($mortgages)) {
            $sql = $this->mainConnection->prepare('
                    INSERT INTO ' . self::$tmpTableName . ' (
                        database,
                        num,
                        "date",
                        due_date,
                        start_date,
                        is_active,
                        deactivate_num,
                        deactivate_date
                    )
                    VALUES (
                        :database,
                        :num,
                        :date,
                        :due_date,
                        :start_date,
                        :is_active,
                        :deactivate_num,
                        :deactivate_date
                    );
                ');

            foreach ($mortgages as $mortgage) {
                $sql->execute([
                    'database' => $this->userDbName,
                    'num' => $mortgage['num'],
                    'date' => $mortgage['date'],
                    'due_date' => $mortgage['due_date'],
                    'start_date' => $mortgage['start_date'],
                    'is_active' => $mortgage['is_active'],
                    'deactivate_num' => $mortgage['deactivate_num'],
                    'deactivate_date' => $mortgage['deactivate_date'],
                ]);
            }

            $output->writeln('In database ' . $this->userDbName . ' has ' . count($mortgages) . ' mortgages' . PHP_EOL);
        } else {
            $output->writeln('In database ' . $this->userDbName . ' has no mortgages found' . PHP_EOL);
        }
    }

    protected function onCommandStart($input, $output)
    {
        $sql = $this->mainConnection->prepare('
            CREATE TABLE IF NOT EXISTS ' . self::$tmpTableName . ' (
                database varchar(255) NOT NULL,
                num varchar(255) NOT NULL,
                "date" date NOT NULL,
                due_date date NOT NULL,
                start_date date NOT NULL,
                is_active bool DEFAULT true NOT NULL,
                deactivate_num varchar(255) NULL,
                deactivate_date date NULL
            );
        ');
        $sql->execute();

        $sql = $this->mainConnection->prepare('
            TRUNCATE ' . self::$tmpTableName . ';
        ');
        $sql->execute();
    }

    /**
     * @param PDO $userDb
     * @param string $tableName
     *
     * @return array|bool
     */
    protected function getMortgages($pdo)
    {
        $cmd = $pdo->prepare('
            select 
                num,
                "date",
                due_date,
                start_date,
                is_active,
                deactivate_num,
                deactivate_date
            from su_hypothecs hyp;
        ');
        $cmd->execute();

        return $cmd->fetchAll(PDO::FETCH_ASSOC);
    }
}
