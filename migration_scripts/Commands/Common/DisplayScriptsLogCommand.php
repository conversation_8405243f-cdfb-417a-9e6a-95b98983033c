<?php

namespace TF\Commands\Common;

use PDO;
use Prado;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Commands\ParamHelper;

Prado::using('Plugins.Core.UserDb.*');

/**
 * TS-2359.
 */
class DisplayScriptsLogCommand extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('tf:display_scripts_log')
            ->setDescription('Izobrazqva izpulnenite scriptove za dadena (vsichki) baza.')
            ->addArgument('user_databases', InputArgument::IS_ARRAY | InputArgument::OPTIONAL, 'Databases to run the script on (separate multiple names with a space)?');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $user_databases = $input->getArgument('user_databases');

        $paramHelper = new ParamHelper();
        $userDbString = $paramHelper->createQueryParamString($user_databases);

        $userDbConditions = 'true';
        if ($user_databases) {
            $userDbConditions = 'db_name IN (' . $userDbString . ')';
        }

        $dbhDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
        $sql = $dbhDev->prepare("SELECT
                    id, db_name, name, date
                FROM scripts_log
                WHERE
                    {$userDbConditions}
                ORDER BY date desc
            ");
        $sql->execute();
        $results = $sql->fetchAll(PDO::FETCH_ASSOC);

        $headers = [
            'id' => 'id',
            'db_name' => 'db_name',
            'name' => 'name',
            'date' => 'date',
        ];

        $table = new \cli\Table();
        $table->setHeaders($headers);
        $table->setRows($results);
        $table->display();
    }
}
