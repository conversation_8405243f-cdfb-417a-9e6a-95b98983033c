<?php

namespace TF\Commands\Common;

use Exception;
use PDO;
use TF\Application\Common\Config;
use TF\Application\Entity\UserLayers;
use TF\Engine\Plugins\Core\UserDb\UserDbModel;

/**
 * 1. Layers without physical tables:.
 *
 *      a. If the layer is of type work - delete it
 *
 *      b. If the layer is not of type work - set default definitions for it
 *
 * 2. Layers with physical tables
 *
 *      a. For layers of type work get the default and the existing definitions and merge them with auto-generated (from physical table) definitions. Compare columns from physical table with those from definitions and create the missing columns in the physical table
 *
 *      b. For non-work layers set default definitions then compare them with the columns from the physical table and create the missing columns in the table
 *
 * #Covered cases for work layers
 *
 * 1. The table has a primary key column
 *
 *      a. If there is definition with category gid pointing to column that is not a primary key it sets category gid to the primary key column and sets category number to the other column
 *
 *      b. If there is no definition with category gid it is created
 *
 * 2. The table does not have primary key column
 *
 *      a.If there is definition of category gid it creates new PK column in the physical table
 *
 *      b. If there is definition of category gid pointing to column that is not primary key - nothing changes
 *
 *      c. If there is no definition of category gid it adds the missing definition and creates new PK column in the physical table
 *
 * 3. There is missing definition for virtual column
 *
 *      a. If there is definition for the reference column it adds new auto-generated definition for the virtual column
 *
 *      b. If there is no definition for the reference column it adds auto-generated definitions for both columns.
 *
 *      c. (The reference column is not visible, not personalizable, … but it is copyable, where the virtual columns is personalizable, visible, … and copyable)
 *
 * 4. There is missing virtual column in the physical table
 *
 *      a. If it has definition for the virtual column but not for the reference column and the reference column exists in the physical table it adds auto-generated definition for the reference column and creates virtual column
 *
 *      b. If it has definition for the reference column it only creates the virtual column
 *
 *      c. Note: We cannot have virtual column without reference column. If a reference column is deleted, the virtual columns is deleted automatically
 *
 * 5. There is missing default definition
 *
 *      a. If it has a physical column for the missing definition, the definition is created
 *
 *      b. If it does not have a physical column for the missing definition, the definition and the physical column are created
 *
 * 6. There is missing non-default definition
 *
 *      a. It creates auto-generated definition
 */
class SyncLayerDefinitionsWithPhysicalTableCommand extends UserDbCommand
{
    private PDO $pdo;

    public function onDbExecute(PDO $pdo, $output, $input)
    {
        /**
         * Set the PDO instance so it can be used across the class.
         *
         * @var PDO $pdo
         */
        $this->pdo = $pdo;

        /**
         * The UserDbCommand class accepts the multiple database names as an argument so we cannot pass another argument to the command.
         * Set $layerId in order to run the command for a specific layer.
         *
         * @var null|int $layerId
         */
        $layerId = null;
        $allowedLayerTypesForSync = implode(', ', $this->getAllowedLayerTypesForSync());

        // Fetch layers to obtain
        $layers = $layerId
            ? UserLayers::finder()->findAll(
                "id = :layerId AND layer_type IN ({$allowedLayerTypesForSync})",
                [
                    ':layerId' => $layerId,
                ]
            )
            : UserLayers::finder()->findAll(
                "group_id = :groupId AND layer_type IN ({$allowedLayerTypesForSync})",
                [
                    ':groupId' => $this->organizationId,
                ]
            );

        if (0 === count($layers)) {
            $output->writeln("No layers found for user '{$this->organizationUserName}' (database: {$this->userDbName})");

            return;
        }

        // Fetch layers physical tables columns
        $output->writeln('Found ' . count($layers) . " layers for user '{$this->organizationUserName}' (database: {$this->userDbName})");
        $output->writeln('Fetching physical tables columns for all layers... ');

        $layersPhyscialTablesColumns = $this->getLayersPhysicalTablesColumns($layers);
        $output->writeln('Found ' . count($layersPhyscialTablesColumns) . ' physical tables!');

        $layersWithoutPhysicalTable = [];
        $layersWithPhysicalTable = [];
        foreach ($layers as $layer) {
            if (array_key_exists($layer->table_name, $layersPhyscialTablesColumns)) {
                $layersWithPhysicalTable[] = [
                    'user_layer' => $layer,
                    'physical_table_columns' => $layersPhyscialTablesColumns[$layer->table_name],
                ];
            } else {
                $layersWithoutPhysicalTable[] = $layer;
            }
        }

        // Handle layers
        if (0 < count($layersWithoutPhysicalTable)) {
            $this->handleLayersWithoutPhysicalTable($layersWithoutPhysicalTable, $output);
        }

        if (0 < count($layersWithPhysicalTable)) {
            $this->handleLayersWithPhysicalTable($layersWithPhysicalTable, $output);
        }
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('tf:sync_layer_definitions_with_physical_table')
            ->setDescription('Create missing layer definitions using the columns in physical tables and create missing physical columns using layer\'s definitions');
    }

    private function getAllowedLayerTypesForSync()
    {
        return [
            Config::LAYER_TYPE_ZP,
            Config::LAYER_TYPE_GPS,
            Config::LAYER_TYPE_KVS,
            Config::LAYER_TYPE_KMS,
            Config::LAYER_TYPE_ISAK,
            Config::LAYER_TYPE_FOR_ISAK,
            Config::LAYER_TYPE_CSD,
            Config::LAYER_TYPE_WORK_LAYER,
        ];
    }

    private function getLayersPhysicalTablesColumns(array $layers): array
    {
        $layerTablesStr = "'" . implode("', '", array_map(function ($layer) {
            return $layer->table_name;
        }, $layers)) . "'";

        $sql = "SELECT 
            info_sch.table_name,
            JSONB_AGG(
                JSONB_BUILD_OBJECT(
                    'col_name', info_sch.column_name,
                    'col_type', info_sch.udt_name,
                    'col_virtual', CASE WHEN info_sch.is_generated = 'ALWAYS'
                        THEN TRUE
                        ELSE FALSE
                    END,
                    'col_expression', info_sch.generation_expression,
                    'col_primary_key', CASE WHEN info_constr.constraint_type = 'PRIMARY KEY'
                        THEN TRUE
                        ELSE FALSE
                    END
                )
            ) AS columns
        FROM 
            information_schema.COLUMNS AS info_sch
        LEFT JOIN information_schema.key_column_usage info_kcu
			  ON info_sch.column_name = info_kcu.column_name
  				AND info_sch.table_name = info_kcu.table_name
  				AND info_sch.table_schema = info_kcu.table_schema
  		LEFT JOIN information_schema.table_constraints info_constr
			 ON info_kcu.constraint_name = info_constr.constraint_name
			  AND info_kcu.table_name = info_constr.table_name
			  AND info_kcu.table_schema = info_constr.table_schema
			  AND info_constr.constraint_type = 'PRIMARY KEY'
        WHERE 
            info_sch.table_name IN ({$layerTablesStr})
        GROUP BY
            info_sch.table_name";

        $cmd = $this->pdo->prepare($sql);
        $cmd->execute();

        return array_map(fn ($columns) => json_decode($columns, true), $cmd->fetchAll(PDO::FETCH_KEY_PAIR));
    }

    /**
     * Remove all work layers without physical table and set default definitions (without resetting col_title) for the rest.
     *
     * @param UserLayers[] $layers
     */
    private function handleLayersWithoutPhysicalTable(array $layers, $output)
    {
        $output->writeln('========= Handling layers without physical tables =========');

        foreach ($layers as $layer) {
            if (Config::LAYER_TYPE_WORK_LAYER == $layer->layer_type) {
                // Delete work layers without physical table because they cannot be used (cannot copy, export, etc.)
                $output->warn("Deleting work layer '{$layer->table_name}' without physical table... ");
                $layer->delete();
                $output->writeln('Done!');

                continue;
            }

            $output->write("Updating definitions for layer '{$layer->table_name}'... ");
            $this->updateNonWorkLayerDefinitions($layer);
            $output->writeln('Done!');
        }

        $output->writeln('========= Done =========');
    }

    /**
     * Set default definitions for non-work layers and create the missing columns in physical tables.
     */
    private function handleLayersWithPhysicalTable(array $layers, $output)
    {
        $output->writeln('========= Handling layers with physical tables =========');
        $userDbModel = new UserDbModel($this->userDbName);

        foreach ($layers as $layer) {
            /**
             * @var UserLayers $userLayer
             */
            $userLayer = $layer['user_layer'];

            // Update definitions
            $output->write("Updating definitions for layer '{$userLayer->table_name}'... ");
            if (Config::LAYER_TYPE_WORK_LAYER == $userLayer->layer_type) {
                $physicalTableColumns = $layer['physical_table_columns'];
                $this->updateWorkLayerDefinitions($userLayer, $physicalTableColumns);
            } else {
                $this->updateNonWorkLayerDefinitions($userLayer);
            }
            $output->writeln('Done!');

            // Create missing columns
            $output->writeln("Checking for missing columns in table '{$userLayer->table_name}'... ");
            $createdColumns = $userDbModel->createLayerMissingColumns($userLayer);

            if (count($createdColumns) > 0) {
                $output->info('Created columns: \'' . implode('\', \'', $createdColumns) . '\'');
            } else {
                $output->writeln('No missing columns');
            }
        }

        $output->writeln('========= Done =========');
    }

    /**
     * Sets the default definitions for non-work layers while keeping
     * the values of $propsToKeep from the existing definitions.
     */
    private function updateNonWorkLayerDefinitions(UserLayers $layer, $propsToKeep = ['col_title'])
    {
        $defaultDefinitions = UserLayers::getDefinitionsByType($layer->layer_type);
        $defaultDefinitions = array_combine(
            array_map(fn ($def) => $def['col_name'] . '_', $defaultDefinitions), // append '_' to guarantee the key is string
            $defaultDefinitions
        );

        $currentDefinitions = $layer->getDefinitions();
        $currentDefinitions = array_combine(
            array_map(fn ($def) => $def['col_name'] . '_', $currentDefinitions), // append '_' to guarantee the key is string
            $currentDefinitions
        );

        $propsToKeep = array_map(fn ($prop) => $prop . '_', $propsToKeep); // append '_' to match the format of the keys in the definitions arrays.

        $updatedDefinitions = [];
        foreach ($defaultDefinitions as $key => $defaultDefinition) {
            $updatedDefinition = $defaultDefinition;

            if (isset($currentDefinitions[$key])) {
                // Set the default definition for the current column while keeping the values of $propsToKeep from the current definition.
                $updatedDefinition = array_merge(
                    $currentDefinitions[$key],
                    array_diff_key($defaultDefinition, array_flip($propsToKeep))
                );
            }

            if (!UserLayers::validateDefinition($updatedDefinition)) {
                $colName = $updatedDefinition['col_name'];

                throw new Exception("Invalid definition for layer column '{$colName}' (layer: {$layer->table_name})!");
            }

            // Use $colName as key to avoid duplicated definitions.
            $updatedDefinitions[$key] = $updatedDefinition;
        }

        $layer->definitions = json_encode(array_values($updatedDefinitions));
        $layer->save();
    }

    private function updateWorkLayerDefinitions(UserLayers $layer, array $physicalTableColumns)
    {
        // Default definitions
        $defaultDefinitions = UserLayers::getDefinitionsByType($layer->layer_type);

        // Existing (current) definitions
        $currentDefinitions = $layer->getDefinitions();

        /**
         * @var array $matchingColumns - Matched default definition columns with existing definition column.
         *            This array is used to avoid duplicated default definitions (one from default def and one from current def when the column names are different).
         *            The keys are column names from the default definitions and the values are the matched columns names from the current definitions.
         */
        $matchingColumns = UserLayers::matchColumns($defaultDefinitions, $currentDefinitions);
        $missingDefaultDefinitions = array_filter(
            $defaultDefinitions,
            function ($def) use ($matchingColumns) {
                // If the $def['col_name'] exists in $matchingColumns keys, it means the column already exists in the current definitions,
                // so do not include it in the missing default definitions.
                return !isset($matchingColumns[$def['col_name']]);
            }
        );

        // Add the missing default definitions to the current (existing) definitions.
        $mergedDefinitions = array_merge($missingDefaultDefinitions, $currentDefinitions);

        // If there is primary key column, set it as 'gid' category.
        [$pkColumn] = array_values(array_filter($physicalTableColumns, fn ($col) => $col['col_primary_key']));

        if (isset($pkColumn)) {
            [$defaultGidDef] = UserLayers::filterDefinitions($defaultDefinitions, [['col_category' => Config::LAYER_COLUMN_CATEGORY_GID]]);
            $mergedDefinitions = array_map(
                function ($def) use ($pkColumn, $defaultGidDef) {
                    if ($def['col_name'] === $pkColumn['col_name']) {
                        return array_merge($defaultGidDef, ['col_name' => $pkColumn['col_name']]);
                    }

                    // Ensure there is only one column of category 'gid'.
                    if (Config::LAYER_COLUMN_CATEGORY_GID === $def['col_category']) {
                        $def['col_category'] = Config::LAYER_COLUMN_CATEGORY_NUMBER;
                    }

                    return $def;
                },
                $mergedDefinitions
            );
        }

        // Sort the physical table columns so the virtual columns are placed first.
        // This is needed because they fill the referenceColumns array which is used to set some properties of the definitions.
        uksort($physicalTableColumns, function ($key1, $key2) use ($physicalTableColumns) {
            $isVirtual1 = $physicalTableColumns[$key1]['col_virtual'];
            $isVirtual2 = $physicalTableColumns[$key2]['col_virtual'];

            if ($isVirtual1 === $isVirtual2) {
                return strcmp($key1, $key2); // If both are virtual or both are not, sort by key
            }

            return $isVirtual1 ? -1 : 1; // Place virtual columns first
        });
        $physicalTableColumns = array_values($physicalTableColumns);

        $colReferences = array_map(
            fn ($def) => $def['col_reference'],
            array_filter($mergedDefinitions, fn ($def) => $def['col_virtual'])
        );

        $mergedDefinitions = array_combine(
            // Use col_name as key to avoid duplicated keys definitions. Append '_' to the keys to ensure the key is string.
            array_map(fn ($def) => $def['col_name'] . '_', $mergedDefinitions),
            $mergedDefinitions
        );

        foreach ($physicalTableColumns as $physicalColumn) {
            $colName = strval($physicalColumn['col_name']);
            $colTitle = str_replace('virtual_', '', $colName);
            $colTitle = ucfirst(str_replace('_', ' ', $colTitle));
            $colVirtual = $physicalColumn['col_virtual'];
            $colExpression = $physicalColumn['col_expression'];

            $colReference = in_array($colName, $colReferences)
                ? $colName
                : null;

            if ($colVirtual && !$colReference) {
                // The column is virtual and there is no reference in the existing definitions.

                // Try to match the reference column name from the expression (matches only letters, digits and '_' which are between '()').
                preg_match('/\(([a-z_0-9]+)\)/', $colExpression, $matches);
                $colReference = $matches[1] ?? null;
            }

            if ($colReference && !in_array($colReference, $colReferences)) {
                $colReferences[] = $colReference;
            }

            $colCategory = $this->getColumnCategory($physicalColumn, $mergedDefinitions);
            $isGidOrGeomCategory = in_array($colCategory, [Config::LAYER_COLUMN_CATEGORY_GID, Config::LAYER_COLUMN_CATEGORY_GEOM]);
            $isReferenceColumn = in_array($colName, $colReferences);

            $colPersonalizable = $colVisible = $colSortable = $colExportable = !$isReferenceColumn && !$isGidOrGeomCategory;
            $colFilterSelectionType = !$isReferenceColumn && !$isGidOrGeomCategory
                ? Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE
                : null;
            $colEditable = !$colVirtual && !$isGidOrGeomCategory;
            $colCopyable = !$isReferenceColumn;

            $definition = [
                'col_name' => $colName,
                'col_title' => $colTitle,
                'col_visible' => $colVisible,
                'col_personalizable' => $colPersonalizable,
                'col_category' => $colCategory,
                'col_multiedit' => $colEditable,
                'col_singleedit' => $colEditable,
                'col_sortable' => $colSortable,
                'col_exportable' => $colExportable,
                'col_copyable' => $colCopyable,
                'col_virtual' => $colVirtual,
                'col_expression' => $colExpression,
                'col_filter_selection_type' => $colFilterSelectionType,
                'col_reference' => $colReference,
            ];

            $mergedDefKey = $colName . '_';
            if (isset($mergedDefinitions[$mergedDefKey])) {
                // Merge the properties of the generated $definition whith those from the existing definition.
                // The existing definition properties will overwrite the generated ones and the missing properties (if any) will ne added.
                // Overwrite the col_category property with the one generated by the getColumnCategory method.
                $definition = array_merge($definition, $mergedDefinitions[$mergedDefKey], ['col_category' => $colCategory]);
            }

            if (!UserLayers::validateDefinition($definition)) {
                throw new Exception("Invalid definition for layer column '{$colName}' (layer: {$layer->table_name})!");
            }

            $mergedDefinitions[$mergedDefKey] = $definition;
        }

        $layer->definitions = json_encode(array_values($mergedDefinitions));
        $layer->save();
    }

    private function getColumnCategory(array $physicalColumn, array $definitions): string
    {
        $colName = $physicalColumn['col_name'];
        $colCategory = Config::LAYER_COLUMN_CATEGORY_TEXT;
        $genericColumnCategories = $GLOBALS['Layers']['genericColumnCategories'];
        $usedCategories = [];

        [$definition] = UserLayers::filterDefinitions($definitions, [['col_name' => $colName]]);
        if (isset($definition['col_category'])) {
            // Get the category from the existing definition.
            $colCategory = $definition['col_category'];
        } else {
            $colType = $physicalColumn['col_type'];
            $usedCategories = array_column($definitions, 'col_category');
            $colCategory = $this->getColumnCategoryByDBType($colType, $colName, false);
        }

        if (in_array($colCategory, $usedCategories) && !in_array($colCategory, $genericColumnCategories)) {
            // The column is not generic and already exists in the definitions.
            // Ensure the non-generic column categories exist only once in the definition.
            $colCategory = $this->getColumnCategoryByDBType($colType, $colName, true);
        }

        return $colCategory;
    }

    private function getColumnCategoryByDBType(string $dbType, string $colName, bool $onlyGeneric): string
    {
        switch ($dbType) {
            case 'geometry':
                return Config::LAYER_COLUMN_CATEGORY_GEOM;
            case 'int':
            case 'int2':
            case 'int4':
            case 'int8':
            case 'numeric':
            case 'float':
            case 'float4':
            case 'float8':
                if (!$onlyGeneric && in_array($colName, ['gid', 'id', 'fid'])) {
                    return Config::LAYER_COLUMN_CATEGORY_GID;
                }

                if (!$onlyGeneric && 'slope' === $colName) {
                    return Config::LAYER_COLUMN_CATEGORY_SLOPE;
                }

                return Config::LAYER_COLUMN_CATEGORY_NUMBER;
            case 'bool':
                return Config::LAYER_COLUMN_CATEGORY_BOOLEAN;
            case 'date':
            case 'timestamp':
            case 'timestamptz':
                return Config::LAYER_COLUMN_CATEGORY_DATE;
            case 'text':
            case 'varchar':
                if (!$onlyGeneric && in_array($colName, ['name'])) {
                    return Config::LAYER_COLUMN_CATEGORY_NAME;
                }

                if (!$onlyGeneric && in_array($colName, ['culture', 'crop_code', 'cropcode', 'crop_code'])) {
                    return Config::LAYER_COLUMN_CATEGORY_CROP;
                }

                if (!$onlyGeneric && in_array($colName, ['declaration_type'])) {
                    return Config::LAYER_COLUMN_CATEGORY_LEGAL_RIGHTS;
                }

                if (!$onlyGeneric && in_array($colName, ['ekatte', 'ekate', 'nm_lfa_eka', 'imekatte', 'ekatte_'])) {
                    return Config::LAYER_COLUMN_CATEGORY_EKATTE;
                }

                if (!$onlyGeneric && in_array($colName, ['area_type', 'ntp', 'ntp_k'])) {
                    return Config::LAYER_COLUMN_CATEGORY_NTP;
                }

                if (!$onlyGeneric && in_array($colName, ['category'])) {
                    return Config::LAYER_COLUMN_CATEGORY_CATEGORY;
                }

                if (!$onlyGeneric && in_array($colName, ['label'])) {
                    return Config::LAYER_COLUMN_CATEGORY_LABEL;
                }

                if (in_array($colName, ['fill_color', 'border_color'])) {
                    return Config::LAYER_COLUMN_CATEGORY_COLOR;
                }

                if (in_array($colName, ['declared_area_status'])) {
                    return Config::LAYER_COLUMN_CATEGORY_DECLARED_AREA_STATUS;
                }

                return Config::LAYER_COLUMN_CATEGORY_TEXT;
            default:
                return Config::LAYER_COLUMN_CATEGORY_TEXT;
        }
    }
}
