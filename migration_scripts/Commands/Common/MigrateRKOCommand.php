<?php

namespace TF\Commands\Common;

use PDO;
use Prado;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Commands\ParamHelper;

Prado::using('Plugins.Core.UserDb.*');

/**
 * TS-5260 command run on all databases.
 */
class MigrateRKOCommand extends BaseCommand
{
    private $susiMainConnection;

    protected function configure()
    {
        $this
            ->setName('tf:migrate_RKOs')
            ->setDescription('Get biggest rko number from payments and add it in rko_number in su_user_farming (susi_main)')
            ->addArgument(
                'user_databases',
                InputArgument::IS_ARRAY | InputArgument::OPTIONAL,
                'Databases to run the script on (separate multiple names with a space)?'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $user_databases = $input->getArgument('user_databases');
        $paramHelper = new ParamHelper();
        $userDbString = $paramHelper->createQueryParamString($user_databases);

        $this->susiMainConnection = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $userDbConditions = 'true';
        if ($user_databases) {
            $userDbConditions = 'u.database IN (' . $userDbString . ')';
        }

        $sql = $this->susiMainConnection->prepare(
            "SELECT u.id AS id, u.database FROM su_users u
            WHERE 
            {$userDbConditions}
            AND u.level = 2
            ORDER BY database"
        );

        $sql->execute();
        $users = $sql->fetchAll();

        $wrongRKOs = [];
        foreach ($users as $user) {
            $payments = $this->getPayments($user['database']);
            foreach ($payments as $payment) {
                $nextRko = $this->getNextRkoNumber($payment['rko_number']);
                if (!$nextRko) {
                    $wrongRKOs[$user['database']] = [
                        'farming_id' => $payment['farming_id'],
                        'rko_number' => $payment['rko_number'],
                    ];

                    continue;
                }
                $this->saveRKO($payment['farming_id'], $nextRko);
            }
        }

        if ($wrongRKOs) {
            echo "WRONG RKOs:\r\n";
            echo print_r($wrongRKOs, true);
        } else {
            echo "All RKO numbers are migrated successfully!\n\r";
        }
    }

    protected function getNextRkoNumber($rko_number)
    {
        preg_match('/(\D+)?(\d+\z)/', $rko_number, $matches);
        if (!empty($matches)) {
            $nextRKO = $matches[1] . ($matches[2] + 1); // keeps the rko integer as array key for comparison with the next values
        } else {
            return false;
        }

        return $nextRKO;
    }

    private function getPayments($database)
    {
        $pdo = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $database . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
        $sql = $pdo->prepare('select pp.farming_id, pp.rko_number[1] 
                    from (
                        select 
                            farming_id
                            ,array_agg(rko_number order by id desc) as rko_number 
                        from su_payments sp 
                        where 
                            rko_number is not null and 
                            farming_id is not null
                        group by farming_id
                    ) as pp');
        $sql->execute();

        return $sql->fetchAll();
    }

    private function saveRKO($farmingId, $rko)
    {
        $sql = "UPDATE public.su_users_farming SET rko_number = '{$rko}' WHERE  id = " . $farmingId;
        $results = $this->susiMainConnection->prepare($sql);
        $results->execute();
    }
}
