<?php

namespace TF\Commands\Common;

use PDO;

/**
 * command run on all databases.
 */
class RemoveKVSplotsWithoutEKTTECommand extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('tf:remove-kvs-plots-without-ekatte')
            ->setDescription('Remove all kvs plots with ekatte is null and not in su_contracts_plots_rel table');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $viewsSql = $pdo->prepare('select * from layer_kvs lk where ekate is null and gid not in (select plot_id from su_contracts_plots_rel scpr)');
        $viewsSql->execute();
        $plots = $viewsSql->fetchAll(PDO::FETCH_ASSOC);
        if (empty($plots)) {
            $output->writeln($this->userDbName . ': NO PLOTS TO DELETE!');

            return;
        }
        // Log result in file
        $logFile = $this->userDbName . '_remove_invalide_kvs_records';
        parent::logging(date('Y-m-d H:i:s') . PHP_EOL . json_encode($plots), $logFile);

        $sql = $pdo->prepare('DELETE FROM layer_kvs WHERE gid IN (' . implode(',', array_column($plots, 'gid')) . ')');
        $sql->execute();

        $output->writeln($this->userDbName . ': ' . count($plots) . ' PLOTS ARE SUCCESSFULLY DELETED!');
    }
}
