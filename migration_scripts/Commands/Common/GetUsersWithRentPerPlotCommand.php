<?php

namespace TF\Commands\Common;

use PDO;

class GetUsersWithRentPerPlotCommand extends UserDbCommand
{
    private static $collectedData = [];

    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $rentPerPlots = $this->getContractsAndPlotsWithRentPerPlot($pdo);

        if (!empty($rentPerPlots)) {
            foreach ($rentPerPlots as &$rentPerPlot) {
                $rentPerPlot['organization_username'] = $this->organizationUserName;
                $rentPerPlot['organization_name'] = $this->organizationName;
            }

            $output->writeln('In database ' . $this->userDbName . ' has ' . count($rentPerPlots) . ' plots with individual rent' . PHP_EOL);

            static::$collectedData = array_merge(static::$collectedData, $rentPerPlots);
        }
    }

    protected function onCommandEnd($input, $output)
    {
        $this->logging(json_encode(static::$collectedData), 'GetUsersWithRentPerPlot');
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('tf:get_plots_with_individual_rent')
            ->setDescription('Get all plots with individual rent per plot in all user databases');
    }

    /**
     * @param PDO $userDb
     * @param string $tableName
     *
     * @return array|bool
     */
    protected function getContractsAndPlotsWithRentPerPlot($pdo)
    {
        $cmd = $pdo->prepare('
            select
                sc.id, 
                sc.c_num, 
                sc.start_date, 
                sc.due_date, 
                lk.kad_ident, 
                sc.renta as contract_renta, 
                scpr.rent_per_plot as rent_per_plot
            from su_contracts sc 
            left join su_contracts_plots_rel scpr on scpr.contract_id = sc.id 
            left join layer_kvs lk on lk.gid = scpr.plot_id 
            where scpr.rent_per_plot is not null;
        ');
        $cmd->execute();

        return $cmd->fetchAll(PDO::FETCH_ASSOC);
    }
}
