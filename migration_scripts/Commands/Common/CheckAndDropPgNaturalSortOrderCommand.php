<?php

namespace TF\Commands\Common;

use PDO;

/**
 * command run on all databases.
 */
class CheckAndDropPgNaturalSortOrderCommand extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('tf:check-and-drop-natural-sort-order')
            ->setDescription('Check if pg_natural_sort_order exists in user database and drop it if exists.');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $extensionsSql = $pdo->prepare("
            SELECT e.extname AS \"name\", e.extversion AS \"version\", n.nspname AS \"schema\" 
            FROM pg_catalog.pg_extension e 
            LEFT JOIN pg_catalog.pg_namespace n ON n.oid = e.extnamespace 
            LEFT JOIN pg_catalog.pg_description c ON c.objoid = e.oid AND c.classoid = 'pg_catalog.pg_extension'::pg_catalog.regclass 
            WHERE e.extname = 'pg_natural_sort_order';
        ");

        $extensionsSql->execute();
        $pgNaturalSortOrder = $extensionsSql->fetchAll(PDO::FETCH_COLUMN);

        if ($pgNaturalSortOrder) {
            echo 'Drop pg_natural_sort_order' . PHP_EOL;
            $sql = $pdo->prepare('DROP EXTENSION pg_natural_sort_order;');
            $sql->execute();

            $extensionsSql->execute();
            $pgNaturalSortOrderAfterDropped = $extensionsSql->fetchAll(PDO::FETCH_COLUMN);

            if (0 === count($pgNaturalSortOrderAfterDropped)) {
                echo 'SUCCESSFULLY DROPPED  pg_natural_sort_order' . PHP_EOL;
            } else {
                echo 'ERROR DROPPING  pg_natural_sort_order' . PHP_EOL;
            }
        }
    }
}
