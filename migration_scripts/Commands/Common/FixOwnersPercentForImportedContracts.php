<?php

namespace TF\Commands\Common;

use Exception;
use PDO;
use Prado;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Helper\Table;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\Question;
use TF\Commands\ParamHelper;
use UserDbController;

Prado::using('Common.Config');
Prado::using('BaseApp.vendor.pradosoft.prado.framework.Web.Services.TRpcService');
Prado::using('Plugins.Core.UserDb.*');

/**
 * Class FixOwnersPercentForImportedContracts.
 *
 * @property UserDbController $userDbController
 */
class FixOwnersPercentForImportedContracts extends BaseCommand
{
    protected $userDbController;
    protected $server;
    protected $input;
    protected $output;
    protected $userDb;
    protected $outputTable;
    protected $questionHelper;

    protected function configure()
    {
        $this
            ->setName('tf:fix_owners_percent')
            ->setDescription('After importing contracts with the import contract tool there may be cases where the owner\'s owned area can exceed the total area of a plot. This tool fix this case')
            ->addArgument(
                'import_table',
                InputArgument::REQUIRED,
                'Table on which contracts are loaded'
            )
            ->addArgument(
                'user_databases',
                InputArgument::IS_ARRAY | InputArgument::REQUIRED,
                'Databases to run the script on (separate multiple names with a space)?'
            );
    }

    /**
     * @throws Exception
     *
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $user_databases = $input->getArgument('user_databases');
        $import_table = $input->getArgument('import_table');

        $this->input = $input;
        $this->output = $output;

        /*$this->questionHelper = $this->getHelper('question');
        $question = new Question('Please select a limit for this execution: ', 25);
        $limit = $this->questionHelper->ask($input, $output, $question);*/

        $user_databases = (new ParamHelper())->createQueryParamString($user_databases);
        $userDbWhere = 'database IN (' . $user_databases . ')';

        $mainDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = $mainDev->prepare("SELECT id, username, 
        name, database FROM su_users u WHERE {$userDbWhere} AND u.level IN(2,3) ORDER BY database");

        $sql->execute();

        $user_res = $sql->fetchAll(PDO::FETCH_ASSOC);

        if (empty($user_res)) {
            $this->output->writeln('<info>no db founded for user_database: ' . $user_databases . '</info>');

            return 0;
        }
        $database = $user_res[0]['database'];
        $this->userDbController = new UserDbController($database);
        $importTableExists = $this->userDbController->getTableNameExist($import_table);
        // todo: make this as an option
        if (0 == $importTableExists) {
            $this->output->writeln('INFO: no table founded with name : ' . $import_table . ' on db ' . $database);

            return 0;
        }
        $this->userDb = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $database . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
        $owners_contract = $this->selectContractsWithMoreThan100Percent();
        if (empty($owners_contract)) {
            $this->output->writeln('INFO: no records found with sum of owner percentage greater than 100% or between 95% and 100%');

            return 0;
        }
        $this->outputTable = new Table($this->output);
        $this->outputTable->setHeaders(array_keys($owners_contract[0]))->setRows($owners_contract);
        $this->outputTable->render();
        unset($importTableExists, $userDbWhere, $sql, $mainDev, $user_databases, $user_res);
        $owners_contract = $this->prepareData($owners_contract);
        $owners_contract = $this->assignPlotOwnerIds($owners_contract);
        $owners_contract = $this->addContractAreaFromImportTable($owners_contract, $import_table);
        $this->checkAreaKvsAndContractsArea($owners_contract);
        $this->setNewPercentage($owners_contract);
        $this->updatePercentage($owners_contract);
    }

    /**
     * @return array
     */
    protected function selectContractsWithMoreThan100Percent()
    {
        $sql = $this->userDb->prepare("
                    SELECT 
                    DISTINCT(Cont.id, Cont.c_num,gid,kad_ident, pc_rel_id ) AS ids,
                    count(kad_ident) AS plots_count ,
                    array_agg(o.id) AS owner_ids,
                    array_agg(kad_ident) AS kad,
                    array_agg(po.percent) AS percentages,
                    array_agg((CASE WHEN o.owner_type = 1 THEN name || ' ' || surname || ' ' || lastname ELSE company_name END)) AS owner_names,
                    string_agg((CASE WHEN o.owner_type = 1 THEN o.egn ELSE o.eik END), ',') AS egn_eik,
                    SUM(po.percent) AS sum_po_percent,
                    array_agg(DISTINCT(kvs.document_area)) AS kvs_doc_area
                    FROM layer_kvs kvs
                    LEFT JOIN su_contracts_plots_rel pc ON (pc.plot_id = kvs.gid)
                    LEFT JOIN su_contracts Cont ON (Cont. ID = pc.contract_id)
                    LEFT JOIN su_plots_owners_rel po ON (po.pc_rel_id = pc. ID)
                    LEFT JOIN su_owners o ON (o.id = po.owner_id)
                    GROUP BY Cont.c_num ,kad_ident,gid,pc_rel_id,Cont.id      
                    HAVING round((SUM(po.percent))::numeric, 3) > 100 or 
                    (round((SUM(po.percent))::numeric, 3)  > 90 AND round((SUM(po.percent))::numeric, 3)  <= 100 )
                    AND round((SUM(po.percent))::numeric, 3) <> 100
                    ORDER BY count(kad_ident) DESC");
        $sql->execute();

        return $sql->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * @param array $owners_contract
     *
     * @return array
     */
    protected function prepareData(&$owners_contract)
    {
        $toReplace = ['(', ')', '{', '}'];
        foreach ($owners_contract as &$contract_plot) {
            $contract_plot['ids'] = explode(',', str_replace(['(', ')'], '', $contract_plot['ids']));
            $contract_plot['c_id'] = (int)$contract_plot['ids'][0];
            $contract_plot['c_num'] = $contract_plot['ids'][1];
            $contract_plot['gid'] = (int)$contract_plot['ids'][2];
            $contract_plot['kat_ident'] = $contract_plot['ids'][3];
            $contract_plot['pc_rel_id'] = (int)$contract_plot['ids'][4];
            $contract_plot['plots_count'] = (int)$contract_plot['plots_count'];
            $contract_plot['sum_po_percent'] = (int)$contract_plot['sum_po_percent'];
            // $contract_plot['kvs_doc_area'] = (float)str_replace(',', '.', $contract_plot['kvs_doc_area']);
            unset($contract_plot['ids']);
            $contract_plot['kad'] = str_replace($toReplace, '', $contract_plot['kad']);
            $contract_plot['percentages'] = str_replace($toReplace, '', $contract_plot['percentages']);
            $contract_plot['owner_names'] = str_replace(['(', ')', '{', '}', '"'], '', $contract_plot['owner_names']);
            $contract_plot['owner_ids'] = str_replace(['(', ')', '{', '}', '"'], '', $contract_plot['owner_ids']);
            $contract_plot['kvs_doc_area'] = str_replace($toReplace, '', $contract_plot['kvs_doc_area']);

            if (false !== strpos($contract_plot['owner_ids'], ',')) {
                $contract_plot['owner_ids'] = explode(',', $contract_plot['owner_ids']);
            } else {
                $contract_plot['owner_ids'] = [$contract_plot['owner_ids']];
            }

            if (false !== strpos($contract_plot['kad'], ',')) {
                $contract_plot['kad'] = explode(',', $contract_plot['kad']);
            } else {
                $contract_plot['kad'] = [$contract_plot['kad']];
            }
            if (false !== strpos($contract_plot['percentages'], ',')) {
                $contract_plot['percentages'] = explode(',', $contract_plot['percentages']);
            } else {
                $contract_plot['percentages'] = [$contract_plot['percentages']];
            }

            if (false !== strpos($contract_plot['owner_names'], ',')) {
                $contract_plot['owner_names'] = explode(',', $contract_plot['owner_names']);
            } else {
                $contract_plot['owner_names'] = [$contract_plot['owner_names']];
            }
            if (false !== strpos($contract_plot['egn_eik'], ',')) {
                $contract_plot['egn_eik'] = explode(',', $contract_plot['egn_eik']);
            } else {
                $contract_plot['egn_eik'] = [$contract_plot['egn_eik']];
            }
        }

        return $owners_contract;
    }

    /**
     * @param array $owners_contract
     *
     * @return array
     */
    protected function assignPlotOwnerIds(&$owners_contract)
    {
        foreach ($owners_contract as &$contract_plot) {
            for ($i = 0; $i < $contract_plot['plots_count']; $i++) {
                $query = $this->userDb->prepare(
                    "
                select id from su_plots_owners_rel where 
                pc_rel_id = {$contract_plot['pc_rel_id']} and 
                owner_id = {$contract_plot['owner_ids'][$i]}"
                );
                $query->execute();
                $po_rel_data = $query->fetchAll(PDO::FETCH_ASSOC);
                $contract_plot['po_rel_id'][$i] = $po_rel_data[0]['id'];
            }
        }

        return $owners_contract;
    }

    /**
     * @param array $owners_contract
     * @param string $import_table
     *
     * @throws Exception
     *
     * @return array
     */
    protected function addContractAreaFromImportTable(&$owners_contract, $import_table)
    {
        foreach ($owners_contract as $index => &$contract_plot) {
            for ($i = 0; $i < $contract_plot['plots_count']; $i++) {
                $query = $this->userDb->prepare(
                    "
                select * from {$import_table} where 
                c_num = '{$contract_plot['c_num']}' and 
                katident = '{$contract_plot['kat_ident']}' and 
                egn_eik = '{$contract_plot['egn_eik'][$i]}' and 
                owner_name = '{$contract_plot['owner_names'][$i]}'"
                );
                $query->execute();
                $import_data = $query->fetchAll(PDO::FETCH_ASSOC);
                $contracts_found = count($import_data);
                if (1 !== $contracts_found) {
                    $this->output->writeln(
                        'INFO: no contract found with these criteria : '
                        . 'c_num: ' . $contract_plot['c_num'] . ' '
                        . 'katident: ' . $contract_plot['kat_ident'] . ' '
                        . 'egn_eik: ' . $contract_plot['egn_eik'][$i] . ' '
                        . 'owner_name: ' . $contract_plot['owner_names'][$i]
                    );
                    if (1 == $contract_plot['plots_count']) {
                        unset($owners_contract[$index]);
                    }

                    continue;
                }
                $import_data = $import_data[0];
                $contract_plot['contract_area'][$i] = $import_data['contract_area'];
                $contract_plot['document_area'][$i] = $import_data['ducument_area'];
            }
            $contract_plot['contract_area_total'] = array_sum($contract_plot['contract_area']);
            $contract_plot['document_area_total'] = array_sum($contract_plot['document_area']);
        }

        return $owners_contract;
    }

    /**
     * @param array $owners_contract
     *
     * @return array
     */
    protected function checkAreaKvsAndContractsArea(&$owners_contract)
    {
        foreach ($owners_contract as $index => &$contract_plot) {
            if ((string)$contract_plot['contract_area_total'] != $contract_plot['kvs_doc_area']) {
                $this->output->writeln(
                    'WARNING: kvs area differ from the total contract area: '
                    . 'contract_area_total: ' . $contract_plot['contract_area_total'] . ' '
                    . 'contract number: ' . $contract_plot['c_num'] . ' '
                    . 'kat_ident: ' . $contract_plot['kat_ident'] . ' '
                    . 'kvs_doc_area: ' . $contract_plot['kvs_doc_area']
                );
            }
        }

        return $owners_contract;
    }

    protected function setNewPercentage(&$owners_contract)
    {
        foreach ($owners_contract as $index => &$contract_plot) {
            for ($i = 0; $i < $contract_plot['plots_count']; $i++) {
                $contract_plot['new_percent'][$i] = ($contract_plot['contract_area'][$i] * 100) / $contract_plot['contract_area_total'];
                /*                $contract_plot['fraction'][$i] = $this->dec2frac($contract_plot['new_percent'][$i]);
                                $contract_plot['denominator'][$i] = $contract_plot['fraction'][$i]['den'];
                                $contract_plot['numerator'][$i]   = $contract_plot['fraction'][$i]['num'];*/
            }
            $contract_plot['new_percent_sum'] = array_sum($contract_plot['new_percent']);
        }

        return $owners_contract;
    }

    // update contract_area and area_for_rent in su_contract_plot_rel
    protected function updatePercentage(&$owners_contract)
    {
        foreach ($owners_contract as $index => $contract_plot) {
            for ($i = 0; $i < $contract_plot['plots_count']; $i++) {
                $query1 = $this->userDb->prepare(
                    " UPDATE su_plots_owners_rel SET 
                percent = {$contract_plot['new_percent'][$i]} 
                WHERE id = {$contract_plot['po_rel_id'][$i]}
                "
                );
                $query1->execute();

                $query2 = $this->userDb->prepare(
                    " UPDATE su_contracts_plots_rel SET 
                contract_area = {$contract_plot['contract_area_total']} 
                area_for_rent = {$contract_plot['contract_area_total']} 
                WHERE contract_id = {$contract_plot['c_id']} AND 
                plot_id = {$contract_plot['gid']}
                "
                );
                $query2->execute();
            }
        }

        return $owners_contract;
    }

    /**
     * @return array|int|string
     */
    private function dec2frac($decimal)
    {
        $decimal = (string)$decimal;
        $num = '';
        $den = 1;
        $dec = false;

        // find least reduced fractional form of number
        for ($i = 0, $ix = strlen($decimal); $i < $ix; $i++) {
            // build the denominator as we 'shift' the decimal to the right
            if ($dec) {
                $den *= 10;
            }

            // find the decimal place/ build the numerator
            if ('.' == $decimal[$i]) {
                $dec = true;
            } else {
                $num .= $decimal[$i];
            }
        }
        $num = (int)$num;

        // whole number, just return it
        if (1 == $den) {
            return $num;
        }

        $num2 = $num;
        $den2 = $den;
        $rem = 1;
        // Euclid's Algorithm (to find the gcd)
        while ($num2 % $den2) {
            $rem = $num2 % $den2;
            $num2 = $den2;
            $den2 = $rem;
        }
        if ($den2 != $den) {
            $rem = $den2;
        }

        // now $rem holds the gcd of the numerator and denominator of our fraction
        return ['num' => ($num / $rem), 'den' => ($den / $rem)];
    }
}
