<?php

namespace TF\Commands\Common;

use PDO;
use Symfony\Component\Console\Input\InputOption;

/**
 * init_payroll command run on all databases.
 */
class UpdateAllowableAreaInKVS extends UserDbCommand
{
    private $regions = [
        'blagoevgrad',
        'burgas',
        'dobrich',
        'gabrovo',
        'haskovo',
        'kardzhali',
        'kyustendil',
        'lovech',
        'montana',
        'pazardzhik',
        'pernik',
        'pleven',
        'plovdiv',
        'razgrad',
        'ruse',
        'shumen',
        'silistra',
        'sliven',
        'smolyan',
        'sofia',
        'sofiagrad',
        'starazagora',
        'targovishte',
        'varna',
        'velikotarnovo',
        'vidin',
        'vratsa',
        'yambol',
    ];

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('tf:update_allowable_area')
            ->setDescription('Update allowable area in kvs. Usually used after new allowable layer is loaded.')
            ->addOption('regions', '-r', InputOption::VALUE_OPTIONAL, 'If set true, the script will be execute by region kvs tables instead of layer_kvs');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $output->writeln('Start updating allowable_area.');
        $this->updateAllowableArea($pdo, $output);
        $output->writeln('End updating allowable_area.');
        $output->writeln('Start updating allowable area on the plots out of allowable layer.');
        $this->updateAllowableAreaOnOuterPlots($pdo, $input, $output);
        $output->writeln('End updating allowable area on the plots out of allowable layer.');
        $output->writeln('Start refreshing mat views.');
        $this->refreshAllowableFromIsakMatViews($pdo, $output);
        $output->writeln('End refreshing mat views.');
    }

    private function updateAllowableArea(PDO $pdo, $output)
    {
        $ekattesSql = $pdo->prepare('SELECT kvs.ekate FROM layer_kvs kvs GROUP BY kvs.ekate;');
        $ekattesSql->execute();
        $ekattes = $ekattesSql->fetchAll();
        $countEkattes = count($ekattes);

        $output->writeln('Found: ' . $countEkattes . ' EKATTEs');

        foreach ($ekattes as $key => $ekatte) {
            $query = "
                UPDATE layer_kvs SET allowable_area= allowable_intersection.allowable_area, allowable_type=allowable_intersection.allowable_type
                FROM (
                        SELECT
                            kvs.gid,
                            string_agg (DISTINCT(A .ntp) :: TEXT, ', ') AS allowable_type,
                            round(
                                (
                                    st_area (
                                        safe_intersection (ST_SetSRID(kvs.geom, 32635), st_union(A .geom, 0.0001))
                                    ) / 1000
                                ) :: NUMERIC,
                                3
                            ) AS allowable_area
                        FROM
                            layer_kvs kvs,
                            dblink (
                                'host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "' :: TEXT,
                                'SELECT
                                    geom,
                                    ntp
                                FROM
                                    layer_allowable_final where geom && ST_GeomFromText(''' || (
                                    SELECT
                                        st_astext (
                                            st_envelope (st_extent(ST_SetSRID(geom, 32635)))
                                        )
                                    FROM
                                        layer_kvs
                                    WHERE ekate = '" . $ekatte['ekate'] . "'
                                ) || ''');'
                            ) AS A (geom geometry, ntp VARCHAR)
                        WHERE
                            kvs.ekate = '" . $ekatte['ekate'] . "'
                            AND st_intersects (ST_SetSRID(kvs.geom, 32635), A .geom)
                        GROUP BY
                            kvs.gid
                ) allowable_intersection WHERE layer_kvs.gid = allowable_intersection.gid;";
            $sql = $pdo->prepare($query);
            $sql->execute();

            $output->writeln('Processed: ' . $ekatte['ekate'] . ' (' . ($key + 1) . ' / ' . $countEkattes . ')');
        }
    }

    private function updateAllowableAreaOnOuterPlots(PDO $pdo, $input, $output)
    {
        if ($input->getOption('regions') && 'true' === $input->getOption('regions')) {
            $currentCount = 1;
            $allRegionsCount = count($this->regions);
            foreach ($this->regions as $region) {
                echo 'Region: ' . $region . ' (' . $currentCount++ . '/' . $allRegionsCount . ")\r\n";
                $this->createGetOutterPlotsPostgreFunction($pdo, $region, $output);
                $sql = $pdo->prepare('UPDATE ' . $region . ' as kvs SET allowable_area = 0.000 from tf_get_plots_out_from_allowable_layer() as outer_plots where kvs.gid = outer_plots.gid;');

                $isSuccess = $sql->execute();

                if (!$isSuccess) {
                    $output->writeln('====== Error Info:<pre>' . print_r($sql->errorInfo(), true));
                    exit;
                }
            }
        } else {
            $this->createGetOutterPlotsPostgreFunction($pdo, 'layer_kvs', $output);
            $ekattesSql = $pdo->prepare('SELECT kvs.ekate FROM layer_kvs kvs GROUP BY kvs.ekate;');
            $ekattesSql->execute();
            $ekattes = $ekattesSql->fetchAll();
            $countEkattes = count($ekattes);

            foreach ($ekattes as $key => $ekatte) {
                if (!empty($ekatte['ekate'])) {
                    $sql = $pdo->prepare("UPDATE layer_kvs SET allowable_area = 0.000 from tf_get_plots_out_from_allowable_layer('" . $ekatte['ekate'] . "') as outer_plots where layer_kvs.gid = outer_plots.gid;");
                    $isSuccess = $sql->execute();
                    if (!$isSuccess) {
                        $output->writeln('====== Error Info:<pre>' . print_r($sql->errorInfo(), true));
                        exit;
                    }

                    $output->writeln('Processed: ' . $ekatte['ekate'] . ' (' . ($key + 1) . ' / ' . $countEkattes . ')');
                }
            }
        }
    }

    private function refreshAllowableFromIsakMatViews(PDO $pdo, $output)
    {
        $viewsSql = $pdo->prepare("SELECT matviewname FROM pg_matviews WHERE matviewname ~ '(allowable_from_isak_)[0-9]+$'");
        $viewsSql->execute();
        $views = $viewsSql->fetchAll();

        foreach ($views as $view) {
            $query = 'REFRESH MATERIALIZED VIEW ' . $view['matviewname'] . ';';
            $sql = $pdo->prepare($query);
            $isSuccess = $sql->execute();
            if (!$isSuccess) {
                $output->writeln('====== Error Info:<pre>' . print_r($sql->errorInfo(), true));
                exit;
            }

            $output->writeln('====== QUERY: ' . $query);
        }
    }

    private function createGetOutterPlotsPostgreFunction(PDO $pdo, $region = 'layer_kvs', $output)
    {
        $pdo->prepare('DROP FUNCTION IF EXISTS tf_get_plots_out_from_allowable_layer;')->execute();

        $createFunctionSql = 'CREATE OR REPLACE FUNCTION public.tf_get_plots_out_from_allowable_layer(_ekate text)
                 RETURNS TABLE(gid integer, kad_ident character varying)
                 LANGUAGE plpgsql
                AS $function$
                    DECLARE
                        reg record;
                    BEGIN
                        FOR reg IN SELECT kvs.ekate FROM public.' . $region . ' kvs WHERE kvs.ekate = _ekate group by ekate
                           loop
                                RETURN QUERY SELECT
                                    kvs.gid, kvs.kad_ident
                                FROM public.' . $region . " kvs
                                inner join dblink (
                                        'host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "'::TEXT,
                                        'SELECT
                                            st_union(ST_MakeValid(geom), 0.0001)
                                        FROM
                                            layer_allowable_final
                                        WHERE geom && ST_GeomFromText(''' || (
                                            SELECT
                                                st_astext (
                                                    st_envelope (st_extent(geom))
                                                )
                                            FROM
                                                " . $region . " where ekate = reg.ekate
                                        ) || ''');'
                                    ) AS A (geom geometry) on not st_intersects(kvs.geom, a.geom)
                                    where kvs.ekate = reg.ekate;
                            END LOOP;
                    END;
                \$function$
        ;";

        $sql = $pdo->prepare($createFunctionSql);
        $isSuccess = $sql->execute();

        if (!$isSuccess) {
            $output->writeln('====== Error Info:<pre>' . print_r($sql->errorInfo(), true));
            exit;
        }
    }
}
