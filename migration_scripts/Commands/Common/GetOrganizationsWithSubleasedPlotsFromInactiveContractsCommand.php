<?php

namespace TF\Commands\Common;

use PDO;

class GetOrganizationsWithSubleasedPlotsFromInactiveContractsCommand extends UserDbCommand
{
    private static $tmpTableName = 'tmp_subleases_from_inactive_contracts';

    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $subleasesFromInactiveContracts = $this->getSubleasesFromInactiveContracts($pdo);

        if (!empty($subleasesFromInactiveContracts)) {
            $sql = $this->mainConnection->prepare('
                    INSERT INTO ' . self::$tmpTableName . ' (database, organization_user, organization_name, sublease_id, sublease_c_num, sublease_start_date, sublease_due_date, kad_ident, contract_id, contract_c_num, start_date, due_date)
                    VALUES (:database, :organization_user, :organization_name, :sublease_id, :sublease_c_num, :sublease_start_date, :sublease_due_date, :kad_ident, :contract_id, :contract_c_num, :start_date, :due_date);
                ');

            foreach ($subleasesFromInactiveContracts as $subleasesRecord) {
                $sql->execute([
                    'database' => $this->userDbName,
                    'organization_user' => $this->organizationUserName,
                    'organization_name' => $this->organizationName,
                    'sublease_id' => $subleasesRecord['sublease_id'],
                    'sublease_c_num' => $subleasesRecord['sublease_c_num'],
                    'sublease_start_date' => $subleasesRecord['sublease_start_date'],
                    'sublease_due_date' => $subleasesRecord['sublease_due_date'],
                    'kad_ident' => $subleasesRecord['kad_ident'],
                    'contract_id' => $subleasesRecord['contract_id'],
                    'contract_c_num' => $subleasesRecord['contract_c_num'],
                    'start_date' => $subleasesRecord['start_date'],
                    'due_date' => $subleasesRecord['due_date'],
                ]);
            }

            $output->writeln('In database ' . $this->userDbName . ' has ' . count($subleasesFromInactiveContracts) . ' subleases from inactive contracts' . PHP_EOL);
        } else {
            $output->writeln('In database ' . $this->userDbName . ' has no contracts with duplicated renta_nat' . PHP_EOL);
        }
    }

    public function getSubleasesFromInactiveContracts($pdo)
    {
        $cmd = $pdo->prepare('
            SELECT c.id AS sublease_id,
                c.c_num AS sublease_c_num,
                c.start_date::date AS sublease_start_date,
                c.due_date::date AS sublease_due_date,
                cpr.plot_id,
                c.active,
                c2.id AS contract_id,
                c2.c_num AS contract_c_num,
                c2.start_date AS start_date,
                c2.due_date AS due_date,
                layer_kvs.kad_ident
            FROM su_contracts c
                LEFT JOIN su_subleases_plots_contracts_rel scpr ON scpr.sublease_id = c.id
                LEFT JOIN su_contracts_plots_rel cpr ON cpr.id = scpr.pc_rel_id
                LEFT JOIN su_contracts c2 ON c2.id = cpr.contract_id
                LEFT JOIN layer_kvs on layer_kvs.gid = cpr.plot_id
            WHERE 
                true 
                AND
                    c.is_sublease = true
                AND
                    c.active = true
                AND
                    c.due_date > now()::date             
                AND
                    c2.active = false
            GROUP BY 
                c.id, cpr.plot_id, c2.id,layer_kvs.kad_ident
            ');

        $cmd->execute();

        return $cmd->fetchAll(PDO::FETCH_ASSOC);
    }

    protected function onCommandStart($input, $output)
    {
        $sql = $this->mainConnection->prepare('
            CREATE TABLE IF NOT EXISTS ' . self::$tmpTableName . ' (
                        id serial4 NOT NULL,
                        "database" varchar NULL,
                        organization_user varchar NOT NULL,
                        organization_name varchar NOT NULL,
                        sublease_id int4 NOT NULL,
                        sublease_c_num varchar NOT  NULL,
                        sublease_start_date timestamp NULL,
                        sublease_due_date timestamp NULL,
                        kad_ident varchar NOT NULL,
                        contract_id int4 NOT NULL,
                        contract_c_num varchar NOT NULL,
                        start_date timestamp NULL,
                        due_date timestamp NULL
                    );
        ');
        $sql->execute();

        $sql = $this->mainConnection->prepare('
            TRUNCATE ' . self::$tmpTableName . ';
        ');

        $sql->execute();
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('tf:get_organizations_with_subleased_plots_from_inactive_contracts')
            ->setDescription('Get all contracts with duplicated renta_nat in all user databases and log the result in temp table ' . self::$tmpTableName . ' in susi_main');
    }
}
