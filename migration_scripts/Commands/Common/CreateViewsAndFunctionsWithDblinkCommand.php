<?php

namespace TF\Commands\Common;

use Exception;
use PDO;

/**
 * command run on all databases.
 */
class CreateViewsAndFunctionsWithDblinkCommand extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('tf:create-views-and-functions-with-dblink')
            ->setDescription('Create ekatte_combobox and osz_ekatte_combobox materialized views');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        try {
            $ekatteComboBoxSql = $pdo->prepare("
            CREATE MATERIALIZED VIEW public.ekate_combobox
            TABLESPACE pg_default
            AS SELECT kvs.ekate,
                e.ekatte_name,
                replace(regexp_replace(st_extent(kvs.geom)::text, '([BOX()])'::text, ''::text, 'g'::text), ' '::text, ','::text) AS extent
               FROM layer_kvs kvs
                 LEFT JOIN dblink('host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "'::text, 'SELECT ekatte_name, ekatte_code FROM su_ekatte WHERE true'::text) e(ekatte_name character varying, ekatte_code character varying) ON e.ekatte_code::text = kvs.ekate::text
               WHERE kvs.ekate is not null
              GROUP BY kvs.ekate, e.ekatte_name
            WITH DATA;
        ");
            $ekatteComboBoxSql->execute();

            $oszEkatteComboBoxSql = $pdo->prepare("
            CREATE MATERIALIZED VIEW public.osz_ekatte_combobox
            TABLESPACE pg_default
            AS SELECT DISTINCT kvs.ekatte,
                e.ekatte_name
               FROM su_osz_files_plots kvs
                 LEFT JOIN dblink('host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "'::text, 'SELECT ekatte_name, ekatte_code FROM su_ekatte WHERE true'::text) e(ekatte_name character varying, ekatte_code character varying) ON e.ekatte_code::text = kvs.ekatte::text
            WITH DATA;
        ");
            $oszEkatteComboBoxSql->execute();

            $plotsOutFromAllowableLayerFuncSql = $pdo->prepare("CREATE OR REPLACE FUNCTION public.tf_get_plots_out_from_allowable_layer(_ekate text)
                 RETURNS TABLE(gid integer, kad_ident character varying)
                 LANGUAGE plpgsql
                AS \$function$
                    DECLARE
                        reg record;
                    BEGIN
                        FOR reg IN SELECT kvs.ekate FROM public.layer_kvs kvs WHERE kvs.ekate = _ekate group by ekate
                           loop
                                RETURN QUERY SELECT
                                    kvs.gid, kvs.kad_ident
                                FROM public.layer_kvs kvs
                                inner join dblink (
                                        'host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "'::TEXT,
                                        'SELECT
                                            st_union(geom)
                                        FROM
                                            layer_allowable_final
                                        WHERE geom && ST_GeomFromText(''' || (
                                            SELECT
                                                st_astext (
                                                    st_envelope (st_extent(geom))
                                                )
                                            FROM
                                                layer_kvs where ekate = reg.ekate
                                        ) || ''');'
                                    ) AS A (geom geometry) on not st_intersects(kvs.geom, a.geom)
                                    where kvs.ekate = reg.ekate;
                            END LOOP;
                    END;
                \$function$
            ;");

            $plotsOutFromAllowableLayerFuncSql->execute();
        } catch (Exception $e) {
            $output->error("Can't create  EKATTE COMBOBOX MATERIALIZED VIEWS for: " . $this->userDbName . ' Details: ' . $e->getMessage());
        }

        echo 'ekate_combobox - SUCCESSFULLY CREATED!' . PHP_EOL;
        echo 'osz_ekatte_combobox - SUCCESSFULLY CREATED!' . PHP_EOL;
    }
}
