<?php

namespace TF\Commands\Common;

use PDO;

class GetUsersWithDuplicatedRentaNatCommand extends UserDbCommand
{
    private static $tmpTableName = 'tmp_contracts_with_duplicated_renta_nat';

    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $contractsWithDuplicatedRents = $this->getContractsWithDuplicatedRentaNats($pdo);

        if (!empty($contractsWithDuplicatedRents)) {
            $sql = $this->mainConnection->prepare('
                    INSERT INTO ' . self::$tmpTableName . ' (database, contract_id, c_num, farm_name, c_date, start_date, due_date, rents)
                    VALUES (:database, :contract_id, :c_num, :farm_name, :c_date, :start_date, :due_date, :rents);
                ');

            foreach ($contractsWithDuplicatedRents as $contractsWithDuplicatedRentsRecord) {
                $sql->execute([
                    'database' => $this->userDbName,
                    'contract_id' => $contractsWithDuplicatedRentsRecord['contract_id'],
                    'c_num' => $contractsWithDuplicatedRentsRecord['c_num'],
                    'farm_name' => $contractsWithDuplicatedRentsRecord['farming_name'],
                    'c_date' => $contractsWithDuplicatedRentsRecord['c_date'],
                    'start_date' => $contractsWithDuplicatedRentsRecord['start_date'],
                    'due_date' => $contractsWithDuplicatedRentsRecord['due_date'],
                    'rents' => $contractsWithDuplicatedRentsRecord['jsonb_agg'],
                ]);
            }

            $output->writeln('In database ' . $this->userDbName . ' has ' . count($contractsWithDuplicatedRents) . ' contracts with duplicated renta_nat' . PHP_EOL);
        } else {
            $output->writeln('In database ' . $this->userDbName . ' has no contracts with duplicated renta_nat' . PHP_EOL);
        }
    }

    protected function onCommandStart($input, $output)
    {
        $sql = $this->mainConnection->prepare('
            CREATE TABLE IF NOT EXISTS ' . self::$tmpTableName . ' (
                        id serial4 NOT NULL,
                        "database" varchar NULL,
                        contract_id int4 NOT NULL,
                        c_num varchar NULL,
                        farm_name varchar NULL,
                        c_date timestamp NULL,
                        start_date timestamp NULL,
                        due_date timestamp NULL,
                        rents jsonb NULL
                    );
        ');
        $sql->execute();

        $sql = $this->mainConnection->prepare('
            TRUNCATE ' . self::$tmpTableName . ';
        ');
        $sql->execute();
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('tf:get_contracts_with_duplicated_renta_nat')
            ->setDescription('Get all contracts with duplicated renta_nat in all user databases and log the result in temp table ' . self::$tmpTableName . ' in susi_main');
    }

    /**
     * @param PDO $userDb
     * @param string $tableName
     *
     * @return array|bool
     */
    protected function getContractsWithDuplicatedRentaNats($pdo)
    {
        $cmd = $pdo->prepare('
            select 
                contract_id,
                c.c_num,
                c.c_date,
                c.start_date,
                c.due_date,
                c.farming_name,
                jsonb_agg(srt."name") 
            from su_contracts_rents scr
            left join su_contracts c on c.id = scr.contract_id
            left join su_renta_types srt on srt.id = scr.renta_id 
            group by contract_id, c.id, scr.renta_id
            having count(renta_id) > 1;
        ');
        $cmd->execute();

        return $cmd->fetchAll(PDO::FETCH_ASSOC);
    }
}
