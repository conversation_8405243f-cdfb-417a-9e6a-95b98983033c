<?php

namespace TF\Commands\Common;

/**
 * UpdateFrom2015To2016 command run on all databases.
 */
class UpdateFrom2015To2016Command extends UserDbCommand
{
    protected $skipLogging = true;

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('tf:update-from-2015-to-2016')
            ->setDescription('Update na user database ot 2015 do 2016');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $commandsArr = [
            new \TF\Commands\sprint_s11\TS1916Command(),
            new \TF\Commands\sprint_s11\TS1938Command(),
            new \TF\Commands\sprint_s11\TS1939Command(),
            new \TF\Commands\sprint_s11\TS1949Command(),
            new \TF\Commands\sprint_s11\TS2034Command(),
            new \TF\Commands\sprint_s12\TS11741Command(),
            new \TF\Commands\sprint_s12\TS1174Command(),
            new \TF\Commands\sprint_s12\TS1911Command(),
            new \TF\Commands\sprint_s12\TS2088Command(),
            new \TF\Commands\sprint_s12\TS2091Command(),
            new \TF\Commands\sprint_s12\TS2107Command(),
            new \TF\Commands\sprint_s12\TS2188Command(),
            new \TF\Commands\sprint_s12\TS2231Command(),
            new \TF\Commands\sprint_s12\TS2276Command(),
            new \TF\Commands\sprint_s12\TS2308Command(),
            new \TF\Commands\sprint_s13\TS2242Command(),
            new \TF\Commands\sprint_s13\TS22421Command(),
            new \TF\Commands\sprint_s13\TS2378Command(),
            new \TF\Commands\sprint_s13\TS2431Command(),
            new \TF\Commands\sprint_s14\UpdateToSprint14(),
            new \TF\Commands\sprint_s14\TS2588Command(),
            new \TF\Commands\sprint_s14\TS2603Command(),
            new \TF\Commands\sprint_s13\TS23311Command(),
            new \TF\Commands\sprint_s13\TS2331Command(),
            new \TF\Commands\sprint_s15\UpdateToSprint15(),
        ];

        for ($i = 0; $i < count($commandsArr); $i++) {
            $command = $commandsArr[$i];
            $output->writeln('Executing command: ' . $command->getName());
            $this->logScript($userDb, $command->getName());
            $command->onDbExecute($userDb, $output, $input);
        }
    }

    protected function logScript($user_db, $name)
    {
        $date = date('Y-m-d H:i:s');
        $this->openConnection();
        $sql = $this->mainConnection->prepare("
            INSERT INTO scripts_log (db_name, name, date)
            VALUES ('{$user_db}', '{$name}', '{$date}')
        ");

        return $sql->execute();
    }
}
