<?php

namespace TF\Commands\Common;

use PDO;
use TF\Application\Entity\UserLayers;
use TF\Engine\Plugins\Core\UserDb\UserDbModel;

class CreateMissingLayerColumnsByDefinitionsCommand extends UserDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $layersCmd = $this->mainConnection->prepare('
            SELECT
                 sul.*
            FROM
                su_users_layers sul
            JOIN su_users su 
                ON su.group_id = sul.user_id
            WHERE 
                su.database = :database
                and table_name not in (:remoteTables) 
        ');
        $layersCmd->execute([
            ':database' => $this->userDbName,
            ':remoteTables' => "'" . implode("', '", $GLOBALS['Layers']['remoteTables']) . "'",
        ]);
        $layers = $layersCmd->fetchAll(PDO::FETCH_CLASS, UserLayers::class);
        $userDbModel = new UserDbModel($this->userDbName);

        $updatedTablesCount = 0;
        foreach ($layers as $layer) {
            $tableExists = $userDbModel->getTableNameExist($this->userDbName, $layer->table_name);

            if (!$tableExists) {
                $output->warn("Table {$layer->table_name} does not exist... Skipping");

                continue;
            }

            $output->writeln("Processing layer: {$layer->table_name}");
            $createdColumns = $userDbModel->createLayerMissingColumns($layer);

            if (count($createdColumns) > 0) {
                $output->info('Created columns: \'' . implode('\', \'', $createdColumns) . '\'');
                $updatedTablesCount++;
            } else {
                $output->writeln('No missing columns');
            }
        }

        $output->writeln('===========================');
        $output->writeln('Updated tables count: ' . $updatedTablesCount);
        $output->writeln('===========================');
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('tf:add_missing_layers_columns_by_definitions')
            ->setDescription('Create missing columns in layer physical tables by definitions');
    }
}
