<?php

namespace TF\Commands\Common;

use PDO;
use UserDbModel;

/**
 * command run on all databases.
 */
class RecreateMatViewsUsedDbLinkCommand extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('tf:refresh-math-views-with-dblink')
            ->setDescription('Recreate all materialized views which used dblink');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $userDbModel = new UserDbModel($userDb);
        $pdo = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $viewsSql = $pdo->prepare("SELECT matviewname, substring(matviewname, '[0-9]+') matvew_id FROM pg_matviews WHERE matviewname ~ '(allowable_from_isak_|pzp_for_isak_|sepp_for_isak_)[0-9]+$'");

        $viewsSql->execute();
        $views = $viewsSql->fetchAll();
        foreach ($views as $view) {
            if (in_array($view['matviewname'], ['ekate_combobox', 'osz_ekatte_combobox'])) {
                continue;
            }
            echo '=====>>> View ' . $view['matviewname'] . ":\r\n";
            switch ($view['matviewname']) {
                case (strpos($view['matviewname'], 'allowable_from_isak_')) : {
                    $userDbModel->createAllowableFromIsakReportView($view['matvew_id'], $userDb);

                    break;
                }
                case (strpos($view['matviewname'], 'pzp_for_isak_')) : {
                    $userDbModel->createPZPReportView($view['matvew_id'], $userDb);

                    break;
                }
                case (strpos($view['matviewname'], 'sepp_for_isak_')) : {
                    $userDbModel->createSEPPReportView($view['matvew_id'], $userDb);

                    break;
                }
                default: {
                    $output->info("The materialized {$view['matviewname']} view is not one of: allowable_from_isak_ , pzp_for_isak_ , sepp_for_isak_ .");

                    continue 2;
                }
            }

            echo "Recreating successfully!\r\n";
            echo "Refreshing...\r\n";
            $userDbModel->refreshView($view['matviewname']);
            echo "Done!\r\n";
        }
    }
}
