<?php

namespace TF\Commands\Common;

use Exception;
use Prado;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Engine\Kernel\ExportToExcelClass;
use UserDbPlotsModel;
use UsersController;

Prado::using('Plugins.Core.UserDb.*');
Prado::using('Plugins.Core.Layers.*');
Prado::using('APIClasses.Contr.UsedPlotsReportGrid');
Prado::using('APIClasses.Common.FarmingCombobox');
Prado::using('APIClasses.Common.EkateCombobox');
Prado::using('APIClasses.Common.Config');

class GetUsersEkattesCommand extends UserDbCommand
{
    protected $skipLogging = true;
    protected $pradoApp;
    protected $headers = [
        'ID',
        'Потребител',
        'Общо',
        'ЕКАТТЕ',
    ];
    protected $headersMerged = [
        'ЕКАТТЕ',
    ];

    private $accountInfoArr = [];

    public function setPradoApp($pradoApp)
    {
        $this->pradoApp = $pradoApp;
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('tf:users_ekattes_export')
            ->setDescription('Generates an Excel file with information for every account about EKATTEs.')
            ->addOption('file_name', null, InputOption::VALUE_OPTIONAL, 'The name of generated file.', 'accounts_ekattes_report.xlsx')
            ->addOption('merged', 'm', InputOption::VALUE_NONE, 'If set, the script will generate file with merged EKATTETs of all selected users');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $UsersController = new UsersController('Users');
        $auth = $this->pradoApp->getModule('auth');

        try {
            $user = $this->getUser($UsersController, $userDb);
            $auth->switchUser($user['username']);

            $ekattets = $this->getUserEkattets($userDb);
            $this->accountInfoArr[] = [
                $user['id'],
                $user['username'],
                count($ekattets),
                (string)implode(',', $ekattets),
            ];
        } catch (Exception $e) {
            $output->write($e->getMessage());
        }
    }

    protected function onCommandEnd(InputInterface $input, OutputInterface $output)
    {
        $result = [];
        $fileName = $input->getOption('file_name');
        $exportExcelDoc = new ExportToExcelClass();

        if (!empty($input->getOption('merged'))) {
            $uniquesEkattes = [];
            foreach ($this->accountInfoArr as $user) {
                $ekattes = explode(',', $user[3]);
                foreach ($ekattes as $ekatte) {
                    if (!in_array($ekatte, $uniquesEkattes) && !empty($ekatte)) {
                        $uniquesEkattes[] = $ekatte;
                    }
                }
            }
            foreach ($uniquesEkattes as $ekatte) {
                $result[] = [$ekatte];
            }

            $headers = $this->headersMerged;
        } else {
            $result = $this->accountInfoArr;
            $headers = $this->headers;
        }
        $exportExcelDoc->export($result, $headers);
        $exportExcelDoc->saveFile($fileName);
        $output->info("Generated file: {$fileName}");
    }

    protected function getUser($UsersController, $userDb)
    {
        $users = $UsersController->getUsers([
            'return' => ['id', 'username', 'name', 'paid_support'],
            'where' => [
                'database' => ['column' => 'database', 'compare' => '=', 'value' => $userDb],
                'level' => ['column' => 'level', 'compare' => '=', 'value' => 2],
            ],
        ]);
        if (0 === count($users)) {
            return;
        }

        return $users[0];
    }

    protected function getUserEkattets($userDb)
    {
        $usedPlotsReportGrid = new UserDbPlotsModel($userDb);

        $options = [
            'return' => ['ekate'],
            'tablename' => 'layer_kvs',
            'where' => [
                'ekateNull' => ['column' => 'ekate', 'compare' => 'IS NOT', 'value' => 'NULL'],
            ],
            'group' => 'ekate',
        ];

        $ekate = $usedPlotsReportGrid->getPlotsArea($options, false, false);

        return array_map(function ($element) {
            return $element['ekate'];
        }, $ekate);
    }
}
