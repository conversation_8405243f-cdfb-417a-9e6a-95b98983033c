<?php

namespace TF\Commands\Common;

use PDO;

class GenerateStatisticOfUsingOwnersFieldsCommand extends UserDbCommand
{
    private static $tmpTableName = 'tmp_statisic_of_using_owners_fields';

    public function configure()
    {
        parent::configure();
        $this
            ->setName('tf:generate_statistic_of_using_owners_fields')
            ->setDescription('Generate statistics of using owners fields and publish the results in table ' . self::$tmpTableName . ' in susi_main');
    }

    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $ownersFieldsUsing = [
            'id' => 0,
            'name' => 0,
            'surname' => 0,
            'lastname' => 0,
            'egn' => 0,
            'lk_nomer' => 0,
            'lk_izdavane' => 0,
            'company_name' => 0,
            'eik' => 0,
            'phone' => 0,
            'fax' => 0,
            'mobile' => 0,
            'email' => 0,
            'address' => 0,
            'mol' => 0,
            'company_address' => 0,
            'iban' => 0,
            'rent_place' => 0,
            'remark' => 0,
            'prepiska' => 0,
            'bic' => 0,
            'bank_name' => 0,
            'dead_date' => 0,
            'post_payment_fields' => 0,
            'country' => 0,
            'birthday' => 0,
        ];
        $owners = $this->getOwners($pdo);
        foreach ($owners as $owner) {
            foreach ($owner as $field => $value) {
                if ('post_payment_fields' == $field) {
                    if ('[]' == $value) {
                        continue;
                    }
                    $postFieldsArr = json_decode($value, true);
                    foreach ($postFieldsArr as $postField) {
                        if (!empty($postField)) {
                            $ownersFieldsUsing[$field]++;

                            break;
                        }
                    }
                } else {
                    if (!empty($owner[$field])) {
                        $ownersFieldsUsing[$field]++;
                    }
                }
            }
        }

        if (!empty($owners)) {
            $sql = $this->mainConnection->prepare('
                    INSERT INTO ' . self::$tmpTableName . ' (
                        database,
                        users_count,
                        "name",
                        surname,
                        lastname,
                        egn,
                        lk_nomer,
                        lk_izdavane,
                        company_name,
                        eik,
                        phone,
                        fax,
                        mobile,
                        email,
                        address,
                        mol,
                        company_address,
                        iban,
                        rent_place,
                        remark,
                        prepiska,
                        bic,
                        bank_name,
                        dead_date,
                        post_payment_fields,
                        country,
                        birthday
                    )
                    VALUES (
                        :database,
                        :users_count,
                        :name,
                        :surname,
                        :lastname,
                        :egn,
                        :lk_nomer,
                        :lk_izdavane,
                        :company_name,
                        :eik,
                        :phone,
                        :fax,
                        :mobile,
                        :email,
                        :address,
                        :mol,
                        :company_address,
                        :iban,
                        :rent_place,
                        :remark,
                        :prepiska,
                        :bic,
                        :bank_name,
                        :dead_date,
                        :post_payment_fields,
                        :country,
                        :birthday
                    );
                ');

            $sql->execute([
                'database' => $this->userDbName,
                'users_count' => $ownersFieldsUsing['id'],
                'name' => $ownersFieldsUsing['name'],
                'surname' => $ownersFieldsUsing['surname'],
                'lastname' => $ownersFieldsUsing['lastname'],
                'egn' => $ownersFieldsUsing['egn'],
                'lk_nomer' => $ownersFieldsUsing['lk_nomer'],
                'lk_izdavane' => $ownersFieldsUsing['lk_izdavane'],
                'company_name' => $ownersFieldsUsing['company_name'],
                'eik' => $ownersFieldsUsing['eik'],
                'phone' => $ownersFieldsUsing['phone'],
                'fax' => $ownersFieldsUsing['fax'],
                'mobile' => $ownersFieldsUsing['mobile'],
                'email' => $ownersFieldsUsing['email'],
                'address' => $ownersFieldsUsing['address'],
                'mol' => $ownersFieldsUsing['mol'],
                'company_address' => $ownersFieldsUsing['company_address'],
                'iban' => $ownersFieldsUsing['iban'],
                'rent_place' => $ownersFieldsUsing['rent_place'],
                'remark' => $ownersFieldsUsing['remark'],
                'prepiska' => $ownersFieldsUsing['prepiska'],
                'bic' => $ownersFieldsUsing['bic'],
                'bank_name' => $ownersFieldsUsing['bank_name'],
                'dead_date' => $ownersFieldsUsing['dead_date'],
                'post_payment_fields' => $ownersFieldsUsing['post_payment_fields'],
                'country' => $ownersFieldsUsing['country'],
                'birthday' => $ownersFieldsUsing['birthday'],
            ]);

            $output->writeln('In database ' . $this->userDbName . ' has ' . count($owners) . ' owners' . PHP_EOL);
        } else {
            $output->writeln('In database ' . $this->userDbName . ' has no owners found' . PHP_EOL);
        }
    }

    protected function onCommandStart($input, $output)
    {
        $sql = $this->mainConnection->prepare('
            CREATE TABLE IF NOT EXISTS ' . self::$tmpTableName . ' (
                database varchar NULL,
                users_count int4 NULL,
                "name" int4 NULL,
                surname int4 NULL,
                lastname int4 NULL,
                egn int4 NULL,
                lk_nomer int4 NULL,
                lk_izdavane int4 NULL,
                company_name int4 NULL,
                eik int4 NULL,
                phone int4 NULL,
                fax int4 NULL,
                mobile int4 NULL,
                email int4 NULL,
                address int4 NULL,
                mol int4 NULL,
                company_address int4 NULL,
                iban int4 NULL,
                rent_place int4 NULL,
                remark int4 NULL,
                prepiska int4 NULL,
                bic int4 NULL,
                bank_name int4 NULL,
                dead_date int4 NULL,
                post_payment_fields int4 NULL,
                country int4 NULL,
                birthday int4 NULL
            );
        ');
        $sql->execute();

        $sql = $this->mainConnection->prepare('
            TRUNCATE ' . self::$tmpTableName . ';
        ');
        $sql->execute();
    }

    /**
     * @param PDO $userDb
     * @param string $tableName
     *
     * @return array|bool
     */
    protected function getOwners($pdo)
    {
        $cmd = $pdo->prepare('
            select 
                id,
                "name",
                surname,
                lastname,
                egn,
                lk_nomer,
                lk_izdavane,
                company_name,
                eik,
                phone,
                fax,
                mobile,
                email,
                address,
                mol,
                company_address,
                iban,
                rent_place,
                remark,
                prepiska,
                bic,
                bank_name,
                dead_date,
                post_payment_fields,
                country,
                birthday
            from su_owners;
        ');
        $cmd->execute();

        return $cmd->fetchAll(PDO::FETCH_ASSOC);
    }
}
