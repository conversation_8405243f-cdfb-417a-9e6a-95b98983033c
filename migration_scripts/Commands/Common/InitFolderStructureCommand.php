<?php

namespace TF\Commands\Common;

use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * TS-3203.
 */
class InitFolderStructureCommand extends BaseCommand
{
    protected function configure()
    {
        parent::configure();

        $this
            ->setName('tf:init_folder_structure')
            ->setDescription('Sazdava i ili smenq pravata na papkata');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $this->validateDir(AGREEMENTS_QUEUE_PATH);
        $this->validateDir(COVERAGE_QUEUE_PATH);
        $this->validateDir(OVERLAPS_QUEUE_PATH);
        $this->validateDir(LAYERS_QUEUE_PATH);
        $this->validateDir(LAYERS_CONTRACTS_PATH);
        $this->validateDir(SALES_CONTRACTS_PATH);
        $this->validateDir(SOIL_SAMPLES_QUEUE_PATH);
        $this->validateDir(COOPERATORS_DOCUMENTS_PATH);
        $this->validateDir(HYPOTHECS_FILES_PATH);
        $this->validateDir(OWNER_DOCUMENT_FILES);
        $this->validateDir(PAYROLL_EXPORTS_PATH);
        $this->validateDir(PUBLIC_UPLOAD);
        $this->validateDir(PUBLIC_PATH);
        $this->validateDir(PUBLIC_UPLOAD_OVERLAPS);
        $this->validateDir(PUBLIC_UPLOAD_HISTORY);
        $this->validateDir(PUBLIC_UPLOAD_COOPERATOR);
        $this->validateDir(PUBLIC_UPLOAD_BLANK);
        $this->validateDir(PUBLIC_UPLOAD_EXPORT);
        $this->validateDir(WMS_IMAGE_PATH);
        $this->validateDir(WMS_MAP_PATH);
    }

    private function validateDir($path)
    {
        if (!file_exists($path)) {
            mkdir($path, 0774);
        } else {
            chmod($path, 0774);
        }
    }
}
