<?php

namespace TF\Commands\Common;

use Exception;
use PDO;
use PDOException;
use Prado\Prado;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Formatter\OutputFormatterStyle;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\ConfirmationQuestion;
use TF\Application\Entity\User;
use TF\Commands\Common\Custom\TFConsoleOutput;
use TF\Commands\ParamHelper;
use TF\Engine\Kernel\Sentry\Sentry;

/**
 * TF class extention on BaseCommand.
 */
abstract class UserDbCommand extends BaseCommand
{
    protected $mainConnection;
    protected $output;
    /* Променлива, която указва дали да се пропуска записване в susi_main.scripts_log
     * $skipLogging = true   <- няма да се записва, за кои потребителски бази е пускан скриптът,
     *                          също така няма да бъде направена проверка, за кои потребители
     *                          скриптът е пускан.
     * $skipLogging = false  <- ще се записва за кои бази е пускат скрипта
     */
    protected $skipLogging = false;
    /*
     * Променлива, която указва дали да бъде викан методът displayIssues след края на изпълнението на скрипта
     * $displayIssues = true  <- ще се извика методът displayIssues на конкретния скрипт
     * $displayIssues = false <- няма да се извика методът displayIssues на конкретния скрипт
     * */
    protected $displayIssues = false;
    /*
     * Променлива, която указва дали скриптът може да бъде пускат няколко пъти за един и същи потребител
     * Ако няма проблеми да бъде пускат по повече от един път за потребител може да бъде оставено на false
     * Ако бъде сложен $singleRunPerUser = true
     * То при пускане на скрипта за няколко потребителя, на един от които вече е пускан, то изпълнението
     * за всички няма да стартира.
     * */
    protected $singleRunPerUser = false;
    /*
     * Променлива, която указва дали скриптът се изпълнява едновременно няколко пъти.
     * След като скриптът е пуснат веднъж, може да бъде пуснат отново конкурентно на първото пускане и всяка от инстанциите
     * ще бъде изпълнявана само по веднъж на потребителска база.
     * */
    protected $simultaneousExecution = false;
    /*
     * Променлива, с името на временна таблица в която е записано за кои бази се изпълнява/ е изпълнен сктипта по време на
     * процеса на изпълнение. След приключване таблицата се затрива.
     * */
    protected $simultaneousExecutionTableName = '';

    protected $force = false;

    protected $userDbName = '';
    protected $organizationUserName = '';
    protected $organizationName = '';
    protected $organizationId = '';

    /**
     * Deistviq za izpulnenie na otdelnata potrebitelska baza (ekstendva se v konkretniq skript).
     */
    abstract protected function onDbExecute(PDO $pdo, TFConsoleOutput $output, InputInterface $input);

    protected function configure()
    {
        $this->addArgument(
            'user_databases',
            InputArgument::IS_ARRAY | InputArgument::OPTIONAL,
            'Databases to run the script on (separate multiple names with a space)?'
        )
            ->addOption('multiple_instances', null, InputOption::VALUE_NONE, 'If set, the script could be ran simultaneously on different databases')
            ->addOption('year', null, InputOption::VALUE_OPTIONAL, 'If set, the task will only on users with paid support for the inpit year')
            ->addOption('skipLogging', null, InputOption::VALUE_NONE, 'skipLogging')
            ->addOption('displayIssues', null, InputOption::VALUE_NONE, 'displayIssues')
            ->addOption('singleRunPerUser', null, InputOption::VALUE_NONE, 'singleRunPerUser')
            ->addOption('simultaneousExecution', null, InputOption::VALUE_NONE, 'simultaneousExecution')
            ->addOption('simultaneousExecutionTableName', null, InputOption::VALUE_NONE, 'simultaneousExecutionTableName')
            ->addOption('force', 'f', InputOption::VALUE_NONE, 'If set, the the command will be executed without any additional questions.');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $this->setConsoleOptions($input);
        $user_databases = $input->getArgument('user_databases');
        $this->output = $output;
        $paramHelper = new ParamHelper();
        $userDbString = $paramHelper->createQueryParamString($user_databases);
        $skippingUsers = [];

        if (empty($userDbString) && !$this->force) {
            $helper = $this->getHelper('question');
            $question = new ConfirmationQuestion("This command will be executed for every client database. \n Do you want to continue? (y / n)", false);

            if (!$helper->ask($input, $output, $question)) {
                return;
            }
        }

        $userDbConditions = 'true';
        if ($user_databases) {
            $userDbConditions = "u.database IN ({$userDbString})";
        }

        if ($input->getOption('multiple_instances')) {
            $this->simultaneousExecution = true;
        }

        $this->openConnection();
        // Get all organizations databases
        $sql = $this->mainConnection->prepare("SELECT
                    u.id,
                    database,
                    u.username as username,
                    u.name as name
                FROM
                    pg_database d
                INNER JOIN su_users u ON d.datname = u.database
                WHERE
                    {$userDbConditions}
                AND u.level = 2
                AND datistemplate = FALSE
                AND database ilike 'db_bg_%'
            ");

        $sql->execute();
        $results = $sql->fetchAll();

        if (empty($userDbString)) {
            $results[] = ['database' => 'new_users_db'];
        }
        $this->prepareStyles();

        if (!$this->force) {
            $skippingUsers = $this->checkForAlreadyRunScripts($results);
        }

        if ($this->simultaneousExecution) {
            $this->simultaneousExecutionTableName = $this->getName();
            $this->simultaneousExecutionTableName = str_replace(':', '_', $this->simultaneousExecutionTableName);
            $this->simultaneousExecutionTableName = str_replace('-', '_', $this->simultaneousExecutionTableName);
            $this->simultaneousExecutionTableName = strtolower($this->simultaneousExecutionTableName);
            $this->simultaneousExecutionTableName = 'tmp_executing_' . $this->simultaneousExecutionTableName;

            $multipleExecutionExists = $this->getTableNameExists($this->simultaneousExecutionTableName);

            if (empty($multipleExecutionExists) || false == $multipleExecutionExists[0]['exists']) {
                $sql = $this->mainConnection->prepare("
                    CREATE TABLE {$this->simultaneousExecutionTableName} (
                      db_name varchar(255),
                      is_script_running boolean DEFAULT false,
                      is_script_done boolean DEFAULT false
                    );
                ");

                $sql->execute();

                $insertSql = "INSERT INTO {$this->simultaneousExecutionTableName} (db_name) VALUES";

                foreach ($results as $value) {
                    $insertSql .= " ('{$value['database']}'),";
                }
                $insertSql = rtrim($insertSql, ',');
                $insertSql .= ';';

                $sql = $this->mainConnection->prepare($insertSql);
                $sql->execute();
            }
        }

        $this->onCommandStart($input, $output);

        for ($i = 0; $i < count($results); $i++) {
            // Skip the user if the script is already runned for it
            if (in_array($results[$i]['database'], $skippingUsers)) {
                continue;
            }

            if ($this->simultaneousExecution) {
                $executionStatus = $this->getExecutionStatus($results[$i]['database']);

                if ($executionStatus['is_script_running'] || $executionStatus['is_script_done']) {
                    continue;
                }
                $this->setExecutionStatus($results[$i]['database'], ['is_script_running' => 'true', 'is_script_done' => 'false']);
            }

            $output->info("Script: {$this->getName()}");
            $output->info("database=: {$results[$i]['database']}");
            $pdo = $this->openUserDbConnection($results[$i]['database']);
            $this->userDbName = $results[$i]['database'];
            $this->organizationUserName = $results[$i]['username'];
            $this->organizationName = $results[$i]['name'];
            $this->organizationId = $results[$i]['id'];

            try {
                $pdo->beginTransaction();
                $this->onDbExecute($pdo, new TFConsoleOutput(), $input);
                $pdo->commit();
            } catch (PDOException $e) {
                Sentry::logException($e);
                $pdo->rollBack();

                $output->error($e->getMessage());
                if (!$this->skipLogging) {
                    $this->logScript($results[$i]['database'], $e->getMessage(), 'error');
                }

                continue;
            }

            if (!$this->skipLogging) {
                $this->logScript($results[$i]['database'], $this->getName());
            }

            if ($this->simultaneousExecution) {
                $this->setExecutionStatus($results[$i]['database'], ['is_script_running' => 'false', 'is_script_done' => 'true']);
            }
        }

        if ($this->simultaneousExecution) {
            $completeResults = $this->getIsMultipleExecutionComplete();

            if (1 == count($completeResults)) {
                if (false == $completeResults[0]['is_script_running'] and true == $completeResults[0]['is_script_done']) {
                    $sql = $this->mainConnection->prepare("
                        drop TABLE {$this->simultaneousExecutionTableName}
                    ");
                    $sql->execute();
                }
            }
        }

        if ($this->displayIssues) {
            $this->displayIssues();
        }

        $this->onCommandEnd($input, $output);
    }

    /**
     * The function is called before processing client databases.
     */
    protected function onCommandStart(InputInterface $input, OutputInterface $output) {}

    /**
     * The function is called when all client databases are processed.
     */
    protected function onCommandEnd(InputInterface $input, OutputInterface $output) {}

    /**
     * Logva skripta kojto e izpalnen i za koq baza v scripts_log.
     */
    protected function logScript($user_db, $message = '', $status = 'success')
    {
        $date = date('Y-m-d H:i:s');
        $script_name = $this->getName();

        $sql = $this->mainConnection->prepare("
            INSERT INTO scripts_log (db_name, name, date, message, status)
            VALUES ('{$user_db}', '{$script_name}', '{$date}', '{$message}', '{$status}')
        ");

        return $sql->execute();
    }

    // Proverqva za koi bazi izbraniqt skript e runnat

    protected function checkForAlreadyRunScripts($bases)
    {
        $skippingUsers = [];

        $dbases = [];
        $scriptName = $this->getName();
        foreach ($bases as $base) {
            array_push($dbases, $base['database']);
        }

        $dbstring = '';
        for ($i = 0; $i < count($dbases); $i++) {
            $dbstring .= "'" . $dbases[$i] . "'";
            if ($i < count($dbases) - 1) {
                $dbstring .= ', ';
            }
        }

        $sql = "SELECT distinct(db_name) as db_name FROM scripts_log WHERE status = 'success' and name ='{$scriptName}'AND db_name IN ({$dbstring})";
        $command = $this->mainConnection->prepare($sql);
        $command->execute();
        $results = $command->fetchAll();

        if (count($results)) {
            foreach ($results as $result) {
                $skippingUsers[] = $result['db_name'];
                $this->displayErrorMessage('Skipped account. ' . $scriptName . ' is already run for database: ' . $result['db_name'], $this->output, 'yellow', 'white');
            }
            if ($this->singleRunPerUser) {
                die;
            }
        }

        return $skippingUsers;
    }

    protected function prepareStyles()
    {
        $style = new OutputFormatterStyle('black', 'green');
        $this->output->getFormatter()->setStyle('success', $style);
        $style = new OutputFormatterStyle('black', 'red');
        $this->output->getFormatter()->setStyle('error', $style);
        $style = new OutputFormatterStyle('red', 'yellow');
        $this->output->getFormatter()->setStyle('info', $style);
    }

    protected function displayErrorMessage($message, $output, $bgcolor = 'yellow', $fgcolor = 'red', $options = ['bold'])
    {
        if (!$this->output) {
            $this->output = $output;
        }
        $colors = ['black', 'red', 'green', 'yellow', 'blue', 'magenta', 'cyan', 'white'];
        if (!in_array($bgcolor, $colors) || !in_array($fgcolor, $colors)) {
            $this->output->writeln('Available foreground and background colors are: black, red, green, yellow, blue, magenta, cyan and white.');
            die;
        }
        $style = new OutputFormatterStyle($fgcolor, $bgcolor, $options);
        $this->output->getFormatter()->setStyle('error', $style);
        $this->output->writeln('<error>' . $message . '</error>');
    }

    protected function openConnection()
    {
        $this->mainConnection = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
        $this->mainConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    }

    /**
     * @param string $userDb
     *
     * @return PDO
     */
    protected function openUserDbConnection($userDb)
    {
        $pdo = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        return $pdo;
    }

    protected function displayIssues() {}

    protected function getTableNameExists($tableName, $database = DEFAULT_DB_DATABASE)
    {
        $sql = "SELECT EXISTS (
            SELECT 1
            FROM   pg_catalog.pg_class c
            JOIN   pg_catalog.pg_namespace n ON n.oid = c.relnamespace
            WHERE  n.nspname = 'public'
            AND    c.relname = '{$tableName}'
            AND    c.relkind = 'r'
            );";

        $command = $this->mainConnection->prepare($sql);
        $command->execute();

        return $command->fetchAll();
    }

    protected function getExecutionStatus($userDatabase)
    {
        $sql = "SELECT * FROM {$this->simultaneousExecutionTableName} WHERE db_name ='{$userDatabase}'";
        $command = $this->mainConnection->prepare($sql);
        $command->execute();
        $results = $command->fetchAll();

        return $results[0];
    }

    protected function setExecutionStatus($userDatabase, $status_array)
    {
        $sql = "UPDATE {$this->simultaneousExecutionTableName}
            SET is_script_running = '{$status_array['is_script_running']}',
            is_script_done = '{$status_array['is_script_done']}'
            WHERE db_name = '{$userDatabase}'";

        $command = $this->mainConnection->prepare($sql);
        $command->execute();
    }

    protected function getIsMultipleExecutionComplete()
    {
        $sql = "SELECT is_script_done, is_script_running FROM {$this->simultaneousExecutionTableName}
                GROUP BY is_script_done, is_script_running";

        $command = $this->mainConnection->prepare($sql);
        $command->execute();

        return $command->fetchAll();
    }

    protected function executeSql(PDO $pdo, string $pathToSql)
    {
        $sql = file_get_contents($pathToSql);
        $queries = explode(';', $sql);
        foreach ($queries as $query) {
            if (empty(trim($query))) {
                continue;
            }
            $sql = $pdo->prepare($query);
            $sql->execute();
        }
    }

    protected static function logging($data, $logName)
    {
        $logsDirPath = __DIR__ . '/Logs/';
        $logFilePath = $logsDirPath . $logName . '.log';

        if (!is_dir($logsDirPath)) {
            mkdir($logFilePath, 0777, true);
        }

        file_put_contents($logFilePath, $data, FILE_APPEND);
    }

    protected function initAuthUser($organizationId)
    {
        $application = Prado::getApplication();

        $organization = User::finder()->find(
            'group_id = :group_id',
            [':group_id' => $organizationId]
        );

        if (!$organization) {
            throw new Exception('No organization found');
        }

        [$user] = $organization->getOrganizationUsers();

        if (!$user) {
            throw new Exception('No user found');
        }

        $application->getModule('auth')->switchUser($user->getUsername());
    }

    private function setConsoleOptions($input)
    {
        $skipLogging = $input->getOption('skipLogging');
        $displayIssues = $input->getOption('displayIssues');
        $singleRunPerUser = $input->getOption('singleRunPerUser');
        $simultaneousExecution = $input->getOption('simultaneousExecution');
        $simultaneousExecutionTableName = $input->getOption('simultaneousExecutionTableName');
        $force = $input->getOption('force');

        if ($skipLogging) {
            $this->skipLogging = true;
        }

        if ($displayIssues) {
            $this->displayIssues = true;
        }

        if ($singleRunPerUser) {
            $this->singleRunPerUser = true;
        }

        if ($simultaneousExecution) {
            $this->simultaneousExecution = true;
        }

        if ($simultaneousExecutionTableName) {
            $this->simultaneousExecutionTableName = $simultaneousExecutionTableName;
        }

        if ($force) {
            $this->force = true;
        }
    }
}
