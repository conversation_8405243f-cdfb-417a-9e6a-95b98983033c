<?php

namespace TF\Commands\Common;

use Exception;
use PDO;

/**
 * ConvertBGNtoEUROCommand run on all databases.
 *
 * This command will convert all monetary values from BGN to EURO across all financial tables
 * Conversion rate: 1 EUR = 1.95583 BGN (BGN to EUR = 0.51129)
 */
class ConvertBGNtoEUROCommand extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('tf:convert-bgn-to-euro')
            ->setDescription('Convert all monetary values from BGN to EURO across all financial tables');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        // BGN to EURO conversion rate (1 EUR = 1.95583 BGN)
        $conversionRate = 0.51129; // 1 / 1.95583

        $output->writeln('Starting BGN to EURO conversion...');
        $output->writeln('Conversion rate: 1 BGN = ' . $conversionRate . ' EUR');

        // Array of all tables and columns that contain monetary values
        $monetaryColumns = [
            // Main rental and contract tables
            'su_charged_renta_params' => ['charged_renta', 'renta'],
            'su_renta_types' => ['renta_value'],
            'su_contracts' => ['renta', 'renta_nat'],
            'su_contracts_rents' => ['renta_value'],
            'su_sales_contracts' => ['renta', 'renta_nat', 'original_renta', 'original_renta_nat'],

            // Payment and transaction tables
            'su_payments' => ['amount', 'amount_nat'],
            'su_transactions' => ['amount', 'amount_nat'],
            'su_collections' => ['amount'],
            'su_hypothecs_payments' => ['amount'],

            // Natura payment tables
            'su_payments_natura' => ['amount', 'unit_value'],
            'su_transactions_natura' => ['amount'],
            'su_charged_renta_natura' => ['amount', 'nat_unit_price'],
            'su_charged_renta_natura_params' => ['amount', 'price_per_unit'],

            // Personal use tables
            'su_personal_use_rents' => ['renta_per_dka', 'unit_value', 'treatments_price', 'price_sum'],

            // Cooperator financial tables
            'su_cooperators_capital' => ['owe_capital', 'cashout'],
            'su_devidends_payment' => ['current_capital', 'cashout'],

            // Warehouse tables
            'wh_articles' => ['price_with_dds', 'price_no_dds'],
            'wh_transactions' => ['price_with_dds', 'price_no_dds'],

            // Diary and expense tables
            'su_diary_expenses' => ['price'],
            'su_diary_treatments_products' => ['tratment_cost', 'substance_unit_price', 'price_per_area'],
        ];

        $totalUpdatedRows = 0;

        foreach ($monetaryColumns as $tableName => $columns) {
            $output->writeln("Processing table: {$tableName}");

            // Check if table exists
            $checkTableQuery = "SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = :tableName
            )";

            $stmt = $pdo->prepare($checkTableQuery);
            $stmt->bindParam(':tableName', $tableName);
            $stmt->execute();
            $tableExists = $stmt->fetchColumn();

            if (!$tableExists) {
                $output->writeln("  - Table {$tableName} does not exist, skipping...");

                continue;
            }

            foreach ($columns as $columnName) {
                // Check if column exists
                $checkColumnQuery = "SELECT EXISTS (
                    SELECT FROM information_schema.columns 
                    WHERE table_schema = 'public' 
                    AND table_name = :tableName 
                    AND column_name = :columnName
                )";

                $stmt = $pdo->prepare($checkColumnQuery);
                $stmt->bindParam(':tableName', $tableName);
                $stmt->bindParam(':columnName', $columnName);
                $stmt->execute();
                $columnExists = $stmt->fetchColumn();

                if (!$columnExists) {
                    $output->writeln("  - Column {$columnName} does not exist in {$tableName}, skipping...");

                    continue;
                }

                // Update the monetary values
                $updateQuery = "UPDATE {$tableName} 
                               SET {$columnName} = {$columnName} * :rate 
                               WHERE {$columnName} IS NOT NULL AND {$columnName} != 0";

                try {
                    $stmt = $pdo->prepare($updateQuery);
                    $stmt->bindParam(':rate', $conversionRate);
                    $stmt->execute();
                    $rowsAffected = $stmt->rowCount();
                    $totalUpdatedRows += $rowsAffected;

                    $output->writeln("  - Updated {$rowsAffected} rows in column {$columnName}");
                } catch (Exception $e) {
                    $output->writeln("  - Error updating {$tableName}.{$columnName}: " . $e->getMessage());
                }
            }
        }

        $output->writeln('Currency conversion completed successfully!');
        $output->writeln("Total rows updated: {$totalUpdatedRows}");
        $output->writeln('All monetary values have been converted from BGN to EURO.');
    }
}
