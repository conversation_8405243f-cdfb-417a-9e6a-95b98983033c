<?php

namespace TF\Commands\Common;

use PDO;
use PDOException;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

class ReplaceKvsContractsUpdateViewCommand extends BaseCommand
{
    private $currentOrganizationDBName = '';
    private $currentOrganizationDBInstance;
    private $userDbController;

    protected function configure()
    {
        $this
            ->setName('tf:replace_kvs_contracts_update_view')
            ->setDescription('Replace kvs contracts update view with new one');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $mainDB = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
        $organizations = $this->getOrganizations($input, $mainDB);

        $output->writeln('Find ' . count($organizations) . ' organizations');

        foreach ($organizations as $key => $organization) {
            $output->writeln('Processing organization with database: ' . $organization['database']);

            try {
                $this->currentOrganizationDBInstance = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $organization['database'] . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
                $this->currentOrganizationDBName = $organization['database'];

                $this->userDbController = new UserDbController($organization['database']);
            } catch (PDOException $PDOException) {
                if (7 !== $PDOException->getCode()) {
                    $output->writeln('ERROR :' . $PDOException->getMessage());
                }

                continue;
            }

            $viewEkattes = $this->getMaterializedViews('kvs_contracts_update_%');
            foreach ($viewEkattes as $viewName) {
                $dropViewSql = "DROP MATERIALIZED VIEW IF EXISTS {$viewName}";
                $this->currentOrganizationDBInstance->exec($dropViewSql);

                $ekatte = str_replace('kvs_contracts_update_', '', $viewName);
                $this->userDbController->createKvsContractsUpdateView(null, $ekatte, null);
            }
        }
    }

    protected function getOrganizations(InputInterface $input, $mainDb)
    {
        $sql = $mainDb->prepare(
            "SELECT
                u.group_id AS group_id, 
                u.database
            FROM su_users u
            WHERE
                u.database ~ '^db_bg_[0-9]+$'
                and
                u.level = 2
            GROUP BY u.group_id,u.database
            ORDER BY database"
        );
        $sql->execute();

        return $sql->fetchAll(PDO::FETCH_ASSOC);
    }

    private function getMaterializedViews($pattern): array
    {
        $sql = 'SELECT matviewname FROM pg_matviews WHERE matviewname ILIKE :pattern';
        $cmd = $this->currentOrganizationDBInstance->prepare($sql);
        $cmd->bindParam(':pattern', $pattern);
        $cmd->execute();

        return $cmd->fetchAll(PDO::FETCH_COLUMN);
    }
}
