<?php

namespace TF\Commands\Common;

use PDO;

/**
 * command run on all databases.
 */
class RemoveViewsAndFunctionsUsedDbLinkCommand extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('tf:remove-views-and-functions-with-dblink')
            ->setDescription('Remove all materialized views and functions which used dblink');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $viewsSql = $pdo->prepare("SELECT matviewname FROM pg_matviews WHERE matviewname ~ '(allowable_from_isak_|pzp_for_isak_|sepp_for_isak_)[0-9]+$' OR matviewname IN ('ekate_combobox', 'osz_ekatte_combobox')");

        $viewsSql->execute();
        $views = $viewsSql->fetchAll(PDO::FETCH_COLUMN);
        foreach ($views as $view) {
            $sql = $pdo->prepare('DROP MATERIALIZED VIEW IF EXISTS ' . $view);
            $sql->execute();

            echo $view . ' - SUCCESSFULLY DROPPED!' . PHP_EOL;
        }

        $sql = $pdo->prepare('DROP FUNCTION IF EXISTS tf_get_plots_out_from_allowable_layer;');
        $sql->execute();

        echo 'tf_get_plots_out_from_allowable_layer - SUCCESSFULLY DROPPED!' . PHP_EOL;
    }
}
