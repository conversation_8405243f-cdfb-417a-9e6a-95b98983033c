<?php

namespace TF\Commands\Common;

use PDO;

class CreateAOIdsCommand extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('tf:create-ao-ids')
            ->setDescription('Create ao_id column in tables with AO records. It is needed in order to linked migrated records to AO db');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $this->executeSql($pdo, __DIR__ . '/sql/create_ao_ids.sql');
    }
}
