<?php

namespace TF\Commands\Common;

/**
 * init_payroll command run on all databases.
 */
class InitPayrollCommand extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('tf:init_payroll')
            ->setDescription('Creates all of required stored procedures for executing payroll');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/payroll.pgsql', $return);
    }
}
