<?php

namespace TF\Commands\Common;

use Exception;
use PDO;
use PDOException;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Application\Common\Config;
use TF\Engine\Plugins\Core\Users\UsersController;

class GrantWarehouseWriteRightToAdminUsers extends BaseCommand
{
    private $susiMainConnection;
    private $currentUserDBName;
    private $currentUserDBInstance;
    private $pradoApp;

    public function setPradoApp($pradoApp)
    {
        $this->pradoApp = $pradoApp;
    }

    protected function configure()
    {
        $this->setName('tf:grant_warehouse_write_right_to_admin_users')
            ->setDescription('Update migrated user from level 3 to level 2 permissions to parent permissions');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $UsersController = new UsersController('Users');

        $this->susiMainConnection = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = $this->susiMainConnection->prepare(
            "SELECT 
            u.id,
            u.group_id,
            u.database,
            u.username,
            u.name,
            u.level,
            json_agg(sur.right_id) as rights
         FROM 
            su_users as u
            INNER join su_users_rights as sur on sur.group_id = u.id and right_id = :right_id
         WHERE
            u.level = 2
            AND
            LOWER(u.database) like '%db_bg_%'
         group by u.id    
             "
        );

        $rightId = Config::WAREHOUSE_USER_RIGHTS;

        $sql->bindParam(':right_id', $rightId, PDO::PARAM_INT);

        if (!$sql->execute()) {
            die(print_r($sql->errorInfo()));
        }

        $sql->execute();
        $parentUsers = $sql->fetchAll(PDO::FETCH_ASSOC);

        try {
            foreach ($parentUsers as $parent) {
                $subUsers = $this->getSubUsers($parent['id']);
                $adminUsers = $this->getWarehouseAdminUsers($parent, $output);
                $adminUsernames = array_column($adminUsers, 'username');

                $filteredSubUsers = array_filter($subUsers, function ($subUser) use ($adminUsernames) {
                    return in_array($subUser['username'], $adminUsernames);
                });

                foreach ($filteredSubUsers as $subUser) {
                    $options = [
                        'user_id' => $subUser['id'],
                        'group_id' => $parent['id'],
                        'right_id' => Config::WAREHOUSE_ADMIN_RIGHTS,
                    ];
                    $UsersController->addUserRights($options);
                }
            }
        } catch (Exception $e) {
            $output->write($e->getMessage());
        }

        $output->write('end');
    }

    private function getSubUsers($parentId)
    {
        $sql = $this->susiMainConnection->prepare(
            'SELECT 
                u.id,
                u.database,
                u.username,
                u.group_id,
                u.name,
                u.level
            FROM su_users as u
            WHERE 
                u.parent_id = :parentId
                and 
                u.level = 3
            group by u.id    
            '
        );

        $sql->execute([':parentId' => $parentId]);

        return $sql->fetchAll(PDO::FETCH_ASSOC);
    }

    private function getWarehouseAdminUsers($organization, $output)
    {
        $sql = $this->susiMainConnection->prepare('
            SELECT 1 FROM pg_database WHERE datname = :datname
        ');

        $sql->execute([':datname' => $organization['database']]);

        if ($sql->fetch()) {
            try {
                $this->currentUserDBInstance = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $organization['database'] . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
                $this->currentUserDBName = $organization['database'];
            } catch (PDOException $PDOException) {
                if (7 !== $PDOException->getCode()) {
                    $output->writeln('ERROR :' . $PDOException->getMessage());
                }
            }

            $sql = $this->currentUserDBInstance->prepare(
                'SELECT 
                    *
                FROM wh_users as whu
                WHERE
                :role_name = ANY(ARRAY(SELECT json_array_elements_text(whu.roles)))
                '
            );

            $sql->execute(['role_name' => 'ROLE_ADMIN']);

            return $sql->fetchAll(PDO::FETCH_ASSOC);
        }
    }
}
