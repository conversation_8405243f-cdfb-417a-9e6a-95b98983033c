<?php

namespace TF\Commands\Common;

use PDO;
use Prado;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Commands\ParamHelper;

Prado::using('Plugins.Core.UserDb.*');

/**
 * TS-4928 command run on all databases.
 */
class MigrateDiarySubTypesOptionsCommand extends BaseCommand
{
    public const CONFIG_TYPE = 2;

    protected function configure()
    {
        $this
            ->setName('tf:migrate_diary_sub_types_options')
            ->setDescription('Add default json in options about config_type = 2 (su_diary_config) - TS-4928')
            ->addArgument(
                'user_databases',
                InputArgument::IS_ARRAY | InputArgument::OPTIONAL,
                'Databases to run the script on (separate multiple names with a space)?'
            )
            ->addOption('year', null, InputOption::VALUE_OPTIONAL, 'If set, the task will only on users with paid support for the inpit year');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $user_databases = $input->getArgument('user_databases');
        $paramHelper = new ParamHelper();
        $userDbString = $paramHelper->createQueryParamString($user_databases);

        $userDbConditions = 'true';
        if ($user_databases) {
            $userDbConditions = 'u.database IN (' . $userDbString . ')';
        }

        $mainDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = $mainDev->prepare(
            "SELECT u.id AS id, u.database FROM su_users u
            WHERE 
            {$userDbConditions}
            AND u.level = 2
            ORDER BY database"
        );

        $sql->execute();
        $results = $sql->fetchAll();

        foreach ($results as $result) {
            $pdo = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $result['database'] . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
            $items = $this->getOptions($pdo);

            $defaultOptions['hasProduce'] = false;

            foreach ($items as $item) {
                if (!empty($item['options'])) {
                    echo 'Sub Type ' . $item['name'] . ' (id: ' . $item['id'] . ') has data in options. Options: ' . $item['options'] . PHP_EOL;

                    continue;
                }
                $this->saveOptions($pdo, $item['id'], json_encode($defaultOptions));
            }

            echo 'Database: ' . $result['database'] . ' - DONE' . PHP_EOL;
        }
    }

    private function getOptions($pdo)
    {
        $sql = 'SELECT id, name, options FROM public.su_diary_configs as c WHERE  config_type = ' . self::CONFIG_TYPE;
        $results = $pdo->prepare($sql);
        $results->execute();

        return $results->fetchAll();
    }

    private function saveOptions($pdo, $id, $options)
    {
        $sql = "UPDATE public.su_diary_configs SET options = '{$options}' WHERE  id = " . $id;
        $results = $pdo->prepare($sql);
        $results->execute();
    }
}
