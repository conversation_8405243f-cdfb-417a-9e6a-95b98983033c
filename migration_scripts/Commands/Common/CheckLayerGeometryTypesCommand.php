<?php

namespace TF\Commands\Common;

use PDO;
use Prado;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Commands\ParamHelper;

Prado::using('Plugins.Core.UserDb.*');

/**
 * TS-2596 command run on all databases.
 */
class CheckLayerGeometryTypesCommand extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('tf:check_layer_geometries')
            ->setDescription('proverqva dali sashtestvuvat geometrii ot tip ST_GeometryCollection v sloevete.')
            ->addArgument(
                'user_databases',
                InputArgument::IS_ARRAY | InputArgument::OPTIONAL,
                'Databases to run the script on (separate multiple names with a space)?'
            )
            ->addOption('repair', null, InputOption::VALUE_NONE, 'If set, the script will try to update geometry collections with ST_CollectionExtract');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $user_databases = $input->getArgument('user_databases');

        $paramHelper = new ParamHelper();
        $userDbString = $paramHelper->createQueryParamString($user_databases);

        $userDbConditions = 'true';
        if ($user_databases) {
            $userDbConditions = 'u.database IN (' . $userDbString . ')';
        }

        $mainDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = $mainDev->prepare(
            "SELECT u.id AS id, u.database, ul.table_name FROM su_users u LEFT JOIN su_users_layers ul ON u.id = ul.user_id
            WHERE {$userDbConditions} AND u.level = 2 AND ul.layer_type in (1, 2, 3, 4, 6, 9) AND ul.is_exist = true
            ORDER BY database"
        );

        $sql->execute();
        $results = $sql->fetchAll();

        $id_array = [];
        foreach ($results as $result) {
            $id_array[] = $result['id'];
            if (null == $tmpDatabase || $tmpDatabase != $result['database']) {
                $tmpDatabase = $result['database'];
                $userDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $result['database'] . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
            }

            $layerSql = $userDev->prepare('SELECT ST_GeometryType(geom) from ' . $result['table_name'] . " where  ST_GeometryType(geom) = 'ST_GeometryCollection';");

            $layerSql->execute();
            $layerResults = $layerSql->fetchAll();

            if (0 != count($layerResults)) {
                $output->writeln("UserID: {$result['database']} ");
                $output->writeln('layer:' . $result['table_name'] . ' - ' . count($layerResults) . ' geoms');
                if ($input->getOption('repair')) {
                    $updateSql = $userDev->prepare('UPDATE ' . $result['table_name'] . " SET geom = ST_CollectionExtract(geom, 3) WHERE ST_GeometryType(geom) = 'ST_GeometryCollection';");
                    $updateSql->execute();
                }
            }
        }
    }
}
