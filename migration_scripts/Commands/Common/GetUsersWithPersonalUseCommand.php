<?php

namespace TF\Commands\Common;

use PDO;
use PDOException;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Commands\ParamHelper;

class GetUsersWithPersonalUseCommand extends BaseCommand
{
    private $currentUserDBName = '';
    private $currentUserDBInstance;
    private $logsDirPath;
    private $logFilePath;

    protected function configure()
    {
        $this
            ->setName('tf:get_users_with_personal_use')
            ->setDescription('Get all users with personal use in there databases')
            ->addArgument(
                'user_databases',
                InputArgument::IS_ARRAY | InputArgument::OPTIONAL,
                'Databases to run the script on (separate multiple names with space)'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $this->logsDirPath = __DIR__ . '/Logs/';
        $classNameWithoutNamespace = basename(str_replace('\\', '/', get_called_class()));
        $this->logFilePath = $this->logsDirPath . $classNameWithoutNamespace . '.log';

        if (file_exists($this->logFilePath)) {
            unlink($this->logFilePath);
        }

        $mainDB = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
        $users = $this->getUsers($input, $mainDB);

        $output->writeln('Find ' . count($users) . ' users');
        $personalUseCount = 0;
        $allUsersLog = [];
        foreach ($users as $key => $user) {
            try {
                $this->currentUserDBInstance = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $user['database'] . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
                $this->currentUserDBName = $user['database'];
            } catch (PDOException $PDOException) {
                if (7 !== $PDOException->getCode()) {
                    $output->writeln('ERROR :' . $PDOException->getMessage());
                }

                continue;
            }

            $PersonalUseRecords = $this->getContractsWithPersonalUse();

            if (!empty($PersonalUseRecords)) {
                foreach ($PersonalUseRecords as &$record) {
                    $record['username'] = $user['username'];
                    $record['database'] = $user['database'];
                }
                $allUsersLog = array_merge($allUsersLog, $PersonalUseRecords);

                $output->writeln('In database ' . $this->currentUserDBName . ' has ' . count($PersonalUseRecords) . ' records in su_personal_use' . PHP_EOL);

                $personalUseCount++;
            }

            $output->writeln('' . ($key + 1) . ' / ' . count($users) . ' users processed');
        }

        $this->logging(json_encode($allUsersLog));
    }

    /**
     * @param PDO $userDb
     * @param string $tableName
     *
     * @return array|bool
     */
    protected function getContractsWithPersonalUse()
    {
        $cmd = $this->currentUserDBInstance->prepare('
            SELECT 
                sc.c_num, 
                to_char(sc.start_date, \'YYYY-MM-DD\') as start_date,
                to_char(sc.due_date, \'YYYY-MM-DD\') as due_date,
                concat(so."name", \' \', so.surname, \' \', so.lastname) as owner_names, 
                lk.kad_ident,
                srt."name",
                sum(spur.area),
                string_agg(distinct scor.is_personal_use_proportionally_distributed::text, \', \') as proportionally 
            FROM su_personal_use pu
            left join su_contracts_plots_rel scpr on scpr.id = pu.pc_rel_id
            left join su_contracts sc on sc.id = scpr.contract_id
            left join layer_kvs lk on lk.gid = scpr.plot_id 
            left join su_personal_use_rents spur on spur.pu_id = pu.id
            left join su_renta_types srt on srt.id = spur.renta_type
            left join su_owners so on so.id = pu.owner_id 
            left join su_contract_owner_rel scor on scor.owner_id = so.id and scor.contract_id = sc.id
            group by 
                sc.id, 
                lk.kad_ident, 
                srt.id, 
                so.id;
        ');
        $cmd->execute();

        return $cmd->fetchAll(PDO::FETCH_ASSOC);
    }

    protected function getUsers(InputInterface $input, $mainDev)
    {
        $user_databases = $input->getArgument('user_databases');
        $paramHelper = new ParamHelper();
        $userDbString = $paramHelper->createQueryParamString($user_databases);
        $userDbConditions = '';
        if ($user_databases) {
            $userDbConditions = ' and u.database IN (' . $userDbString . ')';
        }

        $sql = $mainDev->prepare(
            "SELECT 
                u.database as database,
                coalesce(
                    (select string_agg(su.username, ', ') from su_users su where su.group_id = u.group_id and su.level = 3),
                    replace(u.database, 'db_', '')
                ) as username
            FROM su_users u 
            WHERE
                u.database is not null
            GROUP BY u.database, u.group_id
            ORDER BY database"
        );
        $sql->execute();

        return $sql->fetchAll(PDO::FETCH_ASSOC);
    }

    private function logging($data)
    {
        if (!is_dir($this->logsDirPath)) {
            mkdir($this->logsDirPath, 0777, true);
        }

        file_put_contents($this->logFilePath, $data, FILE_APPEND);
    }
}
