<?php

namespace TF\Commands\Common;

use Exception;
use PDO;
use Symfony\Component\Console\Input\InputOption;

class CollectPlotsCommand extends UserDbCommand
{
    private $batch;
    private $collectDb;
    private $collectTable;
    private $pdo;
    private $pdoUserDB;

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('tf:collect_plots')
            ->setDescription('Collect all Plots.')
            ->addOption('collect_table_name', null, InputOption::VALUE_OPTIONAL, 'The name of the table where the plots are been collected.', 'collected_plots')
            ->addOption('collect_data_base', null, InputOption::VALUE_OPTIONAL, 'How many records will be inserted in one query', 'db_tatyana')
            ->addOption('batch', null, InputOption::VALUE_OPTIONAL, 'How many records will be inserted in one query', 5000);
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $this->batch = $input->getOption('batch');
        $this->collectTable = $input->getOption('collect_table_name');
        $this->collectDb = $input->getOption('collect_data_base');

        $this->pdo = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $this->collectDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
        $this->pdoUserDB = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $this->createTable();

        try {
            $startTime = time();
            $plotsCount = $this->getUserPlotsCount();
            $loopCount = ceil($plotsCount / $this->batch);
            for ($i = 0; $i < $loopCount; $i++) {
                $offset = $i * $this->batch;
                $plots = $this->getUserPlots($this->batch, $offset);
                $this->addPlots($plots);
            }

            echo 'Execution time: ' . print_r(time() - $startTime, true) . " s. \r\n";
        } catch (Exception $e) {
            $output->write($e->getMessage());
        }
    }

    protected function getUserPlots($limit, $offset)
    {
        $sql = 'SELECT kad_ident, category, area_type 
                FROM layer_kvs 
                WHERE ekate IS NOT NULL
                ORDER BY kad_ident
                LIMIT ' . $limit . ' OFFSET ' . $offset;
        $query = $this->pdoUserDB->prepare($sql);
        $query->execute();

        return $query->fetchAll(PDO::FETCH_ASSOC);
    }

    protected function getUserPlotsCount()
    {
        $sql = 'SELECT count(kad_ident) 
                  FROM layer_kvs  
                  WHERE ekate IS NOT NULL';

        $query = $this->pdoUserDB->prepare($sql);
        $query->execute();
        $result = $query->fetch(PDO::FETCH_ASSOC);

        return $result['count'];
    }

    protected function addPlots($plots)
    {
        $insertedKadIdents = [];
        $sql = 'INSERT INTO ' . $this->collectTable . ' as p (kad_ident, category, area_type) VALUES';
        foreach ($plots as $plot) {
            if (in_array($plot['kad_ident'], $insertedKadIdents, true)) {
                continue;
            }
            $sql .= " ('" . $plot['kad_ident'] . "', '"
            . ((int)$plot['category'] >= 1 && (int)$plot['category'] <= 10 ? $plot['category'] : null) . "', '"
            . (ctype_digit($plot['area_type']) && 4 === strlen((string)$plot['area_type']) ? $plot['area_type'] : null) . "'),";
            $insertedKadIdents[] = $plot['kad_ident'];
        }

        echo 'Adding: ' . count($insertedKadIdents) . " plots \r\n";

        $sql = substr_replace($sql, '', -1);
        $sql .= " ON CONFLICT (kad_ident) DO UPDATE SET 
                    category = (CASE WHEN p.category ~ '^\d{1}?$' AND p.category::numeric BETWEEN 1 AND 10 THEN p.category ELSE (
                        CASE WHEN EXCLUDED.category ~ '^\d{1}?$' AND EXCLUDED.category::numeric BETWEEN 1 AND 10 THEN EXCLUDED.category ELSE NULL END) 
                    END), 
                    area_type = (CASE WHEN p.area_type ~ '^\d{4}?$' THEN p.area_type ELSE (
                        CASE WHEN EXCLUDED.area_type ~ '^\d{4}?$' THEN EXCLUDED.area_type ELSE NULL END)
                    END);";
        $this->pdo->prepare($sql)->execute();
    }

    private function createTable()
    {
        $sql = 'CREATE TABLE IF NOT EXISTS ' . $this->collectTable . ' (
                        id serial NOT NULL,
                        kad_ident varchar(55) NULL,
                        category varchar(55) NULL,
                        area_type varchar(55) NULL
                    );
                    ALTER TABLE ' . $this->collectTable . ' ADD CONSTRAINT plots_un UNIQUE (kad_ident);';

        $this->pdo->exec($sql);
    }
}
