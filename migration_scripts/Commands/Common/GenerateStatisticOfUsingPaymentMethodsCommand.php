<?php

namespace TF\Commands\Common;

use PDO;

class GenerateStatisticOfUsingPaymentMethodsCommand extends UserDbCommand
{
    private static $tmpTableName = 'tmp_statisic_of_using_payment_methods';

    public function configure()
    {
        parent::configure();
        $this
            ->setName('tf:generate_statistic_of_using_payment_methods')
            ->setDescription('Generate statistics of using payment methods and publish the results in table ' . self::$tmpTableName . ' in susi_main');
    }

    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $transactionMethodsByYear = $this->getUserData($pdo);

        foreach ($transactionMethodsByYear as $transactionMethods) {
            $sql = $this->mainConnection->prepare('
                    INSERT INTO ' . self::$tmpTableName . ' (
                        database,
                        year,
                        from_nat_to_nat,
                        from_money_to_nat,
                        from_nat_to_money,
                        from_money_to_money,
                        cash_payment,
                        bank_payment,
                        post_payment
                    )
                    VALUES (
                        :database,
                        :year,
                        :from_nat_to_nat,
                        :from_money_to_nat,
                        :from_nat_to_money,
                        :from_money_to_money,
                        :cash_payment,
                        :bank_payment,
                        :post_payment
                    );
                ');

            $sql->execute([
                'database' => $this->userDbName,
                'year' => $transactionMethods['year'],
                'from_nat_to_nat' => $transactionMethods['from_nat_to_nat'],
                'from_money_to_nat' => $transactionMethods['from_money_to_nat'],
                'from_nat_to_money' => $transactionMethods['from_nat_to_money'],
                'from_money_to_money' => $transactionMethods['from_money_to_money'],
                'cash_payment' => $transactionMethods['cash_payment'],
                'bank_payment' => $transactionMethods['bank_payment'],
                'post_payment' => $transactionMethods['post_payment'],
            ]);
        }
    }

    protected function onCommandStart($input, $output)
    {
        $sql = $this->mainConnection->prepare('
            CREATE TABLE IF NOT EXISTS ' . self::$tmpTableName . ' (
                database varchar NULL,
                year int4 NULL,
                from_nat_to_nat int4 NULL,
                from_money_to_nat int4 NULL,
                from_nat_to_money int4 NULL,
                from_money_to_money int4 NULL,
                cash_payment int4 NULL,
                bank_payment int4 NULL,
                post_payment int4 NULL
            );
        ');
        $sql->execute();

        $sql = $this->mainConnection->prepare('
            TRUNCATE ' . self::$tmpTableName . ';
        ');
        $sql->execute();
    }

    /**
     * @param PDO $userDb
     * @param string $tableName
     *
     * @return array|bool
     */
    protected function getUserData($pdo)
    {
        $cmd = $pdo->prepare('
            with 
                years as (
                    select farming_year as year
                    from su_transactions st
                    group by farming_year
                )
                , from_nat_to_nat as (
                    select farming_year, count (id) as count
                    from su_transactions st
                    where paid_from = 2 and paid_in = 2
                    group by farming_year
                )
                , from_money_to_nat as (
                    select farming_year, count (id) as count
                    from su_transactions st
                    where paid_from = 1 and paid_in = 2
                    group by farming_year
                )
                , from_nat_to_money as (
                    select farming_year, count (id) as count
                    from su_transactions st
                    where paid_from = 2 and paid_in = 1
                    group by farming_year
                )
                , from_money_to_money as (
                    select farming_year, count (id) as count
                    from su_transactions st
                    where paid_from = 1 and paid_in = 1
                    group by farming_year
                )
                , cash_payment as (
                    select farming_year, count (id) as count
                    from su_transactions st
                    where bank_payment = false and paid_in = 1
                    group by farming_year
                )
                , bank_payment as (
                    select farming_year, count (id) as count
                    from su_transactions st
                    where bank_payment = true and bank_payment_type = 0
                    group by farming_year
                )
                , post_payment as (
                    select farming_year, count (id) as count
                    from su_transactions st
                    where bank_payment = true and bank_payment_type = 1
                    group by farming_year
                )
                select 
                    years.year,
                    max(coalesce(from_nat_to_nat.count, 0)) as from_nat_to_nat,
                    max(coalesce(from_money_to_nat.count, 0)) as from_money_to_nat,
                    max(coalesce(from_nat_to_money.count, 0)) as from_nat_to_money,
                    max(coalesce(from_money_to_money.count, 0)) as from_money_to_money,
                    max(coalesce(cash_payment.count, 0)) as cash_payment,
                    max(coalesce(bank_payment.count, 0)) as bank_payment,
                    max(coalesce(post_payment.count, 0)) as post_payment
                from years
                left join from_nat_to_nat as from_nat_to_nat on from_nat_to_nat.farming_year = years.year
                left join from_money_to_nat as from_money_to_nat on from_money_to_nat.farming_year = years.year
                left join from_nat_to_money as from_nat_to_money on from_nat_to_money.farming_year = years.year
                left join from_money_to_money as from_money_to_money on from_money_to_money.farming_year = years.year
                left join cash_payment as cash_payment on cash_payment.farming_year = years.year
                left join bank_payment as bank_payment on bank_payment.farming_year = years.year
                left join post_payment as post_payment on post_payment.farming_year = years.year
                group by years.year
        ');
        $cmd->execute();

        return $cmd->fetchAll(PDO::FETCH_ASSOC);
    }
}
