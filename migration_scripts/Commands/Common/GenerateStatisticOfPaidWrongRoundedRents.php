<?php

namespace TF\Commands\Common;

use Exception;
use PDO;
use Symfony\Component\Console\Input\InputOption;
use TF\Application\Entity\User;
use TF\Engine\Plugins\Core\Payments\PaymentsController;

class GenerateStatisticOfPaidWrongRoundedRents extends UserDbCommand
{
    protected $pradoApp;
    private static $tmpTableName = 'tmp_statistic_of_paid_wrong_rounded_rents_';

    public function setPradoApp($pradoApp)
    {
        $this->pradoApp = $pradoApp;
    }

    public function configure()
    {
        parent::configure();
        $this
            ->setName('tf:generate_statistic_of_paid_wrong_rounded_rents')
            ->setDescription('Generate statistic of already paid wrong rointed rents and insert the info in ' . self::$tmpTableName . ' in susi_main')
            ->addOption('farm_years', null, InputOption::VALUE_OPTIONAL, 'Farming year ID in Technofarm system. For multiple values please use comma.', 15)
            ->addOption('fix', null, InputOption::VALUE_OPTIONAL, 'Fix the payments', false);
    }

    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $farmYears = explode(',', $input->getOption('farm_years'));
        $doFix = $input->getOption('fix');

        $user = User::finder()->find('database = :database and level = :level', [':database' => $this->userDbName, ':level' => '2']);

        try {
            $this->initAuthUser($user->id);
        } catch (Exception $e) {
            $output->writeln('Error: ' . $e->getMessage());

            return;
        }

        $PaymentsController = new PaymentsController($this->userDbName);

        $owners = $this->getPaymentOwners($pdo);

        $reportSql = $this->mainConnection->prepare('
                    INSERT INTO ' . self::$tmpTableName . ' (
                        database,
                        farming_year,
                        farming_year_title,
                        owner_names,
                        owner_egn_eik,
                        contract_id,
                        contract_num,
                        renta,
                        charged_renta,
                        paid_renta,
                        unpaid_renta,
                        overpaid_renta,
                        new_payment_money_amount,
                        new_payment_nat_amount,
                        payment_id,
                        nat_payment_id
                    )
                    VALUES (
                        :database,
                        :farming_year,
                        :farming_year_title,
                        :owner_names,
                        :owner_egn_eik,
                        :contract_id,
                        :contract_num,
                        :renta,
                        :charged_renta,
                        :paid_renta,
                        :unpaid_renta,
                        :overpaid_renta,
                        :new_payment_money_amount,
                        :new_payment_nat_amount,
                        :payment_id,
                        :nat_payment_id
                    );
                ');

        $fixMoneyPaymentSql = $pdo->prepare('
            UPDATE su_payments
            SET amount = :amount
            WHERE id = :payment_id;
        ');

        $fixNatPaymentSql = $pdo->prepare('
            UPDATE su_payments_natura
            SET amount = :amount
            WHERE id = :nat_payment_id;
        ');

        $ownersCount = count($owners);
        $output->writeln('In database ' . $this->userDbName . ' has ' . $ownersCount . ' owners' . PHP_EOL);

        foreach ($owners as $ownerKey => $owner) {
            // if(!in_array($owner['owner_id'], [800])) continue;

            if (!in_array($owner['farming_year'], $farmYears)) {
                continue;
            }

            $payments = $PaymentsController->getOwnerPayments($owner['farming_year'], $owner['owner_id']);
            if ($payments['rows']) {
                foreach ($payments['rows'] as $payment) {
                    // if($payment['contract_id'] != 24636) continue;

                    if ($this->haveWrongPayment($payment)) {
                        $newNatAmount = null;
                        $newMoneyAmount = null;

                        if ('true' === $doFix) {
                            $cmdPayment = $pdo->prepare('
                                select  
                                    p.id as payment_id,
                                    spn.id as nat_payment_id,
                                    p.owner_id,
                                    p.farming_year,
                                    p.paid_from,
                                    p.paid_in,
                                    p.amount as money_amount,
                                    spn.amount as nat_amount,
                                    spn.unit_value
                                from su_payments p
                                inner join su_transactions st on st.id = p.transaction_id 
                                left join su_payments_natura spn on spn.payment_id = p.id
                                where 
                                    st.status = true
                                    and p.paid_from = 1
                                    and p.owner_id = :owner_id
                                    and p.contract_id = :contract_id
                                    and p.farming_year = :farming_year
                                limit 1
                            ');

                            $cmdPayment->execute([
                                'owner_id' => $payment['owner_id'],
                                'contract_id' => $payment['contract_id'],
                                'farming_year' => $owner['farming_year'],
                            ]);

                            $paymentForFix = $cmdPayment->fetch(PDO::FETCH_ASSOC);

                            if ($paymentForFix) {
                                if ($paymentForFix['nat_payment_id']) {
                                    $newPaymentAmount = $wrongPaymentAmount = $paymentForFix['nat_amount'] * $paymentForFix['unit_value'];
                                    if ($payment['overpaid_renta'] > 0) {
                                        $newPaymentAmount = $wrongPaymentAmount - $payment['overpaid_renta'];
                                    }
                                    if ($payment['unpaid_renta'] > 0) {
                                        $newPaymentAmount = $wrongPaymentAmount + $payment['unpaid_renta'];
                                    }

                                    $newNatAmount = $newPaymentAmount / $paymentForFix['unit_value'];

                                    $fixNatPaymentSql->execute(
                                        [
                                            'nat_payment_id' => $paymentForFix['nat_payment_id'],
                                            'amount' => $newNatAmount,
                                        ]
                                    );

                                    $output->writeln('Owner: ' . $payment['owner_names'] . ' Contract: ' . $payment['contract_id'] . ' Old nat amount: ' . $paymentForFix['nat_amount'] . ' New Nat amount: ' . $newNatAmount);
                                } else {
                                    if ($payment['overpaid_renta'] > 0) {
                                        $newMoneyAmount = $paymentForFix['money_amount'] - $payment['overpaid_renta'];
                                    }
                                    if ($payment['unpaid_renta'] > 0) {
                                        $newMoneyAmount = $paymentForFix['money_amount'] + $payment['unpaid_renta'];
                                    }

                                    $fixMoneyPaymentSql->execute(
                                        [
                                            'payment_id' => $paymentForFix['payment_id'],
                                            'amount' => $newMoneyAmount,
                                        ]
                                    );

                                    $output->writeln('Owner: ' . $payment['owner_names'] . ' Contract: ' . $payment['contract_id'] . ' Old Money amount: ' . $paymentForFix['money_amount'] . ' New Money amount: ' . $newMoneyAmount);
                                }
                            }
                        }

                        $reportSql->execute([
                            'database' => $this->userDbName,
                            'farming_year' => $owner['farming_year'],
                            'farming_year_title' => $GLOBALS['Farming']['years'][$owner['farming_year']]['farming_year_short'],
                            'owner_names' => $payment['owner_names'],
                            'owner_egn_eik' => $payment['egn_eik'],
                            'contract_id' => $payment['contract_id'],
                            'contract_num' => $payment['c_num'],
                            'renta' => $payment['renta'],
                            'charged_renta' => ('-' == $payment['charged_renta'] ? 0 : $payment['charged_renta']),
                            'paid_renta' => $payment['paid_renta'],
                            'unpaid_renta' => $payment['unpaid_renta'],
                            'overpaid_renta' => $payment['overpaid_renta'],
                            'new_payment_money_amount' => $newMoneyAmount,
                            'new_payment_nat_amount' => $newNatAmount,
                            'payment_id' => $paymentForFix['payment_id'] ?? null,
                            'nat_payment_id' => $paymentForFix['nat_payment_id'] ?? null,
                        ]);

                        $output->writeln('Owners left: ' . $ownersCount);
                    }
                }
            }

            $ownersCount--;
        }
    }

    protected function onCommandStart($input, $output)
    {
        self::$tmpTableName .= time();

        $sql = $this->mainConnection->prepare('
            CREATE TABLE IF NOT EXISTS ' . self::$tmpTableName . ' (
                "database" varchar NULL,
                farming_year int4 NULL,
                farming_year_title varchar NULL,
                owner_names varchar NULL,
                owner_egn_eik varchar NULL,
                contract_id int4 NULL,
                contract_num varchar NULL,
                renta numeric NULL,
                charged_renta numeric NULL,
                paid_renta numeric NULL,
                unpaid_renta numeric NULL,
                overpaid_renta numeric NULL,
                new_payment_money_amount numeric NULL,
                new_payment_nat_amount numeric NULL,
                payment_id int4 NULL,
                nat_payment_id int4 NULL,
                created_at timestamp NULL DEFAULT now()
            );
        ');
        $sql->execute();

        $sql = $this->mainConnection->prepare('
            TRUNCATE ' . self::$tmpTableName . ';
        ');
        $sql->execute();
    }

    protected function getPaymentOwners($pdo)
    {
        $cmd = $pdo->prepare('
            select 
                p.owner_id,
                p.farming_year
            from su_payments p
            inner join su_transactions st on st.id = p.transaction_id and st.status = true 
            group by p.owner_id, p.farming_year;
        ');
        $cmd->execute();

        return $cmd->fetchAll(PDO::FETCH_ASSOC);
    }

    private function haveWrongPayment($payment)
    {
        $unpadRent = round($payment['unpaid_renta'], 2);
        $overpaidRent = round($payment['overpaid_renta'], 2);

        return (bool) (
            $payment['paid_renta'] > 0
            && ($unpadRent > 0 && $unpadRent < 1)
            || ($overpaidRent > 0 && $overpaidRent < 1)
        );
    }
}
