<?php

namespace TF\Commands\Common;

use PDO;
use Prado;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Commands\ParamHelper;
use UserDbModel;

require_once SITE_PATH . 'engine/Plugins/Core/Notifications/conf.php';

Prado::using('Plugins.Core.UserDb.*');

/**
 * TS-5164 command run on all databases.
 */
class AddMissingNotificationAlertsCommand extends BaseCommand
{
    public const CONFIG_TYPE = 7;

    protected function configure()
    {
        $this
            ->setName('tf:add_missing_notification_alerts')
            ->setDescription('Check if alerts in config.php exist in  and su_alert_settings and if not insert its')
            ->addArgument(
                'user_databases',
                InputArgument::IS_ARRAY | InputArgument::OPTIONAL,
                'Databases to run the script on (separate multiple names with a space)?'
            )
            ->addOption('year', null, InputOption::VALUE_OPTIONAL, 'If set, the task will only on users with paid support for the inpit year');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $user_databases = $input->getArgument('user_databases');
        $paramHelper = new ParamHelper();
        $userDbString = $paramHelper->createQueryParamString($user_databases);

        $userDbConditions = 'true';
        if ($user_databases) {
            $userDbConditions = 'u.database IN (' . $userDbString . ')';
        }

        $mainDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = $mainDev->prepare(
            "SELECT u.id AS id, u.database FROM su_users u
            WHERE 
            {$userDbConditions}
            AND u.level = 2
            ORDER BY database"
        );

        $sql->execute();
        $results = $sql->fetchAll();
        $configuratedAlarms = $GLOBALS['Notifications']['Alarms'];
        foreach ($results as $result) {
            $pdo = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $result['database'] . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
            $dbAlarms = $this->getNotificationsAlerts($pdo);
            foreach ($configuratedAlarms as $alarm) {
                if (!in_array($alarm['alert_name'], $dbAlarms)) {
                    $this->insertNotification($result['database'], $alarm);
                }
            }
            echo 'Database: ' . $result['database'] . ' - DONE' . PHP_EOL;
        }
    }

    private function getNotificationsAlerts($pdo)
    {
        $sql = 'SELECT alert_name FROM public.su_alert_settings WHERE true';

        $results = $pdo->prepare($sql);
        $results->execute();

        return $results->fetchAll(PDO::FETCH_COLUMN, 0);
    }

    private function insertNotification($dataBase, $data)
    {
        $userDbModel = new UserDbModel($dataBase);
        $params = [
            'tablename' => DEFAULT_DB_PREFIX . 'alert_settings',
            'mainData' => $data,
        ];
        $userDbModel->addItem($params);
    }
}
