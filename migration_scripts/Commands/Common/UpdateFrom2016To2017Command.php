<?php

namespace TF\Commands\Common;

/**
 * UpdateFrom2016To2017 command run on all databases.
 */
class UpdateFrom2016To2017Command extends UserDbCommand
{
    protected $skipLogging = true;

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('tf:update-from-2016-to-2017')
            ->setDescription('Update na user database ot 2016 do 2017');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $commandsArr = [
            new \TF\Commands\Common\UpdateUserTo2017SupportCommand(),
            new \TF\Commands\sprint_s15_1\UpdateToSprint15_1(),
            new \TF\Commands\sprint_s16\UpdateToSprint16(),
            new \TF\Commands\sprint_s17\UpdateToSprint17(),
            new \TF\Commands\sprint_s18\UpdateToSprint18(),
            new \TF\Commands\sprint_s19\TS3620Command(),
        ];

        for ($i = 0; $i < count($commandsArr); $i++) {
            $command = $commandsArr[$i];
            $output->writeln('Executing command: ' . $command->getName());
            $this->logScript($userDb, $command->getName());
            $command->onDbExecute($userDb, $output, $input);
        }
    }

    protected function logScript($user_db, $name)
    {
        $date = date('Y-m-d H:i:s');
        $this->openConnection();
        $sql = $this->mainConnection->prepare("
            INSERT INTO scripts_log (db_name, name, date)
            VALUES ('{$user_db}', '{$name}', '{$date}')
        ");

        return $sql->execute();
    }
}
