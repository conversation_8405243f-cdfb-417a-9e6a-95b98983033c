<?php

namespace TF\Commands\Common;

use PDO;

/**
 * TS-4973 command run on all databases.
 */
class UpdateSubleasesViewCommand extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('tf:update_subleases_view')
            ->setDescription('Update subleases_view source');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $this->executeSql($pdo, __DIR__ . '/sql/4973.sql');
    }
}
