<?php

namespace TF\Commands\Common;

/**
 * UpdateFrom2014To2015 command run on all databases.
 */
class UpdateFrom2014To2015Command extends UserDbCommand
{
    protected $skipLogging = true;

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('tf:update-from-2014-to-2015')
            ->setDescription('Update na user database ot 2014 do 2015');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $commandsArr = [
            new \TF\Commands\sprint_s04\TS515Command(),
            new \TF\Commands\sprint_s04\TS522Command(),

            // Sprint 5.1
            new \TF\Commands\sprint_s05_1\TS563Command(),

            // Sprint 5.2
            new \TF\Commands\sprint_s05_2\TS599Command(),
            new \TF\Commands\sprint_s05_2\TS602Command(),
            new \TF\Commands\sprint_s05_2\TS622Command(),
            new \TF\Commands\sprint_s05_2\TS651Command(),

            // Sprint 6
            new \TF\Commands\sprint_s06\TS694Command(),
            new \TF\Commands\sprint_s06\TS704Command(),
            new \TF\Commands\sprint_s06\TS739Command(),
            new \TF\Commands\sprint_s06\TS740Command(),
            new \TF\Commands\sprint_s06\TS741Command(),
            new \TF\Commands\sprint_s06\TS789Command(),
            new \TF\Commands\sprint_s06\TS794Command(),
            new \TF\Commands\sprint_s06\TS7941Command(),
            new \TF\Commands\sprint_s06\TS798Command(),
            new \TF\Commands\sprint_s06\TS806Command(),
            new \TF\Commands\sprint_s06\TS833Command(),

            // Sprint 6.1
            new \TF\Commands\sprint_s06_1\TS803Command(),
            new \TF\Commands\sprint_s06_1\TS804Command(),

            // Sprint 8
            new \TF\Commands\sprint_s08\TS879Command(),
            new \TF\Commands\sprint_s08\TS219Command(),
            new \TF\Commands\sprint_s08\TS880Command(),
            new \TF\Commands\sprint_s08\TS8801Command(),
            new \TF\Commands\sprint_s08\TS896Command(),
            new \TF\Commands\sprint_s08\TS976Command(),
            new \TF\Commands\sprint_s08\TS9762Command(),
            new \TF\Commands\sprint_s08\TS1260Command(),
            new \TF\Commands\sprint_s08\TS1314Command(),
            new \TF\Commands\sprint_s08\TS1387Command(),
            new \TF\Commands\sprint_s08\TS1460Command(),
            new \TF\Commands\sprint_s08\TS14601Command(),

            // Sprint 9
            new \TF\Commands\sprint_s09\TS1192Command(),
            new \TF\Commands\sprint_s09\TS1193Command(),
            new \TF\Commands\sprint_s09\TS1440Command(),
            new \TF\Commands\sprint_s09\TS1443Command(),
            new \TF\Commands\sprint_s09\TS1711Command(),

            // Sprint 10
            new \TF\Commands\sprint_s10\TS1754Command(),
            new \TF\Commands\sprint_s10\TS1778Command(),
            new \TF\Commands\sprint_s10\TS1783Command(),
            new \TF\Commands\sprint_s10\TS1784Command(),
            new \TF\Commands\sprint_s10\TS1785Command(),
            new \TF\Commands\sprint_s10\TS1901Command(),

            new \TF\Commands\sprint_s10\TS19581Command(),
        ];

        for ($i = 0; $i < count($commandsArr); $i++) {
            $command = $commandsArr[$i];
            $output->writeln('Executing command: ' . $command->getName());
            $this->logScript($userDb, $command->getName());
            $command->onDbExecute($userDb, $output, $input);
        }
    }

    protected function logScript($user_db, $name)
    {
        $date = date('Y-m-d H:i:s');
        $this->openConnection();
        $sql = $this->mainConnection->prepare("
            INSERT INTO scripts_log (db_name, name, date)
            VALUES ('{$user_db}', '{$name}', '{$date}')
        ");

        return $sql->execute();
    }
}
