<?php

namespace TF\Commands\Common;

use Config;
use PD<PERSON>;
use PDOException;
use Prado;
use S<PERSON>fony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Helper\Table;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Commands\ParamHelper;

Prado::using('Plugins.Core.UserDb.*');

/**
 * TS-4037 command run on all databases.
 */
class CheckLayersTablesSchemaCommand extends BaseCommand
{
    protected $requiredIsakColumns = ['prc_uin', 'area', 'ekatte', 'cropcode', 'watering', 'schemata', 'cropname'];

    protected function configure()
    {
        $this
            ->setName('tf:check_layers_table_schema')
            ->setDescription('check required columns layer_tables ( of type LAYER_TYPE_ISAK for now)')
            ->addArgument(
                'user_databases',
                InputArgument::IS_ARRAY | InputArgument::OPTIONAL,
                'Databases to run the script on (separate multiple names with a space)'
            )
            ->addOption('year', null, InputOption::VALUE_OPTIONAL, 'If set, the task will only on users with paid support for the input year');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $output->writeln('INFO: check_layers_table_schema process started');
        $mainDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
        $users = $this->getUsers($input, $mainDev);

        foreach ($users as $user) {
            try {
                $userDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $user['database'] . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
            } catch (PDOException $PDOException) {
                $output->writeln('ERROR :' . $PDOException->getMessage());

                continue;
            }
            $tables = $this->getTables(Config::LAYER_TYPE_ISAK, $userDev);
            if (!$tables || empty($tables)) {
                $output->writeln('INFO: no tables of type LAYER_ISAK found in database : ' . $user['database']);

                continue;
            }
            $result = $this->validateTables($tables, Config::LAYER_TYPE_ISAK, $user);
            if (empty($result)) {
                $output->writeln('SUCCESS: all tables of type LAYER_ISAK are valid user : ' . $user['database']);

                continue;
            }
            $outputTable = new Table($output);
            $outputTable->setHeaders(['user_id', 'user_db', 'table', 'missing columns'])->setRows($result);
            $outputTable->render();
        }
        $output->writeln('INFO: check_layers_table_schema process end');
    }

    /**
     * @param int $layer_type
     * @param string $userDev
     *
     * @return array|bool
     */
    protected function getTables($layer_type, $userDev)
    {
        $table_name = null;
        if (Config::LAYER_TYPE_ISAK == $layer_type) {
            $table_name = 'layer_isak_%';
        }
        $cmd = $userDev->prepare(
            "SELECT TABLE_NAME as table_name, string_agg(COLUMN_NAME,'|') as column_names FROM information_schema.COLUMNS WHERE TABLE_NAME LIKE '{$table_name}' GROUP BY TABLE_NAME"
        );
        $cmd->execute();

        return $cmd->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * @param array $tables
     * @param int $layer_type
     * @param array $user
     *
     * @return array
     */
    protected function validateTables($tables, $layer_type, $user)
    {
        $requiredColumns = null;
        $missingColumns = [];

        if (Config::LAYER_TYPE_ISAK == $layer_type) {
            $requiredColumns = $this->requiredIsakColumns;
        }
        foreach ($tables as $table) {
            $tableColumns = explode('|', $table['column_names']);
            foreach ($requiredColumns as $requiredColumn) {
                if (!in_array($requiredColumn, $tableColumns)) {
                    $missingColumns[$table['table_name']][] = $requiredColumn;
                }
            }
        }
        // format data
        $return = [];
        $index = 0;
        if (!empty($missingColumns)) {
            foreach ($missingColumns as $tableName => $missingColumn) {
                $return[$index]['user_id'] = $user['id'];
                $return[$index]['user_db'] = $user['database'];
                $return[$index]['table'] = $tableName;
                $return[$index]['columns'] = implode(', ', $missingColumn);
                $index++;
            }
        }

        return $return;
    }

    protected function getUsers(InputInterface $input, $mainDev)
    {
        $user_databases = $input->getArgument('user_databases');
        $paramHelper = new ParamHelper();
        $userDbString = $paramHelper->createQueryParamString($user_databases);
        $userDbConditions = 'true';
        if ($user_databases) {
            $userDbConditions = 'u.database IN (' . $userDbString . ')';
        }
        $userDbPaidSuport = ' AND true';
        if ($input->getOption('year')) {
            $userDbPaidSuport = " AND u.paid_support = '" . $input->getOption('year') . "-01-01'";
        }

        $sql = $mainDev->prepare(
            "SELECT u.id AS id, u.database FROM su_users u WHERE 
            {$userDbConditions}
            {$userDbPaidSuport}
            AND u.level = 2
            ORDER BY database"
        );

        $sql->execute();

        return $sql->fetchAll(PDO::FETCH_ASSOC);
    }
}
