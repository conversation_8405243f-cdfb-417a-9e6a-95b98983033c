<?php

namespace TF\Commands\Common;

use PDO;

/**
 * command run on all databases.
 */
class RemoveSeppViewsCommand extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('tf:remove-sepp-views')
            ->setDescription('Removes all sepp views from acounts with disabled module subsidies');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $susiMain = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
        $userDbPdo = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
        $sqlSusiMain = $susiMain->prepare('SELECT
            u. ID
        FROM
            su_users u
        LEFT JOIN su_users_rights ur ON ur.user_id = u.id
        AND ur.right_id = 3
        WHERE
            u.level = 2
        AND ur.right_id NOTNULL and u.database=:database;');

        $sqlSusiMain->bindParam(':database', $userDb);
        $sqlSusiMain->execute();
        $userId = $sqlSusiMain->fetchColumn();

        if ($userId) {
            $output->warn('Subsidies module is still active. Skipping');

            return;
        }

        $viewsSql = $userDbPdo->prepare("
        SELECT
            matviewname
        FROM
            pg_matviews
        WHERE
            matviewname ILIKE 'sepp_%'
            OR matviewname ILIKE 'pzp_%';
        ");
        $viewsSql->execute();

        $seppViews = $viewsSql->fetchAll(PDO::FETCH_COLUMN, 0);

        foreach ($seppViews as $view) {
            $output->info("Removing {$view} view.");
            $dropViewSql = $userDbPdo->prepare("DROP MATERIALIZED VIEW {$view}");

            $dropViewSql->execute();
        }
    }
}
