<?php

namespace TF\Commands\Common;

use PDO;

class DeleteAllPersonalUseData extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('tf:delete_all_personal_use_data')
            ->setDescription('Delete data from su_personal_use and su_personal_use_rents tables in all databases');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $this->executeSql($pdo, __DIR__ . '/sql/GPS-3534.sql');
    }
}
