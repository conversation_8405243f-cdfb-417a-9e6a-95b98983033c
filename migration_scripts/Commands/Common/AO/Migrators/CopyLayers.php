<?php

namespace TF\Commands\Common\AO\Migrators;

use PDO;
use TF\Commands\Common\AO\AOMigratorCommand;
use TF\Commands\Common\AO\AOMigratorInterface;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

class CopyLayers implements AOMigratorInterface
{
    public string $description = 'Copy user layers from AO database in TechnoFarm DB';

    public function exec(AOMigratorCommand $migrator)
    {
        $destDbConnection = $migrator->tfUserDbConnection;
        $destDbConnectionMain = $migrator->tfMainDbConnection;
        $sourceDbConnection = $migrator->aoMapUserDbConnection;
        $sourceDbConnectionSystem = $migrator->aoSystemDbConnection;

        $migrator->logging('PREPARE DATABASES', $migrator::LOG_TYPE_LABEL);
        $stmt = $destDbConnection->exec('DROP EXTENSION IF EXISTS ogr_fdw CASCADE');
        if (false === $stmt) {
            $migrator->logging($destDbConnection->errorInfo()[2], $migrator::LOG_TYPE_ERROR);
            exit();
        }
        $migrator->logging('DROP EXTENSION ogr_fdw SUCCESSFULLY.');

        $stmt = $destDbConnection->exec('CREATE EXTENSION ogr_fdw;');
        if (false === $stmt) {
            $migrator->logging($destDbConnection->errorInfo()[2], $migrator::LOG_TYPE_ERROR);
            exit();
        }
        $migrator->logging('CREATE EXTENSION ogr_fdw SUCCESSFULLY.');

        $stmt = $destDbConnection->exec('CREATE SERVER svr_AO_SYS FOREIGN DATA WRAPPER ogr_fdw OPTIONS (datasource \'MSSQL:driver={ODBC Driver 17 for SQL Server};server=' . $migrator->aoMapDbHost . ',1433;database=Agro_System;UID=' . $migrator->aoMapDbUser . ';PWD=t}[ZY3ad;\',format \'MSSQLSpatial\');');
        if (false === $stmt) {
            $migrator->logging($destDbConnection->errorInfo()[2], $migrator::LOG_TYPE_ERROR);
            exit();
        }
        $migrator->logging('CREATE SERVER svr_AO_SYS SUCCESSFULLY.');

        $stmt = $destDbConnection->exec('IMPORT FOREIGN SCHEMA "dbo.AMP_Properties" FROM SERVER svr_AO_SYS INTO public;');
        if (false === $stmt) {
            print($destDbConnection->errorInfo()[2]);
            exit();
        }
        $migrator->logging('IMPORT FOREIGN SCHEMA SUCCESSFULLY.');

        $stmt = $destDbConnection->exec('CREATE SERVER svr_AO FOREIGN DATA WRAPPER ogr_fdw OPTIONS (datasource \'MSSQL:driver={ODBC Driver 17 for SQL Server};server=' . $migrator->aoMapDbHost . ',1433;database=' . $migrator->aoClientUserName . ';UID=' . $migrator->aoMapDbUser . ';PWD=t}[ZY3ad;\',format \'MSSQLSpatial\');');
        if (false === $stmt) {
            $migrator->logging($destDbConnection->errorInfo()[2], $migrator::LOG_TYPE_ERROR);
            exit();
        }
        $migrator->logging('CREATE SERVER svr_AO SUCCESSFULLY.');

        $stmt = $destDbConnection->exec('IMPORT FOREIGN SCHEMA "dbo.AMP_ArableLands" FROM SERVER svr_AO INTO public;');
        if (false === $stmt) {
            $migrator->logging($destDbConnection->errorInfo()[2], $migrator::LOG_TYPE_ERROR);
            exit();
        }
        $migrator->logging('IMPORT FOREIGN SCHEMA SUCCESSFULLY.');

        $tf_user_id = $migrator->tfUser['id'];
        $tf_group_id = $migrator->tfUser['group_id'];

        $migrator->logging('group id = ' . $tf_group_id);
        $migrator->logging('user id = ' . $tf_user_id);

        /*
         * IMPORT KVS DATA
         * ##################################################################################################################
         */

        $migrator->logging('IMPORT KVS DATA', $migrator::LOG_TYPE_LABEL);

        $kvs = [];
        $stmtI = $sourceDbConnection->prepare('select MapOfRestoredOwnershipID  FROM AMP_ClientMapOfRestoredOwnerships a');
        $stmtI->execute();
        while ($row = $stmtI->fetch()) {
            $kvs[] = $row[0];
        }

        $stmtI = $sourceDbConnectionSystem->prepare('
            select ap.id, av.EKATTE, ap.ArrayNumber, ap.PropertyNumber, ap.SizeByDocument, al.Name as locality, alu.number as LastingUseID, ap.LandCategory
                    from AMP_Properties ap 
                    inner join AMP_MapOfRestoredOwnerships amoro on ap.MapOfRestoredOwnershipID = amoro.ID 
                    inner join AMP_Villages av on  av.ID = amoro.VillageID
                    inner join AMP_Localities al on  al.ID = ap.LocalityID
                    inner join AMP_LastingUses alu on  alu.ID = ap.LastingUseID
                    where amoro.ID in (' . implode(',', $kvs) . ')');
        $stmtI->execute();

        $allAoPlots = $stmtI->fetchAll(PDO::FETCH_ASSOC);

        //        $filterdPlots = array_filter($allAoPlots, function ($plot){
        //            return ($plot['EKATTE'] === '268');
        //        });
        //        $filterdPlots = array_values($filterdPlots);
        $filterdPlots = array_values($allAoPlots);

        $startTime = time();
        $chunkSize = 100;
        $insertSql = 'INSERT INTO layer_kvs (kad_ident, ekate, masiv, number, used_area, document_area, allowable_area, category, mestnost, area_type, geom) VALUES ';
        $sqlValues = '';
        $plotsCount = count($filterdPlots);

        $migrator->logging('Found ' . count($kvs) . ' KVS and ' . $plotsCount . ' plots for migrating');

        foreach ($filterdPlots as $key => $aoPlot) {
            $key++;
            $migrator->logging('Plot: ' . $key . ' / ' . $plotsCount);
            $ekatte = str_pad($aoPlot['EKATTE'], 5, '0', STR_PAD_LEFT);
            $kad_ident = $ekatte . '.' . $aoPlot['ArrayNumber'] . '.' . $aoPlot['PropertyNumber'];
            $fid = $aoPlot['id'];
            $category = $aoPlot['LandCategory'];
            $ntp = $aoPlot['LastingUseID'];
            $mestnost = $aoPlot['locality'];

            $stmt = $destDbConnection->prepare('select count(gid) as num from layer_kvs where ekate=? AND masiv=? and number=?');
            $stmt->execute([$ekatte, $row['ArrayNumber'], $row['PropertyNumber']]);
            $count = $stmt->fetchColumn();

            if ($count > 0) {
                $migrator->logging('Duplicated: ' . $ekatte . '.' . $row['ArrayNumber'] . '.' . $row['PropertyNumber'], $migrator::LOG_TYPE_WARNING);

                continue;
            }

            $sqlValues .= "('" . $kad_ident . "', '" . $ekatte . "', '" . $aoPlot['ArrayNumber'] . "', '" . $aoPlot['PropertyNumber'] . "', " . ($aoPlot['SizeByDocument'] / 1000) . ', ' . ($aoPlot['SizeByDocument'] / 1000) . ', ' . ($aoPlot['SizeByDocument'] / 1000) . ", '" . $category . "', '" . $mestnost . "', '" . $ntp . "', ST_Transform(ST_SetSRID((select bounds from dbo_amp_properties where fid='" . $fid . "'),4326),32635)),";

            if ((0 === $key % $chunkSize) || $plotsCount === $key) {
                $destDbConnection->exec($insertSql . rtrim($sqlValues, ',') . ';');
                if (false === $stmt) {
                    $migrator->logging($destDbConnection->errorInfo()[2], $migrator::LOG_TYPE_ERROR);
                    exit();
                }
                $migrator->logging('SUCCESSFUL IMPORTED ' . $key . ' PLOTS');
                sqlsrv_free_stmt($stmt);
                $sqlValues = '';
            }
        }

        $endTime = ((time() - $startTime) / 60);
        $migrator->logging('Inserting KVS finished in ' . round($endTime, 2) . ' minutes.');

        $UserDbController = new UserDbController($migrator->User->Database);
        $UserDbController->refreshEkateCombobox();
        $migrator->logging('REFRESH MATERIALIZED VIEW SUCCESSFULLY.');

        $stmt = $destDbConnectionMain->prepare('select id from su_users_layers where user_id=' . $tf_user_id . ' and layer_type=5');
        $stmt->execute();
        $layer_id = $stmt->fetchColumn();

        $LayersController = new LayersController('Layers');
        $MapTools = makeApiClass('map-rpc', 'map-tools');
        $LayersChange = makeApiClass('map-rpc', 'layer-change');

        $LayersController->updateUnstyledEkkates($migrator->tfUser['database'], $layer_id);
        $migrator->logging('UPDATING UNSTYLED EKATTES FINISHED SUCCESSFULLY.');

        /*
         * IMPORT ALL USER FARMINGS
         * ##################################################################################################################
         */

        $migrator->logging('IMPORT ALL USER FARMINGS', $migrator::LOG_TYPE_LABEL);

        $stmt = $sourceDbConnection->prepare('select distinct(c.Name) from Companies c inner join AMP_Layers al on c.ID = al.CompanyID ');
        $stmt->execute();
        $FarmingGrid = makeApiClass('farming-rpc', 'farming-grid');
        $aoCompanies = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $tfUsers = $migrator->getTFusers($migrator->User->GroupID);

        foreach ($aoCompanies as $aoCompany) {
            $FarmingGrid->addNewFarming([
                'name' => $aoCompany['Name'],
                'user_id' => $migrator->User->UserID,
                'company' => $aoCompany['Name'],
                'bulstat' => null,
                'group_id' => $migrator->User->GroupID,
                'address' => null,
                'company_address' => null,
                'mol' => null,
                'mol_egn' => null,
                'iban_arr' => [[
                    'name' => null,
                    'iban' => null,
                    'bic' => null,
                    'bank_branch' => null,
                    'bank_branch_address' => null,
                ]],
                'representative_id' => null,
                'farmingUsers' => array_column($tfUsers, 'id'),
            ]);

            $migrator->logging('FARMING: ' . $aoCompany['Name'] . ' CREATED SUCCESSFULLY.');
        }

        /*
         * IMPORT ALL OTHER LAYERS
         * ##################################################################################################################
         */

        $migrator->logging('IMPORT ALL OTHER LAYERS', $migrator::LOG_TYPE_LABEL);

        $stmtI = $sourceDbConnection->prepare('select DISTINCT (layerid), al.Name, al.Color, al.CompanyID  from AMP_ArableLands ap inner join AMP_Layers al on ap.LayerID = al.ID');
        $stmtI->execute();
        while ($row = $stmtI->fetch()) {
            $migrator->logging('Layer data: ' . $row['Name'] . ', ' . $row['layerid'] . ', ' . $row['Color'], $migrator::LOG_TYPE_LABEL);
            $layer_name = 'ao_tmp';

            $stmt = $destDbConnection->exec('CREATE TABLE ' . $layer_name . ' as select fid, size, number, lastmodifydate, bounds, name from dbo_amp_arablelands where layerid=' . $row['layerid']);
            if (false === $stmt) {
                $migrator->logging($destDbConnection->errorInfo()[2], $migrator::LOG_TYPE_ERROR);
                exit();
            }
            $migrator->logging('CREATE TABLE: ' . $layer_name);

            $stmt = $destDbConnection->exec('ALTER TABLE ' . $layer_name . ' ADD COLUMN geom geometry');
            if (false === $stmt) {
                $migrator->logging($destDbConnection->errorInfo()[2], $migrator::LOG_TYPE_ERROR);
                exit();
            }
            $migrator->logging('Add geom column');

            $stmt = $destDbConnection->exec('UPDATE ' . $layer_name . ' SET geom = ST_Transform(ST_SetSRID(bounds,4326),32635);');
            if (false === $stmt) {
                $migrator->logging($destDbConnection->errorInfo()[2], $migrator::LOG_TYPE_ERROR);
                exit();
            }
            $migrator->logging('Transfrom SRID from 4326 to 32635');

            $stmt = $destDbConnection->exec('ALTER TABLE ' . $layer_name . ' ADD COLUMN gid int4');
            if (false === $stmt) {
                $migrator->logging($destDbConnection->errorInfo()[2], $migrator::LOG_TYPE_ERROR);
                exit();
            }
            $migrator->logging('Add gid column');

            $stmt = $destDbConnection->exec('UPDATE ' . $layer_name . ' SET gid = fid;');
            if (false === $stmt) {
                $migrator->logging($destDbConnection->errorInfo()[2], $migrator::LOG_TYPE_ERROR);
                exit();
            }
            $migrator->logging('Update gid with values from fid column');

            $stmt = $destDbConnection->exec('ALTER TABLE ' . $layer_name . ' DROP COLUMN bounds;');
            if (false === $stmt) {
                $migrator->logging($destDbConnection->errorInfo()[2], $migrator::LOG_TYPE_ERROR);
                exit();
            }
            $migrator->logging('Drop bounds column');

            $stmt = $sourceDbConnection->prepare('select Name from Companies where ID=?');
            $stmt->execute([$row['CompanyID']]);
            $s_company = $stmt->fetchColumn();
            $migrator->logging('AgroOffice company name: ' . $s_company);

            $stmt = $destDbConnectionMain->prepare('select id from su_users_farming where name=? AND user_id=' . $tf_user_id);
            $stmt->execute([$s_company]);
            $d_company_id = $stmt->fetchColumn();
            $migrator->logging('TechnoFarm company ID: ' . $d_company_id);

            $params = [
                'layer_type' => 19,
                'layertmp_type' => 19,
                'layertmp_id' => 'new',
                'layer_name' => 'ao_tmp',
                'layertmp_name' => 'layer_ao_' . $row['layerid'],
            ];
            $layertmp_id = $MapTools->copyLayerItems($params);

            $migrator->logging('Layer created with id: ' . $layertmp_id['layertmp_id']);

            $rpc = [];
            $rpc['id'] = $layertmp_id['layertmp_id'];
            $rpc['color'] = $row['Color'];
            $rpc['border_color'] = 'ffffff';
            $rpc['transparency'] = 50;
            $rpc['border_only'] = false;
            $rpc['layer_type'] = 19;
            $rpc['layer_name'] = '[AO] ' . $row['Name'];
            $rpc['layer_farming'] = $d_company_id;
            $rpc['layer_year'] = 14;
            $rpc['label_name'] = ['name'];

            $LayersChange->saveLayerPersonazation($rpc);

            $migrator->logging('Save Layer Personalization.');

            $stmt = $destDbConnection->exec('DROP table IF EXISTS ' . $layer_name);
            if (false === $stmt) {
                $migrator->logging($destDbConnection->errorInfo()[2], $migrator::LOG_TYPE_ERROR);
                exit();
            }

            $migrator->logging('Drop the temp table: ' . $layer_name);

            sqlsrv_free_stmt($stmt);
        }
    }
}
