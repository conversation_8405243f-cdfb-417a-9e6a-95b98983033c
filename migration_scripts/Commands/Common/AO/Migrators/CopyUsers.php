<?php

namespace TF\Commands\Common\AO\Migrators;

use PDO;
use TF\Commands\Common\AO\AOMigratorCommand;
use TF\Commands\Common\AO\AOMigratorInterface;

class CopyUsers implements AOMigratorInterface
{
    public const USER_RIGHTS = [
        4 => 'AGRO_RIGHTS',
        16 => 'AGRO_RIGHTS_RW',
        24 => 'COLLECTIONS_RIGHTS',
        25 => 'COLLECTIONS_RIGHTS_RW',
        9 => 'CONTRACTS_OWN_WRITE_RIGHTS',
        26 => 'DASHBOARD_RIGHTS',
        10 => 'EQUITY_RIGHTS',
        29 => 'EXPORT_MASS_PAYMENT_RIGHTS',
        20 => 'HYPOTHECS_RIGHTS_R',
        21 => 'HYPOTHECS_RIGHTS_RW',
        28 => 'KVS_CUTTING_RIGHTS',
        1 => 'MAP_RIGHTS_R',
        12 => 'MAP_RIGHTS_RW',
        2 => 'PLOT_RIGHTS_R',
        14 => 'PLOT_RIGHTS_RW',
        18 => 'SALES_CONTRACTS_RIGHTS_R',
        19 => 'SALES_CONTRACTS_RIGHTS_RW',
        3 => 'SUBSIDY_RIGHTS',
        15 => 'SUBSIDY_RIGHTS_RW',
        22 => 'THEMATIC_MAPS_RIGHTS_R',
        23 => 'THEMATIC_MAPS_RIGHTS_RW',
        27 => 'WAREHOUSE_USER_RIGHTS',
        32 => 'WAREHOUSE_ADMIN_RIGHTS',
        33 => 'WAREHOUSE_EDITOR_RIGHTS',
    ];

    public string $description = 'test Migration Description';

    public function exec(AOMigratorCommand $migrator)
    {
        $aoInstance = $migrator->getAOInstance();
        $stmt = $migrator->aoUserLoginDbConnection->prepare('
            SELECT 
                au.UserId AS ao_id, 
                au.LoweredUserName AS username, 
                am.LoweredEmail AS email,
                am.CreateDate AS creation_date,
                au.LastActivityDate AS last_login_date
            FROM aspnet_Users au
            LEFT JOIN UserCompany uc ON uc.UserId = au.UserId 
            LEFT JOIN Company c ON c.Id = uc.CompanyId
            LEFT JOIN [Instance] i ON i.Id = c.InstanceId
            LEFT JOIN aspnet_Membership am ON am.UserId = au.UserId 
            WHERE i.id = :instance_id
            GROUP by au.UserId, au.LoweredUserName, am.LoweredEmail, am.CreateDate, au.LastActivityDate;
        ');

        $stmt->execute([':instance_id' => $aoInstance['Id']]);
        $aoUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $stmp = $migrator->tfMainDbConnection->prepare('SELECT sur.right_id FROM su_users_rights sur WHERE sur.user_id = :user_id;');
        $stmp->execute([':user_id' => $migrator->tfUser['id']]);
        $tfUserRights = $stmp->fetchAll(PDO::FETCH_COLUMN, 0);
        $usersClass = makeApiClass('users-rpc', 'users');

        $tfUserFarmingsSelectedIds = [];
        $tfUserFarmings = $usersClass->getFarmings($migrator->tfUser['id']);
        foreach ($tfUserFarmings as $farming) {
            if (isset($farming['selected']) && $farming['selected']) {
                $tfUserFarmingsSelectedIds[] = $farming['id'];
            }
        }

        foreach ($aoUsers as $aoUser) {
            $rpcParams = [
                'username' => $aoUsers['username'],
                'phone' => '',
                'password' => 'ao123agro',
                'name' => $aoUsers['username'],
                'email' => $aoUsers['email'],
                'address' => '',
                'comment' => '',
                'editUserID' => -1,
                'userFarmings' => $tfUserFarmingsSelectedIds,
            ];
            $migrator->logging($rpcParams);
            //            foreach (self::USER_RIGHTS as $key => $userRight) {
            //                $rpcParams[$userRight] = in_array($key, $tfUserRights);
            //            }
            //
            //            $a = $usersClass->doAddUser($rpcParams);
        }
    }
}
