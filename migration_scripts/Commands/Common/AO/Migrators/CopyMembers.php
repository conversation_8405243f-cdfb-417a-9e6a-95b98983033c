<?php

namespace TF\Commands\Common\AO\Migrators;

use PDO;
use TF\Commands\Common\AO\AOMigratorCommand;
use TF\Commands\Common\AO\AOMigratorInterface;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

class CopyMembers implements AOMigratorInterface
{
    public const OWNERS_TYPE_MAP = [
    ];

    public string $description = 'Copy members data from AO database in TechnoFarm DB';

    public function exec(AOMigratorCommand $migrator)
    {
        $destDbConnection = $migrator->tfUserDbConnection;
        $sourceDbConnectionSystem = $migrator->aoSystemDbConnection;

        /*
         * IMPORT OWNERS DATA
         * ##################################################################################################################
         */

        $stmtI = $destDbConnection->prepare('SELECT ekate, ekatte_name FROM ekate_combobox');
        $stmtI->execute();
        $ekattes = $stmtI->fetchAll(PDO::FETCH_ASSOC);

        $UsersDbController = new UserDbController($_SESSION['database']);

        $migrator->logging('Insert files records.', $migrator::LOG_TYPE_LABEL);

        $files = [];
        foreach ($ekattes as $ekatte) {
            $options = [
                'tablename' => $UsersDbController->DbHandler->tableOSZFiles,
                'mainData' => [
                    'land' => $ekatte['ekatte_name'],
                    'ekatte' => $ekatte['ekate'],
                ],
            ];
            $files[$ekatte['ekate']] = $UsersDbController->addItem($options);

            $migrator->logging('File for ' . $ekatte['ekatte_name'] . '(' . $ekatte['ekate'] . ') was inserted');
        }

        $migrator->logging('Inserting plots owners.', $migrator::LOG_TYPE_LABEL);

        $stmtI = $destDbConnection->prepare('SELECT kad_ident, ekate, masiv, number, document_area, allowable_area, category, area_type, virtual_ntp_title  FROM layer_kvs');
        $stmtI->execute();
        $kvsPlots = $stmtI->fetchAll(PDO::FETCH_ASSOC);
        $plotsCount = count($kvsPlots);

        $migrator->logging('Migrate ' . $plotsCount . ' plots in ' . count($ekattes) . ' EKATTEs.');

        foreach ($kvsPlots as $key => $plot) {
            $stmt = $sourceDbConnectionSystem->prepare(
                "SELECT 
                                                                DISTINCT(o.Name) AS name, 
                                                                ot.Number AS owner_type_number, 
                                                                ot.name AS owner_type_name,
                                                                COALESCE(pot.Number, 0) AS plot_ownership_type_number,        
                                                                COALESCE(pot.Name, 'Няма данни') AS plot_ownership_type_name,
                                                                dt.CategoryID AS document_type_category_id,
                                                                dt.Number AS document_type_number,
                                                                dt.Name AS document_type_name,
                                                                dtc.Name AS document_category_name,      
                                                                dtc.ID AS document_category_id        
                                                              FROM [Agro_System].[dbo].[AMP_Properties] p
                                                              LEFT JOIN AMP_PropertyOwnerships po ON po.PropertyID = p.ID
                                                              LEFT JOIN AMP_OwnershipTypes pot ON pot.ID = p.DataTypeID
                                                              LEFT JOIN DocumentTypes dt ON dt.ID = po.DocumentTypeID
                                                              LEFT JOIN DocumentTypeCategories dtc ON dtc.ID = dt.CategoryID 
                                                              LEFT JOIN AMP_Owners o ON o.ID = po.OwnerID
                                                              LEFT JOIN AMP_PersonTypes ot ON ot.ID = o.PersonTypeID
                                                              LEFT JOIN AMP_MapOfRestoredOwnerships mro ON mro.ID = p.MapOfRestoredOwnershipID
                                                              LEFT JOIN AMP_Villages v ON v.ID = mro.VillageID                                                              
                                                              WHERE o.Name != '' 
                                                                  AND p.ArrayNumber = '" . $plot['masiv'] . "' 
                                                                  AND p.PropertyNumber='" . $plot['number'] . "' 
                                                                  AND v.EKATTE='" . $plot['ekate'] . "'"
            );
            $stmt->execute();
            $owners = $stmt->fetchAll(PDO::FETCH_ASSOC);

            if (empty($owners)) {
                continue;
            }

            $records = [];
            foreach ($owners as $owner) {
                $records[] = [
                    'id_imot' => $key,
                    'kvs_no' => str_pad($plot['ekate'], 5, '0') . str_pad($plot['masiv'], 3, '0') . str_pad($plot['number'], 3, '0'),
                    'kad_no' => $plot['kad_ident'],
                    'kod_subekt' => $owner['owner_type_number'],
                    'txt_subekt' => $owner['owner_type_name'],
                    'ime_subekt' => $owner['name'],
                    'egn_subekt' => '',
                    'kod_pr_osn' => $owner['document_category_id'],
                    'txt_pr_osn' => $owner['document_category_name'],
                    'pl_dka' => $plot['document_area'],
                    'pl_dka_po' => $plot['allowable_area'],
                    'kategoria' => $plot['category'],
                    'kod_ntp' => $plot['area_type'],
                    'txt_ntp' => mb_strcut($plot['virtual_ntp_title'], 0, 15, 'utf-8'),
                    'kod_sobstv' => $owner['plot_ownership_type_number'],
                    'txt_sobstv' => $owner['plot_ownership_type_name'],
                    'kod_imot' => '',
                    'txt_imot' => '',
                    'ekatte' => $plot['ekate'],
                    'ver_no' => null,
                    'data' => null,
                    'vreme' => null,
                    'file_id' => $files[$plot['ekate']],
                ];
            }

            $usedColumns = array_keys($records[0]);
            $usedColumnsStr = implode(', ', $usedColumns);
            $UsersDbController->addItems([
                'tablename' => $UsersDbController->DbHandler->tableOSZFilesPlots,
                'columns' => $usedColumnsStr,
                'values' => $records,
            ]);

            $migrator->logging('Plot ' . $key . ' / ' . $plotsCount);

            sqlsrv_free_stmt($stmt);
        }

        $migrator->logging('Delete duplicated records from OSZ', $migrator::LOG_TYPE_LABEL);
        foreach ($ekattes as $ekatte) {
            $UsersDbController->deleteDuplicatedRecordsFromOSZ($ekatte);
        }

        $migrator->logging('Refresh MATERIALIZED VIEWS', $migrator::LOG_TYPE_LABEL);
        $UsersDbController->refreshTopicLayerKVSViews();
        $UsersDbController->refreshOszEkateCombobox();
    }
}
