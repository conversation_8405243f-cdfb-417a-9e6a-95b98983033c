<?php

namespace TF\Commands\Common;

use Config;
use EkateCombobox;
use Exception;
use FarmingCombobox;
use Prado;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Engine\Kernel\ExportToExcelClass;
use TJsonRpcProtocol;
use TRpcServer;
use UsedPlotsReportGrid;
use UsersController;

Prado::using('Plugins.Core.UserDb.*');
Prado::using('Plugins.Core.Layers.*');
Prado::using('APIClasses.Plots.UsedPlotsReportGrid');
Prado::using('APIClasses.Common.FarmingCombobox');
Prado::using('APIClasses.Common.EkateCombobox');
Prado::using('APIClasses.Common.Config');

class AccountsReportCommand extends UserDbCommand
{
    protected $skipLogging = true;
    protected $pradoApp;
    protected $headers = [
        'Потребител',
        'Име',
        'Стопанства',
        'Договори 2018',
        'Площ използвана земя 2018',
        'Договори 2017',
        'Площ използвана земя 2017',
        'ГАП',
        'Модули',
        'Землища',
    ];

    private $accountInfoArr = [];

    public function setPradoApp($pradoApp)
    {
        $this->pradoApp = $pradoApp;
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('tf:accounts_report')
            ->setDescription('Generates an Excel file with information for every account about farms, contracts and etc.')
            ->addOption('file_name', null, InputOption::VALUE_OPTIONAL, 'The name of generated file.', 'accounts_report.xlsx');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $UsersController = new UsersController('Users');

        $auth = $this->pradoApp->getModule('auth');
        $server = new TRpcServer(new TJsonRpcProtocol());
        $usedPlotsReportGrid = new UsedPlotsReportGrid($server);
        $farmingCombobox = new FarmingCombobox($server);
        $ekateCombobox = new EkateCombobox($server);

        try {
            $user = $this->getUser($UsersController, $userDb);
            $auth->switchUser($user['username']);
            $startTime = time();

            $usedPlotsReportData = $this->getUsedPlotsData($usedPlotsReportGrid, '2017-10-01', '2018-09-30');
            $contractsTotal2018 = $usedPlotsReportData['total'];
            $footerTotal2018 = $usedPlotsReportData['footer'][1];

            $usedPlotsReportData = $this->getUsedPlotsData($usedPlotsReportGrid, '2016-10-01', '2017-09-30');
            $contractsTotal2017 = $usedPlotsReportData['total'];
            $footerTotal2017 = $usedPlotsReportData['footer'][1];

            $farmingComboboxData = $farmingCombobox->getFarmingType([]);
            $ekateComboboxData = $ekateCombobox->getEKATTECombox([]);
            $userRightsIds = $UsersController->getUserRightsByUserID($user['id']);
            $userRightsStr = $this->formatUsersRights($userRightsIds);

            $this->accountInfoArr[] = [
                $user['username'],
                $user['name'],
                count($farmingComboboxData),
                $contractsTotal2018,
                floatval(str_replace(' ', '', $footerTotal2018['area'])),
                $contractsTotal2017,
                floatval(str_replace(' ', '', $footerTotal2017['area'])),
                $user['paid_support'],
                $userRightsStr,
                count($ekateComboboxData),
            ];

            $endTime = time();
            $execution_time = $endTime - $startTime;
        } catch (Exception $e) {
            $output->write('Error: Report id: ' . $report['id'] . ' , message: ' . $e->getMessage());
            $UsersController->setPayrollExportMessage($report['id'], $e->getMessage(), true);
        }
    }

    protected function onCommandEnd(InputInterface $input, OutputInterface $output)
    {
        $fileName = $input->getOption('file_name');
        $exportExcelDoc = new ExportToExcelClass();
        $exportExcelDoc->export($this->accountInfoArr, $this->headers);
        $exportExcelDoc->saveFile($fileName);
        $output->info("Generated file: {$fileName}");
    }

    protected function getUser($UsersController, $userDb)
    {
        $users = $UsersController->getUsers([
            'return' => ['id', 'username', 'name', 'paid_support'],
            'where' => [
                'database' => ['column' => 'database', 'compare' => '=', 'value' => $userDb],
                'level' => ['column' => 'level', 'compare' => '=', 'value' => 2],
            ],
        ]);
        if (0 === count($users)) {
            return;
        }

        return $users[0];
    }

    protected function getUsedPlotsData(UsedPlotsReportGrid $usedPlotsReportGrid, $fromDate, $toDate)
    {
        $filter = [
            'report_arendator' => '',
            'report_category' => '',
            'report_choose_participation' => '',
            'report_choose_renewed' => '',
            'report_contract_date' => '',
            'report_contract_date_to' => '',
            'report_date' => $toDate,
            'report_date_as_of' => '',
            'report_date_from' => $fromDate,
            'report_ekate' => '',
            'report_exclude_inactive' => true,
            'report_farming' => '',
            'report_include_subleases' => false,
            'report_irrigation' => 'all',
            'report_mestnost' => '',
            'report_ntp' => '',
            'report_sublease_type' => 'null',
        ];
        $usedPlotsReportData = $usedPlotsReportGrid->getUseedPlots(['filters' => $filter]);
        $contractsTotal = $usedPlotsReportData['total'];

        if (0 == $contractsTotal) {
            $filter['report_date'] = '2017-09-30';
            $filter['report_date_from'] = '2016-10-01';
            $usedPlotsReportData = $usedPlotsReportGrid->getUseedPlots(['filters' => $filter]);
        }

        return $usedPlotsReportData;
    }

    protected function formatUsersRights($userRightsIds)
    {
        $userRightsIdsFormated = array_map(function ($rightId) {
            switch ($rightId['right_id']) {
                case Config::MAP_RIGHTS_RW:
                    return 'Карта';

                    break;
                case Config::PLOT_RIGHTS_RW:
                    return 'Имоти';

                    break;
                case Config::CONTRACTS_WRITE_RIGHTS:
                    return 'Договори';

                    break;
                case Config::SUBSIDY_RIGHTS_RW:
                    return 'Субсидии';

                    break;
                case Config::AGRO_RIGHTS_RW:
                    return 'Агротехника';

                    break;
                case Config::SALES_CONTRACTS_RIGHTS_RW:
                    return 'Договори за продажба';

                    break;
                case Config::HYPOTHECS_RIGHTS_RW:
                    return 'Ипотеки';

                    break;
                case Config::THEMATIC_MAPS_RIGHTS_RW:
                    return 'Тематични карти';

                    break;
                case Config::COLLECTIONS_RIGHTS_RW:
                    return 'Вземания';

                    break;
                case Config::EQUITY_RIGHTS:
                    return 'Дялов капитал';

                    break;

                default:
                    return '';

                    break;
            }
        }, $userRightsIds);
        $filtered = array_values(array_filter($userRightsIdsFormated, function ($rightName) {
            return !empty($rightName);
        }));

        return implode(', ', $filtered);
    }
}
