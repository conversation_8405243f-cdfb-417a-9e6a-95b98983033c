<?php

namespace TF\Commands\sprint_s20;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-3868 command run on all databases.
 */
class TS3868Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s20:TS-3868')
            ->setDescription('adds column cancelled_by (as string username) to user\'s su_transaction tables');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-3868.sql', $return);
    }
}
