-- changing the column type of retna

DROP TRIGGER IF EXISTS "SU_CONTRACTS_TRIGGER" on "public"."su_contracts";
DROP MATERIALIZED VIEW IF EXISTS renta_nats_mat_view;
DROP MATERIALIZED VIEW IF EXISTS renta_nats_annexes_mat_view;



ALTER TABLE su_contracts alter COLUMN renta TYPE NUMERIC(24,12);
ALTER TABLE su_contracts add COLUMN overall_renta NUMERIC(24,12);
ALTER TABLE su_contracts_plots_rel alter COLUMN rent_per_plot TYPE NUMERIC(24,12);


--recreating views and trigger
CREATE MATERIALIZED VIEW renta_nats_annexes_mat_view AS 

   SELECT (crt.renta_value * (((pc.area_for_rent * po.percent) / (100)::double precision) - COALESCE(pu.area, (0)::real))) AS renta_nat,
    (
        CASE
            WHEN (a.renta IS NULL) THEN c.renta
            ELSE a.renta
        END * ((pc.area_for_rent * (po.percent / (100)::double precision)) - COALESCE(pu.area, (0)::real))) AS renta,
    a.renta AS annex_renta,
    pc.area_for_rent AS annex_area,
    ((pc.area_for_rent * (po.percent / (100)::double precision)) - COALESCE(pu.area, (0)::real)) AS contract_area,
    c.id AS c_id,
    a.id AS a_id,
    a.farming_id,
    a.start_date,
    a.due_date,
    po.owner_id,
    o.is_dead,
    kvs.ekate,
    kvs.gid AS plot_id,
    crt.renta_id AS nat_type,
    rt.name,
    rt.unit,
    pu.year
   FROM ((((((((su_contracts c
     LEFT JOIN su_contracts a ON (((a.parent_id = c.id) AND (a.active = true))))
     JOIN su_contracts_plots_rel pc ON ((pc.contract_id = a.id)))
     LEFT JOIN su_plots_owners_rel po ON ((po.pc_rel_id = pc.id)))
     JOIN su_owners o ON ((o.id = po.owner_id)))
     LEFT JOIN layer_kvs kvs ON ((kvs.gid = pc.plot_id)))
     LEFT JOIN su_personal_use pu ON (((pu.owner_id = po.owner_id) AND (pu.pc_rel_id = pc.id))))
     LEFT JOIN su_contracts_rents crt ON (
        CASE
            WHEN (a.id IS NULL) THEN (c.id = crt.contract_id)
            ELSE (a.id = crt.contract_id)
        END))
     LEFT JOIN su_renta_types rt ON ((crt.renta_id = rt.id)))
  WHERE ((pc.annex_action = 'added'::annex_action_enum) AND (a.is_annex = true))
  GROUP BY rt.name, crt.renta_value, crt.renta_id, pc.area_for_rent, po.percent, c.id, a.id, po.owner_id, a.farming_id, a.start_date, a.due_date, rt.unit, kvs.ekate, kvs.gid, o.is_dead, pu.area, pu.year
  ORDER BY c.id;

CREATE MATERIALIZED VIEW renta_nats_mat_view AS 

   SELECT (crt.renta_value * (((pc.area_for_rent * po.percent) / (100)::double precision) - COALESCE(pu.area, (0)::real))) AS renta_nat,
    (c.renta * (((pc.area_for_rent * po.percent) / (100)::double precision) - COALESCE(pu.area, (0)::real))) AS renta,
    ((pc.area_for_rent * (po.percent / (100)::double precision)) - COALESCE(pu.area, (0)::real)) AS contract_area,
    c.c_num,
    c.id AS c_id,
    c.farming_id,
    c.start_date,
    c.due_date,
    po.owner_id,
    o.is_dead,
    kvs.ekate,
    kvs.gid AS plot_id,
    crt.renta_id AS nat_type,
    rt.name,
    rt.unit,
    pu.year
   FROM (((((((su_contracts c
     JOIN su_contracts_plots_rel pc ON ((pc.contract_id = c.id)))
     JOIN su_plots_owners_rel po ON ((po.pc_rel_id = pc.id)))
     JOIN su_owners o ON ((o.id = po.owner_id)))
     JOIN layer_kvs kvs ON ((kvs.gid = pc.plot_id)))
     LEFT JOIN su_personal_use pu ON (((pu.owner_id = po.owner_id) AND (pu.pc_rel_id = pc.id))))
     LEFT JOIN su_contracts_rents crt ON ((c.id = crt.contract_id)))
     LEFT JOIN su_renta_types rt ON ((crt.renta_id = rt.id)))
  WHERE ((pc.annex_action = 'added'::annex_action_enum) AND (c.is_annex = false))
  GROUP BY rt.name, crt.renta_value, crt.renta_id, pc.area_for_rent, po.percent, c.id, po.owner_id, c.farming_id, c.start_date, c.due_date, rt.unit, kvs.ekate, kvs.gid, o.is_dead, pu.area, pu.year
  ORDER BY c.id;

CREATE TRIGGER "SU_CONTRACTS_TRIGGER" 
    AFTER INSERT OR UPDATE OF "id", "c_num", "c_date", "nm_usage_rights", "sv_num", "sv_date", "start_date", "renta", "due_date", "renta_nat", "farming_id", "agg_type", "active", "parent_id", "is_annex", "renta_nat_type_id", "is_sublease", "original_due_date", "original_renta", "original_renta_nat", "original_renta_nat_type_id", "na_num", "payday" 
    OR DELETE ON "public"."su_contracts"
FOR EACH STATEMENT
EXECUTE PROCEDURE "refresh_rentas_materialized_views"();