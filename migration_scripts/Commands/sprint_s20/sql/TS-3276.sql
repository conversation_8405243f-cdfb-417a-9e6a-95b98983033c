
-- addding the rents table
CREATE TABLE "public"."su_personal_use_rents" (
"id" int4 NOT NULL PRIMARY KEY,
"pu_id" int4,
"renta_type" int4,
"renta_per_dka" FLOAT8,
"unit_value" FLOAT8
)
WITH (OIDS=FALSE);


ALTER TABLE "public"."su_personal_use_rents" ADD CONSTRAINT "su_personal_use_rents_pu_id_fk" FOREIGN KEY ("pu_id") REFERENCES "public"."su_personal_use" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

ALTER TABLE "public"."su_personal_use_rents" ADD CONSTRAINT "su_personal_use_rents_renta_type_id_fk" FOREIGN KEY ("renta_type") REFERENCES "public"."su_renta_types" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

CREATE SEQUENCE su_personal_use_rents_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER TABLE su_personal_use_rents_id_seq OWNER TO postgres;

ALTER TABLE ONLY su_personal_use_rents ALTER COLUMN id SET DEFAULT nextval('su_personal_use_rents_id_seq'::regclass);

-- adding the contract-owner relation table
CREATE TABLE "public"."su_contract_owner_rel" (
"id" int4 NOT NULL PRIMARY KEY,
"owner_id" int4,
"contract_id" int4,
"is_personal_use_proportionally_distributed" bool
)
WITH (OIDS=FALSE);


ALTER TABLE "public"."su_contract_owner_rel" ADD CONSTRAINT "su_contract_owner_rel_owner_id_fk" FOREIGN KEY ("owner_id") REFERENCES "public"."su_owners" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;
ALTER TABLE "public"."su_contract_owner_rel" ADD CONSTRAINT "su_contract_owner_rel_contract_id_fk" FOREIGN KEY ("contract_id") REFERENCES "public"."su_contracts" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;


CREATE SEQUENCE su_contract_owner_rel_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE su_contract_owner_rel_id_seq OWNER TO postgres;


ALTER SEQUENCE su_contract_owner_rel_id_seq OWNED BY su_contract_owner_rel.id;


ALTER TABLE ONLY su_contract_owner_rel ALTER COLUMN id SET DEFAULT nextval('su_contract_owner_rel_id_seq'::regclass);

-- fill the contract-owner table with all the owners and contracts from the current personal_use
insert into su_contract_owner_rel (owner_id, contract_id, is_personal_use_proportionally_distributed) 
(select pu.owner_id, cprel.contract_id, false  from su_personal_use pu
join su_contracts_plots_rel cprel on cprel.id = pu.pc_rel_id
group by cprel.contract_id, owner_id)