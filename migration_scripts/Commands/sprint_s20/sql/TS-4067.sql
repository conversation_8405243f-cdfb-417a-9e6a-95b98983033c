create table if not exists su_global_notification_types
(
	id serial not null
		constraint su_notification_types_pkey
			primary key,
	name varchar(255),
	background_color_hex varchar(7),
	"order" integer
)
;

create unique index if not exists su_global_notification_types_order_uindex
	on su_global_notification_types ("order")
;

create table if not exists su_global_notifications
(
	id serial not null
		constraint su_notifications_id_pk
			primary key,
	type_id integer
		constraint su_notifications__types_fk
			references su_global_notification_types,
	title varchar(255),
	text varchar(255),
	created timestamp default now(),
	modified timestamp,
	start_time timestamp,
	end_time timestamp,
	is_closable boolean,
	is_active boolean
)
;

create table if not exists su_global_notifications_users_closed
(
	id serial not null
		constraint su_notifications_users_closed_pkey
			primary key,
	notification_id integer
		constraint su_notifications_users_closed_notifications__fk
			references su_global_notifications,
	user_id integer
		constraint su_notifications_users_closed__users_fk
			references su_users,
	time_closed timestamp default now()
)
;

INSERT INTO public.su_global_notification_types (id, name, background_color_hex, "order") VALUES (2, 'warning', '#ffcc00', 2);
INSERT INTO public.su_global_notification_types (id, name, background_color_hex, "order") VALUES (1, 'info', '#2eb82e', 3);
INSERT INTO public.su_global_notification_types (id, name, background_color_hex, "order") VALUES (3, 'danger', '#ff0000', 1);

