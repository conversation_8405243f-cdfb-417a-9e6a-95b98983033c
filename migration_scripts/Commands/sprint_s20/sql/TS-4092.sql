-- ----------------------------
-- Table structure for su_user_machine
-- ----------------------------
DROP TABLE IF EXISTS "public"."su_user_machine";
CREATE TABLE "public"."su_user_machine" (
"id" serial NOT NULL,
"last_date" date,
"machine" varchar(255) COLLATE "default",
"status" bool,
"user_id" int4,
"device_id" int4 NOT NULL,
"serial" varchar(16) COLLATE "default"
)
WITH (OIDS=FALSE)

;

-- ----------------------------
-- Alter Sequences Owned By
-- ----------------------------

-- ----------------------------
-- Primary Key structure for table su_user_machine
-- ----------------------------
ALTER TABLE "public"."su_user_machine" ADD PRIMARY KEY ("id");
