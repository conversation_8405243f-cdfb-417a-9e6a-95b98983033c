UPDATE su_contracts_plots_rel d
SET area_for_rent = contracts.contract_area
FROM
  (
    SELECT
      cprel.id,
      cprel.contract_area,
      cprel.area_for_rent
    FROM
      su_contracts_plots_rel cprel
      INNER JOIN layer_kvs kvs ON (cprel.plot_id = kvs.gid)
      INNER JOIN su_contracts C ON (C.ID = cprel.contract_id)
    WHERE
      TRUE
      AND C.active = 'true'
      AND C.nm_usage_rights IN ('2', '3')
      AND (
        cprel.area_for_rent = 0
        OR cprel.area_for_rent IS NULL
      )
    ORDER BY
      cprel.contract_id
  ) contracts
WHERE
  contracts.ID = d.id;