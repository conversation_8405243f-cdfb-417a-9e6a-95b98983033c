<?php

namespace TF\Commands\sprint_s20;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-4169 command run on all databases.
 */
class TS4169Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s20:TS-4169')
            ->setDescription('Changes comment that are null = empty string');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-4169.sql', $return);
    }
}
