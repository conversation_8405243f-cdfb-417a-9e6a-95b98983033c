<?php

namespace TF\Commands\sprint_s20;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-3807 command run on all databases.
 */
class TS3807Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s20:TS-3807')
            ->setDescription('updates all area_for_rent records that are zero or null for arenda and naem contracts');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-3807.sql', $return);
    }
}
