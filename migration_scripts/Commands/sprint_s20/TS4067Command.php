<?php

namespace TF\Commands\sprint_s20;

use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * TS-4067 command run on all databases.
 */
class TS4067Command extends BaseCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s20:TS-4067')
            ->setDescription('creates tables su_global_notification_type, su_global_notifications, su_global_notifications_users_closed');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        system(
            'psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . DEFAULT_DB_DATABASE . ' -f ' . __DIR__ . '/sql/TS-4067.sql',
            $return
        );
    }
}
