<?php

namespace TF\Commands\sprint_s20;

use TF\Commands\Common\UserDbCommand;

/**
 * Runs all scripts required for migration from Sprint 19 to Sprint 20.
 */
class UpdateToSprint20 extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s20:update-to-sprint20')
            ->setDescription('Runs all scripts required for migration from Sprint 19 to Sprint 20.');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $commandsArr = [
            new TS3276Command(),
            new TS3565Command(),
            new TS4067Command(),
            new TS4169Command(),
            new TS3868Command(),
        ];

        $count = count($commandsArr);
        for ($i = 0; $i < $count; $i++) {
            $command = $commandsArr[$i];
            $this->logScript($userDb, $command->getName());
            $output->writeln('Executing command: ' . $command->getName());
            $command->onDbExecute($userDb, $output, $input);
        }
    }

    protected function logScript($user_db, $name)
    {
        $date = date('Y-m-d H:i:s');

        $this->openConnection();

        $sql = $this->mainConnection->prepare("
            INSERT INTO scripts_log (db_name, name, date)
            VALUES ('{$user_db}', '{$name}', '{$date}')
        ");

        return $sql->execute();
    }
}
