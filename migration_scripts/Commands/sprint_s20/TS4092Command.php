<?php

namespace TF\Commands\sprint_s20;

use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * TS-4092 command run on all databases.
 */
class TS4092Command extends BaseCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s20:TS-4092')
            ->setDescription('Creates tables su_user_machine');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . DEFAULT_DB_DATABASE . ' -f ' . __DIR__ . '/sql/TS-4092.sql', $return);
    }
}
