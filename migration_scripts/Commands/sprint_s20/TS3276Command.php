<?php

namespace TF\Commands\sprint_s20;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-3276 command run on all databases.
 */
class TS3276Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s20:TS-3276')
            ->setDescription('creates tables su_personal_use_rents and su_contract_owner_rel');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-3276.sql', $return);
    }
}
