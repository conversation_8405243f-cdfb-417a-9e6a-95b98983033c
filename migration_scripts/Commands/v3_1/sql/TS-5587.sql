CREATE TABLE public.wh_ext_log_entries (
    id INT NOT NULL,
    action VARCHAR(8) NOT NULL,
    logged_at TIMESTAMP(0) WITHOUT TIME ZONE NOT NULL,
    object_id VARCHAR(64) DEFAULT NULL,
    object_class VARCHAR(191) NOT NULL,
    version INT NOT NULL,
    data TEXT DEFAULT NULL,
    username VARCHAR(191) DEFAULT NULL,
    PRIMARY KEY(id)
);

ALTER TABLE public.wh_ext_log_entries OWNER TO postgres;

CREATE SEQUENCE public.ext_log_entries_id_seq INCREMENT BY 1 MINVALUE 1 START 1;

ALTER TABLE public.wh_ext_log_entries OWNER TO postgres;

CREATE INDEX log_class_lookup_idx ON public.wh_ext_log_entries (object_class);
CREATE INDEX log_date_lookup_idx ON public.wh_ext_log_entries (logged_at);
CREATE INDEX log_user_lookup_idx ON public.wh_ext_log_entries (username);
CREATE INDEX log_version_lookup_idx ON public.wh_ext_log_entries (object_id, object_class, version);
COMMENT ON COLUMN public.wh_ext_log_entries.data IS '(DC2Type:array)';





