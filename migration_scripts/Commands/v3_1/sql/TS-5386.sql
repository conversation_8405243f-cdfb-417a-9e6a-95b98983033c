CREATE TABLE public.wh_warehouse_config (
     id integer NOT NULL,
     parameter_name varchar(255) NOT NULL,
     parameter_description varchar(255),
     parameter_value boolean NOT NULL DEFAULT false
);

ALTER TABLE ONLY public.wh_warehouse_config ADD CONSTRAINT wh_articles_pkey PRIMARY KEY (id);

BEGIN;
INSERT INTO public.wh_warehouse_config (id, parameter_name, parameter_value)
VALUES (1, 'allow_edit_transactions',true);

INSERT INTO public.wh_warehouse_config (id, parameter_name, parameter_value)
VALUES (2, 'allow_warehouse_sum_quantities',true);

INSERT INTO public.wh_warehouse_config (id, parameter_name, parameter_value)
VALUES (3, 'item_with_warehouse',true);

INSERT INTO public.wh_warehouse_config (id, parameter_name, parameter_value)
VALUES (4, 'auto_generate_codes',true);
COMMIT;