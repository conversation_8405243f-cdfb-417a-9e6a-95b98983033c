delete from su_contracts_plots_rel cpr where cpr.id in (
    select t.id
    from su_contracts c
    inner join lateral (
        select cpr.id as id
        from su_contracts_plots_rel cpr
        where cpr.contract_id = c.id
            and not exists (
                select sv.sublease_contract_id from subleases_view sv where sv.plot_id = cpr.plot_id and sv.sublease_contract_id = c.from_sublease
            )
    ) as t on true
    where c.from_sublease is not null
    group by t.id
)