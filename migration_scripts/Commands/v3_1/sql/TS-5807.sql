DROP MATERIALIZED VIEW IF EXISTS ekate_combobox;
CREATE MATERIALIZED VIEW public.ekate_combobox
TABLESPACE pg_default
AS SELECT
       kvs.ekate,
       e.ekatte_name,
       replace(regexp_replace(ST_Extent(kvs.geom)::text, '([BOX()])'::text, ''::text, 'g'::text), ' '::text, ','::text) AS extent
   FROM layer_kvs kvs
            LEFT JOIN dblink('host=127.0.0.1 port=5432 dbname=susi_main user=postgres password=6nuk23'::text, 'SELECT ekatte_name, ekatte_code FROM su_ekatte WHERE true'::text) e(ekatte_name character varying, ekatte_code character varying) ON e.ekatte_code::text = kvs.ekate::text
   WHERE kvs.ekate is not null
   GROUP BY kvs.ekate, e.ekatte_name
WITH DATA;