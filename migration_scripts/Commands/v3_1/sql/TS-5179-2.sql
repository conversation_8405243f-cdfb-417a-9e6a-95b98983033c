DROP FUNCTION IF EXISTS tf_json_flat_arr;
CREATE OR REPLACE FUNCTION public.tf_json_flat_arr(input json)
 RETURNS json
 LANGUAGE sql
AS $function$
	with
		value as (
			SELECT json_array_elements_text(input::json) as a
		),
		arrays as (
			select
				case is_json(a)
					when true then a::jsonb
					else jsonb_build_array(a)
				end b
			from value
		),
		elements as (
			select jsonb_array_elements(b::jsonb) c from arrays
		)
	select json_agg(c) from elements
	$function$
;
