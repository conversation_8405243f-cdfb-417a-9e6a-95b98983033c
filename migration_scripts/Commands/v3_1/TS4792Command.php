<?php

namespace TF\Commands\v3_1;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-4792 command run on all databases.
 */
class TS4792Command extends UserDbCommand
{
    private $pdo;
    private $notChangedColumns = ['id', 'gid', 'geom'];

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v3.1:TS-4792')
            ->setDescription('Alter work layers columns to varchar');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $this->pdo = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $columns = $this->getTableColumnsTypes();
        foreach ($columns as $column) {
            if (in_array($column['column_name'], $this->notChangedColumns)) {
                continue;
            }
            $sql = $this->pdo->prepare("ALTER TABLE {$column['table_name']} ALTER COLUMN {$column['column_name']} TYPE varchar(255) USING {$column['column_name']}::varchar;");
            $sql->execute();
        }
    }

    /**
     * @return array
     */
    private function getTableColumnsTypes()
    {
        $notChangedColumnsStr = implode("','", $this->notChangedColumns);
        $sql = $this->pdo->prepare("select table_name, column_name, data_type from information_schema.columns where table_name like 'layer_work_%' and column_name not in ('{$notChangedColumnsStr}');");
        $sql->execute();

        return $sql->fetchAll();
    }
}
