<?php

namespace TF\Commands\v3_1;

use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * TS-4897-1 command run on susi_main database.
 */
class TS4897_1Command extends BaseCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v3.1:TS-4897-1')
            ->setDescription('Alter table su_users_layers (susi_main) add column definitions');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . DEFAULT_DB_DATABASE . ' -f ' . __DIR__ . '/sql/TS-4897-1.sql', $return);
    }
}
