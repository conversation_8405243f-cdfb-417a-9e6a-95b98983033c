<?php

namespace TF\Commands\v3_1;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-5807 command run on all databases.
 */
class TS5807Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v3.1:TS-5807')
            ->setDescription('Add extent in ekate_combobox');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-5807.sql', $return);
    }
}
