<?php

namespace TF\Commands\v3_1;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-5587.
 */
class TS5587Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v3.1:TS-5587')
            ->setDescription('Create Warehouse loggable table into Technofarm DB`.');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-5587.sql', $return);
    }
}
