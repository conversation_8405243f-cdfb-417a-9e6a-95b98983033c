<?php

namespace TF\Commands\v3_1;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-4647-2 command run on all databases.
 */
class TS4897_2Command extends UserDbCommand
{
    public const COLUMNS_FOR_SKIPPING = ['gid', 'geom'];
    public const OVERWRITE = true;

    public const TRANSLATIONS = [
        'kad_ident' => 'Идентификатор',
        'ekate' => 'ЕКАТТЕ',
        'masiv' => 'Масив',
        'number' => 'Имот',
        'category' => 'Категория',
        'area_type' => 'НТП',
        'mestnost' => 'Местност',
        'name' => 'Име',
        'area' => 'Площ (дка)',
        'document_area' => 'Площ по документ (дка)',
        'allowable_area' => 'Площ по сечение (дка)',
        'used_area' => 'Използвана площ (дка)',
        'irrigated_area' => 'Поливна площ (дка)',
        'allow_prec' => 'Процент',
    ];

    private $usedDb;
    private $maindDb;
    private $layers;

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v3.1:TS-4897-2')
            ->setDescription('Fill su_users_layers definitions. Default OVERWRITE = false');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $start = time();
        $this->usedDb = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        if (!$this->maindDb) {
            $this->maindDb = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
        }
        if (empty($this->layers)) {
            $this->layers = $this->getLayers();
        }

        $userLayers = array_filter($this->layers, function ($layer) use ($userDb) {
            if ($layer['user_db'] === $userDb) {
                return true;
            }
        });

        foreach ($userLayers as $layer) {
            $columns = $this->getTableColumns($layer['table_name']);
            if (!empty($layer['definitions']) && !self::OVERWRITE) {
                continue;
            }
            $definitions = [];
            foreach ($columns as $col) {
                if (in_array($col, self::COLUMNS_FOR_SKIPPING)) {
                    continue;
                }

                $definitions[] = [
                    'col_name' => $col,
                    'col_title' => array_key_exists($col, self::TRANSLATIONS) ? self::TRANSLATIONS[$col] : $col,
                    'col_visible' => true,
                ];
            }

            $this->setLayerDefinitions($layer['layer_id'], $definitions);
        }
    }

    private function getLayers($layerType = 19)
    {
        $sql = $this->maindDb->prepare('
            SELECT 
                u.id as user_id, 
                u.database as user_db,
                ul.table_name as table_name, 
                ul.id as layer_id,
                ul.definitions as definitions
            from su_users_layers ul
            join su_users u on u.id = ul.user_id
            where ul.layer_type = :layerType 
            and u.active = true
            ');
        $sql->execute(['layerType' => $layerType]);

        return $sql->fetchAll(PDO::FETCH_ASSOC);
    }

    private function getTableColumns($tableName)
    {
        $sql = $this->usedDb->prepare("
            SELECT column_name
                FROM information_schema.columns 
                WHERE 
                    table_schema = 'public' 
                    AND table_name = :tableName
            ");

        $sql->execute(['tableName' => $tableName]);

        return $sql->fetchAll(PDO::FETCH_COLUMN);
    }

    private function setLayerDefinitions($layerId, $definitions)
    {
        $sql = $this->maindDb->prepare("
            UPDATE su_users_layers SET definitions = '" . json_encode($definitions) . "'
            WHERE id = {$layerId}
        ");

        $isUpdated = $sql->execute();

        if (!$isUpdated) {
            echo '=== Error in layer id:' . print_r($layerId, true) . PHP_EOL;
        } else {
            echo 'Edited layer id:' . print_r($layerId, true) . PHP_EOL;
        }
    }
}
