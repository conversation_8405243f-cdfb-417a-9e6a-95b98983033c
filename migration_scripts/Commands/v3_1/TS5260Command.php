<?php

namespace TF\Commands\v3_1;

use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * TS-5260 command run on susi_main database.
 */
class TS5260Command extends BaseCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v3.1:TS-5260')
            ->setDescription('Alter table su_users_farming (susi_main) rename column from star_rko to rko_number');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . DEFAULT_DB_DATABASE . ' -f ' . __DIR__ . '/sql/TS-5260.sql', $return);
    }
}
