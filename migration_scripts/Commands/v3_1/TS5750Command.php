<?php

namespace TF\Commands\v3_1;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-5750 command run on all databases.
 */
class TS5750Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v3.1:TS-5750')
            ->setDescription('Remove plots from а sublease copy if they not exist in the sublease');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-5750.sql', $return);
    }
}
