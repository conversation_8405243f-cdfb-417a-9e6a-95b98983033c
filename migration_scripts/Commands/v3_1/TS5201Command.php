<?php

namespace TF\Commands\v3_1;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-5201.
 */
class TS5201Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v3.1:TS-5201')
            ->setDescription('Create Warehouse transactions fields column`.');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-5201.sql', $return);
    }
}
