<?php

namespace TF\Commands\v3_1;

use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * TS-4955-1 command run on susi_main database.
 */
class TS4955_1Command extends BaseCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v3.1:TS-4955-1')
            ->setDescription('Alter column iban_arr in su_users_farming (susi_main) to jsonb');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . DEFAULT_DB_DATABASE . ' -f ' . __DIR__ . '/sql/TS-4955-1.sql', $return);
    }
}
