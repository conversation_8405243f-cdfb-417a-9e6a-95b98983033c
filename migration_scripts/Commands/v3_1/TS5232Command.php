<?php

namespace TF\Commands\v3_1;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-5232 command run on all databases.
 */
class TS5232Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v3.1:TS-5232')
            ->setDescription('Fixes `subleases_view` to show subleased plots which are part of both active and unactive contracts.');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-5232.sql', $return);
    }
}
