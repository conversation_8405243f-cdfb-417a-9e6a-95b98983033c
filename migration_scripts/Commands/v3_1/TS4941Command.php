<?php

namespace TF\Commands\v3_1;

use TF\Commands\Common\UserDbCommand;

class TS4941Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v3.1:TS-4941')
            ->setDescription('Added new jsonb column in su_collections table. Column name: payment_data');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-4941.sql', $return);
    }
}
