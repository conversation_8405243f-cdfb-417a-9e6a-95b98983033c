<?php

namespace TF\Commands\v3_1;

use TF\Commands\Common\UpdateSubleasesViewCommand;
use TF\Commands\Common\UserDbCommand;

/**
 * Runs all scripts required for migration from Version 3.0 to 3.1.
 */
class UpdateToVersion3_1 extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v3.1:update-to-version-3.1')
            ->setDescription('Runs all scripts required for migration from version 3.0 to version 3.1');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $commandsArr = [
            new TS4792Command(),
            new TS5163Command(),
            new TS5164Command(),
            new TS4942Command(),
            new TS5179_1_Command(),
            new TS5179_2_Command(),
            new TS4928Command(),
            new TS4955_2Command(),
            new TS5232Command(),
            new TS5233Command(),
            new UpdateSubleasesViewCommand(),
            new TS4941Command(),
            new TS5201Command(),
            new TS5750Command(),
            new TS5807Command(),
            new TS4897_1Command(),
            new TS4897_2Command(),
            new TS5892Command(),
        ];

        $count = count($commandsArr);
        for ($i = 0; $i < $count; $i++) {
            $command = $commandsArr[$i];
            $this->logScript($userDb, $command->getName());
            $output->writeln('Executing command: ' . $command->getName());
            $command->onDbExecute($userDb, $output, $input);
        }
    }

    protected function logScript($user_db, $name)
    {
        $date = date('Y-m-d H:i:s');

        $this->openConnection();

        $sql = $this->mainConnection->prepare("
            INSERT INTO scripts_log (db_name, name, date)
            VALUES ('{$user_db}', '{$name}', '{$date}')
        ");

        return $sql->execute();
    }
}
