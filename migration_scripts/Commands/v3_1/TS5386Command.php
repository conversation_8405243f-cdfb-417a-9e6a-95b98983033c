<?php

namespace TF\Commands\v3_1;

use TF\Commands\Common\UserDbCommand;

class TS5386Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v3.1:TS-5386')
            ->setDescription('Create Warehouse config table into Technofarm DB`.');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-5386.sql', $return);
    }
}
