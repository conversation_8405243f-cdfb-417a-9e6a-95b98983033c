<?php

namespace TF\Commands\sprint_s05;

use PDO;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Commands\ParamHelper;

/**
 * TS-539 command run on all databases.
 */
class TS539Command extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('sprint_s05:TS-539')
            ->setDescription('setva nqkoi defaultni stoinosti na zapisi za sloeve zaIsak v su_users_layers')
            ->addArgument(
                'user_databases',
                InputArgument::IS_ARRAY | InputArgument::OPTIONAL,
                'Databases to run the script on (separate multiple names with a space)?'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $user_databases = $input->getArgument('user_databases');

        $layers = [
            0 => [
                'name' => '������������������� ������',
                'type' => 10,
                'table_name' => 'layer_lfa',
                'color' => '0000ff',
            ],
            1 => [
                'name' => '������ 2000',
                'type' => 11,
                'table_name' => 'layer_natura_2000',
                'color' => '00ff00',
            ],
            2 => [
                'name' => '��������� ��������� �����',
                'type' => 12,
                'table_name' => 'layer_pzp',
                'color' => 'ff0000',
            ],
        ];

        $dbhDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $paramHelper = new ParamHelper();
        $userDbString = $paramHelper->createQueryParamString($user_databases);

        $userDbConditions = 'true';
        if ($user_databases) {
            $userDbConditions = 'u.database IN (' . $userDbString . ')';
        }
        $sql = $dbhDev->prepare("select t.user_id , f.id as farm_id from su_users_layers t
		 join su_users_farming f on (f.user_id = t.user_id)
		 left join su_users u on (t.user_id = u.id) 
		 where {$userDbConditions} AND f.is_system = true GROUP BY t.user_id, f.id ORDER BY user_id");
        $sql->execute();
        // var_export($sql->errorInfo());
        $results = $sql->fetchAll();
        foreach ($results as $user) {
            $output->writeln($user[0] . '');
            foreach ($layers as $layer) {
                $sql = $dbhDev->prepare('INSERT INTO su_users_layers
        	(user_id, name, table_name, date_created, color, border_color, extent, farming, year,transparency, position, layer_type, group_id, tags, border_only) values (' . $user[0] . ", '" . $layer['name'] . "', '" . $layer['table_name'] . "','2015-03-23 16:42:21.372834', '" . $layer['color'] . "', '111111', '125190.6162 4573142.7188 631370.3273 4887149.5823', " . $user[1] . ', 6, 100, 0, ' . $layer['type'] . ', ' . $user[0] . ', true, false);');
                $sql->execute();
            }
        }
    }
}
