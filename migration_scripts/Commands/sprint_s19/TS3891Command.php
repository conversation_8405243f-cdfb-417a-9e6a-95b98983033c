<?php

namespace TF\Commands\sprint_s19;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-3891 command run on databases that dont have the cooperator files table.
 */
class TS3891Command extends UserDbCommand
{
    protected $userDev;

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s19:TS-3891')
            ->setDescription('Sets used_area = document_area, where used_area is null');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-3891.sql', $return);
    }
}
