<?php

namespace TF\Commands\sprint_s19;

use PDO;
use Symfony\Component\Console\Input\InputOption;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-3967 command run on all databases.
 */
class TS3967Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s19:TS-3967')
            ->setDescription('Check if su_sublease_contract_plots_rel has foreign key to su_contracts_plots_rel; also must run on the new_users_table')
            ->addOption('add_foreign_key', null, InputOption::VALUE_NONE, 'adds a foregn key to su_contracts_plots_rel');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $userDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = $userDev->prepare(
            "SELECT
                *
            FROM
                information_schema.table_constraints
            WHERE
                constraint_type = 'FOREIGN KEY'
            AND TABLE_NAME = 'su_subleases_plots_contracts_rel'
            and constraint_name like '%pc_rel_id%'"
        );

        $sql->execute();
        $records = $sql->fetchAll();

        if (!count($records)) {
            $output->writeln("{$userDb} does not have foreign key to su_contracts_plots_rel ");

            // if the flag for adding foreign key is up
            if ($input->getOption('add_foreign_key')) {
                // /drop records that dont have existing cp_rel
                $this->deleteRecordsWithoutRelation($userDev);

                // add the foreign key
                $this->addForeignKey($userDev);
            }
        }
    }

    private function deleteRecordsWithoutRelation($userDev)
    {
        $sql = $userDev->prepare(
            'DELETE from su_subleases_plots_contracts_rel where pc_rel_id not in (SELECT id from su_contracts_plots_rel);'
        );

        $sql->execute();
    }

    private function addForeignKey($userDev)
    {
        $sql = $userDev->prepare(
            'ALTER TABLE su_subleases_plots_contracts_rel ADD FOREIGN KEY (pc_rel_id) REFERENCES su_contracts_plots_rel (ID) ON DELETE CASCADE ON UPDATE NO ACTION;'
        );

        $sql->execute();
    }
}
