<?php

namespace TF\Commands\sprint_s19;

use Prado;
use Symfony\Component\Console\Input\ArgvInput;
use Symfony\Component\Console\Output\ConsoleOutput;
use TF\Commands\Common\UserDbCommand;
use TF\Commands\sprint_s16\TS3132Command;
use TF\Commands\sprint_s17\TS2983Command;
use UserDbController;

Prado::using('Plugins.Core.UserDb.*');

class TS3768Command extends UserDbCommand
{
    protected $userDb;

    protected function configure()
    {
        parent::configure();
        $this->setName('sprint_s19:TS-3768')->setDescription('Check andd add the missing allowable_area, allowable_type and rent_per_plot columns.');
    }

    /**
     * @param ConsoleOutput $output
     * @param ArgvInput $input
     */
    protected function onDbExecute($database, $output, $input)
    {
        $userController = new UserDbController($database);
        $isAllowableAreaExists = $userController->DbHandler->getColumnNameExist($database, 'layer_kvs', 'allowable_area');
        $isAllowableTypeExists = $userController->DbHandler->getColumnNameExist($database, 'layer_kvs', 'allowable_type');

        if (!($isAllowableAreaExists && $isAllowableTypeExists)) {
            $command = new TS3132Command();
            $this->info("Script: {$command->getName()}", $output);
            $command->onDbExecute($database, $output, $input);
        }

        $isRentPerPlotExists = $userController->DbHandler->getColumnNameExist($database, 'su_contracts_plots_rel', 'rent_per_plot');
        if (!$isRentPerPlotExists) {
            $command = new TS2983Command();
            $this->info("Script: {$command->getName()}", $output);
            $command->onDbExecute($database, $output, $input);
        }
    }
}
