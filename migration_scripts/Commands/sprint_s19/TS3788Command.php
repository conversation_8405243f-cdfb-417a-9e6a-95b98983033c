<?php

namespace TF\Commands\sprint_s19;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-3788 command run on databases that dont have the cooperator files table.
 */
class TS3788Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s19:TS-3788')
            ->setDescription('suzdava tablica su_cooperators_documents v bazite, kadeto lipsva');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-3788.sql', $return);
    }
}
