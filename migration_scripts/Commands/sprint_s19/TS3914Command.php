<?php

namespace TF\Commands\sprint_s19;

use PDO;
use Symfony\Component\Console\Formatter\OutputFormatterStyle;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-3914 command run on all databases.
 */
class TS3914Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s19:TS-3914')
            ->setDescription('obnovqvq koeficienta na azotfiksirashtite kulturi v tablicite za isak');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $style = new OutputFormatterStyle('white', 'green');
        $output->getFormatter()->setStyle('success', $style);
        $style = new OutputFormatterStyle('black', 'yellow');
        $output->getFormatter()->setStyle('error', $style);

        $userDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = $userDev->prepare(
            "SELECT table_name FROM information_schema.tables WHERE table_name LIKE '%layer_for_isak%'"
        );

        $sql->execute();
        $tables = $sql->fetchAll();

        if (count($tables) > 0) {
            foreach ($tables as $table) {
                $updateSQL = "UPDATE \"public\".\"{$table['table_name']}\" set green_area_factor = {$GLOBALS['Farming']['crops_short_type']['azot_fixed_crop']['factor_enp']} where azot_fixed_crop = true;";
                $updateCMD = $userDev->prepare($updateSQL);
                $updateCMD->execute();

                $output->writeln("<success>{$userDb}: {$table['table_name']} updated</success>");
            }
        }
    }
}
