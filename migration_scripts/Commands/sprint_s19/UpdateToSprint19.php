<?php

namespace TF\Commands\sprint_s19;

use TF\Commands\Common\UserDbCommand;

/**
 * Runs all scripts required for migration from Sprint 18 to Sprint 19.
 */
class UpdateToSprint19 extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s19:update-to-sprint19')
            ->setDescription('Runs all scripts required for migration from Sprint 18 to Sprint 19.');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $commandsArr = [
            new TS3716Command(),
            // Sprint 19
            new TS3620Command(),
            new TS3335Command(),
            new TS3914Command(),
        ];

        $count = count($commandsArr);
        for ($i = 0; $i < $count; $i++) {
            $command = $commandsArr[$i];
            $this->logScript($userDb, $command->getName());
            $output->writeln('Executing command: ' . $command->getName());
            $command->onDbExecute($userDb, $output, $input);
        }
    }

    protected function logScript($user_db, $name)
    {
        $date = date('Y-m-d H:i:s');

        $this->openConnection();

        $sql = $this->mainConnection->prepare("
            INSERT INTO scripts_log (db_name, name, date)
            VALUES ('{$user_db}', '{$name}', '{$date}')
        ");

        return $sql->execute();
    }
}
