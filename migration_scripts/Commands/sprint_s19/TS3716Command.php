<?php

namespace TF\Commands\sprint_s19;

use PDO;
use PDOStatement;
use Prado;
use Symfony\Component\Console\Input\ArgvInput;
use Symfony\Component\Console\Output\ConsoleOutput;
use TF\Commands\Common\UserDbCommand;
use UserDbController;

Prado::using('Plugins.Core.UserDb.*');

/**
 * Class TS3716Command.
 */
class TS3716Command extends UserDbCommand
{
    public const PLANT_PROTECTION_PRODUCTS = 7;

    protected $mainDev;
    protected $userDev;
    protected $userDbController;

    protected function configure()
    {
        parent::configure();
        $this->setName('sprint_s19:TS-3716')->setDescription('remove is_chemical treatment_column and add new json column options to store more data');
    }

    /**
     * @param string $database
     * @param ConsoleOutput $output
     * @param ArgvInput $input
     */
    protected function onDbExecute($database, $output, $input)
    {
        $this->userDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $database . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $this->userDbController = new UserDbController($database);

        // 1) Add new column substance_consumed

        $exist = (bool)$this->userDbController->checkColumnNameExist('su_diary_treatments_products', 'substance_consumed');

        if (!$exist) {
            $created = $this->addColumn('su_diary_treatments_products', 'substance_consumed', 'float8');
            if ($created) {
                $output->writeln("column 'substance_consumed' created successfully on database: {$database}");
            } else {
                $output->writeln("ERROR: column 'substance_consumed' not created on database: {$database}");
            }
        } else {
            $output->writeln("INFO: column 'substance_consumed' already exist on database: {$database}");
        }

        // 2) Check if column options exists, if not add it
        $exist = (bool)$this->userDbController->checkColumnNameExist('su_diary_configs', 'options');

        if (!$exist) {
            $created = $this->addColumn('su_diary_configs', 'options', 'json');
            if ($created) {
                $output->writeln("column 'options' created successfully on database: {$database}");
            } else {
                $output->writeln("ERROR: column 'options' not created on database: {$database}");
            }
        } else {
            $output->writeln("INFO: column 'options' already exist on database: {$database}");
        }
        $exist = (bool)$this->userDbController->checkColumnNameExist('su_diary_configs', 'is_chemical_treatment');
        if (!$exist) {
            $output->writeln("INFO: {$database} already migrated");

            return;
        }
        // 3) COPY OLD config settings to new column options and delete old column is_chemical_treatment
        $chemical_products = $this->userDbController->getItemsByParams([
            'tablename' => $this->userDbController->DbHandler->tableDiaryConfigs,
            'where' => [
                'config_id' => ['column' => 'config_type', 'compare' => '=', 'value' => self::PLANT_PROTECTION_PRODUCTS],
            ],
        ]);

        foreach ($chemical_products as $item) {
            $options = [];
            $json = [];

            $json['chemical_treatment'] = $item['is_chemical_treatment'];
            $item['options'] = json_encode($json);
            $options['mainData'] = $item;
            $options['mainData']['is_chemical_treatment'] = (true == $options['mainData']['is_chemical_treatment']) ? 't' : 'f';
            $options['where'] = ['id' => $item['id']];
            $options['tablename'] = $this->userDbController->DbHandler->tableDiaryConfigs;
            $options['return'] = ['*'];
            $saved = $this->userDbController->editItem($options);
            $id = $item['id'];
            if ($saved) {
                $output->writeln("item with id {$id} updated");
            } else {
                $output->writeln("item with id {$id} could not be updated");
            }
        }
        $deleted = $this->dropColumn('su_diary_configs', 'is_chemical_treatment');
        if ($deleted) {
            $output->writeln("column 'is_chemical_treatment' removed correctly from database: {$database}");
        } else {
            $output->writeln("ERROR: column 'is_chemical_treatment' could not be removed from database: {$database}");
        }
    }

    protected function addColumn($tablename, $column, $type)
    {
        $sql = "ALTER TABLE {$tablename} ADD COLUMN {$column} {$type} DEFAULT NULL;";
        $cmd = $this->userDev->prepare($sql);

        return $cmd->execute();
    }

    protected function dropColumn($tablename, $column)
    {
        $sql = "ALTER TABLE {$tablename} DROP COLUMN IF EXISTS {$column};";
        $cmd = $this->userDev->prepare($sql);

        return $cmd->execute();
    }

    /**
     * @return bool
     *
     * @deprecated
     */
    protected function checkCustomTypeExist($typeName)
    {
        $sql = 'SELECT 1 FROM pg_type WHERE typname = :typename;';
        $cmd = $this->userDev->prepare($sql);
        $cmd->bindValue(':typename', $typeName);
        $cmd->execute();

        return !empty($cmd->fetchAll(PDO::FETCH_ASSOC));
    }

    /**
     * @deprecated
     */
    protected function createCustomType($typeName)
    {
        /** @var PDOStatement $cmd */
        $sql = "CREATE TYPE {$typeName} AS ENUM('fertilizer','chemical_treatment');";
        $cmd = $this->userDev->prepare($sql);

        return $cmd->execute();
    }
}
