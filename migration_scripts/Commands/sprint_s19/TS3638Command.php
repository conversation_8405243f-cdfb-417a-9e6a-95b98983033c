<?php

namespace TF\Commands\sprint_s19;

use PD<PERSON>;
use <PERSON>OEx<PERSON>;
use Prado;
use S<PERSON>fony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Commands\ParamHelper;

Prado::using('Plugins.Core.UserDb.*');
Prado::using('Plugins.Core.Layers.*');

/**
 * TS-515 command run on all databases.
 */
class TS3638Command extends BaseCommand
{
    private $mainDev;
    private $userDev;

    private $commands = [
        '3197' => null,
        '3198' => null,
        '3198_1' => null,
    ];

    protected function configure()
    {
        $this
            ->setName('sprint_s19:TS-3638')
            ->setDescription('check for migration 3197,3198 and 3198_1 in each user database')
            ->addArgument(
                'user_databases',
                InputArgument::IS_ARRAY | InputArgument::OPTIONAL,
                'Databases to run the script on (separate multiple names with a space)?'
            );

        $this->commands['3197'] = new \TF\Commands\sprint_s17\TS3197Command();
        $this->commands['3198'] = new \TF\Commands\sprint_s17\TS3198Command();
        $this->commands['3198_1'] = new \TF\Commands\sprint_s17\TS3198_1_Command();
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $output->writeln('Command started at : ' . date('H:i:s d-M-Y') . PHP_EOL);

        $userDbParam = $input->getArgument('user_databases');

        $paramHelper = new ParamHelper();
        $userDbParam = $paramHelper->createQueryParamString($userDbParam);

        $whereDbParam = 'true';

        if ('' !== $userDbParam) {
            $whereDbParam = 'u.database IN (' . $userDbParam . ')';
        }

        $this->mainDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = $this->mainDev->prepare("SELECT u.id AS user_id, datname AS database FROM pg_database d INNER JOIN su_users u ON d.datname = u.\"database\" WHERE datistemplate = FALSE AND u. LEVEL = 2 AND {$whereDbParam}");

        $sql->execute();

        $users = $sql->fetchAll(PDO::FETCH_ASSOC);
        $output->writeln('');

        foreach ($users as $index => $user) {
            $database = $user['database'];

            $output->writeln("{$index}) Checking Database: {$database}");

            try {
                $this->userDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $database . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
            } catch (PDOException $exception) {
                $output->writeln("{$index}) ERROR: {$exception->getMessage()}");

                continue;
            }

            $table_exist = $this->getDiaryProductTable();
            $fKey_exist = $this->checkFKonEventTable();
            if (empty($table_exist) || 0 == $fKey_exist) {
                $this->executeMigration($input, $output, $index, $database, $this->commands['3197']);
            }

            $units_exist = $this->getUnitsDiaryConfig();
            if (empty($units_exist)) {
                $this->executeMigration($input, $output, $index, $database, $this->commands['3198']);
            }

            $column_exist = $this->getColumnEventsTable();
            if (0 == $column_exist) {
                $this->executeMigration($input, $output, $index, $database, $this->commands['3198_1']);
            }
        }
        $output->writeln(PHP_EOL . 'Command ended at : ' . date('H:i:s d-M-Y'));
    }

    /**
     * @return array
     */
    protected function getDiaryProductTable()
    {
        $sql = $this->userDev->prepare("SELECT * FROM information_schema.tables WHERE table_name = 'su_diary_treatments_products'");
        $sql->execute();

        return $sql->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * @return array
     */
    protected function getUnitsDiaryConfig()
    {
        $sql = $this->userDev->prepare('SELECT * FROM su_diary_configs WHERE config_type = 10');
        $sql->execute();

        return $sql->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * @return int
     */
    protected function getColumnEventsTable()
    {
        $sql = $this->userDev->prepare("SELECT count(*) FROM information_schema.columns WHERE table_schema='public' AND table_name='su_diary_events' AND column_name='fuel_unit';");
        $sql->execute();
        $result = $sql->fetchAll(PDO::FETCH_ASSOC);

        return (int)$result[0]['count'];
    }

    protected function checkFKonEventTable()
    {
        $sql = $this->userDev->prepare("SELECT COUNT(*) FROM information_schema.table_constraints WHERE constraint_name='treatments__on_event_id_fk' AND table_name='su_diary_treatments_products';");
        $sql->execute();
        $result = $sql->fetchAll(PDO::FETCH_ASSOC);

        return (int)$result[0]['count'];
    }

    protected function executeMigration(InputInterface $input, OutputInterface $output, $index, $database, $command)
    {
        $output->writeln($index . ') Executing command: ' . $command->getName() . ' on Database: ' . $database);
        $return = $command->onDbExecute($database, $output, $input);
        $output->writeln('command result: ' . (0 === $return) ? 'OK' . PHP_EOL : print_r($return, true) . PHP_EOL);

        return $return;
    }
}
