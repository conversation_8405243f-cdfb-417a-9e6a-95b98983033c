<?php

namespace TF\Commands\sprint_s19;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-3895 command run on databases that dont have the cooperator files table.
 */
class TS3895Command extends UserDbCommand
{
    protected $userDev;

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s19:TS-3895')
            ->setDescription('Checks for missing sequences in the work_layer tables and add them if so');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $this->userDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = $this->userDev->prepare("
        SELECT
            relname as table_name,
            nspname || '.' || relname AS full_rel_name,
            pg_get_serial_sequence (
                nspname || '.' || relname,
                'gid'
            )
        FROM
            pg_class,
            pg_namespace
        WHERE
            relnamespace = pg_namespace.oid
        AND nspname = 'public'
        AND relkind = 'r'
        AND relname ILIKE 'layer_work%'");

        $sql->execute();

        // tables and sequences
        $result = $sql->fetchAll(PDO::FETCH_ASSOC);

        $primaryKeys = $this->getPrimaryKeys();

        foreach ($result as $table) {
            if (null == $table['pg_get_serial_sequence']) {
                // will create new sequence if there is none with that name
                $sql = "CREATE SEQUENCE {$table['table_name']}_gid_seq
                START WITH 1
                INCREMENT BY 1
                NO MINVALUE
                NO MAXVALUE
                CACHE 1
                OWNED BY {$table['table_name']}.gid";

                $cmd = $this->userDev->prepare($sql);
                $cmd->execute();

                // will add owner to the sequence if there
                $sql = "ALTER SEQUENCE {$table['table_name']}_gid_seq OWNED BY {$table['table_name']}.gid";
                $cmd = $this->userDev->prepare($sql);
                $cmd->execute();

                // add the sequence to the table
                $sql = "ALTER TABLE ONLY {$table['table_name']} ALTER COLUMN gid SET DEFAULT nextval('" . $table['table_name'] . "_gid_seq'::regclass)";
                $cmd = $this->userDev->prepare($sql);
                $cmd->execute();

                $output->writeln("adding sequence to : {$table['table_name']}");

                // if there is no primary key - add one.
                if (null == $primaryKeys[$table['table_name']]['constraint_name']) {
                    $sql = 'ALTER TABLE ONLY ' . $table['table_name'] . ' ADD CONSTRAINT "' . $table['table_name'] . '_pk" PRIMARY KEY (gid)';
                    $cmd = $this->userDev->prepare($sql);
                    $cmd->execute();

                    $output->writeln("adding primary key : {$table['table_name']}");
                }

                // if there is no geometry index - add one.
                $sql = "CREATE INDEX IF NOT EXISTS {$table['table_name']}_gist ON {$table['table_name']} USING gist (geom);";
                $cmd = $this->userDev->prepare($sql);
                $cmd->execute();
            }
        }
    }

    private function getPrimaryKeys()
    {
        $sql = "SELECT  t.table_catalog,
                t.table_schema,
                t.table_name,
                kcu.constraint_name,
                kcu.column_name,
                kcu.ordinal_position
        FROM    INFORMATION_SCHEMA.TABLES t
                LEFT JOIN INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
                        ON tc.table_catalog = t.table_catalog
                        AND tc.table_schema = t.table_schema
                        AND tc.table_name = t.table_name
                        AND tc.constraint_type = 'PRIMARY KEY'
                LEFT JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
                        ON kcu.table_catalog = tc.table_catalog
                        AND kcu.table_schema = tc.table_schema
                        AND kcu.table_name = tc.table_name
                        AND kcu.constraint_name = tc.constraint_name
        WHERE   t.table_schema NOT IN ('pg_catalog', 'information_schema')
        AND t.table_name ILIKE 'layer_work%'
        ORDER BY t.table_catalog,
                t.table_schema,
                t.table_name,
                kcu.constraint_name,
                kcu.ordinal_position;";

        $cmd = $this->userDev->prepare($sql);
        $cmd->execute();

        $results = $cmd->fetchAll(PDO::FETCH_ASSOC);

        $formattedResult = [];
        foreach ($results as $key => $value) {
            $formattedResult[$value['table_name']] = $value;
        }

        return $formattedResult;
    }
}
