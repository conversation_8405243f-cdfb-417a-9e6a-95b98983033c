<?php

namespace TF\Commands\sprint_s19;

use PDO;
use PDOStatement;
use Prado;
use Symfony\Component\Console\Input\ArgvInput;
use Symfony\Component\Console\Output\ConsoleOutput;
use TF\Commands\Common\UserDbCommand;
use UserDbController;

Prado::using('Plugins.Core.UserDb.*');

/**
 * Class TS3620Command.
 */
class TS3620Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this->setName('sprint_s19:TS-3620')->setDescription('Add column "name" to all work layers table');
    }

    /**
     * @param ConsoleOutput $output
     * @param ArgvInput $input
     */
    protected function onDbExecute($database, $output, $input)
    {
        $userDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $database . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
        $workLayersTables = $this->getWorkLayersTables($userDev);

        if (empty($workLayersTables)) {
            $this->displayErrorMessage('no work table found for db ' . $database, $output, 'black', 'yellow');
        }

        $userController = new UserDbController($database);

        foreach ($workLayersTables as $index => $workLayerTable) {
            $tableName = $workLayerTable['tablename'];
            $columnNameExist = (int)$userController->DbHandler->getColumnNameExist($database, $tableName, 'name');

            if ($columnNameExist) {
                $output->writeln("{$index} Database.Table : {$database} {$tableName} ok");

                continue;
            }
            $output->writeln("{$index}) adding column name on Database.Table : {$database} {$tableName}");
            $userController->DbHandler->addColumn($tableName, 'name');
        }
    }

    /**
     * @return array
     */
    protected function getWorkLayersTables(PDO $userDev)
    {
        /** @var PDOStatement $sql */
        $sql = $userDev->prepare("SELECT tablename FROM pg_catalog.pg_tables WHERE schemaname = 'public' AND tablename LIKE '%layer_work%'");
        $sql->execute();

        return $sql->fetchAll(PDO::FETCH_ASSOC);
    }
}
