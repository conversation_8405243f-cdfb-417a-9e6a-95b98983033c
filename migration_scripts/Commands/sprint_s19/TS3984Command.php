<?php

namespace TF\Commands\sprint_s19;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-3984 command run on all databases.
 */
class TS3984Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s19:TS-3984')
            ->setDescription('Alter column name on all work layer tables on user\'s databases to be type varchar');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $userDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = $userDev->prepare(
            "SELECT TABLE_NAME, COLUMN_NAME, data_type
            FROM
                information_schema. COLUMNS
            WHERE
            TABLE_NAME like 'layer_work_%'
            AND COLUMN_NAME = 'name'
            and data_type != 'character varying'"
        );

        $sql->execute();
        $records = $sql->fetchAll();
        if (!count($records)) {
            $output->writeln("{$userDb} does not have work layers table with column name with float type");

            return false;
        }
        $output->writeln("migration running for {$userDb}");
        print_r($records);

        foreach ($records as $column) {
            $tablename = $column['table_name'];
            $output->writeln("alter column name on table {$tablename} of user {$userDb}");
            $sql = "ALTER TABLE {$tablename} ALTER COLUMN name TYPE varchar(255) USING (name::varchar(255));";
            $cmd = $userDev->prepare($sql);
            $cmd->execute();
        }
    }
}
