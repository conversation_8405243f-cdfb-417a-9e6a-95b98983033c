CREATE TABLE "public"."su_cooperators_documents" (
"id" int4 DEFAULT nextval('su_cooperators_documents_id_seq'::regclass) NOT NULL,
"type_id" int4,
"number" varchar(255) COLLATE "default",
"date" timestamp(6),
"cooperator_id" int4
)
WITH (OIDS=FALSE)

;

-- ----------------------------
-- Alter Sequences Owned By 
-- ----------------------------

-- ----------------------------
-- Primary Key structure for table su_cooperators_documents
-- ----------------------------
ALTER TABLE "public"."su_cooperators_documents" ADD PRIMARY KEY ("id");

-- ----------------------------
-- Foreign Key structure for table "public"."su_cooperators_documents"
-- ----------------------------
ALTER TABLE "public"."su_cooperators_documents" ADD FOREIGN KEY ("cooperator_id") REFERENCES "public"."su_cooperators" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;