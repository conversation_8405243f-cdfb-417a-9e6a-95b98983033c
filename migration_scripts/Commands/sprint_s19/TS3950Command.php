<?php

namespace TF\Commands\sprint_s19;

use PDO;
use Prado;
use TF\Commands\Common\UserDbCommand;
use UserDbController;

Prado::using('Plugins.Core.UserDb.*');
/**
 * TS-3950 command run on all databases.
 */
class TS3950Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s19:TS-3950')
            ->setDescription('Removes layer records from the su_users_layers table for work layers whose table does not exist');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $userDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
        $mainDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = $mainDev->prepare(
            "SELECT *
            FROM
            su_users u
                join su_users_layers ul on (u.id = ul.user_id)
            WHERE
            layer_type = 19
            and database = '{$userDb}'"
        );

        $sql->execute();
        $workLayersRecords = $sql->fetchAll();

        $tableRecordsToDelete = [];
        $userController = new UserDbController($userDb);
        // check for work layer existance
        foreach ($workLayersRecords as $record) {
            $workLayerTableExists = $userController->getTableNameExist($record['table_name']);

            if (!$workLayerTableExists) {
                $tableRecordsToDelete[] = '\'' . $record['table_name'] . '\'';
            }
        }

        if (!empty($tableRecordsToDelete)) {
            $tableString = implode(',', $tableRecordsToDelete);

            $output->writeln('deleting ' . $tableString);

            $sql = $mainDev->prepare(
                "DELETE
                FROM
                su_users_layers ul
                WHERE
                layer_type = 19
                and table_name in ({$tableString})"
            );

            $sql->execute();
        }
    }
}
