<?php

namespace TF\Commands\sprint_s19;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-3431 command run on all databases.
 */
class TS3335Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s19:TS-3335')
            ->setDescription('suzdava tablica su_alert_settings s nastriki za alarmi');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-3335.sql', $return);
    }
}
