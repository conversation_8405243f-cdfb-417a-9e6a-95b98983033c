--
-- PostgreSQL database dump
--

-- Dumped from database version 9.5.14
-- Dumped by pg_dump version 9.5.14

SET statement_timeout = 0;
SET lock_timeout = 0;
SET client_encoding = 'SQL_ASCII';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: plpgsql; Type: EXTENSION; Schema: -; Owner:
--

CREATE EXTENSION IF NOT EXISTS plpgsql WITH SCHEMA pg_catalog;


--
-- Name: EXTENSION plpgsql; Type: COMMENT; Schema: -; Owner:
--

COMMENT ON EXTENSION plpgsql IS 'PL/pgSQL procedural language';


--
-- Name: articles_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.articles_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.articles_id_seq OWNER TO postgres;

--
-- Name: companies_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.companies_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.companies_id_seq OWNER TO postgres;

--
-- Name: company_groups_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.company_groups_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.company_groups_id_seq OWNER TO postgres;

--
-- Name: company_type_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.company_type_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.company_type_id_seq OWNER TO postgres;

--
-- Name: documents_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.documents_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.documents_id_seq OWNER TO postgres;

--
-- Name: item_groups_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.item_groups_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.item_groups_id_seq OWNER TO postgres;

--
-- Name: items_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.items_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.items_id_seq OWNER TO postgres;

--
-- Name: measures_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.measures_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.measures_id_seq OWNER TO postgres;

--
-- Name: sequences_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.sequences_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.sequences_id_seq OWNER TO postgres;

--
-- Name: transactions_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.transactions_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.transactions_id_seq OWNER TO postgres;

--
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.users_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.users_id_seq OWNER TO postgres;

--
-- Name: warehouses_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.warehouses_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.warehouses_id_seq OWNER TO postgres;

SET default_tablespace = '';

SET default_with_oids = false;

--
-- Name: wh_articles; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.wh_articles (
    id integer NOT NULL,
    item_id integer,
    warehouse_id integer,
    company_id integer,
    quantity numeric(10,2) NOT NULL,
    price_with_dds numeric(10,2) DEFAULT NULL::numeric,
    price_no_dds numeric(10,2) DEFAULT NULL::numeric,
    batch character varying(255) DEFAULT NULL::character varying,
    code character varying(255) NOT NULL,
    fields json,
    created_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


ALTER TABLE public.wh_articles OWNER TO postgres;

--
-- Name: COLUMN wh_articles.fields; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.wh_articles.fields IS '(DC2Type:json)';


--
-- Name: wh_companies; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.wh_companies (
    id integer NOT NULL,
    type_id integer,
    group_id integer,
    name character varying(255) NOT NULL,
    print_name character varying(255) DEFAULT NULL::character varying,
    code character varying(255) NOT NULL,
    fields json,
    created_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


ALTER TABLE public.wh_companies OWNER TO postgres;

--
-- Name: COLUMN wh_companies.fields; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.wh_companies.fields IS '(DC2Type:json)';


--
-- Name: wh_company_groups; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.wh_company_groups (
    id integer NOT NULL,
    parent_id integer,
    name character varying(80) NOT NULL
);


ALTER TABLE public.wh_company_groups OWNER TO postgres;

--
-- Name: wh_company_type; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.wh_company_type (
    id integer NOT NULL,
    name character varying(80) NOT NULL,
    fields json
);


ALTER TABLE public.wh_company_type OWNER TO postgres;

--
-- Name: COLUMN wh_company_type.fields; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.wh_company_type.fields IS '(DC2Type:json)';


--
-- Name: wh_documents; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.wh_documents (
    id integer NOT NULL,
    company_in integer,
    company_out integer,
    user_id integer,
    type character varying(255) NOT NULL,
    transaction_type character varying(255) NOT NULL,
    date date NOT NULL,
    number character varying(255) NOT NULL,
    vehicle_number character varying(255) DEFAULT NULL::character varying,
    driver_name character varying(255) DEFAULT NULL::character varying,
    created_at timestamp(0) without time zone NOT NULL,
    closed_at timestamp(0) without time zone DEFAULT NULL::timestamp without time zone,
    updated_at timestamp(0) without time zone NOT NULL,
    dds_type character varying(255) NOT NULL,
    plot_name character varying(255) DEFAULT NULL::character varying,
    area character varying(255) DEFAULT NULL::character varying
);


ALTER TABLE public.wh_documents OWNER TO postgres;

--
-- Name: wh_item_groups; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.wh_item_groups (
    id integer NOT NULL,
    parent_id integer,
    name character varying(80) NOT NULL
);


ALTER TABLE public.wh_item_groups OWNER TO postgres;

--
-- Name: wh_items; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.wh_items (
    id integer NOT NULL,
    group_id integer,
    measure_id integer,
    warehouse_id integer,
    name character varying(255) NOT NULL,
    code character varying(255) DEFAULT NULL::character varying,
    print_name character varying(255) DEFAULT NULL::character varying,
    min_quantity character varying(255) DEFAULT NULL::character varying
);


ALTER TABLE public.wh_items OWNER TO postgres;

--
-- Name: wh_measures; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.wh_measures (
    id integer NOT NULL,
    name character varying(32) NOT NULL,
    short_name character varying(32) NOT NULL
);


ALTER TABLE public.wh_measures OWNER TO postgres;

--
-- Name: wh_sequences; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.wh_sequences (
    id integer NOT NULL,
    type character varying(255) NOT NULL,
    number integer NOT NULL
);


ALTER TABLE public.wh_sequences OWNER TO postgres;

--
-- Name: wh_transactions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.wh_transactions (
    id integer NOT NULL,
    item_id integer,
    document_id integer,
    warehouse_id integer,
    article_id integer,
    type character varying(255) NOT NULL,
    number character varying(255) NOT NULL,
    quantity numeric(10,2) NOT NULL,
    price_with_dds numeric(10,2) DEFAULT NULL::numeric,
    price_no_dds numeric(10,2) DEFAULT NULL::numeric,
    created_at timestamp(0) without time zone NOT NULL,
    note character varying(255) DEFAULT NULL::character varying,
    batch character varying(255) DEFAULT NULL::character varying,
    expiry_date date
);


ALTER TABLE public.wh_transactions OWNER TO postgres;

--
-- Name: wh_users; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.wh_users (
    id integer NOT NULL,
    username character varying(180) NOT NULL,
    name character varying(180) DEFAULT NULL::character varying,
    roles json NOT NULL,
    password character varying(255) NOT NULL
);


ALTER TABLE public.wh_users OWNER TO postgres;

--
-- Name: COLUMN wh_users.roles; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.wh_users.roles IS '(DC2Type:json)';


--
-- Name: wh_warehouse_company; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.wh_warehouse_company (
    warehouse_id integer NOT NULL,
    company_id integer NOT NULL
);


ALTER TABLE public.wh_warehouse_company OWNER TO postgres;

--
-- Name: wh_warehouses; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.wh_warehouses (
    id integer NOT NULL,
    code character varying(255) NOT NULL,
    name character varying(255) NOT NULL,
    parent_id integer,
    fields json,
    created_at timestamp(0) without time zone NOT NULL,
    updated_at timestamp(0) without time zone NOT NULL
);


ALTER TABLE public.wh_warehouses OWNER TO postgres;

--
-- Name: COLUMN wh_warehouses.fields; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.wh_warehouses.fields IS '(DC2Type:json)';


--
-- Name: articles_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.articles_id_seq', 1, false);


--
-- Name: companies_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.companies_id_seq', 1, false);


--
-- Name: company_groups_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.company_groups_id_seq', 1, false);


--
-- Name: company_type_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.company_type_id_seq', 1, false);


--
-- Name: documents_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.documents_id_seq', 1, false);


--
-- Name: item_groups_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.item_groups_id_seq', 1, false);


--
-- Name: items_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.items_id_seq', 1, false);


--
-- Name: measures_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.measures_id_seq', 1, false);


--
-- Name: sequences_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.sequences_id_seq', 1, false);


--
-- Name: transactions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.transactions_id_seq', 1, false);


--
-- Name: users_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.users_id_seq', 1, false);


--
-- Name: warehouses_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.warehouses_id_seq', 1, false);


--
-- Data for Name: wh_articles; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.wh_articles (id, item_id, warehouse_id, company_id, quantity, price_with_dds, price_no_dds, batch, code, fields, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: wh_companies; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.wh_companies (id, type_id, group_id, name, print_name, code, fields, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: wh_company_groups; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.wh_company_groups (id, parent_id, name) FROM stdin;
\.


--
-- Data for Name: wh_company_type; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.wh_company_type (id, name, fields) FROM stdin;
1	Стопанства	{"address": {"label": "Адрес","order": 1}, "eik": { "label": "ЕИК", "order": 2},"mol": {"label": "МОЛ","order": 3,"required": true},"tf": {"label": "Технофарм ID","system": true}}
2	Контрагенти	{  "address": {    "label": "Адрес",    "order": 1  },  "eik": {    "label": "ЕИК",    "order": 2  },  "mol": {    "label": "МОЛ",    "order": 3  },  "phone": {    "label": "Телефон",    "order": 5  }}
3	Активи	{"farm":{"label":"Стопанство","order":1,"join":{"entity":"Company","key":"id"}},"number":{"label":"Рег. номер","order":1}}
\.


--
-- Data for Name: wh_documents; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.wh_documents (id, company_in, company_out, user_id, type, transaction_type, date, number, vehicle_number, driver_name, created_at, closed_at, updated_at, dds_type, plot_name, area) FROM stdin;
\.


--
-- Data for Name: wh_item_groups; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.wh_item_groups (id, parent_id, name) FROM stdin;
\.


--
-- Data for Name: wh_items; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.wh_items (id, group_id, measure_id, warehouse_id, name, code, print_name, min_quantity) FROM stdin;
\.


--
-- Data for Name: wh_measures; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.wh_measures (id, name, short_name) FROM stdin;
\.


--
-- Data for Name: wh_sequences; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.wh_sequences (id, type, number) FROM stdin;
\.


--
-- Data for Name: wh_transactions; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.wh_transactions (id, item_id, document_id, warehouse_id, article_id, type, number, quantity, price_with_dds, price_no_dds, created_at, note, batch, expiry_date) FROM stdin;
\.


--
-- Data for Name: wh_users; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.wh_users (id, username, name, roles, password) FROM stdin;
\.


--
-- Data for Name: wh_warehouse_company; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.wh_warehouse_company (warehouse_id, company_id) FROM stdin;
\.


--
-- Data for Name: wh_warehouses; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.wh_warehouses (id, code, name, parent_id, fields, created_at, updated_at) FROM stdin;
\.


--
-- Name: wh_articles_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.wh_articles
    ADD CONSTRAINT wh_articles_pkey PRIMARY KEY (id);


--
-- Name: wh_companies_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.wh_companies
    ADD CONSTRAINT wh_companies_pkey PRIMARY KEY (id);


--
-- Name: wh_company_groups_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.wh_company_groups
    ADD CONSTRAINT wh_company_groups_pkey PRIMARY KEY (id);


--
-- Name: wh_company_type_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.wh_company_type
    ADD CONSTRAINT wh_company_type_pkey PRIMARY KEY (id);


--
-- Name: wh_documents_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.wh_documents
    ADD CONSTRAINT wh_documents_pkey PRIMARY KEY (id);


--
-- Name: wh_item_groups_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.wh_item_groups
    ADD CONSTRAINT wh_item_groups_pkey PRIMARY KEY (id);


--
-- Name: wh_items_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.wh_items
    ADD CONSTRAINT wh_items_pkey PRIMARY KEY (id);


--
-- Name: wh_measures_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.wh_measures
    ADD CONSTRAINT wh_measures_pkey PRIMARY KEY (id);


--
-- Name: wh_sequences_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.wh_sequences
    ADD CONSTRAINT wh_sequences_pkey PRIMARY KEY (id);


--
-- Name: wh_transactions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.wh_transactions
    ADD CONSTRAINT wh_transactions_pkey PRIMARY KEY (id);


--
-- Name: wh_users_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.wh_users
    ADD CONSTRAINT wh_users_pkey PRIMARY KEY (id);


--
-- Name: wh_warehouse_company_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.wh_warehouse_company
    ADD CONSTRAINT wh_warehouse_company_pkey PRIMARY KEY (warehouse_id, company_id);


--
-- Name: wh_warehouses_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.wh_warehouses
    ADD CONSTRAINT wh_warehouses_pkey PRIMARY KEY (id);


--
-- Name: idx_1c35220727aca70; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_1c35220727aca70 ON public.wh_company_groups USING btree (parent_id);


--
-- Name: idx_4a8d7929126f525e; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_4a8d7929126f525e ON public.wh_transactions USING btree (item_id);


--
-- Name: idx_4a8d79295080ecde; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_4a8d79295080ecde ON public.wh_transactions USING btree (warehouse_id);


--
-- Name: idx_4a8d79297294869c; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_4a8d79297294869c ON public.wh_transactions USING btree (article_id);


--
-- Name: idx_4a8d7929c33f7837; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_4a8d7929c33f7837 ON public.wh_transactions USING btree (document_id);


--
-- Name: idx_5915515f727aca70; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_5915515f727aca70 ON public.wh_item_groups USING btree (parent_id);


--
-- Name: idx_809884085080ecde; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_809884085080ecde ON public.wh_warehouse_company USING btree (warehouse_id);


--
-- Name: idx_80988408979b1ad6; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_80988408979b1ad6 ON public.wh_warehouse_company USING btree (company_id);


--
-- Name: idx_9bad2be126f525e; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_9bad2be126f525e ON public.wh_articles USING btree (item_id);


--
-- Name: idx_9bad2be5080ecde; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_9bad2be5080ecde ON public.wh_articles USING btree (warehouse_id);


--
-- Name: idx_9bad2be979b1ad6; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_9bad2be979b1ad6 ON public.wh_articles USING btree (company_id);


--
-- Name: idx_a10ae1105080ecde; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_a10ae1105080ecde ON public.wh_items USING btree (warehouse_id);


--
-- Name: idx_a10ae1105da37d00; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_a10ae1105da37d00 ON public.wh_items USING btree (measure_id);


--
-- Name: idx_a10ae110fe54d947; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_a10ae110fe54d947 ON public.wh_items USING btree (group_id);


--
-- Name: idx_cdb6628a774ef3c8; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_cdb6628a774ef3c8 ON public.wh_documents USING btree (company_in);


--
-- Name: idx_cdb6628aa76ed395; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_cdb6628aa76ed395 ON public.wh_documents USING btree (user_id);


--
-- Name: idx_cdb6628abd19e9f1; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_cdb6628abd19e9f1 ON public.wh_documents USING btree (company_out);


--
-- Name: idx_ed42ba38c54c8c93; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ed42ba38c54c8c93 ON public.wh_companies USING btree (type_id);


--
-- Name: idx_ed42ba38fe54d947; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_ed42ba38fe54d947 ON public.wh_companies USING btree (group_id);


--
-- Name: uniq_4188a58b77153098; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX uniq_4188a58b77153098 ON public.wh_warehouses USING btree (code);


--
-- Name: uniq_5497adb4f85e0677; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX uniq_5497adb4f85e0677 ON public.wh_users USING btree (username);


--
-- Name: uniq_a10ae11077153098; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX uniq_a10ae11077153098 ON public.wh_items USING btree (code);


--
-- Name: uniq_ed42ba3877153098; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX uniq_ed42ba3877153098 ON public.wh_companies USING btree (code);


--
-- Name: fk_1c35220727aca70; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.wh_company_groups
    ADD CONSTRAINT fk_1c35220727aca70 FOREIGN KEY (parent_id) REFERENCES public.wh_company_groups(id);


--
-- Name: fk_4a8d7929126f525e; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.wh_transactions
    ADD CONSTRAINT fk_4a8d7929126f525e FOREIGN KEY (item_id) REFERENCES public.wh_items(id);


--
-- Name: fk_4a8d79295080ecde; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.wh_transactions
    ADD CONSTRAINT fk_4a8d79295080ecde FOREIGN KEY (warehouse_id) REFERENCES public.wh_warehouses(id);


--
-- Name: fk_4a8d79297294869c; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.wh_transactions
    ADD CONSTRAINT fk_4a8d79297294869c FOREIGN KEY (article_id) REFERENCES public.wh_articles(id);


--
-- Name: fk_4a8d7929c33f7837; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.wh_transactions
    ADD CONSTRAINT fk_4a8d7929c33f7837 FOREIGN KEY (document_id) REFERENCES public.wh_documents(id);


--
-- Name: fk_5915515f727aca70; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.wh_item_groups
    ADD CONSTRAINT fk_5915515f727aca70 FOREIGN KEY (parent_id) REFERENCES public.wh_item_groups(id);


--
-- Name: fk_809884085080ecde; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.wh_warehouse_company
    ADD CONSTRAINT fk_809884085080ecde FOREIGN KEY (warehouse_id) REFERENCES public.wh_warehouses(id) ON DELETE CASCADE;


--
-- Name: fk_80988408979b1ad6; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.wh_warehouse_company
    ADD CONSTRAINT fk_80988408979b1ad6 FOREIGN KEY (company_id) REFERENCES public.wh_companies(id) ON DELETE CASCADE;


--
-- Name: fk_9bad2be126f525e; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.wh_articles
    ADD CONSTRAINT fk_9bad2be126f525e FOREIGN KEY (item_id) REFERENCES public.wh_items(id);


--
-- Name: fk_9bad2be5080ecde; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.wh_articles
    ADD CONSTRAINT fk_9bad2be5080ecde FOREIGN KEY (warehouse_id) REFERENCES public.wh_warehouses(id);


--
-- Name: fk_9bad2be979b1ad6; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.wh_articles
    ADD CONSTRAINT fk_9bad2be979b1ad6 FOREIGN KEY (company_id) REFERENCES public.wh_companies(id);


--
-- Name: fk_a10ae1105080ecde; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.wh_items
    ADD CONSTRAINT fk_a10ae1105080ecde FOREIGN KEY (warehouse_id) REFERENCES public.wh_warehouses(id);


--
-- Name: fk_a10ae1105da37d00; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.wh_items
    ADD CONSTRAINT fk_a10ae1105da37d00 FOREIGN KEY (measure_id) REFERENCES public.wh_measures(id);


--
-- Name: fk_a10ae110fe54d947; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.wh_items
    ADD CONSTRAINT fk_a10ae110fe54d947 FOREIGN KEY (group_id) REFERENCES public.wh_item_groups(id);


--
-- Name: fk_cdb6628a774ef3c8; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.wh_documents
    ADD CONSTRAINT fk_cdb6628a774ef3c8 FOREIGN KEY (company_in) REFERENCES public.wh_companies(id);


--
-- Name: fk_cdb6628aa76ed395; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.wh_documents
    ADD CONSTRAINT fk_cdb6628aa76ed395 FOREIGN KEY (user_id) REFERENCES public.wh_users(id);


--
-- Name: fk_cdb6628abd19e9f1; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.wh_documents
    ADD CONSTRAINT fk_cdb6628abd19e9f1 FOREIGN KEY (company_out) REFERENCES public.wh_companies(id);


--
-- Name: fk_ed42ba38c54c8c93; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.wh_companies
    ADD CONSTRAINT fk_ed42ba38c54c8c93 FOREIGN KEY (type_id) REFERENCES public.wh_company_type(id);


--
-- Name: fk_ed42ba38fe54d947; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.wh_companies
    ADD CONSTRAINT fk_ed42ba38fe54d947 FOREIGN KEY (group_id) REFERENCES public.wh_company_groups(id);


--
-- Name: SCHEMA public; Type: ACL; Schema: -; Owner: postgres
--

REVOKE ALL ON SCHEMA public FROM PUBLIC;
REVOKE ALL ON SCHEMA public FROM postgres;
GRANT ALL ON SCHEMA public TO postgres;
GRANT ALL ON SCHEMA public TO PUBLIC;


--
-- PostgreSQL database dump complete
--

