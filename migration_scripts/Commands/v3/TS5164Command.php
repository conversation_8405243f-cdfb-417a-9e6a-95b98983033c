<?php

namespace TF\Commands\v3;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-5164 command run on all databases.
 */
class TS5164Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v3:TS-5164')
            ->setDescription('Create su_alert_settings_id_seq');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-5164.sql', $return);
    }
}
