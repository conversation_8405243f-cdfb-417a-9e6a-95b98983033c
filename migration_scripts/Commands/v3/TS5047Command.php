<?php

namespace TF\Commands\v3;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-5047 command run on all databases.
 */
class TS5047Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v3:TS-5047')
            ->setDescription('Add invoice_number and invoice_date columns in wh_documents.');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-5047.sql', $return);
    }
}
