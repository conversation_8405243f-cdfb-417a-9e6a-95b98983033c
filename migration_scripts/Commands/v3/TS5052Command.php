<?php

namespace TF\Commands\v3;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-5052 command run on all databases.
 */
class TS5052Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v3:TS-5052')
            ->setDescription('Create Warehouse tables into Technofarm DB`.');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-5052.sql', $return);
    }
}
