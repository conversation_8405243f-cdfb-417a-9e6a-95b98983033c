<?php

namespace TF\Commands\v3;

use TF\Commands\Common\UserDbCommand;

/**
 * Runs all scripts required for migration from Version 2.1 to 2.2.
 */
class UpdateToVersion3 extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v3:update-to-version-3')
            ->setDescription('Runs all scripts required for migration from version 2.2 to version 3.');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $commandsArr = [
            new TS4668Command(),
            new TS4950Command(),
            new TS5052Command(),
            new TS5065Command(),
            new TS5047Command(),
            new TS5078Command(),
        ];

        $count = count($commandsArr);
        for ($i = 0; $i < $count; $i++) {
            $command = $commandsArr[$i];
            $this->logScript($userDb, $command->getName());
            $output->writeln('Executing command: ' . $command->getName());
            $command->onDbExecute($userDb, $output, $input);
        }
    }

    protected function logScript($user_db, $name)
    {
        $date = date('Y-m-d H:i:s');

        $this->openConnection();

        $sql = $this->mainConnection->prepare("
            INSERT INTO scripts_log (db_name, name, date)
            VALUES ('{$user_db}', '{$name}', '{$date}')
        ");

        return $sql->execute();
    }
}
