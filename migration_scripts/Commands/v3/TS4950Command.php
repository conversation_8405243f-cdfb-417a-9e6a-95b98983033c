<?php

namespace TF\Commands\v3;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-4950 command run on all databases.
 */
class TS4950Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v3:TS-4950')
            ->setDescription('Add column warehouse_item_id in `su_diary_configs`.');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-4950.sql', $return);
    }
}
