<?php

namespace TF\Commands\v3;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-4668 command run on all databases.
 */
class TS4668Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v3:TS-4668')
            ->setDescription('Add rent_area column on su_subleases_plots_area table');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-4668.sql', $return);
    }
}
