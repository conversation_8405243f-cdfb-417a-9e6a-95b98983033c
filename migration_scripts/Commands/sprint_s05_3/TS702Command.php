<?php

namespace TF\Commands\sprint_s05_3;

use PDO;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Commands\ParamHelper;

/**
 * TS-702 command run on all databases.
 */
class TS702Command extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('sprint_s05_3:TS-702')
            ->setDescription('iztriva dublirashti se zapisi v  "su_contracts_plots_rel"')
            ->addArgument(
                'user_databases',
                InputArgument::IS_ARRAY | InputArgument::OPTIONAL,
                'Databases to run the script on (separate multiple names with a space)?'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $user_databases = $input->getArgument('user_databases');

        $paramHelper = new ParamHelper();
        $userDbString = $paramHelper->createQueryParamString($user_databases);

        $userDbConditions = 'true';
        if ($user_databases) {
            $userDbConditions = 'u.database IN (' . $userDbString . ')';
        }

        $mainDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = $mainDev->prepare(
            "SELECT database FROM su_users u
			WHERE {$userDbConditions} AND u.level = 2 
			ORDER BY database"
        );

        $sql->execute();
        $results = $sql->fetchAll();

        for ($i = 0; $i < count($results); $i++) {
            $sql = "SELECT 1 from pg_database WHERE datname='" . $results[$i]['database'] . "';";
            $sqlSelect = $mainDev->prepare($sql);
            $sqlSelect->execute();
            $result = $sqlSelect->fetchAll();

            // id db exists -> view the accounts with duplicated data
            if (!count($result)) {
                continue;
            }

            $dbhDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $results[$i]['database'] . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

            $sql = 'SELECT id
		              	FROM (
		              			SELECT id, ROW_NUMBER() OVER (partition BY contract_id, plot_id ORDER BY id) AS rnum 
		              			FROM su_contracts_plots_rel
		              		  ) t
		              	WHERE t.rnum > 1';

            $sqlSelect = $dbhDev->prepare($sql);
            $sqlSelect->execute();
            $result = $sqlSelect->fetchAll();

            if (!count($result)) {
                continue;
            }

            var_export($results[$i]['database'] . '->' . count($result));
            $output->writeln('');

            // delete duplications in su_contracts_plots_rel
            $sql = 'DELETE FROM su_contracts_plots_rel
					WHERE id IN
					(
						SELECT id
		              	FROM (
		              			SELECT id, ROW_NUMBER() OVER (partition BY contract_id, plot_id ORDER BY id) AS rnum 
		              			FROM su_contracts_plots_rel
		              		  ) t
		              	WHERE t.rnum > 1

					)';
            $cmd = $dbhDev->prepare($sql);
            $cmd->execute();
        }

        $output->writeln('Done!');
    }
}
