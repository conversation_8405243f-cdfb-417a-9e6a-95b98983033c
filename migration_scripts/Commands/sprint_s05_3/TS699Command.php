<?php

namespace TF\Commands\sprint_s05_3;

use PDO;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Commands\ParamHelper;

/**
 * TS-699 command run on all databases.
 */
class TS699Command extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('sprint_s05_3:TS-699')
            ->setDescription('Update "cropname" spored "cropcode" v tablicite ot tip "Za isak"')
            ->addArgument(
                'user_databases',
                InputArgument::IS_ARRAY | InputArgument::OPTIONAL,
                'Databases to run the script on (separate multiple names with a space)?'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $user_databases = $input->getArgument('user_databases');

        $dbhDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $paramHelper = new ParamHelper();
        $userDbString = $paramHelper->createQueryParamString($user_databases);

        $userDbConditions = 'true';
        if ($user_databases) {
            $userDbConditions = 'u.database IN (' . $userDbString . ')';
        }

        $sql = $dbhDev->prepare(
            "SELECT ul.table_name,u.database FROM su_users_layers ul
			INNER JOIN su_users u on u.id = ul.user_id
			WHERE {$userDbConditions} ul.layer_type = 9"
        );

        $sql->execute();
        $results = $sql->fetchAll();

        for ($i = 0; $i < count($results); $i++) {
            $output->writeln('database=' . $results[$i]['database'] . ' ' . $results[$i]['table_name'] . '');

            $sql = "SELECT 1 from pg_database WHERE datname='" . $results[$i]['database'] . "';";
            $sqlSelect = $dbhDev->prepare($sql);
            $sqlSelect->execute();
            $result = $sqlSelect->fetchAll();

            // id db does not exists -> continue
            if (!count($result)) {
                continue;
            }

            $dbhDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $results[$i]['database'] . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

            $sql = $dbhDev->prepare("SELECT * FROM {$results[$i]['table_name']}");

            $sql->execute();
            $data = $sql->fetchAll();

            for ($j = 0; $j < count($data); $j++) {
                if (strlen($data[$j]['cropcode'])) {
                    $crop = $GLOBALS['Farming']['crops'][$data[$j]['cropcode']];

                    $cmd = $dbhDev->prepare("UPDATE {$results[$i]['table_name']} SET 
											 cropname = :cropname
											 WHERE gid = :gid");

                    $cmd->bindValue(':cropname', $crop['crop_name']);
                    $cmd->bindValue(':gid', $data[$j]['gid']);

                    $cmd->execute();
                }
            }
        }
        $output->writeln('Done!');
    }
}
