<?php

namespace TF\Commands\sprint_s05_3;

use PDO;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Commands\ParamHelper;

/**
 * TS-701 command run on all databases.
 */
class TS701Command extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('sprint_s05_3:TS-701')
            ->setDescription('dobavq plosht po dogovor na aneksirani imoti s nuleva plosht, ravna na tazi v aneksiraniq dogovor')
            ->addArgument(
                'user_databases',
                InputArgument::IS_ARRAY | InputArgument::OPTIONAL,
                'Databases to run the script on (separate multiple names with a space)?'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $user_databases = $input->getArgument('user_databases');

        $paramHelper = new ParamHelper();
        $userDbString = $paramHelper->createQueryParamString($user_databases);

        $userDbConditions = 'true';
        if ($user_databases) {
            $userDbConditions = 'u.database IN (' . $userDbString . ')';
        }

        $dbhDev = new PDO('pgsql:host=' . DBLINK_HOST . ';port=' . DBLINK_PORT . ';dbname=' . DBLINK_DATABASE . ';', DBLINK_USERNAME, DBLINK_PASSWORD);

        $sql = $dbhDev->prepare(
            "SELECT database FROM su_users u 
		 	WHERE {$userDbConditions}
			ORDER BY u.database"
        );

        $sql->execute();
        $results = $sql->fetchAll();
        $affected_users = [];
        for ($i = 0; $i < count($results); $i++) {
            $sql = $dbhDev->prepare(
                "SELECT datname FROM pg_catalog.pg_database
		 	 WHERE lower(datname) = lower('" . $results[$i]['database'] . "');"
            );

            $sql->execute();
            $existDatabase = $sql->fetch();

            if (!$existDatabase) {
                continue;
            }

            $dbhDevDB = new PDO('pgsql:host=' . DBLINK_HOST . ';port=' . DBLINK_PORT . ';dbname=' . $results[$i]['database'] . ';', DBLINK_USERNAME, DBLINK_PASSWORD);

            $sql = $dbhDevDB->prepare(
                'SELECT
		                    c.cpr_id,
		                	C.ID,
		                	A.ID AS anex_id,
		                	C.contract_area,
		                	A.contract_area AS anex_area,
		                	C.plot_id,
		                	A.plot_id
		                FROM
		                	(
		                		SELECT
		                			C.ID,
		                			C.parent_id,
		                			C.is_annex,
		                			cpr.contract_area,
		                			cpr. ID AS cpr_id,
		                			cpr.plot_id
		                		FROM
		                			su_contracts C
		                		LEFT JOIN su_contracts_plots_rel cpr ON C.id = cpr.contract_id
		                		WHERE
		                			C.is_annex = FALSE
		                	) C
		                INNER JOIN (
		                	SELECT
		                		C.ID,
		                		C.parent_id,
		                		C.is_annex,
		                		cpr.contract_area,
		                		cpr. ID AS cpr_id,
		                		cpr.plot_id
		                	FROM
		                		su_contracts C
		                	LEFT JOIN su_contracts_plots_rel cpr ON C.id = cpr.contract_id
		                	WHERE
		                		C .is_annex = TRUE
		                ) A ON C . ID = A .parent_id
		                AND A .plot_id = C .plot_id
		                WHERE
		                	A .contract_area ISNULL
		                ORDER BY
		                	ID;'
            );
            $sql->execute();
            $Qresults = $sql->fetch();
            if (false != $Qresults) {
                $affected_users[] = $results[$i]['database'];
                $fixSql = $dbhDevDB->prepare(
                    '
	                UPDATE su_contracts_plots_rel d
	                SET contract_area = C .contract_area FROM (
	                	SELECT
	                		C . ID,
	                		C .parent_id,
	                		C .is_annex,
	                		cpr.contract_area,
	                		cpr. ID AS cpr_id,
	                		cpr.plot_id
	                	FROM
	                		su_contracts C
	                	LEFT JOIN su_contracts_plots_rel cpr ON C .id = cpr.contract_id
	                	WHERE
	                		C .is_annex = FALSE
	                ) C,
	                 (
	                	SELECT
	                		C . ID,
	                		C .parent_id,
	                		C .is_annex,
	                		cpr.contract_area,
	                		cpr. ID AS cpr_id,
	                		cpr.plot_id
	                	FROM
	                		su_contracts C
	                	LEFT JOIN su_contracts_plots_rel cpr ON C .id = cpr.contract_id
	                	WHERE
	                		C .is_annex = TRUE
	                		
	                ) A
	                WHERE
	                	C.ID = A.parent_id
	                AND A.plot_id = C.plot_id
	                AND A.contract_area ISNULL
	                and d.id = a.cpr_id;'
                );
            }
        }
    }
}
