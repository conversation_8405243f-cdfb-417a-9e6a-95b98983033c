<?php

namespace TF\Commands;

use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\ArrayInput;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Output\StreamOutput;
use TF\Commands\Common\RegenerateMapFilesCommand;

/**
 * MigrateScripts command run on all databases.
 */
class MigrateToLogin3Command extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('tf:migrate_to_login3')
            ->setDescription('Izpulnqva vsichki migracionni skriptove za migrirane na potrebitel ot login 2 -> login 3')
            ->addArgument(
                'user_databases',
                InputArgument::IS_ARRAY | InputArgument::OPTIONAL,
                'Databases to run the script on (separate multiple names with a space)?'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $user_databases = $input->getArgument('user_databases');

        $commandsArr = [
            // Sprint 4
            new sprint_s4\TS515Command(),
            new sprint_s4\TS522Command(),

            // Sprint 5.1
            new sprint_s5_1\TS563Command(),

            // Sprint 5.2
            new sprint_s5_2\TS595Command(),
            new sprint_s5_2\TS599Command(),
            new sprint_s5_2\TS602Command(),
            new sprint_s5_2\TS622Command(),
            new sprint_s5_2\TS651Command(),

            // Sprint 6
            new sprint_s6\TS694Command(),
            new sprint_s6\TS704Command(),
            new sprint_s6\TS739Command(),
            new sprint_s6\TS740Command(),
            new sprint_s6\TS741Command(),
            new sprint_s6\TS789Command(),
            new sprint_s6\TS794Command(),
            new sprint_s6\TS7941Command(),
            new sprint_s6\TS798Command(),
            new sprint_s6\TS806Command(),
            new sprint_s6\TS833Command(),

            // Sprint 6.1
            new sprint_s6_1\TS803Command(),
            new sprint_s6_1\TS804Command(),

            // Sprint 8
            new sprint_s8\TS879Command(),
            new sprint_s8\TS219Command(),
            new sprint_s8\TS880Command(),
            new sprint_s8\TS8801Command(),
            new sprint_s8\TS896Command(),
            new sprint_s8\TS976Command(),
            new sprint_s8\TS9762Command(),
            new sprint_s8\TS1260Command(),
            new sprint_s8\TS1314Command(),
            new sprint_s8\TS1387Command(),
            new sprint_s8\TS1460Command(),
            new sprint_s8\TS14601Command(),

            // Sprint 9
            new sprint_s9\TS1192Command(),
            new sprint_s9\TS1193Command(),
            new sprint_s9\TS1440Command(),
            new sprint_s9\TS1443Command(),
            new sprint_s9\TS1711Command(),

            // Sprint 10
            new sprint_s10\TS1754Command(),
            new sprint_s10\TS1778Command(),
            new sprint_s10\TS1783Command(),
            new sprint_s10\TS1784Command(),
            new sprint_s10\TS1785Command(),
            new sprint_s10\TS1901Command(),

            // Sprint 12
            new sprint_s12\TS11741Command(),
            new sprint_s12\TS1174Command(),
            new sprint_s12\TS2091Command(),
            new sprint_s12\TS2107Command(),

            new RegenerateMapFilesCommand(),
        ];

        $commandOutput = new StreamOutput(fopen('php://memory', 'w', false));
        $commandInput = new ArrayInput(['user_databases' => $user_databases]);

        for ($i = 0; $i < count($commandsArr); $i++) {
            $command = $commandsArr[$i];
            $output->writeln('Executing command: ' . $command->getName());
            $command->run($commandInput, $commandOutput);
        }
    }
}
