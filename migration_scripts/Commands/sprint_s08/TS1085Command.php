<?php

namespace TF\Commands\sprint_s08;

use PDO;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * TS-1085 command run on all databases.
 */
class TS1085Command extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('sprint_s08:TS-1085')
            ->setDescription('Dobavq nova kolona za bankovi smetki na stopanstvata.');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $mainDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $mainDev->setAttribute(PDO::ATTR_EMULATE_PREPARES, true);
        $mainDev->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_WARNING);

        $sql = $mainDev->prepare(
            "ALTER TABLE su_users_farming ADD COLUMN iban_arr text DEFAULT '[]'"
        );

        $sql->execute();
    }
}
