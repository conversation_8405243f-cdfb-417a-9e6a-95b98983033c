<?php

namespace TF\Commands\sprint_s08;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-896 command run on all databases.
 */
class TS896Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s08:TS-896')
            ->setDescription('Sazdava tablicite za podmodul "Danni ot OSZ". Vajno! - Trqbva da se instalira php_dbase extension.');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-896.sql', $return);
    }
}
