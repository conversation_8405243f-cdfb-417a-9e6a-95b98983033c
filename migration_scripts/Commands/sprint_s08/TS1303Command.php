<?php

namespace TF\Commands\sprint_s08;

use PDO;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Commands\ParamHelper;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * TS-1303 command run on all databases.
 */
class TS1303Command extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('sprint_s08:TS-1303')
            ->setDescription('Pregenerira vsichki sepp i pzp meterializirani viuta(MATERIALIZED VIEW)')
            ->addArgument(
                'user_databases',
                InputArgument::IS_ARRAY | InputArgument::OPTIONAL,
                'Databases to run the script on (separate multiple names with a space)?'
            )
            ->addOption('new_user_db', null, InputOption::VALUE_NONE, 'If set, the task will also  run on the new_user database ');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $user_databases = $input->getArgument('user_databases');

        $paramHelper = new ParamHelper();
        $userDbString = $paramHelper->createQueryParamString($user_databases);

        $userDbConditions = 'true';
        if ($user_databases) {
            $userDbConditions = 'u.database IN (' . $userDbString . ')';
        }

        $mainDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = $mainDev->prepare(
            "SELECT database FROM su_users u
			WHERE {$userDbConditions} AND u.level = 2 
			ORDER BY database"
        );

        $sql->execute();
        $results = $sql->fetchAll();

        if ($input->getOption('new_user_db')) {
            $results[] = ['database' => 'new_users_db'];
        }

        for ($i = 0; $i < count($results); $i++) {
            $sql = "SELECT 1 from pg_database WHERE datname='" . $results[$i]['database'] . "';";
            $sqlSelect = $mainDev->prepare($sql);
            $sqlSelect->execute();
            $result = $sqlSelect->fetchAll();

            // id db does not exists
            if (!count($result)) {
                continue;
            }

            $dbName = $results[$i]['database'];
            $dbhDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $dbName . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
            $output->writeln("Database: {$dbName} ");

            $sql = "SELECT
						oid :: regclass :: TEXT
					FROM
						pg_class
					WHERE
						relkind = 'm'
					AND oid :: regclass :: TEXT ILIKE 'sepp_%'
					OR oid :: regclass :: TEXT ILIKE 'pzp_%'";

            $cmd = $dbhDev->prepare($sql);
            $cmd->execute();
            $resultData = $cmd->fetchAll();

            $UserDbController = new UserDbController($dbName);// getPluginInstanceUserDb($dbName);

            for ($j = 0; $j < count($resultData); $j++) {
                $output->writeln('MATERIALIZED VIEW: ' . $resultData[$j]['oid'] . ' ');

                $view = $resultData[$j]['oid'];
                $aView = explode('_', $view);

                $viewType = current($aView);
                $layerId = end($aView);

                // echo end($layerId);
                if (is_numeric($layerId)) {
                    // DROP MATERIALIZED VIEW
                    $sql = 'DROP MATERIALIZED VIEW IF EXISTS ' . $view . '';
                    $cmd = $dbhDev->prepare($sql);
                    $cmd->execute();

                    $sql = 'DROP TABLE IF EXISTS ' . $view . '';
                    $cmd = $dbhDev->prepare($sql);
                    $cmd->execute();

                    if ('sepp' == $viewType) {
                        // CREATE MATERIALIZED VIEW
                        $UserDbController->createSEPPReportView($layerId);
                    }

                    if ('pzp' == $viewType) {
                        // CREATE MATERIALIZED VIEW
                        $UserDbController->createPZPReportView($layerId);
                    }

                    $output->writeln('CREATED VIEW: ' . $view . ' ');
                } else {
                    // DROP INDEX (blocking code)
                    $sql = 'DROP INDEX IF EXISTS ' . $view . '';
                    $cmd = $dbhDev->prepare($sql);
                    $cmd->execute();

                    $output->writeln('DROPED INDEX: ' . $view . ' ');
                }
            }
        }
    }
}
