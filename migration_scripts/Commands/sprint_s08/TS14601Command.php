<?php

namespace TF\Commands\sprint_s08;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-14601 command run on all databases.
 */
class TS14601Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s08:TS-14601')
            ->setDescription('Promqna na type na kolonite number, masiv, ekate v su_charged_renta_params i su_charged_renta_history');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-14601.sql', $return);
    }
}
