-- Name: charged_rentas_mat_view; Type: Materialized view; Schema: public; Owner: postgres; Tablespace: 
-- charged_rentas_mat_view

-- DROP the view if exists.
DROP MATERIALIZED VIEW IF EXISTS charged_rentas_mat_view;

-- CREATE the new materialized view
CREATE MATERIALIZED VIEW charged_rentas_mat_view AS 
   SELECT ((cr.renta * pc.contract_area) * (po.percent / (100)::double precision)) AS charged_renta,
        CASE
            WHEN (crn.nat_is_converted = true) THEN NULL::double precision
            ELSE ((crn.amount * pc.contract_area) * (po.percent / (100)::double precision))
        END AS charged_renta_nat,
        CASE
            WHEN (crn.nat_is_converted = true) THEN (((crn.amount * crn.nat_unit_price) * pc.contract_area) * (po.percent / (100)::double precision))
            ELSE NULL::double precision
        END AS converted_charged_renta_nat,
    c.id AS c_id,
    po.owner_id,
    crn.nat_is_converted,
    crn.nat_type,
    pc.plot_id,
    kvs.ekate,
    rt.name,
    rt.unit,
    cr.year,
    c.farming_id,
    c.start_date,
    c.due_date
   FROM ((((((su_contracts c
     JOIN su_contracts_plots_rel pc ON ((pc.contract_id = c.id)))
     JOIN layer_kvs kvs ON ((kvs.gid = pc.plot_id)))
     LEFT JOIN su_plots_owners_rel po ON ((po.pc_rel_id = pc.id)))
     JOIN su_charged_renta cr ON ((((cr.plot_id = kvs.gid) AND (cr.contract_id = c.id)) AND (cr.owner_id = po.owner_id))))
     LEFT JOIN su_charged_renta_natura crn ON ((crn.renta_id = cr.id)))
     LEFT JOIN su_renta_types rt ON ((crn.nat_type = rt.id)))
  WHERE ((((c.is_annex = false) AND (c.is_sublease = false)) AND (po.is_heritor = false)) AND (pc.annex_action = 'added'::annex_action_enum))
  GROUP BY po.owner_id, crn.nat_type, pc.plot_id, crn.nat_is_converted, crn.amount, pc.contract_area, po.percent, c.id, crn.nat_unit_price, kvs.ekate, rt.name, rt.unit, cr.year, cr.renta;

-- DROP the view if exists.
DROP MATERIALIZED VIEW IF EXISTS charged_rentas_annexes_mat_view;

-- CREATE the new materialized view
CREATE MATERIALIZED VIEW charged_rentas_annexes_mat_view AS 
   SELECT ((cr.renta * pc.contract_area) * (po.percent / (100)::double precision)) AS charged_renta,
        CASE
            WHEN (crn.nat_is_converted = true) THEN NULL::double precision
            ELSE ((crn.amount * pc.contract_area) * (po.percent / (100)::double precision))
        END AS charged_renta_nat,
        CASE
            WHEN (crn.nat_is_converted = true) THEN (((crn.amount * crn.nat_unit_price) * pc.contract_area) * (po.percent / (100)::double precision))
            ELSE NULL::double precision
        END AS converted_charged_renta_nat,
    a.id AS a_id,
    c.id AS c_id,
    po.owner_id,
    crn.nat_is_converted,
    crn.nat_type,
    pc.plot_id,
    kvs.ekate,
    rt.name,
    rt.unit,
    cr.year,
    a.farming_id,
    a.start_date,
    a.due_date
   FROM (((((((su_contracts c
     LEFT JOIN su_contracts a ON (((a.parent_id = c.id) AND (a.active = true))))
     JOIN su_contracts_plots_rel pc ON ((pc.contract_id = a.id)))
     JOIN layer_kvs kvs ON ((kvs.gid = pc.plot_id)))
     LEFT JOIN su_plots_owners_rel po ON ((po.pc_rel_id = pc.id)))
     JOIN su_charged_renta cr ON ((((cr.plot_id = kvs.gid) AND (cr.contract_id = c.id)) AND (cr.owner_id = po.owner_id))))
     LEFT JOIN su_charged_renta_natura crn ON ((crn.renta_id = cr.id)))
     LEFT JOIN su_renta_types rt ON ((crn.nat_type = rt.id)))
  WHERE ((((a.is_annex = true) AND (a.is_sublease = false)) AND (po.is_heritor = false)) AND (pc.annex_action = 'added'::annex_action_enum))
  GROUP BY po.owner_id, crn.nat_type, pc.plot_id, crn.nat_is_converted, crn.amount, pc.contract_area, po.percent, a.id, c.id, crn.nat_unit_price, kvs.ekate, rt.name, rt.unit, cr.year, cr.renta;

-- DROP the view if exists.
DROP MATERIALIZED VIEW IF EXISTS renta_nats_mat_view;

-- CREATE the new materialized view
CREATE MATERIALIZED VIEW renta_nats_mat_view AS 
      SELECT ((crt.renta_value * pc.contract_area) * (po.percent / (100)::double precision)) AS renta_nat,
    ((c.renta * pc.contract_area) * (po.percent / (100)::double precision)) AS renta,
    (pc.contract_area * (po.percent / (100)::double precision)) AS contract_area,
    c.c_num,
    c.id AS c_id,
    c.farming_id,
    c.start_date,
    c.due_date,
    po.owner_id,
    o.is_dead,
    kvs.ekate,
    kvs.gid AS plot_id,
    crt.renta_id AS nat_type,
    rt.name,
    rt.unit
   FROM ((((((su_contracts c
     JOIN su_contracts_plots_rel pc ON ((pc.contract_id = c.id)))
     LEFT JOIN su_plots_owners_rel po ON ((po.pc_rel_id = pc.id)))
     JOIN su_owners o ON ((o.id = po.owner_id)))
     JOIN layer_kvs kvs ON ((kvs.gid = pc.plot_id)))
     LEFT JOIN su_contracts_rents crt ON ((c.id = crt.contract_id)))
     LEFT JOIN su_renta_types rt ON ((crt.renta_id = rt.id)))
  WHERE ((pc.annex_action = 'added'::annex_action_enum) AND (c.is_annex = false))
  GROUP BY rt.name, crt.renta_value, crt.renta_id, pc.contract_area, po.percent, c.id, po.owner_id, c.farming_id, c.start_date, c.due_date, rt.unit, kvs.ekate, kvs.gid, o.is_dead
  ORDER BY c.id;

  -- DROP the view if exists.
DROP MATERIALIZED VIEW IF EXISTS renta_nats_annexes_mat_view;

-- CREATE the new materialized view
CREATE MATERIALIZED VIEW renta_nats_annexes_mat_view AS 
     SELECT (((crt.renta_value * pc.contract_area) * po.percent) / (100)::double precision) AS renta_nat,
    (
        CASE
            WHEN (a.renta IS NULL) THEN c.renta
            ELSE a.renta
        END * (pc.contract_area * (po.percent / (100)::double precision))) AS renta,
    a.renta AS annex_renta,
    pc.contract_area AS annex_area,
    (pc.contract_area * (po.percent / (100)::double precision)) AS contract_area,
    c.id AS c_id,
    a.id AS a_id,
    a.farming_id,
    a.start_date,
    a.due_date,
    po.owner_id,
    o.is_dead,
    kvs.ekate,
    kvs.gid AS plot_id,
    crt.renta_id AS nat_type,
    rt.name,
    rt.unit
   FROM (((((((su_contracts c
     LEFT JOIN su_contracts a ON (((a.parent_id = c.id) AND (a.active = true))))
     JOIN su_contracts_plots_rel pc ON ((pc.contract_id = a.id)))
     LEFT JOIN su_plots_owners_rel po ON ((po.pc_rel_id = pc.id)))
     JOIN su_owners o ON ((o.id = po.owner_id)))
     LEFT JOIN layer_kvs kvs ON ((kvs.gid = pc.plot_id)))
     LEFT JOIN su_contracts_rents crt ON (
        CASE
            WHEN (a.id IS NULL) THEN (c.id = crt.contract_id)
            ELSE (a.id = crt.contract_id)
        END))
     LEFT JOIN su_renta_types rt ON ((crt.renta_id = rt.id)))
  WHERE ((pc.annex_action = 'added'::annex_action_enum) AND (a.is_annex = true))
  GROUP BY rt.name, crt.renta_value, crt.renta_id, pc.contract_area, po.percent, c.id, a.id, po.owner_id, a.farming_id, a.start_date, a.due_date, rt.unit, kvs.ekate, kvs.gid, o.is_dead
  ORDER BY c.id;

--DROP ALL TRIGGERS IF THEY EXIST in order to rename the update function
DROP TRIGGER IF EXISTS "LAYER_KVS_TRIGGER" on "public"."layer_kvs";
DROP TRIGGER IF EXISTS "SU_CHARGED_RENTA_NATURA_TRIGGER" on "public"."su_charged_renta_natura";
DROP TRIGGER IF EXISTS "SU_CHARGED_RENTA_TRIGGER" on "public"."su_charged_renta";
DROP TRIGGER IF EXISTS "SU_CONTRACTS_PLOTS_REL" on "public"."su_contracts_plots_rel";
DROP TRIGGER IF EXISTS "SU_CONTRACTS_TRIGGER" on "public"."su_contracts";
DROP TRIGGER IF EXISTS "SU_PLOTS_OWNERS_REL_TRIGGER" on "public"."su_plots_owners_rel";
DROP TRIGGER IF EXISTS "SU_RENTA_TYPES_TRIGGER" on "public"."su_renta_types";

--DROP THE OLD FUNCTION
DROP FUNCTION IF EXISTS "public"."refresh_charged_rentas_view"();

--CREATE the update function, which will be executed when any of the triggers is triggered
    CREATE OR REPLACE FUNCTION "public"."refresh_rentas_materialized_views"()
    RETURNS "pg_catalog"."trigger" AS 
        $BODY$
            BEGIN
                REFRESH MATERIALIZED VIEW charged_rentas_mat_view;
                REFRESH MATERIALIZED VIEW charged_rentas_annexes_mat_view;
                REFRESH MATERIALIZED VIEW renta_nats_mat_view;
                REFRESH MATERIALIZED VIEW renta_nats_annexes_mat_view;
                RETURN NUll;
            END
        $BODY$
    LANGUAGE 'plpgsql';

--CREATE THE NEW TRIGGERS
CREATE TRIGGER "LAYER_KVS_TRIGGER" 
    AFTER INSERT OR UPDATE OF "gid",  "geom", "category", "area_type", "has_contracts", "used_area_by", "area_farming", "area_year", "used_area", "usable", "document_area", "is_edited"
    OR DELETE ON "public"."layer_kvs"
FOR EACH STATEMENT
EXECUTE PROCEDURE "refresh_rentas_materialized_views"();

CREATE TRIGGER "SU_CHARGED_RENTA_NATURA_TRIGGER" 
    AFTER INSERT OR UPDATE OF "id", "renta_id", "amount", "nat_type", "nat_is_converted", "nat_unit_price" 
    OR DELETE ON "public"."su_charged_renta_natura"
FOR EACH STATEMENT
EXECUTE PROCEDURE "refresh_rentas_materialized_views"();

CREATE TRIGGER "SU_CHARGED_RENTA_TRIGGER" 
    AFTER INSERT OR UPDATE OF "id", "contract_id", "year", "renta", "renta_nat", "plot_id", "nat_is_converted", "nat_unit_price", "owner_id" 
    OR DELETE ON "public"."su_charged_renta"
FOR EACH STATEMENT
EXECUTE PROCEDURE "refresh_rentas_materialized_views"();

CREATE TRIGGER "SU_CONTRACTS_PLOTS_REL" 
    AFTER INSERT OR UPDATE OF "id", "contract_id", "plot_id", "contract_area", "price_per_acre", "price_sum", "annex_action" 
    OR DELETE ON "public"."su_contracts_plots_rel"
FOR EACH STATEMENT
EXECUTE PROCEDURE "refresh_rentas_materialized_views"();

CREATE TRIGGER "SU_CONTRACTS_TRIGGER" 
    AFTER INSERT OR UPDATE OF "id", "c_num", "c_date", "nm_usage_rights", "sv_num", "sv_date", "start_date", "renta", "due_date", "renta_nat", "farming_id", "agg_type", "active", "parent_id", "is_annex", "renta_nat_type_id", "is_sublease", "original_due_date", "original_renta", "original_renta_nat", "original_renta_nat_type_id", "na_num", "payday" 
    OR DELETE ON "public"."su_contracts"
FOR EACH STATEMENT
EXECUTE PROCEDURE "refresh_rentas_materialized_views"();

CREATE TRIGGER "SU_PLOTS_OWNERS_REL_TRIGGER" 
    AFTER INSERT OR UPDATE OF "id", "pc_rel_id", "owner_id", "percent", "owner_document_id", "rep_id", "proxy_num", "proxy_date", "path", "is_heritor", "numerator", "denominator"
    OR DELETE ON "public"."su_plots_owners_rel"
FOR EACH STATEMENT
EXECUTE PROCEDURE "refresh_rentas_materialized_views"();

CREATE TRIGGER "SU_RENTA_TYPES_TRIGGER" 
    AFTER INSERT OR UPDATE OF "id", "name", "unit", "unit_value"
    OR DELETE ON "public"."su_renta_types"
FOR EACH STATEMENT
EXECUTE PROCEDURE "refresh_rentas_materialized_views"();