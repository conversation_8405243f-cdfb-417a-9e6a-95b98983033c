-- Name: charged_rentas; Type: Materialized view; Schema: public; Owner: postgres; Tablespace: 
-- charged_rentas

-- DROP the view if exists.
DROP MATERIALIZED VIEW IF EXISTS charged_rentas;

-- CREATE the new materialized view
CREATE MATERIALIZED VIEW charged_rentas AS 
    SELECT (((
        CASE
            WHEN (crn.nat_is_converted = true) THEN (0)::double precision
            ELSE crn.amount
        END * pc.contract_area) * po.percent) / (100)::double precision) AS charged_renta_nat,
    (((
        CASE
            WHEN (crn.nat_is_converted = true) THEN (crn.amount * crn.nat_unit_price)
            ELSE (0)::double precision
        END * pc.contract_area) * po.percent) / (100)::double precision) AS converted_charged_renta_nat,
    po.owner_id,
    cr.year,
    c.farming_id,
    c.start_date,
    c.due_date
    FROM ((((((su_contracts c
     LEFT JOIN su_contracts a ON (((a.parent_id = c.id) AND (a.active = true))))
     JOIN su_contracts_plots_rel pc ON ((pc.contract_id =
        CASE
            WHEN (a.id IS NULL) THEN c.id
            ELSE a.id
        END)))
     JOIN layer_kvs kvs ON ((kvs.gid = pc.plot_id)))
     LEFT JOIN su_plots_owners_rel po ON ((po.pc_rel_id = pc.id)))
     LEFT JOIN su_charged_renta cr ON (((cr.plot_id = kvs.gid) AND (cr.contract_id = c.id))))
     LEFT JOIN su_charged_renta_natura crn ON ((crn.renta_id = cr.id)))
    WHERE (pc.annex_action = 'added'::annex_action_enum);

--CREATE the update function, which will be executed when any of the triggers is triggered
CREATE OR REPLACE FUNCTION "public"."refresh_charged_rentas_view"()
    RETURNS "pg_catalog"."trigger" AS 
        $BODY$
            BEGIN
                REFRESH MATERIALIZED VIEW charged_rentas;
                RETURN NUll;
            END
        $BODY$
    LANGUAGE 'plpgsql';

--CREATE the triggers, monitoring INSERT, UPDATE or DELETE for any of the tables
DROP TRIGGER IF EXISTS "LAYER_KVS_TRIGGER" on "public"."layer_kvs";

CREATE TRIGGER "LAYER_KVS_TRIGGER" 
    AFTER INSERT OR UPDATE OF "gid",  "geom", "category", "area_type", "has_contracts", "used_area_by", "area_farming", "area_year", "used_area", "usable", "document_area", "is_edited"
    OR DELETE ON "public"."layer_kvs"
FOR EACH STATEMENT
EXECUTE PROCEDURE "refresh_charged_rentas_view"();

DROP TRIGGER IF EXISTS "SU_CHARGED_RENTA_NATURA_TRIGGER" on "public"."su_charged_renta_natura";

CREATE TRIGGER "SU_CHARGED_RENTA_NATURA_TRIGGER" 
    AFTER INSERT OR UPDATE OF "id", "renta_id", "amount", "nat_type", "nat_is_converted", "nat_unit_price" 
    OR DELETE ON "public"."su_charged_renta_natura"
FOR EACH STATEMENT
EXECUTE PROCEDURE "refresh_charged_rentas_view"();

DROP TRIGGER IF EXISTS "SU_CHARGED_RENTA_TRIGGER" on "public"."su_charged_renta";

CREATE TRIGGER "SU_CHARGED_RENTA_TRIGGER" 
    AFTER INSERT OR UPDATE OF "id", "contract_id", "year", "renta", "renta_nat", "plot_id", "nat_is_converted", "nat_unit_price", "owner_id" 
    OR DELETE ON "public"."su_charged_renta"
FOR EACH STATEMENT
EXECUTE PROCEDURE "refresh_charged_rentas_view"();

DROP TRIGGER IF EXISTS "SU_CONTRACTS_PLOTS_REL" on "public"."su_contracts_plots_rel";

CREATE TRIGGER "SU_CONTRACTS_PLOTS_REL" 
    AFTER INSERT OR UPDATE OF "id", "contract_id", "plot_id", "contract_area", "price_per_acre", "price_sum", "annex_action" 
    OR DELETE ON "public"."su_contracts_plots_rel"
FOR EACH STATEMENT
EXECUTE PROCEDURE "refresh_charged_rentas_view"();

DROP TRIGGER IF EXISTS "SU_CONTRACTS_TRIGGER" on "public"."su_contracts";

CREATE TRIGGER "SU_CONTRACTS_TRIGGER" 
    AFTER INSERT OR UPDATE OF "id", "c_num", "c_date", "nm_usage_rights", "sv_num", "sv_date", "start_date", "renta", "due_date", "renta_nat", "farming_id", "agg_type", "active", "parent_id", "is_annex", "renta_nat_type_id", "is_sublease", "original_due_date", "original_renta", "original_renta_nat", "original_renta_nat_type_id", "na_num", "payday" 
    OR DELETE ON "public"."su_contracts"
FOR EACH STATEMENT
EXECUTE PROCEDURE "refresh_charged_rentas_view"();

DROP TRIGGER IF EXISTS "SU_PLOTS_OWNERS_REL_TRIGGER" on "public"."su_plots_owners_rel";

CREATE TRIGGER "SU_PLOTS_OWNERS_REL_TRIGGER" 
    AFTER INSERT OR UPDATE OF "id", "pc_rel_id", "owner_id", "percent", "owner_document_id", "rep_id", "proxy_num", "proxy_date", "path", "is_heritor", "numerator", "denominator"
    OR DELETE ON "public"."su_plots_owners_rel"
FOR EACH STATEMENT
EXECUTE PROCEDURE "refresh_charged_rentas_view"();