--
-- PostgreSQL database dump
--

SET statement_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SET check_function_bodies = false;
SET client_min_messages = warning;

SET search_path = public, pg_catalog;

SET default_tablespace = '';

SET default_with_oids = false;

--
-- Name: su_osz_files; Type: TABLE; Schema: public; Owner: postgres; Tablespace: 
--

CREATE TABLE su_osz_files (
    id integer NOT NULL,
    land character varying(64) NOT NULL,
    ekatte character varying(5) NOT NULL,
    date date DEFAULT now() NOT NULL
);


ALTER TABLE public.su_osz_files OWNER TO postgres;

--
-- Name: su_osz_files_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE su_osz_files_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.su_osz_files_id_seq OWNER TO postgres;

--
-- Name: su_osz_files_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE su_osz_files_id_seq OWNED BY su_osz_files.id;


--
-- Name: id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY su_osz_files ALTER COLUMN id SET DEFAULT nextval('su_osz_files_id_seq'::regclass);


--
-- Name: su_osz_files_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres; Tablespace: 
--

ALTER TABLE ONLY su_osz_files
    ADD CONSTRAINT su_osz_files_pkey PRIMARY KEY (id);


--
-- PostgreSQL database dump complete
--



--
-- PostgreSQL database dump
--

SET statement_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SET check_function_bodies = false;
SET client_min_messages = warning;

SET search_path = public, pg_catalog;

SET default_tablespace = '';

SET default_with_oids = false;

--
-- Name: su_osz_files_plots; Type: TABLE; Schema: public; Owner: postgres; Tablespace: 
--

CREATE TABLE su_osz_files_plots (
    id integer NOT NULL,
    id_imot integer,
    kvs_no character varying(11),
    kad_no character varying(15),
    kod_subekt character varying(1),
    txt_subekt character varying(15),
    ime_subekt character varying(45),
    egn_subekt character varying(13),
    kod_pr_osn character varying(1),
    txt_pr_osn character varying(20),
    pl_dka numeric,
    pl_dka_po numeric,
    kategoria character varying(2),
    kod_ntp character varying(4),
    txt_ntp character varying(15),
    kod_sobstv character varying(2),
    txt_sobstv character varying(20),
    kod_imot character varying(1),
    txt_imot character varying(12),
    ekatte character varying(5),
    ver_no character varying(7),
    data character varying(15),
    vreme character varying(5),
    file_id integer NOT NULL
);


ALTER TABLE public.su_osz_files_plots OWNER TO postgres;

--
-- Name: su_osz_files_plots_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE su_osz_files_plots_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.su_osz_files_plots_id_seq OWNER TO postgres;

--
-- Name: su_osz_files_plots_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE su_osz_files_plots_id_seq OWNED BY su_osz_files_plots.id;


--
-- Name: id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY su_osz_files_plots ALTER COLUMN id SET DEFAULT nextval('su_osz_files_plots_id_seq'::regclass);


--
-- Name: su_osz_files_plots_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres; Tablespace: 
--

ALTER TABLE ONLY su_osz_files_plots
    ADD CONSTRAINT su_osz_files_plots_pkey PRIMARY KEY (id);


--
-- Name: su_osz_files_plots_file_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY su_osz_files_plots
    ADD CONSTRAINT su_osz_files_plots_file_id_fkey FOREIGN KEY (file_id) REFERENCES su_osz_files(id) ON DELETE CASCADE;


--
-- PostgreSQL database dump complete
--
