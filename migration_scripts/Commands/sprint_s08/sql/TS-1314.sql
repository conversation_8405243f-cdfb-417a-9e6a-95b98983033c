ALTER TABLE "public"."su_hypothecs_files"
DROP CONSTRAINT "su_hypothecs_fkey",
ADD CONSTRAINT "su_hypothecs_fkey" FOREIGN KEY ("hypothec_id") REFERENCES "public"."su_hypothecs" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

ALTER TABLE "public"."su_hypothecs_payments"
DROP CONSTRAINT "su_hypothecs_fkey",
ADD CONSTRAINT "su_hypothecs_fkey" FOREIGN KEY ("hypothec_id") REFERENCES "public"."su_hypothecs" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

ALTER TABLE "public"."su_hypothecs_plots_rel"
DROP CONSTRAINT "hypothecs_fkey",
ADD CONSTRAINT "hypothecs_fkey" FOREIGN KEY ("hypothec_id") REFERENCES "public"."su_hypothecs" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

DROP TABLE "public"."su_hypothecs_old"