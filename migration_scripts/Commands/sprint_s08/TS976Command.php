<?php

namespace TF\Commands\sprint_s08;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-976 command run on all databases.
 */
class TS976Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s08:TS-976')
            ->setDescription('Dobavqne na stoinost "new_boundary" kam izbroim tip edit_type_enum, izpolzvan v layer_kvs_edit_log');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $sql = "ALTER TYPE edit_type_enum ADD VALUE 'new_boundary'";
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -c "' . $sql . '"', $return);
    }
}
