<?php

namespace TF\Commands\sprint_s08;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-880 command run on all databases.
 */
class TS880Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s08:TS-880')
            ->setDescription('Sazdava materialized view, trigger i update funkciq za materialized view "charged_rentas" za oblekchavane na zaqvkata za "Vedomost".');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-880.sql', $return);
    }
}
