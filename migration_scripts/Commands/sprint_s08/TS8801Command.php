<?php

namespace TF\Commands\sprint_s08;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-880-1 command run on all databases.
 */
class TS8801Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s08:TS-880-1')
            ->setDescription('Preimenuva materialized view "harged_rentas" na "charged_rentas_mat_view". Dobavq "renta_nat_mat_view" i updateva vsichki trigger funkcii za refresh');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-880-1.sql', $return);
    }
}
