<?php

namespace TF\Commands\sprint_s08;

use PDO;
use Prado;
use TF\Commands\Common\UserDbCommand;

Prado::using('Plugins.Core.Layers.*');
Prado::using('Plugins.Core.Layers.conf');
/**
 * TS-1387 command run on all databases.
 */
class TS1387Command extends UserDbCommand
{
    protected $skipLogging = true;

    /**
     * Checks if a given column exists in a given table.
     *
     * @param PDO $db a PDO instance
     * @param string $column the column name
     * @param string $table the table name
     *
     * @return bool
     */
    public function isColumnExists(PDO $db, $column, $table)
    {
        $sql = 'SELECT
            TRUE
        FROM
            information_schema. COLUMNS
        WHERE
            TABLE_NAME = :table
        AND COLUMN_NAME = :column';
        $sql = $db->prepare($sql);
        $sql->bindValue(':column', $column);
        $sql->bindValue(':table', $table);
        $sql->execute();

        return (bool) $sql->fetchColumn();
    }

    /**
     * Adds 'is_ixist' column to 'su_users_layers' table.
     *
     * @param PDO $db a PDO instance
     */
    public function addIsExistColumn(PDO $db)
    {
        $sql = 'ALTER TABLE su_users_layers ADD COLUMN is_exist bool DEFAULT false NOT NULL';
        $sql = $db->prepare($sql);
        $sql->execute();

        $sql = 'UPDATE su_users_layers ul
                SET is_exist = FALSE;';
        $sql = $db->prepare($sql);
        $sql->execute();
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s08:TS-1387')
            ->setDescription('Skrivane na vsichki sloeve v darvoto sas sloeve, za koito nyama zaredeni danni.');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $this->openConnection();

        if (!$this->isColumnExists($this->mainConnection, 'is_exist', 'su_users_layers')) {
            $output->writeln('Adding `is_exist` column.');
            $this->addIsExistColumn($this->mainConnection);
        }

        $sql = $this->mainConnection->prepare(
            'SELECT database, id FROM su_users
            WHERE database = :userDb 
            AND level = 2 
            ORDER BY database'
        );

        $sql->bindParam(':userDb', $userDb);

        $sql->execute();
        $results = $sql->fetchAll();
        $userId = $results[0]['id'];

        $dbhDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        // Select users's tables
        $sql = "SELECT
                    table_name
                FROM
                    information_schema.tables T
                WHERE
                    table_schema = 'public'
                AND T .table_type = 'BASE TABLE'
                AND TABLE_NAME ILIKE 'layer%'
                AND TABLE_NAME NOT IN (
                    'layer_kvs_edit_log',
                    'layer_kvs_copy',
                    'layer_kvs',
                    'layer_gps',
                    'layer_satellite_work'
                )
                AND TABLE_NAME NOT ILIKE 'layer_tmp%';";

        $sql = $dbhDev->prepare($sql);
        $sql->execute();
        $resultTables = $sql->fetchAll();

        for ($j = 0; $j < count($resultTables); $j++) {
            $sql = 'UPDATE su_users_layers SET is_exist = TRUE
                    WHERE user_id = ' . $userId . " AND table_name = '" . $userDb . "'";
            $sql = $this->mainConnection->prepare($sql);
            $sql->execute();
        }

        $sql = 'UPDATE su_users_layers
                SET is_exist = TRUE
                WHERE user_id = ' . $userId . "
                AND table_name IN ('layer_kvs',
                'layer_gps',
                'layer_satellite_work',
                'layer_pzp',
                'layer_natura_2000',
                'layer_lfa',
                'layer_vps_orli_leshoyadi',
                'layer_vps_livaden_blatar',
                'layer_vps_gaski_zimni',
                'layer_vps_gaski_chervenogushi',
                'layer_vps_merg');";
        $sql = $this->mainConnection->prepare($sql);
        $sql->execute();
    }
}
