<?php

namespace TF\Commands\sprint_s08;

use PDO;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Commands\ParamHelper;

/**
 * TS-862 command run on all databases.
 */
class TS862Command extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('sprint_s08:TS-862')
            ->setDescription('Sazdava lipsvashti Primary Keys na tablici.')
            ->addArgument(
                'user_databases',
                InputArgument::IS_ARRAY | InputArgument::OPTIONAL,
                'Databases to run the script on (separate multiple names with a space)?'
            )
            ->addOption('new_user_db', null, InputOption::VALUE_NONE, 'If set, the task will also  run on the new_user database ');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $user_databases = $input->getArgument('user_databases');

        $paramHelper = new ParamHelper();
        $userDbString = $paramHelper->createQueryParamString($user_databases);

        $userDbConditions = 'true';
        if ($user_databases) {
            $userDbConditions = 'u.database IN (' . $userDbString . ')';
        }

        $mainDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = $mainDev->prepare(
            "SELECT database FROM su_users u
			WHERE {$userDbConditions} AND u.level = 2 
			ORDER BY database"
        );

        $sql->execute();
        $results = $sql->fetchAll();

        if ($input->getOption('new_user_db')) {
            $results[] = ['database' => 'new_users_db'];
        }

        for ($i = 0; $i < count($results); $i++) {
            $sql = "SELECT 1 from pg_database WHERE datname='" . $results[$i]['database'] . "';";
            $sqlSelect = $mainDev->prepare($sql);
            $sqlSelect->execute();
            $result = $sqlSelect->fetchAll();

            // id db does not exists
            if (!count($result)) {
                continue;
            }

            $dbName = $results[$i]['database'];
            $dbhDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $dbName . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
            $output->writeln("Database: {$dbName} ");

            // Get all tables without primary key defined
            $cmd = $dbhDev->prepare("
			SELECT
			    n.nspname AS schema_name,
		    	C .relname AS table_name,
		    	C .relhaspkey AS has_pk
			FROM
			    pg_catalog.pg_class C
			JOIN pg_namespace n ON (
			    C .relnamespace = n.oid
			    AND n.nspname NOT IN (
			        'information_schema',
			        'pg_catalog'
			    )
			    AND C .relkind = 'r'
			)
			WHERE C .relhaspkey = false
			ORDER BY
			    C .relhaspkey,
			    C .relname;");
            $cmd->execute();
            $resultData = $cmd->fetchAll();

            for ($j = 0; $j < count($resultData); $j++) {
                $sql = 'ALTER TABLE ONLY ' . $resultData[$j]['table_name'] . ' ADD CONSTRAINT ' . $resultData[$j]['table_name'] . '_id_seq_pkey PRIMARY KEY (id);';

                system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $dbName . ' -c "' . $sql . '"', $return);
            }
        }
    }
}
