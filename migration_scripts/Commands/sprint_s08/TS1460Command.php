<?php

namespace TF\Commands\sprint_s08;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-1460 command run on all databases.
 */
class TS1460Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s08:TS-1460')
            ->setDescription('Syzdavane na mat views - charged_rentas_mat_view, charged_rentas_annexes_mat_view, renta_nats_annexes_mat_view, renta_nats_mat_view');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-1460.sql', $return);
    }
}
