<?php

namespace TF\Commands\sprint_s08;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-879 command run on all databases.
 */
class TS879Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s08:TS-879')
            ->setDescription('Sazdava kolonata unit_value v su_renta_types');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $sql = 'ALTER TABLE su_renta_types ADD COLUMN unit_value float4;';
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -c "' . $sql . '"', $return);
    }
}
