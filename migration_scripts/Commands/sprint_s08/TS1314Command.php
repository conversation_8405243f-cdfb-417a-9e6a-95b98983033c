<?php

namespace TF\Commands\sprint_s08;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-1314 command run on all databases.
 */
class TS1314Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s08:TS-1314')
            ->setDescription('Tarsi potrebitelski bazi, v koito ima tablica "su_hypothecs_old" i prenasochva foreign keys ot neq kam su_hypothecs i nakraq iztriva "su_hypothecs_old"');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        // Create new connection to the selected user DB
        $secondaryDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
        $secondaryDev->setAttribute(PDO::ATTR_EMULATE_PREPARES, true);
        $secondaryDev->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_WARNING);
        $checkForExistingSql = "SELECT EXISTS (
								SELECT 1
								FROM   information_schema.tables 
								WHERE  table_schema = 'public'
								AND    table_name = 'su_hypothecs_old'
								)";

        $secondarySqlSelect = $secondaryDev->prepare($checkForExistingSql);
        $secondarySqlSelect->execute();
        $oldTableExistsResults = $secondarySqlSelect->fetchAll();

        if ($oldTableExistsResults[0]['exists']) {
            system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-1314.sql', $return);
        }

        $secondaryDev = null;
    }
}
