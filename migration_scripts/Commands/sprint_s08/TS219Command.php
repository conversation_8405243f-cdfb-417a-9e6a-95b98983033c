<?php

namespace TF\Commands\sprint_s08;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-219 command run on all databases.
 */
class TS219Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s08:TS-219')
            ->setDescription('Sazdava kolonite plot_name i plot_info v layer_gps');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $sql = 'ALTER TABLE layer_gps ADD COLUMN plot_name varchar;
	    ALTER TABLE layer_gps ADD COLUMN plot_info varchar;';
        $sql = str_replace("\r", ' ', $sql);

        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -c "' . $sql . '"', $return);
    }
}
