<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4862Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4862')
            ->setDescription('Create db function ');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec("CREATE OR REPLACE FUNCTION contract_with_annexes_cover_period(
                p_contract_id INT,
                period_start DATE,
                period_end DATE
            )
            RETURNS BOOLEAN AS $$
            BEGIN
                RETURN (
                    WITH
                    -- Step 1: Get all periods for the contract and its annexes.
                    all_periods AS (
                        SELECT
                            start_date,
                            COALESCE(due_date, '9999-12-31'::date) as due_date
                        FROM
                            su_contracts
                        WHERE id = p_contract_id
                        UNION ALL
                        SELECT
                            start_date,
                            COALESCE(due_date, '9999-12-31'::date) as due_date
                        FROM
                            su_contracts
                        WHERE
                            parent_id = p_contract_id
                            AND active = true
                    ),

                    -- Step 2: Identify which rows have a gap before them.
                    periods_with_gap_flags AS (
                        SELECT
                            start_date,
                            due_date,
                            CASE
                                WHEN start_date > LAG(due_date, 1) OVER (ORDER BY start_date) + INTERVAL '1 day'
                                THEN 1  -- Flag this row as having a gap before it
                                ELSE 0
                            END AS is_a_gap
                        FROM all_periods
                    ),

                    -- Step 3: Now, aggregate the flags and boundaries from the step above.
                    coverage_stats AS (
                        SELECT
                            MIN(start_date) AS overall_start,
                            MAX(due_date) AS overall_end,
                            -- Summing the flags gives us the total count of gaps.
                            SUM(is_a_gap) AS gap_count
                        FROM periods_with_gap_flags
                    )

                    -- Final Step: The logic remains the same. Check for zero gaps and date containment.
                    SELECT
                        gap_count = 0
                        AND period_start >= overall_start
                        AND period_end <= overall_end
                    FROM coverage_stats
                );
            END;
            $$ LANGUAGE plpgsql;
        ");
    }
}
