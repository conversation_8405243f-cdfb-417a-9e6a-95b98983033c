<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4936Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4936')
            ->setDescription('Change layer_kvs is_edited column to be not nullable and default to false');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec('ALTER TABLE layer_kvs DISABLE TRIGGER "LAYER_KVS_TRIGGER";');
        $pdo->exec('ALTER TABLE layer_kvs DISABLE TRIGGER refresh_views_after_update;');

        $pdo->exec('UPDATE layer_kvs SET is_edited = false WHERE is_edited IS NULL');

        $pdo->exec('ALTER TABLE layer_kvs ENABLE TRIGGER "LAYER_KVS_TRIGGER";');
        $pdo->exec('ALTER TABLE layer_kvs ENABLE TRIGGER refresh_views_after_update;');

        $pdo->exec('ALTER TABLE layer_kvs ALTER COLUMN is_edited SET NOT NULL');
        $pdo->exec('ALTER TABLE layer_kvs ALTER COLUMN is_edited SET DEFAULT false');
    }
}
