<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4959Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4959')
            ->setDescription('Fix farming_id in su_charged_renta_params for records made with farming \'Всички\'');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec('UPDATE su_charged_renta_params SET farming_id = 0 WHERE farming_id = 1');
    }
}
