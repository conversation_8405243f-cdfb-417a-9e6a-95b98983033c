<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4878_3Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4878-3')
            ->setDescription('Create db function number_of_contracts_by_user_and_plot');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec("CREATE OR REPLACE FUNCTION 
            number_of_contracts_by_user_and_plot(keycloak_uid TEXT, plot_id INT) RETURNS INT AS 
            $$
                DECLARE number_of_contracts INT;
                BEGIN 
                    SELECT
                        count(DISTINCT c.id) + count(DISTINCT sublease_contracts.id) + count(DISTINCT sales_contracts.id)
                    INTO
                        number_of_contracts
                    FROM
                        layer_kvs AS kvs
                    LEFT JOIN su_contracts_plots_rel AS scpr
                        ON scpr.plot_id = kvs.gid
                    JOIN su_contracts AS c
                        ON c.id = scpr.contract_id
                        AND c.nm_usage_rights <> 4 -- Skip agreement contracts because they should not appear in FE.
                    JOIN user_farming_permissions AS ufp
                        ON ufp.farming_id = c.farming_id
                        AND ufp.\"permission\" = 'read'
                        AND ufp.keycloak_uid::TEXT=number_of_contracts_by_user_and_plot.keycloak_uid
                    LEFT JOIN LATERAL (
                            SELECT
                                sspcr.sublease_id
                            FROM
                                su_subleases_plots_contracts_rel sspcr
                            JOIN su_contracts AS sbc
                                ON sbc.id = sspcr.sublease_id
                            JOIN user_farming_permissions AS ufp
                                ON ufp.farming_id = sbc.farming_id
                                AND ufp.\"permission\" = 'read'
                                AND ufp.keycloak_uid::TEXT=number_of_contracts_by_user_and_plot.keycloak_uid
                            WHERE
                                sspcr.pc_rel_id = scpr.id
                        ) AS sublease_contracts(id) ON
                        TRUE
                    LEFT JOIN LATERAL (
                            SELECT
                                sscpr.contract_id
                            FROM
                                su_sales_contracts_plots_rel sscpr
                            JOIN su_contracts AS sac
                                ON sac.id = sscpr.sales_contract_id
                            JOIN user_farming_permissions AS ufp
                                ON ufp.farming_id = sac.farming_id
                                AND ufp.\"permission\" = 'read'
                                AND ufp.keycloak_uid::TEXT=number_of_contracts_by_user_and_plot.keycloak_uid
                            WHERE
                                sscpr.pc_rel_id = scpr.id
                        ) AS sales_contracts(id) ON
                        TRUE
                    WHERE
                        kvs.gid = number_of_contracts_by_user_and_plot.plot_id
                    GROUP BY
                        kvs.gid;

                    RETURN number_of_contracts;
                END;
            $$ LANGUAGE plpgsql;
        ");

        // Note:
        // This function is a wrapper for the original function, which accepts UUID as keycloak_uid.
        // In the original function the parameter keycloak_uid is of type TEXT so it does not throw an error
        // if an invalid UUID is passed, but returns null instead
        $pdo->exec('CREATE OR REPLACE FUNCTION number_of_contracts_by_user_and_plot(keycloak_uid UUID, plot_id INT) 
            RETURNS INT AS 
            $$
            BEGIN
                -- Calls the original function, casting keycloak_uid to TEXT
                RETURN number_of_contracts_by_user_and_plot(keycloak_uid::TEXT, plot_id);
            END;
            $$ LANGUAGE plpgsql;
        ');
    }
}
