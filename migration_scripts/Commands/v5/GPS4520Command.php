<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4520Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4520')
            ->setDescription('Remove from style labels that are not in the table and update the label column in the layers tables');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $recordsForUpdateCmd = $pdo->prepare("SELECT
            sls.layer_id
            , c.table_name
            , coalesce(json_agg(c.column_name) filter(where c.column_name notnull), '[]'::json) AS labels_json
            , coalesce(array_agg(c.column_name) filter(where c.column_name notnull), '{}'::text[]) AS labels_arr
            , pmv.matviewname notnull as is_matview
        FROM
            su_layer_styles sls
            LEFT JOIN UNNEST(sls.labels) AS label ON TRUE
            LEFT JOIN information_schema.columns c ON label = c.column_name
                AND sls.table_name = c.table_name
            LEFT JOIN pg_matviews pmv on pmv.matviewname=sls.table_name and schemaname = 'public'
        GROUP BY
            sls.layer_id
            , sls.table_name
            , pmv.matviewname
            ,c.table_name
        ORDER BY
            sls.table_name
        ");

        $recordsForUpdateCmd->execute();
        $recordsForUpdate = $recordsForUpdateCmd->fetchAll(PDO::FETCH_ASSOC);
        $output->warn('Found ' . count($recordsForUpdate) . ' records for update');

        foreach ($recordsForUpdate as $record) {
            $layerId = $record['layer_id'];
            $table = $record['table_name'];
            $isMatview = $record['is_matview'];

            $labelsColumnsLst = implode(',', json_decode($record['labels_json'], true));

            $sql = "UPDATE su_layer_styles SET labels = '{$record['labels_arr']}' WHERE layer_id = '{$layerId}'";
            $pdo->prepare($sql)->execute();
            if ($isMatview) {
                continue;
            }
            if (empty($table)) {
                continue;
            }
            $labelSQL = "concat_ws('^', {$labelsColumnsLst})";
            if (0 === strlen($labelsColumnsLst)) {
                $labelSQL = "'{}'";
            }
            if ('layer_kvs' === $table) {
                [$id, $ekatte] = explode('_', $layerId);
                $sqlTable = "UPDATE {$table} set label={$labelSQL} where ekate = '{$ekatte}'";
                $pdo->prepare($sqlTable)->execute();

                continue;
            }
            $sqlTable = "UPDATE {$table} set label={$labelSQL}";
            $pdo->prepare($sqlTable)->execute();
        }
    }
}
