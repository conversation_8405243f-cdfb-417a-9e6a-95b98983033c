<?php

namespace TF\Commands\v5;

use PDO;
use TF\Application\Common\Config;
use TF\Application\Entity\LayerStyles;
use TF\Commands\Common\MainDbCommand;

class GPS4492Command extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4492')
            ->setDescription('Migration for adding missing styles in su_users_layers');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $layerTypes = [
            Config::LAYER_TYPE_ZP,
            Config::LAYER_TYPE_GPS,
            Config::LAYER_TYPE_KMS,
            Config::LAYER_TYPE_KVS,
            Config::LAYER_TYPE_ISAK,
            Config::LAYER_TYPE_FOR_ISAK,
        ];
        $layerTypesString = implode(',', $layerTypes);

        $defaultStyles = json_encode(LayerStyles::generateDefaultStyle());

        $pdo->exec("UPDATE su_users_layers SET style = '{$defaultStyles}' WHERE layer_type IN({$layerTypesString}) AND style IS NULL");
        $output->writeln('Done');
    }
}
