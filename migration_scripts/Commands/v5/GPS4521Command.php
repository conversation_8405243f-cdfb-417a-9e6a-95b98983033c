<?php

namespace TF\Commands\v5;

use PDO;
use TF\Application\Common\Config;
use TF\Commands\Common\UserDbCommand;

class GPS4521Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4521')
            ->setDescription('Add missing allowable layers in su_layer_styles.');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $layersConfig = $GLOBALS['Layers']['customLayers'];
        $allowableLayersTables = [Config::LAYER_TYPE_ALLOWABLE, Config::LAYER_TYPE_ALLOWABLE_FINAL];

        $layersCmd = $pdo->prepare("SELECT id,table_name FROM su_layer_styles WHERE table_name IN ('" . Config::LAYER_TYPE_ALLOWABLE . "', '" . Config::LAYER_TYPE_ALLOWABLE_FINAL . "')");
        $layersCmd->execute();
        $layerStyles = $layersCmd->fetchAll(PDO::FETCH_ASSOC);

        $existingStyleLayers = array_column($layerStyles, 'table_name');
        foreach ($allowableLayersTables as $table) {
            if (!in_array($table, $existingStyleLayers)) {
                $sql = 'INSERT INTO 
                su_layer_styles (layer_id, table_name, "type", transparency, fill_column_name, fill_color, border_column_name, border_color, border_width, border_only, labels, label_size, tags) 
                    VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)';
                $insertCmd = $pdo->prepare($sql);
                $insertCmd->execute([
                    $layersConfig[$table]['id'],
                    $table,
                    'single',
                    0,
                    null,
                    $layersConfig[$table]['fill_color'],
                    null,
                    $layersConfig[$table]['border_color'],
                    1,
                    'false',
                    '{}',
                    8,
                    true,
                ]);
            }
        }
    }
}
