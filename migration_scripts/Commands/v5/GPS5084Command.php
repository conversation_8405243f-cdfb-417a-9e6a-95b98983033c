<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS5084Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-5084')
            ->setDescription('Set contract_area = document area if the contract area is bigger than the document area');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec(
            'update su_contracts_plots_rel 
            set contract_area = kvs.document_area
            from layer_kvs kvs
            where 
                kvs.gid = su_contracts_plots_rel.plot_id 
                and kvs.document_area < su_contracts_plots_rel.contract_area'
        );

        $pdo->exec(
            'update su_contracts_plots_rel 
            set area_for_rent = contract_area
            where 
                area_for_rent is not null
                and area_for_rent > contract_area'
        );
    }
}
