<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS3573_10Command run on client databases.
 *
 * This command will create and seed column virtual_category_title to layer_kvs tables
 */
class GPS4958Command extends UserDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec('
            UPDATE 
                layer_kvs
            SET 
                edit_active_from = NULL
            WHERE
                is_edited = false
                AND edit_active_from IS NOT NULL
        ');
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4958')
            ->setDescription('Update edit_active_from to null where is edited is false');
    }
}
