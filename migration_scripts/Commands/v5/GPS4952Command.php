<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4952Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4952')
            ->setDescription('Prepend schema name before enums and su_contracts table name in get_contract_status(), get_document_status_by_dates(), get_ekatte_name_by_code(), get_plot_category_by_id(), get_contract_type_by_id(), get_ntp_title_by_code() functions');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $sql = "CREATE OR REPLACE FUNCTION public.get_contract_status(contract_id integer, active boolean, start_date timestamp without time zone, due_date timestamp without time zone, check_annexes boolean DEFAULT true)
                RETURNS public.contract_status_enum
                LANGUAGE plpgsql
                IMMUTABLE
                AS \$function$       
                            DECLARE
                                contract_status public.contract_status_enum;
                                annexed_contract_count int := 0;
                            BEGIN
                                IF check_annexes THEN
                                    SELECT COUNT(*) INTO annexed_contract_count
                                    FROM public.su_contracts AS c
                                    WHERE c.parent_id = contract_id AND c.active = TRUE;
                                END IF;

                                contract_status := CASE 
                                    WHEN check_annexes AND annexed_contract_count > 0 THEN 'Annexed'::public.contract_status_enum
                                    WHEN active = FALSE THEN 'Canceled'::public.contract_status_enum
                                    WHEN active = TRUE AND start_date > now() THEN 'Upcoming'::public.contract_status_enum
                                    WHEN active = TRUE AND (COALESCE(due_date, '9999-01-01')::date >= now()::date) THEN 'Active'::public.contract_status_enum
                                    ELSE 'Expired'::public.contract_status_enum
                                END;

                                RETURN contract_status;
                            END;
                            \$function$
                ;";
        $stmt = $pdo->prepare($sql);
        $stmt->execute();

        $sql = "CREATE OR REPLACE FUNCTION public.get_document_status_by_dates(start_date date, end_date date)
                RETURNS public.contract_status_enum
                LANGUAGE plpgsql
                AS \$function$
                                DECLARE
                                    status public.contract_status_enum;

                                BEGIN
                                    IF start_date <= now() AND end_date >= now() THEN
                                        status := 'Active'::public.contract_status_enum;
                                    ELSIF start_date > now() THEN
                                        status := 'Upcoming'::public.contract_status_enum;
                                    ELSIF end_date < now() THEN
                                        status := 'Expired'::public.contract_status_enum;
                                    ELSE
                                        status := null;
                                    END IF;

                                    RETURN status;
                                END;
                            \$function$
                ;";
        $stmt = $pdo->prepare($sql);
        $stmt->execute();

        $sql = "CREATE OR REPLACE FUNCTION public.get_ekatte_name_by_code(code character varying)
                RETURNS character varying
                LANGUAGE plpgsql
                IMMUTABLE
                AS \$function$
                    -- Note: If you use this function in virtual column, in order to refresh the calculated value
                    -- you need to update the virtual column - for example: UPDATE {table} set {virtual_column_name} = {virtual_column_name}
                    DECLARE res VARCHAR;
                    BEGIN
                        SELECT 
                            ekatte_name || ' (' || ekatte_code || ')' INTO res
                        FROM
                            public.su_ekatte
                        WHERE
                            ekatte_code = code;
                        RETURN res;
                    END;
                \$function$
        ;";
        $stmt = $pdo->prepare($sql);
        $stmt->execute();

        $sql = "CREATE OR REPLACE FUNCTION public.get_plot_category_by_id(category_id character varying)
                RETURNS character varying
                LANGUAGE plpgsql
                IMMUTABLE
                AS \$function$
                    -- Note: If you use this function in virtual column, in order to refresh the calculated value
                    -- you need to update the virtual column - for example: UPDATE {table} set {virtual_column_name} = {virtual_column_name}
                    DECLARE res VARCHAR;
                    BEGIN
                        SELECT 
                            title INTO res
                        FROM
                            public.su_plot_categories
                        WHERE
                            category_id ~ '^[0-9]+$'
                            AND id = category_id::INT;

                        IF res IS NULL THEN
                            res := category_id;
                        END IF;

                        RETURN res;
                    END;
                \$function$
        ;";
        $stmt = $pdo->prepare($sql);
        $stmt->execute();

        $sql = 'CREATE OR REPLACE FUNCTION public.get_contract_type_by_id(type_id integer)
                RETURNS character varying
                LANGUAGE plpgsql
                IMMUTABLE
                AS $function$
                    -- Note: If you use this function in virtual column, in order to refresh the calculated value
                    -- you need to update the virtual column - for example: UPDATE {table} set {virtual_column_name} = {virtual_column_name}
                    DECLARE res VARCHAR;
                    BEGIN
                        SELECT 
                            title INTO res
                        FROM
                            public.su_contract_types
                        WHERE
                            id = type_id;

                        RETURN res;
                    END;
                $function$
                ;';
        $stmt = $pdo->prepare($sql);
        $stmt->execute();

        $sql = "CREATE OR REPLACE FUNCTION public.get_ntp_title_by_code(code character varying)
                RETURNS character varying
                LANGUAGE plpgsql
                IMMUTABLE
                AS \$function$
                    -- Note: If you use this function in virtual column, in order to refresh the calculated value
                    -- you need to update the virtual column - for example: UPDATE {table} set {virtual_column_name} = {virtual_column_name}
                    DECLARE res VARCHAR;
                    BEGIN
                        SELECT 
                            title INTO res
                        FROM
                            public.su_area_types
                        WHERE
                            code ~ '^[0-9]+$'
                            AND (
                                id = code::INT
                                OR code::INT = ANY(additional_codes)
                            );

                        IF res IS NULL THEN
                            res := code::VARCHAR;
                        END IF;
                        RETURN res;
                    END;
                \$function$
        ;";
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
    }
}
