<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS5064Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-5064')
            ->setDescription('Set kvs_allowable_area in su_contracts_plots_rel to be allowable_area from layer_kvs where it is null or 0');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec('update 
                                    su_contracts_plots_rel 
                                set 
                                    kvs_allowable_area = kvs.allowable_area 
                                from 
                                    layer_kvs kvs
                                where 
                                    (kvs_allowable_area is null or kvs_allowable_area = 0)
                                    and kvs.gid = su_contracts_plots_rel.plot_id');
    }
}
