<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4713Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4713')
            ->setDescription('Add virtual_ekatte_name and virtual_category_title column in su_charged_renta_history table');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec('ALTER TABLE su_charged_renta_history ADD virtual_ekatte_name varchar GENERATED ALWAYS AS (get_ekatte_name_by_code(ekate)) STORED NULL;');
        $pdo->exec('ALTER TABLE su_charged_renta_history ADD virtual_category_title varchar GENERATED ALWAYS AS (get_plot_category_by_id(category)) STORED NULL;');
    }
}
