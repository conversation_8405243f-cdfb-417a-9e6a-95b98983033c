<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4742_2Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4742-2')
            ->setDescription('Insert data in tables su_units_of_measure_categories and su_units_of_measure');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $this->executeSql($pdo, __DIR__ . '/sql/GPS4742_1.sql');
        $this->executeSql($pdo, __DIR__ . '/sql/GPS4742_2.sql');
    }
}
