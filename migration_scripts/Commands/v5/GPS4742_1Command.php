<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4742_1Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4742-1')
            ->setDescription('Create new tables su_units_of_measure_categories and su_units_of_measure');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec('CREATE TABLE su_units_of_measure_categories (
                id serial4 PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                created_at TIMESTAMP DEFAULT now(),
                updated_at TIMESTAMP DEFAULT now(),
                icon XML DEFAULT NULL
            );
        ');

        $pdo->exec('CREATE TABLE su_units_of_measure (
                id serial4 PRIMARY KEY,
                full_name VARCHAR(255) NOT NULL,
                short_name VARCHAR(255) NOT NULL,
                category_id INT NOT NULL,
                base_unit_id INT NULL,
                coefficient DECIMAL(10, 2) DEFAULT 1,
                numerator_unit_id INT NULL,
                denominator_unit_id INT NULL,
                created_at TIMESTAMP DEFAULT current_timestamp,
                updated_at TIMESTAMP DEFAULT current_timestamp,
                FOREIGN KEY (category_id) REFERENCES su_units_of_measure_categories(id) ON DELETE CASCADE ON UPDATE CASCADE,
                FOREIGN KEY (base_unit_id) REFERENCES su_units_of_measure(id) ON DELETE CASCADE ON UPDATE CASCADE,
                FOREIGN KEY (numerator_unit_id) REFERENCES su_units_of_measure(id) ON DELETE CASCADE ON UPDATE CASCADE,
                FOREIGN KEY (denominator_unit_id) REFERENCES su_units_of_measure(id) ON DELETE CASCADE ON UPDATE CASCADE
            );
        ');
    }
}
