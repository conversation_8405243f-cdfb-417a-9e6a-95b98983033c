<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\MainDbCommand;

class GPS4664Command extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4664')
            ->setDescription('Set col_personalizable to false for reference columns in work layers');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec("WITH virtual_columns_definitions AS (
            -- select all definitions having col_virtual: true and col_reference different than null for work layers
            SELECT 
                id AS layer_id,
                elem AS virtual_col_def,
                definitions AS all_def
            FROM 
                su_users_layers,
                jsonb_array_elements(definitions) AS elem
            WHERE 
                elem->>'col_reference' IS NOT NULL
                AND (elem->>'col_virtual')::boolean = true
                AND layer_type = 19
        ),
        reference_columns_definitions AS (
            -- select all definitions and all reference columns having col_personalizable: true for the work layers
            SELECT 
                layer_id,
                array_agg(reference_col_def->>'col_name') AS reference_cols,
                all_def
            FROM 
                virtual_columns_definitions,
                LATERAL (
                    SELECT elem AS reference_col_def
                    FROM jsonb_array_elements(all_def) elem
                    WHERE elem->>'col_name' = (virtual_col_def->>'col_reference')
                    AND (elem->>'col_personalizable')::boolean = true
                ) ref_col
            GROUP BY
                layer_id,
                all_def
        ),
        layers_to_update AS (
            -- select layer_id, and updated definitions (where the col_personalizable is set to false for the reference columns) for all work layers 
            SELECT 
                layer_id,
        --		reference_cols AS cols_to_update,
        --		all_def,
                modified_def
            FROM 
                reference_columns_definitions,
                LATERAL (
                    SELECT jsonb_agg(
                        CASE 
                            WHEN elem->>'col_name' = ANY(reference_cols)
                            THEN jsonb_set(elem, '{col_personalizable}', 'false'::jsonb)
                            ELSE elem
                        END
                    ) AS modified_def
                    FROM jsonb_array_elements(all_def) elem
                ) AS modified_definitions
        )
        UPDATE su_users_layers
        SET 
            definitions = layers_to_update.modified_def
        FROM 
            layers_to_update
        WHERE
            su_users_layers.id = layers_to_update.layer_id
       ");
    }
}
