<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

class GPS4878_1Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4878-1')
            ->setDescription('Create view user_farming_permissions');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $UserDbController = new UserDbController($this->userDbName);

        $UserDbController->dropViewIfExists('user_farming_permissions');
        $UserDbController->createUserFarmingPermissionsView($this->organizationId);
    }
}
