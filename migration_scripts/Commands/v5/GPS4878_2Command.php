<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\MainDbCommand;

class GPS4878_2Command extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4878-2')
            ->setDescription('Create index idx_layer_id_ekatte on su_layer_styles table');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_layer_id_ekatte ON su_layer_styles (
                split_part(layer_id, '_', 2)
            );
        ");
    }
}
