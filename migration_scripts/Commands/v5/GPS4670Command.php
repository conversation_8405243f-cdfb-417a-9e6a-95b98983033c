<?php

namespace TF\Commands\v5;

use PDO;
use TF\Application\Common\Config;
use TF\Commands\Common\UserDbCommand;

class GPS4670Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4670')
            ->setDescription('Add new system layers, landscape elements, mowing, and phycial blocks');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $layers = [
            Config::LAYER_TYPE_LANDSCAPE_ELEMENTS_POINTS => [
                'name' => 'Ландшафтни елементи (точки)',
                'table_name' => 'layer_landscape_elements_points',
                'layer_type' => Config::LAYER_TYPE_LANDSCAPE_ELEMENTS_POINTS,
                'extent' => Config::DEFAULT_MAX_EXTENT,
                'group_id' => $this->organizationId,
                'user_id' => $this->organizationId,
                'is_exist' => 1,
                'definitions' => $GLOBALS['Layers']['definitions'][Config::LAYER_TYPE_LANDSCAPE_ELEMENTS_POINTS],
                'fill_color' => '#e17d56',
                'border_color' => '#000000',
                'feature_label' => 'type_bg',
                'transparency' => 0,
                'border_width' => 6,
                'type' => 'single',
            ],
            Config::LAYER_TYPE_LANDSCAPE_ELEMENTS_LINES => [
                'name' => 'Ландшафтни елементи (линейни)',
                'table_name' => 'layer_landscape_elements_lines',
                'layer_type' => Config::LAYER_TYPE_LANDSCAPE_ELEMENTS_LINES,
                'extent' => Config::DEFAULT_MAX_EXTENT,
                'group_id' => $this->organizationId,
                'user_id' => $this->organizationId,
                'is_exist' => 1,
                'definitions' => $GLOBALS['Layers']['definitions'][Config::LAYER_TYPE_LANDSCAPE_ELEMENTS_LINES],
                'fill_color' => '#e17d56',
                'border_color' => '#000000',
                'feature_label' => 'type_bg',
                'transparency' => 0,
                'border_width' => 1,
                'type' => 'single',
            ],
            Config::LAYER_TYPE_LANDSCAPE_ELEMENTS_POLYGONS => [
                'name' => 'Ландшафтни елементи (площи)',
                'table_name' => 'layer_landscape_elements_polygons',
                'layer_type' => Config::LAYER_TYPE_LANDSCAPE_ELEMENTS_POLYGONS,
                'extent' => Config::DEFAULT_MAX_EXTENT,
                'group_id' => $this->organizationId,
                'user_id' => $this->organizationId,
                'is_exist' => 1,
                'definitions' => $GLOBALS['Layers']['definitions'][Config::LAYER_TYPE_LANDSCAPE_ELEMENTS_POLYGONS],
                'fill_color' => '#e17d56',
                'border_color' => '#000000',
                'feature_label' => 'type_bg',
                'transparency' => 70,
                'border_width' => 1,
                'type' => 'single',
            ],
            // Config::LAYER_TYPE_PHYSICAL_BLOCKS_PRELIMINARY => [
            //     'name' => 'Физически блокове 2024 (проект)',
            //     'table_name' => 'layer_physical_blocks_preliminary',
            //     'layer_type' => Config::LAYER_TYPE_PHYSICAL_BLOCKS_PRELIMINARY,
            //     'extent' => Config::DEFAULT_MAX_EXTENT,
            //     'group_id' => $this->organizationId,
            //     'user_id' => $this->organizationId,
            //     'is_exist' => 1,
            //     'definitions' => $GLOBALS['Layers']['definitions'][Config::LAYER_TYPE_PHYSICAL_BLOCKS_PRELIMINARY],
            //     'fill_color' => '#d53cd5',
            //     'border_color' => '#000000',
            //     'feature_label' => 'PHBIDENT',
            //     'transparency' => 70,
            //     'border_width' => 1,
            //     'type' => 'single',
            // ],
            // Config::LAYER_TYPE_PERMANENT_GRASSLAND_FOR_MOWING_PRELIMINARY => [
            //     'name' => 'ПЗП за косене 2024 (проект)',
            //     'table_name' => 'layer_permanent_grassland_for_mowing_preliminary',
            //     'layer_type' => Config::LAYER_TYPE_PERMANENT_GRASSLAND_FOR_MOWING_PRELIMINARY,
            //     'extent' => Config::DEFAULT_MAX_EXTENT,
            //     'group_id' => $this->organizationId,
            //     'user_id' => $this->organizationId,
            //     'is_exist' => 1,
            //     'definitions' => $GLOBALS['Layers']['definitions'][Config::LAYER_TYPE_PERMANENT_GRASSLAND_FOR_MOWING_PRELIMINARY],
            //     'fill_color' => '#00f000',
            //     'border_color' => '#000000',
            //     'feature_label' => 'ELGIDENT',
            //     'transparency' => 70,
            //     'border_width' => 1,
            //     'type' => 'single',
            // ],
            Config::LAYER_TYPE_PERMANENT_GRASSLAND_FOR_MOWING => [
                'name' => 'ПЗП за косене 2024',
                'table_name' => 'layer_permanent_grassland_for_mowing',
                'layer_type' => Config::LAYER_TYPE_PERMANENT_GRASSLAND_FOR_MOWING,
                'extent' => Config::DEFAULT_MAX_EXTENT,
                'group_id' => $this->organizationId,
                'user_id' => $this->organizationId,
                'is_exist' => 1,
                'definitions' => $GLOBALS['Layers']['definitions'][Config::LAYER_TYPE_PERMANENT_GRASSLAND_FOR_MOWING],
                'fill_color' => '#00f000',
                'border_color' => '#000000',
                'feature_label' => 'ELGIDENT',
                'transparency' => 70,
                'border_width' => 1,
                'type' => 'single',
            ],
            Config::LAYER_TYPE_DS_PRC => [
                'name' => 'Кампания 2024',
                'table_name' => 'layer_ds_prc',
                'layer_type' => Config::LAYER_TYPE_DS_PRC,
                'extent' => Config::DEFAULT_MAX_EXTENT,
                'group_id' => $this->organizationId,
                'user_id' => $this->organizationId,
                'is_exist' => 1,
                'definitions' => $GLOBALS['Layers']['definitions'][Config::LAYER_TYPE_DS_PRC],
                'fill_color' => '',
                'border_color' => '#000000',
                'feature_label' => 'crop_name',
                'transparency' => 0,
                'border_width' => 1,
                'type' => 'by attribute',
            ],
            Config::LAYER_TYPE_PERMANETELY_GREEN_AREAS => [
                'name' => 'Постоянно затревени площи - 2024 г.',
                'table_name' => 'layer_pzp',
                'layer_type' => Config::LAYER_TYPE_PERMANETELY_GREEN_AREAS,
                'extent' => Config::DEFAULT_MAX_EXTENT,
                'group_id' => $this->organizationId,
                'user_id' => $this->organizationId,
                'is_exist' => 1,
                'definitions' => $GLOBALS['Layers']['definitions'][Config::LAYER_TYPE_PERMANETELY_GREEN_AREAS],
                'fill_color' => '',
                'border_color' => '#00f000',
                'feature_label' => 'imotcode',
                'transparency' => 0,
                'border_width' => 1,
                'type' => 'single',
            ],
            Config::LAYER_TYPE_PHYSICAL_BLOCKS => [
                'name' => 'Физически блокове 2024 г. - окончателен',
                'table_name' => 'layer_allowable',
                'layer_type' => Config::LAYER_TYPE_PHYSICAL_BLOCKS_CODE,
                'extent' => '',
                'group_id' => $this->organizationId,
                'user_id' => $this->organizationId,
                'is_exist' => 1,
                'definitions' => $GLOBALS['Layers']['definitions'][Config::LAYER_TYPE_PHYSICAL_BLOCKS],
                'fill_color' => '',
                'border_color' => '#ecb710',
                'feature_label' => 'fbident',
                'transparency' => 0,
                'border_width' => 1,
                'type' => 'single',
            ],
            Config::LAYER_TYPE_ALLOWABLE_FINAL => [
                'name' => 'Окончателен допустим - 14.02.2023 г',
                'table_name' => 'layer_allowable',
                'layer_type' => Config::LAYER_TYPE_ALLOWABLE_FINAL_CODE,
                'extent' => '',
                'group_id' => $this->organizationId,
                'user_id' => $this->organizationId,
                'is_exist' => 1,
                'definitions' => $GLOBALS['Layers']['definitions'][Config::LAYER_TYPE_ALLOWABLE_FINAL],
                'fill_color' => '',
                'border_color' => '#0000ff',
                'feature_label' => 'fbident',
                'transparency' => 0,
                'border_width' => 1,
                'type' => 'single',
            ],
        ];

        foreach ($layers as $layerType => $layer) {
            // check if layer already exists
            $stmt = $this->mainConnection->prepare('SELECT id FROM public.su_users_layers WHERE "layer_type" = :layer_type AND group_id = :group_id');
            $stmt->bindValue(':layer_type', $layer['layer_type']);
            $stmt->bindValue(':group_id', $layer['group_id']);
            $stmt->execute();
            $layerID = $stmt->fetchColumn(0);

            if (!$layerID) {
                $layerStmt = $this->mainConnection->prepare('INSERT INTO public.su_users_layers 
                (user_id,
                "name",
                table_name,
                extent,
                layer_type,
                group_id,
                is_exist,
                definitions) VALUES (
                :user_id,
                :name,
                :table_name,
                :extent,
                :layer_type,
                :group_id,
                :is_exist,
                :definitions)');
                $layerStmt->execute([
                    'user_id' => $layer['user_id'],
                    'name' => $layer['name'],
                    'table_name' => $layer['table_name'],
                    'extent' => $layer['extent'],
                    'layer_type' => $layer['layer_type'],
                    'group_id' => $layer['group_id'],
                    'is_exist' => $layer['is_exist'],
                    'definitions' => json_encode($layer['definitions']),
                ]);
                $layerID = $this->mainConnection->lastInsertId();
            }

            // check if this layer already has a style
            $stmt = $pdo->prepare('SELECT id FROM public.su_layer_styles WHERE layer_id = :layer_id');
            $stmt->bindValue(':layer_id', $layerID);
            $stmt->execute();
            $styleID = $stmt->fetchColumn();

            if ($styleID) {
                continue;
            }

            $styleStmt = $pdo->prepare('INSERT INTO su_layer_styles 
            (layer_id,table_name,transparency,fill_color,border_color,border_width,label_size,type,labels,tags) 
            VALUES (:layer_id, :table_name, :transparency, :fill_color, :border_color, :border_width, :label_size, :type, :labels, :tags)');

            $styleStmt->execute([
                'layer_id' => $layerID,
                'table_name' => $layer['table_name'],
                'fill_color' => $layer['fill_color'],
                'border_color' => $layer['border_color'],
                'border_width' => $layer['border_width'],
                'label_size' => 8,
                'type' => $layer['type'],
                'labels' => "{{$layer['feature_label']}}",
                'tags' => 1,
                'transparency' => $layer['transparency'],
            ]);
        }
    }
}
