INSERT INTO su_units_of_measure_categories (name, icon, created_at, updated_at) VALUES
('Area', '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M20 16.2V7.8C21.2 7.4 22 6.3 22 5C22 3.3 20.7 2 19 2C17.7 2 16.6 2.8 16.2 4H7.8C7.4 2.8 6.3 2 5 2C3.3 2 2 3.3 2 5C2 6.3 2.8 7.4 4 7.8V16.2C2.8 16.6 2 17.7 2 19C2 20.7 3.3 22 5 22C6.3 22 7.4 21.2 7.8 20H16.2C16.6 21.2 17.7 22 19 22C20.7 22 22 20.7 22 19C22 17.7 21.2 16.6 20 16.2ZM17.9 4C18.2 3.7 18.6 3.5 19 3.5C19.8 3.5 20.5 4.2 20.5 5C20.5 5.4 20.3 5.8 20 6.1C19.7 6.3 19.4 6.5 19 6.5C18.8 6.5 18.7 6.5 18.5 6.4C18.1 6.2 17.7 5.9 17.6 5.5C17.5 5.3 17.5 5.2 17.5 5C17.5 4.6 17.7 4.3 17.9 4ZM3.5 5C3.5 4.2 4.2 3.5 5 3.5C5.4 3.5 5.8 3.7 6.1 4C6.3 4.3 6.5 4.6 6.5 5C6.5 5.2 6.5 5.3 6.4 5.5C6.3 5.9 5.9 6.3 5.5 6.4C5.3 6.5 5.2 6.5 5 6.5C4.6 6.5 4.3 6.3 4 6.1C3.7 5.8 3.5 5.4 3.5 5ZM6.1 20C5.8 20.3 5.4 20.5 5 20.5C4.2 20.5 3.5 19.8 3.5 19C3.5 18.6 3.7 18.2 4 17.9C4.3 17.7 4.6 17.5 5 17.5C5.2 17.5 5.3 17.5 5.5 17.6C5.9 17.8 6.3 18.1 6.4 18.5C6.5 18.7 6.5 18.8 6.5 19C6.5 19.4 6.3 19.7 6.1 20ZM16.1 18.5H7.9C7.7 17.2 6.7 16.3 5.5 16.1V7.9C6.8 7.7 7.7 6.7 7.9 5.5H16C16.2 6.8 17.2 7.7 18.4 7.9V16C17.2 16.3 16.3 17.2 16.1 18.5ZM19 20.5C18.6 20.5 18.2 20.3 17.9 20C17.7 19.7 17.5 19.4 17.5 19C17.5 18.8 17.5 18.7 17.6 18.5C17.8 18.1 18.1 17.7 18.5 17.6C18.7 17.5 18.8 17.5 19 17.5C19.4 17.5 19.7 17.7 20 17.9C20.3 18.2 20.5 18.6 20.5 19C20.5 19.8 19.8 20.5 19 20.5Z" fill="#1B1D23"/>
</svg>', NOW(), NOW()),
('Weight', '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.1456 10.5L5.96378 18.5H18.036L15.8542 10.5H8.1456ZM7.76371 9C7.31275 9 6.91759 9.30182 6.79894 9.73688L4.3444 18.7369C4.1709 19.373 4.64979 20 5.30916 20H18.6907C19.35 20 19.8289 19.373 19.6554 18.7369L17.2009 9.73688C17.0822 9.30182 16.6871 9 16.2361 9H7.76371Z" fill="#1B1D23"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12 8.5C12.8284 8.5 13.5 7.82843 13.5 7C13.5 6.17157 12.8284 5.5 12 5.5C11.1716 5.5 10.5 6.17157 10.5 7C10.5 7.82843 11.1716 8.5 12 8.5ZM12 10C13.6569 10 15 8.65685 15 7C15 5.34315 13.6569 4 12 4C10.3431 4 9 5.34315 9 7C9 8.65685 10.3431 10 12 10Z" fill="#1B1D23"/>
</svg>', NOW(), NOW()),
('Distance', '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M19 8C18.3 8 17.7 8.3 17.1 8.7L13.7 6.2C13.9 5.8 14 5.4 14 5C14 3.3 12.7 2 11 2C9.3 2 8 3.3 8 5C8 6 8.5 6.8 9.2 7.4L5.5 16C5.4 16 5.2 16 5 16C3.3 16 2 17.3 2 19C2 20.7 3.3 22 5 22C6.7 22 8 20.7 8 19C8 18 7.5 17.2 6.8 16.6L10.5 8C10.6 8 10.8 8 11 8C11.7 8 12.3 7.7 12.9 7.3L16.3 9.8C16.1 10.2 16 10.6 16 11C16 12.7 17.3 14 19 14C20.7 14 22 12.7 22 11C22 9.3 20.7 8 19 8ZM5 20.5C4.2 20.5 3.5 19.8 3.5 19C3.5 18.2 4.2 17.5 5 17.5C5.8 17.5 6.5 18.2 6.5 19C6.5 19.8 5.8 20.5 5 20.5ZM11 6.5C10.2 6.5 9.5 5.8 9.5 5C9.5 4.2 10.2 3.5 11 3.5C11.8 3.5 12.5 4.2 12.5 5C12.5 5.8 11.8 6.5 11 6.5ZM19 12.5C18.2 12.5 17.5 11.8 17.5 11C17.5 10.2 18.2 9.5 19 9.5C19.8 9.5 20.5 10.2 20.5 11C20.5 11.8 19.8 12.5 19 12.5Z" fill="#1B1D23"/>
</svg>', NOW(), NOW()),
('Volume', '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12 3.9C14.4 5.9 18.5 10.2 18.5 14C18.5 18.1 15.2 20.5 12 20.5C8.8 20.5 5.5 18.1 5.5 14C5.5 10.2 9.6 6 12 3.9ZM12 2C12 2 4 8 4 14C4 19 8 22 12 22C16 22 20 19 20 14C20 8 12 2 12 2Z" fill="#1B1D23"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M11.5 12.25C11.9142 12.25 12.25 12.5858 12.25 13V16.75H13.5C13.9142 16.75 14.25 17.0858 14.25 17.5C14.25 17.9142 13.9142 18.25 13.5 18.25H10.75V13C10.75 12.5858 11.0858 12.25 11.5 12.25Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M10.1171 9.81022C9.64049 10.022 9.21658 10.3438 9.03024 10.5301C8.73729 10.823 8.26242 10.8229 7.96957 10.5299C7.67673 10.237 7.67682 9.76211 7.96976 9.46927C8.28342 9.15573 8.85951 8.72764 9.50792 8.43949C10.1256 8.16497 11.0242 7.92343 11.8354 8.32906C12.1318 8.47727 12.418 8.68823 12.6538 8.8621C12.6728 8.87614 12.6915 8.88994 12.7099 8.90344C12.9782 9.10076 13.2123 9.26752 13.4604 9.39156C13.6993 9.51103 13.9334 9.58091 14.1869 9.58598C14.4392 9.59104 14.7572 9.5328 15.1646 9.32909C15.5351 9.14385 15.9856 9.29401 16.1708 9.6645C16.3561 10.035 16.2059 10.4855 15.8354 10.6707C15.2428 10.967 14.6858 11.0963 14.1569 11.0857C13.629 11.0751 13.1756 10.9262 12.7896 10.7332C12.4126 10.5447 12.0842 10.3052 11.8213 10.1119L11.8096 10.1033C11.5306 9.89814 11.3417 9.75926 11.1646 9.6707C10.9758 9.57633 10.6244 9.58478 10.1171 9.81022Z" fill="black"/>
</svg>', NOW(), NOW()),
('Compound', '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M6.5 20.5C7.60457 20.5 8.5 19.6046 8.5 18.5C8.5 17.3954 7.60457 16.5 6.5 16.5C5.39543 16.5 4.5 17.3954 4.5 18.5C4.5 19.6046 5.39543 20.5 6.5 20.5ZM6.5 22C8.433 22 10 20.433 10 18.5C10 16.567 8.433 15 6.5 15C4.567 15 3 16.567 3 18.5C3 20.433 4.567 22 6.5 22Z" fill="#1B1D23"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M17.5 20.5C18.6046 20.5 19.5 19.6046 19.5 18.5C19.5 17.3954 18.6046 16.5 17.5 16.5C16.3954 16.5 15.5 17.3954 15.5 18.5C15.5 19.6046 16.3954 20.5 17.5 20.5ZM17.5 22C19.433 22 21 20.433 21 18.5C21 16.567 19.433 15 17.5 15C15.567 15 14 16.567 14 18.5C14 20.433 15.567 22 17.5 22Z" fill="#1B1D23"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12 10.5C13.933 10.5 15.5 8.933 15.5 7C15.5 5.067 13.933 3.5 12 3.5C10.067 3.5 8.5 5.067 8.5 7C8.5 8.933 10.067 10.5 12 10.5ZM12 12C14.7614 12 17 9.76142 17 7C17 4.23858 14.7614 2 12 2C9.23858 2 7 4.23858 7 7C7 9.76142 9.23858 12 12 12Z" fill="#1B1D23"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M10.682 10.8102L8.18196 16.3102L6.81641 15.6895L9.31641 10.1895L10.682 10.8102Z" fill="black"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M13.318 10.8102L15.818 16.3102L17.1836 15.6895L14.6836 10.1895L13.318 10.8102Z" fill="black"/>
</svg>', NOW(), NOW()),
('Count', '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M13.9995 10.5C15.6995 10.5 17.0995 9.3 17.3995 7.7L19.5858 7.7C19.5858 7.7 19.5859 7.4 19.5859 7C19.5859 6.6 19.5858 6.3 19.5858 6.3L17.3995 6.3C17.0995 4.7 15.6995 3.5 13.9995 3.5C12.2995 3.5 10.8995 4.7 10.5995 6.3L4.7995 6.3C4.7995 6.3 4.7995 6.5 4.7995 7C4.7995 7.5 4.7995 7.9 4.7995 7.9L10.5995 7.9C10.8995 9.3 12.2995 10.5 13.9995 10.5ZM11.9995 7C11.9995 5.9 12.8995 5 13.9995 5C15.0995 5 15.9995 5.9 15.9995 7C15.9995 8.1 15.0995 9 13.9995 9C12.8995 9 11.9995 8.1 11.9995 7Z" fill="#1B1D23"/>
<path d="M9.99922 12C8.29922 12 6.89922 13.2 6.59922 14.8L4.79922 14.8C4.79922 14.8 4.79922 15 4.79922 15.5C4.79922 16 4.79922 16.4 4.79922 16.4L6.59922 16.4C6.89922 18 8.29922 19.2 9.99922 19.2C11.6992 19.2 13.0992 18 13.3992 16.4L20.1992 16.4C20.1992 16.4 20.1992 15.9 20.1992 15.5C20.1992 15.1 20.1992 14.7 20.1992 14.7L13.3992 14.7C13.0992 13.2 11.6992 12 9.99922 12ZM11.9992 15.5C11.9992 16.6 11.0992 17.5 9.99922 17.5C8.89922 17.5 7.99922 16.6 7.99922 15.5C7.99922 14.4 8.89922 13.5 9.99922 13.5C11.0992 13.5 11.9992 14.4 11.9992 15.5Z" fill="#1B1D23"/>
<path d="M4.5 20V3" stroke="#1B1D23" stroke-width="1.5" stroke-linecap="round"/>
<path d="M20 20V3" stroke="#1B1D23" stroke-width="1.5" stroke-linecap="round"/>
</svg>', NOW(), NOW()),
('Time', '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12 3.5C16.7 3.5 20.5 7.3 20.5 12C20.5 16.7 16.7 20.5 12 20.5C7.3 20.5 3.5 16.7 3.5 12C3.5 7.3 7.3 3.5 12 3.5ZM12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2Z" fill="#1B1D23"/>
<path d="M15.3 13.4999H11.3C10.9 13.4999 10.5 13.1999 10.5 12.6999V7.6999C10.5 7.2999 10.8 6.8999 11.3 6.8999C11.8 6.8999 12 7.2999 12 7.7999V11.9999H15.3C15.7 11.9999 16.1 12.2999 16.1 12.7999C16.1 13.2999 15.7 13.4999 15.3 13.4999Z" fill="#1B1D23"/>
</svg>', NOW(), NOW()),
('Temperature', '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M13.0992 14.3V7.1C13.0992 6.7 12.8992 6.5 12.4992 6.5C12.0992 6.5 11.8992 6.8 11.8992 7.1V14.3C10.8992 14.5 10.1992 15.5 10.1992 16.6C10.1992 17.9 11.2992 18.9 12.4992 18.9C13.6992 18.9 14.7992 17.8 14.7992 16.6C14.8992 15.5 14.0992 14.6 13.0992 14.3Z" fill="#1B1D23"/>
<path d="M12.5 3.5C13.4 3.5 14.1 4.2 14.1 5.1V12.9L14.4 13.1C15.7 13.8 16.4 15.1 16.4 16.6C16.4 18.8 14.6 20.6 12.4 20.6C10.2 20.6 8.4 18.8 8.4 16.6C8.4 15.2 9.2 13.8 10.5 13.2L10.9 13V5.1C10.9 4.2 11.6 3.5 12.5 3.5ZM12.5 2C10.8 2 9.4 3.4 9.4 5.1V12.1C7.9 13.1 7 14.8 7 16.5C7 19.5 9.4 22 12.5 22C15.6 22 18 19.6 18 16.5C18 14.7 17.1 13 15.7 12V5C15.6 3.4 14.2 2 12.5 2Z" fill="#1B1D23"/>
</svg>', NOW(), NOW());