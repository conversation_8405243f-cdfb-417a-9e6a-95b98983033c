<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4742_4Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4742-4')
            ->setDescription('Update db function number_of_contracts_by_plot.');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec('CREATE OR REPLACE FUNCTION
            number_of_contracts_by_plot(plot_id INT) RETURNS INT AS 
                $$
                    DECLARE number_of_contracts INT;
                    BEGIN 
                        SELECT
                            count(DISTINCT sc.id) + count(DISTINCT sublease_contracts.id) + count(DISTINCT sales_contracts.id) INTO number_of_contracts
                        FROM
                            layer_kvs AS kvs
                        LEFT JOIN su_contracts_plots_rel AS scpr
                            ON scpr.plot_id = kvs.gid
                        LEFT JOIN su_contracts sc 
                            ON sc.id = scpr.contract_id 
                            AND sc.nm_usage_rights <> 4 -- Skip agreement contracts because they should not appear in FE.
                        LEFT JOIN LATERAL (
                            SELECT 
                                sspcr.sublease_id
                            FROM 
                                su_subleases_plots_contracts_rel sspcr
                            WHERE
                                sspcr.pc_rel_id = scpr.id
                        ) AS sublease_contracts(id) ON TRUE
                        LEFT JOIN LATERAL (
                            SELECT 
                                sscpr.contract_id
                            FROM 
                                su_sales_contracts_plots_rel sscpr
                            WHERE
                                sscpr.pc_rel_id = scpr.id
                        ) AS sales_contracts(id) ON TRUE
                        WHERE
                            kvs.gid = number_of_contracts_by_plot.plot_id
                        GROUP BY
                            kvs.gid;
                        
                        RETURN number_of_contracts;
                    END;
            $$ LANGUAGE plpgsql;
        ');
    }
}
