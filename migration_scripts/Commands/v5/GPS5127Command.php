<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS5127Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-5127')
            ->setDescription('Create get_contracts_containing_plot_for_period function to return contract IDs that contain a plot for a given period');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec("CREATE OR REPLACE FUNCTION get_contracts_containing_plot_for_period(
                p_plot_id INT,
                period_start DATE,
                period_end DATE
            )
            RETURNS INT[] AS $$
            BEGIN
                RETURN (
                    WITH all_contracts AS (
                        -- Get main contracts that intersect with the period
                        SELECT DISTINCT
                            c.id as contract_id,
                            c.start_date,
                            COALESCE(c.due_date, '9999-12-31'::date) as due_date,
                            c.nm_usage_rights
                        FROM su_contracts c
                        INNER JOIN su_contracts_plots_rel cpr ON c.id = cpr.contract_id
                        WHERE cpr.plot_id = p_plot_id
                            AND c.active = true
                            AND c.is_sublease = false
                            AND (c.parent_id IS NULL OR c.parent_id = 0)
                            AND c.start_date <= period_end
                            AND COALESCE(c.due_date, '9999-12-31'::date) >= period_start
                        UNION ALL
                        -- Get annexes that intersect with the period
                        SELECT DISTINCT
                            a.id as contract_id,
                            a.start_date,
                            COALESCE(a.due_date, '9999-12-31'::date) as due_date,
                            a.nm_usage_rights
                        FROM su_contracts a
                        INNER JOIN su_contracts_plots_rel cpr ON a.id = cpr.contract_id AND cpr.annex_action = 'added'
                        WHERE cpr.plot_id = p_plot_id
                            AND a.active = true
                            AND a.parent_id IS NOT NULL
                            AND a.parent_id != 0
                            AND a.is_annex = true
                            AND a.start_date <= period_end
                            AND COALESCE(a.due_date, '9999-12-31'::date) >= period_start
                    ),
                    ownership_full_coverage AS (
                        -- Check for ownership contracts that fully cover the entire period
                        SELECT array_agg(DISTINCT contract_id) as ownership_ids
                        FROM all_contracts
                        WHERE nm_usage_rights = 1  -- Ownership contracts
                            AND start_date <= period_start
                            AND due_date >= period_end
                    ),
                    ordered_periods AS (
                        -- Order all contracts by start date for gap analysis
                        SELECT
                            contract_id,
                            start_date,
                            due_date,
                            nm_usage_rights,
                            LAG(due_date) OVER (ORDER BY start_date, due_date DESC) as prev_end
                        FROM all_contracts
                    ),
                    coverage_check AS (
                        SELECT
                            MIN(start_date) as coverage_start,
                            MAX(due_date) as coverage_end,
                            COUNT(*) FILTER (WHERE start_date > prev_end + INTERVAL '1 day') as gap_count,
                            array_agg(DISTINCT contract_id) as all_contract_ids
                        FROM ordered_periods
                    )
                    SELECT
                        CASE
                            -- Priority 1: If we have ownership contracts that fully cover the period, return only those
                            WHEN (SELECT ownership_ids FROM ownership_full_coverage) IS NOT NULL
                                 AND array_length((SELECT ownership_ids FROM ownership_full_coverage), 1) > 0
                            THEN (SELECT ownership_ids FROM ownership_full_coverage)
                            -- Priority 2: Check if all contracts together provide full coverage without gaps
                            WHEN (SELECT gap_count FROM coverage_check) = 0
                                 AND period_start >= (SELECT coverage_start FROM coverage_check)
                                 AND period_end <= (SELECT coverage_end FROM coverage_check)
                            THEN (SELECT all_contract_ids FROM coverage_check)
                            -- No valid coverage found
                            ELSE ARRAY[]::INT[]
                        END
                );
            END;
            $$ LANGUAGE plpgsql;
        ");
    }
}
