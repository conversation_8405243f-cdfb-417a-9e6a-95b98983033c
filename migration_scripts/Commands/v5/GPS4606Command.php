<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4606Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4606')
            ->setDescription('Create indexes for table su_contracts');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $this->executeSql($pdo, __DIR__ . '/sql/GPS4606.sql');

        if ('00000' == $pdo->errorCode()) {
            $output->writeln('Styles updated successfully.');
        } else {
            $output->writeln('Error: ' . $pdo->errorInfo()[2]);
        }
    }
}
