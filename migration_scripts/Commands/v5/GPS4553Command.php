<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4553Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4553')
            ->setDescription('Update styles with 100% transparency and border width 0');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $this->executeSql($pdo, __DIR__ . '/sql/GPS4553.sql');

        if ('00000' == $pdo->errorCode()) {
            $output->writeln('Styles updated successfully.');
        } else {
            $output->writeln('Error: ' . $pdo->errorInfo()[2]);
        }
    }
}
