<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS4808_1Command run on main database.
 *
 * This command will add new status 'Annexed' to contract_status_enum.
 */
class GPS4808_1Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4808_1')
            ->setDescription("Add new status 'Annexed' to contract_status_enum.");
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec("ALTER TYPE contract_status_enum ADD VALUE 'Annexed'");
    }
}
