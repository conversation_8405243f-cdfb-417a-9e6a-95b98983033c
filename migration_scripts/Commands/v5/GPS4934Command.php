<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4934Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4934')
            ->setDescription('Prepend schema name before mat. view name in refresh_rentas_materialized_views()');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $sql = <<<SQL
        CREATE OR REPLACE FUNCTION public.refresh_rentas_materialized_views()
            RETURNS trigger
            LANGUAGE plpgsql
            AS $$
            BEGIN
                REFRESH MATERIALIZED VIEW public.charged_rentas_mat_view;
                REFRESH MATERIALIZED VIEW public.charged_rentas_annexes_mat_view;
                REFRESH MATERIALIZED VIEW public.renta_nats_mat_view;
                REFRESH MATERIALIZED VIEW public.renta_nats_annexes_mat_view;
                RETURN NULL;
            END;
            $$;
        SQL;

        $stmt = $pdo->prepare($sql);
        $stmt->execute();
    }
}
