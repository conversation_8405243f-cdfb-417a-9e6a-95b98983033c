<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4742_5Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4742-5')
            ->setDescription('Add indexes for su_subleases_plots_contracts_rel and su_sales_contracts_plots_rel tables.');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec('CREATE INDEX IF NOT EXISTS idx_su_subleases_pc_rel_id ON su_subleases_plots_contracts_rel(pc_rel_id);');
        $pdo->exec('CREATE INDEX IF NOT EXISTS idx_su_sales_pc_rel_id ON su_sales_contracts_plots_rel(pc_rel_id);');
    }
}
