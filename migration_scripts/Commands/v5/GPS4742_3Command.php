<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4742_3Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4742-3')
            ->setDescription('Update unit column in su_renta_types table and relate it to su_units_of_measure table');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec("UPDATE su_renta_types
            SET unit = (
                CASE
                    WHEN unit = 1 THEN (SELECT id FROM su_units_of_measure WHERE full_name = 'Kilogram')
                    WHEN unit = 2 THEN (SELECT id FROM su_units_of_measure WHERE full_name = 'Litre')
                    WHEN unit = 3 THEN (SELECT id FROM su_units_of_measure WHERE full_name = 'Piece')
                END
            );
        ");

        $pdo->exec('ALTER TABLE su_renta_types
            ADD CONSTRAINT fk_su_renta_types_unit
            FOREIGN KEY (unit)
            REFERENCES su_units_of_measure(id)
            ON DELETE SET NULL
            ON UPDATE CASCADE;
        ');
    }
}
