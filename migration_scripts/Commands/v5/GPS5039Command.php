<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\MainDbCommand;

class GPS5039Command extends MainDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec('ALTER TABLE public.su_users_farming ADD COLUMN company_ekatte VARCHAR(5) DEFAULT NULL;');
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-5039')
            ->setDescription('Add a new column company_ekatte to the farming table');
    }
}
