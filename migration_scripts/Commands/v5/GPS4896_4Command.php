<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4896_4Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4896-4')
            ->setDescription('Create db function get_document_status_by_dates');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec('DROP FUNCTION IF EXISTS get_document_status_by_dates(start_date DATE, end_date DATE);');

        $pdo->exec("CREATE FUNCTION get_document_status_by_dates(start_date DATE, end_date DATE)
            RETURNS contract_status_enum AS
            $$
                DECLARE
                    status contract_status_enum;

                BEGIN
                    IF start_date <= now() AND end_date >= now() THEN
                        status := 'Active'::contract_status_enum;
                    ELSIF start_date > now() THEN
                        status := 'Upcoming'::contract_status_enum;
                    ELSIF end_date < now() THEN
                        status := 'Expired'::contract_status_enum;
                    ELSE
                        status := null;
                    END IF;

                    RETURN status;
                END;
            $$
            LANGUAGE plpgsql;
        ");
    }
}
