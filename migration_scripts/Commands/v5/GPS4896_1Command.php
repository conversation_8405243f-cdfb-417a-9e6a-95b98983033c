<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4896_1Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4896-1')
            ->setDescription('Drop db functions number_of_contracts_by_user_and_plot');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec('DROP FUNCTION IF EXISTS number_of_contracts_by_user_and_plot(keycloak_uid TEXT, plot_id INT);');
        $pdo->exec('DROP FUNCTION IF EXISTS number_of_contracts_by_user_and_plot(keycloak_uid UUID, plot_id INT);');
    }
}
