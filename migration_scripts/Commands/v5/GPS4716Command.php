<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4716<PERSON>ommand extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4716')
            ->setDescription('Add primary keys in su_plots_owners_rel and su_charged_renta if not exist');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $output->writeln('ALTER TABLE public.su_plots_owners_rel ADD CONSTRAINT su_plots_owners_rel_id_seq_pkey PRIMARY KEY (id);');
        $pdo->exec("
            DO $$
                BEGIN
                    -- Check if the primary key constraint already exists
                    IF NOT EXISTS (
                        SELECT 1
                        FROM pg_constraint con
                        INNER JOIN pg_class rel ON rel.oid = con.conrelid
                        INNER JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
                        WHERE nsp.nspname = 'public'
                        AND rel.relname = 'su_plots_owners_rel'
                        AND con.contype = 'p' -- 'p' stands for primary key constraint
                    ) THEN
                        -- Add the primary key constraint if it doesn't exist
                        ALTER TABLE public.su_plots_owners_rel 
                        ADD CONSTRAINT su_plots_owners_rel_id_seq_pkey 
                        PRIMARY KEY (id);
                    END IF;
            END $$;");

        $output->writeln('ALTER TABLE public.su_charged_renta ADD CONSTRAINT su_charged_renta_id_seq_pkey PRIMARY KEY (id);');
        $pdo->exec("
            DO $$
                BEGIN
                    -- Check if the primary key constraint already exists
                    IF NOT EXISTS (
                        SELECT 1
                        FROM pg_constraint con
                        INNER JOIN pg_class rel ON rel.oid = con.conrelid
                        INNER JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
                        WHERE nsp.nspname = 'public'
                        AND rel.relname = 'su_charged_renta'
                        AND con.contype = 'p' -- 'p' stands for primary key constraint
                    ) THEN
                        -- Add the primary key constraint if it doesn't exist
                        ALTER TABLE public.su_charged_renta 
                        ADD CONSTRAINT su_charged_renta_id_seq_pkey 
                        PRIMARY KEY (id);
                    END IF;
            END $$;");

        $output->writeln('CREATE INDEX layer_kvs_kad_ident_idx ON public.layer_kvs (kad_ident);');
        $pdo->exec("
            DO $$ 
                BEGIN
                    IF NOT EXISTS (
                        SELECT 1 FROM pg_indexes 
                        WHERE schemaname = 'public' 
                        AND tablename = 'layer_kvs' 
                        AND indexname = 'layer_kvs_kad_ident_idx'
                    ) THEN
                        CREATE INDEX layer_kvs_kad_ident_idx ON public.layer_kvs (kad_ident);
                    END IF;
                END $$;
        ");

        $output->writeln('CREATE INDEX layer_kvs_ekate_idx ON public.layer_kvs (ekate);');
        $pdo->exec("
            DO $$ 
                BEGIN
                    IF NOT EXISTS (
                        SELECT 1 FROM pg_indexes 
                        WHERE schemaname = 'public' 
                        AND tablename = 'layer_kvs' 
                        AND indexname = 'layer_kvs_ekate_idx'
                    ) THEN
                        CREATE INDEX layer_kvs_ekate_idx ON public.layer_kvs (ekate);
                    END IF;
                END $$;
        ");

        if ('00000' == $pdo->errorCode()) {
            $output->writeln('Primary key added successfully.');
        } else {
            $output->writeln('Error: ' . $pdo->errorInfo()[2]);
        }
    }
}
