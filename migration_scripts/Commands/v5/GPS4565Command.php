<?php

namespace TF\Commands\v5;

use Exception;
use PDO;
use PDOException;
use TF\Application\Common\Config;
use TF\Application\Entity\LayerStyles;
use TF\Application\Entity\UserLayers;
use TF\Commands\Common\UserDbCommand;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

class GPS4565Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4565')
            ->setDescription('Update layer kvs virtual columns without "Generation expression"');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        try {
            $this->initAuthUser($this->organizationId);
        } catch (Exception $e) {
            throw new PDOException($e->getMessage());
        }

        $definitions = UserLayers::getDefinitionsByType(Config::LAYER_TYPE_KVS);
        $virtualDefinitions = UserLayers::filterDefinitions($definitions, [['col_virtual' => true]]);
        $virtualColumns = array_column($virtualDefinitions, 'col_name');
        $virtualDefinitions = array_combine($virtualColumns, $virtualDefinitions);
        $virtualColumnsForUpdate = $this->getVirtualColumnsWithoutGenerationExpression($pdo, $virtualColumns);

        if (!count($virtualColumnsForUpdate)) {
            $output->writeln('There is nothing to update.');

            return;
        }

        $output->write('Start processing...' . PHP_EOL);
        /** @var UserLayers[] $csdUserLayers */
        $csdUserLayers = UserLayers::finder()->findAll('group_id = (:groupId) and layer_type = :csdLayerType', [':groupId' => $this->organizationId, ':csdLayerType' => Config::LAYER_TYPE_CSD]);
        $isNeedToRegenerateCsdViews = false;
        $oldVirtualColumnsForDelete = [];

        foreach ($virtualColumnsForUpdate as $columnName) {
            $oldColumName = "{$columnName}_old";
            $definition = $virtualDefinitions[$columnName];
            $expression = $definition['col_expression'];
            $virtualColumnsUsedInView = ['virtual_category_title', 'virtual_ntp_title'];

            if (count($csdUserLayers) && in_array($columnName, $virtualColumnsUsedInView)) {
                // If the column is used in view, we need to rename the column "..._old" and create the new virtual column with the correct generation expression.
                // Then we need to regenerate the views and delete the old column.
                $isNeedToRegenerateCsdViews = true;
                $oldVirtualColumnsForDelete[] = $oldColumName;

                $output->write("Rename column {$columnName} to  {$oldColumName}" . PHP_EOL);
                $pdo->exec("ALTER TABLE public.layer_kvs RENAME COLUMN {$columnName} TO {$oldColumName};");
                $output->write("Create column {$columnName} with generation expression" . PHP_EOL);
                $pdo->exec("ALTER TABLE public.layer_kvs ADD {$columnName} varchar GENERATED ALWAYS AS ({$expression}) STORED NULL;");
            } else {
                // If the column is not used in any view, we can delete it directly and create the new virtual column.
                $output->write("Delete column {$columnName}" . PHP_EOL);
                $pdo->exec("ALTER TABLE public.layer_kvs DROP COLUMN {$columnName};");
                $output->write("Create column {$columnName} with generation expression" . PHP_EOL);
                $pdo->exec("ALTER TABLE public.layer_kvs ADD {$columnName} varchar GENERATED ALWAYS AS ({$expression}) STORED NULL;");
            }
        }

        // Before updating the views, we need to commit the transaction
        $this->commitTransaction($pdo);

        try {
            if ($isNeedToRegenerateCsdViews) {
                $this->updateViews($csdUserLayers, $output);
            }
        } catch (Exception $e) {
            $output->write('Update views fail' . PHP_EOL);
            if (count($oldVirtualColumnsForDelete)) {
                // If updating views failed, we need to undo the changes
                $this->revertColumnChanges($pdo, $oldVirtualColumnsForDelete, $output);
                // Commit the transaction because we need to revert columns changes
                $this->commitTransaction($pdo);
            }

            throw new PDOException($e->getMessage());
        }

        if (count($oldVirtualColumnsForDelete)) {
            $this->deleteOldColumns($pdo, $oldVirtualColumnsForDelete, $output);
        }
    }

    private function getVirtualColumnsWithoutGenerationExpression(PDO $pdo, $columns)
    {
        $columns = "'" . implode("','", $columns) . "'";
        $cmd = $pdo->prepare("SELECT
            column_name
        FROM
            information_schema.columns
        WHERE
            table_schema = 'public'
            AND table_name = 'layer_kvs'
            AND column_name IN ({$columns})
            AND generation_expression is null;");

        $cmd->execute();

        return $cmd->fetchAll(PDO::FETCH_COLUMN);
    }

    private function updateViews(array $csdUserLayers, $output)
    {
        $UserDbController = new UserDbController($this->userDbName);
        foreach ($csdUserLayers as $layer) {
            if (!$layer->is_exist) {
                // Skip layers
                continue;
            }

            $tableName = $layer->table_name;
            $output->write("Update colors for CSD view in table '{$tableName}'" . PHP_EOL);

            [$style] = $layer->getStyles();
            $labelSql = $style->generateMapLabelSQL($layer);

            if (LayerStyles::SINGLE_COLORING_TYPE === $style->type) {
                $coloringOptions = [
                    'fill_color' => $style->fill_color,
                    'border_color' => $style->border_color,
                    'label' => $labelSql,
                ];
            } else {
                $csdColumnDefinitions = $layer->getDefinitions();
                [$fillColumnDefinition] = UserLayers::filterDefinitions($csdColumnDefinitions, [['col_name' => $style->fill_column_name]]);
                [$borderColumnDefinition] = UserLayers::filterDefinitions($csdColumnDefinitions, [['col_name' => $style->border_column_name]]);

                $coloringOptions = [
                    'fill_column_definition' => $fillColumnDefinition,
                    'border_column_definition' => $borderColumnDefinition,
                    'label' => $labelSql,
                ];
            }

            $modifiedString = str_replace('layer_decl_69_70_', '', $tableName);
            [$ekatte, $farmYear] = explode('_', $modifiedString);

            $UserDbController->createCsdMatView($layer->table_name, $ekatte, $farmYear, $coloringOptions, LayerStyles::BY_ATTRIBUTE_COLORING_TYPE);

            $output->writeln('Update is completed');
        }
    }

    private function deleteOldColumns(PDO $pdo, array $oldVirtualColumns, $output)
    {
        $output->write('Delete old columns' . PHP_EOL);
        $virtualColumnsForUpdate = $this->getVirtualColumnsWithoutGenerationExpression($pdo, $oldVirtualColumns);

        foreach ($virtualColumnsForUpdate as $column) {
            $columnName = $column['column_name'];
            $pdo->exec("ALTER TABLE public.layer_kvs DROP COLUMN {$columnName};");
        }
    }

    private function revertColumnChanges(PDO $pdo, array $oldVirtualColumns, $output)
    {
        $output->write('Revert columns changes' . PHP_EOL);
        $virtualColumnsForUpdate = $this->getVirtualColumnsWithoutGenerationExpression($pdo, $oldVirtualColumns);

        foreach ($virtualColumnsForUpdate as $oldColumnName) {
            $columnName = str_replace('_old', '', $oldColumnName);
            $pdo->exec("ALTER TABLE public.layer_kvs DROP COLUMN {$columnName};");
            $pdo->exec("ALTER TABLE public.layer_kvs RENAME COLUMN {$oldColumnName} TO {$columnName};");
        }
    }

    private function commitTransaction(PDO $pdo)
    {
        $pdo->commit();
        // Start a new transaction for the onDbExecute method, as we need to commit the transaction at the end
        $pdo->beginTransaction();
    }
}
