<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS4808Command run on main database.
 *
 * This command will update function 'get_contract_status' to use new status 'Annexed' in su_contracts table
 */
class GPS4808_2Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4808_2')
            ->setDescription("Update function 'get_contract_status' to use new status 'Annexed' in su_contracts table.");
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec('DROP FUNCTION IF EXISTS get_contract_status;');

        // Update function for getting contract status to use new status 'Annexed' and support checking annexes
        $pdo->exec("CREATE OR REPLACE FUNCTION get_contract_status(
                contract_id int,
                active boolean,
                due_date timestamp without time zone,
                check_annexes boolean DEFAULT true
            ) 
            RETURNS contract_status_enum 
            IMMUTABLE 
            AS $$       
            DECLARE
                contract_status contract_status_enum;
                annexed_contract_count int := 0;
            BEGIN
                IF check_annexes THEN
                    SELECT COUNT(*) INTO annexed_contract_count
                    FROM su_contracts AS c
                    WHERE c.parent_id = contract_id AND c.active = TRUE;
                END IF;

                contract_status := CASE 
                    WHEN check_annexes AND annexed_contract_count > 0 THEN 'Annexed'::contract_status_enum
                    WHEN active = FALSE THEN 'Canceled'::contract_status_enum
                    WHEN active = TRUE AND (COALESCE(due_date, '9999-01-01')::date >= now()::date) THEN 'Active'::contract_status_enum
                    ELSE 'Expired'::contract_status_enum
                END;

                RETURN contract_status;
            END;
            $$ LANGUAGE plpgsql;
        ");
    }
}
