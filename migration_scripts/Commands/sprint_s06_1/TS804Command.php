<?php

namespace TF\Commands\sprint_s06_1;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-804 command run on all databases.
 */
class TS804Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s06_1:TS-804')
            ->setDescription('Sazdava tablicite za podmodul "Dogovori za prodajba".');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-804.sql', $return);
    }
}
