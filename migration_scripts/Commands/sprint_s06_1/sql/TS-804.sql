--
-- PostgreSQL database dump
--

SET statement_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SET check_function_bodies = false;
SET client_min_messages = warning;

SET search_path = public, pg_catalog;

SET default_tablespace = '';

SET default_with_oids = false;

--
-- Name: su_sales_contracts; Type: TABLE; Schema: public; Owner: postgres; Tablespace: 
-- su_sales_contracts

CREATE TABLE su_sales_contracts (
    id serial,
    c_num varchar(255),
    c_date timestamp(6),
    sv_num varchar(255),
    sv_date timestamp(6),
    start_date timestamp(6),
    renta float4,
    due_date timestamp(6),
    renta_nat float4,
    farming_id int4,
    comment text,
    agg_type int4,
    active bool DEFAULT true,
    parent_id int4 DEFAULT 0 NOT NULL,
    is_annex bool DEFAULT false NOT NULL,
    renta_nat_type_id int4 DEFAULT 0 NOT NULL,
    is_sublease bool DEFAULT false NOT NULL,
    original_due_date timestamp(6),
    original_renta int4,
    original_renta_nat int4,
    original_renta_nat_type_id int4,
    na_num varchar(255),
    tom varchar(255),
    delo varchar(255),
    court varchar(255),
    payday varchar(255)
);

ALTER TABLE su_sales_contracts OWNER TO postgres;

ALTER TABLE su_sales_contracts_id_seq OWNER TO postgres;

ALTER SEQUENCE su_sales_contracts_id_seq OWNED BY su_sales_contracts.id;

ALTER TABLE ONLY su_sales_contracts ADD CONSTRAINT su_sales_contracts_id_seq_pkey PRIMARY KEY (id);


CREATE TABLE su_sales_contracts_plots_rel (
    id serial,
    sales_contract_id int4,
    contract_id int4,
    sublease_contract_id int4,
    plot_id int4,
    contract_area float4,
    contract_area_for_sale float4,
    price_per_acre float8,
    price_sum float8
);

CREATE SEQUENCE su_sales_contracts_plots_rel_seq OWNED BY su_sales_contracts_plots_rel.id;

ALTER TABLE su_sales_contracts_plots_rel ADD UNIQUE (sales_contract_id, plot_id);

ALTER TABLE su_sales_contracts_plots_rel ADD FOREIGN KEY (sales_contract_id) REFERENCES su_sales_contracts (id) ON DELETE CASCADE ON UPDATE NO ACTION;    
ALTER TABLE su_sales_contracts_plots_rel ADD FOREIGN KEY (plot_id) REFERENCES layer_kvs (gid) ON DELETE CASCADE ON UPDATE NO ACTION;     
ALTER TABLE su_sales_contracts_plots_rel ADD FOREIGN KEY (contract_id) REFERENCES su_contracts (id) ON DELETE CASCADE ON UPDATE NO ACTION;    
ALTER TABLE su_sales_contracts_plots_rel ADD FOREIGN KEY (sublease_contract_id) REFERENCES su_contracts (id) ON DELETE CASCADE ON UPDATE NO ACTION;    

CREATE TABLE su_sales_contracts_files (
    id serial,
    filename varchar(255),
    date timestamp(6) DEFAULT now(),
    user_id int4,
    group_id int4,
    sales_contract_id int4
);

ALTER TABLE su_sales_contracts_files OWNER TO postgres;

ALTER TABLE su_sales_contracts_files_id_seq OWNER TO postgres;

ALTER SEQUENCE su_sales_contracts_files_id_seq OWNED BY su_sales_contracts_files.id;

ALTER TABLE ONLY su_sales_contracts_files ADD CONSTRAINT su_sales_contracts_files_id_seq_pkey PRIMARY KEY (id);

ALTER TABLE su_sales_contracts_files ADD FOREIGN KEY (sales_contract_id) REFERENCES su_sales_contracts (id) ON DELETE CASCADE ON UPDATE NO ACTION;


CREATE TABLE su_buyers (
id serial,
name varchar(255),
contacts varchar(255)
);

ALTER TABLE su_buyers OWNER TO postgres;

ALTER TABLE su_buyers_id_seq OWNER TO postgres;

ALTER SEQUENCE su_buyers_id_seq OWNED BY su_buyers.id;

ALTER TABLE ONLY su_buyers ADD CONSTRAINT su_buyers_id_seq_pkey PRIMARY KEY (id);    

CREATE TABLE su_sales_contracts_buyers_rel (
    id serial,
    sales_contract_id int4,
    buyer_id int4
);

CREATE SEQUENCE su_sales_contracts_buyers_rel_seq OWNED BY su_sales_contracts_buyers_rel.id;

ALTER TABLE su_sales_contracts_buyers_rel ADD UNIQUE (sales_contract_id, buyer_id);

ALTER TABLE su_sales_contracts_buyers_rel ADD FOREIGN KEY (sales_contract_id) REFERENCES su_sales_contracts (id) ON DELETE CASCADE ON UPDATE NO ACTION;    
ALTER TABLE su_sales_contracts_buyers_rel ADD FOREIGN KEY (buyer_id) REFERENCES su_buyers (id) ON DELETE CASCADE ON UPDATE NO ACTION;     