ALTER TABLE su_hypothecs RENAME TO su_hypothecs_old;

ALTER SEQUENCE su_hypothecs_id_seq OWNED BY su_hypothecs_old.id;
ALTER SEQUENCE su_hypothecs_id_seq RENAME TO su_hypothecs_old_id_seq;

ALTER INDEX su_hypothecs_pkey RENAME to su_hypothecs_old_pkey;



--
-- PostgreSQL database dump
--

SET statement_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SET check_function_bodies = false;
SET client_min_messages = warning;

SET search_path = public, pg_catalog;

SET default_tablespace = '';

SET default_with_oids = false;

--
-- Name: su_hypothecs_creditors; Type: TABLE; Schema: public; Owner: postgres; Tablespace: 
--

CREATE TABLE su_hypothecs_creditors (
    id integer NOT NULL,
    name character varying(64)
);


ALTER TABLE public.su_hypothecs_creditors OWNER TO postgres;

--
-- Name: su_hypothecs_creditors_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE su_hypothecs_creditors_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.su_hypothecs_creditors_id_seq OWNER TO postgres;

--
-- Name: su_hypothecs_creditors_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE su_hypothecs_creditors_id_seq OWNED BY su_hypothecs_creditors.id;


--
-- Name: id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY su_hypothecs_creditors ALTER COLUMN id SET DEFAULT nextval('su_hypothecs_creditors_id_seq'::regclass);


--
-- Name: su_creditors_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres; Tablespace: 
--

ALTER TABLE ONLY su_hypothecs_creditors
    ADD CONSTRAINT su_creditors_pkey PRIMARY KEY (id);


--
-- PostgreSQL database dump complete
--






--
-- PostgreSQL database dump
--

SET statement_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SET check_function_bodies = false;
SET client_min_messages = warning;

SET search_path = public, pg_catalog;

SET default_tablespace = '';

SET default_with_oids = false;

--
-- Name: su_hypothecs; Type: TABLE; Schema: public; Owner: postgres; Tablespace: 
--

CREATE TABLE su_hypothecs (
    id integer NOT NULL,
    num character varying(255) NOT NULL,
    date date NOT NULL,
    due_date date NOT NULL,
    comment text,
    creditor_id integer NOT NULL,
    start_date date NOT NULL,
    farming_id integer NOT NULL,
    tom character varying(255),
    na_num character varying(255),
    delo character varying(255),
    court character varying(255),
    is_active boolean DEFAULT true NOT NULL,
    deactivate_num character varying(255),
    deactivate_date date
);


ALTER TABLE public.su_hypothecs OWNER TO postgres;

--
-- Name: su_hypothecs_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE su_hypothecs_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.su_hypothecs_id_seq OWNER TO postgres;

--
-- Name: su_hypothecs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE su_hypothecs_id_seq OWNED BY su_hypothecs.id;


--
-- Name: id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY su_hypothecs ALTER COLUMN id SET DEFAULT nextval('su_hypothecs_id_seq'::regclass);


--
-- Name: su_hypothecs_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres; Tablespace: 
--

ALTER TABLE ONLY su_hypothecs
    ADD CONSTRAINT su_hypothecs_pkey PRIMARY KEY (id);


--
-- Name: creditor_rel; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY su_hypothecs
    ADD CONSTRAINT creditor_rel FOREIGN KEY (creditor_id) REFERENCES su_hypothecs_creditors(id) ON DELETE CASCADE;


--
-- PostgreSQL database dump complete
--



--
-- PostgreSQL database dump
--

SET statement_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SET check_function_bodies = false;
SET client_min_messages = warning;

SET search_path = public, pg_catalog;

SET default_tablespace = '';

SET default_with_oids = false;

--
-- Name: su_hypothecs_files; Type: TABLE; Schema: public; Owner: postgres; Tablespace: 
--

CREATE TABLE su_hypothecs_files (
    id integer NOT NULL,
    filename character varying(255),
    date timestamp without time zone DEFAULT now(),
    user_id integer,
    group_id integer,
    hypothec_id integer
);


ALTER TABLE public.su_hypothecs_files OWNER TO postgres;

--
-- Name: su_hypothecs_files_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE su_hypothecs_files_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.su_hypothecs_files_id_seq OWNER TO postgres;

--
-- Name: su_hypothecs_files_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE su_hypothecs_files_id_seq OWNED BY su_hypothecs_files.id;


--
-- Name: id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY su_hypothecs_files ALTER COLUMN id SET DEFAULT nextval('su_hypothecs_files_id_seq'::regclass);


--
-- Name: su_hypothecs_files_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres; Tablespace: 
--

ALTER TABLE ONLY su_hypothecs_files
    ADD CONSTRAINT su_hypothecs_files_pkey PRIMARY KEY (id);


--
-- Name: su_hypothecs_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY su_hypothecs_files
    ADD CONSTRAINT su_hypothecs_fkey FOREIGN KEY (hypothec_id) REFERENCES su_hypothecs(id) ON DELETE CASCADE;


--
-- PostgreSQL database dump complete
--



--
-- PostgreSQL database dump
--

SET statement_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SET check_function_bodies = false;
SET client_min_messages = warning;

SET search_path = public, pg_catalog;

SET default_tablespace = '';

SET default_with_oids = false;

--
-- Name: su_hypothecs_payments; Type: TABLE; Schema: public; Owner: postgres; Tablespace: 
--

CREATE TABLE su_hypothecs_payments (
    id integer NOT NULL,
    hypothec_id integer NOT NULL,
    amount double precision NOT NULL,
    date date DEFAULT now() NOT NULL,
    comment character varying(255)
);


ALTER TABLE public.su_hypothecs_payments OWNER TO postgres;

--
-- Name: su_hypothecs_payments_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE su_hypothecs_payments_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.su_hypothecs_payments_id_seq OWNER TO postgres;

--
-- Name: su_hypothecs_payments_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE su_hypothecs_payments_id_seq OWNED BY su_hypothecs_payments.id;


--
-- Name: id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY su_hypothecs_payments ALTER COLUMN id SET DEFAULT nextval('su_hypothecs_payments_id_seq'::regclass);


--
-- Name: su_hypothecs_payments_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres; Tablespace: 
--

ALTER TABLE ONLY su_hypothecs_payments
    ADD CONSTRAINT su_hypothecs_payments_pkey PRIMARY KEY (id);


--
-- Name: su_hypothecs_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY su_hypothecs_payments
    ADD CONSTRAINT su_hypothecs_fkey FOREIGN KEY (hypothec_id) REFERENCES su_hypothecs(id) ON DELETE CASCADE;


--
-- PostgreSQL database dump complete
--




--
-- PostgreSQL database dump
--

SET statement_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SET check_function_bodies = false;
SET client_min_messages = warning;

SET search_path = public, pg_catalog;

SET default_tablespace = '';

SET default_with_oids = false;

--
-- Name: su_hypothecs_plots_rel; Type: TABLE; Schema: public; Owner: postgres; Tablespace: 
--

CREATE TABLE su_hypothecs_plots_rel (
    id integer NOT NULL,
    hypothec_id integer NOT NULL,
    plot_id integer NOT NULL,
    hypothec_area double precision NOT NULL
);


ALTER TABLE public.su_hypothecs_plots_rel OWNER TO postgres;

--
-- Name: su_hypothecs_plots_rel_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE su_hypothecs_plots_rel_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.su_hypothecs_plots_rel_id_seq OWNER TO postgres;

--
-- Name: su_hypothecs_plots_rel_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE su_hypothecs_plots_rel_id_seq OWNED BY su_hypothecs_plots_rel.id;


--
-- Name: id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY su_hypothecs_plots_rel ALTER COLUMN id SET DEFAULT nextval('su_hypothecs_plots_rel_id_seq'::regclass);


--
-- Name: su_hypothecs_plots_rel_hypothec_id_plot_id_key; Type: CONSTRAINT; Schema: public; Owner: postgres; Tablespace: 
--

ALTER TABLE ONLY su_hypothecs_plots_rel
    ADD CONSTRAINT su_hypothecs_plots_rel_hypothec_id_plot_id_key UNIQUE (hypothec_id, plot_id);


--
-- Name: su_hypothecs_plots_rel_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres; Tablespace: 
--

ALTER TABLE ONLY su_hypothecs_plots_rel
    ADD CONSTRAINT su_hypothecs_plots_rel_pkey PRIMARY KEY (id);


--
-- Name: hypothecs_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY su_hypothecs_plots_rel
    ADD CONSTRAINT hypothecs_fkey FOREIGN KEY (hypothec_id) REFERENCES su_hypothecs(id) ON DELETE CASCADE;


--
-- Name: layer_kvs_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY su_hypothecs_plots_rel
    ADD CONSTRAINT layer_kvs_fkey FOREIGN KEY (plot_id) REFERENCES layer_kvs(gid) ON DELETE CASCADE;


--
-- PostgreSQL database dump complete
--

