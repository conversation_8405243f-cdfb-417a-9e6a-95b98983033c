<?php

namespace TF\Commands\sprint_s06_1;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-803 command run on all databases.
 */
class TS803Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s06_1:TS-803')
            ->setDescription('Sazdava tablicite za podmodul "Ipoteki".');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        exec('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-803.sql', $return);

        $dbhDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $dbhDev->setAttribute(PDO::ATTR_EMULATE_PREPARES, true);
        $dbhDev->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_WARNING);

        $sqlContragents = $dbhDev->prepare('SELECT DISTINCT(h_contragent) FROM su_hypothecs_old');

        $sqlContragents->execute();
        $contragents = $sqlContragents->fetchAll(PDO::FETCH_ASSOC);

        $sqlInsertCreditor = "INSERT INTO su_hypothecs_creditors (name) VALUES ('');";
        for ($j = 0; $j < count($contragents); $j++) {
            $sqlInsertCreditor .= "INSERT INTO su_hypothecs_creditors (name) VALUES ('{$contragents[$j]['h_contragent']}');";
        }

        exec('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -c "' . $sqlInsertCreditor . '"', $return);

        $sqlHypothecs = $dbhDev->prepare('SELECT h_num, array_agg(DISTINCT(h_date)) as h_date, array_agg(DISTINCT(h_comment)) as h_comment, 
	                            array_agg(DISTINCT(h_due_date)) as h_due_date, array_agg(DISTINCT(h_contragent)) as h_contragent, array_agg(DISTINCT(plot_id)) as plot_id
	                            FROM su_hypothecs_old 
	                            GROUP BY h_num, h_date, h_due_date, h_contragent');

        $sqlHypothecs->execute();
        $data = $sqlHypothecs->fetchAll(PDO::FETCH_ASSOC);

        $hypothecsPlotsRel = [];

        for ($j = 0; $j < count($data); $j++) {
            $creditor = trim($data[$j]['h_contragent'], '{""}');

            $sqlCreditor = $dbhDev->prepare('SELECT id FROM su_hypothecs_creditors WHERE name = :creditor');
            $sqlCreditor->bindValue(':creditor', $creditor);

            $sqlCreditor->execute();
            $creditors = $sqlCreditor->fetchAll(PDO::FETCH_ASSOC);

            $creditorId = 1;
            if ($creditors) {
                $creditorId = $creditors[0]['id'];
            }

            $plotIds = trim($data[$j]['plot_id'], '{}');
            $sqlFarming = $dbhDev->prepare("SELECT farming_id FROM su_contracts c
	                                JOIN su_contracts_plots_rel pc ON pc.contract_id = c.id
	                                WHERE plot_id IN({$plotIds})
	                                AND nm_usage_rights = 1
	                                GROUP BY farming_id");

            $sqlFarming->execute();
            $farmings = $sqlFarming->fetchAll(PDO::FETCH_ASSOC);

            $farmingId = 0;
            if ($farmings) {
                $farmingId = $farmings[0]['farming_id'];
            }

            $sqlInsertHypothec = $dbhDev->prepare('INSERT INTO su_hypothecs (num, date, due_date, comment, creditor_id, start_date, farming_id)
	                                                VALUES (:num, :date, :due_date, :comment, :creditor_id, :start_date, :farming_id)');

            $sqlInsertHypothec->bindValue(':num', $data[$j]['h_num']);
            $sqlInsertHypothec->bindValue(':date', trim($data[$j]['h_date'], '{}'));
            $sqlInsertHypothec->bindValue(':due_date', trim($data[$j]['h_due_date'], '{}'));
            $sqlInsertHypothec->bindValue(':comment', trim($data[$j]['h_comment'], '{}'));
            $sqlInsertHypothec->bindValue(':creditor_id', $creditorId);
            $sqlInsertHypothec->bindValue(':start_date', trim($data[$j]['h_date'], '{}'));
            $sqlInsertHypothec->bindValue(':farming_id', $farmingId);

            $sqlInsertHypothec->execute();

            $hypothecId = $dbhDev->lastInsertId('su_hypothecs_id_seq');

            $plots = explode(',', $plotIds);

            $temp = [];
            for ($k = 0; $k < count($plots); $k++) {
                $temp[] = [
                    'hypothec_id' => $hypothecId,
                    'plot_id' => $plots[$k],
                ];
            }

            $hypothecsPlotsRel = array_merge($hypothecsPlotsRel, $temp);
        }

        for ($j = 0; $j < count($hypothecsPlotsRel); $j++) {
            $hypothecId = (int) $hypothecsPlotsRel[$j]['hypothec_id'];
            $plotId = (int) $hypothecsPlotsRel[$j]['plot_id'];
            $sqlHPRel = "INSERT INTO su_hypothecs_plots_rel (hypothec_id, plot_id, hypothec_area) VALUES ({$hypothecId}, {$plotId}, (SELECT (CASE WHEN document_area IS NULL THEN round((ST_Area(geom)/1000)::numeric, 3) ELSE round(document_area::numeric, 3) END) FROM layer_kvs WHERE gid = {$plotId}));";

            $sqlInsertRel = $dbhDev->prepare($sqlHPRel);
            $sqlInsertRel->execute();
        }

        $dbhDev = null;

        $sqlDropTable = 'DROP TABLE IF EXISTS su_hypothecs_old';
        exec('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -c "' . $sqlDropTable . '"', $return);
    }
}
