<?php

namespace TF\Commands\sprint_s15;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-2693 command run on all databases.
 */
class TS2693Command extends UserDbCommand
{
    protected $skipLogging = true;
    protected $displayIssues = true;
    protected $withIssues = [];

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s15:TS-2693')
            ->setDescription('proverqva za kolona "is_edited" v layer_kvs dali e syzdadena korektno');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $userDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
        $sql = $userDev->prepare(
            "select column_name, column_default, is_nullable, data_type from INFORMATION_SCHEMA.COLUMNS where table_name = 'layer_kvs' and column_name = 'is_edited'"
        );

        $sql->execute();
        $column = $sql->fetchAll();

        if (0 == count($column)) {
            $this->withIssues[] = $userDb . ': ne syshtestvuva kolona is_edited';
        }
        if ('false' !== $column[0]['column_default']) {
            $this->withIssues[] = $userDb . ': is_edited nqma korektna default stoinost';
        }
        if ('NO' !== $column[0]['is_nullable']) {
            $this->withIssues[] = $userDb . ': is_edited e nullable';
        }
    }

    protected function displayIssues()
    {
        if (count($this->withIssues)) {
            foreach ($this->withIssues as $issue) {
                $this->output->writeln("<error>{$issue}</error>");
            }
        } else {
            $this->output->writeln('<success>TS-2693: No issues found</success>');
        }
    }
}
