<?php

namespace TF\Commands\sprint_s15;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-2608 command run on all databases.
 */
class TS2608Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s15:TS-2608')
            ->setDescription('dobavq kolona "is_foreigner" v su_owners');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-2608.sql', $return);
    }
}
