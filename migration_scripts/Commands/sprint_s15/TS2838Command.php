<?php

namespace TF\Commands\sprint_s15;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-2838 command run on all databases.
 */
class TS2838Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s15:TS-2838')
            ->setDescription('premahva izlishni zapisi ot su_subleases_plots_area i su_subleases_plots_contracts_rel');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-2838.sql', $return);
    }
}
