<?php

namespace TF\Commands\sprint_s15;

use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * TS-2774 command run on all databases.
 */
class TS2774Command extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('sprint_s15:TS-2774')
            ->setDescription('dobavq kolona "login_token" v susi_main.su_users');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . DEFAULT_DB_DATABASE . ' -f ' . __DIR__ . '/sql/TS-2774.sql', $return);
    }
}
