<?php

namespace TF\Commands\sprint_s15;

use PDO;
use Symfony\Component\Console\Formatter\OutputFormatterStyle;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-2899 command run on all databases.
 */
class TS2899Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s15:TS-2899')
            ->setDescription('pregenerira views za presichane na ISAK i dopustim sloi');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $style = new OutputFormatterStyle('white', 'green');
        $output->getFormatter()->setStyle('success', $style);
        $style = new OutputFormatterStyle('black', 'yellow');
        $output->getFormatter()->setStyle('error', $style);

        $userDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
        $sql = $userDev->prepare(
            "SELECT distinct(oid::regclass::text) as name
            FROM   pg_class
            WHERE  relkind = 'm' and oid::regclass::text like '%allowable_from_isak_%'"
        );

        $sql->execute();
        $views = $sql->fetchAll();
        $names = [];

        if (count($views) > 0) {
            foreach ($views as $view) {
                array_push($names, $view['name']);
            }

            foreach ($names as $name) {
                $refreshSql = "REFRESH MATERIALIZED VIEW {$name};";

                $createCmd = $userDev->prepare($refreshSql);
                $createCmd->execute();
                $output->writeln("<success>{$userDb}: {$name} updated</success>");
            }
        } else {
            $output->writeln("<error>{$userDb}: no views to update</error>");
        }
    }
}
