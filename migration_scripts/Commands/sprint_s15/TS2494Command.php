<?php

namespace TF\Commands\sprint_s15;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-2494 command run on all databases.
 */
class TS2494Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s15:TS-2494')
            ->setDescription('smenq kolonata "pl_dka" sys "pl_dka_po" v materializiranite views za tematichnite karti');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-2494.sql', $return);
    }
}
