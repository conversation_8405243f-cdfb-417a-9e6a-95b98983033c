<?php

namespace TF\Commands\sprint_s15;

use TF\Commands\Common\UserDbCommand;

/**
 * Runs all scripts required for migration from Sprint 14 to Sprint 15.
 */
class UpdateToSprint15 extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s15:update-to-sprint15')
            ->setDescription('Runs all scripts required for migration from Sprint 14 to Sprint 15.');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $commandsArr = [
            // Sprint 15
            new TS2608Command(),
            new TS2494Command(),
            new TS2838Command(),

            // Common
            new \TF\Commands\Common\RegenerateMapFilesCommand(),
        ];

        for ($i = 0; $i < count($commandsArr); $i++) {
            $command = $commandsArr[$i];
            $this->logScript($userDb, $command->getName());
            $output->writeln('Executing command: ' . $command->getName());
            $command->onDbExecute($userDb, $output, $input);
        }
    }

    protected function logScript($user_db, $name)
    {
        $date = date('Y-m-d H:i:s');

        $this->openConnection();

        $sql = $this->mainConnection->prepare("
            INSERT INTO scripts_log (db_name, name, date)
            VALUES ('{$user_db}', '{$name}', '{$date}')
        ");

        return $sql->execute();
    }
}
