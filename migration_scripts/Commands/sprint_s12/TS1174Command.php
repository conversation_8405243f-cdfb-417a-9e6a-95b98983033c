<?php

namespace TF\Commands\sprint_s12;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-1174 command run on all databases.
 */
class TS1174Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s12:TS-1174')
            ->setDescription('Runnva se sled TS-11741 script!!! Promqna zaradi lichnoto polzvane na mat views - charged_rentas_mat_view, charged_rentas_annexes_mat_view, renta_nats_annexes_mat_view, renta_nats_mat_view i syzdavane na trigger SU_PERSONAL_USE_TRIGGER');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-1174.sql', $return);
    }
}
