<?php

namespace TF\Commands\sprint_s12;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-21881 command run on all databases.
 */
class TS21881Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s12:TS-2188-1')
            ->setDescription('Premahvane na prava za dostap do podmodul Tematichni karti');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $this->openConnection();

        $sql = $this->mainConnection->prepare(
            "SELECT database,id FROM su_users u
            WHERE database = {$userDb} AND u.level = 2 
            ORDER BY database"
        );

        $sql->execute();
        $results = $sql->fetchAll();

        $id = $results[0]['id'];

        // Добабяне на права за четене на подмодул Тематични карти
        $insertSql = '
        DELETE FROM su_users_rights where user_id = :id and right_id = 22;
        ';

        $insertCmd = $this->mainConnection->prepare($insertSql);
        $insertCmd->bindParam(':id', $id);
        $insertCmd->execute();

        // Добавяне на права за писане в подмодул Тематични карти
        $insertSql = '
        DELETE FROM su_users_rights where user_id = :id and right_id = 23;
        ';

        $insertCmd = $this->mainConnection->prepare($insertSql);
        $insertCmd->bindParam(':id', $id);
        $insertCmd->execute();
    }
}
