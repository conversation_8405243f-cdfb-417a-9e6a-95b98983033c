<?php

namespace TF\Commands\sprint_s12;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-2267 command run on all databases.
 */
class TS2267Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s12:TS-2267')
            ->setDescription('dobavq zapisi v su_subleases_plots_area, kadeto lipsvat takiva, no ima zapis za saotvetniq sublease i plot v su_subleases_plots_rel');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-2267.sql', $return);
    }
}
