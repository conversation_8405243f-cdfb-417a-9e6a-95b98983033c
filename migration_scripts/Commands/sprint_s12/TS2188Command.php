<?php

namespace TF\Commands\sprint_s12;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-2188 command run on all databases.
 */
class TS2188Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s12:TS-2188')
            ->setDescription('Dobavqne na prava za dostap do podmodul Tematichni karti');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        define(THEMATIC_MAPS_RIGHTS_R, 22);
        define(THEMATIC_MAPS_RIGHTS_RW, 23);

        $this->openConnection();

        $sql = $this->mainConnection->prepare(
            "SELECT id FROM su_users u
            WHERE database = {$userDb} AND u.level = 2
            AND paid_support = '2016-01-01'"
        );

        $sql->execute();
        $results = $sql->fetchAll();

        $sql = $this->mainConnection->prepare(
            "SELECT user_id, array_agg(right_id) as rights FROM su_users_rights
            WHERE user_id IN ({$results[0]['id']})
            GROUP BY user_id"
        );

        $sql->execute();
        $userRights = $sql->fetchAll();

        for ($i = 0; $i < count($userRights); $i++) {
            $id = $userRights[$i]['user_id'];
            $output->writeln("User ID: {$id} \n");

            $rights = explode(',', str_replace(['{', '}'], ['', ''], $userRights[$i]['rights']));

            if (!in_array(THEMATIC_MAPS_RIGHTS_R, $rights)) {
                // Добабяне на права за четене на подмодул Тематични карти
                $insertSql = '
                INSERT INTO su_users_rights VALUES(:id, 22);
                ';

                $insertCmd = $this->mainConnection->prepare($insertSql);
                $insertCmd->bindParam(':id', $id);
                $insertCmd->execute();
            }

            if (!in_array(THEMATIC_MAPS_RIGHTS_RW, $rights)) {
                // Добавяне на права за писане в подмодул Тематични карти
                $insertSql = '
                INSERT INTO su_users_rights VALUES(:id, 23);
                ';

                $insertCmd = $this->mainConnection->prepare($insertSql);
                $insertCmd->bindParam(':id', $id);
                $insertCmd->execute();
            }
        }
    }
}
