<?php

namespace TF\Commands\sprint_s12;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-1911 command run on all databases.
 */
class TS1911Command extends UserDbCommand
{
    protected $skipLogging = true;

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s12:TS-1911')
            ->setDescription('Dobavqne stoinosti v su_users_layers->style');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $this->openConnection();

        $sql = $this->mainConnection->prepare(
            "SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name='su_users_layers' and column_name='style';"
        );

        $sql->execute();
        $results = $sql->fetchAll();

        if ('style' == $results[0]['column_name']) {
            $this->displayErrorMessage('Column styles exists in su_users_layers. Skipping!', $output, 'black', 'green');
        } else {
            $this->displayErrorMessage('Adding "styles" in su_users_layers', $output, 'black', 'green');
            system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . DEFAULT_DB_DATABASE . ' -f ' . __DIR__ . '/sql/TS-1911.sql', $return);
        }

        $sql = $this->mainConnection->prepare(
            'SELECT database, id FROM su_users u
            WHERE database = :userDb AND u.level = 2 '
        );
        $sql->bindParam(':userDb', $userDb);
        $sql->execute();
        $results = $sql->fetchAll();
        $group_id = $results[0]['id'];

        // set style column in su_users_layers

        $sql = $this->mainConnection->prepare(
            "SELECT * FROM su_users_layers ul
            WHERE ul.group_id = {$group_id} "
        );

        $sql->execute();
        $user_layers = $sql->fetchAll();
        foreach ($user_layers as $key => $value) {
            if (5 == $value['layer_type']) {
                // build kvs style
                $ekkates = $this->getUserEkkates($userDb);

                $style = [];
                foreach ($ekkates as $ekkate_key => $ekkate_value) {
                    $style[$ekkate_value['ekate']] = [
                        'color' => $value['color'],
                        'border_color' => $value['border_color'],
                        'transparency' => $value['transparency'],
                        'border_only' => $value['border_only'],
                        'label_name' => [('' != $value['label_name'] && 'Array' != $value['label_name']) ? $value['label_name'] : 'kad_ident'],
                        'tags' => $value['tags'],
                        'label_size' => 8,
                    ];
                }
            } else {
                // build normal layer style

                $style = [
                    'color' => $value['color'],
                    'border_color' => $value['border_color'],
                    'transparency' => $value['transparency'],
                    'border_only' => $value['border_only'],
                    'label_name' => [('' != $value['label_name'] && 'Array' != $value['label_name']) ? $value['label_name'] : null],
                    'tags' => $value['tags'],
                    'label_size' => 8,
                ];
            }

            $style = json_encode($style);

            $updateSql = $this->mainConnection->prepare(" update su_users_layers set style = '" . $style . "'  where id = {$value['id']} ");
            $updateSql->execute();
        }
    }

    private function getUserEkkates($dbName)
    {
        $userEkkates = [];

        $userDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $dbName . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
        $ekateSql = $userDev->prepare('SELECT ekate from layer_kvs GROUP BY ekate');

        $ekateSql->execute();

        return $ekateSql->fetchAll();
    }
}
