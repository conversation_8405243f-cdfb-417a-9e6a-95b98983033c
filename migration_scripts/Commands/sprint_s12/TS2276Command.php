<?php

namespace TF\Commands\sprint_s12;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-2276 command run on all databases.
 */
class TS2276Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s12:TS-2276')
            ->setDescription('dobavq kolona "old_kad_ident" v layer_kvs');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-2276.sql', $return);
    }
}
