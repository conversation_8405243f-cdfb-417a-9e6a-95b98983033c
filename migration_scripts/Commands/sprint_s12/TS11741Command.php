<?php

namespace TF\Commands\sprint_s12;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-11741 command run on all databases.
 */
class TS11741Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s12:TS-11741')
            ->setDescription('Pyrvo tozi script se rynva!!! Dobavqne na nova kolona v su_personal_use pc_rel_id i dobavqneto i kato foreign key, pozvolqvane na kolonata contract_id da e NULL, iztrivane na vsichki zapisi ot su_personal_use, premahvane na foreign key na contract_id');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-11741.sql', $return);
    }
}
