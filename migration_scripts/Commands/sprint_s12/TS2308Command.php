<?php

namespace TF\Commands\sprint_s12;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-2308 command run on all databases.
 */
class TS2308Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s12:TS-2308')
            ->setDescription('dobavq koloni "reicpient_address" i "recipient_lk" v su_transactions');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-2308.sql', $return);
    }
}
