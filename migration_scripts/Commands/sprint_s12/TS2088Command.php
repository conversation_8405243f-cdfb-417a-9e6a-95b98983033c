<?php

namespace TF\Commands\sprint_s12;

use TF\Commands\Common\UserDbCommand;

class TS2088Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s12:TS-2088')
            ->setDescription('Dobavq kolona `su_users_files.add_to_existing`.');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . DEFAULT_DB_DATABASE . ' -f "' . __DIR__ . '/sql/TS-2088.sql"', $return);
    }
}
