CREATE TABLE su_thematic_maps (
    "id" int4 NOT NULL,
	"filters" json,
	"name" varchar(255) NOT NULL,
	"criteria" varchar(255) NOT NULL,
	"criteria_text" varchar(255),
	"criteria_prefix" varchar(255),
	"map_layer" varchar(255) NOT NULL,
	"chart_type" varchar(255),
	"chart_criteria" varchar(255),
	"colors" json,
	"extent" varchar(255),
	PRIMARY KEY("id")
);

ALTER TABLE public.su_thematic_maps OWNER TO postgres;
--
-- Name: su_thematic_maps_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE su_thematic_maps_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER TABLE public.su_thematic_maps_id_seq OWNER TO postgres;
ALTER TABLE "public"."su_thematic_maps"
ALTER COLUMN "id" SET DEFAULT nextval('su_thematic_maps_id_seq'::regclass);

--
-- Name: su_thematic_maps_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE su_thematic_maps_id_seq OWNED BY su_thematic_maps.id;