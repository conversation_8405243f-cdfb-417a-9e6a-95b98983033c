--изтриване на връзки към несъществуващи договори

DELETE from su_subleases_plots_contracts_rel where sublease_id not in (SELECT id from su_contracts);

--добявя липсващите записи в su_subleases_plots_area

INSERT INTO su_subleases_plots_area (
    sublease_id,
    plot_id,
    contract_area
)(
    SELECT
        spc.sublease_id,
        cp.plot_id,
        cp.contract_area
    FROM
        su_subleases_plots_contracts_rel spc
    JOIN su_contracts_plots_rel cp ON (spc.pc_rel_id = cp. ID)
    WHERE
        (
            SELECT
                spa. ID
            FROM
                su_subleases_plots_area spa
            WHERE
                spa.plot_id = cp.plot_id
            AND spa.sublease_id = spc.sublease_id
        ) IS NULL
);

--трие излишните записи от su_subleases_plots_area
DELETE
FROM
    su_subleases_plots_area spa
WHERE
    spa.sublease_id = (
        SELECT
            MIN (spa1.sublease_id)
        FROM
            su_subleases_plots_area spa1
    )
AND spa.plot_id NOT IN (
    SELECT
        cp.plot_id AS good_plots
    FROM
        su_subleases_plots_contracts_rel spc
    JOIN su_contracts_plots_rel cp ON (spc.pc_rel_id = cp. ID)
    WHERE
        sublease_id = (
            SELECT
                MIN (sublease_id)
            FROM
                su_subleases_plots_area
        )
);

-- добавя foreign key constraint към su_subleases_plots_contracts_rel
ALTER TABLE su_subleases_plots_contracts_rel ADD FOREIGN KEY (sublease_id) REFERENCES su_contracts (id) ON DELETE CASCADE ON UPDATE NO ACTION;