--премахва дублиращите се записи в su_osz_files_plots
DELETE
FROM
    su_osz_files_plots
WHERE
    ID IN (
        SELECT
            ID
        FROM
            (
                SELECT
                    ID,
                    ROW_NUMBER () OVER (
                        PARTITION BY id_imot,
                        kvs_no,
                        kad_no,
                        kod_subekt,
                        txt_subekt,
                        ime_subekt,
                        egn_subekt,
                        kod_pr_osn,
                        txt_pr_osn,
                        pl_dka,
                        pl_dka_po,
                        kategoria,
                        kod_ntp,
                        txt_ntp,
                        kod_sobstv,
                        txt_sobstv,
                        kod_imot,
                        txt_imot,
                        ekatte,
                        ver_no,
                        DATA,
                        vreme,
                        file_id
                    ORDER BY
                        ID
                    ) AS rnum
                FROM
                    su_osz_files_plots
            ) T
        WHERE
            T .rnum > 1
    );