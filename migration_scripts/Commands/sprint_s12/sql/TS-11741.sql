-- Iztrivane na vsichki zapisi ot su_personal_use
DELETE FROM su_personal_use;

-- Dobavqne na nova kolona v su_personal_use pc_rel_id i dobavqneto i kato foreign key
ALTER TABLE "public"."su_personal_use"
	ADD COLUMN "pc_rel_id" integer DEFAULT NULL;
ALTER TABLE "public"."su_personal_use"
ADD CONSTRAINT "pc_rel_pers_use_fkey" FOREIGN KEY ("pc_rel_id") REFERENCES "public"."su_contracts_plots_rel" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- Pozvolqvane na kolonata contract_id da e NULL
ALTER TABLE su_personal_use ALTER COLUMN contract_id DROP NOT NULL;

-- Premahvane na foreign key na contract_id
ALTER TABLE su_personal_use DROP CONSTRAINT "personal_use_contract_id";