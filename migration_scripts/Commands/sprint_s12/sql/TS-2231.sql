DROP MATERIALIZED VIEW IF EXISTS topic_layer_kvs_by_owner_name_label_items;
DROP MATERIALIZED VIEW IF EXISTS topic_layer_kvs_by_agreement_mat_view;
DROP MATERIALIZED VIEW IF EXISTS topic_layer_kvs_by_agreement_label_items;
DROP MATERIALIZED VIEW IF EXISTS topic_layer_kvs_by_category_mat_view;
DROP MATERIALIZED VIEW IF EXISTS topic_layer_kvs_by_category_label_items;
DROP MATERIALIZED VIEW IF EXISTS topic_layer_kvs_by_ntp_mat_view;
DROP MATERIALIZED VIEW IF EXISTS topic_layer_kvs_by_ntp_label_items;
DROP MATERIALIZED VIEW IF EXISTS topic_layer_kvs_by_owner_name_mat_view;
DROP MATERIALIZED VIEW IF EXISTS topic_layer_kvs_by_owner_name_label_items;
DROP MATERIALIZED VIEW IF EXISTS topic_layer_kvs_by_ownership_mat_view;
DROP MATERIALIZED VIEW IF EXISTS topic_layer_kvs_by_ownership_label_items;
DROP MATERIALIZED VIEW IF EXISTS topic_layer_kvs_by_tenant_name_mat_view;
DROP MATERIALIZED VIEW IF EXISTS topic_layer_kvs_by_tenant_name_label_items;

ALTER TABLE su_osz_files_plots
    ALTER COLUMN kod_pr_osn TYPE varchar(2);

-----------------------------------------------------------------
--  ТЕМАТИЧНА КАРТА ПО СПОРАЗУМЕНИЕ
-----------------------------------------------------------------
DROP MATERIALIZED VIEW IF EXISTS topic_layer_kvs_by_agreement_mat_view;
CREATE MATERIALIZED VIEW topic_layer_kvs_by_agreement_mat_view AS
 SELECT DISTINCT osz.egn_subekt,
    st_union(kvs.geom) AS geom,
    sum(osz.pl_dka) AS area,
    max((osz.ime_subekt)::text) AS ime_subekt,
    array_length(array_agg(DISTINCT kvs.kad_ident), 1) AS number,
    kvs.ekate,
    array_agg(DISTINCT kvs.gid) AS gid
   FROM (su_osz_files_plots osz
     LEFT JOIN layer_kvs kvs ON (((osz.kad_no)::text = (kvs.kad_ident)::text)))
  WHERE (((osz.kod_pr_osn)::text = ANY (ARRAY[('4'::character varying)::text, ('5'::character varying)::text])) AND ((osz.pl_dka)::double precision > (0)::double precision))
  GROUP BY osz.egn_subekt, kvs.ekate
  ORDER BY sum(osz.pl_dka) DESC;

DROP MATERIALIZED VIEW IF EXISTS topic_layer_kvs_by_agreement_label_items;
CREATE MATERIALIZED VIEW topic_layer_kvs_by_agreement_label_items AS
 SELECT DISTINCT kvs.gid,
    st_centroid(kvs.geom) AS geom,
        CASE
            WHEN ((osz.ime_subekt)::text <> ''::text) THEN (((osz.ime_subekt)::text || ','::text) || (kvs.kad_ident)::text)
            ELSE max((kvs.kad_ident)::text)
        END AS ime_subekt,
    kvs.ekate,
    max(osz.file_id) AS file_id
   FROM (su_osz_files_plots osz
     LEFT JOIN layer_kvs kvs ON (((osz.kad_no)::text = (kvs.kad_ident)::text)))
  WHERE ((osz.kod_pr_osn)::text = ANY (ARRAY[('4'::character varying)::text, ('5'::character varying)::text]))
  GROUP BY kvs.gid, osz.ime_subekt;

-----------------------------------------------------------------
--  ТЕМАТИЧНА КАРТА ПО КАТЕГОРИЯ
-----------------------------------------------------------------
DROP MATERIALIZED VIEW IF EXISTS topic_layer_kvs_by_category_mat_view;
CREATE MATERIALIZED VIEW topic_layer_kvs_by_category_mat_view AS
 SELECT st_union(kvs.geom) AS geom,
    (sum(osz.pl_dka))::double precision AS area,
    osz.kategoria AS ime_subekt,
    array_length(array_agg(DISTINCT kvs.kad_ident), 1) AS number,
    kvs.ekate,
    array_agg(DISTINCT kvs.gid) AS gid
   FROM (su_osz_files_plots osz
     LEFT JOIN layer_kvs kvs ON (((osz.kad_no)::text = (kvs.kad_ident)::text)))
  WHERE ((osz.pl_dka)::double precision > (0)::double precision)
  GROUP BY osz.kategoria, kvs.ekate
  ORDER BY (sum(osz.pl_dka))::double precision DESC;

DROP MATERIALIZED VIEW IF EXISTS topic_layer_kvs_by_category_label_items;
CREATE MATERIALIZED VIEW topic_layer_kvs_by_category_label_items AS
 SELECT DISTINCT kvs.gid,
    st_centroid(kvs.geom) AS geom,
        CASE
            WHEN ((osz.kategoria)::text <> ''::text) THEN (((osz.kategoria)::text || ','::text) || (kvs.kad_ident)::text)
            ELSE max((kvs.kad_ident)::text)
        END AS ime_subekt,
    kvs.ekate,
    max(osz.file_id) AS file_id
   FROM (su_osz_files_plots osz
     LEFT JOIN layer_kvs kvs ON (((osz.kad_no)::text = (kvs.kad_ident)::text)))
  WHERE (kvs.gid IS NOT NULL)
  GROUP BY kvs.gid, osz.kategoria;

-----------------------------------------------------------------
--  ТЕМАТИЧНА КАРТА ПО НТП
-----------------------------------------------------------------
DROP MATERIALIZED VIEW IF EXISTS topic_layer_kvs_by_ntp_mat_view;
CREATE MATERIALIZED VIEW topic_layer_kvs_by_ntp_mat_view AS
 SELECT st_union(kvs.geom) AS geom,
    (sum(osz.pl_dka))::double precision AS area,
    osz.kod_ntp,
    osz.txt_ntp,
    array_length(array_agg(DISTINCT kvs.kad_ident), 1) AS number,
    kvs.ekate,
    array_agg(DISTINCT kvs.gid) AS gid
   FROM (su_osz_files_plots osz
     LEFT JOIN layer_kvs kvs ON (((osz.kad_no)::text = (kvs.kad_ident)::text)))
  WHERE ((osz.pl_dka)::double precision > (0)::double precision)
  GROUP BY osz.kod_ntp, osz.txt_ntp, kvs.ekate
  ORDER BY (sum(osz.pl_dka))::double precision DESC;

DROP MATERIALIZED VIEW IF EXISTS topic_layer_kvs_by_ntp_label_items;
CREATE MATERIALIZED VIEW topic_layer_kvs_by_ntp_label_items AS
 SELECT DISTINCT kvs.gid,
    st_centroid(kvs.geom) AS geom,
    (((osz.txt_ntp)::text || ','::text) || (kvs.kad_ident)::text) AS ime_subekt,
    kvs.ekate,
    max(osz.file_id) AS file_id
   FROM (su_osz_files_plots osz
     LEFT JOIN layer_kvs kvs ON (((osz.kad_no)::text = (kvs.kad_ident)::text)))
  WHERE (kvs.gid IS NOT NULL)
  GROUP BY kvs.gid, osz.txt_ntp;
  
-----------------------------------------------------------------
--  ТЕМАТИЧНА КАРТА ПО СОБСТВЕНИК
-----------------------------------------------------------------
DROP MATERIALIZED VIEW IF EXISTS topic_layer_kvs_by_owner_name_mat_view;
CREATE MATERIALIZED VIEW topic_layer_kvs_by_owner_name_mat_view AS
 SELECT DISTINCT osz.egn_subekt,
    st_union(kvs.geom) AS geom,
    (sum(osz.pl_dka))::double precision AS area,
    max(DISTINCT (osz.ime_subekt)::text) AS ime_subekt,
    array_length(array_agg(DISTINCT kvs.kad_ident), 1) AS number,
    kvs.ekate,
    array_agg(DISTINCT kvs.gid) AS gid
   FROM (su_osz_files_plots osz
     LEFT JOIN layer_kvs kvs ON (((osz.kad_no)::text = (kvs.kad_ident)::text)))
  WHERE (((osz.kod_pr_osn)::text = ANY (ARRAY[('1'::character varying)::text, ('6'::character varying)::text])) AND ((osz.pl_dka)::double precision > (0)::double precision))
  GROUP BY osz.egn_subekt, kvs.ekate
  ORDER BY (sum(osz.pl_dka))::double precision DESC;

DROP MATERIALIZED VIEW IF EXISTS topic_layer_kvs_by_owner_name_label_items;
CREATE MATERIALIZED VIEW topic_layer_kvs_by_owner_name_label_items AS
 SELECT DISTINCT kvs.gid,
    st_centroid(kvs.geom) AS geom,
        CASE
            WHEN ((osz.ime_subekt)::text <> ''::text) THEN (((osz.ime_subekt)::text || ','::text) || (kvs.kad_ident)::text)
            ELSE max((kvs.kad_ident)::text)
        END AS ime_subekt,
    kvs.ekate,
    max(osz.file_id) AS file_id
   FROM (su_osz_files_plots osz
     LEFT JOIN layer_kvs kvs ON (((osz.kad_no)::text = (kvs.kad_ident)::text)))
  WHERE (((osz.kod_pr_osn)::text = ANY (ARRAY[('1'::character varying)::text, ('6'::character varying)::text])) AND (osz.pl_dka > (0)::numeric))
  GROUP BY kvs.gid, osz.ime_subekt;

-----------------------------------------------------------------
--  ТЕМАТИЧНА КАРТА ПО ТИП СОБСТВЕНОСТ
-----------------------------------------------------------------
DROP MATERIALIZED VIEW IF EXISTS topic_layer_kvs_by_ownership_mat_view;
CREATE MATERIALIZED VIEW topic_layer_kvs_by_ownership_mat_view AS
 SELECT st_union(kvs.geom) AS geom,
    sum(osz.pl_dka) AS area,
    osz.kod_sobstv,
    array_length(array_agg(DISTINCT kvs.kad_ident), 1) AS number,
    kvs.ekate,
    osz.txt_sobstv as ime_subekt,
    array_agg(DISTINCT kvs.gid) AS gid
   FROM (su_osz_files_plots osz
     LEFT JOIN layer_kvs kvs ON (((osz.kad_no)::text = (kvs.kad_ident)::text)))
  WHERE (st_area(kvs.geom) > (0)::double precision)
  GROUP BY osz.kod_sobstv, kvs.ekate, osz.txt_sobstv
  ORDER BY sum(osz.pl_dka) DESC;

DROP MATERIALIZED VIEW IF EXISTS topic_layer_kvs_by_ownership_label_items;
CREATE MATERIALIZED VIEW topic_layer_kvs_by_ownership_label_items AS
 SELECT DISTINCT kvs.gid,
    st_centroid(kvs.geom) AS geom,
    osz.kod_sobstv,
        CASE
            WHEN ((osz.txt_sobstv)::text <> ''::text) THEN (((osz.txt_sobstv)::text || ','::text) || (kvs.kad_ident)::text)
            ELSE max((kvs.kad_ident)::text)
        END AS ime_subekt,
    kvs.ekate,
    max(osz.file_id) AS file_id
   FROM (su_osz_files_plots osz
     LEFT JOIN layer_kvs kvs ON (((osz.kad_no)::text = (kvs.kad_ident)::text)))
  WHERE (kvs.gid IS NOT NULL)
  GROUP BY kvs.gid, osz.kod_sobstv, osz.txt_sobstv;
  
-----------------------------------------------------------------
--  ТЕМАТИЧНА КАРТА ПО АРЕНДАТОРИ
-----------------------------------------------------------------
DROP MATERIALIZED VIEW IF EXISTS topic_layer_kvs_by_tenant_name_mat_view;
CREATE MATERIALIZED VIEW topic_layer_kvs_by_tenant_name_mat_view AS
 SELECT DISTINCT osz.egn_subekt,
    st_union(kvs.geom) AS geom,
    sum(osz.pl_dka) AS area,
    max((osz.ime_subekt)::text) AS ime_subekt,
    array_length(array_agg(DISTINCT kvs.kad_ident), 1) AS number,
    kvs.ekate,
    array_agg(DISTINCT kvs.gid) AS gid
   FROM (su_osz_files_plots osz
     LEFT JOIN layer_kvs kvs ON (((osz.kad_no)::text = (kvs.kad_ident)::text)))
  WHERE (((osz.kod_pr_osn)::text = ANY (ARRAY[('2'::character varying)::text, ('3'::character varying)::text, ('7'::character varying)::text])) AND ((osz.pl_dka)::double precision > (0)::double precision))
  GROUP BY osz.egn_subekt, kvs.ekate
  ORDER BY sum(osz.pl_dka) DESC;

DROP MATERIALIZED VIEW IF EXISTS topic_layer_kvs_by_tenant_name_label_items;
CREATE MATERIALIZED VIEW topic_layer_kvs_by_tenant_name_label_items AS
 SELECT DISTINCT kvs.gid,
    st_centroid(kvs.geom) AS geom,
        CASE
            WHEN ((osz.ime_subekt)::text <> ''::text) THEN (((osz.ime_subekt)::text || ','::text) || (kvs.kad_ident)::text)
            ELSE max((kvs.kad_ident)::text)
        END AS ime_subekt,
    kvs.ekate,
    max(osz.file_id) AS file_id
   FROM (su_osz_files_plots osz
     LEFT JOIN layer_kvs kvs ON (((osz.kad_no)::text = (kvs.kad_ident)::text)))
  WHERE (((osz.kod_pr_osn)::text = ANY (ARRAY[('2'::character varying)::text, ('3'::character varying)::text, ('7'::character varying)::text])) AND (kvs.gid IS NOT NULL))
  GROUP BY kvs.gid, osz.ime_subekt;
  
  
 DROP MATERIALIZED VIEW "public"."topic_layer_kvs_by_owner_name_label_items";

CREATE MATERIALIZED VIEW "topic_layer_kvs_by_owner_name_label_items"
AS 
 SELECT DISTINCT kvs.gid,
    st_centroid(kvs.geom) AS geom,
        CASE
            WHEN ((osz.ime_subekt)::text <> ''::text) THEN (((osz.ime_subekt)::text || ','::text) || (kvs.kad_ident)::text)
            ELSE max((kvs.kad_ident)::text)
        END AS ime_subekt,
        CASE
            WHEN ((osz.ime_subekt)::text <> ''::text) THEN string_agg(DISTINCT (osz.egn_subekt)::text, ','::text)
            ELSE NULL::text
        END AS egn_subekt,
    kvs.ekate,
    max(osz.file_id) AS file_id
   FROM (su_osz_files_plots osz
     LEFT JOIN layer_kvs kvs ON (((osz.kad_no)::text = (kvs.kad_ident)::text)))
  WHERE (((osz.kod_pr_osn)::text = ANY (ARRAY[('1'::character varying)::text, ('6'::character varying)::text])) AND (osz.pl_dka > (0)::numeric))
  GROUP BY kvs.gid, osz.ime_subekt;