<?php

namespace TF\Commands\sprint_s12;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-2107 command run on all databases.
 */
class TS2107Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s12:TS-2107')
            ->setDescription('Dobavqne na tablica su_thematic_maps. Dobavqne na foreign key v su_plots_farming_rel pc_rel_id. Premahvane na vsichki nevalidni zapisi v su_plots_farming_rel, za koito nqma korespondirasht zapis v su_contracts_plots_rel');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $userDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        // Заявка, с която да се изтрият всички несъществуващи връзки между su_contracts_plots_rel и su_plots_farming_rel
        $delSql = 'DELETE FROM su_plots_farming_rel WHERE pc_rel_id NOT IN (SELECT id FROM su_contracts_plots_rel)';
        $delCmd = $userDev->prepare($delSql);
        $delCmd->execute();

        // Заявка, с която се вземат всички връзки между имоти и договори за собственост, за които няма добавено стопанство като собственик.
        $sql = "SELECT
        cpr.id,
        c.farming_id
        FROM
        su_contracts_plots_rel cpr
        LEFT JOIN su_contracts c ON cpr.contract_id = c.id
        LEFT JOIN su_plots_farming_rel pfr ON cpr.id = pfr.pc_rel_id
        WHERE c.nm_usage_rights = 1
        AND pfr.id IS NULL
        AND cpr.annex_action = 'added'";

        $cmd = $userDev->prepare($sql);
        $cmd->execute();

        $cprResults = $cmd->fetchAll();

        if (count($cprResults)) {
            // След като са взети всички тези връзки се подготвя batch insert, с които да се въведат стопанствата от всеки договор
            // като собственик със 100% собственост за съответните имоти, за които потребителя не е въвел изрично стопанство
            // като собственик до момента. След промените направени със задача TS-2107 стопанствата ще се добавят автоматично към имоти
            // в договори за собственост със 100% собственост.
            $newSql = 'INSERT INTO su_plots_farming_rel (pc_rel_id, farming_id, percent) VALUES ';
            $count = count($cprResults);
            for ($i = 0; $i < $count; $i++) {
                $newSql .= '(' . $cprResults[$i]['id'] . ',' . $cprResults[$i]['farming_id'] . ', 100)';

                if ($i != $count - 1) {
                    $newSql .= ', ';
                }
            }

            $cmd1 = $userDev->prepare($newSql);
            $cmd1->execute();
        }

        // Накрая се добавя foreign key в таблицата за собственост на стопанствата, които да сочи към su_contracts_plots_rel
        $alterSql = '
        ALTER TABLE "public"."su_plots_farming_rel"
        ADD CONSTRAINT "pc_rel_id_rel" FOREIGN KEY ("pc_rel_id") REFERENCES "public"."su_contracts_plots_rel" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;';

        $alterCmd = $userDev->prepare($alterSql);
        $alterCmd->execute();

        // След подготовката на съществуващите таблици се добавя и новата таблица su_thematic_maps, която се използва
        // за работа с подмодул Тематични карти.
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-2107.sql', $return);
    }
}
