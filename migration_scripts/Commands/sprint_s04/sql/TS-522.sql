CREATE TABLE layer_deprived(
	id		integer PRIMARY KEY,
	ekatte_id	character varying(128),
	center_x	numeric,
	center_y	numeric,
	calc_area	numeric,
	agro_area	numeric,
	nm_lfa_eka  	character varying(128),
	nm_lfa_e_1	character varying(128),
	nm_lfa_e_3	character varying(128),
    	nm_lfa_lfa	character varying(128),
	geom		geometry(MultiPolygon,32635)
);

CREATE TABLE layer_natura_2000(
    	objectid_1      integer PRIMARY KEY,
	sitecode	character varying(128),
	iba_code	character varying(128),
CREATE TABLE layer_deprived(
	id		integer PRIMARY KEY,
	ekatte_id	character varying(128),
	center_x	numeric,
	center_y	numeric,
	calc_area	numeric,
	agro_area	numeric,
	nm_lfa_eka  	character varying(128),
	nm_lfa_e_1	character varying(128),
	nm_lfa_e_3	character varying(128),
    	nm_lfa_lfa	character varying(128),
	geom		geometry(MultiPolygon,32635)
);

CREATE TABLE layer_natura_2000(
    	objectid_1      integer PRIMARY KEY,
	sitecode	character varying(128),
	iba_code	character varying(128),
	name_bg		character varying(128),
	name_lat	character varying(128),
	type		character varying(128),
	source		character varying(255),
	date_		date,
	zapoved_no	character varying(128),
	dv		character varying(128),
	area_z_dka	numeric,
	shape_leng	numeric,
	shape_area	numeric,
	geom		geometry(MultiPolygon,32635)
);

CREATE TABLE layer_pzp(
	ext_id		numeric
	imoti_pml_	integer NOT NULL,
	obl		character varying(128),
	oblast		character varying(128),
	obshtina	character varying(128),
	zeml		character varying(128), 
	imekatte	character varying(128),
	imotcode	character varying(128) PRIMARY KEY,
	l_uscode	character varying(128),
	l_tycode	character varying(128),
	zemcad		character varying(128),
    	sharea		numeric,
	natura		character varying(128),
	area_spa	numeric,
	part_spa	numeric,
	area_sci	numeric,	
	part_sci	numeric,
	area_hab	numeric,
	part_hab	numeric,
	decl_oz		character varying(128),
	rem_1		character varying(128),
	rem_2		character varying(128),
	rem_3		character varying(128),
	pzp_imot	character varying(128),
   	pzp_area	numeric,
	pzp_perc	numeric,
	geom		geometry(MultiPolygon,32635)
);


