<?php

namespace TF\Commands\sprint_s04;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-515 command run on all databases.
 */
class TS515Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s04:TS-515')
            ->setDescription('preimenuva sloi Isak->otIsak i dobavq sloi zaIsak');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . DEFAULT_DB_DATABASE . ' -v v1="u.database = \'' . $userDb . '\'" -f ' . __DIR__ . '/sql/TS-515.sql');
    }
}
