<?php

namespace TF\Commands\sprint_s04;

use PDO;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Commands\ParamHelper;

/**
 * TS-519 command run on all databases.
 */
class TS519Command extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('sprint_s04:TS-519')
            ->setDescription('dobavq koloni cropcode i common_cultures kam tablicite ot tip zaIsak')
            ->addArgument(
                'user_databases',
                InputArgument::IS_ARRAY | InputArgument::OPTIONAL,
                'Databases to run the script on (separate multiple names with a space)?'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $dbhDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $user_databases = $input->getArgument('user_databases');
        $paramHelper = new ParamHelper();
        $userDbString = $paramHelper->createQueryParamString($user_databases);

        $userDbConditions = 'true';
        if ($user_databases) {
            $userDbConditions = 'u.database IN (' . $userDbString . ')';
        }

        $sql = $dbhDev->prepare(
            "SELECT ul.table_name,u.database FROM su_users_layers ul
        	INNER JOIN su_users u on u.id = ul.user_id
        	WHERE {$userDbConditions} AND ul.layer_type = 9"
        );

        $sql->execute();
        $results = $sql->fetchAll();

        for ($i = 0; $i < count($results); $i++) {
            $output->writeln('database=' . $results[$i]['database'] . ' ' . $results[$i]['table_name'] . '');

            $sql = "ALTER TABLE {$results[$i]['table_name']} ADD COLUMN common_cultures bool DEFAULT false NOT NULL; 
        			ALTER TABLE {$results[$i]['table_name']} ADD COLUMN cropcode varchar(10);";

            system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . $results[$i]['database'] . ' -c "' . $sql . '"', $return);
        }
    }
}
