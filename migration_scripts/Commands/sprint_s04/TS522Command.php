<?php

namespace TF\Commands\sprint_s04;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-522 command run on all databases.
 */
class TS522Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s04:TS-522');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $layers = [
            0 => [
                'name' => 'Необлагодетелствани райони',
                'type' => 10,
                'table_name' => 'layer_lfa',
                'color' => '0000ff',
            ],
            1 => [
                'name' => 'Натура 2000',
                'type' => 11,
                'table_name' => 'layer_natura_2000',
                'color' => '00ff00',
            ],
            2 => [
                'name' => 'Постоянно затревени площи',
                'type' => 12,
                'table_name' => 'layer_pzp',
                'color' => 'ff0000',
            ],
        ];

        $this->openConnection();

        $sql = $this->mainConnection->prepare(
            '
            select t.user_id , f.id as farm_id from su_users_layers t 
            join su_users_farming f on (f.user_id = t.user_id) 
            LEFT JOIN su_users u on (u.id = f.id)
            WHERE u.database = :userDb AND f.is_system = true 
            GROUP BY t.user_id, f.id 
            ORDER BY user_id'
        );

        $sql->bindParam(':userDb', $userDb);

        $sql->execute();
        // var_export($sql->errorInfo());
        $results = $sql->fetchAll();

        foreach ($results as $user) {
            $output->writeln($user[0] . '');
            foreach ($layers as $layer) {
                $sql = $this->mainConnection->prepare('INSERT INTO su_users_layers
                (user_id, name, table_name, date_created, color, border_color, extent, farming, year,transparency, position, layer_type, group_id, tags, border_only) values (' . $user[0] . ", '" . $layer['name'] . "', '" . $layer['table_name'] . "','2015-03-23 16:42:21.372834', '" . $layer['color'] . "', '111111', '125190.6162 4573142.7188 631370.3273 4887149.5823', " . $user[1] . ', 6, 100, 0, ' . $layer['type'] . ', ' . $user[0] . ', true, false);');
                $sql->execute();
            }
        }
    }
}
