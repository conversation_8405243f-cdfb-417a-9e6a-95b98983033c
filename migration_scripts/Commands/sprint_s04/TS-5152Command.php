<?php

namespace TF\Commands\sprint_s04;

use PDO;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Commands\ParamHelper;

/**
 * TS-515-2 command run on all databases.
 */
class TS5152Command extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('sprint_s04:TS-515-2')
            ->setDescription('promenq imenata na tablicite "isak" kam "za_isak" v bazata na vseki potrebitel')
            ->addArgument(
                'user_databases',
                InputArgument::IS_ARRAY | InputArgument::OPTIONAL,
                'Databases to run the script on (separate multiple names with a space)?'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $user_databases = $input->getArgument('user_databases');

        $paramHelper = new ParamHelper();
        $userDbString = $paramHelper->createQueryParamString($user_databases);

        $userDbConditions = 'true';
        if ($user_databases) {
            $userDbConditions = 'u.database IN (' . $userDbString . ')';
        }

        $dbhDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = $dbhDev->prepare("SELECT DISTINCT(database) FROM su_users where {$userDbConditions}");
        $sql->execute();
        $results = $sql->fetchAll();
        foreach ($results as $user) {
            $sql = $dbhDev->prepare("SELECT 1 from pg_database WHERE datname= '{$user['database']}'");
            $sql->execute();
            $exists = $sql->fetchAll();
            if (!empty($exists)) {
                $output->writeln($user['database'] . '');
                $dbhDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $user['database'] . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
                $sql = $dbhDev->prepare("SELECT table_name FROM information_schema.tables WHERE table_schema='public' and table_name like 'layer_isak_%'");
                $sql->execute();
                $isak_tables = $sql->fetchAll();
                if ($isak_tables) {
                    foreach ($isak_tables as $table_name) {
                        $new_table_name = str_replace('isak', 'for_isak', $table_name['table_name']);
                        $sql = $dbhDev->prepare('CREATE TABLE ' . $new_table_name . ' as TABLE ' . $table_name['table_name']);
                        $sql->execute();
                        $create_res = $sql->fetchAll();
                    }
                }
            }
        }
    }
}
