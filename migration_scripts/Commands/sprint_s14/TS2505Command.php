<?php

namespace TF\Commands\sprint_s14;

use PDO;
use Symfony\Component\Console\Formatter\OutputFormatterStyle;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-2505 command run on all databases.
 */
class TS2505Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s14:TS-2505')
            ->setDescription('pregenerira views za contracts update, kato premahva redaktiranite imoti');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $style = new OutputFormatterStyle('white', 'green');
        $output->getFormatter()->setStyle('success', $style);
        $style = new OutputFormatterStyle('black', 'yellow');
        $output->getFormatter()->setStyle('error', $style);

        $userDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
        $sql = $userDev->prepare(
            "SELECT distinct(oid::regclass::text) as name
            FROM   pg_class
            WHERE  relkind = 'm' and oid::regclass::text like '%kvs_contracts_update%'"
        );

        $sql->execute();
        $views = $sql->fetchAll();
        $names = [];

        if (count($views) > 0) {
            foreach ($views as $view) {
                array_push($names, $view['name']);
            }

            foreach ($names as $name) {
                $ekate = substr($name, -5);
                $tmp_kvs_table = 'layer_tmp_kvs_' . $ekate;
                $viewName = 'kvs_contracts_update_' . $ekate;
                $dropSql = "DROP MATERIALIZED VIEW IF EXISTS {$viewName}";
                $dropCmd = $userDev->prepare($dropSql);
                $dropCmd->execute();

                $createSql = "
                CREATE MATERIALIZED VIEW {$viewName} AS
                     SELECT kvs.gid,
                        kvs.kad_ident,
                        kvs.ekate,
                        c.c_num,
                        c.id AS contract_id,
                        c.nm_usage_rights,
                        to_char(c.due_date, 'DD.MM.YYYY'::text) AS due_date,
                        st_astext(kvs.geom) AS geom,
                        array_agg(DISTINCT (((nkvs.kad_no)::text || ':'::text) || st_astext(nkvs.geom))) AS ngeom,
                        array_agg(DISTINCT (((oldkvs.kad_ident)::text || ':'::text) || st_astext(oldkvs.geom))) AS oldgeom,
                        array_agg(DISTINCT nkvs.kad_no) AS new_kad_idents,
                        array_agg(DISTINCT oldkvs.kad_ident) AS old_kadidents
                       FROM ((((layer_kvs kvs
                         JOIN su_contracts_plots_rel cp ON (((kvs.gid = cp.plot_id) AND ((kvs.ekate)::text = '{$ekate}'::text))))
                         JOIN su_contracts c ON ((cp.contract_id = c.id)))
                         LEFT JOIN {$tmp_kvs_table} nkvs ON (((st_area(st_intersection(kvs.geom, nkvs.geom)) > (50)::double precision) AND ((kvs.ekate)::text = '{$ekate}'::text))))
                         JOIN layer_kvs oldkvs ON (((st_area(st_intersection(oldkvs.geom, nkvs.geom)) > (50)::double precision) AND ((oldkvs.ekate)::text = '{$ekate}'::text))))
                      WHERE ((((((kvs.is_edited = false) AND (oldkvs.is_edited = false)) AND (NOT ((kvs.kad_ident)::text IN ( SELECT {$tmp_kvs_table}.kad_no
                               FROM {$tmp_kvs_table})))) AND (NOT ((oldkvs.kad_ident)::text IN ( SELECT {$tmp_kvs_table}.kad_no
                               FROM {$tmp_kvs_table})))) AND ((kvs.ekate)::text = '{$ekate}'::text)) AND st_isvalid(nkvs.geom))
                      GROUP BY kvs.gid, c.c_num, c.id;
                ";

                $createCmd = $userDev->prepare($createSql);
                $createCmd->execute();
                $output->writeln("<success>{$userDb}: {$viewName} updated</success>");
            }
        } else {
            $output->writeln("<error>{$userDb}: no views to update</error>");
        }
    }
}
