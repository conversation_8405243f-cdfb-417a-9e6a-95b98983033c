ALTER TABLE "public"."su_users_payroll_exports" ALTER COLUMN "status" TYPE varchar;
ALTER TABLE "public"."su_users_payroll_exports" ADD COLUMN "message" text;
DROP TYPE "public"."file_status_enum";
CREATE TYPE "public"."file_status_enum" AS ENUM ('queued', 'processing', 'processed', 'error');
ALTER TYPE "public"."file_status_enum" OWNER TO "postgres";
ALTER TABLE "public"."su_users_payroll_exports" ALTER COLUMN "status" TYPE "public"."file_status_enum" USING status::file_status_enum;