--creates users_payroll_exports table
DROP TYPE IF EXISTS file_status_enum;
CREATE TYPE "public"."file_status_enum" AS ENUM ('queued', 'processing', 'processed');
ALTER TYPE "public"."file_status_enum" OWNER TO "postgres";

DROP TABLE IF EXISTS su_users_payroll_exports;
CREATE TABLE su_users_payroll_exports (
  "id" int4 NOT NULL,
  "user_id" int4 NOT NULL,
  "group_id" int4 NOT NULL,
  "export_date" timestamp(6),
  "filter_params" json,
  "status" file_status_enum,
  "filename" varchar(255),
  "execution_time" int8,
  PRIMARY KEY ("id")
);

ALTER TABLE public.su_users_payroll_exports OWNER TO postgres;

CREATE SEQUENCE su_users_payroll_exports_id_seq
START WITH 1
INCREMENT BY 1
NO MINVALUE
NO MAXVALUE
CACHE 1;

ALTER TABLE public.su_users_payroll_exports_id_seq OWNER TO postgres;
ALTER TABLE "public"."su_users_payroll_exports"
  ALTER COLUMN "id" SET DEFAULT nextval('su_users_payroll_exports_id_seq'::regclass);

ALTER SEQUENCE su_users_payroll_exports_id_seq OWNED BY su_users_payroll_exports.id;

ALTER TABLE "public"."su_users_payroll_exports"
  ADD CONSTRAINT "users_payroll_exports_fk" FOREIGN KEY ("user_id") REFERENCES "public"."su_users" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;
