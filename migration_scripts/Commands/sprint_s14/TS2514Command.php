<?php

namespace TF\Commands\sprint_s14;

use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * TS-2514 command run on all databases.
 */
class TS2514Command extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('sprint_s14:TS-2514')
            ->setDescription('dobavq tablica su_users_payroll_exports');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . DEFAULT_DB_DATABASE . ' -f "Commands/sprint_s14/sql/TS-2514.sql"', $return);
    }
}
