<?php

namespace TF\Commands\sprint_s14;

use PDO;
use Symfony\Component\Console\Formatter\OutputFormatterStyle;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-2534 command run on all databases.
 */
class TS2534Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s14:TS-2534')
            ->setDescription('slaga maximalen razmer na kolonata cropname v tablicite za isak');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $style = new OutputFormatterStyle('white', 'green');
        $output->getFormatter()->setStyle('success', $style);
        $style = new OutputFormatterStyle('black', 'yellow');
        $output->getFormatter()->setStyle('error', $style);

        $userDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = $userDev->prepare(
            "SELECT table_name FROM information_schema.tables WHERE table_name LIKE '%layer_for_isak%'"
        );

        $sql->execute();
        $tables = $sql->fetchAll();
        $names = [];

        if (count($tables) > 0) {
            foreach ($tables as $table) {
                array_push($names, $table['table_name']);
            }

            foreach ($names as $name) {
                $alterSQL = "ALTER TABLE \"public\".\"{$name}\" ALTER COLUMN \"cropname\" TYPE varchar(100) COLLATE \"default\"";
                $alterCMD = $userDev->prepare($alterSQL);
                $alterCMD->execute();
                $dropSQL = "DROP INDEX \"public\".\"{$name}_geom_gist\"";
                $dropCMD = $userDev->prepare($dropSQL);
                $dropCMD->execute();
                $insertSQL = "CREATE INDEX \"{$name}_geom_gist\" ON \"public\".\"{$name}\" USING gist (\"geom\" \"public\".\"gist_geometry_ops_2d\")";
                $insertCMD = $userDev->prepare($insertSQL);
                $insertCMD->execute();

                $output->writeln("<success>{$userDb}: {$name} updated</success>");
            }
        } else {
            $output->writeln("<error>{$userDb}: no views to update</error>");
        }
    }
}
