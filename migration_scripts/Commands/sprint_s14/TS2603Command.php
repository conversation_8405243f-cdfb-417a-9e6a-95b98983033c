<?php

namespace TF\Commands\sprint_s14;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-2603 command run on all databases.
 */
class TS2603Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s14:TS-2603')
            ->setDescription('Dobavq edit_active_from za novite imoti, za koito ima informaciq v layer_kvs_edit_log, no nqma v layer_kvs');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $userDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
        $sql = $userDev->prepare(
            '
                update layer_kvs kvs
                set edit_active_from = ekvs.edit_active_from
                from layer_kvs_edit_log ekvs
                where kvs.gid = ekvs.new_gid
                and kvs.edit_active_from is NULL
            '
        );

        $sql->execute();
        $this->displayErrorMessage($sql->rowCount() . ' plots updated in: ' . $userDb, $output, 'green', 'black');
    }
}
