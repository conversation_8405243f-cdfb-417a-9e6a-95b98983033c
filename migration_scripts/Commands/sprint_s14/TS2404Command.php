<?php

namespace TF\Commands\sprint_s14;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-2404 command run on all databases.
 */
class TS2404Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s14:TS-2404')
            ->setDescription('dobavq kolona "attachment" v su_owners_documents');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-2404.sql', $return);
    }
}
