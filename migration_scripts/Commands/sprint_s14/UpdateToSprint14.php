<?php

namespace TF\Commands\sprint_s14;

use TF\Commands\Common\UserDbCommand;

/**
 * Runs all scripts required for migration from Sprint 13 to Sprint 14.
 */
class UpdateToSprint14 extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s14:update-to-sprint14')
            ->setDescription('Runs all scripts required for migration from Sprint 13 to Sprint 14.');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $commandsArr = [
            // Sprint 13
            new \TF\Commands\sprint_s13\TS2481Command(),
            new \TF\Commands\sprint_s13\TS2320Command(),

            // Sprint 14
            new TS2404Command(),
            new TS2414Command(),
            new TS2505Command(),
            new TS2534Command(),
        ];

        for ($i = 0; $i < count($commandsArr); $i++) {
            $command = $commandsArr[$i];
            $this->logScript($userDb, $command->getName());
            $output->writeln('Executing command: ' . $command->getName());
            $command->onDbExecute($userDb, $output, $input);
        }
    }

    protected function logScript($user_db, $name)
    {
        $date = date('Y-m-d H:i:s');

        $this->openConnection();

        $sql = $this->mainConnection->prepare("
            INSERT INTO scripts_log (db_name, name, date)
            VALUES ('{$user_db}', '{$name}', '{$date}')
        ");

        return $sql->execute();
    }
}
