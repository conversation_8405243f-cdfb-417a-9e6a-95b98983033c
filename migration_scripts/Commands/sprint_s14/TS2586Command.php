<?php

namespace TF\Commands\sprint_s14;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-2586 command run on all databases.
 */
class TS2586Command extends UserDbCommand
{
    protected $skipLogging = false;
    protected $displayIssues = true;
    protected $userDb;
    protected $output;
    protected $counter = 0;

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s14:TS-2586')
            ->setDescription('promenq stoinostite na color i border_color v su_users_layers da syvpadat s tezi ot kolona "style"');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $this->userDb = $userDb;
        $this->output = $output;

        $this->openConnection();

        $cmd = $this->mainConnection->prepare('
            SELECT id from su_users where database = :userDb 
        ');
        $cmd->bindParam(':userDb', $userDb);
        $cmd->execute();
        $results = $cmd->fetchAll();
        $id = $results[0]['id'];

        $layersCmd = $this->mainConnection->prepare("
            SELECT id, color, border_color, style from su_users_layers where group_id = {$id} and layer_type in (1,2,3,4,6,9) and style is not null
        ");

        $layersCmd->execute();
        $layers = $layersCmd->fetchAll();
        $this->counter = 0;
        foreach ($layers as $layer) {
            $layerId = $layer['id'];
            $style = json_decode($layer['style'], true);
            $st_color = $style['color'];
            $st_border = $style['border_color'];

            $color = $layer['color'];
            $border = $layer['border_color'];

            if ($st_color != $color || $st_border != $border) {
                $updateCmd = $this->mainConnection->prepare("
                    UPDATE su_users_layers SET color = '{$st_color}', border_color = '{$st_border}' WHERE id = {$layerId}
                ");
                $updateCmd->execute();
                $this->counter++;
            }
        }
    }

    protected function displayIssues()
    {
        if ($this->counter > 0) {
            $this->displayErrorMessage('TS-2586: ' . $this->userDb . ' -> updated layers: ' . $this->counter, $this->output);
        } else {
            $this->displayErrorMessage('TS-2586: ' . $this->userDb . ' no layers needed update', $this->output, 'green', 'black');
        }
    }
}
