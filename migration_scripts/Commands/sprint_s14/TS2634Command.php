<?php

namespace TF\Commands\sprint_s14;

use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * TS-2634 command run on all databases.
 */
class TS2634Command extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('sprint_s14:TS-2634')
            ->setDescription('dobavq kolona "message" v su_payroll_exports, za da se zapazvat greshki, ako ima takiva');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . DEFAULT_DB_DATABASE . ' -f ' . __DIR__ . '/sql/TS-2634.sql', $return);
    }
}
