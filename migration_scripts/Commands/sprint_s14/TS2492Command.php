<?php

namespace TF\Commands\sprint_s14;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-2492 command run on all databases.
 */
class TS2492Command extends UserDbCommand
{
    protected $skipLogging = true;
    protected $displayIssues = true;
    protected $withIssues = [];

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s14:TS-2492')
            ->setDescription('proverqva za kolona prc_name, prc_uin v tablicite ot isak');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $userDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
        $sql = $userDev->prepare(
            "select c.table_name, string_agg(column_name, ',') as column_name from information_schema.columns c left JOIN information_schema.tables t on c.table_name = t.table_name where
c.table_name like '%layer_isak%' and c.column_name like '%prc_%' GROUP BY c.table_name"
        );

        $sql->execute();
        $tables = $sql->fetchAll();

        if (count($tables) > 0) {
            foreach ($tables as $table => $cols) {
                $columns = explode(',', $cols['column_name']);
                if (count($columns) > 1) {
                    array_push($this->withIssues, $userDb . ' -> ' . $cols['table_name'] . ' -> ' . $cols['column_name']);
                } elseif ('prc_uin' == $columns[0]) {
                    continue;
                } else {
                    array_push($this->withIssues, $userDb . ' -> ' . $cols['table_name'] . ' -> ' . $cols['column_name']);
                }
            }
        }
    }

    protected function displayIssues()
    {
        if (count($this->withIssues)) {
            foreach ($this->withIssues as $issue) {
                $this->output->writeln("<error>{$issue}</error>");
            }
        } else {
            $this->output->writeln('<success>TS-2492: No issues found</success>');
        }
    }
}
