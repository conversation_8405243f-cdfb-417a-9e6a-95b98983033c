<?php

namespace TF\Commands\sprint_s14;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-2414 command run on all databases.
 */
class TS2414Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s14:TS-2414')
            ->setDescription('dobavq kolona "from_sublease" v su_contracts');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-2414.sql', $return);
    }
}
