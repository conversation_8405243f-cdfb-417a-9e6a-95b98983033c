<?php

namespace TF\Commands\sprint_s14;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-2588 command run on all databases.
 */
class TS2588Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s14:TS-2588')
            ->setDescription('dobavq pg_natural_sort extension v potrebitelskite bazi');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $userDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
        $sql = $userDev->prepare(
            'CREATE EXTENSION IF NOT EXISTS pg_natural_sort_order;'
        );

        $sql->execute();
        $this->displayErrorMessage('pg_natural_sort_order extension created for: ' . $userDb, $output, 'green', 'black');
    }
}
