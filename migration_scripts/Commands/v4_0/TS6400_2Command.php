<?php

namespace TF\Commands\v4_0;

use PDO;
use TF\Commands\Common\MainDbCommand;

/**
 * TS-6400 command run on susi_main database.
 */
class TS6400_2Command extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.0:TS-6400_2')
            ->setDescription('Create alpha_numeric_bg collation for susi_main.');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $this->executeSql($pdo, __DIR__ . '/sql/TS-6400.sql');
    }
}
