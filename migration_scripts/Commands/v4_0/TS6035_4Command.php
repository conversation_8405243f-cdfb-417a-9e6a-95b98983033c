<?php

namespace TF\Commands\v4_0;

use PDO;
use TF\Commands\Common\MainDbCommand;

/**
 * TS-6035_4 command run on susi_main database.
 */
class TS6035_4Command extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.0:TS-6035_4')
            ->setDescription('Add validation_error in file_status_enum.');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $this->executeSql($pdo, __DIR__ . '/sql/TS-6035_4.sql');
    }
}
