<?php

namespace TF\Commands\v4_0;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-TS6192_2Command command run on all databases.
 */
class TS6192_2Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.0:TS-6192_2')
            ->setDescription('Insert area, intersection_area and intersection_percent columns in su_coverage_data.');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $sql = $pdo->prepare("SELECT tablename 
                                    FROM pg_catalog.pg_tables 
                                    WHERE 
                                        tablename LIKE 'layer_coverage_%'
                                        AND tablename NOT LIKE '%_intersection' 
                                        AND tablename NOT IN (SELECT RTRIM(i.tablename, '_intersection') FROM pg_catalog.pg_tables i WHERE tablename LIKE '%_intersection');");
        $sql->execute();
        $tables = $sql->fetchAll();

        if (0 === count($tables)) {
            echo "No coverage tables for processing was found\r\n";
        }

        foreach ($tables as $table) {
            $intersectionTable = $table['tablename'] . '_intersection';
            echo 'Creating: ' . $intersectionTable . "\r\n";
            $sql = $pdo->prepare($this->createLayerCoverageIntersectionTable($table['tablename'], $intersectionTable));
            $sql->execute();
            echo 'Creating: ' . $table['tablename'] . "_geom_idx \r\n";
            $sql = $pdo->prepare($this->createGeomIndex($table['tablename']));
            $sql->execute();
            echo 'Creating: ' . $intersectionTable . "_geom_idx \r\n";
            $sql = $pdo->prepare($this->createGeomIndex($intersectionTable));
            $sql->execute();

            $this->fillCoverageDataTableMissingInfo($pdo, $table['tablename'], $intersectionTable);

            echo "===============================\r\n";
        }
    }

    /**
     * @param string $tableName
     * @param string $intersectionTable
     *
     * @return string
     */
    private function createLayerCoverageIntersectionTable($tableName, $intersectionTable)
    {
        return 'CREATE TABLE IF NOT EXISTS ' . $intersectionTable . ' (gid, geom) AS
                SELECT t1.gid, ST_Transform(ST_Intersection(t2.geom, t1.geom), 32635) FROM ' . $tableName . ' t1
                INNER JOIN ' . $tableName . ' t2 ON (ST_IsValid(t1.geom) and ST_IsValid(t2.geom) and ST_Overlaps(t1.geom, t2.geom));';
    }

    /**
     * @param string $tableName
     *
     * @return string
     */
    private function createGeomIndex($tableName)
    {
        return 'CREATE INDEX IF NOT EXISTS ' . $tableName . '_geom_idx ON ' . $tableName . ' USING GIST (geom); ';
    }

    private function fillCoverageDataTableMissingInfo(PDO $pdo, string $tableName, string $intersectionTable)
    {
        $statement = $pdo->query('SELECT round((SUM(St_Area(geom))/1000)::numeric, 3) as area from ' . $tableName . ';');
        $areaResult = $statement->fetch(PDO::FETCH_ASSOC);

        $statement = $pdo->query('SELECT round(((SUM(St_Area(geom))/1000)::numeric / 2), 3) as area from ' . $intersectionTable . ';');
        $overlayAreaResult = $statement->fetch(PDO::FETCH_ASSOC);

        $overlayPercent = number_format($overlayAreaResult['area'] / $areaResult['area'] * 100, 2);

        $statement = $pdo->prepare('UPDATE su_coverage_data SET area = ' . $areaResult['area'] . " WHERE tablename = '" . $tableName . "'");

        $statement->execute();
        $statement = $pdo->prepare('UPDATE su_coverage_data SET overlap_area = ' . $overlayAreaResult['area'] . " WHERE tablename = '" . $tableName . "'");
        $statement->execute();
        $statement = $pdo->prepare('UPDATE su_coverage_data SET overlap_percent = ' . $overlayPercent . " WHERE tablename = '" . $tableName . "'");
        $statement->execute();

        echo 'Table ' . $tableName . ' was update with follow values (area, overlap_area, overlap_percent): ' . $areaResult['area'] . ', ' . $overlayAreaResult['area'] . ', ' . $overlayPercent . "\r\n";
    }
}
