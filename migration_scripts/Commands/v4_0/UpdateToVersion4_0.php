<?php

namespace TF\Commands\v4_0;

use TF\Commands\Common\UserDbCommand;

/**
 * Runs all scripts required for migration from Version 3.1 to 4.0.
 */
class UpdateToVersion4_0 extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.0:update-to-version-4.0')
            ->setDescription('Runs all scripts required for migration from version 3.1 to version 4.0');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $commandsArr = [
            new TS6035_1Command(),
            new TS6035_2Command(),
            new TS6035_3Command(),
            new TS6035_4Command(),
            new TS6089Command(),
            new TS6192_1Command(),
            new TS6192_2Command(),
            new TS6196Command(),
            new TS3721Command(),
            new TS6400_1Command(),
            new TS6400_2Command(),
            new TS6452Command(),
            new TS6514Command(),
            new TS6685Command(),
            new TS6690Command(),
            new TS6719_2Command(),
            new TS4553Command(),
            new GPS2180Command(),
            new GPS2440Command(),
            new TS6781Command(),
            new TS6751Command(),
        ];

        $count = count($commandsArr);
        for ($i = 0; $i < $count; $i++) {
            $command = $commandsArr[$i];
            $this->logScript($userDb, $command->getName());
            $output->writeln('Executing command: ' . $command->getName());
            $command->onDbExecute($userDb, $output, $input);
        }
    }

    protected function logScript($user_db, $name)
    {
        $date = date('Y-m-d H:i:s');

        $this->openConnection();

        $sql = $this->mainConnection->prepare("
            INSERT INTO scripts_log (db_name, name, date)
            VALUES ('{$user_db}', '{$name}', '{$date}')
        ");

        return $sql->execute();
    }
}
