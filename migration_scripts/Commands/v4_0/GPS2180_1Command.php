<?php

namespace TF\Commands\v4_0;

use PDO;
use TF\Commands\Common\MainDbCommand;

/**
 * GPS-2180_1 command run on susi_main database.
 */
class GPS2180_1Command extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.0:GPS2180_1')
            ->setDescription('Update organization_id');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $this->executeSql($pdo, __DIR__ . '/sql/GPS-2180_1.sql');
    }
}
