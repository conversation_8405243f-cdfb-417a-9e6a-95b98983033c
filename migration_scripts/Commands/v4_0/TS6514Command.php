<?php

namespace TF\Commands\v4_0;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-6514 command run on all databases.
 */
class TS6514Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.0:TS-6514')
            ->setDescription('Add missing primary keys.');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $this->executeSql($pdo, __DIR__ . '/sql/TS-6514.sql');
    }
}
