<?php

namespace TF\Commands\v4_0;

use PDO;
use TF\Commands\Common\MainDbCommand;

/**
 * TS-6719 command run on susi_main database.
 */
class TS6719_1Command extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.0:TS-6719_1')
            ->setDescription('Add post payment fields json column to su_users_farming');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $this->executeSql($pdo, __DIR__ . '/sql/TS-6719_1.sql');
    }
}
