<?php

namespace TF\Commands\v4_0;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS2440Command run on all databases.
 *
 * This command will add new column 'slope' to all layer_work tables.
 */
class GPS2440Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.0:GPS-2440')
            ->setDescription('Add new column \'slope\' to all layer_work tables');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $workLayerTablesStmt = $pdo->prepare(
            "SELECT 
                table_name
            FROM
                information_schema.TABLES
            WHERE 
                table_name LIKE 'layer_work_%';"
        );
        $workLayerTablesStmt->execute();
        $workLayerTables = $workLayerTablesStmt->fetchAll();

        foreach ($workLayerTables as $table) {
            $addSlopeColumnStmt = $pdo->prepare("ALTER TABLE {$table['table_name']} ADD COLUMN IF NOT EXISTS slope DOUBLE PRECISION;");
            $addSlopeColumnStmt->execute();
        }
    }
}
