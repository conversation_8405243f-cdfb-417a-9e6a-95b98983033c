<?php

namespace TF\Commands\v4_0;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-TS61921_1Command command run on all databases.
 */
class TS6192_1Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.0:TS-6192_1')
            ->setDescription('Insert area, intersection_area and intersection_percent columns in su_coverage_data.');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $this->executeSql($pdo, __DIR__ . '/sql/TS-6192_1.sql');
    }
}
