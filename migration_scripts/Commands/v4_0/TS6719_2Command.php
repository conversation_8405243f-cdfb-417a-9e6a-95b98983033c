<?php

namespace TF\Commands\v4_0;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-6719 command run on all databases.
 */
class TS6719_2Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.0:TS-6719_2')
            ->setDescription('Add post payment fields json column to su_owners and su_owners_reps and bank_ppayment_type column');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $this->executeSql($pdo, __DIR__ . '/sql/TS-6719_2.sql');
    }
}
