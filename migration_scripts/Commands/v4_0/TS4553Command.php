<?php

namespace TF\Commands\v4_0;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-4553 command run on all databases.
 */
class TS4553Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.0:TS-4553')
            ->setDescription('Add is_signer column in the su_plots_owners_rel tables. The column shows whether this owner is signed the document.');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $this->executeSql($pdo, __DIR__ . '/sql/TS-4553.sql');
    }
}
