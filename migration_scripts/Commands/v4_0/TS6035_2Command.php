<?php

namespace TF\Commands\v4_0;

use PDO;
use TF\Commands\Common\MainDbCommand;

/**
 * TS-6035_2 command run on susi_main database.
 */
class TS6035_2Command extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.0:TS-6035_2')
            ->setDescription('Insert masspayment_type column in su_users_payroll_exports.');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $this->executeSql($pdo, __DIR__ . '/sql/TS-6035_2.sql');
    }
}
