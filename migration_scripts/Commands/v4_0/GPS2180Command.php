<?php

namespace TF\Commands\v4_0;

use PDO;
use TF\Commands\Common\MainDbCommand;

/**
 * GPS-2180 command run on susi_main database.
 */
class GPS2180Command extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.0:GPS2180')
            ->setDescription('Add organization identity number, keycloak_uid, group_id in organization rights');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $this->executeSql($pdo, __DIR__ . '/sql/GPS-2180.sql');
    }
}
