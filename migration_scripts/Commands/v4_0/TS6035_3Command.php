<?php

namespace TF\Commands\v4_0;

use PDO;
use TF\Commands\Common\MainDbCommand;

/**
 * TS-6035_3 command run on susi_main database.
 */
class TS6035_3Command extends MainDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v4.0:TS-6035_3')
            ->setDescription('Add su_banks in susi_main.');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $this->executeSql($pdo, __DIR__ . '/sql/TS-6035_3.sql');
    }
}
