<?php

namespace TF\Commands\sprint_s17;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-3281 command run on all databases.
 */
class TS3281Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s17:TS-3281')
            ->setDescription('update-va nulevi sotinosti na area_for_rent v user_db.su_contracts_plots_rel, nezavisimo ot drugi usloviq');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-3281.sql', $return);
    }
}
