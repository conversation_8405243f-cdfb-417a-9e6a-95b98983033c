<?php

namespace TF\Commands\sprint_s17;

use Prado;
use TF\Commands\Common\UserDbCommand;

Prado::using('Plugins.Core.UserDb.*');
Prado::using('Plugins.Core.UserDb.conf');
Prado::using('Plugins.Core.Users.*');
// \Prado::using('Plugins.Core.Users.conf');
Prado::using('Plugins.Core.UserDbOwners.*');

/**
 * TS-3076 command run on all databases.
 */
class TS3076Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s17:TS-3076')
            ->setDescription('dobavq kolona farming_year v su_collections');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-3076.sql', $return);
    }
}
