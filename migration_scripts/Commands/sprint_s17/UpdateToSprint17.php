<?php

namespace TF\Commands\sprint_s17;

use TF\Commands\Common\UserDbCommand;

/**
 * Runs all scripts required for migration from Sprint 16 to Sprint 17.
 */
class UpdateToSprint17 extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s17:update-to-sprint17')
            ->setDescription('Runs all scripts required for migration from Sprint 16 to Sprint 17.');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $commandsArr = [
            // Sprint 17
            new TS2983Command(),
            new TS2981Command(),
            new TS3228Command(),
            new TS3197Command(),
            new TS3198Command(),
            new TS3307Command(),
            new TS3076Command(),
            new TS3092Command(),
            new TS3198_1_Command(),
        ];

        for ($i = 0; $i < count($commandsArr); $i++) {
            $command = $commandsArr[$i];
            $this->logScript($userDb, $command->getName());
            $output->writeln('Executing command: ' . $command->getName());
            $command->onDbExecute($userDb, $output, $input);
        }
    }

    protected function logScript($user_db, $name)
    {
        $date = date('Y-m-d H:i:s');

        $this->openConnection();

        $sql = $this->mainConnection->prepare("
            INSERT INTO scripts_log (db_name, name, date)
            VALUES ('{$user_db}', '{$name}', '{$date}')
        ");

        return $sql->execute();
    }
}
