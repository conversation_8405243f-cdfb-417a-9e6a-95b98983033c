<?php

namespace TF\Commands\sprint_s17;

use Prado;
use TF\Commands\Common\UserDbCommand;

Prado::using('Plugins.Core.UserDb.*');
Prado::using('Plugins.Core.UserDb.conf');
Prado::using('Plugins.Core.Users.*');
// \Prado::using('Plugins.Core.Users.conf');
Prado::using('Plugins.Core.UserDbOwners.*');

/**
 * TS-3294 command run on all databases.
 */
class TS3294Command extends UserDbCommand
{
    protected $singleRunPerUser = true;

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s17:TS-3294')
            ->setDescription('setvane na from_sublease na slujebni dogovori, na koito tova pole e null (konkretno na FPI)');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-3294.sql', $return);
    }
}
