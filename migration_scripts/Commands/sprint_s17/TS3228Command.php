<?php

namespace TF\Commands\sprint_s17;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-3228 command run on all databases.
 */
class TS3228Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s17:TS-3228')
            ->setDescription('dobavq tablicite su_users_files, su_contracts_files_rel i su_files_deletions_log i iztriva su_contactrs_files. Trqbva da se izpulni i s opciq new_user_db');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-3228.sql', $return);
    }
}
