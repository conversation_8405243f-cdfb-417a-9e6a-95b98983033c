<?php

namespace TF\Commands\sprint_s17;

use TF\Commands\Common\UserDbCommand;

class TS3198_1_Command extends UserDbCommand
{
    public function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-3198_1.sql', $return);
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s17:TS-3198_1')
            ->setDescription('add an extra column to diary_events to set fuel unit measure');
    }
}
