<?php

namespace TF\Commands\sprint_s17;

use PDO;
use Prado;
use <PERSON>ymfony\Component\Console\Input\InputOption;
use TF\Commands\Common\UserDbCommand;

Prado::using('Plugins.Core.UserDb.*');
Prado::using('Plugins.Core.UserDb.conf');
Prado::using('Plugins.Core.Users.*');
Prado::using('Plugins.Core.Users.conf');
Prado::using('Plugins.Core.UserDbOwners.*');

/**
 * TS-3305 command run on all databases.
 */
class TS3305Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s17:TS-3305')
            ->setDescription('Migration script za dobavqne na vremenni koloni old_wialon_id i should_be_removed vav su_diary_configs')
            ->addOption('update', null, InputOption::VALUE_OPTIONAL, 'update');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $userDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
        $sql = $userDev->prepare(
            '	select
                    kvs.kad_ident,
                    c.id contract_id,
                    c.c_num contract_name,
                    pf.id as pf_rel_id
                from
                    su_contracts c
                    inner join su_contracts_plots_rel cpr on(cpr.contract_id = c.id)
                    inner join layer_kvs kvs on(cpr.plot_id = kvs.gid)
                    inner join su_plots_farming_rel pf on(pf.pc_rel_id = cpr.id)
                where
                  pf.farming_id = c.farming_id
                 AND c.nm_usage_rights <> 1'
        );

        $sql->execute();
        $records = $sql->fetchAll();

        $output->writeln('Account command: ' . $userDb);
        foreach ($records as $record) {
            $output->writeln('Plot ident: ' . $record['kad_ident']);
            $output->writeln('Contract id: ' . $record['contract_id']);
            $output->writeln('Contract name: ' . $record['contract_name']);
            $output->writeln('----------');

            if ($input->getOption('update')) {
                $this->deletePlotsFarmings($record, $userDev);
                $output->writeln('updated');
                $output->writeln('----------');
            }
        }
    }

    private function deletePlotsFarmings($record, $userDev)
    {
        $sql = $userDev->prepare(' DELETE FROM  su_plots_farming_rel WHERE id = ' . (int)$record['pf_rel_id'] . '');
        $sql->execute();
    }
}
