-- setvane na from_sublease na slujebni dogovori, na koito tova pole e null (konkretno na FPI)

CREATE MATERIALIZED VIEW service_contracts_to_be_updated AS
SELECT
	c.id, c.c_num, c.nm_usage_rights, c.start_date, c.due_date, c.farming_id,  
    c1.id as contract_to_update_id, c1.c_num as c_num1, c1.nm_usage_rights as nm_usage_rights1, c1.start_date as start_date1, c1.due_date as due_date1, c1.farming_id as farming_id1
    FROM
        su_contracts C
        JOIN su_contracts c1 ON (
            C .start_date = c1.start_date
        AND C .due_date = c1.due_date
        AND C . ID = c1. ID - 1
        AND C .is_sublease = TRUE
        AND c1.from_sublease IS NULL
        AND c1.is_sublease = FALSE
        and c.farming_id <> c1.farming_id
        and c.c_num = c1.c_num
    )
    ORDER BY c.farming_id;




update su_contracts set from_sublease = id-1  
where id in (
    SELECT
	--c.id, c.c_num, c.nm_usage_rights, c.start_date, c.due_date, c.farming_id,  c1.c_num, c1.nm_usage_rights, c1.start_date, c1.due_date, c1.farming_id
    c1.id
    FROM
        su_contracts C
        JOIN su_contracts c1 ON (
            C .start_date = c1.start_date
        AND C .due_date = c1.due_date
        AND C . ID = c1. ID - 1
        AND C .is_sublease = TRUE
        AND c1.from_sublease IS NULL
        AND c1.is_sublease = FALSE
        and c.farming_id <> c1.farming_id
        and c.c_num = c1.c_num
    )
    ORDER BY c.farming_id
);
