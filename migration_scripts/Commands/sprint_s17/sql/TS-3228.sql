
-- ----------------------------
-- Table structure for su_user_files
-- ----------------------------
CREATE TABLE "public"."su_user_files" (
"id" int4 NOT NULL,
"filename" varchar(255) COLLATE "default",
"date" timestamp(6),
"user_id" int4,
"group_id" int4
)
WITH (OIDS=FALSE)

;

-- ----------------------------
-- Alter Sequences Owned By 
-- ----------------------------

-- ----------------------------
-- Primary Key structure for table su_user_files
-- ----------------------------
ALTER TABLE "public"."su_user_files" ADD PRIMARY KEY ("id");

CREATE SEQUENCE su_user_files_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE su_user_files_id_seq OWNER TO postgres;

--
-- Name: su_user_files_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE su_user_files_id_seq OWNED BY su_user_files.id;


--
-- Name: id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY su_user_files ALTER COLUMN id SET DEFAULT nextval('su_user_files_id_seq'::regclass);


-- ----------------------------
-- Table structure for su_contracts_files_rel
-- ----------------------------
CREATE TABLE "public"."su_contracts_files_rel" (
"file_id" int4 NOT NULL,
"contract_id" int4 NOT NULL
)
WITH (OIDS=FALSE)

;

-- ----------------------------
-- Alter Sequences
-- ----------------------------

-- ----------------------------
-- Foreign Key structure for table "public"."su_contracts_files_rel"
-- ----------------------------
ALTER TABLE "public"."su_contracts_files_rel" ADD FOREIGN KEY ("contract_id") REFERENCES "public"."su_contracts" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;
ALTER TABLE "public"."su_contracts_files_rel" ADD FOREIGN KEY ("file_id") REFERENCES "public"."su_user_files" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;



--insert data from su_contracts_files
INSERT INTO su_user_files (SELECT id, filename, date, user_id, group_id FROM "public"."su_contracts_files" GROUP BY id);
INSERT INTO su_contracts_files_rel (SELECT id, contract_id FROM su_contracts_files);


--drop the su_contracts_files table

DROP TABLE IF EXISTS su_contracts_files;





--add file_deletions_log_table
DROP TABLE IF EXISTS "public"."su_files_deletions_log";
CREATE TABLE "public"."su_files_deletions_log" (
"id" int4 NOT NULL,
"ip" varchar(255) COLLATE "default",
"time_of_deletion" timestamp(6),
"user_id" int4,
"is_hard_deletion" boolean,
"file_id" int4,
"contract_id" int4
)
WITH (OIDS=FALSE)

;

-- ----------------------------
-- Alter Sequences Owned By 
-- ----------------------------

-- ----------------------------
-- Primary Key structure for table su_files_deletions_log
-- ----------------------------
ALTER TABLE "public"."su_files_deletions_log" ADD PRIMARY KEY ("id");

CREATE SEQUENCE su_files_deletions_log_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE su_files_deletions_log_id_seq OWNER TO postgres;

--
-- Name: su_files_deletions_log_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE su_files_deletions_log_id_seq OWNED BY su_files_deletions_log.id;


--
-- Name: id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY su_files_deletions_log ALTER COLUMN id SET DEFAULT nextval('su_files_deletions_log_id_seq'::regclass);
