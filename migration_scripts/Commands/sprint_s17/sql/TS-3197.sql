-- ----------------------------
-- Table structure for su_diary_treatments_products
-- ----------------------------
CREATE TABLE IF NOT EXISTS "public"."su_diary_treatments_products" (
  "id" serial NOT NULL primary key,
  "event_id" int4 DEFAULT nextval('su_diary_treatments_products_id_seq'::regclass) NOT NULL,
  "substance_id" int4,
  "substance_technic_id" int4,
  "substance_unit_type" int4,
  "substance_dose" float8,
  "treated_area" float8,
  "tratment_cost" float8,
  "substance_unit_price" float8,
  "price_per_area" float8,
  "is_substance_treatment" bool DEFAULT false NOT NULL,
  "quarantine_period" int4,
  "pest_name" varchar(100) COLLATE "default"
)
WITH (OIDS=FALSE);

-- ----------------------------
-- Alter Sequences Owned By
-- ----------------------------

-- ----------------------------
-- Primary Key structure for table su_diary_treatments_products
-- ----------------------------
ALTER TABLE "public"."su_diary_treatments_products"
  ADD CONSTRAINT "treatments__on_event_id_fk" FOREIGN KEY ("event_id") REFERENCES "public"."su_diary_events" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;
