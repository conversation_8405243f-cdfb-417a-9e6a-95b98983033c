<?php

namespace TF\Commands\sprint_s17;

use Prado;
use TF\Commands\Common\UserDbCommand;

Prado::using('Plugins.Core.UserDb.*');
Prado::using('Plugins.Core.UserDb.conf');
Prado::using('Plugins.Core.Users.*');
// \Prado::using('Plugins.Core.Users.conf');
Prado::using('Plugins.Core.UserDbOwners.*');

/**
 * TS-3290 command run on all databases.
 */
class TS3290Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s17:TS-3290')
            ->setDescription('promqna na is_edited poleto v layer_kvs');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-3290.sql', $return);
    }
}
