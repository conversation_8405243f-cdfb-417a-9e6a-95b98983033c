<?php

namespace TF\Commands\sprint_s17;

use TF\Commands\Common\UserDbCommand;

class TS3198Command extends UserDbCommand
{
    public function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-3198.sql', $return);

        return $return;
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s17:TS-3198')
            ->setDescription('this migration will insert into diary_config new units (kg, liters) record , new config_type number will be 10');
    }
}
