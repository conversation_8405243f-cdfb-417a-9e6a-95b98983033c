<?php

namespace TF\Commands\sprint_s17;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-3307 command run on all databases.
 */
class TS3307Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s17:TS-3307')
            ->setDescription('update-va nulevi sotinosti na document_area = st_area(geom)/1000');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-3307.sql', $return);
    }
}
