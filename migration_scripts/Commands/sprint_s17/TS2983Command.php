<?php

namespace TF\Commands\sprint_s17;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-2983 command run on all databases.
 */
class TS2983Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s17:TS-2983')
            ->setDescription('dobavq kolona rent_per_plot v su_sales_contracts_plots_rel');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-2983.sql', $return);
    }
}
