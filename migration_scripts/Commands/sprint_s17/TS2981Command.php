<?php

namespace TF\Commands\sprint_s17;

use Prado;
use TF\Commands\Common\UserDbCommand;
use UserDbOwnersController;

Prado::using('Plugins.Core.UserDb.*');
Prado::using('Plugins.Core.UserDb.conf');
Prado::using('Plugins.Core.Users.*');
Prado::using('Plugins.Core.Users.conf');
Prado::using('Plugins.Core.UserDbOwners.*');

/**
 * TS-2981 command run on all databases.
 */
class TS2981Command extends UserDbCommand
{
    protected $singleRunPerUser = true;

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s17:TS-2981')
            ->setDescription('dobavq kolona is_set_manual v su_contracts_plots_rel i obnovqva su_contracts_plots_rel');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-2981.sql', $return);

        $userDbOwnersController = new UserDbOwnersController($userDb);

        $plotsOwners = $userDbOwnersController->getPlotsOwnres();

        foreach ($plotsOwners as $plotsOwner) {
            $userDbOwnersController->buildHeritorsTree(
                $plotsOwner['owner_id'] . '.*{1}',
                $plotsOwner['percent'],
                $plotsOwner['numerator'],
                $plotsOwner['denominator'],
                1,
                $plotsOwner['fakeid'],
                $plotsOwner['pc_rel_id'],
                true
            );
        }
    }
}
