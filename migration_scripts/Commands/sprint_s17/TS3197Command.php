<?php

namespace TF\Commands\sprint_s17;

use TF\Commands\Common\UserDbCommand;

class TS3197Command extends UserDbCommand
{
    public function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-3197.sql', $return);

        return $return;
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s17:TS-3197')
            ->setDescription('create new table diary_treatments_products ,separated from diary_events');
    }
}
