<?php

namespace TF\Commands\sprint_s16;

use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * TS-2987 command run on all databases.
 */
class TS2987Command extends BaseCommand
{
    protected function configure()
    {
        $this
            ->setName('sprint_s16:TS-2987')
            ->setDescription('dobavq kolona "mol_egn" v susi_main.su_users_farming');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . DEFAULT_DB_DATABASE . ' -f ' . __DIR__ . '/sql/TS-2987.sql', $return);
    }
}
