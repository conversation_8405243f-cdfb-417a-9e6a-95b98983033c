<?php

namespace TF\Commands\sprint_s16;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-2992 command run on all databases.
 */
class TS2992Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s16:TS-2992')
            ->setDescription('dobavq kolona "comment" v user_db.su_subleases_plots_area');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-2992.sql', $return);
    }
}
