<?php

namespace TF\Commands\sprint_s16;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-3133 command run on all databases.
 */
class TS3133Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s16:TS-3133')
            ->setDescription('dobavq kolona "area_for_rent" v user_db.su_contracts_plots_rel i obnovqva materializiranite viuta da izpolzvat tazi kolona vmesto contract_area');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-3133.sql', $return);
    }
}
