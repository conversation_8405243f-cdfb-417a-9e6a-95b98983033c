<?php

namespace TF\Commands\sprint_s16;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * TS-3186 command run on all databases.
 */
class TS3186ReportCommand extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s16:TS-3186Report')
            ->setDescription('spravka za nulevi sotinosti na contract_area i area_for_rent ');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $userDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDb . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = $userDev->prepare(
            'SELECT
                (
                    CASE
                    WHEN document_area = 0 THEN
                        round((st_area(geom) / 1000) :: NUMERIC,3)
                    ELSE
                        document_area
                    END
                ) AS update_area,
                allowable_area,
                contract_area,
                document_area,
                area_for_rent,
                contract_id,
                cprel.id as contrac_plot_rel_id
            FROM
                su_contracts_plots_rel cprel
            JOIN layer_kvs ON cprel.plot_id = layer_kvs.gid
            WHERE
                contract_area = 0
                and area_for_rent = 0
            ORDER BY
            contract_id'
        );

        $sql->execute();
        $records = $sql->fetchAll();

        $sql = $userDev->prepare(
            'SELECT
                (
                    CASE
                    WHEN document_area = 0 THEN
                        round((st_area(geom) / 1000) :: NUMERIC,3)
                    ELSE
                        document_area
                    END
                ) AS update_area,
                allowable_area,
                contract_area,
                document_area,
                area_for_rent,
                contract_id,
                cprel.id as contrac_plot_rel_id
            FROM
                su_contracts_plots_rel cprel
            JOIN layer_kvs ON cprel.plot_id = layer_kvs.gid
            WHERE
             area_for_rent = 0
            ORDER BY
            contract_id'
        );

        $sql->execute();
        $ZeroAreaForRentRecords = $sql->fetchAll();

        if (!file_exists(__DIR__ . '/report1.csv')) {
            @mkdir($this->userDir, 0777);
            $file = fopen(__DIR__ . '/report1.csv', 'a');
            fputcsv(
                $file,
                ['userDb', 'Plots with zero contractArea', 'Plots with zero area_for_rent']
            );
        } else {
            $file = fopen(__DIR__ . '/report1.csv', 'a');
        }

        $writeArray = [
            $userDb,
            count($records),
            count($ZeroAreaForRentRecords),
        ];

        fputcsv($file, $writeArray);

        fclose($file);
    }
}
