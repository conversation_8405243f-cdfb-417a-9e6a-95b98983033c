<?php
/**
 * Created by PhpStorm.
 * User: User
 * Date: 5/12/2017
 * Time: 10:14 AM.
 */

namespace TF\Commands\sprint_s16;

use PHPExcel_IOFactory;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class TS3065Command extends BaseCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s16:TS-3065')
            ->setDescription('generate table susi_main.su_users_salesman , update salesman_id column on su_users');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        // MIGRATION
        $output->writeln('** Start of script **');
        $output->writeln('creating tables: temp_su_users_salesman and su_users_salesman. File : TS-3065.sql');
        $command = 'psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . DEFAULT_DB_DATABASE . ' -f ' . __DIR__ . '/sql/TS-3065.sql';
        system($command, $return);

        $temp_file = __DIR__ . '/sql/TS-3065_INSERT.sql'; // temporary file to hold php generate sql string, which are passed to the command line

        // READ EXCEL FILE DATA
        $output->writeln('reading excel file');
        $objPHPExcel = PHPExcel_IOFactory::load(__DIR__ . '/sql/users.xlsx');
        $activeSheet = $objPHPExcel->getActiveSheet();
        $data = $activeSheet->toArray();
        // UNIQUE SALES PERSON: TABLE su_users_salesman
        $sales_persons = array_unique(array_column($activeSheet->rangeToArray('A2:A' . count($data)), 0));
        unset($data[0]); // remove first row from excel file
        $sql = $this->tempInsertSql($data);
        file_put_contents($temp_file, $sql . PHP_EOL, LOCK_EX);
        $output->writeln('seeding table temp_su_users_salesman...');
        $command = 'psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . DEFAULT_DB_DATABASE . ' -f ' . $temp_file;
        system($command, $return);
        $output->writeln('table temp_su_users_salesman seeded');

        $output->writeln('seeding table su_users_salesman...');
        $sql = $this->SalesInsertSql($sales_persons);
        file_put_contents($temp_file, $sql . PHP_EOL, LOCK_EX);
        $command = 'psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . DEFAULT_DB_DATABASE . ' -f ' . $temp_file;
        system($command, $return);
        $output->writeln('table su_users_salesman seeded');

        $output->writeln('running update query on su_user_table');
        $sql = 'UPDATE su_users AS su SET salesperson_id = sus.id FROM temp_su_users_salesman AS tus , su_users_salesman AS sus WHERE su.username = tus.user_name AND tus.sales_person = sus.name;';
        $command = 'psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . DEFAULT_DB_DATABASE . ' -c "' . $sql . '"';
        system($command, $return);

        $output->writeln('su_user_table update successfully');
        $output->writeln('deleting temporary file..');
        $deleted = unlink($temp_file);
        $output->writeln($deleted ? 'temporary file deleted' : 'Cannot delete temporary file : ' . $temp_file);

        $output->writeln('updating salesperson_id based on parental account');
        $sql = 'UPDATE su_users SET salesperson_id = x.salesperson_id FROM ( SELECT DISTINCT group_id , salesperson_id FROM su_users WHERE salesperson_id IS NOT NULL) x WHERE x.group_id = su_users.group_id; ';
        $command = 'psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . DEFAULT_DB_DATABASE . ' -c "' . $sql . '"';
        system($command, $return);

        $output->writeln('removing temporary table temp_su_users_salesman..');
        $sql = 'DROP TABLE IF EXISTS public.temp_su_users_salesman;';
        $command = 'psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . DEFAULT_DB_DATABASE . ' -c "' . $sql . '"';
        system($command, $return);
        $output->writeln($return ? 'Error in removing temp_su_users_salesman ' : 'temp_su_users_salesman removed. Exit code: ' . $return);
        $output->writeln('** End of script **');
    }

    private function SalesInsertSql($data)
    {
        $data = array_values($data);
        $sql = 'INSERT INTO public.su_users_salesman (id, name) VALUES ';
        foreach ($data as $index => $item) {
            $sql .= " ('" . ($index + 1) . "' , '" . $item . "'),";
        }

        return substr($sql, 0, -1) . ';';
    }

    private function tempInsertSql($data)
    {
        $sql = 'INSERT INTO public.temp_su_users_salesman (sales_person, user_name) VALUES ';
        foreach ($data as $index => $item) {
            $sql .= " ('" . $item[0] . "' , '" . $item[1] . "'),";
        }

        return substr($sql, 0, -1) . ';';
    }
}
