<?php

namespace TF\Commands\sprint_s16;

use TF\Commands\Common\UserDbCommand;

/**
 * Runs all scripts required for migration from Sprint 14 to Sprint 16.
 */
class UpdateToSprint16 extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s16:update-to-sprint16')
            ->setDescription('Runs all scripts required for migration from Sprint 15_1 to Sprint 16.');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        $commandsArr = [
            // Sprint 16
            new TS2992Command(),
            new TS2988Command(),
            new TS3133Command(),
            new TS3132Command(),
            new TS3092Command(),
        ];

        for ($i = 0; $i < count($commandsArr); $i++) {
            $command = $commandsArr[$i];
            $this->logScript($userDb, $command->getName());
            $output->writeln('Executing command: ' . $command->getName());
            $command->onDbExecute($userDb, $output, $input);
        }
    }

    protected function logScript($user_db, $name)
    {
        $date = date('Y-m-d H:i:s');

        $this->openConnection();

        $sql = $this->mainConnection->prepare("
            INSERT INTO scripts_log (db_name, name, date)
            VALUES ('{$user_db}', '{$name}', '{$date}')
        ");

        return $sql->execute();
    }
}
