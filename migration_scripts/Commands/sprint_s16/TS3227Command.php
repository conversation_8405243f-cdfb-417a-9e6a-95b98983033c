<?php

namespace TF\Commands\sprint_s16;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-3227 command run on all databases.
 */
class TS3227Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s16:TS-3227')
            ->setDescription('update-va stoinostta na kolona area_for_rent pri dogovori za savmestna obrabotka');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-3227.sql', $return);
    }
}
