<?php

namespace TF\Commands\sprint_s16;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-3186 command run on all databases.
 */
class TS3186Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s16:TS-3186')
            ->setDescription('update-va nulevi sotinosti na contract_area i area_for_rent v user_db.su_contracts_plots_rel');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        // when displaying contract area in the contracts-plots table if the contract_area is 0 we show document_area. If the
        // document area is also 0 we show the st_area(geom). So when you multiedit contracts to make the area_for_rent equal to
        // the allowable area and the actual contract_area is 0 it sets the contract_area as area_for_rent

        // for contracts_plots_rels where contract_area = 0 and area_for_rent = 0 set area_for_rent = document_area
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-3186_1.sql', $return);

        // for contracts_plots_rels where contract_area = 0 set contract_area = document_area or st_area(geom) (if document_area is 0)
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-3186_2.sql', $return);
    }
}
