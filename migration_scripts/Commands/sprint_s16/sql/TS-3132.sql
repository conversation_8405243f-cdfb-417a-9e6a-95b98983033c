-- adding 'allowable_area' and 'allowable_type' columns, 
-- representing the intersection area of geometries in layer_kvs and allowable_layer_final
ALTER TABLE "public"."layer_kvs" ADD COLUMN "allowable_area" float4;
ALTER TABLE "public"."layer_kvs" ADD COLUMN "allowable_type" varchar(255);


--adding safe_intersection function to deal with "Error performing intersection: TopologyException: no outgoing dirEdge found"

CREATE OR REPLACE FUNCTION safe_intersection(geom_a geometry, geom_b geometry)
RETURNS geometry AS
$$
BEGIN
    RETURN ST_Intersection(geom_a, geom_b);
    EXCEPTION
        WHEN OTHERS THEN
            BEGIN
                RETURN ST_Intersection(<PERSON>_Buffer(geom_a, 0.0000001), ST_Buffer(geom_b, 0.0000001));
                EXCEPTION
                    WHEN OTHERS THEN
                        RETURN ST_GeomFromText('POLYGON EMPTY');
    END;
END
$$
LANGUAGE 'plpgsql' STABLE STRICT;


--setting the column values

UPDATE layer_kvs SET allowable_area= allowable_intersection.allowable_area, allowable_type=allowable_intersection.allowable_type
FROM (
SELECT
    kvs.gid,
    string_agg (DISTINCT(A .ntp) :: TEXT, ', ') AS allowable_type,
    round(
        (
            st_area (
                safe_intersection (kvs.geom, st_union(A .geom))
            ) / 1000
        ) :: NUMERIC,
        3
    ) AS allowable_area
FROM
    layer_kvs kvs,
    dblink (
        'host=127.0.0.1 port=5432 dbname=susi_main user=postgres password=6nuk23' :: TEXT,
        'SELECT
            geom,
            ntp
        FROM
            layer_allowable_final where geom && ST_GeomFromText(''' || (
            SELECT
                st_astext (
                    st_envelope (st_extent(geom))
                )
            FROM
                layer_kvs
        ) || ''');'
    ) AS A (geom geometry, ntp VARCHAR)
WHERE
st_intersects (kvs.geom, A .geom)
GROUP BY
    kvs.gid
) allowable_intersection WHERE layer_kvs.gid = allowable_intersection.gid