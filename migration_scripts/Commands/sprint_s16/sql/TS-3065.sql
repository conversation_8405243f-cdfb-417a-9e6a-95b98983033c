
SET client_encoding = 'UTF8';

/** CREATE TEMPORARY TABLE */

\echo 'CREATING temp_su_users_salesman TABLE'
DROP TABLE IF EXISTS public.temp_su_users_salesman;

CREATE TABLE IF NOT EXISTS public.temp_su_users_salesman (
  "sales_person"  VARCHAR(100) NOT NULL,
  "user_name" VARCHAR(100) NOT NULL
);
\echo 'TABLE temp_su_users_salesman CREATED'
/** CREATE SALES TABLE */

\echo 'CREATING temp_su_users_salesman TABLE'
DROP TABLE IF EXISTS public.su_users_salesman;

CREATE TABLE IF NOT EXISTS public.su_users_salesman (
  "id"   INT4         NOT NULL,
  "name" VARCHAR(100) NOT NULL,
  PRIMARY KEY ("id")
)
WITH (
OIDS = FALSE
);

\echo 'TABLE su_users_salesman CREATED'
/* ALTER THE USER TABLE TO RECEIVE SALES PERSON ID, FOR NOW WE CREATE A COLUMN FOR THAT*/

\echo 'ABOUT TO ADD NEW COLUMN salesperson_id ON su_users TABLE'
ALTER TABLE "public"."su_users"  DROP COLUMN IF EXISTS "salesperson_id";
ALTER TABLE "public"."su_users"  ADD COLUMN "salesperson_id" INT4;
\echo 'NEW COLUMN su_users.salesperson_id ADDED'
\echo 'SCRIPT EXECUTED SUCCESSFULLY!'