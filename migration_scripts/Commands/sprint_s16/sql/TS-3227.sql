DROP TRIGGER IF EXISTS "SU_CONTRACTS_PLOTS_REL" on "public"."su_contracts_plots_rel";

UPDATE su_contracts_plots_rel
SET area_for_rent = contract_area
WHERE
    ID IN (
        SELECT
            su_contracts_plots_rel. ID
        FROM
            "public"."su_contracts"
        JOIN su_contracts_plots_rel ON su_contracts. ID = contract_id
        WHERE
            nm_usage_rights = 5
        AND contract_area <> area_for_rent
    );


CREATE TRIGGER "SU_CONTRACTS_PLOTS_REL" 
    AFTER INSERT OR UPDATE OF "id", "contract_id", "plot_id", "contract_area", "area_for_rent", "price_per_acre", "price_sum", "annex_action" 
    OR DELETE ON "public"."su_contracts_plots_rel"
FOR EACH STATEMENT
EXECUTE PROCEDURE "refresh_rentas_materialized_views"();


REFRESH MATERIALIZED VIEW charged_rentas_mat_view;
REFRESH MATERIALIZED VIEW charged_rentas_annexes_mat_view;
REFRESH MATERIALIZED VIEW renta_nats_mat_view;
REFRESH MATERIALIZED VIEW renta_nats_annexes_mat_view;
