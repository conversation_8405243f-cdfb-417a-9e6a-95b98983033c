--setva plosht za renta na imoti splosht po dogovor = 0

DROP TRIGGER IF EXISTS "SU_CONTRACTS_PLOTS_REL" on "public"."su_contracts_plots_rel";

UPDATE su_contracts_plots_rel d
    SET area_for_rent = C .update_area FROM (
        SELECT
        (
            CASE
            WHEN document_area = 0 THEN
                round((st_area(geom) / 1000) :: NUMERIC,3)
            ELSE
                document_area
            END
        ) AS update_area,
        allowable_area,
        contract_area,
        document_area,
        area_for_rent,
        cprel.id
    FROM
        "public"."su_contracts_plots_rel" cprel
    JOIN layer_kvs ON cprel.plot_id = layer_kvs.gid
    WHERE
        contract_area = 0
        and area_for_rent = 0
    ORDER BY
    contract_id
    ) C
    WHERE
        C.ID = d.id;

CREATE TRIGGER "SU_CONTRACTS_PLOTS_REL" 
    AFTER INSERT OR UPDATE OF "id", "contract_id", "plot_id", "contract_area", "area_for_rent", "price_per_acre", "price_sum", "annex_action" 
    OR DELETE ON "public"."su_contracts_plots_rel"
FOR EACH STATEMENT
EXECUTE PROCEDURE "refresh_rentas_materialized_views"();


REFRESH MATERIALIZED VIEW charged_rentas_mat_view;
REFRESH MATERIALIZED VIEW charged_rentas_annexes_mat_view;
REFRESH MATERIALIZED VIEW renta_nats_mat_view;
REFRESH MATERIALIZED VIEW renta_nats_annexes_mat_view;