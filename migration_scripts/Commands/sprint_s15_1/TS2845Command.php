<?php

namespace TF\Commands\sprint_s15_1;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-2845 command run on all databases.
 */
class TS2845Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s15_1:TS-2845')
            ->setDescription('dobavq kolona "remark" v su_owners');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-2845.sql', $return);
    }
}
