<?php

namespace TF\Commands\sprint_s15_1;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-2844 command run on all databases.
 */
class TS2844Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s15_1:TS-2844')
            ->setDescription('dobavq koloni "osz_date" i "osz_num" v su_contracts');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-2844.sql', $return);
    }
}
