<?php

namespace TF\Commands\sprint_s15_1;

use TF\Commands\Common\UserDbCommand;

/**
 * TS-2797 command run on all databases.
 */
class TS2797Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('sprint_s15_1:TS-2797')
            ->setDescription('dobavq kolona "iban" v su_owners_reps');
    }

    protected function onDbExecute($userDb, $output, $input)
    {
        system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $userDb . ' -f ' . __DIR__ . '/sql/TS-2797.sql', $return);
    }
}
