<?php
/**
 * <AUTHOR>
 *
 * Обновява всички СЕПП view-та, ако има таблици вместо view-та
 * и PostgreSQL верисията е 9.3 или по-нова изтрива таблицата и създава view.
 */
require_once '../config/global.config.php';

class SEPPViewUpdate
{
    public const REL_TYPE_VIEW = 'm';
    public const REL_TYPE_TABLE = 'r';

    public function __construct($argument)
    {
        # code...
    }

    public function run()
    {
        $dbhDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

        $sql = $dbhDev->prepare(
            'SELECT
				u."database",
				ul."id" AS layer_id
			FROM
				su_users u
			INNER JOIN su_users_layers ul ON u."id" = ul.user_id
			WHERE
				u."level" = 2
			AND ul.layer_type = 9
			ORDER BY
				u."database"'
        );

        $sql->execute();
        $results = $sql->fetchAll();

        for ($i = 0; $i < count($results); $i++) {
            $database = $results[$i]['database'];
            $layerId = $results[$i]['layer_id'];
            $seppViewName = 'sepp_for_isak_' . $layerId;
            if (!$relationKind) {
                continue;
            }

            echo "Database {$database} \n";
            $relationKind = $this->getRelationKind($seppViewName, $database);
            if (self::REL_TYPE_VIEW == $relationKind) {
                echo 'View: ';
                echo $seppViewName;
                echo "\n";
                $output = [];
                $refreshViewSql = "REFRESH MATERIALIZED VIEW {$seppViewName}";
                exec('psql -U postgres -d ' . $database . ' -c "' . $refreshViewSql . '" 2>&1', $output, $return);
                var_dump($output);
            }

            if (self::REL_TYPE_TABLE == $relationKind && DEFAULT_DB_VERSION >= 9.3) {
                echo 'Table: ';
                echo $seppViewName;
                echo "\n";
                $dropTableSql = "DROP TABLE IF EXISTS {$seppViewName};";
                $output = [];
                exec('psql -U postgres -d ' . $database . ' -c "' . $dropTableSql . '" 2>&1', $output, $return);
                var_dump($output);
                $this->createSEPPReportView($layerId, $database);
            }
        }
    }

    private function getRelationKind($seppViewName, $database)
    {
        $query = "SELECT relkind FROM pg_class WHERE relname = '{$seppViewName}'";
        $return = [];
        $output = [];

        exec('psql -P t -P format=unaligned -U postgres -d ' . $database . ' -c "' . $query . '" 2>&1', $output, $return);

        if (0 == count($output)) {
            return;
        }

        return $output[0];
    }

    private function createSEPPReportView($layerId, $database)
    {
        $output = [];
        $return = [];
        $viewName = 'sepp_for_isak_' . $layerId;
        $sqlView = "CREATE MATERIALIZED VIEW \"{$viewName}\"
	        AS 
	         SELECT a.geom 
	           FROM dblink('host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "'::text, 'SELECT
	            geom
	        FROM
	            layer_allowable_final,
	            (
	                SELECT
	                    A [ 1 ]:: NUMERIC AS xmin,
	                    A [ 2 ]:: NUMERIC AS ymin,
	                    A [ 3 ]:: NUMERIC AS xmax,
	                    A [ 4 ]:: NUMERIC AS ymax
	                FROM
	                    (
	                        SELECT
	                            regexp_split_to_array(
	                                (
	                                    SELECT
	                                        extent
	                                    FROM
	                                        su_users_layers
	                                    WHERE
	                                        ID = {$layerId}
	                                ),
	                                '' ''
	                            )
	                    ) AS dt (A)
	            ) AS extent
	        WHERE
	            geom && st_makeenvelope (
	                extent.xmin,
	                extent.ymin,
	                extent.xmax,
	                extent.ymax,
	                32635
	            )'::text) a(geom geometry);";
        exec('psql -U postgres -d ' . $database . ' -c "' . $sqlView . '" 2>&1', $output, $return);
        var_dump($output);
        $sqlOwner = "ALTER MATERIALIZED VIEW \"{$viewName}\" OWNER TO \"postgres\";";
        exec('psql -U postgres -d ' . $database . ' -c "' . $sqlOwner . '" 2>&1', $output, $return);
        var_dump($output);

        $sqlIndex = "CREATE INDEX \"{$viewName}_gis\" ON \"public\".\"{$viewName}\" USING gist (geom \"public\".\"gist_geometry_ops_2d\");";
        exec('psql -U postgres -d ' . $database . ' -c "' . $sqlIndex . '" 2>&1', $output, $return);
        var_dump($output);
    }
}
