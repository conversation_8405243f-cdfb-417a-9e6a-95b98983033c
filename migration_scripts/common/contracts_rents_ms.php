<?php

require_once '../config/global.config.php';

if ($_SERVER['HTTP_USER_AGENT']) {
    die;
}

$conn = pg_pconnect('user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' host=' . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' dbname=susi_main');

$query = pg_query('SELECT DISTINCT(database) FROM su_users');
$db_list = pg_fetch_all($query);
pg_close($conn);

$db_list[] = ['database' => 'new_users_db'];

// var_dump($db_list);

foreach ($db_list as $database) {
    $conn = pg_pconnect('user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' host=' . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' dbname=' . $database['database']);
    // var_export($database['database']);
    echo 'database=' . $database['database'] . "\n";
    $query = pg_query($conn, '
			DROP TABLE IF EXISTS su_contracts_rents;
			CREATE TABLE IF NOT EXISTS "public"."su_contracts_rents"
			(
				"id" serial,
				"contract_id" int4 references "su_contracts" ("id"),
				"renta_id" int4 references "su_renta_types" ("id"),
				"renta_value" float8,
				PRIMARY KEY ("id")
			);
            CREATE OR REPLACE function insertincontrrents() returns setof su_contracts
            AS
              $body$
              DECLARE
              r su_contracts%rowtype;
            BEGIN
              FOR r IN
              SELECT *
              FROM   su_contracts
              WHERE  renta_nat IS NOT NULL
              AND    renta_nat_type_id <> 0
            LOOP
              INSERT INTO su_contracts_rents
                          (
                                      "contract_id",
                                      "renta_id",
                                      "renta_value"
                          )
                          VALUES
                          (
                                      r.id,
                                      r.renta_nat_type_id ,
                                      r.renta_nat
                          );
            RETURN next r;
            END LOOP;
            RETURN;
            END
            $body$
            language "plpgsql";
            SELECT Insertincontrrents();
			DROP FUNCTION IF EXISTS Insertincontrrents();');
    pg_close($conn);
}
//     var_export($db_list);die();
