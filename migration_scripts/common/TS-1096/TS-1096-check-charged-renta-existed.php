<?php

use TF\Engine\Plugins\Core\UserDb\UserDbController;

require_once '../../config/global.config.php';
require_once '../../crons/shell.php';

$databases = [
    'db_vzk_hristo_botev', 'db_rumbich', 'db_agri_profi', 'db_shumanski', 'db_zkpu_edinstvo',
    'db_deshin', 'db_el_agro', 'db_velkovi', 'db_nenovi_ood', 'db_et_arko', 'db_agro_85', 'db_zp_boris',
    'db_kristal', 'db_gorna_malina', 'db_stoikovi', 'db_et_venat', 'db_agro_invest', 'db_zp_rangelov',
    'db_tabachi', 'db_sabko_borisov', 'db_alik', 'db_nava_agroserviz', 'db_amc_agro', 'db_zp_cvqtkov',
    'db_tf_ivo', 'db_gs_vassil_r', 'db_agroidol', 'db_kptu_izvor', 'db_dimitrovi', 'db_zp_tgenov',
    'db_gaq_agro', 'db_zl_klas', 'db_rosa_karchanov', 'db_agrotrend', 'db_technotrade', 'db_tf_vassil',
    'db_skala_13', 'db_et_ambar', 'db_agrodt', 'db_et_chenchev', 'db_ovech_agro', 'db_todor_todorov',
    'db_agriplanet_ood', 'db_zp_conev', 'db_napreduk', 'db_zp_martinov', 'db_zpk_niva', 'db_agro_stil',
    'db_tf_hristo', 'db_agrofirma', 'db_romanova_et', 'db_zk_zora_ravnec', 'db_sd_ilievi', 'db_zpk_edinstvo',
    'db_zp_petkov', 'db_agrodar', 'db_tatyana', 'db_apex_plant', 'db_saglasie_jalti_brqg', 'db_ceco123',
    'db_et_boyan_bonchev', 'db_sd_tundja', 'db_demo_p', 'db_zk_ovcarovo', 'db_kemapul', 'db_zk_bata',
    'db_sandy_87', 'db_izicomers', 'db_izgrev', 'db_zk_sila59', 'db_zora06', 'db_zkpu_drujba',
    'db_zarra2000', 'db_dimo_123', 'db_nikolay_velikov', 'db_zkpu_saznanie_topolica', 'db_georgi',
    'db_zkpu_2013', 'db_vejen', 'db_ahristov', 'db_leyber', 'db_ekani_ad', 'db_ivko_m', 'db_zkpirne',
    'db_zkpunj', 'db_asenica_96', 'db_balgarovoad', 'db_hristov', 'db_ivan_cherni_vruh', 'db_zp_enchev',
    'db_gets_gi_agro', 'db_et_dobromir', 'db_pelikanagro_eood', 'db_kapriz_2004', 'db_lordmp',
    'db_tf_qa', 'db_agri_milk', 'db_agrindo_eood', 'db_toev_kampo', 'db_demo_gs', 'db_zora_agro',
    'db_et_gergana', 'db_enchev', 'db_zkputetovo', 'db_angelov', 'db_zlaten_klas', 'db_zp_andonov',
    'db_nadin', 'db_agrotehrem', 'db_misho', 'db_et_pavis', 'db_zppkbadeshte93', 'db_agrimeks',
    'db_mitko_dimitrov', 'db_batak', 'db_ivapres', 'db_test_ivan', 'db_krasimir_iliev',
    'db_arile', 'db_ahat_agro', 'db_test_vlado', 'db_zp_lalo_stoqnov', 'db_pk_napredak',
    'db_bratq_jelevi', 'db_medven', 'db_et_velichko_borisov', 'db_et_delishes', 'db_release',
    'db_zkpuasenovo', 'db_zp_juliq', 'db_toshel_agro', 'db_demo_vlado', 'db_agro_pro',
    'db_plamen_tonchev', 'db_milenium', 'db_zkpuslaviani', 'db_et_shampion', 'db_et_dar',
    'db_agrostart', 'db_kronos', 'db_lazarov', 'db_et_kalivana', 'db_ppzk_progres',
    'db_zlatenklas2004', 'db_kubratovo147', 'db_gs_vassil_bg', 'db_yankopetrushev',
    'db_agrotida', 'db_inkotar2_eood', 'db_valeri123', 'db_etketi', 'db_popov', 'db_antimagro',
    'db_zk_doverie_makak', 'db_et_naid', 'db_zk_tsarev_dol', 'db_intermed_1', 'db_vladimir_nikolov',
    'db_vivagro_eood', 'db_trend_agro', 'db_agro_vital'];

for ($i = 0; $i < count($databases); $i++) {
    $dbName = $databases[$i];

    $UserDbController = new UserDbController($dbName); // getPluginInstanceUserDb($dbName);

    $results = $UserDbController->DbHandler->getDataByQuery(
        'SELECT * FROM su_charged_renta 
        	WHERE owner_id IS NULL'
    );

    if (count($results) > 0) {
        echo $dbName . ' ';
    }
}
