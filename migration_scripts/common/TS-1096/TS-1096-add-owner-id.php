<?php

use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;
use TF\Engine\Plugins\Core\Users\UsersController;

require_once '../../config/global.config.php';
require_once '../../crons/shell.php';

$mainDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

// $databases = array(
// 	'db_vzk_hristo_botev', 'db_el_agro', 'db_velkovi', 'db_stoikovi', 'db_et_venat',
// 	 'db_alik', 'db_amc_agro', 'db_agrotrend', 'db_skala_13', 'db_agrodt', 'db_et_chenchev',
// 	  'db_zp_martinov', 'db_agro_stil', 'db_agrofirma', 'db_zk_zora_ravnec',
// 	   'db_zp_petkov', 'db_agrodar', 'db_saglasie_jalti_brqg',
// 	    'db_izgrev', 'db_zk_sila59', 'db_zkpu_saznanie_topolica', 'db_zkpu_2013',
// 	     'db_asenica_96', 'db_balgarovoad', 'db_gets_gi_agro', 'db_et_dobromir', 'db_kapriz_2004',
// 	      'db_agri_milk', 'db_agrindo_eood', 'db_zlaten_klas', 'db_ivapres',
// 	       'db_agrostart', 'db_zlatenklas2004', 'db_kubratovo147', 'db_et_naid', 'db_intermed_1');

$databases = [
    'db_tf_hristo', 'db_ceco123', 'db_misho', 'db_tatyana', 'db_demo_vlado', 'db_test_vlado', 'db_demo_p',
];

$UsersController = new UsersController();
$FarmingController = new FarmingController();

// $UsersController = getPluginInstance('Plugins.Core.Users');
// $FarmingController = getPluginInstance('Plugins.Core.Farming');

for ($i = 0; $i < count($databases); $i++) {
    // $dbName = $results[$i]['database'];
    $dbName = $databases[$i];
    echo "Database: {$dbName}\n";

    $UserDbController = new UserDbController($dbName);// getPluginInstanceUserDb($dbName);
    $UsersDbContractsController = new UserDbContractsController($dbName);// getPluginInstanceModuleUserDb('Contracts', $dbName);

    // get charged renta without added owner
    $options = [
        'tablename' => $UserDbController->DbHandler->tableChargedRenta,
        'where' => [
            'owner_id' => ['column' => 'owner_id', 'compare' => 'IS', 'value' => 'NULL'],
        ],
    ];

    $chargedRentaWithoutOwners = $UserDbController->getItemsByParams($options, false, false);

    // get charged renta with added owner
    $options = [
        'tablename' => $UserDbController->DbHandler->tableChargedRenta,
        'where' => [
            'owner_id' => ['column' => 'owner_id', 'compare' => 'IS NOT', 'value' => 'NULL'],
        ],
    ];

    $chargedRentaWithOwners = $UserDbController->getItemsByParams($options, false, false);

    for ($j = 0; $j < count($chargedRentaWithoutOwners); $j++) {
        $chargedRentaOut = $chargedRentaWithoutOwners[$j];
        $chargedRentaArr = [];
        $existChargedRenta = false;

        for ($k = 0; $k < count($chargedRentaWithOwners); $k++) {
            $chargedRentaWith = $chargedRentaWithOwners[$k];

            if ($chargedRentaOut['contract_id'] == $chargedRentaWith['contract_id']
                         && $chargedRentaOut['plot_id'] == $chargedRentaWith['plot_id']
                             && $chargedRentaOut['year'] == $chargedRentaWith['year']) {
                $chargedRentaArr['contract_id'] = $chargedRentaWith['contract_id'];
                $chargedRentaArr['plot_id'] = $chargedRentaWith['plot_id'];
                $chargedRentaArr['year'] = $chargedRentaWith['year'];
                $chargedRentaArr['owner_ids'][] = $chargedRentaWith['owner_id'];

                $existChargedRenta = true;
            }
        }

        $missingOwnerIdsArr = [];

        if ($existChargedRenta) {
            // get contract, plots owner
            $options = [
                'return' => ['string_agg(po.owner_id::TEXT, \',\') as owner_ids'],
                'where' => [
                    'plot_id' => ['column' => 'plot_id', 'prefix' => 'pc', 'compare' => '=', 'value' => $chargedRentaArr['plot_id']],
                    'contract_id' => ['column' => 'contract_id', 'prefix' => 'pc', 'compare' => '=', 'value' => $chargedRentaArr['contract_id']],
                ],
            ];

            $plotsOwners = $UsersDbContractsController->getPlotOwnerRelData($options, false, false);
            $plotsOwnersArr = explode(',', $plotsOwners[0]['owner_ids']);

            for ($l = 0; $l < count($plotsOwnersArr); $l++) {
                $plotsOwner = $plotsOwnersArr[$l];
                $missingOwner = true;

                for ($p = 0; $p < count($chargedRentaArr['owner_ids']); $p++) {
                    $chargedRentaOwner = $chargedRentaArr['owner_ids'][$p];

                    if ($plotsOwner == $chargedRentaOwner) {
                        $missingOwner = false;

                        break;
                    }
                }

                if ($missingOwner) {
                    $missingOwnerIdsArr[] = $plotsOwner;
                }
            }
        } else {
            // get contract, plots owner
            $options = [
                'return' => ['string_agg(po.owner_id::TEXT, \',\') as owner_ids'],
                'where' => [
                    'plot_id' => ['column' => 'plot_id', 'prefix' => 'pc', 'compare' => '=', 'value' => $chargedRentaOut['plot_id']],
                    'contract_id' => ['column' => 'contract_id', 'prefix' => 'pc', 'compare' => '=', 'value' => $chargedRentaOut['contract_id']],
                ],
            ];

            $plotsOwners = $UsersDbContractsController->getPlotOwnerRelData($options, false, false);

            $missingOwnerIdsArr = explode(',', $plotsOwners[0]['owner_ids']);
        }

        $countMissingOwnerIds = count($missingOwnerIdsArr);

        if ($countMissingOwnerIds > 0 && '' != $missingOwnerIdsArr[0]) {
            $rentaIds = [];

            for ($o = 0; $o < $countMissingOwnerIds; $o++) {
                $missingOwnerId = $missingOwnerIdsArr[$o];

                $options = [
                    'tablename' => $UserDbController->DbHandler->tableChargedRenta,
                    'mainData' => [
                        'contract_id' => $chargedRentaOut['contract_id'],
                        'plot_id' => $chargedRentaOut['plot_id'],
                        'year' => $chargedRentaOut['year'],
                        'renta' => $chargedRentaOut['renta'],
                        'renta_nat' => $chargedRentaOut['renta_nat'],
                        'nat_is_converted' => $chargedRentaOut['nat_is_converted'],
                        'nat_unit_price' => $chargedRentaOut['nat_unit_price'],
                        'owner_id' => $missingOwnerId,
                    ],
                    'returning' => ['id'],
                ];

                $rentaIds[] = $UserDbController->addItem($options);
            }

            // get charged renta natura by renta_id
            $optionsNatura = [
                'tablename' => $UserDbController->DbHandler->tableNaturaChargedRenta,
                'where' => [
                    'renta_id' => ['column' => 'renta_id', 'compare' => '=', 'value' => $chargedRentaOut['id']],
                ],
            ];

            $chargedRentaNaturaAll = $UserDbController->getItemsByParams($optionsNatura, false, false);

            for ($u = 0; $u < count($rentaIds); $u++) {
                $rentaId = $rentaIds[$u];

                for ($y = 0; $y < count($chargedRentaNaturaAll); $y++) {
                    $chargedRentaNatura = $chargedRentaNaturaAll[$y];

                    $options = [
                        'tablename' => $UserDbController->DbHandler->tableNaturaChargedRenta,
                        'mainData' => [
                            'renta_id' => $rentaId,
                            'amount' => $chargedRentaNatura['amount'],
                            'nat_type' => $chargedRentaNatura['nat_type'],
                            'nat_is_converted' => $chargedRentaNatura['nat_is_converted'],
                            'nat_unit_price' => $chargedRentaNatura['nat_unit_price'],
                        ],
                    ];

                    $UserDbController->addItem($options);
                }
            }
        }

        echo 'DELETE FROM su_charged_renta where id = ' . $chargedRentaOut['id'];
        echo "\n";
        // delete charged renta without owner_id
        $optionsDelete = [
            'tablename' => $UserDbController->DbHandler->tableChargedRenta,
            'id_string' => $chargedRentaOut['id'],
        ];

        $UserDbController->deleteItemsByParams($optionsDelete);
    }
}
