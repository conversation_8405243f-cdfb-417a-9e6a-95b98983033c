<?php

use Prado\Prado;

require_once '../../config/global.config.php';
require_once '../../crons/shell.php';

Prado::using('BaseApp.vendor.pradosoft.prado.framework.Web.Services.TJsonService');
Prado::using('BaseApp.vendor.pradosoft.prado.framework.Web.THttpRequest');
Prado::using('JsonClasses.Payments.ContractsTreeClass');
Prado::using('JsonClasses.Payments.ContractPaymentsGridClass');
// Prado::using('Plugins.Core.Users.conf');
Prado::using('Plugins.Core.Users.*');

$mainDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

$databases = [
    'db_amc_agro', 'db_agrotrend', 'db_skala_13', 'db_agrodt', 'db_et_chenchev',
    'db_zp_martinov', 'db_agro_stil', 'db_agrofirma', 'db_zk_zora_ravnec',
    'db_zp_petkov', 'db_agrodar', 'db_saglasie_jalti_brqg',
    'db_izgrev', 'db_zk_sila59', 'db_zkpu_saznanie_topolica', 'db_zkpu_2013',
    'db_asenica_96', 'db_balgarovoad', 'db_gets_gi_agro', 'db_et_dobromir', 'db_kapriz_2004',
    'db_agri_milk', 'db_agrindo_eood', 'db_zlaten_klas', 'db_ivapres',
    'db_agrostart', 'db_zlatenklas2004', 'db_kubratovo147', 'db_et_naid'];

$application = Prado::getApplication();

for ($d = 0; $d < count($databases); $d++) {
    $dbName = $databases[$d];
    echo "Database: {$dbName}\n";
    $username = str_replace('db_', '', $dbName);

    $sql = $mainDev->prepare(
        "SELECT password FROM su_system_users WHERE username = '{$username}'"
    );

    $sql->execute();
    $password = $sql->fetch();
    $password = $password[0];

    $authManager = $application->getModule('auth');
    $authManager->login($username, $password);

    $request = new THttpRequest();
    $request['year'] = '6';
    $application->setRequest($request);

    $ContractsTreeClass = new ContractsTreeClass();
    $contractsResults = $ContractsTreeClass->getJsonContent();

    for ($i = 0; $i < count($contractsResults); $i++) {
        $contract = $contractsResults[$i]['attributes'];

        $_POST['contract_id'] = $contract['id'];
        $_POST['year'] = '6';
        $_POST['sort'] = 'c.id';
        $_POST['order'] = 'desc';

        if ($contract['annex_id']) {
            $_POST['annex_id'] = $contract['annex_id'];
        }

        $ContractPaymentsGridClass = new ContractPaymentsGridClass();
        $contractsPaymentsResultsDev = $ContractPaymentsGridClass->getJsonContent();

        $countResultDev = count($contractsPaymentsResultsDev['rows']);

        for ($j = 0; $j < $countResultDev; $j++) {
            $paymentDev = $contractsPaymentsResultsDev['rows'][$j];

            if ($paymentDev['is_dead']) {
                $countChildren = count($paymentDev['children']);

                for ($y = 0; $y < $countChildren; $y++) {
                    $contractsPaymentsResultsDev['rows'][] = $paymentDev['children'][$y];
                }

                $countResultDev += $countChildren;
            }

            $error = '';

            if (((float)$paymentDev['paid_renta'] > (float)$paymentDev['charged_renta']) && (float)$paymentDev['charged_renta'] > 0) {
                $razlika = (float)$paymentDev['paid_renta'] - (float)$paymentDev['charged_renta'];

                $error = 'Dogovor: ' . $contract['c_num'] . ' , Sobstvenik: ' . $paymentDev['owner_names']
                             . ', Izplatena suma sega: ' . $paymentDev['paid_renta'] . ', Nachislena suma sega: ' . $paymentDev['charged_renta']
                             . ', Razlika: ' . $razlika;

                $filename = 'razliki_re_' . $dbName . '.txt';
                errorImportLog($error, $filename);
            }
        }
    }
}

function makeFileIfNotExisted($filename)
{
    // create file
    if (!file_exists($filename)) {
        $myfile = fopen($filename, 'w');
        fclose($myfile);
    }
}

function errorImportLog($error, $filename)
{
    makeFileIfNotExisted($filename);

    $fd = fopen($filename, 'a');

    fwrite($fd, $error . "\n");

    fclose($fd);
}
