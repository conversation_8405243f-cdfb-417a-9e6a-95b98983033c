<?php

use Prado\Prado;

require_once '../../config/global.config.php';
require_once '../../crons/shell.php';

Prado::using('BaseApp.vendor.pradosoft.prado.framework.Web.Services.TJsonService');
Prado::using('BaseApp.vendor.pradosoft.prado.framework.Web.THttpRequest');
Prado::using('JsonClasses.Payments.ContractsTreeClass');
Prado::using('JsonClasses.Payments.ContractPaymentsGridClass');
// Prado::using('Plugins.Core.Users.conf');
Prado::using('Plugins.Core.Users.*');

$mainDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

$databases = [
    'db_vzk_hristo_botev', 'db_el_agro', 'db_velkovi', 'db_stoikovi', 'db_et_venat',
    'db_alik', 'db_amc_agro', 'db_agrotrend', 'db_skala_13', 'db_agrodt', 'db_et_chenchev',
    'db_zp_martinov', 'db_agro_stil', 'db_agrofirma', 'db_zk_zora_ravnec',
    'db_zp_petkov', 'db_agrodar', 'db_saglasie_jalti_brqg',
    'db_izgrev', 'db_zk_sila59', 'db_zkpu_saznanie_topolica', 'db_zkpu_2013',
    'db_asenica_96', 'db_balgarovoad', 'db_gets_gi_agro', 'db_et_dobromir', 'db_kapriz_2004',
    'db_agri_milk', 'db_agrindo_eood', 'db_zlaten_klas', 'db_ivapres',
    'db_agrostart', 'db_zlatenklas2004', 'db_kubratovo147', 'db_et_naid', 'db_intermed_1'];

$application = Prado::getApplication();
for ($d = 0; $d < count($databases); $d++) {
    $dbName = $databases[$d];
    $username = str_replace('db_', '', $dbName);

    $sql = $mainDev->prepare(
        "SELECT password FROM su_system_users WHERE username = '{$username}'"
    );

    $sql->execute();
    $password = $sql->fetch();
    $password = $password[0];

    // controllers
    // $UsersController = getPluginInstance('Plugins.Core.Users');
    // $FarmingController = getPluginInstance('Plugins.Core.Farming');
    // $UserDbController = getPluginInstanceUserDb($dbName);
    // $UsersDbContractsController = getPluginInstanceModuleUserDb('Contracts', $dbName);

    $authManager = $application->getModule('auth');
    $authManager->login($username, $password);

    $request = new THttpRequest();
    $request['year'] = '6';
    $application->setRequest($request);

    $ContractsTreeClass = new ContractsTreeClass();
    $contractsResults = $ContractsTreeClass->getJsonContent();

    for ($i = 0; $i < count($contractsResults); $i++) {
        $contract = $contractsResults[$i]['attributes'];

        $_POST['contract_id'] = $contract['id'];
        $_POST['year'] = '6';
        $_POST['sort'] = 'c.id';
        $_POST['order'] = 'desc';

        if ($contract['annex_id']) {
            $_POST['annex_id'] = $contract['annex_id'];
        }

        $ContractPaymentsGridClass = new ContractPaymentsGridClass();
        $contractsPaymentsResultsDev = $ContractPaymentsGridClass->getJsonContent();

        // set the directory for the cookie using defined document root var
        // $dir = SITE_PATH . "ctemp/contracts";
        // $path = $dir; // . $i

        // make folder if not existerd
        // makeFolderIfNotExisted($path);

        // login form action url
        // $url="http://betalogin3.technofarm.bg/";
        // $loginInfo ="ctl0$Content$Username=".$username."&ctl0$Content$Password=".$password;
        // $loginInfo .= "&PRADO_POSTBACK_TARGET=ctl0$Content$LoginButton";
        // $loginInfo .= "&PRADO_POSTBACK_PARAMETER=undefined";
        // $loginInfo .= "&PRADO_PAGESTATE=eJy1VFtv2jAU5qdMfq+WC5fgaA8rBW1aO9BIn5FrG2LViZlt1iLEf9+xcyGptrVStSfwd875zvXLlMbxJIpYTIKEPEySKBptgzEfw/8xm25JMhyGCWEjgkN8EjhICY7wyeAhRtTKADXvKUZfOGGZsJI7sO+dYHTHLcnIzqB0iUN4ZzUwU1JyaoUqEZBWTIPsVhg72DDHFFdMS9wJQnjsXMPY+dbYYCPA3+AAI/cTBT1bbu1+/vMgfjlbGGE0U6Xlpb3KjnvusVHPvyQF77C5ijtWWgX7PDAJy5/tx9wWMv1Ac6INt5/us8VV4iMnvUhDc95nHjr7TBV7VQLlYOPHF+DT+Sxw+Pa2we0bPz4pzcxr7TeZ/9wxED12iP7eeUPzngajtzcYhhjdcEO12PtreVeTjoz1yf5no+feWVOUChynPUyj9AGu/J8sBkMztcLAEdJmnOalWhBdoNTZHXZHRLlQuvh6U+vQ4El77rWgfOp1rp7mWivdJOipenRRedgTtGeEQtb26ApZwspQVj3q7fmRe2Sw2Qou3SFd4qiSLqWBOpDmsNpzs/AmRLk6v7ezqGEqiTEVXu29wQ/GqqKuxlvDjpUJs5fkWJt9AQvxzNlrCzt7IdwbrquTaSYE4ArqcPK4gK5jGPlOWZjx9cFad1GtEcq5VTvxwtBOO2q/oe6z9JmxORN2RUouu1ObF0TITqzb9JqX7AXp2Ymq3VI0qhavlTQ/OChDi3K3UsZeE/rYifkNcOXYzA==";

        // Content-Type:application/x-www-form-urlencoded
        // $cookie_file_path = $path."/cookie.txt";

        $ch = curl_init();
        // curl_setopt($ch, CURLOPT_HTTPHEADER,array('Content-Type: application/x-www-form-urlencoded'));
        // curl_setopt($ch, CURLOPT_HEADER, false);
        // curl_setopt($ch, CURLOPT_NOBODY, false);
        // curl_setopt($ch, CURLOPT_URL, $url);
        // curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);

        // curl_setopt($ch, CURLOPT_COOKIEJAR, $cookie_file_path);
        // curl_setopt($ch, CURLOPT_COOKIEFILE, $cookie_file_path);
        // curl_setopt($ch, CURLOPT_COOKIESESSION, true);
        // set the cookie the site has for certain features, this is optional
        // curl_setopt($ch, CURLOPT_COOKIE, "cookiename=0");
        curl_setopt(
            $ch,
            CURLOPT_USERAGENT,
            'Mozilla/5.0 (Windows; U; Windows NT 5.0; en-US; rv:1.7.12) Gecko/20050915 Firefox/1.0.7'
        );
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);

        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($ch, CURLOPT_POST, 1);
        // curl_setopt($ch, CURLOPT_POSTFIELDS, $loginInfo);
        // $loginRes = curl_exec($ch);

        if (curl_error($ch)) {
            echo curl_error($ch);
        }

        // page with the content I want to grab
        // do stuff with the info with DomDocument() etc
        $postinfo = 'contract_id=' . $contract['id']
                    . '&year=' . $_POST['year'] . '&sort=' . $_POST['sort']
                        . '&order=' . $_POST['order'] . '&database=' . $dbName;

        if ($_POST['annex_id']) {
            $postinfo .= '&annex_id=' . $_POST['annex_id'];
        }

        curl_setopt($ch, CURLOPT_URL, 'http://betalogin3.technofarm.bg/index.php?json=contract-payments-grid');
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postinfo);
        $contractsPaymentsResultsLogin3 = curl_exec($ch);
        curl_close($ch);

        $contractsPaymentsResultsLogin3 = json_decode($contractsPaymentsResultsLogin3, true);

        $countResultDev = count($contractsPaymentsResultsDev['rows']);
        $countResultLogin3 = count($contractsPaymentsResultsLogin3['rows']);

        if ($countResultLogin3 != $countResultDev) {
            $filename = 'razliki_sobstvenici_dog_' . $dbName . '.txt';
            $error = 'Contract which have not equal owners in dev and login3. ID: ' . $contractsPaymentsResultsLogin3['rows'][0]['contract_id'];
            errorImportLog($error, $filename);

            continue;
        }

        for ($j = 0; $j < $countResultDev; $j++) {
            $paymentDev = $contractsPaymentsResultsDev['rows'][$j];
            $paymentLogin3 = $contractsPaymentsResultsLogin3['rows'][$j];
            $error = '';

            if ((float)$paymentLogin3['paid_renta'] > (float)$paymentDev['charged_renta']) {
                $razlika = (float)$paymentLogin3['paid_renta'] - (float)$paymentDev['charged_renta'];

                $error = 'Dogovor: ' . $contract['c_num'] . ' , Sobstvenik: ' . $paymentDev['owner_names']
                             . ', Izplatena suma: ' . $paymentLogin3['paid_renta'] . ', Nachislena suma sega: ' . $paymentDev['charged_renta']
                             . ', Razlika: ' . $razlika;

                $filename = 'razliki_re_' . $dbName . '.txt';
                errorImportLog($error, $filename);
            }
        }
    }
}

function makeFileIfNotExisted($filename)
{
    // create file
    if (!file_exists($filename)) {
        $myfile = fopen($filename, 'w');
        fclose($myfile);
    }
}

function errorImportLog($error, $filename)
{
    makeFileIfNotExisted($filename);

    $fd = fopen($filename, 'a');

    fwrite($fd, $error . "\n");

    fclose($fd);
}
