<?php

require_once '../../config/global.config.php';

if (isset($_SERVER['HTTP_USER_AGENT'])) {
    die;
}

$mainDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

$sql = $mainDev->prepare(
    'SELECT database FROM su_users
	WHERE level = 2 
	ORDER BY database'
);

$sql->execute();
$results = $sql->fetchAll();
$results[] = ['database' => 'new_users_db'];

for ($i = 0; $i < count($results); $i++) {
    $dbName = $results[$i]['database'];

    echo "Database: {$dbName} \n";

    $sql = 'ALTER TABLE su_charged_renta_natura_params ALTER COLUMN price_per_unit TYPE float8;
			ALTER TABLE su_charged_renta_natura_params ALTER COLUMN amount TYPE float8;';

    $sql = str_replace("\r\n", ' ', $sql);
    system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres -d ' . $dbName . ' -c "' . $sql . '"', $return);
}
