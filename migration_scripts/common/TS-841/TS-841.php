<?php

require_once '../../../config/global.config.php';
require_once '../../../protected/Common/Config.php';

if (isset($_SERVER['HTTP_USER_AGENT'])) {
    die;
}

// How to use it? >php TS-841.php georgi 1:25 2:50

// Main input values
$input = array_slice($argv, 1);

// generate input array $rentaTypes
$rentaTypes = [];
for ($i = 1; $i < count($argv) - 1; $i++) {
    $inputPieces = explode(':', $input[$i]);
    $currentKey = current($inputPieces);
    $rentaValue = end($inputPieces);
    $rentaTypes[$currentKey] = $rentaValue;
}
// Input $userDbName
$userDbName = 'db_' . $input[0];

$mainDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

$cmd = $mainDev->prepare('SELECT 1 from pg_database WHERE datname = :datname');
$cmd->bindValue(':datname', $userDbName);
$cmd->execute();
$result = $cmd->fetchAll();

// if db does not exists
if (!count($result)) {
    die('Sorry. Invalid input: username: ' . $input[0] . '!');
}

$userDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $userDbName . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

// select contracts
$sql = $userDev->prepare(
    'SELECT * FROM su_contracts c 
 	 WHERE c.nm_usage_rights IN (2,3)
 	 ORDER BY c.id desc'
);
$sql->execute();
$resultsContracts = $sql->fetchAll();

// select renta types
$sql = $userDev->prepare(
    'SELECT * FROM su_renta_types rt 
 	 ORDER BY rt.id desc'
);
$sql->execute();
$resultsRentaTypes = $sql->fetchAll();

// compare Renta Types
$dbRentaIds = array_map(function ($renta) {
    return $renta['id'];
}, $resultsRentaTypes);

$inputRentaIds = array_keys($rentaTypes);
$missingRentaIds = array_diff($inputRentaIds, $dbRentaIds);

if (count($missingRentaIds)) {
    die('Sorry. Invalid input: Renta Ids!');
}

// insert INTO su_contracts_rents
for ($i = 0; $i < count($resultsContracts); $i++) {
    foreach ($rentaTypes as $key => $value) {
        $sql = 'INSERT INTO su_contracts_rents (contract_id, renta_id, renta_value) VALUES (:contract_id, :renta_id, :renta_value);';
        $cmd = $userDev->prepare($sql);
        $cmd->bindValue(':contract_id', $resultsContracts[$i]['id']);
        $cmd->bindValue(':renta_id', $key);
        $cmd->bindValue(':renta_value', $value);
        $cmd->execute();
    }
}

echo 'Done!';
