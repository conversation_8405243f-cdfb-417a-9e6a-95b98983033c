<?php

require_once '../../config/global.config.php';

if ($_SERVER['HTTP_USER_AGENT']) {
    die;
}

$dbhDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

$sql = $dbhDev->prepare(
    'SELECT ul.table_name,u.database FROM su_users_layers ul
	INNER JOIN su_users u on u.id = ul.user_id
	WHERE ul.layer_type = 9'
);

$sql->execute();
$results = $sql->fetchAll();

for ($i = 0; $i < count($results); $i++) {
    echo 'database=' . $results[$i]['database'] . "\n";

    $sql = "ALTER TABLE {$results[$i]['table_name']} DROP CONSTRAINT {$results[$i]['table_name']}_prc_name_key;
			ALTER TABLE {$results[$i]['table_name']} DROP CONSTRAINT prc_name;
			";

    system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . $results[$i]['database'] . ' -c "' . $sql . '"', $return);
}
