<?php

require_once '../config/global.config.php';

if ($_SERVER['HTTP_USER_AGENT']) {
    die;
}

$conn = pg_pconnect('user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' host=' . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' dbname=susi_main');

$query = pg_query('SELECT DISTINCT(database) FROM su_users');
$db_list = pg_fetch_all($query);
pg_close($conn);

$db_list[] = ['database' => 'new_users_db'];
// var_dump($db_list);
// die;
foreach ($db_list as $database) {
    $conn = pg_pconnect('user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' host=' . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' dbname=' . $database['database']);
    // var_export($database['database']);
    echo 'database=' . $database['database'] . "\n";
    $query = pg_query('
        ALTER TABLE su_personal_use add column "renta_nat_type" int4;
        CREATE OR REPLACE function updatepersonaluse() returns setof su_contracts
        AS
          $body$
          DECLARE
          r su_contracts%rowtype;
        BEGIN
          FOR r IN
          SELECT c.*
          FROM   su_contracts c join su_personal_use pu on(c.id = pu.contract_id)
        LOOP
        UPDATE su_personal_use
        SET  renta_nat_type = r.renta_nat_type_id           
        WHERE contract_id = r.id;
     
        RETURN next r;
        END LOOP;
        RETURN;
        END
        $body$
        language "plpgsql";
        SELECT updatepersonaluse();
		DROP FUNCTION IF EXISTS updatepersonaluse();');
    pg_close($conn);
}
