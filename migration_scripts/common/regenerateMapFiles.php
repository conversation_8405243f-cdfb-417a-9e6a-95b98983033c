<?php

use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

require_once '../config/global.config.php';
include '../crons/shell.php';

if ($_SERVER['HTTP_USER_AGENT']) {
    die;
}

$dbhDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

$sql = $dbhDev->prepare("SELECT DISTINCT(group_id), database FROM su_users WHERE database = 'db_bgagro'");
$sql->execute();
$results = $sql->fetchAll();
// $results[] = array('database' => 'new_users_db');
// var_dump($results); die();

$LayersController = new LayersController('Layers');

for ($i = 0; $i < count($results); $i++) {
    echo 'database=' . $results[$i]['database'] . "\n";
    $options = [
        'user_id' => $results[$i]['group_id'],
        'database' => $results[$i]['database'],
    ];

    $UserDbController = new UserDbController($results[$i]['database']);
    // $UsersDbController = getPluginInstanceUserDb($results[$i]['database']);

    $LayersController->generateMapFile($options);
}
