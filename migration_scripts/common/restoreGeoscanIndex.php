<?php

require_once '../config/global.config.php';
include '../modules/shell.php';

if ($_SERVER['HTTP_USER_AGENT']) {
    die;
}

$dbhDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

$sql = $dbhDev->prepare("SELECT 
        array_agg(s.id) AS sat_ids,
		array_agg(DISTINCT s.tileid || '/' || s.date::date || '/' || s. NAME || '.tif') AS sat_image_names,
		s.date::date AS date,
		so.user_id,
		sop.order_id,
		array_agg(DISTINCT sop.gid ORDER BY sop.gid ASC) AS plots
        FROM su_satellite AS s
        JOIN su_satellite_orders_plots AS sop ON (ST_Intersects(s.extent, ST_Transform(sop.geom, 4326)))
		join su_satellite_orders AS so ON so.id = sop.order_id
        where true
        and so.status = 'processed'
        group by so.user_id, sop.order_id, s.date::date
        order by s.date::date asc");
$sql->execute();
$results = $sql->fetchAll();

// var_dump($results); die();

for ($i = 0; $i < count($results); $i++) {
    $tile = $results[$i];
    $plotsIds = explode(',', trim($tile['plots'], '{}'));

    for ($j = 0; $j < count($plotsIds); $j++) {
        $plotId = $plotsIds[$j];
        $tileDate = $tile['date'];
        $tileLayerName = 'ndvi_raster_' . $tile['date'];
        $tileUserID = $tile['user_id'];

        $sql = $dbhDev->prepare("SELECT * FROM su_satellite_layers_plots WHERE plot_id = {$plotId} AND date = '{$tileDate}' AND user_id = {$tileUserID}");
        $sql->execute();
        $existingIndex = $sql->fetchAll();

        if (count($existingIndex) > 0) {
            continue;
        }

        $created = date('Y-m-d H:i:s');

        echo "Inserting row (plot_id: {$plotId}, date: {$tileDate}, user_id: {$tileUserID}) - result: ";

        $insertSql = $dbhDev->prepare("INSERT INTO su_satellite_layers_plots (plot_id, layer_name, date, user_id, created_at) VALUES ({$plotId}, '{$tileLayerName}', '{$tileDate}', {$tileUserID}, '{$created}');");

        $r = $insertSql->execute();
        echo $r . "\n";
    }
}
