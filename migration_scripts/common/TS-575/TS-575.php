<?php

require_once '../../../config/global.config.php';
include '../../../modules/shell.php';
Prado::using('Plugins.Core.Farming.conf');

define('PHASE_ID', 2);
define('TYPE_ID', 225);
// define('YEAR_ID', 5); //2014
define('YEAR_ID', 4); // 2013
define('FARM_ID', 350);
// define('ZP_TABLE', 'layer_zp_1414500952'); //2014
define('ZP_TABLE', 'layer_zp_1414500951'); // 2013
define('DB_NAME', 'db_test_hristo');
define('DB_HERA_TABLE', 'hera_jan');
define('SU_DAIRY_CONFIG', 'su_diary_configs');
define('SU_DIARY_EVENTS', 'su_diary_events');

if ($_SERVER['HTTP_USER_AGENT']) {
    die;
}

$dbhDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DB_NAME . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

// Check if Machine exist in dairy_config if don't exist, update column mashine_missing

$sqlAllMachines = $dbhDev->prepare('SELECT machine
				   FROM ' . DB_HERA_TABLE . "
				   WHERE machine != ''");

$sqlAllMachines->execute();
$allMachinesResult = $sqlAllMachines->fetchAll();
$allUnuqueMachinesExcel = findUniqueValue($allMachinesResult);

// config_type 4 = only machine
$sqlAllMachinesDairy = $dbhDev->prepare('SELECT number
				   FROM ' . SU_DAIRY_CONFIG . '
				   WHERE config_type = 4');

$sqlAllMachinesDairy->execute();
$allMachinesDairyResult = $sqlAllMachinesDairy->fetchAll();
$allUnuqueMachinesDairy = findUniqueValue($allMachinesDairyResult);

$machineExist = false;
for ($i = 0; $i < count($allUnuqueMachinesExcel); $i++) {
    for ($j = 0; $j < count($allUnuqueMachinesDairy); $j++) {
        if ($allUnuqueMachinesDairy[$j] == $allUnuqueMachinesExcel[$i]) {
            $machineExist = true;

            break;
        }

        $machineExist = false;
    }

    if (!$machineExist) {
        // update machine_missing cause not exist in dairy_config
        $sqlUpMachine = $dbhDev->prepare(
            'UPDATE ' . DB_HERA_TABLE . ' SET machine_missing = TRUE
		 WHERE machine = :machine'
        );

        $sqlUpMachine->bindValue(':machine', $allUnuqueMachinesExcel[$i]);
        $sqlUpMachine->execute();
    }
}

// Check if Inventory exist in dairy_config if don't exist, update column inventory_missing

$sqlAllInventory = $dbhDev->prepare('SELECT inventory
				   FROM ' . DB_HERA_TABLE . "
				   WHERE inventory != ''");

$sqlAllInventory->execute();
$allInventoryResult = $sqlAllInventory->fetchAll();
$allUnuqueInventoryExcel = findUniqueValue($allInventoryResult);

// config_type 6 = only inventory
$sqlAllInventoryDairy = $dbhDev->prepare('SELECT number
				   FROM ' . SU_DAIRY_CONFIG . '
				   WHERE config_type = 6');

$sqlAllInventoryDairy->execute();
$allInventoryDairyResult = $sqlAllInventoryDairy->fetchAll();
$allUnuqueInventoryDairy = findUniqueValue($allInventoryDairyResult);

$inventoryExist = false;
for ($i = 0; $i < count($allUnuqueInventoryExcel); $i++) {
    for ($j = 0; $j < count($allUnuqueInventoryDairy); $j++) {
        if ($allUnuqueInventoryDairy[$j] == $allUnuqueInventoryExcel[$i]) {
            $inventoryExist = true;

            break;
        }

        $inventoryExist = false;
    }

    if (!$inventoryExist) {
        // update inventory_missing cause not exist in dairy_config
        $sqlUpInventory = $dbhDev->prepare(
            'UPDATE ' . DB_HERA_TABLE . ' SET inventory_missing = TRUE
		 WHERE inventory = :inventory'
        );

        $sqlUpInventory->bindValue(':inventory', $allUnuqueInventoryExcel[$i]);
        $sqlUpInventory->execute();
    }
}

// Check if ss_praktik exist in dairy_config if don't exist, update column ss_praktika_missing

$sqlAllSsPraktik = $dbhDev->prepare('SELECT ss_praktik
				   FROM ' . DB_HERA_TABLE . "
				   WHERE ss_praktik != ''");

$sqlAllSsPraktik->execute();
$allSsPraktikResult = $sqlAllSsPraktik->fetchAll();
$allUnuqueSsPraktikExcel = findUniqueValue($allSsPraktikResult);

// config_type 6 = only ss_praktik
$sqlAllSsPraktikDairy = $dbhDev->prepare('SELECT name
				   FROM ' . SU_DAIRY_CONFIG . '
				   WHERE config_type = 2');

$sqlAllSsPraktikDairy->execute();
$allSsPraktikDairyResult = $sqlAllSsPraktikDairy->fetchAll();
$allUnuqueSsPraktikDairy = findUniqueValue($allSsPraktikDairyResult);

$ssPraktikExist = false;
for ($i = 0; $i < count($allUnuqueSsPraktikExcel); $i++) {
    for ($j = 0; $j < count($allUnuqueSsPraktikDairy); $j++) {
        $lev = levenshtein($allUnuqueSsPraktikDairy[$j], $allUnuqueSsPraktikExcel[$i]);

        if ($allUnuqueSsPraktikDairy[$j] == $allUnuqueSsPraktikExcel[$i]) {
            $ssPraktikExist = true;

            break;
        } elseif ($lev < 3) {
            var_export('Repeated: ' . $lev . ', Export: ' . $allUnuqueSsPraktikExcel[$i] . ' Diary: ' . $allUnuqueSsPraktikDairy[$j]);
            $sqlUpSsPraktikExcel = $dbhDev->prepare(
                'UPDATE ' . DB_HERA_TABLE . ' SET ss_praktik = :ss_praktik_diary
									 WHERE ss_praktik = :ss_praktik'
            );

            $sqlUpSsPraktikExcel->bindValue(':ss_praktik_diary', $allUnuqueSsPraktikDairy[$j]);
            $sqlUpSsPraktikExcel->bindValue(':ss_praktik', $allUnuqueSsPraktikExcel[$i]);
            $sqlUpSsPraktikExcel->execute();

            $ssPraktikExist = true;

            break;
        }

        $ssPraktikExist = false;
    }

    if (!$ssPraktikExist) {
        // Update ss_praktik_missing cause not exist in dairy_config
        $sqlUpSsPraktik = $dbhDev->prepare(
            'UPDATE ' . DB_HERA_TABLE . ' SET ss_praktik_missing = TRUE
		 WHERE ss_praktik = :ss_praktik'
        );

        $sqlUpSsPraktik->bindValue(':ss_praktik', $allUnuqueSsPraktikExcel[$i]);
        $sqlUpSsPraktik->execute();
    }
}

// Check if Mechanics exist in dairy_config if don't exist, insert them

$sqlAllMechanics = $dbhDev->prepare('SELECT mechanics
				   FROM ' . DB_HERA_TABLE . "
				   WHERE mechanics != ''");

$sqlAllMechanics->execute();
$allMechanicsResult = $sqlAllMechanics->fetchAll();
$allUnuqueMechanicsExcel = findUniqueValue($allMechanicsResult);

// config_type 9 = only mechanics
$sqlAllMechanicsDairy = $dbhDev->prepare('SELECT name
				   FROM ' . SU_DAIRY_CONFIG . '
				   WHERE config_type = 9');

$sqlAllMechanicsDairy->execute();
$allMechanicsDairyResult = $sqlAllMechanicsDairy->fetchAll();
$allUnuqueMechanicsDairy = findUniqueValue($allMechanicsDairyResult);

$mechanicExist = false;
for ($i = 0; $i < count($allUnuqueMechanicsExcel); $i++) {
    for ($j = 0; $j < count($allUnuqueMechanicsDairy); $j++) {
        $lev = levenshtein($allUnuqueMechanicsDairy[$j], $allUnuqueMechanicsExcel[$i]);

        if ($allUnuqueMechanicsDairy[$j] == $allUnuqueMechanicsExcel[$i]) {
            $mechanicExist = true;

            break;
        } elseif ($lev < 2) {
            var_export('Repeated: ' . $lev . ', Export: ' . $allUnuqueMechanicsExcel[$i] . ' Diary: ' . $allUnuqueMechanicsDairy[$j]);
            die;
            $sqlUpMechanicsExcel = $dbhDev->prepare(
                'UPDATE ' . DB_HERA_TABLE . ' SET mechanics = :mechanics_diary
									 WHERE mechanics = :mechanics'
            );

            $sqlUpMechanicsExcel->bindValue(':ss_praktik_diary', $allUnuqueMechanicsDairy[$j]);
            $sqlUpMechanicsExcel->bindValue(':ss_praktik', $allUnuqueMechanicsExcel[$i]);
            $sqlUpMechanicsExcel->execute();

            $mechanicExist = true;

            break;
        }

        $mechanicExist = false;
    }
    if (!$mechanicExist) {
        // insert into dairy_config missing mechanics
        $sqlInsertMechanics = $dbhDev->prepare(
            'INSERT INTO ' . SU_DAIRY_CONFIG . ' (name, config_type, perf_title, is_chemical_treatment)
						VALUES(:mechanics, :config_type, :perf_title, :is_chemical_treatment)'
        );

        $sqlInsertMechanics->bindValue(':mechanics', $allUnuqueMechanicsExcel[$i]);
        $sqlInsertMechanics->bindValue(':config_type', 9);
        $sqlInsertMechanics->bindValue(':perf_title', 'Механизатор');
        $sqlInsertMechanics->bindValue(':is_chemical_treatment', false, PDO::PARAM_BOOL);
        $sqlInsertMechanics->execute();
    }
}

// die;

$sqlSerialNum = $dbhDev->prepare(
    'SELECT max(serial_num) as max_serial_num, count(serial_num) as count_serial_num
	 FROM ' . SU_DIARY_EVENTS
);

$sqlSerialNum->execute();
$serialNumResult = $sqlSerialNum->fetch();
$serialNum = 1;

$sql = $dbhDev->prepare(
    "SELECT d_c.id as performer_id, d_c2.id as machine_id, d_c3.id as attachment_id,d_c4.id as subtype_id, 
			replace(h_j.obrabotena_plosht, ',', '.') as completed_area, h_j.zar_gor_litri as start_fuel, zp.id as plot_id, h_j.date::TIMESTAMP as add_date,
			(CASE 
				WHEN h_j.mch_begin = '' OR  h_j.mch_end = ''
				THEN NULL
				ELSE(h_j.mch_end)::integer - (h_j.mch_begin)::integer
	 		END
			) as total_time_in,
			h_j.mechanics as mechanics, h_j.machine as machine, h_j.inventory as inventory, h_j.ss_praktik as ss_praktik, h_j.block_number as block_number,
			h_j.obrabotena_plosht as obrabotena_plosht, h_j.zar_gor_litri as zar_gor_litri
	 FROM " . DB_HERA_TABLE . ' h_j
	 LEFT JOIN ' . SU_DAIRY_CONFIG . ' d_c on (h_j.mechanics = d_c.name)
	 LEFT JOIN ' . SU_DAIRY_CONFIG . ' d_c2 on (h_j.machine = d_c2.number)
	 LEFT JOIN ' . SU_DAIRY_CONFIG . ' d_c3 on (h_j.inventory = d_c3.number)
	 LEFT JOIN ' . SU_DAIRY_CONFIG . ' d_c4 on (h_j.ss_praktik = d_c4.name)
	 INNER JOIN ' . ZP_TABLE . " zp on (zp.area_name = h_j.block_number)
	 WHERE zp.area_name not similar to '%(,|;)%'"
);

$sql->execute();
$results = $sql->fetchAll();
$count = 0;

for ($i = 0; $i < count($results); $i++) {
    $result = $results[$i];

    // Update column "missing" with "TRUE" cause it's add to " . SU_DIARY_EVENTS . "
    $completed_area = replace($result['obrabotena_plosht'], '.', ',');
    $sqlMissingUpdate = $dbhDev->prepare(
        'UPDATE ' . DB_HERA_TABLE . ' SET missing = TRUE
		 WHERE mechanics = :mechanics
		 AND machine = :machine
		 AND inventory = :inventory
		 AND block_number = :block_number
		 AND obrabotena_plosht = :completed_area
		 AND zar_gor_litri = :start_fuel'
    );

    $sqlMissingUpdate->bindValue(':mechanics', $result['mechanics']);
    $sqlMissingUpdate->bindValue(':subtype', $result['subtype']);
    $sqlMissingUpdate->bindValue(':machine', $result['machine']);
    $sqlMissingUpdate->bindValue(':inventory', $result['inventory']);
    $sqlMissingUpdate->bindValue(':block_number', $result['block_number']);
    $sqlMissingUpdate->bindValue(':completed_area', $completed_area);
    $sqlMissingUpdate->bindValue(':start_fuel', $result['zar_gor_litri']);

    $sqlMissingUpdate->execute();

    $cmd = $dbhDev->prepare('INSERT INTO ' . SU_DIARY_EVENTS . ' (phase_id, type_id, subtype_id, performer_id, machine_id, 
				attachment_id, farming_id, year_id, plot_id, completed_area, start_fuel, serial_num, total_time_in, add_date)
							VALUES(:phase_id, :type_id, :subtype_id, :performer_id, :machine_id, :attachment_id, 
								:farming_id, :year_id, :plot_id, :completed_area, :start_fuel, :serial_num, :total_time_in, :add_date)');

    $cmd->bindValue(':phase_id', PHASE_ID, PDO::PARAM_INT);
    $cmd->bindValue(':type_id', TYPE_ID, PDO::PARAM_INT);
    $cmd->bindValue(':subtype_id', $result['subtype_id'], PDO::PARAM_INT);
    $cmd->bindValue(':performer_id', $result['performer_id'], PDO::PARAM_INT);
    $cmd->bindValue(':machine_id', $result['machine_id'], PDO::PARAM_INT);
    $cmd->bindValue(':attachment_id', $result['attachment_id'], PDO::PARAM_INT);
    $cmd->bindValue(':farming_id', FARM_ID, PDO::PARAM_INT);
    $cmd->bindValue(':year_id', YEAR_ID, PDO::PARAM_INT);
    $cmd->bindValue(':plot_id', $result['plot_id'], PDO::PARAM_INT);
    $cmd->bindValue(':completed_area', $result['completed_area'] ? $result['completed_area'] : null);
    $cmd->bindValue(':start_fuel', $result['start_fuel'] ? $result['start_fuel'] : null);

    // increase serial_num with 1
    ++$count;
    if ($serialNumResult['count_serial_num'] > 0) {
        $serialNum = $serialNumResult['max_serial_num'] + $count;
    } else {
        $serialNum = $count;
    }

    if ($result['total_time_in'] < 10) {
        $result['total_time_in'] = str_pad($result['total_time_in'], 2, '0', STR_PAD_LEFT) . ':00:00';
    } else {
        $result['total_time_in'] = $result['total_time_in'] . ':00:00';
    }

    $cmd->bindValue(':serial_num', $serialNum, PDO::PARAM_INT);
    $cmd->bindValue(':total_time_in', '00:00:00' == $result['total_time_in'] ? null : $result['total_time_in']);
    $cmd->bindValue(':add_date', $result['add_date'], PDO::PARAM_STR);

    $cmd->execute();
    var_export($sql->errorInfo());
    var_export($result);
}

function findUniqueValue($array)
{
    $a = [];

    foreach ($array as $k => $v) {
        foreach ($v as $key => $value) {
            if (!in_array($value, $a)) {
                $a[] = $value;
            }
        }
    }

    return $a;
}
