<?php

require_once '../config/global.config.php';

if ($_SERVER['HTTP_USER_AGENT']) {
    die;
}

$conn = pg_pconnect('user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' host=' . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' dbname=susi_main');

$query = pg_query('SELECT DISTINCT(database) FROM su_users');
$db_list = pg_fetch_all($query);
pg_close($conn);

// $db_list[] = array('database' => 'new_users_db');
// var_dump($db_list);
// die;
foreach ($db_list as $database) {
    $conn = pg_pconnect('user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' host=' . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' dbname=' . $database['database']);
    // 	    var_export($database['database']);
    echo 'database=' . $database['database'] . "\n";
    $owners = pg_query('select distinct owner_id from su_heritors');
    $owners = pg_fetch_all($owners);
    for ($i = 0; $i < count($owners); $i++) {
        $q2 = pg_query("select * from su_heritors where path ~ '*.{$owners[$i]['owner_id']}'");
        $q2 = pg_fetch_all($q2);
        $q3 = pg_query("select * from su_heritors where path ~ '{$owners[$i]['owner_id']}.*'");
        $q3 = pg_fetch_all($q3);
        for ($j = 0; $j < count($q2); $j++) {
            $q_arr = explode('.', $q2[$j]['path']);
            array_pop($q_arr);
            $q2[$j]['path'] = implode('.', $q_arr);
            for ($k = 0; $k < count($q3); $k++) {
                $q_arr2 = explode('.', $q3[$k]['path']);
                $q3_owner = array_pop($q_arr2);
                $new_path = $q2[$j]['path'] . '.' . $q3[$k]['path'];

                $exist_record = pg_query("select * from su_heritors where path ~ '{$new_path}'");
                $exist_record = pg_fetch_all($exist_record);
                if (empty($exist_record)) {
                    pg_query(" INSERT INTO su_heritors (
                    				owner_id,
                    				path
                    			) VALUES(
                    			    {$q3_owner},
                    			    '{$new_path}'
                			    )");
                    var_export('added' . $new_path . '\n');
                }
            }
        }
    }
    pg_close($conn);
}
