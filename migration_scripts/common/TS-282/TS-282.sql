
ALTER TABLE layer_kvs ADD COLUMN is_edited B<PERSON><PERSON><PERSON><PERSON> NOT NULL DEFAULT false;
ALTER TABLE layer_kvs ADD COLUMN edit_date TIMESTAMP WITHOUT TIME ZONE;
ALTER TABLE layer_kvs ADD COLUMN edit_active_from T<PERSON><PERSON><PERSON><PERSON> WITHOUT TIME ZONE;


CREATE TYPE edit_type_enum AS ENUM ('split', 'merge');
--
-- PostgreSQL database dump
--

SET statement_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SET check_function_bodies = false;
SET client_min_messages = warning;

SET search_path = public, pg_catalog;

SET default_tablespace = '';

SET default_with_oids = false;

--
-- Name: layer_kvs_edit_log; Type: TABLE; Schema: public; Owner: postgres; Tablespace: 
--

CREATE TABLE layer_kvs_edit_log (
    id integer NOT NULL,
    new_gid integer NOT NULL,
    old_gid integer NOT NULL,
    edit_type edit_type_enum,
    edit_date date,
    edit_active_from date
);


ALTER TABLE public.layer_kvs_edit_log OWNER TO postgres;

--
-- Name: layer_kvs_edit_log_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE layer_kvs_edit_log_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.layer_kvs_edit_log_id_seq OWNER TO postgres;

--
-- Name: layer_kvs_edit_log_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE layer_kvs_edit_log_id_seq OWNED BY layer_kvs_edit_log.id;


--
-- Name: id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY layer_kvs_edit_log ALTER COLUMN id SET DEFAULT nextval('layer_kvs_edit_log_id_seq'::regclass);


--
-- Name: layer_kvs_edit_log_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres; Tablespace: 
--

ALTER TABLE ONLY layer_kvs_edit_log
    ADD CONSTRAINT layer_kvs_edit_log_pkey PRIMARY KEY (id);


--
-- Name: new_gid_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY layer_kvs_edit_log
    ADD CONSTRAINT new_gid_fk FOREIGN KEY (new_gid) REFERENCES layer_kvs(gid) ON DELETE CASCADE;


--
-- Name: old_gid_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY layer_kvs_edit_log
    ADD CONSTRAINT old_gid_fk FOREIGN KEY (old_gid) REFERENCES layer_kvs(gid) ON DELETE CASCADE;


--
-- PostgreSQL database dump complete
--


CREATE TYPE annex_action_enum AS ENUM ('added', 'removed');

ALTER TABLE su_contracts_plots_rel ADD COLUMN annex_action annex_action_enum NOT NULL DEFAULT 'added'::annex_action_enum;