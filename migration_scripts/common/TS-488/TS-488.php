<?php

require_once '../../config/global.config.php';

if ($_SERVER['HTTP_USER_AGENT']) {
    die;
}

$dbhDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

$sql = $dbhDev->prepare('SELECT DISTINCT(database) FROM su_users');
$sql->execute();
$results = $sql->fetchAll();
$results[] = ['database' => 'new_users_db'];
// var_dump($results); die();

for ($i = 0; $i < count($results); $i++) {
    echo 'database=' . $results[$i]['database'] . "\n";
    system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . $results[$i]['database'] . ' -f TS-488.sql', $return);
}
