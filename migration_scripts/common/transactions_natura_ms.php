<?php

require_once '../config/global.config.php';

if ($_SERVER['HTTP_USER_AGENT']) {
    die;
}

$conn = pg_pconnect('user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' host=' . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' dbname=susi_main');

$query = pg_query('SELECT DISTINCT(database) FROM su_users');
$db_list = pg_fetch_all($query);
pg_close($conn);

$db_list[] = ['database' => 'new_users_db'];
// var_dump($db_list);
// die;
foreach ($db_list as $database) {
    $conn = pg_pconnect('user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' host=' . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' dbname=' . $database['database']);
    // var_export($database['database']);
    echo 'database=' . $database['database'] . "\n";
    $query = pg_query('
			DROP TABLE IF EXISTS su_transactions_natura;
            CREATE TABLE IF NOT EXISTS "public"."su_transactions_natura"
			(
				"id" serial,
				"transaction_id" int4 references "su_transactions" ("id") on delete CASCADE,
				"nat_type" int4 references "su_renta_types" ("id"),
				"paid_from" int4,
				"paid_in" int4,
				"amount" float8,
				PRIMARY KEY ("id")
			);
            CREATE OR REPLACE function insertintransactionsnatura() returns setof su_transactions
            AS
              $body$
              DECLARE
              r su_transactions%rowtype;
            BEGIN
              FOR r IN
              SELECT *
              FROM   su_transactions
              WHERE  amount_nat IS NOT NULL
              AND    payment_nat_type <> 0
            LOOP
              INSERT INTO su_transactions_natura
                          (
                                      "transaction_id",
                                      "nat_type",
                                      "paid_from",
                            	      "paid_in",
                            	      "amount"
                          )
                          VALUES
                          (
                                      r.id,
                                      r.payment_nat_type ,
                                      r.paid_from,
	                                  r.paid_in,
	                                  r.amount_nat
                          );
            RETURN next r;
            END LOOP;
            RETURN;
            END
            $body$
            language "plpgsql";
            SELECT insertintransactionsnatura();
			DROP FUNCTION IF EXISTS insertintransactionsnatura();');
    pg_close($conn);
}
