<?php

$dbName = 'db_toshel_agro';
require_once '../../config/global.config.php';
$mainDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . $dbName . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

$sql = $mainDev->prepare(
    'SELECT cf.*, c_num FROM su_contracts_files cf  left join su_contracts c on cf.contract_id = c.id ORDER BY id asc'
);

$sql->execute();
$results = $sql->fetchAll();

foreach ($results as $file) {
    $file_fragments = explode('.', $file['filename']);
    $filename = current($file_fragments);
    $ext = end($file_fragments);

    $filePath = LAYERS_CONTRACTS_PATH . $file['group_id'] . '/' . $file['user_id'] . '/' . $file['id'] . '.' . $ext;
    $newFileName = LAYERS_CONTRACTS_PATH . $file['group_id'] . '/' . $file['user_id'] . '/' . $filename . '_' . $file['id'] . '.' . $ext;

    $relFilePath = 'files/contract_files/' . $file['group_id'] . '/' . $file['user_id'] . '/' . $file['id'] . '.' . $ext;
    $relNewFileName = 'files/contract_files/' . $file['group_id'] . '/' . $file['user_id'] . '/' . $filename . '_' . $file['id'] . '.' . $ext;

    if (file_exists($filePath)) {
        $isRenamed = rename($filePath, $newFileName);

        if ($isRenamed) {
            // echo "Exists: { $relNewFileName } \n";
        }
        // echo "Exists: { $relFilePath } \n";
    } else {
        if (file_exists($newFileName)) {
            // echo "Exists: { $relNewFileName } \n";
        } else {
            echo "Does not exist: {$file['filename']} from {$file['c_num']}\n";
        }
    }
}
