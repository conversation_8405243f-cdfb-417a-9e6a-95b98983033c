<?php

require_once '../config/global.config.php';

if ($_SERVER['HTTP_USER_AGENT']) {
    die;
}

$conn = pg_pconnect('user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' host=' . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' dbname=susi_main');

$query = pg_query('SELECT DISTINCT(database) FROM su_users');
$db_list = pg_fetch_all($query);
pg_close($conn);

$db_list[] = ['database' => 'new_users_db'];
// die;
foreach ($db_list as $database) {
    $conn = pg_pconnect('user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' host=' . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' dbname=' . $database['database']);
    // var_export($database['database']);
    echo 'database=' . $database['database'] . "\n";
    $query = pg_query("
		SELECT *
			FROM   pg_tables
			WHERE  schemaname='public'
			AND    tablename LIKE 'layer_zp%';");

    $zp_tables = pg_fetch_all($query);
    if ($zp_tables) {
        foreach ($zp_tables as $table) {
            $alter_query = pg_query('
				ALTER TABLE ' . $table['tablename'] . '  ADD COLUMN area_name character varying(255);
				UPDATE ' . $table['tablename'] . ' SET area_name = isak_prc_uin;');
        }
    }
    // var_dump($zp_tables);
    pg_close($conn);
}
