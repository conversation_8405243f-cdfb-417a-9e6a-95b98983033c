<?php

require_once '../config/global.config.php';

if ($_SERVER['HTTP_USER_AGENT']) {
    die;
}

$conn = pg_pconnect('user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' host=' . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' dbname=susi_main');

$query = pg_query('SELECT DISTINCT(database) FROM su_users');
$db_list = pg_fetch_all($query);
pg_close($conn);

$db_list[] = ['database' => 'new_users_db'];
// die;

foreach ($db_list as $database) {
    $conn = pg_pconnect('user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' host=' . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' dbname=' . $database['database']);
    // var_export($database['database']);
    echo 'database=' . $database['database'] . "\n";
    $query = pg_query('
			DROP TABLE IF EXISTS su_charged_renta_natura;
	        CREATE TABLE IF NOT EXISTS "public"."su_charged_renta_natura" 
	        (
                    "id" serial,
                    "renta_id" int4 references "su_charged_renta" ("id") on delete CASCADE,
                    "amount" float8,
                    "nat_type" int4 references "su_renta_types" ("id"),
                    "nat_is_converted" BOOLEAN,
                    "nat_unit_price" float8,
                    PRIMARY KEY ("id")
			);
            ');

    $subQuery = pg_query('
		   INSERT INTO su_charged_renta_natura
				(
					  "renta_id",
					  "amount",
					  "nat_type",
					  "nat_is_converted",
					  "nat_unit_price"
				)
				SELECT cr.id, cr.renta_nat, c.renta_nat_type_id, cr.nat_is_converted, cr.nat_unit_price 
				FROM su_charged_renta cr join su_contracts c on(cr.contract_id = c.id)
				WHERE c.renta_nat_type_id <> 0;
			');
    pg_close($conn);
}
