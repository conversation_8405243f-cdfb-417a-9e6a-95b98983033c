<?php

require_once '../../config/global.config.php';

if ($_SERVER['HTTP_USER_AGENT']) {
    die;
}

$dbhDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);

$sql = $dbhDev->prepare(
    'SELECT ul.table_name,u.database FROM su_users_layers ul
	INNER JOIN su_users u on u.id = ul.user_id
	WHERE ul.layer_type = 9'
);

$sql->execute();
$results = $sql->fetchAll();

for ($i = 0; $i < count($results); $i++) {
    echo 'database=' . $results[$i]['database'] . "\n";

    $sql = "ALTER TABLE {$results[$i]['table_name']} ADD COLUMN edited bool DEFAULT false NOT NULL; 
			ALTER TABLE {$results[$i]['table_name']} ADD COLUMN comment varchar(254);
			ALTER TABLE {$results[$i]['table_name']} ADD COLUMN sepp bool DEFAULT false NOT NULL;
			ALTER TABLE {$results[$i]['table_name']} ADD COLUMN crop_type varchar(127);
			ALTER TABLE {$results[$i]['table_name']} ADD COLUMN crop_genus varchar(127);
			ALTER TABLE {$results[$i]['table_name']} ADD COLUMN azot_fixed_crop bool DEFAULT false NOT NULL;
			ALTER TABLE {$results[$i]['table_name']} ADD COLUMN is_intermediate_crop bool DEFAULT false NOT NULL;
			ALTER TABLE {$results[$i]['table_name']} ADD COLUMN is_intermediate_weat_crop bool DEFAULT false NOT NULL;
			ALTER TABLE {$results[$i]['table_name']} ADD COLUMN is_tree_short_rotation bool DEFAULT false NOT NULL;
			ALTER TABLE {$results[$i]['table_name']} ADD COLUMN no_pndn bool DEFAULT false NOT NULL;
			ALTER TABLE {$results[$i]['table_name']} ADD COLUMN zdp bool DEFAULT false NOT NULL;
		    ALTER TABLE {$results[$i]['table_name']} ADD COLUMN pndp bool DEFAULT false NOT NULL;
			";

    system('psql -h ' . DEFAULT_DB_HOST . ' -p ' . DEFAULT_DB_PORT . ' -U postgres ' . $results[$i]['database'] . ' -c "' . $sql . '"', $return);
}
