<?php

require_once '../config/global.config.php';

if ($_SERVER['HTTP_USER_AGENT']) {
    die;
}

$conn = pg_pconnect('user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' host=' . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' dbname=susi_main');

$query = pg_query('SELECT DISTINCT(database) FROM su_users');
$db_list = pg_fetch_all($query);
pg_close($conn);

$db_list[] = ['database' => 'new_users_db'];
// var_dump($db_list);
// die;
foreach ($db_list as $database) {
    $conn = pg_pconnect('user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' host=' . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' dbname=' . $database['database']);
    // var_export($database['database']);
    echo 'database=' . $database['database'] . "\n";
    $query = pg_query('
			DROP TABLE IF EXISTS su_payments_natura;
            CREATE TABLE IF NOT EXISTS "public"."su_payments_natura"
			(
				"id" serial,
				"payment_id" int4 references "su_payments" ("id") on delete CASCADE,
				"nat_type" int4 references "su_renta_types" ("id"),
				"amount" float8,
				PRIMARY KEY ("id")
			);
            INSERT INTO su_payments_natura (
				payment_id,
				nat_type,
				amount
			) 
			SELECT
				p.id as payment_id,
				t.payment_nat_type as nat_type,
				p.amount_nat as amount
			FROM
				su_payments p
			INNER JOIN su_transactions t ON t.id = p.transaction_id
			WHERE t.payment_nat_type <> 0 AND p.amount_nat <> 0
			AND NOT EXISTS (SELECT 1 FROM su_payments_natura ex WHERE ex.payment_id = p.id AND ex.nat_type = t.payment_nat_type)');
    pg_close($conn);
}
