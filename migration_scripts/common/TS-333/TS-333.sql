DROP TABLE IF EXISTS su_subleases_plots_area;

--
-- PostgreSQL database dump
--

SET statement_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SET check_function_bodies = false;
SET client_min_messages = warning;

SET search_path = public, pg_catalog;

SET default_tablespace = '';

SET default_with_oids = false;

--
-- Name: su_subleases_plots_area; Type: TABLE; Schema: public; Owner: postgres; Tablespace: 
--

CREATE TABLE su_subleases_plots_area (
    id integer NOT NULL,
    sublease_id integer,
    plot_id integer,
    contract_area double precision
);


ALTER TABLE public.su_subleases_plots_area OWNER TO postgres;

--
-- Name: su_subleases_plots_area_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE su_subleases_plots_area_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.su_subleases_plots_area_id_seq OWNER TO postgres;

--
-- Name: su_subleases_plots_area_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE su_subleases_plots_area_id_seq OWNED BY su_subleases_plots_area.id;


--
-- Name: id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY su_subleases_plots_area ALTER COLUMN id SET DEFAULT nextval('su_subleases_plots_area_id_seq'::regclass);


--
-- Name: su_subleases_plots_area_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres; Tablespace: 
--

ALTER TABLE ONLY su_subleases_plots_area
    ADD CONSTRAINT su_subleases_plots_area_pkey PRIMARY KEY (id);


--
-- Name: su_subleases_plots_area_gid_fk; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY su_subleases_plots_area
    ADD CONSTRAINT su_subleases_plots_area_gid_fk FOREIGN KEY (plot_id) REFERENCES layer_kvs(gid) ON DELETE CASCADE;


--
-- Name: su_subleases_plots_area_sid; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY su_subleases_plots_area
    ADD CONSTRAINT su_subleases_plots_area_sid FOREIGN KEY (sublease_id) REFERENCES su_contracts(id) ON DELETE CASCADE;


--
-- PostgreSQL database dump complete
--


INSERT INTO su_subleases_plots_area (
	sublease_id,
	plot_id,
	contract_area
) SELECT
	sublease_id,
	plot_id,
	round(SUM(pc.contract_area::NUMERIC), 3) AS pc_area
FROM
	su_subleases_plots_contracts_rel spc
INNER JOIN su_contracts_plots_rel pc ON (pc.id = spc.pc_rel_id)
INNER JOIN su_contracts c ON (c.id = spc.sublease_id)
GROUP BY
	sublease_id,
	plot_id;


UPDATE su_subleases_plots_area
SET contract_area = round((CASE WHEN layer_kvs.document_area IS NULL THEN ST_Area(layer_kvs.geom)/1000 ELSE layer_kvs.document_area END)::numeric, 3) 
                        FROM layer_kvs 
                        WHERE layer_kvs.gid = su_subleases_plots_area.plot_id 
                        AND su_subleases_plots_area.contract_area > round((CASE WHEN layer_kvs.document_area IS NULL THEN ST_Area(layer_kvs.geom)/1000 ELSE layer_kvs.document_area END)::numeric, 3);