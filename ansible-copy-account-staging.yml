---
- hosts: "staging"
  strategy: debug
  vars:
    USER_DB: "{{ lookup('env', 'USER_DB') }}"
  environment:
    DEFAULT_DB_USERNAME: "{{ lookup('env', 'DEFAULT_DB_USERNAME') }}"
    DEFAULT_DB_PASSWORD: "{{ lookup('env', 'DEFAULT_DB_PASSWORD') }}"
    DEFAULT_DB_HOST: "{{ lookup('env', 'DEFAULT_DB_HOST') }}"
    DEFAULT_DB_PORT: "{{ lookup('env', 'DEFAULT_DB_PORT') }}"
    PROD_DB_HOST: "{{ lookup('env', 'PROD_DB_HOST') }}"
    PROD_DB_PORT: "{{ lookup('env', 'PROD_DB_PORT') }}"
    PROD_DB_USER: "{{ lookup('env', 'PROD_DB_USER') }}"
    PROD_DB_PASS: "{{ lookup('env', 'PROD_DB_PASS') }}"
  tasks:
    - name: Copy User Account
      community.docker.docker_container_exec:
        container: tf-{{ lookup('env', 'CONTAINER_NAME') }}
        command: php run.php tf:copy_user_account {{ lookup('env', 'USER_DB') }}
      register: result

    - name: Print stdout
      debug:
        var: result.stdout