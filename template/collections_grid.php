<table width="100%" style="" border="1" cellspacing="0" cellpadding="0">
    
  <tr style="font-size:14px;font-weight:bold;">
      <td rowspan="1" width="50" align="center" style="padding: 5px;">Договор</td>
      <td rowspan="1" width="60" align="center" style="padding: 5px;">Тип</td>
      <td rowspan="1" width="60" align="center" style="padding: 5px;">Наемодател</td>
      <td rowspan="1" width="80" align="center" style="padding: 5px;">Дата на влизане в сила</td>
      <td rowspan="1" width="60" align="center" style="padding: 5px;">Крайна дата</td>
      <td rowspan="1" width="100" align="center" style="padding: 5px;">Наемател/ Арендатор</td>
      <td rowspan="1" width="50" align="center" style="padding: 5px;">Дата на падеж на плащането</td>
      <td rowspan="1" width="80" align="center" style="padding: 5px;">Дължима рента в пари на декар</td>
      <td rowspan="1" width="100" align="center" style="padding: 5px;">Площ по договор (дка)</td>
      <td rowspan="1" width="67" align="center" style="padding: 5px;">Сума за получаване</td>
      <td rowspan="1" width="80" align="center" style="padding: 5px;">Общо получени до момента</td>
      <td rowspan="1" width="100" align="center" style="padding: 5px;">Остатък</td>
 </tr>

 <?php
    for ($i = 0;$i < count($pdf_data);$i++) {
        ?>
  <tr>
      <td align="center"><?php echo $pdf_data[$i]['c_num'] ?></td>
      <td align="center"><?php echo $pdf_data[$i]['nm_usage_rights'] ?></td>
      <td align="center"><?php echo $pdf_data[$i]['subleasing_farm_name'] ?></td>
      <td align="center"><?php echo $pdf_data[$i]['start_date'] ?></td>
      <td align="center"><?php echo $pdf_data[$i]['due_date'] ?></td>
      <td align="center"><?php echo $pdf_data[$i]['owner_names'] ?></td>
      <td align="center"><?php echo $pdf_data[$i]['payday'] ?></td>
      <td align="center"><?php echo $pdf_data[$i]['renta_txt'] ?></td>
      <td align="center"><?php echo $pdf_data[$i]['contract_area'] ?></td>
      <td align="center"><?php echo $pdf_data[$i]['money_to_collect_txt'] ?></td>
      <td align="center"><?php echo $pdf_data[$i]['collected_money_txt'] ?></td>
      <td align="center"><?php echo $pdf_data[$i]['unpaid_txt'] ?></td>
 </tr>
 <?php
    }
    for ($i = 0;$i < count($footer);$i++) {
        ?>
 <tr>
      <td align="center"></td>
      <td align="center"></td>
      <td align="center"></td>
      <td align="center"></td>
      <td align="center"></td>
      <td align="center"></td>
      <td align="center"></td>
      <td align="center"><?php echo $footer[$i]['renta_txt'] ?></td>
      <td align="center"><?php echo $footer[$i]['contract_area'] ?></td>
      <td align="center"><?php echo $footer[$i]['money_to_collect_txt'] ?></td>
      <td align="center"><?php echo $footer[$i]['collected_money_txt'] ?></td>
      <td align="center"><?php echo $footer[$i]['unpaid_txt'] ?></td>
 </tr>
 <?php
    }
 ?>
</table>