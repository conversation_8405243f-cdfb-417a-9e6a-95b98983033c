LAYER
    NAME '<?php echo $layername ?>'
    TYPE POLYGON

    EXTENT <?php echo $maxextent ?>
    
    PROCESSING "LABEL_NO_CLIP=on" # dobaveno za da ne se dublirat labels ot edin poligon, popadashti v dva tiles
    
    <?php if ($classitem) {
        echo "CLASSITEM '" . $classitem . "'";
    } ?>
    
    
    CONNECTIONTYPE postgis
    CONNECTION "host=<?php echo $host ?> dbname=<?php echo $dbname ?> user=<?php echo $username ?> password=<?php echo $password ?> port=<?php echo $port ?> application_name=MapServer"
    DATA "geom FROM <?php echo $query ?>" 
   
    <?php if (false !== $display_label && 0 != $display_label) { ?>	
    LABELITEM "kad_ident"
    <?php } ?>

    METADATA
      'ows_title' '<?php echo $layername ?>'
      'gml_include_items' '<?php echo $gid ?>'    
    END    
    
    COMPOSITE
      OPACITY <?php echo $transparency ?>
    END

    PROJECTION
        'proj=utm'
        'datum=WGS84'
        'zone=35'
        'units=m'
        'north'
        'no_defs'
    END
    <?php foreach ($classes as $class) { ?>
CLASS
       NAME '<?php echo trim($class['name']) ?>'
       <?php if (isset($class['expression'])) {
           echo 'EXPRESSION "' . trim($class['expression']) . '"';
       } ?>
       <?php if (isset($class['expression_numeric'])) {
           echo 'EXPRESSION ' . trim($class['expression']);
       } ?>
       
       STYLE
         WIDTH 0.91         
         OUTLINECOLOR <?php echo $class['border_color'] ?>
         
         <?php if (false != $class['color']) {
             echo 'COLOR ' . $class['color'];
         } ?>
         
       END
       
       <?php if (false !== $class['display_label']) { ?>
        LABEL # dobaveno za labels
                 ANGLE auto 
                 SIZE 10
                 ANTIALIAS TRUE	
                 COLOR 0 0 0
                 TYPE truetype
                 MAXSCALEDENOM 12500
                 POSITION auto
                 ALIGN CENTER
                 FONT monofont
         END
       <?php } ?>
    END
    <?php } ?>
END
