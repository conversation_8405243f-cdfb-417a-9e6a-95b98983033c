<p style="text-align: center;"><?php echo $title ?></p>

<table width="100%" style="font-size: 10px; empty-cells: show;" border="1" cellspacing="0" cellpadding="0">
	<tr>
	<?php
	foreach ($header as $item)
	{
		?>
		<td align="center"><?php echo $item['text'] ?></td>
		<?php
	}
	?>
	</tr>

	<?php $rows_count = count($rows);
	for ($i = 0; $i < $rows_count; $i++)
	{
		?>
	    <tr>
		<?php
		foreach ($header as $key => $value)
		{
			?>
<!--			<td><div style="width: <?php //echo $value['width'] ?>px; margin: 0px; padding: 0px; text-align: center;"><?php //echo $rows[$i][$key] ?></div></td>-->
				<td style="width: <?php echo $value['width'] ?>px; margin: 0px; padding-left: 5px;">

					<?php 
						if($key == 'number' && !isset($rows[$i][$key]))
						{
							echo $i + 1;
						}
						else
						{
							echo $rows[$i][$key];
						}
					?>

				</td>
			<?php
		}
		?>
		</tr>
		<?php
	}
	?>
</table>