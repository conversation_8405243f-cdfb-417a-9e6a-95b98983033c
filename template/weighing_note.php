<style type="text/css">
    .horizontal_dotted_line {
        border-top: 2px dotted black;
        width: 300px;
        height: 20px;
    }

    .weighing-note-table {
        page-break-inside: avoid;
    }
</style>
<table class="weighing-note-table">

    <tr>
        <td>
            <div style="text-align: center; font-weight: bold; font-size: 18px;">КАНТАРНА БЕЛЕЖКА</div>
            <div style="text-align: center;">№ ................ /<?php echo $date; ?></div>
            <table class='wighingExport' style="width: 400px; margin: auto; font-size: 12px;">
                <tr>
                    <td style="width: 30px;">
                        договор:
                    </td>
                    <td style="width: 300px;" align="left">
                        <?php echo $c_num; ?>
                    </td>
                </tr>
                <tr>
                    <td style="width: 30px;">
                        година:
                    </td>
                    <td style="width: 300px;" align="left">
                        <?php echo $farming_year; ?>
                    </td>
                </tr>
            </table>
            <table cellpadding="0" cellspacing="5" width="100%" style="margin: auto;">
                <tr>
                    <td style="width: 450px;">
                        <table cellpadding="3" cellspacing="0" width="100%" border="1">
                            <tr>
                                <td colspan="2" bgcolor="#ddd">
                                    Доставчик
                                </td>
                            </tr>
                            <tr>
                                <td style="width: 100px;">
                                    Фирма:
                                </td>
                                <td>
                                    <?php echo mb_strimwidth($provider_name, 0, 50, '...', 'utf8'); ?>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    Дан. номер:
                                </td>
                                <td>
                                    <?php echo $provider_dan_number; ?>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    Булстат:
                                </td>
                                <td>
                                    <?php echo $provider_bulstat; ?>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    МОЛ:
                                </td>
                                <td>
                                    <?php echo mb_strimwidth($provider_mol, 0, 50, '...', 'utf8'); ?>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    Адрес:
                                </td>
                                <td>
                                    <?php echo mb_strimwidth($provider_address, 0, 50, '...', 'utf8'); ?>
                                </td>
                            </tr>
                        </table>
                    </td>
                    <td style="width: 450px;">
                        <table cellpadding="3" cellspacing="0" width="100%" border="1">
                            <tr>
                                <td colspan="2" bgcolor="#ddd">
                                    Клиент
                                </td>
                            </tr>
                            <tr>
                                <td style="width: 100px;">
                                    Име:
                                </td>
                                <td>
                                    <?php echo mb_strimwidth($customer_name, 0, 50, '...', 'utf8'); ?>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    Дан. номер:
                                </td>
                                <td>
                                    <?php echo $customer_dan_number; ?>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    ЕИК/ЕГН:
                                </td>
                                <td>
                                    <?php echo $customer_bulstat; ?>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    МОЛ:
                                </td>
                                <td>
                                    <?php echo mb_strimwidth($customer_mol, 0, 50, '...', 'utf8'); ?>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    Адрес:
                                </td>
                                <td>
                                    <?php echo mb_strimwidth($customer_address, 0, 50, '...', 'utf8'); ?>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td colspan="2">
                        <table cellpadding="3" cellspacing="0" border="1">
                            <tr bgcolor="#ddd">
                                <td style="width: 45px;">
                                    №
                                </td>
                                <td style="width: 468px;">
                                    Наименование
                                </td>
                                <td style="width: 150px;">
                                    Нето
                                </td>
                                <td style="width: 200px;">
                                    Единична стойност
                                </td>
                                <td style="width: 200px;">
                                    Равностойност в пари
                                </td>
                            </tr>
                            <?php $rowsCount = count($rows); ?>
                            <?php for ($i = 0; $i < $rowsCount; $i++) { ?>
                                <tr>
                                    <td>
                                        <?php echo $i + 1; ?>
                                    </td>
                                    <td>
                                        <?php echo $rows[$i]['renta_nat_type']; ?>
                                    </td>
                                    <td>
                                        <?php echo $rows[$i]['net']; ?>
                                    </td>
                                    <td>
                                        <?php echo $rows[$i]['unit_value']; ?> 
                                    </td>
                                    <td>
                                        <?php echo BGNtoEURO($rows[$i]['amount']); ?> 
                                    </td>
                                </tr>
                                <?php
                            }
            ?>
                        </table>
                    </td>
                </tr>
            </table>
            <table cellpadding="0" cellspacing="5" style="margin: auto; margin-top: 30px; font-size: 14px; margin-bottom: 18px;">
                <tr>
                    <td style="width: 460px">
                        Предал:.............................................................
                    </td>
                    <td style="width: 460px">
                        Приел:..............................................................
                    </td>
                </tr>
            </table>
        </td>
    </tr>
    <?php if(isset($copies_left) && 0 !== $copies_left): ?>
        <tr><td class="horizontal_dotted_line"></td></tr>
    <?php endif; ?>
</table>