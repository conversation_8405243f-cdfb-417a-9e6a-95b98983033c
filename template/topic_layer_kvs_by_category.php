LAYER
        CLASSITEM 'area'
        NAME 'topic_layer_kvs_by_category'
        GROUP 'topic_layer_kvs_by_category'
        TYPE POLYGON
        
        TEMPLATE 'topic_layer_kvs_by_category'
        EXTENT <?php echo $extent; ?>        
        PROCESSING 'LABEL_NO_CLIP=on' # dobaveno za da ne se dublirat labels ot edin poligon, popadashti v dva tiles
        CONNECTIONTYPE postgis
        <?php echo $connection_string . "\n"; ?>
        DATA "geom FROM (SELECT * from topic_layer_kvs_by_category_mat_view where ekate = '<?php echo $ekate;?>' order by area desc) as subquery using unique ime_subekt using srid=32635"
        METADATA
          'ows_title' 'topic_layer_kvs_by_category'
          'gml_include_items' 'gid'    
        END

        COMPOSITE
            OPACITY 90
        END

        PROJECTION
            'proj=utm'
            'datum=WGS84'
            'zone=35'
            'units=m'
            'north'
            'no_defs'
        END
<?php for ($i = 0; $i < count($results); $i++) {
    if ($i < 49) { ?>
        CLASS
            NAME 'topic_layer_kvs_<?php echo $i; ?>'
            EXPRESSION ([area] = <?php echo preg_replace('/\s+/', '', $results[$i]['area']); ?>)
            STYLE
                WIDTH 0.91 
                OUTLINECOLOR 0 0 0
                COLOR <?php echo $GLOBALS['Layers']['colors'][$i]['map_serv_rgb'] . "\n"; ?>
            END
        END
   <?php }
    if (49 == $i) { ?>
        CLASS
            NAME 'topic_layer_kvs_<?php echo $i; ?>'
            EXPRESSION ([area] <= <?php echo preg_replace('/\s+/', '', $results[$i]['area']); ?>)
            STYLE
                WIDTH 0.91 
                OUTLINECOLOR 0 0 0
                COLOR <?php echo $GLOBALS['Layers']['colors'][$i]['map_serv_rgb'] . "\n"; ?> 
            END
        END
<?php }
    }
    if (0 == count($results)) { ?>
        CLASS
            NAME 'topic_layer_kvs_0'
            STYLE
                WIDTH 0.91 
                OUTLINECOLOR 0 0 0
                COLOR 0 0 0
            END
        END
    <?php }?>
    END 
    LAYER 
        NAME 'topic_layer_kvs_by_category_label_items' 
        GROUP 'topic_layer_kvs_by_category' 
        TYPE POINT 
         
        TEMPLATE 'topic_layer_kvs_by_category' 
        EXTENT <?php echo $extent; ?>         
        PROCESSING 'LABEL_NO_CLIP=on' # dobaveno za da ne se dublirat labels ot edin poligon, popadashti v dva tiles 
        CONNECTIONTYPE postgis 
        <?php echo $connection_string . "\n"; ?>
        DATA "geom FROM (select * from topic_layer_kvs_by_category_label_items where ekate = '<?php echo $ekate;?>') as subquery using unique ime_subekt using srid=32635"
        LABELITEM 'ime_subekt' 
        METADATA 
          'ows_title' 'topic_layer_kvs_by_category' 
          'gml_include_items' 'geom'     
        END 

        COMPOSITE
            OPACITY 90
        END

        PROJECTION 
            'proj=utm' 
            'datum=WGS84' 
            'zone=35' 
            'units=m' 
            'north' 
            'no_defs' 
        END 
        CLASS 
            NAME 'topic_layer_kvs_by_category_label_items'             
            LABEL # dobaveno za labels 
                ANGLE auto  
                SIZE 8 
                ANTIALIAS TRUE 
                COLOR 255 255 255 
                TYPE truetype 
                MAXSCALEDENOM 6500 
                POSITION auto 
                PARTIALS false 
                ALIGN CENTER 
                FONT arial 
                MINFEATURESIZE 2 
                MINDISTANCE 1 
                BUFFER 0 
                MAXSIZE 2000 
                WRAP ',' 
            END 
            LEADER 
                GRIDSTEP 25 # number of pixels between positions that are tested 
                MAXDISTANCE 30000 # distance in pixels that leader text can be drawn 
                STYLE # normal line styles are supported 
                    COLOR 255 255 255 
                    WIDTH 2 
                END 
            END 
        END 
    END 
    LAYER
        NAME 'topic_layer_kvs_by_category_outlines'
        TYPE POLYGON
        GROUP 'topic_layer_kvs_by_category'
        
        TEMPLATE 'topic_layer_kvs_by_category_outlines'
        EXTENT <?php echo $extent; ?>        
        PROCESSING "LABEL_NO_CLIP=on" # dobaveno za da ne se dublirat labels ot edin poligon, popadashti v dva tiles
        PROCESSING "CLOSE_CONNECTION=DEFER" # dobaveno za optimizacia
        CONNECTIONTYPE postgis
        <?php echo $connection_string . "\n"; ?>
        DATA "geom FROM (SELECT gid,geom,kad_ident FROM layer_kvs WHERE true AND is_edited = 'FALSE' AND ekate = '<?php echo $ekate;?>') as subquery using unique gid"
    
        #LABELITEM "kad_ident"
        METADATA
          'ows_title' 'topic_layer_kvs_by_category_outlines'
          'gml_include_items' 'gid'    
        END

        COMPOSITE
            OPACITY 70
        END

        PROJECTION
            'proj=utm'
            'datum=WGS84'
            'zone=35'
            'units=m'
            'north'
            'no_defs'
        END
        CLASS
            NAME 'layer_kvs' 
            STYLE
                WIDTH 0.91 
                OUTLINECOLOR 0 0 0                                            
            END
        END
    END