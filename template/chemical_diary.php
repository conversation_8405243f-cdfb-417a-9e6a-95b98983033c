<?php 
	$counter = 0;
	foreach ($content as $key => $item) { ?>
	<div style="text-align: center;">
		<span><b>Землище </b></span>
		<span><?php echo ($item['headers']['ekatte']) ? $item['headers']['ekatte'] : ' ......................'; ?>,</span>
		<span><b>Засята площ(дка) </b></span>
		<span><?php echo ($item['headers']['area']) ? $item['headers']['area'] : ' ......................'; ?>,</span>
		<span><b>Поле номер </b></span>
		<span><?php echo ($item['headers']['isak']) ? $item['headers']['isak'] : ' ......................'; ?>,</span>
		<span><b>Култура </b></span>
		<span><?php echo ($item['headers']['culture']) ? $item['headers']['culture'] : ' ......................'; ?></span>
	</div>
	
	<h3 style="margin-top: 15px; margin-bottom: 5px;">Проведени химични обработки</h3>
	<table border="1" cellpadding="3" cellspacing="0">
		<thead>
			<tr style="font-weight: bold;">
				<td rowspan="2">Пореден №</td>
				<td rowspan="2">Дата, месец, година</td>
				<td rowspan="2">Дата, месец, година</td>
				<td rowspan="2">Вредител (наименование)</td>
				<td rowspan="2">ПРЗ* (търговско наименование)</td>
				<td rowspan="2">Доза на дка (концентрация в %)</td>
				<td rowspan="2">Третирани площи (дка)</td>
				<td rowspan="2">Техника на приложение</td>
				<td colspan="2">Забележка</td>
				<td rowspan="2">Име, презиме, фамилия на специалиста**</td>
				<td rowspan="2">Серия и № на диплома за висше образование</td>
				<td rowspan="2">Подпис на специалиста</td>
			</tr>
			<tr style="font-weight: bold;">
				<td>Карантинен срок на продукта</td>
				<td>Най-ранна дата за прибиране или ръчно обработване на културата</td>
			</tr>
			<tr style="text-align: center; font-weight: bold;">
				<td>1</td>
				<td>2</td>
				<td>3</td>
				<td>4</td>
				<td>5</td>
				<td>6</td>
				<td>7</td>
				<td>8</td>
				<td>9</td>
				<td>10</td>
				<td>11</td>
				<td>12</td>
				<td>13</td>
			</tr>
		</thead>
		<tbody>
			<?php $count = count($item['substanceRows']);
			for ($i = 0; $i < $count; $i++) { ?>
			<tr>
				<td><?php echo $i+1; ?></td>
				<td><?php echo $item['substanceRows'][$i]['date_from']; ?></td>
				<td><?php echo $item['substanceRows'][$i]['date_to']; ?></td>
				<td><?php echo $item['substanceRows'][$i]['pest_name']; ?></td>
				<td><?php echo $item['substanceRows'][$i]['substance_name']; ?></td>
				<td><?php echo $item['substanceRows'][$i]['substance_dose']; ?></td>
				<td><?php echo $item['substanceRows'][$i]['treated_area']; ?></td>
				<td><?php echo $item['substanceRows'][$i]['apply_technic']; ?></td>
				<td><?php echo $item['substanceRows'][$i]['quarantine_period']; ?></td>
				<td><?php echo $item['substanceRows'][$i]['quarantine_until']; ?></td>
				<td></td>
				<td></td>
				<td></td>
			</tr>
			<?php } ?>
		</tbody>
	</table>

	<h3 style="margin-top: 15px; margin-bottom: 5px;">Употребени минерални и органични торове, подобрители на почвата и биологично активни вещества</h3>
	<table border="1" cellpadding="3" cellspacing="0" style="margin-bottom: 30px;">
		<thead>
			<tr style="font-weight: bold;">
				<td>№ по ред</td>
				<td>Дата, месец, година</td>
				<td>Дата, месец, година</td>
				<td>Търговско наименование (състав; акт. в-во в %)</td>
				<td>Употребено количество в натура (за дка)</td>
				<td>Наторени площи (дка)</td>
				<td>Добив (кг/дка)</td>
			</tr>
			<tr style="text-align: center; font-weight: bold;">
				<td>1</td>
				<td>2</td>
				<td>3</td>
				<td>4</td>
				<td>5</td>
				<td>6</td>
				<td>7</td>
			</tr>
		</thead>
		<tbody>
			<?php $count1 = count($item['fertilizersRows']);
			for ($i = 0; $i < $count1; $i++) { ?>
			<tr>
				<td><?php echo $i+1; ?></td>
				<td><?php echo $item['fertilizersRows'][$i]['date_from']; ?></td>
				<td><?php echo $item['fertilizersRows'][$i]['date_to']; ?></td>
				<td><?php echo $item['fertilizersRows'][$i]['substance_name']; ?></td>
				<td><?php echo $item['fertilizersRows'][$i]['used_material_qty']; ?></td>
				<td><?php echo $item['fertilizersRows'][$i]['treated_area']; ?></td>
				<td></td>
			</tr>
			<?php } ?>
		</tbody>
	</table>
<?php
	$counter++;
	if ($counter < count($content)) {
		echo '<div style="display: block; page-break-before: always;"></div>';
	}
} ?>