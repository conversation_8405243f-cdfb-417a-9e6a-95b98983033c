<html xmlns:o='urn:schemas-microsoft-com:office:office' xmlns:w='urn:schemas-microsoft-com:office:word' xmlns='http://www.w3.org/TR/REC-html40'>
<head>
<meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
<title><?php echo $title; ?></title>
<link rel=File-List href="{base_path}/filelist.xml">
<style><!--
@page {
    mso-footer: url("{base_path}/headerfooter.htm") f1;
}

<?php
    foreach ($sections as $name => $props) {
        ?>
    @page <?php echo $name ?> {
        <?php foreach ($props as $key => $value) {
            echo "{$key}: {$value};\n";
        } ?>
    }
    div.<?php echo $name ?> { page: <?php echo $name ?>; }
<?php
    }
?>


p.<PERSON>, p.<PERSON>ooter {
	font-family: C;
	text-align: right;
 }
  p.<PERSON><PERSON><PERSON><PERSON><PERSON>, li.<PERSON><PERSON><PERSON><PERSON><PERSON>, div.Mso<PERSON><PERSON>al
	{mso-style-unhide:no;
	mso-style-qformat:yes;
	mso-style-parent:"";
	margin:0cm;
	margin-bottom:.0001pt;
	mso-pagination:widow-orphan;
	font-size:12.0pt;
	font-family:"Times New Roman",serif;
	mso-fareast-font-family:"Times New Roman";
	mso-ansi-language:EN-GB;
    mso-fareast-language:EN-US;}
 table.MsoNormalTable
	{mso-style-name:"Table Normal";
	mso-tstyle-rowband-size:0;
	mso-tstyle-colband-size:0;
	mso-style-noshow:yes;
	mso-style-unhide:no;
	mso-style-parent:"";
	mso-padding-alt:0cm 5.4pt 0cm 5.4pt;
	mso-para-margin:0cm;
	mso-para-margin-bottom:.0001pt;
	mso-pagination:widow-orphan;
	font-size:10.0pt !important;
	font-family:"Times New Roman",serif;}
.page-break {
        page-break-after: always;
        clear: both;
}	
--></style>
</head>
<body>
<?php echo $content ?>
</body>
</html>
