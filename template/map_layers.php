    LAYER
        NAME '<?php
        use TF\Application\Common\Config;

        echo $layername ?>'
        <?php if ($groupname) {
            echo "GROUP '" . $groupname . "'";
        } ?>
        TYPE <?php echo $type ? $type : 'POLYGON'; ?>
        TEMPLATE '<?php echo $layername ?>'
        <?php if ($maxextent) {
            echo 'EXTENT ' . $maxextent;
        } ?>
    <?php if (!empty($validation)) {
        echo "VALIDATION\n";
        foreach ($validation as $param => $value) {
            # code...
            echo '"' . $param . '" "' . $value . '"';
        }
        echo "END\n";
    }

        ?>

        PROCESSING "LABEL_NO_CLIP=on" # dobaveno za da ne se dublirat labels ot edin poligon, popadashti v dva tiles

         <?php if ($classitem) {
             echo "CLASSITEM '" . $classitem . "'";
         } ?>

        CONNECTIONTYPE postgis
        CONNECTION "host=<?php echo $host ?> dbname=<?php echo $dbname ?> user=<?php echo $username ?> password=<?php echo $password ?> port=<?php echo $port ?> application_name=MapServer"
        DATA "geom FROM <?php echo $query ?>"

	<?php echo('' != $tag_label ? 'LABELITEM "' . $tag_label . '"' : '') ?>

        METADATA
          'ows_title' '<?php echo $layername ?>'
          'gml_include_items' 'all'
        END

    <?php if ('' != $transparency) {
        ?>
        COMPOSITE
            OPACITY <?php echo $transparency ?>
        END    
        <?php
    } ?>
        PROJECTION
            'proj=utm'
            'datum=WGS84'
            'zone=35'
            'units=m'
            'north'
            'no_defs'
        END

       
        <?php foreach ($classes as $class) {
            ?>

        CLASS
            NAME '<?php echo $class['name'] ?>'
            <?php if ($class['expression']) {
                echo 'EXPRESSION ' . $class['expression'];
            } ?>
            <?php if ($class['symbol']) {
                ?>
            STYLE
                SYMBOL "<?php echo $class['symbol']['name']; ?>"
                SIZE <?php echo $class['symbol']['size']; ?>

                <?php if (false != $class['symbol']['angle']) {
                    echo 'ANGLE ' . $class['symbol']['angle'];
                } ?>

                <?php if (false != $class['symbol']['color']) {
                    echo 'COLOR ' . $class['symbol']['color'];
                } ?>

                <?php if (false != $class['symbol']['outlinecolor']) {
                    echo 'OUTLINECOLOR ' . $class['symbol']['outlinecolor'];
                } ?>
            END
            <?php
            } else {
                ?>
            STYLE
                WIDTH <?php echo $class['width'] ? $class['width'] : Config::LAYER_BOUNDARY_DEFAULT_BORDER_WIDTH ?>
                OUTLINECOLOR <?php echo $class['border_color'] ?>

                <?php if ($class['dashed']) {
                    ?>
                PATTERN 36 14 END
                <?php
                } ?>
                <?php if (false != $class['color']) {
                    echo 'COLOR ' . $class['color'];
                } ?>

            <?php if ('' != $class['transparency']) {
                ?>
            
                OPACITY <?php echo intval($class['transparency']) ?>
            
            <?php
            } ?> 
            END
 
            <?php
            } ?>
            <?php if (true == $class['tags'] && '' != $tag_label) {
                ?>

            LABEL # dobaveno za labels
                ANGLE auto
                SIZE <?php echo $class['size']?>
                ANTIALIAS TRUE
                COLOR <?php echo $class['label_color'] ? $class['label_color'] : $class['border_color']; ?>
                TYPE truetype
                MAXSCALEDENOM 12500
                POSITION auto
                ALIGN CENTER
                FONT arial
                <?php if (null != $class['wrap_character']) {
                    ?>
                WRAP <?php echo '\'' . $class['wrap_character'] . '\''; ?>
                <?php
                } ?>
                <?php if ($class['label_style']) {
                    ?>
                STYLE
                    GEOMTRANSFORM 'labelpoly'
                    COLOR <?php echo $class['label_style']['color'] ?>
                    <?php if ($class['label_style']['padding']) {
                        ?>
                        OUTLINECOLOR <?php echo $class['label_style']['color'] ?>
                        WIDTH <?php echo $class['label_style']['padding'] ?>
                    <?php
                    } ?>
                END
                <?php
                } ?>
            END
            <?php
            } ?>
            <?php echo ($class['label_text']) ? 'TEXT ' . $class['label_text'] : ''; ?>
        END
  
        <?php
        } ?>
    END


