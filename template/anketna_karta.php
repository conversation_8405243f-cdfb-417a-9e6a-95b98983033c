<!DOCTYPE html>
<html xmlns:v="urn:schemas-microsoft-com:vml"
xmlns:o="urn:schemas-microsoft-com:office:office"
xmlns:x="urn:schemas-microsoft-com:office:excel"
xmlns="http://www.w3.org/TR/REC-html40">
<head>
	<title></title>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
	<meta name="ProgId" content="Excel.Sheet">
	<meta name="Generator" content="Microsoft Excel 15">
	<style type="text/css">
		<!--
		table {
			mso-displayed-decimal-separator: ".";
			border: thin solid;
		}
		@page {
			margin:.47in 0in .98in .53in;
			mso-header-margin:.51in;
			mso-footer-margin:.51in;
			mso-page-orientation:landscape;
		}
		-->
	</style>
	<style>
		.va {
			vertical-align: middle;
		}
		.font {
			font-family: Arial;
		}
		.header {
			font-size: 14px;
		}
		td {
			font-size: 12px;	
		}
		.border-right {
			border-right: thin solid;
		}
		.border-right-txt-middle {
			border-right: thin solid;
			vertical-align: middle;
		}
		.border-bottom {
			border-bottom: thin solid;
		}
		.border-right-bottom-txt-middle {
			border-right: thin solid;
			border-bottom: thin solid;
			vertical-align: middle;
		}
		.border-right-bottom {
			border-right: thin solid;
			border-bottom: thin solid;
		}
		.border-all {
			border: thin solid;
		}
	</style>
	<!--[if gte mso 9]>
        <xml>
            <x:ExcelWorkbook>
                <x:ExcelWorksheets>
                    <x:ExcelWorksheet>
                        <x:Name><?php echo 'Анкетна карта'; ?></x:Name>
                        <x:WorksheetOptions>
                            <x:PageSetup>         
                                <x:Layout x:Orientation="Landscape"/>         
                                <x:Header x:Margin="0.3"/>         
                                <x:Footer x:Margin="0.3"/>         
                                <x:PageMargins x:Bottom="0.75" x:Left="0.5" x:Right="0.5" x:Top="0.75"/>        
                            </x:PageSetup>        
                            <x:FitToPage/>  
                            <x:Print>         
                                <x:FitHeight>100</x:FitHeight>         
                                <x:ValidPrinterInfo/>         
                                <x:HorizontalResolution>600</x:HorizontalResolution>         
                                <x:VerticalResolution>600</x:VerticalResolution>        
                            </x:Print>
                        </x:WorksheetOptions>
                    </x:ExcelWorksheet>
                </x:ExcelWorksheets>
            </x:ExcelWorkbook>
        </xml>
        <![endif]-->
</head>
<body style="width:500px">
<table border="0" width="500" class="font">
	<?php for ($j=0; $j < count($karta); $j++) {?>
	<tr>
		<td class="border-all"><?php echo $karta[$j]['kad_ident'];?></td>
		<td class="border-all"><?php echo $karta[$j]['area_type'];?></td>
		<td class="border-all"><?php echo $karta[$j]['nm_usage_rights']; ?></td>
		<td class="border-all"><?php echo number_format($karta[$j]['area'], 3);?></td>
	</tr>
	<?php } ?>
</table>
</body>
</html>