    LAYER
        CLASSITEM '<?php echo $criteria; ?>'
        NAME 'thematic_map'
        GROUP 'thematic_map'
        TYPE POLYGON
        TEMPLATE 'thematic_map'
        EXTENT <?php echo $extent; ?>

        PROCESSING 'LABEL_NO_CLIP=on' # dobaveno za da ne se dublirat labels ot edin poligon, popadashti v dva tiles

        CONNECTIONTYPE postgis
        <?php echo $connection_string . "\n"; ?>
        DATA "geom FROM (<?php echo $query_string; ?>) as subquery using unique <?php echo $criteria; ?> using srid=32635"

        LABELITEM '<?php echo $criteria; ?>'
        METADATA
          'ows_title' 'thematic_map'
          'gml_include_items' 'gid'
        END

        OPACITY 90

        PROJECTION
            'proj=utm'
            'datum=WGS84'
            'zone=35'
            'units=m'
            'north'
            'no_defs'
        END
       
<?php for ($i = 0; $i < count($results); $i++) { ?>
        CLASS
            NAME 'thematic_map_<?php echo $i . "'\n"; ?>
            <?php if (is_null($results[$i]['original_value'])) { ?>
            EXPRESSION ([<?php echo $criteria; ?>] IS NULL)
            <?php } else { ?>
                <?php if ($is_boolean_criteria) { ?>
            EXPRESSION '<?php echo $results[$i]['original_value'] ? 't' : 'f'; ?>'
                <?php } else { ?>
            EXPRESSION '<?php echo trim($results[$i]['original_value']); ?>'
            <?php }
                } ?>
            STYLE
                WIDTH 0.91
                OUTLINECOLOR 0 0 0
                COLOR <?php echo $results[$i]['rgb_color'] . "\n"; ?>
            END
        END
   <?php }
if (0 == count($results)) { ?>
        CLASS
            NAME 'thematic_map_0'
            STYLE
                WIDTH 0.91
                OUTLINECOLOR 0 0 0
                COLOR 0 0 0
            END
        END
    <?php }?>
    END


LAYER
        CLASSITEM 'ekate'
        NAME 'thematic_map_outlines'
        TYPE POLYGON
        GROUP 'thematic_map'
        TEMPLATE 'thematic_map_outlines'
        EXTENT 125190.6162 4573142.7188 631370.3273 4887149.5823

        PROCESSING 'LABEL_NO_CLIP=on' # dobaveno za da ne se dublirat labels ot edin poligon, popadashti v dva tiles

        CONNECTIONTYPE postgis
        <?php echo $connection_string . "\n"; ?>
        
        DATA "<?php echo $kvs_query ?>"

        LABELITEM "ekate"
        METADATA
          'ows_title' 'thematic_map_outlines'
          'gml_include_items' 'gid'
        END

        COMPOSITE
            OPACITY 70
        END

        PROJECTION
            'proj=utm'
            'datum=WGS84'
            'zone=35'
            'units=m'
            'north'
            'no_defs'
        END

        <?php foreach ($ekate_styles as $ekate => $style): ?>
CLASS
            NAME 'ekate_<?php echo $ekate?>' 
            EXPRESSION '<?php echo $ekate?>'                        
            STYLE
                WIDTH 0.91 
                OUTLINECOLOR 17 17 17               
                OPACITY 100
            END
                        
            LABEL # dobaveno za labels
                ANGLE auto
                SIZE 8
                ANTIALIAS TRUE
                COLOR 0 0 0
                TYPE truetype
                MAXSCALEDENOM 12500
                POSITION auto
                PARTIALS false
                ALIGN CENTER
                FONT arial
                MINFEATURESIZE 2
                MINDISTANCE 1
                BUFFER 0
                MAXSIZE 2000
                WRAP '^'
            END
            TEXT '[label_<?php echo $ekate?>]'
            LEADER
                GRIDSTEP 25 # number of pixels between positions that are tested
                MAXDISTANCE 30000 # distance in pixels that leader text can be drawn
                STYLE # normal line styles are supported
                    COLOR 0 0 0
                    WIDTH 2
                END
            END
        END
        <?php endforeach ?>
    END