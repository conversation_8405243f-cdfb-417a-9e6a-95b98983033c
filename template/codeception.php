modules:
    config:
      \Helper\Api:
        username: 'codeception'
        password: 'qwerty'
        url: '<?php echo $cc_url; ?>/index.php?'
        dbIp: '<?php echo $cc_dbase_ip; ?>'
        dbPort: '<?php echo $cc_dbase_port ?>'
        dbUser: '<?php echo $cc_dbase_user ?>'
        dbPass: '<?php echo $cc_dbase_pass ?>'
        tfPath: '<?php echo $cc_path ?>'
        site_path: '<?php echo $cc_path ?>/public/'
        userDb: 'db_codeception'
        susiMainDb: 'susi_main_test'
      Db:
        dsn: "pgsql:host=<?php echo $cc_dbase_ip; ?>;port=<?php echo $cc_dbase_port ?>;dbname=db_codeception"
        user: '<?php echo $cc_dbase_user ?>'
        password: '<?php echo $cc_dbase_pass ?>'
        reconnect: false
        dump: tests/_data/reset_test_database.sql
        populate: true
        cleanup: false
      Helper\SusiMainDb:
        dsn: "pgsql:host=<?php echo $cc_dbase_ip; ?>;port=<?php echo $cc_dbase_port ?>;dbname=susi_main_test"
        user: '<?php echo $cc_dbase_user ?>'
        password: '<?php echo $cc_dbase_pass ?>'
        reconnect: false
        dump: tests/_data/reset_susi_main_database.sql
        populate: true
        cleanup: false
      REST:
        url: <?php echo $cc_url; ?>/
        depends: PhpBrowser
        part: Json
      Asserts:
    enabled:
      - Db
      - Asserts
      - REST