<?php echo '<?xml version="1.0" encoding="UTF-8"?>'?>
<kml xmlns="http://www.opengis.net/kml/2.2" xmlns:gx="http://www.google.com/kml/ext/2.2">
    <Document id="kvs_export">
        <name><?php echo $name; ?></name>
        <visibility>1</visibility>
        <?php foreach ($ekattes as $ekatte => $ekatteData ) { ?>
            <Style id="style<?php echo $ekatteData['ekatte']; ?>">
                <LabelStyle>
                    <scale>1.1</scale>
                    <color><?php echo $ekatteData['borderColor']; ?></color>
                    <colorMode>normal</colorMode>
                </LabelStyle>
                <PolyStyle>
                    <color><?php echo $ekatteData['plotColor']; ?></color>
                    <fill><?php echo $ekatteData['plotFill']; ?></fill>
                </PolyStyle>
                <LineStyle>
                    <color><?php echo $ekatteData['borderColor']; ?></color>
                    <width><?php echo $ekatteData['borderWith']; ?></width>
                </LineStyle>
            </Style>
        <?php } ?>
        <?php foreach ($plots as $ekatte => $ekatteData) { ?>
            <Folder>
                <name><?php echo $ekattes[$ekatte]['fullname']; ?></name>
                <visibility>1</visibility>
                <?php foreach ($ekatteData['plots'] as $plot): ?>
                    <Placemark>
                        <name><?php echo $plot['name']; ?></name>
                        <description><?php echo $plot['description']; ?></description>
                        <visibility>1</visibility>
                        <styleUrl>#style<?php echo $plot['ekatte']; ?></styleUrl>
                        <?php
                            if(empty($plot['center'])) {
                                echo $plot['geom'];
                            } else { ?>
                                <MultiGeometry>
                                    <?php
                                        echo $plot['center'];
                                        echo $plot['geom'];
                                    ?>
                                </MultiGeometry>
                           <?php } ?>

                    </Placemark>
                <?php endforeach ?>
            </Folder>
        <?php } ?>
    </Document>
</kml>
