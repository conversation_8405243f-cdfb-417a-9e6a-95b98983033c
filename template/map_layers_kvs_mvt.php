LAYER
    NAME 'kvs_mvt'
    TYPE POLYGON

    VALIDATION
        "ekatte" <?php echo '"' . $validation['ekatte'] . "\"\n" ?>
        "default_ekatte" <?php echo '"' . $validation['default_ekatte'] . "\"\n" ?>
        "plot_statuses" <?php echo '"' . $validation['plot_statuses'] . "\"\n" ?>
        "default_plot_statuses" <?php echo '"' . $validation['default_plot_statuses'] . "\"\n" ?>
        "__keycloak_uid__" <?php echo '"' . $validation['__keycloak_uid__'] . "\"\n" ?>
        "__default_keycloak_uid__" <?php echo '"' . $validation['__default_keycloak_uid__'] . "\"\n" ?>
    END

    CONNECTIONTYPE UNION
    CONNECTION "kvs_near,kvs_middle,kvs_far"
    PROCESSING "ITEMS=gid,layer_type,<?php echo implode(',', array_keys($query_columns)); ?>"

    PROJECTION 
        "init=EPSG:32635"
    END


    METADATA
        WMS_TITLE 'KVS MVT Map'
        WMS_SRS 'EPSG:32635'
        "gml_include_items" "all"
    END
END

LAYER
    NAME 'kvs_near'
    TYPE POLYGON

    VALIDATION
        "ekatte" <?php echo '"' . $validation['ekatte'] . "\"\n" ?>
        "default_ekatte" <?php echo '"' . $validation['default_ekatte'] . "\"\n" ?>
        "plot_statuses" <?php echo '"' . $validation['plot_statuses'] . "\"\n" ?>
        "default_plot_statuses" <?php echo '"' . $validation['default_plot_statuses'] . "\"\n" ?>
        "__keycloak_uid__" <?php echo '"' . $validation['__keycloak_uid__'] . "\"\n" ?>
        "__default_keycloak_uid__" <?php echo '"' . $validation['__default_keycloak_uid__'] . "\"\n" ?>
    END
  
    MAXSCALEDENOM 72223.822090
  
    CONNECTIONTYPE postgis
    CONNECTION "host=<?php echo $host ?> dbname=<?php echo $dbname ?> user=<?php echo $username ?> password=<?php echo $password ?> port=<?php echo $port ?> application_name=MapServer"

    DATA "geom from (
        SELECT 
           <?php echo implode(",\n\t", array_values($query_columns)); ?>
        FROM 
            layer_kvs
        LEFT JOIN su_layer_styles
            ON split_part(su_layer_styles.layer_id, '_', 1) = '<?php echo $layerid ?>'
            AND split_part(su_layer_styles.layer_id, '_', 2) = layer_kvs.ekate
        LEFT JOIN topic_layer_kvs_by_owner_name_label_items tkvs
            ON tkvs.gid = layer_kvs.gid
        LEFT JOIN user_contracts_count_by_plot_and_status(NULLIF('%__keycloak_uid__%', 'null')) uccps 
            ON uccps.plot_id = layer_kvs.gid
        WHERE
            ST_Intersects(layer_kvs.geom,!BOX!)
            AND (
                '%ekatte%' = 'null'
                OR '%ekatte%' = ''
                OR layer_kvs.ekate = ANY('{%ekatte%}'::text[])
            ) AND (
                '%plot_statuses%' = 'null'
                OR '%plot_statuses%' = ''
                OR get_kvs_plot_status(layer_kvs.is_edited, layer_kvs.edit_active_from)::text = ANY('{%plot_statuses%}'::text[])
            )
        GROUP BY
            layer_kvs.gid,
            uccps.contracts_count_by_status,
            su_layer_styles.id
    ) AS subq USING UNIQUE gid USING srid=32635"

    PROJECTION 
        "init=EPSG:32635"
    END
  
    METADATA
        WMS_TITLE 'KVS MVT Map Near'
        WMS_SRS 'EPSG:32635'
        "gml_include_items" "all"
    END

END


LAYER
    NAME 'kvs_middle'
    TYPE POLYGON

    VALIDATION
        "ekatte" <?php echo '"' . $validation['ekatte'] . "\"\n" ?>
        "default_ekatte" <?php echo '"' . $validation['default_ekatte'] . "\"\n" ?>
    END

    MINSCALEDENOM 72223.822090
    MAXSCALEDENOM 144447.644200
      
    CONNECTIONTYPE postgis
    CONNECTION "host=<?php echo $host ?> dbname=<?php echo $dbname ?> user=<?php echo $username ?> password=<?php echo $password ?> port=<?php echo $port ?> application_name=MapServer"
    
    DATA "geom from (
        SELECT DISTINCT ON (ekatte, masiv)
            layer_kvs_borders.ekatte as ekate,
            layer_kvs_borders.masiv,
            layer_kvs_borders.geom,
            su_layer_styles.fill_color,
            su_layer_styles.border_color,
            su_layer_styles.border_width,
            su_layer_styles.border_only,
            su_layer_styles.transparency,
            su_layer_styles.label_size,
            su_layer_styles.layer_id as style_layer_id,
            layer_kvs_borders.ekatte || '.' || layer_kvs_borders.masiv as label,
            <?php echo "{$layertype} as layer_type\n"; ?>,
            <?php echo 'null as \\"' . implode("\\\",\n\tnull as \\\"", array_filter(
                array_keys($query_columns),
                fn ($col) => !in_array($col, [
                    'ekate', 'layer_name', 'fill_color', 'border_color', 'layer_type',
                    'border_width', 'border_only', 'transparency', 'label_size', 'geom', 'masiv', 'label', 'style_layer_id',
                ])
            )) . '\\"';
        ?>
        FROM
            layer_kvs_borders
        LEFT JOIN su_layer_styles
            ON split_part(su_layer_styles.layer_id, '_', 1) = '<?php echo $layerid ?>'
            AND split_part(su_layer_styles.layer_id, '_', 2) = layer_kvs_borders.ekatte
        WHERE 
            ST_Intersects(geom,!BOX!) 
            AND border_type='masiv'::border_type_enum
            AND (
                '%ekatte%' = 'null'
                OR '%ekatte%' = ''
                OR ekatte = ANY('{%ekatte%}'::text[])
            )
    ) AS subq USING UNIQUE gid USING srid=32635"

    PROJECTION 
        "init=EPSG:32635"
    END

    METADATA
        WMS_TITLE 'KVS MVT Map Middle'
        WMS_SRS 'EPSG:32635'
        "gml_include_items" "all"
    END
END


LAYER
    NAME 'kvs_far'
    TYPE POLYGON

    VALIDATION
        "ekatte" <?php echo '"' . $validation['ekatte'] . "\"\n" ?>
        "default_ekatte" <?php echo '"' . $validation['default_ekatte'] . "\"\n" ?>
    END

    MINSCALEDENOM 144447.644200 # 13
    PROCESSING "FORCE2D=YES"
    
    CONNECTIONTYPE postgis
    CONNECTION "host=<?php echo $host ?> dbname=<?php echo $dbname ?> user=<?php echo $username ?> password=<?php echo $password ?> port=<?php echo $port ?> application_name=MapServer"

    DATA "geom from (
        SELECT DISTINCT ON (ekatte)
            layer_kvs_borders.ekatte as ekate,
            layer_kvs_borders.geom,
            su_layer_styles.fill_color,
            su_layer_styles.border_color,
            su_layer_styles.border_width,
            su_layer_styles.border_only,
            su_layer_styles.transparency,
            su_layer_styles.label_size,
            layer_kvs_borders.ekatte as label,
            su_layer_styles.layer_id as style_layer_id,
            <?php echo "{$layertype} as layer_type\n"; ?>,
            <?php echo 'null as \\"' . implode("\\\",\n\tnull as \\\"", array_filter(
                array_keys($query_columns),
                fn ($col) => !in_array($col, [
                    'ekate', 'layer_name', 'fill_color', 'border_color', 'layer_type',
                    'border_width', 'border_only', 'transparency', 'label_size', 'geom', 'label', 'style_layer_id',
                ])
            )) . '\\"';
        ?>
        FROM 
            layer_kvs_borders
        LEFT JOIN su_layer_styles
            ON split_part(su_layer_styles.layer_id, '_', 1) = '<?php echo $layerid ?>'
            AND split_part(su_layer_styles.layer_id, '_', 2) = layer_kvs_borders.ekatte
        WHERE
            ST_Intersects(geom,!BOX!)
            AND border_type='ekatte'
            AND (
                '%ekatte%' = 'null'
                OR '%ekatte%' = ''
                OR ekatte = ANY('{%ekatte%}'::text[])
            )
    ) AS subq USING UNIQUE gid USING srid=32635"

    PROJECTION 
        "init=EPSG:32635"
    END
  
    METADATA
        WMS_TITLE 'KVS MVT Map Far'
        WMS_SRS 'EPSG:32635'
        "gml_include_items" "all"
    END
END