{"mu": 0, "reports": [{"ct": "avl_unit_group", "id": 12, "n": "TF33336", "p": "{\"bind\":[16804]}", "tbl": [{"c": "", "cl": "", "f": 0, "l": "Statistics", "n": "unit_group_stats", "p": "{\"address_format\":\"583008256_10_5\",\"us_units\":0}", "s": "[\"skip_empty_rows\",\"exclude_thefts\",\"address_format\",\"us_units\"]", "sch": {"f1": 0, "f2": 0, "fl": 0, "m": 0, "t1": 0, "t2": 0, "w": 0, "y": 0}, "sl": "[\"Skip empty rows\",\"Exclude thefts from fuel consumption\",\"Mileage and fuel with accuracy to two decimal places\",\"Address\"]"}, {"c": "", "cl": "", "f": 0, "l": "Statistics", "n": "unit_group_stats_zones", "p": "", "s": "[\"desc_address\",\"all_resources\",\"address_zones\"]", "sch": {"f1": 0, "f2": 0, "fl": 0, "m": 0, "t1": 0, "t2": 0, "w": 0, "y": 0}, "sl": "[\"Add geofence description to address\",\"Specify geofences:\",\"Use geofences for addresses\"]"}, {"c": "[\"time_begin\",\"time_end\",\"duration_ival\",\"duration_in\",\"duration_stop\", \"duration_stay\",\"mileage\",\"fuel_consumption_all\",\"fuel_consumption_fls\",   \"avg_engine_rpm\" ,\"max_engine_rpm\" ,\"avg_speed\",\"max_speed\",\"visits_count\",\"avg_fuel_consumption_fls\",\"fuel_consumption_math\"]", "cl": "[\"Time Start\",\"Time End\",\"Total time\",\"Duration\",\"Idling\",\"Parkings duration\",\"Mileage\",\"Consumed\",\"Consumed by FLS\",\"Avg engine revs\",\"Max engine revs\",\"Avg speed\",\"Max speed\",\"Visits\",     \"Avg consumption by FLS\",\"Consumed by math\"]", "f": 0, "l": "Geofences", "n": "unit_group_zones_visit", "p": "{\"grouping\":\"{\\\"type\\\":\\\"unit\\\",\\\"nested\\\":{\\\"type\\\":\\\"day\\\"}}\",\"geozones\":\"996\",\"duration_format\":\"1\"}", "s": "", "sch": {"f1": 0, "f2": 0, "fl": 0, "m": 0, "t1": 0, "t2": 0, "w": 0, "y": 0}, "sl": ""}, {"c": "[\"time_begin\",\"time_end\",\"duration_ival\",\"duration\",\"eh_duration\",\"mileage\",\"absolute_mileage_begin\",\"absolute_mileage_end\",\"fuel_level_begin\",\"fuel_level_end\",\"fuel_consumption_all\",\"fuel_consumption_fls\",\"avg_speed\",\"avg_engine_rpm\",\"avg_fuel_consumption_all\",\"avg_fuel_consumption_fls\",\"fuel_consumption_math\"]", "cl": "[\"Time Start\",\"Time End\",\"Total time\",\"Duration\",\"Engine hours\",\"Mileage\",\"Initial mileage\",\"Final mileage\",\"Initial fuel level\",\"Final fuel level\",\"Consumed\",\"Consumed by FLS\",\"Avg speed\",\"Avg engine revs\",\"Avg consumption\",\"Avg consumption by FLS\",\"Consumed by math\"]", "f": 0, "l": "Trips", "n": "unit_group_trips", "p": "{\"grouping\":\"{\\\"type\\\":\\\"unit\\\",\\\"nested\\\":{\\\"type\\\":\\\"day\\\"}}\"}", "s": "", "sch": {"f1": 0, "f2": 0, "fl": 0, "m": 0, "t1": 0, "t2": 0, "w": 0, "y": 0}, "sl": ""}, {"c": "[\"duration\"]", "cl": "[\"Duration\"]", "f": 0, "l": "Parkings_in_geofence", "n": "unit_group_stays", "p": "{\"grouping\":\"{\\\"type\\\":\\\"unit\\\",\\\"nested\\\":{\\\"type\\\":\\\"day\\\"}}\",\"geozones_ex\":{\"zones\":\"999\",\"types\":\"1\"}}", "s": "", "sch": {"f1": 0, "f2": 0, "fl": 0, "m": 0, "t1": 0, "t2": 0, "w": 0, "y": 0}, "sl": ""}, {"c": "[\"duration\"]", "cl": "[\"Duration\"]", "f": 0, "l": "Parkings_clean", "n": "unit_group_stays", "p": "{\"grouping\":\"{\\\"type\\\":\\\"unit\\\",\\\"nested\\\":{\\\"type\\\":\\\"day\\\"}}\"}", "s": "", "sch": {"f1": 0, "f2": 0, "fl": 0, "m": 0, "t1": 0, "t2": 0, "w": 0, "y": 0}, "sl": ""}]}], "type": "avl_resource", "version": "b4"}