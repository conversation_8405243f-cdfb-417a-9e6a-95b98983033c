<?xml version="1.0" encoding="utf-8"?>
<xs:schema elementFormDefault="qualified"
    xmlns:xs="http://www.w3.org/2001/XMLSchema"
    version="1.0.3"
>

    <xs:element name="Payments" type="BGNPayments" />
    <xs:complexType name="BGNPayments">
        <xs:sequence>
            <xs:element name="Header" type="PaymentsHeader" minOccurs="1" maxOccurs="1" />
            <xs:element name="Rows" type="PaymentTypes" />
        </xs:sequence>
    </xs:complexType>

  <xs:complexType name="PaymentTypes">
    <xs:choice minOccurs="0" maxOccurs="unbounded">
      <xs:element minOccurs="1" name="CP" type="CP" />
      <xs:element minOccurs="1" name="BT" type="BT" />
      <xs:element minOccurs="1" name="DD" type="DD" />
    </xs:choice>
  </xs:complexType>

    <xs:element name="PaymentsHeader" type="PaymentsHeader" />
    <xs:complexType name="PaymentsHeader">
        <xs:sequence>
            <xs:element name="CustomerName" type="xs:string" minOccurs="1" maxOccurs="1" />
            <xs:element name="DocumentAccount" type="xs:string" minOccurs="1" maxOccurs="1" nillable="false" />
            <xs:element name="DocumentBankCode" type="SWIFT" />
            <xs:element name="DocumentBankName" type="xs:string" />
            <xs:element name="Currency" type="CCY"/>
            <xs:element name="TotalAmount" type="xs:decimal" minOccurs="1" maxOccurs="1" />
            <xs:element name="TotalRows" type="xs:int" minOccurs="1" maxOccurs="1" />
        </xs:sequence>
    </xs:complexType>

    <xs:element name="PaymentParty" type="PaymentParty" />
    <xs:complexType name="PaymentParty">
        <xs:sequence>
            <xs:element name="Name" type="xs:string" minOccurs="1" maxOccurs="1" nillable="false" />
            <xs:element name="IBAN" type="xs:string" minOccurs="1" maxOccurs="1" nillable="false" />
            <xs:element name="Address" type="xs:string" />
            <xs:element name="Town" type="xs:string" />
            <xs:element name="BIC" type="xs:string" />
            <xs:element name="BankName" type="xs:string" />
        </xs:sequence>
    </xs:complexType>

  <xs:element name="LiabilityPerson" type="LiabilityPerson" />
  <xs:complexType name="LiabilityPerson">
    <xs:sequence>
      <xs:element name="Name" type="xs:string" minOccurs="1" maxOccurs="1" nillable="false" />
      <xs:element name="Identification" type="xs:string" minOccurs="1" maxOccurs="1" nillable="false" />
      <xs:element name="IdentificationType" type="IdentificationType" minOccurs="1" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>

    <xs:element name="PaymentRow" type="PaymentRow" />
    <xs:complexType name="PaymentRow">
        <xs:sequence>
            <xs:element name="Amount" type="xs:decimal" minOccurs="1" maxOccurs="1" />
            <xs:element name="Reason" type="xs:string" />
            <xs:element name="Reason2" type="xs:string" />
            <xs:element ref="Charges" />
            <xs:element name="ProcessingDate" type="xs:dateTime" minOccurs="1" maxOccurs="1" />
            <xs:element name="Payroll" type="xs:boolean" minOccurs="1" maxOccurs="1" />
            <xs:element name="MassPayment" type="xs:boolean" minOccurs="1" maxOccurs="1" />
            <xs:element name="Aviso" type="xs:boolean" minOccurs="1" maxOccurs="1" />
            <xs:element name="ExternalReference" type="xs:string" minOccurs="1" maxOccurs="1" />
            <xs:element name="DirtyMoneyStatement" type="DirtyMoneyStatement" minOccurs="0" maxOccurs="1" />
        </xs:sequence>
    </xs:complexType>

    <xs:element name="CP" type="CP" />
    <xs:complexType name="CP">
        <xs:complexContent>
            <xs:extension base="PaymentRow">
                <xs:sequence>
                    <xs:element name="PaymentSystem" type="PaymentSystems" minOccurs="1" maxOccurs="1" />
                    <xs:element name="Beneficiary" type="PaymentParty" minOccurs="1" maxOccurs="1" />
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:element name="BT" type="BT" />
    <xs:complexType name="BT">
        <xs:complexContent>
            <xs:extension base="PaymentRow">
                <xs:sequence>
                    <xs:element name="PaymentSystem" type="PaymentSystems" minOccurs="1" maxOccurs="1" />
                    <xs:element name="Beneficiary" type="PaymentParty" minOccurs="1" maxOccurs="1" />
                    <xs:element name="LiabilityPerson" type="LiabilityPerson" minOccurs="1" maxOccurs="1" />
                    <xs:element name="DocumentType" type="xs:string" minOccurs="1" maxOccurs="1" nillable="false" />
                    <xs:element name="DocumentNumber" type="xs:string" minOccurs="0" maxOccurs="1"/>
                    <xs:element name="DocumentDate" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
                    <xs:element name="PeriodFROM" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
                    <xs:element name="PeriodTO" type="xs:dateTime" minOccurs="0" maxOccurs="1" />
                    <xs:element name="PayerPaymentType" type="xs:string" minOccurs="0" maxOccurs="1"/>
                    <xs:element name="PayeePaymentType" type="xs:string" minOccurs="0" maxOccurs="1"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:element name="DD" type="DD" />
    <xs:complexType name="DD">
        <xs:complexContent>
            <xs:extension base="PaymentRow">
                <xs:sequence>
                    <xs:element name="OrderingParty" type="PaymentParty" minOccurs="1" maxOccurs="1" />
                    <xs:element name="InstructionalCode" type="xs:string" />
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>

    <xs:simpleType name="IdentificationType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="BULSTAT"/>
            <xs:enumeration value="EGN"/>
            <xs:enumeration value="LNC"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:element name="SWIFT" type="SWIFT" />
    <xs:simpleType name="SWIFT">
        <xs:restriction base="xs:string">
            <xs:enumeration value="STSABGSF"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:element name="CCY" type="CCY" />
    <xs:simpleType name="CCY">
        <xs:restriction base="xs:string">
            <xs:enumeration value="BGN"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:element name="Charges" type="Charges" />
    <xs:simpleType name="Charges">
        <xs:restriction base="xs:string">
            <xs:enumeration value="OUR" />
            <xs:enumeration value="SHA" />
            <xs:enumeration value="BEN" />
        </xs:restriction>
    </xs:simpleType>

  <xs:element name="PaymentSystems" type="PaymentSystems" />
  <xs:simpleType name="PaymentSystems">
    <xs:restriction base="xs:string">
      <xs:enumeration value="BISERA" />
      <xs:enumeration value="RINGS" />
    </xs:restriction>
  </xs:simpleType>

  <xs:element name="DirtyMoneyStatement" type="DirtyMoneyStatement" />
  <xs:complexType name="DirtyMoneyStatement">
    <xs:sequence>
      <xs:element name="SignerType" type="xs:int" minOccurs="1" maxOccurs="1" />
      <xs:element name="SignerName" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="SignerAddress" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="SignerCitizenship" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="SignerEGN" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="SignerID" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="SignerPosition" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="Company" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="CompanyAddress" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="CompanyBULSTAT" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="CompanyVAT" type="xs:string" minOccurs="1" maxOccurs="1" />
      <xs:element name="DirtyMoneyOrigin" type="xs:string" minOccurs="1" maxOccurs="1" />
    </xs:sequence>
  </xs:complexType>

</xs:schema>