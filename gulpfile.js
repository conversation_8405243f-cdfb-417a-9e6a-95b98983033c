var gulp = require('gulp'),
    gp_concat = require('gulp-concat'),
    gp_rename = require('gulp-rename'),
    gp_uglify = require('gulp-uglify'),
    babel = require('gulp-babel'),
    fixmyjs = require('gulp-fixmyjs'),
    Namespace = require('gulp-namespace'),
    gutil = require('gulp-util'),
    minifyCSS = require('gulp-minify-css'),
    fs = require('fs'),
    jsFiles = JSON.parse(fs.readFileSync('./jsSources.json')),
    key,
    modulesArray = [],
    finalTasks = [];

for(key in jsFiles) {
    modulesArray.push(key);
}

modulesArray.forEach(function (module) {
    'use strict';
    var currentModule = jsFiles[module];

    gulp.task(jsFiles[module].taskName, function () {
        if (currentModule.minify) {
            return gulp.src(currentModule.src)
                .pipe(gp_concat(currentModule.fileName + '.concat.js'))
                .pipe(gulp.dest(currentModule.dest))
/*                 .pipe(babel({
                    presets: ['@babel/env']
                }))
                .pipe(fixmyjs({
                    curly: true,
                    lastsemic: false
                })) */
                .pipe(gp_rename(currentModule.fileName + '.min.js'))
                // .pipe(gp_uglify())
                .pipe(gulp.dest(currentModule.dest));
        }


        return gulp.src(currentModule.src)
            .pipe(gp_concat(currentModule.fileName + '.concat.js'))
            .pipe(gulp.dest(currentModule.dest));
    });
    finalTasks.push(currentModule.taskName);
});

gulp.task('help', function () {
    'use strict';
    var key;
    for(key in jsFiles) {
        gutil.log(gutil.colors.green(jsFiles[key].taskName) + " <- " + gutil.colors.magenta(jsFiles[key].taskDescription));
    }
});

gulp.task('default', finalTasks, function () {
    'use strict';
    gutil.log(gutil.colors.green("All done!"));
});
