---
- hosts: "servers"
  vars:
    GENERATING_MAP_FILES: "{{ lookup('env', 'GENERATING_MAP_FILES') }}"
  environment:
    DEFAULT_DB_USERNAME: "{{ lookup('env', 'DEFAULT_DB_USERNAME') }}"
    DEFAULT_DB_PASSWORD: "{{ lookup('env', 'DEFAULT_DB_PASSWORD') }}"
    DEFAULT_DB_HOST: "{{ lookup('env', 'DEFAULT_DB_HOST') }}"
    DEFAULT_DB_PORT: "{{ lookup('env', 'DEFAULT_DB_PORT') }}"
    DEFAULT_DB_DATABASE: "{{ lookup('env', 'DEFAULT_DB_DATABASE') }}"
    DBLINK_DRIVER: "{{ lookup('env', 'DBLINK_DRIVER') }}"
    DBLINK_HOST: "{{ lookup('env', 'DBLINK_HOST') }}"
    DBLINK_PORT: "{{ lookup('env', 'DB<PERSON>INK_PORT') }}"
    DBLINK_DATABASE: "{{ lookup('env', 'DBLINK_DATABASE') }}"
    DBLINK_USERNAME: "{{ lookup('env', 'DBLINK_USERNAME') }}"
    DBLINK_PASSWORD: "{{ lookup('env', 'DBLINK_PASSWORD') }}"
    WMS_SERVER: "{{ lookup('env', 'WMS_SERVER') }}"
    LOGIN3_WMS_SERVER: "{{ lookup('env', 'LOGIN3_WMS_SERVER') }}"
    RPI_SERVER: "{{ lookup('env', 'RPI_SERVER') }}"
    RPI_USERNAME: "{{ lookup('env', 'RPI_USERNAME') }}"
    RPI_PASSWORD: "{{ lookup('env', 'RPI_PASSWORD') }}"
    IMAGE_TAG_NAME: "{{ lookup('env', 'IMAGE_TAG_NAME') }}"
    DOCKERHUB_REPO_NAME: "{{ lookup('env', 'DOCKERHUB_REPO_NAME') }}"
    MAPSERVER_EXTERNAL_PORT: "{{ lookup('env', 'MAPSERVER_EXTERNAL_PORT') }}"
    CONTAINER_NAME: "{{ lookup('env', 'CONTAINER_NAME') }}"
    MAPCACHE_EXTERNAL_PORT: "{{ lookup('env', 'MAPCACHE_EXTERNAL_PORT') }}"
    API_EXTERNAL_PORT: "{{ lookup('env', 'API_EXTERNAL_PORT') }}"
    SITE_URL: "{{ lookup('env', 'SITE_URL') }}"
    PHPMAILER_HOST: "{{ lookup('env', 'PHPMAILER_HOST') }}"
    PHPMAILER_PORT: "{{ lookup('env', 'PHPMAILER_PORT') }}"
    PHPMAILER_USERNAME: "{{ lookup('env', 'PHPMAILER_USERNAME') }}"
    PHPMAILER_PASSWORD: "{{ lookup('env', 'PHPMAILER_PASSWORD') }}"
    COMMON_SERVICES_API_URL: "{{ lookup('env', 'COMMON_SERVICES_API_URL') }}"
    GENERATING_MAP_FILES: "{{ lookup('env', 'GENERATING_MAP_FILES') }}"
    CSS_CUSTOM_THEME_NAME: "{{ lookup('env', 'CSS_CUSTOM_THEME_NAME') }}"
    WMS_SERVER_INTERNAL: "{{ lookup('env', 'WMS_SERVER_INTERNAL') }}"
    WAREHOUSE_API_URL: "{{ lookup('env', 'WAREHOUSE_API_URL') }}"
    ALARMS_MAIL: "{{ lookup('env', 'ALARMS_MAIL') }}"

  tasks:
    - name: Install apache httpd  (state=present is optional)
      apt:
        name: apache2
        state: present
    - name: Enable the Apache2 module proxy
      community.general.apache2_module:
        state: present
        name: proxy
    - name: Enable the Apache2 module proxy_http
      community.general.apache2_module:
        state: present
        name: proxy_http
    - name: Enable the Apache2 module rewrite
      community.general.apache2_module:
        state: present
        name: rewrite
    - name: Enable the Apache2 module headers
      community.general.apache2_module:
        state: present
        name: headers
    - name: Install required system packages
      apt:
        pkg:
          - apt-transport-https
          - ca-certificates
          - curl
          - software-properties-common
          - python3-pip
          - virtualenv
          - python3-setuptools
        state: latest
        update_cache: true
    - name: Add Docker GPG apt Key
      apt_key:
        url: https://download.docker.com/linux/ubuntu/gpg
        state: present
    - name: Add Docker Repository
      apt_repository:
        repo: deb https://download.docker.com/linux/ubuntu focal stable
        state: present
    - name: Install docker-ce
      apt:
        name: docker-ce
        state: latest
        update_cache: true
    - name: Install docker-compose-plugin
      apt:
        name: docker-compose-plugin
        state: latest
        update_cache: true
    - name: Install Docker Module for Python
      pip:
        name: docker
    - name: Install pip docker-compose
      pip:
        name: docker-compose
    - name: Create .docker directory
      file:
        path: /root/.docker
        state: directory
        mode: 0700
        group: root
        owner: root

    - name: Create a network
      community.docker.docker_network:
        name: technofarm-net

    - name: Log in
      community.docker.docker_login:
        username: "{{ lookup('env', 'DOCKER_REGISTRY_USERNAME') }}"
        password: "{{ lookup('env', 'DOCKER_REGISTRY_PASSWORD') }}"

    - name: Docker pull technofarm
      community.docker.docker_image:
        name: technofarm/technofarm:{{ lookup('env', 'IMAGE_TAG_NAME') }}
        source: pull
        force_source: yes

    - name: Docker pull cron
      community.docker.docker_image:
        name: technofarm/technofarm-cron:{{ lookup('env', 'IMAGE_TAG_NAME') }}
        source: pull
        force_source: yes

    - name: Docker pull gdal
      community.docker.docker_image:
        name: technofarm/technofarm-gdal
        source: pull
        force_source: yes

    - name: Docker pull mapserver
      community.docker.docker_image:
        name: technofarm/technofarm-mapserver:{{ lookup('env', 'IMAGE_TAG_NAME') }}
        source: pull
        force_source: yes

    - name: Docker pull nginx
      community.docker.docker_image:
        name: technofarm/technofarm-nginx:{{ lookup('env', 'IMAGE_TAG_NAME') }}
        source: pull
        force_source: yes

    - name: Copy docker-compose.test.tf.yml
      copy:
        src: docker-compose.test.yml
        dest: ~/docker-compose.test.tf.yml
        mode: 0644

    - name: Copy env.example
      copy:
        src: .env.example
        dest: .env
        mode: 0644

    - name: DOWN the containers
      community.docker.docker_compose:
        project_src: ~/
        project_name: technofarm-test
        state: absent
        files:
          - docker-compose.test.tf.yml

    - name: Delete Volume crons
      community.docker.docker_volume:
        name: tf-{{ lookup('env', 'CONTAINER_NAME') }}-crons
        state: absent

    - name: Delete Volume maps
      community.docker.docker_volume:
        name: tf-{{ lookup('env', 'CONTAINER_NAME') }}-maps
        state: absent
      when: GENERATING_MAP_FILES == 'yes'

    - name: Delete Volume app
      community.docker.docker_volume:
        name: tf-{{ lookup('env', 'CONTAINER_NAME') }}-app
        state: absent

    - name: Create volume tf-{{ lookup('env', 'CONTAINER_NAME') }}-key-data
      community.docker.docker_volume:
        name: tf-{{ lookup('env', 'CONTAINER_NAME') }}-key-data

    - name: Create volume tf-mapcache-config
      community.docker.docker_volume:
        name: tf-mapcache-config

    - name: Create volume tf-mapcache
      community.docker.docker_volume:
        name: tf-mapcache

    - name: Create volume tf-user-files
      community.docker.docker_volume:
        name: tf-user-files

    - name: Create volume tf-logs
      community.docker.docker_volume:
        name: tf-logs

    - name: UP the containers
      community.docker.docker_compose:
        project_src: ~/
        project_name: technofarm-test
        build: yes
        recreate: always
        dependencies: no
        files:
          - docker-compose.test.tf.yml

    - name: Copy .env in "{{ lookup('env', 'CONTAINER_NAME') }}" container
      command: docker cp .env tf-"{{ lookup('env', 'CONTAINER_NAME') }}":/var/www/html/app

    - name: Copy .env in "{{ lookup('env', 'CONTAINER_NAME') }}-crontab" container
      command: docker cp .env tf-"{{ lookup('env', 'CONTAINER_NAME') }}"-crontab:/var/www/html/app

    - name: Generate folder structure
      community.docker.docker_container_exec:
        container: tf-{{ lookup('env', 'CONTAINER_NAME') }}
        command: /bin/sh -c "php run.php tf:init_folder_structure"

    - name: Generate map files - {{ lookup('env', 'GENERATING_MAP_FILES') }}
      community.docker.docker_container_exec:
        container: tf-{{ lookup('env', 'CONTAINER_NAME') }}
        command: /bin/sh -c "php run.php tf:regenerate_map_files -f"
      when: GENERATING_MAP_FILES == 'yes'

    - name: Install pip
      community.docker.docker_container_exec:
        container: tf-{{ lookup('env', 'CONTAINER_NAME') }}
        command: /bin/sh -c "pip install -r requirements.txt"

    - name: MapServer change permission
      community.docker.docker_container_exec:
        container: tf-{{ lookup('env', 'CONTAINER_NAME') }}-mapcache
        command: /bin/sh -c "chmod -R 777 /var/sig/tiles"

    - name: Image Prune
      community.docker.docker_prune:
        images: yes

    - name: Delete docker-compose.test.yml
      ansible.builtin.file:
        path: ~/docker-compose.test.tf.yml
        state: absent

    #- name: User files change permission
    #  command: chmod 777 -R /var/lib/docker/volumes/tf-user-files/_data
    #  ignore_errors: yes
