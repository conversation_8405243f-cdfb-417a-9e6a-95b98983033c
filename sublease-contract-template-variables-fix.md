# Sublease Contract Template Variables Fix - Technical Analysis

**Document Version**: 1.0  
**Date**: 2025-01-30  
**Severity**: High - Contract Export Functionality Issue  
**Affected Systems**: Contract Export, Template Processing, Sublease-Derived Contracts

## Executive Summary

Template variables `[[obobshtena_contract_area]]`, `[[obobshtena_obrabotvaema_area]]`, and `[[obobshtena_rent_area]]` were displaying as 0 when printing contracts created from sublease contracts. The root cause was identified as missing owner relationships in the database for sublease-derived contracts, causing area calculations to fail.

## Problem Statement

### Issue Description
- **Template variables** showing 0 instead of actual area values
- **Affected contracts**: Only contracts created from sublease contracts (`from_sublease` property)
- **Affected variables**: 
  - `[[obobshtena_contract_area]]` - Total contract area
  - `[[obobshtena_obrabotvaema_area]]` - Total workable area  
  - `[[obobshtena_rent_area]]` - Total rent area

### Test Case
- **Contract ID**: 2479 (sublease-derived contract)
- **Template ID**: 2
- **Expected**: Area values > 0
- **Actual**: All area variables = 0

## Root Cause Analysis

### 1. Code Path Investigation

**Contract Export Flow:**
```
index.php?contracts-rpc=contracts-exports
→ ContractsExports::exportContractBlank()
→ Lines 843-865: Area calculation logic
→ UserDbPlotsController::getFullPlotData()
→ UserDbPlotsModel::getFullPlotData()
```

### 2. Area Calculation Logic

The area calculation in `ContractsExports.php` (lines 845-849) uses:

```php
'SUM(contract_area::numeric * (po_percent / 100)) as contract_area',
'SUM(area_for_rent::numeric * (po_percent / 100)) as area_for_rent',
'SUM(coalesce(kvs_allowable_area::numeric, 0) * (po_percent / 100)) as kvs_allowable_area',
```

**Key dependency**: `po_percent` (plot owner percentage) from `su_plots_owners_rel` table.

### 3. Database Query Analysis

The `getFullPlotData()` method uses:

```sql
SELECT ... FROM layer_kvs kvs
INNER JOIN su_contracts_plots_rel pc ON(pc.plot_id = kvs.gid)
INNER JOIN su_contracts c ON(c.id = pc.contract_id)
LEFT JOIN su_plots_owners_rel po ON(po.pc_rel_id = pc.id)  -- Critical join
LEFT JOIN su_owners o ON(o.id = po.owner_id)
```

### 4. Sublease Contract Creation Issue

When creating contracts from subleases (`SubleaseContragentData.php`):

**✅ CORRECT - Plot relationships created:**
```php
// Line 301-311: Creates su_contracts_plots_rel records
'contract_id' => $recordID,
'plot_id' => $sublease_plot_data[$i]['plot_id'],
'contract_area' => $sublease_plot_data[$i]['contract_area'],
```

**❌ INCORRECT - Wrong relationship type:**
```php
// Line 318-324: Creates plotsFarmingRelTable records instead of plotsOwnersRelTable
'tablename' => $UserDbController->DbHandler->plotsFarmingRelTable,  // WRONG TABLE
```

**❌ MISSING - Owner relationships:**
- No records created in `su_plots_owners_rel`
- `po_percent` is NULL in area calculation queries
- Result: `SUM(area * NULL/100) = 0`

## Impact Assessment

### Affected Functionality
- **Contract template exports** for sublease-derived contracts
- **Area calculations** in financial reports
- **Document generation** with incorrect area values
- **Legal compliance** issues with inaccurate contract documents

### Business Impact
- **Incorrect legal documents** being generated
- **Manual workarounds** required for contract printing
- **Potential legal issues** with inaccurate area declarations
- **Loss of system reliability** for sublease workflows

## Solution Implementation

### Fix Applied

**File**: `engine/APIClasses/Subleases/SubleaseContragentData.php`  
**Lines**: 315-332

**Before (Incorrect):**
```php
$options = [
    'tablename' => $UserDbController->DbHandler->plotsFarmingRelTable,  // Wrong table
    'mainData' => [
        'pc_rel_id' => $pcRelID,
        'farming_id' => $farming_data['id'],
        'percent' => 100,
    ],
];
```

**After (Correct):**
```php
$options = [
    'tablename' => $UserDbController->DbHandler->plotsOwnersRelTable,  // Correct table
    'mainData' => [
        'pc_rel_id' => $pcRelID,
        'farming_id' => $farming_data['id'],
        'percent' => 100,
        'numerator' => 1,      // Added for fraction representation
        'denominator' => 1,    // Added for fraction representation
    ],
];
```

### Technical Changes

1. **Table Change**: `plotsFarmingRelTable` → `plotsOwnersRelTable`
2. **Added Fields**: `numerator` and `denominator` for proper fraction representation
3. **Updated Log Message**: More descriptive logging for debugging

### Why This Fix Works

1. **Correct Table**: Area calculations depend on `su_plots_owners_rel` table
2. **Proper Percentage**: `po_percent = 100` ensures full area calculation
3. **Complete Record**: All required fields populated for owner relationships
4. **Maintains Compatibility**: Doesn't break existing functionality

## Verification Steps

### 1. Test Contract Export
```bash
# Test the specific contract that was failing
POST /index.php?contracts-rpc=contracts-exports
{
    "method": "exportContractBlank",
    "params": [{
        "template_id": 2,
        "contract_id": 2479,
        "contractsData": null,
        "blank_type": "doc"
    }]
}
```

### 2. Database Verification
```sql
-- Check that owner relationships exist for sublease-derived contracts
SELECT c.id, c.c_num, c.from_sublease, po.percent
FROM su_contracts c
INNER JOIN su_contracts_plots_rel pc ON pc.contract_id = c.id
LEFT JOIN su_plots_owners_rel po ON po.pc_rel_id = pc.id
WHERE c.from_sublease IS NOT NULL
AND c.id = 2479;
```

### 3. Template Variable Check
- Verify `[[obobshtena_contract_area]]` shows actual area values
- Verify `[[obobshtena_obrabotvaema_area]]` shows workable area
- Verify `[[obobshtena_rent_area]]` shows rent area

## Data Migration Considerations

### Existing Sublease-Derived Contracts

**Issue**: Existing contracts created from subleases before this fix will still have missing owner relationships.

**Solution**: Create a migration script to fix existing data:

```sql
-- Migration script to fix existing sublease-derived contracts
INSERT INTO su_plots_owners_rel (pc_rel_id, farming_id, percent, numerator, denominator)
SELECT DISTINCT 
    pc.id as pc_rel_id,
    pf.farming_id,
    100 as percent,
    1 as numerator,
    1 as denominator
FROM su_contracts c
INNER JOIN su_contracts_plots_rel pc ON pc.contract_id = c.id
INNER JOIN su_plots_farming_rel pf ON pf.pc_rel_id = pc.id
LEFT JOIN su_plots_owners_rel po ON po.pc_rel_id = pc.id
WHERE c.from_sublease IS NOT NULL
AND po.id IS NULL;  -- Only where owner relationship doesn't exist
```

## Risk Assessment

### Low Risk Changes
- **Isolated Impact**: Only affects sublease contract creation
- **Backward Compatible**: Doesn't break existing functionality
- **Well-Tested Pattern**: Uses same approach as regular contract creation

### Potential Issues
- **Data Consistency**: Need to migrate existing contracts
- **Performance**: Additional database records created
- **Testing**: Requires thorough testing of sublease workflows

## Success Metrics

- **Template Variables**: All area variables show correct values (> 0)
- **Contract Exports**: Successful generation of sublease-derived contract documents
- **Data Integrity**: Proper owner relationships exist for all sublease contracts
- **No Regressions**: Regular contract functionality remains unaffected

## Next Steps

1. **Deploy Fix**: Apply the code change to production
2. **Run Migration**: Execute data migration script for existing contracts
3. **Test Verification**: Verify contract 2479 exports correctly
4. **Monitor**: Watch for any related issues in sublease workflows
5. **Documentation**: Update sublease contract creation documentation

## Conclusion

The issue was caused by incorrect database relationship creation during sublease contract generation. The fix ensures that proper owner relationships are created, enabling area calculations to work correctly for template variable population. This resolves the immediate issue while maintaining system integrity and compatibility.

---
*For technical questions or implementation details, contact the development team.*
