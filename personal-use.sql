SELECT pu.owner_id as owner_id,
       c.id as contract_id,
       c.c_num as c_num,
       spur.renta_type,
       spur.unit_value as personal_use_unit_value,
       srt."name" as renta_type_name,
       sum(sum(round(spur.area::numeric, 3))) over (PARTITION BY owner_id, c.id) as total_personal_use_area,
       json_agg(json_build_object('pc_rel_id', pu.pc_rel_id, 'area', round(spur.area::numeric, 3))) as personal_use_plots_area,
       sum(round(spur.area::numeric, 3)) as area_by_renta_type,
       sum(round(spur.area::numeric, 3) * spur.renta_per_dka) as personal_use_renta,

    (select sum(scpr.area_for_rent * spor.percent / 100)
     from su_contracts_plots_rel scpr
     inner join su_plots_owners_rel spor on spor.pc_rel_id = scpr.id
     where spor.owner_id = pu.owner_id
         and scpr.contract_id = coalesce(a.id, c.id) ) as total_owned_area,
       sum(round(spur.area::numeric, 3) * coalesce(spur.treatments_price, 0)) as personal_use_treatments_sum,

    (SELECT COALESCE(sum(amount), 0)
     FROM public.su_collections
     where contract_id = coalesce(a.id, c.id)
         and "type" = 2
         AND status = true
         AND farming_year IN (16)
         AND payment_data->>'owner_id' = pu.owner_id::text
         AND payment_data->>'renta_type_id' = srt.id::text ) as personal_use_paid_treatments,
       sum(round(spur.area::numeric, 3) * coalesce(spur.treatments_price, 0)) -
    (SELECT COALESCE(sum(amount), 0)
     FROM public.su_collections
     where contract_id = coalesce(a.id, c.id)
         and "type" = 2
         AND status = true
         AND farming_year IN (16)
         AND payment_data->>'owner_id' = pu.owner_id::text
         AND payment_data->>'renta_type_id' = srt.id::text ) as personal_use_unpaid_treatments,

    (select sum(stn.amount)
     from su_transactions st
     left join su_transactions_natura stn on stn.transaction_id = st.id
     left join su_payments sp on sp.transaction_id = st.id
     where st.type = 2
         and st.status = true
         and sp.owner_id = pu.owner_id
         and sp.farming_year IN (16)
         and sp.contract_id = coalesce(a.id, c.id)
         and stn.nat_type = srt.id ) as personal_use_paid_renta,
       sum(round(spur.area::numeric, 3) * spur.renta_per_dka) - coalesce (
                                                                              (select sum(stn.amount)
                                                                               from su_transactions st
                                                                               left join su_transactions_natura stn on stn.transaction_id = st.id
                                                                               left join su_payments sp on sp.transaction_id = st.id
                                                                               where st.type = 2
                                                                                   and st.status = true
                                                                                   and sp.owner_id = pu.owner_id
                                                                                   and sp.farming_year IN (16)
                                                                                   and sp.contract_id = coalesce(a.id, c.id)
                                                                                   and stn.nat_type = srt.id ), 0) as personal_use_unpaid_renta
FROM su_contracts c
LEFT JOIN su_contracts a ON(a.parent_id = c.id
                            AND a.active = true
                            AND a.start_date <= '2024-10-01'
                            AND a.due_date >= '2025-09-30')
INNER JOIN su_contracts_plots_rel pc ON(pc.contract_id = (CASE
                                                              WHEN a.id IS NULL THEN c.id
                                                              ELSE a.id
                                                          END))
LEFT JOIN su_personal_use pu ON(pu.year in (16)
                                AND pu.pc_rel_id = pc.id)
LEFT JOIN su_personal_use_rents spur on spur.pu_id = pu.id
LEFT JOIN su_renta_types srt on srt.id = spur.renta_type
WHERE spur.area > 0
    AND pc.annex_action = 'added'
    AND c.id IN(2143)
    AND (c.start_date <= '2024-10-01'
         OR a.start_date <= '2024-10-01')
    AND (c.due_date >= '2025-09-30'
         OR a.due_date >= '2025-09-30')
GROUP BY pu.owner_id,
         c.id,
         a.id,
         spur.renta_type,
         srt."name",
         srt.id,
         spur.unit_value