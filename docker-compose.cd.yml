version: "3.7"

services:
    app:
        image: ${BITBUCKET_REPO_SLUG}:${BITBUCKET_COMMIT}
        build:
            context: .
            dockerfile: ./.docker/Dockerfile
            args:
                - APP_ENV=prod
                - USER_ID=1000
                - GROUP_ID=1000
    app_cron:
        image: ${BITBUCKET_REPO_SLUG}-cron:${BITBUCKET_COMMIT}
        build:
            context: .
            dockerfile: ./.docker/crontab/Dockerfile
            args:
                - APP_ENV=prod
                - USER_ID=1000
                - GROUP_ID=1000
    mapserver:
        image: ${BITBUCKET_REPO_SLUG}-mapserver:${BITBUCKET_COMMIT}
        build:
            context: .
            dockerfile: .docker/mapserver/Dockerfile
    web:
        image: ${BITBUCKET_REPO_SLUG}-nginx:${BITBUCKET_COMMIT}
        build:
            context: .
            dockerfile: ./.docker/nginx/Dockerfile

    db-backup-s3:
        image: technofarm/db-backup-s3:${BITBUCKET_COMMIT}
        build:
            context: ./.docker/db-backup-s3/
            dockerfile: ./Dockerfile
