image: docker:dind

options:
    docker: true

definitions:

    steps:
        - step: &Build
              name: "Build"
            #   runs-on:
                #   - "self.hosted"
                #   - "linux"
                #   - "office"
              services:
                  - docker
              script:
                  - echo "Building the image and push to docker hub registry"
                  - docker login -u $DOCKERHUB_USER -p $DOCKERHUB_PASSWORD
                  - export DOCKER_BUILDKIT=0
                  - docker-compose -f docker-compose.cd.yml build --force-rm --compress
                  - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-7)
                  - export ISSUE_NUMBER=$(echo $BITBUCKET_BRANCH | sed "s/\//_/g")
                  - export IMAGE_TAG_NAME=$ISSUE_NUMBER-$BITBUCKET_COMMIT_SHORT
                  - docker tag ${BITBUCKET_REPO_SLUG}:${BITBUCKET_COMMIT} ${DOCKERHUB_REPO_NAME}:${IMA<PERSON>_TAG_NAME}
                  - docker tag ${BITBUCKET_REPO_SLUG}-nginx:${BITBUCKET_COMMIT} ${DOCKERHUB_REPO_NAME}-nginx:${IMAGE_TAG_NAME}
                  - docker tag ${BITBUCKET_REPO_SLUG}-cron:${BITBUCKET_COMMIT} ${DOCKERHUB_REPO_NAME}-cron:${IMAGE_TAG_NAME}
                  - docker tag ${BITBUCKET_REPO_SLUG}-mapserver:${BITBUCKET_COMMIT} ${DOCKERHUB_REPO_NAME}-mapserver:${IMAGE_TAG_NAME}
                  - docker tag technofarm/db-backup-s3:${BITBUCKET_COMMIT} technofarm/db-backup-s3:${IMAGE_TAG_NAME}
                  - docker push ${DOCKERHUB_REPO_NAME}:${IMAGE_TAG_NAME}
                  - docker push ${DOCKERHUB_REPO_NAME}-nginx:${IMAGE_TAG_NAME}
                  - docker push ${DOCKERHUB_REPO_NAME}-cron:${IMAGE_TAG_NAME}
                  - docker push ${DOCKERHUB_REPO_NAME}-mapserver:${IMAGE_TAG_NAME}
                  - docker push technofarm/db-backup-s3:${IMAGE_TAG_NAME}
                  - docker logout
pipelines:
    branches:
        "master":
            - step: *Build
            - step:
                  name: "Deploy on staging"
                  runs-on:
                      - "self.hosted"
                      - "linux"
                      - "office"
                  image: willhallonline/ansible:2.15-alpine-3.16
                  deployment: staging
                  services:
                      - docker
                  script:
                      - export DOCKER_REGISTRY_PASSWORD=$DOCKERHUB_PASSWORD
                      - export DOCKER_REGISTRY_USERNAME=$DOCKERHUB_USER
                      - export ANSIBLE_HOST_KEY_CHECKING="False"
                      - export ANSIBLE_DEBUG=0
                      - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-7)
                      - export ISSUE_NUMBER=$(echo $BITBUCKET_BRANCH | sed "s/\//_/g")
                      - export IMAGE_TAG_NAME=$ISSUE_NUMBER-$BITBUCKET_COMMIT_SHORT
                      - export DOCKERHUB_REPO_NAME=$DOCKERHUB_REPO_NAME
                      - export CONTAINER_NAME=$CONTAINER_NAME
                      - export GENERATING_MAP_FILES
                      - ansible-galaxy collection install community.general
                      - ansible-galaxy collection install community.docker
                      - ansible-playbook -i .ansible/hosts -l staging ansible-deploy-prod.yml
        # "release/*":
        #     - step: *Build
        #     - step:
        #           name: "Deploy on staging"
        #           runs-on:
        #               - "self.hosted"
        #               - "linux"
        #               - "office"
        #           image: willhallonline/ansible:2.15-alpine-3.16
        #           deployment: keycloak
        #           services:
        #               - docker
        #           script:
        #               - export DOCKER_REGISTRY_PASSWORD=$DOCKERHUB_PASSWORD
        #               - export DOCKER_REGISTRY_USERNAME=$DOCKERHUB_USER
        #               - export ANSIBLE_HOST_KEY_CHECKING="False"
        #               - export ANSIBLE_DEBUG=0
        #               - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-7)
        #               - export ISSUE_NUMBER=$(echo $BITBUCKET_BRANCH | sed "s/\//_/g")
        #               - export IMAGE_TAG_NAME=$ISSUE_NUMBER-$BITBUCKET_COMMIT_SHORT
        #               - export DOCKERHUB_REPO_NAME=$DOCKERHUB_REPO_NAME
        #               - export CONTAINER_NAME=$CONTAINER_NAME
        #               - export GENERATING_MAP_FILES
        #               - ansible-galaxy collection install community.general
        #               - ansible-galaxy collection install community.docker
        #               - ansible-playbook -i .ansible/hosts -l staging ansible-deploy-prod.yml
    # pull-requests:
    #   '**':
    #       - step: *Build
    #       - step:
    #             name: "Deploy on testing environment"
    #             runs-on:
    #                     - 'self.hosted'
    #                     - 'linux'
    #                     - 'office'
    #             image: willhallonline/ansible:2.15-alpine-3.16
    #             deployment: test
    #             services:
    #               - docker
    #             script:
    #               - export DOCKER_REGISTRY_PASSWORD=$DOCKERHUB_PASSWORD
    #               - export DOCKER_REGISTRY_USERNAME=$DOCKERHUB_USER
    #               - export ANSIBLE_HOST_KEY_CHECKING="False"
    #               - export ANSIBLE_DEBUG=0
    #               - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-7)
    #               - export ISSUE_NUMBER=$(echo $BITBUCKET_BRANCH | sed "s/\//_/g")
    #               - export IMAGE_TAG_NAME=$ISSUE_NUMBER-$BITBUCKET_COMMIT_SHORT
    #               - export DOCKERHUB_REPO_NAME=$DOCKERHUB_REPO_NAME
    #               - export CONTAINER_NAME=$CONTAINER_NAME
    #               - export GENERATING_MAP_FILES
    #               - ansible-galaxy collection install community.general
    #               - ansible-galaxy collection install community.docker
    #               - ansible-playbook -i .ansible/hosts -l staging ansible-deploy-test.yml
    #       - step:
    #             name: "Testing environment"
    #             services:
    #               - docker
    #             script:
    #               - pipe: atlassian/trigger-pipeline:5.0.0
    #                 variables:
    #                   BITBUCKET_USERNAME: $BITBUCKET_USERNAME
    #                   BITBUCKET_APP_PASSWORD: $BITBUCKET_APP_PASSWORD
    #                   REPOSITORY: 'technofarm-e2e'
    #                   CUSTOM_PIPELINE_NAME: 'testing-pipeline'
    custom:
        "Copy user account":
            - variables:
                  - name: USER_DB
            - step:
                  name: "Copy user account"
                  image: willhallonline/ansible:2.15-alpine-3.16
                  trigger: automatic
                  services:
                      - docker
                  script:
                      - export USER_DB
                      - export ANSIBLE_HOST_KEY_CHECKING="False"
                      - ansible-galaxy collection install community.general
                      - ansible-galaxy collection install community.docker
                      - ansible-playbook -vvv -i .ansible/hosts -l staging ansible-copy-account-staging.yml

        "Staging deployment":
            - variables:
                  - name: GENERATING_MAP_FILES
                    default: false
                    allowed-values:
                        - true
                        - false
            - parallel:
                  - step: *Build
                  - step:
                        name: "Deploy on staging"
                        runs-on:
                            - "self.hosted"
                            - "linux"
                            - "office"
                        image: willhallonline/ansible:2.15-alpine-3.16
                        deployment: keycloak
                        trigger: manual
                        services:
                            - docker
                        script:
                            - export DOCKER_REGISTRY_PASSWORD=$DOCKERHUB_PASSWORD
                            - export DOCKER_REGISTRY_USERNAME=$DOCKERHUB_USER
                            - export ANSIBLE_HOST_KEY_CHECKING="False"
                            - export ANSIBLE_DEBUG=0
                            - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-7)
                            - export ISSUE_NUMBER=$(echo $BITBUCKET_BRANCH | sed "s/\//_/g")
                            - export IMAGE_TAG_NAME=$ISSUE_NUMBER-$BITBUCKET_COMMIT_SHORT
                            - export DOCKERHUB_REPO_NAME=$DOCKERHUB_REPO_NAME
                            - export CONTAINER_NAME=$CONTAINER_NAME
                            - export GENERATING_MAP_FILES
                            - ansible-galaxy collection install community.general
                            - ansible-galaxy collection install community.docker
                            - ansible-playbook -i .ansible/hosts -l staging ansible-deploy-prod.yml

        "Clients deployments":
            - variables:
                  - name: GENERATING_MAP_FILES
                    default: false
                    allowed-values:
                        - true
                        - false
            - parallel:
                  - step: *Build

                  - step:
                        name: "Deploy on Pavlovi"
                        runs-on:
                            - "self.hosted"
                            - "linux"
                            - "office"
                        image: willhallonline/ansible:2.15-alpine-3.16
                        deployment: pavlovi
                        trigger: manual
                        services:
                            - docker
                        script:
                            - export DOCKER_REGISTRY_PASSWORD=$DOCKERHUB_PASSWORD
                            - export DOCKER_REGISTRY_USERNAME=$DOCKERHUB_USER
                            - export ANSIBLE_HOST_KEY_CHECKING="False"
                            - export ANSIBLE_DEBUG=0
                            - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-7)
                            - export ISSUE_NUMBER=$(echo $BITBUCKET_BRANCH | sed "s/\//_/g")
                            - export IMAGE_TAG_NAME=$ISSUE_NUMBER-$BITBUCKET_COMMIT_SHORT
                            - export DOCKERHUB_REPO_NAME=$DOCKERHUB_REPO_NAME
                            - export CONTAINER_NAME=$CONTAINER_NAME
                            - export GENERATING_MAP_FILES
                            - ansible-galaxy collection install community.general
                            - ansible-galaxy collection install community.docker
                            - ansible-playbook -i .ansible/hosts -l pavlovi ansible-deploy-prod.yml
                  - step:
                        name: "Deploy on FPI"
                        runs-on:
                            - "self.hosted"
                            - "linux"
                            - "office"
                        image: willhallonline/ansible:2.15-alpine-3.16
                        deployment: fpi
                        trigger: manual
                        services:
                            - docker
                        script:
                            - export DOCKER_REGISTRY_PASSWORD=$DOCKERHUB_PASSWORD
                            - export DOCKER_REGISTRY_USERNAME=$DOCKERHUB_USER
                            - export ANSIBLE_HOST_KEY_CHECKING="False"
                            - export ANSIBLE_DEBUG=0
                            - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-7)
                            - export ISSUE_NUMBER=$(echo $BITBUCKET_BRANCH | sed "s/\//_/g")
                            - export IMAGE_TAG_NAME=$ISSUE_NUMBER-$BITBUCKET_COMMIT_SHORT
                            - export DOCKERHUB_REPO_NAME=$DOCKERHUB_REPO_NAME
                            - export CONTAINER_NAME=$CONTAINER_NAME
                            - export GENERATING_MAP_FILES
                            - ansible-galaxy collection install community.general
                            - ansible-galaxy collection install community.docker
                            - ansible-playbook -i .ansible/hosts -l fpi ansible-deploy-prod.yml
                  - step:
                        name: "Deploy on Ilchovski"
                        runs-on:
                            - "self.hosted"
                            - "linux"
                            - "office"
                        image: willhallonline/ansible:2.15-alpine-3.16
                        deployment: ilchovski
                        trigger: manual
                        services:
                            - docker
                        script:
                            - export DOCKER_REGISTRY_PASSWORD=$DOCKERHUB_PASSWORD
                            - export DOCKER_REGISTRY_USERNAME=$DOCKERHUB_USER
                            - export ANSIBLE_HOST_KEY_CHECKING="False"
                            - export ANSIBLE_DEBUG=0
                            - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-7)
                            - export ISSUE_NUMBER=$(echo $BITBUCKET_BRANCH | sed "s/\//_/g")
                            - export IMAGE_TAG_NAME=$ISSUE_NUMBER-$BITBUCKET_COMMIT_SHORT
                            - export DOCKERHUB_REPO_NAME=$DOCKERHUB_REPO_NAME
                            - export CONTAINER_NAME=$CONTAINER_NAME
                            - export GENERATING_MAP_FILES
                            - ansible-galaxy collection install community.general
                            - ansible-galaxy collection install community.docker
                            - ansible-playbook -i .ansible/hosts -l ilchovski ansible-deploy-prod.yml
                  - step:
                        name: "Deploy on Karadzha"
                        runs-on:
                            - "self.hosted"
                            - "linux"
                            - "office"
                        image: willhallonline/ansible:2.15-alpine-3.16
                        deployment: karadzha
                        trigger: manual
                        services:
                            - docker
                        script:
                            - export DOCKER_REGISTRY_PASSWORD=$DOCKERHUB_PASSWORD
                            - export DOCKER_REGISTRY_USERNAME=$DOCKERHUB_USER
                            - export ANSIBLE_HOST_KEY_CHECKING="False"
                            - export ANSIBLE_DEBUG=0
                            - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-7)
                            - export ISSUE_NUMBER=$(echo $BITBUCKET_BRANCH | sed "s/\//_/g")
                            - export IMAGE_TAG_NAME=$ISSUE_NUMBER-$BITBUCKET_COMMIT_SHORT
                            - export DOCKERHUB_REPO_NAME=$DOCKERHUB_REPO_NAME
                            - export CONTAINER_NAME=$CONTAINER_NAME
                            - export GENERATING_MAP_FILES
                            - ansible-galaxy collection install community.general
                            - ansible-galaxy collection install community.docker
                            - ansible-playbook -i .ansible/hosts -l karadzha ansible-deploy-prod.yml
                  - step:
                        name: "Deploy on Agrobel"
                        runs-on:
                            - "self.hosted"
                            - "linux"
                            - "office"
                        image: willhallonline/ansible:2.15-alpine-3.16
                        deployment: agrobel
                        trigger: manual
                        services:
                            - docker
                        script:
                            - export DOCKER_REGISTRY_PASSWORD=$DOCKERHUB_PASSWORD
                            - export DOCKER_REGISTRY_USERNAME=$DOCKERHUB_USER
                            - export ANSIBLE_HOST_KEY_CHECKING="False"
                            - export ANSIBLE_DEBUG=0
                            - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-7)
                            - export ISSUE_NUMBER=$(echo $BITBUCKET_BRANCH | sed "s/\//_/g")
                            - export IMAGE_TAG_NAME=$ISSUE_NUMBER-$BITBUCKET_COMMIT_SHORT
                            - export DOCKERHUB_REPO_NAME=$DOCKERHUB_REPO_NAME
                            - export CONTAINER_NAME=$CONTAINER_NAME
                            - export GENERATING_MAP_FILES
                            - ansible-galaxy collection install community.general
                            - ansible-galaxy collection install community.docker
                            - ansible-playbook -i .ansible/hosts -l agrobel ansible-deploy-prod.yml

        "Atridi deployment":
            - variables:
                  - name: GENERATING_MAP_FILES
                    default: false
                    allowed-values:
                        - true
                        - false
            - parallel:
                  - step: *Build
                  - step:
                        name: "Deploy on Atridi"
                        runs-on:
                            - "self.hosted"
                            - "linux"
                            - "office"
                        image: willhallonline/ansible:2.15-alpine-3.16
                        deployment: atridi
                        trigger: manual
                        services:
                            - docker
                        script:
                            - export DOCKER_REGISTRY_PASSWORD=$DOCKERHUB_PASSWORD
                            - export DOCKER_REGISTRY_USERNAME=$DOCKERHUB_USER
                            - export ANSIBLE_HOST_KEY_CHECKING="False"
                            - export ANSIBLE_DEBUG=0
                            - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-7)
                            - export ISSUE_NUMBER=$(echo $BITBUCKET_BRANCH | sed "s/\//_/g")
                            - export IMAGE_TAG_NAME=$ISSUE_NUMBER-$BITBUCKET_COMMIT_SHORT
                            - export DOCKERHUB_REPO_NAME=$DOCKERHUB_REPO_NAME
                            - export CONTAINER_NAME=$CONTAINER_NAME
                            - export GENERATING_MAP_FILES
                            - ansible-galaxy collection install community.general
                            - ansible-galaxy collection install community.docker
                            - ansible-playbook -i .ansible/hosts -l atridi ansible-deploy-prod.yml

        "BIM deployment":
            - variables:
                  - name: GENERATING_MAP_FILES
                    default: false
                    allowed-values:
                        - true
                        - false
            - parallel:
                  - step: *Build
                  - step:
                        name: "Deploy on BIM"
                        runs-on:
                            - "self.hosted"
                            - "linux"
                            - "office"
                        image: willhallonline/ansible:2.15-alpine-3.16
                        deployment: bim
                        trigger: manual
                        services:
                            - docker
                        script:
                            - export DOCKER_REGISTRY_PASSWORD=$DOCKERHUB_PASSWORD
                            - export DOCKER_REGISTRY_USERNAME=$DOCKERHUB_USER
                            - export ANSIBLE_HOST_KEY_CHECKING="False"
                            - export ANSIBLE_DEBUG=0
                            - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-7)
                            - export ISSUE_NUMBER=$(echo $BITBUCKET_BRANCH | sed "s/\//_/g")
                            - export IMAGE_TAG_NAME=$ISSUE_NUMBER-$BITBUCKET_COMMIT_SHORT
                            - export DOCKERHUB_REPO_NAME=$DOCKERHUB_REPO_NAME
                            - export CONTAINER_NAME=$CONTAINER_NAME
                            - export GENERATING_MAP_FILES
                            - ansible-galaxy collection install community.general
                            - ansible-galaxy collection install community.docker
                            - ansible-playbook -i .ansible/hosts -l bim ansible-deploy-prod.yml
        "BIM deployment - Staging":
            - variables:
                  - name: GENERATING_MAP_FILES
            - parallel:
                  - step: *Build
                  - step:
                        name: "Deploy on BIM Staging"
                        runs-on:
                            - "self.hosted"
                            - "linux"
                            - "office"
                        image: willhallonline/ansible:2.15-alpine-3.16
                        deployment: bim-staging
                        trigger: manual
                        services:
                            - docker
                        script:
                            - export DOCKER_REGISTRY_PASSWORD=$DOCKERHUB_PASSWORD
                            - export DOCKER_REGISTRY_USERNAME=$DOCKERHUB_USER
                            - export ANSIBLE_HOST_KEY_CHECKING="False"
                            - export ANSIBLE_DEBUG=0
                            - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-7)
                            - export ISSUE_NUMBER=$(echo $BITBUCKET_BRANCH | sed "s/\//_/g")
                            - export IMAGE_TAG_NAME=$ISSUE_NUMBER-$BITBUCKET_COMMIT_SHORT
                            - export DOCKERHUB_REPO_NAME=$DOCKERHUB_REPO_NAME
                            - export CONTAINER_NAME=$CONTAINER_NAME
                            - export GENERATING_MAP_FILES
                            - ansible-galaxy collection install community.general
                            - ansible-galaxy collection install community.docker
                            - ansible-playbook -i .ansible/hosts -l staging ansible-deploy-prod.yml

        "Kronos deployment":
            - variables:
                  - name: GENERATING_MAP_FILES
                    default: false
                    allowed-values:
                        - true
                        - false
            - parallel:
                  - step: *Build
                  - step:
                        name: "Deploy on Kronos"
                        runs-on:
                            - "self.hosted"
                            - "linux"
                            - "office"
                        image: willhallonline/ansible:2.15-alpine-3.16
                        deployment: kronos
                        trigger: manual
                        services:
                            - docker
                        script:
                            - export DOCKER_REGISTRY_PASSWORD=$DOCKERHUB_PASSWORD
                            - export DOCKER_REGISTRY_USERNAME=$DOCKERHUB_USER
                            - export ANSIBLE_HOST_KEY_CHECKING="False"
                            - export ANSIBLE_DEBUG=0
                            - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-7)
                            - export ISSUE_NUMBER=$(echo $BITBUCKET_BRANCH | sed "s/\//_/g")
                            - export IMAGE_TAG_NAME=$ISSUE_NUMBER-$BITBUCKET_COMMIT_SHORT
                            - export DOCKERHUB_REPO_NAME=$DOCKERHUB_REPO_NAME
                            - export CONTAINER_NAME=$CONTAINER_NAME
                            - export GENERATING_MAP_FILES
                            - ansible-galaxy collection install community.general
                            - ansible-galaxy collection install community.docker
                            - ansible-playbook -i .ansible/hosts -l kronos ansible-deploy-prod.yml

        "Kronos deployment - Staging":
            - variables:
                  - name: GENERATING_MAP_FILES
            - parallel:
                  - step: *Build
                  - step:
                        name: "Deploy on Kronos Staging"
                        runs-on:
                            - "self.hosted"
                            - "linux"
                            - "office"
                        image: willhallonline/ansible:2.15-alpine-3.16
                        deployment: kronos-staging
                        trigger: manual
                        services:
                            - docker
                        script:
                            - export DOCKER_REGISTRY_PASSWORD=$DOCKERHUB_PASSWORD
                            - export DOCKER_REGISTRY_USERNAME=$DOCKERHUB_USER
                            - export ANSIBLE_HOST_KEY_CHECKING="False"
                            - export ANSIBLE_DEBUG=0
                            - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-7)
                            - export ISSUE_NUMBER=$(echo $BITBUCKET_BRANCH | sed "s/\//_/g")
                            - export IMAGE_TAG_NAME=$ISSUE_NUMBER-$BITBUCKET_COMMIT_SHORT
                            - export DOCKERHUB_REPO_NAME=$DOCKERHUB_REPO_NAME
                            - export CONTAINER_NAME=$CONTAINER_NAME
                            - export GENERATING_MAP_FILES
                            - ansible-galaxy collection install community.general
                            - ansible-galaxy collection install community.docker
                            - ansible-playbook -i .ansible/hosts -l staging ansible-deploy-prod.yml

        "KamenShishkov deployment":
            - variables:
                  - name: GENERATING_MAP_FILES
                    default: false
                    allowed-values:
                        - true
                        - false
            - parallel:
                  - step: *Build
                  - step:
                        name: "Deploy on KamenShishkov"
                        runs-on:
                            - "self.hosted"
                            - "linux"
                            - "office"
                        image: willhallonline/ansible:2.15-alpine-3.16
                        deployment: kamenshishkov
                        trigger: manual
                        services:
                            - docker
                        script:
                            - export DOCKER_REGISTRY_PASSWORD=$DOCKERHUB_PASSWORD
                            - export DOCKER_REGISTRY_USERNAME=$DOCKERHUB_USER
                            - export ANSIBLE_HOST_KEY_CHECKING="False"
                            - export ANSIBLE_DEBUG=0
                            - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-7)
                            - export ISSUE_NUMBER=$(echo $BITBUCKET_BRANCH | sed "s/\//_/g")
                            - export IMAGE_TAG_NAME=$ISSUE_NUMBER-$BITBUCKET_COMMIT_SHORT
                            - export DOCKERHUB_REPO_NAME=$DOCKERHUB_REPO_NAME
                            - export CONTAINER_NAME=$CONTAINER_NAME
                            - export GENERATING_MAP_FILES
                            - ansible-galaxy collection install community.general
                            - ansible-galaxy collection install community.docker
                            - ansible-playbook -i .ansible/hosts -l kamenshishkov ansible-deploy-prod.yml
        "Migrator deployment":
            - variables:
                  - name: GENERATING_MAP_FILES
            - parallel:
                  - step: *Build
                  - step:
                        name: "Deploy on migrator"
                        runs-on:
                            - "self.hosted"
                            - "linux"
                            - "office"
                        image: willhallonline/ansible:2.15-alpine-3.16
                        deployment: migrator
                        trigger: manual
                        services:
                            - docker
                        script:
                            - export DOCKER_REGISTRY_PASSWORD=$DOCKERHUB_PASSWORD
                            - export DOCKER_REGISTRY_USERNAME=$DOCKERHUB_USER
                            - export ANSIBLE_HOST_KEY_CHECKING="False"
                            - export ANSIBLE_DEBUG=0
                            - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-7)
                            - export ISSUE_NUMBER=$(echo $BITBUCKET_BRANCH | sed "s/\//_/g")
                            - export IMAGE_TAG_NAME=$ISSUE_NUMBER-$BITBUCKET_COMMIT_SHORT
                            - export DOCKERHUB_REPO_NAME=$DOCKERHUB_REPO_NAME
                            - export CONTAINER_NAME=$CONTAINER_NAME
                            - export GENERATING_MAP_FILES
                            - ansible-galaxy collection install community.general
                            - ansible-galaxy collection install community.docker
                            - ansible-playbook -i .ansible/hosts -l migrator ansible-deploy-prod.yml

        "Technofarm Kubernetes deployment":
            - variables:
                  - name: GENERATING_MAP_FILES
                    default: false
                    allowed-values:
                        - true
                        - false
            - parallel:
                  - step: *Build
                  - step:
                        name: "Deploy to Agrimi"
                        runs-on:
                            - "self.hosted"
                            - "linux"
                            - "office"
                        image: atlassian/pipelines-kubectl
                        deployment: agrimi
                        trigger: manual
                        services:
                            - docker
                        script:
                            - export DOCKER_REGISTRY_PASSWORD=$DOCKERHUB_PASSWORD
                            - export DOCKER_REGISTRY_USERNAME=$DOCKERHUB_USER
                            - export ANSIBLE_HOST_KEY_CHECKING="False"
                            - export ANSIBLE_DEBUG=0
                            - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-7)
                            - export ISSUE_NUMBER=$(echo $BITBUCKET_BRANCH | sed "s/\//_/g")
                            - export IMAGE_TAG_NAME=$ISSUE_NUMBER-$BITBUCKET_COMMIT_SHORT
                            - export DOCKERHUB_REPO_NAME=$DOCKERHUB_REPO_NAME
                            - export CONTAINER_NAME=$CONTAINER_NAME
                            - export API_EXTERNAL_PORT=$API_EXTERNAL_PORT
                            - export TECHNOFARM_URL=$TECHNOFARM_URL
                            - export CONFIGMAP=$CONFIGMAP
                            - export DEFAULT_DB_USERNAME=$DEFAULT_DB_USERNAME
                            - export DEFAULT_DB_PASSWORD=$DEFAULT_DB_PASSWORD
                            - export DEFAULT_DB_HOST=$DEFAULT_DB_HOST
                            - export DEFAULT_DB_PORT=$DEFAULT_DB_PORT
                            - export DEFAULT_DB_DATABASE=$DEFAULT_DB_DATABASE
                            - export DBLINK_DRIVER=$DBLINK_DRIVER
                            - export DBLINK_HOST=$DBLINK_HOST
                            - export DBLINK_PORT=$DBLINK_PORT
                            - export DBLINK_DATABASE=$DBLINK_DATABASE
                            - export DBLINK_USERNAME=$DBLINK_USERNAME
                            - export DBLINK_PASSWORD=$DBLINK_PASSWORD
                            - export WMS_SERVER=$WMS_SERVER
                            - export LOGIN3_WMS_SERVER=$LOGIN3_WMS_SERVER
                            - export RPI_SERVER=$RPI_SERVER
                            - export RPI_USERNAME=$RPI_USERNAME
                            - export RPI_PASSWORD=$RPI_PASSWORD
                            - export SITE_URL=$SITE_URL
                            - export SITE_BASE_HREF=$SITE_BASE_HREF
                            - export PHPMAILER_HOST=$PHPMAILER_HOST
                            - export PHPMAILER_PORT=$PHPMAILER_PORT
                            - export PHPMAILER_USERNAME=$PHPMAILER_USERNAME
                            - export PHPMAILER_PASSWORD=$PHPMAILER_PASSWORD
                            - export COMMON_SERVICES_API_URL=$COMMON_SERVICES_API_URL
                            - export WAREHOUSE_API_URL=$WAREHOUSE_API_URL
                            - export ALARMS_MAIL=$ALARMS_MAIL
                            - export KEYKLOACK_LOGIN_REQUIRED=$KEYKLOACK_LOGIN_REQUIRED
                            - export KEYCLOAK_AUTH_SERVER_URL=$KEYCLOAK_AUTH_SERVER_URL
                            - export KEYCLOAK_REALM=$KEYCLOAK_REALM
                            - export KEYCLOAK_CLIENT_ID=$KEYCLOAK_CLIENT_ID
                            - export KEYCLOAK_CLIENT_SECRET=$KEYCLOAK_CLIENT_SECRET
                            - export KEYCLOAK_REDIRECT_URI=$KEYCLOAK_REDIRECT_URI
                            - export KEYCLOAK_LOGOUT_REDIRECT_URI=$KEYCLOAK_LOGOUT_REDIRECT_URI
                            - export KEYCLOAK_ALGORYTHM=$KEYCLOAK_ALGORYTHM
                            - export CMS_API_URL=$CMS_API_URL
                            - export KAIS_SESSION_ID=$KAIS_SESSION_ID
                            - export KAIS_CA_TOKEN=$KAIS_CA_TOKEN
                            - export LEGACY_MODE=$LEGACY_MODE
                            - export REDIS_HOST=$REDIS_HOST
                            - export REDIS_PORT=$REDIS_PORT
                            - export CSV2XLS_PATH=$CSV2XLS_PATH
                            - export NFS_SERVER=$NFS_SERVER
                            - export NFS_DIR_MAPS=$NFS_DIR_MAPS
                            - export NFS_DIR_FILES=$NFS_DIR_FILES
                            - export NFS_DIR_LOGS=$NFS_DIR_LOGS
                            - export NFS_DIR_STATIC=$NFS_DIR_STATIC
                            - export NFS_DIR_MAPCACHE=$NFS_DIR_MAPCACHE
                            - export GENERATING_MAP_FILES=$GENERATING_MAP_FILES
                            - export CONFIGMAPFILES=$CONFIGMAPFILES
                            - export KUBE_TOKEN=$KUBE_TOKEN
                            - export KUBE_CA=$KUBE_CA
                            - export CONFIGMAP=$CONFIGMAP
                            - export NAMESPACE=$NAMESPACE
                            - echo $KUBE_TOKEN | base64 -d > ./kube_token
                            - echo $KUBE_CA | base64 -d > ./kube_ca
                            - export GEOSCAN_APP_URL=$GEOSCAN_APP_URL
                            - export MAX_KVS_PROCESSING_FILES=$MAX_KVS_PROCESSING_FILES
                            - export MAIN_NAVIGATION_INSTANCE=$MAIN_NAVIGATION_INSTANCE
                            - export MS_MAP_PATTERN=$MS_MAP_PATTERN
                            - export MS_DEBUGLEVEL=$MS_DEBUGLEVEL
                            - export MIN_PROCESSES=$MIN_PROCESSES
                            - export MAX_PROCESSES=$MAX_PROCESSES
                            - export APACHE_RUN_USER=$APACHE_RUN_USER
                            - export APACHE_RUN_GROUP=$APACHE_RUN_GROUP
                            - export MAPSERVER_CATCH_SEGV=$MAPSERVER_CATCH_SEGV
                            - kubectl config set-cluster $KUBE_CLUSTER --server=$KUBE_SERVER --certificate-authority="$(pwd)/kube_ca"
                            - kubectl config set-credentials $KUBE_SA --token="$(cat ./kube_token)"
                            - kubectl config set-context $NAMESPACE --cluster=$KUBE_CLUSTER --user=$KUBE_SA
                            - kubectl config use-context $NAMESPACE
                            - sed -i "s|REPLACE_DEFAULT_DB_USERNAME|${DEFAULT_DB_USERNAME}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_DEFAULT_DB_PASSWORD|${DEFAULT_DB_PASSWORD}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_DEFAULT_DB_HOST|${DEFAULT_DB_HOST}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_DEFAULT_DB_PORT|${DEFAULT_DB_PORT}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_DEFAULT_DB_DATABASE|${DEFAULT_DB_DATABASE}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_DBLINK_DRIVER|${DBLINK_DRIVER}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_DBLINK_HOST|${DBLINK_HOST}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_DBLINK_PORT|${DBLINK_PORT}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_DBLINK_DATABASE|${DBLINK_DATABASE}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_DBLINK_USERNAME|${DBLINK_USERNAME}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_DBLINK_PASSWORD|${DBLINK_PASSWORD}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_WMS_SERVER|${WMS_SERVER}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_LOGIN3_WMS_SERVER|${LOGIN3_WMS_SERVER}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_RPI_SERVER|${RPI_SERVER}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_RPI_USERNAME|${RPI_USERNAME}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_RPI_PASSWORD|${RPI_PASSWORD}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_SITE_BASE_HREF|${SITE_BASE_HREF}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_SITE_URL|${SITE_URL}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_PHPMAILER_HOST|${PHPMAILER_HOST}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_PHPMAILER_PORT|${PHPMAILER_PORT}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_PHPMAILER_USERNAME|${PHPMAILER_USERNAME}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_PHPMAILER_PASSWORD|${PHPMAILER_PASSWORD}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_COMMON_SERVICES_API_URL|${COMMON_SERVICES_API_URL}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_WAREHOUSE_API_URL|${WAREHOUSE_API_URL}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_ALARMS_MAIL|${ALARMS_MAIL}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYKLOACK_LOGIN_REQUIRED|${KEYKLOACK_LOGIN_REQUIRED}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_AUTH_SERVER_URL|${KEYCLOAK_AUTH_SERVER_URL}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_REALM|${KEYCLOAK_REALM}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_CLIENT_ID|${KEYCLOAK_CLIENT_ID}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_CLIENT_SECRET|${KEYCLOAK_CLIENT_SECRET}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_REDIRECT_URI|${KEYCLOAK_REDIRECT_URI}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_LOGOUT_REDIRECT_URI|${KEYCLOAK_LOGOUT_REDIRECT_URI}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_ALGORYTHM|${KEYCLOAK_ALGORYTHM}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_CMS_API_URL|${CMS_API_URL}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_GEOSCAN_CMS_BASE_URI|${GEOSCAN_CMS_BASE_URI}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KAIS_SESSION_ID|${KAIS_SESSION_ID}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KAIS_CA_TOKEN|${KAIS_CA_TOKEN}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_LEGACY_MODE|${LEGACY_MODE}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYKLOACK_KVS_STORE_AUTH_SERVER_URL|${KEYKLOACK_KVS_STORE_AUTH_SERVER_URL}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYKLOACK_KVS_STORE_CLIENT_ID|${KEYKLOACK_KVS_STORE_CLIENT_ID}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYKLOACK_KVS_STORE_CLIENT_SECRET|${KEYKLOACK_KVS_STORE_CLIENT_SECRET}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYKLOACK_KVS_STORE_REALM|${KEYKLOACK_KVS_STORE_REALM}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KVS_STORE_URL|${KVS_STORE_URL}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KVS_STORE_CALLBACK_URL|${KVS_STORE_CALLBACK_URL}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_M2M_CLIENT_ID|${KEYCLOAK_M2M_CLIENT_ID}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_M2M_CLIENT_SECRET|${KEYCLOAK_M2M_CLIENT_SECRET}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_APP_ENV|${APP_ENV}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_HELPHERO_ID|${HELPHERO_ID}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_POSTHOG_API_KEY|${POSTHOG_API_KEY}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_MAX_KVS_PROCESSING_FILES|${MAX_KVS_PROCESSING_FILES}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_GEOSCAN_APP_URL|${GEOSCAN_APP_URL}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_MAIN_NAVIGATION_INSTANCE|${MAIN_NAVIGATION_INSTANCE}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_MS_MAP_PATTERN|${MS_MAP_PATTERN}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_MS_DEBUGLEVEL|${MS_DEBUGLEVEL}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_MIN_PROCESSES|${MIN_PROCESSES}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_MAX_PROCESSES|${MAX_PROCESSES}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_APACHE_RUN_USER|${APACHE_RUN_USER}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_APACHE_RUN_GROUP|${APACHE_RUN_GROUP}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_MAPSERVER_CATCH_SEGV|${MAPSERVER_CATCH_SEGV}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_CSV2XLS_PATH|${CSV2XLS_PATH}|g" .kube/configmap-env-file
                            - kubectl --namespace ${NAMESPACE} create configmap ${CONFIGMAP} --from-env-file=.kube/configmap-env-file --dry-run=client -o yaml | kubectl --namespace ${NAMESPACE} apply -f -
                            #Begin nginx configmap
                            - echo "CONTAINER_NAME=${CONTAINER_NAME}" > .kube/nginx-configmap
                            - echo "MAPSERVER_EXTERNAL_PORT=${MAPSERVER_EXTERNAL_PORT}" >> .kube/nginx-configmap
                            - echo "MAPCACHE_EXTERNAL_PORT=${MAPCACHE_EXTERNAL_PORT}" >> .kube/nginx-configmap
                            - kubectl --namespace ${NAMESPACE} create configmap ${NGINX_CONFIGMAP} --from-env-file=.kube/nginx-configmap --dry-run=client -o yaml | kubectl --namespace ${NAMESPACE} apply -f -
                            #End nginx configmap
                            - sed -i "s|REPLACE_REDIS_HOST|${REDIS_HOST}|g" .kube/php.ini
                            - sed -i "s|REPLACE_REDIS_PORT|${REDIS_PORT}|g" .kube/php.ini
                            - kubectl --namespace ${NAMESPACE} create configmap ${CONFIGMAPFILES} --from-file=.kube/php.ini --from-file=.env.example --dry-run=client -o yaml | kubectl --namespace ${NAMESPACE} apply -f -
                            - sed -i "s|REPLACE_CONFIG_MAP_FILES|${CONFIGMAPFILES}|g" .kube/deployment-*.yaml
                            - sed -i "s|REPLACE_CONFIG_MAP|${CONFIGMAP}|g" .kube/deployment-*.yaml
                            - sed -i "s|REPLACE_NGINX_CONFIG_MAP|${NGINX_CONFIGMAP}|g" .kube/deployment-*.yaml
                            - sed -i "s|REPLACE_NAMESPACE|${NAMESPACE}|g" .kube/*.yaml
                            - sed -i "s|REPLACE_REPO|${DOCKERHUB_REPO_NAME}|g" .kube/deployment-*.yaml
                            - sed -i "s|REPLACE_TAG|${IMAGE_TAG_NAME}|g" .kube/deployment-*.yaml
                            - sed -i "s|REPLACE_NFS_SERVER|${NFS_SERVER}|g" .kube/deployment-*.yaml
                            - sed -i "s|REPLACE_NFS_DIR_MAPS|${NFS_DIR_MAPS}|g" .kube/deployment-*.yaml
                            - sed -i "s|REPLACE_NFS_DIR_FILES|${NFS_DIR_FILES}|g" .kube/deployment-*.yaml
                            - sed -i "s|REPLACE_NFS_DIR_LOGS|${NFS_DIR_LOGS}|g" .kube/deployment-*.yaml
                            - sed -i "s|REPLACE_NFS_DIR_STATIC|${NFS_DIR_STATIC}|g" .kube/deployment-*.yaml
                            - sed -i "s|REPLACE_NFS_DIR_MAPCACHE|${NFS_DIR_MAPCACHE}|g" .kube/deployment-*.yaml
                            - sed -i "s|REPLACE_KUBE_REPLICAS|${KUBE_REPLICAS}|g" .kube/*.yaml
                            - sed -i "s|REPLACE_TAG|${IMAGE_TAG_NAME}|g" .kube/cronjobs-backup-dbs.yaml
                            - sed -i "s|REPLACE_CONFIG_MAP_FILES|${CONFIGMAPFILES}|g" .kube/cronjobs-*.yaml
                            - sed -i "s|REPLACE_CONFIG_MAP|${CONFIGMAP}|g" .kube/cronjobs-*.yaml
                            - sed -i "s|REPLACE_REPO|${DOCKERHUB_REPO_NAME}|g" .kube/cronjobs-*.yaml
                            - sed -i "s|REPLACE_TAG|${IMAGE_TAG_NAME}|g" .kube/cronjobs-*.yaml
                            - sed -i "s|REPLACE_NFS_SERVER|${NFS_SERVER}|g" .kube/cronjobs-*.yaml
                            - sed -i "s|REPLACE_NFS_DIR_MAPS|${NFS_DIR_MAPS}|g" .kube/cronjobs-*.yaml
                            - sed -i "s|REPLACE_NFS_DIR_FILES|${NFS_DIR_FILES}|g" .kube/cronjobs-*.yaml
                            - sed -i "s|REPLACE_NFS_DIR_LOGS|${NFS_DIR_LOGS}|g" .kube/cronjobs-*.yaml
                            - sed -i "s|REPLACE_NFS_DIR_STATIC|${NFS_DIR_STATIC}|g" .kube/cronjobs-*.yaml
                            - sed -i "s|REPLACE_NFS_DIR_MAPCACHE|${NFS_DIR_MAPCACHE}|g" .kube/cronjobs-*.yaml
                            - kubectl --namespace ${NAMESPACE} apply -f .kube/deployment-tf-technofarm.yaml
                            - kubectl --namespace ${NAMESPACE} apply -f .kube/deployment-tf-technofarm-nginx.yaml
                            - kubectl --namespace ${NAMESPACE} apply -f .kube/deployment-tf-technofarm-mapserver.yaml
                            - kubectl --namespace ${NAMESPACE} apply -f .kube/cronjobs-2am.yaml
                            - kubectl --namespace ${NAMESPACE} apply -f .kube/cronjobs-delete-downloaded-files.yaml
                            - kubectl --namespace ${NAMESPACE} apply -f .kube/cronjobs-everymin.yaml
                            - kubectl --namespace ${NAMESPACE} -n $NAMESPACE rollout restart deployment/tf-technofarm
                            - kubectl --namespace ${NAMESPACE} -n $NAMESPACE rollout restart deployment/tf-technofarm-nginx
                            - kubectl --namespace ${NAMESPACE} -n $NAMESPACE rollout restart deployment/tf-technofarm-mapserver
                            - kubectl --namespace ${NAMESPACE} apply -f .kube/service-tf-technofarm.yaml
                            - kubectl --namespace ${NAMESPACE} apply -f .kube/service-tf-technofarm-nginx.yaml
                            - kubectl --namespace ${NAMESPACE} apply -f .kube/service-tf-technofarm-mapserver.yaml
                            - if [ "$GENERATING_MAP_FILES" == 'yes' ]; then echo "Generate map files"; fi
                            - kubectl --namespace ${NAMESPACE} apply -f .kube/cronjobs-backup-dbs.yaml
                            - kubectl --namespace ${NAMESPACE} apply -f .kube/secret-rclone-s3.yaml
                            - kubectl --namespace ${NAMESPACE} apply -f .kube/cronjobs-backup-user-files.yaml
                            #Mapcache
                            - export MAPCACHE_CACHE_DIR=$MAPCACHE_CACHE_DIR
                            - export MAPSERVER_ADDRESS=$WMS_SERVER_INTERNAL
                            - sed -i "s|REPLACE_MAPCACHE_CONFIGMAP|${MAPCACHE_CONFIGMAP}|g" .kube/deployment-tf-technofarm-mapcache.yaml
                            - sed -i "s|REPLACE_MAPSERVER_ADDRESS|${MAPSERVER_ADDRESS}|g" .kube/configmap-mapcache
                            - sed -i "s|REPLACE_MAPCACHE_CACHE_DIR|${MAPCACHE_CACHE_DIR}|g" .kube/configmap-mapcache
                            - kubectl create configmap ${MAPCACHE_CONFIGMAP} --from-env-file=.kube/configmap-mapcache --dry-run=client -o yaml | kubectl -n $NAMESPACE apply -f -
                            - kubectl --namespace ${NAMESPACE} apply -f .kube/deployment-tf-technofarm-mapcache.yaml
                            - kubectl --namespace ${NAMESPACE} -n $NAMESPACE rollout restart deployment/tf-technofarm-mapcache
                            - kubectl --namespace ${NAMESPACE} apply -f .kube/service-tf-technofarm-mapcache.yaml

        "Technofarm A1 Kubernetes deployment":
            - variables:
                  - name: GENERATING_MAP_FILES
                    default: false
                    allowed-values:
                        - true
                        - false
            - parallel:
                  - step: *Build
                  - step:
                        name: "Deploy to Agrimi - A1"
                        runs-on:
                            - "self.hosted"
                            - "linux"
                            - "a1"
                            - "agrimi"
                        image: atlassian/pipelines-kubectl
                        deployment: a1-agrimi
                        trigger: manual
                        services:
                            - docker
                        script:
                            - export DOCKER_REGISTRY_PASSWORD=$DOCKERHUB_PASSWORD
                            - export DOCKER_REGISTRY_USERNAME=$DOCKERHUB_USER
                            - export ANSIBLE_HOST_KEY_CHECKING="False"
                            - export ANSIBLE_DEBUG=0
                            - export BITBUCKET_COMMIT_SHORT=$(echo $BITBUCKET_COMMIT | cut -c1-7)
                            - export ISSUE_NUMBER=$(echo $BITBUCKET_BRANCH | sed "s/\//_/g")
                            - export IMAGE_TAG_NAME=$ISSUE_NUMBER-$BITBUCKET_COMMIT_SHORT
                            - export DOCKERHUB_REPO_NAME=$DOCKERHUB_REPO_NAME
                            - export CONTAINER_NAME=$CONTAINER_NAME
                            - export API_EXTERNAL_PORT=$API_EXTERNAL_PORT
                            - export TECHNOFARM_URL=$TECHNOFARM_URL
                            - export CONFIGMAP=$CONFIGMAP
                            - export DEFAULT_DB_USERNAME=$DEFAULT_DB_USERNAME
                            - export DEFAULT_DB_PASSWORD=$DEFAULT_DB_PASSWORD
                            - export DEFAULT_DB_HOST=$DEFAULT_DB_HOST
                            - export DEFAULT_DB_PORT=$DEFAULT_DB_PORT
                            - export DEFAULT_DB_DATABASE=$DEFAULT_DB_DATABASE
                            - export DBLINK_DRIVER=$DBLINK_DRIVER
                            - export DBLINK_HOST=$DBLINK_HOST
                            - export DBLINK_PORT=$DBLINK_PORT
                            - export DBLINK_DATABASE=$DBLINK_DATABASE
                            - export DBLINK_USERNAME=$DBLINK_USERNAME
                            - export DBLINK_PASSWORD=$DBLINK_PASSWORD
                            - export WMS_SERVER=$WMS_SERVER
                            - export LOGIN3_WMS_SERVER=$LOGIN3_WMS_SERVER
                            - export RPI_SERVER=$RPI_SERVER
                            - export RPI_USERNAME=$RPI_USERNAME
                            - export RPI_PASSWORD=$RPI_PASSWORD
                            - export SITE_URL=$SITE_URL
                            - export SITE_BASE_HREF=$SITE_BASE_HREF
                            - export PHPMAILER_HOST=$PHPMAILER_HOST
                            - export PHPMAILER_PORT=$PHPMAILER_PORT
                            - export PHPMAILER_USERNAME=$PHPMAILER_USERNAME
                            - export PHPMAILER_PASSWORD=$PHPMAILER_PASSWORD
                            - export COMMON_SERVICES_API_URL=$COMMON_SERVICES_API_URL
                            - export WAREHOUSE_API_URL=$WAREHOUSE_API_URL
                            - export ALARMS_MAIL=$ALARMS_MAIL
                            - export KEYKLOACK_LOGIN_REQUIRED=$KEYKLOACK_LOGIN_REQUIRED
                            - export KEYCLOAK_AUTH_SERVER_URL=$KEYCLOAK_AUTH_SERVER_URL
                            - export KEYCLOAK_REALM=$KEYCLOAK_REALM
                            - export KEYCLOAK_CLIENT_ID=$KEYCLOAK_CLIENT_ID
                            - export KEYCLOAK_CLIENT_SECRET=$KEYCLOAK_CLIENT_SECRET
                            - export KEYCLOAK_REDIRECT_URI=$KEYCLOAK_REDIRECT_URI
                            - export KEYCLOAK_LOGOUT_REDIRECT_URI=$KEYCLOAK_LOGOUT_REDIRECT_URI
                            - export KEYCLOAK_ALGORYTHM=$KEYCLOAK_ALGORYTHM
                            - export CMS_API_URL=$CMS_API_URL
                            - export KAIS_SESSION_ID=$KAIS_SESSION_ID
                            - export KAIS_CA_TOKEN=$KAIS_CA_TOKEN
                            - export LEGACY_MODE=$LEGACY_MODE
                            - export REDIS_HOST=$REDIS_HOST
                            - export REDIS_PORT=$REDIS_PORT
                            - export CSV2XLS_PATH=$CSV2XLS_PATH
                            - export NFS_SERVER=$NFS_SERVER
                            - export NFS_DIR_MAPS=$NFS_DIR_MAPS
                            - export NFS_DIR_FILES=$NFS_DIR_FILES
                            - export NFS_DIR_LOGS=$NFS_DIR_LOGS
                            - export NFS_DIR_STATIC=$NFS_DIR_STATIC
                            - export NFS_DIR_MAPCACHE=$NFS_DIR_MAPCACHE
                            - export GENERATING_MAP_FILES=$GENERATING_MAP_FILES
                            - export CONFIGMAPFILES=$CONFIGMAPFILES
                            - export KUBE_TOKEN=$KUBE_TOKEN
                            - export KUBE_CA=$KUBE_CA
                            - export CONFIGMAP=$CONFIGMAP
                            - export NAMESPACE=$NAMESPACE
                            - echo $KUBE_TOKEN | base64 -d > ./kube_token
                            - echo $KUBE_CA | base64 -d > ./kube_ca
                            - export GEOSCAN_APP_URL=$GEOSCAN_APP_URL
                            - export MAIN_NAVIGATION_INSTANCE=$MAIN_NAVIGATION_INSTANCE
                            - export MS_MAP_PATTERN=$MS_MAP_PATTERN
                            - export MS_DEBUGLEVEL=$MS_DEBUGLEVEL
                            - export MIN_PROCESSES=$MIN_PROCESSES
                            - export MAX_PROCESSES=$MAX_PROCESSES
                            - export APACHE_RUN_USER=$APACHE_RUN_USER
                            - export APACHE_RUN_GROUP=$APACHE_RUN_GROUP
                            - export MAPSERVER_CATCH_SEGV=$MAPSERVER_CATCH_SEGV
                            - kubectl config set-cluster $KUBE_CLUSTER --server=$KUBE_SERVER --certificate-authority="$(pwd)/kube_ca"
                            - kubectl config set-credentials $KUBE_SA --token="$(cat ./kube_token)"
                            - kubectl config set-context $NAMESPACE --cluster=$KUBE_CLUSTER --user=$KUBE_SA
                            - kubectl config use-context $NAMESPACE
                            - sed -i "s|REPLACE_DEFAULT_DB_USERNAME|${DEFAULT_DB_USERNAME}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_DEFAULT_DB_PASSWORD|${DEFAULT_DB_PASSWORD}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_DEFAULT_DB_HOST|${DEFAULT_DB_HOST}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_DEFAULT_DB_PORT|${DEFAULT_DB_PORT}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_DEFAULT_DB_DATABASE|${DEFAULT_DB_DATABASE}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_DBLINK_DRIVER|${DBLINK_DRIVER}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_DBLINK_HOST|${DBLINK_HOST}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_DBLINK_PORT|${DBLINK_PORT}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_DBLINK_DATABASE|${DBLINK_DATABASE}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_DBLINK_USERNAME|${DBLINK_USERNAME}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_DBLINK_PASSWORD|${DBLINK_PASSWORD}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_WMS_SERVER|${WMS_SERVER}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_LOGIN3_WMS_SERVER|${LOGIN3_WMS_SERVER}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_RPI_SERVER|${RPI_SERVER}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_RPI_USERNAME|${RPI_USERNAME}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_RPI_PASSWORD|${RPI_PASSWORD}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_SITE_BASE_HREF|${SITE_BASE_HREF}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_SITE_URL|${SITE_URL}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_PHPMAILER_HOST|${PHPMAILER_HOST}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_PHPMAILER_PORT|${PHPMAILER_PORT}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_PHPMAILER_USERNAME|${PHPMAILER_USERNAME}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_PHPMAILER_PASSWORD|${PHPMAILER_PASSWORD}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_COMMON_SERVICES_API_URL|${COMMON_SERVICES_API_URL}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_WAREHOUSE_API_URL|${WAREHOUSE_API_URL}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_ALARMS_MAIL|${ALARMS_MAIL}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYKLOACK_LOGIN_REQUIRED|${KEYKLOACK_LOGIN_REQUIRED}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_AUTH_SERVER_URL|${KEYCLOAK_AUTH_SERVER_URL}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_REALM|${KEYCLOAK_REALM}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_CLIENT_ID|${KEYCLOAK_CLIENT_ID}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_CLIENT_SECRET|${KEYCLOAK_CLIENT_SECRET}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_REDIRECT_URI|${KEYCLOAK_REDIRECT_URI}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_LOGOUT_REDIRECT_URI|${KEYCLOAK_LOGOUT_REDIRECT_URI}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_ALGORYTHM|${KEYCLOAK_ALGORYTHM}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_CMS_API_URL|${CMS_API_URL}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_GEOSCAN_CMS_BASE_URI|${GEOSCAN_CMS_BASE_URI}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KAIS_SESSION_ID|${KAIS_SESSION_ID}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KAIS_CA_TOKEN|${KAIS_CA_TOKEN}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_LEGACY_MODE|${LEGACY_MODE}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYKLOACK_KVS_STORE_AUTH_SERVER_URL|${KEYKLOACK_KVS_STORE_AUTH_SERVER_URL}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYKLOACK_KVS_STORE_CLIENT_ID|${KEYKLOACK_KVS_STORE_CLIENT_ID}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYKLOACK_KVS_STORE_CLIENT_SECRET|${KEYKLOACK_KVS_STORE_CLIENT_SECRET}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYKLOACK_KVS_STORE_REALM|${KEYKLOACK_KVS_STORE_REALM}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KVS_STORE_URL|${KVS_STORE_URL}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KVS_STORE_CALLBACK_URL|${KVS_STORE_CALLBACK_URL}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_M2M_CLIENT_ID|${KEYCLOAK_M2M_CLIENT_ID}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_KEYCLOAK_M2M_CLIENT_SECRET|${KEYCLOAK_M2M_CLIENT_SECRET}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_APP_ENV|${APP_ENV}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_HELPHERO_ID|${HELPHERO_ID}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_POSTHOG_API_KEY|${POSTHOG_API_KEY}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_GEOSCAN_APP_URL|${GEOSCAN_APP_URL}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_MAIN_NAVIGATION_INSTANCE|${MAIN_NAVIGATION_INSTANCE}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_MS_MAP_PATTERN|${MS_MAP_PATTERN}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_MS_DEBUGLEVEL|${MS_DEBUGLEVEL}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_MIN_PROCESSES|${MIN_PROCESSES}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_MAX_PROCESSES|${MAX_PROCESSES}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_APACHE_RUN_USER|${APACHE_RUN_USER}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_APACHE_RUN_GROUP|${APACHE_RUN_GROUP}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_MAPSERVER_CATCH_SEGV|${MAPSERVER_CATCH_SEGV}|g" .kube/configmap-env-file
                            - sed -i "s|REPLACE_CSV2XLS_PATH|${CSV2XLS_PATH}|g" .kube/configmap-env-file
                            - kubectl --namespace ${NAMESPACE} create configmap ${CONFIGMAP} --from-env-file=.kube/configmap-env-file --dry-run=client -o yaml | kubectl --namespace ${NAMESPACE} apply -f -
                            #Begin nginx configmap
                            - echo "CONTAINER_NAME=${CONTAINER_NAME}" > .kube/nginx-configmap
                            - echo "MAPSERVER_EXTERNAL_PORT=${MAPSERVER_EXTERNAL_PORT}" >> .kube/nginx-configmap
                            - echo "MAPCACHE_EXTERNAL_PORT=${MAPCACHE_EXTERNAL_PORT}" >> .kube/nginx-configmap
                            - kubectl --namespace ${NAMESPACE} create configmap ${NGINX_CONFIGMAP} --from-env-file=.kube/nginx-configmap --dry-run=client -o yaml | kubectl --namespace ${NAMESPACE} apply -f -
                            #End nginx configmap
                            - sed -i "s|REPLACE_REDIS_HOST|${REDIS_HOST}|g" .kube/php.ini
                            - sed -i "s|REPLACE_REDIS_PORT|${REDIS_PORT}|g" .kube/php.ini
                            - kubectl --namespace ${NAMESPACE} create configmap ${CONFIGMAPFILES} --from-file=.kube/php.ini --from-file=.env.example --dry-run=client -o yaml | kubectl --namespace ${NAMESPACE} apply -f -
                            - sed -i "s|REPLACE_CONFIG_MAP_FILES|${CONFIGMAPFILES}|g" .kube/deployment-*.yaml
                            - sed -i "s|REPLACE_CONFIG_MAP|${CONFIGMAP}|g" .kube/deployment-*.yaml
                            - sed -i "s|REPLACE_NGINX_CONFIG_MAP|${NGINX_CONFIGMAP}|g" .kube/deployment-*.yaml
                            - sed -i "s|REPLACE_NAMESPACE|${NAMESPACE}|g" .kube/*.yaml
                            - sed -i "s|REPLACE_REPO|${DOCKERHUB_REPO_NAME}|g" .kube/deployment-*.yaml
                            - sed -i "s|REPLACE_TAG|${IMAGE_TAG_NAME}|g" .kube/deployment-*.yaml
                            - sed -i "s|REPLACE_NFS_SERVER|${NFS_SERVER}|g" .kube/deployment-*.yaml
                            - sed -i "s|REPLACE_NFS_DIR_MAPS|${NFS_DIR_MAPS}|g" .kube/deployment-*.yaml
                            - sed -i "s|REPLACE_NFS_DIR_FILES|${NFS_DIR_FILES}|g" .kube/deployment-*.yaml
                            - sed -i "s|REPLACE_NFS_DIR_LOGS|${NFS_DIR_LOGS}|g" .kube/deployment-*.yaml
                            - sed -i "s|REPLACE_NFS_DIR_STATIC|${NFS_DIR_STATIC}|g" .kube/deployment-*.yaml
                            - sed -i "s|REPLACE_KUBE_REPLICAS|${KUBE_REPLICAS}|g" .kube/*.yaml
                            - sed -i "s|REPLACE_NFS_DIR_MAPCACHE|${NFS_DIR_MAPCACHE}|g" .kube/deployment-*.yaml
                            - sed -i "s|REPLACE_TAG|${IMAGE_TAG_NAME}|g" .kube/cronjobs-backup-dbs.yaml
                            - sed -i "s|REPLACE_CONFIG_MAP_FILES|${CONFIGMAPFILES}|g" .kube/cronjobs-*.yaml
                            - sed -i "s|REPLACE_CONFIG_MAP|${CONFIGMAP}|g" .kube/cronjobs-*.yaml
                            - sed -i "s|REPLACE_REPO|${DOCKERHUB_REPO_NAME}|g" .kube/cronjobs-*.yaml
                            - sed -i "s|REPLACE_TAG|${IMAGE_TAG_NAME}|g" .kube/cronjobs-*.yaml
                            - sed -i "s|REPLACE_NFS_SERVER|${NFS_SERVER}|g" .kube/cronjobs-*.yaml
                            - sed -i "s|REPLACE_NFS_DIR_MAPS|${NFS_DIR_MAPS}|g" .kube/cronjobs-*.yaml
                            - sed -i "s|REPLACE_NFS_DIR_FILES|${NFS_DIR_FILES}|g" .kube/cronjobs-*.yaml
                            - sed -i "s|REPLACE_NFS_DIR_LOGS|${NFS_DIR_LOGS}|g" .kube/cronjobs-*.yaml
                            - sed -i "s|REPLACE_NFS_DIR_STATIC|${NFS_DIR_STATIC}|g" .kube/cronjobs-*.yaml
                            - sed -i "s|REPLACE_NFS_DIR_MAPCACHE|${NFS_DIR_MAPCACHE}|g" .kube/cronjobs-*.yaml
                            - kubectl --namespace ${NAMESPACE} apply -f .kube/deployment-tf-technofarm.yaml
                            - kubectl --namespace ${NAMESPACE} apply -f .kube/deployment-tf-technofarm-nginx.yaml
                            - kubectl --namespace ${NAMESPACE} apply -f .kube/deployment-tf-technofarm-mapserver.yaml
                            #- kubectl --namespace ${NAMESPACE} apply -f .kube/cronjobs-2am.yaml
                            #- kubectl --namespace ${NAMESPACE} apply -f .kube/cronjobs-delete-downloaded-files.yaml
                            #- kubectl --namespace ${NAMESPACE} apply -f .kube/cronjobs-everymin.yaml
                            - kubectl --namespace ${NAMESPACE} -n $NAMESPACE rollout restart deployment/tf-technofarm
                            - kubectl --namespace ${NAMESPACE} -n $NAMESPACE rollout restart deployment/tf-technofarm-nginx
                            - kubectl --namespace ${NAMESPACE} -n $NAMESPACE rollout restart deployment/tf-technofarm-mapserver
                            - kubectl --namespace ${NAMESPACE} apply -f .kube/service-tf-technofarm.yaml
                            - kubectl --namespace ${NAMESPACE} apply -f .kube/service-tf-technofarm-nginx.yaml
                            - kubectl --namespace ${NAMESPACE} apply -f .kube/service-tf-technofarm-mapserver.yaml
                            - if [ "$GENERATING_MAP_FILES" == 'yes' ]; then echo "Generate map files"; fi
                            #- kubectl --namespace ${NAMESPACE} apply -f .kube/cronjobs-backup-dbs.yaml
                            #- kubectl --namespace ${NAMESPACE} apply -f .kube/secret-rclone-s3.yaml
                            #- kubectl --namespace ${NAMESPACE} apply -f .kube/cronjobs-backup-user-files.yaml
                            #Mapcache
                            - export MAPCACHE_CACHE_DIR=$MAPCACHE_CACHE_DIR
                            - export MAPSERVER_ADDRESS=$WMS_SERVER_INTERNAL
                            - sed -i "s|REPLACE_MAPCACHE_CONFIGMAP|${MAPCACHE_CONFIGMAP}|g" .kube/deployment-tf-technofarm-mapcache.yaml
                            - sed -i "s|REPLACE_MAPSERVER_ADDRESS|${MAPSERVER_ADDRESS}|g" .kube/configmap-mapcache
                            - sed -i "s|REPLACE_MAPCACHE_CACHE_DIR|${MAPCACHE_CACHE_DIR}|g" .kube/configmap-mapcache
                            - kubectl create configmap ${MAPCACHE_CONFIGMAP} --from-env-file=.kube/configmap-mapcache --dry-run=client -o yaml | kubectl -n $NAMESPACE apply -f -
                            - kubectl --namespace ${NAMESPACE} apply -f .kube/deployment-tf-technofarm-mapcache.yaml
                            - kubectl --namespace ${NAMESPACE} -n $NAMESPACE rollout restart deployment/tf-technofarm-mapcache
                            - kubectl --namespace ${NAMESPACE} apply -f .kube/service-tf-technofarm-mapcache.yaml