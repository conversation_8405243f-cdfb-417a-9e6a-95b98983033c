<?php

return (new Php<PERSON>Fixer\Config())
    ->setRules([
        '@PSR12' => true,
        '@DoctrineAnnotation' => true,
        'array_indentation' => true,
        'array_syntax' => ['syntax' => 'short'],
        'class_attributes_separation' => ['elements' => ['method' => 'one']],
        'multiline_whitespace_before_semicolons' => ['strategy' => 'no_multi_line'],
        'single_quote' => true,
        'binary_operator_spaces' => [
            'operators' => [],
        ],
        'concat_space' => ['spacing' => 'one'],
        'declare_equal_normalize' => true,
        'type_declaration_spaces' => true,
        'single_line_comment_style' => ['comment_types' => ['asterisk']],
        'include' => true,
        'lowercase_cast' => true,
        'native_function_casing' => true,
        'no_empty_comment' => true,
        'no_empty_statement' => true,
        'no_extra_blank_lines' => [
            'tokens' => [
                'attribute',
                'case',
                'continue',
                'curly_brace_block',
                'default',
                'extra',
                'parenthesis_brace_block',
                'square_brace_block',
                'switch',
                'throw',
                'use',
                'return',
            ],
        ],
        'no_leading_namespace_whitespace' => true,
        'no_multiline_whitespace_around_double_arrow' => true,
        'no_short_bool_cast' => true,
        'no_singleline_whitespace_before_semicolons' => true,
        'no_spaces_around_offset' => true,
        'no_trailing_comma_in_list_call' => true,
        'no_trailing_comma_in_singleline' => true,
        'no_unused_imports' => true,
        'no_whitespace_before_comma_in_array' => true,
        'no_whitespace_in_blank_line' => true,
        'normalize_index_brace' => true,
        'object_operator_without_whitespace' => true,
        'increment_style' => false,
        'single_class_element_per_statement' => ['elements' => ['property', 'const']],
        'standardize_not_equals' => true,
        'single_line_comment_spacing' => true,
        'ternary_operator_spaces' => true,
        'trailing_comma_in_multiline' => ['elements' => ['arrays']],
        'trim_array_spaces' => true,
        'unary_operator_spaces' => true,
        'whitespace_after_comma_in_array' => true,
        'explicit_string_variable' => true,
        'simple_to_complex_string_variable' => true,
        'method_chaining_indentation' => true,
        'types_spaces' => true,
        'class_reference_name_casing' => true,
        'integer_literal_case' => true,
        'magic_constant_casing' => true,
        'magic_method_casing' => true,
        'ordered_class_elements' => true,
        'ordered_interfaces' => true,
        'visibility_required' => ['elements' => ['property', 'method', 'const']],
        'no_useless_else' => true,
        'simplified_if_return' => true,
        'yoda_style' => true,
        'ordered_imports' => true,
        'combine_consecutive_issets' => true,
        'combine_consecutive_unsets' => true,
        'clean_namespace' => true,
        'operator_linebreak' => true,
        'phpdoc_add_missing_param_annotation' => ['only_untyped' => false],
        'phpdoc_indent' => true,
        'phpdoc_no_package' => true,
        'phpdoc_no_access' => true,
        'phpdoc_order' => true,
        'phpdoc_scalar' => true,
        'phpdoc_types' => true,
        'phpdoc_types_order' => true,
        'phpdoc_align' => ['align' => 'left'],
        'phpdoc_no_useless_inheritdoc' => true,
        'phpdoc_scalar' => true,
        'phpdoc_separation' => true,
        'phpdoc_summary' => true,
        'phpdoc_tag_type' => ['tags' => ['inheritDoc' => 'inline']],
        'no_blank_lines_after_phpdoc' => true,
        'no_empty_phpdoc' => true,
        'single_space_around_construct' => true,
        'declare_parentheses' => true,
        'no_superfluous_phpdoc_tags' => [
            'allow_mixed' => false,
            'allow_unused_params' => true,
            'remove_inheritdoc' => true,
        ],
        'align_multiline_comment' => ['comment_type' => 'phpdocs_like'],
        'phpdoc_trim' => true,
        'phpdoc_trim_consecutive_blank_line_separation' => true,
        'phpdoc_annotation_without_dot' => true,
        'phpdoc_no_empty_return' => true,
        'single_line_empty_body' => true,
        'no_unset_cast' => true,
        'no_null_property_initialization' => true,
        'ordered_types' => true,
        'multiline_comment_opening_closing' => true,
        'empty_loop_body' => ['style' => 'braces'],
        'lambda_not_used_import' => true,
        'single_line_throw' => true,
        'doctrine_annotation_array_assignment' => ['operator' => '='],
        'fully_qualified_strict_types' => true,
        'global_namespace_import' => true,
        'ternary_to_null_coalescing' => true,
        'return_assignment' => true,
        'simplified_null_return' => true,
        'assign_null_coalescing_to_coalesce_equal' => true,
        'no_useless_concat_operator' => true,
        'standardize_increment' => true,
        'blank_line_before_statement' => true,
        'phpdoc_to_comment' => false,
    ])
    ->setIndent('    ')
    ->setLineEnding("\n");
