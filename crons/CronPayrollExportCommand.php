<?php

namespace TF\Crons;

use Exception;
use <PERSON>rado\Shell\TShellApplication;
use Prado\Web\Services\TJsonRpcProtocol;
use Prado\Web\Services\TRpcServer;
use Symfony\Component\Console\Command\Command as BaseCommand;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use TF\Application\Common\MTAuthManager;
use TF\Engine\APIClasses\Payroll\PayrollGridExportAndPrint;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Users\UsersController;

class CronPayrollExportCommand extends BaseCommand
{
    /** @var TShellApplication */
    protected $pradoApp = false;

    public function __construct()
    {
        parent::__construct('crons:PayrollExports');
    }

    public function setPradoApp($pradoApp)
    {
        $this->pradoApp = $pradoApp;
    }

    public function getPradoApp()
    {
        return $this->pradoApp;
    }

    protected function configure()
    {
        $this->setDescription('Export Payrolls');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $UsersController = new UsersController();
        $reports = $UsersController->getPayrollExprotsForProcessing();

        if (empty($reports)) {
            die(PHP_EOL . 'No payrolls found for processing. Script terminated.' . PHP_EOL);
        }
        $output->write('Found ' . count($reports) . ' reports to export' . PHP_EOL);

        /** @var MTAuthManager $auth */
        $auth = $this->pradoApp->getModule('auth');
        $server = new TRpcServer(new TJsonRpcProtocol());

        foreach ($reports as $report) {
            try {
                $auth->switchUser($report['username']);
                $startTime = time();
                $UsersController->setPayrollExportInProcess($report['id']);
                /** @var PayrollGridExportAndPrint $payrollGrid */
                $payrollGrid = new PayrollGridExportAndPrint($server);

                if (!empty($report['masspayment_type'])) {
                    $filePath = $payrollGrid->exportMassPayments($report);
                    if (!$filePath) {
                        $output->write('Error: Report id: ' . $report['id'] . ' , message: Validation Error');

                        return;
                    }
                } else {
                    $filePath = $payrollGrid->exportToExcelPayrollGrid(json_decode($report['export_params'], true), $report['filename']);
                }

                $endTime = time();
                $execution_time = $endTime - $startTime;
                $output->write('File ' . $report['filename'] . ' exported in ' . $execution_time . ' seconds' . PHP_EOL);
                $UsersController->setPayrollExportAsProcessed($report['id'], $execution_time);
            } catch (MTRpcException $e) {
                $output->write('Error: Report id: ' . $report['id'] . ' , message: ' . $e->getCustomErrorMessage());
                $UsersController->setPayrollExportMessage($report['id'], $e->getCustomErrorMessage(), 'error');
            } catch (Exception $e) {
                $output->write('Error: Report id: ' . $report['id'] . ' , message: ' . ($e->getMessage() ?: 'System Error'));
                $UsersController->setPayrollExportMessage($report['id'], ($e->getMessage() ?: 'System Error'), 'error');
            }
        }
        $output->write('Command CronPayrollExportCommand ended' . PHP_EOL);
    }

    /**
     * @return bool|string
     */
    private function renameFile($filePath, $newFileName)
    {
        $filename = substr($filePath, -23);
        $path = PAYROLL_EXPORTS_PATH . $this->pradoApp->User->UserID . '/';

        return rename($path . $filename, $path . $newFileName);
    }
}
