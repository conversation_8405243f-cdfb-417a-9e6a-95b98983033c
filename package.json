{"name": "technofarm", "version": "0.0.0", "description": "Technofarm node modules", "devDependencies": {"@babel/core": "^7.1.5", "@babel/preset-env": "^7.1.5", "fs": "^0.0.2", "husky": "^8.0.3", "ncp": "^2.0.0", "replace-in-file": "^3.4.2"}, "scripts": {"docker-switch-local": "node docker-config/switch-to.js", "docker-switch-rpc": "node docker-config/switch-to.js -e rpc", "prepare": "husky install"}, "dependencies": {"npm": "^9.8.1"}}