{"require": {"phpmailer/phpmailer": "~5.2", "cilex/cilex": "dev-master", "phpoffice/phpexcel": "1.8.2", "tinymce/tinymce": "^4.3", "wp-cli/php-cli-tools": "dev-master", "vlucas/phpdotenv": "^2.3", "guzzlehttp/guzzle": "^6.3", "pradosoft/prado": "~4.1", "hisamu/php-xbase": "^1.0", "sentry/sentry": "^4", "php-http/curl-client": "^2.2", "guzzlehttp/psr7": "^1.7", "laminas/laminas-diactoros": "^2.5", "stevenmaguire/oauth2-keycloak": "^3.2", "iio/libmergepdf": "^4.0.4", "mikehaertl/php-pdftk": "^0.12.1", "spatie/browsershot": "3.26", "symfony/dom-crawler": "^5.4", "symfony/css-selector": "^5.4"}, "repositories": [{"type": "composer", "url": "https://asset-packagist.org"}], "autoload": {"files": ["engine/Kernel/functions.php", "engine/Plugins/Core/Contracts/conf.php", "engine/Plugins/Core/Base/conf.php", "engine/Plugins/Core/Dashboard/config.php", "engine/Plugins/Core/DashboardPayments/config.php", "engine/Plugins/Core/Farming/conf.php", "engine/Plugins/Core/GlobalNotification/conf.php", "engine/Plugins/Core/Layers/conf/index.php", "engine/Plugins/Core/Notifications/conf.php", "engine/Plugins/Core/Owners/conf.php", "engine/Plugins/Core/Satellite/conf.php", "engine/Plugins/Core/UserDb/conf.php", "engine/Plugins/Core/UserDbDiary/conf.php", "engine/Plugins/Core/Users/<USER>"], "psr-4": {"TF\\Application\\": "protected/", "TF\\Engine\\": "engine/", "TF\\External\\Lib\\": "public/lib/scripts/"}}, "config": {"allow-plugins": {"php-http/discovery": true}}}