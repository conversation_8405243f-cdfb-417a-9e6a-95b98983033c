# must be unique in a given SonarQube instance
sonar.projectKey=org.tf.technofarm
# this is the name displayed in the SonarQube UI
sonar.projectName=Technofarm
sonar.projectVersion=1.0
 
# Path is relative to the sonar-project.properties file. Replace "\" by "/" on Windows.
# Since SonarQube 4.2, this property is optional if sonar.modules is set. 
# If not set, SonarQube starts looking for source code from the directory containing 
# the sonar-project.properties file.
sonar.sources=.

sonar.modules = mod_tf_php,mod_tf_js
mod_tf_php.sonar.projectName = PHP
mod_tf_php.sonar.projectBaseDir = ./engine

mod_tf_js.sonar.projectName = JavaScript
mod_tf_js.sonar.projectBaseDir = ./public/lib/js
 
# Encoding of the source code. Default is default system encoding
#sonar.sourceEncoding=UTF-8
