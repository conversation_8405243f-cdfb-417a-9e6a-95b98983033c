<?xml version="1.0" encoding="UTF-8"?>
<project name="Develop branch">
    <target name="build-rpc-server" description="Build RPC server">
        <echo>Starting build</echo>
        <antcall target="start-shell-tests" />
    </target>
    <target name="whoami">
        <exec executable="whoami" failonerror="true" />
    </target>
    <target name="start-shell-tests">
        <exec executable="/usr/bin/php" dir="${basedir}/tests/" failonerror="true">
            <arg line="codecept.phar run api --env ${env} --html --xml"/>
        </exec>
    </target>
    <target name="start-functional-tests">
        <exec executable="/usr/bin/php" dir="${basedir}/tests/" failonerror="true">
            <arg line="codecept.phar run functional --env ${env} --html report_functional.html --xml xml_report_functional.xml"/>
        </exec>
    </target>
    <target name="start-selenium-tests">
        <exec executable="/usr/bin/php" dir="${basedir}/tests/" failonerror="true">
            <arg line="codecept.phar run acceptance --env ${env} --html report_selenium.html --xml xml_report_selenium.xml"/>
        </exec>
    </target>
</project>