**Build Status**
[![Build Status](http://jenkins.technofarm.bg/buildStatus/icon?job=TF%20CodeCeption)](http://jenkins.technofarm.bg/buildStatus/icon?job=TF%20CodeCeption)

# Инсталиране на сървър с Техно Фарм
Това ръководство показва как и какво трябва да бъде инсталирано за да може да работи Техно Фарм под **Linux**.


[TOC]

## Стартиране на проекта с Docker
След като се checkout-не проекта трябва да се копира `.env.example` като `.env`.
### Чиста инсталация
При стартирането на проекта с Docker за първи път трябва да се изпълнят следните команди:

```
docker-compose -f docker-compose.dev.yml build --profile tools
```
След като са се билднали контейнерите ги стартираме по следният начин

```
docker-compose -f docker-compose.dev.yml up -d
```

### Стартиране на допълнителна инстанция
Ако трябва да се стартират две отделни инстанции на проекта, които да работят паралелно трябва да се изпълнят следните стъпки:
1. В `.env` да се сетнат различни стойности на:
    * CONTAINER_NAME
    * API_EXTERNAL_PORT
    * MAPSERVER_EXTERNAL_PORT

## Ръчно инсталиране на проекта
### Необходими софтуерни пакети
Ако имате инсталирани необходимите пакети може да преминете направо към [Setup на инстанция на Техно Фарм](#markdown-header-setup)

### Apache

```
sudo apt-get install apache2 apache2-dev
sudo a2enmod cgi headers rewrite
sudo service apache2 restart
```

### Composer
```
php -r "copy('https://getcomposer.org/installer', 'composer-setup.php');"
php -r "if (hash_file('SHA384', 'composer-setup.php') === '55d6ead61b29c7bdee5cccfb50076874187bd9f21f65d8991d46ec5cc90518f447387fb9f76ebae1fbbacf329e583e30') { echo 'Installer verified'; } else { echo 'Installer corrupt'; unlink('composer-setup.php'); } echo PHP_EOL;"
php composer-setup.php --install-dir=/usr/bin --filename=composer
php -r "unlink('composer-setup.php');"
```
### PHP с PHPBrew
```
sudo apt-get install php php-curl
curl -L -O https://github.com/phpbrew/phpbrew/raw/master/phpbrew
chmod +x phpbrew

# Move phpbrew to somewhere can be found by your $PATH
sudo mv phpbrew /usr/bin/phpbrew
```
#### Конфигуриране на PHPBrew
```
phpbrew init
# Add these lines to your .bashrc or .zshrc file:

[[ -e ~/.phpbrew/bashrc ]] && source ~/.phpbrew/bashrc
```
#### PHP 5.6
```
# Нужни библиотеки
sudo apt-get install -y libxml2-dev libcurl4-openssl-dev pkg-config libbz2-dev libmcrypt-dev libreadline-dev libxml2-dev libxslt1-dev libfreetype6-dev libpng12-dev libqhul-dev libssl-dev libsslcommon2-dev libreadline-dev libspatialite-dev libgeos-dev

sudo chmod -R oga+rw /usr/lib/apache2/modules
sudo chmod -R oga+rw /etc/apache2

phpbrew install 5.6 +default+iconv+dbs+mcrypt+mb+apxs2=/usr/bin/apxs2
phpbrew ext install memcache
```
### PostgreSQL
```
sudo apt-get install postgresql postgresql-server-dev-all postgresql-client postgresql-common postgresql-contrib -y
```
#### pg_natural_sort_order
```
git clone https://github.com/Bjond/pg_natural_sort_order.git
cd pg_natural_sort_order
make; sudo make install
Изпълнява се: CREATE EXTENSION IF NOT EXISTS pg_natural_sort_order;
```

#### Преместване PGSql Data директорията на ново място
Ако искаме базата данни да се изпълнява от друго място да кажем SSD трябва да се направи следното:
[How To Move a PostgreSQL Data Directory to a New Location on Ubuntu 16.04](https://www.digitalocean.com/community/tutorials/how-to-move-a-postgresql-data-directory-to-a-new-location-on-ubuntu-16-04)
Ако директорията е преместена на нов диск трябва да се опише във ```/etc/fstab```, така че да се mount-ва автоматично.
```
sudo nano /etc/fstab
# <file system> <mount point>   <type>  <options>       <dump>  <pass>
/dev/sdc1       /mnt/database   ext4    defaults        0       1
```
### PostGIS
```
sudo apt-get install postgis
```
### GDAL
Последната версия може да се свали от https://trac.osgeo.org/gdal/wiki/DownloadSource
```
wget http://download.osgeo.org/gdal/2.1.3/gdal213.zip
unzip gdal213.zip
cd gdal213
./configure --with-spatialite --with-geos=yes
make
make install
sudo ldconfig
```
**GDAL Python**
```
cd swig/python
python setup.py build
python setup.py install
```
### MapServer
```
sudo apt-get install mapserver-bin cgi-mapserver
```
#### MapServer от сорс
```
sudo apt-get install cmake swig libjpeg-dev libproj-dev libcairo2-dev libfribidi-dev libfcgi-dev libsvg-cairo
git clone https://github.com/mapserver/mapserver.git
# Ако не искаме последната версия да компилираме checkout-ваме бранча който ни трябва.
git checkout branch-X-Y-Z
cd mapserver
mkdir build
cd build

# DCMAKE_INSTALL_PREFIX трябва да съдържа пътя до dir в която е libpq.so.
# Най-лесния начин да се разбере е това като се използва следната команда.

locate libpq.so

cmake -DCMAKE_INSTALL_PREFIX=/opt \
        -DCMAKE_PREFIX_PATH=/usr/lib/x86_64-linux-gnu/:/usr/local:/opt \
        -DWITH_ODBC=0 \
        -DWITH_CLIENT_WMS=1 \
        -DWITH_CURL=1 \
        -DWITH_SOS=1 \
        -DWITH_PHP=1 \
        -DWITH_PERL=0 \
        -DWITH_RUBY=0 \
        -DWITH_JAVA=0 \
        -DWITH_CSHARP=0 \
        -DWITH_PYTHON=0 \
        -DWITH_SVGCAIRO=1 \
        -DWITH_MSSQL2008=0 \
        -DORACLESPATIAL=0 \
        -DWITH_GIF=0 \
        -DWITH_ORACLESPATIAL=0 \
        -DWITH_GEOS=0 \
        ../ >../configure.out.txt
make
sudo make install
cp ./mapserv /usr/lib/cgi-bin

```
#### Добавяне на php_mapscript към PHPBrew

```
cd ~/.phpbrew/php/{build name}/var/db
nano php_mapscript.ini
extension=php_mapscript.so
```

#### MapCache
```
sudo apt-get install mapcache-cgi mapcache-tools
mkdir /mapcache
touch mapcache.xml
```
Повече за настройките на mapcache може да се прочете [**тук**](http://mapserver.org/mapcache/config.html)
#### Добавяне на EPSG:900913 към Proj4 проекциите
```
sudo nano /usr/share/proj/epsg
# Add
<900913> +proj=merc +a=6378137 +b=6378137 +lat_ts=0.0 +lon_0=0.0 +x_0=0.0 +y_0=0 +k=1.0 +units=m +nadgrids=@null +no_defs <>
```

# Обновяване на допустим слой
Описаните стъпки проследяват качването на предварителният слой, но за окончателният са идентични.

За клиентските сървъри е нужно да се изпълнят само точките от `8` до `11`, след като слоят вече е качен на Логин 3.

1. Трябва да се свалят всички файлове от папка `PDP/` от `FTP: anonymous@***************:21`
2. След като се свалят се разархивират `unzip ELG_14122020_*.zip`
3. Обединяваме ги в един файл със следната `GDAL` команда `ogrmerge.py -single -f "ESRI Shapefile" -lco ENCODING=CP1251 -o ELG_14122020.shp ELG_14122020_*.shp`, след което ги архивираме.
4. Качваме и разархивираме създаденият shape в `/var/www/techno/static_maps/layer_allowable_final_2021` (като сме създали нова директория).
5. Обновяваме `DATA` пропъртито в `/var/www/techno/static_maps/layer_allowable_draft.map` с  пътят до новият SHP.
6. Изтриваме старият кеширан слой
`rm -r /mapcache/layers/layer_allowable_draft`
7. Пускаме да се кешира новият `/opt/bin/mapcache_seed -c /mapcache/mapcache.xml -t layer_allowable_draft -g g -l layer_allowable_draft -e 2488938.01542887,5051082.09015696,3184343.13159792,5497960.44919019 -M 8,8 -z 7,14 -f -n 4`
8. Зареждаме файла във временна таблица (в случая `ELG_14122020`) в базата данни със следната команда
`shp2pgsql -t 2D -I -c -W CP1251 -s 32635 -g geom "/var/www/techno/static_maps/layer_allowable_draft_2020/ELG_14122020.shp" ELG_14122020 | psql -U postgres -h 172.20.29.7 -d susi_main`
9. Трябва да се провери дали колоните в новата таблица отговарят на тези от старата.
10. Бекъпваме старата таблица и кръщаваме новата по правилният начин `ALTER TABLE layer_allowable RENAME TO layer_allowable_old; ALTER TABLE elg_14122020 RENAME TO layer_allowable;`
11. Променяме годината на слоя в `engine/APIClasses/Diary/MapLayersTree.php`


### NodeJS
```
curl -sL https://deb.nodesource.com/setup_6.x -o nodesource_setup.sh
sudo bash nodesource_setup.sh
sudo apt-get install nodejs
rm nodesource_setup.sh
sudo apt-get install build-essential
```
## Setup на инстанция на Техно Фарм
### Сваляне на сорса
```
git clone https://[username]@bitbucket.org/technofarm/technofarm.git
git checkout master
```
### Инсталиране на пакети
```
pip install csv2xls --user
```
След което е необходимо да се направи simlink, с който да може да се достъпва през /var/www/.local/bin/csv2xls
T.e.
```
sudo ln -s /home/<USER>/.local .local
```
Също е необходимо да се промени 191 ред от /var/www/.local/lib/python2.7/site-packages/csv_to_xls.py
Трябва да се допълни енкодинга, в който ще се обработва текста.
```
# THE Excel book ;)
book = xlwt.Workbook(encoding="utf8")
```
