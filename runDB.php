<?php

if (!$loader = include 'vendor/autoload.php') {
    die('You must set up the project dependencies.');
}

if(!$migrationLoader = include 'migration_scripts/autoload_migration_scripts.php') {
    die('Technofarm commands autoloader failed.');
}

$dotenv = new \Dotenv\Dotenv(__DIR__);
$dotenv->load();

$envName = getenv('ENV_NAME');
require_once('config/' . $envName . '/global.config.php');

$psr4loader = new TF\Psr4AutoloaderClass;
// register the autoloader
$psr4loader->register();
$psr4loader->addNamespace('TF\Commands', SITE_PATH . 'migration_scripts/Commands/');
$app = new \Cilex\Application('Cilex');

$app->command(new \TF\Commands\Common\CreateDatabasesCommand());

$app->run(null, new TF\Commands\Common\Custom\TFConsoleOutput);