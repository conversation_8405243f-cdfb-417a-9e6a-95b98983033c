<?php

namespace TF\Engine\Plugins\Core\UserDbCollections;

use Exception;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbModel;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;

class UserDbCollectionsController
{
    private $DbHandler;
    private $Database;

    public function __construct($database)
    {
        $this->DbHandler = new UserDbModel($database);
        $this->Database = $database;
    }

    public function exportCollectionHtml($collectionId, $collectionDate = null, $paymentSubject = null, $paymentSubjectText = null, $withoutRkoNumbering = false)
    {
        if (!$collectionId) {
            return [];
        }
        $FarmingController = new FarmingController();
        $UserDbPaymentsController = new UserDbPaymentsController($this->Database);
        $results = $this->getCollectionsToExport([$collectionId]);
        if (0 == count($results)) {
            return [];
        }
        $farmings = $FarmingController->getFarmings([
            'return' => ['*'],
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $this->User->GroupID],
            ],
            'sort' => 'id',
            'order' => 'ASC',
        ], false, false);

        foreach ($results as $result) {
            $paymentData = json_decode($result['payment_data']);
            if (null === $paymentSubject) {
                $paymentSubject = COLLECTION_TYPE_RENT === $result['type'] ? 4 : 1; // Collection order
            }

            if ($paymentSubject > 0) {
                $template = $paymentSubject ? $UserDbPaymentsController->getPaymentSubject($paymentSubject) : null;
                $farmYear = $GLOBALS['Farming']['years'][$result['farming_year']]['farming_year'];
                $template = str_replace('[[nomer_na_dogovor]]', $result['c_num'], $template);
                $template = str_replace('[[stopanska_godina]]', $farmYear, $template);
            } else {
                $template = null;
                if (null != $paymentSubjectText) {
                    $template = $paymentSubjectText;
                }
            }
            $amount = number_format($result['amount'], 2, '.', '');
            $amount_abs = abs($amount);
            sscanf($amount_abs, '%d.%d', $whole, $fraction);
            $farmingKey = array_search($paymentData->farming_id, array_column($farmings, 'id'));
            $printData['company'] = $farmings[$farmingKey]['company'];
            $printData['bulstat'] = $farmings[$farmingKey]['bulstat'];
            $printData['owner_names'] = $paymentData->owner_names;
            $printData['recipient_name'] = $paymentData->recipient;
            $printData['recipient_egn'] = $paymentData->recipient_egn;
            $printData['date'] = date('d.m.Y', strtotime($result['date'])) . 'г.';
            $printData['pko_number'] = $result['id'];
            $printData['for'] = $template;
            $printData['price'] = BGNtoEURO($amount);
            $printData['price_text'] = '';
            if ($amount < 0) {
                $printData['price_text'] = 'минус ';
            }

            $amountEuro = convertBGNtoEURO($amount_abs);
            sscanf($amount_abs, '%d.%d', $whole, $fraction);
            sscanf($amountEuro, '%d.%d', $wholeEuro, $fractionEuro);

            $printData['price_text'] .= $FarmingController->StringHelper->numToString($whole) . ' лева и ' . number_format(($fraction = ($amount_abs - $whole) * 100), 0) . ' ст.';
            $printData['price_text'] .= ' / ';
            $printData['price_text'] .= str_replace('един ', 'едно ', $FarmingController->StringHelper->numToString($wholeEuro)) . ' евро и ' . number_format(($fractionEuro = ($amountEuro - $wholeEuro) * 100), 0) . ' цента.';

            $templateKey = 48;
            if (COLLECTION_TYPE_RENT == $result['type']) {
                $templateKey = 4;
                $printData['company'] = $paymentData->owner_names;
                if ($paymentData->owner_id && $paymentData->farming_id) {
                    $printData['company'] = '';
                }
                $printData['recipient'] = $paymentData->recipient_company;
                $printData['name'] = $paymentData->recipient;
                $printData['egn'] = $paymentData->recipient_egn;
            }

            $ltext = $FarmingController->StringHelper->loadTemplate($GLOBALS['Templates'][$templateKey]['template'], $printData);
            $pages[] = $ltext;
        }

        $finaltext = '';
        foreach ($pages as $page) {
            $finaltext .= '<page style="font-family: freeserif" format="A4">' . $page . '</page>';
        }

        return $finaltext;
    }

    public function exportCollectionsHtml($collectionIds, $collectionDate = null, $paymentSubject = null, $paymentSubjectText = null, $withoutRkoNumbering = false)
    {
        if (!$collectionIds) {
            return [];
        }
        $FarmingController = new FarmingController();
        $UserDbPaymentsController = new UserDbPaymentsController($this->Database);
        $results = $this->getCollectionsToExport($collectionIds);
        if (0 == count($results)) {
            return [];
        }
        $farmings = $FarmingController->getFarmings([
            'return' => ['*'],
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $this->User->GroupID],
            ],
            'sort' => 'id',
            'order' => 'ASC',
        ], false, false);
        $amountSum = 0;
        foreach ($results as $result) {
            $paymentData = json_decode($result['payment_data']);
            $paymentSubject = 1; // Collection order
            $template = $UserDbPaymentsController->getPaymentSubject($paymentSubject);
            $farmYear = $GLOBALS['Farming']['years'][$result['farming_year']]['farming_year'];
            $template = str_replace('[[nomer_na_dogovor]]', $result['c_num'], $template);
            $template = str_replace('[[stopanska_godina]]', $farmYear, $template);
            $amount = number_format($result['amount'], 2, '.', '');
            $amount_abs = abs($amount);
            $amountSum += $amount_abs;
            $farmingKey = array_search($paymentData->farming_id, array_column($farmings, 'id'));
            $printData['company'] = $farmings[$farmingKey]['company'];
            $printData['bulstat'] = $farmings[$farmingKey]['bulstat'];
            $printData['owner_names'] = $paymentData->owner_names;
            $printData['recipient_name'] = $paymentData->recipient;
            $printData['recipient_egn'] = $paymentData->recipient_egn;
            $printData['date'] = date('d.m.Y', strtotime($result['date'])) . 'г.';
            $printData['pko_number'] = $result['id'];
            $printData['for'] = $template;
            $printData['renta_types'] .= $paymentData->renta_type_name . ': ' . $amount_abs . ' лв. ';
        }
        sscanf($amountSum, '%d.%d', $whole, $fraction);
        $printData['price'] = $amountSum . ' лева';
        $printData['price_text'] = '';
        if ($amount < 0) {
            $printData['price_text'] = 'минус ';
        }
        $printData['price_text'] = $FarmingController->StringHelper->numToString($whole) . ' лева и ' . number_format(($fraction = ($amountSum - $whole) * 100), 0) . ' ст.';
        $ltext = $FarmingController->StringHelper->loadTemplate($GLOBALS['Templates'][48]['template'], $printData);
        $pages[] = $ltext;
        $finaltext = '';
        foreach ($pages as $page) {
            $finaltext .= '<page style="font-family: freeserif" format="A4">' . $page . '</page>';
        }

        return $finaltext;
    }

    public function hasCollectionsForPersonalUse($personalUseIds)
    {
        $collections = $this->getCollectionsForPersonalUse($personalUseIds);
        if ($collections[0]['count'] > 0) {
            throw new MTRpcException('PERSONAL_USE_COLLECTIONS_EXIST', -33236);
        }

        return false;
    }

    public function getCollectionsForPersonalUse($personalUseIds)
    {
        $UserDbPaymentsController = new UserDbPaymentsController($this->Database);
        $personalUseInfo = $UserDbPaymentsController->getItemsByParams([
            'tablename' => 'su_personal_use pu',
            'return' => ['rel.contract_id', 'owner_id', 'year'],
            'joins' => [
                'left join su_contracts_plots_rel rel on rel.id = pu.pc_rel_id',
            ],
            'where' => [
                'id' => ['column' => 'id', 'prefix' => 'pu', 'compare' => 'IN', 'value' => $personalUseIds],
            ],
            'group' => 'rel.contract_id, owner_id, year',
        ]);

        if (empty($personalUseInfo)) {
            throw new Exception('Wrong input data');
        }

        return $UserDbPaymentsController->getItemsByParams([
            'tablename' => 'su_collections col',
            'return' => ['contract_id', 'farming_year', 'payment_data'],
            'where' => [
                'type' => ['column' => 'type', 'prefix' => 'col', 'compare' => '=', 'value' => COLLECTION_TYPE_PERSONAL_USE],
                'status' => ['column' => 'status', 'prefix' => 'col', 'compare' => '=', 'value' => true],
                'contract_id' => ['column' => 'contract_id', 'prefix' => 'col', 'compare' => '=', 'value' => $personalUseInfo[0]['contract_id']],
                'farming_year' => ['column' => 'farming_year', 'prefix' => 'col', 'compare' => '=', 'value' => $personalUseInfo[0]['year']],
                'owner_id' => ['column' => "(col.payment_data->>'owner_id' = " . $personalUseInfo[0]['owner_id'] . '::text)', 'compare' => '=', 'value' => true],
            ],
        ], true);
    }

    private function getCollectionsToExport($collectionIds)
    {
        $options = [
            'tablename' => 'su_collections',
            'return' => ["
                id,
                contract_id,
                round(cast(amount as numeric), 2) as amount,
                to_char(date, 'dd-mm-YYYY') as date,                
                recieved_from,
                user_name,
                bank_payment,
                payment_order,
                farming_year,
                payment_data,
                type
            "],
            'where' => [
                'contract_id' => ['column' => 'id', 'compare' => 'IN', 'value' => $collectionIds],
            ],
        ];

        return $this->DbHandler->getItemsByParams($options, false, false);
    }
}
