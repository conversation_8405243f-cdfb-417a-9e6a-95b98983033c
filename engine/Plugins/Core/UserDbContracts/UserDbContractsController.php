<?php

namespace TF\Engine\Plugins\Core\UserDbContracts;

use DateTime;
use Prado\Prado;
use TF\Application\Common\Config;
use TF\Engine\APIClasses\KVSContractsUpdate\KVSContractsUpdate;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;
use TF\Engine\Plugins\Core\Users\UsersController;

class UserDbContractsController extends UserDbController
{
    use KVSContractsUpdate;
    public const CONTRACT_PLOT_DUPLICATION_ERROR_CODE = -33656;
    public const CONTRACT_SOLD_PLOT_ERROR_CODE = -33659;
    public $DbHandler;
    public $Database;

    public function __construct($database)
    {
        $this->DbHandler = new UserDbContractsModel($database);
        $this->Database = $database;
    }

    public function getContractsData($params, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getContractsData($params, $counter, $returnOnlySQL);
    }

    public function getSalesContractsData($params, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getSalesContractsData($params, $counter, $returnOnlySQL);
    }

    public function getContractsPlotsRelations($contract_id = false)
    {
        return $this->DbHandler->getContractsPlotsRelations($contract_id);
    }

    public function getSalesContractsPlotsRelations($contract_id = false)
    {
        return $this->DbHandler->getSalesContractsPlotsRelations($contract_id);
    }

    public function getPlotDataForContracts($params, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getPlotDataForContracts($params, $counter, $returnOnlySQL);
    }

    public function getPlotDataForSalesContracts($params, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getPlotDataForSalesContracts($params, $counter, $returnOnlySQL);
    }

    public function getSalesContractsSubleasedPlot($params, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getSalesContractsSubleasedPlot($params, $counter, $returnOnlySQL);
    }

    public function getSubleaseContractName($gid, $activeAfter)
    {
        return $this->DbHandler->getSubleaseContractName($gid, $activeAfter);
    }

    public function getPlotIDsForContracts()
    {
        return $this->DbHandler->getPlotIDsForContracts();
    }

    public function getPlotIDsForSalesContracts()
    {
        return $this->DbHandler->getPlotIDsForSalesContracts();
    }

    public function updatePlotContractStatus($id_string, $status)
    {
        $this->DbHandler->updatePlotContractStatus($id_string, $status);
    }

    public function getContractPlotRelationID($contract_id, $plot_id)
    {
        return $this->DbHandler->getContractPlotRelationID($contract_id, $plot_id);
    }

    public function getPendingPayments($options, $counter = false)
    {
        return $this->DbHandler->getPendingPayments($options, $counter);
    }

    public function getPayments($options, $counter = false)
    {
        return $this->DbHandler->getPayments($options, $counter);
    }

    public function getSubleasedContractsData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getSubleasedContractsData($options, $counter, $returnOnlySQL);
    }

    public function getAnnexes($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getAnnexes($options, $counter, $returnOnlySQL);
    }

    public function getContractDataByPCRel($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getContractDataByPCRel($options, $counter, $returnOnlySQL);
    }

    public function getContractContragentsData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getContractContragentsData($options, $counter, $returnOnlySQL);
    }

    public function getFullContractDataByFilter($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getFullContractDataByFilter($options, $counter, $returnOnlySQL);
    }

    public function getContracts($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getContracts($options, $counter, $returnOnlySQL);
    }

    public function getContractPlotData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getContractPlotData($options, $counter, $returnOnlySQL);
    }

    public function getContractRentsData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getContractRentsData($options, $counter, $returnOnlySQL);
    }

    public function getPlotOwnerRelData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getPlotOwnerRelData($options, $counter, $returnOnlySQL);
    }

    public function getPlotFarmingRelData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getPlotFarmingRelData($options, $counter, $returnOnlySQL);
    }

    public function getEditedPlotsBeforeContractActiveDate($options, $counter = false)
    {
        return $this->DbHandler->getEditedPlotsBeforeContractActiveDate($options, $counter);
    }

    public function setResponseDataContracts($contractType, $hasContractsOwnWriteRights)
    {
        return $this->DbHandler->setResponseDataContracts($contractType, $hasContractsOwnWriteRights);
    }

    public function getContractType($contractId)
    {
        return $this->DbHandler->getContractType($contractId);
    }

    public function getResponseDataContracts($contractType, $hasContractsOwnWriteRights)
    {
        return $this->DbHandler->getResponseDataContracts($contractType, $hasContractsOwnWriteRights);
    }

    public function updateContractsInIds($contractIds, $dueDate)
    {
        return $this->DbHandler->updateContractsInIds($contractIds, $dueDate);
    }

    public function getBuyersSalesContractsRelation($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getBuyersSalesContractsRelation($options, $counter, $returnOnlySQL);
    }

    public function getReportSalesContracts($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getReportSalesContracts($options, $counter, $returnOnlySQL);
    }

    public function hasContractEditedPlots($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->hasContractEditedPlots($options, $counter, $returnOnlySQL);
    }

    public function getSalesContractsPlotsAndBayers($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getSalesContractsPlotsAndBayers($options, $counter, $returnOnlySQL);
    }

    public function getContractsFilteredPlots($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getContractsFilteredPlots($options, $counter, $returnOnlySQL);
    }

    public function isContractFromSublease($contractID)
    {
        return $this->DbHandler->isContractFromSublease($contractID);
    }

    public function hasSalesContractsRelation(array $params)
    {
        return $this->DbHandler->hasSalesContractsRelation($params);
    }

    public function deleteCPRelForCPRel($pc_rel_id)
    {
        return $this->DbHandler->deleteCPRelForCPRel($pc_rel_id);
    }

    public function getSalesContractsPlotsData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getSalesContractsPlotsData($options, $counter, $returnOnlySQL);
    }

    public function copyContractFileRelations($fromContractID, $toContractId)
    {
        return $this->DbHandler->copyContractFileRelations($fromContractID, $toContractId);
    }

    public function getContractFiles($options, $counter, $returnOnlySQL)
    {
        return $this->DbHandler->getContractFiles($options, $counter, $returnOnlySQL);
    }

    public function deleteContractFileRelation($fileId, $contractId)
    {
        return $this->DbHandler->deleteContractFileRelation($fileId, $contractId);
    }

    public function getOverallRenta($contractId)
    {
        $options = [
            'return' => [
                'overall_renta',
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $contractId],
            ],
        ];

        $result = $this->DbHandler->getContractsData($options, false, false);
        $overallRenta = null;
        if (!empty($result) && null != $result[0]['overall_renta']) {
            $overallRenta = $result[0]['overall_renta'];
        }

        return $overallRenta;
    }

    public function removeRentPerPlot($contractId)
    {
        return $this->DbHandler->removeRentPerPlot($contractId);
    }

    public function getTotalAreaForRent($contractId)
    {
        // get total area_for_rent
        $options = [
            'return' => [
                'sum(area_for_rent) as area_for_rent',
            ],
            'where' => [
                'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $contractId],
                'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'value' => 'added'],
            ],
        ];

        return $this->DbHandler->getContractPlotsData($options, false, false);
    }

    public function manageOverallRenta($contractId)
    {
        $overallRenta = $this->getOverallRenta($contractId);
        if ($overallRenta) {
            $this->removeRentPerPlot($contractId);
            // recalculate rent
            $totalAreaForRent = $this->getTotalAreaForRent($contractId);
            if (!empty($totalAreaForRent) && null != $totalAreaForRent[0]['area_for_rent']) {
                $options = [
                    'tablename' => 'su_contracts',
                    'mainData' => [
                        'renta' => ($overallRenta / $totalAreaForRent[0]['area_for_rent']),
                    ],
                    'where' => [
                        'id' => $contractId,
                    ],
                ];

                $this->editItem($options);
            }
        }
    }

    public function getNotAvailableContractsPlots($contractStartDate, $contractDueDate, $contractIds, $options = [], $plotIds = [], $excludeContracts = [], $contractFarming = null)
    {
        return $this->DbHandler->getNotAvailableContractsPlots($contractStartDate, $contractDueDate, $contractIds, $options, $plotIds, $excludeContracts, $contractFarming);
    }

    public function getSoldContractsPlots($contractStartDate, $contractIds, $plotIds)
    {
        return $this->DbHandler->getSoldContractsPlots($contractStartDate, $contractIds, $plotIds);
    }

    public function validatePlotAreas($plots, $plotsWithActiveContracts)
    {
        $invalidPlots = [];
        foreach ($plots as $plot) {
            foreach ($plotsWithActiveContracts as $activePlot) {
                if (in_array($plot['plot_id'] ?? $plot['gid'], json_decode($activePlot['plot_ids'], true))
                && bcdiv($plot['contract_area'], 1, 2) > bcdiv($activePlot['available_plot_area'], 1, 2)) {
                    $invalidPlots[] = $activePlot;
                }
            }
        }
        if (!empty($invalidPlots)) {
            throw new MTRpcException(UserDbContractsController::buildDuplicatePlotErrorMsg($invalidPlots), UserDbContractsController::CONTRACT_PLOT_DUPLICATION_ERROR_CODE);
        }
    }

    public function isPlotsParticipateInSublease($contractId)
    {
        $options = [
            'tablename' => 'subleases_view',
            'return' => [
                'plot_id',
            ],
            'where' => [
                'contract_id' => ['column' => 'origin_contract_id', 'compare' => '@>', 'value' => (string)$contractId],
            ],
        ];

        $plots = $this->DbHandler->getItemsByParams($options, false, false);

        if (!empty($plots)) {
            throw new MTRpcException('CHANGE_FARMING_SUBLEASE_ERROR', -33759);
        }

        return true;
    }

    /**
     * Get contract payments.
     *
     * @api-method read
     *
     * @param int $contract_id -The Contract id
     * @param int $year -The year for contract payments
     *
     * @return array
     */
    public function getContractUsedAreas($contract_id, $year)
    {
        // init controllers
        $UserDbPaymentsController = new UserDbPaymentsController($this->Database);
        $UsersController = new UsersController('Users');

        $contractID = $contract_id;
        $currentAnnexId = 0;

        $start_date = $GLOBALS['Farming']['years'][$year]['year'] . '-09-30';
        $due_date = ($GLOBALS['Farming']['years'][$year]['year'] - 1) . '-10-01';

        $currentAnnexId = $UserDbPaymentsController->getCurrentAnnexId($contractID, $start_date, $due_date);

        $farming_year_from_id = $UsersController->StringHelper->getFarmingYearByDate($start_date);
        $farming_year_to_id = $UsersController->StringHelper->getFarmingYearByDate($due_date);
        $farming_years = [];

        for ($i = $farming_year_from_id; $i <= $farming_year_to_id; $i++) {
            $farming_years[] = $i;
        }

        // build main query(getting all payments by owners)
        $options = [
            'return' => [
                'o.id as owner_id', 'is_dead',
                "array_agg(c.id || '|' || kvs.gid || '|' || po.percent) as plots_percent",
                'round(SUM ((pc.area_for_rent * po.percent / 100)::numeric), 4) as owner_area',
                'round(SUM ((pc.contract_area * po.percent / 100)::numeric), 4) as contract_area',
            ],
            'where' => [
                'is_heritor' => ['column' => 'is_heritor', 'compare' => '=', 'prefix' => 'po', 'value' => 'FALSE'],
                'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
                'percent' => ['column' => 'percent', 'compare' => '>', 'prefix' => 'po', 'value' => '0'],
            ],
            'group' => 'o.id, c.id',
            // this parameter will be used for joining charged renta table
            'chosen_year' => $year,
            'contract_id_string' => $contractID,
            'start_date' => $start_date,
            'due_date' => $due_date,
        ];

        if (0 != $currentAnnexId[0]['id']) {
            $options['where']['annex_id'] = ['column' => 'id', 'compare' => '=', 'prefix' => 'a', 'value' => $currentAnnexId[0]['id']];
        }

        $results = $UserDbPaymentsController->getPaymentsForOwners($options, false, false);

        $return = [];
        // iterate and convert results to grid format
        for ($i = 0; $i < count($results); $i++) {
            // assign variables for easy access
            $ownerID = $results[$i]['owner_id'];
            $plots_percent = explode(',', trim($results[$i]['plots_percent'], '{}'));

            foreach ($plots_percent as $element) {
                $launch = explode('|', $element);
                $results[$i]['owner_plots_percent'][$launch[0]][$launch[1]] = $launch[2];
            }

            $results[$i]['owner_area'] = number_format($results[$i]['owner_area'], 3, '.', '');
            $results[$i]['contract_area'] = number_format($results[$i]['contract_area'], 3, '.', '');

            // if owner is dead then heritor info is required
            if ($results[$i]['is_dead']) {
                // get heritors data and put it inside row element children
                $results[$i]['children'] = $this->getOwnerHeritors($ownerID . '.*{1}', 1, $ownerID, null, $contract_id, $year, $results[$i]);
            }
            // Use id as key of array for faster searching in the tree
            $return[$ownerID] = $results[$i];
        }

        return $return;
    }

    public function findAreaByOwner($ownerId, $path, array $areas, $areaType = 'owner_area')
    {
        foreach ($areas as $area) {
            if (empty($path) && !isset($area['path']) && $ownerId == $area['owner_id']) {
                switch ($areaType) {
                    case 'contract_area':
                        return $area['contract_area'];
                    case 'owner_area':
                        return $area['owner_area'];
                    default:
                        return $area['owner_area'];
                }
            } // It's parent
            if (!empty($path) && isset($area['path']) && $ownerId == $area['owner_id'] && $path == $area['path']) {
                switch ($areaType) {
                    case 'contract_area':
                        return $area['contract_area'];
                    case 'owner_area':
                        return $area['owner_area'];
                    default:
                        return $area['owner_area'];
                }
            } // It's is searched nod
            if (!empty($area['children'])) {
                $found = $this->findAreaByOwner($ownerId, $path, $area['children'], $areaType);
            }
            if ($found) {
                return $found;
            }
        }

        return '';
    }

    public function addPlotToContractAnnex($data, $User, $Module, $Service, bool $excludeRemovedWithAnnex)
    {
        $UserDbController = new UserDbController($User->Database);
        $usersController = new UsersController('Users');
        $UserDbPaymentsController = new UserDbPaymentsController($User->Database);
        $UserDbContractsController = new UserDbContractsController($User->Database);

        $plot_id_array = [];

        if (!$data['plot_data_array'] || 0 == count($data['plot_data_array']) || !$data['contract_id'] || !(int) $data['contract_id']) {
            return [];
        }

        $oldOptions = [
            'return' => [
                'c.*',
                'cpr.plot_id',
            ],
            'tablename' => $UserDbController->DbHandler->tableContracts . ' c',
            'innerjoin' => [
                'table' => $UserDbController->DbHandler->contractsPlotsRelTable . ' cpr',
                'condition' => ' ON (c.id = cpr.contract_id)',
            ],
            'where' => [
                'id' => ['column' => 'c.id', 'compare' => '=', 'value' => $data['contract_id']],
            ],
        ];
        $oldData = $UserDbController->getItemsByParams($oldOptions);

        // No Rights to operate with "Договори за собственост"
        if (Config::CONTRACT_TYPE_OWN == $oldData[0]['nm_usage_rights'] && !$User->HasContractsOwnWriteRights) {
            return $this->setResponseDataContracts($oldData[0]['nm_usage_rights'], $User->HasContractsOwnWriteRights);
        }

        $isFromSublease = $this->isContractFromSublease($data['contract_id']);
        if ($isFromSublease) {
            throw new MTRpcException('CANNOT_MODIFY_FROM_SUBLEASE_CONTRACT', -33752);
        }

        $additionalParams = [];
        if (!empty($oldData[0]['parent_id'])) {
            $additionalParams = [
                'parent_id' => $oldData[0]['parent_id'],
                'start_date' => $oldData[0]['start_date'],
                'due_date' => $oldData[0]['due_date'],
            ];
        }
        $UserDbPaymentsController->hasPaymentRestriction($data['contract_id'], $additionalParams);

        $plotDataArrayCount = count($data['plot_data_array']);
        for ($i = 0; $i < $plotDataArrayCount; $i++) {
            $plot_id_array[] = $data['plot_data_array'][$i]['plot_id'];
        }

        $options = [
            'return' => [
                'c.farming_id',
                'c.nm_usage_rights',
                'c.start_date',
                'MAX(
                    CASE WHEN c.is_annex THEN
                        CASE WHEN a.due_date IS NULL THEN c.due_date 
                        WHEN a.due_date < c.due_date THEN c.due_date
                        ELSE a.due_date 
                        END
                    ELSE c.due_date 
                    END    
                ) as due_date',
                'c.renta',
                'c.id',
            ],
            'where' => [
                'contract_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'c', 'value' => $data['contract_id']],
            ],
            'group' => 'c.id',
        ];

        $results = $this->getContractDataByPCRel($options, false, false);
        if (!$results[0]['due_date']) {
            $results[0]['due_date'] = null;
        }
        $originalContractDueDate = $results[0]['due_date'];
        $originalContractType = $results[0]['nm_usage_rights'];
        $originalContractFarming = $results[0]['farming_id'];

        $options = [
            'return' => [
                'pc.plot_id',
                'pc.contract_area as contract_area',
                'c.id as contract_id',
                'c.from_sublease as from_sublease',
                'c.farming_id as farming_id',
                'c.start_date as contract_start_date',
                'c.due_date as contract_due_date',
                'pc.id as pc_rel_id',
                'array_agg(sscpr.contract_area_for_sale) as sold_area',
                'array_agg(sscpr.sales_contract_id) as sales_contract_ids',
                'c.parent_id as parent_id',
                'c.is_annex::int as is_annex',
                '( 
                    select 
                        (case when max(scpr.id) is not null then true else false end) as is_removed_by_annex 
                    from su_contracts_plots_rel scpr
                    left join su_contracts sa on sa.id = scpr.contract_id and sa.active = true 
                    where 
                        scpr.plot_id = pc.plot_id
                        and (sa.start_date <= c.due_date and sa.due_date >= c.start_date)
                        and scpr.annex_action = \'removed\'
                ) as is_removed_by_annex',
            ],
            'where' => [
                'plot_id' => ['column' => 'plot_id', 'compare' => 'IN', 'prefix' => 'pc', 'value' => $plot_id_array],
                'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
                'nm_usage_rights' => ['column' => 'nm_usage_rights', 'compare' => '<>', 'prefix' => 'c', 'value' => 4],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'start_date' => ['column' => 'start_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $results[0]['due_date']],
                'due_date' => ['column' => "(CASE WHEN c.nm_usage_rights = 1 THEN '9999-12-31 00:00:00' ELSE c.due_date END)", 'compare' => '>=', 'value' => $results[0]['start_date']],
            ],
            'group' => 'pc.plot_id, pc.contract_area,c.id, a.parent_id, pc.id',
            'exclude_removed_with_annex' => $excludeRemovedWithAnnex,
            'joins' => [
                'left join su_sales_contracts_plots_rel sscpr on sscpr.pc_rel_id = pc.id',
            ],
        ];

        if (true == $data['is_annex'] && $data['annex_parent_id']) {
            // if we are adding plot to annex, exclude parent contracts from validation
            $options['where']['contract_id'] = ['column' => 'id', 'compare' => '<>', 'prefix' => 'c', 'value' => $data['annex_parent_id']];
        } else {
            // if we are adding plot to contract, exclude contract annexes from validation
            $options['exclude_contract_annexes'] = true;
            $options['contract_id'] = $data['contract_id'];
        }

        $options['exclude_contract_if_annex_or_contract_from_sublease'] = true;

        $tempPercent = 100;
        if (isset($data['ownership']) && $data['ownership']) {
            $fraction = $data['ownership']['fraction'];
            $percent = (float)$data['ownership']['percent'];
            $tempPercent = $percent;

            $temp = explode('/', $fraction);

            $nomerator = (int)$temp[0];
            $denominator = (int)$temp[1];

            if ($nomerator > $denominator && 100 != $percent) {
                throw new MTRpcException('WRONG_FRACTION', -33219);
            }
            if ($percent < 0 || $percent > 100) {
                throw new MTRpcException('WRONG_PERCENT_OWNERSHIP', -33220);
            }
        }

        $data['plot_data_array'] = array_map((function ($plot) use ($tempPercent) {
            $plotData = array_merge($plot, [
                'contract_area' => $plot['contract_area'] * ($tempPercent / 100),
                'area_for_rent' => $plot['area_for_rent'] * ($tempPercent / 100),
                'kvs_allowable_area' => $plot['kvs_allowable_area'] * ($tempPercent / 100),
            ]);

            if ($plot['document_area'] < $plot['contract_area']) {
                throw new MTRpcException('WRONG_PLOT_AREA', -33221);
            }

            return $plotData;
        }), $data['plot_data_array']);

        $startDate = !empty($data['annex_start_date']) ? $data['annex_start_date'] : $data['contract_start_date'];
        $dueDate = !empty($data['annex_due_date']) ? $data['annex_due_date'] : $data['contract_due_date'];

        // Check contracts plots exist in another contract with overlapping period
        $plotsWithActiveContracts = $UserDbContractsController->getNotAvailableContractsPlots(
            $startDate,
            $dueDate,
            [$data['contract_id']],
            ['editContract' => true],
            $plot_id_array,
            null,
            null
        );
        $UserDbContractsController->validatePlotAreas($data['plot_data_array'], $plotsWithActiveContracts);

        $addedRelations = [];
        $plotsData = [];
        for ($i = 0; $i < $plotDataArrayCount; $i++) {
            // Disable it in order to make faster adding plots into contracts. It is enable again below
            $UserDbController->disableRentaMatViewTriggers();

            $options = [
                'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
                'mainData' => [
                    'contract_id' => $data['contract_id'],
                    'plot_id' => $data['plot_data_array'][$i]['plot_id'],
                    'contract_area' => $data['plot_data_array'][$i]['contract_area'],
                    'area_for_rent' => filter_var($data['plot_data_array'][$i]['area_for_rent'], FILTER_VALIDATE_FLOAT, FILTER_NULL_ON_FAILURE),
                    'price_per_acre' => filter_var($data['plot_data_array'][$i]['price_per_acre'], FILTER_VALIDATE_FLOAT, FILTER_NULL_ON_FAILURE),
                    'price_sum' => filter_var($data['plot_data_array'][$i]['price_sum'], FILTER_VALIDATE_FLOAT, FILTER_NULL_ON_FAILURE),
                    'rent_per_plot' => filter_var($data['plot_data_array'][$i]['rent_per_plot'], FILTER_VALIDATE_FLOAT, FILTER_NULL_ON_FAILURE),
                    'contract_end_date' => $originalContractDueDate,
                    'fraction' => $fraction,
                    'percent' => $percent,
                    'kvs_allowable_area' => $data['plot_data_array'][$i]['kvs_allowable_area'] >= 0 ? $data['plot_data_array'][$i]['kvs_allowable_area'] : null,
                ],
            ];

            $new_rel_id = $UserDbController->addItem($options);

            if (1 == $originalContractType) {
                $UserDbController->addFarmingAsOwnerForPcRelId($new_rel_id, $originalContractFarming);
            }

            $addedRelations[] = $new_rel_id;

            $usersController->groupLog($User->Name, $User->UserID, $User->GroupID, $Module, $Service, __METHOD__, $options['mainData'], ['contract_plot_rel_id' => $new_rel_id], 'adds contract-plot relation');

            // add owners data to added plots
            if (array_key_exists($data['plot_data_array'][$i]['plot_id'], $data['owner_results_by_plot'])) {
                $owner_data = $data['owner_results_by_plot'][$data['plot_data_array'][$i]['plot_id']];
                $owner_dataCount = count($owner_data);
                for ($j = 0; $j < $owner_dataCount; $j++) {
                    $options = [
                        'tablename' => $UserDbController->DbHandler->plotsOwnersRelTable,
                        'mainData' => $owner_data[$j],
                    ];

                    $options['mainData']['pc_rel_id'] = $new_rel_id;
                    unset($options['mainData']['plot_id'], $options['mainData']['id']);

                    $po_rel_id = $UserDbController->addItem($options);

                    $usersController->groupLog($User->Name, $User->UserID, $User->GroupID, $Module, $Service, __METHOD__, array_merge($options['mainData'], ['plot_id' => $owner_data[$j]['plot_id']]), ['plot_owner_relation' => $po_rel_id], 'Add annex plot-owner relation');
                }
            }

            // add farming data to added plots
            if (array_key_exists($data['added_plots_data'][$i]['plot_id'], $data['farming_results_by_plot'])) {
                $farming_data = $data['farming_results_by_plot'][$data['added_plots_data'][$i]['plot_id']];
                $farming_dataCount = count($farming_data);
                for ($j = 0; $j < $farming_dataCount; $j++) {
                    $options = [
                        'tablename' => $UserDbController->DbHandler->plotsFarmingRelTable,
                        'mainData' => $farming_data[$j],
                    ];

                    $options['mainData']['pc_rel_id'] = $new_rel_id;
                    unset($options['mainData']['plot_id'], $options['mainData']['id']);

                    $pf_rel_id = $UserDbController->addItem($options);
                    $usersController->groupLog($User->Name, $User->UserID, $User->GroupID, $Module, $Service, __METHOD__, array_merge($options['mainData'], ['plot_id' => $farming_data[$j]['plot_id']]), ['plot_farming_relation' => $pf_rel_id], 'Add annex plot-farming relation');
                }
            }

            $groupId = Prado::getApplication()->getUser()->GroupID;

            $oldOptions = [
                'tablename' => $UserDbController->DbHandler->tableKVS . ' kvs',
                'return' => [
                    'document_area',
                    'ekate',
                    'kad_ident',
                    'max(date_uploaded) as date_uploaded',
                    'max(file_id) as file_id',
                ],
                'joins' => [
                    "LEFT JOIN dblink('host=" . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' dbname=' . DEFAULT_DB_DATABASE . "',
                    'SELECT id AS file_id, ekate AS ekatte_code, date_uploaded, group_id, shape_type FROM su_users_files') AS files (file_id integer, ekatte_code text, date_uploaded timestamp, group_id integer, shape_type integer) 
                    ON kvs.ekate = files.ekatte_code AND files.group_id = {$groupId} AND files.shape_type = " . Config::LAYER_TYPE_KVS_OSZ,
                ],
                'where' => [
                    'gid' => ['column' => 'gid', 'compare' => '=', 'value' => $data['plot_data_array'][$i]['plot_id']],
                ],
                'group' => 'kvs.ekate, kvs.kad_ident, kvs.ekate, kvs.document_area',
            ];
            $oldValues = current($UserDbController->getItemsByParams($oldOptions, false, false));

            if ($oldValues['file_id']) {
                if (!array_key_exists('plots_data', $plotsData[$oldValues['ekate']])) {
                    $plotsData[$oldValues['ekate']]['plots_data'] = [];
                }

                $plotsData[$oldValues['ekate']]['file_id'] = $oldValues['file_id'];

                array_push($plotsData[$oldValues['ekate']]['plots_data'], [
                    'kad_ident' => $oldValues['kad_ident'],
                    'edit_active_from' => (DateTime::createFromFormat('Y-m-d H:i:s.u', $oldValues['date_uploaded']))->modify('-1 day')->format('Y-m-d'),
                ]);
            }

            $usersController->groupLog($User->Name, $User->UserID, $User->GroupID, $Module, $Service, __METHOD__, $options, $oldValues, 'edits document area in layer_kvs');
        }

        $this->manageOverallRenta($data['contract_id']);
        $UserDbController->enableRentaMatViewTriggers();

        if (count($plotsData) > 0) {
            foreach ($plotsData as $ekatte => $data) {
                if ($data['file_id']) {
                    $this->tryResolveContracts($data['file_id'], $ekatte, $data['plots_data']);
                }
            }
        }

        // if all plots were added
        return $addedRelations;
    }

    public function getContractsToProcess(array $params): array
    {
        return $this->DbHandler->getContractsToProcess($params);
    }

    public function validatePlotAreasInContracts($plotIds, $startDate, $dueDate, $parentId): array
    {
        return $this->DbHandler->validatePlotAreasInContracts($plotIds, $startDate, $dueDate, $parentId);
    }

    public static function buildDuplicatePlotErrorMsg($plotsData): array
    {
        foreach ($plotsData as $key => $plotData) {
            $contarctsInfo = json_decode($plotData['contracts_info'], true);
            foreach ($contarctsInfo as $contractInfo) {
                if (array_key_exists($contractInfo['contract_id'], $formatedResult)) {
                    $formatedResult[$contractInfo['contract_id']]['gids'][] = $contractInfo['gid'];
                    if (!in_array($contractInfo['kad_ident'], $formatedResult[$contractInfo['contract_id']]['kad_idents_array'])) {
                        $formatedResult[$contractInfo['contract_id']]['kad_idents_array'][] = $contractInfo['kad_ident'];
                        $formatedResult[$contractInfo['contract_id']]['plot_kad_idents'] .= ',' . $contractInfo['kad_ident'];
                        $formatedResult[$contractInfo['contract_id']]['contract_area'] .= ',' . $contractInfo['contract_area'];
                    }
                } else {
                    $formatedResult[$contractInfo['contract_id']] = [
                        'c_num' => $contractInfo['contract_c_num'],
                        'gids' => [$contractInfo['gid']],
                        'plot_kad_idents' => $contractInfo['kad_ident'],
                        'ekatte_name' => $contractInfo['ekatte_name'],
                        'contract_area' => $contractInfo['contract_area'],
                        'c_id' => $contractInfo['contract_id'],
                        'is_annex' => $contractInfo['is_annex'],
                    ];
                }
            }
        }

        return $formatedResult;
    }

    public function getOwnerHeritorsForExports($path, $rat_ownage, $level, $pc_relation_id)
    {
        $UserDbOwnersController = new UserDbOwnersController($this->Database);
        $UsersController = new UsersController('Users');

        ++$level;
        $options = [
            'return' => [
                'h.id',
                "name || ' ' || surname || ' ' || lastname as owner_names",
                'owner_id',
                'is_dead',
                'path',
                'o.*',
                "('ЕГН ' || egn) as egn_text",
                'o.address  as address',
                "(SELECT poi.percent FROM su_plots_owners_rel poi WHERE poi.path = h.path AND poi.is_heritor = true AND poi.pc_rel_id = {$pc_relation_id}) as percent",
            ],
            'where' => [
                'path' => ['column' => 'path', 'compare' => '~', 'value' => $path],
            ],
        ];

        $counter = $UserDbOwnersController->getOwnersHeritors($options, true);

        if (0 == $counter[0]['count']) {
            return [];
        }
        $results = $UserDbOwnersController->getOwnersHeritors($options, false, false);
        $resultsCount = count($results);
        $sum_custom_ownage = 0;
        $heritors = 0;
        for ($i = 0; $i < $resultsCount; $i++) {
            if (null != $results[$i]['percent']) {
                $sum_custom_ownage += $results[$i]['percent'];
            } else {
                $heritors++;
            }
        }

        for ($i = 0; $i < $resultsCount; $i++) {
            if (null == $results[$i]['percent']) {
                $results[$i]['percent'] = ($rat_ownage - $sum_custom_ownage) / $heritors;
                $results[$i]['percent'] = ($results[$i]['percent'] > 0) ? $results[$i]['percent'] : 0;
            }
            if ($results[$i]['is_dead']) {
                $results[$i]['children'] = $this->getOwnerHeritorsExports(
                    $results[$i]['path'] . '.*{1}',
                    $results[$i]['percent'],
                    $level,
                    $pc_relation_id
                );
            }
            $results[$i]['is_heritor'] = true;
            $results[$i]['level'] = $level;
            $results[$i]['percent'] = floor((float)$results[$i]['percent'] * 10000) / 10000;
            if (0 != $results[$i]['percent']) {
                $results[$i]['rat_ownage'] = $results[$i]['percent'] . '% (' . $UsersController->StringHelper->float2rat($results[$i]['percent'] / 100) . ')';
            } else {
                $results[$i]['rat_ownage'] = $results[$i]['percent'] . '%';
            }
        }
        $filtered_children = [];
        while (!empty($results)) {
            $childRes = [];
            foreach ($results as $key => $value) {
                if (false == $results[$key]['is_dead'] && $results[$key]['percent'] > 0) {
                    $filtered_children[] = $results[$key];
                } elseif ($results[$key]['children'] && $results[$key]['percent'] > 0) {
                    foreach ($results[$key]['children'] as $child) {
                        $childRes[] = $child;
                    }
                }
            }
            $results = $childRes;
        }

        return $filtered_children;
    }

    /**
     * @return array
     */
    public function getKontragentMatchedColumns(&$template)
    {
        $kontragent_matches = [];
        $kontragent_rep_matches = [];
        if (false !== strpos($template, '[[kontragent ]]')) {
            $template = str_replace('[[kontragent ]]', '[[kontragent]]', $template);
        }
        if (false !== strpos($template, '[[kontragent_rep ]]')) {
            $template = str_replace('[[kontragent_rep ]]', '[[kontragent_rep]]', $template);
        }
        $old_kontragent_requested = false !== strpos($template, '[[kontragent]]');
        $old_kontragent_rep_requested = false !== strpos($template, '[[kontragent_rep]]');
        // back-port for old [[kontragent]] string
        if ($old_kontragent_requested) {
            $template = str_replace(
                '[[kontragent]]',
                '[[kontragent owner_names egn_eik address company_address lk_nomer lk_izdavane prepiska mol phone fax mobile email iban rent-place owner_reps remark]]',
                $template
            );
        }
        if ($old_kontragent_rep_requested) {
            $template = str_replace('[[kontragent_rep]]', '[[kontragent_rep rep_names rep_egn rep_lk rep_lk_izdavane rep_address rep_phone rep_iban rep_rent_place remark]]', $template);
        }
        $kontragent_requested = false !== strpos($template, '[[kontragent');
        $kontragent_rep_requested = false !== strpos($template, '[[kontragent_rep');
        if ($kontragent_requested) {
            preg_match_all("/\[\[kontragent (.*?)\]\]/", $template, $kontragent_matches);
        }
        if ($kontragent_rep_requested) {
            preg_match_all("/\[\[kontragent_rep (.*?)\]\]/", $template, $kontragent_rep_matches);
        }

        return [$kontragent_matches, $kontragent_rep_matches];
    }

    /**
     * @return array
     */
    public function getOwnerAndReps($contract_id)
    {
        $UserDbOwnersController = new UserDbOwnersController($this->Database);

        $dbLink = "'host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "'::text";
        $ekattesTable = "(SELECT * FROM dblink({$dbLink}, 'SELECT ekatte_code, ekatte_name FROM su_ekatte') AS ekattes(ekatte_code VARCHAR, ekatte_name VARCHAR(255))) as ekattes";
        $options = [
            'return' => array_merge(
                $this->getOwnersSelect(),
                [
                    'ekattes.ekatte_name as rent_place',
                    'round(
                        sum((po.percent / 100) * pc.contract_area)::numeric,
                        3
                    ) AS contract_signer_contract_area',
                    'round(
                        sum((po.percent / 100) * pc.kvs_allowable_area)::numeric,
                        3
                    ) AS contract_signer_cultivated_area',
                    'round(
                        sum((po.percent / 100) * pc.area_for_rent)::numeric,
                        3
                    ) AS contract_signer_contract_rent_area',
                ]
            ),
            'where' => [
                'contract_id' => [
                    'column' => 'contract_id',
                    'compare' => '=',
                    'prefix' => 'pc',
                    'value' => $contract_id,
                ],
                'is_heritor' => ['column' => 'is_heritor', 'compare' => '=', 'prefix' => 'po', 'value' => 'false'],
                'percent' => ['column' => 'percent', 'compare' => '>', 'prefix' => 'po', 'value' => '0'],
            ],
            'group' => 'o.id, po.is_heritor, po.path, ekattes.ekatte_name',
            'leftjoin' => [
                'table' => $ekattesTable,
                'condition' => ' ON ekattes.ekatte_code = o.rent_place',
            ],
        ];

        return $UserDbOwnersController->getOwnersDataByContract($options, false, false);
    }

    /**
     * @param array $columns
     * @param array $owner
     *
     * @return string
     */
    public function setOwnerTemplateStr($columns, $owner)
    {
        $data = [];

        foreach ($columns as $column) {
            if (isset($data[$column])) {
                continue;
            }

            if ('egn_eik' === $column || 'egn' === $column) {
                if (true == $owner['is_heritor']) {
                    if (!empty($owner['egn_text'])) {
                        $data[$column] = $owner['egn_text'];
                    }
                } else {
                    if (!empty($owner['egn_eik'])) {
                        $data[$column] = $owner['egn_eik'];
                    }
                }
            }

            if ('owner_names' === $column && !empty($owner['owner_names'])) {
                $data[$column] = $owner['owner_names'];
            }

            if ('remark' === $column && !empty($owner['remark'])) {
                $data[$column] = $owner['remark'];
            }

            if ('contract_signer_contract_area' === $column && !empty($owner['contract_signer_contract_area'])) {
                $data[$column] = 'площ по договор: ' . $owner['contract_signer_contract_area'] . ' дка';
            }

            if ('contract_signer_contract_rent_area' === $column && !empty($owner['contract_signer_contract_rent_area'])) {
                $data[$column] = 'площ за рента: ' . $owner['contract_signer_contract_rent_area'] . ' дка';
            }

            if ('contract_signer_cultivated_area' === $column && !empty($owner['contract_signer_cultivated_area'])) {
                $data[$column] = 'обработвама площ: ' . $owner['contract_signer_cultivated_area'] . ' дка';
            }

            if ('contract_signer_owner_note' === $column && !empty($owner['contract_signer_owner_note'])) {
                $data[$column] = 'бележка: ' . $owner['contract_signer_owner_note'];
            }

            if ('lk_nomer' === $column && !empty($owner['lk_nomer'])) {
                $data[$column] = 'ЛК номер ' . $owner['lk_nomer'];
            }

            if ('lk_izdavane' === $column && !empty($owner['lk_izdavane'])) {
                $data[$column] = 'ЛК издадена на/от ' . $owner['lk_izdavane'];
            }

            if ('mol' === $column && !empty($owner['mol'])) {
                $data[$column] = 'с управител ' . $owner['mol'];
            }

            if ('address' === $column && !empty($owner['address'])) {
                $data[$column] = 'адрес ' . $owner['address'];
            }

            if ('phone' === $column && !empty($owner['phone'])) {
                $data[$column] = 'телефон ' . $owner['phone'];
            }

            if ('fax' === $column && !empty($owner['fax'])) {
                $data[$column] = 'факс ' . $owner['fax'];
            }

            if ('mobile' === $column && !empty($owner['mobile'])) {
                $data[$column] = 'мобилен ' . $owner['mobile'];
            }

            if ('company_address' === $column && !empty($owner['company_address'])) {
                $data[$column] = 'адрес по регистрация ' . $owner['company_address'];
            }

            if ('email' === $column && !empty($owner['email'])) {
                $data[$column] = 'e-mail ' . $owner['email'];
            }

            if ('iban' === $column && !empty($owner['iban'])) {
                $data[$column] = 'Банкова сметка ' . $owner['iban'];
            }

            if ('rent-place' === $column && !empty($owner['rent_place'])) {
                $data[$column] = 'Получаване на рента в ' . $owner['rent_place'];
            }
        }

        return implode(', ', $data);
    }

    public function setRepTemplateStr($columns, $rep)
    {
        $str = ' ';
        foreach ($columns as $column) {
            if ('rep_names' === $column) {
                $str .= $rep['rep_names'];
            }
            if ('rep_egn' === $column && $rep['rep_egn']) {
                $str .= ', ' . $rep['rep_egn'];
            }
            if ('rep_lk' === $column && $rep['rep_lk']) {
                $str .= ', ' . $rep['rep_lk'];
            }
            if ('rep_lk_izdavane' === $column && $rep['rep_lk_izdavane']) {
                $str .= ', ' . $rep['rep_lk_izdavane'];
            }
            if ('rep_address' === $column && $rep['rep_address']) {
                $str .= ', ' . $rep['rep_address'];
            }
            if ('rep_phone' === $column && $rep['rep_phone']) {
                $str .= ', ' . $rep['rep_phone'];
            }
            if ('rep_iban' === $column && $rep['rep_iban']) {
                $str .= ', ' . $rep['rep_iban'];
            }
        }

        return $str;
    }

    /**
     * @return array
     */
    public function getFinalFarmings()
    {
        $FarmingController = new FarmingController('Farming');

        $final_farming = [];
        // options for farming query
        $options = [
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => Prado::getApplication()->getUser()->GroupID],
            ],
        ];
        $farming_array = $FarmingController->getFarmings($options);
        $farmCount = count($farming_array);
        if (0 == $farmCount) {
            return $final_farming;
        }
        for ($i = 0; $i < $farmCount; $i++) {
            $final_farming[$farming_array[$i]['id']] = $farming_array[$i];
        }

        return $final_farming;
    }

    public function concatRepsData($owner_reps, $proxy_rel)
    {
        $proxies = [];
        $exploded_rels = explode(',', trim($proxy_rel, '{}'));
        foreach ($exploded_rels as $key => $value) {
            $arr = explode('->', $value);
            $proxies[(int)$arr[0]][] = ' № ' . $arr[1];
        }

        foreach ($proxies as $key => $value) {
            $text = ', съгласно пълномощно ';
            if (count($proxies[$key]) > 1) {
                $text = ', съгласно пълномощни ';
            }
            $owner_reps = str_replace('rep_id->' . $key, $text . implode(', ', $proxies[$key]), $owner_reps);
        }
        if (false !== strpos($owner_reps, 'rep_id->')) {
            $owner_reps = explode('rep_id->', $owner_reps);
            $ownersRepsCount = count($owner_reps);
            for ($i = 1; $i < $ownersRepsCount; $i++) {
                $tmp_reps = explode(',', $owner_reps[$i]);
                unset($tmp_reps[0]);
                $owner_reps[$i] = implode(',', $tmp_reps);
            }
            $owner_reps = implode('', $owner_reps);
        }

        return $owner_reps;
    }

    /**
     * @param array $owner
     *
     * @return array
     */
    public function setOwnerDataArr($owner)
    {
        $ownersDataArr = [];
        $ownersDataArr['pc_rel_id_array'] = [];
        $ownersDataArr['percent'] = [];
        $pc_owner_rel_id = explode(',', trim($owner['pc_owner_rel_id'], '{}'));
        foreach ($pc_owner_rel_id as $key => $value) {
            $arr = explode('-', $value);
            $ownersDataArr['pc_rel_id_array'][] = $arr[0];
            $ownersDataArr['percent'][] = $arr[1];
        }

        return $ownersDataArr;
    }

    public function getOwnersSelect()
    {
        $selectRapresentative = 'array_to_string(array_agg(DISTINCT((CASE WHEN r.owner_id <> o.id OR r.owner_id IS NULL THEN ';

        return [
            'o.id as owner_id',
            'o.*',
            'po.is_heritor',
            'po.path',
            "array_agg(pc.id || '-' || po.percent) as pc_owner_rel_id",
            "array_agg (DISTINCT(r.id || '->'||(case WHEN po.proxy_num IS NOT NULL
                AND po.proxy_num <> '' THEN
                po.proxy_num || '/' || to_char(po.proxy_date,'DD.MM.YYYY') END)
                || (
                    CASE
                        WHEN po.notary_name IS NOT NULL THEN
                            concat_ws (
                                ' ',
                                ' заверено от нотариус',
                                po.notary_name,
                                'вписан в регистъра на нотариалната камара под №',
                                po.notary_number,
                                'с адрес',
                                po.notary_address
                            )
                        ELSE
                            ''
                        END
                    ))) AS proxy_rel",
            "(CASE WHEN owner_type = 1 THEN name || ' ' || surname || ' ' || lastname ELSE company_name END) as owner_names",
            "(CASE WHEN owner_type = 1 THEN 'ЕГН ' || egn ELSE 'ЕИК ' || eik END) as egn_eik",
            "('ЕГН ' || egn) as egn_text",
            'o.address as address',
            $selectRapresentative . " 'rep_id->'|| r.id END))), ', ') as rep_id",
            $selectRapresentative . "r.rep_name || ' ' || r.rep_surname || ' ' || r.rep_lastname END))), ', ') as rep_names",
            $selectRapresentative . "' с ЕГН ' || r.rep_egn END))), ', ') as rep_egn",
            $selectRapresentative . "' ЛК номер ' || r.rep_lk END))), ', ') as rep_lk",
            $selectRapresentative . "' ЛК издадена на/от ' || r.rep_lk_izdavane END))), ', ') as rep_lk_izdavane",
            $selectRapresentative . "' Банкова сметка' || r.iban END))), ', ') as rep_iban",
            $selectRapresentative . "' телефон ' || r.rep_phone END))), ', ') as rep_phone",
            $selectRapresentative . "' адрес ' || r.rep_address END))), ', ') as rep_address",
            $selectRapresentative . "' Получаване на рента в ' || ekattes.ekatte_name END))), ', ') as rep_rent_place",
            'array_to_string(array_agg(DISTINCT((CASE WHEN r.owner_id <> o.id OR r.owner_id IS NULL '
            . "THEN r.rep_name || ' ' || r.rep_surname || ' ' || r.rep_lastname || ', с ЕГН ' || r.rep_egn || "
            . "', ЛК номер ' || r.rep_lk || ', ЛК издадена на/от ' || r.rep_lk_izdavane || ', телефон ' || r.rep_phone || ', адрес ' || r.rep_address || ', Банкова сметка ' || r.iban || ', Получаване на рента в '  || ekattes.ekatte_name  || "
            . " 'rep_id->'|| r.id"
            . " END))), ', ') as owner_reps",
        ];
    }

    /**
     * get owner heritors recursively.
     *
     * @param string $path
     * @param string $level
     * @param string $root_id
     * @param array|bool $parent_plots_ownage
     * @param int $contract_id
     * @param int $year
     *
     * @return array
     */
    private function getOwnerHeritors($path, $level, $root_id, $parent_plots_ownage = false, $contract_id, $year, $ownerResults)
    {
        // init controllers
        $UserDbPaymentsController = new UserDbPaymentsController($this->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->Database);

        $heritor_data = [];
        $heritor_percent_by_plots = [];
        $level++;

        $owner_plots_percent = $ownerResults['owner_plots_percent'];
        $start_date = $GLOBALS['Farming']['years'][$year]['year'] . '-09-30';
        $due_date = ($GLOBALS['Farming']['years'][$year]['year'] - 1) . '-10-01';

        // get all heritors
        $options = [
            'return' => ['owner_id', 'is_dead', 'path'],
            'where' => [
                'path' => ['column' => 'path', 'compare' => '~', 'value' => $path],
            ],
        ];

        $heritor_results = $UserDbOwnersController->getOwnersHeritors($options, false, false);
        $options = [
            'return' => [
                'o.id as root_id', 'po.percent', 'gid', 'c.id as contract_id', 'pc.id as pc_rel_id',
                '(pc.area_for_rent) as contract_area', '(pc.contract_area) as real_contract_area', 'COALESCE(pu.area, 0) as pu_area',
            ],
            'where' => [
                'owner_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'o', 'value' => $root_id],
                'contract_type' => ['column' => 'nm_usage_rights', 'compare' => 'NOT IN', 'prefix' => 'c', 'value' => [1, 4]],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'is_annnex' => ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
                'percent' => ['column' => 'percent', 'compare' => '>', 'prefix' => 'po', 'value' => '0'],
                'is_heritor' => ['column' => 'is_heritor', 'compare' => '=', 'prefix' => 'po', 'value' => 'FALSE'],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
                'contract_id' => ['column' => "(c.id = {$contract_id} or a.id = {$contract_id})", 'compare' => '=', 'value' => true],
            ],
            'start_date' => $start_date,
            'due_date' => $due_date,
            'year_id' => [$year],
            'group' => 'gid, o.id, po.percent, c.id, pc.id, cr.renta, cr.nat_is_converted, cr.renta_nat, r.rent_place, a.id, a.renta, a.renta_nat, a.renta_nat_type_id, pu.area',
        ];

        $plot_results = $UserDbPaymentsController->getPayrollData($options, false, false);

        // iterate all plots
        for ($i = 0; $i < count($plot_results); $i++) {
            $plotID = $plot_results[$i]['gid'];

            // get heritors for the current plot
            $options = [
                'return' => ['h.id', 'owner_id', 'is_dead', 'path',
                    "(SELECT poi.percent FROM su_plots_owners_rel poi WHERE poi.path = h.path AND poi.is_heritor = true AND poi.pc_rel_id = {$plot_results[$i]['pc_rel_id']}) as percent",
                ],
                'where' => [
                    'path' => ['column' => 'path', 'compare' => '~', 'value' => $path],
                ],
            ];
            $results = $UserDbOwnersController->getOwnersHeritors($options, false, false);
            $contract_arr[] = $plot_results[$i]['contract_id'];

            $sum_custom_ownage = 0;
            $heritors = 0;
            $all_heritors_count = count($results);

            for ($j = 0; $j < $all_heritors_count; $j++) {
                if (null != $results[$j]['percent']) {
                    $sum_custom_ownage += $results[$j]['percent'];
                } else {
                    $heritors++;
                }
            }

            for ($j = 0; $j < count($results); $j++) {
                $ownerID = $results[$j]['owner_id'];
                $parent_path_e = explode('.', $results[$j]['path'], -1);
                $parent_path = implode('.', $parent_path_e);

                if (!$results[$j]['percent'] || 0 == $results[$j]['percent']) {
                    if ($parent_plots_ownage[$plotID][$parent_path] || '0' == $parent_plots_ownage[$plotID][$parent_path]) {
                        if ('0' == $results[$j]['percent']) {
                            $results[$j]['percent'] = '0';
                        } else {
                            $results[$j]['percent'] = ($parent_plots_ownage[$plotID][$parent_path] - $sum_custom_ownage) / $heritors;
                        }
                        $heritor_percent_by_plots[$plotID][$results[$j]['path']] = $results[$j]['percent'];
                    } else {
                        if (null == $results[$j]['percent']) {
                            $results[$j]['percent'] = ($plot_results[$i]['percent'] - $sum_custom_ownage) / $heritors;
                            $heritor_percent_by_plots[$plotID][$results[$j]['path']] = $results[$j]['percent'];
                        } elseif ('0' == $results[$j]['percent']) {
                            $heritor_percent_by_plots[$plotID][$results[$j]['path']] = '0';
                        }
                    }
                } else {
                    $heritor_percent_by_plots[$plotID][$results[$j]['path']] = $results[$j]['percent'];
                }

                if ($results[$j]['percent'] <= 0) {
                    continue;
                }

                $results[$j]['percent_heritor'] = 0;
                if ($owner_plots_percent[$plot_results[$i]['contract_id']][$plotID] > 0) {
                    $results[$j]['percent_heritor'] = $results[$j]['percent'] / $owner_plots_percent[$plot_results[$i]['contract_id']][$plotID];
                }
                $heritor_data[$ownerID]['owner_plots_percent'][$plot_results[$i]['contract_id']][$plotID] = $results[$j]['percent'];
                $heritor_data[$ownerID]['area'] += ($plot_results[$i]['contract_area'] * ($results[$j]['percent'] / 100) - ($plot_results[$i]['pu_area'] * $results[$j]['percent_heritor']));
                $heritor_data[$ownerID]['contract_area'] += ($plot_results[$i]['real_contract_area'] * ($results[$j]['percent'] / 100) - ($plot_results[$i]['pu_area'] * $results[$j]['percent_heritor']));
            }
        }

        $return = [];
        // prepare heritor results for grid format
        for ($i = 0; $i < count($heritor_results); $i++) {
            $ownerID = $heritor_results[$i]['owner_id'];
            $heritor_results[$i]['owner_area'] = number_format($heritor_data[$ownerID]['area'], 3, '.', '');
            $heritor_results[$i]['contract_area'] = number_format($heritor_data[$ownerID]['contract_area'], 3, '.', '');
            $heritor_results[$i]['owner_plots_percent'] = $heritor_data[$ownerID]['owner_plots_percent'];
            if ($heritor_results[$i]['is_dead']) {
                $heritor_results[$i]['children'] = $this->getOwnerHeritors(
                    $heritor_results[$i]['path'] . '.*{1}',
                    $level,
                    $root_id,
                    $heritor_percent_by_plots,
                    $contract_id,
                    $year,
                    $heritor_results[$i]
                );
            }
            // Use id as key of array for faster searching in the tree
            $return[$ownerID] = $heritor_results[$i];
        }

        return $return;
    }

    private function getOwnerHeritorsExports($path, $rat_ownage, $level, $pc_relation_id)
    {
        $UserDbOwnersController = new UserDbOwnersController($this->Database);
        $UsersController = new UsersController('Users');

        ++$level;
        $options = [
            'return' => [
                'h.id',
                "name || ' ' || surname || ' ' || lastname as owner_names",
                'owner_id',
                'is_dead',
                'path',
                'o.*',
                "('ЕГН ' || egn) as egn_text",
                'o.address  as address',
                "(SELECT poi.percent FROM su_plots_owners_rel poi WHERE poi.path = h.path AND poi.is_heritor = true AND poi.pc_rel_id = {$pc_relation_id}) as percent",
            ],
            'where' => [
                'path' => ['column' => 'path', 'compare' => '~', 'value' => $path],
            ],
        ];

        $counter = $UserDbOwnersController->getOwnersHeritors($options, true);

        if (0 == $counter[0]['count']) {
            return [];
        }
        $results = $UserDbOwnersController->getOwnersHeritors($options, false, false);
        $resultsCount = count($results);
        $sum_custom_ownage = 0;
        $heritors = 0;
        for ($i = 0; $i < $resultsCount; $i++) {
            if (null != $results[$i]['percent']) {
                $sum_custom_ownage += $results[$i]['percent'];
            } else {
                $heritors++;
            }
        }

        for ($i = 0; $i < $resultsCount; $i++) {
            if (null == $results[$i]['percent']) {
                $results[$i]['percent'] = ($rat_ownage - $sum_custom_ownage) / $heritors;
                $results[$i]['percent'] = ($results[$i]['percent'] > 0) ? $results[$i]['percent'] : 0;
            }
            if ($results[$i]['is_dead']) {
                $results[$i]['children'] = $this->getOwnerHeritorsExports(
                    $results[$i]['path'] . '.*{1}',
                    $results[$i]['percent'],
                    $level,
                    $pc_relation_id
                );
            }
            $results[$i]['is_heritor'] = true;
            $results[$i]['level'] = $level;
            $results[$i]['percent'] = floor((float)$results[$i]['percent'] * 10000) / 10000;
            if (0 != $results[$i]['percent']) {
                $results[$i]['rat_ownage'] = $results[$i]['percent'] . '% (' . $UsersController->StringHelper->float2rat($results[$i]['percent'] / 100) . ')';
            } else {
                $results[$i]['rat_ownage'] = $results[$i]['percent'] . '%';
            }
        }
        $filtered_children = [];
        while (!empty($results)) {
            $childRes = [];
            foreach ($results as $key => $value) {
                if (false == $results[$key]['is_dead'] && $results[$key]['percent'] > 0) {
                    $filtered_children[] = $results[$key];
                } elseif ($results[$key]['children'] && $results[$key]['percent'] > 0) {
                    foreach ($results[$key]['children'] as $child) {
                        $childRes[] = $child;
                    }
                }
            }
            $results = $childRes;
        }

        return $filtered_children;
    }
}
