<?php

namespace TF\Engine\Plugins\Core\Interfaces;

interface ILayerable
{
    /**
     * Get layer extent.
     */
    public function getMaxExtent(string $tableName);

    /**
     * Get layer items.
     */
    public function getItemsByParams(array $options, bool $counter = false, bool $returnOnlySQL = false, array $bindingParams = []);

    /**
     * Check if table exists.
     */
    public function getTableNameExist(string $tablename);
}
