<?php

namespace TF\Engine\Plugins\Core\Filter;

trait FilterGroups
{
    public function createWhereOrGroupSQL($sql, $groups, $returnOnlySQL)
    {
        $sql .= ' AND (( true ';
        $countGroups = count($groups);
        foreach ($groups as $key => $groupOptions) {
            if ($groupOptions['where']) {
                $sql = $this->createWhereSQL($sql, $groupOptions['where'], $returnOnlySQL);
            }

            if ($groupOptions['whereOr']) {
                $sql = $this->createWhereOrSQL($sql, $groupOptions['whereOr'], $returnOnlySQL);
            }

            // checks category with 'OR' clause
            if (count($groupOptions['category'])) {
                $idx = array_search('-1', $groupOptions['category']);

                $withoutCategorySelected = false;
                // if selected "Без категория"
                if (false !== $idx) {
                    unset($options['category'][$idx]);

                    $withoutCategorySelected = true;
                }

                $arrCategory = array_values($groupOptions['category']);

                if (count($arrCategory)) {
                    $sql .= ' AND (category IN(';

                    for ($i = 0; $i < count($arrCategory); $i++) {
                        $sql .= "'" . $arrCategory[$i] . "'";

                        if ($i < count($arrCategory) - 1) {
                            $sql .= ', ';
                        }
                    }

                    $sql .= ')';

                    if ($withoutCategorySelected) {
                        $sql .= ' OR category IS NULL)';
                    } else {
                        $sql .= ')';
                    }
                } else {
                    $sql .= ' AND category IS NULL';
                }
            }

            if ($key + 1 == $countGroups) {
                $sql .= ' ))';
            } else {
                $sql .= ' ) OR ( true ';
            }
        }

        return $sql;
    }

    public function createWhereGroupBinds($cmd, $groups)
    {
        foreach ($groups as $groupOptions) {
            if ($groupOptions['where']) {
                $this->createWhereBinds($cmd, $groupOptions['where']);
            }

            if ($groupOptions['whereOr']) {
                $this->createWhereBinds($cmd, $groupOptions['whereOr']);
            }
        }
    }
}
