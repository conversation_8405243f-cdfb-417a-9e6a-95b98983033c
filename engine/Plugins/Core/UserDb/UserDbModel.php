<?php

namespace TF\Engine\Plugins\Core\UserDb;

use DateTime;
use Exception;
use PDO;
use Prado\Data\TDbCommand;
use Prado\Data\TDbConnection;
use Prado\Exceptions\TDbException;
use Prado\Prado;
use TF\Application\Common\Config;
use TF\Application\Entity\DefaultColors;
use TF\Application\Entity\DTO\EkatteDto;
use TF\Application\Entity\LayerStyles;
use TF\Application\Entity\UserLayers;
use TF\Engine\Kernel\ArrayHelper;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Kernel\PdoHelper;
use TF\Engine\Kernel\Sentry\Sentry;
use TF\Engine\Kernel\StringHelper;
use TF\Engine\Plugins\Core\Filter\FilterGroups;

include_once __DIR__ . '/../../../Plugins/Core/Farming/conf.php';

/**
 * UsersModel class file.
 *
 * <AUTHOR>
 */

/**
 * UsersModel class, handle queries on user's db.
 *
 * @property TDbConnection $DbModule
 */
class UserDbModel
{
    use FilterGroups;

    public $tableUserEkatteRel = false;
    public $tableEkatte = false;
    public $tableOblasti = false;
    public $tableObshtini = false;
    public $tableKmetstva = false;
    public $tablePlotOwners = false;
    public $tableLayers = false;
    public $tableContracts = false;
    public $tableContractsGroup = false;
    public $tableOwners = false;
    public $tablePayments = false;
    public $tableCollections = false;
    public $contractsOwnersRelTable = false;
    public $contractsPlotsRelTable = false;
    public $plotsOwnersRelTable = false;
    public $plotsFarmingRelTable = false;
    public $cropLayersTable = false;
    public $cropLayersDataTable = false;
    public $tableKVS = false;
    public $tableKVSBorders = false;
    public $tableKVSBordersClusterIdx = false;
    /**
     * @var TDbConnection
     */
    public $DbModule;
    public $tableOverlaps = false;
    public $tableOverlapsData = false;
    public $tableSoilSamples = false;
    public $tableAgreements = false;
    public $tableAgreementsData = false;
    public $tableSoilSamplesFiles = false;
    public $tableAppliedSoilSamples = false;
    public $tableAnnexes = false;
    public $tableTemplates = false;
    public $tableChargedRenta = false;
    public $tableNaturaChargedRenta = false;
    public $tableRentaTypes = false;
    public $tableTransactions = false;
    public $tableNaturaTransactions = false;
    public $tableNaturaPayments = false;
    public $tablePersonalUse = false;
    public $tablePersonalUseRents = false;
    public $tableCoverageFiles = false;
    public $tableCoverageData = false;
    public $tableOwnersDocuments = false;
    public $tableOwnersFiles = false;
    public $tableOwnersReps = false;
    public $tableContractsContragents = false;
    public $tableContractsFarmingContragents = false;
    public $tableSubleasesPlotsContractsRel = false;
    public $tableDiaryConfigs = false;
    public $tableDiaryEvents = false;
    public $tableDiaryProducts = false;
    public $tableDiaryProduces = false;
    public $tableHypothecs = false;
    public $tableHeritors = false;
    public $tableKvsEditLog = false;
    public $tableSubleasesPlotsArea = false;
    public $chargedRentaParams = false;
    public $chargedRentaNaturaParams = false;
    public $tableCooperators = false;
    public $tableCooperatorsDocuments = false;
    public $chargedRentaHistory = false;
    public $tableDiaryExpenses = false;
    public $tableCooperatorHeritors = false;
    public $tableCooperatorsCapital = false;
    public $tableDividendsAnnualReport = false;
    public $tableDividendsPayment = false;
    public $tableCooperatorsAnualDividends = false;
    public $tableCooperatorsFiles = false;
    public $tableSalesContracts = false;
    public $salesContractsPlotsRelTable = false;
    public $salesContractsFilesTable = false;
    public $tableBuyers = false;
    public $tableSalesContractsBuyersRel = false;
    public $viewTopicLayerByOwnerName = false;
    public $viewTopicLayerByOwnerNameLabelItems = false;
    public $viewTopicLayerByTenantName = false;
    public $viewTopicLayerByAgreement = false;
    public $viewTopicLayerByCategory = false;
    public $viewTopicLayerByNTP = false;
    public $viewTopicLayerByOwnership = false;
    public $subleasesView = false;
    public $tableLayerGps = false;
    public $tablePaymentSubjects = false;
    public $tableThematicMaps = false;
    public $tableThematicMapsColors = false;
    public $tableAlarmSettings = false;
    public $userFilesTable = false;
    public $contractsFilesRelTable = false;
    public $filesDeletionsLog = false;
    public $tableAbLines = false;
    public $tableConsolidationDo = false;
    public $tableConsolidationDoInfo = false;
    public $tableConsolidationZd = false;
    public $tableConsolidationZdInfo = false;
    public $tableOszFilesPlots = false;
    public $contractsRentsRelTable = false;

    protected $arrayHelper;

    public function __construct($database)
    {
        $this->tableUserEkatteRel = DEFAULT_DB_PREFIX . 'users_ekatte_rel';
        $this->tableEkatte = DEFAULT_DB_PREFIX . 'ekatte';
        $this->tableEkatteCombobox = 'ekate_combobox';
        $this->tableObshtini = DEFAULT_DB_PREFIX . 'obshtini';
        $this->tableOblasti = DEFAULT_DB_PREFIX . 'oblasti';
        $this->tableKmetstva = DEFAULT_DB_PREFIX . 'kmetstva';
        $this->tableLayers = DEFAULT_DB_PREFIX . 'users_layers';
        $this->tablePlotOwners = DEFAULT_DB_PREFIX . 'owners';
        $this->tableContracts = DEFAULT_DB_PREFIX . 'contracts';
        $this->tableContractsGroup = DEFAULT_DB_PREFIX . 'contract_group';
        $this->contractsRentsRelTable = DEFAULT_DB_PREFIX . 'contracts_rents';
        $this->tableOwners = DEFAULT_DB_PREFIX . 'owners';
        $this->tablePayments = DEFAULT_DB_PREFIX . 'payments';
        $this->tableCollections = DEFAULT_DB_PREFIX . 'collections';
        $this->contractsOwnersRelTable = DEFAULT_DB_PREFIX . 'contracts_owners_rel';
        $this->contractsPlotsRelTable = DEFAULT_DB_PREFIX . 'contracts_plots_rel';

        $this->userFilesTable = DEFAULT_DB_PREFIX . 'user_files';
        $this->contractsFilesRelTable = DEFAULT_DB_PREFIX . 'contracts_files_rel';
        $this->filesDeletionsLog = DEFAULT_DB_PREFIX . 'files_deletions_log';

        $this->plotsOwnersRelTable = DEFAULT_DB_PREFIX . 'plots_owners_rel';
        $this->plotsFarmingRelTable = DEFAULT_DB_PREFIX . 'plots_farming_rel';
        $this->tableKVS = 'layer_kvs';
        $this->tableKVSBorders = 'layer_kvs_borders';
        $this->tableKVSBordersClusterIdx = 'layer_kvs_borders_geom_idx';
        $this->tableOwnersDocuments = DEFAULT_DB_PREFIX . 'owners_documents';
        $this->tableOwnersFiles = DEFAULT_DB_PREFIX . 'owners_files';
        $this->tableOwnersReps = DEFAULT_DB_PREFIX . 'owners_reps';
        $this->tableContractsContragents = DEFAULT_DB_PREFIX . 'contracts_contragents';
        $this->tableContractsFarmingContragents = DEFAULT_DB_PREFIX . 'contracts_farming_contragents';

        $this->cropLayersTable = DEFAULT_DB_PREFIX . 'crop_layers';
        $this->cropLayersDataTable = DEFAULT_DB_PREFIX . 'crop_layers_data';

        $this->tableOverlaps = DEFAULT_DB_PREFIX . 'overlaps';
        $this->tableOverlapsData = DEFAULT_DB_PREFIX . 'overlaps_data';

        $this->tableSoilSamples = DEFAULT_DB_PREFIX . 'soil_samples';
        $this->tableSoilSamplesFiles = DEFAULT_DB_PREFIX . 'soil_sample_files';
        $this->tableAppliedSoilSamples = DEFAULT_DB_PREFIX . 'applied_soil_samples';

        $this->tableAgreements = DEFAULT_DB_PREFIX . 'agreements';
        $this->tableTmpAgreements = 'tmp_agreements';
        $this->tableAgreementsData = DEFAULT_DB_PREFIX . 'agreements_data';

        $this->tableAnnexes = DEFAULT_DB_PREFIX . 'annexes';

        $this->tableTemplates = DEFAULT_DB_PREFIX . 'templates';
        $this->tableChargedRenta = DEFAULT_DB_PREFIX . 'charged_renta';
        $this->tableNaturaChargedRenta = DEFAULT_DB_PREFIX . 'charged_renta_natura';

        $this->tableRentaTypes = DEFAULT_DB_PREFIX . 'renta_types';
        $this->tableTransactions = DEFAULT_DB_PREFIX . 'transactions';
        $this->tableNaturaTransactions = DEFAULT_DB_PREFIX . 'transactions_natura';
        $this->tableNaturaPayments = DEFAULT_DB_PREFIX . 'payments_natura';
        $this->tablePersonalUse = DEFAULT_DB_PREFIX . 'personal_use';
        $this->tablePersonalUseRents = DEFAULT_DB_PREFIX . 'personal_use_rents';
        $this->tableCoverageFiles = DEFAULT_DB_PREFIX . 'coverage_files';
        $this->tableCoverageData = DEFAULT_DB_PREFIX . 'coverage_data';
        $this->tableSubleasesPlotsContractsRel = DEFAULT_DB_PREFIX . 'subleases_plots_contracts_rel';

        $this->tableDiaryConfigs = DEFAULT_DB_PREFIX . 'diary_configs';
        $this->tableDiaryEvents = DEFAULT_DB_PREFIX . 'diary_events';
        $this->tableDiaryProducts = DEFAULT_DB_PREFIX . 'diary_treatments_products';
        $this->tableDiaryProduces = DEFAULT_DB_PREFIX . 'diary_produces';

        $this->tableHypothecs = DEFAULT_DB_PREFIX . 'hypothecs';
        $this->tableHeritors = DEFAULT_DB_PREFIX . 'heritors';
        $this->tableKvsEditLog = 'layer_kvs_edit_log';

        $this->tableSubleasesPlotsArea = DEFAULT_DB_PREFIX . 'subleases_plots_area';
        $this->chargedRentaParams = DEFAULT_DB_PREFIX . 'charged_renta_params';
        $this->chargedRentaNaturaParams = DEFAULT_DB_PREFIX . 'charged_renta_natura_params';
        $this->chargedRentaHistory = DEFAULT_DB_PREFIX . 'charged_renta_history';

        $this->tableCooperators = DEFAULT_DB_PREFIX . 'cooperators';
        $this->tableCooperatorsDocuments = DEFAULT_DB_PREFIX . 'cooperators_documents';
        $this->tableDiaryExpenses = DEFAULT_DB_PREFIX . 'diary_expenses';
        $this->tableCooperatorHeritors = DEFAULT_DB_PREFIX . 'cooperator_heritors';
        $this->tableCooperatorsFiles = DEFAULT_DB_PREFIX . 'cooperators_files';
        $this->tableCooperatorsCapital = DEFAULT_DB_PREFIX . 'cooperators_capital';

        $this->tableDividendsAnnualReport = DEFAULT_DB_PREFIX . 'devidends_annual_report';
        $this->tableDividendsPayment = DEFAULT_DB_PREFIX . 'devidends_payment';
        $this->tableCooperatorsAnualDividends = DEFAULT_DB_PREFIX . 'cooperators_annual_dividends';
        $this->tableCooperatorsAnualDividends = DEFAULT_DB_PREFIX . 'cooperators_annual_dividends';

        $this->tableCreditors = DEFAULT_DB_PREFIX . 'hypothecs_creditors';
        $this->tableHypothecsPlotsRel = DEFAULT_DB_PREFIX . 'hypothecs_plots_rel';
        $this->tableHypothecsPayments = DEFAULT_DB_PREFIX . 'hypothecs_payments';
        $this->tableHypothecsFiles = DEFAULT_DB_PREFIX . 'hypothecs_files';

        $this->tableOSZFiles = DEFAULT_DB_PREFIX . 'osz_files';
        $this->tableOSZFilesPlots = DEFAULT_DB_PREFIX . 'osz_files_plots';

        $this->tableSalesContracts = DEFAULT_DB_PREFIX . 'sales_contracts';
        $this->salesContractsPlotsRelTable = DEFAULT_DB_PREFIX . 'sales_contracts_plots_rel';
        $this->salesContractsFilesTable = DEFAULT_DB_PREFIX . 'sales_contracts_files';

        $this->tableBuyers = DEFAULT_DB_PREFIX . 'buyers';
        $this->tableSalesContractsBuyersRel = DEFAULT_DB_PREFIX . 'sales_contracts_buyers_rel';

        $this->tableThematicMaps = DEFAULT_DB_PREFIX . 'thematic_maps';
        $this->tableThematicMapsColors = DEFAULT_DB_PREFIX . 'thematic_maps_colors';
        $this->tableLayerGps = 'layer_gps';

        $this->tableAlarmSettings = DEFAULT_DB_PREFIX . 'alert_settings';

        $this->viewTopicLayerByOwnerName = 'topic_layer_kvs_by_owner_name_mat_view';
        $this->viewTopicLayerByOwnerNameLabelItems = 'topic_layer_kvs_by_owner_name_label_items';
        $this->viewTopicLayerByTenantName = 'topic_layer_kvs_by_tenant_name_mat_view';
        $this->viewTopicLayerByAgreement = 'topic_layer_kvs_by_agreement_mat_view';
        $this->viewTopicLayerByCategory = 'topic_layer_kvs_by_category_mat_view';
        $this->viewTopicLayerByNTP = 'topic_layer_kvs_by_ntp_mat_view';
        $this->viewTopicLayerByOwnership = 'topic_layer_kvs_by_ownership_mat_view';

        $this->viewTopicLayerByOwnerNameLabels = 'topic_layer_kvs_by_owner_name_label_items';

        $this->tablePaymentSubjects = DEFAULT_DB_PREFIX . 'payment_subjects';
        $this->tableAbLines = DEFAULT_DB_PREFIX . 'ab_lines';
        $this->subleasesView = 'subleases_view';

        $appName = APP_NAME;
        if (!Prado::getApplication() instanceof TShellApplication) {
            $dsn = 'pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ";dbname={$database};options='--application_name={$appName}';";
            $this->DbModule = new TDbConnection($dsn, DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
            $this->DbModule->Active = true;
            $this->DbModule->setAttribute(PDO::ATTR_STRINGIFY_FETCHES, false);
        } else {
            $dsn = 'pgsql:host=' . DBLINK_HOST . ';port=' . DBLINK_PORT . ";dbname={$database};options='--application_name={$appName}';";
            $this->DbModule = new TDbConnection($dsn, DBLINK_USERNAME, DBLINK_PASSWORD);
            $this->DbModule->Active = true;
            $this->DbModule->setAttribute(PDO::ATTR_STRINGIFY_FETCHES, false);
        }

        $dsn_remote = 'pgsql:host=' . DBLINK_HOST . ';port=' . DBLINK_PORT . ';dbname=' . DBLINK_DATABASE . ";options='--application_name={$appName}';";
        $this->DbModule2 = new TDbConnection($dsn_remote, DBLINK_USERNAME, DBLINK_PASSWORD);
        $this->DbModule2->Active = true;
        $this->DbModule2->setAttribute(PDO::ATTR_STRINGIFY_FETCHES, false);

        $this->tableConsolidationDo = DEFAULT_DB_PREFIX . 'consolidation_do';
        $this->tableConsolidationZd = DEFAULT_DB_PREFIX . 'consolidation_zd';
        $this->tableConsolidationDoInfo = DEFAULT_DB_PREFIX . 'consolidation_do_info';
        $this->tableConsolidationZdInfo = DEFAULT_DB_PREFIX . 'consolidation_zd_info';

        $this->arrayHelper = new ArrayHelper();
    }

    public function getLayerColumnValues(string $tableName, string $columnName, array $options = [])
    {
        $sql = "SELECT DISTINCT(TRIM({$columnName}::text)) as {$columnName} FROM {$tableName} WHERE true";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], false);
        }

        $sql .= " ORDER BY TRIM({$columnName}::text) ASC";

        $cmd = $this->DbModule->createCommand($sql);

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getKVSDefinition($database, $tmp_name)
    {
        $sql = 'SELECT column_name FROM information_schema.columns ';
        $sql .= "where table_catalog = :dbname and
                table_schema = 'public' and
                table_name = :tmp_name";

        $tmp_name = 'tmp_kvs_' . $tmp_name;

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':dbname', $database);
        $cmd->bindParameter(':tmp_name', $tmp_name);

        return $cmd->query()->readAll();
    }

    public function getWorkDefinition($database, $tmp_name)
    {
        $sql = 'SELECT column_name FROM information_schema.columns ';
        $sql .= "where table_catalog = :dbname and
                table_schema = 'public' and
                table_name = :tmp_name";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':dbname', $database);
        $cmd->bindParameter(':tmp_name', $tmp_name);

        return $cmd->query()->readAll();
    }

    public function getGspDefinition($database, $tmp_name)
    {
        $sql = 'SELECT column_name FROM information_schema.columns ';
        $sql .= "where table_catalog = :dbname and
                table_schema = 'public' and
                table_name = :tmp_name";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':dbname', $database);
        $cmd->bindParameter(':tmp_name', $tmp_name);

        return $cmd->query()->readAll();
    }

    public function getKMSDefinition($database, $tmpTableName)
    {
        $sql = 'SELECT column_name FROM information_schema.columns ';
        $sql .= "where table_catalog = :dbname and
                table_schema = 'public' and
                table_name = :tmp_table";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':dbname', $database);
        $cmd->bindParameter(':tmp_table', $tmpTableName);

        return $cmd->query()->readAll();
    }

    /**
     * @deprecated Use method copyLayerItems instead
     */
    public function insertFromTmpWithDefinition($database, $tablename, $definition, $fileId)
    {
        $fieldsInsert[] = 'geom';
        $fieldsFrom[] = 'geom';
        $tmp_tablename = 'tmp_' . $tablename . '_' . $fileId;

        $simplifyGeomTolerance = $GLOBALS['Layers']['simplifyGeomTolerance'];
        $this->simplifyGeometry($tmp_tablename, $simplifyGeomTolerance);

        $flag = $this->getColumnNameExist($database, $tmp_tablename, $definition['number']);
        if ($definition['number'] && $flag) {
            $sql = "ALTER TABLE {$tmp_tablename} RENAME COLUMN {$definition['number']} TO technofarm_name";
            $cmd = $this->DbModule->createCommand($sql);
            $cmd->execute();
            $fieldsInsert[] = 'name';
            $fieldsFrom[] = 'technofarm_name';
        }

        $flag = $this->getColumnNameExist($database, $tmp_tablename, $definition['ekatte']);
        if ($definition['ekatte'] && $flag) {
            $sql = "ALTER TABLE {$tmp_tablename} RENAME COLUMN {$definition['ekatte']} TO technofarm_ekatte";
            $cmd = $this->DbModule->createCommand($sql);
            $cmd->execute();
            $fieldsInsert[] = 'ekatte';
            $fieldsFrom[] = 'technofarm_ekatte';
        }

        $flag = $this->getColumnNameExist($database, $tmp_tablename, $definition['crop_name']);
        if ($definition['crop_name'] && $flag) {
            $sql = "ALTER TABLE {$tmp_tablename} RENAME COLUMN {$definition['crop_name']} TO technofarm_crop_name";
            $cmd = $this->DbModule->createCommand($sql);
            $cmd->execute();
            $fieldsInsert[] = 'crop_name';
            $fieldsFrom[] = 'technofarm_crop_name';
        }

        $flag = $this->getColumnNameExist($database, $tmp_tablename, $definition['crop_code']);
        if ($definition['crop_code'] && $flag) {
            $sql = "ALTER TABLE {$tmp_tablename} RENAME COLUMN {$definition['crop_code']} TO technofarm_crop_code";
            $cmd = $this->DbModule->createCommand($sql);
            $cmd->execute();
            $fieldsInsert[] = 'crop_code';
            $fieldsFrom[] = 'technofarm_crop_code';
        }

        $sql = "INSERT INTO {$tablename}(" . implode(',', $fieldsInsert) . ') SELECT ' . implode(
            ',',
            $fieldsFrom
        ) . " FROM {$tmp_tablename}";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    /**
     * @deprecated Use method copyLayerItems instead
     */
    public function insertFromTmpWithDefinitionForWork(
        $database,
        $tablename,
        $tmp_tablename,
        $column,
        $definition,
        $addToExisting = false
    ) {
        if ($definition['crop_name'] && $definition['crop_name'] !== $column) {
            $columnExists = $this->getColumnNameExist($database, $tmp_tablename, $column);

            if ($columnExists) {
                $sql = "ALTER TABLE {$tmp_tablename} RENAME COLUMN {$column} TO {$column}_old";
                $cmd = $this->DbModule->createCommand($sql);
                $cmd->execute();
            }

            $sql = "ALTER TABLE {$tmp_tablename} RENAME COLUMN {$definition['crop_name']} TO {$column}";
            $cmd = $this->DbModule->createCommand($sql);
            $cmd->execute();
        }

        if (!$addToExisting) {
            $sql = "TRUNCATE {$tablename} CASCADE";
            $cmd = $this->DbModule->createCommand($sql);
            $cmd->execute();
        }

        $this->copyDataFromTMPWorkTable($tablename, $tmp_tablename);

        if (0 !== strpos($tmp_tablename, 'tmp_geom_')) {
            $this->addPrimaryKeyAndGidSequence($tablename);
        }
    }

    public function copyKVSData($database, $definition, $tmp_table)
    {
        $fieldsInsert[] = 'geom';
        $fieldsFrom[] = 'geom';

        // used for kad_ident implode
        $hasEkatte = false;
        $hasMasiv = false;
        $hasNumber = false;
        $addKvsBorders = false;

        $flag = $this->getColumnNameExist($database, $tmp_table, $definition['ekatte']);
        if ($definition['ekatte'] && $flag) {
            $sql = "ALTER TABLE {$tmp_table} RENAME COLUMN {$definition['ekatte']} TO technofarm_ekatte";
            $cmd = $this->DbModule->createCommand($sql);
            $cmd->execute();
            $fieldsInsert[] = 'ekate';
            $fieldsFrom[] = 'technofarm_ekatte';
            $hasEkatte = true;
            $addKvsBorders = true;
        }

        $flag = $this->getColumnNameExist($database, $tmp_table, $definition['masiv']);
        if ($definition['masiv'] && $flag) {
            $sql = "ALTER TABLE {$tmp_table} RENAME COLUMN {$definition['masiv']} TO technofarm_masiv";
            $cmd = $this->DbModule->createCommand($sql);
            $cmd->execute();
            $fieldsInsert[] = 'masiv';
            $fieldsFrom[] = 'technofarm_masiv';
            $hasMasiv = true;
        }

        $flag = $this->getColumnNameExist($database, $tmp_table, $definition['number']);
        if ($definition['number'] && $flag) {
            $sql = "ALTER TABLE {$tmp_table} RENAME COLUMN {$definition['number']} TO technofarm_number";
            $cmd = $this->DbModule->createCommand($sql);
            $cmd->execute();
            $fieldsInsert[] = 'number';
            $fieldsFrom[] = 'technofarm_number';
            $hasNumber = true;
        }

        $flag = $this->getColumnNameExist($database, $tmp_table, $definition['fullnumber']);
        if ($definition['fullnumber'] && $flag) {
            $addKvsBorders = true;
            $sql = "ALTER TABLE {$tmp_table} RENAME COLUMN {$definition['fullnumber']} TO technofarm_kad_ident";
            $cmd = $this->DbModule->createCommand($sql);
            $cmd->execute();
            $fieldsInsert[] = 'kad_ident';
            $fieldsFrom[] = 'technofarm_kad_ident';

            // get kad_ident separator
            $sql = "SELECT technofarm_kad_ident FROM {$tmp_table} LIMIT 1";
            $cmd = $this->DbModule->createCommand($sql);
            $result = $cmd->query()->read();

            $example_ident = $result['technofarm_kad_ident'];
            // test for separator
            $explode_ident = explode('.', $example_ident);
            if (count($explode_ident) > 2) {
                $separator = '.';
            } else {
                $explode_ident = explode('-', $example_ident);
                if (count($explode_ident) > 2) {
                    $separator = '-';
                } else {
                    $separator = false;
                }
            }

            if ($separator) {
                if (!$hasEkatte) {
                    $fieldsInsert[] = 'ekate';
                    $fieldsFrom[] = "(string_to_array(technofarm_kad_ident, '{$separator}'))[1]";
                }
                if (!$hasMasiv) {
                    $fieldsInsert[] = 'masiv';
                    $fieldsFrom[] = "(string_to_array(technofarm_kad_ident, '{$separator}'))[2]";
                }
                if (!$hasNumber) {
                    $fieldsInsert[] = 'number';
                    $fieldsFrom[] = "(string_to_array(technofarm_kad_ident, '{$separator}'))[3]";
                }
            }
        } elseif ($hasEkatte && $hasMasiv && $hasNumber) {
            // create technofarm_kad_ident column so it can be updated
            $sql = "ALTER TABLE {$tmp_table} ADD COLUMN technofarm_kad_ident character varying(100)";
            $cmd = $this->DbModule->createCommand($sql);
            $cmd->execute();

            // if user did not choose a column for kad_ident and he chose ekatte, masiv and imot we form the field for him
            $sql = "UPDATE {$tmp_table} SET technofarm_kad_ident = technofarm_ekatte || '.' || technofarm_masiv || '.' || technofarm_number";
            $cmd = $this->DbModule->createCommand($sql);
            $cmd->execute();

            $fieldsInsert[] = 'kad_ident';
            $fieldsFrom[] = 'technofarm_kad_ident';
        }

        $flag = $this->getColumnNameExist($database, $tmp_table, $definition['category']);
        if ($definition['category'] && $flag) {
            $sql = "ALTER TABLE {$tmp_table} RENAME COLUMN {$definition['category']} TO technofarm_category";
            $cmd = $this->DbModule->createCommand($sql);
            $cmd->execute();
            $fieldsInsert[] = 'category';
            $fieldsFrom[] = 'technofarm_category';
        }

        $flag = $this->getColumnNameExist($database, $tmp_table, $definition['waytouse']);
        if ($definition['waytouse'] && $flag) {
            $sql = "ALTER TABLE {$tmp_table} RENAME COLUMN {$definition['waytouse']} TO technofarm_waytouse";
            $cmd = $this->DbModule->createCommand($sql);
            $cmd->execute();
            $fieldsInsert[] = 'area_type';
            $fieldsFrom[] = 'technofarm_waytouse';
        }

        $flag = $this->getColumnNameExist($database, $tmp_table, $definition['mestnost']);
        if ($definition['mestnost'] && $flag) {
            $sql = "ALTER TABLE {$tmp_table} RENAME COLUMN {$definition['mestnost']} TO technofarm_mestnost";
            $cmd = $this->DbModule->createCommand($sql);
            $cmd->execute();
            $fieldsInsert[] = 'mestnost';
            $fieldsFrom[] = 'technofarm_mestnost';
        }

        $flag = $this->getColumnNameExist($database, $tmp_table, $definition['document_area']);
        if ($definition['document_area'] && $flag) {
            $sql = "ALTER TABLE {$tmp_table} RENAME COLUMN {$definition['document_area']} TO technofarm_document_area";
            $cmd = $this->DbModule->createCommand($sql);
            $cmd->execute();
            $fieldsInsert[] = 'document_area';
            $fieldsFrom[] = 'technofarm_document_area::numeric';

            $sql = "UPDATE {$tmp_table} SET technofarm_document_area = (St_Area(geom)/1000)
                    WHERE technofarm_document_area is null";
            $cmd = $this->DbModule->createCommand($sql);
            $cmd->execute();
        }

        $fieldsInsert[] = 'allowable_area';
        $fieldsFrom[] = 'allowable_area';
        $fieldsInsert[] = 'allowable_type';
        $fieldsFrom[] = 'allowable_type';

        $sql = 'INSERT INTO layer_kvs(' . implode(',', $fieldsInsert) . ') SELECT ' . implode(
            ',',
            $fieldsFrom
        ) . " FROM {$tmp_table}";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();

        if ($addKvsBorders) {
            $ekatteColumn = $hasEkatte
            ? 'technofarm_ekatte'
            : "(string_to_array(technofarm_kad_ident, '{$separator}'))[1]";

            $newEkattes = $this->getItemsByParams([
                'tablename' => $tmp_table,
                'return' => ["DISTINCT {$ekatteColumn} as ekatte"],
            ], false, false);
            $newEkattes = array_column($newEkattes, 'ekatte');

            // Delete old borders for the specified ekattes and insert new ones from layer_kvs table
            $this->updateKvsBorders($newEkattes);
        }

        // update KVS used area and kad_ident to be with . separator
        $sql = "UPDATE {$this->tableKVS} SET used_area = (St_Area(geom)/1000),kad_ident = replace(kad_ident, '-', '.')";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function getColumnNameExist($database, $tablename, $columnName)
    {
        $sql = 'SELECT COUNT(*) AS num FROM information_schema.columns ';
        $sql .= "where table_catalog = :dbname and
                table_schema = 'public' and
                table_name = :tname and
                LOWER(column_name)=:cname";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':dbname', $database);
        $cmd->bindParameter(':tname', $tablename);
        $cmd->bindParameter(':cname', $columnName);

        return $cmd->queryScalar();
    }

    public function getTableNameExist($database, $tablename)
    {
        $sql = 'SELECT COUNT(*) AS num FROM information_schema.columns ';
        $sql .= "where table_catalog = :dbname and
                table_schema = 'public' and
                table_name = :tname";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':dbname', $database);
        $cmd->bindParameter(':tname', $tablename);

        return $cmd->queryScalar();
    }

    public function getTableColumnsList(string $tablename, $schema = 'public')
    {
        $sql = 'SELECT
                column_name
            FROM
                information_schema.columns
            WHERE
                table_schema = :schema
                AND table_name = :tablename
            ORDER BY
                ordinal_position;
            ';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':tablename', $tablename);
        $cmd->bindParameter(':schema', $schema);

        return array_column($cmd->query()->readAll(), 'column_name');
    }

    /**
     * @param string $database
     *
     * @return array of tables for selected database (the user database)
     */
    public function getAllUserTables($database)
    {
        $sql = "SELECT table_name FROM information_schema.tables WHERE table_catalog = '{$database}' AND table_schema = 'public'";
        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    /**
     * @param string $database
     *
     * @return array of tables for selected database (the user database)
     */
    public function getAllUsedZPLayerTables($database)
    {
        $sql = "SELECT table_name FROM information_schema.tables WHERE table_name like 'layer_zp%' AND table_catalog = '{$database}' AND table_schema = 'public'";
        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function getLayerCRS($tablename)
    {
        $sql = "SELECT ST_SRID(geom) FROM {$tablename} LIMIT 1;";
        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->queryScalar();
    }

    public function getIntersectByGeom($tablename1, $tablename2)
    {
        $sql = "SELECT COUNT(*) as num FROM {$tablename1} a INNER JOIN {$tablename2} b ON ST_Intersects(a.geom, b.geom)";
        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->queryScalar();
    }

    public function getIsakmNumberIntersect($tablename1, $tablename2)
    {
        $sql = "SELECT COUNT(*) as num FROM {$tablename1} a INNER JOIN {$tablename2} b ON (LOWER(a.prc_uin) = LOWER(b.prc_uin))";
        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->queryScalar();
    }

    public function getValidGeom($tablename)
    {
        $sql = "SELECT count(*) as valid FROM {$tablename} WHERE ST_IsValid(geom) = FALSE";
        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->queryScalar();
    }

    public function getNotValidGeom($tablename, $geomfield)
    {
        $sql = "SELECT * FROM {$tablename} WHERE ST_IsValid({$geomfield}) = FALSE";
        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    /**
     * checkMissingColumns check Missing Columns in Tmp Table and throws Exception if there is missing column.
     *
     * @param string $tmp_table
     * @param array $check_columns
     *
     * @throws Exception
     */
    public function checkMissingColumns($tmp_table, $check_columns)
    {
        $sql = "select column_name from information_schema.columns where
                table_name='{$tmp_table}';";
        $cmd = $this->DbModule->createCommand($sql);
        $column_names = $cmd->query()->readAll();

        $columns = [];
        foreach ($column_names as $key => $value) {
            $columns[] = $column_names[$key]['column_name'];
        }

        $missing_columns = '';
        foreach ($check_columns as $key => $value) {
            if (!in_array('' . $value . '', $columns)) {
                $missing_columns .= $value . ', ';
            }
        }

        if (strlen($missing_columns)) {
            $missing_columns = substr($missing_columns, 0, -2);

            throw new Exception($missing_columns, ERROR_MISSING_COLUMN);
        }
    }

    /**
     * This method creates the columns that exist in the layer's definitions but are missing in the layer's table.
     *
     * @return array The names of the created columns
     */
    public function createLayerMissingColumns(UserLayers $layer)
    {
        $missingColumns = $this->getLayerMissingColumns($layer);
        $missingColumnsDefinitions = array_filter($layer->getDefinitions(), function ($layerDef) use ($missingColumns) {
            return in_array($layerDef['col_name'], $missingColumns);
        });

        foreach ($missingColumnsDefinitions as $def) {
            $colType = $GLOBALS['Layers']['columnTypesByCategory'][$def['col_category']] ?? 'VARCHAR';
            $colName = $def['col_name'];

            if ($def['col_virtual']) {
                $colType = $colType . ' GENERATED ALWAYS AS (' . $def['col_expression'] . ') STORED';
            }

            $this->addColumn($layer->table_name, $colName, $colType);
        }

        return $missingColumns;
    }

    /**
     * This method compares the columns from layer's table with the columns from layer's definitions
     * and returns an array of columns that exist in the definitions but not in the table.
     */
    public function getLayerMissingColumns(UserLayers $layer)
    {
        $tableColumns = $this->getTableColumnsList($layer->table_name);
        $layerColumns = UserLayers::getColumns($layer->getDefinitions());

        return array_diff($layerColumns, $tableColumns);
    }

    public function deleteTable($tablename)
    {
        $sql = "DROP TABLE IF EXISTS {$tablename};";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function insertIntoTableFromTable($tablename1, $tablename2)
    {
        // check Missing Columns(ОТ ИСАК) and throws Exception if there is missing column
        $this->checkMissingColumns(
            $tablename2,
            ['prc_uin', 'area', 'ekatte', 'cropcode', 'watering', 'schemata', 'cropname']
        );

        $sql = "INSERT INTO {$tablename1} SELECT * FROM {$tablename2}";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function insertIntoTableFromTableGeomOnly(
        $tablename1,
        $tablename2,
        $addToExisting = true,
        $geomField = 'geom',
        $idField = 'gid',
        $idArray = []
    ) {
        if (!$addToExisting) {
            $sql = "TRUNCATE {$tablename1} CASCADE";
            $cmd = $this->DbModule->createCommand($sql);
            $cmd->execute();
        }

        $where = '';
        if (count($idArray) > 0) {
            $id_string = implode(',', $idArray);
            $where = " WHERE {$idField} IN ({$id_string})";
        }

        if ($this->checkColumnNameExist($tablename2, 'name')) {
            $sql = "INSERT INTO {$tablename1} (geom,plot_name) SELECT {$geomField}, name FROM {$tablename2}{$where}";
        } else {
            $sql = "INSERT INTO {$tablename1} (geom) SELECT ({$geomField}) FROM {$tablename2}{$where}";
        }

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    /**
     * @deprecated Use method copyLayerItems instead
     *
     * @param null|mixed $ekate
     */
    public function insertIntoLayerForIsak(
        $tablenameFrom,
        $fromTableType,
        $tablenameTo,
        $idField,
        $idArray = [],
        $idCropsArray,
        $ekate = null
    ) {
        $where_id_in = '';
        if (count($idArray) > 0) {
            $id_string = implode(',', $idArray);
            $where_id_in .= " AND {$idField} IN ({$id_string})";
        }

        if ($ekate) {
            $where_id_in .= " AND ekate='" . $ekate . "' AND is_edited=false ";
        }

        $crodCodes = array_map(function ($cropData) {
            return "'" . $cropData['crop_code'] . "'";
        }, $idCropsArray);

        $id_crops_string = implode(',', $crodCodes);

        $sql = '';
        if (Config::LAYER_TYPE_ISAK == $fromTableType || Config::LAYER_TYPE_FOR_ISAK == $fromTableType) {
            $innerSql = 'SELECT prc_uin,';

            if (Config::LAYER_TYPE_ISAK == $fromTableType) {
                $innerSql .= ' prc_uin as prc_name, schemata, ';
            } else {
                $innerSql .= ' prc_name, schema, ';
            }

            if ('' != $id_crops_string) {
                $innerSql .= "(CASE WHEN cropcode IN ({$id_crops_string}) THEN cropcode END) as cropcode, (CASE WHEN cropcode IN ({$id_crops_string}) THEN cropname END) as cropname";
            } else {
                $innerSql .= 'null as cropcode, null as cropname';
            }

            $innerSql .= ", round((ST_Area(geom)/1000)::numeric, 3) AS area, ekatte, geom
                         FROM {$tablenameFrom}
                         WHERE true {$where_id_in}";

            $sql = "INSERT INTO {$tablenameTo} (prc_uin,prc_name,schema,cropcode,cropname,area,ekatte,geom) {$innerSql}";
        }
        if (Config::LAYER_TYPE_KVS == $fromTableType) {
            $innerSql = "SELECT kad_ident as prc_name,round((ST_Area(geom)/1000)::numeric, 3) AS area,ekate,geom FROM {$tablenameFrom} WHERE true {$where_id_in}";

            $sql = "INSERT INTO {$tablenameTo} (prc_name,area,ekatte,geom) {$innerSql}";
        }
        if (Config::LAYER_TYPE_ZP == $fromTableType) {
            $innerSql = 'SELECT area_name as prc_name,round((ST_Area(geom)/1000)::numeric, 3) AS area,isak_prc_uin,ekatte,geom, ';

            if ('' != $id_crops_string) {
                $innerSql .= "(CASE WHEN culture IN ({$id_crops_string}) THEN culture END) as culture";
            } else {
                $innerSql .= 'null as culture';
            }

            $innerSql .= " FROM {$tablenameFrom} WHERE true {$where_id_in}";

            $sql = "INSERT INTO {$tablenameTo} (prc_name,area,prc_uin,ekatte,geom,cropcode) {$innerSql}";
        }
        if (Config::LAYER_TYPE_KMS == $fromTableType) {
            $innerSql = "SELECT concat_ws('-', ekatte, name),round((ST_Area(geom)/1000)::numeric, 3) AS area,ekatte, geom, ";

            if ('' != $id_crops_string) {
                $innerSql .= "(CASE WHEN crop_code IN ({$id_crops_string}) THEN virtual_crop_name END) as crop_name,
                                (CASE WHEN crop_code IN ({$id_crops_string}) THEN crop_code END) as crop_code";
            } else {
                $innerSql .= 'null as crop_name, null as crop_code';
            }

            $innerSql .= " FROM {$tablenameFrom} WHERE true {$where_id_in}";

            $sql = "INSERT INTO {$tablenameTo} (prc_name,area,ekatte,geom,cropname, cropcode) {$innerSql}";
        }
        if (Config::LAYER_TYPE_GPS == $fromTableType) {
            $innerSql = "SELECT plot_name AS prc_name, round((ST_Area(geom)/1000)::numeric, 3) AS area, geom  FROM {$tablenameFrom} WHERE true {$where_id_in}";

            $sql = "INSERT INTO {$tablenameTo} (prc_name,area,geom) {$innerSql}";
        }
        if (Config::LAYER_TYPE_WORK_LAYER == $fromTableType) {
            $innerSql = "SELECT round((ST_Area(geom)/1000)::numeric, 3) AS area, geom  FROM {$tablenameFrom} WHERE true {$where_id_in}";

            $sql = "INSERT INTO {$tablenameTo} (area,geom) {$innerSql}";
        }
        if (Config::LAYER_TYPE_PERMANETELY_GREEN_AREAS == $fromTableType) {
            $innerSql = "SELECT imotcode||' - ПЗП',imekatte,round((ST_Area(geom)/1000)::numeric, 3) AS area,geom FROM
            dblink (
                'host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "',
                'select imotcode,imekatte,round((ST_Area(geom)/1000)::numeric, 3) AS area,geom from {$tablenameFrom} WHERE true {$where_id_in}'
            ) AS A (imotcode varchar, imekatte varchar,area numeric, geom geometry)";

            $sql = "INSERT INTO {$tablenameTo} (prc_name,ekatte,area,geom) {$innerSql}";
        }
        if ('layer_allowable' == $tablenameFrom || 'layer_allowable_final' == $tablenameFrom) {
            $innerSql = "SELECT elg_ident,ekatte_,round((ST_Area(geom)/1000)::numeric, 3) AS area,geom FROM
            dblink (
                'host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "',
                'select elg_ident,ekatte_,round((ST_Area(geom)/1000)::numeric, 3) AS area,geom from {$tablenameFrom} WHERE true {$where_id_in}'
            ) AS A (elg_ident varchar, ekatte_ varchar,area numeric, geom geometry)";

            $sql = "INSERT INTO {$tablenameTo} (prc_name,ekatte,area,geom) {$innerSql}";
        }
        if (Config::LAYER_TYPE_VPS_ORLI_LESHOYADI == $fromTableType) {
            $innerSql = "SELECT name,ekatte,round((ST_Area(geom)/1000)::numeric, 3) AS area,geom FROM
            dblink (
                'host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "',
                        'select concat_ws(''.'', ekatte, blockuin) as name,ekatte,round((ST_Area(geom)/1000)::numeric, 3) AS area,geom from {$tablenameFrom} WHERE true {$where_id_in}'
                        ) AS A (name varchar, ekatte varchar,area numeric, geom geometry)";

            $sql = "INSERT INTO {$tablenameTo} (prc_name,ekatte,area,geom) {$innerSql}";
        }

        if (strlen($sql)) {
            $cmd = $this->DbModule->createCommand($sql);
            $cmd->execute();
        }
    }

    public function insertIntoLayerZP($tablenameFrom, $fromTableType, $tablenameTo, $idField, $idArray = [], $ekate = null, $definitions = null)
    {
        $where_id_in = '';
        if (count($idArray) > 0) {
            $id_string = implode(',', $idArray);
            $where_id_in .= " AND {$idField} IN ({$id_string})";
        }
        $common = [];
        if (!empty($definitions)) {
            list($fromEkatteField, $common) = $this->getCommonColumns($definitions, ['id']);
            $common = array_keys($common);
        }
        if ($ekate) {
            $where_id_in .= " AND {$fromEkatteField}='" . $ekate . "' ";
            if (false !== in_array('is_edited', $definitions['from']['columns'])) {
                $where_id_in .= 'AND is_edited=false ';
            }
        }

        $sql = '';
        if (Config::LAYER_TYPE_FOR_ISAK == $fromTableType) {
            $innerSql = "SELECT prc_name as area_name, prc_uin as isak_prc_uin, ekatte, cropcode as culture, geom
            FROM {$tablenameFrom}
            WHERE true {$where_id_in}";

            $sql = "INSERT INTO {$tablenameTo} (area_name, isak_prc_uin, ekatte, culture, geom) ({$innerSql})";
        } elseif (Config::LAYER_TYPE_ISAK == $fromTableType) {
            $innerSql = "SELECT prc_uin as area_name, prc_uin as isak_prc_uin, ekatte, cropcode as culture, geom
            FROM {$tablenameFrom}
            WHERE true {$where_id_in}";

            $sql = "INSERT INTO {$tablenameTo} (area_name, isak_prc_uin, ekatte, culture, geom) ({$innerSql})";
        } elseif (Config::LAYER_TYPE_KMS == $fromTableType) {
            $innerSql = "SELECT CONCAT (ekatte, '.', name) as area_name, ekatte, crop_code as culture, geom
            FROM {$tablenameFrom}
            WHERE true {$where_id_in}";

            $sql = "INSERT INTO {$tablenameTo} (area_name, ekatte, culture, geom) ({$innerSql})";
        } elseif (Config::LAYER_TYPE_PERMANETELY_GREEN_AREAS == $fromTableType) {
            $innerSql = "SELECT imotcode, prc_uin, imekatte, geom FROM
            dblink (
                'host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "',
                'select imotcode, imotcode as prc_uin, imekatte,geom from {$tablenameFrom} WHERE true {$where_id_in}'
            ) AS A (imotcode varchar, prc_uin varchar, imekatte varchar, geom geometry)";

            $sql = "INSERT INTO {$tablenameTo} (area_name,isak_prc_uin,ekatte,geom) {$innerSql}";
        } elseif (Config::LAYER_TYPE_KVS == $fromTableType) {
            $innerSql = "SELECT ekate || '.' || masiv || '.' || number, '',ekate,geom FROM {$tablenameFrom} WHERE true {$where_id_in}";

            $sql = "INSERT INTO {$tablenameTo} (area_name,isak_prc_uin,ekatte,geom) {$innerSql}";
        } elseif (Config::LAYER_TYPE_VPS_ORLI_LESHOYADI == $fromTableType) {
            $innerSql = "SELECT name,ekatte,geom FROM
            dblink (
                'host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "',
                        'select concat_ws(''.'', ekatte, blockuin) as name,ekatte,geom from {$tablenameFrom} WHERE true {$where_id_in}'
                        ) AS A (name varchar, ekatte varchar, geom geometry)";

            $sql = "INSERT INTO {$tablenameTo} (area_name,ekatte,geom) {$innerSql}";
        } elseif (Config::LAYER_TYPE_GPS == $fromTableType) {
            $innerSql = "SELECT plot_name as area_name, geom FROM {$tablenameFrom} WHERE true {$where_id_in}";

            $sql = "INSERT INTO {$tablenameTo} (area_name, geom) {$innerSql}";
        } elseif (Config::LAYER_TYPE_WORK_LAYER == $fromTableType) {
            $implodedColumns = empty($common) ? '' : ', ' . implode(', ', $common);
            $innerSql = "SELECT  geom, name {$implodedColumns} FROM {$tablenameFrom} WHERE true {$where_id_in}";
            $sql = "INSERT INTO {$tablenameTo} (geom, area_name {$implodedColumns}) {$innerSql}";
        } elseif (Config::LAYER_TYPE_ALLOWABLE_FINAL == $fromTableType || Config::LAYER_TYPE_PHYSICAL_BLOCKS == $fromTableType) {
            $innerSql = "SELECT * 
                FROM dblink (
                    'host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "',
                    '
                        SELECT {$fromEkatteField} as ekatte, geom, fbident as area_name
                        FROM {$tablenameFrom}
                        WHERE true {$where_id_in}
                    '
                ) as A (ekatte varchar, geom geometry, area_name varchar)
            ";

            $sql = "INSERT INTO {$tablenameTo} (ekatte, geom, area_name) ({$innerSql})";
        }

        if (strlen($sql)) {
            $cmd = $this->DbModule->createCommand($innerSql);
            $results = $cmd->query()->readAll();

            $cmd = $this->DbModule->createCommand($sql);
            $cmd->execute();
        }
    }

    /** INSERT INTO LAYER_TYPE_WORK_LAYER.
     * @param array $idArray
     * @param null $ekate
     * @param null $definitions
     *
     * @throws TDbException
     *
     * @deprecated Use method copyLayerItems instead
     */
    public function insertIntoWorkLayer($tablenameFrom, $tablenameTo, $fromLayerType, $idArray = [], $ekate = null, $definitions = null)
    {
        $where_id_in = '';
        $fromEkatteField = 'ekatte';
        if (Config::LAYER_TYPE_ZP == $fromLayerType) {
            $idField = 'id';
            $name = 'area_name';
        } elseif (Config::LAYER_TYPE_WORK_LAYER == $fromLayerType) {
            $idField = 'gid';
            $name = 'name';
        } elseif (Config::LAYER_TYPE_GPS == $fromLayerType) {
            $idField = 'gid';
            $name = 'plot_name';
        } elseif (Config::LAYER_TYPE_FOR_ISAK == $fromLayerType) {
            $idField = 'gid';
            $name = 'prc_name';
        } elseif (Config::LAYER_TYPE_ISAK == $fromLayerType) {
            $idField = 'gid';
            $name = 'prc_uin';
        } elseif (Config::LAYER_TYPE_KMS == $fromLayerType) {
            $idField = 'gid';
            $name = 'name';
        } elseif (Config::LAYER_TYPE_KVS == $fromLayerType) {
            $idField = 'gid';
            $name = 'kad_ident';
        } elseif (Config::LAYER_TYPE_ALLOWABLE_FINAL == $fromLayerType || Config::LAYER_TYPE_PHYSICAL_BLOCKS == $fromLayerType) {
            $idField = 'gid';
            $name = 'fbident';
        } else {
            $idField = 'gid';
            $name = '';
        }

        if (count($idArray) > 0) {
            $id_string = implode(',', $idArray);
            $where_id_in = " AND {$idField} IN ({$id_string})";
        }

        $common = [];
        if (!empty($definitions)) {
            // if destination table has less columns than the source table, he add those columns
            $destinationCount = count($definitions['to']['columns']);
            $sourceCount = count($definitions['from']['columns']);
            if ($destinationCount < $sourceCount && $destinationCount <= 4) {
                $columnsToCreate = array_diff($definitions['from']['columns'], $definitions['to']['columns']);
                $sqlCols = '';
                $i = 0;
                foreach ($definitions['from']['columns'] as $column) {
                    if (in_array($column, $columnsToCreate)) {
                        $data_type = $definitions['from']['data_type'][$i];
                        $chr_max_len = $definitions['from']['chr_max_len'][$i];
                        $chr_max_len = 'NULL' != $chr_max_len ? '(' . $chr_max_len . ')' : '';
                        $in_nullable = $definitions['from']['is_nullable'][$i];
                        $in_nullable = 'NO' == $in_nullable ? 'NOT NULL' : '';
                        $sqlCols .= "ALTER TABLE {$tablenameTo} ADD COLUMN {$column} {$data_type} {$chr_max_len} NULL;";
                    }
                    $i++;
                }
                $cmd = $this->DbModule->createCommand($sqlCols);
                $cmd->execute();
                $definitions['to']['columns'] = array_merge($columnsToCreate, $definitions['to']['columns']);
                unset($i, $column, $sqlCols, $columnsToCreate, $data_type, $chr_max_len, $in_nullable);
            }

            list($fromEkatteField, $common) = $this->getCommonColumns($definitions, [$idField, $name]);
        }

        if ($ekate && $fromEkatteField) {
            $where_id_in .= " AND {$fromEkatteField}='" . $ekate . "' ";
            if (false !== in_array('is_edited', $definitions['from']['columns'])) {
                $where_id_in .= 'AND is_edited=false ';
            }
        }

        $columnsStr = '';
        $columnsWithTypesStr = '';
        $columnConter = 0;
        foreach ($common as $column => $type) {
            $columnsStr .= $column . ' ';
            $columnsWithTypesStr .= $column . ' ' . $type;
            if ($columnConter < count($common) - 1) {
                $columnsStr .= ', ';
                $columnsWithTypesStr .= ', ';
            }
            $columnConter++;
        }

        if (in_array($tablenameFrom, $GLOBALS['Layers']['systemTables'])) {
            $tablenameFrom = "dblink (
                    'host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "',
                    '
                        SELECT gid, geom, {$name}, {$columnsStr}
                        FROM {$tablenameFrom} 
                        WHERE true {$where_id_in}
                    '
                ) as A (gid int, geom geometry, {$name} varchar, {$columnsWithTypesStr})
            ";
        }

        $sql = "INSERT INTO {$tablenameTo} (geom, name, {$columnsStr}) SELECT geom, {$name} as name, {$columnsStr}
          FROM {$tablenameFrom} WHERE true {$where_id_in}";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function insertIntoLayerGPSFromTableGeomOnly($fromTablename, $fromTableType, $geomField = 'geom', $idField = 'gid', $idArray = [], $ekate = null, $definitions = [])
    {
        $where_id_in = '';
        if (count($idArray) > 0) {
            $id_string = implode(',', $idArray);
            $where_id_in .= " AND {$fromTablename}.{$idField} IN ({$id_string})";
        }

        $common = [];
        if (!empty($definitions)) {
            list($fromEkatteField, $common) = $this->getCommonColumns($definitions);
        }
        if ($ekate) {
            $where_id_in .= " AND {$fromEkatteField}='" . $ekate . "' ";
            if (false !== in_array('is_edited', $definitions['from']['columns'])) {
                $where_id_in .= 'AND is_edited=false ';
            }
        }

        if (Config::LAYER_TYPE_PERMANETELY_GREEN_AREAS == $fromTableType) {
            $innerSql = "SELECT geom FROM
            dblink (
                'host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "',
                'select geom from layer_pzp WHERE true {$where_id_in}'
            ) AS A (geom geometry)";

            $sql = "INSERT INTO layer_gps (geom) {$innerSql}";
        } elseif (Config::LAYER_TYPE_VPS_ORLI_LESHOYADI == $fromTableType) {
            $innerSql = "SELECT geom FROM
        dblink (
            'host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "',
                        'select geom from layer_vps_orli_leshoyadi WHERE true {$where_id_in}'
                        ) AS A (geom geometry)";

            $sql = "INSERT INTO layer_gps (geom) {$innerSql}";
        } elseif (Config::LAYER_TYPE_ALLOWABLE_FINAL == $fromTableType || Config::LAYER_TYPE_PHYSICAL_BLOCKS == $fromTableType) {
            $innerSql = "SELECT * 
                FROM dblink (
                    'host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "',
                    '
                        SELECT geom, fbident as plot_name
                        FROM {$fromTablename}
                        WHERE true {$where_id_in}
                    '
                ) as A (geom geometry, plot_name varchar)
            ";

            $sql = "INSERT INTO layer_gps (geom, plot_name) ({$innerSql})";
        } else {
            $nameField = '';
            $inputField = '';
            switch ($fromTableType) {
                case Config::LAYER_TYPE_KVS:
                    $nameField = ', kad_ident';
                    $inputField = ', plot_name';

                    break;
                case Config::LAYER_TYPE_FOR_ISAK:
                    $nameField = ', prc_name';
                    $inputField = ', plot_name';

                    break;
                case Config::LAYER_TYPE_ISAK:
                    $nameField = ', prc_uin';
                    $inputField = ', plot_name';

                    break;
                case Config::LAYER_TYPE_ZP:
                    $nameField = ', area_name || \' (\' || isak_prc_uin ||  \')\'';
                    $inputField = ', plot_name';

                    break;
                case Config::LAYER_TYPE_KMS:
                    $nameField = ', name';
                    $inputField = ', plot_name';

                    break;
                case Config::LAYER_TYPE_WORK_LAYER:
                    $nameField = ', name';
                    $inputField = ', plot_name';

                    break;
                default:
                    $nameField = '';
                    $inputField = '';

                    break;
            }
            $innerSql = "SELECT DISTINCT({$fromTablename}.{$idField}) FROM {$fromTablename} "
                . "JOIN layer_gps tmp ON ((ST_Area(ST_Intersection({$fromTablename}.{$geomField}, tmp.geom))) > 50) WHERE true {$where_id_in}";
            $sql = "INSERT INTO layer_gps (geom {$inputField}) SELECT {$geomField} {$nameField} as plot_name FROM {$fromTablename} WHERE {$idField} NOT IN ({$innerSql}){$where_id_in}";
        }

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function insertIntoTableFromTableIsakNum($tablename1, $tablename2, $idCropsArray)
    {
        if (!$idCropsArray) {
            $idCropsArray = [];
        }

        $crodCodes = array_map(function ($cropData) {
            return "'" . $cropData['crop_code'] . "'";
        }, $idCropsArray);

        $id_crops_string = implode(',', $crodCodes);

        $sql = "INSERT INTO {$tablename1} (geom,isak_prc_uin,culture) SELECT geom, prc_uin, (CASE WHEN cropcode IN ({$id_crops_string}) THEN cropcode END) as cropcode FROM {$tablename2}";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function getMaxExtent($tablename)
    {
        $sql = "SELECT ST_extent(geom) as extent FROM {$tablename}";
        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->queryScalar();
    }

    public function getZPMaxExtent($tablename, $id)
    {
        $sql = "SELECT ST_extent(geom) as extent FROM {$tablename} WHERE id=:id";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':id', $id, PDO::PARAM_INT);

        return $cmd->queryScalar();
    }

    public function getKvsMaxExtent($tablename, $id, $bymasiv)
    {
        if ($bymasiv) {
            $where = 'masiv';
        } else {
            $where = 'gid';
        }

        $sql = "SELECT ST_extent(geom) as extent FROM {$tablename} WHERE {$where}=:id";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':id', $id);

        return $cmd->queryScalar();
    }

    public function updateLayerCRS($fromCRS, $toCRS, $tablename)
    {
        try {
            $result = [];

            $sql = "UPDATE {$tablename} SET geom=ST_Multi(ST_Buffer(ST_Transform(ST_SetSRID(geom, :fromcrs), :tocrs),0))";
            $cmd = $this->DbModule->createCommand($sql);
            $cmd->bindParameter(':fromcrs', $fromCRS, PDO::PARAM_INT);
            $cmd->bindParameter(':tocrs', $toCRS, PDO::PARAM_INT);
            $cmd->execute();

            $result['value'] = true;
            $result['error'] = '';

            return $result;
        } catch (Exception $e) {
            echo $e;

            $result['value'] = false;
            $result['error'] = $e->getMessage() . '\n=====' . $e->getTraceAsString();

            return $result;
        }
    }

    public function createColumnCRS($CRS, $tablename)
    {
        $columnName = 'geom_new';
        $sql = 'SELECT COUNT(*) AS num FROM information_schema.columns ';
        $sql .= "where
                table_schema = 'public' and
                table_name = :tname and
                LOWER(column_name)=:cname";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':tname', $tablename);
        $cmd->bindParameter(':cname', $columnName);

        if (!$cmd->queryScalar()) {
            $sql = "SELECT AddGeometryColumn('{$tablename}','geom_new',{$CRS}, 'MULTIPOLYGON', 2);";
            $cmd = $this->DbModule->createCommand($sql);
            $cmd->execute();
        }

        $sql = "UPDATE {$tablename} SET geom_new = ST_Transform(geom,{$CRS});";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();

        return true;
    }

    public function createTableKvsEkatteInvalid($tmpTable)
    {
        $sql = 'CREATE TABLE ' . $tmpTable . '_invalid (
        id SERIAL NOT NULL,
        fr_gid INT4 NOT NULL,
        geom GEOMETRY
        );';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();

        $sql = 'ALTER TABLE ONLY ' . $tmpTable . '_invalid ADD CONSTRAINT "' . $tmpTable . '_invalid_pk" PRIMARY KEY (id)';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function dropTableKvsEkatteInvalid($tmpTable)
    {
        $sql = 'DROP TABLE ' . $tmpTable . '_invalid';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function dropTableKvsEkatte($tmpTable, $cascade = false)
    {
        $sql = 'DROP TABLE IF EXISTS ' . $tmpTable;

        if (true === $cascade) {
            $sql .= ' CASCADE';
        }
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function dropMaterializedView($view)
    {
        $sql = 'DROP MATERIALIZED VIEW IF EXISTS ' . $view;
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function updateSequenceKVS()
    {
        $sql = 'SELECT max(gid)+1 AS maxid FROM layer_kvs';
        $cmd = $this->DbModule->createCommand($sql);
        $result = $cmd->query()->readAll();

        $nextval = $result[0]['maxid'] ? $result[0]['maxid'] : 1;

        $sql = 'ALTER SEQUENCE layer_kvs_gid_seq RESTART WITH ' . $nextval . '';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function createTableKVS()
    {
        $sql = 'CREATE TABLE layer_kvs (
                gid INTEGER NOT NULL,
                kad_ident CHARACTER VARYING(50),
                geom GEOMETRY(MultiPolygon),
                ekate CHARACTER VARYING(255),
                masiv CHARACTER VARYING(255),
                number CHARACTER VARYING(255),
                category CHARACTER VARYING(255),
                area_type CHARACTER VARYING(255),
                has_contracts BOOLEAN DEFAULT FALSE NOT NULL,
                mestnost CHARACTER VARYING(255),
                waiting_update BOOLEAN DEFAULT FALSE NOT NULL
                );';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();

        $sql = 'CREATE INDEX layer_kvs_geom_gist ON layer_kvs USING GIST (geom)';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();

        $sql = 'CREATE SEQUENCE layer_kvs_gid_seq
                START WITH 1
                INCREMENT BY 1
                NO MINVALUE
                NO MAXVALUE
                CACHE 1;';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();

        $sql = "ALTER TABLE ONLY layer_kvs ALTER COLUMN gid SET DEFAULT nextval('layer_kvs_gid_seq'::REGCLASS)";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();

        $sql = 'ALTER TABLE ONLY layer_kvs ADD CONSTRAINT "layer_kvs_pk" PRIMARY KEY (gid)';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function createTableForLayer(UserLayers $layer)
    {
        $layerDefinitions = $layer->getDefinitions();

        $columnTypesByCategory = $GLOBALS['Layers']['columnTypesByCategory'];
        $geomDefinition = $layer->getDefinitionByCategory(Config::LAYER_COLUMN_CATEGORY_GEOM);
        $geomColumn = $geomDefinition['col_name'];

        $columnsSql = '';
        foreach ($layerDefinitions as $definition) {
            $colName = '"' . $definition['col_name'] . '"';
            $colCategory = $definition['col_category'];
            $colType = $columnTypesByCategory[$colCategory];

            if ($definition['col_virtual'] && $definition['col_expression']) {
                $columnsSql .= $colName . ' ' . $colType . ' GENERATED ALWAYS AS (' . $definition['col_expression'] . ') STORED,';
            } else {
                $columnsSql .= "{$colName} {$colType},";
            }
        }
        $columnsSql = substr($columnsSql, 0, -1);

        $createTableSql = "CREATE TABLE IF NOT EXISTS {$layer->table_name} ({$columnsSql})";
        $createTableCmd = $this->DbModule->createCommand($createTableSql);
        $createTableCmd->execute();

        $sql = "CREATE INDEX IF NOT EXISTS {$layer->table_name}_gist ON {$layer->table_name} USING gist ({$geomColumn});";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function createTableZP($tablename)
    {
        $sql = "CREATE TABLE {$tablename} (
        id integer NOT NULL,
        ekatte character varying(255),
        culture character varying(255),
        obrabotki character varying(255),
        dobivi character varying(255),
        napoqvane character varying(255),
        polivki character varying(255),
        polzvatel character varying(255),
        isak_prc_uin character varying(255),
        mestnost character varying(255),
        geom geometry,
        area_name character varying(255)
        );";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();

        $seq = $tablename . '_id_seq';
        $sql = "CREATE SEQUENCE {$seq}
        START WITH 1
        INCREMENT BY 1
        NO MINVALUE
        NO MAXVALUE
        CACHE 1;";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();

        $tnameid = $tablename . '.id';
        $sql = "ALTER SEQUENCE {$seq} OWNED BY {$tnameid}";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();

        $sql = "ALTER TABLE ONLY {$tablename} ALTER COLUMN id SET DEFAULT nextval('{$seq}'::regclass)";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();

        $sql = "ALTER TABLE ONLY {$tablename} ADD CONSTRAINT {$tablename}_pkey PRIMARY KEY (id)";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();

        $sql = "CREATE INDEX {$tablename}_geom_gist ON {$tablename} USING gist (geom);";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();

        $sql = "INSERT INTO geometry_columns VALUES ('', 'public', '{$tablename}', 'geom', 2, 32635, 'MULTIPOLYGON');";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    /**
     * @deprecated Use method createTableForLayer instead
     */
    public function createTableForISAK($tablename)
    {
        $sql = "CREATE TABLE {$tablename} (
        gid serial NOT NULL,
        prc_name varchar(100),
        prc_uin varchar(50),
        cropcode varchar(10),
        cropname varchar(100),
        schema varchar(254),
        area numeric,
        ekatte varchar(10),
        geom geometry,
        edited bool DEFAULT false NOT NULL,
        comment varchar(254),
        crop_type varchar(127),
        crop_genus varchar(127),
        azot_fixed_crop bool DEFAULT false NOT NULL,
        is_intermediate_crop bool DEFAULT false NOT NULL,
        is_intermediate_weat_crop bool DEFAULT false NOT NULL,
        is_tree_short_rotation bool DEFAULT false NOT NULL,
        no_pndn bool DEFAULT false NOT NULL,
        common_cultures bool DEFAULT false NOT NULL,
        pndp bool DEFAULT false NOT NULL,
        sepp bool DEFAULT false NOT NULL,
        zdp bool DEFAULT false NOT NULL,
        nr1 bool DEFAULT false NOT NULL,
        nr2 bool DEFAULT false NOT NULL,
        crop_short_type varchar(127),
        green_area_factor numeric,
        natura_sitecode varchar(10) DEFAULT NULL,
        weat_crops varchar(511),
        non_weat_crops varchar(511),
        vps_type numeric default null,
        vps_inside_area numeric
        );";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();

        $sql = "CREATE INDEX {$tablename}_geom_gist ON {$tablename} USING gist (geom)";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();

        $sql = 'ALTER TABLE ONLY ' . $tablename . ' ADD CONSTRAINT "' . $tablename . '_pk" PRIMARY KEY (gid)';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();

        // update SRID to 0
        $sql = "SELECT UpdateGeometrySRID('{$tablename}', 'geom', 0);";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function createTableEnpData()
    {
        $sql = 'CREATE TABLE layer_enp_data (
        id SERIAL NOT NULL,
        name VARCHAR(127),
        type VARCHAR(50),
        layer_name VARCHAR(50),
        farming INT4 NOT NULL,
        year INT4 NOT NULL,
        quantity NUMERIC,
        green_area_factor NUMERIC
        );';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();

        $sql = 'ALTER TABLE ONLY layer_enp_data ADD CONSTRAINT "layer_enp_data_pk" PRIMARY KEY (id)';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function updateForIsakColumns($tablename)
    {
        $sql = "SELECT DISTINCT(cropcode)
        FROM {$tablename}
        WHERE cropcode IS NOT NULL AND cropcode <> ''";

        $cmd = $this->DbModule->createCommand($sql);

        $results = $cmd->query()->readAll();

        $cropcodes = array_map(function ($el) {
            return $el['cropcode'];
        }, $results);

        // building one query for updating all rows
        $cropesData = $GLOBALS['Farming']['crops'];
        $sql = "UPDATE {$tablename} SET crop_type = CASE cropcode ";
        foreach ($cropcodes as $cropcode) {
            $sql .= "WHEN '{$cropcode}' THEN '{$cropesData[$cropcode]['crop_type']}' ";
        }

        $sql .= 'END, crop_genus = CASE cropcode ';
        foreach ($cropcodes as $cropcode) {
            $sql .= "WHEN '{$cropcode}' THEN '{$cropesData[$cropcode]['crop_genus']}' ";
        }

        $sql .= 'END, azot_fixed_crop = CASE cropcode ';
        foreach ($cropcodes as $cropcode) {
            if (is_bool($cropesData[$cropcode]['azot_fixed_crop']) && true === $cropesData[$cropcode]['azot_fixed_crop']) {
                $value = 'TRUE';
            } else {
                $value = 'FALSE';
            }
            $sql .= "WHEN '{$cropcode}' THEN {$value} ";
        }

        $sql .= 'END, is_intermediate_crop = CASE cropcode ';
        foreach ($cropcodes as $cropcode) {
            if (is_bool($cropesData[$cropcode]['is_intermediate_crop']) && true === $cropesData[$cropcode]['is_intermediate_crop']) {
                $value = 'TRUE';
            } else {
                $value = 'FALSE';
            }
            $sql .= "WHEN '{$cropcode}' THEN {$value} ";
        }

        $sql .= 'END, is_intermediate_weat_crop = CASE cropcode ';
        foreach ($cropcodes as $cropcode) {
            if (is_bool($cropesData[$cropcode]['is_intermediate_weat_crop']) && true === $cropesData[$cropcode]['is_intermediate_weat_crop']) {
                $value = 'TRUE';
            } else {
                $value = 'FALSE';
            }
            $sql .= "WHEN '{$cropcode}' THEN {$value} ";
        }

        $sql .= 'END, is_tree_short_rotation = CASE cropcode ';
        foreach ($cropcodes as $cropcode) {
            if (is_bool($cropesData[$cropcode]['is_tree_short_rotation']) && true === $cropesData[$cropcode]['is_tree_short_rotation']) {
                $value = 'TRUE';
            } else {
                $value = 'FALSE';
            }
            $sql .= "WHEN '{$cropcode}' THEN {$value} ";
        }

        $sql .= "END WHERE cropcode IN ('" . implode('\',\'', $cropcodes) . "')";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    /**
     * @deprecated Use method createTableForLayer instead
     */
    public function createTableDSS($tablename)
    {
        $sql = "CREATE TABLE {$tablename} (
        gid integer NOT NULL,
        geom geometry
        );";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();

        $sql = "CREATE INDEX {$tablename}_geom_gist ON {$tablename} USING gist (geom)";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();

        $sql = "CREATE SEQUENCE {$tablename}_gid_seq
        START WITH 1
        INCREMENT BY 1
        NO MINVALUE
        NO MAXVALUE
        CACHE 1;";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();

        $sql = "ALTER TABLE ONLY {$tablename} ALTER COLUMN gid SET DEFAULT nextval('{$tablename}_gid_seq'::regclass)";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();

        $sql = 'ALTER TABLE ONLY ' . $tablename . ' ADD CONSTRAINT "' . $tablename . '_kvs_pk" PRIMARY KEY (gid)';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();

        $sql = "INSERT INTO geometry_columns VALUES ('', 'public', '{$tablename}', 'geom', 2, 32635, 'MULTIPOLYGON');";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    /**
     * Check Column Name Exist.
     *
     * @param string $table
     * @param string $column
     *
     * @return int
     */
    public function checkColumnNameExist($table, $column)
    {
        $sql = "SELECT count(*)
                FROM information_schema.columns
                WHERE table_name = '{$table}' and column_name = :column_name";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':column_name', $column);

        return intval($cmd->queryScalar());
    }

    /**
     * Insert data to a given table from `tmp_geom` table.
     *
     * @param type string $tablename The name of the table to insert into
     * @param type array $tmpColumns The name of the columns which have to be selected from the `tmp_geom`
     */
    public function insertFromTmpInto($tablename, array $tmpColumns = [], array $targetColumns = [])
    {
        $defaultColumns = ['geom'];
        $columns = implode(',', array_merge($defaultColumns, $tmpColumns));

        if (empty($targetColumns)) {
            $targetColumns = $columns;
        } else {
            $targetColumns = implode(',', array_merge($defaultColumns, $targetColumns));
        }

        $simplifyGeomTolerance = $GLOBALS['Layers']['simplifyGeomTolerance'];
        $this->simplifyGeometry('tmp_geom', $simplifyGeomTolerance);
        $sql = "INSERT INTO {$tablename} ({$targetColumns}) SELECT {$columns} FROM tmp_geom";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function updateGeometry($tablename, $geometry, $gid)
    {
        $sql = "UPDATE {$tablename} SET geom=ST_Multi(ST_SetSRID(ST_GeomFromText(:geometry),32635)) WHERE gid = :gid";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':geometry', $geometry);
        $cmd->bindParameter(':gid', $gid, PDO::PARAM_INT);
        $cmd->execute();
    }

    public function updateAllowableArea($tablename, $plotIdsArray)
    {
        if (empty($plotIdsArray)) {
            return;
        }
        $plotIds = implode(',', $plotIdsArray);

        $sql = "UPDATE {$tablename} SET allowable_area= allowable_intersection.allowable_area, allowable_type=allowable_intersection.allowable_type
                FROM (
                SELECT
                    kvs.gid,
                    string_agg (DISTINCT(A .ntp) :: TEXT, ', ') AS allowable_type,
                    round(
                        (
                            st_area (
                                st_intersection (kvs.geom, st_union(A .geom))
                            ) / 1000
                        ) :: NUMERIC,
                        3
                    ) AS allowable_area
                FROM
                    {$tablename} kvs,
                    dblink (
                        'host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "'::text,
                        'SELECT
                            geom,
                            ntp
                        FROM
                            layer_allowable_final'
                    ) AS A (geom geometry, ntp VARCHAR)
                WHERE
                kvs.gid in ({$plotIds}) AND
                st_intersects (kvs.geom, A .geom)
                GROUP BY
                    kvs.gid
                ) allowable_intersection WHERE {$tablename}.gid = allowable_intersection.gid";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function updateGeometryWithInfoForGps($tablename, $geometry, $gid, $plot_name, $plot_info)
    {
        $sql = "UPDATE {$tablename} SET geom=ST_Multi(ST_SetSRID(ST_GeomFromText(:geometry),32635)), plot_name = :plot_name, plot_info = :plot_info WHERE gid = :gid";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':geometry', $geometry);
        $cmd->bindParameter(':plot_name', $plot_name);
        $cmd->bindParameter(':plot_info', $plot_info);
        $cmd->bindParameter(':gid', $gid, PDO::PARAM_INT);
        $cmd->execute();
    }

    public function updateGeometryWithInfoForGpsFromGeoJson($tableGPS, $geometry, $gid, $plotName, $plotInfo, $srcProj = 3857, $dstProj = 32635)
    {
        $sql = "UPDATE {$tableGPS} SET geom=ST_Multi(ST_Transform(ST_SetSRID(ST_GeomFromGeoJSON(:geometry), :src_proj), :dst_proj)), plot_name = :plot_name, plot_info = :plot_info WHERE gid = :gid";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':geometry', $geometry);
        $cmd->bindParameter(':plot_name', $plotName);
        $cmd->bindParameter(':plot_info', $plotInfo);
        $cmd->bindParameter(':src_proj', $srcProj, PDO::PARAM_INT);
        $cmd->bindParameter(':dst_proj', $dstProj, PDO::PARAM_INT);
        $cmd->bindParameter(':gid', $gid, PDO::PARAM_INT);
        $cmd->execute();
    }

    public function updateGeometryWithInfoForIsak($tablename, $geometry, $gid, $prc_name, $ekatte)
    {
        $sql = "UPDATE {$tablename} SET geom=ST_Multi(ST_SetSRID(ST_GeomFromText(:geometry),32635)), prc_name = :prc_name, ekatte = :ekatte WHERE gid = :gid";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':geometry', $geometry);
        $cmd->bindParameter(':prc_name', $prc_name);
        $cmd->bindParameter(':ekatte', $ekatte);
        $cmd->bindParameter(':gid', $gid, PDO::PARAM_INT);
        $cmd->execute();
    }

    public function updateDataForIsak($tablename, $gid)
    {
        $sql = "UPDATE {$tablename} SET edited=false WHERE gid = :gid";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':gid', $gid, PDO::PARAM_INT);
        $cmd->execute();
    }

    public function addGeometry($tablename, $geometry, array $fieldParams = [])
    {
        $properiesData = '';
        $bindParams = '';
        if (count($fieldParams)) {
            $properiesKeys = array_keys($fieldParams);
            $escapedKeys = array_map(function ($key) {
                return '"' . $key . '"';
            }, $properiesKeys);
            $properiesData = ',' . implode(',', $escapedKeys);
            $bindParams = ',:' . implode(',:', $properiesKeys);
        }

        $sql = "INSERT INTO {$tablename} (geom{$properiesData}) VALUES((SELECT
            CASE
                WHEN ST_GeometryType(ST_MakeValid(ST_Multi(ST_SetSRID(ST_GeomFromText(:geometry), 32635)))) = 'ST_GeometryCollection' THEN
                    ST_CollectionExtract(ST_MakeValid(ST_Multi(ST_SetSRID(ST_GeomFromText(:geometry), 32635))), 3)
                ELSE
                    ST_Multi(ST_SetSRID(ST_GeomFromText(:geometry), 32635))
            END){$bindParams})";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':geometry', $geometry);

        if (count($fieldParams)) {
            foreach ($fieldParams as $key => $value) {
                $dataType = PdoHelper::getPdoType($value);
                $cmd->bindValue(':' . $key, $value, $dataType);
            }
        }

        $cmd->execute();
    }

    /**
     * @param string $tablename
     * @param array $data
     *
     * @throws TDbException
     */
    public function addLayers($tablename, $data)
    {
        if (empty($data)) {
            return;
        }

        // Get the keys only for the first element because all elements are equal
        $firstElement = reset($data);
        $keys = array_keys($firstElement);
        foreach ($firstElement as $k => $v) {
            $values[] = 'geom' === $k ? "(SELECT
            CASE
                WHEN ST_GeometryType(ST_MakeValid(ST_Multi(ST_SetSRID(ST_GeomFromText(:{$k}), 32635)))) = 'ST_GeometryCollection' THEN
                    ST_CollectionExtract(ST_MakeValid(ST_Multi(ST_SetSRID(ST_GeomFromText(:{$k}), 32635))), 3)
                ELSE
                    ST_Multi(ST_SetSRID(ST_GeomFromText(:{$k}), 32635))
            END)" : ':' . $k;
        }

        $sql = 'INSERT INTO ' . $tablename . ' (' . sprintf('"%s"', implode('","', $keys)) . ') VALUES(' . implode(',', $values) . ')';
        $cmd = $this->DbModule->createCommand($sql);

        foreach ($data as $layer) {
            foreach ($layer as $key => $value) {
                $value = empty($value) ? null : trim($value);
                switch (gettype($value)) {
                    case 'boolean':
                        $cmd->bindParameter(':' . $key, $value, PDO::PARAM_BOOL);

                        break;
                    case 'integer':
                        $cmd->bindParameter(':' . $key, $value, PDO::PARAM_INT);

                        break;
                    case 'double':
                        $cmd->bindParameter(':' . $key, $value, PDO::PARAM_STR);

                        break;
                    case 'NULL':
                        $cmd->bindParameter(':' . $key, $null, PDO::PARAM_NULL);

                        break;
                    default:
                        $cmd->bindParameter(':' . $key, trim($value), PDO::PARAM_STR);

                        break;
                }
            }
            $cmd->execute();
        }
    }

    public function addGeometryForIsakAtrInfo(string $tablename, string $geometry, array $arrAtrInfo, int $br, ?int $prefix)
    {
        $prc_name = $arrAtrInfo['prc_name'];

        if (!is_null($prefix)) {
            $prc_name = $arrAtrInfo['prc_name'] . '-' . $br . '-' . $prefix;
        }

        $sql = "INSERT INTO {$tablename} (geom,
                                          prc_name,
                                          ekatte,
                                          cropcode,
                                          cropname,
                                          schema,
                                          comment,
                                          crop_type,
                                          crop_genus,
                                          azot_fixed_crop,
                                          is_intermediate_crop,
                                          is_intermediate_weat_crop,
                                          is_tree_short_rotation,
                                          no_pndn,
                                          common_cultures,
                                          pndp,
                                          sepp,
                                          zdp,
                                          nr1,
                                          nr2,
                                          crop_short_type,
                                          green_area_factor,
                                          area) VALUES(
                                          ST_Multi(ST_GeomFromText(:geometry)),
                                          :plot_name,
                                          :ekatte,
                                          :cropcode,
                                          :cropname,
                                          :schema,
                                          :comment,
                                          :crop_type,
                                          :crop_genus,
                                          :azot_fixed_crop,
                                          :is_intermediate_crop,
                                          :is_intermediate_weat_crop,
                                          :is_tree_short_rotation,
                                          :no_pndn,
                                          :common_cultures,
                                          :pndp,
                                          :sepp,
                                          :zdp,
                                          :nr1,
                                          :nr2,
                                          :crop_short_type,
                                          :green_area_factor,
                                          round((ST_Area(ST_Multi(ST_GeomFromText(:geometry)))/1000)::numeric, 3))";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':geometry', $geometry);
        $cmd->bindParameter(':plot_name', $prc_name);
        $cmd->bindParameter(':ekatte', $arrAtrInfo['ekatte']);
        $cmd->bindParameter(':cropcode', $arrAtrInfo['cropcode']);
        $cmd->bindParameter(':cropname', $arrAtrInfo['cropname']);
        $cmd->bindParameter(':schema', $arrAtrInfo['schema']);
        $cmd->bindParameter(':comment', $arrAtrInfo['comment']);
        $cmd->bindParameter(':crop_type', $arrAtrInfo['crop_type']);
        $cmd->bindParameter(':crop_genus', $arrAtrInfo['crop_genus']);
        $cmd->bindParameter(':azot_fixed_crop', $arrAtrInfo['azot_fixed_crop'], PDO::PARAM_BOOL);
        $cmd->bindParameter(':is_intermediate_crop', $arrAtrInfo['is_intermediate_crop'], PDO::PARAM_BOOL);
        $cmd->bindParameter(':is_intermediate_weat_crop', $arrAtrInfo['is_intermediate_weat_crop'], PDO::PARAM_BOOL);
        $cmd->bindParameter(':is_tree_short_rotation', $arrAtrInfo['is_tree_short_rotation'], PDO::PARAM_BOOL);
        $cmd->bindParameter(':no_pndn', $arrAtrInfo['no_pndn'], PDO::PARAM_BOOL);
        $cmd->bindParameter(':common_cultures', $arrAtrInfo['common_cultures'], PDO::PARAM_BOOL);
        $cmd->bindParameter(':pndp', $arrAtrInfo['pndp'], PDO::PARAM_BOOL);
        $cmd->bindParameter(':sepp', $arrAtrInfo['sepp'], PDO::PARAM_BOOL);
        $cmd->bindParameter(':zdp', $arrAtrInfo['zdp'], PDO::PARAM_BOOL);
        $cmd->bindParameter(':nr1', $arrAtrInfo['nr1'], PDO::PARAM_BOOL);
        $cmd->bindParameter(':nr2', $arrAtrInfo['nr2'], PDO::PARAM_BOOL);
        $cmd->bindParameter(':crop_short_type', $arrAtrInfo['crop_short_type']);
        $cmd->bindParameter(':green_area_factor', $arrAtrInfo['green_area_factor']);
        $cmd->execute();
    }

    public function addGeometryWithInfoForIsak($tablename, $geometry, $plot_name, $ekatte)
    {
        $sql = "INSERT INTO {$tablename} (geom, prc_name, ekatte, area) VALUES(ST_Multi(ST_SetSRID(ST_GeomFromText(:geometry), 32635)), :plot_name, :ekatte, round((ST_Area(ST_Multi(ST_GeomFromText(:geometry)))/1000)::numeric, 3))";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':geometry', $geometry);
        $cmd->bindParameter(':plot_name', $plot_name);
        $cmd->bindParameter(':ekatte', $ekatte);
        $cmd->execute();
    }

    public function addGeometryWithInfoForGps($tablename, $geometry, $plot_name, $plot_info)
    {
        $sql = "INSERT INTO {$tablename} (geom, plot_name, plot_info) VALUES(ST_Multi(ST_SetSRID(ST_GeomFromText(:geometry), 32635)), :plot_name, :plot_info)";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':geometry', $geometry);
        $cmd->bindParameter(':plot_name', $plot_name);
        $cmd->bindParameter(':plot_info', $plot_info);
        $cmd->execute();
    }

    public function removeAllGeometries($tablename)
    {
        $sql = "TRUNCATE {$tablename}";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function removeGeometry($tablename, $gid, $id_name)
    {
        $sql = "DELETE FROM {$tablename} WHERE {$id_name} = :gid";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':gid', $gid);
        $cmd->execute();
    }

    public function emptyTable($tableGPS)
    {
        $sql = "TRUNCATE {$tableGPS} CASCADE";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function createWhereSQL($sql, $options, $returnOnlySQL, $logicalOperation = 'AND')
    {
        $bindNames = array_keys($options);
        $bindOptions = array_values($options);
        for ($i = 0; $i < count($bindNames); $i++) {
            if (!is_null($bindOptions[$i]) && is_string($bindOptions[$i]) && '' != $bindOptions[$i]) {
                $sql .= ' AND ' . $bindOptions[$i];

                continue;
            }
            $compare = $bindOptions[$i]['compare'];
            $column = $bindOptions[$i]['column'];
            $value = $bindOptions[$i]['value'];
            $prefix = (isset($bindOptions[$i]['prefix'])) ? $bindOptions[$i]['prefix'] . '.' : '';

            if ('IN' == $compare || 'NOT IN' == $compare) {
                if (is_array($value) && count($value) > 0) {
                    $sql .= " {$logicalOperation} (" . $prefix . $column . ' ';

                    if (in_array(null, $value, true)) {
                        $value = array_values(array_filter($value, function ($v) {
                            return !is_null($v);
                        }));

                        $sql .= 'IS NULL';

                        if (!count($value)) {
                            $sql .= ')';

                            continue;
                        }
                        $sql .= ' OR ' . $prefix . $column . ' ';
                    }

                    $sql .= $compare . ' (';

                    for ($j = 0; $j < count($value); $j++) {
                        if ($returnOnlySQL) {
                            $valueType = gettype($value[$j]);
                            if ('string' === $valueType) {
                                $sql .= "'{$value[$j]}'";
                            } elseif ('boolean' === $valueType) {
                                $sql .= $value[$j] ? 'true' : 'false';
                            } else {
                                $sql .= "{$value[$j]}";
                            }
                        } else {
                            $sql .= ':' . $bindNames[$i] . $j;
                        }

                        if ($j < count($value) - 1) {
                            $sql .= ', ';
                        }
                    }
                    $sql .= '))';
                }
            } elseif ('BETWEEN' == $compare && 2 == count($value)) {
                if ($returnOnlySQL) {
                    $sql .= " {$logicalOperation} " . $prefix . $column . ' ' . $compare . " {$value[0]} AND {$value[1]}";
                } else {
                    $sql .= " {$logicalOperation} " . $prefix . $column . ' ' . $compare . ' :' . $bindNames[$i] . '0 AND :' . $bindNames[$i] . '1';
                }
            } else {
                // formatting the value if LIKE functions are used
                if ('ILIKE' == $compare || 'LIKE' == $compare) {
                    $value = '%' . $value . '%';
                }
                // IF full text search is performed
                if ('@@' == $compare && '' != $value) {
                    if ($returnOnlySQL) {
                        $value = preg_split('/ /', $value, -1, PREG_SPLIT_NO_EMPTY);
                        $value = implode('&', $value);
                        $sql .= " {$logicalOperation} to_tsvector(lower(" . $prefix . $column . ')) ' . $compare . " to_tsquery(LOWER ('{$value}'))";
                    } else {
                        $sql .= " {$logicalOperation} to_tsvector(lower(" . $prefix . $column . ')) ' . $compare . " to_tsquery(LOWER (:{$bindNames[$i]}))";
                    }

                    continue;
                }

                // checking if value is not empty and creating a required query
                if ('' !== $value && null !== $value && '%%' !== $value) {
                    if ($returnOnlySQL) {
                        $sql .= " {$logicalOperation} " . $prefix . $column . ' ' . $compare . ' ';

                        if (is_string($value)) {
                            $sql .= "'{$value}'";
                        } elseif (is_bool($value)) {
                            $sql .= $value ? 'true' : 'false';
                        } else {
                            $sql .= $value;
                        }
                    } else {
                        $sql .= " {$logicalOperation} " . $prefix . $column . ' ' . $compare . ' :' . $bindNames[$i];
                    }
                }
            }
        }

        return $sql;
    }

    /**
     * @param array $options
     */
    public function createWhereBinds(TDbCommand $cmd, $options)
    {
        $bindNames = array_keys($options);
        $bindOptions = array_values($options);

        $keysCount = count($bindNames);
        for ($i = 0; $i < $keysCount; $i++) {
            if (!is_null($bindOptions[$i]) && is_string($bindOptions[$i]) && '' != $bindOptions[$i]) {
                continue;
            }
            $compare = $bindOptions[$i]['compare'];
            $column = $bindOptions[$i]['column'];
            $value = $bindOptions[$i]['value'];
            $prefix = $bindOptions[$i]['prefix'] ?? null;

            if ('IN' === $compare || 'NOT IN' === $compare) {
                $value = array_values(array_filter($value ?? [], fn ($v) => !is_null($v)));
                for ($j = 0; $j < count($value); $j++) {
                    $param = ':' . $bindNames[$i] . $j;
                    $val = &$value[$j];
                    $dataType = PdoHelper::getPdoType($value[$j]);

                    $cmd->bindParameter($param, $val, $dataType);
                }
            } elseif ('BETWEEN' === $compare && 2 === count($value)) {
                $dataType0 = PdoHelper::getPdoType($value[0]);
                $dataType1 = PdoHelper::getPdoType($value[1]);
                $cmd->bindParameter(':' . $bindNames[$i] . '0', $value[0], $dataType0);
                $cmd->bindParameter(':' . $bindNames[$i] . '1', $value[1], $dataType1);
            } else {
                if ('' != $bindOptions[$i]['value'] && null !== $bindOptions[$i]['value']) {
                    if ('NULL' == strtoupper($bindOptions[$i]['value'])) {
                        $nullValue = null;
                        $cmd->bindParameter(':' . $bindNames[$i], $nullValue, PDO::PARAM_INT);

                        continue;
                    }
                    if ('ILIKE' == $bindOptions[$i]['compare'] || 'LIKE' == $bindOptions[$i]['compare']) {
                        $bindOptions[$i]['value'] = '%' . $bindOptions[$i]['value'] . '%';
                    }
                }
                if ('@@' == $compare && '' != $bindOptions[$i]['value']) {
                    $bindOptions[$i]['value'] = preg_split('/\s+/', $bindOptions[$i]['value'], -1, PREG_SPLIT_NO_EMPTY);
                    $bindOptions[$i]['value'] = implode('&', $bindOptions[$i]['value']);
                }
                // checking if value is not empty and creating a required query
                if ('' !== $bindOptions[$i]['value'] && null !== $bindOptions[$i]['value'] && '%%' !== $bindOptions[$i]['value']) {
                    $dataType = PdoHelper::getPdoType($bindOptions[$i]['value']);
                    $cmd->bindParameter(':' . $bindNames[$i], $bindOptions[$i]['value'], $dataType);
                }
            }
        }
    }

    public function createHavingSQL($sql, $options, $returnOnlySQL)
    {
        $bindNames = array_keys($options);
        $bindOptions = array_values($options);

        for ($i = 0; $i < count($bindNames); $i++) {
            $compare = $bindOptions[$i]['compare'];
            $column = $bindOptions[$i]['column'];
            $value = $bindOptions[$i]['value'];
            $prefix = (isset($bindOptions[$i]['prefix'])) ? $bindOptions[$i]['prefix'] . '.' : '';

            if ('IN' == $compare || 'NOT IN' == $compare) {
                if (count($value) > 0) {
                    $sql .= ' AND ' . $prefix . $column . ' ' . $compare . ' (';
                    for ($j = 0; $j < count($value); $j++) {
                        if ($returnOnlySQL) {
                            $sql .= "{$value[$j]}";
                        } else {
                            $sql .= ':' . $bindNames[$i] . $j;
                        }

                        if ($j < count($value) - 1) {
                            $sql .= ', ';
                        }
                    }
                    $sql .= ')';
                }
            } else {
                // formatting the value if LIKE functions are used
                if ('ILIKE' == $compare || 'LIKE' == $compare) {
                    $value = '%' . $value . '%';
                }
                // checking if value is not empty and creating a required query
                if ('' != $value && null !== $value && '%%' != $value) {
                    if ($returnOnlySQL) {
                        $sql .= ' AND ' . $prefix . $column . ' ' . $compare . " '" . $value . "'";
                    } else {
                        $sql .= ' AND ' . $prefix . $column . ' ' . $compare . ' :' . $bindNames[$i];
                    }
                }
            }
        }

        return $sql;
    }

    public function createHavingBinds(TDbCommand $cmd, $options)
    {
        $bindNames = array_keys($options);
        $bindOptions = array_values($options);

        for ($i = 0; $i < count($bindNames); $i++) {
            $compare = $bindOptions[$i]['compare'];
            $column = $bindOptions[$i]['column'];
            $value = $bindOptions[$i]['value'];
            $prefix = $bindOptions[$i]['prefix'];

            if ('IN' === $compare || 'NOT IN' === $compare) {
                for ($j = 0; $j < count($value); $j++) {
                    $dataType = PdoHelper::getPdoType($value[$j]);
                    $cmd->bindParameter(':' . $bindNames[$i] . $j, $value[$j], $dataType);
                }
            } else {
                // formatting the value if LIKE functions are used
                if ('' != $bindOptions[$i]['value'] && null !== $bindOptions[$i]['value']) {
                    if ('NULL' == strtoupper($bindOptions[$i]['value'])) {
                        $nullValue = null;
                        $cmd->bindParameter(':' . $bindNames[$i], $nullValue, PDO::PARAM_INT);

                        continue;
                    }
                    if ('ILIKE' == $bindOptions[$i]['compare'] || 'LIKE' == $bindOptions[$i]['compare']) {
                        $bindOptions[$i]['value'] = '%' . $bindOptions[$i]['value'] . '%';
                    }
                }
                // checking if value is not empty and creating a required query
                if ('' != $bindOptions[$i]['value'] && null !== $bindOptions[$i]['value'] && '%%' != $bindOptions[$i]['value']) {
                    $dataType = PdoHelper::getPdoType($bindOptions[$i]['value']);
                    $cmd->bindParameter(':' . $bindNames[$i], $bindOptions[$i]['value'], $dataType);
                }
            }
        }
    }

    public function addItem($options)
    {
        if (isset($options['id_name'])) {
            $id_name = $options['id_name'];
        } else {
            $id_name = 'id';
        }

        $tablename = $options['tablename'];
        $fieldNames = array_keys($options['mainData']);
        $fieldValues = array_values($options['mainData']);

        $fields = implode(', ', $fieldNames);
        $count = count($fieldNames);

        $sql = 'INSERT INTO ' . $tablename . ' (' . $fields . ') VALUES (';
        for ($i = 0; $i < $count; $i++) {
            $sql .= ':field' . $i;
            if ($i < ($count - 1)) {
                $sql .= ', ';
            }
        }
        $sql .= ') RETURNING ' . $id_name;

        $cmd = $this->DbModule->createCommand($sql);
        for ($i = 0; $i < $count; $i++) {
            $data_type = PDO::PARAM_STR;
            if (is_bool($fieldValues[$i])) {
                $data_type = PDO::PARAM_BOOL;
            }
            $cmd->bindParameter(':field' . $i, $fieldValues[$i], $data_type);
        }

        return $cmd->queryScalar();
    }

    public function getPlotsOwnres($pcRelIds = [])
    {
        $sqlSelect = 'SELECT
                    por.*, CAST(CAST(por.owner_id AS text)||CAST(por.owner_id AS text) AS numeric(24,0)) as fakeid
                FROM
                    su_plots_owners_rel por
                WHERE
                    por.is_heritor = FALSE
                AND por.percent IS NOT NULL';

        if (!empty($pcRelIds)) {
            $sqlSelect .= ' AND por.pc_rel_id IN (' . implode(',', $pcRelIds) . ')';
        }

        $cmd = $this->DbModule->createCommand($sqlSelect);

        return $results = $cmd->query()->readAll();
    }

    public function getParentPath($parentPath)
    {
        $sqlSelect = "SELECT
                    por.pc_rel_id
                FROM
                    su_plots_owners_rel por
                WHERE
                    por.path ~ '" . $parentPath . "'
                GROUP BY por.pc_rel_id";

        $cmd = $this->DbModule->createCommand($sqlSelect);

        return $results = $cmd->query()->readAll();
    }

    public function addItems($options)
    {
        $tablename = $options['tablename'];
        $columns = $options['columns'];
        $fieldValues = $options['values'];

        $query = "INSERT INTO {$tablename} ({$columns}) VALUES ";

        foreach ($fieldValues as $k => $v) {
            $i = 0;

            $query .= '(';
            foreach ($v as $key => $value) {
                $query .= ':v_' . $key . '_' . $k;

                if ($i !== sizeof($v) - 1) {
                    $query .= ', ';
                }
                $i++;
            }
            $query .= ')';

            if ($k !== sizeof($fieldValues) - 1) {
                $query .= ', ';
            }
        }

        $cmd = $this->DbModule->createCommand($query);

        foreach ($fieldValues as $k => $v) {
            foreach ($v as $key => $value) {
                $dataType = PdoHelper::getPdoType($value);
                $cmd->bindValue(':v_' . $key . '_' . $k, $value, $dataType);
            }
        }

        $cmd->execute();
    }

    /**
     * Prepares Update SQL query.
     *
     * @param string $table , name of the table, CAN NOT BE NULL
     * @param array $fields , fields in table, which values will be updated in, CAN NOT BE NULL
     * @param array $fieldTerms , field's name of records, which will be used in where clause, CAN NOT BE NULL
     *
     * @return string
     */
    public function prepareUpdateSQL($table, $fields, $fieldTerms)
    {
        $sql = "UPDATE {$table} SET ";

        $cntFields = count($fields);

        for ($i = 0; $i < $cntFields; $i++) {
            $sql .= " {$fields[$i]} = :setField{$i}";
            if ($i != $cntFields - 1) {
                $sql .= ', ';
            }
        }

        $cntTerms = count($fieldTerms);

        if ($cntTerms) {
            $sql .= ' WHERE ';
        }

        for ($i = 0; $i < $cntTerms; $i++) {
            $sql .= " {$fieldTerms[$i]} = :whereField{$i}";
            if ($i != $cntTerms - 1) {
                $sql .= ' AND ';
            }
        }

        return $sql;
    }

    public function editItem($options)
    {
        $tablename = $options['tablename'];
        $fieldNames = array_keys($options['mainData']);
        $fieldValues = array_values($options['mainData']);

        $whereFields = [];
        $whereValues = [];
        if (count($options['where']) > 0) {
            $whereFields = array_keys($options['where']);
            $whereValues = array_values($options['where']);
        }

        if ($options['id_name']) {
            $id_name = $options['id_name'];
        } else {
            $id_name = 'id';
        }

        $id_array = [];
        if ($options['id_string']) {
            $id_array = explode(',', $options['id_string']);
        }

        // List of fields to be treated as column names rather than values
        $columnFields = $options['columnFields'] ?? [];

        $sql = 'UPDATE ' . $tablename . ' SET ';
        $countParams = count($fieldNames);
        for ($i = 0; $i < $countParams; $i++) {
            if (is_array($fieldValues[$i])) {
                $sql .= "\"{$fieldNames[$i]}\" = " . $fieldValues[$i]['value'];
            } elseif (in_array($fieldValues[$i], $columnFields)) {
                // If the field is in $columnFields, add it directly as a column
                $sql .= "\"{$fieldNames[$i]}\" = " . $fieldValues[$i];
            } else {
                $sql .= " \"{$fieldNames[$i]}\" = :setField{$i}";
            }
            if ($i != $countParams - 1) {
                $sql .= ', ';
            }
        }

        if (isset($options['from'])) {
            $sql .= ' from ' . $options['from'];
        }

        $whereCount = count($whereFields);

        if ($whereCount || count($id_array) > 0) {
            $sql .= ' WHERE TRUE';
        }

        if (count($id_array) > 0) {
            $sql .= " AND {$id_name} IN(";

            for ($i = 0; $i < count($id_array); $i++) {
                $sql .= ':str_var' . $i;
                if ($i < count($id_array) - 1) {
                    $sql .= ',';
                }
            }

            $sql .= ')';
        }

        // Add the conditions in the WHERE section using the columns or values according to $columnFields
        for ($i = 0; $i < count($whereFields); $i++) {
            if (in_array($whereFields[$i], $columnFields)) {
                // If the field is in $columnFields, add it directly as a column
                $sql .= " AND {$whereFields[$i]} = " . $options['where'][$whereFields[$i]];
            } else {
                // Otherwise we add it as a placeholder
                $sql .= " AND {$whereFields[$i]} = :whereField{$i}";
            }
        }

        $cmd = $this->DbModule->createCommand($sql);
        for ($i = 0; $i < $countParams; $i++) {
            if (is_array($fieldValues[$i])) {
                foreach ($fieldValues[$i]['parameters'] as $parameterKey => $parameterValue) {
                    $parameterType = PdoHelper::getPdoType($parameterValue);
                    $cmd->bindParameter(':' . $parameterKey, $parameterValue, $parameterType);
                }
            } else {
                // Bind parameters for where only if not specified in columnFields
                if (!in_array($fieldValues[$i], $columnFields)) {
                    $parameterType = PdoHelper::getPdoType($fieldValues[$i]);
                    $cmd->bindParameter(':setField' . $i, $fieldValues[$i], $parameterType);
                }
            }
        }

        for ($i = 0; $i < count($id_array); $i++) {
            $cmd->bindParameter(':str_var' . $i, $id_array[$i]);
        }
        for ($i = 0; $i < $whereCount; $i++) {
            // Bind parameters for where only if not specified in columnFields
            if (!in_array($whereFields[$i], $columnFields)) {
                $cmd->bindParameter(':whereField' . $i, $whereValues[$i]);
            }
        }

        return $cmd->execute();
    }

    public function getIsakLayerNumberExist($tablename, $isak_number)
    {
        $sql = "SELECT COUNT(*) FROM {$tablename} WHERE prc_uin = :number";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':number', $isak_number);

        return $cmd->queryScalar();
    }

    public function getIsakLayerGeomByNumber($tablename, $isak_number)
    {
        $sql = "SELECT ST_AsText(geom) FROM {$tablename} WHERE prc_uin = :number";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':number', $isak_number);

        return $cmd->queryScalar();
    }

    public function getPolygonDataById($tablename, $whereFields, $whereValues, $return)
    {
        if ($return) {
            $return = implode(',', $return);
        } else {
            $return = '*';
        }
        $sql = "SELECT {$return} FROM {$tablename} WHERE true";
        for ($i = 0; $i < count($whereFields); $i++) {
            $sql .= ' AND ' . $whereFields[$i] . ' = :' . $whereFields[$i];
        }
        $cmd = $this->DbModule->createCommand($sql);
        for ($i = 0; $i < count($whereFields); $i++) {
            $cmd->bindParameter(':' . $whereFields[$i], $whereValues[$i]);
        }

        return $cmd->query()->readAll();
    }

    public function getExportItemsForTrimble($tablename, $id = false, $field = 'gid')
    {
        $sql = "SELECT ST_asText(ST_Centroid(ST_extent(ST_Transform(geom, 4326)))) as extent FROM {$tablename}";

        if ($id) {
            $sql .= " WHERE {$field} = :gid";
        }

        $cmd = $this->DbModule->createCommand($sql);
        if ($id) {
            $cmd->bindParameter(':gid', $id);
        }

        return $cmd->query()->read();
    }

    public function setLayerGPSGeom()
    {
        $sql = 'UPDATE layer_gps SET geom = ST_SetSRID(geom, 32635)';

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function deleteItemsByParams($options)
    {
        if ($options['id_name']) {
            $id_name = $options['id_name'];
        } else {
            $id_name = 'id';
        }

        $id_array = [];
        if ($options['id_string']) {
            $id_array = explode(',', $options['id_string']);
        }

        if (empty($id_array) and empty($options['where'])) {
            throw new MTRpcException('Missing deleting parameters...');
        }

        $sql = "DELETE FROM {$options['tablename']} WHERE true ";

        if (!empty($id_array)) {
            $sql .= "AND {$id_name} IN(";

            for ($i = 0; $i < count($id_array); $i++) {
                $sql .= ':str_var' . $i;
                if ($i < count($id_array) - 1) {
                    $sql .= ',';
                }
            }

            $sql .= ')';
        }

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], false);
        }

        $cmd = $this->DbModule->createCommand($sql);

        for ($i = 0; $i < $c = count($id_array); $i++) {
            $cmd->bindParameter(':str_var' . $i, $id_array[$i]);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->execute();
    }

    public function createReturnVariable($return = false, $counter, $custom_counter = false)
    {
        if ($counter) {
            if ($custom_counter) {
                $return = $custom_counter;
            } else {
                $return = 'COUNT(*)';
            }
        } elseif ($return) {
            $return = implode(',', $return);
        } else {
            $return = '*';
        }

        return $return;
    }

    public function createWhereOrSQL($sql, $options, $returnOnlySQL)
    {
        $newSql = $this->createWhereSQL($sql, $options, $returnOnlySQL, 'OR') . ')';

        return str_replace($sql . ' OR', $sql . ' AND (', $newSql);
    }

    public function getItemsByParams(array $options, bool $counter, bool $returnOnlySQL, array $bindingParams = [])
    {
        $return = $this->createReturnVariable($options['return'] ?? false, $counter, $options['custom_counter'] ?? false);

        $sql = "SELECT {$return}";

        if ($options['tablename']) {
            $sql .= " FROM {$options['tablename']}";
        }

        if (!empty($options['innerjoin'])) {
            $sql .= ' INNER JOIN ' . $options['innerjoin']['table'] . $options['innerjoin']['condition'];
        }

        if (!empty($options['leftjoin'])) {
            $sql .= ' LEFT JOIN ' . $options['leftjoin']['table'] . $options['leftjoin']['condition'];
        }

        if (!empty($options['joins']) || is_array($options['joins'])) {
            foreach ($options['joins'] as $join) {
                $sql .= ' ' . $join . ' ';
            }
        }

        $sql .= ' WHERE true ';

        if (!empty($options['where'])) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if (!empty($options['whereOrGroup'])) {
            $sql = $this->createWhereOrGroupSQL($sql, $options['whereOrGroup'], $returnOnlySQL);
        }

        if (!empty($options['whereOr'])) {
            $sql = $this->createWhereOrSQL($sql, $options['whereOr'], $returnOnlySQL);
        }

        if (!empty($options['group'])) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if (!empty($options['order']) && !empty($options['sort']) && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if (!empty($options['where'])) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        if (!empty($options['whereOrGroup'])) {
            $this->createWhereGroupBinds($cmd, $options['whereOrGroup']);
        }

        if (!empty($options['whereOr'])) {
            $this->createWhereBinds($cmd, $options['whereOr']);
        }

        if (count($bindingParams)) {
            foreach ($bindingParams as $key => $data) {
                $cmd->bindParameter($key, $data);
            }
        }

        return $cmd->query()->readAll();
    }

    public function multiEdit($options)
    {
        $updateFields = array_keys($options['update']);
        $updateValues = array_values($options['update']);

        $sql = 'UPDATE ' . $options['tablename'] . ' SET ';

        for ($i = 0; $i < count($updateFields); $i++) {
            $sql .= ' ' . $updateFields[$i] . ' = :' . $updateFields[$i];
            if ($i < count($updateFields) - 1) {
                $sql .= ', ';
            }
        }

        $sql .= ' WHERE true ';

        if (!empty($options['where'])) {
            $sql = $this->createWhereSQL($sql, $options['where'], false);
        }

        if (!empty($options['whereOrGroup'])) {
            $sql = $this->createWhereOrGroupSQL($sql, $options['whereOrGroup'], false);
        }

        $cmd = $this->DbModule->createCommand($sql);

        for ($i = 0; $i < count($updateFields); $i++) {
            $cmd->bindParameter(':' . $updateFields[$i], $updateValues[$i]);
        }

        if (!empty($options['where'])) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        if (!empty($options['whereOrGroup'])) {
            $this->createWhereGroupBinds($cmd, $options['whereOrGroup']);
        }

        $cmd->execute();
    }

    public function getLayersByParams($options, $counter, $returnOnlySQL, $bindingParams = [])
    {
        $return = $this->createReturnVariable($options['return'] ?? false, $counter, $options['custom_counter'] ?? false);

        $sql = "SELECT {$return}";

        if ($options['tablename']) {
            $sql .= " FROM {$options['tablename']}";
        }

        if (!empty($options['innerjoin'])) {
            $sql .= ' INNER JOIN ' . $options['innerjoin']['table'] . $options['innerjoin']['condition'];
        }

        if (!empty($options['leftjoin'])) {
            $sql .= ' LEFT JOIN ' . $options['leftjoin']['table'] . $options['leftjoin']['condition'];
        }

        if (!empty($options['joins']) || is_array($options['joins'])) {
            foreach ($options['joins'] as $join) {
                $sql .= ' ' . $join . ' ';
            }
        }

        $sql .= ' WHERE true ';

        if (!empty($options['where'])) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }
        if (!empty($options['whereOr'])) {
            $sql = $this->createWhereOrSQL($sql, $options['whereOr'], $returnOnlySQL);
        }

        if (!empty($options['whereOrGroup'])) {
            $sql = $this->createWhereOrGroupSQL($sql, $options['whereOrGroup'], $returnOnlySQL);
        }

        if (!empty($options['group'])) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if (!empty($options['order']) && !empty($options['sort']) && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];

            $layer = UserLayers::getLayerById($options['layer_id']);
            $layerIdColumnName = $layer->getDefinitionByCategory(Config::LAYER_COLUMN_CATEGORY_GID)['col_name'];

            if ($options['sort'] !== $layerIdColumnName) {
                $sql .= ", {$layerIdColumnName} " . $options['order'];
            }
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && false == $counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if (!empty($options['where'])) {
            $this->createWhereBinds($cmd, $options['where']);
        }
        if (!empty($options['whereOr'])) {
            $this->createWhereBinds($cmd, $options['whereOr']);
        }

        if (!empty($options['whereOrGroup'])) {
            $this->createWhereGroupBinds($cmd, $options['whereOrGroup']);
        }

        if (count($bindingParams)) {
            foreach ($bindingParams as $key => $data) {
                $cmd->bindParameter($key, $data);
            }
        }

        return $cmd->query()->readAll();
    }

    public function updateItemStatus($options)
    {
        if ($options['status_name']) {
            $status_name = $options['status_name'];
        } else {
            $status_name = 'status';
        }

        if ($options['id_name']) {
            $id_name = $options['id_name'];
        } else {
            $id_name = 'id';
        }

        $sql = "UPDATE {$options['tablename']} SET {$status_name} = :status WHERE {$id_name} = :id";

        $cmd = $this->DbModule->createCommand($sql);

        $cmd->bindParameter(':status', $options['status']);
        $cmd->bindParameter(':id', $options['id']);

        $cmd->execute();
    }

    public function getOwnership($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM su_plots_owners_rel po
        INNER JOIN su_contracts_plots_rel pc ON(po.pc_rel_id = pc.id) WHERE true";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($returnOnlySQL) {
            return $sql;
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getCooperators($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM {$this->tableCooperators} WHERE true";

        if ($options['whereOr']) {
            $values = explode(' ', $options['whereOr']['value']);
            $sql .= ' AND';

            for ($i = 0; $i < count($values); $i++) {
                $value = $values[$i];

                $sql .= " {$options['whereOr']['column1']} {$options['whereOr']['compare']} '%{$value}%' ";
                $sql .= " OR {$options['whereOr']['column2']} {$options['whereOr']['compare']} '%{$value}%' ";
                $sql .= " OR {$options['whereOr']['column3']} {$options['whereOr']['compare']} '%{$value}%' ";

                if ($i != (count($values) - 1)) {
                    $sql .= ' OR';
                }
            }
        }

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        if ($options['whereOr']) {
            $this->createWhereOrBinds($cmd, $options['whereOr'], $values);
        }

        return $cmd->query()->readAll();
    }

    public function transformColumnCRS($tablename, $tocrs)
    {
        $sql = "SELECT
            type
        FROM geometry_columns
        WHERE
            f_table_schema = 'public'
        AND f_table_name = '{$tablename}'
        AND f_geometry_column = 'geom'";

        $cmd = $this->DbModule->createCommand($sql);
        $geomColumnType = $cmd->queryScalar();

        $sql = "
        ALTER TABLE {$tablename} DROP COLUMN IF EXISTS geom_new;
        SELECT AddGeometryColumn('{$tablename}', 'geom_new', {$tocrs}, '{$geomColumnType}', 2);
        UPDATE {$tablename} set geom_new = st_transform(geom, :tocrs_2);
        ALTER TABLE {$tablename} DROP COLUMN IF EXISTS geom;
        ALTER TABLE {$tablename} RENAME COLUMN geom_new TO geom;";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':tocrs_2', $tocrs, PDO::PARAM_INT);

        $cmd->execute();
    }

    public function getDataByQuery(string $query, array $params = [])
    {
        $cmd = $this->DbModule->createCommand($query);

        foreach ($params as $key => $value) {
            $cmd->bindParameter(":{$key}", $value);
        }

        return $cmd->query()->readAll();
    }

    public function updateLayerProjection($tablename, $projection)
    {
        $sql = "UPDATE {$tablename} SET geom = ST_SetSRID(geom, {$projection})";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function getTableName($database, $tablename)
    {
        $sql = 'SELECT DISTINCT table_name FROM information_schema.columns ';
        $sql .= "where table_catalog = :dbname and
                table_schema = 'public' and
                table_name LIKE :tname";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':dbname', $database);
        $cmd->bindParameter(':tname', $tablename);

        return $cmd->query()->readAll();
    }

    public function addColumn($tablename, $column, $columnType = 'character varying(255)')
    {
        $sql = "ALTER TABLE {$tablename} ADD COLUMN IF NOT EXISTS \"{$column}\" {$columnType}";
        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->execute();
    }

    public function addColumnsToWorkLayer(UserLayers $layer, array $columnDefinitions)
    {
        if (!count($columnDefinitions)) {
            return;
        }

        $sql = '';

        if (Config::LAYER_TYPE_WORK_LAYER != $layer->layer_type) {
            throw new Exception('Invalid layer type');
        }

        // Sort the array so the virtual columns are at the end of the array. This way their col_reference columns are added before them
        usort($columnDefinitions, function ($a, $b) {
            return $a['col_virtual'] <=> $b['col_virtual'];
        });

        foreach ($columnDefinitions as $columnDef) {
            $definitionValid = UserLayers::validateDefinition($columnDef);
            $colName = '"' . $columnDef['col_name'] . '"';
            $colType = $GLOBALS['Layers']['columnTypesByCategory'][$columnDef['col_category']];

            if (!$definitionValid) {
                throw new Exception("Invalid column definition for column {$colName}");
            }

            if ($columnDef['col_virtual'] && $columnDef['col_expression']) {
                $colType = $colType . ' GENERATED ALWAYS AS (' . $columnDef['col_expression'] . ') STORED';
            }

            $sql .= "ALTER TABLE {$layer->table_name} ADD COLUMN {$colName} {$colType};\n";
        }

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();

        $layer->definitions = json_encode(array_merge($layer->getDefinitions(), $columnDefinitions));
        $layer->save();
    }

    /**
     * This method updates the physical table columns and the layer definitions of a work layer.
     */
    public function setWorkLayerColumnsByDefinitions(UserLayers $layer, array $columnDefinitions): bool
    {
        if (Config::LAYER_TYPE_WORK_LAYER != $layer->layer_type) {
            throw new MTRpcException('LAYER_TYPE_NOT_SUPPORTED', -33064);
        }

        $stringHelper = new StringHelper();

        $defaultDefinitions = UserLayers::getDefinitionsByType($layer->layer_type);
        $defaultDefinitions = array_combine(array_column($defaultDefinitions, 'col_name'), $defaultDefinitions); // Use col_name as key
        $oldDefinitions = $layer->getDefinitions();
        $oldDefinitions = array_combine(array_column($oldDefinitions, 'col_name'), $oldDefinitions); // Use col_name as key

        $definitionsToSet = [];
        $addColumnsSql = '';
        $dropColumnsSql = '';
        $regenerateMapFile = false;

        foreach ($columnDefinitions as $columnDef) {
            $isValidDefinition = UserLayers::validateDefinition(
                $columnDef,
                [
                    'col_visible',
                    'col_multiedit',
                    'col_personalizable',
                    'col_singleedit',
                    'col_sortable',
                    'col_exportable',
                    'col_copyable',
                    'col_virtual',
                    'col_expression',
                    'col_filter_selection_type',
                ]
            );

            if (!$isValidDefinition) {
                throw new MTRpcException('INVALID_LAYER_DEFINITIONS', -33055);
            }

            // Definition to create
            if (!strlen($columnDef['col_name'] ?? '')) {
                $colName = strtolower($stringHelper->transLitString($columnDef['col_title']));
                $existingDefinitionByName = UserLayers::filterDefinitions($layer->getDefinitions(), [['col_name' => $colName]]);
                // valide if newly created column name already exists in the layer definitions
                if ($existingDefinitionByName) {
                    throw new MTRpcException('INVALID_LAYER_DEFINITIONS', -33055);
                }

                $columnDef['col_name'] = $colName;
                $columnDef['col_multiedit'] = true;
                $columnDef['col_personalizable'] = true;
                $columnDef['col_visible'] ??= true;
                $columnDef['col_singleedit'] = true;
                $columnDef['col_sortable'] = true;
                $columnDef['col_exportable'] = true;
                $columnDef['col_copyable'] = true;
                $columnDef['col_virtual'] = false;
                $columnDef['col_expression'] = null;
                $columnDef['col_reference'] = null;
                $columnDef['col_filter_selection_type'] = Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE;

                $colType = $GLOBALS['Layers']['columnTypesByCategory'][$columnDef['col_category']];
                $addColumnsSql .= "ALTER TABLE {$layer->table_name} ADD COLUMN IF NOT EXISTS \"{$columnDef['col_name']}\" {$colType};\n";

                $definitionsToSet[$columnDef['col_name']] = $columnDef;

                if (in_array($columnDef['col_category'], $GLOBALS['Layers']['columnsWithVirtualColumns'])) {
                    $virtualColumnDef = $columnDef;
                    $virtualColumnDef['col_name'] = strtolower('virtual_' . $columnDef['col_name'] . '_name');
                    $virtualColumnDef['col_virtual'] = true;
                    $virtualColumnDef['col_singleedit'] = false;
                    $virtualColumnDef['col_multiedit'] = false;
                    $virtualColumnDef['col_reference'] = $columnDef['col_name'];

                    $colExpression = $GLOBALS['Layers']['virtualColumnsExpressions'][$columnDef['col_category']];
                    $virtualColumnDef['col_expression'] = str_replace('replace_with_column_name', $columnDef['col_name'], $colExpression);

                    $colType = $colType . ' GENERATED ALWAYS AS (' . $virtualColumnDef['col_expression'] . ') STORED';
                    $addColumnsSql .= "ALTER TABLE {$layer->table_name} ADD COLUMN IF NOT EXISTS \"{$virtualColumnDef['col_name']}\" {$colType};\n";

                    $definitionsToSet[$virtualColumnDef['col_name']] = $virtualColumnDef;

                    // Update reference column definitons to not be personalizable, visible or expotable
                    $definitionsToSet[$columnDef['col_name']]['col_personalizable'] = false;
                    $definitionsToSet[$columnDef['col_name']]['col_exportable'] = false;
                    $definitionsToSet[$columnDef['col_name']]['col_visible'] = false;
                }

                continue;
            }

            // Definition to update
            if (isset($oldDefinitions[$columnDef['col_name']])) {
                // The col_name exists in the old definitions => definition to update
                $definitionsToSet[$columnDef['col_name']] = $oldDefinitions[$columnDef['col_name']];

                if ($oldDefinitions[$columnDef['col_name']]['col_personalizable']) {
                    // Update only the col_visible and col_title properties of the definitions that are personalizable

                    if (!is_null($columnDef['col_visible'])) {
                        $definitionsToSet[$columnDef['col_name']]['col_visible'] = $columnDef['col_visible'];
                    }

                    if (!is_null($columnDef['col_title'])) {
                        $definitionsToSet[$columnDef['col_name']]['col_title'] = $columnDef['col_title'];
                    }
                }

                // Add the reference column  to $definitionsToSet when updating a virtual column,
                // because this array is used to set the final definitions of the layer
                // and if the reference column is missing it will be deleted.
                if ($columnDef['col_reference']) {
                    $referenceColumnDef = $oldDefinitions[$columnDef['col_reference']];
                    $referenceColumnDef['col_title'] = $columnDef['col_title'];
                    $definitionsToSet[$referenceColumnDef['col_name']] = $referenceColumnDef;
                }
            }
        }

        foreach ($oldDefinitions as $oldColumnDef) {
            if (
                in_array($oldColumnDef['col_name'], array_column($columnDefinitions, 'col_name'))
                || in_array($oldColumnDef['col_name'], array_column($columnDefinitions, 'col_reference'))
                || in_array($oldColumnDef['col_name'], array_column($defaultDefinitions, 'col_name'))
                || in_array($oldColumnDef['col_name'], array_column($definitionsToSet, 'col_name'))
            ) {
                continue;
            }

            // Definition to delete.
            // The definitions that exist in the old definitions but not in the new definitions will be deleted
            $dropColumnsSql .= "ALTER TABLE {$layer->table_name} DROP COLUMN IF EXISTS \"{$oldColumnDef['col_name']}\";\n";
        }

        $definitionsToSet = array_merge($defaultDefinitions, $definitionsToSet);

        $transaction = $this->startTransaction();

        try {
            $layer->definitions = json_encode(array_values($definitionsToSet));
            $layer->save();

            if (strlen($addColumnsSql) > 0) {
                $addColumnsCmd = $this->DbModule->createCommand($addColumnsSql);
                $addColumnsCmd->execute();
                $regenerateMapFile = true;
            }

            if (strlen($dropColumnsSql) > 0) {
                $dropColumnsCmd = $this->DbModule->createCommand($dropColumnsSql);
                $dropColumnsCmd->execute();
                $regenerateMapFile = true;
            }

            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollback();
            Sentry::logException($e);

            $layer->definitions = json_encode(array_values($oldDefinitions));
            $layer->save();
            $regenerateMapFile = false;
        }

        return $regenerateMapFile;
    }

    public function commonSqlResult($sql, $options, $counter, $returnOnlySQL)
    {
        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function commonSqlResultWithCounts($sql, $options, $counter, $returnOnlySQL)
    {
        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($counter) {
            $sql .= ') as counts_info';
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    /**
     * Returns a polygon as WKT from a given table.
     *
     * @param string $tableName A table with a geometry column
     * @param float $lon The longitude coordinate
     * @param float $lat The latitude coordinate
     * @param array $extent The extent in which is the point. For faster search.
     * @param string $geomField the name of the geometry column
     *
     * @return string the WKT representation of the selected geometry
     */
    public function getPolygonDataByPointFromLogin3(
        $tableName,
        $lon,
        $lat,
        array $extent = [],
        $idField = 'gid',
        $geomField = 'geom'
    ) {
        $selectGeomField = 'geom';
        if ($tableName == $GLOBALS['Layers']['vpsTables'][Config::LAYER_TYPE_VPS_PASISHTA]) {
            // this table is used when selecting (or clipping) objects from the merg layer
            $tableName = 'layer_merg_selection';
            $selectGeomField = 'st_simplify(geom, 60)';
        }

        $whereExtent = '';
        if (count($extent)) {
            $extent = implode(',', $extent);
            $whereExtent = " geom && ST_MakeEnvelope({$extent}) AND ";
        }
        $sql = "SELECT
            {$idField}, st_astext({$selectGeomField}) as geom
        FROM
            {$tableName} AS A
        WHERE
            {$whereExtent}
            st_intersects(st_setsrid(st_makepoint({$lon}, {$lat}), 32635), a.geom)";

        $cmd = $this->DbModule2->createCommand($sql);

        return $cmd->query()->readAll();
    }

    /**
     * Makes all invalid geometries in a given table valid.
     *
     * @param string $tablename the the table name
     * @param string $geomField default column is 'geom'
     */
    public function stMakeValid($tablename, $geomField = 'geom')
    {
        $sql = "UPDATE {$tablename} SET {$geomField} = st_makevalid({$geomField}) WHERE NOT ST_IsValid(geom)";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function removeGeometryCollection($tablename, $geomField = 'geom')
    {
        $sql = "UPDATE {$tablename} SET {$geomField} = ST_Multi(ST_CollectionExtract(ST_MakeValid({$geomField}), 3)) WHERE
		NOT ST_IsValid({$geomField}) OR ST_GeometryType({$geomField}) = 'ST_GeometryCollection'";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    /**
     * Create a View with name sepp_for_isak_["for isak" layer id].
     *
     * @param int $layerId for ISAK layer id
     */
    public function createSEPPReportView($layerId, $databse)
    {
        $viewName = 'sepp_for_isak_' . $layerId;
        if ($this->getTableNameExist($databse, $viewName)) {
            return;
        }
        $sqlView = "DROP MATERIALIZED VIEW IF EXISTS \"{$viewName}\";
            CREATE MATERIALIZED VIEW \"{$viewName}\"
            AS
             SELECT a.geom
               FROM dblink('host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "'::text, 'SELECT
                geom
            FROM
                layer_allowable_final,
                (
                    SELECT
                        A [ 1 ]:: NUMERIC AS xmin,
                        A [ 2 ]:: NUMERIC AS ymin,
                        A [ 3 ]:: NUMERIC AS xmax,
                        A [ 4 ]:: NUMERIC AS ymax
                    FROM
                        (
                            SELECT
                                regexp_split_to_array(
                                    (
                                        SELECT
                                            extent
                                        FROM
                                            su_users_layers
                                        WHERE
                                            ID = {$layerId}
                                    ),
                                    '' ''
                                )
                        ) AS dt (A)
                ) AS extent
            WHERE
                geom && st_makeenvelope (
                    extent.xmin,
                    extent.ymin,
                    extent.xmax,
                    extent.ymax,
                    32635
                )'::text) a(geom geometry);";

        $cmd = $this->DbModule->createCommand($sqlView);
        $cmd->execute();

        $sqlOwner = "ALTER MATERIALIZED VIEW \"{$viewName}\" OWNER TO \"postgres\";";
        $cmd = $this->DbModule->createCommand($sqlOwner);
        $cmd->execute();

        $sqlIndex = "DROP INDEX IF EXISTS \"{$viewName}_gis\"; CREATE INDEX \"{$viewName}_gis\" ON \"public\".\"{$viewName}\" USING gist (geom \"public\".\"gist_geometry_ops_2d\");";
        $cmd = $this->DbModule->createCommand($sqlIndex);
        $cmd->execute();
    }

    /**
     * Create a View with name kvs_contracts_update_["user file" ekate].
     *
     * @param int $id su_users_files id
     * @param string $ekate
     * @param string $dateUploaded
     */
    public function createKvsContractsUpdateView($id, $ekate, $dateUploaded, $databse)
    {
        $tmpTable = 'layer_tmp_kvs_' . $ekate;

        $viewName = 'kvs_contracts_update_' . $ekate;
        if ($this->getTableNameExist($databse, $viewName)) {
            return;
        }
        $sqlView = "CREATE MATERIALIZED VIEW {$viewName}
            AS
            SELECT
                kvs.gid,
                kvs.kad_ident,
                kvs.ekate,
                C .c_num,
                C . ID AS contract_id,
                C .nm_usage_rights,
                C .is_annex,
                to_char(C .due_date, 'DD.MM.YYYY') AS due_date,
                ST_ASTEXT (kvs.geom) AS geom,
                ARRAY_AGG (
                    DISTINCT (
                        nkvs.kad_no || ':' || ST_ASTEXT (nkvs.geom)
                    )
                ) AS ngeom,
                ARRAY_AGG (
                    DISTINCT (
                        oldkvs.kad_ident || ':' || ST_ASTEXT (oldkvs.geom)
                    )
                ) AS oldgeom,
                ARRAY_AGG (DISTINCT(nkvs.kad_no)) AS new_kad_idents,
                ARRAY_AGG (DISTINCT(oldkvs.kad_ident)) AS old_kadidents
            FROM
                layer_kvs kvs
            JOIN su_contracts_plots_rel cp ON (kvs.gid = cp.plot_id) AND kvs.ekate = '{$ekate}'
            JOIN su_contracts C ON (cp.contract_id = C . ID)
            LEFT JOIN {$tmpTable} nkvs 
                ON 
                    (ST_Intersects(kvs.geom, nkvs.geom) and not ST_Touches(kvs.geom, nkvs.geom))
                AND 
                    kvs.ekate = '{$ekate}'
                AND
                    st_area (
                        st_intersection (kvs.geom, nkvs.geom)
                    ) > 50       
            JOIN layer_kvs oldkvs 
                ON 
                   (ST_Intersects(oldkvs.geom, nkvs.geom) and not ST_Touches(oldkvs.geom, nkvs.geom)) 
                AND 
                    oldkvs.ekate = '{$ekate}'
                AND       
                    st_area (
                        st_intersection (oldkvs.geom, nkvs.geom)
                    ) > 50            
            WHERE
                kvs.is_edited = FALSE
                AND oldkvs.is_edited = FALSE
                AND kvs.kad_ident NOT IN (
                    SELECT
                        kad_no
                    FROM
                        {$tmpTable}
                )
                AND oldkvs.kad_ident NOT IN (
                    SELECT
                        kad_no
                    FROM
                        {$tmpTable}
                )
                AND kvs.ekate = '{$ekate}'
                AND st_isvalid(nkvs.geom)
            GROUP BY
                kvs.gid,
                C .c_num,
                C . ID";

        $cmd = $this->DbModule->createCommand($sqlView);
        $cmd->execute();

        $sqlOwner = "ALTER MATERIALIZED VIEW {$viewName} OWNER TO postgres;";
        $cmd = $this->DbModule->createCommand($sqlOwner);
        $cmd->execute();
    }

    public function createCsdMatView(string $viewName, string $ekatte, string $csdDatayear, array $coloringOptions = [], string $coloringType = LayerStyles::SINGLE_COLORING_TYPE)
    {
        $declaredAreaThreshold = Config::DECLARED_AREA_THRESHOLD;
        $declType69 = Config::DECLARATION_TYPE_69;
        $declType70 = Config::DECLARATION_TYPE_70;

        [$fillColorSQL, $borderColorSQL, $label, $joinColorTable, $moreGroupingParams] = $this->prepareCsdViewStyle($coloringType, $coloringOptions);

        $sqlView = "WITH plot_documents_count_by_status AS (
                        SELECT
                            kad_ident,
                            get_document_status_by_dates(scd.dog_nach, scd.dog_kraj) AS status,
                            count(get_document_status_by_dates(scd.dog_nach, scd.dog_kraj)) AS documents_count
                        FROM
                            su_consolidation_do AS scd
                        WHERE
                            scd.godina = '{$csdDatayear}'
                            AND 
                            scd.ekatte = '{$ekatte}'
                        GROUP BY 
                            kad_ident, status		
                    ),
                     scd_aggregated AS (
                       SELECT
                            kad_ident,
                            JSONB_OBJECT_AGG(
                                status, documents_count
                            ) || JSONB_BUILD_OBJECT(
                                'All', SUM(documents_count)
                            ) AS number_of_documents
                        FROM
                            plot_documents_count_by_status
                        GROUP BY 
                            kad_ident
                    ),
                    lessor_aggregated as (
                        SELECT 
                            scd.kad_ident,
                            string_agg(distinct scd.sobst_ime, ',') as lessor
                        FROM
                            su_consolidation_do scd
                            INNER JOIN scd_aggregated on scd_aggregated.kad_ident = scd.kad_ident
                            INNER JOIN su_consolidation_zd as zd on zd.kad_ident = scd.kad_ident  AND zd.dog_nach >= scd.dog_nach AND zd.dog_nach <= scd.dog_kraj AND zd.polz_ident::text = scd.polz_ident::text
                        WHERE 
                            scd.godina = '{$csdDatayear}'
                            and
                            scd.ekatte = '{$ekatte}'
                        group by scd.kad_ident	
                    ),
                    sz_aggregated AS (
                        SELECT 
                            lk.kad_ident,
                            CASE
                                WHEN SUM(sz.polz_dka) OVER (PARTITION BY lk.kad_ident) < ( lk.document_area - {$declaredAreaThreshold} ) THEN 'underdeclared'::declared_area_status_enum
                                WHEN SUM(sz.polz_dka) OVER (PARTITION BY lk.kad_ident) > ( lk.document_area + {$declaredAreaThreshold} ) THEN 'overdeclared'::declared_area_status_enum
                                ELSE 'complete'::declared_area_status_enum
                            END AS declared_area_status,
                            CASE
                                WHEN sz.kod = 1 THEN '{$declType69}'
                                WHEN sz.kod = 2 THEN '{$declType70}'
                                ELSE sczk.value
                            END AS declaration_type,
                            sz.polz_dka AS declared_area,
                            sz.kod,
                            sz.polz_ime AS applicant,
                            sz.polz_ident AS applicant_id,
                            sz.godina as \"year\",
                            sczj.value AS wish
                        FROM 
                            layer_kvs lk
                            LEFT JOIN su_consolidation_zd as sz ON lk.kad_ident = sz.kad_ident
                            LEFT JOIN su_consolidation_zd_kod sczk ON sczk.kod = sz.kod
                            LEFT JOIN su_consolidation_zd_jelanie sczj on sczj.jelanie = sz.jelanie
                        WHERE 
                            lk.ekate = '{$ekatte}'
                            AND
                            godina = '{$csdDatayear}'
                        GROUP BY 
                            lk.kad_ident,lk.gid,
                            sz.polz_ime, sz.polz_ident, sz.kod,sz.godina, sz.jelanie, sz.polz_dka,
                            sczk.value,
                            sczj.value
                    )   
                    SELECT
                        row_number() OVER () AS gid,
                        lk.kad_ident,
                        lk.ekate as ekatte,
                        lk.geom,
                        lk.virtual_ntp_title AS ntp,
                        lk.virtual_category_title AS category,
                        lk.mestnost AS locality,
                        lk.document_area,
                        lk.allowable_area,
                        sz_aggregated.declared_area,
                        sz_aggregated.declared_area_status,
                        sz_aggregated.year,
                        sz_aggregated.declaration_type,
                        sz_aggregated.applicant,
                        sz_aggregated.applicant_id,
                        sz_aggregated.wish,
                        lessor_aggregated.lessor,
                        scd.number_of_documents,
                        {$fillColorSQL},
                        {$borderColorSQL},
                        {$label} as label
                    FROM
                        layer_kvs lk
                    LEFT JOIN 
                        sz_aggregated ON sz_aggregated.kad_ident = lk.kad_ident         
                    LEFT JOIN 
                        lessor_aggregated ON lessor_aggregated.kad_ident = sz_aggregated.kad_ident
                    LEFT JOIN 
                        scd_aggregated scd ON scd.kad_ident = sz_aggregated.kad_ident    
                    {$joinColorTable}
                    WHERE
                        lk.ekate = '{$ekatte}'
                        AND
                        sz_aggregated.year = '{$csdDatayear}'
                    GROUP BY              
                        lk.gid, lk.kad_ident, scd.number_of_documents, lessor_aggregated.lessor,
                        sz_aggregated.kod, sz_aggregated.declaration_type, sz_aggregated.declared_area_status, sz_aggregated.declared_area,
                        sz_aggregated.applicant, sz_aggregated.applicant_id, sz_aggregated.year, sz_aggregated.wish {$moreGroupingParams};";

        try {
            $cmd = $this->DbModule->createCommand($sqlView);
            $cmd->execute();
        } catch (Exception $ex) {
            throw new Exception('SQL validation failed: ' . $ex->getMessage());
        }

        if ($this->getViewNameExists($viewName)) {
            $this->dropMaterializedView($viewName);
        }

        $cmd = $this->DbModule->createCommand("CREATE MATERIALIZED VIEW {$viewName} AS {$sqlView}")->execute();

        $sqlOwner = "ALTER MATERIALIZED VIEW {$viewName} OWNER TO postgres;";
        $cmd = $this->DbModule->createCommand($sqlOwner);
        $cmd->execute();

        // Add unique index on uuid column
        $sqlIndex = "CREATE UNIQUE INDEX idx_{$viewName}_gid ON {$viewName} (gid);";
        $cmd = $this->DbModule->createCommand($sqlIndex);
        $cmd->execute();
    }

    public function findEkatteByCode(string $ekatteCode): ?EkatteDto
    {
        $sql = '
            SELECT
                *
            FROM
                su_ekatte
            where
                ekatte_code = :ekatteCode     
            ';

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':ekatteCode', $ekatteCode, PDO::PARAM_STR);

        $result = $cmd->query()->read();

        if ($result) {
            return new EkatteDto($result);
        }

        return null;
    }

    public function refreshView($viewName)
    {
        $sql = "REFRESH MATERIALIZED VIEW {$viewName}";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function refreshAndSelectView($viewName)
    {
        $sql = "REFRESH MATERIALIZED VIEW {$viewName};
                SELECT * FROM {$viewName};";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();

        return $cmd->query()->readAll();
    }

    public function getViewNameExists(string $viewName)
    {
        $sql = 'SELECT 1 FROM pg_class WHERE relname=:view_name';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':view_name', $viewName);

        return $cmd->queryScalar();
    }

    /**
     * The fake view mimics the materialised views in PG9.3.
     *
     * @param int $layerId for ISAK layer id
     * @param string $databse the user database
     *
     * @return [type]          [description]
     */
    public function createSEPPReportFakeView($layerId, $databse)
    {
        $viewName = 'sepp_for_isak_' . $layerId;
        if ($this->getTableNameExist($databse, $viewName)) {
            return;
        }
        $sqlView = "CREATE TABLE \"{$viewName}\"
            AS
             (SELECT a.geom
                FROM  dblink('host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "'::text,
            'SELECT
                 geom
             FROM
                 layer_allowable_final,
                 (
                     SELECT
                         A [ 1 ]:: NUMERIC AS xmin,
                         A [ 2 ]:: NUMERIC AS ymin,
                         A [ 3 ]:: NUMERIC AS xmax,
                         A [ 4 ]:: NUMERIC AS ymax
                     FROM
                         (
                             SELECT
                                 regexp_split_to_array(
                                     (
                                         SELECT
                                             extent
                                         FROM
                                             su_users_layers
                                         WHERE
                                             ID = {$layerId}
                                     ),
                                     '' ''
                                 )
                         ) AS dt (A)
                 ) AS extent
             WHERE
                 geom && st_makeenvelope (
                     extent.xmin,
                     extent.ymin,
                     extent.xmax,
                     extent.ymax,
                     32635
                 )'::text) a(geom geometry));";

        $cmd = $this->DbModule->createCommand($sqlView);
        $cmd->execute();

        $sqlOwner = "ALTER TABLE \"{$viewName}\" OWNER TO \"postgres\";";
        $cmd = $this->DbModule->createCommand($sqlOwner);
        $cmd->execute();

        $sqlIndex = "CREATE INDEX \"{$viewName}_gis\" ON \"public\".\"{$viewName}\" USING gist (geom \"public\".\"gist_geometry_ops_2d\");";
        $cmd = $this->DbModule->createCommand($sqlIndex);
        $cmd->execute();
    }

    public function refreshSEPPReportFakeView($layerId)
    {
        $viewName = 'sepp_for_isak_' . $layerId;
        $sqlTruncate = "TRUNCATE {$viewName}";
        $cmd = $this->DbModule->createCommand($sqlTruncate);
        $cmd->execute();

        $selInsert = "INSERT INTO {$viewName} (geom) SELECT a.geom
                FROM  dblink('host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "'::text,
            'SELECT
                 geom
             FROM
                 layer_allowable_final,
                 (
                     SELECT
                         A [ 1 ]:: NUMERIC AS xmin,
                         A [ 2 ]:: NUMERIC AS ymin,
                         A [ 3 ]:: NUMERIC AS xmax,
                         A [ 4 ]:: NUMERIC AS ymax
                     FROM
                         (
                             SELECT
                                 regexp_split_to_array(
                                     (
                                         SELECT
                                             extent
                                         FROM
                                             su_users_layers
                                         WHERE
                                             ID = {$layerId}
                                     ),
                                     '' ''
                                 )
                         ) AS dt (A)
                 ) AS extent
             WHERE
                 geom && st_makeenvelope (
                     extent.xmin,
                     extent.ymin,
                     extent.xmax,
                     extent.ymax,
                     32635
                 )'::text) a(geom geometry)";
        $cmd = $this->DbModule->createCommand($selInsert);
        $cmd->execute();
    }

    /**
     * Create a View with name pzp_for_isak_["for isak" layer id].
     *
     * @param int $layerId for ISAK layer id
     */
    public function createPZPReportView($layerId, $databse)
    {
        $viewName = 'pzp_for_isak_' . $layerId;
        if ($this->getTableNameExist($databse, $viewName)) {
            return;
        }
        $sqlView = "DROP MATERIALIZED VIEW IF EXISTS \"{$viewName}\";
            CREATE MATERIALIZED VIEW \"{$viewName}\"
            AS
             SELECT a.geom, a.imotcode
               FROM dblink('host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "'::text, 'SELECT
                geom, imotcode
            FROM
                layer_pzp,
                (
                    SELECT
                        A [ 1 ]:: NUMERIC AS xmin,
                        A [ 2 ]:: NUMERIC AS ymin,
                        A [ 3 ]:: NUMERIC AS xmax,
                        A [ 4 ]:: NUMERIC AS ymax
                    FROM
                        (
                            SELECT
                                regexp_split_to_array(
                                    (
                                        SELECT
                                            extent
                                        FROM
                                            su_users_layers
                                        WHERE
                                            ID = {$layerId}
                                    ),
                                    '' ''
                                )
                        ) AS dt (A)
                ) AS extent
            WHERE
                geom && st_makeenvelope (
                    extent.xmin,
                    extent.ymin,
                    extent.xmax,
                    extent.ymax,
                    32635
                )'::text) a(geom geometry, imotcode CHARACTER VARYING);";

        $cmd = $this->DbModule->createCommand($sqlView);
        $cmd->execute();

        $sqlOwner = "ALTER MATERIALIZED VIEW \"{$viewName}\" OWNER TO \"postgres\";";
        $cmd = $this->DbModule->createCommand($sqlOwner);
        $cmd->execute();

        $sqlIndex = "DROP INDEX IF EXISTS \"{$viewName}_gis\"; CREATE INDEX \"{$viewName}_gis\" ON \"public\".\"{$viewName}\" USING gist (geom \"public\".\"gist_geometry_ops_2d\");";
        $cmd = $this->DbModule->createCommand($sqlIndex);
        $cmd->execute();
    }

    /**
     * The fake view mimics the materialised views in PG9.3.
     *
     * @param int $layerId for ISAK layer id
     * @param string $databse the user database
     *
     * @return [type]          [description]
     */
    public function createPZPReportFakeView($layerId, $databse)
    {
        $viewName = 'pzp_for_isak_' . $layerId;
        if ($this->getTableNameExist($databse, $viewName)) {
            return;
        }

        $sqlView = "CREATE TABLE \"{$viewName}\"
            AS
             (SELECT a.geom, a.imotcode
                FROM  dblink('host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "'::text,
            'SELECT
                 geom, imotcode
             FROM
                 layer_pzp,
                 (
                     SELECT
                         A [ 1 ]:: NUMERIC AS xmin,
                         A [ 2 ]:: NUMERIC AS ymin,
                         A [ 3 ]:: NUMERIC AS xmax,
                         A [ 4 ]:: NUMERIC AS ymax
                     FROM
                         (
                             SELECT
                                 regexp_split_to_array(
                                     (
                                         SELECT
                                             extent
                                         FROM
                                             su_users_layers
                                         WHERE
                                             ID = {$layerId}
                                     ),
                                     '' ''
                                 )
                         ) AS dt (A)
                 ) AS extent
             WHERE
                 geom && st_makeenvelope (
                     extent.xmin,
                     extent.ymin,
                     extent.xmax,
                     extent.ymax,
                     32635
                 )'::text) a(geom geometry, imotcode CHARACTER VARYING));";

        $cmd = $this->DbModule->createCommand($sqlView);
        $cmd->execute();

        $sqlOwner = "ALTER TABLE \"{$viewName}\" OWNER TO \"postgres\";";
        $cmd = $this->DbModule->createCommand($sqlOwner);
        $cmd->execute();

        $sqlIndex = "CREATE INDEX \"{$viewName}_gis\" ON \"public\".\"{$viewName}\" USING gist (geom \"public\".\"gist_geometry_ops_2d\");";
        $cmd = $this->DbModule->createCommand($sqlIndex);
        $cmd->execute();
    }

    public function refreshPZPReportFakeView($layerId)
    {
        $viewName = 'pzp_for_isak_' . $layerId;
        $sqlTruncate = "TRUNCATE {$viewName}";
        $cmd = $this->DbModule->createCommand($sqlTruncate);
        $cmd->execute();

        $selInsert = "INSERT INTO {$viewName} (geom, imotcode) SELECT a.geom, a.imotcode
                FROM  dblink('host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "'::text,
            'SELECT
                 geom, imotcode
             FROM
                 layer_pzp,
                 (
                     SELECT
                         A [ 1 ]:: NUMERIC AS xmin,
                         A [ 2 ]:: NUMERIC AS ymin,
                         A [ 3 ]:: NUMERIC AS xmax,
                         A [ 4 ]:: NUMERIC AS ymax
                     FROM
                         (
                             SELECT
                                 regexp_split_to_array(
                                     (
                                         SELECT
                                             extent
                                         FROM
                                             su_users_layers
                                         WHERE
                                             ID = {$layerId}
                                     ),
                                     '' ''
                                 )
                         ) AS dt (A)
                 ) AS extent
             WHERE
                 geom && st_makeenvelope (
                     extent.xmin,
                     extent.ymin,
                     extent.xmax,
                     extent.ymax,
                     32635
                 )'::text) a(geom geometry, imotcode CHARACTER VARYING)";

        $cmd = $this->DbModule->createCommand($selInsert);
        $cmd->execute();
    }

    public function getCropsByYears($id, $oldCrop, $years)
    {
        $sql = 'SELECT *
                FROM su_crop_layers_data
                WHERE id = :ID
                ';

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':ID', $id);

        $cropById = $cmd->query()->read();

        $columnsWithOldCrop = [];
        // culture1, culture2, culture3, culture4, culture5
        $columsWithCrop = 5;

        for ($i = 1; $i <= $columsWithCrop; $i++) {
            if ($cropById['culture' . $i] == $oldCrop && in_array($i, $years)) {
                array_push($columnsWithOldCrop, $i);
            }
        }

        return $columnsWithOldCrop;
    }

    public function getExtentOfMultipleLayers($innerQuery, $counter, $returnOnlySQL)
    {
        if ($counter) {
            $sql = "SELECT count(subq.geom) FROM ({$innerQuery}) as subq";
        } else {
            $sql = "SELECT st_extent(subq.geom) FROM ({$innerQuery}) as subq";
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function getExtentOfRemoteMultipleLayers($innerQuery, $counter, $returnOnlySQL)
    {
        if ($counter) {
            $sql = "SELECT count(subq.geom) FROM ({$innerQuery}) as subq";
        } else {
            $sql = "SELECT st_extent(subq.geom) FROM ({$innerQuery}) as subq";
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule2->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function getMissingKVSPlots($options, $counter, $returnOnlySQL)
    {
        $sql = "WITH added_plots_dates AS (
            SELECT 
                a.parent_id, 
                'added' as annex_action, 
                MIN(a.start_date) as min_start_date, 
                MAX(a.due_date) as max_due_date,
                cp.plot_id 
            FROM 
                su_contracts as a
                inner join su_contracts_plots_rel cp on a.id = cp.contract_id and annex_action = 'added'
                INNER JOIN {$options['tablename']} as view ON view.contract_id = a.id and view.gid = cp.plot_id
            where
                a.is_annex = true
                and a.parent_id is not null
                and a.active = true 
            GROUP BY 
                a.parent_id, cp.plot_id
        ),
        removed_plots_dates AS (
            SELECT 
                a.parent_id, 
                'removed' as annex_action, 
                MIN(a.start_date) as min_start_date, 
                MAX(a.due_date) as max_due_date,
                cp.plot_id 
            FROM 
                su_contracts as a
                inner join su_contracts_plots_rel cp on a.id = cp.contract_id  and annex_action = 'removed'
                INNER JOIN {$options['tablename']} as view ON view.contract_id = a.id and view.gid = cp.plot_id 
            where
                a.is_annex = true
                and a.parent_id is not null
                and a.active = true
            GROUP BY 
                a.parent_id, cp.plot_id
        ),
        contracts AS (
            SELECT 
                c.id as contract_id, 
                c.c_num,
                COALESCE(
                    case
                        WHEN added_plots_dates.annex_action = 'added' AND added_plots_dates.min_start_date <= c.start_date 
                        	THEN added_plots_dates.min_start_date
                        WHEN removed_plots_dates.annex_action = 'removed' AND removed_plots_dates.max_due_date <= c.due_date and removed_plots_dates.min_start_date <= c.start_date
                        	THEN removed_plots_dates.max_due_date + INTERVAL '1 day'
                        ELSE c.start_date
                    END
                ) as start_date,
                COALESCE(
                    CASE 
                        WHEN removed_plots_dates.annex_action = 'removed' AND removed_plots_dates.max_due_date >= c.due_date THEN removed_plots_dates.min_start_date - INTERVAL '1 day'
                        WHEN added_plots_dates.annex_action = 'added' AND added_plots_dates.max_due_date >= c.due_date THEN added_plots_dates.max_due_date
                        else c.due_date
                    END
                ) as due_date,
                kvs.gid,
                kvs.kad_ident,
                kvs.ekate,
                ROUND((St_Area(view.geom)/1000)::numeric, 3)::numeric as area,
                ((to_date(view.due_date, 'DD.MM.YYYY') >= now()::date) OR (view.due_date IS NULL AND view.nm_usage_rights = 1)) as active_status,
                view.ngeom,
                view.geom,
                view.nm_usage_rights
            FROM 
                su_contracts as c
            INNER JOIN 
                su_contracts_plots_rel as cp ON cp.contract_id = c.id and annex_action = 'added'
            LEFT JOIN 
                added_plots_dates ON added_plots_dates.plot_id = cp.plot_id and added_plots_dates.parent_id = c.id
            LEFT JOIN 
                removed_plots_dates ON removed_plots_dates.plot_id = cp.plot_id and removed_plots_dates.parent_id = c.id  
            INNER JOIN 
                {$options['tablename']} as view ON view.contract_id = c.id and view.gid = cp.plot_id and view.is_annex= false
            INNER JOIN 
                layer_kvs as kvs ON kvs.gid = cp.plot_id    
            WHERE
                true
            GROUP BY 
                c.id, c.start_date, c.due_date, 
                kvs.kad_ident, kvs.ekate, kvs.gid, 
                view.geom, view.ngeom, view.due_date, view.nm_usage_rights, 
                added_plots_dates.annex_action, added_plots_dates.min_start_date, added_plots_dates.max_due_date, 
                removed_plots_dates.annex_action, removed_plots_dates.min_start_date, removed_plots_dates.max_due_date
        )
        SELECT 
            contracts.*,
            to_char(contracts.due_date,'DD.MM.YYYY') as due_date,
            to_char(contracts.start_date, 'DD.MM.YYYY') as start_date
        FROM contracts    
        WHERE
            true
            and (due_date > start_date or due_date is null and nm_usage_rights = 1)
        ";

        if ($options['where']['due_date']) {
            $sql .= ' AND (( due_date >= :due_date ) OR ( due_date IS NULL AND nm_usage_rights = 1 and start_date > :due_date))';
        }

        if ($options['where']['kad_ident']) {
            $sql .= ' AND kad_ident = :kad_ident';
        }

        if ($options['where']['gids']) {
            $gidValues = implode(', ', $options['where']['gids']);
            $sql .= ' AND gid IN (' . $gidValues . ')';
        }

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']['due_date']) {
            $cmd->bindParameter(':due_date', $options['where']['due_date']);
        }

        if ($options['where']['kad_ident']) {
            $cmd->bindParameter(':kad_ident', $options['where']['kad_ident']);
        }

        return $cmd->query()->readAll();
    }

    /**
     * @param string $tmpTable
     * @param string $ekate
     * @param array $columns - kay is the name of column in the tmp table, value is the name of column in the kvs table
     *
     * @throws TDbException
     *
     * @return int
     */
    public function updateKvsFromOszFile($tmpTable, $ekate, $columns)
    {
        $sql = 'UPDATE ' . $this->tableKVS . ' kvs SET ';

        foreach ($columns as $tmpColName => $kvsColName) {
            $sql .= $kvsColName . ' = COALESCE(tmp_kvs.' . $tmpColName . ', kvs.' . $kvsColName . '), ';
        }

        $sql = rtrim($sql, ', ');

        $sql .= ' FROM ' . $tmpTable . ' tmp_kvs  WHERE kvs.ekate = \'' . $ekate . '\' AND tmp_kvs.kad_no = kvs.kad_ident ';

        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->execute();
    }

    public function removeInactivePlotsKvs($tmpTable, $ekate)
    {
        $sql = "UPDATE {$this->tableKVS} SET is_edited = TRUE, edit_date = now(), edit_active_from = now() WHERE gid IN
                (SELECT DISTINCT kvs.gid FROM {$this->tableKVS} kvs
                LEFT JOIN {$this->contractsPlotsRelTable} cpr on cpr.plot_id = kvs.gid
                WHERE cpr.plot_id IS NULL AND kvs.kad_ident NOT IN (SELECT kad_no FROM {$tmpTable}) AND kvs.ekate = '{$ekate}')";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function reactivateOldPlotsKvs($tmpTable, $ekate)
    {
        $sql = "UPDATE {$this->tableKVS} SET is_edited = FALSE, edit_date = now(), edit_active_from = now() WHERE gid IN
                (SELECT DISTINCT kvs.gid FROM {$this->tableKVS} kvs
                LEFT JOIN {$this->contractsPlotsRelTable} cpr on cpr.plot_id = kvs.gid
                WHERE cpr.plot_id IS NULL AND kvs.kad_ident IN (SELECT kad_no FROM {$tmpTable}) AND kvs.ekate = '{$ekate}' AND kvs.is_edited = TRUE)";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function addKvsOszPlots($tmpTable)
    {
        $sql = "INSERT into {$this->tableKVS} (ekate, kad_ident, geom, masiv, number, category, area_type, document_area, mestnost, waiting_update, allowable_area, allowable_type) (
                    SELECT tmp.ekatte, tmp.kad_no, tmp.geom,(string_to_array(tmp.kad_no, '.'))[2] as masiv, (string_to_array(tmp.kad_no, '.'))[3] as number,
                    tmp.kategoria, tmp.kod_ntp, tmp.pl_dka, tmp.mestnost, true, tmp.allowable_area, tmp.allowable_type
                    FROM {$tmpTable} tmp
                    WHERE tmp.is_system = FALSE AND st_isvalid(geom)
                )";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    /**
     * This function removes all borders for the specified ekattes then
     * generates new using the layer_kvs table.
     * If there are no borders for any ekatte they will be created.
     */
    public function updateKvsBorders(array $ekattes)
    {
        if (!count($ekattes)) {
            return;
        }

        // Delete existing borders
        $this->deleteKvsBorders($ekattes, false);

        $options = [
            'tablename' => $this->tableKVS,
            'return' => [
                'geom' => 'get_geom_boundary(ST_Union(geom))',
                'border_type' => "'ekatte'::border_type_enum",
                'ekatte' => 'ekate as ekatte',
            ],
            'where' => [
                'ekate' => ['column' => 'ekate', 'compare' => 'IN', 'value' => $ekattes],
            ],
            'group' => 'ekatte',
        ];

        // Add ekatte borders from layer_kvs table
        $this->addKvsBordersFromTable($options);

        // Add masiv borders from layer_kvs table
        $options['return']['masiv'] = 'masiv';
        $options['return']['border_type'] = "'masiv'::border_type_enum";
        $options['group'] = 'ekatte, masiv';
        $this->addKvsBordersFromTable($options);

        $this->clusterKvsBordersTable();
    }

    public function deleteKvsBorders(array $ekattes, $clusterTable = true)
    {
        $this->deleteItemsByParams([
            'tablename' => $this->tableKVSBorders,
            'where' => [
                'ekatte' => ['column' => 'ekatte', 'compare' => 'IN', 'value' => $ekattes],
            ],
        ]);

        if ($clusterTable) {
            $this->clusterKvsBordersTable();
        }
    }

    public function clusterKvsBordersTable()
    {
        // This query will reorder the physical data by the specified index. This way it will improve the performance of the queries.
        $insertSql = "CLUSTER {$this->tableKVSBorders} USING {$this->tableKVSBordersClusterIdx}";
        $cmd = $this->DbModule->createCommand($insertSql);
        $cmd->execute();
    }

    public function addKvsBordersFromTable(array $tableOptions)
    {
        $requiredColumns = ['geom', 'border_type', 'ekatte'];
        $columns = array_keys($tableOptions['return'] ?? []);

        if (!empty(array_diff($requiredColumns, $columns))) {
            throw new Exception('Missing required columns in tableOptions. Required columns are: ' . implode(', ', $requiredColumns));
        }

        $insertColumns = implode(', ', $columns);
        $selectSql = $this->getItemsByParams($tableOptions, false, true);

        $insertSql = "INSERT INTO {$this->tableKVSBorders} ({$insertColumns}) {$selectSql}";
        $cmd = $this->DbModule->createCommand($insertSql);
        $cmd->execute();
    }

    public function updateKVSDocumentArea($tmpTable, $ekate)
    {
        $sql = "UPDATE {$this->tableKVS} set document_area = (st_area(geom)/1000)
            WHERE document_area is null
            AND ekate = '{$ekate}'";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function addInvalidKvsOszPlots($tmpTable)
    {
        $sql = "INSERT into {$tmpTable}_invalid (fr_gid, geom) (
                    SELECT
                        gid,
                        (CASE
                        WHEN st_geometrytype(st_makevalid(geom)) = 'ST_GeometryCollection' THEN
                            st_collectionextract(ST_MakeValid(geom), 3)
                        ELSE
                            geom
                        END) as geom
                    FROM {$tmpTable}
                    WHERE gid IN (SELECT gid FROM {$tmpTable} WHERE ST_IsValid(geom) = FALSE)
                )";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function addDumpedKvsOszPlots($tmpTable)
    {
        $sql = "INSERT into {$tmpTable}_invalid (fr_gid, geom) (
                    SELECT fr_gid,(st_dump(st_makevalid(geom))).geom FROM {$tmpTable}_invalid
                    WHERE st_geometrytype(st_makevalid(geom)) != 'ST_LineString'
                )";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function removeInvalidAndLineStringsKvsOszPlots($tmpTable, $field)
    {
        $sql = "DELETE FROM {$tmpTable} WHERE {$field} IN
                (SELECT {$field} FROM {$tmpTable}
                 WHERE not st_isvalid(geom) OR st_geometrytype(st_makevalid(geom)) = 'ST_LineString'
                )";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function deleteInvalidKvsOszPlots($tmpTable)
    {
        $sql = "DELETE FROM {$tmpTable}_invalid";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function createColumnIsSystem($tablename)
    {
        $sql = 'ALTER TABLE ' . $tablename . ' ADD COLUMN is_system BOOLEAN DEFAULT false';

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();

        return true;
    }

    public function updateColumnType(string $table, string $column, string $type)
    {
        $sql = "ALTER TABLE {$table} ALTER COLUMN {$column} TYPE {$type}";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function updateColumnIsSystem($tablename, $whereStatement, $returnOnlySQL)
    {
        $tpmTableChunk = explode('_', $tablename);
        $ekate = end($tpmTableChunk);

        $sql = "UPDATE
            {$tablename}
        SET
            is_system = CASE WHEN layer_kvs.kad_ident IS NULL THEN FALSE ELSE TRUE END
        FROM
            (SELECT DISTINCT kad_ident
            FROM layer_kvs
            WHERE ekate = '{$ekate}') AS layer_kvs
        WHERE
            layer_kvs.kad_ident = {$tablename}.kad_no;
        ";

        if ($whereStatement) {
            $sql = $this->createWhereSQL($sql, $whereStatement, $returnOnlySQL);
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        $cmd->execute();
    }

    public function getNewPlotsForHistoryLog($tablename, $ekate)
    {
        $sql = "SELECT kvs.gid from {$this->tableKVS} kvs WHERE gid IN
                (SELECT DISTINCT kvs.gid FROM {$this->tableKVS} kvs
                LEFT JOIN {$this->contractsPlotsRelTable} cpr on cpr.plot_id = kvs.gid
                WHERE cpr.plot_id IS NULL AND kvs.kad_ident NOT IN (SELECT kad_no FROM {$tablename}) AND kvs.ekate = '{$ekate}')";

        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function getInvalidKvsOszPlots($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM {$options['tmpTableInvalid']} i "
            . "INNER JOIN {$options['tmpTable']} tmp on tmp.gid = i.fr_gid";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function saveKvsOszInvalidPlotsChanges($rpcParam)
    {
        // Ako няма договор за този имот и го няма в в системното квс, го записваме в системното квс - layer_kvs
        $sql = "SELECT kvs.gid FROM {$this->tableKVS} kvs
                WHERE kvs.kad_ident = :kad_ident";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':kad_ident', $rpcParam['kad_no']);
        $resultKvs = $cmd->query()->readAll();

        $sql = "SELECT kvs.gid FROM {$this->tableKVS} kvs
                INNER JOIN {$this->contractsPlotsRelTable} cpr on cpr.plot_id = kvs.gid
                WHERE kvs.kad_ident = :kad_ident";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':kad_ident', $rpcParam['kad_no']);
        $resultContract = $cmd->query()->readAll();

        if (!strlen($resultKvs[0]['gid']) && !strlen($resultContract[0]['gid'])) {
            // Записване в системното квс - layer_kvs
            $sql = "INSERT INTO {$this->tableKVS} (geom,ekate,kad_ident,masiv,number,category,area_type,document_area)
                    VALUES (
                        ST_GeomFromText(:geom1, 32635),
                        :ekate,
                        :kad_ident,
                        :masiv,
                        :number,
                        :category,
                        :area_type,
                        round((St_Area(ST_GeomFromText(:geom2, 32635))/1000)::numeric, 3)
                    )";

            $aKadIdent = explode('.', $rpcParam['kad_no']);

            $masiv = ($aKadIdent[1] && isset($aKadIdent[1])) ? $aKadIdent[1] : '';
            $number = ($aKadIdent[2] && isset($aKadIdent[2])) ? $aKadIdent[2] : '';

            $cmd = $this->DbModule->createCommand($sql);
            $cmd->bindParameter(':geom1', $rpcParam['geometry']);
            $cmd->bindParameter(':geom2', $rpcParam['geometry']);
            $cmd->bindParameter(':ekate', $rpcParam['ekatte']);
            $cmd->bindParameter(':kad_ident', $rpcParam['kad_no']);
            $cmd->bindParameter(':masiv', $masiv);
            $cmd->bindParameter(':number', $number);
            $cmd->bindParameter(':category', $rpcParam['kategoria']);
            $cmd->bindParameter(':area_type', $rpcParam['kod_ntp']);
            $cmd->execute();
        }

        // Записваме и във временната таблица - layer_tmp_kvs_ЕКАТТЕ
        $tablename = 'layer_tmp_kvs_' . $rpcParam['ekatte'] . '';

        $sql = "INSERT INTO {$tablename} (geom,ekatte,kvs_no,kad_no,kategoria,kod_ntp,kod_sobstv,is_system,pl_dka)
                VALUES (
                    ST_GeomFromText(:geom1, 32635),
                    :ekatte,
                    :kvs_no,
                    :kad_no,
                    :kategoria,
                    :kod_ntp,
                    :kod_sobstv,
                    :is_system,
                    round((St_Area(ST_GeomFromText(:geom2, 32635))/1000)::numeric, 3)
                ) RETURNING gid";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':geom1', $rpcParam['geometry']);
        $cmd->bindParameter(':geom2', $rpcParam['geometry']);
        $cmd->bindParameter(':ekatte', $rpcParam['ekatte']);
        $cmd->bindParameter(':kvs_no', $rpcParam['kvs_no']);
        $cmd->bindParameter(':kad_no', $rpcParam['kad_no']);
        $cmd->bindParameter(':kategoria', $rpcParam['kategoria']);
        $cmd->bindParameter(':kod_ntp', $rpcParam['kod_ntp']);
        $cmd->bindParameter(':kod_sobstv', $rpcParam['kod_sobstv']);
        $cmd->bindParameter(':is_system', $rpcParam['is_system'], PDO::PARAM_BOOL);
        $cmd->execute();

        $recordID = 0;
        $recordID = $cmd->queryScalar();

        if ($recordID > 0) {
            $sql = "UPDATE {$tablename} set id_imot = :id_imot WHERE gid = :gid";

            $cmd = $this->DbModule->createCommand($sql);
            $cmd->bindParameter(':id_imot', $recordID);
            $cmd->bindParameter(':gid', $recordID);
            $cmd->execute();
        }

        return $recordID;
    }

    public function setDataTypeGeometry($tmpTable)
    {
        $sql = "ALTER TABLE {$tmpTable} ALTER COLUMN geom SET DATA TYPE geometry;";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function refreshTopicLayerKVSViews()
    {
        $sql = '
        REFRESH MATERIALIZED VIEW topic_layer_kvs_by_agreement_mat_view;
        REFRESH MATERIALIZED VIEW topic_layer_kvs_by_agreement_label_items;
        REFRESH MATERIALIZED VIEW topic_layer_kvs_by_category_mat_view;
        REFRESH MATERIALIZED VIEW topic_layer_kvs_by_category_label_items;
        REFRESH MATERIALIZED VIEW topic_layer_kvs_by_ntp_mat_view;
        REFRESH MATERIALIZED VIEW topic_layer_kvs_by_ntp_label_items;
        REFRESH MATERIALIZED VIEW topic_layer_kvs_by_owner_name_mat_view;
        REFRESH MATERIALIZED VIEW topic_layer_kvs_by_owner_name_label_items;
        REFRESH MATERIALIZED VIEW topic_layer_kvs_by_ownership_mat_view;
        REFRESH MATERIALIZED VIEW topic_layer_kvs_by_ownership_label_items;
        REFRESH MATERIALIZED VIEW topic_layer_kvs_by_tenant_name_mat_view;
        REFRESH MATERIALIZED VIEW topic_layer_kvs_by_tenant_name_label_items;
        ';
        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function getOszInfoForPlot($plotGid)
    {
        $plotInfoSql = "select kad_ident from layer_kvs where gid = {$plotGid};";

        $plotCmd = $this->DbModule->createCommand($plotInfoSql);

        $plotResults = $plotCmd->query()->readAll();

        $kadNo = $plotResults[0]['kad_ident'];

        $oszInfoSql = "SELECT
        DISTINCT(egn_subekt),
                kod_pr_osn,
                txt_pr_osn,
                max(DISTINCT (ime_subekt)::text) as ime_subekt
                FROM
                su_osz_files_plots
                WHERE
                kad_no = '{$kadNo}'
                AND file_id = (
                    SELECT
                    MAX (file_id)
                    FROM
                    su_osz_files_plots
                    WHERE
                    kad_no = '{$kadNo}'
                    )
                GROUP BY
                    egn_subekt,
                    kod_pr_osn,
                    txt_pr_osn
                ORDER BY txt_pr_osn";
        $oszCmd = $this->DbModule->createCommand($oszInfoSql);

        return $oszCmd->query()->readAll();
    }

    public function getMaxExtentForEkate($tablename, $ekate)
    {
        $sql = "SELECT ST_extent(geom) as extent FROM {$tablename} WHERE ekate = :ekate";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':ekate', $ekate);

        return $cmd->queryScalar();
    }

    public function getSoldPlotsAfterDate($start_date)
    {
        $sql = "SELECT plot_id
                    FROM {$this->tableSalesContracts} sc
                    LEFT JOIN {$this->salesContractsPlotsRelTable} scp on (sc.id = sales_contract_id)
                    WHERE start_date <= :start_date
                    AND plot_id IS NOT NULL";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':start_date', $start_date);
        $rawResults = $cmd->query()->readAll();

        $idsForReturn = [];
        foreach ($rawResults as $result) {
            $idsForReturn[] = $result['plot_id'];
        }

        return $idsForReturn;
    }

    /**
     * Insert plot from technofarm mobile(transoform srcProj(defaults to 3857 - google projection) to dstProj(defaults to 32635 - UTM)).
     *
     * @param array $data plot data
     */
    public function addPlotToLayerGps(array $data, int $srcProj = 3857, int $dstProj = 32635)
    {
        $sql = "INSERT INTO {$this->tableLayerGps} (geom,plot_name,plot_info)
                VALUES (ST_Multi(ST_MakeValid((ST_Dump(ST_Transform(ST_SetSRID(ST_GeomFromGeoJSON(:geom), :src_proj), :dst_proj))).geom)), :plot_name, :plot_info)";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':geom', $data['geom']);
        $cmd->bindParameter(':plot_name', $data['plot_name']);
        $cmd->bindParameter(':plot_info', $data['plot_info']);
        $cmd->bindParameter(':src_proj', $srcProj, PDO::PARAM_INT);
        $cmd->bindParameter(':dst_proj', $dstProj, PDO::PARAM_INT);
        $cmd->execute();
    }

    /**
     * Get all plots from database (transoform 32635(UTM) to 3857(google projection)).
     *
     * @param int $rows number of rows
     * @param string $sort sorted by
     * @param string $order kind of sort (ASC OR DESC)
     *
     * @return string all plots in format FeatureCollection
     */
    public function getGeoJsonFeatures($rows, $sort, $order)
    {
        $sql = "SELECT
                        row_to_json (fc)
                    FROM
                        (
                            SELECT
                                'FeatureCollection' AS TYPE,
                                array_to_json (ARRAY_AGG(f)) AS features
                            FROM
                                (
                                    SELECT
                                        'Feature' AS TYPE,
                                        ST_AsGeoJSON (ST_Transform(ST_SetSRID(geom, 32635), 3857)) :: json AS geometry,
                                        row_to_json (
                                            (
                                                SELECT
                                                    l
                                                FROM
                                                    (SELECT ST_Transform(ST_SetSRID(geom, 32635), 3857),
                                                             gid, plot_name, plot_info, round((ST_Area(geom)/1000)::numeric, 3) as plot_area) AS l
                                            )
                                        ) AS properties
                                    FROM
                                        {$this->tableLayerGps} AS lg
                                    ORDER BY {$sort} {$order} LIMIT {$rows}
                                ) AS f
                        ) AS fc;";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();

        return $cmd->query()->readAll();
    }

    /**
     * Create a View with name allowable_from_isak_["for isak" layer id].
     *
     * @param int $layerId for ISAK layer id
     */
    public function createAllowableFromIsakReportView($layerId, $databse)
    {
        $viewName = 'allowable_from_isak_' . $layerId;
        if ($this->getTableNameExist($databse, $viewName)) {
            return;
        }

        $sqlView = "DROP MATERIALIZED VIEW IF EXISTS \"{$viewName}\";
        CREATE MATERIALIZED VIEW \"{$viewName}\"
            AS
             SELECT a.geom
               FROM dblink('host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "'::text, 'SELECT
                geom
            FROM
                layer_allowable,
                (
                    SELECT
                        A [ 1 ]:: NUMERIC AS xmin,
                        A [ 2 ]:: NUMERIC AS ymin,
                        A [ 3 ]:: NUMERIC AS xmax,
                        A [ 4 ]:: NUMERIC AS ymax
                    FROM
                        (
                            SELECT
                                regexp_split_to_array(
                                    (
                                        SELECT
                                            extent
                                        FROM
                                            su_users_layers
                                        WHERE
                                            ID = {$layerId}
                                    ),
                                    '' ''
                                )
                        ) AS dt (A)
                ) AS extent
            WHERE
                geom && st_makeenvelope (
                    extent.xmin,
                    extent.ymin,
                    extent.xmax,
                    extent.ymax,
                    32635
                )'::text) a(geom geometry);";

        $cmd = $this->DbModule->createCommand($sqlView);
        $cmd->execute();

        $sqlOwner = "ALTER MATERIALIZED VIEW \"{$viewName}\" OWNER TO \"postgres\";";
        $cmd = $this->DbModule->createCommand($sqlOwner);
        $cmd->execute();

        $sqlIndex = "DROP INDEX IF EXISTS \"{$viewName}_gis\"; CREATE INDEX \"{$viewName}_gis\" ON \"public\".\"{$viewName}\" USING gist (geom \"public\".\"gist_geometry_ops_2d\");";
        $cmd = $this->DbModule->createCommand($sqlIndex);
        $cmd->execute();
    }

    public function equalizeContractAndPlotAreas()
    {
        $sql = 'UPDATE su_contracts_plots_rel cpr
        SET contract_area = (
            CASE
            WHEN kvs.document_area > 0 THEN
                kvs.document_area
            ELSE
                st_area (kvs.geom) / 1000
            END
            )
        FROM
            layer_kvs kvs
        LEFT JOIN su_contracts_plots_rel cpr1 ON (cpr1.plot_id = kvs.gid)
        LEFT JOIN su_contracts c ON (cpr1.contract_id = c.id)
        WHERE
            cpr.plot_id = kvs.gid
        AND c.from_sublease IS NULL
        AND round((CASE
            WHEN kvs.document_area > 0 THEN
                kvs.document_area
            ELSE
                st_area (kvs.geom) / 1000
            END - cpr.contract_area)::NUMERIC,3) <= 0.01
        AND round((CASE
            WHEN kvs.document_area > 0 THEN
                kvs.document_area
            ELSE
                st_area (kvs.geom) / 1000
            END - cpr.contract_area)::NUMERIC,3) >= -0.01
        AND CASE
            WHEN kvs.document_area > 0 THEN
                kvs.document_area
            ELSE
                st_area (kvs.geom) / 1000
            END - cpr.contract_area <> 0';

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function getSinglePlotPricePerContract($contract_id)
    {
        $sql = 'SELECT
            (C .contract_price / sum (cpr. contract_area)) AS plot_price
            FROM
                su_contracts C
            INNER JOIN su_contracts_plots_rel cpr ON (c.id = cpr.contract_id)
            WHERE c.id  = :contract
            GROUP BY c.id
       ';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':contract', $contract_id);

        return $cmd->queryScalar();
    }

    public function setSinglePricePerContract($contract_id, $single_price)
    {
        $sql = 'UPDATE su_contracts_plots_rel SET price_per_acre = :single_price WHERE contract_id = :contract_id';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':contract_id', $contract_id);
        $cmd->bindParameter(':single_price', $single_price);

        return $cmd->execute();
    }

    public function addFarmingAsOwnerForPcRelId($pc_rel_id, $farming_id)
    {
        $sql = 'INSERT INTO su_plots_farming_rel (pc_rel_id, farming_id, percent) VALUES (:pc_rel_id, :farming_id, 100)';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':pc_rel_id', $pc_rel_id);
        $cmd->bindParameter(':farming_id', $farming_id);
        $cmd->execute();
    }

    public function deleteDuplicatedRecordsFromOSZ($ekatte)
    {
        $sql = '
            DELETE FROM su_osz_files_plots
            WHERE id IN (SELECT id
                           FROM (SELECT id,
                                         ROW_NUMBER() OVER (PARTITION BY id_imot,kvs_no,kad_no,kod_subekt,txt_subekt,ime_subekt,egn_subekt,kod_pr_osn,txt_pr_osn,pl_dka,pl_dka_po,kategoria,kod_ntp,txt_ntp,kod_sobstv,txt_sobstv,kod_imot,txt_imot,ekatte,ver_no,data,vreme,file_id ORDER BY id) AS rnum
                                  FROM su_osz_files_plots) t
                           WHERE t.rnum > 1)
            AND ekatte = :ekatte;
        ';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':ekatte', $ekatte);
        $cmd->execute();
    }

    public function updateGeometryUniversal($tablename, $geometry, $id, $id_name = 'gid')
    {
        $sql = "UPDATE {$tablename} SET geom=ST_Multi(ST_SetSRID(ST_GeomFromText(:geometry),32635)) WHERE {$id_name} = :id";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':geometry', $geometry);
        $cmd->bindParameter(':id', $id, PDO::PARAM_INT);
        $cmd->execute();
    }

    public function refreshEkateCombobox()
    {
        $sql = 'REFRESH MATERIALIZED VIEW ekate_combobox';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function refreshOszEkateCombobox()
    {
        $sql = 'REFRESH MATERIALIZED VIEW osz_ekatte_combobox';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function refreshRentaViews()
    {
        $sql = '
            REFRESH MATERIALIZED VIEW charged_rentas_mat_view;
            REFRESH MATERIALIZED VIEW charged_rentas_annexes_mat_view;
            REFRESH MATERIALIZED VIEW renta_nats_mat_view;
        ';

        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function getEkateAreaForCPRID($ids)
    {
        $ids = array_map('intval', $ids);
        $sql = '
            SELECT
                round(
                    SUM (
                        CASE
                        WHEN document_area IS NULL THEN
                            ST_AREA (geom) / 1000
                        ELSE
                            document_area
                        END
                    ) :: NUMERIC,
                    3
                ) AS document_area,
                round(
                    (SUM(st_area(geom)) / 1000) :: NUMERIC,
                    3
                ) AS geom_area
            FROM
                layer_kvs kvs
            WHERE
                ekate IN (
                    SELECT DISTINCT
                        (ekate)
                    FROM
                        su_contracts_plots_rel cpr
                    LEFT JOIN layer_kvs kvs ON kvs.gid = cpr.plot_id
                    WHERE
                        cpr. ID IN (';
        for ($i = 0; $i < count($ids); $i++) {
            $sql .= ':cpr_id' . $i;
            if ($i < count($ids) - 1) {
                $sql .= ', ';
            }
        }
        $sql .= ')
                )
        ';

        $cmd = $this->DbModule->createCommand($sql);
        for ($i = 0; $i < count($ids); $i++) {
            $cmd->bindParameter(':cpr_id' . $i, $ids[$i]);
        }

        return $cmd->query()->readAll();
    }

    public function deleteTmpKvsTable($id)
    {
        $id = intval($id);
        $tmp_table_name = 'tmp_kvs_' . $id;
        $sql = "DROP TABLE IF EXISTS {$tmp_table_name}";

        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->execute();
    }

    public function disableRentaMatViewTriggers()
    {
        $sql = 'CREATE OR REPLACE FUNCTION "public"."refresh_rentas_materialized_views"()
                  RETURNS "pg_catalog"."trigger" AS $BODY$
                  BEGIN
                        --REFRESH MATERIALIZED VIEW charged_rentas_mat_view;
                        --REFRESH MATERIALIZED VIEW charged_rentas_annexes_mat_view;
                        --REFRESH MATERIALIZED VIEW renta_nats_mat_view;
                        RETURN NUll;
                  END
                    $BODY$
              LANGUAGE \'plpgsql\' VOLATILE COST 100;';
        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->execute();
    }

    public function enableRentaMatViewTriggers()
    {
        $sql = 'CREATE OR REPLACE FUNCTION "public"."refresh_rentas_materialized_views"()
                  RETURNS "pg_catalog"."trigger" AS $BODY$
                  BEGIN
                        REFRESH MATERIALIZED VIEW charged_rentas_mat_view;
                        REFRESH MATERIALIZED VIEW charged_rentas_annexes_mat_view;
                        REFRESH MATERIALIZED VIEW renta_nats_mat_view;
                        RETURN NUll;
                  END
                    $BODY$
              LANGUAGE \'plpgsql\' VOLATILE COST 100;';
        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->execute();
    }

    public function intersectKvsWithCustomTable($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = "SELECT {$return} FROM {$this->tableKVS} kvs, {$options['joinTable']} a
        WHERE st_intersects(kvs.geom, a.geom)";

        if ($options['minimalIntersection']) {
            $sql .= ' AND st_area(st_intersection(st_setsrid(kvs.geom, 32635), st_setsrid(a.geom, 32635))) > :minimalIntersection';
        }
        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        if ($options['minimalIntersection']) {
            $cmd->bindParameter(':minimalIntersection', $options['minimalIntersection']);
        }

        return $cmd->query()->readAll();
    }

    public function copyDataFromTMPWorkTable($tablename, $tmp_tablename)
    {
        $createMissingDefaultColumns = false;
        if (0 !== strpos($tmp_tablename, 'tmp_geom_')) {
            // Create layer_work_ table
            $sql = "CREATE TABLE {$tablename} AS TABLE {$tmp_tablename};";
            $cmd = $this->DbModule->createCommand($sql);
            $cmd->execute();

            $sqlTransform = "ALTER TABLE {$tablename} ALTER COLUMN geom TYPE Geometry(Geometry, 32635) USING ST_Transform(geom, 32635);";
            $cmd = $this->DbModule->createCommand($sqlTransform);
            $cmd->execute();

            $createMissingDefaultColumns = true;
        } else {
            $sql = "INSERT INTO {$tablename} (geom, plot_name) SELECT ST_Transform(geom, 32635) as geom, plot_name FROM {$tmp_tablename}";
            $cmd = $this->DbModule->createCommand($sql);
            $cmd->execute();
        }

        if ($createMissingDefaultColumns) {
            $defaultDefinitions = UserLayers::getDefinitionsByType(Config::LAYER_TYPE_WORK_LAYER);

            foreach ($defaultDefinitions as $def) {
                $colType = $GLOBALS['Layers']['columnTypesByCategory'][$def['col_category']] ?? 'VARCHAR';
                $colName = $def['col_name'];

                if ($def['col_virtual']) {
                    $colType = $colType . ' GENERATED ALWAYS AS (' . $def['col_expression'] . ') STORED';
                }

                $this->addColumn($tablename, $colName, $colType);
            }
        }
    }

    public function transformColumnGeometryProjection($tablename, $projection)
    {
        $sql = "update {$tablename} set geom = st_transform(geom, {$projection})";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function transformColumnToGeometry($tablename)
    {
        $sql = "ALTER TABLE {$tablename} ALTER COLUMN geom TYPE geometry(Geometry,0);";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function wktToGeoJSON(string $wkt): string
    {
        $sql = "SELECT 
            ST_AsGeoJSON(
                ST_GeomFromText('{$wkt}')
            ) AS geojson";

        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->queryScalar();
    }

    public function addPrimaryKeyAndGidSequence($tablename)
    {
        $tableSeq = $tablename . '_gid_seq';
        $tableGid = $tablename . '.gid';
        $sql = "
            ALTER TABLE {$tablename} add PRIMARY key(\"gid\");
            ALTER TABLE {$tablename} OWNER TO postgres;

            CREATE SEQUENCE {$tableSeq}
                START WITH 1
                INCREMENT BY 1
                NO MINVALUE
                NO MAXVALUE
                CACHE 1;

            ALTER TABLE {$tablename} OWNER TO postgres;
            ALTER TABLE {$tablename}
            ALTER COLUMN \"gid\" SET DEFAULT nextval('{$tableSeq}'::regclass);
            ALTER SEQUENCE {$tableSeq} OWNED BY {$tableGid};
            SELECT SETVAL('public.{$tableSeq}', COALESCE(MAX(gid), 1) ) FROM {$tablename};
        ";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    /**
     * @param $contract_ids
     *
     * @throws TDbException
     */
    public function updateAreaForRentToAllowableArea($contractIds)
    {
        if (!$contractIds) {
            return;
        }

        $sql = "UPDATE {$this->contractsPlotsRelTable}
        SET area_for_rent = subquery.update_area
        FROM
            (
                SELECT
                    cp_rel. ID AS cp_rel_id,
                    contract_id,
                    plot_id,
                    gid,
                    allowable_area,
                    contract_area,
                    (CASE WHEN allowable_area > contract_area THEN contract_area ELSE allowable_area END) as update_area
                FROM
                    {$this->contractsPlotsRelTable} cp_rel
                JOIN {$this->tableKVS} kvs ON (cp_rel.plot_id = kvs.gid)
                WHERE
                    contract_id IN ({$contractIds})
            ) subquery
        WHERE subquery.cp_rel_id = {$this->contractsPlotsRelTable}. ID ";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    /**
     * @throws TDbException
     */
    public function updateAreaForRentToContractArea($contractIds)
    {
        if (!$contractIds) {
            return;
        }

        $sql = "UPDATE {$this->contractsPlotsRelTable}
        SET area_for_rent = subquery.update_area
        FROM
            (
                SELECT
                    cp_rel. ID AS cp_rel_id,
                    contract_id,
                    plot_id,
                    gid,
                    allowable_area,
                    contract_area,
                    contract_area as update_area
                FROM
                    {$this->contractsPlotsRelTable} cp_rel
                JOIN {$this->tableKVS} kvs ON (cp_rel.plot_id = kvs.gid)
                WHERE
                    contract_id IN ({$contractIds})
            ) subquery
        WHERE subquery.cp_rel_id = {$this->contractsPlotsRelTable}. ID ";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    /**
     * @throws TDbException
     */
    public function updateAreaForRentToArableArea($contractIds)
    {
        if (!$contractIds) {
            return;
        }

        $sql = "UPDATE {$this->contractsPlotsRelTable}
        SET 
            area_for_rent = subquery.update_area
        FROM
            (
                SELECT
                    cp_rel. ID AS cp_rel_id,
                    (case when coalesce(cp_rel.kvs_allowable_area, kvs.allowable_area, 0) > cp_rel.contract_area then cp_rel.contract_area else coalesce(cp_rel.kvs_allowable_area, kvs.allowable_area, 0) end) as update_area
                FROM
                    {$this->contractsPlotsRelTable} cp_rel
                    left join {$this->tableKVS} kvs on kvs.gid = cp_rel.plot_id
                WHERE
                    contract_id IN ({$contractIds})
            ) subquery
        WHERE subquery.cp_rel_id = {$this->contractsPlotsRelTable}.id ";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function checkIfIndexExists($database, $indexName)
    {
        $sql = 'SELECT 1 FROM pg_indexes where indexname like :index_name';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':index_name', $indexName);

        return $cmd->queryScalar();
    }

    /**
     * @throws TDbException
     *
     * @return array|false
     */
    public function getGeoJSON($options, $includeLineFeature, $offset, $bindingParams = [])
    {
        if ($options['tablename']) {
            $dumpGeomCol = $options['keep_multipolygons'] ? 'ST_Transform(lg.geom, 4326)' : 'dump.geom';
            $dumpGeomFrom = $options['keep_multipolygons'] ? '' : ',ST_Dump(ST_Transform(lg.geom, 4326)) dump';
            $mainQueryFeature = "SELECT
                                'Feature'                                                                    AS type,
                                row_to_json((SELECT l
                                        FROM (SELECT " . implode(',', $options['return']) . " ) AS l
                                            )) AS properties,
                                (CASE WHEN ST_GeometryType(lg.geom)='ST_MultiPolygon' THEN ST_AsGeoJSON({$dumpGeomCol}, 15, 4) :: JSON ELSE
                                CASE WHEN ST_GeometryType(lg.geom)='ST_Polygon' THEN ST_AsGeoJSON(ST_Transform(lg.geom, 4326), 15, 4) :: JSON ELSE NULL END
                                END) AS geometry
                            FROM 
                                {$options['tablename']} AS lg {$dumpGeomFrom}

        ";

            $offset = is_numeric($offset) ? $offset : 0;
            $mainQueryLineFeature = $this->getLineFeatureSql($options, $offset);

            if (!empty($options['where']['id'])) {
                $mainQueryFeature .= ' WHERE ' . $options['where']['id']['column'] . ' ' . $options['where']['id']['compare'] . ' (' . implode(
                    ',',
                    $options['where']['id']['value']
                ) . ')';
                $mainQueryLineFeature .= ' WHERE ' . $options['where']['id']['column'] . ' ' . $options['where']['id']['compare'] . ' (' . implode(
                    ',',
                    $options['where']['id']['value']
                ) . ')';
            }
        }

        if (!$options['tablename']) {
            $dumpGeomCol = $options['keep_multipolygons'] ? 'ST_Transform(geomData, 4326)' : 'dump.geom';
            $dumpGeomFrom = $options['keep_multipolygons'] ? '' : ',ST_Dump(ST_Transform(geomData, 4326)) dump';

            $mainQueryFeature = "SELECT
                                    'Feature' AS type,
                                    (CASE WHEN ST_GeometryType(geomData)='ST_MultiPolygon' THEN ST_AsGeoJSON({$dumpGeomCol}, 15, 4) :: JSON ELSE
                                    CASE WHEN ST_GeometryType(geomData)='ST_Polygon' THEN ST_AsGeoJSON(ST_Transform(geomData, 4326), 15, 4) :: JSON ELSE NULL END
                                    END) AS geometry
                                FROM 
                                    {$options['return']} AS geomData {$dumpGeomFrom}
            ";
        }

        $mainQuery = '';
        $mainQuery .= $mainQueryFeature;

        if ($includeLineFeature) {
            $mainQuery .= ' UNION ALL ';
            $mainQuery .= $mainQueryLineFeature;
        }

        $sql = "
            SELECT 
                row_to_json(fc) as \"geoJSON\"
            FROM (SELECT
                    'FeatureCollection' AS type,
                    array_to_json(array_agg(f)
                ) AS features
            FROM ({$mainQuery}) AS f) AS fc;
        ";

        $cmd = $this->DbModule->createCommand($sql);

        if (count($bindingParams)) {
            foreach ($bindingParams as $key => $data) {
                $cmd->bindParameter($key, $data);
            }
        }

        $result = $cmd->query()->read();
        $cmd->query()->close();

        return $result;
    }

    public function getLineFeatureSql($options, $offset)
    {
        $table = $options['tablename'];

        return "
            SELECT
                'LineFeature' AS type,
                row_to_json((SELECT l
                                FROM (SELECT " . implode(',', $options['return']) . " ) AS l
                            ))  AS properties,
                (ST_AsGeoJSON(ST_Transform(ST_OffsetCurve(
                    ST_SnapToGrid(ST_ExteriorRing((ST_Dump(geom)).geom),0.003),
                    {$offset}, 'quad_segs=10 join=mitre mitre_limit=10'), 4326), 15, 4
                )) :: JSON  AS geometry
            FROM {$table} AS lg
        ";
    }

    /**
     * @throws TDbException
     *
     * @return array|false
     */
    public function getLineFeatureGeoJson($options, $offset)
    {
        $lineFeatureSql = $this->getLineFeatureSql($options, $offset);

        if (!empty($options['where']['id'])) {
            $lineFeatureSql .= ' WHERE ' . $options['where']['id']['column'] . ' ' . $options['where']['id']['compare'] . ' (' . implode(
                ',',
                $options['where']['id']['value']
            ) . ')';
        }

        $sql = "
            SELECT 
                row_to_json(fc) as \"geoJSON\"
            FROM (SELECT
                    'FeatureCollection' AS type,
                    array_to_json(array_agg(f)
                ) AS features
            FROM ({$lineFeatureSql}) AS f) AS fc;
        ";

        $cmd = $this->DbModule->createCommand($sql);
        $result = $cmd->query()->read();
        $cmd->query()->close();

        return $result;
    }

    public function getKvsMapFileQuery()
    {
        $sql = "SELECT gid, geom, ekate, label
            FROM {$this->tableKVS}
            WHERE true AND (CASE WHEN '%ekatte%' = 'null' THEN TRUE ELSE ekate = ANY('{%ekatte%}'::text[]) END) AND (is_edited = FALSE AND (edit_active_from <= now() OR edit_active_from IS NULL)) OR (is_edited = TRUE AND edit_active_from > now())";

        return "({$sql}) as subquery using unique gid using srid=32635";
    }

    public function getEkatteCombobox()
    {
        $sql = 'SELECT * FROM ekate_combobox';
        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    /**
     * @throws TDbException
     *
     * @return array
     */
    public function contractsForEkatte($ekatte)
    {
        $cmd = $this->DbModule->createCommand('SELECT k.ekate, c.plot_id, c.contract_id FROM ' . $this->tableKVS . ' AS k INNER JOIN ' . $this->contractsPlotsRelTable . ' AS c ON c.plot_id = k.gid WHERE k.ekate = :ekatte');
        $cmd->bindParameter(':ekatte', $ekatte);

        return $cmd->query()->readAll();
    }

    /**
     * @throws TDbException
     *
     * @return int
     */
    public function deleteEkatte($ekatte)
    {
        $cmd = $this->DbModule->createCommand('DELETE FROM ' . $this->tableKVS . ' WHERE ekate = :ekatte');
        $cmd->bindParameter(':ekatte', $ekatte);

        $this->deleteKvsBorders([$ekatte]);

        return $cmd->execute();
    }

    /**
     * @return TDbTransaction
     */
    public function startTransaction()
    {
        return $this->DbModule->beginTransaction();
    }

    /**
     * @param string $tablename
     *
     * @throws TDbException
     *
     * @return array
     */
    public function getTableColumnsTypes($tablename)
    {
        $sql = "select column_name,data_type from information_schema.columns where table_name = '" . $tablename . "';";
        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    /**
     * @throws TDbException
     *
     * @return array|false
     */
    public function getAdaptTFPluginFieldBoundaries($options)
    {
        $table = $options['tablename'];
        list($gid, $name) = $options['return'];
        $sqlCTE = "SELECT
                    (SELECT {$name}) as \"NAME\", -- use this in case the name variable contains an alias (e.g. 'case when ... end as area_name') 
                    (SELECT {$gid}) as \"ID\", -- use this in case the gid variable contains alias
                    CASE ST_GeometryType(lg.geom)
                    WHEN 'ST_MultiPolygon' THEN dump.geom
                    WHEN 'ST_Polygon' THEN ST_Transform(lg.geom, 4326)
                    END AS geom
                    FROM
                        {$table} AS lg,
                        ST_Dump(ST_Transform(lg.geom, 4326)) dump";
        if (!empty($options['where']['id'])) {
            $sqlCTE .= ' WHERE ' . $options['where']['id']['column'] . ' ' . $options['where']['id']['compare'] . ' (' . implode(
                ',',
                $options['where']['id']['value']
            ) . ')';
        }

        $sql = "WITH spatial_data AS ({$sqlCTE})
            SELECT
                row_to_json(fc) as \"data\"
            FROM
                (
                SELECT
                    array_to_json(array_agg(f.record)) AS \"FieldBoundaries\"
                FROM
                    (
                        SELECT
                            json_build_object(
                                'FieldId', \"ID\",
                                'Name', \"NAME\",
                                'SpatialData', ST_AsGeoJSON(ST_Force3D(st_multi(sd.geom)), 15, 4)::json
                            ) record
                        FROM
                            spatial_data sd
                    ) AS f
                ) AS fc;

        ";

        $cmd = $this->DbModule->createCommand($sql);
        $result = $cmd->query()->read();
        $cmd->query()->close();

        return $result;
    }

    /**
     * @throws TDbException
     *
     * @return array|false
     */
    public function getAdaptTFPluginGuidanceInfo($options)
    {
        $table = $options['tablename'];
        $type = $options['type'];
        list($gid, $name) = $options['return'];

        $sqlCTE = "SELECT
                    array_agg((SELECT {$name})) as \"NAME\" ,
                    (SELECT {$gid}) as gid, -- use this in case the gid variable contains alias
                    (SELECT {$name}) as name, -- use this in case the name variable contains an alias (e.g. 'case when ... end as area_name')
                    lg.plot_id as plot_id,
                    array_agg(ST_Transform(lg.geom, 4326)) as geom
                    FROM
                        {$table} AS lg,
                        ST_Dump(ST_Transform(lg.geom, 4326)) dump";
        if (!empty($options['where']['id'])) {
            $sqlCTE .= ' WHERE '
                . $options['where']['id']['column'] . ' '
                . $options['where']['id']['compare'] . ' (' . implode(',', $options['where']['id']['value']) . ')';
        }

        $sqlCTE .= ' GROUP BY lg.plot_id, lg.name, lg.gid';

        $sql = "WITH spatial_data AS ({$sqlCTE})
            SELECT
                row_to_json(fc) as \"data\"
            FROM
                (
                SELECT
                    array_to_json(array_agg(f.record)) AS \"Guidance\"
                FROM
                    (
                        SELECT
                            json_build_object(
                                'Name', CONCAT('field_name', '_', sd.plot_id),
                                'Id', \"plot_id\",
                                'Patterns', array_to_json(array_agg(json_build_object(                                       
                                        'Name', sd.name,
                                        'Id', sd.gid,
                                        'type', '{$type}',
                                        'Shape',  sd.geom                                    
                                )))
                            ) record
                        FROM
                            spatial_data sd
                        GROUP BY
                            sd.plot_id  
                    ) AS f
                ) AS fc;
        ";

        $cmd = $this->DbModule->createCommand($sql);
        $result = $cmd->query()->read();
        $cmd->query()->close();

        $data = json_decode($result['data'], true);

        foreach ($data['Guidance'] as &$record) {
            foreach ($record['Patterns'] as &$rec) {
                $rec['Shape'] = $rec['Shape'][0]['coordinates'];
            }
        }

        $result['data'] = json_encode($data);

        return $result;
    }

    /**
     * @throws TDbException
     *
     * @return array|false
     */
    public function getAdaptTFPluginFieldsInfo($options, $farmName, $userName)
    {
        $table = $options['tablename'];
        list($gid, $name) = $options['return'];

        // $guidanceIds are used as relation to AB lines
        $guidanceIds = null;
        if (!empty($options['where']['id'])) {
            $guidanceIds = implode(',', $options['where']['id']['value']);
        }

        $sqlCTE = "SELECT
                (SELECT {$name}) as  \"NAME\"
                , (SELECT {$gid}) as \"ID\"
                , json_build_object('Name', '{$userName}', 'Id', -1) AS \"Grower\"
                , json_build_object('Name', '{$farmName}', 'Id', -1) AS \"Farm\"
                , json_build_array((SELECT {$gid})) AS \"GuidanceGroupsIds\"
                , 'vrReportedFieldArea' AS \"Code\"
                , '' AS \"Description\"
                , (st_area(geom) * 0.001)::decimal(10, 3) AS \"Value\"
            FROM
                {$table}";

        if (!empty($options['where']['id'])) {
            $sqlCTE .= ' WHERE ' . $options['where']['id']['column'] . ' ' . $options['where']['id']['compare'] . ' (' . implode(
                ',',
                $options['where']['id']['value']
            ) . ')';
        }

        $sql = "WITH fields AS ({$sqlCTE})
                SELECT
                    json_build_object('Fields', json_agg(f)) AS \"data\"
                FROM
                    fields f";

        $cmd = $this->DbModule->createCommand($sql);
        $result = $cmd->query()->read();
        $cmd->query()->close();

        return $result;
    }

    /**
     * @param string $tableName
     *
     * @throws TDbException
     *
     * @return array
     */
    public function getTableInfo($tableName)
    {
        $sql = "SELECT * 
                FROM information_schema.columns 
                WHERE 
                    table_schema = 'public' 
                    AND table_name = :tableName";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':tableName', $tableName);

        return $cmd->query()->readAll();
    }

    public function transformGeom($geom, $fromCrs, $toCrs)
    {
        $sql = "SELECT ST_AsText(ST_Transform(ST_GeomFromText('{$geom}',{$fromCrs}),{$toCrs})) as geom";
        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function getChargedRenta($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);
        $sql = "SELECT
        scrp.*
        , STRING_AGG(DISTINCT CONCAT(scrnp.nat_type , 'x' , scrnp.amount , 'x ' , scrnp.price_per_unit) , ',') AS renta_type_amount
        , scg.name as c_group_name
        FROM su_charged_renta_params scrp
        LEFT JOIN su_charged_renta_natura_params scrnp ON scrp.id = scrnp.params_id
        LEFT JOIN su_charged_renta_history scrh ON scrh.params_id = scrp.id
        LEFT JOIN su_owners so ON so.id = scrh.owner_id
            AND so.owner_type = 1
        LEFT JOIN su_owners so_c ON so_c.id = scrh.owner_id
            AND so_c.owner_type = 0
        LEFT JOIN su_contracts c ON c.id = scrh.contract_id
        LEFT JOIN layer_kvs kvs ON kvs.kad_ident = scrh.kad_ident
        LEFT JOIN su_contract_group scg ON scg.id = scrp.c_group
        ";

        if (!empty($options['where']['rep_egn']['value'])) {
            $sql .= ' LEFT JOIN su_owners_reps sor ON sor.id = (case when length(scrp.rep_id) > 0 then scrp.rep_id::numeric else 0 end)';
        }

        $sql .= ' WHERE true ';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        // The query is too heavy. Commented until the query is made lighter
        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($counter) {
            $sql = "SELECT
                        count(*)
                    FROM ({$sql}) as crp
            ";
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    /**
     * Updates multiple rows in the specified table.
     *
     * @param string $tableName The table to update
     * @param array $arrForUpdate Array of associative array where the key is the column name and the value - the new
     *                            value of the column. Must contain $keyName key.
     * @param string $keyName The name of the column that specifies which rows will be updated
     * @param array $columnsToUpdate Array that represents the names of the columns that will be updated
     * @param array $columnCasts Array that describes the columns explicit type casting (if needed)
     */
    public function multiUpdate(string $tableName, array $arrForUpdate, string $keyName, array $columnsToUpdate, array $columnCasts = []): array
    {
        // Convert object to array
        $arrForUpdate = (array)$arrForUpdate;
        $arrForUpdate = array_map(function ($arrValue) {
            return (array)$arrValue;
        }, $arrForUpdate);

        $columnsCount = count($columnsToUpdate);
        $valuesCount = count($arrForUpdate);

        if (!$valuesCount || !$columnsCount) {
            return [];
        }

        $valuesQuery = '';
        foreach ($arrForUpdate as $valueIndex => $value) {
            if (!$value[$keyName]) {
                throw new Exception("Column '{$keyName}' is not set!");
            }

            $valueQuery = '(' . $value[$keyName] . ', ';

            foreach ($columnsToUpdate as $columnIndex => $column) {
                if (isset($columnCasts[$column])) {
                    $columnType = $columnCasts[$column];
                    $value[$column] = 'varchar' == $columnType
                        ? "'" . $value[$column] . "'"
                        : "'" . $value[$column] . "'::{$columnType}";
                }

                if ($columnIndex != $columnsCount - 1) {
                    $valueQuery .= $value[$column] . ', ';

                    continue;
                }

                $valueQuery .= $value[$column] . ')';
            }

            $valuesQuery .= $valueQuery;
            if ($valueIndex != $valuesCount - 1) {
                $valuesQuery .= ', ';
            }
        }

        $setQuery = '';
        foreach ($columnsToUpdate as $columnIndex => $column) {
            $setQuery .= "\"{$column}\" = c.\"{$column}\"";

            if ($columnIndex != $columnsCount - 1) {
                $setQuery .= ', ';

                continue;
            }
        }

        $columnsToUpdateStr = implode('", "', $columnsToUpdate);
        $columnsToReturnStr = implode(', t.', array_merge([$keyName], $columnsToUpdate));

        $sql = "UPDATE {$tableName} as t SET {$setQuery}";
        $sql .= " FROM (VALUES {$valuesQuery}) as c(\"{$keyName}\", \"{$columnsToUpdateStr}\")";
        $sql .= " WHERE c.{$keyName} = t.{$keyName}";
        $sql .= " RETURNING t.{$columnsToReturnStr}";

        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function getPlotGeomInvalidContracts(DateTime $editActiveFrom, string $kadIdent)
    {
        $sql = '
            SELECT 
                c.id as contract_id
            FROM 
                public.su_contracts_plots_rel as scpr
            inner join layer_kvs as kvs on kvs.gid = scpr.plot_id 
            inner join su_contracts as c on c.id = scpr.contract_id
            WHERE
                (c.due_date > :edit_active_from or c.due_date is null)
            and     
                kvs.kad_ident =:kad_ident
            and 
                scpr.annex_action =:annex_action
            and
                c.is_sublease =:is_sublease
        ';

        $annexAction = 'added';
        $isSublease = 'false';

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':edit_active_from', $editActiveFrom->format('Y-m-d H:i:s'));
        $cmd->bindParameter(':kad_ident', $kadIdent);
        $cmd->bindParameter(':annex_action', $annexAction);
        $cmd->bindParameter(':is_sublease', $isSublease);

        return $cmd->query()->readAll();
    }

    /**
     * Copy items from one layer to another.
     *
     * @param UserLayers $srcLayer Source layer
     * @param UserLayers $dstLayer Destination layer
     * @param array $gidsOrData An array containing data to copy from the source layer.
     *                          - When $copyFromSrcTable is true, this array should contain the GIDs of the source layer items to copy.
     *                          - When $copyFromSrcTable is false, this array should contain the data to copy.
     * @param bool $mergeNeighbourFeatures Whether to merge neighbour features
     * @param bool $copyFromSrcTable Whether to copy from the source table or from $data
     *
     * @throws TDbException
     */
    public function copyLayerItems(UserLayers $srcLayer, UserLayers $dstLayer, array $gidsOrData, bool $mergeNeighbourFeatures = false, bool $copyFromSrcTable = true)
    {
        $dstTable = $dstLayer->table_name;

        /** @var array $srcLayerDefinitions Definitions of the copyable columns from the srouce layer */
        $filter = [['col_copyable' => true, 'col_virtual' => false]];

        /** @var array $srcLayerDefinitions Definitions of the src layer */
        $srcLayerDefinitions = $srcLayer->getDefinitions();
        $srcLayerDefinitions = UserLayers::filterDefinitions($srcLayerDefinitions, $filter);

        /** @var array $dstLayerDefinitions Definitions of the destination layer */
        $dstLayerDefinitions = $dstLayer->getDefinitions();
        $dstLayerDefinitions = UserLayers::filterDefinitions($dstLayerDefinitions, $filter);

        $fromExpr = $copyFromSrcTable
            ? $srcLayer->table_name
            : UserLayers::generateValuesExprByDefinitions($gidsOrData, $srcLayerDefinitions);

        /** @var array $columnsMap Array where the keys are the columns from srcLayer and the values are the matched columns from dstLayer */
        $columnsMap = UserLayers::matchColumns($srcLayerDefinitions, $dstLayerDefinitions);

        $srcTableColumns = array_keys($columnsMap);

        $gidValues = $copyFromSrcTable ? implode(', ', array_map('intval', $gidsOrData)) : [];
        $gidDefinition = $srcLayer->getDefinitionByCategory(Config::LAYER_COLUMN_CATEGORY_GID);
        $gidColumn = $gidDefinition['col_name'];

        if (!$gidColumn) {
            throw new Exception('GID definition not found in source layer');
        }

        if ($mergeNeighbourFeatures) {
            $srcTableColumnsStr = '';
            $cte = '';
            $geomColumn = '';
            foreach ($srcLayerDefinitions as $columnDef) {
                $colName = $columnDef['col_name'];
                $colCategory = $columnDef['col_category'];

                if (!array_key_exists($colName, $columnsMap)) {
                    continue;
                }

                if (Config::LAYER_COLUMN_CATEGORY_GEOM === $columnDef['col_category']) {
                    $geomColumn = $columnDef['col_name'];
                    $cte = "WITH grouped_geom AS (SELECT (st_dump(st_union({$columnDef['col_name']}))).{$columnDef['col_name']} AS new_geom FROM {$fromExpr}";
                    $cte .= count($gidsOrData) ? " WHERE {$gidColumn} IN ({$gidValues}))" : ')';

                    $srcTableColumnsStr .= 'gg.new_geom as geom,';

                    continue;
                }

                $isGeneric = in_array($colCategory, $GLOBALS['Layers']['genericColumnCategories']);
                if ($isGeneric || Config::LAYER_COLUMN_CATEGORY_NAME === $columnDef['col_category']) {
                    if (in_array($colCategory, [Config::LAYER_COLUMN_CATEGORY_NUMBER, Config::LAYER_COLUMN_CATEGORY_DATE])) {
                        $srcTableColumnsStr .= "CASE 
                                                    WHEN COUNT(DISTINCT {$colName}) = 1 
                                                    THEN MAX({$colName}) 
                                                    ELSE NULL 
                                                END AS {$colName}, ";
                    } elseif (Config::LAYER_COLUMN_CATEGORY_BOOLEAN === $colCategory) {
                        $srcTableColumnsStr .= "BOOL_OR({$colName}) AS {$colName}, ";
                    } else {
                        $maxLength = $this->columnLenght($dstTable, $columnsMap[$colName]);
                        $srcTableColumnsStr .= "LEFT(string_agg(DISTINCT {$colName}, ', '), {$maxLength}) AS {$colName}, ";
                    }
                } else {
                    $srcTableColumnsStr .= "CASE WHEN COUNT(DISTINCT {$colName}) = 1 THEN MAX({$colName}) ELSE NULL END AS {$colName}, ";
                }
            }
            $srcTableColumnsStr = rtrim($srcTableColumnsStr, ', ');
        } else {
            $srcColName = UserLayers::filterDefinitions($srcLayerDefinitions, [['col_category' => Config::LAYER_COLUMN_CATEGORY_NAME]])[0];
            $maxLength = $this->columnLenght($dstTable, $columnsMap[$srcColName['col_name']]);
            $srcTableColumnsStr = '"' . implode('", "', $srcTableColumns) . '"';
            $srcTableColumnsStr = str_replace("\"{$srcColName['col_name']}\"", "substring({$srcColName['col_name']}::text from 1 for {$maxLength}) AS {$srcColName['col_name']}", $srcTableColumnsStr);
        }

        $dstTableColumns = array_values($columnsMap);
        $dstTableColumnsStr = '"' . implode('", "', $dstTableColumns) . '"';

        $selectSql = '';

        if ($mergeNeighbourFeatures) {
            $selectSql .= $cte;
        }

        $selectSql .= "SELECT {$srcTableColumnsStr} FROM {$fromExpr}";

        if ($mergeNeighbourFeatures) {
            $selectSql .= " JOIN grouped_geom gg ON st_intersects (gg.new_geom ,{$geomColumn})";
        }

        if (count($gidsOrData) && $copyFromSrcTable) {
            $selectSql .= " WHERE {$gidColumn} IN ({$gidValues})";
        }

        if ($mergeNeighbourFeatures) {
            $selectSql .= ' group by gg.new_geom;';
        }

        if ($srcLayer->isRemote()) {
            $srcColumnsDef = array_filter($srcLayer->getDefinitions(), function ($columnDef) use ($srcTableColumns) {
                return in_array($columnDef['col_name'], $srcTableColumns);
            });

            $dbLinkColumns = array_map(function ($columnDef) {
                $colName = '"' . $columnDef['col_name'] . '"';
                $colType = $GLOBALS['Layers']['columnTypesByCategory'][$columnDef['col_category']];

                if (!$colType) {
                    throw new Exception('Invalid column type by category');
                }

                return "{$colName} {$colType}";
            }, $srcColumnsDef);
            $dbLinkColumnsStr = implode(', ', $dbLinkColumns);

            $selectSql = str_replace("'", "''", $selectSql);
            $selectSql = "SELECT * FROM dblink('host=" . DBLINK_HOST
                . ' port=' . DBLINK_PORT
                . ' dbname=' . DBLINK_DATABASE
                . ' user=' . DBLINK_USERNAME
                . ' password=' . DBLINK_PASSWORD . "', '"
                . $selectSql . "') as t ({$dbLinkColumnsStr})";
        }

        $sql = "INSERT INTO {$dstTable} ({$dstTableColumnsStr}) {$selectSql}";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    /**
     * Copy layer items from table to layer by matching the columns of the table with those from layer's definition.
     *
     * @param UserLayers $layer - The layer to copy items to
     * @param string $table - The table to copy items from
     * @param bool $matchAllColumns - If true, an exception will be thrown if a column from layer's definition is missing in the table, otherwise the column will be skipped
     * @param array $columnsToCopy - The columns to copy from the table. If empty or not present all columns are matched with the columns from layer's definition
     *
     * @throws Exception
     */
    public function copyLayerItemsFromTable(UserLayers $layer, string $table, bool $matchAllColumns = false, array $columnsToCopy = [])
    {
        $columnTypesByCategory = $GLOBALS['Layers']['columnTypesByCategory'];
        $tableColumns = $this->getTableColumnsList($table);

        /**
         * @var array $fillableColumnsExpressions Array having column name as key and its select expression as value
         */
        $fillableColumnsExpressions = [];
        foreach ($layer->getDefinitions() as $layerDef) {
            if (
                $layerDef['col_virtual']
                || Config::LAYER_COLUMN_CATEGORY_GID === $layerDef['col_category']
                || (count($columnsToCopy) > 0 && !in_array($layerDef['col_name'], $columnsToCopy))
            ) {
                // Skip virtual columns and gid column as they are not fillable. Skip the columns that are not in $columnsToCopy
                continue;
            }

            if (!in_array($layerDef['col_name'], $tableColumns)) {
                if ($matchAllColumns) {
                    // Throw exception if $matchAllColumns is set and non-virtual culumn exists in definition but not in tmp table
                    throw new Exception("Column '{$layerDef['col_name']}' is missing in table '{$table}'.", ERROR_MISSING_COLUMN);
                }

                // Skip column if $matchAllColumns is false and non-virtual culumn exists in definition but not in tmp table
                continue;
            }

            $columnType = Config::LAYER_COLUMN_CATEGORY_BOOLEAN === $layerDef['col_category']
                ? 'INT::' . $columnTypesByCategory[$layerDef['col_category']] // In case the column from src table is numeric or string
                : $columnTypesByCategory[$layerDef['col_category']];

            // Explicitly cast the column to the type of the column in the layer definition
            $fillableColumnsExpressions[$layerDef['col_name']] = " \"{$layerDef['col_name']}\"::{$columnType}";
        }

        $insertColumnsStr = implode(', ', array_keys($fillableColumnsExpressions));
        $selectColumnsStr = implode(', ', array_values($fillableColumnsExpressions));

        // Ensure there are columns to select
        if (!count($fillableColumnsExpressions)) {
            throw new Exception('No matching columns found between tables.', ERROR_MISSING_COLUMN);
        }

        try {
            $sql = "INSERT INTO {$layer->table_name} ({$insertColumnsStr}) SELECT {$selectColumnsStr} FROM {$table}";
            $cmd = $this->DbModule->createCommand($sql);
            $cmd->execute();
        } catch (Exception $e) {
            throw new Exception($e->getMessage(), ERROR_RUNTIME);

            return false;
        }

        return true;
    }

    public function simplifyGeometry(string $tableName, float $tolerance, string $geomColumn = 'geom')
    {
        $sql = "UPDATE  {$tableName} SET \"{$geomColumn}\" =  simplify_geom(\"{$geomColumn}\", {$tolerance})";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    /**
     * @throws TDbException
     *
     * @return array|false
     */
    public function getAllOwnerPaymentsAmountByTransactionId($transactionId)
    {
        $cmd = $this->DbModule->createCommand(
            'SELECT 
                    sp.owner_id, 
                    sp.contract_id, 
                    sp.farming_year, 
                    concat(sp.owner_id, sp.contract_id, sp.farming_year) as owner_key,
                    sum(sp.amount) as amount
                FROM public.su_payments sp
                INNER JOIN su_transactions st ON st.id = sp.transaction_id
                WHERE
                    st.status = true
                    and (sp.owner_id, sp.contract_id, sp.farming_year) IN (
                        SELECT sp1.owner_id, sp1.contract_id, sp1.farming_year
                        FROM su_payments sp1
                        WHERE sp1.transaction_id IN (:transaction_id)
                )
                group by sp.owner_id, sp.contract_id, sp.farming_year'
        );
        $cmd->bindParameter(':transaction_id', $transactionId);

        return $cmd->query()->readAll();
    }

    public function getLayerColoringLegend(UserLayers $layer, string $ekatte = null)
    {
        // Get layer style

        $layerId = $layer->id;
        if ($ekatte) {
            $layerId .= '_' . $ekatte;
        }

        $layerStyle = LayerStyles::getLayerStyleByLayerIdAndType($layerId, LayerStyles::BY_ATTRIBUTE_COLORING_TYPE);

        if (!$layerStyle) {
            throw new Exception('Cannot find styles by attribute for this layer', 404);
        }

        // Get fill column name and value
        $definitions = $layer->getDefinitions();
        $definitions = array_combine(array_column($definitions, 'col_name'), $definitions);
        $virtuаlDefinitions = UserLayers::filterDefinitions($definitions, [['col_virtual' => true]]);
        $virtuаlDefinitions = array_combine(array_column($virtuаlDefinitions, 'col_reference'), $virtuаlDefinitions);

        $fillColumnName = $layerStyle->fill_column_name;
        $fillVirtualColumnName = $virtuаlDefinitions[$fillColumnName];
        $fillColumnValue = $fillVirtualColumnName['col_name'] ?? $fillColumnName;

        // Get border column name and value
        $borderColumnName = $layerStyle->border_column_name;
        $borderVirtualColumnName = $virtuаlDefinitions[$borderColumnName];
        $borderColumnValue = $borderVirtualColumnName['col_name'] ?? $borderColumnName;

        // Get area columns
        $areaColumnsDef = [];
        if (Config::LAYER_TYPE_KVS == $layer->layer_type) {
            // TODO: Update after adding LAYER_COLUMN_CATEGORY_AREA
            $areaColumnsDef = UserLayers::filterDefinitions(
                $layer->getDefinitions(),
                [
                    ['col_name' => 'allowable_area'],
                    ['col_name' => 'document_area'],
                ]
            );
        } else {
            // TODO: Update after adding LAYER_COLUMN_CATEGORY_AREA
            $areaColumnsDef = array_filter($layer->getDefinitions(), function ($columnDef) {
                return Config::LAYER_COLUMN_CATEGORY_NUMBER === $columnDef['col_category']
                    && false !== strpos($columnDef['col_name'], 'area');
            });
        }

        // Generate coloring legend query
        $areaColumnsSql = 'JSONB_BUILD_ARRAY(' . array_reduce($areaColumnsDef, function ($carry, $columnDef) {
            return $carry . "JSONB_BUILD_OBJECT(
                'label', '{$columnDef['col_title']}',
                'value', SUM(ROUND(COALESCE({$columnDef['col_name']}, 0)::NUMERIC, 3))::TEXT
            ),";
        }, '');
        $areaColumnsSql = rtrim($areaColumnsSql, ',');
        $areaColumnsSql .= ')';

        $stringHelper = new StringHelper();
        if (isset($ekatte) && !$stringHelper->validateEkatte($ekatte)) {
            throw new Exception('Invalid ekatte');
        }

        $ekatteFilter = $ekatte ? "ekate = '{$ekatte}'" : 'TRUE';

        $fill = null;
        if (isset($fillColumnName, $fillColumnValue)) {
            $fillQuery = "SELECT
                    \"{$fillColumnName}\" AS \"value\",
                    \"fill_color\" AS \"color\",
                    COALESCE(normalize_value(\"{$fillColumnValue}\"::TEXT), 'No value') AS \"label\",
                    {$areaColumnsSql} AS area_columns
                FROM 
                    {$layer->table_name} 
                WHERE
                    {$ekatteFilter}
                GROUP BY
                    \"{$fillColumnName}\", \"{$fillColumnValue}\", \"fill_color\"
            ";
            $fillCmd = $this->DbModule->createCommand($fillQuery);
            $fill = array_map(function ($item) {
                $item['area_columns'] = json_decode($item['area_columns'], true);

                return $item;
            }, $fillCmd->query()->readAll());
        }

        $borders = null;
        if (isset($borderColumnName, $borderColumnValue)) {
            $bordersQuery = "SELECT
                    \"{$borderColumnName}\" AS \"value\",
                    \"border_color\" AS \"color\",
                    COALESCE(normalize_value(\"{$borderColumnValue}\"::TEXT), 'No value') AS \"label\",
                    {$areaColumnsSql} AS area_columns
                FROM 
                    {$layer->table_name} 
                WHERE
                    {$ekatteFilter}
                GROUP BY
                    \"{$borderColumnName}\", \"{$borderColumnValue}\", \"border_color\"
            ";

            $bordersCmd = $this->DbModule->createCommand($bordersQuery);

            $borders = array_map(function ($item) {
                $item['area_columns'] = json_decode($item['area_columns'], true);

                return $item;
            }, $bordersCmd->query()->readAll());
        }

        return [
            'layer_id' => $layerStyle->layer_id,
            'fill' => $fill,
            'borders' => $borders,
        ];
    }

    public function dropViewIfExists(string $viewName)
    {
        $cmd = $this->DbModule->createCommand("DROP VIEW IF EXISTS {$viewName}");
        $cmd->execute();
    }

    public function createUserFarmingPermissionsView(int $groupId)
    {
        $dbLink = 'host=' . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD;

        $createViewCmd = $this->DbModule->createCommand("CREATE VIEW 
            user_farming_permissions AS
                SELECT *
                FROM dblink(
                    '{$dbLink}',
                    $$
                        SELECT
                            su.id as user_id,
                            su.keycloak_uid,
                            su.group_id as group_id,
                            suf.id AS farming_id,
                            suf.name AS farming_name,
                            suf.uuid as farming_uuid,
                            CASE 	
                                WHEN sop.\"permission\" = 1 THEN 'read'
                                WHEN sop.\"permission\" = 2 THEN 'write'
                            END AS permission
                        FROM 
                            su_users AS su
                        JOIN su_users_farming AS suf
                            ON suf.group_id  = su.group_id
                        JOIN su_object_permissions AS sop
                            ON sop.user_id = su.id
                            AND sop.class='TF\Application\Entity\UserFarmings'
                            AND sop.object_id = suf.id
                        WHERE 
                            su.group_id = {$groupId}
                    $$
                ) AS (user_id INT, keycloak_uid UUID, group_id INT, farming_id INT, farming_name VARCHAR, farming_uuid UUID, \"permission\" VARCHAR);
            ");

        $createViewCmd->execute();
    }

    protected function createWhereOrBinds(TDbCommand $cmd, $options, $values)
    {
        for ($i = 0; $i < count($values); $i++) {
            $value = $values[$i];
            $dataType = PdoHelper::getPdoType($value);

            $cmd->bindParameter(':' . $i . 'name', $value, $dataType);
        }
    }

    /**
     * @param array $definitions
     * @param array $commonToRemove
     *
     * @return array
     */
    private function getCommonColumns($definitions, $commonToRemove = [])
    {
        $fromEkatteField = array_filter($definitions['from']['columns'] ?? [], function ($column) {
            return false !== strpos($column, 'ekate')
                || false !== strpos($column, 'ekatte');
        });
        $fromEkatteField = reset($fromEkatteField) ?? null;

        $from = array_combine($definitions['from']['columns'], $definitions['from']['udt_name']);
        $to = array_combine($definitions['to']['columns'], $definitions['to']['udt_name']);

        $common = array_intersect($from, $to);

        if ($common['name']) {
            unset($common['name']);
        }
        if ($common['geom']) {
            unset($common['geom']);
        }
        if ($common['id']) {
            unset($common['id']);
        }
        if ($common['gid']) {
            unset($common['gid']);
        }
        if ($common['area_name']) {
            unset($common['area_name']);
        }
        if (!empty($commonToRemove)) {
            $commonToRemove = array_combine($commonToRemove, $commonToRemove);
            foreach ($commonToRemove as $i => $column) {
                if ($common[$column]) {
                    unset($common[$column]);
                }
            }
        }

        return [$fromEkatteField, $common];
    }

    private function columnLenght($table, $colName): int
    {
        $sql = 'SELECT character_maximum_length FROM information_schema.columns WHERE table_name = :tableName AND column_name = :columnName;';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':tableName', $table);
        $cmd->bindParameter(':columnName', $colName);

        return $cmd->query()->read()['character_maximum_length'] ?? 255; // when create new layer character_maximum_length is null. column default value is 255
    }

    private function prepareCsdViewStyle(string $coloringType, array $coloringOptions)
    {
        $declaredAreaThreshold = Config::DECLARED_AREA_THRESHOLD;

        /**
         * @var array $csdJoinColumnMap
         *            Mapper for CSD view column and its join value. Because we do not have a real column when we create the view and need to use join value to take color or label.
         */
        $csdJoinColumnMap = [
            'kad_ident' => 'lk.kad_ident',
            'ntp' => 'lk.virtual_ntp_title',
            'category' => 'lk.virtual_category_title',
            'locality' => 'lk.mestnost',
            'document_area' => 'lk.document_area',
            'declared_area_status' => 'declared_area_status::text',
            'allowable_area' => 'lk.allowable_area',
            'applicant' => 'applicant',
            'declared_area' => 'declared_area',
            'lessor' => 'lessor_aggregated.lessor',
            'declaration_type' => 'declaration_type',
            'wish' => 'wish',
            'year' => 'year::text',
            'number_of_documents' => 'scd.number_of_documents::text',
        ];

        $defaultColorCategories = DefaultColors::getCategories();
        $dbLink = 'host=' . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD;

        $label = $coloringOptions['label'] ?? "('')";
        foreach ($csdJoinColumnMap as $csdColumn => $joinColumn) {
            $label = preg_replace('/\b' . preg_quote($csdColumn, '/') . '\b/', $joinColumn, $label);
        }
        $label = str_replace('"', '', $label);

        $joinColorTable = '';
        $moreGroupingParams = '';
        if (LayerStyles::SINGLE_COLORING_TYPE === $coloringType) {
            $fillColor = trim($coloringOptions['fill_color'] ?? null);
            $borderColor = trim($coloringOptions['border_color'] ?? Config::LAYER_BOUNDARY_DEFAULT_COLOR);
            $fillColorSQL = empty($fillColor) ? 'null AS fill_color' : "'{$fillColor}' AS fill_color";
            $borderColorSQL = "'{$borderColor}' AS border_color";
        } else {
            $fillColorSQL = 'null AS fill_color';
            if ($coloringOptions['fill_column_definition']) {
                $fillColumnDefinition = $coloringOptions['fill_column_definition'];
                $fillColumnName = $fillColumnDefinition['col_name'];
                $fillColorSQL = "generate_color_from_value({$csdJoinColumnMap[$fillColumnName]}) as fill_color";

                if (in_array($fillColumnDefinition['col_category'], $defaultColorCategories)) {
                    $fillColorSQL = 'sc_fill.color AS fill_color';
                    $joinColorTable .= " LEFT JOIN (
                            SELECT *
                            FROM dblink(
                                '{$dbLink}',
                                'SELECT category, value, color FROM su_default_colors'
                            ) AS su_def_colors(category TEXT, value TEXT, color TEXT)
                        ) AS sc_fill ON sc_fill.category = '" . $fillColumnDefinition['col_category'] . "' AND sc_fill.value = " . $csdJoinColumnMap[$fillColumnDefinition['col_name']];
                    $moreGroupingParams .= ', sc_fill.color';
                }
            }

            $borderColor = "'" . Config::LAYER_BOUNDARY_DEFAULT_COLOR . "'";
            if ($coloringOptions['border_column_definition']) {
                $borderColumnDefinition = $coloringOptions['border_column_definition'];
                $borderColumnName = $borderColumnDefinition['col_name'];
                $borderColor = "generate_color_from_value({$csdJoinColumnMap[$borderColumnName]})";

                if (in_array($borderColumnDefinition['col_category'], $defaultColorCategories)) {
                    $borderColor = 'sc_border.color';
                    $joinColorTable .= " LEFT JOIN (
                            SELECT *
                            FROM dblink(
                                '{$dbLink}',
                                'SELECT category, value, color FROM su_default_colors'
                            ) AS su_def_colors(category TEXT, value TEXT, color TEXT)
                        ) AS sc_border ON sc_border.category = '" . $borderColumnDefinition['col_category'] . "' AND sc_border.value = " . $csdJoinColumnMap[$borderColumnDefinition['col_name']];
                    $moreGroupingParams .= ', sc_border.color';
                }
            }

            $borderColorSQL = " {$borderColor} AS border_color";
        }

        return [$fillColorSQL, $borderColorSQL, $label, $joinColorTable, $moreGroupingParams];
    }
}
