<?php

namespace TF\Engine\Plugins\Core\GlobalNotification;

use DateTime;
use TF\Engine\Plugins\Core\Base\BaseModel;

class GlobalNotificationModel extends BaseModel
{
    private $tableNotificationTypes;
    private $tableNotificationUsersClosed;

    public function __construct($tableName = null, $fieldName = null)
    {
        parent::__construct($tableName, $fieldName);

        $this->tableNotificationTypes = DEFAULT_DB_PREFIX . 'global_notification_types';
        $this->tableNotificationUsersClosed = DEFAULT_DB_PREFIX . 'global_notifications_users_closed';
    }

    public function getActiveNotClosedByUserId($userId)
    {
        $sql = "SELECT n.*, t.background_color_hex, t.name FROM {$this->tableName} n
                LEFT JOIN {$this->tableNotificationTypes} t ON t.id = n.type_id 
                LEFT JOIN {$this->tableNotificationUsersClosed} uc ON uc.notification_id = n.id AND uc.user_id = :userID
                WHERE start_time <= NOW() AND end_time >= NOW() AND is_active = TRUE AND uc.id is null
                ORDER BY t.order ASC, n.created DESC";

        $cmd = $this->DbModule->createCommand($sql);

        $cmd->bindParameter(':userID', $userId);

        return $cmd->query()->readAll();
    }

    public function close($userId, $notificationId)
    {
        $sql = "INSERT INTO {$this->tableNotificationUsersClosed} (notification_id, user_id) VALUES (:notificationId, :userId)";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':userId', $userId);
        $cmd->bindParameter(':notificationId', $notificationId);

        $cmd->execute();

        return true;
    }

    public function getAll($startDate, $endDate)
    {
        $sql = "SELECT n.*, t.background_color_hex, t.name as type_name FROM {$this->tableName} n
                LEFT JOIN {$this->tableNotificationTypes} t ON t.id = n.type_id 
                WHERE true ";

        if (!empty($startDate)) {
            $sql .= 'AND start_time = :startTime ';
        }
        if (!empty($endDate)) {
            $sql .= 'AND end_time = :endTime';
        }

        $sql .= ' ORDER BY n.start_time DESC, n.created DESC';

        $cmd = $this->DbModule->createCommand($sql);

        if (!empty($startDate)) {
            $cmd->bindParameter('startTime', $startDate);
        }
        if (!empty($endDate)) {
            $cmd->bindParameter('endTime', $endDate);
        }

        return $cmd->query()->readAll();
    }

    public function getTypes()
    {
        $sql = "SELECT * FROM {$this->tableNotificationTypes}";

        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function add($data)
    {
        $title = $data['title'];
        $text = $data['content'];
        $typeId = $data['type'];
        $startTime = $data['start_date'] ? new DateTime($data['start_date']) : new DateTime();
        $startTime->setTime(0, 0, 0);
        $startTime = "TIMESTAMP '" . $startTime->format('Y-m-d H:i:s') . "'";
        $endTime = 'NULL';
        if ($data['end_date']) {
            $endTime = new DateTime($data['end_date']);
            $endTime->setTime(23, 59, 59);
            $endTime = "TIMESTAMP '" . $endTime->format('Y-m-d H:i:s') . "'";
        }
        $isClosable = $data['closable'] ? $data['closable'] : 0;
        $isActive = $data['active'] ? $data['active'] : 0;

        $sql = "INSERT INTO {$this->tableName} (title, text, type_id, start_time, end_time, is_closable, is_active) 
                VALUES('{$title}', '{$text}', '{$typeId}', {$startTime}, {$endTime}, '{$isClosable}', '{$isActive}')";

        $cmd = $cmd = $this->DbModule->createCommand($sql);

        $cmd->execute();

        return true;
    }

    public function getById($id)
    {
        $sql = "SELECT n.id, n.title, n.text, n.start_time :: date, n.end_time :: date, n.is_closable, n.is_active, n.type_id FROM {$this->tableName} n 
                WHERE n.id = :id";

        $cmd = $this->DbModule->createCommand($sql);

        $cmd->bindParameter(':id', $id);

        return $cmd->queryRow();
    }

    public function edit($data)
    {
        $id = $data['id'];
        $title = $data['title'];
        $text = $data['content'];
        $typeId = $data['type'];
        $startTime = $data['start_date'] ? new DateTime($data['start_date']) : new DateTime();
        $startTime->setTime(0, 0, 0);
        $startTime = "TIMESTAMP '" . $startTime->format('Y-m-d H:i:s') . "'";
        $endTime = 'NULL';
        if ($data['end_date']) {
            $endTime = new DateTime($data['end_date']);
            $endTime->setTime(23, 59, 59);
            $endTime = "TIMESTAMP '" . $endTime->format('Y-m-d H:i:s') . "'";
        }
        $isClosable = $data['closable'] ? $data['closable'] : 0;
        $isActive = $data['active'] ? $data['active'] : 0;
        $sql = "UPDATE {$this->tableName} 
                SET 
                  title = '{$title}',
                  text = '{$text}', 
                  start_time = {$startTime},
                  end_time = {$endTime},
                  is_closable = '{$isClosable}',
                  is_active = '{$isActive}',
                  type_id = '{$typeId}',
                  modified = NOW()
                WHERE id = {$id}";

        $cmd = $this->DbModule->createCommand($sql);

        $cmd->execute();

        return true;
    }
}
