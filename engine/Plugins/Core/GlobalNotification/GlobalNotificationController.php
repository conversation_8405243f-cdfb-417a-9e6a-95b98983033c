<?php

namespace TF\Engine\Plugins\Core\GlobalNotification;

use TF\Engine\Plugins\Core\Base\BaseController;

include_once __DIR__ . '/../../../Plugins/Core/GlobalNotification/conf.php';

/**
 * GlobalNotificationController class file.
 *
 * <AUTHOR>
 */

/**
 * Class GlobalNotificationController.
 *
 * @property GlobalNotificationModel $DbHandler
 */
class GlobalNotificationController extends BaseController
{
    public function getActiveNotClosedByUserId($userId)
    {
        return $this->DbHandler->getActiveNotClosedByUserId($userId);
    }

    public function close($userId, $notificationId)
    {
        return $this->DbHandler->close($userId, $notificationId);
    }

    public function getAll($startDate, $endDate)
    {
        return $this->DbHandler->getAll($startDate, $endDate);
    }

    public function getTypes()
    {
        return $this->DbHandler->getTypes();
    }

    public function add($data)
    {
        return $this-><PERSON><PERSON><PERSON><PERSON><PERSON>->add($data);
    }

    public function getById($id)
    {
        return $this->DbHandler->getById($id);
    }

    public function edit($data)
    {
        return $this->DbHandler->edit($data);
    }
}
