<?php

namespace TF\Engine\Plugins\Core\Notifications;

use TF\Engine\Plugins\Core\UserDb\UserDbModel;

/**
 * User: k.v<PERSON><PERSON>
 * Date: 11/17/2017
 * Time: 1:23 PM.
 */
class NotificationModel extends UserDbModel
{
    private $alarmSetings = false;

    public function __construct($tableName = null, $fieldName = null)
    {
        parent::__construct($tableName, $fieldName);

        $this->tableName = $tableName;
        $this->fieldName = $fieldName;
        $this->alarmSetings = DEFAULT_DB_PREFIX . 'alert_settings';
    }

    public function getAlarmSettings($alarmType = null)
    {
        $result = $this->checkAlarmSettings();
        if (!$result) {
            foreach ($GLOBALS['Notifications']['Alarms'] as $key => $alarm) {
                $this->insertAlarmSettings($alarm);
            }
        }

        return $result;
    }

    private function checkAlarmSettings()
    {
        $sql = 'SELECT * FROM ' . $this->alarmSetings;
        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    private function insertAlarmSettings($options)
    {
        $params['tablename'] = $this->alarmSetings;
        $params['mainData'] = $options;

        return $this->addItem($params);
    }
}
