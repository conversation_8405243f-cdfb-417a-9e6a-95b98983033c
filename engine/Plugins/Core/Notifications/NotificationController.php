<?php

namespace TF\Engine\Plugins\Core\Notifications;

use TF\Engine\Kernel\StringHelper;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * NotificationController class file.
 *
 * <AUTHOR>
 */
// Prado::using('Plugins.Core.Base.BaseController');
// Prado::using('Plugins.Core.Notifications.conf');
// Prado::using('Plugins.Core.Notifications.*');

class NotificationController extends UserDbController
{
    /**
     * Returns user data.
     *
     * @param array $username
     *
     * @return array
     */
    public $DbHandler;
    public $Database;
    /** @var string $String */
    public $String;

    public function __construct($database)
    {
        $this->DbHandler = new NotificationModel($database);
        $this->Database = $database;
        $this->StringHelper = new StringHelper();
    }

    public function getNotificationsData($username)
    {
        return $this->DbHandler->getNotificationsData($username);
    }

    public function getAlarmSettings($alarmType = null)
    {
        return $this->DbHandler->getAlarmSettings($alarmType);
    }

    public function getAlarms()
    {
        return $this->DbHandler->getAlarms();
    }
}
