<?php

namespace TF\Engine\Plugins\Core\Contracts;

use TF\Engine\Plugins\Core\Base\BaseModel;

// Prado::using('Plugins.Core.Base.BaseModel');

class ContractsModel extends BaseModel
{
    private $contractsOwnersRelTable = false;
    private $contractsPlotsRelTable = false;

    public function __construct($tableName = null, $fieldName = null)
    {
        parent::__construct($tableName, $fieldName);

        $this->tableName = $tableName;
        $this->fieldName = $fieldName;
        $this->contractsOwnersRelTable = DEFAULT_DB_PREFIX . 'contracts_owners_rel';
        $this->contractsPlotsRelTable = DEFAULT_DB_PREFIX . 'contracts_plots_rel';
    }
}
