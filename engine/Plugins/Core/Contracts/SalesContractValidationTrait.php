<?php

namespace TF\Engine\Plugins\Core\Contracts;

use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;

trait SalesContractValidationTrait
{
    /**
     * Validates if there are sales contract relations and throws an exception if found.
     *
     * @param array $relations the relations to check
     * @param object $UserDbContractsController the controller to interact with the database
     *
     * @throws MTRpcException
     */
    protected function validateSalesContractRelations(array $params, UserDbContractsController $UserDbContractsController)
    {
        $salesContracts = $UserDbContractsController->hasSalesContractsRelation($params);

        if (count($salesContracts)) {
            throw new MTRpcException($salesContracts, -34100);
        }
    }
}
