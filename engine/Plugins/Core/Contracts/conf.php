<?php

$GLOBALS['Contracts']['tableName'] = 'contracts';
$GLOBALS['Contracts']['fieldName'] = 'id';

$GLOBALS['Contracts']['ContractTypes'] = [
    1 => ['id' => 1, 'name' => 'Собственост'],
    2 => ['id' => 2, 'name' => 'Аренда'],
    3 => ['id' => 3, 'name' => 'Наем'],
    4 => ['id' => 4, 'name' => 'Споразумение'],
    5 => ['id' => 5, 'name' => 'Съвместна обработка'],
];
// Договор тип продажба няма място в горния масив! 7 => array('id' => 7, 'name' => 'Продажба'),

$GLOBALS['Contracts']['AnnexTypes'] = [
    1 => ['id' => 1, 'name' => 'Добавяне/Премахване на имот'],
    2 => ['id' => 2, 'name' => 'Редактирани имоти'],
    3 => ['id' => 3, 'name' => 'Параметри на договора'],
];

$GLOBALS['Contracts']['agg_types'] = [
    1 => ['id' => 1, 'name' => 'По желание'],
    2 => ['id' => 2, 'name' => 'Служебно'],
];

$GLOBALS['Contracts']['DSTypes'] = [
    1 => ['type_id' => 1, 'name' => 'Договор за покупка'],
    2 => ['type_id' => 2, 'name' => 'Договор за замяна'],
    3 => ['type_id' => 3, 'name' => 'Нотариален акт'],
    4 => ['type_id' => 4, 'name' => 'Друго'],
    5 => ['type_id' => 5, 'name' => 'Решение на поземлена комисия'],
    6 => ['type_id' => 6, 'name' => 'Договор за доброволна делба'],
];

$GLOBALS['Contracts']['template_variables'] = [
    ['id' => 1, 'title' => 'Номер на договор', 'code' => '[[nomer_na_dogovor]]', 'group' => ''],
    ['id' => 2, 'title' => 'Имоти - подробно', 'code' => '[[imoti_podrobno]]', 'group' => ''],
    ['id' => 3, 'title' => 'Землище (ЕКАТТЕ)', 'code' => '[[zemlishte_ekatte]]', 'group' => ''],
    ['id' => 4, 'title' => 'Дата на сключване', 'code' => '[[data_na_dogovor]]', 'group' => ''],
    ['id' => 5, 'title' => 'Дата на влизане в сила', 'code' => '[[vlizane_v_sila]]', 'group' => ''],
    ['id' => 6, 'title' => 'Крайна дата', 'code' => '[[kraina_data]]', 'group' => ''],

    ['id' => 38, 'title' => 'Стопанство - подробно', 'code' => '[[stopanstvo]]', 'group' => 'Стопанство'],
    ['id' => 7, 'title' => 'Стопанство Име', 'code' => '[[stopanstvo_name]]', 'group' => 'Стопанство'],
    ['id' => 8, 'title' => 'Стопанство aдрес', 'code' => '[[stopanstvo_address]]', 'group' => 'Стопанство'],
    ['id' => 9, 'title' => 'Стопанство - Фирма', 'code' => '[[stopanstvo_firma]]', 'group' => 'Стопанство'],
    ['id' => 10, 'title' => 'Стопанство - Фирма Булстат', 'code' => '[[stopanstvo_bulstat]]', 'group' => 'Стопанство'],
    ['id' => 11, 'title' => 'Стопанство - Фирма адрес', 'code' => '[[stopanstvo_firma_address]]', 'group' => 'Стопанство'],
    ['id' => 12, 'title' => 'Стопанство - МОЛ', 'code' => '[[stopanstvo_mol]]', 'group' => 'Стопанство'],
    ['id' => 13, 'title' => 'Стопанство - МОЛ ЕГН', 'code' => '[[stopanstvo_mol_egn]]', 'group' => 'Стопанство'],
    ['id' => 14, 'title' => 'Стопанство - Банкови сметки', 'code' => '[[stopanstvo_iban_arr]]', 'group' => 'Стопанство'],

    ['id' => 17, 'title' => 'Тип на договор', 'code' => '[[tip_na_dogovor]]', 'group' => 'Наем, аренда и съвместна обработка'],
    ['id' => 17, 'title' => 'Група на договор', 'code' => '[[grupa_na_dogovor]]', 'group' => 'Наем, аренда и съвместна обработка'],
    ['id' => 18, 'title' => 'Номер от служба по вписване', 'code' => '[[nomer_na_vpisvane]]', 'group' => 'Наем, аренда и съвместна обработка'],
    ['id' => 19, 'title' => 'Дата от служба по вписване', 'code' => '[[data_na_vpisvane]]', 'group' => 'Наем, аренда и съвместна обработка'],
    ['id' => 20, 'title' => 'Рента', 'code' => '[[renta]]', 'group' => 'Наем, аренда и съвместна обработка'],
    ['id' => 21, 'title' => 'Рента в натура', 'code' => '[[renta_v_natura]]', 'group' => 'Наем, аренда и съвместна обработка'],
    ['id' => 22, 'title' => 'Общо дължима рента в лева', 'code' => '[[total_renta]]', 'group' => 'Наем, аренда и съвместна обработка'],
    ['id' => 39, 'title' => 'Общо рента за получаване ( преотдадени )', 'code' => '[[total_subleases_renta]]', 'group' => 'Наем, аренда и съвместна обработка'],

    ['id' => 23, 'title' => 'Страна по договор', 'code' => '[[kontragent]]', 'group' => 'Наем, аренда и съвместна обработка'],
    ['id' => 24, 'title' => 'Страна по договор ( представител )', 'code' => '[[kontragent_rep]]', 'group' => 'Наем, аренда и съвместна обработка'],
    ['id' => 25, 'title' => 'Страна по договор ( преотдаване )', 'code' => '[[sublease]]', 'group' => 'Наем, аренда и съвместна обработка'],
    ['id' => 26, 'title' => 'Имоти по кадастрален идентификатор', 'code' => '[[imoti]]', 'group' => 'Наем, аренда и съвместна обработка'],
    ['id' => 27, 'title' => 'Имоти по землища', 'code' => '[[imoti_zemlishta]]', 'group' => 'Наем, аренда и съвместна обработка'],
    ['id' => 28, 'title' => 'Имоти по землища - идентификатор', 'code' => '[[imoti_zemlishta_kadident]]', 'group' => 'Наем, аренда и съвместна обработка'],
    ['id' => 29, 'title' => 'Падеж', 'code' => '[[padej]]', 'group' => 'Наем, аренда и съвместна обработка'],
    ['id' => 30, 'title' => 'Срок на договор в стопански години', 'code' => '[[timespan_farming_years]]', 'group' => 'Наем, аренда и съвместна обработка'],
    ['id' => 31, 'title' => 'Имоти по категория', 'code' => '[[imoti_kategoriq]]', 'group' => 'Наем, аренда и съвместна обработка'],
    ['id' => 43, 'title' => 'Страна по договор - подписал договора', 'code' => '[[contract_signer]]', 'group' => 'Наем, аренда и съвместна обработка'],

    // ['id' => 32, 'title' => 'Име на член - кооператор', 'code' => '[[ime_kooperator]]', 'group' => 'Дялов капитал'],
    // ['id' => 33, 'title' => 'Размер на дялов капитал', 'code' => '[[razmer_dyalov_kapital]]', 'group' => 'Дялов капитал'],
    // ['id' => 34, 'title' => 'Текущ капитал', 'code' => '[[tekusht_kapital]]', 'group' => 'Дялов капитал'],
    // ['id' => 35, 'title' => 'ЕГН на член - кооператор', 'code' => '[[egn_kooperator]]', 'group' => 'Дялов капитал'],
    // ['id' => 36, 'title' => 'Л.К. на член - кооператор', 'code' => '[[l_k_kooperator]]', 'group' => 'Дялов капитал'],
    // ['id' => 37, 'title' => 'Дата на издаване на Л.К.', 'code' => '[[l_k_data_izdavane]]', 'group' => 'Дялов капитал'],

    ['id' => 41, 'title' => 'Днес', 'code' => '[[today]]', 'group' => 'Лични данни'],
    ['id' => 42, 'title' => 'Декларация за лични данни', 'code' => '[[owner_detailed]]', 'group' => 'Лични данни'],

    ['id' => 44, 'title' => 'Номер от служба по вписвания', 'code' => '[[sv_num]]', 'group' => 'Договор'],
    ['id' => 45, 'title' => 'Дата от служба по вписвания', 'code' => '[[sv_date]]', 'group' => 'Договор'],
    ['id' => 46, 'title' => 'Номер - информация ОСЗ', 'code' => '[[osz_num]]', 'group' => 'Договор'],
    ['id' => 47, 'title' => 'Дата - информация ОСЗ', 'code' => '[[osz_date]]', 'group' => 'Договор'],
    ['id' => 48, 'title' => 'Забележка (към договора)', 'code' => '[[comment]]', 'group' => 'Договор'],
    // ['id' => 48, 'title' => 'Забележка (към собственика)', 'code' => '[[kontragent remark]]', 'group' => 'Договор'],
    ['id' => 49, 'title' => 'Обща площ по договор', 'code' => '[[obobshtena_contract_area]]', 'group' => 'Договор'],
    ['id' => 50, 'title' => 'Обработваема площ - обща по договор', 'code' => '[[obobshtena_obrabotvaema_area]]', 'group' => 'Договор'],
    ['id' => 51, 'title' => 'Обща площ за рента по договор', 'code' => '[[obobshtena_rent_area]]', 'group' => 'Договор'],
    ['id' => 52, 'title' => 'Стойност на договора (когато има въведена информация за рента)', 'code' => '[[contract_price]]', 'group' => 'Договор'],
    ['id' => 53, 'title' => 'Рента в размери - кол рента по договор', 'code' => '[[renta_natura_obobshteno]]', 'group' => 'Договор'],
];

$GLOBALS['Contracts']['variables_plots_detailed'] = [
    'c_num' => 'Договор за <br/>наем/аренда Nº',
    'c_date' => 'Договор от <br/>дата',
    'zemlishte' => 'Землище',
    'kad_ident' => 'Идентификатор',
    'old_kad_ident' => 'Стар индентификатор',
    'imoten_nomer' => 'Имотен номер',
    'contract_area' => 'Площ по <br/>договор(дка)',
    'document_area' => 'Площ по <br/>документ(дка)',
    'allowable_area' => 'Площ по <br/> сечение(дка)',
    'category' => 'Категория',
    'mestnost' => 'Местност',
    'ntp' => 'НТП',
    'contract_area_for_sale' => 'Прод.-Продадена площ(дка)',
    'price_per_acre' => 'Прод.-Цена/дка',
    'price_sum' => 'Прод.-Сума',
    'area_for_rent' => 'Площ за <br/>рента(дка)',
    'rent_per_plot' => 'Индивидуална рента на имот',
    'plot_neighbors' => 'Съседи на имота',
    'comment' => 'Забележка',
    'kvs_allowable_area' => 'Обработваема площ',
];

$GLOBALS['Contracts']['renta_units'] = [
    1 => ['id' => 1, 'name' => 'кг', 'fullname' => 'килограм', 'per_dka_name' => 'кг/дка'],
    2 => ['id' => 2, 'name' => 'л', 'fullname' => 'литър', 'per_dka_name' => 'л/дка'],
    3 => ['id' => 3, 'name' => 'бр', 'fullname' => 'бройка', 'per_dka_name' => 'бр/дка'],
];

$GLOBALS['Months'] = [
    0 => 'Януари',
    1 => 'Февруари',
    2 => 'Март',
    3 => 'Април',
    4 => 'Май',
    5 => 'Юни',
    6 => 'Юли',
    7 => 'Август',
    8 => 'Септември',
    9 => 'Октомври',
    10 => 'Ноември',
    11 => 'Декември',
];

$GLOBALS['Payments']['types'] = [
    1 => ['payment_from_id' => 1, 'payment_in_id' => 1, 'payment_type_text' => 'от Лева в Лева'],
    2 => ['payment_from_id' => 1, 'payment_in_id' => 2, 'payment_type_text' => 'от Лева в Натура'],
    3 => ['payment_from_id' => 2, 'payment_in_id' => 1, 'payment_type_text' => 'от Натура в Лева'],
    4 => ['payment_from_id' => 2, 'payment_in_id' => 2, 'payment_type_text' => 'от Натура в Натура'],
];

$GLOBALS['Payments']['RKO_TYPE'] = [
    'RKO_STANDART' => 'rko_standart',
    'RKO_RECEIPT' => 'rko_receipt',
    'RKO_CARD' => 'rko_card',
];

const TRANSACTION_TYPE_PAYMENT = 1;
const TRANSACTION_TYPE_PERSONAL_USE = 2;
$GLOBALS['transactions']['types'] = [
    TRANSACTION_TYPE_PAYMENT => [
        'label' => 'Плащания',
    ],
    TRANSACTION_TYPE_PERSONAL_USE => [
        'label' => 'Лично ползване',
    ],
];

const COLLECTION_TYPE_RENT = 1;
const COLLECTION_TYPE_PERSONAL_USE = 2;

$GLOBALS['collections']['types'] = [
    COLLECTION_TYPE_RENT => [
        'label' => 'Ренти',
    ],
    COLLECTION_TYPE_PERSONAL_USE => [
        'label' => 'Лично ползване',
    ],
];
