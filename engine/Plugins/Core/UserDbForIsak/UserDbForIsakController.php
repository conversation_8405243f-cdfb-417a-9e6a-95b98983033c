<?php

namespace TF\Engine\Plugins\Core\UserDbForIsak;

use TF\Engine\Plugins\Core\UserDb\UserDbController;

// Prado::using('Plugins.Core.UserDb.UserDbController');

class UserDbForIsakController extends UserDbController
{
    public $DbHandler;
    public $Database;

    public function __construct($database)
    {
        $this->DbHandler = new UserDbForIsakModel($database);
        $this->Database = $database;
    }

    public function getForIsakDiffVPSReportData($options, $tableName, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getForIsakDiffVPSReportData($options, $tableName, $counter, $returnOnlySQL);
    }

    public function getForIsakPNDReportData($options, $tableName, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getForIsakPNDReportData($options, $tableName, $counter, $returnOnlySQL);
    }

    public function getForIsakDiffAllowableFinalData($options, $tableName, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getForIsakDiffAllowableFinalData($options, $tableName, $counter, $returnOnlySQL);
    }

    public function getForIsakDiffLayer($options, $tableName, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getForIsakDiffLayer($options, $tableName, $counter, $returnOnlySQL);
    }

    public function getForIsakDiffLfaData($options, $tableName, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getForIsakDiffLfaData($options, $tableName, $counter, $returnOnlySQL);
    }

    public function getForIsakDiffNaturaData($options, $tableName, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getForIsakDiffNaturaData($options, $tableName, $counter, $returnOnlySQL);
    }

    public function updateForIsakComment($data)
    {
        return $this->DbHandler->updateForIsakComment($data);
    }

    public function ForIsakMultiEdit($options)
    {
        $this->DbHandler->ForIsakMultiEdit($options);
    }

    public function updateForNR($data)
    {
        if (count($data->nr1) > 0) {
            $this->DbHandler->updateForNR(implode(',', $data->nr1), $data->layer_name, 'TRUE', 'FALSE');
        }

        if (count($data->nr2) > 0) {
            $this->DbHandler->updateForNR(implode(',', $data->nr2), $data->layer_name, 'FALSE', 'TRUE');
        }
    }

    public function updateOffNR($data)
    {
        if (count($data->nr1) > 0) {
            $this->DbHandler->updateOffNR(implode(',', $data->nr1), $data->layer_name, 'nr1');
        }

        if (count($data->nr2) > 0) {
            $this->DbHandler->updateOffNR(implode(',', $data->nr2), $data->layer_name, 'nr2');
        }
    }

    public function updateNaturaSitecode($data)
    {
        for ($i = 0; $i < count($data->natura); $i++) {
            $this->DbHandler->updateNaturaSitecode((object) $data->natura[$i], $data->layer_name);
        }
    }

    public function getForIsakData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getForIsakData($options, $counter, $returnOnlySQL);
    }

    public function updateForRequest($data)
    {
        return $this->DbHandler->updateForRequest($data);
    }

    public function getForIsakDataZDP($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getForIsakDataZDP($options, $counter, $returnOnlySQL);
    }

    public function getForIsakDataPZP($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getForIsakDataPZP($options, $counter, $returnOnlySQL);
    }

    public function getForIsakDataPZPDiffLayer($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getForIsakDataPZPDiffLayer($options, $counter, $returnOnlySQL);
    }

    public function getReportForIsakDataZDP($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getReportForIsakDataZDP($options, $counter, $returnOnlySQL);
    }

    public function getForIsakDiffVPSData($options, $vpsTable, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getForIsakDiffVPSData($options, $vpsTable, $counter, $returnOnlySQL);
    }

    public function updateForIsakDiffVPSArea($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->updateForIsakDiffVPSArea($options, $counter, $returnOnlySQL);
    }

    public function getForIsakDiffVPSDetailedData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getForIsakDiffVPSDetailedData($options, $counter, $returnOnlySQL);
    }
}
