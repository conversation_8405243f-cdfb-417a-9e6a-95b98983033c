<?php

namespace TF\Engine\Plugins\Core\UserDbForIsak;

use PDO;
use TF\Engine\Plugins\Core\UserDb\UserDbModel;

class UserDbForIsakModel extends UserDbModel
{
    public function getReportForIsakDataZDP($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM {$options['tablename']}  as for_isak
                WHERE true";

        return $this->commonSqlResult($sql, $options, $counter, $returnOnlySQL);
    }

    public function getForIsakDiffVPSReportData($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = "SELECT {$return} FROM {$options['tablename']}
                WHERE true ";

        if ($counter) {
            $sql = "SELECT {$return}
                    FROM
                    (
                        SELECT gid, round((ST_Area(geom) / 1000)::numeric, 3) AS area 
                        FROM {$options['tablename']}
                        WHERE true ";
        }

        return $this->commonSqlResultWithCounts($sql, $options, $counter, $returnOnlySQL);
    }

    public function getForIsakPNDReportData($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = "SELECT {$return} FROM {$options['tablename']}  as for_isak
                WHERE true ";

        if ($counter) {
            $sql = "SELECT {$return}
                    FROM
                    (
                        SELECT gid, round((ST_Area(geom)/10000)::numeric, 4) as area 
                        FROM {$options['tablename']} as for_isak 
                        WHERE true ";
        }

        return $this->commonSqlResultWithCounts($sql, $options, $counter, $returnOnlySQL);
    }

    public function getForIsakDataZDP($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = "SELECT {$return} FROM {$options['tablename']}  as for_isak
                WHERE true AND (for_isak.azot_fixed_crop = true OR 
                                for_isak.common_cultures = true OR
                                for_isak.is_tree_short_rotation = true OR
                                for_isak.cropcode = '190000')";

        if ($counter) {
            $sql = "SELECT {$return}
                    FROM
                    (
                        SELECT gid, round((ST_Area(geom)/10000)::numeric, 4) as area, round((ST_Area(geom)/10000)::numeric, 4)*green_area_factor as green_area 
                        FROM {$options['tablename']} as for_isak 
                        WHERE true AND (for_isak.azot_fixed_crop = true OR for_isak.common_cultures = true 
                        OR for_isak.is_tree_short_rotation = true OR for_isak.cropcode = '190000')";
        }

        return $this->commonSqlResultWithCounts($sql, $options, $counter, $returnOnlySQL);
    }

    public function getForIsakDiffAllowableFinalData($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);
        $layerId = $options['forIsakLayerId'];
        $seppView = $options['seppView'];

        $sql = "SELECT {$return}
        FROM
            {$options['tablename']} for_isak
        LEFT JOIN {$seppView} A ON st_intersects (A .geom, for_isak.geom) WHERE TRUE ";

        if ($counter) {
            $sql = "SELECT {$return}
            FROM
                (SELECT ST_AsText (for_isak.geom) AS st_astext,gid,round(ST_Area (for_isak.geom) :: NUMERIC / 10000, 4 ) AS area,
                    CASE
                    WHEN (
                    round(((ST_Area (for_isak.geom) :: NUMERIC ) - (ST_Area (ST_Difference (st_buffer(for_isak.geom, 0.00001), ST_Union (st_buffer(A .geom,0.00001) ) ) ) :: NUMERIC ) ) :: NUMERIC / 10000, 4 ) ) ISNULL THEN
                    round(ST_Area (for_isak.geom) :: NUMERIC / 10000, 4 ) ELSE
                    round(ST_Area (ST_Difference (st_buffer(for_isak.geom, 0.00001), ST_Union (st_buffer(A .geom,0.00001) ) ) ) :: NUMERIC / 10000, 4 ) END AS outside_area,
                                    CASE
                    WHEN (
                    round(((ST_Area (for_isak.geom) :: NUMERIC ) - (ST_Area (ST_Difference (st_buffer(for_isak.geom, 0.00001), ST_Union (st_buffer(A .geom,0.00001) ) ) ) :: NUMERIC ) ) :: NUMERIC / 10000, 4 ) ) ISNULL THEN
                    0
                    ELSE
                    round(((ST_Area (for_isak.geom) :: NUMERIC ) - (ST_Area (ST_Difference (st_buffer(for_isak.geom, 0.00001), ST_Union (st_buffer(A .geom,0.00001) ) ) ) :: NUMERIC ) ) :: NUMERIC / 10000, 4 ) END AS inside_area
                FROM
                    {$options['tablename']} for_isak
                LEFT JOIN {$seppView} A ON st_intersects (st_buffer(A .geom, 0.00001), st_buffer(for_isak.geom, 0.00001)) 
                WHERE TRUE ";
        }

        return $this->commonSqlResultWithCounts($sql, $options, $counter, $returnOnlySQL);
    }

    public function getForIsakDiffLayer($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = "SELECT {$return} FROM {$options['tablename']} for_isak
                LEFT JOIN
                    dblink('host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "',
                            'select geom from {$options['layer']} where geom && ST_MakeEnvelope({$options['extent']})'
                        ) AS layer(geom geometry) ON ST_Intersects(layer.geom, for_isak.geom)
                WHERE true";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];

            if ($options['having']) {
                $sql .= ' HAVING ' . $options['having'];
            }
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getForIsakDataPZP($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $pzpView = $options['pzpView'];

        $cropTypes = '';
        if (isset($options['isSchema'])) {
            $cropTypes = $this->getPZPCropTypes($options['isSchema']);
        }

        $sql = "SELECT {$return}
                FROM (SELECT 
                        gid,
                        prc_name,
                        edited,
                        comment,
                        ekatte,
                        cropname,
                        natura_sitecode,
                        sepp,
                        zdp,
                        pndp,
                        nr1,
                        nr2,
                        vps_type,
                        cropcode,
                        ST_AsText(for_isak.geom) AS st_astext,
                        round(ST_Area(for_isak.geom)::numeric/10000, 4) AS area,
                        for_isak.crop_type,
                        CASE WHEN ST_Area (ST_Intersection (for_isak.geom, ST_Union (pzp.geom))) IS NULL
                                THEN 0
                                ELSE round(ST_Area(ST_Intersection (for_isak.geom, ST_Union (pzp.geom)))::numeric/10000, 4) END AS in_area_pzp,
                        STRING_AGG(pzp.imotcode, '<br>') AS imotcode,
                        string_agg(round(ST_Area(ST_Intersection (for_isak.geom, pzp.geom))::numeric/10000, 4)::text, '<br>') as sub_areas
                    FROM
                        {$options['tablename']} for_isak
                    LEFT JOIN {$pzpView} pzp 
                    ON ST_Intersects(pzp.geom, for_isak.geom) 
                    WHERE round(ST_Area(ST_Intersection (for_isak.geom, pzp.geom))::numeric/10000, 4) >= 0.0001
                    GROUP BY for_isak.gid
                ) AS pzp_report
                WHERE true";

        if ($options['searchInCropType']) {
            $sql .= " AND (pzp_report.in_area_pzp >= 0.001 AND pzp_report.crop_type IN ({$cropTypes}))";
        }

        if (true == $options['isSchema']) {
            $sql .= ' OR (pzp_report.in_area_pzp >= 0.001 AND cropname IS NULL)';
        }

        return $this->commonSqlResult($sql, $options, $counter, $returnOnlySQL);
    }

    public function getForIsakDataPZPDiffLayer($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $pzpView = $options['pzpView'];

        $cropTypes = $this->getPZPCropTypes($options['isSchema']);

        $sql = "SELECT {$return}
                FROM (SELECT 
                        gid,
                        cropname,
                        for_isak.crop_type,
                        CASE WHEN ST_Area (ST_Intersection (for_isak.geom, ST_Union (pzp.geom))) IS NULL
                                THEN 0
                                ELSE round(ST_Area(ST_Intersection (for_isak.geom, ST_Union (pzp.geom)))::numeric/10000, 4) END AS in_area_pzp,
                        ST_Intersection (for_isak.geom, pzp.geom) as geom
                    FROM
                        {$options['tablename']} for_isak
                    LEFT JOIN {$pzpView} pzp
                    ON ST_Intersects(pzp.geom, for_isak.geom) 
                    WHERE round(ST_Area(ST_Intersection (for_isak.geom, pzp.geom))::numeric/10000, 4) >= 0.0001
                    GROUP BY for_isak.gid, pzp.geom
                ) AS pzp_report
                WHERE true AND (pzp_report.in_area_pzp >= 0.001 AND pzp_report.crop_type IN ({$cropTypes}))";

        if (true == $options['isSchema']) {
            $sql .= ' OR (pzp_report.in_area_pzp >= 0.001 AND cropname IS NULL)';
        }

        return $this->commonSqlResult($sql, $options, $counter, $returnOnlySQL);
    }

    public function getForIsakDiffLfaData($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = "SELECT {$return}
                FROM (SELECT 
                        gid,
                        for_isak.prc_name,
                        ST_AsText(for_isak.geom) AS st_astext,
                        ST_Area(for_isak.geom) AS area,
                        CASE WHEN ST_Area (ST_Difference (for_isak.geom, ST_Union (lfa.geom))) IS NULL
                            THEN ST_Area(for_isak.geom)
                            ELSE ST_Area (ST_Difference (for_isak.geom, ST_Union (lfa.geom))) END AS outside_area,
                        STRING_AGG(CASE WHEN ST_Area(st_intersection (for_isak.geom, lfa.geom)) > 50 THEN lfa.nm_lfa_eka END, ',') AS ekate,
                        for_isak.edited,
                        for_isak. COMMENT,
                        CASE lfa.nm_lfa_lfa WHEN '1' THEN for_isak.nr1 WHEN '2' THEN for_isak.nr2 ELSE false END AS nr,
                        lfa.nm_lfa_lfa
                    FROM
                        {$options['tablename']} for_isak
                    LEFT JOIN dblink('host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "',
                                'select geom, nm_lfa_lfa, nm_lfa_eka from layer_lfa where geom && ST_MakeEnvelope({$options['extent']})'
                            ) AS lfa (geom geometry, nm_lfa_lfa CHARACTER VARYING, nm_lfa_eka CHARACTER VARYING)
                    ON ST_Intersects(lfa.geom, for_isak.geom) AND ST_Area(ST_Intersection(lfa.geom, for_isak.geom)) > 50
                    GROUP BY lfa.nm_lfa_lfa, for_isak.gid
                    ORDER BY gid
                ) AS nr_report
                WHERE true";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];

            if ($options['having']) {
                $sql .= ' HAVING ' . $options['having'];
            }
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getForIsakDiffNaturaData($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = "SELECT {$return}
                FROM (SELECT 
                        for_isak.gid,
                        for_isak.prc_name,
                        ST_AsText(for_isak.geom) AS st_astext,
                        ST_Area(for_isak.geom) AS area,
                        CASE WHEN ST_Area (ST_Difference (for_isak.geom, ST_Union (natura.geom))) IS NULL
                            THEN ST_Area(for_isak.geom)
                            ELSE ST_Area (ST_Difference (for_isak.geom, ST_Union (natura.geom))) END AS outside_area,
                        for_isak.edited,
                        for_isak.cropcode,
                        for_isak.cropname,
                        for_isak.natura_sitecode,
                        for_isak. COMMENT,
                        natura.sitecode,
                        MAX(natura.name_bg) AS name_bg,
                        MAX(natura.gid) AS sitecode_gid,
                        MAX(natura.zapoved_no || '<br/>(' || natura.dv || ')') AS order_dv
                    FROM
                        {$options['tablename']} for_isak
                    LEFT JOIN
                        dblink('host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "',
                                'select geom, sitecode, name_bg, gid, zapoved_no, dv from layer_natura_2000 where geom && ST_MakeEnvelope({$options['extent']})'
                            ) AS natura (geom geometry, sitecode CHARACTER VARYING, name_bg CHARACTER VARYING, gid INTEGER, zapoved_no CHARACTER VARYING, dv CHARACTER VARYING)
                    ON ST_Intersects(natura.geom, for_isak.geom) AND ST_Area(ST_Intersection(natura.geom, for_isak.geom)) > 50
                        GROUP BY sitecode, for_isak.gid
                ) AS natura_report
                WHERE true";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];

            if ($options['having']) {
                $sql .= ' HAVING ' . $options['having'];
            }
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function updateForIsakComment($data)
    {
        $sql = "UPDATE {$data->layer_name} 
                SET   comment = :comment
                    , edited =  :edited
                WHERE gid = :gid";

        $cmd = $this->DbModule->createCommand($sql);

        $cmd->bindParameter(':comment', $data->comment);
        $cmd->bindParameter(':edited', $data->edited, PDO::PARAM_BOOL);
        $cmd->bindParameter(':gid', $data->gid);

        $cmd->execute();
    }

    public function updateForRequest($data)
    {
        $inside_area_sql = '';
        if ('vps_type' == $data->field) {
            $data->vps_inside_area = $data->vps_inside_area * 10;
            $inside_area_sql = ", vps_inside_area = {$data->vps_inside_area} ";
        }
        $sql = "UPDATE {$data->layer_name} 
                SET   {$data->field} =  {$data->field_value}" . $inside_area_sql . '
                WHERE gid = :gid';

        $cmd = $this->DbModule->createCommand($sql);

        $cmd->bindParameter(':gid', $data->gid);

        $cmd->execute();
    }

    public function updateForNR($gids, $table, $nr1, $nr2)
    {
        $sql = "UPDATE {$table}
                SET nr1 =  {$nr1}, nr2 = {$nr2} 
                WHERE gid IN({$gids})";

        $cmd = $this->DbModule->createCommand($sql);

        $cmd->execute();
    }

    public function updateOffNR($gids, $table, $column)
    {
        $sql = "UPDATE {$table}
                SET {$column} =  'FALSE' 
                WHERE gid IN({$gids})";

        $cmd = $this->DbModule->createCommand($sql);

        $cmd->execute();
    }

    public function updateNaturaSitecode($data, $table)
    {
        $sql = "UPDATE {$table}
                SET natura_sitecode = :sitecode
                WHERE gid = :gid";

        $cmd = $this->DbModule->createCommand($sql);

        $cmd->bindParameter(':sitecode', $data->sitecode);
        $cmd->bindParameter(':gid', $data->gid);

        $cmd->execute();
    }

    public function ForIsakMultiEdit($options)
    {
        $updateFields = array_keys($options['update']);
        $updateValues = array_values($options['update']);

        $sql = 'UPDATE ' . $options['tablename'] . ' SET ';

        for ($i = 0; $i < count($updateFields); $i++) {
            $sql .= ' ' . $updateFields[$i] . ' = :' . $updateFields[$i];
            if ($i < count($updateFields) - 1) {
                $sql .= ', ';
            }
        }
        if (0 != $options['id_string']) {
            $sql .= ' WHERE gid IN (' . $options['id_string'] . ')';
        }
        $cmd = $this->DbModule->createCommand($sql);

        for ($i = 0; $i < count($updateFields); $i++) {
            $cmd->bindParameter(':' . $updateFields[$i], $updateValues[$i]);
        }

        $cmd->execute();
    }

    public function getForIsakDiffVPSData($options, $vpsTable, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = "SELECT {$return}
        FROM
          (SELECT for_isak.gid,
                  ST_Area(for_isak.geom) AS area,
                  ST_Area (ST_Difference (for_isak.geom, ST_Union (system_table.geom))) AS outside_area,
                  for_isak.vps_type,
                  max(CASE WHEN ST_Area(st_intersection(for_isak.geom, system_table.geom)) > 50 THEN system_table.gid END) AS merg_gid
           FROM {$options['tablename']} for_isak,
                                        dblink('host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "', 'select geom, gid from {$vpsTable} where {$options['dblink_where']}' ) AS system_table (geom geometry, gid INTEGER)
           GROUP BY system_table.geom,
                    system_table.gid,
                    for_isak.gid
           HAVING NOT ((CASE WHEN ST_Area(st_intersection(for_isak.geom, system_table.geom)) > 50 THEN system_table.gid ELSE 0 END) = 0
                       AND count(*) <
                         (SELECT count(gid) FROM dblink('host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "', 'select gid from {$vpsTable} where {$options['dblink_where']}' ) AS gid_count (gid INTEGER)))
          ) AS natura_report
        WHERE TRUE";
        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];

            if ($options['having']) {
                $sql .= ' HAVING ' . $options['having'];
            }
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function updateForIsakDiffVPSArea($options, $counter, $returnOnlySQL)
    {
        $sql = "UPDATE {$options['tablename']}
        SET vps_inside_area = :inside_area,
            area = :area
        WHERE gid = :gid";

        $cmd = $this->DbModule->createCommand($sql);

        $cmd->bindParameter(':inside_area', $options['inside_area']);
        $cmd->bindParameter(':area', $options['area']);
        $cmd->bindParameter(':gid', $options['gid']);

        $cmd->execute();
    }

    public function getForIsakDiffVPSDetailedData($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);
        $natura_2000_sql_return = '';
        $natura_2000_sql_join = '';
        if ('layer_vps_merg' == $options['vps_tablename']) {
            $natura_2000_sql_join = "
                left join 
            			dblink (
            				'host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . '  password=' . DBLINK_PASSWORD . "',
            				'select geom from layer_natura_2000 where  {$options['dblink_where']}'
            			) AS b (geom geometry)
            on st_intersects(for_isak.geom, b.geom)";

            $natura_2000_sql_return = '
                CASE
                WHEN (
                    round(((ST_Area (for_isak.geom) :: NUMERIC ) - (ST_Area (ST_Difference (for_isak.geom, ST_Union (b .geom) ) ) :: NUMERIC ) ) :: NUMERIC / 10000, 4 ) ) ISNULL THEN
                    0
                ELSE
                    round(((ST_Area (for_isak.geom) :: NUMERIC ) - (ST_Area (ST_Difference (for_isak.geom, ST_Union (b .geom) ) ) :: NUMERIC ) ) :: NUMERIC / 10000, 4 ) END AS natura_2000_inside_area,';
        }
        $sql = 'SELECT
			for_isak.prc_name,
            ST_AsText (for_isak.geom) AS st_astext,
            gid,
            round(ST_Area (for_isak.geom) :: NUMERIC / 10000, 4 ) AS area, CASE
        WHEN (
            round(((ST_Area (for_isak.geom) :: NUMERIC ) - (ST_Area (ST_Difference (for_isak.geom, ST_Union (A .geom) ) ) :: NUMERIC ) ) :: NUMERIC / 10000, 4 ) ) ISNULL THEN
            round(ST_Area (for_isak.geom) :: NUMERIC / 10000, 4 ) ELSE
            round(ST_Area (ST_Difference (for_isak.geom, ST_Union (A .geom) ) ) :: NUMERIC / 10000, 4 ) END AS outside_area,
         CASE
        WHEN (
            round(((ST_Area (for_isak.geom) :: NUMERIC ) - (ST_Area (ST_Difference (for_isak.geom, ST_Union (A .geom) ) ) :: NUMERIC ) ) :: NUMERIC / 10000, 4 ) ) ISNULL THEN
            0
        ELSE
            round(((ST_Area (for_isak.geom) :: NUMERIC ) - (ST_Area (ST_Difference (for_isak.geom, ST_Union (A .geom) ) ) :: NUMERIC ) ) :: NUMERIC / 10000, 4 ) END AS inside_area,
         ' . $natura_2000_sql_return . "
         for_isak.cropcode,
         for_isak.cropname,
         for_isak.natura_sitecode,
         for_isak.vps_type,
         for_isak.edited,
         for_isak. COMMENT
        
		FROM
			{$options['tablename']} for_isak
            left join 
            			dblink (
            				'host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "',
            				'select geom from {$options['vps_tablename']} where  {$options['dblink_where']}'
            			) AS a (geom geometry)
            on st_intersects(for_isak.geom, a.geom)
            " . $natura_2000_sql_join . '
            GROUP BY for_isak.gid
            HAVING CASE
        WHEN (
            round(((ST_Area (for_isak.geom) :: NUMERIC ) - (ST_Area (ST_Difference (for_isak.geom, ST_Union (A .geom) ) ) :: NUMERIC ) ) :: NUMERIC / 10000, 4 ) ) ISNULL THEN
            0
        ELSE
            round(((ST_Area (for_isak.geom) :: NUMERIC ) - (ST_Area (ST_Difference (for_isak.geom, ST_Union (A .geom) ) ) :: NUMERIC ) ) :: NUMERIC / 10000, 4 ) end > 0.1
        ';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];

            if ($options['having']) {
                $sql .= ' HAVING ' . $options['having'];
            }
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    private function getPZPCropTypes($isSchema)
    {
        $cropTypes = '';
        if ($isSchema) {
            // the report  "Парцели в ПЗП"
            $cropTypes = "'Обработваема земя', 'Трайни насаждения', 'Неизвестна', 'Неземедeлски площи, Култивирани гъби', 'Семейни градини'";
        } else {
            // the tab 'Постоянно затревени площи'
            $cropTypes = "'Постоянно затревени площи'";
        }

        return $cropTypes;
    }
}
