<?php

namespace TF\Engine\Plugins\Core\DashboardPayments;

use TF\Engine\Plugins\Core\UserDb\UserDbModel;

class DashboardPaymentsModel extends UserDbModel
{
    public function __construct($tableName = null, $fieldName = null)
    {
        parent::__construct($tableName, $fieldName);

        $this->tableName = $tableName;
        $this->fieldName = $fieldName;
    }

    public function getPayments($options)
    {
        $sql = '
            SELECT
                DISTINCT (t4. DATE),
                t4.nat_type,
                COALESCE (amount, 0) AS total_amount
            FROM
                (
                    SELECT
                        *
                    FROM
                        (
                            SELECT DISTINCT
                                "date"
                            FROM
                                su_payments P
                            INNER JOIN su_payments_natura pn ON P ."id" = pn.payment_id
                        ) t1,
                        (
                            SELECT DISTINCT
                                pn.nat_type
                            FROM
                                su_payments P
                            INNER JOIN su_payments_natura pn ON P ."id" = pn.payment_id
                        ) t2
                ) t4
            LEFT JOIN (
                SELECT
                    "date",
                    pn.nat_type,
                    SUM (pn.amount) AS amount
                FROM
                    su_payments P
                INNER JOIN su_payments_natura pn ON P ."id" = pn.payment_id
                GROUP BY
                    "date",
                    pn.nat_type
            ) t3 ON (
                "t3"."date" = t4."date"
                AND "t3"."nat_type" = t4.nat_type
            )
            ORDER BY
                t4. DATE';

        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function getContractDates($options)
    {
        $return = [];

        $sql[0] = 'select distinct c_date ,  nm_usage_rights from su_contracts where nm_usage_rights = 1 ';
        $sql[1] = 'select distinct c_date from su_sales_contracts';

        foreach ($sql as $query) {
            $cmd = $this->DbModule->createCommand($query);
            $result = $cmd->query()->readAll();
            foreach ($result as $r) {
                array_push($return, $r['c_date']);
            }
        }
        $return = array_unique($return);
        asort($return);

        return $return;
    }

    public function rentNatura($params)
    {
        $sql = '	
        select 
        due.sum_rent,
        (case when paid.sum_amount is null then 0 else paid.sum_amount end) as paid_amount,
        (case when paid.sum_amount is null then due.sum_rent else(due.sum_rent - paid.sum_amount) end) as rest_sum,
        due.contract_renta_type, rt.name as renta_type';

        if ('farming' == $params['group']) {
            $sql .= ' , due.farming_id ';
        } elseif ('ekate' == $params['group']) {
            $sql .= ', due.ekate ';
        }
        $sql .= '
        from 
        (select sum(t2.rent_amount_sum)as sum_rent, t2.contract_renta_type';
        if ('farming' == $params['group']) {
            $sql .= ' , t2.farming_id ';
        } elseif ('ekate' == $params['group']) {
            $sql .= ', t2.ekate ';
        }
        $sql .= "
        from
		(select 
		scpr.id,
			c_num,
			sc.start_date,
			sc.due_date,
			sc.active,
			contract_end_date,
			area_for_rent,
			lk.ekate,
			rent_per_plot,
			sc.renta,
			farming_id, 
			scr.renta_id as contract_renta_type,
			sch.f_year,
			(case when sch.amount is null then scr.renta_value else sch.amount end) as rent_amount,
			(case when sch.amount is null then scr.renta_value*area_for_rent else sch.amount*area_for_rent end) as rent_amount_sum,
			sch.nat_type as charge_type,
			sc.parent_id
		from su_contracts_plots_rel scpr
		INNER join su_contracts sc  on (scpr.contract_id = sc.id)
		INNER join su_contracts_rents scr on (scpr.contract_id =scr.contract_id )
		left join layer_kvs lk on scpr.plot_id = lk.gid
		left join(
			select distinct(schr.plot_id) as plot_id,schn.amount, schn.nat_type, schr.\"year\"as f_year from su_charged_renta schr 
			join su_charged_renta_natura schn on (schr.id=schn.renta_id)
			where schr.\"year\" = :farming_year
		)sch on sch.plot_id = scpr.plot_id and sch.nat_type=scr.renta_id
		where sc.id not in (select parent_id from su_contracts where parent_id != 0 and sc.start_date <= ':start_date' and sc.due_date >= ':due_date') 
		)t2
		where
		t2.active = true
		and t2.start_date <= ':start_date'
		and t2.due_date >= ':due_date'
		and t2.contract_renta_type is not null
		";
        $sql .= '
		group by t2.contract_renta_type';

        if ('farming' == $params['group']) {
            $sql .= ' , t2.farming_id ';
        } elseif ('ekate' == $params['group']) {
            $sql .= ', t2.ekate ';
        }

        $sql .= '
		) due
 left join 	(select sum (p1.amount) as sum_amount, p1.nat_type,p1.farming_id from (
			select 
			stn.nat_type, 
			stn.amount, 
			stn.paid_from,
			stn.paid_in, 
			sc.farming_id 
			from su_transactions st
				left join su_payments sp on sp.transaction_id = st.id
				left join su_transactions_natura stn on stn.transaction_id = st.id
				left join su_contracts sc on sp.contract_id = sc.id
				where st.status = true and 
				sp.farming_year = :farming_year  
				and stn.paid_from = 2
			)p1
			group by p1.nat_type, p1.farming_id) paid 
  on due.contract_renta_type = paid.nat_type
   ';
        if ('farming' == $params['group']) {
            $sql .= '
                and due.farming_id = paid.farming_id ';
        }
        $sql .= 'left join su_renta_types rt on  due.contract_renta_type = rt.id';

        $sql = str_replace(':farming_year', $params['farming_year'], $sql);
        $sql = str_replace(':start_date', $params['payroll_from_date'], $sql);
        $sql = str_replace(':due_date', $params['payroll_to_date'], $sql);

        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function getContractsGrid($params)
    {
        $sql = "select t1.c_num, array_agg(distinct(concat(so.name, ' ' ,so.lastname))) as owners,array_agg(distinct(rt.name)) as rent , sum(t1.contract_area) as area from (
                select sc.c_num, scpr.contract_area,  sc.start_date, sc.due_date, sc.farming_id, spor.owner_id, scr.renta_id
				from su_contracts_plots_rel scpr
				inner join su_contracts sc on scpr.contract_id = sc.id
				inner join su_contracts_rents scr on scpr.contract_id = scr.contract_id
				left join su_plots_owners_rel spor on scpr.plot_id = spor.pc_rel_id 
				left join layer_kvs lk on scpr.plot_id = lk.gid
				where sc.id not in (select parent_id from su_contracts where parent_id !=0 and start_date >=':start_date' and due_date <=':due_date') 
				and sc.start_date >= ':start_date' and sc.due_date <=':due_date' 
				and sc.nm_usage_rights = '2' or sc.nm_usage_rights = '3'";
        if ($params['farming_id']) {
            $sql .= '
				and sc.farming_id = $params["farming_id"]';
        } elseif ($params['ekate']) {
            $sql .= 'and lk.ekate = $params["ekate"]';
        }
        $sql .= '
				) t1
			left join su_owners so on so.id = t1.owner_id 
			left join su_renta_types rt on t1.renta_id = rt.id
			group by t1.c_num';
        $sql = str_replace(':start_date', $params['payroll_from_date'], $sql);
        $sql = str_replace(':due_date', $params['payroll_to_date'], $sql);

        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function getOwnGrid($params)
    {
        $sql = ' with own_data as (';
        $col = implode(', ', $params['col']);
        $sql .= 'select ' . $col;
        $sql .= "
          from
            (((SELECT DISTINCT
                t1.*,
                kvs.ekate,
                round(cpr.contract_area :: numeric, 3) AS area,
                kvs.usable,
                c.id as c_id,
                c.c_num,
                c.start_date,
                c.farming_id
                FROM su_contracts_plots_rel cpr LEFT JOIN su_contracts C ON (C.ID = cpr.contract_id)
                LEFT JOIN su_contracts a on (a.parent_id = c.id AND a.active = true)
                LEFT JOIN layer_kvs kvs ON (kvs.gid = cpr.plot_id)
                LEFT JOIN (
                select  scpr.contract_id,
                        scpr.plot_id,
                        SUM(scpr.contract_area_for_sale) as sold_area,
                        MAX(scpr.contract_area) as total_area
                      FROM su_sales_contracts_plots_rel scpr INNER join su_sales_contracts sc
                        on (sc.id = scpr.sales_contract_id and sc.start_date <= ':start_date')
                        group by scpr.contract_id, scpr.plot_id) as t1 on (t1.contract_id = c.id and t1.plot_id = kvs.gid)
              WHERE (kvs.is_edited = FALSE AND (kvs.edit_active_from <= ':due_date' OR kvs.edit_active_from IS NULL)) AND
                case when t1.total_area is not null
                then t1.total_area > t1.sold_area
                else c.id > 0 end AND C.nm_usage_rights = 1 AND C.start_date <= ':start_date' AND C.is_annex = 'false' AND
                c.is_sublease = false AND c.active =true))
            UNION ((SELECT DISTINCT
                t1.*,
                kvs.ekate,
                round(cpr.contract_area :: numeric, 3) AS area,
                kvs.usable,
                c.id as c_id,
                c.c_num,
                c.start_date,
                c.farming_id
              FROM su_contracts_plots_rel cpr LEFT JOIN su_contracts C ON (C.ID = cpr.contract_id)
                LEFT JOIN su_contracts a on (a.parent_id = c.id AND a.active = true)
                LEFT JOIN layer_kvs kvs ON (kvs.gid = cpr.plot_id)
                LEFT JOIN (
                 select scpr.contract_id,
                        scpr.plot_id,
                        SUM(scpr.contract_area_for_sale) as sold_area,
                        MAX(scpr.contract_area) as total_area
                       FROM su_sales_contracts_plots_rel scpr INNER join su_sales_contracts sc
                        on (sc.id = scpr.sales_contract_id and sc.start_date <= ':start_date')
                        group by scpr.contract_id, scpr.plot_id) as t1 on (t1.contract_id = c.id and t1.plot_id = kvs.gid)
               WHERE (kvs.is_edited = TRUE AND kvs.edit_active_from >= ':due_date') AND case when t1.total_area is not null
                then t1.total_area > t1.sold_area
                else c.id > 0 end AND
                C.nm_usage_rights = 1 AND C.start_date <= ':start_date' AND C.is_annex = 'false' AND
                c.is_sublease = false AND c.active =true))
            )own_grid
            where true 
           ";
        if ($params['payroll_farming']) {
            $sql .= "
            and farming_id = ':payroll_farming'
            ";
            $sql = str_replace(':payroll_farming', $params['payroll_farming'], $sql);
        }
        if ($params['payroll_ekate']) {
            $sql .= "
            and ekate = ':payroll_ekate'
            ";
            $sql = str_replace(':payroll_ekate', $params['payroll_ekate'], $sql);
        }
        if ($params['group_by']) {
            $group = implode(', ', $params['group_by']);
            $sql .= 'group by ' . $group;
        }
        $sql .= ' )';
        $sql = str_replace(':start_date', $params['payroll_from_date'], $sql);
        $sql = str_replace(':due_date', $params['payroll_to_date'], $sql);
        if ('line_chart' == $params['query_type']) {
            $sql .= 'select start_date, sum(area) over (order by start_date asc rows between unbounded preceding and current row) from own_data';
        } elseif ('bar_chart' == $params['query_type']) {
            $sql .= 'select farming, ekate, sum(area) as suma from own_data group by ekate, farming';
        } else {
            $sql .= 'select * from own_data';
        }
        if ($params['order_by']) {
            $order = implode(', ', $params['order_by']);
            $sql .= ' order by ' . $order;
        }
        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function getRentContracts($params)
    {
        $sql = ' with contract_data as ';
        $sql .= "
        (( SELECT distinct
ekate,
round(cpr.contract_area::numeric,3) as area,
case when c.is_sublease then c1.c_num else c.c_num end as c_num,
case when c.is_sublease then c1.id else c.id end as c_id,
case when c.is_sublease then c.id else null end as sublease_id,
case when c.is_sublease then c.c_num else null end as sublease_num,
c.sv_num,to_char(c.sv_date,'DD.MM.YYYY') as sv_date,
c.nm_usage_rights as c_type,
c.farming_id as farming_id,
c.renta,c.is_sublease,
to_char((case when c.is_sublease then c.due_date else cpr.contract_end_date end),'DD.MM.YYYY') AS due_date,
pf.farming_id as arendodatel,
string_agg(DISTINCT(CASE WHEN owner_type = 1 THEN name || ' ' || surname || ' ' || lastname ELSE company_name END), ',<br />') as owner_names,
to_char((c.start_date),'DD.MM.YYYY') AS subl_start_date,
to_char((c.due_date),'DD.MM.YYYY') AS subl_due_date 
				FROM su_contracts C
                left JOIN su_contracts a ON(a.parent_id = c.id AND a.active = true and a.start_date<= ':due_date' and a.due_date >= ':start_date')
				INNER JOIN su_contracts_plots_rel cpr ON(cpr.contract_id = (CASE WHEN a.id IS NULL THEN c.id ELSE a.id END))
				INNER JOIN su_contracts c1 ON(c1.id = cpr.contract_id)
				LEFT JOIN layer_kvs kvs ON (cpr.plot_id = kvs.gid)
				LEFT JOIN su_plots_farming_rel PF ON(pf.pc_rel_id = cpr.id)
				LEFT JOIN su_plots_owners_rel PO ON(po.pc_rel_id = cpr.id)
				LEFT JOIN su_owners O ON(o.id = po.owner_id)
                LEFT JOIN 
                    (select scpr.contract_id, scpr.plot_id, SUM(scpr.contract_area_for_sale) as sold_area, MAX(scpr.contract_area) as total_area
                        FROM su_sales_contracts_plots_rel scpr
                        INNER join su_sales_contracts sc on(sc.id = scpr.sales_contract_id and sc.start_date <= ':start_date')
                        group by scpr.contract_id, scpr.plot_id
                    ) as t2 on (t2.contract_id = c.id and t2.plot_id = kvs.gid)
                LEFT JOIN 
                    ( select spc.pc_rel_id from su_subleases_plots_contracts_rel as spc 
                        join su_contracts as c2 on (c2.id = spc.sublease_id)
                        where  
                          c2.due_date >= ':due_date'  
                          and c2.start_date <= ':start_date'
                          and c2.is_sublease = true 
                          and c2.active = true
                    ) as t1 on (t1.pc_rel_id = cpr.id)
				WHERE (kvs.is_edited = FALSE AND (kvs.edit_active_from <= ':start_date' OR kvs.edit_active_from IS NULL))
				AND (pf.farming_id <> c.farming_id OR pf.farming_id is null)
				AND c.nm_usage_rights <> '1'
				AND c.nm_usage_rights <> '4'
                AND case when t2.total_area is not null then t2.total_area > t2.sold_area else c.id > 0 end
				AND t1.pc_rel_id is NULL  AND (c.start_date <= ':due_date' OR a.start_date <= ':due_date') AND (c.due_date IS NULL OR c.due_date >= ':start_date' OR a.due_date >= ':start_date') AND c.is_annex = :is_annex AND c.active = :active AND c1.active = :c1_active GROUP BY kvs.gid, cpr.id, c.id, pf.id, cpr.contract_area, cpr.price_per_acre, cpr.price_sum, c1.id) 
				UNION
				  ( SELECT distinct
				  ekate,
				  round(cpr.contract_area::numeric,3) as area,
				  case when c.is_sublease then c1.c_num else c.c_num end as c_num,
				  case when c.is_sublease then c1.id else c.id end as c_id,
				  case when c.is_sublease then c.id else null end as sublease_id,
				  case when c.is_sublease then c.c_num else null end as sublease_num,
				  c.sv_num,to_char(c.sv_date,'DD.MM.YYYY') as sv_date,
				  c.nm_usage_rights as c_type,
				  c.farming_id as farming_id,
				  c.renta,
				  c.is_sublease,
				  to_char((case when c.is_sublease then c.due_date else cpr.contract_end_date end),'DD.MM.YYYY') AS due_date,
				  pf.farming_id as arendodatel,string_agg(DISTINCT(CASE WHEN owner_type = 1 THEN name || ' ' || surname || ' ' || lastname ELSE company_name END), ',<br />') as owner_names,
				  to_char((c.start_date),'DD.MM.YYYY') AS subl_start_date,
				  to_char((c.due_date),'DD.MM.YYYY') AS subl_due_date 
				FROM su_contracts C
                left JOIN su_contracts a ON(a.parent_id = c.id AND a.active = true and a.start_date<= ':due_date' and a.due_date >= ':start_date')
				INNER JOIN su_contracts_plots_rel cpr ON(cpr.contract_id = (CASE WHEN a.id IS NULL THEN c.id ELSE a.id END))
				INNER JOIN su_contracts c1 ON(c1.id = cpr.contract_id)
				LEFT JOIN layer_kvs kvs ON (cpr.plot_id = kvs.gid)
				LEFT JOIN su_plots_farming_rel pf ON(pf.pc_rel_id = cpr.id)
				LEFT JOIN su_plots_owners_rel po ON(po.pc_rel_id = cpr.id)
				LEFT JOIN su_owners o ON(o.id = po.owner_id)
                LEFT JOIN 
                    (select scpr.contract_id, scpr.plot_id, SUM(scpr.contract_area_for_sale) as sold_area, MAX(scpr.contract_area) as total_area
                        FROM su_sales_contracts_plots_rel scpr
                        INNER join su_sales_contracts sc on(sc.id = scpr.sales_contract_id and sc.start_date <= ':start_date')
                        group by scpr.contract_id, scpr.plot_id
                    ) as t2 on (t2.contract_id = c.id and t2.plot_id = kvs.gid)
                LEFT JOIN 
                    ( select spc.pc_rel_id from su_subleases_plots_contracts_rel as spc 
                        join su_contracts as c2 on (c2.id = spc.sublease_id)
                        where  
                          c2.due_date >= ':due_date'  
                          and c2.start_date <= ':start_date'
                          and c2.is_sublease = true 
                          and c2.active = true
                    ) as t1 on (t1.pc_rel_id = cpr.id)
				WHERE (kvs.is_edited = TRUE AND kvs.edit_active_from >= ':due_date')
				AND (pf.farming_id <> c.farming_id OR pf.farming_id is null)
				AND c.nm_usage_rights <> '1'
				AND c.nm_usage_rights <> '4'
                AND case when t2.total_area is not null then t2.total_area > t2.sold_area else c.id > 0 end
				AND t1.pc_rel_id is NULL
				AND (c.start_date <= ':due_date' OR a.start_date <= ':due_date') AND (c.due_date IS NULL OR c.due_date >= ':start_date' OR a.due_date >= ':start_date') AND c.is_annex = :is_annex AND c.active = :active AND c1.active = :c1_active GROUP BY kvs.gid, cpr.id, c.id, pf.id, cpr.contract_area, cpr.price_per_acre, cpr.price_sum, c1.id ))";

        $sql = str_replace(':start_date', $params['report_date_from'], $sql);
        $sql = str_replace(':due_date', $params['report_date_to'], $sql);
        $sql = str_replace(':is_annex', 'false', $sql);
        $sql = str_replace(':active', 'true', $sql);
        $sql = str_replace(':c1_active', 'true', $sql);
        // $sql = str_replace(':nm_usage_rights', '2', $sql);

        $col = implode(', ', $params['col']);
        $sql .= 'select ' . $col;
        $sql .= ' from contract_data';
        $sql .= ' where true ';
        if ($params['where']) {
            $where = implode(' ', $params['where']);
            $sql .= $where;
        }
        if ($params['group_by']) {
            $group = implode(', ', $params['group_by']);
            $sql .= ' group by ' . $group;
        }
        if ($params['order_by']) {
            $order = implode(', ', $params['order_by']);
            $sql .= ' order by ' . $order;
        }
        if ($params['grid']) {
            $sql = "select * from ({$sql}).t3 group by t3.c_num";
        }
        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function getSubleasedContracts($params)
    {
        $sql = "
        with contract_data as(
(
	select
			c1.nm_usage_rights as rights,
            c.nm_usage_rights as s_rights,
            c.id as c_id,
            c.c_num,
            contract_area,
            c1.farming_id,
            c.start_date,
            c.due_date,
            c.active,
            concat(co.name , ' ',co.lastname) as name,
            c.farming_id as farming,
            kvs.*
		from
			layer_kvs kvs inner join su_contracts_plots_rel pc on
			(
				pc.plot_id = kvs.gid
			) inner join su_subleases_plots_contracts_rel spc on
			(
				spc.pc_rel_id = pc.id
			) inner join su_contracts c on
			(
				c.id = spc.sublease_id
			) inner join su_contracts c1 on
			(
				c1.id = pc.contract_id
			) left join su_contracts_contragents cc on
			(
				cc.contract_id = spc.sublease_id
			) left join su_owners co on
			(
				co.id = cc.owner_id
			) left join su_plots_owners_rel po on
			(
				po.pc_rel_id = pc.id
			) left join su_owners o on
			(
				o.id = po.owner_id
			) left join su_contracts_farming_contragents fc on
			(
				fc.contract_id = spc.sublease_id
			) left join su_contracts a on
			(
				a.parent_id = c1.id
				and a.active = true
			) left join(
				select
					scpr.contract_id,
					scpr.plot_id,
					sum( scpr.contract_area_for_sale ) as sold_area,
					max( scpr.contract_area ) as total_area
				from
					su_sales_contracts_plots_rel scpr inner join su_sales_contracts sc on
					(
						sc.id = scpr.sales_contract_id
						and sc.start_date >= ':start_date'
						and sc.due_date >= ':due_date'
					)
				group by
					scpr.contract_id,
					scpr.plot_id
			) as t2 on
			(
				t2.contract_id = c.id
				and t2.plot_id = kvs.gid
			)
		where
			case
				when t2.total_area is not null then t2.total_area > t2.sold_area
				else c.id > 0
			end
			and kvs.is_edited = false
			and(
				kvs.edit_active_from <= c.start_date
				or kvs.edit_active_from is null
			)
			and c.is_sublease =:is_sublease
			and c.active =:active
			and true
	)
union(
	select
		c1.nm_usage_rights as rights,
		c.nm_usage_rights as s_rights,
		c.id as c_id,
		c.c_num,
		contract_area,
		c1.farming_id,
		c.start_date,
		c.due_date,
		c1.active,
		concat(co.name , ' ',co.lastname) as name,
		c.farming_id as farming,
		kvs.*
	from
		layer_kvs kvs inner join su_contracts_plots_rel pc on
		(
			pc.plot_id = kvs.gid
		) inner join su_subleases_plots_contracts_rel spc on
		(
			spc.pc_rel_id = pc.id
		) inner join su_contracts c on
		(
			c.id = spc.sublease_id
		) inner join su_contracts c1 on
		(
			c1.id = pc.contract_id
		) left join su_contracts_contragents cc on
		(
			cc.contract_id = spc.sublease_id
		) left join su_owners co on
		(
			co.id = cc.owner_id
		) left join su_plots_owners_rel po on
		(
			po.pc_rel_id = pc.id
		) left join su_owners o on
		(
			o.id = po.owner_id
		) left join su_contracts_farming_contragents fc on
		(
			fc.contract_id = spc.sublease_id
		) left join su_contracts a on
		(
			a.parent_id = c1.id
			and a.active = true
		) left join(
			select
				scpr.contract_id,
				scpr.plot_id,
				sum( scpr.contract_area_for_sale ) as sold_area,
				max( scpr.contract_area ) as total_area
			from
				su_sales_contracts_plots_rel scpr inner join su_sales_contracts sc on
				(
					sc.id = scpr.sales_contract_id
					and sc.start_date >= ':start_date'
					and sc.due_date >= ':due_date'
				)
			group by
				scpr.contract_id,
				scpr.plot_id
		) as t2 on
		(
			t2.contract_id = c.id
			and t2.plot_id = kvs.gid
		)
	where
		case
			when t2.total_area is not null then t2.total_area > t2.sold_area
			else c.id > 0
		end
		and kvs.is_edited = true
		and kvs.edit_active_from > now()
		and c.is_sublease =:is_sublease
		and c.active =:active
		and true
)
)
";
        $sql .= 'select ';
        $col = implode(', ', $params['col']);
        $sql .= $col;
        $sql .= " from contract_data
        where start_date  <= ':due_date'
        and due_date >= ':start_date'
        and rights :ownership
        ";
        if ($params['rights']) {
            $sql .= "and s_rights = ':rghts'";
        }
        if ($params['farming']) {
            $sql .= "and farming_id = ':farming_id'";
        }
        if ($params['ekate']) {
            $sql .= "and farming_id = ':farming_id'";
        }
        if ($params['type']) {
            $sql .= "and farming_id = ':farming_id'";
        }
        if ($params['group']) {
            $group = implode(', ', $params['group']);
            $sql .= '      group by  ' . $group;
        }

        $sql = str_replace(':start_date', $params['report_date_from'], $sql);
        $sql = str_replace(':due_date', $params['report_date_to'], $sql);
        $sql = str_replace(':is_sublease', 'true', $sql);
        $sql = str_replace(':active', 'true', $sql);
        $sql = str_replace(':rights', $params['rights'], $sql);
        $sql = str_replace(':farming_id', $params['farming_id'], $sql);
        $sql = str_replace(':farming_id', $params['ekate'], $sql);
        $sql = str_replace(':ownership', $params['ownership'], $sql);

        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }
}
