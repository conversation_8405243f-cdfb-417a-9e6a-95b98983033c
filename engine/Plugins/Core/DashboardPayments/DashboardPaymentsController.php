<?php

namespace TF\Engine\Plugins\Core\DashboardPayments;

use TF\Engine\Kernel\StringHelper;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * DashboardPaymentsController class file.
 */
// Prado::using('Plugins.Core.Base.BaseController');
// Prado::using('Plugins.Core.DashboardPayments.*');

class DashboardPaymentsController extends UserDbController
{
    /**
     * Returns user data.
     *
     * @param array $username
     *
     * @return array
     */
    public $DbHandler;
    public $Database;
    /** @var string $String */
    public $String;

    public function __construct($database)
    {
        $this->DbHandler = new DashboardPaymentsModel($database);
        $this->Database = $database;
        $this->StringHelper = new StringHelper();
    }

    public function getPaymentsData($options, $counter, $returnOnlySQL)
    {
        return $this->DbHandler->getPayments($options, $counter, $returnOnlySQL);
    }

    public function getContractDates($options, $counter, $returnOnlySQL)
    {
        return $this->DbHandler->getContractDates($options, $counter, $returnOnlySQL);
    }

    public function conection()
    {
        return $this->DbHandler->connection();
    }

    public function rentaNatura($params)
    {
        return $this->DbHandler->rentNatura($params);
    }

    public function getContractsGrid($params)
    {
        return $this->DbHandler->getContractsGrid($params);
    }

    public function getOwnGrid($params)
    {
        return $this->DbHandler->getOwnGrid($params);
    }

    public function getRentContracts($params)
    {
        return $this->DbHandler->getRentContracts($params);
    }

    public function getSubleasedContracts($params)
    {
        return $this->DbHandler->getSubleasedContracts($params);
    }
}
