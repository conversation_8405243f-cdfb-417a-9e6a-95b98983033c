<?php

namespace TF\Engine\Plugins\Core\UserDbPlotCategoriesType;

use PDO;
use TF\Engine\Plugins\Core\UserDb\UserDbModel;

class UserDbPlotCategoriesTypeModel extends UserDbModel
{
    public function getPlotCategories(): array
    {
        $sql = 'SELECT id::varchar, title as name FROM su_plot_categories';
        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function getPlotCategoryTitle(string $code): string
    {
        $sql = 'SELECT public.get_plot_category_by_id(:code) AS title;';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindValue(':code', $code, PDO::PARAM_STR);

        return $cmd->query()->readColumn(0);
    }
}
