<?php

namespace TF\Engine\Plugins\Core\UserDbPlotCategoriesType;

use TF\Engine\Plugins\Core\UserDb\UserDbController;

class UserDbPlotCategoriesTypeController extends UserDbController
{
    /**
     * @var UserDbPlotCategoriesTypeModel
     */
    public $DbHandler;
    public $Database;

    public function __construct($database)
    {
        $this->DbHandler = new UserDbPlotCategoriesTypeModel($database);
        $this->Database = $database;
    }

    public function getPlotCategories(): array
    {
        return $this->DbHandler->getPlotCategories();
    }

    public function getPlotCategoryTitle(string $code): string
    {
        return $this->DbHandler->getPlotCategoryTitle($code);
    }
}
