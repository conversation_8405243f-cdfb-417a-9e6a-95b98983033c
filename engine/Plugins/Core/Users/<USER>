<?php

namespace TF\Engine\Plugins\Core\Users;

use Exception;
use Prado\Prado;
use TF\Application\Common\Config;
use TF\Engine\Kernel\Mail;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Base\BaseController;

/**
 * UsersController class file.
 *
 * <AUTHOR>
 */

/**
 * UsersController class.
 *
 * @property UsersModel $DbHandler
 * @property File $File
 */
class UsersController extends BaseController
{
    /**
     * @var UsersModel
     */
    public $DbHandler;

    /**
     * Returns user data.
     *
     * @param array $username
     *
     * @return array
     */
    public function getUserData($username)
    {
        return $this->DbHandler->getUserData($username);
    }

    /**
     * Returns user data.
     *
     * @param array $username
     * @param null|mixed $hash
     *
     * @return array
     */
    public function getUserDataById($id, $hash = null)
    {
        return $this->DbHandler->getUserDataById($id, $hash);
    }

    /**
     * @param [type] $username
     */
    public function getUserDataByUsername($username)
    {
        return $this->DbHandler->getUserDataByUsername($username);
    }

    /**
     * @param [string] $username
     */
    public function getUserRights($username)
    {
        return $this->DbHandler->getUserRights($username);
    }

    /**
     * @param [int] $userId
     */
    public function getOrganizationFarmings($organizationId, $filter = [])
    {
        return $this->DbHandler->getOrganizationFarmings($organizationId, $filter);
    }

    /**
     * Returns user data.
     *
     * @param array $username
     *
     * @return array
     */
    public function getUserDataByShopId($id)
    {
        return $this->DbHandler->getUserDataByShopId($id);
    }

    /**
     * Returns user data.
     *
     * @param array $username
     *
     * @return array
     */
    public function getUserNumberBySubDomain($value)
    {
        return $this->DbHandler->getUserNumberBySubDomain($value);
    }

    /**
     * Gets user exist.
     *
     * @param string $username - the name of the user to login
     *
     * @return bool
     */
    public function getExistUser($username)
    {
        return $this->DbHandler->existUser($username);
    }

    /**
     * Gets user with email exist.
     *
     * @param string $email - the email of the user
     *
     * @return bool
     */
    public function getExistEmail($email)
    {
        return $this->DbHandler->existEmail($email);
    }

    public function getExistSubDomain($username)
    {
        return $this->DbHandler->getExistSubDomain($username);
    }

    /**
     * Adds new user.
     *
     * @param array $settings
     * @param array $cashiers
     *
     * @return int
     */
    public function addUser($settings = [])
    {
        return $this->DbHandler->addUser($settings);
    }

    /**
     * Adds new subuser.
     *
     * @param array $settings
     * @param array $cashiers
     *
     * @return int
     */
    public function addSubUser($settings = [])
    {
        return $this->DbHandler->addSubUser($settings);
    }

    /**
     * Edits an existing user.
     *
     * @param int $userID
     * @param array $fields
     * @param array $cashiers
     *
     * @return int
     */
    public function editUser($userID, $fields = [])
    {
        $transaction = $this->DbHandler->startTransaction();

        try {
            $success = $this->DbHandler->editItem($this->DbHandler->tableName, array_keys($fields), array_values($fields), ['id'], [$userID]);

            $transaction->commit();

            return $success;
        } catch (Exception $e) {
            $transaction->rollBack();

            throw $e;
        }
    }

    /**
     * Deletes users from the db.
     *
     * @param array $arrayID
     */
    public function deleteUsers($arrayID = [])
    {
        foreach ($arrayID as $id) {
            $data = $this->getUserDataById($id);
            // if userlevel is 3 then he is subuser and does not have database
            if (3 != $data['level']) {
                $this->DbHandler->dropDatabase($data['database']);
            }
        }

        $transaction = $this->DbHandler->startTransaction();

        try {
            foreach ($arrayID as $id) {
                $this->DbHandler->deleteItem($this->DbHandler->tableName, [$this->DbHandler->fieldName], [$id]);
            }

            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();

            throw $e;
        }
    }

    /**
     * Gets items, which should be displayed on home page.
     * Expected array $settings:.
     *
     * $options = array
     * 	(
     * 		'flags' -> array
     * 			(
     * 				'equality' -> true or false
     * 				'strict' -> true or false,
     * 				'values' -> array of flags
     * 			),
     * 		'place'  ->'backend' or 'frontend'
     * 		'return' -> returned fields in query
     * 		'keywords' -> keywords for searching
     * 		'offset' -> offset distance in query
     * 		'limit' -> limit in query
     * 	);
     *
     * @param array $options
     *
     * @return array
     */
    public function getHomeItems(&$settings = [])
    {
        $orderby = $this->getOrderBy($settings['place']);
        $ordertype = $this->getOrderType($settings['place']);

        $options = [
            'return' => $settings['return'],
            'orderby' => '' != $settings['orderby'] ? $settings['orderby'] : $orderby,
            'ordertype' => '' != $settings['ordertype'] ? $settings['ordertype'] : $ordertype,
            'offset' => $settings['offset'],
            'limit' => $settings['limit'],
            'parent_id' => $settings['parent_id'],
        ];
        if ($settings['username']) {
            $name = '%' . $settings['username'] . '%';
            $options['custom'] = [
                'fields' => ['t.username'],
                'values' => [$name],
            ];
        }

        if ($settings['email']) {
            $email = '%' . $settings['email'] . '%';
            $options['custom'] = [
                'fields' => ['t.email'],
                'values' => [$email],
            ];
        }

        if ($settings['paid_support']) {
            $paid_support = '%' . $settings['paid_support'] . '%';
            $options['paid_support'] = $paid_support;
        }

        $result = [];
        if (isset($settings['count'])) {
            $options['count'] = $settings['count'];
            $result[$settings['count'] ? 'count' : 'data'] = $this->DbHandler->getHomeItems($options);
        } else {
            $options['count'] = true;
            $result['count'] = $this->DbHandler->getHomeItems($options);
            $options['count'] = false;
            $result['data'] = $this->DbHandler->getHomeItems($options);
        }

        return $result;
    }

    public function getAutoComplete($keywords)
    {
        return $this->DbHandler->getAutoComplete($keywords);
    }

    public function getUserParentNumberByNumber($number)
    {
        return $this->DbHandler->getUserParentNumberByNumber($number);
    }

    public function getReportStatusByUserId($id, $month, $year)
    {
        return $this->DbHandler->getReportStatusByUserId($id, $month, $year);
    }

    public function getBonusByYear($userid, $bonuslevel, $year, $parent, $monthCur = false)
    {
        $monthArray = [];

        if (!$monthCur) {
            $monthCur = date('m');
        }

        for ($month = $monthCur; $month <= $monthCur; $month++) {
            $options = [
                'return' => ['t.id', 'r.own_amount', 'r.group_amount', 'r.bonus_level'],
                'parent_number' => $parent,
                'show_report' => true,
                'month' => $month,
                'year' => $year,
            ];

            $result = $this->getHomeItems($options);

            $users = $result['data'];

            $totalMonthGroupAmount = 0;
            foreach ($users as $user) {
                $bonus_diff = (int) $bonuslevel - (int) $user['bonus_level'];
                $currUserAmount = 0;

                if ($bonus_diff > 0) {
                    $userAmount = $user['own_amount'] + $user['group_amount'];
                    $currUserAmount = round($userAmount * $bonus_diff / 100, 2);
                }
                $totalMonthGroupAmount += $currUserAmount;
            }

            $dataCurUser = $this->DbHandler->getReportStatusByUserId($userid, $month, $year);

            $monthArray[$month - 1]['month'] = $month;
            $monthArray[$month - 1]['year'] = $year;
            $monthArray[$month - 1]['group_amount'] = $totalMonthGroupAmount;
            $monthArray[$month - 1]['own_amount'] = $dataCurUser['own_amount'] * $dataCurUser['bonus_level'] / 100;
        }

        return $monthArray;
    }

    public function getDownloadItemData($userid, $month, $year)
    {
        return $this->DbHandler->getDownloadItemData($userid, $month, $year);
    }

    public function addItemToQueue($options = [])
    {
        $transaction = $this->DbHandler->startTransaction();

        try {
            $id = $this->DbHandler->addItem($this->DbHandler->tableNameQueue, array_keys($options), array_values($options));
            $this->onAddItem($id, $options);

            $transaction->commit();

            return $id;
        } catch (Exception $e) {
            $transaction->rollBack();

            throw $e;
        }
    }

    public function getMaxUserId()
    {
        return $this->DbHandler->getMaxUserId();
    }

    public function getEkatteComboboxData($options, $user_id)
    {
        // function splits when ekatte information is required
        $used_ekatte = [];
        if ('ekatte' == $options['tablename']) {
            $used_ekatte = $this->DbHandler->getAlreadyUsedEkatteID($user_id);
        }

        return $this->DbHandler->getEkatteComboboxData($options, $used_ekatte);
    }

    public function addNewUserEkatteRelation($user_id, $ekatte_id, $oblas_id)
    {
        $this->DbHandler->addNewUserEkatteRelation($user_id, $ekatte_id);

        $code = $this->getEkatteCode($ekatte_id);
        $code = $code['ekatte_code'];

        $database = $this->DbHandler->getItem($this->DbHandler->tableName, ['database'], ['id'], [$user_id]);
        $database = $database['database'];

        $tableName = $this->DbHandler->tableLayers;
        $data = $this->DbHandler->getItem($tableName, ['table_name'], ['farming', 'year', 'user_id', 'layer_type'], [86, 4, $user_id, 5]);
        $table_name = $data['table_name'];
    }

    public function getEkatteCode($id)
    {
        return $this->DbHandler->getItem($this->DbHandler->tableEkatte, ['ekatte_code'], ['id'], [$id]);
    }

    public function getEkatteName($ekatte_code)
    {
        return $this->DbHandler->getEkatteName($ekatte_code);
    }

    public function getEkatteNameMulti($options)
    {
        return $this->DbHandler->getEkatteNameMulti($options);
    }

    public function getAllEkatteData($options = [])
    {
        return $this->DbHandler->getAllEkatteData($options);
    }

    public function getAllEkateName($ekatte_codes)
    {
        $ekatte = [];

        $ekatte_data = $this->getEkatteNameMulti(
            [
                'return' => ['ekatte_code as code', 'ekatte_name as name'],
                'ekatte_codes' => $ekatte_codes,
            ]
        );
        foreach ($ekatte_data as $row) {
            $ekatte[$row['code']] = $row['name'];
        }

        return $ekatte;
    }

    public function getEkatteItems($user_id, $options)
    {
        return $this->DbHandler->getEkatteItems($user_id, $options);
    }

    public function getSubUsers($userId)
    {
        return $this->DbHandler->getSubUsers($userId);
    }

    public function getEkatteOnlyItems($options)
    {
        return $this->DbHandler->getEkatteOnlyItems($options);
    }

    public function deleteEkatteRelation($user_id, $ekatte_array)
    {
        $this->DbHandler->deleteEkatteRelation($user_id, $ekatte_array);
    }

    public function getUserByEmail($email)
    {
        return $this->DbHandler->getUserByEmail($email);
    }

    public function setUserHash($user_id)
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $randomString = '';
        for ($i = 0; $i <= 52; $i++) {
            $randomString .= $characters[rand(0, strlen($characters) - 1)];
        }

        return $this->DbHandler->setUserHash($user_id, $randomString);
    }

    public function checkHashData($user_id, $hash)
    {
        return $this->DbHandler->checkHashData($user_id, $hash);
    }

    public function setNewPassword($user_id, $hash, $password)
    {
        $this->DbHandler->setNewPassword($user_id, $hash, $password);
    }

    /**
     * @param array $options
     *
     * @throws phpmailerException
     */
    public function sendForgottenPasswordEmail($options)
    {
        $message = '
    		<html>
    			<body>
    				<p>Здравейте, ' . $options['name'] . '</br>' . '
                        Във връзка с направената от Вас заявка за промяна на паролата, ние екипът на Техно Фарм Ви информираме, че за потребителско име '
                        . $options['username'] . ' , можете да промените паролата си от следния ЛИНК: <p>
    				<a href=' . $options['change_link'] . '>' . $options['change_link'] . '</a>
    			</body>
    		</html>
    	';

        $mail = new Mail();
        $mail->sendMail($options['email'], 'Промяна на парола', $message, 'TechnoFarm');
    }

    public function addCropLayerProcessing($userid, $userdatabase, $group_id, $id)
    {
        return $this->DbHandler->addItem($this->DbHandler->tableCropLayersProcessing, ['user_id', 'database', 'group_id', 'item_id'], [$userid, $userdatabase, $group_id, $id]);
    }

    public function getCropLayerForProcessing()
    {
        return $this->DbHandler->getCropLayerForProcessing();
    }

    public function setCropLayerProcessed($id)
    {
        $this->DbHandler->setCropLayerProcessed($id);
    }

    public function addCropLayerData($options)
    {
        $this->DbHandler->addItem($options['tablename'], array_keys($options['mainData']), array_values($options['mainData']));
    }

    public function addOverlapItemForProcessing($userid, $userdatabase, $id)
    {
        return $this->DbHandler->addItem($this->DbHandler->tableOverlaps, ['user_id', 'database', 'item_id'], [$userid, $userdatabase, $id]);
    }

    public function getOverlapsForProcessing()
    {
        return $this->DbHandler->getOverlapsForProcessing();
    }

    public function setOverlapProcessed($id)
    {
        $this->DbHandler->setOverlapProcessed($id);
    }

    public function addUserRights($options)
    {
        $this->DbHandler->addItem($this->DbHandler->tableUserRights, array_keys($options), array_values($options), false);
    }

    public function addUserToSystem($options)
    {
        $this->DbHandler->addItem($this->DbHandler->tableSystemUsers, array_keys($options), array_values($options), false);
    }

    public function getChildAccountCount($parent_id)
    {
        return $this->DbHandler->getChildAccountCount($parent_id);
    }

    public function getUserRightsByUserID($user_id)
    {
        return $this->DbHandler->getUserRightsByUserID($user_id);
    }

    public function getUserOrganizationRights(int $userId, int $organizationId)
    {
        return $this->DbHandler->getUserOrganizationRights($userId, $organizationId);
    }

    public function deleteUserRightsByUserID($userId, $organizationId = null, $rightId = null)
    {
        $this->DbHandler->deleteUserRightsByUserID($userId, $organizationId, $rightId);
    }

    public function updateUserSystemPassword($user_id, $password)
    {
        $this->DbHandler->editItem($this->DbHandler->tableSystemUsers, ['password'], [$password], ['user_id'], [$user_id]);
    }

    public function addAgreementItemForProcessing($userid, $userdatabase, $id)
    {
        return $this->DbHandler->addItem($this->DbHandler->tableAgreements, ['user_id', 'database', 'item_id'], [$userid, $userdatabase, $id]);
    }

    public function getAgreementsForProcessing()
    {
        return $this->DbHandler->getAgreementsForProcessing();
    }

    public function getPayrollExprotsForProcessing()
    {
        return $this->DbHandler->getPayrollExprotsForProcessing();
    }

    public function setAgreementProcessed($id)
    {
        $this->DbHandler->setAgreementProcessed($id);
    }

    public function updateUsersData($options)
    {
        $this->DbHandler->updateUsersData($options);
    }

    public function getUsers($options = [], $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getUsers($options, $counter, $returnOnlySQL);
    }

    public function addCustomItem($options)
    {
        $column_names = array_keys($options['mainData']);
        $column_values = array_values($options['mainData']);
        $tablename = $options['tablename'];

        return $this->DbHandler->addItem($tablename, $column_names, $column_values);
    }

    public function getCoverageItemsForProcessing()
    {
        return $this->DbHandler->getCoverageItemsForProcessing();
    }

    public function setCoverageItemProcessed($id)
    {
        $this->DbHandler->setCoverageItemProcessed($id);
    }

    public function updateFarmingMol($updateMolParams)
    {
        $this->DbHandler->updateFarmingMol($updateMolParams);
    }

    public function deleteItemsByParams($options)
    {
        $this->DbHandler->deleteItemsByParams($options);
    }

    public function startTransaction($options = null)
    {
        return $this->DbHandler->startTransaction($options);
    }

    public function setCoverageItemStatus($id, $error, $message = '')
    {
        $this->DbHandler->setCoverageItemStatus($id, $error, $message);
    }

    public function getItemsByParams($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getItemsByParams($options, $counter, $returnOnlySQL);
    }

    public function getTableNameExist($database, $tablename)
    {
        return $this->DbHandler->getTableNameExist($database, $tablename);
    }

    public function getColumnNameExist($database, $tablename, $columnName)
    {
        return $this->DbHandler->getColumnNameExist($database, $tablename, $columnName);
    }

    public function updateOrderedPlots($data)
    {
        $this->DbHandler->updateOrderedPlots($data);
    }

    public function getUserPayrollExports($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getUserPayrollExports($options, $counter, $returnOnlySQL);
    }

    public function setPayrollExportAsProcessed($id, $execution_time = null)
    {
        return $this->DbHandler->setPayrollExportAsProcessed($id, $execution_time);
    }

    public function setPayrollExportInProcess($id)
    {
        return $this->DbHandler->setPayrollExportInProcess($id);
    }

    public function setPayrollExportMessage($id, $message, $status = null)
    {
        return $this->DbHandler->setPayrollExportMessage($id, $message, $status);
    }

    public function getForIsakSchema($result)
    {
        $farmSchemas = $GLOBALS['Farming']['schema'];

        $schemasArr = [];
        $naturaSitecode = $result['natura_sitecode'];

        $schemasArr[] = (($result['sepp']) ? $farmSchemas[Config::SEPP]['name'] : null);
        $schemasArr[] = (($result['zdp']) ? $farmSchemas[Config::ZDP]['name'] : null);
        $schemasArr[] = (($result['pndp']) ? $farmSchemas[Config::PNDP]['name'] : null);
        $schemasArr[] = ($naturaSitecode ? 'Н2000'
         . ' - ' . $GLOBALS['Farming']['natura_zones'][$naturaSitecode]['name'] . ' (' . $naturaSitecode . ')' : null);
        $schemasArr[] = (($result['nr1']) ? $GLOBALS['Farming']['lfa_schema_types'][Config::NR1]['name'] : null);
        $schemasArr[] = (($result['nr2']) ? $GLOBALS['Farming']['lfa_schema_types'][Config::NR2]['name'] : null);
        $schemasArr[] = (($result['vps_type']) ? ('ВПС - ' . $GLOBALS['Farming']['vps_schema_types'][$result['vps_type']]['name']) : null);
        foreach ($schemasArr as $key => $schema) {
            if (empty($schema)) {
                unset($schemasArr[$key]);
            }
        }

        return implode(',', $schemasArr);
    }

    public function getSalesPersons()
    {
        return $this->DbHandler->getSalesPersons();
    }

    public function getUserModems($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getDevices($options, $counter, $returnOnlySQL);
    }

    public function getAllModems()
    {
        $deviceModule = Prado::getApplication()->getModule('deviceModule');

        return $deviceModule->getAllModems();
    }

    public function addUserModems($data, $user)
    {
        foreach ($data as $modemInfo) {
            $modemInfo['last_date'] = date('Y-m-d');
            $modemInfo['user_id'] = $user;
            $this->DbHandler->addModems($modemInfo, $user);
        }

        return;
    }

    public function removeUserModem($id)
    {
        return $this->DbHandler->removeModem($id);
    }

    public function getModemFiles($user_id, $status = null)
    {
        $fileInfo = [];
        $options = [
            'return' => ['serial', 'machine'],
            'where' => [
                'userId' => ['column' => 'user_id', 'compare' => '=', 'value' => $user_id],
            ]];
        $modems = $this->getUserModems($options);
        $deviceModule = Prado::getApplication()->getModule('deviceModule');

        foreach ($modems as $device) {
            $files = $deviceModule->getModemsFiles($device, $status);
            foreach ($files as $file) {
                array_push($fileInfo, $file);
            }
        }

        return $fileInfo;
    }

    public function loadModemFiles($filename, $destPath)
    {
        $deviceModule = Prado::getApplication()->getModule('deviceModule');
        $deviceModule->downloadFiles($filename, $destPath);
    }

    /**
     * @throws MTRpcException
     *
     * @return bool
     */
    public function allowLoginAs($loggedUser, $requestedUser)
    {
        if ($requestedUser['id'] === $loggedUser['id'] || !in_array($loggedUser['level'], array_keys(Config::$LOGIN_AS_POLICY))) {
            throw new MTRpcException('POLICY_FORBIDDEN_LOGIN_AS', -33853);
        }

        $loggedUserPolicy = Config::$LOGIN_AS_POLICY[$loggedUser['level']];

        if (!is_array($loggedUserPolicy)) {
            return false;
        }

        foreach ($loggedUserPolicy as $restriction) {
            $logged = $requestedUser[$restriction['self']];
            $requested = array_key_exists('otherValue', $restriction) ? $restriction['otherValue'] : $requestedUser[$restriction['other']];
            $reverse = array_key_exists('reverse', $restriction) && (bool)$restriction['reverse'];

            $a = $reverse ? $requested : $logged;
            $b = $reverse ? $logged : $requested;
            $op = $restriction['compare'];

            if (!$this->ArrayHelper->compareWithOperand($a, $b, $op)) {
                return false;
            }
        }

        return true;
    }

    public function getUserSubscriptions($id)
    {
        $options = [
            'tablename' => $this->DbHandler->tableSubscriptions,
            'where' => [
                'user_id' => ['column' => 'user_id', 'compare' => '=', 'value' => $id],
            ],
        ];

        return $this->DbHandler->getWithOptions($options);
    }

    public function setUserSubscriptions($userId, $subscriptions = [])
    {
        $existing = $this->getUserSubscriptions($userId);
        $existingKeys = $existing ? array_column($existing, 'subscription_usage_type') : [];

        foreach ($subscriptions as $type => $usage) {
            $existingIdx = array_search($type, $existingKeys);

            if (!is_int($existingIdx)) {
                $this->DbHandler->addUserSubscriptions($userId, $type, $usage);
            } elseif ($existing[$existingIdx]['subscription_usage_id'] != $usage) {
                $this->DbHandler->updateUserSubscriptions($existing[$existingIdx]['id'], $usage);
            }
        }
    }

    public function getSubscriptionsByType($type)
    {
        $options = [
            'tablename' => $this->DbHandler->subscriptionUsageBase . $type,
        ];

        return $this->DbHandler->getWithOptions($options);
    }
}
