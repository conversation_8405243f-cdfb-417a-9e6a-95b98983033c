<?php

namespace TF\Engine\Plugins\Core\Users;

use PDO;
use Prado\Prado;
use TF\Application\Common\Config;
use TF\Engine\Kernel\PdoHelper;
use TF\Engine\Plugins\Core\Base\BaseModel;

/**
 * UsersModel class file.
 *
 * <AUTHOR>
 */
// Prado::using('Plugins.Core.Base.BaseModel');

/**
 * UsersModel class.
 */
class UsersModel extends BaseModel
{
    public $tableNameReport = '';
    public $tableNamePDF = '';
    public $tableNameQueue = '';
    public $tableUserEkatteRel = false;
    public $tableEkatte = false;
    public $tableOblasti = false;
    public $tableObshtini = false;
    public $tableKmetstva = false;
    public $tablePlotOwners = false;
    public $tableLayers = false;
    public $tableCropLayersProcessing = false;
    public $tableOverlaps = false;
    public $tableSystemUsers = false;
    public $tableUserRights = false;
    public $tableAgreements = false;
    public $tableCoverage = false;
    public $tableCropCodes = false;
    public $tablePayrollExports = false;
    public $tableSalesPerson = false;
    public $tableUserMachine = false;
    public $tableSubscriptions = false;
    public $subscriptionUsageBase = false;

    /**
     * UsersModel constructor.
     *
     * @param null $tableName
     * @param null $fieldName
     */
    public function __construct($tableName = null, $fieldName = null)
    {
        parent::__construct($tableName, $fieldName);

        $this->tableName = $tableName;
        $this->fieldName = $fieldName;
        $this->tableUserEkatteRel = DEFAULT_DB_PREFIX . 'users_ekatte_rel';
        $this->tableEkatte = DEFAULT_DB_PREFIX . 'ekatte';
        $this->tableObshtini = DEFAULT_DB_PREFIX . 'obshtini';
        $this->tableOblasti = DEFAULT_DB_PREFIX . 'oblasti';
        $this->tableKmetstva = DEFAULT_DB_PREFIX . 'kmetstva';
        $this->tableLayers = DEFAULT_DB_PREFIX . 'users_layers';
        $this->tablePlotOwners = DEFAULT_DB_PREFIX . 'owners';
        $this->tableCropLayersProcessing = DEFAULT_DB_PREFIX . 'users_croplayers';
        $this->tableOverlaps = DEFAULT_DB_PREFIX . 'users_overlaps';
        $this->tableSystemUsers = DEFAULT_DB_PREFIX . 'system_users';
        $this->tableUserRights = DEFAULT_DB_PREFIX . 'users_rights';
        $this->tableAgreements = DEFAULT_DB_PREFIX . 'users_agreements';
        $this->tableCoverage = DEFAULT_DB_PREFIX . 'users_coverage';
        $this->tableCropCodes = DEFAULT_DB_PREFIX . 'crop_codes';
        $this->tablePins = DEFAULT_DB_PREFIX . 'users_pins';
        $this->tablePayrollExports = DEFAULT_DB_PREFIX . 'users_payroll_exports';
        $this->tableSalesPerson = DEFAULT_DB_PREFIX . 'users_salesman';
        $this->tableUserMachine = DEFAULT_DB_PREFIX . 'user_machine';
        $this->tableSubscriptions = DEFAULT_DB_PREFIX . 'users_subscriptions';
        $this->subscriptionUsageBase = DEFAULT_DB_PREFIX . 'subscription_usage_';
    }

    /**
     * Gets user data by username.
     *
     * @param string $username - the name of the user
     *
     * @return array
     */
    public function getUserLogin($username)
    {
        $sql = 'SELECT * FROM ' . $this->tableName . '
        	WHERE username = :uname';

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':uname', $username, PDO::PARAM_STR);

        return $cmd->queryRow();
    }

    /**
     * Gets user data by username.
     *
     * @param string $username - the name of the user
     *
     * @return string
     */
    public function getUserData($username)
    {
        $usersTable = $this->tableName;
        $sql = "SELECT
            u.ID,
            u.NAME,
            u.address,
            u.phone,
            u.email,
            u.username,
            u.is_superadmin,
            coalesce(p.DATABASE, u.DATABASE) \"database\",
            u.parent_id,
            u.group_id,
            u.LEVEL,
            u.map_type,
            u.is_trial,
            u.track_token,
            coalesce(p.paid_support, u.paid_support) paid_support,
            u.app_version,
            u.app_critical_upd,
            u.login_token,
            u.keycloak_uid
        FROM
            {$usersTable} u
        LEFT JOIN {$usersTable} p ON u.parent_id = p.id AND p.level=:level
        WHERE
            LOWER(u.username) = :uname";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':uname', strtolower($username), PDO::PARAM_STR);
        $cmd->bindValue(':level', Config::USERS_ADMIN_FLAG);

        return $cmd->queryRow();
    }

    /**
     * Gets user data by number.
     *
     * @param string $number - the name of the user
     *
     * @return string
     */
    public function getUserParentNumberByNumber($number)
    {
        $sql = 'SELECT id, parent_number FROM ' . $this->tableName . ' WHERE number = :number';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':number', $number, PDO::PARAM_INT);

        return $cmd->queryRow();
    }

    /**
     * Gets user data by ID.
     *
     * @param string $id
     * @param null|mixed $hash
     *
     * @return string
     */
    public function getUserDataById($id, $hash = null)
    {
        $sql = 'SELECT t.* FROM ' . $this->tableName . ' t  WHERE t.id = :id';

        if ($hash) {
            $sql .= ' AND t.hash=:hash';
        }

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':id', $id, PDO::PARAM_INT);
        if ($hash) {
            $cmd->bindParameter(':hash', $hash, PDO::PARAM_INT);
        }

        return $cmd->queryRow();
    }

    public function getUserDataByUsername($username)
    {
        $sql = '
            SELECT 
                t.*,
                JSON_AGG(
                    rights.right_id
                ) as rights
            FROM ' . $this->tableName . ' t
            LEFT JOIN ' . $this->tableUserRights . ' as rights on (rights.user_id = t.id and rights.group_id = t.group_id)
            WHERE
                 t.username = :username
            GROUP BY t.id     
            ';

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':username', $username, PDO::PARAM_STR);

        return $cmd->queryRow();
    }

    public function getUserRights($username)
    {
        $sql = '
            SELECT 
                t.id as "userId",
                u.username as "organizationId",
                JSON_AGG(
                    rights.right_id
                ) as rights
            FROM ' . $this->tableName . ' t
            LEFT JOIN ' . $this->tableUserRights . ' as rights on (rights.user_id = t.id)
            INNER JOIN ' . $this->tableName . ' as u on u.id = rights.group_id
            WHERE
                t.username = :username
            GROUP BY t.id, u.username   
        ';

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':username', $username, PDO::PARAM_STR);

        return $cmd->query()->readAll();
    }

    public function getOrganizationFarmings($organizationId, array $filter = [])
    {
        $sql = 'SELECT 
                    f.id,
                    f.name,
                    f.group_id
                FROM su_users_farming as f
                WHERE f.group_id = :parentId';

        if (isset($filter['name'])) {
            $sql .= ' AND f.name = :name';
        }

        $cmd = $this->DbModule->createCommand($sql);

        $cmd->bindParameter(':parentId', $organizationId);

        if (isset($filter['name'])) {
            $cmd->bindParameter(':name', $filter['name'], PDO::PARAM_INT);
        }

        return $cmd->query()->readAll();
    }

    public function getUserDataByShopId($id)
    {
        $sql = 'SELECT t.* FROM ' . $this->tableName . ' t  WHERE t.shop_id = :id';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':id', $id, PDO::PARAM_INT);

        return $cmd->queryRow();
    }

    /**
     * Checks whether users exist.
     *
     * @param string $username - the name of the user
     *
     * @return bool
     */
    public function existUser($username)
    {
        $sql = 'SELECT COUNT(*) FROM ' . $this->tableName . ' WHERE username = :uname';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindValue(':uname', $username, PDO::PARAM_STR);

        return $cmd->queryScalar();
    }

    /**
     * Checks if there is an exising user with the specified email.
     *
     * @param string $email - the email of the user
     *
     * @return bool
     */
    public function existEmail($email)
    {
        $sql = 'SELECT COUNT(*) FROM ' . $this->tableName . ' WHERE email = :email';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindValue(':email', $email, PDO::PARAM_STR);

        return $cmd->queryScalar();
    }

    /**
     * Adds new nacs user.
     *
     * @param array $settings
     *
     * @return int
     */
    public function addUser(&$settings = [])
    {
        $userID = $this->addItem($this->tableName, array_keys($settings), array_values($settings));

        // get PostgreSQL version and kill db connections
        $sql = 'SELECT version()';
        $cmd = $this->DbModule->createCommand($sql);
        $results = $cmd->query()->readAll();

        $version = $results[0]['version'];

        $re = '/PostgreSQL (?<main_v>[\\d])+\\.(?<sub_version>[\\d]+)\\.[\\d]*/i';

        preg_match($re, $version, $matches);

        if (1 == $matches['sub_version']) {
            $sql = "SELECT pg_terminate_backend(pg_stat_activity.procpid) FROM pg_stat_activity WHERE pg_stat_activity.datname = 'TARGET_DB' AND procpid <> pg_backend_pid();";
        } else {
            $sql = "SELECT pg_terminate_backend(pg_stat_activity.pid) FROM pg_stat_activity WHERE pg_stat_activity.datname = 'new_users_db' AND pid <> pg_backend_pid();";
        }
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();

        $sql = 'CREATE DATABASE ' . $settings['database'] . ' TEMPLATE new_users_db';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();

        return $userID;
    }

    public function addSubUser(&$settings = [])
    {
        $userID = $this->addItem($this->tableName, array_keys($settings), array_values($settings));

        return $userID;
    }

    public function dropDatabase($database)
    {
        $sql = 'DROP DATABASE ' . $database;
        $cmd = $this->DbModule->createCommand($sql);
        @$cmd->execute();
    }

    /**
     * Gets home items data.
     * Expected $option array:
     * $options = array
     * 	(
     * 		'count'     -> true or false
     * 		'return'    -> return fields
     * 		'orderby'   -> order field,
     * 		'ordertype' -> order type: 'ASC' or 'DESC'
     * 		'offset'    -> offset distance in query
     * 		'limit'     -> limit in query
     * 	);.
     *
     * @param string $options
     *
     * @return array
     */
    public function getHomeItems(&$options = [])
    {
        $count = $options['count'];
        $return = $count ? 'COUNT(*)' : implode(', ', $options['return']);
        $customFields = $options['custom']['fields'];
        $customValues = $options['custom']['values'];
        $orderBy = $options['orderby'];
        $orderType = $options['ordertype'];
        $limit = $options['limit'];
        $offset = $options['offset'];

        $table = $this->tableName;
        $tableReport = $this->tableNameReport;
        $sql = "SELECT {$return} FROM {$table} t
                LEFT JOIN su_users p on p.id = t.parent_id";

        $sql .= ' WHERE true';
        if ($options['parent_id']) {
            $sql .= ' AND t.parent_id = :parent_id';
        }

        if ($options['paid_support']) {
            $sql .= ' AND t.paid_support = :paid_support';
        }

        $customQuery = $this->generateSearchQuery('sql', $customFields);
        if (!empty($customQuery)) {
            $sql .= " AND {$customQuery} ";
        }

        if (!$count and !empty($orderType) and !empty($orderBy)) {
            if (empty($orderType)) {
                $orderType = 'ASC';
            }
            $sql .= " ORDER BY {$orderBy} {$orderType} nulls last";
        }

        if (!$count and isset($limit) and isset($offset)) {
            $sql .= " LIMIT {$limit}";
            $sql .= " OFFSET {$offset}";
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($options['parent_id']) {
            $cmd->bindParameter(':parent_id', $options['parent_id']);
        }

        if ($options['paid_support']) {
            $cmd->bindParameter(':paid_support', $options['paid_support']);
        }

        $cmd = $this->generateSearchQuery('bind', $customValues, $cmd);

        if ($count) {
            return $cmd->queryScalar();
        }

        return $cmd->query()->readAll();
    }

    /**
     * Gets data for auto complete.
     *
     * @param string $keywords, keywords
     *
     * @return array
     */
    public function getAutoComplete($keywords)
    {
        $table = $this->tableName;
        $sql = "SELECT number, name FROM {$table} ";
        $sql .= ' WHERE ';
        $sql .= " number LIKE '" . $keywords . "%'";
        $sql .= ' ORDER BY number ASC LIMIT 100';

        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function getReportStatusByUserId($id, $month, $year)
    {
        $table = $this->tableNameReport;
        $sql = "SELECT group_sells, own_sells, own_amount, group_amount, bonus_level FROM {$table} ";
        $sql .= ' WHERE ';
        $sql .= ' user_id = :uid AND month = :month AND year = :year';

        $cmd = $this->DbModule->createCommand($sql);

        $cmd->bindParameter(':uid', $id);
        $cmd->bindParameter(':month', $month);
        $cmd->bindParameter(':year', $year);

        return $cmd->queryRow();
    }

    public function getDownloadItemData($userid, $month, $year)
    {
        $table = $this->tableNamePDF;
        $sql = "SELECT hash FROM {$table} ";
        $sql .= ' WHERE ';
        $sql .= ' user_id = :uid AND EXTRACT(MONTH FROM date) = :month AND EXTRACT(YEAR FROM date) = :year';

        $cmd = $this->DbModule->createCommand($sql);

        $cmd->bindParameter(':uid', $userid);
        $cmd->bindParameter(':month', $month);
        $cmd->bindParameter(':year', $year);
        // var_dump($cmd->queryRow(),$userid,$month, $year, $sql);
        return $cmd->queryRow();
    }

    public function getUserNumberBySubDomain($value)
    {
        $sql = 'SELECT number, name FROM ' . $this->tableName . '
        	WHERE subdomain = :subdomain AND is_active=1';

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':subdomain', $value, PDO::PARAM_STR);

        return $cmd->queryRow();
    }

    /**
     * Checks whether subdmain exist.
     *
     * @param string $username - the name of the user
     *
     * @return bool
     */
    public function getExistSubDomain($username)
    {
        $sql = 'SELECT COUNT(*) FROM ' . $this->tableName . ' WHERE subdomain = :subdomain';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':subdomain', $username, PDO::PARAM_STR);

        return $cmd->queryScalar();
    }

    public function getMaxUserId()
    {
        $sql = 'SELECT MAX(number) FROM ' . $this->tableName . ' WHERE number<9999';
        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->queryScalar() + 1;
    }

    public function getEkatteComboboxData($options, $used_ekatte)
    {
        // ready the data for SQL
        $return = implode(', ', $options['return']);
        $tablename = DEFAULT_DB_PREFIX . $options['tablename'];

        // creating the query
        $sql = "SELECT {$return} FROM " . $tablename . ' WHERE true';
        if ($options['custom']) {
            $fieldsCount = count($options['custom']['fields']);
            for ($i = 0; $i < $fieldsCount; $i++) {
                $sql .= ' AND ' . $options['custom']['fields'][$i] . " = '" . $options['custom']['values'][$i] . "'";
            }
        }
        // in case the required info is for ekatte
        if (0 != count($used_ekatte)) {
            // used_ekatte is still an array here and we convert it into string
            $used_ekatte_string = implode(', ', $used_ekatte);
            $sql .= " AND id NOT IN ({$used_ekatte_string})";
        }

        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function addNewUserEkatteRelation($user_id, $ekatte_id)
    {
        $sql = 'INSERT INTO ' . $this->tableUserEkatteRel . " VALUES ('" . $user_id . "','" . $ekatte_id . "')";
        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function getAlreadyUsedEkatteID($user_id)
    {
        $sql = 'SELECT ekatte_id FROM ' . $this->tableUserEkatteRel . ' WHERE user_id = :user_id';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':user_id', $user_id);
        $results = $cmd->query()->readAll();

        $return_array = [];
        if (0 != count($results)) {
            for ($i = 0; $i < count($results); $i++) {
                $return_array[] = $results[$i]['ekatte_id'];
            }
        }

        return $return_array;
    }

    public function getEkatteItems($user_id, $options)
    {
        $return_string = implode(', ', $options['return']);
        $sql = "SELECT {$return_string} FROM " . $this->tableUserEkatteRel . ' t INNER JOIN ' . $this->tableEkatte . ' ekatte ON (t.ekatte_id = ekatte.id)';
        $sql .= ' LEFT JOIN ' . $this->tableKmetstva . ' kmet ON (ekatte.nm_kmetst_id = kmet.id)';
        $sql .= ' LEFT JOIN ' . $this->tableObshtini . ' obs ON (kmet.nm_obst_id = obs.id)';
        $sql .= ' LEFT JOIN ' . $this->tableOblasti . ' obl ON (obs.nm_obl_id = obl.id)';
        $sql .= ' WHERE t.user_id = :uid';

        if ($options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        if (isset($options['limit'], $options['offset'])) {
            $sql .= ' LIMIT :limit OFFSET :offset';
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset'])) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        $cmd->bindParameter(':uid', $user_id);
        $return['data'] = $cmd->query()->readAll();

        // getting the total count for the pagination
        $sql = 'SELECT count(*) FROM ' . $this->tableUserEkatteRel . ' WHERE user_id = :user_id';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':user_id', $user_id);
        $result = $cmd->query()->read();
        // assigning the total count to the return array
        $return['count'] = $result['count'];

        return $return;
    }

    public function getEkatteName($ekatte_code)
    {
        if (empty($ekatte_code)) {
            return '-';
        }

        $table = $this->tableEkatte;

        $sql = "SELECT ekatte_name FROM {$table} WHERE true";

        if ($ekatte_code) {
            $sql .= ' AND ekatte_code = :ekatte_code';
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($ekatte_code) {
            $cmd->bindParameter(':ekatte_code', $ekatte_code);
        }

        $return = $cmd->query()->readAll();

        return $return[0]['ekatte_name'];
    }

    public function getEkatteNameMulti($options = [])
    {
        if (empty($options['ekatte_codes'])) {
            return [];
        }

        foreach ($options['ekatte_codes'] as &$ekatte) {
            $ekatte = "'{$ekatte}'";
        }

        $ekatte_string = implode(',', $options['ekatte_codes']);
        $return_string = implode(', ', $options['return']);

        $sql = "SELECT {$return_string} FROM {$this->tableEkatte} WHERE ekatte_code IN ({$ekatte_string})";

        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function getAllEkatteData($options = [])
    {
        $table = $this->tableEkatte;

        $sql = "SELECT * FROM {$table} WHERE true";

        if (array_key_exists('sort', $options)) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function deleteEkatteRelation($user_id, $ekatte_array)
    {
        $ekatte_string = implode(',', $ekatte_array);

        $sql = 'DELETE FROM ' . $this->tableUserEkatteRel . " WHERE user_id = :user_id AND ekatte_id IN ({$ekatte_string})";
        $cmd = $this->DbModule->createCommand($sql);

        $cmd->bindParameter(':user_id', $user_id);
        $cmd->execute();
    }

    public function getUserByEmail($email)
    {
        $sql = 'SELECT * FROM ' . $this->tableName . ' WHERE email = :email LIMIT 1';
        $cmd = $this->DbModule->createCommand($sql);

        $cmd->bindParameter(':email', $email);

        return $cmd->query()->read();
    }

    public function setUserHash($user_id, $hash)
    {
        $sql = 'UPDATE ' . $this->tableName . ' SET hash = :hash WHERE id = :user_id';
        $cmd = $this->DbModule->createCommand($sql);

        $cmd->bindParameter(':user_id', $user_id);
        $cmd->bindParameter(':hash', $hash);
        $cmd->execute();

        $sql = 'SELECT * FROM ' . $this->tableName . ' WHERE id = :user_id LIMIT 1';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':user_id', $user_id);

        return $cmd->query()->read();
    }

    public function checkHashData($user_id, $hash)
    {
        $sql = 'SELECT * FROM ' . $this->tableName . ' WHERE id = :user_id AND hash = :hash LIMIT 1';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':user_id', $user_id);
        $cmd->bindParameter(':hash', $hash);

        return $cmd->query()->read();
    }

    public function setNewPassword($user_id, $hash, $password)
    {
        $sql = 'UPDATE ' . $this->tableName . ' SET password = :password WHERE id = :user_id AND hash = :hash';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':user_id', $user_id);
        $cmd->bindParameter(':password', $password);
        $cmd->bindParameter(':hash', $hash);

        $cmd->execute();
    }

    public function getCropLayerForProcessing()
    {
        $sql = 'SELECT * FROM ' . $this->tableCropLayersProcessing . ' WHERE status = false';
        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function setCropLayerProcessed($id)
    {
        $sql = 'UPDATE ' . $this->tableCropLayersProcessing . ' SET status = TRUE WHERE id = :id';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':id', $id);
        $cmd->execute();
    }

    public function getOverlapsForProcessing()
    {
        $sql = 'SELECT * FROM ' . $this->tableOverlaps . ' WHERE status = FALSE';

        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function setOverlapProcessed($id)
    {
        $sql = 'UPDATE ' . $this->tableOverlaps . ' SET status = TRUE WHERE item_id = :id';

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':id', $id);

        $cmd->execute();
    }

    public function getUserRightsByUserID($user_id)
    {
        $sql = "SELECT 
                    right_id
                FROM {$this->tableUserRights} as rights
                INNER JOIN {$this->tableName} as users on (users.id = rights.user_id and users.group_id = rights.group_id)
                WHERE
                    rights.user_id = :user_id
                ";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':user_id', $user_id);

        return $cmd->query()->readAll();
    }

    public function getUserOrganizationRights(int $userId, int $organizationId)
    {
        $sql = "SELECT 
                    right_id
                FROM {$this->tableUserRights} as rights
                WHERE
                    rights.user_id = :user_id
                    AND
                    rights.group_id = :group_id
                ";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':user_id', $userId);
        $cmd->bindParameter(':group_id', $organizationId);

        return $cmd->query()->readAll();
    }

    public function getChildAccountCount($parent_id)
    {
        $sql = 'SELECT COUNT(*) FROM ' . $this->tableName . ' WHERE parent_id = :parent_id';

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':parent_id', $parent_id);
        $result = $cmd->query()->read();

        return $result['count'];
    }

    public function deleteUserRightsByUserID($userId, $organizationId = null, $rightId = null)
    {
        $sql = 'DELETE FROM ' . $this->tableUserRights . ' WHERE user_id = :user_id ';

        if ($organizationId) {
            $sql .= ' AND group_id = :organization_id';
        }

        if ($rightId) {
            $sql .= ' AND right_id = :right_id';
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($organizationId) {
            $cmd->bindParameter(':organization_id', $organizationId);
        }

        if ($rightId) {
            $cmd->bindParameter(':right_id', $rightId);
        }

        $cmd->bindParameter(':user_id', $userId);

        $cmd->execute();
    }

    /**
     * AGREEMENTS.
     */
    public function getAgreementsForProcessing()
    {
        $sql = 'SELECT * FROM ' . $this->tableAgreements . ' WHERE status = FALSE';

        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function setAgreementProcessed($id)
    {
        $sql = 'UPDATE ' . $this->tableAgreements . ' SET status = TRUE WHERE id = :id';

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':id', $id);

        $cmd->execute();
    }

    public function updateUsersData($options)
    {
        $updateFields = array_keys($options['mainData']);
        $updateValues = array_values($options['mainData']);

        $sql = "UPDATE {$this->tableName} SET";

        for ($i = 0; $i < count($updateFields); $i++) {
            $sql .= " {$updateFields[$i]} = :field{$i}";
            if ($i < count($updateValues) - 1) {
                $sql .= ',';
            }
        }

        $sql .= ' WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], false);
        }

        $cmd = $this->DbModule->createCommand($sql);

        for ($i = 0; $i < count($updateValues); $i++) {
            $type = PdoHelper::getPdoType($updateValues[$i]);
            $cmd->bindParameter(':field' . $i, $updateValues[$i], $type);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        $cmd->execute();
    }

    public function getUsers($options, $counter, $returnOnlySQL)
    {
        if ($counter) {
            $return = 'COUNT(*)';
        } elseif ($options['return']) {
            $return = implode(',', $options['return']);
        } else {
            $return = '*';
        }

        $sql = "SELECT {$return} FROM {$this->tableName} WHERE true";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], false);
        }

        if ($options && !$counter) {
            if ($options['sort']) {
                $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
            }

            $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
            $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

            if (false !== $limit && false !== $offset) {
                $sql .= $returnOnlySQL
                    ? " LIMIT {$limit} OFFSET {$offset}"
                    : ' LIMIT :limit OFFSET :offset';
            }
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getCoverageItemsForProcessing()
    {
        $sql = "SELECT * FROM {$this->tableCoverage} WHERE status = 0";

        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function setCoverageItemStatus($id, $status, $message = '')
    {
        $sql = "UPDATE {$this->tableCoverage} SET status = :status, errors = :errors
                WHERE id = :id";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':id', $id);
        $cmd->bindParameter(':status', $status);
        $cmd->bindParameter(':errors', $message);
        $cmd->execute();
    }

    public function deleteItemsByParams($options)
    {
        $sql = "DELETE FROM {$options['tablename']} WHERE true";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], false);
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        $cmd->execute();
    }

    public function createReturnVariable($return = false, $counter, $custom_counter = false)
    {
        if ($counter) {
            if ($custom_counter) {
                $return = $custom_counter;
            } else {
                $return = 'COUNT(*)';
            }
        } elseif ($return) {
            $return = implode(',', $return);
        } else {
            $return = '*';
        }

        return $return;
    }

    /**
     * @return array|string
     */
    public function getItemsByParams($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'] ?? false, $counter, $options['custom_counter'] ?? false);

        $sql = "SELECT {$return} FROM {$options['tablename']} WHERE true";

        if (!empty($options['where'])) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if (!empty($options['group'])) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if (!empty($options['having'])) {
            $sql .= ' HAVING ' . $options['having'];
        }

        if (!empty($options['order']) && !empty($options['sort']) && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if (!empty($options['where'])) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getEkatteOnlyItems($options)
    {
        $return_string = implode(', ', $options['return']);
        $sql = "SELECT {$return_string} FROM " . $this->tableEkatte . ' ekatte';
        $sql .= ' LEFT JOIN ' . $this->tableKmetstva . ' kmet ON (ekatte.nm_kmetst_id = kmet.id)';
        $sql .= ' LEFT JOIN ' . $this->tableObshtini . ' obs ON (kmet.nm_obst_id = obs.id)';
        $sql .= ' LEFT JOIN ' . $this->tableOblasti . ' obl ON (obs.nm_obl_id = obl.id)';
        $sql .= ' WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], false);
        }

        if ($options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        if (isset($options['limit'], $options['offset'])) {
            $sql .= ' LIMIT :limit OFFSET :offset';
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset'])) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getTableNameExist($database, $tablename)
    {
        $sql = 'SELECT COUNT(*) as num FROM information_schema.columns ';
        $sql .= "where table_catalog = :dbname and
                table_schema = 'public' and
                table_name = :tname";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':dbname', $database);
        $cmd->bindParameter(':tname', $tablename);

        return $cmd->queryScalar();
    }

    public function getColumnNameExist($database, $tablename, $columnName)
    {
        $sql = 'SELECT COUNT(*) AS num FROM information_schema.columns ';
        $sql .= "WHERE table_catalog = :dbname AND
                table_schema = 'public' AND
                table_name = :tname AND
                column_name = :column";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':dbname', $database);
        $cmd->bindParameter(':tname', $tablename);
        $cmd->bindParameter(':column', $columnName);

        return $cmd->queryScalar();
    }

    public function updateOrderedPlots($data)
    {
        $sql = 'UPDATE su_satellite_orders_plots
                SET name = :name, culture = :culture
                WHERE gid = :gid';

        $cmd = $this->DbModule->createCommand($sql);

        $cmd->bindParameter(':name', $data['name']);
        $cmd->bindParameter(':culture', $data['culture']);
        $cmd->bindParameter(':gid', $data['gid']);

        $cmd->execute();
    }

    public function getUserPayrollExports($options, $counter = false, $returnOnlySQL = false)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM {$this->tablePayrollExports} WHERE true";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getPayrollExprotsForProcessing()
    {
        $sql = "SELECT pe.*, u.database, u.username FROM {$this->tablePayrollExports} pe
                LEFT JOIN su_users u ON (pe.user_id = u.id)
                WHERE pe.status = 'queued'";
        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function setPayrollExportInProcess($id)
    {
        $sql = 'UPDATE ' . $this->tablePayrollExports . " SET status = 'processing' WHERE id = :id";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':id', $id);

        $cmd->execute();
    }

    public function setPayrollExportAsProcessed($id, $execution_time)
    {
        $sql = 'UPDATE ' . $this->tablePayrollExports . " SET status = 'processed', execution_time = :execution_time WHERE id = :id";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':id', $id);
        $cmd->bindParameter(':execution_time', $execution_time);

        $cmd->execute();
    }

    public function setPayrollExportMessage($id, $message, $status = null)
    {
        if ($status) {
            $sql = 'UPDATE ' . $this->tablePayrollExports . " SET status = '" . $status . "', message = :message WHERE id = :id";
        } else {
            $sql = 'UPDATE ' . $this->tablePayrollExports . ' SET message = :message WHERE id = :id';
        }

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':id', $id);
        $cmd->bindParameter(':message', $message);

        $cmd->execute();
    }

    public function getSalesPersons()
    {
        $sql = 'SELECT * FROM ' . $this->tableSalesPerson;
        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function getDevices($options, $counter = false, $returnOnlySQL = false)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM {$this->tableUserMachine}  WHERE TRUE ";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function addModems($data)
    {
        if ($data) {
            $sql = "INSERT INTO su_user_machine(machine, device_id, user_id, last_date,serial) VALUES ('" . $data['name'] . "','" . $data['id'] . "','" . $data['user_id'] . "','" . $data['last_date'] . "','" . $data['serial'] . "')";
            $cmd = $this->DbModule->createCommand($sql);

            return $cmd->query()->readAll();
        }
    }

    public function removeModem($id)
    {
        if ($id) {
            $sql = 'DELETE FROM su_user_machine WHERE id = :id';
            $cmd = $this->DbModule->createCommand($sql);
            $cmd->bindParameter(':id', $id);
            $cmd->execute();
        }
    }

    public function addUserSubscriptions($id, $type, $usage)
    {
        $sql = 'INSERT INTO ' . $this->tableSubscriptions . ' VALUES(DEFAULT, ' . $id . ", '" . $type . "' , " . $usage . ')';
        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->read();
    }

    public function updateUserSubscriptions($subscriptionId, $usage)
    {
        $sql = 'UPDATE ' . $this->tableSubscriptions . ' SET subscription_usage_id = ' . $usage . ' WHERE id = ' . $subscriptionId;
        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->read();
    }

    /**
     * @param int $groupId
     *
     * @return array
     */
    public function getSubUsers($groupId)
    {
        $cmd = $this->DbModule->createCommand(
            'SELECT 
                u.id,
                u.database,
                u.username,
                u.level
            FROM su_users as u
            WHERE u.group_id = :group_id'
        );

        $cmd->bindParameter(':group_id', $groupId);

        return $cmd->query()->readAll();
    }
}
