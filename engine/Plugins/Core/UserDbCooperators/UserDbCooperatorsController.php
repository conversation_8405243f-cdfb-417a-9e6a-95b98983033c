<?php

namespace TF\Engine\Plugins\Core\UserDbCooperators;

use TF\Engine\Plugins\Core\UserDb\UserDbController;

class UserDbCooperatorsController extends UserDbController
{
    public $DbHandler;
    public $Database;

    public function __construct($database)
    {
        $this->DbHandler = new UserDbCooperatorsModel($database);
        $this->Database = $database;
    }

    public function getCooperatorHeritors($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getCooperatorHeritors($options, $counter, $returnOnlySQL);
    }

    public function getCooperatorCapitals($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getCooperatorCapitals($options, $counter, $returnOnlySQL);
    }

    public function getDevidendsByAnnualReport($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getDevidendsByAnnualReport($options, $counter, $returnOnlySQL);
    }

    public function getCooperatorDividendByAnnualReport($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getCooperatorDividendByAnnualReport($options, $counter, $returnOnlySQL);
    }

    public function editCooperatorCurrentCapital($cooperator_id, $newCurrentCapital)
    {
        return $this->DbHandler->editCooperatorCurrentCapital($cooperator_id, $newCurrentCapital);
    }
}
