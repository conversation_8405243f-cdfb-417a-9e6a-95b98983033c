<?php

namespace TF\Engine\Plugins\Core\UserDbAgreements;

use TF\Engine\Plugins\Core\UserDb\UserDbController;

// Prado::using('Plugins.Core.UserDb.UserDbController');

class UserDbAgreementsController extends UserDbController
{
    public $DbHandler;
    public $Database;

    public function __construct($database)
    {
        $this->DbHandler = new UserDbAgreementsModel($database);
        $this->Database = $database;
    }

    public function getAgreementsDataByParams($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getAgreementsDataByParams($options, $counter, $returnOnlySQL);
    }

    public function getAgreementsMapExtent($agreementid)
    {
        return $this->DbHandler->getAgreementsMapExtent($agreementid);
    }

    public function getKMSInterceptionForAgreement($kmsTableName)
    {
        return $this->DbHand<PERSON>->getKMSInterceptionForAgreement($kmsTableName);
    }
}
