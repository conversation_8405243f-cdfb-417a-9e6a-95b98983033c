<?php

namespace TF\Engine\Plugins\Core\UserDbAgreements;

use PDO;
use TF\Engine\Plugins\Core\UserDb\UserDbModel;

class UserDbAgreementsModel extends UserDbModel
{
    public function getAgreementsDataByParams($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM {$this->tableAgreements} a
					INNER JOIN {$this->tableAgreementsData} ad ON(a.id = ad.agreement_id)
						WHERE TRUE";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getAgreementsMapExtent($agreementid)
    {
        $tablename = $this->tableAgreementsData;
        $tableKVS = $this->tableKVS;
        $sql = "SELECT ST_Extent(kvs.geom) as extent FROM {$tableKVS} kvs INNER JOIN {$tablename} t ON (kvs.gid = t.gid) WHERE TRUE AND t.agreement_id  = :id AND t.has_match = TRUE";

        $cmd = $this->DbModule->createCommand($sql);

        $cmd->bindParameter(':id', $agreementid);

        return $cmd->query()->readAll();
    }

    public function getKMSInterceptionForAgreement($kmsTableName)
    {
        $tableKVS = $this->tableKVS;

        $sql = "SELECT kvs.gid, kvs.ekate, kvs.masiv, kvs.number, kvs.document_area,
                SUM(round((st_area (st_intersection (kvs.geom, kms.geom)) / 1000)::numeric, 3)) AS intersection_area
                FROM
                    {$tableKVS} kvs,
                    {$kmsTableName} kms
                WHERE
                    st_intersects (kvs.geom, kms.geom)
                AND ( st_area ( st_intersection ( kvs.geom, kms.geom ) )/1000 ) > 0.01
                AND kvs.ekate IS NOT NULL
                AND kvs.is_edited = false
                GROUP BY kvs.gid, kvs.ekate, kvs.masiv, kvs.number, kvs.document_area
                ";

        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }
}
