<?php

namespace TF\Engine\Plugins\Core\Base;

use Exception;
use PDO;
use Prado\Data\TDbCommand;
use TF\Engine\Kernel\DbHandler;
use TF\Engine\Kernel\PdoHelper;

/**
 * Base model class file
 * Every plugin extends this one.
 *
 * <AUTHOR> Ltd. <<EMAIL>>
 *
 * @link http://www.devision.bg/
 *
 * @copyright Copyright &copy; 2008 Devision Ltd.
 */

/**
 * BaseModel class.
 *
 * Implements functionality to work with the database
 */
class BaseModel extends DbHandler
{
    private $_parameters;

    /**
     * Constructor of the class.
     *
     * @param string $tableName - The name of the main table
     * @param string $fieldName - The relation field name
     */
    public function __construct($tableName = null, $fieldName = null)
    {
        parent::__construct($tableName, $fieldName);

        $this->tableName = $tableName;
        $this->fieldName = $fieldName;
    }

    /**
     * Setter for tables and fields.
     *
     * @param string $name
     */
    public function __set($name, $value)
    {
        if (0 === strpos($name, 'table') || 0 === strpos($name, 'field')) {
            $this->_parameters[$name] = $value;
        } else {
            parent::__set($name, $value);
        }
    }

    /**
     * Getter for tables and fields.
     *
     * @param string $name
     *
     * @throws Exception
     */
    public function __get($name)
    {
        if (0 === strpos($name, 'tableName')) {
            if (isset($this->_parameters[$name])) {
                return $this->getDbPrefix() . $this->_parameters[$name];
            }

            throw new Exception("No such variable '{$name}'");
        } elseif (0 === strpos($name, 'field') || 0 === strpos($name, 'table')) {
            if (isset($this->_parameters[$name])) {
                return $this->_parameters[$name];
            }

            throw new Exception("No such variable '{$name}'");
        } else {
            return parent::__get($name);
        }
    }

    /**
     * starts Transaction.
     */
    public function startTransaction()
    {
        return $this->DbModule->beginTransaction();
    }

    /**
     * creates Command.
     *
     * @param string $sql - the actual sql
     */
    public function createCommand($sql)
    {
        return $this->DbModule->createCommand($sql);
    }

    /**
     * Gets item data by query terms.
     *
     * @param string $table - name of the table from which to select
     * @param array $returnFields - array of fields which to be returned
     * @param array $fieldTerms - array of fields which to be queried in the where clause
     * @param array $valueTerms - array of the corresponding values which to be queried in the where clause
     *
     * @return array
     */
    public function getItem($table, $returnFields, $fieldTerms, $valueTerms)
    {
        $sql = $this->prepareGetSQL($table, $returnFields, $fieldTerms, $valueTerms);

        $cmd = $this->DbModule->createCommand($sql);

        foreach ($valueTerms as $key => $value) {
            if (null !== $value) {
                $cmd->bindValue(':field' . $key, $value);
            }
        }

        return $cmd->queryRow();
    }

    /**
     * Gets items data by query terms.
     *
     * @param string $table - name of the table from which to select
     * @param array $returnFields - array of fields which to be returned
     * @param array $fieldTerms - array of fields which to be queried in the where clause
     * @param array $valueTerms - array of the corresponding values which to be queried in the where clause
     *
     * @return array
     */
    public function getItems($table, $returnFields, $fieldTerms = [], $valueTerms = [], $options = [])
    {
        $orderBy = $options['orderby'];
        $orderType = $options['ordertype'];
        $limit = $options['limit'];
        $offset = $options['offset'];

        $sql = $this->prepareGetSQL($table, $returnFields, $fieldTerms, $valueTerms);

        if (!empty($orderBy) and !empty($orderType)) {
            $sql .= " ORDER BY {$orderBy} {$orderType} ";
        }

        if (isset($limit) and isset($offset)) {
            $sql .= " LIMIT {$limit}";
            $sql .= " OFFSET {$offset}";
        }

        $cmd = $this->DbModule->createCommand($sql);

        $cnt = count($valueTerms);
        for ($i = 0; $i < $cnt; $i++) {
            $cmd->bindParameter(':field' . $i, $valueTerms[$i], PDO::PARAM_STR);
        }

        return $cmd->query()->readAll();
    }

    /**
     * Gets items data by list of ids.
     *
     * @param string $table
     * @param array $returnFields
     * @param array $ids
     * @param array $field
     *
     * @return array
     */
    public function getItemsByIds($table, $returnFields, $ids, $field = 'id', $associative = false)
    {
        if (!count($ids)) {
            return [];
        }

        $return = implode(', ', $returnFields);
        foreach ($ids as $key => $value) {
            if ($idsSql) {
                $idsSql .= ', ';
            }
            $idsSql .= ":id{$key}";
        }
        $sql = "SELECT {$return} FROM {$table} WHERE {$field} IN ({$idsSql})";

        $cmd = $this->DbModule->createCommand($sql);

        foreach ($ids as $key => $value) {
            $cmd->bindValue(":id{$key}", $value, PDO::PARAM_STR);
        }
        $data = $cmd->query()->readAll();

        if ($associative) {
            $newData = [];
            foreach ($data as $value) {
                $newData[$value[$field]] = $value;
            }
            $data = &$newData;
        }

        return $data;
    }

    /**
     * Gets home items data.
     * Expected $option array:
     * $options = array
     * 	(
     * 		'flags' -> array
     * 			 			(
     * 			 				'equality' -> true or false
     * 			 				'strict' -> true or false
     * 			 				'values' -> array of flags
     * 			 			)
     * 		'count'     -> true or false
     * 		'return'    -> return fields
     * 		'orderby'   -> order field,
     * 		'ordertype' -> order type: 'ASC' or 'DESC'
     * 		'offset'    -> offset distance in query
     * 		'limit'     -> limit in query
     * 	);.
     *
     * @param string $options
     *
     * @return array
     */
    public function getHomeItems(&$options = [])
    {
        $flags = $this->generateFlags($options['flags']);
        $count = $options['count'];
        $return = $count ? 'COUNT(*)' : implode(', ', $options['return']);
        $customFields = $options['custom']['fields'];
        $customValues = $options['custom']['values'];
        $orderBy = $options['orderby'];
        $orderType = $options['ordertype'];
        $limit = $options['limit'];
        $offset = $options['offset'];

        $table = $this->tableName;

        $sql = "SELECT {$return} FROM {$table} WHERE flag{$flags}";

        $customQuery = $this->generateSearchQuery('sql', $customFields);
        if (!empty($customQuery)) {
            $sql .= " AND {$customQuery} ";
        }

        if (!$count and !empty($orderType) and !empty($orderBy)) {
            if (empty($orderType)) {
                $orderType = 'ASC';
            }
            $sql .= " ORDER BY {$orderBy} {$orderType} ";
        }

        if (!$count and isset($limit) and isset($offset)) {
            $sql .= " LIMIT {$limit}";
            $sql .= " OFFSET {$offset}";
        }

        $cmd = $this->DbModule->createCommand($sql);

        $cmd = $this->generateSearchQuery('bind', $customValues, $cmd);

        if ($count) {
            return $cmd->queryScalar();
        }

        return $cmd->query()->readAll();
    }

    /**
     * Generates flags using the array of specified constants.
     * Creates a string containing the binary mathematical operations
     * required to filter the specific results.
     *
     * @param array $options
     *
     * @return string
     */
    public function generateFlags($options = [])
    {
        // setting default values for equality and strict
        if (!isset($options['equality'])) {
            $options['equality'] = true;
        }
        if (!isset($options['strict'])) {
            $options['strict'] = false;
        }

        $left = 0; // number before '&' sign
        $right = 0; // number after '&' sign

        if (is_array($options['values'])) {
            foreach ($options['values'] as $key => $value) {
                $left += constant('Config::' . $key);
                if ($value) {
                    $right += constant('Config::' . $key);
                }
            }
        }

        $sign = $options['equality'] ? '=' : '<>';
        if ($options['strict']) {
            $flags = $sign . $right;
        } else {
            $flags = '&' . $left . $sign . $right;
        }

        return $flags;
    }

    /**
     * Returns the GROUP BY sentence according to return fields.
     *
     * @return string
     */
    public function generateGroupBy(array $mainFields, array $return)
    {
        if ('pgsql' == $this->DbModule->DriverName) {
            $mainFields += $return;
        }
        foreach ($mainFields as &$value) {
            $find = stripos($value, ' as ');
            if (false !== $find) {
                $value = substr($value, 0, $find);
            }
        }

        return 'GROUP BY ' . implode(', ', array_unique($mainFields));
    }

    /**
     * Adds item in specific table.
     *
     * @param string $table - name of the table into which the new rows to be inserted
     * @param array $fieldTerms - array of fields' names
     * @param array $valueTerms - array of the corresponding values
     * @param bool $returnID - whether to return last inserted id
     * @param null|mixed $id
     *
     * @return int
     */
    public function addItem($table, $fieldTerms, $valueTerms, $returnID = true, $id = null)
    {
        $sql = $this->prepareInsertSQL($table, $fieldTerms);

        if ($returnID) {
            $sql .= ' RETURNING ' . (null === $id ? 'id' : $id);
        }

        $cmd = $this->DbModule->createCommand($sql);

        $cnt = count($valueTerms);
        for ($i = 0; $i < $cnt; $i++) {
            $type = PdoHelper::getPdoType($valueTerms[$i]);
            $cmd->bindParameter(':field' . $i, $valueTerms[$i], $type);
        }

        if ($returnID) {
            return $cmd->queryScalar();
        }

        return $cmd->execute();
    }

    /**
     * Replaces item in specific table.
     *
     * @param string $table - name of the table to update
     * @param array $fieldTerms - array of the fields' names to be updated
     * @param array $valueTerms - array of the corresponding values
     *
     * @return int
     */
    public function replaceItem($table, $fieldTerms, $valueTerms, $fieldNames = [])
    {
        $fieldNames[] = $this->fieldName;

        if ('pgsql' == $this->DbModule->DriverName) {
            $field = [];
            $id = [];
            foreach ($fieldTerms as $key => $value) {
                if (in_array($value, $fieldNames)) {
                    $id[] = $valueTerms[$key];
                    $field[] = $value;
                }
            }

            $data = $this->getItem($table, ['COUNT(*) as num'], $field, $id);

            if ($data['num']) {
                $this->editItem($table, $fieldTerms, $valueTerms, $field, $id);
            } else {
                return $this->addItem($table, $fieldTerms, $valueTerms, false);
            }
        } else {
            $sql = $this->prepareReplaceSQL($table, $fieldTerms);

            $cmd = $this->DbModule->createCommand($sql);

            $cnt = count($valueTerms);
            for ($i = 0; $i < $cnt; $i++) {
                $cmd->bindParameter(':field' . $i, $valueTerms[$i], PDO::PARAM_STR);
            }

            $cmd->execute();

            return $this->DbModule->getLastInsertID(null);
        }
    }

    /**
     * Edits item in specific table.
     *
     * @param string $table - table to edit
     * @param array $setFields - edited fields
     * @param array $setValues - values in edited fields
     * @param array $whereFields - fields in where clause
     * @param array $whereValues - values in fields in where clause
     *
     * @return int
     */
    public function editItem($table, $setFields, $setValues, $whereFields, $whereValues)
    {
        $sql = $this->prepareUpdateSQL($table, $setFields, $whereFields);

        $cmd = $this->DbModule->createCommand($sql);

        $cnt = count($setValues);

        for ($i = 0; $i < $cnt; $i++) {
            $type = PdoHelper::getPdoType($setValues[$i]);
            $cmd->bindValue(':setField' . $i, $setValues[$i], $type);
        }

        $cnt = count($whereValues);
        for ($i = 0; $i < $cnt; $i++) {
            $type = PdoHelper::getPdoType($whereValues[$i]);
            $cmd->bindValue(':whereField' . $i, $whereValues[$i], $type);
        }

        return $cmd->execute();
    }

    /**
     * Deletes items in specific table.
     *
     * @param string $table - name of a table
     * @param array $fieldTerms - fields in where clause
     * @param array $valueTerms - values in fields in where clause
     *
     * @return int
     */
    public function deleteItem($table, $fieldTerms, $valueTerms)
    {
        $sql = $this->prepareDeleteSQL($table, $fieldTerms);

        $cmd = $this->DbModule->createCommand($sql);

        $cnt = count($valueTerms);
        for ($i = 0; $i < $cnt; $i++) {
            $cmd->bindParameter(':field' . $i, $valueTerms[$i], PDO::PARAM_STR);
        }

        return $cmd->execute();
    }

    /**
     * Sets the flag of the records that are given by ids in the given table.
     *
     * @param string $table - Name of the table with the records to be updated
     * @param array $ids - ids of records to be updated with a new flag
     * @param string $flag - bind records to this flag
     * @param string $action - if the records should be added to this flag or remove ('add' or 'remove')
     */
    public function setFlag($table, $ids, $flag, $action)
    {
        if ($table and $flag and is_array($ids) and $cnt = count($ids) and in_array($action, ['add', 'remove'])) {
            $set = 'add' == $action ? "|{$flag}" : "& ~{$flag}";

            $sql = "UPDATE {$table} SET flag=flag{$set} WHERE id IN(";

            for ($i = 0; $i < $cnt; $i++) {
                $sql .= " :k{$i} ";
                if ($i < $cnt - 1) {
                    $sql .= ',';
                }
            }
            $sql .= ')';

            $cmd = $this->DbModule->createCommand($sql);

            for ($i = 0; $i < $cnt; $i++) {
                $cmd->bindParameter(":k{$i}", $ids[$i], PDO::PARAM_INT);
            }

            $cmd->execute();
        }
    }

    /**
     * Removes the flag from all items in the table.
     *
     * @param string $table
     * @param int $flag
     */
    public function setFlagAll($table, $flag, $action)
    {
        $set = 'add' == $action ? "|{$flag}" : "& ~{$flag}";

        $sql = "UPDATE {$table} SET flag=flag{$set}";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    /**
     * Moves items in specific table to trash.
     *
     * @param string $table - name of the table
     * @param string $field - field in where clause
     * @param array $ids - the ids of the records to be moved to trash
     */
    public function moveItemsToTrash($table, $field, $ids = [])
    {
        $flag = Config::FLAGS_TRASH;

        if ($table and $field and is_array($ids) and $cnt = count($ids)) {
            $sql = "UPDATE {$table} SET flag = flag|{$flag} WHERE {$field} IN (";

            for ($i = 0; $i < $cnt; $i++) {
                $sql .= " :k{$i} ";
                if ($i < $cnt - 1) {
                    $sql .= ',';
                }
            }
            $sql .= ')';

            $cmd = $this->DbModule->createCommand($sql);

            for ($i = 0; $i < $cnt; $i++) {
                $cmd->bindParameter(":k{$i}", $ids[$i], PDO::PARAM_INT);
            }

            return $cmd->execute();
        }
    }

    /**
     * Restores items from trash.
     *
     * @param string $table - name of a table
     * @param string $field - field in where clause
     * @param array $ids - the ids of the records to be restored
     */
    public function restoreItemsFromTrash($table, $field, $ids = [])
    {
        $flag = Config::FLAGS_TRASH;

        if ($table and $field and is_array($ids) and $cnt = count($ids)) {
            $sql = "UPDATE {$table} SET flag = flag&(~{$flag}) WHERE {$field} IN (";

            for ($i = 0; $i < $cnt; $i++) {
                $sql .= " :k{$i} ";
                if ($i < $cnt - 1) {
                    $sql .= ',';
                }
            }
            $sql .= ')';

            $cmd = $this->DbModule->createCommand($sql);

            for ($i = 0; $i < $cnt; $i++) {
                $cmd->bindParameter(":k{$i}", $ids[$i], PDO::PARAM_INT);
            }

            return $cmd->execute();
        }
    }

    /**
     * Restores items from trash.
     *
     * @param string $table - name of a table
     * @param string $field - field in where clause
     * @param array $ids - the ids of the records to be deleted from trash
     */
    public function deleteItemsFromTrash($table, $field, $ids = [])
    {
        if ($table and $field and is_array($ids) and $cnt = count($ids)) {
            $sql = "DELETE FROM {$table} WHERE {$field} IN ( ";
            for ($i = 0; $i < $cnt; $i++) {
                $sql .= " :k{$i} ";
                if ($i < $cnt - 1) {
                    $sql .= ',';
                }
            }
            $sql .= ' )';

            $cmd = $this->DbModule->createCommand($sql);

            for ($i = 0; $i < $cnt; $i++) {
                $cmd->bindParameter(":k{$i}", $ids[$i], PDO::PARAM_INT);
            }

            return $cmd->execute();
        }
    }

    /**
     * Returns the maximum value of a field in a table.
     *
     * @param string $table - name of a table
     * @param string $field - the field
     * @param array $parent - the parents
     *
     * @return array
     */
    public function getMaxValue($table, $field, $parent = [])
    {
        $sql = "SELECT MAX({$field}) as max, COUNT(*) as count FROM {$table}
			WHERE {$field} IS NOT NULL";
        foreach ($parent as $key => $value) {
            if (null !== $value) {
                $sql .= " AND {$key} = :where{$key}";
            } else {
                $sql .= " AND {$key} IS NULL";
            }
        }

        $cmd = $this->DbModule->createCommand($sql);
        foreach ($parent as $key => $value) {
            if (null !== $value) {
                $cmd->bindValue(":where{$key}", $value, PDO::PARAM_STR);
            }
        }

        return $cmd->queryRow();
    }

    /**
     * Returns the Language ID given the name.
     *
     * @param string $name
     *
     * @return int
     */
    public function getLangID($name)
    {
        return 1;
    }

    public function createWhereSQL($sql, $options, $returnOnlySQL, $logicalOperation = 'AND')
    {
        $bindNames = array_keys($options);
        $bindOptions = array_values($options);

        for ($i = 0; $i < count($bindNames); $i++) {
            if (!is_null($bindOptions[$i]) && is_string($bindOptions[$i]) && '' != $bindOptions[$i]) {
                $sql .= ' AND ' . $bindOptions[$i];

                continue;
            }
            $compare = $bindOptions[$i]['compare'];
            $column = $bindOptions[$i]['column'];
            $value = $bindOptions[$i]['value'];
            $prefix = (isset($bindOptions[$i]['prefix'])) ? $bindOptions[$i]['prefix'] . '.' : '';

            if ('IN' == $compare || 'NOT IN' == $compare) {
                if (is_array($value) && count($value) > 0) {
                    $sql .= " {$logicalOperation} (" . $prefix . $column . ' ';

                    if (in_array(null, $value, true)) {
                        $value = array_values(array_filter($value, function ($v) {
                            return !is_null($v);
                        }));

                        $sql .= 'IS NULL';

                        if (!count($value)) {
                            $sql .= ')';

                            continue;
                        }
                        $sql .= ' OR ' . $prefix . $column . ' ';
                    }

                    $sql .= $compare . ' (';

                    for ($j = 0; $j < count($value); $j++) {
                        if ($returnOnlySQL) {
                            $valueType = gettype($value[$j]);
                            if ('string' === $valueType) {
                                $sql .= "'{$value[$j]}'";
                            } elseif ('boolean' === $valueType) {
                                $sql .= $value[$j] ? 'true' : 'false';
                            } else {
                                $sql .= "{$value[$j]}";
                            }
                        } else {
                            $sql .= ':' . $bindNames[$i] . $j;
                        }

                        if ($j < count($value) - 1) {
                            $sql .= ', ';
                        }
                    }
                    $sql .= '))';
                }
            } elseif ('EMPTY' === $compare) {
                $sql .= " {$logicalOperation} " . $prefix . $column . " = '' ";
            } elseif ('NOTEMPTY' === $compare) {
                $sql .= " {$logicalOperation} " . $prefix . $column . " != '' ";
            } else {
                // formatting the value if LIKE functions are used
                if ('ILIKE' == $compare || 'LIKE' == $compare) {
                    $value = '%' . $value . '%';
                }
                // checking if prefix is set
                //				if ($prefix)
                //				{
                //					$prefix = $prefix . '.';
                //				}

                // checking if value is not empty and creating a required query
                if ('' !== $value && null !== $value && '%%' !== $value) {
                    if ($returnOnlySQL) {
                        $sql .= " {$logicalOperation} " . $prefix . $column . ' ' . $compare . " '" . $value . "'";
                    } else {
                        $sql .= " {$logicalOperation} " . $prefix . $column . ' ' . $compare . ' :' . $bindNames[$i];
                    }
                }
            }
        }

        return $sql;
    }

    public function createWhereBinds(TDbCommand $cmd, $options)
    {
        $bindNames = array_keys($options);
        $bindOptions = array_values($options);

        $bindNamesCunt = count($bindNames);
        for ($i = 0; $i < $bindNamesCunt; $i++) {
            if (!is_null($bindOptions[$i]) && is_string($bindOptions[$i]) && '' != $bindOptions[$i]) {
                continue;
            }
            $compare = $bindOptions[$i]['compare'];
            $column = $bindOptions[$i]['column'];
            $value = $bindOptions[$i]['value'];
            $prefix = $bindOptions[$i]['prefix'] ?? '';

            if ('IN' === $compare || 'NOT IN' === $compare) {
                $value = array_values(array_filter($value ?? [], fn ($v) => !is_null($v)));
                for ($j = 0; $j < count($value); $j++) {
                    $dataType = PdoHelper::getPdoType($value[$j]);
                    $cmd->bindParameter(':' . $bindNames[$i] . $j, $value[$j], $dataType);
                }
            } else {
                // formatting the value if LIKE functions are used
                if ('' != $bindOptions[$i]['value'] && null !== $bindOptions[$i]['value']) {
                    if ('ILIKE' == $bindOptions[$i]['compare'] || 'LIKE' == $bindOptions[$i]['compare']) {
                        $bindOptions[$i]['value'] = '%' . $bindOptions[$i]['value'] . '%';
                    }
                }
                // checking if value is not empty and creating a required query
                if ('' !== $bindOptions[$i]['value'] && null !== $bindOptions[$i]['value'] && '%%' !== $bindOptions[$i]['value']) {
                    $dataType = PdoHelper::getPdoType($bindOptions[$i]['value']);
                    $cmd->bindParameter(':' . $bindNames[$i], $bindOptions[$i]['value'], $dataType);
                }
            }
        }
    }

    public function createWhereOrSQL($sql, $options, $returnOnlySQL)
    {
        $newSql = $this->createWhereSQL($sql, $options, $returnOnlySQL, 'OR') . ')';

        return str_replace($sql . ' OR', $sql . ' AND (', $newSql);
    }

    public function createWhereOrBinds(TDbCommand $cmd, $options, $values)
    {
        for ($i = 0; $i < count($values); $i++) {
            $value = $values[$i];
            $dataType = PdoHelper::getPdoType($value);

            $cmd->bindParameter(':' . $i . 'name', $value, $dataType);
        }
    }

    public function getWithOptions($options, $counter = false, $returnOnlySQL = false)
    {
        if (!$options['tablename']) {
            return;
        }

        if ($counter) {
            $return = 'COUNT(*)';
        } elseif ($options['return']) {
            $return = implode(',', $options['return']);
        } else {
            $return = '*';
        }

        $sql = "SELECT {$return} FROM {$options['tablename']} WHERE true";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options && !$counter) {
            if ($options['sort']) {
                $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
            }

            $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
            $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

            if (false !== $limit && false !== $offset) {
                $sql .= $returnOnlySQL
                    ? " LIMIT {$limit} OFFSET {$offset}"
                    : ' LIMIT :limit OFFSET :offset';
            }
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    /**
     * Function to generate custom query for the home page of the plugin.
     *
     * @param string $mode - 'sql' or 'bind' If it should bind the values in param or create the where like clause
     * @param array $params - The table fields that should be search for
     * @param string $cmd - Reference to a TDbCommand instance
     */
    protected function generateSearchQuery($mode, $params, $cmd = null)
    {
        if ('sql' == $mode) {
            $string = '';
            $like = 'pgsql' == $this->DbModule->DriverName ? 'ILIKE' : 'LIKE';

            $cnt = count($params);
            for ($i = 0; $i < $cnt; $i++) {
                $string .= "{$params[$i]} {$like} :custom{$i} ";
                if ($i != $cnt - 1) {
                    $string .= ' OR ';
                }
            }

            if ($string) {
                return '( ' . $string . ' )';
            }

            return '';
        } elseif ('bind' == $mode) {
            $cnt = count($params);
            for ($i = 0; $i < $cnt; $i++) {
                $cmd->bindParameter(':custom' . $i, $params[$i], PDO::PARAM_STR);
            }

            return $cmd;
        }
    }
}
