<?php

namespace TF\Engine\Plugins\Core\Base;

use Exception;
use ReflectionClass;
use TF\Engine\Kernel\Controller;

/**
 * BaseController class file.
 *
 * <AUTHOR> Ltd. <<EMAIL>>
 *
 * @link http://www.devision.bg/
 *
 * @copyright Copyright &copy; 2008 Devision Ltd.
 */

/**
 * BaseController class.
 *
 * This is the controller class for the Base plugin
 */
class BaseController extends Controller
{
    /**
     * Constructor of the class.
     * Instantiates the model.
     *
     * @param string $param - Title of the plugin
     */
    public function __construct($param = '')
    {
        $reflectionClass = new ReflectionClass(static::class);
        $class = $this->getModel($reflectionClass);
        $plugin = $this->getPlugin($reflectionClass);
        parent::__construct($plugin);
        $this->DbHandler = new $class($GLOBALS[$this->pluginName]['tableName'], $GLOBALS[$this->pluginName]['fieldName']);

        /*
        switch ($param) {
            case 'Layers': $class = 'TF\Engine\Plugins\Core\Layers\LayersModel';break;
            case 'Farming': $class = 'TF\Engine\Plugins\Core\Farming\FarmingModel';break;
            case 'Users': $class = 'TF\Engine\Plugins\Core\Users\UsersModel';break;
            case 'GlobalNotification': $class = 'TF\Engine\Plugins\Core\GlobalNotification\GlobalNotificationModel';break;
            default: $class = $param . 'Model';break;
        }
        */
    }

    /**
     * Gets items, which should be displayed on home page.
     * Expected array $option:.
     *
     * $options = array
     *	(
     *		'flags' -> array
     *			(
     *				'equality' -> true or false
     *				'strict' -> true or false,
     *				'values' -> array of flags
     *			),
     *		'place'  ->'backend' or 'frontend'
     *		'return' -> returned fields in query
     *		'keywords' -> keywords for searching
     *		'offset' -> offset distance in query
     *		'limit' -> limit in query
     *	);
     *
     * @param array $options
     *
     * @return array
     */
    public function getHomeItems(&$settings = [])
    {
        $orderby = $this->getOrderBy($settings['place']);
        $ordertype = $this->getOrderType($settings['place']);

        $options = [
            'flags' => $settings['flags'],
            'return' => $settings['return'],
            'orderby' => '' != $settings['orderby'] ? $settings['orderby'] : $orderby,
            'ordertype' => '' != $settings['ordertype'] ? $settings['ordertype'] : $ordertype,
            'offset' => $settings['offset'],
            'limit' => $settings['limit'],
        ];

        if ($settings['keywords']) {
            $keywords = '%' . $settings['keywords'] . '%';
            $options['custom'] = [
                'fields' => ['value'],
                'values' => [$keywords],
            ];
        }

        $result = [];
        if (isset($settings['count'])) {
            $options['count'] = $settings['count'];
            $result[$settings['count'] ? 'count' : 'data'] = $this->DbHandler->getHomeItems($options);
        } else {
            $options['count'] = true;
            $result['count'] = $this->DbHandler->getHomeItems($options);
            $options['count'] = false;
            $result['data'] = $this->DbHandler->getHomeItems($options);
        }

        return $result;
    }

    /**
     * Adds Item method.
     *
     * @param array $options
     *
     * @return int
     */
    public function addItem(&$options = [])
    {
        $transaction = $this->DbHandler->startTransaction();

        try {
            $res = $this->onAddItem($options);
            $transaction->commit();

            return $res;
        } catch (Exception $e) {
            $transaction->rollBack();

            throw $e;
        }
    }

    /**
     * Function to be executed on addItem.
     *
     * @param array $options - options of the addItem
     *
     * @return int - the id returned from the query
     */
    public function onAddItem(&$settings)
    {
        $id = $this->DbHandler->addItem($this->DbHandler->tableName, array_keys($settings['mainData']), array_values($settings['mainData']));

        return $id;
    }

    /**
     * Edits Item method.
     *
     * @param array $options
     *
     * @return int
     */
    public function editItem(&$options = [])
    {
        $transaction = $this->DbHandler->startTransaction();

        try {
            $res = $this->onEditItem($options);
            $transaction->commit();

            return $res;
        } catch (Exception $e) {
            $transaction->rollBack();

            throw $e;
        }
    }

    /**
     * Function to be executed after editItem.
     *
     * @param array $options - options of the edited added
     *
     * @return int
     */
    public function onEditItem(&$settings)
    {
        $setFields = array_keys($settings['mainData']);
        $setValues = array_values($settings['mainData']);
        $whereFields = ['id'];
        $whereValues = [$settings['id']];
        $id = $this->DbHandler->editItem($this->DbHandler->tableName, $setFields, $setValues, $whereFields, $whereValues);

        return $id;
    }

    /**
     * Delete items.
     * Expected $options array:
     * $options = array
     *	(
     *		'whereFields' -> array with fields in "WHERE" part of query
     *		'whereValues' -> array with values in "WHERE" part of query, connected with fields
     *	);.
     *
     * @param array $options
     *
     * @return int
     */
    public function deleteItem(&$options = [])
    {
        $transaction = $this->DbHandler->startTransaction();

        try {
            $id = $this->DbHandler->deleteItem($this->DbHandler->tableName, array_keys($options), array_values($options));
            $this->onDeleteItem($options);

            $transaction->commit();

            return $id;
        } catch (Exception $e) {
            $transaction->rollBack();

            throw $e;
        }
    }

    /**
     * Function to be executed after deleteItem.
     *
     * @param $options - options of the deleted added
     */
    public function onDeleteItem(&$options) {}

    /**
     * Gets Item.
     * Expected $options array:
     * $options = array
     *	(
     *		'return' => array with return fields
     *		'whereFields' => array with fields in "WHERE" part of query
     *		'whereValues' => array with values in "WHERE" part of query, connected with fields
     *	);.
     *
     * @param array $options
     *
     * @return array
     */
    public function getItem(&$options = [])
    {
        return $this->DbHandler->getItem($this->DbHandler->tableName, $options['return'], $options['whereFields'], $options['whereValues']);
    }

    public function getItemByParams(&$options = [])
    {
        return $this->DbHandler->getItem($options['tablename'], $options['return'], $options['whereFields'], $options['whereValues']);
    }

    public function addItemByParams(&$settings)
    {
        $id = $this->DbHandler->addItem($settings['tablename'], array_keys($settings['mainData']), array_values($settings['mainData']));

        return $id;
    }

    /**
     * Sets the session for sorters on the home page.
     *
     * @param string $where - can be 'backend' or 'frontend'
     * @param string $sort - order field
     */
    public function setSorters($where, $page, $repeater, $sort)
    {
        if ($_SESSION[$where][$page][$repeater]['orderby'] == $sort) {
            if ('ASC' == $_SESSION[$where][$page][$repeater]['ordertype']) {
                $_SESSION[$where][$page][$repeater]['ordertype'] = 'DESC';
            } else {
                $_SESSION[$where][$page][$repeater]['ordertype'] = 'ASC';
            }
        } else {
            $_SESSION[$where][$page][$repeater]['orderby'] = $sort;
            $_SESSION[$where][$page][$repeater]['ordertype'] = 'ASC';
        }
    }

    /**
     * Changes the visibility of an item.
     *
     * @param int $id - id of record, witch will be changed the visibility
     * @param int $flag - value witch contains a sum of flags
     */
    public function changeVisibility($id, $flag)
    {
        $action = ($flag & Config::FLAGS_VISIBILITY) == Config::FLAGS_VISIBILITY ? 'remove' : 'add';

        $this->DbHandler->setFlag($this->DbHandler->tableName, [$id], Config::FLAGS_VISIBILITY, $action);
    }

    /**
     * Changes the visibility of items.
     *
     * @param array $ids - array with records to change the visibility
     * @param $action - 'add' or 'remove'
     */
    public function changeSelectedVisibility($ids, $action)
    {
        $this->DbHandler->setFlag($this->DbHandler->tableName, $ids, Config::FLAGS_VISIBILITY, $action);
    }

    /**
     * Changes the flags of items.
     *
     * @param array $ids - array with records to change the visibility
     * @param $action - 'add' or 'remove'
     * @param $set - the flag value to set
     */
    public function changeFlags($ids, $action, $set)
    {
        $this->DbHandler->setFlag($this->DbHandler->tableName, $ids, $set, $action);
    }

    /**
     * Changes the flags of an item and removes all other items' flags.
     *
     * @param int $id
     * @param $action - 'add' or 'remove'
     * @param $set - the flag value to set
     */
    public function changeFlagSingle($id, $action, $set)
    {
        if ('add' == $action) {
            $this->DbHandler->setFlagAll($this->DbHandler->tableName, $set, 'remove');
        }
        $this->DbHandler->setFlag($this->DbHandler->tableName, [$id], $set, $action);
    }

    /**
     * Moves items to trash or delete items from trash.
     *
     * @param array $id - array of ids
     * @param bool $isTrash - whether record is on trash or not
     *
     * @return true - if query is ok
     */
    public function deleteItemsClicked($ids, $isTrash)
    {
        $transaction = $this->DbHandler->startTransaction();

        try {
            $arrayToTrash = $ids;

            if ($isTrash) {
                $this->DbHandler->deleteItemsFromTrash($this->DbHandler->tableName, 'id', $arrayToTrash);
            } else {
                $this->DbHandler->moveItemsToTrash($this->DbHandler->tableName, 'id', $arrayToTrash);
            }

            $transaction->commit();

            return true;
        } catch (Exception $e) {
            $transaction->rollBack();

            throw $e;
        }
    }

    /**
     * Restores items from trash that were selected.
     *
     * @param array $ids - Array of the ids of the items to be restored
     *
     * @return true - if query is ok
     */
    public function restoreItemsClicked($ids)
    {
        $transaction = $this->DbHandler->startTransaction();

        try {
            $this->DbHandler->restoreItemsFromTrash($this->DbHandler->tableName, 'id', $ids);

            $transaction->commit();

            return true;
        } catch (Exception $e) {
            $transaction->rollBack();

            throw $e;
        }
    }

    /**
     * Set the meta data info.
     *
     * @param array $options
     */
    public function setMetaData(&$options)
    {
        $name = $this->pluginName;
        $table = $this->DbHandler->DbPrefix . 'plugins';
        $plugin = $this->DbHandler->getItem($table, ['id'], ['name'], [$name]);
        $options['values']['plugin_id'] = $plugin['id'];

        $tableLang = $table . '_lang';
        $options['values']['lang'] = $this->getLangID($options['lang']);
        $this->DbHandler->replaceItem(
            $tableLang,
            array_keys($options['values']),
            array_values($options['values']),
            ['plugin_id', 'lang']
        );
    }

    /**
     * Get the meta data info.
     *
     * @param array $options
     *
     * @return array
     */
    public function getMetaData(&$options)
    {
        $name = $this->pluginName;
        $table = $this->DbHandler->DbPrefix . 'plugins';
        $plugin = $this->DbHandler->getItem($table, ['id'], ['name'], [$name]);

        $tableLang = $table . '_lang';

        return $this->DbHandler->getItem(
            $tableLang,
            $options['return'],
            ['plugin_id', 'lang'],
            [$plugin['id'], $this->getLangID($options['lang'])]
        );
    }

    /**
     * Get the options for the Menu.
     *
     * @return array
     */
    public function getMenuOptions()
    {
        return $GLOBALS[$this->pluginName]['MenuOptions'];
    }

    /**
     * Get the items for the Menu.
     *
     * @param array $settings
     *
     * @return array
     */
    public function getItemsForMenu(&$settings)
    {
        $options = [
            'count' => false,
            'flags' => ['values' => ['FLAGS_TRASH' => false]],
            'return' => ['id', 'value as title'],
            'orderby' => 'id',
            'ordertype' => 'ASC',
            'offset' => $settings['offset'],
            'limit' => $settings['limit'],
        ];
        if ($settings['keywords']) {
            $keywords = '%' . $settings['keywords'] . '%';
            $options['custom'] = [
                'fields' => ['value'],
                'values' => [$keywords],
            ];
        }
        $data = $this->DbHandler->getHomeItems($options);
        foreach ($data as $key => $value) {
            $data[$key]['parameters'] = $value['id'];
        }

        return $data;
    }

    /**
     * Returns a list of tables.
     */
    public function getTables()
    {
        $tables = [];
        $tables[] = $this->DbHandler->tableName;

        return $tables;
    }

    /**
     * Returns the Language ID given the name.
     *
     * @param string $name
     *
     * @return int
     */
    public function getLangID($name)
    {
        return 1;
    }

    /**
     * Temp getModel name function until configuration file is created.
     */
    private function getModel(ReflectionClass $reflectionClass): string
    {
        $namespace = $reflectionClass->getNamespaceName();
        $classShortName = $reflectionClass->getShortName();
        $modelShortName = str_replace('Controller', 'Model', $classShortName);

        return $namespace . '\\' . $modelShortName;
    }

    /**
     * Temp getPlugin function.
     *
     * @return string|string[]
     */
    private function getPlugin(ReflectionClass $reflectionClass): string
    {
        $classShortName = $reflectionClass->getShortName();

        return str_replace('Controller', '', $classShortName);
    }
}
