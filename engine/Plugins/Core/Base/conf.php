<?php

$GLOBALS['Base']['tableName'] = 'base';
$GLOBALS['Base']['fieldName'] = '';

// SQL MODEL
/* PostgreSQL
CREATE TABLE atom_base (
id SERIAL,
flag integer NOT NULL DEFAULT 0,
value character varying(255) NOT NULL DEFAULT '',
PRIMARY KEY (id)
);
*/
/* MySQL
CREATE TABLE `atom_base` (
`id` int(11) NOT NULL auto_increment,
`flag` int(11) NOT NULL default '0',
`value` varchar(255) NOT NULL,
PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
*/
