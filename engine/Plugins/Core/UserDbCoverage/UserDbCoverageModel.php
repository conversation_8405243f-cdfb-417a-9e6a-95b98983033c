<?php

namespace TF\Engine\Plugins\Core\UserDbCoverage;

use PDO;
use TF\Engine\Plugins\Core\UserDb\UserDbModel;

class UserDbCoverageModel extends UserDbModel
{
    public function getCoverageIntersections($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM {$options['tablename']} t1";
        $sql .= " INNER JOIN {$options['tablename']} t2 ON (ST_Overlaps(t1.geom, t2.geom))";
        $sql .= ' WHERE ST_IsValid(t1.geom)';
        $sql .= ' AND ST_IsValid(t2.geom)';

        $cmd = $this->DbModule->createCommand($sql);

        if ($returnOnlySQL) {
            return $sql;
        }

        return $cmd->query()->readAll();
    }

    public function getZPCoverageAreaDifference($zp_tablename, $coverage_tablename)
    {
        $this->makeGeomValid($coverage_tablename);
        $this->makeGeomValid($zp_tablename);

        $sql = "SELECT (SELECT ST_AREA(zp2.geom) from {$zp_tablename} zp2) - SUM(ST_AREA(ST_INTERSECTION(zp.geom, cov.geom))) as area FROM {$coverage_tablename} cov INNER JOIN {$zp_tablename} zp ON ST_INTERSECTS(zp.geom, cov.geom)";

        $cmd = $this->DbModule->createCommand($sql);

        $results = $cmd->query()->readAll();

        return $results[0]['area'];
    }

    public function getCoverageZPAreaDifference($zp_tablename, $coverage_tablename, $options)
    {
        $this->makeGeomValid($coverage_tablename);
        $this->makeGeomValid($zp_tablename);

        $sql = "SELECT (SELECT SUM(ST_AREA(cov2.geom)) from {$coverage_tablename} cov2) - SUM(ST_AREA(ST_INTERSECTION(zp.geom, cov.geom))) as area FROM {$coverage_tablename} cov INNER JOIN {$zp_tablename} zp ON ST_INTERSECTS(zp.geom, cov.geom)";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], false);
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        $results = $cmd->query()->readAll();

        return $results[0]['area'];
    }

    public function getCoverageZpMissedArea($zp_tablename, $event_tablename, $plot_id)
    {
        $sql = "SELECT round((ST_AREA(ST_Difference(zp.geom, ST_Union(cov.geom)))/1000)::numeric,3) as area
				FROM {$event_tablename} cov 
				INNER JOIN {$zp_tablename} zp ON ST_INTERSECTS(zp.geom, cov.geom) AND zp.id = :plot_id
				GROUP BY zp.geom";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':plot_id', $plot_id);
        $result = $cmd->query()->readAll();

        return $result[0]['area'];
    }

    public function makeGeomValid($tablename)
    {
        $sql = "UPDATE {$tablename} SET geom = ST_MakeValid(geom)";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function getZPCoverageIntersectItems($options, $counter, $returnOnlySQL)
    {
        $zp_tablename = $options['zp_tablename'];
        $cov_tablename = $options['cov_tablename'];

        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM {$zp_tablename} zp";
        $sql .= " INNER JOIN {$cov_tablename} cov ON(St_Intersects(zp.geom, cov.geom))";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options && false == $counter) {
            if ($options['sort']) {
                $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
            }

            $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
            $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

            if (false !== $limit && false !== $offset) {
                $sql .= $returnOnlySQL
                    ? " LIMIT {$limit} OFFSET {$offset}"
                    : ' LIMIT :limit OFFSET :offset';
            }
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }
}
