<?php

namespace TF\Engine\Plugins\Core\UserDbCoverage;

use TF\Engine\Plugins\Core\UserDb\UserDbController;

class UserDbCoverageController extends UserDbController
{
    public $DbHandler;
    public $Database;

    public function __construct($database)
    {
        $this->DbHandler = new UserDbCoverageModel($database);
        $this->Database = $database;
    }

    public function getCoverageIntersections($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getCoverageIntersections($options, $counter, $returnOnlySQL);
    }

    public function getZPCoverageAreaDifference($zp_tablename, $coverage_tablename)
    {
        return $this->DbHandler->getZPCoverageAreaDifference($zp_tablename, $coverage_tablename);
    }

    public function getCoverageZPAreaDifference($zp_tablename, $coverage_tablename, $options = [])
    {
        return $this->DbHandler->getCoverageZPAreaDifference($zp_tablename, $coverage_tablename, $options);
    }

    public function makeGeomValid($tablename)
    {
        $this->DbHandler->makeGeomValid($tablename);
    }

    public function getZPCoverageIntersectItems($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getZpCoverageIntersectItems($options, $counter, $returnOnlySQL);
    }

    public function getCoverageZpMissedArea($zp_tablename, $event_tablename, $plot_id)
    {
        return $this->DbHandler->getCoverageZpMissedArea($zp_tablename, $event_tablename, $plot_id);
    }
}
