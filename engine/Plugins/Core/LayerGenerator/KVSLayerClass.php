<?php

namespace TF\Engine\Plugins\Core\LayerGenerator;

use labelObj;

require 'AbstractLayerClass.php';

class KVSLayerClass extends AbstractLayerClass
{
    protected $tableName = 'layer_kvs';
    protected $mapscript;

    protected $connectionType = MS_POSTGIS;
    protected $gml_include_items = ['gid'];
    protected $labelName;
    protected $layerFileName = '_layer_kvs.map';
    protected $isIncluded = true;

    /**
     * KVS layer class constructor.
     *
     * @param array $layerData{
     *                          #item array extent{
     *                          #item double 0,
     *                          #item double 1,
     *                          #item double 2,
     *                          #item double 3
     *                          },
     *                          #item string label_name,
     *                          #item array label_color{
     *                          #item int 0,
     *                          #item int 1,
     *                          #item int 2,
     *                          },
     *                          #item integer label_size,
     *                          #item array layer_color{
     *                          #item int 0,
     *                          #item int 1,
     *                          #item int 2
     *                          },
     *                          #item array border_color{
     *                          #item int 0,
     *                          #item int 1,
     *                          #item int 2
     *                          },
     *                          #item integer transparent,
     *                          }
     * @param object $msMapObj
     * @param string $dbHost
     * @param string $dbName
     * @param string $dbUser
     * @param string $dbPass
     * @param string $dbPort
     *
     * @return type
     */
    public function __construct($layerData, $msMapObj, $dbHost = '', $dbName = '', $dbUser = '', $dbPass = '', $dbPort = '')
    {
        parent::__construct($layerData, $msMapObj, $dbHost, $dbName, $dbUser, $dbPass, $dbPort);

        $label = $this->createLabel($layerData['label_color'], $layerData['label_size']);

        $class = $this->addLayerClass([
            'class_name' => $this->tableName,
            'style_name' => $this->tableName,
            'layer_width' => 0.91,
            'layer_color' => $layerData['layer_color'],
            'layer_outlinecolor' => $layerData['border_color'],
        ]);
        $this->layerFileName = $layerData['userGroupID'] . $this->layerFileName;
        $class->addLabel($label);
    }

    /**
     * returns the datasource.
     *
     * @return string
     */
    public function getDataSource()
    {
        switch ($this->labelName) {
            case 'area_kvs':
                $label = 'ST_Area(geom) as area_kvs';

                break;
            case 'irrigated_area':
                $label = '(CASE WHEN irrigated_area = TRUE THEN document_area ELSE 0 END) AS irrigated_area';

                break;
            default:
                $label = $this->labelName;

                break;
        }

        return "geom FROM (SELECT gid, geom, {$label} FROM {$this->tableName} 
                                WHERE true AND is_edited = 'FALSE') as subquery using unique gid";
    }

    /**
     *  creates a mapscript label.
     *
     * @param array $color{
     *                      #item integer 0,
     *                      #item integer 1,
     *                      #item integer 2
     *                      }
     * @param int $size
     *
     * @return object
     */
    public function createLabel($color, $size)
    {
        $label = new labelObj();
        $label->set('encoding', 'UTF-8');
        $label->set('font', 'arial');
        // $label->set("type", "truetype");
        $label->set('angle', 'auto');
        $label->set('antialias', MS_TRUE);
        $label->color->setRGB($color[0], $color[1], $color[2]);
        $label->set('maxscaledenom', 12500);
        $label->set('position', MS_AUTO);
        $label->set('align', MS_ALIGN_CENTER);
        $label->set('size', $size);

        return $label;
    }
}
