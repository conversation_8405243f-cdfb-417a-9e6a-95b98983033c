<?php

namespace TF\Engine\Plugins\Core\LayerGenerator;

use labelObj;

class TopicLayerByOwnerNameLabelItems extends AbstractLayerClass
{
    protected $tableName = 'topic_layer_kvs_by_owner_name_label_items';
    protected $mapscript;
    protected $ekate;
    protected $connectionType = MS_POSTGIS;
    protected $gml_include_items = ['gid'];
    protected $labelName;

    public function __construct($layerData, $msMapObj, $dbHost = '', $dbName = '', $dbUser = '', $dbPass = '', $dbPort = '')
    {
        parent::__construct($layerData, $msMapObj, $dbHost, $dbName, $dbUser, $dbPass, $dbPort);
        $this->ekate = $layerData['ekate'];
        $label = $this->createLabel($layerData['label_color'], $layerData['label_size']);
        $class = $this->addLayerClass([
            'class_name' => $this->tableName,
            'style_name' => $this->tableName,
            'layer_width' => 0.91,
        ]);
        $class->addLabel($label);
    }

    public function createLabel($color, $size)
    {
        $label = new labelObj();
        $label->set('encoding', 'UTF-8');
        $label->set('angle', 'auto');
        $label->set('size', $size);
        $label->set('antialias', MS_TRUE);
        $label->color->setRGB($color[0], $color[1], $color[2]);
        // $label->set("type", "truetype");
        $label->set('maxscaledenom', 6500);
        $label->set('position', MS_AUTO);
        $label->set('align', MS_ALIGN_CENTER);
        $label->set('font', 'arial');
        $label->set('minfeaturesize', 2);
        $label->set('mindistance', 0);
        $label->set('buffer', 0);
        $label->set('maxsize', 2000);
        $label->set('wrap', ',');

        return $label;
    }

    public function getDataSource()
    {
        return "geom FROM (select * from topic_layer_kvs_by_owner_name_label_items where ekate = '{$this->ekate}') as subquery using unique geom using srid=32635";
    }
}
