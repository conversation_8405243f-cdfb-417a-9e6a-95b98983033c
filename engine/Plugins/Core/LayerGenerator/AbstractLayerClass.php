<?php

namespace TF\Engine\Plugins\Core\LayerGenerator;

/**
 * <AUTHOR>
abstract class AbstractLayerClass
{
    protected $tableName;
    protected $mapscript;
    protected $connectionType = MS_POSTGIS;
    protected $mapscriptLayerType = MS_LAYER_POLYGON;
    protected $projection = '+proj=utm +datum=WGS84 +zone=35 +units=m +north +no_defs';
    protected $gml_include_items = ['gid'];
    protected $labelName;
    protected $layerFileName;
    protected $isIncluded = false;

    /**
     * Abstract class constructor.
     *
     * @param array $layerData{
     *                          #item array extent{
     *                          #item double 0,
     *                          #item double 1,
     *                          #item double 2,
     *                          #item double 3
     *                          },
     *                          #item string label_name,
     *                          #item integer transparent
     *                          }
     * @param object $msMapObj
     * @param string $dbHost
     * @param string $dbName
     * @param string $dbUser
     * @param string $dbPass
     * @param string $dbPort
     *
     * @return type
     */
    protected function __construct($layerData, $msMapObj, $dbHost = '', $dbName = '', $dbUser = '', $dbPass = '', $dbPort = '')
    {
        $this->mapscript = ms_newlayerobj($msMapObj);
        $this->mapscript->set('name', $this->tableName);
        $this->mapscript->set('type', $this->mapscriptLayerType);
        $this->mapscript->set('dump', 'true');
        $this->mapscript->set('template', $this->tableName);

        $this->isIncluded = $layerData['isIncluded'];

        $this->mapscript->setExtent($layerData['extent'][0], $layerData['extent'][1], $layerData['extent'][2], $layerData['extent'][3]);
        $this->mapscript->setProcessing('LABEL_NO_CLIP=on');
        $this->mapscript->setProcessing('CLOSE_CONNECTION=DEFER');

        $this->mapscript->setConnectionType($this->connectionType);

        if ($this->connectionType = MS_POSTGIS) {
            $connection = 'host=' . $dbHost . ' dbname=' . $dbName . ' user=' . $dbUser . ' password=' . $dbPass . ' port=' . $dbPort;
            $this->mapscript->set('connection', $connection);
        }
        $this->labelName = $layerData['label_name'];
        $layerQuery = $this->getDataSource();
        $this->mapscript->set('data', $layerQuery);

        $this->mapscript->set('labelitem', $layerData['label_name']);

        $this->mapscript->setMetaData('ows_title', $this->tableName);
        $this->mapscript->setMetaData('gml_include_items', implode(', ', $this->gml_include_items));

        $this->mapscript->set('opacity', $layerData['transparent']);
        $this->mapscript->setProjection($this->projection);

        $this->mapscript->set('status', MS_OFF);
    }

    /**
     * returns the mapscript layer object.
     *
     * @return object
     */
    public function getMapscript()
    {
        return $this->mapscript;
    }

    /**
     * sets the debug level of the layer (in order of verbosity 1-5).
     *
     * @param int $debugLevel
     */
    public function setDebugLevel($debugLevel = 3)
    {
        $this->mapscript->set('debug', $debugLevel);
    }

    /**
     * save layer object mapscript to file.
     *
     * @param string $filePath
     */
    public function save($filePath)
    {
        file_put_contents($filePath, $this->mapscript->convertToString());
    }

    /**
     * get layer file name.
     *
     * @return string
     */
    public function getLayerFileName()
    {
        return $this->layerFileName;
    }

    /**
     * get layer index.
     *
     * @return int
     */
    public function getLayerIndex()
    {
        return $this->mapscript->index;
    }

    public function getIsIncluded()
    {
        return $this->isIncluded;
    }

    /**
     * returns the datasource (must be declaired in child class).
     *
     * @return type
     */
    abstract protected function getDataSource();

    /**
     * adds class to the layer.
     *
     * @param array $attr{
     *                     #item string class_name
     *                     #item string expression
     *                     #item string layer_width
     *                     #item array layer_color{
     *                     #item int 0,
     *                     #item int 1,
     *                     #item int 2
     *                     }
     *                     #item array layer_outlinecolor{
     *                     #item int 0,
     *                     #item int 1,
     *                     #item int 2
     *                     }
     *                     }
     *
     * @return object
     */
    protected function addLayerClass($attr)
    {
        $class = new classObj($this->mapscript);
        $class->set('name', $attr['class_name']);

        if ($attr['expression']) {
            $class->setExpression($attr['expression']);
        }

        $style = new styleObj($class);
        $style->set('width', $attr['layer_width']);
        $style->color->setRGB($attr['layer_color'][0], $attr['layer_color'][1], $attr['layer_color'][2]);
        $style->outlinecolor->setRGB($attr['layer_outlinecolor'][0], $attr['layer_outlinecolor'][1], $attr['layer_outlinecolor'][2]);

        return $class;
    }

    /**
     * adds class with symbol to the layer.
     *
     * @param array $attr{
     *                     #item string class_name
     *                     #item string expression
     *                     #item string layer_width
     *                     #item array layer_color{
     *                     #item int 0,
     *                     #item int 1,
     *                     #item int 2
     *                     }
     *                     #item array layer_outlinecolor{
     *                     #item int 0,
     *                     #item int 1,
     *                     #item int 2
     *                     }
     *                     #item string symbol_name
     *                     }
     *
     * @return object
     */
    protected function addSymbolLayerClass($attr)
    {
        $class = new classObj($this->mapscript);
        $class->set('name', $attr['class_name']);

        if ($attr['expression']) {
            $class->setExpression($attr['expression']);
        }

        $style = new styleObj($class);
        $style->set('width', $attr['layer_width']);
        $style->color->setRGB($attr['layer_color'][0], $attr['layer_color'][1], $attr['layer_color'][2]);
        $style->outlinecolor->setRGB($attr['layer_outlinecolor'][0], $attr['layer_outlinecolor'][1], $attr['layer_outlinecolor'][2]);

        if ($attr['symbol_name']) {
            $style->set('symbolname', $attr['symbol_name']);
        }

        return $class;
    }
}
