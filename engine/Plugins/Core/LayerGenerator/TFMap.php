<?php

namespace TF\Engine\Plugins\Core\LayerGenerator;

require 'KVSLayerClass.php';
class TFMap
{
    protected $mapscript;
    protected $layerTypes = [];
    protected $layers = [];
    protected $userGroupID;

    /**
     * Creates a basic mapfile with no layers.
     *
     * @param int $userGroupID
     */
    public function __construct($userGroupID)
    {
        $this->userGroupID = $userGroupID;
        $this->mapscript = ms_newmapobjfromstring('MAP END');

        $this->mapscript->set('name', 'Technofarm Map');
        $this->mapscript->setSize(150, 150);
        $this->mapscript->set('units', MS_METERS);

        $this->mapscript->setProjection('+proj=utm +datum=WGS84 +zone=35 +units=m +north +no_defs');   // Set map projection from PROJ.4 definition string proj4.

        $this->mapscript->imagecolor->setRGB(255, 255, 255);

        $this->mapscript->setFontSet(SITE_PATH . 'public/lib/fonts/fonts.list');
        $this->mapscript->setSymbolSet(SITE_PATH . 'maps/symbolset');

        // $outputformat = ms_newOutputFormatObj('AGG/PNG', 'png');
        $this->mapscript->outputformat->set('extension', 'png');
        $this->mapscript->outputformat->set('mimetype', 'image/png');
        $this->mapscript->outputformat->setOption('imagemode', MS_IMAGEMODE_RGB);
        $this->mapscript->outputformat->setOption('QUALITY', 95);

        $this->mapscript->legend->set('status', MS_ON);
        // $this->mapscript->legend->set("position", MS_AUTO);
        $this->mapscript->legend->set('keysizex', 18);
        $this->mapscript->legend->set('keysizey', 12);
        $this->mapscript->legend->imagecolor->setRGB(255, 255, 255);

        $this->mapscript->legend->label->color->setRGB(0, 0, 89);
        $this->mapscript->legend->label->set('type', MS_BITMAP);                                    // MS_TRUETYPE IF FORCE NEEDED
        $this->mapscript->legend->label->set('size', MS_MEDIUM);

        $this->mapscript->web->set('imagepath', '/tmp/');
        $this->mapscript->web->set('imageurl', '/tmp/');

        $this->mapscript->web->metadata->set('wms_title', 'Technofarm');
        $this->mapscript->web->metadata->set('wms_onlineresource', WMS_SERVER);
        $this->mapscript->web->metadata->set('wms_srs', WMS_SRS);
        $this->mapscript->web->metadata->set('wms_enable_request', '*');
        $this->mapscript->web->metadata->set('wfs_title', 'Technofarm');
        $this->mapscript->web->metadata->set('wfs_enable_request', '*');
        $this->mapscript->web->metadata->set('wfs_srs', 'EPSG:900913');
        $this->mapscript->web->metadata->set('wfs_onlineresource', WMS_SERVER);
    }

    /**
     * Register map layer in the mapfile.
     *
     * @param string $type
     * @param string $class
     */
    public function registerLayerType($type, $class)
    {
        $this->layerTypes[$type] = $class;
    }

    /**
     * Adds a maplayerObj to the mapObj.
     *
     * @param array $layerData{
     *                          }
     * @param string $dbHost
     * @param string $dbName
     * @param string $dbUser
     * @param string $dbPass
     * @param string $dbPort
     *
     * @return object
     */
    public function createLayer($layerData, $dbHost = '', $dbName = '', $dbUser = '', $dbPass = '', $dbPort = '')
    {
        $className = $this->layerTypes[$layerData['layer_type']];
        $layerData['userGroupID'] = $this->userGroupID;
        $layer = new $className($layerData, $this->mapscript, $dbHost, $dbName, $dbUser, $dbPass, $dbPort);
        $this->layers[$layer->getLayerIndex()] = $layer;

        return $layer;
    }

    /**
     * returns the mapscript object of a layer.
     *
     * @param string $layer_name
     *
     * @return mapscript object
     */
    public function getMapscriptLayerByName($layer_name)
    {
        $msLayer = $this->mapscript->getLayerByName($layer_name);
    }

    /**
     * returns the mapscript string.
     *
     * @return string
     */
    public function getMapscriptString()
    {
        return $this->mapscript->convertToString();
    }

    /**
     * sets the location of the debug file (must be set if debugging).
     *
     * @param type $logfile
     */
    public function setDebugFile($logfile)
    {
        $this->mapscript->setConfigOption(MS_ERRORFILE, $logfile);
    }

    /**
     * sets the debug level on the mapObj (in order of verbosity 1-5).
     *
     * @param int $debugLevel
     */
    public function setDebugLevel($debugLevel = 3)
    {
        $this->mapscript->setConfigOption(DEBUG, $debugLevel);
    }

    /**
     * saves the mapObj into a file.
     *
     * @param string $path
     */
    public function saveMapfile($path)
    {
        $includes = '';

        foreach ($this->layers as $index => $layer) {
            if (true == $layer->getIsIncluded()) {
                $includes .= "INCLUDE '" . WMS_MAP_PATH . $layer->getLayerFileName() . "'\n";
                $layer->save(WMS_MAP_PATH . $layer->getLayerFileName());
                $this->mapscript->removeLayer($index);
            }
        }
        $includes .= 'END # MAP';

        $this->mapscript->save($path);

        $mapString = file_get_contents($path);
        $mapString = str_replace('END # MAP', $includes, $mapString);
        file_put_contents($path, $mapString);
    }
}
