<?php

namespace TF\Engine\Plugins\Core\UserDbOverlaps;

use PDO;
use TF\Engine\Plugins\Core\UserDb\UserDbModel;

class UserDbOverlapsModel extends UserDbModel
{
    public function updateOverlapItemStatus($id, $status)
    {
        $sql = 'UPDATE ' . $this->tableOverlaps . ' SET status = :status WHERE id = :id';

        $cmd = $this->DbModule->createCommand($sql);

        $cmd->bindParameter(':status', $status);
        $cmd->bindParameter(':id', $id);

        $cmd->execute();
    }

    public function getOverlapData($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM " . $this->tableOverlapsData . ' o
    			LEFT JOIN ' . $this->tableKVS . ' p ON(p.gid = o.gid)'
                    . ' WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options && false == $counter) {
            if ($options['sort']) {
                $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
            }

            $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
            $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

            if (false !== $limit && false !== $offset) {
                $sql .= $returnOnlySQL
                    ? " LIMIT {$limit} OFFSET {$offset}"
                    : ' LIMIT :limit OFFSET :offset';
            }
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getOverlapsMapExtent($overlapid)
    {
        $tablename = $this->tableOverlapsData;
        $tableKVS = $this->tableKVS;
        $tablePlotsRel = $this->contractsPlotsRelTable;
        $tableContracts = $this->tableContracts;

        $sql = "SELECT ST_Extent(kvs.geom) as extent FROM {$tableKVS} kvs 
                INNER JOIN {$tablename} t ON (kvs.gid = t.gid)
                LEFT JOIN {$tablePlotsRel} rel ON (rel.plot_id = kvs.gid)
                LEFT JOIN {$tableContracts} cont ON (rel.contract_id = cont.id AND ((cont.due_date>='" . date('Y-m-d') . "' AND cont.start_date<='" . date('Y-m-d') . "' AND cont.active=TRUE AND cont.parent_id=0) OR cont.nm_usage_rights = 1)) 
                WHERE TRUE AND t.overlap_id = :id AND t.has_match = TRUE";

        $cmd = $this->DbModule->createCommand($sql);

        $cmd->bindParameter(':id', $overlapid);

        return $cmd->query()->readAll();
    }
}
