<?php

namespace TF\Engine\Plugins\Core\UserDbOverlaps;

use TF\Engine\Plugins\Core\UserDb\UserDbController;

class UserDbOverlapsController extends UserDbController
{
    public $DbHandler;
    public $Database;

    public function __construct($database)
    {
        $this->DbHandler = new UserDbOverlapsModel($database);
        $this->Database = $database;
    }

    public function updateOverlapItemStatus($id, $status)
    {
        $this->DbHandler->updateOverlapItemStatus($id, $status);
    }

    public function getOverlapData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getOverlapData($options, $counter, $returnOnlySQL);
    }

    public function getOverlapsMapExtent($overlapid)
    {
        return $this->DbHandler->getOverlapsMapExtent($overlapid);
    }
}
