<?php

namespace TF\Engine\Plugins\Core\Payments;

use TF\Engine\Plugins\Core\UserDb\UserDbModel;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;

class PaymentsModel extends UserDbModel
{
    /**
     * Get filted plots with their owners, rents, charged rents, contracts and annexes.
     *
     * @param int $year the year for which to get the payment plots
     * @param int $contractAnnexId the ID of the contract annex
     * @param int $ownerId the ID of the owner
     * @param null|string $path the path to filter by (optional)
     * @param array $filterParams additional filter parameters (optional)
     *
     * @return array the result set of payment plots
     */
    public function getPaymentPlots($year, $contractAnnexId, $ownerId, $path = null, $filterParams = [])
    {
        $farmYearStart = ($GLOBALS['Farming']['years'][$year]['year'] - 1) . '-10-01';
        $farmYearEnd = $GLOBALS['Farming']['years'][$year]['year'] . '-09-30';

        $where = '';

        if ($ownerId) {
            $where .= ' AND o.id = ' . $ownerId;
            if (!$path) {
                $where .= ' AND po.path is null';
            }
        }

        if ($path) {
            $where .= " AND po.path = '" . $path . "'";
        }
        if ($contractAnnexId) {
            $where .= ' AND c.id = ' . $contractAnnexId;
        }
        if (!empty($filterParams['payroll_ekate']) && '' != $filterParams['payroll_ekate'][0]) {
            $where .= " AND kvs.ekate IN ('" . implode("','", $filterParams['payroll_ekate']) . "')";
        }
        if (!empty($filterParams['payroll_farming']) && '' != $filterParams['payroll_farming'][0]) {
            $hasNullValue = in_array(null, $filterParams['payroll_farming']);

            if ($hasNullValue) {
                $filterParams['payroll_farming'] = array_filter($filterParams['payroll_farming'], function ($value) {
                    return null !== $value;
                });
            }
            $where .= ' AND (c.farming_id IN (' . implode(', ', $filterParams['payroll_farming']) . ')'
                . ($hasNullValue ? 'OR c.farming_id IS NULL' : '') . ')';
        }

        $isDead = UserDbOwnersController::isDead('o', [$farmYearStart, $farmYearEnd]);

        // Вземат се всички имото по даденит критерии и стопанска година
        $query = "
            SELECT 
                c.id as contract_id,
                c.parent_id as parent_id,
                c.c_num as c_num,
                cg.name as contract_group_name,
                c.farming_id as farming_id,
                c.farming_name as farming_name,
                c.nm_usage_rights as nm_usage_rights,
                c.virtual_contract_type as contract_type,
                c.start_date as contract_start_date,
                c.due_date as contract_due_date,
                c.virtual_contract_type as contract_type,
                c.osz_num as osz_num,
                c.osz_date as osz_date,
                c.sv_num as sv_num,
                to_char(c.sv_date, 'YYYY-MM-DD') as sv_date,
                a. id as annex_id,
                pc.id as pc_rel_id,
                kvs.gid as plot_id,
                kvs.kad_ident as kad_ident,
                kvs.mestnost as mestnost,
                kvs.virtual_category_title as category,
                kvs.virtual_ekatte_name as ekatte_name,
                kvs.virtual_ntp_title as area_type,
                o.id as owner_id,
                o.lk_nomer as lk_nomer,
                o.lk_izdavane as lk_izdavane,
                o.phone,
                o.mobile,
                (CASE WHEN o.owner_type = 1 THEN o.egn ELSE o.eik END) as egn_eik,
                (CASE WHEN o.owner_type = 1 THEN o.address ELSE o.company_address END) as address,
                (CASE WHEN rep.id is null THEN o.rent_place ELSE rep.rent_place END) as rent_place_ekatte,
                rp.ekatte_name as rent_place_name,
                {$isDead},
                o.dead_date as dead_date,
                o.post_payment_fields as owner_post_payment_fields,
                o.iban as iban,
                (CASE
                    WHEN o.is_dead = false THEN true
                    WHEN o.is_dead = true and o.dead_date is null THEN false
                    WHEN o.is_dead = true AND o.dead_date BETWEEN '" . $farmYearStart . "' AND '" . $farmYearEnd . "' THEN true
                    WHEN o.is_dead = true AND o.dead_date > '" . $farmYearStart . "' THEN true
                    ELSE false
                END) as allow_owner_payment,
                (CASE
                    WHEN o.is_dead = false THEN true
                    WHEN o.is_dead = true AND o.dead_date BETWEEN '" . $farmYearStart . "' AND '" . $farmYearEnd . "' THEN true
                    ELSE false
                END) as dead_date_in_current_farm_year,
                (CASE
                    WHEN o.is_dead = false THEN true
                    WHEN o.is_dead = true AND o.dead_date > '" . $farmYearEnd . "' THEN true
                    ELSE false
                END) as dont_show_heritoris,
                (o.is_dead = false) as is_heritor,
                po.path as path,
                po.owner_id || '_' || COALESCE(po.path::text, 0::text) as owner_path_key,
                (case when o.owner_type = 1 then concat(o.name, ' ', o.surname, ' ', o.lastname) else o.company_name  end) as owner_names,
                concat(op.name, ' ', op.surname, ' ', op.lastname) as owner_parent_names,
                op.id as owner_parent_id,
                CASE WHEN rep.id NOTNULL
                     THEN concat(rep.rep_name, ' ', rep.rep_surname, ' ', rep.rep_lastname) 
                     ELSE NULL
                END as rep_names,
                rep.iban as rep_iban,
                rep.rep_egn,
                rep.rep_address,
                rep.rep_lk,
                rep.rep_lk_izdavane,
                po.percent as plots_percent,
                pc.area_for_rent * po.percent / 100 as plot_owned_area,
                pc.contract_area * po.percent / 100 as plot_owned_contract_area,
                pc.kvs_allowable_area * po.percent / 100 as cultivated_area,
                SUM((pc.area_for_rent * po.percent / 100)::numeric) OVER (PARTITION BY o.id, c.id, po.path) AS contract_owned_area,
                -- SUM((pc.area_for_rent * po.percent / 100)::numeric) OVER (PARTITION BY pc.id, o.id) AS plot_owned_area_total,
                (
                    select 
                        SUM((pc_sub.area_for_rent * po_sub.percent / 100)::numeric)
                    from su_contracts_plots_rel pc_sub
                    inner join su_plots_owners_rel po_sub on (po_sub.pc_rel_id = pc_sub.id)
                    where 
                        pc_sub.id = pc.id
                        and po_sub.owner_id = o.id
                ) as plot_owned_area_total,
                CASE WHEN a.id IS NOT NULL THEN a.renta ELSE c.renta END as contract_renta_value,
                pc.rent_per_plot as rent_per_plot_value,
                cr.renta as charged_renta_value,
                (
                    SELECT
                        json_agg(renta_nat_results)
                    FROM (
                        SELECT
                            json_build_object(
                                'renta_nat_id', scr.renta_id,
                                'renta_nat_name', srt.name,
                                'unit_id', srt.unit,
                                'unit_value', srt.unit_value,
                                'nat_value', scr.renta_value,
                                'unit_value', COALESCE(MAX(srt.unit_value), 0)
                            ) AS renta_nat_results
                        FROM su_contracts_rents scr 
                        LEFT JOIN su_renta_types srt ON srt.id = scr.renta_id
                        WHERE 
                            scr.contract_id = (case when a.id is null then c.id else a.id end)
                        GROUP BY scr.renta_id, srt.name, srt.unit, srt.unit_value, scr.renta_value
                    ) AS renta_nat_query
                ) AS renta_nat_json,
                (
                	SELECT
                        json_agg(renta_nat_results)
                    FROM (
                        SELECT
                            json_build_object(
                                'charged_renta_nat_id', crn.nat_type,
                                'charged_renta_nat_name', srt.name,
                                'amount', crn.amount,
                                'nat_is_converted', crn.nat_is_converted,
                                'unit_value', srt.unit_value
                            ) AS renta_nat_results
                        FROM su_charged_renta cr 
                        LEFT JOIN su_charged_renta_natura crn ON crn.renta_id = cr.id 
                        LEFT JOIN su_renta_types srt ON srt.id = crn.nat_type
                        WHERE 
                            crn.nat_type is not null
                            AND cr.contract_id = (case when c.parent_id is not null and c.parent_id > 0 then c.parent_id else c.id end) 
                            AND cr.plot_id = kvs.gid
                            AND cr.owner_id = coalesce(subpath(po.path,0,1)::text::numeric, o.id) 
                            AND cr.year = '" . $year . "'
                        GROUP BY crn.nat_type, srt.name, crn.amount, crn.nat_is_converted, srt.unit_value
                    ) AS charged_renta_nat_query
                ) AS charged_renta_nat_json
            from su_contracts c
            left join su_contracts a on (a.parent_id = c.id and a.active = true and a.start_date <= '" . $farmYearEnd . "' and a.due_date >= '" . $farmYearStart . "')
            left join su_contract_group cg on cg.id = (case when a.group is null then c.group else a.group end)
            inner join su_contracts_plots_rel pc on (pc.contract_id = (case when a.id is null then c.id else a.id end))
            inner join layer_kvs kvs on (kvs.gid = pc.plot_id)
            inner join su_plots_owners_rel po on (po.pc_rel_id = pc.id)
            inner join su_owners o on (o.id = po.owner_id)
            left join su_owners op on (op.id = subltree(path, 0, 1)::text::numeric)
            left join su_owners_reps rep on (rep.id = po.rep_id)
            left join ekate_combobox rp on (rp.ekate = (CASE WHEN rep.id is null THEN o.rent_place ELSE rep.rent_place END))
            left join su_personal_use pu on (pu.owner_id = o.id and pu.year in (" . $year . ') and pu.pc_rel_id = pc.id)
            left join su_charged_renta cr on (cr.contract_id = (case when c.parent_id is not null and c.parent_id > 0 then c.parent_id else c.id end) and cr.plot_id = kvs.gid and cr.owner_id = coalesce(subpath(po.path, 0, 1)::text::numeric, o.id) and cr.year = ' . $year . ")
            left join su_contracts c2 on c.id = c2.parent_id and c2.active = true and c2.start_date <= '" . $farmYearEnd . "' and c2.due_date >= '" . $farmYearStart . "'
            where
                c.active = true
                and pc.annex_action = 'added'
                and po.percent > '0'
                and (case when kvs.is_edited = false then true else kvs.edit_date > '" . $farmYearStart . "' end) = 'TRUE'
                and c.start_date <= '" . $farmYearEnd . "' and c.due_date >= '" . $farmYearStart . "'
                and (c2.id is null or c2.start_date > c.due_date) /* skip contracts with annexes */
                " . $where . '
            group by
                o.id,
                op.id,
                rep.id,
                c.id,
                a.id,
                cg.name,
                pc.id,
                po.id,
                cr.id,
                kvs.gid,
                rp.ekatte_name
        ';

        return $this->getDataByQuery($query);
    }

    /**
     * Get contracts based on the specified criteria. It is used for the owner payments.
     *
     * @param int $year the year for which to get the owner contracts
     * @param array $filterParams additional filter parameters (optional)
     *
     * @return array the result set of owner contracts
     */
    public function getOwnerContracts($year, $filterParams = []): array
    {
        $farmYearStart = ($GLOBALS['Farming']['years'][$year]['year'] - 1) . '-10-01';
        $farmYearEnd = $GLOBALS['Farming']['years'][$year]['year'] . '-09-30';

        $where = '';

        if (!empty($filterParams['owner_id'])) {
            $where .= ' AND o.id = ' . $filterParams['owner_id'];
        }

        if (!empty($filterParams['egn'])) {
            $where .= " AND o.egn = '" . trim($filterParams['egn']) . "'";
            $where .= ' AND spor.is_heritor = false';
        }

        if (!empty($filterParams['company_name'])) {
            $where .= " AND o.company_name ilike '%" . trim($filterParams['company_name']) . "%'";
        }

        if (!empty($filterParams['company_eiks'])) {
            $where .= " AND o.eik ilike '%" . trim($filterParams['company_eiks']) . "%'";
        }

        if (!empty($filterParams['rent_place'])) {
            $where .= " AND o.rent_place = '" . trim($filterParams['rent_place']) . "'";
        }

        if (!empty($filterParams['rep_egn'])) {
            $where .= " AND sor.rep_egn = '" . trim($filterParams['rep_egn']) . "'";
        }

        if (!empty($filterParams['heritor_egn'])) {
            $where .= " AND o.egn = '" . trim($filterParams['heritor_egn']) . "'";
            $where .= ' AND spor.is_heritor = true';
        }

        if (!empty($filterParams['rep_rent_place'])) {
            $where .= " AND sor.rent_place = '" . trim($filterParams['rep_rent_place']) . "'";
        }

        // $filterParams['owner_type'] !== '1,2' да се махне в новия ФЕ
        if (isset($filterParams['owner_type']) && '' !== $filterParams['owner_type'] && (false == strpos($filterParams['owner_type'], ','))) {
            $where .= ' AND o.owner_type = ' . $filterParams['owner_type'];
        }

        if ($filterParams['owner_names']) {
            $tmp_person_names = preg_replace('/\s+/', '.*', trim($filterParams['owner_names']));
            $tmp_person_names = mb_strtolower($tmp_person_names, 'UTF-8');

            $where .= ' AND lower(TRIM (o.name)) || \' \' || lower(TRIM (o.surname)) || \' \' || lower(TRIM (o.lastname)) || \' \' ~ \'' . $tmp_person_names . '\'';
            $where .= ' AND spor.is_heritor = false';
        }

        if ($filterParams['heritor_names']) {
            $tmp_person_names = preg_replace('/\s+/', '.*', trim($filterParams['heritor_names']));
            $tmp_person_names = mb_strtolower($tmp_person_names, 'UTF-8');

            $where .= ' AND lower(TRIM (o.name)) || \' \' || lower(TRIM (o.surname)) || \' \' || lower(TRIM (o.lastname)) || \' \' ~ \'' . $tmp_person_names . '\'';
            $where .= ' AND spor.is_heritor = true';
        }

        if ($filterParams['rep_names']) {
            $tmp_rep_names = preg_replace('/\s+/', '.*', trim($filterParams['rep_names']));
            $tmp_rep_names = mb_strtolower($tmp_rep_names, 'UTF-8');

            $where .= ' AND lower(TRIM (sor.rep_name)) || \' \' || lower(TRIM (sor.rep_surname)) || \' \' || lower(TRIM (sor.rep_lastname)) || \' \'  ~ \'' . $tmp_rep_names . '\'';
        }

        if (!empty($filterParams['payroll_farming']) && '' != $filterParams['payroll_farming'][0]) {
            $hasNullValue = in_array(null, $filterParams['payroll_farming']);
            if ($hasNullValue) {
                $filterParams['payroll_farming'] = array_filter($filterParams['payroll_farming'], function ($value) {
                    return null !== $value;
                });
            }

            $where .= ' AND (c.farming_id IN (' . implode(', ', $filterParams['payroll_farming']) . ')'
                . ($hasNullValue ? 'OR c.farming_id IS NULL' : '') . ')';
        }

        if (!empty($filterParams['payroll_ekate']) && '' != $filterParams['payroll_ekate'][0]) {
            $where .= " AND kvs.ekate IN ('" . implode("','", $filterParams['payroll_ekate']) . "')";
        }

        $query = "
                WITH contract_data AS (
                    SELECT
                        c.id AS contract_id, 
                        string_agg(DISTINCT c.parent_id::text, ',') AS parent_id,
                        string_agg(DISTINCT o.id::text, ',') AS owner_ids 
                    FROM su_plots_owners_rel spor 
                    LEFT JOIN su_contracts_plots_rel scpr 
                        ON scpr.id = spor.pc_rel_id 
                        AND scpr.annex_action = 'added'
                    LEFT JOIN su_contracts c 
                        ON c.id = scpr.contract_id 
                    LEFT JOIN su_owners o 
                        ON o.id = spor.owner_id
                    LEFT JOIN layer_kvs kvs 
                        ON kvs.gid = scpr.plot_id
                    LEFT JOIN su_owners_reps sor 
                        ON sor.id = spor.rep_id
                    WHERE 
                        c.start_date <= '" . $farmYearEnd . "' 
                        AND c.due_date >= '" . $farmYearStart . "'
                        AND c.active = true
                        " . $where . "
                    GROUP BY c.id
                )
                SELECT *
                FROM contract_data cd
                WHERE NOT EXISTS (
                    SELECT 1
                    FROM contract_data other
                    WHERE cd.contract_id = ANY(string_to_array(other.parent_id, ',')::int[])
                );
        ";

        return $this->getDataByQuery($query);
    }

    /**
     * Get personal use data for owners based on the specified options.
     *
     * @param array $options the options for the query
     * @param bool $counter whether to return a count of the results
     * @param bool $returnOnlySQL whether to return only the SQL query string
     *
     * @return array|string the result set of personal use data or the SQL query string
     */
    public function getPersonalUseForOwners($options, $counter, $returnOnlySQL)
    {
        // chosen year is used multiple times in query
        $chosen_year = $options['chosen_years'];
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);
        $sql = "SELECT {$return} FROM {$this->tableContracts} c";
        $contractJoinDatesConditions = '';

        if ($options['start_date']) {
            $contractJoinDatesConditions .= ' AND a.start_date <= :start_date ';
        }

        if ($options['due_date']) {
            $contractJoinDatesConditions .= ' AND a.due_date >= :due_date ';
        }

        $sql .= " LEFT JOIN {$this->tableContracts} a ON(a.parent_id = c.id AND a.active = true " . $contractJoinDatesConditions . ')';
        $sql .= " INNER JOIN {$this->contractsPlotsRelTable} pc ON(pc.contract_id = (CASE WHEN a.id IS NULL THEN c.id ELSE a.id END))";
        $sql .= " LEFT JOIN {$this->tablePersonalUse} pu ON(pu.year in ({$chosen_year}) AND pu.pc_rel_id = pc.id)";
        $sql .= ' LEFT JOIN su_personal_use_rents spur on spur.pu_id = pu.id';
        $sql .= ' LEFT JOIN su_renta_types srt on srt.id = spur.renta_type';
        $sql .= ' WHERE spur.area > 0';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['contract_id_string']) {
            $sql .= " AND a.id IN({$options['contract_id_string']})";
        }

        if ($options['owner_id_string']) {
            $sql .= " AND o.id IN({$options['owner_id_string']})";
        }

        if ($options['start_date']) {
            $sql .= ' AND (c.start_date <= :start_date OR a.start_date <= :start_date)';
        }

        if ($options['due_date']) {
            $sql .= ' AND (c.due_date >= :due_date OR a.due_date >= :due_date)';
        }

        if ($options['group'] && false === $counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            $sql = str_replace(':start_date', "'" . $options['start_date'] . "'", $sql);
            $sql = str_replace(':due_date', "'" . $options['due_date'] . "'", $sql);

            return str_replace(':charged_year', "'" . $chosen_year . "'", $sql);
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['start_date']) {
            $cmd->bindParameter(':start_date', $options['start_date']);
        }

        if ($options['due_date']) {
            $cmd->bindParameter(':due_date', $options['due_date']);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }
}
