<?php

namespace TF\Engine\Plugins\Core\Farming;

use Exception;
use Prado\Prado;
use TF\Application\Common\MTUser;
use TF\Application\Entity\ObjectPermissions;
use TF\Application\Entity\UserFarmings;
use TF\Engine\Plugins\Core\Base\BaseController;

/**
 * FarmingController class file.
 *
 * <AUTHOR> Ltd. <<EMAIL>>
 *
 * @link http://www.devision.bg/
 *
 * @copyright Copyright &copy; 2008 Devision Ltd.
 */
// Prado::using('Plugins.Core.Base.BaseController');

/**
 * FarmingController class.
 *
 * This is the controller class for the Base plugin
 *
 * @property FarmingModel $DbHandler
 */
class FarmingController extends BaseController
{
    /**
     * @var FarmingModel
     */
    public $DbHandler;

    public function getHomeItems(&$settings = [])
    {
        $orderby = $this->getOrderBy($settings['place']);
        $ordertype = $this->getOrderType($settings['place']);

        $options = [
            'return' => $settings['return'],
            'user_id' => $settings['user_id'],
            'orderby' => '' != $settings['orderby'] ? $settings['orderby'] : $orderby,
            'ordertype' => '' != $settings['ordertype'] ? $settings['ordertype'] : $ordertype,
            'offset' => $settings['offset'],
            'limit' => $settings['limit'],
        ];

        if ($settings['keywords']) {
            $keywords = '%' . $settings['keywords'] . '%';
            $options['custom'] = [
                'fields' => ['value'],
                'values' => [$keywords],
            ];
        }

        $result = [];
        if (isset($settings['count'])) {
            $options['count'] = $settings['count'];
            $result[$settings['count'] ? 'count' : 'data'] = $this->DbHandler->getHomeItems($options);
        } else {
            $options['count'] = true;
            $result['count'] = $this->DbHandler->getHomeItems($options);
            $options['count'] = false;
            $result['data'] = $this->DbHandler->getHomeItems($options);
        }

        return $result;
    }

    public function addFarmingItem(&$settings)
    {
        $table = $this->DbHandler->tableName;
        $fields = array_keys($settings['mainData']);
        $values = array_values($settings['mainData']);
        $id = $this->DbHandler->addItem($table, $fields, $values);

        return $id;
    }

    /**
     * @param bool $counter
     * @param bool $returnOnlySQL
     *
     * @return array
     */
    public function getFarmings($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getFarmings($options, $counter, $returnOnlySQL);
    }

    public function restoreIsDefault($userid)
    {
        $setFields = ['is_default'];
        $setValues = ['0'];
        $whereFields = ['user_id'];
        $whereValues = [$userid];
        $id = $this->DbHandler->editItem($this->DbHandler->tableName, $setFields, $setValues, $whereFields, $whereValues);
    }

    /**
     * Deletes items from the db.
     *
     * @param array $arrayID
     */
    public function deleteFarmingItems($arrayID = [], $user_id, $database)
    {
        $transaction = $this->DbHandler->startTransaction();

        try {
            foreach ($arrayID as $id) {
            }

            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();

            throw $e;
        }
    }

    public function getDefaultFarming($userid)
    {
        return $this->DbHandler->getItem($this->DbHandler->tableName, ['id'], ['user_id', 'is_default'], [$userid, '1']);
    }

    // checks if the farming item is not the default system item
    public function checkIfSystemItem($itemID)
    {
        return $this->DbHandler->checkIfSystemItem($itemID);
    }

    public function getFarmingItemsByIDString($id_string, $user_id)
    {
        return $this->DbHandler->getFarmingItemsByIDString($id_string, $user_id);
    }

    public function getSystemFarmingDataByUserId($user_id)
    {
        return $this->DbHandler->getSystemFarmingDataByUserId($user_id);
    }

    public function getSystemFarmingDataByGroupId($group_id)
    {
        return $this->DbHandler->getSystemFarmingDataByGroupId($group_id);
    }

    public function getCurrentFarmingYearID()
    {
        return $this->DbHandler->getCurrentFarmingYearID();
    }

    public function getFarmingYearByTableName($table)
    {
        return $this->DbHandler->getFarmingYearByTableName($table);
    }

    public function getFarmingYearIdByDate($date, $returnIDOnly = false)
    {
        return $this->DbHandler->getFarmingYearIdByDate($date, $returnIDOnly);
    }

    public function updateFarmingStartRko($rko_number, $farming_id)
    {
        return $this->DbHandler->updateFarmingStartRko($rko_number, $farming_id);
    }

    public function updateFarmingMol($updateParms)
    {
        return $this->DbHandler->updateFarmingMol($updateParms);
    }

    /**
     * @param string $template
     * @param array $farmData {
     *                        #item address
     *                        #item bulstat
     *                        #item company_address
     *                        #item mol
     *                        #item mol_egn
     *                        #item iban_arr
     *                        }
     *
     * @return string
     */
    public function farmDetailedTemplate($template, $farmData)
    {
        $processedFarmDetailTemplate = '';
        $farmMatches = [];
        $farmMiniTemplates = [
            'address' => 'с адрес: address',
            'bulstat' => 'ЕИК: bulstat',
            'company_address' => 'с адрес: company_address',
            'mol' => 'МОЛ: mol',
            'mol_egn' => 'с ЕГН: mol_egn',
        ];
        if (!preg_match_all('/\[\[stopanstvo (?P<cols>[^\]]+)\]\]/m', $template, $farmMatches, PREG_SET_ORDER, 0)) {
            return $template;
        }

        foreach ($farmMatches as $match) {
            $farmDetCols = explode(' ', $match['cols']);
            $farmResult = implode(', ', array_filter(array_map(function ($col) use ($farmData, $farmMiniTemplates) {
                if ('iban_arr' == $col) {
                    $banksInfo = '';
                    $banks = json_decode($farmData['iban_arr'], true);
                    if (!empty($banks)) {
                        foreach ($banks as $key => $bank) {
                            if (empty($bank['iban'])) {
                                continue;
                            }
                            $banksInfo .= $bank['iban'] . (empty($bank['name']) ? '' : ' при банка ' . $bank['name']) . ', ';
                        }
                        $banksInfo = rtrim($banksInfo, ', ');
                    }

                    return $banksInfo;
                }

                if (array_key_exists($col, $farmMiniTemplates)) {
                    return str_replace($col, $farmData[$col], $farmMiniTemplates[$col]);
                }

                return $farmData[$col];
            }, $farmDetCols)));

            $template = str_replace($match[0], $farmResult, $template);
        }

        return $template;
    }

    /**
     * @param bool $detailed
     *                       When true returns an associative array where the key is the farming id and the value is the full farming object containing all columns.
     *                       Otherwise returns an array where the key is the farming id and the value is the farming name.
     *
     * @return array
     */
    public function getUserFarmings(bool $detailed = false)
    {
        /**
         * @var MTUser $user
         */
        $user = Prado::getApplication()->getModule('auth')->getUser();

        try {
            $userFarmingIds = $user->getPermissionObjectIds(ObjectPermissions::PERMISSION_READ, UserFarmings::class);
            $where = [
                'id' => ['column' => 'id', 'compare' => 'IN', 'value' => $userFarmingIds],
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $user->GroupID],
            ];
        } catch (Exception $e) {
            $where = ['group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $user->GroupID]];
        }

        $options = [
            'return' => $detailed ? ['*'] : ['id', 'name'],
            'where' => $where,
            'sort' => 'name',
            'order' => 'ASC',
        ];
        $farmings = [];
        $farming_results = $this->getFarmings($options);
        if (empty($farming_results)) {
            return [];
        }
        foreach ($farming_results as $value) {
            $farmings[$value['id']] = $detailed ? $value : $value['name'];
        }

        return $farmings;
    }

    public static function getYearKeyByYear(string $year)
    {
        $years = $GLOBALS['Farming']['years'];

        // Filter the array to find the matching year
        $filteredYears = array_filter($years, function ($item) use ($year) {
            return $item['year'] == $year;
        });

        return !empty($filteredYears) ? current($filteredYears)['id'] : null;
    }
}
