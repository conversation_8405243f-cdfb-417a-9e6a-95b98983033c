<?php

namespace TF\Engine\Plugins\Core\Farming;

use DateTime;
use PDO;
use TF\Engine\Plugins\Core\Base\BaseModel;

/**
 * Base model class file
 * Every plugin extends this one.
 *
 * <AUTHOR> Ltd. <<EMAIL>>
 *
 * @link http://www.devision.bg/
 *
 * @copyright Copyright &copy; 2008 Devision Ltd.
 */
// Prado::using('Plugins.Core.Base.BaseModel');

/**
 * BaseModel class.
 *
 * Implements functionality to work with the database
 */
class FarmingModel extends BaseModel
{
    public $tableUsers;
    public $tableFarming;
    public $tableLayers;

    /**
     * Constructor of the class.
     *
     * @param string $tableName - The name of the main table
     * @param string $fieldName - The relation field name
     */
    public function __construct($tableName = null, $fieldName = null)
    {
        parent::__construct($tableName, $fieldName);

        $this->tableUsers = DEFAULT_DB_PREFIX . 'users';
        $this->tableFarming = DEFAULT_DB_PREFIX . 'users_farming';
        $this->tableLayers = DEFAULT_DB_PREFIX . 'users_layers';
    }

    public function getHomeItems(&$options = [])
    {
        $count = $options['count'];
        $return = $count ? 'COUNT(*)' : implode(', ', $options['return']);
        $customFields = $options['custom']['fields'];
        $customValues = $options['custom']['values'];
        $orderBy = $options['orderby'];
        $orderType = $options['ordertype'];
        $limit = $options['limit'];
        $offset = $options['offset'];

        $userid = $options['user_id'];

        $table = $this->tableName;

        $sql = "SELECT {$return} FROM {$table} t";
        $sql .= ' WHERE TRUE';

        if ($userid) {
            $sql .= ' AND t.user_id = :userid';
        }

        $customQuery = $this->generateSearchQuery('sql', $customFields);
        if (!empty($customQuery)) {
            $sql .= " AND {$customQuery} ";
        }

        if (!$count and !empty($orderType) and !empty($orderBy)) {
            if (empty($orderType)) {
                $orderType = 'ASC';
            }
            $sql .= " ORDER BY {$orderBy} {$orderType} ";
        }

        if (!$count and isset($limit) and isset($offset)) {
            $sql .= " LIMIT {$limit}";
            $sql .= " OFFSET {$offset}";
        }

        $cmd = $this->DbModule->createCommand($sql);

        // $cmd = $this->generateSearchQuery('bind', $customValues, $cmd);

        if ($userid) {
            $cmd->bindParameter(':userid', $userid);
        }

        if ($count) {
            return $cmd->queryScalar();
        }

        return $cmd->query()->readAll();
    }

    public function getFarmings($options, $counter, $returnOnlySQL)
    {
        $tablename = $this->tableFarming;
        if ($counter) {
            $return = 'COUNT(*)';
        } elseif ($options['return']) {
            $return = implode(', ', $options['return']);
        } else {
            $return = '*';
        }

        $sql = "SELECT {$return} FROM {$tablename} WHERE TRUE";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if (!empty($options['order']) && !empty($options['sort']) && !$counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function checkIfSystemItem($itemID)
    {
        $return = 'is_system';
        $tablename = $this->tableName;
        $sql = "SELECT {$return} FROM {$tablename} WHERE true";

        if ($itemID) {
            $sql .= ' AND id = :id';
        }
        $cmd = $this->DbModule->createCommand($sql);
        if ($itemID) {
            $cmd->bindParameter(':id', $itemID);
        }
        $result = $cmd->query()->read();

        return $result[$return];
    }

    public function getFarmingItemsByIDString($id_string, $user_id)
    {
        $sql = 'SELECT * FROM ' . $this->tableFarming . " WHERE id IN ({$id_string}) AND group_id = :user_id";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':user_id', $user_id);

        return $cmd->query()->readAll();
    }

    public function getSystemFarmingDataByUserId($user_id)
    {
        $sql = 'SELECT * FROM ' . $this->tableFarming . ' WHERE user_id = :user_id AND is_system=TRUE';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':user_id', $user_id);

        return $cmd->query()->readAll();
    }

    public function getSystemFarmingDataByGroupId($group_id)
    {
        $sql = 'SELECT * FROM ' . $this->tableFarming . ' WHERE group_id = :group_id AND is_system=TRUE';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':group_id', $group_id);

        return $cmd->query()->readAll();
    }

    public function getCurrentFarmingYearID()
    {
        $year_id = '';
        $today = new DateTime('now');
        $today_year = (int)$today->format('Y');
        $switch_date = DateTime::createFromFormat('Y-m-d', $today_year . '-06-15');
        $business_year = $today_year + 1;
        $year_offset = ($today >= $switch_date) ? 0 : -1;
        $business_year += $year_offset;
        foreach ($GLOBALS['Farming']['years'] as $year) {
            if ($business_year == $year['year']) {
                $year_id = $year['id'];

                break;
            }
        }

        return $year_id;
    }

    public function getFarmingYearByTableName($table)
    {
        $sql = 'SELECT year FROM ' . $this->tableLayers . ' WHERE table_name = :table';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':table', $table);

        return $cmd->query()->readAll();
    }

    public function getFarmingYearIdByDate($date, $returnIDOnly)
    {
        $today = strtotime($date);
        foreach ($GLOBALS['Farming']['years'] as $year => $year_properties) {
            $start_date = strtotime($year_properties['start_date']);
            $end_date = strtotime($year_properties['end_date']);
            if ($today >= $start_date && $today < $end_date) {
                return $returnIDOnly ? $year_properties['id'] : $year_properties['year'];
            }
        }
    }

    public function updateFarmingStartRko($rko_number, $farming_id)
    {
        $sql = 'UPDATE ' . $this->tableFarming . ' SET rko_number = :rko_number 
        WHERE id = :farming_id';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':rko_number', $rko_number);
        $cmd->bindParameter(':farming_id', $farming_id);

        return $cmd->execute();
    }

    public function updateFarmingMol($updateMolParams)
    {
        if (!$updateMolParams['group_id']) {
            return;
        }

        $sql = "UPDATE {$this->tableFarming} SET mol = :mol, mol_egn = :mol_egn
                WHERE group_id = :group_id
                AND representative_id = :representative_id";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':group_id', $updateMolParams['group_id']);
        $cmd->bindParameter(':representative_id', $updateMolParams['representative_id']);
        $cmd->bindParameter(':mol', $updateMolParams['mol_name']);
        $cmd->bindParameter(':mol_egn', $updateMolParams['mol_egn']);
        $cmd->execute();
    }
}
