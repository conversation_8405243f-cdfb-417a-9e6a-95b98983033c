<?php

namespace TF\Engine\Plugins\Core\UserDbZPlots;

use TF\Application\Common\Config;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

class UserDbZPlotsController extends UserDbController
{
    public $DbHandler;
    public $Database;

    public function __construct($database)
    {
        $this->DbHandler = new UserDbZPlotsModel($database);
        $this->Database = $database;
    }

    public function getKVSReportZP($options, $tableName, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getKVSReportZP($options, $tableName, $counter, $returnOnlySQL);
    }

    public function ZPMultiEdit($options)
    {
        $this->DbHandler->ZPMultiEdit($options);
    }

    public function getZPlotAreaReport($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getZPlotAreaReport($options, $counter, $returnOnlySQL);
    }

    public function getContractsZPlotData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getContractsZPlotData($options, $counter, $returnOnlySQL);
    }

    public function getZPlotDataByLayerParams($options)
    {
        $layersController = new LayersController('Layers');
        if (!($options['farming'] || $options['year'])) {
            return;
        }

        $zplotLayerInfo = $layersController->getLayersIdByParams([
            'return' => ['table_name'],
            'farming' => $options['farming'],
            'year' => $options['year'],
            'user_id' => $options['user_id'],
            'layer_type' => Config::LAYER_TYPE_ZP,
        ]);
        $tableName = $zplotLayerInfo['table_name'];
        $data = $this->getItemsByParams([
            'tablename' => $tableName,
            'return' => $options['return'],
            'where' => $options['where'],
        ]);
        if (!count($data)) {
            return;
        }

        return $data[0];
    }
}
