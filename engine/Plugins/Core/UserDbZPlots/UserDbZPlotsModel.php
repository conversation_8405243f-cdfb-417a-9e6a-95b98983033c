<?php

namespace TF\Engine\Plugins\Core\UserDbZPlots;

use PDO;
use TF\Engine\Plugins\Core\UserDb\UserDbModel;

class UserDbZPlotsModel extends UserDbModel
{
    public function getKVSReportZP($options, $tableNameZP, $count, $returnOnlySQL)
    {
        $tablename = $this->tableKVS;

        if ($count) {
            if ($options['custom_counter']) {
                $return = $options['custom_counter'];
            } else {
                $return = " COUNT(DISTINCT a.gid ||'-'|| b.id)";
            }
        } else {
            $return = implode(', ', $options['return']);
        }
        $sql = "SELECT {$return} FROM {$tablename} a
					INNER JOIN {$tableNameZP} b ON ST_Intersects(ST_SetSRID(a.geom,32635), ST_SetSRID(b.geom,32635))
						WHERE true AND ST_Area(ST_Intersection(ST_SetSRID(a.geom,32635),ST_SetSRID(b.geom,32635))) > 10
						AND a.gid in (select layer_kvs.gid from layer_kvs inner join {$tableNameZP} on st_intersects(layer_kvs.geom, {$tableNameZP}.geom)) ";

        if ($options['kvs_id_string'] && '' != $options['kvs_id_string']) {
            $sql .= " AND a.gid IN ({$options['kvs_id_string']})";
        }

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group']) {
            if (is_array($options['group'])) {
                $group = implode(', ', $options['group']);
            } else {
                $group = $options['group'];
            }

            $sql .= " GROUP BY {$group}";
        }

        if ($options && !$count) {
            if ($options['sort']) {
                $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
            }

            $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
            $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

            if (false !== $limit && false !== $offset) {
                $sql .= $returnOnlySQL
                    ? " LIMIT {$limit} OFFSET {$offset}"
                    : ' LIMIT :limit OFFSET :offset';
            }
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$count) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function ZPMultiEdit($options)
    {
        $updateFields = array_keys($options['update']);
        $updateValues = array_values($options['update']);

        $sql = 'UPDATE ' . $options['tablename'] . ' SET ';

        for ($i = 0; $i < count($updateFields); $i++) {
            $sql .= ' ' . $updateFields[$i] . ' = :' . $updateFields[$i];
            if ($i < count($updateFields) - 1) {
                $sql .= ', ';
            }
        }

        if ($options['id_string']) {
            $sql .= ' WHERE id IN (' . $options['id_string'] . ')';
        }

        $cmd = $this->DbModule->createCommand($sql);

        for ($i = 0; $i < count($updateFields); $i++) {
            $cmd->bindParameter(':' . $updateFields[$i], $updateValues[$i]);
        }

        $cmd->execute();
    }

    public function getZPlotAreaReport($options, $counter, $returnOnlySQL)
    {
        if ($counter) {
            $return = 'COUNT(DISTINCT(' . $options['group'] . '))';
        } elseif (!$options['return']) {
            $return = '*, ST_ASTEXT(geom)';
        } else {
            $return = implode(', ', $options['return']);
        }

        $sql = "SELECT {$return} FROM " . $options['tablename'] . ' p WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }
        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getContractsZPlotData($options, $counter, $returnOnlySQL)
    {
        if ($counter) {
            $return = 'COUNT(*)';
        } else {
            $return = implode(', ', $options['return']);
        }
        $contractsTbl = $this->tableContracts;
        $contractsPlotRel = $this->contractsPlotsRelTable;
        $kvsTbl = $this->tableKVS;
        $sql = "SELECT {$return} 
		FROM {$contractsTbl} c 
		INNER JOIN {$contractsPlotRel} cpr ON (cpr.contract_id = c.id)
		INNER JOIN {$kvsTbl} a ON (cpr.plot_id = a.gid)
		WHERE True ";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }
        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }
}
