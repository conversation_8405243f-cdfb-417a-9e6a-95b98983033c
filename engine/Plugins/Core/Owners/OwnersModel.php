<?php

namespace TF\Engine\Plugins\Core\Owners;

Prado::using('Plugins.Core.Base.BaseModel');

class OwnersModel extends BaseModel
{
    public function __construct($tableName = null, $fieldName = null)
    {
        parent::__construct($tableName, $fieldName);

        $this->tableName = $tableName;
        $this->fieldName = $fieldName;
    }

    // 	public function getOwnersData($params, $counter) {
    // 		if ($counter == true) {
    // 			$return = 'COUNT(*)';
    // 		} else {
    // 			$return = '*';
    // 		}
    // 		$sql = "SELECT $return FROM " . $this->tableName . " WHERE true";
    // 		if ($params['user_id']) {
    // 			$sql .= " AND user_id = :user_id";
    // 		}
    // 		if ($params['id']) {
    // 			$sql .= " AND id = :id";
    // 		}
    // 		if ($params['name']) {
    // 			$sql .= " AND name ~ :name";
    // 		}
    // 		if ($params['eik']) {
    // 			$sql .= " AND eik = :eik";
    // 		}
    // 		if ($params['egn']) {
    // 			$sql .= " AND egn = :egn";
    // 		}
    // 		if ($params['company_name']) {
    // 			$sql .= " AND company_name ~ :company_name";
    // 		}
    // 		if ($params['id_string']) {
    // 			$id_string = $params['id_string'];
    // 			$sql .= " AND id IN ($id_string)";
    // 		}
    // 		if ($params['anti_id_string']) {
    // 			$id_string = $params['anti_id_string'];
    // 			$sql .= " AND id NOT IN ($id_string)";
    // 		}

    // 		if ($params['order'] && $params['sort'] && $counter == false) {
    // 			$sql .= " ORDER BY " . $params['sort'] . " " . $params['order'];
    // 		}
    // 		if ($params['limit'] && $counter == false) {
    // 			$sql .= " LIMIT " . $params['limit'];
    // 			$sql .= " OFFSET " . $params['offset'];
    // 		}

    // 		$cmd = $this->DbModule->createCommand($sql);
    // 		if ($params['user_id']) {
    // 			$cmd->bindParameter(':user_id', $params['user_id']);
    // 		}
    // 		if ($params['id']) {
    // 			$cmd->bindParameter(':id', $params['id']);
    // 		}
    // 		if ($params['name']) {
    // 			$cmd->bindParameter(':name', $params['name']);
    // 		}
    // 		if ($params['eik']) {
    // 			$cmd->bindParameter(':eik', $params['eik']);
    // 		}
    // 		if ($params['egn']) {
    // 			$cmd->bindParameter(':egn', $params['egn']);
    // 		}
    // 		if ($params['company_name']) {
    // 			$cmd->bindParameter(':company_name', $params['company_name']);
    // 		}
    // 		$data = $cmd->query()->readAll();
    // 		return $data;
    // 	}

    // 	public function deleteOwner($id_string) {
    // 		if(strlen($id_string) != 0) {
    // 			$sql .= "DELETE FROM " . $this->tableName . " WHERE id IN ($id_string)";
    // 			$cmd = $this->DbModule->createCommand($sql);
    // 			$cmd->execute();
    // 		}
    // 	}
}
