<?php

namespace TF\Engine\Plugins\Core\UserDbDiary;

use DateTime;
use Prado\Prado;
use TF\Engine\Kernel\ExportData\SmartConvertExport\AbLinesExport;
use TF\Engine\Kernel\StringHelper;
use TF\Engine\Kernel\WarehouseModuleClass;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\Layers\LayersModel;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

// Prado::using('Plugins.Core.UserDb.UserDbController');

/**
 * Class UserDbDiaryController.
 *
 * @property string $String
 */
class UserDbDiaryController extends UserDbController
{
    /**
     * @var UserDbDiaryModel
     */
    public $DbHandler;

    public $Database;
    /**
     * @var string $String
     */
    public $String;
    private $curl;

    public function __construct($database)
    {
        $this->DbHandler = new UserDbDiaryModel($database);
        $this->Database = $database;
        $this->StringHelper = new StringHelper();
        $this->curl = curl_init();
    }

    public function getFullDiaryConfigData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getFullDiaryConfigData($options, $counter, $returnOnlySQL);
    }

    public function getEventZPlotsInfo($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getEventZPlotsInfo($options, $counter, $returnOnlySQL);
    }

    /**
     * @param string $path path for request
     * @param array $params post params
     * @param string $session users session id
     * @param bool $empty_params if params is required with empty value
     */
    public function executeWialonRequest($path, $params = [], $session = '', $empty_params = false)
    {
        $urlRequest = WIALON_PATH . "ajax.html?svc={$path}";

        if ('' != $session) {
            $urlRequest .= "&sid={$session}";
        }

        if ($empty_params) {
            $urlRequest .= '&params={}';
        }

        curl_setopt($this->curl, CURLOPT_URL, $urlRequest);
        curl_setopt($this->curl, CURLOPT_FAILONERROR, true);
        curl_setopt($this->curl, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($this->curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($this->curl, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($this->curl, CURLOPT_SSL_VERIFYPEER, false);

        if (count($params)) {
            $post_params = '&params=' . $this->StringHelper->jsonRemoveUnicodeSequences($params);
            curl_setopt($this->curl, CURLOPT_POSTFIELDS, $post_params);
        }

        return json_decode(curl_exec($this->curl));
    }

    public function getDiaryReport($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getDiaryReport($options, $counter, $returnOnlySQL);
    }

    public function getDiaryReportByPerformer($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getDiaryReportByPerformer($options, $counter, $returnOnlySQL);
    }

    public function getExpensesData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getExpensesData($options, $counter, $returnOnlySQL);
    }

    public function getDetaildDetaildEventsReport($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getDetaildDetaildEventsReport($options, $counter, $returnOnlySQL);
    }

    public function getEventsWithProductsAndProduces($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getEventsWithProductsAndProduces($options, $counter, $returnOnlySQL);
    }

    /**
     * @param false $counter
     * @param false $returnOnlySQL
     *
     * @return array|mixed|string
     */
    public function getDiaryExpensesForPeriod($options, $counter = false, $returnOnlySQL = false)
    {
        $options = [
            'where' => [
                'performer' => ['column' => 'performer_id', 'compare' => '=', 'prefix' => 'de', 'value' => $options['performer_id']],
                'activity' => ['column' => 'activity_id', 'compare' => '=', 'prefix' => 'de', 'value' => $options['activity_id']],
                'date_from' => ['column' => 'valid_from::date', 'compare' => '>=', 'prefix' => 'de', 'value' => $options['start_date']],
                'date_to' => ['column' => 'valid_to::date', 'compare' => '<=', 'prefix' => 'de', 'value' => $options['end_date']],
            ],
        ];

        return $this->DbHandler->getExpensesData($options, $counter, $returnOnlySQL);
    }

    /**
     * @throws TDbException
     */
    public function saveAbLine($options)
    {
        $data = [];

        $layersModel = new LayersModel($this->Database);
        $layerData = $layersModel->getLayerData($options['zp_layer_id']);

        // prepare data
        $data[] = [
            'plot_id' => $options['gid'],
            'name' => $options['name'],
            'marge' => $options['offset'],
            'direction' => $options['direction'],
            'layer_table' => $layerData['table_name'],
            'geom' => $options['geom'],
        ];

        $this->DbHandler->addLayers('su_ab_lines', $data);
    }

    /**
     * @param $options
     *
     * @throws TDbException
     */
    public function delAbLine($ids)
    {
        $options = [
            'tablename' => $this->DbHandler->tableAbLines,
            'id_name' => 'gid',
            'id_string' => implode(',', $ids),
        ];

        $this->DbHandler->deleteItemsByParams($options);
    }

    public function editAbLine($data)
    {
        $options = [
            'tablename' => $this->DbHandler->tableAbLines,
            'mainData' => [
                'name' => $data['name'],
            ],
            'where' => ['gid' => $data['gid']],
        ];
        $this->DbHandler->editItem($options);
    }

    public function exportAbLines($options)
    {
        $exportABlinesModule = new AbLinesExport();
        $exportABlinesModule->setUser(Prado::getApplication()->getUser());

        $exportABlinesModule->setExportPath(PUBLIC_UPLOAD_EXPORT . '/' . Prado::getApplication()->getUser()->userID . '/');
        $exportABlinesModule->setData($options);

        return $exportABlinesModule->exportSeparateFiles();
    }

    public function generateABLinesMapFile($userId, $database)
    {
        $abline = [
            'layername' => 'ab_lines',
            'groupname' => 'ab_lines',
            'type' => 'LINE',
            'validation' => [
                'layer_name' => '[a-z_]+',
            ],
            'host' => DEFAULT_DB_HOST,
            'dbname' => $database,
            'username' => DEFAULT_DB_USERNAME,
            'password' => DEFAULT_DB_PASSWORD,
            'port' => DEFAULT_DB_PORT,
            'query' => "(SELECT geom, gid FROM su_ab_lines WHERE layer_table='%layer_name%') as subquery USING UNIQUE gid",
            'classes' => [[
                'name' => 'ab_lines',
                'width' => '1',
                'border_color' => '255 0 0',
            ]],
        ];
        $ablineLabels = [
            'layername' => 'ab_lines_labels',
            'groupname' => 'ab_lines',
            'type' => 'POINT',
            'validation' => [
                'layer_name' => '[a-z_]+',
            ],
            'host' => DEFAULT_DB_HOST,
            'dbname' => $database,
            'username' => DEFAULT_DB_USERNAME,
            'password' => DEFAULT_DB_PASSWORD,
            'port' => DEFAULT_DB_PORT,
            'tag_label' => 'label',
            'classes' => [[
                'name' => 'ab_lines_labels',
                'border_color' => '255 0 0',
                'size' => '10',
                'label_color' => '0 0 255',
                'tags' => true,
                // 'label_style' => [

                // ],
                'symbol' => [
                    'name' => 'circle',
                    'size' => '5',
                    'color' => '255 0 0',
                ],
            ]],
            'query' => "(SELECT row_number() over (ORDER BY gid) id, geom, label from (
                SELECT
                    gid,
                    st_astext(st_startpoint((st_dump(geom)).geom)) geom,
                    'A' as label
                FROM
                    su_ab_lines
                WHERE layer_table='%layer_name%'
                UNION
                SELECT
                    gid,
                    st_astext(st_endpoint((st_dump(geom)).geom)) geom,
                    'B' as label
                FROM
                    su_ab_lines
                WHERE layer_table='%layer_name%'
                ) a) as subquery USING UNIQUE id USING srid=32635",
        ];

        $out = WMS_MAP_PATH . $userId . '_ab_lines.map';

        $template = $this->loadMapLayerTemplate($abline);

        $template .= $this->loadMapLayerTemplate($ablineLabels);

        $file = fopen($out, 'w');
        fwrite($file, $template);
        fclose($file);
    }

    /**
     * @param int $productWarehouseId
     * @param string $date
     * @param int $farmId
     *
     * @return array
     */
    public function getWarehouseProductQuantity($productWarehouseId, $date, $farmId)
    {
        $params = [
            'criteries' => [
                'document_tr_types' => ['SUB_PLOT', 'RETURN'], // TODO:: should be a constant
                'companies' => $farmId,
                'items' => $productWarehouseId,
                'before_doc_date' => $date,
            ],
        ];

        $warehouseModule = new WarehouseModuleClass();
        $items = $warehouseModule->getTransactionItems($params);

        return [
            'haveItems' => count($items['result']['items']) > 0,
            'quantity' => isset($items['result']['items'][0]) ? abs($items['result']['items'][0]['total_quantity']) : 0,
            'singlePrice' => isset($items['result']['items'][0]) ? abs($items['result']['items'][0]['single_price_no_dds']) : 0,
        ];
    }

    /**
     * @param int $productId
     * @param string $date
     * @param int $farmId
     *
     * @throws Exception
     *
     * @return int
     */
    public function getEventQuantities($productId, $date, $farmId)
    {
        $eventsUsedQuantity = 0;

        $options = [
            'tablename' => $this->DbHandler->tableDiaryProducts . ' products',
            'return' => [
                'round(sum(products.substance_consumed)::numeric, 3) as quantity', 'products.substance_id', 'products.event_id', 'events.complete_date_from', 'events.farming_id',
            ],
            'leftjoin' => [
                'table' => $this->DbHandler->tableDiaryEvents . ' events',
                'condition' => ' ON (events.id = products.event_id)',
            ],
            'where' => [
                'substance_id' => ['column' => 'substance_id', 'prefix' => 'products', 'compare' => '=', 'value' => $productId],
                'complete_date_from' => ['column' => 'complete_date_from', 'prefix' => 'events', 'compare' => '<=', 'value' => (new DateTime($date))->format('Y-m-d 23:59:59')],
                'farming_id' => ['column' => 'farming_id', 'prefix' => 'events', 'compare' => '=', 'value' => $farmId],
            ],
            'group' => 'products.substance_id, products.event_id, events.complete_date_from, events.farming_id',
        ];

        $events = $this->getItemsByParams($options, false, false);

        foreach ($events as $event) {
            $eventsUsedQuantity += $event['quantity'];
        }

        return $eventsUsedQuantity;
    }

    /**
     * @param int $eventId
     * @param int $productId
     *
     * @return array
     */
    public function getEventData($eventId, $productId)
    {
        $options = [
            'tablename' => $this->DbHandler->tableDiaryEvents . ' events',
            'return' => [
                'round(sum(products.substance_consumed)::numeric, 3) as quantity', 'products.substance_id', 'products.event_id', 'events.farming_id',
            ],
            'leftjoin' => [
                'table' => $this->DbHandler->tableDiaryProducts . ' products',
                'condition' => ' ON (events.id = products.event_id)',
            ],
            'where' => [
                'id' => ['column' => 'id', 'prefix' => 'events', 'compare' => '=', 'value' => $eventId],
                'substance_id' => ['column' => 'substance_id', 'prefix' => 'products', 'compare' => '=', 'value' => $productId],
            ],
            'group' => 'products.substance_id, products.event_id, events.complete_date_from, events.farming_id',
        ];

        $events = $this->getItemsByParams($options, false, false);

        return empty($events) ? [] : $events[0];
    }

    /**
     * @param string $date
     * @param int $farmId
     *
     * @throws Exception
     *
     * @return array
     */
    public function getUsedProductsQuantityByWarehouseId(array $warehouseProductsIds, $date, $farmId)
    {
        $options = [
            'tablename' => $this->DbHandler->tableDiaryConfigs . ' configs',
            'return' => [
                'round(sum(products.substance_consumed)::numeric, 3) as quantity',
                'products.substance_id',
                'products.substance_id',
                'products.event_id',
                'events.complete_date_from',
                'events.farming_id',
                'configs.warehouse_item_id',
            ],
            'innerjoin' => [
                'table' => $this->DbHandler->tableDiaryProducts . ' products',
                'condition' => ' ON (configs.id = products.substance_id)',
            ],
            'leftjoin' => [
                'table' => $this->DbHandler->tableDiaryEvents . ' events',
                'condition' => ' ON (events.id = products.event_id)',
            ],
            'where' => [
                'warehouse_item_id' => [
                    'column' => 'warehouse_item_id',
                    'prefix' => 'configs',
                    'compare' => 'IN',
                    'value' => array_map('strval', $warehouseProductsIds),
                ],
                'complete_date_from' => ['column' => 'complete_date_from',
                    'prefix' => 'events',
                    'compare' => '<=',
                    'value' => (new DateTime($date))->format('Y-m-d'),
                ],
                'farming_id' => ['column' => 'farming_id',
                    'prefix' => 'events',
                    'compare' => '=',
                    'value' => $farmId,
                ],
            ],
            'group' => 'products.substance_id, products.event_id, events.complete_date_from, events.farming_id, configs.warehouse_item_id',
        ];

        return $this->getItemsByParams($options, false, false);
    }

    public function getPlotData(array $rpcParams, array $additionalOptions = [], int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $layersController = new LayersController('Layers');
        $arrayHelper = $layersController->ArrayHelper;

        switch ($sort) {
            case 'id':
                $sort = 'de.id';

                break;
            case 'moto_hours':
                $sort = 'de.total_time_in';

                break;
            case 'year':
                $sort = 'de.year_id';

                break;
            case 'farming':
                $sort = 'de.farming_id';

                break;
            case 'zp_area':
                $sort = 'de.treated_area';

                break;
            default:
                $sort = 'de.id';

                break;
        }
        if ($rpcParams['zp_ekate'] || '' != $rpcParams['zp_name'] || $rpcParams['zp_crops'] || '' != $rpcParams['isak_prc_uin']) {
            $layers_tablenames = $layersController->getLayersTablenames();
            $existing_layer_tables = $layersController->getExistingLayersTables($layers_tablenames);

            $zp_final = $this->filterByZpData($existing_layer_tables, $rpcParams);
        }

        $farmingController = new FarmingController('Farming');
        $farmings = $farmingController->getUserFarmings() ?? [];

        $userFarmingIds = array_keys($farmings);
        $farmingIds = $arrayHelper->filterEmptyStringArr($rpcParams['farming']);
        $farmingIds = isset($farmingIds) && count($farmingIds) > 0
            ? array_intersect($farmingIds, $userFarmingIds)
            : $userFarmingIds;

        $options = [
            'sort' => $sort,
            'order' => $order,
            'return' => [
                'de.plot_id', "(de.farming_id || '-' || de.year_id || '#' || de.plot_id) AS zp_ident",
                '(CASE WHEN de.phase_id = 1 THEN de.plan_date_to ELSE de.complete_date_to END) as complete_date',
                'dc2.name', 'de.attachment_id', 'de.farming_id', 'de.year_id', 'de.type_id',
                'dc3.name as type',
                'dx.price',
                'de.price_per_area',
                'round(de.treated_area::numeric,3) as treated_area',
                'de.total_time_in moto_hours',
                'sum(total_time_in) over ()  total_moto_hours',
                "CONCAT_WS (' ', dc3.name, '-', dc4.name) full_event_name",
            ],
            'where' => [
                'performer' => ['column' => 'id', 'compare' => 'IN', 'prefix' => 'dc2', 'value' => $arrayHelper->filterEmptyStringArr($rpcParams['performer'])],
                'farming' => ['column' => 'farming_id', 'compare' => 'IN', 'prefix' => 'de', 'value' => $farmingIds],
                'date_from' => ['column' => 'complete_date_from::date', 'compare' => '>=', 'prefix' => 'de', 'value' => $rpcParams['date_from']],
                'date_to' => ['column' => 'complete_date_from::date', 'compare' => '<=', 'prefix' => 'de', 'value' => $rpcParams['date_to']],
                'farming_year' => ['column' => 'year_id', 'compare' => 'IN', 'prefix' => 'de', 'value' => $arrayHelper->filterEmptyStringArr($rpcParams['farming_year'])],
                'event_type' => ['column' => 'type_id', 'compare' => 'IN', 'prefix' => 'de', 'value' => $arrayHelper->filterEmptyStringArr($rpcParams['event_type'])],
                'event_kind' => ['column' => 'id', 'compare' => 'IN', 'prefix' => 'dc4', 'value' => $arrayHelper->filterEmptyStringArr($rpcParams['event_kind'])],
                'machine' => ['column' => 'machine_id', 'compare' => 'IN', 'prefix' => 'de', 'value' => $arrayHelper->filterEmptyStringArr($rpcParams['machine'])],
                'attachment' => ['column' => 'attachment_id', 'compare' => 'IN', 'prefix' => 'de', 'value' => $arrayHelper->filterEmptyStringArr($rpcParams['attachment'])],
                'zp_filter' => ['column' => "de.farming_id || '-' || de.year_id || '#' || de.plot_id", 'compare' => 'IN', 'value' => $zp_final],
                'product' => ['column' => 'id', 'compare' => '=', 'prefix' => 'dc8', 'value' => $rpcParams['product']],
            ],
            'group' => 'de.id, t.table_name, dc.id, dc2.id, dc3.id, dc4.id, dc5.id, dc6.id, dc7.id, dx.id',
        ];

        if (!empty($additionalOptions['return'])) {
            $options['return'] = array_merge($options['return'], $additionalOptions['return']);
        }

        if (!empty($additionalOptions['where'])) {
            $options['where'] = array_merge($options['where'], $additionalOptions['where']);
        }

        if (!empty($additionalOptions['group'])) {
            $options['group'] .= ', ' . ltrim(trim($additionalOptions['group']), ',');
        }
        $results = $this->getDetaildDetaildEventsReport($options, false, false);
        $resultsCount = count($results);

        if (0 == $resultsCount) {
            return [
                'rows' => [],
                'total' => 0,
                'footer' => [],
            ];
        }

        $reportRows = [];

        for ($i = 0; $i < $resultsCount; $i++) {
            $reportRows[$i]['complete_date'] = $results[$i]['complete_date'];
            $reportRows[$i]['completed_area'] = $results[$i]['completed_area'];
            $reportRows[$i]['total_completed_area'] = $results[$i]['total_completed_area'];
            $reportRows[$i]['moto_hours'] = $results[$i]['moto_hours'];
            $reportRows[$i]['total_moto_hours'] = $results[$i]['total_moto_hours'];
            $reportRows[$i]['performer'] = $results[$i]['name'];
            $reportRows[$i]['machine'] = $results[$i]['machine_type'] . ' ' . $results[$i]['machine'];
            $reportRows[$i]['attachment'] = $results[$i]['attachment_type'] . ' ' . $results[$i]['attachment'];
            $reportRows[$i]['farming_id'] = $results[$i]['farming_id'];
            $reportRows[$i]['farming'] = $farmings[$results[$i]['farming_id']];
            $reportRows[$i]['year'] = $GLOBALS['Farming']['years'][$results[$i]['year_id']]['title'];
            $reportRows[$i]['type'] = $results[$i]['full_event_name'];
            if ($results[$i]['fuel_quantity']) {
                $reportRows[$i]['fuel_quantity'] = $results[$i]['fuel_quantity'];
                $reportRows[$i]['total_fuel'] = $results[$i]['total_fuel'];
                $reportRows[$i]['single_price'] = BGNtoEURO($results[$i]['single_price']);
            }

            if ($results[$i]['product_name']) {
                $reportRows[$i]['product_name'] = $results[$i]['product_name'];
                $reportRows[$i]['product_dose'] = $results[$i]['product_dose'];
                $reportRows[$i]['sort'] = $results[$i]['sort'];
                $reportRows[$i]['product_measure'] = $results[$i]['product_measure'];
                $reportRows[$i]['product_consumed'] = $results[$i]['product_consumed'];
                $reportRows[$i]['single_price'] = BGNtoEURO($results[$i]['single_price']);
            }

            if ($results[$i]['produce']) {
                $reportRows[$i]['produce'] = $results[$i]['produce'];
                $reportRows[$i]['total_produce'] = $results[$i]['total_produce'];
            }

            if ($results[$i]['produce_per_dka']) {
                $reportRows[$i]['produce_per_dka'] = $results[$i]['produce_per_dka'];
            }

            if ($results[$i]['sort']) {
                $reportRows[$i]['sort'] = $results[$i]['sort'];
            }

            $reportRows[$i]['price'] = BGNtoEURO($results[$i]['event_price']);

            $reportRows[$i]['total_cost_all_plots'] = BGNtoEURO($results[$i]['total_cost_all_plots']);
            $reportRows[$i]['event_price'] = BGNtoEURO($results[$i]['event_price']);

            $zpData = $this->getZPData($results[$i]['zp_table'], $results[$i]['plot_id']);
            $reportRows[$i]['zp_name'] = $zpData['zp_name'];
            $reportRows[$i]['isak_prc_uin'] = $zpData['isak_prc_uin'];
            $reportRows[$i]['zp_area'] = $zpData['zp_area'];
            $reportRows[$i]['zp_culture'] = $zpData['zp_culture'];
        }

        $resultsForPage = $reportRows;
        if (null != $page && null != $rows) {
            $resultsForPage = array_slice($reportRows, ($page - 1) * $rows, $rows);
        }

        $totalZPAreaForPage = array_sum(array_column($resultsForPage, 'zp_area'));
        $totalZPArea = array_sum(array_column($reportRows, 'zp_area'));
        $totalCompletedAreaForPage = array_sum(array_column($resultsForPage, 'completed_area'));
        $totalCompletedArea = $reportRows[0]['total_completed_area'];
        $totalMotoHoursForPage = sumHours(array_column($resultsForPage, 'moto_hours'));
        $totalMotoHours = $reportRows[0]['total_moto_hours'];
        $totalEventPriceForPage = array_sum(array_column($resultsForPage, 'event_price'));
        $totalEventPrice = $reportRows[0]['total_cost_all_plots'];

        $totalProduceForPage = array_sum(array_column($resultsForPage, 'produce'));
        $totalProduce = $reportRows[0]['total_produce'];

        if ($results[0]['fuel_quantity']) {
            $totalFuelQuantityForPage = array_sum(array_column($resultsForPage, 'fuel_quantity'));
            $totalFuelQuantity = $reportRows[0]['total_fuel'];
        }

        $totalPerPage = [
            'attachment' => '<b>Общо за стр.</b>',
            'moto_hours' => $totalMotoHoursForPage,
            'completed_area' => number_format($totalCompletedAreaForPage, 3, '.', ''),
            'zp_area' => number_format($totalZPAreaForPage, 3, '.', ''),
        ];

        $total = [
            'attachment' => '<b>Общо</b>',
            'moto_hours' => $totalMotoHours,
            'completed_area' => number_format($totalCompletedArea, 3, '.', ''),
            'zp_area' => number_format($totalZPArea, 3, '.', ''),
        ];

        if (isset($totalFuelQuantity, $totalFuelQuantityForPage)) {
            $totalPerPage['fuel_quantity'] = number_format($totalFuelQuantityForPage, 3, '.', '');
            $total['fuel_quantity'] = number_format($totalFuelQuantity, 3, '.', '');
        }

        if (isset($totalProduceForPage, $totalProduce)) {
            $totalPerPage['produce'] = number_format($totalProduceForPage, 3, '.', '');
            $total['produce'] = number_format($totalProduce, 3, '.', '');
        }

        $return['rows'] = $resultsForPage;
        $return['total'] = count($results);
        $return['footer'] = [$totalPerPage, $total];

        $return['footer'][0]['event_price'] = BGNtoEURO($totalEventPriceForPage);
        $return['footer'][1]['event_price'] = BGNtoEURO($totalEventPrice);

        return $return;
    }

    public function filterByZpData($existing_layer_tables, $rpcParams)
    {
        $layersController = new LayersController('Layers');
        $layer_tables = array_values($existing_layer_tables);
        $layerTablesCount = count($layer_tables);

        $arrayHelper = $layersController->ArrayHelper;

        $zp_final = [];
        for ($i = 0; $i < $layerTablesCount; $i++) {
            $layer_zp_options = [
                'tablename' => $layer_tables[$i],
                'return' => ['id'],
                'where' => [
                    'zp_ekate' => ['column' => 'ekatte', 'compare' => 'IN', 'value' => $arrayHelper->filterEmptyStringArr($rpcParams['zp_ekate'])],
                    'zp_name' => ['column' => 'area_name', 'compare' => 'ILIKE', 'value' => $rpcParams['zp_name']],
                    'isak_prc_uin' => ['column' => 'isak_prc_uin', 'compare' => 'ILIKE', 'value' => $rpcParams['isak_prc_uin']],
                    'zp_crops' => ['column' => 'culture', 'compare' => 'IN', 'value' => $arrayHelper->filterEmptyStringArr($rpcParams['zp_crops'])],
                ],
            ];
            $zp_results = $this->getItemsByParams($layer_zp_options, false, false);
            $zpCount = count($zp_results);

            for ($j = 0; $j < $zpCount; $j++) {
                $zp_key = array_search($layer_tables[$i], $existing_layer_tables);
                $zp_final[] = $zp_key . '#' . $zp_results[$j]['id'];
            }
        }

        return $zp_final;
    }

    public function getZPData($zpTable, $gid)
    {
        $reportRow = [
            'zp_name' => '-',
            'zp_area' => '-',
            'zp_culture' => '-',
        ];
        if (!$zpTable) {
            return $reportRow;
        }
        $options = [
            'tablename' => $zpTable,
            'return' => ['area_name', 'isak_prc_uin', 'round((ST_Area(geom)/1000)::numeric, 3) AS zp_area', 'culture'],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $gid],
            ],
        ];
        $result = $this->getItemsByParams($options, false, false);

        if (!$result) {
            return $reportRow;
        }
        $plot = $result[0];
        $cropCode = $plot['culture'];
        $reportRow['zp_name'] = $plot['area_name'];
        $reportRow['isak_prc_uin'] = $plot['isak_prc_uin'];
        $reportRow['zp_area'] = $plot['zp_area'];
        $reportRow['zp_culture'] = $GLOBALS['Farming']['crops'][$cropCode]['crop_name'];

        return $reportRow;
    }
}
