<?php

namespace TF\Engine\Plugins\Core\UserDbDiary;

use PDO;
use Prado\Prado;
use TF\Engine\Plugins\Core\UserDb\UserDbModel;

class UserDbDiaryModel extends UserDbModel
{
    public function getFullDiaryConfigData($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM {$this->tableDiaryConfigs} dc1"
                . " INNER JOIN {$this->tableDiaryConfigs} dc2 ON(dc2.id = dc1.type_id)";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($returnOnlySQL) {
            return $sql;
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getEventZPlotsInfo($options, $counter, $returnOnlySQL)
    {
        $zp_tablename = $options['zp_tablename'];

        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM {$this->tableDiaryEvents} e "
                . " INNER JOIN {$zp_tablename} zp ON(zp.id = e.plot_id)"
                . ' WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($returnOnlySQL) {
            return $sql;
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getDiaryReport($options, $counter = false, $returnOnlySQL = false)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM {$options['zp_tablename']} zp"
            . " INNER JOIN {$this->tableDiaryEvents} e ON(zp.id = e.plot_id)"
            . " INNER JOIN {$this->tableDiaryProducts} prd ON(e.id = prd.event_id)"
            . " INNER JOIN {$this->tableDiaryConfigs} dc ON(dc.id = prd.substance_id)";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getDiaryReportByPerformer($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM {$this->tableDiaryConfigs} dc"
                . " LEFT JOIN {$this->tableDiaryEvents} e ON(e.performer_id = dc.id)"
                . ' WHERE TRUE';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getExpensesData($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);
        $table = $this->tableDiaryExpenses;
        $sql = "SELECT
                    {$return}
                FROM
                    {$table} de
                LEFT JOIN su_diary_configs dca ON de.activity_id = dca.id
                LEFT JOIN su_diary_configs dcp ON de.performer_id = dcp.id
                ";
        if ($options['join_activity_type']) {
            $sql .= ' LEFT JOIN su_diary_configs dt ON dca.type_id = dt.id ';
        }

        $sql .= ' WHERE true ';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getDetaildDetaildEventsReport($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);
        $table = $this->tableDiaryExpenses;
        $userGroupId = Prado::getApplication()->getUser()->groupID;

        $sql = "SELECT
                    t. TABLE_NAME zp_table,
                    dc3.name AS TYPE, -- event type
                    dc4.name AS subtype, -- event subtype
                    dc2.name AS performer,
                    dc6.name as machine_type,
                    dc5.number AS machine,
                    dc.name,
                    dc7.name as attachment_type,
                    dc.number AS attachment,
                    dx.price, -- performer price for a given period.
                    de.farming_id,
                    de.year_id,
                    {$return}
                FROM
                    su_diary_events de
                LEFT JOIN su_diary_configs dc  ON de.attachment_id = dc.id
                LEFT JOIN su_diary_configs dc2 ON de.performer_id = dc2.id
                LEFT JOIN su_diary_configs dc3 ON de.type_id = dc3.id
                LEFT JOIN su_diary_configs dc4 ON de.subtype_id = dc4.id
                LEFT JOIN su_diary_configs dc5 ON de.machine_id = dc5.id
                LEFT JOIN su_diary_configs dc6 ON dc5.type_id = dc6.id
                LEFT JOIN su_diary_configs dc7 ON dc.type_id = dc7.id
                LEFT JOIN su_diary_produces dp ON dp.event_id = de.id
                LEFT JOIN su_diary_treatments_products dtp ON dtp.event_id = de.id
                LEFT JOIN su_diary_expenses dx ON (dx.activity_id = de.subtype_id
                    AND dx.performer_id = de.performer_id
                    AND de.complete_date_from::date >= dx.valid_from
                    AND de.complete_date_from::date <= dx.valid_to)";

        if (!empty($options['where']['product_type']) || !empty($options['where']['product_name'] || !empty($options['where']['product_id']))) {
            $sql .= ' LEFT JOIN su_diary_configs dc8 ON dc8.id = dtp.substance_id
                     LEFT JOIN su_diary_configs dc9 ON dc9.id = dtp.substance_unit_type';
        }

        $sql .= " LEFT JOIN
                    dblink (
                        'host=" . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' dbname=' . DEFAULT_DB_DATABASE . ' user=postgres password=' . DEFAULT_DB_PASSWORD . "' :: TEXT,
                        'select table_name, farming, year from su_users_layers where farming=' || de.farming_id || ' and year=' || de. year_id || ' and user_id={$userGroupId} and layer_type=1'
                    ) ul (
                        table_name TEXT,
                        farming int4,
                        YEAR int4
                    )
                    ON (ul.farming = de.farming_id
                    AND ul. YEAR = de. year_id)
                LEFT JOIN information_schema.tables t on t.TABLE_NAME = ul.table_name
                WHERE true";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['whereOr']) {
            $sql = $this->createWhereOrSQL($sql, $options['whereOr'], $returnOnlySQL);
        }

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        if ($options['whereOr']) {
            $this->createWhereBinds($cmd, $options['whereOr']);
        }

        return $cmd->query()->readAll();
    }

    public function getEventsWithProductsAndProduces($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} , SUM( dtp.price_per_area ) as product_cost 
                FROM su_diary_events de 
                LEFT JOIN su_diary_treatments_products dtp ON de.id = dtp.event_id 
                LEFT JOIN su_diary_produces dp ON de.id = dp.event_id 
                LEFT JOIN su_diary_configs cnf ON cnf.id = dtp.substance_id 
                WHERE true ";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], true);
        }

        $sql .= !empty($options['group']) ? ' GROUP BY de.id, ' . $options['group'] : ' GROUP BY de.id';

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($returnOnlySQL) {
            return $sql;
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }
}
