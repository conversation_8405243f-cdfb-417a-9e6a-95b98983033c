<?php

$GLOBALS['Diary']['event_phase'] = [
    1 => ['id' => 1, 'name' => 'Планирана'],
    2 => ['id' => 2, 'name' => 'Изпълнена'],
];

define('CONFIG_TYPE_TYPES', 1);
define('CONFIG_TYPE_SUB_TYPES', 2);
define('CONFIG_TYPE_MACHINES_TYPES', 3);
define('CONFIG_TYPE_MACHINES', 4);
define('CONFIG_TYPE_ATTACHMENT_TYPES', 5);
define('CONFIG_TYPE_ATTACHMENT', 6);
define('CONFIG_TYPE_PRODUCTS', 7);
define('CONFIG_TYPE_SUPPLEMENTS_TECHNIQUES', 8);
define('CONFIG_TYPE_PERFORMERS', 9);
define('CONFIG_TYPE_MEASURES', 10);

/*
name = Номенклатура(от меню "Типове дейности", "Видове дейности", "Типове машини", "Типове прикачeн инвентар", "Препарати за растителна защита", "Техники за прилагане на препарати") и Имена(от меню "Изпълнители")
number = Рег. номер(от меню "Машини", "Прикачени инвентари")
type_id = Сочи към ID на Типа избран от dropdown(от меню "Машини", "Прикачени инвентари", "Видове дейности")
model = Модел(от меню "Машини", "Прикачени инвентари")
manufacturer = Марка(от меню "Машини", "Прикачени инвентари")
description = Описание(от меню "Машини", "Прикачени инвентари", "Изпълнители")

config_type = равен е на request_type и номера на реда, който си избрал.
Примери:

 "Типове дейности" - 1,
 "Видове дейности" - 2,
 "Типове машини" - 3,
 "Машини" - 4,
 "Типове прикачeн инвентар" - 5,
 "Прикачени инвентари" - 6,
 "Продукти" - 7,
 "Техники за прилагане на препарати" - 8,
 "Изпълнители" - 9

  "Types of activities" - 1,
  "Types of activities" - 2,
  "Machine types" - 3,
  "Machines" - 4,
  "Types of Attachments" - 5,
  "Attached Inventory" - 6,
  "Products" - 7,
  "Techniques for application of preparations" - 8,
  "Performers" - 9

perf_title = Длъжност(от меню "Изпълнители")
 */
