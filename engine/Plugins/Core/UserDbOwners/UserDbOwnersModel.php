<?php

namespace TF\Engine\Plugins\Core\UserDbOwners;

use PDO;
use Prado\Exceptions\TDbException;
use TF\Engine\Plugins\Core\UserDb\UserDbModel;

class UserDbOwnersModel extends UserDbModel
{
    public function getOwnersData($options, $counter, $returnOnlySQL)
    {
        $custom_counter = $options['custom_counter'] ? $options['custom_counter'] : false;
        $return = $this->createReturnVariable($options['return'], $counter, $custom_counter);

        $sql = "SELECT {$return} FROM " . $this->tableOwners . ' o';
        $sql .= ' LEFT JOIN ' . $this->plotsOwnersRelTable . ' po ON (po.owner_id = o.id) ';
        $sql .= ' LEFT JOIN ' . $this->tableOwnersReps . ' o_r ON (o_r.owner_id = o.id) ';
        $sql .= ' LEFT JOIN ' . $this->contractsPlotsRelTable . ' cpr ON (po.pc_rel_id = cpr.id) ';
        $sql .= ' LEFT JOIN ' . $this->tableKVS . ' kvs ON (cpr.plot_id = kvs.gid) ';
        $sql .= ' LEFT JOIN ' . $this->tableContracts . ' c ON (cpr.contract_id = c.id) ';
        $sql .= ' WHERE TRUE';

        if ($options['whereOr']) {
            $values = explode(' ', $options['whereOr']['value']);
            $sql .= ' AND';
            $value_count = count($values);
            for ($i = 0; $i < $value_count; $i++) {
                $value = $values[$i];

                $sql .= " {$options['whereOr']['column1']} {$options['whereOr']['compare']} '%{$value}%' ";
                $sql .= " OR {$options['whereOr']['column2']} {$options['whereOr']['compare']} '%{$value}%' ";
                $sql .= " OR {$options['whereOr']['column3']} {$options['whereOr']['compare']} '%{$value}%' ";

                if ($i != (count($values) - 1)) {
                    $sql .= ' OR';
                }
            }
        }

        if ($options['where_or']) {
            $sql .= ' AND (false';

            $sql = $this->createWhereSQL($sql, $options['where_or'], $returnOnlySQL, 'OR');

            $sql .= ')';
        }

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        // checking for ID strings
        if ($options['id_string']) {
            $sql .= ' AND o.id IN (' . $options['id_string'] . ')';
        }
        if ($options['anti_id_string']) {
            $sql .= ' AND o.id NOT IN (' . $options['anti_id_string'] . ')';
        }

        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }
        // sorting information of there is no counter required
        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where_or']) {
            $this->createWhereBinds($cmd, $options['where_or']);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function deleteOwners($id_string)
    {
        $sql = 'DELETE FROM ' . $this->tableOwners . ' WHERE id IN (' . $id_string . ')';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function getPlotDataByOwnerFilter($options, $counter, $returnOnlySQL)
    {
        if (!$options['return']) {
            $return = '*, ST_ASTEXT(geom)';
        }

        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = 'SELECT ' . $return . ' FROM ' . $options['tablename'] . ' p'
                . ' INNER JOIN ' . $this->contractsPlotsRelTable . ' r ON(r.plot_id = p.gid)'
                . ' INNER JOIN ' . $this->tableContracts . ' c ON(r.contract_id = c.id)'
                . ' INNER JOIN ' . $this->plotsOwnersRelTable . ' re ON(re.pc_rel_id = r.id)'
                . ' INNER JOIN ' . $this->tableOwnersReps . ' o_r ON(o_r.id = re.rep_id)'
                . ' INNER JOIN ' . $this->tableOwners . ' o ON(o.id = re.owner_id) WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
            $sql = $this->createWhereSQL($sql, $options['contract_data'], $returnOnlySQL);
            $sql = $this->createWhereSQL($sql, $options['owner_data'], $returnOnlySQL);
        }

        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
            $this->createWhereBinds($cmd, $options['contract_data']);
            $this->createWhereBinds($cmd, $options['owner_data']);
        }

        return $cmd->query()->readAll();
    }

    public function getPlotOwnersData($options, $counter, $returnOnlySQL)
    {
        if (!$options['return']) {
            $options['return'] = ['rel.percent', 't.*'];
        }

        foreach ($options['return'] as &$value) {
            if ('owner_id' == $value) {
                $value = 'rel.owner_id';
            }
        }

        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM " . $this->plotsOwnersRelTable . ' rel '
                . "LEFT JOIN {$this->contractsPlotsRelTable} pc ON (rel.pc_rel_id = pc.id) "
                . 'LEFT JOIN ' . $this->tableOwners . ' t ON (rel.owner_id = t.id) '
                . 'LEFT JOIN ' . $this->tableOwnersDocuments . ' d ON (rel.owner_document_id = d.id) '
                . 'LEFT JOIN ' . $this->tableOwnersReps . ' r ON (rel.rep_id = r.id) '
                . 'WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        // checking for ID strings
        if ($options['id_string']) {
            $sql .= ' AND r.id IN (' . $options['id_string'] . ')';
        }
        if ($options['anti_id_string']) {
            $sql .= ' AND r.id NOT IN (' . $options['anti_id_string'] . ')';
        }

        if ($options && false == $counter) {
            if ($options['sort']) {
                $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
            }

            $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
            $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

            if (false !== $limit && false !== $offset) {
                $sql .= $returnOnlySQL
                    ? " LIMIT {$limit} OFFSET {$offset}"
                    : ' LIMIT :limit OFFSET :offset';
            }
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function addNewPCToOwnerRelation($relation_id, $owner_id, $percent, $owner_document_id, $rep_id)
    {
        $sql = 'INSERT INTO ' . $this->plotsOwnersRelTable . ' (pc_rel_id, owner_id, percent, owner_document_id, rep_id) '
                . 'VALUES (:rel_id, :owner_id, :percent, :owner_document_id, :rep_id)';

        $cmd = $this->DbModule->createCommand($sql);

        $cmd->bindParameter(':rel_id', $relation_id);
        $cmd->bindParameter(':owner_id', $owner_id);
        $cmd->bindParameter(':percent', $percent);
        $cmd->bindParameter(':owner_document_id', $owner_document_id);
        $cmd->bindParameter(':rep_id', $rep_id);

        $cmd->execute();
    }

    public function deletePCToOwnerRelation($relation_id, $owner_id)
    {
        $sql = 'DELETE FROM ' . $this->plotsOwnersRelTable . ' WHERE pc_rel_id = :rel_id AND owner_id = :owner_id';

        $cmd = $this->DbModule->createCommand($sql);

        $cmd->bindParameter(':rel_id', $relation_id);
        $cmd->bindParameter(':owner_id', $owner_id);

        $cmd->execute();
    }

    /**
     * Deletes plots_owners_relation by plots_contracts_relation and
     * owner path.
     *
     * @param int $relation_id
     * @param ltree $path
     */
    public function deletePCToHeritorRelation($relation_id, $path)
    {
        $sql = 'DELETE FROM ' . $this->plotsOwnersRelTable . ' WHERE pc_rel_id = :rel_id AND path ~ :path';

        $cmd = $this->DbModule->createCommand($sql);

        $cmd->bindParameter(':rel_id', $relation_id);
        $cmd->bindParameter(':path', $path);

        $cmd->execute();
    }

    public function getPlotOwnerID($pc_rel_id, $owner_id)
    {
        $sql = 'SELECT id FROM ' . $this->plotsOwnersRelTable . ' WHERE pc_rel_id = :pc_rel_id AND owner_id = :owner_id';

        $cmd = $this->DbModule->createCommand($sql);

        $cmd->bindParameter(':pc_rel_id', $pc_rel_id);
        $cmd->bindParameter(':owner_id', $owner_id);

        $result = $cmd->query()->read();

        return $result['id'];
    }

    public function getOwnersAreaReport($options, $counter, $returnOnlySQL)
    {
        if ($counter) {
            $return = 'COUNT(DISTINCT(o.id))';
        } elseif (!$options['return']) {
            $return = '*';
        } else {
            $return = implode(', ', $options['return']);
        }

        $sql = "SELECT {$return} FROM su_owners o
					INNER JOIN su_plots_owners_rel re ON(re.owner_id = o.id)
					INNER JOIN su_contracts_plots_rel r ON(re.pc_rel_id = r.id)
					INNER JOIN layer_kvs p ON(p.gid = r.plot_id)
						WHERE true";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getFullOwnerAreaReport($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = "SELECT {$return} FROM su_owners o
					INNER JOIN su_plots_owners_rel re ON(re.owner_id = o.id)
					INNER JOIN su_contracts_plots_rel r ON(re.pc_rel_id = r.id)
					INNER JOIN layer_kvs p ON(p.gid = r.plot_id)
					INNER JOIN su_contracts c ON(r.contract_id = c.id)
					LEFT JOIN {$this->tableContracts} a ON(a.parent_id = c.id AND a.active = true AND a.start_date <= :start_date AND a.due_date >= :due_date)
						WHERE true";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['start_date']) {
            $sql .= ' AND (c.start_date <= :start_date OR a.start_date <= :start_date)';
        }

        if ($options['due_date']) {
            $sql .= ' AND (c.due_date >= :due_date OR a.due_date >= :due_date)';
        }

        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['start_date']) {
            $cmd->bindParameter(':start_date', $options['start_date']);
        }
        if ($options['due_date']) {
            $cmd->bindParameter(':due_date', $options['due_date']);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getOwnersDataByContract($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM {$this->tableOwners} o";
        $sql .= " INNER JOIN {$this->plotsOwnersRelTable} po ON(po.owner_id = o.id)";
        $sql .= " LEFT JOIN {$this->tableOwnersReps} r ON(po.rep_id = r.id)";
        $sql .= " INNER JOIN {$this->contractsPlotsRelTable} pc ON(pc.id = po.pc_rel_id)";

        if (!empty($options['leftjoin'])) {
            $sql .= ' LEFT JOIN ' . $options['leftjoin']['table'] . $options['leftjoin']['condition'];
        }

        $sql .= ' WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getOwnersHeritors($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = " SELECT {$return} FROM {$this->tableOwners} o"
                . " INNER JOIN {$this->tableHeritors} h ON(h.owner_id = o.id)";
        if ($options['onCondition']) {
            $sql .= " LEFT JOIN {$this->plotsOwnersRelTable} poi ON(poi.path = h.path " . $options['onCondition'] . ')';
            $sql .= " LEFT JOIN {$this->contractsPlotsRelTable} pc ON (poi.pc_rel_id = pc.id)";
            $sql .= " LEFT JOIN {$this->tableOwnersReps} r ON(r.id = poi.rep_id)";
        }

        if (is_array($options['joins'])) {
            foreach ($options['joins'] as $join) {
                $sql .= ' ' . $join . ' ';
            }
        }

        $sql .= ' WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }
        if ($options['where_or']) {
            $sql = $this->createWhereSQL($sql, $options['where_or'], $returnOnlySQL, 'OR');
        }
        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }
        if ($options['where_or']) {
            $this->createWhereBinds($cmd, $options['where_or']);
        }

        return $cmd->query()->readAll();
    }

    public function getPlotFarmingData($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM {$this->plotsFarmingRelTable} f";
        $sql .= " LEFT JOIN {$this->tableOwnersReps} r ON(r.id = f.rep_id)";
        $sql .= ' WHERE TRUE';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($returnOnlySQL) {
            return $sql;
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getOwnerContracts($options, $counter = false, $returnOnlySQL = false)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} ";
        $sql .= " FROM {$this->tableOwners} o";
        $sql .= " LEFT JOIN {$this->plotsOwnersRelTable} spor ON(spor.owner_id = o.id)";
        $sql .= " INNER JOIN {$this->contractsPlotsRelTable} scpr ON(scpr.id = spor.pc_rel_id)";
        $sql .= " LEFT JOIN {$this->tableContracts} c ON(c.id = scpr.contract_id)";
        $sql .= ' WHERE TRUE';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        $sql .= ' UNION ALL ';
        $sql .= " SELECT {$return}";
        $sql .= " FROM {$this->tableOwners} o";
        $sql .= " INNER JOIN {$this->tableContractsContragents} scc ON(scc.owner_id = o.id)";
        $sql .= " LEFT JOIN {$this->tableContracts} c ON(c.id = scc.contract_id)";
        $sql .= ' WHERE TRUE';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($returnOnlySQL) {
            return $sql;
        }
        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getOwnersPaymentsData($options, $counter, $returnOnlySQL)
    {
        if (true == $counter) {
            $custom_counter = 'count(distinct(case when o.is_dead = true then h.owner_id else o.id end))';
            $sql = '';
        } else {
            $custom_counter = false;
            $sql = 'SELECT * FROM (';
        }
        $return = $this->createReturnVariable($options['return'], $counter, $custom_counter);

        $sql .= "SELECT {$return} FROM {$this->tableContracts} c ";
        $sql .= ' LEFT JOIN (SELECT DISTINCT ON(parent_id) * '
            . "FROM {$this->tableContracts} "
            . 'WHERE is_annex = true '
                . 'AND active = true '
                    . 'AND start_date <= :start_date '
                        . 'AND due_date >= :due_date '
                            . 'ORDER BY parent_id, due_date DESC) a '
                                . 'ON a.parent_id = c.id';
        $sql .= " LEFT JOIN {$this->contractsRentsRelTable} c_r ON ((CASE WHEN A.id IS NULL THEN C.id ELSE A.id END) = c_r.contract_id)";
        $sql .= " INNER JOIN {$this->contractsPlotsRelTable} pc ON (pc.contract_id = (CASE WHEN a.id IS NULL THEN c.id ELSE a.id END))";
        $sql .= " INNER JOIN {$this->plotsOwnersRelTable} po ON (po.pc_rel_id = pc.id)";
        $sql .= " INNER JOIN {$this->tableOwners} o ON (o.id = po.owner_id)";
        $sql .= " LEFT JOIN {$this->tableOwnersReps} o_r ON (o_r.id = po.rep_id)";
        $sql .= " LEFT JOIN {$this->tableHeritors} h on (o.id = ltree2text(subltree(h.path, 0, 1))::numeric)";
        $sql .= " LEFT JOIN {$this->tableOwners} oh on (h.owner_id = oh.id) WHERE true";
        $sql .= ' AND (c.start_date <= :start_date OR a.start_date <= :start_date)';
        $sql .= ' AND (c.due_date >= :due_date OR a.due_date >= :due_date)';
        $sql .= ' AND (oh.is_dead = false or oh.is_dead is NULL)';
        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['whereOr']) {
            $sql = $this->createWhereOrSQL($sql, $options['whereOr'], $returnOnlySQL);
        }

        // checking for ID strings
        if ($options['id_string']) {
            $sql .= ' AND id IN (' . $options['id_string'] . ')';
        }
        if ($options['anti_id_string']) {
            $sql .= ' AND id NOT IN (' . $options['anti_id_string'] . ')';
        }

        if (!$counter) {
            $sql .= ') as data ORDER BY (CASE WHEN data.owner_type = 1 THEN data.owner_names ELSE data.company_name END) COLLATE "alpha_numeric_bg" asc';
            // sorting information of there is no counter required

            $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
            $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

            if (false !== $limit && false !== $offset) {
                $sql .= $returnOnlySQL
                    ? " LIMIT {$limit} OFFSET {$offset}"
                    : ' LIMIT :limit OFFSET :offset';
            }
        }

        if ($returnOnlySQL) {
            $sql = str_replace(':start_date', "'" . $options['start_date'] . "'", $sql);

            return str_replace(':due_date', "'" . $options['due_date'] . "'", $sql);
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['start_date']) {
            $cmd->bindParameter(':start_date', $options['start_date']);
        }
        if ($options['due_date']) {
            $cmd->bindParameter(':due_date', $options['due_date']);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        if ($options['whereOr']) {
            $this->createWhereBinds($cmd, $options['whereOr']);
        }

        return $cmd->query()->readAll();
    }

    /**
     * makeOwnersTreeToListById makes owners tree to list by owner_id.
     *
     * @param array $tree tree with result for owners
     * @param array &$list empty array
     * @param int $searchId owner_id
     *
     * @return array
     */
    public function makeOwnersTreeToListById($tree, &$list, $searchId)
    {
        for ($i = 0; $i < count($tree); $i++) {
            $node = $tree[$i];
            if ($node['children']) {
                $this->makeOwnersTreeToListById($node['children'], $list, $searchId);
            }
            if ($node['owner_id'] == $searchId && floatval($node['area']) > 0) {
                $list[] = $node;
            }
        }

        return $list;
    }

    /**
     * summaryOwnerResultsByFields summary owner results by fields.
     *
     * @param array $ownerResults with result for owner
     * @param array $fields with fields for summary
     *
     * @return array with summary owner result
     */
    public function summaryOwnerResultsByFields($ownerResults, $fields)
    {
        $sumArray = [];

        foreach ($ownerResults as $k => $subArray) {
            foreach ($subArray as $id => $value) {
                if (in_array($id, $fields)) {
                    if ('-' === $value && ('string' == gettype($sumArray[$id]) || is_null($sumArray[$id]))) {
                        $sumArray[$id] = $value;

                        continue;
                    }

                    $sumArray[$id] += (float)$value;

                    continue;
                }

                if (!array_key_exists($id, $sumArray)) {
                    $sumArray[$id] = $value;
                }
            }
        }

        return $sumArray;
    }

    /**
     * saveOwnerResultsByPathInArray save owner results by path and fields in array.
     *
     * @param array $ownerResults with result for owner
     * @param array $fields with fields for save to array
     *
     * @return array with save fields in array
     */
    public function saveOwnerResultsByPathInArray($ownerResults, $fields)
    {
        $sumArray = [];

        for ($i = 0; $i < count($fields); $i++) {
            $field = $fields[$i];

            $path = null != $ownerResults['path'] ? $ownerResults['path'] : $ownerResults['owner_id'];
            $id = null != $ownerResults['path'] ? 'path_renta_natura' : 'id_renta_natura';
            $sumArray[$id][$path][$field][] = $ownerResults[$field];
        }

        return $sumArray;
    }

    /**
     * summaryOwnerResultsByNewLineFields summary owner results by new line fields.
     *
     * @param array $ownerResults with result for owner
     * @param array $fields with fields for summary
     *
     * @return array with summary owner result
     */
    public function summaryOwnerResultsByNewLineFields($ownerResults, $fields)
    {
        $sumArray = [];

        for ($i = 0; $i < count($fields); $i++) {
            $field = $fields[$i];

            for ($j = 0; $j < count($ownerResults[0][$field]); $j++) {
                $temp = '';

                for ($m = 0; $m < count($ownerResults); $m++) {
                    if ('-' === $ownerResults[$m][$field][$j] && ('string' == gettype($temp) || is_null($temp))) {
                        $temp = $ownerResults[$m][$field][$j];

                        continue;
                    }
                    $temp += (float)$ownerResults[$m][$field][$j];
                }

                if ('-' === $temp) {
                    $sumArray[$field][] = $temp;

                    continue;
                }

                $sumArray[$field][] = number_format($temp, 3, '.', '');
            }
        }

        return $sumArray;
    }

    /**
     * getOwnerHeritors [recursively get owner heritors].
     *
     * @param array $heritorParams with params for heritor
     * @param array $c_arr with params for contract
     * @param array $filterParam{
     *
     * @item int year,
     * @item int owner_id
     * }
     *
     * @return array with params for heritor
     */
    public function getOwnerHeritors($UserDbPaymentsController, $heritorParams, $parent_plots_ownage = false, $c_arr, $contractId, $filterParam, $iterator)
    {
        $year = $filterParam['year'];
        $ownerId = $filterParam['owner_id'];
        $startDate = $filterParam['start_date'];
        $dueDate = $filterParam['due_date'];
        $path = $heritorParams['path'];
        $level = $heritorParams['level'];
        $root_id = $heritorParams['root_id'];

        $heritor_data = [];
        $heritor_percent_by_plots = [];
        $level++;

        // get all plots chosen root
        $options = [
            'return' => [
                'o.id as owner_id', 'is_dead',
                'pc.id as pc_rel_id', 'pc.contract_area as area',
                'kvs.gid',
                'c.id as contract_id',
                'po.percent',
                '(CASE WHEN a.renta IS NULL THEN c.renta ELSE a.renta END) as renta',
            ],
            'where' => [
                'is_heritor' => ['column' => 'is_heritor', 'compare' => '=', 'prefix' => 'po', 'value' => 'FALSE'],
                'owner_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'o', 'value' => $root_id],
                'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
            ],
            // this parameter will be used for joining charged renta table
            'chosen_year' => $year,
            'contract_id_string' => $contractId,
            'start_date' => $startDate,
            'due_date' => $dueDate,
        ];

        $plot_results = $UserDbPaymentsController->getPaymentsForOwners($options, false, false);

        // iterate all plots
        for ($i = 0; $i < count($plot_results); $i++) {
            $plotId = $plot_results[$i]['gid'];

            // get heritors for the current plot
            $options = [
                'return' => [
                    'h.id', 'owner_id', 'is_dead', 'path',
                    "(SELECT poi.percent FROM su_plots_owners_rel poi WHERE poi.path = h.path AND poi.is_heritor = true AND poi.pc_rel_id = {$plot_results[$i]['pc_rel_id']}) as percent",
                ],
                'where' => [
                    'path' => ['column' => 'path', 'compare' => '~', 'value' => $path],
                ],
            ];

            $results = $this->getOwnersHeritors($options, false, false);

            $sumCustomOwnage = 0;
            $heritors = 0;

            for ($j = 0; $j < count($results); $j++) {
                if (null != $results[$j]['percent']) {
                    $sumCustomOwnage += $results[$j]['percent'];
                } else {
                    $heritors++;
                }
            }

            for ($j = 0; $j < count($results); $j++) {
                $ownerID = $results[$j]['owner_id'];
                $parent_path_e = explode('.', $results[$j]['path'], -1);
                $parent_path = implode('.', $parent_path_e);

                if (!$results[$j]['percent'] || 0 == $results[$j]['percent']) {
                    if ($parent_plots_ownage[$plotID][$parent_path] || '0' == $parent_plots_ownage[$plotID][$parent_path]) {
                        if ('0' == $results[$j]['percent']) {
                            $results[$j]['percent'] = '0';
                        } else {
                            $results[$j]['percent'] = ($parent_plots_ownage[$plotID][$parent_path] - $sumCustomOwnage) / $heritors;
                        }

                        $heritor_percent_by_plots[$plotID][$results[$j]['path']] = $results[$j]['percent'];
                    } else {
                        if (null == $results[$j]['percent']) {
                            $results[$j]['percent'] = ($plot_results[$i]['percent'] - $sumCustomOwnage) / $heritors;
                            $heritor_percent_by_plots[$plotID][$results[$j]['path']] = $results[$j]['percent'];
                        } elseif ('0' == $results[$j]['percent']) {
                            $heritor_percent_by_plots[$plotID][$results[$j]['path']] = '0';
                        }
                    }
                } else {
                    $heritor_percent_by_plots[$plotID][$results[$j]['path']] = $results[$j]['percent'];
                }

                $heritor_data[$ownerID]['area'] += $plot_results[$i]['area'] * $results[$j]['percent'] / 100;
                $heritor_data[$ownerID]['renta'] = $plot_results[$i]['renta'];
                $heritor_data[$ownerID]['contract_id'] = $plot_results[$i]['contract_id'];
            }
        }

        // get all heritors
        $options = [
            'return' => [
                'h.id', "name || ' ' || surname || ' ' || lastname as owner_names", 'owner_id', 'is_dead', 'path',
                "(SELECT SUM(amount) FROM su_payments p WHERE p.contract_id = {$contractId} AND
                 p.owner_id = h.owner_id AND paid_from = 1 AND p.farming_year = {$year} AND p.is_heritor = TRUE AND p.path = h.path) as paid_renta",
            ],
            'where' => [
                'path' => ['column' => 'path', 'compare' => '~', 'value' => $path],
            ],
        ];

        $counter = $this->getOwnersHeritors($options, true, false);

        if (0 == $counter[0]['count']) {
            return [];
        }

        // all heritors
        $heritor_results = $this->getOwnersHeritors($options, false, false);

        for ($i = 0; $i < count($heritor_results); $i++) {
            $ownerID = $heritor_results[$i]['owner_id'];
            $owner_area = number_format($heritor_data[$ownerID]['area'], 3, '.', '');
            $heritor_results[$i]['c_num'] = $c_arr['c_num'];
            $heritor_results[$i]['type'] = $c_arr['type'];
            $heritor_results[$i]['is_annex'] = $c_arr['is_annex'];
            $path_arr = explode('.', $heritor_results[$i]['path']);
            $heritor_results[$i]['parent_id'] = $path_arr[0];
            $heritor_results[$i]['contract_id'] = $heritor_data[$ownerID]['contract_id'];
            $heritor_results[$i]['owner_area'] = $owner_area;

            $iterator++;
            $heritor_results[$i]['id'] = $iterator;

            if ($heritor_results[$i]['is_dead']) {
                $heritorParams = [
                    'path' => $heritor_results[$i]['path'] . '.*{1}',
                    'level' => $level,
                    'root_id' => $root_id,
                ];

                $heritor_results[$i]['children'] = $this->getOwnerHeritors(
                    $UserDbPaymentsController,
                    $heritorParams,
                    $parent_plots_ownage = false,
                    $c_arr,
                    $contractId,
                    $filterParam,
                    $iterator
                );
            }

            $heritor_results[$i]['is_heritor'] = true;
            $heritor_results[$i]['level'] = $level;
        }

        return $heritor_results;
    }

    public function getDocumentPlotData($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM {$this->tableOwnersDocuments} od";
        $sql .= ' INNER JOIN layer_kvs kvs ON(kvs.gid = od.plot_id)';
        $sql .= ' WHERE TRUE';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($returnOnlySQL) {
            return $sql;
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getOwnersFiles($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM su_owners_files f";
        $sql .= ' WHERE TRUE';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($returnOnlySQL) {
            return $sql;
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getRepresentativesTree($options, $counter = false, $returnOnlySQL = false)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = " SELECT {$return} FROM " . $this->tableOwnersReps . ' o_r';
        $sql .= ' LEFT JOIN ' . $this->plotsOwnersRelTable . ' por ON (por.rep_id = o_r.id) ';
        $sql .= ' LEFT JOIN ' . $this->tableOwners . ' o ON (por.owner_id = o.id) ';
        $sql .= ' LEFT JOIN ' . $this->contractsPlotsRelTable . ' cpr ON (por.pc_rel_id = cpr.id) ';
        $sql .= ' LEFT JOIN ' . $this->tableKVS . ' kvs ON (cpr.plot_id = kvs.gid) ';
        $sql .= ' LEFT JOIN ' . $this->tableContracts . ' c ON (cpr.contract_id = c.id) ';
        $sql .= ' WHERE TRUE';

        if ($options['whereOr']) {
            $values = explode(' ', $options['whereOr']['value']);
            $sql .= ' AND';

            for ($i = 0; $i < count($values); $i++) {
                $value = $values[$i];

                $sql .= " {$options['whereOr']['column1']} {$options['whereOr']['compare']} '%{$value}%' ";
                $sql .= " OR {$options['whereOr']['column2']} {$options['whereOr']['compare']} '%{$value}%' ";
                $sql .= " OR {$options['whereOr']['column3']} {$options['whereOr']['compare']} '%{$value}%' ";

                if ($i != (count($values) - 1)) {
                    $sql .= ' OR';
                }
            }
        }

        if ($options['where_or']) {
            $sql .= ' AND (false';

            $sql = $this->createWhereSQL($sql, $options['where_or'], $returnOnlySQL, 'OR');

            $sql .= ')';
        }

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        // checking for ID strings
        if ($options['id_string']) {
            $sql .= ' AND o.id IN (' . $options['id_string'] . ')';
        }
        if ($options['anti_id_string']) {
            $sql .= ' AND o.id NOT IN (' . $options['anti_id_string'] . ')';
        }

        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }
        // sorting information of there is no counter required
        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where_or']) {
            $this->createWhereBinds($cmd, $options['where_or']);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getRepresentativePlots($options, $counter = false, $returnOnlySQL = false)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} from su_plots_owners_rel por ";
        $sql .= 'LEFT JOIN su_contracts_plots_rel cpr on cpr.id = por.pc_rel_id ';
        $sql .= 'LEFT JOIN su_owners_reps o_r on o_r.id = por.rep_id ';
        $sql .= 'left join su_contracts c on c.id = cpr.contract_id ';
        $sql .= 'left JOIN layer_kvs kvs on kvs.gid = cpr.plot_id ';
        $sql .= 'LEFT JOIN su_owners o on o.id = por.owner_id ';
        $sql .= ' WHERE TRUE';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }
        // sorting information of there is no counter required
        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getOwnerRepresentedPlots($options, $counter = false, $returnOnlySQL = false)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} from su_plots_owners_rel por ";
        $sql .= 'LEFT JOIN su_contracts_plots_rel cpr on cpr.id = por.pc_rel_id ';
        $sql .= 'LEFT JOIN su_owners_reps o_r on o_r.id = por.rep_id ';
        $sql .= 'left join su_contracts c on c.id = cpr.contract_id ';
        $sql .= 'left JOIN layer_kvs kvs on kvs.gid = cpr.plot_id ';
        $sql .= 'LEFT JOIN su_owners o on o.id = por.owner_id ';
        $sql .= ' WHERE TRUE';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }
        $sql .= ' AND (o_r.owner_id is null or o_r.owner_id <> :owner_id) ';
        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }
        // sorting information of there is no counter required
        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }
        $cmd->bindParameter(':owner_id', $options['owner_id']);

        return $cmd->query()->readAll();
    }

    public function replaceRepresentativeWithSelfRep($repId)
    {
        $sql = 'UPDATE su_plots_owners_rel por
                SET rep_id = o_r.id
                FROM su_owners_reps o_r
                WHERE por.owner_id = o_r.owner_id
                AND por.rep_id = :rep_id';

        $cmd = $this->DbModule->createCommand($sql);

        $cmd->bindParameter(':rep_id', $repId);

        $cmd->execute();
    }

    public function setSelfRepresentative($options)
    {
        $sql = 'UPDATE su_plots_owners_rel por
                SET rep_id = null
                from su_contracts_plots_rel cpr 
                WHERE por.owner_id = :owner_id
                AND cpr.contract_id = :contract_id
                AND por.pc_rel_id = cpr.id';

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':contract_id', $options['contract_id']);
        $cmd->bindParameter(':owner_id', $options['owner_id']);

        $cmd->execute();
    }

    public function isRepresentingLegalEntities($repId)
    {
        $sql = 'SELECT
                    count(*)
                FROM
                    su_plots_owners_rel por
                LEFT JOIN su_owners o ON o. ID = por.owner_id
                WHERE
                    owner_type = 0
                AND rep_id = :rep_id';

        $cmd = $this->DbModule->createCommand($sql);

        $cmd->bindParameter(':rep_id', $repId);

        $results = $cmd->query()->readAll();

        return (bool) ($results[0]['count'] > 0);
    }

    public function addPercentRecord(array $data, $contractId)
    {
        $results = $this->getContractOwnerPercent($data, $contractId)[0];

        if ($results && (float)$results['percent'] != (float)$data['percent']) {
            $this->updateContractOwnerPercent($data, $contractId);
        } elseif (null === $results) {
            $this->insertContractOwnerPercent($data, $contractId);
        }
    }

    public function getParentPlotOwnerRel($parentPathId)
    {
        $sqlSelect = "SELECT
                   por.pc_rel_id
                FROM
                     {$this->plotsOwnersRelTable} por
                WHERE
                    por.owner_id = :ownerId";

        $cmd = $this->DbModule->createCommand($sqlSelect);
        $cmd->bindParameter(':ownerId', $parentPathId);

        return $results = $cmd->query()->readAll();
    }

    public function getContractOwnerPercent(array $data, $contractId)
    {
        $sqlSelect = "SELECT
                   por.percent
                FROM
                     {$this->plotsOwnersRelTable} por
                WHERE
                    por.pc_rel_id = :contractId
                AND por.owner_id = :ownerId
                AND por.path ~ :path";

        $cmd = $this->DbModule->createCommand($sqlSelect);
        $cmd->bindParameter(':contractId', $contractId);
        $cmd->bindParameter(':ownerId', $data['owner_id']);
        $cmd->bindParameter(':path', $data['path']);

        return $results = $cmd->query()->readAll();
    }

    public function updateContractOwnerPercent(array $data, $contractId)
    {
        $sqlUpdate = "UPDATE {$this->plotsOwnersRelTable} SET percent = :percent 
                WHERE 
                      pc_rel_id = :contractId 
                AND   owner_id = :ownerId 
                AND   path ~ :path";

        $cmd = $this->DbModule->createCommand($sqlUpdate);
        $cmd->bindParameter(':contractId', $contractId);
        $cmd->bindParameter(':ownerId', $data['owner_id']);
        $cmd->bindParameter(':percent', $data['percent']);
        $cmd->bindParameter(':path', $data['path']);
        $cmd->execute();
    }

    public function insertContractOwnerPercent(array $data, $contractId)
    {
        $sqlInsert = 'INSERT INTO ' . $this->plotsOwnersRelTable . '
                             (pc_rel_id, owner_id, percent, path, is_heritor, is_set_manual)
                      VALUES (:contractId, :ownerId, :percent, :path, TRUE, FALSE)';

        $cmd = $this->DbModule->createCommand($sqlInsert);
        $cmd->bindParameter(':contractId', $contractId);
        $cmd->bindParameter(':ownerId', $data['owner_id']);
        $cmd->bindParameter(':percent', $data['percent']);
        $cmd->bindParameter(':path', $data['path']);
        $cmd->execute();
    }

    public function getOwnersPayments($options, $counter, $returnOnlySQL)
    {
        if (true == $counter) {
            $custom_counter = 'count(distinct(o.id))';
            $sql = '';
        } else {
            $custom_counter = false;
            $sql = 'SELECT * FROM (';
        }

        $return = $this->createReturnVariable($options['return'], $counter, $custom_counter);

        $sql .= "SELECT {$return} FROM {$this->tableContracts} c ";
        $sql .= ' LEFT JOIN (SELECT DISTINCT ON(parent_id) * '
            . "FROM {$this->tableContracts} "
            . 'WHERE is_annex = true '
                . 'AND active = true '
                    . 'AND start_date <= :start_date '
                        . 'AND due_date >= :due_date '
                            . 'ORDER BY parent_id, due_date DESC) a '
                                . 'ON a.parent_id = c.id';
        $sql .= " LEFT JOIN {$this->contractsRentsRelTable} c_r ON ((CASE WHEN A.id IS NULL THEN C.id ELSE A.id END) = c_r.contract_id)";
        $sql .= " INNER JOIN {$this->contractsPlotsRelTable} pc ON (pc.contract_id = (CASE WHEN a.id IS NULL THEN c.id ELSE a.id END))";
        $sql .= " INNER JOIN {$this->plotsOwnersRelTable} po ON (po.pc_rel_id = pc.id)";
        $sql .= " INNER JOIN {$this->tableOwners} o ON (o.id = po.owner_id)";
        $sql .= " LEFT JOIN {$this->tableOwnersReps} o_r ON (o_r.id = po.rep_id)";
        $sql .= " LEFT JOIN {$this->tableHeritors} h ON h.owner_id = o.id";
        $sql .= ' WHERE true';
        $sql .= ' AND (c.start_date <= :start_date OR a.start_date <= :start_date)';
        $sql .= ' AND (c.due_date >= :due_date OR a.due_date >= :due_date)';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['whereOr']) {
            $sql = $this->createWhereOrSQL($sql, $options['whereOr'], $returnOnlySQL);
        }

        // checking for ID strings
        if ($options['id_string']) {
            $sql .= ' AND id IN (' . $options['id_string'] . ')';
        }
        if ($options['anti_id_string']) {
            $sql .= ' AND id NOT IN (' . $options['anti_id_string'] . ')';
        }

        if (!$counter) {
            $sql .= ') as data ORDER BY (CASE WHEN data.owner_type = 1 THEN data.owner_names ELSE data.company_name END) COLLATE "alpha_numeric_bg" asc';
            // sorting information of there is no counter required
            $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
            $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

            if (false !== $limit && false !== $offset) {
                $sql .= $returnOnlySQL
                    ? " LIMIT {$limit} OFFSET {$offset}"
                    : ' LIMIT :limit OFFSET :offset';
            }
        }

        if ($returnOnlySQL) {
            $sql = str_replace(':start_date', "'" . $options['start_date'] . "'", $sql);

            return str_replace(':due_date', "'" . $options['due_date'] . "'", $sql);
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['start_date']) {
            $cmd->bindParameter(':start_date', $options['start_date']);
        }
        if ($options['due_date']) {
            $cmd->bindParameter(':due_date', $options['due_date']);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        if ($options['whereOr']) {
            $this->createWhereBinds($cmd, $options['whereOr']);
        }

        return $cmd->query()->readAll();
    }

    public function getOwners($ownerIds)
    {
        $ids = implode(',', $ownerIds);
        $sql = "
            SELECT
                *
            FROM 
                su_owners as owners
            WHERE
                owners.id IN ({$ids})     
        ";

        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function getAllOwnersSortedAlphabetically()
    {
        $sqlSelect = "SELECT
            id
        FROM
            {$this->tableOwners} as o 
            ORDER BY (CASE WHEN o.owner_type = 1 THEN TRIM(o.name) || ' ' || TRIM(o.surname) || ' ' || TRIM(o.lastname) ELSE o.company_name END) COLLATE \"alpha_numeric_bg\" asc;   
        ";

        $cmd = $this->DbModule->createCommand($sqlSelect);

        return $cmd->query()->readAll();
    }

    /**
     * @throws TDbException
     */
    public function changeContractOwnerSignedDoc($criteries, $isSigner)
    {
        $sqlUpdate = "UPDATE {$this->plotsOwnersRelTable} SET is_signer = :is_signer
                      WHERE true ";

        if (!empty($criteries['id'])) {
            $sqlUpdate .= ' and id = :id';
        }

        if (!empty($criteries['owner_id'])) {
            $sqlUpdate .= ' and owner_id = :owner_id';
        }

        $cmd = $this->DbModule->createCommand($sqlUpdate);

        $cmd->bindParameter(':is_signer', $isSigner);
        foreach ($criteries as $key => $value) {
            $cmd->bindParameter(':' . $key, $value);
        }

        $cmd->execute();
    }

    /**
     * @param int $ownerId
     * @param string $deadDate
     *
     * @throws TDbException
     *
     * @return array
     */
    public function getPersonalUseOfDeadOwner($ownerId, $deadDate)
    {
        $sql = 'select 
                    sc.id as contract_id,
                    sc.c_num as c_num,
                    sc.start_date as start_date,
                    sc.due_date as due_date,
                    lk.gid as plot_id,
                    lk.kad_ident as kad_ident
                from su_personal_use spu
                left join su_contracts_plots_rel scpr on scpr.id = spu.pc_rel_id
                left join layer_kvs lk on lk.gid = scpr.plot_id 
                left join su_contracts sc on sc.id = scpr.contract_id 
                where 
                    spu.owner_id = :owner_id
                    and sc.start_date <= :dead_date
                    and sc.due_date >= :dead_date';
        $cmd = $this->DbModule->createCommand($sql);

        $cmd->bindParameter(':owner_id', $ownerId);
        $cmd->bindParameter(':dead_date', $deadDate);

        return $cmd->query()->readAll();
    }

    public function getPreviousFarmYearsForOwner($ownerId, $yearId)
    {
        $currentFarmYearStartDate = $GLOBALS['Farming']['years'][$yearId]['start_date'];
        $prevFarmYearDueDate = $GLOBALS['Farming']['years'][--$yearId]['end_date'];
        $firstFarmYearInConfig = $GLOBALS['Farming']['years'][4]['start_date'];

        $sql = "
            with annexes as (
                select 
                    parent_id, 
                    id,
                    start_date,
                    due_date,
                    row_number() over (partition by a.parent_id order by due_date desc) as rn 
                from su_contracts a
                where a.is_annex = true 
                    and a.active = true
            )
            select 
                distinct 
                fy.farm_year_start::date,
                (fy.farm_year_start + interval '11 months' + interval '29 days')::date as farm_year_end
            from su_contracts c 
            left join annexes a ON a.parent_id = c.id  and a.rn = 1
            inner join su_contracts_plots_rel pc on (pc.contract_id = coalesce(a.id, c.id)) 
            left join su_plots_owners_rel po on (po.pc_rel_id = pc.id) 
            left join su_owners o on (o.id = po.owner_id)
            left join lateral (
                select
                    generate_series(
                        date_trunc('year', c.start_date) + interval '9 months',
                        case
                            when c.due_date is null then :prev_farm_year_due_date -- Representation of infinity with a selected date in the future
                            else date_trunc('year', case when a.due_date > c.due_date then a.due_date else c.due_date end) + interval '8 months' + interval '29 days'
                        end,
                        interval '1 year'
                    ) AS farm_year_start
            ) fy on true
            where o.id = :owner_id
                and c.active = true
                and po.percent > 0
                and c.start_date < :farm_year_start
                and c.start_date >= :first_farm_year_in_config
                and (fy.farm_year_start + INTERVAL '11 months' + INTERVAL '29 days')::date <:farm_year_start
            order by farm_year_end
        ";

        $cmd = $this->DbModule->createCommand($sql);

        $cmd->bindParameter(':owner_id', $ownerId);
        $cmd->bindParameter(':farm_year_start', $currentFarmYearStartDate);
        $cmd->bindParameter(':prev_farm_year_due_date', $prevFarmYearDueDate);
        $cmd->bindParameter(':first_farm_year_in_config', $firstFarmYearInConfig);

        return $cmd->query()->readAll();
    }
}
