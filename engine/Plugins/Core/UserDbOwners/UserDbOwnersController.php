<?php

namespace TF\Engine\Plugins\Core\UserDbOwners;

use Prado\Exceptions\TDbException;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

// Prado::using('Plugins.Core.UserDb.UserDbController');

class UserDbOwnersController extends UserDbController
{
    public const USER_HAS_CONTRACTS = -33315;
    public $DbHandler;
    public $Database;

    public function __construct($database)
    {
        $this->DbHandler = new UserDbOwnersModel($database);
        $this->Database = $database;
    }

    public function getOwnersData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getOwnersData($options, $counter, $returnOnlySQL);
    }

    public function hasOwnerContracts($options)
    {
        $ownerContracts = $this->DbHandler->getOwnerContracts($options);

        if (count($ownerContracts) > 0) {
            throw new MTRpcException($ownerContracts, UserDbOwnersController::USER_HAS_CONTRACTS);
        }
    }

    public function deleteOwners($id_string)
    {
        $this->DbHandler->deleteOwners($id_string);
    }

    public function getPlotDataByOwnerFilter($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getPlotDataByOwnerFilter($options, $counter, $returnOnlySQL);
    }

    public function getPlotOwnersData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getPlotOwnersData($options, $counter, $returnOnlySQL);
    }

    public function addNewPCToOwnerRelation($relation_id, $owner_id, $percent, $owner_document_id = null, $rep_id = null)
    {
        $this->DbHandler->addNewPCToOwnerRelation($relation_id, $owner_id, $percent, $owner_document_id, $rep_id);
    }

    public function deletePCToOwnerRelation($relation_id, $owner_id)
    {
        $this->DbHandler->deletePCToOwnerRelation($relation_id, $owner_id);
        $this->deletePCToHeritorRelation($relation_id, $owner_id . '.*');
    }

    public function deletePCToHeritorRelation($relation_id, $path)
    {
        $this->DbHandler->deletePCToHeritorRelation($relation_id, $path);
    }

    public function getPlotOwnerID($pc_rel_id, $owner_id)
    {
        return $this->DbHandler->getPlotOwnerID($pc_rel_id, $owner_id);
    }

    public function getOwnersAreaReport($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getOwnersAreaReport($options, $counter, $returnOnlySQL);
    }

    public function getFullOwnerAreaReport($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getFullOwnerAreaReport($options, $counter, $returnOnlySQL);
    }

    public function getOwnersDataByContract($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getOwnersDataByContract($options, $counter, $returnOnlySQL);
    }

    public function getOwnersHeritors($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getOwnersHeritors($options, $counter, $returnOnlySQL);
    }

    public function getPlotFarmingData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getPlotFarmingData($options, $counter, $returnOnlySQL);
    }

    public function getOwnersPaymentsData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getOwnersPaymentsData($options, $counter, $returnOnlySQL);
    }

    public function getOwnersPayments($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getOwnersPayments($options, $counter, $returnOnlySQL);
    }

    public function makeOwnersTreeToListById($tree, $list, $searchId)
    {
        return $this->DbHandler->makeOwnersTreeToListById($tree, $list, $searchId);
    }

    public function saveOwnerResultsByPathInArray($ownerResults, $fields)
    {
        return $this->DbHandler->saveOwnerResultsByPathInArray($ownerResults, $fields);
    }

    public function summaryOwnerResultsByFields($ownerResults, $fields)
    {
        return $this->DbHandler->summaryOwnerResultsByFields($ownerResults, $fields);
    }

    public function summaryOwnerResultsByNewLineFields($ownerResults, $fields)
    {
        return $this->DbHandler->summaryOwnerResultsByNewLineFields($ownerResults, $fields);
    }

    public function getOwnerHeritors($UserDbPaymentsController, $heritorParams, $parent_plots_ownage = false, $c_arr, $contractId, $filterParam, $iterator)
    {
        return $this->DbHandler->getOwnerHeritors($UserDbPaymentsController, $heritorParams, $parent_plots_ownage = false, $c_arr, $contractId, $filterParam, $iterator);
    }

    public function getDocumentPlotData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getDocumentPlotData($options, $counter, $returnOnlySQL);
    }

    public function getOwnersFiles($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getOwnersFiles($options, $counter, $returnOnlySQL);
    }

    public function getRepresentativesTree($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getRepresentativesTree($options, $counter, $returnOnlySQL);
    }

    public function getRepresentativePlots($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getRepresentativePlots($options, $counter, $returnOnlySQL);
    }

    public function getOwnerRepresentedPlots($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getOwnerRepresentedPlots($options, $counter, $returnOnlySQL);
    }

    public function replaceRepresentativeWithSelfRep($repId)
    {
        return $this->DbHandler->replaceRepresentativeWithSelfRep($repId);
    }

    public function setSelfRepresentative($options)
    {
        return $this->DbHandler->setSelfRepresentative($options);
    }

    public function isRepresentingLegalEntities($repId)
    {
        return $this->DbHandler->isRepresentingLegalEntities($repId);
    }

    public function addPercentRecord(array $data, $contractId)
    {
        return $this->DbHandler->addPercentRecord($data, $contractId);
    }

    /**
     * @throws TDbException
     */
    public function changeContractOwnerSignedDoc($criteries, $signed)
    {
        return $this->DbHandler->changeContractOwnerSignedDoc($criteries, $signed);
    }

    /**
     * @param int $ownerId
     * @param string $deadDate
     *
     * @throws TDbException
     */
    public function hasPersonalUse($ownerId, $deadDate)
    {
        return $this->DbHandler->getPersonalUseOfDeadOwner($ownerId, $deadDate);
    }

    /**
     * @param int $ownerId
     * @param string $deadDate
     *
     * @throws TDbException
     */
    public function getPreviousFarmYearsForOwner($ownerId, $yearId)
    {
        return $this->DbHandler->getPreviousFarmYearsForOwner($ownerId, $yearId);
    }

    public function getParentPlotOwnerRel($parentPathId)
    {
        return $this->DbHandler->getParentPlotOwnerRel($parentPathId);
    }

    public function buildHeritorsTree($path, $rat_ownage, $numerator, $denominator, $level, $parentFakeId = null, $relationId, $write = false)
    {
        ++$level;
        $options = [
            'return' => [
                'CAST(CAST(h.owner_id AS text)||CAST(h.id AS text) AS numeric(24,0)) as fakeid', 'h.id', "o.name || ' ' || o.surname || ' ' || o.lastname as owner_names",
                'h.owner_id', 'o.is_dead', 'h.path as path',
                'poi.percent as percent',
                'poi.numerator as numerator',
                'poi.denominator as denominator',
                'poi.is_set_manual as manual',
                'pc.contract_area',
                'o.is_dead as dead',
                'poi.id as id',
                "TRIM(r.rep_name) || ' ' || TRIM(r.rep_surname) || ' ' || TRIM(r.rep_lastname) as rep_names",
                'r.rep_egn as rep_egn',
                'r.id as rep_id',
                'poi.is_signer as is_signer',
            ],
            'where' => [
                'path' => ['column' => 'h.path', 'compare' => '~', 'value' => $path],
            ],
            'onCondition' => 'AND poi.pc_rel_id = ' . $relationId,
        ];

        $results = $this->getOwnersHeritors($options, false, false);

        if (empty($results)) {
            return [];
        }

        if ($write) {
            $sum_custom_ownage = 0;
            $heritors = 0;
            for ($i = 0; $i < $c = count($results); $i++) {
                if (null != $results[$i]['percent'] && $results[$i]['manual']) {
                    $sum_custom_ownage += $results[$i]['percent'];
                } else {
                    $heritors++;
                }
            }
        }

        for ($i = 0; $i < count($results); $i++) {
            if ($write && (null == $results[$i]['percent'] || !$results[$i]['manual'])) {
                $results[$i]['percent'] = ($rat_ownage - $sum_custom_ownage) / $heritors;
                $results[$i]['percent'] = ($results[$i]['percent'] > 0) ? $results[$i]['percent'] : 0;

                $this->addPercentRecord($results[$i], $relationId);
            }

            if ($results[$i]['is_dead']) {
                $results[$i]['children'] = $this
                    ->buildHeritorsTree(
                        $results[$i]['path'] . '.*{1}',
                        $results[$i]['percent'],
                        $results[$i]['numerator'],
                        $results[$i]['denominator'],
                        $level,
                        $results[$i]['fakeid'],
                        $relationId,
                        $write
                    );
                $results[$i]['iconCls'] = 'icon-tree-user-rip';
            } else {
                $results[$i]['iconCls'] = 'icon-tree-user';
            }

            $results[$i]['_parentId'] = $parentFakeId;

            $results[$i]['is_heritor'] = true;
            $results[$i]['level'] = $level;
        }

        return $results;
    }

    public function createPlotsOwnersRelation($parent)
    {
        $parentPathId = explode('.', $parent)[0];
        $parentPath = '*.' . $parentPathId . '.*';

        $parentInheritedPlotsRel = $this->getParentPath($parentPath);
        $parentOwnedPlotsRels = $this->getParentPlotOwnerRel($parentPathId);
        $plotRels = array_merge($parentInheritedPlotsRel, $parentOwnedPlotsRels);

        foreach ($plotRels as $parentPcRelId) {
            $pcRelIds[] = $parentPcRelId['pc_rel_id'];
        }

        if (empty($pcRelIds)) {
            return;
        }

        $plotsOwners = $this->getPlotsOwnres($pcRelIds);

        foreach ($plotsOwners as $plotsOwner) {
            $this->buildHeritorsTree(
                $plotsOwner['owner_id'] . '.*{1}',
                $plotsOwner['percent'],
                $plotsOwner['numerator'],
                $plotsOwner['denominator'],
                1,
                $plotsOwner['fakeid'],
                $plotsOwner['pc_rel_id'],
                true
            );
        }
    }

    public function getOwners(array $ownerIds)
    {
        return $this->DbHandler->getOwners($ownerIds);
    }

    public static function isDead(string $tableAlias, array $period = [], $includeRowAlias = true): string
    {
        list($periodStart, $periodEnd) = $period;

        $sql = "CASE
                    WHEN {$tableAlias}.is_dead = false THEN false
                    WHEN {$tableAlias}.is_dead = true and {$tableAlias}.dead_date is null THEN true
                    WHEN {$tableAlias}.is_dead = true AND {$tableAlias}.dead_date >= '" . $periodEnd . "' THEN false
                    WHEN {$tableAlias}.is_dead = true AND {$tableAlias}.dead_date < '" . $periodEnd . "' THEN true
                    else {$tableAlias}.is_dead
                END
        ";

        if (true == $includeRowAlias) {
            $sql .= ' as is_dead';
        }

        return $sql;
    }

    public static function allowOwnerPayment(string $tableAlias, array $period = [], $includeRowAlias = true): string
    {
        list($periodStart, $periodEnd) = $period;

        $sql = "CASE
                    WHEN {$tableAlias}.is_dead = false THEN true
                    WHEN {$tableAlias}.is_dead = true and {$tableAlias}.dead_date is null THEN false
                    WHEN {$tableAlias}.is_dead = true AND {$tableAlias}.dead_date BETWEEN '" . $periodStart . "' AND '" . $periodEnd . "' THEN true
                    WHEN {$tableAlias}.is_dead = true AND {$tableAlias}.dead_date > '" . $periodStart . "' THEN true
                    ELSE false
                END
        ";

        if (true == $includeRowAlias) {
            $sql .= ' as allow_owner_payment';
        }

        return $sql;
    }

    public static function isDeadDateInCurrentFarmYear(string $tableAlias, array $period = []): string
    {
        list($periodStart, $periodEnd) = $period;

        return "CASE
                    WHEN {$tableAlias}.is_dead = false THEN true
                    WHEN {$tableAlias}.is_dead = true AND {$tableAlias}.dead_date BETWEEN '" . $periodStart . "' AND '" . $periodEnd . "' THEN true
                    ELSE false
                END as dead_date_in_current_farm_year
        ";
    }

    public function getAllOwnersSortedAlphabetically()
    {
        return $this->DbHandler->getAllOwnersSortedAlphabetically();
    }
}
