<?php

namespace TF\Engine\Plugins\Core\UserDbMap;

use Prado\Exceptions\TDbException;
use TF\Application\Common\Config;
use TF\Application\Entity\UserLayers;
use TF\Engine\Plugins\Core\UserDb\UserDbModel;

class UserDbMapModel extends UserDbModel
{
    public function getIntersection($options, $counter, $returnOnlySQL, $getDeleteData = false)
    {
        $return = $this->createReturnVariable($options['return'], $counter);
        $idsA = implode(',', $options['where']['gidsA']['value']);
        $idsB = implode(',', $options['where']['gidsB']['value']);
        $tableA = $options['tablename1'];
        $tableB = $options['tablename2'];
        $cteQuery = '';
        $joinOnClause = 'ST_Intersects(t1.geom, t2.geom)';
        if (!$idsB) {
            $cteQuery = "WITH {$tableA}_filtered AS (
                SELECT
                    gid, geom, st_extent (geom) OVER () layer_extent
                FROM
                    {$tableA}
                WHERE
                    gid IN ({$idsA})
            )";
            $tableA = "{$tableA}_filtered";
            $joinOnClause = 't2.geom && t1.layer_extent AND ST_Intersects(t1.geom, t2.geom)';
        }
        $sql = $cteQuery;

        if ($getDeleteData) {
            $sql .= "SELECT {$return} FROM {$tableA}  as t1
                INNER JOIN {$tableB} t2 ON {$joinOnClause}
                WHERE true";
        } else {
            $sql .= "SELECT {$return} FROM {$tableA}  as t1
                LEFT JOIN {$tableB} t2 ON {$joinOnClause}
                WHERE true";
        }

        $options['group'] = 't1.geom, t1.gid';

        return $this->commonSqlResult($sql, $options, $counter, $returnOnlySQL);
    }

    public function getDataBeforeProcess($tableA, $aProcessedGidA, $idNameA, $layerId = null)
    {
        $columnsStr = '*';

        if ($layerId) {
            $layer = UserLayers::getLayerById($layerId);
            $definitions = $layer->getDefinitions();
            $definitions = UserLayers::filterDefinitions($definitions, [['col_virtual' => false]]);
            $columns = UserLayers::getColumns($definitions);
            $columnsStr = implode(',', array_map(function ($value) {
                return "\"{$value}\"";
            }, $columns));
        }

        $options = [
            'return' => [
                $columnsStr,
            ],
            'tablename' => $tableA,
            'where' => [
                'gidsA' => ['column' => $idNameA, 'compare' => 'IN', 'value' => $aProcessedGidA],
            ],
        ];

        return $this->getItemsByParams($options, false, false);
    }

    public function getIntersectionWithDbLink($options, $getDeleteData = false)
    {
        $return1 = $options['return'][0];
        $return2 = $options['return'][1];

        $columnNameA = $options['where']['gidsA']['column'];
        $columnNameB = $options['where']['gidsB']['column'];

        $idsA = implode(',', $options['where']['gidsA']['value']);
        $idsB = implode(',', $options['where']['gidsB']['value']);
        $ntps = isset($options['where']['ntp_k']) ? implode(',', array_map(function ($value) { return "''" . $value . "''"; }, $options['where']['ntp_k']['value'])) : [];

        $tableA = $options['tablename1'];
        $tableB = $options['tablename2'];
        $cteQuery = '';
        $dbLinkDSN = 'host=' . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . '';
        $dbLinkWhere = "'SELECT geom FROM {$tableB} WHERE {$columnNameB} IN ({$idsB})'";

        if (count($ntps)) {
            $dbLinkWhere = "'SELECT geom FROM {$tableB} WHERE {$columnNameB} IN ({$idsB}) AND ntp_k IN ({$ntps})'";
        }

        if (!$idsB) {
            $cteQuery = "WITH {$tableA}_filtered AS (
                SELECT
                    gid, geom, st_extent (geom) OVER () layer_extent
                FROM
                    {$tableA}
                WHERE
                    gid IN ({$idsA})
            )";
            $dbLinkWhere = "format ('SELECT geom FROM {$tableB} WHERE geom && ST_GeomFromeWKT(%L)', ST_AsText(t1.layer_extent))";

            if (count($ntps)) {
                $dbLinkWhere = "format ('SELECT geom FROM {$tableB} WHERE ntp_k IN ({$ntps}) and geom && ST_GeomFromeWKT(%L)', ST_AsText(t1.layer_extent))";
            }
            $tableA = "{$tableA}_filtered";
        }
        $sql = $cteQuery;
        if ($getDeleteData) {
            $sql .= "SELECT {$return1}, {$return2} FROM {$tableA} AS t1
                    INNER JOIN dblink('{$dbLinkDSN}', {$dbLinkWhere}) AS t2(geom geometry)
                    ON (ST_Intersects(t1.geom, t2.geom))
                    WHERE t1.{$columnNameA} IN ({$idsA}) AND t2.geom notnull
                    GROUP BY t1.{$columnNameA}, t1.geom";
        } else {
            $sql .= "SELECT {$return1}, {$return2} FROM {$tableA} AS t1
                    LEFT JOIN dblink('{$dbLinkDSN}', {$dbLinkWhere}) AS t2(geom geometry)
                    ON (ST_Intersects(t1.geom, t2.geom))
                    WHERE t1.{$columnNameA} IN ({$idsA})
                    GROUP BY t1.{$columnNameA}, t1.geom";
        }

        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function getCutData($tableA, $tableB, $data, $idNameA, $idNameB)
    {
        $options = [
            'return' => [
                "st_astext(
                    CASE
                        WHEN st_geometrytype (
                            ST_MakeValid(
                                ST_Intersection (
                                    ST_MakeValid(t1.geom),
                                    ST_MakeValid(ST_union(ST_Buffer(ST_SnapToGrid(t2.geom, 0.5), 0.0001)))
                                )
                            )
                        ) = 'ST_GeometryCollection' THEN
                            ST_CollectionExtract (
                                ST_MakeValid(
                                    ST_Intersection (
                                        ST_MakeValid(t1.geom),
                                        ST_MakeValid(ST_union(ST_Buffer(ST_SnapToGrid(t2.geom, 0.5), 0.0001)))
                                    )
                                ),
                                3
                            )
                        ELSE
                            ST_Intersection (
                                ST_MakeValid (t1.geom),
                                ST_MakeValid(ST_union(ST_Buffer(ST_SnapToGrid(t2.geom, 0.5), 0.0001)))
                            )
                    END
                ) geometry",
                't1.' . $idNameA,
                'ST_Contains(st_buffer(ST_union(ST_SnapToGrid(t2.geom, 0.5)), 0.1), t1.geom) is_contains',
                'ST_NumGeometries(ST_Intersection(ST_MakeValid(t1.geom)',
                'ST_MakeValid(ST_union(ST_SnapToGrid(t2.geom, 0.5))))) as geometries_num',
            ],
            'tablename1' => $tableA,
            'tablename2' => $tableB,
            'where' => [
                'gidsA' => ['column' => $idNameA, 'compare' => 'IN', 'prefix' => 't1', 'value' => array_filter($data->featuresGidA, function ($value) {
                    return !empty($value);
                })],
                'gidsB' => ['column' => $idNameB, 'compare' => 'IN', 'prefix' => 't2', 'value' => array_filter($data->featuresGidB, function ($value) {
                    return !empty($value);
                })],
            ],
        ];

        if (isset($data->ntpIds) && count($data->ntpIds)) {
            $options['where']['ntp_k'] = ['column' => 'ntp_k', 'compare' => 'IN', 'prefix' => 't2', 'value' => $data->ntpIds];
        }

        if (in_array($tableB, $GLOBALS['Layers']['systemTables'])) {
            $results = array_map(function ($item) {
                return (object) $item;
            }, $this->getIntersectionWithDbLink($options));
        } else {
            $results = array_map(function ($item) {
                return (object) $item;
            }, $this->getIntersection($options, false, false));
        }

        $results = array_filter(
            $results,
            function ($v) {
                return 'GEOMETRYCOLLECTION EMPTY' != $v->geometry;
            }
        );

        return $results;
    }

    public function getDeleteData($tableA, $tableB, $data, $idNameA, $idNameB)
    {
        $options = [
            'return' => [
                'ST_AsText(ST_Difference(ST_MakeValid(t1.geom), ST_union(ST_MakeValid(t2.geom)))) as geometry', 't1.' . $idNameA . ', ST_Contains(st_buffer(ST_union(t2.geom), 0.1), t1.geom) as is_contains, ST_NumGeometries(ST_Difference(ST_MakeValid(t1.geom), ST_MakeValid(ST_union(t2.geom)))) as geometries_num',
            ],
            'tablename1' => $tableA,
            'tablename2' => $tableB,
            'where' => [
                'gidsA' => ['column' => $idNameA, 'compare' => 'IN', 'prefix' => 't1', 'value' => array_filter($data->featuresGidA, function ($value) {
                    return !empty($value);
                })],
                'gidsB' => ['column' => $idNameB, 'compare' => 'IN', 'prefix' => 't2', 'value' => array_filter($data->featuresGidB, function ($value) {
                    return !empty($value);
                })],
            ],
            'group' => 't1.' . $idNameA . ', t1.geom',
        ];

        if (in_array($tableB, $GLOBALS['Layers']['systemTables'])) {
            $results = array_map(function ($item) {
                return (object) $item;
            }, $this->getIntersectionWithDbLink($options, true));
        } else {
            $results = array_map(function ($item) {
                return (object) $item;
            }, $this->getIntersection($options, false, false, true));
        }

        return $results;
    }

    public function processMultiPolygons($tableA, $idNameA, $layerTypeA, $layerId)
    {
        $aMultiPolygons = $this->getMultiPolygons($tableA, $idNameA);

        if (count($aMultiPolygons)) {
            $sMultiPolygons = '';
            for ($i = 0; $i < count($aMultiPolygons); $i++) {
                $sMultiPolygons .= $aMultiPolygons[$i][$idNameA] . ',';
            }

            if (Config::LAYER_TYPE_FOR_ISAK == $layerTypeA) {
                $this->insertSinglePolygonsForIsak($tableA);
                $sMultiPolygons = substr($sMultiPolygons, 0, -1);
                $this->removeMultiPolygons($tableA, $idNameA, $sMultiPolygons);
            } elseif (Config::LAYER_TYPE_ZP == $layerTypeA) {
                $sMultiPolygons = substr($sMultiPolygons, 0, -1);
                $idsToBeRemoved = $this->insertSinglePolygonsZP($tableA, $sMultiPolygons);
                if (!empty($idsToBeRemoved)) {
                    $idsToBeRemoved = implode(',', $idsToBeRemoved);
                    $this->removeMultiPolygons($tableA, $idNameA, $idsToBeRemoved);
                }
            } else {
                $this->insertSinglePolygons($tableA, $layerId);
                $sMultiPolygons = substr($sMultiPolygons, 0, -1);
                $this->removeMultiPolygons($tableA, $idNameA, $sMultiPolygons);
            }
        }
    }

    public function insertSinglePolygonsForIsak($tableA)
    {
        $sql = "INSERT INTO {$tableA} (
                    prc_name,
                    geom,  
                    prc_uin, 
                    cropcode, 
                    schema, 
                    area, 
                    ekatte, 
                    edited, 
                    comment, 
                    crop_type, 
                    crop_genus, 
                    azot_fixed_crop, 
                    is_intermediate_crop, 
                    is_intermediate_weat_crop, 
                    is_tree_short_rotation, 
                    no_pndn, 
                    common_cultures, 
                    pndp, 
                    sepp, 
                    zdp, 
                    nr1, 
                    nr2, 
                    crop_short_type, 
                    cropname, 
                    green_area_factor
                )
                with dump_geom as (
                    select
                        gid,
                        prc_name as prc_name_raw,
                        ST_MakeValid(st_multi((st_dump(geom)).geom)) as geom,
                        prc_uin,
                        cropcode,
                        schema,
                        round((ST_Area(st_multi((st_dump(geom)).geom))/1000)::numeric, 3) AS area,
                        ekatte,
                        edited,
                        comment,
                        crop_type,
                        crop_genus,
                        azot_fixed_crop,
                        is_intermediate_crop,
                        is_intermediate_weat_crop,
                        is_tree_short_rotation,
                        no_pndn,
                        common_cultures,
                        pndp,
                        sepp,
                        zdp,
                        nr1,
                        nr2,
                        crop_short_type,
                        cropname,
                        green_area_factor
                    from {$tableA} 
                    where st_geometrytype(geom) = 'ST_MultiPolygon'
                )
                select
                    (
                        case 
                            when COUNT(*) OVER (PARTITION BY gid) > 1 then prc_name_raw || '-' || ROW_NUMBER() OVER (PARTITION BY gid ORDER BY gid) 
                            else prc_name_raw 
                        end
                    ) as prc_name,
                    geom,  
                    prc_uin, 
                    cropcode, 
                    schema, 
                    area, 
                    ekatte, 
                    edited, 
                    comment, 
                    crop_type, 
                    crop_genus, 
                    azot_fixed_crop, 
                    is_intermediate_crop, 
                    is_intermediate_weat_crop, 
                    is_tree_short_rotation, 
                    no_pndn, 
                    common_cultures, 
                    pndp, 
                    sepp, 
                    zdp, 
                    nr1, 
                    nr2, 
                    crop_short_type, 
                    cropname, 
                    green_area_factor
                from dump_geom;";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function insertSinglePolygonsZP($tableA, $sMultiPolygons)
    {
        $ZPplots = explode(',', $sMultiPolygons);
        $plotsToBeDeleted = [];
        for ($i = 0; $i < count($ZPplots); $i++) {
            $sql = "
                SELECT st_dump(geom)
                from {$tableA} where
                id = {$ZPplots[$i]}";

            $cmd = $this->DbModule->createCommand($sql);
            $results = $cmd->query()->readAll();

            // multipoligon can be 1 poligon or multiple poligons (see st_dump)
            if (count($results) > 1) {
                $sql = "insert into {$tableA} (
                    geom,
                    area_name,
                    isak_prc_uin,
                    polzvatel,
                    polivki,
                    napoqvane,
                    dobivi,
                    obrabotki,
                    culture,
                    ekatte
                    ) (
                    SELECT ST_MakeValid(st_multi((st_dump(geom)).geom)),
                    area_name,
                    isak_prc_uin,
                    polzvatel,
                    polivki,
                    napoqvane,
                    dobivi,
                    obrabotki,
                    culture,
                    ekatte
                    from {$tableA} where st_geometrytype(geom) = 'ST_MultiPolygon'
                    and id = {$ZPplots[$i]}
                    )";
                $cmd = $this->DbModule->createCommand($sql);
                $cmd->execute();
                $plotsToBeDeleted[] = $ZPplots[$i];
            }
        }

        return $plotsToBeDeleted;
    }

    /**
     * @throws TDbException
     */
    public function insertSinglePolygons($tableA, $layerId)
    {
        $formattedArr = [];
        $result = [];

        $layer = UserLayers::getLayerById($layerId);
        $definitions = $layer->getDefinitions();
        $definitions = UserLayers::filterDefinitions($definitions, [['col_virtual' => false]]);
        $columns = UserLayers::getColumns($definitions);
        $columnsStr = implode(',', array_map(function ($value) {
            return "\"{$value}\"";
        }, $columns));

        $sql = "SELECT {$columnsStr}, ST_AsText((st_dump(geom)).geom) as geometry from {$tableA} where st_geometrytype(geom) = 'ST_MultiPolygon'";
        $cmd = $this->DbModule->createCommand($sql);
        $areas = $cmd->query()->readAll();

        foreach ($areas as $area) {
            $id = $area['gid'];
            $area['geom'] = $area['geometry'];
            unset($area['gid'], $area['geometry']);
            $formattedArr[$id][] = $area;
        }

        foreach ($formattedArr as $elem) {
            if (1 === count($elem)) {
                $result[] = $elem[0];

                continue;
            }

            foreach ($elem as $key => $value) {
                if (is_array($value) && array_key_exists('name', $value)) {
                    $value['name'] = $value['name'] . '-' . $key;
                } elseif (is_array($value) && array_key_exists('plot_name', $value)) {
                    $value['plot_name'] = $value['plot_name'] . '-' . $key;
                }
                $result[] = $value;
            }
        }

        if (!empty($result)) {
            $this->addLayers($tableA, $result);
        }
    }

    public function getMultiPolygons($tableA, $idNameA)
    {
        $sql = "SELECT {$idNameA} FROM {$tableA}
        		WHERE  st_geometrytype(geom) = 'ST_MultiPolygon'";

        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function removeMultiPolygons($tableA, $idNameA, $sMultiPolygons)
    {
        $sql = "DELETE FROM {$tableA} WHERE {$idNameA} IN ({$sMultiPolygons})";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function removeSmallAndInvalidPolygons($tableA, $idNameA)
    {
        $sql = "DELETE FROM {$tableA}
                WHERE {$idNameA} IN (SELECT {$idNameA} FROM {$tableA} WHERE (ST_Area(geom)/1000) <= 0.01)";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();

        $sql = "DELETE FROM {$tableA}
                WHERE {$idNameA} IN (SELECT {$idNameA} FROM {$tableA} WHERE NOT ST_IsValid(geom) OR st_geometrytype(geom) = 'ST_Linestring' OR st_geometrytype(geom) = 'ST_Point')";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    public function clippingKVSByNTPAndUpdateAreas($ekate, $ntp)
    {
        // Do not update the plot area if intersect area is smaller than value below
        $minIntersectionArea = 50;
        $sql = "UPDATE layer_kvs kvs 
                SET allowable_area = ROUND((ST_Area(ST_Intersection(kvs.geom, a.geom)) / 1000)::DECIMAL,3)
                FROM dblink (
                  'host=" . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' dbname=' . DEFAULT_DB_DATABASE . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . "',
                  'SELECT ST_Union(geom) FROM layer_allowable_final lf WHERE (lf.ekatte_ = ''" . $ekate . "'' AND lf.ntp = ''" . $ntp . "'')' 
                ) AS a (geom geometry)
                WHERE ST_Area(ST_Intersection(kvs.geom, a.geom)) > " . $minIntersectionArea . '  AND kvs.ekate = :ekate';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':ekate', $ekate);

        return $cmd->execute();
    }

    public function clippingLayersAndGetIntersection(UserLayers $tableA, string $tableB, $return = [], $where = []): array
    {
        $ekatteColumn = $tableA->getDefinitionByCategory(Config::LAYER_COLUMN_CATEGORY_EKATTE);

        // Do not update the plot area if intersect area is smaller than value below
        $minIntersectionArea = 50;
        $returnString = '';

        if (!empty($return)) {
            $returnString = ', ' . implode(',', $return);
        }

        $sql = 'SELECT 
                    ROUND((ST_Area(ST_Intersection(tb.geom, ta.geom)) / 1000)::DECIMAL,3) AS intersec_area ' . $returnString . ' 
                FROM ' . $tableA->table_name . ' ta 
                INNER JOIN ' . $tableB . ' tb ON (';
        if ($ekatteColumn) {
            $sql .= 'ta.' . $ekatteColumn['col_name'] . ' = tb.ekate AND ';
        }
        $sql .= "ST_Area(ST_Intersection(tb.geom, ta.geom)) > {$minIntersectionArea})";
        $sql .= " WHERE get_kvs_plot_status(tb.is_edited, tb.edit_active_from) <> 'Archived'::kvs_plot_status_enum";

        return $this->DbModule->createCommand($sql)->query()->readAll();
    }
}
