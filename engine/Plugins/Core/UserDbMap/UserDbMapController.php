<?php

namespace TF\Engine\Plugins\Core\UserDbMap;

use TF\Application\Common\Config;
use TF\Application\Entity\UserLayers;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

class UserDbMapController extends UserDbController
{
    /** @var UserDbMapModel */
    public $DbHandler;

    /** @var string */
    public $Database;

    public function __construct($database)
    {
        $this->DbHandler = new UserDbMapModel($database);
        $this->Database = $database;
    }

    public function getIntersection($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getIntersection($options, $counter, $returnOnlySQL);
    }

    public function processCutClipping($tableA, $tableB, $data, $idNameA, $idNameB, $layerTypeA, $layerIdA)
    {
        $results = $this->DbHandler->getCutData($tableA, $tableB, $data, $idNameA, $idNameB);

        if (count($results) > 0) {
            $aProcessedGidA = [];
            for ($i = 0; $i < count($results); $i++) {
                $aProcessedGidA[] = $results[$i]->gid;
            }

            if (Config::LAYER_TYPE_FOR_ISAK == $layerTypeA) {
                $resultsAtrInfo = $this->DbHandler->getDataBeforeProcess($tableA, $aProcessedGidA, $idNameA);
                $this->addGeometryForIsakAtrInfo($tableA, $idNameA, $results, $resultsAtrInfo);
            } elseif (in_array($layerTypeA, [Config::LAYER_TYPE_WORK_LAYER, Config::LAYER_TYPE_GPS])) {
                $resultsAtrInfo = $this->DbHandler->getDataBeforeProcess($tableA, $aProcessedGidA, $idNameA, $layerIdA);
                $this->addLayers($tableA, $results, $resultsAtrInfo);
            } else {
                $this->addGeometry($tableA, $results);
            }

            $this->removeMultipleGeometries($tableA, $aProcessedGidA, $idNameA);
        }
    }

    public function processDeleteClipping($tableA, $tableB, $data, $idNameA, $idNameB, $layerTypeA, $layerIdA)
    {
        $prefix = '0';
        $results = $this->DbHandler->getDeleteData($tableA, $tableB, $data, $idNameA, $idNameB);

        if (count($results) > 0) {
            $aProcessedGidA = [];
            for ($i = 0; $i < count($results); $i++) {
                $aProcessedGidA[] = $results[$i]->gid;
            }

            if (Config::LAYER_TYPE_FOR_ISAK == $layerTypeA) {
                $resultsAtrInfo = $this->DbHandler->getDataBeforeProcess($tableA, $aProcessedGidA, $idNameA);

                $this->addGeometryForIsakAtrInfo($tableA, $idNameA, $results, $resultsAtrInfo, $prefix);
            } elseif (in_array($layerTypeA, [Config::LAYER_TYPE_WORK_LAYER, Config::LAYER_TYPE_GPS])) {
                $resultsAtrInfo = $this->DbHandler->getDataBeforeProcess($tableA, $aProcessedGidA, $idNameA, $layerIdA);
                $this->addLayers($tableA, $results, $resultsAtrInfo, $prefix);
            } else {
                $this->addGeometry($tableA, $results);
            }

            $this->removeMultipleGeometries($tableA, $aProcessedGidA, $idNameA);
        }
    }

    public function processSplitClipping($tableA, $tableB, $data, $idNameA, $idNameB, $layerTypeA, $layerIdA)
    {
        $results_cut = $this->DbHandler->getCutData($tableA, $tableB, $data, $idNameA, $idNameB);
        $results_delete = $this->DbHandler->getDeleteData($tableA, $tableB, $data, $idNameA, $idNameB);

        if (count($results_delete) > 0 && count($results_cut) > 0) {
            $aProcessedGidA_delete = [];
            for ($i = 0; $i < count($results_delete); $i++) {
                $aProcessedGidA_delete[] = $results_delete[$i]->gid;
            }

            $aProcessedGidA_cut = [];
            for ($i = 0; $i < count($results_cut); $i++) {
                $aProcessedGidA_cut[] = $results_cut[$i]->gid;
            }

            $aProcessedGidA = $aProcessedGidA_delete;

            if (Config::LAYER_TYPE_FOR_ISAK == $layerTypeA) {
                $resultsAtrInfo = $this->DbHandler->getDataBeforeProcess($tableA, $aProcessedGidA, $idNameA, $layerIdA);

                // for delete
                $this->addGeometryForIsakAtrInfo($tableA, $idNameA, $results_delete, $resultsAtrInfo, '0');

                // for cut
                $this->addGeometryForIsakAtrInfo($tableA, $idNameA, $results_cut, $resultsAtrInfo, '1');
            } elseif (in_array($layerTypeA, [Config::LAYER_TYPE_WORK_LAYER, Config::LAYER_TYPE_GPS])) {
                $resultsAtrInfo = $this->DbHandler->getDataBeforeProcess($tableA, $aProcessedGidA, $idNameA, $layerIdA);
                $this->addLayers($tableA, $results_cut, $resultsAtrInfo, '0', true);
                $this->addLayers($tableA, $results_delete, $resultsAtrInfo, '1', true);
            } else {
                $this->addGeometry($tableA, $results_delete);
                $this->addGeometry($tableA, $results_cut);
            }

            $this->removeMultipleGeometries($tableA, $aProcessedGidA, $idNameA);
        }
    }

    public function processMultiPolygons($tableA, $idNameA, $layerTypeA, $layerId)
    {
        $this->DbHandler->processMultiPolygons($tableA, $idNameA, $layerTypeA, $layerId);
    }

    public function removeSmallAndInvalidPolygons($tableA, $idNameA)
    {
        $this->DbHandler->removeSmallAndInvalidPolygons($tableA, $idNameA);
    }

    public function clippingKVSByNTPAndUpdateAreas($ekate, $ntp)
    {
        return $this->DbHandler->clippingKVSByNTPAndUpdateAreas($ekate, $ntp);
    }

    /**
     * Summary of clippingLayerAndGetIntersection.
     */
    public function clippingLayerAndGetIntersection(UserLayers $layerA, string $tableB, $return = [], $where = []): array
    {
        return $this->DbHandler->clippingLayersAndGetIntersection($layerA, $tableB, $return, $where);
    }
}
