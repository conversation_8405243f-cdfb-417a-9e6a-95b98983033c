<?php

namespace TF\Engine\Plugins\Core\UserDbSubleases;

use PDO;
use Prado\Prado;
use TF\Engine\Plugins\Core\UserDb\UserDbModel;

class UserDbSubleasesModel extends UserDbModel
{
    public const CONTRACT_STATUS_TERMINATED = 1;
    public const CONTRACT_STATUS_ACTIVE = 2;
    public const CONTRACT_STATUS_EXPIRED = 3;
    public const CONTRACT_STATUS_NOT_CANCELED = 4;

    public function getSubleasesContragentsData($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM {$this->tableContractsContragents} cc";
        $sql .= " LEFT JOIN {$this->tableOwners} o ON(cc.owner_id = o.id)";
        $sql .= " LEFT JOIN {$this->tableOwnersReps} r ON(r.id = cc.rep_id)";
        $sql .= 'WHERE true ';
        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($returnOnlySQL) {
            return $sql;
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getSubleasesFarmingContragentsData($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM {$this->tableContractsFarmingContragents} cc";
        $sql .= " LEFT JOIN {$this->tableOwnersReps} r ON(r.id = cc.rep_id)";
        $sql .= ' WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($returnOnlySQL) {
            return $sql;
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getSubleasePlotContracts($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = " SELECT {$return} FROM {$this->tableSubleasesPlotsContractsRel} spc"
                . " INNER JOIN {$this->contractsPlotsRelTable} pc ON(spc.pc_rel_id = pc.id)"
                . " INNER JOIN {$this->tableContracts} c ON(c.id = pc.contract_id)"
                . ' WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($returnOnlySQL) {
            return $sql;
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getSubleaseFullData($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = " SELECT {$return} FROM {$this->tableKVS} kvs"
                . " INNER JOIN {$this->contractsPlotsRelTable} pc ON(pc.plot_id = kvs.gid)"
                . " INNER JOIN {$this->tableSubleasesPlotsContractsRel} spc ON(spc.pc_rel_id = pc.id)"
                . " INNER JOIN {$this->tableContracts} c ON(c.id = spc.sublease_id)"
                . " INNER JOIN {$this->tableContracts} c1 ON(c1.id = pc.contract_id)"
                . " LEFT JOIN {$this->plotsOwnersRelTable} po ON(po.pc_rel_id = pc.id)"
                . ' WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ('' != $options['pc_rel_id_string']) {
            $sql .= " AND pc.id IN({$options['pc_rel_id_string']})";
        }
        if ('' != $options['contracts_id_string']) {
            $sql .= " AND c.id IN({$options['contracts_id_string']})";
        }
        if ('' != $options['plots_id_string']) {
            $sql .= " AND kvs.gid IN({$options['plots_id_string']})";
        }

        if ('' != $options['pc_rel_anti_id_string']) {
            $sql .= " AND pc.id NOT IN({$options['pc_rel_anti_id_string']})";
        }
        if ('' != $options['contracts_anti_id_string']) {
            $sql .= " AND c.id NOT IN({$options['contracts_anti_id_string']})";
        }
        if ('' != $options['plots_anti_id_string']) {
            $sql .= " AND kvs.gid NOT IN({$options['plots_anti_id_string']})";
        }

        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getSoldPlotsData($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter'] ?? false);

        $sql = " SELECT {$return} FROM {$this->tableSalesContracts} sc"
            . " INNER JOIN {$this->salesContractsPlotsRelTable} scpr ON (scpr.sales_contract_id = sc.id)"
            . ' WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($returnOnlySQL) {
            return $sql;
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getSubleasePlotsData($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter'] ?? false);

        $sql = " SELECT {$return} FROM {$this->tableKVS} kvs"
                . " INNER JOIN {$this->contractsPlotsRelTable} pc ON(pc.plot_id = kvs.gid)"
                . " INNER JOIN {$this->tableContracts} p ON (p.id = pc.contract_id)"
                . " INNER JOIN {$this->tableSubleasesPlotsContractsRel} spc ON(spc.pc_rel_id = pc.id)"
                . " INNER JOIN {$this->tableSubleasesPlotsArea} spa ON (spa.sublease_id = spc.sublease_id AND pc.plot_id = spa.plot_id)"
                . " INNER JOIN {$this->tableContracts} c ON(c.id = spc.sublease_id)"
                . " LEFT JOIN {$this->tableContracts} c2 ON(c2.id = p.id and c2.active = true)"
                . " INNER JOIN {$this->contractsPlotsRelTable} scpr ON(scpr.id = spc.pc_rel_id)"
                . " INNER JOIN {$this->tableContracts} mc ON(mc.id = scpr.contract_id and mc.active = true)"
                . ' WHERE  (is_edited = FALSE
                    AND (
                        edit_active_from ISNULL
                        OR edit_active_from < now()
                    )
                    or (
                        is_edited
                        AND edit_active_from >= now()
                    )) ';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['whereOr']) {
            $sql = $this->createWhereOrSQL($sql, $options['whereOr'], $returnOnlySQL);
        }

        if ($options['group'] && !$counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['having'] && !$counter) {
            $sql .= ' HAVING ' . $options['having'];
        }

        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        if ($options['whereOr']) {
            $this->createWhereBinds($cmd, $options['whereOr']);
        }

        return $cmd->query()->readAll();
    }

    public function getFullContractDataByFilter($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = "SELECT {$return} FROM {$this->tableContracts} c";
        $sql .= " LEFT JOIN {$this->contractsRentsRelTable} cr ON (cr.contract_id = c.id)";
        $sql .= " LEFT JOIN {$this->tableSubleasesPlotsContractsRel} spc ON (spc.sublease_id = c.id)";
        $sql .= " LEFT JOIN {$this->contractsPlotsRelTable} pc ON (pc.id = spc.pc_rel_id)";
        $sql .= " LEFT JOIN {$this->tableContracts} c1 ON (c1.id = pc.contract_id)";
        $sql .= " LEFT JOIN {$this->tableContractsContragents} cc ON (cc.contract_id = c.id)";
        $sql .= " LEFT JOIN {$this->tableOwners} o ON (o.id = cc.owner_id)";
        $sql .= " LEFT JOIN {$this->tableOwnersReps} o_r ON (o_r.id = cc.rep_id)";
        $sql .= " LEFT JOIN {$this->tableKVS} kvs ON(kvs.gid = pc.plot_id)";
        $sql .= ' WHERE true';

        if (!empty($options['where']['kad_ident']['value'])) {
            $sql .= ' AND (kvs.gid isnull or ((is_edited=false AND (edit_active_from < now() OR edit_active_from ISNULL)) OR (is_edited=true AND edit_active_from>=now())))';
        }

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['whereOr']) {
            $sql = $this->createWhereOrSQL($sql, $options['whereOr'], $returnOnlySQL);
        }

        if ($options['annex_query']) {
            $sql .= " OR c.id IN ({$options['annex_query']})";
        }

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        if ($options['whereOr']) {
            $this->createWhereBinds($cmd, $options['whereOr']);
        }

        return $cmd->query()->readAll();
    }

    public function hasSubleaseEditedPlots($params, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($params['return'], $counter);

        $sql = "SELECT {$return} FROM {$this->tableContracts} c
				LEFT JOIN su_subleases_plots_contracts_rel spc on (c.id = spc.sublease_id)
				INNER JOIN {$this->contractsPlotsRelTable} pc on (pc.id = spc.pc_rel_id)
				INNER JOIN {$this->tableKVS} kvs on (kvs.gid = pc.plot_id)
				WHERE true";

        if ($params['where']) {
            $sql = $this->createWhereSQL($sql, $params['where'], $returnOnlySQL);
        }

        if ($params && false == $counter) {
            if ($params['sort']) {
                $sql .= ' ORDER BY ' . $params['sort'] . ' ' . $params['order'];
            }
            if ($params['limit']) {
                $sql .= ' LIMIT ' . $params['limit'] . ' OFFSET ' . $params['offset'];
            }
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($params['where']) {
            $this->createWhereBinds($cmd, $params['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getCollectionsContractData($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);
        $whereSql = $this->createWhereSQL('', $options['where'], $returnOnlySQL);

        if ($options['whereOr']) {
            $whereSql = $this->createWhereOrSQL($whereSql, $options['whereOr'], $returnOnlySQL);
        }

        $user = Prado::getApplication()->getUser();
        $groupId = $user->GroupID;

        $collections_search_year = !empty($options['collections_search_year']) ? ' AND coll.farming_year = ' . $options['collections_search_year'] : '';

        $sql = 'WITH ';
        $sql .= "su_users_farming as (
                    SELECT 
                        id,
                        name,
                        company,
                        address,
                        company_address,
                        mol,
                        mol_egn,
                        iban_arr                       
                    FROM 
                        dblink('dbname=" . DEFAULT_DB_DATABASE . ' host=' . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . "',
                            'SELECT 
                                id,
                                name,
                                company,
                                address,
                                company_address,
                                mol,
                                mol_egn,
                                iban_arr
                            FROM 
                                su_users_farming
                            WHERE
                                group_id={$groupId}') as t(
                                                            id int,
                                                            name text,
                                                            company text,
                                                            address text,
                                                            company_address text,
                                                            mol text,
                                                            mol_egn text,
                                                            iban_arr json
                                                           )
                ),";
        $sql .= "subleases_by_contract AS (
            SELECT
                sv.sublease_contract_id,
                sv.sublease_c_num,
                sv.sublease_start_date,
                sv.sublease_due_date,
                sv.payday,
                sv.nm_usage_rights,
                SUM (sv.sublease_contract_area)::numeric(10, 3) sublease_contract_area,           
                SUM (sv.rent_area)::numeric(10, 3) rent_area,
                string_agg (
                    DISTINCT (
                        coalesce(
                        CASE
                        WHEN o.owner_type = 1 THEN
                            concat_ws (
                                ' ',
                                trim(o. NAME),
                                trim(o.surname),
                                trim(o.lastname)
                            )
                        ELSE
                            o.company_name
                        END, uf.name)
                    ),
                    ',<br />'
                ) subleaser_name,
                string_agg(distinct uf.name, '') FILTER (WHERE uf.name NOTNULL) farm_name,
                max(cfc.farming_id) farming_id,
                max (sv.renta)::numeric(10, 3) renta,
                bool_and(sv.active) active,                
                max(o.id) owner_id,
                string_agg(distinct o.iban, '') FILTER (WHERE o.iban NOTNULL) owner_iban,
                string_agg(distinct o.bic, '') FILTER (WHERE o.bic NOTNULL) owner_bic,
                string_agg(distinct o.bank_name, '') FILTER (WHERE o.bank_name NOTNULL) owner_bank_name,
                max(uf2.id) subleasing_farm_id,
                string_agg(distinct uf2.name, '') FILTER (WHERE uf2.name NOTNULL) subleasing_farm_name,
                string_agg(distinct uf2.company, '') FILTER (WHERE uf2.company NOTNULL) subleasing_farm_company,
                string_agg(distinct uf2.address, '') FILTER (WHERE uf2.address NOTNULL) subleasing_farm_address,
                string_agg(distinct uf2.company_address, '') FILTER (WHERE uf2.company_address NOTNULL) subleasing_farm_company_address,
                string_agg(distinct uf2.mol, '') FILTER (WHERE uf2.mol NOTNULL) subleasing_farm_mol,
                max(uf2.mol_egn) subleasing_farm_mol_egn,            
                json_agg(uf2.iban_arr) subleasing_farm_iban_arr            
            FROM
                subleases_view sv
            LEFT JOIN {$this->tableKVS} kvs ON sv.plot_id = kvs.gid
            LEFT JOIN {$this->tableContractsContragents} cc ON cc.contract_id = sv.sublease_contract_id
            LEFT JOIN {$this->tableContractsFarmingContragents} cfc on cfc.contract_id = sv.sublease_contract_id
            LEFT JOIN su_users_farming uf on uf.id = cfc.farming_id
            LEFT join su_users_farming uf2 on uf2.id = sv.farming_id
	        LEFT JOIN {$this->tableOwnersReps} orep on orep.id = cfc.rep_id
            LEFT JOIN {$this->tableOwners} o ON o.id = cc.owner_id
            LEFT JOIN {$this->contractsRentsRelTable} cr on cr.contract_id = sv.sublease_contract_id
            WHERE true
            AND ((is_edited=false AND (edit_active_from < now() OR edit_active_from ISNULL)) OR (is_edited=true AND edit_active_from>=now()))
            {$whereSql}
            GROUP BY
                sv.sublease_contract_id,
                sv.sublease_c_num,
                sv.sublease_start_date,
                sv.sublease_due_date,
                sv.payday,
                sv.nm_usage_rights
        ),
        collections_data as (
        SELECT
            sbc.sublease_contract_id,
            sbc.sublease_c_num,
            sbc.sublease_start_date,
            sbc.sublease_due_date,
            sbc.sublease_contract_area,
            COALESCE(sbc.rent_area, sbc.sublease_contract_area) rent_area,
            sbc.subleaser_name,
            sbc.payday,
            sbc.nm_usage_rights,
            max(sbc.renta) renta_dka,
            (max(sbc.renta) * COALESCE(sbc.rent_area, sbc.sublease_contract_area))::NUMERIC(10, 2) money_to_collect,
            sum(coll.amount) collected_money,
            ((max(sbc.renta) * COALESCE(sbc.rent_area, sbc.sublease_contract_area)) - COALESCE(sum(coll.amount), 0))::NUMERIC(10, 2) unpaid,
            bool_and(sbc.active) active,
            max(sbc.owner_id) owner_id,
            max(sbc.farming_id) farming_id,
            max(sbc.subleasing_farm_id) as subleasing_farm_id,
            string_agg(DISTINCT sbc.owner_iban, ',') owner_iban,
            string_agg(DISTINCT sbc.owner_bic, ',') owner_bic,
            string_agg(DISTINCT sbc.owner_bank_name, ',') owner_bank_name,              
            string_agg(DISTINCT sbc.subleasing_farm_name, ',') subleasing_farm_name,
            string_agg(DISTINCT sbc.subleasing_farm_company, ',') subleasing_farm_company,
            string_agg(DISTINCT sbc.subleasing_farm_address, ',') subleasing_farm_address,
            string_agg(DISTINCT sbc.subleasing_farm_company_address, ',') subleasing_farm_company_address,
            string_agg(DISTINCT sbc.subleasing_farm_mol, ',') subleasing_farm_mol,   
            max(sbc.subleasing_farm_mol_egn) subleasing_farm_mol_egn,            
            json_agg(sbc.subleasing_farm_iban_arr) subleasing_farm_iban_arr         
        FROM subleases_by_contract sbc
        LEFT JOIN su_collections coll ON sbc.sublease_contract_id = coll.contract_id {$collections_search_year}
        WHERE true
        GROUP BY
            sbc.sublease_c_num,
            sbc.sublease_contract_id,
            sbc.sublease_start_date,
            sbc.sublease_due_date,
            sbc.sublease_contract_area,
            sbc.rent_area,
            sbc.subleaser_name,
            sbc.payday,
            sbc.nm_usage_rights
        )
        SELECT
            {$return}
        FROM
            collections_data
        ";

        if ($options['order'] && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        if ($options['whereOr']) {
            $this->createWhereBinds($cmd, $options['whereOr']);
        }

        return $cmd->query()->readAll();
    }

    public function getEkateNamesForSubleaseContract($subleaseID)
    {
        $sql = "
    		SELECT
				distinct( ec.ekatte_name || ' (' || kvs.ekate || ')') as zemlishte_ekatte,
				kvs.ekate
			FROM
				su_subleases_plots_area spa
			LEFT JOIN layer_kvs kvs ON kvs.gid = spa.plot_id
			LEFT JOIN ekate_combobox ec on ec.ekate = kvs.ekate
			WHERE spa.sublease_id = :sublease_id
			GROUP BY
				kvs.ekate,
				ec.ekatte_name
			ORDER BY
				kvs.ekate
    	";

        $cmd = $this->DbModule->createCommand($sql);

        $cmd->bindParameter(':sublease_id', $subleaseID);

        return $cmd->query()->readAll();
    }
}
