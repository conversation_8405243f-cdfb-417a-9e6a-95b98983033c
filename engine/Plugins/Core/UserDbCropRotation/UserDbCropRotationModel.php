<?php

namespace TF\Engine\Plugins\Core\UserDbCropRotation;

use PDO;
use TF\Engine\Plugins\Core\UserDb\UserDbModel;

class UserDbCropRotationModel extends UserDbModel
{
    public function getCropLayerDataSoilSamples($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM {$this->cropLayersDataTable} cld
					INNER JOIN {$this->tableSoilSamples} ss ON (cld.id = ss.crop_layer_data_id)
						WHERE true";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getCroprotationOverlapReport($zpTableName, $isakTableName, $options, $counter)
    {
        $tablename = $this->cropLayersDataTable;

        if ($counter) {
            $return = 'COUNT(*)';
        } elseif ($options['return']) {
            $return = implode(', ', $options['return']);
        } else {
            $return = ' c.isak_prc_uin as isak_number, a.area,
                        ST_AREA(ST_INTERSECTION(c.geom, b.geom)) as intersect_area,
                        ST_AREA(ST_INTERSECTION(c.geom, b.geom))/ST_AREA(b.geom)*100 as intersect_percent,
                        b.prc_uin,
                        ST_AREA(c.geom) as zp_area,
						ST_AREA(b.geom) as isak_area,
                        b.gid as isak_gid,
						c.id as zp_id';
        }

        $sql = "SELECT {$return} FROM {$tablename} a
					LEFT JOIN " . $isakTableName . ' b ON (a.isak_number = b.prc_uin)
					LEFT JOIN ' . $zpTableName . ' c ON (ST_Intersects(c.geom, b.geom) AND ST_AREA(ST_INTERSECTION(c.geom, b.geom)) > 10) WHERE true
						AND a.has_number = TRUE
						AND a.crop_layer_id = :cid';

        if ($options && false == $counter) {
            if ($options['sort']) {
                $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
            }
            if (isset($options['limit'], $options['offset'])) {
                $sql .= ' LIMIT :limit OFFSET :offset';
            }
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        $cmd->bindParameter(':cid', $options['crop_layer_id']);

        return $cmd->query()->readAll();
    }

    public function getCultureMatchReport($zpTableName, $isakTableName, $field, $options, $counter)
    {
        $tablename = $this->cropLayersDataTable;

        if ($counter) {
            $return = 'COUNT(*)';
        } elseif ($options['return']) {
            $return = implode(', ', $options['return']);
        } else {
            $return = " a.isak_number, a.area, a.{$field} as culture,
						c.isak_prc_uin,
						c.culture as zp_culture,
						c.id";
        }

        $sql = "SELECT {$return} FROM {$tablename} a
					INNER JOIN " . $isakTableName . ' b ON (a.isak_number = b.prc_uin)
					INNER JOIN ' . $zpTableName . ' c ON ST_Intersects(b.geom, c.geom) WHERE true
						AND a.has_number = TRUE
						AND ST_AREA(ST_INTERSECTION(c.geom, b.geom)) > 90
						AND a.crop_layer_id = :layer_id';

        if ($options && false == $counter) {
            if ($options['sort']) {
                $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
            }
            if (isset($options['limit'], $options['offset'])) {
                $sql .= ' LIMIT :limit OFFSET :offset';
            }
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        $cmd->bindParameter(':layer_id', $options['layer_id']);

        return $cmd->query()->readAll();
    }

    public function getCropLayersItemData($id)
    {
        $sql = 'SELECT * FROM ' . $this->cropLayersTable . ' WHERE id = :id';

        $cmd = $this->DbModule->createCommand($sql);

        $cmd->bindParameter(':id', $id);

        $data = $cmd->query()->readAll();

        return $data[0];
    }

    public function getCropLayerIsakData($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM {$this->cropLayersDataTable} cld
					INNER JOIN {$options['tablename']} isak ON(isak.prc_uin = cld.isak_number AND has_number = TRUE)";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }
}
