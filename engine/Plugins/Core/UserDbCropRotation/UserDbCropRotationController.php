<?php

namespace TF\Engine\Plugins\Core\UserDbCropRotation;

use TF\Engine\Plugins\Core\UserDb\UserDbController;

class UserDbCropRotationController extends UserDbController
{
    public $DbHandler;
    public $Database;

    public function __construct($database)
    {
        $this->DbHandler = new UserDbCropRotationModel($database);
        $this->Database = $database;
    }

    public function getCropLayerDataSoilSamples($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getCropLayerDataSoilSamples($options, $counter, $returnOnlySQL);
    }

    public function getCroprotationOverlapReport($zpTableName, $isakTableName, $options, $counter = false)
    {
        return $this->DbHandler->getCroprotationOverlapReport($zpTableName, $isakTableName, $options, $counter);
    }

    public function getCultureMatchReport($zpTableName, $isakTableName, $index, $options, $counter = false)
    {
        if (0 == $index) {
            $field = 'culture1';
        } elseif (1 == $index) {
            $field = 'culture2';
        } elseif (2 == $index) {
            $field = 'culture3';
        } elseif (3 == $index) {
            $field = 'culture4';
        } elseif (4 == $index) {
            $field = 'culture5';
        }

        return $this->DbHandler->getCultureMatchReport($zpTableName, $isakTableName, $field, $options, $counter);
    }

    public function getCropLayersItemData($id)
    {
        return $this->DbHandler->getCropLayersItemData($id);
    }

    public function getCropLayerIsakData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getCropLayerIsakData($options, $counter, $returnOnlySQL);
    }
}
