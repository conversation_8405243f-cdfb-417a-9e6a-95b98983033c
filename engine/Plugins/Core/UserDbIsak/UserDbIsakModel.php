<?php

namespace TF\Engine\Plugins\Core\UserDbIsak;

use PDO;
use TF\Engine\Plugins\Core\UserDb\UserDbModel;

class UserDbIsakModel extends UserDbModel
{
    public function getIsakDiffAllowableData($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);
        $isakView = $options['isakView'];
        $sql = "SELECT {$return} FROM {$options['tablename']} isak 
					LEFT JOIN {$isakView} A ON st_intersects (A .geom, isak.geom) WHERE TRUE ";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];

            if ($options['having']) {
                $sql .= ' HAVING ' . $options['having'];
            }
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }
}
