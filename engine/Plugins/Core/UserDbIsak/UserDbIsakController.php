<?php

namespace TF\Engine\Plugins\Core\UserDbIsak;

use TF\Engine\Plugins\Core\UserDb\UserDbController;

class UserDbIsakController extends UserDbController
{
    public $DbHandler;
    public $Database;

    public function __construct($database)
    {
        $this->DbHandler = new UserDbIsakModel($database);
        $this->Database = $database;
    }

    public function getIsakDiffAllowableData($options, $tableName, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getIsakDiffAllowableData($options, $tableName, $counter, $returnOnlySQL);
    }
}
