<?php

namespace TF\Engine\Plugins\Core\UserDbOSZ;

use TF\Engine\Plugins\Core\UserDb\UserDbController;

class UserDbOSZController extends UserDbController
{
    public $DbHandler;
    public $Database;

    public function __construct($database)
    {
        $this->DbHandler = new UserDbOSZModel($database);
        $this->Database = $database;
    }

    public function getOSZFilesPlots($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getOSZFilesPlots($options, $counter, $returnOnlySQL);
    }

    public function getOwnersToAddFromOSZ($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getOwnersToAddFromOSZ($options, $counter, $returnOnlySQL);
    }

    public function addOwnersFromOSZ($arrResult)
    {
        return $this->DbHandler->addOwnersFromOSZ($arrResult);
    }
}
