<?php

namespace TF\Engine\Plugins\Core\UserDbOSZ;

use PDO;
use TF\Engine\Plugins\Core\UserDb\UserDbModel;

class UserDbOSZModel extends UserDbModel
{
    public function getOSZFilesPlots($options, $counter, $returnOnlySQL)
    {
        if (!$options['return']) {
            $return = '*';
        }

        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = "SELECT {$return} FROM (
                        SELECT fp.*, kvs.gid, kvs.kad_ident, (CASE WHEN id IS NULL THEN 'kvs-' || gid ELSE 'osz-' || id END) AS id_both FROM {$this->tableOSZFilesPlots} fp
                        FULL JOIN {$this->tableKVS} kvs ON(kvs.kad_ident = fp.kad_no AND fp.file_id = :file_id)
                        WHERE fp.file_id = :file_id OR kvs.ekate = :ekatte
                    ) as p
                LEFT JOIN (
                    SELECT kvs.gid, string_agg(f.bulstat, ', ') as bulstat, array_agg(f.bulstat) as bulstat_arr FROM {$this->tableKVS} kvs
                    JOIN {$this->contractsPlotsRelTable} cp ON(cp.plot_id = kvs.gid)
                    JOIN {$this->tableContracts} c ON(c.id = cp.contract_id)
                    LEFT JOIN dblink (
                        'host=" . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' dbname=' . DEFAULT_DB_DATABASE . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . "',
                        'select id, bulstat from su_users_farming WHERE true'
                    ) AS f (id integer, bulstat varchar) ON(f.id = c.farming_id)
                    GROUP BY kvs.gid
                ) as f ON(f.gid = p.gid)
                WHERE true";

        if ($options['where_or']) {
            $sql .= ' AND (false';

            $sql = $this->createWhereSQL($sql, $options['where_or'], $returnOnlySQL, 'OR');

            $sql .= ')';
        }

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            $sql = str_replace(':file_id', "'" . $options['file_id'] . "'", $sql);

            return str_replace(':ekatte', "'" . $options['ekatte'] . "'", $sql);
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where_or']) {
            $this->createWhereBinds($cmd, $options['where_or']);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        $cmd->bindParameter(':file_id', $options['file_id']);
        $cmd->bindParameter(':ekatte', $options['ekatte']);

        return $cmd->query()->readAll();
    }

    public function getOwnersToAddFromOSZ($options, $counter, $returnOnlySQL)
    {
        if (!$options['return']) {
            $return = '*';
        }

        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = "SELECT {$return} FROM (
                        SELECT fp.id, fp.egn_subekt, fp.ime_subekt, fp.kod_subekt, fp.kad_no, fp.kategoria, fp.txt_ntp, kvs.gid, kvs.kad_ident FROM {$this->tableOSZFilesPlots} fp
                        FULL JOIN {$this->tableKVS} kvs ON(kvs.kad_ident = fp.kad_no AND fp.file_id = :file_id)
                        WHERE fp.file_id = :file_id AND kvs.ekate = :ekatte AND fp.kod_subekt IN ('1','2')
                    ) as p
                LEFT JOIN (
                    SELECT kvs.gid, string_agg(f.bulstat, ', ') as bulstat, array_agg(f.bulstat) as bulstat_arr FROM {$this->tableKVS} kvs
                    JOIN {$this->contractsPlotsRelTable} cp ON(cp.plot_id = kvs.gid)
                    JOIN {$this->tableContracts} c ON(c.id = cp.contract_id)
                    LEFT JOIN dblink (
                        'host=" . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' dbname=' . DEFAULT_DB_DATABASE . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . "',
                        'select id, bulstat from su_users_farming WHERE true'
                    ) AS f (id integer, bulstat varchar) ON(f.id = c.farming_id)
                    GROUP BY kvs.gid
                ) as f ON(f.gid = p.gid)
                WHERE true";

        if ($options['where_or']) {
            $sql .= ' AND (false';

            $sql = $this->createWhereSQL($sql, $options['where_or'], $returnOnlySQL, 'OR');

            $sql .= ')';
        }

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        $sql .= " AND p.id NOT IN(

			SELECT DISTINCT p.id FROM (
						SELECT fp.id, fp.egn_subekt, fp.ime_subekt, fp.kod_subekt, fp.kad_no, fp.kategoria, fp.txt_ntp, kvs.gid, kvs.kad_ident FROM {$this->tableOSZFilesPlots} fp
                        FULL JOIN {$this->tableKVS} kvs ON(kvs.kad_ident = fp.kad_no AND fp.file_id = :file_id)
                        WHERE fp.file_id = :file_id AND kvs.ekate = :ekatte AND fp.kod_subekt IN ('1','2')
					) as p
			LEFT JOIN (
					SELECT kvs.gid, string_agg(f.bulstat, ', ') as bulstat, array_agg(f.bulstat) as bulstat_arr FROM {$this->tableKVS} kvs
                    JOIN {$this->contractsPlotsRelTable} cp ON(cp.plot_id = kvs.gid)
                    JOIN {$this->tableContracts} c ON(c.id = cp.contract_id)
                    LEFT JOIN dblink (
                        'host=" . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' dbname=' . DEFAULT_DB_DATABASE . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . "',
                        'select id, bulstat from su_users_farming WHERE true'
                    ) AS f (id integer, bulstat varchar) ON(f.id = c.farming_id)
                    GROUP BY kvs.gid
			) as f ON(f.gid = p.gid)
			RIGHT JOIN su_owners o on o.egn = p.egn_subekt OR o.eik = p.egn_subekt
			WHERE p.kod_subekt IN ('1','2')";

        if ($options['where_or']) {
            $sql .= ' AND (false';

            $sql = $this->createWhereSQL($sql, $options['where_or'], $returnOnlySQL, 'OR');

            $sql .= ')';
        }

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        $sql .= ')';

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($returnOnlySQL) {
            $sql = str_replace(':file_id', "'" . $options['file_id'] . "'", $sql);

            return str_replace(':ekatte', "'" . $options['ekatte'] . "'", $sql);
        }

        if ($options['where_or']) {
            $this->createWhereBinds($cmd, $options['where_or']);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        $cmd->bindParameter(':file_id', $options['file_id']);
        $cmd->bindParameter(':ekatte', $options['ekatte']);

        return $cmd->query()->readAll();
    }

    public function addOwnersFromOSZ($arrResult)
    {
        $query = 'INSERT INTO su_owners (name, surname, lastname, egn, company_name, eik, owner_type) VALUES ';
        foreach ($arrResult as $k => $v) {
            $query .= '(:v' . $k . '_name, :v' . $k . '_surname, :v' . $k . '_lastname, :v' . $k . '_egn, :v' . $k . '_company_name, :v' . $k . '_eik, :v' . $k . '_owner_type)';
            if ($k !== sizeof($arrResult) - 1) {
                $query .= ', ';
            }
        }

        $cmd = $this->DbModule->createCommand($query);

        foreach ($arrResult as $k => $v) {
            $cmd->bindValue(':v' . $k . '_name', $v['name']);
            $cmd->bindValue(':v' . $k . '_surname', $v['surname']);
            $cmd->bindValue(':v' . $k . '_lastname', $v['lastname']);
            $cmd->bindValue(':v' . $k . '_egn', $v['egn']);
            $cmd->bindValue(':v' . $k . '_company_name', $v['company_name']);
            $cmd->bindValue(':v' . $k . '_eik', $v['eik']);
            $cmd->bindValue(':v' . $k . '_owner_type', $v['owner_type']);
        }

        $cmd->execute();
    }
}
