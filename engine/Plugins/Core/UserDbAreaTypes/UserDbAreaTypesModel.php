<?php

namespace TF\Engine\Plugins\Core\UserDbAreaTypes;

use PDO;
use TF\Engine\Plugins\Core\UserDb\UserDbModel;

class UserDbAreaTypesModel extends UserDbModel
{
    public function getNtpTitle(string $code): string
    {
        $sql = 'SELECT public.get_ntp_title_by_code(:code) AS title;';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindValue(':code', $code, PDO::PARAM_STR);

        return $cmd->query()->readColumn(0);
    }

    public function getNtpAdditionalCodes(array $codes): array
    {
        $sql = 'SELECT unnest(additional_codes)::varchar AS additional_codes
                FROM su_area_types
                WHERE id = ANY(:codes);';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindValue(':codes', '{' . implode(',', $codes) . '}', PDO::PARAM_STR);

        return $cmd->queryColumn();
    }

    public function getNtps(): array
    {
        $sql = 'SELECT id, title as name, CASE WHEN additional_codes <> \'{}\' THEN array_to_json(additional_codes::int[])::jsonb ELSE null END as additionalCodes FROM su_area_types';
        $cmd = $this->DbModule->createCommand($sql);
        $ntps = $cmd->query()->readAll();

        foreach ($ntps as &$row) {
            if (!is_null($row['additionalcodes'])) {
                $row['additionalcodes'] = json_decode($row['additionalcodes'], true);
            }
        }

        return $ntps;
    }
}
