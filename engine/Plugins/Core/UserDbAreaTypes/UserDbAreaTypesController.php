<?php

namespace TF\Engine\Plugins\Core\UserDbAreaTypes;

use TF\Engine\Plugins\Core\UserDb\UserDbController;

class UserDbAreaTypesController extends UserDbController
{
    /**
     * @var UserDbAreaTypesModel
     */
    public $DbHandler;
    public $Database;

    public function __construct($database)
    {
        $this->DbHandler = new UserDbAreaTypesModel($database);
        $this->Database = $database;
    }

    public function getNtpTitle(?string $code): ?string
    {
        if (empty($code)) {
            return null;
        }

        return $this->DbHandler->getNtpTitle($code);
    }

    public function getNtpAdditionalCodes(array $codes): array
    {
        return $this->DbHandler->getNtpAdditionalCodes($codes);
    }

    public function getNtpCodesWithAdditionalCodes(array $codes): array
    {
        $codes = array_filter($codes, function ($ntp) {
            return !empty($ntp);
        });

        $additionalNtpCodes = $this->DbHandler->getNtpAdditionalCodes($codes);

        $withoutNtpIdx =  array_search('-1', $codes);
        if (false !== $withoutNtpIdx) {
            $codes[$withoutNtpIdx] = null;
        }

        return array_merge($codes, $additionalNtpCodes);
    }

    public function getNtps(): array
    {
        return $this->DbHandler->getNtps();
    }
}
