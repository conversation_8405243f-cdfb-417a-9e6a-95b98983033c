<?php

namespace TF\Engine\Plugins\Core\Layers;

use TF\Application\Entity\LayerStyles;
use TF\Application\Entity\UserLayers;

class UserLayerStylesDTO
{
    public ?UserLayers $userLayer = null;
    /**
     * @var LayerStyles[] $styles
     */
    public array $styles = [];

    public function __construct(?UserLayers $userLayer = null, array $styles = [])
    {
        $this->userLayer = $userLayer;
        $this->styles = $styles;
    }
}
