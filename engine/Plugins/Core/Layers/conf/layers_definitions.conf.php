<?php

use TF\Application\Common\Config;

$GLOBALS['Layers']['definitions'] = [
    Config::LAYER_TYPE_ZP => [
        [
            'col_name' => 'id',
            'col_title' => 'id',
            'col_visible' => false, // column is visible in attribute information, can be hidden from personalise layer drawer
            'col_personalizable' => false, // column is listed in layer personalization, can be renamed by user
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GID,
            'col_multiedit' => false, // column is visible in multiedit drawer
            'col_singleedit' => true, // column is visible in edit single feature drawer
            'col_sortable' => false,
            'col_exportable' => true, // column is listed in exported file
            'col_copyable' => false, // column is listed in copy-layer modal if col_copyable is true and col_personalizable is true.
            'col_virtual' => false, // column is generated by expression
            'col_expression' => null, // expression to generate column value
            'col_filter_selection_type' => null, // filter selection type can be multiple single or false
            'col_reference' => null, // reference to another definition
            // If col_personalizable is false and col_copyable is true, the column is not listed in copy-layer modal but it is copied
        ],
        [
            'col_name' => 'geom',
            'col_title' => 'geom',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GEOM,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'virtual_area_dka',
            'col_title' => 'Area(dka)',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => true,
            'col_expression' => '(round((ST_Area(geom)/1000)::numeric, 3))',
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => 'geom',
        ],
        [
            'col_name' => 'area_name',
            'col_title' => 'Field name',
            'col_visible' => true,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NAME,
            'col_multiedit' => false,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'ekatte',
            'col_title' => 'ЕКАТТЕ',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_EKATTE,
            'col_multiedit' => true,
            'col_singleedit' => true,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'virtual_ekatte_name',
            'col_title' => 'ЕКАТТЕ',
            'col_visible' => true,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => true,
            'col_expression' => 'get_ekatte_name_by_code(ekatte)',
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => 'ekatte',
        ],
        [
            'col_name' => 'culture',
            'col_title' => 'Crop',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_CROP,
            'col_multiedit' => true,
            'col_singleedit' => true,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'virtual_crop_name',
            'col_title' => 'Crop',
            'col_visible' => true,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => true,
            'col_expression' => 'get_crop_name_by_code(culture)',
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => 'culture',
        ],
        [
            'col_name' => 'obrabotki',
            'col_title' => 'Tilth',
            'col_visible' => true,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => true,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'dobivi',
            'col_title' => 'Yields',
            'col_visible' => true,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => true,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'napoqvane',
            'col_title' => 'Irrigation',
            'col_visible' => true,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => true,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'polivki',
            'col_title' => 'Irrigation tasks',
            'col_visible' => true,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => true,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'polzvatel',
            'col_title' => 'Used by',
            'col_visible' => true,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => true,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'isak_prc_uin',
            'col_title' => 'ISAK number',
            'col_visible' => true,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'mestnost',
            'col_title' => 'Locality',
            'col_visible' => false,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => true,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'fill_color',
            'col_title' => 'Fill color',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_COLOR,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'border_color',
            'col_title' => 'Border color',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_COLOR,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'label',
            'col_title' => 'Label',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_LABEL,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
    ],

    Config::LAYER_TYPE_GPS => [
        [
            'col_name' => 'gid',
            'col_title' => 'gid',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GID,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'geom',
            'col_title' => 'geom',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GEOM,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'virtual_area_dka',
            'col_title' => 'Area(dka)',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => true,
            'col_expression' => '(round((ST_Area(geom)/1000)::numeric, 3))',
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => 'geom',
        ],
        [
            'col_name' => 'plot_name',
            'col_title' => 'Field name',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NAME,
            'col_multiedit' => false,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'plot_info',
            'col_title' => 'Comment',
            'col_visible' => true,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => true,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'fill_color',
            'col_title' => 'Fill color',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_COLOR,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'border_color',
            'col_title' => 'Border color',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_COLOR,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'label',
            'col_title' => 'Label',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_LABEL,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
    ],

    Config::LAYER_TYPE_KMS => [
        [
            'col_name' => 'gid',
            'col_title' => 'gid',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GID,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'geom',
            'col_title' => 'geom',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GEOM,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'virtual_area_dka',
            'col_title' => 'Area(dka)',
            'col_visible' => true,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => true,
            'col_expression' => '(round((ST_Area(geom)/1000)::numeric, 3))',
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => 'geom',
        ],
        [
            'col_name' => 'name',
            'col_title' => 'BZZ number',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NAME,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'ekatte',
            'col_title' => 'EKATTE',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_EKATTE,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'virtual_ekatte_name',
            'col_title' => 'EKATTE',
            'col_visible' => true,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => true,
            'col_expression' => 'get_ekatte_name_by_code(ekatte)',
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => 'ekatte',
        ],
        [
            'col_name' => 'crop_code',
            'col_title' => 'Crop',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_CROP,
            'col_multiedit' => true,
            'col_singleedit' => true,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'virtual_crop_name',
            'col_title' => 'Crop',
            'col_visible' => true,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => true,
            'col_expression' => 'get_crop_name_by_code(crop_code)',
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => 'crop_code',
        ],
        [
            'col_name' => 'fill_color',
            'col_title' => 'Fill color',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_COLOR,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'border_color',
            'col_title' => 'Border color',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_COLOR,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'label',
            'col_title' => 'Label',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_LABEL,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
    ],

    Config::LAYER_TYPE_KVS => [
        [
            'col_name' => 'gid',
            'col_title' => 'gid',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GID,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'geom',
            'col_title' => 'geom',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GEOM,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'kad_ident',
            'col_title' => 'Identifier',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NAME,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'ekate',
            'col_title' => 'EKATTE',
            'col_visible' => false,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_EKATTE,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'virtual_ekatte_name',
            'col_title' => 'EKATTE',
            'col_visible' => true,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => true,
            'col_expression' => 'get_ekatte_name_by_code(ekate)',
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => 'ekate',
        ],
        [
            'col_name' => 'masiv',
            'col_title' => 'Masiv',
            'col_visible' => false,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'number',
            'col_title' => 'Number',
            'col_visible' => false,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'category',
            'col_title' => 'Category',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_CATEGORY,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'virtual_category_title',
            'col_title' => 'Category',
            'col_visible' => true,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => true,
            'col_expression' => 'get_plot_category_by_id(category)',
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => 'category',
        ],
        [
            'col_name' => 'area_type',
            'col_title' => 'NTP',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NTP,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'virtual_ntp_title',
            'col_title' => 'NTP',
            'col_visible' => true,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => true,
            'col_expression' => 'get_ntp_title_by_code(area_type)',
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => 'area_type',
        ],
        [
            'col_name' => 'has_contracts',
            'col_title' => 'has_contracts',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_BOOLEAN,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'mestnost',
            'col_title' => 'Local area',
            'col_visible' => true,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'include',
            'col_title' => 'include',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_BOOLEAN,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'participate',
            'col_title' => 'participate',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_BOOLEAN,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'white_spots',
            'col_title' => 'white_spots',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_BOOLEAN,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'used_area_by',
            'col_title' => 'used_area_by',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'area_farming',
            'col_title' => 'area_farming',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'area_year',
            'col_title' => 'area_year',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'used_area',
            'col_title' => 'used_area',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'usable',
            'col_title' => 'usable',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_BOOLEAN,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'document_area',
            'col_title' => 'Document area(dka)',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'is_edited',
            'col_title' => 'is_edited',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_BOOLEAN,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'edit_date',
            'col_title' => 'edit_date',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_DATE,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'edit_active_from',
            'col_title' => 'edit_active_from',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_DATE,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'waiting_update',
            'col_title' => 'waiting_update',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_BOOLEAN,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'irrigated_area',
            'col_title' => 'irrigated_area',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_BOOLEAN,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'comment',
            'col_title' => 'comment',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'old_kad_ident',
            'col_title' => 'old_kad_ident',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'allowable_area',
            'col_title' => 'Cross-sectional area(dka)',
            'col_visible' => true,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'allowable_type',
            'col_title' => 'Allowable type',
            'col_visible' => true,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'block',
            'col_title' => 'block',
            'col_visible' => false,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'fill_color',
            'col_title' => 'Fill color',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_COLOR,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'border_color',
            'col_title' => 'Border color',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_COLOR,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'label',
            'col_title' => 'Label',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_LABEL,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
    ],

    Config::LAYER_TYPE_ISAK => [
        [
            'col_name' => 'gid',
            'col_title' => 'gid',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GID,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'geom',
            'col_title' => 'geom',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GEOM,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'prc_uin',
            'col_title' => 'Identifier',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NAME,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'ekatte',
            'col_title' => 'ЕКАТТЕ',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_EKATTE,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            // TODO: VC Remove this column when the virtual column is used instead
            'col_name' => 'ek_name',
            'col_title' => 'ЕКАТТЕ',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'virtual_ekatte_name',
            'col_title' => 'ЕКАТТЕ',
            'col_visible' => true,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => true,
            'col_expression' => 'get_ekatte_name_by_code(ekatte)',
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => 'ekatte',
        ],
        [
            'col_name' => 'cropcode',
            'col_title' => 'cropcode',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_CROP,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            // TODO: VC Remove this column when the virtual column is used instead
            'col_name' => 'cropname',
            'col_title' => 'Crop',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'virtual_crop_name',
            'col_title' => 'Crop',
            'col_visible' => true,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => true,
            'col_expression' => 'get_crop_name_by_code(cropcode)',
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => 'cropcode',
        ],
        [
            'col_name' => 'id',
            'col_title' => 'id',
            'col_visible' => false,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'bzs_id',
            'col_title' => 'BZS',
            'col_visible' => true,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'area',
            'col_title' => 'Area(ha)',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'watering',
            'col_title' => 'Irrigation',
            'col_visible' => true,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_BOOLEAN,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'schemata',
            'col_title' => 'Scheme',
            'col_visible' => true,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'campaign',
            'col_title' => 'Campaign',
            'col_visible' => false,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'urn',
            'col_title' => 'URN',
            'col_visible' => false,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'fill_color',
            'col_title' => 'Fill color',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_COLOR,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'border_color',
            'col_title' => 'Border color',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_COLOR,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'label',
            'col_title' => 'Label',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_LABEL,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
    ],

    Config::LAYER_TYPE_FOR_ISAK => [
        [
            'col_name' => 'gid',
            'col_title' => 'gid',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GID,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'geom',
            'col_title' => 'geom',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GEOM,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            // TODO: VC Remove this column when the virtual column is used instead
            'col_name' => 'area',
            'col_title' => 'Area(ha)',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'virtual_area_ha',
            'col_title' => 'Area(ha)',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => true,
            'col_expression' => '(round((ST_Area(geom)/10000)::numeric, 3))',
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => 'geom',
        ],
        [
            'col_name' => 'prc_name',
            'col_title' => 'Field name',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NAME,
            'col_multiedit' => false,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'ekatte',
            'col_title' => 'ЕКАТТЕ',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_EKATTE,
            'col_multiedit' => true,
            'col_singleedit' => true,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'virtual_ekatte_name',
            'col_title' => 'ЕКАТТЕ',
            'col_visible' => true,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => true,
            'col_expression' => 'get_ekatte_name_by_code(ekatte)',
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => 'ekatte',
        ],
        [
            'col_name' => 'cropcode',
            'col_title' => 'Crop',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_CROP,
            'col_multiedit' => true,
            'col_singleedit' => true,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            // TODO: VC Remove this column when the virtual column is used instead
            'col_name' => 'cropname',
            'col_title' => 'Crop',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'virtual_crop_name',
            'col_title' => 'Crop',
            'col_visible' => true,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => true,
            'col_expression' => 'get_crop_name_by_code(cropcode)',
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => 'cropcode',
        ],
        [
            'col_name' => 'prc_uin',
            'col_title' => 'prc_uin',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'schema',
            'col_title' => 'Schema',
            'col_visible' => true,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => true,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'edited',
            'col_title' => 'For edit',
            'col_visible' => true,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_BOOLEAN,
            'col_multiedit' => true,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'comment',
            'col_title' => 'Comment',
            'col_visible' => true,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => true,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'crop_type',
            'col_title' => 'crop_type',
            'col_visible' => false,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'crop_genus',
            'col_title' => 'crop_genus',
            'col_visible' => false,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'azot_fixed_crop',
            'col_title' => 'azot_fixed_crop',
            'col_visible' => false,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_BOOLEAN,
            'col_multiedit' => false,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'is_intermediate_crop',
            'col_title' => 'is_intermediate_crop',
            'col_visible' => false,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_BOOLEAN,
            'col_multiedit' => false,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'is_intermediate_weat_crop',
            'col_title' => 'is_intermediate_weat_crop',
            'col_visible' => false,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_BOOLEAN,
            'col_multiedit' => false,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'is_tree_short_rotation',
            'col_title' => 'is_tree_short_rotation',
            'col_visible' => false,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_BOOLEAN,
            'col_multiedit' => false,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'no_pndn',
            'col_title' => 'no_pndn',
            'col_visible' => false,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_BOOLEAN,
            'col_multiedit' => false,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'common_cultures',
            'col_title' => 'common_cultures',
            'col_visible' => false,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_BOOLEAN,
            'col_multiedit' => false,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'pndp',
            'col_title' => 'pndp',
            'col_visible' => false,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_BOOLEAN,
            'col_multiedit' => false,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'sepp',
            'col_title' => 'sepp',
            'col_visible' => false,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_BOOLEAN,
            'col_multiedit' => false,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'zdp',
            'col_title' => 'zdp',
            'col_visible' => false,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_BOOLEAN,
            'col_multiedit' => false,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'nr1',
            'col_title' => 'nr1',
            'col_visible' => false,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_BOOLEAN,
            'col_multiedit' => false,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'nr2',
            'col_title' => 'nr2',
            'col_visible' => false,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_BOOLEAN,
            'col_multiedit' => false,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'crop_short_type',
            'col_title' => 'crop_short_type',
            'col_visible' => false,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'green_area_factor',
            'col_title' => 'green_area_factor',
            'col_visible' => false,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'natura_sitecode',
            'col_title' => 'natura_sitecode',
            'col_visible' => false,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'weat_crops',
            'col_title' => 'weat_crops',
            'col_visible' => false,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'non_weat_crops',
            'col_title' => 'non_weat_crops',
            'col_visible' => false,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'vps_type',
            'col_title' => 'vps_type',
            'col_visible' => false,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'vps_inside_area',
            'col_title' => 'vps_inside_area',
            'col_visible' => false,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'fill_color',
            'col_title' => 'Fill color',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_COLOR,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'border_color',
            'col_title' => 'Border color',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_COLOR,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'label',
            'col_title' => 'Label',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_LABEL,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
    ],

    Config::LAYER_TYPE_LFA => [
        [
            'col_name' => 'gid',
            'col_title' => 'gid',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GID,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'geom',
            'col_title' => 'geom',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GEOM,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'nm_lfa_eka',
            'col_title' => 'ЕКАТТЕ',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_EKATTE,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            // TODO: VC Remove this column when the virtual column is used instead
            'col_name' => 'nm_lfa_e_1',
            'col_title' => 'ЕКАТТЕ',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'virtual_ekatte_name',
            'col_title' => 'ЕКАТТЕ',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => true,
            'col_expression' => 'get_ekatte_name_by_code(nm_lfa_eka)',
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => 'nm_lfa_eka',
        ],
        [
            'col_name' => 'id',
            'col_title' => 'id',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'center_x',
            'col_title' => 'center_x',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'center_y',
            'col_title' => 'center_y',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'calc_area',
            'col_title' => 'calc_area',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'agro_area',
            'col_title' => 'agro_area',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'ekatte_id',
            'col_title' => 'ekatte_id',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'nm_lfa_e_3',
            'col_title' => 'nm_lfa_e_3',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'nm_lfa_lfa',
            'col_title' => 'Type HP',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'fill_color',
            'col_title' => 'Fill color',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_COLOR,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'border_color',
            'col_title' => 'Border color',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_COLOR,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'label',
            'col_title' => 'Label',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_LABEL,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
    ],

    Config::LAYER_TYPE_NATURA_2000 => [
        [
            'col_name' => 'gid',
            'col_title' => 'gid',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GID,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'geom',
            'col_title' => 'geom',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GEOM,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'name_bg',
            'col_title' => 'Zone name',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NAME,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'sitecode',
            'col_title' => 'Area code',
            'col_visible' => true,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'objectid_1',
            'col_title' => 'objectid_1',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'area_z_dka',
            'col_title' => 'Area(dka)',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'area1970ha',
            'col_title' => 'area1970ha',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'shape_leng',
            'col_title' => 'shape_leng',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'shape_area',
            'col_title' => 'shape_area',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'source',
            'col_title' => 'source',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'date_',
            'col_title' => 'date_',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'zapoved_no',
            'col_title' => 'Order to declare the zone',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'dv',
            'col_title' => 'State gazette',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'iba_code',
            'col_title' => 'iba_code',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],

        [
            'col_name' => 'name_lat',
            'col_title' => 'name_lat',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'type',
            'col_title' => 'type',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            // This column doest not exist in the db, but it is added to attribute-information response.
            // The data is get from $GLOBALS['Farming']['natura_zones']
            'col_name' => 'bans',
            'col_title' => 'Supported bans',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'fill_color',
            'col_title' => 'Fill color',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_COLOR,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'border_color',
            'col_title' => 'Border color',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_COLOR,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'label',
            'col_title' => 'Label',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_LABEL,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
    ],

    Config::LAYER_TYPE_PERMANETELY_GREEN_AREAS => [
        [
            'col_name' => 'gid',
            'col_title' => 'gid',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GID,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'geom',
            'col_title' => 'geom',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GEOM,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'imotcode',
            'col_title' => 'Identifier',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NAME,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'imekatte',
            'col_title' => 'EKATTE',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_EKATTE,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'virtual_ekatte_name',
            'col_title' => 'EKATTE',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => true,
            'col_expression' => 'get_ekatte_name_by_code(imekatte)',
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => 'imekatte',
        ],
        [
            'col_name' => 'ext_id',
            'col_title' => 'ext_id',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'imoti_pml_',
            'col_title' => 'imoti_pml_',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'sharea',
            'col_title' => 'Area (dka)',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'area_spa',
            'col_title' => 'area_spa',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'part_spa',
            'col_title' => 'part_spa',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'area_sci',
            'col_title' => 'area_sci',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'part_sci',
            'col_title' => 'part_sci',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'area_hab',
            'col_title' => 'area_hab',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'part_hab',
            'col_title' => 'part_hab',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'pzp_area',
            'col_title' => 'Property area PZP(dka)',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'pzp_perc',
            'col_title' => 'pzp_perc',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'natura',
            'col_title' => 'natura',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'pzp_imot',
            'col_title' => 'pzp_imot',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'is_nopml',
            'col_title' => 'is_nopml',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'is_spa',
            'col_title' => 'is_spa',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'is_sci',
            'col_title' => 'is_sci',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'obl',
            'col_title' => 'obl',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'oblast',
            'col_title' => 'oblast',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'obshtina',
            'col_title' => 'obshtina',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'zeml',
            'col_title' => 'Land',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'l_uscode',
            'col_title' => 'l_uscode',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'l_tycode',
            'col_title' => 'l_tycode',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'zemcad',
            'col_title' => 'zemcad',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'is_hab',
            'col_title' => 'is_hab',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'fill_color',
            'col_title' => 'Fill color',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_COLOR,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'border_color',
            'col_title' => 'Border color',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_COLOR,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'label',
            'col_title' => 'Label',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_LABEL,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
    ],

    Config::LAYER_TYPE_VPS_PASISHTA => [
        [
            'col_name' => 'gid',
            'col_title' => 'gid',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GID,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'geom',
            'col_title' => 'geom',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GEOM,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'full_name',
            'col_title' => 'Name',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NAME,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'fid_tiles_',
            'col_title' => 'fid_tiles_',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'fid_vps_me',
            'col_title' => 'fid_vps_me',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'vps_id',
            'col_title' => 'vps_id',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'area',
            'col_title' => 'Area(dka)',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'air_2011',
            'col_title' => 'air_2011',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'sat_2008',
            'col_title' => 'sat_2008',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'sat_2009',
            'col_title' => 'sat_2009',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'air_2012',
            'col_title' => 'air_2012',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'air_2013',
            'col_title' => 'air_2013',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'zone_',
            'col_title' => 'zone_',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'air_2006',
            'col_title' => 'air_2006',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'air_2010',
            'col_title' => 'air_2010',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'fill_color',
            'col_title' => 'Fill color',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_COLOR,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'border_color',
            'col_title' => 'Border color',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_COLOR,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'label',
            'col_title' => 'Label',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_LABEL,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
    ],

    Config::LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI => [
        [
            'col_name' => 'gid',
            'col_title' => 'gid',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GID,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'geom',
            'col_title' => 'geom',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GEOM,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'name',
            'col_title' => 'Name',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NAME,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'ekatte',
            'col_title' => 'EKATTE',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_EKATTE,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'virtual_ekatte_name',
            'col_title' => 'EKATTE',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => true,
            'col_expression' => 'get_ekatte_name_by_code(ekatte)',
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => 'ekatte',
        ],
        [
            // This column doest not exist in the db, but it is added to attribute-information response.
            'col_name' => 'vps_type',
            'col_title' => 'VPS type',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'shape_area',
            'col_title' => 'shape_area',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'objectid',
            'col_title' => 'objectid',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],

        [
            'col_name' => 'shape_leng',
            'col_title' => 'shape_leng',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'fill_color',
            'col_title' => 'Fill color',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_COLOR,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'border_color',
            'col_title' => 'Border color',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_COLOR,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'label',
            'col_title' => 'Label',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_LABEL,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
    ],

    Config::LAYER_TYPE_VPS_GASKI_ZIMNI => [
        [
            'col_name' => 'gid',
            'col_title' => 'gid',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GID,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'geom',
            'col_title' => 'geom',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GEOM,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'name',
            'col_title' => 'Name',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NAME,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'ekatte',
            'col_title' => 'ЕКАТТЕ',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_EKATTE,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'virtual_ekatte_name',
            'col_title' => 'ЕКАТТЕ',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => true,
            'col_expression' => 'get_ekatte_name_by_code(ekatte)',
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => 'ekatte',
        ],
        [
            'col_name' => 'objectid',
            'col_title' => 'objectid',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'shape_leng',
            'col_title' => 'shape_leng',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'shape_area',
            'col_title' => 'shape_area',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'f4',
            'col_title' => 'f4',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'ekatte_1',
            'col_title' => 'ekatte_1',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'zem',
            'col_title' => 'Locality',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'goose',
            'col_title' => 'goose',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'fill_color',
            'col_title' => 'Fill color',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_COLOR,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'border_color',
            'col_title' => 'Border color',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_COLOR,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'label',
            'col_title' => 'Label',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_LABEL,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
    ],

    Config::LAYER_TYPE_VPS_LIVADEN_BLATAR => [
        [
            'col_name' => 'gid',
            'col_title' => 'gid',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GID,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'geom',
            'col_title' => 'geom',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GEOM,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'ime',
            'col_title' => 'Name',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NAME,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            // This column doest not exist in the db, but it is added to attribute-information response.
            'col_name' => 'vps_type',
            'col_title' => 'VPS type',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'objectid_1',
            'col_title' => 'objectid_1',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'objectid_2',
            'col_title' => 'objectid_2',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'objectid',
            'col_title' => 'objectid',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'obstina',
            'col_title' => 'Municipality',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'kod',
            'col_title' => 'kod',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'shape_le_1',
            'col_title' => 'shape_le_1',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'shape_le_2',
            'col_title' => 'shape_le_2',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'shape_area',
            'col_title' => 'shape_area',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'ftype',
            'col_title' => 'ftype',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'n_provonce',
            'col_title' => 'n_provonce',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'eknm2',
            'col_title' => 'eknm2',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'ekatte_zmg',
            'col_title' => 'ekatte_zmg',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'razlika2mz',
            'col_title' => 'razlika2mz',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'shape_leng',
            'col_title' => 'shape_leng',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'selo',
            'col_title' => 'Village',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'province',
            'col_title' => 'province',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'imeobs',
            'col_title' => 'imeobs',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'ftype_opis',
            'col_title' => 'ftype_opis',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'fill_color',
            'col_title' => 'Fill color',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_COLOR,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'border_color',
            'col_title' => 'Border color',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_COLOR,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'label',
            'col_title' => 'Label',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_LABEL,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
    ],

    Config::LAYER_TYPE_VPS_ORLI_LESHOYADI => [
        [
            'col_name' => 'gid',
            'col_title' => 'gid',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GID,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'geom',
            'col_title' => 'geom',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GEOM,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'blockuin',
            'col_title' => 'Physical block number"',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NAME,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'ekatte',
            'col_title' => 'EKATTE',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_EKATTE,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'virtual_ekatte_name',
            'col_title' => 'ЕКАТТЕ',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => true,
            'col_expression' => 'get_ekatte_name_by_code(ekatte)',
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => 'ekatte',
        ],
        [
            'col_name' => 'ntp',
            'col_title' => 'NTP',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NTP,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'ext_id',
            'col_title' => 'ext_id',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'removed',
            'col_title' => 'removed',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'remark',
            'col_title' => 'remark',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'checked_on',
            'col_title' => 'checked_on',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'deserted',
            'col_title' => 'deserted',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'fill_color',
            'col_title' => 'Fill color',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_COLOR,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'border_color',
            'col_title' => 'Border color',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_COLOR,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'label',
            'col_title' => 'Label',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_LABEL,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
    ],

    Config::LAYER_TYPE_PHYSICAL_BLOCKS => [
        [
            'col_name' => 'gid',
            'col_title' => 'gid',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GID,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'geom',
            'col_title' => 'geom',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GEOM,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'fbident',
            'col_title' => 'Physical block',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NAME,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'ekatte_',
            'col_title' => 'EKATTE',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_EKATTE,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'virtual_ekatte_name',
            'col_title' => 'ЕКАТТЕ',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => true,
            'col_expression' => 'get_ekatte_name_by_code(ekatte_)',
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => 'ekatte_',
        ],
        [
            'col_name' => 'ntp_k',
            'col_title' => 'ntp_k',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'elgarea',
            'col_title' => 'Area(ha)',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'fb_area',
            'col_title' => 'Physical block area(ha)',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'zemlishte',
            'col_title' => 'Land',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'ntp',
            'col_title' => 'Type',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
    ],

    Config::LAYER_TYPE_ALLOWABLE_FINAL => [
        [
            'col_name' => 'gid',
            'col_title' => 'gid',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GID,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'geom',
            'col_title' => 'geom',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GEOM,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'fbident',
            'col_title' => 'Physical block',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NAME,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'ekatte_',
            'col_title' => 'EKATTE',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_EKATTE,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'virtual_ekatte_name',
            'col_title' => 'ЕКАТТЕ',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => true,
            'col_expression' => 'get_ekatte_name_by_code(ekatte_)',
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => 'ekatte_',
        ],
        [
            'col_name' => 'ntp_k',
            'col_title' => 'ntp_k',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NTP,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'elgarea',
            'col_title' => 'Area(ha)',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'fb_area',
            'col_title' => 'Physical block area(ha)',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'elg_ident',
            'col_title' => 'elg_ident',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'ntp',
            'col_title' => 'Type',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'usageeng',
            'col_title' => 'usageeng',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'zemlishte',
            'col_title' => 'Land',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'filename',
            'col_title' => 'filename',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
    ],

    Config::LAYER_TYPE_WORK_LAYER => [
        [
            'col_name' => 'gid',
            'col_title' => 'gid',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GID,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'geom',
            'col_title' => 'geom',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GEOM,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'name',
            'col_title' => 'Field name',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NAME,
            'col_multiedit' => false,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'virtual_area_dka',
            'col_title' => 'Area(dka)',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => true,
            'col_expression' => '(round((ST_Area(geom)/1000)::numeric, 3))',
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => 'geom',
        ],
        [
            'col_name' => 'slope',
            'col_title' => 'Slope',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_SLOPE,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'fill_color',
            'col_title' => 'Fill color',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_COLOR,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'border_color',
            'col_title' => 'Border color',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_COLOR,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'label',
            'col_title' => 'Label',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_LABEL,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
    ],
    Config::LAYER_TYPE_CSD => [
        [
            'col_name' => 'gid',
            'col_title' => 'gid',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GID,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'kad_ident',
            'col_title' => 'Identifier',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NAME,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'geom',
            'col_title' => 'geom',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GEOM,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'ekatte',
            'col_title' => 'EKATTE',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_EKATTE,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'ntp',
            'col_title' => 'Type',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'category',
            'col_title' => 'Category',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'locality',
            'col_title' => 'Locality',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'document_area',
            'col_title' => 'Document area(dka)',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'declared_area_status',
            'col_title' => 'Declared area status',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_DECLARED_AREA_STATUS,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'allowable_area',
            'col_title' => 'Cross-sectional area(dka)',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'applicant',
            'col_title' => 'Applicant',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'applicant_id',
            'col_title' => 'Applicant identifier',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'declared_area',
            'col_title' => 'Declared area(dka)',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'lessor',
            'col_title' => 'Lessor',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'declaration_type',
            'col_title' => 'Declaration type',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_LEGAL_RIGHTS,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'wish',
            'col_title' => 'Wish',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'year',
            'col_title' => 'Year',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_SINGLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'number_of_documents',
            'col_title' => 'Documents',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT, // JSON string E.g. {"Active": 5, "Expired": 2, "All": 7}
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'fill_color',
            'col_title' => 'Fill color',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_COLOR,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'border_color',
            'col_title' => 'Border color',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_COLOR,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'label',
            'col_title' => 'Label',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_LABEL,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
    ],
    Config::LAYER_TYPE_PERMANENT_GRASSLAND_FOR_MOWING_PRELIMINARY => [
        [
            'col_name' => 'gid',
            'col_title' => 'gid',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GID,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'ekatte_name',
            'col_title' => 'ЕКАТТЕ',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'phbident',
            'col_title' => 'phbident',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'elgident',
            'col_title' => 'Идентификатор',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'usagecode',
            'col_title' => 'Тип (код)',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'usagebul',
            'col_title' => 'Тип',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'elgarea',
            'col_title' => 'elgarea',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'geom',
            'col_title' => 'geom',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GEOM,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
    ],
    Config::LAYER_TYPE_PHYSICAL_BLOCKS_PRELIMINARY => [
        [
            'col_name' => 'gid',
            'col_title' => 'gid',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GID,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'ekatte_',
            'col_title' => 'EKATTE',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_EKATTE,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'virtual_ekatte_name',
            'col_title' => 'ЕКАТТЕ',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => true,
            'col_expression' => 'get_ekatte_name_by_code(ekatte_)',
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => 'ekatte',
        ],
        [
            'col_name' => 'phbident',
            'col_title' => 'phbident',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'ntp_k',
            'col_title' => 'Тип (код)',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'ntp',
            'col_title' => 'Тип',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'geom',
            'col_title' => 'geom',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GEOM,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
    ],
    Config::LAYER_TYPE_LANDSCAPE_ELEMENTS_POINTS => [
        [
            'col_name' => 'gid',
            'col_title' => 'gid',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GID,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'ident',
            'col_title' => 'Field name',
            'col_visible' => true,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NAME,
            'col_multiedit' => false,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'type_co',
            'col_title' => 'Тип (код)',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'type_bg',
            'col_title' => 'Тип',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],

        [
            'col_name' => 'geom',
            'col_title' => 'geom',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GEOM,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
    ],
    Config::LAYER_TYPE_LANDSCAPE_ELEMENTS_LINES => [
        [
            'col_name' => 'gid',
            'col_title' => 'gid',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GID,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'ident',
            'col_title' => 'Field name',
            'col_visible' => true,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NAME,
            'col_multiedit' => false,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'type_co',
            'col_title' => 'Тип (код)',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'type_bg',
            'col_title' => 'Тип',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],

        [
            'col_name' => 'geom',
            'col_title' => 'geom',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GEOM,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
    ],
    Config::LAYER_TYPE_LANDSCAPE_ELEMENTS_POLYGONS => [
        [
            'col_name' => 'gid',
            'col_title' => 'gid',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GID,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'ident',
            'col_title' => 'Field name',
            'col_visible' => true,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NAME,
            'col_multiedit' => false,
            'col_singleedit' => true,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
        [
            'col_name' => 'type_co',
            'col_title' => 'Тип (код)',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'type_bg',
            'col_title' => 'Тип',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'layer',
            'col_title' => 'layer',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],

        [
            'col_name' => 'geom',
            'col_title' => 'geom',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GEOM,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
    ],
    Config::LAYER_TYPE_DS_PRC => [
        [
            'col_name' => 'gid',
            'col_title' => 'gid',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GID,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'crop_code',
            'col_title' => 'Crop code',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_CROP,
            'col_multiedit' => true,
            'col_singleedit' => true,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'virtual_crop_name',
            'col_title' => 'Crop',
            'col_visible' => true,
            'col_personalizable' => true,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => true,
            'col_expression' => 'get_crop_name_by_code(culture)',
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => 'culture',
        ],
        [
            'col_name' => 'is_pg',
            'col_title' => 'Is PG',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_BOOLEAN,
            'col_multiedit' => false,
            'col_singleedit' => true,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'is_bio',
            'col_title' => 'Is BIO',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_BOOLEAN,
            'col_multiedit' => false,
            'col_singleedit' => true,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'geom',
            'col_title' => 'geom',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GEOM,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'virtual_area_dka',
            'col_title' => 'Area(dka)',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => true,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => '',
            'col_filter_selection_type' => Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE,
            'col_reference' => null,
        ],
    ],
    Config::LAYER_TYPE_CADASTRE => [
        [
            'col_name' => 'gid',
            'col_title' => 'GID',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GID,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'ekatte',
            'col_title' => 'EKATTE',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_EKATTE,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'virtual_ekatte_name',
            'col_title' => 'ЕКАТТЕ',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => true,
            'col_expression' => 'get_ekatte_name_by_code(ekatte)',
            'col_filter_selection_type' => null,
            'col_reference' => 'ekatte',
        ],
        [
            'col_name' => 'kad_ident',
            'col_title' => 'Identifier',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NAME,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'area',
            'col_title' => 'Area (dka)',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NUMBER,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'category',
            'col_title' => 'Category',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_CATEGORY,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'virtual_category_title',
            'col_title' => 'Category',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => true,
            'col_expression' => 'get_plot_category_by_id(category)',
            'col_filter_selection_type' => null,
            'col_reference' => 'category',
        ],
        [
            'col_name' => 'mestnost',
            'col_title' => 'Locality',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'ntp',
            'col_title' => 'NTP',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_NTP,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'virtual_ntp_title',
            'col_title' => 'NTP',
            'col_visible' => true,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => true,
            'col_copyable' => true,
            'col_virtual' => true,
            'col_expression' => 'get_ntp_title_by_code(ntp)',
            'col_filter_selection_type' => null,
            'col_reference' => 'ntp',
        ],
        [
            'col_name' => 'is_archived',
            'col_title' => 'Archived',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_BOOLEAN,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'geom',
            'col_title' => 'Geometry',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_GEOM,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => true,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'updated_at',
            'col_title' => 'Updated At',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
        [
            'col_name' => 'created_at',
            'col_title' => 'Created At',
            'col_visible' => false,
            'col_personalizable' => false,
            'col_category' => Config::LAYER_COLUMN_CATEGORY_TEXT,
            'col_multiedit' => false,
            'col_singleedit' => false,
            'col_sortable' => false,
            'col_exportable' => false,
            'col_copyable' => false,
            'col_virtual' => false,
            'col_expression' => null,
            'col_filter_selection_type' => null,
            'col_reference' => null,
        ],
    ],
];

$GLOBALS['Layers']['definitions'][Config::LAYER_TYPE_KVS_OSZ] = $GLOBALS['Layers']['definitions'][Config::LAYER_TYPE_KVS];
$GLOBALS['Layers']['definitions'][Config::LAYER_TYPE_PERMANENT_GRASSLAND_FOR_MOWING] = $GLOBALS['Layers']['definitions'][Config::LAYER_TYPE_PERMANENT_GRASSLAND_FOR_MOWING_PRELIMINARY];
