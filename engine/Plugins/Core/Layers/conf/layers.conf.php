<?php

use TF\Application\Common\Config;

$GLOBALS['Layers']['tableName'] = 'users_layers';
$GLOBALS['Layers']['fieldName'] = '';

$GLOBALS['Layers']['systemTables'] = [
    'layer_allowable',
    'layer_allowable_final',
    'layer_natura_2000',
    'layer_pzp',
    'layer_lfa',
    'layer_vps_merg',
    'layer_vps_gaski_chervenogushi',
    'layer_vps_gaski_zimni',
    'layer_vps_livaden_blatar',
    'layer_vps_orli_leshoyadi',
    'layer_merg_selection', // this table is used when selecting (or clipping) objects from the merg layer
    // Added for backward compatibility with the old layer name.
    'layer_allowable_draft',
    'layer_permanent_grassland_for_mowing',
];

$GLOBALS['Layers']['customLayers'] = [
    Config::LAYER_TYPE_ALLOWABLE => [
        'layer_name' => 'Физически блокове 2024 г. - окончателен',
        'fill_color' => '#84A3E0',
        'border_color' => '#ecb710',
        'id' => 'd',
        'table_name' => Config::LAYER_TYPE_ALLOWABLE,
        'layer_type' => Config::LAYER_TYPE_ALLOWABLE,
        'has_physical_table' => true,
    ],
    // Added for backward compatibility with the old layer name.
    Config::LAYER_TYPE_ALLOWABLE_DRAFT => [
        'layer_name' => 'Физически блокове 2024 г. - окончателен',
        'fill_color' => '#84A3E0',
        'border_color' => '#ecb710',
        'id' => 'd',
        'table_name' => Config::LAYER_TYPE_ALLOWABLE,
        'layer_type' => Config::LAYER_TYPE_ALLOWABLE,
        'has_physical_table' => true,
    ],
    Config::LAYER_TYPE_ALLOWABLE_FINAL => [
        'layer_name' => 'Окончателен допустим - 14.02.2023 г',
        'fill_color' => '#0000ff',
        'border_color' => '#0000ff',
        'id' => 'df',
        'table_name' => Config::LAYER_TYPE_ALLOWABLE_FINAL,
        'layer_type' => Config::LAYER_TYPE_ALLOWABLE_FINAL,
        'has_physical_table' => true,
    ],
    Config::LAYER_TYPE_CADASTRE => [
        'layer_name' => 'Кадастър',
        'fill_color' => 'transparent',
        'border_color' => '#000000',
        'id' => 'cadastre',
        'table_name' => 'layer_cadastre',
        'layer_type' => Config::LAYER_TYPE_CADASTRE,
        'has_physical_table' => false,
    ],
];

$GLOBALS['Layers']['defaultColorLayers'] = [
    Config::LAYER_TYPE_LFA,
    Config::LAYER_TYPE_NATURA_2000,
    Config::LAYER_TYPE_PERMANETELY_GREEN_AREAS,
    Config::LAYER_TYPE_VPS_PASISHTA,
    Config::LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI,
    Config::LAYER_TYPE_VPS_GASKI_ZIMNI,
    Config::LAYER_TYPE_VPS_LIVADEN_BLATAR,
    Config::LAYER_TYPE_VPS_ORLI_LESHOYADI,
];

$GLOBALS['Layers']['defaultBorderColorLayers'] = [
    Config::LAYER_TYPE_LFA => '#40c080',
    Config::LAYER_TYPE_NATURA_2000 => '#a0a000',
    Config::LAYER_TYPE_PERMANETELY_GREEN_AREAS => '#00f000',
    Config::LAYER_TYPE_VPS_PASISHTA => '#a70000',
    Config::LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI => '#ffa41c',
    Config::LAYER_TYPE_VPS_GASKI_ZIMNI => '#e54100',
    Config::LAYER_TYPE_VPS_LIVADEN_BLATAR => '#a86c0F',
    Config::LAYER_TYPE_VPS_ORLI_LESHOYADI => '#a900e6',
];

$GLOBALS['Layers']['defaultLablesLayers'] = [
    Config::LAYER_TYPE_LFA => ['nm_lfa_e_1'],
    Config::LAYER_TYPE_NATURA_2000 => ['name_bg', 'sitecode'],
    Config::LAYER_TYPE_PERMANETELY_GREEN_AREAS => ['imotcode'],
    Config::LAYER_TYPE_VPS_PASISHTA => [],
    Config::LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI => ['name'],
    Config::LAYER_TYPE_VPS_GASKI_ZIMNI => ['name'],
    Config::LAYER_TYPE_VPS_LIVADEN_BLATAR => ['ime'],
    Config::LAYER_TYPE_VPS_ORLI_LESHOYADI => ['blockuin', 'ekatte'],
];

$GLOBALS['Layers']['vpsTables'] = [
    Config::LAYER_TYPE_VPS_PASISHTA => 'layer_vps_merg',
    Config::LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI => 'layer_vps_gaski_chervenogushi',
    Config::LAYER_TYPE_VPS_GASKI_ZIMNI => 'layer_vps_gaski_zimni',
    Config::LAYER_TYPE_VPS_LIVADEN_BLATAR => 'layer_vps_livaden_blatar',
    Config::LAYER_TYPE_VPS_ORLI_LESHOYADI => 'layer_vps_orli_leshoyadi',
];

$GLOBALS['Layers']['remoteTables'] = [
    Config::LAYER_TYPE_PERMANETELY_GREEN_AREAS => 'layer_pzp',
    Config::LAYER_TYPE_ALLOWABLE => 'layer_allowable',
    Config::LAYER_TYPE_ALLOWABLE_FINAL => 'layer_allowable_final',
    Config::LAYER_TYPE_LFA => 'layer_lfa',
    Config::LAYER_TYPE_NATURA_2000 => 'layer_natura_2000',
    Config::LAYER_TYPE_VPS_PASISHTA => 'layer_vps_merg',
    Config::LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI => 'layer_vps_gaski_chervenogushi',
    Config::LAYER_TYPE_VPS_GASKI_ZIMNI => 'layer_vps_gaski_zimni',
    Config::LAYER_TYPE_VPS_LIVADEN_BLATAR => 'layer_vps_livaden_blatar',
    Config::LAYER_TYPE_VPS_ORLI_LESHOYADI => 'layer_vps_orli_leshoyadi',

    Config::LAYER_TYPE_LANDSCAPE_ELEMENTS_POINTS => 'layer_landscape_elements_points',
    Config::LAYER_TYPE_LANDSCAPE_ELEMENTS_LINES => 'layer_landscape_elements_lines',
    Config::LAYER_TYPE_LANDSCAPE_ELEMENTS_POLYGONS => 'layer_landscape_elements_polygons',
    Config::LAYER_TYPE_PHYSICAL_BLOCKS_PRELIMINARY => 'layer_physical_blocks_preliminary',
    Config::LAYER_TYPE_PERMANENT_GRASSLAND_FOR_MOWING_PRELIMINARY => 'layer_permanent_grassland_for_mowing_preliminary',
    Config::LAYER_TYPE_PERMANENT_GRASSLAND_FOR_MOWING => 'layer_permanent_grassland_for_mowing',
    Config::LAYER_TYPE_DS_PRC => 'layer_ds_prc',
    // added to fix the issue with "Физически блокове 2023 г. - окончателен" layer name from TF\Engine\APIClasses\Diary\MapLayersTree.
    Config::LAYER_TYPE_ALLOWABLE_DRAFT => 'layer_allowable_draft',
];

$GLOBALS['Layers']['srid'] = [
    0 => ['title' => 'Земеделски парцели', 'srid' => 32635, 'id' => 0, 'type' => 1],
    1 => ['title' => 'Временни данни', 'srid' => 4326, 'id' => 1, 'type' => 2],
    2 => ['title' => 'Слой за допустимост', 'srid' => 32635, 'id' => 2, 'type' => 3],
    3 => ['title' => 'Данни от комасация', 'srid' => 32635, 'id' => 3, 'type' => 4],
    4 => ['title' => 'КВС имоти', 'srid' => 32635, 'id' => 4, 'type' => 5],
    5 => ['title' => 'От ИСАК', 'srid' => 32635, 'id' => 5, 'type' => 6],
    6 => ['title' => 'Собствен растер', 'srid' => 32635, 'id' => 6, 'type' => 7],
    7 => ['title' => 'Сателитен работен слой', 'srid' => 32635, 'id' => 7, 'type' => 8],
    8 => ['title' => 'За ИСАК', 'srid' => 32635, 'id' => 8, 'type' => 9],
    9 => ['title' => 'Необлагодетелствани райони', 'srid' => 32635, 'id' => 9, 'type' => 10],
    10 => ['title' => 'Натура 2000', 'srid' => 32635, 'id' => 10, 'type' => 11],
    11 => ['title' => 'Постоянно затревени площи', 'srid' => 32635, 'id' => 11, 'type' => 12],
    12 => ['title' => 'Данни от комасация от други софтуери', 'srid' => 32635, 'id' => 12, 'type' => 13],
    13 => ['title' => 'КВС - Официални данни от ОСЗ', 'srid' => 32635, 'id' => 18, 'type' => 18],
    14 => ['title' => 'Работен слой', 'srid' => 4326, 'id' => 19, 'type' => 19],
    15 => ['title' => 'Импорт Ексел', 'srid' => 4326, 'id' => 23, 'type' => 23],
    16 => ['title' => 'Изходни данни за споразумение', 'srid' => '', 'id' => 28, 'type' => 28],
];

$GLOBALS['Layers']['maps'] = [
    0 => ['name' => 'GeoSCAN', 'id' => '9'],
    1 => ['name' => 'Bing: Aerial With Labels', 'id' => '1'],
    2 => ['name' => 'Bing: Aerial', 'id' => '2'],
    3 => ['name' => 'Bing: Road', 'id' => '3'],
    4 => ['name' => 'Google: TERRAIN', 'id' => '4'],
    5 => ['name' => 'Google', 'id' => '5'],
    6 => ['name' => 'Google: HYBRID', 'id' => '6'],
    7 => ['name' => 'Google: SATELLITE', 'id' => '7'],
    8 => ['name' => 'Open Street Map', 'id' => '8'],
];

$GLOBALS['Layers']['device'] = [
    0 => ['title' => 'Topcon', 'id' => '1'],
    1 => ['title' => 'Trimble', 'id' => '2'],
    // device type = 3 is osz
    2 => ['title' => 'Mueller', 'id' => '4'],
];

$GLOBALS['Layers']['KVSUpdateActionTypes'] = [
    0 => ['name' => 'Делба', 'id' => '1'],
    1 => ['name' => 'Обединение', 'id' => '2'],
    3 => ['name' => 'Промяна на граници', 'id' => '3'],
];

$GLOBALS['Layers']['labelNames'] = [
    Config::LAYER_TYPE_ZP => [
        'area_name' => 'Име на парцела',
        'isak_prc_uin' => 'ИСАК номер',
        'ekatte' => 'ЕКАТТЕ',
        'culture' => 'Култура',
        'virtual_crop_name' => 'Земеделска култура',
        'obrabotki' => 'Обработки',
        'dobivi' => 'Добиви',
        'napoqvane' => 'Напояване',
        'polivki' => 'Поливки',
        'polzvatel' => 'Ползвател',
        'area_zp' => 'Площ',
        'virtual_area_dka' => 'Площ (дка)',
    ],
    Config::LAYER_TYPE_GPS => [
        'plot_name' => 'Име на парцел',
        'area' => 'Площ(ха)',
        'plot_info' => 'Коментар',
    ],
    Config::LAYER_TYPE_KMS => [
        'area' => 'Площ (дка)',
        'virtual_area_dka' => 'Площ (дка)',
        'crop_code' => 'Земеделска култура',
        'virtual_crop_name' => 'Земеделска култура',
        'ekatte' => 'Землище',
        'name' => 'Номер БЗЗ',
    ],
    Config::LAYER_TYPE_KVS => [
        'ekate' => 'EKATTE',
        'masiv' => 'Масив',
        'number' => 'Имот',
        'mestnost' => 'Местност',
        'category' => 'Категория',
        'area_type' => 'НТП',
        'used_area' => 'Използвана площ',
        'area_kvs' => 'Обща площ',
        'document_area' => 'Площ по документ',
        'irrigated_area' => 'Поливна площ',
        'kad_ident' => 'Идентификатор',
        'masiv_imot' => 'Масив.Имот',
    ],
    Config::LAYER_TYPE_ISAK => [
        'prc_uin' => 'Идентификатор',
        'area' => 'Площ(ха)',
        'watering' => 'Напояване',
        'culture' => 'Култура',
        'virtual_crop_name' => 'Земеделска култура',
        'ekatte' => 'ЕКАТТЕ',
    ],
    Config::LAYER_TYPE_SATELLITE_WORK => [
    ],
    Config::LAYER_TYPE_FOR_ISAK => [
        'land' => 'Землище',
        'prc_name' => 'Име на парцел',
        'cropname' => 'Култура',
        'schema' => 'Схеми/Mерки',
        'area' => 'Площ(ха)',
        'ekatte' => 'ЕКАТТЕ',
        'edited' => 'За редактиране',
        'comment' => 'Коментар',
    ],
    Config::LAYER_TYPE_LFA => [
        'zemlishte' => 'Землище',
        'ekate' => 'ЕКАТТЕ',
        'lfa_area_type' => 'Вид НР',
    ],
    Config::LAYER_TYPE_NATURA_2000 => [
        'zone_name' => 'Име на зона',
        'area_z_dka' => 'Площ(дка)',
        'zapoved_no' => 'Заповед за обявяване на зоната',
        'dv' => 'Държавен вестник',
        'sitecode' => 'Код на зоната',
        'bans' => 'Подпомагани забрани',
    ],
    Config::LAYER_TYPE_PERMANETELY_GREEN_AREAS => [
        'imotcode' => 'Идентификатор',
        'zeml' => 'Землище',
        'sharea' => 'Площ на имота(дка)',
        'pzp_area' => 'Площ на имота в ПЗП(дка)',
    ],
    Config::LAYER_TYPE_VPS_PASISHTA => [
    ],
    Config::LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI => [
        'name' => 'Име на землище',
        'ekatte' => 'ЕКАТТЕ',
        'vps_type' => 'Вид ВПС',
    ],
    Config::LAYER_TYPE_VPS_GASKI_ZIMNI => [
        'zem' => 'Име на землище',
        'ekatte' => 'ЕКАТТЕ',
        'vps_type' => 'Вид ВПС',
    ],
    Config::LAYER_TYPE_VPS_LIVADEN_BLATAR => [
        'ime' => 'Име на община',
        'vps_type' => 'Вид ВПС',
    ],
    Config::LAYER_TYPE_VPS_ORLI_LESHOYADI => [
        'blockuin' => 'Номер на физически блок',
        'ekatte_name' => 'Землище',
        'ntp' => 'НТП',
        'vps_type' => 'Вид ВПС',
    ],
    Config::LAYER_TYPE_ALLOWABLE_FINAL => [
        'ntp' => 'НТП',
        'elgarea' => 'Площ(ха)',
        'area_zp' => 'Землище',
        'fbident' => 'Физически блок',
        'fb_area' => 'Площ на физически блок (ха)',
    ],
    Config::LAYER_TYPE_PHYSICAL_BLOCKS => [
        'ntp' => 'НТП',
        'elgarea' => 'Площ(ха)',
        'area_zp' => 'Землище',
        'fbident' => 'Физически блок',
        'fb_area' => 'Площ на физически блок (ха)',
    ],
    Config::LAYER_TYPE_KVS_OSZ => [
    ],
];
$GLOBALS['Layers']['colors'] = [
    0 => ['hex' => '#2B81E0', 'rgb' => 'rgb(43,129,224)', 'map_serv_rgb' => '43 129 224'],
    1 => ['hex' => '#E94909', 'rgb' => 'rgb(233,73,9)', 'map_serv_rgb' => '233 73 9'],
    2 => ['hex' => '#2EBE37', 'rgb' => 'rgb(46,190,55)', 'map_serv_rgb' => '46 190 55'],
    3 => ['hex' => '#5A2B3C', 'rgb' => 'rgb(90,43,60)', 'map_serv_rgb' => '90 43 60'],
    4 => ['hex' => '#048977', 'rgb' => 'rgb(4,137,119)', 'map_serv_rgb' => '4 137 119'],
    5 => ['hex' => '#AD25A5', 'rgb' => 'rgb(173,37,165)', 'map_serv_rgb' => '173 37 165'],
    6 => ['hex' => '#D2AE53', 'rgb' => 'rgb(210,174,83)', 'map_serv_rgb' => '210 174 83'],
    7 => ['hex' => '#E1A1E2', 'rgb' => 'rgb(225,161,226)', 'map_serv_rgb' => '225 161 226'],
    8 => ['hex' => '#B00D41', 'rgb' => 'rgb(176,13,65)', 'map_serv_rgb' => '176 13 65'],
    9 => ['hex' => '#505100', 'rgb' => 'rgb(80,81,0)', 'map_serv_rgb' => '80 81 0'],
    10 => ['hex' => '#F0836A', 'rgb' => 'rgb(240,131,106)', 'map_serv_rgb' => '240 131 106'],
    11 => ['hex' => '#203E75', 'rgb' => 'rgb(32,62,117)', 'map_serv_rgb' => '32 62 117'],
    12 => ['hex' => '#53BDF1', 'rgb' => 'rgb(83,189,241)', 'map_serv_rgb' => '83 189 241'],
    13 => ['hex' => '#1B4147', 'rgb' => 'rgb(27,65,71)', 'map_serv_rgb' => '27 65 71'],
    14 => ['hex' => '#8E6FF5', 'rgb' => 'rgb(142,111,245)', 'map_serv_rgb' => '142 111 245'],
    15 => ['hex' => '#22D397', 'rgb' => 'rgb(34,211,151)', 'map_serv_rgb' => '34 211 151'],
    16 => ['hex' => '#984D14', 'rgb' => 'rgb(152,77,20)', 'map_serv_rgb' => '152 77 20'],
    17 => ['hex' => '#8DA61C', 'rgb' => 'rgb(141,166,28)', 'map_serv_rgb' => '141 166 28'],
    18 => ['hex' => '#01441A', 'rgb' => 'rgb(1,68,26)', 'map_serv_rgb' => '1 68 26'],
    19 => ['hex' => '#F48D2F', 'rgb' => 'rgb(244,141,47)', 'map_serv_rgb' => '244 141 47'],
    20 => ['hex' => '#F877AF', 'rgb' => 'rgb(248,119,175)', 'map_serv_rgb' => '248 119 175'],
    21 => ['hex' => '#107091', 'rgb' => 'rgb(16,112,145)', 'map_serv_rgb' => '16 112 145'],
    22 => ['hex' => '#3E9E55', 'rgb' => 'rgb(62,158,85)', 'map_serv_rgb' => '62 158 85'],
    23 => ['hex' => '#AAAFDA', 'rgb' => 'rgb(170,175,218)', 'map_serv_rgb' => '170 175 218'],
    24 => ['hex' => '#97B861', 'rgb' => 'rgb(151,184,97)', 'map_serv_rgb' => '151 184 97'],
    25 => ['hex' => '#603120', 'rgb' => 'rgb(96,49,32)', 'map_serv_rgb' => '96 49 32'],
    26 => ['hex' => '#F33051', 'rgb' => 'rgb(243,48,81)', 'map_serv_rgb' => '243 48 81'],
    27 => ['hex' => '#E07A86', 'rgb' => 'rgb(224,122,134)', 'map_serv_rgb' => '224 122 134'],
    28 => ['hex' => '#AF2424', 'rgb' => 'rgb(175,36,36)', 'map_serv_rgb' => '175 36 36'],
    29 => ['hex' => '#6CC49D', 'rgb' => 'rgb(108,196,157)', 'map_serv_rgb' => '108 196 157'],
    30 => ['hex' => '#35388C', 'rgb' => 'rgb(53,56,140)', 'map_serv_rgb' => '53 56 140'],
    31 => ['hex' => '#9E207C', 'rgb' => 'rgb(158,32,124)', 'map_serv_rgb' => '158 32 124'],
    32 => ['hex' => '#2597A5', 'rgb' => 'rgb(37,151,165)', 'map_serv_rgb' => '37 151 165'],
    33 => ['hex' => '#8E8506', 'rgb' => 'rgb(142,133,6)', 'map_serv_rgb' => '142 133 6'],
    34 => ['hex' => '#8F93FB', 'rgb' => 'rgb(143,147,251)', 'map_serv_rgb' => '143 147 251'],
    35 => ['hex' => '#883DB1', 'rgb' => 'rgb(136,61,177)', 'map_serv_rgb' => '136 61 177'],
    36 => ['hex' => '#4E3908', 'rgb' => 'rgb(78,57,8)', 'map_serv_rgb' => '78 57 8'],
    37 => ['hex' => '#EDA35B', 'rgb' => 'rgb(237,163,91)', 'map_serv_rgb' => '237 163 91'],
    38 => ['hex' => '#4C316B', 'rgb' => 'rgb(76,49,107)', 'map_serv_rgb' => '76 49 107'],
    39 => ['hex' => '#5DAD1C', 'rgb' => 'rgb(93,173,28)', 'map_serv_rgb' => '93 173 28'],
    40 => ['hex' => '#1DAF4B', 'rgb' => 'rgb(29,175,75)', 'map_serv_rgb' => '29 175 75'],
    41 => ['hex' => '#0A8FBF', 'rgb' => 'rgb(10,143,191)', 'map_serv_rgb' => '10 143 191'],
    42 => ['hex' => '#C39B5F', 'rgb' => 'rgb(195,155,95)', 'map_serv_rgb' => '195 155 95'],
    43 => ['hex' => '#AC841C', 'rgb' => 'rgb(172,132,28)', 'map_serv_rgb' => '172 132 28'],
    44 => ['hex' => '#C56A1A', 'rgb' => 'rgb(197,106,26)', 'map_serv_rgb' => '197 106 26'],
    45 => ['hex' => '#6E1E3C', 'rgb' => 'rgb(110,30,60)', 'map_serv_rgb' => '110 30 60'],
    46 => ['hex' => '#AB0051', 'rgb' => 'rgb(171,0,81)', 'map_serv_rgb' => '171 0 81'],
    47 => ['hex' => '#5368EE', 'rgb' => 'rgb(83,104,238)', 'map_serv_rgb' => '83 104 238'],
    48 => ['hex' => '#DDA0F6', 'rgb' => 'rgb(221,160,246)', 'map_serv_rgb' => '221 160 246'],
    49 => ['hex' => '#787878', 'rgb' => 'rgb(120,120,120)', 'map_serv_rgb' => '120 120 120'],
];

$GLOBALS['Layers']['palette2'] = [
    0 => ['hex' => '#87CEFA', 'rgb' => 'rgb(135,206,250)', 'map_serv_rgb' => '135 206 250'],
    1 => ['hex' => '#778899', 'rgb' => 'rgb(119,136,153)', 'map_serv_rgb' => '119 136 153'],
    2 => ['hex' => '#B0C4DE', 'rgb' => 'rgb(176,196,222)', 'map_serv_rgb' => '176 196 222'],
    3 => ['hex' => '#32CD32', 'rgb' => 'rgb(50,205,50)', 'map_serv_rgb' => '50 205 50'],
    4 => ['hex' => '#FAF0E6', 'rgb' => 'rgb(250,240,230)', 'map_serv_rgb' => '250 240 230'],
    5 => ['hex' => '#800000', 'rgb' => 'rgb(128,0,0)', 'map_serv_rgb' => '128 0 0'],
    6 => ['hex' => '#66CDAA', 'rgb' => 'rgb(102,205,170)', 'map_serv_rgb' => '102 205 170'],
    7 => ['hex' => '#0000CD', 'rgb' => 'rgb(0,0,205)', 'map_serv_rgb' => '0 0 205'],
    8 => ['hex' => '#BA55D3', 'rgb' => 'rgb(186,85,211)', 'map_serv_rgb' => '186 85 211'],
    9 => ['hex' => '#9370DB', 'rgb' => 'rgb(147,112,219)', 'map_serv_rgb' => '147 112 219'],
    10 => ['hex' => '#3CB371', 'rgb' => 'rgb(60,179,113)', 'map_serv_rgb' => '60 179 113'],
    11 => ['hex' => '#7B68EE', 'rgb' => 'rgb(123,104,238)', 'map_serv_rgb' => '123 104 238'],
    12 => ['hex' => '#48D1CC', 'rgb' => 'rgb(72,209,204)', 'map_serv_rgb' => '72 209 204'],
    13 => ['hex' => '#C71585', 'rgb' => 'rgb(199,21,133)', 'map_serv_rgb' => '199 21 133'],
    14 => ['hex' => '#191970', 'rgb' => 'rgb(25,25,112)', 'map_serv_rgb' => '25 25 112'],
    15 => ['hex' => '#808000', 'rgb' => 'rgb(128,128,0)', 'map_serv_rgb' => '128 128 0'],
    16 => ['hex' => '#6B8E23', 'rgb' => 'rgb(107,142,35)', 'map_serv_rgb' => '107 142 35'],
    17 => ['hex' => '#FFA500', 'rgb' => 'rgb(255,165,0)', 'map_serv_rgb' => '255 165 0'],
    18 => ['hex' => '#FF4500', 'rgb' => 'rgb(255,69,0)', 'map_serv_rgb' => '255 69 0'],
    19 => ['hex' => '#DA70D6', 'rgb' => 'rgb(218,112,214)', 'map_serv_rgb' => '218 112 214'],
    20 => ['hex' => '#EEE8AA', 'rgb' => 'rgb(238,232,170)', 'map_serv_rgb' => '238 232 170'],
    21 => ['hex' => '#98FB98', 'rgb' => 'rgb(152,251,152)', 'map_serv_rgb' => '152 251 152'],
    22 => ['hex' => '#AFEEEE', 'rgb' => 'rgb(175,238,238)', 'map_serv_rgb' => '175 238 238'],
    23 => ['hex' => '#DB7093', 'rgb' => 'rgb(219,112,147)', 'map_serv_rgb' => '219 112 147'],
    24 => ['hex' => '#CD853F', 'rgb' => 'rgb(205,133,63)', 'map_serv_rgb' => '205 133 63'],
    25 => ['hex' => '#FFC0CB', 'rgb' => 'rgb(255,192,203)', 'map_serv_rgb' => '255 192 203'],
    26 => ['hex' => '#DDA0DD', 'rgb' => 'rgb(221,160,221)', 'map_serv_rgb' => '221 160 221'],
    27 => ['hex' => '#B0E0E6', 'rgb' => 'rgb(176,224,230)', 'map_serv_rgb' => '176 224 230'],
    28 => ['hex' => '#800080', 'rgb' => 'rgb(128,0,128)', 'map_serv_rgb' => '128 0 128'],
    29 => ['hex' => '#FF0000', 'rgb' => 'rgb(255,0,0)', 'map_serv_rgb' => '255 0 0'],
    30 => ['hex' => '#BC8F8F', 'rgb' => 'rgb(188,143,143)', 'map_serv_rgb' => '188 143 143'],
    31 => ['hex' => '#4169E1', 'rgb' => 'rgb(65,105,225)', 'map_serv_rgb' => '65 105 225'],
    32 => ['hex' => '#8B4513', 'rgb' => 'rgb(139,69,19)', 'map_serv_rgb' => '139 69 19'],
    33 => ['hex' => '#FA8072', 'rgb' => 'rgb(250,128,114)', 'map_serv_rgb' => '250 128 114'],
    34 => ['hex' => '#F4A460', 'rgb' => 'rgb(244,164,96)', 'map_serv_rgb' => '244 164 96'],
    35 => ['hex' => '#2E8B57', 'rgb' => 'rgb(46,139,87)', 'map_serv_rgb' => '46 139 87'],
    36 => ['hex' => '#A0522D', 'rgb' => 'rgb(160,82,45)', 'map_serv_rgb' => '160 82 45'],
    37 => ['hex' => '#C0C0C0', 'rgb' => 'rgb(192,192,192)', 'map_serv_rgb' => '192 192 192'],
    38 => ['hex' => '#87CEEB', 'rgb' => 'rgb(135,206,235)', 'map_serv_rgb' => '135 206 235'],
    39 => ['hex' => '#6A5ACD', 'rgb' => 'rgb(106,90,205)', 'map_serv_rgb' => '106 90 205'],
    40 => ['hex' => '#708090', 'rgb' => 'rgb(112,128,144)', 'map_serv_rgb' => '112 128 144'],
    41 => ['hex' => '#4682B4', 'rgb' => 'rgb(70,130,180)', 'map_serv_rgb' => '70 130 180'],
    42 => ['hex' => '#D2B48C', 'rgb' => 'rgb(210,180,140)', 'map_serv_rgb' => '210 180 140'],
    43 => ['hex' => '#008080', 'rgb' => 'rgb(0,128,128)', 'map_serv_rgb' => '0 128 128'],
    44 => ['hex' => '#D8BFD8', 'rgb' => 'rgb(216,191,216)', 'map_serv_rgb' => '216 191 216'],
    45 => ['hex' => '#FF6347', 'rgb' => 'rgb(255,99,71)', 'map_serv_rgb' => '255 99 71'],
    46 => ['hex' => '#40E0D0', 'rgb' => 'rgb(64,224,208)', 'map_serv_rgb' => '64 224 208'],
    47 => ['hex' => '#EE82EE', 'rgb' => 'rgb(238,130,238)', 'map_serv_rgb' => '238 130 238'],
    48 => ['hex' => '#FFFF00', 'rgb' => 'rgb(255,255,0)', 'map_serv_rgb' => '255 255 0'],
    49 => ['hex' => '#9ACD32', 'rgb' => 'rgb(154,205,50', 'map_serv_rgb' => '154 205 5'],
];

$GLOBALS['Layers']['palette3'] = [
    0 => ['hex' => '#FAEBD7', 'rgb' => 'rgb(250,235,215)', 'map_serv_rgb' => '250 235 215'],
    1 => ['hex' => '#7FFFD4', 'rgb' => 'rgb(127,255,212)', 'map_serv_rgb' => '127 255 212'],
    2 => ['hex' => '#0000FF', 'rgb' => 'rgb(0,0,255)', 'map_serv_rgb' => '0 0 255'],
    3 => ['hex' => '#8A2BE2', 'rgb' => 'rgb(138,43,226)', 'map_serv_rgb' => '138 43 226'],
    4 => ['hex' => '#A52A2A', 'rgb' => 'rgb(165,42,42)', 'map_serv_rgb' => '165 42 42'],
    5 => ['hex' => '#5F9EA0', 'rgb' => 'rgb(95,158,160)', 'map_serv_rgb' => '95 158 160'],
    6 => ['hex' => '#7FFF00', 'rgb' => 'rgb(127,255,0)', 'map_serv_rgb' => '127 255 0'],
    7 => ['hex' => '#D2691E', 'rgb' => 'rgb(210,105,30)', 'map_serv_rgb' => '210 105 30'],
    8 => ['hex' => '#FF7F50', 'rgb' => 'rgb(255,127,80)', 'map_serv_rgb' => '255 127 80'],
    9 => ['hex' => '#6495ED', 'rgb' => 'rgb(100,149,237)', 'map_serv_rgb' => '100 149 237'],
    10 => ['hex' => '#DC143C', 'rgb' => 'rgb(220,20,60)', 'map_serv_rgb' => '220 20 60'],
    11 => ['hex' => '#00FFFF', 'rgb' => 'rgb(0,255,255)', 'map_serv_rgb' => '0 255 255'],
    12 => ['hex' => '#00008B', 'rgb' => 'rgb(0,0,139)', 'map_serv_rgb' => '0 0 139'],
    13 => ['hex' => '#008B8B', 'rgb' => 'rgb(0,139,139)', 'map_serv_rgb' => '0 139 139'],
    14 => ['hex' => '#B8860B', 'rgb' => 'rgb(184,134,11)', 'map_serv_rgb' => '184 134 11'],
    15 => ['hex' => '#A9A9A9', 'rgb' => 'rgb(169,169,169)', 'map_serv_rgb' => '169 169 169'],
    16 => ['hex' => '#006400', 'rgb' => 'rgb(0,100,0)', 'map_serv_rgb' => '0 100 0'],
    17 => ['hex' => '#BDB76B', 'rgb' => 'rgb(189,183,107)', 'map_serv_rgb' => '189 183 107'],
    18 => ['hex' => '#8B008B', 'rgb' => 'rgb(139,0,139)', 'map_serv_rgb' => '139 0 139'],
    19 => ['hex' => '#556B2F', 'rgb' => 'rgb(85,107,47)', 'map_serv_rgb' => '85 107 47'],
    20 => ['hex' => '#FF8C00', 'rgb' => 'rgb(255,140,0)', 'map_serv_rgb' => '255 140 0'],
    21 => ['hex' => '#9932CC', 'rgb' => 'rgb(153,50,204)', 'map_serv_rgb' => '153 50 204'],
    22 => ['hex' => '#8B0000', 'rgb' => 'rgb(139,0,0)', 'map_serv_rgb' => '139 0 0'],
    23 => ['hex' => '#E9967A', 'rgb' => 'rgb(233,150,122)', 'map_serv_rgb' => '233 150 122'],
    24 => ['hex' => '#8FBC8F', 'rgb' => 'rgb(143,188,143)', 'map_serv_rgb' => '143 188 143'],
    25 => ['hex' => '#483D8B', 'rgb' => 'rgb(72,61,139)', 'map_serv_rgb' => '72 61 139'],
    26 => ['hex' => '#2F4F4F', 'rgb' => 'rgb(47,79,79)', 'map_serv_rgb' => '47 79 79'],
    27 => ['hex' => '#00CED1', 'rgb' => 'rgb(0,206,209)', 'map_serv_rgb' => '0 206 209'],
    28 => ['hex' => '#9400D3', 'rgb' => 'rgb(148,0,211)', 'map_serv_rgb' => '148 0 211'],
    29 => ['hex' => '#FF1493', 'rgb' => 'rgb(255,20,147)', 'map_serv_rgb' => '255 20 147'],
    30 => ['hex' => '#696969', 'rgb' => 'rgb(105,105,105)', 'map_serv_rgb' => '105 105 105'],
    31 => ['hex' => '#1E90FF', 'rgb' => 'rgb(30,144,255)', 'map_serv_rgb' => '30 144 255'],
    32 => ['hex' => '#B22222', 'rgb' => 'rgb(178,34,34)', 'map_serv_rgb' => '178 34 34'],
    33 => ['hex' => '#228B22', 'rgb' => 'rgb(34,139,34)', 'map_serv_rgb' => '34 139 34'],
    34 => ['hex' => '#FF00FF', 'rgb' => 'rgb(255,0,255)', 'map_serv_rgb' => '255 0 255'],
    35 => ['hex' => '#FFD700', 'rgb' => 'rgb(255,215,0)', 'map_serv_rgb' => '255 215 0'],
    36 => ['hex' => '#DAA520', 'rgb' => 'rgb(218,165,32)', 'map_serv_rgb' => '218 165 32'],
    37 => ['hex' => '#808080', 'rgb' => 'rgb(128,128,128)', 'map_serv_rgb' => '128 128 128'],
    38 => ['hex' => '#008000', 'rgb' => 'rgb(0,128,0)', 'map_serv_rgb' => '0 128 0'],
    39 => ['hex' => '#ADFF2F', 'rgb' => 'rgb(173,255,47)', 'map_serv_rgb' => '173 255 47'],
    40 => ['hex' => '#FF69B4', 'rgb' => 'rgb(255,105,180)', 'map_serv_rgb' => '255 105 180'],
    41 => ['hex' => '#CD5C5C', 'rgb' => 'rgb(205,92,92)', 'map_serv_rgb' => '205 92 92'],
    42 => ['hex' => '#4B0082', 'rgb' => 'rgb(75,0,130)', 'map_serv_rgb' => '75 0 130'],
    43 => ['hex' => '#F0E68C', 'rgb' => 'rgb(240,230,140)', 'map_serv_rgb' => '240 230 140'],
    44 => ['hex' => '#7CFC00', 'rgb' => 'rgb(124,252,0)', 'map_serv_rgb' => '124 252 0'],
    45 => ['hex' => '#ADD8E6', 'rgb' => 'rgb(173,216,230)', 'map_serv_rgb' => '173 216 230'],
    46 => ['hex' => '#90EE90', 'rgb' => 'rgb(144,238,144)', 'map_serv_rgb' => '144 238 144'],
    47 => ['hex' => '#FFB6C1', 'rgb' => 'rgb(255,182,193)', 'map_serv_rgb' => '255 182 193'],
    48 => ['hex' => '#FFA07A', 'rgb' => 'rgb(255,160,122)', 'map_serv_rgb' => '255 160 122'],
    49 => ['hex' => '#20B2AA', 'rgb' => 'rgb(32,178,170)', 'map_serv_rgb' => '32 178 170'],
];

$GLOBALS['Layers']['columnTypesByCategory'] = [
    Config::LAYER_COLUMN_CATEGORY_GID => 'SERIAL PRIMARY KEY',
    Config::LAYER_COLUMN_CATEGORY_NAME => 'VARCHAR',
    Config::LAYER_COLUMN_CATEGORY_GEOM => 'GEOMETRY',
    Config::LAYER_COLUMN_CATEGORY_CROP => 'VARCHAR',
    Config::LAYER_COLUMN_CATEGORY_EKATTE => 'VARCHAR',
    Config::LAYER_COLUMN_CATEGORY_NTP => 'VARCHAR',
    Config::LAYER_COLUMN_CATEGORY_SLOPE => 'DOUBLE PRECISION',
    Config::LAYER_COLUMN_CATEGORY_TEXT => 'VARCHAR',
    Config::LAYER_COLUMN_CATEGORY_NUMBER => 'NUMERIC',
    Config::LAYER_COLUMN_CATEGORY_BOOLEAN => 'BOOLEAN',
    Config::LAYER_COLUMN_CATEGORY_DATE => 'TIMESTAMP',
    Config::LAYER_COLUMN_CATEGORY_COLOR => 'VARCHAR',
    Config::LAYER_COLUMN_CATEGORY_LABEL => 'VARCHAR',
    Config::LAYER_COLUMN_CATEGORY_LEGAL_RIGHTS => 'VARCHAR',
    Config::LAYER_COLUMN_CATEGORY_CATEGORY => 'VARCHAR',
    Config::LAYER_COLUMN_CATEGORY_DECLARED_AREA_STATUS => 'VARCHAR',
];

$GLOBALS['Layers']['requiredDefinitionProps'] = [
    'col_name',
    'col_title',
    'col_visible',
    'col_category',
    'col_multiedit',
    'col_personalizable',
    'col_singleedit',
    'col_sortable',
    'col_exportable',
    'col_copyable',
    'col_virtual',
    'col_expression',
    'col_filter_selection_type',
    'col_reference',
];

/**
 * @var array Columns that have virtual columns
 */
$GLOBALS['Layers']['columnsWithVirtualColumns'] = [
    Config::LAYER_COLUMN_CATEGORY_CROP,
    Config::LAYER_COLUMN_CATEGORY_EKATTE,
    Config::LAYER_COLUMN_CATEGORY_NTP,
];

/**
 * @var array Virtual columns expresions
 *
 * Use replace string function, to replace string "replace_with_column_name" with the name of the column that will be set
 */
$GLOBALS['Layers']['virtualColumnsExpressions'] = [
    Config::LAYER_COLUMN_CATEGORY_EKATTE => 'get_ekatte_name_by_code(replace_with_column_name)',
    Config::LAYER_COLUMN_CATEGORY_CROP => 'get_crop_name_by_code(replace_with_column_name)',
    Config::LAYER_COLUMN_CATEGORY_NTP => 'get_ntp_title_by_code(replace_with_column_name)',
];

/**
 * @var array Categories for witch there might be more than one column
 */
$GLOBALS['Layers']['genericColumnCategories'] = [
    Config::LAYER_COLUMN_CATEGORY_BOOLEAN,
    Config::LAYER_COLUMN_CATEGORY_TEXT,
    Config::LAYER_COLUMN_CATEGORY_DATE,
    Config::LAYER_COLUMN_CATEGORY_NUMBER,
    Config::LAYER_COLUMN_CATEGORY_COLOR,
];

/**
 * @var array Layers in which we can copy fields
 */
$GLOBALS['Layers']['copyableLayers'] = [
    Config::LAYER_TYPE_GPS,
    Config::LAYER_TYPE_WORK_LAYER,
    Config::LAYER_TYPE_FOR_ISAK,
    Config::LAYER_TYPE_ZP,
    Config::LAYER_TYPE_CADASTRE,
];

$GLOBALS['Layers']['simplifyGeomTolerance'] = 0.005;

$GLOBALS['Layers']['layerName'] = [
    Config::LAYER_TYPE_KVS_OSZ => 'КВС имоти',
    Config::LAYER_TYPE_CSD => 'Заявления и декларации',
];

// Codes from $GLOBALS['Plots']['waytouse']. Some codes missing from $GLOBALS['Plots']['waytouse'] because they are old ones but have to be in additionalCodes array of some other code
$GLOBALS['Plots']['pmlNtpCodes'] = [1400, 1401, 1410, 1420, 2260, 2800, 1430, 2810, 1300, 1310, 2212, 1320, 1322, 1329, 1330, 1331, 1335, 1339, 2700, 2710, 2720, 2730];
