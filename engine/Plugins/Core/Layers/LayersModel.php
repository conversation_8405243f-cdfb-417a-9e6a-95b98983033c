<?php

namespace TF\Engine\Plugins\Core\Layers;

use PDO;
use TF\Application\Common\Config;
use TF\Application\Entity\LayerStyles;
use TF\Application\Entity\User;
use TF\Application\Entity\UserLayers;
use TF\Engine\Plugins\Core\Base\BaseModel;
use TF\Engine\Plugins\Core\Filter\FilterGroups;

/**
 * Base model class file
 * Every plugin extends this one.
 *
 * <AUTHOR> Ltd. <<EMAIL>>
 *
 * @link http://www.devision.bg/
 *
 * @copyright Copyright &copy; 2008 Devision Ltd.
 */
// Prado::using('Plugins.Core.Base.BaseModel');

/**
 * BaseModel class.
 *
 * Implements functionality to work with the database
 */
class LayersModel extends BaseModel
{
    use FilterGroups;

    public $tableUsers;
    public $tableFarming;
    public $tableNameFiles;
    public $layerTable;
    public $copyLayersTable;
    public $layerAllowableTable;

    public $tableUserEkatteRel = false;
    public $tableEkatte = false;
    public $tableOblasti = false;
    public $tableObshtini = false;
    public $tableKmetstva = false;

    /**
     * Constructor of the class.
     *
     * @param string $tableName - The name of the main table
     * @param string fieldName  - The relation field name
     * @param null|mixed $fieldName
     */
    public function __construct($tableName = null, $fieldName = null)
    {
        parent::__construct($tableName, $fieldName);

        $this->tableUsers = DEFAULT_DB_PREFIX . 'users';
        $this->tableFarming = DEFAULT_DB_PREFIX . 'users_farming';
        $this->tableNameFiles = DEFAULT_DB_PREFIX . 'users_files';
        $this->layerTable = DEFAULT_DB_PREFIX . 'users_layers';
        $this->copyLayersTable = DEFAULT_DB_PREFIX . 'layers_copy';
        $this->layerAllowableTable = 'layer_allowable';

        $this->tableUserEkatteRel = DEFAULT_DB_PREFIX . 'users_ekatte_rel';
        $this->tableEkatte = DEFAULT_DB_PREFIX . 'ekatte';
        $this->tableObshtini = DEFAULT_DB_PREFIX . 'obshtini';
        $this->tableOblasti = DEFAULT_DB_PREFIX . 'oblasti';
        $this->tableKmetstva = DEFAULT_DB_PREFIX . 'kmetstva';
    }

    public function getHomeItems(&$options = [])
    {
        $count = $options['count'];
        $return = $count ? 'COUNT(*)' : implode(', ', $options['return']);
        $customFields = $options['custom']['fields'];
        $customValues = $options['custom']['values'];
        $orderBy = $options['orderby'];
        $orderType = $options['ordertype'];
        $limit = $options['limit'];
        $offset = $options['offset'];
        $farming = $options['farming'];
        $name = $options['name'];
        $not_system = $options['not_system'];
        $layer_type = $options['layer_type'];

        $farm_id = ((int)$options['farm_id']) > 0 ? (int)$options['farm_id'] : 0;
        $year = (int)$options['year'];

        $userid = $options['user_id'];

        $table = $this->tableName;

        $sql = "SELECT {$return} FROM {$table} t";

        if ($farming) {
            $sql .= " LEFT JOIN {$this->tableFarming} f ON (f.id=t.farming)";
        }

        $sql .= ' WHERE TRUE';

        if ($year) {
            $sql .= ' AND t.year = :year';
        }

        if ($farm_id) {
            $sql .= ' AND t.farming = :fid';
        }

        if ($userid) {
            $sql .= ' AND t.user_id = :userid';
        }

        if ($name) {
            $sql .= ' AND t.name ~ :name';
        }

        if ($not_system) {
            $sql .= ' AND t.layer_type != 5 AND t.layer_type != 2';
        }

        if ($layer_type) {
            $sql .= ' AND t.layer_type = :layer_type';
        }

        if (!$count and !empty($orderType) and !empty($orderBy)) {
            if (empty($orderType)) {
                $orderType = 'ASC';
            }
            $sql .= " ORDER BY {$orderBy} {$orderType} ";
        }

        if (!$count and isset($limit) and isset($offset)) {
            $sql .= " LIMIT {$limit}";
            $sql .= " OFFSET {$offset}";
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($userid) {
            $cmd->bindParameter(':userid', $userid);
        }

        if ($farm_id) {
            $cmd->bindParameter(':fid', $farm_id);
        }

        if ($year) {
            $cmd->bindParameter(':year', $year);
        }

        if ($name) {
            $cmd->bindParameter(':name', $name);
        }

        if ($layer_type) {
            $cmd->bindParameter(':layer_type', $layer_type);
        }

        if ($count) {
            return $cmd->queryScalar();
        }

        return $cmd->query()->readAll();
    }

    public function getHomeFilesItems(&$options = [])
    {
        $count = $options['count'];
        $return = $count ? 'COUNT(*)' : implode(', ', $options['return']);
        $customFields = $options['custom']['fields'];
        $customValues = $options['custom']['values'];
        $orderBy = $options['orderby'];
        $orderType = $options['ordertype'];
        $limit = $options['limit'];
        $offset = $options['offset'];
        $farming = $options['farming'];
        $name = $options['name'];

        $farm_id = ((int)$options['farm_id']) > 0 ? (int)$options['farm_id'] : 0;
        $year = (int)$options['year'];

        $userid = $options['user_id'];

        $table = $this->tableNameFiles;

        $sql = "SELECT {$return} FROM {$table} t";

        if ($farming) {
            $sql .= " LEFT JOIN {$this->tableFarming} f ON (f.id=t.farming)";
        }

        $sql .= ' WHERE TRUE';

        if ($year) {
            $sql .= ' AND t.year = :year';
        }

        if ($farm_id) {
            $sql .= ' AND t.farming = :fid';
        }

        if ($userid) {
            $sql .= ' AND t.user_id = :userid';
        }

        if ($name) {
            $sql .= ' AND t.name ~ :name';
        }

        if (!$count and !empty($orderType) and !empty($orderBy)) {
            if (empty($orderType)) {
                $orderType = 'ASC';
            }
            $sql .= " ORDER BY {$orderBy} {$orderType} ";
        }

        if (!$count and isset($limit) and isset($offset)) {
            $sql .= " LIMIT {$limit}";
            $sql .= " OFFSET {$offset}";
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($userid) {
            $cmd->bindParameter(':userid', $userid);
        }

        if ($farm_id) {
            $cmd->bindParameter(':fid', $farm_id);
        }

        if ($year) {
            $cmd->bindParameter(':year', $year);
        }

        if ($name) {
            $cmd->bindParameter(':name', $name);
        }

        if ($count) {
            return $cmd->queryScalar();
        }

        return $cmd->query()->readAll();
    }

    public function setLayersPosition($source, $target, $point)
    {
        // source - moved element, target - targeted element, point - top or bottom of the targeted element
        $table = $this->tableName;
        $user_id = $source['user_id'];
        $new_pos = $target['position'];

        // bottom -> top dropdown
        if ($source['position'] > $target['position']) {
            // on top of element
            if ('top' == $point) {
                // updating the moved element
                $sql = "UPDATE {$table} SET ";
                if ($new_pos) {
                    $sql .= ' position = :position';
                }
                $sql .= ' WHERE true';
                if ($source['id']) {
                    $sql .= ' AND id = :id';
                }
                $cmd = $this->DbModule->createCommand($sql);
                if ($new_pos) {
                    $cmd->bindParameter(':position', $new_pos);
                }
                if ($source['id']) {
                    $cmd->bindParameter(':id', $source['id']);
                }
                $cmd->execute();

                $sql = '';
                // updating all others below him and above his previous position
                $sql = "UPDATE {$table} SET position = position + 1";
                $sql .= ' WHERE true';
                if ($new_pos) {
                    $sql .= ' AND position >= :pos';
                }
                if ($source['position']) {
                    $sql .= ' AND position <= :pos2';
                }
                if ($user_id) {
                    $sql .= ' AND user_id = :user_id';
                }
                if ($source['id']) {
                    $sql .= ' AND id <> :id';
                }
                $cmd = $this->DbModule->createCommand($sql);
                if ($new_pos) {
                    $cmd->bindParameter(':pos', $new_pos);
                }
                if ($source['position']) {
                    $cmd->bindParameter(':pos2', $source['position']);
                }
                if ($user_id) {
                    $cmd->bindParameter(':user_id', $user_id);
                }
                if ($source['id']) {
                    $cmd->bindParameter(':id', $source['id']);
                }
                $cmd->execute();
                // bottom of element
            } else {
                $sql = "UPDATE {$table} SET";
                if ($new_pos) {
                    $sql .= ' position = :position';
                }
                $sql .= ' WHERE true';
                if ($source['id']) {
                    $sql .= ' AND id = :id';
                }
                $cmd = $this->DbModule->createCommand($sql);
                if ($source['id']) {
                    $help_var = $new_pos + 1;
                    $cmd->bindParameter(':position', $help_var);
                }
                if ($source['id']) {
                    $cmd->bindParameter(':id', $source['id']);
                }
                $cmd->execute();

                $sql = '';
                $sql = "UPDATE {$table} SET position = position + 1 WHERE true";
                if ($new_pos) {
                    $sql .= ' AND position >= :pos';
                }
                if ($source['position']) {
                    $sql .= ' AND position <= :pos2';
                }
                if ($user_id) {
                    $sql .= ' AND user_id = :user_id';
                }
                if ($source['id']) {
                    $sql .= ' AND id <> :id';
                }
                $cmd = $this->DbModule->createCommand($sql);
                if ($new_pos) {
                    $help_var = $new_pos + 1;
                    $cmd->bindParameter(':pos', $help_var);
                }
                if ($source['position']) {
                    $cmd->bindParameter(':pos2', $source['position']);
                }
                if ($user_id) {
                    $cmd->bindParameter(':user_id', $user_id);
                }
                if ($source['id']) {
                    $cmd->bindParameter(':id', $source['id']);
                }
                $cmd->execute();
            }
            // top -> bottom dropdown
        } else {
            if ('top' == $point) {
                // updating the moved element
                $sql = "UPDATE {$table} SET";
                if ($new_pos) {
                    $sql .= ' position = :position';
                }
                $sql .= ' WHERE true';
                if ($source['id']) {
                    $sql .= ' AND id = :id';
                }
                $cmd = $this->DbModule->createCommand($sql);
                if ($new_pos) {
                    $help_var = $new_pos - 1;
                    $cmd->bindParameter(':position', $help_var);
                }
                if ($source['id']) {
                    $cmd->bindParameter(':id', $source['id']);
                }
                $cmd->execute();

                $sql = '';
                // updating the below him and above the position of the targeted element
                $sql = "UPDATE {$table} SET position = position - 1";
                $sql .= ' WHERE true';
                if ($source['position']) {
                    $sql .= ' AND position > :pos';
                }
                if ($target['position']) {
                    $sql .= ' AND position < :pos2';
                }
                if ($user_id) {
                    $sql .= ' AND user_id = :user_id';
                }
                if ($source['id']) {
                    $sql .= ' AND id <> :id';
                }
                $cmd = $this->DbModule->createCommand($sql);
                if ($source['position']) {
                    $cmd->bindParameter(':pos', $source['position']);
                }
                if ($target['position']) {
                    $cmd->bindParameter(':pos2', $target['position']);
                }
                if ($user_id) {
                    $cmd->bindParameter(':user_id', $user_id);
                }
                if ($source['id']) {
                    $cmd->bindParameter(':id', $source['id']);
                }
                $cmd->execute();
            } else {
                $sql = "UPDATE {$table} SET";
                if ($new_pos) {
                    $sql .= ' position = :position';
                }
                $sql .= ' WHERE true';
                if ($source['id']) {
                    $sql .= ' AND id = :id';
                }
                $cmd = $this->DbModule->createCommand($sql);
                if ($new_pos) {
                    $cmd->bindParameter(':position', $new_pos);
                }
                if ($source['id']) {
                    $cmd->bindParameter(':id', $source['id']);
                }
                $cmd->execute();

                $sql = '';
                $sql = "UPDATE {$table} SET position = position - 1";
                $sql .= ' WHERE true';
                if ($source['position']) {
                    $sql .= ' AND position > :pos';
                }
                if ($target['position']) {
                    $sql .= ' AND position <= :pos2';
                }
                if ($user_id) {
                    $sql .= ' AND user_id = :user_id';
                }
                if ($source['id']) {
                    $sql .= ' AND id <> :id';
                }
                $cmd = $this->DbModule->createCommand($sql);
                if ($source['position']) {
                    $cmd->bindParameter(':pos', $source['position']);
                }
                if ($target['position']) {
                    $cmd->bindParameter(':pos2', $target['position']);
                }
                if ($user_id) {
                    $cmd->bindParameter(':user_id', $user_id);
                }
                if ($source['id']) {
                    $cmd->bindParameter(':id', $source['id']);
                }
                $cmd->execute();
            }
        }
    }

    /**
     * shift all next layers positions.
     *
     * @param $layer_id , $user_id
     */
    public function shiftNextLayersPositions($layer_id, $user_id)
    {
        $table = $this->tableName;
        $sql = "SELECT position FROM {$table} WHERE true";
        if ($layer_id) {
            $sql .= ' AND id = :id';
        }
        $cmd = $this->DbModule->createCommand($sql);
        if ($layer_id) {
            $cmd->bindParameter(':id', $layer_id);
        }
        $results = $cmd->query()->read();

        // shifting
        $sql = "UPDATE {$table} SET position = position - 1 WHERE true";
        if ($results['position']) {
            $sql .= ' AND position > :pos';
        }
        if ($user_id) {
            $sql .= ' AND user_id = :user_id';
        }
        $sql .= ' AND status = 1';
        $cmd = $this->DbModule->createCommand($sql);
        if ($results['position']) {
            $cmd->bindParameter(':pos', $results['position']);
        }
        if ($user_id) {
            $cmd->bindParameter(':user_id', $user_id);
        }
        $cmd->execute();
    }

    public function getGreatestLayerPosition($user_id)
    {
        $table = $this->tableName;

        $sql = "SELECT max(position) as position FROM {$table} WHERE user_id = {$user_id} ";
        if ($user_id) {
            $sql .= ' AND user_id = :user_id';
        }

        $cmd = $this->DbModule->createCommand($sql);
        if ($user_id) {
            $cmd->bindParameter(':user_id', $user_id);
        }

        return $cmd->query()->read();
    }

    /**
     * Getting the data for required layer from database.
     */
    public function getLayerData($layer_id)
    {
        $table = $this->layerTable;

        $sql = "SELECT t.* FROM {$table} t WHERE t.id = :layer_id";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':layer_id', $layer_id);

        return $cmd->query()->read();
    }

    public function getFilesProcessingStatus($id)
    {
        $sql = "SELECT t.status FROM {$this->tableNameFiles} t WHERE t.id = :id";
        $cmd = $this->DbModule->createCommand($sql);

        $cmd->bindParameter(':id', $id);

        return $cmd->query()->read();
    }

    public function setFilesProcessingStatus($id, $status)
    {
        $sql = ' UPDATE ' . $this->tableNameFiles . ' SET status = :status WHERE id=:id';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':status', $status);
        $cmd->bindParameter(':id', $id);
        $cmd->execute();
    }

    public function getFilesForProcessing($fordefinition)
    {
        $table = $this->tableNameFiles;
        $tu = $this->tableUsers;
        $status = 0;

        if ($fordefinition) {
            $status = 11;
        }

        $sql = "SELECT t.*, tu.database FROM {$table} t INNER JOIN {$tu} tu ON(t.user_id=tu.id) WHERE t.status={$status} AND shape_type not in (2, 5, 7, 8, 9, 13, 18, 19, 22, 23, 28)
            ORDER BY t.date_uploaded ASC LIMIT 1";
        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function getKVSFilesForProcessing($fordefinition)
    {
        $table = $this->tableNameFiles;
        $tu = $this->tableUsers;
        $status = 0;

        if ($fordefinition) {
            $status = 11;
        }

        $sql = "SELECT t.*, tu.database FROM {$table} t INNER JOIN {$tu} tu ON(t.user_id=tu.id) WHERE t.status={$status}
            AND shape_type= 5  ORDER BY t.date_uploaded ASC LIMIT 1";

        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function getOszKVSFilesForProcessing()
    {
        $table = $this->tableNameFiles;
        $tu = $this->tableUsers;
        $maxKvsProcessingFiles = MAX_KVS_PROCESSING_FILES;

        $sql = "SELECT 
                    t.*,
                    tu.database 
                FROM {$table} t 
                INNER JOIN {$tu} tu ON(t.user_id=tu.id)
                WHERE t.status = 0
                  AND shape_type = 18 
                  AND (
                        SELECT COUNT(*) FROM {$table} WHERE status = 19 AND shape_type = 18 AND date_uploaded::date = CURRENT_DATE
                    ) <= {$maxKvsProcessingFiles}
                ORDER BY t.date_uploaded ASC 
                LIMIT 1";

        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function getKVSExcelFilesForProcessing()
    {
        $table = $this->tableNameFiles;
        $tu = $this->tableUsers;
        $shapeType = Config::LAYER_TYPE_EXCEL_IMPORT;
        $sql = "SELECT t.*, tu.database FROM {$table} t INNER JOIN {$tu} tu ON(t.user_id=tu.id) WHERE t.status=0
        AND shape_type = {$shapeType} ORDER BY t.date_uploaded ASC LIMIT 1";
        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->read();
    }

    public function getCsdFilesForProcessing()
    {
        $table = $this->tableNameFiles;
        $tu = $this->tableUsers;
        $shapeType = Config::LAYER_TYPE_CSD;

        $sql = "SELECT t.*, tu.database FROM {$table} t INNER JOIN {$tu} tu ON(t.group_id=tu.id) WHERE t.status=0
        AND shape_type = {$shapeType} ORDER BY t.date_uploaded ASC LIMIT 1";

        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function getFilesDataById($id)
    {
        $table = $this->tableNameFiles;
        $sql = "SELECT * FROM {$table} WHERE id = :id";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':id', $id);

        return $cmd->query()->readAll();
    }

    public function getActiveLayersByUserId($userid)
    {
        $table = $this->layerTable;
        $sql = "SELECT t.* FROM {$table} t WHERE (t.user_id = :uid) ORDER BY id ASC";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':uid', $userid);

        return $cmd->query()->readAll();
    }

    public function getKVSForProcessing()
    {
        // $table = $this->tableUsers;
        $sql = 'SELECT u.database, u.id, tr.id as itemid, tr.ekatte_id FROM ' . $this->tableUserEkatteRel . ' tr INNER JOIN ' . $this->tableEkatte . ' ekatte ON (ekatte.id=tr.ekatte_id)';
        $sql .= ' INNER JOIN ' . $this->tableUsers . ' u ON (tr.user_id = u.id)';
        $sql .= ' WHERE tr.is_proccessed =false';

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':uid', $userid);

        return $cmd->query()->readAll();
    }

    public function setKVSProccessed($id)
    {
        $sql = ' UPDATE ' . $this->tableUserEkatteRel . ' SET is_proccessed=true WHERE id=:id';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':id', $id);
        $cmd->execute();
    }

    public function setCopyProccessed($id)
    {
        $sql = ' UPDATE ' . $this->copyLayersTable . ' SET status=1 WHERE id=:id';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':id', $id);
        $cmd->execute();
    }

    public function copyKVSByEKATTE($ekatte)
    {
        $sql = 'DROP TABLE IF EXISTS layer_kvs;';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':ekatte', $ekatte);
        $cmd->execute();

        $sql .= ' create table layer_kvs as
                    select *
                    from layer_kvs_all
                    where ekatte_id = :ekatte';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':ekatte', $ekatte);
        $cmd->execute();
    }

    public function getTableNameByType($userid, $type)
    {
        $sql = 'SELECT table_name FROM ' . $this->layerTable . ' WHERE user_id = :uid AND layer_type = :ltype';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':uid', $userid);
        $cmd->bindParameter(':ltype', $type);
        $cmd->execute();

        return $cmd->queryScalar();
    }

    /**
     * FUNCTION FOR FILE ITEMS.
     */

    /**
     * returns all file items from database.
     *
     * @param $options - page, limit, order, offset
     * @param $counter (default FALSE) - shows if only count of all elements should be returned
     * @param $returnOnlySQL (default FALSE) - if only SQL query is required
     */
    public function getFiles($options, $counter, $returnOnlySQL)
    {
        $tablename = $this->tableNameFiles;

        if ($counter) {
            $return = 'COUNT(*)';
        } elseif ($options['return']) {
            $return = implode(', ', $options['return']);
        } else {
            $return = '*';
        }

        $sql = "SELECT {$return} FROM {$tablename} t LEFT JOIN {$this->tableFarming} f ON(f.id = t.farming) WHERE TRUE";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options && false == $counter) {
            if ($options['sort']) {
                $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
            }

            $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
            $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

            if (false !== $limit && false !== $offset) {
                $sql .= $returnOnlySQL
                    ? " LIMIT {$limit} OFFSET {$offset}"
                    : ' LIMIT :limit OFFSET :offset';
            }
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && false == $counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    /**
     * FUNCTION FOR LAYER ITEMS.
     */

    /**
     * return all layer items from database.
     *
     * @param $options - page, limit, order, offset
     * @param $counter (default FALSE) - shows if only count of all elements should be returned
     * @param $returnOnlySQL (default FALSE) - if only SQL query is required
     *
     * @return array|string
     */
    public function getLayers($options, $counter, $returnOnlySQL)
    {
        $tablename = $this->tableName;
        if ($counter) {
            $return = 'COUNT(*)';
        } elseif ($options['return']) {
            $return = implode(', ', $options['return']);
        } else {
            $return = '*';
        }
        // select everything , from su_user_layers joining the farms, where use_id , group_id, year and farm id
        $sql = "SELECT 
                    {$return} 
                FROM 
                    {$tablename} t 
                LEFT JOIN 
                    {$this->tableFarming} f ON (f.id = t.farming)"
                    . (isset($options['join_on_group_id']) ? " AND f.group_id = '{$options['join_on_group_id']}'" : '')
            . '';

        $sql .= ' WHERE TRUE';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options && false == $counter) {
            if (!empty($options['sort'])) {
                $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
            }

            $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
            $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

            if (false !== $limit && false !== $offset) {
                $sql .= $returnOnlySQL
                    ? " LIMIT {$limit} OFFSET {$offset}"
                    : ' LIMIT :limit OFFSET :offset';
            }
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && false == $counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    /**
     * @return UserLayers[]
     */
    public function getActiveLayersByGroupId($group_id)
    {
        return UserLayers::finder()->findAll('group_id = :group_id', [':group_id' => $group_id]);
    }

    /**
     * @return UserLayerStylesDTO[]
     */
    public function getActiveLayersWithStyles(User $user)
    {
        $conn = 'host=' . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' dbname=' . $user->database;

        $sql = "SELECT 
                sul.*,
                JSONB_AGG(row_to_json(layer_styles.*)) AS styles
            FROM 
                su_users_layers AS sul
            JOIN dblink(
                '{$conn}',
                $$ 
                    SELECT 
                        id, layer_id, sls.table_name, type, transparency, fill_column_name, fill_color, border_column_name, border_color,
                        border_width, border_only, labels, label_size, tags
                    FROM 
                        su_layer_styles as sls
                    JOIN pg_class
                        ON relname = sls.table_name -- ensure the table/view exists
                $$
            ) AS layer_styles (
                id INT4, layer_id VARCHAR, table_name VARCHAR, type VARCHAR, transparency INT4, fill_column_name VARCHAR, fill_color VARCHAR,
                border_column_name VARCHAR, border_color VARCHAR, border_width INT4, border_only BOOL, labels _TEXT, label_size INT4, tags BOOL
            )
                ON layer_styles.table_name = sul.table_name
            WHERE 
                group_id = :group_id
            GROUP BY
                sul.id;";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':group_id', $user->id);

        $data = $cmd->query()->readAll();

        return array_map(
            function ($item) {
                $userLayerStyles = new UserLayerStylesDTO();

                $userLayerStyles->styles = array_map(
                    fn ($style) => new LayerStyles($style),
                    json_decode($item['styles'], true)
                );

                unset($item['styles']);

                $userLayerStyles->userLayer = new UserLayers($item);

                return $userLayerStyles;
            },
            $data
        );
    }

    public function getTmpLayersForProcessing($fordefinition)
    {
        $table = $this->tableNameFiles;
        $tu = $this->tableUsers;
        $status = 0;

        if ($fordefinition) {
            $status = 11;
        }

        $sql = "SELECT t.*, tu.database FROM {$table} t INNER JOIN {$tu} tu ON(t.user_id=tu.id) WHERE t.status = {$status}
        AND shape_type = 2 ORDER BY t.date_uploaded ASC LIMIT 1";

        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function getWorkLayersForProcessing($fordefinition)
    {
        $table = $this->tableNameFiles;
        $tu = $this->tableUsers;
        $status = 0;

        if ($fordefinition) {
            $status = 11;
        }

        $sql = "SELECT t.*, tu.database FROM {$table} t INNER JOIN {$tu} tu ON(t.user_id=tu.id) WHERE t.status={$status}
        AND shape_type = 19 ORDER BY t.date_uploaded ASC LIMIT 1";
        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function updateUsersFilesCrs($id, $crs)
    {
        $sql = "UPDATE {$this->tableNameFiles} SET crs = :crs WHERE id = :id";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':crs', $crs);
        $cmd->bindParameter(':id', $id);

        return $cmd->execute();
    }

    public function updateUsersFilesEkate($id, $ekate)
    {
        $sql = "UPDATE {$this->tableNameFiles} SET ekate = :ekate WHERE id = :id";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':ekate', $ekate);
        $cmd->bindParameter(':id', $id);
        $cmd->execute();
    }

    /**
     * Getting the data for allowable layer from database.
     */
    public function getAllowableLayerData($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM {$options['tablename']} WHERE true";

        if ($options['where']) {
            foreach ($options['where'] as $constraint) {
                if ($constraint['value']) {
                    if (is_array($constraint['value'])) {
                        $sql = $sql . ' AND ' . $constraint['column'] . ' ' . $constraint['compare'] . ' (' . implode(
                            ',',
                            $constraint['value']
                        ) . ')';
                    } else {
                        $sql = $sql . ' AND ' . $constraint['column'] . ' ' . $constraint['compare'] . ' ' . $constraint['value'];
                    }
                }
            }
        }

        if (!empty($options['whereOrGroup'])) {
            $sql = $this->createWhereOrGroupSQL($sql, $options['whereOrGroup'], $returnOnlySQL);
        }

        if (!empty($options['whereOr'])) {
            $sql = $this->createWhereOrSQL($sql, $options['whereOr'], $returnOnlySQL);
        }

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule2->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && false == $counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if (!empty($options['whereOrGroup'])) {
            $this->createWhereGroupBinds($cmd, $options['whereOrGroup']);
        }

        if ($options['whereOr']) {
            $this->createWhereBinds($cmd, $options['whereOr']);
        }

        return $cmd->query()->readAll();
    }

    /**
     * Getting the data for layers on server login3 (natura2000, pzp, lfa) from database.
     */
    public function getRemoteLayerData(array $options, bool $counter, bool $returnOnlySQL, array $bindingParams = [])
    {
        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter'] ?? false);

        $sql = "SELECT {$return} FROM {$options['tablename']} WHERE true";

        if (!empty($options['where'])) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if (!empty($options['whereOrGroup'])) {
            $sql = $this->createWhereOrGroupSQL($sql, $options['whereOrGroup'], $returnOnlySQL);
        }

        if (!empty($options['whereOr'])) {
            $sql = $this->createWhereOrSQL($sql, $options['whereOr'], $returnOnlySQL);
        }

        if (!empty($options['group'])) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if (!empty($options['order']) && !empty($options['sort']) && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }
        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule2->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && false == $counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if (!empty($options['where'])) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        if (!empty($options['whereOrGroup'])) {
            $this->createWhereGroupBinds($cmd, $options['whereOrGroup']);
        }

        if (!empty($options['whereOr'])) {
            $this->createWhereBinds($cmd, $options['whereOr']);
        }

        if (count($bindingParams)) {
            foreach ($bindingParams as $key => $data) {
                $cmd->bindParameter($key, $data);
            }
        }

        return $cmd->query()->readAll();
    }

    public function getRemoteLayerDataWithoutTable($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} WHERE true";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule2->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && false == $counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    /**
     * Getting the data for layers on server login3 (natura2000, pzp, lfa) from database.
     */
    public function getRemoteLayerOrliLeshoyadiData($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM {$options['tablename']} l
        INNER JOIN {$this->tableEkatte} e on l.ekatte = e.ekatte_code
        WHERE true";

        if (!empty($options['where'])) {
            foreach ($options['where'] as $constraint) {
                if ($constraint['value']) {
                    if (is_array($constraint['value'])) {
                        $sql = $sql . ' AND ' . $constraint['column'] . ' ' . $constraint['compare'] . ' (' . implode(
                            ',',
                            $constraint['value']
                        ) . ')';
                    } else {
                        $sql = $sql . ' AND ' . $constraint['column'] . ' ' . $constraint['compare'] . ' ' . $constraint['value'];
                    }
                }
            }
        }

        if (!empty($options['whereOrGroup'])) {
            $sql = $this->createWhereOrGroupSQL($sql, $options['whereOrGroup'], $returnOnlySQL);
        }

        if (!empty($options['group'])) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if (!empty($options['order']) && !empty($options['sort']) && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];

            $layer = UserLayers::getLayerById($options['layer_id']);
            $layerIdColumnName = $layer->getDefinitionByCategory(Config::LAYER_COLUMN_CATEGORY_GID)['col_name'];

            if ($options['sort'] !== $layerIdColumnName) {
                $sql .= ", {$layerIdColumnName} " . $options['order'];
            }
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule2->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && false == $counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if (!empty($options['whereOrGroup'])) {
            $this->createWhereGroupBinds($cmd, $options['whereOrGroup']);
        }

        return $cmd->query()->readAll();
    }

    public function createReturnVariable($return = false, $counter, $custom_counter = false)
    {
        if ($counter) {
            if ($custom_counter) {
                $return = $custom_counter;
            } else {
                $return = 'COUNT(*)';
            }
        } elseif ($return) {
            $return = implode(',', $return);
        } else {
            $return = '*';
        }

        return $return;
    }

    public function getLayersCombobox($options)
    {
        $table = $this->layerTable;
        $layer_type = $options['layer_type'];
        $user_id = $options['user_id'];
        $years = $options['year'];
        $yearsCount = count($years);

        $sql = "SELECT ul.id as layer_id, ul.table_name as layer_table, ul.layer_type as layer_type, ul.name as layer_name, uf.name as farming_name, ul.year as farming_year
                FROM {$table} ul
                INNER JOIN su_users_farming uf on uf.id = ul.farming
                WHERE ul.layer_type = :layer_type
                and ul.user_id = :user_id
                and ul.year IN (";

        for ($i = 0; $i < $yearsCount; $i++) {
            if ($i == ($yearsCount - 1)) {
                $sql .= ':year' . $i;

                break;
            }

            $sql .= ':year' . $i . ', ';
        }

        $sql .= ')';

        if ($options['is_exist']) {
            $sql .= ' and ul.is_exist = true';
        }
        $sql .= ' order by ul.farming asc, layer_type asc, year asc';
        $cmd = $this->DbModule->createCommand($sql);

        $cmd->bindParameter(':layer_type', $layer_type);
        $cmd->bindParameter(':user_id', $user_id);

        for ($i = 0; $i < $yearsCount; $i++) {
            $cmd->bindParameter(':year' . $i, $years[$i]);
        }

        return $cmd->query()->readAll();
    }

    public function getFarmingNameByLayerId($layer_id)
    {
        $sql = 'SELECT f.name FROM su_users_layers l INNER JOIN ' . $this->tableFarming . ' f ON (l.farming = f.id) WHERE l.id = :layer_id';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':layer_id', $layer_id);

        return $cmd->query()->readAll();
    }

    public function getFarmInfoByTable($table)
    {
        $sql = 'SELECT
            suf.id AS farm_id
            , suf."name" AS farm_name
            , sul.id AS layer_id
            , sul."name" AS layer_name
        FROM
            su_users_layers sul
        LEFT JOIN su_users_farming suf ON
            suf.id = sul.farming
        WHERE
            sul.table_name = :table';

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':table', $table);
        $rows = $cmd->query()->readAll();
        if (!count($rows)) {
            return;
        }

        return $rows[0];
    }

    public function getColumnNameExistInDefaultDatabase($tablename, $columnName)
    {
        $sql = 'SELECT COUNT(*) as num FROM information_schema.columns ';
        $sql .= "where table_catalog = 'susi_main' and
                table_schema = 'public' and
                table_name = :tname and
                LOWER(column_name)=:cname";
        $cmd = $this->DbModule2->createCommand($sql);

        $cmd->bindParameter(':tname', $tablename);
        $cmd->bindParameter(':cname', $columnName);

        return $cmd->queryScalar();
    }

    /**
     * Get all layers by type.
     *
     * @param int $layer_type type of layer
     * @param int $user_id user id
     *
     * @return array all layers
     */
    public function getLayersByType($layer_type, $user_id)
    {
        $table = $this->layerTable;
        $table_farming = $this->tableFarming;

        $sql = "SELECT ul.id as layer_id, ul.table_name as layer_table, ul.layer_type as layer_type, ul.name as layer_name, uf.name as farming_name, ul.year as farming_year, ul.extent
                FROM {$table} ul
                INNER JOIN {$table_farming} uf on uf.id = ul.farming
                WHERE ul.layer_type = :layer_type and ul.user_id = :user_id and ul.is_exist IS TRUE
                ORDER BY farming, farming_year";

        $cmd = $this->DbModule->createCommand($sql);

        $cmd->bindParameter(':layer_type', $layer_type);
        $cmd->bindParameter(':user_id', $user_id);

        return $cmd->query()->readAll();
    }

    /**
     * Get geom by text and crs.
     *
     * @param string $polygonA Polygon in WKT format
     * @param string $polygonB Polygon in WKT format
     * @param int $crs The projection code
     *
     * @return bool
     */
    public function testForPolyIntersection($polygonA, $polygonB, $crs)
    {
        $sql = "SELECT ST_Area(ST_Intersection(ST_GeomFromText('{$polygonA}', {$crs}), ST_GeomFromText('{$polygonB}', {$crs}))) > 0";
        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->queryScalar();
    }

    /**
     * @param int $user_id
     * @param int $layerType
     * @param string $name
     *
     * @return array
     */
    public function getLayersNames($user_id, $layerType, $name)
    {
        $nameTmpl = '%' . $name . '%';
        $sql = 'SELECT name FROM su_users_layers WHERE group_id = :group_id AND layer_type = :layer_type AND name LIKE :name';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':group_id', $user_id);
        $cmd->bindParameter(':layer_type', $layerType);
        $cmd->bindParameter(':name', $nameTmpl);

        return $cmd->query()->readAll();
    }

    public function getWorkLayers($user_id)
    {
        $sql = 'SELECT 
                *
            FROM
                su_users_layers 
            WHERE 
                group_id = :group_id
                AND layer_type = 19
                AND farming IS NULL
            ORDER BY
                name COLLATE "alpha_numeric_bg";
        ';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':group_id', $user_id);

        return $cmd->query()->readAll();
    }

    public function getWorkLayersWithFarmings($user_id, $work_layer_id)
    {
        $sql = 'SELECT ul.*, ul.id as layer_id, uf.name as farming_name
            FROM su_users_layers ul
            LEFT JOIN su_users_farming uf ON ul.farming = uf.id
            WHERE ul.group_id = :group_id AND layer_type = 19 AND is_exist';

        if ($work_layer_id > 0) {
            $sql .= ' AND ul.id <> :work_layer_id ';
        }

        $sql .= 'ORDER BY ul.farming ASC , ul.year ASC';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':group_id', $user_id);

        if ($work_layer_id > 0) {
            $cmd->bindParameter(':work_layer_id', $work_layer_id);
        }

        return $cmd->query()->readAll();
    }

    public function deleteWorkLayer($groupID, $layerID, $tablename)
    {
        $sql = 'DELETE FROM su_users_layers WHERE group_id = :group_id AND layer_type = 19 AND id = :layer_id AND table_name = :table_name';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':group_id', $groupID);
        $cmd->bindParameter(':layer_id', $layerID);
        $cmd->bindParameter(':table_name', $tablename);
        $cmd->execute();
    }

    public function getFarmingYearByLayerID($layerID)
    {
        $sql = 'SELECT year FROM su_users_layers WHERE id = :layer_id';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':layer_id', $layerID);
        $result = $cmd->query()->readAll();

        return $result[0]['year'];
    }

    public function createSafeIntersection()
    {
        $sql = "CREATE OR REPLACE FUNCTION safe_intersection(geom_a geometry, geom_b geometry)
                RETURNS geometry AS
                $$
                BEGIN
                    RETURN ST_Intersection(geom_a, geom_b);
                    EXCEPTION
                        WHEN OTHERS THEN
                            BEGIN
                                RETURN ST_Intersection(ST_Buffer(geom_a, 0.0000001), ST_Buffer(geom_b, 0.0000001));
                                EXCEPTION
                                    WHEN OTHERS THEN
                                        RETURN ST_GeomFromText('POLYGON EMPTY');
                    END;
                END
                $$
                LANGUAGE 'plpgsql' STABLE STRICT;";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    /**
     * @throws TDbException
     *
     * @return array|false
     */
    public function getGeoJSON($options, $includeLineFeature, $offset)
    {
        $table = $options['tablename'];
        $mainQueryFeature = "SELECT
              'Feature'                                                                    AS type,
              row_to_json((SELECT l
                           FROM (SELECT " . implode(',', $options['return']) . " ) AS l
                              ))                                                           AS properties,
                   (CASE WHEN ST_GeometryType(lg.geom)='ST_MultiPolygon' THEN ST_AsGeoJSON(dump.geom, 15, 4) :: JSON ELSE
                CASE WHEN ST_GeometryType(lg.geom)='ST_Polygon' THEN ST_AsGeoJSON(ST_Transform(lg.geom, 4326), 15, 4) :: JSON ELSE NULL END
               END) AS geometry
                FROM {$table} AS lg,
                ST_Dump(ST_Transform(lg.geom, 4326)) dump";

        $mainQueryLineFeature = "
                SELECT
              'LineFeature' AS type,
              row_to_json((SELECT l
                           FROM (SELECT " . implode(',', $options['return']) . " ) AS l
                              ))                                                           AS properties,
                 (ST_AsGeoJSON(ST_Transform(ST_OffsetCurve(
                    ST_SnapToGrid(ST_ExteriorRing((ST_Dump(geom)).geom),0.002),
                    {$offset}, 'quad_segs=10 join=mitre mitre_limit=10'), 4326), 15, 4
                 )) :: JSON  AS geometry
                FROM {$table} AS lg
        ";
        if (!empty($options['where']['id'])) {
            $mainQueryFeature .= ' WHERE ' . $options['where']['id']['column'] . ' ' . $options['where']['id']['compare'] . ' (' . implode(
                ',',
                $options['where']['id']['value']
            ) . ')';
            $mainQueryLineFeature .= ' WHERE ' . $options['where']['id']['column'] . ' ' . $options['where']['id']['compare'] . ' (' . implode(
                ',',
                $options['where']['id']['value']
            ) . ')';
        }

        $mainQuery = '';
        $mainQuery .= $mainQueryFeature;

        if ($includeLineFeature) {
            $mainQuery .= ' UNION ALL ';
            $mainQuery .= $mainQueryLineFeature;
        }

        $sql = "
        SELECT row_to_json(fc) as \"geoJSON\"
        FROM (SELECT
            'FeatureCollection'         AS type,
            array_to_json(array_agg(f)) AS features
          FROM ({$mainQuery}) AS f) AS fc;
        ";

        $cmd = $this->DbModule->createCommand($sql);
        $result = $cmd->query()->read();
        $cmd->query()->close();

        return $result;
    }

    public function getTableNameExist(string $tablename)
    {
        $sql = 'SELECT COUNT(*) AS num FROM information_schema.columns ';
        $sql .= "where table_schema = 'public' and
                table_name = :tname";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':tname', $tablename);

        return $cmd->queryScalar();
    }

    public function getViewNameExists(string $viewName)
    {
        $sql = 'SELECT 1 FROM pg_class WHERE relname=:view_name';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':view_name', $viewName);

        return $cmd->queryScalar();
    }

    public function getDataByQuery(
        $query
    ) {
        $cmd = $this->DbModule->createCommand($query);

        return $cmd->query()->readAll();
    }

    /**
     * @see ILayerable::getMaxExtent
     */
    public function getMaxExtent($tablename)
    {
        $sql = "SELECT ST_extent(geom) as extent FROM {$tablename}";
        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->queryScalar();
    }

    public function getAllowableLayersNtpData()
    {
        $query = "
            with group_ntp_layer_allowable as (
                select
                    jsonb_agg(
                        distinct jsonb_build_object(
                        'ntp_k',ntp_k,
                        'ntp', ntp
                    )) as grouped_ntp
                from
                    layer_allowable
            ),
            group_ntp_layer_allowable_final as (
                select
                    json_agg(
                        distinct jsonb_build_object(
                            'ntp_k', ntp_k,
                            'ntp', ntp
                        )
                    )as grouped_ntp
                from
                    layer_allowable_final
                )
            select
                group_ntp_layer_allowable.grouped_ntp::jsonb as layer_allowable,
                group_ntp_layer_allowable_final.grouped_ntp::jsonb as layer_allowable_final
            from
                group_ntp_layer_allowable
            cross join group_ntp_layer_allowable_final        
        ";

        $cmd = $this->DbModule->createCommand($query);

        $result = $cmd->query()->read();

        return [
            'layer_allowable' => json_decode($result['layer_allowable']),
            'layer_allowable_final' => json_decode($result['layer_allowable_final']),
        ];
    }
}
