<?php

namespace TF\Engine\Plugins\Core\Dashboard;

use TF\Engine\Plugins\Core\UserDb\UserDbModel;

class DashboardModel extends UserDbModel
{
    public function __construct($tableName = null, $fieldName = null)
    {
        parent::__construct($tableName, $fieldName);

        $this->tableName = $tableName;
        $this->fieldName = $fieldName;
    }

    public function getTransactions($params)
    {
        $sql = "
        with payment_data as(
				select
					distinct on
					(
						p.id,
						pn.id
					) p.id,
					p.date,
					c.id,
					c.farming_id,
					(
						case
							when p.paid_in = 1 then(
								case
									when rt.name is null then rt.name
									else null
								end
							)
							else rt.name
						end
					) as rent_name,
					(
						case
							when p.paid_in = 1 then(
								case
									when rt.unit is null then rt.unit
									else null
								end
							)
							else rt.unit
						end
					) as rent_unit,
					(
						case
							when p.paid_in = 2 then pn.amount
							else(
								case
									when p.paid_from = 2 then(
										pn.amount * pn.unit_value
									)
									else p.amount
								end
							)
						end
					) as amount,
					lk.ekate
				from
					su_payments p inner join su_transactions t on
					(
						p.transaction_id = t.id
					) left join su_payments_natura pn on
					(
						p.id = pn.payment_id
					) left join su_renta_types rt on
					(
						rt.id = pn.nat_type
					) inner join su_owners o on
					(
						o.id = p.owner_id
					) inner join su_contracts c on
					(
						c.id = p.contract_id
					) left join su_contracts a on
					(
						a.parent_id = c.id
					) left join su_contracts_plots_rel cpra on
					(
						cpra.contract_id = a.id
					) left join layer_kvs lka on
					(
						lka.gid = cpra.plot_id
					) inner join su_contracts_plots_rel cpr on
					(
						cpr.contract_id = c.id
					) inner join layer_kvs lk on
					(
						lk.gid = cpr.plot_id
					)
				where
					t.status = true
					and p.date >= ':start_date'
					and p.date <= ':due_date'
        )";
        $sql .= 'select ';
        $col = implode(', ', $params['col']);
        $sql .= $col;
        $sql .= " from payment_data
        where date  <= ':due_date'
        and date >= ':start_date'
        ";
        if ($params['payroll_farming']) {
            $sql .= " and farming_id = ':farming_id'";
        }
        if ($params['payroll_ekate']) {
            $sql .= " and ekate = ':ekate'";
        }
        if ($params['group']) {
            $group = implode(', ', $params['group']);
            $sql .= '      group by  ' . $group;
        }
        if ($params['order']) {
            $order = implode(', ', $params['group']);
            $sql .= '      order by  ' . $order;
        }
        $sql = str_replace(':start_date', $params['payroll_from_date'], $sql);
        $sql = str_replace(':due_date', $params['payroll_to_date'], $sql);
        $sql = str_replace(':farming_id', $params['payroll_farming'], $sql);
        $sql = str_replace(':ekate', $params['payroll_ekate'], $sql);
        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function getPaymentsContractsSQL()
    {
        $sql = 'WITH ';
        $sql .= $this->getContractsSQL() . ',';
        $sql .= $this->getPaymentsSQL();

        return $sql;
    }

    public function getPayments($options)
    {
        $sql = '
            SELECT
                DISTINCT (t4. DATE),
                t4.nat_type,
                COALESCE (amount, 0) AS total_amount
            FROM
                (
                    SELECT
                        *
                    FROM
                        (
                            SELECT DISTINCT
                                "date"
                            FROM
                                su_payments P
                            INNER JOIN su_payments_natura pn ON P ."id" = pn.payment_id
                        ) t1,
                        (
                            SELECT DISTINCT
                                pn.nat_type
                            FROM
                                su_payments P
                            INNER JOIN su_payments_natura pn ON P ."id" = pn.payment_id
                        ) t2
                ) t4
            LEFT JOIN (
                SELECT
                    "date",
                    pn.nat_type,
                    SUM (pn.amount) AS amount
                FROM
                    su_payments P
                INNER JOIN su_payments_natura pn ON P ."id" = pn.payment_id
                GROUP BY
                    "date",
                    pn.nat_type
            ) t3 ON (
                "t3"."date" = t4."date"
                AND "t3"."nat_type" = t4.nat_type
            )
            ORDER BY
                t4. DATE';

        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function getContractDates($options)
    {
        $return = [];

        $sql[0] = 'select distinct c_date ,  nm_usage_rights from su_contracts where nm_usage_rights = 1 ';
        $sql[1] = 'select distinct c_date from su_sales_contracts';

        foreach ($sql as $query) {
            $cmd = $this->DbModule->createCommand($query);
            $result = $cmd->query()->readAll();
            foreach ($result as $r) {
                array_push($return, $r['c_date']);
            }
        }
        $return = array_unique($return);
        asort($return);

        return $return;
    }

    public function rentNatura($params)
    {
        $sql = '
        select
        due.sum_rent,
        (case when paid.sum_amount is null then 0 else paid.sum_amount end) as paid_amount,
        (case when paid.sum_amount is null then due.sum_rent else(due.sum_rent - paid.sum_amount) end) as rest_sum,
        due.contract_renta_type, rt.name as renta_type, rt.unit as renta_unit_id';

        if ('farming' == $params['group']) {
            $sql .= ' , due.farming_id ';
        } elseif ('ekate' == $params['group']) {
            $sql .= ', due.ekate ';
        }
        $sql .= '
        from
        (select sum(t2.rent_amount_sum)as sum_rent, t2.contract_renta_type';
        if ('farming' == $params['group']) {
            $sql .= ' , t2.farming_id ';
        } elseif ('ekate' == $params['group']) {
            $sql .= ', t2.ekate ';
        }
        $sql .= "
        from
		(select
		scpr.id,
			c_num,
			sc.start_date,
			sc.due_date,
			sc.active,
			contract_end_date,
			area_for_rent,
			lk.ekate,
			rent_per_plot,
			sc.renta,
			farming_id,
			scr.renta_id as contract_renta_type,
			sch.f_year,
			(case when sch.amount is null then scr.renta_value else sch.amount end) as rent_amount,
			(case when sch.amount is null then scr.renta_value*area_for_rent else sch.amount*area_for_rent end) as rent_amount_sum,
			sch.nat_type as charge_type,
			sc.parent_id
		from su_contracts_plots_rel scpr
		INNER join su_contracts sc  on (scpr.contract_id = sc.id)
		INNER join su_contracts_rents scr on (scpr.contract_id =scr.contract_id )
		left join layer_kvs lk on scpr.plot_id = lk.gid
		left join(
			select distinct(schr.plot_id) as plot_id,schn.amount, schn.nat_type, schr.\"year\"as f_year from su_charged_renta schr
			join su_charged_renta_natura schn on (schr.id=schn.renta_id)
			where schr.\"year\" = :farming_year
		)sch on sch.plot_id = scpr.plot_id and sch.nat_type=scr.renta_id
		where sc.id not in (select parent_id from su_contracts where parent_id != 0 and sc.start_date <= ':start_date' and sc.due_date >= ':due_date')
		)t2
		where
		t2.active = true
		and t2.start_date <= ':start_date'
		and t2.due_date >= ':due_date'
		and t2.contract_renta_type is not null
		";
        $sql .= '
		group by t2.contract_renta_type';

        if ('farming' == $params['group']) {
            $sql .= ' , t2.farming_id ';
        } elseif ('ekate' == $params['group']) {
            $sql .= ', t2.ekate ';
        }

        $sql .= '
		) due
 left join 	(select sum (p1.amount) as sum_amount, p1.nat_type,p1.farming_id from (
			select
			stn.nat_type,
			stn.amount,
			stn.paid_from,
			stn.paid_in,
			sc.farming_id
			from su_transactions st
				left join su_payments sp on sp.transaction_id = st.id
				left join su_transactions_natura stn on stn.transaction_id = st.id
				left join su_contracts sc on sp.contract_id = sc.id
				where st.status = true and
				sp.farming_year = :farming_year
				and stn.paid_from = 2
			)p1
			group by p1.nat_type, p1.farming_id) paid
  on due.contract_renta_type = paid.nat_type
   ';
        if ('farming' == $params['group']) {
            $sql .= '
                and due.farming_id = paid.farming_id ';
        }
        $sql .= 'left join su_renta_types rt on  due.contract_renta_type = rt.id';

        $sql = str_replace(':farming_year', $params['farming_year'], $sql);
        $sql = str_replace(':start_date', $params['payroll_from_date'], $sql);
        $sql = str_replace(':due_date', $params['payroll_to_date'], $sql);

        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function getContractsGrid($params)
    {
        $sql = "select t1.c_num, array_agg(distinct(concat(so.name, ' ' ,so.lastname))) as owners,array_agg(distinct(rt.name)) as rent , sum(t1.contract_area) as area from (
                select sc.c_num, scpr.contract_area,  sc.start_date, sc.due_date, sc.farming_id, spor.owner_id, scr.renta_id
				from su_contracts_plots_rel scpr
				inner join su_contracts sc on scpr.contract_id = sc.id
				inner join su_contracts_rents scr on scpr.contract_id = scr.contract_id
				left join su_plots_owners_rel spor on scpr.plot_id = spor.pc_rel_id
				left join layer_kvs lk on scpr.plot_id = lk.gid
				where sc.id not in (select parent_id from su_contracts where parent_id !=0 and start_date >=':start_date' and due_date <=':due_date')
				and sc.start_date >= ':start_date' and sc.due_date <=':due_date'
				and sc.nm_usage_rights = '2' or sc.nm_usage_rights = '3'";
        if ($params['farming_id']) {
            $sql .= '
				and sc.farming_id = $params["farming_id"]';
        } elseif ($params['ekate']) {
            $sql .= 'and lk.ekate = $params["ekate"]';
        }
        $sql .= '
				) t1
			left join su_owners so on so.id = t1.owner_id
			left join su_renta_types rt on t1.renta_id = rt.id
			group by t1.c_num';
        $sql = str_replace(':start_date', $params['payroll_from_date'], $sql);
        $sql = str_replace(':due_date', $params['payroll_to_date'], $sql);

        if (!empty($params['order']) && '' != $params['order']['direction']) {
            $sql .= ' order by ' . $params['order']['active'] . ' ' . $params['order']['direction'];
        }

        if ($params['page'] && $params['page_count']) {
            $cmd = $this->DbModule->createCommand($sql);
            $return['count'] = $cmd->query()->getRowCount();

            $sql .= ' limit ' . $params['page_count'];
            $sql .= ' offset ' . $params['page'];
        }
        $cmd = $this->DbModule->createCommand($sql);
        $return['data'] = $cmd->query()->readAll();

        return $return;
    }

    public function getOwnGrid($params)
    {
        $sql = ' with own_data as (';
        $col = implode(', ', $params['col']);
        $sql .= 'select ' . $col;
        $sql .= "
          from
            (((SELECT DISTINCT
                t1.*,
                kvs.ekate,
                round(cpr.contract_area :: numeric, 3) AS area,
                kvs.usable,
                c.id as c_id,
                c.c_num,
                c.start_date,
                c.farming_id
                FROM su_contracts_plots_rel cpr LEFT JOIN su_contracts C ON (C.ID = cpr.contract_id)
                LEFT JOIN su_contracts a on (a.parent_id = c.id AND a.active = true)
                LEFT JOIN layer_kvs kvs ON (kvs.gid = cpr.plot_id)
                LEFT JOIN (
                select  scpr.contract_id,
                        scpr.plot_id,
                        SUM(scpr.contract_area_for_sale) as sold_area,
                        MAX(scpr.contract_area) as total_area
                      FROM su_sales_contracts_plots_rel scpr INNER join su_sales_contracts sc
                        on (sc.id = scpr.sales_contract_id and sc.start_date <= ':start_date')
                        group by scpr.contract_id, scpr.plot_id) as t1 on (t1.contract_id = c.id and t1.plot_id = kvs.gid)
              WHERE (kvs.is_edited = FALSE AND (kvs.edit_active_from <= ':due_date' OR kvs.edit_active_from IS NULL)) AND
                case when t1.total_area is not null
                then t1.total_area > t1.sold_area
                else c.id > 0 end AND C.nm_usage_rights = 1 AND C.start_date <= ':start_date' AND C.is_annex = 'false' AND
                c.is_sublease = false AND c.active =true))
            UNION ((SELECT DISTINCT
                t1.*,
                kvs.ekate,
                round(cpr.contract_area :: numeric, 3) AS area,
                kvs.usable,
                c.id as c_id,
                c.c_num,
                c.start_date,
                c.farming_id
              FROM su_contracts_plots_rel cpr LEFT JOIN su_contracts C ON (C.ID = cpr.contract_id)
                LEFT JOIN su_contracts a on (a.parent_id = c.id AND a.active = true)
                LEFT JOIN layer_kvs kvs ON (kvs.gid = cpr.plot_id)
                LEFT JOIN (
                 select scpr.contract_id,
                        scpr.plot_id,
                        SUM(scpr.contract_area_for_sale) as sold_area,
                        MAX(scpr.contract_area) as total_area
                       FROM su_sales_contracts_plots_rel scpr INNER join su_sales_contracts sc
                        on (sc.id = scpr.sales_contract_id and sc.start_date <= ':start_date')
                        group by scpr.contract_id, scpr.plot_id) as t1 on (t1.contract_id = c.id and t1.plot_id = kvs.gid)
               WHERE (kvs.is_edited = TRUE AND kvs.edit_active_from >= ':due_date') AND case when t1.total_area is not null
                then t1.total_area > t1.sold_area
                else c.id > 0 end AND
                C.nm_usage_rights = 1 AND C.start_date <= ':start_date' AND C.is_annex = 'false' AND
                c.is_sublease = false AND c.active =true))
            )own_grid
            where true
           ";
        if ($params['payroll_farming']) {
            $sql .= "
            and farming_id = ':payroll_farming'
            ";
            $sql = str_replace(':payroll_farming', $params['payroll_farming'], $sql);
        }
        if ($params['payroll_ekate']) {
            $sql .= "
            and ekate = ':payroll_ekate'
            ";
            $sql = str_replace(':payroll_ekate', $params['payroll_ekate'], $sql);
        }
        if ($params['group_by']) {
            $group = implode(', ', $params['group_by']);
            $sql .= 'group by ' . $group;
        }
        $sql .= ' )';
        $sql = str_replace(':start_date', $params['payroll_from_date'], $sql);
        $sql = str_replace(':due_date', $params['payroll_to_date'], $sql);
        if ('line_chart' == $params['query_type']) {
            $sql .= 'select start_date, sum(area) over (order by start_date asc rows between unbounded preceding and current row) from own_data';
        } elseif ('bar_chart' == $params['query_type']) {
            $sql .= 'select farming, ekate, sum(area) as suma from own_data group by ekate, farming';
        } else {
            $sql .= 'select * from own_data';
        }
        if ($params['order_by']) {
            $order = implode(', ', $params['order_by']);
            $sql .= ' order by ' . $order;
        }
        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function getRentContracts($params)
    {
        $sql = ' with contract_data as ';
        $sql .= "
        (( SELECT distinct
                ekate,
                round(cpr.contract_area::numeric,3) as area,
                case when c.is_sublease then c1.c_num else c.c_num end as c_num,
                case when c.is_sublease then c1.id else c.id end as c_id,
                case when c.is_sublease then c.id else null end as sublease_id,
                case when c.is_sublease then c.c_num else null end as sublease_num,
                c.sv_num,to_char(c.sv_date,'DD.MM.YYYY') as sv_date,
                c.nm_usage_rights as c_type,
                c.farming_id as farming_id,
                c.renta,c.is_sublease,
                to_char((case when c.is_sublease then c.due_date else cpr.contract_end_date end),'DD.MM.YYYY') AS due_date,
                pf.farming_id as arendodatel,
                string_agg(DISTINCT(CASE WHEN owner_type = 1 THEN name || ' ' || surname || ' ' || lastname ELSE company_name END), ',') as owner_names,
                to_char((c.start_date),'DD.MM.YYYY') AS subl_start_date,
                to_char((c.due_date),'DD.MM.YYYY') AS subl_due_date
				FROM su_contracts C
                left JOIN su_contracts a ON(a.parent_id = c.id AND a.active = true and a.start_date<= ':due_date' and a.due_date >= ':start_date')
				INNER JOIN su_contracts_plots_rel cpr ON(cpr.contract_id = (CASE WHEN a.id IS NULL THEN c.id ELSE a.id END))
				INNER JOIN su_contracts c1 ON(c1.id = cpr.contract_id)
				LEFT JOIN layer_kvs kvs ON (cpr.plot_id = kvs.gid)
				LEFT JOIN su_plots_farming_rel PF ON(pf.pc_rel_id = cpr.id)
				LEFT JOIN su_plots_owners_rel PO ON(po.pc_rel_id = cpr.id)
				LEFT JOIN su_owners O ON(o.id = po.owner_id)
                LEFT JOIN
                    (select scpr.contract_id, scpr.plot_id, SUM(scpr.contract_area_for_sale) as sold_area, MAX(scpr.contract_area) as total_area
                        FROM su_sales_contracts_plots_rel scpr
                        INNER join su_sales_contracts sc on(sc.id = scpr.sales_contract_id and sc.start_date <= ':start_date')
                        group by scpr.contract_id, scpr.plot_id
                    ) as t2 on (t2.contract_id = c.id and t2.plot_id = kvs.gid)
                LEFT JOIN
                    ( select spc.pc_rel_id from su_subleases_plots_contracts_rel as spc
                        join su_contracts as c2 on (c2.id = spc.sublease_id)
                        where
                          c2.due_date >= ':due_date'
                          and c2.start_date <= ':start_date'
                          and c2.is_sublease = true
                          and c2.active = true
                    ) as t1 on (t1.pc_rel_id = cpr.id)
				WHERE (kvs.is_edited = FALSE AND (kvs.edit_active_from <= ':start_date' OR kvs.edit_active_from IS NULL))
				AND (pf.farming_id <> c.farming_id OR pf.farming_id is null)
				AND c.nm_usage_rights <> '1'
				AND c.nm_usage_rights <> '4'
                AND case when t2.total_area is not null then t2.total_area > t2.sold_area else c.id > 0 end
				AND t1.pc_rel_id is NULL  AND (c.start_date <= ':due_date' OR a.start_date <= ':due_date') AND (c.due_date IS NULL OR c.due_date >= ':start_date' OR a.due_date >= ':start_date') AND c.is_annex = :is_annex AND c.active = :active AND c1.active = :c1_active GROUP BY kvs.gid, cpr.id, c.id, pf.id, cpr.contract_area, cpr.price_per_acre, cpr.price_sum, c1.id)
				UNION
				  ( SELECT distinct
				  ekate,
				  round(cpr.contract_area::numeric,3) as area,
				  case when c.is_sublease then c1.c_num else c.c_num end as c_num,
				  case when c.is_sublease then c1.id else c.id end as c_id,
				  case when c.is_sublease then c.id else null end as sublease_id,
				  case when c.is_sublease then c.c_num else null end as sublease_num,
				  c.sv_num,to_char(c.sv_date,'DD.MM.YYYY') as sv_date,
				  c.nm_usage_rights as c_type,
				  c.farming_id as farming_id,
				  c.renta,
				  c.is_sublease,
				  to_char((case when c.is_sublease then c.due_date else cpr.contract_end_date end),'DD.MM.YYYY') AS due_date,
				  pf.farming_id as arendodatel,
				  string_agg(DISTINCT(CASE WHEN owner_type = 1 THEN name || ' ' || surname || ' ' || lastname ELSE company_name END), ',') as owner_names,
				  to_char((c.start_date),'DD.MM.YYYY') AS subl_start_date,
				  to_char((c.due_date),'DD.MM.YYYY') AS subl_due_date
				FROM su_contracts C
                left JOIN su_contracts a ON(a.parent_id = c.id AND a.active = true and a.start_date<= ':due_date' and a.due_date >= ':start_date')
				INNER JOIN su_contracts_plots_rel cpr ON(cpr.contract_id = (CASE WHEN a.id IS NULL THEN c.id ELSE a.id END))
				INNER JOIN su_contracts c1 ON(c1.id = cpr.contract_id)
				LEFT JOIN layer_kvs kvs ON (cpr.plot_id = kvs.gid)
				LEFT JOIN su_plots_farming_rel pf ON(pf.pc_rel_id = cpr.id)
				LEFT JOIN su_plots_owners_rel po ON(po.pc_rel_id = cpr.id)
				LEFT JOIN su_owners o ON(o.id = po.owner_id)
                LEFT JOIN
                    (select scpr.contract_id, scpr.plot_id, SUM(scpr.contract_area_for_sale) as sold_area, MAX(scpr.contract_area) as total_area
                        FROM su_sales_contracts_plots_rel scpr
                        INNER join su_sales_contracts sc on(sc.id = scpr.sales_contract_id and sc.start_date <= ':start_date')
                        group by scpr.contract_id, scpr.plot_id
                    ) as t2 on (t2.contract_id = c.id and t2.plot_id = kvs.gid)
                LEFT JOIN
                    ( select spc.pc_rel_id from su_subleases_plots_contracts_rel as spc
                        join su_contracts as c2 on (c2.id = spc.sublease_id)
                        where
                          c2.due_date >= ':due_date'
                          and c2.start_date <= ':start_date'
                          and c2.is_sublease = true
                          and c2.active = true
                    ) as t1 on (t1.pc_rel_id = cpr.id)
				WHERE (kvs.is_edited = TRUE AND kvs.edit_active_from >= ':due_date')
				AND (pf.farming_id <> c.farming_id OR pf.farming_id is null)
				AND c.nm_usage_rights <> '1'
				AND c.nm_usage_rights <> '4'
                AND case when t2.total_area is not null then t2.total_area > t2.sold_area else c.id > 0 end
				AND t1.pc_rel_id is NULL
				AND (c.start_date <= ':due_date' OR a.start_date <= ':due_date') AND (c.due_date IS NULL OR c.due_date >= ':start_date' OR a.due_date >= ':start_date') AND c.is_annex = :is_annex AND c.active = :active AND c1.active = :c1_active GROUP BY kvs.gid, cpr.id, c.id, pf.id, cpr.contract_area, cpr.price_per_acre, cpr.price_sum, c1.id ))
               ";

        $col = implode(', ', $params['col']);
        $sql .= 'select ' . $col;
        $sql .= ' from contract_data';
        $sql .= ' where true ';
        if ($params['payroll_farming'] && '' != $params['payroll_farming']) {
            $sql .= " AND farming_id = ':payroll_farming' ";
        }
        if ($params['payroll_ekate'] && '' != $params['payroll_ekate']) {
            $sql .= " AND ekate = ':ekate' ";
        }

        if ($params['where']) {
            $where = implode(' ', $params['where']);
            $sql .= $where;
        }
        // c_type
        if ($params['rights']) {
            $sql .= " and c_type = ':rights'";
        }
        if ($params['group_by']) {
            $group = implode(', ', $params['group_by']);
            $sql .= ' group by ' . $group;
        }

        if (!empty($params['order']) && '' != $params['order']['direction']) {
            $sql .= ' order by ' . $params['order']['active'] . ' ' . $params['order']['direction'];
        }

        if ($params['grid']) {
            $sql = " select * from ({$sql}).t3 group by t3.c_num";
        }

        $sql = str_replace(':start_date', $params['payroll_from_date'], $sql);
        $sql = str_replace(':due_date', $params['payroll_to_date'], $sql);
        $sql = str_replace(':rights', $params['rights'], $sql);
        $sql = str_replace(':is_annex', 'false', $sql);
        $sql = str_replace(':active', 'true', $sql);
        $sql = str_replace(':c1_active', 'true', $sql);
        $sql = str_replace(':payroll_farming', $params['payroll_farming'], $sql);
        $sql = str_replace(':ekate', $params['payroll_ekate'], $sql);
        if ($params['page'] && $params['page_count']) {
            $cmd = $this->DbModule->createCommand($sql);
            $return['count'] = $cmd->query()->getRowCount();
            $sql .= ' limit ' . $params['page_count'];
            $sql .= ' offset ' . $params['page'];
        }

        $cmd = $this->DbModule->createCommand($sql);
        $return['data'] = $cmd->query()->readAll();

        return $return;
    }

    public function getSubleasedContracts($params)
    {
        $sql = "
        with contract_data as(
(
	select
			c1.nm_usage_rights as rights,
            c.nm_usage_rights as s_rights,
            c.id as c_id,
            c.c_num,
            c.nm_usage_rights as c_type,
            contract_area,
            c1.farming_id,
            c.start_date,
            c.due_date,
            c.active,
            concat(co.name , ' ',co.lastname) as name,
            c.farming_id as farming,
            kvs.*
		from
			layer_kvs kvs inner join su_contracts_plots_rel pc on
			(
				pc.plot_id = kvs.gid
			) inner join su_subleases_plots_contracts_rel spc on
			(
				spc.pc_rel_id = pc.id
			) inner join su_contracts c on
			(
				c.id = spc.sublease_id
			) inner join su_contracts c1 on
			(
				c1.id = pc.contract_id
			) left join su_contracts_contragents cc on
			(
				cc.contract_id = spc.sublease_id
			) left join su_owners co on
			(
				co.id = cc.owner_id
			) left join su_plots_owners_rel po on
			(
				po.pc_rel_id = pc.id
			) left join su_owners o on
			(
				o.id = po.owner_id
			) left join su_contracts_farming_contragents fc on
			(
				fc.contract_id = spc.sublease_id
			) left join su_contracts a on
			(
				a.parent_id = c1.id
				and a.active = true
			) left join(
				select
					scpr.contract_id,
					scpr.plot_id,
					sum( scpr.contract_area_for_sale ) as sold_area,
					max( scpr.contract_area ) as total_area
				from
					su_sales_contracts_plots_rel scpr inner join su_sales_contracts sc on
					(
						sc.id = scpr.sales_contract_id
						and sc.start_date >= ':start_date'
						and sc.due_date >= ':due_date'
					)
				group by
					scpr.contract_id,
					scpr.plot_id
			) as t2 on
			(
				t2.contract_id = c.id
				and t2.plot_id = kvs.gid
			)
		where
			case
				when t2.total_area is not null then t2.total_area > t2.sold_area
				else c.id > 0
			end
			and kvs.is_edited = false
			and(
				kvs.edit_active_from <= c.start_date
				or kvs.edit_active_from is null
			)
			and c.is_sublease =:is_sublease
			and c.active =:active
			and true
	)
union(
	select
		c1.nm_usage_rights as rights,
		c.nm_usage_rights as s_rights,
		c.id as c_id,
		c.c_num,
		c.nm_usage_rights as c_type,
		contract_area,
		c1.farming_id,
		c.start_date,
		c.due_date,
		c1.active,
		concat(co.name , ' ',co.lastname) as name,
		c.farming_id as farming,
		kvs.*
	from
		layer_kvs kvs inner join su_contracts_plots_rel pc on
		(
			pc.plot_id = kvs.gid
		) inner join su_subleases_plots_contracts_rel spc on
		(
			spc.pc_rel_id = pc.id
		) inner join su_contracts c on
		(
			c.id = spc.sublease_id
		) inner join su_contracts c1 on
		(
			c1.id = pc.contract_id
		) left join su_contracts_contragents cc on
		(
			cc.contract_id = spc.sublease_id
		) left join su_owners co on
		(
			co.id = cc.owner_id
		) left join su_plots_owners_rel po on
		(
			po.pc_rel_id = pc.id
		) left join su_owners o on
		(
			o.id = po.owner_id
		) left join su_contracts_farming_contragents fc on
		(
			fc.contract_id = spc.sublease_id
		) left join su_contracts a on
		(
			a.parent_id = c1.id
			and a.active = true
		) left join(
			select
				scpr.contract_id,
				scpr.plot_id,
				sum( scpr.contract_area_for_sale ) as sold_area,
				max( scpr.contract_area ) as total_area
			from
				su_sales_contracts_plots_rel scpr inner join su_sales_contracts sc on
				(
					sc.id = scpr.sales_contract_id
					and sc.start_date >= ':start_date'
					and sc.due_date >= ':due_date'
				)
			group by
				scpr.contract_id,
				scpr.plot_id
		) as t2 on
		(
			t2.contract_id = c.id
			and t2.plot_id = kvs.gid
		)
	where
		case
			when t2.total_area is not null then t2.total_area > t2.sold_area
			else c.id > 0
		end
		and kvs.is_edited = true
		and kvs.edit_active_from > now()
		and c.is_sublease =:is_sublease
		and c.active =:active
		and true
)
)
";
        $sql .= 'select ';
        $col = implode(', ', $params['col']);
        $sql .= $col;
        $sql .= " from contract_data
        where start_date  <= ':due_date'
        and due_date >= ':start_date'
        and rights :ownership
        ";
        if ($params['rights']) {
            $sql .= "and s_rights = ':rights'";
        }

        if ($params['payroll_farming'] && '' != $params['payroll_farming']) {
            $sql .= "and farming_id = ':farming_id'";
        }
        if ($params['payroll_ekate'] && '' != $params['payroll_ekate']) {
            $sql .= "and ekate = ':ekate'";
        }
        if ($params['group']) {
            $group = implode(', ', $params['group']);
            $sql .= '      group by  ' . $group;
        }

        if (!empty($params['order']) && '' != $params['order']['direction']) {
            $sql .= ' order by ' . $params['order']['active'] . ' ' . $params['order']['direction'];
        }

        $sql = str_replace(':start_date', $params['report_date_from'], $sql);
        $sql = str_replace(':due_date', $params['report_date_to'], $sql);
        $sql = str_replace(':is_sublease', 'true', $sql);
        $sql = str_replace(':active', 'true', $sql);
        $sql = str_replace(':rights', $params['rights'], $sql);
        $sql = str_replace(':farming_id', $params['payroll_farming'], $sql);
        $sql = str_replace(':ekate', $params['payroll_ekate'], $sql);
        $sql = str_replace(':ownership', $params['ownership'], $sql);
        if ($params['page'] && $params['page_count']) {
            $cmd = $this->DbModule->createCommand($sql);
            $return['count'] = $cmd->query()->getRowCount();

            $sql .= ' limit ' . $params['page_count'];
            $sql .= ' offset ' . $params['page'];
        }
        $cmd = $this->DbModule->createCommand($sql);
        $return['data'] = $cmd->query()->readAll();

        return $return;
    }

    public function getNaturaPaymentsByFarm($options)
    {
        $sql = 'with payroll_data AS (';
        $sql .= "SELECT
                        farming_id,
                        unpaid_natura,
                        paid_total_virtual_natura
                    FROM
                        tf_payroll2 (
                            :start_date,
                            :due_date,
                            :farm_years::int4 []
                        )
                ),
                user_natura_types_zero AS (
                    SELECT
                        json_agg(json_build_object('id', id, 'name',name, 'value', 0)) AS data
                    FROM
                        su_renta_types
                ),
                user_natura as (
                    SELECT id, name, row_number() over (ORDER BY id) as position from su_renta_types
                ),
                payroll_total_by_farm AS (
                    SELECT
                        farming_id,
                        natura_agg(unpaid_natura) AS total_unpaid_natura,
                        COALESCE(natura_agg(paid_total_virtual_natura), (SELECT data FROM user_natura_types_zero)) AS total_payed_natura
                    FROM payroll_data p
                    GROUP BY farming_id
                ),
                user_farms AS (
                    SELECT
                        id,
                        name
                    FROM dblink('host=" . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' dbname=' . DEFAULT_DB_DATABASE . "',
                        'SELECT id, name FROM su_users_farming where user_id={$options['user_id']}') AS t(id int, name text)
                ),
                payments_natura_by_farm AS (
                    SELECT
                        prbf.farming_id,
                        uf.name AS farm_name,
                        tun.id AS natura_id,
                        tun.name AS natura_name,
                        ptvn.value AS paid_natura,
                        tun. value  AS  unpaid_natura
                    FROM
                        payroll_total_by_farm prbf,
                        json_to_recordset(prbf.total_unpaid_natura) AS tun(id int, name text, value decimal),
                        json_to_recordset(prbf.total_payed_natura) AS ptvn(id int, name text, value decimal),
                        user_farms uf
                    WHERE
                        tun.id = ptvn.id
                        AND tun.id = :natura_id
                        AND prbf.farming_id=uf.id
                )
                SELECT
                    json_build_object(
                        'chart_data',
                        json_build_object(
                            'series',
                                json_build_array(
                                    json_build_object(
                                        'data', json_agg(json_build_object( 'label', 'farm', 'name', farm_name, 'value', paid_natura, 'item_id', farming_id)),
                                        'name','Платено',
                                        'stack','payment',
                                        'type','bar'
                                    ),
                                    json_build_object(
                                        'data', json_agg(json_build_object( 'label', 'farm', 'name', farm_name, 'value', unpaid_natura, 'item_id', farming_id)),
                                        'name','Оставащо',
                                        'stack','payment',
                                        'type','bar'
                                    )
                                ),
                            'xAxis',
                                json_build_array(
                                    json_build_object(
                                        'data', (SELECT json_agg(distinct name) from user_farms uf, payments_natura_by_farm pnbf where pnbf.farming_id=uf.id)
                                    )
                                )
                        ),
                        'table_data', json_agg(json_build_object('land', farm_name, 'paid', paid_natura, 'left', unpaid_natura))
                    ) echart_data
                FROM payments_natura_by_farm";

        $cmd = $this->DbModule->createCommand($sql);
        // var_dump($options);die;
        $cmd->bindParameter(':start_date', $options['start_date']);
        $cmd->bindParameter(':due_date', $options['due_date']);
        $cmd->bindValue(':farm_years', '{' . implode(',', $options['farm_years']) . '}');
        $cmd->bindParameter(':natura_id', $options['natura_id']);

        return json_decode($cmd->queryScalar());
    }

    public function getNaturaPayments($options)
    {
        $sql = 'with payroll_data as (';

        $sql .= "SELECT
                farming_id, unpaid_natura, paid_total_virtual_natura
            FROM
                tf_payroll2 (
                        :start_date,
                        :due_date,
                        :farm_years :: int4 []
                    )
        ),
        user_natura_types_zero AS (
                SELECT
                        json_agg(json_build_object('id', id, 'name',name, 'value', 0)) AS data
                FROM
                        su_renta_types
        ),
        payroll_total_by_natura_json as (
            SELECT
                natura_agg(unpaid_natura) total_unpaid_natura,
                COALESCE(natura_agg(paid_total_virtual_natura), (SELECT data FROM user_natura_types_zero)) AS total_payed_natura
            from payroll_data p
        ),
        payroll_total_by_natura_unpacked as (
            SELECT
                tun.id natura_id,
                tun.name as natura_name,
                ptvn.value as paid_natura,
                tun. value  as  unpaid_natura
            FROM
                payroll_total_by_natura_json prbnj,
                json_to_recordset(prbnj.total_unpaid_natura) as tun(id int, name text, value decimal),
                json_to_recordset(prbnj.total_payed_natura) as ptvn(id int, name text, value decimal)
            WHERE
                tun.id = ptvn.id
        )
        SELECT
        json_build_object(
            'chart_data',
            json_build_object(
                'series',
                    json_build_array(
                        json_build_object(
                            'data', json_agg(json_build_object( 'label', 'natura', 'name', natura_name, 'value', paid_natura, 'item_id', natura_id)),
                            'name','Платено',
                            'stack','payment',
                            'type','bar'
                        ),
                        json_build_object(
                            'data', json_agg(json_build_object( 'label', 'natura', 'name', natura_name, 'value', unpaid_natura, 'item_id', natura_id)),
                            'name','Оставащо',
                            'stack','payment',
                            'type','bar'
                        )
                    ),
                'xAxis',
                    json_build_array(
                        json_build_object(
                            'data', json_agg(natura_name)
                        )
                    )
            )
        ) echart_data
        from payroll_total_by_natura_unpacked";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':start_date', $options['start_date']);
        $cmd->bindParameter(':due_date', $options['due_date']);
        $cmd->bindValue(':farm_years', '{' . implode(',', $options['farm_years']) . '}');
        // var_dump($options);die;
        return json_decode($cmd->queryScalar());
    }

    public function getPaymentsPerDayByNatura($options)
    {
        $sql = 'with payments_per_day as (';
        $sql .= "SELECT
                t.date::date,
                SUM (
                    CASE
                    WHEN P .paid_from = 1
                    AND P .paid_from = P .paid_in THEN
                        P .amount
                    ELSE
                        0
                    END
                )::DECIMAL paid_leva_with_leva,
                COALESCE(SUM(SUM (P .amount) FILTER (WHERE P .paid_from = 1 AND P .paid_in = 2)) OVER w, 0)::DECIMAL paid_leva_with_natura,
                (SUM(SUM (
                    CASE
                    WHEN P .paid_from = 1
                    AND P .paid_from = P .paid_in THEN
                        P .amount
                    ELSE
                        0
                    END
                ) + SUM (
                    CASE
                    WHEN P .paid_from = 1
                    AND P .paid_in = 2
                    THEN
                        P .amount
                    ELSE
                        0
                    END
                )) OVER w)::DECIMAL AS paid_total_leva,
                SUM(SUM (
                    CASE
                    WHEN P .paid_from = 1
                    AND P .paid_from = P .paid_in THEN
                        P .amount
                    ELSE
                        0
                    END
                ) + SUM (
                    CASE
                    WHEN P .paid_from = 2
                    AND P .paid_in = 1
                    AND pn.amount != 0 THEN
                        P .amount
                    ELSE
                        0
                    END
                ) + SUM (
                    CASE
                    WHEN P .paid_from = 1
                    AND P .paid_in = 2 THEN
                        P .amount
                    ELSE
                        0
                    END
                )) OVER w paid_total_virtual_leva,
                tf_json_reduce (
                    json_agg (
                        json_build_object (
                            'name',
                            rt. NAME,
                            'value',
                            pn.amount::NUMERIC(10, 3),
                            'id',
                            rt. ID,
                            'price',
                            P .amount
                        )
                    ) FILTER (

                        WHERE
                            P .paid_from = 1
                        AND P .paid_in = 2
                    )
                ) paid_leva_with_natura_detailed,
                tf_json_reduce (
                    json_agg (
                        json_build_object (
                            'name',
                            rt. NAME,
                            'value',
                            pn.amount::NUMERIC(10, 3),
                            'id',
                            rt. ID,
                            'price',
                            (Pn.amount * pn.unit_value) :: DECIMAL
                        )
                    ) FILTER (

                        WHERE
                            (
                                P .paid_from = 2
                                AND P .paid_in = 2
                            )
                    )
                ) paid_natura_with_natura,
                tf_json_reduce (
                    tf_json_concat (
                        json_agg (
                            json_build_object (
                                'name',
                                rt. NAME,
                                'value',
                                pn.amount::NUMERIC(10, 3),
                                'id',
                                rt. ID,
                                'price',
                                (Pn.amount * pn.unit_value) :: DECIMAL
                            )
                        ) FILTER (

                            WHERE
                                (
                                    P .paid_from = 2
                                    AND P .paid_in = 2
                                )
                        ),
                        json_agg (
                        json_build_object (
                            'name',
                            rt. NAME,
                            'value',
                            pn.amount::NUMERIC(10, 3),
                            'id',
                            rt. ID,
                            'price',
                            P .amount
                        )
                    ) FILTER (

                        WHERE
                            P .paid_from = 1
                        AND P .paid_in = 2
                    )
                    )
                ) paid_total_natura,

                natura_agg (tf_json_reduce (
                    tf_json_concat (
                        json_agg (
                            json_build_object (
                                'name',
                                rt. NAME,
                                'value',
                                pn.amount::NUMERIC(10, 3),
                                'id',
                                rt. ID,
                                'price',
                                (Pn.amount * pn.unit_value) :: DECIMAL
                            )
                        ) FILTER (

                            WHERE
                                (
                                    P .paid_from = 2
                                    AND P .paid_in = 2
                                )
                        ),
                        json_agg (
                        json_build_object (
                            'name',
                            rt. NAME,
                            'value',
                            pn.amount::NUMERIC(10, 3),
                            'id',
                            rt. ID,
                            'price',
                            P .amount
                        )
                    ) FILTER (

                        WHERE
                            P .paid_from = 2
                        AND P .paid_in = 1
                    )
                    )
                )) OVER w paid_total_virtual_natura,
                tf_json_reduce (
                    json_agg (
                        json_build_object (
                            'name',
                            rt. NAME,
                            'value',
                            pn.amount::NUMERIC(10, 3),
                            'id',
                            rt. ID,
                            'price',
                            P .amount
                        )
                    ) FILTER (

                        WHERE
                            P .paid_from = 2
                        AND P .paid_in = 1
                    )
                ) paid_natura_with_leva_detailed
            FROM
                su_payments P
            INNER JOIN su_transactions T ON P .transaction_id = T . ID
            LEFT JOIN su_payments_natura pn ON pn.payment_id = P . ID
            LEFT JOIN su_renta_types rt ON rt. ID = pn.nat_type
            LEFT JOIN su_contracts C ON C . ID = P .contract_id
            WHERE
                T .status = TRUE
            AND P .farming_year = ANY (:farm_years)
            AND (:farm_id ISNULL OR p.farming_id = :farm_id)
            GROUP BY
                t.date::date
            WINDOW w as (PARTITION by t.date::date ORDER BY t.date::date)
        ),
        payments_per_day_by_natura_json as (
            SELECT
                p.date,
                paid_total_virtual_natura total_payed_natura
            from payments_per_day p
        ),
        payments_per_day_by_natura_unpacked as (SELECT
            prbf.date,
            ptvn.id natura_id,
            ptvn.name as natura_name,
            ptvn.value as paid_natura
        FROM
            payments_per_day_by_natura_json prbf,
            json_to_recordset(prbf.total_payed_natura) as ptvn(id int, name text, value decimal)
        WHERE
	        ptvn.id = :natura_id
        )
        SELECT
        json_build_object(
            'chart_data',
            json_build_object(
                'series',
                    json_build_array(
                        json_build_object(
                            'data', json_agg(json_build_object( 'label', 'kind', 'name', natura_name, 'value', paid_natura, 'item_id', natura_id)),
                            'name', 'Платено',
                            'stack', 'payment',
                            'type', 'line'
                        )
                    ),
                'xAxis', json_build_array(
                        json_build_object(
                            'data', json_agg(date)
                        )
                    )
            ),
            'table_data',null
        ) echart_data
        from payments_per_day_by_natura_unpacked";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindValue(':farm_years', '{' . implode(',', $options['farm_years']) . '}');
        $cmd->bindParameter(':natura_id', $options['natura_id']);
        $cmd->bindParameter(':farm_id', $options['farm_id']);
        // var_dump($sql, $options);
        // die;

        return json_decode($cmd->queryScalar());
    }

    public function getNaturaContracts($options, $page, $perPage)
    {
        $sql = 'with user_contracts_data as (';
        $sql .= "SELECT
                    string_agg(
                        DISTINCT
                        (CASE
                        WHEN owner_type = 1 THEN
                            concat_ws (
                                ' ',
                                o. NAME,
                                o.surname,
                                o.lastname
                            )
                        ELSE
                            o.company_name
                        END),
                        ', '
                    ) AS owner_names,
                    COALESCE (A .c_num, C .c_num) AS c_num,
                    (
                        SUM (
                            DISTINCT (
                                po.percent * (pc.area_for_rent) / 100
                            ) :: DECIMAL
                        ) - SUM (
                            DISTINCT COALESCE (pu.area, 0) :: DECIMAL
                        )
                    ) :: DECIMAL AS area
                    ,tf_json_reduce (
                        json_agg (
                            json_build_object (
                                'name',
                                rt. NAME,
                                'value',
                                (
                                    ntr.renta_value * (
                                        (
                                            po.percent * pc.area_for_rent / 100
                                        ) - COALESCE (pu.area, 0)
                                    )
                                )::NUMERIC(10, 3),
                                'id',
                                rt. ID,
                                'price',
                                0
                            )
                        ) FILTER (where crn.amount ISNULL)
                    ) AS rent_natura

                FROM
                    su_contracts C
                LEFT JOIN (
                    SELECT
                        A . ID,
                        A .parent_id,
                        A .is_annex,
                        A .start_date,
                        A .due_date,
                        A .c_num,
                        A .renta,
                        A .farming_id
                    FROM
                        su_contracts A
                    WHERE
                        A .start_date < '2018-09-30'
                    AND A .due_date >= '2017-10-01'
                    AND A .is_annex = TRUE
                    AND A .active = TRUE
                ) A ON (A .parent_id = C . ID)
                LEFT JOIN su_contracts_plots_rel pc ON (
                    pc.contract_id = COALESCE (A . ID, C . ID)
                )
                LEFT JOIN su_plots_owners_rel po ON (po.pc_rel_id = pc. ID)
                INNER JOIN su_owners o ON o. ID = po.owner_id
                LEFT JOIN su_owners_reps reps ON reps.owner_id = o. ID
                LEFT JOIN su_personal_use pu ON (
                    pu.pc_rel_id = pc. ID
                    AND pu.owner_id = po.owner_id
                    AND pu. YEAR = ALL (:farm_years)
                )
                LEFT JOIN su_charged_renta cr ON (
                    (
                        (
                            cr.owner_id = po.owner_id
                            OR cr.owner_id :: TEXT :: ltree @> po. PATH
                        )
                        AND cr.plot_id = pc.plot_id
                        AND cr.contract_id = C . ID
                    )
                    AND cr. YEAR = ALL (:farm_years)
                )
                LEFT JOIN su_contracts_rents ntr ON ntr.contract_id = COALESCE (A . ID, C . ID)
                AND ntr.renta_id NOTNULL
                LEFT JOIN su_charged_renta_natura crn ON (
                    crn.renta_id = cr. ID
                    AND crn.nat_type = ntr.renta_id
                )
                LEFT JOIN su_renta_types rt ON rt. ID = ntr.renta_id
                INNER JOIN layer_kvs kvs ON kvs.gid = pc.plot_id
                WHERE
                    C .nm_usage_rights NOT IN (1, 4)
                AND (
                    (
                        C .start_date < :start_date
                        AND C .due_date >= :due_date
                    )
                    OR (
                        A .start_date < :start_date
                        AND A .due_date >= :due_date
                    )
                )
                AND (:farm_id ISNULL OR c.farming_id = :farm_id)
                AND C .active = 'TRUE'
                AND C .is_annex = 'FALSE'
                AND C .is_sublease = 'FALSE'
                AND pc.annex_action = 'added'
                GROUP BY
                    COALESCE (A . ID, C . ID),
                    C . ID,
                    COALESCE (A .c_num, C .c_num)
            ),
            user_contracts_natura_unpacked as (
                SELECT
                    c_num,
                    string_agg(DISTINCT owner_names, ', ') owners,
                    max(area)::DECIMAL(10,3) area,
                    string_agg(DISTINCT rn.name, ', ') rent,
                    count(*) over () as row_count,
		            row_number() OVER () rn
                from
                    user_contracts_data ucd,
                    json_to_recordset(ucd.rent_natura) as rn(id int, name text, value decimal)
                where
                    rent_natura NOTNULL
                GROUP BY c_num
            )
            SELECT
                json_build_object(
                    'count', max(ucnu.row_count),
                    'rows', json_agg(row_to_json(ucnu))
                )
            FROM
                user_contracts_natura_unpacked ucnu
            WHERE
                rn BETWEEN :offset AND :limit";
        // var_dump($options);die;
        $offset = ($page - 1) * $perPage;
        $limit = $page * $perPage;
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':start_date', $options['start_date']);
        $cmd->bindParameter(':due_date', $options['due_date']);
        $cmd->bindValue(':farm_years', '{' . implode(',', $options['farm_years']) . '}');
        $cmd->bindParameter(':farm_id', $options['farm_id']);
        $cmd->bindParameter(':offset', $offset);
        $cmd->bindParameter(':limit', $limit);

        return json_decode($cmd->queryScalar());
    }

    public function getBgnPayments($options)
    {
        $sql = $this->getPaymentsContractsSQL();
        $sql .= ", payments_total as (SELECT
            COALESCE(sum(paid_total_leva_per_plot), 0)::numeric(10, 2) paid_total,
            GREATEST((COALESCE(sum(fcd.rent_in_leva), 0) - COALESCE(sum(paid_total_leva_per_plot), 0)), 0)::NUMERIC(10,2) unpaid_total
        FROM full_contract_data fcd
        LEFT JOIN payments p
        ON
        (
            p.owner_id = fcd.owner_id
            AND p.contract_id = fcd.contract_id
            AND p.plot_id = fcd.plot_id
            AND p.farming_id = fcd.farming_id
        )
        WHERE
            (:ekatte ISNULL OR ekate = :ekatte)
            AND (:farm_id ISNULL OR fcd.farming_id = :farm_id))
        SELECT
            json_build_object(
                'chart_data',
                json_build_object(
                    'xAxis', json_build_array(json_build_object('data', json_build_array('Платено', 'Оставащо'))),
                    'series', json_build_array(
                        json_build_object(
                            'name', 'Платено / Оставащо',
                            'type', 'pie',
                            'data', json_build_array(
                                json_build_object('label', '', 'name', 'Платено', 'value', paid_total),
                                json_build_object('label', '', 'name', 'Оставащо', 'value', unpaid_total)
                            )
                        )
                    )
                )
            ) echart_data
        FROM
            payments_total";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':start_date', $options['start_date']);
        $cmd->bindParameter(':due_date', $options['due_date']);
        $cmd->bindValue(':farm_years', '{' . implode(',', $options['farm_years']) . '}');
        $cmd->bindParameter(':ekatte', $options['ekatte']);
        $cmd->bindParameter(':farm_id', $options['farm_id']);

        return json_decode($cmd->queryScalar());
    }

    public function getBgnPaymentsByFarm($options)
    {
        $sql = $this->getPaymentsContractsSQL();
        $sql .= ",
                farmings as (
                    SELECT
                            id,
                            name
                        FROM dblink('host=" . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' dbname=' . DEFAULT_DB_DATABASE . "',
                            'SELECT id, name FROM su_users_farming where user_id={$options['user_id']}') AS t(id int, name text)
                ),
                prepared_cte as (
                    SELECT
                        fcd.farming_id item_id,
                        farmings.name item_name,
                        COALESCE(sum(paid_total_leva_per_plot), 0)::numeric(10, 2) paid_total,
                        GREATEST((COALESCE(sum(fcd.rent_in_leva), 0) - COALESCE(sum(paid_total_leva_per_plot), 0)), 0)::NUMERIC(10,2) unpaid_total
                    FROM full_contract_data fcd
                    LEFT JOIN payments p ON
                    (
                        p.owner_id = fcd.owner_id
                        and p.contract_id = fcd.contract_id
                        and p.plot_id = fcd.plot_id
                        and p.farming_id = fcd.farming_id
                    )
                    LEFT JOIN farmings on  fcd.farming_id = farmings.id
                    WHERE
                        (:ekatte ISNULL OR ekate = :ekatte)
                    GROUP BY
                        fcd.farming_id, farmings.name
                    ORDER BY item_name
                )
                ";
        $sql .= $this->getChartTableJsonSqlFormatter('farm');

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':start_date', $options['start_date']);
        $cmd->bindParameter(':due_date', $options['due_date']);
        $cmd->bindParameter(':ekatte', $options['ekatte']);
        $cmd->bindValue(':farm_years', '{' . implode(',', $options['farm_years']) . '}');

        return json_decode($cmd->queryScalar());
    }

    public function getBgnPaymentsPerDay($options)
    {
        $sql = "WITH payments AS (
                    SELECT (t.date)::date AS date,
                c.farming_id,
                p.owner_id,
                kvs.ekate,
                COALESCE(subpath(p.path, 0, (-1)), ((p.owner_id)::text)::ltree) AS parent,
                c.id AS contract_id,
                cpr.plot_id,
                COALESCE(p.path, ((p.owner_id)::text)::ltree) AS p_path,
                ((max(
                    CASE
                        WHEN ((p.paid_from = 1) AND (p.paid_from = p.paid_in)) THEN p.amount
                        ELSE (0)::real
                    END) * (max(cpr.area_for_rent) / COALESCE(NULLIF(sum(max(cpr.area_for_rent)) OVER contract_owner, (0)::double precision), (1)::real))))::numeric AS paid_leva_with_leva,
                    ((sum(DISTINCT
                    CASE
                        WHEN ((p.paid_from = 1) AND (p.paid_in = 2)) THEN p.amount
                        ELSE (0)::real
                    END) * (max(cpr.area_for_rent) / COALESCE(NULLIF(sum(max(cpr.area_for_rent)) OVER contract_owner, (0)::double precision), (1)::real))))::numeric AS paid_leva_with_natura,
                (((max(
                    CASE
                        WHEN ((p.paid_from = 1) AND (p.paid_from = p.paid_in)) THEN p.amount
                        ELSE (0)::real
                    END) + max(
                    CASE
                        WHEN ((p.paid_from = 1) AND (p.paid_in = 2)) THEN p.amount
                        ELSE (0)::real
                    END)) * (max(cpr.area_for_rent) / COALESCE(NULLIF(sum(max(cpr.area_for_rent)) OVER contract_owner, (0)::double precision), (1)::real))))::numeric AS paid_total_leva_per_plot,
                (((max(
                    CASE
                        WHEN ((p.paid_from = 1) AND (p.paid_from = p.paid_in)) THEN p.amount
                        ELSE (0)::real
                    END) + max(
                    CASE
                        WHEN (((p.paid_from = 2) AND (p.paid_in = 1)) AND (pn.amount <> (0)::double precision)) THEN p.amount
                        ELSE (0)::real
                    END)) + max(
                    CASE
                        WHEN ((p.paid_from = 1) AND (p.paid_in = 2)) THEN p.amount
                        ELSE (0)::real
                    END)) * (max(cpr.area_for_rent) / COALESCE(NULLIF(sum(max(cpr.area_for_rent)) OVER contract_owner, (0)::double precision), (1)::real))) AS paid_total_virtual_leva,
                tf_json_reduce(json_agg(json_build_object('name', rt.name, 'value', (pn.amount)::numeric(10,3), 'id', rt.id, 'price', p.amount)) FILTER (WHERE ((p.paid_from = 1) AND (p.paid_in = 2)))) AS paid_leva_with_natura_detailed,
                tf_json_reduce(json_agg(json_build_object('name', rt.name, 'value', (pn.amount)::numeric(10,3), 'id', rt.id, 'price', ((pn.amount * pn.unit_value))::numeric)) FILTER (WHERE ((p.paid_from = 2) AND (p.paid_in = 2)))) AS paid_natura_with_natura,
                tf_json_reduce(tf_json_concat(json_agg(json_build_object('name', rt.name, 'value', (pn.amount)::numeric(10,3), 'id', rt.id, 'price', ((pn.amount * pn.unit_value))::numeric)) FILTER (WHERE ((p.paid_from = 2) AND (p.paid_in = 2))), json_agg(json_build_object('name', rt.name, 'value', (pn.amount)::numeric(10,3), 'id', rt.id, 'price', p.amount)) FILTER (WHERE ((p.paid_from = 1) AND (p.paid_in = 2))))) AS paid_total_natura,
                tf_json_reduce(tf_json_concat(json_agg(json_build_object('name', rt.name, 'value', (pn.amount)::numeric(10,3), 'id', rt.id, 'price', ((pn.amount * pn.unit_value))::numeric)) FILTER (WHERE ((p.paid_from = 2) AND (p.paid_in = 2))), json_agg(json_build_object('name', rt.name, 'value', (pn.amount)::numeric(10,3), 'id', rt.id, 'price', p.amount)) FILTER (WHERE ((p.paid_from = 2) AND (p.paid_in = 1))))) AS paid_total_virtual_natura,
                tf_json_reduce(json_agg(json_build_object('name', rt.name, 'value', (pn.amount)::numeric(10,3), 'id', rt.id, 'price', p.amount)) FILTER (WHERE ((p.paid_from = 2) AND (p.paid_in = 1)))) AS paid_natura_with_leva_detailed,
                p.farming_year
            FROM ((((((su_payments p
                JOIN su_transactions t ON ((p.transaction_id = t.id)))
                LEFT JOIN su_payments_natura pn ON ((pn.payment_id = p.id)))
                LEFT JOIN su_renta_types rt ON ((rt.id = pn.nat_type)))
                LEFT JOIN su_contracts c ON ((c.id = p.contract_id)))
                LEFT JOIN su_contracts_plots_rel cpr ON ((c.id = cpr.contract_id)))
                LEFT JOIN su_plots_owners_rel por ON ((por.pc_rel_id = cpr.id)))
                LEFT JOIN layer_kvs kvs on kvs.gid = cpr.plot_id
            WHERE (t.status = true)
            GROUP BY cpr.plot_id, c.id, p.owner_id, p.path, c.farming_id, (t.date)::date, p.farming_year,kvs.ekate
            WINDOW contract_owner AS (PARTITION BY c.id, p.owner_id, p.farming_year, (t.date)::date)),
            payments_by_date as (
                SELECT
                    date,
                    sum(paid_total_leva_per_plot) paid_total_leva_per_plot
                FROM payments
                WHERE farming_year=ANY(:farm_years)
                AND (:farm_id ISNULL OR farming_id = :farm_id)
                AND (:ekatte ISNULL OR ekate = :ekatte)
                GROUP BY date
                ORDER BY date
            )
            SELECT
            json_build_object(
                'chart_data',
                json_build_object(
                        'series',
                            json_build_array(
                                json_build_object(
                                    'data', json_agg(json_build_object( 'label', 'kind', 'name', 'Палтено', 'value', paid_total_leva_per_plot)),
                                    'name', 'Платено',
                                    'type', 'line'
                                )
                            ),
                            'xAxis', json_build_array(
                                json_build_object(
                                        'data', json_agg(date)
                                )
                        )
                ),
                'table_data',null
            ) echart_data
        from payments_by_date";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindValue(':farm_years', '{' . implode(',', $options['farm_years']) . '}');
        $cmd->bindParameter(':farm_id', $options['farm_id']);
        $cmd->bindParameter(':ekatte', $options['ekatte']);

        return json_decode($cmd->queryScalar());
    }

    public function getBgnPaymentsPerEkatte($options)
    {
        $paymentsSql = $this->getPaymentsContractsSQL();
        $sql = "{$paymentsSql},
            ekattes AS (
                SELECT
                    ekatte_code,
                    ekatte_name
                FROM dblink('host=" . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' dbname=' . DEFAULT_DB_DATABASE . "',
                    'SELECT ekatte_code, ekatte_name FROM su_ekatte') AS t(ekatte_code text, ekatte_name text)
            ),
            prepared_cte AS (
                SELECT
                    ekatte_code item_id,
                    ekatte_name item_name,
                    COALESCE(sum(paid_total_leva_per_plot), 0)::numeric(10, 2) paid_total,
                    GREATEST((COALESCE(sum(fcd.rent_in_leva), 0) - COALESCE(sum(paid_total_leva_per_plot), 0)), 0)::NUMERIC(10,2) unpaid_total
                FROM full_contract_data fcd
                LEFT JOIN payments p
                ON
                (
                    p.owner_id = fcd.owner_id
                    AND p.contract_id = fcd.contract_id
                    AND p.plot_id = fcd.plot_id
                    AND p.farming_id = fcd.farming_id
                )
                LEFT JOIN ekattes on  ekate = ekatte_code
                GROUP BY
                    ekatte_name, ekatte_code
                ORDER BY item_name
            )";
        $sql .= $this->getChartTableJsonSqlFormatter('land');

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindValue(':farm_years', '{' . implode(',', $options['farm_years']) . '}');
        $cmd->bindParameter(':start_date', $options['start_date']);
        $cmd->bindParameter(':due_date', $options['due_date']);

        return json_decode($cmd->queryScalar());
    }

    public function getBgnPaymentsContracts($options, $page, $perPage, $count = false)
    {
        $sql = $this->getPaymentsContractsSQL();
        $sql .= ", prepared_cte as (SELECT
            c_num,
            string_agg(DISTINCT owner_names, ', ') as names,
            sum(area)::numeric(10,3) as area
            FROM full_contract_data fcd
            LEFT JOIN payments p
            ON
            (
                    p.owner_id = fcd.owner_id
                    AND p.contract_id = fcd.contract_id
                    AND p.plot_id = fcd.plot_id
                    AND p.farming_id = fcd.farming_id
            )
            WHERE
                (:ekatte ISNULL OR ekate = :ekatte)
                AND (:farm_id ISNULL OR ekate = :farm_id)
            GROUP BY c_num, p.contract_id)";
        $return = 'c_num, names, area';
        if ($count) {
            $return = 'count(*)';
        }
        $sql .= "SELECT
                {$return}
                FROM prepared_cte
            ";
        if (!$count) {
            $sql .= ' OFFSET :offset LIMIT :limit';
        }

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindValue(':farm_years', '{' . implode(',', $options['farm_years']) . '}');
        $cmd->bindParameter(':farm_id', $options['farm_id']);
        $cmd->bindParameter(':ekatte', $options['ekatte']);
        $cmd->bindParameter(':start_date', $options['start_date']);
        $cmd->bindParameter(':due_date', $options['due_date']);

        if (!$count) {
            $offset = ($page - 1) * $perPage;
            $cmd->bindParameter(':offset', $offset);
            $cmd->bindParameter(':limit', $perPage);
        }

        if ($count) {
            return $cmd->queryScalar();
        }

        return $cmd->query()->readAll();
    }

    private function getPaymentsSQL()
    {
        return "payments AS (
                SELECT
                c.farming_id,
                p.owner_id,
                COALESCE(subpath(p.path, 0, (-1)), ((p.owner_id)::text)::ltree) AS parent,
                c.id AS contract_id,
                cpr.plot_id,
                COALESCE(p.path, ((p.owner_id)::text)::ltree) AS p_path,
                max(cpr.area_for_rent) owner_area,
		        COALESCE(NULLIF(sum(max(cpr.area_for_rent)) OVER contract_owner, (0)::double precision), (1)::real) contract_area,
                ((max(
                    CASE
                        WHEN ((p.paid_from = 1) AND (p.paid_from = p.paid_in)) THEN p.amount
                        ELSE (0)::real
                    END) * (max(cpr.area_for_rent) / COALESCE(NULLIF(sum(max(cpr.area_for_rent)) OVER contract_owner, (0)::double precision), (1)::real))))::numeric AS paid_leva_with_leva,



                    ((sum(DISTINCT
                    CASE
                        WHEN ((p.paid_from = 1) AND (p.paid_in = 2)) THEN p.amount
                        ELSE (0)::real
                    END) * (max(cpr.area_for_rent) / COALESCE(NULLIF(sum(max(cpr.area_for_rent)) OVER contract_owner, (0)::double precision), (1)::real))))::numeric AS paid_leva_with_natura,

                (((max(
                    CASE
                        WHEN ((p.paid_from = 1) AND (p.paid_from = p.paid_in)) THEN p.amount
                        ELSE (0)::real
                    END) + max(
                    CASE
                        WHEN ((p.paid_from = 1) AND (p.paid_in = 2)) THEN p.amount
                        ELSE (0)::real
                    END)) * (max(cpr.area_for_rent) / COALESCE(NULLIF(sum(max(cpr.area_for_rent)) OVER contract_owner, (0)::double precision), (1)::real))))::numeric AS paid_total_leva_per_plot,
                (((max(
                    CASE
                        WHEN ((p.paid_from = 1) AND (p.paid_from = p.paid_in)) THEN p.amount
                        ELSE (0)::real
                    END) + max(
                    CASE
                        WHEN (((p.paid_from = 2) AND (p.paid_in = 1)) AND (pn.amount <> (0)::double precision)) THEN p.amount
                        ELSE (0)::real
                    END)) + max(
                    CASE
                        WHEN ((p.paid_from = 1) AND (p.paid_in = 2)) THEN p.amount
                        ELSE (0)::real
                    END)) * (max(cpr.area_for_rent) / COALESCE(NULLIF(sum(max(cpr.area_for_rent)) OVER contract_owner, (0)::double precision), (1)::real))) AS paid_total_virtual_leva,
                tf_json_reduce(json_agg(json_build_object('name', rt.name, 'value', (pn.amount)::numeric(10,3), 'id', rt.id, 'price', p.amount)) FILTER (WHERE ((p.paid_from = 1) AND (p.paid_in = 2)))) AS paid_leva_with_natura_detailed,
                tf_json_reduce(json_agg(json_build_object('name', rt.name, 'value', (pn.amount)::numeric(10,3), 'id', rt.id, 'price', ((pn.amount * pn.unit_value))::numeric)) FILTER (WHERE ((p.paid_from = 2) AND (p.paid_in = 2)))) AS paid_natura_with_natura,
                tf_json_reduce(tf_json_concat(json_agg(json_build_object('name', rt.name, 'value', (pn.amount)::numeric(10,3), 'id', rt.id, 'price', ((pn.amount * pn.unit_value))::numeric)) FILTER (WHERE ((p.paid_from = 2) AND (p.paid_in = 2))), json_agg(json_build_object('name', rt.name, 'value', (pn.amount)::numeric(10,3), 'id', rt.id, 'price', p.amount)) FILTER (WHERE ((p.paid_from = 1) AND (p.paid_in = 2))))) AS paid_total_natura,
                tf_json_reduce(tf_json_concat(json_agg(json_build_object('name', rt.name, 'value', (pn.amount)::numeric(10,3), 'id', rt.id, 'price', ((pn.amount * pn.unit_value))::numeric)) FILTER (WHERE ((p.paid_from = 2) AND (p.paid_in = 2))), json_agg(json_build_object('name', rt.name, 'value', (pn.amount)::numeric(10,3), 'id', rt.id, 'price', p.amount)) FILTER (WHERE ((p.paid_from = 2) AND (p.paid_in = 1))))) AS paid_total_virtual_natura,
                tf_json_reduce(json_agg(json_build_object('name', rt.name, 'value', (pn.amount)::numeric(10,3), 'id', rt.id, 'price', p.amount)) FILTER (WHERE ((p.paid_from = 2) AND (p.paid_in = 1)))) AS paid_natura_with_leva_detailed,
                p.farming_year
            FROM ((((((su_payments p
                JOIN su_transactions t ON ((p.transaction_id = t.id)))
                LEFT JOIN su_payments_natura pn ON ((pn.payment_id = p.id)))
                LEFT JOIN su_renta_types rt ON ((rt.id = pn.nat_type)))
                LEFT JOIN su_contracts c ON ((c.id = p.contract_id)))
                LEFT JOIN su_contracts_plots_rel cpr ON ((c.id = cpr.contract_id)))
                LEFT JOIN su_plots_owners_rel por ON ((por.pc_rel_id = cpr.id)))
                        LEFT JOIN su_charged_renta cr ON (
                            (
                                (
                                    cr.owner_id = por.owner_id
                                    OR cr.owner_id :: TEXT :: ltree @> por. PATH
                                )
                                AND cr.plot_id = cpr.plot_id
                                AND cr.contract_id = C . ID
                            )
                            AND cr. YEAR = ANY(:farm_years)
                        )
            WHERE (t.status = true and p.farming_year=ANY(:farm_years))
            GROUP BY cpr.plot_id, c.id, p.owner_id, p.path, c.farming_id, p.farming_year
            WINDOW contract_owner AS (PARTITION BY c.id, p.owner_id, p.farming_year)
            )";
    }

    private function getContractsSQL()
    {
        return "full_contract_data as  (
            SELECT
            C .farming_id,
            o. ID AS owner_id,
            o.is_dead AS is_dead,
            COALESCE (
                subpath (po. PATH, 0, - 1),
                o. ID :: TEXT :: ltree
            ) parent,
            COALESCE(po.path, o.id :: TEXT :: ltree) po_path,
            owner_type,
            (
                CASE
                WHEN owner_type = 1 THEN
                    concat_ws (
                        ' ',
                        o. NAME,
                        o.surname,
                        o.lastname
                    )
                ELSE
                    o.company_name
                END
            ) AS owner_names,
            o.rent_place,
            COALESCE (
                NULLIF (o.egn, ''),
                NULLIF (o.eik, '')
            ) egn_eik,
            concat_ws (
                ' ',
                reps.rep_name,
                reps.rep_surname,
                reps.rep_lastname
            ) AS rep_names,
            reps.rep_egn,
            pc.plot_id,
            kvs.kad_ident,
            kvs.ekate,
            SUM (COALESCE(pu.area, 0)) AS personal_area,
            C . ID AS contract_id,
            COALESCE (A .c_num, C .c_num) AS c_num,
            (
                SUM (
                    DISTINCT (
                        po.percent * (pc.area_for_rent) / 100
                    ) :: DECIMAL
                ) - SUM (
                    DISTINCT COALESCE (pu.area, 0) :: DECIMAL
                )
            ) :: DECIMAL AS area,
            SUM (
                DISTINCT (
                (
                        (case
                                when pc.rent_per_plot notnull then pc.rent_per_plot * ( ( po.percent * pc.area_for_rent / 100 ) )
                                when cr.renta notnull THEN 0
                                when A .renta notnull then A .renta * ( ( po.percent * pc.area_for_rent / 100 ) )
                                when C .renta notnull then C .renta * ( ( po.percent * pc.area_for_rent / 100 ) )
                        end) - COALESCE (pu.area, 0)
                    )
                ) :: DECIMAL
            ) AS rent_in_leva,
            SUM (
                DISTINCT (
                    cr.renta * (
                        (
                            po.percent * pc.area_for_rent / 100
                        )
                    ) - COALESCE (pu.area, 0)
                ) :: DECIMAL
            ) filter (WHERE pc.rent_per_plot isnull)
            charged_renta_leva,
            tf_json_reduce (
                json_agg (
                    json_build_object (
                        'name',
                        rt. NAME,
                        'value',
                        (
                            ntr.renta_value * (
                                (
                                    po.percent * pc.area_for_rent / 100
                                ) - COALESCE (pu.area, 0)
                            )
                        )::NUMERIC(10, 3),
                        'id',
                        rt. ID,
                        'price',
                        0
                    )
                ) FILTER (where crn.amount ISNULL)
            ) AS rent_natura,
            tf_json_reduce (
                json_agg (
                    json_build_object (
                        'name',
                        rt. NAME,
                        'value',
                        (
                            CASE
                            WHEN (crn.nat_is_converted) THEN
                                NULL
                            ELSE
                                (
                                    crn.amount * (
                                        (
                                            pc.area_for_rent * (po.percent /(100) :: DECIMAL)
                                        ) - COALESCE (pu.area,(0) :: DECIMAL)
                                    )
                                )
                            END
                        )::NUMERIC(10, 3),
                        'id',
                        rt. ID,
                        'price',
                        0
                    )
                ) filter (where crn.amount NOTNULL)
            ) AS charged_renta_nat,
            tf_json_reduce (
                json_agg (
                    json_build_object (
                        'name',
                        rt. NAME,
                        'value',
                        (
                            CASE
                            WHEN (crn.nat_is_converted) THEN
                                (
                                    (
                                        crn.amount * crn.nat_unit_price
                                    ) * (
                                        (
                                            pc.area_for_rent * (po.percent /(100) :: DECIMAL)
                                        ) - COALESCE (pu.area,(0) :: DECIMAL)
                                    )
                                )
                            ELSE
                                NULL
                            END
                        )::NUMERIC(10, 3),
                        'id',
                        rt. ID,
                        'price',
                        0
                    )
                ) FILTER (WHERE crn.nat_is_converted)
            ) AS converted_charged_renta_nat,
            tf_json_reduce (
                json_agg (
                    json_build_object (
                        'name',
                        rt. NAME,
                        'value',
                        (
                            COALESCE (
                                CASE
                                WHEN (crn.nat_is_converted) THEN
                                    NULL
                                ELSE
                                    (
                                        crn.amount * (
                                            (
                                                pc.area_for_rent * (po.percent /(100) :: DECIMAL)
                                            ) - COALESCE (pu.area,(0) :: DECIMAL)
                                        )
                                    )
                                END
                                ,
                                (
                                    ntr.renta_value * (
                                        (
                                            po.percent * pc.area_for_rent / 100
                                        ) - COALESCE (pu.area, 0)
                                    )
                                )
                            )
                        )::NUMERIC(10, 3),
                        'id',
                        rt. ID,
                        'price',
                        0
                    )
                )
            ) AS final_rent_natura
            FROM
                su_contracts C
            LEFT JOIN (
                SELECT
                    A . ID,
                    A .parent_id,
                    A .is_annex,
                    A .start_date,
                    A .due_date,
                    A .c_num,
                    A .renta,
                    A .farming_id
                FROM
                    su_contracts A
                WHERE
                    A .start_date < :start_date
                AND A .due_date >= :due_date
                AND A .is_annex = TRUE
                AND A .active = TRUE
            ) A ON (A .parent_id = C . ID)
            LEFT JOIN su_contracts_plots_rel pc ON (
                pc.contract_id = COALESCE (A . ID, C . ID)
            )
            LEFT JOIN su_plots_owners_rel po ON (po.pc_rel_id = pc. ID)
            INNER JOIN su_owners o ON o. ID = po.owner_id
            LEFT JOIN su_owners_reps reps ON reps.owner_id = o. ID
            LEFT JOIN su_personal_use pu ON (
                pu.pc_rel_id = pc. ID
                AND pu.owner_id = po.owner_id
                AND pu. YEAR = ANY(:farm_years)
            )
            LEFT JOIN su_charged_renta cr ON (
                (
                    (
                        cr.owner_id = po.owner_id
                        OR cr.owner_id :: TEXT :: ltree @> po. PATH
                    )
                    AND cr.plot_id = pc.plot_id
                    AND cr.contract_id = C . ID
                )
                AND cr. YEAR = ANY(:farm_years)
            )
            LEFT JOIN su_contracts_rents ntr ON ntr.contract_id = COALESCE (A . ID, C . ID)
            AND ntr.renta_id NOTNULL
            LEFT JOIN su_charged_renta_natura crn ON (
                crn.renta_id = cr. ID
                AND crn.nat_type = ntr.renta_id
            )
            LEFT JOIN su_renta_types rt ON rt. ID = ntr.renta_id
            INNER JOIN layer_kvs kvs ON kvs.gid = pc.plot_id
            WHERE
                C .nm_usage_rights NOT IN (1, 4)
            AND (
                (
                    C .start_date < :start_date
                    AND C .due_date >= :due_date
                )
                OR (
                    A .start_date < :start_date
                    AND A .due_date >= :due_date
                )
            )
            AND C .active = 'TRUE'
            AND C .is_annex = 'FALSE'
            AND C .is_sublease = 'FALSE'
            AND pc.annex_action = 'added'

            AND (
                COALESCE (NULL, '') = ''
                OR COALESCE (
                    NULLIF (
                        concat_ws (
                            ' ',
                            o. NAME,
                            o.surname,
                            o.lastname
                        ),
                        ''
                    ),
                    o.company_name
                ) ILIKE '%' || NULL || '%'
            )
            AND (
                COALESCE (NULL, '') = ''
                OR o.egn ILIKE '%' || NULL || '%'
            )
            AND (
                array_length(
                    COALESCE (NULL, '{}' :: int4 []),
                    1
                ) ISNULL
                OR C .farming_id = ALL ('{null}')
            )
            GROUP BY
                pc.plot_id,
                o. ID,
                COALESCE (A . ID, C . ID),
                C . ID,
                COALESCE (A .c_num, C .c_num),
                C .farming_id,
                kvs.kad_ident,
                kvs.ekate,
                po. PATH,
                reps.rep_name,
                reps.rep_surname,
                reps.rep_lastname,
                reps.rep_egn,
                o.rent_place
            ORDER BY
                o. ID

        )";
    }

    private function getChartTableJsonSqlFormatter($type)
    {
        return "SELECT
                json_build_object(
                    'chart_data',
                    json_build_object(
                        'series',
                            json_build_array(
                                json_build_object(
                                    'data', json_agg(json_build_object( 'label', '{$type}', 'name', item_name, 'item_id', item_id, 'value', paid_total)),
                                    'name', 'Платено',
                                    'stack', 'payment',
                                    'type', 'bar'
                                ),

                                json_build_object(
                                    'data', json_agg(json_build_object( 'label', '{$type}', 'name', item_name, 'item_id', item_id, 'value', unpaid_total)),
                                    'name', 'Остатък',
                                    'stack', 'payment',
                                    'type', 'bar'
                                )
                            ),
                            'xAxis',
                            json_build_array(
                                json_build_object(
                                    'data', json_agg(item_name)
                                )
                            )
                    ),
                    'table_data', json_agg(json_build_object('{$type}', item_name, 'paid', paid_total, 'left', unpaid_total))
            ) echart_data
            FROM prepared_cte";
    }
}
