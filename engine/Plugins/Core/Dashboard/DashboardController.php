<?php

namespace TF\Engine\Plugins\Core\Dashboard;

use Prado\Prado;
use TF\Engine\Kernel\StringHelper;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * NotificationController class file.
 *
 * <AUTHOR>
 */
// Prado::using('Plugins.Core.Base.BaseController');
// Prado::using('Plugins.Core.Dashboard.*');

class DashboardController extends UserDbController
{
    /**
     * @var DashboardModel
     */
    public $DbHandler;

    public $Database;

    /**
     * @var string
     */
    public $String;

    public function __construct($database)
    {
        $this->DbHandler = new DashboardModel($database);
        $this->Database = $database;
        $this->StringHelper = new StringHelper();
    }

    public function getPaymentsData($options, $counter, $returnOnlySQL)
    {
        return $this->DbHandler->getPayments($options, $counter, $returnOnlySQL);
    }

    public function getContractDates($options, $counter, $returnOnlySQL)
    {
        return $this->DbHandler->getContractDates($options, $counter, $returnOnlySQL);
    }

    public function conection()
    {
        return $this->DbHandler->connection();
    }

    public function rentaNatura($params)
    {
        return $this->DbHandler->rentNatura($params);
    }

    public function getContractsGrid($params)
    {
        return $this->DbHandler->getContractsGrid($params);
    }

    public function getOwnGrid($params)
    {
        return $this->DbHandler->getOwnGrid($params);
    }

    public function getRentContracts($params)
    {
        return $this->DbHandler->getRentContracts($params);
    }

    public function getSubleasedContracts($params)
    {
        return $this->DbHandler->getSubleasedContracts($params);
    }

    public function getPaymentsTransactions($params)
    {
        return $this->DbHandler->getTransactions($params);
    }

    public function getNaturaPaymentsByFarm($params)
    {
        $user = Prado::getApplication()->getUser();
        $params['user_id'] = $user->UserID;

        return $this->DbHandler->getNaturaPaymentsByFarm($params);
    }

    public function getNaturaPayments($params)
    {
        $user = Prado::getApplication()->getUser();
        $params['user_id'] = $user->UserID;

        return $this->DbHandler->getNaturaPayments($params);
    }

    public function getPaymentsPerDayByNatura($params)
    {
        $farmYear = $this->StringHelper->getFarmingYearByDate($params['start_date']);
        $user = Prado::getApplication()->getUser();
        $params['user_id'] = $user->UserID;
        $params['farm_years'] = [$farmYear];

        return $this->DbHandler->getPaymentsPerDayByNatura($params);
    }

    public function getNaturaContracts($params, $page = 1, $perPage = 10)
    {
        $user = Prado::getApplication()->getUser();
        $params['user_id'] = $user->UserID;

        return $this->DbHandler->getNaturaContracts($params, $page, $perPage);
    }

    public function getBgnPayments($params)
    {
        $user = Prado::getApplication()->getUser();
        $params['user_id'] = $user->UserID;

        return $this->DbHandler->getBgnPayments($params);
    }

    public function getBgnPaymentsByFarm($params)
    {
        $user = Prado::getApplication()->getUser();
        $params['user_id'] = $user->UserID;

        return $this->DbHandler->getBgnPaymentsByFarm($params);
    }

    public function getBgnPaymentsPerDay($params)
    {
        $user = Prado::getApplication()->getUser();
        $params['user_id'] = $user->UserID;

        return $this->DbHandler->getBgnPaymentsPerDay($params);
    }

    public function getBgnPaymentsPerEkatte($params)
    {
        $user = Prado::getApplication()->getUser();
        $params['user_id'] = $user->UserID;

        return $this->DbHandler->getBgnPaymentsPerEkatte($params);
    }

    public function getBgnPaymentsContracts($params, $page = 1, $perPage = 10)
    {
        $user = Prado::getApplication()->getUser();
        $params['user_id'] = $user->UserID;
        $rows = $this->DbHandler->getBgnPaymentsContracts($params, $page, $perPage);
        $count = $this->DbHandler->getBgnPaymentsContracts($params, $page, $perPage, true);

        return [
            'rows' => $rows,
            'total' => $count,
        ];
    }
}
