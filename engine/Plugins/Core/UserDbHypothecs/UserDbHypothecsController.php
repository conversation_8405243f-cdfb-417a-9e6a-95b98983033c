<?php

namespace TF\Engine\Plugins\Core\UserDbHypothecs;

use TF\Engine\Plugins\Core\UserDb\UserDbController;

// Prado::using('Plugins.Core.UserDb.UserDbController');

class UserDbHypothecsController extends UserDbController
{
    public $DbHandler;
    public $Database;

    public function __construct($database)
    {
        $this->DbHandler = new UserDbHypothecsModel($database);
        $this->Database = $database;
    }

    public function getHypothecs($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getHypothecs($options, $counter, $returnOnlySQL);
    }

    public function getHypothecPlots($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getHypothecPlots($options, $counter, $returnOnlySQL);
    }

    public function getAvailablePlotsForHypothec($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getAvailablePlotsForHypothec($options, $counter, $returnOnlySQL);
    }
}
