<?php

namespace TF\Engine\Plugins\Core\UserDbHypothecs;

use PDO;
use TF\Engine\Plugins\Core\UserDb\UserDbModel;

class UserDbHypothecsModel extends UserDbModel
{
    public function getHypothecs($options, $counter, $returnOnlySQL)
    {
        if (!$options['return']) {
            $return = '*';
        }

        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter'] ?? false);

        $sql = "SELECT {$return} FROM {$this->tableHypothecs} h ";
        $sql .= "LEFT JOIN {$this->tableCreditors} c ON(c.id = h.creditor_id)
				LEFT JOIN {$this->tableHypothecsPlotsRel} ph ON(ph.hypothec_id = h.id)
				LEFT JOIN {$this->tableKVS} p ON(p.gid = ph.plot_id)";
        $sql .= 'WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if (isset($options['group'])) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if (isset($options['having'])) {
            $sql .= ' HAVING TRUE ';
            $sql = $this->createHavingSQL($sql, $options['having'], $returnOnlySQL);
        }

        if (!$counter && isset($options['sort'])) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        if (isset($options['having'])) {
            $this->createHavingBinds($cmd, $options['having']);
        }

        return $cmd->query()->readAll();
    }

    public function getHypothecPlots($options, $counter, $returnOnlySQL)
    {
        if (!$options['return']) {
            $return = '*';
        }

        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = "SELECT {$return} FROM {$this->tableKVS} p
                INNER JOIN {$this->tableHypothecsPlotsRel} r ON(p.gid = r.plot_id)
                WHERE true";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];

            $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
            $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

            if (false !== $limit && false !== $offset) {
                $sql .= $returnOnlySQL
                    ? " LIMIT {$limit} OFFSET {$offset}"
                    : ' LIMIT :limit OFFSET :offset';
            }
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (!$counter && $options['sort']) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getAvailablePlotsForHypothec($options, $counter, $returnOnlySQL)
    {
        if (!$options['return']) {
            $return = '*';
        }

        $return = $this->createReturnVariable($options['return'], $counter, $options['custom_counter']);

        $sql = "SELECT {$return} FROM {$this->tableKVS} p
                INNER JOIN {$this->contractsPlotsRelTable} pc ON(pc.plot_id = p.gid)
                INNER JOIN {$this->tableContracts} c ON(c.id = pc.contract_id)
                LEFT JOIN {$this->tableSubleasesPlotsContractsRel} sr ON(sr.pc_rel_id = pc.id and sr.sublease_id in (
                    select id from su_contracts sc where sc.is_sublease = true and sc.start_date <= :h_start_date and sc.due_date >= :h_start_date and sc.active = true
                ))
                LEFT JOIN {$this->tableContracts} s ON(s.id = sr.sublease_id AND s.start_date <= :h_start_date AND s.due_date >= :h_start_date AND s.active = TRUE)
                LEFT JOIN {$this->salesContractsPlotsRelTable} slp ON(slp.plot_id = p.gid)
                LEFT JOIN {$this->tableSalesContracts} sl ON(sl.id = slp.sales_contract_id AND sl.start_date::date <= :h_start_date)
                WHERE true";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['having']) {
            $sql .= ' HAVING TRUE ';
            $sql = $this->createHavingSQL($sql, $options['having'], $returnOnlySQL);
        }

        if (!$counter && $options['sort']) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return str_replace(':h_start_date', "'" . $options['h_start_date'] . "'", $sql);
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        if ($options['having']) {
            $this->createHavingBinds($cmd, $options['having']);
        }

        $cmd->bindParameter(':h_start_date', $options['h_start_date']);

        return $cmd->query()->readAll();
    }
}
