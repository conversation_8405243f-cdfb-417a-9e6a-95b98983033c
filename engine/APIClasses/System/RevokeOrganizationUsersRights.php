<?php

namespace TF\Engine\APIClasses\System;

use Exception;
use Prado\Web\Services\TJsonResponse;
use TF\Application\Entity\User;

/**
 * @json-module System
 *
 * @json revoke-organization-users-rights
 */
class RevokeOrganizationUsersRights extends TJsonResponse
{
    public function getJsonContent()
    {
        if ('POST' != $_SERVER['REQUEST_METHOD']) {
            throw new Exception('Request method not allowed');
        }

        if (!isset($_POST['organizationId']) || !isset($_POST['rights']) || !isset($_POST['countryCode'])) {
            throw new Exception('Organziation, rights, and country code must be submitted');
        }

        $organizationId = $_POST['organizationId'];
        $rights = json_decode($_POST['rights'], true);
        // dd($rights);
        $countryCode = $_POST['countryCode'];

        try {
            $organization = User::finder()->find(
                'username = :name',
                [':name' => strtolower($countryCode) . '_' . $organizationId]
            );

            if (!$organization) {
                throw new Exception('No organization found');
            }

            $subUsers = $organization->getSubUsers();
            foreach ($subUsers as $subUser) {
                $subUser->deleteUserRights(['rights' => $rights]);
            }

            return $this->response(true, 'Organization permissions revoked', $organizationId);
        } catch (Exception $ex) {
            throw $ex;
        }
    }

    protected function response($success = true, $message = '', $result = '')
    {
        return [
            'jsonrpc' => '2.0',
            'success' => $success,
            'message' => $message,
            'result' => $result,
        ];
    }
}
