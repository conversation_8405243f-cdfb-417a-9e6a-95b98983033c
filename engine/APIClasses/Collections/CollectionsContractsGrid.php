<?php

namespace TF\Engine\APIClasses\Collections;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\ExportToExcelClass;
use TF\Engine\Kernel\PrintPdf;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;
use TF\Engine\Plugins\Core\UserDbSubleases\UserDbSubleasesController;
use TF\Engine\Plugins\Core\UserDbSubleases\UserDbSubleasesModel;

/**
 * Грид "Договори".
 *
 * @rpc-module Collections
 *
 * @rpc-service-id collections-contracts-grid
 */
class CollectionsContractsGrid extends TRpcApiProvider
{
    private $module = 'Collections';
    private $service_id = 'collections-contracts-grid';

    /**
     * Registers the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getCollectionsContracts'],
                'validators' => [
                    'filterObj' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'getCollectionsPersonalUse' => ['method' => [$this, 'getCollectionsPersonalUse'],
                'validators' => [
                    'filterObj' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'printCollectionContracts' => ['method' => [$this, 'printCollectionContracts']],
            'exportCollectionContracts' => ['method' => [$this, 'exportCollectionContracts']],
            'exportCollectionPersonalUse' => ['method' => [$this, 'exportCollectionPersonalUse']],
        ];
    }

    /**
     * Gets subleases.
     *
     * @api-method read
     *
     * @param array $filterObj
     *                         {
     *                         #item string c_num,
     *                         #item boolean c_num_complete_match,
     *                         #item integer c_status,
     *                         #item integer c_type,
     *                         #item integer category,
     *                         #item integer collections_search_year
     *                         #item string company_eik,
     *                         #item string company_name,
     *                         #item string date_from,
     *                         #item string date_to,
     *                         #item string due_date_from,
     *                         #item string due_date_to,
     *                         #item array ekate [],
     *                         #item array farming [],
     *                         #item string irrigated_area,
     *                         #item string kad_ident,
     *                         #item string masiv,
     *                         #item array ntp [],
     *                         #item string number,
     *                         #item string owner_egn,
     *                         #item string owner_name,
     *                         #item array renta_types [],
     *                         #item string rep_egn,
     *                         #item string rep_name,
     *                         }
     * @param int $page pagination parameters
     * @param int $rows pagination parameters
     * @param string $sort pagination parameters
     * @param string $order pagination parameters
     *
     * @return array
     */
    public function getCollectionsContracts(array $filterObj, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if ($this->User->isGuest) {
            return [];
        }

        $UserDbSubleasesController = new UserDbSubleasesController($this->User->Database);
        $return = [
            'rows' => [],
            'total' => 0,
            'footer' => [
                [
                    'renta_txt' => '<b>ОБЩО</b>',
                    'contract_area' => '',
                    'rent_area' => '',
                    'money_to_collect_txt' => '',
                    'collected_money_txt' => '',
                    'unpaid_txt' => '',
                ],
            ],
        ];

        $countTotals = $UserDbSubleasesController->getCollectionsContractData($filterObj, true, false);

        $count = $countTotals[0]['count'];

        if (0 === $count) {
            return $return;
        }

        $results = $UserDbSubleasesController->getCollectionsContractData($filterObj, false, false);

        $formatedResult = array_map(function ($row) use ($filterObj) {
            $row['nm_usage_rights'] = $GLOBALS['Contracts']['ContractTypes'][$row['nm_usage_rights']]['name'];
            $row['farming_year'] = $filterObj['collections_search_year'];
            $row['money_to_collect_txt'] = BGNtoEURO($row['money_to_collect']);
            $row['renta_txt'] = BGNtoEURO($row['renta']);
            $row['unpaid_txt'] = BGNtoEURO($row['unpaid']);
            if (is_numeric($row['collected_money'])) {
                $row['collected_money_txt'] = BGNtoEURO($row['collected_money']);
            } else {
                $row['collected_money_txt'] = '-';
            }

            if ($row['payday'] && '-' != $row['payday']) {
                $payday = explode('-', $row['payday']);
                $row['payday'] = $payday[0] . ' ' . $GLOBALS['Months'][$payday[1]];
            }

            return $row;
        }, $results);

        $return['rows'] = $formatedResult;
        $return['total'] = count($formatedResult);
        $return['footer'] = [
            [
                'renta_txt' => '<b>ОБЩО</b>',
                'contract_area' => $countTotals[0]['active_contract_sublease_contract_area'] ?? '-',
                'rent_area' => $countTotals[0]['active_contract_rent_area'] ?? '-',
                'money_to_collect_txt' => BGNtoEURO($countTotals[0]['active_contract_money_to_collect']),
                'collected_money_txt' => BGNtoEURO($countTotals[0]['collected_money']),
                'unpaid_txt' => BGNtoEURO($countTotals[0]['active_contract_unpaid']),
            ],
            [
                'renta_txt' => '<b>Брой договори</b>',
                'contract_area' => $count,
            ],
        ];

        return $return;
    }

    /**
     * @api-method printCollectionContracts
     *
     * @param array $filterObj
     *                         {
     *                         #item string c_num,
     *                         #item boolean c_num_complete_match,
     *                         #item integer c_status,
     *                         #item integer c_type,
     *                         #item integer category,
     *                         #item integer collections_search_year
     *                         #item string company_eik,
     *                         #item string company_name,
     *                         #item string date_from,
     *                         #item string date_to,
     *                         #item string due_date_from,
     *                         #item string due_date_to,
     *                         #item array ekate [],
     *                         #item array farming [],
     *                         #item string irrigated_area,
     *                         #item string kad_ident,
     *                         #item string masiv,
     *                         #item array ntp [],
     *                         #item string number,
     *                         #item string owner_egn,
     *                         #item string owner_name,
     *                         #item array renta_types [],
     *                         #item string rep_egn,
     *                         #item string rep_name,
     *                         }
     *
     * @return string
     */
    public function printCollectionContracts($rpcParams)
    {
        $FarmingController = new FarmingController('Farming');
        $time = strtotime(date('Y-m-d H:i:s'));
        $type = 'collections_';

        if (!is_dir(PUBLIC_UPLOAD_RELATIVE_PATH . '/export/' . $this->User->GroupID)) {
            mkdir(PUBLIC_UPLOAD_RELATIVE_PATH . '/export/' . $this->User->GroupID, 0777);
        }

        $path = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->GroupID . '/' . $type . $time . '.pdf';
        $relativePath = PUBLIC_UPLOAD_RELATIVE_PATH . '/export/' . $this->User->GroupID . '/' . $type . $time . '.pdf';

        $results = $this->getCollectionsContracts($rpcParams);

        $ltext = $FarmingController->StringHelper->loadTemplate(
            $GLOBALS['Templates'][42]['template'],
            [
                'pdf_data' => $results['rows'],
                'footer' => $results['footer'],
            ]
        );
        $ltext = '<page style="font-family: freeserif"><br />' . $ltext . '</page>';

        $printPdf = new PrintPdf();
        $printPdf->generateFromHtml($ltext, $path, ['orientation' => 'Landscape'], true);

        return $relativePath;
    }

    /**
     * @api-method exportCollectionContracts
     *
     * @param array $filterObj
     *                         {
     *                         #item string c_num,
     *                         #item boolean c_num_complete_match,
     *                         #item integer c_status,
     *                         #item integer c_type,
     *                         #item integer category,
     *                         #item integer collections_search_year
     *                         #item string company_eik,
     *                         #item string company_name,
     *                         #item string date_from,
     *                         #item string date_to,
     *                         #item string due_date_from,
     *                         #item string due_date_to,
     *                         #item array ekate [],
     *                         #item array farming [],
     *                         #item string irrigated_area,
     *                         #item string kad_ident,
     *                         #item string masiv,
     *                         #item array ntp [],
     *                         #item string number,
     *                         #item string owner_egn,
     *                         #item string owner_name,
     *                         #item array renta_types [],
     *                         #item string rep_egn,
     *                         #item string rep_name,
     *                         }
     *
     * @return string
     */
    // public function exportCollectionContracts($rpcParams)
    // {
    //     $rpcParams['exportToExcel'] = true;

    //     $time = strtotime(date('Y-m-d H:i:s'));

    //     if (null == $rpcParams['c_status']) {
    //         $rpcParams['c_status'] = UserDbSubleasesModel::CONTRACT_STATUS_NOT_CANCELED;
    //     }

    //     $filename = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID . '/collections_' . $time . '.xlsx';

    //     if (!file_exists(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID)) {
    //         mkdir(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID, 0777);
    //     }
    //     $headers = [
    //         'owner_names' => 'Име',
    //         'egn_eik' => 'ЕГН',
    //         'farming_name' => 'Стопанство',
    //         'farming_year' => 'Стопанска година',
    //         'renta_type_name' => 'Култура',
    //         'personal_use_area' => 'Площ(дка)',
    //         'average_yield' => 'Среден добив (дка)',
    //         'personal_use_rent_quantity' => 'Дължимо количество',
    //         'personal_use_paid_rent_quantity' => 'Изплатено количество',
    //         'personal_use_unpaid_rent_quantity' => 'Оставащо количество',
    //         'treatments_price' => 'Обработки (лв./дка)',
    //         'personal_use_treatments_sum' => 'Сума (лв.)',
    //         'personal_use_paid_treatments' => 'Внесена сума (лв.)',
    //         'personal_use_unpaid_treatments' => 'Остатък сума (лв)',
    //     ];

    //     $data = $this->getCollectionsPersonalUse($rpcParams);

    //     $data['footer'][0]['egn_eik'] = str_replace('<b>', '', $data['footer'][0]['egn_eik']);
    //     $data['footer'][0]['egn_eik'] = str_replace('</b>', '', $data['footer'][0]['egn_eik']);

    //     $exportExcelDoc = new ExportToExcelClass();
    //     $exportExcelDoc->export($data['rows'], $headers, $data['footer']);
    //     $exportExcelDoc->saveFile($filename);

    //     return 'files/uploads/export/' . $this->User->UserID . '/collections_' . $time . '.xlsx';
    // }
    public function exportCollectionContracts($rpcParams)
    {
        $rpcParams['exportToExcel'] = true;

        $time = strtotime(date('Y-m-d H:i:s'));

        if (null == $rpcParams['c_status']) {
            $rpcParams['c_status'] = UserDbSubleasesModel::CONTRACT_STATUS_NOT_CANCELED;
        }

        $filename = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID . '/collections_' . $time . '.xlsx';

        if (!file_exists(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID)) {
            mkdir(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID, 0777);
        }

        $headers = [
            'c_num' => 'Договор',
            'nm_usage_rights' => 'Тип',
            'subleasing_farm_name' => 'Наемодател',
            'start_date' => 'Дата на влизане в сила',
            'due_date' => 'Крайна дата',
            'owner_names' => 'Наемател/Арендатор',
            'payday' => 'Дата на падеж на плащането',
            'renta_txt' => 'Дължима рента в пари на декар',
            'contract_area' => 'Площ по договор (дка)',
            'money_to_collect_txt' => 'Сума за получаване',
            'collected_money_txt' => 'Общо получени до момента',
            'unpaid_txt' => 'Остатък',
        ];

        $data = $this->getCollectionsContracts($rpcParams);
        $data['footer'][0]['renta_txt'] = str_replace('<b>', '', $data['footer'][0]['renta_txt']);
        $data['footer'][0]['renta_txt'] = str_replace('</b>', '', $data['footer'][0]['renta_txt']);
        $data['footer'][1]['renta_txt'] = str_replace('<b>', '', $data['footer'][1]['renta_txt']);
        $data['footer'][1]['renta_txt'] = str_replace('</b>', '', $data['footer'][1]['renta_txt']);

        $exportExcelDoc = new ExportToExcelClass();
        $exportExcelDoc->export($data['rows'], $headers, $data['footer']);
        $exportExcelDoc->saveFile($filename);

        return SITE_URL . 'files/uploads/export/' . $this->User->UserID . '/collections_' . $time . '.xlsx';
    }

    public function exportCollectionPersonalUse($rpcParams)
    {
        $time = strtotime(date('Y-m-d H:i:s'));

        $filename = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID . '/collections_pu_' . $time . '.xlsx';

        if (!file_exists(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID)) {
            mkdir(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID, 0777);
        }

        $headers = [
            'c_num' => 'Договор',
            'farming_name' => 'Стопанство',
            'farming_mol' => 'МОЛ',
            'start_date' => 'Начална дата',
            'due_date' => 'Крайна дата',
            'owner_names' => 'Собственик/длъжник',
            'egn_eik' => 'ЕГН/ЕИК',
            'renta_type_name' => 'Тип натура',
            'personal_use_area' => 'Площ за лично ползване',
            'personal_use_treatments_sum' => 'Сума за обработки',
            'personal_use_paid_treatments' => 'Платени обработки',
            'personal_use_unpaid_treatments' => 'Остатък за получаване',
        ];

        $data = $this->getCollectionsPersonalUse($rpcParams);

        $data['footer'][0]['egn_eik'] = str_replace('<b>', '', $data['footer'][0]['egn_eik']);
        $data['footer'][0]['egn_eik'] = str_replace('</b>', '', $data['footer'][0]['egn_eik']);

        $exportExcelDoc = new ExportToExcelClass();
        $exportExcelDoc->export($data['rows'], $headers, $data['footer']);
        $exportExcelDoc->saveFile($filename);

        return SITE_URL . 'files/uploads/export/' . $this->User->UserID . '/collections_pu_' . $time . '.xlsx';
    }

    public function getCollectionsPersonalUse(array $filterObj, ?int $page = null, ?int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);
        $FarmingController = new FarmingController('Farming');
        $return = [
            'rows' => [],
            'total' => 0,
            'footer' => [
                [
                    'egn_eik' => '<b>ОБЩО</b>',
                    'personal_use_area' => '',
                    'personal_use_treatments_sum' => '',
                    'personal_use_paid_treatments' => '',
                    'personal_use_unpaid_treatments' => '',
                ],
            ],
        ];

        $farmings = $FarmingController->getUserFarmings();
        $userFarmingIds = array_keys($farmings);

        $options['where'] = [
            'year' => ['column' => 'year', 'compare' => '=', 'prefix' => 'pu', 'value' => $filterObj['pu_farming_year']],
            'c_num' => ['column' => 'c_num', 'compare' => 'ILIKE', 'prefix' => 'c', 'value' => $filterObj['c_num']],
            'c_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'c', 'value' => $filterObj['c_id']],
            'farming' => ['column' => 'farming_id', 'compare' => 'IN', 'prefix' => 'c', 'value' => $userFarmingIds],
            'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => true],
            'a_exists' => ['column' => 'active', 'compare' => 'IS', 'prefix' => 'a', 'value' => 'NULL'],
        ];

        $options['farming_year_start'] = $GLOBALS['Farming']['years'][$filterObj['pu_farming_year']]['start_date'];
        $options['farming_year_end'] = $GLOBALS['Farming']['years'][$filterObj['pu_farming_year']]['end_date'];

        // 0 and values < -1 are system values that allow showing all types (-1) or records without types (0)
        if ('0' === $filterObj['renta_type']) {
            $options['where']['rentaType'] = ['column' => 'renta_type', 'compare' => 'IS', 'prefix' => 'pur', 'value' => 'NULL'];
        } elseif ($filterObj['renta_type'] > 0) {
            $options['where']['rentaType'] = ['column' => 'renta_type', 'compare' => '=', 'prefix' => 'pur', 'value' => $filterObj['renta_type']];
        }
        if ($filterObj['owner_names']) {
            $tmp_person_names = preg_replace('/\s+/', '.*', $filterObj['owner_names']);
            $tmp_person_names = mb_strtolower($tmp_person_names, 'UTF-8');
            $options['where']['owner_names'] = ['column' => "lower(TRIM(COALESCE(o.name, ''))) || ' ' || lower(TRIM(COALESCE(o.surname, ''))) || ' ' || lower(TRIM(COALESCE(o.lastname, ''))) || ' ' || lower(TRIM(COALESCE(o.company_name, '')))", 'compare' => '~', 'value' => $tmp_person_names];
        }
        if ($filterObj['egn_eik']) {
            $options['where']['owner_names'] = ['column' => "lower(TRIM (o.egn)) || ' ' || lower(TRIM (o.eik))", 'compare' => '~', 'value' => $filterObj['egn_eik']];
        }

        // get the total results, without pagination
        $counter = $UserDbPaymentsController->getCollectionsPersonalUse($options, true, false);
        if (0 == $counter[0]['count']) {
            return $return;
        }
        $options['offset'] = ($page - 1) * $rows;
        $options['sort'] = $sort;
        $options['order'] = $order;
        $options['limit'] = $rows;

        $personal_use_area = 0;
        $personal_use_treatments_sum = 0;
        $personal_use_paid_treatments = 0;
        $personal_use_unpaid_treatments = 0;

        $results = $UserDbPaymentsController->getCollectionsPersonalUse($options, false, false);
        foreach ($results as &$result) {
            $result['personal_use_area'] = number_format($result['personal_use_area'], 3, '.', '');
            $result['personal_use_treatments_sum_txt'] = BGNtoEURO($result['personal_use_treatments_sum']);
            $result['personal_use_paid_treatments_txt'] = BGNtoEURO($result['personal_use_paid_treatments']);
            $result['personal_use_unpaid_treatments_txt'] = BGNtoEURO($result['personal_use_unpaid_treatments']);

            $personal_use_area += $result['personal_use_area'];
            $personal_use_treatments_sum += $result['personal_use_treatments_sum'];
            $personal_use_paid_treatments += $result['personal_use_paid_treatments'];
            $personal_use_unpaid_treatments += $result['personal_use_unpaid_treatments'];
        }
        $return['footer'][0]['personal_use_area'] = BGNtoEURO($personal_use_area);
        $return['footer'][0]['personal_use_treatments_sum_txt'] = BGNtoEURO($personal_use_treatments_sum);
        $return['footer'][0]['personal_use_paid_treatments_txt'] = BGNtoEURO($personal_use_paid_treatments);
        $return['footer'][0]['personal_use_unpaid_treatments_txt'] = BGNtoEURO($personal_use_unpaid_treatments);

        $return['rows'] = $results;
        $return['total'] = $counter[0]['count'];

        return $return;
    }
}
