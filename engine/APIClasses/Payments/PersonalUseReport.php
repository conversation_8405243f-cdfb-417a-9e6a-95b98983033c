<?php

namespace TF\Engine\APIClasses\Payments;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\ExportToExcelClass;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;

/**
 * Справка Изплатено в брой/по банков път
 *
 * @rpc-module Payments
 *
 * @rpc-service-id personal-use-report
 *
 * @property UserDbPaymentsController $UserDbPaymentsController
 * @property UserDbController $UserDbController
 */
class PersonalUseReport extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getPersonalUseReport'],
                'validators' => [
                    'rpcParams' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'export' => ['method' => [$this, 'exportPersonalUseReport']],
        ];
    }

    public function getPersonalUseReport(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        // Initialize the method controllers
        $FarmingController = new FarmingController('Farming');
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        $farmingYear = $rpcParams['pu_farming_year'];
        $rentaType = $rpcParams['renta_type'];
        $ownerName = $rpcParams['owner_name'];
        $ownerEGN = $rpcParams['owner_egn'];
        $userFarmings = $FarmingController->getUserFarmings();
        $userFarmingIds = array_keys($userFarmings);

        $return = [
            'rows' => [],
            'total' => 0,
            'footer' => [
                [
                    'rent_type' => '<b>ОБЩО</b>',
                ],
            ],
        ];

        $options['where'] = [
            'year' => ['column' => 'year', 'compare' => '=', 'prefix' => 'pu', 'value' => $farmingYear],
            'pu_area' => ['column' => 'area', 'compare' => '>', 'prefix' => 'pur', 'value' => 0],
            'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
            'farming' => ['column' => 'farming_id', 'compare' => 'IN', 'prefix' => 'c', 'value' => $userFarmingIds],
            'a_exists' => ['column' => 'active', 'compare' => 'IS', 'prefix' => 'a', 'value' => 'NULL'],
        ];

        if ($rpcParams['c_num']) {
            $options['where'] = [
                'c_num' => ['column' => 'c_num', 'compare' => 'ILIKE', 'prefix' => 'c', 'value' => $rpcParams['c_num']],
            ];
        }

        // 0 and values < -1 are system values that allow showing all types (-1) or records without types (0)
        if ('0' === $rentaType) {
            $options['where']['rentaType'] = ['column' => 'renta_type', 'compare' => 'IS', 'prefix' => 'pur', 'value' => 'NULL'];
        } elseif ($rentaType > 0) {
            $options['where']['rentaType'] = ['column' => 'renta_type', 'compare' => '=', 'prefix' => 'pur', 'value' => $rentaType];
        }

        if ($ownerName) {
            $tmp_owner_names = preg_replace('/\s+/', '.*', $ownerName);
            $tmp_owner_names = mb_strtolower($tmp_owner_names, 'UTF-8');
            $options['where']['owner_names'] = ['column' => "lower(TRIM (o.NAME)) || ' ' || lower(TRIM (o.surname)) || ' ' || lower(TRIM (o.lastname))", 'compare' => '~', 'value' => $tmp_owner_names];
        }

        if ($ownerEGN) {
            $options['where']['owner_egn'] = ['column' => 'egn', 'prefix' => 'o', 'compare' => 'ILIKE', 'value' => $ownerEGN];
        }

        $options['farming_year_start'] = $GLOBALS['Farming']['years'][$farmingYear]['start_date'];
        $options['farming_year_end'] = $GLOBALS['Farming']['years'][$farmingYear]['end_date'];

        // get the total results, without pagination
        $allResults = $UserDbPaymentsController->getCollectionsPersonalUse($options, false, false);
        $counter = count($allResults);
        if (0 == $counter) {
            return $return;
        }

        $options['offset'] = ($page - 1) * $rows;

        if ('id' === $sort) {
            $options['sort'] = 'o.id';
        } else {
            $options['sort'] = $sort;
        }
        $options['order'] = $order;
        $options['limit'] = $rows;

        $results = $UserDbPaymentsController->getCollectionsPersonalUse($options, false, false);

        $return['footer'][0]['renta_type_name'] = '<b>ОБЩО:</b>';

        foreach ($results as &$result) {
            $result['farming_year_id'] = $result['farming_year'];
            $result['farming_year'] = $GLOBALS['Farming']['years'][$result['farming_year']]['farming_year_short'];

            $return['footer'][0]['personal_use_area'] += round($result['personal_use_area'], 3);
            $return['footer'][0]['personal_use_rent_quantity'] += round($result['personal_use_rent_quantity'], 2);
            $return['footer'][0]['personal_use_paid_rent_quantity'] += round($result['personal_use_paid_rent_quantity'], 2);
            $return['footer'][0]['personal_use_unpaid_rent_quantity'] += round($result['personal_use_unpaid_rent_quantity'], 2);
            $return['footer'][0]['treatments_price'] += round($result['treatments_price'], 2);
            $return['footer'][0]['personal_use_treatments_sum'] += round($result['personal_use_treatments_sum'], 2);
            $return['footer'][0]['personal_use_paid_treatments'] += round($result['personal_use_paid_treatments'], 2);
            $return['footer'][0]['personal_use_unpaid_treatments'] += round($result['personal_use_unpaid_treatments'], 2);

            $result['area'] = number_format($result['area'], 3, '.', '');
            $result['rent_quantity'] = number_format($result['rent_quantity'], 2, '.', '');
            $result['personal_use_area'] = number_format($result['personal_use_area'], 3, '.', '');
            $result['personal_use_rent_quantity'] = number_format($result['personal_use_rent_quantity'], 2, '.', '');
            $result['personal_use_paid_rent_quantity'] = number_format($result['personal_use_paid_rent_quantity'], 2, '.', '');
            $result['personal_use_unpaid_rent_quantity'] = number_format($result['personal_use_unpaid_rent_quantity'], 2, '.', '');

            $result['treatments_price'] = BGNtoEURO($result['treatments_price']);
            $result['personal_use_treatments_sum'] = BGNtoEURO($result['personal_use_treatments_sum']);
            $result['personal_use_paid_treatments'] = BGNtoEURO($result['personal_use_paid_treatments']);
            $result['personal_use_unpaid_treatments'] = BGNtoEURO($result['personal_use_unpaid_treatments']);
        }

        $return['footer'][0]['treatments_price'] = BGNtoEURO($return['footer'][0]['treatments_price']);
        $return['footer'][0]['personal_use_treatments_sum'] = BGNtoEURO($return['footer'][0]['personal_use_treatments_sum']);
        $return['footer'][0]['personal_use_paid_treatments'] = BGNtoEURO($return['footer'][0]['personal_use_paid_treatments']);
        $return['footer'][0]['personal_use_unpaid_treatments'] = BGNtoEURO($return['footer'][0]['personal_use_unpaid_treatments']);

        $return['rows'] = $results;
        $return['total'] = $counter;

        return $return;
    }

    public function exportPersonalUseReport($rpcParams, $sort = '', $order = '')
    {
        // Get the filtered results, using the getPersonalUseReport method
        $resultsToBePrinted = $this->getPersonalUseReport($rpcParams, null, null, $sort, $order);

        unset($resultsToBePrinted['total']);

        // set the header columns
        $headerRow[0] = 'Име';
        $headerRow[1] = 'ЕГН';
        $headerRow[2] = 'Стопанство';
        $headerRow[3] = 'Договор';
        $headerRow[4] = 'Стопанска година';
        $headerRow[5] = 'Култура';
        $headerRow[6] = 'Площ(дка)';
        $headerRow[7] = 'Среден добив (дка)';
        $headerRow[8] = 'Дължимо количество';
        $headerRow[9] = 'Изплатено количество';
        $headerRow[10] = 'Оставащо количество';
        $headerRow[11] = 'Обработки (лв./дка)';
        $headerRow[12] = 'Сума (лв.)';
        $headerRow[13] = 'Внесена сума (лв.)';
        $headerRow[14] = 'Остатък сума (лв)';

        $footer[0][5] = str_replace('<b>', '', $resultsToBePrinted['footer'][0]['rent_type']);
        $footer[0][5] = str_replace('</b>', '', $footer[0][4]);
        $footer[0][6] = $resultsToBePrinted['footer'][0]['personal_use_area'];
        $footer[0][7] = $resultsToBePrinted['footer'][0]['personal_use_rent_quantity'];
        $footer[0][8] = $resultsToBePrinted['footer'][0]['personal_use_paid_rent_quantity'];
        $footer[0][10] = $resultsToBePrinted['footer'][0]['personal_use_unpaid_rent_quantity'];
        $footer[0][12] = $resultsToBePrinted['footer'][0]['personal_use_treatments_sum'];
        $footer[0][13] = $resultsToBePrinted['footer'][0]['personal_use_paid_treatments'];
        $footer[0][14] = $resultsToBePrinted['footer'][0]['personal_use_unpaid_treatments'];

        // Loop through the remaining elements and transform them into sequential array
        // so to be able to match the result columns with the header columns
        $finalResultsArray = [];
        $rowsCount = count($resultsToBePrinted['rows']);

        for ($i = 0; $i < $rowsCount; $i++) {
            // Assign the necessary columns their respective indexes
            $finalResultsArray[$i] = $this->extractAndSortColumns($resultsToBePrinted['rows'][$i]);
        }

        // Add timestamp to ensure unique file names
        $date = date('Y-m-d-H-i-s');

        // set the file name as variable
        $name = str_replace('/', '_', 'personal_use' . $this->User->UserID . '_' . $date);
        $name .= '.xlsx';
        // set the file path
        $path = PUBLIC_UPLOAD_BLANK . '/' . $this->User->GroupID . '/';

        // create the directory if it doesn't exist
        if (!is_dir($path)) {
            mkdir($path, 0774);
        }

        // create the return array
        $return = [];

        $exportExcelDoc = new ExportToExcelClass();
        $exportExcelDoc->export($finalResultsArray, $headerRow, $footer);
        $exportExcelDoc->saveFile($path . $name);

        // Set the return variables
        $return['file_path'] = PUBLIC_UPLOAD_BLANKS_RELATIVE_PATH . $this->User->GroupID . '/' . $name;
        $return['file_name'] = $name;

        return $return;
    }

    /**
     * Helper method which is used to get only the neccessary for printing columns
     * and to arrange them in a sequential array, in the required order.
     *
     * @param array $currentRow
     *
     * @return array
     */
    private function extractAndSortColumns($currentRow)
    {
        // Initialize the return value
        $sortedArray = [];

        // Get only the necessary columns and sort them accordingly
        $sortedArray[0] = $currentRow['owner_names'];
        $sortedArray[1] = $currentRow['egn_eik'];
        $sortedArray[2] = $currentRow['farming_name'];
        $sortedArray[3] = $currentRow['c_num'];
        $sortedArray[4] = $currentRow['farming_year'];
        $sortedArray[5] = $currentRow['renta_type_name'];
        $sortedArray[6] = $currentRow['personal_use_area'];
        $sortedArray[7] = $currentRow['average_yield'];
        $sortedArray[8] = $currentRow['personal_use_rent_quantity'];
        $sortedArray[9] = $currentRow['personal_use_paid_rent_quantity'];
        $sortedArray[10] = $currentRow['personal_use_unpaid_rent_quantity'];
        $sortedArray[11] = $currentRow['treatments_price'];
        $sortedArray[12] = $currentRow['personal_use_treatments_sum'];
        $sortedArray[13] = $currentRow['personal_use_paid_treatments'];
        $sortedArray[14] = $currentRow['personal_use_unpaid_treatments'];

        return $sortedArray;
    }
}
