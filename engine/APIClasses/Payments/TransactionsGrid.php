<?php

namespace TF\Engine\APIClasses\Payments;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\ExportToExcelClass;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;

/**
 * Transactions Grid.
 *
 * @rpc-module Payments
 *
 * @rpc-service-id transactions-grid
 */
class TransactionsGrid extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readTransactionsGrid']],
            'exportToExcel' => ['method' => [$this, 'exportToExcelTransactionsGrid']],
            'disableTransaction' => ['method' => [$this, 'disableTransaction']],
        ];
    }

    /**
     * Read transactions grid.
     *
     * @api-method read
     *
     * @param array $data
     *                    {
     *                    #item string id                       -The transaction id
     *                    #item string transaction_start_date   -The transaction start date
     *                    #item string transaction_due_date     -The transaction due date
     *                    #item array transaction_types          -The transaction types
     *                    #item string paid_to                  -The transaction paid to
     *                    #item string paid_to_egn              -The transaction paid to egn
     *                    #item string paid_by                  -The transaction paid by
     *                    #item string farming_year             -The farming year
     *                    }
     * @param int $page -The current page number
     * @param int $rows -Rows per page
     * @param string $sort -A grid column by which the grid is sorted
     * @param string $order -The sort order ASC/DESC
     *
     * @return array result
     *               {
     *               #item array rows             -The results
     *               #item string total           -The count of all results
     *               }
     */
    public function readTransactionsGrid($data, $page = null, $rows = null, $sort = null, $order = null)
    {
        $data['owners_list'] = $data['owners_list'] ? array_map('intval', $data['owners_list']) : [];

        if (isset($data['transaction_types'])) {
            $data['transaction_types'] = is_array($data['transaction_types']) ? array_filter($data['transaction_types']) : $data['transaction_types'];
        } else {
            $data['transaction_types'] = [];
        }

        // creating the default return
        $default = [
            'rows' => [],
            'total' => 0,
            'footer' => [
                [
                    'date' => '<b>ОБЩО</b>',
                    'ttype' => '',
                    'paid_in_text' => '',
                    'paid_from_text' => '',
                ],
            ],
        ];

        if ('' == $sort) {
            $sort = $data['sort'];
        }
        if ('' == $order) {
            $order = $data['order'];
        }

        // init controllers
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);
        $FarmingController = new FarmingController('Farming');
        $userFarmings = $FarmingController->getUserFarmings(true);
        $farmingIds = array_keys($userFarmings);

        if (isset($data['farming_id']) && in_array((int) $data['farming_id'], $farmingIds)) {
            $farmingIds = [(int)$data['farming_id']];
        }

        $data['paid_by'] = $data['paid_by'] ? preg_replace('/\s+/', ' ', $data['paid_by']) : '';
        $data['paid_to'] = $data['paid_to'] ? preg_replace('/\s+/', ' ', $data['paid_to']) : '';

        $aFarmingYear = null;

        if (!is_array($data['farming_year'])) {
            $aFarmingYear[] = $data['farming_year'];
        } else {
            $aFarmingYear = $data['farming_year'];
        }

        $aFarmingYear = array_values(array_filter($aFarmingYear, function ($val) {
            if (!empty($val)) {
                return $val;
            }
        }));

        $options = [
            'custom_counter' => 'count(DISTINCT(t.id))',
            'return' => [
                't.id', 'max(t.date) as date', 'max(t.bank_acc) as bank_acc', 'max(t.recipient) as recipient',
                'max(t.payer_name) as payer_name',
                'max(t.recipient_egn) as recipient_egn', 'max(t.recipient_proxy) as recipient_proxy',
                'round(max(t.amount)::numeric,2) as amount', 't.farming_year',
                'array_agg(pn.nat_type) as payment_nat_type',
                'array_agg(round(pn.amount::numeric, 3)) as amount_nat',
                'max(t.paid_from) as paid_from',
                'max(t.paid_in) as paid_in',
                'array_agg(DISTINCT(c.farming_id)) AS farming_ids',
                't.bank_payment',
                't.bank_payment_type',
                'array_agg(DISTINCT(c.c_num)) as c_num',
                't.status status',
                't.cancelled_by',
                'max(o.post_payment_fields::text) as owner_post_payment_fields',
            ],
            'where' => [
                'id' => ['column' => 'id', 'prefix' => 't', 'compare' => '=', 'value' => $data['id']],
                'c_num' => ['column' => 'c_num', 'prefix' => 'c', 'compare' => 'ILIKE', 'value' => $data['c_num']],
                'paid_by' => ['column' => 'payer_name', 'prefix' => 't', 'compare' => '=', 'value' => $data['paid_by']],
                'cancelled_by' => ['column' => 'cancelled_by', 'prefix' => 't', 'compare' => '=', 'value' => $data['cancelled_by']],
                'paid_to_egn' => ['column' => 'recipient_egn', 'prefix' => 't', 'compare' => 'ILIKE', 'value' => $data['paid_to_egn']],
                'transaction_due_date' => ['column' => 'date', 'prefix' => 't', 'compare' => '<=', 'value' => $data['transaction_due_date']],
                'transaction_start_date' => ['column' => 'date', 'prefix' => 't', 'compare' => '>=', 'value' => $data['transaction_start_date']],
                'farming' => ['column' => 'farming_id', 'prefix' => 'c', 'compare' => 'IN', 'value' => $farmingIds],
            ],
            'transaction_types' => array_filter($data['transaction_types']),
            'sort' => $sort,
            'order' => $order,
            'limit' => $rows,
            'offset' => ($page - 1) * $rows,
            'group' => 't.id, t.farming_year, t.bank_payment, t.status, t.cancelled_by, t.bank_payment_type',
        ];

        if (!empty($data['owners_list'])) {
            $options['where']['owners_list'] = ['column' => 'id', 'prefix' => 'o', 'compare' => 'IN', 'value' => $data['owners_list']];
        }

        if (true == $data['c_num_complete_match']) {
            $options['where']['c_num']['compare'] = '=';
        }

        if (null !== $aFarmingYear && null != $aFarmingYear[0]) {
            $options['where']['farming_year'] = ['column' => 'farming_year', 'prefix' => 't', 'compare' => 'IN', 'value' => $aFarmingYear];
        }

        if ($data['paid_to']) {
            $tmp_recipient_name = preg_replace('/\s+/', '.*', $data['paid_to']);
            $tmp_recipient_name = mb_strtolower($tmp_recipient_name, 'UTF-8');
            if (!$options['return']) {
                $options['return'] = [];
            }
            $options['return'][] = "regexp_matches(lower(TRIM (t.recipient)), '{$tmp_recipient_name}','g')";
        }

        $counter = $UserDbPaymentsController->getTransactionsByParams($options, true, false, ($data['print'] ? 'true' : 'all'));

        if (0 == $counter[0]['count']) {
            return $default;
        }

        $results = $UserDbPaymentsController->getTransactionsByParams($options, false, false, ($data['print'] ? 'true' : 'all'));
        $resultsCount = count($results);
        if (0 == $resultsCount) {
            return $default;
        }

        $finalFarmings = [];
        foreach ($userFarmings as $farmingId => $farming) {
            $finalFarmings[$farmingId]['name'] = $farming['name'];
            $finalFarmings[$farmingId]['eik'] = $farming['bulstat'];
            $finalFarmings[$farmingId]['sender_post_payment_fields'] = $farming['post_payment_fields'];
        }

        // get all transactions
        unset($options['limit'],$options['offset']);

        $allResults = $UserDbPaymentsController->getTransactionsByParams($options, false, false);
        $allResCount = count($allResults);

        // get renta types
        $renta_types = $this->getNaturaTypes($UserDbController);

        for ($i = 0; $i < $resultsCount; $i++) {
            $results[$i]['ttype'] = 'от ' . (1 == $results[$i]['paid_from'] ? 'Лева' : 'Натура') . ' в ' . (1 == $results[$i]['paid_in'] ? 'Лева' : 'Натура');
            $results[$i]['date'] = strftime('%d.%m.%Y', strtotime($results[$i]['date']));
            $results[$i]['farming_year'] = $GLOBALS['Farming']['years'][$results[$i]['farming_year']]['farming_year_short'];
            $results[$i]['farming_ids'] = trim($results[$i]['farming_ids'], '{}');
            $results[$i]['c_num'] = trim($results[$i]['c_num'], '{}');

            $results[$i]['farming_ids'] = explode(',', $results[$i]['farming_ids']);

            $farmingsString = '';
            $farmingIdsCount = count($results[$i]['farming_ids']);
            for ($j = 0; $j < $farmingIdsCount; $j++) {
                $farmingsString .= $finalFarmings[(int)trim($results[$i]['farming_ids'][$j])]['name'];
                if ($j < $farmingIdsCount - 1) {
                    $farmingsString .= '<br />';
                }
            }

            $results[$i]['farmings'] = $farmingsString;

            $results[$i]['sender_post_payment_fields'] = '';
            if ($farmingIdsCount) {
                $results[$i]['sender_post_payment_fields'] = $finalFarmings[(int)trim($results[$i]['farming_ids'][0])]['sender_post_payment_fields'];
            }

            if (1 == $results[$i]['paid_from']) {
                $results[$i]['paid_from_text'] = BGNtoEURO($results[$i]['amount']);
            } else {
                $payment_nat_types = explode(',', trim($results[$i]['payment_nat_type'], '{}'));
                $payment_nat_types_count = count($payment_nat_types);
                $amount_nat = explode(',', trim($results[$i]['amount_nat'], '{}'));
                $tmp_payments_nat_types = [];

                for ($j = 0; $j < $payment_nat_types_count; $j++) {
                    $tmp_payments_nat_types[$payment_nat_types[$j]] += $amount_nat[$j];
                }

                if (count($tmp_payments_nat_types) > 0) {
                    ksort($tmp_payments_nat_types);
                    foreach ($tmp_payments_nat_types as $renta_type => $value) {
                        $results[$i]['paid_from_text'] .= number_format($value, 3, '.', '') . ' X [' . $renta_types[$renta_type] . ']<br/>';
                    }
                }
            }

            if (1 == $results[$i]['paid_in']) {
                $results[$i]['paid_in_text'] = BGNtoEURO($results[$i]['amount']);
            } else {
                $payment_nat_types = explode(',', trim($results[$i]['payment_nat_type'], '{}'));
                $amount_nat = explode(',', trim($results[$i]['amount_nat'], '{}'));
                $tmp_payments_nat_types = [];
                $payment_nat_types_count = count($payment_nat_types);
                for ($j = 0; $j < $payment_nat_types_count; $j++) {
                    if (is_numeric($amount_nat[$j])) {
                        $tmp_payments_nat_types[$payment_nat_types[$j]] += $amount_nat[$j];
                    }
                }

                if (count($tmp_payments_nat_types) > 0) {
                    ksort($tmp_payments_nat_types);
                    foreach ($tmp_payments_nat_types as $renta_type => $value) {
                        $results[$i]['paid_in_text'] .= number_format($value, 3, '.', '') . ' X [' . $renta_types[$renta_type] . ']<br/>';
                    }
                }
            }
        }

        // get sum of columns type, paid_in_text, paid_from_text
        $typeArr = [];
        $paid_in_natura_text_arr = [];
        $paid_in_leva_text = 0;
        $paid_from_natura_text_arr = [];
        $paid_from_leva_text = 0;
        for ($i = 0; $i < $allResCount; $i++) {
            // ttype
            $ttype = 'от ' . (1 == $allResults[$i]['paid_from'] ? 'Лева' : 'Натура') . ' в ' . (1 == $allResults[$i]['paid_in'] ? 'Лева' : 'Натура');
            if (!in_array($ttype, $typeArr)) {
                $typeArr[] = $ttype;
            }

            // paid_in_text
            if (1 == $allResults[$i]['paid_in']) {
                $paid_in_leva_text += $allResults[$i]['amount'];
            } else {
                $payment_nat_types = explode(',', trim($allResults[$i]['payment_nat_type'], '{}'));
                $payment_nat_types_count = count($payment_nat_types);
                $amount_nat = explode(',', trim($allResults[$i]['amount_nat'], '{}'));
                $tmp_payments_nat_types = [];

                for ($j = 0; $j < $payment_nat_types_count; $j++) {
                    $tmp_payments_nat_types[$payment_nat_types[$j]] += is_numeric($amount_nat[$j]) ? $amount_nat[$j] : 0;
                }

                if (count($tmp_payments_nat_types) > 0) {
                    foreach ($tmp_payments_nat_types as $renta_type => $value) {
                        $paid_in_natura_text_arr[$renta_type] += $value;
                    }
                }
            }

            if (1 == $allResults[$i]['paid_from']) {
                $paid_from_leva_text += (float)($allResults[$i]['amount']);
            } else {
                $payment_nat_types = explode(',', trim($allResults[$i]['payment_nat_type'], '{}'));
                $payment_nat_types_count = count($payment_nat_types);
                $amount_nat = explode(',', trim($allResults[$i]['amount_nat'], '{}'));
                $tmp_payments_nat_types = [];

                for ($j = 0; $j < $payment_nat_types_count; $j++) {
                    $tmp_payments_nat_types[$payment_nat_types[$j]] += $amount_nat[$j];
                }

                if (count($tmp_payments_nat_types) > 0) {
                    foreach ($tmp_payments_nat_types as $renta_type => $value) {
                        $paid_from_natura_text_arr[$renta_type] += $value;
                    }
                }
            }
        }

        $return = [
            'rows' => $results,
            'total' => $counter[0]['count'],
        ];

        if ($data['print']) {
            // formatted total type
            $ttypeText = '';
            if (count($typeArr) > 0) {
                sort($typeArr);
                $ttypeText = implode('</br>', $typeArr);
            }

            // formatted total paid_in_text
            $paid_in_arr = [];

            if ($paid_in_leva_text > 0) {
                $paid_in_arr[] = BGNtoEURO($paid_in_leva_text);
            }

            if (count($paid_in_natura_text_arr) > 0) {
                ksort($paid_in_natura_text_arr);

                foreach ($paid_in_natura_text_arr as $renta_type => $value) {
                    if ($renta_type && $value) {
                        $paid_in_arr[] = number_format($value, 3, '.', '') . ' X [' . $renta_types[$renta_type] . ']';
                    }
                }
            }

            $paid_in_text = implode('</br>', $paid_in_arr);

            // formatted total paid_from_text
            $paid_from_arr = [];

            if ($paid_from_leva_text > 0) {
                $paid_from_arr[] = BGNtoEURO($paid_from_leva_text);
            }
            if (count($paid_from_natura_text_arr) > 0) {
                ksort($paid_from_natura_text_arr);

                foreach ($paid_from_natura_text_arr as $renta_type => $value) {
                    if ($renta_type && $value) {
                        $paid_from_arr[] = number_format($value, 3, '.', '') . ' X [' . $renta_types[$renta_type] . ']';
                    }
                }
            }

            $paid_from_text = implode('</br>', $paid_from_arr);

            $return['footer'] = [
                [
                    'date' => '<b>ОБЩО</b>',
                    'ttype' => $ttypeText,
                    'paid_in_text' => $paid_in_text,
                    'paid_from_text' => $paid_from_text,
                ],
            ];
        }

        return $return;
    }

    public function disableTransaction($id)
    {
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        return $UserDbPaymentsController->disableTransactionsById((int)$id, $this->User->Name);
    }

    /**
     * Export to excel transactions grid.
     *
     * @api-method exportToExcel
     *
     * @param array $dataRpc
     *                       {
     *                       #item array $data
     *                       {
     *                       #item string id                       -The transaction id
     *                       #item string transaction_start_date   -The transaction start date
     *                       #item string transaction_due_date     -The transaction due date
     *                       #item string transaction_type         -The transaction type
     *                       #item string paid_to                  -The transaction paid to
     *                       #item string paid_to_egn              -The transaction paid to egn
     *                       #item string paid_by                  -The transaction paid by
     *                       #item string farming_year             -The farming year
     *                       }
     *                       #item string sort                         -A grid column by which the grid is sorted.
     *                       #item string order                        -The sort order ASC/DESC.
     *                       }
     *
     * @return array [filePath , filename]           -Path to the file to excel export
     */
    public function exportToExcelTransactionsGrid($dataRpc)
    {
        $dataRpc['data']['print'] = true;
        $results = $this->readTransactionsGrid($dataRpc['data']);

        $column_headers = [
            'id' => '№ на транзакция',
            'date' => 'Дата',
            'c_num' => 'Договор',
            'ttype' => 'Вид на транзакция',
            'paid_from_text' => 'Изплащане на',
            'paid_in_text' => 'Изплащане чрез',
            'payer_name' => 'Изплатено от',
            'farmings' => 'Стопанство',
            'recipient' => 'Изплатено на',
            'recipient_egn' => 'ЕГН',
            'recipient_proxy' => 'Пълномощно №/дата',
            'bank_acc' => 'Банкова сметка',
            'farming_year' => 'Стопанска година',
        ];

        foreach ($results['rows'] as $key => $row) {
            $results['rows'][$key]['paid_from_text'] = str_replace('<br/>', ',', $row['paid_from_text']);
            $results['rows'][$key]['paid_in_text'] = str_replace('<br/>', ',', $row['paid_in_text']);
        }

        $results['footer'][0]['date'] = str_replace('<b>', '', $results['footer'][0]['date']);
        $results['footer'][0]['date'] = str_replace('</b>', '', $results['footer'][0]['date']);
        $results['footer'][0]['ttype'] = str_replace('</br>', ',', $results['footer'][0]['ttype']);
        $results['footer'][0]['paid_from_text'] = str_replace('</br>', ',', $results['footer'][0]['paid_from_text']);
        $results['footer'][0]['paid_in_text'] = str_replace('</br>', ',', $results['footer'][0]['paid_in_text']);

        $date = date('Y-m-d-H-i-s');
        $fileName = 'transakcii_' . $date . '.xlsx';
        $path = PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID . '/' . $fileName;

        if (!file_exists(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID)) {
            mkdir(PUBLIC_UPLOAD_EXPORT . '/' . $this->User->UserID, 0777);
        }

        $exportExcelDoc = new ExportToExcelClass();
        $exportExcelDoc->export($results['rows'], $column_headers, $results['footer']);
        $exportExcelDoc->saveFile($path);

        $filePath = PUBLIC_UPLOAD_EXPORT_RELATIVE_PATH . '/' . $this->User->UserID . '/' . $fileName;

        return ['file_path' => $filePath, 'file_name' => $fileName];
    }

    /**
     * @param UserDbController $UserDbController
     *
     * @return array
     */
    private function getNaturaTypes(&$UserDbController)
    {
        $options = [
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
        ];
        $renta_results = $UserDbController->getItemsByParams($options, false, false);
        $rentCount = count($renta_results);
        // form renta types array
        $renta_types = [];
        for ($i = 0; $i < $rentCount; $i++) {
            $renta_types[$renta_results[$i]['id']] = $renta_results[$i]['name'] . '(' . $GLOBALS['Contracts']['renta_units'][$renta_results[$i]['unit']]['name'] . ')';
        }

        return $renta_types;
    }
}
