<?php

namespace TF\Engine\APIClasses\Payments;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\ArrayHelper;
use TF\Engine\Kernel\ExportToExcelClass;
use TF\Engine\Kernel\Loader;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbAreaTypes\UserDbAreaTypesController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Add Charged Payment.
 *
 * @rpc-module Payments
 *
 * @rpc-service-id add-charged-renta
 */
class AddChargedRenta extends TRpcApiProvider
{
    private $UserDbController;
    private $UserDbPaymentsController;
    private $UserDbOwnersController;
    private $UsersController;
    private $module = 'Payments';
    private $service_id = 'add-charged-renta';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'saveChargedRenta' => ['method' => [$this, 'saveChargedRenta']],
            'prevChargedRenta' => ['method' => [$this, 'prevChargedRenta']],
            'prevChargedRentaExport' => ['method' => [$this, 'prevChargedRentaExport']],
        ];
    }

    /**
     * Save charged renta.
     *
     * @api-method saveChargedRenta
     *
     * @param array $data
     *                    {
     *                    #item array data
     *                    {
     *                    #item string year
     *                    #item string ekate
     *                    #item string masiv
     *                    #item string number
     *                    #item string acr_type
     *                    #item array category
     *                    #item array mestnost
     *                    #item string cnum
     *                    #item string contract_type
     *                    #item string farming
     *                    #item string contract_natura
     *                    #item array owner_ids
     *                    #item string owner_egn
     *                    #item array rep_ids
     *                    #item string rep_egn
     *                    #item array company_ids
     *                    #item string company_eik
     *                    #item string renta
     *                    #item int renta_nat
     *                    #item string natura
     *                    #item bool nat_id_converted
     *                    #item array multirents
     *                    {
     *                    #item string renta_nat_type
     *                    #item string renta_value
     *                    #item string price_per_unit
     *                    #item bool is_converted
     *                    }
     *                    }
     *                    }
     *
     * @throws MTRpcException
     */
    public function saveChargedRenta($data)
    {
        $this->prepareChargedRentaData($data);
    }

    /**
     * Save charged renta.
     *
     * @api-method prevChargedRenta
     *
     * @param array $data
     *                    {
     *                    #item array data
     *                    {
     *                    #item string year
     *                    #item string ekate
     *                    #item string masiv
     *                    #item string number
     *                    #item string acr_type
     *                    #item array category
     *                    #item string cnum
     *                    #item string contract_type
     *                    #item string farming
     *                    #item string contract_natura
     *                    #item array owner_ids
     *                    #item string owner_egn
     *                    #item array rep_ids
     *                    #item string rep_egn
     *                    #item array company_ids
     *                    #item string company_eik
     *                    #item string renta
     *                    #item int renta_nat
     *                    #item string natura
     *                    #item bool nat_id_converted
     *                    #item array multirents
     *                    {
     *                    #item string renta_nat_type
     *                    #item string renta_value
     *                    #item string price_per_unit
     *                    #item bool is_converted
     *                    }
     *                    }
     *                    }
     *
     * @throws MTRpcException
     *
     * @return array
     */
    public function prevChargedRenta($data)
    {
        $results = $this->prepareChargedRentaData($data, true);

        return [
            'total' => count($results),
            'rows' => $results,
        ];
    }

    /**
     * Save charged renta.
     *
     * @api-method saveChargedRenta
     *
     * @param array $data
     *                    {
     *                    #item array data
     *                    {
     *                    #item string year
     *                    #item string ekate
     *                    #item string masiv
     *                    #item string number
     *                    #item string acr_type
     *                    #item array category
     *                    #item string cnum
     *                    #item string contract_type
     *                    #item string farming
     *                    #item string contract_natura
     *                    #item array owner_ids
     *                    #item string owner_egn
     *                    #item array rep_ids
     *                    #item string rep_egn
     *                    #item array company_ids
     *                    #item string company_eik
     *                    #item string renta
     *                    #item int renta_nat
     *                    #item string natura
     *                    #item bool nat_id_converted
     *                    #item array multirents
     *                    {
     *                    #item string renta_nat_type
     *                    #item string renta_value
     *                    #item string price_per_unit
     *                    #item bool is_converted
     *                    }
     *                    }
     *                    }
     * @param bool $prev
     *
     * @throws MTRpcException
     */
    public function prepareChargedRentaData($data, $prev = false)
    {
        // Initialize the method controllers
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UsersController = new UsersController('Users');
        $UserDbAreaTypesController = new UserDbAreaTypesController($this->User->Database);
        $arrayHelper = new ArrayHelper(); // new Loader('Kernel.ArrayHelper');

        $data = (object)$data;

        $isChargedRentaNat = false;
        foreach ($data->multirents as $key => $rentNatCharged) {
            $rentNatCharged = (object) $rentNatCharged;
            if ('' != $rentNatCharged->renta_nat_type) {
                $isChargedRentaNat = true;

                break;
            }
        }

        // проверка за въведен тип
        if (!$data->acr_type) {
            throw new MTRpcException('MISSING_CHARGED_RENTA_TYPE', -33217);
        }

        $is_correct = false;
        $have_renta_natura = false;
        // проверка за валидно начисление
        $multiRentCount = count($data->multirents);
        for ($k = 0; $k < $multiRentCount; $k++) {
            $renta_nat = (object)$data->multirents[$k];

            $renta_nat_type = $renta_nat->renta_nat_type;
            $renta_value = $renta_nat->renta_value;
            $price_per_unit = $renta_nat->price_per_unit;
            $is_converted = $renta_nat->is_converted;

            if ((is_null($renta_nat_type) || '0' == $renta_nat_type || '' == $renta_nat_type) && '' == $renta_value) {
                if (!$is_converted) {
                    $is_correct = true;
                } else {
                    if (!is_null($price_per_unit) && '' != $price_per_unit) {
                        $is_correct = true;
                    }
                }
            }
            if ((!is_null($renta_nat_type) || '0' != $renta_nat_type) && '' != $renta_value) {
                if (!$is_converted) {
                    $is_correct = true;
                } else {
                    if (!is_null($price_per_unit) && '' != $price_per_unit && '-' != $renta_value) {
                        $is_correct = true;
                    }
                }
                $have_renta_natura = true;
            }

            if (!$is_correct) {
                throw new MTRpcException('WRONG_CHARGED_RENTA', -33216);
            }

            $is_correct = false;
        }
        if (!$have_renta_natura && '' == $data->renta) {
            throw new MTRpcException('WRONG_CHARGED_RENTA', -33216);
        }

        // Convert the renta value to correct format
        $data->renta = str_replace(',', '.', $data->renta);

        $ntpCodes = [];
        if (!empty($data->area_type)) {
            $ntpCodes = $UserDbAreaTypesController->getNtpCodesWithAdditionalCodes($data->area_type);
        }

        $FarmingController = new FarmingController('Farming');
        $userFarmings = $FarmingController->getUserFarmings();
        $farmingIds = array_keys($userFarmings);
        if (in_array($data->farming, $farmingIds)) {
            $farmingIds = [$data->farming];
        }

        $options = [
            'return' => [
                'c.id as contract_id',
                'o.id as owner_id',
                'max(crn.id) as has_renta_nat',
                'SUM (CASE WHEN cr.owner_id = o.ID THEN cr.renta * ((area_for_rent * po.percent / 100)) ELSE 0 END) as charged_renta',
                'string_agg(distinct(kvs.gid, COALESCE(kvs.ekate, \'\'), COALESCE(kvs.kad_ident, \'\'), COALESCE(kvs.category, \'\'), COALESCE(kvs.mestnost, \'\'), COALESCE(((pc.area_for_rent * po.percent / 100)),0) )::text, \';\') as plots',
            ],
            'where' => [
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'is_annex' => ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
                'c_type' => ['column' => 'nm_usage_rights', 'compare' => 'IN', 'prefix' => 'c', 'value' => [2, 3, 5]],
                'is_heritor' => ['column' => 'is_heritor', 'compare' => '=', 'prefix' => 'po', 'value' => 'FALSE'],
                // filters
                // plot filter
                'ekate' => ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $data->ekate],
                'rent_per_plot' => ['column' => 'rent_per_plot', 'compare' => 'IS', 'prefix' => 'pc', 'value' => 'NULL'],
                'masiv' => ['column' => 'masiv', 'compare' => '=', 'prefix' => 'kvs', 'value' => $data->masiv],
                'number' => ['column' => 'number', 'compare' => '=', 'prefix' => 'kvs', 'value' => $data->number],
                'category' => ['column' => 'category', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $data->category],
                'area_type' => ['column' => 'area_type', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $ntpCodes],
                'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
                // contract filter
                'cnum' => ['column' => 'c_num', 'compare' => '=', 'prefix' => 'c', 'value' => $data->cnum],
                'contract_type' => ['column' => 'nm_usage_rights', 'compare' => '=', 'prefix' => 'c', 'value' => $data->contract_type],
                'farming' => ['column' => 'farming_id', 'compare' => 'IN', 'prefix' => 'c', 'value' => $farmingIds],
                // owner filter
                'owner_egn' => ['column' => 'egn', 'compare' => '=', 'prefix' => 'o', 'value' => $data->owner_egn],
                'rep_ids' => ['column' => 'id', 'compare' => 'IN', 'prefix' => 'o_r', 'value' => $data->rep_ids],
                'rep_egn' => ['column' => 'rep_egn', 'compare' => '=', 'prefix' => 'o_r', 'value' => $data->rep_egn],
                'company_ids' => ['column' => 'id', 'compare' => 'IN', 'prefix' => 'o', 'value' => $data->company_ids],
                'company_eik' => ['column' => 'eik', 'compare' => '=', 'prefix' => 'o', 'value' => $data->company_eik],
                'is_closed_for_editing' => ['column' => 'is_closed_for_editing', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
            ],
            'whereOr' => [
                'area_for_rent_null' => ['column' => 'area_for_rent', 'compare' => 'IS NOT', 'prefix' => 'pc', 'value' => 'NULL'],
                'area_for_rent_0' => ['column' => 'area_for_rent', 'compare' => '!=', 'prefix' => 'pc', 'value' => '0'],
            ],
            // this parameter will be used for joining charged renta table
            'charged_year' => $data->year,
            'start_date' => $GLOBALS['Farming']['years'][$data->year]['year'] . '-09-30',
            'due_date' => ($GLOBALS['Farming']['years'][$data->year]['year'] - 1) . '-10-01',
            'group' => 'c.id, o.id', // , a.id
        ];

        if (!empty($data->contract_group)) {
            $options['contract_group'] = $data->contract_group;
        }

        if (!empty($arrayHelper->filterEmptyStringArr($data->mestnost))) {
            $options['where']['mestnost'] = ['column' => 'mestnost', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $data->mestnost];
        }

        if (!is_null($data->contract_natura)) {
            if (0 == $data->contract_natura) {
                $options['without_contract_natura'] = true;
            } else {
                $options['where']['contract_natura'] = [
                    'column' => 'c_r.renta_id',
                    'compare' => '=',
                    'value' => $data->contract_natura,
                ];
            }
        }

        if (false == $data->overall_renta) {
            // will include only contracts without overall_renta
            $options['where']['overall_renta'] = [
                'column' => 'overall_renta',
                'prefix' => 'c',
                'compare' => 'is',
                'value' => 'NULL',
            ];
        }

        if ('' !== $data->owner_type) {
            if (0 == $data->owner_type) {
                $options['where']['owner_type'] = [
                    'column' => 'owner_type',
                    'prefix' => 'o',
                    'compare' => '=',
                    'value' => 0,
                ];
            } else {
                $options['where']['owner_type'] = [
                    'column' => 'owner_type',
                    'prefix' => 'o',
                    'compare' => '=',
                    'value' => 1,
                ];
            }
        } else {
            $data->owner_type = null;
        }

        $ownersIds = [];
        $leftOwnersIds = [];
        if ($data->owner_ids) {
            $ownersIds = $data->owner_ids;
            $ownersIdCount = count($ownersIds);
            for ($i = 0; $i < $ownersIdCount; $i++) {
                $optionsHeritor = [
                    'return' => ['o.id'],
                    'where' => [
                        'owner_id' => ['prefix' => 'h', 'column' => 'owner_id', 'compare' => '=', 'value' => $ownersIds[$i]],
                    ],
                ];

                $owner = $UserDbOwnersController->getOwnersHeritors($optionsHeritor, false, false);

                if ($owner[0]) {
                    unset($data->owner_ids[$i]);
                }
            }

            $options['where']['owner_ids'] = [
                'column' => 'id',
                'prefix' => 'o',
                'compare' => 'IN',
                'value' => $data->owner_ids,
            ];

            $leftOwnersIds = array_diff($ownersIds, $data->owner_ids);
        }

        $contracts = $UserDbPaymentsController->getPaymentsForContracts($options, false, false);

        $contractsCount = count($contracts);
        // disable the refresh rentas materialised view function so the process is faster
        $UserDbController->disableRentaMatViewTriggers();

        $UsersController->groupLog(
            $this->User->Name,
            $this->User->UserID,
            $this->User->GroupID,
            $this->module,
            $this->service_id,
            __METHOD__,
            ['new_values' => $contracts],
            ['created_id' => 0],
            'Adding records to su_charged_renta'
        );

        $chargedRentaArr = [];
        $infoChargedRenta = [];
        $infoChargedRentaWithPrevRenta = [];

        for ($j = 0; $j < $contractsCount; $j++) {
            $contract = &$contracts[$j];
            $plotsArr = explode(';', $contract['plots']);
            // the ids of the plots in the contract
            $plotIdsCount = count($plotsArr);
            $multiRentsCount = count($data->multirents);
            $isOwnerForContractPlot = false;

            for ($i = 0; $i < $plotIdsCount; $i++) {
                list($plotId, $ekatte, $kadIdent, $category, $mestnost, $ownerArea) = explode(',', trim($plotsArr[$i], '()'));

                $ownerArea -= $this->getPersonalUseForChargedRents($contract['owner_id'], $contract['contract_id'], $plotId, $data->year);

                if ($leftOwnersIds && !in_array($contract['owner_id'], $leftOwnersIds)) {
                    $leftOwnerCount = count($leftOwnersIds);
                    for ($k = 0; $k < $leftOwnerCount; $k++) {
                        $owner_id = $leftOwnersIds[$k];

                        $optionsOwner = [
                            'return' => ['o.id'],
                            'where' => [
                                'owner_id' => ['prefix' => 'po', 'column' => 'owner_id', 'compare' => '=', 'value' => $owner_id],
                                'contract_id' => ['prefix' => 'pc', 'column' => 'contract_id', 'compare' => '=', 'value' => $contract['contract_id']],
                                'plot_id' => ['prefix' => 'pc', 'column' => 'plot_id', 'compare' => '=', 'value' => $plotId],
                            ],
                        ];

                        $haveOwner = $UserDbOwnersController->getOwnersDataByContract($optionsOwner, false, false);
                        $isOwner = $haveOwner[0];

                        $optionsHeritor = [
                            'return' => ['o.id'],
                            'where' => [
                                'path' => ['prefix' => 'h', 'column' => 'path', 'compare' => '~', 'value' => '*.' . $contract['owner_id'] . '.*.' . $owner_id . '.*'],
                            ],
                        ];

                        $owner = $UserDbOwnersController->getOwnersHeritors($optionsHeritor, false, false);

                        if (!$isOwner && $owner[0]) {
                            $isOwnerForContractPlot = false;

                            break;
                        }

                        $isOwnerForContractPlot = true;
                    }
                }

                if ($isOwnerForContractPlot) {
                    continue;
                }

                $options = [
                    'tablename' => $UserDbController->DbHandler->tableChargedRenta,
                    'return' => ['*'],
                    'where' => [
                        'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $contract['contract_id']],
                        'plot_id' => ['column' => 'plot_id', 'compare' => '=', 'value' => $plotId],
                        'year' => ['column' => 'year', 'compare' => '=', 'value' => $data->year],
                        'owner_id' => ['column' => 'owner_id', 'compare' => '=', 'value' => $contract['owner_id']],
                    ],
                ];

                $chargedRenta = $UserDbController->getItemsByParams($options, false, false);

                $chargedRentaNatArr = [];
                foreach ($data->multirents as $rentNat) {
                    if (empty($rentNat['renta_nat_type'])) {
                        continue;
                    }

                    if ($UserDbPaymentsController->hasContractRentNat($contract['contract_id'], $rentNat['renta_nat_type'])) {
                        $chargedRentaNatArr[] = $rentNat;
                    }
                }

                $chargedRentaNatTxt = [];
                if (!empty($chargedRentaNatArr)) {
                    $chargedRentaNatTxt = array_map(
                        function ($renta) use ($ownerArea) {
                            $rentaValue = $renta['is_converted'] ? 0 : $renta['renta_value'];
                            if ('' == $rentaValue || 0 == $rentaValue) {
                                return $renta['renta_nat_type'] . '-без количество';
                            }

                            return $renta['renta_nat_type'] . '-' . round($rentaValue * $ownerArea, 3);
                        },
                        $chargedRentaNatArr ?? []
                    );
                }

                $infoRenta = [
                    'ekate' => $ekatte,
                    'kad_ident' => $kadIdent,
                    'category' => $category,
                    'mestnost' => $mestnost,
                    'contract_id' => $contract['contract_id'],
                    'owner_id' => $contract['owner_id'],
                    'owner_area' => $ownerArea,
                    'should_recalculate_renta_nat' => true,
                    'charged_renta_nat' => count($chargedRentaNatArr) > 0 ? implode(',', $chargedRentaNatTxt) : null,
                    'charged_renta' => '-' === $data->renta ? '' : $ownerArea * $data->renta,
                    'charged_renta_txt' => '-' === $data->renta ? '' : BGNtoEURO($ownerArea * $data->renta),
                    'renta_nats' => $data->multirents,
                    'has_prev_charged_renta' => $contract['charged_renta'] > 0,
                    'plot_id' => $plotId,
                    'year' => $data->year,
                ];

                // used for filling su_charged_renta_history
                $chargedRentaArr[] = $infoRenta;

                if ($prev) {
                    if ($infoRenta['has_prev_charged_renta']) {
                        $infoChargedRentaWithPrevRenta[] = $infoRenta;
                    } else {
                        $infoChargedRenta[] = $infoRenta;
                    }

                    continue;
                }

                $foundChargedRents = count($chargedRenta);
                // If a renta is charged for the first time add record in the database else only edit it.
                if (0 == $foundChargedRents) {
                    $options = [
                        'tablename' => $UserDbController->DbHandler->tableChargedRenta,
                        'mainData' => [
                            'contract_id' => $contract['contract_id'],
                            'plot_id' => $plotId,
                            'owner_id' => $contract['owner_id'],
                            'year' => $data->year,
                            'renta_nat' => $data->renta_nat,
                            'nat_is_converted' => ($data->nat_is_converted) ? true : false,
                            'nat_unit_price' => ($data->nat_is_converted) ? $data->nat_unit_price : 0,
                        ],
                    ];

                    if (is_numeric($data->renta) && $data->renta >= 0) {
                        $options['mainData']['renta'] = $data->renta;
                    }
                    if ('-' == $data->renta) {
                        $options['mainData']['renta'] = null;
                    }

                    $recordID = $UserDbController->addItem($options);
                } else {
                    $options = [
                        'tablename' => $UserDbController->DbHandler->tableChargedRenta,
                        'mainData' => [
                            'renta_nat' => $data->renta_nat,
                            'nat_is_converted' => ($data->nat_is_converted) ? true : false,
                            'nat_unit_price' => ($data->nat_is_converted) ? $data->nat_unit_price : 0,
                        ],
                        'where' => [
                            'contract_id' => $contract['contract_id'],
                            'plot_id' => $plotId,
                            'year' => $data->year,
                            'owner_id' => $contract['owner_id'],
                        ],
                        'returning' => ['id'],
                    ];

                    if (is_numeric($data->renta) && $data->renta >= 0) {
                        $options['mainData']['renta'] = $data->renta;
                    }
                    if ('-' == $data->renta) {
                        $options['mainData']['renta'] = null;
                    }

                    $recordID = $UserDbController->editItem($options);
                }

                if ($multiRentsCount) {
                    for ($k = 0; $k < $multiRentsCount; $k++) {
                        $renta = (object)$data->multirents[$k];

                        // Convert the renta value to correct format
                        $rentaValue = str_replace(',', '.', $renta->renta_value);

                        if ('' == $renta->renta_nat_type || '0' == $renta->renta_nat_type) {
                            continue;
                        }

                        if ('-' == $renta->renta_value) {
                            $rentaValue = null;
                        }

                        $options = [
                            'mainData' => [
                                'amount' => $rentaValue,
                                'nat_type' => $renta->renta_nat_type ? (int)$renta->renta_nat_type : null,
                                'nat_is_converted' => ($renta->is_converted) ? true : false,
                                'nat_unit_price' => ($renta->is_converted) ? $renta->price_per_unit : 0,
                            ],
                            'where' => [
                                'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $contract['contract_id']],
                                'year' => ['column' => 'year', 'compare' => '=', 'value' => $data->year],
                                'plot_id' => ['column' => 'plot_id', 'compare' => '=', 'value' => $plotId],
                                'owner_id' => ['column' => 'owner_id', 'compare' => '=', 'value' => $contract['owner_id']],
                            ],
                        ];
                        $UserDbPaymentsController->editOrAddChargeRentaNatura($options);
                    }
                }
            }
        }

        if ($prev) {
            return $UserDbPaymentsController->prepareChargedRentaDataGrid(array_merge($infoChargedRentaWithPrevRenta, $infoChargedRenta));
        }

        // save data to History
        $categories = implode(',', $data->category);
        $mestnost = implode(',', $data->mestnost);
        $areaТype = implode(',', $data->area_type);
        $ownerIds = implode(',', $ownersIds);
        $repIds = implode(',', $data->rep_ids);
        $companyIds = implode(',', $data->company_ids);
        $now = date('Y-m-d');
        $options
            = ['tablename' => $UserDbController->DbHandler->chargedRentaParams,
                'mainData' => [
                    'type' => $data->acr_type,
                    'date' => $now,
                    'farming_year' => (int)$data->year,
                    'owner_id' => $ownerIds,
                    'owner_egn' => $data->owner_egn,
                    'rep_id' => $repIds,
                    'rep_egn' => $data->rep_egn,
                    'company_id' => $companyIds,
                    'company_eik' => $data->company_eik,
                    'c_num' => $data->cnum,
                    'c_type' => (int)$data->contract_type,
                    'farming_id' => (int)$data->farming,
                    'ekate' => $data->ekate,
                    'mestnost' => $mestnost,
                    'masiv' => $data->masiv,
                    'number' => $data->number,
                    'natura_type' => (int)$data->contract_natura,
                    'natura' => $data->natura,
                    'category' => $categories,
                    'area_type' => $areaТype,
                    'renta' => $data->renta,
                    'with_overall_renta' => $data->overall_renta,
                    'owner_type' => $data->owner_type,
                ],
                'returning' => ['id'],
            ];

        if (!empty($data->contract_group)) {
            $options['mainData']['c_group'] = (int)$data->contract_group;
        }

        $id = $UserDbController->addItem($options);

        $UsersController->groupLog(
            $this->User->Name,
            $this->User->UserID,
            $this->User->GroupID,
            $this->module,
            $this->service_id,
            __METHOD__,
            ['new_values' => $options],
            ['created_id' => $id],
            'Adding record to su_charged_renta_params'
        );
        $multiRentCount = count($data->multirents);
        for ($k = 0; $k < $multiRentCount; $k++) {
            $renta = (object)$data->multirents[$k];

            if ('' == $renta->renta_nat_type || '0' == $renta->renta_nat_type) {
                continue;
            }

            $options = [
                'tablename' => $UserDbController->DbHandler->chargedRentaNaturaParams,
                'mainData' => [
                    'params_id' => $id,
                    'amount' => $renta->renta_value,
                    'nat_type' => (int)$renta->renta_nat_type,
                    'is_converted' => $renta->is_converted ? 'true' : 'false',
                    'price_per_unit' => 'true' == $renta->is_converted ? $renta->price_per_unit : null,
                ],
            ];
            $recordID = $UserDbController->addItem($options);
        }
        // enable back the refresh rentas materialised view function
        $UserDbController->enableRentaMatViewTriggers();
        // add affected plots to History
        $this->affectedPlots($chargedRentaArr, $id);
        $UserDbController->refreshRentaViews();

        return true;
    }

    /**
     * Export to Excel charged renta preview.
     *
     * @api-method prevChargedRentaExport
     *
     * @param array $rpcParams - Charged renta params
     *
     * @throws MTRpcException
     *
     * @return array - Path to excel file
     */
    public function prevChargedRentaExport($rpcParams)
    {
        $fileName = 'export_changed_renta_preview_' . date('d_m_Y-H_i_s') . '.xlsx';

        $path = PUBLIC_UPLOAD_HISTORY . '/' . $this->User->GroupID . '/' . $fileName;

        if (!file_exists(PUBLIC_UPLOAD_HISTORY . '/' . $this->User->GroupID)) {
            mkdir(PUBLIC_UPLOAD_HISTORY . '/' . $this->User->GroupID, 0777);
        }

        $results = $this->prevChargedRenta($rpcParams);
        $result = $results['rows'];

        unset($result[0]['renta_nats'], $result[0]['has_prev_charged_renta']);

        $column_headers = [
            'ekate' => 'Землище',
            'kad_ident' => 'Име на парцел',
            'category' => 'Категория',
            'contract_id' => 'Договор',
            'owner_id' => 'Собственик',
            'owner_area' => 'Използвана площ (дка)',
            'charged_renta_txt' => 'Начислена сума',
            'charged_renta_nat' => 'Начислено количество',
        ];

        $export2Xls = new ExportToExcelClass();
        $export2Xls->export($result, $column_headers, []);
        $export2Xls->saveFile($path);

        $filePath = PUBLIC_UPLOAD_HISTORY_RELATIVE_PATH . '/' . $this->User->GroupID . '/' . $fileName;

        return ['file_path' => $filePath, 'file_name' => $fileName];
    }

    private function affectedPlots($chargedRentaArr, $id)
    {
        $UserDbController = new UserDbController($this->User->Database);

        $chargedRentCount = count($chargedRentaArr);
        for ($i = 0; $i < $chargedRentCount; $i++) {
            $chargedRenta = $chargedRentaArr[$i];

            $options = [
                'tablename' => $UserDbController->DbHandler->chargedRentaHistory,
                'mainData' => [
                    'params_id' => $id,
                    'contract_id' => $chargedRenta['contract_id'],
                    'owner_id' => $chargedRenta['owner_id'],
                    'ekate' => $chargedRenta['ekate'],
                    'kad_ident' => $chargedRenta['kad_ident'],
                    'category' => $chargedRenta['category'],
                    'mestnost' => $chargedRenta['mestnost'],
                    'owner_area' => $chargedRenta['owner_area'],
                    'charged_renta' => $chargedRenta['charged_renta'],
                    'charged_renta_nat' => $chargedRenta['charged_renta_nat'],
                ],
            ];
            $UserDbController->addItem($options);
        }
    }

    private function getPlotById($plot_id, $contract_id)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $options = [
            'tablename' => $UserDbController->DbHandler->tableKVS . ' kvs',
            'return' => ['kvs.kad_ident', 'kvs.ekate', 'kvs.category', 'kvs.masiv', 'kvs.number', 'scpr.rent_per_plot'],
            'where' => [
                'gid' => ['column' => 'gid', 'compare' => '=', 'value' => $plot_id],
            ],
            'joins' => [
                'left join su_contracts_plots_rel scpr on scpr.plot_id = ' . $plot_id . ' and scpr.contract_id = ' . $contract_id,
            ],
        ];
        $result = $UserDbController->getItemsByParams($options, false, false);

        return $result[0];
    }

    private function getPersonalUseForChargedRents($ownerId, $contractId, $plotId, $year)
    {
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);

        $ownerIds = [$ownerId];

        $optionsHeritor = [
            'return' => ['o.id'],
            'where' => [
                'path' => ['prefix' => 'h', 'column' => 'path', 'compare' => '~', 'value' => '*.' . $ownerId . '.*'],
            ],
        ];
        $ownerHeritorsIds = $UserDbOwnersController->getOwnersHeritors($optionsHeritor, false, false);

        if (!empty($ownerHeritorsIds)) {
            $ownerIds = array_merge($ownerIds, array_column($ownerHeritorsIds, 'id'));
        }

        $options = [
            'tablename' => 'su_personal_use spu',
            'return' => ['sum(spur.area)'],
            'where' => [
                'contract_id' => ['prefix' => 'sc', 'column' => 'id', 'compare' => '=', 'value' => $contractId],
                'plot_id' => ['prefix' => 'scpr', 'column' => 'plot_id', 'compare' => '=', 'value' => $plotId],
                'owner_id' => ['prefix' => 'spu', 'column' => 'owner_id', 'compare' => 'IN', 'value' => $ownerIds],
                'year' => ['prefix' => 'spu', 'column' => 'year', 'compare' => '=', 'value' => $year],
            ],
            'joins' => [
                'left join su_personal_use_rents spur on spur.pu_id = spu.id',
                'left join su_contracts_plots_rel scpr on scpr.id = spu.pc_rel_id',
                'left join su_contracts sc on sc.id = scpr.contract_id ',
            ],
        ];

        $puAreaResult = $UserDbOwnersController->getItemsByParams($options, false, false);

        if (!empty($puAreaResult)) {
            return $puAreaResult[0]['sum'];
        }

        return 0;
    }

    private function getAffectedPlotInfo($contractId, $ownerId, $year, $plotId, $ownerArea = null)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);
        $resultPayment = [];
        $charged_rentas_arr = [];

        if (null === $ownerArea) {
            $options = [
                'return' => [
                    'SUM (CASE WHEN cr.owner_id = o.ID THEN cr.renta ELSE 0 END) as renta',
                    'COALESCE(((pc.area_for_rent * po.percent / 100)),0) as owner_area',
                ],
                'where' => [
                    'plot_id' => ['column' => 'plot_id', 'compare' => '=', 'prefix' => 'pc', 'value' => $plotId],
                    'owner_id' => ['column' => 'owner_id', 'compare' => '=', 'prefix' => 'po', 'value' => $ownerId],
                ],
                'chosen_year' => $year,
                'contract_id_string' => $contractId,
                'start_date' => $GLOBALS['Farming']['years'][$year]['year'] . '-09-30',
                'due_date' => ($GLOBALS['Farming']['years'][$year]['year'] - 1) . '-10-01',
                'group' => 'pc.area_for_rent, po.percent, pu.area',
            ];

            $resultsPayments = $UserDbPaymentsController->getPaymentsForOwners($options, false, false);
            $resultPayment = $resultsPayments[0];

            $resultPayment['owner_area'] -= $this->getPersonalUseForChargedRents($ownerId, $contractId, $plotId, $year);
            $resultPayment['charged_renta'] = $resultPayment['renta'] * $resultPayment['owner_area'];
        }

        $options = [
            'tablename' => $UserDbController->DbHandler->tableNaturaChargedRenta,
            'return' => [
                '((CASE WHEN crn.nat_is_converted = TRUE THEN 0 ELSE crn.amount END) * ' . ($ownerArea ?? $resultPayment['owner_area']) . ') as charged_renta_nat',
                'crn.nat_type',
            ],
            'where' => [
                // personal use data
                'owner_id' => ['prefix' => 'po', 'column' => 'owner_id', 'compare' => '=', 'value' => $ownerId],
                'plot_id' => ['prefix' => 'cr', 'column' => 'plot_id', 'compare' => '=', 'value' => $plotId],
                'contract_id' => ['prefix' => 'cp', 'column' => 'contract_id', 'compare' => '=', 'value' => $contractId],
                'year' => ['prefix' => 'cr', 'column' => 'year', 'compare' => '=', 'value' => $year],
                'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'value' => 'added'],
            ],
            'group' => 'crn.nat_type,crn.nat_is_converted,crn.amount',
            'order' => 'ASC',
            'sort' => 'crn.nat_type',
            'join_by_owner_id' => 'AND po.owner_id = cr.owner_id',
        ];

        $charged_rentas = $UserDbPaymentsController->getNaturaCalculatedAmountByContracts($options, false, false);

        $charged_rentas_arr = array_map(function ($renta) {
            return $renta['nat_type'] . '-' . $renta['charged_renta_nat'];
        }, $charged_rentas);

        return [
            'resultPayment' => $resultPayment,
            'chargedRentasArr' => $charged_rentas_arr,
        ];
    }
}
