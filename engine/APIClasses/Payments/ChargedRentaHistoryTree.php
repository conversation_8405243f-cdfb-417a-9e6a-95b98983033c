<?php

namespace TF\Engine\APIClasses\Payments;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbAreaTypes\UserDbAreaTypesController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\UserDbPlotCategoriesType\UserDbPlotCategoriesTypeController;
use TF\Engine\Plugins\Core\Users\UsersController;

include_once __DIR__ . '/../../Plugins/Core/Farming/conf.php';
include_once __DIR__ . '/../../Plugins/Core/Layers/conf/index.php';

/**
 * ChargedRenta History Tree.
 *
 * @rpc-module Payments
 *
 * @rpc-service-id charged-renta-history-tree
 */
class ChargedRentaHistoryTree extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readChargedRentaHistory'],
                'validators' => [
                    'filter' => 'validateArray',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
        ];
    }

    /**
     * Read charged renta history tree.
     *
     * @api-method read
     *
     * @param int $page -The current page number
     * @param int $rows -Rows per page
     * @param string $sort -A grid column by which the grid is sorted
     * @param string $order -The sort order ASC/DESC
     * @param ?array $filter
     *
     * @return array
     */
    public function readChargedRentaHistory(?array $filter, ?int $page = null, ?int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $FarmingController = new FarmingController('Farming');
        $UsersController = new UsersController('Users');
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UserDbAreaTypesController = new UserDbAreaTypesController($this->User->Database);
        $UserDbPlotCategoriesTypeController = new UserDbPlotCategoriesTypeController($this->User->Database);

        // get all group farmings and create array like predefined config
        $renta_types = [];
        $renta_units = [];

        $userFarmings = $FarmingController->getUserFarmings();
        $farmingIds = array_keys($userFarmings);

        if (isset($filter['farming']) && in_array((int) $filter['farming'], $farmingIds)) {
            $farmingIds = [(int) $filter['farming']];
        }

        $options = [
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
        ];

        $renta_results = $UserDbController->getItemsByParams($options, false, false);
        $rentCount = count($renta_results);
        // create renta types array
        for ($i = 0; $i < $rentCount; $i++) {
            $renta_types[$renta_results[$i]['id']] = $renta_results[$i]['name'];
            $renta_units[$renta_results[$i]['id']] = $GLOBALS['Contracts']['renta_units'][$renta_results[$i]['unit']]['name'];
        }

        // prepare options for contract query
        $options = [
            'return' => [
                'crp.*',
                "string_agg(distinct concat(crnp.nat_type, 'x', crnp.amount, 'x ', crnp.price_per_unit), ',') as renta_type_amount",
                'scg.name as c_group_name',
            ],
            'where' => [
                'farming_year' => ['column' => 'farming_year', 'compare' => '=', 'value' => $filter['year']],
                'c_farming_id' => ['column' => 'farming_id', 'prefix' => 'c', 'compare' => 'IN', 'value' => [...$farmingIds, null]],
                'scrp_farming_id' => ['column' => 'farming_id', 'prefix' => 'scrp', 'compare' => 'IN', 'value' => [...$farmingIds, 0]],
                // The su_contracts table is left joined, so we need to add null to farming_id filter in case the contract is null
            ],
            'group' => 'scrp.id,
            scrp.type,
            scrp.date,
            scrp.farming_year,
            scrp.owner_id,
            scrp.owner_egn,
            scrp.rep_id,
            scrp.rep_egn,
            scrp.company_id,
            scrp.company_eik,
            scrp.c_num,
            scrp.c_type,
            scrp.c_group,
            scrp.farming_id,
            scrp.ekate,
            scrp.masiv,
            scrp.number,
            scrp.natura_type,
            scrp.category,
            scrp.area_type,
            scrp.renta,
            scrp.natura,
            scrp.with_overall_renta,
            scrp.owner_type,
            scrp.mestnost,
            scg.name',
            'sort' => $sort,
            'order' => $order,
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
        ];
        // The query is too heavy. Commented until the query is made lighter
        if ($filter['acr_type']) {
            $options['where']['type'] = ['column' => 'type', 'compare' => 'ILIKE', 'value' => '%' . trim($filter['acr_type']) . '%'];
        }

        if ($filter['cnum']) {
            $options['where']['c_num'] = ['column' => 'c_num', 'prefix' => 'c', 'compare' => 'ILIKE', 'value' => '%' . trim($filter['cnum']) . '%'];
        }

        if ($filter['contract_type']) {
            $options['where']['nm_usage_rights'] = ['column' => 'nm_usage_rights', 'prefix' => 'c', 'compare' => '=', 'value' => $filter['contract_type']];
        }

        if ($filter['contract_group']) {
            $options['where']['group'] = ['column' => 'group', 'prefix' => 'c', 'compare' => '=', 'value' => $filter['contract_group']];
        }

        if ($filter['ekate']) {
            $options['where']['ekate'] = ['column' => 'ekate', 'prefix' => 'kvs', 'compare' => '=', 'value' => $filter['ekate']];
        }

        if (strlen($filter['masiv'])) {
            $options['where']['masiv'] = ['column' => 'masiv', 'prefix' => 'kvs', 'compare' => '=', 'value' => $filter['masiv']];
        }

        if ($filter['number']) {
            $options['where']['number'] = ['column' => 'number', 'prefix' => 'kvs', 'compare' => '=', 'value' => $filter['number']];
        }

        // if (strlen($filter['owner_type'])) {
        //     $options['where']['owner_type'] = array('column' => 'owner_type', 'prefix' => 'o', 'compare' => '=', 'value' => $filter['owner_type']);
        // }

        if ($filter['owner_egn']) {
            $options['where']['owner_egn'] = ['column' => 'egn', 'prefix' => 'so', 'compare' => 'ILIKE', 'value' => '%' . $filter['owner_egn'] . '%'];
        }

        if (!empty($filter['owner_ids'])) {
            $options['where']['owner_id'] = ['column' => 'id', 'prefix' => 'so', 'compare' => 'IN', 'value' => $UsersController->ArrayHelper->filterEmptyStringArr($filter['owner_ids'])];
        }

        if (!empty($filter['rep_ids'])) {
            $options['where']['rep_id'] = ['column' => 'rep_id', 'prefix' => 'scrp', 'compare' => 'IN', 'value' => $UsersController->ArrayHelper->filterEmptyStringArr($filter['rep_ids'])];
        }

        if ($filter['rep_egn']) {
            $options['where']['rep_egn'] = ['column' => "(sor.rep_egn ilike '%" . $filter['rep_egn'] . "%' or scrp.rep_egn ilike '%" . $filter['rep_egn'] . "%')", 'compare' => '=', 'value' => 'true'];
        }

        if ($filter['company_ids']) {
            $options['where']['company_id'] = ['column' => 'id', 'prefix' => 'so_c', 'compare' => 'IN', 'value' => $UsersController->ArrayHelper->filterEmptyStringArr($filter['company_ids'])];
        }

        if ($filter['company_eik']) {
            $options['where']['company_eik'] = ['column' => 'eik', 'prefix' => 'so_c', 'compare' => 'ILIKE', 'value' => '%' . $filter['company_eik'] . '%'];
        }

        if ($filter['category']) {
            $options['where']['category'] = ['column' => 'category', 'prefix' => 'kvs', 'compare' => 'IN', 'value' => $UsersController->ArrayHelper->filterEmptyStringArr($filter['category'])];
        }

        if ($filter['area_type']) {
            $options['where']['area_type'] = ['column' => 'area_type', 'prefix' => 'kvs', 'compare' => 'IN', 'value' => $UsersController->ArrayHelper->filterEmptyStringArr($filter['area_type'])];
        }

        if ($filter['mestnost']) {
            $options['where']['mestnost'] = ['column' => 'mestnost', 'prefix' => 'kvs', 'compare' => 'IN', 'value' => $UsersController->ArrayHelper->filterEmptyStringArr($filter['mestnost'])];
        }

        // get all charged renta params for pagination total
        $counter = $UserDbController->getChargedRenta($options, true, false);
        $resultsCount = (int) $counter[0]['count'];
        if (0 == $resultsCount) {
            return [];
        }

        $results = $UserDbController->getChargedRenta($options, false, false);

        // clear old data
        $return = [];

        // transform results into tree format
        for ($i = 0; $i < $resultsCount; $i++) {
            $chargedRentaParam = $results[$i];
            $dataNatura = [];
            $dataIdsAndNames = [];

            if (!$chargedRentaParam['type']) {
                continue;
            }

            $chargedRentaParam['date'] = strftime('%d.%m.%Y', strtotime($chargedRentaParam['date']));
            $text = $chargedRentaParam['type'] . ' (' . $chargedRentaParam['date'] . ')';
            $chargedRentaParam['farming_year'] = $GLOBALS['Farming']['years'][$chargedRentaParam['farming_year']]['farming_year'];

            if (null === $chargedRentaParam['renta']) {
                $chargedRentaParam['renta'] = '-';
            }

            if (!$chargedRentaParam['owner_id'] || '' == $chargedRentaParam['owner_id']) {
                $chargedRentaParam['owner_id'] = '-';
            } else {
                $ownerIdsArr = explode(',', $chargedRentaParam['owner_id']);

                $options = [
                    'return' => [
                        'o.id as unique_id',
                        'o."name"',
                        'o.surname',
                        'o.lastname',
                        'o.egn',
                        'o.lk_nomer',
                        'o.lk_izdavane',
                        'o.company_name',
                        'o.company_name',
                        'o.eik',
                        'o.phone',
                        'o.fax',
                        'o.mobile',
                        'o.email',
                        'o.address',
                        'o.owner_type',
                        'o.mol',
                        'o.company_address',
                        'o.is_dead',
                        'o.iban',
                        'o.rent_place',
                        'o.is_foreigner',
                        'o.remark',
                        'o.prepiska',
                        'o.bic',
                        'o.bank_name',
                        'o.dead_date',
                        'o.post_payment_fields',
                    ],
                    'where' => [
                        'owner_id' => ['column' => 'id', 'prefix' => 'o', 'compare' => 'IN', 'value' => $ownerIdsArr],
                    ],
                    'group' => 'o.id',
                ];
                $resultsOwners = $UserDbOwnersController->getOwnersData($options, false, false);

                $chargedRentaParam['owner_id'] = array_map(function ($resultOwner) {
                    return $resultOwner['name'] . ' ' . $resultOwner['surname'] . ' ' . $resultOwner['lastname'];
                }, $resultsOwners);
                $countOwnersIds = count($ownerIdsArr);
                $countResultsOwners = count($resultsOwners);
                $countDiferrenceOwner = $countOwnersIds - $countResultsOwners;

                if (0 != $countDiferrenceOwner) {
                    for ($m = 0; $m < $countDiferrenceOwner; $m++) {
                        $chargedRentaParam['owner_id'][] = 'Несъществуващ собственик';
                    }
                }

                $dataIdsAndNames['owners'] = array_combine($ownerIdsArr, $chargedRentaParam['owner_id']);
                $chargedRentaParam['owner_id'] = implode(', ', $chargedRentaParam['owner_id']);
            }

            if (!$chargedRentaParam['owner_egn']) {
                $chargedRentaParam['owner_egn'] = '-';
            }

            if (!$chargedRentaParam['rep_id'] || '' == $chargedRentaParam['rep_id']) {
                $chargedRentaParam['rep_id'] = '-';
            } else {
                $repIdsArr = explode(',', $chargedRentaParam['rep_id']);
                $options = [
                    'tablename' => $UserDbController->DbHandler->tableOwnersReps,
                    'return' => [
                        'rep_name as name',
                        'rep_surname as surname',
                        'rep_lastname as lastname',
                    ],
                    'where' => [
                        'rep_id' => ['column' => 'id', 'compare' => 'IN', 'value' => $repIdsArr],
                    ],
                ];
                $resultsReps = $UserDbController->getItemsByParams($options, false, false);

                $chargedRentaParam['rep_id'] = array_map(function ($resultRep) {
                    return $resultRep['name'] . ' ' . $resultRep['surname'] . ' ' . $resultRep['lastname'];
                }, $resultsReps);

                $countRepsIds = count($repIdsArr);
                $countResultsReps = count($resultsReps);
                $countDiferrenceReps = $countRepsIds - $countResultsReps;

                if (0 != $countDiferrenceReps) {
                    for ($k = 0; $k < $countDiferrenceReps; $k++) {
                        $chargedRentaParam['rep_id'][] = 'Несъществуващ представител';
                    }
                }

                $dataIdsAndNames['reps'] = array_combine($repIdsArr, $chargedRentaParam['rep_id']);
                $chargedRentaParam['rep_id'] = implode(', ', $chargedRentaParam['rep_id']);
            }

            if (!$chargedRentaParam['rep_egn']) {
                $chargedRentaParam['rep_egn'] = '-';
            }

            if (!$chargedRentaParam['company_id'] || '' == $chargedRentaParam['company_id']) {
                $chargedRentaParam['company_id'] = '-';
            } else {
                $companyIdsArr = explode(',', $chargedRentaParam['company_id']);
                $options = [
                    'return' => ['o.*'],
                    'where' => [
                        'company_id' => ['column' => 'id', 'prefix' => 'o', 'compare' => 'IN', 'value' => $companyIdsArr],
                    ],
                    'group' => 'o.id',
                ];
                $resultsCompanies = $UserDbOwnersController->getOwnersData($options, false, false);

                $chargedRentaParam['company_id'] = array_map(function ($resultCompany) {
                    return $resultCompany['company_name'];
                }, $resultsCompanies);

                $countCompaniesIds = count($companyIdsArr);
                $countResultsComapnies = count($resultsCompanies);
                $countDiferrenceCompanies = $countCompaniesIds - $countResultsComapnies;

                if (0 != $countDiferrenceCompanies) {
                    for ($l = 0; $l < $countDiferrenceCompanies; $l++) {
                        $chargedRentaParam['company_id'][] = 'Несъществуваща фирма';
                    }
                }

                $dataIdsAndNames['companies'] = array_combine($companyIdsArr, $chargedRentaParam['company_id']);
                $chargedRentaParam['company_id'] = implode(', ', $chargedRentaParam['company_id']);
            }

            if (!$chargedRentaParam['company_eik']) {
                $chargedRentaParam['company_eik'] = '-';
            }

            if (null == $chargedRentaParam['c_num'] || '' == $chargedRentaParam['c_num']) {
                $chargedRentaParam['c_num'] = '-';
            }

            if (!$chargedRentaParam['c_type']) {
                $chargedRentaParam['c_type'] = '-';
            } else {
                $chargedRentaParam['c_type'] = $GLOBALS['Contracts']['ContractTypes'][$chargedRentaParam['c_type']]['name'];
            }

            if (!$chargedRentaParam['farming_id']) {
                $chargedRentaParam['farming_id'] = '-';
            } else {
                $chargedRentaParam['farming_id'] = $userFarmings[$chargedRentaParam['farming_id']];
            }

            if (!$chargedRentaParam['ekate']) {
                $chargedRentaParam['ekate'] = 'Всички';
                $chargedRentaParam['plot'] = '-';
            } else {
                $chargedRentaParam['plot'] = $UsersController->getEkatteName($chargedRentaParam['ekate']);
            }

            if (null == $chargedRentaParam['masiv']) {
                $chargedRentaParam['masiv'] = '-';
            }
            if (null == $chargedRentaParam['number']) {
                $chargedRentaParam['number'] = '-';
            }

            if (null == $chargedRentaParam['category'] || '' == $chargedRentaParam['category']) {
                $chargedRentaParam['category'] = '-';
            } else {
                $chargedRentaParam['category'] = explode(',', $chargedRentaParam['category']);

                $chargedRentaParam['category'] = array_map(function ($category) use ($UserDbPlotCategoriesTypeController) {
                    return $UserDbPlotCategoriesTypeController->getPlotCategoryTitle($category);
                }, $chargedRentaParam['category']);

                $chargedRentaParam['category'] = implode(', ', $chargedRentaParam['category']);
            }
            if (null == $chargedRentaParam['area_type'] || '' === $chargedRentaParam['area_type']) {
                $chargedRentaParam['area_type'] = '-';
            } elseif (0 == $chargedRentaParam['area_type']) {
                $chargedRentaParam['area_type'] = 'Всички';
            } else {
                if (is_numeric($chargedRentaParam['area_type'])) {
                    $chargedRentaParam['area_type'] = $UserDbAreaTypesController->getNtpTitle($chargedRentaParam['area_type']);
                } elseif (false !== strpos($chargedRentaParam['area_type'], ',')) {
                    $chargedRentaParam['area_type'] = explode(',', $chargedRentaParam['area_type']);

                    $chargedRentaParam['area_type'] = array_map(function ($area_type) use ($UserDbAreaTypesController) {
                        return $UserDbAreaTypesController->getNtpTitle($area_type);
                    }, $chargedRentaParam['area_type']);

                    $chargedRentaParam['area_type'] = implode(', ', $chargedRentaParam['area_type']);
                }
            }

            if (null == $chargedRentaParam['renta_type_amount']) {
                $chargedRentaParam['renta_type_amount'] = 'невъведена натура';
            } else {
                $rentaTypeAmount = explode(',', $chargedRentaParam['renta_type_amount']);
                $rentaTypeCount = count($rentaTypeAmount);
                $chargedRentaParam['renta_type_amount'] = [];

                for ($j = 0; $j < $rentaTypeCount; $j++) {
                    $renta = explode('x', $rentaTypeAmount[$j]);
                    $type = $renta[0];
                    $amount = $renta[1];
                    $price = $renta[2];

                    if ('-' == $amount) {
                        $chargedRentaParam['renta_type_amount'][] = $renta_types[$type] . ' X - ';
                        $dataNatura[] = ['type' => $type, 'amount' => $amount];

                        continue;
                    }

                    $priceNatura = null;
                    if (' ' != $price) {
                        $priceNatura = $price;
                        $price = '/ед.ст.' . $price . 'лв.';
                    }

                    $dataNatura[] = ['type' => $type, 'amount' => $amount, 'price' => $priceNatura];
                    $chargedRentaParam['renta_type_amount'][] = $renta_types[$type] . ' - ' . $amount . $renta_units[$type] . $price;
                }

                $chargedRentaParam['renta_type_amount'] = implode(', ', $chargedRentaParam['renta_type_amount']);
            }

            switch ($chargedRentaParam['natura']) {
                case 'all':
                    $chargedRentaParam['natura'] = 'Всички';

                    break;
                case 'with_nat':
                    $chargedRentaParam['natura'] = 'С натура';

                    break;
                case 'without_nat':
                    $chargedRentaParam['natura'] = 'Без натура';

                    break;
                default:
                    $chargedRentaParam['natura'] = '-';

                    break;
            }

            if (!$chargedRentaParam['natura_type'] || '' == $chargedRentaParam['natura_type']) {
                $chargedRentaParam['natura_type'] = '-';
            } else {
                $chargedRentaParam['natura_type'] = $renta_types[$chargedRentaParam['natura_type']];
            }

            if (is_null($chargedRentaParam['owner_type'])) {
                $results[$i]['owner_type'] = '';
                $chargedRentaParam['owner_type'] = '';
            }

            $return[] = [
                'id' => $chargedRentaParam['id'],
                'text' => $text,
                'attributes' => $chargedRentaParam,
                'data' => $results[$i],
                'dataNatura' => $dataNatura,
                'dataIdsNames' => $dataIdsAndNames,
                'iconCls' => 'icon-tree-document',
            ];
        }

        // add attribute to first listed element of three for custom pagination
        // information for total can not be sent otherwise
        $return[0]['attributes']['pagination']['total'] = $resultsCount;
        $return[0]['attributes']['pagination']['limit'] = (int) $rows;

        return $return;
    }
}
