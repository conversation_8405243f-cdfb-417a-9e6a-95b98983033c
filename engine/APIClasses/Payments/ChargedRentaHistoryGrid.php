<?php

namespace TF\Engine\APIClasses\Payments;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\ExportToExcelClass;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * ChargedRenta History Grid.
 *
 * @rpc-module Payments
 *
 * @rpc-service-id charged-renta-history-grid
 */
class ChargedRentaHistoryGrid extends TRpcApiProvider
{
    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readChargedRentaHistory'],
                'validators' => [
                    'paramsId' => 'validateInteger',
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'expChargedRentaHistory' => ['method' => [$this, 'expChargedRentaHistory']],
        ];
    }

    /**
     * Read charged renta history.
     *
     * @api-method read
     *
     * @param string $paramsId -Charged renta history id
     * @param int $page -The current page number
     * @param int $rows -Rows per page
     * @param string $sort -A grid column by which the grid is sorted
     * @param string $order -The sort order ASC/DESC
     *
     * @return array
     *               #item string total count
     *               #item array rows
     */
    public function readChargedRentaHistory(int $paramsId, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UsersController = new UsersController('Users');
        $UserDbController = new UserDbController($this->User->Database);

        $renta_types = [];

        $options = [
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
        ];
        $renta_results = $UserDbController->getItemsByParams($options, false, false);
        // create renta types array

        $rentCount = count($renta_results);
        for ($i = 0; $i < $rentCount; $i++) {
            $renta_types[$renta_results[$i]['id']] = $renta_results[$i]['name'] . '(' . $GLOBALS['Contracts']['renta_units'][$renta_results[$i]['unit']]['name'] . ')';
        }

        $options = [
            'tablename' => $UserDbController->DbHandler->chargedRentaHistory,
            'return' => ['virtual_ekatte_name as ekate', 'kad_ident', 'virtual_category_title as category', 'contract_id', 'owner_id', 'owner_area', 'charged_renta', 'charged_renta_nat', 'mestnost'],
            'where' => [
                'params_id' => ['column' => 'params_id', 'compare' => '=', 'value' => $paramsId],
            ],
            'sort' => $sort,
            'order' => $order,
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
        ];

        $counter = $UserDbController->getItemsByParams($options, true);

        if (0 == $counter[0]['count']) {
            return [
                'total' => 0,
                'rows' => [],
            ];
        }

        $results = $UserDbController->getItemsByParams($options, false, false);

        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $converted_renta = 0;
            $have_converted_renta = false;
            $result = &$results[$i];

            if (null != $result['owner_id'] || '' != $result['owner_id']) {
                $ownerId = $result['owner_id'];
                $options = [
                    'return' => ['o.*'],
                    'where' => [
                        'owner_id' => ['column' => 'id', 'prefix' => 'o', 'compare' => '=', 'value' => $ownerId],
                    ],
                ];
                $resultOwner = $UserDbOwnersController->getOwnersData($options, false, false);

                $owner = $resultOwner[0];

                $result['owner_id'] = $owner['name'] . ' ' . $owner['surname'] . ' ' . $owner['lastname'];

                if ($owner['company_name']) {
                    $result['owner_id'] = $owner['company_name'];
                }
            }

            if (null != $result['charged_renta_nat'] || '' != $result['charged_renta_nat']) {
                $options = [
                    'tablename' => $UserDbController->DbHandler->chargedRentaNaturaParams,
                    'return' => ['amount', 'nat_type', 'is_converted', 'price_per_unit'],
                    'where' => [
                        'params_id' => ['column' => 'params_id', 'compare' => '=', 'value' => $paramsId],
                    ],
                ];

                $charged_renta_nat_results = $UserDbController->getItemsByParams($options, false, false);

                foreach ($charged_renta_nat_results as $charged_renta_nat) {
                    if (true == $charged_renta_nat['is_converted']) {
                        $converted_renta += ($charged_renta_nat['price_per_unit'] * $charged_renta_nat['amount'] * $result['owner_area']);
                        $have_converted_renta = true;
                    }
                }

                $rentaType = explode(',', $result['charged_renta_nat']);
                $rentaTypeCount = count($rentaType);
                $result['charged_renta_nat'] = [];

                for ($m = 0; $m < $rentaTypeCount; $m++) {
                    $renta = explode('-', $rentaType[$m]);

                    $type = $renta[0];
                    $amount = $renta[1];

                    if ('' == $amount || 0 == $amount) {
                        $amount = 'без количество';
                    } else {
                        $amount = number_format($amount, 3, '.', '');
                    }

                    $result['charged_renta_nat'][] = $renta_types[$type] . ' - ' . $amount;
                }

                $result['charged_renta_nat'] = implode('</br> ', $result['charged_renta_nat']);
            }

            if (null != $result['owner_area'] || '' != $result['owner_area']) {
                $result['owner_area'] = number_format($result['owner_area'], 3, '.', '');
            }

            if ($have_converted_renta) {
                $result['charged_renta'] += $converted_renta;
            }

            if (null != $result['charged_renta'] || '' != $result['charged_renta']) {
                $result['charged_renta'] = BGNtoEURO($result['charged_renta']);
            }

            if (null != $result['contract_id'] || '' != $result['contract_id']) {
                $options = [
                    'tablename' => $UserDbController->DbHandler->tableContracts,
                    'return' => ['c_num'],
                    'where' => [
                        'id' => ['column' => 'id', 'compare' => '=', 'value' => $result['contract_id']],
                    ],
                ];

                $resultsContracts = $UserDbController->getItemsByParams($options);
                $contractName = $resultsContracts[0];

                $result['contract_id'] = $contractName['c_num'];
            }
        }

        $results[0]['attributes']['pagination']['total'] = (int)$counter[0]['count'];
        $results[0]['attributes']['pagination']['limit'] = (int)$rows;

        return [
            'total' => $counter[0]['count'],
            'rows' => $results,
        ];
    }

    /**
     * Export to Excel charged renta history.
     *
     * @api-method expChargedRentaHistory
     *
     * @param string $exportType -Export type
     * @param string $paramsId -Charged renta history id
     * @param string $type -Charged renta history id
     *
     * @return array -Path to excel file
     */
    public function expChargedRentaHistory($exportType, $paramsId, $type)
    {
        // replace space and point with underline
        $type = str_replace([' ', '.', '/'], '_', $type);
        $fileName = $type . '.xlsx';

        $path = PUBLIC_UPLOAD_HISTORY . '/' . $this->User->GroupID . '/' . $fileName;
        $results = $this->readChargedRentaHistory($paramsId, null, null, 'id', 'asc');
        $result = $results['rows'];

        unset($result[0]['attributes']);

        $column_headers = [
            'ekate' => 'Землище',
            'kad_ident' => 'Име на парцел',
            'category' => 'Категория',
            'contract_id' => 'Договор',
            'owner_id' => 'Собственик',
            'owner_area' => 'Използвана площ (дка)',
            'charged_renta' => 'Начислена сума',
            'charged_renta_nat' => 'Начислено количество',
            'mestnost' => 'Местност',
        ];

        if (!file_exists(PUBLIC_UPLOAD_HISTORY . '/' . $this->User->GroupID)) {
            mkdir(PUBLIC_UPLOAD_HISTORY . '/' . $this->User->GroupID, 0777);
        }

        $export2Xls = new ExportToExcelClass();
        $export2Xls->export($result, $column_headers, []);
        $export2Xls->saveFile($path);

        $filePath = PUBLIC_UPLOAD_HISTORY_RELATIVE_PATH . '/' . $this->User->GroupID . '/' . $fileName;

        return ['file_path' => $filePath, 'file_name' => $fileName];
    }
}
