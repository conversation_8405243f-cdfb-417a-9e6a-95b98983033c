<?php

namespace TF\Engine\APIClasses\Payments;

use DateTime;
use Exception;
use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\ArrayHelper;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Add Payment.
 *
 * @rpc-module Payments
 *
 * @rpc-service-id contract-add-payment
 */
class AddPayment extends TRpcApiProvider
{
    public const PAYMENT_MONEY = 1;
    public const PAYMENT_NATURA = 2;
    private $renta_types = [];
    private $module = 'Payments';
    private $service_id = 'contract-add-payment';

    private $naturaArray = [];

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'savePayment' => ['method' => [$this, 'savePayment']],
            'saveNaturaPayment' => ['method' => [$this, 'saveNaturaPayment']],
            'savePersonalUsePayment' => ['method' => [$this, 'savePersonalUsePayment']],
            'generateRkoNumbers' => ['method' => [$this, 'generateRkoNumbers']],
            'generateRkoNatNumbers' => ['method' => [$this, 'generateRkoNatNumbers']],
            'setRkoNumberingStart' => ['method' => [$this, 'setRkoNumberingStart']],
            'getLastNumber' => ['method' => [$this, 'getLastNumber']],
        ];
    }

    /**
     * Save payment.
     *
     * @api-method savePayment
     *
     * @param array $data
     *                    {
     *                    #item array owner_array with data for owner
     *                    {
     *                    #item string owner_id
     *                    #item int contract_id
     *                    #item string owner_area
     *                    #item int unpaid_renta
     *                    #item boolean is_heritor
     *                    }
     *                    #item string year
     *                    #item boolean payment_reduce
     *                    }
     * @param array $inputData
     *                         {
     *                         #item boolean payment_type_money
     *                         #item boolean payment_type_natura
     *                         #item boolean payment_method_cash
     *                         #item boolean payment_method_bank
     *                         #item boolean payment_order
     *                         #item boolean weighing_note
     *                         #item string payment_amount
     *                         #item string payment_date
     *                         #item string payment_natura_type
     *                         #item string payment_natura_price
     *                         #item string payment_natura_amount
     *                         #item string payment_recipient
     *                         #item string payment_recipient_egn
     *                         #item string payment_recipient_proxy
     *                         #item string payment_bank_account
     *                         }
     *
     * @throws MTRpcException
     *
     * @return array|bool -Boolean must be 'false'
     *{
     *     #item string payment_type     -cash or bank
     *     #item string transaction_id   -transaction id
     *}
     */
    public function savePayment($data, $inputData)
    {
        if (true === $inputData['deduction'] && false === $inputData['payment_method_cash']) {
            throw new MTRpcException('DEDUCTION_ONLY_CASH', -33238);
        }

        $UsersController = new UsersController('Users');
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);

        // START VALIDATIONS

        if ($data['payment_reduce']) {
            $amountCoef = -1;
        } else {
            $amountCoef = 1;
        }

        // check if data is correct
        if (!$data['year'] || !(int)$data['year'] || 0 == count($data['owner_array'])) {
            return [];
        }

        // if we request rko number generation but we dont pass a farm id, throw error
        if (empty($data['farming_id']) && !empty($data['payment_numbering'])) {
            throw new MTRpcException('MISSING_FARMING_ID', -33227);
        }

        if (!empty($inputData['payment_numbering'])) {
            $this->validateRkoNumbering($inputData['payment_numbering']);
        }

        $this->validatePaymentDate($data, $inputData['payment_date']);

        // flags show if data is correct
        $owner_flag = true;
        $contract_flag = true;

        // check if all owner IDs are integer
        $ownerCount = count($data['owner_array']);
        for ($i = 0; $i < $ownerCount; $i++) {
            if (!(int)$data['owner_array'][$i]) {
                $owner_flag = false;
            }

            if (
                !isset($data['rko_key'])
                || !isset($data['owner_array'][$i][$data['rko_key']])
            ) {
                $owner_flag = false;
            }
        }

        if (is_array($data['contract_array'])) {
            // check if all contract IDs are integer
            for ($i = 0; $i < $contractsCount = count($data['contract_array']); $i++) {
                if (!(int)$data['contract_array'][$i]) {
                    $contract_flag = false;
                }
            }
        }

        // if info is incorrect or corrupt - exit payment saving
        if (false === $contract_flag && false === $owner_flag) {
            return [];
        }

        $totalContractsRent = 0;
        $overpaidRents = [];
        foreach ($data['owner_array'] as $ownerKey => $owner) {
            if ('owner_payments' == $data['payment_type']) {
                // Ако имаме собственик повече от един път в договор то може в единия договор да има надплатена рента.
                // Тази надплатена рента трябва да се извади от следващите плащания на собственика, за да не се получи надплащане в Ренти->Собственици
                if ($owner['unpaid_renta'] <= 0) {
                    if (!isset($overpaidRents[$owner['owner_id']][$owner['contract_id']])) {
                        $overpaidRents[$owner['owner_id']][$owner['contract_id']] = 0;
                    }
                    $overpaidRents[$owner['owner_id']][$owner['contract_id']] += abs($owner['unpaid_renta']);
                }

                if ($owner['unpaid_renta'] > 0 && isset($overpaidRents[$owner['owner_id']][$owner['contract_id']])) {
                    if ($owner['unpaid_renta'] >= $overpaidRents[$owner['owner_id']][$owner['contract_id']]) {
                        $rentToSubtract = $overpaidRents[$owner['owner_id']][$owner['contract_id']];
                    } else {
                        $rentToSubtract = $overpaidRents[$owner['owner_id']][$owner['contract_id']] - $owner['unpaid_renta'];
                    }
                    $data['owner_array'][$ownerKey]['unpaid_renta'] -= $rentToSubtract;
                    $overpaidRents[$owner['owner_id']][$owner['contract_id']] -= $rentToSubtract;
                }

                $totalContractsRent += $owner['unpaid_renta'];
            } else {
                $totalContractsRent += $owner['renta'];
            }
        }

        // check existed due renta
        if ($totalContractsRent <= 0) {
            throw new MTRpcException('MISSING_ADD_PAYMENT', -33218);
        }

        // END VALIDATIONS

        $transactionID = null;
        $collectionRecordID = null;
        $collectionRecordIDs = [];

        // if payment is money
        if ($inputData['payment_type_money']) {
            // create transaction options
            $options = [
                'tablename' => $UserDbController->DbHandler->tableTransactions,
                'mainData' => [
                    'date' => $inputData['payment_date'],
                    'paid_from' => self::PAYMENT_MONEY,
                    'paid_in' => self::PAYMENT_MONEY,
                    'amount' => $inputData['payment_amount'] * $amountCoef,
                    'recipient' => $inputData['payment_recipient'],
                    'recipient_egn' => $inputData['payment_recipient_egn'],
                    'recipient_proxy' => $inputData['payment_recipient_proxy'],
                    'recipient_address' => $inputData['payment_recipient_address'],
                    'recipient_lk' => $inputData['payment_recipient_lk'],
                    'farming_year' => $data['year'],
                    'payer_name' => $this->User->Name,
                    'status' => true,
                ],
            ];

            if ($inputData['payment_method_bank']) {
                $options['mainData']['bank_payment'] = 1;
                $options['mainData']['bank_acc'] = $inputData['payment_bank_account'];
            } elseif ($inputData['payment_method_cash']) {
                $options['mainData']['bank_payment'] = 0;
                $options['mainData']['bank_acc'] = '';
            } elseif ($inputData['payment_method_post_order']) {
                $options['mainData']['bank_payment'] = 1;
                $options['mainData']['bank_payment_type'] = 1;
                $options['mainData']['recipient_address'] = implode(', ', $options['mainData']['post_payment_sender_fields']);
            } else {
                throw new Exception('Invalid payment method');
            }

            $transaction = $UserDbPaymentsController->DbHandler->DbModule->beginTransaction();

            // add transaction in su_transactions
            $transactionID = $UserDbPaymentsController->addItem($options);
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options['mainData'], ['transaction_id' => $transactionID], 'Add transaction');

            try {
                // If the payments is made from rents->owners
                if ('owner_payments' == $data['payment_type']) {
                    $paymentAmount = $inputData['payment_amount'];

                    $ownerPayments = [];

                    foreach ($data['owner_array'] as &$owner) {
                        $ownerRenta = 0;
                        $ownerRenta = $owner['unpaid_renta'];
                        $owner['percent'] = ($ownerRenta) / ($totalContractsRent ? $totalContractsRent : 1);
                    }
                    unset($owner);

                    // if user is owner, his data is on the first position of $data['owner_array']
                    for ($i = 0; $i < $ownerCount; $i++) {
                        if ($paymentAmount <= 0) {
                            break;
                        }

                        $ownerID = $data['owner_array'][$i]['owner_id'];
                        $contractID = $data['owner_array'][$i]['contract_id'];
                        $path = $data['owner_array'][$i]['path'];
                        $unpaidRenta = $data['owner_array'][$i]['unpaid_renta'];
                        $isHeritor = $data['owner_array'][$i]['is_heritor'];
                        $ownerArea = $data['owner_array'][$i]['owner_area'];
                        $farming_id = $data['owner_array'][$i]['farming_id'];
                        $rkoKey = $data['owner_array'][$i][$data['rko_key']];
                        $rkoNumber = $inputData['payment_numbering'][$rkoKey];
                        $paymentMetaData = $data['owner_array'][$i]['payment_meta_data'];
                        $percent = $data['owner_array'][$i]['percent'];

                        if ('' == $isHeritor) {
                            $isHeritor = 'false';
                        }

                        $options = [
                            'tablename' => $UserDbController->DbHandler->tablePayments,
                            'mainData' => [
                                'owner_id' => $ownerID,
                                'contract_id' => $contractID,
                                'paid_from' => self::PAYMENT_MONEY,
                                'paid_in' => self::PAYMENT_MONEY,
                                'transaction_id' => $transactionID,
                                'farming_id' => $farming_id,
                                'date' => $inputData['payment_date'],
                                'farming_year' => $data['year'],
                                'path' => $path,
                                'is_heritor' => $isHeritor,
                                'rko_number' => $rkoNumber,
                                'payment_meta_data' => json_encode($paymentMetaData),
                            ],
                        ];

                        $rentaToPaid = round($paymentAmount * $percent, 2);

                        // Запазваме данните за всеки owner
                        $ownerPayments[] = [
                            'owner_id' => $ownerID,
                            'contract_id' => $contractID,
                            'transaction_id' => $transactionID,
                            'farming_id' => $farming_id,
                            'rko_number' => $rkoNumber,
                            'path' => $path,
                            'is_heritor' => $isHeritor,
                            'owner_area' => $ownerArea,
                            'amount' => ($rentaToPaid * $amountCoef),
                            'payments_meta_data' => $paymentMetaData,
                        ];
                        if ($rentaToPaid > 0) {
                            $options['mainData']['amount'] = $rentaToPaid * $amountCoef;
                            $recordID = $UserDbPaymentsController->addItem($options);

                            $UsersController->groupLog(
                                $this->User->Name,
                                $this->User->UserID,
                                $this->User->GroupID,
                                $this->module,
                                $this->service_id,
                                __METHOD__,
                                $options['mainData'],
                                ['payment_id' => $recordID],
                                'Add payment'
                            );
                        }
                    }

                    $ownerPaymentsCount = count($ownerPayments);
                } else {
                    // If the payments is made from rents->contracts
                    foreach ($data['owner_array'] as &$owner) {
                        $ownerRenta = 0;
                        $ownerRenta = $owner['renta'];
                        $owner['percent'] = ($ownerRenta) / ($totalContractsRent ? $totalContractsRent : 1);
                    }
                    unset($owner);

                    for ($i = 0; $i < $ownerCount; $i++) {
                        $ownerID = $data['owner_array'][$i]['owner_id'];
                        $contractID = $data['owner_array'][$i]['contract_id'];
                        $path = $data['owner_array'][$i]['path'];
                        $isHeritor = $data['owner_array'][$i]['is_heritor'] ? 'TRUE' : 'FALSE';
                        $percent = $data['owner_array'][$i]['percent'];
                        $paymentMetaData = $data['owner_array'][$i]['payment_meta_data'];
                        $rkoKey = $data['owner_array'][$i][$data['rko_key']];
                        $rkoNumber = $inputData['payment_numbering'][$rkoKey];

                        $options = [
                            'tablename' => $UserDbController->DbHandler->tablePayments,
                            'mainData' => [
                                'owner_id' => $ownerID,
                                'contract_id' => $contractID,
                                'farming_id' => $inputData['farming_id'],
                                'paid_from' => self::PAYMENT_MONEY,
                                'paid_in' => self::PAYMENT_MONEY,
                                'transaction_id' => $transactionID,
                                'date' => $inputData['payment_date'],
                                'farming_year' => $data['year'],
                                'path' => $path,
                                'is_heritor' => $isHeritor,
                                'payment_meta_data' => json_encode($paymentMetaData),
                                'rko_number' => $rkoNumber,
                            ],
                        ];

                        $options['mainData']['amount'] = ($inputData['payment_amount'] * $percent) * $amountCoef;

                        $recordID = $UserDbPaymentsController->addItem($options);
                        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options['mainData'], ['payment_id' => $recordID], 'Add payment');

                        if (true === $inputData['deduction']) {
                            foreach ($data['owner_array'][$i]['personal_use'] as $personalUse) {
                                if (empty($personalUse['renta_type'])) {
                                    throw new MTRpcException('MISSING_RENTA_TYPE', -33047);
                                }
                            }
                            $FarmingController = new FarmingController('Farming');
                            $farming = $FarmingController->getFarmings([
                                'return' => ['id', 'name', 'company', 'bulstat', 'address', 'company_address', 'mol', 'mol_egn', 'farming_mol_phone', 'iban_arr'],
                                'where' => [
                                    'id' => ['column' => 'id', 'compare' => '=', 'value' => $inputData['farming_id']],
                                ],
                            ], false, false);

                            foreach ($data['owner_array'][0]['personal_use'] as $personalUse) {
                                $paymentData = [
                                    'payment_method_cash' => $inputData['payment_method_cash'],
                                    'payment_method_bank' => $inputData['payment_method_bank'],
                                    'recipient' => $inputData['collections_recipient_name'],
                                    'recipient_company' => $farming[0]['name'],
                                    'recipient_egn' => $inputData['collections_recipient_egn'],
                                    'recipient_proxy' => '',
                                    'recipient_address' => $inputData['collections_recipient_address'],
                                    'recipient_lk' => $inputData['collections_recipient_lk'],
                                    'rko_number' => '',
                                    'rko_text' => '',
                                    'orderer_iban' => '',
                                    'subleasing_farm_iban' => '',
                                    'owner_id' => $ownerID,
                                    'owner_names' => '',
                                    'farming_id' => $inputData['farming_id'],
                                    'subleasing_farm_id' => '',
                                    'renta_type_id' => $personalUse['renta_type'],
                                    'renta_type_name' => $personalUse['renta_type_name'],
                                ];

                                $options = [
                                    'tablename' => 'su_collections',
                                    'mainData' => [
                                        'contract_id' => $data['owner_array'][0]['annex_id'] ? $data['owner_array'][0]['annex_id'] : $data['owner_array'][0]['contract_id'],
                                        'date' => $inputData['payment_date'],
                                        'amount' => $personalUse['personal_use_unpaid_treatments'],
                                        'recieved_from' => $inputData['payment_recipient'],
                                        'bank_payment' => $inputData['payment_method_bank'],
                                        'payment_order' => $inputData['payment_order'],
                                        'user_name' => $this->User->Name,
                                        'farming_year' => $data['year'],
                                        'payment_data' => json_encode($paymentData),
                                        'type' => COLLECTION_TYPE_PERSONAL_USE,
                                    ],
                                ];

                                $collectionRecordID = $UserDbController->addItem($options);
                                $collectionRecordIDs[] = $collectionRecordID;
                                $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_values' => $options], ['created_id' => $collectionRecordID], 'Adding collection payment');
                            }
                        }
                    }
                }

                $transaction->commit();
            } catch (Exception $e) {
                $transaction->rollBack();

                throw $e;
            }
        }

        // if payment is natura
        if ($inputData['payment_type_natura']) {
            $paymentsNatura = $inputData['payments_natura'];
            $paidAmount = 0;

            foreach ($paymentsNatura as $paymentNatura) {
                $paidAmount += round($paymentNatura['amount'] * $paymentNatura['price'], 3);
            }

            // create transaction options
            $options = [
                'tablename' => $UserDbController->DbHandler->tableTransactions,
                'mainData' => [
                    'date' => $inputData['payment_date'],
                    'paid_from' => self::PAYMENT_MONEY,
                    'paid_in' => self::PAYMENT_NATURA,
                    'amount' => $paidAmount * $amountCoef,
                    'recipient' => $inputData['payment_recipient'],
                    'recipient_egn' => $inputData['payment_recipient_egn'],
                    'recipient_proxy' => $inputData['payment_recipient_proxy'],
                    'recipient_address' => $inputData['payment_recipient_address'],
                    'recipient_lk' => $inputData['payment_recipient_lk'],
                    'amount_nat' => null,
                    'payment_nat_type' => 0,
                    'farming_year' => $data['year'],
                    'payer_name' => $this->User->Name,
                    'status' => true,
                ],
            ];

            $transactionID = $UserDbPaymentsController->addItem($options);
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options['mainData'], ['transaction_id' => $transactionID], 'Add transaction');

            foreach ($paymentsNatura as $paymentNatura) {
                $transactionOptions = [
                    'tablename' => $UserDbController->DbHandler->tableNaturaTransactions,
                    'mainData' => [
                        'paid_from' => self::PAYMENT_MONEY,
                        'paid_in' => self::PAYMENT_NATURA,
                        'transaction_id' => $transactionID,
                        'nat_type' => $paymentNatura['type'],
                        'amount' => round(($paymentNatura['amount'] * $amountCoef), 3),
                    ],
                ];
                $UserDbPaymentsController->addItem($transactionOptions);
            }

            if ('owner_payments' == $data['payment_type']) {
                $paymentAmount = $paidAmount;

                $ownerPayments = [];
                // if user is owner, his data is on the first position of $data['owner_array']
                for ($i = 0; $i < $ownerCount; $i++) {
                    if ($paymentAmount <= 0) {
                        break;
                    }

                    $ownerID = $data['owner_array'][$i]['owner_id'];
                    $contractID = $data['owner_array'][$i]['contract_id'];
                    $path = $data['owner_array'][$i]['path'];
                    $unpaidRenta = $data['owner_array'][$i]['unpaid_renta'];
                    $isHeritor = $data['owner_array'][$i]['is_heritor'];
                    $ownerArea = $data['owner_array'][$i]['owner_area'];
                    $rkoKey = $data['owner_array'][$i][$data['rko_key']];
                    $rkoNumber = $inputData['payment_numbering'][$rkoKey];

                    $options = [
                        'tablename' => $UserDbController->DbHandler->tablePayments,
                        'mainData' => [
                            'owner_id' => $ownerID,
                            'contract_id' => $contractID,
                            'paid_from' => self::PAYMENT_MONEY,
                            'paid_in' => self::PAYMENT_NATURA,
                            'transaction_id' => $transactionID,
                            'date' => $inputData['payment_date'],
                            'farming_year' => $data['year'],
                            'path' => $path,
                            'is_heritor' => $isHeritor,
                            'rko_number' => $rkoNumber,
                        ],
                    ];

                    if ($unpaidRenta > $paymentAmount) {
                        $options['mainData']['amount'] = $paymentAmount * $amountCoef;
                    } else {
                        $options['mainData']['amount'] = $unpaidRenta * $amountCoef;
                    }

                    $ownerPercentNatura = $options['mainData']['amount'] / $paidAmount;

                    foreach ($paymentsNatura as $paymentNatura) {
                        $options['mainData']['amount_nat'] += round((($paymentNatura['amount'] * $ownerPercentNatura) * $amountCoef), 3);
                    }

                    $paymentAmount -= $unpaidRenta;

                    // Запазваме данните за всеки owner
                    $ownerPayments[] = ['owner_id' => $ownerID, 'contract_id' => $contractID, 'transaction_id' => $transactionID,
                        'path' => $path, 'is_heritor' => $isHeritor, 'owner_area' => $ownerArea, 'amount' => $options['mainData']['amount'], 'rko_number' => $rkoNumber];

                    if (0 == $options['mainData']['amount']) {
                        continue;
                    }

                    $recordID = $UserDbPaymentsController->addItem($options);

                    $UsersController->groupLog(
                        $this->User->Name,
                        $this->User->UserID,
                        $this->User->GroupID,
                        $this->module,
                        $this->service_id,
                        __METHOD__,
                        $options['mainData'],
                        ['paymrnt_id' => $recordID],
                        'Add payment'
                    );

                    foreach ($paymentsNatura as $paymentNatura) {
                        $naturaOptions = [
                            'tablename' => $UserDbController->DbHandler->tableNaturaPayments,
                            'mainData' => [
                                'payment_id' => $recordID,
                                'nat_type' => $paymentNatura['type'],
                                'amount' => round(($paymentNatura['amount'] * $ownerPercentNatura * $amountCoef), 3),
                                'unit_value' => $paymentNatura['price'],
                            ],
                        ];

                        $lastOwnerPayments = count($ownerPayments) - 1;

                        // add payment_id and amount natura to data for owner
                        $ownerPayments[$lastOwnerPayments]['payment_id'] = $recordID;
                        $ownerPayments[$lastOwnerPayments]['amount_nat'] += $naturaOptions['mainData']['amount'];
                        $ownerPayments[$lastOwnerPayments]['natura'][] = $naturaOptions['mainData'];

                        $naturaRecordID = $UserDbPaymentsController->addItem($naturaOptions);

                        $UsersController->groupLog(
                            $this->User->Name,
                            $this->User->UserID,
                            $this->User->GroupID,
                            $this->module,
                            $this->service_id,
                            __METHOD__,
                            $naturaOptions['mainData'],
                            ['natura_paymrnt_id' => $naturaRecordID],
                            'Add natura payment'
                        );
                    }
                }

                if ($paymentAmount > 0) {
                    $ownerArea = 0;

                    for ($i = 0; $i < $ownerCount; $i++) {
                        $ownerArea += $data['owner_array'][$i]['owner_area'];
                    }

                    $paymentPercent = $paymentAmount / $ownerArea;
                    $ownerPaymentsCount = count($ownerPayments);
                    for ($i = 0; $i < $ownerPaymentsCount; $i++) {
                        $paymentAmountPerUser = $ownerPayments[$i]['owner_area'] * $paymentPercent;
                        $ownerID = $ownerPayments[$i]['owner_id'];
                        $contractID = $ownerPayments[$i]['contract_id'];
                        $path = $ownerPayments[$i]['path'];
                        $unpaidRenta = $ownerPayments[$i]['unpaid_renta'];
                        $isHeritor = $ownerPayments[$i]['is_heritor'];
                        $paymentID = $ownerPayments[$i]['payment_id'];
                        $oldAmountNat = $ownerPayments[$i]['amount_nat'];
                        $oldAmount = $ownerPayments[$i]['amount'];
                        $amount = $paymentAmountPerUser * $amountCoef;
                        $rkoNumber = $ownerPayments[$i]['rko_number'];

                        $ownerPercentNatura = $paymentAmountPerUser / $paidAmount;
                        $amountNat = 0;
                        foreach ($paymentsNatura as $paymentNatura) {
                            $amountNat += round((($paymentNatura['amount'] * $ownerPercentNatura) * $amountCoef), 3);
                        }

                        if ($paymentAmountPerUser <= 0) {
                            continue;
                        }

                        $options = [
                            'tablename' => $UserDbController->DbHandler->tablePayments,
                            'mainData' => [
                                'amount' => $amount + $oldAmount,
                                'amount_nat' => $amountNat + $oldAmountNat,
                            ],
                            'where' => [
                                'id' => $paymentID,
                            ],
                        ];

                        $rows = $UserDbController->editItem($options);

                        if (0 == $rows) {
                            $options = [
                                'tablename' => $UserDbController->DbHandler->tablePayments,
                                'mainData' => [
                                    'owner_id' => $ownerID,
                                    'contract_id' => $contractID,
                                    'paid_from' => self::PAYMENT_MONEY,
                                    'paid_in' => self::PAYMENT_NATURA,
                                    'amount_nat' => $amountNat,
                                    'transaction_id' => $transactionID,
                                    'date' => $inputData['payment_date'],
                                    'farming_year' => $data['year'],
                                    'path' => $path,
                                    'is_heritor' => $isHeritor,
                                    'amount' => $amount,
                                    'rko_number' => $rkoNumber,
                                ],
                            ];

                            $recordID = $UserDbPaymentsController->addItem($options);
                            $UsersController->groupLog(
                                $this->User->Name,
                                $this->User->UserID,
                                $this->User->GroupID,
                                $this->module,
                                $this->service_id,
                                __METHOD__,
                                $options['mainData'],
                                ['paymеnt_id' => $recordID],
                                'Add payment'
                            );

                            foreach ($paymentsNatura as $paymentNatura) {
                                $naturaOptions = [
                                    'tablename' => $UserDbController->DbHandler->tableNaturaPayments,
                                    'mainData' => [
                                        'payment_id' => $recordID,
                                        'nat_type' => $paymentNatura['type'],
                                        'amount' => round((($paymentNatura['amount'] * $ownerPercentNatura) * $amountCoef), 3),
                                        'unit_value' => $paymentNatura['price'],
                                    ],
                                ];

                                $naturaRecordID = $UserDbPaymentsController->addItem($naturaOptions);
                                $UsersController->groupLog(
                                    $this->User->Name,
                                    $this->User->UserID,
                                    $this->User->GroupID,
                                    $this->module,
                                    $this->service_id,
                                    __METHOD__,
                                    $naturaOptions['mainData'],
                                    ['natura_paymеnt_id' => $naturaRecordID],
                                    'Add natura payment'
                                );
                            }
                        } else {
                            foreach ($paymentsNatura as $paymentNatura) {
                                $oltNatType = null;
                                foreach ($ownerPayments[$i]['natura'] as $payment) {
                                    if ($payment['nat_type'] === $paymentNatura['type']) {
                                        $oltNatType = $payment;

                                        break;
                                    }
                                }

                                $amountNat = round((($paymentNatura['amount'] * $ownerPercentNatura) * $amountCoef), 3);

                                $options = [
                                    'tablename' => $UserDbController->DbHandler->tableNaturaPayments,
                                    'mainData' => [
                                        'amount' => $amountNat + $oltNatType['amount'],
                                    ],
                                    'where' => [
                                        'payment_id' => $paymentID,
                                        'nat_type' => $paymentNatura['type'],
                                    ],
                                ];

                                $UserDbController->editItem($options);
                            }
                        }
                    }
                }
            } else {
                unset($owner);

                foreach ($data['owner_array'] as &$owner) {
                    $ownerRenta = 0;
                    $paidRenta = $owner['paid_renta'] ?? 0;
                    if ($owner['renta']) {
                        $ownerRenta = $owner['renta'];
                    }

                    $owner['percent'] = ($ownerRenta) / ($totalContractsRent ? $totalContractsRent : 1);
                }
                unset($owner);

                foreach ($data['owner_array'] as $owner) {
                    $ownerID = $owner['owner_id'];
                    $contractID = $owner['contract_id'];
                    $path = $owner['path'];
                    $isHeritor = $owner['is_heritor'] ? 'TRUE' : 'FALSE';
                    $percent = $owner['percent'];
                    $rkoKey = $owner[$data['rko_key']];
                    $rkoNumber = $inputData['payment_numbering'][$rkoKey];

                    $options = [
                        'tablename' => $UserDbController->DbHandler->tablePayments,
                        'mainData' => [
                            'owner_id' => $ownerID,
                            'contract_id' => $contractID,
                            'farming_id' => $inputData['farming_id'],
                            'paid_from' => self::PAYMENT_MONEY,
                            'paid_in' => self::PAYMENT_NATURA,
                            'transaction_id' => $transactionID,
                            'date' => $inputData['payment_date'],
                            'farming_year' => $data['year'],
                            'path' => $path,
                            'is_heritor' => $isHeritor,
                            'rko_number' => $rkoNumber,
                        ],
                    ];

                    $options['mainData']['amount'] = ($paidAmount * $percent) * $amountCoef;
                    // amount_nat is the quantity
                    foreach ($paymentsNatura as $paymentNatura) {
                        $options['mainData']['amount_nat'] += round((($paymentNatura['amount'] * $percent) * $amountCoef), 3);
                    }

                    $recordID = $UserDbPaymentsController->addItem($options);
                    $UsersController->groupLog(
                        $this->User->Name,
                        $this->User->UserID,
                        $this->User->GroupID,
                        $this->module,
                        $this->service_id,
                        __METHOD__,
                        $options['mainData'],
                        ['paymеnt_id' => $recordID],
                        'Add payment'
                    );

                    foreach ($paymentsNatura as $paymentNatura) {
                        $naturaOptions = [
                            'tablename' => $UserDbController->DbHandler->tableNaturaPayments,
                            'mainData' => [
                                'payment_id' => $recordID,
                                'nat_type' => $paymentNatura['type'],
                                'amount' => round((($paymentNatura['amount'] * $percent) * $amountCoef), 3),
                                'unit_value' => $paymentNatura['price'],
                            ],
                        ];

                        $naturaRecordID = $UserDbPaymentsController->addItem($naturaOptions);
                        $UsersController->groupLog(
                            $this->User->Name,
                            $this->User->UserID,
                            $this->User->GroupID,
                            $this->module,
                            $this->service_id,
                            __METHOD__,
                            $naturaOptions['mainData'],
                            ['natura_paymеnt_id' => $naturaRecordID],
                            'Add natura payment'
                        );
                    }
                }
            }
        }

        $this->updateFarmingStartRko($data, $inputData);

        // set response data
        if ($inputData['payment_order'] && $inputData['payment_type_money']) {
            return [
                'payment_type' => $inputData['payment_method_cash'] ? 'cash' : ($inputData['payment_method_post_order'] ? 'post' : 'bank'),
                'transaction_id' => $transactionID,
                'collection_id' => $collectionRecordIDs,
                'submited_data' => $data,
            ];
        }

        if ($inputData['weighing_note'] && $inputData['payment_type_natura']) {
            return [
                'payment_type' => 'natura',
                'transaction_id' => $transactionID,
                'collection_id' => $collectionRecordIDs,
                'submited_data' => $data,
            ];
        }

        return false;
    }

    /**
     * Save natura payment.
     *
     * @api-method saveNaturaPayment
     *
     * @param array $data
     *                    {
     *                    #item array owner_array with data for owner
     *                    #item string year
     *                    #item boolean payment_reduce
     *                    }
     * @param array $inputData
     *                         {
     *                         #item boolean payment_type_money
     *                         #item boolean payment_type_natura
     *                         #item boolean payment_method_cash
     *                         #item boolean payment_method_bank
     *                         #item boolean payment_order
     *                         #item boolean weighing_note
     *                         #item string payment_amount
     *                         #item string payment_date
     *                         #item string payment_natura_type
     *                         #item string payment_natura_price
     *                         #item string payment_natura_amount
     *                         #item string payment_recipient
     *                         #item string payment_recipient_egn
     *                         #item string payment_recipient_proxy
     *                         #item string payment_bank_account
     *                         }
     *
     * @throws MTRpcException
     *
     * @return array|int -If Integer the response is 0.
     *{
     *     #item string payment_type     -Cash or bank
     *     #item string transaction_id   -Transaction id
     *}
     */
    public function saveNaturaPayment($data, $inputData)
    {
        $UsersController = new UsersController('Users');
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        $this->validatePaymentDate($data, $inputData['np_date']);

        $data = (object)$data;
        $amount_coef = $data->payment_reduce ? -1 : 1;

        // check if data is correct
        if (!$data->year || !(int)$data->year || 0 == count($data->owner_array)) {
            return [];
        }

        // if we request rko number generation but we dont pass a farm id, throw error
        if (empty($data->farming_id) && !empty($data->payment_numbering)) {
            throw new MTRpcException('MISSING_FARMING_ID', -33227);
        }

        $owner_flag = true;
        if (is_array($data->owner_array)) {
            for ($i = 0; $i < $ownersCount = count($data->owner_array); $i++) {
                if (!(int)$data->owner_array[$i]) {
                    $owner_flag = false;

                    break;
                }

                if (
                    !isset($data->rko_key)
                    || !isset($data->owner_array[$i][$data->rko_key])
                ) {
                    $owner_flag = false;

                    break;
                }
            }
        }

        $contract_flag = true;
        if (is_array($data->contract_array)) {
            for ($i = 0; $i < $contractsCount = count($data->contract_array); $i++) {
                if (!(int)$data->contract_array[$i]) {
                    $contract_flag = false;

                    break;
                }
            }
        }

        if (false === $contract_flag && false === $owner_flag) {
            return [];
        }
        unset($contract_flag, $owner_flag);
        // check existing due renta
        list($haveChargedRentaNats, $haveRentaNats) = $this->hasChargedRentOrNaturaRent($data);

        if (!$haveChargedRentaNats && !$haveRentaNats) {
            throw new MTRpcException('MISSING_ADD_PAYMENT', -33218);
        }
        unset($haveChargedRentaNats, $haveRentaNats);
        $paid_renta_per_nat_type = [];
        if ($inputData['np_type_money']) {
            $contractsIds = array_unique(array_map(function ($owner) {
                return isset($owner['annex_id']) && is_numeric($owner['annex_id']) ? $owner['annex_id'] : $owner['contract_id'];
            }, $data->owner_array));

            $naturaRentsOptions = [
                'tablename' => $UserDbController->DbHandler->contractsRentsRelTable,
                'where' => [
                    'contract_id' => ['column' => 'contract_id', 'compare' => 'IN', 'value' => $contractsIds],
                ],
            ];

            $naturaRents = $UserDbController->getItemsByParams($naturaRentsOptions, false, false);

            // data by natura in naturaArray
            $natIds = array_column($naturaRents, 'renta_id');

            foreach ($data->renta_nat_type_id as $i => $natID) {
                if ('' == $data->renta_nat_total[$i] || $data->renta_nat_total[$i] <= 0
                    || $data->renta_nat_price_per_unit[$i] <= 0 || !in_array($natID, $natIds)) {
                    unset(
                        $data->renta_nat_price_per_unit[$i],
                        $data->renta_nat_total[$i],
                        $data->renta_nat_type_id[$i]
                    );

                    continue;
                }
                $natura = [];
                $natura['nat_id'] = (int)$natID;
                $natura['unit_value'] = (float)$data->renta_nat_price_per_unit[$i];
                $natura['due_leva_tot_by_nat'] = $data->renta_nat_total[$i];
                $natura['due_qty_by_nat'] = $natura['due_leva_tot_by_nat'] / $natura['unit_value'];
                foreach ($naturaRents as $naturaRent) {
                    if ($naturaRent['renta_id'] == $natID) {
                        $natura['qty_for_decar'] = $naturaRent['renta_value'];
                    }
                }
                $this->naturaArray[$natID] = $natura;
                $paid_renta_per_nat_type[$i] = (0 == $data->renta_nat_price_per_unit[$i]) ? 0 : number_format($data->renta_nat_total[$i] / $data->renta_nat_price_per_unit[$i], 3, '.', '');
            }

            $paid_total = array_sum(array_column($this->naturaArray, 'due_leva_tot_by_nat'));
            unset($owner);

            $totalNatMoneyRenta = [];
            foreach ($data->owner_array as $owner) {
                foreach ($owner['renta_nat'] as $natKey => $natValue) {
                    if (!isset($totalNatMoneyRenta[$natKey])) {
                        $totalNatMoneyRenta[$natKey] = $natValue;
                    } else {
                        $totalNatMoneyRenta[$natKey] += $natValue;
                    }

                    if (!empty($owner['charged_renta_nat_values'][$natKey])) {
                        if (!isset($totalNatMoneyRenta[$natKey])) {
                            $totalNatMoneyRenta[$natKey] = $owner['charged_renta_nat_values'][$natKey];
                        } else {
                            $totalNatMoneyRenta[$natKey] += $owner['charged_renta_nat_values'][$natKey];
                        }
                    }

                    $totalNatMoneyRenta[$natKey] -= $owner['paid_renta_nat'][$natKey] ?? 0;
                }
            }

            foreach ($data->owner_array as &$owner) {
                foreach ($this->naturaArray as $natID => $natObj) {
                    $contractNatValue = $owner['renta_nat'][$natID];
                    $chargedNatValue = $owner['charged_renta_nat_values'][$natID];
                    $natValue = $contractNatValue + $chargedNatValue;
                    $percent = ($natValue - ($owner['paid_renta_nat'][$natID] ?? 0)) / ($totalNatMoneyRenta[$natID] ?? 1);

                    $owner['qty_to_pay'][$natID] = $natObj['due_qty_by_nat'] * $percent;
                    $owner['leva_to_pay'][$natID] = $natObj['due_leva_tot_by_nat'] * $percent;
                }
            }
            unset($owner, $naturaRents, $naturaRent, $natura, $natIDs, $natID, $naturaRentsOptions);

            // create transaction options
            $options = [
                'tablename' => $UserDbController->DbHandler->tableTransactions,
                'mainData' => [
                    'date' => $inputData['np_date'],
                    'paid_from' => self::PAYMENT_NATURA,
                    'paid_in' => self::PAYMENT_MONEY,
                    'recipient' => $inputData['np_recipient'],
                    'recipient_egn' => $inputData['np_recipient_egn'],
                    'recipient_proxy' => $inputData['np_recipient_proxy'],
                    'recipient_address' => $inputData['np_recipient_address'],
                    'recipient_lk' => $inputData['np_recipient_lk'],
                    'amount' => $paid_total * $amount_coef,
                    'farming_year' => $data->year,
                    'payer_name' => $this->User->Name,
                    'status' => true,
                ],
            ];

            if ($inputData['np_method_bank']) {
                $options['mainData']['bank_payment'] = 1;
                $options['mainData']['bank_acc'] = $inputData['np_bank_account'];
            } else {
                $options['mainData']['bank_payment'] = 0;
                $options['mainData']['bank_acc'] = '';
            }

            $transactionID = $UserDbPaymentsController->addItem($options);

            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options['mainData'], ['transaction_id' => $transactionID], 'Add transaction');
            // add records on su_transactions_natura by kind
            $transactionOptions = [
                'tablename' => $UserDbController->DbHandler->tableNaturaTransactions,
                'mainData' => [
                    'paid_from' => self::PAYMENT_NATURA,
                    'paid_in' => self::PAYMENT_MONEY,
                ],
            ];
            $paidRentaPerNatTypeCount = count($paid_renta_per_nat_type);
            foreach ($this->naturaArray as $natID => $natObj) {
                $transactionOptions['mainData']['transaction_id'] = $transactionID;
                $transactionOptions['mainData']['nat_type'] = $natID;
                $transactionOptions['mainData']['amount'] = $natObj['due_qty_by_nat'] * $amount_coef;

                $naturaTransactionID = $UserDbPaymentsController->addItem($transactionOptions);

                $UsersController->groupLog(
                    $this->User->Name,
                    $this->User->UserID,
                    $this->User->GroupID,
                    $this->module,
                    $this->service_id,
                    __METHOD__,
                    $transactionOptions['mainData'],
                    ['natura_transaction_id' => $naturaTransactionID],
                    'Add natura transaction'
                );
            }

            if ('owner_payments' == $data->payment_type) {
                $ownerPayments = [];
                // if user is owner, his data is on the first position of $data->owner_array
                for ($i = 0; $i < $ownersCount; $i++) {
                    // check if natura left
                    $leftNatura = $this->leftPositiveValueInArray($paid_renta_per_nat_type);

                    if ($leftNatura) {
                        break;
                    }

                    // check if owner haven't area
                    if ($data->owner_array[$i]['owner_area'] <= 0) {
                        continue;
                    }

                    $ownerID = $data->owner_array[$i]['owner_id'];
                    $contractID = $data->owner_array[$i]['contract_id'];
                    $farmingID = $data->owner_array[$i]['farming_id'];
                    $path = $data->owner_array[$i]['path'];
                    $ownerArea = $data->owner_array[$i]['owner_area'];
                    $unpaidNatura = explode('</br>', $data->owner_array[$i]['unpaid_renta_nat']);
                    $isHeritor = $data->owner_array[$i]['is_heritor'] ? 'TRUE' : 'FALSE';
                    $rkoKey = $data->owner_array[$i][$data->rko_key];
                    $rkoNumber = $inputData['payment_numbering'][$rkoKey];

                    $unpaidNatSum = 0;
                    for ($k = 0; $k < $paidRentaPerNatTypeCount; $k++) {
                        $unpaidNatSum += (float)$unpaidNatura[$k];
                    }

                    // Запазваме данните за всеки owner
                    $ownerPayments[] = ['owner_id' => $ownerID, 'contract_id' => $contractID, 'transaction_id' => $transactionID,
                        'path' => $path, 'is_heritor' => $isHeritor, 'owner_area' => $ownerArea, 'unpaid_renta_nat' => $unpaidNatura, 'rko_number' => $rkoNumber];

                    if (0 == $unpaidNatSum) {
                        continue;
                    }

                    $options = [
                        'tablename' => $UserDbController->DbHandler->tablePayments,
                        'mainData' => [
                            'owner_id' => $ownerID,
                            'contract_id' => $contractID,
                            'farming_id' => $farmingID,
                            'paid_from' => self::PAYMENT_NATURA,
                            'paid_in' => self::PAYMENT_MONEY,
                            'transaction_id' => $transactionID,
                            'date' => $inputData['np_date'],
                            'farming_year' => $data->year,
                            'path' => $path,
                            'is_heritor' => $isHeritor,
                            'rko_number' => $rkoNumber,
                        ],
                    ];

                    $recordID = $UserDbPaymentsController->addItem($options);
                    $lastOwnerPayments = count($ownerPayments) - 1;

                    // add payment_id to data for owner
                    $ownerPayments[$lastOwnerPayments]['payment_id'] = $recordID;

                    $naturaOptions = [
                        'tablename' => $UserDbController->DbHandler->tableNaturaPayments,
                        'mainData' => [
                            'payment_id' => $recordID,
                        ],
                    ];

                    $totalAmountByPayment = 0;
                    for ($j = 0; $j < $paidRentaPerNatTypeCount; $j++) {
                        $paymentAmount = number_format($paid_renta_per_nat_type[$j], 3, '.', '');
                        $unpaidNat = number_format($unpaidNatura[$j], 3, '.', '');

                        if (null != $paymentAmount && $unpaidNat >= 0) {
                            if ($paymentAmount < 0) {
                                continue;
                            }

                            $naturaOptions['mainData']['nat_type'] = $data->renta_nat_type_id[$j];
                            $naturaOptions['mainData']['unit_value'] = $data->renta_nat_price_per_unit[$j];

                            $naturaAmount = 0;
                            if ($unpaidNat > $paid_renta_per_nat_type[$j]) {
                                $naturaAmount = $paymentAmount;
                            } else {
                                $naturaAmount = $unpaidNat;
                            }

                            $naturaOptions['mainData']['amount'] = $naturaAmount * $amount_coef;

                            $paid_renta_per_nat_type[$j] -= $unpaidNat;

                            if ($naturaAmount > 0) {
                                $amountByPayment = $naturaOptions['mainData']['amount'] * $naturaOptions['mainData']['unit_value'];
                                $totalAmountByPayment += $amountByPayment;

                                // add nat amount nat for natura type to data for owner
                                $ownerPayments[$lastOwnerPayments]['amount_nat'][$data->renta_nat_type_id[$j]] = $naturaOptions['mainData']['amount'];

                                $naturaPaymentID = $UserDbPaymentsController->addItem($naturaOptions);
                                $UsersController->groupLog(
                                    $this->User->Name,
                                    $this->User->UserID,
                                    $this->User->GroupID,
                                    $this->module,
                                    $this->service_id,
                                    __METHOD__,
                                    $naturaOptions['mainData'],
                                    ['natura_payment_id' => $naturaPaymentID],
                                    'Add natura payment'
                                );
                            }
                        }
                    }

                    $options = [
                        'tablename' => $UserDbController->DbHandler->tablePayments,
                        'mainData' => [
                            'amount' => number_format($totalAmountByPayment, 2, '.', ''),
                        ],
                        'where' => [
                            'id' => $recordID,
                        ],
                    ];

                    $UserDbController->editItem($options);

                    // add amount to data for owner
                    $ownerPayments[$lastOwnerPayments]['amount'] = $totalAmountByPayment;
                }

                // check if natura left
                $leftNatura = $this->leftPositiveValueInArray($paid_renta_per_nat_type);
                $ownerPaymentsCount = count($ownerPayments);
                if (!$leftNatura) {
                    $ownerArea = 0;
                    // sum owner_area
                    $count_data_owner_arr = count($data->owner_array);
                    for ($k = 0; $k < $count_data_owner_arr; $k++) {
                        $ownerArea += $data->owner_array[$k]['owner_area'];
                    }

                    // if is left natura, pay by owner_area percent
                    for ($i = 0; $i < $ownerPaymentsCount; $i++) {
                        // check if owner haven't area
                        if ($ownerPayments[$i]['owner_area'] <= 0) {
                            continue;
                        }

                        $ownerID = $ownerPayments[$i]['owner_id'];
                        $contractID = $ownerPayments[$i]['contract_id'];
                        $path = $ownerPayments[$i]['path'];
                        $unpaidNatura = $ownerPayments[$i]['unpaid_renta_nat'];
                        $recordID = $ownerPayments[$i]['payment_id'];
                        $oldAmount = $ownerPayments[$i]['amount'];
                        $oldAmountNat = $ownerPayments[$i]['amount_nat'];
                        $isHeritor = ($ownerPayments[$i]['is_heritor']) ? 'TRUE' : 'FALSE';
                        $rkoNumber = $ownerPayments[$i]['rko_number'];

                        if (!$recordID) {
                            $options = [
                                'tablename' => $UserDbController->DbHandler->tablePayments,
                                'mainData' => [
                                    'owner_id' => $ownerID,
                                    'contract_id' => $contractID,
                                    'paid_from' => self::PAYMENT_NATURA,
                                    'paid_in' => self::PAYMENT_MONEY,
                                    'transaction_id' => $transactionID,
                                    'date' => $inputData['np_date'],
                                    'farming_year' => $data->year,
                                    'path' => $path,
                                    'is_heritor' => $isHeritor,
                                    'rko_number' => $rkoNumber,
                                ],
                            ];

                            $recordID = $UserDbPaymentsController->addItem($options);
                        }

                        $naturaOptions = [
                            'tablename' => $UserDbController->DbHandler->tableNaturaPayments,
                            'mainData' => [
                                'payment_id' => $recordID,
                            ],
                        ];

                        $totalAmountByPaymentPercent = 0;
                        for ($j = 0; $j < $paidRentaPerNatTypeCount; $j++) {
                            $paymentAmount = (float)$paid_renta_per_nat_type[$j];
                            $unpaidNat = (float)$unpaidNatura[$j];
                            $renta_nat_type_id = $data->renta_nat_type_id[$j];

                            if (null != $paymentAmount && $unpaidNat >= 0) {
                                $paymentPercent = $paymentAmount / $ownerArea;
                                $paymentAmountPerUser = $data->owner_array[$i]['owner_area'] * $paymentPercent;

                                $naturaOptions['mainData']['nat_type'] = $renta_nat_type_id;
                                $naturaOptions['mainData']['unit_value'] = $data->renta_nat_price_per_unit[$j];
                                $naturaOptions['mainData']['amount'] = number_format($paymentAmountPerUser, 4, '.', '') * $amount_coef;

                                if ($paymentAmountPerUser > 0) {
                                    $totalAmountByPaymentPercent += $naturaOptions['mainData']['amount'] * $naturaOptions['mainData']['unit_value'];

                                    if ($oldAmountNat[$renta_nat_type_id]) {
                                        $optionsNaturaPercent = [
                                            'tablename' => $UserDbController->DbHandler->tableNaturaPayments,
                                            'mainData' => [
                                                'amount' => ($oldAmountNat[$renta_nat_type_id] + $naturaOptions['mainData']['amount']),
                                            ],
                                            'where' => [
                                                'payment_id' => $recordID,
                                                'nat_type' => $renta_nat_type_id,
                                            ],
                                        ];

                                        $UserDbController->editItem($optionsNaturaPercent);
                                    } else {
                                        $UserDbPaymentsController->addItem($naturaOptions);
                                    }
                                }
                            }
                        }

                        $options = [
                            'tablename' => $UserDbController->DbHandler->tablePayments,
                            'mainData' => [
                                'amount' => number_format($oldAmount + $totalAmountByPaymentPercent, 2, '.', ''),
                            ],
                            'where' => [
                                'id' => $recordID,
                            ],
                        ];

                        $UserDbController->editItem($options);
                    }
                }
            } else {
                unset($owner);
                foreach ($data->owner_array as $i => $owner) {
                    $rkoKey = $data->owner_array[$i][$data->rko_key];
                    $rkoNumber = $inputData['payment_numbering'][$rkoKey];

                    // assign variables for easy use
                    $options = [
                        'tablename' => $UserDbController->DbHandler->tablePayments,
                        'mainData' => [
                            'owner_id' => $owner['owner_id'],
                            'contract_id' => $owner['contract_id'],
                            'paid_from' => self::PAYMENT_NATURA,
                            'paid_in' => self::PAYMENT_MONEY,
                            'transaction_id' => $transactionID,
                            'date' => $inputData['np_date'],
                            'farming_year' => $data->year,
                            'farming_id' => $inputData['farming_id'],
                            'path' => $owner['path'],
                            'is_heritor' => $owner['is_heritor'] ? 'TRUE' : 'FALSE',
                            'rko_number' => $rkoNumber,
                        ],
                    ];

                    $options['mainData']['amount'] = array_sum($owner['leva_to_pay']) * $amount_coef;

                    $recordID = $UserDbPaymentsController->addItem($options);
                    $UsersController->groupLog(
                        $this->User->Name,
                        $this->User->UserID,
                        $this->User->GroupID,
                        $this->module,
                        $this->service_id,
                        __METHOD__,
                        $options['mainData'],
                        ['payment_id' => $recordID],
                        'Add payment'
                    );

                    $naturaOptions = [
                        'tablename' => $UserDbController->DbHandler->tableNaturaPayments,
                        'mainData' => [
                            'payment_id' => $recordID,
                        ],
                    ];

                    // qty to pay for owner and type
                    foreach ($owner['qty_to_pay'] as $natID => $qtObj) {
                        $naturaOptions['mainData']['nat_type'] = $natID;
                        $naturaOptions['mainData']['unit_value'] = $this->naturaArray[$natID]['unit_value'];
                        $naturaOptions['mainData']['amount'] = $qtObj * $amount_coef;

                        $naturaPaymentID = $UserDbPaymentsController->addItem($naturaOptions);
                        $UsersController->groupLog(
                            $this->User->Name,
                            $this->User->UserID,
                            $this->User->GroupID,
                            $this->module,
                            $this->service_id,
                            __METHOD__,
                            $naturaOptions['mainData'],
                            ['natura_payment_id' => $naturaPaymentID],
                            'Add natura payment'
                        );
                    }
                }
            }
        } elseif ($inputData['np_type_natura']) {
            // create transaction options

            $options = [
                'tablename' => $UserDbController->DbHandler->tableTransactions,
                'mainData' => [
                    'date' => $inputData['np_date'],
                    'paid_from' => self::PAYMENT_NATURA,
                    'paid_in' => self::PAYMENT_NATURA,
                    'amount' => 0,
                    'recipient' => $inputData['np_recipient'],
                    'recipient_egn' => $inputData['np_recipient_egn'],
                    'recipient_proxy' => $inputData['np_recipient_proxy'],
                    'recipient_address' => $inputData['np_recipient_address'],
                    'recipient_lk' => $inputData['np_recipient_lk'],
                    'amount_nat' => 0 * $amount_coef,
                    'payment_nat_type' => 1,
                    'farming_year' => $data->year,
                    'payer_name' => $this->User->Name,
                    'status' => true,
                ],
            ];

            $transactionOptions = [
                'tablename' => $UserDbController->DbHandler->tableNaturaTransactions,
                'mainData' => [
                    'paid_from' => self::PAYMENT_NATURA,
                    'paid_in' => self::PAYMENT_NATURA,
                ],
            ];

            $transactionID = $UserDbPaymentsController->addItem($options);
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options['mainData'], ['transaction_id' => $transactionID], 'Add transaction');
            $rentaNatTypeIdCount = count($data->renta_nat_type_id);
            // one transaction to store the total amount of the natura payment, for each natura kind
            for ($j = 0; $j < $rentaNatTypeIdCount; $j++) {
                $transactionOptions['mainData']['transaction_id'] = $transactionID;
                $transactionOptions['mainData']['nat_type'] = $data->renta_nat_type_id[$j];
                $transactionOptions['mainData']['amount'] = $data->nat_paid_amount[$j] * $amount_coef;
                $transactionNaturaID = $UserDbPaymentsController->addItem($transactionOptions);
                $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $transactionOptions['mainData'], ['transaction_natura_id' => $transactionNaturaID], 'Add natura transaction');
            }

            // get renta types
            $options = [
                'tablename' => $UserDbController->DbHandler->tableRentaTypes,
            ];

            $renta_results = $UserDbController->getItemsByParams($options, false, false);
            $rentaResCount = count($renta_results);
            // create renta types array with unit value
            for ($i = 0; $i < $rentaResCount; $i++) {
                $this->renta_types[$renta_results[$i]['id']] = (float)$renta_results[$i]['unit_value'];
            }

            if ('owner_payments' == $data->payment_type) {
                // check if natura left
                $leftNatura = $this->leftPositiveValueInArray($data->nat_paid_amount);
                $natPaidAmountCount = count($data->nat_paid_amount);
                if (!$leftNatura) {
                    $ownerArea = 0;
                    // sum owner_area
                    for ($k = 0; $k < $ownersCount; $k++) {
                        $ownerArea += $data->owner_array[$k]['owner_area'];
                    }

                    // if is left natura, pay by owner_area percent
                    for ($i = 0; $i < $ownersCount; $i++) {
                        // check if owner haven't area
                        if ($data->owner_array[$i]['owner_area'] <= 0) {
                            continue;
                        }

                        $ownerID = $data->owner_array[$i]['owner_id'];
                        $contractID = $data->owner_array[$i]['contract_id'];
                        $farmingID = $data->owner_array[$i]['farming_id'];
                        $path = $data->owner_array[$i]['path'];
                        $unpaidNatura = $data->owner_array[$i]['unpaid_renta_nat'];
                        $isHeritor = $data->owner_array[$i]['is_heritor'];

                        $options = [
                            'tablename' => $UserDbController->DbHandler->tablePayments,
                            'mainData' => [
                                'owner_id' => $ownerID,
                                'contract_id' => $contractID,
                                'farming_id' => $farmingID,
                                'paid_from' => self::PAYMENT_NATURA,
                                'paid_in' => self::PAYMENT_NATURA,
                                'transaction_id' => $transactionID,
                                'date' => $inputData['np_date'],
                                'farming_year' => $data->year,
                                'path' => $path,
                                'is_heritor' => $isHeritor,
                                'amount' => 0,
                                'amount_nat' => 0,
                                'rko_number' => null, // NO RKO FOR NATURA PAYMENT TYPE
                            ],
                        ];

                        $recordID = $UserDbPaymentsController->addItem($options);
                        $naturaOptions = [
                            'tablename' => $UserDbController->DbHandler->tableNaturaPayments,
                            'mainData' => [
                                'payment_id' => $recordID,
                            ],
                        ];

                        for ($j = 0; $j < $natPaidAmountCount; $j++) {
                            $paymentAmount = (float)$data->nat_paid_amount[$j];
                            $unpaidNat = (float)$unpaidNatura[$j];

                            if (null != $paymentAmount && $unpaidNat >= 0) {
                                if ($paymentAmount < 0) {
                                    $paymentAmount = 0;
                                }

                                $paymentPercent = $paymentAmount / $ownerArea;
                                $paymentAmountPerUser = $data->owner_array[$i]['owner_area'] * $paymentPercent;
                                $naturaOptions['mainData']['nat_type'] = $data->renta_nat_type_id[$j];
                                $naturaOptions['mainData']['unit_value'] = $this->renta_types[$data->renta_nat_type_id[$j]];
                                $naturaOptions['mainData']['amount'] = number_format($paymentAmountPerUser, 4, '.', '') * $amount_coef;

                                if ($paymentAmountPerUser <= 0) {
                                    continue;
                                }

                                $UserDbPaymentsController->addItem($naturaOptions);
                            }
                        }
                    }
                }
            } else {
                $rentaNatTypeIdCount = count($data->renta_nat_type_id);
                $area_total = array_sum(array_column($data->owner_array, 'owner_area'));
                $totalNatRenta = [];
                foreach ($data->owner_array as $index => $owner) {
                    foreach ($owner['unpaid_renta_nat'] as $natKey => $natValue) {
                        if (!isset($totalNatRenta[$natKey])) {
                            $totalNatRenta[$natKey] = $natValue;
                        } else {
                            $totalNatRenta[$natKey] += $natValue;
                        }
                    }
                }

                for ($i = 0; $i < $ownersCount; $i++) {
                    // assign variables for easy use
                    $contractID = $data->owner_array[$i]['contract_id'];
                    $ownerID = $data->owner_array[$i]['owner_id'];
                    $path = $data->owner_array[$i]['path'];
                    $isHeritor = $data->owner_array[$i]['is_heritor'] ? 'TRUE' : 'FALSE';

                    $options = [
                        'tablename' => $UserDbController->DbHandler->tablePayments,
                        'mainData' => [
                            'owner_id' => $ownerID,
                            'contract_id' => $contractID,
                            'farming_id' => $inputData['farming_id'],
                            'paid_from' => self::PAYMENT_NATURA,
                            'paid_in' => self::PAYMENT_NATURA,
                            'amount' => 0,
                            'amount_nat' => 0,
                            'transaction_id' => $transactionID,
                            'date' => $inputData['np_date'],
                            'farming_year' => $data->year,
                            'path' => $path,
                            'is_heritor' => $isHeritor,
                            'rko_number' => null, // NO RKO FOR NATURA PAYMENT TYPE
                        ],
                    ];

                    $recordID = $UserDbPaymentsController->addItem($options);
                    $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options['mainData'], ['payment_id' => $recordID], 'Add payment');

                    $naturaOptions = [
                        'tablename' => $UserDbController->DbHandler->tableNaturaPayments,
                        'mainData' => [
                            'payment_id' => $recordID,
                        ],
                    ];
                    // insert payments records in su_payments_natura for each natura id/kind
                    for ($j = 0; $j < $rentaNatTypeIdCount; $j++) {
                        $natTypeId = $data->renta_nat_type_id[$j];
                        $naturaOptions['mainData']['nat_type'] = $natTypeId;
                        $naturaOptions['mainData']['unit_value'] = $this->renta_types[$natTypeId];

                        $contractNatValue = $data->owner_array[$i]['renta_nat'][$natTypeId];
                        $chargedNatValue = $data->owner_array[$i]['charged_renta_nat_values'][$natTypeId];
                        $natValue = $contractNatValue + $chargedNatValue;
                        $percent = $data->owner_array[$i]['unpaid_renta_nat'][$natTypeId] / ($totalNatRenta[$natTypeId] ?? 1);

                        if (0 == $data->owner_array[$i]['unpaid_renta_nat'][$natTypeId]) {
                            $percent = 0;
                        }

                        $naturaOptions['mainData']['amount'] = $data->nat_paid_amount[$j] * $percent * $amount_coef;

                        $naturaPaymentID = $UserDbPaymentsController->addItem($naturaOptions);
                        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $naturaOptions['mainData'], ['natura_payment_id' => $naturaPaymentID], 'Add natura payment');
                    }
                }
            }
        }

        $this->updateFarmingStartRko((array) $data, $inputData);

        // setResponseData
        if ($inputData['payment_order'] && $inputData['np_type_money']) {
            $response = [
                'payment_type' => $inputData['np_method_cash'] ? 'cash' : 'bank',
                'transaction_id' => $transactionID,
            ];

            return $response;
        }
        if ($inputData['weighing_note'] && $inputData['np_type_natura']) {
            $response = [
                'payment_type' => 'natura',
                'transaction_id' => $transactionID,
            ];

            return $response;
        }

        return 0;
    }

    /**
     * Save personal use payment.
     *
     * @api-method savePersonalUsePayment
     *
     * @param array $data
     *                    {
     *                    #item array owner_array with data for owner
     *                    #item string year
     *                    #item boolean payment_reduce
     *                    }
     * @param array $inputData
     *                         {
     *                         #item boolean payment_type_money
     *                         #item boolean payment_type_natura
     *                         #item boolean payment_method_cash
     *                         #item boolean payment_method_bank
     *                         #item boolean payment_order
     *                         #item boolean weighing_note
     *                         #item string payment_amount
     *                         #item string payment_date
     *                         #item string payment_natura_type
     *                         #item string payment_natura_price
     *                         #item string payment_natura_amount
     *                         #item string payment_recipient
     *                         #item string payment_recipient_egn
     *                         #item string payment_recipient_proxy
     *                         #item string payment_bank_account
     *                         }
     *
     * @throws MTRpcException
     *
     * @return array|int -If Integer the response is 0.
     *{
     *     #item string payment_type     -Cash or bank
     *     #item string transaction_id   -Transaction id
     *}
     */
    public function savePersonalUsePayment($data, $inputData)
    {
        $UsersController = new UsersController('Users');
        $UserDbController = new UserDbController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        $this->validatePaymentDate($data, $inputData['np_date']);

        $data = (object)$data;

        // check if data is correct
        if (!$data->year || !(int)$data->year || 0 == count($data->owner_array)) {
            return [];
        }

        // if we request rko number generation but we dont pass a farm id, throw error
        if (empty($data->farming_id) && !empty($data->payment_numbering)) {
            throw new MTRpcException('MISSING_FARMING_ID', -33227);
        }

        // create transaction options
        $options = [
            'tablename' => $UserDbController->DbHandler->tableTransactions,
            'mainData' => [
                'date' => $inputData['np_date'],
                'paid_from' => self::PAYMENT_NATURA,
                'paid_in' => self::PAYMENT_NATURA,
                'amount' => 0,
                'recipient' => $inputData['np_recipient'],
                'recipient_egn' => $inputData['np_recipient_egn'],
                'recipient_proxy' => $inputData['np_recipient_proxy'],
                'recipient_address' => $inputData['np_recipient_address'],
                'recipient_lk' => $inputData['np_recipient_lk'],
                'amount_nat' => 0,
                'payment_nat_type' => 1,
                'farming_year' => $data->year,
                'payer_name' => $this->User->Name,
                'status' => true,
                'type' => TRANSACTION_TYPE_PERSONAL_USE,
            ],
        ];

        $transactionID = $UserDbPaymentsController->addItem($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options['mainData'], ['transaction_id' => $transactionID], 'Add transaction');

        $transactionOptions = [
            'tablename' => $UserDbController->DbHandler->tableNaturaTransactions,
            'mainData' => [
                'paid_from' => self::PAYMENT_NATURA,
                'paid_in' => self::PAYMENT_NATURA,
            ],
        ];

        $rentaNatTypeIdCount = count($data->renta_nat_type_id);
        // one transaction to store the total amount of the natura payment, for each natura kind
        for ($j = 0; $j < $rentaNatTypeIdCount; $j++) {
            $transactionOptions['mainData']['transaction_id'] = $transactionID;
            $transactionOptions['mainData']['nat_type'] = $data->renta_nat_type_id[$j];
            $transactionOptions['mainData']['amount'] = $data->nat_paid_amount[$j];
            $transactionNaturaID = $UserDbPaymentsController->addItem($transactionOptions);
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $transactionOptions['mainData'], ['transaction_natura_id' => $transactionNaturaID], 'Add natura transaction');
        }

        // get renta types
        $options = [
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
        ];

        $renta_results = $UserDbController->getItemsByParams($options, false, false);
        $rentaResCount = count($renta_results);
        // create renta types array with unit value
        for ($i = 0; $i < $rentaResCount; $i++) {
            $this->renta_types[$renta_results[$i]['id']] = (float)$renta_results[$i]['unit_value'];
        }

        $rentaNatTypeIdCount = count($data->renta_nat_type_id);

        $ownersCount = count($data->owner_array);
        for ($i = 0; $i < $ownersCount; $i++) {
            // assign variables for easy use
            $contractID = $data->owner_array[$i]['contract_id'];
            $ownerID = $data->owner_array[$i]['owner_id'];
            $path = $data->owner_array[$i]['path'];
            $isHeritor = $data->owner_array[$i]['is_heritor'] ? 'TRUE' : 'FALSE';

            $options = [
                'tablename' => $UserDbController->DbHandler->tablePayments,
                'mainData' => [
                    'owner_id' => $ownerID,
                    'contract_id' => $contractID,
                    'farming_id' => $inputData['farming_id'],
                    'paid_from' => self::PAYMENT_NATURA,
                    'paid_in' => self::PAYMENT_NATURA,
                    'amount' => 0,
                    'amount_nat' => 0,
                    'transaction_id' => $transactionID,
                    'date' => $inputData['np_date'],
                    'farming_year' => $data->year,
                    'path' => $path,
                    'is_heritor' => $isHeritor,
                    'rko_number' => null, // NO RKO FOR PERSONAL USE PAYMENT TYPE,
                ],
            ];

            $recordID = $UserDbPaymentsController->addItem($options);
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options['mainData'], ['payment_id' => $recordID], 'Add payment');

            $naturaOptions = [
                'tablename' => $UserDbController->DbHandler->tableNaturaPayments,
                'mainData' => [
                    'payment_id' => $recordID,
                ],
            ];

            // insert payments records in su_payments_natura for each natura id/kind
            for ($j = 0; $j < $rentaNatTypeIdCount; $j++) {
                $naturaOptions['mainData']['nat_type'] = $data->renta_nat_type_id[$j];
                $naturaOptions['mainData']['unit_value'] = $data->renta_nat_unit_value[$j];
                $naturaOptions['mainData']['amount'] = $data->nat_paid_amount[$j];
                $naturaPaymentID = $UserDbPaymentsController->addItem($naturaOptions);
                $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $naturaOptions['mainData'], ['natura_payment_id' => $naturaPaymentID], 'Add natura payment');
            }
        }

        return [
            'payment_type' => 'natura',
            'transaction_id' => $transactionID,
        ];
    }

    /**
     * Generate RKO numbers.
     *
     * @api-method generateRkoNumbers
     *
     * @param array $data
     *                    {
     *                    #item array owner_array with data for owner
     *                    {
     *                    #item string owner_id
     *                    #item int contract_id
     *                    #item string owner_area
     *                    #item int unpaid_renta
     *                    #item boolean is_heritor
     *                    }
     *                    #item string year
     *                    #item boolean payment_reduce
     *                    }
     * @param array $inputData
     *                         {
     *                         #item boolean payment_type_money
     *                         #item boolean payment_type_natura
     *                         #item boolean payment_method_cash
     *                         #item boolean payment_method_bank
     *                         #item boolean payment_order
     *                         #item boolean weighing_note
     *                         #item string payment_amount
     *                         #item string payment_date
     *                         #item string payment_natura_type
     *                         #item string payment_natura_price
     *                         #item string payment_natura_amount
     *                         #item string payment_recipient
     *                         #item string payment_recipient_egn
     *                         #item string payment_recipient_proxy
     *                         #item string payment_bank_account
     *                         }
     *
     * @return array
     *{
     *     #item string -new rko id
     *}
     */
    public function generateRkoNumbers($data, $inputData)
    {
        $ownersCount = count($data['owner_array']);
        $contractsCount = count($data['contract_array']);

        if ($data['payment_reduce']) {
            $amountCoef = -1;
        } else {
            $amountCoef = 1;
        }
        // check if data is correct
        if (0 == count($data['owner_array']) || !$data['year'] || !(int)$data['year']) {
            return [];
        }

        // flags show if data is correct
        $ownerFlag = true;
        $contractFlag = true;

        // check if all owner IDs are integer
        for ($i = 0; $i < $ownersCount; $i++) {
            if (!(int)$data['owner_array'][$i]) {
                $ownerFlag = false;
            }

            if (
                !isset($data['rko_key'])
                || !isset($data['owner_array'][$i][$data['rko_key']])
            ) {
                $ownerFlag = false;
            }
        }

        // check if all contract IDs are integer
        for ($i = 0; $i < $contractsCount; $i++) {
            if (!(int)$data['contract_array'][$i]) {
                $contractFlag = false;
            }
        }

        // if info is incorrect or corrupt - exit payment saving
        if (false === $contractFlag && false === $ownerFlag) {
            return [];
        }

        /** @var array $farmingPayments - array used to determine for each farm for which record there should be a payment  */
        $farmingPayments = [];

        /** @var string $rkoKey - this variable determines which property from owner_array to use as key in the result array
         */
        $rkoKey = $data['rko_key'];

        for ($i = 0; $i < $ownersCount; $i++) {
            $farmingId = 'owner_payments' == $data['payment_type']
                ? $data['owner_array'][$i]['farming_id']
                : $inputData['farming_id'];

            $farmingPaymentKey = $data['owner_array'][$i][$rkoKey];
            $farmingPayments[$farmingId][$farmingPaymentKey] = false;
        }

        if ($inputData['payment_type_money']) {
            if ('owner_payments' == $data['payment_type']) {
                $paymentAmount = $inputData['payment_amount'];

                // if user is owner, his data is on the first position of $data['owner_array']
                for ($i = 0; $i < $ownersCount; $i++) {
                    if ($paymentAmount <= 0) {
                        break;
                    }

                    $unpaidRenta = $data['owner_array'][$i]['unpaid_renta'];

                    if ($unpaidRenta > $paymentAmount) {
                        $rentaToPaid = $paymentAmount;
                    } else {
                        $rentaToPaid = $unpaidRenta;
                    }

                    $paymentAmount -= $unpaidRenta;

                    if ($rentaToPaid > 0) {
                        $farmingId = $data['owner_array'][$i]['farming_id'];
                        $farmingPaymentKey = $data['owner_array'][$i][$rkoKey];
                        $farmingPayments[$farmingId][$farmingPaymentKey] = true;
                    }
                }

                if ($paymentAmount > 0) {
                    $ownerArea = 0;
                    for ($i = 0; $i < $ownersCount; $i++) {
                        $ownerArea += $data['owner_array'][$i]['owner_area'];
                    }

                    $paymentPercent = $paymentAmount / $ownerArea;
                    for ($i = 0; $i < $ownersCount; $i++) {
                        $paymentAmountPerUser = $data['owner_array'][$i]['owner_area'] * $paymentPercent;

                        if ($paymentAmountPerUser <= 0) {
                            continue;
                        }

                        $farmingId = $data['owner_array'][$i]['farming_id'];
                        $farmingPaymentKey = $data['owner_array'][$i][$rkoKey];
                        $farmingPayments[$farmingId][$farmingPaymentKey] = true;
                    }
                }
            } else {
                for ($i = 0; $i < $ownersCount; $i++) {
                    $farmingId = $inputData['farming_id'];
                    $farmingPaymentKey = $data['owner_array'][$i][$rkoKey];
                    $farmingPayments[$farmingId][$farmingPaymentKey] = true;
                }
            }
        } elseif ($inputData['payment_type_natura']) {
            // create transaction options
            if ('owner_payments' == $data['payment_type']) {
                for ($i = 0; $i < $ownersCount; $i++) {
                    $farmingId = $data['owner_array'][$i]['farming_id'];
                    $farmingPaymentKey = $data['owner_array'][$i][$rkoKey];
                    $farmingPayments[$farmingId][$farmingPaymentKey] = true;
                }
            } else {
                for ($i = 0; $i < $ownersCount; $i++) {
                    $farmingId = $inputData['farming_id'];
                    $farmingPaymentKey = $data['owner_array'][$i][$rkoKey];
                    $farmingPayments[$farmingId][$farmingPaymentKey] = true;
                }
            }
        }

        $farmingIds = 'owner_payments' == $data['payment_type']
            ? array_unique(array_column($data['owner_array'], 'farming_id'))
            : [$inputData['farming_id']];
        $combinePaymentDocument = $data['combine_payment_document'] && 1 === count($farmingIds);

        return $this->generateRkoNumbersFromFarmingPayments($farmingPayments, $combinePaymentDocument);
    }

    /**
     * Generate new natura payment RKO numbers.
     *
     * @api-method generateRkoNatNumbers
     *
     * @param array $data
     *                    {
     *                    #item array owner_array with data for owner
     *                    #item string year
     *                    #item boolean payment_reduce
     *                    }
     * @param array $inputData
     *                         {
     *                         #item boolean payment_type_money
     *                         #item boolean payment_type_natura
     *                         #item boolean payment_method_cash
     *                         #item boolean payment_method_bank
     *                         #item boolean payment_order
     *                         #item boolean weighing_note
     *                         #item string payment_amount
     *                         #item string payment_date
     *                         #item string payment_natura_type
     *                         #item string payment_natura_price
     *                         #item string payment_natura_amount
     *                         #item string payment_recipient
     *                         #item string payment_recipient_egn
     *                         #item string payment_recipient_proxy
     *                         #item string payment_bank_account
     *                         }
     *
     * @return array
     *{
     *     #item string -new rko id
     *}
     */
    public function generateRkoNatNumbers($data, $inputData)
    {
        $data = (object)$data;

        if ($data->payment_reduce) {
            $amountCoef = -1;
        } else {
            $amountCoef = 1;
        }
        $ownersCount = count($data->owner_array);
        $contractsCount = count($data->contract_array);
        // check if data is correct
        if (0 == $ownersCount || !$data->year || !(int) $data->year) {
            return [];
        }

        $ownerFlag = true;
        $contractFlag = true;

        for ($i = 0; $i < $ownersCount; $i++) {
            if (!(int) $data->owner_array[$i]) {
                $ownerFlag = false;
            }

            if (
                !isset($data->rko_key)
                || !isset($data->owner_array[$i][$data->rko_key])
            ) {
                $ownerFlag = false;
            }
        }

        for ($i = 0; $i < $contractsCount; $i++) {
            if (!(int) $data->contract_array[$i]) {
                $contractFlag = false;
            }
        }

        if (false === $contractFlag && false === $ownerFlag) {
            return [];
        }

        /** @var array $farmingPayments - array used to determine for each farm for which record there should be a payment  */
        $farmingPayments = [];

        /** @var string $rkoKey - this variable determines which property from owner_array to use as key in the result array
         */
        $rkoKey = $data->rko_key;

        for ($i = 0; $i < $ownersCount; $i++) {
            $farmingId = 'owner_payments' == $data->payment_type
                ? $data->owner_array[$i]['farming_id']
                : $inputData['farming_id'];

            $farmingPaymentKey = $data->owner_array[$i][$rkoKey];
            $farmingPayments[$farmingId][$farmingPaymentKey] = false;
        }

        // check existing due renta
        for ($m = 0; $m < $ownersCount; $m++) {
            $owner = $data->owner_array[$m];

            $rentaNats = explode('</br>', $owner['renta_nat']);
            $chargedRentaNats = explode('</br>', $owner['charged_renta_nat']);
            $haveRentaNats = false;
            $haveChargedRentaNats = false;

            $chargedRentaNatCount = count($chargedRentaNats);
            for ($n = 0; $n < $chargedRentaNatCount; $n++) {
                $chargedRentaNat = $chargedRentaNats[$n];
                $rentaNat = trim($rentaNats[$n]);

                if ('' == $chargedRentaNat || '' == $rentaNat) {
                    continue;
                }

                if ('-' != $chargedRentaNat) {
                    $haveChargedRentaNats = true;

                    break;
                }
                if ($rentaNat > 0) {
                    $haveRentaNats = true;

                    break;
                }
            }

            if ($haveChargedRentaNats || $haveRentaNats) {
                break;
            }
        }

        if (!$haveChargedRentaNats && !$haveRentaNats) {
            $message = 'Не са открити дължими стойности за избрания тип плащане. Няма да бъде създадена транзакция.';

            return ['message' => $message];
        }

        $paid_renta_per_nat_type = [];

        if ($inputData['np_type_money']) {
            $paid_total = 0;
            $rentaNatTypeIdCount = count($data->renta_nat_type_id);

            for ($i = 0; $i < $rentaNatTypeIdCount; $i++) {
                if ('' == $data->renta_nat_price_per_unit[$i] || '' == $data->renta_nat_total[$i]) {
                    unset($data->renta_nat_price_per_unit[$i], $data->renta_nat_total[$i]);

                    continue;
                }

                $paid_renta_per_nat_type[$i] = (0 == $data->renta_nat_price_per_unit[$i]) ? 0
                    : number_format($data->renta_nat_total[$i] / $data->renta_nat_price_per_unit[$i], 3, '.', '');
                $paid_total += $data->renta_nat_total[$i];
            }

            if ('owner_payments' == $data->payment_type) {
                // if user is owner, his data is on the first position of $data->owner_array
                for ($i = 0; $i < $ownersCount; $i++) {
                    // check if natura left
                    $leftNatura = $this->leftPositiveValueInArray($paid_renta_per_nat_type);

                    if ($leftNatura) {
                        break;
                    }

                    // check if owner haven't area
                    if ($data->owner_array[$i]['owner_area'] <= 0) {
                        continue;
                    }

                    $unpaidNatura = explode('</br>', $data->owner_array[$i]['unpaid_renta_nat']);

                    $unpaidNatSum = 0;
                    $paidRentaPerNatTypeCount = count($paid_renta_per_nat_type);
                    for ($k = 0; $k < $paidRentaPerNatTypeCount; $k++) {
                        $unpaidNatSum += (float)$unpaidNatura[$k];
                    }

                    if (0 == $unpaidNatSum) {
                        continue;
                    }

                    $farmingId = $data->owner_array[$i]['farming_id'];
                    $farmingPaymentKey = $data->owner_array[$i][$rkoKey];
                    $farmingPayments[$farmingId][$farmingPaymentKey] = true;

                    for ($j = 0; $j < $paidRentaPerNatTypeCount; $j++) {
                        $paymentAmount = number_format($paid_renta_per_nat_type[$j], 3, '.', '');
                        $unpaidNat = number_format($unpaidNatura[$j], 3, '.', '');

                        if (null != $paymentAmount && $unpaidNat >= 0) {
                            if ($paymentAmount < 0) {
                                continue;
                            }

                            $naturaOptions['mainData']['nat_type'] = $data->renta_nat_type_id[$j];
                            $naturaOptions['mainData']['unit_value'] = $data->renta_nat_price_per_unit[$j];

                            $naturaAmount = 0;
                            if ($unpaidNat > $paid_renta_per_nat_type[$j]) {
                                $naturaAmount = $paymentAmount;
                            } else {
                                $naturaAmount = $unpaidNat;
                            }

                            $naturaOptions['mainData']['amount'] = $naturaAmount * $amountCoef;

                            $paid_renta_per_nat_type[$j] -= $unpaidNat;
                        }
                    }
                }

                // check if natura left
                $leftNatura = $this->leftPositiveValueInArray($paid_renta_per_nat_type);

                if (!$leftNatura) {
                    $ownerArea = 0;

                    // sum owner_area
                    for ($k = 0; $k < $ownersCount; $k++) {
                        $ownerArea += $data->owner_array[$k]['owner_area'];
                    }

                    // if is left natura, pay by owner_area percent
                    for ($i = 0; $i < $ownersCount; $i++) {
                        // check if owner haven't area
                        if ($data->owner_array[$i]['owner_area'] <= 0) {
                            continue;
                        }

                        $farmingId = $data->owner_array[$i]['farming_id'];
                        $farmingPaymentKey = $data->owner_array[$i][$rkoKey];
                        $farmingPayments[$farmingId][$farmingPaymentKey] = true;
                    }
                }
            } else {
                $charged_renta = 0;

                for ($i = 0; $i < $ownersCount; $i++) {
                    $charged_renta += $data->owner_array[$i]['charged_renta_nat'];
                }

                $farmingId = $inputData['farming_id'];

                for ($i = 0; $i < $ownersCount; $i++) {
                    $farmingPaymentKey = $data->owner_array[$i][$rkoKey];
                    $farmingPayments[$farmingId][$farmingPaymentKey] = true;
                }
            }
        } elseif ($inputData['np_type_natura']) {
            // create transaction options

            if ('owner_payments' == $data->payment_type) {
                // if user is owner, his data is on the first position of $data->owner_array
                for ($i = 0; $i < $ownersCount; $i++) {
                    // check if natura left
                    $leftNatura = $this->leftPositiveValueInArray($data->nat_paid_amount);

                    if ($leftNatura) {
                        break;
                    }

                    // check if owner haven't area
                    if ($data->owner_array[$i]['owner_area'] <= 0) {
                        continue;
                    }
                    $unpaidNatura = $data->owner_array[$i]['unpaid_renta_nat'];

                    $unpaidNatSum = 0;
                    $paidRentaPerNatTypeCount = count($paid_renta_per_nat_type);

                    for ($k = 0; $k < $paidRentaPerNatTypeCount; $k++) {
                        $unpaidNatSum += (float)$unpaidNatura[$k];
                    }

                    if (0 == $unpaidNatSum) {
                        continue;
                    }

                    $farmingPaymentKey = $data->owner_array[$i][$rkoKey];
                    $farmingPayments[$farmingId][$farmingPaymentKey] = true;
                }

                // check if natura left
                $leftNatura = $this->leftPositiveValueInArray($data->nat_paid_amount);

                if (!$leftNatura) {
                    $ownerArea = 0;

                    // sum owner_area
                    for ($k = 0; $k < $ownersCount; $k++) {
                        $ownerArea += $data->owner_array[$k]['owner_area'];
                    }

                    // if is left natura, pay by owner_area percent
                    for ($i = 0; $i < $ownersCount; $i++) {
                        // check if owner haven't area
                        if ($data->owner_array[$i]['owner_area'] <= 0) {
                            continue;
                        }

                        $farmingPaymentKey = $data->owner_array[$i][$rkoKey];
                        $farmingPayments[$farmingId][$farmingPaymentKey] = true;
                    }
                }
            } else {
                for ($i = 0; $i < $ownersCount; $i++) {
                    $farmingPaymentKey = $data->owner_array[$i][$rkoKey];
                    $farmingPayments[$farmingId][$farmingPaymentKey] = true;
                }
            }
        }

        return $this->generateRkoNumbersFromFarmingPayments($farmingPayments);
    }

    /**
     * Set the number of the last payment to in order to start new numbering sequence.
     *
     * @param array $rpcParams
     *                         {
     *                         #item string id|required farm id
     *                         #item string name The farm name
     *                         #item string rko_number|required starting RKO number
     *                         }
     */
    public function setRkoNumberingStart($rpcParams)
    {
        $FarmingController = new FarmingController('Farming');
        foreach ($rpcParams['rows'] as $index => $rpcParam) {
            $FarmingController->updateFarmingStartRko($rpcParam['value'], $rpcParam['farming_id']);
        }
    }

    /**
     * Return the last rko payment number or starting rko by farm.
     *
     * @param array $farmingIds
     *
     * @return array
     */
    public function getLastNumber($farmingIds = [])
    {
        $farmsRko = $this->getUserFarms($farmingIds);
        foreach ($farmsRko as $index => &$farm) {
            $farm['rko_from_settings'] = true;
            $farm['group'] = 'Farms_Rko';
            $farm['editor'] = 'text';
            $farm['value'] = $farm['rko_number'];
        }

        return ['total' => count($farmsRko), 'rows' => $farmsRko];
    }

    protected function getUserFarms($farmingIds = [])
    {
        $farmController = new FarmingController('Farming');
        $userFarmings = $farmController->getUserFarmings();
        $userFarmingIds = array_keys($userFarmings);
        $farmingIds = (new ArrayHelper())->filterEmptyStringArr($farmingIds);
        $farmingIds = isset($farmingIds) && count($farmingIds) > 0
            ? array_intersect($farmingIds, $userFarmingIds)
            : $userFarmingIds;

        $options = [
            'return' => ['id as farming_id', 'name', 'rko_number'],
            'where' => [
                'id' => ['column' => 'id', 'compare' => 'IN', 'value' => $farmingIds],
            ],
        ];

        return $farmController->getFarmings($options);
    }

    /**
     * @return int|string
     */
    protected function setNextRkoNumber($lastNumber)
    {
        if (is_numeric($lastNumber)) {
            $newRKONum = $lastNumber + 1;
            $newRKONum .= '';
        } else {
            $matches = [];
            preg_match('/(\d+)$/', $lastNumber, $matches);
            if (count($matches) > 0) {
                $lastRkoTxtPart = substr($lastNumber, 0, -strlen($matches[0]));
                $incrementedNum = $matches[1] + 1;
                $newRKONum = $lastRkoTxtPart . $incrementedNum;
            } else {
                $newRKONum = $lastNumber . 1;
            }
        }

        return $newRKONum;
    }

    /**
     * @param array $rko_numbers
     */
    protected function getNextRkoNumber($rko_numbers)
    {
        $biggestRko[] = 0;
        foreach ($rko_numbers as $rko_number) {
            preg_match('/(\D+)?(\d+\z)/', $rko_number, $matches);
            if (!empty($matches) && $matches[2] > max(array_keys($biggestRko))) {
                $biggestRko = []; // reset the array because we need to keep only the biggest value
                $biggestRko[$matches[2]] = $matches[1] . ($matches[2] + 1); // keeps the rko integer as array key for comparison with the next values
            }
        }

        return max($biggestRko);
    }

    /**
     * check if left positive values in array.
     *
     * @param array $paid_renta_per_nat_type
     *
     * @return bool
     */
    private function leftPositiveValueInArray($paid_renta_per_nat_type)
    {
        $countPaidRenta = count($paid_renta_per_nat_type);

        for ($i = 0; $i < $countPaidRenta; $i++) {
            $paidRenta = $paid_renta_per_nat_type[$i];

            if ($paidRenta > 0) {
                return false;
            }
        }

        return true;
    }

    /**
     * @return array
     */
    private function hasChargedRentOrNaturaRent($data)
    {
        $haveChargedRentaNats = false;
        $haveRentaNats = false;

        foreach ($data->owner_array as $m => $owner) {
            $chargedRentaNats = explode('</br>', $owner['charged_renta_nat']);
            if (is_array($owner['renta_nat'])) {
                $rentaNats = $owner['renta_nat'];
            } else {
                $rentaNats = explode('</br>', $owner['renta_nat']);
            }

            $n = 0;
            foreach ($rentaNats as $naturaId => $rentaNat) {
                $chargedRentaNat = $chargedRentaNats[$n];
                $rentaN = trim($rentaNats[$naturaId]);
                if ('' == $chargedRentaNat || '' == $rentaN) {
                    continue;
                }
                if ('-' != $chargedRentaNat) {
                    $haveChargedRentaNats = true;

                    break;
                }
                if ($rentaN > 0) {
                    $haveRentaNats = true;

                    break;
                }
                $n++;
            }
            if ($haveChargedRentaNats || $haveRentaNats) {
                break;
            }
        }

        return [$haveChargedRentaNats, $haveRentaNats];
    }

    /**
     * Validates payment date.
     */
    private function validatePaymentDate(array $data, string $paymentDate): void
    {
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);

        $onwerIds = array_column($data['owner_array'], 'owner_id');
        $owners = $UserDbOwnersController->getOwners($onwerIds);
        $paymentDate = DateTime::createFromFormat('Y-m-d', $paymentDate);

        if (!$paymentDate) {
            throw new MTRpcException('INVALID_PAYMENT_DATE', -33233);
        }

        foreach ($owners as $owner) {
            if (!$owner['dead_date']) {
                continue;
            }

            $deadDate = DateTime::createFromFormat('Y-m-d H:i:s', $owner['dead_date']);
            if ($paymentDate > $deadDate) {
                throw new MTRpcException('INVALID_PAYMENT_DATE', -33233);
            }
        }
    }

    private function validateRkoNumbering(array $rkoNumbers)
    {
        foreach ($rkoNumbers as $rkoNumber) {
            preg_match('/(\D+)?(\d+\z)/', $rkoNumber, $matches);
            if (!$matches) {
                throw new MTRpcException('WRONG_RKO_NUMBER', -33228);
            }
        }
    }

    private function generateRkoNumbersFromFarmingPayments(array $farmingPayments, bool $combinePaymentDocument = false)
    {
        $result = [];
        $farmingIds = array_keys($farmingPayments);

        $rkoNumbers = $this->getLastNumber($farmingIds);
        foreach ($rkoNumbers['rows'] as $paymentRko) {
            $lastRkoNumber = $paymentRko['rko_number'];
            $farmingId = $paymentRko['farming_id'];

            $rkoCounter = 0;
            foreach ($farmingPayments[$farmingId] as $farmingPaymentKey => $hasPayment) {
                if (!$hasPayment) {
                    continue;
                }

                if ($rkoCounter >= 1 && !$combinePaymentDocument) {
                    $lastRkoNumber = $this->setNextRkoNumber($lastRkoNumber);
                }

                $result[$farmingPaymentKey] = $lastRkoNumber;

                $rkoCounter++;
            }
        }

        return $result;
    }

    private function updateFarmingStartRko(array $data, array $inputData)
    {
        $farmingController = new FarmingController('Farming');

        $rkoNumbersByFarming = [];
        foreach (($data['owner_array'] ?? []) as $ownerData) {
            $farmingId = 'owner_payments' == $data['payment_type']
                ? $ownerData['farming_id']
                : $inputData['farming_id'];
            $rkoKey = $ownerData[$data['rko_key']];

            if (
                !$farmingId
                || !isset($data['rko_key'])
                || !isset($ownerData[$data['rko_key']])
                || !isset($inputData['payment_numbering'][$rkoKey])
            ) {
                continue;
            }

            $rkoNumbersByFarming[$farmingId][] = $inputData['payment_numbering'][$rkoKey];
        }

        // Update the last rko_number for each farming
        foreach ($rkoNumbersByFarming as $farmingId => $rkoNumbers) {
            $biggestRkoNumber = $this->getNextRkoNumber($rkoNumbers);
            if ($biggestRkoNumber) {
                $farmingController->updateFarmingStartRko($biggestRkoNumber, $farmingId);
            }
        }
    }
}
