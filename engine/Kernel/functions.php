<?php

use Prado\Exceptions\TInvalidDataValueException;
use TF\Engine\Kernel\Math;

function getSiteURL()
{
    $port = '';
    if (!isset($_SERVER['API_EXTERNAL_PORT']) && $_SERVER['SERVER_PORT']) {
        $port = ':' . $_SERVER['SERVER_PORT'];
    }
    $url = $_SERVER['REQUEST_SCHEME'] . '://' . $_SERVER['HTTP_HOST'] . $port . dirname($_SERVER['SCRIPT_URL']) . '/';

    return $url;
}

/**
 * @param string $serviceId
 * @param string $rpcApi
 *
 * @throws TInvalidDataValueException
 */
function makeApiClass($serviceId, $rpcApi)
{
    //    PradoApplication::getInstance();
    $appXmlStr = file_get_contents(__DIR__ . '/../../protected/application.xml');
    $xml = new \SimpleXMLElement($appXmlStr);
    $rpcApiEl = $xml->xpath("/application/services/service[@id='{$serviceId}']/rpcapi[@id='{$rpcApi}']");
    $attrs = $rpcApiEl[0]->attributes();
    $className = (string)$attrs['class'];
    \Prado::using('BaseApp.vendor.pradosoft.prado.framework.Web.Services.TRpcService');
    \Prado::using($className);
    $pathEls = array_pop(explode('.', $className));

    $server = new \TRpcServer(new \TJsonRpcProtocol());

    return new $pathEls($server);
}

/**
 * Reurns the sum of the all given hours.
 *
 * @param array $times an array of 'HH:MM:SS'
 *
 * @return string the result is the sum of all hours
 */
function sumHours(array $times)
{
    $times = array_filter($times);
    $seconds = 0; // declare seconds either it gives Notice: Undefined variable
    // loop throught all the times
    foreach ($times as $time) {
        list($hour, $minute, $second) = explode(':', $time);
        $seconds += $hour * 3600;
        $seconds += $minute * 60;
        $seconds += $second;
    }

    $hours = floor($seconds / 3600);
    $minutes = floor(($seconds - ($hours * 3600)) / 60);
    $seconds -= $hours * 3600 + $minutes * 60;

    // returns the time already formatted
    return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
}

/**
 * Zips recusively directory.
 *
 * @param string $source
 * @param string $destination
 */
function zipRecursive($source, $destination)
{
    if (!extension_loaded('zip') || !file_exists($source)) {
        return false;
    }

    $zip = new ZipArchive();
    if (!$zip->open($destination, ZipArchive::CREATE)) {
        return false;
    }

    $source = str_replace('\\', '/', realpath($source));

    if (true === is_dir($source)) {
        $files = new \RecursiveIteratorIterator(new RecursiveDirectoryIterator($source), \RecursiveIteratorIterator::SELF_FIRST);

        foreach ($files as $file) {
            $file = str_replace('\\', '/', $file);

            // Ignore "." and ".." folders
            if (in_array(substr($file, strrpos($file, '/') + 1), ['.', '..'])) {
                continue;
            }

            $file = realpath($file);

            if (true === is_dir($file)) {
                $zip->addEmptyDir(str_replace($source . '/', '', $file . '/'));
            } elseif (true === is_file($file)) {
                $zip->addFromString(str_replace($source . '/', '', $file), file_get_contents($file));
            }
        }
    } elseif (true === is_file($source)) {
        $zip->addFromString(basename($source), file_get_contents($source));
    }

    return $zip->close();
}

/**
 * Downloads file from a given url to a local path.
 *
 * @param string $fromUrl
 * @param string $toLocalPath
 * @param string $prefix a file name prefix
 *
 * @return string
 */
function transferFile($fromUrl, $toLocalPath, $prefix = '')
{
    $content = file_get_contents($fromUrl);
    if (!file_exists($toLocalPath)) {
        mkdir($toLocalPath);
    }
    $fileName = $prefix . basename($fromUrl);
    $fullFilePath = $toLocalPath . '/' . $fileName;
    file_put_contents($fullFilePath, $content);

    return $fileName;
}

/**
 * @param string $fileExt
 */
function logToFile($fileName, $data, $fileExt = 'txt')
{
    $logfile = DEBUG_LOG_PATH . $fileName . '.' . $fileExt;

    if (!file_exists(DEBUG_LOG_PATH)) {
        mkdir(DEBUG_LOG_PATH, 0755, true);
    }

    if (!is_scalar($data)) {
        $data = print_r($data, true);
    }

    file_put_contents($logfile, $data . PHP_EOL . PHP_EOL . PHP_EOL, FILE_APPEND);
}

/**
 * Delete direcotry and all files and directories in it.
 *
 * @param string $dir
 */
function deleteDirectory($dir)
{
    $it = new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS);
    $files = new RecursiveIteratorIterator($it, RecursiveIteratorIterator::CHILD_FIRST);
    foreach ($files as $file) {
        if ($file->isDir()) {
            rmdir($file->getRealPath());
        } else {
            unlink($file->getRealPath());
        }
    }

    rmdir($dir);
}

/**
 * @param array $files
 */
function deleteFiles($files)
{
    foreach ($files as $file) {
        unlink($file);
    }
}

function areFloatsEqual($a, $b)
{
    // Use absolute difference and compare values
    return (bool) (abs($a - $b) < PHP_FLOAT_EPSILON);
}

/**
 * Convert PostgreSql array to PHP array.
 *
 * @return array
 */
function pgToPHParray(string $value)
{
    return explode(',', trim($value, '{}'));
}

/**
 * @throws Exception
 *
 * @return array
 */
function getFarmingYearsInPeriod($start, $end)
{
    $result = [];
    $interval = DateInterval::createFromDateString('1 day');
    $period = new DatePeriod(new DateTime($start), $interval, new DateTime($end));
    foreach ($period as $dt) {
        $fy = calcFarmngYearFromDate($dt);
        if (!array_key_exists($fy['id'], $result)) {
            $result[$fy['id']] = $fy;
        }
    }

    return $result;
}

/**
 * @throws Exception
 */
function getFarmngYearFromDate($date)
{
    if (!$date instanceof \DateTime) {
        $date = new \DateTime($date);
    }

    $year = $date->format('Y');
    if ($date >= new \DateTime($year . '-10-01')) {
        $year++;
    }

    return $GLOBALS['Farming']['years'][$year - $GLOBALS['Farming']['key_from_year_coefficient']];
}

function calcFarmngYearFromDate($date)
{
    if (!$date instanceof \DateTime) {
        $date = new \DateTime($date);
    }

    $year = $farmingYear = $date->format('Y');
    if ($date >= new \DateTime($year . '-10-01')) {
        $farmingYear++;
    }

    $key = $farmingYear - $GLOBALS['Farming']['key_from_year_coefficient'];

    return [
        'id' => $key,
        'title' => $year . ' г.',
        'default' => false,
        'year' => $year,
        'farming_year' => 'Стопанска ' . $year . '/' . $farmingYear . ' г.',
        'farming_year_short' => $year . '/' . $farmingYear . ' г.',
        'start_date' => $year . '-10-01',
        'end_date' => $farmingYear . '-09-30',
    ];
}

function bearerToken(string $header): string
{
    $response = explode('Bearer', $header);

    return trim($response[1]);
}

/**
 * @throws Exception
 */
function getCurrentFarmingYear()
{
    return getFarmngYearFromDate(new \DateTime('now'));
}

function getAmountToRound($number)
{
    $decimalPart = $number - floor($number);

    return 1 - $decimalPart;
}

function fixRounding($number, $perc = 0.01)
{
    $neededToRound = getAmountToRound($number);

    if ($neededToRound <= $perc) {
        return $number + $neededToRound;
    }

    if ($neededToRound > $perc) {
        $roundingDown = 1 - $neededToRound;

        if ($roundingDown <= $perc) {
            return $number - $roundingDown;
        }
    }

    return $number;
}

function BGNtoEURO($amount, $precision = 2)
{
    $rate = 1.95583; // 1 EUR = 1.95583 BGN

    return number_format($amount, $precision) . ' лв. / €' . number_format(Math::div($amount, $rate), $precision);
}

function EUROtoBGN($amount, $precision = 2)
{
    $rate = 1.95583; // 1 EUR = 1.95583 BGN

    return '€' . number_format($amount, $precision) . ' / ' . number_format(Math::mul($amount, $rate), $precision) . ' лв.';
}

function convertBGNtoEURO($amount)
{
    $rate = 1.95583; // 1 EUR = 1.95583 BGN

    return Math::div($amount, $rate);
}

function convertEUROtoBGN($amount)
{
    $rate = 1.95583; // 1 EUR = 1.95583 BGN

    return Math::mul($amount, $rate);
}
