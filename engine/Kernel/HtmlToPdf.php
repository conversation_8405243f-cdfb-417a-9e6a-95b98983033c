<?php

namespace TF\Engine\Kernel;

require_once SITE_PATH . 'engine/External/pdf/HTML2PDF.php';

use External\pdf\HTML2PDF;

class HtmlToPdf
{
    private $_object;

    public function __construct($orientation = 'P', $format = 'A4', $langue = 'en', $unicode = true, $encoding = 'UTF-8', $marges = [5, 5, 5, 8])
    {
        $this->_object = new HTML2PDF($orientation, $format, $langue, $unicode, $encoding, $marges);
        $this->_object->setDefaultFont('freeserif');
        $this->_object->setTestTdInOnePage(false);
    }

    public function setInit($orientation = 'P', $format = 'A4', $langue = 'en', $unicode = true, $encoding = 'UTF-8', $marges = [5, 5, 5, 8])
    {
        $this->_object = new HTML2PDF($orientation, $format, $langue, $unicode, $encoding, $marges);
        $this->_object->setDefaultFont('freeserif');
        $this->_object->setTestTdInOnePage(false);
    }

    public function writeHTML($content)
    {
        if (null != $this->_object) {
            $content = $this->removeTinyTags($content);

            return $this->_object->WriteHTML($content);
        }
    }

    public function output($name = false, $dest = false)
    {
        if (null != $this->_object) {
            return $this->_object->Output($name, $dest);
        }
    }

    public function removeTinyTags($content)
    {
        $content = str_replace('<code>', '', $content);
        $content = str_replace('</code>', '', $content);
        $content = str_replace('<pre>', '<p>', $content);

        return str_replace('</pre>', '</p>', $content);
    }
}
