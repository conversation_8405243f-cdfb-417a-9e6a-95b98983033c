<?php

namespace TF\Engine\Kernel;

use Prado\Web\THttpRequest;
use TF\Application\Common\KeycloakSystemAuth;
use TF\Application\Common\MTSystemAuthManager;

class KeycloakSystemJsonService extends SystemJsonService
{
    public function init($xml)
    {
        parent::init($xml);
    }

    public function setAuthProvider()
    {
        $this->authProvider = new MTSystemAuthManager(
            new KeycloakSystemAuth()
        );
    }

    public function authenticate(THttpRequest $request)
    {
        $token = $this->authProvider->introspectToken(
            bearerToken($this->getRequest()->getHeaders()['Authorization'])
        );

        if (!$token || false === $token->active) {
            return;
        }

        $this->setIsAuthenticated(true);
    }
}
