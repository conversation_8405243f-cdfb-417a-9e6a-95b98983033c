<?php

namespace TF\Engine\Kernel;

use Knp\Snappy\Pdf;
use RuntimeException;

class Snappy
{
    public $pdf;

    public function __construct()
    {
        $this->pdf = new Pdf(WKHTMLTOPDF_PATH);
        $this->pdf->setOption('encoding', 'utf-8');
        $this->pdf->setOption('enable-local-file-access', true);
    }

    public function isFileExists($filePath)
    {
        if (!is_file($filePath)) {
            throw new RuntimeException(sprintf('File "%s" was not created', $filePath));
        }
    }
}
