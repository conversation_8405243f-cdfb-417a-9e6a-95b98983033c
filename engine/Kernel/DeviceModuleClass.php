<?php

namespace TF\Engine\Kernel;

use Exception;
use GuzzleHttp\Client;
use <PERSON>rado\TModule;

class DeviceModuleClass extends TModule
{
    public $server = '';
    protected Client $http;

    public function init($config)
    {
        parent::init($config); // TODO: Change the autogenerated stub

        $this->http = new Client([
            'base_uri' => RPI_SERVER,
        ]);
    }

    public function getAllModems()
    {
        try {
            $token = $this->getToken();

            $response = $this->http->request('GET', '/api/1.0/user/devices', [
                'headers' => ['Authorization' => 'Bearer ' . $token],
            ]);

            return json_decode($response->getBody()->getContents(), true);
        } catch (\GuzzleHttp\Exception\GuzzleException $e) {
            throw new Exception('Modems api timeout');
        }
    }

    public function uploadFile($serialNumber, $filePath)
    {
        $token = $this->getToken();
        $ext = '.' . pathinfo($filePath, PATHINFO_EXTENSION);
        $baseFileName = basename($filePath, $ext);
        $filename = $baseFileName . '_' . mt_rand() . $ext;

        $response = $this->http->request('POST', '/api/1.0/file/upload', [
            'multipart' => [
                [
                    'name' => 'file',
                    'contents' => fopen($filePath, 'r'),
                    'filename' => $filename,
                ],
                [
                    'name' => 'device_serial',
                    'contents' => $serialNumber,
                ],
            ],
            'headers' => ['Authorization' => 'Bearer ' . $token],
        ]);

        return json_decode($response->getBody()->getContents(), true);
    }

    public function setFileStatus($deviceSerial, $fileId, $status)
    {
        $token = $this->getToken();
        $response = $this->http->request('POST', "/api/1.0/file/{$fileId}/status", [
            'form_params' => [
                'deviceSerial' => $deviceSerial,
                'status' => $status,
            ],
            'headers' => ['Authorization' => 'Bearer ' . $token],
        ]);

        return json_decode($response->getBody()->getContents(), true);
    }

    public function getModemsFiles($device, $status)
    {
        $token = $this->getToken();

        $response = $this->http->request('GET', 'api/1.0/devices/' . $device['serial'] . '/files', [
            'headers' => ['Authorization' => 'Bearer ' . $token],
            'params' => ['status' => $status],
        ]);
        $return = json_decode($response->getBody()->getContents(), true);
        foreach ($return as $key => $row) {
            $return[$key]['device'] = $device['machine'];
        }

        return $return;
    }

    public function downloadFiles($filename, $destination)
    {
        $token = $this->getToken();
        $resource = fopen($destination . DIRECTORY_SEPARATOR . $filename, 'w');
        $response = $this->http->request('POST', 'api/1.0/file/download', [
            'form_params' => [
                'fileName' => $filename,
            ],
            'sink' => $resource,
            'headers' => ['Authorization' => 'Bearer ' . $token],
        ]);
    }

    private function getToken()
    {
        try {
            $response = $this->http->request('POST', '/api/1.0/token', [
                'timeout' => 10,
                'form_params' => [
                    'name' => 'platformUser',
                    'password' => 'platformUser',
                ],
            ]);
            $contentJson = $response->getBody()->getContents();
            $content = json_decode($contentJson, true);

            return $content['token'];
        } catch (\GuzzleHttp\Exception\GuzzleException $e) {
            throw new Exception('Modems api timeout');
        }
    }
}
