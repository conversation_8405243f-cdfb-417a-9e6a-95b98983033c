<?php

namespace TF\Engine\Kernel\Sentry;

use Prado\Security\IUser;
use TF\Application\Common\Config;
use Throwable;

/**
 * Class Sentry.
 */
class Sentry
{
    public static function init(string $dsn, string $environment): void
    {
        \Sentry\init([
            'dsn' => $dsn,
            'environment' => $environment,
            'release' => self::getReleaseVersion(),
            'enable_logs' => true,
        ]);
    }

    public static function configureScope(IUser $user): void
    {
        \Sentry\configureScope(function (\Sentry\State\Scope $scope) use ($user): void {
            $scope->setUser(
                [
                    'id' => $user->UserID,
                    'username' => $user->Name,
                    'email' => $user->Email,
                ]
            );

            $scope->setExtra('name', $user->fullname);
            $scope->setExtra('is-main-account', in_array($user->userLevel, [Config::USERS_ADMIN_FLAG]));
            $scope->setExtra('database', $user->database);
        });
    }

    public static function logException(Throwable $exception): void
    {
        if (true === self::isDisabled()) {
            return;
        }

        \Sentry\withScope(function (\Sentry\State\Scope $scope) use ($exception): void {
            $scope->setExtra('stacktrace', $exception->getTraceAsString());
            \Sentry\captureException($exception);
        });
    }

    public static function getGitBranch(): ?string
    {
        $shellOutput = [];
        exec('git branch | ' . "grep ' * '", $shellOutput);
        foreach ($shellOutput as $line) {
            if (false !== strpos($line, '* ')) {
                return trim(strtolower(str_replace('* ', '', $line)));
            }
        }

        return null;
    }

    public static function getReleaseVersion(): ?string
    {
        if ($branchName = self::getGitBranch()) {
            if (0 !== preg_match('/(\d+\.?)+$/', $branchName, $matches)) {
                return $matches[0];
            }

            return null;
        }

        return null;
    }

    /**
     * Flag to disable sentry if needed.
     */
    private static function isDisabled(): bool
    {
        return getenv('DISABLE_SENTRY_LOG_ERROR' == false);
    }
}
