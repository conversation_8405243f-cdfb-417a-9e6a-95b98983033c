<?php

namespace TF\Engine\Kernel;

use Prado\Web\Services\TJsonService;
use Prado\Web\THttpRequest;

abstract class SystemJsonService extends TJsonService
{
    protected $authProvider;

    private $isAuthenticated = false;

    public function init($xml)
    {
        parent::loadJsonServices($xml);
        $this->setAuthProvider();
    }

    abstract public function setAuthProvider();

    abstract public function authenticate(THttpRequest $request);

    protected function createJsonResponse($service, $properties, $config)
    {
        $this->authenticate(
            $this->getApplication()->getRequest()
        );

        if (true === $this->isAuthenticated) {
            return parent::createJsonResponse($service, $properties, $config);
        }

        $this->setStatusCodeToUnauthorized();
    }

    protected function setIsAuthenticated(bool $isAuthenticated)
    {
        return $this->isAuthenticated = $isAuthenticated;
    }

    protected function setStatusCodeToUnauthorized()
    {
        $response = $this->getResponse();
        $response->setContentType('application/json');
        $response->setCharset('UTF-8');
        $response->setStatusCode(401);

        $this->getApplication()->flushOutput();
        exit;
    }
}
