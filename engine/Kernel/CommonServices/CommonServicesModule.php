<?php

namespace TF\Engine\Kernel\CommonServices;

use <PERSON>rado\TModule;
use TF\Engine\Kernel\MTRpcException;

class CommonServicesModule extends TModule
{
    /**
     * init - prado init function.
     */
    public function init($config)
    {
        parent::init($config);
    }

    /**
     * Calculates average slope for each of the specified plots in the $plotsGeoJson FeatureCollection.
     *
     * @return array $plotsAvgSlope
     *               {
     *               #item ...featureProperties
     *               #item float slope
     *
     * }
     */
    public function calculateAvgSlope(array $plotsGeoJson, string $slopeFile): array
    {
        $client = new \GuzzleHttp\Client();

        try {
            $url = COMMON_SERVICES_API_URL . '/slope/calculate-average';
            $response = $client->request('POST', $url, [
                'multipart' => [
                    [
                        'name' => 'slope_file',
                        'contents' => $slopeFile,
                    ],
                    [
                        'name' => 'plots_geojson',
                        'contents' => json_encode($plotsGeoJson),
                        'filename' => 'plots_geojson_file.json',
                        'headers' => ['Content-Type' => 'application/json'],
                    ],
                ],
                'on_headers' => function ($resp) {
                    if ($resp->getStatusCode() >= 400) {
                        throw new \GuzzleHttp\Exception\RequestException('Error calculating average slope', $resp);
                    }
                },
            ]);
            $response = json_decode($response->getBody()->getContents(), true);
        } catch (\GuzzleHttp\Exception\RequestException $e) {
            $response = null;
        }

        if (!$response) {
            throw new MTRpcException('AVG_SLOPE_CALCULATION_ERROR', -33952);
        }

       return $response;
    }
}
