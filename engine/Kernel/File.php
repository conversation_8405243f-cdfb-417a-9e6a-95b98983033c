<?php

namespace TF\Engine\Kernel;

use Prado\Web\UI\WebControls\TFileUpload;

/**
 * Kernel File class file.
 *
 * <AUTHOR>
 */

/**
 * Kernel File class.
 *
 * This class implements functions that are used to manipulate file structures. This fle is loaded in the controllers'
 * class with the help of the Loader class.
 */
class File
{
    /**
     * Returns array of all files in a directory.
     *
     * @param string $dir
     *
     * @return array
     */
    public function getFilesFromDir($dir)
    {
        $arrayContents = [];
        if (is_dir($dir)) {
            if ($dh = opendir($dir)) {
                $i = 0;

                while (($file = readdir($dh)) !== false) {
                    $allowed = false;
                    if ('file' == filetype($dir . '/' . $file)) {
                        $allowed = true;
                    }
                    if ($allowed) {
                        $arrayContents[$i]['name'] = $file;
                        $arrayContents[$i]['size'] = round(filesize($dir . '/' . $file) / 1024) . ' KB';
                        $i++;
                    }
                }
                closedir($dh);
            }
        }

        return $arrayContents;
    }

    /**
     * Returns array of all files in a directory.
     *
     * @param string $dir
     *
     * @return array
     */
    public function getDirsFromDir($dir)
    {
        $arrayContents = [];
        if (is_dir($dir)) {
            if ($dh = opendir($dir)) {
                $i = 0;

                while (($file = readdir($dh)) !== false) {
                    $allowed = false;
                    if ('dir' == filetype($dir . '/' . $file) && '.' != $file && '..' != $file) {
                        $allowed = true;
                    }
                    if ($allowed) {
                        $arrayContents[$i]['name'] = $file;
                        $i++;
                    }
                }
                closedir($dh);
            }
        }

        return $arrayContents;
    }

    /**
     * Deletes specified file from dir.
     *
     * @param string $dir
     * @param string $file
     */
    public function deleteFileFromDir($dir, $file)
    {
        if (file_exists($dir . '/' . $file)) {
            unlink($dir . '/' . $file);
        }
    }

    /**
     * Uploads file to a dir.
     *
     * @param TFileUpload $sender
     * @param string $dir, path to dir
     * @param array $fileType, allowed file types
     */
    public function uploadFileToDir($sender, $dir, $fileTypes)
    {
        $isOk = false;

        for ($i = 0;$i < count($fileTypes);$i++) {
            if ($fileTypes[$i] == $sender->getFileType()) {
                $isOk = true;

                break;
            }
        }

        if (true == $isOk) {
            if ($sender->HasFile) {
                if (!file_exists($dir)) {
                    mkdir($dir, 0700);
                }
                $uploadfile = $dir . '/' . $sender->getFileName();
                if (move_uploaded_file($sender->getLocalName(), $uploadfile)) {
                    return true;
                }
            }
        } else {
            return false;
        }
    }

    /**
     * Deletes all contents of a directory.
     *
     * @param string $directory, path to dir
     * @param bool $empty, empty or delete
     *
     * @return bool
     */
    public function removeFolder($directory, $empty = false)
    {
        if ('/' == substr($directory, -1)) {
            $directory = substr($directory, 0, -1);
        }

        if (!file_exists($directory) || !is_dir($directory)) {
            return false;
        } elseif (!is_readable($directory)) {
            return false;
        }
        $handle = opendir($directory);

        while (false !== ($item = readdir($handle))) {
            if ('.' != $item && '..' != $item) {
                $path = $directory . '/' . $item;
                if (is_dir($path)) {
                    $this->removeFolder($path);
                } else {
                    unlink($path);
                }
            }
        }
        closedir($handle);

        if (false == $empty) {
            if (!rmdir($directory)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Chmode directory recursevly.
     *
     * @param string $path
     * @param int $filemode
     *
     * @return bool
     */
    public function chmod_R($path, $filemode)
    {
        if (!is_dir($path)) {
            return chmod($path, $filemode);
        }

        $dh = opendir($path);
        while ($file = readdir($dh)) {
            if ('.' != $file && '..' != $file) {
                $fullpath = $path . '/' . $file;
                if (!is_dir($fullpath)) {
                    if (!chmod($fullpath, $filemode)) {
                        return false;
                    }
                } else {
                    if (!$this->chmod_R($fullpath, $filemode)) {
                        return false;
                    }
                }
            }
        }
    }
}
