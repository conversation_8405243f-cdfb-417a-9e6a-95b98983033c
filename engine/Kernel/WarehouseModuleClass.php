<?php

namespace TF\Engine\Kernel;

use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\BadResponseException;
use Prado\TModule;
use Symfony\Component\Process\Exception\RuntimeException;
use TF\Application\Common\Config;

class WarehouseModuleClass extends TModule
{
    public const PLATFORM_NAME = 'technofarm';

    public const ROLE_USER = 'ROLE_USER';
    public const ROLE_ADMIN = 'ROLE_ADMIN';
    public const ROLE_EDITOR = 'ROLE_EDITOR';

    public static $rolesMap = [
        self::ROLE_USER => Config::WAREHOUSE_USER_RIGHTS,
        self::ROLE_ADMIN => Config::WAREHOUSE_ADMIN_RIGHTS,
        self::ROLE_EDITOR => Config::WAREHOUSE_EDITOR_RIGHTS,
    ];

    public $server = '';
    protected $http;

    public function __construct()
    {
        $this->http = new Client([
            'base_uri' => WAREHOUSE_API_URL,
        ]);
    }

    public function init($config)
    {
        // @var Client
        $this->http = new Client([
            'base_uri' => WAREHOUSE_API_URL,
        ]);
    }

    // Transactions Reqeusts
    public function getTransactions($params = null)
    {
        $response = $this->request('transactions', $params, 'GET');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function getTransactionItems($params = null)
    {
        $response = $this->request('transactions/items', $params, 'GET');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function getTransactionItemsForReturn($params = null)
    {
        $response = $this->request('transactions/items/return', $params, 'GET');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function createAddTransaction($params = null)
    {
        $response = $this->request('transactions/add', $params, 'PUT');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function updateAddTransaction($params = null)
    {
        $response = $this->request('transactions/add', $params, 'POST');

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @param null $params
     *
     * @throws MTRpcException
     */
    public function removeTransaction($params)
    {
        if (empty($params['transactionId'])) {
            throw new Exception('Missing transaction id');
        }

        $response = $this->request('transactions/' . $params['transactionId'], $params, 'DELETE');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function createSubTransaction($params = null)
    {
        $response = $this->request('transactions/sub', $params, 'PUT');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function updateSubTransaction($params = null)
    {
        $response = $this->request('transactions/sub', $params, 'POST');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function createTransferTransaction($params = null)
    {
        $response = $this->request('transactions/transfer', $params, 'PUT');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function updateTransferTransaction($params = null)
    {
        $response = $this->request('transactions/transfer', $params, 'POST');

        return json_decode($response->getBody()->getContents(), true);
    }

    // Warehouses Requests

    public function getWarehouse($params)
    {
        if (empty($params['id'])) {
            throw new RuntimeException('Missing warehouse id');
        }
        $response = $this->request('warehouses/' . $params['id'], $params, 'GET');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function getWarehouses($params)
    {
        $response = $this->request('warehouses', $params, 'GET');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function getWarehousesTree()
    {
        $response = $this->request('warehouses/tree', [], 'GET');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function getMainWarehouses()
    {
        $response = $this->request('warehouses/main-warehouses', [], 'GET');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function getAvailableWarehouses($params)
    {
        $response = $this->request('warehouses/available', $params, 'GET');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function addWarehouse($params)
    {
        $response = $this->request('warehouses', $params, 'PUT');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function editWarehouse($params)
    {
        if (empty($params['id'])) {
            throw new RuntimeException('Missing warehouse id');
        }
        $response = $this->request('warehouses/' . $params['id'], $params, 'POST');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function deleteWarehouse($params)
    {
        if (empty($params['id'])) {
            throw new RuntimeException('Missing warehouse id');
        }
        $response = $this->request('warehouses/' . $params['id'], $params, 'DELETE');

        return json_decode($response->getBody()->getContents(), true);
    }

    // Companies Requests

    public function getCompanies($params = null)
    {
        $response = $this->request('companies', $params, 'GET');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function addCompany($params)
    {
        $response = $this->request('companies', $params, 'PUT');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function editCompany($params)
    {
        if (empty($params['id'])) {
            throw new RuntimeException('Missing id');
        }
        $response = $this->request('companies/' . $params['id'], $params, 'POST');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function deleteCompany($params)
    {
        if (empty($params['id'])) {
            throw new RuntimeException('Missing id');
        }
        $response = $this->request('companies/' . $params['id'], $params, 'DELETE');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function getCompanyTypes($params = null)
    {
        $response = $this->request('company-types', $params, 'GET');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function bulkInsertOrUpdateCompanies($params = null)
    {
        $response = $this->request('companies/bulk-insert-update', $params, 'POST');

        return json_decode($response->getBody()->getContents(), true);
    }

    // Items Requests

    public function getItems($params = null)
    {
        $response = $this->request('items', $params, 'GET');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function addItem($params)
    {
        $response = $this->request('items', $params, 'PUT');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function editItem($params)
    {
        if (empty($params['id'])) {
            throw new RuntimeException('Missing item id');
        }
        $response = $this->request('items/' . $params['id'], $params, 'POST');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function deleteItem($params)
    {
        if (empty($params['id'])) {
            throw new RuntimeException('Missing item id');
        }
        $response = $this->request('items/' . $params['id'], $params, 'DELETE');

        return json_decode($response->getBody()->getContents(), true);
    }

    // Documents Requests

    public function getDocuments($params = null)
    {
        $response = $this->request('docs', $params, 'GET');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function getDocument($params)
    {
        if (empty($params['criteries']['document_id'])) {
            throw new RuntimeException('Missing document id');
        }
        $response = $this->request('docs/' . $params['criteries']['document_id'], $params, 'GET');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function getDocumentsInvoice($params)
    {
        $response = $this->request('docs/add-invoice', $params, 'POST');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function getPdfDocument($params)
    {
        if (empty($params['document_id'])) {
            throw new RuntimeException('Missing document id');
        }
        $response = $this->request('docs/' . $params['document_id'] . '/pdf', $params, 'GET');

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @throws MTRpcException
     */
    public function removeDocument($params)
    {
        if (empty($params['documentId'])) {
            throw new RuntimeException('Missing document id');
        }
        $response = $this->request('docs/' . $params['documentId'], $params, 'DELETE');

        return json_decode($response->getBody()->getContents(), true);
    }

    // Items Groups Requests

    public function getItemGroups($params = null)
    {
        $response = $this->request('item-groups', $params, 'GET');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function getItemGroup($params)
    {
        if (empty($params['item_group_id'])) {
            throw new RuntimeException('Missing item group id');
        }
        $response = $this->request('item-groups/' . $params['id'], $params, 'GET');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function addItemGroup($params = null)
    {
        $response = $this->request('item-groups', $params, 'POST');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function editItemGroup($params = null)
    {
        $response = $this->request('item-groups/' . $params['id'], $params, 'PUT');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function deleteItemGroup($params = null)
    {
        $response = $this->request('item-groups/' . $params['id'], $params, 'DELETE');

        return json_decode($response->getBody()->getContents(), true);
    }

    // Company Groups Requests

    public function getCompanyGroups($params = null)
    {
        $response = $this->request('company-groups', $params, 'GET');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function getCompanyGroup($params)
    {
        if (empty($params['company_group_id'])) {
            throw new RuntimeException('Missing company group id');
        }
        $response = $this->request('company-groups/' . $params['id'], $params, 'GET');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function addCompanyGroup($params = null)
    {
        $response = $this->request('company-groups', $params, 'PUT');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function editCompanyGroup($params = null)
    {
        $response = $this->request('company-groups/' . $params['id'], $params, 'POST');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function deleteCompanyGroup($params = null)
    {
        $response = $this->request('company-groups/' . $params['id'], $params, 'DELETE');

        return json_decode($response->getBody()->getContents(), true);
    }

    // Measures Requests

    public function getMeasures($params = null)
    {
        $response = $this->request('measures', $params, 'GET');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function addMeasure($params)
    {
        $response = $this->request('measures', $params, 'PUT');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function editMeasure($params)
    {
        if (empty($params['id'])) {
            throw new RuntimeException('Missing document id');
        }
        $response = $this->request('measures/' . $params['id'], $params, 'POST');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function deleteMeasure($params)
    {
        if (empty($params['id'])) {
            throw new RuntimeException('Missing document id');
        }
        $response = $this->request('measures/' . $params['id'], $params, 'DELETE');

        return json_decode($response->getBody()->getContents(), true);
    }

    // Users Requests
    public function getUsers($params = null)
    {
        $response = $this->request('auth/users', $params, 'GET');

        return json_decode($response->getBody()->getContents(), true);
    }

    // Users Requests
    public function editUserRole($params = null)
    {
        $response = $this->request('auth/users/' . $params['id'], $params, 'PATCH');

        return json_decode($response->getBody()->getContents(), true);
    }

    // Reports Requests
    public function getReports($params = null)
    {
        $response = $this->request('reports/' . $params['report'], $params, 'GET');

        return json_decode($response->getBody()->getContents(), true);
    }

    public function exportReports($params = null)
    {
        $response = $this->request('reports/' . $params['report'] . '/export', $params, 'GET');

        return json_decode($response->getBody()->getContents(), true);
    }

    // Articles Requests
    public function getArticles($params = null)
    {
        $response = $this->request('articles', $params, 'GET');

        return json_decode($response->getBody()->getContents(), true);
    }

    // Get Constants
    public function getConstants($params)
    {
        $response = $this->request('utils/constants', $params, 'GET');

        return json_decode($response->getBody()->getContents(), true);
    }

    // Get DocumentNumber
    public function getDocumentNumber($params)
    {
        $response = $this->request('utils/document-number', $params, 'GET');

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @throws MTRpcException
     */
    public function loadHistory($params)
    {
        $response = $this->request('history', $params, 'GET');

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @throws MTRpcException
     */
    public function getWarehouseConfigParams()
    {
        $response = $this->request('configs', [], 'GET');

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @throws MTRpcException
     */
    public function editWarehouseConfigParams($params)
    {
        $response = $this->request('configs/update', $params, 'POST');

        return json_decode($response->getBody()->getContents(), true);
    }

    /**
     * @param array $params
     * @param string $method
     * @param array $headers
     *
     * @throws MTRpcException
     */
    private function request($url, $params = [], $method = 'POST', $headers = [])
    {
        $token = $this->User->getAccessToken();

        try {
            $reqParams['headers'] = array_merge(['Authorization' => 'Bearer ' . $this->User->getAccessToken()->getToken()], $headers);
            if ('GET' === $method) {
                $reqParams['query'] = $params;
            } else {
                $reqParams['json'] = $params;
            }
            $response = $this->http->request($method, 'api/' . $url, $reqParams);
        } catch (BadResponseException $exception) {
            $err = json_decode($exception->getResponse()->getBody()->getContents());

            $details = !empty($err->error->details) ? $err->error->details : '';
            $code = !empty($err->code) ? $err->code : 500;

            // Check for validation error and get the wrong field name
            if (-33011 === $code) {
                $detailsArr = explode(':', $details);
                $details = $detailsArr[0];
            }

            throw new MTRpcException(!empty($err->error->message) ? $err->error->message : '', $code, $details);
        }

        return $response;
    }

    /**
     * @return string
     */
    private function generateJwtToken(array $payload, array $header = [])
    {
        if (empty(getenv('JWT_SECRET_KEY')) || !file_exists(getenv('JWT_SECRET_KEY'))) {
            throw new RuntimeException('JWT secret key missing');
        }

        // Create the token header and set default parameters if its not exist
        $header['typ'] = empty($header['type']) ? 'JWT' : $header['type'];
        $header['alg'] = empty($header['alg']) ? 'sha256' : $header['alg'];
        $headerJson = json_encode($header);
        // Encode token parts
        $base64Header = base64_encode($headerJson);
        $base64Payload = base64_encode(json_encode($payload));

        // Create Signature Hash
        $signature = hash_hmac('sha256', $base64Header . '.' . $base64Payload, file_get_contents(getenv('JWT_SECRET_KEY')), true);

        return $base64Header . '.' . $base64Payload . '.' . base64_encode($signature);
    }
}
