<?php

namespace TF\Engine\Kernel\ThematicMaps;

use Prado\TModule;

class ThematicMapsResults extends TModule
{
    public $LayersController = false;
    public $UserDbController = false;
    public $UserDbPlotsController = false;
    public $UsersController = false;
    public $layer_type;
    public $mapTemplateID = 0;
    public $boolean_criteria = false;
    public $kvs_styles = [];
    public $kvs_query = '';
    public $main_layer_type = 0;

    public function getResults(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        //        throw new MTRpcException('Method not found', -32601);
    }

    /**
     * Метод, който премахва повтарящите се филтри.
     *
     * @param array $filters
     *                       {
     *                       #item array
     *                       {
     *                       #item string       column
     *                       #item string/array filterValue
     *                       }
     *                       }
     *
     * @return array
     */
    public function getUniqueFilters($filters)
    {
        $uniques = [];
        foreach ($filters as $filter) {
            // Ако колоната, по която се филтрира не е добавена към масивът с уникалните резултати
            // то тя се добавя като ключ на масива
            if (!is_array($uniques[$filter['column']])) {
                if (is_array($filter['filterValue'])) {
                    foreach ($filter['filterValue'] as $value) {
                        $uniques[$filter['column']][] = $value;
                    }
                } else {
                    $uniques[$filter['column']][] = $filter['filterValue'];
                }
            }
            // Ако конкретната стойност не е добавена към уникалните резултати,
            // то той се добавя към уникалните стойности за този ключ (колона)
            // като се взема предвид дали стойностите идват от масив или от единично поле.
            if (is_array($filter['filterValue'])) {
                foreach ($filter['filterValue'] as $value) {
                    if (!in_array($value, $uniques[$filter['column']])) {
                        $uniques[$filter['column']][] = $value;
                    }
                }
            } else {
                if (!in_array($filter['filterValue'], $uniques[$filter['column']])) {
                    $uniques[$filter['column']][] = $filter['filterValue'];
                }
            }
        }

        return $uniques;
    }

    /**
     * Помощна функция, която взема префиксите за всяко едно поле.
     */
    public function getUniqueFilterPrefixes($filters)
    {
        $uniques = [];

        foreach ($filters as $filter) {
            if (!in_array($uniques[$filter['column']], $uniques)) {
                $uniques[$filter['column']] = $filter['prefix'];
            }
        }

        return $uniques;
    }

    /**
     * Взема extent на резултатите от филтрирането.
     *
     * @param array $rpcParams - параметрите за създаването на новата тематична карта
     *                         {
     *                         #item array   filters        - приложени филтри
     *                         #item string  mapName        - име на тематичната карта
     *                         #item string  criteria       - критерии за оцветяване
     *                         #item string  criteriaText   - критерии за оцветяване в четим вариант
     *                         #item string  mapLayer       - основен слой, на който се базира картата
     *                         #item integer chart_type     - тип на диаграмата
     *                         #item integer chart_criteria - критерии за групиране на диаграмата
     *                         }
     *
     * @return string $extent                         - extent на резултатите
     */
    public function getExtent($rpcParams)
    {
        // Добавя се флаг, с който да се окаже, че е необходимо да се вземе само extent
        // на тези резулати и се премахват другите 2 флага
        $rpcParams['layerData']['forExtent'] = true;
        $rpcParams['layerData']['forMapFile'] = false;
        $rpcParams['layerData']['forLabels'] = false;

        $extent = $this->getResults($rpcParams['layerData']);
        $extent = $extent[0]['extent'];
        $extent = str_replace('BOX(', '', $extent);
        $extent = str_replace(')', '', $extent);

        return str_replace(',', ' ', $extent);
    }

    public function getLayerType()
    {
        return $this->layer_type;
    }

    public function getTemplateID()
    {
        return $this->mapTemplateID;
    }

    public function getBoolean()
    {
        return $this->boolean_criteria;
    }

    public function setBoolean($criteria)
    {
        $this->boolean_criteria = $criteria;
    }

    public function getKvsLabelData($data) {}

    public function extractEKATTEFilter($data) {}
}
