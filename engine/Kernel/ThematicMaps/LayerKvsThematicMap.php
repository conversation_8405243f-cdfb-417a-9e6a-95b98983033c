<?php

namespace TF\Engine\Kernel\ThematicMaps;

use TF\Application\Common\Config;
use TF\Engine\Kernel\Validation;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbAreaTypes\UserDbAreaTypesController;
use TF\Engine\Plugins\Core\UserDbPlotCategoriesType\UserDbPlotCategoriesTypeController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Class LayerKvsThematicMap.
 *
 * @property UserDbPlotsController $UserDbPlotsController
 * @property UserDbController $UserDbController
 */
class LayerKvsThematicMap extends ThematicMapsResults
{
    public $layer_type = 'layer_kvs';
    public $mapTemplateID = 41;
    public $main_layer_type = 5;

    public $UserDbPlotsController;
    public $UserDbController;

    /**
     * Метод, който да обработва данните, когато за основен слой е избран слой КВС.
     * В зависимост от приложените флагове връща различни резултати.
     *
     * @param array $rpcParams
     *                         {
     *                         #item boolean forLabels          - флаг, който указва дали данните са за етикети на картата
     *                         #item boolean forMapFile         - флаг, който указва дали данните са за map файл
     *                         #item boolean forExtent          - флаг, който указва дали е необходим само extent
     *                         #item string  mapName            - име на новата тематична карта
     *                         #item string  mapLayer           - име на основния слой
     *                         #item array  filters             - приложени филтри
     *                         {
     *                         #item string column      - колона, по която се филтрира
     *                         #item string filterText  - текст на колоната, по която е филтрирано в четим текст
     *                         #item string filterValue - стойност, на която да отговарят резултатите
     *                         }
     *                         #item string criteria            - критерии за оцветяване
     *                         #item string criteriaText        - критерии за оцветяване в четим текст
     *                         }
     * @param int|string $page - pagination parameter
     * @param int|string $rows - pagination parameter
     * @param string $sort - pagination parameter
     * @param string $order - pagination parameter
     *
     * @return array
     *               {
     *               #item integer total
     *               #item integer rows
     *               {
     *               #item string original_value     - резултат от критерии за групиране
     *               #item string value_field        - резултат от критерии за групиране в четим текст
     *               #item string color_field        - цвят на конкретния резултат
     *               #item string criteria           - критерия, по които е групирано
     *               #item string plot_count         - брой имоти, които попадат в критерия за групиране
     *               #item string area               - площ на имоти, които попадат в критерия за групиране
     *               }
     *               }
     */
    public function getResults(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UsersController = new UsersController('Users');
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);
        $UserDbPlotCategoriesTypeController = new UserDbPlotCategoriesTypeController($this->User->Database);
        $UserDbAreaTypesController = new UserDbAreaTypesController($this->User->Database);

        $return = ['rows' => [], 'total' => 0];
        // Форматират се критериите за сортиране, защото от JS се подават в неподходящ вид
        if ('plot_count' == $sort) {
            $sort = 'count(gid)';
        } elseif ('area' == $sort) {
            $sort = 'sum(st_area(geom))';
        }

        // Вземат се само уникалните полета стойности за всеки филтър,
        // както и префиксите, за всяко едно от полетата
        $uniqueFilters = $this->getUniqueFilters($rpcParams['filters']);
        $uniquePrefixes = $this->getUniqueFilterPrefixes($rpcParams['filters']);

        if (!in_array($rpcParams['criteria'], $uniquePrefixes)) {
            $uniquePrefixes[$rpcParams['criteria']] = $rpcParams['criteriaPrefix'];
        }

        $isContractsRequired = false;
        $isOwnerFieldForCriteria = false;

        foreach ($uniquePrefixes as $prefix => $value) {
            if ('o' == $value || 'c' == $value || 'h' == $value) {
                $isContractsRequired = true;
                if (!$uniqueFilters['start_date'][0] || Config::VALIDATION_VALID_FIELD !== Validation::validateDate($uniqueFilters['start_date'][0])) {
                    $uniqueFilters['start_date'][0] = date('Y-m-d');
                }
                if (!$uniqueFilters['due_date'][0] || Config::VALIDATION_VALID_FIELD !== Validation::validateDate($uniqueFilters['due_date'][0])) {
                    $uniqueFilters['due_date'][0] = date('Y-m-d');
                }
            }
            if ('o' == $value) {
                $isOwnerFieldForCriteria = true;
            }
        }
        if ('o' == $rpcParams['criteriaPrefix']) {
            $isOwnerFieldForCriteria = true;
        }

        // Всички колони, които са от тип boolean в layer_kvs
        $boolean_columns = [
            'has_contracts',
            'participate',
            'include',
            'white_spots',
            'usable',
            'is_edited',
            'waiting_update',
            'irrigated_area',
            'is_subleased',
            'in_hypothec',
        ];
        $this->setBoolean(in_array($rpcParams['criteria'], $boolean_columns));
        $options = [
            'return' => [
                'COUNT (DISTINCT(gid)) AS COUNT'],
            'where' => [
                'is_edited' => ['column' => 'is_edited', 'compare' => '=', 'prefix' => 'kvs', 'value' => 'false'],
            ],
            'group' => $uniquePrefixes[$rpcParams['criteria']] . '.' . $rpcParams['criteria'],
            'sort' => 'value_field' == $sort ? $rpcParams['criteria'] : $sort,
            'order' => $order,
        ];

        if ($isContractsRequired) {
            $options['start_date'] = $uniqueFilters['start_date'][0];
            $options['due_date'] = $uniqueFilters['due_date'][0];
            $options['where']['annex_action'] = ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'cpr', 'value' => 'added'];
            $options['where']['is_annex'] = ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => 'false'];
            $options['where']['active'] = ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'true'];
            $options['where']['nm_usage_rights'] = ['column' => 'nm_usage_rights', 'compare' => '<>', 'prefix' => 'c', 'value' => 4];
            $options['t1.pc_rel_id'] = 'true';
            if ($isOwnerFieldForCriteria) {
                $area_return = 'SUM (
                                    CASE
                                        WHEN
                                            por.percent IS NULL
                                        THEN
                                            CASE WHEN
                                                pfr.percent is NULL
                                            THEN cpr.contract_area
                                            ELSE
                                                cpr.contract_area * pfr.percent / 100
                                            END
                                        ELSE
                                        cpr.contract_area * por.percent / 100
                                    END
                                    ) AS area';
            } else {
                $area_return = 'round(SUM (cpr.contract_area * (CASE WHEN por.percent IS NULL THEN (CASE WHEN pfr.percent IS NULL THEN 100 ELSE pfr.percent END) ELSE por.percent END) / 100) :: NUMERIC,3) as area';
            }
            array_push($options['return'], $area_return);
        } else {
            array_push($options['return'], 'SUM (DISTINCT(st_area(geom)/1000)) AS area');
        }

        // Ако се изисква информация свързана със собствениците само тогава трябва да се join-нат таблиците за собственици.
        $options['ownersRequired'] = $isOwnerFieldForCriteria;
        // Тази проверка се налага, защото ако полето е от тип boolean, не може да му се зададе стойност по подразбиране 'Неизвестно', в случай
        // че полето е null
        if ($this->getBoolean() && 'is_subleased' != $rpcParams['criteria'] && 'in_hypothec' != $rpcParams['criteria']) {
            array_push($options['return'], $rpcParams['criteria']);
        } else {
            switch ($rpcParams['criteria']) {
                case 'owner_names':
                    $tmp_var = "(
                                CASE WHEN pfr.id is not null then pfr.farming_id::TEXT ELSE
                                    CASE WHEN por.id is not null THEN
                                        CASE
                                            WHEN owner_type = 1 THEN
                                                NAME || ' ' || surname || ' ' || lastname
                                            ELSE
                                                company_name
                                        END
                                    ELSE
                                        'no_name'
                                    END
                                END
                            ) AS owner_names";
                    $options['group'] = 'o.id, por.id, pfr.id';

                    break;
                case 'egn':
                    $tmp_var = "(
                                CASE WHEN pfr.id is not null then pfr.farming_id::TEXT ELSE
                                    CASE WHEN por.id is not null THEN
                                        CASE
                                            WHEN owner_type = 1 THEN
                                                egn
                                            ELSE
                                                eik
                                        END
                                    ELSE
                                        'Неизвестно'
                                    END
                                END
                            ) AS egn";
                    $options['group'] = 'o.id, por.id, pfr.id';

                    break;
                case 'farming_id':
                    $tmp_var = 'c.farming_id';

                    break;
                case 'start_date':
                    $tmp_var = "to_char(c.start_date,'DD.MM.YYYY') as start_date";

                    break;
                case 'due_date':
                    $tmp_var = "(CASE WHEN c.due_date is null then 'Няма крайна дата' ELSE to_char(c.due_date,'DD.MM.YYYY') END) as due_date";
                    $options['group'] = 'c.due_date';

                    break;
                case 'nm_usage_rights':
                    $tmp_var = 'c.nm_usage_rights';

                    break;
                case 'in_hypothec':
                    $plot_ids = $this->getGidsInHypothecs($uniqueFilters, true);
                    $tmp_var = "(CASE WHEN cpr.plot_id IN ({$plot_ids}) THEN true ELSE false END) as in_hypothec";
                    $options['group'] = 'in_hypothec';

                    break;
                default:
                    $tmp_var = '(CASE WHEN ' . $uniquePrefixes[$rpcParams['criteria']] . '.' . $rpcParams['criteria'] . " IS NULL THEN 'Неизвестно' ELSE " . $uniquePrefixes[$rpcParams['criteria']] . '.' . $rpcParams['criteria'] . ' END) as ' . $rpcParams['criteria'];

                    break;
            }
            array_push($options['return'], $tmp_var);
        }

        // Обикаля се по всички уникални резултати за филтър
        foreach ($uniqueFilters as $filterColumn => $values) {
            // Ако има повече от един резултат за отделните критерии, трябв да се разглежда като масив
            $valueCount = count($values);
            if ($valueCount > 1) {
                switch ($filterColumn) {
                    case 'owner_names':
                        $ownerIds = $this->getOwnerIds($values);
                        $options['where'][$filterColumn] = ['column' => 'id', 'compare' => 'IN', 'prefix' => 'o', 'value' => $ownerIds];
                        $options['group'] .= ', o.id';

                        break;
                    default:
                        $options['where'][$filterColumn] = ['column' => $filterColumn, 'compare' => 'IN', 'prefix' => $uniquePrefixes[$filterColumn], 'value' => $values];

                        break;
                }
            } else {
                switch ($filterColumn) {
                    case 'owner_names':
                        $tmp_owner_names = preg_replace('/\s+/', '.*', $values[0]);
                        $tmp_owner_names = mb_strtolower($tmp_owner_names, 'UTF-8');
                        array_push($options['return'], "regexp_matches(lower(TRIM (o.NAME)) || ' ' || lower(TRIM (o.surname)) || ' ' || lower(TRIM (o.lastname)), '{$tmp_owner_names}','g')");
                        $options['group'] .= ', o.id';

                        break;
                    case 'start_date':
                        $options['where']['contract_start_date'] = ['column' => $filterColumn, 'prefix' => 'c', 'compare' => '<=', 'value' => $uniqueFilters['due_date'][0]];

                        break;
                    case 'due_date':
                        $options['where']['contract_due_date'] = "((CASE WHEN c.nm_usage_rights = 1 THEN '9999-12-31 00:00:00' ELSE c.due_date END) >= '" . $uniqueFilters['start_date'][0] . "' OR a.due_date >='" . $uniqueFilters['start_date'][0] . "')";

                        break;
                    case 'is_subleased':
                        $options['t1.pc_rel_id'] = !filter_var($values[0], FILTER_VALIDATE_BOOLEAN);

                        break;
                    case 'in_hypothec':
                        $plot_ids = $this->getGidsInHypothecs($uniqueFilters, false);
                        $compare = 'true' === $values[0] ? 'IN' : 'NOT IN';
                        $options['where']['in_hypothec'] = ['column' => 'gid', 'compare' => $compare, 'prefix' => 'kvs', 'value' => $plot_ids];

                        break;
                    default:
                        $options['where'][$filterColumn] = ['column' => $filterColumn, 'compare' => '=', 'prefix' => $uniquePrefixes[$filterColumn], 'value' => $values[0]];

                        break;
                } // Край на switch
            } // Край на else за масиви
        } // Край на цикъла, за създаване на where клаузи

        // Ако е необходимо да се върнат резултати само за етикетите на картата
        // се променя SELECT statement, за да се върнат необходимите за това колони
        // като в същото време се запазва WHERE клаузата, за да не се променят резултатите
        if ($rpcParams['forLabels']) {
            $options['return'] = ['gid', 'kad_ident', 'geom'];
            $options['group'] = 'gid';
            if ($isContractsRequired) {
                return $UserDbPlotsController->getThematicMapInfoForKVSLayerWithContracts($options, false, true);
            }

            return $UserDbPlotsController->getThematicMapInfoForKVSLayer($options, false, true);
        }
        // Ако е необходимо резултатите да се върнат за направата на map файлът
        // се добавя и обединението на геометриите на отделните резултати
        if ($rpcParams['forMapFile']) {
            $options['return'][] = 'ST_union(geom) as geom';
            if ($isContractsRequired) {
                return $UserDbPlotsController->getThematicMapInfoForKVSLayerWithContracts($options, false, true);
            }

            return $UserDbPlotsController->getThematicMapInfoForKVSLayer($options, false, true);
        }

        // Ако е необходим само extent на резултатите се променят SELECT statement
        // без да се прави промяна по WHERE, за да се запазят резултатите непроменени
        if ($rpcParams['forExtent']) {
            $options['return'] = ['ST_extent(geom) as extent'];
            $options['sort'] = '';
            $options['order'] = '';
            $options['group'] = '';
            if ($isContractsRequired) {
                return $UserDbPlotsController->getThematicMapInfoForKVSLayerWithContracts($options, false, false);
            }

            return $UserDbPlotsController->getThematicMapInfoForKVSLayer($options, false, false);
        }

        // Ако не са сложени никакви флагове, значи данните са необходими за грида с резултатите,
        // в които потребителя има възможността за промяна на цветовете на отделните резултати
        $results = [];
        if ($isContractsRequired) {
            $results = $UserDbPlotsController->getThematicMapInfoForKVSLayerWithContracts($options, false, false);
            if ('owner_names' == $rpcParams['criteria'] || 'farming_id' == $rpcParams['criteria'] || 'egn' == $rpcParams['criteria']) {
                // Резултатите се форматират като се групират по собственик в помощен метод
                $results = $this->formatResultsForOwnerNameCriteria($results, $rpcParams['criteria']);
            }
        } else {
            $results = $UserDbPlotsController->getThematicMapInfoForKVSLayer($options, false, false);
        }

        $count = count($results);
        if (0 == $count) {
            return $return;
        }

        $ekateData = $UsersController->getAllEkatteData();

        $ekatteCount = count($ekateData);
        for ($i = 0; $i < $ekatteCount; $i++) {
            $ekateNames[$ekateData[$i]['ekatte_code']] = $ekateData[$i]['ekatte_name'];
        }

        $formattedResults = [];
        $colorCount = count($GLOBALS['Layers']['colors']);

        // Резултатите се форматират, за да са в подходящ за грида вид
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            switch ($rpcParams['criteria']) {
                case 'category':
                    $value = $UserDbPlotCategoriesTypeController->getPlotCategoryTitle($rpcParams['criteria']);
                    if ('0' == $results[$i][$rpcParams['criteria']]) {
                        $value = 'Без категория';
                    }

                    break;
                case 'area_type':
                    $value = $UserDbAreaTypesController->getNtpTitle($results[$i][$rpcParams['criteria']]);

                    break;
                case 'ekate':
                    $value = $results[$i]['ekate'] . ' (' . $UsersController->getEkatteName($results[$i]['ekate']) . ')';

                    break;
                case 'nm_usage_rights':
                    $value = $GLOBALS['Contracts']['ContractTypes'][$results[$i][$rpcParams['criteria']]]['name'];

                    break;
                default:
                    $value = $results[$i][$rpcParams['criteria']];

                    break;
            }
            // В случай, че няма намерени резултати, които да визуализират текст на потребителя
            // то се използва текстът, който е дошъл от потребителя в заявката
            // но ако има, се използват подготвените резултати
            $value = '' == $value ? $results[$i][$rpcParams['criteria']] : $value;
            // Ако критерия за оцветяване и групиране е част от полетата, които
            // са от тип boolean се слагат съответно четими текстове за true и false
            if ($this->getBoolean()) {
                $value = $value ? 'Да' : 'Не';
            }

            $formattedResults[] = [
                'value_field' => $value,
                // В случай, че резултатите са повече от зададените цветове в палитрата
                // т.е. 50, цветовете започват да се повтарят
                'color_field' => $GLOBALS['Layers']['colors'][$i % $colorCount]['hex'],
                'criteria' => $rpcParams['criteria'],
                'original_value' => 'owner_names' == $rpcParams['criteria'] || 'egn' == $rpcParams['criteria'] || 'farming_id' == $rpcParams['criteria'] ? $results[$i]['raw_criteria'] : $results[$i][$rpcParams['criteria']],
                'plot_count' => $results[$i]['count'],
                'area' => number_format($results[$i]['area'], 3, '.', ''),
            ];
        } // Край на цикъла за форматиране на резултатите

        return ['rows' => $formattedResults, 'total' => $count];
    }

    public function getKVSQuery($layerData)
    {
        $ekateFilter = $this->extractEKATTEFilter($layerData);
        $this->getKvsLabelData($ekateFilter);
    }

    public function getKvsLabelData($ekateFilter)
    {
        $LayersController = new LayersController('Layers');
        $wrapCharacter = "\n";
        $options = [
            'tablename' => $LayersController->DbHandler->layerTable,
            'return' => ['style'],
            'whereFields' => [
                'group_id',
                'layer_type',
            ],
            'whereValues' => [
                $this->User->GroupID,
                5,
            ],
        ];

        $styles = $LayersController->getItemByParams($options);
        $styles = json_decode($styles['style'], true);
        $ekates = [];

        foreach ($styles as $ekate => $arr_values) {
            if (in_array($ekate, $ekateFilter['ekate_array'])) {
                $ekates[$ekate] = $arr_values['label_name'];
            }
        }

        if (empty($ekates)) {
            foreach ($styles as $ekate => $arr_values) {
                $ekates[$ekate] = $arr_values['label_name'];
            }
        }

        // Прави се проверка за всички labels, дали сред тях е area_type.
        // В случай, че се изисква е необходимо да се направи dblink към susi_main,
        // за да се вземат имената на НТП като текст, а не само като код.
        $areaTypeJoinRequired = false;

        foreach ($ekates as $ekatte => $value) {
            $ekatte_label = [];

            $label_name = is_array($value) ? $value : [];
            if (empty($label_name)) {
                $ekatte_label[] = "' '";
            }
            foreach ($label_name as $label_key => $label_value) {
                switch ($label_value) {
                    case 'area_kvs':
                        $label = 'round((ST_Area(geom)/1000)::numeric, 3)';

                        break;
                    case 'used_area':
                        $label = "COALESCE(round(used_area::NUMERIC, 3)::text, '')";

                        break;
                    case 'area_type':
                        $areaTypeJoinRequired = true;
                        $label = 'title';

                        break;
                    case 'document_area':
                        $label = '(CASE WHEN document_area IS NULL THEN round((ST_Area(geom)/1000)::numeric, 3) ELSE round(document_area::numeric, 3) END)';

                        break;
                    case 'irrigated_area':
                        $label = '(CASE WHEN irrigated_area = TRUE THEN document_area ELSE 0 END)';

                        break;
                    case 'masiv_imot':
                        $label = "(masiv || '.' || number)";

                        break;
                    case null:
                        $label = 'kad_ident';

                        break;
                    default:
                        $label = $label_value;

                        break;
                }
                $ekatte_label[] = $label;
            }
            $kvs_labels[$ekatte] = implode(' || \'' . $wrapCharacter . '\' || ', $ekatte_label);
            $kvs_labels[$ekatte] .= " as label_{$ekatte}";
        }

        $kvs_query = "geom FROM (SELECT gid, geom, ekate, kad_ident, label_placeholder FROM layer_kvs kvs WHERE true AND kvs.is_edited = 'false' {$ekateFilter['query_string']}) as subquery using unique gid using srid=32635";
        $kvs_query = str_replace('label_placeholder', implode(', ', $kvs_labels), $kvs_query);

        // Ако е необходимо да се прави dblink към susi_main, за да се вземат
        // текстовете за НТП се добавя към заявката за слоя.
        if ($areaTypeJoinRequired) {
            $remoteJoin = " LEFT JOIN dblink (
                    'host=" . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' dbname=' . DEFAULT_DB_DATABASE . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . "',
                    'SELECT id, title FROM su_area_types WHERE true'
                    ) AS e (area_type_id varchar, title varchar) ON(area_type_id = area_type)";

            $remoteJoin .= ' WHERE true';
            $kvs_query = str_replace('WHERE true', $remoteJoin, $kvs_query);
        }

        $this->kvs_styles = $kvs_labels;
        $this->kvs_query = $kvs_query;
    }

    public function extractEKATTEFilter($layerData)
    {
        $ekateFilter = '';
        $ekates = [];
        $ekate_array = [];
        foreach ($layerData['filters'] as $filter) {
            if ('ekate' == $filter['column']) {
                foreach ($filter['filterValue'] as $key) {
                    $ekates[] = "'" . $key . "'";
                    $ekate_array[] = $key;
                }
            }
        }

        if (!empty($ekateFilter['ekate_array'])) {
            $ekateFilter .= ' AND ekate IN (' . implode(', ', $ekates) . ')';
        }

        return ['query_string' => $ekateFilter, 'ekate_array' => $ekate_array];
    }

    private function returnCleanArray($data, $field)
    {
        $return = [];
        foreach ($data as $input) {
            $return[] = $input[$field];
        }

        return array_unique($return);
    }

    // Помощна фукция, която взема GIDs на всички ипотекирани имоти за даден период
    private function getGidsInHypothecs($uniqueFilters, $asString)
    {
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);

        $h_options = [
            'return' => [
                'DISTINCT(gid)'],
            'where' => [
                'h_start_date' => ['column' => 'start_date', 'compare' => '<=', 'prefix' => 'h', 'value' => $uniqueFilters['h_due_date'][0]],
                'h_due_date' => ['column' => 'due_date', 'compare' => '>=', 'prefix' => 'h', 'value' => $uniqueFilters['h_start_date'][0]],
            ],
        ];
        $plot_ids = $UserDbPlotsController->getHypothecsPlotsReport($h_options, false, false);
        $plot_ids = $this->returnCleanArray($plot_ids, 'gid');

        if ($asString) {
            return implode(',', $plot_ids);
        }

        return $plot_ids;
    }

    // Помощна фукция, която взема GIDs на всички преотдадени имоти за даден период
    private function getSubleasedGids($uniqueFilters, $asString)
    {
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);

        $options = [
            'return' => [
                'gid',
            ],
            'where' => [
                'sales_contracts_plots' => ['column' => 'id', 'compare' => 'IS', 'prefix' => 'scpr', 'value' => 'NULL'],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'true'],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'true'],
                'start_date' => ['column' => 'start_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $uniqueFilters['due_date'][0]],
                'due_date' => ['column' => 'due_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $uniqueFilters['start_date'][0]],
            ],
            'group' => 'kvs.gid',
        ];

        $plot_ids = $UserDbPlotsController->getSubleasedPlotsReport($options, false, false);
        $plot_ids = $this->returnCleanArray($plot_ids, 'gid');

        if ($asString) {
            return implode(',', $plot_ids);
        }

        return $plot_ids;
    }

    /**
     * Помощна функция, която групира резултатите по имена, тъй като не може да бъдат групирани
     * по име на собственик и стопанство, тъй като критериите са от различни таблици.
     * Също така е необходимо да се вземат имената на стопанствата, ако има въведено стопанство
     * като собственик на имот.
     */
    private function formatResultsForOwnerNameCriteria($results, $criteria)
    {
        $FarmingController = new FarmingController('Farming');

        $options = [
            'return' => ['*'],
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $this->User->GroupID],
            ],
            'sort' => 'id',
            'order' => 'ASC',
        ];
        $farming_results = $FarmingController->getFarmings($options, false, false);

        $finalFarmings = [];

        // Имената на стопанствата се подготвят във формат
        // $key   -> ID на стопанство
        // $value -> име на стопанство/БУЛСТАТ в зависимост от критерия за групиране
        $farmingCriteria = '';

        switch ($criteria) {
            case 'owner_names':
                $farmingCriteria = 'name';

                break;
            case 'farming_id':
                $farmingCriteria = 'name';

                break;
            case 'egn':
                $farmingCriteria = 'bulstat';

                break;
            default:
                $farmingCriteria = $criteria;

                break;
        }

        $farmingCount = count($farming_results);
        for ($i = 0; $i < $farmingCount; $i++) {
            $finalFarmings[$farming_results[$i]['id']] = $farming_results[$i][$farmingCriteria];
        }

        // Обикалят се всички резултати и се групират в нов масив,
        // където ключът е името на собственика или ID, ако е стопанство,
        // а броят на имотите и площта на собствеността се сумират.
        // Добавя се и името, което е върнато от заявката като
        // raw_criteria, за да може да се използва в заявката за генерирането
        // на map file
        $uniqueNames = [];
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            if (!isset($uniqueNames[$results[$i][$criteria]][$criteria])) {
                $uniqueNames[$results[$i][$criteria]][$criteria] = $results[$i][$criteria];
                $uniqueNames[$results[$i][$criteria]]['raw_criteria'] = $results[$i][$criteria];
            }
            $uniqueNames[$results[$i][$criteria]]['count'] += $results[$i]['count'];
            $uniqueNames[$results[$i][$criteria]]['area'] += $results[$i]['area'];
        }

        // Вземат се всички собственици, за да се форматират имената им
        // във вид, подходящ за визуализация
        $names = array_keys($uniqueNames);

        $namesCount = count($names);
        for ($i = 0; $i < $namesCount; $i++) {
            // Първо се прави проверка дали името на собственикът е Integer, и дали съществува като ключ
            // за стопанство на клиента. В случай, че и двете условия са изпълнени, то за име на собственика
            // се слага името на съответното стопанство
            if (200 == Validation::validateInteger($names[$i]) && array_key_exists($names[$i], $finalFarmings)) {
                $uniqueNames[$names[$i]][$criteria] = $finalFarmings[$names[$i]];
                // Прави се и нова проверка, дали името на собственика е 'no_name', т.е. не са въведени нито
                // физическо/юридическо лице за собственик, нито стопанство. Т.е. имотът участва в договор, но
                // няма зададен собственик
            } elseif ('no_name' == $names[$i]) {
                $uniqueNames[$names[$i]][$criteria] = 'Без собственик';
            }
        }

        // Накрая резултатите се подават в еднакви масиви, готови за грида
        $return = [];
        foreach ($uniqueNames as $key => $value) {
            $return[] = $value;
        }

        return $return;
    }

    private function getOwnerIds($owners)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $return = [];

        foreach ($owners as $name) {
            $options = [
                'tablename' => $UserDbController->DbHandler->tableOwners,
                'return' => ['id'],
            ];
            $tmp_owner_names = preg_replace('/\s+/', '.*', $name);
            $tmp_owner_names = mb_strtolower($tmp_owner_names, 'UTF-8');
            array_push($options['return'], "regexp_matches(lower(TRIM (name)) || ' ' || lower(TRIM (surname)) || ' ' || lower(TRIM (lastname)), '{$tmp_owner_names}','g')");

            $results = $UserDbController->getItemsByParams($options, false, false);
            array_push($return, $results[0]['id']);
        }

        return $return;
    }
}
