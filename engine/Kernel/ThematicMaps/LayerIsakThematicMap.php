<?php

namespace TF\Engine\Kernel\ThematicMaps;

use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;

class LayerIsakThematicMap extends ThematicMapsResults
{
    public $layer_type = 'isak';
    public $mapTemplateID = 45;
    public $main_layer_type = 6;

    /**
     * Метод, който да обработва данните, когато за основен слой е избран слой КВС.
     * В зависимост от приложените флагове връща различни резултати.
     *
     * @param array $rpcParams
     *                         {
     *                         #item boolean forLabels          - флаг, който указва дали данните са за етикети на картата
     *                         #item boolean forMapFile         - флаг, който указва дали данните са за map файл
     *                         #item boolean forExtent          - флаг, който указва дали е необходим само extent
     *                         #item string  mapName            - име на новата тематична карта
     *                         #item string  mapLayer           - име на основния слой
     *                         #item array  filters             - приложени филтри
     *                         {
     *                         #item string column      - колона, по която се филтрира
     *                         #item string filterText  - текст на колоната, по която е филтрирано в четим текст
     *                         #item string filterValue - стойност, на която да отговарят резултатите
     *                         }
     *                         #item string criteria            - критерии за оцветяване
     *                         #item string criteriaText        - критерии за оцветяване в четим текст
     *                         }
     * @param int $page - pagination parameter
     * @param int $rows - pagination parameter
     * @param string $sort - pagination parameter
     * @param string $order - pagination parameter
     *
     * @return array
     *               {
     *               #item integer total
     *               #item integer rows
     *               {
     *               #item string original_value     - резултат от критерии за групиране
     *               #item string value_field        - резултат от критерии за групиране в четим текст
     *               #item string color_field        - цвят на конкретния резултат
     *               #item string criteria           - критерия, по които е групирано
     *               #item string plot_count         - брой имоти, които попадат в критерия за групиране
     *               #item string area               - площ на имоти, които попадат в критерия за групиране
     *               }
     *               }
     */
    public function getResults(array $rpcParams, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbPlotsController = new UserDbPlotsController($this->User->Database);

        $return = ['rows' => [], 'total' => 0];
        // Форматират се критериите за сортиране, защото от JS се подават в неподходящ вид
        if ('plot_count' == $sort) {
            $sort = 'count(gid)';
        } elseif ('area' == $sort) {
            $sort = 'sum(st_area(geom))';
        }

        // Вземат се само уникалните полета стойности за всеки филтър,
        // както и префиксите, за всяко едно от полетата
        $uniqueFilters = $this->getUniqueFilters($rpcParams['filters']);

        // Всички колони, които са от тип boolean в layer_isak_
        $boolean_columns = [
            'watering',
        ];

        $this->setBoolean(in_array($rpcParams['criteria'], $boolean_columns));

        $options = [
            'tablename' => $rpcParams['mapLayer'],
            'return' => [
                'COUNT (DISTINCT(gid)) AS COUNT', 'SUM (DISTINCT(st_area(geom)/1000)) AS area', $rpcParams['criteria'] . '::TEXT'],
            'group' => $rpcParams['criteria'],
            'sort' => 'value_field' == $sort ? $rpcParams['criteria'] : $sort,
            'order' => $order,
        ];

        // Обикаля се по всички уникални резултати за филтър
        foreach ($uniqueFilters as $filterColumn => $values) {
            // Ако има повече от един резултат за отделните критерии, трябв да се разглежда като масив
            $valueCount = count($values);
            if ($valueCount > 1) {
                switch ($filterColumn) {
                    case 'schemata':
                        for ($i = 0; $i < $valueCount; $i++) {
                            $options['whereOr'][$filterColumn . '_' . $i] = ['column' => 'schemata', 'compare' => 'LIKE',  'value' => $GLOBALS['Farming']['schema'][$values[$i]]['name']];
                        }

                        break;
                    default:
                        $options['where'][$filterColumn] = ['column' => $filterColumn, 'compare' => 'IN',  'value' => $values];

                        break;
                }
            } else {
                if ('schemata' == $filterColumn) {
                    $options['where'][$filterColumn] = ['column' => $filterColumn, 'compare' => 'LIKE',  'value' => $GLOBALS['Farming']['schema'][$values[0]]['name']];
                } else {
                    $options['where'][$filterColumn] = ['column' => $filterColumn, 'compare' => '=',  'value' => $values[0]];
                }

                break;
            } // Край на else за масиви
        } // Край на цикъла, за създаване на where клаузи

        // Ако е необходимо да се върнат резултати само за етикетите на картата
        // се променя SELECT statement, за да се върнат необходимите за това колони
        // като в същото време се запазва WHERE клаузата, за да не се променят резултатите
        if ($rpcParams['forLabels']) {
            $options['return'] = ['gid', $rpcParams['criteria'] . '::TEXT', 'geom'];
            $options['group'] = 'gid';

            return $UserDbPlotsController->getItemsByParams($options, false, true);
        }

        // Ако е необходимо резултатите да се върнат за направата на map файлът
        // се добавя и обединението на геометриите на отделните резултати
        if ($rpcParams['forMapFile']) {
            array_push($options['return'], 'ST_union(geom) as geom');

            return $UserDbPlotsController->getItemsByParams($options, false, true);
        }

        // Ако е необходим само extent на резултатите се променят SELECT statement
        // без да се прави промяна по WHERE, за да се запазят резултатите непроменени
        if ($rpcParams['forExtent']) {
            $options['return'] = ['ST_extent(geom) as extent'];
            $options['sort'] = '';
            $options['order'] = '';
            $options['group'] = '';

            return $UserDbPlotsController->getItemsByParams($options, false, false);
        }

        // Ако не са сложени никакви флагове, значи данните са необходими за грида с резултатите,
        // в които потребителя има възможността за промяна на цветовете на отделните резултати
        $results = [];

        $results = $UserDbPlotsController->getItemsByParams($options, false, false);

        $count = count($results);
        if (0 == $count) {
            return $return;
        }

        $formattedResults = [];
        $colorCount = count($GLOBALS['Layers']['colors']);

        // Резултатите се форматират, за да са в подходящ за грида вид
        for ($i = 0; $i < count($results); $i++) {
            switch ($rpcParams['criteria']) {
                case 'cropcode':
                    $value = $GLOBALS['Farming']['crops'][$results[$i][$rpcParams['criteria']]]['crop_name'];

                    break;
                default:
                    $value = $results[$i][$rpcParams['criteria']];

                    break;
            }
            // В случай, че няма намерени резултати, които да визуализират текст на потребителя
            // то се използва текстът, който е дошъл от потребителя в заявката
            // но ако има, се използват подготвените резултати
            $value = '' == $value ? $results[$i][$rpcParams['criteria']] : $value;

            // Ако критерия за оцветяване и групиране е част от полетата, които
            // са от тип boolean се слагат съответно четими текстове за true и false
            if ($this->getBoolean()) {
                $value = 'true' == $value ? 'Да' : 'Не';
            }

            $formattedResults[] = [
                'value_field' => $value,
                // В случай, че резултатите са повече от зададените цветове в палитрата
                // т.е. 50, цветовете започват да се повтарят
                'color_field' => $GLOBALS['Layers']['colors'][$i % $colorCount]['hex'],
                'criteria' => $rpcParams['criteria'],
                'original_value' => $results[$i][$rpcParams['criteria']],
                'plot_count' => $results[$i]['count'],
                'area' => number_format($results[$i]['area'], 3, '.', ''),
            ];
        } // Край на цикъла за форматиране на резултатите

        return ['rows' => $formattedResults, 'total' => $count];
    }
}
