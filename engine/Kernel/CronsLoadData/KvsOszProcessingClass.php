<?php

namespace TF\Engine\Kernel\CronsLoadData;

use Exception;
use TF\Application\Common\Config;
use TF\Application\Entity\LayerStyles;
use TF\Application\Entity\UserLayers;

/**
 * <AUTHOR> <<EMAIL>>
 *
 * @link http://www.technofarm.bg/
 *
 * @copyright Copyright &copy; 2015 TechnoFarm Ltd.
 */
class KvsOszProcessingClass extends AbstractLoadDataClass
{
    public $tmpTable = '';

    /**
     * init - prado init function.
     */
    public function init($config)
    {
        parent::init($config);
    }

    /**
     * startProcessing.
     */
    public function startProcessing()
    {
        $this->mainProcessingSimple();
    }

    /**
     * dataSource.
     *
     * @return array of data from DB
     */
    public function dataSource()
    {
        return $this->LayersController->getOszKVSFilesForProcessing();
    }

    /**
     * mainProcessingSimple - executes a simple main process.
     */
    public function mainProcessingSimple()
    {
        $this->onBeforeProcessing();

        if (0 != count($this->data)) {
            try {
                $this->checkForNotAllowedAdding();

                $this->unzipData();

                $this->processSimpleItems();

                $this->finalActions();
            } catch (Exception $e) {
                $error = $this->catchError('', $e->getMessage());

                if (ERROR_GEOMETRY_COLLECTION != $error && ERROR_READING_SHAPE_OBJECT != $error) {
                    $this->mainException($e);

                    return false;
                }
                $this->errorLog(PARTIALLY_PROCESSED, $e->getMessage());
            }
        }

        try {
            $this->onAfterProcessing();

            if ($this->ID && NOT_UPDATED_CONTRACTS != $this->LayersController->getFilesProcessingStatus($this->ID)['status']) {
                $this->UsersDbController->dropTableKvsEkatte($this->tmpTable, true);
            }
        } catch (Exception $e) {
            $this->UsersDbController->dropTableKvsEkatte($this->tmpTable, true);

            $this->errorLog($e->getCode(), $e->getMessage());

            return false;
        }
    }

    /**
     * finalActions - final actions that we execute.
     */
    public function finalActions()
    {
        if (($this->shapeFile && $this->dbfFile) || ($this->shapeFlag && $this->dbfFlag)) {
            // remove the unziped dir
            $this->LayersController->File->removeFolder($this->userSubDir);
        } else {
            throw new Exception(__METHOD__ . ' Line:' . __LINE__, ERROR_INVALID_ARCHIVE);
        }
    }

    /**
     * copyProcess executes the process to copy the data.
     */
    public function copyProcess()
    {
        try {
            $tpmTableChunk = explode('_', $this->tmpTable);
            $ekate = end($tpmTableChunk);

            if (!$this->UsersDbController->checkIfIndexExists($this->tmpTable . '_geom_gist')) {
                $this->createGeometryIndex();
            }
            $this->addIntersectionColumns();
            $this->createIntersectionViews($ekate);
            $this->updateTmpTableWithIntersectionAreas($ekate);

            // Columns for update - key is the column name in the temp table and value is the column name in the kvs table
            $colsMap = [
                'pl_dka' => 'document_area',
                'kod_ntp' => 'area_type',
                'kategoria' => 'category',
                'mestnost' => 'mestnost',
                'geom' => 'geom',
            ];

            // Update chosen columns in the kvs table
            $this->UsersDbController->updateKvsFromOszFile($this->tmpTable, $colsMap);

            // За имоти неучастващи в договор
            // когато един имот(А) е бил разцепен се сетва с is_edited = true;
            // имотите на които е разделен (B и C) се сетват с removeInactivePlotsKvs на is_edited = true;
            // така в квсто се получава дупка.Следната функция прави видим основния(А - зареден от новото квс имот)
            $this->UsersDbController->reactivateOldPlotsKvs($this->tmpTable);

            // Ако не са включени в активни договори към датата на зареждане (триеме от сис. КВС)
            // kad_no NOT exists in KVS
            $this->UsersDbController->removeInactivePlotsKvs($this->tmpTable);

            // Добавяне на колона is_system във временната таблица
            $this->UsersDbController->updateTemporaryKVSLayerIsSystemColumn($this->tmpTable);

            $this->UsersDbController->updateSequenceKVS();

            // Insert in layer_kvs
            // kad_no NOT exists in KVS
            $this->UsersDbController->addKvsOszPlots($this->tmpTable);

            // updates null document areas
            $this->UsersDbController->updateKVSDocumentArea($this->tmpTable, $ekate);

            // Remove Invalid And LineStrings from layer_kvs
            $this->UsersDbController->removeInvalidAndLineStringsKvsOszPlots($this->UsersDbController->DbHandler->tableKVS, 'gid');

            // Automatically replaces old plotс with the new ones that intersect
            $this->addInactivePlotsToHistory($this->tmpTable);
            $this->dropIntersectionViews($ekate);

            $this->updateKvsBorders($this->tmpTable);
        } catch (Exception $e) {
            $this->dropIntersectionViews($ekate);
            $this->mainException($e);

            throw $e;
        }
    }

    /**
     * onAfterProcessing - actions that we exucete after processing.
     *
     * @return bool
     */
    public function onAfterProcessing()
    {
        if (!$this->database) {
            return false;
        }

        $tableExists = $this->UsersDbController->getTableNameExist($this->tmpTable);

        if ($tableExists) {
            // update ekate in su_users_files
            $this->updateEkateUsersFiles();

            $this->validateAlgorithm();

            // remove the unziped dir
            $this->LayersController->File->removeFolder($this->userSubDir);

            // extent
            $maxExtent = $this->UsersDbController->getMaxExtent('layer_kvs');
            $maxExtent = str_replace('BOX(', '', $maxExtent);
            $maxExtent = str_replace(')', '', $maxExtent);
            $maxExtent = str_replace(',', ' ', $maxExtent);

            $options = [];
            $options['group_id'] = $this->groupID;
            $options['layer_type'] = Config::LAYER_TYPE_KVS;
            $layerID = $this->LayersController->getLayersIdByLayerType($options);

            $options = [];
            $options['mainData'] = [
                'extent' => $maxExtent,
            ];
            $options['id'] = $layerID;
            $this->LayersController->editItem($options);

            $options = [];
            $options['database'] = $this->database;
            $options['user_id'] = $this->groupID;
            $this->LayersController->updateUnstyledEkkates($this->database, $layerID);

            $this->generateLayerStyles($layerID, $this->data[0]['ekate']);
            $this->LayersController->generateMapFile($options);
            $this->UsersDbController->refreshEkateCombobox();

            system('/bin/chown -R ' . WMS_MAP_PATH);
        }
    }

    public function generateLayerStyles($layerId, $ekatte)
    {
        $styleLayerId = $layerId . '_' . $ekatte;
        $layer = UserLayers::getLayerById($layerId);

        $this->initAuthUser($layer->group_id);

        if (!empty($existingLayerStyle = LayerStyles::finder()->findAll('layer_id = :layer_id', [':layer_id' => $styleLayerId]))) {
            $styles = array_combine(array_column($existingLayerStyle, 'layer_id'), $existingLayerStyle);
            $layerStyle = $styles[$styleLayerId];
        } else {
            $layerStyle = LayerStyles::generateDefaultStyle(Config::LAYER_TYPE_KVS_OSZ, false);
            $layerStyle->layer_id = $styleLayerId;
            $layerStyle->table_name = $layer->table_name;
            $layerStyle->save();
        }

        $style = $layerStyle->toArray();
        $style['ekatte'] = $ekatte;

        $this->LayersController->saveLayerPersonalization($layer, $style, KvsOszProcessingClass::class, '');
    }

    private function updateKvsBorders()
    {
        $ekattes = $this->UsersDbController->getItemsByParams(
            [
                'tablename' => $this->tmpTable,
                'return' => [
                    'DISTINCT ekatte',
                ],
            ]
        );
        $ekattes = array_column($ekattes, 'ekatte');

        $this->UsersDbController->updateKvsBorders($ekattes);
    }

    private function validateAlgorithm()
    {
        $invalidData = $this->getInvalidGeomsData();

        if (count($invalidData)) {
            try {
                $this->UsersDbController->removeGeometryCollection($this->tmpTable);
                $this->UsersDbController->stMakeValid($this->tmpTable);
            } catch (Exception $e) {
            }

            $invalidData = $this->getInvalidGeomsData();
        }

        $tpmTableChunk = explode('_', $this->tmpTable);
        $ekate = end($tpmTableChunk);

        if (count($invalidData)) {
            // Записване на невалидните геометрии във временна таблица
            $this->addInvalidGeoms();

            // dump and reapir временната таблица с невалидните геометрии
            $this->dumpInvalidTable();

            $this->copyProcess();

            // ALTER TABLE tmp_table ALTER COLUMN geom SET DATA TYPE geometry;
            $this->UsersDbController->setDataTypeGeometry($this->tmpTable);

            // CREATE MATERIALIZED VIEW
            $this->createMaterializedView();

            // Частично обработен
            $options = [];
            $options['mainData'] = [
                'status' => PARTIALLY_PROCESSED,
            ];
            $options['id'] = $this->ID;
            $this->LayersController->editItemFiles($options);
        } else {
            $this->UsersDbController->dropTableKvsEkatteInvalid($this->tmpTable);

            $this->copyProcess();

            // ALTER TABLE tmp_table ALTER COLUMN geom SET DATA TYPE geometry;
            $this->UsersDbController->setDataTypeGeometry($this->tmpTable);

            // CREATE MATERIALIZED VIEW kvs_contracts_update_[ekate]
            $this->createMaterializedView();

            // Няма невалидни геометриии

            // Проверка за не актуализирани договори
            $options = [
                'tablename' => 'kvs_contracts_update_' . $ekate,
            ];
            $missingPlots = $this->UsersDbController->getMissingKVSPlots($options, false, false);

            if (count($missingPlots)) {
                // Status: Не актуализирани договори
                $options['mainData'] = [
                    'status' => NOT_UPDATED_CONTRACTS,
                ];
            } else {
                // Status: Успешно обработен
                $options['mainData'] = [
                    'status' => SUCCESSFULLY_TREATED,
                ];
            }

            $options['id'] = $this->ID;
            $this->LayersController->editItemFiles($options);
        }
    }

    private function createMaterializedView()
    {
        $tpmTableChunk = explode('_', $this->tmpTable);
        $ekate = end($tpmTableChunk);

        $kvsContractsUpdateView = 'kvs_contracts_update_' . $ekate;
        if (!$this->UsersDbController->getViewNameExists($kvsContractsUpdateView)) {
            $this->dateUploaded = strftime('%Y-%m-%d', strtotime($this->dateUploaded));

            $this->UsersDbController->createKvsContractsUpdateView($this->ID, $ekate, $this->dateUploaded);
        }
    }

    private function addInvalidGeoms()
    {
        $this->UsersDbController->createTableKvsEkatteInvalid($this->tmpTable);

        $this->UsersDbController->deleteInvalidKvsOszPlots($this->tmpTable);

        $this->UsersDbController->addInvalidKvsOszPlots($this->tmpTable);
    }

    private function dumpInvalidTable()
    {
        $options = [
            'tablename' => $this->tmpTable . '_invalid',
            'return' => [
                'id',
            ],
        ];

        $result = $this->UsersDbController->getItemsByParams($options, false, false);

        $id_array = [];
        $count = count($result);
        for ($i = 0; $i < $count; $i++) {
            $id_array[] = $result[$i]['id'];
        }

        $this->UsersDbController->addDumpedKvsOszPlots($this->tmpTable);

        if (count($id_array) > 0) {
            $options = [
                'tablename' => $this->tmpTable . '_invalid',
                'id_name' => 'id',
                'id_string' => implode(',', $id_array),
            ];
            $this->UsersDbController->deleteItemsByParams($options);
        }

        $this->UsersDbController->removeInvalidAndLineStringsKvsOszPlots($this->tmpTable . '_invalid', 'id');
    }

    private function getInvalidGeomsData()
    {
        $options = [
            'tablename' => $this->tmpTable,
            'return' => [
                'gid',
            ],
            'where' => [
                'geom' => ['column' => 'ST_IsValid(geom)', 'compare' => '=', 'value' => 'FALSE'],
            ],
        ];

        return $this->UsersDbController->getItemsByParams($options, false, false);
    }

    private function updateEkateUsersFiles()
    {
        $tpmTableChunk = explode('_', $this->tmpTable);

        $ekate = end($tpmTableChunk);

        $this->LayersController->updateUsersFilesEkate($this->ID, $ekate);
    }

    private function addInactivePlotsToHistory($tmpTable)
    {
        $tpmTableChunk = explode('_', $tmpTable);
        $ekate = end($tpmTableChunk);

        $oldPlots = $this->UsersDbController->getNewPlotsForHistoryLog($tmpTable, $ekate);

        $count = count($oldPlots);
        for ($i = 0; $i < $count; $i++) {
            $options = [
                'tablename' => $this->UsersDbController->DbHandler->tableKvsEditLog,
                'mainData' => [
                    'new_gid' => $oldPlots[$i]['gid'],
                    'old_gid' => $oldPlots[$i]['gid'],
                    'edit_type' => 'new_boundary',
                    'edit_date' => date('Y-m-d'),
                    'edit_active_from' => date('Y-m-d'),
                ],
            ];

            $this->UsersDbController->addItem($options);
        }
    }

    private function addIntersectionColumns()
    {
        $sql = '
            ALTER TABLE ' . $this->tmpTable . '
            ADD COLUMN IF NOT EXISTS "allowable_area" float8,
            ADD COLUMN IF NOT EXISTS "allowable_type" varchar(255)
            ';

        $addCommand = $this->UsersDbController->DbHandler->DbModule->createCommand($sql);
        $addCommand->execute();
    }

    /**
     * addIntersectionColumns - Добавя материализирани views, за отделните типове пресичане,
     * в който ще се съдържа информация за пресичането с отделните слоеве и връзка с временната таблица,
     * в която се зареждат КВС данни.
     */
    private function createIntersectionViews($ekate)
    {
        // create safe_intersection function in case it does not exist
        $this->LayersController->createSafeIntersection();
        $this->createAllowableView($ekate);
    }

    /**
     * Добавяне на view за пресичане с допустим слой.
     */
    private function createAllowableView($ekate)
    {
        $allowableMatView = 'allowable_areas_mat_view_' . $ekate;
        $sql = "
            DROP MATERIALIZED VIEW IF EXISTS {$allowableMatView};
            create MATERIALIZED view {$allowableMatView} as
                SELECT
                    kvs.gid,
                    string_agg(distinct(A .ntp)::text, ', ') as allowable_type,
                    round((st_area(safe_intersection(kvs.geom,st_union(a.geom))) / 1000)::numeric,3) as allowable_area
                FROM
                    {$this->tmpTable} kvs,
                    dblink (
                        'host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "'::text,
                        'SELECT
                            geom,
                            ntp
                        FROM
                            layer_allowable_final'
                    ) AS A (geom geometry, ntp VARCHAR)
                where st_intersects(kvs.geom, a.geom)";

        // ako ima nevalidni geometrii  - te da ne se presichat zashtoto shte gramne zaqvkata
        $invalidTableExists = $this->UsersDbController->getTableNameExist($this->tmpTable . '_invalid');
        if ($invalidTableExists) {
            $sql .= ' and kvs.gid not in (select fr_gid from ' . $this->tmpTable . '_invalid)';
        }

        $sql .= ' GROUP by kvs.gid';

        $addCommand = $this->UsersDbController->DbHandler->DbModule->createCommand($sql);
        $addCommand->execute();
    }

    private function updateTmpTableWithIntersectionAreas($ekate)
    {
        $allowableMatView = 'allowable_areas_mat_view_' . $ekate;
        $sql = "
            UPDATE {$this->tmpTable} kvs SET
            allowable_area = av.allowable_area,
            allowable_type = av.allowable_type
            FROM {$allowableMatView} av
            WHERE kvs.gid = av.gid
            ";

        $updateAllowableCommand = $this->UsersDbController->DbHandler->DbModule->createCommand($sql);
        $updateAllowableCommand->execute();
    }

    private function dropIntersectionViews($ekate)
    {
        $allowableMatView = 'allowable_areas_mat_view_' . $ekate;

        $sql = "
            DROP MATERIALIZED VIEW IF EXISTS {$allowableMatView};
            ";

        $dropCommand = $this->UsersDbController->DbHandler->DbModule->createCommand($sql);
        $dropCommand->execute();
    }

    private function createGeometryIndex()
    {
        $sql = "CREATE INDEX \"{$this->tmpTable}_geom_gist\" ON \"public\".\"{$this->tmpTable}\" USING gist (\"geom\" \"public\".\"gist_geometry_ops_2d\")";

        $dropCommand = $this->UsersDbController->DbHandler->DbModule->createCommand($sql);
        $dropCommand->execute();
    }
}
