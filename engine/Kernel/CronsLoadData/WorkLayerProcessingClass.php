<?php

namespace TF\Engine\Kernel\CronsLoadData;

use Exception;
use TF\Application\Common\Config;
use TF\Application\Entity\LayerStyles;
use TF\Application\Entity\UserLayers;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * <AUTHOR> <<EMAIL>>
 *
 * @link http://www.technofarm.bg/
 *
 * @copyright Copyright &copy; 2015 TechnoFarm Ltd.
 */
class WorkLayerProcessingClass extends AbstractLoadDataClass
{
    /**
     * init - prado init function.
     */
    public function init($config)
    {
        parent::init($config);
        $this->tmpTable = 'tmp_work_' . $this->ID;
    }

    /**
     * startProcessing.
     */
    public function startProcessing()
    {
        $this->mainProcessing();
    }

    /**
     * dataSource.
     *
     * @return array of data from DB
     */
    public function dataSource()
    {
        return $this->LayersController->getWorkLayersForProcessing();
    }

    /**
     * copyProcess executes the process to copy the data.
     */
    public function copyProcess()
    {
        try {
            $this->UsersDbController->transformColumnToGeometry('tmp_work_' . $this->ID);
        } catch (Exception $e) {
            throw new Exception($e->getMessage() . '\n=====' . $e->getTraceAsString(), ERROR_INVALID_GEOMETRY);
        }
        $layer = $this->createUserLayer();

        $tablename = $layer->table_name;

        $this->UsersDbController->copyDataFromTMPTable($tablename, $this->layerType, $layer->id, $this->definition, true, $this->ID);

        $maxExtent = $this->UsersDbController->getMaxExtent($tablename);
        $maxExtent = str_replace('BOX(', '', $maxExtent);
        $maxExtent = str_replace(')', '', $maxExtent);
        $maxExtent = str_replace(',', ' ', $maxExtent);
        $definitions = $this->generateDefinitions($tablename);

        $layer->extent = $maxExtent;
        $layer->is_exist = true;
        $layer->definitions = json_encode($definitions);
        $layer->save();

        $this->generateLayerStyles($layer->id);
    }

    /**
     * onAfterProcessing - we can call it after the Processing.
     */
    public function onAfterProcessing()
    {
        $this->data = $this->LayersController->getWorkLayersForProcessing(true);

        $this->data = $this->data[0];

        if (0 != count($this->data)) {
            $this->initClassVariables($this->data);
            // definition tables

            if ($this->database) {
                $this->UsersDbController = new UserDbController($this->database);// getPluginInstanceUserDb($this->database);
            }

            $definition = $this->data['definition'];
            $this->definition = unserialize($definition);

            try {
                $this->copyProcess();
            } catch (Exception $e) {
                throw new Exception($e->getMessage() . '\n=====' . $e->getTraceAsString(), ERROR_RUNTIME);
            }

            $options = [];
            $options['database'] = $this->database;
            $options['user_id'] = $this->groupID;
            $this->LayersController->generateMapFile($options);

            system('/bin/chown -R ' . WMS_MAP_PATH);

            $options = [];
            $options['mainData'] = [
                'status' => SUCCESSFULLY_TREATED,
            ];
            $options['id'] = $this->ID;
            $this->LayersController->editItemFiles($options);

            // removes temporary tables
            $this->UsersDbController->dropTableKvsEkatte($this->tmpTable);
        }
    }

    public function generateLayerStyles($layerId)
    {
        $layer = UserLayers::getLayerById($layerId);
        $this->initAuthUser($layer->group_id);

        $layerStyle = LayerStyles::generateDefaultStyle(Config::LAYER_TYPE_WORK_LAYER, false);
        $layerStyle->layer_id = $layer->id;
        $layerStyle->table_name = $layer->table_name;
        $layerStyle->save();

        $this->LayersController->saveLayerPersonalization($layer, $layerStyle->toArray(), self::class, '');
    }

    private function generateDefinitions(string $tablename)
    {
        $layerColumnCategoryNumber = Config::LAYER_COLUMN_CATEGORY_NUMBER;
        $layerColumnCategoryBoolean = Config::LAYER_COLUMN_CATEGORY_BOOLEAN;
        $layerColumnCategoryDate = Config::LAYER_COLUMN_CATEGORY_DATE;
        $layerColumnCategoryText = Config::LAYER_COLUMN_CATEGORY_TEXT;

        $defaultDefinitions = UserLayers::getDefinitionsByType(Config::LAYER_TYPE_WORK_LAYER);
        $defaultDefinitionsColumns = array_column($defaultDefinitions, 'col_name');

        $additionalColumns = $this->UsersDbController->getItemsByParams([
            'tablename' => 'information_schema.COLUMNS',
            'return' => [
                'column_name AS col_name',
                'column_name AS col_title',
                "CASE 
                    WHEN udt_name = 'numeric' OR udt_name ilike 'int%' OR udt_name ilike 'float%'
                        THEN 
                            '{$layerColumnCategoryNumber}'
                    WHEN udt_name = 'bool'
                        THEN '{$layerColumnCategoryBoolean}'
                    WHEN udt_name = 'timestamp'
                        THEN '{$layerColumnCategoryDate}'
                    ELSE '{$layerColumnCategoryText}'
                END AS col_category",
            ],
            'where' => [
                'table_name' => ['column' => 'table_name', 'compare' => '=', 'value' => $tablename],
                'column_name' => ['column' => 'column_name', 'compare' => 'NOT IN', 'value' => $defaultDefinitionsColumns],
            ],
        ]);

        $additionalDefinitions = array_map(function ($column) {
            $column['col_visible'] = true;
            $column['col_multiedit'] = true;
            $column['col_singleedit'] = true;
            $column['col_sortable'] = true;
            $column['col_exportable'] = true;
            $column['col_personalizable'] = true;
            $column['col_copyable'] = true;
            $column['col_virtual'] = false;
            $column['col_expression'] = null;
            $column['col_filter_selection_type'] = Config::LAYER_COLUMN_SELECTION_TYPE_MULTIPLE;
            $column['col_reference'] = null;

            return $column;
        }, $additionalColumns);

        return [
            ...$defaultDefinitions,
            ...$additionalDefinitions,
        ];
    }
}
