<?php

namespace TF\Engine\Kernel\CronsLoadData;

use Exception;
use Prado;
use Prado\TModule;
use TF\Application\Common\Config;
use TF\Application\Entity\User;
use TF\Application\Entity\UserLayers;
use TF\Engine\Kernel\DbfEncodingDetector;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * <AUTHOR> <<EMAIL>>
 *
 * @link http://www.technofarm.bg/
 *
 * @copyright Copyright &copy; 2015 TechnoFarm Ltd.
 *
 * @property LayersController $LayersController
 * @property UserDbController $UsersDbController
 */
abstract class AbstractLoadDataClass extends TModule
{
    public const PGSQL_CREATE = '-c';
    public const PGSQL_DELETE = '-d';
    public const PGSQL_APPEND = '-a';

    public const OGR2OGR_APPEND = '-append';
    public const OGR2OGR_OVERWRITE = '-overwrite';
    protected $fileData;
    protected $ID;
    protected $fileName;
    protected $dateUploaded;
    protected $status;
    protected $errors;
    protected $farming;
    protected $year;
    protected $userID;
    protected $name;
    protected $layerType;
    protected $definition;
    protected $groupID;
    protected $deviceType;
    protected $database;
    protected $addToExisting;

    protected $filePath;
    protected $userDir;
    protected $userSubDir;
    protected $modulesPath;
    protected $tmpTable;
    protected $processingSubDir;

    protected $LayersController;
    protected $FarmingController;
    protected $UsersDbController;

    protected $UsersController;

    protected $shapeFile;
    protected $dbfFile;
    protected $agfFile;
    protected $shapeFlag;
    protected $dbfFlag;
    protected $tmpCRS;

    protected $data;

    protected $tableName;

    protected $layerExtent;

    /**
     * init - prado init function.
     * this init is called on every request. Should be reworked.
     */
    public function init($config)
    {
        $this->LayersController = new LayersController('Layers');
        $this->FarmingController = new FarmingController('Farming');
        $this->UsersController = new UsersController('Users');

        $this->initData();
    }

    // Общи методи
    /**
     * initData init data.
     */
    public function initData()
    {
        $this->data = $this->dataSource();

        if (count($this->data)) {
            $this->initClassVariables($this->data[0]);
        }

        if ($this->database) {
            $this->UsersDbController = new UserDbController($this->database);
        }
    }

    /**
     * initClassVariables - init Class Variables.
     *
     * @param array $currentData - a row data from table "su_users_files"
     */
    public function initClassVariables($currentData)
    {
        $this->fileData = $currentData;

        $this->ID = $this->fileData['id'];
        $this->fileName = $this->fileData['filename'];
        $this->dateUploaded = $this->fileData['date_uploaded'];
        $this->status = $this->fileData['status'];
        $this->errors = $this->fileData['errors'];
        $this->farming = $this->fileData['farming'];
        $this->year = $this->fileData['year'];
        $this->userID = $this->fileData['user_id'];
        $this->name = str_replace(' ', '_', $this->fileData['name']);
        $this->name = str_replace('(', '_', $this->name);
        $this->name = str_replace(')', '_', $this->name);
        $this->layerType = $this->fileData['shape_type'];
        $this->definition = $this->fileData['definition'];
        $this->groupID = $this->fileData['group_id'];
        $this->deviceType = $this->fileData['device_type'];
        $this->addToExisting = $this->fileData['add_to_existing'];

        $this->database = $this->fileData['database'];

        $this->filePath = LAYERS_QUEUE_PATH . $currentData['filename'];
        $this->userDir = LAYERS_QUEUE_PATH . $currentData['user_id'];
        $ext_pos = strrpos($currentData['filename'], '.');
        $fileNameWOExt = substr($currentData['filename'], 0, $ext_pos);
        $this->userSubDir = $this->userDir . '/' . $fileNameWOExt . '/';

        $this->modulesPath = SITE_PATH . 'crons/';

        // init default tmp Table
        $this->tmpTable = 'tmp_geom_' . $this->ID;
    }

    /**
     * errorLog - updates the errors field in DB of the processing file.
     *
     * @param int $error - code error
     * @param string $message - error message
     */
    public function errorLog($error, $message = '')
    {
        // find correct error in the error message
        $error = $this->catchError($error, $message);

        $this->LayersController->log(1, 'cron-daemon', $error, [$this->ID]);

        $options = [];
        $options['mainData'] = [
            'status' => $error,
        ];

        if (strlen($message)) {
            $options['mainData']['errors'] = $message;
        }

        $options['id'] = $this->ID;
        $this->LayersController->editItemFiles($options);

        // remove the unziped dir
        $this->LayersController->File->removeFolder($this->userSubDir);
    }

    /**
     * catchCorrectError - find correct error in the error message.
     *
     * @param int $error
     * @param string $message
     *
     * @return int
     */
    public function catchError($error, $message = '')
    {
        if (strlen($message)) {
            // below we can add many checks for differnt erros

            if ($this->findError("transform: couldn't project point", $message)) {
                $error = ERROR_INVALID_CRS;
            }

            if ($this->findError('One of the geometries in the set could not be converted to GEOS', $message)) {
                $error = ERROR_INVALID_GEOMETRY;
            }

            if ($this->findError('Unable to convert data', $message)) {
                $error = ERROR_INCORRECT_ENCODING;
            }

            if ($this->findError('Unable to convert field name', $message)) {
                $error = ERROR_INCORRECT_ENCODING_FIELD;
            }

            if ($this->findError('Geometry type (GeometryCollection)', $message)) {
                $error = ERROR_GEOMETRY_COLLECTION;
            }

            if ($this->findError('Error in fread()', $message) || $this->findError('Error reading shape object', $message)) {
                $error = ERROR_READING_SHAPE_OBJECT;
            }

            if ($this->findError('invalid input syntax', $message)) {
                $error = ERROR_INVALID_INPUT_SYNTAX;
            }

            if ($this->findError('failed to execute the SQL statement', $message)) {
                $error = ERROR_RUNTIME;
            }

            if ($this->findError('sh: shp2pgsql: not found', $message)) {
                $error = ERROR_SHP2PSQL_NOT_FOUND;
            }
        }

        return $error;
    }

    /**
     * findError - find if the error contains in the error message.
     *
     * @param string $search
     * @param string $message
     *
     * @return bool
     */
    public function findError($search, $message = '')
    {
        $pos = strpos($message, $search);

        return (bool) (false !== $pos);
    }

    /**
     * We can call it before the Processing.
     */
    public function onBeforeProcessing()
    {
        $this->LayersController->setFilesProcessingStatus($this->ID, LOADING_FILE_NOW);
        $options = [
            'farming' => $this->farming,
            'year' => $this->year,
            'group_id' => $this->groupID,
            'layer_type' => $this->layerType,
        ];
        if (Config::LAYER_TYPE_WORK_LAYER == $this->layerType) {
            $options['farming'] = null;
            $options['year'] = null;
        }
        $tableName = $this->LayersController->getTableNameByParams($options);
        // Create a new layer if not exist but only for the chosen layer types
        if (empty($tableName) && in_array(
            $this->layerType,
            [
                Config::LAYER_TYPE_KMS,
                Config::LAYER_TYPE_ISAK,
            ]
        )) {
            $layer = $this->createUserLayer();
            $tableName = $layer->table_name;
        }

        $this->tableName = $tableName;
    }

    /**
     * mainProcessing - executes the main process.
     *
     * @return bool
     */
    public function mainProcessing()
    {
        $this->onBeforeProcessing();

        if (0 != count($this->data)) {
            try {
                $this->unzipData();

                if (Config::DEVICE_OSZ == $this->deviceType) {
                    // osz items
                    $this->processSimpleItems();
                } elseif (Config::DEVICE_TOPCON == $this->deviceType || Config::DEVICE_MUELLER == $this->deviceType) {
                    // topcon or mueller items
                    $this->processTopconOrMuellerItems();
                } elseif (Config::DEVICE_TRIMBLE == $this->deviceType) {
                    // check if main required directories exist
                    if (file_exists($this->userSubDir . 'AgGPS/Data')) {
                        // trimble items
                        $this->processAgGpsItems();
                    } elseif (file_exists($this->userSubDir . 'AgData')) {
                        $this->processAgDataItems();
                    } else {
                        throw new Exception(__METHOD__ . ' Line:' . __LINE__, ERROR_INVALID_ARCHIVE);
                    }
                } elseif (Config::DEVICE_UNKNOWN == $this->deviceType) {
                    // satelite
                    $this->processSateliteItems();
                }

                $this->finalActions();
            } catch (Exception $e) {
                $this->mainException($e);

                return false;
            }
        }

        try {
            $this->onAfterProcessing();
        } catch (Exception $e) {
            $this->mainException($e);

            return false;
        }
    }

    /**
     * checkForNotAllowedAdding check For Not Allowed Adding.
     */
    public function checkForNotAllowedAdding()
    {
        $options = [];
        $options['farming'] = $this->farming;
        $options['year'] = $this->year;
        $options['group_id'] = $this->groupID;
        $options['layer_type'] = $this->layerType;
        $tableName = $this->LayersController->getTableNameByParams($options);

        if (Config::LAYER_TYPE_ISAK == $this->layerType) {
            if ($this->UsersDbController->getTableNameExist($tableName)) {
                $options = [
                    'tablename' => $tableName,
                ];

                // do not insert if we have records and update it as ERROR_NOT_ALLOWED_ADDING
                $counter = $this->UsersDbController->getItemsByParams($options, true, false);
                if ($counter[0]['count']) {
                    throw new Exception(__METHOD__ . ' Line:' . __LINE__, ERROR_NOT_ALLOWED_ADDING);
                }
            }
        }
    }

    /**
     * mainProcessingSimple - executes a simple main process.
     */
    public function mainProcessingSimple()
    {
        $this->onBeforeProcessing();

        if (0 != count($this->data)) {
            try {
                $this->checkForNotAllowedAdding();

                $this->unzipData();

                $this->processSimpleItems();

                $this->finalActions();
            } catch (Exception $e) {
                $this->mainException($e);

                return false;
            }
        }

        try {
            $this->onAfterProcessing();
        } catch (Exception $e) {
            $this->mainException($e);

            return false;
        }
    }

    /**
     * mainException manage errorLog.
     *
     * @param Exception $e
     */
    public function mainException($e)
    {
        $errorData = $e->getMessage();
        $erroCode = $e->getCode();

        if (0 != $erroCode) {
            $this->errorLog($erroCode, $errorData);
        } else {
            $this->errorLog(ERROR_RUNTIME, $errorData);
        }
    }

    /**
     * onAfterProcessing - we can call it after the Processing.
     */
    public function onAfterProcessing() {}

    /**
     * unzipData - unzip the .zip file.
     */
    public function unzipData()
    {
        try {
            if (!file_exists($this->userDir)) {
                @mkdir($this->userDir, 0777);
            }

            $this->LayersController->File->removeFolder($this->userSubDir);
            @mkdir($this->userSubDir, 0777);

            // remove .zip file from "layers_queue" to the userDir and set $this->filePath
            if (file_exists($this->filePath)) {
                rename($this->filePath, $this->userDir . '/' . $this->fileName);
                $this->filePath = $this->userDir . '/' . $this->fileName;
            } else {
                $this->filePath = $this->userDir . '/' . $this->fileName;
            }

            $fileDataNoWS = str_replace(' ', '_', $this->filePath);
            rename($this->filePath, $fileDataNoWS);

            $renameOpts = [
                'mainData' => [
                    'filename' => str_replace(' ', '_', $this->fileName),
                ],
                'id' => $this->ID,
            ];
            $this->LayersController->editItemFiles($renameOpts);
            $c = "unzip -o \"{$fileDataNoWS}\" -d {$this->userSubDir} 2>&1";
            system($c, $out);
        } catch (Exception $e) {
            throw new Exception($e->getMessage() . '\n=====' . $e->getTraceAsString(), ERROR_INVALID_ARCHIVE);
        }
    }

    /**
     * processSimpleItems - process a zip with 1 .shp and 1 .dbf file.
     *
     * @throws Exception
     */
    public function processSimpleItems()
    {
        $this->processingSubDir = $this->userSubDir;
        $files = $this->LayersController->File->getFilesFromDir($this->processingSubDir);
        $dirs = $this->LayersController->File->getDirsFromDir($this->processingSubDir);
        $shpFilesCount = count(array_filter($files, function ($file) {
            return 'shp' == strtolower($this->LayersController->StringHelper->GetFileExtentionByPath($this->processingSubDir . $file['name']));
        }));

        $this->shapeFile = false;
        $this->dbfFile = false;

        if (0 === $shpFilesCount && 1 === count($dirs)) {
            // There are no files in the directory, but only one subdirectory
            $this->processingSubDir .= $dirs[0]['name'] . DIRECTORY_SEPARATOR;
            $files = $this->LayersController->File->getFilesFromDir($this->processingSubDir);
        }

        // Проверяваме дали във архива има файлове с имена започващи с "prc_", ако има зареждаме тях, ако не връщаме оригиналния array с файлове
        $files = $this->filterFiles($files, 'prc_');
        if (Config::LAYER_TYPE_KVS_OSZ != $this->layerType) {
            $newName = $this->LayersController->StringHelper->str_makerand(15, 15, true, false, true);
        }

        // set Shape and Dbf Files
        $filesCount = count($files);
        for ($j = 0; $j < $filesCount; $j++) {
            if ($files[$j] && 'shp' == strtolower($this->LayersController->StringHelper->GetFileExtentionByPath($this->processingSubDir . $files[$j]['name']))) {
                if (Config::LAYER_TYPE_KVS_OSZ != $this->layerType) {
                    $shpName = $newName . '.shp';
                    rename($this->processingSubDir . $files[$j]['name'], $this->processingSubDir . $shpName);
                    $this->shapeFile = $shpName;
                } else {
                    $this->shapeFile = $files[$j]['name'];
                }
            }

            if ($files[$j] && 'dbf' == strtolower($this->LayersController->StringHelper->GetFileExtentionByPath($this->processingSubDir . $files[$j]['name']))) {
                if (Config::LAYER_TYPE_KVS_OSZ != $this->layerType) {
                    $dbfName = $newName . '.dbf';
                    rename($this->processingSubDir . $files[$j]['name'], $this->processingSubDir . $dbfName);
                    $this->dbfFile = $dbfName;
                } else {
                    $this->dbfFile = $files[$j]['name'];
                }
            }

            if ($files[$j] && 'shx' == strtolower($this->LayersController->StringHelper->GetFileExtentionByPath($this->processingSubDir . $files[$j]['name']))) {
                if (Config::LAYER_TYPE_KVS_OSZ != $this->layerType) {
                    $shxName = $newName . '.shx';
                    rename($this->processingSubDir . $files[$j]['name'], $this->processingSubDir . $shxName);
                }
            }
        }

        if (!$this->shapeFile) {
            throw new Exception(__METHOD__ . ' Line:' . __LINE__, ERROR_INVALID_SHAPE);
        }

        if (!$this->dbfFile) {
            throw new Exception(__METHOD__ . ' Line:' . __LINE__, ERROR_INVALID_DBF);
        }

        if (strtolower(strstr('.', $this->dbfFile)) !== strtolower(strstr('.', $this->shapeFile))) {
            throw new Exception(__METHOD__ . ' Line:' . __LINE__, ERROR_INVALID_ARCHIVE);
        }

        // osz items
        if (Config::DEVICE_OSZ == $this->deviceType) {
            $rebuildRes = [];
            exec("/usr/bin/python3 {$this->modulesPath}rebuild_shp.py \"{$this->processingSubDir}{$this->shapeFile}\" \"{$this->processingSubDir}{$this->dbfFile}\" 2>&1", $rebuildRes);
        }

        if (Config::LAYER_TYPE_KVS == $this->layerType) {
            $this->tmpTable = 'tmp_kvs_' . $this->data[0]['id'];
        }

        if (Config::LAYER_TYPE_KVS_OSZ == $this->layerType) {
            // set $this->tmpTable with EKATTE prefix and validate the file
            $tablePrefix = $this->getEkatteFromFile();
            $this->tmpTable = 'layer_tmp_kvs_' . $tablePrefix;

            // DROP MATERIALIZED VIEW IF EXISTS
            $this->UsersDbController->dropMaterializedView('kvs_contracts_update_' . $tablePrefix);
        }

        // shp2pgsql common
        $this->commonSHP2PSQL();
    }

    public function commonOGR2OGR($action)
    {
        /** @var DbfEncodingDetector */
        $dbfEncodingDetector = new DbfEncodingDetector();

        $shpEncoding = $dbfEncodingDetector->detect($this->processingSubDir . $this->dbfFile);
        $cmdOGR2OGR = 'ogr2ogr -nlt PROMOTE_TO_MULTI -a_srs "EPSG:' . $this->tmpCRS . '' . $action . '" -lco GEOMETRY_NAME=geom -lco FID=gid -f "PostgreSQL" PG:"host=' . DBLINK_HOST . ' user=' . DBLINK_USERNAME . ' dbname=' . $this->database . ' password=' . DBLINK_PASSWORD . ' port=' . DBLINK_PORT . '" "' . $this->processingSubDir . $this->shapeFile . '" -nln ' . $this->tmpTable . ' --config SHAPE_ENCODING ' . $shpEncoding;

        var_export($cmdOGR2OGR);

        exec($cmdOGR2OGR . ' 2>&1', $output);
        if (
            (!empty($output[0]) && str_contains($output[0], 'Non UTF-8 content'))
            || (!empty($output[1]) && str_contains($output[1], 'encoding'))
        ) {
            $cmdOGR2OGR = 'ogr2ogr -nlt PROMOTE_TO_MULTI -a_srs "EPSG:' . $this->tmpCRS . '' . $action . '" -lco GEOMETRY_NAME=geom -lco FID=gid -f "PostgreSQL" PG:"host=' . DBLINK_HOST . ' user=' . DBLINK_USERNAME . ' dbname=' . $this->database . ' password=' . DBLINK_PASSWORD . ' port=' . DBLINK_PORT . '" "' . $this->processingSubDir . $this->shapeFile . '" -nln ' . $this->tmpTable . ' --config SHAPE_ENCODING WINDOWS-1251';
            var_export($cmdOGR2OGR);
            exec($cmdOGR2OGR . ' 2>&1', $output);
        }
    }

    /**
     * processTopconOrMuellerItems - process Topcon Or Mueller Items.
     */
    public function processTopconOrMuellerItems()
    {
        // check if main required directories exist
        if (
            Config::DEVICE_MUELLER == $this->deviceType
            && !(
                is_dir($this->userSubDir . 'NavGuideGisImport')
                || is_dir($this->userSubDir . 'NavGuideExport')
                || is_dir($this->userSubDir . 'GIS')
                || is_dir($this->userSubDir . 'SHP')
            )
        ) {
            throw new Exception(__METHOD__ . ' Line:' . __LINE__, ERROR_INVALID_ARCHIVE);
        }

        $this->processingSubDir = $this->userSubDir;
        $files = $this->LayersController->File->getFilesFromDir($this->processingSubDir);
        $dirs = $this->LayersController->File->getDirsFromDir($this->processingSubDir);

        $shpFilesCount = count(array_filter($files, function ($file) {
            return 'shp' == strtolower($this->LayersController->StringHelper->GetFileExtentionByPath($this->processingSubDir . $file['name']));
        }));

        if (0 === $shpFilesCount && 1 === count($dirs)) {
            // There are no files in the directory, but only one subdirectory
            $this->processingSubDir .= $dirs[0]['name'] . DIRECTORY_SEPARATOR;
            $files = $this->LayersController->File->getFilesFromDir($this->processingSubDir);
        }

        $used_files = [];

        for ($j = 0; $j < count($files); $j++) {
            if ($files[$j] && 'shp' == strtolower($this->LayersController->StringHelper->GetFileExtentionByPath($this->processingSubDir . $files[$j]['name'])) && !in_array($files[$j]['name'], $used_files)) {
                $newName = $this->LayersController->StringHelper->str_makerand(15, 15, true, false, true);
                $orignalName = pathinfo($this->processingSubDir . $files[$j]['name'], PATHINFO_FILENAME);
                $used_files[] = $files[$j]['name'];
                for ($k = 0; $k < count($files); $k++) {
                    if ($files[$k] && 'shp' == strtolower($this->LayersController->StringHelper->GetFileExtentionByPath($this->processingSubDir . $files[$k]['name'])) && pathinfo($this->processingSubDir . $files[$k]['name'], PATHINFO_FILENAME) == $orignalName) {
                        $shpName = $newName . '.shp';
                        rename($this->processingSubDir . $files[$k]['name'], $this->processingSubDir . $shpName);
                    }
                    if ($files[$k] && 'dbf' == strtolower($this->LayersController->StringHelper->GetFileExtentionByPath($this->processingSubDir . $files[$k]['name'])) && pathinfo($this->processingSubDir . $files[$k]['name'], PATHINFO_FILENAME) == $orignalName) {
                        $dbfName = $newName . '.dbf';
                        rename($this->processingSubDir . $files[$k]['name'], $this->processingSubDir . $dbfName);
                    }
                    if ($files[$k] && 'shx' == strtolower($this->LayersController->StringHelper->GetFileExtentionByPath($this->processingSubDir . $files[$k]['name'])) && pathinfo($this->processingSubDir . $files[$k]['name'], PATHINFO_FILENAME) == $orignalName) {
                        $shxName = $newName . '.shx';
                        rename($this->processingSubDir . $files[$k]['name'], $this->processingSubDir . $shxName);
                    }
                }
            }
        }

        $files = $this->LayersController->File->getFilesFromDir($this->processingSubDir);

        $used_files = [];

        for ($j = 0; $j < count($files); $j++) {
            $this->shapeFile = false;
            $this->dbfFile = false;
            $k = 0;

            for ($k = 0; $k < count($files); $k++) {
                if ($files[$k] && 'shp' == strtolower($this->LayersController->StringHelper->GetFileExtentionByPath($this->processingSubDir . $files[$k]['name']))
                && !in_array($files[$k]['name'], $used_files)) {
                    $this->shapeFile = $files[$k]['name'];
                    $used_files[] = $this->shapeFile;
                    $this->shapeFlag = true;
                    $fileParts = pathinfo($this->processingSubDir . $files[$k]['name']);
                    $this->dbfFile = $fileParts['filename'] . '.dbf';

                    if (file_exists($this->processingSubDir . $this->dbfFile)) {
                        $this->dbfFlag = true;
                    } else {
                        // ponqkoga razshirenito e s glavni bukvi
                        $this->dbfFile = $fileParts['filename'] . '.DBF';

                        if (file_exists($this->processingSubDir . $this->dbfFile)) {
                            $this->dbfFlag = true;
                        } else {
                            throw new Exception(__METHOD__ . ' Line:' . __LINE__, ERROR_INVALID_DBF);
                        }
                    }

                    break;
                }
            }

            if (!$this->shapeFile) {
                if (0 == $j) {
                    throw new Exception(__METHOD__ . ' Line:' . __LINE__, ERROR_INVALID_SHAPE);
                }

                break;
            }

            if (!$this->dbfFile) {
                if (0 == $j) {
                    throw new Exception(__METHOD__ . ' Line:' . __LINE__, ERROR_INVALID_DBF);
                }

                break;
            }

            if (0 == $j) {
                // shp2pgsql commmon
                $this->commonSHP2PSQL();
            } else {
                // shp2pgsql custom
                $this->commonSHP2PSQL(self::PGSQL_APPEND);
            }
        }
    }

    /**
     * processAgGpsItems - process Trimble Items.
     */
    public function processAgGpsItems()
    {
        // creating the final directory array
        $final_dirs = [];

        // getting client directories
        $client_dirs = $this->LayersController->File->getDirsFromDir($this->userSubDir . 'AgGPS/Data');
        if (count($client_dirs)) {
            // J is iteration for client directories
            for ($j = 0; $j < count($client_dirs); $j++) {
                $farming_dirs = $this->LayersController->File->getDirsFromDir($this->userSubDir . 'AgGPS/Data/' . $client_dirs[$j]['name']);
                // if farming directories were found
                if (count($farming_dirs)) {
                    // K is iteration for farming directories
                    for ($k = 0; $k < count($farming_dirs); $k++) {
                        $dir_name = $this->userSubDir . 'AgGPS/Data/' . $client_dirs[$j]['name'] . '/' . $farming_dirs[$k]['name'];
                        $plot_dirs = $this->LayersController->File->getDirsFromDir($dir_name);

                        // if plot directories were found
                        if (count($plot_dirs)) {
                            // L is iteration for plot directories
                            for ($l = 0; $l < count($plot_dirs); $l++) {
                                if (is_array($plot_dirs[$l])) {
                                    $final_dirs[] = $dir_name . '/' . $plot_dirs[$l]['name'] . '/';
                                }
                            }
                        }
                    }
                }
            }
        }

        // Добавено е сортиране по най-дългото име на директория,
        // за да може да се използва то като условие за дължина на колоните
        // които ще създаде shp2sql
        usort($final_dirs, function ($a, $b) {
            return strlen($b) - strlen($a);
        });

        // create iterations flag
        $iteration = 0;
        $this->shapeFlag = false;
        $this->dbfFlag = false;

        // check if any directory with correct structure was found
        if (count($final_dirs)) {
            for ($j = 0; $j < count($final_dirs); $j++) {
                $this->processingSubDir = $final_dirs[$j];

                $files = $this->LayersController->File->getFilesFromDir($this->processingSubDir);
                $this->shapeFile = false;
                $this->dbfFile = false;

                // check if dbf and shp file exists
                for ($k = 0; $k < count($files); $k++) {
                    if ($files[$k] && 'Boundary.dbf' == $files[$k]['name']) {
                        $this->dbfFile = $files[$k]['name'];
                        $this->dbfFlag = true;
                    }
                    if ($files[$k] && 'Boundary.shp' == $files[$k]['name']) {
                        $this->shapeFile = $files[$k]['name'];
                        $this->shapeFlag = true;
                    }
                }

                // if shape and dbf files were found we can proceed
                if ($this->shapeFile && $this->dbfFile) {
                    if (0 == $iteration) {
                        $this->commonSHP2PSQL();
                    } else {
                        $this->commonSHP2PSQL(self::PGSQL_APPEND);
                    }

                    $iteration++;
                }
            }
        } else {
            throw new Exception(__METHOD__ . ' Line:' . __LINE__, ERROR_INVALID_ARCHIVE);
        }
    }

    /**
     * processAgDataItems - process Trimble Items.
     *
     * @throws Exception
     */
    public function processAgDataItems()
    {
        $this->agfFile = true;
        $convertedDir = 'converted';

        $this->convertAgData($this->userSubDir, $convertedDir);
        $agDataJsonFiles = $this->getAgDataJsonFiles($this->userSubDir, $convertedDir);

        foreach ($agDataJsonFiles as $agDataJsonFile) {
            $fileInfo = json_decode(file_get_contents($agDataJsonFile), true);
            if (!isset($fileInfo['features'])) {
                throw new Exception('There is not valid json (geojson) data in: ' . $agDataJsonFile, ERROR_INVALID_FILE_DATA);
            }

            $sql = $this->generateSqlForLayerTable($this->tmpTable, $fileInfo['features']);
        }

        $this->UsersDbController->DbHandler->getDataByQuery($sql);
    }

    /**
     * processSateliteItems - process items in GeoScan.
     */
    public function processSateliteItems()
    {
        if (file_exists($this->userSubDir . 'AgGPS/Data')) {
            $this->processAgGpsItems();
        } else {
            $this->processSimpleItems();
        }
    }

    /**
     * commonSHP2PSQL - insert geoms from shapeFile to DB.
     *
     * @param string $action - type of the action(create or delete)
     *
     * @throws Exception
     *
     * @return string
     */
    public function commonSHP2PSQL($action = null)
    {
        var_export("commonSHP2PSQL \n");

        $gisIndexParam = '';
        if ($action) {
            $createTmpSatCmd = $action;
        } elseif (!$this->UsersDbController->getTableNameExist($this->tmpTable)) {
            $createTmpSatCmd = self::PGSQL_CREATE;
        } else {
            $createTmpSatCmd = self::PGSQL_DELETE;
        }

        if (self::PGSQL_CREATE == $createTmpSatCmd || self::PGSQL_DELETE == $createTmpSatCmd) {
            $gisIndexParam = '-I';
        }

        // absolute path to shape file
        $absoluteShpPath = $this->processingSubDir . $this->shapeFile;

        // get project info
        $prjInfo = $this->getPrjInfo($absoluteShpPath);
        $crs = $this->getPrjCode($prjInfo);

        if (!$crs) {
            $polygon = $this->getPolyFromOgrInfoExtent($prjInfo);

            $isPolyUTM = $this->LayersController->isPolyInBulgaria($polygon, 32635);

            if ($isPolyUTM) {
                $crs = 32635;
            } elseif ($this->LayersController->isPolyInBulgaria($polygon, 4326)) {
                $crs = 4326;
            } else {
                throw new Exception(__METHOD__ . ' Line:' . __LINE__, ERROR_INVALID_CRS);
            }
        }

        $this->tmpCRS = $crs;

        // update file with srid
        $this->LayersController->updateUsersFilesCrs($this->ID, $crs);

        // try with UTF8
        $shpEncoding = $this->getEncoding();
        $cmdSHP2pgsql = SHP2PGSQL_PATH . " -t 2D {$gisIndexParam} {$createTmpSatCmd} -W {$shpEncoding} -s {$crs} -g geom \"{$this->processingSubDir}{$this->shapeFile}\" {$this->tmpTable}";

        var_export($cmdSHP2pgsql);

        exec($cmdSHP2pgsql . ' 2>&1', $output);
        $output = $this->cleanStringOutput($output);

        // catch if there is error
        $error = $this->catchError(LOADING_FILE, $output);

        $ogr2ogrAction = (self::PGSQL_APPEND == $action) ? self::OGR2OGR_APPEND : self::OGR2OGR_OVERWRITE;

        if (ERROR_READING_SHAPE_OBJECT == $error || ERROR_GEOMETRY_COLLECTION == $error || $error = ERROR_SHP2PSQL_NOT_FOUND) {
            return $this->commonOGR2OGR($ogr2ogrAction);
        }

        if (LOADING_FILE_NOW != $error) {
            if (ERROR_INCORRECT_ENCODING == $error || ERROR_INCORRECT_ENCODING_FIELD == $error) {
                // try with CP1251
                $shpEncoding = 'CP1251';
                $cmdSHP2pgsql = SHP2PGSQL_PATH . " -t 2D {$gisIndexParam} {$createTmpSatCmd} -W {$shpEncoding} -s {$crs} -g geom \"{$this->processingSubDir}{$this->shapeFile}\" {$this->tmpTable}";

                var_export($cmdSHP2pgsql);
                exec($cmdSHP2pgsql . ' 2>&1', $output);
                $output = $this->cleanStringOutput($output);

                $error = $this->catchError(LOADING_FILE_NOW, $output);
                if (LOADING_FILE_NOW != $error) {
                    throw new Exception($output, $error);
                }
            }
        }
        // left here this exec call because last item of output array return string
        // Shapefile TYPE : Polygon Postgis TYPE : MULTIPOLYGON [ 2 ]
        // which generates an sql syntax error
        if (ERROR_INCORRECT_ENCODING == $error || ERROR_INCORRECT_ENCODING_FIELD == $error) {
            throw new Exception($output, $error);
        }
        exec($cmdSHP2pgsql, $output);
        $sql = $this->cleanStringOutput($output);

        try {
            $this->UsersDbController->DbHandler->getDataByQuery($sql);
        } catch (Exception $e) {
            $this->commonOGR2OGR($ogr2ogrAction);
        }
    }

    public function getPolyFromOgrInfoExtent($ogrinfo)
    {
        $pattern = '/Extent: \\((?<xmin>[\\d]+\\.[\\d]+), (?<ymin>[\\d]+\\.[\\d]+)\\) - \\((?<xmax>[\\d]+\\.[\\d]+), (?<ymax>[\\d]+\\.[\\d]+)\\)/';
        preg_match($pattern, $ogrinfo, $extent);

        // coordinates
        $xMin = $extent['xmin'];
        $YMin = $extent['ymin'];
        $xMax = $extent['xmax'];
        $yMax = $extent['ymax'];

        return 'POLYGON((' . $xMin . ' ' . $YMin . ',' . $xMax . ' ' . $YMin . ',' . $xMax . ' ' . $yMax . ',' . $xMin . ' ' . $yMax . ',' . $xMin . ' ' . $YMin . '))';
    }

    /**
     * finalActions - final actions that we execute.
     */
    public function finalActions()
    {
        if (Config::LAYER_TYPE_WORK_LAYER == $this->layerType) {
            if (($this->shapeFile && $this->dbfFile) || ($this->shapeFlag && $this->dbfFlag || $this->agfFile)) {
                $this->makeValidGeom();

                $options = [];
                $options['mainData'] = [
                    'status' => ERROR_WAITING_DEFINITION,
                ];
                $options['id'] = $this->ID;
                $this->LayersController->editItemFiles($options);

                // remove the unzipped dir
                $this->LayersController->File->removeFolder($this->userSubDir);
            } else {
                throw new Exception(__METHOD__ . ' Line:' . __LINE__, ERROR_INVALID_ARCHIVE);
            }

            return;
        }

        if (($this->shapeFile && $this->dbfFile) || ($this->shapeFlag && $this->dbfFlag)) {
            $this->makeValidGeom();

            $this->tmpCRS = $this->UsersDbController->getLayerCRS($this->tmpTable);

            $this->copyProcess();

            $options = [];
            $options['database'] = $this->database;
            $options['user_id'] = $this->groupID;
            $this->LayersController->generateMapFile($options);

            system('/bin/chown -R ' . WMS_MAP_PATH);

            // remove the unziped dir
            $this->LayersController->File->removeFolder($this->userSubDir);

            $options = [];
            $options['mainData'] = [
                'status' => SUCCESSFULLY_TREATED,
            ];
            $options['id'] = $this->ID;

            $this->LayersController->editItemFiles($options);

            // remove the tmp table
            $this->UsersDbController->dropTableKvsEkatte($this->tmpTable);
        } else {
            throw new Exception(__METHOD__ . ' Line:' . __LINE__, ERROR_INVALID_ARCHIVE);
        }
    }

    /**
     * makeValidGeom - make Valid Geom.
     */
    public function makeValidGeom()
    {
        $isValidGeom = $this->UsersDbController->getValidGeom($this->tmpTable);
        if (!$isValidGeom) {
            $this->UsersDbController->removeGeometryCollection($this->tmpTable);
            $this->UsersDbController->stMakeValid($this->tmpTable);
            $isValidGeom = $this->UsersDbController->getValidGeom($this->tmpTable);
            if (!$isValidGeom) {
                throw new Exception(__METHOD__ . ' Line:' . __LINE__, ERROR_INVALID_GEOMETRY);
            }
        }
    }

    protected function initAuthUser($organizationId)
    {
        $application = Prado::getApplication();

        $organization = User::finder()->find(
            'group_id = :group_id',
            [':group_id' => $organizationId]
        );

        if (!$organization) {
            throw new Exception('No organization found');
        }

        [$user] = $organization->getOrganizationUsers();

        if (!$user) {
            throw new Exception('No user found');
        }

        $application->getModule('auth')->switchUser($user->getUsername());
    }

    // Методи, които трябва да бъдат дефинирани в дъщерния клас
    /**
     * startProcessing function that starts the process of loading.
     */
    abstract protected function startProcessing();

    /**
     * dataSource set $this->data.
     *
     * @return array of data from su_users_files or su_users_coverage
     */
    abstract protected function dataSource();

    /**
     * copyProcess executes the process to copy the data.
     */
    abstract protected function copyProcess();

    protected function createUserLayer(): UserLayers
    {
        switch ($this->layerType) {
            case Config::LAYER_TYPE_KMS:{
                $tableName = 'Данни от комасация';
                $layerTable = 'layer_kms_' . time();

                break;
            }
            case Config::LAYER_TYPE_ISAK:{
                $tableName = 'от ИСАК';
                $layerTable = 'layer_isak_' . time();

                break;
            }
            case Config::LAYER_TYPE_WORK_LAYER:  {
                $tableName = $this->LayersController->getNextWorkLayerName($this->groupID);
                $layerTable = 'layer_work_' . time();

                break;
            }
            case Config::LAYER_TYPE_CSD: {
                $tableName = $this->getLayerName();
                $layerTable = $this->getLayerTableName();
                $this->layerExtent = $this->getLayerExtent();

                break;
            }
        }

        if (!$tableName) {
            throw new Exception(__METHOD__ . ' Line:' . __LINE__, ERROR_INVALID_TABLE_STRUCTURE);
        }

        $fields = [];

        $color = $this->LayersController->StringHelper->randomColorCode();
        $fields['name'] = $tableName;
        $fields['user_id'] = $this->groupID;
        $fields['extent'] = null === $this->layerExtent ? Config::DEFAULT_MAX_EXTENT : $this->layerExtent;
        $fields['table_name'] = $layerTable;
        $fields['layer_type'] = $this->layerType;
        $fields['color'] = $color;
        $fields['group_id'] = $this->groupID;

        if (Config::LAYER_TYPE_WORK_LAYER != $this->layerType) {
            $fields['farming'] = $this->farming;
            $fields['year'] = $this->year;
        }

        $fields['is_exist'] = true;
        $fields['style'] = json_encode(
            [
                'color' => $color,
                'border_color' => '111111',
                'transparency' => 100,
                'border_only' => false,
                'label_name' => ['area'],
                'tags' => false,
                'label_size' => 8,
            ]
        );
        $settings['mainData'] = $fields;
        $layerId = $this->LayersController->addLayerItem($settings);

        return UserLayers::getLayerById($layerId);
    }

    /**
     * filterFiles - проверяваме дали във архива има файлове с имена започващи с "prc_", ако има зареждаме тях, ако не хваща първите намерени.
     *
     * @param array $files
     * @param null|mixed $fileExtension
     *
     * @return array
     */
    protected function filterFiles($files, $pattern, $fileExtension = null)
    {
        $arrFiles = array_filter($files, function ($file) use ($pattern, $fileExtension) {
            $fileInfo = pathinfo($file['name']);
            $pos = strpos($file['name'], $pattern);

            return $fileExtension
                ? false !== $pos && $fileInfo['extension'] === $fileExtension
                : false !== $pos;
        });

        // Ако има файлове започващи с $pattern връщаме тях
        if (count($arrFiles)) {
            foreach ($arrFiles as $key => $value) {
                $newArr[] = $value;
            }

            return $newArr;
        }

        // Ако не - връщаме оригиналния array с файлове
        return $files;
    }

    /**
     * Get Encoding.
     *
     * @return string
     */
    private function getEncoding()
    {
        exec("/usr/bin/python3 {$this->modulesPath}get_enc.py \"{$this->processingSubDir}{$this->dbfFile}\"", $prjEncData);

        try {
            $prjEnc = json_decode($prjEncData[0]);
            $shpEncoding = $prjEnc->encoding;
        } catch (Exception $e) {
            $shpEncoding = 'LATIN1';
        }

        return $shpEncoding;
    }

    private function getEkatteFromFile()
    {
        $ekatte = strstr($this->shapeFile, 'G');
        if (!$ekatte) {
            throw new Exception(__METHOD__ . ' Line:' . __LINE__, INCONSISTENT_FILE_TYPE);
        }
        $ekatte = strstr($ekatte, '.', true);

        return substr($ekatte, 1);
    }

    /**
     * get projection code.
     *
     * @param  string           projection info
     *
     * @return bool|int return the projection code or false if the shp file doesn't contain projection meta data
     */
    private function getPrjCode($output)
    {
        $prjUTM = 'WGS_1984_UTM_Zone_35N';
        $prjUTM_HR = 'WGS 84 / UTM zone 35N';
        $prjGeoGCS = 'GEOGCS';

        if (false !== strpos($output, $prjUTM) || false !== strpos($output, $prjUTM_HR)) {
            return 32635;
        }
        if (false !== strpos($output, $prjGeoGCS)) {
            return 4326;
        }

        return false;
    }

    /**
     * get projection info from shp file.
     *
     * @param string $absoluteShpPath the absolute path to shp file
     *
     * @return string projection info
     */
    private function getPrjInfo($absoluteShpPath)
    {
        $path_parts = pathinfo($absoluteShpPath);
        $command = getenv('OGRINFO_PATH') . " -ro -so \"{$absoluteShpPath}\" \"{$path_parts['filename']}\" 2>&1";

        exec($command, $output);

        return implode(' ', $output);
    }

    /** replace invalid slash present the sql string for number fields, e.g.: from '/0.000' to '0'.
     * @param array $output
     *
     * @return string
     */
    private function cleanStringOutput($output = [])
    {
        $output = implode(' ', $output);

        return str_replace('/0.000', '0', $output);
    }

    /**
     * @param string $path
     * @param string $convertedDir
     */
    private function convertAgData($path, $convertedDir)
    {
        if (!file_exists($path . '/' . $convertedDir)) {
            mkdir($path . '/' . $convertedDir, 0777, true);
        }
        $command = 'mono ' . JD_EXPORTER . ' import ' . $path . 'AgData TrimbleAgData ' . $path . $convertedDir;
        exec($command, $out);
    }

    /**
     * @param array $data
     *
     * @return string
     */
    private function generateMultiPolygonOfAgDataFeature($data)
    {
        $multipolygon = 'MULTIPOLYGON (((';
        foreach ($data as $points) {
            foreach ($points as $point) {
                $multipolygon .= $point[0] . ' ' . $point[1] . ', ';
            }
        }

        $multipolygon = rtrim($multipolygon, ', ');
        $multipolygon .= ')))';

        return $multipolygon;
    }

    /**
     * @param string $userSubDir
     * @param string $convertedDir
     *
     * @return array
     */
    private function getAgDataJsonFiles($userSubDir, $convertedDir)
    {
        $jsons = [];
        $files = $this->LayersController->File->getFilesFromDir($userSubDir . '/' . $convertedDir);
        foreach ($files as $file) {
            $fileArr = explode('.', $file['name']);
            if ('geojson' === end($fileArr)) {
                $jsons[] = $userSubDir . $convertedDir . '/' . $file['name'];
            }
        }

        return $jsons;
    }

    /**
     * @param string $tableName
     *
     * @return string
     */
    private function generateSqlForLayerTable($tableName, array $features)
    {
        $sql = 'SET CLIENT_ENCODING TO UTF8; ';
        $sql .= 'SET STANDARD_CONFORMING_STRINGS TO ON; ';
        $sql .= 'DROP TABLE IF EXISTS ' . $tableName . '; ';
        $sql .= 'CREATE TABLE ' . $tableName . ' (
                    "gid" serial,
                    "grower" varchar(32),
                    "farm" varchar(32),
                    "farming_year" varchar(32),
                    "field" varchar(32),
                    "timeScope" varchar(32),
                    "polygon_number" varchar(32)
                ); ';
        $sql .= 'ALTER TABLE ' . $tableName . ' ADD PRIMARY KEY (gid); ';
        $sql .= "SELECT AddGeometryColumn('','" . $tableName . "','geom','4326','MULTIPOLYGON',2); ";

        foreach ($features as $feature) {
            $geometry = $this->generateMultiPolygonOfAgDataFeature($feature['geometry']['coordinates']);
            $geometry = "ST_GeomFromText('{$geometry}', 4326)";
            $grower = "LEFT('{$feature['properties']['grower']}', 32)";
            $farm = "LEFT('{$feature['properties']['farm']}', 32)";
            $farmingYear = "LEFT('{$feature['properties']['farming year']}', 32)";
            $field = "LEFT('{$feature['properties']['field']}', 32)";
            $timeScope = "LEFT('{$feature['properties']['timeScope']}', 32)";
            $polygonNumber = "LEFT('{$feature['properties']['polygon number']}', 32)";

            $sql .= 'INSERT INTO ' . $tableName . ' (grower, farm, farming_year, field, "timeScope", polygon_number, geom) ';

            $sql .= "VALUES ({$grower}, {$farm}, {$farmingYear}, {$field}, {$timeScope}, {$polygonNumber}, {$geometry}); ";
        }

        $sql .= 'CREATE INDEX ' . $tableName . '_geom ON ' . $tableName . ' USING gist(geom); ';
        $sql .= 'ANALYZE ' . $tableName . ';';

        return $sql;
    }
}
