<?php

namespace TF\Engine\Kernel\CronsLoadData;

use Exception;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbCoverage\UserDbCoverageController;
use TF\Engine\Plugins\Core\Users\UsersController;

include_once __DIR__ . '/../../Plugins/Core/Users/<USER>';
include_once __DIR__ . '/../../Plugins/Core/UserDb/conf.php';
include_once __DIR__ . '/../../Plugins/Core/Layers/conf/index.php';

/**
 * <AUTHOR> <<EMAIL>>
 *
 * @link http://www.technofarm.bg/
 *
 * @copyright Copyright &copy; 2015 TechnoFarm Ltd.
 *
 * @property UsersController $UsersController
 * @property UserDbController $UsersDbController
 * @property LayersController $LayersController
 * @property UserDbCoverageController $UsersDbCoverageController
 */
class CoverageProcessingClass extends AbstractLoadDataClass
{
    public $UsersController;
    public $LayersController;
    public $UsersDbCoverageController;
    public $itemID;

    /**
     * init - prado init function.
     */
    public function init($config)
    {
        $this->UsersController = new UsersController('Users');
        $this->LayersController = new LayersController('Layers');
    }

    /**
     * startProcessing.
     */
    public function startProcessing()
    {
        $this->mainProcessing();
    }

    /**
     * dataSource.
     *
     * @return array of data from DB
     */
    public function dataSource()
    {
        return $this->UsersController->getCoverageItemsForProcessing();
    }

    /**
     * initClassVariables - init Class Variables.
     *
     * @param array $currentData - a row data from table "su_users_coverage"
     */
    public function initClassVariables($currentData)
    {
        $this->fileData = $currentData;

        $this->ID = $this->fileData['id'];
        $this->fileName = $this->fileData['filename'];
        $this->dateUploaded = $this->fileData['upload_date'];
        $this->groupID = $this->fileData['group_id'];
        $this->userID = $this->fileData['user_id'];
        $this->itemID = $this->fileData['item_id'];
        $this->status = $this->fileData['status'];
        $this->database = $this->fileData['database'];

        $this->userDir = COVERAGE_QUEUE_PATH . $this->groupID;
        $this->filePath = $this->userDir . '/' . $this->ID . '.zip';

        $this->name = str_replace(' ', '_', ucfirst(str_ireplace('.zip', '', substr($this->fileName, 0, -4))));

        $this->userSubDir = $this->userDir . '/' . $this->ID . '/';

        $this->modulesPath = SITE_PATH . 'crons/';
    }

    /**
     * mainProcessing - executes the main process.
     */
    public function mainProcessing()
    {
        $this->data = $this->dataSource();

        foreach ($this->data as $data) {
            $this->initClassVariables($data);
            $this->UsersDbController = new UserDbController($this->database);
            $this->UsersDbCoverageController = new UserDbCoverageController($this->database);

            try {
                $this->unzipData();
                // defines the data format
                if (file_exists($this->userSubDir . 'AgGPS/Data')) {
                    $this->processAgGpsItems();
                } elseif (file_exists($this->userSubDir . 'AgData')) {
                    $this->processAgDataItems();
                } else {
                    throw new Exception(__METHOD__ . ' Line:' . __LINE__, ERROR_INVALID_ARCHIVE);
                }

                $this->copyProcess();
            } catch (Exception $e) {
                $this->mainException($e);
            }
        }
    }

    /**
     * processAgGpsItems - process Trimble Items.
     *
     * @throws Exception
     */
    public function processAgGpsItems()
    {
        // creating the final directory array
        list($final_dirs, $final_dirs_info) = $this->getFinalDirs();

        // create iterations flag
        $iteration = 0;
        $this->shapeFlag = false;
        $this->dbfFlag = false;

        // check if any directory with correct structure was found
        if (!count($final_dirs)) {
            throw new Exception(__METHOD__ . ' Line:' . __LINE__, ERROR_INVALID_ARCHIVE);
        }
        foreach ($final_dirs as $j => $final_dir) {
            $this->processingSubDir = $final_dir;
            $files = $this->LayersController->File->getFilesFromDir($this->processingSubDir);
            $this->shapeFile = false;
            $this->dbfFile = false;
            // check if dbf and shp file exists
            foreach ($files as $file) {
                if ($file && 'Coverage.dbf' == $file['name']) {
                    $this->dbfFile = $file['name'];
                    $this->dbfFlag = true;
                }
                if ($file && 'Coverage.shp' == $file['name']) {
                    $this->shapeFile = $file['name'];
                    $this->shapeFlag = true;
                }
            }

            // if shape and dbf files were found we can proceed, else we move to next dir
            if (!$this->shapeFile && !$this->dbfFile) {
                continue;
            }

            $this->tmpTable = 'layer_coverage_' . time();
            // here we create the temporary tables
            $output = $this->commonSHP2PSQL();
            if (null != $output) {
                throw new Exception(__METHOD__ . ' Line:' . __LINE__, ERROR_RUNTIME);
            }

            $iteration++;

            try {
                $this->UsersDbController->transformColumnCRS($this->tmpTable, DEFAUL_DB_CRS);
            } catch (Exception $e) {
                throw new Exception($e->getMessage() . '\n=====' . $e->getTraceAsString(), ERROR_INVALID_GEOMETRY);
            }

            // add info for new layer into layers list(coverage data)
            $options = [
                'tablename' => $this->UsersDbController->DbHandler->tableCoverageData,
                'mainData' => [
                    'tablename' => $this->tmpTable,
                    'extent' => $this->getMaxExtend(),
                    'client' => $final_dirs_info[$j]['client'],
                    'farming' => $final_dirs_info[$j]['farming'],
                    'plot' => $final_dirs_info[$j]['plot'],
                    'event' => $final_dirs_info[$j]['event'],
                    'file_id' => $this->itemID,
                    'color' => '00FF00',
                    'border_color' => '000000',
                    'transparency' => 100,
                ],
            ];
            $this->UsersDbController->addItem($options);
            sleep(1);
        }
    }

    public function processAgDataItems()
    {
        $convertedDir = 'converted';
        $this->convertAgData($this->userSubDir, $convertedDir);
        $agDataLoggedDataFile = $this->getAgDataLoggedDataFile($this->userSubDir, $convertedDir);
        $loggedDataFileInfo = json_decode(file_get_contents($agDataLoggedDataFile), true);
        $agDataFieldInfo = $this->getAgDataFieldInfo($this->userSubDir, $convertedDir);
        $arr = [];

        // Collect the data
        foreach ($loggedDataFileInfo['LoggedData'] as $dataKey => $data) {
            if (!empty($data['OperationData'][0]['SpatialRecord'][0]['TimeStamp'])) {
                $date = date('d_m_Y', strtotime($data['OperationData'][0]['SpatialRecord'][0]['TimeStamp']));
            }

            if (!isset($date)) {
                continue;
            }

            $field = array_key_exists($data['FieldId'], $agDataFieldInfo['fields']) ? $agDataFieldInfo['fields'][$data['FieldId']] : 'Field' . $data['FieldId'];
            $grower = array_key_exists($data['GrowerId'], $agDataFieldInfo['growers']) ? $agDataFieldInfo['growers'][$data['GrowerId']] : 'Grower' . $data['GrowerId'];
            $farm = array_key_exists($data['FarmId'], $agDataFieldInfo['farms']) ? $agDataFieldInfo['farms'][$data['FarmId']] : 'Farm' . $data['FarmId'];

            if (!isset($arr[$date])) {
                $arr[$date] = [
                    'Grower' => $grower,
                    'Farm' => $farm,
                    'Field' => $field,
                    'OperationData' => [],
                ];
            }
            foreach ($data['OperationData'] as $operationData) {
                foreach ($operationData['SpatialRecord'] as $SpatialRecord) {
                    $arr[$date]['OperationData'][$operationData['OperationType']][$date][] = $SpatialRecord;
                }
            }
        }
        $layer_name = 'layer_coverage_' . time() . rand(1, 1000);
        foreach ($arr as $record) {
            foreach ($record['OperationData'] as $operationName => $operationsData) {
                foreach ($operationsData as $date => $data) {
                    $this->tmpTable = $layer_name . '_' . $date;

                    try {
                        $sql = $this->generateSqlForLayerCoverageTable($this->tmpTable, $data);
                        $this->UsersDbController->DbHandler->getDataByQuery($sql);
                        $this->createGeomIndex($this->tmpTable);
                        echo 'Table: ' . $this->tmpTable . " was successfully created \r\n";
                        $intersectionTable = $this->tmpTable . '_intersection';
                        $this->createLayerCoverageIntersectionTable($this->tmpTable, $intersectionTable);
                        echo 'Table: ' . $intersectionTable . " was successfully created \r\n";
                        $this->createGeomIndex($intersectionTable);

                        $this->UsersDbController->transformColumnCRS($this->tmpTable, DEFAUL_DB_CRS);

                        $event_area_results = $this->UsersDbController->getItemsByParams([
                            'tablename' => $this->tmpTable,
                            'return' => ['round((SUM(St_Area(geom))/1000)::numeric, 3) as area'],
                        ], false, false);
                        $event_area = (float)$event_area_results[0]['area'];

                        $event_overlap_area_results = $this->UsersDbController->getItemsByParams([
                            'tablename' => $intersectionTable,
                            'return' => ['round((SUM(St_Area(geom))/1000)::numeric, 3) / 2 as overlap_area'],
                        ], false, false);
                        $event_overlap_area = (float)$event_overlap_area_results[0]['overlap_area'];

                        $overlap_percent = number_format($event_overlap_area / $event_area * 100, 2);
                    } catch (Exception $e) {
                        throw new Exception($e->getMessage() . '\n=====' . $e->getMessage(), ERROR_RUNTIME);
                    }

                    $options = [
                        'tablename' => $this->UsersDbController->DbHandler->tableCoverageData,
                        'mainData' => [
                            'tablename' => $this->tmpTable,
                            'extent' => $this->getMaxExtend(),
                            'client' => $record['Grower'],
                            'farming' => $record['Farm'],
                            'plot' => $record['Field'],
                            'event' => $operationName . '_' . $date,
                            'file_id' => $this->itemID,
                            'color' => '00FF00',
                            'border_color' => '000000',
                            'transparency' => 100,
                            'area' => $event_area,
                            'overlap_area' => $event_overlap_area,
                            'overlap_percent' => $overlap_percent,
                        ],
                    ];

                    $this->UsersDbController->addItem($options);

                    echo "A new record in su_coverage_data was successfully added \r\n";
                }
            }
        }
    }

    /**
     * unzipData - unzip the .zip file.
     *
     * @throws Exception
     */
    public function unzipData()
    {
        try {
            if (!file_exists($this->userDir)) {
                @mkdir($this->userDir, 0777);
            }

            $this->LayersController->File->removeFolder($this->userSubDir);
            @mkdir($this->userSubDir, 0777);

            $fileDataNoWS = str_replace(' ', '_', $this->filePath);
            rename($this->filePath, $fileDataNoWS);

            $c = "unzip -o \"{$fileDataNoWS}\" -d {$this->userSubDir} 2>&1";
            system($c, $out);
        } catch (Exception $e) {
            throw new Exception($e->getMessage() . '\n=====' . $e->getTraceAsString(), ERROR_INVALID_ARCHIVE);
        }
    }

    /**
     * copyProcess executes the process to copy the data.
     */
    public function copyProcess()
    {
        $this->UsersController->File->removeFolder($this->userSubDir);

        // update main database
        $this->UsersController->setCoverageItemStatus($this->ID, SUCCESSFULLY_TREATED);
        // update user database
        $options = [
            'tablename' => $this->UsersDbController->DbHandler->tableCoverageFiles,
            'mainData' => [
                'status' => SUCCESSFULLY_TREATED,
            ],
            'where' => [
                'id' => $this->itemID,
            ],
        ];
        $this->UsersDbController->editItem($options);
    }

    /**
     * errorLog - updates the errors field in DB of the processing file.
     *
     * @param int $error - code error
     * @param string $message - error message
     */
    public function errorLog($error, $message = '')
    {
        // find correct error in the error message
        $error = $this->catchError($error, $message);

        $this->UsersController->log(1, 'cron-daemon', $error, [$this->ID]);

        $this->UsersController->setCoverageItemStatus($this->ID, $error, $message);

        // remove the unziped dir
        $this->LayersController->File->removeFolder($this->userSubDir);
    }

    /**
     * @throws Exception
     *
     * @return array
     */
    protected function getFinalDirs()
    {
        $final_dirs = [];
        $final_dirs_info = [];
        // getting client directories
        $client_dirs = $this->LayersController->File->getDirsFromDir($this->userSubDir . 'AgGPS/Data');

        if (!count($client_dirs)) {
            throw new Exception(__METHOD__ . ' Line:' . __LINE__, ERROR_INVALID_ARCHIVE);
        }
        // J is iteration for client directories
        foreach ($client_dirs as $client_dir) {
            $dir_name = $this->userSubDir . 'AgGPS/Data/' . $client_dir['name'];
            $farming_dirs = $this->LayersController->File->getDirsFromDir($dir_name);
            // if farming directories were found
            if (!count($farming_dirs)) {
                continue;
            }
            // K is iteration for farming directories
            foreach ($farming_dirs as $farming_dir) {
                $dir_name = $this->userSubDir . 'AgGPS/Data/' . $client_dir['name'] . '/' . $farming_dir['name'];
                $plot_dirs = $this->LayersController->File->getDirsFromDir($dir_name);

                // if plot directories were found
                if (!count($plot_dirs)) {
                    continue;
                }
                foreach ($plot_dirs as $plot_dir) {
                    $dir_name = $this->userSubDir . 'AgGPS/Data/' . $client_dir['name'] . '/' . $farming_dir['name'] . '/' . $plot_dir['name'];
                    $event_dirs = $this->LayersController->File->getDirsFromDir($dir_name);

                    foreach ($event_dirs as $event_dir) {
                        if (!is_array($event_dir)) {
                            continue;
                        }
                        $dir_name = $this->userSubDir . 'AgGPS/Data/' . $client_dir['name'] . '/' . $farming_dir['name'] . '/' . $plot_dir['name'] . '/' . $event_dir['name'];

                        $final_dirs[] = $dir_name . '/';
                        $final_dirs_info[] = [
                            'client' => $client_dir['name'],
                            'farming' => $farming_dir['name'],
                            'plot' => $plot_dir['name'],
                            'event' => $event_dir['name'],
                        ];
                    } // end of event iteration
                } // end of plots iteration
            }
        }

        return [$final_dirs, $final_dirs_info];
    }

    /**
     * @param string $path
     * @param string $convertedDir
     */
    private function convertAgData($path, $convertedDir)
    {
        if (!file_exists($path . '/' . $convertedDir)) {
            mkdir($path . '/' . $convertedDir, 0777, true);
        }

        $command = 'mono ' . SMART_CONVERTER . ' --from TrimbleAgData --to TF --input ' . $path . '/AgData --output ' . $path . '/' . $convertedDir . '/';
        exec($command, $out);

        echo 'Exporter result:' . print_r($out, true);
    }

    /**
     * @param array $data
     *
     * @return string
     */
    private function generateMultiPolygonFromExteriorRing($data)
    {
        $multipolygon = 'MULTIPOLYGON (((';
        foreach ($data[0][0] as $point) {
            $multipolygon .= $point[0] . ' ' . $point[1] . ', ';
        }
        $multipolygon .= $data[0][0][0][0] . ' ' . $data[0][0][0][1] . ')))';

        return $multipolygon;
    }

    private function getMaxExtend()
    {
        $maxExtent = $this->UsersDbController->getMaxExtent($this->tmpTable);
        // convert extent to map file format
        $maxExtent = str_replace('BOX(', '', $maxExtent);
        $maxExtent = str_replace(')', '', $maxExtent);

        return str_replace(',', ' ', $maxExtent);
    }

    /**
     * @param string $tableName
     *
     * @return string
     */
    private function generateSqlForLayerCoverageTable($tableName, array $operation)
    {
        $sql = 'SET CLIENT_ENCODING TO UTF8; ';
        $sql .= 'SET STANDARD_CONFORMING_STRINGS TO ON; ';
        $sql .= 'BEGIN; ';
        $sql .= 'CREATE TABLE ' . $tableName . ' (
                    "gid" serial, 
                    "version" varchar(8), 
                    "gps_status" int2, 
                    "status_txt" varchar(8), 
                    "swath" int4, 
                    "height" float8, 
                    "dateclosed" date, 
                    "timeclosed" varchar(10), 
                    "appldrate" float8, 
                    "moisture" float8, 
                    "material" varchar(30), 
                    "materialid" int4, 
                    "speed" float8, 
                    "xte" float8, 
                    "apptype" int2
                ); ';
        $sql .= 'ALTER TABLE ' . $tableName . ' ADD PRIMARY KEY (gid); ';
        $sql .= "SELECT AddGeometryColumn('','" . $tableName . "','geom','4326','MULTIPOLYGON',2); ";

        foreach ($operation as $record) {
            $dateTime = strtotime($record['TimeStamp']);
            $geometry = $this->generateMultiPolygonFromExteriorRing($record['Geometry']);
            $sensorsData = $this->generateSensorsData(($record['SensorData']));

            $sql .= 'INSERT INTO ' . $tableName . ' ("version", gps_status, status_txt, swath, height, dateclosed, timeclosed, appldrate, moisture, material, materialid, speed, xte, apptype, geom) ';
            $sql .= "VALUES ('1.70.007', 1, 'No Corre', 0, " . (trim($sensorsData['vrElevation']['value']) ? $sensorsData['vrElevation']['value'] : 'NULL') . ", '" . date('Y-m-d', $dateTime) . "', '" . date('h:i:sa', $dateTime) . "', " . (trim($sensorsData['appliedRate']['value']) ? $sensorsData['appliedRate']['value'] : 'NULL') . ', NULL, NULL, NULL, ' . (trim($sensorsData['vrVehicleSpeed']['value']) ? $sensorsData['vrVehicleSpeed']['value'] : 'NULL') . ", 0, 7, ST_GeomFromText('" . $geometry . "', 4326)); ";
        }

        $sql .= 'COMMIT; ';
        $sql .= 'ANALYZE ' . $tableName . ';';

        return $sql;
    }

    /**
     * @param string $tableName
     * @param string $intersectionTable
     *
     * @return string
     */
    private function createLayerCoverageIntersectionTable($tableName, $intersectionTable)
    {
        $sql = 'CREATE TABLE ' . $intersectionTable . ' (gid, geom) AS
                SELECT t1.gid, ST_Transform(ST_Intersection(t2.geom, t1.geom), 32635) FROM ' . $tableName . ' t1
                INNER JOIN ' . $tableName . ' t2 ON (ST_IsValid(t1.geom) and ST_IsValid(t2.geom) and ST_Overlaps(t1.geom, t2.geom));';

        $this->UsersDbController->DbHandler->getDataByQuery($sql);
    }

    /**
     * @param string $tableName
     *
     * @return string
     */
    private function createGeomIndex($tableName)
    {
        $sql = 'CREATE INDEX ' . $tableName . '_geom_idx ON ' . $tableName . ' USING GIST (geom); ';

        $this->UsersDbController->DbHandler->getDataByQuery($sql);
    }

    /**
     * @return array
     */
    private function generateSensorsData(array $sensors)
    {
        $sensorsData = [];
        foreach ($sensors as $sensor) {
            $sensorsData[$sensor['Code']]['value'] = $sensor['Value'];
        }

        return $sensorsData;
    }

    /**
     * @param string $userSubDir
     * @param string $convertedDir
     *
     * @return string
     */
    private function getAgDataLoggedDataFile($userSubDir, $convertedDir)
    {
        $files = $this->LayersController->File->getFilesFromDir($userSubDir . '/' . $convertedDir);
        foreach ($files as $file) {
            if (strpos($file['name'], 'LoggedData.tf')) {
                return $userSubDir . $convertedDir . '/' . $file['name'];
            }
        }
    }

    /**
     * @param string $userSubDir
     * @param string $convertedDir
     *
     * @return array
     */
    private function getAgDataFieldInfo($userSubDir, $convertedDir)
    {
        $file = '';
        $fields = [
            'fields' => [],
            'growers' => [],
            'farms' => [],
        ];
        $files = $this->LayersController->File->getFilesFromDir($userSubDir . '/' . $convertedDir);
        foreach ($files as $file) {
            if (strpos($file['name'], 'FieldInfo.tf')) {
                $file = $userSubDir . $convertedDir . '/' . $file['name'];

                break;
            }
        }

        if (empty($file)) {
            return $files;
        }

        $result = json_decode(file_get_contents($file), true);

        foreach ($result['Fields'] as $field) {
            if (!array_key_exists($field['Id'], $fields['fields'])) {
                $fields['fields'][$field['Id']] = $field['Name'];
            }
            if (!array_key_exists($field['Farm']['Id'], $fields['farms'])) {
                $fields['farms'][$field['Farm']['Id']] = $field['Farm']['Name'];
            }
            if (!array_key_exists($field['Grower']['Id'], $fields['growers'])) {
                $fields['growers'][$field['Grower']['Id']] = $field['Grower']['Name'];
            }
        }

        return $fields;
    }
}
