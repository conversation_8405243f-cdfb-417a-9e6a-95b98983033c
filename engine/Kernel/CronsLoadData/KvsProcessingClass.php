<?php

namespace TF\Engine\Kernel\CronsLoadData;

use Exception;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * <AUTHOR> <<EMAIL>>
 *
 * @link http://www.technofarm.bg/
 *
 * @copyright Copyright &copy; 2015 TechnoFarm Ltd.
 */
class KvsProcessingClass extends AbstractLoadDataClass
{
    public $tmpTable = '';

    /**
     * init - prado init function.
     */
    public function init($config)
    {
        parent::init($config);

        $this->tmpTable = 'tmp_kvs';
    }

    /**
     * startProcessing.
     */
    public function startProcessing()
    {
        $this->mainProcessingSimple();
    }

    /**
     * dataSource.
     *
     * @return array of data from DB
     */
    public function dataSource()
    {
        return $this->LayersController->getKVSFilesForProcessing(false);
    }

    /**
     * finalActions - final actions that we execute.
     */
    public function finalActions()
    {
        if (($this->shapeFile && $this->dbfFile) || ($this->shapeFlag && $this->dbfFlag)) {
            $this->makeValidGeom();

            try {
                $this->UsersDbController->transformColumnCRS($this->tmpTable, DEFAUL_DB_CRS);
            } catch (Exception $e) {
                throw new Exception($e->getMessage() . '\n=====' . $e->getTraceAsString(), ERROR_INVALID_GEOMETRY);
            }

            $options = [];
            $options['mainData'] = [
                'status' => ERROR_WAITING_DEFINITION,
            ];
            $options['id'] = $this->ID;
            $this->LayersController->editItemFiles($options);

            // remove the unziped dir
            $this->LayersController->File->removeFolder($this->userSubDir);
        } else {
            throw new Exception(__METHOD__ . ' Line:' . __LINE__, ERROR_INVALID_ARCHIVE);
        }
    }

    /**
     * onAfterProcessing - actions that we exucete after processing.
     */
    public function onAfterProcessing()
    {
        $this->data = $this->LayersController->getKVSFilesForProcessing(true);
        $this->data = $this->data[0];

        if (0 != count($this->data)) {
            $this->initClassVariables($this->data);
            $this->tmpTable = 'tmp_kvs_' . $this->data['id'];

            if ($this->database) {
                $this->UsersDbController = new UserDbController($this->database);// getPluginInstanceUserDb($this->database);
            }

            $definition = $this->data['definition'];
            $this->definition = unserialize($definition);

            $this->UsersDbController->createTableKVS();

            if (!$this->UsersDbController->checkIfIndexExists($this->tmpTable . '_geom_gist')) {
                $this->createGeometryIndex();
            }
            $this->addIntersectionColumns();
            $this->createIntersectionViews();
            $this->updateTmpTableWithIntersectionAreas();
            $this->dropIntersectionViews();

            try {
                $this->copyProcess();
            } catch (Exception $e) {
                throw new Exception($e->getMessage() . '\n=====' . $e->getTraceAsString(), ERROR_RUNTIME);
            }

            try {
                $this->UsersDbController->transformColumnCRS($this->tmpTable, DEFAUL_DB_CRS);
            } catch (Exception $e) {
                throw new Exception($e->getMessage() . '\n=====' . $e->getTraceAsString(), ERROR_INVALID_GEOMETRY);
            }

            $maxExtent = $this->UsersDbController->getMaxExtent('layer_kvs');
            $maxExtent = str_replace('BOX(', '', $maxExtent);
            $maxExtent = str_replace(')', '', $maxExtent);
            $maxExtent = str_replace(',', ' ', $maxExtent);

            $options = [];
            $options['group_id'] = $this->groupID;
            $options['layer_type'] = $this->layerType;
            $layerID = $this->LayersController->getLayersIdByLayerType($options);

            $options = [];
            $options['mainData'] = [
                'extent' => $maxExtent,
            ];
            $options['id'] = $layerID;
            $this->LayersController->editItem($options);

            $options = [];
            $options['database'] = $this->database;
            $options['user_id'] = $this->groupID;
            $this->LayersController->updateUnstyledEkkates($this->database, $layerID);
            $this->LayersController->generateMapFile($options);
            $this->UsersDbController->refreshEkateCombobox();
            $this->UsersDbController->deleteTmpKvsTable($this->data['id']);

            system('/bin/chown -R ' . WMS_MAP_PATH);

            $options = [];
            $options['mainData'] = [
                'status' => SUCCESSFULLY_TREATED,
            ];
            $options['id'] = $this->ID;
            $this->LayersController->editItemFiles($options);
        }
    }

    /**
     * copyProcess executes the process to copy the data.
     */
    public function copyProcess()
    {
        $this->UsersDbController->copyKVSData($this->database, $this->definition, $this->tmpTable);
    }

    /**
     * addIntersectionColumns - Добавя допълнителните колони, в които ще се записва площта,
     * която влиза в допустимия слой, типът на допустимост, площ в пзп, натура и др.
     */
    private function addIntersectionColumns()
    {
        $sql = '
            ALTER TABLE ' . $this->tmpTable . '
            ADD COLUMN IF NOT EXISTS "allowable_area" float8,
            ADD COLUMN IF NOT EXISTS "allowable_type" varchar(255)
            ';

        $addCommand = $this->UsersDbController->DbHandler->DbModule->createCommand($sql);
        $addCommand->execute();
    }

    /**
     * addIntersectionColumns - Добавя материализирани views, за отделните типове пресичане,
     * в който ще се съдържа информация за пресичането с отделните слоеве и връзка с временната таблица,
     * в която се зареждат КВС данни.
     */
    private function createIntersectionViews()
    {
        // create safe_intersection function in case it does not exist
        $this->LayersController->createSafeIntersection();
        $this->createAllowableView();
    }

    /**
     * Добавяне на view за пресичане с допустим слой.
     */
    private function createAllowableView()
    {
        $id = $this->ID;
        $allowableMatView = 'allowable_areas_mat_view_' . $id;
        $sql = "
            DROP MATERIALIZED VIEW IF EXISTS {$allowableMatView};
            create MATERIALIZED view {$allowableMatView} as
                SELECT
                    kvs.gid,
                    string_agg(distinct(A .ntp)::text, ', ') as allowable_type,
                    round((st_area(safe_intersection(kvs.geom,st_union(a.geom))) / 1000)::numeric,3) as allowable_area
                FROM
                    {$this->tmpTable} kvs,
                    dblink (
                        'host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "'::text,
                        'SELECT
                            geom,
                            ntp
                        FROM
                            layer_allowable_final'
                    ) AS A (geom geometry, ntp VARCHAR)
                where st_intersects(kvs.geom, a.geom)
                GROUP by kvs.gid
            ";

        $addCommand = $this->UsersDbController->DbHandler->DbModule->createCommand($sql);
        $addCommand->execute();
    }

    private function updateTmpTableWithIntersectionAreas()
    {
        $id = $this->ID;
        $allowableMatView = 'allowable_areas_mat_view_' . $id;
        $sql = "
            UPDATE {$this->tmpTable} kvs SET
            allowable_area = av.allowable_area,
            allowable_type = av.allowable_type
            FROM {$allowableMatView} av
            WHERE kvs.gid = av.gid
            ";

        $updateAllowableCommand = $this->UsersDbController->DbHandler->DbModule->createCommand($sql);
        $updateAllowableCommand->execute();
    }

    private function dropIntersectionViews()
    {
        $id = $this->ID;
        $allowableMatView = 'allowable_areas_mat_view_' . $id;
        $sql = "
            DROP MATERIALIZED VIEW IF EXISTS {$allowableMatView};
            ";

        $dropCommand = $this->UsersDbController->DbHandler->DbModule->createCommand($sql);
        $dropCommand->execute();
    }

    private function createGeometryIndex()
    {
        $sql = "CREATE INDEX \"{$this->tmpTable}_geom_gist\" ON \"public\".\"{$this->tmpTable}\" USING gist (\"geom\" \"public\".\"gist_geometry_ops_2d\")";

        $dropCommand = $this->UsersDbController->DbHandler->DbModule->createCommand($sql);
        $dropCommand->execute();
    }
}
