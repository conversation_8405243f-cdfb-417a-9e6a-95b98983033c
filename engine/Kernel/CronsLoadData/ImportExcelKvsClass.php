<?php

namespace TF\Engine\Kernel\CronsLoadData;

use Exception;
use PHPExcel_IOFactory;
use PHPExcel_Reader_Exception;
use Prado\Exceptions\TDbException;
use Prado\TModule;
use RuntimeException;
use TF\Application\Common\Config;
use TF\Application\Entity\UserLayers;
use TF\Engine\APIClasses\Map\MapTools;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Class ImportExcelKvsClass.
 *
 * @property LayersController $LayersController
 * @property UserDbController $UserDbController
 * @property UsersController $UsersController
 * @property FarmingController $FarmingController
 * @property string $layerName
 * @property string $tmpTable
 * @property string[] $kad_idents
 * @property string[] $ekattes
 * @property int $layerId
 * @property int $fileId
 * @property string[] $tables
 */
class ImportExcelKvsClass extends TModule
{
    public const DEFAULT_MAX_EXTENT = '125190.6162 4573142.7188 631370.3273 4887149.5823';
    protected $LayersController;
    protected $UserDbController;
    protected $UsersController;
    protected $FarmingController;

    protected $layerName = 'Ексeл импортир';
    protected $tableName;
    protected $kad_idents;
    protected $ekattes;
    protected $layerId;
    protected $fileId;
    protected $tables;
    /** @var MapTools */
    private $mapTools;
    private $inputFileName;

    public function init($config)
    {
        parent::init($config);
        $this->LayersController = new LayersController('Layers');
        $this->UsersController = new UsersController('Users');
        $this->FarmingController = new FarmingController('Farming');
    }

    /** Main function called from CronImportExcelKvsCommand.php.
     * @param MTAuthManager $auth
     *
     * @return bool
     */
    public function startProcessing(MapTools $mapTools, \TF\Application\Common\MTAuthManager $auth)
    {
        $this->mapTools = $mapTools;
        $file = $this->LayersController->getKVSExcelFilesForProcessing();

        $userData = $this->UsersController->getUserDataById($file['user_id']);
        $auth->switchUser($userData['username']);

        $this->log('Start processing: ImportExcelKvsClass', __METHOD__);
        if (empty($file)) {
            return false;
        }

        $this->UserDbController = new UserDbController($file['database']);
        $this->fileId = $file['id'];
        $this->log('Data found: files to load' . print_r($file, true), __METHOD__);

        return $this->mainProcessing($file);
    }

    /**
     * @return bool
     */
    public function mainProcessing($file)
    {
        $this->log('ImportExcelKvsClass mainProcessing Start', __METHOD__);

        try {
            $this->LayersController->setFilesProcessingStatus($this->fileId, LOADING_FILE_NOW);
            $data = $this->readExcelFile($file);

            // remove the the file
            $filePath = str_replace($file['filename'], '', $this->inputFileName);
            $this->LayersController->File->deleteFileFromDir($filePath, $file['filename']);
            $this->log('File: ' . $file['filename'] . ' is successfully deleted', __METHOD__);

            $kadColumn = $this->validateHeaders($data);

            $this->kad_idents = $this->setKadIdents($data, $kadColumn);

            $srcLayer = UserLayers::getLayerByTableName('layer_kvs', $this->User->GroupID);
            $groups = array_map(function ($kadIdent) {
                return [
                    'kad_ident' => $kadIdent,
                ];
            }, $this->kad_idents);

            $params = [
                'src_layer' => [
                    'id' => $srcLayer->id,
                    'filters' => [
                        'groups' => $groups,
                    ],
                ],
                'dst_layer' => [
                    'id' => 'new',
                    'layer_type' => Config::LAYER_TYPE_WORK_LAYER,
                    'layer_name' => $this->LayersController->getNextWorkLayerName($this->User->GroupID, 'Създаден от ' . $file['name']),
                ],
            ];

            $result = $this->mapTools->copyLayer($params);

            if (!isset($result['id'])) {
                throw new Exception("Error copying layer for {$file['filename']}");
            }

            $file['layer_id'] = $result['id'];

            $this->generateMap($file);
            $this->LayersController->setFilesProcessingStatus($this->fileId, SUCCESSFULLY_TREATED);
            $this->log('ImportExcelKvsClass mainProcessing End', __METHOD__);

            return true;
        } catch (Exception $e) {
            if (!empty($filePath)) {
                $this->LayersController->File->deleteFileFromDir($filePath, $file['filename']);
            }
            $this->log('ERROR! : ' . $e->getMessage(), __METHOD__);
            $this->mainException($e);
        }

        return false;
    }

    /**
     * @throws PHPExcel_Reader_Exception|RuntimeException
     *
     * @return null|array
     */
    protected function readExcelFile($fileRecord)
    {
        $this->log('readExcelFile process started', __METHOD__);
        $this->inputFileName = PUBLIC_UPLOAD_PLOT_DATA . '/' . $fileRecord['group_id'] . '/' . $fileRecord['filename'];
        $this->log('File Path: ' . $this->inputFileName, __METHOD__);
        $inputFileType = PHPExcel_IOFactory::identify($this->inputFileName);
        $objReader = PHPExcel_IOFactory::createReader($inputFileType);
        $objReader->setReadDataOnly(true);
        $objPHPExcel = $objReader->load($this->inputFileName);
        $this->log('File loaded, OK ', __METHOD__);
        $data = $objPHPExcel->getActiveSheet()->toArray(null, true, true, true);
        if (empty($data)) {
            $this->log('ERROR! File loaded is empty' . count($data), __METHOD__);

            throw new RuntimeException(__METHOD__ . ' Line:' . __LINE__, ERROR_RUNTIME);
        }
        $this->log('Data found: headers: ' . print_r($data[0], true) . ' rows to load' . count($data), __METHOD__);

        return $data;
    }

    /**
     * @throws RuntimeException
     *
     * @return int|string
     */
    protected function validateHeaders(array $data)
    {
        $firstRow = array_keys($data);
        // get the first key
        $firstRow = $firstRow[0];
        $headers = $data[$firstRow];
        $this->log('Validating header: valid header are: ' . print_r($headers, true), __METHOD__);
        foreach ($headers as $key => $header) {
            $header = mb_strtolower($header, 'UTF-8');
            if ('kadident' == $header || 'kad_ident' == $header || 'imot_code' == $header || 'КАДИДЕНТ' == $header
                || 'идентификатор' == $header || 'имот номер' == $header || 'Име на парцела' == $header) {
                return $key;
            }
        }
        $this->log('ERROR! not a valid header found for kad ident ', __METHOD__);

        throw new RuntimeException(__METHOD__ . ' Line:' . __LINE__, ERROR_RUNTIME);
    }

    /**
     * @throws RuntimeException
     *
     * @return array
     */
    protected function setKadIdents(array $data, $key)
    {
        $temp = [];
        foreach ($data as $row) {
            $temp[] = $row[$key];
        }
        $kad_idents = array_unique(array_filter($temp));
        if (true == empty($kad_idents)) {
            throw new RuntimeException(__METHOD__ . ' Line:' . __LINE__, ERROR_RUNTIME);
        }
        // unset first row with header from the array
        unset($kad_idents[0]);
        $kad_idents = array_values($kad_idents);
        $this->log('kad_idents to load: ' . print_r($kad_idents, true), __METHOD__);

        return $kad_idents;
    }

    /**
     * @throws RuntimeException
     *
     * @return array
     */
    protected function setEkatte()
    {
        $separator = null;
        if (false !== strpos($this->kad_idents[0], '.')) {
            $separator = '.';
        } elseif (false !== strpos($this->kad_idents[0], '-')) {
            $separator = '-';
        } else {
            throw new RuntimeException(__METHOD__ . ' Line:' . __LINE__, ERROR_RUNTIME);
        }
        $temp = [];
        // extract the ekatte from the ka idents
        foreach ($this->kad_idents as $row) {
            $parts = explode($separator, $row);
            // the ekatte is the first element of the kad ident
            $temp[] = $parts[0];
        }
        $ekattes = array_unique(array_filter($temp));
        if (empty($ekattes)) {
            throw new RuntimeException(__METHOD__ . ' Line:' . __LINE__, ERROR_RUNTIME);
        }
        $this->log('ekatte extracted: ' . print_r($ekattes, true), __METHOD__);

        return $ekattes;
    }

    /** We get [0][1]{oblast_name,ekatte} records in an array to know the destination(s) table,.
     * @param array $ekattes
     *
     * @throws RuntimeException
     *
     * @return array
     */
    protected function getTables($ekattes)
    {
        if (is_array($ekattes)) {
            $ekattes = sprintf("'%s'", implode("','", $ekattes));
        }
        $query = "SELECT o.obl_name, o.nuts_name, e.ekatte_code, e.ekatte_name FROM su_oblasti o
          INNER JOIN su_obshtini obi ON o.id = obi.nm_obl_id
          INNER JOIN su_kmetstva K ON K.nm_obst_id = obi.id
          INNER JOIN su_ekatte e ON e.nm_kmetst_id = K.id
          WHERE e.ekatte_code in ({$ekattes})";
        $cmd = $this->UsersController->DbHandler->DbModule->createCommand($query);
        $tables = $cmd->query()->readAll();
        if (empty($tables)) {
            $this->log('No records and source tables founds for ekatte: ' . $ekattes . '. Existing with status code 24 , ERROR_INVALID_DATA', __METHOD__);

            throw new RuntimeException(__METHOD__ . ' Line:' . __LINE__, ERROR_INVALID_DATA);
        }
        $this->log('source tables: ' . print_r($tables, true), __METHOD__);

        return $tables;
    }

    /**
     * @param string $sourseTable
     * @param string $destinationTable
     *
     * @throws TDbException
     *
     * @return bool
     */
    protected function copyLayerData($sourseTable, $destinationTable)
    {
        if (1 == $this->UserDbController->DbHandler->DbModule->getPdoInstance()->inTransaction()) {
            $this->UserDbController->DbHandler->DbModule->getPdoInstance()->rollBack();
        }
        $transaction = $this->UserDbController->DbHandler->DbModule->beginTransaction();

        $kad_string = sprintf("'%s'", implode("','", $this->kad_idents));

        $sql = 'INSERT INTO ' . $destinationTable . ' (kad_ident, geom, ekate, masiv, number, category, area_type, has_contracts, mestnost, include, participate, white_spots, used_area_by, area_farming, area_year, used_area, usable, document_area, is_edited, edit_date, edit_active_from, waiting_update, irrigated_area, comment, old_kad_ident, allowable_area, allowable_type, block, name) 
        SELECT kad_ident, geom, ekate, masiv, number, category, area_type, has_contracts, mestnost, include, participate, white_spots, used_area_by, area_farming, area_year, used_area, usable, document_area, is_edited, edit_date, edit_active_from, waiting_update, irrigated_area, comment, old_kad_ident, allowable_area, allowable_type, block, kad_ident 
        FROM layer_kvs 
        WHERE kad_ident IN (' . $kad_string . ')';

        $cmd = $this->UserDbController->DbHandler->DbModule->createCommand($sql);

        try {
            $cmd->execute();
            $created = $transaction->commit();
            $this->log('Import Excel table creation status: ' . $created, __METHOD__);
        } catch (Exception $e) {
            $this->log('Error!! Rolling back Transaction', __METHOD__);
            $transaction->rollback();

            throw $e;
        }

        return true;
    }

    /**
     * @param array $file
     */
    protected function generateMap($file)
    {
        $this->log('Generating map file...', __METHOD__);
        $options = [];
        $options['database'] = $file['database'];
        $options['user_id'] = $file['user_id'];
        $result = $this->LayersController->generateMapFile($options);
        $this->log('Map files generation: OK', __METHOD__);

        return $result;
    }

    /** get farmings.
     * @return array
     */
    private function getUserFarmings($userGroup)
    {
        $options = [
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $userGroup],
            ],
        ];

        $farming_results = $this->FarmingController->getFarmings($options);

        $farmings_array = [];
        $farming_count = count($farming_results);
        for ($i = 0; $i < $farming_count; $i++) {
            $farmings_array[$farming_results[$i]['id']] = $farming_results[$i]['name'];
        }

        return $farmings_array;
    }

    private function mainException(Exception $e)
    {
        $this->errorLog($e->getMessage());
    }

    private function errorLog($errorMsg = '')
    {
        $this->LayersController->log(1, 'cron-daemon', ERROR_RUNTIME, [$this->fileId]);
        $options = [];
        $options['id'] = $this->fileId;
        $options['mainData']['status'] = ERROR_RUNTIME;
        $options['mainData']['errors'] = $errorMsg;
        $this->LayersController->editItemFiles($options);
    }

    private function log($msg, $method, $requestArray = null, $resultArray = null)
    {
        // @noinspection ForgottenDebugOutputInspection
        echo($msg . PHP_EOL);
        $this->UsersController->groupLog(
            $this->User->Name,
            $this->User->UserID,
            $this->User->GroupID,
            'ImportExcelKvsClass',
            '',
            $method,
            $requestArray,
            $resultArray,
            $msg
        );
    }
}
