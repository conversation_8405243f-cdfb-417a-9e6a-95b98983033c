<?php

namespace TF\Engine\Kernel\CronsLoadData;

use Exception;
use TF\Application\Common\Config;
use TF\Application\Entity\DTO\EkatteDto;
use TF\Application\Entity\LayerStyles;
use TF\Application\Entity\UserLayers;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

class CsdProcessingClass extends AbstractLoadDataClass
{
    public $itemID;
    public $UserDbController;
    private $doFile;
    private $zdFile;
    private $tmpZdTable;
    private $tmpDoTable;
    private $ekatte;
    private $csdDataYear;

    /**
     * init - prado init function.
     */
    public function init($config)
    {
        parent::init($config);
    }

    public function initControllers($database)
    {
        $this->UsersDbController = new UserDbController($this->database);
        $this->FarmingController = new FarmingController();
        $this->LayersController = new LayersController();
    }

    public function setDatabase($database)
    {
        $this->database = $database;
    }

    public function setYear($year)
    {
        $this->csdDataYear = $year;
        $this->year = $this->FarmingController->getYearKeyByYear($year);
    }

    public function setEkatte(EkatteDto $ekatte)
    {
        $this->ekatte = $ekatte;
    }

    public function startProcessing()
    {
        $this->mainProcessingSimple();
    }

    public function setGroupId($groupId)
    {
        $this->groupID = $groupId;
    }

    public function setUserId($userId)
    {
        $this->userID = $userId;
    }

    public function setLayerType()
    {
        $this->layerType = Config::LAYER_TYPE_CSD;
    }

    public function initClassVariables($currentData)
    {
        $this->fileData = $currentData;
        $this->ID = $this->fileData['id'];
        $this->fileName = $this->fileData['filename'];
        $this->groupID = $this->fileData['group_id'];
        $this->userID = $this->fileData['user_id'];
        $this->itemID = $this->fileData['item_id'];
        $this->status = $this->fileData['status'];
        $this->database = $this->fileData['database'];
        $this->layerType = Config::LAYER_TYPE_CSD;
        $this->UserDbController = new UserDbController($this->database);
        $this->userDir = PUBLIC_UPLOAD_PLOT_DATA . DIRECTORY_SEPARATOR . $this->groupID;
        $this->filePath = $this->userDir . '/' . $this->ID . '.zip';
        $this->name = str_replace(' ', '_', $this->fileData['name']);
        $this->name = str_replace('(', '_', $this->name);
        $this->name = str_replace(')', '_', $this->name);
        $ext_pos = strrpos($currentData['filename'], '.');
        $fileNameWOExt = substr($currentData['filename'], 0, $ext_pos);
        $this->processingSubDir = $this->userSubDir = $this->userDir . '/' . $fileNameWOExt . '/';
        $this->modulesPath = SITE_PATH . 'crons/';
    }

    public function processSimpleItems()
    {
        // This check is done because some of the archives contain another directory in them.
        if (file_exists($this->processingSubDir . DIRECTORY_SEPARATOR . $this->name)) {
            $this->processingSubDir = $this->processingSubDir . DIRECTORY_SEPARATOR . $this->name;
        }

        $files = $this->LayersController->File->getFilesFromDir($this->processingSubDir);
        $this->doFile = $this->validateRequiredFile($files, 'DO_');
        $this->zdFile = $this->validateRequiredFile($files, 'ZD_');

        $this->validateRequiredColumns($this->doFile, [
            'EKATTE', 'IMOT_NO', 'OSZ_NO', 'DATA', 'DOG_KOD', 'DOG_DATA', 'DOG_NOVP', 'DOG_NACH', 'DOG_KRAJ',
            'DOG_DKA', 'POLZ_IDENT', 'POLZ_IME', 'SOBST_EGN', 'SOBST_IME', 'SOBST_ADR', 'SOBST_TEL',
        ]);
        $this->validateRequiredColumns($this->zdFile, [
            'EKATTE', 'IMOT_NO', 'GODINA', 'DATA', 'KOD', 'DOK_NO', 'DOK_DATA', 'DOK_NO', 'DOK_DATA', 'DOG_KOD',
            'DOG_NO', 'DOG_NACH', 'DOG_SROK', 'DOG_DATA', 'POLZ_DKA', 'POLZ_IDENT', 'POLZ_IME', 'JELANIE', 'SLUJ_VKL',
            'POLZ_EKT', 'POLZ_ADR', 'POLZ_TEL',
        ]);

        $this->validateFilesColumnValues();

        $this->commonOGR2OGR(null);
    }

    public function commonOGR2OGR($action)
    {
        $connStr = 'host=' . DEFAULT_DB_HOST . ' user=' . DEFAULT_DB_USERNAME . ' dbname=' . $this->database . ' password=' . DEFAULT_DB_PASSWORD . ' port=' . DEFAULT_DB_PORT;

        // Fill do table
        $this->tmpDoTable = 'tmp_do_' . $this->ekatte->ekatte_code;
        $doFileWithoutExtension = str_replace('.DBF', '', $this->doFile);
        $doSql = "SELECT
                EKATTE as ekatte, concat(EKATTE, '.', IMOT_NO) as kad_ident, OSZ_NO as osz_no, DATA as data,
                DOG_KOD as dog_kod, DOG_DATA as dog_data, DOG_NOVP as dog_novp, DOG_NACH as dog_nach,
                DOG_KRAJ as dog_kraj, DOG_DKA as dog_dka, POLZ_IDENT as polz_ident, POLZ_IME as polz_ime,
                SOBST_EGN as sobst_egn, SOBST_IME as sobst_ime, SOBST_ADR as sobst_adr, SOBST_TEL as sobst_tel
            FROM  {$doFileWithoutExtension}";

        $doCmdOGR2OGR = getenv('OGR2OGR_PATH')
            . " -f \"PostgreSQL\" PG:\"{$connStr}\" "
            . '"' . $this->processingSubDir . DIRECTORY_SEPARATOR . $this->doFile . '"'
            . ' -nln ' . $this->tmpDoTable . " -sql \"{$doSql}\" 2>&1";

        $this->execCommand($doCmdOGR2OGR);

        $this->tmpZdTable = 'tmp_zd_' . $this->ekatte->ekatte_code;
        $zdFileWithoutExtension = str_replace('.DBF', '', $this->zdFile);
        $zdSql = "SELECT 
                EKATTE as ekatte, concat(EKATTE, '.', IMOT_NO) as kad_ident, GODINA as godina, DATA as data,
                KOD as kod, DOK_NO as dok_no, DOK_DATA as dok_data, DOG_KOD as dog_kod, DOG_NO as dog_no,
                DOG_NACH as dog_nach, DOG_SROK as dog_srok, DOG_DATA as dog_data, POLZ_DKA as polz_dka, POLZ_IDENT as polz_ident,
                POLZ_IME as polz_ime, JELANIE as jelanie, SLUJ_VKL as sluj_vkl, POLZ_EKT as polz_ekt, POLZ_ADR as polz_adr,
                POLZ_TEL as polz_tel 
            FROM {$zdFileWithoutExtension}";

        $zdCmdOGR2OGR = getenv('OGR2OGR_PATH')
            . " -f \"PostgreSQL\" PG:\"{$connStr}\" "
            . '"' . $this->processingSubDir . DIRECTORY_SEPARATOR . $this->zdFile . '"'
            . ' -nln ' . $this->tmpZdTable . " -sql \"{$zdSql}\" 2>&1";

        $this->execCommand($zdCmdOGR2OGR);
    }

    public function finalActions()
    {
        $this->LayersController->File->removeFolder($this->userSubDir);
        $this->copyProcess();
        $this->dropTmpTables();
    }

    public function onAfterProcessing()
    {
        $this->deleteUserLayer();
        $userLayer = $this->createUserLayer();
        $this->generateLayerStyles($userLayer);
        $this->generateMatViews();
        $this->personalizeByAttribute();
        $this->updateFileStatus();
    }

    public function updateFileStatus()
    {
        $options = [];
        $options['mainData'] = [
            'status' => SUCCESSFULLY_TREATED,
        ];
        $options['id'] = $this->ID;

        $this->LayersController->editItemFiles($options);
    }

    public function generateLayerStyles(UserLayers $userLayer)
    {
        $this->initAuthUser($userLayer->group_id);
        if (!is_null($deleteLayerStyle = LayerStyles::finder()->find('table_name = :table_name', [':table_name' => $userLayer->table_name]))) {
            $deleteLayerStyle->delete();
        }

        $layerStyle = LayerStyles::generateDefaultStyle(Config::LAYER_TYPE_CSD, false);
        $layerStyle->layer_id = $userLayer->id;
        $layerStyle->table_name = $userLayer->table_name;
        $layerStyle->save();
    }

    protected function dataSource()
    {
        return $this->LayersController->getCsdFilesForProcessing();
    }

    protected function copyProcess()
    {
        $transaction = $this->UserDbController->DbHandler->DbModule->beginTransaction();

        try {
            $this->removeDbEntriesIfEkatteFound('su_consolidation_do');
            $this->transferDataFromTmpTableToDoTable(
                $this->tmpDoTable,
                'su_consolidation_do',
            );

            $this->removeDbEntriesIfEkatteFound('su_consolidation_zd');
            $this->transferDataFromTmpTableToZdTable(
                $this->tmpZdTable,
                'su_consolidation_zd'
            );

            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollback();
            $this->dropTmpTables();

            throw $e;
        }
    }

    protected function getLayerName()
    {
        return "чл. 69 и 70 {$this->ekatte->ekatte_name} {$this->csdDataYear}";
    }

    protected function getLayerTableName()
    {
        return "layer_decl_69_70_{$this->ekatte->ekatte_code}_{$this->csdDataYear}";
    }

    private function transferDataFromTmpTableToDoTable(string $tmpTable, string $doTable)
    {
        $sql = "INSERT INTO 
                {$doTable} (
                    ekatte, kad_ident, osz_no, data, dog_data,
                    dog_novp, dog_nach, dog_kraj, dog_dka, polz_ident,
                    polz_ime, sobst_egn, sobst_ime, sobst_adr, sobst_tel,
                    dog_kod, godina
                ) 
                SELECT 
                    tmp.ekatte, tmp.kad_ident, tmp.osz_no, tmp.data,tmp.dog_data,
                    tmp.dog_novp, tmp.dog_nach, tmp.dog_kraj, tmp.dog_dka, tmp.polz_ident,
                    tmp.polz_ime, tmp.sobst_egn, tmp.sobst_ime, tmp.sobst_adr, tmp.sobst_tel,
                    COALESCE(do_dog_kod.dog_kod, 0) as dog_kod,
                    {$this->csdDataYear} as godina
                FROM 
                    {$tmpTable} tmp
                LEFT JOIN su_consolidation_do_dog_kod do_dog_kod
                    ON tmp.dog_kod = do_dog_kod.dog_kod
            ";

        $this->executeSql($sql);
    }

    private function transferDataFromTmpTableToZdTable(string $tmpTable, string $zdTable)
    {
        $sql = "INSERT INTO 
                {$zdTable} (
                    ekatte, kad_ident, godina, data, dok_no, dok_data, dog_no,
                    dog_data, dog_nach, dog_srok, polz_dka, polz_ident, polz_ime,
                    sluj_vkl, polz_ekt, polz_adr, polz_tel,
                    kod, dog_kod, jelanie
                ) 
                SELECT 
                    tmp.ekatte, tmp.kad_ident, tmp.godina, tmp.data, tmp.dok_no, tmp.dok_data, tmp.dog_no,
                    tmp.dog_data, tmp.dog_nach, tmp.dog_srok, tmp.polz_dka, tmp.polz_ident, tmp.polz_ime,
                    tmp.sluj_vkl, tmp.polz_ekt, tmp.polz_adr, tmp.polz_tel,
                    COALESCE(zd_kod.kod, 0) as kod, COALESCE(zd_dog_kod.dog_kod, 0) as dog_kod, COALESCE(zd_jelanie.jelanie, 0) as jelanie
                FROM 
                    {$tmpTable} tmp
                LEFT JOIN su_consolidation_zd_kod zd_kod
                    ON tmp.kod = zd_kod.kod
                LEFT JOIN su_consolidation_zd_dog_kod zd_dog_kod
                    ON tmp.dog_kod = zd_dog_kod.dog_kod
                LEFT JOIN  su_consolidation_zd_jelanie zd_jelanie
                    ON tmp.jelanie = zd_jelanie.jelanie
            ";
        $this->executeSql($sql);
    }

    private function deleteUserLayer()
    {
        $this->LayersController->DbHandler->deleteItem('su_users_layers', ['group_id', 'layer_type', 'year', 'table_name'], [$this->groupID, Config::LAYER_TYPE_CSD, $this->year, $this->getLayerTableName()]);
    }

    private function getLayerExtent()
    {
        $maxExtent = $this->UsersDbController->getMaxExtent($this->getLayerTableName());
        $maxExtent = str_replace('BOX(', '', $maxExtent);
        $maxExtent = str_replace(')', '', $maxExtent);

        return str_replace(',', ' ', $maxExtent);
    }

    private function generateMatViews()
    {
        $this->UsersDbController->createCsdMatView(
            $this->getLayerTableName(),
            $this->ekatte->ekatte_code,
            $this->csdDataYear
        );
    }

    private function personalizeByAttribute()
    {
        $LayersController = new LayersController('Layers');
        $layer = UserLayers::getLayerByTableName($this->getLayerTableName(), $this->groupID);
        $csdColumnDefinitions = $layer->getDefinitions();
        [$applicantColumnDefinition] = UserLayers::filterDefinitions($csdColumnDefinitions, [['col_name' => 'applicant']]);
        [$declarationTypeColumnDefinition] = UserLayers::filterDefinitions($csdColumnDefinitions, [['col_name' => 'declaration_type']]);

        [$style] = $layer->getStyles();
        $style = $style->toArray();
        $style['type'] = LayerStyles::BY_ATTRIBUTE_COLORING_TYPE;
        $style['fill_column_name'] = $applicantColumnDefinition['col_name'];
        $style['border_column_name'] = $declarationTypeColumnDefinition['col_name'];

        $LayersController->saveLayerPersonalization($layer, $style, self::class, '');
    }

    private function validateRequiredFile(array $files, string $filePattern): string
    {
        $files = $this->filterFiles($files, $filePattern, 'DBF');

        if (!count($files)) {
            throw new Exception(__METHOD__ . ' Line:' . __LINE__, ERROR_INVALID_DBF);
        }

        if (count($files) > 1) {
            throw new Exception(__METHOD__ . "There is more than one {$filePattern} file! Please upload just one", ERROR_INVALID_DBF);
        }

        return $files[0]['name'];
    }

    private function validateRequiredColumns(string $fileName, array $requiredColumns): void
    {
        $missing_columns = [];

        $fileWithoutExtension = str_replace('.DBF', '', $fileName);
        $command = getenv('OGRINFO_PATH') . " -ro -so  \"{$this->processingSubDir}\" \"{$fileWithoutExtension}\" 2>&1";
        $output = shell_exec($command);

        foreach ($requiredColumns as $column) {
            if (!str_contains($output, $column)) {
                $missing_columns[] = $column;
            }
        }

        if (0 === count($missing_columns)) {
            return;
        }

        throw new Exception(__METHOD__ . ' Missing columns: ' . implode(', ', $missing_columns), ERROR_MISSING_COLUMN);
    }

    private function validateFilesColumnValues()
    {
        $doEkatte = $this->findEkatte($this->doFile);
        $zdEkatte = $this->findEkatte($this->zdFile);
        $csdDataYear = $this->findYear($this->zdFile);

        if (!$this->ekatteOwnedByOrganization($doEkatte->ekatte_code)) {
            throw new Exception(Config::$FILE_STATUSES_MAP[ERROR_MISSING_KVS_EKATTE], ERROR_MISSING_KVS_EKATTE);
        }

        if (!$doEkatte->ekatte_code || !$zdEkatte->ekatte_code) {
            throw new Exception(__METHOD__ . ' Ekatte not found', ERROR_INVALID_FILE_DATA);
        }

        if ($doEkatte->ekatte_code !== $zdEkatte->ekatte_code) {
            throw new Exception(__METHOD__ . ' Ekatte in DO and ZD file is different', ERROR_INVALID_FILE_DATA);
        }

        if (!$csdDataYear) {
            throw new Exception(__METHOD__ . ' Year is required', ERROR_INVALID_FILE_DATA);
        }

        $this->setEkatte($doEkatte);
        $this->setYear($csdDataYear);
    }

    private function ekatteOwnedByOrganization(string $ekatte): bool
    {
        return array_pop($this->UserDbController->getItemsByParams(
            [
                'return' => ['count(ekate)'],
                'tablename' => 'ekate_combobox',
                'where' => [
                    'ekate' => ['column' => 'ekate', 'compare' => '=', 'value' => $ekatte],
                ],
            ]
        ))['count'] > 0;
    }

    private function removeDbEntriesIfEkatteFound(string $dbTable)
    {
        $sql = "select distinct ekatte from {$dbTable} where ekatte = '{$this->ekatte->ekatte_code}';";
        $foundEkatte = $this->executeSql($sql, true);

        if (count($foundEkatte)) {
            $sql = "delete from {$dbTable} where ekatte = '{$this->ekatte->ekatte_code}' and godina = '{$this->csdDataYear}';";
            $this->executeSql($sql);
        }
    }

    private function findEkatte($fileName): ?EkatteDto
    {
        $fileWithoutExtension = str_replace('.DBF', '', $fileName);
        $command = getenv('OGRINFO_PATH') . " \"{$this->processingSubDir}\" -sql \"SELECT distinct EKATTE FROM {$fileWithoutExtension} LIMIT 1\" 2>&1";
        $output = shell_exec($command);

        preg_match('/EKATTE \(String\) = (\d+)/', $output, $matches);

        return $this->UserDbController->findEkatteByCode((string )$matches[1]);
    }

    private function findYear($fileName)
    {
        $fileWithoutExtension = str_replace('.DBF', '', $fileName);
        $command = getenv('OGRINFO_PATH') . " \"{$this->processingSubDir}\" -sql \"SELECT distinct GODINA FROM {$fileWithoutExtension} LIMIT 1\" 2>&1";
        $output = shell_exec($command);

        preg_match('/GODINA \(Integer\) = (\d+)/', $output, $matches);

        return $matches[1];
    }

    private function execCommand($cmdOGR2OGR)
    {
        exec($cmdOGR2OGR, $out, $resultCode);

        if (0 !== $resultCode) {
            throw new Exception(__METHOD__ . ' Line:' . __LINE__, ERROR_RUNTIME);
        }
    }

    private function executeSql($sql, bool $readData = false)
    {
        $cmd = $this->UserDbController->DbHandler->DbModule->createCommand($sql);

        if ($readData) {
            return $cmd->query()->read();
        }

        $cmd->execute();
    }

    private function dropTmpTables()
    {
        $sqlDO = 'DROP TABLE IF EXISTS ' . $this->tmpDoTable . '; ';
        $this->executeSql($sqlDO);

        $sqlZD = 'DROP TABLE IF EXISTS ' . $this->tmpZdTable . '; ';

        $this->executeSql($sqlZD);
    }
}
