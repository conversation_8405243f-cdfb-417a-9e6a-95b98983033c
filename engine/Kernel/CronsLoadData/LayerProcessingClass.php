<?php

namespace TF\Engine\Kernel\CronsLoadData;

use Exception;
use TF\Application\Common\Config;
use TF\Application\Entity\LayerStyles;
use TF\Application\Entity\UserLayers;

/**
 * <AUTHOR> <<EMAIL>>
 *
 * @link http://www.technofarm.bg/
 *
 * @copyright Copyright &copy; 2015 TechnoFarm Ltd.
 */
class LayerProcessingClass extends AbstractLoadDataClass
{
    /**
     * init - prado init function.
     */
    public function init($config)
    {
        parent::init($config);

        if (Config::LAYER_TYPE_KMS == $this->layerType) {
            $options = [];
            $options['farming'] = $this->farming;
            $options['year'] = $this->year;
            $options['group_id'] = $this->groupID;
            $options['layer_type'] = $this->layerType;

            $tableName = $this->LayersController->getTableNameByParams($options);
            $this->tmpTable = 'tmp_' . $tableName . '_' . $this->ID;
        }
    }

    /**
     * startProcessing.
     */
    public function startProcessing()
    {
        $this->mainProcessingSimple();
    }

    /**
     * dataSource.
     *
     * @return array of data from DB
     */
    public function dataSource()
    {
        return $this->LayersController->getFilesForProcessing();
    }

    /**
     * copyProcess executes the process to copy the data.
     */
    public function copyProcess()
    {
        $options = [];
        $options['farming'] = $this->farming;
        $options['year'] = $this->year;
        $options['group_id'] = $this->groupID;
        $layerType = $this->layerType;
        $options['layer_type'] = $layerType;
        $tableName = $this->LayersController->getTableNameByParams($options);

        // uodate layer CRS
        try {
            $this->UsersDbController->transformColumnCRS($this->tmpTable, DEFAUL_DB_CRS);
        } catch (Exception $e) {
            throw new Exception($e->getMessage() . '\n=====' . $e->getTraceAsString(), ERROR_INVALID_GEOMETRY);
        }

        $options = [];
        $options['farming'] = $this->farming;
        $options['year'] = $this->year;
        $options['user_id'] = $this->groupID;
        $options['layer_type'] = $layerType;
        $layerID = $this->LayersController->getLayersIdByParams($options);

        $this->UsersDbController->copyDataFromTMPTable($tableName, $this->layerType, $layerID, $this->definition, true, $this->ID);

        $maxExtent = $this->UsersDbController->getMaxExtent($tableName);
        $maxExtent = str_replace('BOX(', '', $maxExtent);
        $maxExtent = str_replace(')', '', $maxExtent);
        $maxExtent = str_replace(',', ' ', $maxExtent);

        $options = [];
        $options['mainData'] = [
            'extent' => $maxExtent,
            'is_exist' => true,
        ];
        $options['id'] = $layerID;
        $this->LayersController->editItem($options);
    }

    /**
     * onAfterProcessing - we can call it after the Processing.
     */
    public function onAfterProcessing()
    {
        $this->data = $this->LayersController->getFilesForProcessing(true);
        $this->data = $this->data[0];
        if (0 != count($this->data)) {
            $this->initClassVariables($this->data);
            // definition tables
        }

        if (Config::LAYER_TYPE_ISAK == $this->layerType || Config::LAYER_TYPE_KMS == $this->layerType) {
            $options = [];
            $options['farming'] = $this->farming;
            $options['year'] = $this->year;
            $options['user_id'] = $this->groupID;
            $options['layer_type'] = $this->layerType;
            $layerID = $this->LayersController->getLayersIdByParams($options);

            $this->generateLayerStyles($layerID);
            // creates isak-layer_allowable difference mat. view
            if (Config::LAYER_TYPE_ISAK == $this->layerType) {
                $this->UsersDbController->createAllowableFromIsakReportView($layerID);
            }
        }
    }

    protected function generateLayerStyles($layerId)
    {
        $layer = UserLayers::getLayerById($layerId);

        $this->initAuthUser($layer->group_id);

        if (!empty($existingLayerStyle = LayerStyles::finder()->findAll('layer_id = :layer_id', [':layer_id' => (string) $layerId]))) {
            $styles = array_combine(array_column($existingLayerStyle, 'layer_id'), $existingLayerStyle);
            $style = $styles[$layerId];
            $style = $style->toArray();
        } else {
            $layerStyle = LayerStyles::generateDefaultStyle($layer->layer_type, false);
            $layerStyle->layer_id = $layer->id;
            $layerStyle->table_name = $layer->table_name;
            $layerStyle->save();
            $style = $layerStyle->toArray();
        }

        $this->LayersController->saveLayerPersonalization($layer, $style, LayerProcessingClass::class, '');
    }
}
