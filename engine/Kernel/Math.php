<?php

namespace TF\Engine\Kernel;

/**
 * Class for Precision Mathematics operations
 * https://www.php.net/manual/en/book.bc.php.
 */
class Math
{
    public static $scale = 15;

    /**
     * Add (a + b) two arbitrary precision numbers.
     *
     * @param string $addends1
     * @param string $addends2
     * @param int $scale
     *
     * @return string
     */
    public static function add($addends1, $addends2, $round = 6)
    {
        $result = bcadd((string)$addends1, (string)$addends2, self::$scale);

        if ($round) {
            $result = self::round($result, $round);
        }

        return $result;
    }

    /**
     * Subtract (a - b) one arbitrary precision number from another.
     *
     * @param string $minuend
     * @param string $subtrahend
     * @param int $scale
     *
     * @return string
     */
    public static function sub($minuend, $subtrahend, $round = 6)
    {
        $result = bcsub((string)$minuend, (string)$subtrahend, self::$scale);

        if ($round) {
            $result = self::round($result, $round);
        }

        return $result;
    }

    /**
     * Multiply (a * b) two arbitrary precision numbers.
     *
     * @param string $multiplicand
     * @param string $multiplier
     * @param int $scale
     *
     * @return string
     */
    public static function mul($multiplicand, $multiplier, $round = 6)
    {
        $result = bcmul((string)$multiplicand, (string)$multiplier, self::$scale);

        return self::round($result, $round);
    }

    /**
     * Divide (a / b) two arbitrary precision numbers.
     *
     * @param string $dividend
     * @param string $divisor
     * @param int $scale
     *
     * @return string
     */
    public static function div($dividend, $divisor, $round = 6)
    {
        $result = bcdiv((string)$dividend, (string)$divisor, self::$scale);

        return self::round($result, $round);
    }

    /**
     * Compares num1 to num2 and returns the result of the comparison as an integer.
     *
     *  0 if both operands are equal,
     *  1 if num1 is greater than num2,
     * -1 if num2 is greater than num1
     *
     * @param string $num1
     * @param string $num2
     *
     * @return int
     *             0 if both operands are equal,
     *             1 if num1 is greater than num2,
     *             -1 if num2 is greater than num1
     */
    public static function compare($num1, $num2)
    {
        return bccomp((string)$num1, (string)$num2);
    }

    /**
     * Round arbitrary precision number.
     *
     * @param string $num
     * @param int $scale
     *
     * @return string
     */
    public static function round($num, $round = 6)
    {
        return round($num, $round); // Replace this logic with bcround when PHP 8.0 is used in TF
    }
}
