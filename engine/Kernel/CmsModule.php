<?php

namespace TF\Engine\Kernel;

use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use InvalidArgumentException;
use <PERSON>rado\TModule;

class CmsModule extends TModule
{
    public const PLATFORM_NAME = 'cmsModule';
    public const APPLICATION_JSON = ['accept' => 'application/json'];
    public const PACKAGE_SLUG_ADMINISTRATIVE_MAP = 'Administrative map';

    private $httpClient;

    public function init($config)
    {
        $this->httpClient = new Client([
            'base_uri' => GEOSCAN_CMS_BASE_URI,
        ]);
    }

    public function getSubscriptionPackage(string $packageSlug): array
    {
        return $this->makeRequest('GET', '/api/organizations/subscriptions', [
            'integration' => 'TF',
            'filterPerOrganization' => true,
            'packageSlug' => $packageSlug,
        ], [], 'query');
    }

    private function makeRequest(string $method, string $url, array $body, array $header = [], $contentType = 'json')
    {
        try {
            $options = ['timeout' => 120];
            $headers['Authorization'] = 'Bearer ' . ($header['token'] ?? $this->User->getAccessToken()->getToken());
            $headers['Accept'] = 'application/json';

            switch ($contentType) {
                case 'json':
                    $options['query'] = $body;

                    break;
                case 'multipart':
                    $options['multipart'] = $this->handleMultipart($body);

                    break;
                case 'query':
                    $options['query'] = $body;

                    break;
                default:
                    throw new InvalidArgumentException("Invalid content type: {$contentType}");
            }

            $response = $this->httpClient->request($method, $url, array_merge(['headers' => $headers], $options));

            return json_decode($response->getBody()->getContents(), true);
        } catch (ClientException $e) {
            throw new Exception($e->getMessage(), $e->getCode(), $e);
        }
    }

    private function handleMultipart(array $body): array
    {
        $multipart = [];

        foreach ($body as $name => $contents) {
            $multipart[] = [
                'name' => $name,
                'contents' => is_array($contents) ? json_encode($contents) : $contents,
            ];
        }

        return $multipart;
    }
}
