<?php

namespace TF\Engine\Kernel;

use Exception;
use Firebase\JWT\JWK;
use Firebase\JWT\JWT;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;

class KeycloakToken
{
    private $httpCLient;

    public function __construct()
    {
        $this->httpCLient = new Client([
            'base_uri' => getenv('KEYCLOAK_AUTH_SERVER_URL'),
        ]);
    }

    /**
     * Decode a JWT token.
     *
     * @param string $token
     * @param $publicKey
     *
     * @return null|mixed
     */
    public function decode(string $token = null, int $leeway = 0)
    {
        JWT::$leeway = $leeway;
        $jwk = $this->getJwk();

        try {
            $decoded = (array) JWT::decode($token, JWK::parseKeySet($jwk), [getenv('KEYCLOAK_ALGORYTHM')]);
        } catch (Exception $ex) {
            throw $ex;
        }

        return $token ? $decoded : null;
    }

    /**
     * Get public json web keys from identity provider.
     */
    public function getJwk(): array
    {
        try {
            $httpCLient = new Client([
                'base_uri' => getenv('KEYCLOAK_AUTH_SERVER_URL'),
            ]);

            $response = $httpCLient->request('GET', $this->buildCertsUrl(), []);

            $response = json_decode($response->getBody()->getContents(), true);

            $jwk = array_filter($response['keys'], function ($value) {
                if ($value['alg'] === getenv('KEYCLOAK_ALGORYTHM')) {
                    return $value;
                }
            }, ARRAY_FILTER_USE_BOTH);

            return ['keys' => $jwk];
        } catch (ClientException $exception) {
            throw new Exception('Public key not found');
        }
    }

    /**
     * Build identity provider url for public keys.
     */
    private function buildCertsUrl(): string
    {
        return 'realms/' . getenv('KEYCLOAK_REALM') . '/protocol/openid-connect/certs';
    }
}
