<?php

namespace TF\Engine\Kernel;

use Prado\Prado;

/**
 * Controller class file.
 *
 * <AUTHOR> Ltd. <<EMAIL>>
 *
 * @link http://www.devision.bg/
 *
 * @copyright Copyright &copy; 2008 Devision Ltd.
 */

/**
 * Controller class.
 *
 * This is the main controller class in ATOM. Every plugin's controller is successor of this class.
 */
class Controller
{
    public $DbHandler;
    public $trashPageUrl = '';
    public $addPageUrl = '';
    public $editPageUrl = '';
    public $homePageUrl = '';
    public $metaDataPageUrl = '';
    public $Langs;
    public $Image;
    public $StringHelper;
    public $File;
    public $Mail;
    public $Logger;
    public $CheckOut;
    public $Application;
    public $HtmlToPdf;
    /**
     * @var ArrayHelper
     */
    public $ArrayHelper;
    public $pluginName = '';
    public $MemCache;
    public $Validator;
    public $default_memcache_expire = 900; // 15 mins

    /**
     * Constructor of the class.
     *
     * @param string $title, title of the plugin witch extend this class
     *
     * @return void()
     */
    public function __construct($title)
    {
        $this->pluginName = $title;

        $this->trashPageUrl = $title . '.Trash';
        $this->addPageUrl = $title . '.Add';
        $this->editPageUrl = $title . '.Edit';
        $this->homePageUrl = $title . '.Home';
        $this->metaDataPageUrl = $title . '.MetaData';

        $this->Lang = new Loader('Kernel.Lang');                        // missing class
        $this->Image = new Image();                                     // new Loader('Kernel.Image');
        $this->StringHelper = new StringHelper();                       // new Loader('Kernel.StringHelper');
        $this->File = new File();                                       // new Loader('Kernel.File');
        $this->Mail = new Mail();                                       // new Loader('Kernel.Mail');
        $this->CheckOut = new Loader('Kernel.CheckOut');                // missing class
        $this->HtmlToPdf = new HtmlToPdf();                             // new Loader('Kernel.HtmlToPdf');
        $this->ArrayHelper = new ArrayHelper();                         // new Loader('Kernel.ArrayHelper');
        $this->Validation = new Validation();                           // new Loader('Kernel.Validation');
        $this->Application = Prado::getApplication();
        $this->initMemcache();
    }

    public function initMemcache()
    {
        $application = Prado::getApplication();
        $this->MemCache = new MemCacheHolder();
        $this->MemCache->MemCache = $application->getModule('cache');
    }

    /**
     * Logs an event.
     *
     * @param int $type, type of a message
     * @param string $username, which user is create a log
     * @param string $message, text of the message
     * @param array $ids, ids of records, which are affected of some event
     */
    public function log($type, $username, $message, $ids = [])
    {
        if (count($ids)) {
            $idsString = '; ID: ' . @implode(', ', $ids);
        } else {
            $idsString = '';
        }

        switch ($type) {
            case LoggerType::MESSAGE:
                $message = "User: {$username}; {$message}{$idsString}";

                break;
            case LoggerType::ITEM:
                $table = $this->DbHandler->tableName;
                $message = "User: {$username}; Table: {$table}; {$message} (Item){$idsString}";

                break;
            case LoggerType::CATEGORY:
                $table = $this->DbHandler->tableNameCategories;
                $message = "User: {$username}; Table: {$table}; {$message} (Category){$idsString}";

                break;
            case LoggerType::PICTURE:
                $table = $this->DbHandler->tableNamePictures;
                $message = "User: {$username}; Table: {$table}; {$message} (Picture){$idsString}";

                break;
            case LoggerType::VIDEO:
                $table = $this->DbHandler->tableNameVideos;
                $message = "User: {$username}; Table: {$table}; {$message} (Video){$idsString}";

                break;
            case LoggerType::FILE:
                $table = $this->DbHandler->tableNameFiles;
                $message = "User: {$username}; Table: {$table}; {$message} (File){$idsString}";

                break;
            case LoggerType::ANALYSIS:
                $table = $this->DbHandler->tableNameAnalysisArticle;
                $message = "User: {$username}; Table: {$table}; {$message} (Analysis){$idsString}";

                break;
            case LoggerType::LEADING:
                $table = $this->DbHandler->tableNameLeading;
                $message = "User: {$username}; Table: {$table}; {$message} (Leading){$idsString}";

                break;
            case LoggerType::ANSWER:
                $table = $this->DbHandler->tableNameAnswers;
                $message = "User: {$username}; Table: {$table}; {$message} (Answer){$idsString}";

                break;
            case LoggerType::SEASON:
                $table = $this->DbHandler->tableNameSeasons;
                $message = "User: {$username}; Table: {$table}; {$message} (Season){$idsString}";

                break;
            case LoggerType::RATING:
                $table = $this->DbHandler->tableNameComments;
                $message = "User: {$username}; Table: {$table}; {$message} (Comment){$idsString}";

                break;
            case LoggerType::DIRECTORY:
                $table = $this->DbHandler->tableNameDirs;
                $message = "User: {$username}; Table: {$table}; {$message} (Directory){$idsString}";

                break;
            case LoggerType::OPTION:
                $table = $this->DbHandler->tableNameOptions;
                $message = "User: {$username}; Table: {$table}; {$message} (Option){$idsString}";

                break;
            case LoggerType::OPTION_VALUE:
                $table = $this->DbHandler->tableNameOptionsValues;
                $message = "User: {$username}; Table: {$table}; {$message} (Option value){$idsString}";

                break;
            case LoggerType::CUSTOM_FIELD:
                $table = $this->DbHandler->tableNameCustomFields;
                $message = "User: {$username}; Table: {$table}; {$message} (Custom field){$idsString}";

                break;
            case LoggerType::DELIVERY:
                $table = $this->DbHandler->tableNameDeliveries;
                $message = "User: {$username}; Table: {$table}; {$message} (Delivery){$idsString}";

                break;
            case LoggerType::PAYMENT:
                $table = $this->DbHandler->tableNamePayments;
                $message = "User: {$username}; Table: {$table}; {$message} (Payment){$idsString}";

                break;
            default:
                $message = "User: {$username}; {$message}{$idsString}";

                break;
        }

        $userIP = $_SERVER['REMOTE_ADDR'];
        if (array_key_exists('HTTP_X_REAL_IP', $_SERVER)) {
            $userIP = $_SERVER['HTTP_X_REAL_IP'];
        }
        $message .= ' [IP] ' . $userIP;

        Prado::log($message, 2, $this->pluginName);
    }

    /**
     * Open and write into group log file.
     *
     * @param string $username, username
     * @param string $userID, userID
     * @param string $groupID, groupID
     * @param string $module, defined in class
     * @param string $action, rpc-serv-id
     * @param string $methodname, method that called the function
     * @param array $requestArray, param array for the performed action
     * @param array $resultArray, array result of the action
     * @param string $message, ifromation message
     */
    public function groupLog($username, $userID, $groupID, $module, $action, $methodname, $requestArray = [], $resultArray = [], $message = '')
    {
        $filename = LOG_PATH . $groupID . '.log';

        if (!file_exists($filename)) {
            fopen($filename, 'w');
        }

        $date = date('Y F d H:i:s');

        $userIP = $_SERVER['REMOTE_ADDR'];
        if (array_key_exists('HTTP_X_REAL_IP', $_SERVER)) {
            $userIP = $_SERVER['HTTP_X_REAL_IP'];
        }

        $logMessage = "{$date} [Info] [IP] " . $userIP . " [Module: {$module}] [Username: {$username}] [UserID: {$userID}] ";
        $logMessage .= " [RPC_SERVICE_ID: {$action}]";
        $logMessage .= " [Method: {$methodname}]";
        if ($message) {
            $logMessage .= " [DeveloperMessage: {$message}]";
        }

        $logMessage .= ' [RequestArray: ' . json_encode($requestArray, JSON_UNESCAPED_UNICODE) . ']';
        $logMessage .= ' [ResultArray: ' . json_encode($resultArray, JSON_UNESCAPED_UNICODE) . ']';

        file_put_contents($filename, $logMessage . "\n", FILE_APPEND);
    }

    /**
     * Sets the connection information for the DbHandler.
     *
     * @param string $connection
     * @param string $prefix
     */
    public function setDbConnection($connection, $prefix)
    {
        // $this->DbHandler->setDbModule($connection);
        // $this->DbHandler->setDbPrefix($prefix);
    }

    /**
     * Exports the files and sql for the selected plugin.
     *
     * @param ZipArchive $archive
     * @param string $archivePath
     * @param int $filesCount
     * @param TXmlDocument $xml
     */
    public function export($archive, $archivePath, &$filesCount, $xml)
    {
        if (method_exists($this, 'getTables') and ($tablesArray = $this->getTables())) {
            $prefix = $this->DbHandler->DbPrefix;
            foreach ($tablesArray as $table) {
                $data = $this->DbHandler->getExportData($table);
                $tableElement = new TXmlElement('table');
                if ($hasPrefix = 0 === strpos($table, $prefix)) {
                    $table = substr($table, strlen($prefix));
                }
                $tableElement->Attributes['prefix'] = $hasPrefix;
                $tableElement->Attributes['name'] = $table;
                $xml->Elements[] = $tableElement;
                foreach ($data as $row) {
                    $rowElement = new TXmlElement('record');
                    $tableElement->Elements[] = $rowElement;
                    foreach ($row as $key => $field) {
                        $fieldElement = new TXmlElement('field');
                        $rowElement->Elements[] = $fieldElement;
                        $fieldElement->Attributes['key'] = $key;
                        if (null === $field) {
                            $fieldElement->Attributes['null'] = true;
                        } else {
                            $fieldElement->Value = $field;
                        }
                    }
                }
            }
        }
        if (method_exists($this, 'getDirectories') and ($dirs = $this->getDirectories())) {
            foreach ($GLOBALS['DISTRIBUTED_SERVERS'] as $key => $server) {
                foreach ($dirs as $dir) {
                    if ($dir) {
                        $this->File->archiveDir($archive, $archivePath, $server['storage_path'], $dir, $key, $filesCount);
                    }
                }
            }
        }
    }

    /**
     * Imports the files.
     *
     * @param string $path
     * @param TXmlDocument $xml
     * @param array $servers
     */
    public function import($path, $xml, $servers = null)
    {
        if (method_exists($this, 'getTables') and ($tablesArray = $this->getTables())) {
            $this->DbHandler->setCheckForeignKeys(0);
            $prefix = $this->DbHandler->DbPrefix;
            foreach ($tablesArray as $table) {
                foreach ($xml->getElementsByTagName('table') as $xmlTable) {
                    $xmlTableName = $xmlTable->Attributes['prefix'] ? $prefix . $xmlTable->Attributes['name'] : $xmlTable->Attributes['name'];
                    if ($xmlTableName == $table) {
                        $this->DbHandler->clearTable($table);
                        $tableInfo = $this->DbHandler->getTableInfo($table);
                        if ($tableInfo['exists']) {
                            foreach ($xmlTable->getElementsByTagName('record') as $xmlRecord) {
                                $record = [];
                                foreach ($xmlRecord->getElementsByTagName('field') as $xmlField) {
                                    if (in_array($xmlField->Attributes['key'], $tableInfo['fields'])) {
                                        $record[$xmlField->Attributes['key']] = $xmlField->Attributes['null'] ? null : $xmlField->Value;
                                    }
                                }
                                $this->DbHandler->addItem($table, array_keys($record), array_values($record), false);
                            }
                        }
                    }
                }
            }
            $this->DbHandler->setCheckForeignKeys(1);
        }

        if (method_exists($this, 'getDirectories') and ($dirs = $this->getDirectories())) {
            if (null === $servers) {
                $servers = $GLOBALS['DISTRIBUTED_SERVERS'];
            }
            foreach ($servers as $key => $server) {
                foreach ($dirs as $dir) {
                    $this->File->dircopy($path . '/' . $key . '/', $server['storage_path'], $dir);
                }
            }
        }
    }

    /**
     * Returns the orderby for the place.
     *
     * @param array $place - the place
     *
     * @return string
     */
    public function getOrderBy($place, $default = 'id')
    {
        if (empty($place)) {
            return $default;
        }

        if (!$_SESSION[$place['place']][$place['page']][$place['grid']]['orderby']) {
            $_SESSION[$place['place']][$place['page']][$place['grid']]['orderby'] = $default;
        }

        return $_SESSION[$place['place']][$place['page']][$place['grid']]['orderby'];
    }

    /**
     * Returns the ordertype for the place.
     *
     * @param array $place - the place
     *
     * @return string
     */
    public function getOrderType($place, $default = 'ASC')
    {
        if (empty($place)) {
            return $default;
        }

        if (!$_SESSION[$place['place']][$place['page']][$place['grid']]['ordertype']) {
            $_SESSION[$place['place']][$place['page']][$place['grid']]['ordertype'] = $default;
        }

        return $_SESSION[$place['place']][$place['page']][$place['grid']]['ordertype'];
    }

    /**
     * Returns $options['mainData'] for saving the ForIsakPropertyGrid.
     *
     * @param array $data
     *
     * @return array $options['mainData']
     */
    public function getMainDatOptionsForIsakPropertyGrid($params)
    {
        $crop = false;
        $cropcode = '';

        // this record holds the combobox data, if it's changed it is numeric
        if (is_numeric($params['cropcode']['value']) || is_null($params['cropcode']['value'])) {
            $cropcode = $params['cropcode']['value'];
            $crop = $GLOBALS['Farming']['crops'][$cropcode];
        } else {
            if (is_numeric($params['culture']['cropcode']) || $params['culture']['value']) {
                $cropcode = $params['culture']['cropcode'];
                $crop = $GLOBALS['Farming']['crops'][$cropcode];
            }
        }

        if (!$crop) {
            $options['mainData'] = [
                'prc_name' => $params['plot_name']['value'],
                'edited' => ('Да' == $params['to_edit']['value']) ? true : false,
                'comment' => $params['comment']['value'],
            ];
        } else {
            $green_area_factor = 1;
            $crop_short_type = '';
            if ($crop['azot_fixed_crop']) {
                $crop_short_type .= 'АФК, ';
                $green_area_factor = $GLOBALS['Farming']['crops_short_type']['azot_fixed_crop']['factor_enp'];
            }
            if ($crop['is_tree_short_rotation']) {
                $crop_short_type .= 'ДВКЦР, ';
                $green_area_factor = $GLOBALS['Farming']['crops_short_type']['is_tree_short_rotation']['factor_enp'];
            }
            // Угар
            if ('190000' == $cropcode) {
                $crop_short_type .= 'УГАР, ';
                $green_area_factor = $GLOBALS['Farming']['crops_short_type']['190000']['factor_enp'];
            }

            if (strlen($crop_short_type)) {
                $crop_short_type = substr($crop_short_type, 0, -2);
            }

            $options['mainData'] = [
                'prc_name' => $params['plot_name']['value'],
                'cropcode' => $cropcode,
                'edited' => ('Да' == $params['to_edit']['value']) ? true : false,
                'comment' => $params['comment']['value'],
                'crop_type' => $crop['crop_type'],
                'crop_genus' => $crop['crop_genus'],
                'azot_fixed_crop' => (($crop['azot_fixed_crop']) ? 'true' : 'false'),
                'is_intermediate_crop' => (($crop['is_intermediate_crop']) ? 'true' : 'false'),
                'is_intermediate_weat_crop' => (($crop['is_intermediate_weat_crop']) ? 'true' : 'false'),
                'is_tree_short_rotation' => (($crop['is_tree_short_rotation']) ? 'true' : 'false'),
                'no_pndn' => (($crop['no_pndn']) ? 'true' : 'false'),
                'green_area_factor' => $green_area_factor,
                'crop_short_type' => $crop_short_type,
                'cropname' => $crop['crop_name'],
            ];
        }

        if (is_numeric($params['ekatte']['value']) || is_null($params['ekatte']['value'])) {
            $options['mainData']['ekatte'] = $params['ekatte']['value'];
        }

        return $options['mainData'];
    }

    /**
     * Returns the matched by regular expression lines from the user log file.
     *
     * @param string $pattern the pattern against wich will be tested every
     *                        line from the log file
     * @param bool $stopOnFirstMatch if true stops and returns the first matched line
     *
     * @return array
     */
    public function regExpLogsSearch($logFile, $pattern, $stopOnFirstMatch = false)
    {
        $results = [];
        $matches = [];

        $reverseFileReader = new ReverseFile($logFile);

        foreach ($reverseFileReader as $line) {
            if (!preg_match_all($pattern, $line, $matches)) {
                continue;
            }

            $results[] = $matches;

            if ($stopOnFirstMatch) {
                return $results;
            }
        }

        return $results;
    }
}

class MemCacheHolder
{
    public $MemCache;

    public function get($hash = '')
    {
        if (!$this->MemCache) {
            return false;
        }

        return $this->MemCache->get($hash);
    }

    public function flush()
    {
        $this->MemCache->flush();
    }

    public function delete($hash = '')
    {
        $this->MemCache->delete($hash);
    }

    public function add($hash, $result, $timeout)
    {
        if ($this->MemCache) {
            return $this->MemCache->add($hash, $result, $timeout);
        }
    }
}
