<?php

namespace TF\Engine\Kernel;

use PDO;

/**
 * Kernel PDO Helper class.
 */
class PdoHelper
{
    /**
     * The method returns PDO Type for the passed value.
     *
     * @return int
     */
    public static function getPdoType($value)
    {
        if (is_int($value)) {
            return PDO::PARAM_INT;
        } elseif (is_bool($value)) {
            return PDO::PARAM_BOOL;
        } elseif (is_null($value)) {
            return PDO::PARAM_NULL;
        }

        return PDO::PARAM_STR;
    }
}
