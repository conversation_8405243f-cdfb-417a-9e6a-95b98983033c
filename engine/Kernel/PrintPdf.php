<?php

namespace TF\Engine\Kernel;

use RuntimeException;
use <PERSON><PERSON>\Browsershot\Browsershot;

class PrintPdf
{
    public const DEFAULT_MARGIN = 10;

    public function __construct() {}

    public function generateFromHtml($html, $filePath, $options = [], $overwrite = false)
    {
        if (!$overwrite && is_file($filePath)) {
            throw new RuntimeException(sprintf('File "%s" already exists', $filePath));
        }

        $browsershot = Browsershot::html($html)
            ->noSandbox()
            ->setOption('addStyleTag', json_encode(['content' => 'body{ font-size: 11px;} table{ font-size: 11px; width: 100%; line-height: 1; }']))
            ->margins(
                $options['margin-top'] ?? self::DEFAULT_MARGIN,
                $options['margin-right'] ?? self::DEFAULT_MARGIN,
                $options['margin-bottom'] ?? self::DEFAULT_MARGIN,
                $options['margin-left'] ?? self::DEFAULT_MARGIN
            );

        if (!empty($options['pageNumber'])) {
            $browsershot->showBrowserHeaderAndFooter()->hideHeader()->footerHtml(self::getFooterHtml($options['pageNumber']));
        }

        if (isset($options['orientation']) && 'landscape' == strtolower($options['orientation'])) {
            $browsershot->landscape();
        }

        $browsershot->save($filePath);

        $this->isFileExists($filePath);
    }

    public function isFileExists($filePath)
    {
        if (!is_file($filePath)) {
            throw new RuntimeException(sprintf('File "%s" was not created', $filePath));
        }
    }

    private static function getFooterHtml($pageNumberAlign = 'center')
    {
        return '<html>
                    <head>
                        <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
                        <style>
                            .pagination {
                                font-style: normal;
                                font-weight: normal;
                                font-size: 10px;
                                line-height: 12px;
                                text-align: center;
                                padding-top: 10px;
                            }
                
                            .footer {
                                width: 100%;
                                transform: scale(0.75);
                                bottom: 0;
                                border-top: 1px solid #C7C7C7;
                            }
                        </style>
                    </head>
                
                    <body>
                        <div class="footer">
                            <div class="pagination"  style="text-align:' . $pageNumberAlign . '">
                                <span class="pageNumber"></span> / <span class="totalPages"></span>
                            </div>
                        </div>
                    </body>
                </html>';
    }
}
