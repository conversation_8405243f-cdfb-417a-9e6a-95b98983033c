<?php

namespace TF\Engine\Kernel;

use <PERSON><PERSON>\TModule;

class Export2XlsClass extends TModule
{
    public const CSV_2_XLS_PATH = 'csv2xlsx';
    public const DELIMITER = ';';
    public const TMP_DIR = '/tmp';
    public const PYTHON_PATH = '';
    public $TmpDir;
    public $Delimiter;
    public $Csv2xlsPath;
    public $PythonPath;

    public function __construct()
    {
        $this->TmpDir = self::TMP_DIR;
        $this->Csv2xlsPath = CSV2XLS_PATH . self::CSV_2_XLS_PATH;
        $this->PythonPath = self::PYTHON_PATH;
        $this->Delimiter = self::DELIMITER;
    }

    /**
     * Exports the csv file as xsl directly to the browser or return the path to the file.
     *
     * @param string $fileName the name of the file we want to create
     * @param array $data the input data
     * @param array $columnHeaders the column names for the excel
     */
    public function export($fileName, array $data, array $columnHeaders = [], $forceDownload = true)
    {
        if (count($columnHeaders)) {
            array_unshift($data, $columnHeaders);
        }
        $name = str_replace('/', '_', pathinfo($fileName, PATHINFO_FILENAME));

        $csvPath = "{$this->TmpDir}/{$name}.csv";
        $xlsPath = "{$this->TmpDir}/{$name}.xlsx";

        touch($xlsPath);
        chmod($xlsPath, 0774);

        $fp = fopen($csvPath, 'w');
        chmod($csvPath, 0774);

        $dataCount = count($data);
        for ($i = 0; $i < $dataCount; $i++) {
            fputcsv($fp, $data[$i], $this->Delimiter);
        }

        fclose($fp);

        exec($this->Csv2xlsPath . ' -d "' . $this->Delimiter . '" -o "' . $xlsPath . '" "' . $csvPath . '" 2>&1', $out);
        if ($forceDownload) {
            $this->getResponse()->setContentType('application/force-download');
            $this->Response->writeFile($xlsPath);
            unlink($xlsPath);

            return;
        }

        return $xlsPath;
    }

    /**
     * Upload csv file as xls and return the path to the file. REQUIRES ABSOLUTE PATH!
     *
     * @param string $fileName the name of the file we want to create
     * @param array $data the input data
     * @param array $columnHeaders the column names for the excel
     *
     * @return string url path
     */
    public function exportUrlPath($path, array $data, array $columnHeaders = [])
    {
        $dirname = pathinfo($path, PATHINFO_DIRNAME);
        $pathBefore = pathinfo($dirname, PATHINFO_DIRNAME);

        // create folder
        if (!file_exists($pathBefore)) {
            mkdir($pathBefore, 0777);
        }

        if (!file_exists($dirname)) {
            mkdir($dirname, 0777);
        }
        $tempPath = $this->export($path, $data, $columnHeaders, false);

        if (copy($tempPath, $path)) {
            unlink($tempPath);
        }
        $path = explode('public/', $path);

        return array_pop($path);
    }
}
