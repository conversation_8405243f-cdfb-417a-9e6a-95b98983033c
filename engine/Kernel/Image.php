<?php

namespace TF\Engine\Kernel;

use TF\External\Lib\CropCanvas;

/**
 * Kernel Image class file.
 *
 * <AUTHOR>
 */

/**
 * Kernel Image class.
 *
 * This class is used to manipulate images on the ATOM
 */
class Image
{
    public $ccHandler;

    public function __construct()
    {
        $this->ccHandler = new CropCanvas();
    }

    /**
     * Resizes and creates new image file from existing one.
     * Last parameter $resizeOption defines if image is resized proportionally
     * or image is fixed to specified size.
     * $resizeOption values: "resize", "fixed".
     * If resize option is not specified image is resized by "resize" method.
     * File types: (JPG, PNG, GIF).
     *
     * @param string $file, file to be resized
     * @param string $newFile, path to new file
     * @param float $newWidth, new file width
     * @param float $newHeight, new file height
     * @param string $resizeOption, resize option
     */
    public function makeimage($file, $newFile, $newWidth, $newHeight, $resizeOption = null)
    {
        if (null == $resizeOption) {
            $resizeOption = 'resize';
        }

        list($width, $height, $type) = getimagesize($file);

        if (IMAGETYPE_GIF == $type || IMAGETYPE_JPEG == $type || IMAGETYPE_PNG == $type) {
            switch ($type) {
                case IMAGETYPE_GIF:
                    $source = imagecreatefromgif($file);

                    break;
                case IMAGETYPE_JPEG:
                    $source = imagecreatefromjpeg($file);

                    break;
                case IMAGETYPE_PNG:
                    $source = imagecreatefrompng($file);

                    break;
            }

            switch ($resizeOption) {
                // Resizes image proportionally to new size.
                case 'resize':
                    if ($width > $height) {
                        if ($newWidth > $width) {
                            $w = $width;
                        } else {
                            $w = $newWidth;
                        }

                        $h = $height * $newWidth / $width;

                        if ($h > $height) {
                            $h = $height;
                        }
                    } else {
                        if ($newHeight > $height) {
                            $h = $height;
                        } else {
                            $h = $newHeight;
                        }
                        $w = $width * $newHeight / $height;

                        if ($w > $width) {
                            $w = $width;
                        }
                    }
                    $dest = imagecreatetruecolor($w, $h);
                    imagecopyresampled($dest, $source, 0, 0, 0, 0, $w, $h, $width, $height);

                    break;

                    // Resizes image proportionally to new size.
                case 'forceResize':
                    if ($width > $height) {
                        $w = $newWidth;
                        $h = $height * $newWidth / $width;
                    } else {
                        $w = $width * $newHeight / $height;
                        $h = $newHeight;
                    }

                    $dest = imagecreatetruecolor($w, $h);
                    imagecopyresampled($dest, $source, 0, 0, 0, 0, $w, $h, $width, $height);

                    break;

                case 'resizeW':
                    if ($width > $newWidth) {
                        $w = $newWidth;
                        $h = $height * $newWidth / $width;
                    } else {
                        $w = $width;
                        $h = $height;
                    }
                    $dest = imagecreatetruecolor($w, $h);
                    imagecopyresampled($dest, $source, 0, 0, 0, 0, $w, $h, $width, $height);

                    break;

                case 'resizeH':
                    if ($height > $newHeight) {
                        $w = $width * $newHeight / $height;
                        $h = $newHeight;
                    } else {
                        $w = $width;
                        $h = $height;
                    }
                    $dest = imagecreatetruecolor($w, $h);
                    imagecopyresampled($dest, $source, 0, 0, 0, 0, $w, $h, $width, $height);

                    break;

                    // Fixes image to new size.
                case 'fixed':
                    if ($width / $height >= $newWidth / $newHeight) {
                        $w = $width * $newHeight / $height;
                        $h = $newHeight;
                        $x = -($w - $newWidth) / 2;
                        $y = 0;
                        $dest = imagecreatetruecolor($newWidth, $h);
                    } else {
                        $w = $newWidth;
                        $h = $height * $newWidth / $width;
                        $x = 0;
                        $y = -($h - $newHeight) / 2;
                        $dest = imagecreatetruecolor($w, $newHeight);
                    }
                    imagecopyresampled($dest, $source, $x, $y, 0, 0, $w, $h, $width, $height);

                    break;
            }

            return (bool) (imagejpeg($dest, $newFile, 100));
        }

        return false;
    }

    /**
     * Crop and creates new image file from existing one.
     * File types: (JPG, PNG, GIF).
     *
     * @param string $file, file to be resized
     * @param string $newFile, path to new file
     * @param int x1, x1 coordinate form original image
     * @param int y1, y1 coordinate from original image
     * @param int x2, x2 coordinate from original image
     * @param int y2, y2 coordinate from original image
     */
    public function makeImageWithCropSize($file, $newFile, $x1, $y1, $x2, $y2)
    {
        if (is_file($file)) {
            list($width, $height, $type, $attr) = getimagesize($file);
            if ($width < $x2) {
                $y2 = $y2 * $width / $x2;
                $x2 = $width;
            }

            if ($height < $y2) {
                $x2 = $x2 * $height / $y2;
                $y2 = $height;
            }

            $this->ccHandler->loadImage($file);
            $this->ccHandler->cropToDimensions($x1, $y1, $x2, $y2);
            $flag = $this->ccHandler->saveImage($newFile);
            $this->ccHandler->flushImages(false);
        }

        return $flag;
    }
}
