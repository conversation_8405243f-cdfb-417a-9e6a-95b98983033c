<?php

namespace TF\Engine\Kernel;

use Prado\Prado;

/**
 * Loader File class file.
 *
 * <AUTHOR>
 */

/**
 * Kernel File class.
 *
 * This class is used the controller to "load" the File, Image, Lang, Mail and String classes
 *
 * @TODO REWORK
 */
class Loader
{
    private $_path;
    private $_name;
    private $_object;

    /**
     * Constructor.
     * Creates the Loader.
     */
    public function __construct($path)
    {
        $this->_path = $path;
        preg_match('/([^\.]+)$/', $path, $name); // get the name of the class
        $this->_name = $name[0];
    }

    /**
     * CALL Listener.
     *
     * @param array $params
     *
     * @return returns the function result, or FALSE on error
     */
    public function __call($method, $params)
    {
        if (!$this->_object) {
            $name = $this->_name;
            Prado::using($this->_path);
            $this->_object = new $name();
        }

        return call_user_func_array([$this->_object, $method], $params);
    }
}
