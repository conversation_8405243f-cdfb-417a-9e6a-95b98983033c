<?php

namespace TF\Engine\Kernel;

use Prado\Web\UI\TClientScriptManager;
use Prado\Web\UI\TPage;

class MTClientScriptManager extends TClientScriptManager
{
    /**
     * OVERWITE THE CONSTRUCTOR IN ORDER TO SET ClientSupportsJavaScript TO FALSE.
     *
     * @param TPage $owner page that owns this client script manager
     */
    public function __construct(TPage $owner)
    {
        $_page = $owner;
        $_page->setClientSupportsJavaScript(false);

        parent::__construct($_page);
    }

    /**
     * OVERWRITE THE ORIGINAL METHOD IN ORDER TO NOT REGISTER THE PRADO SCRIPTS.
     */
    public function registerPradoScript($name) {}

    /**
     * OVERWRITE THE ORIGINAL METHOD IN ORDER TO NOT REGISTER THE PRADO SCRIPTS.
     */
    public function registerPradoScriptInternal($name) {}
}
