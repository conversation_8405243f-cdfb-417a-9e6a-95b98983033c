<?php

namespace TF\Engine\Kernel\PolygonFromMultipleLayers;

use <PERSON>rado\TModule;
use TF\Application\Common\MTUser;

abstract class AbstractPolygonFromLayers extends TModule
{
    protected ?MTUser $user = null;
    protected string $tableName;
    protected int $groupId;
    protected string $geometry;
    protected array $filters = [];

    public function init($config) {}

    public function setUser(MTUser $user)
    {
        $this->user = $user;
    }

    public function setTableName(string $tableName)
    {
        $this->tableName = $tableName;
    }

    public function setGroupId(int $groupId)
    {
        $this->groupId = $groupId;
    }

    public function setGeometry(string $geometry)
    {
        $this->geometry = $geometry;
    }

    public function setFilters(array $filters)
    {
        $this->filters = $filters;
    }

    abstract public function getGeoJson(): array;
}
