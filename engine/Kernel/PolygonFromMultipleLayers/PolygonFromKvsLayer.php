<?php

namespace TF\Engine\Kernel\PolygonFromMultipleLayers;

use TF\Engine\APIClasses\Plots\PlotsTree;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

class PolygonFromKvsLayer extends AbstractPolygonFromLayers
{
    public function getGeoJson(): array
    {
        $UserDbController = new UserDbController($this->User->Database);
        $kcUid = isset($this->User->KeyCloakUID) ? "'{$this->User->KeyCloakUID}'" : 'NULL';

        $query = "SELECT
            ST_AsGeoJSON (r.*)::json as geo_json
        FROM
           (
            SELECT
                kvs.gid,
                kvs.geom,
                uccps.contracts_count_by_status AS number_of_contracts,
                kvs.kad_ident,
                kvs.ekate,
                kvs.mestnost, 
                kvs.category,
                kvs.virtual_category_title,
                kvs.virtual_ntp_title,
                kvs.virtual_ekatte_name,
                kvs.document_area,
                kvs.allowable_area,
                round((case when allowable_area is not null and allowable_area > 0 then allowable_area / (ST_AREA(kvs.geom) / 1000) * 100 else 0 end)::numeric, 2) || ' %' as allow_prec,
                e.layer_type,
                e.layer_id,
                e.layer_label,
                e.layer_name,
                e.farm_name,
                e.fill_color,
                e.border_color,
                COALESCE(at.area_type, kvs.area_type) as area_type,
                string_agg(DISTINCT tkvs.owner_name || ' (' || tkvs.egn_subekt || ')', ', ') as owners_osz
            FROM
                {$this->tableName} AS kvs
            LEFT JOIN user_contracts_count_by_plot_and_status({$kcUid}) uccps ON (uccps.plot_id = kvs.gid)
            LEFT JOIN topic_layer_kvs_by_owner_name_label_items tkvs on(tkvs.gid = kvs.gid)
            LEFT JOIN dblink (
                    'host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "',
                    format($$ SELECT
                                at.id,
			                    at.title
                                FROM su_area_types at $$)
            ) AS at (
                area_type_id int,
				area_type varchar
            ) ON
                kvs.area_type = at.area_type_id::text
            LEFT JOIN dblink (
                    'host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "',
                    format($$ SELECT
                                sul.layer_type,
                                sul.id AS layer_id,
                                sul.name AS layer_label,
                                sul.table_name as layer_name,
                                suf.name AS farm_name,
                                 CASE
                                    WHEN NOT border_only THEN  
                                    '#' || coalesce(JSON_EXTRACT_PATH(sul.\"style\" , '%1\$s' , 'color')::text, sul.style->>'color', sul.color)
                                END AS fill_color,
                                '#' || coalesce(JSON_EXTRACT_PATH(sul.\"style\" , '%1\$s' , 'border_color')::text, sul.style->>'border_color', sul.border_color)
                                FROM su_users_layers sul
                            LEFT JOIN su_users_farming suf ON suf.id = sul.farming
                            WHERE
                                table_name = '{$this->tableName}'
                                AND sul.group_id = {$this->groupId} $$,
                        'ekate')
            ) AS e (
                layer_type varchar,
                layer_id int,
                layer_label varchar,
                layer_name varchar,
                farm_name varchar,
                fill_color varchar,
                border_color varchar
            ) ON true
            WHERE
                st_intersects(st_setsrid(ST_GeomFromGeoJSON('{$this->geometry}'), 32635), kvs.geom)";

        if (isset($this->filters['plot_statuses'])) {
            $plotStatusesFilter = PlotsTree::getPlotStatusesFilter($this->filters['plot_statuses']);

            if (count($plotStatusesFilter) > 0) {
                $query = $UserDbController->DbHandler->createWhereOrSQL($query, $plotStatusesFilter, true);
            }
        }

        $query .= 'GROUP BY
                    kvs.gid,
                    e.layer_type,
                    e.layer_id,
                    e.layer_label,
                    e.layer_name,
                    e.farm_name,
                    e.fill_color,
                    e.border_color,
                    at.area_type,
                    uccps.contracts_count_by_status
            ) as r';

        return $UserDbController->getDataByQuery($query);
    }
}
