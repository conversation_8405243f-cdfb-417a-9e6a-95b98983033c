<?php

namespace TF\Engine\Kernel\PolygonFromMultipleLayers;

use Prado\Prado;
use Prado\TModule;

class PolygonFromLayersFactory extends TModule
{
    public function init($config) {}

    public function getClass($tableName, $groupId, $geometry, $filters = []): ?TModule
    {
        $systemLayer = array_search($tableName, $GLOBALS['Layers']['remoteTables']);
        $id = $systemLayer ? 'systemLayers' : ('layer_kvs' === $tableName ? 'layerKVS' : 'userLayers');
        if (array_key_exists($tableName, $GLOBALS['Layers']['customLayers'])) {
            $tableName = $GLOBALS['Layers']['customLayers'][$tableName]['table_name'];
        }
        $polygonFromLayerClass = Prado::getApplication()->getModule($id);
        $polygonFromLayerClass->setUser(Prado::getApplication()->getUser());
        $polygonFromLayerClass->setTableName($tableName);
        $polygonFromLayerClass->setGroupId($groupId);
        $polygonFromLayerClass->setGeometry($geometry);
        $polygonFromLayerClass->setFilters($filters);

        return $polygonFromLayerClass;
    }
}
