<?php

namespace TF\Engine\Kernel\PolygonFromMultipleLayers;

use TF\Engine\Plugins\Core\Layers\LayersController;

class PolygonFromSystemLayers extends AbstractPolygonFromLayers
{
    public function getGeoJson(): array
    {
        $LayersController = new LayersController('Layers');

        $query = "SELECT ST_AsGeoJSON(r.*) AS geo_json 
        FROM (
            SELECT 
                t.*,
                sul.name AS layer_label, 
                sul.table_name AS layer_name, 
                sul.id AS layer_id,
                format('#%s', sul.style->>'color') AS fill_color,
                format('#%s', sul.style->>'border_color') AS border_color
        FROM
           {$this->tableName} AS t
           left join su_users_layers sul on sul.user_id={$this->groupId} AND sul.table_name='{$this->tableName}'
        WHERE
            st_intersects(st_setsrid(ST_GeomFromGeoJSON('{$this->geometry}'), 32635), t.geom)
            ) r
            ";

        return $LayersController->getDataByQuery($query);
    }
}
