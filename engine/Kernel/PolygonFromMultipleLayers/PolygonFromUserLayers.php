<?php

namespace TF\Engine\Kernel\PolygonFromMultipleLayers;

use TF\Application\Common\Config;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

class PolygonFromUserLayers extends AbstractPolygonFromLayers
{
    public function getGeoJson(): array
    {
        $UserDbController = new UserDbController($this->User->Database);
        $isakLayer = Config::LAYER_TYPE_ISAK;
        $forIsakLayer = Config::LAYER_TYPE_FOR_ISAK;

        $query = "SELECT
            ST_AsGeoJSON (r.*)::json as geo_json
        FROM
           (
            SELECT
                e.*, 
                t.*,
            case when layer_type = '{$isakLayer}'  or  layer_type = '{$forIsakLayer}'
                then  round((ST_Area(geom) / 10000)::numeric, 3) 
                else  round((ST_Area(geom) / 1000)::numeric, 3)
                end AS area
            FROM
                {$this->tableName} AS t
            LEFT JOIN dblink (
                    'host=" . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "',
                    format($$ SELECT
                                sul.layer_type,
                                sul.id AS layer_id,
                                sul.name AS layer_label,
                                sul.table_name as layer_name,
                                suf.name AS farm_name,
                                sul.year AS farm_year,
                                CASE
                                    WHEN NOT border_only THEN  
                                    '#' || coalesce(JSON_EXTRACT_PATH(sul.\"style\" , '%1\$s' , 'color')::text, sul.style->>'color', sul.color)
                                END AS fill_color,
                                '#' || coalesce(JSON_EXTRACT_PATH(sul.\"style\" , '%1\$s' , 'border_color')::text, sul.style->>'border_color', sul.border_color)                             
                                FROM su_users_layers sul
                            LEFT JOIN su_users_farming suf ON suf.id = sul.farming
                            WHERE
                                table_name = '{$this->tableName}'
                                AND sul.group_id = {$this->groupId} $$,
                         '')
            ) AS e (
                layer_type varchar,
                layer_id int,
                layer_label varchar,
                layer_name varchar,
                farm_name varchar,
                farm_year int,
                fill_color varchar,
                border_color varchar
            ) ON true
            WHERE
                st_intersects(st_setsrid(ST_GeomFromGeoJSON('{$this->geometry}'), 32635), t.geom)
            ) as r";

        return $UserDbController->getDataByQuery($query);
    }
}
