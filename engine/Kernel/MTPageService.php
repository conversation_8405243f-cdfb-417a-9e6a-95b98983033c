<?php

namespace TF\Engine\Kernel;

use Prado\Web\Services\TPageService;

class MTPageService extends TPageService
{
    /**
     * OVERWRITE THE ORIGINAL init METHOD IN ORDER TO SET OUR CUSTOM CLIENT SCRIPT MANAGER (MTClientScriptManager).
     */
    public function init($config)
    {
        $this->setClientScriptManagerClass('TF\Engine\Kernel\MTClientScriptManager');
        parent::init($config);
    }
}
