<?php

namespace TF\Engine\Kernel;

/**
 * Kernel File class file.
 *
 * <AUTHOR>
 */

/**
 * Kernel String class.
 *
 * Implements functionlity for manipulation of arrays
 */
class ArrayHelper
{
    /**
     * The method returns empty array if the passed array has only one unique element and it's an empty string.
     *
     * @param array $in
     *
     * @return array
     */
    public function filterEmptyStringArr($in)
    {
        if (is_array($in) && 1 === count($in) && '' === $in[0]) {
            return [];
        }

        return $in;
    }

    /**
     * Casts all elements of an array to strings.
     *
     * @param array $array
     *
     * @return array
     */
    public function mapArrayElToStr($array)
    {
        if (!is_array($array)) {
            return [];
        }

        return array_map(function ($item) {
            return "'" . $item . "'";
        }, $array);
    }

    /**
     * Sorts grid formatted array.
     *
     * @param string $order
     *
     * @return array
     */
    public function sortResultArray(array $array, $sortBy, $order)
    {
        $field = [];

        foreach ($array as $key => $row) {
            $field[$key] = $row[$sortBy];
        }

        switch ($order) {
            case 'asc':
                $orderType = SORT_ASC;

                break;
            case 'desc':
                $orderType = SORT_DESC;

                break;
            default:
                $orderType = SORT_ASC;

                break;
        }

        array_multisort($field, $orderType, $array);

        return $array;
    }

    public function compareWithOperand($a, $b, $op)
    {
        switch ($op) {
            case '=':
            case '===':
            case 'eq':
                return $a === $b;
            case '==':
                return $a == $b;
            case '!==':
            case 'neq':
                return $a !== $b;
            case '!=':
                return $a != $b;
            case '<':
            case 'lt':
                return $a < $b;
            case '<=':
            case 'lte':
                return $a <= $b;
            case '>':
            case 'gt':
                return $a > $b;
            case '>=':
            case 'gte':
                return $a >= $b;
            case 'in':
                return is_array($b) && in_array($a, $b);
            case 'nin':
                return is_array($b) && !in_array($a, $b);
            default:
                return;
        }
    }
}
