<?php

namespace TF\Engine\Kernel;

use PHPMailer;
use phpmailerException;

/**
 * Kernel Mail class file.
 *
 * <AUTHOR> Ltd. <<EMAIL>>
 *
 * @link http://www.devision.bg/
 *
 * @copyright Copyright &copy; 2008 Devision Ltd.
 */

/**
 * Kernel Mail class.
 *
 * Implements functionality for sending mails that is being used in the ATOM
 */
class Mail
{
    /**
     * Set mailer to use SMTP.
     *
     * @var bool $isSMTP
     */
    private $isSMTP = true;

    /** @var string $charSet */
    private $charSet = 'UTF-8';

    /**
     * Enable SMTP authentication.
     *
     * @var bool $SMTPAuth
     */
    private $SMTPAuth = true;

    /**
     * Enable TLS encryption, `ssl` also accepted.
     *
     * @var string $SMTPSecure
     */
    private $SMTPSecure = '';

    /** @var bool $debug */
    private $debug = true;

    /**
     * Set email format to HTML.
     *
     * @var bool $isHTML
     */
    private $isHTML = true;

    /** @var string $host */
    private $host = PHPMAILER_HOST;

    /** @var string $username */
    private $username = PHPMAILER_USERNAME;

    /** @var string $password */
    private $password = PHPMAILER_PASSWORD;

    /** @var int $port */
    private $port = PHPMAILER_PORT;

    /**
     * Uses PHPMailer and SMTP class in ..External/ .
     *
     * @param string $to
     * @param string $subject
     * @param string $body
     * @param string $fromName
     * @param string $fromMail
     * @param string $file
     *
     * @throws phpmailerException
     *
     * @return bool
     */
    public function sendMail($to, $subject, $body, $fromName = '', $fromMail = '', $file = '')
    {
        $mail = new PHPMailer();

        if ($this->isSMTP()) {
            $mail->isSMTP();
        }

        if ($this->isHTML()) {
            $mail->isHTML();
        }

        $mail->SMTPAuth = $this->isSMTPAuth();
        $mail->CharSet = $this->getCharSet();
        $mail->SMTPSecure = $this->getSMTPSecure();

        $mail->Host = $this->getHost();
        $mail->Port = $this->getPort();
        $mail->Username = $this->getUsername();
        $mail->Password = $this->getPassword();

        $mail->setFrom($this->getUsername(), $fromName);
        if ($fromMail) {
            $mail->setFrom($fromMail, $fromName);
        }
        $mail->addAddress($to);
        $mail->Subject = $subject;
        $mail->Body = $body;
        if ($file) {
            $mail->addAttachment($file);
        }

        if (!$mail->send()) {
            if ($this->isDebug()) {
                $mail->SMTPDebug = 3;
                echo "Check whether 'Less secure app access' in Enabled for:" . $this->getUsername();
                var_dump($mail->ErrorInfo);
            }

            return false;
        }

        return true;
    }

    /**
     * @return bool
     */
    public function isSMTP()
    {
        return $this->isSMTP;
    }

    /**
     * @param bool $isSMTP
     */
    public function setIsSMTP($isSMTP)
    {
        $this->isSMTP = $isSMTP;
    }

    /**
     * @return string
     */
    public function getCharSet()
    {
        return $this->charSet;
    }

    /**
     * @param string $charSet
     */
    public function setCharSet($charSet)
    {
        $this->charSet = $charSet;
    }

    /**
     * @return bool
     */
    public function isSMTPAuth()
    {
        return $this->SMTPAuth;
    }

    /**
     * @param bool $SMTPAuth
     */
    public function setSMTPAuth($SMTPAuth)
    {
        $this->SMTPAuth = $SMTPAuth;
    }

    /**
     * @return string
     */
    public function getSMTPSecure()
    {
        return $this->SMTPSecure;
    }

    /**
     * @param string $SMTPSecure
     */
    public function setSMTPSecure($SMTPSecure)
    {
        $this->SMTPSecure = $SMTPSecure;
    }

    /**
     * @return bool
     */
    public function isDebug()
    {
        return $this->debug;
    }

    /**
     * @param bool $debug
     */
    public function setDebug($debug)
    {
        $this->debug = $debug;
    }

    /**
     * @return bool
     */
    public function isHTML()
    {
        return $this->isHTML;
    }

    /**
     * @param bool $isHTML
     */
    public function setIsHTML($isHTML)
    {
        $this->isHTML = $isHTML;
    }

    /**
     * @return array|false|string
     */
    public function getHost()
    {
        return $this->host;
    }

    /**
     * @param array|false|string $host
     */
    public function setHost($host)
    {
        $this->host = $host;
    }

    /**
     * @return array|false|string
     */
    public function getUsername()
    {
        return $this->username;
    }

    /**
     * @param array|false|string $username
     */
    public function setUsername($username)
    {
        $this->username = $username;
    }

    /**
     * @return array|false|string
     */
    public function getPassword()
    {
        return $this->password;
    }

    /**
     * @param array|false|string $password
     */
    public function setPassword($password)
    {
        $this->password = $password;
    }

    /**
     * @return array|false|string
     */
    public function getPort()
    {
        return $this->port;
    }

    /**
     * @param array|false|string $port
     */
    public function setPort($port)
    {
        $this->port = $port;
    }
}
