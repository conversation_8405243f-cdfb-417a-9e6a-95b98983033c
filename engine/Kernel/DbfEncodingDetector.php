<?php

namespace TF\Engine\Kernel;

use Exception;
use <PERSON>rado\TModule;
use XBase\Table;

class DbfEncodingDetector extends TModule
{
    protected $encodingMapping = [
        '1251' => 'cp1251',
        'Windows-1251' => 'cp1251',
        'UTF-8' => 'UTF-8',
    ];

    public function init($config) {}

    /**
     * @throws Exception
     */
    public function detect($dbfPath)
    {
        $encoding = $this->getEncodingFromCpgFile($dbfPath);
        if ($encoding) {
            return $encoding;
        }

        $table = new Table($dbfPath);
        $cols = $table->getColumns();

        foreach ($cols as $col) {
            if ('C' != $col->getType()) {
                continue;
            }
            $stringCols[] = $col->getName();
        }

        while ($record = $table->nextRecord()) {
            foreach ($stringCols as $strCol) {
                $text = $record->getChar($strCol);
                $text = preg_replace('/[\.,_+\-]/', '', $text);
                if (!$text) {
                    continue;
                }
                if (is_numeric($text)) {
                    continue;
                }
                if (!iconv('CP1251', 'UTF-8', $text)) {
                    return 'UTF-8';
                }
                if (!mb_convert_encoding($text, 'UTF-8', 'CP1251')) {
                    return 'UTF-8';
                }
                if ($mbEnc = mb_detect_encoding($text, 'CP1251', true)) {
                    return $mbEnc;
                }
                if ($mbEnc = mb_detect_encoding($text, 'UTF-8', true)) {
                    return $mbEnc;
                }
            }
        }

        return 'CP1251';
    }

    private function getEncodingFromCpgFile($dbfPath)
    {
        $fileName = pathinfo($dbfPath, PATHINFO_FILENAME);
        $basePath = pathinfo($dbfPath, PATHINFO_DIRNAME);
        $cpgFile = "{$basePath}/{$fileName}.cpg";
        $encoding = '';
        if (file_exists($cpgFile)) {
            $encoding = file_get_contents($cpgFile);
        }
        if ($encoding) {
            return ($this->encodingMapping[$encoding]);
        }
    }
}
