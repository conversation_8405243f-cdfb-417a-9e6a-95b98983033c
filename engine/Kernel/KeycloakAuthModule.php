<?php

namespace TF\Engine\Kernel;

use Exception;
use GuzzleHttp\Client;
use League\OAuth2\Client\Provider\ResourceOwnerInterface;
use League\OAuth2\Client\Token\AccessToken;
use League\OAuth2\Client\Token\AccessTokenInterface;
use Prado\Prado;
use Prado\TModule;
use Prado\Web\THttpRequest;
use Prado\Web\THttpResponse;
use Stevenmaguire\OAuth2\Client\Provider\Keycloak;
use TF\Application\Common\Config;

class KeycloakAuthModule extends TModule
{
    public const PLATFORM_NAME = 'keycloak';

    private $provider;
    private $authManager;
    private $application;
    private $tokenExpiration;

    public function init($config)
    {
        $this->provider = new Keycloak([
            'authServerUrl' => getenv('KEYCLOAK_AUTH_SERVER_URL'),
            'realm' => getenv('KEYCLOAK_REALM'),
            'clientId' => getenv('KEYCLOAK_CLIENT_ID'),
            'clientSecret' => getenv('KEYCLOAK_CLIENT_SECRET'),
            'redirectUri' => getenv('KEYCLOAK_REDIRECT_URI'),
            'version' => '20.0.0',
        ]);
        $this->application = Prado::getApplication();
        $this->authManager = $this->application->getModule(Config::AUTH_MODULE);
    }

    /**
     * 1) Redirects user to external identity provider.
     *
     * 2) Validates provider state
     *
     * 3) Receive externaly issued tokens try to auth app user
     */
    public function initAuth(THttpRequest $request): void
    {
        if (!isset($request['code'])) {
            $_SESSION['oauth2state'] = $this->provider->getState();
            header('Location: ' . $this->provider->getAuthorizationUrl());
            exit;
        } elseif (empty($request['state']) || (isset($_SESSION['oauth2state']) && $request['state'] !== $_SESSION['oauth2state'])) {
            // Check given state against previously stored one to mitigate CSRF attack
            // It's possible to get that type of error if initKeycloakAuth is executed multiple times
            if (isset($_SESSION['oauth2state'])) {
                unset($_SESSION['oauth2state']);
            }

            throw new MTRpcException('INVALID_AUTH_STATE', -34051);
        } else {
            $this->authenticate($request);
        }
    }

    /**
     * Generate new access token.
     *
     * @throws MTRpcException
     */
    public function refreshAccessToken(string $refreshToken): AccessTokenInterface
    {
        try {
            $token = $this->provider->getAccessToken('refresh_token', [
                'refresh_token' => $refreshToken,
            ]);
        } catch (Exception $e) {
            throw $e;
        }

        return $token;
    }

    public function getKeycloakUser(AccessTokenInterface $accessToken): ResourceOwnerInterface
    {
        return $this->provider->getResourceOwner(
            $accessToken
        );
    }

    public function getProvider()
    {
        return $this->provider;
    }

    /**
     * @throws MTRpcException
     */
    public function backChannelLogout(string $logoutToken): void
    {
        try {
            $token = new KeycloakToken();

            if ($token->decode($logoutToken)) {
                $this->authManager->logout();

                $response = new THttpResponse();
                $response->redirect('index.php?page=Home');
            }
        } catch (Exception $ex) {
            throw new MTRpcException('FAILED_BACKCHANELL_LOGOUT', -34054);
        }
    }

    public function getLogoutUrl(): string
    {
        return $this->provider->getLogoutUrl(['access_token' => $this->User->getAccessToken()]);
    }

    public function logout(): void
    {
        $client = new Client([
            'base_uri' => getenv(KEYCLOAK_AUTH_SERVER_URL),
        ]);

        $cookies = $this->application->getRequest()->getCookies();

        $accessCookie = $cookies->findCookieByName($this->authManager->getUserKey() . '_kca');
        $refreshCookie = $cookies->findCookieByName($this->authManager->getUserKey() . '_kcr');

        try {
            $client->request('POST', $this->getBaseLogoutUrl(), [
                'headers' => [
                    'Authorization' => 'Bearer ' . $accessCookie->getValue(),
                    'Content-Type' => 'application/x-www-form-urlencoded',
                ],
                'on_headers' => function ($response) {
                    if ($response->getStatusCode() >= 400) {
                        throw new Exception($response->getReasonPhrase());
                    }
                },
                'form_params' => [
                    'client_id' => getenv(KEYCLOAK_CLIENT_ID),
                    'client_secret' => getenv(KEYCLOAK_CLIENT_SECRET),
                    'refresh_token' => $refreshCookie->getValue(),
                ],
            ]);

            $this->authManager->logout();
        } catch (\GuzzleHttp\Exception\RequestException $e) {
            if ($e->hasResponse()) {
                $response = $e->getResponse();

                throw new Exception($response->getReasonPhrase());
            }
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    /**
     * Get a new refresh token using an access token.
     *
     * @throws Exception
     *
     * @return array
     */
    public function exchangeToken(string $accessToken): AccessTokenInterface
    {
        try {
            $url = getenv('KEYCLOAK_AUTH_SERVER_URL') . '/realms/' . getenv('KEYCLOAK_REALM') . '/protocol/openid-connect/token';

            $response = $this->provider->getHttpClient()->post($url, [
                'form_params' => [
                    'grant_type' => 'urn:ietf:params:oauth:grant-type:token-exchange',
                    'subject_token' => $accessToken,
                    'audience' => getenv('KEYCLOAK_CLIENT_ID'),
                    'subject_token_type' => 'urn:ietf:params:oauth:token-type:access_token',
                    'client_id' => getenv('KEYCLOAK_CLIENT_ID'),
                    'client_secret' => getenv('KEYCLOAK_CLIENT_SECRET'),
                ],
            ]);

            $parsed = json_decode($response->getBody()->getContents(), true);

            return new AccessToken($parsed);
        } catch (Exception $ex) {
            throw new Exception('FAILED_TO_EXCHANGE_ACCESS_TOKEN', -34057);
        }
    }

    /**
     * Auth user by keycloak token.
     */
    private function authenticate(THttpRequest $request)
    {
        $token = $this->getToken($request['code']);

        // Optional: Now you have a token you can look up a users profile data
        try {
            // We got an access token, let's now get the user's details
            $user = $this->provider->getResourceOwner($token);

            $this->authManager->doLogin($user, $token);
        } catch (Exception $e) {
            throw new MTRpcException('FAILED_TO_GET_RESOURCE_OWNER', -34053);
        }
    }

    /**
     * @param string $code
     *
     * @throws MTRpcException
     */
    private function getToken($code): AccessTokenInterface
    {
        try {
            $token = $this->provider->getAccessToken('authorization_code', [
                'code' => $code,
            ]);
        } catch (Exception $e) {
            throw new MTRpcException('FAILED_TO_GET_ACCESS_TOKEN', -34052);
        }

        return $token;
    }

    /**
     * Keycloak auth url.
     */
    private function getBaseLogoutUrl(): string
    {
        return getenv(KEYCLOAK_AUTH_SERVER_URL) . '/realms/' . getenv(KEYCLOAK_REALM) . '/protocol/openid-connect/logout';
    }
}
