<?php

namespace TF\Engine\Kernel;

use Prado\Web\Services\TRpcException;

class MTRpcException extends TRpcException
{
    private $errorCodeNumber;
    private $errorMessageInfo;
    private $fieldName;

    public function __construct($msg = '', $code = '', $field = '')
    {
        $this->setErrorCodeNumber($code);
        $this->setCustomErrorMessage($msg);
        $this->setErrorFieldName($field);
        // base::__construct($msg);
    }

    public function getErrorCodeNumber()
    {
        return $this->errorCodeNumber;
    }

    public function getCustomErrorMessage()
    {
        return $this->errorMessageInfo;
    }

    public function getErrorFieldName()
    {
        return $this->fieldName;
    }

    private function setCustomErrorMessage($msg)
    {
        $this->errorMessageInfo = $msg;
    }

    private function setErrorCodeNumber($code)
    {
        $this->errorCodeNumber = $code;
    }

    private function setErrorFieldName($field)
    {
        $this->fieldName = $field;
    }
}
