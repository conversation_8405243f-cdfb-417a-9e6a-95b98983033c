<?php

namespace TF\Engine\Kernel;

/**
 * LoggerType File class file.
 *
 * <AUTHOR> Ltd. <<EMAIL>>
 *
 * @link http://www.devision.bg/
 *
 * @copyright Copyright &copy; 2008 Devision Ltd.
 */
/**
 * Logger types class.
 *
 * Contains constants that are used for logging events
 */
class LoggerType
{
    public const MESSAGE = 0;
    public const ITEM = 1;
    public const CATEGORY = 2;
    public const PICTURE = 3;
    public const VIDEO = 4;
    public const FILE = 5;
    public const ANALYSIS = 6;
    public const LEADING = 7;
    public const ANSWER = 8;
    public const SEASON = 9;
    public const RATING = 10;
    public const DIRECTORY = 11;
    public const OPTION = 12;
    public const OPTION_VALUE = 13;
    public const ORDER = 14;
    public const CUSTOM_FIELD = 15;
    public const PAYMENT = 16;
    public const DELIVERY = 17;
}
