<?php

namespace TF\Engine\Kernel;

use DateTime;
use Prado\TModule;
use ReflectionClass;
use TF\Application\Common\Config;

/**
 * Kernel File class file.
 *
 * <AUTHOR>
 */

/**
 * Kernel String class.
 *
 * Implements functionlity for validating user input fields
 */
class Validation extends TModule
{
    /**
     * The function checks if the field contains only digits, leading zeros are allowed.
     *
     * @param string $param contains the data from the field
     *
     * @return int is the parameter valid
     */
    public static function validateDigitsOnly($param)
    {
        if (ctype_digit($param) || '' == $param) {
            return Config::VALIDATION_VALID_FIELD;
        }

        return Config::VALIDATION_INVALID_DIGITS_ONLY;
    }

    /**
     * The function checks if the field contains a valid number, leading zeros are not allowed.
     *
     * @param string $param contains the data from the field
     *
     * @return int is the parameter valid
     */
    public static function validateNumber($param)
    {
        if ((is_numeric($param) && floatval($param) >= 0) || '' == $param) {
            return Config::VALIDATION_VALID_FIELD;
        }

        return Config::VALIDATION_INVALID_NUMBER;
    }

    /**
     * The function checks if the field contains any html-like sintaxis.
     *
     * @param string $param contains the data from the field
     *
     * @return int is the parameter valid
     */
    public static function validateText($param)
    {
        if (!preg_match('#(?<=<)\w+(?=[^<]*?>)#', $param)) {
            return Config::VALIDATION_VALID_FIELD;
        }

        return Config::VALIDATION_INVALID_TEXT;
    }

    /**
     * The function validates if the param is string.
     *
     * @param string $param contains the data from the field
     *
     * @return int is the parameter valid
     */
    public static function validateString($param)
    {
        if (is_string($param) || !isset($param)) {
            return Config::VALIDATION_VALID_FIELD;
        }

        return Config::VALIDATION_INVALID_TEXT;
    }

    /**
     * Checks for if the parameter is set and if it's not null.
     *
     * @param string $param parameter to be checked
     *
     * @return int is the parameter valid
     */
    public static function validateRequired($param)
    {
        if ('' === $param || null === $param) {
            return Config::VALIDATION_INVALID_REQUIRED;
        }

        return Config::VALIDATION_VALID_FIELD;
    }

    public static function validateNotNull($param)
    {
        if (null != $param) {
            return Config::VALIDATION_VALID_FIELD;
        }

        return Config::VALIDATION_INVALID_NOT_NULL;
    }

    /**
     * The function checks if the field contains valid integer.
     *
     * @param string $param contains the data from the field
     *
     * @return int is the parameter valid
     */
    public static function validateInteger($param)
    {
        if (is_int($param) || '' == $param || -1 == $param || '-1' == $param) {
            return Config::VALIDATION_VALID_FIELD;
        } elseif (is_string($param) && ctype_digit($param)) {
            $number = intval($param);
            if (is_int($number) && $number >= 0 && $number <= PHP_INT_MAX) {
                return Config::VALIDATION_VALID_FIELD;
            }
        }

        return Config::VALIDATION_INVALID_INTEGER;
    }

    /**
     * The function checks if the field is valid integer.
     *
     * @param string $param contains the data from the field
     *
     * @return int is the parameter valid
     */
    public static function validateStrictInteger($param)
    {
        if (is_int($param) || !isset($param)) {
            return Config::VALIDATION_VALID_FIELD;
        }

        return Config::VALIDATION_INVALID_INTEGER;
    }

    /**
     * The function checks if the field contains valid hexidecimal color.
     *
     * @param string $param contains the data from the field
     *
     * @return int is the parameter valid
     */
    public static function validateColor($param)
    {
        if (ctype_xdigit($param) || '' == $param) {
            return Config::VALIDATION_VALID_FIELD;
        }

        return Config::VALIDATION_INVALID_COLOR;
    }

    /**
     * The function checks if the field contains valid date/datetime.
     *
     * @param string $param contains the data from the field
     *
     * @return int is the parameter valid
     */
    public static function validateDate($param)
    {
        $delimiter = '';
        if ('' == $param) {
            return Config::VALIDATION_VALID_FIELD;
        }
        if (preg_match('/\./', $param)) {
            $delimiter = '.';
        }
        if (preg_match('/\-/', $param)) {
            $delimiter = '-';
        }
        if (preg_match('/\//', $param)) {
            $delimiter = '/';
        }

        $format1 = 'd' . $delimiter . 'm' . $delimiter . 'Y';
        $format2 = 'Y' . $delimiter . 'm' . $delimiter . 'd';
        $format3 = 'Y' . $delimiter . 'm' . $delimiter . 'd H:i';

        $d1 = DateTime::createFromFormat($format1, $param);
        if ($d1 && $d1->format($format1) == $param) {
            return Config::VALIDATION_VALID_FIELD;
        }
        $d2 = DateTime::createFromFormat($format2, $param);
        if ($d2 && $d2->format($format2) == $param) {
            return Config::VALIDATION_VALID_FIELD;
        }
        $d3 = DateTime::createFromFormat($format3, $param);
        if ($d3 && $d3->format($format3) == $param) {
            return Config::VALIDATION_VALID_FIELD;
        }

        return Config::VALIDATION_INVALID_DATE;
    }

    /**
     * The function checks if the field contains valid time format.
     *
     * @param string $param contains the data from the field
     *
     * @return int is the parameter valid
     */
    public static function validateTime($param)
    {
        if ('' == $param) {
            return Config::VALIDATION_VALID_FIELD;
        }
        if (1 === preg_match('^(([0-1][0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?)$^', $param)) {
            return Config::VALIDATION_VALID_FIELD;
        }

        return Config::VALIDATION_INVALID_TIMESTAMP;
    }

    /**
     * The method validates if the kad_ident field is constructed by the
     * three other fields: ekate, masiv, number separated by a dot.
     *
     * @return bool
     */
    public static function validateKadIdent(string $param)
    {
        if (!is_string($param)) {
            return Config::VALIDATION_INVALID_KAD_IDENT;
        }

        [$ekatte, $masiv, $number] = explode('.', $param);

        if (!ctype_digit($ekatte) || !ctype_digit($masiv) || !ctype_digit($number)) {
            return Config::VALIDATION_INVALID_KAD_IDENT;
        }

        return Config::VALIDATION_VALID_FIELD;
    }

    /**
     * The method validates if the selected farming year is
     * defined in the Global Config.
     *
     * @param int $params
     *
     * @return int
     */
    public static function validateFarmingYear($param)
    {
        if (isset($param)) {
            if (!array_key_exists($param, $GLOBALS['Farming']['years'])) {
                return Config::VALIDATION_INVALID_FARMING_YEAR;
            }
        }

        return Config::VALIDATION_VALID_FIELD;
    }

    /**
     * The method validates if the selected contract type is
     * defined in the Global Config.
     *
     * @param int $params
     *
     * @return int
     */
    public static function validateContractType($param)
    {
        if (isset($param)) {
            if (array_key_exists($param, $GLOBALS['Contracts']['ContractTypes'])) {
                return Config::VALIDATION_INVALID_CONTRACT_TYPE;
            }
        }

        return Config::VALIDATION_VALID_FIELD;
    }

    /**
     * Validates an array, whose elements are all from the same type.
     *
     * @param array $data - the actuall data array to be checked
     * @param string $valueType - the validation type. The name must be
     *                          exactly the same as the validation method
     *                          which will be used.
     *                          Example: to validate Integer array the
     *                          $valueType parameter must be
     *                          validateInteger -> the same as the static method
     */
    public static function validateTypedArray($data, $valueType, $arrayName)
    {
        if (null == $data) {
            return Config::VALIDATION_VALID_FIELD;
        }
        if (self::isAssoc($data) && !empty($data)) {
            throw new MTRpcException('error_invalid_rpc_param_array_type_associative', Config::VALIDATION_INVALID_ARRAY_TYPE, $arrayName);
        }
        $error_code = self::getErrorCode($valueType);
        for ($i = 0; $i < count($data); $i++) {
            // $isFieldValid = call_user_func('Validation::'.$valueType,$data[$i]);
            $isFieldValid = self::$valueType($data[$i]);
            if (Config::VALIDATION_VALID_FIELD != $isFieldValid) {
                throw new MTRpcException('error_invalid_rpc_param_in_array_' . $arrayName, $error_code, 'array_element_' . $i);
            }
        }
    }

    public static function validateComplexArray($rules, $dataToValidate)
    {
        for ($i = 0; $i < count($dataToValidate); $i++) {
            self::validateArray($rules, $dataToValidate[$i]);
        }
    }

    /**
     * Validates if an array is associative.
     *
     * @param array $arr
     *
     * @return true if the $arr is associative array
     * @return false if the $arr is sequential array
     */
    public static function isAssoc($arr)
    {
        return array_keys($arr) !== range(0, count($arr) - 1);
    }

    /**
     * Returns the assigned error code, based on the validation rule.
     *
     * @param string $validationType - the required validation type
     *                               (should be equal to the corresponding static method:
     *                               for integer - validateInteger,
     *                               for digits - ValidateDigitsOnly etc.)
     *
     * @return int
     */
    public static function getErrorCode($validationType)
    {
        $type = (substr(strtoupper($validationType), 8));
        switch ($type) {
            case 'NOTNULL':
                $type = 'NOT_NULL';

                break;

            case 'KADIDENT':
                $type = 'KAD_IDENT';

                break;

            case 'DIGITSONLY':
                $type = 'DIGITS_ONLY';

                break;

            default:
                break;
        }
        $oClass = new ReflectionClass('TF\Application\Common\Config');

        $constants = $oClass->getConstants();

        $validation_constants = [];
        foreach ($constants as $key => $value) {
            if (0 === strpos($key, 'VALIDATION_INVALID_')) {
                $validation_constants[$key] = $value;
            }
        }

        return $validation_constants['VALIDATION_INVALID_' . $type];
    }

    /**
     * This method is invoked when an API call is made.
     *
     * @param array $rules the validator rules array, taken from the registerMethods() of each RPC API
     * @param array $data the data to be validated, taken from the javascript and the RPC Request it makes
     */
    public static function runValidation($rules, $dataToValidate)
    {
        $isFieldValid = false;
        $counter = 0;
        foreach ($rules as $key => $value) {
            if (1 == count($dataToValidate) && isset($dataToValidate[0])) {
                if ($counter > 0) {
                    break;
                }

                $validationData = $dataToValidate[0];
            } else {
                $validationData = $dataToValidate[$counter];
            }

            if (is_array($value)) {
                self::validateArray($value, $validationData);
            } else {
                self::validateSingleField($key, $value, $validationData);
            }
            $counter++;
        }
    }

    /**
     * The method validates an array of data with a matching array of validation rules.
     *
     * @param array $rules associated array of validation rules
     *
     *                                @item key - the key to be validated
     *                                @item value - comma separated validation rules
     *
     * @param array $dataToValidate associated array of data to be validated
     *
     *                                @item key - matching key to be validated
     *                                @item value - the value itself to be validated
     */
    public static function validateArray($rules, $dataToValidate)
    {
        // Loops through all validation rules to be validated
        foreach ($rules as $key => $value) {
            if ('validateComplexArray' == substr($key, 0, 20)) {
                $array_name = (substr($key, 21));
                self::validateComplexArray($value, $dataToValidate[$array_name]);

                break;
            }
            // Checks if the value is an array and the method self-invokes with the corresponding data
            if (is_array($value)) {
                self::validateArray($value, $dataToValidate[$key]);

                break;
            }

            // If the current value (rule) is a string then the script continues
            // by getting all the validation rules for the current field
            $currentRules = array_map('trim', explode(',', $value));

            // Loops through all rules for the current value
            foreach ($currentRules as $ruleKey => $ruleValue) {
                // Checks if the rule is for validating sequential array
                if ('Array' === substr($ruleValue, -5)) {
                    // Get the current value type to be validated
                    $currentArrayType = substr($ruleValue, 0, -5);
                    // Run the validation method
                    self::validateTypedArray($dataToValidate[$key], $currentArrayType, $key);

                    break;
                }

                // Check for valid data and throw an exception if the data is not valid
                $isFieldValid = call_user_func('TF\Engine\Kernel\Validation::' . $ruleValue, $dataToValidate[$key]);
                if (Config::VALIDATION_VALID_FIELD != $isFieldValid) {
                    throw new MTRpcException('error_invalid_rpc_param_' . $key, $isFieldValid, '(' . gettype($dataToValidate[$key]) . ') ' . $dataToValidate[$key]);
                }
            }
        }
    }

    /**
     * The method validates single field from the 'validators' array.
     *
     * @param string $field the field name to be validated. It matches the key inside the 'validation' array
     * @param string $rules comma separated validators, to test the value
     * @param mixed $dataToValidate the actual data to be validated against the $rules
     *
     * @throws MTRpcException
     */
    public static function validateSingleField($field, $rules, $dataToValidate)
    {
        // Set the current validation data to be validated
        $validationData = is_array($dataToValidate) ? $dataToValidate[$field] : $dataToValidate;
        // Get the current validation rules to validate the data with
        $currentRules = array_map('trim', explode(',', $rules));
        // loop through the rules
        foreach ($currentRules as $ruleKey => $ruleValue) {
            // Check if the validation is for sequential array
            if ('Array' === substr($ruleValue, -5)) {
                // Get the validation type for the array
                $currentArrayType = substr($ruleValue, 0, -5);
                self::validateTypedArray($validationData, $currentArrayType, $field);

                break;
            }
            // If is single field validation - run the validator

            // $isFieldValid = call_user_func('Validation::'.$ruleValue,$validationData);
            $isFieldValid = self::$ruleValue($validationData);
            if (Config::VALIDATION_VALID_FIELD != $isFieldValid) {
                throw new MTRpcException('error_invalid_rpc_param_' . $field, $isFieldValid, $field);
            }
        }
    }

    public static function validateOrder($param)
    {
        if (!isset($param) || in_array(strtoupper($param), ['ASC', 'DESC'])) {
            return Config::VALIDATION_VALID_FIELD;
        }

        return Config::VALIDATION_INVALID_INTEGER;
    }

    public static function validateSort($sortColumn)
    {
        if (!isset($sortColumn)) {
            return Config::VALIDATION_VALID_FIELD;
        }

        $filteredSortColumn = preg_replace('/[^a-zA-Z0-9_.]/', '', $sortColumn);

        if ($filteredSortColumn !== $sortColumn) {
            return Config::VALIDATION_INVALID_INTEGER;
        }

        return Config::VALIDATION_VALID_FIELD;
    }
}
