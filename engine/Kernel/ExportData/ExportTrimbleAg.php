<?php

namespace TF\Engine\Kernel\ExportData;

use Exception;
use Prado\Exceptions\TDbException;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

class ExportTrimbleAg extends ExportClass
{
    /**
     * @throws TDbException
     *
     * @return string
     */
    public function exportOneFile()
    {
        return $this->export();
    }

    /**
     * @throws TDbException
     *
     * @return string
     */
    public function exportSeparateFiles()
    {
        return $this->export('false');
    }

    /**
     * @param string $singleFile
     *
     * @throws TDbException
     *
     * @return string
     */
    private function export($singleFile = 'true')
    {
        $options = $this->getOptions('exportTrimbleAg');
        $userDbController = new UserDbController($this->User->database);
        $layerDbController = new LayersController('Layers');

        $layer = $this->layerType;
        $farmingYear = $GLOBALS['Farming']['years'][(new FarmingController('Farming'))->getCurrentFarmingYearID()]['year'];

        $dbController = $userDbController;
        if ($layer::$isRemote) {
            $dbController = $layerDbController;
        }

        $bindingParams = [];
        if ($layer::$hasCustomGeometry) {
            $bindingParams = [':geomData' => $this->data['geom']];
        }

        $result = $dbController->getGeoJSON($options, false, 0, $bindingParams);

        if (empty($result['geoJSON'])) {
            throw new Exception('Invalid data');
        }

        $arr = json_decode($result['geoJSON'], true);

        foreach ($arr['features'] as $key => &$feature) {
            $feature['properties'][$layer::$nameColumn] = $key + 1 . '_' . $feature['properties'][$layer::$nameColumn];
        }

        $json = json_encode($arr);

        $layerName = $layer::$title;
        $fileBaseName = $this->data['export_type'] . "_{$layerName}_" . date('d_m_Y_H_i_s');
        $jsonFileName = $fileBaseName . '.geojson';
        $jsonFullPath = $this->path . '/' . $jsonFileName;
        $zipName = $fileBaseName . '.zip';
        $zipFullPath = $this->path . $zipName;
        $zipExportPath = JD_EXPORT_PATH . $zipName;

        $jsonFile = fopen($jsonFullPath, 'w');
        fwrite($jsonFile, $json);
        fclose($jsonFile);
        $command = 'mono ' . JD_EXPORTER . ' export ' . $jsonFullPath . ' ' . $this->data['output_device'] . ' ' . $farmingYear . ' ' . $this->User->name . ' ' . $this->getFarmingName() . ' ' . $singleFile . ' ' . JD_EXPORT_PATH;
        exec($command, $out);
        exec("mv {$zipExportPath} {$zipFullPath}");

        return $zipFullPath;
    }
}
