<?php

namespace TF\Engine\Kernel\ExportData\LayerTypes;

use TF\Application\Common\Config;
use TF\Application\Entity\UserLayers;

class LayerZP extends AbstractLayerType
{
    public static $type = Config::LAYER_TYPE_ZP;

    public static function init(?UserLayers $layer)
    {
        parent::init($layer);

        self::$dbfColumns = [
            'default' => ['*'],
            'exportTrimble' => ["' ' as date", "' ' as time", "' ' as version", 'id as "ID"', "' ' as area", "' ' as perimeter",
                "' ' as swathsin", "' ' as dist1", "' ' as dist2", "' ' as prefweight", 'lg.geom',
                self::nameCase(),
            ],
            'exportTopcon' => ['id', 'isak_prc_uin as objname',
                self::nameCase(), 'ST_Transform(geom, 4326) as geom',
            ],
            'exportMueller' => ['id as index', "'' as area", "'' as Flaeche_Un",
                self::nameCase(), 'ST_Transform(geom, 4326) as geom',
            ],
            'exportGps' => ['id', 'ekatte', 'culture', 'obrabotki', 'dobivi', 'napoqvane', 'polivki', 'polzvatel', 'isak_prc_uin',
                self::nameCase(), 'geom',
            ],
            'exportJohnDeere' => [
                'id  AS "ID"',
                self::nameCase(),
            ],
            'exportTrimbleAg' => [
                'id  AS "ID"',
                self::nameCase(),
            ],
            'exportISAK' => ['id', 'ST_SimplifyPreserveTopology(geom,0.005) as geom'],
            'exportGpx' => [
                'ST_Boundary(geom) as geom',
                self::nameCase(),
            ],
        ];
    }
}
