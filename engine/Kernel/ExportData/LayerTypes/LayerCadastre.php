<?php

namespace TF\Engine\Kernel\ExportData\LayerTypes;

use TF\Application\Common\Config;
use TF\Application\Entity\UserLayers;

class LayerCadastre extends AbstractLayerType
{
    public static $title = 'Cadastre_parceli';
    public static $type = Config::LAYER_TYPE_CADASTRE;

    public static function init(?UserLayers $layer)
    {
        self::setNameColumn('name');
        self::setIdColumn('gid');
        self::$combineColumnsForName = 'kad_ident';

        self::$dbfColumns = [
            'default' => ['ST_SetSRID(geom, 32635) as geom'],
            'exportTrimble' => [" ' ' AS date", "' ' AS time", "' ' AS version", self::$idColumn . ' AS "ID"', "' ' AS area", "' ' AS perimeter",
                "' ' AS swathsin", "' ' AS dist1", "' ' AS dist2", "' ' AS prefweight",
                self::nameCase(),
                'ST_SetSRID(lg.geom, 32635) as geom',
            ],
            'exportTopcon' => [self::$idColumn . ' as id', "'' as objname",
                'ST_Transform(geom, 4326) as geom',
                self::nameCase(),
            ],
            'exportMueller' => [self::$idColumn . ' as index', "'' as area", "'' as Flaeche_Un",
                'ST_Transform(geom, 4326) as geom',
                self::nameCase(),
            ],
            'exportGps' => ['ST_SetSRID(geom, 32635) as geom'],
            'exportJohnDeere' => [
                self::$idColumn . ' AS "ID"',
                self::nameCase(),
            ],
            'exportTrimbleAg' => [
                self::$idColumn . ' AS "ID"',
                self::nameCase(),
            ],
            'exportISAK' => [self::$idColumn . ' AS gid', 'ST_SimplifyPreserveTopology(geom,0.005) as geom'],
            'exportGpx' => [
                self::nameCase(),
                'ST_Boundary(geom) as geom',
            ],
            'exportKML' => [
                'ST_AsKML(ST_GeometryN(ST_SetSRID(geom, 32635), generate_series(1, ST_NumGeometries(ST_SetSRID(geom, 32635)))),32635) as geom',
                'ST_AsKML(ST_Centroid(ST_SetSRID(geom, 32635))) as center',
                'ekatte', 'virtual_ekatte_name', self::nameCase(), 'area', 'category', 'virtual_category_title', 'mestnost', 'ntp', 'virtual_ntp_title',
            ],
            'exportKMLfarmTrack' => [
                'ST_AsKML(ST_GeometryN(ST_SetSRID(geom, 32635), generate_series(1, ST_NumGeometries(ST_SetSRID(geom, 32635)))),32635) as geom',
                'ekatte', 'virtual_ekatte_name', self::nameCase(), 'area', 'category', 'virtual_category_title', 'mestnost', 'ntp', 'virtual_ntp_title',
            ],
        ];
    }
}
