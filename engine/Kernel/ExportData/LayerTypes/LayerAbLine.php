<?php

namespace TF\Engine\Kernel\ExportData\LayerTypes;

use TF\Application\Entity\UserLayers;

class LayerAbLine extends AbstractLayerType
{
    public static $nameColumn = 'name';
    public static $idColumn = 'gid';
    public static $title = 'AbLine';
    public static $type; // No specific layer type constant for AbLine

    public static function init(?UserLayers $layer)
    {
        // Set the ID and name columns in parent class
        self::setIdColumn(self::$idColumn);
        self::setNameColumn(self::$nameColumn);

        self::$dbfColumns = [
            'default' => [
                'gid  AS "ID"',
                self::nameCase(),
            ],
        ];
    }
}
