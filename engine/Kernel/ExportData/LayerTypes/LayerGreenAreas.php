<?php

namespace TF\Engine\Kernel\ExportData\LayerTypes;

use TF\Application\Common\Config;
use TF\Application\Entity\UserLayers;

class LayerGreenAreas extends AbstractLayerType
{
    public static $isRemote = true;
    public static $type = Config::LAYER_TYPE_PERMANETELY_GREEN_AREAS;

    public static function init(?UserLayers $layer)
    {
        parent::init($layer);

        self::$dbfColumns = [
            'default' => ['*'],
            'exportTrimble' => [" ' ' as date", "' ' as time", "' ' as version", 'gid as "ID"', "' ' as area", "' ' as perimeter",
                "' ' as swathsin", "' ' as dist1", "' ' as dist2", "' ' as prefweight", 'lg.geom', self::nameCase(),
            ],
            'exportTopcon' => ['gid as id', 'imotcode as objname',
                self::nameCase(), 'ST_Transform(geom, 4326) as geom',
            ],
            'exportMueller' => ['gid as index', "'' as area", "'' as Flaeche_Un",
                self::nameCase(), 'ST_Transform(geom, 4326) as geom',
            ],
            'exportGps' => ['gid', 'layer', 'imoti_pml_', 'obl', 'oblast', 'obshtina', 'zeml', 'imekatte', self::nameCase(), 'l_uscode', 'l_tycode', 'zemcad', 'sharea', 'natura', 'area_spa',
                'part_spa', 'area_sci', 'part_sci', 'area_hab', 'part_hab', 'is_nopml', 'is_spa', 'is_sci', 'is_hab', 'pzp_imot', 'pzp_area', 'pzp_perc', 'geom',
            ],
            'exportJohnDeere' => [
                'gid  AS "ID"', self::nameCase(),
            ],
            'exportTrimbleAg' => [
                'gid  AS "ID"', self::nameCase(),
            ],
            'exportISAK' => ['gid', 'ST_SimplifyPreserveTopology(geom,0.005) as geom'],
            'exportGpx' => [
                'ST_Boundary(geom) as geom', self::nameCase(),
            ],
        ];
    }
}
