<?php

namespace TF\Engine\Kernel\ExportData\LayerTypes;

use TF\Application\Common\Config;
use TF\Application\Entity\UserLayers;

class LayerDsPrc extends AbstractLayerType
{
    public static $isRemote = true;
    public static $type = Config::LAYER_TYPE_DS_PRC;

    public static function init(?UserLayers $layer)
    {
        parent::init($layer);
        self::$combineColumnsForName = '(' . $layer->getDefinitionByCategory(Config::LAYER_COLUMN_CATEGORY_EKATTE)['col_name'] . " ||  '-'  || " . self::$nameColumn . ')';

        self::$dbfColumns = [
            'default' => ['*'],
            'exportTrimble' => [" ' ' as date", "' ' as time", "' ' as version", 'gid as "ID"', "' ' as area", "' ' as perimeter",
                "' ' as swathsin", "' ' as dist1", "' ' as dist2", "' ' as prefweight", 'lg.geom',
                self::nameCase(),
            ],
            'exportTopcon' => ['gid as id', "crop_code || '-' || crop_name as objname",
                self::nameCase(),
                'ST_Transform(geom, 4326) as geom'],
            'exportMueller' => ['gid as index', "'' as area", "'' as Flaeche_Un",
                self::nameCase(),
                'ST_Transform(geom, 4326) as geom'],
            'exportGps' => ['gid', 'crop_code', 'crop_name', 'is_pg', 'is_bio', 'virtual_area_dka', 'geom'],
            'exportJohnDeere' => [
                'gid  AS "ID"',
                self::nameCase(),
            ],
            'exportTrimbleAg' => [
                'gid  AS "ID"',
                self::nameCase(),
            ],
            'exportISAK' => ['gid', 'ST_SimplifyPreserveTopology(geom,0.005) as geom'],
            'exportGpx' => [
                'ST_Boundary(geom) as geom',
                self::nameCase(),
                'crop_code || ' - ' || crop_name as objname',
            ],
        ];
    }
}
