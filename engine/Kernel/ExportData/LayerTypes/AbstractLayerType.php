<?php

namespace TF\Engine\Kernel\ExportData\LayerTypes;

use TF\Application\Common\Config;
use TF\Application\Entity\UserLayers;

abstract class AbstractLayerType
{
    public static $hasCustomGeometry = false;
    public static $isRemote = false;
    public static $dbfColumns;
    public static $idColumn;
    public static $nameColumn;
    public static $combineColumnsForName;
    public static $title;
    public static $type;

    public static function init(?UserLayers $layer)
    {
        if (!$layer) {
            return;
        }

        self::setIdColumn(
            $layer->getDefinitionByCategory(Config::LAYER_COLUMN_CATEGORY_GID)['col_name']
        );

        self::setNameColumn(
            $layer->getDefinitionByCategory(Config::LAYER_COLUMN_CATEGORY_NAME)['col_name']
        );
    }

    public static function setNameColumn(?string $nameColumn)
    {
        self::$nameColumn = $nameColumn;
    }

    public static function setIdColumn(?string $idColumn)
    {
        self::$idColumn = $idColumn;
    }

    public static function nameCase()
    {
        return 'case
                    when ' . (self::$combineColumnsForName ?? self::$nameColumn) . ' is not null and ' . (self::$combineColumnsForName ?? self::$nameColumn) . ' <> \'\' 
                    then substring(replace_special_chars(cyr_to_lat(' . (self::$combineColumnsForName ?? self::$nameColumn) . ')) from 1 for 50)
                    else cast(' . self::$idColumn . ' as text)
                end as ' . self::$nameColumn;
    }
}
