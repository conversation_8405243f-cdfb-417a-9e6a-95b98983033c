<?php

namespace TF\Engine\Kernel\ExportData\LayerTypes;

use TF\Application\Common\Config;
use TF\Application\Entity\UserLayers;

class LayerGPS extends AbstractLayerType
{
    public static $type = Config::LAYER_TYPE_GPS;

    public static function init(?UserLayers $layer)
    {
        parent::init($layer);

        self::$dbfColumns = [
            'default' => ['*'],
            'exportTrimble' => [" ' ' AS date", "' ' AS time", "' ' AS version", 'gid AS "ID"', "' ' AS area", "' ' AS perimeter",
                "' ' AS swathsin", "' ' AS dist1", "' ' AS dist2", "' ' AS prefweight", 'lg.geom',
                self::nameCase(),
            ],
            'exportTopcon' => ['gid as id', "'' as objname",
                self::nameCase(), 'ST_Transform(geom, 4326) as geom',
            ],
            'exportMueller' => ['gid as index', "'' as area", "'' as Flaeche_Un",
                self::nameCase(), 'ST_Transform(geom, 4326) as geom',
            ],
            'exportGps' => ['*'],
            'exportJohnDeere' => [
                'gid  AS "ID"',
                self::nameCase(),
            ],
            'exportTrimbleAg' => [
                'gid  AS "ID"', self::nameCase(),
            ],
            'exportISAK' => ['gid', 'ST_SimplifyPreserveTopology(geom,0.005) as geom'],
            'exportGpx' => [
                'ST_Boundary(geom) as geom', self::nameCase(),
            ],
        ];
    }
}
