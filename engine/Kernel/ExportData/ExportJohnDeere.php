<?php

namespace TF\Engine\Kernel\ExportData;

use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * <AUTHOR> <<EMAIL>>
 *
 * @link http://www.technofarm.bg/
 *
 * @copyright Copyright &copy; 2018 TechnoFarm Ltd.
 */
class ExportJohnDeere extends ExportClass
{
    /**
     * init - prado init function.
     */
    public function init($config)
    {
        parent::init($config);
    }

    /**
     * @return string
     */
    public function exportOneFile()
    {
        return $this->export();
    }

    /**
     * @return string
     */
    public function exportSeparateFiles()
    {
        return $this->export('false');
    }

    /**
     * @param string $singleFile
     *
     * @return string
     */
    private function export($singleFile = 'true')
    {
        $options = $this->getOptions('exportJohnDeere');

        $userDbController = new UserDbController($this->User->database);
        $layerDbController = new LayersController('Layers');

        $layer = $this->layerType;
        $farmingYear = $GLOBALS['Farming']['years'][(new FarmingController('Farming'))->getCurrentFarmingYearID()]['year'];
        $layerName = $this->layerType::$title;
        $salt = date('d_m_Y_H_i_s');
        $fileBaseName = $this->data['export_type'] . '_' . "{$layerName}_{$salt}";
        $jsonFileName = $fileBaseName . '.geojson';
        $jsonFullPath = $this->path . $jsonFileName;
        $zipName = $fileBaseName . '.zip';
        $zipFullPath = $this->path . $zipName;
        $exportPath = JD_EXPORT_PATH . $salt;
        $zipExportPath = $exportPath . DIRECTORY_SEPARATOR . $zipName;
        $device = $this->data['output_device'];
        $username = $this->User->name;
        $farm = $this->getFarmingName();
        $adaptConverter = JD_EXPORTER;

        $dbController = $userDbController;
        if ($layer::$isRemote) {
            $dbController = $layerDbController;
        }

        $bindingParams = [];
        if ($layer::$hasCustomGeometry) {
            $bindingParams = [':geomData' => $this->data['geom']];
        }

        $result = $dbController->getGeoJSON($options, false, 0, $bindingParams);
        $json = $result['geoJSON'];

        $jsonFile = fopen($jsonFullPath, 'w');
        fwrite($jsonFile, $json);
        fclose($jsonFile);

        $command = "mono {$adaptConverter} export {$jsonFullPath} {$device} {$farmingYear} {$username} {$farm} {$singleFile} {$exportPath}";
        exec($command, $out);
        @rename($zipExportPath, $zipFullPath);
        exec("rm -r {$exportPath}");
        unlink($jsonFullPath);

        return $zipFullPath;
    }
}
