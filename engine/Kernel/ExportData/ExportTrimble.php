<?php

namespace TF\Engine\Kernel\ExportData;

use Exception;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * <AUTHOR> Zaimov <<EMAIL>>
 *
 * @link http://www.technofarm.bg/
 *
 * @copyright Copyright &copy; 2015 TechnoFarm Ltd.
 */
class ExportTrimble extends ExportClass
{
    public $limitSepareteFiles = 1000;

    protected $shpName = 'Boundary';

    /**
     * init - prado init function.
     */
    public function init($config)
    {
        parent::init($config);
    }

    /**
     * Exports items in one .shp file.
     *
     * @return string Path to generated .zip file
     */
    public function exportOneFile()
    {
        return $this->export(true, $this->data['include_line_feature'], $this->data['line_feature_offset']);
    }

    /**
     * Exports items in separate .shp files.
     *
     * @return string Path to generated .zip file
     */
    public function exportSeparateFiles()
    {
        return $this->export(false, $this->data['include_line_feature'], $this->data['line_feature_offset']);
    }

    private function export($singleFile = true, $includeLineFeature = false, $offset = 0)
    {
        $options = $this->getOptions('exportTrimble');
        $layer = $this->layerType;

        $userDbController = new UserDbController($this->User->database);
        $layerDbController = new LayersController('Layers');

        $dbController = $userDbController;
        if ($layer::$isRemote) {
            $dbController = $layerDbController;
        }

        $result = $dbController->getGeoJSON($options, $includeLineFeature, $offset);

        if (empty($result['geoJSON'])) {
            throw new Exception('Invalid data');
        }

        $arr = json_decode($result['geoJSON'], true);

        foreach ($arr['features'] as &$feature) {
            $feature['properties']['NAME'] = $feature['properties'][$layer::$nameColumn];
        }
        $json = json_encode($arr);

        $farming_name = $this->getFarmingName();

        $tmpFile = tempnam(sys_get_temp_dir(), 'guzzle-download');
        $handle = fopen($tmpFile, 'w');

        $client = new \GuzzleHttp\Client();

        try {
            $url = COMMON_SERVICES_API_URL . '/export/trimble';
            $res = $client->request('POST', $url, [
                'json' => [
                    'client_name' => $this->User->Name,
                    'layer_name' => $layer::$title,
                    'farming_name' => $farming_name,
                    'type' => $singleFile ? 'SINGLE' : 'SEPARATE',
                    'include_line_features' => $includeLineFeature ? 'true' : 'false',
                    'GeoJSON' => $json,
                ],
                'save_to' => $handle,
                'on_headers' => function ($response) {
                    if ($response->getStatusCode() >= 400) {
                        throw new \GuzzleHttp\Exception\RequestException('Error export', $response);
                    }
                },
            ]);
        } catch (\GuzzleHttp\Exception\RequestException $e) {
            if ($e->hasResponse()) {
                $response = $e->getResponse();
                if (413 == $response->getStatusCode()) {
                    throw new MTRpcException('REQUEST_ENTITY_TOO_LARGE', -33951);
                } elseif ($response->getStatusCode() >= 400) {
                    throw new MTRpcException('GENERIC_EXPORT_ERROR', -33952);
                }
            }
        } catch (Exception $exception) {
            throw new MTRpcException('GENERIC_EXPORT_ERROR', -33952);
        }

        fclose($handle);

        $fileBaseName = $this->data['export_type'] . "_{$layer::$title}_" . date('d_m_Y_H_i_s');
        $zipName = $this->path . $fileBaseName . '.zip';
        @rename($tmpFile, $zipName);

        return $zipName;
    }
}
