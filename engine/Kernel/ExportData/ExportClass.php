<?php

namespace TF\Engine\Kernel\ExportData;

use Exception;
use Prado\TModule;
use TF\Application\Entity\UserLayers;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use ZipArchive;

/**
 * <AUTHOR> <<EMAIL>>
 *
 * @link http://www.technofarm.bg/
 *
 * @copyright Copyright &copy; 2015 TechnoFarm Ltd.
 */
abstract class ExportClass extends TModule
{
    public $limitOneFile;
    public $limitSepareteFiles;
    protected $path;
    protected $tmpPath;
    protected $archive;
    protected $dbConfig;
    protected $User;
    protected $data;
    protected $layerType;
    protected $layer;
    protected $archivePath = '';
    protected $shpName = 'gps';
    protected $shpTargetEncoding = 'UTF-8';

    /**
     * init - prado init function.
     */
    public function init($config) {}

    /**
     * Sets layer property.
     *
     * @param string $layer Layer type class
     */
    public function setLayer($layerType, ?UserLayers $layer = null)
    {
        $this->layerType = $layerType;
        $this->layerType::init($layer);
        $this->layer = $layer;
    }

    public function setLayerTitle(?string $title)
    {
        if (!$this->layerType) {
            throw new Exception('Set layer first!');
        }

        if ($title && strlen($title)) {
            $this->layerType::$title = $title;
        }
    }

    /**
     * Sets dbConfig property.
     *
     * @param array $dbConfig {
     *
     * @item string host
     * @item string username
     * @item string password
     * @item string database
     * }
     */
    public function setDbConfig($dbConfig)
    {
        $this->dbConfig = $dbConfig;
    }

    /**
     * Sets User property.
     *
     * @param MTUser $user
     */
    public function setUser($user)
    {
        $this->User = $user;
    }

    /**
     * Sets data property.
     *
     * @param array $data {
     *
     * @item string host
     * }
     */
    public function setData($data)
    {
        $this->data = $data;
    }

    /**
     * Sets path property and create directory.
     *
     * @param string $path
     */
    public function setExportPath($path)
    {
        $this->path = $path;
        if (!file_exists($path)) {
            mkdir($path, 0777);
        }
        $this->setExportTmpPath($path);
    }

    /**
     * Sets temp path property and create directory.
     */
    public function setExportTmpPath($path)
    {
        $salt = substr(md5(time() . $this->User->UserID), 0, 6);
        $this->tmpPath = $path . $salt . '/';

        if (!file_exists($this->tmpPath)) {
            mkdir($this->tmpPath, 0777);
        }
    }

    /**
     * Exports items in one .shp file.
     *
     * @return string Path to generated .zip file
     */
    public function exportOneFile()
    {
        $this->befereExport();
        $layer = $this->layerType;
        $filename = $this->data['export_type'] . '_' . $layer::$title . '_' . date('d_m_Y_H_i_s');
        $this->createArchive($filename);
        $shpPath = $this->path . $filename . '/';

        if (!file_exists($shpPath)) {
            mkdir($shpPath, 0777);
        }

        $shpName = $this->shpName;

        $options = $this->getOptions();

        if ($this->limitOneFile) {
            $options['offset'] = 0;
            $options['limit'] = $this->limitOneFile;
        }

        $userLayer = UserLayers::getLayerById($this->data['layer_id']);

        if ($userLayer->hasPhysicalTable()) {
            $database = $this->dbConfig['database'];
            $UsersDbController = new UserDbController($database);
            $UsersDbController->removeGeometryCollection($this->data['layer_name']);
        }

        $query = $this->getQuery($options, false);

        $this->pgsql2shp($shpPath, $shpName, $query);

        $this->addFilesToArchive($shpPath, $this->archivePath, $shpName);

        $this->archive->close();

        $LayersController = new LayersController('Layers');
        $LayersController->File->removeFolder($shpPath);

        return $this->path . $filename . '.zip';
    }

    /**
     * Exports items in separate .shp files.
     *
     * @return string Path to generated .zip file
     */
    public function exportSeparateFiles()
    {
        $this->befereExport();

        $layer = $this->layerType;
        $filename = $this->data['export_type'] . '_' . $layer::$title . '_' . date('d_m_Y_H_i_s');
        $shpPath = $this->path . $this->data['layer_name'] . '/';

        $this->createArchive($filename);

        if (!file_exists($shpPath)) {
            mkdir($shpPath, 0777);
        }

        $options = $this->getOptions();

        if ($this->limitSepareteFiles) {
            $options['offset'] = 0;
            $options['limit'] = $this->limitSepareteFiles;
        }

        $queryOptions = $options;
        $queryOptions['return'] = $layer::$idColumn ? array_merge($queryOptions['return'], [$layer::$idColumn]) : $queryOptions['return'];

        $results = $this->executeQuery($queryOptions, false);

        $userLayer = UserLayers::getLayerById($this->data['layer_id']);
        if ($userLayer->hasPhysicalTable()) {
            $database = $this->dbConfig['database'];
            $UsersDbController = new UserDbController($database);
            $UsersDbController->removeGeometryCollection($options['tablename']);
        }

        $count = count($results);
        for ($i = 0; $i < $count; $i++) {
            $shpName = str_replace('/', '_', $results[$i][$layer::$nameColumn]);
            $options['where'] = [
                'id' => ['column' => $layer::$idColumn, 'compare' => '=', 'value' => $results[$i][$layer::$idColumn]],
            ];

            if ('exportMueller' == $this->data['export_type'] && strlen($shpName) <= 3) {
                // muller export filename should be longer than 3 symbols
                $shpName = $shpName . '_' . $results[$i][$layer::$idColumn];
            }

            $query = $this->getQuery($options, false);

            if ($layer::$hasCustomGeometry) {
                $query = str_replace(':geomData', "'{$this->data['geom']}'", $query);
                $shpName = (string)rand();
            }

            $this->pgsql2shp($shpPath, $shpName, $query);
            $this->addFilesToArchive($shpPath, $this->archivePath, $shpName);
        }

        $this->archive->close();

        $LayersController = new LayersController('Layers');
        $LayersController->File->removeFolder($shpPath);

        return $this->path . $filename . '.zip';
    }

    /**
     * Remove space, double and single quotes.
     *
     * @param string $string
     *
     * @return string
     */
    protected function customStrReplace($string)
    {
        $string = str_replace(' ', '_', $string);
        $string = str_replace('"', '', $string);

        return str_replace("'", '', $string);
    }

    /**
     * Executes pgsql2shp command.
     *
     * @param string $filePath
     * @param string $fileName
     * @param string $query
     */
    protected function pgsql2shp($filePath, $fileName, $query)
    {
        $db = $this->dbConfig;
        $file = $this->customStrReplace($filePath . $fileName) . '.shp';
        $lcoEncoding = '';
        if ($this->shpTargetEncoding) {
            $lcoEncoding = "-lco ENCODING={$this->shpTargetEncoding}";
        }

        $query = str_replace('"', '\"', $query);

        $command = getenv('OGR2OGR_PATH') . ' -f "ESRI Shapefile" ' . $file . ' PG:"host=' . $db['host'] . '
        user=' . $db['username'] . ' dbname=' . $db['database'] . ' password=' . $db['password'] . ' port=' . $db['port'] . '" -sql "' . $query . '" ' . $lcoEncoding . ' 2>&1';

        exec($command, $out);
    }

    /**
     * Executes ogr2ogr command.
     *
     * @param string $filePath
     * @param string $fileName
     */
    protected function ogr2ogr($filePath, $fileName)
    {
        exec(getenv('OGR2OGR_PATH') . ' -s_srs EPSG:32635 -t_srs EPSG:4326 -overwrite ' . $this->customStrReplace($filePath) . ' ' . $this->customStrReplace($filePath . $fileName) . '.shp', $output);
    }

    /**
     * Creates .zip file and opens it.
     *
     * @param string $name
     */
    protected function createArchive($name)
    {
        if (file_exists($this->path . $name . '.zip')) {
            @unlink($this->path . $name . '.zip');
        }

        $this->archive = new ZipArchive();
        $this->archive->open($this->path . $name . '.zip', ZipArchive::CREATE);
    }

    /**
     * Adds files to archive.
     *
     * @param string $filePath
     * @param string $archivePath Path to file in archive
     * @param string $shpName
     */
    protected function addFilesToArchive($filePath, $archivePath, $shpName, $centroid = '')
    {
        $this->archive->addFile($filePath . $shpName . '.shp', $archivePath . $shpName . '.shp');
        $this->archive->addFile($filePath . $shpName . '.dbf', $archivePath . $shpName . '.dbf');
        $this->archive->addFile($filePath . $shpName . '.shx', $archivePath . $shpName . '.shx');
        $this->archive->addFile($filePath . $shpName . '.prj', $archivePath . $shpName . '.prj');
    }

    /**
     * Creates query string.
     *
     * @param array $options
     * @param bool $counter
     *
     * @return string
     */
    protected function getQuery($options, $counter)
    {
        $UserDbController = new UserDbController($this->User->database);

        return $UserDbController->getItemsByParams($options, $counter, true);
    }

    /**
     * Executes query and returns result.
     *
     * @param array $options
     * @param bool $counter
     *
     * @return array
     */
    protected function executeQuery($options, $counter, $bindingParams = [])
    {
        $LayersController = new LayersController('Layers');
        $UserDbController = new UserDbController($this->User->database);
        $layer = $this->layerType;

        if (true == $layer::$isRemote) {
            return $LayersController->getRemoteLayerData($options, $counter, false);
        }

        return $UserDbController->getItemsByParams($options, $counter, false, $bindingParams);
    }

    /**
     * Generates options array.
     *
     * @param string $exportType
     * @param ?string $tableAlias
     *
     * @return array
     */
    protected function getOptions($exportType = 'default', ?string $tableAlias = null)
    {
        $layer = $this->layerType;

        $fromExpr = null;
        if (isset($this->data['from_values']) && strlen($this->data['from_values']) > 0) {
            $fromExpr = '(SELECT * FROM ' . $this->data['from_values'] . ') ' . ($tableAlias ?? '');
        } elseif ('layer_allowable_draft' !== $this->data['layer_name']) {
            $fromExpr = $this->data['layer_name'];
        } else {
            $fromExpr = $this->data['layer_type'];
        }

        if (!$fromExpr) {
            throw new Exception('Invalid from clause for export: ' . $this->data['layer_name']);
        }

        $options = [
            'tablename' => $fromExpr,
            'return' => $layer::$dbfColumns[$exportType],
        ];

        if ($this->layerType::$hasCustomGeometry) {
            unset($options['tablename']);
        }

        if (isset($this->data['id_array']) && count($this->data['id_array']) > 0) {
            $options['where']['id'] = [
                'column' => $layer::$idColumn,
                'compare' => 'IN',
                'value' => $this->data['id_array'],
            ];
        }

        return $options;
    }

    /**
     * Returns name for .shp file.
     *
     * @param array $row Record from database
     *
     * @return string
     */
    protected function getShpName($row)
    {
        return str_replace('/', '_', $row[$this->layerType::$idColumn]);
    }

    /**
     * It's called before export.
     */
    protected function befereExport() {}

    /**
     * Returns name of farming.
     *
     * @return string
     */
    protected function getFarmingName()
    {
        $LayersController = new LayersController('Layers');
        $layer = $this->layerType;
        $userLayer = UserLayers::getLayerById($this->data['layer_id']);

        if (true == $layer::$isRemote || !isset($this->data['layer_id']) || !$userLayer->hasPhysicalTable()) {
            return 'Osnovno_stopanstvo';
        }

        $farmingName = $LayersController->getFarmingNameByLayerId($this->data['layer_id']);
        $farmingName = $farmingName[0]['name'];
        if ('UTF-8' == mb_detect_encoding($farmingName)) {
            $farmingName = $LayersController->StringHelper->convertToLatin($farmingName);
        }

        $farmingName = str_replace(' ', '_', $farmingName);
        $farmingName = str_replace('"', '_', $farmingName);
        $farmingName = str_replace('\'', '_', $farmingName);
        $farmingName = str_replace('/', '_', $farmingName);
        $farmingName = str_replace('(', '_', $farmingName);
        $farmingName = str_replace(')', '_', $farmingName);

        return ($farmingName) ? $farmingName : 'Osnovno_stopanstvo';
    }
}
