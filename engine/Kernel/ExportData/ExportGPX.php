<?php

namespace TF\Engine\Kernel\ExportData;

class ExportGpx extends ExportClass
{
    /**
     * init - prado init function.
     */
    public function init($config)
    {
        parent::init($config);
    }

    /**
     * Exports items in one .shp file.
     *
     * @return string Path to generated .zip file
     */
    public function exportOneFile()
    {
        $time = date('d_m_Y_H_i_s');
        $layerName = $this->layerType::$title;
        $outFileName = $this->data['export_type'] . "_{$layerName}_{$time}.gpx";
        $this->ogr2ogr($this->path, $outFileName);

        return "{$this->path}{$outFileName}";
    }

    /**
     * Executes ogr2ogr command.
     *
     * @param string $filePath
     * @param string $fileName
     */
    protected function ogr2ogr($filePath, $fileName)
    {
        $options = $this->getOptions();
        $query = $this->getQuery($options, false);

        if ($this->layerType::$hasCustomGeometry) {
            $query = str_replace(':geomData', "'{$this->data['geom']}'", $query);
        }

        $ogr2ogr = OGR2OGR_PATH;
        $db = $this->dbConfig;
        $dbConnString = "PG:\"host={$db['host']} user={$db['username']} dbname={$db['database']} password={$db['password']} port={$db['port']}\"";

        $query = str_replace('"', '\"', $query);
        $cmd = "{$ogr2ogr} -f GPX {$filePath}{$fileName} -overwrite -s_srs \"EPSG:32635\" -t_srs \"EPSG:4326\" -nln tracks -nlt MULTILINESTRING -append -geomfield geom {$dbConnString} -sql \"{$query}\"";
        exec($cmd);
    }

    protected function getOptions($exportType = 'exportGpx', $tableAlias = 'l')
    {
        return parent::getOptions($exportType, $tableAlias);
    }
}
