<?php

namespace TF\Engine\Kernel\ExportData;

use TF\Engine\Plugins\Core\Layers\LayersController;
use ZipArchive;

/**
 * <AUTHOR> <<EMAIL>>
 *
 * @link http://www.technofarm.bg/
 *
 * @copyright Copyright &copy; 2015 TechnoFarm Ltd.
 */
class ExportGps extends ExportClass
{
    private $tmp_filePath;

    /**
     * init - prado init function.
     */
    public function init($config)
    {
        parent::init($config);
    }

    /**
     * Exports items in one .shp file.
     *
     * @return string Path to generated .zip file
     */
    public function exportOneFile()
    {
        $this->befereExport();

        $archiveName = $this->data['export_type'] . '_' . $this->layerType::$title . '_' . date('d_m_Y_H_i_s');
        $this->createArchive($archiveName);

        $shpPath = $this->path . $archiveName . '/';
        @mkdir($shpPath, 0777);

        $shpName = $this->shpName;

        $options = $this->getOptions();

        if ($this->limitOneFile) {
            $options['offset'] = 0;
            $options['limit'] = $this->limitOneFile;
        }

        $query = $this->getQuery($options, false);

        if ($this->layerType::$hasCustomGeometry) {
            $query = str_replace(':geomData', "'{$this->data['geom']}'", $query);
        }

        $this->pgsql2shp($shpPath, $shpName, $query);

        $this->ogr2ogr($shpPath, $shpName);

        $this->addFilesToArchive($shpPath, $this->archivePath, $shpName);

        $this->archive->close();

        $LayersController = new LayersController('Layers');
        $LayersController->File->removeFolder($shpPath);

        return $this->path . $archiveName . '.zip';
    }

    /**
     * Executes ogr2ogr command.
     *
     * @param string $filePath
     * @param string $fileName
     */
    protected function ogr2ogr($filePath, $fileName)
    {
        $this->tmp_filePath = $filePath . '/tmp/';
        if (!file_exists($this->tmp_filePath)) {
            mkdir($this->tmp_filePath, 0777);
        }

        exec(OGR2OGR_PATH . ' -s_srs EPSG:32635 -t_srs EPSG:4326 -overwrite ' . $this->customStrReplace($this->tmp_filePath) . ' ' . $this->customStrReplace($filePath . $fileName) . '.shp', $output);
    }

    /**
     * Creates .zip file and opens it.
     *
     * @param string $name
     */
    protected function createArchive($name)
    {
        if (file_exists($this->path . $name . '.zip')) {
            @unlink($this->path . $name . '.zip');
        }

        $this->archive = new ZipArchive();
        $this->archive->open($this->path . $name . '.zip', ZipArchive::CREATE);
    }

    /**
     * Adds files to archive.
     *
     * @param string $filePath
     * @param string $archivePath Path to file in archive
     * @param string $shpName
     */
    protected function addFilesToArchive($filePath, $archivePath, $shpName, $centroid = '')
    {
        $this->archive->addFile($this->tmp_filePath . $shpName . '.shp', $archivePath . $shpName . '.shp');
        $this->archive->addFile($this->tmp_filePath . $shpName . '.dbf', $archivePath . $shpName . '.dbf');
        $this->archive->addFile($this->tmp_filePath . $shpName . '.shx', $archivePath . $shpName . '.shx');
        $this->archive->addFile($this->tmp_filePath . $shpName . '.prj', $archivePath . $shpName . '.prj');
    }

    protected function getOptions($exportType = 'exportGps', $tableAlias = 'l')
    {
        return parent::getOptions($exportType, $tableAlias);
    }
}
