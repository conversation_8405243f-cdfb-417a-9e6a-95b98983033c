<?php

namespace TF\Engine\Kernel\ExportData;

use RuntimeException;
use TF\Application\Common\Config;
use TF\Application\Entity\UserLayers;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Kernel\StringHelper;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbAreaTypes\UserDbAreaTypesController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * <AUTHOR> Zaimov <<EMAIL>>
 *
 * @link http://www.technofarm.bg/
 *
 * @copyright Copyright &copy; 2015 TechnoFarm Ltd.
 */
class ExportKML extends ExportClass
{
    private $ekatteNames = [];
    private $kmlText = '';

    /**
     * init - prado init function.
     */
    public function init($config)
    {
        parent::init($config);
    }

    /**
     * Exports items in one .shp file.
     *
     * @throws MTRpcException
     *
     * @return string Path to generated .zip file
     */
    public function exportOneFile()
    {
        $this->befereExport();

        $UsersController = new UsersController('Users');
        $LayersController = new LayersController('Layers');
        $UserDbController = new UserDbController($this->User->Database);

        // get all ekate data
        $ekateData = $UsersController->getAllEkatteData();

        foreach ($ekateData as $ekatte) {
            $this->ekatteNames[$ekatte['ekatte_code']] = $ekatte['ekatte_name'];
        }

        $userLayer = UserLayers::getLayerById($this->data['layer_id']);

        if ($userLayer->hasPhysicalTable()) {
            $options = [
                'tablename' => $this->data['layer_name'] . ' t',
                'return' => ['distinct(ekatte)'],
            ];

            $options['where']['gid'] = [
                'column' => Config::LAYER_TYPE_ZP == $this->data['layer_type'] ? 'id' : 'gid',
                'compare' => 'IN',
                'value' => $this->data['selected_ids'],
            ];

            if (Config::LAYER_TYPE_KVS == $this->data['layer_type']) {
                $options['return'] = ['distinct(ekate) as ekatte'];
            }

            $plots = [];
            $finalEkates = [];

            if (in_array($this->data['layer_type'], [Config::LAYER_TYPE_GPS, Config::LAYER_TYPE_WORK_LAYER], true)) {
                $finalEkates[0] = [
                    'ekatte' => 0,
                    'fullname' => 'Парцели',
                    'plotColor' => 'ffe100',
                    'borderColor' => 'FF14F0FF',
                    'borderWith' => 2,
                    'plotFill' => 0,
                ];
            } else {
                $plots = $UserDbController->getItemsByParams($options, false, false);

                if (empty($plots)) {
                    throw new MTRpcException('EMPTY_EXPORT_FILE', -33062);
                }
            }

            $plotsStyleOptions = [
                'tablename' => 'su_users_layers',
                'return' => ['style', 'name'],
                'where' => [
                    'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'value' => $this->data['layer_type']],
                    'group_id' => ['column' => 'group_id', 'compare' => '=', 'value' => $this->User->GroupID],
                ],
                'limit' => 1,
                'offset' => 0,
            ];

            $plotStyle = $UsersController->getItemsByParams($plotsStyleOptions, false, false);

            if (!empty($plotStyle[0]['style'])) {
                $plotStyle = json_decode($plotStyle[0]['style'], true);
            }

            $finalResults = [];
            $options = $this->generateAdditionalSqlByLayerType($this->data['layer_type'], $options);

            foreach ($plots as $plot) {
                $finalEkates[$plot['ekatte']]['ekatte'] = $plot['ekatte'];
                $finalEkates[$plot['ekatte']]['fullname'] = empty($this->ekatteNames[$plot['ekatte']]) ? 'Парцели' : $this->ekatteNames[$plot['ekatte']] . ' (' . $plot['ekatte'] . ')';
                $finalEkates[$plot['ekatte']]['plotColor'] = dechex(100 - ($plotStyle[$plot['ekatte']]['transparency'] ?: 50)) . $this->transformRGBtoBGR($plotStyle[$plot['ekatte']]['color'] ?: 'ffe100');
                $finalEkates[$plot['ekatte']]['borderColor'] = 'FF14F0FF';
                $finalEkates[$plot['ekatte']]['borderWith'] = 2;
                $finalEkates[$plot['ekatte']]['plotFill'] = 0;
            }

            $plotResultsCount = $UserDbController->getItemsByParams($options, true, false);

            if ($plotResultsCount[0]['count'] > 10000) {
                throw new MTRpcException('TOO_MANY_EXPORTED_PLOTS', -33063);
            }

            $plotResults = $UserDbController->getItemsByParams($options, false, false);
        } else {
            $finalEkates = [];
            $options = $this->getOptions();
            $plotResults = $UserDbController->getItemsByParams($options, false, false);

            $ekattes = array_unique(array_column($plotResults, 'ekatte'));
            foreach ($ekattes as $ekatte) {
                $finalEkates[$ekatte] = [
                    'ekatte' => $ekatte,
                    'fullname' => empty($this->ekatteNames[$ekatte]) ? 'Парцели' : $this->ekatteNames[$ekatte] . ' (' . $ekatte . ')',
                    'plotColor' => 'ffe100',
                    'borderColor' => 'FF14F0FF',
                    'borderWith' => 2,
                    'plotFill' => 0,
                ];
            }
        }

        foreach ($plotResults as $plot) {
            $tmpPlot = [];
            $tmpPlot['name'] = $plot['name'];
            $tmpPlot['description'] = $this->generateDescriptionByLayerType($this->data['layer_type'], $plot);
            $tmpPlot['geom'] = $plot['geom'];
            $tmpPlot['center'] = $plot['center'];
            $tmpPlot['ekatte'] = $plot['ekatte'];
            $finalResults[$plot['ekatte']]['plots'][] = $tmpPlot;
        }

        $data['ekattes'] = $finalEkates;
        $data['plots'] = $finalResults;
        $data['name'] = $plotStyle[0]['name'] ?: 'Имоти';

        $this->kmlText = $LayersController->StringHelper->loadTemplate($GLOBALS['Templates'][47]['template'], $data);

        $fileName = $this->data['export_type'] . '_' . $this->layerType::$title;

        return $this->generateFile($fileName);
    }

    /**
     * Generates options array.
     *
     * @param string $exportType
     *
     * @return array
     */
    protected function getOptions($exportType = 'exportKML', $tableAlias = 'l')
    {
        return parent::getOptions($exportType, $tableAlias);
    }

    /**
     * @param string $fileName
     *
     * @throws MTRpcException
     *
     * @return string
     */
    protected function generateFile($fileName = '')
    {
        $folder = PUBLIC_UPLOAD_EXPORT . DIRECTORY_SEPARATOR . $this->User->UserID;

        if (!is_dir($folder) && !mkdir($folder, 7770) && !is_dir($folder)) {
            throw new RuntimeException(sprintf('Directory "%s" was not created', $folder));
        }

        $file = $folder . DIRECTORY_SEPARATOR . $fileName . date('d_m_Y_H_i_s') . '.kml';

        if (false === file_put_contents($file, $this->kmlText)) {
            throw new RuntimeException('Failed to write KML file');
        }

        return $file;
    }

    protected function generateAdditionalSqlByLayerType($layerType, $options)
    {
        switch ($layerType) {
            case Config::LAYER_TYPE_WORK_LAYER:
                $options['return'] = [
                    'geom' => 'st_askml(geom) as geom',
                    'center' => 'st_askml(st_centroid(geom)) as center',
                    'name' => 'substring(replace_special_chars(cyr_to_lat(name)) from 1 for 50) as name',
                    'ekatte' => '0 as ekatte',
                    'area' => 'st_area(geom)/1000 as area',
                ];

                break;
            case Config::LAYER_TYPE_GPS:
                $options['return'] = [
                    'geom' => 'st_askml(geom) as geom',
                    'center' => 'st_askml(st_centroid(geom)) as center',
                    'ekatte' => '0 as ekatte',
                    'name' => 'substring(replace_special_chars(cyr_to_lat(plot_name)) from 1 for 50) as name',
                    'area' => 'st_area(geom)/1000 as area',
                ];

                break;
            case Config::LAYER_TYPE_FOR_ISAK:
                $options['return'] = [
                    'geom' => 'st_askml(geom) as geom',
                    'center' => 'st_askml(st_centroid(geom)) as center',
                    'ekatte' => 'ekatte',
                    'name' => 'substring(replace_special_chars(cyr_to_lat(prc_name)) from 1 for 50) as name',
                    'area' => 'st_area(geom)/1000 as area',
                    'cropcode' => 'cropcode',
                    'crop_name' => 'virtual_crop_name as crop_name',
                ];

                break;
            case Config::LAYER_TYPE_ISAK:
                $options['return'] = [
                    'geom' => 'st_askml(geom) as geom',
                    'center' => 'st_askml(st_centroid(geom)) as center',
                    'ekatte' => 'ekatte',
                    'name' => 'substring(replace_special_chars(cyr_to_lat(prc_uin)) from 1 for 50) as name',
                    'area' => 'st_area(geom)/1000 as area',
                    'cropcode' => 'cropcode',
                    'crop_name' => 'virtual_crop_name as crop_name',
                ];

                break;
            case Config::LAYER_TYPE_KMS:
                $options['return'] = [
                    'geom' => 'st_askml(geom) as geom',
                    'center' => 'st_askml(st_centroid(geom)) as center',
                    'ekatte' => 'ekatte',
                    'name' => 'substring(replace_special_chars(cyr_to_lat(name)) from 1 for 50) as name',
                    'area' => 'st_area(geom)/1000 as area',
                    'cropcode' => 'crop_code as cropcode',
                    'crop_name' => 'virtual_crop_name as crop_name',
                ];

                break;
            case Config::LAYER_TYPE_ZP:
                $options['return'] = [
                    'geom' => 'st_askml(geom) as geom',
                    'center' => 'st_askml(st_centroid(geom)) as center',
                    'ekatte' => 'ekatte',
                    'name' => 'substring(replace_special_chars(cyr_to_lat(area_name)) from 1 for 50) as name',
                    'area' => 'st_area(geom)/1000 as area',
                    'cropcode' => 'culture as cropcode',
                    'crop_name' => 'virtual_crop_name as crop_name',
                ];

                break;
            case Config::LAYER_TYPE_KVS:
                $options['return'] = [
                    'geom' => 'st_askml(geom) as geom',
                    'center' => 'st_askml(st_centroid(geom)) as center',
                    'ekatte' => 'ekate as ekatte',
                    'name' => 'substring(replace_special_chars(cyr_to_lat(kad_ident)) from 1 for 50) as name',
                    'area' => 'st_area(geom)/1000 as area',
                    'mestnost' => 'mestnost',
                    'category' => 'category',
                    'area_type' => 'area_type',
                    'ntp_title' => 'virtual_ntp_title as ntp_title',
                    'allowable_area' => "ROUND((CASE WHEN allowable_area > 0 THEN allowable_area ELSE 0 END)::NUMERIC,3) || ' (' || ROUND((CASE WHEN allowable_area > 0 THEN (allowable_area/(st_area(geom) / 1000)) * 100 ELSE 0 END)::NUMERIC, 2) || '%)' as allowable_area",
                    'outside_allowable_area' => "round(((st_area(geom) / 1000) - (CASE WHEN allowable_area > 0 THEN allowable_area ELSE 0 END))::NUMERIC, 3) || ' (' || ROUND(((((st_area(geom) / 1000) - (CASE WHEN allowable_area > 0 THEN allowable_area ELSE 0 END)) / ((st_area(geom) / 1000)))*100)::NUMERIC,2) || '%)' as outside_allowable_area",
                    'owner' => 'osz.ime_subekt as owner',
                ];

                $options['leftjoin'] = [
                    'table' => 'su_osz_files_plots osz',
                    'condition' => ' ON (osz.kad_no = t.kad_ident)',
                ];

                break;
        }

        return $options;
    }

    /**
     * Помощен метод, който трансформира RGB в BGR, защото
     * за kml стандартът цветовата схема е
     * aabbggrr, където:
     *     аа - alfa channel
     *     bb - blue channel
     *     gg - green channel
     *     rr - red channel.
     *
     * @param string $rgb - hexidecimal цвят
     *
     * @return string - същият hexidecimal цвят, но обърнат в BGR
     */
    private function transformRGBtoBGR($rgb)
    {
        $r = substr($rgb, 0, 2);
        $g = substr($rgb, 2, 2);
        $b = substr($rgb, 4, 2);

        return $b . $g . $r;
    }

    private function generateDescriptionByLayerType($layerType, $data)
    {
        $StringHelper = new StringHelper();
        $description = '<b>Площ:</b> ' . number_format($data['area'], 3, '.', '') . ' дка; <br/>';
        if (in_array($layerType, [
            Config::LAYER_TYPE_FOR_ISAK,
            Config::LAYER_TYPE_ISAK,
            Config::LAYER_TYPE_KMS,
            Config::LAYER_TYPE_ZP,
        ], true)) {
            $description .= '<b>ЕКАТТЕ:</b> ' . $data['ekatte'] . '<br/>';
            $description .= '<b>Землище:</b> ' . $this->ekatteNames[$data['ekatte']] . '<br/>';
            $description .= '<b>Култура:</b> ' . $data['crop_name'] . '<br/>';
        } elseif (Config::LAYER_TYPE_CADASTRE === $layerType) {
            $UserDbAreaTypesController = new UserDbAreaTypesController($this->User->Database);
            $ntpTitle = $UserDbAreaTypesController->getNtpTitle($data['area_type']);

            $description .= '<b>ЕКАТТЕ:</b> ' . $data['ekatte'] . '<br/>';
            $description .= '<b>Землище:</b> ' . $this->ekatteNames[$data['ekatte']] . '<br/>';
            $description .= '<b>Категория:</b> ' . $StringHelper->romans_number($data['category']) . '<br/>';
            $description .= '<b>НТП:</b> ' . $data['virtual_ntp_title'] . '<br/>';
            $description .= '<b>Площ (дка) </b> ' . $data['area'] . '<br/>';
        } elseif (in_array($layerType, [Config::LAYER_TYPE_KVS])) {
            $description .= '<b>ЕКАТТЕ:</b> ' . $data['ekatte'] . '<br/>';
            $description .= '<b>Землище:</b> ' . $this->ekatteNames[$data['ekatte']] . '<br/>';
            $description .= '<b>Местност:</b> ' . $data['mestnost'] . '<br/>';
            $description .= '<b>Категория:</b> ' . $StringHelper->romans_number($data['category']) . '<br/>';
            $description .= '<b>НТП:</b> ' . $data['ntp_title'] . '<br/>';
            $description .= '<b>Площ (дка) в "Допустим слой - окончателен:</b> ' . $data['allowable_area'] . '<br/>';
            $description .= '<b>Площ (дка) извън "Допустим слой - окончателен:</b> ' . $data['outside_allowable_area'] . '<br/>';
            $description .= '<b>Собственик:</b> ' . $data['owner'] . '<br/>';
        }

        return $description;
    }
}
