<?php

namespace TF\Engine\Kernel\ExportData\SmartConvertExport;

use Exception;
use TF\Application\Entity\UserLayers;
use TF\Engine\Kernel\ExportData\LayerTypes\LayerAbLine;
use TF\Engine\Kernel\ExportData\LayerTypes\LayerZP;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

class AbLinesExport extends SmartConvertExport
{
    /**
     * @param bool $singleFile
     *
     * @throws Exception
     *
     * @return mixed|string
     */
    public function export($singleFile = true)
    {
        if (empty($this->data) || !is_array($this->data)) {
            throw new Exception('Wrong input data');
        }

        try {
            $this->generateFiles();
            $this->convertFiles($this->data['output_device'], $singleFile);

            return $this->zipFiles($this->path, 'AB_lines');
        } catch (Exception $exception) {
            throw new Exception('Ab lines Export failed');
        }
    }

    /**
     * @throws TDbException
     */
    public function generateFiles()
    {
        $layer = UserLayers::getLayerByTableName($this->data['tablename'], $this->User->GroupID);
        $userDbController = new UserDbController($this->User->database);
        $this->setLayer(LayerZP::class, $layer);

        $options = $this->getOptions('exportTrimbleAg');
        $farmName = $userDbController->StringHelper->transLitString($this->data['farm_name']);
        $userName = $userDbController->StringHelper->transLitString($this->getUser()->Fullname);
        $this->generateFieldsInfo($options, $farmName, $userName);

        $this->generateFieldBoundaries($options);

        $this->setLayer(LayerAbLine::class);
        $this->data['id_array'] = $this->data['ab_lines_id_array'];
        $options = $this->getOptions('default');
        $options['tablename'] = 'su_ab_lines';
        $options['type'] = self::GUIDANCE_PATTERN_ABLINE;

        $this->generateGuidance($options);
    }
}
