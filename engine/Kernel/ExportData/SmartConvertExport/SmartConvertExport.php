<?php

namespace TF\Engine\Kernel\ExportData\SmartConvertExport;

use TF\Engine\Kernel\ExportData\ExportClass;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * Class SmartConvertExport.
 */
abstract class SmartConvertExport extends ExportClass // implements SmartExportInterface
{
    public const GUIDANCE_PATTERN_ABLINE = 'AbLine';
    public const GUIDANCE_PATTERN_ABCURVE = 'AbCurve';
    public const GUIDANCE_PATTERN_SPIRAL = 'Spiral';
    protected $layerDbController;

    private $guidancePatternsMap = [
        0 => self::GUIDANCE_PATTERN_ABLINE,
        1 => self::GUIDANCE_PATTERN_ABCURVE,
        2 => self::GUIDANCE_PATTERN_SPIRAL,
    ];

    private $fileFormat = 'tf';

    public function __construct()
    {
        $this->layerDbController = new LayersController('Layers');
    }

    /**
     * init - prado init function.
     */
    public function init($config)
    {
        parent::init($config);
    }

    /**
     * @param $options
     *
     * @return string
     */
    public function exportOneFile()
    {
        return $this->export();
    }

    /**
     * @param $options
     *
     * @return string
     */
    public function exportSeparateFiles()
    {
        return $this->export(false);
    }

    /**
     * @param bool $singleFile
     */
    abstract public function export($singleFile = true);

    /**
     * @param array $options
     * @param string $farmName
     * @param string $userName
     *
     * @throws TDbException
     */
    protected function generateFieldsInfo(/* array */ $options, /* string */ $farmName, /* string */ $userName)
    {
        $userDbController = new UserDbController($this->User->database);
        $fieldsInfo = $userDbController->getAdaptTFPluginFieldsInfo($options, $farmName, $userName);
        $fieldsInfo['Fields'] = array_map(function ($field) use ($userDbController) {
            $field['NAME'] = $userDbController->StringHelper->transLitString($field['NAME']);

            return $field;
        }, $fieldsInfo['Fields']);

        $this->writeToFile('1TF_FieldInfo', $fieldsInfo);
    }

    /**
     * @throws TDbException
     */
    protected function generateFieldBoundaries(/* array */ $options)
    {
        $userDbController = new UserDbController($this->User->database);
        $fieldsBoundaries = $userDbController->getAdaptTFPluginFieldBoundaries($options);
        $fieldsBoundaries['FieldBoundaries'] = array_map(function ($boundary) use ($userDbController) {
            $boundary['Name'] = $userDbController->StringHelper->transLitString($boundary['Name']);

            return $boundary;
        }, $fieldsBoundaries['FieldBoundaries']);

        $this->writeToFile('2TF_FieldBoundaries', $fieldsBoundaries);
    }

    protected function generateGuidance(/* array */ $options)
    {
        $userDbController = new UserDbController($this->User->database);
        $guidance = $userDbController->getAdaptTFPluginGuidance($options);

        $this->writeToFile('3TF_Guidance', $guidance);
    }

    /**
     * @param string $device
     * @param bool $singleFile
     *
     * @throws MTRpcException
     */
    protected function convertFiles(/* string */ $device, /* bool */ $singleFile)
    {
        $all = '';
        if (true === $singleFile) {
            $all = '--all';
        }

        $command = 'mono ' . SMART_CONVERTER . " --from TF --to {$device} {$all} --input {$this->tmpPath} --output {$this->tmpPath}/output 2>&1";
        exec($command, $out);

        // die(print_r(array($command, $out)));
        $outText = implode("\n", $out);
        if (false !== strpos('Unhandled Exception', $outText)) {
            throw new MTRpcException($outText, 1);
        }
    }

    /**
     * @param string $zipPath
     * @param string $zipName
     *
     * @return string
     */
    protected function zipFiles(/* string */ $zipPath, /* string */ $zipName)
    {
        $salt = substr(md5(time() . $this->User->UserID), 0, 6);

        $fileBaseName = "{$zipName}_{$salt}";

        $zipName = $fileBaseName . '.zip';
        $zipFullPath = $zipPath . $zipName;

        $command = "rm {$this->tmpPath}*.tf";
        $command .= ";cd {$this->tmpPath}/output";
        $command .= ";rm {$this->tmpPath}/output/compress_directory.zip";
        exec($command, $out);

        zipRecursive($this->tmpPath . '/output', $zipFullPath);

        $command = "rm -r {$this->tmpPath} 2>&1 ";
        exec($command, $out);

        return $zipFullPath;
    }

    protected function writeToFile(/* string */ $fileBaseName, /* array */ $data)
    {
        $jsonFileName = $fileBaseName . '.' . $this->fileFormat;
        if (!file_exists($this->tmpPath)) {
            mkdir($this->tmpPath, 0755, true);
        }

        $jsonFullPath = $this->tmpPath . $jsonFileName;
        $jsonFile = fopen($jsonFullPath, 'w');

        fwrite($jsonFile, json_encode($data));
        fclose($jsonFile);
    }
}
