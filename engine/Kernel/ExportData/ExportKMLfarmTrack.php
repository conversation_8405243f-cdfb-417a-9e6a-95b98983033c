<?php

namespace TF\Engine\Kernel\ExportData;

/**
 * <AUTHOR> <<EMAIL>>
 *
 * @link http://www.technofarm.bg/
 *
 * @copyright Copyright &copy; 2015 TechnoFarm Ltd.
 */
class ExportKMLfarmTrack extends ExportKML
{
    /**
     * Generates options array.
     *
     * @param string $exportType
     *
     * @return array
     */
    protected function getOptions($exportType = 'exportKMLfarmTrack', $tableAlias = 'l')
    {
        return parent::getOptions($exportType, $tableAlias);
    }

    protected function generateAdditionalSqlByLayerType($layerType, $options)
    {
        $result = parent::generateAdditionalSqlByLayerType($layerType, $options);

        $result['return']['geom'] = 'ST_AsKML(ST_GeomFromText(st_astext(ST_GeometryN(geom, generate_series(1, ST_NumGeometries(geom)))),32635)) as geom';
        $result['return']['center'] = 'null as center';

        return $result;
    }
}
