<?php

namespace TF\Engine\Kernel\ExportData;

use Prado\Prado;
use Prado\TModule;
use TF\Application\Entity\UserLayers;
use TF\Engine\Kernel\StringHelper;

/**
 * <AUTHOR> <<EMAIL>>
 *
 * @link http://www.technofarm.bg/
 *
 * @copyright Copyright &copy; 2015 TechnoFarm Ltd.
 */
class LayersExportFactory extends TModule
{
    private $dbConfig = [
        'userDb' => [
            'host' => DEFAULT_DB_HOST,
            'port' => DEFAULT_DB_PORT,
            'username' => DEFAULT_DB_USERNAME,
            'password' => DEFAULT_DB_PASSWORD,
            'database' => '',
        ],
        'remote' => [
            'host' => DBLINK_HOST,
            'port' => DBLINK_PORT,
            'username' => DBLINK_USERNAME,
            'password' => DBLINK_PASSWORD,
            'database' => DBLINK_DATABASE,
        ],
    ];

    /**
     * init - prado init function.
     */
    public function init($config) {}

    /**
     * Returns export class with set properties.
     *
     * @param string $exportType
     * @param string $layerClass
     *
     * @return object
     */
    public function getExportClass($exportType, $layerClass, $layerId)
    {
        $string = new StringHelper();
        $layer = $layerId ? UserLayers::getLayerById($layerId) : null;
        $title = $layer && strlen($layer->name) ? $string->transLitString($layer->name) : null;

        $exportClass = Prado::getApplication()->getModule($exportType);
        $exportClass->setLayer($layerClass, $layer);
        $exportClass->setLayerTitle($title);
        $exportClass->setDbConfig($this->getDbConfig($layerClass));
        $exportClass->setUser(Prado::getApplication()->getUser());
        $exportClass->setExportPath(PUBLIC_UPLOAD_EXPORT . '/' . Prado::getApplication()->getUser()->userID . '/');

        return $exportClass;
    }

    /**
     * Gets proper db configuration for layer.
     *
     * @param string $layer Layer class name
     *
     * @return array
     */
    private function getDbConfig($layer)
    {
        if (true == $layer::$isRemote) {
            return $this->dbConfig['remote'];
        }

        $this->dbConfig['userDb']['database'] = Prado::getApplication()->getUser()->database;

        return $this->dbConfig['userDb'];
    }
}
