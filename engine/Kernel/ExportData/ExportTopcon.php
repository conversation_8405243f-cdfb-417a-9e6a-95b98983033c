<?php

namespace TF\Engine\Kernel\ExportData;

use TF\Application\Common\Config;
use TF\Engine\Plugins\Core\Layers\LayersController;

/**
 * <AUTHOR> Zaimov <<EMAIL>>
 *
 * @link http://www.technofarm.bg/
 *
 * @copyright Copyright &copy; 2015 TechnoFarm Ltd.
 */
class ExportTopcon extends ExportClass
{
    public $limitOneFile;
    public $limitSepareteFiles = 100;

    /**
     * init - prado init function.
     */
    public function init($config)
    {
        parent::init($config);
    }

    /**
     * Generates options array.
     *
     * @param string $exportType
     *
     * @return array
     */
    protected function getOptions($exportType = 'exportTopcon', $tableAlias = 'l')
    {
        return parent::getOptions($exportType, $tableAlias);
    }

    /**
     * It's called before export.
     */
    protected function befereExport()
    {
        $this->shpName = $this->layerType::$title;
    }

    /**
     * Returns name for .shp file.
     *
     * @param array $row Record from database
     *
     * @return string
     */
    protected function getShpName($row)
    {
        $shpName = $row['objname'];

        if (Config::LAYER_TYPE_ZP == $this->data['layer_type']) {
            $shpName = $row['name'];
        }

        if ('UTF-8' == mb_detect_encoding($shpName)) {
            $LayersController = new LayersController('Layers');
            $shpName = $LayersController->StringHelper->convertToLatin($shpName);
        }
        $shpName = str_replace('/', '_', $shpName);

        return ($shpName) ? (str_replace('.', '-', $shpName)) : $row['id'];
    }
}
