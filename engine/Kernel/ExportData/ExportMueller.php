<?php

namespace TF\Engine\Kernel\ExportData;

use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Layers\LayersController;

/**
 * <AUTHOR> Zaimov <<EMAIL>>
 *
 * @link http://www.technofarm.bg/
 *
 * @copyright Copyright &copy; 2015 TechnoFarm Ltd.
 */
class ExportMueller extends ExportClass
{
    public const EXPORT_FOLDER_NAV = 0;
    public const EXPORT_FOLDER_GIS = 1;
    public const EXPORT_FOLDER_SHP = 2;

    public static $exportFolderMap = [
        self::EXPORT_FOLDER_NAV => 'NavGuideGisImport',
        self::EXPORT_FOLDER_GIS => 'GIS',
        self::EXPORT_FOLDER_SHP => 'SHP',
    ];

    public $limitOneFile;
    public $limitSepareteFiles = 100;
    // Use ogr2ogr default encoding.
    protected $shpTargetEncoding;

    /**
     * init - prado init function.
     */
    public function init($config)
    {
        parent::init($config);
    }

    /**
     * Generates options array.
     *
     * @param string $exportType
     *
     * @return array
     */
    protected function getOptions($exportType = 'exportMueller', $tableAlias = 'l')
    {
        return parent::getOptions($exportType, $tableAlias);
    }

    /**
     * It's called before export.
     */
    protected function befereExport()
    {
        $this->shpName = $this->layerType::$title;

        if (!array_key_exists($this->data['export_structure_type'], self::$exportFolderMap)) {
            throw new MTRpcException('EXPORT_STRUCTURE_ERROR', -33953);
        }

        $this->archivePath = self::$exportFolderMap[$this->data['export_structure_type']] . '/';
    }

    /**
     * Returns name for .shp file.
     *
     * @param array $row Record from database
     *
     * @return string
     */
    protected function getShpName($row)
    {
        $shpName = $row['name'];

        if ('UTF-8' == mb_detect_encoding($shpName)) {
            $LayersController = new LayersController('Layers');
            $shpName = $LayersController->StringHelper->convertToLatin($shpName);
        }

        $shpName = str_replace(' ', '_', $shpName);
        $shpName = str_replace('/', '_', $shpName);

        return ($shpName) ? (str_replace('.', '-', $shpName)) : $row['index'];
    }
}
