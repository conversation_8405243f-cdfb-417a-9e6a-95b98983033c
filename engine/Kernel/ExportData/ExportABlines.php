<?php

namespace TF\Engine\Kernel\ExportData;

use Exception;
use Prado\Exceptions\TDbException;
use TF\Engine\Kernel\StringHelper;

class ExportABlines extends ExportClass
{
    /**
     * @param $options
     *
     * @throws TDbException
     *
     * @return string
     */
    public function exportOneFile()
    {
        return $this->export();
    }

    /**
     * @param $options
     *
     * @throws TDbException
     *
     * @return string
     */
    public function exportSeparateFiles()
    {
        return $this->export('false');
    }

    /**
     * @param string $singleFile
     *
     * @throws Exception
     *
     * @return string
     */
    private function export($singleFile = 'true')
    {
        $string = new StringHelper();

        if (empty($this->data) || !is_array($this->data)) {
            throw new Exception('Wrong input data');
        }
        $result = [];

        foreach ($this->data['plots'] as $key => $plot) {
            if (empty($plot['name'])) {
                throw new Exception('Wrong input data');
            }

            $result[$key]['id'] = (string)$plot['id'];
            $result[$key]['properties']['name'] = $string->transLitString($plot['name']);
            $result[$key]['geom'] = json_decode($plot['geom'], true);
            $result[$key]['ab_lines'] = [];

            foreach ($plot['children'] as $lineKey => $line) {
                $result[$key]['ab_lines'][$lineKey]['id'] = $line['id'];
                $result[$key]['ab_lines'][$lineKey]['properties']['name'] = $string->transLitString($line['name']);
                $result[$key]['ab_lines'][$lineKey]['properties']['offset'] = $line['offset'];
                $result[$key]['ab_lines'][$lineKey]['geom'] = json_decode($line['geom']);
            }
        }

        $layerName = 'AB_lines';
        $salt = substr(md5(time() . $this->User->UserID), 0, 6);
        $fileBaseName = "{$layerName}_{$salt}";
        $jsonFileName = $fileBaseName . '.geojson';
        $jsonFullPath = AB_LINES_EXPORT_PATH . $jsonFileName;

        $zipName = $fileBaseName . '.zip';
        $zipFullPath = $this->path . '/' . $zipName;
        $zipExportPath = AB_LINES_EXPORT_PATH . $zipName;
        $jsonFile = fopen($jsonFullPath, 'w');
        fwrite($jsonFile, json_encode($result));
        fclose($jsonFile);
        $tmpExportDir = AB_LINES_EXPORT_PATH . "abline_{$salt}";
        mkdir($tmpExportDir, 0777, true);

        $command = 'mono ' . AB_LINES_EXPORTER . ' import --input ' . $jsonFullPath . ' -p ' . $this->data['output_device'] . ' -s AbLinesToAdapt --output ' . $tmpExportDir . ' 2>&1';

        // die(print_r(array($command, $tmpExportDir, $zipFullPath)));
        exec($command, $out);

        zipRecursive($tmpExportDir, $zipFullPath);

        unlink($jsonFullPath);
        exec("rm -r {$tmpExportDir}");

        return $zipFullPath;
    }
}
