<?php

namespace TF\Engine\Kernel;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\BadResponseException;
use Prado\TModule;
use TF\Application\Common\KvsStoreAuth;
use TF\Application\Common\MTSystemAuthManager;

class KvsStoreModule extends TModule
{
    public const PLATFORM_NAME = 'kvsStoreModule';
    public const APPLICATION_JSON = ['accept' => 'application/json'];
    public const CALLBACK_URL = 'kvs-client-json=kvs-store';
    public const EKATTE_STATUS_CALLBACK_URL = 'kvs-client-json=kvs-status';

    private $httpClient;
    private $authProvider;

    public function init($config)
    {
        $this->httpClient = new Client([
            'base_uri' => KVS_STORE_URL,
        ]);

        $this->authProvider = new MTSystemAuthManager(
            new KvsStoreAuth()
        );
    }

    public function requestEkatte(string $ekatteCode): array
    {
        $response = $this->makeRequest(
            '/api/ekatte',
            [
                'ekatte' => $ekatteCode,
                'callback_url' => $this->buildUrl(self::CALLBACK_URL),
                'ekatte_status_callback_url' => $this->buildUrl(self::EKATTE_STATUS_CALLBACK_URL),
            ],
            'POST'
        );

        return json_decode($response, true);
    }

    public function getPlotAttributes(array $params): array
    {
        $response = $this->makeRequest(
            '/api/plot-attributes',
            $params,
            'GET'
        );

        return json_decode($response, true);
    }

    private function buildUrl(string $path): string
    {
        return KVS_STORE_CALLBACK_URL . $path;
    }

    private function makeRequest($url, $params = [], $method = 'POST')
    {
        try {
            $token = $this->authProvider->getToken();

            $options = [
                'headers' => [
                    'Authorization' => 'Bearer ' . $token->getToken(),
                ],
            ];

            if ('GET' === $method) {
                $options['query'] = $params;
            } else {
                $options['form_params'] = $params;
            }

            $response = $this->httpClient->request($method, $url, $options);

            return $response->getBody()->getContents();
        } catch (BadResponseException $exception) {
            $err = json_decode($exception->getResponse()->getBody()->getContents());

            $details = !empty($err->error->details) ? $err->error->details : '';
            $code = !empty($err->code) ? $err->code : 500;

            // Check for validation error and get the wrong field name
            if (-33011 === $code) {
                $detailsArr = explode(':', $details);
                $details = $detailsArr[0];
            }

            throw new MTRpcException(!empty($err->error->message) ? $err->error->message : '', $code, $details);
        }
    }
}
