<?php

namespace TF\Engine\Kernel;

use PDO;
use <PERSON>rado\Data\TDataSourceConfig;
use Prado\Data\TDbConnection;
use <PERSON>rado\Prado;
use Prado\TComponent;

/**
 * DbHandler class file.
 *
 * <AUTHOR> Ltd. <<EMAIL>>
 *
 * @link http://www.devision.bg/
 *
 * @copyright Copyright &copy; 2008 Devision Ltd.
 */

/**
 * DbHandler class.
 *
 * This is the main model class in ATOM. Every plugin's model is successor of this class.
 * Instntiates the DbModule variable that is used as an interface connection to the DB.
 * Implements common functions for creating sql queries that could be used in the successors.
 */
class Db<PERSON>andler extends TComponent
{
    private $_dbModule;
    private $_dbModule2;
    private $_dbPrefix;

    /**
     * Constructor.
     * Creates connection to dbModule with parameters defined in config.php file.
     */
    public function __construct()
    {
        $this->setDbModule(DEFAULT_DB_MODULE);
        $this->setDbModule2(REMOTE_DB_MODULE);
        $this->setDbPrefix(DEFAULT_DB_PREFIX);
    }

    /**
     * Returns the Database Module.
     *
     * @return TDbConnection
     */
    public function getDbModule()
    {
        $this->_dbModule->setAttribute(PDO::ATTR_STRINGIFY_FETCHES, false);

        return $this->_dbModule;
    }

    /**
     * Returns the Database Module to login3.
     *
     * @return TDbConnection
     */
    public function getDbModule2()
    {
        $this->_dbModule2->setAttribute(PDO::ATTR_STRINGIFY_FETCHES, false);

        return $this->_dbModule2;
    }

    /**
     * Sets the Database Module.
     *
     * @param string $id - the ID of the TDataSourceConfig in the application xml
     */
    public function setDbModule($id)
    {
        $module = Prado::getApplication()->getModule($id);
        if ($module instanceof TDataSourceConfig) {
            $this->_dbModule = $module->DbConnection;

            return true;
        }

        throw new \Exception('The Database Module should be an instance of TDataSourceConfig!');

        return false;
    }

    /**
     * Sets the Remote Database Module.
     *
     * @param string $id - the ID of the TDataSourceConfig in the application xml
     */
    public function setDbModule2($id)
    {
        $module = Prado::getApplication()->getModule($id);
        if ($module instanceof TDataSourceConfig) {
            $this->_dbModule2 = $module->DbConnection;

            return true;
        }

        throw new Exception('The Database Module should be an instance of TDataSourceConfig!');

        return false;
    }

    /**
     * Sets the database prefix.
     *
     * @param string $value
     */
    public function setDbPrefix($value)
    {
        $this->_dbPrefix = $value;
    }

    /**
     * Returns the database prefix.
     *
     * @return string
     */
    public function getDbPrefix()
    {
        return $this->_dbPrefix;
    }

    /**
     * COMMON FUNCTION USED FOR PREPARING QUERIES
     * ------------------------------------------------------------------------------------.
     */

    /**
     * prepareGetSQL query .
     *
     * @param string $table, name of the table, CAN NOT BE NULL
     * @param array $returnFields, fields name of records, which will be returned
     * @param array $fieldsTerm, field's in table, which values will be used in where clause
     *
     * @return string
     */
    public function prepareGetSQL($table, $returnFields, $fieldTerms, $valueTerms)
    {
        $sql = 'SELECT ' . implode(', ', $returnFields) . ' FROM ' . $table;

        $cntTerms = count($fieldTerms);

        if ($cntTerms) {
            $sql .= ' WHERE ';
        }

        if (is_array($fieldTerms)) {
            $whereSql = '';
            foreach ($fieldTerms as $key => $value) {
                if ($whereSql) {
                    $whereSql .= ' AND ';
                }
                if (null !== $valueTerms[$key]) {
                    $whereSql .= "{$value} = :field{$key}";
                } else {
                    $whereSql .= "{$value} IS NULL";
                }
            }
            $sql .= $whereSql;
        }

        return $sql;
    }

    /**
     * Prapares sql for insert.
     *
     * @param string $table, name of the table, CAN NOT BE NULL
     * @param array $fields, fields in table, which values will be inserted in, CAN NOT BE NULL
     *
     * @return string
     */
    public function prepareInsertSQL($table, $fields)
    {
        $countFields = count($fields);
        if (0 != $countFields) {
            $sql = 'INSERT INTO ' . $table . ' ("' . implode('", "', $fields) . '") VALUES (';
            for ($i = 0; $i < $countFields; $i++) {
                $sql .= ":field{$i}";
                if ($i != $countFields - 1) {
                    $sql .= ', ';
                }
            }

            $sql .= ')';

            return $sql;
        }

        return false;
    }

    /**
     * Prapares sql for replace.
     *
     * @param string $table, name of the table, CAN NOT BE NULL
     * @param array $fields, fields in table, which values will be inserted in, CAN NOT BE NULL
     *
     * @return string
     */
    public function prepareReplaceSQL($table, $fields)
    {
        $countFields = count($fields);
        if (0 != $countFields) {
            $sql = 'REPLACE INTO ' . $table . ' (' . implode(', ', $fields) . ') VALUES (';

            for ($i = 0; $i < $countFields; $i++) {
                $sql .= ":field{$i}";
                if ($i != $countFields - 1) {
                    $sql .= ', ';
                }
            }

            $sql .= ')';

            return $sql;
        }

        return false;
    }

    /**
     * Prepares Update SQL query.
     *
     * @param string $table, name of the table, CAN NOT BE NULL
     * @param array $fields, fields in table, which values will be updated in, CAN NOT BE NULL
     * @param array $fieldTerms, field's name of records, which will be used in where clause, CAN NOT BE NULL
     *
     * @return string
     */
    public function prepareUpdateSQL($table, $fields, $fieldTerms)
    {
        $sql = "UPDATE {$table} SET ";

        $cntFields = count($fields);

        for ($i = 0; $i < $cntFields; $i++) {
            $sql .= " {$fields[$i]} = :setField{$i}";
            if ($i != $cntFields - 1) {
                $sql .= ', ';
            }
        }

        $cntTerms = count($fieldTerms);

        if ($cntTerms) {
            $sql .= ' WHERE ';
        }

        for ($i = 0; $i < $cntTerms; $i++) {
            $sql .= " {$fieldTerms[$i]} = :whereField{$i}";
            if ($i != $cntTerms - 1) {
                $sql .= ' AND ';
            }
        }

        return $sql;
    }

    /**
     * Prepares DeleteSQL query.
     *
     * @param string $table, name of the table, CAN  NOT BE NULL
     * @param string $fieldTerms, field's name of a records, which will be updated
     *
     * @return string
     */
    public function prepareDeleteSQL($table, $fieldTerms)
    {
        $cntTerms = count($fieldTerms);
        if ($cntTerms) {
            $sql = "DELETE FROM {$table} WHERE ";

            for ($i = 0; $i < $cntTerms; $i++) {
                $sql .= " {$fieldTerms[$i]} = :field{$i}";
                if ($i != $cntTerms - 1) {
                    $sql .= ' AND ';
                }
            }
        }

        return $sql;
    }

    /**
     * Sets fields null.
     *
     * @param string $table - Name of the table with the records to be updated
     * @param array $fields - the fields that need to be set null
     * @param array $where - the where clause fields/values
     */
    public function setNull($table, $fields, $where)
    {
        if ($table and is_array($fields) and is_array($where)) {
            $set = '';
            foreach ($fields as $field) {
                if ($set) {
                    $set .= ', ';
                }
                $set .= $field . ' = NULL';
            }

            $whereClause = '';
            foreach ($where as $field => $value) {
                if ($whereClause) {
                    $whereClause .= ' AND ';
                }
                $whereClause .= $field . ' = :' . $field;
            }

            $sql = "UPDATE {$table} SET {$set} WHERE {$whereClause}";

            $cmd = $this->DbModule->createCommand($sql);

            foreach ($where as $field => $value) {
                $cmd->bindValue(':' . $field, $value, PDO::PARAM_STR);
            }

            $cmd->execute();
        }
    }

    /**
     * Returns all the items from a table.
     *
     * @param string $table
     *
     * @return array
     */
    public function getExportData($table)
    {
        $sql = "SELECT * FROM {$table}";
        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    /**
     * Returns whether the table exists.
     *
     * @param string $table
     *
     * @return bool
     */
    public function getTableInfo($table)
    {
        $sql = 'SHOW TABLES LIKE :table';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindValue(':table', $table, PDO::PARAM_STR);
        $exists = $cmd->queryScalar() ? true : false;
        if ($exists) {
            $sql = "SHOW COLUMNS FROM {$table}";
            $cmd = $this->DbModule->createCommand($sql);
            $data = $cmd->query()->readAll();
            $fields = [];
            foreach ($data as $field) {
                $fields[] = $field['Field'];
            }
        }

        return ['exists' => $exists, 'fields' => $fields];
    }

    /**
     * Clears the table.
     *
     * @param string $table
     */
    public function clearTable($table)
    {
        $sql = "DELETE FROM {$table}";
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->execute();
    }

    /**
     * Sets the foreign key check.
     *
     * @param bool $check
     */
    public function setCheckForeignKeys($check)
    {
        $sql = 'SET FOREIGN_KEY_CHECKS = :check';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindValue(':check', $check, PDO::PARAM_INT);
        $cmd->execute();
    }
}
