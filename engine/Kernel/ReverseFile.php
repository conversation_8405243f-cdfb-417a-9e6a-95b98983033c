<?php

namespace TF\Engine\Kernel;

use Iterator;

/**
 * An iterator class for reading files backwards.
 * It can be usefull for reading only last n-th
 * lines from log files.
 *
 * <AUTHOR>
 *
 * @see http://stackoverflow.com/a/10494801
 */
class ReverseFile implements Iterator
{
    public const BUFFER_SIZE = 4096;
    public const SEPARATOR = "\n";

    public function __construct($filename)
    {
        $this->_fh = fopen($filename, 'r');
        $this->_filesize = filesize($filename);
        $this->_pos = -1;
        $this->_buffer = null;
        $this->_key = -1;
        $this->_value = null;
    }

    public function _read($size)
    {
        $this->_pos -= $size;
        fseek($this->_fh, $this->_pos);

        return fread($this->_fh, $size);
    }

    public function _readline()
    {
        $buffer = &$this->_buffer;
        while (true) {
            if (0 == $this->_pos) {
                return array_pop($buffer);
            }
            if (count($buffer) > 1) {
                return array_pop($buffer);
            }
            $buffer = explode(self::SEPARATOR, $this->_read(self::BUFFER_SIZE) . $buffer[0]);
        }
    }

    public function next()
    {
        ++$this->_key;
        $this->_value = $this->_readline();
    }

    public function rewind()
    {
        if ($this->_filesize > 0) {
            $this->_pos = $this->_filesize;
            $this->_value = null;
            $this->_key = -1;
            $this->_buffer = explode(self::SEPARATOR, $this->_read($this->_filesize % self::BUFFER_SIZE ?: self::BUFFER_SIZE));
            $this->next();
        }
    }

    public function key()
    {
        return $this->_key;
    }

    public function current()
    {
        return $this->_value;
    }

    public function valid()
    {
        return !is_null($this->_value);
    }
}
