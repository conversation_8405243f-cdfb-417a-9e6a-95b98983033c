<?php

namespace TF\Engine\Kernel;

/**
 * LoggerMessage File class file.
 *
 * <AUTHOR> Ltd. <<EMAIL>>
 *
 * @link http://www.devision.bg/
 *
 * @copyright Copyright &copy; 2008 Devision Ltd.
 */
/**
 * Logger messages class.
 *
 * Contains constants that are used for logging events
 */
class LoggerMessages
{
    public const BACKEND_USER_LOGGED = 'User logged in.';
    public const BACKEND_DENY_REQUEST = 'Request denied.';
    public const BACKEND_USER_LOGOUT = 'User logged out.';

    public const ITEM_ADD = 'Record added.';
    public const ITEM_EDIT = 'Record updated.';
    public const ITEM_DELETE = 'Record deleted.';
    public const ITEM_MOVE_TO_TRASH = 'Records moved to trash.';
    public const ITEM_RESTORE_FROM_TRASH = 'Records restored from trash.';
    public const ITEM_DELETE_FROM_TRASH = 'Records deleted from trash.';

    public const ORDER_STATUS_CHANGE = 'Status of an order has been changed.';

    public const BACKEND_USER_WRONG_CODE = '{Login error} Invalid security code.';
    public const BACKEND_WRONG_ACOUNT = '{Login error} Invalid username or password.';

    public const WRONG_ACCOUNT = 'Wrong username or password';
    public const USER_LOGGED = 'User is logged in';
}
