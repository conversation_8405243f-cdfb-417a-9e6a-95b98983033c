<?php

namespace TF\Engine\Kernel;

/**
 * Kernel String class file.
 *
 * <AUTHOR>
 */

/**
 * Kernel String class.
 *
 * Implements functionality for manipulation of strings
 */
class StringHelper
{
    /**
     * Converts php string to javascript compatable string
     * to be passed as parameter in js functions.
     *
     * @param string $string
     *
     * @return string
     */
    public function jsSpecialChars($string = '')
    {
        $string = preg_replace("/\r*\n/", '\\n', $string);
        $string = preg_replace("/\//", "\\\/", $string);
        $string = preg_replace('/"/', '\\"', $string);

        return preg_replace("/'/", ' ', $string);
    }

    /**
     * 	Description: string str_makerand(int $minLength, int $maxLength, bool $useUpper, bool $useSpecial, bool $useNumbers)
     * 	returns a randomly generated string of length between $minlength and $maxlength inclusively.
     *
     * 	Notes:
     * 	- If $useUpper is true uppercase characters will be used; if false they will be excluded.
     * 	- If $useSpecial is true special characters will be used; if false they will be excluded.
     * 	- If $useNumbers is true numerical characters will be used; if false they will be excluded.
     * 	- If $minLength is equal to $minLength a string of length $maxlength will be returned.
     * 	- Not all special characters are included since they could cause parse errors with queries.
     *
     * 	Modify at will.
     */
    public function str_makerand($minLength, $maxLength, $useUpper, $useSpecial, $useNumbers)
    {
        $charset = 'abcdefghijklmnopqrstuvwxyz';
        if ($useUpper) {
            $charset .= 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        }
        if ($useNumbers) {
            $charset .= '0123456789';
        }
        if ($useSpecial) {
            $charset .= '~@#$%^*()_+-={}|]['; // Note: using all special characters this reads: "~!@#$%^&*()_+`-={}|\\]?[\":;'><,./";
        }
        if ($minLength > $maxLength) {
            $length = mt_rand($maxLength, $minLength);
        } else {
            $length = mt_rand($minLength, $maxLength);
        }
        $key = '';
        for ($i = 0; $i < $length; $i++) {
            $key .= $charset[(mt_rand(0, (strlen($charset) - 1)))];
        }

        return $key;
    }

    public function randomColorCode()
    {
        return str_pad(dechex(mt_rand(0, 0xFFFFFF)), 6, '0', STR_PAD_LEFT);
    }

    /**
     * Returns date formated time by given timestamp.
     *
     * @param int $timestamp
     * @param string $format
     *
     * @return string
     */
    public function formatTimestamp($timestamp, $format)
    {
        $d = preg_split('/( )|(-)|(:)/', $timestamp);

        return date($format, mktime($d[3], $d[4], $d[5], $d[1], $d[2], $d[0]));
    }

    /**
     * Returns just the file extension from a filename.
     *
     * @param string $fileName
     *
     * @return string
     */
    public function GetFileExtention($fileName)
    {
        return strstr($fileName, '.');
    }

    /**
     * Returns just the file extension of a file.
     *
     * @param string $filePath
     *
     * @return string
     */
    public function GetFileExtentionByPath($filePath)
    {
        $info = pathinfo($filePath);

        return $info['extension'];
    }

    /**
     * Replaces all occurances of string in text, so that after this
     * it is highlighted.
     *
     * @param string $search
     * @param string $text
     * @param string $highlightColor
     *
     * @return string
     */
    public function replaceSearchString($search, $string, $highlightColor = 'red')
    {
        $word = explode(' ', strip_tags($search));
        $replace = explode(' ', strip_tags($search));
        for ($i = 0; $i < count($word); $i++) {
            $replace[$i] = '<span style="color:' . $highlightColor . '">' . $replace[$i] . '</span>';
        }

        $pos = strpos($string, $word[0]);
        $arr1 = str_split($string);

        $returnString = '';

        for ($i = 0; $i < count($arr1); $i++) {
            if ($pos >= $i - 100 && $pos <= $i + 100) {
                $returnString .= $arr1[$i];
            }
        }
        if (count($arr1) < 100) {
            $string = str_replace($word, $replace, $returnString) . '...';
        } else {
            $string = '...' . str_replace($word, $replace, $returnString) . '...';
        }

        return $string;
    }

    /**
     * Returns specified number of words in string.
     *
     * @param string $string
     * @param int $wordsNumber
     * @param $isTag boolean
     *
     * @return string
     */
    public function getWordsInString($string, $wordsNumber, $isTags = false)
    {
        if (!$isTags) {
            $string = str_replace(['<br>', '<br />', '</p>', '</li>', '</td>'], ' ', $string);
            $string = strip_tags($string);
        } else {
            $string = strip_tags($string, '<font><strong><b><br><br /><p>');
        }
        $words = explode(' ', $string);

        if ($wordsNumber > count($words)) {
            return $string;
        }

        $newString = '';
        for ($i = 0; $i < $wordsNumber; $i++) {
            $newString .= $words[$i] . ' ';
        }
        $newString .= '...';

        return $newString;
    }

    /**
     * Returns specified number of words in string.
     *
     * @param string $string - initial string
     * @param int $chars - maximum number of characters
     *
     * @return string - returned string
     */
    public function getCharsInString($string, $chars, $stripTags = true)
    {
        if ($stripTags) {
            $string = strip_tags($string);
        }

        $length = mb_strlen($string);
        if ($chars < $length) {
            $pos = mb_strrpos(mb_substr($string, 0, $chars - 2), ' ');
            $string = mb_substr($string, 0, $pos);
            $string .= '...';
        }

        return $string;
    }

    /**
     * Used to transform $keywords string(entered in search input)
     * into an array. Illegal characters are replaced with whitespace.
     *
     * @param string $string
     *
     * @return string
     */
    public function splitInputString($string)
    {
        return mb_ereg_replace('[@|\-|,|#|%|\[|<|>(|)|!|#|$|%|\|^|&|=|+|~|`|*|"|\'|¡|¤|¢|£|¥|¦|§|\]|/|*|&|!|:]', ' ', $string);
    }

    public function getYesNo($value)
    {
        if ($value) {
            return 'Да';
        }

        return 'Не';
    }

    /**
     * Get User Type.
     *
     * @param $value integer
     *
     * @return string
     */
    public function getUserType($value)
    {
        switch ($value) {
            case 1:
                return 'Администратор';

                break;
            case 0:
                return 'Дистрибутор';

                break;
        }
    }

    /**
     * Get User Type.
     *
     * @param $value integer
     *
     * @return string
     */
    public function getMonthName($value)
    {
        switch ($value) {
            case 1:
                return 'Януари';
            case 2:
                return 'Февруари';
            case 3:
                return 'Март';
            case 4:
                return 'Април';
            case 5:
                return 'Май';
            case 6:
                return 'Юни';
            case 7:
                return 'Юли';
            case 8:
                return 'Август';
            case 9:
                return 'Септември';
            case 10:
                return 'Октомври';
            case 11:
                return 'Ноември';
            case 12:
                return 'Декември';
        }
    }

    public function setMonthUpToNow()
    {
        $data = [];
        $current_month = (int) date('m');

        for ($i = 0; $i < $current_month; $i++) {
            $data[$i]['title'] = $this->getMonthName($i + 1);
            $data[$i]['number'] = $i + 1;
        }

        return $data;
    }

    public function setYearUpToNow()
    {
        $data = [];
        $current_year = date('Y');

        for ($i = 2010; $i <= $current_year; $i++) {
            $data[$i]['title'] = $i;
            $data[$i]['number'] = $i;
        }

        return $data;
    }

    /**
     * Retrieves the server http url to storage folder.
     */
    public function getServerHTTP($serverID)
    {
        return $GLOBALS['DISTRIBUTED_SERVERS'][$serverID]['server'] . $GLOBALS['DISTRIBUTED_SERVERS'][$serverID]['path'];
    }

    /**
     * Retrieves the server http url to storage folder.
     */
    public function getServerPath($serverID)
    {
        return $GLOBALS['DISTRIBUTED_SERVERS'][$serverID]['storage_path'];
    }

    /**
     * Retrieves the server http url to storage folder.
     */
    public function getServerRelativePath($serverID)
    {
        return $GLOBALS['DISTRIBUTED_SERVERS'][$serverID]['path'];
    }

    public function convertToLatin($string)
    {
        $cyrilic = ['a', 'b', 'v', 'g', 'd', 'e', 'j', 'z', 'i', 'ii', 'k', 'l', 'm', 'n', 'o', 'p', 'r', 's', 't', 'u', 'f', 'h', 'c', 'ch', 'sh', 'sht', 'u', 'io', 'iu', 'q', 'A', 'B', 'V', 'G', 'D', 'E', 'J', 'Z', 'I', 'II', 'K', 'L', 'M', 'N', 'O', 'P', 'R', 'S', 'T', 'U', 'F', 'H', 'C', 'CH', 'SH', 'SHT', 'U', 'IO', 'IU', 'Q'];
        $latin = ['а', 'б', 'в', 'г', 'д', 'е', 'ж', 'з', 'и', 'й', 'к', 'л', 'м', 'н', 'о', 'п', 'р', 'с', 'т', 'у', 'ф', 'х', 'ц', 'ч', 'ш', 'щ', 'ъ', 'ь', 'ю', 'я', 'А', 'Б', 'В', 'Г', 'Д', 'Е', 'Ж', 'З', 'И', 'Й', 'К', 'Л', 'М', 'Н', 'О', 'П', 'Р', 'С', 'Т', 'У', 'Ф', 'Х', 'Ц', 'Ч', 'Ш', 'Щ', 'Ъ', 'Ь', 'Ю', 'Я'];

        return str_replace($latin, $cyrilic, $string);
    }

    public function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, $precision) . ' ' . $units[$pow];
    }

    public function loadTemplate($template, $data)
    {
        if (is_array($data) && count($data) > 0) {
            extract($data, EXTR_PREFIX_SAME, 'wddx');
        }

        ob_start();
        require TEMPLATE_PATH . $template . '.php';
        $content = ob_get_contents();
        ob_end_clean();

        return $content;
    }

    public function loadTemplateInSegments($template, $data)
    {
        if (is_array($data) && count($data) > 0) {
            extract($data, EXTR_PREFIX_SAME, 'wddx');
            unset($data);
        }

        ob_start(null, 4096);
        require TEMPLATE_PATH . $template . '.php';

        $content = '';
        while (ob_get_level() > 0) {
            $content .= ob_get_clean();
        }
        ob_end_clean();

        return $content;
    }

    public function numToString($num)
    {
        $num = intval($num);

        $nums0 = [0 => 'нула', 1 => 'един', 2 => 'два', 3 => 'три', 4 => 'четири',
            5 => 'пет', 6 => 'шест', 7 => 'седем', 8 => 'осем', 9 => 'девет',
            10 => 'десет', 11 => 'единадесет'];
        $nums100 = [1 => 'сто', 2 => 'двеста', 3 => 'триста'];

        $div10 = ($num - $num % 10) / 10;
        $mod10 = $num % 10;
        $div100 = ($num - $num % 100) / 100;
        $mod100 = $num % 100;
        $div1000 = ($num - $num % 1000) / 1000;
        $mod1000 = $num % 1000;

        if (0 == $num) {
            return $nums0[$num];
        }

        if ($num > 0 && $num < 20) {
            return $nums0[$num] ?? $nums0[$mod10] . 'надесет';
        }

        if ($num > 19 && $num < 100) {
            $tmp = $nums0[$div10] . 'десет';
            $tmp .= $mod10 ? ' и ' . $nums0[$mod10] : '';

            return $tmp;
        }

        if ($num > 99 && $num < 1000) {
            $tmp = $nums100[$div100] ?? $nums0[$div100] . 'стотин';
            if ((0 == $mod100 % 10 || $mod100 < 20) && 0 != $mod100) {
                $tmp .= ' и';
            }
            if ($mod100) {
                $tmp .= ' ' . $this->numToString($mod100);
            }

            return $tmp;
        }

        if ($num > 999 && $num < 1000000) {
            $tmp = (1 == $div1000) ? 'хиляда' : ((2 == $div1000) ? 'две хиляди' : $this->numToString($div1000) . ' хиляди');
            if ((0 == $mod1000 % 10 || $mod1000 < 20) && 0 != $mod1000) {
                if (!((0 == $mod100 % 10 || $mod100 < 20) && 0 != $mod100)) {
                    $tmp .= ' и';
                }
            }
            if ((0 == $mod1000 % 10 || $mod1000 < 20) && 0 != $mod1000 && $mod1000 < 100) {
                $tmp .= ' и';
            }
            if ($mod1000) {
                $tmp .= ' ' . $this->numToString($mod1000);
            }

            return $tmp;
        }
    }

    public function float2rat($n, $tolerance = 1.e-5)
    {
        $h1 = 1;
        $h2 = 0;
        $k1 = 0;
        $k2 = 1;

        if (0 == $n) {
            return '0';
        }

        $b = 1 / $n;
        do {
            $b = 1 / $b;
            $a = floor($b);
            $aux = $h1;
            $h1 = $a * $h1 + $h2;
            $h2 = $aux;
            $aux = $k1;
            $k1 = $a * $k1 + $k2;
            $k2 = $aux;
            $b = $b - $a;
        } while (abs($n - $h1 / $k1) > $n * $tolerance);

        return "{$h1}/{$k1}";
    }

    public function generateColorArray($start_color, $color_count)
    {
        $colorArray = [];
        $theColorBegin = $start_color;
        $theColorEnd = 0xFFFFFF;

        $theNumSteps = $color_count;

        $theR0 = ($theColorBegin & 0xFF0000) >> 16;
        $theG0 = ($theColorBegin & 0x00FF00) >> 8;
        $theB0 = ($theColorBegin & 0x0000FF) >> 0;

        $theR1 = ($theColorEnd & 0xFF0000) >> 16;
        $theG1 = ($theColorEnd & 0x00FF00) >> 8;
        $theB1 = ($theColorEnd & 0x0000FF) >> 0;

        // return the interpolated value between pBegin and pEnd
        // generate gradient swathe now
        for ($i = 0; $i <= $theNumSteps; $i++) {
            $theR = $this->interpolate($theR0, $theR1, $i, $theNumSteps);
            $theG = $this->interpolate($theG0, $theG1, $i, $theNumSteps);
            $theB = $this->interpolate($theB0, $theB1, $i, $theNumSteps);

            $theVal = ((($theR << 8) | $theG) << 8) | $theB;

            $colorArray[] = sprintf('%06X', $theVal);
        }

        $colorArray = array_reverse($colorArray);

        return $colorArray;
    }

    public function interpolate($pBegin, $pEnd, $pStep, $pMax)
    {
        if ($pBegin < $pEnd) {
            return (($pEnd - $pBegin) * ($pStep / $pMax)) + $pBegin;
        }

        return (($pBegin - $pEnd) * (1 - ($pStep / $pMax))) + $pEnd;
    }

    /**
     * @return string
     */
    public function romans_number($integer)
    {
        $table = ['M' => 1000, 'CM' => 900, 'D' => 500, 'CD' => 400, 'C' => 100, 'XC' => 90, 'L' => 50, 'XL' => 40, 'X' => 10, 'IX' => 9, 'V' => 5, 'IV' => 4, 'I' => 1];
        $return = '';
        while ($integer > 0) {
            foreach ($table as $rom => $arb) {
                if ($integer >= $arb) {
                    $integer -= $arb;
                    $return .= $rom;

                    break;
                }
            }
        }

        return $return;
    }

    public function strToBlankFormat($str, $boxCount, $orientation = 'left')
    {
        $str = trim($str);
        $chars = preg_split('/(?!^)(?=.)/u', $str);
        $return = null;
        for ($i = 0; $i < count($chars); $i++) {
            if (' ' == $chars[$i]) {
                $return .= '|&emsp;';
            } else {
                if (0 == $i) {
                    $return .= '&nbsp;' . $chars[$i] . '&nbsp;';
                } else {
                    $return .= '|&nbsp;' . $chars[$i] . '&nbsp;';
                }
            }
        }
        if ('left' == $orientation) {
            $return .= str_repeat('|&emsp;', $boxCount - count($chars));
        } elseif ('right' == $orientation) {
            $return = str_repeat('&emsp;|', $boxCount - count($chars)) . $return;
        }

        return $return;
    }

    public function getFarmingYearByDate($date_string)
    {
        $date_to_time = strtotime($date_string);

        $year = date('Y', $date_to_time);
        $month = date('m', $date_to_time);
        $date = date('d', $date_to_time);

        $last_date = $year . '-09-30';
        $last_date_to_time = strtotime($last_date);

        if ($date_to_time <= $last_date_to_time) {
            $farming_year_id = (int) $year - 2009;
        } else {
            $farming_year_id = (int) $year + 1 - 2009;
        }

        return $farming_year_id;
    }

    public function jsonRemoveUnicodeSequences($struct)
    {
        return preg_replace('/\\\\u([a-f0-9]{4})/', "iconv('UCS-4LE','UTF-8',pack('V', hexdec('U$1')))", json_encode($struct, JSON_UNESCAPED_UNICODE));
    }

    public function calculateFractions($numerator, $denominator, $number)
    {
        return $numerator . '/' . ($denominator * $number);
    }

    public function validateEgn($value)
    {
        $value = (string) $value;
        $egnControlDigit = substr($value, 9);

        $weights = [2, 4, 8, 5, 10, 9, 7, 3, 6];
        $controlSum = 0;

        for ($i = 0; $i < 9; $i++) {
            $egnDigit = $value[$i];
            $weight = $weights[$i];
            $controlSum = $controlSum + ($egnDigit * $weight);
        }

        $controlDigit = $controlSum % 11;
        if (10 == $controlDigit) {
            $controlDigit = 0;
        }

        return $egnControlDigit == $controlDigit;
    }

    public function validateEkatte(string $ekatte)
    {
        preg_match('/^\d{5,}$/', $ekatte, $matches);

        return count($matches) > 0;
    }

    /**
     * Translate a string to latin string contains only alphabets, digits, and underscore.
     *
     * @return string
     */
    public function transLitString($string)
    {
        $result = $this->convertToLatin($string);
        $result = preg_replace('/[^a-zA-Z\d]/', '_', $result);
        $result = preg_replace('/_+/', '_', $result);

        return trim($result, '_');
    }

    public function trimStringToLength($string, $length = 10, $addPeriods = true)
    {
        if (mb_strlen($string, 'UTF-8') > $length) {
            if ($addPeriods) {
                return mb_substr($string, 0, $length - 3, 'UTF-8') . '...';
            }

            return mb_substr($string, 0, $length, 'utf-8');
        }

        return $string;
    }

    /**
     * Properly escape a literal value for PostgreSQL.
     * This is equivalent to PostgreSQL's pg_escape_literal function.
     *
     * @param mixed $value The value to escape
     *
     * @return string The escaped literal value
     */
    public function escapeLiteral($value): string
    {
        if (null === $value) {
            return 'NULL';
        }

        // Convert to string
        $stringValue = (string)$value;

        // Escape single quotes by doubling them and wrap in single quotes
        $escaped = "'" . str_replace("'", "''", $stringValue) . "'";

        // Additional protection: escape backslashes for PostgreSQL
        return str_replace('\\', '\\\\', $escaped);
    }
}
