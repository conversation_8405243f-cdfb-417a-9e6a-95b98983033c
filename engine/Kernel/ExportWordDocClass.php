<?php

namespace TF\Engine\Kernel;

use <PERSON>rado\TModule;
use Prado\Xml\TXmlDocument;

class ExportWordDocClass extends TModule
{
    public $TmpDir;
    private $StringHelper;
    private $responseFiles = [];
    private $defSection = [
        'WordSection1' => [
            'size' => '21cm 29.7cm',
            'margin' => '2cm 2cm 2cm 2cm',
            'mso-page-orientation' => 'portrait',
        ],
    ];

    /**
     * Initalize the ExportWordDocClass properties from attributes in <module> tag.
     *
     * @param TXmlDocument xml configuration
     */
    public function init($xml)
    {
        parent::init($xml);
        $this->StringHelper = new StringHelper();
        if (!isset($this->TmpDir)) {
            $this->TmpDir = sys_get_temp_dir();
        }
    }

    /**
     * Exports the doc file.
     *
     * @param string $name the name of the file we want to create
     * @param string $template the content of document
     * @param bool $exportTofile if true the word doc will be saved in a file
     *
     * @return array array('file' => '')
     */
    public function export($name, $template, $exportTofile = false, $docOpts = ['sections' => []])
    {
        $this->responseFiles = [];

        if (0 == count($docOpts['section'])) {
            $docOpts['section'][] = $this->defSection;
        }

        $name = preg_replace("@[\/]+@", '_', $name);
        $headerTxt = $this->getHeaderFooterTag('page_header', $template);
        $headerTxt = $this->convertToCyr($headerTxt);
        $footerTxt = $this->getHeaderFooterTag('page_footer', $template);
        $footerTxt = $this->convertToCyr($footerTxt);

        $wordHeaderFooter = $GLOBALS['Templates'][28]['template'];
        $this->StringHelper = new StringHelper();

        $headerFooterTmplTxt = $this->StringHelper->loadTemplate($wordHeaderFooter, [
            'header' => $headerTxt,
            'footer' => $footerTxt,
        ]);

        $pathToCreatedDir = '/' . $name . '_files/';
        $wordPath = '/' . $name . '.htm';
        $blanksPath = SITE_PATH . 'public/files/uploads/blanks/' . $this->User->GroupID . '/';

        $template = str_replace('{base_path}', $pathToCreatedDir, $template);
        $this->createBodyFile($wordPath, $pathToCreatedDir, $name, $template, $docOpts);

        $images = $this->getImages($template);
        foreach ($images as $image) {
            $this->addResponseFile([
                'path' => $pathToCreatedDir . pathinfo($image, PATHINFO_BASENAME),
                'data' => file_get_contents(TEMPLATE_PATH . '/imgs/' . pathinfo($image, PATHINFO_BASENAME)),
                'content_type' => mime_content_type(TEMPLATE_PATH . '/imgs/' . pathinfo($image, PATHINFO_BASENAME)),
            ]);
        }

        $wordHeaderFooterPath = $pathToCreatedDir . 'headerfooter.htm';
        $headerFooterTmplTxt = str_replace('{base_path}', $pathToCreatedDir, $headerFooterTmplTxt);
        $this->addResponseFile([
            'path' => $wordHeaderFooterPath,
            'data' => $headerFooterTmplTxt,
            'content_type' => 'text/html; charset="utf-8"',
        ]);

        if (!file_exists($blanksPath)) {
            $makeDir = mkdir($blanksPath, 0777);
        }

        $response = '';
        if ($exportTofile) {
            $docPath = $blanksPath . $name . '.doc';
            $response = $this->exportToFile($docPath);
        } else {
            $this->response($name);
        }

        return $response;
    }

    public function createBodyFile($wordPath, $pathToCreatedDir, $name, $template, $docOpts)
    {
        $wordContTmpl = $GLOBALS['Templates'][27]['template'];
        $ltext = $this->StringHelper->loadTemplate($wordContTmpl, array_merge([
            'content' => $template,
            'dirName' => $name,
            'title' => $name,
        ], $docOpts));
        $ltext = str_replace('{base_path}', $pathToCreatedDir, $ltext);

        $this->addResponseFile([
            'path' => $wordPath,
            'data' => $ltext,
            'content_type' => 'text/html; charset="utf-8"',
        ]);
    }

    public function packWordFiles(array $filesData, $boundaryStr = '_NextPart_ZROIIZO.ZCZYUACXV.ZARTUI')
    {
        $globalStart = "MIME-Version: 1.0\nContent-Type: multipart/related; boundary=\"----={$boundaryStr}\"\n";

        for ($i = 0; $i < count($filesData); $i++) {
            $filePath = $filesData[$i]['path'];
            $fileData = $filesData[$i]['data'];
            $fileContentType = $filesData[$i]['content_type'];

            $start = "\n------={$boundaryStr}";
            $start .= "\nContent-Location: " . preg_replace('!\\\!', '/', $filePath);
            $start .= "\nContent-Transfer-Encoding: base64";
            $start .= "\nContent-Type: " . $fileContentType . "\n\n";
            $start .= base64_encode($fileData) . "\n";
            $globalStart .= $start;
        }

        $globalEnd = "\n------={$boundaryStr}--\n\n";
        $globalStart .= $globalEnd;

        return $globalStart;
    }

    public function getHeaderFooterTag($tagName, &$template)
    {
        $tagContentRe = "/\[\[{$tagName}_\]\](?P<content>.*?)\[\[_{$tagName}\]\]/s";
        $matches = [];

        if (!preg_match_all($tagContentRe, $template, $matches)) {
            return '';
        }
        $template = preg_replace($tagContentRe, '', $template);

        return $matches['content'][0];
    }

    public function getImages($string)
    {
        $imageRe = "/<img\\s[^>]*?src\\s*=\\s*['\\\"](?:(?P<img_src>[^'\\\"]*?)['\\\"][^>]*?>)/s";
        $matches = [];

        if (preg_match_all($imageRe, $string, $matches)) {
            return $matches['img_src'];
        }

        return [];
    }

    public function addPageNumbers($content, $pageNumber = 'center')
    {
        return '<div class="WordSection">
                        ' . $content . '
                        [[page_footer_]]
                        <p class=MsoFooter style=\'text-align: ' . $pageNumber . '\'>
                            <!--[if supportFields]>
                            <span class=MsoPageNumber>
                                                        <span style=\'mso-element:field-begin\'></span>
                                                        <span style=\'mso-spacerun:yes\'> </span>PAGE
                                                        <span style=\'mso-element:field-separator\'></span>
                                                    </span>
                            <![endif]-->
                            <span class=MsoPageNumber><span style=\'mso-no-proof:yes\'>1</span></span>
                            <!--[if supportFields]>
                            <span class=MsoPageNumber>
                                                        <span style=\'mso-element:field-end\'></span>
                                                    </span>
                            <![endif]-->
                            <span class=MsoPageNumber>/</span>
                            <!--[if supportFields]>
                            <span class=MsoPageNumber>
                                                        <span style=\'mso-element:field-begin\'></span> NUMPAGES <span style=\'mso-element:field-separator\'></span>
                                                    </span>
                            <![endif]-->
                            <span class=MsoPageNumber><span style=\'mso-no-proof:yes\'>1</span></span>
                            <!--[if supportFields]>
                            <span class=MsoPageNumber>
                                                        <span style=\'mso-element:field-end\'></span>
                                                    </span>
                            <![endif]-->
                        </p>
                    [[_page_footer]]
                </div>';
    }

    private function response($fileName)
    {
        $this->Response->setContentType('application/msword');
        $this->Response->clear();
        $this->Response->appendHeader('Content-disposition: attachment; filename=' . $fileName . '.doc', true);
        $packedResponce = $this->packWordFiles($this->responseFiles);
        $this->Response->write($packedResponce);
        $this->Response->flush();
    }

    private function exportToFile($path)
    {
        $packedResponce = $this->packWordFiles($this->responseFiles);

        $fp = fopen($path, 'w');
        // chmod($path, 0777);
        fputs($fp, $packedResponce);
        fclose($fp);

        return 'files/uploads/blanks/' . $this->User->GroupID . '/' . basename($path);
    }

    private function convertToCyr($str)
    {
        if (mb_check_encoding($str, 'utf8')) {
            return mb_convert_encoding($str, 'cp1251');
        }

        return $str;
    }

    private function addResponseFile($data)
    {
        $this->responseFiles[] = $data;
    }
}
