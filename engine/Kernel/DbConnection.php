<?php

namespace TF\Engine\Kernel;

use Prado\Data\TDbConnection;
use Prado\Prado;
use Prado\TPropertyValue;

/**
 * DbHandlerConnection class file.
 *
 * <AUTHOR>
 */

/**
 * DbHandlerConnection class.
 *
 * DbHandlerConnection represents connection and operation to a database.
 *
 * DbHandlerConnection works together with DbHandler class
 * There is a private method named getDatabaseConnection, who creates instance to TDbConnection
 * __call listener catch methods and properties
 * PHP extension.
 */
class DbConnection
{
    private $db;
    private $_Driver;
    private $_Host;
    private $_Username;
    private $_Password;
    private $_Database;
    private $_Port;
    private $_Persistent = false;

    private $_HandlerClassName;

    /**
     * Constructor.
     * Note, the DB connection is not established when this
     * instance is created. Only some properties are set.
     *
     * @param string $driver, contains the information database engine;
     * @param string $host
     * @param int $port, contains port required in connection;
     * @param string $user, contains username for the DSN string;
     * @param string $pass, contains password for the DSN string;
     * @param string $db, contains database name for the DSN string;
     * @param bool $persistent
     */
    public function __construct($driver, $host, $port, $user, $pass, $db, $persistent, $handlerClassName)
    {
        $this->_Driver = $driver;
        $this->_Host = $host;
        $this->_Username = $user;
        $this->_Password = $pass;
        $this->_Database = $db;
        $this->_Persistent = $persistent;
        $this->_Port = $port;
        $this->_HandlerClassName = $handlerClassName;
    }

    /**
     * CALL Listener.
     *
     * @param array $params
     *
     * @return returns the function result, or FALSE on error
     */
    public function __call($method, $params)
    {
        $conn = $this->getDatabaseConnection();
        $conn->createCommand("SET NAMES 'utf8'")->execute();

        Prado::log('Model: ' . $this->_HandlerClassName . '  ' . $params[0], 2, 'SQL');

        return call_user_func_array([$conn, $method], $params);
    }

    /**
     * Returns database instance.
     */
    public function getDbConnection()
    {
        return $this->db;
    }

    /** Gets driver.
     *
     * @return string Driver
     */
    public function getDriver()
    {
        return $this->_Driver;
    }

    /** Sets driver.
     *
     * @param string $value
     */
    public function setDriver($value)
    {
        $this->_Driver = TPropertyValue::ensureString($value);
    }

    /**
     * Gets host.
     *
     * @return string
     */
    public function getHost()
    {
        return $this->_Host;
    }

    /**
     * Sets host.
     *
     * @param string $value
     */
    public function setHost($value)
    {
        $this->_Host = TPropertyValue::ensureString($value);
    }

    /**
     * Gets username.
     *
     * @return string
     */
    public function getUsername()
    {
        return $this->_Username;
    }

    /**
     * Sets username.
     *
     * @param string $value
     */
    public function setUsername($value)
    {
        $this->_Username = TPropertyValue::ensureString($value);
    }

    /**
     * Gets password.
     *
     * @return string
     */
    public function getPassword()
    {
        return $this->_Password;
    }

    /**
     * Sets password.
     *
     * @param string $value
     */
    public function setPassword($value)
    {
        $this->_Password = TPropertyValue::ensureString($value);
    }

    /**
     * Gets database.
     *
     * @return string
     */
    public function getDatabase()
    {
        return $this->_Database;
    }

    /**
     * Sets database.
     *
     * @param string $value
     */
    public function setDatabase($value)
    {
        $this->_Database = TPropertyValue::ensureString($value);
    }

    /**
     * Gets persistant.
     *
     * @return bool
     */
    public function getPersistent()
    {
        return $this->_Persistent;
    }

    /**
     * Sets persistant.
     *
     * @param bool $value
     */
    public function setPersistent($value)
    {
        $this->_Persistent = TPropertyValue::ensureBoolean($value);
    }

    /**
     * Gets port.
     *
     * @return int
     */
    public function getPort()
    {
        return $this->_Port;
    }

    /**
     * Sets port.
     *
     * @param int $value
     */
    public function setPort($value)
    {
        $this->_Port = TPropertyValue::ensureInteger($value);
    }

    /**
     * This method establishes connection to database through PRADO TDbConnection class.
     *
     * @return connection mixed
     */
    private function getDatabaseConnection()
    {
        if (null === $this->db) {
            $dsn = "{$this->_Driver}:host={$this->_Host};port={$this->_Port};dbname={$this->_Database};options='--application_name=" . APP_NAME . "';";
            $this->db = new TDbConnection($dsn, $this->_Username, $this->_Password);
            $this->db->Active = true;
        }

        return $this->db;
    }
}
