<?php

namespace TF\Engine\Kernel;

use PHPExcel;
use PHPExcel_Cell;
use PHPExcel_Cell_DataType;
use PHPExcel_IOFactory;
use PHPExcel_Reader_Exception;
use PHPExcel_Shared_Date;
use PHPExcel_Style_Alignment;
use PHPExcel_Style_Color;
use PHPExcel_Style_Fill;
use Prado\TModule;
use Prado\Xml\TXmlDocument;

/**
 * Class ExportToExcelClass.
 *
 * @property PHPExcel $objPHPExcel
 */
class ExportToExcelClass extends TModule
{
    public const excelVersion = 'Excel2007';

    private $objPHPExcel;

    /**
     * Initialize the ExportToExcelClass properties from attributes in <module> tag.
     *
     * @param TXmlDocument xml configuration
     */
    public function init($xml)
    {
        parent::init($xml);
    }

    /**
     * Writes data into the excel file.
     *
     * @param array $data
     * @param array $headers
     * @param array $footers [ ['column' => value] ]
     * @param int $headersStartIndex
     * @param array $options
     *
     * @throws PHPExcel_Exception
     *
     * @return array array('file' => '')
     */
    public function export($data, $headers = [], $footers = [], $headersStartIndex = 0, $options = [])
    {
        $this->objPHPExcel = new PHPExcel();

        if (is_array($headers[0])) {
            foreach (array_reverse($headers) as $headerRow) {
                array_unshift($data, $headerRow);
            }
            $columnIndexes = $this->mapColumnIndex($headers[0]);
            $maxColumnIndex = $this->getMaxColumnIndex($headers[0]);
        } else {
            array_unshift($data, $headers);
            array_unshift($data, []);
            $columnIndexes = $this->mapColumnIndex($headers);
            $maxColumnIndex = $this->getMaxColumnIndex($headers);
        }

        for ($i = 0; $i < $headersStartIndex; $i++) {
            array_unshift($data, []);
        }

        // enable auto size
        //        PHPExcel_Shared_Font::setAutoSizeMethod(PHPExcel_Shared_Font::AUTOSIZE_METHOD_EXACT);

        $this->objPHPExcel->setActiveSheetIndex(0);
        $sheet = $this->objPHPExcel->getActiveSheet();

        if ($options['freezePane']) {
            $sheet->freezePane($options['freezePane']);
        }
        $data_count = count($data);
        for ($i = 0; $i < $data_count; $i++) {
            foreach ($columnIndexes as $key => $index) {
                $coordinate = $index . $i;
                $cellValue = $data[$i][$key];

                $sheet->setCellValueExplicit($coordinate, $cellValue, PHPExcel_Cell_DataType::TYPE_STRING);

                if (!empty($options['format']) && array_key_exists($key, $options['format'])) {
                    if ('custom' === $options['format'][$key]['type']) {
                        $sheet->getStyle($coordinate)->getNumberFormat()->setFormatCode($options['format'][$key]['value']);
                    } elseif ('date' === $options['format'][$key]['type']) {
                        if (!empty(strtotime($data[$i][$key]))) {
                            $dateArr = explode('.', $data[$i][$key]);
                            $time = gmmktime(0, 0, 0, $dateArr[1], $dateArr[0], $dateArr[2]);
                            $sheet->setCellValue($coordinate, PHPExcel_Shared_Date::PHPToExcel($time));
                            $sheet->getStyle($coordinate)->getNumberFormat()->setFormatCode($options['format'][$key]['value']);
                        }
                    } elseif ('number' === $options['format'][$key]['type']) {
                        $sheet->setCellValue($coordinate, $cellValue);
                        $sheet->getStyle($coordinate)->getNumberFormat()->setFormatCode($options['format'][$key]['value']);
                    } else {
                        $sheet->getStyle($coordinate)->getNumberFormat()->setFormatCode($options['format'][$key]['type']);
                        $sheet->setCellValueExplicit($coordinate, $cellValue, PHPExcel_Cell_DataType::TYPE_STRING);
                    }
                }

                if (!empty($options['wrapText']) && in_array($key, $options['wrapText'])) {
                    $sheet->getStyle($coordinate)->getAlignment()->setWrapText(true);
                }

                if (!empty($options['verticalAlignCenter'] && true == $options['verticalAlignCenter'])) {
                    $sheet->getStyle($coordinate)->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);
                }
            }

            if (!$options['noStyle'] && 0 == $i % 2 && $i > $headersStartIndex) {
                $sheet
                    ->getStyle('A' . $i . ':' . $maxColumnIndex . $i)
                    ->getFill()
                    ->setFillType(PHPExcel_Style_Fill::FILL_SOLID)
                    ->getStartColor()
                    ->setARGB('ededed');
            }

            $cellIterator = $sheet->getRowIterator()->current()->getCellIterator();
            //            $cellIterator->setIterateOnlyExistingCells(true);

            /** @var PHPExcel_Cell $cell */
            foreach ($cellIterator as $cell) {
                $sheet->getColumnDimension($cell->getColumn())->setAutoSize(true);
            }
        }

        if (!empty($options['mergeCells'])) {
            foreach ($options['mergeCells'] as $mergeCells) {
                $sheet->mergeCellsByColumnAndRow($mergeCells['startCol'], $mergeCells['startRow'], $mergeCells['endCol'], $mergeCells['endRow']);
            }
        }

        if (!empty($options['mergeRows'])) {
            foreach ($options['mergeRows'] as $mergeCells) {
                $range = $mergeCells['col'] . $mergeCells['startRow'] . ':' . $mergeCells['col'] . $mergeCells['endRow'];
                $sheet->mergeCells($range);
            }
        }

        if (!$options['noStyle']) {
            $this->applyHeaderFooterStyle($sheet, $headersStartIndex + 1, $maxColumnIndex);
        }

        if (!empty($options['tableBorders'])) {
            foreach ($options['tableBorders'] as $border) {
                $array = [
                    $border['borderPosition'] => [
                        'style' => $border['style'],
                        'color' => $border['color'],
                    ],
                ];
                $sheet->getStyle($border['startCol'] . $border['startRow'] . ':' . $border['endCol'] . ($border['endRow'] ?: count($data) - 1))->getBorders()->applyFromArray($array);
            }
        }

        if (!empty($options['bold'])) {
            foreach ($options['bold'] as $bold) {
                $sheet->getStyle($bold['startCol'] . $bold['startRow'] . ':' . $bold['endCol'] . $bold['endRow'])->getFont()->setBold(true);
            }
        }

        if (!empty($options['horizontalAlign'])) {
            foreach ($options['horizontalAlign'] as $align) {
                $sheet->getStyle($align['startCol'] . $align['startRow'] . ':' . $align['endCol'] . $align['endRow'])->getAlignment()->setHorizontal($align['align']);
            }
        }

        if (!empty($options['bgColor'])) {
            foreach ($options['bgColor'] as $bgColor) {
                $sheet->getStyle($bgColor['startCol'] . $bgColor['startRow'] . ':' . $bgColor['endCol'] . $bgColor['endRow'])
                    ->getFill()
                    ->setFillType(PHPExcel_Style_Fill::FILL_SOLID)
                    ->getStartColor()
                    ->setRGB('ebebeb');
            }
        }

        if (!empty($footers)) {
            $rowIndex = count($data);
            foreach ($footers as $key => $footer) {
                $this->appendFooters($sheet, $footer, $rowIndex + $key, $columnIndexes);
                if (!$options['noStyle']) {
                    $this->applyHeaderFooterStyle($sheet, $rowIndex + $key, $maxColumnIndex);
                }
            }
        }

        if ($options['addAutoFilter']) {
            $sheet->setAutoFilter($sheet->calculateWorksheetDimension());
        }
    }

    /**
     * Saves the file.
     *
     * @param string $filename
     *
     * @throws PHPExcel_Reader_Exception
     * @throws PHPExcel_Writer_Exception
     *
     * @return bool
     */
    public function saveFile($filename)
    {
        $objWriter = PHPExcel_IOFactory::createWriter($this->objPHPExcel, self::excelVersion);

        return $objWriter->save($filename);
    }

    /**
     * Applies information before the table; call after the export method.
     *
     * @param array $data
     */
    public function prependData($data, array $headers = [])
    {
        array_unshift($data, []);

        $columnIndexes = $this->mapColumnIndex($headers);

        $this->objPHPExcel->setActiveSheetIndex(0);
        $dataCount = count($data);
        for ($i = 0; $i < $dataCount; $i++) {
            foreach ($columnIndexes as $key => $index) {
                $this->objPHPExcel->getActiveSheet()->setCellValue($index . $i, $data[$i][$key]);
            }
        }
    }

    /**
     * @throws PHPExcel_Reader_Exception
     *
     * @return array
     */
    public function import($filename)
    {
        $objWriter = PHPExcel_IOFactory::createReader($this->objPHPExcel);
        $objWriter->load($filename);
        $rowIterator = $this->objPHPExcel->getActiveSheet()->getRowIterator();
        $data = [];
        foreach ($rowIterator as $row) {
            $cellIterator = $row->getCellIterator();
            foreach ($cellIterator as $cell) {
                $data[$row->getRowIndex()][$cell->getColumn()] = $cell->getCalculatedValue();
            }
        }

        return $data;
    }

    /**
     * Adds the footer rows to the data of the excel file.
     *
     * @param PHPExcel_Worksheet $sheet
     * @param array $footers
     * @param int $rowIndex the index of the row where the footer will be
     * @param array $columnIndexes indexes of the columns
     */
    private function appendFooters($sheet, $footers, $rowIndex, $columnIndexes)
    {
        foreach ($columnIndexes as $key => $index) {
            if (array_key_exists($key, $footers)) {
                $sheet->setCellValue($index . $rowIndex, $footers[$key]);
            }
        }
    }

    /**
     * Applies the header/footer style to a row.
     *
     * @param PHPExcel_Worksheet $sheet the worksheet currently being written
     * @param int $rowIndex the index of the row where the style will be aplied
     * @param array $maxColumnIndex the index of the last column where the style will be applied
     *
     * @throws PHPExcel_Exception
     */
    private function applyHeaderFooterStyle($sheet, $rowIndex, $maxColumnIndex)
    {
        $sheet->getStyle('A' . $rowIndex . ':' . $maxColumnIndex . $rowIndex)
            ->getFont()->getColor()->setARGB(PHPExcel_Style_Color::COLOR_WHITE);
        $sheet->getStyle('A' . $rowIndex . ':' . $maxColumnIndex . $rowIndex)
            ->getFont()->setBold(true);
        $sheet->getStyle('A' . $rowIndex . ':' . $maxColumnIndex . $rowIndex)->getFill()
            ->setFillType(PHPExcel_Style_Fill::FILL_SOLID)
            ->getStartColor()
            ->setARGB('a5a5a5');
    }

    /**
     * Maps column names to the column indexes.
     *
     * @param array $headers
     *
     * @return array
     */
    private function mapColumnIndex($headers)
    {
        $columnIndexes = [];
        $letters = range('A', 'Z');
        $headersCount = count($headers);
        $letterCount = count($letters);
        $finalLetters = $letters;
        for ($i = 0; $i < $letterCount; $i++) {
            $finalLetters[] = 'A' . $letters[$i];
        }
        for ($i = 0; $i < $letterCount; $i++) {
            $finalLetters[] = 'B' . $letters[$i];
        }
        for ($i = 0; $i < $letterCount; $i++) {
            $finalLetters[] = 'C' . $letters[$i];
        }

        $i = 0;
        foreach ($headers as $key => $value) {
            $columnIndexes[$key] = $finalLetters[$i];
            $i++;
        }

        return $columnIndexes;
    }

    /**
     * Returns the column on the most right.
     *
     * @param array $headers
     *
     * @return string
     */
    private function getMaxColumnIndex($headers)
    {
        $letters = range('A', 'Z');
        $headersCount = count($headers);
        $letterCount = count($letters);
        $finalLetters = $letters;
        for ($i = 0; $i < $letterCount; $i++) {
            $finalLetters[] = 'A' . $letters[$i];
        }
        for ($i = 0; $i < $letterCount; $i++) {
            $finalLetters[] = 'B' . $letters[$i];
        }
        for ($i = 0; $i < $letterCount; $i++) {
            $finalLetters[] = 'C' . $letters[$i];
        }

        return $finalLetters[count($headers) - 1];
    }
}
