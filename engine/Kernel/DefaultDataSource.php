<?php

namespace TF\Engine\Kernel;

use Prado\Data\TDataSourceConfig;
use Prado\TShellApplication;
use Prado\Xml\TXmlDocument;

/**
 * DefaultDataSource class file.
 *
 * <AUTHOR> Ltd. <<EMAIL>>
 *
 * @link http://www.devision.bg/
 *
 * @copyright Copyright &copy; 2008 Devision Ltd.
 */
/**
 * DefaultDataSource class.
 */
class DefaultDataSource extends TDataSourceConfig
{
    /**
     * Initalize the database connection properties from attributes in <database> tag.
     *
     * @param TXmlDocument xml configuration
     */
    public function init($xml)
    {
        if (!$this->getApplication() instanceof TShellApplication) {
            $db = $this->getDbConnection();
            $driver = constant('DEFAULT_DB_DRIVER');
            $host = constant('DEFAULT_DB_HOST');
            $port = constant('DEFAULT_DB_PORT');
            $database = constant('DEFAULT_DB_DATABASE');
            $username = constant('DEFAULT_DB_USERNAME');
            $password = constant('DEFAULT_DB_PASSWORD');
            $persistent = constant('DEFAULT_DB_PERSISTENT');
            $socket = constant('DEFAULT_DB_SOCKET');
        } else {
            $db = $this->getDbConnection();
            $driver = constant('DBLINK_DRIVER');
            $host = constant('DBLINK_HOST');
            $port = constant('DBLINK_PORT');
            $database = constant('DBLINK_DATABASE');
            $username = constant('DBLINK_USERNAME');
            $password = constant('DBLINK_PASSWORD');
            $persistent = constant('DBLINK_PERSISTENT');
            $socket = constant('DBLINK_SOCKET');
        }

        if ($socket) {
            $dsn = "{$driver}:unix_socket={$socket};dbname={$database}";
        } else {
            $dsn = "{$driver}:host={$host};port={$port};dbname={$database};";
        }
        // var_dump($dsn);
        // die();
        $db->ConnectionString = $dsn;
        $db->Username = $username;
        $db->Password = $password;

        parent::init($xml);
    }
}
