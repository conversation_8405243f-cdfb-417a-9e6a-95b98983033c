---
- hosts: "servers"
  tasks:
      - name: Add an Apt signing key, uses whichever key is at the URL
        ansible.builtin.apt_key:
          url: https://www.postgresql.org/media/keys/ACCC4CF8.asc
          state: present
      - name: Add PostgreSQL Repository
        apt_repository:
          repo: deb http://apt.postgresql.org/pub/repos/apt jammy-pgdg main
          state: present
      - name: Install postgresql-client-14
        apt:
          name: postgresql-client-14
          state: latest
          update_cache: true
      - name: Install postgresql-14
        apt:
          name: postgresql-14
          state: latest
          update_cache: true
      - name: Install postgresql-server-dev-14
        apt:
          name: postgresql-server-dev-14
          state: latest
          update_cache: true
      - name: Install postgresql-14-postgis-3
        apt:
          name: postgresql-14-postgis-3
          state: latest
          update_cache: true
      - name: Install required system packages
        apt:
          pkg:
            - apt-transport-https
            - ca-certificates
            - curl
            - software-properties-common
            - python3-pip
            - virtualenv
            - python3-setuptools
      - name: Install psycopg2-binary for Python
        pip:
          name: psycopg2-binary
      - name: Enable PostgreSQL service
        ansible.builtin.systemd:
          name: postgresql
          enabled: yes
          state: started
      - name: "Create app susi_main"
        postgresql_db:
          state: present
          name: susi_main
        become: yes
        become_user: postgres
      - name: "Create app db_admin"
        postgresql_db:
          state: present
          name: db_admin
        become: yes
        become_user: postgres
      - name: "Create app new_users_db"
        postgresql_db:
          state: present
          name: new_users_db
        become: yes
        become_user: postgres
      - name: "Grant db user access to app db"
        postgresql_privs:
          type: database
          database: susi_main
          roles: postgres
          grant_option: no
          privs: all
        become: yes
        become_user: postgres