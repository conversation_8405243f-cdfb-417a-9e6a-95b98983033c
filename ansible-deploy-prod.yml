---
- hosts: "servers"
  vars:
      GENERATING_MAP_FILES: "{{ lookup('env', 'GENERATING_MAP_FILES') }}"
  environment:
      DEFAULT_DB_USERNAME: "{{ lookup('env', 'DEFAULT_DB_USERNAME') }}"
      DEFAULT_DB_PASSWORD: "{{ lookup('env', 'DEFAULT_DB_PASSWORD') }}"
      DEFAULT_DB_HOST: "{{ lookup('env', 'DEFAULT_DB_HOST') }}"
      DEFAULT_DB_PORT: "{{ lookup('env', 'DEFAULT_DB_PORT') }}"
      DEFAULT_DB_DATABASE: "{{ lookup('env', 'DEFAULT_DB_DATABASE') }}"
      PGPASSWORD: "{{ lookup('env', 'DEFAULT_DB_PASSWORD') }}"
      DBLINK_DRIVER: "{{ lookup('env', 'DBLINK_DRIVER') }}"
      DBLINK_HOST: "{{ lookup('env', 'DBLINK_HOST') }}"
      DBLINK_PORT: "{{ lookup('env', 'DBLINK_PORT') }}"
      DBLINK_DATABASE: "{{ lookup('env', 'DBLINK_DATABASE') }}"
      DBLINK_USERNAME: "{{ lookup('env', 'DBLINK_USERNAME') }}"
      DBLINK_PASSWORD: "{{ lookup('env', 'DBLINK_PASSWORD') }}"
      WMS_SERVER: "{{ lookup('env', 'WMS_SERVER') }}"
      LOGIN3_WMS_SERVER: "{{ lookup('env', 'LOGIN3_WMS_SERVER') }}"
      RPI_SERVER: "{{ lookup('env', 'RPI_SERVER') }}"
      RPI_USERNAME: "{{ lookup('env', 'RPI_USERNAME') }}"
      RPI_PASSWORD: "{{ lookup('env', 'RPI_PASSWORD') }}"
      IMAGE_TAG_NAME: "{{ lookup('env', 'IMAGE_TAG_NAME') }}"
      DOCKERHUB_REPO_NAME: "{{ lookup('env', 'DOCKERHUB_REPO_NAME') }}"
      MAPSERVER_EXTERNAL_PORT: "{{ lookup('env', 'MAPSERVER_EXTERNAL_PORT') }}"
      CONTAINER_NAME: "{{ lookup('env', 'CONTAINER_NAME') }}"
      MAPCACHE_EXTERNAL_PORT: "{{ lookup('env', 'MAPCACHE_EXTERNAL_PORT') }}"
      API_EXTERNAL_PORT: "{{ lookup('env', 'API_EXTERNAL_PORT') }}"
      SITE_URL: "{{ lookup('env', 'SITE_URL') }}"
      SITE_BASE_HREF: "{{ lookup('env', 'SITE_BASE_HREF') }}"
      PHPMAILER_HOST: "{{ lookup('env', 'PHPMAILER_HOST') }}"
      PHPMAILER_PORT: "{{ lookup('env', 'PHPMAILER_PORT') }}"
      PHPMAILER_USERNAME: "{{ lookup('env', 'PHPMAILER_USERNAME') }}"
      PHPMAILER_PASSWORD: "{{ lookup('env', 'PHPMAILER_PASSWORD') }}"
      COMMON_SERVICES_API_URL: "{{ lookup('env', 'COMMON_SERVICES_API_URL') }}"
      GENERATING_MAP_FILES: "{{ lookup('env', 'GENERATING_MAP_FILES') }}"
      CSS_CUSTOM_THEME_NAME: "{{ lookup('env', 'CSS_CUSTOM_THEME_NAME') }}"
      WMS_SERVER_INTERNAL: "{{ lookup('env', 'WMS_SERVER_INTERNAL') }}"
      WAREHOUSE_API_URL: "{{ lookup('env', 'WAREHOUSE_API_URL') }}"
      MAPSERVER_CATCH_SEGV: "{{ lookup('env', 'MAPSERVER_CATCH_SEGV') }}"
      APACHE_RUN_GROUP: "{{ lookup('env', 'APACHE_RUN_GROUP') }}"
      APACHE_RUN_USER: "{{ lookup('env', 'APACHE_RUN_USER') }}"
      MS_DEBUGLEVEL: "{{ lookup('env', 'MS_DEBUGLEVEL') }}"
      MS_MAP_PATTERN: "{{ lookup('env', 'MS_MAP_PATTERN') }}"
      MIN_PROCESSES: "{{ lookup('env', 'MIN_PROCESSES') }}"
      MAX_PROCESSES: "{{ lookup('env', 'MAX_PROCESSES') }}"
      COPY_USER_SOURCE_DB_HOST: "{{ lookup('env', 'COPY_USER_SOURCE_DB_HOST') }}"
      COPY_USER_SOURCE_DB_PORT: "{{ lookup('env', 'COPY_USER_SOURCE_DB_PORT') }}"
      COPY_USER_SOURCE_DB_USER: "{{ lookup('env', 'COPY_USER_SOURCE_DB_USER') }}"
      COPY_USER_SOURCE_DB_PASS: "{{ lookup('env', 'COPY_USER_SOURCE_DB_PASS') }}"
      COPY_USER_COMMAND_SSH_HOST: "{{ lookup('env', 'COPY_USER_COMMAND_SSH_HOST') }}"
      COPY_USER_COMMAND_SSH_USER: "{{ lookup('env', 'COPY_USER_COMMAND_SSH_USER') }}"
      KEYKLOACK_LOGIN_REQUIRED: "{{ lookup('env', 'KEYKLOACK_LOGIN_REQUIRED') }}"
      KEYCLOAK_AUTH_SERVER_URL: "{{ lookup('env', 'KEYCLOAK_AUTH_SERVER_URL') }}"
      KEYCLOAK_REALM: "{{ lookup('env', 'KEYCLOAK_REALM') }}"
      KEYCLOAK_CLIENT_ID: "{{ lookup('env', 'KEYCLOAK_CLIENT_ID') }}"
      KEYCLOAK_CLIENT_SECRET: "{{ lookup('env', 'KEYCLOAK_CLIENT_SECRET') }}"
      KEYCLOAK_REDIRECT_URI: "{{ lookup('env', 'KEYCLOAK_REDIRECT_URI') }}"
      KEYCLOAK_LOGOUT_REDIRECT_URI: "{{ lookup('env', 'KEYCLOAK_LOGOUT_REDIRECT_URI') }}"
      KEYCLOAK_ALGORYTHM: "{{ lookup('env', 'KEYCLOAK_ALGORYTHM') }}"
      CMS_API_URL: "{{ lookup('env', 'CMS_API_URL') }}"
      GEOSCAN_CMS_BASE_URI: "{{ lookup('env', 'GEOSCAN_CMS_BASE_URI') }}"
      KAIS_SESSION_ID: "{{ lookup('env', 'KAIS_SESSION_ID') }}"
      KAIS_CA_TOKEN: "{{ lookup('env', 'KAIS_CA_TOKEN') }}"
      APP_ENV: "{{ lookup('env', 'APP_ENV') }}"
      GEOSCAN_APP_URL: "{{ lookup('env', 'GEOSCAN_APP_URL') }}"
      MAIN_NAVIGATION_INSTANCE: "{{ lookup('env', 'MAIN_NAVIGATION_INSTANCE') }}"
      LEGACY_MODE: "{{ lookup('env', 'LEGACY_MODE') }}"
      KEYKLOACK_KVS_STORE_AUTH_SERVER_URL: "{{ lookup('env', 'KEYKLOACK_KVS_STORE_AUTH_SERVER_URL') }}"
      KEYKLOACK_KVS_STORE_CLIENT_ID: "{{ lookup('env', 'KEYKLOACK_KVS_STORE_CLIENT_ID') }}"
      KEYKLOACK_KVS_STORE_CLIENT_SECRET: "{{ lookup('env', 'KEYKLOACK_KVS_STORE_CLIENT_SECRET') }}"
      KEYKLOACK_KVS_STORE_REALM: "{{ lookup('env', 'KEYKLOACK_KVS_STORE_REALM') }}"
      KVS_STORE_URL: "{{ lookup('env', 'KVS_STORE_URL') }}"
      KVS_STORE_CALLBACK_URL: "{{ lookup('env', 'KVS_STORE_CALLBACK_URL') }}"
      KEYCLOAK_M2M_CLIENT_ID: "{{ lookup('env', 'KEYCLOAK_M2M_CLIENT_ID') }}"
      KEYCLOAK_M2M_CLIENT_SECRET: "{{ lookup('env', 'KEYCLOAK_M2M_CLIENT_SECRET') }}"
      CSV2XLS_PATH: "{{ lookup('env', 'CSV2XLS_PATH') }}"
      ALARMS_MAIL: "{{ lookup('env', 'ALARMS_MAIL') }}"

  tasks:
      - name: Install apache httpd  (state=present is optional)
        apt:
            name: apache2
            state: present
      - name: Enable the Apache2 module proxy
        community.general.apache2_module:
            state: present
            name: proxy
      - name: Enable the Apache2 module proxy_http
        community.general.apache2_module:
            state: present
            name: proxy_http
      - name: Enable the Apache2 module rewrite
        community.general.apache2_module:
            state: present
            name: rewrite
      - name: Enable the Apache2 module headers
        community.general.apache2_module:
            state: present
            name: headers
      #    - name: Copy file with owner and permissions
      #      ansible.builtin.copy:
      #        src: .ansible/apache-template-port80.conf
      #        dest: /etc/apache2/sites-enabled/apache-template-port80.conf
      - name: Install required system packages
        apt:
            pkg:
                - apt-transport-https
                - ca-certificates
                - curl
                - software-properties-common
                - python3-pip
                - virtualenv
                - python3-setuptools
            state: latest
            update_cache: false

      #      - name: Add Docker GPG apt Key
      #        apt_key:
      #            url: https://download.docker.com/linux/ubuntu/gpg
      #            state: present

      #      - name: Add Docker Repository 24
      #        apt_repository:
      #            repo: deb [arch=amd64 signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu noble stable
      #            state: present
      #        when: ansible_distribution_major_version == "24"

      - name: Add Docker Repository
        apt_repository:
            repo: deb https://download.docker.com/linux/ubuntu jammy stable
            state: present
        when: ansible_distribution_major_version == "22"

      - name: Add Docker Repository
        apt_repository:
            repo: deb https://download.docker.com/linux/ubuntu focal stable
            state: present
        when: ansible_distribution_major_version == "20"

      - name: Install docker-ce
        apt:
            name: docker-ce
            state: latest
            update_cache: false

      - name: Install docker-compose-plugin
        apt:
            name: docker-compose-plugin
            state: latest
            update_cache: false

      - name: Check if the OS is Ubuntu and major version is older than 24
        set_fact:
            is_old_ubuntu: "{{ (ansible_distribution == 'Ubuntu') and (ansible_distribution_major_version | int < 24) }}"

      - name: Install Docker Module for Python
        pip:
            name: docker
        when: is_old_ubuntu

      - name: Install pip docker-compose
        pip:
            name: docker-compose
        when: is_old_ubuntu

      - name: Create .docker directory
        file:
            path: /root/.docker
            state: directory
            mode: 0700
            group: root
            owner: root

      - name: Create a network
        community.docker.docker_network:
            name: technofarm-net

      - name: Log in
        community.docker.docker_login:
            username: "{{ lookup('env', 'DOCKER_REGISTRY_USERNAME') }}"
            password: "{{ lookup('env', 'DOCKER_REGISTRY_PASSWORD') }}"

      - name: Docker pull technofarm
        community.docker.docker_image:
            name: technofarm/technofarm:{{ lookup('env', 'IMAGE_TAG_NAME') }}
            source: pull
            force_source: yes

      - name: Docker pull cron
        community.docker.docker_image:
            name: technofarm/technofarm-cron:{{ lookup('env', 'IMAGE_TAG_NAME') }}
            source: pull
            force_source: yes

      - name: Docker pull gdal
        community.docker.docker_image:
            name: technofarm/technofarm-gdal
            source: pull
            force_source: yes

      - name: Docker pull mapserver
        community.docker.docker_image:
            name: technofarm/technofarm-mapserver:{{ lookup('env', 'IMAGE_TAG_NAME') }}
            source: pull
            force_source: yes

      - name: Docker pull nginx
        community.docker.docker_image:
            name: technofarm/technofarm-nginx:{{ lookup('env', 'IMAGE_TAG_NAME') }}
            source: pull
            force_source: yes

      - name: Copy docker-compose.prod.tf.yml
        copy:
            src: docker-compose.prod.yml
            dest: ~/docker-compose.prod.tf.yml
            mode: 0644

      - name: Copy env.example
        copy:
            src: .env.example
            dest: .env
            mode: 0644

      - name: DOWN the containers
        community.docker.docker_compose:
            project_src: ~/
            project_name: "{{ lookup('env', 'CONTAINER_NAME') }}"
            state: absent
            files:
                - docker-compose.prod.tf.yml

      - name: Delete Volume crons
        community.docker.docker_volume:
            name: "{{ lookup('env', 'CONTAINER_NAME') }}-crons"
            state: absent

      - name: Delete Volume maps
        community.docker.docker_volume:
            name: "{{ lookup('env', 'CONTAINER_NAME') }}-maps"
            state: absent
        when: GENERATING_MAP_FILES == 'yes'

      - name: Delete Volume {{ lookup('env', 'CONTAINER_NAME') }}-app
        community.docker.docker_volume:
            name: "{{ lookup('env', 'CONTAINER_NAME') }}-app"
            state: absent

      - name: Create volume {{ lookup('env', 'CONTAINER_NAME') }}-key-data
        community.docker.docker_volume:
            name: "{{ lookup('env', 'CONTAINER_NAME') }}-key-data"

      - name: Create volume {{ lookup('env', 'CONTAINER_NAME') }}-mapcache-config
        community.docker.docker_volume:
            name: "{{ lookup('env', 'CONTAINER_NAME') }}-mapcache-config"

      - name: Create volume {{ lookup('env', 'CONTAINER_NAME') }}-mapcache
        community.docker.docker_volume:
            name: "{{ lookup('env', 'CONTAINER_NAME') }}-mapcache"

      - name: Create volume {{ lookup('env', 'CONTAINER_NAME') }}-user-files
        community.docker.docker_volume:
            name: "{{ lookup('env', 'CONTAINER_NAME') }}-user-files"

      - name: Create volume {{ lookup('env', 'CONTAINER_NAME') }}-logs
        community.docker.docker_volume:
            name: "{{ lookup('env', 'CONTAINER_NAME') }}-logs"

      - name: UP the containers
        community.docker.docker_compose:
            project_src: ~/
            project_name: "{{ lookup('env', 'CONTAINER_NAME') }}"
            build: yes
            recreate: always
            dependencies: no
            files:
                - docker-compose.prod.tf.yml

      - name: Copy .env in "{{ lookup('env', 'CONTAINER_NAME') }}" container
        command: docker cp .env "{{ lookup('env', 'CONTAINER_NAME') }}":/var/www/html/app

      - name: Copy .env in "{{ lookup('env', 'CONTAINER_NAME') }}-crontab" container
        command: docker cp .env "{{ lookup('env', 'CONTAINER_NAME') }}"-crontab:/var/www/html/app

      - name: Generate folder structure
        community.docker.docker_container_exec:
            container: "{{ lookup('env', 'CONTAINER_NAME') }}"
            command: /bin/sh -c "php run.php tf:init_folder_structure"

      - name: Generate map files - {{ lookup('env', 'GENERATING_MAP_FILES') }}
        community.docker.docker_container_exec:
            container: "{{ lookup('env', 'CONTAINER_NAME') }}"
            command: /bin/sh -c "php run.php tf:regenerate_map_files -f"
        when: GENERATING_MAP_FILES == 'yes'

      - name: MapServer change permission
        community.docker.docker_container_exec:
            container: "{{ lookup('env', 'CONTAINER_NAME') }}-mapcache"
            command: /bin/sh -c "chmod -R 777 /var/sig/tiles"

      - name: Image Prune
        community.docker.docker_prune:
            images: yes

      - name: Delete docker-compose.prod.yml
        ansible.builtin.file:
            path: ~/docker-compose.prod.tf.yml
            state: absent
