version: "3.7"

services:
    tf-php:
        image: technofarm/technofarm:${IMAGE_TAG_NAME}
        container_name: ${CONTAINER_NAME}
        dns:
            - 8.8.8.8
        user: "1000:1000"
        volumes:
            - app:/var/www/html/app
            - maps:/var/www/html/app/maps/
            - keydata:/var/www/html/app/config/jwt
            - tf-mapcache-data:/var/www/html/app/static_maps
            - tf-mapcache-config:/var/www/html/app/.docker/mapcache
            - tf-user-files:/var/www/html/app/public/files
            - tf-logs:/var/www/html/app/logs/group_logs
        networks:
            - technofarm-net
            - geoscan-net
        restart: always
        healthcheck:
            test: "exit 0"
        environment:
            - DEFAULT_DB_USERNAME
            - DEFAULT_DB_PASSWORD
            - DEFAULT_DB_HOST
            - DEFAULT_DB_PORT
            - DEFAULT_DB_DATABASE
            - PGPASSWORD
            - DBLINK_DRIVER
            - DBLINK_HOST
            - DBLINK_PORT
            - DBLINK_DATABASE
            - DBLINK_USERNAME
            - DBLINK_PASSWORD
            - WMS_SERVER
            - LOGIN3_WMS_SERVER
            - RPI_SERVER
            - RPI_USERNAME
            - RPI_PASSWORD
            - SITE_URL
            - SITE_BASE_HREF
            - PHPMAILER_HOST
            - PHPMAILER_PORT
            - PHPMAILER_USERNAME
            - PHPMAILER_PASSWORD
            - COMMON_SERVICES_API_URL
            - CSS_CUSTOM_THEME_NAME
            - WMS_SERVER_INTERNAL
            - WAREHOUSE_API_URL
            - COPY_USER_SOURCE_DB_HOST
            - COPY_USER_SOURCE_DB_PORT
            - COPY_USER_SOURCE_DB_USER
            - COPY_USER_SOURCE_DB_PASS
            - COPY_USER_COMMAND_SSH_HOST
            - COPY_USER_COMMAND_SSH_USER
            - KEYKLOACK_LOGIN_REQUIRED
            - KEYCLOAK_AUTH_SERVER_URL
            - KEYCLOAK_REALM
            - KEYCLOAK_CLIENT_ID
            - KEYCLOAK_CLIENT_SECRET
            - KEYCLOAK_REDIRECT_URI
            - KEYCLOAK_LOGOUT_REDIRECT_URI
            - KEYCLOAK_ALGORYTHM
            - CMS_API_URL
            - GEOSCAN_CMS_BASE_URI
            - KAIS_SESSION_ID
            - KAIS_CA_TOKEN
            - APP_ENV
            - GEOSCAN_APP_URL
            - MAIN_NAVIGATION_INSTANCE
            - LEGACY_MODE
            - KEYKLOACK_KVS_STORE_AUTH_SERVER_URL
            - KEYKLOACK_KVS_STORE_CLIENT_ID
            - KEYKLOACK_KVS_STORE_CLIENT_SECRET
            - KEYKLOACK_KVS_STORE_REALM
            - KVS_STORE_URL
            - KVS_STORE_CALLBACK_URL
            - KEYCLOAK_M2M_CLIENT_ID
            - KEYCLOAK_M2M_CLIENT_SECRET
            - CONTAINER_NAME
            - CSV2XLS_PATH
            - ALARMS_MAIL
    tf-crontab:
        image: technofarm/technofarm-cron:${IMAGE_TAG_NAME}
        container_name: ${CONTAINER_NAME}-crontab
        dns:
            - 8.8.8.8
        depends_on:
            - tf-php
        volumes:
            - app:/var/www/html/app
            - tf-user-files:/var/www/html/app/public/files
            - tf-logs:/var/www/html/app/logs/group_logs
            - maps:/var/www/html/app/maps/
        networks:
            - technofarm-net
            - geoscan-net
        restart: always
        healthcheck:
            test: "exit 0"
        environment:
            - DEFAULT_DB_USERNAME
            - DEFAULT_DB_PASSWORD
            - DEFAULT_DB_HOST
            - DEFAULT_DB_PORT
            - DEFAULT_DB_DATABASE
            - PGPASSWORD
            - DBLINK_DRIVER
            - DBLINK_HOST
            - DBLINK_PORT
            - DBLINK_DATABASE
            - DBLINK_USERNAME
            - DBLINK_PASSWORD
            - WMS_SERVER
            - LOGIN3_WMS_SERVER
            - RPI_SERVER
            - RPI_USERNAME
            - RPI_PASSWORD
            - SITE_URL
            - SITE_BASE_HREF
            - PHPMAILER_HOST
            - PHPMAILER_PORT
            - PHPMAILER_USERNAME
            - PHPMAILER_PASSWORD
            - COMMON_SERVICES_API_URL
            - CSS_CUSTOM_THEME_NAME
            - WMS_SERVER_INTERNAL
            - WAREHOUSE_API_URL
            - COPY_USER_SOURCE_DB_HOST
            - COPY_USER_SOURCE_DB_PORT
            - COPY_USER_SOURCE_DB_USER
            - COPY_USER_SOURCE_DB_PASS
            - COPY_USER_COMMAND_SSH_HOST
            - COPY_USER_COMMAND_SSH_USER
            - KEYKLOACK_LOGIN_REQUIRED
            - KEYCLOAK_AUTH_SERVER_URL
            - KEYCLOAK_REALM
            - KEYCLOAK_CLIENT_ID
            - KEYCLOAK_CLIENT_SECRET
            - KEYCLOAK_REDIRECT_URI
            - KEYCLOAK_LOGOUT_REDIRECT_URI
            - KEYCLOAK_ALGORYTHM
            - CMS_API_URL
            - GEOSCAN_CMS_BASE_URI
            - KAIS_SESSION_ID
            - KAIS_CA_TOKEN
            - APP_ENV
            - GEOSCAN_APP_URL
            - MAIN_NAVIGATION_INSTANCE
            - LEGACY_MODE
            - KEYKLOACK_KVS_STORE_AUTH_SERVER_URL
            - KEYKLOACK_KVS_STORE_CLIENT_ID
            - KEYKLOACK_KVS_STORE_CLIENT_SECRET
            - KEYKLOACK_KVS_STORE_REALM
            - KVS_STORE_URL
            - KVS_STORE_CALLBACK_URL
            - KEYCLOAK_M2M_CLIENT_ID
            - KEYCLOAK_M2M_CLIENT_SECRET
            - CONTAINER_NAME
            - ALARMS_MAIL
    tf-mapserver:
        image: technofarm/technofarm-mapserver:${IMAGE_TAG_NAME}
        container_name: ${CONTAINER_NAME}-mapserver
        environment:
            - MS_MAP_PATTERN=${MS_MAP_PATTERN}
            - MS_DEBUGLEVEL=${MS_DEBUGLEVEL:-0}
            - MIN_PROCESSES=${MIN_PROCESSES:-1}
            - MAX_PROCESSES=${MAX_PROCESSES:-2}
            - APACHE_RUN_USER=${APACHE_RUN_USER:-appuser}
            - APACHE_RUN_GROUP=${APACHE_RUN_GROUP:-appgroup}
            - MAPSERVER_CATCH_SEGV=${MAPSERVER_CATCH_SEGV:-0}
        dns:
            - 8.8.8.8
        networks:
            - technofarm-net
            - geoscan-net
        ports:
            - ${MAPSERVER_EXTERNAL_PORT}:8080
        volumes:
            - maps:/var/www/html/app/maps/
            - tf-mapcache-data:/var/www/html/app/static_maps
        restart: always
        healthcheck:
            test: "exit 0"
    tf-mapcache:
        image: camptocamp/mapcache:1.6
        container_name: ${CONTAINER_NAME}-mapcache
        dns:
            - 8.8.8.8
        volumes:
            - tf-mapcache-config:/etc/mapcache
            - tf-mapcache-config:/var/sig/tiles
            - tf-mapcache-data:/var/www/html/app/static_maps
        networks:
            - technofarm-net
            - geoscan-net
        restart: always
        ports:
            - ${MAPCACHE_EXTERNAL_PORT}:80
        healthcheck:
            test: "exit 0"
    tf-web:
        container_name: tf-${CONTAINER_NAME}-nginx
        image: technofarm/technofarm-nginx:${IMAGE_TAG_NAME}
        working_dir: /etc/nginx
        environment:
            - CONTAINER_NAME
            - MAPSERVER_EXTERNAL_PORT=${MAPSERVER_EXTERNAL_PORT}
            - MAPCACHE_EXTERNAL_PORT=${MAPCACHE_EXTERNAL_PORT}
        ports:
            - ${API_EXTERNAL_PORT}:80
        volumes:
            - app:/var/www/html/app
            - tf-user-files:/var/www/html/app/public/files
        networks:
            - technofarm-net
            - geoscan-net
        depends_on:
            - tf-php
            - tf-mapserver
        restart: always
        healthcheck:
            test: "exit 0"
    tf-gdal:
        image: technofarm/technofarm-gdal
        networks:
            - technofarm-net
            - geoscan-net
        container_name: tf-${CONTAINER_NAME}-gdal
        profiles:
            - tools
networks:
    technofarm-net:
        name: technofarm-net
        external: true
    geoscan-net:
        name: geoscan-net
        external: true

volumes:
    app:
        name: ${CONTAINER_NAME}-app
    maps:
        name: ${CONTAINER_NAME}-maps
    keydata:
        name: ${CONTAINER_NAME}-key-data
        external: true
    crons:
        name: ${CONTAINER_NAME}-crons
    tf-mapcache-config:
        name: ${CONTAINER_NAME}-mapcache-config
        external: true
    tf-mapcache-data:
        name: ${CONTAINER_NAME}-mapcache
        external: true
    tf-user-files:
        name: ${CONTAINER_NAME}-user-files
        external: true
    tf-logs:
        name: ${CONTAINER_NAME}-logs
        external: true
