version: '3.7'

services:
  tf-php:
    image: technofarm/technofarm:${IMAGE_TAG_NAME}
    container_name: ${CONTAINER_NAME}
    dns:
      - *******
    user: "1000:1000"
    volumes:
      - app:/var/www/html/app
      - maps:/var/www/html/app/maps/
      - keydata:/var/www/html/app/config/jwt
      - tf-mapcache-data:/var/www/html/app/static_maps
      - tf-mapcache-config:/var/www/html/app/.docker/mapcache
      - tf-user-files:/var/www/html/app/public/files
      - tf-logs:/var/www/html/app/logs/group_logs
    networks:
      - technofarm-net
    restart: always
    healthcheck:
      test: "exit 0"
    environment:
      - DEFAULT_DB_USERNAME
      - DEFAULT_DB_PASSWORD
      - DEFAULT_DB_HOST
      - DEFAULT_DB_PORT
      - DEFAULT_DB_DATABASE
      - PGPASSWORD
      - DBLINK_DRIVER
      - DBLINK_HOST
      - DBLINK_PORT
      - DBLINK_DATABASE
      - DBLINK_USERNAME
      - DBLINK_PASSWORD
      - WMS_SERVER
      - LOGIN3_WMS_SERVER
      - RPI_SERVER
      - RPI_USERNAME
      - RPI_PASSWORD
      - SITE_URL
      - PHPMAILER_HOST
      - PHPMAILER_PORT
      - PHPMAILER_USERNAME
      - PHPMAILER_PASSWORD
      - COMMON_SERVICES_API_URL
      - CSS_CUSTOM_THEME_NAME
      - WMS_SERVER_INTERNAL
      - WAREHOUSE_API_URL
      - ALARMS_MAIL

  tf-crontab:
    image: technofarm/technofarm-cron:${IMAGE_TAG_NAME}
    container_name: ${CONTAINER_NAME}-crontab
    dns:
      - *******
    depends_on:
      - tf-php
    volumes:
      - app:/var/www/html/app
      - tf-user-files:/var/www/html/app/public/files
      - tf-logs:/var/www/html/app/logs/group_logs
      - maps:/var/www/html/app/maps/
    networks:
      - technofarm-net
    restart: always
    healthcheck:
      test: "exit 0"
    environment:
      - DEFAULT_DB_USERNAME
      - DEFAULT_DB_PASSWORD
      - DEFAULT_DB_HOST
      - DEFAULT_DB_PORT
      - DEFAULT_DB_DATABASE
      - PGPASSWORD
      - DBLINK_DRIVER
      - DBLINK_HOST
      - DBLINK_PORT
      - DBLINK_DATABASE
      - DBLINK_USERNAME
      - DBLINK_PASSWORD
      - WMS_SERVER
      - LOGIN3_WMS_SERVER
      - RPI_SERVER
      - RPI_USERNAME
      - RPI_PASSWORD
  tf-mapserver:
    image: technofarm/technofarm-mapserver:${IMAGE_TAG_NAME}
    container_name: ${CONTAINER_NAME}-mapserver
    dns:
      - *******
    networks:
      - technofarm-net
    ports:
      - ${MAPSERVER_EXTERNAL_PORT}:8080
    volumes:
      - maps:/var/www/html/app/maps/
      - tf-mapcache-data:/var/www/html/app/static_maps
    restart: always
    healthcheck:
      test: "exit 0"
  tf-mapcache:
    image: camptocamp/mapcache:1.6
    container_name: ${CONTAINER_NAME}-mapcache
    dns:
      - *******
    volumes:
      - tf-mapcache-config:/etc/mapcache
      - tf-mapcache-config:/var/sig/tiles
      - tf-mapcache-data:/var/www/html/app/static_maps
    networks:
      - technofarm-net
    restart: always
    ports:
      - ${MAPCACHE_EXTERNAL_PORT}:80
    healthcheck:
      test: "exit 0"
  tf-web:
    container_name: ${CONTAINER_NAME}-nginx
    image: technofarm/technofarm-nginx:${IMAGE_TAG_NAME}
    working_dir: /etc/nginx
    environment:
      - CONTAINER_NAME=${CONTAINER_NAME}
      - MAPSERVER_EXTERNAL_PORT=${MAPSERVER_EXTERNAL_PORT}
      - MAPCACHE_EXTERNAL_PORT=${MAPCACHE_EXTERNAL_PORT}
    ports:
      - ${API_EXTERNAL_PORT}:80
    volumes:
      - app:/var/www/html/app
      - tf-user-files:/var/www/html/app/public/files
    networks:
      - technofarm-net
    depends_on:
      - tf-php
      - tf-mapserver
    restart: always
    healthcheck:
      test: "exit 0"
  tf-gdal:
    image: technofarm/technofarm-gdal
    networks:
      - technofarm-net
    container_name: ${CONTAINER_NAME}-gdal
    profiles:
      - tools
networks:
  technofarm-net:
    name: technofarm-net
    external: true

volumes:
  app:
    name: ${CONTAINER_NAME}-app
  maps:
    name: ${CONTAINER_NAME}-maps
  keydata:
    name: ${CONTAINER_NAME}-key-data
    external: true
  crons:
    name: ${CONTAINER_NAME}-crons
  tf-mapcache-config:
    name: tf-mapcache-config
    external: true
  tf-mapcache-data:
    name: tf-mapcache
    external: true
  tf-user-files:
    name: tf-user-files
    external: true
  tf-logs:
    name: tf-logs
    external: true