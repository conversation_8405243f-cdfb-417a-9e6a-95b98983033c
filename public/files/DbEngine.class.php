<?php
class DbEngine {
	private $db;
	private $_connectionString;
	private $_Driver;
	private $_Host;
	private $_Port;
	private $_Username;
	private $_Password;
	private $_Database;
	private $_Persistent = false;
	 
		
	public function init($config){
		if (!$this->Driver){
			throw new Exception('Missing param: Driver');
		}
		if (!$this->Host){
			throw new Exception('Missing param: Host');
		}
		if (!$this->Username){
			throw new Exception('Missing param: Username');
		}
		if (!$this->Password){
			throw new Exception('Missing param: Password');        
		}
		if (!$this->Database){
			throw new Exception('Missing param: Database');        
		}
		parent::init($config);
	}
		
	//PHP magic function.
	//This method will pass all method calls to PDO class/library.
	public function __call($method, $params){
		$conn = $this->getDatabaseConnection();
		return call_user_func_array(array($conn, $method), $params);
	}
	
	private function getDatabaseConnection()
	{		
		if($this->db === null){
			$dsn = "$this->_Driver:host=$this->_Host;port=$this->_Port;dbname=$this->_Database;";		
			$this->db = new PDO($dsn,$this->_Username,$this->_Password);
			$this->db->Active = true;
			$this->db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
		}
		return $this->db;
	}

	//Getter and setters for params.
	public function getDriver(){
		return $this->_Driver;
	}
	public function setDriver($value){
		$this->_Driver = $value;
	}
	public function getHost(){
		return $this->_Host;
	}
	public function setHost($value){
		$this->_Host = $value;
	}
	public function getPort(){
		return $this->_Port;
	}
	public function setPort($value){
		$this->_Port = $value;
	}
	public function getUsername(){
		return $this->_Username;
	}
	public function setUsername($value){
		$this->_Username = $value;
	}
	public function getPassword(){
		return $this->_Password;
	}
	public function setPassword($value){
		$this->_Password = $value;
	}
	public function getDatabase(){
		return $this->_Database;
	}
	public function setDatabase($value){
		$this->_Database = $value;
	}
	public function getPersistent(){
		return $this->_Persistent;
	}
	public function setPersistent($value){
		$this->_Persistent = $value;
	}
}

?>