<?php
/**
 * Shell index file.
 *
 * <AUTHOR> Ltd. <<EMAIL>>
 * @link http://www.devision.bg/
 * @copyright Copyright &copy; 2008 Devision Ltd.
 */

use Plugins\Core\UserDb\UserDbController;
use Prado\Prado;

if(isset($_GET['sid']))
{
    @session_id($_GET['sid']);
    @session_start();
}

if(session_id() == '' || !isset($_SESSION)){
    @session_start();
}

/**
 * @param $plugin
 * @return mixed
 *
 * @DEPRACATED
 * @DEPRECATION NOTE fuction should not be used. Plugin should be instantiated  with new $plugin();
 */
function getPluginInstance($plugin)
{
	preg_match('/^(.*)\.([^\.]+)$/', $plugin, $split);
	$pluginPath = $split[1];
	$pluginName = $split[2];
	$controllerName = $pluginName.'Controller';
	
	Prado::using($plugin.'.*');
	Prado::using($plugin.'.conf');
	return new $controllerName($pluginName);
}

/**
 * @param $database
 * @return UserDbController
 *
 * @DEPRACATED
 * @DEPRECATION NOTE fuction should not be used. Plugin should be instantiated  with new $plugin();
 */
function getPluginInstanceUserDb($database)
{
    Prado::using('Plugins.Core.UserDb.*');
    Prado::using('Plugins.Core.UserDb.conf');           
    return new UserDbController($database);
}

/**
 * @param $module
 * @param $database
 * @return mixed
 *
 * @DEPRACATED
 * @DEPRECATION NOTE fuction should not be used. Plugin should be instantiated  with new $plugin();
 */
function getPluginInstanceModuleUserDb($module, $database) {
	$controller = 'UserDb' . $module . 'Controller';
	Prado::using('Plugins.Core.UserDb' . $module . '.*');
	return new $controller($database);
}

$prefix = '';

mb_internal_encoding('utf-8');
mb_regex_encoding('utf-8');

include '../../vendor/autoload.php';

$dotenv = new Dotenv\Dotenv(__DIR__.'/../..');
$dotenv->load();

$dir = realpath(dirname(__FILE__)) . '/../../';
require_once($dir.'config/global.config.php');
require_once($dir.'config/templates.config.php');

if (file_exists($prefix.FRAMEWORK_PATH)) require_once($prefix.FRAMEWORK_PATH);
elseif (file_exists(SITE_PATH.FRAMEWORK_PATH)) require_once(SITE_PATH.FRAMEWORK_PATH);
else require_once(FRAMEWORK_PATH);

$application=new TShellApplication(SITE_PATH.'/protected/application.xml');
$application->run();

?>