<?php

use TF\Engine\Plugins\Core\UserDbAreaTypes\UserDbAreaTypesController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\UserDbPlotCategoriesType\UserDbPlotCategoriesTypeController;
use TF\Engine\Plugins\Core\UserDbPlots\UserDbPlotsController;

require_once 'shell.php';

include SITE_PATH . 'lib/fpdf16/fpdf.php';

$PlotsController = new UserDbPlotsController($this->User->Database);
$RentsController = getPluginInstance('Plugins.Core.Rents'); // Does not exist
$LeasesController = getPluginInstance('Plugins.Core.Leases'); // Does not exist
$NeighboursController = getPluginInstance('Plugins.Core.Neighbours'); // Does not exist
$SeedsController = getPluginInstance('Plugins.Core.Seeds'); // Does not exist
$DeedsController = getPluginInstance('Plugins.Core.Deeds'); // Does not exist
$OwnersController = new UserDbOwnersController($this->User->Database);
$UserDbPlotCategoriesTypeController = new UserDbPlotCategoriesTypeController($this->User->Database);
$UserDbAreaTypesController = new UserDbAreaTypesController($this->User->Database);

// $PlotsController = getPluginInstance('Plugins.Core.Plots');
// $RentsController = getPluginInstance('Plugins.Core.Rents');
// $LeasesController = getPluginInstance('Plugins.Core.Leases');
// $OwnersController = getPluginInstance('Plugins.Core.Owners');
// $NeighboursController = getPluginInstance('Plugins.Core.Neighbours');

$ekatte = (int) $_GET['ekatte'];
$todate = trim($_GET['todate']);
$fromdate = trim($_GET['fromdate']);
$contract = $_GET['contract'];
$contragent = $_GET['contragent'];
$egn = $_GET['egn'];

$settings = [];
$settings['ekatte'] = $ekatte;
$settings['todate'] = '' == $todate ? '' : strftime('%Y-%m-%d', strtotime($todate));
$settings['fromdate'] = '' == $fromdate ? '' : strftime('%Y-%m-%d', strtotime($fromdate));
$settings['contract'] = $contract;
$settings['contragent'] = $contragent;
$settings['egn'] = $egn;

$data = $RentsController->getPolygonsRentsFilter($settings);

$pdf = new FPDF('P');
$pdf->AddFont('arial', '', 'verdana.php');
$pdf->AddPage('');
$pdf->SetFont('arial', '', 18);
$text = mb_convert_encoding('Справка НАЕМИ ', 'Windows-1251');
$pdf->Cell(0, 10, $text, 0, 1, 'C');

$pdf->SetFont('arial', '', 10);
$text = mb_convert_encoding('' . date('d.m.Y H:i:s'), 'Windows-1251');
$pdf->Cell(0, 5, $text, 0, 1, 'C');
$pdf->Ln();

$pdf->SetFillColor(0, 74, 113);
$pdf->SetDrawColor(255, 255, 255);
$pdf->SetTextColor(255, 255, 255);
$pdf->Cell(0, 0.1, ' ', 0, 1, 'L', true);
$pdf->SetFillColor(255, 255, 255);
$pdf->SetDrawColor(255, 255, 255);
$pdf->SetTextColor(0, 0, 0);
$pdf->Ln(2);

for ($i = 0; $i < count($data); $i++) {
    addPlotById($data[$i]['id']);
}

$pdf->Output('report.pdf', 'D');

function addPlotById($id)
{
    global $pdf;
    global $PlotsController, $RentsController, $LeasesController, $OwnersController, $NeighboursController, $SeedsController, $DeedsController, $UserDbAreaTypesController, $UserDbPlotCategoriesTypeController;

    $plotData = $PlotsController->getPolygonsByPlotId($id);

    $way_to_use = $plotData['way_to_use'] ? $UserDbAreaTypesController->getNtpTitle($plotData['way_to_use']) : '-';
    $is_own = $GLOBALS['Plots']['owntype'][$plotData['is_own']]['title'];
    $is_exchange = $plotData['is_exchange'] ? 'Да' : 'Не';
    $exchange_date = '0000-00-00' == $plotData['exchange_date'] ? '-' : strftime('%d.%m.%Y', strtotime($plotData['exchange_date']));
    $exchange_with = $plotData['exchange_with'] ? $plotData['exchange_with'] : '-';
    $notes = $plotData['notes'];
    $price = $plotData['price'] ? $plotData['price'] : '-';

    $ekatte = $plotData['ekatte'] ? $GLOBALS['Plots']['ekatte'][$plotData['ekatte']]['title'] : '-';
    $number = $plotData['number'] ? $plotData['number'] : '-';
    $group = $plotData['plot_group'];
    $plot = $plotData['plot'] ? $plotData['plot'] : '-';
    $area = $plotData['area'] ? $plotData['area'] : '-';
    $category = $UserDbPlotCategoriesTypeController->getPlotCategoryTitle($plotData['category']);

    $pdf->SetFont('arial', 'B', 10);
    $text = mb_convert_encoding($number, 'Windows-1251');
    $pdf->Cell(0, 5, $text, 0, 1, 'L');

    $pdf->SetFont('arial', '', 10);
    $text = mb_convert_encoding('ЕКАТТЕ: ' . $ekatte . ', масив: ' . $group . ', парцел: ' . $plot . ', кат.: ' . $category . ', площ: ' . $area . ' дка.', 'Windows-1251');
    $pdf->Cell(0, 5, $text, 0, 1, 'L');

    $text = mb_convert_encoding('Начин на ползване: ' . $way_to_use, 'Windows-1251');
    $pdf->Cell(0, 5, $text, 0, 1, 'L');

    $text = mb_convert_encoding('Вид собственост: ' . $is_own . ', цена: ' . $price, 'Windows-1251');
    $pdf->Cell(0, 5, $text, 0, 1, 'L');

    $text = mb_convert_encoding('Замяна: ' . $is_exchange . ', дата на замяна: ' . $exchange_date . ', замяна с: ' . $exchange_with, 'Windows-1251');
    $pdf->Cell(0, 5, $text, 0, 1, 'L');

    $pdf->Ln(2);

    // ##############################RENTS########################################

    $options = [
        'flags' => ['values' => ['FLAGS_TRASH' => false]],
        'plot_id' => (int) $id,
        'return' => ['id', 'number', 'date_from', 'date_to', 'name', 'egn'],
    ];

    $result = $RentsController->getHomeItems($options);

    $pdf->SetDrawColor(0, 0, 0);
    $pdf->SetFillColor(221, 221, 221);
    $pdf->SetFont('arial', '', 10);
    $text = mb_convert_encoding('Наеми', 'Windows-1251');
    $pdf->Cell(0, 5, $text, 0, 1, 'L', true);
    $pdf->SetFont('arial', '', 8);

    if ($result['count'] > 0) {
        $text = mb_convert_encoding('Имот N', 'Windows-1251');
        $pdf->Cell(30, 5, $text, 0, 0, 'L');
        $text = mb_convert_encoding('От дата', 'Windows-1251');
        $pdf->Cell(40, 5, $text, 0, 0, 'L');
        $text = mb_convert_encoding('До дата', 'Windows-1251');
        $pdf->Cell(40, 5, $text, 0, 0, 'L');
        $text = mb_convert_encoding('Контрагент', 'Windows-1251');
        $pdf->Cell(50, 5, $text, 0, 0, 'L');
        $text = mb_convert_encoding('ЕГН', 'Windows-1251');
        $pdf->Cell(30, 5, $text, 0, 0, 'L');
        $pdf->Ln();
    } else {
        $text = mb_convert_encoding('Няма намерени резултати. ', 'Windows-1251');
        $pdf->Cell(0, 5, $text, 0, 1, 'L');
    }

    for ($i = 0; $i < $result['count']; $i++) {
        $text = mb_convert_encoding($result['data'][$i]['number'], 'Windows-1251');
        $pdf->Cell(30, 7, $text, 1, 0, 'L');
        $text = mb_convert_encoding($result['data'][$i]['date_from'], 'Windows-1251');
        $pdf->Cell(40, 7, $text, 1, 0, 'L');
        $text = mb_convert_encoding($result['data'][$i]['date_to'], 'Windows-1251');
        $pdf->Cell(40, 7, $text, 1, 0, 'L');
        $text = mb_convert_encoding($result['data'][$i]['name'], 'Windows-1251');
        $pdf->Cell(50, 7, $text, 1, 0, 'L');
        $text = mb_convert_encoding($result['data'][$i]['egn'], 'Windows-1251');
        $pdf->Cell(30, 7, $text, 1, 0, 'L');
        $pdf->Ln();
    }
    $pdf->Ln(2);
    // ###############################LEASES#####################################

    $options = [
        'flags' => ['values' => ['FLAGS_TRASH' => false]],
        'plot_id' => (int) $id,
        'return' => ['*'],
    ];

    $result = $LeasesController->getHomeItems($options);

    $pdf->SetFont('arial', '', 10);
    $text = mb_convert_encoding('Аренди', 'Windows-1251');
    $pdf->Cell(0, 5, $text, 0, 1, 'L', true);
    $pdf->SetFont('arial', '', 8);

    if ($result['count'] > 0) {
        $text = mb_convert_encoding('Договор N', 'Windows-1251');
        $pdf->Cell(30, 5, $text, 0, 0, 'L');
        $text = mb_convert_encoding('Входящ N', 'Windows-1251');
        $pdf->Cell(30, 5, $text, 0, 0, 'L');
        $text = mb_convert_encoding('От дата', 'Windows-1251');
        $pdf->Cell(30, 5, $text, 0, 0, 'L');
        $text = mb_convert_encoding('До дата', 'Windows-1251');
        $pdf->Cell(30, 5, $text, 0, 0, 'L');
        $text = mb_convert_encoding('Контрагент', 'Windows-1251');
        $pdf->Cell(40, 5, $text, 0, 0, 'L');
        $text = mb_convert_encoding('ЕГН', 'Windows-1251');
        $pdf->Cell(30, 5, $text, 0, 0, 'L');
        $pdf->Ln();
    } else {
        $text = mb_convert_encoding('Няма намерени резултати. ', 'Windows-1251');
        $pdf->Cell(0, 5, $text, 0, 1, 'L');
    }

    for ($i = 0; $i < $result['count']; $i++) {
        $text = mb_convert_encoding($result['data'][$i]['number'], 'Windows-1251');
        $pdf->Cell(30, 7, $text, 1, 0, 'L');
        $text = mb_convert_encoding($result['data'][$i]['in_number'], 'Windows-1251');
        $pdf->Cell(30, 7, $text, 1, 0, 'L');
        $text = mb_convert_encoding($result['data'][$i]['date_from'], 'Windows-1251');
        $pdf->Cell(30, 7, $text, 1, 0, 'L');
        $text = mb_convert_encoding($result['data'][$i]['date_to'], 'Windows-1251');
        $pdf->Cell(30, 7, $text, 1, 0, 'L');
        $text = mb_convert_encoding($result['data'][$i]['name'], 'Windows-1251');
        $pdf->Cell(40, 7, $text, 1, 0, 'L');
        $text = mb_convert_encoding($result['data'][$i]['egn'], 'Windows-1251');
        $pdf->Cell(30, 7, $text, 1, 0, 'L');
        $pdf->Ln();
    }
    $pdf->Ln(2);
    // #############################OWNERS#######################################

    $options = [
        'flags' => ['values' => ['FLAGS_TRASH' => false]],
        'plot_id' => (int) $id,
        'return' => ['*'],
    ];

    $result = $OwnersController->getHomeItems($options);

    $pdf->SetFont('arial', '', 10);
    $text = mb_convert_encoding('Собственици', 'Windows-1251');
    $pdf->Cell(0, 5, $text, 0, 1, 'L', true);
    $pdf->SetFont('arial', '', 8);

    if ($result['count'] > 0) {
        $text = mb_convert_encoding('Име', 'Windows-1251');
        $pdf->Cell(50, 5, $text, 0, 0, 'L');
        $text = mb_convert_encoding('ЕГН', 'Windows-1251');
        $pdf->Cell(40, 5, $text, 0, 0, 'L');
        $text = mb_convert_encoding('От дата', 'Windows-1251');
        $pdf->Cell(50, 5, $text, 0, 0, 'L');
        $text = mb_convert_encoding('Вид документ', 'Windows-1251');
        $pdf->Cell(50, 5, $text, 0, 0, 'L');
        $pdf->Ln();
    } else {
        $text = mb_convert_encoding('Няма намерени резултати. ', 'Windows-1251');
        $pdf->Cell(0, 5, $text, 0, 1, 'L');
    }

    for ($i = 0; $i < $result['count']; $i++) {
        $text = mb_convert_encoding($result['data'][$i]['name'], 'Windows-1251');
        $pdf->Cell(50, 7, $text, 1, 0, 'L');
        $text = mb_convert_encoding($result['data'][$i]['egn'], 'Windows-1251');
        $pdf->Cell(40, 7, $text, 1, 0, 'L');
        $text = mb_convert_encoding($result['data'][$i]['from_date'], 'Windows-1251');
        $pdf->Cell(50, 7, $text, 1, 0, 'L');
        $text = mb_convert_encoding($result['data'][$i]['doc_type'], 'Windows-1251');
        $pdf->Cell(50, 7, $text, 1, 0, 'L');
        $pdf->Ln();
    }
    $pdf->Ln(2);
    // ##########################NEIGHBOURS#####################################

    $options = [
        'flags' => ['values' => ['FLAGS_TRASH' => false]],
        'plot_id' => (int) $id,
        'return' => ['*'],
    ];

    $result = $NeighboursController->getHomeItems($options);

    $pdf->SetFont('arial', '', 10);
    $text = mb_convert_encoding('Съседи', 'Windows-1251');
    $pdf->Cell(0, 5, $text, 0, 1, 'L', true);
    $pdf->SetFont('arial', '', 8);

    if ($result['count'] > 0) {
        $text = mb_convert_encoding('Имот N', 'Windows-1251');
        $pdf->Cell(50, 5, $text, 0, 0, 'L');
        $text = mb_convert_encoding('Вид', 'Windows-1251');
        $pdf->Cell(40, 5, $text, 0, 0, 'L');
        $text = mb_convert_encoding('Съсед', 'Windows-1251');
        $pdf->Cell(50, 5, $text, 0, 0, 'L');
        $text = mb_convert_encoding('Площ', 'Windows-1251');
        $pdf->Cell(50, 5, $text, 0, 0, 'L');
        $pdf->Ln();
    } else {
        $text = mb_convert_encoding('Няма намерени резултати. ', 'Windows-1251');
        $pdf->Cell(0, 5, $text, 0, 1, 'L');
    }

    for ($i = 0; $i < $result['count']; $i++) {
        $text = mb_convert_encoding($result['data'][$i]['number'], 'Windows-1251');
        $pdf->Cell(50, 7, $text, 1, 0, 'L');
        $text = mb_convert_encoding($UserDbAreaTypesController->getNtpTitle($result['data'][$i]['plot_type']), 'Windows-1251');
        $pdf->Cell(40, 7, $text, 1, 0, 'L');
        $text = mb_convert_encoding($result['data'][$i]['name'], 'Windows-1251');
        $pdf->Cell(50, 7, $text, 1, 0, 'L');
        $text = mb_convert_encoding($result['data'][$i]['area'] . ' дка.', 'Windows-1251');
        $pdf->Cell(50, 7, $text, 1, 0, 'L');
        $pdf->Ln();
    }
    $pdf->Ln(2);
    // #########################SEEDS###########################################
    $options = [
        'flags' => ['values' => ['FLAGS_TRASH' => false]],
        'plot_id' => (int) $id,
        'return' => ['*'],
    ];

    $result = $SeedsController->getHomeItems($options);

    $pdf->SetFont('arial', '', 10);
    $text = mb_convert_encoding('Засаждания', 'Windows-1251');
    $pdf->Cell(0, 5, $text, 0, 1, 'L', true);
    $pdf->SetFont('arial', '', 8);

    if ($result['count'] > 0) {
        $text = mb_convert_encoding('Култура', 'Windows-1251');
        $pdf->Cell(50, 5, $text, 0, 0, 'L');
        $text = mb_convert_encoding('Площ', 'Windows-1251');
        $pdf->Cell(30, 5, $text, 0, 0, 'L');
        $text = mb_convert_encoding('Засяване', 'Windows-1251');
        $pdf->Cell(40, 5, $text, 0, 0, 'L');
        $text = mb_convert_encoding('Прибиране', 'Windows-1251');
        $pdf->Cell(40, 5, $text, 0, 0, 'L');
        $text = mb_convert_encoding('Добив', 'Windows-1251');
        $pdf->Cell(30, 5, $text, 0, 0, 'L');
        $pdf->Ln();
    } else {
        $text = mb_convert_encoding('Няма намерени резултати. ', 'Windows-1251');
        $pdf->Cell(0, 5, $text, 0, 1, 'L');
    }

    for ($i = 0; $i < $result['count']; $i++) {
        $text = mb_convert_encoding($GLOBALS['Plots']['culture'][$result['data'][$i]['culture']]['title'], 'Windows-1251');
        $pdf->Cell(50, 7, $text, 1, 0, 'L');
        $text = mb_convert_encoding($result['data'][$i]['area'] . ' дка.', 'Windows-1251');
        $pdf->Cell(30, 7, $text, 1, 0, 'L');
        $text = mb_convert_encoding($result['data'][$i]['date_seed'], 'Windows-1251');
        $pdf->Cell(40, 7, $text, 1, 0, 'L');
        $text = mb_convert_encoding($result['data'][$i]['date_get'], 'Windows-1251');
        $pdf->Cell(40, 7, $text, 1, 0, 'L');
        $text = mb_convert_encoding($result['data'][$i]['amount'] . ' дка.', 'Windows-1251');
        $pdf->Cell(30, 7, $text, 1, 0, 'L');
        $pdf->Ln();
    }
    $pdf->Ln(2);
    // ###########################DEEDS#########################################
    $options = [
        'flags' => ['values' => ['FLAGS_TRASH' => false]],
        'plot_id' => (int) $id,
        'return' => ['*'],
    ];

    $result = $DeedsController->getHomeItems($options);

    $pdf->SetFont('arial', '', 10);
    $text = mb_convert_encoding('Нотариални актове', 'Windows-1251');
    $pdf->Cell(0, 5, $text, 0, 1, 'L', true);
    $pdf->SetFont('arial', '', 8);

    if ($result['count'] > 0) {
        $text = mb_convert_encoding('Входящ N', 'Windows-1251');
        $pdf->Cell(30, 5, $text, 0, 0, 'L');
        $text = mb_convert_encoding('От дата', 'Windows-1251');
        $pdf->Cell(30, 5, $text, 0, 0, 'L');
        $text = mb_convert_encoding('Акт. номер', 'Windows-1251');
        $pdf->Cell(30, 5, $text, 0, 0, 'L');
        $text = mb_convert_encoding('Том', 'Windows-1251');
        $pdf->Cell(20, 5, $text, 0, 0, 'L');
        $text = mb_convert_encoding('Дело', 'Windows-1251');
        $pdf->Cell(20, 5, $text, 0, 0, 'L');
        $text = mb_convert_encoding('Име', 'Windows-1251');
        $pdf->Cell(30, 5, $text, 0, 0, 'L');
        $text = mb_convert_encoding('ЕГН', 'Windows-1251');
        $pdf->Cell(30, 5, $text, 0, 0, 'L');
        $pdf->Ln();
    } else {
        $text = mb_convert_encoding('Няма намерени резултати. ', 'Windows-1251');
        $pdf->Cell(0, 5, $text, 0, 1, 'L');
    }

    for ($i = 0; $i < $result['count']; $i++) {
        $text = mb_convert_encoding($result['data'][$i]['number'], 'Windows-1251');
        $pdf->Cell(30, 7, $text, 1, 0, 'L');
        $text = mb_convert_encoding($result['data'][$i]['date'] . ' дка.', 'Windows-1251');
        $pdf->Cell(30, 7, $text, 1, 0, 'L');
        $text = mb_convert_encoding($result['data'][$i]['register'], 'Windows-1251');
        $pdf->Cell(30, 7, $text, 1, 0, 'L');
        $text = mb_convert_encoding($result['data'][$i]['tom'], 'Windows-1251');
        $pdf->Cell(20, 7, $text, 1, 0, 'L');
        $text = mb_convert_encoding($result['data'][$i]['delo'], 'Windows-1251');
        $pdf->Cell(20, 7, $text, 1, 0, 'L');
        $text = mb_convert_encoding($result['data'][$i]['name'], 'Windows-1251');
        $pdf->Cell(30, 7, $text, 1, 0, 'L');
        $text = mb_convert_encoding($result['data'][$i]['egn'], 'Windows-1251');
        $pdf->Cell(30, 7, $text, 1, 0, 'L');
        $pdf->Ln();
    }

    $pdf->Ln(2);

    $pdf->SetFillColor(0, 74, 113);
    $pdf->SetDrawColor(255, 255, 255);
    $pdf->SetTextColor(255, 255, 255);
    $pdf->Cell(0, 0.1, ' ', 0, 1, 'L', true);
    $pdf->SetFillColor(255, 255, 255);
    $pdf->SetDrawColor(255, 255, 255);
    $pdf->SetTextColor(0, 0, 0);

    $pdf->Ln(2);
}
