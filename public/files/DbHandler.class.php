<?php

/**
 * Used in ajax scripts. Includes all function connect with db proceses.
 *  
 * <AUTHOR>
 */
require_once("DbEngine.class.php");

class DbHandler {

    private $db;

    /**
     * Class constructor
     *
     * @param ref $driver
     * @param string $host
     * @param string $username
     * @param string $password
     * @param string $database
     * @param boolean $persistent
     */
    public function __construct($driver = null, $host = null, $username = null, $password = null, $database = null, $persistent = null) {
        $this->db = new DbEngine();
        $this->db->setDriver(DEFAULT_DB_DRIVER);
        $this->db->setHost(DEFAULT_DB_HOST);
        $this->db->setPort(DEFAULT_DB_PORT);
        $this->db->setDatabase(DEFAULT_DB_DATABASE);
        $this->db->setUsername(DEFAULT_DB_USERNAME);
        $this->db->setPassword(DEFAULT_DB_PASSWORD);

        $sql = "SET NAMES 'UTF8' ";
        $cmd = $this->db->prepare($sql);
        $cmd->execute();
    }

    public function getDbInstance() {
        return $this->db;
    }

    public function prepareGetSQL($table, $returnFields, $fieldTerms) {
        $sql = 'SELECT ' . implode(', ', $returnFields) . ' FROM ' . $table;

        $cntTerms = count($fieldTerms);

        if ($cntTerms) {
            $sql .= ' WHERE ';
        }

        for ($i = 0; $i < $cntTerms; $i++) {
            $sql .= "$fieldTerms[$i] = :field$i";
            if ($i != $cntTerms - 1) {
                $sql .= ' AND ';
            }
        }

        return $sql;
    }

    /**
     * Gets item data by query terms.
     * 	 
     * @param string $table       - name of the table from which to select
     * @param array $returnFields - array of fields which to be returned
     * @param array $fieldTerms   - array of fields which to be queried in the where clause
     * @param array $valueTerms   - array of the corresponding values which to be queried in the where clause
     * @return array
     */
    public function getItem($table, $returnFields, $fieldTerms, $valueTerms) {
        $sql = $this->prepareGetSQL($table, $returnFields, $fieldTerms);

        $cmd = $this->db->prepare($sql);

        $cnt = count($valueTerms);
        for ($i = 0; $i < $cnt; $i++) {
            $cmd->bindParam(':field' . $i, $valueTerms[$i]);
        }
        return $cmd->fetch();
    }

    /**
     * Prapares sql for insert.
     * 
     * @param string $table, name of the table, CAN NOT BE NULL
     * @param array $fields, fields in table, which values will be inserted in, CAN NOT BE NULL     
     * @return string 
     */
    public function prepareInsertSQL($table, $fields) {
        $countFields = count($fields);
        if ($countFields != 0) {
            $sql = 'INSERT INTO ' . $table . ' (' . implode(', ', $fields) . ') VALUES (';

            for ($i = 0; $i < $countFields; $i++) {
                $sql .= ":field$i";
                if ($i != $countFields - 1) {
                    $sql .= ', ';
                }
            }

            $sql .= ')';

            return $sql;
        } else {
            return FALSE;
        }
    }

    /**
     * Adds item in specific table.
     * 
     * @param string $table	    - name of the table into which the new rows to be inserted
     * @param array $fieldTerms - array of fields' names
     * @param array $valueTerms - array of the corresponding values
     * @param boolean $returnID - whether to return last inserted id
     * @return integer
     */
    public function addItem($table, $fieldTerms, $valueTerms) {
        $sql = $this->prepareInsertSQL($table, $fieldTerms);

        $cmd = $this->db->prepare($sql);

        $cnt = count($valueTerms);
        for ($i = 0; $i < $cnt; $i++) {
            $cmd->bindParam(':field' . $i, $valueTerms[$i]);
        }

        $cmd->execute();
    }

    /**
     * Returns md5 of the file, if user has no right returns false
     *
     * @param int $fileID
     * @param int $user
     */
    public function getFile($fileID) {
        if (!$fileID) {
            return false;
        } else {
            $tableFiles = DEFAULT_DB_PREFIX . 'files';
            $id = $fileID;

            $sql = "SELECT id, orig_name, file, server_id, extension FROM $tableFiles WHERE id = :id";
            $cmd = $this->db->prepare($sql);
            $cmd->bindValue(':id', $id, PDO::PARAM_INT);
            $cmd->execute();

            return $cmd->fetch(PDO::FETCH_ASSOC);
        }
    }

    public function getFileByHash($hash) {
        if (!$hash) {
            return false;
        } else {
            $tableFiles = DEFAULT_DB_PREFIX . 'pdf';

            $sql = "SELECT id, file FROM $tableFiles WHERE hash = :hash";
            $cmd = $this->db->prepare($sql);
            $cmd->bindValue(':hash', $hash, PDO::PARAM_INT);
            $cmd->execute();

            return $cmd->fetch(PDO::FETCH_ASSOC);
        }
    }

    public function incrDownloads($fileID) {
        $tableFiles = DEFAULT_DB_PREFIX . 'files';
        $id = $fileID;

        $sql = "UPDATE $tableFiles SET downloads = downloads +1 WHERE id = :id";
        $cmd = $this->db->prepare($sql);
        $cmd->bindValue(':id', $id, PDO::PARAM_INT);
        $cmd->execute();
    }
    
    public function getFileById($fileID) {
        if (!$fileID) {
            return false;
        } else {
            $tableFiles = DEFAULT_DB_PREFIX . 'files';
            $id = $fileID;

            $sql = "SELECT id, filetype, title, plot_id FROM $tableFiles WHERE id = :id";
            $cmd = $this->db->prepare($sql);
            $cmd->bindValue(':id', $id, PDO::PARAM_INT);
            $cmd->execute();

            return $cmd->fetch(PDO::FETCH_ASSOC);
        }
    }
    
    public function getRentFileById($fileID)
    {
        if (!$fileID) {
            return false;
        } else {
            $tableFiles = DEFAULT_DB_PREFIX . 'rents';
            $id = $fileID;

            $sql = "SELECT id, filename, filesize, plot_id FROM $tableFiles WHERE id = :id";
            $cmd = $this->db->prepare($sql);
            $cmd->bindValue(':id', $id, PDO::PARAM_INT);
            $cmd->execute();

            return $cmd->fetch(PDO::FETCH_ASSOC);
        }
    }
    
    public function getLeaseFileById($fileID)
    {
        if (!$fileID) {
            return false;
        } else {
            $tableFiles = DEFAULT_DB_PREFIX . 'leases';
            $id = $fileID;

            $sql = "SELECT id, filename, filesize, plot_id FROM $tableFiles WHERE id = :id";
            $cmd = $this->db->prepare($sql);
            $cmd->bindValue(':id', $id, PDO::PARAM_INT);
            $cmd->execute();

            return $cmd->fetch(PDO::FETCH_ASSOC);
        }
    }

}

?>