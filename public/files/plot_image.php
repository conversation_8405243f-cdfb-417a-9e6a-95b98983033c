<?php

use TF\Engine\Plugins\Core\UserDb\UserDbController;

if(session_id() == '' || !isset($_SESSION)){
    @session_start();
}
include "shell.php";

$UsersDbController = new UserDbController($_SESSION['database']);

if (!$_GET['gid'] || !$_GET['width'] || !$_GET['height'] || !$_GET['user_id'])
	die();

if (!$_SESSION['user_id'] || $_SESSION['user_id'] != $_GET['user_id'])
	die();

$gid = $_GET['gid'];
$width = $_GET['width'];
$height = $_GET['height'];

header("Content-type: image/png");

$map = ms_newMapObj(WMS_MAP_PATH . $_SESSION['group_id'] . ".map");

$maxextent = $UsersDbController->getMaxExtent('layer_kvs');
$maxextent = str_replace("BOX(", "", $maxextent);
$maxextent = str_replace(")", "", $maxextent);
$maxextent = str_replace(",", " ", $maxextent);

$extent = explode(" ",$maxextent);

$map->setExtent($extent[0], $extent[1], $extent[2], $extent[3]);
$map->setSize($width, $height);

$layer1 = ms_newLayerObj($map, $map->getLayerByName("layer_kvs"));
$layer1->set('status', MS_DEFAULT);
$class1 = $layer1->getClass(0);
$class1->set("name", "layer_kvs");
$class1->set("title", "Granici");

$layer2 = ms_newLayerObj($map);
$layer2->setConnectionType(MS_POSTGIS);
$layer2->set('name',"layer_kvs_filter");
$layer2->set('type',MS_LAYER_POLYGON);
$layer2->set('status', MS_DEFAULT);
$layer2->set('connection', "host=".DEFAULT_DB_HOST." dbname=".$_SESSION['database']." user=".DEFAULT_DB_USERNAME." password=".DEFAULT_DB_PASSWORD." port=".DEFAULT_DB_PORT);
$layer2->set('data',"geom from (select * from layer_kvs where gid = '".$gid."') as foo using unique gid");
$layer2->set("dump", "true");
$layer2->set("template", "layer_kvs_filter");
$class2 = ms_newClassObj($layer2);
$class2->set("name", "foo");
$class2->set("title", "Izbran imot");
$style = ms_newStyleObj($class2);
$style->color->setRGB(66, 167, 11);
$style->outlinecolor->setRGB( 0,0,0 );

$maxextent = $UsersDbController->getKvsMaxExtent('layer_kvs',$gid);
$maxextent = str_replace("BOX(", "", $maxextent);
$maxextent = str_replace(")", "", $maxextent);
$maxextent = str_replace(",", " ", $maxextent);
$extent = explode(" ",$maxextent);

$map->setExtent($extent[0], $extent[1], $extent[2], $extent[3]); //die();

$my_point = ms_newPointObj();
$my_point->setXY($width/2,$height/2);

$my_extent = ms_newRectObj();
$my_extent->setextent($extent[0], $extent[1], $extent[2], $extent[3]);

$map->zoompoint(-2,$my_point,$map->width,$map->height,$my_extent);

$scale = round($map->scaledenom);
$class3 = ms_newClassObj($layer1);
$class3->set("name", "scale");
$class3->set("title", "1:".$scale);

$map->scalebar->set("units", MS_METERS);
$map->scalebar->outlinecolor->setRGB(0,0,0);
$map->scalebar->set("status", MS_EMBED);

$map->legend->set("status", MS_EMBED);
$map->legend->set("position", MS_LR);
$map->legend->label->color->setRGB(0,0,0);

$image = $map->draw();
$url = $image->saveWebImage();
$image= ImageCreateFromPng($url);

imagePng($image);

?>
