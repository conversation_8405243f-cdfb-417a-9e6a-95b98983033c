require.config({
    baseUrl: "lib", // Key Clock changes
    paths: {
        jquery: "js_external/jquery.min",
        easyui: "js_external/jquery.easyui.min",
        domReady: "js_external/domReady",
        tinyMCE: "js_external/tinymce/tinymce.min",
        "easyui-lang-bg": "js_external/locale/easyui-lang-bg",
        "datagrid-bufferview": "js_external/datagrid-bufferview",
        "datagrid-detailview": "js_external/datagrid-detailview",
        "datagrid-scrollview": "js_external/datagrid-scrollview",
        egn: "js_external/egn",
        fraction: "js_external/fraction.min",
        "jquery.fcbkcomplete.min": "js_external/jquery.fcbkcomplete.min",
        "jquery.parser": "js_external/jquery.parser",
        OpenLayers: "js_external/OpenLayers",
        "ModifyFeature-tools": "js_external/ModifyFeature-tools",
        "proj4js-compressed": "js_external/proj4js-compressed",
        store: "js_external/store.min",
        "tf.filterwidget": "js_external/tf.filterwidget",
        "jquery-ui.min": "js_external/jquery-ui.min",
        "typeahead.bundle.min": "js_external/typeahead/typeahead.bundle.min",
        underscore: "js_external/underscore-min",
        colorpicker: "js_external/colorpicker/colorpicker",
        eye: "js_external/colorpicker/eye",
        layout: "js_external/colorpicker/layout",
        utils: "js_external/colorpicker/utils",
        jsts: "js_external/jsts/jsts",
        "javascript.util": "js_external/jsts/javascript.util",
        TF: "js/TF"
    },
    shim: {
        easyui: {
            exports: "easyui",
            deps: ["jquery" /* "easyui-lang-bg" */],
            init: function() {
                jQuery.fn.datebox.defaults.formatter = function(date) {
                    return ('0' + date.getDate()).slice(-2) + '.' + ('0' + (date.getMonth()+1)).slice(-2) + '.' + date.getFullYear();
                };

                jQuery.fn.datebox.defaults.parser = function(s){
                    var tmpTodayDate = new Date();
                    var todayDay     = tmpTodayDate.getDate();
                    var todayMonth   = tmpTodayDate.getMonth();
                    var todayYear    = tmpTodayDate.getFullYear();

                    //Ако е подаден Date object директно се връща към полето
                    if(s instanceof Date && typeof s.getMonth == "function") {
                        return s;
                    }
                    //Ако няма въведена дата или все още не е завършено въвеждането на дата
                    //се връша днешна дата (1.1.2015 - 8 символа)
                    if(!s || s.length < 8) {
                        return new Date(todayYear, todayMonth, todayDay);
                    }

                    var separator,
                        year,
                        month,
                        day,
                        parts,
                        date = s;

                    //Проверява се какъв е разделителя на въвеждане
                    if (date.indexOf('.') !== -1) {
                        separator = '.';
                    } else if (date.indexOf('-') !== -1) {
                        separator = '-';
                    } else if (date.indexOf(',') !== -1) {
                        separator = ',';
                    } else if (date.indexOf('/') !== -1) {
                        separator = '/';
                    } else if (date.indexOf('*') !== -1) {
                        separator = '*';
                    } else if (date.indexOf('\\') !== -1) {
                        separator = '\\';
                    } else if (date.indexOf('+') !== -1) {
                        separator = '+';
                    } else {
                        //Ако не е от стандартните се връща днешна дата
                        return new Date(todayYear, todayMonth, todayDay)
                    }

                    parts = date.split(separator);

                    if (parts.length === 3) {

                        if (isNaN(parts[0]) || parts[0].length > 4 || parts[0].length === 0) {
                            return new Date(todayYear, todayMonth, todayDay);
                        }
                        if (isNaN(parts[1]) || parts[1].length > 2 || parts[1].length === 0) {
                            return new Date(todayYear, todayMonth, todayDay);
                        }
                        if (isNaN(parts[2]) || parts[2].length > 4 || parts[2].length === 0) {
                            return new Date(todayYear, todayMonth, todayDay);
                        }

                        //Годината е първи елемент
                        if (parts[0].length > 2) {
                            year  = parseInt(parts[0],10);
                            month = parseInt(parts[1],10) - 1;
                            day   = parseInt(parts[2],10);
                            //Годината е последен елемент
                        } else {
                            day   = parseInt(parts[0],10);
                            month = parseInt(parts[1],10) - 1;
                            year  = parseInt(parts[2],10);
                        }
                        return new Date(year, month, day);
                    }

                    //Ако датата не е в подходящ формат или няма нужния брой елементи се връща днешна дата
                    return new Date(todayYear, todayMonth, todayDay)
                };


            }
        },
        "datagrid-bufferview": {
            deps: ["jquery", "easyui"]
        },
        "datagrid-detailview": {
            deps: ["jquery", "easyui"]
        },
        "datagrid-scrollview": {
            deps: ["jquery", "easyui"]
        },
        egn: {
            exports: "EGN"
        },
        "jquery.fcbkcomplete.min": {
            deps: ["jquery", "typeahead.bundle.min"]
        },
        "jquery.parser": {
            deps: ["jquery", "easyui"]
        },
        Namespace: {
            exports: "Namespace"
        },
        OpenLayers: {
            exports: "OpenLayers"
        },
        "ModifyFeature-tools": {
            deps: ["OpenLayers"]
        },
        "proj4js-compressed": {
            exports: "Proj4js"
        },
        "tf.filterwidget": {
            deps: ["jquery", "easyui", "jquery-ui.min"]
        },
        colorpicker: {
            deps: ["jquery", "eye", "utils", "layout"]
        },
        jsts: {
            deps: ["javascript.util"],
            exports: "jsts"
        },
        tinyMCE: {
            exports: "tinyMCE",
            init: function() {
                this.tinyMCE.DOM.events.domLoaded = true;
                return this.tinyMCE;
            }
        }
    }
});
