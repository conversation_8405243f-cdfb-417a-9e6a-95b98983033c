The FPDF library is made up of the following elements:

- the main file, fpdf.php, which contains the class
- the font metric files (located in the font directory of this archive)

The metric files are necessary as soon as you want to output some text in a document.
They can be accessed from three different locations:

- the directory defined by the FPDF_FONTPATH constant (if this constant is defined)
- the font directory located in the directory containing fpdf.php (as it is the case in this archive)
- the directories accessible through include()

Here is an example defining FPDF_FONTPATH (note the mandatory final slash):

define('FPDF_FONTPATH','/home/<USER>/font/');
require('fpdf.php');

If the files are not accessible, the SetFont() method will produce the following error:

FPDF error: Could not include font metric file


Remarks:

- Only the files corresponding to the fonts actually used are necessary
- The tutorials provided in this package are ready to be executed
