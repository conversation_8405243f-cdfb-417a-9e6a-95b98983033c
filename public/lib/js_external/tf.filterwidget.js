/*jslint browser: true*/
/*global jQuery,_ */
jQuery(function () {
    'use strict';
    jQuery.widget("tf.filterWidget", {
        options: {
            groups: {
                "plots" : {
                    "name": "Имот",
                    "fields": {
                        "kad_ident": {name: "Идентификатор", group: "plots", type: "array", dom_id: "search-kad-ident"},
                        "ekate": {name: "ЕКАТТЕ", group: "plots", type: "array", dom_id: "search-ekatte"},
                        "masiv": {name: "Масив", group: "plots", type: "array", dom_id: "search-masiv"},
                        "number": {name: "Имот", group: "plots", type: "array", dom_id: "search-number"},
                        "category": {name: "Категория", group: "plots", type: "array", dom_id: "search-category", ident: "category_id"},
                        "area_type": {name: "НТП", group: "plots", type: "array", dom_id: "search-area-type", ident: "area_type_id"},
                        "mestnost": {name: "Местност", group: "plots", type: "array", dom_id: "search-mestnost"},
                        "block": {name: "Блок", group: "plots", type: "array", dom_id: "search-block"},
                        "comment": {name: "Забележка", group: "plots", type: "array", dom_id: "search-comment"},
                        "irrigated_area": {name: "Поливна площ", group: "plots", type: "array", dom_id: "search-irrigated-area"},
                        "participation": {name: "Участие по чл.37в", group: "plots", type: "array", dom_id: "search-participation"}
                    }
                },
                "contracts" : {
                    "name": "Договор",
                    "subgroups": {
                        "c_main_info" : {
                            "name": "Основна информация",
                            "fields" : {
                                "cnum": {name: "Номер на договор", group: "contracts", subgroup_key : "c_main_info", type: "array", dom_id: "search-cnum"},
                                "contract_type": {name: "Тип на договор", group: "contracts", subgroup_key : "c_main_info", type: "array", dom_id: "search-contract-type"},
                                "contract_status": {name: "Статус", group: "contracts", subgroup_key : "c_main_info", type: "array", dom_id: "search-contract-status"},
                                "farming": {name: "Стопанство", group: "contracts", subgroup_key : "c_main_info", type: "array", dom_id: "search-farming"},
                            }
                        },
                        "c_date" : {
                            "name": "Дата на сключване",
                            "fields" : {
                                "date_from": {name: "От дата", group: "contracts", subgroup_key : "c_date", type: "array", dom_id: "search-date-from"},
                                "date_to": {name: "До дата", group: "contracts", subgroup_key : "c_date", type: "array", dom_id: "search-date-to"},
                            }
                        },
                        "c_due_date" : {
                            "name": "Период на действие на договор",
                            "fields" : {
                                "start_date": {name: "От дата", group: "contracts", subgroup_key : "c_due_date", type: "array", dom_id: "search-start-date"},
                                "due_date": {name: "До дата", group: "contracts", subgroup_key : "c_due_date", type: "array", dom_id: "search-due-date-to"},
                            }
                        },
                    }
                },
                "ownership": {
                    "name" : "Собственост",
                    "subgroups" : {
                        "o_owner_info" : {
                            "name": "Информация за собственик",
                            "fields" : {
                                "owner_name": {name: "Име", group: "contracts", subgroup_key : "o_owner_info", type: "array", dom_id: "search-owner-name"},
                                "owner_egn": {name: "ЕГН", group: "contracts", subgroup_key : "o_owner_info", type: "array", dom_id: "search-owner-egn"},
                            }
                        },
                        "o_osz_info" : {
                            "name": "Cобственик от Данни от ОСЗ",
                            "fields" : {
                                "ime_subekt": {name: "Име", group: "contracts", subgroup_key : "o_osz_info", type: "array", dom_id: "search-owner-ime-subekt"},
                                "egn_subekt": {name: "ЕГН", group: "contracts", subgroup_key : "o_osz_info", type: "array", dom_id: "search-owner-egn-subekt"},
                            }
                        },
                        "o_rep_info" : {
                            "name": "Информация за представител",
                            "fields" : {
                                "rep_name": {name: "Име", group: "contracts", subgroup_key : "o_rep_info", type: "array", dom_id: "search-represent-name"},
                                "rep_egn": {name: "ЕГН", group: "contracts", subgroup_key : "o_rep_info", type: "array", dom_id: "search-represent-egn"},
                            }
                        },
                        "o_company_info" : {
                            "name": "Информация за фирма",
                            "fields" : {
                                "company_name": {name: "Име", group: "contracts", subgroup_key : "o_company_info", type: "array", dom_id: "search-company-name"},
                                "company_eik": {name: "ЕИК", group: "contracts", subgroup_key : "o_company_info", type: "array", dom_id: "search-company-eik"},
                            }
                        }
                    }
                }
            },
            oldData: {}
        },
        values: {},
        oldValues: {},
        _create: function () {
            this.element.on('click', '.remove-filter', jQuery.proxy(this.onClose, this));
            this.element.css({'max-width': "195px", "overflow-y" : "none", 'overflow-x' : "auto"});
        },
        onClose: function (e) {
            jQuery(e.currentTarget).trigger('onclose');
            e.preventDefault();
            this._removeFilter(e.currentTarget);
            jQuery(e.currentTarget).trigger('onclosed');
        },
        addField: function (group, container, id, data) {
            var el = this.options.groups[group].fields[id],
                html = "<span class='filter-identifier'>" + el.name + ": </span><ul data-list-name=" + id + " style='max-height: 120px; max-width: 185px; overflow-y: none; overflow-x: auto'>",
                containerElement,
                height;
            if (el.name === 'Всички') {
                return;
            }
            if (el.type === 'single') {
                html += '<li>' + this._generateHtmlForField(id, data, el, id) + '</li>';
            } else {
                html += this._generateHtmlForArray(data, el, id);
            }
            html += '</ul>';
            jQuery(html).appendTo(container);
            if (el.type === 'array') {
                containerElement = container.find("ul[data-list-name='" + id + "']");
                height = containerElement.height();
                if (height === 60) {
                    containerElement.addClass('filter-multiselect-border');
                }
            }
        },
        addSubGroupField: function (group, subgroup, container, id, data) {
            var el = this.options.groups[group].subgroups[subgroup].fields[id],
                html = "<span class='filter-identifier'>" + el.name + ": </span><ul style='max-height: 120px; max-width: 185px; overflow-y: none; overflow-x: auto'>";
            if (el.type === 'single') {
                html +=  '<li>' + this._generateHtmlForField(id, data, el, id) + '</li>';
            } else {
                html += this._generateHtmlForArray(data, el, id);
            }
            html += '</ul>';
            jQuery(html).appendTo(container);
        },
        _createGroup: function (group) {
            var currGroup = this.options.groups[group],
                html = '<fieldset style="border: 1px solid black; padding: 5px;" data-group-name="' + group + '"><legend style="font-weight: bold">' + currGroup.name + '</legend></fieldset>';
            jQuery(html).appendTo(this.element);
        },
        _createSubGroup: function (group, subGroup) {
            var currSubgroup = this.options.groups[group].subgroups[subGroup],
                html = '<fieldset style="border: 1px solid black; padding: 3px;" data-group-name="' + subGroup + '"><legend style="font-weight: bold">' + currSubgroup.name + '</legend></fieldset>',
                parent = this._findGroupContainer(group);
            jQuery(html).appendTo(parent);
        },
        _generateHtmlForField: function (id, data, el, group) {
            let value = data;
            if(this.oldValues[group] === undefined) {
                this.oldValues[group] = {};
            }
            if (data.length > 18) {
                data = data.substring(0, 15) + '...';
            }

            if(Array.isArray(this.values[group]) && this.values[group][data]) {
                this.oldValues[group][data] = this.values[group][data];
            }
            if(this.oldValues[group] && this.oldValues[group][data]) {
                value = this.oldValues[group][data];
            }

            return '<span id="bbl-' + id + '" class="filter-bubble">' + data + '<a class="remove-filter" data-dom-id="' + el.dom_id + '" data-text="' + data + '" data-value="' +  value + '" data-current-id="' + id + '" data-group-ident="' + group  + '" data-target="' + (el.ident !== undefined ? el.ident : group) + '"></a></span>';
        },
        _generateHtmlForArray: function (text, el, id) {
            var html = '', i = 0, arr = text.split(',');
            for (i = 0; i < arr.length; i = i + 1) {
                html += this._generateHtmlForField(i, arr[i], el, id);
            }
            return html;
        },
        _findGroupContainer: function (name) {
            return this.element.find("fieldset[data-group-name='" + name + "']");
        },
        populate: function (newData, newValues) {
            var key, groups = [], that = this, data;
            this.element.html('');
            if (newData.filter_action === 'add_to_filter') {
                data = this._combineOldAndNewData(newData);
            } else {
                data = newData;
            }
            this.options.oldData = data;
            for (key in this.options.groups) {
                groups.push(key);
            }
            this.values = newValues;
            groups.forEach(function (group) {
                that._populateGroup(group, data);
            });
            return undefined;
        },
        _populateGroup: function (group, data) {
            var containerHtml = '',
                fieldKey,
                subGrKey,
                groupFieldKeys = [],
                that = this,
                groupFields = this.options.groups[group].fields || [],
                groupSubgroups = this.options.groups[group].subgroups || [],
                groupContainer,
                emptyGroup,
                subGroupKeys = [],
                groupCreated = false,
                isEmpty = true;

            if (groupFields.toString() !== '') {
                for (fieldKey in groupFields) {
                    groupFieldKeys.push(fieldKey);
                }
            }
            emptyGroup = this._isGroupEmpty(groupFieldKeys, data);
            if (!emptyGroup) {
                if (!groupCreated) {
                    that._createGroup(group);
                    groupCreated = true;
                }

                groupContainer = that._findGroupContainer(group);

                if (groupFieldKeys.length > 0) {
                    groupFieldKeys.forEach(function (key) {
                        if (typeof data[key] === 'string' && data[key] !== '' && data[key] !== 'all' && data[key] !== 'Всички') {
                            containerHtml += that.addField(group, groupContainer, key, data[key]);
                        }
                        if (typeof data[key] === 'object' && data[key][0] !== '') {
                            containerHtml += that.addField(group, groupContainer, key, data[key]);
                        }
                    });
                }
                jQuery(containerHtml).appendTo(groupContainer);
                jQuery(groupContainer).appendTo(that.element);
            }
            if (groupSubgroups.toString() !== '') {
                for (subGrKey in groupSubgroups) {
                    subGroupKeys.push(subGrKey);
                }
                subGroupKeys.forEach(function (subgr) {
                    isEmpty = that._isSubgroupEmpty(group, subgr, data);
                    if (!isEmpty) {
                        if (!groupCreated) {
                            that._createGroup(group);
                            groupCreated = true;
                        }
                        that._createSubGroup(group, subgr);
                        that._populateSubgroup(group, subgr, data);
                    }
                });
            }
        },
        _populateSubgroup: function (group, subgroup, data) {
            var that = this,
                subGroupFields = this.options.groups[group].subgroups[subgroup].fields,
                key,
                subGroupFieldKeys = [],
                isEmpty,
                created = false,
                subGroupContainer;

            for (key in subGroupFields) {
                subGroupFieldKeys.push(key);
            }

            isEmpty = this._isGroupEmpty(subGroupFieldKeys, data);
            if (!isEmpty) {
                subGroupContainer = that._findGroupContainer(subgroup);
                if (subGroupFieldKeys.length > 0) {
                    subGroupFieldKeys.forEach(function (key) {
                        if (typeof data[key] === 'string' && data[key] !== '' && data[key] !== 'all' && data[key] !== 'Всички') {
                            that.addSubGroupField(group, subgroup, subGroupContainer, key, data[key]);
                        }
                        if (typeof data[key] === 'object' && data[key][0] !== '') {
                            that.addSubGroupField(group, subgroup, subGroupContainer, key, data[key]);
                        }
                    });
                }
            }
            return created;
        },
        _isGroupEmpty: function (keys, data) {
            var isEmpty = true;

            keys.forEach(function (key) {
                if (typeof data[key] === 'string' && data[key] !== '' && data[key] !== 'all' && data[key] !== 'Всички') {
                    isEmpty = false;
                }
                if (typeof data[key] === 'object' && data[key][0] !== '') {
                    isEmpty = false;
                }
            });

            return isEmpty;
        },
        _isSubgroupEmpty: function (group, subgroup, data) {
            var subGroupFields = this.options.groups[group].subgroups[subgroup].fields,
                key,
                subGroupFieldKeys = [];

            for (key in subGroupFields) {
                subGroupFieldKeys.push(key);
            }

            return this._isGroupEmpty(subGroupFieldKeys, data);
        },
        _removeFilter: function (element) {
            var id = jQuery(element).data('current-id'),
                el = '#' + jQuery(element).data('dom-id'),
                value,
                values,
                comboData;

            if (jQuery(el).data().hasOwnProperty('datebox')) {
                jQuery(el).datebox('reset');
                return true;
            }
            if (jQuery(el).data().hasOwnProperty('textbox')) {
                jQuery(el).textbox('reset');
                return true;
            }
            if (jQuery(el).data().hasOwnProperty('combobox')) {

                values = jQuery(el).combobox('getValues');
                value = values[id];
                if (values.length === 1) {
                    jQuery(el).combobox('reset');

                    comboData = jQuery(el).combobox('getData');
                    comboData.forEach(function (elem) {
                        if (elem.value === 'all') {
                            jQuery(el).combobox('select', 'all');
                            return true;
                        }
                    });
                } else {
                    jQuery(el).combobox('unselect', value);
                }
                return true;
            }
            jQuery(el).val('');
        },
        _combineOldAndNewData: function (newData) {
            var oldData = this.options.oldData,
                key,
                dataKeys = [],
                combinedData = {},
                tmpArray;

            if (oldData.number === undefined) {
                return newData;
            }

            for (key in oldData) {
                dataKeys.push(key);
            }

            dataKeys.forEach(function (key) {
                if (typeof oldData[key] === 'object') {
                    if (newData[key].length !== undefined) {
                        combinedData[key] = _.union(oldData[key], newData[key]);
                    } else {
                        oldData[key].push(newData[key]);
                    }
                } else {
                    combinedData[key] = [oldData[key], newData[key]];
                }

                combinedData[key] = combinedData[key].join();
                tmpArray = combinedData[key].split(',');
                combinedData[key] = [];
                tmpArray.forEach(function (el) {
                    if (el !== '') {
                        combinedData[key].push(el);
                    }
                });
                combinedData[key] = _.uniq(combinedData[key]);
                combinedData[key] = combinedData[key].join();
            });
            return combinedData;
        }
    });
});
