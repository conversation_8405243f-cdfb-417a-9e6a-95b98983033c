(function (root, factory) {
	if (typeof define === "function" && define.amd) {
		// AMD. Register as an anonymous module.
		define([
			"jquery",
			"easyui"
		], factory);
	} else {
		// Browser globals (root is window)
		factory(
			jQuery,
			undefined
		);
	}
})(typeof self !== "undefined" ? self : this, function (
	jQuery,
) {
	if ($.fn.pagination){
		$.fn.pagination.defaults.beforePageText = 'Стр.';
		$.fn.pagination.defaults.afterPageText = 'от {pages}';
		$.fn.pagination.defaults.displayMsg = 'Показани от {from} до {to} от {total} записа';
	}

	if ($.fn.datalist){
		$.fn.datalist.defaults.loadMsg = 'Mоля изчакайте ...';
	}

	if ($.fn.datagrid){
		$.fn.datagrid.defaults.loadMsg = 'Обработка, моля изчакайте ...';
	}

	if ($.fn.treegrid && $.fn.datagrid){
		$.fn.treegrid.defaults.loadMsg = $.fn.datagrid.defaults.loadMsg;
	}
	if ($.messager){
		$.messager.defaults.ok = 'ОК';
		$.messager.defaults.cancel = 'Откажи';
	}
	if ($.fn.validatebox){
		$.fn.validatebox.defaults.missingMessage = 'Това поле е задължително.';
		$.fn.validatebox.defaults.rules.email.message = 'Моля, въведете валиден имейл адрес.';
		$.fn.validatebox.defaults.rules.url.message = 'Моля въведете валиден URL.';
		$.fn.validatebox.defaults.rules.length.message = 'Моля, въведете стойност между {0} и {1}.';
	}
	if ($.fn.numberbox){
		$.fn.numberbox.defaults.missingMessage = 'Това поле е задължително.';
	}
	if ($.fn.combobox){
		$.fn.combobox.defaults.missingMessage = 'Това поле е задължително.';
	}
	if ($.fn.combotree){
		$.fn.combotree.defaults.missingMessage = 'Това поле е задължително.';
	}
	if ($.fn.combogrid){
		$.fn.combogrid.defaults.missingMessage = 'Това поле е задължително.';
	}
	if ($.fn.calendar){
		$.fn.calendar.defaults.weeks = ['Н','П','В','С','Ч','П','С'];
		$.fn.calendar.defaults.months = ['януари', 'февр', 'март', 'април', 'май', 'юни', 'юли', 'август', 'септ', 'окт', 'ноем', 'дек'];
	}
	if ($.fn.datebox){
		$.fn.datebox.defaults.currentText = 'Днес';
		$.fn.datebox.defaults.closeText = 'Затвори';
		$.fn.datebox.defaults.okText = 'ОК';
		$.fn.datebox.defaults.missingMessage = 'Това поле е задължително.';
	}
	if ($.fn.datetimebox && $.fn.datebox){
		$.extend($.fn.datetimebox.defaults,{
			currentText: $.fn.datebox.defaults.currentText,
			closeText: $.fn.datebox.defaults.closeText,
			okText: $.fn.datebox.defaults.okText,
			missingMessage: $.fn.datebox.defaults.missingMessage
		});
	}
})
