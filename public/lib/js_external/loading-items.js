var pe;
var peIndex = 1;
var isLoading = false;

function changePeriodicExecutor(){
	if(peIndex == 20){
		peIndex = 1;
		jQuery('#progress').progressbar('setValue', peIndex * 5);        
    } else {
        peIndex++;
        jQuery('#progress').progressbar('setValue', peIndex * 5);
    }
}

function startLoading(){
	
    if(!isLoading){
        isLoading = true;
        peIndex = 0;
        pe = new PeriodicalExecuter(changePeriodicExecutor, 0.1);
        jQuery('#progress-win').window('open');
    }
}

function endLoading(){
    jQuery('#progress-win').window('close');
    pe.stop();
    jQuery('#progress').progressbar('setValue', 0);
    peIndex = 0;
    isLoading=false;
}

function initLoadingItems() {
	jQuery('#progress-win').window({  
	    width:400,  
	    height:90,
	    title: 'Зареждане...',
	    iconCls: "icon-progress",
	    modal:true,
	    closed: true,
	    closable: false,
	    resizable: false,
	    collapsible: false,
	    minimizable: false,
	    maximizable: false
	});
	
	jQuery('#progress').progressbar({  
	    value: 0
	});
}