tinymce-dist
============

Production ready minified version of TinyMCE excluding the source code, build tools and unit tests. If you need the full source code go to the [main repository](//github.com/tinymce/tinymce) instead.

## Tags

Each release has it's own tag using the [semver standard](http://semver.org/). This means you can checkout a specific version using git clone.
```
git clone -b <version> git://github.com/tinymce/tinymce-dist.git
```

## Git submodule
You can add TinyMCE as a [submodule](http://git-scm.com/book/en/Git-Tools-Submodules) to your existing repository.
```
git <NAME_EMAIL>:tinymce/tinymce-dist.git tinymce
```

## Package managers
You can also install TinyMCE using various [package managers](http://www.tinymce.com/wiki.php/Installation_using_package_managers).
