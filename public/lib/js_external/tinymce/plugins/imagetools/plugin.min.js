!function(){var e={},t=function(t){for(var n=e[t],i=n.deps,o=n.defn,a=i.length,s=new Array(a),l=0;a>l;++l)s[l]=r(i[l]);var c=o.apply(null,s);if(void 0===c)throw"module ["+t+"] returned undefined";n.instance=c},n=function(t,n,r){if("string"!=typeof t)throw"module id must be a string";if(void 0===n)throw"no dependencies for "+t;if(void 0===r)throw"no definition function for "+t;e[t]={deps:n,defn:r,instance:void 0}},r=function(n){var r=e[n];if(void 0===r)throw"module ["+n+"] was undefined";return void 0===r.instance&&t(n),r.instance},i=function(e,t){for(var n=e.length,i=new Array(n),o=0;n>o;++o)i.push(r(e[o]));t.apply(null,t)},o={};o.bolt={module:{api:{define:n,require:i,demand:r}}};var a=n,s=function(e,t){a(e,[],function(){return t})};s("1",tinymce.PluginManager),s("2",tinymce.Env),s("3",tinymce.util.Promise),s("4",tinymce.util.URI),s("5",tinymce.util.Tools),s("6",tinymce.util.Delay),a("m",[],function(){function e(e,t){return n(document.createElement("canvas"),e,t)}function t(e){return e.getContext("2d")}function n(e,t,n){return e.width=t,e.height=n,e}return{create:e,resize:n,get2dContext:t}}),a("n",[],function(){function e(e){return e.naturalWidth||e.width}function t(e){return e.naturalHeight||e.height}return{getWidth:e,getHeight:t}}),a("o",[],function(){function e(e,t){return function(){e.apply(t,arguments)}}function t(t){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof t)throw new TypeError("not a function");this._state=null,this._value=null,this._deferreds=[],s(t,e(r,this),e(i,this))}function n(e){var t=this;return null===this._state?void this._deferreds.push(e):void l(function(){var n=t._state?e.onFulfilled:e.onRejected;if(null===n)return void(t._state?e.resolve:e.reject)(t._value);var r;try{r=n(t._value)}catch(i){return void e.reject(i)}e.resolve(r)})}function r(t){try{if(t===this)throw new TypeError("A promise cannot be resolved with itself.");if(t&&("object"==typeof t||"function"==typeof t)){var n=t.then;if("function"==typeof n)return void s(e(n,t),e(r,this),e(i,this))}this._state=!0,this._value=t,o.call(this)}catch(a){i.call(this,a)}}function i(e){this._state=!1,this._value=e,o.call(this)}function o(){for(var e=0,t=this._deferreds.length;t>e;e++)n.call(this,this._deferreds[e]);this._deferreds=null}function a(e,t,n,r){this.onFulfilled="function"==typeof e?e:null,this.onRejected="function"==typeof t?t:null,this.resolve=n,this.reject=r}function s(e,t,n){var r=!1;try{e(function(e){r||(r=!0,t(e))},function(e){r||(r=!0,n(e))})}catch(i){if(r)return;r=!0,n(i)}}if(window.Promise)return window.Promise;var l=t.immediateFn||"function"==typeof setImmediate&&setImmediate||function(e){setTimeout(e,1)},c=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)};return t.prototype["catch"]=function(e){return this.then(null,e)},t.prototype.then=function(e,r){var i=this;return new t(function(t,o){n.call(i,new a(e,r,t,o))})},t.all=function(){var e=Array.prototype.slice.call(1===arguments.length&&c(arguments[0])?arguments[0]:arguments);return new t(function(t,n){function r(o,a){try{if(a&&("object"==typeof a||"function"==typeof a)){var s=a.then;if("function"==typeof s)return void s.call(a,function(e){r(o,e)},n)}e[o]=a,0===--i&&t(e)}catch(l){n(l)}}if(0===e.length)return t([]);for(var i=e.length,o=0;o<e.length;o++)r(o,e[o])})},t.resolve=function(e){return e&&"object"==typeof e&&e.constructor===t?e:new t(function(t){t(e)})},t.reject=function(e){return new t(function(t,n){n(e)})},t.race=function(e){return new t(function(t,n){for(var r=0,i=e.length;i>r;r++)e[r].then(t,n)})},t}),a("p",[],function(){function e(e){var t=document.createElement("a");return t.href=e,t.pathname}function t(t){var n=e(t).split("."),r=n[n.length-1],i={jpg:"image/jpeg",jpeg:"image/jpeg",png:"image/png"};return r&&(r=r.toLowerCase()),i[r]}return{guessMimeType:t}}),a("e",["o","m","p","n"],function(e,t,n,r){function i(t){return new e(function(e){function n(){t.removeEventListener("load",n),e(t)}t.complete?e(t):t.addEventListener("load",n)})}function o(e){return i(e).then(function(e){var n,i;return i=t.create(r.getWidth(e),r.getHeight(e)),n=t.get2dContext(i),n.drawImage(e,0,0),i})}function a(e){return i(e).then(function(e){var t=e.src;return 0===t.indexOf("blob:")?l(t):0===t.indexOf("data:")?c(t):o(e).then(function(e){return c(e.toDataURL(n.guessMimeType(t)))})})}function s(t){return new e(function(e){function n(){r.removeEventListener("load",n),e(r)}var r=new Image;r.addEventListener("load",n),r.src=URL.createObjectURL(t),r.complete&&n()})}function l(t){return new e(function(e){var n=new XMLHttpRequest;n.open("GET",t,!0),n.responseType="blob",n.onload=function(){200==this.status&&e(this.response)},n.send()})}function c(t){return new e(function(e){var n,r,i,o,a,s;if(t=t.split(","),o=/data:([^;]+)/.exec(t[0]),o&&(a=o[1]),n=atob(t[1]),window.WebKitBlobBuilder){for(s=new WebKitBlobBuilder,r=new ArrayBuffer(n.length),i=0;i<r.length;i++)r[i]=n.charCodeAt(i);return s.append(r),void e(s.getBlob(a))}for(r=new Uint8Array(n.length),i=0;i<r.length;i++)r[i]=n.charCodeAt(i);e(new Blob([r],{type:a}))})}function u(e){return 0===e.indexOf("blob:")?l(e):0===e.indexOf("data:")?c(e):null}function d(e,t){return c(e.toDataURL(t))}function f(t){return new e(function(e){var n=new FileReader;n.onloadend=function(){e(n.result)},n.readAsDataURL(t)})}function h(e){return f(e).then(function(e){return e.split(",")[1]})}function p(e){URL.revokeObjectURL(e.src)}return{blobToImage:s,imageToBlob:a,blobToDataUri:f,blobToBase64:h,imageToCanvas:o,canvasToBlob:d,revokeImageUrl:p,uriToBlob:u}}),a("q",[],function(){function e(e,t,n){return e=parseFloat(e),e>n?e=n:t>e&&(e=t),e}function t(){return[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1]}function n(e,t){var n,r,i,o,a=[],s=new Array(10);for(n=0;5>n;n++){for(r=0;5>r;r++)a[r]=t[r+5*n];for(r=0;5>r;r++){for(o=0,i=0;5>i;i++)o+=e[r+5*i]*a[i];s[r+5*n]=o}}return s}function r(t,n){return n=e(n,0,1),t.map(function(t,r){return r%6===0?t=1-(1-t)*n:t*=n,e(t,0,1)})}function i(t,r){var i;return r=e(r,-1,1),r*=100,0>r?i=127+r/100*127:(i=r%1,i=0===i?d[r]:d[Math.floor(r)]*(1-i)+d[Math.floor(r)+1]*i,i=127*i+127),n(t,[i/127,0,0,0,.5*(127-i),0,i/127,0,0,.5*(127-i),0,0,i/127,0,.5*(127-i),0,0,0,1,0,0,0,0,0,1])}function o(t,r){var i,o,a,s;return r=e(r,-1,1),i=1+(r>0?3*r:r),o=.3086,a=.6094,s=.082,n(t,[o*(1-i)+i,a*(1-i),s*(1-i),0,0,o*(1-i),a*(1-i)+i,s*(1-i),0,0,o*(1-i),a*(1-i),s*(1-i)+i,0,0,0,0,0,1,0,0,0,0,0,1])}function a(t,r){var i,o,a,s,l;return r=e(r,-180,180)/180*Math.PI,i=Math.cos(r),o=Math.sin(r),a=.213,s=.715,l=.072,n(t,[a+i*(1-a)+o*-a,s+i*-s+o*-s,l+i*-l+o*(1-l),0,0,a+i*-a+.143*o,s+i*(1-s)+.14*o,l+i*-l+o*-.283,0,0,a+i*-a+o*-(1-a),s+i*-s+o*s,l+i*(1-l)+o*l,0,0,0,0,0,1,0,0,0,0,0,1])}function s(t,r){return r=e(255*r,-255,255),n(t,[1,0,0,0,r,0,1,0,0,r,0,0,1,0,r,0,0,0,1,0,0,0,0,0,1])}function l(t,r,i,o){return r=e(r,0,2),i=e(i,0,2),o=e(o,0,2),n(t,[r,0,0,0,0,0,i,0,0,0,0,0,o,0,0,0,0,0,1,0,0,0,0,0,1])}function c(t,i){return i=e(i,0,1),n(t,r([.393,.769,.189,0,0,.349,.686,.168,0,0,.272,.534,.131,0,0,0,0,0,1,0,0,0,0,0,1],i))}function u(t,i){return i=e(i,0,1),n(t,r([.33,.34,.33,0,0,.33,.34,.33,0,0,.33,.34,.33,0,0,0,0,0,1,0,0,0,0,0,1],i))}var d=[0,.01,.02,.04,.05,.06,.07,.08,.1,.11,.12,.14,.15,.16,.17,.18,.2,.21,.22,.24,.25,.27,.28,.3,.32,.34,.36,.38,.4,.42,.44,.46,.48,.5,.53,.56,.59,.62,.65,.68,.71,.74,.77,.8,.83,.86,.89,.92,.95,.98,1,1.06,1.12,1.18,1.24,1.3,1.36,1.42,1.48,1.54,1.6,1.66,1.72,1.78,1.84,1.9,1.96,2,2.12,2.25,2.37,2.5,2.62,2.75,2.87,3,3.2,3.4,3.6,3.8,4,4.3,4.7,4.9,5,5.5,6,6.5,6.8,7,7.3,7.5,7.8,8,8.4,8.7,9,9.4,9.6,9.8,10];return{identity:t,adjust:r,multiply:n,adjustContrast:i,adjustBrightness:s,adjustSaturation:o,adjustHue:a,adjustColors:l,adjustSepia:c,adjustGrayscale:u}}),a("c",["m","n","e","q"],function(e,t,n,r){function i(r,i){return n.blobToImage(r).then(function(r){function o(e,t){var n,r,i,o,a,s=e.data,l=t[0],c=t[1],u=t[2],d=t[3],f=t[4],h=t[5],p=t[6],m=t[7],g=t[8],v=t[9],y=t[10],b=t[11],C=t[12],x=t[13],w=t[14],N=t[15],E=t[16],_=t[17],S=t[18],k=t[19];for(a=0;a<s.length;a+=4)n=s[a],r=s[a+1],i=s[a+2],o=s[a+3],s[a]=n*l+r*c+i*u+o*d+f,s[a+1]=n*h+r*p+i*m+o*g+v,s[a+2]=n*y+r*b+i*C+o*x+w,s[a+3]=n*N+r*E+i*_+o*S+k;return e}var a,s=e.create(t.getWidth(r),t.getHeight(r)),l=e.get2dContext(s);return l.drawImage(r,0,0),u(r),a=o(l.getImageData(0,0,s.width,s.height),i),l.putImageData(a,0,0),n.canvasToBlob(s)})}function o(r,i){return n.blobToImage(r).then(function(r){function o(e,t,n){function r(e,t,n){return e>n?e=n:t>e&&(e=t),e}var i,o,a,s,l,c,u,d,f,h,p,m,g,v,y,b,C;for(a=Math.round(Math.sqrt(n.length)),s=Math.floor(a/2),i=e.data,o=t.data,b=e.width,C=e.height,c=0;C>c;c++)for(l=0;b>l;l++){for(u=d=f=0,p=0;a>p;p++)for(h=0;a>h;h++)m=r(l+h-s,0,b-1),g=r(c+p-s,0,C-1),v=4*(g*b+m),y=n[p*a+h],u+=i[v]*y,d+=i[v+1]*y,f+=i[v+2]*y;v=4*(c*b+l),o[v]=r(u,0,255),o[v+1]=r(d,0,255),o[v+2]=r(f,0,255)}return t}var a,s,l=e.create(t.getWidth(r),t.getHeight(r)),c=e.get2dContext(l);return c.drawImage(r,0,0),u(r),a=c.getImageData(0,0,l.width,l.height),s=c.getImageData(0,0,l.width,l.height),s=o(a,s,i),c.putImageData(s,0,0),n.canvasToBlob(l)})}function a(r){return function(i,o){return n.blobToImage(i).then(function(i){function a(e,t){var n,r=e.data;for(n=0;n<r.length;n+=4)r[n]=t[r[n]],r[n+1]=t[r[n+1]],r[n+2]=t[r[n+2]];return e}var s,l,c=e.create(t.getWidth(i),t.getHeight(i)),d=e.get2dContext(c),f=new Array(256);for(l=0;l<f.length;l++)f[l]=r(l,o);return d.drawImage(i,0,0),u(i),s=a(d.getImageData(0,0,c.width,c.height),f),d.putImageData(s,0,0),n.canvasToBlob(c)})}}function s(e){return function(t,n){return i(t,e(r.identity(),n))}}function l(e){return function(t){return i(t,e)}}function c(e){return function(t){return o(t,e)}}var u=n.revokeImageUrl;return{invert:l([-1,0,0,0,255,0,-1,0,0,255,0,0,-1,0,255,0,0,0,1,0]),brightness:s(r.adjustBrightness),hue:s(r.adjustHue),saturate:s(r.adjustSaturation),contrast:s(r.adjustContrast),grayscale:s(r.adjustGrayscale),sepia:s(r.adjustSepia),colorize:function(e,t,n,o){return i(e,r.adjustColors(r.identity(),t,n,o))},sharpen:c([0,-1,0,-1,5,-1,0,-1,0]),emboss:c([-2,-1,0,-1,1,1,0,1,2]),gamma:a(function(e,t){return 255*Math.pow(e/255,1-t)}),exposure:a(function(e,t){return 255*(1-Math.exp(-(e/255)*t))}),colorFilter:i,convoluteFilter:o}}),a("d",["e","m","n"],function(e,t,n){function r(r,i){return e.blobToImage(r).then(function(o){var a=t.create(n.getWidth(o),n.getHeight(o)),l=t.get2dContext(a),c=0,u=0;return i=0>i?360+i:i,90!=i&&270!=i||t.resize(a,a.height,a.width),90!=i&&180!=i||(c=a.width),270!=i&&180!=i||(u=a.height),l.translate(c,u),l.rotate(i*Math.PI/180),l.drawImage(o,0,0),s(o),e.canvasToBlob(a,r.type)})}function i(r,i){return e.blobToImage(r).then(function(r){var o=t.create(n.getWidth(r),n.getHeight(r)),a=t.get2dContext(o);return"v"==i?(a.scale(1,-1),a.drawImage(r,0,-o.height)):(a.scale(-1,1),a.drawImage(r,-o.width,0)),s(r),e.canvasToBlob(o)})}function o(n,r,i,o,a){return e.blobToImage(n).then(function(n){var l=t.create(o,a),c=t.get2dContext(l);return c.drawImage(n,-r,-i),s(n),e.canvasToBlob(l)})}function a(n,r,i){return e.blobToImage(n).then(function(o){var a=t.create(r,i),l=t.get2dContext(a);return l.drawImage(o,0,0,r,i),s(o),e.canvasToBlob(a,n.type)})}var s=e.revokeImageUrl;return{rotate:r,flip:i,crop:o,resize:a}}),a("7",["c","d"],function(e,t){var n=function(t){return e.invert(t)},r=function(t){return e.sharpen(t)},i=function(t){return e.emboss(t)},o=function(t,n){return e.gamma(t,n)},a=function(t,n){return e.exposure(t,n)},s=function(t,n,r,i){return e.colorize(t,n,r,i)},l=function(t,n){return e.brightness(t,n)},c=function(t,n){return e.hue(t,n)},u=function(t,n){return e.saturate(t,n)},d=function(t,n){return e.contrast(t,n)},f=function(t,n){return e.grayscale(t,n)},h=function(t,n){return e.sepia(t,n)},p=function(e,n){return t.flip(e,n)},m=function(e,n,r,i,o){return t.crop(e,n,r,i,o)},g=function(e,n,r){return t.resize(e,n,r)},v=function(e,n){return t.rotate(e,n)};return{invert:n,sharpen:r,emboss:i,brightness:l,hue:c,saturate:u,contrast:d,grayscale:f,sepia:h,colorize:s,gamma:o,exposure:a,flip:p,crop:m,resize:g,rotate:v}}),a("8",["e"],function(e){var t=function(t){return e.blobToImage(t)},n=function(t){return e.imageToBlob(t)},r=function(t){return e.blobToDataUri(t)},i=function(t){return e.blobToBase64(t)};return{blobToImage:t,imageToBlob:n,blobToDataUri:r,blobToBase64:i}}),s("f",tinymce.dom.DOMUtils),s("g",tinymce.ui.Factory),s("h",tinymce.ui.Form),s("i",tinymce.ui.Container),s("r",tinymce.ui.Control),s("s",tinymce.ui.DragHelper),s("t",tinymce.geom.Rect),s("v",tinymce.dom.DomQuery),s("w",tinymce.util.Observable),s("x",tinymce.util.VK),a("u",["v","s","t","5","w","x"],function(e,t,n,r,i,o){var a=0;return function(s,l,c,u,d){function f(e,t){return{x:t.x+e.x,y:t.y+e.y,w:t.w,h:t.h}}function h(e,t){return{x:t.x-e.x,y:t.y-e.y,w:t.w,h:t.h}}function p(){return h(c,s)}function m(e,t,r,i){var o,a,l,u,d;o=t.x,a=t.y,l=t.w,u=t.h,o+=r*e.deltaX,a+=i*e.deltaY,l+=r*e.deltaW,u+=i*e.deltaH,20>l&&(l=20),20>u&&(u=20),d=s=n.clamp({x:o,y:a,w:l,h:u},c,"move"==e.name),d=h(c,d),E.fire("updateRect",{rect:d}),x(d)}function g(){function n(e){var n;return new t(R,{document:u.ownerDocument,handle:R+"-"+e.name,start:function(){n=s},drag:function(t){m(e,n,t.deltaX,t.deltaY)}})}e('<div id="'+R+'" class="'+T+'croprect-container" role="grid" aria-dropeffect="execute">').appendTo(u),r.each(k,function(t){e("#"+R,u).append('<div id="'+R+"-"+t+'"class="'+T+'croprect-block" style="display: none" data-mce-bogus="all">')}),r.each(_,function(t){e("#"+R,u).append('<div id="'+R+"-"+t.name+'" class="'+T+"croprect-handle "+T+"croprect-handle-"+t.name+'"style="display: none" data-mce-bogus="all" role="gridcell" tabindex="-1" aria-label="'+t.label+'" aria-grabbed="false">')}),S=r.map(_,n),y(s),e(u).on("focusin focusout",function(t){e(t.target).attr("aria-grabbed","focus"===t.type)}),e(u).on("keydown",function(e){function t(e,t,r,i,o){e.stopPropagation(),e.preventDefault(),m(n,r,i,o)}var n;switch(r.each(_,function(t){return e.target.id==R+"-"+t.name?(n=t,!1):void 0}),e.keyCode){case o.LEFT:t(e,n,s,-10,0);break;case o.RIGHT:t(e,n,s,10,0);break;case o.UP:t(e,n,s,0,-10);break;case o.DOWN:t(e,n,s,0,10);break;case o.ENTER:case o.SPACEBAR:e.preventDefault(),d()}})}function v(t){var n;n=r.map(_,function(e){return"#"+R+"-"+e.name}).concat(r.map(k,function(e){return"#"+R+"-"+e})).join(","),t?e(n,u).show():e(n,u).hide()}function y(t){function n(t,n){n.h<0&&(n.h=0),n.w<0&&(n.w=0),e("#"+R+"-"+t,u).css({left:n.x,top:n.y,width:n.w,height:n.h})}r.each(_,function(n){e("#"+R+"-"+n.name,u).css({left:t.w*n.xMul+t.x,top:t.h*n.yMul+t.y})}),n("top",{x:l.x,y:l.y,w:l.w,h:t.y-l.y}),n("right",{x:t.x+t.w,y:t.y,w:l.w-t.x-t.w+l.x,h:t.h}),n("bottom",{x:l.x,y:t.y+t.h,w:l.w,h:l.h-t.y-t.h+l.y}),n("left",{x:l.x,y:t.y,w:t.x-l.x,h:t.h}),n("move",t)}function b(e){s=e,y(s)}function C(e){l=e,y(s)}function x(e){b(f(c,e))}function w(e){c=e,y(s)}function N(){r.each(S,function(e){e.destroy()}),S=[]}var E,_,S,k,T="mce-",R=T+"crid-"+a++;return _=[{name:"move",xMul:0,yMul:0,deltaX:1,deltaY:1,deltaW:0,deltaH:0,label:"Crop Mask"},{name:"nw",xMul:0,yMul:0,deltaX:1,deltaY:1,deltaW:-1,deltaH:-1,label:"Top Left Crop Handle"},{name:"ne",xMul:1,yMul:0,deltaX:0,deltaY:1,deltaW:1,deltaH:-1,label:"Top Right Crop Handle"},{name:"sw",xMul:0,yMul:1,deltaX:1,deltaY:0,deltaW:-1,deltaH:1,label:"Bottom Left Crop Handle"},{name:"se",xMul:1,yMul:1,deltaX:0,deltaY:0,deltaW:1,deltaH:1,label:"Bottom Right Crop Handle"}],k=["top","right","bottom","left"],g(u),E=r.extend({toggleVisibility:v,setClampRect:w,setRect:b,getInnerRect:p,setInnerRect:x,setViewPortRect:C,destroy:N},i)}}),a("j",["r","s","t","5","3","u"],function(e,t,n,r,i,o){function a(e){return new i(function(t){function n(){e.removeEventListener("load",n),t(e)}e.complete?t(e):e.addEventListener("load",n)})}return e.extend({Defaults:{classes:"imagepanel"},selection:function(e){return arguments.length?(this.state.set("rect",e),this):this.state.get("rect")},imageSize:function(){var e=this.state.get("viewRect");return{w:e.w,h:e.h}},toggleCropRect:function(e){this.state.set("cropEnabled",e)},imageSrc:function(e){var t=this,r=new Image;r.src=e,a(r).then(function(){var e,i,o=t.state.get("viewRect");i=t.$el.find("img"),i[0]?i.replaceWith(r):t.getEl().appendChild(r),e={x:0,y:0,w:r.naturalWidth,h:r.naturalHeight},t.state.set("viewRect",e),t.state.set("rect",n.inflate(e,-20,-20)),o&&o.w==e.w&&o.h==e.h||t.zoomFit(),t.repaintImage(),t.fire("load")})},zoom:function(e){return arguments.length?(this.state.set("zoom",e),this):this.state.get("zoom")},postRender:function(){return this.imageSrc(this.settings.imageSrc),this._super()},zoomFit:function(){var e,t,n,r,i,o,a,s=this;a=10,e=s.$el.find("img"),t=s.getEl().clientWidth,n=s.getEl().clientHeight,r=e[0].naturalWidth,i=e[0].naturalHeight,o=Math.min((t-a)/r,(n-a)/i),o>=1&&(o=1),s.zoom(o)},repaintImage:function(){var e,t,n,r,i,o,a,s,l,c;c=this.getEl(),s=this.zoom(),l=this.state.get("rect"),a=this.$el.find("img"),i=c.offsetWidth,o=c.offsetHeight,n=a[0].naturalWidth*s,r=a[0].naturalHeight*s,e=Math.max(0,i/2-n/2),t=Math.max(0,o/2-r/2),a.css({left:e,top:t,width:n,height:r}),this.cropRect&&(this.cropRect.setRect({x:l.x*s+e,y:l.y*s+t,w:l.w*s,h:l.h*s}),this.cropRect.setClampRect({x:e,y:t,w:n,h:r}),this.cropRect.setViewPortRect({x:0,y:0,w:i,h:o}))},bindStates:function(){function e(e){t.cropRect=new o(e,t.state.get("viewRect"),t.state.get("viewRect"),t.getEl(),function(){t.fire("crop")}),t.cropRect.on("updateRect",function(e){var n=e.rect,r=t.zoom();n={x:Math.round(n.x/r),y:Math.round(n.y/r),w:Math.round(n.w/r),h:Math.round(n.h/r)},t.state.set("rect",n)}),t.on("remove",t.cropRect.destroy)}var t=this;t.state.on("change:cropEnabled",function(e){t.cropRect.toggleVisibility(e.value),t.repaintImage()}),t.state.on("change:zoom",function(){t.repaintImage()}),t.state.on("change:rect",function(n){var r=n.value;t.cropRect||e(r),t.cropRect.setRect(r)})}})}),a("k",[],function(){return function(){function e(e){var t;return t=o.splice(++a),o.push(e),{state:e,removed:t}}function t(){return r()?o[--a]:void 0}function n(){return i()?o[++a]:void 0}function r(){return a>0}function i(){return-1!=a&&a<o.length-1}var o=[],a=-1;return{data:o,add:e,undo:t,redo:n,canUndo:r,canRedo:i}}}),a("9",["f","5","3","g","h","i","j","7","8","k"],function(e,t,n,r,i,o,a,s,l,c){function u(e){return{blob:e,url:URL.createObjectURL(e)}}function d(e){e&&URL.revokeObjectURL(e.url)}function f(e){t.each(e,d)}function h(n,l,h){function p(e){var t,n,r,i;t=O.find("#w")[0],n=O.find("#h")[0],r=parseInt(t.value(),10),i=parseInt(n.value(),10),O.find("#constrain")[0].checked()&&ae&&se&&r&&i&&("w"==e.control.settings.name?(i=Math.round(r*le),n.value(i)):(r=Math.round(i*ce),t.value(r))),ae=r,se=i}function m(e){return Math.round(100*e)+"%"}function g(){O.find("#undo").disabled(!ue.canUndo()),O.find("#redo").disabled(!ue.canRedo()),O.statusbar.find("#save").disabled(!ue.canUndo())}function v(){O.find("#undo").disabled(!0),O.find("#redo").disabled(!0)}function y(e){e&&$.imageSrc(e.url)}function b(e){return function(){var n=t.grep(oe,function(t){return t.settings.name!=e});t.each(n,function(e){e.hide()}),e.show(),e.focus()}}function C(e){z=u(e),y(z)}function x(e){n=u(e),y(n),f(ue.add(n).removed),g()}function w(){var e=$.selection();s.crop(n.blob,e.x,e.y,e.w,e.h).then(function(e){x(e),_()})}function N(e){var t=[].slice.call(arguments,1);return function(){var r=z||n;e.apply(this,[r.blob].concat(t)).then(C)}}function E(e){var t=[].slice.call(arguments,1);return function(){e.apply(this,[n.blob].concat(t)).then(x)}}function _(){y(n),d(z),b(I)(),g()}function S(){z&&(x(z.blob),_())}function k(){var e=$.zoom();2>e&&(e+=.1),$.zoom(e)}function T(){var e=$.zoom();e>.1&&(e-=.1),$.zoom(e)}function R(){n=ue.undo(),y(n),g()}function A(){n=ue.redo(),y(n),g()}function B(){l(n.blob),O.close()}function D(e){return new i({layout:"flex",direction:"row",labelGap:5,border:"0 0 1 0",align:"center",pack:"center",padding:"0 10 0 10",spacing:5,flex:0,minHeight:60,defaults:{classes:"imagetool",type:"button"},items:e})}function L(e,t){return D([{text:"Back",onclick:_},{type:"spacer",flex:1},{text:"Apply",subtype:"primary",onclick:S}]).hide().on("show",function(){v(),t(n.blob).then(function(e){var t=u(e);y(t),d(z),z=t})})}function M(e,t,r,i,o){function a(e){t(n.blob,e).then(function(e){var t=u(e);y(t),d(z),z=t})}return D([{text:"Back",onclick:_},{type:"spacer",flex:1},{type:"slider",flex:1,ondragend:function(e){a(e.value)},minValue:i,maxValue:o,value:r,previewFilter:m},{type:"spacer",flex:1},{text:"Apply",subtype:"primary",onclick:S}]).hide().on("show",function(){this.find("slider").value(r),v()})}function P(e,t){function r(){var e,r,i;e=O.find("#r")[0].value(),r=O.find("#g")[0].value(),i=O.find("#b")[0].value(),t(n.blob,e,r,i).then(function(e){var t=u(e);y(t),d(z),z=t})}return D([{text:"Back",onclick:_},{type:"spacer",flex:1},{type:"slider",label:"R",name:"r",minValue:0,value:1,maxValue:2,ondragend:r,previewFilter:m},{type:"slider",label:"G",name:"g",minValue:0,value:1,maxValue:2,ondragend:r,previewFilter:m},{type:"slider",label:"B",name:"b",minValue:0,value:1,maxValue:2,ondragend:r,previewFilter:m},{type:"spacer",flex:1},{text:"Apply",subtype:"primary",onclick:S}]).hide().on("show",function(){O.find("#r,#g,#b").value(1),v()})}function H(e){e.control.value()===!0&&(le=se/ae,ce=ae/se)}var O,I,F,z,U,W,V,$,q,j,Y,X,K,G,J,Q,Z,ee,te,ne,re,ie,oe,ae,se,le,ce,ue=new c;U=D([{text:"Back",onclick:_},{type:"spacer",flex:1},{text:"Apply",subtype:"primary",onclick:w}]).hide().on("show hide",function(e){$.toggleCropRect("show"==e.type)}).on("show",v),W=D([{text:"Back",onclick:_},{type:"spacer",flex:1},{type:"textbox",name:"w",label:"Width",size:4,onkeyup:p},{type:"textbox",name:"h",label:"Height",size:4,onkeyup:p},{type:"checkbox",name:"constrain",text:"Constrain proportions",checked:!0,onchange:H},{type:"spacer",flex:1},{text:"Apply",subtype:"primary",onclick:"submit"}]).hide().on("submit",function(e){var t=parseInt(O.find("#w").value(),10),n=parseInt(O.find("#h").value(),10);e.preventDefault(),E(s.resize,t,n)(),_()}).on("show",v),V=D([{text:"Back",onclick:_},{type:"spacer",flex:1},{icon:"fliph",tooltip:"Flip horizontally",onclick:N(s.flip,"h")},{icon:"flipv",tooltip:"Flip vertically",onclick:N(s.flip,"v")},{icon:"rotateleft",tooltip:"Rotate counterclockwise",onclick:N(s.rotate,-90)},{icon:"rotateright",tooltip:"Rotate clockwise",onclick:N(s.rotate,90)},{type:"spacer",flex:1},{text:"Apply",subtype:"primary",onclick:S}]).hide().on("show",v),Y=L("Invert",s.invert),te=L("Sharpen",s.sharpen),ne=L("Emboss",s.emboss),X=M("Brightness",s.brightness,0,-1,1),K=M("Hue",s.hue,180,0,360),G=M("Saturate",s.saturate,0,-1,1),J=M("Contrast",s.contrast,0,-1,1),Q=M("Grayscale",s.grayscale,0,0,1),Z=M("Sepia",s.sepia,0,0,1),ee=P("Colorize",s.colorize),re=M("Gamma",s.gamma,0,-1,1),ie=M("Exposure",s.exposure,1,0,2),F=D([{text:"Back",onclick:_},{type:"spacer",flex:1},{text:"hue",icon:"hue",onclick:b(K)},{text:"saturate",icon:"saturate",onclick:b(G)},{text:"sepia",icon:"sepia",onclick:b(Z)},{text:"emboss",icon:"emboss",onclick:b(ne)},{text:"exposure",icon:"exposure",onclick:b(ie)},{type:"spacer",flex:1}]).hide(),I=D([{tooltip:"Crop",icon:"crop",onclick:b(U)},{tooltip:"Resize",icon:"resize2",onclick:b(W)},{tooltip:"Orientation",icon:"orientation",onclick:b(V)},{tooltip:"Brightness",icon:"sun",onclick:b(X)},{tooltip:"Sharpen",icon:"sharpen",onclick:b(te)},{tooltip:"Contrast",icon:"contrast",onclick:b(J)},{tooltip:"Color levels",icon:"drop",onclick:b(ee)},{tooltip:"Gamma",icon:"gamma",onclick:b(re)},{tooltip:"Invert",icon:"invert",onclick:b(Y)}]),$=new a({flex:1,imageSrc:n.url}),q=new o({layout:"flex",direction:"column",border:"0 1 0 0",padding:5,spacing:5,items:[{type:"button",icon:"undo",tooltip:"Undo",name:"undo",onclick:R},{type:"button",icon:"redo",tooltip:"Redo",name:"redo",onclick:A},{type:"button",icon:"zoomin",tooltip:"Zoom in",onclick:k},{type:"button",icon:"zoomout",tooltip:"Zoom out",onclick:T}]}),j=new o({type:"container",layout:"flex",direction:"row",align:"stretch",flex:1,items:[q,$]}),oe=[I,U,W,V,F,Y,X,K,G,J,Q,Z,ee,te,ne,re,ie],O=r.create("window",{layout:"flex",direction:"column",align:"stretch",minWidth:Math.min(e.DOM.getViewPort().w,800),minHeight:Math.min(e.DOM.getViewPort().h,650),title:"Edit image",items:oe.concat([j]),buttons:[{text:"Save",name:"save",subtype:"primary",onclick:B},{text:"Cancel",onclick:"close"}]}),O.renderTo(document.body).reflow(),O.on("close",function(){h(),f(ue.data),ue=null,z=null}),ue.add(n),g(),$.on("load",function(){ae=$.imageSize().w,se=$.imageSize().h,le=se/ae,ce=ae/se,O.find("#w").value(ae),O.find("#h").value(se)}),$.on("crop",w)}function p(e){return new n(function(t,n){h(u(e),t,n)})}return{edit:p}}),a("a",[],function(){function e(e){function t(e){return/^[0-9\.]+px$/.test(e)}var n,r;return n=e.style.width,r=e.style.height,n||r?t(n)&&t(r)?{w:parseInt(n,10),h:parseInt(r,10)}:null:(n=e.width,r=e.height,n&&r?{w:parseInt(n,10),h:parseInt(r,10)}:null)}function t(e,t){var n,r;t&&(n=e.style.width,r=e.style.height,(n||r)&&(e.style.width=t.w+"px",e.style.height=t.h+"px",e.removeAttribute("data-mce-style")),n=e.width,r=e.height,(n||r)&&(e.setAttribute("width",t.w),e.setAttribute("height",t.h)))}function n(e){return{w:e.naturalWidth,h:e.naturalHeight}}return{getImageSize:e,setImageSize:t,getNaturalImageSize:n}}),a("l",["3","5"],function(e,t){var n=function(e){return null!==e&&void 0!==e},r=function(e,t){var r;return r=t.reduce(function(e,t){return n(e)?e[t]:void 0},e),n(r)?r:null},i=function(n,r){return new e(function(e){var i;i=new XMLHttpRequest,i.onreadystatechange=function(){4===i.readyState&&e({status:i.status,blob:this.response})},i.open("GET",n,!0),t.each(r,function(e,t){i.setRequestHeader(t,e)}),i.responseType="blob",i.send()})},o=function(t){return new e(function(e){var n=new FileReader;n.onload=function(t){var n=t.target;e(n.result)},n.readAsText(t)})},a=function(e){var t;try{t=JSON.parse(e)}catch(n){}return t};return{traverse:r,readBlob:o,requestUrlAsBlob:i,parseJson:a}}),a("b",["3","5","l"],function(e,t,n){function r(t){return n.requestUrlAsBlob(t,{}).then(function(t){return t.status>=400?o(t.status):e.resolve(t.blob)})}var i=function(e){return 400===e||403===e||500===e},o=function(t){return e.reject("ImageProxy HTTP error: "+t)},a=function(t){e.reject("ImageProxy Service error: "+t)},s=function(e,t){return n.readBlob(t).then(function(e){var t=n.parseJson(e),r=n.traverse(t,["error","type"]);return a(r?r:"Invalid JSON")})},l=function(e,t){return i(e)?s(e,t):o(e)},c=function(t,r){return n.requestUrlAsBlob(t,{"Content-Type":"application/json;charset=UTF-8","tiny-api-key":r}).then(function(t){return t.status>=400?l(t.status,t.blob):e.resolve(t.blob)})},u=function(e,t){return t?c(e,t):r(e)};return{getUrl:u}}),a("0",["1","2","3","4","5","6","7","8","9","a","b"],function(e,t,n,r,i,o,a,s,l,c,u){var d=function(e){function d(t){e.notificationManager.open({text:t,type:"error"})}function f(){return e.selection.getNode()}function h(){return"imagetools"+D++}function p(t){var n=t.src;return 0===n.indexOf("data:")||0===n.indexOf("blob:")||new r(n).host===e.documentBaseURI.host}function m(t){return-1!==i.inArray(e.settings.imagetools_cors_hosts,new r(t.src).host)}function g(){return e.settings.api_key||e.settings.imagetools_api_key}function v(t){var n,r=t.src;return m(t)?u.getUrl(t.src,null):p(t)?s.imageToBlob(t):(r=e.settings.imagetools_proxy,r+=(-1===r.indexOf("?")?"?":"&")+"url="+encodeURIComponent(t.src),n=g(),u.getUrl(r,n))}function y(){var t;return t=e.editorUpload.blobCache.getByUri(f().src),t?t.blob():v(f())}function b(){A=o.setEditorTimeout(e,function(){e.editorUpload.uploadImagesAuto()},3e4)}function C(){clearTimeout(A)}function x(t,n){return s.blobToDataUri(t).then(function(i){var o,a,s,l,c;return c=f(),o=h(),s=e.editorUpload.blobCache,a=r.parseDataUri(i).data,l=s.create(o,t,a),s.add(l),e.undoManager.transact(function(){function t(){e.$(c).off("load",t),e.nodeChanged(),n?e.editorUpload.uploadImagesAuto():(C(),b())}e.$(c).on("load",t),e.$(c).attr({src:l.blobUri()}).removeAttr("data-mce-src")}),l})}function w(t){return function(){return e._scanForImages().then(y).then(t).then(x,d)}}function N(e){return function(){return w(function(t){var n=c.getImageSize(f());return n&&c.setImageSize(f(),{w:n.h,h:n.w}),a.rotate(t,e)})()}}function E(e){return function(){return w(function(t){return a.flip(t,e)})()}}function _(){var e=f(),t=c.getNaturalImageSize(e),r=function(r){return new n(function(n){s.blobToImage(r).then(function(i){var o=c.getNaturalImageSize(i);t.w==o.w&&t.h==o.h||c.getImageSize(e)&&c.setImageSize(e,o),URL.revokeObjectURL(i.src),n(r)})})},i=function(e){return l.edit(e).then(r).then(function(e){x(e,!0)},function(){})};e&&v(e).then(i,d)}function S(){e.addButton("rotateleft",{title:"Rotate counterclockwise",onclick:N(-90)}),e.addButton("rotateright",{title:"Rotate clockwise",onclick:N(90)}),e.addButton("flipv",{title:"Flip vertically",onclick:E("v")}),e.addButton("fliph",{title:"Flip horizontally",onclick:E("h")}),e.addButton("editimage",{title:"Edit image",onclick:_}),e.addButton("imageoptions",{title:"Image options",icon:"options",cmd:"mceImage"})}function k(){e.on("NodeChange",function(t){B&&B.src!=t.element.src&&(C(),e.editorUpload.uploadImagesAuto(),B=void 0),T(t.element)&&(B=t.element)})}function T(t){var n=e.dom.is(t,"img:not([data-mce-object],[data-mce-placeholder])");return n&&(p(t)||m(t)||e.settings.imagetools_proxy)}function R(){var t=e.settings.imagetools_toolbar;t||(t="rotateleft rotateright | flipv fliph | crop editimage imageoptions"),e.addContextToolbar(T,t)}var A,B,D=0;t.fileApi&&(S(),R(),k(),e.addCommand("mceEditImage",_))};return e.add("imagetools",d),function(){}}),r("0")()}();