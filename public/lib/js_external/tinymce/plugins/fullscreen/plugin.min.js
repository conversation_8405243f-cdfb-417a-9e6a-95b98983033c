tinymce.PluginManager.add("fullscreen",function(e){function t(){var e,t,n=window,r=document,i=r.body;return i.offsetWidth&&(e=i.offsetWidth,t=i.offsetHeight),n.innerWidth&&n.innerHeight&&(e=n.innerWidth,t=n.innerHeight),{w:e,h:t}}function n(){var e=tinymce.DOM.getViewPort();return{x:e.x,y:e.y}}function r(e){scrollTo(e.x,e.y)}function i(){function i(){f.setStyle(m,"height",t().h-(p.clientHeight-m.clientHeight))}var h,p,m,g,v=document.body,y=document.documentElement;d=!d,p=e.getContainer(),h=p.style,m=e.getContentAreaContainer().firstChild,g=m.style,d?(u=n(),o=g.width,a=g.height,g.width=g.height="100%",l=h.width,c=h.height,h.width=h.height="",f.addClass(v,"mce-fullscreen"),f.addClass(y,"mce-fullscreen"),f.addClass(p,"mce-fullscreen"),f.bind(window,"resize",i),i(),s=i):(g.width=o,g.height=a,l&&(h.width=l),c&&(h.height=c),f.removeClass(v,"mce-fullscreen"),f.removeClass(y,"mce-fullscreen"),f.removeClass(p,"mce-fullscreen"),f.unbind(window,"resize",s),r(u)),e.fire("FullscreenStateChanged",{state:d})}var o,a,s,l,c,u,d=!1,f=tinymce.DOM;return e.settings.inline?void 0:(e.on("init",function(){e.addShortcut("Ctrl+Shift+F","",i)}),e.on("remove",function(){s&&f.unbind(window,"resize",s)}),e.addCommand("mceFullScreen",i),e.addMenuItem("fullscreen",{text:"Fullscreen",shortcut:"Meta+Alt+F",selectable:!0,onClick:function(){i(),e.focus()},onPostRender:function(){var t=this;e.on("FullscreenStateChanged",function(e){t.active(e.state)})},context:"view"}),e.addButton("fullscreen",{tooltip:"Fullscreen",shortcut:"Meta+Alt+F",onClick:i,onPostRender:function(){var t=this;e.on("FullscreenStateChanged",function(e){t.active(e.state)})}}),{isFullscreen:function(){return d}})});