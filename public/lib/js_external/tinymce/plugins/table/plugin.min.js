!function(e,t){"use strict";function n(e,t){for(var n,r=[],a=0;a<e.length;++a){if(n=o[e[a]]||i(e[a]),!n)throw"module definition dependecy not found: "+e[a];r.push(n)}t.apply(null,r)}function r(e,r,i){if("string"!=typeof e)throw"invalid module definition, module id must be defined and be a string";if(r===t)throw"invalid module definition, dependencies must be specified";if(i===t)throw"invalid module definition, definition function must be specified";n(r,function(){o[e]=i.apply(null,arguments)})}function i(t){for(var n=e,r=t.split(/[.\/]/),i=0;i<r.length;++i){if(!n[r[i]])return;n=n[r[i]]}return n}var o={};r("tinymce/tableplugin/Utils",["tinymce/Env"],function(e){function t(e,t){return parseInt(e.getAttribute(t)||1,10)}function n(t){(!e.ie||e.ie>9)&&(t.hasChildNodes()||(t.innerHTML='<br data-mce-bogus="1" />'))}return{getSpanVal:t,paddCell:n}}),r("tinymce/tableplugin/TableGrid",["tinymce/util/Tools","tinymce/Env","tinymce/tableplugin/Utils"],function(e,n,r){var i=e.each,o=r.getSpanVal;return function(a,s,l){function c(){a.$("td[data-mce-selected],th[data-mce-selected]").removeAttr("data-mce-selected")}function u(e){return e===a.getBody()}function d(t,n){return t?(n=e.map(n.split(","),function(e){return e.toLowerCase()}),e.grep(t.childNodes,function(t){return-1!==e.inArray(n,t.nodeName.toLowerCase())})):[]}function f(){var e=0;z=[],U=0,i(["thead","tbody","tfoot"],function(t){var n=d(s,t)[0],r=d(n,"tr");i(r,function(n,r){r+=e,i(d(n,"td,th"),function(e,n){var i,a,s,l;if(z[r])for(;z[r][n];)n++;for(s=o(e,"rowspan"),l=o(e,"colspan"),a=r;r+s>a;a++)for(z[a]||(z[a]=[]),i=n;n+l>i;i++)z[a][i]={part:t,real:a==r&&i==n,elm:e,rowspan:s,colspan:l};U=Math.max(U,n+1)})}),e+=r.length})}function h(e){return a.fire("newrow",{node:e}),e}function p(e){return a.fire("newcell",{node:e}),e}function m(e,t){return e=e.cloneNode(t),e.removeAttribute("id"),e}function g(e,t){var n;return n=z[t],n?n[e]:void 0}function v(e,t,n){e&&(n=parseInt(n,10),1===n?e.removeAttribute(t,1):e.setAttribute(t,n,1))}function y(e){return e&&(!!q.getAttrib(e.elm,"data-mce-selected")||e==l)}function b(){var e=[];return i(s.rows,function(t){i(t.cells,function(n){return q.getAttrib(n,"data-mce-selected")||l&&n==l.elm?(e.push(t),!1):void 0})}),e}function C(){var e=q.createRng();u(s)||(e.setStartAfter(s),e.setEndAfter(s),$.setRng(e),q.remove(s))}function x(t){var o,s={};return a.settings.table_clone_elements!==!1&&(s=e.makeMap((a.settings.table_clone_elements||"strong em b i span font h1 h2 h3 h4 h5 h6 p div").toUpperCase(),/[ ,]/)),e.walk(t,function(e){var r;return 3==e.nodeType?(i(q.getParents(e.parentNode,null,t).reverse(),function(e){s[e.nodeName]&&(e=m(e,!1),o?r&&r.appendChild(e):o=r=e,r=e)}),r&&(r.innerHTML=n.ie&&n.ie<10?"&nbsp;":'<br data-mce-bogus="1" />'),!1):void 0},"childNodes"),t=m(t,!1),p(t),v(t,"rowSpan",1),v(t,"colSpan",1),o?t.appendChild(o):r.paddCell(t),t}function w(){var e,t=q.createRng();return i(q.select("tr",s),function(e){0===e.cells.length&&q.remove(e)}),0===q.select("tr",s).length?(t.setStartBefore(s),t.setEndBefore(s),$.setRng(t),void q.remove(s)):(i(q.select("thead,tbody,tfoot",s),function(e){0===e.rows.length&&q.remove(e)}),f(),void(W&&(e=z[Math.min(z.length-1,W.y)],e&&($.select(e[Math.min(e.length-1,W.x)].elm,!0),$.collapse(!0)))))}function N(e,t,n,r){var i,o,a,s,l;for(i=z[t][e].elm.parentNode,a=1;n>=a;a++)if(i=q.getNext(i,"tr")){for(o=e;o>=0;o--)if(l=z[t+a][o].elm,l.parentNode==i){for(s=1;r>=s;s++)q.insertAfter(x(l),l);break}if(-1==o)for(s=1;r>=s;s++)i.insertBefore(x(i.cells[0]),i.cells[0])}}function E(){i(z,function(e,t){i(e,function(e,n){var r,i,a;if(y(e)&&(e=e.elm,r=o(e,"colspan"),i=o(e,"rowspan"),r>1||i>1)){for(v(e,"rowSpan",1),v(e,"colSpan",1),a=0;r-1>a;a++)q.insertAfter(x(e),e);N(n,t,i-1,r)}})})}function _(t,n,r){var o,a,s,l,c,u,d,h,p,m,b;if(t?(o=P(t),a=o.x,s=o.y,l=a+(n-1),c=s+(r-1)):(W=V=null,i(z,function(e,t){i(e,function(e,n){y(e)&&(W||(W={x:n,y:t}),V={x:n,y:t})})}),W&&(a=W.x,s=W.y,l=V.x,c=V.y)),h=g(a,s),p=g(l,c),h&&p&&h.part==p.part){for(E(),f(),h=g(a,s).elm,v(h,"colSpan",l-a+1),v(h,"rowSpan",c-s+1),d=s;c>=d;d++)for(u=a;l>=u;u++)z[d]&&z[d][u]&&(t=z[d][u].elm,t!=h&&(m=e.grep(t.childNodes),i(m,function(e){h.appendChild(e)}),m.length&&(m=e.grep(h.childNodes),b=0,i(m,function(e){"BR"==e.nodeName&&b++<m.length-1&&h.removeChild(e)})),q.remove(t)));w()}}function S(e){var n,r,a,s,l,c,u,d,f,p;if(i(z,function(t,r){return i(t,function(t){return y(t)&&(t=t.elm,l=t.parentNode,c=h(m(l,!1)),n=r,e)?!1:void 0}),e?!n:void 0}),n!==t){for(s=0,p=0;s<z[0].length;s+=p)if(z[n][s]&&(r=z[n][s].elm,p=o(r,"colspan"),r!=a)){if(e){if(n>0&&z[n-1][s]&&(d=z[n-1][s].elm,f=o(d,"rowSpan"),f>1)){v(d,"rowSpan",f+1);continue}}else if(f=o(r,"rowspan"),f>1){v(r,"rowSpan",f+1);continue}u=x(r),v(u,"colSpan",r.colSpan),c.appendChild(u),a=r}c.hasChildNodes()&&(e?l.parentNode.insertBefore(c,l):q.insertAfter(c,l))}}function k(e){var t,n;i(z,function(n){return i(n,function(n,r){return y(n)&&(t=r,e)?!1:void 0}),e?!t:void 0}),i(z,function(r,i){var a,s,l;r[t]&&(a=r[t].elm,a!=n&&(l=o(a,"colspan"),s=o(a,"rowspan"),1==l?e?(a.parentNode.insertBefore(x(a),a),N(t,i,s-1,l)):(q.insertAfter(x(a),a),N(t,i,s-1,l)):v(a,"colSpan",a.colSpan+1),n=a))})}function T(t){return e.grep(R(t),y)}function R(e){var t=[];return i(e,function(e){i(e,function(e){t.push(e)})}),t}function A(){var t=[];if(u(s)){if(1==z[0].length)return;if(T(z).length==R(z).length)return}i(z,function(n){i(n,function(n,r){y(n)&&-1===e.inArray(t,r)&&(i(z,function(e){var t,n=e[r].elm;t=o(n,"colSpan"),t>1?v(n,"colSpan",t-1):q.remove(n)}),t.push(r))})}),w()}function B(){function e(e){var t,n;i(e.cells,function(e){var n=o(e,"rowSpan");n>1&&(v(e,"rowSpan",n-1),t=P(e),N(t.x,t.y,1,1))}),t=P(e.cells[0]),i(z[t.y],function(e){var t;e=e.elm,e!=n&&(t=o(e,"rowSpan"),1>=t?q.remove(e):v(e,"rowSpan",t-1),n=e)})}var t;t=b(),u(s)&&t.length==s.rows.length||(i(t.reverse(),function(t){e(t)}),w())}function D(){var e=b();if(!u(s)||e.length!=s.rows.length)return q.remove(e),w(),e}function L(){var e=b();return i(e,function(t,n){e[n]=m(t,!0)}),e}function M(e,t){var n=b(),r=n[t?0:n.length-1],o=r.cells.length;e&&(i(z,function(e){var t;return o=0,i(e,function(e){e.real&&(o+=e.colspan),e.elm.parentNode==r&&(t=1)}),t?!1:void 0}),t||e.reverse(),i(e,function(e){var n,i,a=e.cells.length;for(h(e),n=0;a>n;n++)i=e.cells[n],p(i),v(i,"colSpan",1),v(i,"rowSpan",1);for(n=a;o>n;n++)e.appendChild(p(x(e.cells[a-1])));for(n=o;a>n;n++)q.remove(e.cells[n]);t?r.parentNode.insertBefore(e,r):q.insertAfter(e,r)}),c())}function P(e){var t;return i(z,function(n,r){return i(n,function(n,i){return n.elm==e?(t={x:i,y:r},!1):void 0}),!t}),t}function H(e){W=P(e)}function O(){var e,t;return e=t=0,i(z,function(n,r){i(n,function(n,i){var o,a;y(n)&&(n=z[r][i],i>e&&(e=i),r>t&&(t=r),n.real&&(o=n.colspan-1,a=n.rowspan-1,o&&i+o>e&&(e=i+o),a&&r+a>t&&(t=r+a)))})}),{x:e,y:t}}function I(e){var t,n,r,i,o,a,s,l,u,d;if(V=P(e),W&&V){for(t=Math.min(W.x,V.x),n=Math.min(W.y,V.y),r=Math.max(W.x,V.x),i=Math.max(W.y,V.y),o=r,a=i,d=n;i>=d;d++)for(u=t;r>=u;u++)e=z[d][u],e.real&&(s=e.colspan-1,l=e.rowspan-1,s&&u+s>o&&(o=u+s),l&&d+l>a&&(a=d+l));for(c(),d=n;a>=d;d++)for(u=t;o>=u;u++)z[d][u]&&q.setAttrib(z[d][u].elm,"data-mce-selected","1")}}function F(e,t){var n,r,i;n=P(e),r=n.y*U+n.x;do{if(r+=t,i=g(r%U,Math.floor(r/U)),!i)break;if(i.elm!=e)return $.select(i.elm,!0),q.isEmpty(i.elm)&&$.collapse(!0),!0}while(i.elm==e);return!1}var z,U,W,V,$=a.selection,q=$.dom;s=s||q.getParent($.getStart(!0),"table"),f(),l=l||q.getParent($.getStart(!0),"th,td"),l&&(W=P(l),V=O(),l=g(W.x,W.y)),e.extend(this,{deleteTable:C,split:E,merge:_,insertRow:S,insertCol:k,deleteCols:A,deleteRows:B,cutRows:D,copyRows:L,pasteRows:M,getPos:P,setStartCell:H,setEndCell:I,moveRelIdx:F,refresh:f})}}),r("tinymce/tableplugin/Quirks",["tinymce/util/VK","tinymce/util/Delay","tinymce/Env","tinymce/util/Tools","tinymce/tableplugin/Utils"],function(e,t,n,r,i){var o=r.each,a=i.getSpanVal;return function(s){function l(){function n(n){function r(e,t){var r=e?"previousSibling":"nextSibling",o=s.dom.getParent(t,"tr"),a=o[r];if(a)return v(s,t,a,e),n.preventDefault(),!0;var l=s.dom.getParent(o,"table"),d=o.parentNode,f=d.nodeName.toLowerCase();if("tbody"===f||f===(e?"tfoot":"thead")){var h=i(e,l,d,"tbody");if(null!==h)return c(e,h,t)}return u(e,o,r,l)}function i(e,t,n,r){var i=s.dom.select(">"+r,t),o=i.indexOf(n);if(e&&0===o||!e&&o===i.length-1)return l(e,t);if(-1===o){var a="thead"===n.tagName.toLowerCase()?0:i.length-1;return i[a]}return i[o+(e?-1:1)]}function l(e,t){var n=e?"thead":"tfoot",r=s.dom.select(">"+n,t);return 0!==r.length?r[0]:null}function c(e,t,r){var i=d(t,e);return i&&v(s,r,i,e),n.preventDefault(),!0}function u(e,t,i,o){var a=o[i];if(a)return f(a),!0;var l=s.dom.getParent(o,"td,th");if(l)return r(e,l,n);var c=d(t,!e);return f(c),n.preventDefault(),!1}function d(e,t){var n=e&&e[t?"lastChild":"firstChild"];return n&&"BR"===n.nodeName?s.dom.getParent(n,"td,th"):n}function f(e){s.selection.setCursorLocation(e,0)}function h(){return C==e.UP||C==e.DOWN}function p(e){var t=e.selection.getNode(),n=e.dom.getParent(t,"tr");return null!==n}function m(e){for(var t=0,n=e;n.previousSibling;)n=n.previousSibling,t+=a(n,"colspan");return t}function g(e,t){var n=0,r=0;return o(e.children,function(e,i){return n+=a(e,"colspan"),r=i,n>t?!1:void 0}),r}function v(e,t,n,r){var i=m(s.dom.getParent(t,"td,th")),o=g(n,i),a=n.childNodes[o],l=d(a,r);f(l||a)}function y(e){var t=s.selection.getNode(),n=s.dom.getParent(t,"td,th"),r=s.dom.getParent(e,"td,th");return n&&n!==r&&b(n,r)}function b(e,t){return s.dom.getParent(e,"TABLE")===s.dom.getParent(t,"TABLE")}var C=n.keyCode;if(h()&&p(s)){var x=s.selection.getNode();t.setEditorTimeout(s,function(){y(x)&&r(!n.shiftKey&&C===e.UP,x,n)},0)}}s.on("KeyDown",function(e){n(e)})}function c(){function e(e,t){var n,r=t.ownerDocument,i=r.createRange();return i.setStartBefore(t),i.setEnd(e.endContainer,e.endOffset),n=r.createElement("body"),n.appendChild(i.cloneContents()),0===n.innerHTML.replace(/<(br|img|object|embed|input|textarea)[^>]*>/gi,"-").replace(/<[^>]+>/g,"").length}s.on("KeyDown",function(t){var n,r,i=s.dom;37!=t.keyCode&&38!=t.keyCode||(n=s.selection.getRng(),r=i.getParent(n.startContainer,"table"),r&&s.getBody().firstChild==r&&e(n,r)&&(n=i.createRng(),n.setStartBefore(r),n.setEndBefore(r),s.selection.setRng(n),t.preventDefault()))})}function u(){s.on("KeyDown SetContent VisualAid",function(){var e;for(e=s.getBody().lastChild;e;e=e.previousSibling)if(3==e.nodeType){if(e.nodeValue.length>0)break}else if(1==e.nodeType&&("BR"==e.tagName||!e.getAttribute("data-mce-bogus")))break;e&&"TABLE"==e.nodeName&&(s.settings.forced_root_block?s.dom.add(s.getBody(),s.settings.forced_root_block,s.settings.forced_root_block_attrs,n.ie&&n.ie<10?"&nbsp;":'<br data-mce-bogus="1" />'):s.dom.add(s.getBody(),"br",{"data-mce-bogus":"1"}))}),s.on("PreProcess",function(e){var t=e.node.lastChild;t&&("BR"==t.nodeName||1==t.childNodes.length&&("BR"==t.firstChild.nodeName||"\xa0"==t.firstChild.nodeValue))&&t.previousSibling&&"TABLE"==t.previousSibling.nodeName&&s.dom.remove(t)})}function d(){function e(e,t,n,r){var i,o,a,s=3,l=e.dom.getParent(t.startContainer,"TABLE");return l&&(i=l.parentNode),o=t.startContainer.nodeType==s&&0===t.startOffset&&0===t.endOffset&&r&&("TR"==n.nodeName||n==i),a=("TD"==n.nodeName||"TH"==n.nodeName)&&!r,o||a}function t(){var t=s.selection.getRng(),n=s.selection.getNode(),r=s.dom.getParent(t.startContainer,"TD,TH");if(e(s,t,n,r)){r||(r=n);for(var i=r.lastChild;i.lastChild;)i=i.lastChild;3==i.nodeType&&(t.setEnd(i,i.data.length),s.selection.setRng(t))}}s.on("KeyDown",function(){t()}),s.on("MouseDown",function(e){2!=e.button&&t()})}function f(){function t(e){s.selection.select(e,!0),s.selection.collapse(!0)}function n(e){s.$(e).empty(),i.paddCell(e)}s.on("keydown",function(i){if((i.keyCode==e.DELETE||i.keyCode==e.BACKSPACE)&&!i.isDefaultPrevented()){var o,a,l,c;if(o=s.dom.getParent(s.selection.getStart(),"table")){if(a=s.dom.select("td,th",o),l=r.grep(a,function(e){return!!s.dom.getAttrib(e,"data-mce-selected")}),0===l.length)return c=s.dom.getParent(s.selection.getStart(),"td,th"),void(s.selection.isCollapsed()&&c&&s.dom.isEmpty(c)&&(i.preventDefault(),n(c),t(c)));i.preventDefault(),s.undoManager.transact(function(){a.length==l.length?s.execCommand("mceTableDelete"):(r.each(l,n),t(l[0]))})}}})}f(),n.webkit&&(l(),d()),n.gecko&&(c(),u()),n.ie>9&&(c(),u())}}),r("tinymce/tableplugin/CellSelection",["tinymce/tableplugin/TableGrid","tinymce/dom/TreeWalker","tinymce/util/Tools"],function(e,t,n){return function(r,i){function o(e){r.getBody().style.webkitUserSelect="",(e||p)&&(r.$("td[data-mce-selected],th[data-mce-selected]").removeAttr("data-mce-selected"),p=!1)}function a(e,t){return e&&t?e===h.getParent(t,"table"):!1}function s(t){var n,o,s=t.target;if(!f&&s!==d&&(d=s,u&&c)){if(o=h.getParent(s,"td,th"),a(u,o)||(o=h.getParent(u,"td,th")),c===o&&!p)return;if(i(!0),a(u,o)){t.preventDefault(),l||(l=new e(r,u,c),r.getBody().style.webkitUserSelect="none"),l.setEndCell(o),p=!0,n=r.selection.getSel();try{n.removeAllRanges?n.removeAllRanges():n.empty()}catch(m){}}}}var l,c,u,d,f,h=r.dom,p=!0,m=function(){c=l=u=d=null,i(!1)};return r.on("SelectionChange",function(e){p&&e.stopImmediatePropagation()},!0),r.on("MouseDown",function(e){2==e.button||f||(o(),c=h.getParent(e.target,"td,th"),u=h.getParent(c,"table"))}),r.on("mouseover",s),r.on("remove",function(){h.unbind(r.getDoc(),"mouseover",s),o()}),r.on("MouseUp",function(){function e(e,r){var o=new t(e,e);do{if(3==e.nodeType&&0!==n.trim(e.nodeValue).length)return void(r?i.setStart(e,0):i.setEnd(e,e.nodeValue.length));if("BR"==e.nodeName)return void(r?i.setStartBefore(e):i.setEndBefore(e))}while(e=r?o.next():o.prev())}var i,o,a,s,u,d=r.selection;if(c){if(l&&(r.getBody().style.webkitUserSelect=""),o=h.select("td[data-mce-selected],th[data-mce-selected]"),o.length>0){i=h.createRng(),s=o[0],i.setStartBefore(s),i.setEndAfter(s),e(s,1),a=new t(s,h.getParent(o[0],"table"));do if("TD"==s.nodeName||"TH"==s.nodeName){if(!h.getAttrib(s,"data-mce-selected"))break;u=s}while(s=a.next());e(u),d.setRng(i)}r.nodeChanged(),m()}}),r.on("KeyUp Drop SetContent",function(e){o("setcontent"==e.type),m(),f=!1}),r.on("ObjectResizeStart ObjectResized",function(e){f="objectresized"!=e.type}),{clear:o}}}),r("tinymce/tableplugin/Dialogs",["tinymce/util/Tools","tinymce/Env"],function(e,t){var n=e.each;return function(r){function i(){var e=r.settings.color_picker_callback;return e?function(){var t=this;e.call(r,function(e){t.value(e).fire("change")},t.value())}:void 0}function o(e){return{title:"Advanced",type:"form",defaults:{onchange:function(){d(e,this.parents().reverse()[0],"style"==this.name())}},items:[{label:"Style",name:"style",type:"textbox"},{type:"form",padding:0,formItemDefaults:{layout:"grid",alignH:["start","right"]},defaults:{size:7},items:[{label:"Border color",type:"colorbox",name:"borderColor",onaction:i()},{label:"Background color",type:"colorbox",name:"backgroundColor",onaction:i()}]}]}}function a(e){return e?e.replace(/px$/,""):""}function s(e){return/^[0-9]+$/.test(e)&&(e+="px"),e}function l(e){n("left center right".split(" "),function(t){r.formatter.remove("align"+t,{},e)})}function c(e){n("top middle bottom".split(" "),function(t){r.formatter.remove("valign"+t,{},e)})}function u(t,n,r){function i(t,r){return r=r||[],e.each(t,function(e){var t={text:e.text||e.title};e.menu?t.menu=i(e.menu):(t.value=e.value,n&&n(t)),r.push(t)}),r}return i(t,r||[])}function d(e,t,n){var r=t.toJSON(),i=e.parseStyle(r.style);n?(t.find("#borderColor").value(i["border-color"]||"")[0].fire("change"),t.find("#backgroundColor").value(i["background-color"]||"")[0].fire("change")):(i["border-color"]=r.borderColor,i["background-color"]=r.backgroundColor),t.find("#style").value(e.serializeStyle(e.parseStyle(e.serializeStyle(i))))}function f(e,t,n){var r=e.parseStyle(e.getAttrib(n,"style"));r["border-color"]&&(t.borderColor=r["border-color"]),r["background-color"]&&(t.backgroundColor=r["background-color"]),t.style=e.serializeStyle(r)}function h(e,t,r){var i=e.parseStyle(e.getAttrib(t,"style"));n(r,function(e){i[e.name]=e.value}),e.setAttrib(t,"style",e.serializeStyle(e.parseStyle(e.serializeStyle(i))))}var p=this;p.tableProps=function(){p.table(!0)},p.table=function(i){function c(){function n(e,t,r){if("TD"===e.tagName||"TH"===e.tagName)x.setStyle(e,t,r);else if(e.children)for(var i=0;i<e.children.length;i++)n(e.children[i],t,r)}var i;d(x,this),w=e.extend(w,this.toJSON()),w["class"]===!1&&delete w["class"],r.undoManager.transact(function(){if(m||(m=r.plugins.table.insertTable(w.cols||1,w.rows||1)),r.dom.setAttribs(m,{style:w.style,"class":w["class"]}),r.settings.table_style_by_css){if(C=[],C.push({name:"border",value:w.border}),C.push({name:"border-spacing",value:s(w.cellspacing)}),h(x,m,C),x.setAttribs(m,{"data-mce-border-color":w.borderColor,"data-mce-cell-padding":w.cellpadding,"data-mce-border":w.border}),m.children)for(var e=0;e<m.children.length;e++)n(m.children[e],"border",w.border),n(m.children[e],"padding",s(w.cellpadding))}else r.dom.setAttribs(m,{border:w.border,cellpadding:w.cellpadding,cellspacing:w.cellspacing});x.getAttrib(m,"width")&&!r.settings.table_style_by_css?x.setAttrib(m,"width",a(w.width)):x.setStyle(m,"width",s(w.width)),x.setStyle(m,"height",s(w.height)),i=x.select("caption",m)[0],i&&!w.caption&&x.remove(i),!i&&w.caption&&(i=x.create("caption"),i.innerHTML=t.ie?"\xa0":'<br data-mce-bogus="1"/>',m.insertBefore(i,m.firstChild)),l(m),w.align&&r.formatter.apply("align"+w.align,{},m),r.focus(),r.addVisual()})}function p(e,t){function n(e,n){for(var r=0;r<n.length;r++){var i=x.getStyle(n[r],t);if("undefined"==typeof e&&(e=i),e!=i)return""}return e}var i,o=r.dom.select("td,th",e);return i=n(i,o)}var m,g,v,y,b,C,x=r.dom,w={};i===!0?(m=x.getParent(r.selection.getStart(),"table"),m&&(w={width:a(x.getStyle(m,"width")||x.getAttrib(m,"width")),height:a(x.getStyle(m,"height")||x.getAttrib(m,"height")),cellspacing:a(x.getStyle(m,"border-spacing")||x.getAttrib(m,"cellspacing")),cellpadding:x.getAttrib(m,"data-mce-cell-padding")||x.getAttrib(m,"cellpadding")||p(m,"padding"),border:x.getAttrib(m,"data-mce-border")||x.getAttrib(m,"border")||p(m,"border"),borderColor:x.getAttrib(m,"data-mce-border-color"),caption:!!x.select("caption",m)[0],"class":x.getAttrib(m,"class")},n("left center right".split(" "),function(e){r.formatter.matchNode(m,"align"+e)&&(w.align=e)}))):(g={label:"Cols",name:"cols"},v={label:"Rows",name:"rows"}),r.settings.table_class_list&&(w["class"]&&(w["class"]=w["class"].replace(/\s*mce\-item\-table\s*/g,"")),y={name:"class",type:"listbox",label:"Class",values:u(r.settings.table_class_list,function(e){e.value&&(e.textStyle=function(){return r.formatter.getCssText({block:"table",classes:[e.value]})})})}),b={type:"form",layout:"flex",direction:"column",labelGapCalc:"children",padding:0,items:[{type:"form",labelGapCalc:!1,padding:0,layout:"grid",columns:2,defaults:{type:"textbox",maxWidth:50},items:r.settings.table_appearance_options!==!1?[g,v,{label:"Width",name:"width"},{label:"Height",name:"height"},{label:"Cell spacing",name:"cellspacing"},{label:"Cell padding",name:"cellpadding"},{label:"Border",name:"border"},{label:"Caption",name:"caption",type:"checkbox"}]:[g,v,{label:"Width",name:"width"},{label:"Height",name:"height"}]},{label:"Alignment",name:"align",type:"listbox",text:"None",values:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},y]},r.settings.table_advtab!==!1?(f(x,w,m),r.windowManager.open({title:"Table properties",data:w,bodyType:"tabpanel",body:[{title:"General",type:"form",items:b},o(x)],onsubmit:c})):r.windowManager.open({title:"Table properties",data:w,body:b,onsubmit:c})},p.merge=function(e,t){r.windowManager.open({title:"Merge cells",body:[{label:"Cols",name:"cols",type:"textbox",value:"1",size:10},{label:"Rows",name:"rows",type:"textbox",value:"1",size:10}],onsubmit:function(){var n=this.toJSON();r.undoManager.transact(function(){e.merge(t,n.cols,n.rows)})}})},p.cell=function(){function t(){d(m,this),h=e.extend(h,this.toJSON()),r.undoManager.transact(function(){n(g,function(e){r.dom.setAttribs(e,{scope:h.scope,style:h.style,"class":h["class"]}),r.dom.setStyles(e,{width:s(h.width),height:s(h.height)}),h.type&&e.nodeName.toLowerCase()!=h.type&&(e=m.rename(e,h.type)),l(e),h.align&&r.formatter.apply("align"+h.align,{},e),c(e),h.valign&&r.formatter.apply("valign"+h.valign,{},e)}),r.focus()})}var i,h,p,m=r.dom,g=[];if(g=r.dom.select("td[data-mce-selected],th[data-mce-selected]"),i=r.dom.getParent(r.selection.getStart(),"td,th"),!g.length&&i&&g.push(i),i=i||g[0]){h={width:a(m.getStyle(i,"width")||m.getAttrib(i,"width")),height:a(m.getStyle(i,"height")||m.getAttrib(i,"height")),scope:m.getAttrib(i,"scope"),"class":m.getAttrib(i,"class")},h.type=i.nodeName.toLowerCase(),n("left center right".split(" "),function(e){r.formatter.matchNode(i,"align"+e)&&(h.align=e)}),n("top middle bottom".split(" "),function(e){r.formatter.matchNode(i,"valign"+e)&&(h.valign=e)}),r.settings.table_cell_class_list&&(p={name:"class",type:"listbox",label:"Class",values:u(r.settings.table_cell_class_list,function(e){e.value&&(e.textStyle=function(){return r.formatter.getCssText({block:"td",classes:[e.value]})})})});var v={type:"form",layout:"flex",direction:"column",labelGapCalc:"children",padding:0,items:[{type:"form",layout:"grid",columns:2,labelGapCalc:!1,padding:0,defaults:{type:"textbox",maxWidth:50},items:[{label:"Width",name:"width"},{label:"Height",name:"height"},{label:"Cell type",name:"type",type:"listbox",text:"None",minWidth:90,maxWidth:null,values:[{text:"Cell",value:"td"},{text:"Header cell",value:"th"}]},{label:"Scope",name:"scope",type:"listbox",text:"None",minWidth:90,maxWidth:null,values:[{text:"None",value:""},{text:"Row",value:"row"},{text:"Column",value:"col"},{text:"Row group",value:"rowgroup"},{text:"Column group",value:"colgroup"}]},{label:"H Align",name:"align",type:"listbox",text:"None",minWidth:90,maxWidth:null,values:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{label:"V Align",name:"valign",type:"listbox",text:"None",minWidth:90,maxWidth:null,values:[{text:"None",value:""},{text:"Top",value:"top"},{text:"Middle",value:"middle"},{text:"Bottom",value:"bottom"}]}]},p]};r.settings.table_cell_advtab!==!1?(f(m,h,i),r.windowManager.open({title:"Cell properties",bodyType:"tabpanel",data:h,body:[{title:"General",type:"form",items:v},o(m)],onsubmit:t})):r.windowManager.open({title:"Cell properties",data:h,body:v,onsubmit:t})}},p.row=function(){function t(){var t,i,o;d(v,this),m=e.extend(m,this.toJSON()),r.undoManager.transact(function(){var e=m.type;n(y,function(n){r.dom.setAttribs(n,{scope:m.scope,style:m.style,"class":m["class"]}),r.dom.setStyles(n,{height:s(m.height)}),e!=n.parentNode.nodeName.toLowerCase()&&(t=v.getParent(n,"table"),i=n.parentNode,o=v.select(e,t)[0],o||(o=v.create(e),t.firstChild?t.insertBefore(o,t.firstChild):t.appendChild(o)),o.appendChild(n),i.hasChildNodes()||v.remove(i)),l(n),m.align&&r.formatter.apply("align"+m.align,{},n)}),r.focus()})}var i,c,h,p,m,g,v=r.dom,y=[];i=r.dom.getParent(r.selection.getStart(),"table"),c=r.dom.getParent(r.selection.getStart(),"td,th"),n(i.rows,function(e){n(e.cells,function(t){return v.getAttrib(t,"data-mce-selected")||t==c?(y.push(e),!1):void 0})}),h=y[0],h&&(m={height:a(v.getStyle(h,"height")||v.getAttrib(h,"height")),scope:v.getAttrib(h,"scope"),"class":v.getAttrib(h,"class")},m.type=h.parentNode.nodeName.toLowerCase(),n("left center right".split(" "),function(e){r.formatter.matchNode(h,"align"+e)&&(m.align=e)}),r.settings.table_row_class_list&&(p={name:"class",type:"listbox",label:"Class",values:u(r.settings.table_row_class_list,function(e){e.value&&(e.textStyle=function(){return r.formatter.getCssText({block:"tr",classes:[e.value]})})})}),g={type:"form",columns:2,padding:0,defaults:{type:"textbox"},items:[{type:"listbox",name:"type",label:"Row type",text:"None",maxWidth:null,values:[{text:"Header",value:"thead"},{text:"Body",value:"tbody"},{text:"Footer",value:"tfoot"}]},{type:"listbox",name:"align",label:"Alignment",text:"None",maxWidth:null,values:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{label:"Height",name:"height"},p]},r.settings.table_row_advtab!==!1?(f(v,m,h),r.windowManager.open({title:"Row properties",data:m,bodyType:"tabpanel",body:[{title:"General",type:"form",items:g},o(v)],onsubmit:t})):r.windowManager.open({title:"Row properties",data:m,body:g,onsubmit:t}))}}}),r("tinymce/tableplugin/ResizeBars",["tinymce/util/Tools","tinymce/util/VK"],function(e,n){var r;return function(i){function o(e,t){return{index:e,y:i.dom.getPos(t).y}}function a(e,t){return{index:e,y:i.dom.getPos(t).y+t.offsetHeight}}function s(e,t){return{index:e,x:i.dom.getPos(t).x}}function l(e,t){return{index:e,x:i.dom.getPos(t).x+t.offsetWidth}}function c(){var e=i.getBody().dir;return"rtl"===e}function u(){return i.inline}function d(){return u?i.getBody().ownerDocument.body:i.getBody()}function f(e,t){return c()?l(e,t):s(e,t)}function h(e,t){return c()?s(e,t):l(e,t)}function p(e,t){return m(e,"width")/m(t,"width")*100}function m(e,t){var n=i.dom.getStyle(e,t,!0),r=parseInt(n,10);return r}function g(e){var t=m(e,"width"),n=m(e.parentElement,"width");return t/n*100}function v(e,t){var n=m(e,"width");return t/n*100}function y(e,t){var n=m(e.parentElement,"width");return t/n*100}function b(e,t,n){for(var r=[],i=1;i<n.length;i++){var o=n[i].element;r.push(e(i-1,o))}var a=n[n.length-1];return r.push(t(n.length-1,a.element)),r}function C(){var t=i.dom.select("."+fe,d());e.each(t,function(e){i.dom.remove(e)})}function x(e){C(),B(e)}function w(e,t,n,r,i,o,a,s){var l={"data-mce-bogus":"all","class":fe+" "+e,unselectable:"on","data-mce-resize":!1,style:"cursor: "+t+"; margin: 0; padding: 0; position: absolute; left: "+n+"px; top: "+r+"px; height: "+i+"px; width: "+o+"px; "};return l[a]=s,l}function N(t,n,r){e.each(t,function(e){var t=r.x,o=e.y-xe/2,a=xe,s=n;i.dom.add(d(),"div",w(he,pe,t,o,a,s,me,e.index))})}function E(t,n,r){e.each(t,function(e){var t=e.x-xe/2,o=r.y,a=n,s=xe;i.dom.add(d(),"div",w(ve,ye,t,o,a,s,be,e.index))})}function _(t){return e.map(t.rows,function(t){var n=e.map(t.cells,function(e){var t=e.hasAttribute("rowspan")?parseInt(e.getAttribute("rowspan"),10):1,n=e.hasAttribute("colspan")?parseInt(e.getAttribute("colspan"),10):1;return{element:e,rowspan:t,colspan:n}});return{element:t,cells:n}})}function S(n){function r(e,t){return e+","+t}function i(e,t){return s[r(e,t)]}function o(){var t=[];return e.each(l,function(e){t=t.concat(e.cells)}),t}function a(){return l}var s={},l=[],c=0,u=0;return e.each(n,function(n,i){var o=[];e.each(n.cells,function(e){for(var n=0;s[r(i,n)]!==t;)n++;for(var a={element:e.element,colspan:e.colspan,rowspan:e.rowspan,rowIndex:i,colIndex:n},l=0;l<e.colspan;l++)for(var d=0;d<e.rowspan;d++){var f=i+d,h=n+l;s[r(f,h)]=a,c=Math.max(c,f+1),u=Math.max(u,h+1)}o.push(a)}),l.push({element:n.element,cells:o})}),{grid:{maxRows:c,maxCols:u},getAt:i,getAllCells:o,getAllRows:a}}function k(e,t){for(var n=[],r=e;t>r;r++)n.push(r);return n}function T(e,t,n){for(var r,i=e(),o=0;o<i.length;o++)t(i[o])&&(r=i[o]);return r?r:n()}function R(t){var n=k(0,t.grid.maxCols),r=k(0,t.grid.maxRows);return e.map(n,function(e){function n(){for(var n=[],i=0;i<r.length;i++){var o=t.getAt(i,e);o&&o.colIndex===e&&n.push(o)}return n}function i(e){return 1===e.colspan}function o(){for(var n,i=0;i<r.length;i++)if(n=t.getAt(i,e))return n;return null}return T(n,i,o)})}function A(t){var n=k(0,t.grid.maxCols),r=k(0,t.grid.maxRows);return e.map(r,function(e){function r(){for(var r=[],i=0;i<n.length;i++){var o=t.getAt(e,i);o&&o.rowIndex===e&&r.push(o)}return r}function i(e){return 1===e.rowspan}function o(){return t.getAt(e,0)}return T(r,i,o)})}function B(e){var t=_(e),n=S(t),r=A(n),s=R(n),l=i.dom.getPos(e),c=r.length>0?b(o,a,r):[],u=s.length>0?b(f,h,s):[];N(c,e.offsetWidth,l),E(u,e.offsetHeight,l)}function D(e,t,n,r){if(0>t||t>=e.length-1)return"";var i=e[t];if(i)i={value:i,delta:0};else for(var o=e.slice(0,t).reverse(),a=0;a<o.length;a++)o[a]&&(i={value:o[a],delta:a+1});var s=e[t+1];if(s)s={value:s,delta:1};else for(var l=e.slice(t+1),c=0;c<l.length;c++)l[c]&&(s={value:l[c],delta:c+1});var u=s.delta-i.delta,d=Math.abs(s.value-i.value)/u;return n?d/m(r,"width")*100:d}function L(e,t){var n=i.dom.getStyle(e,t);return n||(n=i.dom.getAttrib(e,t)),n||(n=i.dom.getStyle(e,t,!0)),n}function M(e,t,n){var r=L(e,"width"),i=parseInt(r,10),o=t?p(e,n):m(e,"width");return(t&&!V(r)||!t&&!$(r))&&(i=0),!isNaN(i)&&i>0?i:o}function P(t,n,r){for(var i=R(t),o=e.map(i,function(e){return f(e.colIndex,e.element).x}),a=[],s=0;s<i.length;s++){var l=i[s].element.hasAttribute("colspan")?parseInt(i[s].element.getAttribute("colspan"),10):1,c=l>1?D(o,s):M(i[s].element,n,r);c=c?c:we,a.push(c)}return a}function H(e){var t=L(e,"height"),n=parseInt(t,10);return V(t)&&(n=0),!isNaN(n)&&n>0?n:m(e,"height")}function O(t){for(var n=A(t),r=e.map(n,function(e){return o(e.rowIndex,e.element).y}),i=[],a=0;a<n.length;a++){var s=n[a].element.hasAttribute("rowspan")?parseInt(n[a].element.getAttribute("rowspan"),10):1,l=s>1?D(r,a):H(n[a].element);l=l?l:Ne,i.push(l)}return i}function I(t,n,r,i,o){function a(t){return e.map(t,function(){return 0})}function s(){var e;if(o)e=[100-d[0]];else{var t=Math.max(i,d[0]+r);e=[t-d[0]]}return e}function l(e,t){var n,o=a(d.slice(0,e)),s=a(d.slice(t+1));if(r>=0){var l=Math.max(i,d[t]-r);n=o.concat([r,l-d[t]]).concat(s)}else{var c=Math.max(i,d[e]+r),u=d[e]-c;n=o.concat([c-d[e],u]).concat(s)}return n}function c(e,t){var n,o=a(d.slice(0,t));if(r>=0)n=o.concat([r]);else{var s=Math.max(i,d[t]+r);n=o.concat([s-d[t]])}return n}var u,d=t.slice(0);return u=0===t.length?[]:1===t.length?s():0===n?l(0,1):n>0&&n<t.length-1?l(n,n+1):n===t.length-1?c(n-1,n):[]}function F(e,t,n){for(var r=0,i=e;t>i;i++)r+=n[i];return r}function z(t,n){var r=t.getAllCells();return e.map(r,function(e){var t=F(e.colIndex,e.colIndex+e.colspan,n);return{element:e.element,width:t,colspan:e.colspan}})}function U(t,n){var r=t.getAllCells();return e.map(r,function(e){var t=F(e.rowIndex,e.rowIndex+e.rowspan,n);return{element:e.element,height:t,rowspan:e.rowspan}})}function W(t,n){var r=t.getAllRows();return e.map(r,function(e,t){return{element:e.element,height:n[t]}})}function V(e){return _e.test(e)}function $(e){return Se.test(e)}function q(t,n,r){function o(t,n){e.each(t,function(e){i.dom.setStyle(e.element,"width",e.width+n),i.dom.setAttrib(e.element,"width",null)})}function a(){return r<u.grid.maxCols-1?g(t):g(t)+y(t,n)}function s(){return r<u.grid.maxCols-1?m(t,"width"):m(t,"width")+n}function l(e,n,o){r!=u.grid.maxCols-1&&o||(i.dom.setStyle(t,"width",e+n),i.dom.setAttrib(t,"width",null))}for(var c=_(t),u=S(c),d=V(t.width)||V(t.style.width),f=P(u,d,t),h=d?v(t,n):n,p=I(f,r,h,we,d,t),b=[],C=0;C<p.length;C++)b.push(p[C]+f[C]);var x=z(u,b),w=d?"%":"px",N=d?a():s();i.undoManager.transact(function(){o(x,w),l(N,w,d)})}function j(t,n,r){for(var o=_(t),a=S(o),s=O(a),l=[],c=0,u=0;u<s.length;u++)l.push(u===r?n+s[u]:s[u]),c+=c[u];var d=U(a,l),f=W(a,l);i.undoManager.transact(function(){e.each(f,function(e){i.dom.setStyle(e.element,"height",e.height+"px"),i.dom.setAttrib(e.element,"height",null)}),e.each(d,function(e){i.dom.setStyle(e.element,"height",e.height+"px"),i.dom.setAttrib(e.element,"height",null)}),i.dom.setStyle(t,"height",c+"px"),i.dom.setAttrib(t,"height",null)})}function Y(){ae=setTimeout(function(){J()},200)}function X(){clearTimeout(ae)}function K(){var e=document.createElement("div");return e.setAttribute("style","margin: 0; padding: 0; position: fixed; left: 0px; top: 0px; height: 100%; width: 100%;"),e.setAttribute("data-mce-bogus","all"),e}function G(e,t){i.dom.bind(e,"mouseup",function(){J()}),i.dom.bind(e,"mousemove",function(e){X(),se&&t(e)}),i.dom.bind(e,"mouseout",function(){Y()})}function J(){if(i.dom.remove(le),
se){i.dom.removeClass(ce,Ee),se=!1;var e,t;if(Z(ce)){var n=parseInt(i.dom.getAttrib(ce,Ce),10),o=i.dom.getPos(ce).x;e=parseInt(i.dom.getAttrib(ce,be),10),t=c()?n-o:o-n,q(r,t,e)}else if(ee(ce)){var a=parseInt(i.dom.getAttrib(ce,ge),10),s=i.dom.getPos(ce).y;e=parseInt(i.dom.getAttrib(ce,me),10),t=s-a,j(r,t,e)}x(r),i.nodeChanged()}}function Q(e,t){le=le?le:K(),se=!0,i.dom.addClass(e,Ee),ce=e,G(le,t),i.dom.add(d(),le)}function Z(e){return i.dom.hasClass(e,ve)}function ee(e){return i.dom.hasClass(e,he)}function te(e){ue=ue!==t?ue:e.clientX;var n=e.clientX-ue;ue=e.clientX;var r=i.dom.getPos(ce).x;i.dom.setStyle(ce,"left",r+n+"px")}function ne(e){de=de!==t?de:e.clientY;var n=e.clientY-de;de=e.clientY;var r=i.dom.getPos(ce).y;i.dom.setStyle(ce,"top",r+n+"px")}function re(e){ue=t,Q(e,te)}function ie(e){de=t,Q(e,ne)}function oe(e){var t=e.target,n=i.getBody();if(i.$.contains(n,r)||r===n)if(Z(t)){e.preventDefault();var o=i.dom.getPos(t).x;i.dom.setAttrib(t,Ce,o),re(t)}else if(ee(t)){e.preventDefault();var a=i.dom.getPos(t).y;i.dom.setAttrib(t,ge,a),ie(t)}else C()}var ae,se,le,ce,ue,de,fe="mce-resize-bar",he="mce-resize-bar-row",pe="row-resize",me="data-row",ge="data-initial-top",ve="mce-resize-bar-col",ye="col-resize",be="data-col",Ce="data-initial-left",xe=4,we=10,Ne=10,Ee="mce-resize-bar-dragging",_e=new RegExp(/(\d+(\.\d+)?%)/),Se=new RegExp(/px|em/);return i.on("init",function(){i.dom.bind(d(),"mousedown",oe)}),i.on("ObjectResized",function(t){var n=t.target;if("TABLE"===n.nodeName){var r=[];e.each(n.rows,function(t){e.each(t.cells,function(e){var t=i.dom.getStyle(e,"width",!0);r.push({cell:e,width:t})})}),e.each(r,function(e){i.dom.setStyle(e.cell,"width",e.width),i.dom.setAttrib(e.cell,"width",null)})}}),i.on("mouseover",function(e){if(!se){var t=i.dom.getParent(e.target,"table");("TABLE"===e.target.nodeName||t)&&(r=t,x(t))}}),i.on("keydown",function(e){switch(e.keyCode){case n.LEFT:case n.RIGHT:case n.UP:case n.DOWN:C()}}),i.on("remove",function(){C(),i.dom.unbind(d(),"mousedown",oe)}),{adjustWidth:q,adjustHeight:j,clearBars:C,drawBars:B,determineDeltas:I,getTableGrid:S,getTableDetails:_,getWidths:P,getPixelHeights:O,isPercentageBasedSize:V,isPixelBasedSize:$,recalculateWidths:z,recalculateCellHeights:U,recalculateRowHeights:W}}}),r("tinymce/tableplugin/Plugin",["tinymce/tableplugin/TableGrid","tinymce/tableplugin/Quirks","tinymce/tableplugin/CellSelection","tinymce/tableplugin/Dialogs","tinymce/tableplugin/ResizeBars","tinymce/util/Tools","tinymce/dom/TreeWalker","tinymce/Env","tinymce/PluginManager"],function(e,t,n,r,i,o,a,s,l){function c(o){function a(e){return function(){o.execCommand(e)}}function l(e,t){var n,r,i,a;for(i='<table id="__mce"><tbody>',n=0;t>n;n++){for(i+="<tr>",r=0;e>r;r++)i+="<td>"+(s.ie&&s.ie<10?"&nbsp;":"<br>")+"</td>";i+="</tr>"}return i+="</tbody></table>",o.undoManager.transact(function(){o.insertContent(i),a=o.dom.get("__mce"),o.dom.setAttrib(a,"id",null),o.$("tr",a).each(function(e,t){o.fire("newrow",{node:t}),o.$("th,td",t).each(function(e,t){o.fire("newcell",{node:t})})}),o.dom.setAttribs(a,o.settings.table_default_attributes||{}),o.dom.setStyles(a,o.settings.table_default_styles||{})}),a}function c(e,t,n){function r(){var r,i,a,s={},l=0;i=o.dom.select("td[data-mce-selected],th[data-mce-selected]"),r=i[0],r||(r=o.selection.getStart()),n&&i.length>0?(u(i,function(e){return s[e.parentNode.parentNode.nodeName]=1}),u(s,function(e){l+=e}),a=1!==l):a=!o.dom.getParent(r,t),e.disabled(a),o.selection.selectorChanged(t,function(t){e.disabled(!t)})}o.initialized?r():o.on("init",r)}function d(){c(this,"table")}function f(){c(this,"td,th")}function h(){c(this,"td,th",!0)}function p(){var e="";e='<table role="grid" class="mce-grid mce-grid-border" aria-readonly="true">';for(var t=0;10>t;t++){e+="<tr>";for(var n=0;10>n;n++)e+='<td role="gridcell" tabindex="-1"><a id="mcegrid'+(10*t+n)+'" href="#" data-mce-x="'+n+'" data-mce-y="'+t+'"></a></td>';e+="</tr>"}return e+="</table>",e+='<div class="mce-text-center" role="presentation">1 x 1</div>'}function m(e,t,n){var r,i,a,s,l,c=n.getEl().getElementsByTagName("table")[0],u=n.isRtl()||"tl-tr"==n.parent().rel;for(c.nextSibling.innerHTML=e+1+" x "+(t+1),u&&(e=9-e),i=0;10>i;i++)for(r=0;10>r;r++)s=c.rows[i].childNodes[r].firstChild,l=(u?r>=e:e>=r)&&t>=i,o.dom.toggleClass(s,"mce-active",l),l&&(a=s);return a.parentNode}function g(){o.addButton("tableprops",{title:"Table properties",onclick:w.tableProps,icon:"table"}),o.addButton("tabledelete",{title:"Delete table",onclick:a("mceTableDelete")}),o.addButton("tablecellprops",{title:"Cell properties",onclick:a("mceTableCellProps")}),o.addButton("tablemergecells",{title:"Merge cells",onclick:a("mceTableMergeCells")}),o.addButton("tablesplitcells",{title:"Split cell",onclick:a("mceTableSplitCells")}),o.addButton("tableinsertrowbefore",{title:"Insert row before",onclick:a("mceTableInsertRowBefore")}),o.addButton("tableinsertrowafter",{title:"Insert row after",onclick:a("mceTableInsertRowAfter")}),o.addButton("tabledeleterow",{title:"Delete row",onclick:a("mceTableDeleteRow")}),o.addButton("tablerowprops",{title:"Row properties",onclick:a("mceTableRowProps")}),o.addButton("tablecutrow",{title:"Cut row",onclick:a("mceTableCutRow")}),o.addButton("tablecopyrow",{title:"Copy row",onclick:a("mceTableCopyRow")}),o.addButton("tablepasterowbefore",{title:"Paste row before",onclick:a("mceTablePasteRowBefore")}),o.addButton("tablepasterowafter",{title:"Paste row after",onclick:a("mceTablePasteRowAfter")}),o.addButton("tableinsertcolbefore",{title:"Insert column before",onclick:a("mceTableInsertColBefore")}),o.addButton("tableinsertcolafter",{title:"Insert column after",onclick:a("mceTableInsertColAfter")}),o.addButton("tabledeletecol",{title:"Delete column",onclick:a("mceTableDeleteCol")})}function v(e){var t=o.dom.is(e,"table")&&o.getBody().contains(e);return t}function y(){var e=o.settings.table_toolbar;""!==e&&e!==!1&&(e||(e="tableprops tabledelete | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol"),o.addContextToolbar(v,e))}var b,C,x=this,w=new r(o);!o.settings.object_resizing||o.settings.table_resize_bars===!1||o.settings.object_resizing!==!0&&"table"!==o.settings.object_resizing||(C=i(o)),o.settings.table_grid===!1?o.addMenuItem("inserttable",{text:"Insert table",icon:"table",context:"table",onclick:w.table}):o.addMenuItem("inserttable",{text:"Insert table",icon:"table",context:"table",ariaHideMenu:!0,onclick:function(e){e.aria&&(this.parent().hideAll(),e.stopImmediatePropagation(),w.table())},onshow:function(){m(0,0,this.menu.items()[0])},onhide:function(){var e=this.menu.items()[0].getEl().getElementsByTagName("a");o.dom.removeClass(e,"mce-active"),o.dom.addClass(e[0],"mce-active")},menu:[{type:"container",html:p(),onPostRender:function(){this.lastX=this.lastY=0},onmousemove:function(e){var t,n,r=e.target;"A"==r.tagName.toUpperCase()&&(t=parseInt(r.getAttribute("data-mce-x"),10),n=parseInt(r.getAttribute("data-mce-y"),10),(this.isRtl()||"tl-tr"==this.parent().rel)&&(t=9-t),t===this.lastX&&n===this.lastY||(m(t,n,e.control),this.lastX=t,this.lastY=n))},onclick:function(e){var t=this;"A"==e.target.tagName.toUpperCase()&&(e.preventDefault(),e.stopPropagation(),t.parent().cancel(),o.undoManager.transact(function(){l(t.lastX+1,t.lastY+1)}),o.addVisual())}}]}),o.addMenuItem("tableprops",{text:"Table properties",context:"table",onPostRender:d,onclick:w.tableProps}),o.addMenuItem("deletetable",{text:"Delete table",context:"table",onPostRender:d,cmd:"mceTableDelete"}),o.addMenuItem("cell",{separator:"before",text:"Cell",context:"table",menu:[{text:"Cell properties",onclick:a("mceTableCellProps"),onPostRender:f},{text:"Merge cells",onclick:a("mceTableMergeCells"),onPostRender:h},{text:"Split cell",onclick:a("mceTableSplitCells"),onPostRender:f}]}),o.addMenuItem("row",{text:"Row",context:"table",menu:[{text:"Insert row before",onclick:a("mceTableInsertRowBefore"),onPostRender:f},{text:"Insert row after",onclick:a("mceTableInsertRowAfter"),onPostRender:f},{text:"Delete row",onclick:a("mceTableDeleteRow"),onPostRender:f},{text:"Row properties",onclick:a("mceTableRowProps"),onPostRender:f},{text:"-"},{text:"Cut row",onclick:a("mceTableCutRow"),onPostRender:f},{text:"Copy row",onclick:a("mceTableCopyRow"),onPostRender:f},{text:"Paste row before",onclick:a("mceTablePasteRowBefore"),onPostRender:f},{text:"Paste row after",onclick:a("mceTablePasteRowAfter"),onPostRender:f}]}),o.addMenuItem("column",{text:"Column",context:"table",menu:[{text:"Insert column before",onclick:a("mceTableInsertColBefore"),onPostRender:f},{text:"Insert column after",onclick:a("mceTableInsertColAfter"),onPostRender:f},{text:"Delete column",onclick:a("mceTableDeleteCol"),onPostRender:f}]});var N=[];u("inserttable tableprops deletetable | cell row column".split(" "),function(e){"|"==e?N.push({text:"-"}):N.push(o.menuItems[e])}),o.addButton("table",{type:"menubutton",title:"Table",menu:N}),s.isIE||o.on("click",function(e){e=e.target,"TABLE"===e.nodeName&&(o.selection.select(e),o.nodeChanged())}),x.quirks=new t(o),o.on("Init",function(){x.cellSelection=new n(o,function(e){e&&C.clearBars()}),x.resizeBars=C}),o.on("PreInit",function(){o.serializer.addAttributeFilter("data-mce-cell-padding,data-mce-border,data-mce-border-color",function(e,t){for(var n=e.length;n--;)e[n].attr(t,null)})}),u({mceTableSplitCells:function(e){e.split()},mceTableMergeCells:function(e){var t;t=o.dom.getParent(o.selection.getStart(),"th,td"),o.dom.select("td[data-mce-selected],th[data-mce-selected]").length?e.merge():w.merge(e,t)},mceTableInsertRowBefore:function(e){e.insertRow(!0)},mceTableInsertRowAfter:function(e){e.insertRow()},mceTableInsertColBefore:function(e){e.insertCol(!0)},mceTableInsertColAfter:function(e){e.insertCol()},mceTableDeleteCol:function(e){e.deleteCols()},mceTableDeleteRow:function(e){e.deleteRows()},mceTableCutRow:function(e){b=e.cutRows()},mceTableCopyRow:function(e){b=e.copyRows()},mceTablePasteRowBefore:function(e){e.pasteRows(b,!0)},mceTablePasteRowAfter:function(e){e.pasteRows(b)},mceTableDelete:function(e){C&&C.clearBars(),e.deleteTable()}},function(t,n){o.addCommand(n,function(){var n=new e(o);n&&(t(n),o.execCommand("mceRepaint"),x.cellSelection.clear())})}),u({mceInsertTable:w.table,mceTableProps:function(){w.table(!0)},mceTableRowProps:w.row,mceTableCellProps:w.cell},function(e,t){o.addCommand(t,function(t,n){e(n)})}),g(),y(),o.settings.table_tab_navigation!==!1&&o.on("keydown",function(t){var n,r,i;9==t.keyCode&&(n=o.dom.getParent(o.selection.getStart(),"th,td"),n&&(t.preventDefault(),r=new e(o),i=t.shiftKey?-1:1,o.undoManager.transact(function(){!r.moveRelIdx(n,i)&&i>0&&(r.insertRow(),r.refresh(),r.moveRelIdx(n,i))})))}),x.insertTable=l}var u=o.each;l.add("table",c)})}(this);