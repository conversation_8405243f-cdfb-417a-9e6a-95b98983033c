tinymce.PluginManager.add('jbimages',function(editor,url){function jbBox(){editor.windowManager.open({title:'Добавяне на снимка',file:url+'/dialog-v4.htm',width:350,height:135,buttons:[{text:'Добавяне',classes:'widget btn primary first abs-layout-item',disabled:true,onclick:'close'},{text:'Close',onclick:'close'}]})}editor.addButton('jbimages',{tooltip:'Добавяне на снимка',icon:'image',text:'',onclick:jbBox});editor.addMenuItem('jbimages',{text:'Добавяне на снимка',icon:'image',context:'insert',onclick:jbBox})});