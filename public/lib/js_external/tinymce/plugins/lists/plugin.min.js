tinymce.PluginManager.add("lists",function(e){function t(t){return e.$.contains(e.getBody(),t)}function n(e){return e&&"BR"==e.nodeName}function r(e){return e&&/^(OL|UL|DL)$/.test(e.nodeName)&&t(e)}function i(e){return e.parentNode.firstChild==e}function o(e){return e.parentNode.lastChild==e}function a(t){return t&&!!e.schema.getTextBlockElements()[t.nodeName]}function s(t){return t===e.getBody()}var l=this;e.on("init",function(){function c(e,t){var n=S.isEmpty(e);return t&&S.select("span[data-mce-type=bookmark]").length>0?!1:n}function u(e){function t(t){var r,i,o;i=e[t?"startContainer":"endContainer"],o=e[t?"startOffset":"endOffset"],1==i.nodeType&&(r=S.create("span",{"data-mce-type":"bookmark"}),i.hasChildNodes()?(o=Math.min(o,i.childNodes.length-1),t?i.insertBefore(r,i.childNodes[o]):S.insertAfter(r,i.childNodes[o])):i.appendChild(r),i=r,o=0),n[t?"startContainer":"endContainer"]=i,n[t?"startOffset":"endOffset"]=o}var n={};return t(!0),e.collapsed||t(),n}function d(e){function t(t){function n(e){for(var t=e.parentNode.firstChild,n=0;t;){if(t==e)return n;1==t.nodeType&&"bookmark"==t.getAttribute("data-mce-type")||n++,t=t.nextSibling}return-1}var r,i,o;r=o=e[t?"startContainer":"endContainer"],i=e[t?"startOffset":"endOffset"],r&&(1==r.nodeType&&(i=n(r),r=r.parentNode,S.remove(o)),e[t?"startContainer":"endContainer"]=r,e[t?"startOffset":"endOffset"]=i)}t(!0),t();var n=S.createRng();n.setStart(e.startContainer,e.startOffset),e.endContainer&&n.setEnd(e.endContainer,e.endOffset),k.setRng(n)}function f(t,n){var r,i,o,a=S.createFragment(),s=e.schema.getBlockElements();if(e.settings.forced_root_block&&(n=n||e.settings.forced_root_block),n&&(i=S.create(n),i.tagName===e.settings.forced_root_block&&S.setAttribs(i,e.settings.forced_root_block_attrs),a.appendChild(i)),t)for(;r=t.firstChild;){var l=r.nodeName;o||"SPAN"==l&&"bookmark"==r.getAttribute("data-mce-type")||(o=!0),s[l]?(a.appendChild(r),i=null):n?(i||(i=S.create(n),a.appendChild(i)),i.appendChild(r)):a.appendChild(r)}return e.settings.forced_root_block?o||tinymce.Env.ie&&!(tinymce.Env.ie>10)||i.appendChild(S.create("br",{"data-mce-bogus":"1"})):a.appendChild(S.create("br")),a}function h(){return tinymce.grep(k.getSelectedBlocks(),function(e){return/^(LI|DT|DD)$/.test(e.nodeName)})}function p(e,t,n){function r(e){tinymce.each(a,function(n){e.parentNode.insertBefore(n,t.parentNode)}),S.remove(e)}var i,o,a,s;for(a=S.select('span[data-mce-type="bookmark"]',e),n=n||f(t),i=S.createRng(),i.setStartAfter(t),i.setEndAfter(e),o=i.extractContents(),s=o.firstChild;s;s=s.firstChild)if("LI"==s.nodeName&&S.isEmpty(s)){S.remove(s);break}S.isEmpty(o)||S.insertAfter(o,e),S.insertAfter(n,e),c(t.parentNode)&&r(t.parentNode),S.remove(t),c(e)&&S.remove(e)}function m(e){var t,n;if(t=e.nextSibling,t&&r(t)&&t.nodeName==e.nodeName){for(;n=t.firstChild;)e.appendChild(n);S.remove(t)}if(t=e.previousSibling,t&&r(t)&&t.nodeName==e.nodeName){for(;n=t.firstChild;)e.insertBefore(n,e.firstChild);S.remove(t)}}function g(e){tinymce.each(tinymce.grep(S.select("ol,ul",e)),function(e){var t,n=e.parentNode;"LI"==n.nodeName&&n.firstChild==e&&(t=n.previousSibling,t&&"LI"==t.nodeName&&(t.appendChild(e),c(n)&&S.remove(n))),r(n)&&(t=n.previousSibling,t&&"LI"==t.nodeName&&t.appendChild(e))})}function v(e){function t(e){c(e)&&S.remove(e)}var n,a=e.parentNode,l=a.parentNode;return s(a)?!0:"DD"==e.nodeName?(S.rename(e,"DT"),!0):i(e)&&o(e)?("LI"==l.nodeName?(S.insertAfter(e,l),t(l),S.remove(a)):r(l)?S.remove(a,!0):(l.insertBefore(f(e),a),S.remove(a)),!0):i(e)?("LI"==l.nodeName?(S.insertAfter(e,l),e.appendChild(a),t(l)):r(l)?l.insertBefore(e,a):(l.insertBefore(f(e),a),S.remove(e)),!0):o(e)?("LI"==l.nodeName?S.insertAfter(e,l):r(l)?S.insertAfter(e,a):(S.insertAfter(f(e),a),S.remove(e)),!0):("LI"==l.nodeName?(a=l,n=f(e,"LI")):n=r(l)?f(e,"LI"):f(e),p(a,e,n),g(a.parentNode),!0)}function y(e){function t(t,n){var i;if(r(t)){for(;i=e.lastChild.firstChild;)n.appendChild(i);S.remove(t)}}var n,i;return"DT"==e.nodeName?(S.rename(e,"DD"),!0):(n=e.previousSibling,n&&r(n)?(n.appendChild(e),!0):n&&"LI"==n.nodeName&&r(n.lastChild)?(n.lastChild.appendChild(e),t(e.lastChild,n.lastChild),!0):(n=e.nextSibling,n&&r(n)?(n.insertBefore(e,n.firstChild),!0):(n=e.previousSibling,n&&"LI"==n.nodeName?(i=S.create(e.parentNode.nodeName),n.appendChild(i),i.appendChild(e),t(e.lastChild,i),!0):!1)))}function b(){var t=h();if(t.length){for(var n=u(k.getRng(!0)),r=0;r<t.length&&(y(t[r])||0!==r);r++);return d(n),e.nodeChanged(),!0}}function C(){var t=h();if(t.length){var n,r,i=u(k.getRng(!0)),o=e.getBody();for(n=t.length;n--;)for(var a=t[n].parentNode;a&&a!=o;){for(r=t.length;r--;)if(t[r]===a){t.splice(n,1);break}a=a.parentNode}for(n=0;n<t.length&&(v(t[n])||0!==n);n++);return d(i),e.nodeChanged(),!0}}function x(t){function i(){function t(e){var t,n;for(t=s[e?"startContainer":"endContainer"],n=s[e?"startOffset":"endOffset"],1==t.nodeType&&(t=t.childNodes[Math.min(n,t.childNodes.length-1)]||t);t.parentNode!=o;){if(a(t))return t;if(/^(TD|TH)$/.test(t.parentNode.nodeName))return t;t=t.parentNode}return t}for(var r,i=[],o=e.getBody(),l=t(!0),c=t(),u=[],d=l;d&&(u.push(d),d!=c);d=d.nextSibling);return tinymce.each(u,function(e){if(a(e))return i.push(e),void(r=null);if(S.isBlock(e)||n(e))return n(e)&&S.remove(e),void(r=null);var t=e.nextSibling;return tinymce.dom.BookmarkManager.isBookmarkNode(e)&&(a(t)||!t&&e.parentNode==o)?void(r=null):(r||(r=S.create("p"),e.parentNode.insertBefore(r,e),i.push(r)),void r.appendChild(e))}),i}var o,s=k.getRng(!0),l="LI";"false"!==S.getContentEditable(k.getNode())&&(t=t.toUpperCase(),"DL"==t&&(l="DT"),o=u(s),tinymce.each(i(),function(e){var n,i;i=e.previousSibling,i&&r(i)&&i.nodeName==t?(n=i,e=S.rename(e,l),i.appendChild(e)):(n=S.create(t),e.parentNode.insertBefore(n,e),n.appendChild(e),e=S.rename(e,l)),m(n)}),d(o))}function w(){var t=u(k.getRng(!0)),n=e.getBody();tinymce.each(h(),function(e){var t,i;if(!s(e.parentNode)){if(c(e))return void v(e);for(t=e;t&&t!=n;t=t.parentNode)r(t)&&(i=t);p(i,e)}}),d(t)}function N(e){var t=S.getParent(k.getStart(),"OL,UL,DL");if(!s(t))if(t)if(t.nodeName==e)w(e);else{var n=u(k.getRng(!0));m(S.rename(t,e)),d(n)}else x(e)}function E(t){return function(){var n=S.getParent(e.selection.getStart(),"UL,OL,DL");return n&&n.nodeName==t}}function _(e){return n(e)?!(!S.isBlock(e.nextSibling)||n(e.previousSibling)):!1}var S=e.dom,k=e.selection;l.backspaceDelete=function(i){function o(t,n){var r,i,o=t.startContainer,a=t.startOffset;if(3==o.nodeType&&(n?a<o.data.length:a>0))return o;for(r=e.schema.getNonEmptyElements(),1==o.nodeType&&(o=tinymce.dom.RangeUtils.getNode(o,a)),i=new tinymce.dom.TreeWalker(o,e.getBody()),n&&_(o)&&i.next();o=i[n?"next":"prev2"]();){if("LI"==o.nodeName&&!o.hasChildNodes())return o;if(r[o.nodeName])return o;if(3==o.nodeType&&o.data.length>0)return o}}function a(e,i){var o,a,l=e.parentNode;if(t(e)&&t(i)){if(r(i.lastChild)&&(a=i.lastChild),l==i.lastChild&&n(l.previousSibling)&&S.remove(l.previousSibling),o=i.lastChild,o&&n(o)&&e.hasChildNodes()&&S.remove(o),c(i,!0)&&S.$(i).empty(),!c(e,!0))for(;o=e.firstChild;)i.appendChild(o);a&&i.appendChild(a),S.remove(e),c(l)&&!s(l)&&S.remove(l)}}if(k.isCollapsed()){var l,f,h,p=S.getParent(k.getStart(),"LI");if(p){if(l=p.parentNode,s(l)&&S.isEmpty(l))return!0;if(f=k.getRng(!0),h=S.getParent(o(f,i),"LI"),h&&h!=p){var m=u(f);return i?a(h,p):a(p,h),d(m),!0}if(!h&&!i&&w(l.nodeName))return!0}}},e.on("BeforeExecCommand",function(t){var n,r=t.command.toLowerCase();return"indent"==r?b()&&(n=!0):"outdent"==r&&C()&&(n=!0),n?(e.fire("ExecCommand",{command:t.command}),t.preventDefault(),!0):void 0}),e.addCommand("InsertUnorderedList",function(){N("UL")}),e.addCommand("InsertOrderedList",function(){N("OL")}),e.addCommand("InsertDefinitionList",function(){N("DL")}),e.addQueryStateHandler("InsertUnorderedList",E("UL")),e.addQueryStateHandler("InsertOrderedList",E("OL")),e.addQueryStateHandler("InsertDefinitionList",E("DL")),e.on("keydown",function(t){9!=t.keyCode||tinymce.util.VK.metaKeyPressed(t)||e.dom.getParent(e.selection.getStart(),"LI,DT,DD")&&(t.preventDefault(),t.shiftKey?C():b())})}),e.addButton("indent",{icon:"indent",title:"Increase indent",cmd:"Indent",onPostRender:function(){var t=this;e.on("nodechange",function(){for(var n=e.selection.getSelectedBlocks(),r=!1,o=0,a=n.length;!r&&a>o;o++){var s=n[o].nodeName;r="LI"==s&&i(n[o])||"UL"==s||"OL"==s||"DD"==s}t.disabled(r)})}}),e.on("keydown",function(e){e.keyCode==tinymce.util.VK.BACKSPACE?l.backspaceDelete()&&e.preventDefault():e.keyCode==tinymce.util.VK.DELETE&&l.backspaceDelete(!0)&&e.preventDefault()})});