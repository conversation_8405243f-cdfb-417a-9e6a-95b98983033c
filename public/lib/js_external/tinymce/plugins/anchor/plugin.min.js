tinymce.PluginManager.add("anchor",function(e){function t(){var t=e.selection.getNode(),n="",r="A"==t.tagName&&""===e.dom.getAttrib(t,"href");r&&(n=t.name||t.id||""),e.windowManager.open({title:"Anchor",body:{type:"textbox",name:"name",size:40,label:"Name",value:n},onsubmit:function(n){var i=n.data.name;r?t.id=i:(e.selection.collapse(!0),e.execCommand("mceInsertContent",!1,e.dom.createHTML("a",{id:i})))}})}e.addCommand("mceAnchor",t),e.addButton("anchor",{icon:"anchor",tooltip:"Anchor",onclick:t,stateSelector:"a:not([href])"}),e.addMenuItem("anchor",{icon:"anchor",text:"Anchor",context:"insert",onclick:t})});