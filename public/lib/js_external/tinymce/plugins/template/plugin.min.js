tinymce.PluginManager.add("template",function(e){function t(t){return function(){var n=e.settings.templates;return"function"==typeof n?void n(t):void("string"==typeof n?tinymce.util.XHR.send({url:n,success:function(e){t(tinymce.util.JSON.parse(e))}}):t(n))}}function n(t){function n(t){function n(t){if(-1==t.indexOf("<html>")){var n="";tinymce.each(e.contentCSS,function(t){n+='<link type="text/css" rel="stylesheet" href="'+e.documentBaseURI.toAbsolute(t)+'">'});var i=e.settings.body_class||"";-1!=i.indexOf("=")&&(i=e.getParam("body_class","","hash"),i=i[e.id]||""),t="<!DOCTYPE html><html><head>"+n+'</head><body class="'+i+'">'+t+"</body></html>"}t=o(t,"template_preview_replace_values");var a=r.find("iframe")[0].getEl().contentWindow.document;a.open(),a.write(t),a.close()}var a=t.control.value();a.url?tinymce.util.XHR.send({url:a.url,success:function(e){i=e,n(i)}}):(i=a.content,n(i)),r.find("#description")[0].text(t.control.value().description)}var r,i,s=[];if(!t||0===t.length){var l=e.translate("No templates defined.");return void e.notificationManager.open({text:l,type:"info"})}tinymce.each(t,function(e){s.push({selected:!s.length,text:e.title,value:{url:e.url,content:e.content,description:e.description}})}),r=e.windowManager.open({title:"Insert template",layout:"flex",direction:"column",align:"stretch",padding:15,spacing:10,items:[{type:"form",flex:0,padding:0,items:[{type:"container",label:"Templates",items:{type:"listbox",label:"Templates",name:"template",values:s,onselect:n}}]},{type:"label",name:"description",label:"Description",text:"\xa0"},{type:"iframe",flex:1,border:1}],onsubmit:function(){a(!1,i)},width:e.getParam("template_popup_width",600),height:e.getParam("template_popup_height",500)}),r.find("listbox")[0].fire("select")}function r(t,n){function r(e,t){if(e=""+e,e.length<t)for(var n=0;n<t-e.length;n++)e="0"+e;return e}var i="Sun Mon Tue Wed Thu Fri Sat Sun".split(" "),o="Sunday Monday Tuesday Wednesday Thursday Friday Saturday Sunday".split(" "),a="Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec".split(" "),s="January February March April May June July August September October November December".split(" ");return n=n||new Date,t=t.replace("%D","%m/%d/%Y"),t=t.replace("%r","%I:%M:%S %p"),t=t.replace("%Y",""+n.getFullYear()),t=t.replace("%y",""+n.getYear()),t=t.replace("%m",r(n.getMonth()+1,2)),t=t.replace("%d",r(n.getDate(),2)),t=t.replace("%H",""+r(n.getHours(),2)),t=t.replace("%M",""+r(n.getMinutes(),2)),t=t.replace("%S",""+r(n.getSeconds(),2)),t=t.replace("%I",""+((n.getHours()+11)%12+1)),t=t.replace("%p",""+(n.getHours()<12?"AM":"PM")),t=t.replace("%B",""+e.translate(s[n.getMonth()])),t=t.replace("%b",""+e.translate(a[n.getMonth()])),t=t.replace("%A",""+e.translate(o[n.getDay()])),t=t.replace("%a",""+e.translate(i[n.getDay()])),t=t.replace("%%","%")}function i(t){var n=e.dom,r=e.getParam("template_replace_values");s(n.select("*",t),function(e){s(r,function(t,i){n.hasClass(e,i)&&"function"==typeof r[i]&&r[i](e)})})}function o(t,n){return s(e.getParam(n),function(e,n){"function"==typeof e&&(e=e(n)),t=t.replace(new RegExp("\\{\\$"+n+"\\}","g"),e)}),t}function a(t,n){function a(e,t){return new RegExp("\\b"+t+"\\b","g").test(e.className)}var l,c,u=e.dom,d=e.selection.getContent();n=o(n,"template_replace_values"),l=u.create("div",null,n),c=u.select(".mceTmpl",l),c&&c.length>0&&(l=u.create("div",null),l.appendChild(c[0].cloneNode(!0))),s(u.select("*",l),function(t){a(t,e.getParam("template_cdate_classes","cdate").replace(/\s+/g,"|"))&&(t.innerHTML=r(e.getParam("template_cdate_format",e.getLang("template.cdate_format")))),a(t,e.getParam("template_mdate_classes","mdate").replace(/\s+/g,"|"))&&(t.innerHTML=r(e.getParam("template_mdate_format",e.getLang("template.mdate_format")))),a(t,e.getParam("template_selected_content_classes","selcontent").replace(/\s+/g,"|"))&&(t.innerHTML=d)}),i(l),e.execCommand("mceInsertContent",!1,l.innerHTML),e.addVisual()}var s=tinymce.each;e.addCommand("mceInsertTemplate",a),e.addButton("template",{title:"Insert template",onclick:t(n)}),e.addMenuItem("template",{text:"Insert template",onclick:t(n),context:"insert"}),e.on("PreProcess",function(t){var n=e.dom;s(n.select("div",t.node),function(t){n.hasClass(t,"mceTmpl")&&(s(n.select("*",t),function(t){n.hasClass(t,e.getParam("template_mdate_classes","mdate").replace(/\s+/g,"|"))&&(t.innerHTML=r(e.getParam("template_mdate_format",e.getLang("template.mdate_format"))))}),i(t))})})});