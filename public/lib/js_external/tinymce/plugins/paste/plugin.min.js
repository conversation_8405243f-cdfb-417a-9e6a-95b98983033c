!function(e,t){"use strict";function n(e,t){for(var n,r=[],o=0;o<e.length;++o){if(n=a[e[o]]||i(e[o]),!n)throw"module definition dependecy not found: "+e[o];r.push(n)}t.apply(null,r)}function r(e,r,i){if("string"!=typeof e)throw"invalid module definition, module id must be defined and be a string";if(r===t)throw"invalid module definition, dependencies must be specified";if(i===t)throw"invalid module definition, definition function must be specified";n(r,function(){a[e]=i.apply(null,arguments)})}function i(t){for(var n=e,r=t.split(/[.\/]/),i=0;i<r.length;++i){if(!n[r[i]])return;n=n[r[i]]}return n}function o(n){var r,i,o,s,l;for(r=0;r<n.length;r++){i=e,o=n[r],s=o.split(/[.\/]/);for(var c=0;c<s.length-1;++c)i[s[c]]===t&&(i[s[c]]={}),i=i[s[c]];i[s[s.length-1]]=a[o]}if(e.AMDLC_TESTS){l=e.privateModules||{};for(o in a)l[o]=a[o];for(r=0;r<n.length;r++)delete l[n[r]];e.privateModules=l}}var a={};r("tinymce/pasteplugin/Utils",["tinymce/util/Tools","tinymce/html/DomParser","tinymce/html/Schema"],function(e,t,n){function r(t,n){return e.each(n,function(e){t=e.constructor==RegExp?t.replace(e,""):t.replace(e[0],e[1])}),t}function i(i){function o(e){var t=e.name,n=e;if("br"===t)return void(l+="\n");if(c[t]&&(l+=" "),u[t])return void(l+=" ");if(3==e.type&&(l+=e.value),!e.shortEnded&&(e=e.firstChild))do o(e);while(e=e.next);d[t]&&n.next&&(l+="\n","p"==t&&(l+="\n"))}var a=new n,s=new t({},a),l="",c=a.getShortEndedElements(),u=e.makeMap("script noscript style textarea video audio iframe object"," "),d=a.getBlockElements();return i=r(i,[/<!\[[^\]]+\]>/g]),o(s.parse(i)),l}function o(e){function t(e,t,n){return t||n?"\xa0":" "}return e=r(e,[/^[\s\S]*<body[^>]*>\s*|\s*<\/body[^>]*>[\s\S]*$/g,/<!--StartFragment-->|<!--EndFragment-->/g,[/( ?)<span class="Apple-converted-space">\u00a0<\/span>( ?)/g,t],/<br class="Apple-interchange-newline">/g,/<br>$/i])}function a(e){var t=0;return function(){return e+t++}}return{filter:r,innerText:i,trimHtml:o,createIdGenerator:a}}),r("tinymce/pasteplugin/Clipboard",["tinymce/Env","tinymce/dom/RangeUtils","tinymce/util/VK","tinymce/pasteplugin/Utils","tinymce/util/Delay"],function(e,t,n,r,i){return function(o){function a(e){var t,n=o.dom;if(t=o.fire("BeforePastePreProcess",{content:e}),t=o.fire("PastePreProcess",t),e=t.content,!t.isDefaultPrevented()){if(o.hasEventListeners("PastePostProcess")&&!t.isDefaultPrevented()){var r=n.add(o.getBody(),"div",{style:"display:none"},e);t=o.fire("PastePostProcess",{node:r}),n.remove(r),e=t.node.innerHTML}t.isDefaultPrevented()||o.insertContent(e,{merge:o.settings.paste_merge_formats!==!1,data:{paste:!0}})}}function s(e){e=o.dom.encode(e).replace(/\r\n/g,"\n");var t,n=o.dom.getParent(o.selection.getStart(),o.dom.isBlock),i=o.settings.forced_root_block;i&&(t=o.dom.createHTML(i,o.settings.forced_root_block_attrs),t=t.substr(0,t.length-3)+">"),n&&/^(PRE|DIV)$/.test(n.nodeName)||!i?e=r.filter(e,[[/\n/g,"<br>"]]):(e=r.filter(e,[[/\n\n/g,"</p>"+t],[/^(.*<\/p>)(<p>)$/,t+"$1"],[/\n/g,"<br />"]]),-1!=e.indexOf("<p>")&&(e=t+e)),a(e)}function l(){function t(e){var t,n,i,o=e.startContainer;if(t=e.getClientRects(),t.length)return t[0];if(e.collapsed&&1==o.nodeType){for(i=o.childNodes[x.startOffset];i&&3==i.nodeType&&!i.data.length;)i=i.nextSibling;if(i)return"BR"==i.tagName&&(n=r.doc.createTextNode("\ufeff"),i.parentNode.insertBefore(n,i),e=r.createRng(),e.setStartBefore(n),e.setEndAfter(n),t=e.getClientRects(),r.remove(n)),t.length?t[0]:void 0}}var n,r=o.dom,i=o.getBody(),a=o.dom.getViewPort(o.getWin()),s=a.y,l=20;if(x=o.selection.getRng(),o.inline&&(n=o.selection.getScrollContainer(),n&&n.scrollTop>0&&(s=n.scrollTop)),x.getClientRects){var c=t(x);if(c)l=s+(c.top-r.getPos(i).y);else{l=s;var u=x.startContainer;u&&(3==u.nodeType&&u.parentNode!=i&&(u=u.parentNode),1==u.nodeType&&(l=r.getPos(u,n||i).y))}}C=r.add(o.getBody(),"div",{id:"mcepastebin",contentEditable:!0,"data-mce-bogus":"all",style:"position: absolute; top: "+l+"px;width: 10px; height: 10px; overflow: hidden; opacity: 0"},S),(e.ie||e.gecko)&&r.setStyle(C,"left","rtl"==r.getStyle(i,"direction",!0)?65535:-65535),r.bind(C,"beforedeactivate focusin focusout",function(e){e.stopPropagation()}),C.focus(),o.selection.select(C,!0)}function c(){if(C){for(var e;e=o.dom.get("mcepastebin");)o.dom.remove(e),o.dom.unbind(e);x&&o.selection.setRng(x)}C=x=null}function u(){var e,t,n,r,i="";for(e=o.dom.select("div[id=mcepastebin]"),t=0;t<e.length;t++)n=e[t],n.firstChild&&"mcepastebin"==n.firstChild.id&&(n=n.firstChild),r=n.innerHTML,i!=S&&(i+=r);return i}function d(e){var t={};if(e){if(e.getData){var n=e.getData("Text");n&&n.length>0&&-1==n.indexOf(k)&&(t["text/plain"]=n)}if(e.types)for(var r=0;r<e.types.length;r++){var i=e.types[r];t[i]=e.getData(i)}}return t}function f(e){return d(e.clipboardData||o.getDoc().dataTransfer)}function h(e){return v(e,"text/html")||v(e,"text/plain")}function p(e,t){function n(e){var t;return t=e.indexOf(","),-1!==t?e.substr(t+1):null}function r(r){function i(e,r){t&&(o.selection.setRng(t),t=null);var i=o.editorUpload.blobCache,s=i.create(T(),r,n(e.result));i.add(s),a('<img src="'+s.blobUri()+'">')}var s,l,c,u=!1;if(r)for(s=0;s<r.length;s++)if(l=r[s],/^image\/(jpeg|png|gif|bmp)$/.test(l.type)){var d=l.getAsFile?l.getAsFile():l;c=new FileReader,c.onload=i.bind(null,c,d),c.readAsDataURL(d),e.preventDefault(),u=!0}return u}var i=e.clipboardData||e.dataTransfer;return o.settings.paste_data_images&&i?r(i.items)||r(i.files):void 0}function m(e){var t=e.clipboardData;return-1!=navigator.userAgent.indexOf("Android")&&t&&t.items&&0===t.items.length}function g(e){return t.getCaretRangeFromPoint(e.clientX,e.clientY,o.getDoc())}function v(e,t){return t in e&&e[t].length>0}function y(e){return n.metaKeyPressed(e)&&86==e.keyCode||e.shiftKey&&45==e.keyCode}function b(){function t(e,t,n){var i;return v(e,"text/html")?i=e["text/html"]:(i=u(),i==S&&(n=!0)),i=r.trimHtml(i),C&&C.firstChild&&"mcepastebin"===C.firstChild.id&&(n=!0),c(),i.length||(n=!0),n&&(i=v(e,"text/plain")&&-1==i.indexOf("</p>")?e["text/plain"]:r.innerText(i)),i==S?void(t||o.windowManager.alert("Please use Ctrl+V/Cmd+V keyboard shortcuts to paste contents.")):void(n?s(i):a(i))}function n(e){return 0===e["text/plain"].indexOf("file://")}o.on("keydown",function(t){function n(e){y(e)&&!e.isDefaultPrevented()&&c()}if(y(t)&&!t.isDefaultPrevented()){if(w=t.shiftKey&&86==t.keyCode,w&&e.webkit&&-1!=navigator.userAgent.indexOf("Version/"))return;if(t.stopImmediatePropagation(),E=(new Date).getTime(),e.ie&&w)return t.preventDefault(),void o.fire("paste",{ieFake:!0});c(),l(),o.once("keyup",n),o.once("paste",function(){o.off("keyup",n)})}});var b=function(){return x||o.selection.getRng()};o.on("paste",function(n){var r=(new Date).getTime(),a=f(n),s=(new Date).getTime()-r,d=(new Date).getTime()-E-s<1e3,g="text"==N.pasteFormat||w;return w=!1,n.isDefaultPrevented()||m(n)?void c():!h(a)&&p(n,b())?void c():(d||n.preventDefault(),!e.ie||d&&!n.ieFake||(l(),o.dom.bind(C,"paste",function(e){e.stopPropagation()}),o.getDoc().execCommand("Paste",!1,null),a["text/html"]=u()),void(v(a,"text/html")?(n.preventDefault(),t(a,d,g)):i.setEditorTimeout(o,function(){t(a,d,g)},0)))}),o.on("dragstart dragend",function(e){_="dragstart"==e.type}),o.on("drop",function(e){var t,l;if(l=g(e),!e.isDefaultPrevented()&&!_&&(t=d(e.dataTransfer),(h(t)&&!n(t)||!p(e,l))&&l&&o.settings.paste_filter_drop!==!1)){var c=t["mce-internal"]||t["text/html"]||t["text/plain"];c&&(e.preventDefault(),i.setEditorTimeout(o,function(){o.undoManager.transact(function(){t["mce-internal"]&&o.execCommand("Delete"),o.selection.setRng(l),c=r.trimHtml(c),t["text/html"]?a(c):s(c)})}))}}),o.on("dragover dragend",function(e){o.settings.paste_data_images&&e.preventDefault()})}var C,x,w,N=this,E=0,_=!1,S="%MCEPASTEBIN%",k="data:text/mce-internal,",T=r.createIdGenerator("mceclip");N.pasteHtml=a,N.pasteText=s,o.on("preInit",function(){b(),o.parser.addNodeFilter("img",function(t,n,r){function i(e){return e.data&&e.data.paste===!0}function a(t){t.attr("data-mce-object")||u===e.transparentSrc||t.remove()}function s(e){return 0===e.indexOf("webkit-fake-url")}function l(e){return 0===e.indexOf("data:")}if(!o.settings.paste_data_images&&i(r))for(var c=t.length;c--;){var u=t[c].attributes.map.src;u&&(s(u)?a(t[c]):!o.settings.allow_html_data_urls&&l(u)&&a(t[c]))}})})}}),r("tinymce/pasteplugin/WordFilter",["tinymce/util/Tools","tinymce/html/DomParser","tinymce/html/Schema","tinymce/html/Serializer","tinymce/html/Node","tinymce/pasteplugin/Utils"],function(e,t,n,r,i,o){function a(e){return/<font face="Times New Roman"|class="?Mso|style="[^"]*\bmso-|style='[^'']*\bmso-|w:WordDocument/i.test(e)||/class="OutlineElement/.test(e)||/id="?docs\-internal\-guid\-/.test(e)}function s(t){var n,r;return r=[/^[IVXLMCD]{1,2}\.[ \u00a0]/,/^[ivxlmcd]{1,2}\.[ \u00a0]/,/^[a-z]{1,2}[\.\)][ \u00a0]/,/^[A-Z]{1,2}[\.\)][ \u00a0]/,/^[0-9]+\.[ \u00a0]/,/^[\u3007\u4e00\u4e8c\u4e09\u56db\u4e94\u516d\u4e03\u516b\u4e5d]+\.[ \u00a0]/,/^[\u58f1\u5f10\u53c2\u56db\u4f0d\u516d\u4e03\u516b\u4e5d\u62fe]+\.[ \u00a0]/],t=t.replace(/^[\u00a0 ]+/,""),e.each(r,function(e){return e.test(t)?(n=!0,!1):void 0}),n}function l(e){return/^[\s\u00a0]*[\u2022\u00b7\u00a7\u25CF]\s*/.test(e)}function c(c){var u=c.settings;c.on("BeforePastePreProcess",function(d){function f(e){function t(e){var n="";if(3===e.type)return e.value;if(e=e.firstChild)do n+=t(e);while(e=e.next);return n}function n(e,t){if(3===e.type&&t.test(e.value))return e.value=e.value.replace(t,""),!1;if(e=e.firstChild)do if(!n(e,t))return!1;while(e=e.next);return!0}function r(e){if(e._listIgnore)return void e.remove();if(e=e.firstChild)do r(e);while(e=e.next)}function o(e,t,o){var s=e._listLevel||u;s!=u&&(u>s?a&&(a=a.parent.parent):(c=a,a=null)),a&&a.name==t?a.append(e):(c=c||a,a=new i(t,1),o>1&&a.attr("start",""+o),e.wrap(a)),e.name="li",s>u&&c&&c.lastChild.append(a),u=s,r(e),n(e,/^\u00a0+/),n(e,/^\s*([\u2022\u00b7\u00a7\u25CF]|\w+\.)/),n(e,/^\u00a0+/)}for(var a,c,u=1,d=[],f=e.firstChild;"undefined"!=typeof f&&null!==f;)if(d.push(f),f=f.walk(),null!==f)for(;"undefined"!=typeof f&&f.parent!==e;)f=f.walk();for(var h=0;h<d.length;h++)if(e=d[h],"p"==e.name&&e.firstChild){var p=t(e);if(l(p)){o(e,"ul");continue}if(s(p)){var m=/([0-9]+)\./.exec(p),g=1;m&&(g=parseInt(m[1],10)),o(e,"ol",g);continue}if(e._listLevel){o(e,"ul",1);continue}a=null}else c=a,a=null}function h(t,n){var r,o={},a=c.dom.parseStyle(n);return e.each(a,function(e,i){switch(i){case"mso-list":r=/\w+ \w+([0-9]+)/i.exec(n),r&&(t._listLevel=parseInt(r[1],10)),/Ignore/i.test(e)&&t.firstChild&&(t._listIgnore=!0,t.firstChild._listIgnore=!0);break;case"horiz-align":i="text-align";break;case"vert-align":i="vertical-align";break;case"font-color":case"mso-foreground":i="color";break;case"mso-background":case"mso-highlight":i="background";break;case"font-weight":case"font-style":return void("normal"!=e&&(o[i]=e));case"mso-element":if(/^(comment|comment-list)$/i.test(e))return void t.remove()}return 0===i.indexOf("mso-comment")?void t.remove():void(0!==i.indexOf("mso-")&&("all"==p||m&&m[i])&&(o[i]=e))}),/(bold)/i.test(o["font-weight"])&&(delete o["font-weight"],t.wrap(new i("b",1))),/(italic)/i.test(o["font-style"])&&(delete o["font-style"],t.wrap(new i("i",1))),o=c.dom.serializeStyle(o,t.name),o?o:null}var p,m,g=d.content;if(g=g.replace(/<b[^>]+id="?docs-internal-[^>]*>/gi,""),g=g.replace(/<br class="?Apple-interchange-newline"?>/gi,""),p=u.paste_retain_style_properties,p&&(m=e.makeMap(p.split(/[, ]/))),u.paste_enable_default_filters!==!1&&a(d.content)){d.wordContent=!0,g=o.filter(g,[/<!--[\s\S]+?-->/gi,/<(!|script[^>]*>.*?<\/script(?=[>\s])|\/?(\?xml(:\w+)?|img|meta|link|style|\w:\w+)(?=[\s\/>]))[^>]*>/gi,[/<(\/?)s>/gi,"<$1strike>"],[/&nbsp;/gi,"\xa0"],[/<span\s+style\s*=\s*"\s*mso-spacerun\s*:\s*yes\s*;?\s*"\s*>([\s\u00a0]*)<\/span>/gi,function(e,t){return t.length>0?t.replace(/./," ").slice(Math.floor(t.length/2)).split("").join("\xa0"):""}]]);var v=u.paste_word_valid_elements;v||(v="-strong/b,-em/i,-u,-span,-p,-ol,-ul,-li,-h1,-h2,-h3,-h4,-h5,-h6,-p/div,-a[href|name],sub,sup,strike,br,del,table[width],tr,td[colspan|rowspan|width],th[colspan|rowspan|width],thead,tfoot,tbody");var y=new n({valid_elements:v,valid_children:"-li[p]"});e.each(y.elements,function(e){e.attributes["class"]||(e.attributes["class"]={},e.attributesOrder.push("class")),e.attributes.style||(e.attributes.style={},e.attributesOrder.push("style"))});var b=new t({},y);b.addAttributeFilter("style",function(e){for(var t,n=e.length;n--;)t=e[n],t.attr("style",h(t,t.attr("style"))),"span"==t.name&&t.parent&&!t.attributes.length&&t.unwrap()}),b.addAttributeFilter("class",function(e){for(var t,n,r=e.length;r--;)t=e[r],n=t.attr("class"),/^(MsoCommentReference|MsoCommentText|msoDel)$/i.test(n)&&t.remove(),t.attr("class",null)}),b.addNodeFilter("del",function(e){for(var t=e.length;t--;)e[t].remove()}),b.addNodeFilter("a",function(e){for(var t,n,r,i=e.length;i--;)if(t=e[i],n=t.attr("href"),r=t.attr("name"),n&&-1!=n.indexOf("#_msocom_"))t.remove();else if(n&&0===n.indexOf("file://")&&(n=n.split("#")[1],n&&(n="#"+n)),n||r){if(r&&!/^_?(?:toc|edn|ftn)/i.test(r)){t.unwrap();continue}t.attr({href:n,name:r})}else t.unwrap()});var C=b.parse(g);u.paste_convert_word_fake_lists!==!1&&f(C),d.content=new r({validate:u.validate},y).serialize(C)}})}return c.isWordContent=a,c}),r("tinymce/pasteplugin/Quirks",["tinymce/Env","tinymce/util/Tools","tinymce/pasteplugin/WordFilter","tinymce/pasteplugin/Utils"],function(e,t,n,r){return function(i){function o(e){i.on("BeforePastePreProcess",function(t){t.content=e(t.content)})}function a(e){if(!n.isWordContent(e))return e;var o=[];t.each(i.schema.getBlockElements(),function(e,t){o.push(t)});var a=new RegExp("(?:<br>&nbsp;[\\s\\r\\n]+|<br>)*(<\\/?("+o.join("|")+")[^>]*>)(?:<br>&nbsp;[\\s\\r\\n]+|<br>)*","g");return e=r.filter(e,[[a,"$1"]]),e=r.filter(e,[[/<br><br>/g,"<BR><BR>"],[/<br>/g," "],[/<BR><BR>/g,"<br>"]])}function s(e){if(n.isWordContent(e))return e;var t=i.settings.paste_webkit_styles;if(i.settings.paste_remove_styles_if_webkit===!1||"all"==t)return e;if(t&&(t=t.split(/[, ]/)),t){var r=i.dom,o=i.selection.getNode();e=e.replace(/(<[^>]+) style="([^"]*)"([^>]*>)/gi,function(e,n,i,a){var s=r.parseStyle(i,"span"),l={};if("none"===t)return n+a;for(var c=0;c<t.length;c++){var u=s[t[c]],d=r.getStyle(o,t[c],!0);/color/.test(t[c])&&(u=r.toHex(u),d=r.toHex(d)),d!=u&&(l[t[c]]=u)}return l=r.serializeStyle(l,"span"),l?n+' style="'+l+'"'+a:n+a})}else e=e.replace(/(<[^>]+) style="([^"]*)"([^>]*>)/gi,"$1$3");return e=e.replace(/(<[^>]+) data-mce-style="([^"]+)"([^>]*>)/gi,function(e,t,n,r){return t+' style="'+n+'"'+r})}e.webkit&&o(s),e.ie&&o(a)}}),r("tinymce/pasteplugin/Plugin",["tinymce/PluginManager","tinymce/pasteplugin/Clipboard","tinymce/pasteplugin/WordFilter","tinymce/pasteplugin/Quirks"],function(e,t,n,r){var i;e.add("paste",function(e){function o(){return i||e.settings.paste_plaintext_inform===!1}function a(){if("text"==s.pasteFormat)this.active(!1),s.pasteFormat="html",e.fire("PastePlainTextToggle",{state:!1});else if(s.pasteFormat="text",this.active(!0),!o()){var t=e.translate("Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.");e.notificationManager.open({text:t,type:"info"}),i=!0,e.fire("PastePlainTextToggle",{state:!0})}e.focus()}var s,l=this,c=e.settings;l.clipboard=s=new t(e),l.quirks=new r(e),l.wordFilter=new n(e),e.settings.paste_as_text&&(l.clipboard.pasteFormat="text"),c.paste_preprocess&&e.on("PastePreProcess",function(e){c.paste_preprocess.call(l,l,e)}),c.paste_postprocess&&e.on("PastePostProcess",function(e){c.paste_postprocess.call(l,l,e)}),e.addCommand("mceInsertClipboardContent",function(e,t){t.content&&l.clipboard.pasteHtml(t.content),t.text&&l.clipboard.pasteText(t.text)}),e.settings.paste_block_drop&&e.on("dragend dragover draggesture dragdrop drop drag",function(e){e.preventDefault(),e.stopPropagation()}),e.settings.paste_data_images||e.on("drop",function(e){var t=e.dataTransfer;t&&t.files&&t.files.length>0&&e.preventDefault()}),e.addButton("pastetext",{icon:"pastetext",tooltip:"Paste as text",onclick:a,active:"text"==l.clipboard.pasteFormat}),e.addMenuItem("pastetext",{text:"Paste as text",selectable:!0,active:s.pasteFormat,onclick:a})})}),o(["tinymce/pasteplugin/Utils"])}(this);