tinymce.PluginManager.add("importcss",function(e){function t(e){var t=tinymce.Env.cacheSuffix;return"string"==typeof e&&(e=e.replace("?"+t,"").replace("&"+t,"")),e}function n(t){var n=e.settings,r=n.skin!==!1?n.skin||"lightgray":!1;if(r){var i=n.skin_url;return i=i?e.documentBaseURI.toAbsolute(i):tinymce.baseURL+"/skins/"+r,t===i+"/content"+(e.inline?".inline":"")+".min.css"}return!1}function r(e){return"string"==typeof e?function(t){return-1!==t.indexOf(e)}:e instanceof RegExp?function(t){return e.test(t)}:e}function i(r,i){function o(e,r){var l,c=e.href;if(c=t(c),c&&i(c,r)&&!n(c)){s(e.imports,function(e){o(e,!0)});try{l=e.cssRules||e.rules}catch(u){}s(l,function(e){e.styleSheet?o(e.styleSheet,!0):e.selectorText&&s(e.selectorText.split(","),function(e){a.push(tinymce.trim(e))})})}}var a=[],l={};s(e.contentCSS,function(e){l[e]=!0}),i||(i=function(e,t){return t||l[e]});try{s(r.styleSheets,function(e){o(e)})}catch(c){}return a}function o(t){var n,r=/^(?:([a-z0-9\-_]+))?(\.[a-z0-9_\-\.]+)$/i.exec(t);if(r){var i=r[1],o=r[2].substr(1).split(".").join(" "),a=tinymce.makeMap("a,img");return r[1]?(n={title:t},e.schema.getTextBlockElements()[i]?n.block=i:e.schema.getBlockElements()[i]||a[i.toLowerCase()]?n.selector=i:n.inline=i):r[2]&&(n={inline:"span",title:t.substr(1),classes:o}),e.settings.importcss_merge_classes!==!1?n.classes=o:n.attributes={"class":o},n}}var a=this,s=tinymce.each;e.on("renderFormatsMenu",function(t){var n=e.settings,l={},c=n.importcss_selector_converter||o,u=r(n.importcss_selector_filter),d=t.control;e.settings.importcss_append||d.items().remove();var f=[];tinymce.each(n.importcss_groups,function(e){e=tinymce.extend({},e),e.filter=r(e.filter),f.push(e)}),s(i(t.doc||e.getDoc(),r(n.importcss_file_filter)),function(t){if(-1===t.indexOf(".mce-")&&!l[t]&&(!u||u(t))){var n,r=c.call(a,t);if(r){var i=r.name||tinymce.DOM.uniqueId();if(f)for(var o=0;o<f.length;o++)if(!f[o].filter||f[o].filter(t)){f[o].item||(f[o].item={text:f[o].title,menu:[]}),n=f[o].item.menu;break}e.formatter.register(i,r);var s=tinymce.extend({},d.settings.itemDefaults,{text:r.title,format:i});n?n.push(s):d.add(s)}l[t]=!0}}),s(f,function(e){d.add(e.item)}),t.control.renderNew()}),a.convertSelectorToFormat=o});