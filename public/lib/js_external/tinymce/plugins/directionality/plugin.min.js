tinymce.PluginManager.add("directionality",function(e){function t(t){var n,r=e.dom,i=e.selection.getSelectedBlocks();i.length&&(n=r.getAttrib(i[0],"dir"),tinymce.each(i,function(e){r.getParent(e.parentNode,"*[dir='"+t+"']",r.getRoot())||(n!=t?r.setAttrib(e,"dir",t):r.set<PERSON>ttrib(e,"dir",null))}),e.nodeChanged())}function n(e){var t=[];return tinymce.each("h1 h2 h3 h4 h5 h6 div p".split(" "),function(n){t.push(n+"[dir="+e+"]")}),t.join(",")}e.addCommand("mceDirectionLTR",function(){t("ltr")}),e.addCommand("mceDirectionRTL",function(){t("rtl")}),e.addButton("ltr",{title:"Left to right",cmd:"mceDirectionLTR",stateSelector:n("ltr")}),e.addButton("rtl",{title:"Right to left",cmd:"mceDirectionRTL",stateSelector:n("rtl")})});