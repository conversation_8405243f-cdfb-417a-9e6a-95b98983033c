tinymce.PluginManager.add("visualchars",function(e){function t(t){function n(e){return'<span data-mce-bogus="1" class="mce-'+h[e]+'">'+e+"</span>"}function o(){var e,t="";for(e in h)t+=e;return new RegExp("["+t+"]","g")}function a(){var e,t="";for(e in h)t&&(t+=","),t+="span.mce-"+h[e];return t}var s,l,c,u,d,f,h,p,m=e.getBody(),g=e.selection;if(h={"\xa0":"nbsp","\xad":"shy"},r=!r,i.state=r,e.fire("VisualChars",{state:r}),p=o(),t&&(f=g.getBookmark()),r)for(l=[],tinymce.walk(m,function(e){3==e.nodeType&&e.nodeValue&&p.test(e.nodeValue)&&l.push(e)},"childNodes"),c=0;c<l.length;c++){for(u=l[c].nodeValue,u=u.replace(p,n),d=e.dom.create("div",null,u);s=d.lastChild;)e.dom.insertAfter(s,l[c]);e.dom.remove(l[c])}else for(l=e.dom.select(a(),m),c=l.length-1;c>=0;c--)e.dom.remove(l[c],1);g.moveToBookmark(f)}function n(){var t=this;e.on("VisualChars",function(e){t.active(e.state)})}var r,i=this;e.addCommand("mceVisualChars",t),e.addButton("visualchars",{title:"Show invisible characters",cmd:"mceVisualChars",onPostRender:n}),e.addMenuItem("visualchars",{text:"Show invisible characters",cmd:"mceVisualChars",onPostRender:n,selectable:!0,context:"view",prependToContext:!0}),e.on("beforegetcontent",function(e){r&&"raw"!=e.format&&!e.draft&&(r=!0,t(!1))})});