tinymce.PluginManager.add("link",function(e){function t(t){return function(){var n=e.settings.link_list;"string"==typeof n?tinymce.util.XHR.send({url:n,success:function(e){t(tinymce.util.JSON.parse(e))}}):"function"==typeof n?n(t):t(n)}}function n(e,t,n){function r(e,n){return n=n||[],tinymce.each(e,function(e){var i={text:e.text||e.title};e.menu?i.menu=r(e.menu):(i.value=e.value,t&&t(i)),n.push(i)}),n}return r(e,n||[])}function r(t){function r(e){var t=d.find("#text");(!t.value()||e.lastControl&&t.value()==e.lastControl.text())&&t.value(e.control.text()),d.find("#href").value(e.control.value())}function i(t){var n=[];return tinymce.each(e.dom.select("a:not([href])"),function(e){var r=e.name||e.id;r&&n.push({text:r,value:"#"+r,selected:-1!=t.indexOf("#"+r)})}),n.length?(n.unshift({text:"None",value:""}),{name:"anchor",type:"listbox",label:"Anchors",values:n,onselect:r}):void 0}function o(){!u&&0===C.text.length&&f&&this.parent().parent().find("#text")[0].value(this.value())}function a(t){var n=t.meta||{};p&&p.value(e.convertURL(this.value(),"href")),tinymce.each(t.meta,function(e,t){d.find("#"+t).value(e)}),n.text||o.call(this)}function s(e){var t=x.getContent();if(/</.test(t)&&(!/^<a [^>]+>[^<]+<\/a>$/.test(t)||-1==t.indexOf("href=")))return!1;if(e){var n,r=e.childNodes;if(0===r.length)return!1;for(n=r.length-1;n>=0;n--)if(3!=r[n].nodeType)return!1}return!0}var l,c,u,d,f,h,p,m,g,v,y,b,C={},x=e.selection,w=e.dom;l=x.getNode(),c=w.getParent(l,"a[href]"),f=s(),C.text=u=c?c.innerText||c.textContent:x.getContent({format:"text"}),C.href=c?w.getAttrib(c,"href"):"",c?C.target=w.getAttrib(c,"target"):e.settings.default_link_target&&(C.target=e.settings.default_link_target),(b=w.getAttrib(c,"rel"))&&(C.rel=b),(b=w.getAttrib(c,"class"))&&(C["class"]=b),(b=w.getAttrib(c,"title"))&&(C.title=b),f&&(h={name:"text",type:"textbox",size:40,label:"Text to display",onchange:function(){C.text=this.value()}}),t&&(p={type:"listbox",label:"Link list",values:n(t,function(t){t.value=e.convertURL(t.value||t.url,"href")},[{text:"None",value:""}]),onselect:r,value:e.convertURL(C.href,"href"),onPostRender:function(){p=this}}),e.settings.target_list!==!1&&(e.settings.target_list||(e.settings.target_list=[{text:"None",value:""},{text:"New window",value:"_blank"}]),g={name:"target",type:"listbox",label:"Target",values:n(e.settings.target_list)}),e.settings.rel_list&&(m={name:"rel",type:"listbox",label:"Rel",values:n(e.settings.rel_list)}),e.settings.link_class_list&&(v={name:"class",type:"listbox",label:"Class",values:n(e.settings.link_class_list,function(t){t.value&&(t.textStyle=function(){return e.formatter.getCssText({inline:"a",classes:[t.value]})})})}),e.settings.link_title!==!1&&(y={name:"title",type:"textbox",label:"Title",value:C.title}),d=e.windowManager.open({title:"Insert link",data:C,body:[{name:"href",type:"filepicker",filetype:"file",size:40,autofocus:!0,label:"Url",onchange:a,onkeyup:o},h,y,i(C.href),p,m,g,v],onSubmit:function(t){function n(t,n){var r=e.selection.getRng();tinymce.util.Delay.setEditorTimeout(e,function(){e.windowManager.confirm(t,function(t){e.selection.setRng(r),n(t)})})}function r(){var t={href:i,target:C.target?C.target:null,rel:C.rel?C.rel:null,"class":C["class"]?C["class"]:null,title:C.title?C.title:null};c?(e.focus(),f&&C.text!=u&&("innerText"in c?c.innerText=C.text:c.textContent=C.text),w.setAttribs(c,t),x.select(c),e.undoManager.add()):f?e.insertContent(w.createHTML("a",t,w.encode(C.text))):e.execCommand("mceInsertLink",!1,t)}var i;return C=tinymce.extend(C,t.data),(i=C.href)?i.indexOf("@")>0&&-1==i.indexOf("//")&&-1==i.indexOf("mailto:")?void n("The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?",function(e){e&&(i="mailto:"+i),r()}):e.settings.link_assume_external_targets&&!/^\w+:/i.test(i)||!e.settings.link_assume_external_targets&&/^\s*www[\.|\d\.]/i.test(i)?void n("The URL you entered seems to be an external link. Do you want to add the required http:// prefix?",function(e){e&&(i="http://"+i),r()}):void r():void e.execCommand("unlink")}})}e.addButton("link",{icon:"link",tooltip:"Insert/edit link",shortcut:"Meta+K",onclick:t(r),stateSelector:"a[href]"}),e.addButton("unlink",{icon:"unlink",tooltip:"Remove link",cmd:"unlink",stateSelector:"a[href]"}),e.addShortcut("Meta+K","",t(r)),e.addCommand("mceLink",t(r)),this.showDialog=r,e.addMenuItem("link",{icon:"link",text:"Insert/edit link",shortcut:"Meta+K",onclick:t(r),stateSelector:"a[href]",context:"insert",prependToContext:!0})});