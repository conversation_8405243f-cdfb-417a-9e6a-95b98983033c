tinymce.ThemeManager.add("modern",function(e){function t(t,n){var r,i=[];if(t)return p(t.split(/[ ,]/),function(t){function o(){function n(e){return function(n,r){for(var i,o=r.parents.length;o--&&(i=r.parents[o].nodeName,"OL"!=i&&"UL"!=i););t.active(n&&i==e)}}var r=e.selection;"bullist"==a&&r.selectorChanged("ul > li",n("UL")),"numlist"==a&&r.selectorChanged("ol > li",n("OL")),t.settings.stateSelector&&r.selectorChanged(t.settings.stateSelector,function(e){t.active(e)},!0),t.settings.disabledStateSelector&&r.selectorChanged(t.settings.disabledStateSelector,function(e){t.disabled(e)})}var a;"|"==t?r=null:h.has(t)?(t={type:t,size:n},i.push(t),r=null):(r||(r={type:"buttongroup",items:[]},i.push(r)),e.buttons[t]&&(a=t,t=e.buttons[a],"function"==typeof t&&(t=t()),t.type=t.type||"button",t.size=n,t=h.create(t),r.items.push(t),e.initialized?o():e.on("init",o)))}),{type:"toolbar",layout:"flow",items:i}}function n(e){function n(n){return n?(r.push(t(n,e)),!0):void 0}var r=[];if(tinymce.isArray(f.toolbar)){if(0===f.toolbar.length)return;tinymce.each(f.toolbar,function(e,t){f["toolbar"+(t+1)]=e}),delete f.toolbar}for(var i=1;10>i&&n(f["toolbar"+i]);i++);return r.length||f.toolbar===!1||n(f.toolbar||b),r.length?{type:"panel",layout:"stack",classes:"toolbar-grp",ariaRoot:!0,ariaRemember:!0,items:r}:void 0}function r(){function t(t){var n;return"|"==t?{text:"|"}:n=e.menuItems[t]}function n(n){var r,i,o,a,s;if(s=tinymce.makeMap((f.removed_menuitems||"").split(/[ ,]/)),f.menu?(i=f.menu[n],a=!0):i=y[n],i){r={text:i.title},o=[],p((i.items||"").split(/[ ,]/),function(e){var n=t(e);n&&!s[e]&&o.push(t(e))}),a||p(e.menuItems,function(e){e.context==n&&("before"==e.separator&&o.push({text:"|"}),e.prependToContext?o.unshift(e):o.push(e),"after"==e.separator&&o.push({text:"|"}))});for(var l=0;l<o.length;l++)"|"==o[l].text&&(0!==l&&l!=o.length-1||o.splice(l,1));if(r.menu=o,!r.menu.length)return null}return r}var r,i=[],o=[];if(f.menu)for(r in f.menu)o.push(r);else for(r in y)o.push(r);for(var a="string"==typeof f.menubar?f.menubar.split(/[ ,]/):o,s=0;s<a.length;s++){var l=a[s];l=n(l),l&&i.push(l)}return i}function i(t){function n(e){var n=t.find(e)[0];n&&n.focus(!0)}e.shortcuts.add("Alt+F9","",function(){n("menubar")}),e.shortcuts.add("Alt+F10","",function(){n("toolbar")}),e.shortcuts.add("Alt+F11","",function(){n("elementpath")}),t.on("cancel",function(){e.focus()})}function o(t,n){function r(e){return{width:e.clientWidth,height:e.clientHeight}}var i,o,a,s;i=e.getContainer(),o=e.getContentAreaContainer().firstChild,a=r(i),s=r(o),null!==t&&(t=Math.max(f.min_width||100,t),t=Math.min(f.max_width||65535,t),m.setStyle(i,"width",t+(a.width-s.width)),m.setStyle(o,"width",t)),n=Math.max(f.min_height||100,n),n=Math.min(f.max_height||65535,n),m.setStyle(o,"height",n),e.fire("ResizeEditor")}function a(t,n){var r=e.getContentAreaContainer();d.resizeTo(r.clientWidth+t,r.clientHeight+n)}function s(){function n(){return e.contextToolbars||[]}function r(t){var n,r,i;return n=tinymce.DOM.getPos(e.getContentAreaContainer()),r=e.dom.getRect(t),i=e.dom.getRoot(),"BODY"==i.nodeName&&(r.x-=i.ownerDocument.documentElement.scrollLeft||i.scrollLeft,r.y-=i.ownerDocument.documentElement.scrollTop||i.scrollTop),r.x+=n.x,r.y+=n.y,r}function i(){p(e.contextToolbars,function(e){e.panel&&e.panel.hide()})}function o(e,t,n){t=t?t.substr(0,2):"",p({t:"down",b:"up"},function(r,i){e.classes.toggle("arrow-"+r,n(i,t.substr(0,1)))}),p({l:"left",r:"right"},function(r,i){e.classes.toggle("arrow-"+r,n(i,t.substr(1,1)))})}function a(e){return{left:e.x,top:e.y,width:e.w,height:e.h,right:e.x+e.w,bottom:e.y+e.h}}function s(e,t,n,r,i){return i=a({x:e,y:t,w:i.w,h:i.h}),f.inline_toolbar_position_handler&&(i=f.inline_toolbar_position_handler({elementRect:a(n),contentAreaRect:a(r),panelRect:i})),i}function l(e,t){e.moveTo(t.left,t.top)}function c(t){var n,a,c,u,d,f,h;if(!e.removed){if(!t||!t.toolbar.panel)return void i();h=["bc-tc","tc-bc","tl-bl","bl-tl","tr-br","br-tr"],d=t.toolbar.panel,d.show(),c=r(t.element),a=tinymce.DOM.getRect(d.getEl()),u=tinymce.DOM.getRect(e.getContentAreaContainer()||e.getBody()),c.w=t.element.clientWidth,c.h=t.element.clientHeight,e.inline||(u.w=e.getDoc().documentElement.offsetWidth),e.selection.controlSelection.isResizable(t.element)&&(c=g.inflate(c,0,8)),n=g.findBestRelativePosition(a,c,u,h),c=g.clamp(c,u),n?(f=g.relativePosition(a,c,n),l(d,s(f.x,f.y,c,u,a))):(u.h+=40,c=g.intersect(u,c),c?(n=g.findBestRelativePosition(a,c,u,["bc-tc","bl-tl","br-tr"]),n?(f=g.relativePosition(a,c,n),l(d,s(f.x,f.y,c,u,a))):l(d,s(c.x,c.y,c,u,a))):d.hide()),o(d,n,function(e,t){return(!c||c.w>40)&&e===t})}}function u(){function t(){e.selection&&c(y(e.selection.getNode()))}tinymce.util.Delay.requestAnimationFrame(t)}function d(){b||(b=e.selection.getScrollContainer()||e.getWin(),tinymce.$(b).on("scroll",u),e.on("remove",function(){tinymce.$(b).off("scroll")}))}function m(n){var r;return n.toolbar.panel?(n.toolbar.panel.show(),void c(n)):(d(),r=h.create({type:"floatpanel",role:"dialog",classes:"tinymce tinymce-inline arrow",ariaLabel:"Inline toolbar",layout:"flex",direction:"column",align:"stretch",autohide:!1,autofix:!0,fixed:!0,border:1,items:t(n.toolbar.items),oncancel:function(){e.focus()}}),n.toolbar.panel=r,r.renderTo(document.body).reflow(),void c(n))}function v(){tinymce.each(n(),function(e){e.panel&&e.panel.hide()})}function y(t){var r,i,o,a=n();for(o=e.$(t).parents().add(t),r=o.length-1;r>=0;r--)for(i=a.length-1;i>=0;i--)if(a[i].predicate(o[r]))return{toolbar:a[i],element:o[r]};return null}var b;e.on("click keyup setContent",function(t){("setcontent"!=t.type||t.selection)&&tinymce.util.Delay.setEditorTimeout(e,function(){var t;t=y(e.selection.getNode()),t?(v(),m(t)):v()})}),e.on("blur hide",v),e.on("ObjectResizeStart",function(){var t=y(e.selection.getNode());t&&t.toolbar.panel&&t.toolbar.panel.hide()}),e.on("nodeChange ResizeEditor ResizeWindow",u),e.on("remove",function(){tinymce.each(n(),function(e){e.panel&&e.panel.remove()}),e.contextToolbars={}}),e.shortcuts.add("ctrl+shift+e > ctrl+shift+p","",function(){var t=y(e.selection.getNode());t&&t.toolbar.panel&&t.toolbar.panel.items()[0].focus()})}function l(e){return function(){e.initialized?e.fire("SkinLoaded"):e.on("init",function(){e.fire("SkinLoaded")})}}function c(t){function o(){if(p&&p.moveRel&&p.visible()&&!p._fixed){var t=e.selection.getScrollContainer(),n=e.getBody(),r=0,i=0;if(t){var o=m.getPos(n),a=m.getPos(t);r=Math.max(0,a.x-o.x),i=Math.max(0,a.y-o.y)}p.fixed(!1).moveRel(n,e.rtl?["tr-br","br-tr"]:["tl-bl","bl-tl","tr-br"]).moveBy(r,i)}}function a(){p&&(p.show(),o(),m.addClass(e.getBody(),"mce-edit-focus"))}function c(){p&&(p.hide(),v.hideAll(),m.removeClass(e.getBody(),"mce-edit-focus"))}function u(){return p?void(p.visible()||a()):(p=d.panel=h.create({type:g?"panel":"floatpanel",role:"application",classes:"tinymce tinymce-inline",layout:"flex",direction:"column",align:"stretch",autohide:!1,autofix:!0,fixed:!!g,border:1,items:[f.menubar===!1?null:{type:"menubar",border:"0 0 1 0",items:r()},n(f.toolbar_items_size)]}),e.fire("BeforeRenderUI"),p.renderTo(g||document.body).reflow(),i(p),a(),s(),e.on("nodeChange",o),e.on("activate",a),e.on("deactivate",c),void e.nodeChanged())}var p,g;return f.fixed_toolbar_container&&(g=m.select(f.fixed_toolbar_container)[0]),f.content_editable=!0,e.on("focus",function(){t.skinUiCss?tinymce.DOM.styleSheetLoader.load(t.skinUiCss,u,u):u()}),e.on("blur hide",c),e.on("remove",function(){p&&(p.remove(),p=null)}),t.skinUiCss&&tinymce.DOM.styleSheetLoader.load(t.skinUiCss,l(e)),{}}function u(t){function a(){return function(e){"readonly"==e.mode?c.find("*").disabled(!0):c.find("*").disabled(!1)}}var c,u,p;return t.skinUiCss&&tinymce.DOM.styleSheetLoader.load(t.skinUiCss,l(e)),c=d.panel=h.create({type:"panel",role:"application",classes:"tinymce",style:"visibility: hidden",layout:"stack",border:1,items:[f.menubar===!1?null:{type:"menubar",border:"0 0 1 0",items:r()},n(f.toolbar_items_size),{type:"panel",name:"iframe",layout:"stack",classes:"edit-area",html:"",border:"1 0 0 0"}]}),f.resize!==!1&&(u={type:"resizehandle",direction:f.resize,onResizeStart:function(){var t=e.getContentAreaContainer().firstChild;p={width:t.clientWidth,height:t.clientHeight}},onResize:function(e){"both"==f.resize?o(p.width+e.deltaX,p.height+e.deltaY):o(null,p.height+e.deltaY)}}),f.statusbar!==!1&&c.add({type:"panel",name:"statusbar",classes:"statusbar",layout:"flow",border:"1 0 0 0",ariaRoot:!0,items:[{type:"elementpath"},u]}),e.fire("BeforeRenderUI"),e.on("SwitchMode",a()),c.renderBefore(t.targetNode).reflow(),f.readonly&&e.setMode("readonly"),f.width&&tinymce.DOM.setStyle(c.getEl(),"width",f.width),e.on("remove",function(){c.remove(),c=null}),i(c),s(),{iframeContainer:c.find("#iframe")[0].getEl(),editorContainer:c.getEl()}}var d=this,f=e.settings,h=tinymce.ui.Factory,p=tinymce.each,m=tinymce.DOM,g=tinymce.geom.Rect,v=tinymce.ui.FloatPanel,y={file:{title:"File",items:"newdocument"},edit:{title:"Edit",items:"undo redo | cut copy paste pastetext | selectall"},insert:{title:"Insert",items:"|"},view:{title:"View",items:"visualaid |"},format:{title:"Format",items:"bold italic underline strikethrough superscript subscript | formats | removeformat"},table:{title:"Table"},tools:{title:"Tools"}},b="undo redo | styleselect | bold italic | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link image";d.renderUI=function(t){var n=f.skin!==!1?f.skin||"lightgray":!1;if(n){var r=f.skin_url;r=r?e.documentBaseURI.toAbsolute(r):tinymce.baseURL+"/skins/"+n,tinymce.Env.documentMode<=7?t.skinUiCss=r+"/skin.ie7.min.css":t.skinUiCss=r+"/skin.min.css",e.contentCSS.push(r+"/content"+(e.inline?".inline":"")+".min.css")}return e.on("ProgressState",function(e){d.throbber=d.throbber||new tinymce.ui.Throbber(d.panel.getEl("body")),e.state?d.throbber.show(e.time):d.throbber.hide()}),f.inline?c(t):u(t)},d.resizeTo=o,d.resizeBy=a});