Namespace('TF.OSZ.Home');

TF.OSZ.Home = (function ($) {
    $.fn.datagrid.defaults.loader = EasyUIRPCLoaders.EasyUIGridCustomLoader.loader;
    $.fn.datagrid.defaults.loadFilter = EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter;

    $.fn.tree.defaults.loader = EasyUIRPCLoaders.EasyUIGridCustomLoader.loader;
    $.fn.tree.defaults.loadFilter = EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter;

    $.fn.combobox.defaults.loader = EasyUIRPCLoaders.EasyUIGridCustomLoader.loader;
    $.fn.combobox.defaults.loadFilter = EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter;

    //retrieve GET parameters
	GET = {};
	location.search.substr(1).split("&").forEach(function(item) {GET[decodeURIComponent(item.split("=")[0])] = decodeURIComponent(item.split("=")[1])});

	//change URL without refresh if it's posible
	if (history && history.replaceState){
		history.replaceState(null, null, 'index.php?page=' + GET.page);
	}
    setUserRights();
}(jQuery));
