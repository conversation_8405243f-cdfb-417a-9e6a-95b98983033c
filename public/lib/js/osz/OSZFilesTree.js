Namespace('TF.OSZ.HypothecsTree');

TF.OSZ.OSZFilesTree = (function ($) {
    var oszFilesTree = $('#osz-files-tree');
    var landTb = $('#land');
    var ekatteTb = $('#ekatte');
    var searchDateDb = $('#search-date');
    var uploader;
    
    searchDateDb.datebox();
    
    oszFilesTree.tree({
		url: 'index.php?osz-rpc=osz-files',
		animate: true,
		lines: true,
		sort: 'id',
		order: 'desc',
        rows: 50,
        page: 1,
		rpcParams: [null],
		onSelect: function(node) {
            TF.OSZ.OSZFilesPlotsGrid.grid.datagrid('options').rpcParams = [{
                    file_id: node.id,
                    ekatte: node.attributes.ekatte
            }];
            TF.OSZ.OSZFilesPlotsGrid.grid.datagrid('reload');
            TF.OSZ.OSZFilesPlotsGrid.clearFilterFields();
		},
		onLoadSuccess: function() {
			var roots = oszFilesTree.tree('getRoots');
			var total = 0;
			var limit = 30;

			if (roots.length) {
                oszFilesTree.tree('select', roots[0].target);

				total = roots[0]['attributes']['pagination']['total'];
				limit = roots[0]['attributes']['pagination']['limit'];
			}
            else
            {
                TF.OSZ.OSZFilesPlotsGrid.grid.datagrid('options').rpcParams = [null];
                TF.OSZ.OSZFilesPlotsGrid.grid.datagrid('reload');
                TF.OSZ.OSZFilesPlotsGrid.grid.datagrid('reloadFooter', []);
                TF.OSZ.OSZFilesPlotsGrid.clearFilterFields();
            }

			//init pagination with total contract elements
			initPagination(total, limit);
		}
	});

    $('#add-osz-file-btn').on('click', function(e) {
        if (!hasMapRightsRW) {
            messagerMapWriteRights();
            return false;
        };
		e.preventDefault();
        
        landTb.textbox({
            required: true,
            missingMessage: 'Това поле е задължително.',
            value: '',
            validType:{
                length:[0,65]
            }
        });
        
        ekatteTb.textbox({
            required: true,
            missingMessage: 'Това поле е задължително.',
            value: '',
            validType:{
                length:[0,5]
            }
        });
        
        initFileUploads();
        $('#win-add-file').window('open');
	});
    
    $('#save-osz-file-btn').on('click', function(e) {
		e.preventDefault();
        
        if(!landTb.textbox('isValid')) {
            landTb.textbox('textbox').focus();
            return;
        }
        
        if(!ekatteTb.datebox('isValid')) {
            ekatteTb.datebox('textbox').focus();
            return;
        }
        
        if(!uploader) {
            return;
        }
        
        if(!uploader.files.length) {
            $.messager.alert('Грешка', 'Моля изберете файл.');
            return;
        }
        
        uploader.settings.multipart_params.land = landTb.textbox('getValue');
        uploader.settings.multipart_params.ekatte = ekatteTb.textbox('getValue');
        
        uploader.start();
	});
    
    $('#delete-osz-file-btn').on('click', function(e) {
        if (!hasMapRightsRW) {
            messagerMapWriteRights();
            return false;
        };
		e.preventDefault();
        
        var selectedRow = oszFilesTree.tree('getSelected');
                    
        if (!selectedRow) {
            $.messager.alert('Грешка', 'Моля изберете файл.');
            return;
        }

        $.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да премахнете този файл?', function(r) {
            if (r) {
                TF.Rpc.OSZ.OSZFiles.delete(selectedRow.id)
                    .done(function(data) {
                        oszFilesTree.tree('reload');
                    })
                    .fail(function(error) {
                        $.messager.alert('Грешка', error.getMessage());
                    });
            }
        });
	});
    
    $('#filter-osz-files-btn').on('click', function(e) {
		e.preventDefault();
        
        $('#win-osz-files-filter').window('open');
	});
    
    $('#search-osz-files-btn').on('click', function(e) {
		e.preventDefault();
        
        var data = {};
        
        data.land = $('#search-land').val();
        data.ekatte = $('#search-ekatte').val();
        data.date = searchDateDb.datebox('getValue');
        
        oszFilesTree.tree('options').rpcParams = [data];
        oszFilesTree.tree('reload');
        
        $('#win-osz-files-filter').window('close');
	});
    
    $('#clear-filter-osz-files-btn').on('click', function(e) {
		e.preventDefault();
        
        $('#search-land').val('');
        $('#search-ekatte').val('');
        searchDateDb.datebox('clear');
        
        oszFilesTree.tree('options').rpcParams = [null];
        oszFilesTree.tree('reload');
	});
    
    function initPagination(total, limit) {
        $('#osz-files-tree-pagination').pagination({
            showPageList: false,
            showRefresh: false,
            beforePageText: 'Стр.',
            displayMsg: '',
            total: total,
            pageSize: limit,
            onSelectPage: function(pageNumber, pageSize) {
                oszFilesTree.tree('options').page = pageNumber;
                oszFilesTree.tree('reload');
            }
        });
    }
    
    function initFileUploads()
    {
        const url  = "index.php?json=osz-upload"; 
        
        $("#uploader").pluploadQueue({
            // General settings
            runtimes: 'gears,html5,flash,silverlight,browserplus',
            url: url,
            max_file_size: '100mb',
            multi_selection: false,
            unique_names: true,
            multipart_params: {
            },
            // Flash settings
            flash_swf_url: 'lib/js_external/fileupload/plupload.flash.swf'
        });

        uploader = $("#uploader").pluploadQueue();
        
        uploader.bind('FilesAdded', function (up, files) {
            var fileCount = up.files.length;
            var ids = $.map(up.files, function (item) { return item.id; });

            for (var i = 0; i < fileCount - 1; i++) {
                uploader.removeFile(uploader.getFile(ids[i]));
            }
        });
        
        uploader.bind('UploadComplete', function() {
            $('#win-add-file').window('close');
            
            $.messager.alert('Информация', 'Зареждането може да отнеме повече време, докато се появят всички имоти в таблицата.');
            
            setTimeout(function() {
                oszFilesTree.tree('reload');
            }, 1000);
        });
        
        $('.plupload_start').hide();
    }

    return {
        tree: oszFilesTree
    };
}(jQuery));