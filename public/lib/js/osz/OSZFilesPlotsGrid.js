Namespace('TF.OSZ.OSZFilesPlotsGrid');
var _fileName;
TF.OSZ.OSZFilesPlotsGrid = (function ($) {
    var oszFilesPlotsGrid = jQuery('#hypothecs-plots-grid');
    var currentFilter = {};
    
    oszFilesPlotsGrid.datagrid({
		iconCls: 'icon-edit-geometry',
		nowrap: true,
		title: 'Имоти',
		autoRowHeight: true,
		striped: true,
		pageSize: 50,
		fit: true,
		singleSelect: true,
		fitColumns: false,
		showFooter: true,
		url: 'index.php?osz-rpc=osz-files-plots',
        rpcParams: [null],
		sortName: 'id',
		sortOrder: 'asc',
		idField: 'id',
        rowStyler: function(index, row){
            if (!row.kad_ident){
                return 'background-color:#ffff00;';	
            }
            
            if (!row.kvs_no){
                return 'background-color:#0000ff;color:#ffffff';	
            }
            
            if (row.contract_subekt && row.contract_subekt.split(', ').indexOf(row.egn_subekt) === -1){
                return 'background-color:#db3333;';	
            }
        },
		columns: [[
				{
					field: 'kad_ident',
					title: '<b>Имот КВС</b>',
					sortable: true,
					width: 100
				}, {
					field: 'contract_subekt',
					title: '<b>В договор с</b>',
					sortable: true,
					width: 100
				}, {
					field: 'kvs_no',
					title: '<b>KVS_NO</b>',
					sortable: true,
					width: 100
				}, {
					field: 'kad_no',
					title: '<b>KAD_NO</b>',
					sortable: true,
					width: 100
				}, {
					field: 'kod_subekt',
					title: '<b>KOD_SUBEKT</b>',
					sortable: true,
					width: 100
                }, {
					field: 'txt_subekt',
					title: '<b>TXT_SUBEKT</b>',
					sortable: true,
					width: 100
                }, {
					field: 'ime_subekt',
					title: '<b>IME_SUBEKT</b>',
					sortable: true,
					width: 100
                }, {
					field: 'egn_subekt',
					title: '<b>EGN_SUBEKT</b>',
					sortable: true,
					width: 100
                }, {
					field: 'kod_pr_osn',
					title: '<b>KOD_PR_OSN</b>',
					sortable: true,
					width: 100
                }, {
					field: 'txt_pr_osn',
					title: '<b>TXT_PR_OSN</b>',
					sortable: true,
					width: 100
                }, {
					field: 'pl_dka',
					title: '<b>PL_DKA</b>',
					sortable: true,
					width: 100
                }, {
					field: 'pl_dka_po',
					title: '<b>PL_DKA_PO</b>',
					sortable: true,
					width: 100
                }, {
					field: 'kategoria',
					title: '<b>KATEGORIA</b>',
					sortable: true,
					width: 100
                }, {
					field: 'kod_ntp',
					title: '<b>KOD_NTP</b>',
					sortable: true,
					width: 100
                }, {
					field: 'txt_ntp',
					title: '<b>TXT_NTP</b>',
					sortable: true,
					width: 100
                }, {
					field: 'kod_sobstv',
					title: '<b>KOD_SOBSTV</b>',
					sortable: true,
					width: 100
                }, {
					field: 'txt_sobstv',
					title: '<b>TXT_SOBSTV</b>',
					sortable: true,
					width: 100
                }, {
					field: 'kod_imot',
					title: '<b>KOD_IMOT</b>',
					sortable: true,
					width: 100
                }, {
					field: 'txt_imot',
					title: '<b>TXT_IMOT</b>',
					sortable: true,
					width: 100
                }, {
					field: 'ekatte',
					title: '<b>EKATTE</b>',
					sortable: true,
					width: 100
                }, {
					field: 'ver_no',
					title: '<b>VER_NO</b>',
					sortable: true,
					width: 100
                }, {
					field: 'data',
					title: '<b>DATA</b>',
					sortable: true,
					width: 100
                }, {
					field: 'vreme',
					title: '<b>VREME</b>',
					sortable: true,
					width: 100
                }
			]],
		pagination: true,
		rownumbers: true,
		toolbar: [
			{
                id: 'btnexportexceloszplotsgrid',
                text: 'Експорт Excel',
                iconCls: 'icon-csv',
                handler: function() {

                    var exportObj = oszFilesPlotsGrid.datagrid('options').rpcParams[0];
                    var sort = oszFilesPlotsGrid.datagrid('options').sortName;
                    var order = oszFilesPlotsGrid.datagrid('options').sortOrder;

                    TF.Rpc.OSZ.OSZFilesPlots.exportExcel(exportObj,sort, order)
                    .done(function (data) {
                        createDownloadVariables();
                        winDownloadDiaryReport.window('open');
                        var path = data;
                        _pathFile = data.filePath;
                        _fileName = data.fileName;
                        downloadFileDiaryReport.attr("href", _pathFile); 
                    })
                    .fail(function (errorObj) {

                    });
                }
            },
            {
                id: 'btnimportoszowners',
                text: 'Зареди собственици',
                iconCls: 'icon-upload',
                handler: function() {

                    jQuery.messager.confirm('Потвърждение', 'Всички данни на собственици, които имат имоти в системното КВС да бъдат заредени в подмодул "Собственици"?', function(r)
                    {
                        if (r)
                        {
                            var filterObj = oszFilesPlotsGrid.datagrid('options').rpcParams[0];

                            TF.Rpc.OSZ.OSZFilesPlots.importOwners(filterObj)
                            .done(function (data) {
                            })
                            .fail(function (errorObj) {
                            });
                        }
                    });
                }
            }
		],
		onLoadSuccess: function() {
			oszFilesPlotsGrid.datagrid('clearChecked');
		}
	});
    
    
    $('#filter-osz-files-plots-btn').on('click', function(e) {
		e.preventDefault();
        
        var data = oszFilesPlotsGrid.datagrid('options').rpcParams[0];
        
        data.action = 'new';
        data.kad_no = $('#search-kad-no').val();
        data.ime_subekt = $('#search-ime-subekt').val();
        data.egn_subekt = $('#search-egn-subekt').val();
        data.kategoria = $('#search-kategoria').val();
        data.txt_ntp = $('#search-txt-ntp').val();
        
        if($('#search-not-in-kvs').is(':checked')) {
            data.not_in_kvs = true;
        }
        else {
            data.not_in_kvs = false;
        }
        if($('#search-only-in-kvs').is(':checked')) {
            data.only_in_kvs = true;
        }
        else {
            data.only_in_kvs = false;
        }
        if($('#search-different-subekt').is(':checked')) {
            data.different_subekt = true;
        }
        else {
            data.different_subekt = false;
        }
        
        currentFilter = {};
        currentFilter.kad_no = [data.kad_no].filter(Boolean);
        currentFilter.ime_subekt = [data.ime_subekt].filter(Boolean);
        currentFilter.egn_subekt = [data.egn_subekt].filter(Boolean);
        currentFilter.kategoria = [data.kategoria].filter(Boolean);
        currentFilter.txt_ntp = [data.txt_ntp].filter(Boolean);
        
        currentFilter.not_in_kvs = data.not_in_kvs;
        currentFilter.only_in_kvs = data.only_in_kvs;
        currentFilter.different_subekt = data.different_subekt;
        
        showCurrentFilterInfo(currentFilter);
        
        oszFilesPlotsGrid.datagrid('reload');
	});
    
    $('#add-filter-osz-files-plots-btn').on('click', function(e) {
		e.preventDefault();
        
        var params = oszFilesPlotsGrid.datagrid('options').rpcParams[0];
        var fileId = params.file_id;
        var ekatte= params.ekatte;
        
        var data = {};
        
        data.action = 'add';
        data.file_id = fileId;
        data.ekatte = ekatte;
        data.kad_no = $('#search-kad-no').val();
        data.ime_subekt = $('#search-ime-subekt').val();
        data.egn_subekt = $('#search-egn-subekt').val();
        data.kategoria = $('#search-kategoria').val();
        data.txt_ntp = $('#search-txt-ntp').val();
        
        if($('#search-not-in-kvs').is(':checked')) {
            data.not_in_kvs = true;
        }
        if($('#search-only-in-kvs').is(':checked')) {
            data.only_in_kvs = true;
        }
        if($('#search-different-subekt').is(':checked')) {
            data.different_subekt = true;
        }
        
        currentFilter.kad_no = currentFilter.kad_no || [];
        currentFilter.kad_no = currentFilter.kad_no.concat(data.kad_no).filter(Boolean);
        currentFilter.ime_subekt = currentFilter.ime_subekt || [];
        currentFilter.ime_subekt = currentFilter.ime_subekt.concat(data.ime_subekt).filter(Boolean);
        currentFilter.egn_subekt = currentFilter.egn_subekt || [];
        currentFilter.egn_subekt = currentFilter.egn_subekt.concat(data.egn_subekt).filter(Boolean);
        currentFilter.kategoria = currentFilter.kategoria || [];
        currentFilter.kategoria = currentFilter.kategoria.concat(data.kategoria).filter(Boolean);
        currentFilter.txt_ntp = currentFilter.txt_ntp || [];
        currentFilter.txt_ntp = currentFilter.txt_ntp.concat(data.txt_ntp).filter(Boolean);
        
        currentFilter.not_in_kvs = currentFilter.not_in_kvs || data.not_in_kvs;
        currentFilter.only_in_kvs = currentFilter.only_in_kvs || data.only_in_kvs;
        currentFilter.different_subekt = currentFilter.different_subekt || data.different_subekt;
        
        showCurrentFilterInfo(currentFilter);
        
        oszFilesPlotsGrid.datagrid('options').rpcParams = [data];
        oszFilesPlotsGrid.datagrid('reload');
	});
    
    $('#clear-filter-osz-files-plots-btn').on('click', function(e) {
        e.preventDefault();
        
        clearFilterFields();
        
        currentFilter = {};
        
		var data = oszFilesPlotsGrid.datagrid('options').rpcParams[0];
        var fileId = data.file_id;
        var ekatte= data.ekatte;

        oszFilesPlotsGrid.datagrid('options').rpcParams = [{
            file_id: fileId,
            ekatte: ekatte,
            action: 'clear'
        }];

        oszFilesPlotsGrid.datagrid('reload');
	});
    
    function showCurrentFilterInfo(data) {
        var txtArr = [];
        
        if(data.kad_no.length) {
            txtArr.push('Имот ' + data.kad_no.join(', '));
        }
        if(data.ime_subekt.length) {
            txtArr.push('Ползвател ' + data.ime_subekt.join(', '));
        }
        if(data.egn_subekt.length) {
            txtArr.push('Идентификатор ' + data.egn_subekt.join(', '));
        }
        if(data.kategoria.length) {
            txtArr.push('Категория ' + data.kategoria.join(', '));
        }
        if(data.txt_ntp.length) {
            txtArr.push('НТП ' + data.txt_ntp.join(', '));
        }
        if(data.not_in_kvs) {
            txtArr.push('Покажи само имотите, които не фигурират в системно КВС');
        }
        if(data.only_in_kvs) {
            txtArr.push('Покажи само имотите от системното КВС, които не фигурират във файла');
        }
        if(data.different_subekt) {
            txtArr.push('Покажи само имотите с договор, за които ползвателя от файла е различен');
        }
        
        $('#osz-current-filter').html(txtArr.join(' <b>и</b> '));
    }
    
    //public methods
    var clearFilterFields = function() {
        $('#search-kad-no').val('');
        $('#search-ime-subekt').val('');
        $('#search-egn-subekt').val('');
        $('#search-kategoria').val('');
        $('#search-txt-ntp').val('');
        
        $('#search-not-in-kvs').attr('checked', false);
        $('#search-only-in-kvs').attr('checked', false);
        $('#search-different-subekt').attr('checked', false);
        
        $('#osz-current-filter').html('');
    };

    return {
        grid: oszFilesPlotsGrid,
        clearFilterFields: clearFilterFields
    };
}(jQuery));


function createDownloadVariables() {
    winDownloadDiaryReport = jQuery('#win-download').window({
        onClose: onDownloadOSZtWindowClose
    });

    downloadFileDiaryReport = jQuery('#btn-download-file');
    cancelDownloadFileDiaryReport = jQuery('#btn-download-file-close');
}

function onDownloadOSZtWindowClose() {
   return;    
}