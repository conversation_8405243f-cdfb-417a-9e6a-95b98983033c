//init global variables
var map;
var controlNavigation;
var controlScaleLine;
var controlZoomIn;
var controlZoomOut;
var ComboboxData;
var bingApiKey = "AiWEso3-IjWyX1aZMuep9Sjl62D6FUMqv8qQGpV-kgbW0qWk61to4nrqHh-2D5HL";
var renderer = OpenLayers.Util.getParameters(window.location.href).renderer;
if (renderer) {
	OpenLayers.Layer.Vector.prototype.renderers = [renderer];
}

jQuery(function () {
    jQuery.fn.combobox.defaults.loader = EasyUIRPCLoaders.EasyUIGridCustomLoader.loader;
    jQuery.fn.combobox.defaults.loadFilter = EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter;
});

OpenLayers.ProxyHost = Settings.OPEN_LAYERS_PROXY;

//on body load
jQuery(function()
{
	if (typeof ComboboxData == undefined) {
		TF.Rpc.Common.CombinedComboboxData.read(true)
			.done(function (data) {
				ComboboxData = data;
				jQuery(window).trigger("comboboxdataloaded", [data]);
				initControls();
			})
			.fail(function (error) {
				jQuery.messager.alert('Грешка', error.getMessage(), 'warning');
			});
	}
	initMapPad();
	initDefaultMapControls();
	zoomToBulgaria();
	initPositionDisplay();
	initScaleDisplay();
	initMapTools();
	//init layer for layer features selection
	initVectorLayer();
	initVectorLayer2();

});

function initMapPad(specific_map_type)
{
	var chosenMapType;

	//init all map features
	var options = {
		controls: [],
		projection: new OpenLayers.Projection("EPSG:900913")
	};

	//on init map type will not be specified
	if (specific_map_type == undefined)
	{
		chosenMapType = mapType;
		//init map
		map = new OpenLayers.Map('map', options);
	}
	//when map type is changed specific_map_type will have the value of map type
	else {
		chosenMapType = parseInt(specific_map_type);
	}

	var layerMapPad;

	switch (chosenMapType)
	{
		case 2:
			layerMapPad = new OpenLayers.Layer.Bing({
				key: bingApiKey,
				type: "Aerial",
				name: "MapPad"
			});
			break;
		case 3:
			layerMapPad = new OpenLayers.Layer.Bing({
				key: bingApiKey,
				type: "Road",
				name: "MapPad"
			});
			break;
		case 4:
			layerMapPad = new OpenLayers.Layer.Google(
					"MapPad",
					{type: google.maps.MapTypeId.TERRAIN}
			);
			break;
		case 5:
			layerMapPad = new OpenLayers.Layer.Google(
					"MapPad",
					{numZoomLevels: 20}
			);
			break;
		case 6:
			layerMapPad = new OpenLayers.Layer.Google(
					"MapPad",
					{type: google.maps.MapTypeId.HYBRID, numZoomLevels: 20}
			);
			break;
		case 7:
			layerMapPad = new OpenLayers.Layer.Google(
					"MapPad",
					{type: google.maps.MapTypeId.SATELLITE, numZoomLevels: 22}
			);
			break;
		case 8:
			layerMapPad = new OpenLayers.Layer.OSM();
			break;
        case 9:
            layerMapPad = new OpenLayers.Layer.WMS(
				'MapPad',
				imagesWMSServer + "?map=" + '/var/www/satellite_processor/maps/geo_scan.map',
                //wmsServer + "?map=" + mapPath + groupID + '.map',
				{
					layers: 'geo_scan',
				},
                {
                    numZoomLevels: 18
                });
			break;
		default: // default is bing aerial with labels
			layerMapPad = new OpenLayers.Layer.Bing({
				key: bingApiKey,
				type: "AerialWithLabels",
				name: "MapPad"
			});
			break;
	}

	//specific map type will be given only on reload
	//on first load(init) specific map type should be undefined
	if (specific_map_type == undefined)
	{
		map.addLayer(layerMapPad);
	}
	else {
		map.addLayer(layerMapPad);
		map.setLayerIndex(map.layers[map.layers.length - 1], 0);
		map.removeLayer(map.layers[1]);
		map.layers[0].redraw(true);
	}
}

function initDefaultMapControls()
{
	controlNavigation = new OpenLayers.Control.Navigation({
		name: "navigation"
	});
	map.addControl(controlNavigation);

	controlScaleLine = new OpenLayers.Control.ScaleLine({
		name: "scaleline",
		bottomInUnits: 'km'
	});
	map.addControl(controlScaleLine);

	controlLineMeasure = new OpenLayers.Control.DynamicMeasure(OpenLayers.Handler.Path, {
		name: 'linemeasure',
		persist: true,
		geodesic: true
	});
	map.addControl(controlLineMeasure);

	controlPolygonMeasure = new OpenLayers.Control.DynamicMeasure(OpenLayers.Handler.Polygon, {
		name: "polygonmeasure",
		persist: true,
		geodesic: true
	});
	map.addControl(controlPolygonMeasure);

	controlZoomIn = new OpenLayers.Control.ZoomBox({
		name: "zoomin",
		title: "Zoom in box",
		out: false
	})
	map.addControl(controlZoomIn);

	controlZoomOut = new OpenLayers.Control.ZoomBox({
		name: "zoomout",
		title: "Zoom out box",
		out: true
	});
	map.addControl(controlZoomOut);
}

function zoomToBulgaria()
{
	map.zoomToExtent(new OpenLayers.Bounds.fromString("125190.6162, 4573142.7188, 631370.3273, 4887149.5823").transform(
			new OpenLayers.Projection("EPSG:32635"),
			map.getProjectionObject()));
}

function initPositionDisplay()
{
	map.events.register("mousemove", map, function(e)
	{
		var position = map.getLonLatFromViewPortPx(e.xy);

		OpenLayers.Util.getElement("coords").innerHTML = 'x: ' + position.lat + ', y: ' + position.lon;
	});
}

function initScaleDisplay()
{
	map.events.register("zoomend", map, function(e)
	{
		var currentScale = Math.round(map.getScale());

		var scale = jQuery("#scale-denominator input").val(currentScale);
	});
}

var VECTOR_LAYER_STYLE = {
	cursor: "pointer",
	graphicName: 'square',
	pointRadius: 10,
	fillColor: '#cccccc',
	strokeColor: "#8863FF",
	strokeWidth: 5,
	fillOpacity: 0.25,
	fontColor:"#3600e6",
	labelYOffset: 13,
	label: '${label}'
};
var VECTOR_LAYER_STYLE2 = {
		cursor: "pointer",
		graphicName: 'square',
		pointRadius: 10,
		fillColor: '#cccccc',
		strokeColor: "#ff4444",
		strokeWidth: 3,
		fillOpacity: 0.25,
		fontColor:"#ff4444",
		label: '${label}'
	};

function displayFeatureSelection(oldgeom, ngeom)
{
    //remove all previous features
    vectors.removeAllFeatures();
    vectors2.removeAllFeatures();

    var in_options = {
        'internalProjection': map.baseLayer.projection,
        'externalProjection': new OpenLayers.Projection("EPSG:32635")
    };

    var features;
    for(var i=0; i<ngeom.length; i++) {
    	features = new OpenLayers.Format.WKT(in_options).read(ngeom[i][1]);
    	features.attributes.label = ngeom[i][0];

    	if (features) {
            if (features.constructor != Array) {
                features = [features];
            }

            vectors.addFeatures(features);
        }
    }

    var bounds;
    for(var i=0; i < oldgeom.length; i++) {
    	features = new OpenLayers.Format.WKT(in_options).read(oldgeom[i][1]);
    	features.attributes.label = oldgeom[i][0];
    	if (features) {
            if (features.constructor != Array) {
                features = [features];
            }

            for (var j = 0; j < features.length; ++j) {
                if (!bounds) {
                    bounds = features[j].geometry.getBounds();
                } else {
                    bounds.extend(features[j].geometry.getBounds());
                }
            }

            vectors2.addFeatures(features);
        }
    }

    var bounds = vectors2.getDataExtent();
    map.zoomToExtent(bounds);
}

function initVectorLayer()
{
	var renderer = OpenLayers.Util.getParameters(window.location.href).renderer;
	renderer = (renderer) ? [renderer] : OpenLayers.Layer.Vector.prototype.renderers;

	vectors = new OpenLayers.Layer.Vector("Vector Layer", {
		styleMap: new OpenLayers.StyleMap(new OpenLayers.Style(VECTOR_LAYER_STYLE, {
            isDefault: true,
			context: {
				label: function (feature) {
					if(!feature.attributes.hasOwnProperty('label')) {
						return '';
					}
					return feature.attributes.label;
				}
			}
		})),
		renderers: renderer
	});

	map.addLayer(vectors);
}

function initVectorLayer2()
{
	var renderer = OpenLayers.Util.getParameters(window.location.href).renderer;
	renderer = (renderer) ? [renderer] : OpenLayers.Layer.Vector.prototype.renderers;

	vectors2 = new OpenLayers.Layer.Vector("Vector Layer2", {
		styleMap: new OpenLayers.StyleMap(new OpenLayers.Style(VECTOR_LAYER_STYLE2, {
            isDefault: true,
			context: {
				label: function (feature) {
					if(!feature.attributes.hasOwnProperty('label')) {
						return '';
					}
					return feature.attributes.label;
				}
			}
		})),
		renderers: renderer
	});

	map.addLayer(vectors2);
}

function deactivateAllControls()
{
	for (var i = 0; i < map.options.controls.length; i++)
	{
		map.options.controls[i].deactivate();
	}
}

function unselectAll()
{
	jQuery('#tool-panzoom').linkbutton('unselect');
	jQuery('#tool-zoomin').linkbutton('unselect');
	jQuery('#tool-zoomout').linkbutton('unselect');
}

function initMapTools()
{
	//init map tools
	jQuery('#tool-set-scale').bind('click', function()
	{
		var scale = jQuery("#scale-denominator input").val();
		map.zoomToScale(scale);
	});

	jQuery('#tool-panzoom').bind('click', function()
	{
		_clippingZoom = true;
		var options = jQuery('#tool-panzoom').linkbutton('options');
		if (options.disabled)
		{
			return false;
		}

		//stop choose layer object event if active
		map.events.unregister('click', map, propertyWindowFunction);
		chooseControl('navigation');
		//unselect all buttons
		unselectAll();
		//select navigation button
		jQuery('#tool-panzoom').linkbutton('select');
	});

	jQuery('#tool-zoomin').bind('click', function()
	{
		_clippingZoom = true;
		unselectAll();
		map.events.unregister('click', map, propertyWindowFunction);

		chooseControl('zoomin');
		jQuery('#tool-zoomin').linkbutton('select');
	});

	jQuery('#tool-zoomout').bind('click', function()
	{
		_clippingZoom = true;
		unselectAll();
		map.events.unregister('click', map, propertyWindowFunction);

		chooseControl('zoomout');
		jQuery('#tool-zoomout').linkbutton('select');
	});

}
