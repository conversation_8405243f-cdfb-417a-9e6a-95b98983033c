var isSuperAdmin=false;

var GET = {};
location.search.substr(1).split("&").forEach(function(item) {GET[decodeURIComponent(item.split("=")[0])] = decodeURIComponent(item.split("=")[1])});
var file_id = GET['file_id'];
var file_name = GET['filename'];
var date_uploaded = GET['date_uploaded'];
jQuery(function(){
	initUpdateText();
    initEditActiveFrom();
    jQuery('#kvs-update-action-type-combobox > input').combobox({
    	url: 'index.php?common-rpc=kvs-update-action-type-combobox',
    	rpcParams:[true],
    	valueField: 'id',
    	textField: 'name',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
    });

    jQuery('#kvs-contracts-plots-for-update-table').treegrid({
        title:'Данни договори',
        iconCls:'icon-contract',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 20,
        fit: true,
        fitColumns: true,
        showFooter: true,
        url:'index.php?kvs-contracts-update-rpc=kvs-contracts-plots-for-update-grid',
        sortName: 'date_uploaded',
        rpcParams: [{
        	'filename': file_name,
        	'file_id': file_id,
        	'date_uploaded': date_uploaded
        }],
        sortOrder: 'desc',
        idField:'kad_ident',
        treeField:'kad_ident',
        singleSelect: true,
        columns:[[{
            field:'kad_ident',
            title:'<b>Номер на имот</b>',
            sortable:true,
            width:250,
            formatter:function(value, row, index){
                if(row.contract_data.active_status != undefined && row.contract_data.active_status == false){
                    return '<span style="color:#aaa">'+value+'</span>';
                }
                return value;
            }
        },{
            field:'c_num',
            title:'<b>Номер на договор</b>',
            sortable:true,
            width:250,
            formatter:function(value, row, index){
            	return '<a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="showContractInNewTab('+row.contract_id+')">'+row.c_num+'</a>';
            }
        },{
            field:'nm_usage_rights',
            title:'<b>Тип</b>',
            sortable:true,
            width:160,
            formatter:function(value, row, index){
                if(row.contract_data.active_status != undefined && row.contract_data.active_status == false){
                    return '<span style="color:#aaa">'+row.contract_data.nm_usage_rights+'</span>';
                }
                return row.contract_data.nm_usage_rights;
            }
        },{
            field:'due_date',
            title:'<b>Крайна дата на договора</b>',
            sortable:true,
            width:250,
            formatter:function(value, row, index){
                if(row.active_status != undefined && row.active_status == false){
                    return '<span style="color:#aaa">'+value+'</span>';
                }
                return value;
            }
        }]],
        pagination:true,
        rownumbers:true,
        toolbar: [{
                id: 'btnendupdate',
                text: 'Приключване на актуализация',
                iconCls: 'icon-payments',
                handler: function() {

                    TF.Rpc.Files.FilesGrid.endUpdate(file_id)
                    .done(function(data) {
                        jQuery.messager.alert('Внимание','Успешно приключване на актуализация!','info', function(){
                            window.location.assign('index.php?page=Files.Home');
                        });
                   })
                    .fail(function(errorObj){
                        var message = TF.Rpc.ExceptionsList.SYSTEM_ERROR.message;
                        if (errorObj.is(TF.Rpc.ExceptionsList.END_UPDATE_KVS_NOT_UPDATED_PLOTS)) {
                            message = TF.Rpc.ExceptionsList.END_UPDATE_KVS_NOT_UPDATED_PLOTS.message
                        };
                        jQuery.messager.confirm('Внимание', message, function(r){
                            if (r){
                               TF.Rpc.Files.FilesGrid.endUpdate(file_id, true)
                                .done(function(data) {
                                   jQuery.messager.alert('Внимание','Успешно приключване на актуализация!','info', function(){
                                        window.location.assign('index.php?page=Files.Home');
                                    });
                               });
                            }
                        });
                    });
                }
            }],
        onBeforeLoad: function(){
        	jQuery('#kvs-contracts-plots-for-update-table').treegrid('clearChecked');
        	initUpdateText(file_name, date_uploaded);
        },
        onSelect: function(node) {
			displayFeatureSelection(node.oldgeom, node.ngeom);
            addRemoveComboboxActionTypes(node);
		},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

});

function addRemoveComboboxActionTypes(node) {
    var comboboxItem = jQuery('.combobox-item').filter(':contains("Промяна на граници")');

    if(node.ngeom.length != node.oldgeom.length) {
        comboboxItem.addClass("combobox-item-disabled");
        jQuery('#kvs-update-action-type-combobox > input').combobox('select', 1);
    } else {
        comboboxItem.removeClass("combobox-item-disabled");
    }
}
function initUpdateText(file_name, date_uploaded) {

    var text = "Списък с имоти, които не фигурират във файл " + file_name + " от " + date_uploaded + ", но за които към тази дата има активни договори за наем или аренда. " +
    		"Ако имотите от списъка са били обект на делба или обединяване, можете да отразите това чрез инструментите в картата. " +
    		"Ако на картата не виждате новите имоти, които да се припокриват със стария имот, това може да се дължи на невалидна геометрия на " +
    		"новите имоти или грешка във файла от ОСЗ. Ако не отразите промяната в имотите е възможно някои от справките за имоти да не работят " +
    		"коректно. След отразяването на промените може да актуализирате и договора за съответния имот, като използвате линка от колона " +
    		"Номер на договор.";

    jQuery('#kvs-contracts-plots-for-update-text').html(text);
}

function initEditActiveFrom()
{
    jQuery('#kvs-update-action-edit-active-from  > input').datebox();
}



function showContractInNewTab(contract_id) {
	window.open("index.php?page=Contracts.Home&contract_id=" + contract_id, '_blank');
}

function saveKVSPlotsUpdate() {
	var action = jQuery("#kvs-update-action-type-combobox > input").combobox("getValue");
	var selected_row =  jQuery('#kvs-contracts-plots-for-update-table').treegrid("getSelected");

    if(!selected_row) {
        jQuery.messager.alert('Грешка', 'Моля изберете имот.', 'error');
        return;
    }    

    const editActiveFrom = jQuery('#kvs-update-action-edit-active-from  > input').datebox('getValue');
    if (! editActiveFrom ) {
        jQuery.messager.alert('Грешка', 'Моля изберете дата за валидност на старите имоти.', 'error');
        return;
    }    

	TF.Rpc.KVSContractsUpdate.KVSContractsPlotsForUpdateGrid.update(action, selected_row.ngeom, selected_row.ekate, selected_row.new_kad_idents, selected_row.old_kadidents,selected_row.oldgeom, date_uploaded, file_id)
	.done(function(data) {

        vectors.removeAllFeatures();
        vectors2.removeAllFeatures();

		jQuery('#kvs-contracts-plots-for-update-table').treegrid('reload');
	});
}

