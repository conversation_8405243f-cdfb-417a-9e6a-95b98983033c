var _reportId;

function initDividendsTree(pageNumber, filterObj) {
	_reportId = undefined;
	var page_number = pageNumber || 1;
	var annualReportTree = jQuery('#annual-report-tree');
	var isTreeBound = annualReportTree.data().hasOwnProperty('tree');

	if (isTreeBound) {
		annualReportTree.tree({
			rpcParams: [filterObj],
			page: page_number
		});
		return;
	}
	var params = {};

	if (filterObj) {
		params.start_date = filterObj.start_date;
		params.end_date = filterObj.end_date;
	}

	initDividendsGrid('0');
	annualReportTree.tree({
		url: 'index.php?dividends-rpc=annual-report-tree',
		animate: true,
		lines: true,
		sort: 'id',
		order: 'desc',
		page: page_number,
		rpcParams: [params],
		onBeforeLoad: function(node, param) {
			initReportInfo(0);
			initDividendsGrid(0);		
			_reportId = 0;	
		},
		onSelect: function(node) {
			initReportInfo(node.attributes);
			initDividendsGrid(node.id);		

			_reportId = node.id;	
		},
		onLoadSuccess: function() {
			var roots = annualReportTree.tree('getRoots');
			var total = 0;
			var limit = 10;

			if (roots.length) {
				annualReportTree.tree('select', roots[0].target);
				total = roots[0]['attributes']['pagination']['total'];
				limit = roots[0]['attributes']['pagination']['limit'];
			}

			//init pagination with total elements
			initCooperatorsPagination(total, limit);

			endLoading();
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function initCooperatorsPagination(total, limit) {
	jQuery('#annual-report-tree-pagination').pagination({
		showPageList: false,
		showRefresh: false,
		displayMsg: '',
		total: total,
		pageSize: limit,
		onSelectPage: function(pageNumber, pageSize) {
			var obj = new Object();
			initDividendsTree(pageNumber, obj);
		}
	});
}

var date = new Date();
var _todayDate = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();

jQuery(function() {
	
	jQuery('#start-date').datebox({
		requred: true,
		missingMessage: 'Моля въведете дата.',
		value: _todayDate
	});

	jQuery('#end-date').datebox({
		requred: true,
		missingMessage: 'Моля въведете дата.',
		value: _todayDate
	});

	jQuery('#search-start-date-reports').datebox({
		requred: false
	});

	jQuery('#search-due-date-reports').datebox({
		requred: false
	});

	initRequiredFieldsAnnualReport();


	//Open Add Annual Report
	jQuery('#add-annual-report').bind('click', openAddAnnualReport);

	//Close Add/Update Annual Report
	jQuery('#btn-close-annual-report').bind('click', closeAnnualReport);

	//Create Annual Report
	jQuery('#btn-add-annual-report').bind('click', createAnnualReport);

	//Load Annual Report
	jQuery('#load-annual-report').bind('click', loadAnnualReport);	

	//Update Annual Report
	jQuery('#btn-update-annual-report').bind('click', updateAnnualReport);

	//Delete Annual Report
	jQuery('#delete-annual-report').bind('click', deleteAnnualReport);


	//Open Filter Reports
	jQuery('#filter-annual-report').bind('click', openFilterReports);	

	//Execute Filter Reports
	jQuery('#btn-search-filter-reports').bind('click', executeFilterReports);	

	//Close Filter Reports
	jQuery('#btn-cancel-filter-reports').bind('click', closeFilterReports);	

	//Reset Filter Reports
	jQuery('#cancel-filter-annual-report').bind('click', resetFilterReports);	
});	

function executeFilterReports() {
	var obj = new Object();
	obj.start_date= jQuery('#search-start-date-reports').datebox('getValue');
	obj.end_date = jQuery('#search-due-date-reports').datebox('getValue');

	initDividendsTree(1, obj);
	closeFilterReports();
}

function resetFilterReports() {
	jQuery('#search-start-date-reports').datebox('setValue', '');
	jQuery('#search-due-date-reports').datebox('setValue', '');

	//initDividendsTree('0');
	jQuery('#annual-report-tree-pagination').pagination('select', 1);
}

function closeFilterReports() {

	jQuery('#win-annual-report-filter').window('close');
}

function openFilterReports() {
	jQuery('#win-annual-report-filter').window('open');
}

function openAddAnnualReport() {

	resetFormFieldsValues();
	initRequiredFieldsAnnualReport();
	jQuery('#win-annual-report-add').window({title:'Добавяне на годишен отчет'}); 
	jQuery('#win-annual-report-add').window('open');

	jQuery('#btn-add-annual-report').show();
	jQuery('#btn-update-annual-report').hide();
}

function closeAnnualReport() {
	jQuery('#win-annual-report-add').window('close');
}

function loadAnnualReport() {
	
	var getSelected = jQuery('#annual-report-tree').tree('getSelected');
	if (getSelected) {
		
		//php function params
		var params = getSelected.id;

		TF.Rpc.Dividends.AnnualReportTree.load(params)
        .done(function (data)
        {
			//set window title
			jQuery('#win-annual-report-add').window({title:'Редактиране на годишен отчет'}); 

			//set values to the form fields
			setFormFieldsValues(data);

			initRequiredFieldsAnnualReport();

			jQuery('#win-annual-report-add').window('open');

			jQuery('#btn-update-annual-report').show();
			jQuery('#btn-add-annual-report').hide();

		});    

	} else {
		jQuery.messager.alert('Грешка', 'Моля изберете кооператор.');
	}
}

function resetFormFieldsValues()
{
	jQuery('#start-date').datebox({
		requred: true,
		missingMessage: 'Моля въведете дата.',
		value: _todayDate
	});
	jQuery('#end-date').datebox({
		requred: true,
		missingMessage: 'Моля въведете дата.',
		value: _todayDate
	});
	jQuery('#win-amount').numberbox('setValue', '');	
	jQuery('#tax-amount').numberbox('setValue', '');	
	jQuery('#distribution-amount').numberbox('setValue', '');	
	jQuery('#increase-capital-amount').numberbox('setValue', '');	
}

function setFormFieldsValues(data)
{
	jQuery('#start-date').datebox({
		value: data.start_date
	});
	jQuery('#end-date').datebox({
		value: data.end_date
	});
	jQuery('#win-amount').numberbox('setValue', data.win_amount);	
	jQuery('#tax-amount').numberbox('setValue', data.tax_amount);	
	jQuery('#distribution-amount').numberbox('setValue', data.distribution_amount);	
	jQuery('#increase-capital-amount').numberbox('setValue', data.increase_capital_amount);	
}

function updateAnnualReport() {

	var getSelected = jQuery('#annual-report-tree').tree('getSelected');
	if (getSelected) {

		if(!validateSubmitInfo())
		{
			return;
		}

		//form values
		var params = getFormValuesAnnualReport();
		params.id = getSelected.id;

		TF.Rpc.Dividends.AnnualReportTree.update(params)
        .done(function (data)
		{
        	jQuery('#annual-report-tree').tree('reload');
			jQuery('#win-annual-report-add').window('close');
		}).fail(function (data){
			jQuery.messager.alert('Грешка', 'Системна грешка при редактиране на годишен отчет!', 'warning');
			return;	
		});    
		
	} else {
		jQuery.messager.alert('Грешка', 'Моля изберете отчет.');
	}
}	

function deleteAnnualReport() {
	
	var getSelected = jQuery('#annual-report-tree').tree('getSelected');
	if (getSelected) {
		jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да изтриете този отчет?', function(r) {
			if (r) {
				_reportId = undefined;
				
				//php function params
				var params = getSelected.id;

				TF.Rpc.Dividends.AnnualReportTree.deleteLeaf(params)
		        .done(function (data)
				{});
				
				jQuery('#annual-report-tree').tree('reload');
			}
		});
	} else {
		jQuery.messager.alert('Грешка', 'Моля изберете кооператор.');
	}
}

function getFormValuesAnnualReport() {

	var params = {
		start_date: jQuery('#start-date').datebox('getValue'),
		end_date: jQuery('#end-date').datebox('getValue'),
		win_amount: jQuery('#win-amount').val(), 
		tax_amount: jQuery('#tax-amount').val(), 
		distribution_amount: jQuery('#distribution-amount').val(), 
		increase_capital_amount: jQuery('#increase-capital-amount').val(), 
	};

	return params;
}

function createAnnualReport() {

	if(!validateSubmitInfo())
	{
		return;
	}

	//form values
	var params = getFormValuesAnnualReport();

		TF.Rpc.Dividends.AnnualReportTree.create(params)
        .done(function (data)
		{
			jQuery('#annual-report-tree').tree('reload');
			jQuery('#win-annual-report-add').window('close');
		}).fail(function (data){
			jQuery.messager.alert('Грешка', 'Системна грешка при добавяне на годишен отчет!', 'warning');
			return;	
		});    
}

function validateSubmitInfo() {
	var winAmount = jQuery('#win-amount');
	var distributionAmount = jQuery('#distribution-amount');
	var increaseAmount = jQuery('#increase-capital-amount');
	var winAmountValue = winAmount.numberbox('getValue');
	var distributionAmountValue = distributionAmount.numberbox('getValue');
	var increaseAmountValue = increaseAmount.numberbox('getValue');

	var totalCheckValue = (parseFloat(distributionAmountValue) + parseFloat(increaseAmountValue));

	if (!compareDates('#start-date', '#end-date', 2)) {
		jQuery.messager.alert('Грешка', 'Въведена е грешна дата - крайната дата е по-ранна от началната дата на отчета.', 'warning');
		return false;
	}

	if(parseFloat(winAmountValue) < totalCheckValue){
		jQuery.messager.alert('Грешка', '"Сума за разпределяне" + "Сума за увеличение на дялов капитал" не може да е по-голяма от "Сума на печалбата"');
		return false;
	}
	if (winAmount.numberbox('isValid') &&
		jQuery('#tax-amount').numberbox('isValid') &&
		distributionAmount.numberbox('isValid') &&	
		increaseAmount.numberbox('isValid')) {

		return true;
	} else {
		jQuery.messager.alert('Грешка', 'Моля попълнете задължителните полета.');
		return false;
	}
}

function initRequiredFieldsAnnualReport() {
    numLengthbox();
    var taxAmount = jQuery('#tax-amount');

    jQuery('#win-amount').numberbox({
        required: true,
        precision: 2,
        min: 0.00,
        missingMessage: 'Моля въведете Сума на печалбата.'
    });
    taxAmount.numberbox({
        required: false,
        precision: 2,
        min: 0.00,
        disabled: true
    });
    jQuery('#distribution-amount').numberbox({
        required: true,
        precision: 2,
        min: 0.00,
        missingMessage: 'Моля въведете Сума за разпределяне.',
		onChange: function (newValue, oldValue) {
			taxAmount.numberbox('setValue', newValue * 0.05);
		}
    });
    jQuery('#increase-capital-amount').numberbox({
        required: true,
        precision: 2,
        min: 0.00,
        missingMessage: 'Моля въведете Сума за увеличение на дялов капитал.'
    });
    
}