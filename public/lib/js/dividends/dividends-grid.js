var documentEditIndex = undefined;
var _fileName = "";

function initDividendsGrid(report_id) {

    var winDownload = jQuery('#win-download').window({
        onClose: onDowbloadWindowClose
    });

    var downloadFile = jQuery('#btn-download-file');
    var cancelDownloadFile = jQuery('#btn-download-file-close');
    var getSelectedReport = jQuery('#annual-report-tree').tree('getSelected');

    jQuery('#dividends-table').treegrid({
        title: 'Член-кооператори',
        iconCls: 'icon-rents',
        nowrap: false,
        autoRowHeight: true,
        striped: true,
        pageSize: 30,
        fit: true,
        fitColumns: true,
        singleSelect:false,
        showFooter: true,
        url: 'index.php?dividends-rpc=dividends-grid',
        idField: 'uid',
        sortName: 'c.name',
        border: false,
        sortOrder: 'asc',
        rpcParams: [report_id],
        pagination: true,
        rownumbers: true,
        selectOnCheck: true,
        checkOnSelect: true,
        treeField:'cooperator_names',
        columns:[[
            {
                  field: 'cooperator_names',
                  title: '<b>Три имена</b>',
                  width: 100,
                  align: 'left'
            },{
                  field: 'dividend',
                  title: '<b>Разпределен</br>дивидент</b>',
                  width: 80,
                  align: 'center'
            },{
                  field: 'tax',
                  title: '<b>Данък</b>',
                  width: 60,
                  align: 'center'
            },{
                  field: 'due_dividend',
                  title: '<b>Дължим дивидент</b>',
                  width: 80,
                  align: 'center'
            },{
                  field: 'cashout',
                  title: '<b>Изплатен дивидент</b>',
                  width: 80,
                  align: 'center',
            },{
                  field: 'over_paid',
                  title: '<b>Надплатен дивидент</b>',
                  width: 80,
                  align: 'center',
                    formatter: function(value,row,index){
                        if(value){
                            return parseFloat(value).toFixed(3);   
                        }
                    }
            },{
                  field: 'pay_date',
                  title: '<b>Дата на последно</br>изплащане</b>',
                  width: 80,
                  align: 'center'
            },{
                  field: 'increase_capital',
                  title: '<b>Увеличение на дяловия</br>капитал</b>',
                  width: 80,
                  align: 'center'
            }
        ]],
        toolbar: [
            {
                id: 'btn-pay-dividend',
                text: 'Изплащане на дивидент',
                iconCls: 'icon-payments',
                handler: function() {
                    var getChecked = jQuery('#dividends-table').treegrid('getChecked');

                    if (getChecked.length > 0) {
                        var dividend = 0;
                        var egn = '';
                        var name = '';
                        
                        jQuery.each(getChecked, function(key, value) {
                          if(jQuery.isNumeric(value.due_dividend)) {
                                dividend += parseFloat(value.due_dividend);
                          }
                        });
                        dividend = dividend.toFixed(2);

                        if(getChecked.length == 1){
                            name = getChecked[0].cooperator_names;
                            egn = getChecked[0].egn;
                        }

                        //Open Изплащане
                        openAddDividend(dividend, name, egn);
                    } else
                    {
                        jQuery.messager.alert('Грешка', 'Моля изберете кооператор.');
                    }
                }
            }, {
                id: 'btn-reversal-dividend',
                text: 'Сторниране',
                iconCls: 'icon-payments',
                handler: function() {
                    var getChecked = jQuery('#dividends-table').treegrid('getChecked');
                    
                    if (getChecked.length > 0) {
                        var dividend = 0;
                        var paidDividend = 0;
                        var egn = '';
                        var name = '';

                        jQuery.each(getChecked, function(key, value) {
                            if (jQuery.isNumeric(value.due_dividend)) {
                                dividend += parseFloat(value.due_dividend);
                            }
                            if (jQuery.isNumeric(value.cashout)) {
                                paidDividend += parseFloat(value.cashout);
                            }
                        });
                        dividend = dividend.toFixed(2);
                        paidDividend = paidDividend.toFixed(2);

                        if(getChecked.length == 1){
                            name = getChecked[0].cooperator_names;
                            egn = getChecked[0].egn;
                        }

                        //Open Сторниране
                        openAddDividendReversal(dividend, paidDividend, name, egn);                       
                    } else
                    {
                        jQuery.messager.alert('Грешка', 'Моля изберете кооператор.');
                    }
                }
            }, {
                id: 'btnexportpdfreport',
                text: 'Отпечатай pdf',
                iconCls: 'icon-pdf',
                handler: function() {
                    if (getSelectedReport) {
                        var reportName = getSelectedReport.text;

                        TF.Rpc.Dividends.DividendsGrid.expDividendReport(getSelectedReport.id, 'pdf', reportName)
                        .done(function(data) {
                            winDownload.window('open');
                            var path = data.path;
                            _fileName = data.file_name;
                            downloadFile.attr("href", path); 
                        });
                    } else {
                        jQuery.messager.alert('Грешка', 'Моля изберете годишен отчет, върху който да разпечатате!');
                    }
                }
            },{
                id: 'btnexportdocreport',
                text: 'Отпечатай Word',
                iconCls: 'icon-word',
                handler:function() {
                    if (getSelectedReport) {
                        var reportName = getSelectedReport.text;

                        TF.Rpc.Dividends.DividendsGrid.expDividendReport(getSelectedReport.id, 'doc', reportName)
                        .done(function(data) {
                            winDownload.window('open');
                            var path = data.path;
                            _fileName = data.file_name;
                            downloadFile.attr("href", path); 
                        });
                    } else {
                        jQuery.messager.alert('Грешка', 'Моля изберете годишен отчет, върху който да разпечатате!');
                    }
                }
            }
        ],
        onSelect: function(node) {
            if(node.is_dead) {
                jQuery('#dividends-table').treegrid('unselect', node.id);
            }
        }
    });

    //custom pager
    var pager = jQuery('#dividends-table').treegrid('getPager');
    pager.pagination({
      beforePageText: 'Стр.',
      displayMsg: 'От {from} до {to} от {total}'
    });
}

function onDowbloadWindowClose() {
    return;
}

function closeWinDownload() {
    setTimeout(function(){
        jQuery('#win-download').window('close');
    },2000);
}    

function openAddDividendReversal(dividend, paidDividend, name, egn) {

    initRequiredFieldsDividendReversal(); 

    jQuery('#owe-dividend-reversal').html(dividend);
    jQuery('#paid-dividend-reversal').html(paidDividend);
    jQuery('#dividend-reversal-representative').val(name);
    jQuery('#dividend-reversal-representative-egn').val(egn);

    jQuery('#win-cooperator-add-dividend-reversal').window('open');
}

function openAddDividend(dividend, name, egn) {

    initRequiredFieldsDividend(); 

    jQuery('#win-add-cooperator-dividend').window('setTitle', 'Изплащане на дивидент');
    jQuery('#btn-add-cooperator-dividend > a').linkbutton({text:'Изплати'});

    jQuery('#owe-dividend').html(dividend);
    jQuery('#dividend-representative').val(name);
    jQuery('#dividend-representative-egn').val(egn);

    jQuery('#win-cooperator-add-dividend').window('open');
}

jQuery(function(){  

    jQuery('#dividend-payment-method-cash').change(function() {
        if (jQuery('#dividend-payment-method-cash').is(':checked') == true)
        {
            jQuery('#bank-account-row').hide();
        }
    });

    jQuery('#dividend-payment-method-bank').change(function() {
        if (jQuery('#dividend-payment-method-bank').is(':checked') == true)
        {
            jQuery('#bank-account-row').show();
        }
    });

    jQuery('#dividend-pay-date').datebox({
        requred: true,
        missingMessage: 'Моля въведете дата.',
        value: _todayDate
    });

    jQuery('#dividend-pay-date-reversal').datebox({
        requred: true,
        missingMessage: 'Моля въведете дата.',
        value: _todayDate
    });

    initRequiredFieldsDividend();

    //Create dividend
    jQuery('#btn-add-dividend').bind('click', createDividend);

    //Close dividend
    jQuery('#btn-close-dividend').bind('click', closeDividend);

    //Create dividend Reversal
    jQuery('#btn-add-dividend-reversal').bind('click', createDividendReversal);

    //Close dividend Reversal
    jQuery('#btn-close-dividend-reversal').bind('click', closeDividendReversal);

});    

function closeDividend() {

    jQuery('#win-cooperator-add-dividend').window('close');
}

function closeDividendReversal() {

    jQuery('#win-cooperator-add-dividend-reversal').window('close');
}      

function createDividend() {

    if(!validateSubmitInfoCreateDividend())
    {
        return;
    }

    //form values
    var params = getFormValuesDividend();
    var report = jQuery('#annual-report-tree').tree("getSelected");
    var cooperators = jQuery('#dividends-table').treegrid("getChecked");
    var cooperatorsData = [];

    jQuery.each(cooperators, function(key, value) {
      cooperatorsData.push({
        id: value.id,
        path: value.path,
        is_heritor: value.is_heritor
      });
    });

    params.report_id = report.id;
    params.cooperators_data = cooperatorsData;

    TF.Rpc.Dividends.DividendsGrid.createDividend(params)
    .done();
    jQuery('#dividends-table').treegrid('reload');     

    var cashout = params.cashout;
    var payDate = params.pay_date;
    var iban = params.iban;
    var generateOrder = params.generate_order;
    var generatePayment = params.generate_payment;

    var representativeArr = {
        representative: params.representative,
        representative_egn: params.representative_egn,
        representative_proxy: params.representative_proxy
    };

    var winDownload = jQuery('#win-download');
    var downloadFile = jQuery('#btn-download-file');
    var cancelDownloadFile = jQuery('#btn-download-file-close');

    if(generateOrder){
        TF.Rpc.Dividends.ExportDividendPaymentsBanks.expDividendOrder(cooperatorsData, payDate, cashout, representativeArr)
        .done(function(data) {
            winDownload.window('open');
            var path = data.pdf_blank_file;
            downloadFile.attr("href", path); 
        });
    }  
    if(generatePayment){
       TF.Rpc.Dividends.ExportDividendPaymentsBanks.expDividendPayment(cooperatorsData, cashout, iban)
        .done(function(data) {
            winDownload.window('open');
            var path = data.pdf_blank_file;
            downloadFile.attr("href", path); 
        });
    } 

    jQuery('#win-cooperator-add-dividend').window('close');
}

function getFormValuesDividend() {

    var params = {
        cashout: jQuery('#cashout-dividend').val(),
        iban: jQuery('#dividend-iban').val(),
        representative: jQuery('#dividend-representative').val(),
        representative_egn: jQuery('#dividend-representative-egn').val(),
        representative_proxy: jQuery('#dividend-representative-proxy').val(),
        pay_date: jQuery('#dividend-pay-date').datebox('getValue'),
    };

    if(jQuery('#dividend-generate-order').is(":checked")) { 
        params.generate_order = true; 
    }else{
        params.generate_order = false; 
    }
    if(jQuery('#dividend-generate-payment').is(":checked")) { 
        params.generate_payment = true; 
    }else{
        params.generate_payment = false; 
    }

    return params;
}

function validateSubmitInfoCreateDividend() {

    //&& jQuery('#dividend-representative').validatebox('isValid')
    if (jQuery('#cashout-dividend').numberbox('isValid')) {

        if(jQuery('#cashout-dividend').numberbox('getValue') > 0.00)
        {
            return true;    
        }else{
            jQuery.messager.alert('Грешка', 'Сума за изплащане трябва де е по-голяма от 0.00 лв.');
        return false;    
        }    

        
    } else {
        jQuery.messager.alert('Грешка', 'Моля попълнете задължителните полета.');
        return false;
    }
}

function validateSubmitInfoCreateDividendReversal() {

    if (jQuery('#cashout-dividend-reversal').numberbox('isValid')) {
        return true;
    } else {
        jQuery.messager.alert('Грешка', 'Моля попълнете задължителните полета.');
        return false;
    }
}

function initRequiredFieldsDividend() {
    numLengthbox();
    
    jQuery('#cashout-dividend').numberbox({
        required: true,
        precision: 2,
        min: 0.00,
        missingMessage: 'Моля въведете Сума за изплащане.'
    });
    // jQuery('#dividend-representative').validatebox({
    //     required: true,
    //     missingMessage: 'Моля въведете Представител.'
    // });
}

function createDividendReversal() {

    if(!validateSubmitInfoCreateDividendReversal())
    {
        return;
    }

    //form values
    var params = getFormValuesDividendReversal();
    var report = jQuery('#annual-report-tree').tree("getSelected");
    var cooperators = jQuery('#dividends-table').treegrid("getChecked");
    var cooperatorsData = [];

    jQuery.each(cooperators, function(key, value) {
      cooperatorsData.push({
        id: value.id,
        path: value.path,
        is_heritor: value.is_heritor
      });
    });

    params.report_id = report.id;
    params.cooperators_data = cooperatorsData;

    TF.Rpc.Dividends.DividendsGrid.createDividendReversal(params)
    .done();
    jQuery('#dividends-table').treegrid('reload');      

    jQuery('#win-cooperator-add-dividend-reversal').window('close');
}

function getFormValuesDividendReversal() {

    var params = {
        cashout: jQuery('#cashout-dividend-reversal').val(),
        representative: jQuery('#dividend-reversal-representative').val(),
        representative_egn: jQuery('#dividend-reversal-representative-egn').val(),
        representative_proxy: jQuery('#dividend-reversal-representative-proxy').val(),
        pay_date: jQuery('#dividend-pay-date-reversal').datebox('getValue'),
    };

    return params;
}

function initRequiredFieldsDividendReversal() {
    numLengthbox();
    
    jQuery('#cashout-dividend-reversal').numberbox({
        required: true,
        precision: 2,
        min: 0.00,
        missingMessage: 'Моля въведете Сума за изплащане.'
    });
}