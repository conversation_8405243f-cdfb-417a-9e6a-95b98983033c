jQuery.extend(jQuery.fn.datagrid.defaults, {
	loader: function (params, succ, error) {
		var options = jQuery(this).datagrid('options');
		var rpcQuery = options.rpcParams || [];	

		var paging = [
			params.page,
			params.rows,
			params.sort,
			params.order
		] 
		var rpcAllParams = rpcQuery.concat(paging);

		var params = JSON.stringify({
			"method": "read", 
			"params": rpcAllParams, 
			"id": 1,
			"jsonrpc": "2.0"
		});
		var url = options.url;
		jQuery.ajax({
			url: url,
			data: params,
			accepts: 'application/json',
			contentType: 'application/json',
			method: 'post',
			dataType: 'json',
			processData: false
		})
		.done(succ);
	},
	loadFilter: function (data) {
		if(typeof data.result === 'undefined'){
			return data || [];			
		}else{
			return data.result || [];			
		}
	}
});

jQuery.extend(jQuery.fn.datagrid.methods, {
	loadRpc: function(element, params) {
		jQuery(element).datagrid({
			rpcParams: params
		});
	},
});

jQuery.extend(jQuery.fn.treegrid.defaults, {
	loader: function (params, succ, error) {
		var options = jQuery(this).treegrid('options');
		var rpcQuery = options.rpcParams || [];	

		var paging = [
			params.page,
			params.rows,
			params.sort,
			params.order
		] 
		var rpcAllParams = rpcQuery.concat(paging);

		var params = JSON.stringify({
			"method": "read", 
			"params": rpcAllParams, 
			"id": 1,
			"jsonrpc": "2.0"
		});
		var url = options.url;
		jQuery.ajax({
			url: url,
			data: params,
			accepts: 'application/json',
			contentType: 'application/json',
			method: 'post',
			dataType: 'json',
			processData: false
		})
		.done(succ);
	},
	loadFilter: function (data) {
		if(typeof data.result === 'undefined'){
			return data || [];			
		}else{
			return data.result || [];			
		}
	}
});

jQuery.extend(jQuery.fn.treegrid.methods, {
	loadRpc: function(element, params) {
		jQuery(element).treegrid({
			rpcParams: params
		});
	},
});

jQuery.extend(jQuery.fn.combobox.defaults, {
	loader: function (params, succ, error) {
		var options = jQuery(this).combobox('options');
		var rpcQuery = options.rpcParams || [];	

		var params = JSON.stringify({
			"method": "read", 
			"params": rpcQuery, 
			"id": 1,
			"jsonrpc": "2.0"
		});
		var url = options.url;
		jQuery.ajax({
			url: url,
			data: params,
			accepts: 'application/json',
			contentType: 'application/json',
			method: 'post',
			dataType: 'json',
			processData: false
		})
		.done(succ);
	},
	loadFilter: function (data) {
		if(typeof data.result === 'undefined'){
			return data || [];			
		}else{
			return data.result || [];			
		}
	}
});

jQuery.extend(jQuery.fn.tree.defaults, {
	loader: function (filterParams, succ, error) {
		var options = jQuery(this).tree('options');
		var rpcParams = options.rpcParams || [];

		// rpcParams.push(filterParams);
		rpcParams.push(options.page);
		rpcParams.push(options.rows);
		rpcParams.push(options.sort);	
		rpcParams.push(options.order);

		var params = JSON.stringify({
			"method": "read", 
			"params": rpcParams, 
			"id": 1,
			"jsonrpc": "2.0"
		});
		var url = options.url;
		jQuery.ajax({
			url: url,
			data: params,
			accepts: 'application/json',
			contentType: 'application/json',
			method: 'post',
			dataType: 'json',
			processData: false
		})
		.done(succ);
	},
	loadFilter: function (data) {
		return data.result || [];
	}
});

jQuery(function(){
    setUserLastLogin();

	//retrieve GET parameters
	var GET = {};
	location.search.substr(1).split("&").forEach(function(item) {GET[decodeURIComponent(item.split("=")[0])] = decodeURIComponent(item.split("=")[1])});

	//change URL without refresh if it's posible
	if (history && history.replaceState){
		history.replaceState(null, null, 'index.php?page=' + GET.page);
	}

	initDividendsTree(1, GET);

});

function initReportInfo(report){
    if(!report)
    {
        jQuery('#info-report-period').html('');
        jQuery('#info-win-amount').html('');
        jQuery('#info-distribution-amount').html('');
        jQuery('#info-tax-amount').html('');
        jQuery('#info-increase-capital-amount').html('');
        
        return false;
    }

    jQuery('#info-report-period').html(report.dates);
    jQuery('#info-win-amount').html(report.win_amount);
    jQuery('#info-distribution-amount').html(report.distribution_amount);
    jQuery('#info-tax-amount').html(report.tax_amount);
    jQuery('#info-increase-capital-amount').html(report.increase_capital_amount);
}
