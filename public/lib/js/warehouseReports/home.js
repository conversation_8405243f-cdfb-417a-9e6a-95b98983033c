define([
    "easyui",
    "js/main/EasyUIRPCLoaders",
    "js/main/rpc_errors_handler",
    "js/warehouseReports/reports",
    "js/TF/Rpc/Warehouse/Reports",
    "js/warehouseReports/reports_configs",
    "js/warehouse/companies-grid",
    "js/warehouse/utils",
    "js/warehouse/comboboxes",
    "js/warehouse/constants",
    "js/warehouse/dynamic-warehouse-fields"
], function(
    _,
    EasyUIRPCLoaders,
    RpcErrorHandler,
    Reports,
    ReportsApi,
    ReportsConfigs,
    CompaniesGrid,
    Utils,
    Comboboxes,
    CONSTANTS,
    DynamicFields
) {
    const EXPORT_FORMAT_PDF = 'pdf';
    const EXPORT_FORMAT_XLS = 'xls';

    var PAGE_ALL = false;
    var PAGE_SELECTED = true;
    var selected_report = null;
    reportTree = jQuery('#reports-tree');

    // TOOLBAR BUTTONS
    var print_all_pages = jQuery('#print-report');
    var print_single_page = jQuery('#print-report-page');
    var export_pdf_btn = jQuery('#export-pdf-report');
    var export_full_excel_btn = jQuery('#export-xls-report');
    var export_excel_btn = jQuery('#export-xls-report-page');
    var filter_menu_btn = jQuery('#filter-report');

    function init() {
        initFilterComponents();
        setReportTree();
        bindEventsListeners();
    }

    function bindEventsListeners() {
        jQuery("#btn-manage-contragents").on("click", openContragentsWindow);

        jQuery('#btn-report-filter').on('click', search);

        jQuery('#clear-filter-report').on('click', function () {
            if (filter_menu_btn.hasClass('l-btn-disabled')) {
                return;
            }

            filter_clear_and_reload();
            search();
        });

        filter_menu_btn.bind('click', function () {
            if (filter_menu_btn.hasClass('l-btn-disabled')) {
                return;
            }

            jQuery('#win-filter-report').window('open');
        });

        print_all_pages.bind('click', function () {
            printReport(PAGE_ALL);
        });

        print_single_page.bind('click', function () {
            printReport(PAGE_SELECTED);
        });

        export_excel_btn.bind('click', function () {
            return exportReport(EXPORT_FORMAT_XLS);
        });

        export_full_excel_btn.bind('click', function () {
            return exportReport(EXPORT_FORMAT_XLS, false);
        });

        export_pdf_btn.bind('click', function () {
            return exportReport(EXPORT_FORMAT_PDF);
        });

        jQuery(".closeBtn").on("click", Utils.closePanels);
    }

    function openContragentsWindow() {
        CompaniesGrid.initContragentsGrid({types: [CONSTANTS.COMPANY_TYPE_CONTRAGENT]});
        jQuery("#win-manage-contragents").window("open");

        return false;
    }

    function setReportTree() {
        reportTree.tree({
            animate: true,
            lines: true,
            sort: 'id',
            data: ReportsConfigs.getInitData(),
            onSelect: function (node) {
                if (!node || !node.module) return;
                filter_clear_and_reload();
                selected_report = node;
                report_title = 'Справка: ' + node.text;
                setReportTitle(report_title);
                
                toggleGrids(node.layout);
                initReportFilters(node.filters);
                jQuery(node.layout).find('.datagrid-f').datagrid('resize', {width: '100%', height: '100%'});
                jQuery('#win-filter-report').window().window('resize', node.dim).window('center').window('close');

                return loadReports(node);
            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });

        var firstNode = reportTree.tree('find', 1);
        if (firstNode) reportTree.tree('select', firstNode.target);

        function loadReports(node) {
            report_type = node.type;
            report_filter = node.name;
            Reports.addLegendButton(node.legendMsg);
            Reports.initReportsDataGrid(node.rpcParams, node.columns, node.onLoadSuccessCallback);
        }

        function toggleGrids(name) {
            jQuery('#main_layout').children().hide();
            jQuery(name).show();
        }

        function initReportFilters(filters) {
            jQuery('#win-filter-report fieldset').hide();
            for (var fileset of filters){
                jQuery('#win-filter-report #' +fileset).show();
            }
        }

        function setReportTitle(title) {
            jQuery('#central_region').find('.panel-with-icon').text(title);
        }
    }

    function getFiltersValues() {
        var obj = {};

        //In order to have ability of using duplicates fields we have to use class instead id, and get the value of used field (with value)
        jQuery('.js-company-in').each(function (index, element) {
            var val = Utils.clearEmptyStringArray(jQuery(element).combobox('getValues'));
            if(val.length) {
                if(jQuery(element).hasClass("js-inversed")){
                    obj.companiesOut = val;
                } else {
                    obj.companiesIn = val;
                }
            }
            return true;
        });

        jQuery('.js-company-out').each(function (index, element) {
            var val = Utils.clearEmptyStringArray(jQuery(element).combobox('getValues'));
            if(val.length) {
                if(jQuery(element).hasClass("js-inversed")){
                    obj.companiesIn = val;
                } else {
                    obj.companiesOut = val;
                }
            }
            return true;
        });

        var itemGroups = [];
        jQuery('.js-items-groups').each(function (index, element) {
            var val = Utils.clearEmptyStringArray(jQuery(element).combobox('getValues'));
            if(val.length) itemGroups = val;
            return true;
        });

        var companiesOutTransfer = Utils.clearEmptyStringArray(jQuery('#company-out-transfer').combobox('getValues'));
        if(companiesOutTransfer.length > 0) {
            obj.companiesOut = companiesOutTransfer;
        }

        obj.itemsGroups = itemGroups;
        if(jQuery('#code-include').prop('checked')){
            obj.item_name_code = jQuery('#items').textbox('getValue');
        } else {
            obj.item_name = jQuery('#items').textbox('getValue');
        }
        obj.company_code = jQuery('#company-code').textbox('getValue');

        obj.after_doc_date = jQuery('#date_from').datebox('getValue');
        obj.before_doc_date = jQuery('#date_to').datebox('getValue');
        obj.expiry_date_to = jQuery('#expiry_date_to').datebox('getValue');
        obj.expiry_date_from = jQuery('#expiry_date_from').datebox('getValue');
        obj.warehouses = Utils.clearEmptyStringArray(jQuery('#warehouses').combobox('getValues'));
        obj.warehouseFields = DynamicFields.getFieldsValues();
        obj.activeFarm = Utils.clearEmptyStringArray(jQuery('#active-farm').combobox('getValues'));
        obj.batch = jQuery('#batch').textbox('getValue');
        obj.noBatch = jQuery('#noBatch').prop('checked');
        obj.plotName = jQuery('#plot-name').textbox('getValue');
        obj.area = jQuery('#area').textbox('getValue');
        obj.companiesGroups = Utils.clearEmptyStringArray(jQuery('#companies-groups').combobox('getValues'));
        obj.note = jQuery('#note').textbox('getValue');

        return obj;
    }

    function search() {
        if (!selected_report || !selected_report.layout) return false;
        var current_datagrid = jQuery(selected_report.layout).find('.datagrid-f');
        if (!current_datagrid) return false;

        jQuery('#win-filter-report').window('close');

        current_datagrid.datagrid('loadRpc', [{
            report: selected_report.rpcParams.report,
            criteries: {...selected_report.rpcParams.criteries, ...getFiltersValues()},
            parameters: selected_report.rpcParams.parameters
        }]);
    }

    function filter_clear_and_reload() {
        jQuery('#date_from').datebox();
        jQuery('#date_to').datebox();
        jQuery('#expiry_date_from').datebox();
        jQuery('#expiry_date_to').datebox();
        jQuery('.js-company-out').combobox('clear');
        jQuery('.js-company-in').combobox('clear');
        jQuery('#active-farm').combobox('clear');
        jQuery('#company-out-transfer').combobox('clear');
        jQuery('#warehouses').combobox('clear');
        jQuery('#items').textbox('clear');
        jQuery('#company-code').textbox('clear');
        jQuery('#batch').textbox('clear');
        jQuery('#plot-name').textbox('clear');
        jQuery('#area').textbox('clear');
        jQuery('#noBatch').prop('checked', false);
        jQuery('#companies-groups').combobox('clear');
        jQuery('.js-items-groups').combobox('clear');
        jQuery(".warehouseFieldsList .container-box").empty();
        jQuery('#code-include').prop('checked', false);
        jQuery('#note').textbox('clear');
    }

    function printReport(currentPage) {
        var params = {
            report: selected_report.rpcParams.report,
            criteries: {...selected_report.rpcParams.criteries, ...getFiltersValues()},
            parameters: selected_report.rpcParams.parameters
        };
        var current_datagrid = jQuery(selected_report.layout).find('.datagrid-f');

        ReportsApi.getReportData(params)
            .done(function (data) {
                return printResultsFromRequests(data, current_datagrid);
            })
            .fail(function (errorObj) {
                if (errorObj) jQuery.messager.alert('Грешка', errorObj.getMessage());
            });
    }

    function printResultsFromRequests(data, current_datagrid) {
        var Templates = {
            /**
             * Generates html table
             * @param {array} header
             * @param {array} rows
             * @returns {string}
             */
            table: function(header, rows) {
                if(!(header instanceof Array || rows instanceof Array))
                {
                    return 'Invalid arguments!';
                }
                var table = '<table border="1" width="100%" cellpadding="5" cellspacing="0">';
                var fields = [];

                for(var rowHeader of header){
                    table += '<tr>';
                    for(var colHeader of rowHeader)
                    {
                        if(colHeader.colspan) {
                            table += '<th colspan="'+colHeader.colspan+'">' + colHeader.title + '</th>';
                        } else {
                            table += '<th>' + colHeader.title + '</th>';
                        }
                        if(colHeader.field) fields.push(colHeader.field);
                    }
                    table += '</tr>';
                }

                for(var rowTable of rows) {
                    table += '<tr>';
                    for(var field of fields){
                        if(rowTable[field] === null || rowTable[field] === undefined) rowTable[field] = "";
                        table += '<td>' + rowTable[field] + '</td>';
                    }
                    table += '</tr>';
                }

                table += '</table>';

                return table;
            }
        };

        var html;
        var title = '';
        var gridOptions = current_datagrid.datagrid('options');
        var report_date_from = jQuery('#date_from').datebox('getValue');
        var report_date_to = jQuery('#date_to').datebox('getValue');

        var footerData = data.footer;

        var gridData = data.rows;

        var date = new Date();
        var date_now = date.getDate() + '.' + (date.getMonth() + 1) + '.' + date.getFullYear();

        if(report_date_from && report_date_to) {
            title = report_title + ' от дата ' + report_date_from + ' до дата ' + report_date_to;
        } else if(report_date_from && !report_date_to) {
            title = report_title + ' от дата ' + report_date_from + ' до дата ' + date_now;
        } else if(!report_date_from && report_date_to) {
            title = report_title + ' към дата ' + date_now;
        } else {
            title = report_title + ' към дата ' + date_now;
        }

        html = '<style>@page{size: landscape;}body,table{font-family: Arial;font-size: 10px;}</style>' +
            '<h2 align="center">' + title + '</h2>';

        var header = [];
        var columns = gridOptions.columns;

        for (var row of columns) {
            var temp = [];
            for( var col of row) {
                temp.push({
                  'title' : col.title.replace(/<\/?[^>]+(>|$)/g, ""),
                  'colspan' : col.colspan,
                  'field' : col.field,
                });
            }
            header.push(temp);
        }

        var rows = gridData;
        // if (footerData[1]) rows.push(footerData[1]);
        html += Templates.table(header, rows);

        jQuery('body').append('<iframe id="print-iframe"></iframe>');
        var printIframe = jQuery('#print-iframe');
        var contentWindow = printIframe.get(0).contentWindow;
        contentWindow.document.write(html);
        contentWindow.print();
        printIframe.remove();
    }

    // New functions
    function initFilterComponents() {
        jQuery('#date_from').datebox();
        jQuery('#date_to').datebox();
        jQuery('#expiry_date_to').datebox();
        jQuery('#expiry_date_from').datebox();

        jQuery("#tr-plot").textbox();
        jQuery("#tr-area").textbox();

        Comboboxes.companies(".js-company-out", [], {types:[CONSTANTS.COMPANY_TYPE_CONTRAGENT]});
        Comboboxes.companies("#company-out-transfer", [], {types:[CONSTANTS.COMPANY_TYPE_FARM]});
        jQuery("#items").textbox();
        jQuery("#batch").textbox();
        jQuery("#noBatch").prop('checked', false);
        Comboboxes.companies(".js-company-in", [], {types:[CONSTANTS.COMPANY_TYPE_FARM]});
        Comboboxes.allWarehouses("#warehouses");
        Comboboxes.companies("#machine", [], {types:[CONSTANTS.COMPANY_TYPE_MACHINE]});
        Comboboxes.companies("#active-farm", [], {types:[CONSTANTS.COMPANY_TYPE_FARM]});

        Comboboxes.companyGroups("#companies-groups", [], {}, false, false, true);
        Comboboxes.itemGroups(".js-items-groups", [], {}, false, false, true);

        function changeWarehouseNameValue(element) {
            for(var i = 0; i< element.length; i++){
                element[i].text = element[i].name;
                if(element[i].children !== undefined) {
                    changeWarehouseNameValue(element[i].children)
                }
            }

            return element;
        }
    }

    function exportReport(exportFormat, fullReport = true)
    {
        var winDownload = jQuery('#win-download').window({});
        var data = jQuery('#simple-transactions-table').datagrid('getData');
        var gridOptions = jQuery('#simple-transactions-table').datagrid('options');
        if (data.rows.length === 0) return jQuery.messager.alert('Грешка', 'Не може да експортнете празна справка.', 'error');

        selected_report.rpcParams.parameters.tableHeaders = [];
        selected_report.rpcParams.parameters.tableFooters = [];

        if(!fullReport) {
            selected_report.rpcParams.parameters.rows = gridOptions.pageSize;
            selected_report.rpcParams.parameters.page = gridOptions.pageNumber;
        }

        // add report name to header 
        columnsLength = 1;
        for(var row of selected_report.columns){
            row = row.filter(col => col[`exportTo${exportFormat.toUpperCase()}`] !== false)
            if (columnsLength < row.length) {
                columnsLength = row.length;
            }
        }

        let title = [{
            title: selected_report.text,
            colspan: columnsLength,
        }];

        selected_report.rpcParams.parameters.tableHeaders.push(title);

        // add additional headers for PDF 
        if(exportFormat === EXPORT_FORMAT_PDF) {
            if (selected_report.name === 'asset-report') {
                additionalHeaderDataForSubtractedItemsReport(columnsLength);
            }
        
            addHeaderDataFromFilter(columnsLength);
        }

        for(var row of selected_report.columns){
            var temp = [];
            for(var col of row){
                if(col[`exportTo${exportFormat.toUpperCase()}`] === false) {
                    continue;
                }
                
                temp.push({
                    title: col.title.replace(/<\/?[^>]+(>|$)/g, ""),
                    colspan: col.colspan,
                    field: col.field
                });
            }
            selected_report.rpcParams.parameters.tableHeaders.push(temp);
        }

        if(selected_report.footers){
            selected_report.rpcParams.parameters.tableFooters = selected_report.footers;
        }

        selected_report.rpcParams.parameters.export = exportFormat.toLowerCase();
        var report = getFiltersValues();
        report.report = selected_report.rpcParams.report;

        var params = {
            report: report.report,
            criteries: {...selected_report.rpcParams.criteries, ...getFiltersValues()},
            parameters: selected_report.rpcParams.parameters
        };

        ReportsApi.exportReport(params)
            .done(function (data) {
                var downloadFile = jQuery('#btn-download-file');
                downloadFile.attr("href", data.result.file_path);
                winDownload.window('open');
            })
            .fail(function (errorObj) {
                if (errorObj) jQuery.messager.alert('Грешка', errorObj.getMessage());
            });
    }

    function addHeaderDataFromFilter(columnsLength) {
        const dateFrom = jQuery('#date_from').datebox('getValue')
        const dateTo = jQuery('#date_to').datebox('getValue')

        if (dateFrom != '' && dateTo != '') {
            selected_report.rpcParams.parameters.tableHeaders.push([{
                title: `Период: ${dateFrom} - ${dateTo}`,
                colspan: columnsLength,
            }]);
        }

        const expiryDateFrom = jQuery('#expiry_date_from').datebox('getValue')
        const expiryDateTo = jQuery('#expiry_date_to').datebox('getValue')
        console.log(expiryDateFrom, expiryDateTo);

        if(expiryDateFrom != '' && expiryDateTo != '') {
            selected_report.rpcParams.parameters.tableHeaders.push([{
                title: `Период: ${expiryDateFrom} - ${expiryDateTo}`,
                colspan: columnsLength,
            }]);
        }
    }

    function additionalHeaderDataForSubtractedItemsReport(columnsLength) {
        let additionalHeaderColumns = [];

        const selectedFarmId = jQuery('#active-farm').combobox('getValue') 
        const farms = jQuery('#active-farm').combobox('getData') 

        if ( selectedFarmId!= '') {
            additionalHeaderColumns.push([{
                title: `${farms.find(farm => farm.id == selectedFarmId).name}`,
                colspan: columnsLength,
            }]);
        } else {
            const farmNames = farms.map(farm => farm.name).join(', ')
            additionalHeaderColumns.push([{
                title: farmNames,
                colspan: columnsLength,
            }]);
        }

        const selectedMachine = jQuery('#machine').combobox('getValue')
        const machines = jQuery('#machine').combobox('getData') 

        if (selectedMachine != '' ) {
            additionalHeaderColumns.push([{
                title: `${machines.find(machine => machine.id == selectedMachine).name}`,
                colspan: columnsLength,
            }]);
        } else {
            additionalHeaderColumns.push([{
                title: "Всички",
                colspan: columnsLength,
            }]);

        }

        if(additionalHeaderColumns.length > 0){
            additionalHeaderColumns.forEach(header => {
                selected_report.rpcParams.parameters.tableHeaders.push(header);
            });
        }
    }

    return {
        init
    };

});
