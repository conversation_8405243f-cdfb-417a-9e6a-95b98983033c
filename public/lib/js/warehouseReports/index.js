require(["../../app"], function (app) {
    require(["js_external/domReady!", "js/warehouse/utils"], function (domReady, Utils) {
        Utils.hidePageLoader();
        require([
            "jquery",
            "easyui",
            "js/main/navigation-menu",
            "js/warehouseReports/home",
            "js/warehouse/home",
            "js/warehouse/warehouse-navigation",
            "js_external/locale/easyui-lang-bg",
        ], function (
            jQuery,
            _,
            navigation_menu,
            home,
            warehouse_home,
            WarehouseNavigation
        ) {
            jQuery.noConflict();
            navigation_menu.init();
            home.init();
            warehouse_home.init();
            jQuery("#body-init-loading").hide();
            jQuery("#bodydiv").css("visibility", "visible");
            WarehouseNavigation.init();
        });
    });
});
