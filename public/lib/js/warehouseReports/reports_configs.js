define([
    "jquery",
    "easyui",
    "js/warehouse/constants",
    "js/warehouse/history",
], function(
    jQuery,
    _,
    CONSTANTS,
    historyLog,
) {
    function init() {}
    
    function drawButton(value, row, index) {
        return '<button  type="button" class="icon-info" id="history-button-'+row.id+'" style="width:16px;height:16px;border:0"></button>';
    }

    const getJson = (str) => {
        try {
            return JSON.parse(str);
        } catch (e) {
            return str;
        }
    }

    // const formatWarehouseDynamicFields = (value, row, index) => {
    //     if (typeof value === 'undefined' || value === null) return;
    //
    //     let innerHtml = "";
    //     const transactionField = getJson(value);
    //     const wField = getJson(row.warehouse_fields);
    //
    //     Object.keys(transactionField).forEach(key => {
    //         innerHtml += "<div>"+wField[key].label + ": " + transactionField[key] + "</div>";
    //     });
    //
    //     return innerHtml;
    // }

    function getInitData() {

        jQuery.messager.progress({
            msg: 'Моля изчакайте',
            text: 'Зареждане...',
            interval: 200,
            openAnimation: 'fade',
            closeAnimation: 'fade',
            shadow: false,
            onOpen: function (){
                jQuery('.window-mask').hide();
            },
            onBeforeOpen: function (){
                jQuery('#progress-mask').fadeIn(500);
            },
            onBeforeClose: function () {
                jQuery('#progress-mask').fadeOut(500);
            }
        });

        return [
            {
                id: 1,
                iconCls: 'icon-datagrid',
                module: 'Warehouse',
                filters:['reports_filter_document_dates', 'reports_filter_companies_group', 'reports_filter_warehouses_and_items', 'reports_filter_groups', 'reports_filter_note'],
                layout: '#report-simple-transactions-layout',
                name: 'report-add-transactions',
                text: 'Заприходени количества',
                grid: 'initTransactionsReportGrid',
                rpcParams: {
                    showFooter: true,
                    report: 'added-items-report',
                    criteries: {
                        document_tr_types: ["ADD"],
                        showOnlyClosedDocuments: true
                    },
                    parameters: {}
                },
                onLoadSuccessCallback: function(data) {
                    jQuery.messager.progress('close');

                    var grid = jQuery('#simple-transactions-table');
                    grid.datagrid('reloadFooter', [
                        {
                            measure_short_name: '<b>Кол. за стр.</b>',
                            quantity:parseFloat(data.amounts.pageQty).toFixed(3),
                            single_price_no_dds:'<b>С-т за стр.</b>',
                            value_price_no_dds: parseFloat(data.amounts.pagePriceNoDDS).toFixed(5),
                        },
                        {
                            measure_short_name:'<b>Общо кол.</b>',
                            quantity:parseFloat(data.amounts.totalQty).toFixed(3),
                            single_price_no_dds:'<b>Обща с-т</b>',
                            value_price_no_dds:parseFloat(data.amounts.totalPriceNoDDS).toFixed(5),
                        }
                    ]);
                },
                footers: [
                    {
                        measure_short_name: {
                            type: 'text',
                            value: "Общо количество"
                        },
                        quantity: {
                            type: 'value',
                            value: "pageQty"
                        },
                        single_price_no_dds: {
                            type: 'text',
                            value: "Обща стойност"
                        },
                        value_price_no_dds: {
                            type: 'value',
                            value: "pagePriceNoDDS"
                        },
                    },
                ],
                columns: [
                    [
                        {
                            field: 'document_number',
                            title: '<b>Документ №</b>',
                            sortable: false,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        },
                        {
                            field: 'document_date',
                            title: '<b>Документ дата</b>',
                            sortable: false,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        },
                        {
                            field: 'company_in_name',
                            title: '<b>Стопанство</b>',
                            sortable: false,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'warehouse_name',
                            title: '<b>Склад</b>',
                            sortable: true,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        },
                        {
                            field: 'sub_warehouse_name',
                            title: '<b>Подсклад</b>',
                            sortable: false,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'item_name',
                            title: '<b>Артикул</b>',
                            sortable: true,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'item_code',
                            title: '<b>Артикул код</b>',
                            sortable: true,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'measure_short_name',
                            title: '<b>Мярка</b>',
                            sortable: true,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'quantity',
                            title: '<b>Количество</b>',
                            sortable: true,
                            align: 'right',
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'single_price_no_dds',
                            title: '<b>Ед. цена</b>',
                            sortable: true,
                            align: 'right',
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'value_price_no_dds',
                            title: '<b>Стойност</b>',
                            sortable: true,
                            align: 'right',
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        },
                        {
                            field: 'company_out_name',
                            title: '<b>Контрагент</b>',
                            sortable: true,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'user_name',
                            title: '<b>Заприходено от</b>',
                            sortable: true,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        },
                        // , {
                        //     field: "transaction_fields",
                        //     title: "<b>Допълнителни полета</b>",
                        //     width: 240,
                        //     formatter: formatWarehouseDynamicFields
                        // }
                        {
                            field: 'transaction_note',
                            title: '<b>Бележка</b>',
                            sortable: true,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }
                    ]
                ],
            },
            {
                id: 1,
                iconCls: 'icon-datagrid',
                module: 'Warehouse',
                filters:['reports_filter_document_dates', 'reports_filter_company_in', 'reports_filter_warehouses_and_items', 'reports_filter_groups_items', 'reports_filter_document_plot', 'reports_filter_note'],
                layout: '#report-simple-transactions-layout',
                name: 'report-add-transactions',
                text: 'Готова продукция',
                grid: 'initTransactionsReportGrid',
                rpcParams: {
                    showFooter: true,
                    report: 'added-items-report',
                    criteries: {
                        document_tr_types: ["ADD_PRODUCTION"]
                    },
                    parameters: {}
                },
                onLoadSuccessCallback: function(data) {
                    var grid = jQuery('#simple-transactions-table');
                    grid.datagrid('reloadFooter', [
                        {
                            measure_short_name: '<b>Кол. за стр.</b>',
                            quantity:parseFloat(data.amounts.pageQty).toFixed(3),
                            single_price_no_dds:'<b>С-т за стр.</b>',
                            value_price_no_dds: parseFloat(data.amounts.pagePriceNoDDS).toFixed(5),
                        },
                        {
                            measure_short_name:'<b>Общо кол.</b>',
                            quantity:parseFloat(data.amounts.totalQty).toFixed(3),
                            single_price_no_dds:'<b>Обща с-т</b>',
                            value_price_no_dds:parseFloat(data.amounts.totalPriceNoDDS).toFixed(5),
                        }
                    ]);
                },
                footers: [
                    {
                        measure_short_name: {
                            type: 'text',
                            value: "Общо количество"
                        },
                        quantity: {
                            type: 'value',
                            value: "pageQty"
                        },
                        single_price_no_dds: {
                            type: 'text',
                            value: "Обща стойност"
                        },
                        value_price_no_dds: {
                            type: 'value',
                            value: "pagePriceNoDDS"
                        },
                    },
                ],
                columns: [
                    [
                        {
                            field: 'document_number',
                            title: '<b>Документ №</b>',
                            sortable: false,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        },
                        {
                            field: 'document_date',
                            title: '<b>Документ дата</b>',
                            sortable: false,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        },
                        {
                            field: 'company_in_name',
                            title: '<b>Стопанство</b>',
                            sortable: false,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'warehouse_name',
                            title: '<b>Склад</b>',
                            sortable: true,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        },
                        {
                            field: 'sub_warehouse_name',
                            title: '<b>Подсклад</b>',
                            sortable: false,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'item_name',
                            title: '<b>Артикул</b>',
                            sortable: true,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'item_code',
                            title: '<b>Артикул код</b>',
                            sortable: true,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'measure_short_name',
                            title: '<b>Мярка</b>',
                            sortable: true,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'quantity',
                            title: '<b>Количество</b>',
                            sortable: true,
                            align: 'right',
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                            formatter: function (val) {
                                return parseFloat(val).toFixed(3)
                            }
                        }, 
                        {
                            field: 'single_price_no_dds',
                            title: '<b>Ед. цена</b>',
                            sortable: true,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                            formatter: function (val, row) {
                                if(row.item_id !== undefined) { //Check the row is not a footer row
                                    return parseFloat(val).toFixed(5)
                                }
                                return val;
                            }
                        }, 
                        {
                            field: 'value_price_no_dds',
                            title: '<b>Стойност</b>',
                            sortable: true,
                            align: 'right',
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                            formatter: function (val) {
                                return parseFloat(val).toFixed(5)
                            }
                        },
                        {
                            field: 'document_plot_name',
                            title: '<b>Име на парцела</b>',
                            sortable: true,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        },
                        {
                            field: 'document_area',
                            title: '<b>Землище</b>',
                            sortable: true,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        },
                        {
                            field: 'user_name',
                            title: '<b>Заприходено от</b>',
                            sortable: true,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }
                        // , {
                        //     field: "transaction_fields",
                        //     title: "<b>Допълнителни полета</b>",
                        //     width: 240,
                        //     formatter: formatWarehouseDynamicFields
                        // }
                    ]
                ]
            },
            {
                id: 2,
                iconCls: 'icon-datagrid',
                module: 'Warehouse',
                filters:['reports_filter_document_dates', 'reports_filter_companies_group_inversed', 'reports_filter_warehouses_and_items', 'reports_filter_note'],
                layout: '#report-simple-transactions-layout',
                name: 'subtracted-items-report',
                text: 'Изписани количества към контрагенти',
                grid: 'initTransactionsReportGrid',
                rpcParams: {
                    showFooter: true,
                    report: 'subtracted-items-report',
                    criteries: {
                        document_tr_types: ["SUB"]
                    },
                    parameters: {}
                },
                onLoadSuccessCallback: function(data) {
                    var grid = jQuery('#simple-transactions-table');
                    grid.datagrid('reloadFooter', [
                        {
                            measure_short_name: '<b>Кол. за стр.</b>',
                            quantity:parseFloat(data.amounts.pageQty).toFixed(3),
                            single_price_no_dds:'<b>С-т за стр.</b>',
                            value_price_no_dds: parseFloat(data.amounts.pagePriceNoDDS).toFixed(5),
                        },
                        {
                            measure_short_name:'<b>Общо кол.</b>',
                            quantity:parseFloat(data.amounts.totalQty).toFixed(3),
                            single_price_no_dds:'<b>Обща с-т</b>',
                            value_price_no_dds:parseFloat(data.amounts.totalPriceNoDDS).toFixed(5),
                        }
                    ]);
                },
                footers: [
                    {
                        measure_short_name: {
                            type: 'text',
                            value: "Общо количество"
                        },
                        quantity: {
                            type: 'value',
                            value: "pageQty"
                        },
                        single_price_no_dds: {
                            type: 'text',
                            value: "Обща стойност"
                        },
                        value_price_no_dds: {
                            type: 'value',
                            value: "pagePriceNoDDS"
                        },
                    },
                ],
                columns: [
                    [
                        {
                            field: 'document_number',
                            title: '<b>Документ №</b>',
                            sortable: false,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        },
                        {
                            field: 'document_date',
                            title: '<b>Документ дата</b>',
                            sortable: false,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        },
                        {
                            field: 'company_out_name',
                            title: '<b>Стопанство</b>',
                            sortable: false,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'warehouse_name',
                            title: '<b>Склад</b>',
                            sortable: true,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        },
                        {
                            field: 'sub_warehouse_name',
                            title: '<b>Подсклад</b>',
                            sortable: false,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'item_name',
                            title: '<b>Артикул</b>',
                            sortable: true,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'item_code',
                            title: '<b>Артикул код</b>',
                            sortable: true,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'measure_short_name',
                            title: '<b>Мярка</b>',
                            sortable: true,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'quantity',
                            title: '<b>Количество</b>',
                            sortable: true,
                            align: 'right',
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                            formatter: function (val) {
                                return Math.abs(val).toFixed(3)
                            }
                        }, 
                        {
                            field: 'single_price_no_dds',
                            title: '<b>Ед. цена</b>',
                            sortable: true,
                            align: 'right',
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'value_price_no_dds',
                            title: '<b>Стойност</b>',
                            sortable: true,
                            align: 'right',
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        },
                        {
                            field: 'company_in_name',
                            title: '<b>Контрагент</b>',
                            sortable: true,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'user_name',
                            title: '<b>Изписано от</b>',
                            sortable: true,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }
                        // , {
                        //     field: "transaction_fields",
                        //     title: "<b>Допълнителни полета</b>",
                        //     width: 240,
                        //     formatter: formatWarehouseDynamicFields
                        // }
                    ]
                ]
            },
            {
                id: 2,
                iconCls: 'icon-datagrid',
                module: 'Warehouse',
                filters:['reports_filter_document_dates', 'reports_filter_companies_group_trasfer', 'reports_filter_warehouses_and_items', 'reports_filter_note'],
                layout: '#report-simple-transactions-layout',
                name: 'subtracted-items-report',
                text: 'Изписани количества към парцели',
                grid: 'initTransactionsReportGrid',
                rpcParams: {
                    showFooter: true,
                    report: 'subtracted-items-report',
                    criteries: {
                        document_tr_types: ["SUB_PLOT"]
                    },
                    parameters: {}
                },
                onLoadSuccessCallback: function(data) {
                    var grid = jQuery('#simple-transactions-table');
                    grid.datagrid('reloadFooter', [
                        {
                            measure_short_name: '<b>Кол. за стр.</b>',
                            quantity:parseFloat(data.amounts.pageQty).toFixed(3),
                            single_price_no_dds:'<b>С-т за стр.</b>',
                            value_price_no_dds: parseFloat(data.amounts.pagePriceNoDDS).toFixed(5),
                        },
                        {
                            measure_short_name:'<b>Общо кол.</b>',
                            quantity:parseFloat(data.amounts.totalQty).toFixed(3),
                            single_price_no_dds:'<b>Обща с-т</b>',
                            value_price_no_dds:parseFloat(data.amounts.totalPriceNoDDS).toFixed(5),
                        }
                    ]);
                },
                footers: [
                    {
                        measure_short_name: {
                            type: 'text',
                            value: "Общо количество"
                        },
                        quantity: {
                            type: 'value',
                            value: "pageQty"
                        },
                        single_price_no_dds: {
                            type: 'text',
                            value: "Обща стойност"
                        },
                        value_price_no_dds: {
                            type: 'value',
                            value: "pagePriceNoDDS"
                        },
                    },
                ],
                columns: [
                    [
                        {
                            field: 'document_number',
                            title: '<b>Документ №</b>',
                            sortable: false,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        },
                        {
                            field: 'document_date',
                            title: '<b>Документ дата</b>',
                            sortable: false,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        },
                        {
                            field: 'company_out_name',
                            title: '<b>Доставчик</b>',
                            sortable: false,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'warehouse_name',
                            title: '<b>Склад</b>',
                            sortable: true,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        },
                        {
                            field: 'sub_warehouse_name',
                            title: '<b>Подсклад</b>',
                            sortable: false,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'item_name',
                            title: '<b>Артикул</b>',
                            sortable: true,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'item_code',
                            title: '<b>Артикул код</b>',
                            sortable: true,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'measure_short_name',
                            title: '<b>Мярка</b>',
                            sortable: true,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'quantity',
                            title: '<b>Количество</b>',
                            sortable: true,
                            align: 'right',
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                            formatter: function (val) {
                                return Math.abs(val).toFixed(3)
                            }
                        }, 
                        {
                            field: 'single_price_no_dds',
                            title: '<b>Ед. цена</b>',
                            sortable: true,
                            align: 'right',
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'value_price_no_dds',
                            title: '<b>Стойност</b>',
                            sortable: true,
                            align: 'right',
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        },
                        {
                            field: 'company_in_name',
                            title: '<b>Получател</b>',
                            sortable: true,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'user_name',
                            title: '<b>Изписано от</b>',
                            sortable: true,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }
                        // , {
                        //     field: "transaction_fields",
                        //     title: "<b>Допълнителни полета</b>",
                        //     width: 240,
                        //     formatter: formatWarehouseDynamicFields
                        // }
                    ]
                ]
            },
            {
                id: 3,
                iconCls: 'icon-datagrid',
                module: 'Plots',
                filters:['reports_filter_company_in', 'reports_filter_warehouses_and_items', 'reports_filter_note'],
                layout: '#report-simple-transactions-layout',
                name: 'report-own-plots-detailed',
                text: 'Текущи наличности',
                grid: 'initCurrentAvailabilityReportGrid',
                default_filter: ['report_date_as_of'],
                rpcParams: {
                    showFooter: false,
                    report: 'current-availability-report',
                    parameters: {}
                },
                columns: [
                    [
                        {
                            title: '',
                            colspan: 7
                        }, 
                        {
                            title: '<b>Наличност</b>',
                            colspan: 2
                        }
                    ],
                    [
                        {
                            field: 'company_in_name',
                            title: '<b>Стопанство</b>',
                            sortable: false,
                            width: 200,
                            exportToXLS: true,
                            exportToPDF: true,
                        },
                        {
                            field: 'warehouse_name',
                            title: '<b>Склад</b>',
                            sortable: true,
                            width: 200,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'sub_warehouse_name',
                            title: '<b>Подсклад</b>',
                            sortable: false,
                            width: 200,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'item_name',
                            title: '<b>Артикул</b>',
                            sortable: true,
                            width: 200,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'item_code',
                            title: '<b>Артикул код</b>',
                            sortable: true,
                            width: 200,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'measure_short_name',
                            title: '<b>Мярка</b>',
                            sortable: true,
                            width: 200,
                            exportToXLS: true,
                            exportToPDF: true,
                        }
                        // , {
                        //     field: "fields",
                        //     title: "<b>Допълнителни полета</b>",
                        //     width: 240,
                        //     formatter: formatWarehouseDynamicFields
                        // }
                        , {
                            field: 'quantity',
                            title: '<b>Количество</b>',
                            sortable: true,
                            align: 'right',
                            width: 200,
                            exportToXLS: true,
                            exportToPDF: true,
                            formatter: function (val) {
                                return parseFloat(val).toFixed(3)
                            }
                        }, 
                        {
                            field: 'single_price_no_dds',
                            title: '<b>Ед. цена</b>',
                            sortable: true,
                            align: 'right',
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                            formatter: function (val) {
                                return parseFloat(val).toFixed(5)
                            }
                        }
                    ]
                ]
            },
            {
                id: 4,
                iconCls: 'icon-datagrid',
                module: 'Plots',
                filters:['reports_filter_document_dates', 'reports_filter_company_in', 'reports_filter_warehouses_and_items', 'reports_filter_note'],
                layout: '#report-simple-transactions-layout',
                name: 'report-own-plots-detailed',
                text: 'Отчет за склад',
                grid: 'initWarehouseReportGrid',
                default_filter: ['report_date_as_of'],
                rpcParams: {
                    showFooter: false,
                    report: 'warehouse-items-report',
                    parameters: {}
                },
                columns: [
                    [
                        {
                            title: '',
                            colspan: 6
                        },
                        {
                            title: '<b>Начална наличност</b>',
                            colspan: 2
                        },
                        {
                            title: '<b>Приход</b>',
                            colspan: 2
                        },
                        {
                            title: '<b>Разход</b>',
                            colspan: 2
                        },
                        {
                            title: '<b>Крайна наличност</b>',
                            colspan: 2,
                        }
                    ],
                    [
                        {
                            field: 'company_in_name',
                            title: '<b>Стопанство</b>',
                            sortable: false,
                            width: 130,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'warehouse_name',
                            title: '<b>Склад</b>',
                            sortable: false,
                            width: 160,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'sub_warehouse_name',
                            title: '<b>Подсклад</b>',
                            sortable: true,
                            width: 160,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'item_name',
                            title: '<b>Артикул</b>',
                            sortable: true,
                            width: 100,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'item_code',
                            title: '<b>Артикул код</b>',
                            sortable: true,
                            width: 100,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'measure_short_name',
                            title: '<b>Мярка</b>',
                            sortable: true,
                            width: 80,
                            exportToXLS: true,
                            exportToPDF: true,
                        }
                        // , {
                        //     field: "transaction_fields",
                        //     title: "<b>Допълнителни полета</b>",
                        //     width: 240,
                        //     formatter: formatWarehouseDynamicFields
                        // }
                        , {
                            field: 'init_quantity',
                            title: '<b>Количество</b>',
                            sortable: true,
                            width: 100,
                            exportToXLS: true,
                            exportToPDF: true,
                            align: 'right',
                            formatter: function (val) {
                                return Math.abs(val).toFixed(3)
                            }
                        }, 
                        {
                            field: 'init_value_price_no_dds',
                            title: '<b>Стойност</b>',
                            sortable: true,
                            width: 100,
                            exportToXLS: true,
                            exportToPDF: true,
                            align: 'right',
                            formatter: function (val) {
                                return Math.abs(val).toFixed(5)
                            }
                        }, 
                        {
                            field: 'added_quantity',
                            title: '<b>Количество</b>',
                            sortable: true,
                            width: 100,
                            exportToXLS: true,
                            exportToPDF: true,
                            align: 'right',
                            formatter: function (val) {
                                return Math.abs(val).toFixed(3)
                            }
                        }, 
                        {
                            field: 'added_value_price_no_dds',
                            title: '<b>Стойност</b>',
                            sortable: true,
                            width: 100,
                            exportToXLS: true,
                            exportToPDF: true,
                            align: 'right',
                            formatter: function (val) {
                                return Math.abs(val).toFixed(5)
                            }
                        }, 
                        {
                            field: 'subtracted_quantity',
                            title: '<b>Количество</b>',
                            sortable: true,
                            width: 100,
                            exportToXLS: true,
                            exportToPDF: true,
                            align: 'right',
                            formatter: function (val) {
                                return Math.abs(val).toFixed(3)
                            }
                        }, 
                        {
                            field: 'subtracted_value_price_no_dds',
                            title: '<b>Стойност</b>',
                            sortable: true,
                            width: 100,
                            exportToXLS: true,
                            exportToPDF: true,
                            align: 'right',
                            formatter: function (val) {
                                return Math.abs(val).toFixed(5)
                            }
                        }, 
                        {
                            field: 'rest_quantity',
                            title: '<b>Количество</b>',
                            sortable: true,
                            width: 100,
                            exportToXLS: true,
                            exportToPDF: true,
                            align: 'right',
                        }, 
                        {
                            field: 'rest_value_price_no_dds',
                            title: '<b>Стойност</b>',
                            sortable: true,
                            width: 100,
                            exportToXLS: true,
                            exportToPDF: true,
                            align: 'right',
                        }
                    ]
                ]
            },
            {
                id: 4,
                iconCls: 'icon-datagrid',
                module: 'Plots',
                filters:['reports_filter_document_dates', 'reports_filter_company_in', 'reports_filter_warehouses_and_items', 'reports_filter_batch', 'reports_filter_note'],
                layout: '#report-simple-transactions-layout',
                name: 'report-own-plots-detailed',
                text: 'Отчет за склад по партиди',
                grid: 'initWarehouseReportGrid',
                default_filter: ['report_date_as_of'],
                rpcParams: {
                    showFooter: false,
                    report: 'warehouse-items-report',
                    parameters: {},
                    criteries: {
                        groupByBatch: true
                    }
                },
                columns: [
                    [
                        {
                            title: '',
                            colspan: 7
                        },
                        {
                            title: '<b>Начална наличност</b>',
                            colspan: 2
                        },
                        {
                            title: '<b>Приход</b>',
                            colspan: 2
                        },
                        {
                            title: '<b>Разход</b>',
                            colspan: 2
                        },
                        {
                            title: '<b>Крайна наличност</b>',
                            colspan: 2,
                        }
                    ],
                    [
                        {
                            field: 'company_in_name',
                            title: '<b>Стопанство</b>',
                            sortable: false,
                            width: 130,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'warehouse_name',
                            title: '<b>Склад</b>',
                            sortable: false,
                            width: 160,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, {
                            field: 'sub_warehouse_name',
                            title: '<b>Подсклад</b>',
                            sortable: true,
                            width: 160,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'item_name',
                            title: '<b>Артикул</b>',
                            sortable: true,
                            width: 100,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'batch',
                            title: '<b>Партида</b>',
                            sortable: true,
                            width: 100,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'item_code',
                            title: '<b>Артикул код</b>',
                            sortable: true,
                            width: 100,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'measure_short_name',
                            title: '<b>Мярка</b>',
                            sortable: true,
                            width: 80,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'init_quantity',
                            title: '<b>Количество</b>',
                            sortable: true,
                            align: 'right',
                            width: 100,
                            exportToXLS: true,
                            exportToPDF: true,
                            formatter: function (val) {
                                return Math.abs(val).toFixed(3)
                            }
                        }, 
                        {
                            field: 'init_value_price_no_dds',
                            title: '<b>Стойност</b>',
                            sortable: true,
                            align: 'right',
                            width: 100,
                            exportToXLS: true,
                            exportToPDF: true,
                            formatter: function (val) {
                                return Math.abs(val).toFixed(5)
                            }
                        }, 
                        {
                            field: 'added_quantity',
                            title: '<b>Количество</b>',
                            sortable: true,
                            align: 'right',
                            width: 100,
                            exportToXLS: true,
                            exportToPDF: true,
                            formatter: function (val) {
                                return Math.abs(val).toFixed(3)
                            }
                        }, 
                        {
                            field: 'added_value_price_no_dds',
                            title: '<b>Стойност</b>',
                            sortable: true,
                            align: 'right',
                            width: 100,
                            exportToXLS: true,
                            exportToPDF: true,
                            formatter: function (val) {
                                return Math.abs(val).toFixed(5)
                            }
                        }, 
                        {
                            field: 'subtracted_quantity',
                            title: '<b>Количество</b>',
                            sortable: true,
                            align: 'right',
                            width: 100,
                            exportToXLS: true,
                            exportToPDF: true,
                            formatter: function (val) {
                                return Math.abs(val).toFixed(3)
                            }
                        }, 
                        {
                            field: 'subtracted_value_price_no_dds',
                            title: '<b>Стойност</b>',
                            sortable: true,
                            align: 'right',
                            width: 100,
                            exportToXLS: true,
                            exportToPDF: true,
                            formatter: function (val) {
                                return Math.abs(val).toFixed(5)
                            }
                        }, 
                        {
                            field: 'rest_quantity',
                            title: '<b>Количество</b>',
                            sortable: true,
                            align: 'right',
                            width: 100,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'rest_value_price_no_dds',
                            title: '<b>Стойност</b>',
                            sortable: true,
                            align: 'right',
                            width: 100,
                            exportToXLS: true,
                            exportToPDF: true,
                        }
                    ]
                ]
            },
            {
                id: 5,
                iconCls: 'icon-datagrid',
                module: 'Plots',
                filters:['reports_filter_document_dates', 'reports_filter_companies_group_trasfer', 'reports_filter_warehouses_and_items', 'reports_filter_note'],
                layout: '#report-simple-transactions-layout',
                name: 'report-own-plots-detailed',
                text: 'Прехвърлени количества',
                grid: 'initTransferredItemsReportGrid',
                default_filter: ['report_date_as_of'],
                rpcParams: {
                    showFooter: true,
                    report: 'transferred-items-report',
                    parameters: {}
                },
                onLoadSuccessCallback: function(data) {
                    var grid = jQuery('#simple-transactions-table');
                    grid.datagrid('reloadFooter', [
                        {
                            measure_short_name: '<b>Кол. за стр.</b>',
                            quantity:parseFloat(data.amounts.pageQty).toFixed(3),
                            single_price_no_dds:'<b>С-т за стр.</b>',
                            value_price_no_dds: parseFloat(data.amounts.pagePriceNoDDS).toFixed(5),
                        },
                        {
                            measure_short_name:'<b>Общо кол.</b>',
                            quantity:parseFloat(data.amounts.totalQty).toFixed(3),
                            single_price_no_dds:'<b>Обща с-т</b>',
                            value_price_no_dds:parseFloat(data.amounts.totalPriceNoDDS).toFixed(5),
                        }
                    ]);
                },
                footers: [
                    {
                        measure_short_name: {
                            type: 'text',
                            value: "Общо количество"
                        },
                        quantity: {
                            type: 'value',
                            value: "pageQty"
                        },
                        single_price_no_dds: {
                            type: 'text',
                            value: "Обща стойност"
                        },
                        value_price_no_dds: {
                            type: 'value',
                            value: "pagePriceNoDDS"
                        },
                    },
                ],
                columns: [
                    [
                        {
                            title: '<b>Данни за изпращача</b>',
                            colspan: 3
                        }, 
                        {
                            title: '',
                            colspan: 9
                        }, 
                        {
                            title: '<b>Данни за получателя</b>',
                            colspan: 3
                        }
                    ],
                    [
                        {
                            field: 'company_out_name',
                            title: '<b>Стопанство</b>',
                            sortable: true,
                            width: 130,
                            exportToXLS: true,
                            exportToPDF: true,
                        },
                        {
                            field: 'warehouse_out_name',
                            title: '<b>Склад</b>',
                            sortable: true,
                            width: 130,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'sub_warehouse_out_name',
                            title: '<b>Подсклад</b>',
                            sortable: true,
                            width: 130,
                            exportToXLS: true,
                            exportToPDF: true,
                        },
                        {
                            field: 'document_number',
                            title: '<b>Документ №</b>',
                            sortable: false,
                            width: 100,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'document_date',
                            title: '<b>Документ дата</b>',
                            sortable: true,
                            width: 100,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'item_code',
                            title: '<b>Артикул код</b>',
                            sortable: true,
                            width: 80,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'item_name',
                            title: '<b>Артикул</b>',
                            sortable: true,
                            width: 80,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'measure_short_name',
                            title: '<b>Мярка</b>',
                            sortable: true,
                            width: 80,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'quantity',
                            title: '<b>Количество</b>',
                            sortable: true,
                            align: 'right',
                            width: 100,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'single_price_no_dds',
                            title: '<b>Ед. цена</b>',
                            sortable: true,
                            align: 'right',
                            width: 100,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'value_price_no_dds',
                            title: '<b>Стойност</b>',
                            sortable: true,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }
                        // , {
                        //     field: "transaction_fields",
                        //     title: "<b>Допълнителни полета</b>",
                        //     width: 240,
                        //     formatter: formatWarehouseDynamicFields
                        // }
                        ,{
                            field: 'company_in_name',
                            title: '<b>Стопанство</b>',
                            sortable: true,
                            align: 'right',
                            width: 130,
                        }, 
                        {
                            field: 'warehouse_in_name',
                            title: '<b>Склад</b>',
                            sortable: true,
                            width: 130
                        }, 
                        {
                            field: 'sub_warehouse_in_name',
                            title: '<b>Подсклад</b>',
                            sortable: true,
                            width: 130
                        }
                    ]
                ]
            },
            {
                id: 6,
                iconCls: 'icon-datagrid',
                module: 'Plots',
                filters:['reports_filter_expiry_date', 'reports_filter_company_in', 'reports_filter_warehouses_and_items', 'reports_filter_note'],
                layout: '#report-simple-transactions-layout',
                name: 'report-own-plots-detailed',
                text: 'Срок на годност',
                grid: 'initItemsExpiryDateReportGrid',
                default_filter: ['report_date_as_of'],
                rpcParams: {
                    showFooter: false,
                    report: 'items-expiry-date-report',
                    parameters: {}
                },
                legendMsg: '<ul>' +
                    '<li>' +
                    '<div style="width: 30px; height: 10px; background-color: #fb9d37; float: left; margin-top: 4px;margin-right: 5px;">' +
                    '</div>Записите маркирани в този цвят показват, че срокът на годност на съответните артикули ще изтече след по-малко от ' + CONSTANTS.EXPIRY_DATE_NOTIFICATION_DAYS + ' дни.' +
                    '</li>' +
                    '<li>' +
                    '<div style="width: 30px; height: 10px; background-color: #FF0000; float: left; margin-top: 4px;margin-right: 5px;">' +
                    '</div>Записите маркирани в този цвят показват, че срокът на годност на съответните артикули е изтекъл.' +
                    '</li>' +
                    '</ul>',
                columns: [
                    [
                        {
                            field: 'company_in_name',
                            title: '<b>Стопанство</b>',
                            sortable: false,
                            width: 200,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'warehouse_name',
                            title: '<b>Склад</b>',
                            sortable: true,
                            width: 200,
                            exportToXLS: true,
                            exportToPDF: true,
                        },
                        {
                            field: 'sub_warehouse_name',
                            title: '<b>Подсклад</b>',
                            sortable: false,
                            width: 200,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'expiry_date',
                            title: '<b>Срок на годност</b>',
                            sortable: true,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'item_name',
                            title: '<b>Артикул</b>',
                            sortable: true,
                            width: 200,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'item_code',
                            title: '<b>Артикул код</b>',
                            sortable: true,
                            width: 200,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'measure_short_name',
                            title: '<b>Мярка</b>',
                            sortable: true,
                            width: 200,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'quantity',
                            title: '<b>Количество</b>',
                            sortable: true,
                            align: 'right',
                            width: 200,
                            formatter: function (val) {
                                return parseFloat(val).toFixed(3)
                            }
                        }
                        // ,{
                        //     field: "transaction_fields",
                        //     title: "<b>Допълнителни полета</b>",
                        //     width: 240,
                        //     formatter: formatWarehouseDynamicFields
                        // }
                    ]
                ]
            },
            {
                id: 7,
                iconCls: 'icon-datagrid',
                module: 'Plots',
                filters:['reports_filter_company_in', 'reports_filter_warehouses_and_items', 'reports_filter_note'],
                layout: '#report-simple-transactions-layout',
                name: 'report-own-plots-detailed',
                text: 'Минимални количества',
                grid: 'initItemsMinQuantitiesReportGrid',
                default_filter: ['report_date_as_of'],
                rpcParams: {
                    showFooter: false,
                    report: 'items-min-quantities-report',
                    parameters: {}
                },
                legendMsg: '<ul>' +
                    '<li>' +
                    '<div style="width: 30px; height: 10px; background-color: #fb9d37; float: left; margin-top: 4px;margin-right: 5px;">' +
                    '</div>Записите маркирани в този цвят показват, че количеството на даденият артикул е достигнал ' + CONSTANTS.MIN_QUANTITY_NOTIFICATION_PERCENT + '% от зададеният критичен минимум за този артикул.' +
                    '</li>' +
                    '<li>' +
                    '<div style="width: 30px; height: 10px; background-color: #FF0000; float: left; margin-top: 4px;margin-right: 5px;">' +
                    '</div>Записите маркирани в този цвят показват, че количеството на даденият артикул е под критичния минимум.' +
                    '</li>' +
                    '</ul>',
                columns: [
                    [
                        {
                            field: 'company_name',
                            title: '<b>Стопанство</b>',
                            sortable: false,
                            width: 200,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'warehouse_name',
                            title: '<b>Склад</b>',
                            sortable: true,
                            width: 200,
                            exportToXLS: true,
                            exportToPDF: true,
                        },
                        {
                            field: 'sub_warehouse_name',
                            title: '<b>Подсклад</b>',
                            sortable: false,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'item_name',
                            title: '<b>Артикул</b>',
                            sortable: true,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'code',
                            title: '<b>Артикул код</b>',
                            sortable: true,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'measure_short_name',
                            title: '<b>Мярка</b>',
                            sortable: true,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'quantity',
                            title: '<b>Количество</b>',
                            sortable: true,
                            align: 'right',
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                            formatter: function (val) {
                                return parseFloat(val).toFixed(3)
                            }
                        }, 
                        {
                            field: 'min_quantity',
                            title: '<b>Минимално количество</b>',
                            sortable: true,
                            align: 'right',
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                            formatter: function (val) {
                                return parseFloat(val).toFixed(3)
                            }
                        }
                        // , {
                        //     field: "fields",
                        //     title: "<b>Допълнителни полета</b>",
                        //     width: 240,
                        //     formatter: formatWarehouseDynamicFields
                        // }
                    ]
                ]
            },
            {
                id: 8,
                iconCls: 'icon-datagrid',
                module: 'Warehouse',
                filters:['reports_filter_document_dates', 'reports_filter_machines', 'reports_filter_warehouses_and_items' , 'reports_filter_items_code', 'reports_filter_note'],
                layout: '#report-simple-transactions-layout',
                name: 'asset-report',
                text: 'Отчет по активи',
                grid: 'initTransactionsReportGrid',
                rpcParams: {
                    showFooter: true,
                    report: 'machines-report',
                    criteries: {},
                    parameters: {}
                },
                onLoadSuccessCallback: function(data) {
                    var grid = jQuery('#simple-transactions-table');
                    jQuery.each(data.rows, function (i, row) {
                        grid.datagrid('beginEdit', i);

                        var machineFarmEditor = grid.datagrid('getEditor', {index: i, field: 'custom_field_farm_name'});
                        jQuery(machineFarmEditor.target).textbox('setValue', row.company_in_fields.farm.name);

                        var machineNumberEditor = grid.datagrid('getEditor', {index: i, field: 'custom_field_number'});
                        jQuery(machineNumberEditor.target).textbox('setValue', row.company_in_fields.number);

                        grid.datagrid('endEdit', i);
                    });

                    grid.datagrid('reloadFooter', [
                        {
                            measure_short_name: '<b>Кол. за стр.</b>',
                            quantity:parseFloat(data.amounts.pageQty).toFixed(3),
                            single_price_no_dds:'<b>С-т за стр.</b>',
                            value_price_no_dds: parseFloat(data.amounts.pagePriceNoDDS).toFixed(5),
                        },
                        {
                            measure_short_name:'<b>Общо кол.</b>',
                            quantity:parseFloat(data.amounts.totalQty).toFixed(3),
                            single_price_no_dds:'<b>Обща с-т</b>',
                            value_price_no_dds:parseFloat(data.amounts.totalPriceNoDDS).toFixed(5),
                        }
                    ]);

                    const mahineFilter= jQuery('#machine').combobox('getValue');

                    if(!mahineFilter){
                        jQuery('#machine').combobox('reload');
                    }

                },
                footers: [
                    {
                        measure_short_name: {
                            type: 'text',
                            value: "Общо количество"
                        },
                        quantity: {
                            type: 'value',
                            value: "pageQty"
                        },
                        single_price_no_dds: {
                            type: 'text',
                            value: "Обща стойност"
                        },
                        value_price_no_dds: {
                            type: 'value',
                            value: "pagePriceNoDDS"
                        },
                    },
                ],
                columns: [
                    [
                        {
                            title: '<b>Документ</b>',
                            colspan: 3,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            title: '<b>Артикул</b>',
                            colspan: 6,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            title: '<b>Актив</b>',
                            colspan: 4,
                            exportToXLS: true,
                            exportToPDF: false,
                        }
                    ],
                    [
                        {
                            field: 'document_number',
                            title: '<b>Номер</b>',
                            align: 'center',
                            sortable: false,
                            width: 100,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'document_date',
                            title: '<b>Дата</b>',
                            align: 'center',
                            sortable: false,
                            width: 100,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'warehouse_name',
                            title: '<b>Склад</b>',
                            align: 'center',
                            sortable: true,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'item_code',
                            title: '<b>Номер</b>',
                            align: 'center',
                            sortable: true,
                            width: 100,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'item_name',
                            title: '<b>Наименование</b>',
                            align: 'center',
                            sortable: true,
                            width: 300,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'measure_short_name',
                            title: '<b>Мярка</b>',
                            align: 'center',
                            sortable: true,
                            width: 100,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'quantity',
                            title: '<b>Количество</b>',
                            align: 'right',
                            sortable: true,
                            width: 100,
                            exportToXLS: true,
                            exportToPDF: true,
                            formatter: function (val) {
                                return Math.abs(val).toFixed(3)
                            }
                        }, 
                        {
                            field: 'single_price_no_dds',
                            title: '<b>Ед. цена</b>',
                            align: 'right',
                            sortable: true,
                            width: 100,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'value_price_no_dds',
                            title: '<b>Стойност</b>',
                            align: 'right',
                            sortable: true,
                            width: 100,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'company_in_name',
                            title: '<b>Наименование</b>',
                            sortable: true,
                            width: 200,
                            exportToXLS: true,
                            exportToPDF: false,
                        }, 
                        {
                            field: 'custom_field_farm_name',
                            title: '<b>Стопанство</b>',
                            sortable: true,
                            width: 200,
                            exportToXLS: true,
                            exportToPDF: false,
                            editor: {
                                type: 'textbox',
                                options: {
                                    editable: false,
                                }
                            }
                        }, 
                        {
                            field: 'custom_field_number',
                            title: '<b>Рег. номер</b>',
                            sortable: true,
                            width: 200,
                            exportToXLS: true,
                            exportToPDF: false,
                            editor: {
                                type: 'textbox',
                                options: {
                                    editable: false,
                                }
                            }
                        },
                        {
                            field: 'company_in_code',
                            title: '<b>Код</b>',
                            sortable: true,
                            width: 200,
                            exportToXLS: true,
                            exportToPDF: false,
                            editor: {
                                type: 'textbox',
                                options: {
                                    editable: false,
                                }
                            }
                        }
                    ]
                ]
            },
            {
                id: 1,
                iconCls: 'icon-datagrid',
                module: 'Warehouse',
                filters:['report_date_as_of', 'reports_filter_companies_group', 'reports_filter_warehouses_and_items', 'reports_filter_groups', 'reports_filter_note'],
                layout: '#report-simple-transactions-layout',
                name: 'report-articles-history',
                text: 'Отчет по артикули',
                grid: 'initArticlesReportGrid',
                rpcParams: {
                    report: 'articles-report',
                    showFooter: false,
                    criteries: {},
                    parameters: {}
                },
                columns: [
                    [
                        {
                            field: 'company_name',
                            title: '<b>Стопанство</b>',
                            sortable: false,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'warehouse_name',
                            title: '<b>Склад</b>',
                            sortable: true,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'item_name',
                            title: '<b>Артикул</b>',
                            sortable: true,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'batch',
                            title: '<b>Партида</b>',
                            sortable: true,
                            width: 100,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'item_code',
                            title: '<b>Артикул код</b>',
                            sortable: true,
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'measure_short_name',
                            title: '<b>Мярка</b>',
                            sortable: true,
                            width: 100,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'quantity',
                            title: '<b>Количество</b>',
                            sortable: true,
                            align: 'right',
                            width: 150,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'priceNoDDS',
                            title: '<b>Средно претеглена цена без ДДС</b>',
                            sortable: true,
                            align: 'right',
                            width: 220,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'priceWithDDS',
                            title: '<b>Средно претеглена цена c ДДС</b>',
                            sortable: true,
                            align: 'right',
                            width: 220,
                            exportToXLS: true,
                            exportToPDF: true,
                        }, 
                        {
                            field: 'article_history',
                            title: '<b>История</b>',
                            align: 'center',
                            width: 100,
                            exportToXLS: false,
                            exportToPDF: false,
                            formatter: drawButton
                        }
                    ]
                ]
            }
        ];
    }

    return {
        init,
        getInitData,
    };

});
