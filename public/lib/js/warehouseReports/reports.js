define([
    "easyui",
    "js/main/EasyUIRPCLoaders",
    "js/warehouse/constants",
    "js/warehouse/history"
], function(
    _,
    EasyUIRPCLoaders,
    CONSTANTS,
    historyLog
) {

    function init() {}
    
    function initReportsDataGrid(params, columns, onLoadSuccessCallback = null) {
        jQuery('#simple-transactions-table').datagrid({
            iconCls: 'icon-edit-geometry',
            nowrap: true,
            autoRowHeight: true,
            striped: true,
            pageSize: 30,
            fit: true,
            showFooter: params.showFooter === true,
            url: "index.php?warehouse-rpc=warehouse-transactions",
            rpcMethod: "getReports",
            idField: '',
            border: false,
            rpcParams: [params],
            columns: columns,
            pagination: true,
            rownumbers: true,
            toolbar: '#reports_toolbar',
            onLoadSuccess: function (data) {
                if(typeof onLoadSuccessCallback === 'function') {
                    onLoadSuccessCallback(data);
                }

                if (params.report === 'articles-report') {
                    historyLog.bindClickEvents(CONSTANTS.HISTORY_CLASS_ARTICLE);
                }

                jQuery(`#filter-report`).linkbutton('enable');
                jQuery(`#clear-filter-report`).linkbutton('enable');
            },
            onBeforeLoad: function() {
                jQuery(this).datagrid('clearChecked');
                jQuery(`#filter-report`).linkbutton('disable');
                jQuery(`#clear-filter-report`).linkbutton('disable');
            },
            rowStyler: function (index, row) {
                if(jQuery.isEmptyObject(row)) return;
                let style = [];
                if (row.transaction_status === 0) {
                    style.push('text-decoration: line-through');
                    style.push('color: #aaa');
                }
                
                if(params.report === 'items-min-quantities-report'){
                    if(row.quantity <= row.min_quantity){
                        style.push('background-color:#FF0000');
                        return style.join(';');
                    }
                    
                    var notificationValue = parseFloat(row.min_quantity) + ((row.min_quantity / 100) * CONSTANTS.MIN_QUANTITY_NOTIFICATION_PERCENT);
                    if(row.quantity <= notificationValue) {
                        style.push('background-color:#fb9d37');
                        return style.join(';');
                    }
                } else if(params.report === 'items-expiry-date-report'){
                    var today =  new Date();
                    var expiryDateArr = row.expiry_date.split(".")
                    var expiryDate = new Date(parseInt(expiryDateArr[2]), parseInt(expiryDateArr[1]) -1, parseInt(expiryDateArr[0]));

                    if(today.getTime() >= expiryDate.getTime()){
                        style.push('background-color:#FF0000');
                        return style.join(';');
                    }

                    var diffTime = expiryDate.getTime() - today.getTime();
                    if(diffTime / (1000 * 3600 * 24) <= CONSTANTS.EXPIRY_DATE_NOTIFICATION_DAYS) {
                        style.push('background-color:#fb9d37');
                        return style.join(';');
                    }
                }
                
                return style.join(';');

            },
            onClickRow: function(index,row) {
                var message = 'Справката се отнася за артикул от: ';
                if ( row.warehouses_path !== undefined) {
                    for (var i = 0; i < row.warehouses_path.length; i++) {
                        message += 'подсклад на <b>' + row.warehouses_path[i] + '</b>, ';
                    }
                }
                
                // addFooterMessage(message);
            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }

    function addLegendButton(msg) {
        jQuery("#legend-report").hide();
        jQuery("#legend-report").off("click");

        if(msg !== undefined && msg !== "") {
            jQuery("#legend-report").show();
            jQuery("#legend-report").on("click", function () {
                jQuery.messager.alert({
                        title: 'Легенда',
                        width: 700,
                        msg: msg
                    }
                );
            });
        }
    }

    function addFooterMessage(message) {
        jQuery('#report-simple-transactions-layout').layout('add',{
            region: 'south',
            height: 30,
            noheader: true,
            content: message,
            bodyCls: 'datagrid-footer-padding',
            border: false,
            style: { 'border-top': '1px solid #000', 'padding-top': '5px'}
        });
    }

    return {
        init,
        initReportsDataGrid,
        addLegendButton
    };

});
