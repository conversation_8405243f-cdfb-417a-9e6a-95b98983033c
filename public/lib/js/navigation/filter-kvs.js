'use strict';

function initKvsFilters(){
    TF.Rpc.Common.CombinedComboboxData.read(false, [
            'EkateCombobox',
            'PlotCategoryCombobox',
            'PlotNTPCombobox',
            'MestnostCombobox',
            'IrrigatedAreaCombobox'
        ]
    ).done(function (data) {
        initFilterCtrl(data);
        jQuery('#kvs-plots-tables').datagrid({
            url: 'index.php?diary-rpc=kvs-diary-grid',
            rpcParams: [getFiltersValues()],
            iconCls: 'icon-plot',
            pageSize: 10,
            pagination: true,
            striped: true,
            sortName: 'gid',
            sortOrder: 'asc',
            idField: 'gid',
            singleSelect: true,
            fitColumns: false,
            fit:true,
            border: true,
            nowrap: true,
            rownumbers: true,
            frozenColumns: [
                [{
                    field: 'ck',
                    checkbox: true
                }]
            ],
            columns: [
                [{
                    field: 'ekate',
                    title: '<b>EKATTE</b>',
                    sortable: true,
                    width: "7.23%"
                }, {
                    field: 'masiv',
                    title: '<b>Масив</b>',
                    sortable: true,
                    width: "6.02%"
                }  , {
                    field: 'number',
                    title: '<b>Имот</b>',
                    sortable: true,
                    width: "6.02%"
                }, {
                    field: 'mestnost',
                    title: '<b>Местност</b>',
                    sortable: true,
                    width: "12.05%"
                }, {
                    field: 'category',
                    title: '<b>Категория</b>',
                    sortable: true,
                    width: "12.05%"
                }, {
                    field: 'area_type',
                    title: '<b>НТП</b>',
                    sortable: true,
                    width: "18.07%"
                }, {
                    field: 'used_area',
                    title: '<b>Използвана<br/>площ (дка)</b>',
                    sortable: true,
                    align: 'center',
                    width: "9.64%"
                }, {
                    field: 'area_kvs',
                    title: '<b>Обща<br/>площ (дка)</b>',
                    sortable: true,
                    align: 'center',
                    width: "9.64%"
                }, {
                    field: 'document_area',
                    title: '<b>Площ по<br/>док. (дка)</b>',
                    sortable: true,
                    align: 'center',
                    width: "9.64%"
                }, {
                    field: 'irrigated_area',
                    title: '<b>Поливна<br/>площ (дка)</b>',
                    sortable: true,
                    align: 'center',
                    width: "9.64%",
                    formatter: function (value) {
                        return (value == true) ? 'Да' : 'Не';
                    }
                }, {
                    field: 'allowable_area',
                    title: '<b>Площ по<br/>сечение (дка)</b>',
                    sortable: true,
                    align: 'center',
                    width: "12.05%"
                },{
                    field: 'allow_prec',
                    title: '<b> Процент </b>',
                    sortable: true,
                    align: 'center',
                    width: "12.05%"
                }
                ]
            ],
            onLoadSuccess: function (data) {

                if(data.extent){
                    var bounds = new OpenLayers.Bounds.fromString(data.extent).transform(new OpenLayers.Projection("EPSG:32635"), map.getProjectionObject());
                    var center = bounds.getCenterLonLat();
                    var lonLat = new OpenLayers.LonLat(center.lon + Math.abs( (bounds.left-bounds.right) ), center.lat);
                    map.zoomToExtent(bounds);
                    map.panTo(lonLat);
                }
                map.updateSize();
            },
            onSelect: function (index, node) {
                if (!node.geom) return;
                var kadIdent = node.ekate + '.' + node.masiv + '.' + node.number;
                displayFeatureSelection(node.geom, kadIdent);
            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }).fail(function (error) {
        if(error)jQuery.messager.alert('Грешка', error.getMessage(), 'warning');
    });
    jQuery('#btn-filter-kvs-search').bind('click', function () {
        jQuery('#kvs-plots-tables').datagrid('options').rpcParams[0] = getFiltersValues();
        jQuery('#kvs-plots-tables').datagrid('reload');
    });
}


/**
 * @returns array of objects
 */
function getFiltersControls() {
    return [
        {id: 'search-kad-ident', type: null, field: 'kad_ident', data: ''},
        {
            id: 'search-ekatte',
            type: 'combobox',
            field: 'ekate',
            data: 'EkateCombobox',
            valueField: 'ekate',
            textField: 'text'
        },
        {id: 'search-masiv', type: null, field: 'masiv', data: ''},
        {id: 'search-number', type: null, field: 'number', data: ''},
        {
            id: 'search-category',
            type: 'combobox',
            field: 'category',
            data: 'PlotCategoryCombobox',
            valueField: 'id',
            textField: 'name'
        },
        {
            id: 'search-area-type',
            type: 'combobox',
            field: 'area_type',
            data: 'PlotNTPCombobox',
            valueField: 'id',
            textField: 'name'
        },
        {
            id: 'search-mestnost',
            type: 'combobox',
            field: 'mestnost',
            data: 'MestnostCombobox',
            valueField: 'mestnost',
            textField: 'text'
        },
        {
            id: 'search-irrigated-area',
            type: 'combobox',
            field: 'irrigated_area',
            data: 'IrrigatedAreaCombobox',
            valueField: 'value',
            textField: 'label',
            multiple: false
        },
        {
            id: 'search-participation',
            type: 'combobox',
            field: 'participation',
            valueField: 'value',
            textField: 'text',
            data: [
                {"value": '', "text": "Всички", "selected": true},
                {"value": 'without', "text": "Без зададено желание"},
                {"value": 'participate', "text": "Да"},
                {"value": 'no_participate', "text": "Не"}
            ],
            multiple: false
        }
    ];
}

/**
 * @returns object with db fields and values from controls
 */
function getFiltersValues() {
    var controls = getFiltersControls();
    var obj = {};
    for (var i = 0; i < controls.length; i++) {
        var ctrl = controls[i];
        ctrl.id = '#' + ctrl.id;
        if (ctrl.type == null) {
            obj[ctrl.field] = jQuery(ctrl.id).val();
        }
        if (ctrl.type == 'combobox') {
            var value = null;
            if (ctrl.multiple == null) {
                value = jQuery(ctrl.id)[ctrl.type]('getValues');
                obj[ctrl.field] = value;
            } else {
                value = jQuery(ctrl.id)[ctrl.type]('getValue');
                obj[ctrl.field] = value;
            }
        }
    }
    return obj;
}
/**
 * @param data
 */
function initFilterCtrl(data) {
    var controls = getFiltersControls();
    for (var i = 0; i < controls.length; i++) {
        var ctrl = controls[i];
        ctrl.id = '#' + ctrl.id;
        if (ctrl.type != 'combobox') continue;
        if (typeof ctrl.data == 'string' && data[ctrl.data] != undefined) {
            ctrl.data = ctrl.data = data[ctrl.data];
        }
        // settings default
        if (!ctrl.valueField) ctrl.valueField = 'id';
        if (!ctrl.textField) ctrl.textField = 'name';
        if (ctrl.multiple == undefined) ctrl.multiple = true;
        // init Easy UI controls
        jQuery(ctrl.id)[ctrl.type]({
            data: ctrl.data,
            editable: false,
            multiple: ctrl.multiple,
            valueField: ctrl.valueField,
            textField: ctrl.textField,
            onSelect: function (row) {
                var isCombobox = jQuery(this).data().hasOwnProperty('combobox');
                if (!isCombobox) return;
                var valueField = jQuery(this).combobox('options').valueField;
                var selectedValue = row[valueField];
                var data = jQuery(this).combobox('getValues');
                if (data.length == 1) return row;
                if (data.indexOf('') != -1 || data.indexOf('-1') != -1) {
                    for (var i = 0; i < data.length; i++) {
                        if (data[i] === '' || data[i] === '-1') {
                            if (selectedValue !== data[i]) {
                                jQuery(this).combobox('unselect', data[i]);
                            }
                            continue;
                        }
                        if (selectedValue !== data[i]) {
                            jQuery(this).combobox('unselect', data[i]);
                        }
                    }
                }
            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }
    jQuery('#search-irrigated-area').combobox('select','all');
}

function clearFiltersValues() {
    var controls = getFiltersControls();
    for (var i = 0; i < controls.length; i++) {
        var ctrl = controls[i];
        ctrl.id = '#' + ctrl.id;
        if (ctrl.type == null) jQuery(ctrl.id).val('');
        if (ctrl.type == 'combobox') jQuery(ctrl.id)[ctrl.type]('clear');
        if (ctrl.id == '#search-irrigated-area') {
            jQuery(ctrl.id)[ctrl.type]('select','all');
        }
    }
    jQuery('#btn-filter-kvs-search').click();
}
