(function(root, factory) {
    if (typeof define === "function" && define.amd) {
        // AMD. Register as an anonymous module.
        define(["jquery", "OpenLayers", "TF/Rpc/Diary/DiaryMap"], factory);
    } else {
        // Browser globals (root is window)
        root.ab_lines = factory(jQuery, OpenLayers, TF.Rpc.Diary.DiaryMap);
    }
})(typeof self !== "undefined" ? self : this, function(
    jQuery,
    OpenLayers,
    DiaryMap
) {
    const SHIFT_DIRECTIONS = [
        {
            text: "Дясно",
            value: "right",
            selected: true
        },
        {
            text: "Ляво",
            value: "left"
        }
    ];
    const SHIFT_DIR = {
        RIGHT: "right",
        LEFT: "left"
    };

    function init(params) {
        var winAblineExportTfconnect = jQuery("#win-abline-export-tfconnect");

        jQuery("#menu-exort-abline-agdata").bind("click", function(e) {
            e.preventDefault();
            _exportABLine("TrimbleAgData");
        });

        jQuery("#menu-exort-abline-aggps").bind("click", function(e) {
            e.preventDefault();
            _exportABLine("TrimbleAgGPS");
        });

        jQuery("#menu-exort-abline-tfconnect").bind("click", function(e) {
            winAblineExportTfconnect
                .find(".js-abline-tfconnect-list")
                .combobox({
                    url: "index.php?common-rpc=modems-combobox",
                    valueField: "value",
                    textField: "name",
                    loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                    loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
                    onLoadSuccess: function(data) {
                        winAblineExportTfconnect.window("open");
                    }
                });
        });

        jQuery("#exort-abline-tfconnect").bind("click", _onTFConnectExport);

        var winAblineDownload = jQuery("#win-abline-download");
        jQuery("#btn-abline-download-file-close").bind("click", function() {
            winAblineDownload.window("close");
        });
        jQuery("#btn-abline-download-file").bind("click", function() {
            winAblineDownload.window("close");
        });
        jQuery("#tool-abline-add").bind("click", _onABLineAdd);

        jQuery("#tool-abline-del").bind("click", _onABlineDelete);
        jQuery("#tool-abline-save").bind("click", function(e) {
            var options = jQuery(this).linkbutton("options");
            if (options.disabled) {
                return;
            }
            jQuery("#win-abline-save").window("open");
        });
        jQuery("#tool-abline-projection").bind("click", _onABLineShowProjection);


        jQuery("#tool-abline-shift").bind("click", function(e) {
            var options = jQuery(this).linkbutton("options");
            if (options.disabled) {
                return;
            }
            jQuery("#win-abline-shift").window("open");
        });
        jQuery("#win-abline-shift").window({
            onBeforeOpen: function(params) {
                jQuery(this)
                    .find(".js-abline-direction")
                    .combobox("select", "right");
            },
            onClose: function(params) {
                _resetABLineForm(jQuery(this));
                getAbLinesVectorLayer().removeAllFeatures();
            }
        });
        jQuery("#win-abline-save").window({
            onClose: function() {
                _resetABLineForm(jQuery(this));
            }
        });

        jQuery("#win-abline-shift .js-abline-offset").numberbox({
            onChange: _onShiftOffsetChange
        });
        jQuery("#win-abline-shift .js-abline-direction").combobox({
            onChange: _onShiftDirectionChange
        });
        jQuery("#win-abline-save,  #win-abline-shift")
            .find(".js-abline-direction")
            .combobox({
                data: SHIFT_DIRECTIONS
            });

        jQuery("#btn-abline-save").bind("click", _onABLineSave);
        jQuery("#btn-abline-shift-save").bind("click", _onABLineShiftSave);

        jQuery("#btn-abline-cancel").bind("click", function(e) {
            e.preventDefault();
            jQuery("#win-abline-save").window("close");
        });

        jQuery("#btn-abline-tfconnect-cancel").bind("click", function(e) {
            e.preventDefault();
            jQuery("#win-abline-export-tfconnect").window("close");
        });

        jQuery("#btn-abline-shift-cancel").bind("click", function(e) {
            e.preventDefault();
            var win = jQuery("#win-abline-shift");
            win.window("close");
            getAbLinesVectorLayer().removeAllFeatures();
        });

        jQuery("#tool-abline-copy").bind("click", openAbLineCopyWin);
        jQuery("#btn-load-farming-plots").bind("click", copyAbLineToPlot);
    }

    function _onTFConnectExport(e) {
        e.preventDefault();
        var winAblineExportTfconnect = jQuery(
            "#win-abline-export-tfconnect"
        );
        var selNavEl = winAblineExportTfconnect.find(
            '.js-navigations-list input[name="navigation"]:checked'
        );
        var plugin = selNavEl.val();
        var tfcDeviceId = winAblineExportTfconnect
            .find(".js-abline-tfconnect-list")
            .combobox("getValue");
        if (!tfcDeviceId) {
            jQuery.messager.alert(
                "Грешка",
                "Моля изберете TF Connect устройство.",
                "error"
            );
            return;
        }
        _exportABLineTfConnect(plugin, tfcDeviceId);
    }

    function _exportABLine(plugin) {
        var parents = getTreeParentElements("#zp-tree");
        if (parents.length === 0) return;
        var data;

        try {
            data = _prepareExportAbLinesData(parents, plugin);
        } catch (e) {
            jQuery.messager.alert("Грешка", e.message);
            return;
        }

        TF.Rpc.Diary.DiaryMap.exportAbLines(data)
            .done(function(zipURL) {
                jQuery("#btn-abline-download-file").attr("href", zipURL);
                jQuery("#win-abline-download").window("open");
            })
            .fail(function(errorObj) {
                jQuery.messager.alert("Грешка", errorObj.getMessage());
            });
    }
    
    function _exportABLineTfConnect(plugin, tfcDeviceId) {
        var parents = getTreeParentElements("#zp-tree");
        if (parents.length === 0) return;
        var data;
        try {
            data = _prepareExportAbLinesData(parents, plugin);
        } catch (e) {
            jQuery.messager.alert("Грешка", e.message);
            return;
        }
        data.device_id = tfcDeviceId;
        TF.Rpc.Diary.DiaryMap.exportAbLinesTfConnect(data)
            .done(function(zipURL) {
                jQuery("#win-abline-export-tfconnect").window("close");
                jQuery.messager.alert(
                    "Информация",
                    "Експортът беше завършен успешно!",
                    "info"
                );
            })
            .fail(function(errorObj) {
                jQuery.messager.alert("Грешка", errorObj.getMessage());
            });
    }
    
    function _onShiftDirectionChange(direction) {
        var shift = jQuery("#win-abline-shift .js-abline-offset").numberbox(
            "getValue"
        );
        var node = jQuery("#zp-tree").tree("getSelected");
        var win = jQuery("#win-abline-shift");
        var isVisible = !win.window("options").closed;
        if (!isVisible) {
            return;
        }
        if (!node || node.hasOwnProperty("children")) {
            jQuery.messager.alert(
                "Грешка",
                "Моля селектрирайте AB линия",
                "error"
            );
            return;
        }
        var ABLineGeom = node.attributes.geometry;
        shift = _calcABLineShift(direction, shift);
        _shiftAbLine(ABLineGeom, shift);
    }

    function _onShiftOffsetChange(shift) {
        shift = parseFloat(shift);
        var node = jQuery("#zp-tree").tree("getSelected");
        var win = jQuery("#win-abline-shift");
        var isVisible = !win.window("options").closed;

        if (!isVisible) {
            return;
        }
        if (!node || node.hasOwnProperty("children")) {
            jQuery.messager.alert(
                "Грешка",
                "Моля селектрирайте AB линия",
                "error"
            );
            return;
        }
        var direction = jQuery(
            "#win-abline-shift .js-abline-direction"
        ).combobox("getValue");
        var ABLineGeom = node.attributes.geometry;
        shift = _calcABLineShift(direction, shift);
        _shiftAbLine(ABLineGeom, shift);
    }

    function _onABlineDelete() {
        var parents = getTreeParentElements("#zp-tree");
        if (parents.length === 0) return;
        jQuery.messager.confirm(
            "Потвърждение",
            "Ще бъдат изтрити всички линии, пред които има поставена отметка. Сигурни ли сте, че искате да продължите?",
            function(r) {
                if (r) {
                    _deleteAbLines(parents);
                }
            }
        );
    }
    
    function _calcABLineShift(direction, shift) {
        if (direction === SHIFT_DIR.RIGHT) {
            shift *= -1;
        }
        if (direction === SHIFT_DIR.LEFT) {
            shift = Math.abs(shift);
        }
        return shift;
    }

    function _shiftAbLine(geometry, shift) {
        var selectedABLine = jQuery("#zp-tree").tree("getSelected");
        var selectedPlot = jQuery("#zp-tree").tree(
            "getParent",
            selectedABLine.target
        );
        var projectionArr = [];
        var extent = selectedPlot.attributes.extent.split(",");
        var biggestPlotSide = Math.max(extent[2]-extent[0], extent[3]-extent[1]);
        var multiply = parseInt(biggestPlotSide/Math.abs(shift));
        var reader = new jsts.io.GeoJSONReader();
        var parser = new jsts.io.OpenLayersParser();
        var jstsGeom = reader.read(geometry);
        var jstsPlotGeom = reader.read(selectedPlot.attributes.geometry).buffer(0);
        var extendedLine = extendLineString(jstsGeom.getCoordinates()[0], jstsGeom.getCoordinates()[1], biggestPlotSide/2);
        var lineCoordinates = extendedLine.getCoordinates();
        var lineFactory = extendedLine.getFactory();
        var bufParams = new jsts.operation.buffer.BufferParameters();
        var ocb = new jsts.operation.buffer.OffsetCurveBuilder(
            lineFactory.getPrecisionModel(),
            bufParams
        );  
        var pts = ocb.getOffsetCurve(lineCoordinates, shift);           
        var shiftedABLine = extendedLine.getFactory().createLineString(pts);
        var intersection = jstsPlotGeom.intersection(shiftedABLine);
        var feature = new OpenLayers.Feature.Vector(
            parser.write(intersection),
            {}
        );
        
        feature.geometry.transform("EPSG:32635", "EPSG:900913");

        for (var i = 1; i <= multiply; i++) {
            var currentOffset = Math.abs(shift)*i;
            var ptsRight = ocb.getOffsetCurve(lineCoordinates, -currentOffset);
            var ptsLeft = ocb.getOffsetCurve(lineCoordinates, currentOffset);
            var shiftedRightABLine = lineFactory.createLineString(ptsRight);
            var shiftedLeftABLine = lineFactory.createLineString(ptsLeft);
            var intersectionRight = jstsPlotGeom.intersection(shiftedRightABLine)
            var intersectionLeft = jstsPlotGeom.intersection(shiftedLeftABLine)
            var featureRight = new OpenLayers.Feature.Vector(
                parser.write(intersectionRight),
                {}
            );
            var featureLeft = new OpenLayers.Feature.Vector(
                parser.write(intersectionLeft),
                {}
            );

            featureRight.geometry.transform("EPSG:32635", "EPSG:900913");
            featureLeft.geometry.transform("EPSG:32635", "EPSG:900913");
            projectionArr.push(featureRight, featureLeft)
        }

        
        var abLineVectorLayer = getAbLinesVectorLayer();
        abLineVectorLayer.removeAllFeatures();
        abLineVectorLayer.addFeatures([feature]);
        abLineVectorLayer.addFeatures(projectionArr);
    }

    function _onABLineShowProjection() {
        var options = jQuery('#tool-abline-projection').linkbutton('options');

        if (options.disabled) {
            return;
        }

        var selectedABLine = jQuery("#zp-tree").tree("getSelected");
        
        if (!selectedABLine || selectedABLine.hasOwnProperty("children")) {
            jQuery.messager.alert(
                "Грешка",
                "Моля, селектрирайте AB линия",
                "error"
            );
            return;
        }

        var shift = selectedABLine.attributes.offset;
        var direction = selectedABLine.attributes.direction;

        if(!shift) {
            jQuery.messager.alert(
                "Грешка",
                "Линията не може да бъде проектирана.",
                "error"
            );
            return;
        }
   
        unselectAll();
        jQuery(this).linkbutton("select");
        shift = _calcABLineShift(direction, shift);  
        _shiftAbLine(selectedABLine.attributes.geometry, shift);
    }

    function _resetABLineForm(win) {
        win.find(".fields-list .textbox-f").textbox("reset");
    }

    function _onABLineShiftSave(e) {
        e.preventDefault();
        var win = jQuery("#win-abline-shift");
        var offsetEl = win.find(".js-abline-offset");
        var directionEl = win.find(".js-abline-direction");
        var zpComboTree = jQuery("#zp-comboTree");
        var zpTree = jQuery("#zp-tree");
        var abLineWMSLayer = getAbLinesWMSLayer();
        var selectedLayer = zpComboTree.combotree("tree").tree("getSelected");
        if (!selectedLayer) {
            jQuery.messager.alert(
                "Грешка",
                "Трябва да изберете стопанска година.",
                "error"
            );
            return;
        }

        var selectedABLine = zpTree.tree("getSelected");
        if (!selectedABLine || selectedABLine.hasOwnProperty("children")) {
            jQuery.messager.alert("Грешка", "Не е избрана AB линия.", "error");
            return;
        }
        var hasOffset = offsetEl.numberbox("isValid");
        if (!hasOffset) {
            jQuery.messager.alert(
                "Грешка",
                "Не е въведено отместване!",
                "error"
            );
            return;
        }

        var wkt = _getABLineWKT();
        var layerId = selectedLayer.attributes.id;
        var gid = selectedABLine.attributes.plot_id;
        var dirText = directionEl.combobox("getText");
        var offset = offsetEl.numberbox("getValue");
        var name = `${selectedABLine.text} ${dirText[0]}${offset}`;

        DiaryMap.saveAbLine({
            gid,
            name,
            offset: offsetEl.numberbox("getValue"),
            direction: directionEl.combobox("getValue"),
            zp_layer_id: layerId,
            geom: wkt
        }).then(function() {
            getAbLinesVectorLayer().removeAllFeatures();
            win.window("close");
            _resetABLineForm(jQuery("#win-abline-shift"));
            zpTree.tree("reload");
            abLineWMSLayer.redraw(true);
        });
    }
    
    function _onABLineSave(e) {
        e.preventDefault();
        var zpTree = jQuery("#zp-tree");
        var selectedItem = zpTree.tree("getSelected");
        if (selectedItem.attributes.node_type === 'ab_line') {
            _onABLineSaveEdit(selectedItem);
            return
        }

        _onABLineSaveCreate(selectedItem);
    }

    function _onABLineSaveEdit(selectedABLine) {
        var zpTree = jQuery("#zp-tree");
        var abLineAttr = selectedABLine.attributes;
        var win = jQuery("#win-abline-save");
        var nameInput = win.find(".js-abline-name");
        var newName = nameInput.textbox('getValue');

        if (!nameInput.textbox('isValid')) {
            return;
        }
        win.window("close");
        jQuery("#btn-edit-abline-event").linkbutton("disable");

        DiaryMap.editAbLine({
            gid: abLineAttr.id,
            name: newName
        }).then(function () {
            zpTree.tree("reload");
        });
    }

    function _onABLineSaveCreate(selectedPlot) {
        var win = jQuery("#win-abline-save");
        var nameEl = win.find(".js-abline-name");

        var zpComboTree = jQuery("#zp-comboTree");
        var zpTree = jQuery("#zp-tree");
        var abLineWMSLayer = getAbLinesWMSLayer();

        var selectedLayer = zpComboTree.combotree("tree").tree("getSelected");
        if (!selectedLayer) {
            jQuery.messager.alert(
                "Грешка",
                "Трябва да изберете стопанска година.",
                "error"
            );
            return;
        }
        var selectedPlot = zpTree.tree("getSelected");
        if (!selectedPlot) {
            jQuery.messager.alert("Грешка", "Не е избран парцел.", "error");
            return;
        }

        var wkt = _getABLineWKT();
        var layerId = selectedLayer.attributes.id;
        var gid = selectedPlot.id;

        if (!nameEl.textbox("isValid")) {
            jQuery.messager.alert(
                "Грешка",
                "Моля въведете име за линията.",
                "error"
            );
            return;
        }

        DiaryMap.saveAbLine({
            gid,
            name: nameEl.textbox("getValue"),
            zp_layer_id: layerId,
            geom: wkt
        }).then(function() {
            getAbLinesVectorLayer().removeAllFeatures();
            win.window("close");
            zpTree.tree("reload");
            abLineWMSLayer.redraw(true);
        });
    }

    /**
     * Returns the WKT form of the first drawn line in the AB line vector layer.
     * @returns {string}
     */
    function _getABLineWKT() {
        var abLineVectorLayer = getAbLinesVectorLayer();
        var abLineWMSLayer = getAbLinesWMSLayer();

        if (!abLineVectorLayer.features.length) {
            jQuery.messager.alert("Грешка", "Не е избрана AB линия.", "error");
            return;
        }
        var geom = abLineVectorLayer.features[0];
        var wktFormat = new OpenLayers.Format.WKT({
            internalProjection: map.baseLayer.projection,
            externalProjection: new OpenLayers.Projection("EPSG:32635")
        });
        return wktFormat.write(geom);
    }
    
    function _onABLineAdd() {
        var options = jQuery(this).linkbutton("options");
        if (options.disabled) {
            return false;
        }
        var isParent = true;
        var selectedPlot = jQuery("#zp-tree").tree("getSelected");
        if (selectedPlot)
            isParent = jQuery("#zp-tree").tree(
                "getParent",
                selectedPlot.target
            );
        if (!selectedPlot || isParent) {
            jQuery.messager.alert("Грешка", "Не е избран парцел.", "error");
            return;
        }
        var layer = getAbLinesVectorLayer();
        layer.removeAllFeatures();
        snappingControl();
        chooseControl("drawABLine");
        unselectAll();
        jQuery(this).linkbutton("select");
    }

    function createABLineControll() {
        var layer = getAbLinesVectorLayer();
        var addABLineBtn = jQuery("#tool-abline-add");
        var saveABLineBtn = jQuery("#tool-abline-save");
        var control = new OpenLayers.Control.DrawFeature(
            layer,
            OpenLayers.Handler.Path,
            {
                eventListeners: {
                    featureadded: function(event) {
                        control.deactivate();
                        addABLineBtn.linkbutton("unselect");
                        saveABLineBtn.linkbutton({ disabled: false });

                        makeStartEndAbLineFeatures(getAbLinesVectorLayer(),event.feature);
                    },
                }
            }
        );
        control.handler.maxVertices = 2;

        return control;
    }
    
    /**
     * Configure the snapping agent
     */
    function snappingControl() {
        const abLinesLayer = getAbLinesVectorLayer();
        const plotsLayer = getPlotsLayer();

        const snap = new OpenLayers.Control.Snapping({
            layer: abLinesLayer,
            targets: [{
                layer: plotsLayer,
                tolerance: 8
            }],
            greedy: false
        });
        snap.activate();
    }
    
    function getPlotsLayer() {
        var layers = map.getLayersByName("Очертания");
        return layers[0];
    }
    
    function getAbLinesVectorLayer() {
        var layers = map.getLayersByName("ABLines");
        return layers[0];
    }

    function getAbLinesWMSLayer() {
        var layers = map.getLayersByName("ab_lines");
        return layers[0];
    }

    function getTreeParentElements(treeId) {
        var zpTree = jQuery(treeId);
        var checked = zpTree.tree("getChecked");
        var parents = [];

        //Iterate around all checked elements and get only parents because they contain all needed info
        for (var i = 0; i < checked.length; i++) {
            var parent = zpTree.tree("getParent", checked[i].target);
            if (parent) {
                if (parents.indexOf(parent) === -1) parents.push(parent);
            }
        }

        //Return an error if there is no checked children (AB lines) element or there is no checked any elements
        if (parents.length === 0 || checked.length === 0)
            jQuery.messager.alert(
                "Грешка",
                "Трябва да изберете поне една AB линия"
            );

        return parents;
    }

    function initABLinesLayer(params) {
        var layer = new OpenLayers.Layer.Vector("ABLines", {
            styleMap: new OpenLayers.StyleMap(
                new OpenLayers.Style({
                    strokeColor: "#ff0000",
                    strokeWidth: 3,
                    strokeOpacity: 0.5,
                    pointRadius: 2,
                    label: "${getLabel}",
                    fontColor: "#fff",
                    fontSize: "20px",
                    fontWeight: "bold",
                },
                {
                    context: {
                        getLabel: function(feature) {
                            if (feature.attributes.type === "start abLine") {
                                return "A"; 
                            } else if (feature.attributes.type === "end abLine") {
                                return "B"; 
                            }
                            return "";
                        }
                    }
                })
            ),
            renderers: OpenLayers.Layer.Vector.prototype.renderers
        });

        map.addLayer(layer);
        return layer;
    }

    /**
     *
     * @param plotsEl
     * @param plugin
     * @returns {{output_device: *, plots: []}}
     * @private
     */
    function _prepareExportAbLinesData(plotsEl, plugin) {
        const zpComboTree = jQuery("#zp-comboTree");
        const selectedLayer = zpComboTree.combotree("tree").tree("getSelected");

        const tableName = selectedLayer.attributes.table;
        const farmNode =  zpComboTree.combotree("tree").tree('getParent', selectedLayer.target);
        const farmName  = farmNode.text;

        var data = {
            output_device: plugin,
            tablename: tableName,
            layer_name: tableName,
            farm_name: farmName,
            id_array: [],
            ab_lines_id_array: [],
            plots: [],
        };

        for (var plotEl of plotsEl) {
            if(!plotEl.attributes.area_name) {
                throw new Error("Има парцели без въведени имена. Моля въведете имена на парцелите в модул Карта чрез редакция на парцел.");
            }
            var p = {
                id: plotEl.id,
                name: plotEl.attributes.area_name,
                geom: _transformGeom(
                    JSON.parse(plotEl.attributes.geometry),
                    "EPSG:32635",
                    "EPSG:4326",
                    OpenLayers.Format.GeoJSON
                ),
                children: []
            };
            for (var abLineEl of plotEl.children) {
                if (
                    abLineEl._checked !== undefined &&
                    abLineEl._checked === true
                ) {
                    p.children.push({
                        id: abLineEl.attributes.id,
                        name: abLineEl.text,
                        gid: abLineEl.attributes.gid,
                        geom: _transformGeom(
                            abLineEl.attributes.geometry,
                            "EPSG:32635",
                            "EPSG:4326",
                            OpenLayers.Format.GeoJSON
                        ),
                        offset: abLineEl.attributes.offset
                    });
                    data.ab_lines_id_array.push(abLineEl.attributes.id);
                }
            }
            data.plots.push(p);
            data.id_array.push(p.id);
        }
        return data;
    }
    /**
     * Transforms a geom from one projection to another and returns a `WKT`.
     *
     * @param {string} geom A WKT string.
     * @param {string} source An EPSG code.
     * @param {string} dest An EPSG code.
     * @param {type} formatter A type from `OpenLayers.Format.*`
     *
     * @returns {string}
     */
    function _transformGeom(geom, source, dest, formatter) {
        var reader = new formatter();
        var geometry = reader.parseGeometry(geom);
        var geomTransformed = geometry.transform(
            new OpenLayers.Projection(source),
            new OpenLayers.Projection(dest)
        );

        return reader.write(geomTransformed);
    }

    function _deleteAbLines() {
        var zpTree = jQuery("#zp-tree");
        var checked = zpTree.tree("getChecked");
        var abLineWMSLayer = ab_lines.getAbLinesWMSLayer();
        var ablinesIds = checked
            .filter(function(node) {
                var parent = zpTree.tree("getParent", node.target);
                return parent !== null;
            })
            .map(function(ablineEl) {
                return ablineEl.attributes.id;
            });

        DiaryMap.delAbLine(ablinesIds)
            .done(function(dataObj) {
                for (var id of ablinesIds) {
                    removeFeaturesByIndex(id);
                }
                zpTree.tree("reload");
                abLineWMSLayer.redraw(true);
            })
            .fail(function(errorObj) {
                jQuery.messager.alert("Грешка", errorObj.getMessage(), "error");
            });
    }

    function openAbLineCopyWin() {
        var options = jQuery(this).linkbutton("options");
        if (options.disabled) {
            return;
        }

        jQuery('#win-abline-layer-copy').window('open')
            .window('center');

        initAbLineLayerDropDown()
    }

    function initAbLineLayerDropDown() {

        const combo = jQuery('#zp-comboTree').combotree('tree');
        const treeData = combo.tree('getChildren');
        const selectedLayer =combo.tree("getSelected");

        let filteredData = [];
        for (const [key, treeObject] of Object.entries(treeData)) {
            // i can not unset object property because is passed by reference.
            if (treeObject.hasOwnProperty('children')) {
                filteredData.push(treeObject);
            }
        }

        const COMBO_TREE_CTRL = jQuery('#select-abline-layer');
        COMBO_TREE_CTRL.combotree({
            data: filteredData,
            animate: true,
            lines: true,
            multiple: false,
            editable: false,
            panelHeight: 'auto',
            onBeforeLoad:function(node){
                if (node && (!node.children || !node.children.length)){
                    $(node.target).find('.tree-icon').css('display','inline-block');
                }
            },
            onLoadSuccess: function () {
                COMBO_TREE_CTRL.combotree('setText', 'Стопанство / Година');
            },
            onBeforeSelect: function (node) {
                if (node.id === selectedLayer.id) {
                    jQuery.messager.alert("Грешка","Моля изберете различен слой.");
                    return false;
                }
            },
            onClick: function (node) {
                var farmNode = COMBO_TREE_CTRL.combotree('tree').tree('getParent', node.target);
                if (!farmNode) {
                    COMBO_TREE_CTRL.combotree('setText', node.text);
                    return false;
                }
                COMBO_TREE_CTRL.combotree('setText', farmNode.text + ' / ' + node.text);
            },
            onSelect: function (node) {
                if (node.children !== undefined || node.id === selectedLayer.id) return;

                var farmNode = COMBO_TREE_CTRL.combotree('tree').tree('getParent', node.target);
                if (!farmNode) return false;

                initPlotsTree( farmNode.id, node.id)
            },
            onBeforeExpand: function () {
                return false;
            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }

    function initPlotsTree(farming, year) {
        const PLOTS_TREE_CTRL = jQuery('#plots-abline-copy-tree');

        PLOTS_TREE_CTRL.tree({
            url: 'index.php?diary-rpc=diary-zp-tree',
            animate: true,
            lines: true,
            rpcMethod: 'read',
            cascadeCheck: false,
            rpcParams: [{
                farming: farming,
                year: year
            }],
            checkbox: false,
            onLoadSuccess: function (node, data) {
                //endLoading();
                jQuery('#ab-line-copy-plot-field').show();
            },
            onBeforeCheck: function (node) {

            },
            onSelect: function (node) {

            },
            onCheck: function (node, checked) {
            },

            onBeforeExpand: function (node) {

            },
            onBeforeLoad: function (node, param) {

            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }

    function copyAbLineToPlot()
    {
        if(!isAllSelectedAbLinesInSamePlot()) {
            jQuery.messager.alert(
                "Грешка",
                "Моля изберета AB линии само от един парцел",
                "error"
            );
            return;
        }

        const PLOTS_TREE_CTRL = jQuery('#plots-abline-copy-tree');
        const selectedPlot = PLOTS_TREE_CTRL.tree('getSelected');

        if(!selectedPlot) {
            jQuery.messager.alert(
                "Грешка",
                "Моля изберета парцел в който да копирате AB линиите",
                "error"
            );
            return;
        }

        const COMBO_TREE_CTRL = jQuery('#select-abline-layer');
        const selectedLayer = COMBO_TREE_CTRL.combotree("tree").tree("getSelected");
        const gid = selectedPlot.id;

        const abLines = getSelectedAbLinesToCopy();
        DiaryMap.copyAbLines({
            gid,
            zp_layer_id: selectedLayer.attributes.id,
            ab_lines_ids: abLines
        }).done(function(zipURL) {
            //endLoading();
            const zpTree = jQuery("#zp-tree");
            zpTree.tree('reload');
            jQuery('#win-abline-layer-copy').window('close')
        }).fail(function(errorObj) {
            //endLoading();
            jQuery.messager.alert("Грешка", errorObj.getMessage());
        });

    }

    /**
     *
     * @returns {{}}
     */
    function getSelectedAbLinesToCopy()
    {
        const ZP_TREE_CTRL = jQuery('#zp-tree');

        let checkedNodes = ZP_TREE_CTRL.tree('getChecked');
        let selectedAbLines = [];
        for (const node of checkedNodes) {
            if (node.attributes.node_type === 'ab_line') {
                selectedAbLines.push(node.attributes.id)
            }
        }

        return selectedAbLines;
    }

    function extendLineString(pointA, pointB, length) {
        var geomFactory = new jsts.geom.GeometryFactory();
		var lenAB = Math.sqrt(Math.pow(pointA.x - pointB.x, 2.0) + Math.pow(pointA.y - pointB.y, 2.0));

        var A1X = pointA.x + (pointA.x - pointB.x) / lenAB * length;
        var A1Y = pointA.y + (pointA.y - pointB.y) / lenAB * length;
        
        var B1X = pointB.x + (pointB.x - pointA.x) / lenAB * length;
        var B1Y = pointB.y + (pointB.y - pointA.y) / lenAB * length;

        var pointA1 = new jsts.geom.Coordinate(A1X, A1Y);
        var pointB1 = new jsts.geom.Coordinate(B1X, B1Y);
        
        var extendedLine = geomFactory.createLineString([pointA1, pointB1]);

        return extendedLine;
    }

    return {
        init,
        initABLinesLayer,
        createABLineControll,
        getAbLinesVectorLayer,
        getAbLinesWMSLayer
    };
});
