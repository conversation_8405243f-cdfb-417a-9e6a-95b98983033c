Namespace('TF.Rpc.Navigation');

//variables hold farming and year and plot_id for expanded node
var ZPselected = {
    farming: 0,
    year: 0,
    plot_ids: [],
};

let disableMarkAsCheckedOnSelect = false;

/**
 * Holds last used plot object by user
 * It will be used to for better UX when plot tree is reloaded
 * @type {null}
 */
var ZPLastEditedPlot = null;

var exportAllChecked = false;
var byPassTreeCheckHandler = false;
var lastLoadedMap = null;

//filter params
var filterIsak,
    filterEkate,
    filterCulture;

var COMBO_TREE_CTRL;
var ZP_TREE_CTRL;
var ZP_EDIT_WIN;
var ZP_TRACK_GRID;
//init buttons for zplots tree
jQuery(function () {

    //declaration of selectors
    ZP_EDIT_WIN = jQuery('#win-edit-zp');
    ZP_TRACK_GRID = jQuery('#diary-configs-plot-tracks');

    jQuery('#btn-edit-zplot').bind('click', function () {
        var selected = ZP_TREE_CTRL.tree('getSelected');
        if (!selected) {
            jQuery.messager.alert('Грешка', 'Не е избран земеделски парцел');
            return false;
        }
        var obj = {};
        obj.farming_id = selected['attributes'].farming;
        obj.year_id = selected['attributes'].year;
        obj.zplot_id = selected.id;
        TF.Rpc.Diary.ZPTree.markForEdit(obj)
            .done(function (data) {
                initZPEditComponents();
                clearZPEditFields();
                setZPEditFields(data);
                ZP_EDIT_WIN.window('open');
            })
            .fail(function (errorObj) {
                jQuery.messager.alert('Грешка', errorObj.getMessage());
            });
        return false;
    });

    jQuery('#btn-filter-event').bind('click', function () {
        jQuery('#win-tree-filter').window('open');
        return false;
    });

    //execute filter inside window
    jQuery('#btn-filter-zp-tree').bind('click', function () {
        initZPlotsFilterValues();
        ZP_TREE_CTRL.tree('reload');
        jQuery('#win-tree-filter').window('close');
    });

    jQuery('#btn-clear-filter-event').bind('click', function () {
        ZPselected.plot_ids = [];
        resetCheckZpTreeState()
        clearZPlotsFilter();
        ZP_TREE_CTRL.tree('reload');
        return false;
    });

    jQuery('#btn-edit-abline-event').bind('click', function (e) {
        var selItem = jQuery('#zp-tree').tree('getSelected');
        if (!selItem || selItem.attributes.node_type !== 'ab_line') {
            e.preventDefault();
            return;
        }
        var win = jQuery("#win-abline-save").window("open");
        win.find('.js-abline-name').textbox('setValue', selItem.text)
        return false;
    });

    jQuery('#toggle-check-zp-tree').bind('click', function () {
        if (!ZP_TREE_CTRL && !ZP_TREE_CTRL.data().hasOwnProperty('tree')){
            return;
        }
        var nodes = ZP_TREE_CTRL.tree('getRoots');
        var count = nodes.length;
        if (!count || count <= 0) {
            return;
        }
        byPassTreeCheckHandler = true;
        exportAllChecked = !exportAllChecked;
        jQuery(this).toggleClass("tree-checkbox0 tree-checkbox1");
        var action = exportAllChecked ? 'check' : 'uncheck';

        if (action == 'check') {
            ZPselected.plot_ids = [];
        }
        for (var i = 0; i < count; i++) {
            ZP_TREE_CTRL.tree(action, nodes[i].target);
            var children = ZP_TREE_CTRL.tree('getChildren', nodes[i].target);
            if (children.length > 0) {
                for (var child of children) {
                    ZP_TREE_CTRL.tree(action, child.target);
                }
            }
            if (action == 'check') {
                ZPselected.plot_ids.push(nodes[i].id);
            }
        }
        byPassTreeCheckHandler = false;

        disableMarkAsCheckedOnSelect = true;
        var lastNode = nodes[count - 1];
        if (action == 'check') {
            //display vector feature on the last checked
            displayFeatureSelection(lastNode['attributes'].geometry, lastNode['attributes'].label);
            ZP_TREE_CTRL.tree('select', lastNode.target);
        }else{
            jQuery(lastNode.target).removeClass('tree-node-selected');
            ZPselected.plot_ids = [];
        }
        disableMarkAsCheckedOnSelect = false;
    });
});

function resetCheckZpTreeState() {
    exportAllChecked = false;
    byPassTreeCheckHandler = false;
    var checkbox = jQuery('#toggle-check-zp-tree');
    if (checkbox.hasClass('tree-checkbox1')) {
        checkbox.removeClass('tree-checkbox1').addClass('tree-checkbox0');
    }
}

function initComboTree() {
    COMBO_TREE_CTRL = jQuery('#zp-comboTree');
    COMBO_TREE_CTRL.combotree({
        url: 'index.php?diary-rpc=diary-zp-tree',
        animate: true,
        lines: true,
        multiple: false,
        editable: false,
        panelHeight: jQuery(window).height() - jQuery('#zp-comboTree').offset().top - 30,
        rpcMethod: 'read',
        rpcParams: [{
            farming: 0,
            year: 0,
            plot_id: 0
        }],
        onLoadSuccess: function (node,data) {
            COMBO_TREE_CTRL.combotree('setText', 'Стопанство / Година');
            map.updateSize();
        },
        onBeforeSelect: function (node) {
            if (node.children !== undefined) return;
            //we are on the last element
            var farmNode = COMBO_TREE_CTRL.combotree('tree').tree('getParent', node.target);
            if (!farmNode) return false;
            ZPselected.farming = farmNode.id;
            ZPselected.year = node.id;
            ZPselected.plot_ids = [];
            // load plots on tree
            ZP_TREE_CTRL.tree('options').rpcParams = [ZPselected];
            ZP_TREE_CTRL.tree('options').url = 'index.php?diary-rpc=diary-zp-tree';
            resetCheckZpTreeState();
            jQuery('#plots-abline-copy-tree').html('');
            jQuery('#win-abline-layer-copy').window('close');
            jQuery("#tool-abline-copy").linkbutton({disabled: true});
            ZP_TREE_CTRL.tree('reload');
        },
        onClick: function (node) {
            var farmNode = COMBO_TREE_CTRL.combotree('tree').tree('getParent', node.target);
            if (!farmNode) {
                COMBO_TREE_CTRL.combotree('setText', node.text);
                return false;
            }
            COMBO_TREE_CTRL.combotree('setText', farmNode.text + ' / ' + node.text);
        },
        onSelect: function (node) {
            vectors.removeAllFeatures();
            var farmNode = COMBO_TREE_CTRL.combotree('tree').tree('getParent', node.target);
            if (!farmNode) return false;
            ZPselected.table = node.attributes.table;
            ZPselected.extent = node.attributes.extent;
            if (ZPselected.table && lastLoadedMap !== ZPselected.table) { //and not already loaded
                if (lastLoadedMap != undefined) {
                    removeLayerByName(lastLoadedMap); //remove old one
                    removeLayerByName("ab_lines"); //remove old AB layer one
                }
                lastLoadedMap = ZPselected.table; //keep track of last selected map
                loadMapLayer(ZPselected.table, ZPselected.extent); //load new map layer
                loadMapLayer("ab_lines", ZPselected.extent, {
                    layer_name: ZPselected.table
                }); //load new map layer
            }
            var bounds = new OpenLayers.Bounds.fromString(ZPselected.extent).transform(new OpenLayers.Projection("EPSG:32635"), map.getProjectionObject());
            var b = bounds.getCenterLonLat();
            var LonLat = new OpenLayers.LonLat(b.lon, b.lat);
            map.zoomToExtent(bounds);
            map.panTo(LonLat);
            map.updateSize();
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initZPTree() {
    ZP_TREE_CTRL = jQuery('#zp-tree');
    ZP_TREE_CTRL.tree({
        animate: true,
        lines: true,
        rpcMethod: 'read',
        cascadeCheck: false,
        rpcParams: [{
            farming: ZPselected.farming,
            year: ZPselected.year
        }],
        checkbox: true,
        onLoadSuccess: function (node, data) {
            endLoading();
            if (data.total !== undefined && data.total === 0) {
                jQuery.messager.alert('Внимание', 'Не са открити записи', 'warning');
            }

            // Tooltips
            jQuery('.tree-checkbox').tooltip({
                position: 'bottom',
                content: '<span style="color:#000;">Селектиране и работа с парцел</span>',
                onShow: function(){
                    jQuery(this).tooltip('tip').css({
                        zIndex: '229999',
                        borderColor: '#000'
                    });
                }
            });

            jQuery('.tree-title').tooltip({
                position: 'bottom',
                content: '<span style="color:#000;">Мащабиране на парцел</span>',
                onShow: function(){
                    jQuery(this).tooltip('tip').css({
                        zIndex: '229999',
                        borderColor: '#000'
                    });
                }
            });

            jQuery('ul span.tree-checkbox').tooltip({
                position: 'bottom',
                content: '<span style="color:#000;">Селектиране и работа с АB линии</span>',
                onShow: function(){
                    jQuery(this).tooltip('tip').css({
                        zIndex: '229999',
                        borderColor: '#000'
                    });
                }
            });

            jQuery('ul span.tree-title').tooltip('destroy');

            restoreTreeView();
        },
        onBeforeCheck: function (node) {

        },
        onSelect: function (node) {
            var parentNode = ZP_TREE_CTRL.tree('getParent', node.target);

            var extent = node.attributes.extent
                ? node.attributes.extent
                : parentNode.attributes.extent;
            var bounds = new OpenLayers.Bounds.fromString(extent).transform(
                new OpenLayers.Projection("EPSG:32635"),
                map.getProjectionObject()
            );


            if(!parentNode){
                var b = bounds.getCenterLonLat();
                var LonLat = new OpenLayers.LonLat(b.lon, b.lat);
                map.zoomToExtent(bounds);
                map.panTo(LonLat);
                jQuery("#btn-edit-abline-event").linkbutton("disable");

                displayFeatureSelection(node.attributes.geometry, node.attributes.label !== undefined ? node.attributes.label : '', 'GEO_JSON');
            }

            if (node.attributes.node_type === 'ab_line') {
                // move also map over ab_line
                // displayFeatureSelection(node.attributes.geometry, node.attributes.label !== undefined ? node.attributes.label : '', 'GEO_JSON', false);
                jQuery("#btn-edit-abline-event").linkbutton("enable");
            }

            markNodeAsChecked(node);
            setLastUsedNode(node);
        },
        onCheck: function (node, checked) {
            var parentNode = ZP_TREE_CTRL.tree('getParent', node.target);

            if (byPassTreeCheckHandler == true) {
                return;
            }

            // Enter in this if, when uncheck the element
            if (!checked) {
                const index = ZPselected.plot_ids.indexOf(node.id);
                if (index > -1) {
                    ZPselected.plot_ids.splice(index, 1);
                }

                //If the unchecked element doesn't have a parentNode means that it is a parent node. Therefore we should uncheck all children elements in this node;
                if(!parentNode && node['children'] !== undefined){
                    for(let child of node['children']){
                        if(child.target){
                            ZP_TREE_CTRL.tree('uncheck', child.target);
                        }
                    }
                }

                if(parentNode) removeFeaturesByIndex(node.attributes.id);

            } else {
                // indexOf will return -1 if node.id is not in plot_ids array
                // add only node of type plot (node.id is undefined for node_type = ab_line)
                if (node.attributes.node_type == 'plot' && ZPselected.plot_ids.indexOf(node.id) < 0) {
                    ZPselected.plot_ids.push(node.id);
                }

                var attributes;
                //if the checked node is child we should check and parent node
                if(parentNode){
                    // do not trigger event if parentNode is already checked
                    if (!parentNode.hasOwnProperty('_checked') || parentNode._checked == false) {
                        ZP_TREE_CTRL.tree('check', parentNode.target);
                    }

                    attributes = parentNode.attributes;
                    displayFeatureSelection(node.attributes.geometry, node.attributes.id, 'GEO_JSON', false, false);
                } else {
                    attributes = node.attributes;
                }

                initZplotEventsGrid(attributes.farming, attributes.year, node.id);
            }

            // enable/disable ab line copy control
            initAbLineCopyControl();
        },

        onBeforeExpand: function (node) {
            ZPselected.farming = node.attributes.farming;
            ZPselected.year = node.attributes.year;
        },

        onBeforeLoad: function (node, param) {
            //here we filter the plots
            if (ZPselected.farming !== 0 && ZPselected.year !== 0) {
                param.farming = ZPselected.farming;
                param.year = ZPselected.year;
            }
            if (ZPselected.plot_ids !== []) {
                param.plot_ids = ZPselected.plot_ids;
            }

            param.show_ablines = true;
            var filter = {};
            //adding filter params
            filter.zp_isak = filterIsak;
            filter.zp_ekate = filterEkate;
            filter.zp_culture = filterCulture;
            param.filter = filter;

            ZP_TREE_CTRL.tree('options').rpcParams = [param];
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

/**
 *
 * enable/disable copy ab control
 */
function initAbLineCopyControl() {
    const enable = isAllSelectedAbLinesInSamePlot();

    if (enable === true) {
        jQuery("#tool-abline-copy").linkbutton({disabled: false});
    } else {
        jQuery("#tool-abline-copy").linkbutton({disabled: true});
    }
}

/**
 *
 * @returns {boolean}
 */
function isAllSelectedAbLinesInSamePlot() {
    let enableCopy = false;
    let firstMetParent = null;
    let checkedNodes = ZP_TREE_CTRL.tree('getChecked');

    for (const node of checkedNodes) {
        if (node.attributes.node_type === 'ab_line') {
            const parentNode = ZP_TREE_CTRL.tree('getParent', node.target);
            if (firstMetParent === null) {
                firstMetParent = parentNode;
                enableCopy = true;
            } else {
                if (firstMetParent.id != parentNode.id) {
                    enableCopy = false;
                    break;
                }

                enableCopy = true;
            }
        }
    }

    return enableCopy;
}

/**
 *
 * @param node
 */
function setLastUsedNode(node) {
    if (node == null) {
        return;
    }

    const parentNode = ZP_TREE_CTRL.tree('getParent', node.target);

    if (parentNode) {
        ZPLastEditedPlot = parentNode;
        return;
    }
    ZPLastEditedPlot = node;
}

/**
 *
 * After reload tree restore last used by user tree nodes state
 */
function restoreTreeView()
{
    // make copy of global property, because it will be overwritten by current js execution
    const nodeToRestore =  ZPLastEditedPlot;

    if (nodeToRestore !== null) {
        // tree nodes id's are changing on every reload, so we need to map our stored object to newly generated one
        const currentPlotNode = ZP_TREE_CTRL.tree('find', nodeToRestore.id);
        // select will also trigger check
        ZP_TREE_CTRL.tree('select', currentPlotNode.target);
        scrollToTreeNode(currentPlotNode);

        if (nodeToRestore.hasOwnProperty('children')) {
            nodeToRestore.children.map(function(children) {
                if (children.hasOwnProperty('_checked')) {
                    // before reload we can delete children, so check again for children prop
                    if (currentPlotNode.hasOwnProperty('children')) {
                        const currentPlotChildrens = ZP_TREE_CTRL.tree('getChildren', currentPlotNode.target)
                        // children will not have target prop if is not returned by find or some other easy ui method
                        currentPlotChildrens.map(function (currentChildren) {
                            if (currentChildren.attributes.id == children.attributes.id) {
                                ZP_TREE_CTRL.tree('check', currentChildren.target);
                            }
                        });
                    }
                }
            });
        }
    }
}

/**
 *
 * Scrolls to last checked plot
 */
function scrollToLastCheckedPlot()
{
    const node = getLastCheckedPlotNode();
    if (node) {
        scrollToTreeNode(node);
    }
}

/**
 * Scrolls to node
 *
 * @param node
 */
function scrollToTreeNode(node)
{
    ZP_TREE_CTRL.tree('scrollTo', node.target);
}

/**
 * return last checked by user plot node
 *
 * @returns {boolean|*}
 */
function getLastCheckedPlotNode()
{
    const plotId = getLastElementOfArray(ZPselected.plot_ids);

    if (plotId) {
        return ZP_TREE_CTRL.tree('find', plotId);
    }

    return false;
}

/**
 * @param arr
 * @returns {null|*}
 */
function getLastElementOfArray(arr)
{
    if (arr.length) {
        return arr.slice(-1).pop();
    }
    return null;
}

/**
 * check node and if node is plot unselect all other plots
 * @param node
 */
function markNodeAsChecked(node)
{
    if(disableMarkAsCheckedOnSelect == true){
        return;
    }

    // Shallow Copy Only, clone array because can be modified in onCheck event
    const cloneZP = [... ZPselected.plot_ids]

    ZP_TREE_CTRL.tree('check',node.target);
    cloneZP.map(function (plotId, index) {
        const parentNode = ZP_TREE_CTRL.tree('getParent', node.target);

        // currently the behaviour is to clear all ab_lines from maps, with this change we will also uncheck the boxes
        if (parentNode === null) {
            if (node.hasOwnProperty('children')) {
                node.children.map(function(children) {
                    if (children.hasOwnProperty('_checked')) {
                        ZP_TREE_CTRL.tree('uncheck', children.target);
                    }
                })
            }
        }

        const prevNode = ZP_TREE_CTRL.tree('find', plotId);

        // skipp uncheck for currently selected node and for parentNode if is ab_line
        if (prevNode.id == node.id || parentNode)  {
           return;
        }

        ZP_TREE_CTRL.tree('uncheck', prevNode.target);
    })
}

function initZPEditComponents() {
    jQuery('#edit-zp-ekate-input').combobox({
        url: 'index.php?common-rpc=ekate-combobox',
        valueField: 'ekate',
        textField: 'ekate',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    jQuery('#edit-zp-culture-input').combobox({
        url: 'index.php?common-rpc=culture-combobox',
        valueField: 'id',
        textField: 'name',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function saveZPlotEdit() {
    var requestObj = getZPEditFields();
    var selected = ZP_TREE_CTRL.tree('getSelected');
    requestObj.farming_id = selected['attributes'].farming;
    requestObj.year_id = selected['attributes'].year;
    requestObj.zplot_id = selected.id;

    TF.Rpc.Diary.ZPTree.saveEdit(requestObj)
        .done(function () {
            finishZPlotSaveEdit();
        })
        .fail(function (errorObj) {
            jQuery.messager.alert('Грешка', errorObj.getMessage());
        });
}

function finishZPlotSaveEdit() {
    ZP_EDIT_WIN.window('close');
    ZP_TREE_CTRL.tree('reload');
}

function initZPlotsFilterValues() {
    filterIsak = jQuery('#search-isak-num > input').val();
    filterEkate = jQuery('#search-ekate-input').combobox('getValue');
    filterCulture = jQuery('#search-culture-input').combobox('getValue');
}

function clearZPlotsFilter() {
    filterIsak = undefined;
    filterEkate = undefined;
    filterCulture = undefined;
    jQuery('#search-isak-num > input').val('');
    jQuery('#search-ekate-input').combobox('reset');
    jQuery('#search-culture-input').combobox('reset');
}

function clearZPEditFields() {
    jQuery('#edit-zp-isak-input').val('');
    jQuery('#edit-zp-ekate-input').combobox('reset');
    jQuery('#search-culture-input').combobox('reset');
    jQuery('#edit-zp-obrabotki-input').val('');
    jQuery('#edit-zp-dobivi-input').val('');
    jQuery('#edit-zp-napoqvane-input').val('');
    jQuery('#edit-zp-polivki-input').val('');
    jQuery('#edit-zp-polzvatel-input').val('');
}

function setZPEditFields(data) {
    if (data) {
        jQuery('#edit-zp-isak-input').val(data.isak_prc_uin);
        jQuery('#edit-zp-ekate-input').combobox('select', data.ekatte);
        jQuery('#edit-zp-culture-input').combobox('select', data.culture);
        jQuery('#edit-zp-obrabotki-input').val(data.obrabotki);
        jQuery('#edit-zp-dobivi-input').val(data.dobivi);
        jQuery('#edit-zp-napoqvane-input').val(data.napoqvane);
        jQuery('#edit-zp-polivki-input').val(data.polivki);
        jQuery('#edit-zp-polzvatel-input').val(data.polzvatel);
    }
}

function getZPEditFields() {
    return {
        isak: jQuery('#edit-zp-isak-input').val(),
        ekate: jQuery('#edit-zp-ekate-input').combobox('getValue'),
        culture: jQuery('#edit-zp-culture-input').combobox('getValue'),
        obrabotki: jQuery('#edit-zp-obrabotki-input').val(),
        dobivi: jQuery('#edit-zp-dobivi-input').val(),
        napoqvane: jQuery('#edit-zp-napoqvane-input').val(),
        polivki: jQuery('#edit-zp-polivki-input').val(),
        polzvatel: jQuery('#edit-zp-polzvatel-input').val()
    };
}
