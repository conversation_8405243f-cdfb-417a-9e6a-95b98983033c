var context_layer = undefined;

function initAllLayersTree() {
    jQuery('#all-layers-tree').tree({
        url: 'index.php?diary-rpc=all-layers-tree',
        animate: true,
        lines: true,
        checkbox: true,
        dnd: false,
        cascadeCheck: false,
        fit: true,
        rpcParams: [{
            allowable: false,
            system_only: true,
            vps: true
        }],
        onBeforeSelect: function (node) {
            if ((!node['attributes'].is_system && node['attributes'].level != 3) || (node['attributes'].is_system && node['attributes'].level != 2)) {
                return false;
            }
        },
        onSelect: function (node) {
            jQuery(this).tree('check', node.target);

            if (!node['attributes'].is_system) {
                jQuery('#active-layer-menubutton > span .l-btn-text').html(node['attributes'].farming + ' / ' + node['attributes'].year + ' / ' + node['attributes'].name);
            } else {
                jQuery('#active-layer-menubutton > span .l-btn-text').html(node['attributes'].name);
            }
        },
        onBeforeCheck: function (node) {

        },
        onCheck: function (node, isChecked) {
            updateKSVMenu();
            if (node['attributes'].level == 3 || (node['attributes'].is_system && node['attributes'].level == 2)) {
                if (!isChecked) {
                    removeLayerByName(node['attributes'].layer_name);
                    return;
                }

                loadMapLayers(node);
            }
        },
        onContextMenu: function (e, node) {
            e.preventDefault();
            context_layer = node.id;
            if (!node.checked) {
                jQuery(this).tree('check', node.target);
            }
            if ((node['attributes'].is_system == false && node['attributes'].level == 3) || (node['attributes'].is_system == true && node['attributes'].level == 2)) {
                jQuery('#all-layers-tree-cm').menu('show', {
                    left: e.pageX,
                    top: e.pageY
                });
            }
        },
        onLoadSuccess: function () {
            jQuery('div.tooltip').hide();

            jQuery('.layer-tree-tooltip').tooltip({
                position: 'bottom',
                content: '<span style="color:#000">Избор на активен слой</span>',
                onShow: function(){
                    jQuery(this).tooltip('tip').css({
                        zIndex: '229999',
                        borderColor: '#000'
                    });
                }
            });

            jQuery('.tree-checkbox').tooltip({
                position: 'bottom',
                content: '<span style="color:#000;">Визуализация на слой</span>',
                onShow: function(){
                    jQuery(this).tooltip('tip').css({
                        zIndex: '229999',
                        borderColor: '#000'
                    });
                }
            });

            updateKSVMenu();
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

}

function updateKSVMenu() {
    var active_layer = jQuery('#all-layers-tree').tree('getSelected');
    var menu = jQuery('#kvs-filter-menu-btn');
    if (active_layer == null) {
        menu.menubutton('disable');
        return false;
    }
    if (active_layer.attributes.layer_name === 'layer_kvs') {
        menu.menubutton('enable');
    }else{
        menu.menubutton('disable');
    }
}
