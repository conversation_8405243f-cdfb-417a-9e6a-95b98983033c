(function(root, factory) {
    if (typeof define === "function" && define.amd) {
        // AMD. Register as an anonymous module.
        define(["jquery", "OpenLayers", "TF/Rpc/Diary/DiaryMap"], factory);
    } else {
        // Browser globals (root is window)
        root.navigation_preview = factory(jQuery, OpenLayers);
    }
})(typeof self !== "undefined" ? self : this, function(
    jQuery,
    OpenLayers
) {

    function initNavigationPreviewLayer () {
        var renderer = OpenLayers.Util.getParameters(window.location.href).renderer;
        renderer = (renderer) ? [renderer] : OpenLayers.Layer.Vector.prototype.renderers;
    
        let layer = new OpenLayers.Layer.Vector("NavigationLineFeature", {
            styleMap: new OpenLayers.StyleMap(new OpenLayers.Style({
                    strokeColor: "#ff0000",
                    strokeWidth: 2,
                    fillOpacity: 0.2,
                    fillColor: "#ff0000",
                }
            )),
            renderers: OpenLayers.Layer.Vector.prototype.renderers
        });
    
        map.addLayer(layer);
        return layer;
    }
    
    function getNavigationPreviewLayer() {
        let layers = map.getLayersByName("NavigationLineFeature");
        return layers[0];
    }
    
    
    return {
        initNavigationPreviewLayer,
        getNavigationPreviewLayer
    };
});
