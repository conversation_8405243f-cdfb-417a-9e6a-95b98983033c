jQuery(function () {
    initPlotTracksGrid();
});

function initZplotEventsGrid(zp_farming, zp_year, zplot_id) {
    jQuery('#zplot-events-tables').datagrid({
        nowrap: true,
        singleSelect: true,
        pageSize: 20,
        fit: true,
        border: false,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?diary-rpc=diary-zplot-events-grid',
        rpcParams: [{
            farming: zp_farming,
            year: zp_year,
            zplot_id: zplot_id
        }],
        idField: 'id',
        rowStyler: function (index, row) {
            if (row.red_color == true) {
                return 'background-color:#FF0000;';
            }
        },
        columns: [
            [
                {field: 'phase', title: '<b>Етап</b>', sortable: false, rowspan: 2},
                {field: 'event_type', title: '<b>Тип</b>', sortable: false, rowspan: 2},
                {field: 'event_subtype', title: '<b>Вид</b>', sortable: false, rowspan: 2},
                {field: 'machine_number', title: '<b>Основна машина</b>', sortable: false, width: 110, rowspan: 2},
                {title: '<b>Планиране</b>', colspan: 2},
                {
                    title: '<b>Изпълнение</b>',
                    colspan: 2
                }, {
                title: '<b>Обща стойност (лв.)</b>',
                field: 'event_total',
                sortable: false,
                width: 110,
                rowspan: 2,
                align: 'center'
            }
            ],
            [{
                field: 'plan_date_from',
                title: '<b>От</b>',
                sortable: false,
                width: 65,
                align: 'center'
            }, {
                field: 'plan_date_to',
                title: '<b>До</b>',
                sortable: false,
                width: 65,
                align: 'center'
            }, {
                field: 'complete_date_from',
                title: '<b>От</b>',
                sortable: false,
                width: 65,
                align: 'center'
            }, {
                field: 'complete_date_to',
                title: '<b>До</b>',
                sortable: false,
                width: 65,
                align: 'center'
            }
            ]
        ],
        rownumbers: true,
        toolbar: [
            {
                id: 'btn_add_event',
                text: 'Добавяне',
                iconCls: 'icon-add',
                handler: function () {
                    clearAddEditEventPanel();
                    jQuery('#zplot-events-tables').datagrid('clearSelections');
                    jQuery('#win-add-edit-event').window('open');
                }
            }, {
                id: 'btn_edit_event',
                text: 'Редактиране',
                iconCls: 'icon-edit',
                handler: function () {
                    loadSingleEdit();
                }
            }, {
                id: 'btn_delete_event',
                text: 'Изтриване',
                iconCls: 'icon-delete',
                handler: function () {
                    deleteSingleEvent();
                }
            }
        ],
        onDblClickRow: function (index, row) {
            if (!row) return false;
            loadSingleEdit();
        },
        onBeforeLoad: function (param) {
            if (param == null || zp_farming == null || zp_year == null || zplot_id == null) return false;
            clearAddEditEventPanel();
            jQuery(this).datagrid('clearSelections');
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function loadSingleEdit() {
    var eventData = jQuery('#zplot-events-tables').datagrid('getSelected');
    if (eventData) {
        TF.Rpc.Diary.ZPlotEventsGrid.singleEditEvent(eventData.id)
            .done(function (data) {
                jQuery('#win-add-edit-event').window('open');
                setEditEventFields(data);
            });
    } else {
        jQuery.messager.alert('Грешка', 'Не е избрана обработка!');
    }
}

function deleteSingleEvent(){
    var eventData = jQuery('#zplot-events-tables').datagrid('getSelected');
    if (eventData && eventData.id) {
        TF.Rpc.Diary.ZPlotEventsGrid.deleteSingleEvent(eventData.id)
            .done(function (data) {
                jQuery('#zplot-events-tables').datagrid('clearSelections').datagrid('reload');
            });
    } else {
        jQuery.messager.alert('Съобщение', 'Моля изберете обработка!');
    }
}

function setEditEventFields(event) {
    clearAddEditEventPanel();
    var temp_machine_id = checkMachineWithWialonId(event.machine_id);
    jQuery("#event-phase > input").combobox('select', event.phase_id);
    jQuery("#event-type > input").combobox('setValue', event.type_id);
    jQuery("#event-performer > input").combobox('setValue', event.performer_id);
    event.treated_area =  Math.min(event.treated_area, ZPselected.area);
    jQuery("#event-processed-area > input").numberbox('setValue', event.completed_area);
    jQuery("#event-plan-date-from > input").datebox('setValue', event.plan_date_from);
    jQuery("#event-plan-time-from > input").timespinner('setValue', event.plan_time_from);
    jQuery("#event-plan-date-to > input").datebox('setValue', event.plan_date_to);
    jQuery("#event-plan-time-to > input").timespinner('setValue', event.plan_time_to);
    jQuery("#event-complete-date-from > input").datebox('setValue', event.complete_date_from);               // track event
    jQuery("#event-complete-date-to > input").datebox('setValue', event.complete_date_to);                   // track event
    jQuery("#event-complete-time-from .timespinner-f").timespinner('setValue', event.complete_time_from);    // track event
    jQuery("#event-complete-time-to .timespinner-f").timespinner('setValue', event.complete_time_to);
    if(jQuery("#event-subtype > input").data().hasOwnProperty('combobox') == true){
        jQuery("#event-subtype > input").combobox('loadData', (event.sub_types) ? event.sub_types : []).combobox('select', event.subtype_id);
    }
      // track event
    jQuery("#event-machine > input").combobox('setValue', (temp_machine_id !== false) ? temp_machine_id : event.machine_id);
    jQuery("#event-attachment > input").combobox('setValue', event.attachment_id);
    jQuery("#event-amortization > input").numberbox('setValue', event.amortization_cost);
    jQuery("#event-machine-rented > input").prop('checked', event.is_rented).trigger('change');
    jQuery("#event-rent-cost > input").numberbox('setValue', event.rent_cost);
    jQuery('#event-fuel-unit').combobox('select', event.fuel_unit);
    jQuery("#event-fuel-consumed-qty > input").numberbox('setValue', event.total_fuel_cost);
    jQuery("#event-movement-fuel > input").numberbox('setValue', event.movement_fuel);                       // track fuel
    jQuery("#event-parking-fuel > input").numberbox('setValue', event.parking_fuel);                         // track fuel
    jQuery("#event-start-fuel-level > input").numberbox('setValue', event.start_fuel);                       // track fuel
    jQuery("#event-end-fuel-level > input").numberbox('setValue', event.end_fuel);                           // track fuel
    jQuery("#event-avg-engine-revs > input").numberbox('setValue', event.avg_engine_revs);                   // track fuel
    jQuery("#event-avg-speed > input").numberbox('setValue', event.avg_speed);                               // track fuel
    jQuery("#event-mileage > input").numberbox('setValue', event.completed_mileage);                         // track fuel
    jQuery("#event-time-in > input").timespinner('setValue', event.time_in);                                 // track fuel
    jQuery("#event-time-out > input").timespinner('setValue', event.time_out);                               // track fuel
    jQuery("#event-total-time-in > input").timespinner('setValue', event.total_time_in);                     // track fuel
    jQuery("#event-movement-time > input").timespinner('setValue', event.movement_time);                     // track fuel
    jQuery("#event-parking-time > input").timespinner('setValue', event.parking_time);                       // track fuel
    jQuery("#event-comment > textarea").val(event.comment);
    jQuery("#event-per-litre-fuel-cost").numberbox('setValue', event.price_per_litre);
    if (event.products && event.products.length > 0) {
        jQuery('#dg').datagrid('loadData', event.products);
        update_products_total_cost(null, null);
        var productMaxTreatableArea = event.treated_area;
        for (var i = 0; i < event.products.length; i++) {
            productMaxTreatableArea = Math.max(productMaxTreatableArea, event.products[i]['treated_area']);
        }
        productMaxTreatableArea = Math.min(productMaxTreatableArea, ZPselected.area);
        jQuery("#event-treatable_area").numberbox('setValue', productMaxTreatableArea);
    }else{
        jQuery("#event-treatable_area").numberbox('setValue', event.treated_area);
    }
    // keep for backward compatibility ,
    // plan_fuel_cost ia planned fuel consumption liter / dk
    // final_fuel_cost is reported expense liter / dka
    jQuery("#event-plan-fuel-cost > input").numberbox('setValue', event.plan_fuel_cost);
    jQuery("#event-fuel-cost > input").val(event.final_fuel_cost);
    // this will update also the plot's grand total.
    update_products_total_cost(null, null);
}

function getEditEventFields() {
    var temp_machine_id = checkMachineWithWialonId(jQuery("#event-machine > input").combobox('getValue'));
    return {
        phase_id: jQuery("#event-phase > input").combobox('getValue'),
        type_id: jQuery("#event-type > input").combobox('getValue'),
        subtype_id: jQuery("#event-subtype > input").combobox('getValue'),
        performer_id: jQuery("#event-performer > input").combobox('getValue'),
        plan_date_from: jQuery("#event-plan-date-from > input").datebox('getValue'),
        plan_date_to: jQuery("#event-plan-date-to > input").datebox('getValue'),
        plan_time_from: jQuery("#event-plan-time-from > input").timespinner('getValue'),
        plan_time_to: jQuery("#event-plan-time-to > input").timespinner('getValue'),
        complete_date_from: jQuery("#event-complete-date-from > input").datebox('getValue'),
        complete_date_to: jQuery("#event-complete-date-to > input").datebox('getValue'),
        complete_time_from: jQuery("#event-complete-time-from > input").timespinner('getValue'),
        complete_time_to: jQuery("#event-complete-time-to > input").timespinner('getValue'),
        treatable_area: jQuery("#event-treatable_area").numberbox('getValue'),
        completed_area: parseFloat(jQuery("#event-processed-area > input").val()),
        machine_id: (temp_machine_id !== false) ? temp_machine_id : jQuery("#event-machine > input").combobox('getValue') ,
        attachment_id: jQuery("#event-attachment > input").combobox('getValue'),
        amortization_cost: jQuery("#event-amortization > input").val(),
        is_rented: jQuery("#event-machine-rented > input").prop('checked'),
        rent_cost: jQuery("#event-rent-cost > input").val(),
        total_fuel_cost: jQuery("#event-fuel-consumed-qty > input").val(),
        movement_fuel: jQuery("#event-movement-fuel > input").val(),
        parking_fuel: jQuery("#event-parking-fuel > input").val(),
        start_fuel: jQuery("#event-start-fuel-level > input").val(),
        end_fuel: jQuery("#event-end-fuel-level > input").val(),
        avg_engine_revs: jQuery("#event-avg-engine-revs > input").val(),
        avg_speed: jQuery("#event-avg-speed > input").val(),
        completed_mileage: jQuery("#event-mileage > input").val(),
        time_in: jQuery("#event-time-in > input").timespinner('getValue'),
        time_out: jQuery("#event-time-out > input").timespinner('getValue'),
        total_time_in: jQuery("#event-total-time-in > input").timespinner('getValue'),
        movement_time: jQuery("#event-movement-time > input").timespinner('getValue'),
        parking_time: jQuery("#event-parking-time > input").timespinner('getValue'),
        comment: jQuery("#event-comment > textarea").val(),
        price_per_litre: jQuery("#event-per-litre-fuel-cost").numberbox('getValue'),
        products: getProducts(),
        // backward
        plan_fuel_cost: jQuery("#event-plan-fuel-cost > input").val(),
        final_fuel_cost: jQuery("event-final-fuel-cost > input").val(),
        fuel_unit: jQuery('#event-fuel-unit').combobox('getValue'),
        zplot_area: ZPselected.area // passing zplot area to the server to calculate the expense cost (if any) on the server
};
}

function clearAddEditEventPanel() {
    jQuery("#event-processed-area > input").numberbox('setValue');
    resetInfoTab();
    resetProductTab();
    resetMachineTab();
    resetFuelTab();
    resetFarmTrackTab();
    resetUnitsComboBox();
    jQuery("#event-total > input").numberbox('setValue');
}

function resetInfoTab() {

    jQuery('#event-complete-date-from > input').datebox('setValue', new Date().toString('dd-MMM-yyyy'));
    jQuery('#event-complete-date-to > input').datebox('setValue', new Date().toString('dd-MMM-yyyy'));

    jQuery("#event-complete-time-from > input").timespinner('setValue', '00:00');
    jQuery("#event-complete-time-to > input").timespinner('setValue', '23:59');

    jQuery("#event-plan-date-from > input").datebox('setValue', new Date().toString('dd-MMM-yyyy'));
    jQuery("#event-plan-date-to > input").datebox('setValue', new Date().toString('dd-MMM-yyyy'));

    jQuery("#event-plan-time-from > input").timespinner('setValue', '00:00');
    jQuery("#event-plan-time-to > input").timespinner('setValue', '23:59');

    jQuery("#event-treatable_area").numberbox('reset').numberbox('options').max = ZPselected.area;

    jQuery("#event-comment > textarea").val('');

    jQuery("#event-phase > input").combobox('reset');
    jQuery("#event-type > input").combobox('reset');
    try{
        jQuery("#event-subtype > input").combobox('reset');
    }catch(e){

    }
    jQuery("#event-performer > input").combobox('reset');
    jQuery("#event-processed-area > input").val('');
    jQuery("#event-treatable_area").numberbox('reset');
}

function resetProductTab() {
    product_datagrid.datagrid('loadData', []);
    product_datagrid.datagrid('deleteRow', 0);
}

function resetMachineTab() {
    jQuery("#event-machine > input").combobox('reset');
    jQuery("#event-attachment > input").combobox('reset');
    jQuery("#event-amortization > input").numberbox('setValue');
    jQuery("#event-machine-rented > input").prop('checked', false);
    jQuery("#event-rent-cost > input").numberbox('setValue');
}

function resetFuelTab() {
    jQuery("#event-plan-fuel-cost > input").numberbox('setValue');
    jQuery("#event-fuel-consumed-qty > input").numberbox('setValue');
    jQuery("#event-fuel-cost > input").numberbox('setValue');
    jQuery("#event-per-litre-fuel-cost").numberbox('setValue');
    jQuery("#event-movement-fuel > input").numberbox('setValue');
    jQuery("#event-parking-fuel > input").numberbox('setValue');
    jQuery("#event-start-fuel-level > input").numberbox('setValue');
    jQuery("#event-end-fuel-level > input").numberbox('setValue');
}

function resetFarmTrackTab() {
    jQuery("#event-avg-engine-revs > input").numberbox('setValue');
    jQuery("#event-avg-speed > input").numberbox('setValue');
    jQuery("#event-mileage > input").numberbox('setValue');
    jQuery("#event-time-in > input").timespinner('setValue');
    jQuery("#event-time-out > input").timespinner('setValue');
    jQuery("#event-total-time-in > input").timespinner('setValue');
    jQuery("#event-movement-time > input").timespinner('setValue');
    jQuery("#event-parking-time > input").timespinner('setValue');
}

function setFuelFields(data) {
    jQuery("#event-movement-fuel").find("> input").numberbox('setValue', parseFloat(data['Consumed']));
    jQuery("#event-fuel-consumed-qty").find("> input").numberbox('setValue', parseFloat(data['Consumed']));
    jQuery("#event-start-fuel-level").find("> input").numberbox('setValue', data['Initial fuel level']);
    jQuery("#event-end-fuel-level").find("> input").numberbox('setValue', data['Final fuel level']);
    jQuery("#event-avg-engine-revs").find("> input").numberbox('setValue', data['Avg engine revs']);
    jQuery("#event-avg-speed").find("> input").numberbox('setValue', data['Avg speed']);
    jQuery("#event-mileage").find("> input").numberbox('setValue', data['Mileage']);
    jQuery("#event-total-time-in").find("> input").timespinner('setValue', data['Total time']);
    jQuery("#event-movement-time").find(">input").timespinner('setValue', data['In movement']);
    jQuery('#event-parking-time .timespinner-f').timespinner('setValue', data['Idling']);
}

function resetFuelFields() {
    jQuery("#event-movement-fuel").find("> input").numberbox('setValue');
    jQuery("#event-parking-fuel").find("> input").numberbox('setValue');
    jQuery("#event-start-fuel-level").find("> input").numberbox('setValue');
    jQuery("#event-end-fuel-level").find("> input").numberbox('setValue');
    jQuery("#event-avg-engine-revs").find("> input").numberbox('setValue');
    jQuery("#event-avg-speed").find("> input").numberbox('setValue');
    jQuery("#event-mileage").find("> input").numberbox('setValue');
    jQuery("#event-total-time-in").find("> input").timespinner('setValue');
    jQuery("#event-movement-time").find(">input").timespinner('setValue');
    jQuery('#event-parking-time .timespinner-f').timespinner('setValue');
}

function isEventValid() {
    var eventData = {};
    eventData.phase = jQuery('#event-phase').find('> input').combobox('getValue');
    eventData.type = jQuery('#event-type').find('> input').combobox('getValue');
    eventData.subtype = jQuery("#event-subtype > input").combobox('getValue');

    var farm_valid = (ZPselected.farming != 0 && ZPselected.plot_id != 0 && ZPselected.year != 0);
    var event_valid = ((eventData.phase == EVENT_COMPLETE || eventData.phase == EVENT_PLANNED) && eventData.type != '');
    var type_valid = (eventData.type != undefined && eventData.type != '' && eventData.type > 0);
    var sub_type_valid = (eventData.subtype != undefined && eventData.subtype != '' && eventData.subtype > 0);
    var valid_date = false;

    if (eventData.phase == EVENT_COMPLETE) {
        eventData.complete_date_from = jQuery("#event-complete-date-from > input").datebox('getValue');
        eventData.complete_date_to = jQuery("#event-complete-date-to > input").datebox('getValue');
        eventData.complete_time_from = jQuery("#event-complete-time-from > input").timespinner('getValue');
        eventData.complete_time_to = jQuery("#event-complete-time-to > input").timespinner('getValue');
        valid_date = (eventData.complete_date_from != '' && eventData.complete_date_to != '' && eventData.complete_time_from != '' && eventData.complete_time_to != '');
    }
    if (eventData.phase == EVENT_PLANNED) {
        eventData.plan_date_from = jQuery("#event-plan-date-from > input").datebox('getValue');
        eventData.plan_date_to = jQuery("#event-plan-date-to > input").datebox('getValue');
        eventData.plan_time_from = jQuery("#event-plan-time-from > input").timespinner('getValue');
        eventData.plan_time_to = jQuery("#event-plan-time-to > input").timespinner('getValue');
        valid_date = (eventData.plan_date_from != '' && eventData.plan_date_to != '' && eventData.plan_time_from != '' && eventData.plan_time_to != '');

    }
    var timeFrame_greater_zero = checkTimeFrame(eventData);
    return {
        valid: (farm_valid && event_valid && valid_date && type_valid && sub_type_valid && timeFrame_greater_zero),
        details: {
            farm_valid: farm_valid,
            event_valid: event_valid,
            type_valid: type_valid,
            sub_type_valid: sub_type_valid,
            valid_date: valid_date,
            timeFrame_greater_zero: timeFrame_greater_zero
        }
    };
}

//after edit values array pushed for every row
function getProducts() {
    var data = product_datagrid.datagrid('getData');
    if (!data.rows || data.rows.length <= 0) return null;
    data = data.rows;
    var temp = [];
    for (var r = 0; r < data.length; r++) {
        var row = data[r];
        if (row.editing) {
            product_datagrid.datagrid('endEdit', r);
        }
        if (row.values && row.editing === false && row.substance_id) {
            temp.push(data[r].values);
        }
    }
    return temp.length ? temp : null;
}

function validateProducts() {
    var data = product_datagrid.datagrid('getData');
    if (!data.rows || data.rows.length <= 0) return null;
    data = data.rows;
    for (var r = 0; r < data.length; r++) {
        product_datagrid.datagrid('selectRow', r);
        var row = data[r];
        var rowEditing = row.editing;
        if (rowEditing) product_datagrid.datagrid('endEdit', r);
        if (row.substance_id == null || row.substance_unit_type == null || row.substance_technic_id == null) {
            return false;
        }
        if (rowEditing) product_datagrid.datagrid('beginEdit', r);
    }
    return true;
}


/* total cost for all products/treatments of the selected event
 * index and sub_total are the index and the value of the selected row being updated.
 * we loop trough all the row to sum their price_per_area,
 * but if index and sub_total are present we use the sub_total value for that row
 * */
function update_products_total_cost(selected_index, sub_total) {
    var total = 0;
    var sub_tot = null;
    var data = product_datagrid.datagrid('getData');
    if (data.rows && data.rows.length > 0) {
        for (var i = 0; i < data.rows.length; i++) {
            if (selected_index != null && selected_index == i) {
                sub_tot = sub_total;
            } else {
                sub_tot = parseFloat(data.rows[i].price_per_area);
            }
            if (!isNaN(sub_tot)) total += parseFloat(sub_tot);
        }
    }
    jQuery('#event-substance-total-money').val(total.toFixed(2) + ' .лв');
    updateTotalField();
}

function checkMachineWithWialonId(id_to_search){
    var all_machines = jQuery("#event-machine > input").combobox('getData');
    //get proper db id , not wialon id
    for (var i = 0; i < all_machines.length; i++) {
        if (all_machines[i].wialon_id == id_to_search) {
            return all_machines[i].id;
        }
    }
    return false;
}
