Namespace("TF.Rpc.Diary");

var EVENT_PLANNED = 1;
var EVENT_COMPLETE = 2;

var is_edit = false;
var trackMachineID;
var trackDate;
var trackTimeFrom;
var trackTimeTo;
var product_datagrid;
jQuery(function() {
    initZPTree();
    initComboTree();
    initMap();
    initAllLayersTree();
    initVectorLayers();
    ab_lines.initABLinesLayer();
    navigation_preview.initNavigationPreviewLayer();
    initControls();
    setUserLastLogin();

    var diaryExpensesActivity = jQuery("#diary-expenses-activity");
    var diaryExpensesPerformer = jQuery("#diary-expenses-performer");
    var diaryExpensesValidFrom = jQuery("#diary-expenses-valid-from");
    var diaryExpensesValidTo = jQuery("#diary-expenses-valid-to");
    var diaryExpensesPrice = jQuery("#diary-expenses-price");
    var winAddEditExpenses = jQuery("#win-add-edit-expenses");
    var expensesGrid = jQuery("#diary-configs-expenses");
    var diaryExpensesReportBtn = jQuery("#diary-expenses-report");
    var diarySettingsWin = jQuery("#win-diary-settings");
    var plotsTrackWindow = jQuery("#win-diary-plot-tracks");
    var diaryConfigsGrid = jQuery("#diary-configs-tables");
    product_datagrid = jQuery("#dg");
    plotsTrackWindow.window({ closed: true });
    winAddEditExpenses.window({
        onClose: function() {
            diaryExpensesActivity.combobox("reset");
            diaryExpensesPerformer.combobox("reset");
            diaryExpensesValidFrom.datebox("reset");
            diaryExpensesValidTo.datebox("reset");
            diaryExpensesPrice.numberbox("reset");
        }
    });
    diarySettingsWin.window({
        onBeforeOpen: function() {
            diaryConfigsGrid.datagrid("clearSelections");
            var options = diaryConfigsGrid.datagrid("options");
            if (!options.rpcParams || !options.rpcParams[0]) return;
            var frozen_cols = diaryConfigsGrid.datagrid(
                "getColumnFields",
                true
            );
            if (options.rpcParams[0].request_type == 7) {
                //hide ck boxes for substance_types
                for (var i = 0; i < frozen_cols.length; i++) {
                    if (frozen_cols[i] == "ck") {
                        diaryConfigsGrid.datagrid("hideColumn", "ck");
                        return true;
                    }
                }
            } else {
                if (frozen_cols.length > 0)
                    diaryConfigsGrid.datagrid("showColumn", "ck");
            }
        }
    });
    if (isAdmin) {
        diaryExpensesReportBtn.show();
    }
    ab_lines.init();
    //event types
    jQuery("#diary-settings-event-types").bind("click", function() {
        //change window options
        diarySettingsWin.window("resize", {
            width: 300,
            height: 400
        });
        diarySettingsWin.window("center");
        diarySettingsWin.window("setTitle", "Типове дейности");
        //init grid
        initEventTypesGrid();
        //open window
        diarySettingsWin.window("open");
    });
    jQuery("#btn-save-event-type").bind("click", function() {
        var getChecked = diaryConfigsGrid.datagrid("getChecked");

        var requestObj = {};

        requestObj.name = jQuery("#event-type-name > input").val();
        requestObj.request_type = 1;

        if (is_edit) {
            requestObj.id = getChecked[0].id;
        }
        if (requestObj.name) {
            TF.Rpc.Diary.DiaryAuxiliaryItems.saveItem(requestObj)
                .done(function() {
                    diaryConfigsGrid.datagrid("clearChecked");
                    jQuery("#win-add-edit-event-type").window("close");
                    diaryConfigsGrid.datagrid("reload");
                })
                .fail(function(errorObj) {
                    jQuery.messager.alert("Грешка", errorObj.getMessage());
                });
        } else {
            jQuery.messager.alert("Грешка", "Моля задайте тип");
        }
    });

    //event subtypes
    jQuery("#diary-settings-event-subtypes").bind("click", function() {
        //change window options
        diarySettingsWin.window("resize", {
            width: 500,
            height: 400
        });
        diarySettingsWin.window("center");
        diarySettingsWin.window("setTitle", "Видове дейности");
        //init grid
        initEventSubtypesGrid();
        //open window
        diarySettingsWin.window("open");
    });
    jQuery("#btn-save-event-subtype").bind("click", function() {
        if (
            jQuery("#event-subtype-name > input").val() &&
            jQuery("#event-subtype-type > input").combobox("getValue")
        ) {
            var getChecked = diaryConfigsGrid.datagrid("getChecked");
            var requestObj = getEventSubtypeFields();
            requestObj.request_type = 2;
            if (is_edit) {
                requestObj.id = getChecked[0].id;
            }
            TF.Rpc.Diary.DiaryAuxiliaryItems.saveItem(requestObj)
                .done(function(data) {
                    diaryConfigsGrid.datagrid("clearChecked");
                    jQuery("#win-add-edit-event-subtype").window("close");
                    diaryConfigsGrid.datagrid("reload");
                })
                .fail(function(errorObj) {
                    jQuery.messager.alert("Грешка", errorObj.getMessage());
                });
        } else {
            jQuery.messager.alert("Грешка", "Моля попълнете всички полета!");
        }
    });

    //machine types
    jQuery("#diary-settings-machine-types").bind("click", function() {
        //change window options
        diarySettingsWin.window("resize", {
            width: 500,
            height: 400
        });
        diarySettingsWin.window("center");
        diarySettingsWin.window("setTitle", "Типове машини");
        //init grid
        initMachineTypesGrid();
        //open window
        diarySettingsWin.window("open");
    });
    jQuery("#btn-save-machine-type").bind("click", function() {
        var getChecked = diaryConfigsGrid.datagrid("getChecked");

        var requestObj = {};

        requestObj.name = jQuery("#machine-type-name > input").val();
        requestObj.request_type = 3;

        if (is_edit) {
            requestObj.id = getChecked[0].id;
        }

        if (requestObj.name) {
            TF.Rpc.Diary.DiaryAuxiliaryItems.saveItem(requestObj)
                .done(function(data) {
                    diaryConfigsGrid.datagrid("clearChecked");
                    jQuery("#win-add-edit-machine-type").window("close");
                    diaryConfigsGrid.datagrid("reload");
                })
                .fail(function(errorObj) {
                    jQuery.messager.alert("Грешка", errorObj.getMessage());
                });
        } else {
            jQuery.messager.alert("Грешка", "Моля попълнете всички полета!");
        }
    });

    //machines
    jQuery("#diary-settings-machines").bind("click", function() {
        //change window options
        diarySettingsWin.window("resize", {
            width: 1024,
            height: 400
        });
        diarySettingsWin.window("center");
        diarySettingsWin.window("setTitle", "Машини");
        //init grid
        initMachinesGrid();
        //open window
        diarySettingsWin.window("open");
    });
    jQuery("#btn-save-machine").bind("click", function() {
        var getChecked = diaryConfigsGrid.datagrid("getChecked");

        var requestObj = getMachineEditData();

        requestObj.request_type = 4;

        if (is_edit) {
            requestObj.id = getChecked[0].id;
        }

        if (requestObj.number && requestObj.type_id) {
            TF.Rpc.Diary.DiaryAuxiliaryItems.saveItem(requestObj)
                .done(function(data) {
                    diaryConfigsGrid.datagrid("clearChecked");
                    jQuery("#win-add-edit-machine").window("close");
                    diaryConfigsGrid.datagrid("reload");
                })
                .fail(function(errorObj) {
                    jQuery.messager.alert("Грешка", errorObj.getMessage());
                });
        } else {
            jQuery.messager.alert(
                "Грешка",
                "Полетата за номер и тип са задължителни!"
            );
        }
    });

    //attachment types
    jQuery("#diary-settings-attachment-types").bind("click", function() {
        //change window options

        diarySettingsWin.window("resize", {
            width: 300,
            height: 400
        });
        diarySettingsWin.window("center");
        diarySettingsWin.window("setTitle", "Типове прикачен инвентар");
        //init grid
        initAttachmentTypesGrid();
        //open window
        diarySettingsWin.window("open");
    });
    jQuery("#btn-save-attachment-type").bind("click", function() {
        var getChecked = diaryConfigsGrid.datagrid("getChecked");

        var requestObj = {};

        requestObj.name = jQuery("#attachment-type-name > input").val();
        requestObj.request_type = 5;

        if (is_edit) {
            requestObj.id = getChecked[0].id;
        }

        if (requestObj.name) {
            TF.Rpc.Diary.DiaryAuxiliaryItems.saveItem(requestObj)
                .done(function(data) {
                    diaryConfigsGrid.datagrid("clearChecked");
                    jQuery("#win-add-edit-attachment-type").window("close");
                    diaryConfigsGrid.datagrid("reload");
                })
                .fail(function(errorObj) {
                    jQuery.messager.alert("Грешка", errorObj.getMessage());
                });
        } else {
            jQuery.messager.alert("Грешка", "Моля задайте тип");
        }
    });

    //attachments
    jQuery("#diary-settings-attachments").bind("click", function() {
        //change window options
        diarySettingsWin.window("resize", {
            width: 1024,
            height: 400
        });
        diarySettingsWin.window("center");
        diarySettingsWin.window("setTitle", "Прикачени инвентари");
        //init grid
        initAttachmentsGrid();
        //open window
        diarySettingsWin.window("open");
    });
    jQuery("#btn-save-attachment").bind("click", function() {
        var getChecked = diaryConfigsGrid.datagrid("getChecked");

        var requestObj = getAttachmentEditData();

        requestObj.request_type = 6;

        if (is_edit) {
            requestObj.id = getChecked[0].id;
        }

        if (requestObj.number && requestObj.type_id) {
            TF.Rpc.Diary.DiaryAuxiliaryItems.saveItem(requestObj)
                .done(function(data) {
                    diaryConfigsGrid.datagrid("clearChecked");
                    jQuery("#win-add-edit-attachment").window("close");
                    diaryConfigsGrid.datagrid("reload");
                })
                .fail(function(errorObj) {
                    jQuery.messager.alert("Грешка", errorObj.getMessage());
                });
        } else {
            jQuery.messager.alert(
                "Грешка",
                "Полетата за номер и тип са задължителни!"
            );
        }
    });

    //substance technics
    jQuery("#diary-settings-substance-technics").bind("click", function() {
        //change window options

        diarySettingsWin.window("resize", {
            width: 300,
            height: 400
        });
        diarySettingsWin.window("center");
        diarySettingsWin.window("setTitle", "Техники за прилагане на ПРЗ");
        //init grid
        initSubstanceTechnicsGrid();
        //open window
        diarySettingsWin.window("open");
    });
    jQuery("#btn-save-substance-technic").bind("click", function() {
        var getChecked = diaryConfigsGrid.datagrid("getChecked");

        var requestObj = {};

        requestObj.name = jQuery("#substance-technic-name > input").val();
        requestObj.request_type = 8;

        if (is_edit) {
            requestObj.id = getChecked[0].id;
        }
        if (requestObj.name) {
            TF.Rpc.Diary.DiaryAuxiliaryItems.saveItem(requestObj)
                .done(function(data) {
                    jQuery("#win-add-edit-substance-technic").window("close");
                    diaryConfigsGrid.datagrid("reload");
                })
                .fail(function(errorObj) {
                    jQuery.messager.alert("Грешка", errorObj.getMessage());
                });
        } else {
            jQuery.messager.alert("Грешка", "Моля задайте тип");
        }
    });

    jQuery("#substance-types > input").combobox({
        data: [
            { key: "fertilizer", label: "Тор" },
            { key: "chemical_treatment", label: "ПРЗ" }
        ],
        editable: false,
        textField: "label",
        valueField: "key",
        value: "",
        multiple: true
    });

    //substance types
    jQuery("#diary-settings-substance-types").bind("click", function() {
        //change window options
        diarySettingsWin.window("resize", {
            width: 300,
            height: 400
        });
        diarySettingsWin.window("center");
        diarySettingsWin.window("setTitle", "Продукти");
        //init grid
        initSubstanceTypesGrid();
        //open window
        diarySettingsWin.window("open");
    });

    //substance types
    jQuery("#diary-settings-pending-substance-types").bind("click", function() {
        //change window options
        diarySettingsWin.window("resize", {
            width: 300,
            height: 400
        });
        diarySettingsWin.window("center");
        diarySettingsWin.window("setTitle", "Чакащи продукти");
        //init grid
        initSubstanceTypesGrid('pending');
        //open window
        diarySettingsWin.window("open");
    });

    jQuery("#btn-save-substance-type").bind("click", function() {
        var row = jQuery("#diary-configs-tables").datagrid("getSelected");

        var requestObj = {};

        requestObj.name = jQuery("#substance-type-name > input").val();

        var allOptions = jQuery("#substance-types > input").combobox("options")
            .data;
        var selectedOptions = jQuery("#substance-types > input").combobox(
            "getValues"
        );

        if (selectedOptions.length) {
            var jsonOptions = {};
            for (i = 0; i < selectedOptions.length; i++) {
                var json_key = selectedOptions[i];
                jsonOptions[json_key] = true;
            }
        }
        if (!jQuery.isEmptyObject(jsonOptions)) {
            requestObj.options = jsonOptions;
        }

        requestObj.request_type = 7;

        if (is_edit) {
            requestObj.id = row.id;
        }
        if (requestObj.name) {
            TF.Rpc.Diary.DiaryAuxiliaryItems.saveItem(requestObj)
                .done(function(data) {
                    jQuery("#win-add-edit-substance-type").window("close");
                    diaryConfigsGrid.datagrid("reload");
                })
                .fail(function(errorObj) {
                    jQuery.messager.alert("Грешка", errorObj.getMessage());
                });
        } else {
            jQuery.messager.alert("Грешка", "Моля задайте тип");
        }
    });

    //performers
    jQuery("#diary-settings-performers").bind("click", function() {
        //change window options
        diarySettingsWin.window("resize", {
            width: 600,
            height: 400
        });
        diarySettingsWin.window("center");
        diarySettingsWin.window("setTitle", "Изпълнители");
        //init grid
        initPerformersGrid();
        //open window
        diarySettingsWin.window("open");
    });
    jQuery("#btn-save-performer").bind("click", function() {
        var getChecked = diaryConfigsGrid.datagrid("getChecked");

        var requestObj = getEditPerformetFieldsData();
        requestObj.request_type = 9;

        if (is_edit) {
            requestObj.id = getChecked[0].id;
        }

        if (requestObj.name && requestObj.perf_title) {
            TF.Rpc.Diary.DiaryAuxiliaryItems.saveItem(requestObj)
                .done(function(data) {
                    jQuery("#win-add-edit-performer").window("close");
                    diaryConfigsGrid.datagrid("reload");
                })
                .fail(function(errorObj) {
                    jQuery.messager.alert("Грешка", errorObj.getMessage());
                });
        } else {
            jQuery.messager.alert(
                "Грешка",
                "Полетата за имена и длъжност са задължителни!"
            );
        }
    });

    //units
    jQuery("#diary-settings-units").bind("click", function() {
        //change window options
        diarySettingsWin.window("resize", {
            width: 300,
            height: 400
        });
        diarySettingsWin.window("center");
        diarySettingsWin.window("setTitle", "Мерни единици");
        //init grid
        initSubstanceUnitsGrid();
        //open window
        diarySettingsWin.window("open");
    });
    jQuery("#btn-save-unit").bind("click", function() {
        var getChecked = diaryConfigsGrid.datagrid("getChecked");

        var requestObj = {};

        requestObj.name = jQuery("#substance-unit-name").val() + "/дка";
        requestObj.request_type = 10;

        if (is_edit) {
            requestObj.id = getChecked[0].id;
        }
        if (requestObj.name) {
            TF.Rpc.Diary.DiaryAuxiliaryItems.saveItem(requestObj)
                .done(function(data) {
                    jQuery("#win-add-edit-units").window("close");
                    diaryConfigsGrid.datagrid("reload");
                })
                .fail(function(errorObj) {
                    jQuery.messager.alert("Грешка", errorObj.getMessage());
                });
        } else {
            jQuery.messager.alert("Грешка", "Моля задайте тип");
        }
    });

    //expenses
    jQuery("#diary-settings-expenses").bind("click", function() {
        //change window options
        var diaryExpensesWindow = jQuery("#win-diary-expenses");
        diaryExpensesWindow.window("resize", {
            width: 600,
            height: 400
        });
        diaryExpensesWindow.window("center");
        diaryExpensesWindow.window("setTitle", "Разходи");
        //init grid
        iniExpensesGrid();
        //open window
        diaryExpensesWindow.window("open");
    });
    jQuery("#diary-expenses-valid-from").datebox({});
    jQuery("#diary-expenses-valid-to").datebox({});
    jQuery("#diary-expenses-performer").combobox({
        url: "index.php?diary-rpc=diary-configs-combobox",
        rpcParams: [
            {
                request_type: 9
            }
        ],
        editable: true,
        textField: "name",
        valueField: "id",
        groupField: "group",
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    jQuery("#diary-expenses-activity").combobox({
        url: "index.php?diary-rpc=diary-configs-combobox",
        rpcParams: [
            {
                request_type: 2
            }
        ],
        editable: true,
        textField: "name",
        valueField: "id",
        groupField: "group",
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    jQuery("#diary-expenses-price").numberbox({
        precision: 2,
        min: 0
    });
    jQuery("#diary-expenses-cancel").on("click", function(e) {
        e.preventDefault();
        winAddEditExpenses.window("close");
    });
    jQuery("#diary-expenses-save").on("click", function(e) {
        e.preventDefault();
        var expensesData = {
            activity_id: diaryExpensesActivity.combobox("getValue"),
            performer_id: diaryExpensesPerformer.combobox("getValue"),
            valid_from: diaryExpensesValidFrom.datebox("getValue"),
            valid_to: diaryExpensesValidTo.datebox("getValue"),
            price: diaryExpensesPrice.numberbox("getValue")
        };
        var editWarningMessage =
            "Разходите за всички мероприятия свързани" +
            " с редактираните стойности ще бъдат променени.";

        if (winAddEditExpenses.data("isEditWin")) {
            jQuery.messager.confirm("Внимание", editWarningMessage, function(
                r
            ) {
                if (!r) {
                    return;
                }
                saveExpenses(expensesData, true);
            });
            return;
        }
        saveExpenses(expensesData);
    });

    jQuery("#btn-projection").on("click", function(e) {
        var ids = ZPselected.plot_ids;
        var offset = jQuery("input[name='offset']").val();

        previewNavigationData(ids, offset);
    });

    jQuery("#btn-export").on("click", function(e){
        e.preventDefault();
        var ids = ZPselected.plot_ids;
        var united = jQuery("input[name='single-separate-file']:checked").val();
        var offset = jQuery("input[name='offset']").val();
        var isUnited = true;
        if (united == "separate") {
            isUnited = false;
        }

        exportData("exportTrimble", ids, isUnited, true, offset);
    });

    function saveExpenses(expensesData, isUpdate) {
        var expenseId;

        if (isUpdate) {
            var selected = expensesGrid.datagrid("getSelected");
            expenseId = selected.id;

            TF.Rpc.Diary.DiaryExpenses.updateExpenses(expensesData, expenseId)
                .done(function(data) {
                    winAddEditExpenses.window("close");
                    expensesGrid.datagrid("loadRpc");
                })
                .fail(function(errorObj) {
                    if (
                        errorObj.is(
                            TF.Rpc.ExceptionsList.EXPENSE_ALREADY_EXISTS
                        )
                    ) {
                        jQuery.messager.alert(
                            "Грешка",
                            TF.Rpc.ExceptionsList.EXPENSE_ALREADY_EXISTS
                                .message,
                            "error"
                        );
                    }
                });
        } else {
            TF.Rpc.Diary.DiaryExpenses.createExpenses(expensesData, expenseId)
                .done(function(data) {
                    winAddEditExpenses.window("close");
                    expensesGrid.datagrid("loadRpc");
                })
                .fail(function(errorObj) {
                    if (
                        errorObj.is(
                            TF.Rpc.ExceptionsList.EXPENSE_ALREADY_EXISTS
                        )
                    ) {
                        jQuery.messager.alert(
                            "Грешка",
                            TF.Rpc.ExceptionsList.EXPENSE_ALREADY_EXISTS
                                .message,
                            "error"
                        );
                    }
                });
        }
    }

    //filters
    jQuery("#search-ekate > input").combobox({
        url: "index.php?common-rpc=ekate-combobox",
        valueField: "ekate",
        textField: "text",
        filter: function(q, row) {
            var opts = jQuery(this).combobox("options");
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if (text.indexOf(find) != -1 || value.indexOf(find) != -1) {
                return true;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    jQuery("#search-culture > input").combobox({
        url: "index.php?common-rpc=culture-combobox",
        valueField: "id",
        textField: "name",
        editable: false,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    jQuery("#search-event-phase > input").combobox({
        url: "index.php?diary-rpc=diary-event-phase-combobox",
        editable: false,
        textField: "name",
        valueField: "id",
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    jQuery("#search-event-subtype > input").combobox({
        url: "index.php?diary-rpc=diary-configs-combobox",
        rpcParams: [
            {
                request_type: 2,
                event_type: 0
            }
        ],
        editable: false,
        textField: "name",
        valueField: "id",
        disabled: true,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    jQuery("#search-event-type > input").combobox({
        url: "index.php?diary-rpc=diary-configs-combobox",
        rpcParams: [
            {
                request_type: 1
            }
        ],
        editable: false,
        textField: "name",
        valueField: "id",
        onSelect: function(record) {
            //init subtype combobox
            jQuery("#search-event-subtype > input").combobox({
                url: "index.php?diary-rpc=diary-configs-combobox",
                rpcParams: [
                    {
                        request_type: 2,
                        event_type: record.id
                    }
                ],
                editable: false,
                textField: "name",
                valueField: "id",
                disabled: false,
                loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
            });
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    jQuery("#search-event-performer > input").combobox({
        url: "index.php?diary-rpc=diary-configs-combobox",
        rpcParams: [
            {
                request_type: 9
            }
        ],
        editable: false,
        textField: "name",
        valueField: "id",
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    jQuery("#search-event-date-from > input").datebox();
    jQuery("#search-event-date-to > input").datebox();
    /** @deprecated */
    jQuery("#btn-get-wialon-message").bind("click", function() {
        var obj = {};

        trackMachineID = jQuery("#wialon-message-machine")
            .find("> input")
            .combobox("getValue");
        trackDate = jQuery("#wialon-message-date")
            .find("> input")
            .datebox("getValue");

        if (trackMachineID == "") {
            jQuery.messager.alert("Грешка", "Не сте избрали машина!");
            return;
        }

        if (trackDate == "") {
            jQuery.messager.alert("Грешка", "Не сте избрали дата!");
            return;
        }

        trackTimeFrom = jQuery("#wialon-message-time-from input").timespinner(
            "getValue"
        );
        trackTimeTo = jQuery("#wialon-message-time-to input").timespinner(
            "getValue"
        );
        var trackName =
            jQuery("#wialon-message-machine > input").combobox("getText") +
            " / " +
            jQuery("#wialon-message-date > input").datebox("getText");

        //change button name
        jQuery("#btn-get-tracks > span > span").html(trackName);

        obj.machine_id = trackMachineID;
        obj.date = trackDate;
        obj.time_from = trackTimeFrom;
        obj.time_to = trackTimeTo;

        TF.Rpc.Diary.WialonActions.getMessages(obj)
            .done(function(data) {
                loadMapMessages(data);
                jQuery("#win-set-message-params").window("close");
            })
            .fail(function(errorObj) {
                if (
                    errorObj !== undefined &&
                    errorObj.is(TF.Rpc.ExceptionsList.INVALID_OR_EXPIRED_TOKEN)
                ) {
                    jQuery("#win-get-wialon-token").window("open");
                    return;
                }
                jQuery.messager.alert("Грешка", errorObj.getMessage());
            });
    });
    jQuery("#btn-get-tracks").bind("click", function() {
        var opts = plotsTrackWindow.window("options");
        if (opts.closed == true) plotsTrackWindow.window("open");
        plotsTrackWindow.window("resize", { width: 840, height: 280 });
        plotsTrackWindow.window("center");
        return false;
    });
    //tools
    jQuery("#tool-zoom-to-layer").bind("click", function() {
        var currentLayer = ZP_TREE_CTRL.tree("getSelected");
        if (currentLayer) {
            map.zoomToExtent(
                new OpenLayers.Bounds.fromString(
                    currentLayer["attributes"].extent
                ).transform(
                    new OpenLayers.Projection("EPSG:32635"),
                    map.getProjectionObject()
                )
            );
            return;
        }
        currentLayer = COMBO_TREE_CTRL.combotree("tree").tree("getSelected");
        if (currentLayer) {
            map.zoomToExtent(
                new OpenLayers.Bounds.fromString(
                    currentLayer["attributes"].extent
                ).transform(
                    new OpenLayers.Projection("EPSG:32635"),
                    map.getProjectionObject()
                )
            );
        }
    });
    jQuery("#tool-measure-line").bind("click", function() {
        var options = jQuery("#tool-measure-line").linkbutton("options");
        if (!options.disabled) {
            unselectAll();
            jQuery("#tool-measure-line").linkbutton("select");
            chooseControl("line");
        }
        return false;
    });
    jQuery("#tool-measure-polygon").bind("click", function() {
        var options = jQuery("#tool-measure-polygon").linkbutton("options");
        if (!options.disabled) {
            unselectAll();
            jQuery("#tool-measure-polygon").linkbutton("select");
            chooseControl("polygon");
        }
        return false;
    });
    jQuery("#tool-zoomout").bind("click", function() {
        var options = jQuery("#tool-zoomout").linkbutton("options");
        if (!options.disabled) {
            unselectAll();
            jQuery("#tool-zoomout").linkbutton("select");
            chooseControl("zoomout");
        }
        return false;
    });
    jQuery("#tool-zoomin").bind("click", function() {
        var options = jQuery("#tool-zoomin").linkbutton("options");
        if (!options.disabled) {
            unselectAll();
            jQuery("#tool-zoomin").linkbutton("select");
            chooseControl("zoomin");
        }
        return false;
    });
    jQuery("#tool-panzoom").bind("click", function() {
        var options = jQuery("#tool-panzoom").linkbutton("options");
        if (!options.disabled) {
            unselectAll();
            jQuery("#tool-panzoom").linkbutton("select");
            chooseControl("none");
        }
        return false;
    });
    jQuery("#tool-select-zplot").bind("click", function() {
        var options = jQuery(this).linkbutton("options");
        if (options.disabled) {
            return false;
        }
        if (ZPselected.table) {
            unselectAll();
            jQuery(this).linkbutton("select");
            chooseControl("none");
            map.events.register("click", map, getZPlotData);
        } else {
            jQuery.messager.alert(
                "Грешка",
                'Избор на земеделски парцели от карта може да се изпълни само когато активният слой е от тип "Земеделски парцели"!'
            );
        }
        return false;
    });
    jQuery("#tool-clear-selection").bind("click", function() {
        var options = jQuery("#tool-clear-selection").linkbutton("options");
        if (options.disabled) {
            return false;
        }
        ab_lines.getAbLinesVectorLayer().removeAllFeatures();
        vectors.removeAllFeatures();
        unselectAll();
        chooseControl("none");
    });

    jQuery("#tool-export").bind("click", function() {
        if (ZPselected.plot_ids.length == 0) {
            return jQuery.messager.alert(
                "Грешка",
                "Трябва да изберете поне един имот"
            );
        }
        jQuery("#win-export").window("open");
    });

    jQuery("#btn-choose-diary-type").bind("click", function() {
        jQuery("#win-choose-diary-type").window("open");
    });
    jQuery("#btn-save-event").bind("click", function() {
        var evntValidator = isEventValid();
        if (!evntValidator.valid) {
            if (!evntValidator.details.timeFrame_greater_zero) {
                return jQuery.messager.alert(
                    "Грешка",
                    "Началният и крайният час не може да съвпадат"
                );
            }
            return jQuery.messager.alert(
                "Грешка",
                "Моля, попълнете всички задължителни полета.",
                "warning"
            );
        }
        //set final callback params
        var product_data = product_datagrid.datagrid("getData");
        if (product_data.rows && product_data.rows.length > 0) {
            //validate products before save or return;
            var products_valid = validateProductsAndProduces(product_datagrid, ['substance_id', 'substance_unit_type', 'substance_technic_id']);
            if (products_valid !== true) {
                jQuery("#events_win_tabs").tabs("select", 2);
                return jQuery.messager.alert(
                    "Грешка",
                    "Моля, попълнете всички задължителни полета в продуктовата таблица.",
                    "warning"
                );
            }
        }
        var eventData = jQuery("#zplot-events-tables").datagrid("getSelected");
        var obj = getEditEventFields();
        obj.event_id = eventData ? eventData.id : null;
        obj.farming_id = ZPselected.farming;
        obj.year_id = ZPselected.year;
        obj.plot_id = ZPselected.plot_id;

        TF.Rpc.Diary.ZPlotEventsGrid.saveEvent(obj)
            .done(function(data) {
                clearAddEditEventPanel();
                jQuery("#win-add-edit-event").window("close");
                jQuery("#zplot-events-tables").datagrid("reload");
            })
            .fail(function(errorObj) {
                jQuery.messager.alert("Грешка", errorObj.getMessage());
            });
    });
    initKvsFilters();
});

function enableDisableTabs(selector, isValid) {
    var enabled = isValid ? "enableTab" : "disableTab";
    jQuery(selector).tabs(enabled, 1);
    jQuery(selector).tabs(enabled, 2);
    jQuery(selector).tabs(enabled, 3);
    jQuery(selector).tabs(enabled, 4);
}

function isDate(dateString) {
    var minDate = new Date("1970-01-01 00:00:01");
    var maxDate = new Date("2038-01-19 03:14:07");
    var date = new Date(dateString);
    return (
        date !== "Invalid Date" &&
        !isNaN(date) &&
        (date > minDate && date < maxDate)
    );
}

function initPlotTracksGrid() {
    var toolbar = null;
    var searchBtn = null;

    jQuery(searchBtn).bind("click", function() {
        var params = get_ZP_TRACK_window_data();
        var d1 = new Date(params.date_from);
        var d2 = new Date(params.date_to);
        var diff = Math.abs(d1 - d2) / 86400000;
        ZP_TRACK_GRID.treegrid("options").rpcParams = [params];
        if (diff > 7) {
            jQuery.messager.confirm(
                "Потвърждение",
                "Тази функция може да отнеме няколко минути при периоди повече от една седмица. Жалаете ли да продължите?",
                function(r) {
                    if (r != false) {
                        ZP_TRACK_GRID.treegrid("reload");
                    }
                }
            );
        } else {
            ZP_TRACK_GRID.treegrid("reload");
        }
    });
}

var product_grid_data = null;
/* we make an ajax call to get data from diary_config table, to be used in the product datagrid */
(function initEditorsValuesForGrid() {
    jQuery
        .ajax("index.php?diary-rpc=diary-configs-combobox", {
            type: "POST",
            dataType: "json",
            contentType: "application/json; charset=UTF-8",
            data: JSON.stringify({
                method: "getAll",
                params: [null],
                id: 1,
                jsonrpc: "2.0"
            })
        })
        .done(function(data) {
            product_grid_data = data.result;
            jQuery(function() {
                initAddEditEventsWindow();
            });
        });
})();

function getEventTime() {
    var event = {};
    var ephase = jQuery("#event-phase")
        .find("> input")
        .combobox("getValue");
    if (ephase == undefined || ephase == "") {
        return null;
    }
    event.from = "";
    event.to = "";
    if (ephase == EVENT_PLANNED) {
        var plan_date_from = jQuery("#event-plan-date-from > input").datebox(
            "getValue"
        );
        var plan_time_from = jQuery(
            "#event-plan-time-from > input"
        ).timespinner("getValue");
        var plan_date_to = jQuery("#event-plan-date-to > input").datebox(
            "getValue"
        );
        var plan_time_to = jQuery("#event-plan-time-to > input").timespinner(
            "getValue"
        );
        event.from = plan_date_from + " " + plan_time_from + ":00";
        event.to = plan_date_to + " " + plan_time_to + ":59";
    }
    if (ephase == EVENT_COMPLETE) {
        var complete_date_from = jQuery(
            "#event-complete-date-from > input"
        ).datebox("getValue");
        var complete_time_from = jQuery(
            "#event-complete-time-from > input"
        ).timespinner("getValue");
        var complete_date_to = jQuery(
            "#event-complete-date-to > input"
        ).datebox("getValue");
        var complete_time_to = jQuery(
            "#event-complete-time-to > input"
        ).timespinner("getValue");
        event.from = complete_date_from + " " + complete_time_from + ":00";
        event.to = complete_date_to + " " + complete_time_to + ":59";
    }
    if (!isDate(event.from) || !isDate(event.to)) return null;
    return event;
}

function isValidExpense(expense, event) {
    if (!expense || !event) return false;
    return (
        expense !== null &&
        event !== null &&
        expense.price !== undefined &&
        ZPselected.area !== 0 &&
        isDate(expense.valid_from) &&
        isDate(expense.valid_to) &&
        (expense.valid_from <= event.to && expense.valid_to >= event.from)
    );
}

//init subtype combobox
var last_sub_type_expense_query;

/**
 *
 * @param params {
 *     use_type_id: bool , if true return where query with activity id = event-type combobox value
 *     subtype_id: bool , if true return where query where sub-activity id = subtype_id
 *  }
 */

function loadSubtypeExpense(params) {
    var subTypeCombo = jQuery("#event-subtype > input");
    var init = subTypeCombo.data().hasOwnProperty("combobox");
    if (init == true) {
        subTypeCombo.combobox({ disabled: true });
    }
    var rpcParams = {};
    if (params.use_type_id) {
        rpcParams.type_id = jQuery("#event-type > input").combobox("getValue");
    }
    if (params.subtype_id) {
        rpcParams.subtype_id = params.subtype_id;
    }
    //if subtype_id is present it means we are loading the event from a selected row on the event table
    if (
        last_sub_type_expense_query == JSON.stringify(rpcParams) &&
        !params.subtype_id
    )
        return;
    last_sub_type_expense_query = JSON.stringify(rpcParams);

    if (init == false) {
        subTypeCombo.combobox({
            url: "index.php?diary-rpc=diary-zplot-events-grid",
            rpcMethod: "loadExpensesForEvent",
            rpcParams: [rpcParams],
            editable: false,
            required: true,
            textField: "sub_type_name",
            valueField: "sub_type_id",
            groupField: "group",
            onLoadSuccess: function(data) {
                jQuery(this).combobox("enable");
                if (data.length == 1) {
                    jQuery(this).combobox("setValue", data[0].sub_type_id);
                    data[0].selected = true;
                }
                var expense_cost = null;
                var event = getEventTime();
                var event_validation_obj = isEventValid();
                enableDisableTabs(
                    "#events_win_tabs",
                    event_validation_obj.valid
                );
                for (var i = 0; i < data.length; i++) {
                    var expense = data[i];
                    if (expense.selected == true) {
                        if (isValidExpense(expense, event)) {
                            jQuery("#event-expense_cost").val(
                                expense.price * ZPselected.area
                            );
                        } else {
                            jQuery("#event-expense_cost").val(0);
                        }
                        if (data[i].performer_id !== undefined) {
                            jQuery("#event-performer > input").combobox(
                                "setValue",
                                expense.performer_id
                            );
                            jQuery("#event-performer > input").combobox(
                                "setText",
                                expense.performer_name
                            );
                        }
                        break;
                    }
                }
                updateTotalField();
            },
            onLoadError: function(data) {
                jQuery(this).combobox("enable");
                updateTotalField();
            },
            onSelect: function(record) {
                var event_validation_obj = isEventValid();
                enableDisableTabs(
                    "#events_win_tabs",
                    event_validation_obj.valid
                );
                var event = getEventTime();
                if (!event || !record) {
                    jQuery("#event-expense_cost").val(0);
                    return updateTotalField();
                }
                if (isValidExpense(record, event)) {
                    jQuery("#event-expense_cost").val(
                        record.price * ZPselected.area
                    );
                } else {
                    jQuery("#event-expense_cost").val(0);
                }
                if (record.performer_id !== undefined) {
                    jQuery("#event-performer > input").combobox(
                        "setValue",
                        record.performer_id
                    );
                    jQuery("#event-performer > input").combobox(
                        "setText",
                        record.performer_name
                    );
                }
                updateTotalField();
            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    } else {
        subTypeCombo.combobox("loadRpc", [rpcParams]);
    }
}

function initAddEditEventsWindow() {
    var selectedIndex = null;
    /* array of window widths for each tab */
    var widths = [440, 440, 950, 740, 680];

    //SET THE OPTION FOR THE COMBOBOX EDITORS IN THE DATAGRID
    function getDataForEditors(config_type) {
        return jQuery.grep(product_grid_data, function(n, i) {
            return n.config_type == config_type;
        });
    }

    //SET THE OPTION FOR THE COMBOBOX EDITORS IN THE DATAGRID
    function getSubstancePrz(config_type, substance_id) {
        return jQuery.grep(product_grid_data, function(n, i) {
            return n.config_type == config_type && n.id == substance_id;
        });
    }

    //MAIN WINDOW
    jQuery("#win-add-edit-event").window({
        onLoad: function() {
            var event_validation_obj = isEventValid();
            enableDisableTabs("#events_win_tabs", event_validation_obj.valid);
        },
        onOpen: function() {
            var event_validation_obj = isEventValid();
            enableDisableTabs("#events_win_tabs", event_validation_obj.valid);
            var tab = jQuery("#events_win_tabs");
            if (event_validation_obj.valid == false) tab.tabs("select", 0);
            var index = tab.tabs("getTabIndex", tab.tabs("getSelected"));
            if (jQuery(this).window("options").top < 0)
                jQuery(this).window("move", { top: 50 });
            tab.tabs("resize", { width: "auto", height: "auto" });
            jQuery(this).window("resize", {
                width: widths[index],
                height: "auto"
            });
            jQuery(this).window("center");
        },
        onClose: function() {
            selectedIndex = null;
        }
    });

    //TABS
    jQuery("#events_win_tabs").tabs({
        border: false,
        onSelect: function(title, index) {
            var win = jQuery("#win-add-edit-event");
            win.window("resize", { width: widths[index], height: "auto" });
            jQuery(this).tabs("resize", { width: "auto", height: "auto" });
            if (index == 2)
                jQuery("#events_win_tabs")
                    .find(".datagrid-wrap")
                    .height(240);
            win.window("resize", { width: "auto", height: "auto" });
            win.window("center");
        }
    });

    function update_substance_sub_total(dose, treated_area, unit_price) {
        var sub_total = 0;
        var consumed_qty = 0;
        var price_editor = product_datagrid.datagrid("getEditor", {
            index: selectedIndex,
            field: "price_per_area"
        });
        var consumed_editor = product_datagrid.datagrid("getEditor", {
            index: selectedIndex,
            field: "substance_consumed"
        });
        if (dose > 0 && treated_area > 0 && unit_price > 0) {
            sub_total = unit_price * dose * treated_area;
        }
        if (dose > 0 && treated_area > 0) {
            consumed_qty = dose * treated_area;
        }
        jQuery(price_editor.target).numberbox("setValue", sub_total);
        jQuery(consumed_editor.target).numberbox("setValue", consumed_qty);
        update_products_total_cost(selectedIndex, sub_total);
    }

    //PRODUCT DATAGRID ON TAB 3
    product_datagrid.datagrid({
        title: "Информация за препрати",
        singleSelect: true,
        fitColumns: false,
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        fit: true,
        border: true,
        rownumbers: true,
        showFooter: true,
        toolbar: [
            {
                id: "event-product-btn_add",
                text: "Добавяне",
                iconCls: "icon-add",
                handler: function() {
                    var new_row = {
                        editing: true,
                        substance_id: null,
                        substance_dose: null,
                        substance_unit_type: null,
                        substance_unit_price: null,
                        treated_area: ZPselected.area,
                        price_per_area: null,
                        pest_name: "",
                        substance_technic_id: null,
                        quarantine_period: null
                    };
                    product_datagrid.datagrid("insertRow", {
                        index: 0,
                        row: new_row
                    });
                    product_datagrid.datagrid("beginEdit", 0);
                }
            },
            {
                id: "event-product-btn_edit",
                text: "Редактиране",
                iconCls: "icon-edit",
                handler: function() {
                    if (selectedIndex != null) {
                        product_datagrid.datagrid("beginEdit", selectedIndex);
                        product_datagrid.datagrid("selectRow", selectedIndex);
                    }
                }
            },
            {
                id: "event-product-btn-apply",
                text: "Запази",
                iconCls: "icon-ok",
                handler: function() {
                    var row = product_datagrid.datagrid("getSelected");
                    if (!row) return false;
                    if (row.editing == true && selectedIndex != null) {
                        product_datagrid.datagrid("endEdit", selectedIndex);
                        product_datagrid.datagrid("selectRow", selectedIndex);
                        update_products_total_cost();
                    }
                }
            },
            {
                id: "event-product-btn_cancel",
                text: "Откажи",
                iconCls: "icon-redirect",
                handler: function() {
                    var row = product_datagrid.datagrid("getSelected");
                    if (!row) return false;
                    if (row.editing == true && selectedIndex != null) {
                        product_datagrid.datagrid("cancelEdit", selectedIndex);
                        product_datagrid.datagrid("selectRow", selectedIndex);
                    }
                }
            },
            {
                id: "event-product-btn_delete",
                text: "Премахване",
                iconCls: "icon-delete",
                handler: function() {
                    if (
                        selectedIndex != null &&
                        selectedIndex >= 0 &&
                        getProducts() !== null
                    ) {
                        jQuery.messager.confirm(
                            "Confirm",
                            "Сигурни ли сте?",
                            function(r) {
                                if (r) {
                                    product_datagrid.datagrid(
                                        "deleteRow",
                                        selectedIndex
                                    );
                                    product_datagrid.datagrid("selectRow", 0);
                                    update_products_total_cost();
                                }
                            }
                        );
                    }
                }
            }
        ],
        columns: [
            [
                {
                    field: "substance_id",
                    title: "<b>вид</b>",
                    width: 110,
                    align: "left",
                    editor: {
                        type: "combobox",
                        options: {
                            data: getDataForEditors(7),
                            editable: false,
                            valueField: "id",
                            textField: "name",
                            required: true,
                            align: "left",
                            onShowPanel: function() {
                                var target = jQuery(this).parent();
                                product_datagrid.datagrid(
                                    "selectRow",
                                    getRowIndex(target)
                                );
                            }
                        }
                    }
                },
                {
                    field: "substance_dose",
                    title: "<b>разходна<br>норма<b>",
                    align: "center",
                    editor: {
                        type: "numberbox",
                        options: {
                            precision: 3,
                            min: 0,
                            align: "center",
                            onChange: function(dose, old_val) {
                                var row = product_datagrid.datagrid(
                                    "getSelected"
                                );
                                if (
                                    selectedIndex != null &&
                                    selectedIndex >= 0 &&
                                    row
                                ) {
                                    var treated_area = product_datagrid
                                        .datagrid("getEditor", {
                                            index: selectedIndex,
                                            field: "treated_area"
                                        })
                                        .target.numberbox("getValue");
                                    var unit_price = product_datagrid
                                        .datagrid("getEditor", {
                                            index: selectedIndex,
                                            field: "substance_unit_price"
                                        })
                                        .target.numberbox("getValue");
                                    update_substance_sub_total(
                                        dose,
                                        treated_area,
                                        unit_price
                                    );
                                }
                            }
                        }
                    }
                },
                {
                    field: "substance_unit_type",
                    align: "left",
                    title: "<b>мерна<br>единица<b>",
                    width: 80,
                    editor: {
                        type: "combobox",
                        options: {
                            editable: false,
                            textField: "name",
                            valueField: "id",
                            required: true,
                            data: getDataForEditors(10),
                            align: "left",
                            onShowPanel: function() {
                                var target = jQuery(this).parent();
                                product_datagrid.datagrid(
                                    "selectRow",
                                    getRowIndex(target)
                                );
                            }
                        }
                    }
                },
                {
                    field: "substance_unit_price",
                    title: "<b>Единична<br>стойност (лв.)<b>",
                    align: "center",
                    editor: {
                        type: "numberbox",
                        options: {
                            precision: 2,
                            min: 0,
                            align: "center",
                            onChange: function(unit_price, old_val) {
                                var row = product_datagrid.datagrid(
                                    "getSelected"
                                );
                                if (
                                    selectedIndex != null &&
                                    selectedIndex >= 0 &&
                                    row
                                ) {
                                    var treated_area = product_datagrid
                                        .datagrid("getEditor", {
                                            index: selectedIndex,
                                            field: "treated_area"
                                        })
                                        .target.numberbox("getValue");
                                    var dose = product_datagrid
                                        .datagrid("getEditor", {
                                            index: selectedIndex,
                                            field: "substance_dose"
                                        })
                                        .target.numberbox("getValue");
                                    update_substance_sub_total(
                                        dose,
                                        treated_area,
                                        unit_price
                                    );
                                }
                            }
                        }
                    }
                },
                {
                    field: "treated_area",
                    title: "<b>площ<b>",
                    width: 80,
                    align: "center",
                    editor: {
                        type: "numberbox",
                        options: {
                            precision: 2,
                            min: 0,
                            align: "center",
                            onChange: function(treated_area, old_val) {
                                var row = product_datagrid.datagrid(
                                    "getSelected"
                                );
                                if (
                                    selectedIndex != null &&
                                    selectedIndex >= 0 &&
                                    row
                                ) {
                                    var unit_price = product_datagrid
                                        .datagrid("getEditor", {
                                            index: selectedIndex,
                                            field: "substance_unit_price"
                                        })
                                        .target.numberbox("getValue");
                                    var dose = product_datagrid
                                        .datagrid("getEditor", {
                                            index: selectedIndex,
                                            field: "substance_dose"
                                        })
                                        .target.numberbox("getValue");
                                    update_substance_sub_total(
                                        dose,
                                        treated_area,
                                        unit_price
                                    );
                                }
                            }
                        }
                    }
                },
                {
                    /* this is the COST field, calculated for each row*/
                    field: "price_per_area",
                    title: "<b>Отчетен<br>разход (лв.)<b>",
                    align: "center",
                    editor: {
                        type: "numberbox",
                        options: {
                            precision: 2,
                            min: 0,
                            readonly: true,
                            disabled: true,
                            value: 0,
                            align: "center"
                        }
                    }
                },
                {
                    field: "pest_name",
                    title: "<b>Вредител<b>",
                    width: 80,
                    align: "center",
                    editor: { type: "textbox", align: "center" },
                    formatter: function(value, row, index) {
                        if (value) return value.substring(0, 100);
                    }
                },
                {
                    field: "substance_technic_id",
                    title: "<b>Приложение<b>",
                    width: 120,
                    align: "left",
                    editor: {
                        type: "combobox",
                        options: {
                            align: "left",
                            editable: false,
                            valueField: "id",
                            textField: "name",
                            required: true,
                            data: getDataForEditors(8),
                            onShowPanel: function() {
                                var target = jQuery(this).parent();
                                product_datagrid.datagrid(
                                    "selectRow",
                                    getRowIndex(target)
                                );
                            }
                        }
                    }
                },
                {
                    field: "quarantine_period",
                    title: "<b>Карантинен<br>срок<b>",
                    width: 60,
                    align: "center",
                    editor: {
                        type: "numberbox",
                        options: { precision: 0, min: 0, align: "center" }
                    }
                },
                {
                    field: "substance_consumed",
                    title: "<b>Употребено<br>количество<b>",
                    width: 60,
                    align: "center",
                    editor: {
                        type: "numberbox",
                        options: {
                            precision: 2,
                            min: 0,
                            align: "center",
                            readonly: "true",
                            disabled: true
                        }
                    }
                }
            ]
        ],
        onBeginEdit: function(index, row) {
            if (!row.values) row.values = row;
            // we update here data from row.values to each editor for each column
            var columns = product_datagrid.datagrid("getColumnFields");
            for (var i = 0; i < columns.length; i++) {
                var editor = product_datagrid.datagrid("getEditor", {
                    index: index,
                    field: columns[i]
                });
                var value = row.values[columns[i]];
                if (!editor) continue;
                if (editor.type === "checkbox") {
                    /*for PRZ checkbox, the susbstance table dictate if the product is a pesticide or not */
                    jQuery(editor.target).prop(
                        "checked",
                        getSubstancePrz(7, row.substance_id)
                    );
                } else if (editor.type === "combobox") {
                    jQuery(editor.target)[editor.type]("select", value);
                } else {
                    jQuery(editor.target)[editor.type]("setValue", value);
                    if (columns[i] == "treated_area") {
                        var treatedArea = parseFloat(
                            jQuery("#event-treatable_area").numberbox(
                                "getValue"
                            )
                        );
                        if (!isNaN(treatedArea))
                            jQuery(editor.target).numberbox(
                                "options"
                            ).max = treatedArea;
                    }
                }
            }
            jQuery(this).datagrid("selectRow", index);
        },
        onEndEdit: function(index, row) {
            var columns = product_datagrid.datagrid("getColumnFields");
            row.values = {}; // we save and store here data values from the editor
            for (var i = 0; i < columns.length; i++) {
                var field = columns[i];
                var editor = product_datagrid.datagrid("getEditor", {
                    index: index,
                    field: field
                });
                var value = null;
                var text = null;
                if (!editor) continue;
                if (editor.type === "checkbox") {
                    value = jQuery(editor.target).is(":checked");
                    text = value ? "Да" : "Не";
                } else {
                    value = jQuery(editor.target)[editor.type]("getValue");
                    text = jQuery(editor.target)[editor.type]("getText");
                }
                row[field] = text;
                row.values[field] = value;
            }
        },
        onLoadSuccess: function(data) {
            //trigger edit on rows so we can trigger values injection in editors
            if (data.rows.length > 0) {
                var count = data.rows.length;
                for (var i = 0; i < count; i++) {
                    jQuery(product_datagrid).datagrid("beginEdit", i);
                }
                for (i = 0; i < count; i++) {
                    jQuery(product_datagrid).datagrid("endEdit", i);
                }
            } else {
                jQuery("#event-product-btn_add").trigger("click");
            }
            jQuery(this).datagrid("fitColumns");
        },
        onBeforeEdit: function(index, row) {
            if (!row) return false;
            row.editing = true;
            selectedIndex = index;
            jQuery(this).datagrid("refreshRow", index);
        },
        onAfterEdit: function(index, row) {
            row.editing = false;
            jQuery(this).datagrid("refreshRow", index);
        },
        onCancelEdit: function(index, row) {
            row.editing = false;
            jQuery(this).datagrid("refreshRow", index);
        },
        onSelect: function(index, row) {
            selectedIndex = index;
            if (!row) {
                jQuery("#event-product-btn_edit").linkbutton("disable");
                return;
            }
            if (row && row.editing === true) {
                jQuery("#event-product-btn-apply").linkbutton("enable");
                jQuery("#event-product-btn_cancel").linkbutton("enable");
                jQuery("#event-product-btn_edit").linkbutton("disable");
            } else {
                jQuery("#event-product-btn-apply").linkbutton("disable");
                jQuery("#event-product-btn_cancel").linkbutton("disable");
                jQuery("#event-product-btn_edit").linkbutton("enable");
            }
        },
        onBeforeLoad: function() {
            return ZPselected.plot_id !== 0;
        },
        onClickRow: function(index, row) {
            selectedIndex = index;
            jQuery(this).datagrid("selectRow", index);
        },
        onClickCell: function(index, field, value) {
            selectedIndex = index;
        },
        onDblClickRow: function(index, row) {
            selectedIndex = index;
            jQuery(this).datagrid("beginEdit", index);
        },
        loadFilter: function(data) {
            if (!data || data.error) {
                return { total: 0, rows: [] };
            } else {
                if (!data.total && !data.rows) {
                    return { total: data.length, rows: data };
                } else {
                    return data;
                }
            }
        }
    });
    var total_cost_products = jQuery(
        '<td><label style="margin-left: 180px;" for="event-substance-total-money"><b>Обща стойност </b></label><input readonly disabled id="event-substance-total-money" type="text" style="width:80px;text-align: center;"/></td>'
    );
    jQuery("#events_win_tabs .datagrid-toolbar table tr:last").append(
        total_cost_products
    );
    /*--------------DATE-TIME FIELDS-------------------*/
    jQuery("#event-plan-date-from")
        .find("input")
        .datebox({
            value: new Date().toString("dd-MMM-yyyy"),
            required: true,
            onChange: onEventTimeRangeChange,
            onSelect: onEventTimeRangeChange
        });
    jQuery("#event-plan-date-to")
        .find("input")
        .datebox({
            value: new Date().toString("dd-MMM-yyyy"),
            required: true,
            onChange: onEventTimeRangeChange,
            onSelect: onEventTimeRangeChange
        });
    jQuery("#event-plan-time-from")
        .find("> input")
        .timespinner({
            value: "00:00:00",
            showSeconds: true,
            required: true,
            onChange: onEventTimeRangeChange,
            onSpinUp: onEventTimeRangeChange,
            onSpinDown: onEventTimeRangeChange
        });
    jQuery("#event-plan-time-to")
        .find("> input")
        .timespinner({
            value: "23:59:59",
            showSeconds: true,
            required: true,
            onChange: onEventTimeRangeChange,
            onSpinUp: onEventTimeRangeChange,
            onSpinDown: onEventTimeRangeChange
        });
    jQuery("#event-complete-date-from")
        .find("> input")
        .datebox({
            value: new Date().toString("dd-MMM-yyyy"),
            required: true,
            onChange: onEventTimeRangeChange,
            onSelect: onEventTimeRangeChange
        });
    jQuery("#event-complete-date-to")
        .find("> input")
        .datebox({
            value: new Date().toString("dd-MMM-yyyy"),
            required: true,
            onChange: onEventTimeRangeChange,
            onSelect: onEventTimeRangeChange
        });
    jQuery("#event-complete-time-from")
        .find("> input")
        .timespinner({
            value: "00:00:00",
            showSeconds: true,
            required: true,
            onChange: onEventTimeRangeChange,
            onSpinUp: onEventTimeRangeChange,
            onSpinDown: onEventTimeRangeChange
        });
    jQuery("#event-complete-time-to")
        .find("> input")
        .timespinner({
            value: "23:59:59",
            showSeconds: true,
            required: true,
            onChange: onEventTimeRangeChange,
            onSpinUp: onEventTimeRangeChange,
            onSpinDown: onEventTimeRangeChange
        });

    function onEventTimeRangeChange() {
        var event_validation_obj = isEventValid();
        enableDisableTabs("#events_win_tabs", event_validation_obj.valid);
        if (!jQuery("#win-add-edit-event").window("options").closed) {
            var expense_id = jQuery("#event-subtype > input").combobox(
                "getValue"
            );
            var event = getEventTime();
            if (
                expense_id == undefined ||
                (expense_id == "" && event !== null)
            ) {
                return;
            }
            var all_expense_data = jQuery("#event-subtype > input").combobox(
                "getData"
            );

            for (var i = 0; i < all_expense_data.length; i++) {
                var expense = all_expense_data[i];
                if (isValidExpense(expense, event)) {
                    all_expense_data[i].group = "Разходи";
                } else {
                    all_expense_data[i].group = "Вид";
                }
            }
            jQuery("#event-subtype > input").combobox(
                "loadData",
                all_expense_data
            );
        }
    }

    jQuery("#event-amortization")
        .find("input")
        .numberbox({
            precision: 2,
            min: 0,
            onChange: function(new_val, old_val) {
                updateTotalField();
            }
        });
    jQuery("#event-rent-cost input[type='text']").numberbox({
        disabled: true,
        precision: 2,
        min: 0,
        onChange: function(new_val, old_val) {
            updateTotalField();
        }
    });
    jQuery("#event-total input[type='text']").numberbox({
        disabled: true,
        precision: 2,
        min: 0
    });
    jQuery("#event-movement-fuel input[type='text']").numberbox({
        disabled: true,
        precision: 2,
        min: 0
    });
    jQuery("#event-parking-fuel input[type='text']").numberbox({
        disabled: true,
        precision: 2,
        min: 0
    });
    jQuery("#event-start-fuel-level input[type='text']").numberbox({
        disabled: true,
        precision: 2,
        min: 0
    });
    jQuery("#event-end-fuel-level input[type='text']").numberbox({
        disabled: true,
        precision: 2,
        min: 0
    });
    jQuery("#event-avg-engine-revs input[type='text']").numberbox({
        disabled: true,
        precision: 2,
        min: 0
    });
    jQuery("#event-avg-speed input[type='text']").numberbox({
        disabled: true,
        precision: 2,
        min: 0
    });
    jQuery("#event-mileage input[type='text']").numberbox({
        disabled: true,
        precision: 2,
        min: 0
    });
    jQuery("#event-time-in input").timespinner({
        disabled: true,
        showSeconds: true
    });
    jQuery("#event-time-out input").timespinner({
        disabled: true,
        showSeconds: true
    });
    jQuery("#event-total-time-in input").timespinner({
        disabled: true,
        showSeconds: true
    });
    jQuery("#event-movement-time input").timespinner({
        disabled: true,
        showSeconds: true
    });
    jQuery("#event-parking-time input").timespinner({
        disabled: true,
        showSeconds: true
    });
    // keep for backward compatibilty
    jQuery("#event-plan-fuel-cost")
        .find("input")
        .numberbox({ precision: 2, min: 0 });
    jQuery("#event-final-fuel-cost")
        .find("input")
        .numberbox({ precision: 2, min: 0 });
    jQuery("#event-processed-area > input").numberbox({
        disabled: false,
        precision: 2,
        min: 0,
        onChange: function(processed_area, old_val) {
            var calculate_by = jQuery("#event-fuel-unit").combobox("getValue");
            var cost_per_liter = jQuery("#event-per-litre-fuel-cost").numberbox(
                "getValue"
            );
            var fuel_consumed = jQuery(
                "#event-fuel-consumed-qty > input"
            ).numberbox("getValue");
            if (
                cost_per_liter > 0 &&
                fuel_consumed > 0 &&
                processed_area > 0 &&
                calculate_by == 2
            ) {
                jQuery("#event-fuel-cost > input").numberbox(
                    "setValue",
                    cost_per_liter * fuel_consumed * processed_area
                );
            }
            updateTotalField();
        }
    });

    jQuery("#event-fuel-consumed-qty input[type='text']").numberbox({
        disabled: false,
        precision: 2,
        min: 0,
        onChange: function(fuel_consumed, old_val) {
            var calculate_by = jQuery("#event-fuel-unit").combobox("getValue");

            var cost_per_liter = jQuery("#event-per-litre-fuel-cost").numberbox(
                "getValue"
            );
            if (fuel_consumed > 0 && cost_per_liter > 0 && calculate_by == 1) {
                jQuery("#event-fuel-cost > input").numberbox(
                    "setValue",
                    fuel_consumed * cost_per_liter
                );
            }
            var processed_area = jQuery(
                "#event-processed-area > input"
            ).numberbox("getValue");
            if (
                cost_per_liter > 0 &&
                fuel_consumed > 0 &&
                processed_area > 0 &&
                calculate_by == 2
            ) {
                jQuery("#event-fuel-cost > input").numberbox(
                    "setValue",
                    cost_per_liter * fuel_consumed * processed_area
                );
            }
            updateTotalField();
        }
    });
    jQuery("#event-per-litre-fuel-cost").numberbox({
        precision: 2,
        min: 0,
        onChange: function(cost_per_liter, old_val) {
            var calculate_by = jQuery("#event-fuel-unit").combobox("getValue");

            var fuel_consumed = jQuery(
                "#event-fuel-consumed-qty > input"
            ).numberbox("getValue");
            if (cost_per_liter > 0 && fuel_consumed > 0 && calculate_by == 1) {
                jQuery("#event-fuel-cost > input").numberbox(
                    "setValue",
                    cost_per_liter * fuel_consumed
                );
            }
            var processed_area = jQuery(
                "#event-processed-area > input"
            ).numberbox("getValue");
            if (
                cost_per_liter > 0 &&
                fuel_consumed > 0 &&
                processed_area > 0 &&
                calculate_by == 2
            ) {
                jQuery("#event-fuel-cost > input").numberbox(
                    "setValue",
                    cost_per_liter * fuel_consumed * processed_area
                );
            }
            updateTotalField();
        }
    });
    jQuery("#event-fuel-unit").combobox({
        textField: "name",
        valueField: "id",
        required: true,
        data: [
            { id: 1, name: "литра", selected: true },
            { id: 2, name: "Литър / дка" }
        ],
        onSelect: function(record) {
            if (!record) return;
            if (record.id == 1) {
                jQuery("#event-fuel-unit-label").text("Общ отчетен разход (л)");
                jQuery("#processed-area-label").css("visibility", "hidden");
                jQuery("#event-processed-area").css("visibility", "hidden");
            }
            if (record.id == 2) {
                jQuery("#event-fuel-unit-label").text("Отчетен разход (л/дка)");
                jQuery("#processed-area-label").css("visibility", "visible");
                jQuery("#event-processed-area").css("visibility", "visible");
            }
            var calculate_by = record.id;
            var cost_per_liter = jQuery("#event-per-litre-fuel-cost").numberbox(
                "getValue"
            );
            var fuel_consumed = jQuery(
                "#event-fuel-consumed-qty > input"
            ).numberbox("getValue");
            if (cost_per_liter > 0 && fuel_consumed > 0 && calculate_by == 1) {
                jQuery("#event-fuel-cost > input").numberbox(
                    "setValue",
                    cost_per_liter * fuel_consumed
                );
                updateTotalField();
            }
            var processed_area = jQuery(
                "#event-processed-area > input"
            ).numberbox("getValue");
            if (
                cost_per_liter > 0 &&
                fuel_consumed > 0 &&
                processed_area > 0 &&
                calculate_by == 2
            ) {
                jQuery("#event-fuel-cost > input").numberbox(
                    "setValue",
                    cost_per_liter * fuel_consumed * processed_area
                );
                updateTotalField();
            }
        },
        onLoadSuccess: function(record) {
            if (record == undefined) return;
            if (record.id == 1 || record.id == undefined) {
                jQuery("#event-fuel-unit-label").text("Общ отчетен разход (л)");
                jQuery("#processed-area-label").css("visibility", "hidden");
                jQuery("#event-processed-area").css("visibility", "hidden");
            }
            if (record.id == 2) {
                jQuery("#event-fuel-unit-label").text("Отчетен разход (л/дка)");
                jQuery("#processed-area-label").css("visibility", "visible");
                jQuery("#event-processed-area").css("visibility", "visible");
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery("#event-fuel-cost > input").numberbox({
        disabled: true,
        precision: 2,
        min: 0
    });

    jQuery("#event-phase")
        .find("> input")
        .combobox({
            data: [{ id: 1, name: "Планирана" }, { id: 2, name: "Изпълнена" }],
            editable: false,
            textField: "name",
            required: true,
            valueField: "id",
            onLoadSuccess: function(data) {
                if (data.result) data = data.result;
                var value = jQuery(this).combobox("getValue");
                if (value == "") {
                    jQuery(this).combobox("setValue", EVENT_PLANNED);
                }
                //handle case EVENT TYPE IS COMPLETED (id 2)
                if (value == EVENT_COMPLETE) {
                    jQuery("#wialon_reload_btn").linkbutton("enable");
                    //enable final fields
                    jQuery("#event-avg-engine-revs .numberbox-f").numberbox(
                        "enable"
                    );
                    jQuery("#event-avg-speed .numberbox-f").numberbox("enable");
                    jQuery("#event-mileage .numberbox-f").numberbox("enable");

                    jQuery(
                        "#event-complete-time-from .timespinner-f"
                    ).timespinner("enable");
                    jQuery(
                        "#event-complete-time-to .timespinner-f"
                    ).timespinner("enable");

                    jQuery("#event-time-in .timespinner-f").timespinner(
                        "enable"
                    );
                    jQuery("#event-time-out .timespinner-f").timespinner(
                        "enable"
                    );
                    jQuery("#event-total-time-in .timespinner-f").timespinner(
                        "enable"
                    );
                    jQuery("#event-movement-time .timespinner-f").timespinner(
                        "enable"
                    );
                    jQuery("#event-parking-time .timespinner-f").timespinner(
                        "enable"
                    );
                } else {
                    jQuery("#wialon_reload_btn").linkbutton("disable");
                }
            },
            onSelect: function(record) {
                if (!record) return;
                // IN CASE EVENT TYPE == COMPLETED (id:2), WE CAN MAKE CALCULATION ON TRIP'S FUEL.
                var enable = record.id == EVENT_COMPLETE ? "enable" : "disable";
                if (enable == "enable") {
                    //show correct dates
                    jQuery("#plan-dates").hide();
                    jQuery("#plan-times").hide();
                    jQuery("#complete-dates").show();
                    jQuery("#complete-times").show();
                } else {
                    jQuery("#complete-dates").hide();
                    jQuery("#complete-times").hide();
                    jQuery("#plan-dates").show();
                    jQuery("#plan-times").show();
                }
                jQuery("#wialon_reload_btn").linkbutton(enable);
                //enable final fields
                jQuery("#event-avg-engine-revs .numberbox-f").numberbox(enable);
                jQuery("#event-avg-speed .numberbox-f").numberbox(enable);
                jQuery("#event-mileage .numberbox-f").numberbox(enable);

                jQuery("#event-complete-time-from .timespinner-f").timespinner(
                    enable
                );
                jQuery("#event-complete-time-to .timespinner-f").timespinner(
                    enable
                );

                jQuery("#event-time-in .timespinner-f").timespinner(enable);
                jQuery("#event-time-out .timespinner-f").timespinner(enable);
                jQuery("#event-total-time-in .timespinner-f").timespinner(
                    enable
                );
                jQuery("#event-movement-time .timespinner-f").timespinner(
                    enable
                );
                jQuery("#event-parking-time .timespinner-f").timespinner(
                    enable
                );
                resetFuelTab();
                resetFarmTrackTab();
            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });

    jQuery("#event-type > input").combobox({
        url: "index.php?diary-rpc=diary-configs-combobox",
        rpcParams: [
            {
                request_type: 1
            }
        ],
        editable: false,
        required: true,
        textField: "name",
        valueField: "id",
        onLoadSuccess: function() {
            loadSubtypeExpense({ use_type_id: true });
        },
        onSelect: function(record) {
            var event_validation_obj = isEventValid();
            enableDisableTabs("#events_win_tabs", event_validation_obj.valid);
            loadSubtypeExpense({ use_type_id: true });
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery("#event-performer > input").combobox({
        url: "index.php?diary-rpc=diary-configs-combobox",
        rpcParams: [
            {
                request_type: 9
            }
        ],
        editable: false,
        textField: "name",
        valueField: "id",
        groupField: "group",
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery("#event-treatable_area").numberbox({
        disabled: false,
        precision: 2,
        min: 0,
        max: ZPselected.area || 0,
        onChange: function(new_val, old_val) {
            var products = product_datagrid.datagrid("getData");
            if (products.rows.length && new_val != "") {
                for (var i = 0; i < products.rows.length; i++) {
                    jQuery(product_datagrid).datagrid("beginEdit", i);
                    var editor = product_datagrid.datagrid("getEditor", {
                        index: i,
                        field: "treated_area"
                    });
                    jQuery(editor.target).numberbox("options").max = parseFloat(
                        new_val
                    );
                    if (
                        jQuery(editor.target).numberbox("getValue") >
                        parseFloat(new_val)
                    ) {
                        jQuery(editor.target).numberbox(
                            "setValue",
                            parseFloat(new_val)
                        );
                    }
                    jQuery(product_datagrid).datagrid("endEdit", i);
                }
            }
        }
    });

    function resetGeofenceCrossingFields() {
        jQuery("#event-time-in > input").timespinner("setValue");
        jQuery("#event-time-out > input").timespinner("setValue");
        jQuery("#event-parking-time > input").timespinner("setValue");
    }

    jQuery("#event-machine")
        .find("> input")
        .combobox({
            url: "index.php?diary-rpc=diary-configs-combobox",
            rpcParams: [
                {
                    request_type: 34
                }
            ],
            groupField: "group",
            groupFormatter: function(group) {
                if (group.indexOf("FarmTrack") !== -1) {
                    return (
                        '<span style="color:#e22625"><b>' +
                        group +
                        "</b></span>"
                    );
                }
                return (
                    '<span style="color:#3b4454"><b>' + group + "</b></span>"
                );
            },
            editable: false,
            textField: "name",
            valueField: "id",
            onSelect: function(selection) {
                var fuel_report = null;
                var geofence_report = null;
                var row = null;
                if (
                    !selection ||
                    !selection.name ||
                    selection.group.indexOf("FarmTrack") === -1
                )
                    return;
                for (var key in fuel_report_obj.data.unit_group_zones_visit) {
                    if (
                        !fuel_report_obj.data.unit_group_zones_visit.hasOwnProperty(
                            key
                        )
                    ) {
                        continue;
                    }
                    row = fuel_report_obj.data.unit_group_zones_visit[key];
                    //grouping is the unit name from the wialon template report
                    if (row.Grouping != selection.name) {
                        continue;
                    }
                    geofence_report = row;
                    break;
                }
                for (key in fuel_report_obj.data.unit_group_engine_hours) {
                    if (
                        !fuel_report_obj.data.unit_group_engine_hours.hasOwnProperty(
                            key
                        )
                    ) {
                        continue;
                    }
                    row = fuel_report_obj.data.unit_group_engine_hours[key];
                    //grouping is the unit name from the wialon template report
                    if (row.Grouping != selection.name) {
                        continue;
                    }
                    fuel_report = row;
                    break;
                }
                if (fuel_report != null) {
                    setFuelFields(fuel_report);
                } else {
                    resetFuelFields();
                }
                if (geofence_report != null) {
                    resetFarmTrackTab();
                    resetFuelFields();
                    var time_in = geofence_report["Time in"].v;
                    var time_out = geofence_report["Time out"].v;
                    time_in = new Date(time_in * 1000);
                    time_out = new Date(time_out * 1000); // guarda se e un oggetto
                    var parking = geofence_report["Parkings duration"];
                    jQuery("#event-avg-engine-revs > input").numberbox(
                        "setValue",
                        geofence_report["Avg engine revs"]
                    );
                    jQuery("#event-avg-speed > input").numberbox(
                        "setValue",
                        geofence_report["Avg speed"]
                    );
                    jQuery("#event-mileage > input").numberbox(
                        "setValue",
                        parseFloat(geofence_report["Mileage"])
                    );
                    jQuery("#event-time-in")
                        .find("> input")
                        .timespinner(
                            "setValue",
                            time_in.getHours() +
                                ":" +
                                time_in.getMinutes() +
                                ":" +
                                time_in.getSeconds()
                        );
                    jQuery("#event-movement-fuel > input").numberbox(
                        "setValue",
                        geofence_report["Consumed"]
                    );
                    jQuery("#event-time-out")
                        .find("> input")
                        .timespinner(
                            "setValue",
                            time_out.getHours() +
                                ":" +
                                time_out.getMinutes() +
                                ":" +
                                time_out.getSeconds()
                        );
                    jQuery("#event-total-time-in > input").timespinner(
                        "setValue",
                        geofence_report["Total time"]
                    );
                    jQuery("#event-movement-time > input").timespinner(
                        "setValue",
                        geofence_report["Total time"]
                    );
                    jQuery("#event-parking-time")
                        .find("> input")
                        .timespinner("setValue", parking);
                } else {
                    resetGeofenceCrossingFields();
                }
            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    jQuery("#event-attachment > input").combobox({
        url: "index.php?diary-rpc=diary-configs-combobox",
        rpcParams: [
            {
                request_type: 6,
                record_none: true
            }
        ],
        editable: false,
        textField: "name",
        valueField: "id",
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery("#event-machine-rented > input").change(function() {
        if (jQuery("#event-machine-rented > input").is(":checked")) {
            jQuery("#event-rent-cost .numberbox-f").numberbox("enable");
        } else {
            jQuery("#event-rent-cost .numberbox-f").numberbox("disable");
            jQuery("#event-rent-cost .numberbox-f").numberbox("setValue");
        }
    });
    if (jQuery("#event-machine-rented > input").is(":checked")) {
        jQuery("#event-rent-cost .numberbox-f").numberbox("enable");
    }
}

// grid method
function getRowIndex(target) {
    var tr = jQuery(target).closest("tr.datagrid-row");
    return parseInt(tr.attr("datagrid-row-index"));
}

function get_ZP_TRACK_window_data() {
    var params = {};
    params.date_from = jQuery("#track_dateFrom").datebox("getValue");
    params.date_to = jQuery("#track_dateTo").datebox("getValue");
    params.time_from = jQuery("#track_timeFrom").timespinner("getValue");
    params.time_to = jQuery("#track_timeTo").timespinner("getValue");
    params.farming = ZPselected.farming;
    params.year = ZPselected.year;
    params.plot_id = ZPselected.plot_id;
    return params;
}

function show_selected_wialonTrack() {
    var track_data = ZP_TRACK_GRID.treegrid("getSelected");
    if (!track_data) return;
    var obj = {};
    obj.machine_id = track_data["unit_id"];
    var d = new Date(track_data["Time Start"]["v"] * 1000);
    obj.time_from =
        ("0" + d.getDate()).slice(-2) +
        "-" +
        ("0" + (d.getMonth() + 1)).slice(-2) +
        "-" +
        d.getFullYear() +
        " " +
        ("0" + d.getHours()).slice(-2) +
        ":" +
        ("0" + d.getMinutes()).slice(-2);
    d = new Date(track_data["Time End"]["v"] * 1000);
    obj.time_to =
        ("0" + d.getDate()).slice(-2) +
        "-" +
        ("0" + (d.getMonth() + 1)).slice(-2) +
        "-" +
        d.getFullYear() +
        " " +
        ("0" + d.getHours()).slice(-2) +
        ":" +
        ("0" + d.getMinutes()).slice(-2);
    TF.Rpc.Diary.WialonActions.getMessages(obj)
        .done(function(data) {
            loadMapMessages(data);
            map.updateSize();
        })
        .fail(function(errorObj) {
            if (errorObj.is(TF.Rpc.ExceptionsList.INVALID_OR_EXPIRED_TOKEN)) {
                jQuery("#win-get-wialon-token").window("open");
                return;
            }
            jQuery.messager.alert("Грешка", errorObj.getMessage());
        });
}

function getZPlotData(e) {
    var layerData = COMBO_TREE_CTRL.combotree("tree").tree("getSelected");
    if (layerData.attributes.table) {
        var obj = {};
        obj.layer = layerData.attributes.table;
        obj.x = parseInt(e.xy.x);
        obj.y = parseInt(e.xy.y);
        obj.bbox = map.getExtent().toBBOX();
        obj.width = map.size.w;
        obj.height = map.size.h;
        TF.Rpc.Diary.DiaryMap.getMapZPlotInfo(obj)
            .done(function(data) {
                selectZPlotTreeNode(data);
            })
            .fail(function(errorObj) {
                if (errorObj.is(TF.Rpc.ExceptionsList.MAP_EMPTY_AREA)) {
                    jQuery.messager.alert(
                        "Грешка",
                        TF.Rpc.ExceptionsList.MAP_EMPTY_AREA.message,
                        "error"
                    );
                }
            });
    }
}

function populate_units_combobox(data) {
    var combo = jQuery("#event-machine").find("> input");
    var combo_data = combo.combobox("getData");
    var temp = [];
    // colelct units from group 'Всички машини'
    for (var i = 0; i < combo_data.length; i++) {
        if (combo_data[i].group == "Всички машини") {
            temp.push(combo_data[i]);
        }
    }
    if (data == undefined || data.farm_track_units == undefined) {
        combo.combobox("loadData", temp);
        return;
    }
    //keep reference of fuel report so we can
    //fill the form on machine selection without calling the api
    fuel_report_obj = data;
    var wialon_grp = [];
    var machines_not_in_db = [];
    /*
     here we need to assign the correct ID to the wialon's machine.
     we use the ones that we already have in the category all machines, from the db.
     If there is a new machine coming from the report, and we don't have and id for it,
     an alert is displayed to the user
     */
    for (var key in data.farm_track_units) {
        if (!data.farm_track_units.hasOwnProperty(key)) continue;
        row = data.farm_track_units[key];
        row.group = "Данни от FarmTrack";
        row.id = row.wialon_id;
        delete row.wialon_id;
        wialon_grp.push(row);
    }
    /* here we merge the two categories */
    if (machines_not_in_db.length > 0)
        jQuery.messager.alert(
            "внимание",
            "Моля, синхронизирайте Вашите машини преди изпълнение на тази функция"
        );
    /* jQuery('#events_win_tabs').tabs('select', 2);*/
    combo.combobox("loadData", wialon_grp.concat(temp));
}

function resetUnitsComboBox() {
    fuel_report_obj = null;
    var combo = jQuery("#event-machine").find("> input");
    var combo_data = combo.combobox("getData");
    // here we remove the entries with group "danni ot farmtrack" , to reset the combobox to his basic state
    for (var i = 0; i < combo_data.length; i++) {
        if (combo_data[i].group !== "Всички машини") {
            combo_data.splice(i, 1);
        }
    }
    combo.combobox("loadData", combo_data);
}

/* global var to hold report data about unit's fuel */
var fuel_report_obj = null;

function checkTimeFrame(obj) {
    var d1 = null;
    var d2 = null;
    if (
        obj.date_from !== undefined &&
        obj.date_to !== undefined &&
        obj.time_from !== undefined &&
        obj.time_to !== undefined
    ) {
        d1 = new Date(obj.date_from + " " + obj.time_from);
        d2 = new Date(obj.date_to + " " + obj.time_to);
    }
    if (
        obj.complete_date_from !== undefined &&
        obj.complete_date_to !== undefined &&
        obj.complete_time_from !== undefined &&
        obj.complete_time_to !== undefined
    ) {
        d1 = new Date(obj.complete_date_from + " " + obj.complete_time_from);
        d2 = new Date(obj.complete_date_to + " " + obj.complete_time_to);
    }
    if (
        obj.plan_date_from !== undefined &&
        obj.plan_date_to !== undefined &&
        obj.plan_time_from !== undefined &&
        obj.plan_time_to !== undefined
    ) {
        d1 = new Date(obj.plan_date_from + " " + obj.plan_time_from);
        d2 = new Date(obj.plan_date_to + " " + obj.plan_time_to);
    }
    if (d2 - d1 <= 0) {
        return false;
    }
    return true;
}

/* whne pressing the wialon button */
function getFuelUnitsPlot() {
    var plot = ZP_TREE_CTRL.tree("getSelected");
    if (!plot) return jQuery.messager.alert("Грешка", "Няма избран парцел!");
    var event_form_data = getEditEventFields();
    var obj = {};

    obj.farming_id = plot["attributes"].farming;
    obj.year_id = plot["attributes"].year;
    obj.plot_id = plot.id;
    obj.date_from = event_form_data.complete_date_from;
    obj.date_to = event_form_data.complete_date_to;
    obj.time_from = event_form_data.complete_time_from;
    obj.time_to = event_form_data.complete_time_to;

    obj.timezoneOffset = new Date(
        obj.date_from + " " + obj.time_from
    ).getTimezoneOffset();
    if (checkTimeFrame(obj) === false) {
        return jQuery.messager.alert(
            "Грешка",
            "Началният и крайният час не може да съвпадат"
        );
    }
    TF.Rpc.Diary.WialonActions.getFuelUnitsPlot(obj)
        .done(function(data) {
            jQuery("#win-add-edit-event").window("open");
            populate_units_combobox(data);
        })
        .fail(function(errorObj) {
            if (errorObj.is(TF.Rpc.ExceptionsList.INVALID_OR_EXPIRED_TOKEN)) {
                jQuery("#win-get-wialon-token").window("open");
                return;
            }
            jQuery.messager.alert("Грешка", errorObj.getMessage());
        });
}

function findUnitByName(name) {
    var combo = jQuery("#event-machine").find("> input");
    var combo_data = combo.combobox("getData");
    for (var i = 0; i < combo_data.length; i++) {
        if (combo_data[i].name == name) {
            return combo_data[i].id;
        }
    }
    return null;
}

/* when creating an event from the track window */
function enter_event() {
    var track_data = ZP_TRACK_GRID.treegrid("getSelected");
    if (!track_data) return;
    var obj = {};
    obj.phase_id = EVENT_COMPLETE;
    var d = new Date(track_data["Time Start"]["v"] * 1000);
    obj.complete_date_from =
        d.getFullYear() +
        "-" +
        ("0" + (d.getMonth() + 1)).slice(-2) +
        "-" +
        ("0" + d.getDate()).slice(-2);
    obj.complete_time_from =
        ("0" + d.getHours()).slice(-2) +
        ":" +
        ("0" + d.getMinutes()).slice(-2) +
        ":" +
        ("0" + d.getSeconds()).slice(-2);
    d = new Date(track_data["Time End"]["v"] * 1000);
    obj.complete_date_to =
        d.getFullYear() +
        "-" +
        ("0" + (d.getMonth() + 1)).slice(-2) +
        "-" +
        ("0" + d.getDate()).slice(-2);
    obj.complete_time_to =
        ("0" + d.getHours()).slice(-2) +
        ":" +
        ("0" + d.getMinutes()).slice(-2) +
        ":" +
        ("0" + d.getSeconds()).slice(-2);
    //db id, not wialon id
    obj.machine_id = findUnitByName(track_data["unit_name"]);
    obj.time_in = obj.complete_time_from;
    obj.time_out = obj.complete_time_to;
    obj.total_time_in = track_data["Total time"];
    obj.completed_mileage = parseFloat(track_data["Mileage"]);
    obj.movement_time = track_data["movement_duration"];
    obj.parking_time = track_data["Parkings duration"];
    obj.start_fuel =
        track_data._table === "Trips"
            ? track_data["Initial fuel level"]
            : "n.a.";
    obj.end_fuel =
        track_data._table === "Trips" ? track_data["Final fuel level"] : "n.a.";
    obj.movement_fuel = track_data["fuel_spent"];
    obj.total_fuel_cost = track_data["fuel_spent"];
    obj.parking_fuel = 0;
    obj.avg_engine_revs = track_data["Avg engine revs"];
    obj.avg_speed = track_data["Avg speed"];
    obj.fuel_unit = 1;
    setEditEventFields(obj);
    jQuery("#events_win_tabs").tabs("select", 0);
    jQuery("#win-add-edit-event").window("open");
}

function unixTimeStampToString(unix_timestamp) {
    var date = new Date(unix_timestamp * 1000);
    var hours = date.getHours();
    var minutes = "0" + date.getMinutes();
    var seconds = "0" + date.getSeconds();
    var formattedTime =
        hours + ":" + minutes.substr(-2) + ":" + seconds.substr(-2);
    return formattedTime;
}

function adjustForTimezone(date) {
    var timeOffsetInMS = date.getTimezoneOffset() * 60000;
    date.setTime(date.getTime() - timeOffsetInMS);
    return date;
}

function updateTotalField() {
    var total_cost = 0;
    var rent_cost = parseFloat(
        jQuery("#event-rent-cost > input").numberbox("getValue")
    );
    var amortization_cost = parseFloat(
        jQuery("#event-amortization > input").numberbox("getValue")
    );
    var fuel_cost = parseFloat(
        jQuery("#event-fuel-cost > input").numberbox("getValue")
    );
    var products_cost = parseFloat(
        jQuery("#event-substance-total-money").val()
    );
    var expense_cost = parseFloat(jQuery("#event-expense_cost").val());
    if (!isNaN(rent_cost)) total_cost += rent_cost;
    if (!isNaN(amortization_cost)) total_cost += amortization_cost;
    if (!isNaN(fuel_cost)) total_cost += fuel_cost;
    if (!isNaN(products_cost)) total_cost += products_cost;
    if (!isNaN(expense_cost)) total_cost += expense_cost;
    jQuery("#event-total > input").numberbox(
        "setValue",
        isNaN(total_cost) ? 0 : total_cost
    );
}

/** @deprecated */
function getWialonReportData() {
    if (trackMachineID == undefined) {
        jQuery.messager.alert("Грешка", "Не е намерена активна следа!");
    } else {
        var obj = {};
        var plotData = ZP_TREE_CTRL.tree("getSelected");

        obj.farming_id = plotData["attributes"].farming;
        obj.year_id = plotData["attributes"].year;
        obj.plot_id = plotData.id;
        /* DATA FROM OLD TRACK WINDOW */
        obj.machine_id = trackMachineID;
        obj.track_date = trackDate;
        obj.track_time_from = trackTimeFrom;
        obj.track_time_to = trackTimeTo;
        TF.Rpc.Diary.WialonActions.getWialonReportData(obj)
            .done(function(data) {
                fillInfoFromWialon(data);
            })
            .fail(function(errorObj) {
                if (
                    errorObj.is(TF.Rpc.ExceptionsList.INVALID_OR_EXPIRED_TOKEN)
                ) {
                    jQuery("#win-get-wialon-token").window("open");
                    return;
                }
                jQuery.messager.alert("Грешка", errorObj.getMessage());
            });
    }
}

/** @deprecated */
function fillInfoFromWialon(data) {
    if (data) {
        var timeInDate = new Date(data.time_in * 1000);
        var timeInHours = timeInDate.getHours();
        var timeInMinutes = timeInDate.getMinutes();
        var timeInSeconds = timeInDate.getSeconds();
        var timeInStr = timeInHours + ":" + timeInMinutes + ":" + timeInSeconds;
        var timeOutDate = new Date(data.time_out * 1000);
        var timeOutHours = timeOutDate.getHours();
        var timeOutMinutes = timeOutDate.getMinutes();
        var timeOutSeconds = timeOutDate.getSeconds();
        var timeOutStr =
            timeOutHours + ":" + timeOutMinutes + ":" + timeOutSeconds;
        jQuery("#event-complete-date-from")
            .find("> input")
            .datebox("setValue", data.complete_date_from);
        jQuery("#event-complete-date-to")
            .find("> input")
            .datebox("setValue", data.complete_date_to);
        jQuery("#event-fuel-cost")
            .find(".numberbox-f")
            .numberbox("setValue", data.consumed_fuel);
        jQuery("#event-avg-engine-revs .numberbox-f").numberbox(
            "setValue",
            data.avg_engine_revs
        );
        jQuery("#event-avg-speed .numberbox-f").numberbox(
            "setValue",
            data.avg_speed
        );
        jQuery("#event-mileage .numberbox-f").numberbox(
            "setValue",
            data.mileage
        );

        jQuery("#event-time-in .timespinner-f").timespinner(
            "setValue",
            timeInStr
        );
        jQuery("#event-time-out .timespinner-f").timespinner(
            "setValue",
            timeOutStr
        );
        jQuery("#event-total-time-in .timespinner-f").timespinner(
            "setValue",
            data.duration_in
        );
        jQuery("#event-movement-time .timespinner-f").timespinner(
            "setValue",
            data.movement_duration
        );
        jQuery("#event-parking-time .timespinner-f").timespinner(
            "setValue",
            data.parking_duration
        );

        jQuery("#event-complete-time-from .timespinner-f").timespinner(
            "setValue",
            data.track_time_from
        );
        jQuery("#event-complete-time-to .timespinner-f").timespinner(
            "setValue",
            data.track_time_to
        );

        var price_per_litre = jQuery("#event-per-litre-fuel-cost").numberbox(
            "getValue"
        );
        if (price_per_litre > 0) {
            jQuery("#event-fuel-cost .numberbox-f").numberbox(
                "setValue",
                price_per_litre * data.consumed_fuel
            );
        } else {
            jQuery("#event-fuel-cost > input").numberbox("setValue");
        }

        //set machine
        jQuery("#event-machine > input").combobox("setValue", data.machine_id);
    }
}

function getWialonToken() {
    var dns = wialonServer;
    // construct login page URL
    var url = dns + "/login.html"; // your site DNS + "/login.html"
    url += "?client_id=" + "Technofarm"; // your application name
    url += "&access_type=" + -1; // access level"
    url += "&activation_time=" + 0; // activation time, 0 = immediately; you can pass any UNIX time value
    url += "&duration=" + 0; // duration, 604800 = one week in seconds
    url += "&flags=" + 0x1; // options, 0x1 = add username in response
    url += "&lang=" + "bg";
    url += "&redirect_uri=" + dns + "/post_token.html"; // if login succeed - redirect to this page

    // listen message with token from login page window
    window.addEventListener("message", tokenRecieved);

    // finally, open login page in new window
    var top = (jQuery(window).height() - 450) / 2;
    var left = (jQuery(window).width() - 550) / 2;
    window.open(
        url,
        "_blank",
        "width=550, height=450, top=" + top + ", left=" + left
    );

    function tokenRecieved(e) {
        // get message from login window
        var msg = e.data;
        if (typeof msg == "string" && msg.indexOf("access_token=") >= 0) {
            // get token
            var token = msg.replace("access_token=", "");
            TF.Rpc.Diary.WialonActions.addWialonToken(token)
                .done(function(data) {
                    jQuery("#win-get-wialon-token").window("close");
                    trackToken = token;
                })
                .fail(function(errorObj) {
                    jQuery.messager.alert("Грешка", errorObj.getMessage());
                });

            // remove "message" event listener
            window.removeEventListener("message", tokenRecieved);
        }
    }
}


previewNavigationData = (ids, lineFeatureOffset) => {
    if (ids.length === 0) return jQuery.messager.alert('Грешка', 'Трябва да изберете поне един имот');

    let requestObj = {};
    requestObj.layer_name = COMBO_TREE_CTRL.combotree("tree").tree("getSelected").attributes.table;
    requestObj.selected_ids = ids;
    requestObj.line_feature_offset = lineFeatureOffset;
    requestObj.layer_type = 1;
    requestObj.layer_id = COMBO_TREE_CTRL.combotree("tree").tree("getSelected").attributes.id;

    TF.Rpc.Map.NavigationLayerData.getLineFeature(requestObj)
    .done(function(dataObj) {
        previewLineFeatureCollection(dataObj);
    })
    .fail(function(errorObj) {
        jQuery.messager.alert("Грешка", errorObj.getMessage());
    });
}

function exportData(
    exportType,
    ids,
    isUnited,
    includeLineFeature,
    lineFeatureOffset
) {
    if (ids.length === 0) return jQuery.messager.alert('Грешка', 'Трябва да изберете поне един имот');
    
    let vectorNavigationLayer = navigation_preview.getNavigationPreviewLayer();
    vectorNavigationLayer.removeAllFeatures();
    
    var obj = {};
    obj.export_type = exportType;
    obj.layer_name = COMBO_TREE_CTRL.combotree("tree").tree(
        "getSelected"
    ).attributes.table;
    obj.selected_ids = ids;
    obj.united = isUnited;
    obj.include_line_feature = includeLineFeature;
    obj.line_feature_offset = lineFeatureOffset;
    obj.layer_type = 1;
    obj.layer_id = COMBO_TREE_CTRL.combotree("tree").tree("getSelected").attributes.id;
    obj.use_filter = 0;

    TF.Rpc.Map.MapExportLayer.export(obj)
        .done(function(dataObj) {
            jQuery("#btn-download-file").attr("href", dataObj);
            jQuery("#win-download").window("open");
        })
        .fail(function(errorObj) {
            jQuery.messager.alert("Грешка", errorObj.getMessage());
        });
}
