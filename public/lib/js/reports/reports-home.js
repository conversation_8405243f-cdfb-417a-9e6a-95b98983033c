'use strict';
var ComboboxData = ComboboxData || undefined;
var reportTree = undefined;
var plotsFilter = plotsFilter || undefined;
var resetDateBoxes = undefined;
var map = map || undefined;
var layers = layers || undefined;
jQuery(function () {

    initMapLayers();
    setUserLastLogin();

    //retrieve GET parameters
    var GET = {};
    location.search.substr(1).split("&").forEach(function (item) {
        GET[decodeURIComponent(item.split("=")[0])] = decodeURIComponent(item.split("=")[1]);
    });

    //change URL without refresh if it's possible
    if (history && history.replaceState) {
        history.replaceState(null, null, 'index.php?page=' + GET.page);
    }

    var PAGE_ALL = false;
    var PAGE_SELECTED = true;
    var selected_report = null;
    reportTree = jQuery('#reports-tree');

    var plot_detailed_table = jQuery('#plots-report-tables-detail');

    var arendator = jQuery('#choose-arendator');
    var subleaseType = jQuery('#choose-sublease-type');

    // TOOLBAR BUTTONS
    var print_all_pages = jQuery('#print-report');
    var print_single_page = jQuery('#print-report-page');
    var export_excel_btn = jQuery('#export-report');
    var filter_menu_btn = jQuery('#filter-report');
    var clear_filter_btn = jQuery('#clear-filter-report');

    var filter_btn = jQuery('#btn-report-filter');
    // plots filters data
    var filters_array = [
        {field: 'report_date_from', id: '#choose-report-date', ctrl: 'datebox'},
        {field: 'report_date', id: '#choose-report-date-to', ctrl: 'datebox'},
        {field: 'report_date_as_of', id: '#choose-report-date-as-of', ctrl: 'datebox'},
        {field: 'report_contract_date', id: '#choose-report-contract-date', ctrl: 'datebox'},
        {field: 'report_contract_date_to', id: '#choose-report-contract-date-to', ctrl: 'datebox'},

        {field: 'report_kad_ident', id: '#search-report-kad-ident', ctrl: 'input'},
        {field: 'report_ekate', id: '#choose-report-ekate', ctrl: 'combobox'},
        {field: 'report_masiv', id: '#search-report-masiv', ctrl: 'input'},
        {field: 'report_plot_number', id: '#search-report-plot-number', ctrl: 'input'},


        {field: 'report_ntp', id: '#choose-report-ntp', ctrl: 'combobox'},
        {field: 'report_category', id: '#choose-report-category', ctrl: 'combobox'},
        {field: 'report_farming', id: '#choose-report-farming', ctrl: 'combobox'},
        {field: 'report_mestnost', id: '#choose-report-mestnost', ctrl: 'combobox'},
        {field: 'report_irrigation', id: '#choose-irrigation-area', ctrl: 'combobox'},
        {field: 'report_arendator', id: '#choose-arendator', ctrl: 'input'},
        {field: 'report_sublease_type', id: '#choose-sublease-type', ctrl: 'combobox'},
        {field: 'report_include_subleases', id: '#include-subleased-in-report', ctrl: 'checkbox'},
        {field: 'report_exclude_inactive', id: '#exclude-inactive-contracts-checkbox', ctrl: 'checkbox'},
        {field: 'report_choose_participation', id: '#choose-participation', ctrl: 'combobox'},
        {field: 'report_choose_renewed', id: '#choose-renewed', ctrl: 'combobox'}
    ];

    function getPlotFiltersValues(report) {
        var filters = {};
        for (var i = 0; i < filters_array.length; i++) {
            var value = '';
            var obj = jQuery(filters_array[i].id);
            var field = filters_array[i].field;
            var type = filters_array[i].ctrl;
            if (type == 'datebox' || type == 'combobox') value = obj[type]('getValue');
            if (type == 'input') value = obj.val();
            if (type == 'checkbox') value = obj.prop('checked');
            filters[field] = value;
        }

        return [{'filters': filters}];
    }

    function initReportTreeData() {
        var plotReports = [
            {id: 1, iconCls: 'icon-datagrid', module: 'Plots',  dim: {width: '570px'}, filter: '#plots_filters', printApi: 'OwnPlotsReportGrid', layout: '#plots-report-layout', name: 'report-own-plots', text: 'Собствена земя', type: 'own_plots', default_filter: ['report_date_as_of'], filterBtn: '#btn-report-filter'},
            {id: 2, iconCls: 'icon-datagrid', module: 'Plots',  dim: {width: '570px'}, filter: '#plots_filters', printApi: 'DetailedOwnPlotsReportGrid', layout: '#plots-report-layout-detail', name: 'report-own-plots-detailed', text: 'Собствена земя - по имоти', type: 'own_plots_detailed', default_filter: ['report_date_as_of'], filterBtn: '#btn-report-filter'},
            {id: 3, iconCls: 'icon-datagrid', module: 'Plots',  dim: {width: '570px'}, filter: '#plots_filters', printApi: 'SubleasedPlotsReportGrid', layout: '#plots-report-layout', name: 'report-subleased-plots', text: 'Отдадена собствена земя', type: 'subleased', default_filter: ['report_date', 'report_date_from', 'report_date_as_of'], filterBtn: '#btn-report-filter'},
            {id: 4, iconCls: 'icon-datagrid', module: 'Plots',  dim: {width: '570px'}, filter: '#plots_filters', printApi: 'ForSubleasePlotsReportGrid', layout: '#plots-report-layout', name: 'report-for-sublease', text: 'Собствена земя, свободна за отдаване под наем/аренда', type: 'for_sublease', default_filter: ['report_date', 'report_date_from', 'report_date_as_of'], filterBtn: '#btn-report-filter'},
            {id: 5, iconCls: 'icon-datagrid', module: 'Plots',  dim: {width: '570px'}, filter: '#plots_filters', printApi: 'HypothecsPlotsReportGrid', layout: '#plots-report-layout', name: 'report-hypothecs', text: 'Ипотеки и тежести', type: 'hypothecs', default_filter: ['report_date', 'report_date_from', 'report_date_as_of'], filterBtn: '#btn-report-filter'},
            {id: 6, iconCls: 'icon-datagrid', module: 'Plots',  dim: {width: '570px'}, filter: '#plots_filters', printApi: 'ForHypothecPlotsReportGrid', layout: '#plots-report-layout', name: 'report-for-hypothec', text: 'Свободна земя за ипотека', type: 'for_hypothec', default_filter: ['report_date', 'report_date_from', 'report_date_as_of'], filterBtn: '#btn-report-filter'},
            {id: 7, iconCls: 'icon-datagrid', module: 'Plots',  dim: {width: '650px'}, filter: '#plots_total_filters', printApi: 'PlotReports', layout: '#total-report-layout', name: 'report-total-area', text: 'Обща площ', type: 'total_area', addTofilterBtn: '#btn-add-to-filter', filterBtn: '#btn-filter-plots'},
            {id: 8, iconCls: 'icon-datagrid', module: 'Plots',  dim: {width: '570px'}, filter: '#plots_filters', printApi: 'RentedPlotsReportGrid', layout: '#plots-report-layout', name: 'report-rented-plots', text: 'Наета/арендована земя', type: 'rented_plots', default_filter: ['report_date', 'report_date_from', 'report_include_subleases', 'report_date_as_of'], filterBtn: '#btn-report-filter'},
            {id: 9, iconCls: 'icon-datagrid', module: 'Plots',  dim: {width: '570px'}, filter: '#plots_filters', printApi: 'UsedPlotsReportGrid', layout: '#plots-report-layout', name: 'report-used-plots', text: 'Използвана земя', type: 'used_plots', default_filter: ['report_date', 'report_date_from', 'report_include_subleases', 'report_date_as_of'], filterBtn: '#btn-report-filter'},
            {id: 10, iconCls: 'icon-datagrid', module: 'Plots', dim: {width: '570px'}, filter: '#plots_filters', printApi: 'SubleasedRentedPlotsReportGrid', layout: '#plots-report-layout', name: 'report-subleased-rented-plots', text: 'Преотдадена/пренаета земя', type: 'subleased_rented_plots', default_filter: ['report_date', 'report_date_from', 'report_date_as_of'], filterBtn: '#btn-report-filter'},
            {id: 11, iconCls: 'icon-datagrid', module: 'Plots', dim: {width: '570px'}, filter: '#plots_filters', printApi: 'ExpiringContractsReportGrid', layout: '#plots-report-layout', name: 'report-rented-expiring-contracts', text: 'Имоти с изтичащи договори', type: 'rented_expiring_contracts', default_filter: ['report_date', 'report_date_from'], filterBtn: '#btn-report-filter'},
            {id: 12, iconCls: 'icon-datagrid', module: 'Plots', dim: {width: '570px'}, filter: '#plots_filters', printApi: 'PlotsInManyContractsReportGrid', layout: '#plots-report-layout', name: 'report-plots-in-many-contracts', text: 'Имоти в повече от един договор', type: 'plots_in_many_contracts', default_filter: ['report_date', 'report_date_from', 'report_date_as_of'], filterBtn: '#btn-report-filter'},
            {id: 13, iconCls: 'icon-datagrid', module: 'Plots', dim: {width: '570px'}, filter: '#plots_filters', printApi: 'ContractsWithOwnerlessPlotsReportGrid', layout: '#plots-report-layout', name: 'report-contracts-with-ownerless-plots', text: 'Договори с имоти без собственици', type: 'contracts_with_ownerless_plots', default_filter: ['report_date', 'report_date_from', 'report_date_as_of'], filterBtn: '#btn-report-filter'},
            {id: 14, iconCls: 'icon-datagrid', module: 'Plots', dim: {width: '570px'}, filter: '#plots_filters', printApi: 'HistoricalPlotsReportGrid', layout: '#plots-report-layout', name: 'report-historical-plots', text: 'Архивни имоти', type: 'historical_plots', default_filter: ['report_date', 'report_date_from', 'report_date_as_of'], filterBtn: '#btn-report-filter'}
        ];
        var diaryReports = [
            {id: 15, iconCls: 'icon-datagrid', module: 'Diary', dim: {width: '300px'}, filter: '#diary_summary_filters', layout: '#diary-report-layout', name: 'diary-summary-by-performer', text: 'Обобщена по механизатор/служител', filterBtn: '#btn-report-by-performer-filter'},
            {id: 16, iconCls: 'icon-datagrid', module: 'Diary', dim: {width: '280px'}, filter: '#diary_detailed_filters', layout: '#diary-report-detailed-layout', name: 'diary-detailed-by-performer', text: 'Подробна по механизатор/служител', filterBtn: '#btn-report-by-performer-detailed-filter'},
            {id: 17, iconCls: 'icon-datagrid', module: 'Diary', dim: {width: '280px'}, filter: '#diary_detailed_filters', layout: '#diary-report-detailed-layout', name: 'diary-expense-report', text: 'Подробна за заработки по механизатор/служител', filterBtn: '#btn-report-by-performer-detailed-filter'},
            {id: 18, iconCls: 'icon-datagrid', module: 'Diary', dim: {width: '280px'}, filter: '#diary_plots_filters', layout: '#diary-plots-report-layout', name: 'diary-plot-report', text: 'По парцел', filterBtn: '#btn-diary-report-filter'},
            {id: 19, iconCls: 'icon-datagrid', module: 'Diary', dim: {width: '280px'}, filter: '#diary_plots_filters', layout: '#diary-plots-report-layout', name: 'diary-plots-report-by-fuel', text: 'По гориво', filterBtn: '#btn-diary-report-filter'},
            {id: 20, iconCls: 'icon-datagrid', module: 'Diary', dim: {width: '280px'}, filter: '#diary_plots_filters', layout: '#diary-plots-report-layout', name: 'diary-plots-report-by-seeds', text: 'По семена', filterBtn: '#btn-diary-report-filter'},
            {id: 21, iconCls: 'icon-datagrid', module: 'Diary', dim: {width: '280px'}, filter: '#diary_plots_filters', layout: '#diary-plots-report-layout', name: 'diary-plots-report-by-substances', text: 'По препарати', filterBtn: '#btn-diary-report-filter'},
            {id: 22, iconCls: 'icon-datagrid', module: 'Diary', dim: {width: '280px'}, filter: '#diary_plots_filters', layout: '#diary-plots-report-layout', name: 'diary-plots-report-by-other-products', text: 'По други продукти', filterBtn: '#btn-diary-report-filter'},
            {id: 23, iconCls: 'icon-datagrid', module: 'Diary', dim: {width: '280px'}, filter: '#diary_plots_filters', layout: '#diary-plots-report-layout', name: 'diary-plots-report-by-produces', text: 'По добиви', filterBtn: '#btn-diary-report-filter'}
        ];
        var paymentsReports = [
            {id: 19, iconCls: 'icon-datagrid', module: 'Payments', dim: {width: '525px'}, filter: '#payments_by_bank_filters', layout: '#paid-by-bank-report-tables-layout', name: 'summary-paid-by-bank', text: 'Изплатено в брой/по банков път', type: 'summary-paid-by-bank', filterBtn: '#btn-owners-filter-bank'},
            {id: 20, iconCls: 'icon-datagrid', module: 'Payments', dim: {width: '525px'}, filter: '#payments_by_bank_filters', layout: '#paid-by-bank-and-natura-report-tables-layout', name: 'summary-paid-by-bank-and-natura', text: 'Изплатено в брой/по банков път, и в натура', type: 'summary-paid-by-bank-and-natura', filterBtn: '#btn-owners-filter-bank'},
            {id: 21, iconCls: 'icon-datagrid', module: 'Payments', dim: {width: '335px'}, filter: '#personal_use_filters', layout: '#personal-use-report-tables-layout', name: 'personal-use-report', text: 'Лично ползване', type: 'personal-use-report', filterBtn: '#btnPersonalUseFilter'},
            // {id: 22, iconCls: 'icon-datagrid', module: 'Payments', dim: {width: '275px'}, filter: '#rest_filters', layout: '#rest-report-tables-layout', name: 'summary-rest', text: 'Оставаща рента за изплащане', type: 'owners', filterBtn: '#btn-report-filter'}
            {id: 22, iconCls: 'icon-datagrid', module: 'Payments', dim: {width: '275px'}, filter: '#rest_filters', layout: '#rest-report-stopped-layout', name: 'summary-rest', text: 'Оставаща рента за изплащане', type: 'owners', filterBtn: '#btn-report-filter'},
        ];
        return [
            {name: 'plots', text: 'Имоти', iconCls: 'icon-edit-geometry', children: plotReports},
            {name: 'diary', text: 'Мероприятия', iconCls: 'icon-event', children: diaryReports},
            {name: 'payments', text: 'Собственици', iconCls: 'icon-contract', children: paymentsReports}
        ];
    }

    function setReportTree() {
        reportTree.tree({
            animate: true,
            lines: true,
            sort: 'id',
            data: initReportTreeData(),
            onSelect: function (node) {
                if(node.name === 'summary-rest'){
                    jQuery('#generic-toolbar-overlay').show();
                } else {
                    jQuery('#generic-toolbar-overlay').hide();
                }
                if (!node || !node.module) return;

                if (selected_report) {
                    // Remove the listener for the previous report
                    stopSearchOnEnter(selected_report)
                }
                
                selected_report = node;
                initPlotsReportFields();
                report_title = 'Справка ' + node.text;
                setReportTitle(report_title);
                removeFooterMessage();
                toggleGrids(node.layout);
                jQuery(node.layout).find('.datagrid-f').datagrid('resize', {width: '100%', height: '100%'});
                jQuery('#win-filter-report').window('resize', node.dim).window('center').window('close');
                toggleFilters(node);
                initSearchOnEnter(node);

                if(node.name != 'summary-rest'){
                    TF.Loading.start();
                }

                if (node.module == 'Plots') {
                    return loadPlotsReports(node);
                }
                jQuery('#map-report').linkbutton('disable');
                if (node.module == 'Diary') {
                    return loadDiaryReports(node);
                }
                if (node.module == 'Payments') {
                    return loadPaymentsReports(node);
                }
            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });

        let report = 1;
        if(GET.report) {
            report = GET.report;
        }
        var reportNode = reportTree.tree('find', report);
        if (reportNode) reportTree.tree('select', reportNode.target);

        function loadPlotsReports(node) {

            report_type = node.type;
            report_filter = node.name;
            jQuery('#choose-report-date').datebox();
            jQuery('#choose-report-ekate').combobox();
            jQuery('#choose-report-farming').combobox();
            jQuery('#choose-report-ntp').combobox();
            jQuery('#choose-report-category').combobox();
            jQuery('#choose-report-mestnost').combobox();
            jQuery('#choose-irrigation-area').combobox();
            jQuery('#choose-participation-holder').hide();
            jQuery('#choose-report-date-from-cont').hide();
            jQuery('#include-subleased-in-report-menu').hide();
            jQuery('#include-subleased-in-report').prop('checked', true);
            jQuery('#win-filter-report').window('resize', {width: 585});
            //set filters values and visibility
            initReportFilters();
            setPlotFiltersDefaults(node);
            if (node.name == 'report-own-plots') initOwnPlotsReportGrid();
            if (node.name == 'report-own-plots-detailed') {
                initDetailedOwnPlotsReport();
                jQuery('#map-report').linkbutton('disable');
            } else {
                jQuery('#map-report').linkbutton('enable');
            }
            if (node.name == 'report-subleased-plots') initSubleasedPlotsReportGrid();
            if (node.name == 'report-for-sublease') initForSubleasePlotsReportGrid();
            if (node.name == 'report-hypothecs') initHypothecsReportGrid();
            if (node.name == 'report-for-hypothec') initForHypothecReportGrid();
            if (node.name == 'report-total-area') initTotalAreaReport(false);
            if (node.name == 'report-used-plots') {
                initUsedPlotsReportGrid();
                jQuery('#choose-participation-holder').show();
                return addSubleasedFooterMessage();
            }else{
                jQuery('#choose-participation-holder').hide();
            }
            if (node.name == 'report-rented-plots') {
                initRentedPlotsReportGrid();
                return addSubleasedFooterMessage();
            }
            if (node.name == 'report-plots-in-many-contracts') initPlotsInManyContractsReportGrid();
            if (node.name == 'report-contracts-with-ownerless-plots') initContractsWithOwnerlessPlotsReportGrid(GET);
            if (node.name == 'report-historical-plots') initHistoricalPlotsReportGrid();
            if (node.name == 'report-subleased-rented-plots') initSubleasedRentedPlotsReportGrid();
            if (node.name == 'report-rented-expiring-contracts') initRentedExpiringContractsReportGrid();
            addDateFilterFooterMessage(node.type);
        }

        function loadDiaryReports(node) {
            if (node.name == 'diary-summary-by-performer') {
                return displaySummaryReportByPerformer();
            }
            if (node.name == 'diary-detailed-by-performer') {
                return displayDetailedReportByPerformer();
            }
            if (node.name == 'diary-expense-report') {
                return displayDetailedReportByPerformer(null, true)
            }
            if (node.name == 'diary-plot-report') {
                return displayDiaryPlotsReport();
            }
            if (node.name == 'diary-plots-report-by-fuel') {
                return displayDiaryPlotsByFuelReport();
            }
            if (node.name == 'diary-plots-report-by-seeds') {
                return displayDiaryPlotsByProductReport(node.text, ['seeds']);
            }
            if (node.name == 'diary-plots-report-by-substances') {
                return displayDiaryPlotsByProductReport(node.text, ['chemical_treatment', 'fertilizer']);
            }
            if (node.name == 'diary-plots-report-by-other-products') {
                return displayDiaryPlotsByProductReport(node.text, ['other']);
            }
            if (node.name == 'diary-plots-report-by-produces') {
                return displayDiaryPlotsReportByProduce(node.text);
            }
        }

        function loadPaymentsReports(node) {
            report_type = node.type;
            jQuery('#include-subleased-in-report-menu').hide();
            if (node.name == 'summary-paid-by-bank') {
                initPaidByBankReportFilters();
                initPaidByBankReport();
            }
            if (node.name == 'summary-paid-by-bank-and-natura') {
                jQuery('#win-filter-report').window('resize', {height: 445});
                initPaidByBankReportFilters();
                initPaidByBankAndNaturaReport();
            }
            if (node.name == 'personal-use-report') {
                clearPersonalUseFilter();
            }
            if (node.name == 'summary-rest') {
                clearRestFilter();
            }
            addDateFilterFooterMessage(node.type);
        }

        function toggleGrids(name) {
            jQuery('#main_layout').children().hide();
            jQuery(name).show();
        }

        function toggleFilters(node) {
            var name = node.filter;
            jQuery('#filter_container').children().hide();
            jQuery(name).show();
            //we need to query the two buttons inside the filter form to close the form window

            filter_btn = jQuery(name).find('.js-filter');
            clear_filter_btn = jQuery(name).find('.js-filter-clear');

            if (filter_btn.length == 0 || clear_filter_btn.length == 0) return false;

            filter_btn.unbind();
            clear_filter_btn.unbind();

            if (node.module == 'Plots') {
                filter_btn.bind('click', search);
                clear_filter_btn.bind('click', filter_clear_and_reload);
            } else {
                filter_btn.bind('click', closeFilterWindow);
                clear_filter_btn.bind('click', closeFilterWindow);
            }
        }

        function closeFilterWindow() {
            jQuery('#win-filter-report').window('close');
        }

        function addDateFilterFooterMessage(report_type) {
            var layoutObj = jQuery(selected_report.layout);
            if (layoutObj.data().layout.panels.south.length === 0) {
                layoutObj.layout('add', {
                    region: 'south',
                    height: 60,
                    noheader: true,
                    content: '',
                    bodyCls: 'datagrid-footer-padding',
                    border: false,
                    style: {'border-top': '1px solid #000', 'padding-top': '5px'}
                });
            } else {
                layoutObj.layout('panel', 'south').panel('resize', {height: 60});
            }

            var originalContent = '<p>В справката са представени имоти, включени в договори с ';

            var noFilterContent = '<p>В справката са представени имоти, включени в договори независимо от периода им на действие.</p>';
            if (layoutObj.layout('panel', 'south').panel('options').content === noFilterContent) {
                layoutObj.layout('panel', 'south').panel('options').content = originalContent;
            }
            var datesContent = originalContent;
            var panelOptions = layoutObj.layout('panel', 'south').panel('options');
            var content = panelOptions.content.split(datesContent)[0];

            var dates = getFilterDates();
            if (dates.fromDate !== '') datesContent += ' <b>начална дата</b> преди ' + dates.fromDate;
            if (dates.toDate !== '') datesContent += ' <b> крайна дата</b> след ' + dates.toDate;
            if (dates.asOfDate !== '') datesContent += ' <b>начална дата</b> преди ' + dates.asOfDate + ' <b>крайна дата</b> след ' + dates.asOfDate;
            if (dates.contractToDate !== '') datesContent += ' <b>дата на сключване</b> преди ' + dates.contractToDate;
            if (dates.contractFromDate !== '') datesContent += ' <b>дата на сключване</b> след ' + dates.contractFromDate;

            datesContent += '</p>';

            content += datesContent;
            if (content === originalContent + '</p>') {
                content = noFilterContent;
            }

            if (report_type == 'rented_expiring_contracts') {
                content = '<p>В справката са представени имоти, включени в договори и анекси, които <b>изтичат в периода</b> от ' + dates.fromDate + ' до ' + dates.toDate + '</p>';
            }
            if (report_type == 'summary-paid-by-bank' || report_type == 'summary-paid-by-bank-and-natura') {
                content = '<b>*Внимание! В справката се визуализират данни за плащания, направени след 01.01.2016г.</b>';
                content += '<br><div><div style="width:13px;height:13px;background-color:#ff7f6b;float:left;margin-right:3px; margin-left: 3px; border: 1px solid #716e6e;"></div> Плащанията, отбелязани с този цвят, са направени за собственици, които са премахнати от договорите.</div>';
            }
            if (report_type == 'personal-use-report') {
                content = '<b></b>';
            }

            layoutObj.layout('panel', 'south').panel('options').content = content;
            layoutObj.layout('panel', 'south').panel();
        }

        function setReportTitle(title) {
            jQuery('#central_region').find('.panel-with-icon').text(title);
        }
    }

    function setPlotFiltersDefaults(node) {
        if (node.name !== 'report-own-plots-detailed' && node.name !== 'report-own-plots') {
            jQuery('#choose-report-date').datebox('setValue', todayDate).datebox('enable');
            jQuery('#choose-report-date-to').datebox('setValue', toDateStr).datebox('enable');
            jQuery('#choose-report-date-as-of').datebox('reset');
            jQuery('#container-choose-inactive-contracts').hide();
        } else {
            jQuery('#choose-report-date').datebox('clear').datebox('disable');
            jQuery('#choose-report-date-to').datebox('clear').datebox('disable');
            jQuery('#choose-report-date-as-of').datebox('setValue', todayDate).datebox('enable');
        }
        if (node.name == 'report-rented-expiring-contracts') {
            jQuery('#choose-report-date').datebox('setValue', todayDate).datebox('enable');
            jQuery('#choose-report-date-to').datebox('setValue', toDateStr).datebox('enable');
            jQuery('#choose-report-date-as-of').datebox('reset').datebox('disable');
            jQuery('#filter-title-placeholder').html('Договори, изтичащи в период');
            jQuery('#choose-renewed-holder').show();
            initSearchArendator();
            arendator.parent().parent().show();
            jQuery(arendator.parent().siblings()[0]).html('Арендодател/Наемодател');
        } else {
            jQuery('#filter-title-placeholder').html('Договори, действащи в период');
            jQuery('#choose-report-date-as-of').datebox('enable');
            jQuery('#choose-renewed-holder').hide();
        }
        if (node.name == 'report-subleased-rented-plots') {
            jQuery('#choose-report-date').datebox('setValue', todayDate).datebox('enable');
            jQuery('#choose-report-date-to').datebox('setValue', toDateStr).datebox('enable');
            jQuery('#choose-report-date-as-of').datebox('reset').datebox('disable');
            arendator.parent().parent().show();
            jQuery(arendator.parent().siblings()[0]).html('Арендатор');
            subleaseType.parent().parent().show();
        } else {
            arendator.parent().parent().hide();
            subleaseType.parent().parent().hide();
        }
        if (node.name == 'report-subleased-plots') {
            jQuery('#choose-report-date').datebox('setValue', todayDate).datebox('enable');
            jQuery('#choose-report-date-to').datebox('setValue', toDateStr).datebox('enable');
        }
        if (node.name == 'report-hypothecs') {
            jQuery('#choose-report-date').datebox('clear');
            jQuery('#choose-report-date-to').datebox('clear');
        }
        if (node.name == 'report-own-plots-detailed') {
            jQuery('#choose-report-date-as-of').datebox('setValue', todayDate);
            jQuery('#container-choose-inactive-contracts').show();
        }
        if (node.name == 'report-total-area') {
            jQuery('#win-filter-report').window('resize', {width: 1350});
            jQuery('#grouping-options-fields').show();
        }

    }

    function getFiltersValues() {
        var obj = {};
        obj.kad_ident = jQuery('#search-kad-ident').val();
        obj.ekate = jQuery('#search-ekatte').combobox('getValues');
        obj.masiv = jQuery('#search-masiv').val();
        obj.number = jQuery('#search-number').val();
        obj.category = jQuery('#search-category').combobox('getValues');
        obj.area_type = jQuery('#search-area-type').combobox('getValues');
        obj.mestnost = jQuery('#search-mestnost').combobox('getValue');
        obj.irrigated_area = jQuery('#search-irrigated-area').combobox('getValue');
        obj.cnum = jQuery('#search-cnum').val();
        obj.contract_type = jQuery('#search-contract-type').combobox('getValues');
        obj.farming = jQuery('#search-farming').combobox('getValues');
        obj.contract_status = jQuery('#search-contract-status').combobox('getValue');
        obj.date_from = jQuery('#search-date-from').datebox('getValue');
        obj.date_to = jQuery('#search-date-to').datebox('getValue');
        obj.start_date = jQuery('#search-start-date').datebox('getValue');
        obj.due_date = jQuery('#search-due-date-to').datebox('getValue');
        obj.owner_name = jQuery('#search-owner-name').val();
        obj.owner_egn = jQuery('#search-owner-egn').val();
        obj.ime_subekt = jQuery('#search-owner-ime-subekt').val();
        obj.egn_subekt = jQuery('#search-owner-egn-subekt').val();
        obj.rep_name = jQuery('#search-represent-name').val();
        obj.rep_egn = jQuery('#search-represent-egn').val();
        obj.company_name = jQuery('#search-company-name').val();
        obj.company_eik = jQuery('#search-company-eik').val();
        obj.participation = jQuery('#search-participation').combobox('getValue');
        obj.group_by_ekatte = jQuery('#group-by-ekatte').is(':checked');
        obj.group_by_maisv = jQuery('#group-by-masiv').is(':checked');
        obj.group_by_farming = jQuery('#group-by-farming').is(':checked');
        obj.group_by_contract_type = jQuery('#group-by-contract-type').is(':checked');
        obj.group_by_with_contract = jQuery('#group-by-with-contract').is(':checked');
        obj.include_subleases = jQuery('#include-subleased-plots-to-report').is(':checked');
        obj.is_edited = is_edit !== undefined ? is_edit : false;
        obj.group_by = 'ekate';
        return [obj];
    }

    filter_btn.bind('click', search);

    jQuery('#map-report').bind('click', function (e) {
        if(selected_report.module != 'Plots' || report_type == 'own_plots_detailed') {
            return;
        }
        e.preventDefault();
        jQuery('#win-report-map').window('open');
        var obj = {};
        obj.type = report_type;
        obj.report_date = jQuery('#choose-report-date').datebox('getValue');
        obj.report_date_to = jQuery('#choose-report-date-to').datebox('getValue');
        obj.report_date_as_of = jQuery('#choose-report-date-as-of').datebox('getValue');
        obj.report_ekate = jQuery('#choose-report-ekate').combobox('getValue');
        obj.report_farming = jQuery('#choose-report-farming').combobox('getValue');
        obj.report_mestnost = jQuery('#choose-report-mestnost').combobox('getValue');
        obj.report_contract_date = jQuery('#choose-report-contract-date').datebox('getValue');
        obj.report_contract_date_to = jQuery('#choose-report-contract-date-to').datebox('getValue');
        obj.report_ntp = jQuery('#choose-report-ntp').combobox('getValue');
        obj.report_category = jQuery('#choose-report-category').combobox('getValue');
        obj.report_irrigation = jQuery('#choose-irrigation-area').combobox('getValue');
        obj.report_arendator = jQuery('#choose-arendator').val();
        obj.report_sublease_type = jQuery('#choose-sublease-type').combobox('getValue');
        obj.report_choose_participation = jQuery('#choose-participation').combobox('getValue');
        obj.report_choose_renewed = jQuery('#choose-renewed').combobox('getValue');
        TF.Rpc.Plots.ReportShowMap.mapReportsByType(obj).done(function (dataObj) {
            initReportMap();
            reloadAllReportMapLayers(dataObj.extent);
        }).fail(function (errorObj) {
            if (errorObj) {
                location.replace(errorObj.getData());
            }
        });
    });

    function search() {
        if (!selected_report || !selected_report.layout) return false;
        var current_datagrid = jQuery(selected_report.layout).find('.datagrid-f');
        if (!current_datagrid) return false;

        if (selected_report.type == 'own_plots_detailed') {
            jQuery('#win-filter-report').window('close');
            current_datagrid = plot_detailed_table;
        }
        if (selected_report.module == 'Plots') {
            if (selected_report.type == 'total_area') {
                jQuery('#win-filter-report').window('close');
                current_datagrid.datagrid('loadRpc', getFiltersValues());
            } else {
                var obj = getPlotFiltersValues(selected_report);
                if(!reportsValidation(obj)) {
                    jQuery.messager.alert('Грешка', 'Моля въведете коректни филтри!');
                }else{
                    jQuery('#win-filter-report').window('close');
                    current_datagrid.datagrid('loadRpc', obj);
                }
            }
        } else {
            jQuery('#win-filter-report').window('close');
            current_datagrid.datagrid('reload');
        }
        addDateFilterFooterMessage(selected_report.type);
    }

    function reportsValidation(params) {
        if (selected_report.type == 'for_hypothec'
            || selected_report.type == 'used_plots'
            || selected_report.type == 'rented_plots'
            || selected_report.type == 'rented_expiring_contracts'
            ) {
            if(!params[0].filters.report_date_as_of && (!params[0].filters.report_date_from || !params[0].filters.report_date)){
                return false;
            }else {
                return true
            }
        } else if(selected_report.type == 'own_plots_detailed') {
            if(!params[0].filters.report_date_as_of){
                return false;
            } else {
                return true;
            }
        } else{
            return true;
        }
    }

    // Toolbar Handlers
    clear_filter_btn.bind('click', filter_clear_and_reload);

    function filter_clear_and_reload() {
        jQuery('#win-filter-report').window('close');
        if (selected_report.module == 'Plots') {
            initReportFilters();
            if (selected_report.type == 'total_area') {
                clearFilterPlotValues();
                jQuery('#search-ekatte').combobox('select', jQuery('#search-ekatte').combobox('getData')[0].ekate);
            }
            setPlotFiltersDefaults(selected_report);
            search();
            return;
        }
        if (selected_report.name == 'diary-summary-by-performer') return clearSummaryReportByPerformerFilter();
        if (selected_report.name == 'diary-detailed-by-performer') return clearDetailedReportByPerformerFilter();
        if (selected_report.name == 'diary-expense-report') return clearDetailedReportByPerformerFilter();
        if (selected_report.name == 'diary-plot-report') return clearPlotsReportFilter();
        if (selected_report.name == 'diary-plots-report-by-fuel') return clearPlotsReportFilter();
        if (selected_report.name == 'diary-plots-report-by-seeds') return clearPlotsReportFilter();
        if (selected_report.name == 'diary-plots-report-by-substances') return clearPlotsReportFilter();
        if (selected_report.name == 'diary-plots-report-by-other-products') return clearPlotsReportFilter();
        if (selected_report.name == 'diary-plots-report-by-produces') return clearPlotsReportFilter();

        if (selected_report.name == 'summary-paid-by-bank') return clearPaidByBankFilter();
        if (selected_report.name == 'summary-paid-by-bank-and-natura') return clearPaidByBankAndNaturaFilter();
        if (selected_report.name == 'personal-use-report') return clearPersonalUseFilter();
        if (selected_report.name == 'summary-rest') return clearRestFilter();
    }

    filter_menu_btn.bind('click', function () {
        jQuery('#win-filter-report').window('open');
    });

    print_all_pages.bind('click', function () {
        printReport(PAGE_ALL);
    });

    print_single_page.bind('click', function () {
        printReport(PAGE_SELECTED);
    });
    export_excel_btn.unbind();
    export_excel_btn.bind('click', function () {
        if (selected_report.module == 'Plots') {
            return exportToExcelReport();
        }
        if (selected_report.name == 'diary-summary-by-performer') {
            return exportXLSSummaryDiaryReport(PAGE_ALL); // true: single page, false all
        }
        if (selected_report.name == 'diary-detailed-by-performer' || selected_report.name == 'diary-expense-report') {
            return exportXLSDetailedReport(PAGE_ALL);
        }
        if (['diary-plot-report', 'diary-plots-report-by-fuel', 'diary-plots-report-by-seeds', 'diary-plots-report-by-substances', 'diary-plots-report-by-other-products', 'diary-plots-report-by-produces'].includes(selected_report.name)) {
            return exportXLSDiaryReport(PAGE_ALL);
        }
        if (selected_report.name == 'summary-paid-by-bank') {
            return exportReportBankPayment();
        }
        if (selected_report.name == 'summary-paid-by-bank-and-natura') {
            return exportReportBankAndNAturaPayment();
        }
        if (selected_report.name == 'personal-use-report') {
            return exportPersonalUseReport();
        }
        if (selected_report.name == 'summary-rest') {
            return exportReportRest();
        }
        if (selected_report.name == 'diary-plots-report-by-seeds') {
            return exportReportRest();
        }
    });

    // Load Filters Data Asynchronously and init widgets
    TF.Rpc.Common.CombinedComboboxData.read()
        .done(function (data) {
            ComboboxData = data;
            initFilterComponents(false);
            initReportFilters();
            setReportTree();

        }).fail(function (error) { if (error) jQuery.messager.alert('Грешка', error.getMessage(), 'warning'); });

    function printReport(currentPage) {
        if (!selected_report || !selected_report.layout) return false;
        if (selected_report.name == 'diary-summary-by-performer') {
            return printSummaryDiaryReport(currentPage);
        }
        if (selected_report.name == 'diary-detailed-by-performer' || selected_report.name == 'diary-expense-report') {
            return printDetailedReport(currentPage);
        }
        if (['diary-plot-report', 'diary-plots-report-by-fuel', 'diary-plots-report-by-seeds', 'diary-plots-report-by-substances', 'diary-plots-report-by-other-products', 'diary-plots-report-by-produces'].includes(selected_report.name)) {
            const reportTitle = this.getReportTitle(selected_report.name);
            return printDiaryReport(currentPage, reportTitle);
        }
        if (selected_report.name == 'summary-paid-by-bank') {
            return printReportBankPayment(currentPage);
        }
        if (selected_report.name == 'summary-paid-by-bank-and-natura') {
            return printReportBankAndNaturaPayment(currentPage);
        }
        if (selected_report.name == 'personal-use-report') {
            return printPersonalUseReport(currentPage);
        }
        if (selected_report.name == 'summary-rest') {
            return printReportRest(currentPage);
        }
        /* !! from here on only plots reports */
        /* This jquery selector can return multiple datagrids in case of the detailed report own_plots_detailed */
        var current_datagrid = jQuery(selected_report.layout).find('.datagrid-f');
        if (!current_datagrid) return false;
        if (selected_report.type == 'own_plots_detailed') {
            current_datagrid = plot_detailed_table;
        }
        var data = current_datagrid.datagrid('getData');
        if (data['rows'].length == 0) return jQuery.messager.alert('Грешка', 'Не може да отпечатате празна справка.');
        var options = current_datagrid.datagrid('options');
        var params = options.rpcParams[0];
        var page = (currentPage) ? options.pageNumber : null;
        var rows = (currentPage) ? options.pageSize : null;
        var sort = options.sortName || null;
        var order = options.sortOrder || null;
        TF.Rpc.Plots[selected_report.printApi].read(params, page, rows, sort, order)
            .done(function (data) {
                return printResultsFromRequests(data, current_datagrid);
            })
            .fail(function (errorObj) {
                if (errorObj) jQuery.messager.alert('Грешка', errorObj.getMessage());
            });
    }

    function printResultsFromRequests(data, current_datagrid) {
        var html;
        var gridOptions = current_datagrid.datagrid('options');
        var report_date = jQuery('#choose-report-date').datebox('getValue');
        var report_date_to = jQuery('#choose-report-date-to').datebox('getValue');
        var report_date_as_of = jQuery('#choose-report-date-as-of').datebox('getValue');

        var date = new Date(report_date);
        var footerData = data.footer;

        report_date = date.getDate() + '.' + (date.getMonth() + 1) + '.' + date.getFullYear();

        var date_to = new Date(report_date_to);
        report_date_to = date_to.getDate() + '.' + (date_to.getMonth() + 1) + '.' + date_to.getFullYear();

        var report_title_date;
        var gridData = data.rows;
        if (report_date_as_of != '') {
            var date_as_of = new Date(report_date_as_of);
            report_date_as_of = date_as_of.getDate() + '.' + (date_as_of.getMonth() + 1) + '.' + date_as_of.getFullYear();
            report_title_date = report_date_as_of;
        } else {
            report_title_date = report_date_to;
        }
        /* output string */
        html = '<style>@page{size: landscape;}body,table{font-family: Arial;font-size: 10px;}</style>' +
            '<h2 align="center">' + report_title + ' към дата ' + report_title_date + '</h2>';

        if (gridOptions.rpcParams.length == 4) {
            var date = new Date(gridOptions.rpcParams[0]);
            report_date_to = date.getDate() + '.' + (date.getMonth() + 1) + '.' + date.getFullYear();

            html = '<style>@page{size: landscape;}body,table{font-family: Arial;font-size: 10px;}</style>' +
                '<h2 align="center">' + report_title + ' от дата ' + report_date + ' до дата ' + report_date_to + '</h2>';
        }

        var header = {};
        var columns = gridOptions.columns[0];

        if (report_type == 'rented_expiring_contracts') {
            columns = gridOptions.columns[1];
            html = '<style>@page{size: landscape;}body,table{font-family: Arial;font-size: 10px;}</style>' +
                '<h2 align="center">' + report_title + ' в периода от дата ' + report_date + ' до дата ' + report_date_to + '</h2>';
        }
        for (var i = 0; i < columns.length; i++) {
            header[columns[i].field] = columns[i].title;
        }
        var rows = gridData;
        if (footerData[1]) rows.push(footerData[1]);
        html += Templates.table(header, rows);

        jQuery('body').append('<iframe id="printf" name="printf"></iframe>');
        var newWin = window.frames['printf'];
        newWin.document.write('<body onload=window.print()>'+html+'</body>');
        newWin.document.close();
        setTimeout(function () {
            jQuery('#printf').remove();
        }, 1000);
    }

    /* to avoid errors when using portlet PlotsFilter on filter click we redefine our function here */
    plotsFilter = function () {
        jQuery('#win-filter-report').window('close');
        var reportTotal = reportTree.tree('find', 7);
        if (reportTotal) reportTree.tree('select', reportTotal.target);
    };

    resetDateBoxes = function () {
        if (report_type == 'own_plots_detailed' || report_type == 'own_plots') {
            jQuery('#choose-report-date-as-of').datebox('reset');
            return;
        }
        jQuery('#choose-report-date').datebox('enable').datebox('setValue', todayDate);
        jQuery('#choose-report-date-to').datebox('enable').datebox('setValue', toDateStr);
        jQuery('#choose-report-date-as-of').datebox('reset');
    }
});



function initSearchOnEnter(report) {
    jQuery(report.filter).off("keyup").on("keyup", function (event) {
		// Listen for enter key
        if (13 !== event.keyCode) {
			return;
		}

        jQuery(report.filterBtn).click()
    });
}

function stopSearchOnEnter(report) {
    jQuery(report.filter).off("keyup");
}

function getReportTitle(reportName) {
    if (!reportName) {
        return null
    }

    const reportTitleByNameMap ={
        'diary-plot-report': 'Справка по парцел',
        'diary-plots-report-by-fuel': 'Справка по гориво',
        'diary-plots-report-by-seeds': 'Справка по семена',
        'diary-plots-report-by-substances': 'Справка по препарати',
        'diary-plots-report-by-other-products': 'Справка по други продукти',
        'diary-plots-report-by-produces': 'Справка по добив'
    }


    return reportTitleByNameMap[reportName] ?? null
}
