var GET = {};
location.search.substr(1).split("&").forEach(function(item) {GET[decodeURIComponent(item.split("=")[0])] = decodeURIComponent(item.split("=")[1])});
var file_id = GET['file_id'];
var file_name = GET['filename'];
var date_uploaded = GET['date_uploaded'];

jQuery(function(){
	initUpdateText();

    jQuery('#kvs-invalid-plots-table').datagrid({
        title:'Данни невалидни геометрии',
        iconCls:'icon-edit-geometry',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 20,
        fit: true,
        fitColumns: true,
        showFooter: true,
        url:'index.php?kvs-invalid-geometry-rpc=kvs-invalid-plots-grid',
        sortName: 'pl_dka',
        rpcParams: [file_id],
        sortOrder: 'desc',
        idField:'id',
        singleSelect: true,
        frozenColumns:[[{
            field:'ck',
            checkbox:true
        }]],
        columns:[[{
            field:'kad_no',
            title:'<b>Номер на имот</b>',
            sortable:true,
            width:250
        },{
            field:'pl_dka',
            title:'<b>Площ по документ</b>',
            sortable:true,
            width:250
        }
        ]],
        pagination:true,
        rownumbers:true,
        toolbar: [{
            id: 'btnendupdate',
            text: 'Приключване на актуализация',
            iconCls: 'icon-payments',
            handler: function() {

                TF.Rpc.Files.FilesGrid.endUpdate(file_id)
                .done(function(data) {
                    jQuery.messager.alert('Внимание','Успешно приключване на актуализация!','info', function(){
                        window.location.assign('index.php?page=Files.Home');
                    });
               })
                .fail(function(errorObj){
                    var message = TF.Rpc.ExceptionsList.SYSTEM_ERROR.message;
                    if (errorObj.is(TF.Rpc.ExceptionsList.END_UPDATE_KVS_NOT_UPDATED_PLOTS)) {
                        message = TF.Rpc.ExceptionsList.END_UPDATE_KVS_NOT_UPDATED_PLOTS.message
                    };
                    jQuery.messager.confirm('Внимание', message, function(r){
                        if (r){
                           TF.Rpc.Files.FilesGrid.endUpdate(file_id, true)
                            .done(function(data) {
                               jQuery.messager.alert('Внимание','Успешно приключване на актуализация!','info', function(){
                                    window.location.assign('index.php?page=Files.Home');
                                });
                           });
                        }
                    });
                });
            }
        }],
        onBeforeLoad: function(){
        	jQuery('#kvs-invalid-plots-table').datagrid('clearChecked');
        	initUpdateText(file_name, date_uploaded);
        },
        onLoadSuccess: function() {
            jQuery('#kvs-invalid-plots-table').datagrid('selectRow', 0);
        },
        onSelect: function(rowIndex, rowData) {
            if(rowData) {
                displayFeatureSelection(rowData);
            }
		},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

});

function initUpdateText(file_name, date_uploaded) {

    var text = "Списък с невалидните геометрии на полигони от файл <b>" + file_name + "</b> от <b>" + date_uploaded + "</b>.<br>" +
    		"Изберете ред от таблицата и той ще се визуализира в картата.<br>" +
    		"С инструментите в картата може да редактирате границите и да запазите валиден полигон.";

    jQuery('#kvs-invalid-plots-text').html(text);
}

function showContractInNewTab(contract_id) {
	window.open("index.php?page=Contracts.Home&contract_id=" + contract_id, '_blank');
}
