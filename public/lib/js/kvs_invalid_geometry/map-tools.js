var _toolOperation = 0;
var TOOL_OPERATION_EDIT_GEOMETRY = 1;
var TOOL_OPERATION_DRAW_HOLE = 2;
var TOOL_OPERATION_MERGE = 3;
var TOOL_OPERATION_REMOVE_HOLES = 4;
var TOOL_OPERATION_SPLIT = 5;
var TOOL_OPERATION_SAVE = 6;
var TOOL_OPERATION_DRAW = 7;
var TOOL_OPERATION_SAVE_WITH_INFO = 8;

function initMapTools()
{
	//init map tools 
	jQuery('#tool-set-scale').bind('click', function()
	{
		var scale = jQuery("#scale-denominator input").val();

		map.zoomToScale(scale);
	});

	jQuery('#tool-panzoom').bind('click', function()
	{
		_clippingZoom = true;
		var options = jQuery('#tool-panzoom').linkbutton('options');
		if (options.disabled)
		{
			return false;
		}

		//stop choose layer object event if active
		map.events.unregister('click', map, propertyWindowFunction);
		chooseControl('navigation');
		//unselect all buttons
		unselectAll();
		//select navigation button
		jQuery('#tool-panzoom').linkbutton('select');
	});

	jQuery('#tool-zoomin').bind('click', function()
	{
		_clippingZoom = true;
		unselectAll();
		map.events.unregister('click', map, propertyWindowFunction);

		var skip_controls = 'modifyfeature';
		chooseControl('zoomin', skip_controls);
		jQuery('#tool-zoomin').linkbutton('select');
	});

	jQuery('#tool-zoomout').bind('click', function()
	{
		_clippingZoom = true;
		unselectAll();
		map.events.unregister('click', map, propertyWindowFunction);

		var skip_controls = 'modifyfeature';
		chooseControl('zoomout', skip_controls);
		jQuery('#tool-zoomout').linkbutton('select');
	});

	jQuery('#tool-measure-line').bind('click', function()
	{
		unselectAll();
		map.events.unregister('click', map, propertyWindowFunction);

		chooseControl('linemeasure');
		jQuery('#tool-measure-line').linkbutton('select');
	});

	jQuery('#tool-measure-polygon').bind('click', function()
	{
		unselectAll();
		map.events.unregister('click', map, propertyWindowFunction);

		chooseControl('polygonmeasure');
		jQuery('#tool-measure-polygon').linkbutton('select');
	});

	jQuery('#tool-clear-selection').bind('click', function()
	{
		var options = jQuery('#tool-clear-selection').linkbutton('options');
		if (options.disabled)
		{
			return false;
		}
		controlSelect.unselectAll();
		unselectAll();
        deactivateAllControls();
        chooseControl('navigation');
	});

	jQuery('#tool-select').bind('click', function()
	{

		var options = jQuery('#tool-select').linkbutton('options');
		if (options.disabled)
		{
			return false;
		}

		//remove all previous selections and drawed polygons
		map.events.unregister('click', map, propertyWindowFunction);
		//deactivate control modify bofore removing features
		//if control is still active it will trigger an error
		controlModify.deactivate();
		//remove features and unselect buttons
		// vectors.removeAllFeatures();
		controlSelect.unselectAll();
		unselectAll();
		//select button
		jQuery('#tool-select').linkbutton('select');

		chooseControl('selectfeature');
	});

	//zoom to active layer extent
	jQuery('#tool-zoom-layer').bind('click', function() {
		var selected = jQuery('#all-layers-tree').tree('getSelected');

		if (!selected)
		{
			jQuery.messager.alert('Грешка', 'Не е избран активен слой!');
			return false;
		}

		map.zoomToExtent(new OpenLayers.Bounds.fromString(selected['attributes'].extent).transform(
				new OpenLayers.Projection("EPSG:32635"),
				map.getProjectionObject())
				);

		return false;
	});

	jQuery('#tool-edit-geometry').bind('click', function()
	{
		var options = jQuery('#tool-edit-geometry').linkbutton('options');
		if (options.disabled)
		{
			return false;
		}

		//remove info selection
		map.events.unregister('click', map, propertyWindowFunction);
		//unselect all buttons
		unselectAll();

		if (vectors.selectedFeatures.length > 0)
		{
			chooseControl("modifyfeature");
		} else
		{
			jQuery.messager.alert('Грешка', 'Не са селектирани обекти!');
		}

		jQuery('#tool-edit-geometry').linkbutton('select');
	});

	jQuery('#tool-save').bind('click', function()
	{
		var options = jQuery('#tool-save').linkbutton('options');
		if (options.disabled)
		{
			return false;
		}

		//unselect all buttons
		unselectAll();

		if (vectors.selectedFeatures.length > 0)
		{
			if (vectors.selectedFeatures.length == 1)
			{
				saveKvsOszInvalidPlotsChanges();

				controlSelect.unselectAll();
				unselectAll();
		        deactivateAllControls();
		        chooseControl('navigation');
			}else {
				jQuery.messager.alert('Грешка', 'Не е разрешено запазването на повече от един обект!');
			}	
		} else
		{
			jQuery.messager.alert('Грешка', 'Не e селектиран обект!');
		}
	});

	jQuery('#tool-merge').bind('click', function()
	{
		var options = jQuery('#tool-merge').linkbutton('options');
		if (options.disabled)
		{
			return false;
		}

		//unselect all buttons
		unselectAll();

		/*var layerData = jQuery('#all-layers-tree').tree('getSelected');
		var layerType = layerData.attributes.layer_type;*/
		var idName;

		var featuresCount = vectors.selectedFeatures.length;
		if (featuresCount == 0)
		{
			jQuery.messager.alert('Грешка', 'Не са избрани обекти!');
			return false;
		}

        //attributes
        var beforeMergeIds = '';
        for(var i=0; i<featuresCount; i++)
        {
            beforeMergeIds += vectors.selectedFeatures[i].attributes.id + ',';
        }
        var ekatte = vectors.selectedFeatures[0].attributes.ekatte;
        var fr_gid = vectors.selectedFeatures[0].attributes.fr_gid;
        var is_system = vectors.selectedFeatures[0].attributes.is_system;
        var kategoria = vectors.selectedFeatures[0].attributes.kategoria;
        var kod_ntp = vectors.selectedFeatures[0].attributes.kod_ntp;
        var kod_sobstv = vectors.selectedFeatures[0].attributes.kod_sobstv;
        var kvs_no = vectors.selectedFeatures[0].attributes.kvs_no;
        var label = vectors.selectedFeatures[0].attributes.label;
        
		//last element will always be the union feature
		//at first iteration last element will be taken as union feature
		var unionFeature = vectors.selectedFeatures[featuresCount - 1];
		//add last feature ID into merge array with merge ID
		replacePolygonRequired = true;
		replacePolygonAction = 'merge';

		
		replacePolygonIdArray.push(unionFeature.attributes.id);
		idName = 'id';
		
		replacePolygonIdName = idName;

		//last feature will not be counted
		//last feature will be taken as previous union and will not be counted for intersection
		features: for (var i = featuresCount - 2; i >= 0; i--)
		{
			var intersection = false;
			var intersection_index;
			intersections: for (var j = 0; j < vectors.selectedFeatures.length - 1; j++)
			{
				if (unionFeature.geometry.intersects(vectors.selectedFeatures[j].geometry))
				{
					intersection = true;
					intersection_index = j;
					break intersections;
				} else {
					intersection = false;
				}
			}

			if (!intersection)
			{
				controlSelect.unselectAll();
				jQuery.messager.alert('Грешка', 'Избраните обекти не могат да бъдат обединени!');
				return false;
			}
			
			replacePolygonIdArray.push(vectors.selectedFeatures[intersection_index].attributes.id);

			var reader = new jsts.io.WKTReader();

			var a = reader.read(unionFeature.geometry.toString());
			var b = reader.read(vectors.selectedFeatures[intersection_index].geometry.toString());

			a.buffer(0.);
			var union = a.union(b);

			var parser = new jsts.io.OpenLayersParser();

			union = parser.write(union);

			//remove last used features features
			vectors.removeFeatures(unionFeature);
			vectors.removeFeatures(vectors.selectedFeatures[intersection_index]);
			//rewrite the union feature
            var attr = {};
            attr.id = beforeMergeIds.replace(/,\s*$/, "");
            attr.ekatte = ekatte;
            attr.fr_gid = fr_gid;
            attr.is_system = is_system;
            attr.kategoria = kategoria;
            attr.kod_ntp = kod_ntp;
            attr.kod_sobstv = kod_sobstv;
            attr.kvs_no = kvs_no;
            attr.label = label;
            
			unionFeature = new OpenLayers.Feature.Vector(union, attr);

			vectors.addFeatures(unionFeature);

			controlSelect.select(unionFeature);
		}
	});
	
	jQuery('#tool-split').bind('click', function()
	{
		var options = jQuery('#tool-split').linkbutton('options');
		if (options.disabled)
		{
			return false;
		}
        
        if (vectors.selectedFeatures.length == 0)
		{
			jQuery.messager.alert('Грешка', 'Не са избрани обекти, които да бъдат разделени! ');
			return false;
		}
		if (vectors.selectedFeatures.length > 1)
		{
			jQuery.messager.alert('Грешка', 'Не може да разцепвате няколко обекта едновременно!');
			return false;
		}

		unselectAll();
		chooseControl('navigation');
		controlDrawSplitLine.activate();
		jQuery('#tool-split').linkbutton('select');
	});
}

function saveKvsOszInvalidPlotsChanges()
{
	var obj = new Object();

	var selectedFeature = vectors.selectedFeatures[0];

	obj.file_id = file_id;
	obj.id = selectedFeature.attributes.id;
	obj.fr_gid = selectedFeature.attributes.fr_gid;
	obj.ekatte = selectedFeature.attributes.ekatte;
	obj.kvs_no = selectedFeature.attributes.kvs_no;
	obj.kategoria = selectedFeature.attributes.kategoria;
	obj.kod_ntp = selectedFeature.attributes.kod_ntp;
	obj.kod_sobstv = selectedFeature.attributes.kod_sobstv;
	obj.is_system = selectedFeature.attributes.is_system;
	obj.kad_no = selectedFeature.attributes.label;
	obj.geometry = selectedFeature.geometry.transform(
					new OpenLayers.Projection("EPSG:900913"),
					new OpenLayers.Projection("EPSG:32635")
				   ).toString();

	TF.Rpc.KVSInvalidGeometry.KVSInvalidGeometryMapTools.saveKvsOszInvalidPlotsChanges(obj)
	.done(function (data) {
		jQuery('#kvs-invalid-plots-table').datagrid('reload');
	})
	.fail(function (errorObj) {
		
	});
}