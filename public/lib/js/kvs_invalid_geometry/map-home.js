//init global variables
var map;
var controlNavigation;
var controlScaleLine;
var controlZoomIn;
var controlZoomOut;
var controlModify; //control handles modify of selected feature with controlSelect
var replacePolygonIdArray = [];
var controlDrawSplitLine;

var bingApiKey = "AiWEso3-IjWyX1aZMuep9Sjl62D6FUMqv8qQGpV-kgbW0qWk61to4nrqHh-2D5HL";
var renderer = OpenLayers.Util.getParameters(window.location.href).renderer;
if (renderer) {
	OpenLayers.Layer.Vector.prototype.renderers = [renderer];
}

jQuery(function () {
    jQuery.fn.combobox.defaults.loader = EasyUIRPCLoaders.EasyUIGridCustomLoader.loader;
    jQuery.fn.combobox.defaults.loadFilter = EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter;
});

OpenLayers.ProxyHost = Settings.OPEN_LAYERS_PROXY;

//on body load
jQuery(function()
{
	initMapPad();
	initKVSLayer();
	initVectorLayer();
	initDefaultMapControls();
	zoomToBulgaria();
	initPositionDisplay();
	initScaleDisplay();
	initMapTypes();
	initMapTools();

	initPolygonModifyControl();
	initPolygonSplitControl();

});

function initMapTypes() {
	//init map types combobox
    jQuery('#map-types-combobox > input').combobox({
        url: 'index.php?common-rpc=map-types-combobox',
        rpcParams: [{selected: true}],
        valueField: 'id',
        textField: 'name',
        onSelect: function(record)
        {
            initMapPad(record.id);
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
    });
}

function initKVSLayer() {

	var layerData = new OpenLayers.Layer.WMS(
		'layer_kvs',
		wmsServer + "?map=" + mapPath + groupID + '.map',
		{
			layers: 'layer_kvs',
			format: 'image/png',
			transparent: "true"
	});
	map.addLayer(layerData);
}

function initMapPad(specific_map_type)
{
	var chosenMapType;

	//init all map features
	var options = {
		controls: [],
		projection: new OpenLayers.Projection("EPSG:900913")
	};

	//on init map type will not be specified
	if (specific_map_type == undefined)
	{
		chosenMapType = mapType;
		//init map
		map = new OpenLayers.Map('map', options);
	}
	//when map type is changed specific_map_type will have the value of map type
	else {
		chosenMapType = parseInt(specific_map_type);
	}

	var layerMapPad;

	switch (chosenMapType)
	{
		case 2:
			layerMapPad = new OpenLayers.Layer.Bing({
				key: bingApiKey,
				type: "Aerial",
				name: "MapPad"
			});
			break;
		case 3:
			layerMapPad = new OpenLayers.Layer.Bing({
				key: bingApiKey,
				type: "Road",
				name: "MapPad"
			});
			break;
		case 4:
			layerMapPad = new OpenLayers.Layer.Google(
					"MapPad",
					{type: google.maps.MapTypeId.TERRAIN}
			);
			break;
		case 5:
			layerMapPad = new OpenLayers.Layer.Google(
					"MapPad",
					{numZoomLevels: 20}
			);
			break;
		case 6:
			layerMapPad = new OpenLayers.Layer.Google(
					"MapPad",
					{type: google.maps.MapTypeId.HYBRID, numZoomLevels: 20}
			);
			break;
		case 7:
			layerMapPad = new OpenLayers.Layer.Google(
					"MapPad",
					{type: google.maps.MapTypeId.SATELLITE, numZoomLevels: 22}
			);
			break;
		case 8:
			layerMapPad = new OpenLayers.Layer.OSM();
			break;
        case 9:
            layerMapPad = new OpenLayers.Layer.WMS(
				'MapPad',
				imagesWMSServer + "?map=" + '/var/www/satellite_processor/maps/geo_scan.map',
                //wmsServer + "?map=" + mapPath + groupID + '.map',
				{
					layers: 'geo_scan',
				},
                {
                    numZoomLevels: 18
                });
			break;
		default: // default is bing aerial with labels
			layerMapPad = new OpenLayers.Layer.Bing({
				key: bingApiKey,
				type: "AerialWithLabels",
				name: "MapPad"
			});
			break;
	}

	//specific map type will be given only on reload
	//on first load(init) specific map type should be undefined
	if (specific_map_type == undefined)
	{
		map.addLayer(layerMapPad);
	}
	else {
		map.addLayer(layerMapPad);
		map.setLayerIndex(map.layers[map.layers.length - 1], 0);
		map.removeLayer(map.layers[1]);
		map.layers[0].redraw(true);
	}
}

function initDefaultMapControls()
{
	controlNavigation = new OpenLayers.Control.Navigation({
		name: "navigation"
	});
	map.addControl(controlNavigation);

	controlScaleLine = new OpenLayers.Control.ScaleLine({
		name: "scaleline",
		bottomInUnits: 'km'
	});
	map.addControl(controlScaleLine);

	controlLineMeasure = new OpenLayers.Control.DynamicMeasure(OpenLayers.Handler.Path, {
		name: 'linemeasure',
		persist: true,
		geodesic: true
	});
	map.addControl(controlLineMeasure);

	controlPolygonMeasure = new OpenLayers.Control.DynamicMeasure(OpenLayers.Handler.Polygon, {
		name: "polygonmeasure",
		persist: true,
		geodesic: true
	});
	map.addControl(controlPolygonMeasure);

	controlZoomIn = new OpenLayers.Control.ZoomBox({
		name: "zoomin",
		title: "Zoom in box",
		out: false
	})
	map.addControl(controlZoomIn);

	controlZoomOut = new OpenLayers.Control.ZoomBox({
		name: "zoomout",
		title: "Zoom out box",
		out: true
	});
	map.addControl(controlZoomOut);

	controlSelect = new OpenLayers.Control.SelectFeature(
        [vectors],
        {
			name: "selectfeature",
            clickout: true, toggle: false,
            multiple: true,
            onHidePanel: onHidePanelMultiSelect, hover: false,
            toggleKey: "ctrlKey", // ctrl key removes from selection
            multipleKey: "shiftKey" // shift key adds to selection
        }
    );

	map.addControl(controlSelect);
}

function zoomToBulgaria()
{
	map.zoomToExtent(new OpenLayers.Bounds.fromString("125190.6162, 4573142.7188, 631370.3273, 4887149.5823").transform(
			new OpenLayers.Projection("EPSG:32635"),
			map.getProjectionObject()));
}

function initPositionDisplay()
{
	map.events.register("mousemove", map, function(e)
	{
		var position = map.getLonLatFromViewPortPx(e.xy);

		OpenLayers.Util.getElement("coords").innerHTML = 'x: ' + position.lat + ', y: ' + position.lon;
	});
}

function initScaleDisplay()
{
	map.events.register("zoomend", map, function(e)
	{
		var currentScale = Math.round(map.getScale());

		var scale = jQuery("#scale-denominator input").val(currentScale);
	});
}

var VECTOR_LAYER_STYLE_TMP = {
	cursor: "pointer",
	graphicName: 'square',
	pointRadius: 10,
	fillColor: '#cccccc',
	strokeColor: "#2D5B0F",
	strokeWidth: 5,
	fillOpacity: 0.25,
	fontColor:"#030302",
	label: '${label}'
};

function displayFeatureSelection(rowData)
{
	//remove all previous features
	vectors.removeAllFeatures();

	var in_options = {
		'internalProjection': map.baseLayer.projection,
		'externalProjection': new OpenLayers.Projection("EPSG:32635")
	};

	rowData.arr_geom = rowData.arr_geom.replace('{','');
	rowData.arr_geom = rowData.arr_geom.replace('}','');
	//rowData.arr_geom = rowData.arr_geom.replace('"','');

	var resGeom = rowData.arr_geom.split('","');

	var bounds;
    var features;
	for (var i = 0; i < resGeom.length; ++i) {

		var dataGeom = resGeom[i].split('-');

		dataGeom[0] = dataGeom[0].replace('"','');
		dataGeom[1] = dataGeom[1].replace('"','');

		var features = new OpenLayers.Format.WKT(in_options).read(dataGeom[1]);
		features.attributes.label = rowData.kad_no;
		features.attributes.id = dataGeom[0];
		features.attributes.fr_gid = rowData.fr_gid;
		features.attributes.ekatte = rowData.ekatte;
		features.attributes.kvs_no = rowData.kvs_no;
		features.attributes.kategoria = rowData.kategoria;
		features.attributes.kod_ntp = rowData.kod_ntp;
		features.attributes.kod_sobstv = rowData.kod_sobstv;
		features.attributes.is_system = rowData.is_system;

		if (features) {
	        vectors.addFeatures(features);
	    }
	}

	var bounds = vectors.getDataExtent();
    map.zoomToExtent(bounds);
}

function initVectorLayer()
{
	var renderer = OpenLayers.Util.getParameters(window.location.href).renderer;
	renderer = (renderer) ? [renderer] : OpenLayers.Layer.Vector.prototype.renderers;

	vectors = new OpenLayers.Layer.Vector("Vector Layer", {
		styleMap: new OpenLayers.StyleMap({
			default: new OpenLayers.Style(VECTOR_LAYER_STYLE_TMP,
			{
	            isDefault: true,
				context: {
					label: function (feature) {
						if(!feature.attributes.hasOwnProperty('label')) {
							return '';
						}
						return feature.attributes.label;
					}
				}
			}),
			select: new OpenLayers.Style(OpenLayers.Util.applyDefaults({
                fillColor: "red",
                strokeColor: "gray",
                graphicName: "square",
                rotation: 45,
                pointRadius: 15
            }, OpenLayers.Feature.Vector.style["select"])),
		}),
		renderers: renderer
	});

	map.addLayer(vectors);
}

function deactivateAllControls()
{
	for (var i = 0; i < map.options.controls.length; i++)
	{
		map.options.controls[i].deactivate();
	}
}

function unselectAll()
{
	jQuery('#tool-panzoom').linkbutton('unselect');
	jQuery('#tool-zoomin').linkbutton('unselect');
	jQuery('#tool-zoomout').linkbutton('unselect');
	jQuery('#tool-select').linkbutton('unselect');
	jQuery('#tool-edit-geometry').linkbutton('unselect');
	jQuery('#tool-merge').linkbutton('unselect');
	jQuery('#tool-split').linkbutton('unselect');
	jQuery('#tool-auto-split').linkbutton('unselect');
}

function initPolygonModifyControl()
{
	controlModify = new OpenLayers.Control.ModifyFeature(vectors, {
		name: 'modifyfeature',
		deferDelete: true,
		createVertices: true,
		mode: 110,
		tools: [// custom tools
			{
				// to rotate the "angle" attribute of a point by steps of 15 degrees
				geometryTypes: ['OpenLayers.Geometry.Point',
					'OpenLayers.Geometry.MultiPoint'],
				dragAction: function(feature, initialAtt, escale, rotation) {
					var angle = ((initialAtt.angle || 0) - rotation) % 360;
					// force steps of 15 degrres
					angle = Math.floor(angle / 15) * 15;
					feature.attributes.angle = angle;
				},
				style: OpenLayers.Control.ModifyFeature_styles.rotate
			}, {
				// to resize the pointRadius.
				geometryTypes: ['OpenLayers.Geometry.Point',
					'OpenLayers.Geometry.MultiPoint'],
				dragAction: function(feature, initialAtt, escale, rotation) {
					var radius = (initialAtt.radius || 6) * escale;
					feature.attributes.radius = Math.max(6, radius);
				},
				style: OpenLayers.Control.ModifyFeature_styles.resize
			}, {
				// to close a lineString as a ring
				geometryTypes: ['OpenLayers.Geometry.LineString'],
				pressingAction: function(feature) {
					var geometry = feature.geometry;
					geometry.addComponent(geometry.components[0].clone());
				},
				style: {
					label: 'ring',
					title: 'press to close as a ring',
					cursor: "pointer",
					fontSize: '8px',
					fontColor: '#222',
					pointRadius: 10,
					fillColor: '#cccccc',
					strokeColor: '#444444'
				}
			}]
	});

	map.addControl(controlModify);
}

function initPolygonSplitControl()
{
	controlDrawSplitLine = new OpenLayers.Control.DrawFeature(vectors, OpenLayers.Handler.Path, VECTOR_LAYER_STYLE_TMP);
	map.addControl(controlDrawSplitLine);

	controlDrawSplitLine.events.register("featureadded", this, function(e)
	{
		//last feature will be the split line - (featuresCount - 1)
		var reader = new jsts.io.WKTReader();

		var featuresCount = vectors.selectedFeatures.length;

		if (featuresCount  == 0)
		{
			jQuery.messager.alert('Грешка', 'Не са избрани обекти, които да бъдат разделени! ');
			
			return false;
		}

		var isMultiPolygon = false;
		var targetOuterBoundary;
		var holesCount = 0;

		//attributes
        var attributesObject = new Object();
        attributesObject = {
        	id: vectors.selectedFeatures[0].attributes.id,
	        ekatte: vectors.selectedFeatures[0].attributes.ekatte,
	        fr_gid: vectors.selectedFeatures[0].attributes.fr_gid,
	        is_system: vectors.selectedFeatures[0].attributes.is_system,
	        kategoria: vectors.selectedFeatures[0].attributes.kategoria,
	        kod_ntp: vectors.selectedFeatures[0].attributes.kod_ntp,
	        kod_sobstv: vectors.selectedFeatures[0].attributes.kod_sobstv,
	        kvs_no: vectors.selectedFeatures[0].attributes.kvs_no,
	        label: vectors.selectedFeatures[0].attributes.label
	    };

        //create a helper vector layers
        var renderer = OpenLayers.Util.getParameters(window.location.href).renderer;
        renderer = (renderer) ? [renderer] : OpenLayers.Layer.Vector.prototype.renderers;

        var helpVectors = new OpenLayers.Layer.Vector("Temporary Help Vector Layer", {
            renderers: renderer
        });

        var tempVectors = new OpenLayers.Layer.Vector("Temporary Vector Layer", {
            renderers: renderer
        });

        for(var z=0; z<(featuresCount); z++)
        {
            //convert polygon/multipolygon to jsts geometry
            var target = reader.read(vectors.selectedFeatures[z].geometry.toString());
            var targetBoundaries = target.getBoundary();
            tempVectors.removeAllFeatures();

            //create a helper vector layer

            var holeVectors = new OpenLayers.Layer.Vector("Temporary Hole Vector Layer", {
                renderers: renderer
            });

            //if object has geometries it is MiltiPolygon
            if (targetBoundaries.geometries)
            {
                isMultiPolygon = true;
                holesCount = targetBoundaries.geometries.length - 1;
                targetOuterBoundary = targetBoundaries.geometries[0];

                holes: for (var i = 0; i < target.holes.length; i++)
                {
                    var points = [];
                    points_coords: for (var j = 0; j < target.holes[i].points.length; j++)
                    {
                        var currentPoint = target.holes[i].points[j];
                        points.push(new OpenLayers.Geometry.Point(currentPoint.x, currentPoint.y));
                    }
                    var linearRing = new OpenLayers.Geometry.LinearRing(points);
                    var polygon = new OpenLayers.Feature.Vector(
                            new OpenLayers.Geometry.Polygon([linearRing])
                            );
                    holeVectors.addFeatures(polygon);
                }
            } else
            {
                targetOuterBoundary = targetBoundaries;
            }

            //get target outer boundary and create vector openlayers feature from it
            var boundaryPoints = [];
            boundary_points: for (var i = 0; i < targetOuterBoundary.points.length; i++)
            {
                var currentPoint = targetOuterBoundary.points[i];
                boundaryPoints.push(new OpenLayers.Geometry.Point(currentPoint.x, currentPoint.y));
            }
            var boundaryLinearRing = new OpenLayers.Geometry.LinearRing(boundaryPoints);
            var boundaryPolygon = new OpenLayers.Feature.Vector(
                    new OpenLayers.Geometry.Polygon([boundaryLinearRing])
                    );

            //convect split line to jsts geometry
            olSplitLineFeature = vectors.features[vectors.features.length-1];
            var splitLine = reader.read(olSplitLineFeature.geometry.toString());
            var splitResult = targetOuterBoundary.union(splitLine);

            var polygonizer = new jsts.operation.polygonize.Polygonizer();

            polygonizer.add(splitResult);

            var parser = new jsts.io.OpenLayersParser();

            //iterate all result polygons
            var polygons = polygonizer.getPolygons();
            for (var i = polygons.iterator(); i.hasNext(); ) {
                var polygon = i.next();
                var feature = new OpenLayers.Feature.Vector(parser.write(polygon));

                var featureVertices = feature.geometry.getVertices();
                var isInsideTarget = true;
                //foreach feature: check if feature is in target polygon outer boundry
                vertices: for (var j = 0; j < featureVertices.length; j++)
                {
                    //check if vertice does not intersect target polygon or is not close to it
                    //if vertice is outside polygon will not be added to vector layer
                    if (boundaryPolygon.geometry.intersects(featureVertices[j]) === false
                            && boundaryPolygon.geometry.distanceTo(featureVertices[j]) > 0.1)
                    {
                        isInsideTarget = false;
                        break vertices;
                    }

                    if(j < featureVertices.length - 1)
                    {
                        //create point in the middle of current and next points
                        var x = (featureVertices[j].x + featureVertices[j+1].x) / 2;
                        var y = (featureVertices[j].y + featureVertices[j+1].y) / 2;
                        var middlePoint = new OpenLayers.Geometry.Point(x, y);
                    }
                    else
                    {
                        //craete point in the middle of current and first points
                        var x = (featureVertices[j].x + featureVertices[0].x) / 2;
                        var y = (featureVertices[j].y + featureVertices[0].y) / 2;
                        var middlePoint = new OpenLayers.Geometry.Point(x, y);
                    }

                    //check if middle point is inside or close to boundary polygon boundaries
                    if (boundaryPolygon.geometry.intersects(middlePoint) === false
                            && boundaryPolygon.geometry.distanceTo(middlePoint) > 0.1)
                    {
                        isInsideTarget = false;
                        break vertices;
                    }
                }

                //only if polygon is inside
                if (isInsideTarget === true)
                {
                    tempVectors.addFeatures([feature]);
                    isInsideTarget = true;
                }
            }

            //save info for polygon replacement in global variable
            replacePolygonRequired = true;
            replacePolygonAction = 'split';

	        replacePolygonIdName = 'id';
	        replacePolygonIdArray.push(vectors.selectedFeatures[z].attributes.id);

            //###
            //Cut Holes
            //###
            var tempFeatures = new Array();

            holes: for (var i = 0; i < holeVectors.features.length; i++)
            {
                tempFeatures = new Array();

                features: for (var j = 0; j < tempVectors.features.length; j++)
                {
                    var targetFeature = reader.read(tempVectors.features[j].geometry.toString());
                    var holeFeature = reader.read(holeVectors.features[i].geometry.toString());
                    var diff = targetFeature.difference(holeFeature);
                    diff = parser.write(diff);

                    var diffOutput = new OpenLayers.Feature.Vector(diff);
                    tempFeatures.push(diffOutput);
                }

                tempVectors.removeAllFeatures();
                tempVectors.addFeatures(tempFeatures);
            }

            //###
            //End of Cut Holes
            //###

            tempFeatures = new Array();
            for(var i = 0; i < tempVectors.features.length; i++)
            {
                var geometry = tempVectors.features[i].geometry;

                if(geometry.CLASS_NAME == 'OpenLayers.Geometry.MultiPolygon')
                {
                    var jstsPolygon = reader.read(geometry.toString());
                    var polygonizer = new jsts.operation.polygonize.Polygonizer();

                    polygonizer.add(jstsPolygon);

                    var parser = new jsts.io.OpenLayersParser();

                    //iterate all result polygons
                    var polygons = polygonizer.getPolygons();
                    for (var j = polygons.iterator(); j.hasNext(); ) {
                        var polygon = j.next();
                        var feature = new OpenLayers.Feature.Vector(parser.write(polygon));
                        tempFeatures.push(feature);
                    }
                } else {
                    tempFeatures.push(tempVectors.features[i]);
                }
            }

            tempVectors.removeAllFeatures();
            tempVectors.addFeatures(tempFeatures);

            helpVectors.addFeatures(tempVectors.features);
        }

        vectors.removeFeatures(vectors.selectedFeatures);
        controlSelect.unselectAll();
        vectors.removeFeatures(olSplitLineFeature);

	    //add attributes
        for(var i = 0; i < helpVectors.features.length; i++)
        {
        	helpVectors.features[i].attributes.id = attributesObject.id;
        	helpVectors.features[i].attributes.ekatte = attributesObject.ekatte;
        	helpVectors.features[i].attributes.fr_gid = attributesObject.fr_gid;
        	helpVectors.features[i].attributes.is_system = attributesObject.is_system;
        	helpVectors.features[i].attributes.kategoria = attributesObject.kategoria;
        	helpVectors.features[i].attributes.kod_ntp = attributesObject.kod_ntp;
        	helpVectors.features[i].attributes.kod_sobstv = attributesObject.kod_sobstv;
        	helpVectors.features[i].attributes.kvs_no = attributesObject.kvs_no;
        	helpVectors.features[i].attributes.label = attributesObject.label;
        }

        vectors.addFeatures(helpVectors.features);
	});
}
