Namespace('TF.Rpc.Map');

var date = new Date();
var todayDate = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();

function chooseControl(control_name, skip_controls)
{
    var mapControls = map.options.controls;
    for (var i = 0; i < mapControls.length; i++)
    {
        var control = mapControls[i];
        if (mapControls[i].name === control_name)
        {
            control.activate();
        } else
        {
            if(typeof(skip_controls) != "undefined") {

                var skip_control = skip_controls.split(",");

                for (var j = 0; j < skip_control.length; j++) {
                    
                    if(mapControls[i].name !== skip_control[j]) {
                        control.deactivate();
                    }
                };
                
            }else{
                control.deactivate();
            }            
        }
    }
}