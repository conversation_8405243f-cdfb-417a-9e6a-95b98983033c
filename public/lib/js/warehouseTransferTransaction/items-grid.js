define([
    "jquery",
    "easyui",
    "js/main/EasyUIRPCLoaders",
    "js/main/rpc_errors_handler",
    "TF/Rpc/Warehouse/Items",
    "js/warehouse/utils",
    "js/warehouse/constants"
], function(jQ<PERSON>y, _, EasyUIRPCLoaders,RpcErrorHandler, Items, Utils, CONSTANTS) {
    function init() {}

    var itemsGrid;
    var itemsGridToolBarId = "#manage-warehouses-items-toolbar";

    function bindEventsListeners() {
        jQuery("#saveItemBtn").on("click", saveItem);

        jQuery(document).find(itemsGridToolBarId).on("keyup", ".datagrid_search_box", function () {
            var filters = {
                company:jQuery("#tr-company-id-out").val(),
                warehouses: [jQuery("#farm-warehouses-combobox-out").combobox('getValue')]
            };
            Utils.quickFilterDataGrid(itemsGrid, itemsGridToolBarId, 'item_name', filters);
        });
    }

    function initWarehousesItemsGrid(additionalColumns = [], warehouseId = null, companyId = null) {
        bindEventsListeners();

        var columns = [
            {
                field: "item_name",
                title: "<b>Име</b>",
                width: 120
            },
            {
                field: "measure_short_name",
                title: "<b>Мярка</b>",
                width: 50
            },
            {
                field: "warehouse_name",
                title: "<b>Склад</b>",
                width: 120
            },
            {
                field: "batch",
                title: "<b>Партида</b>",
                width: 100
            },
            {
                field: "expiry_date",
                title: "<b>Годност до</b>",
                width: 90
            }
        ];

        if(CONSTANTS.ALLOW_WAREHOUSE_SUM_QUANTITIES) {
            columns.push(
                {
                    field: "company_price",
                    title: "<b>Средна цена<br>за стопанство</b>",
                    width: 100,
                    align: 'right'
                },
                {
                    field: "company_quantity",
                    title: "<b>Количество<br>за стопанство</b>",
                    width: 90,
                    align: 'right'
                },
                {
                    field: "warehouse_quantity",
                    title: "<b>Количество<br>за склад</b></b>",
                    width: 90,
                    align: 'right'
                }
            );
        } else {
            columns.push(
                {
                    field: "priceNoDDS",
                    title: "<b>Цена<br>без ДДС</b>",
                    width: 100,
                    align: 'right'
                },
                {
                    field: "priceWithDDS",
                    title: "<b>Цена<br>с ДДС</b>",
                    width: 100,
                    align: 'right'
                },
                {
                    field: "quantity",
                    title: "<b>Количество</b>",
                    width: 90,
                    align: 'right'
                }
            );
        }

        //Add additional columns
        if(additionalColumns.length > 0) {
            for (var column of additionalColumns) {
                columns.unshift(column);
            }
        }

        itemsGrid = jQuery("#manage-warehouses-items-grid");
        itemsGrid.datagrid({
            autoRowHeight: true,
            autoRowWidth: true,
            url: "index.php?warehouse-rpc=warehouse-articles",
            rpcMethod: "getArticles",
            pagination: true,
            fit: true,
            border: false,
            singleSelect: true,
            rpcParams: [{
                criteries: {
                    warehouses: [warehouseId],
                    company: companyId
                }
            }],
            columns: [columns],
            toolbar: itemsGridToolBarId,
            onSelect: function(rowIndex, rowData) {},
            onLoadSuccess: function() {},
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }

    function saveItem() {
        var item = {};
        var endpoint;

        item.name = jQuery("#item-name").textbox("getValue");
        item.measure = jQuery("#item-measure").textbox("getValue");
        item.code = jQuery("#item-code").textbox("getValue");

        if (item.name === "" || item.measure === "") {
            jQuery.messager.alert(
                "Грешка",
                "Моля попълнете всички задължителни полета."
            );
            return false;
        }

        if (jQuery("#item-id").val() !== "") {
            item.id = parseInt(jQuery("#item-id").val());
            endpoint = Items.editItem(item);
        } else {
            endpoint = Items.addItem(item);
        }

        endpoint
            .done(function() {
                jQuery("#win-add-edit-item").window("close");
                jQuery("#items-grid").datagrid("reload");
            })
            .fail(function(errorObj) {
                jQuery.messager.alert(
                    "Грешка",
                    errorObj.getMessage(),
                    "warning"
                );
            });
    }

    return {
        init,
        initWarehousesItemsGrid
    };
});
