define([
    "jquery",
    "easyui",
    "js/main/EasyUIRPCLoaders",
    "js/warehouseTransferTransaction/items-grid",
    "js/warehouse/utils",
    "js/warehouse/constants"
], function(
    jQuery,
    _,
    EasyUIRPCLoaders,
    ItemsGrid,
    Utils,
    CONSTANTS
    ) {

    var item;
    var dds;

    function init() {
        initItemsGrid();
        bindEventsListeners();
    }

    function bindEventsListeners() {
        jQuery('div#rightSlidePanelCloseBtn').on("click", function () {
            Utils.closeRightSlidePanel()
        });

        jQuery(document).on("click", ".selectTransactionsItemBtn", function () {
            var index = jQuery(this).data("row-index");
            item =  jQuery("#manage-warehouses-items-grid").datagrid("getRows")[index];
            item.warehouse_quantity = item.quantity;
            jQuery("#price_no_dds").textbox('setValue', item.priceNoDDS);
            jQuery("#price_with_dds").textbox('setValue', item.priceWithDDS);

            //Reset Warehouse Fields
            jQuery(".warehouseFieldsList .container-box").empty();
            jQuery("#warehouseFields").hide();

            jQuery("#win-transfer-transactions").window("open");
        });

        jQuery(document).on("click", "#add-item-btn", function () {
            var quantity = jQuery("#quantity").textbox('getValue');
            if (parseFloat(quantity) > parseFloat(item.warehouse_quantity)) {
                jQuery.messager.alert("Грешка","Недостатъчна наличност.");
                return false;
            }

            item.warehouseFields = item.fields;
            item.quantity = quantity;
            item.priceNoDDS = jQuery("#price_no_dds").textbox('getValue');

            var note = jQuery("#note").textbox('getValue');
            if(note) item.note = note;

            Utils.insertItemInTransaction(item);
            Utils.reloadPrices(dds);
            closeTransferItemModal()
        });

        jQuery(document).on("click", "#reject-item-btn", function () {
            closeTransferItemModal()
        });

    }

    function initItemsGrid() {
        jQuery('#rightSlidePanelContent').find('.easyui-panel').panel({
            closable: true,
            onClose: function () {
                Utils.closeRightSlidePanel();
                jQuery(this).panel('open');
            }
        });

        jQuery("#tr-document-dds").combobox({
            onChange: function () {
                var ddsSelected = jQuery("#tr-document-dds").combobox("getValue");
                var ddsRows = jQuery("#tr-document-dds").combobox("getData");
                for (var ddsRow of ddsRows) {
                    if(ddsRow.data.key === ddsSelected) dds = ddsRow.data.value;
                }
                Utils.reloadPrices(dds);
            }
        });

        var addItemsGrid = jQuery("#added-items-grid");

        var columns = [
            {
                field: "item_name",
                title: "<b>Име</b>",
                width: 240
            },
            {
                field: "measure_name",
                title: "<b>Мярка</b>",
                width: 120
            },
            {
                field: "warehouse_name",
                title: "<b>Склад</b>",
                width: 120
            },
            {
                field: "quantity",
                title: "<b>Количество</b>",
                width: 120,
                align: 'right',
                editor: {
                    type: 'text'
                }
            },
            {
                field: "priceNoDDS",
                title: "<b>Цена без ДДС</b>",
                width: 140,
                align:'right',
                editor: {
                    type: 'text'
                }
            },
            {
                field: "priceWithDDS",
                title: "<b>Цена с ДДС</b>",
                width: 140,
                align:'right',
                formatter: function(value,row){
                    if(dds && row.priceNoDDS !== undefined) {
                        return (((dds * row.priceNoDDS) * 100) / 100).toFixed(5);
                    }
                }
            },
            {
                field: "priceSumNoDDS",
                title: "<b>Общо без ДДС</b>",
                width: 140,
                align:'right',
                formatter: function(value,row){
                    if(row.priceNoDDS !== undefined) {
                        return (((row.priceNoDDS * row.quantity) * 100) / 100).toFixed(5);
                    }
                }
            },
            {
                field: "note",
                title: "<b>Забележка</b>",
                width: 140,
                editor: {
                    type: 'text'
                }
            }
        ];

        var toolBar = [
            {
                id: "btn_add_event",
                text: "Добавяне",
                iconCls: "icon-add",
                handler: function() {
                    var warehouseOut = jQuery("#farm-warehouses-combobox-out").combobox("getValue");
                    var companyOut = jQuery("#tr-company-id-out").val();
                    if (!warehouseOut) {
                        jQuery.messager.alert("Грешка","Моля изберете склад, от който ще изписвате артикули.");
                        return false;
                    }
                    jQuery('.datagrid_search_box').val(''); //Reset quick filter
                    ItemsGrid.initWarehousesItemsGrid([
                            {
                                field: "addBtn",
                                title: "",
                                width: 40,
                                formatter: function (value, row, index) {
                                    return '<a data-row-index="' + index + '" class="l-btn l-btn-small l-btn-plain selectTransactionsItemBtn"><span class="l-btn-left l-btn-icon-left"><span class="l-btn-text"></span><span class="l-btn-icon icon-add"></span></span></a>';
                                }
                            }
                        ],
                        warehouseOut, companyOut);
                    Utils.openRightSlidePanel('selectManageWarehousesItemsTransfer');

                }
            },
            {
                id: "btn_edit_event",
                text: "Запази промените",
                iconCls: "icon-save",
                handler: function() {
                    jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да запазите промените?', function (r) {
                            if (r)  {
                                addItemsGrid.datagrid("acceptChanges");
                                Utils.reloadPrices(dds)
                            }
                        });

                }
            },
            {
                id: "btn_edit_event",
                text: "Откажи промените",
                iconCls: "icon-undo",
                handler: function() {
                    jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да откажете промените?', function (r) {
                        if (r)  addItemsGrid.datagrid("rejectChanges");
                    });

                }
            },
            {
                id: "btn_edit_event",
                text: "Премахване",
                iconCls: "icon-delete",
                handler: function() {
                    var item = addItemsGrid.datagrid("getSelected");
                    if (!item) {
                        jQuery.messager.alert("Грешка","Моля изберете артикул за изтриване.");
                        return false;
                    }
                    var rowIndex = addItemsGrid.datagrid("getRowIndex", item);

                    jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да премахнете избрания артикул?', function (r) {
                        if (r)  addItemsGrid.datagrid("deleteRow", rowIndex);
                    });

                }
            }
        ];

        addItemsGrid.datagrid({
            autoRowHeight: true,
            autoRowWidth: true,
            showFooter: true,
            fitColumns:true,
            data: {
                rows: [],
                total: 0,
                footer: Utils.generateFooter(dds)
            },
            rpcMethod: "read",
            pagination: true,
            border: false,
            singleSelect: true,
            checkbox: true,
            rpcParams: [{}],
            columns: [columns],
            toolbar: toolBar,
            onSelect: function(rowIndex, rowData) {
                jQuery("#btn_warehouse_fields").linkbutton({disabled:rowData.warehouseFields.length === 0});

                jQuery(this).datagrid('beginEdit', rowIndex);
            },
            onLoadSuccess: function() {},
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }

    // function drawWarehouseFields(fields, wrapper, fieldTemplate) {
    //     jQuery(".warehouseFieldsList .container-box").empty();
    //
    //     jQuery.each(fields, function (index, field) {
    //         if(field.key === CONSTANTS.WAREHOUSE_SYSTEM_TYPE_KEY) return; //Skip the system files
    //
    //         var res = fieldTemplate.replace(/{\w+}/g, function(placeholder) {
    //             var v = placeholder.replace(/[{}]/g, '');
    //             return v in field ? field[v] : placeholder;
    //         });
    //
    //         jQuery(res).appendTo(wrapper + " .container-box");
    //         jQuery('#'+field.key).textbox({
    //             value: field.value ? field.value : '',
    //         });
    //     });
    //
    //     jQuery(wrapper).show();
    // }

    function closeTransferItemModal() {
        jQuery("#quantity").textbox('clear');
        jQuery("#price_no_dds").textbox('clear');
        jQuery("#price_with_dds").textbox('clear');
        jQuery("#note").textbox('clear');

        jQuery("#win-transfer-transactions").window("close");
    }

    return {
        init
    };
});
