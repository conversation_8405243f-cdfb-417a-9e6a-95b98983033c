define([
    "easyui",
    "js/main/EasyUIRPCLoaders",
    "js/main/rpc_errors_handler",
    "js/warehouseTransferTransaction/add-items-grid",
    "TF/Rpc/Warehouse/Transactions",
    "js/warehouse/utils",
    "TF/Rpc/Warehouse/Warehouses",
    "TF/Rpc/Warehouse/Documents",
    "js/warehouse/companies-grid",
    "js/warehouse/documents-grid",
    "js/warehouse/items-grid",
    "js/warehouse/measures-grid",
    "js/warehouse/comboboxes",
    "js/warehouse/constants"
], function(
    _,
    EasyUIRPCLoaders,
    RpcErrorHandler,
    AddItemsGrid,
    Transactions,
    Utils,
    Warehouses,
    Documents,
    CompaniesGrid,
    DocumentsGrid,
    ItemsGrid,
    MeasuresGrid,
    Comboboxes,
    CONSTANTS
) {
    function init() {
        AddItemsGrid.init();
        Comboboxes.constants(
            "#tr-document-type",
            ['WAREHOUSE_NOTE_TRANSFER'],
            {get: "documents.types.transfer"},
            true,
            true
        );

        Comboboxes.constants(
            "#tr-document-dds",
            [],
            {get: "documents.dds"},
            false,
            true
        );

        jQuery("#tr-document-date").datebox('setValue', new Date());

        var document_id = sessionStorage.getItem("document_id");
        if(document_id > 0) {
            jQuery('#selectFarmOutBtn').linkbutton('disable');
            var params = {
                criteries: {
                    document_id: document_id
                },
                parameters: {
                    mergedTransactions: true
                }
            };
            Utils.initEditDocument(params, AddItemsGrid);
        }
        bindEventsListeners();
    }

    function bindEventsListeners() {
        jQuery("#saveTransactionBtn").on("click", function () {
            var requestData = getTransferTransactionData();
            if(!requestData) return;

            Utils.storeTransaction(requestData);
        });

        jQuery("#saveAndCloseTransactionBtn").on("click", function () {
            jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да преместите избраните артикули?', function (r) {
                if (r) {
                    var requestData = getTransferTransactionData();
                    if(!requestData) return;

                    requestData.document.closed = 1;
                    Utils.storeTransaction(requestData);
                }
            });
        });

        jQuery('#selectFarmOutBtn').on("click", function () {
            var btnOptions = jQuery('#selectFarmOutBtn').linkbutton('options');
            if(btnOptions.disabled) return;

            CompaniesGrid.initContragentsGrid(
                {types: [CONSTANTS.COMPANY_TYPE_FARM]},
                [
                    {
                        field: "selectFarm",
                        title: "",
                        width: 40,
                        formatter: function(value,row,index){
                            return '<a data-row-index="'+index+'" data-farm-role="out" class="l-btn l-btn-small l-btn-plain js-selectCompany"><span class="l-btn-left l-btn-icon-left"><span class="l-btn-text"></span><span class="l-btn-icon icon-add"></span></span></a>';
                        }
                    }],
                '#farms-grid-rp',
                '#farms-toolbar-rp'
            );

            Utils.openRightSlidePanel('selectFarm');
        });

        jQuery('#selectFarmInBtn').on("click", function () {
            var farm_out = jQuery("#tr-company-id-out").val();
            var warehouse_out = jQuery("#farm-warehouses-combobox-out").combobox('getValue');

            if(!farm_out || !warehouse_out){
                jQuery.messager.alert('Грешка', 'Моля изберете стопанство и склад за изписване.');
                return;
            }

            CompaniesGrid.initContragentsGrid(
                {
                    types: [CONSTANTS.COMPANY_TYPE_FARM],
                    warehouses: [warehouse_out]
                },
                [
                    {
                        field: "selectFarm",
                        title: "",
                        width: 40,
                        formatter: function(value,row,index){
                            return '<a data-row-index="'+index+'" data-farm-role="in" class="l-btn l-btn-small l-btn-plain js-selectCompany"><span class="l-btn-left l-btn-icon-left"><span class="l-btn-text"></span><span class="l-btn-icon icon-add"></span></span></a>';
                        }
                    }],
                '#farms-grid-rp',
                '#farms-toolbar-rp'
            );

            Utils.openRightSlidePanel('selectFarm');
        });

        jQuery(document).on("click", ".js-selectCompany", function () {
            var index = jQuery(this).data("row-index");
            var role = jQuery(this).data("farm-role");

            var rowData =  jQuery("#farms-grid-rp").datagrid("getRows")[index];

            if(!CONSTANTS.ITEMS_WITH_WAREHOUSE) {
                Utils.initTransferWarehousesCombobox(role, rowData.id);
            } else if(role === 'out') {
                    Utils.initTransferWarehousesCombobox(role, rowData.id);
            } else {
                var farmOut = jQuery("#tr-company-id-out").val();
                if(rowData.id == farmOut){
                    jQuery.messager.alert('Грешка', 'Не може да прехвърляте артикул от склад и фирма в същия склад и фирма..');
                    jQuery(this).combobox("clear");
                    return;
                }
            }

            jQuery("#js-selected-farm-" + role).text(rowData.name);
            jQuery("#tr-company-id-" + role).val(rowData.id);

            Utils.closeRightSlidePanel();
        });
    }

    function getTransferTransactionData() {
        var document_id = jQuery("#document-id").val();
        var transaction_type = jQuery("#transaction_type").val();
        var dds = jQuery("#tr-document-dds").combobox('getValue');

        var farm_out = jQuery("#tr-company-id-out").val();
        if(!farm_out){
            jQuery.messager.alert('Грешка', 'Моля изберете стопанство за изписване.');
            return;
        }

        var warehouse_out = jQuery("#farm-warehouses-combobox-out").combobox('getValue');
        if(!warehouse_out){
            jQuery.messager.alert('Грешка', 'Моля изберете склад за изписване.');
            return;
        }

        var farm_in = jQuery("#tr-company-id-in").val();
        if(!farm_in){
            jQuery.messager.alert('Грешка', 'Моля изберете стопанство за заприхождаване.');
            return;
        }
        var warehouse_in = jQuery("#farm-warehouses-combobox-in").combobox('getValue')
        if(!warehouse_in){
            jQuery.messager.alert('Грешка', 'Моля изберете склад за заприхождаване.');
            return;
        }

        var date = jQuery("#tr-document-date").datebox('getValue');
        var number = jQuery("#tr-document-number").textbox('getValue');

        var vehicle_number = jQuery("#tr-vehicle-number").textbox('getValue');
        var driver_name = jQuery("#tr-driver-name").textbox('getValue');

        if(!date || !number){
            jQuery.messager.alert('Грешка', 'Моля попълнете всички задължителни за документа полета.');
            return;
        }

        var addedItems = jQuery("#added-items-grid").datagrid("getData");

        if(addedItems.total === 0){
            jQuery.messager.alert('Грешка', 'Няма избрани артикули.');
            return;
        }

        var requestData = {
            "document": {},
            "transactions": []
        };

        requestData.document = {
            "document_id": parseInt(document_id),
            "transaction_type": transaction_type,
            "farm_in": parseInt(farm_in),
            "farm_out": parseInt(farm_out),
            "warehouse_in": jQuery("#farm-warehouses-combobox-in").combobox('getValue'),
            "warehouse_out": jQuery("#farm-warehouses-combobox-out").combobox('getValue'),
            "number": number,
            "dds": dds,
            "date": date,
            "vehicle_number": vehicle_number,
            "driver_name": driver_name,
            "closed": 0
        };

        for(var item of addedItems.rows) {
            requestData.transactions.push({
                "item_id": parseInt(item.item_id),
                "warehouse_id": parseInt(item.warehouse_id),
                "quantity": item.quantity,
                "single_price_no_dds": item.priceNoDDS,
                "single_price_with_dds": item.priceWithDDS,
                "batch": item.batch,
                "expiry_date": item.expiryDate,
                "measure_id": parseInt(item.measure_id),
                "note": item.note,
                "warehouseFields": item.warehouseFields ? item.warehouseFields : [],
            });
        }

        return requestData;
    }

    return {
        init
    };

});
