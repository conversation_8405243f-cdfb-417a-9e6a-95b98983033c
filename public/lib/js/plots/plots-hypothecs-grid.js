function initPlotsHypothecsGrid(plot_id) {

    var hypothecsDataGrid = jQuery('#hypothec-info-tables');
    var isDatagridBound = hypothecsDataGrid.data().hasOwnProperty('datagrid');

    if (isDatagridBound) {
        if (plot_id !== 0) {
            hypothecsDataGrid.datagrid({
                url: 'index.php?plots-rpc=plots-hypothecs-datagrid',
                rpcParams: [plot_id],
            });
        } else {
            hypothecsDataGrid.datagrid('loadData', {rows: [], total: 0});
        }
        return;
    }
    hypothecsDataGrid.datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        iconCls: 'icon-rents',
        title: 'Ипотеки',
        pageSize: 10,
        singleSelect: false,
        fitColumns: true,
        showFooter: true,
        fit: true,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
        sortName: 'h.id',
        sortOrder: 'desc',
        idField: 'id',
        rowStyler: function(index, row) {
            if (!row.is_active) {
                return 'color: #aaa';
            }
        },
        columns:[[{
            field:'num',
            title:'<b>Номер</b>',
            sortable:true,
            width: 150
        },{
            field:'start_date',
            title:'<b>Влиза в сила</b>',
            sortable:true,
            width: 150,
            formatter: gridDateFormatter
        },{
            field:'due_date',
            title:'<b>Падеж</b>',
            sortable:true,
            width: 150,
            formatter: gridDateFormatter
        },{
            field:'creditor',
            title:'<b>Кредитор</b>',
            sortable:true,
            width: 200
        },{
            field:'hypothec_area',
            title:'<b>Ипотекирана<br>площ (дка)</b>',
            align: 'center',
            sortable:true,
            width: 200,
            formatter: function(value){
                if (!value){
                    return '-';
                }

                return parseFloat(value).toFixed(3);
            }
        },{
            field:'comment',
            title:'<b>Забележка</b>',
            sortable:true,
            width: 200,
            formatter: function(value){
                if (!value){
                    return '-';
                }

                return value;
            }
        }
        ]],
        pagination:true,
        rownumbers:true,
        toolbar: [{
            text:'Информация',
            iconCls:'icon-info',
            handler:function(){
                var checkedRows = jQuery('#hypothec-info-tables').datagrid('getChecked');
                    if (!checkedRows[0]) {
                        jQuery.messager.alert('Грешка', 'Моля изберете ипотека.');
                        return;
                    }

                    var idString = jQuery.map(checkedRows, function(e) {
                        return e.id;
                    }).join();

                    window.open("index.php?page=Hypothecs.Home&hypothec_id=" + idString, '_blank');
            }
        }],
        onBeforeLoad: function() {
			if (jQuery(this).datagrid('getChecked')) {
	            jQuery(this).datagrid("clearChecked");
			}
        }
    });
}

function gridDateFormatter(value) {
    if (!value){
        return '-';
    }

    var newValue = value.split('-').reverse().join('.');

    return newValue;
}
