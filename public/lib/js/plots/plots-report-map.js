var reportMap;
function initReportMap(){
    if(!reportMap){
        var options = {
            controls: [new OpenLayers.Control.Navigation()],
            projection: new OpenLayers.Projection("EPSG:900913")    
        };
        reportMap = new OpenLayers.Map('map', options); 
        var apiKey = "AiWEso3-IjWyX1aZMuep9Sjl62D6FUMqv8qQGpV-kgbW0qWk61to4nrqHh-2D5HL";
        var bhyb = new OpenLayers.Layer.Bing({
            name: "<PERSON>",
            key: apiKey,
            type: "AerialWithLabels"
        });
                    
        reportMap.addLayer(bhyb);
        //map.addLayer(ghyb);
    
        initReportMapLayers();
        initMapControls();
    }
    reportMap.render("report-map");
}

function initReportMapLayers(){
    boundsArray = [];
    for(var i=0;i<layers.length;i++){
        boundsArray[i] = new OpenLayers.Bounds.fromString(layers[i].extent).transform(
            new OpenLayers.Projection("EPSG:32635"),
            reportMap.getProjectionObject()
        );
    }
    
    layerArray = [];
    for(var i=0;i<layers.length;i++)
    {
        if(layers[i].extent && layers[i].name) 
        {
            layerArray[i] = new OpenLayers.Layer.WMS(
                layers[i].name,
                wmsServer+"?map="+mapPath+groupID+'.map',
                {   
                    layers: layers[i].name,                   
                    format: 'image/png',	         
                    transparent: "true"
                //map: mapPath+userid+'.map'
                },{
                    displayInLayerSwitcher: false
                }
            ); 
        }
    }             
                    
    if (layers.length) reportMap.addLayers(layerArray);    
      
    if(layers.length) 
    {
        reportMap.zoomToExtent(boundsArray[0]);
    } 
    else 
    {
        reportMap.zoomToExtent(new OpenLayers.Bounds.fromString("125190.6162, 4573142.7188, 631370.3273, 4887149.5823").transform(
            new OpenLayers.Projection("EPSG:32635"),
            reportMap.getProjectionObject())
        );
    }
}

function reloadAllReportMapLayers(extent) { 
	if(extent)
    {
        reportMap.zoomToExtent(new OpenLayers.Bounds.fromString(extent).transform(
                new OpenLayers.Projection("EPSG:32635"),
                reportMap.getProjectionObject()));
    }
	else 
    {
        reportMap.zoomToExtent(new OpenLayers.Bounds.fromString("125190.6162, 4573142.7188, 631370.3273, 4887149.5823").transform(
            new OpenLayers.Projection("EPSG:32635"),
            reportMap.getProjectionObject())
        );
    }
	
	for(var i=0;i<layerArray.length;i++) {
        layerArray[i].redraw(true);
    }
}