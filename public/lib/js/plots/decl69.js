var _fileName;
function initDecl69ChosenGrid(ekate, year, farming) {
	declYear = year;
	declEkate = ekate;
	declFarming = farming;

	jQuery('#plot-number-format-word-69-combobox').combobox({
        data: [
            {
                value: "",
                text: "[Масив][Парцел]"
            },
            {
                value: "dot_separator",
                text: "[Масив].[Парцел]"
            },
            {
                value: "underscore_separator",
                text: "[Масив]_[Парцел]",
                selected: true
            }
        ],
        valueField: 'value',
        textField: 'text',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter

    });

	jQuery('#zerro-lpad-word-69-combobox').combobox({
        data: [
            {
                value: "4",
                text: "Кад. формат",
                selected: true
            },
            {
                value: "3",
                text: "КВС формат",
            },
            {
                value: "",
                text: "Без нули",
            }
            
        ],
        valueField: 'value',
        textField: 'text',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

	jQuery('#declaration-chosen-tables').datagrid({
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		iconCls: 'icon-edit-geometry',
		title: chosenHeading,
		showFooter: true,
		fit: true,
		fitColumns: true,
		border: false,
		url: 'index.php?plots-rpc=declaration-chosen-grid',
		rpcParams: [{
			ekate: declEkate,
			year: declYear,
			farming: declFarming,
			action: 'open'
		}],
		rpcMethod: 'decl69',
		sortName: 'kad_ident',
		sortOrder: 'asc',
		frozenColumns: [
			[
				{
					field: 'ck',
					checkbox: true
				}
			]
		],
		rowStyler: function(index, row) {
			if (!row.include && !row.participate) {
				return 'background-color:#FF0000;';
			}
			if (row.is_subleased) {
				return 'background-color:#FF9900;';
			}
		},
		columns: [
			[
				{
					field: 'kad_ident',
					title: '<b>Идентификатор</b>',
					sortable: true,
					width: 130
				}, {
				field: 'area',
				title: '<b>Площ<br/>(дка)</b>',
				sortable: true,
				width: 50
			}, {
				field: 'area_type',
				title: '<b>НТП</b>',
				sortable: true,
				width: 150
			}, {
				field: 'c_num',
				title: '<b>Договор</b>',
				sortable: true,
				width: 70
			}, {
				field: 'c_type',
				title: '<b>Тип<br/>договор</b>',
				sortable: true,
				width: 90
			}, {
				field: 'subleases',
				title: '<b>Преотдаден<br/>чрез</b>',
				sortable: true,
				width: 100
			}
			]
		],
		rownumbers: true,
		toolbar: '#decl69-chosen-toolbar',
		onBeforeLoad: function() {
		},
		onLoadSuccess: function(){
			initDecl69ChooseGrid(ekate, year, farming);
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function initDecl69ChooseGrid(ekate, year, farming) {

	declYear = year;
	declEkate = ekate;
	declFarming = farming;

	jQuery('#declaration-choose-tables').datagrid({
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		iconCls: 'icon-edit-geometry',
		title: chooseHeading,
		pageSize: 10,
		fit: true,
		fitColumns: true,
		showFooter: true,
		url: 'index.php?plots-rpc=declaration-choose-grid',
		rpcParams: [{
			ekate: declEkate,
			year: declYear,
			farming: declFarming,
			action:'open'
		}],
		rpcMethod: 'decl69',
		sortName: 'c.id',
		sortOrder: 'asc',
		idField: 'pc_rel_id',
		frozenColumns: [
			[
				{
					field: 'ck',
					checkbox: true
				}
			]
		],
		columns: [
			[
				{
					field: 'kad_ident',
					title: '<b>Идентификатор</b>',
					sortable: true,
					width: 130
				}, {
				field: 'area',
				title: '<b>Площ<br/>(дка)</b>',
				sortable: true,
				width: 50
			}, {
				field: 'area_type',
				title: '<b>НТП</b>',
				sortable: true,
				width: 150
			}, {
				field: 'c_num',
				title: '<b>Договор</b>',
				sortable: true,
				width: 70
			}, {
				field: 'c_type',
				title: '<b>Тип<br/>договор</b>',
				sortable: true,
				width: 90
			}, {
				field: 'subleases',
				title: '<b>Преотдаден<br/>чрез</b>',
				sortable: true,
				width: 100
			}
			]
		],
		pagination: true,
		rownumbers: true,
		toolbar: [{
			id: 'btnaddtoanketnakarta',
			text: 'Добавяне',
			iconCls: 'icon-add',
			handler: function() {
				var getChecked = jQuery('#declaration-choose-tables').datagrid('getChecked');
				if (getChecked[0]) {
					var idArray = [];

					for (var i = 0; i < getChecked.length; i++) {
						idArray[i] = getChecked[i]['pc_rel_id'];
					}

					TF.Rpc.Plots.DeclarationChosenGrid.addToDeclaration(idArray)
						.done(function () {
							jQuery('#declaration-choose-tables').datagrid('options').rpcParams[0].action = 'addPlot';
							jQuery('#declaration-chosen-tables').datagrid('options').rpcParams[0].action = 'addPlot';

							jQuery('#declaration-choose-tables').datagrid('reload');
							jQuery('#declaration-chosen-tables').datagrid('reload');
						})
						.fail();
				} else {
					jQuery.messager.alert('Грешка', 'Моля изберете имоти, които да бъдат добавени към декларацията.');
				}
			}
		}, {
			id: 'btnfilteranketnakartaadd',
			text: 'Филтър',
			iconCls: 'icon-filter',
			handler: function() {
				jQuery('#win-anketna-karta-filter').window('open');
			}
		}, {
			id: 'btnfilteranketnakartaclear',
			text: 'Покажи всички',
			iconCls: 'icon-clear-filter',
			handler: function() {
				clearAnketnaKartaFilter();
			}
		}
		],
		onBeforeLoad: function() {
			jQuery('#declaration-choose-tables').datagrid('clearChecked');
		},
		rowStyler: function(index, row) {
			if (row.is_subleased) {
				return 'background-color:#FF9900;';
			}
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

jQuery(function () {
	jQuery('#print-decl-69-word').bind('click', function () {
		var winDownload = jQuery('#win-download'),
			downloadFile = jQuery('#btn-download-file'),
			year = jQuery('#choose-decl-year > input').combobox('getValue'),
			ekate = jQuery('#choose-decl-ekate > input').combobox('getValue'),
			farming = jQuery('#choose-decl-farming > input').combobox('getValue'),
			ntp = jQuery('#ak-results-ntp > input').combobox('getValues'),
			from_date = jQuery('#ak-results-from-date > input').datebox('getValue'),
			to_date = jQuery('#ak-results-to-date > input').datebox('getValue'),
			gridOptions = jQuery('#declaration-chosen-tables').datagrid('options'),
			isForCadastralMap = jQuery('#export-decl-cadastral-map > input').is(':checked'),
			splitLegalGrounds = jQuery('#export-decl-split-legal-grounds > input').is(':checked'),
			plotNumberFormat = jQuery('#plot-number-format-word-69-combobox').combobox('getValue'),
			zerroLpad = jQuery('#zerro-lpad-word-69-combobox').combobox('getValue'),

			exportObject = {
				farming: farming,
				year: year,
				ekate: ekate,
				ntp: ntp,
				from_date: from_date,
				to_date: to_date,
				has_zerro_lpad: zerroLpad,
				plot_number_format: plotNumberFormat,
				is_for_cadastral_map: isForCadastralMap,
				split_legal_grounds: splitLegalGrounds,
				titlePageInfo: getDecl69TitlePageInfo()
			},
			sort = gridOptions.sortName,
			order = gridOptions.sortOrder;

		TF.Rpc.Plots.PlotsDeclarations.createDecl69(exportObject,null,null,sort,order)
			.done(function (data)
			{
				jQuery('#win-declaration-69-info').window('close');
				winDownload.window('open');
				var path = data.word_file_location;
				_pathFile = path;
				_fileName = data.file_name;
				downloadFile.attr("href", path);
			})
			.fail(function () {
				jQuery.messager.alert('Грешка', 'Възникна грешка при обработка на данните', 'warning');
			});
	});
	jQuery('#clear-decl-69-word').bind('click', function () {
		jQuery('#decl-69-osz-city').val('');
		jQuery('#decl-69-osz-oblast').val('');
		jQuery('#decl-69-owner-names').val('');
		jQuery('#decl-69-owner-egn').val('');
		jQuery('#decl-69-company-name').val('');
		jQuery('#decl-69-company-bulstat').val('');
		jQuery('#decl-69-company-city').val('');
		jQuery('#decl-69-company-municipality').val('');
		jQuery('#decl-69-company-region').val('');
		jQuery('#decl-69-company-address').val('');
		jQuery('#decl-69-company-phone').val('');
		jQuery('#decl-69-company-mail').val('');
		jQuery('#decl-69-prepiska-number').val('');
		jQuery('#decl-69-prepiska-date').val('');
		jQuery('#decl-69-prepiska-city').val('');
		jQuery('#decl-69-zplots-city').val('');
		jQuery('#decl-69-palnomoshntnik-name').val('');
		jQuery('#decl-69-palnomoshntnik-egn').val('');
		jQuery('#decl-69-palnomoshntnik-city').val('');
		jQuery('#decl-69-palnomoshntnik-municipality').val('');
		jQuery('#decl-69-palnomoshntnik-region').val('');
		jQuery('#decl-69-palnomoshntnik-address').val('');
		jQuery('#decl-69-palnomoshntnik-phone').val('');
		jQuery('#decl-69-palnomoshntnik-mail').val('');
		jQuery('#decl-69-palnomoshntno-number').val('');
		jQuery('#decl-69-palnomoshntno-date').val('');
		jQuery('#decl-69-palnomoshntno-zaverka').val('');
	});
});

function getDecl69TitlePageInfo() {
	return {
		osz_city: jQuery('#decl-69-osz-city').val(),
		osz_oblast: jQuery('#decl-69-osz-oblast').val(),
		farming_year: jQuery('#decl-69-farming-year').html(),
		owner_names: jQuery('#decl-69-owner-names').val(),
		owner_egn: jQuery('#decl-69-owner-egn').val(),
		company_name: jQuery('#decl-69-company-name').val(),
		company_bulstat: jQuery('#decl-69-company-bulstat').val(),
		company_city: jQuery('#decl-69-company-city').val(),
		company_municipality: jQuery('#decl-69-company-municipality').val(),
		company_region: jQuery('#decl-69-company-region').val(),
		company_address: jQuery('#decl-69-company-address').val(),
		company_phone: jQuery('#decl-69-company-phone').val(),
		company_mail: jQuery('#decl-69-company-mail').val(),
		prepiska_number: jQuery('#decl-69-prepiska-number').val(),
		prepiska_date: jQuery('#decl-69-prepiska-date').val(),
		prepiska_city: jQuery('#decl-69-prepiska-city').val(),
		zplots_city: jQuery('#decl-69-zplots-city').val(),
		palnomoshntnik_name: jQuery('#decl-69-palnomoshntnik-name').val(),
		palnomoshntnik_egn: jQuery('#decl-69-palnomoshntnik-egn').val(),
		palnomoshntnik_city: jQuery('#decl-69-palnomoshntnik-city').val(),
		palnomoshntnik_municipality: jQuery('#decl-69-palnomoshntnik-municipality').val(),
		palnomoshntnik_region: jQuery('#decl-69-palnomoshntnik-region').val(),
		palnomoshntnik_address: jQuery('#decl-69-palnomoshntnik-address').val(),
		palnomoshntnik_phone: jQuery('#decl-69-palnomoshntnik-phone').val(),
		palnomoshntnik_mail: jQuery('#decl-69-palnomoshntnik-mail').val(),
		palnomoshntno_number: jQuery('#decl-69-palnomoshntno-number').val(),
		palnomoshntno_date: jQuery('#decl-69-palnomoshntno-date').val(),
		palnomoshntno_zaverka: jQuery('#decl-69-palnomoshntno-zaverka').val()
	}
}

function removePlotFromDecl69(docType = 'declaration'){
	var docTypeText = 'декларацията';
	if(docType === 'application') docTypeText = 'заявлението'

	var getChecked = jQuery('#declaration-chosen-tables').datagrid('getChecked');
	if (getChecked[0]) {
		jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да премахнете избраните имоти от ' + docTypeText + ' ?', function(r) {
			if (r) {
				var idArray = [];

				for (var i = 0; i < getChecked.length; i++) {
					idArray[i] = getChecked[i]['pc_rel_id'];
				}

				TF.Rpc.Plots.DeclarationChosenGrid.deleteFromDeclaration(idArray)
					.done(function () {
                        jQuery('#declaration-choose-tables').datagrid('options').rpcParams[0].action = 'removePlot';
                        jQuery('#declaration-chosen-tables').datagrid('options').rpcParams[0].action = 'removePlot';

						jQuery('#declaration-chosen-tables').datagrid('reload');
						jQuery('#declaration-choose-tables').datagrid('reload');
					})
					.fail();
			}
		});
	} else {
		jQuery.messager.alert('Грешка', 'Моля изберете имоти, които да бъдат добавени към декларацията.');
	}
}

function decl69ExportWord() {
	if (jQuery('#declaration-chosen-tables').datagrid('getData').rows.length == 0) {
		jQuery.messager.alert('Внимание!','Няма включени имоти в декларацията.');
	}else{
		var farmingYearText = jQuery('#choose-decl-year > input').combobox('getText');
		fillWordDeclaration69Fields();
		jQuery('#decl-69-farming-year').html(farmingYearText.substr(10));
		jQuery('#win-declaration-69-info').window('open');
		jQuery('#win-declaration-69-info').window('center');
	}
}

function fillWordDeclaration69Fields(){
	let selectedFarm = getSelectedFarm();
	let selectedEkatteName = getSelectedEkatteName();
	if(!selectedFarm) return;

	jQuery('#decl-69-owner-names').val(selectedFarm.mol);
	jQuery('#decl-69-owner-egn').val(selectedFarm.mol_egn);
	jQuery('#decl-69-company-name').val(selectedFarm.name);
	jQuery('#decl-69-company-bulstat').val(selectedFarm.bulstat);
	jQuery('#decl-69-company-address').val(selectedFarm.address);
	jQuery('#decl-69-company-phone').val(selectedFarm.farming_mol_phone);
	jQuery('#decl-69-zplots-city').val(selectedEkatteName);
}

function decl69ExportCSV() {
	if (jQuery('#declaration-chosen-tables').datagrid('getData').rows.length == 0) {
		jQuery.messager.alert('Внимание!','Няма включени имоти в декларацията.');
	}else{
		jQuery('#win-decl-export-info').window('open');
		document_type = 'csv';
	}
}

function decl69ExportXLS() {
	if (jQuery('#declaration-chosen-tables').datagrid('getData').rows.length == 0) {
		jQuery.messager.alert('Внимание!','Няма включени имоти в декларацията.');
	}else{
		fillExcelDeclarationFields();
		jQuery('#win-decl-export-info').window('open');
		document_type = 'xls';
	}
}
