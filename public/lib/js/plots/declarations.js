var exportType = false;
var declYear = 0;
var declEkate = 0;
var declFarming = 0;
var declWindowHeading = '';
var chooseHeading = '';
var chosenHeading = '';
var document_type = '';

function initDeclType(declType) {
    var showEkatteCombobox = true;
    var footerHeight = 70;

    jQuery('#ekatte-decl-input').show();
    jQuery('#year-decl-input').show();
    jQuery('#decl69-chosen-toolbar').hide();
    jQuery('#decl70-chosen-toolbar').hide();
    jQuery('#anketna-karta-chosen-toolbar').hide();
    jQuery('#year-decl-73-input').hide();
    jQuery('#dividend-decl-73-checkbox').hide();
    jQuery('#with-dividend-decl-73 > input').prop('checked', false);

    if (jQuery('#declaration-chosen-tables').data().hasOwnProperty('datagrid')) {
        jQuery('#declaration-chosen-tables').datagrid('clearChecked');
        jQuery('#rightLayout .datagrid-toolbar').appendTo('#bodydiv');
    }

    jQuery('#anketna-karta-msg').hide();

    if (declType == 'decl69') {
        exportType = 'decl69';
        chooseHeading = 'Избор на имоти за добавяне към декларация по чл.69';
        chosenHeading = 'Имоти към декларация по чл.69';
        declWindowHeading = 'Декларация по чл.69';
        footerHeight = 106;

        jQuery('#decl-red-color-info').html('За имотите, маркирани в този цвят, не е избрано желание по член 37в от ЗСПЗЗ. За декларацията по член 69 от ППЗСПЗЗ е задължително да се избере желание.');
        jQuery('#decl-orange-color-info').html('Имотите, маркирани в този цвят, участват в договори за преотдаване за избраната стопанска година.');
    }

    if (declType == 'decl70') {
        exportType = 'decl70';
        chooseHeading = 'Избор на имоти за добавяне към заявление по чл.70';
        chosenHeading = 'Имоти към заявление по чл.70';
        declWindowHeading = 'Заявление по чл.70';
        footerHeight = 150;

        jQuery('#decl-red-color-info').html('За имотите, маркирани в този цвят, не е избрано желание по член 37в от ЗСПЗЗ. За заявлението по член 70 от ППЗСПЗЗ е задължително да се избере желание.');
        jQuery('#decl-orange-color-info').html('Имотите, маркирани в този цвят, участват в договори за преотдаване за избраната стопанска година.');
        jQuery('#decl-bold-font-info').html('От площта на имотите с удебелен текст е преотдадена идеална част. Може да намерите тази площ в друг ред на таблицата с оранжев цвят и същия номер на договор.');
    }

    if (declType == 'declPML') {
        exportType = 'declPML';
        chooseHeading = 'Избор на имоти за добавяне към заявление ПМЛ';
        chosenHeading = 'Имоти към заявление ПМЛ';
        declWindowHeading = 'Заявление ПМЛ';
        showEkatteCombobox = false;
        footerHeight = 70;

        jQuery('#decl-orange-color-legend').hide();
        jQuery('#decl-red-color-info').html('За имотите, маркирани в този цвят, не е избрано желание по член 37ж от ЗСПЗЗ. За опис към заявление ПМЛ е задължително да се избере желание.');
    }

    if (declType == 'anketna-karta') {
        exportType = 'anketna-karta';
        chooseHeading = 'Избор на имоти за добавяне към анкетна карта';
        chosenHeading = 'Имоти към анкетна карта';
        declWindowHeading = 'Анкетна карта';
        footerHeight = 178;
        jQuery('#anketna-karta-msg').show();

        jQuery('#decl-orange-color-info').html('Имотите, маркирани в този цвят, участват в договори за преотдаване за избраната стопанска година.');
        jQuery('#decl-red-color-legend').show();
        jQuery("#decl-red-color-legend span").html("Имотите, маркирани в този цвят /червено/ са с различен НТП от допустимият за споразумение по 37в. Можете да промените НТП, ако е необходимо чрез бутона за редакция на имота.");
    }

    var ps = jQuery('#rightLayout').layout('panel', 'south');
    ps.panel('resize', {height: footerHeight});
    jQuery('#rightLayout').layout('resize');

    jQuery('#ekatte-decl-input').toggle(showEkatteCombobox);

    if (declType == 'decl73') {
        jQuery('#ekatte-decl-input').hide();
        jQuery('#year-decl-input').hide();
        jQuery('#year-decl-73-input').show();
        jQuery('#dividend-decl-73-checkbox').show();
        exportType = 'decl73';
        declWindowHeading = 'Декларация по чл. 73';
    }

    jQuery('#win-choose-decl-type').window('open').window('setTitle', 'Избор на ' + declWindowHeading);
}

function initDeclarations() {
    jQuery('#plot-number-format-combobox').combobox({
        data: [
            {
                value: "",
                text: "[Масив][Парцел]"
            },
            {
                value: "dot_separator",
                text: "[Масив].[Парцел]"
            },
            {
                value: "underscore_separator",
                text: "[Масив]_[Парцел]",
                selected: true
            }
        ],
        valueField: 'value',
        textField: 'text',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter

    });

	jQuery('#zerro-lpad-combobox').combobox({
        data: [
            {
                value: "4",
                text: "Кад. формат",
                selected: true
            },
            {
                value: "3",
                text: "КВС формат",
            },
            {
                value: "",
                text: "Без нули",
            }
            
        ],
        valueField: 'value',
        textField: 'text',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    TF.Rpc.Plots.DeclarationChosenGrid.clearDeclarations()
        .done(function (data) {
            initDeclarationGrids();
            initAnketnaKartaFilterFields();
            initAnketnaKartaResultsFilterFields();
        })
        .fail(function (data) {
            jQuery.messager.alert('Грешка', 'Възникна грешка при обработка на данните.');
        });
}

function initDeclarationGrids() {
    var year = jQuery('#choose-decl-year > input').combobox('getValue');
    var farming = jQuery('#choose-decl-farming > input').combobox('getValue');
    var ekate = jQuery('#choose-decl-ekate > input').combobox('getValue');

    if (exportType === 'decl73') {
        withDividendDecl73 = jQuery('#with-dividend-decl-73 > input').prop('checked');
        yearDecl73 = jQuery('#choose-decl-73-year > input').combobox('getValue');
        initDecl73Grid(yearDecl73, farming, withDividendDecl73);
        jQuery('#win-declaration-73').window('open');
        return;
    }

    jQuery('#win-declarations').window('setTitle', declWindowHeading);
    jQuery('#win-declarations').window('open');

    if (exportType === 'decl69') {
        initDecl69ChosenGrid(ekate, year, farming);
    } else if (exportType === 'decl70') {
        initDecl70ChosenGrid(ekate, year, farming);
    } else if (exportType === 'declPML') {
        initDeclPMLChosenGrid(year, farming);
    } else if (exportType === 'anketna-karta') {
        initAnketnaKartaChosenGrid(ekate, year, farming);
    }

    jQuery('#win-declarations').window('resize');
}

function initAnketnaKartaFilterFields() {
    jQuery('#ak-contract-type > input').combobox({
        editable: false,
        data: jQuery('#search-contract-type').combobox('getData'),
        valueField: 'id',
        textField: 'name'
    });

    jQuery('#ak-from-date > input').datebox({
        required: false,
        missingMessage: 'Моля въведете дата.',
        buttons: dateboxWithClearButton
    });
    jQuery('#ak-from-date > input').datebox('setValue');

    jQuery('#ak-to-date > input').datebox({
        required: false,
        missingMessage: 'Моля въведете дата.',
        buttons: dateboxWithClearButton
    });
    jQuery('#ak-to-date > input').datebox('setValue');

    jQuery('#ak-ntp > input').combobox({
        data: ComboboxData.PlotNTPCombobox,
        valueField: 'ntp',
        valueField: 'id',
        textField: 'name',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function anketnaKartaFilter() {
    jQuery('#declaration-choose-tables').datagrid({
        rpcParams: [{
            ekate: declEkate,
            year: declYear,
            farming: declFarming,
            masiv: jQuery('#ak-masiv > input').val(),
            imot: jQuery('#ak-imot > input').val(),
            c_type: jQuery('#ak-contract-type > input').combobox('getValue'),
            ntp: jQuery('#ak-ntp > input').combobox('getValues'),
            from_date: jQuery('#ak-from-date > input').datebox('getValue'),
            to_date: jQuery('#ak-to-date > input').datebox('getValue')
        }]
    });
    jQuery('#win-anketna-karta-filter').window('close');
}

function clearAnketnaKartaFilter() {
    jQuery('#ak-masiv > input').val('');
    jQuery('#ak-imot > input').val('');
    jQuery('#ak-contract-type > input').combobox('reset');
    jQuery('#ak-ntp > input').combobox('reset');
    jQuery('#ak-from-date > input').datebox('setValue');
    jQuery('#ak-to-date > input').datebox('setValue');

    jQuery('#declaration-choose-tables').datagrid({
        rpcParams: [{
            ekate: declEkate,
            year: declYear,
            farming: declFarming,
            ntp: null,
            from_date: null,
            to_date: null
        }]
    });
}

function initAnketnaKartaResultsFilterFields() {

    jQuery('#ak-results-from-date > input').datebox({
        required: false,
        missingMessage: 'Моля въведете дата.',
        buttons: dateboxWithClearButton
    });
    jQuery('#ak-results-from-date > input').datebox('setValue');

    jQuery('#ak-results-to-date > input').datebox({
        required: false,
        missingMessage: 'Моля въведете дата.',
        buttons: dateboxWithClearButton
    });
    jQuery('#ak-results-to-date > input').datebox('setValue');

    jQuery('#ak-results-ntp > input').combobox({
        data: ComboboxData.PlotNTPCombobox,
        valueField: 'id',
        textField: 'name',
        value: '',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function anketnaKartaResultsFilter() {
    jQuery('#declaration-chosen-tables').datagrid({
        rpcParams: [{
            ekate: declEkate,
            year: declYear,
            farming: declFarming,
            masiv: jQuery('#ak-results-masiv > input').val(),
            imot: jQuery('#ak-results-imot > input').val(),
            ntp: jQuery('#ak-results-ntp > input').combobox('getValues'),
            from_date: jQuery('#ak-results-from-date > input').datebox('getValue'),
            to_date: jQuery('#ak-results-to-date > input').datebox('getValue')
        }]
    });
    jQuery('#win-anketna-karta-results-filter').window('close');
}

function clearAnketnaKartaResultsFilter() {
    jQuery('#ak-results-masiv > input').val('');
    jQuery('#ak-results-imot > input').val('');
    jQuery('#ak-results-ntp > input').combobox('reset');
    jQuery('#ak-results-from-date > input').datebox('setValue');
    jQuery('#ak-results-to-date > input').datebox('setValue');

    jQuery('#declaration-chosen-tables').datagrid({
        rpcParams: [{
            ekate: declEkate,
            year: declYear,
            farming: declFarming,
            masiv: null,
            imot: null,
            ntp: null,
            from_date: null,
            to_date: null
        }]
    });
}

function exportDeclaration() {
    var winDownload = jQuery('#win-download').window({
        onClose: onDownloadWindowClose
    });

    var downloadFile = jQuery('#btn-download-file');
    var cancelDownloadFile = jQuery('#btn-download-file-close');

    var names = jQuery('#export-decl-info-names > input').val(),
        egn = jQuery('#export-decl-info-egn > input').val(),
        company = jQuery('#export-decl-info-company > input').val(),
        address = jQuery('#export-decl-info-address > textarea').val(),
        address_ekate = jQuery('#export-decl-info-ekate > input').combobox('getValue'),
        phone = jQuery('#export-decl-info-phone > input').val(),
        bulstat = jQuery('#export-decl-info-bulstat > input').val();

    let splitLegalGrounds = jQuery('#export-decl-split-legal-grounds > input').is(':checked');

    var year = jQuery('#choose-decl-year > input').combobox('getValue');
    var ekate = jQuery('#choose-decl-ekate > input').combobox('getValue');
    var farming = jQuery('#choose-decl-farming > input').combobox('getValue');

    var ntp = jQuery('#ak-results-ntp > input').combobox('getValues');
    var from_date = jQuery('#ak-results-from-date > input').datebox('getValue');
    var to_date = jQuery('#ak-results-to-date > input').datebox('getValue');

    var gridOptions = jQuery('#declaration-chosen-tables').datagrid('options');

    var zerroLpad = jQuery('#zerro-lpad-combobox').combobox('getValue');
    var plotNumberFormat = jQuery('#plot-number-format-combobox').combobox('getValue');

    var exportOptions = {
        names: names,
        egn: egn,
        address_ekate: address_ekate,
        company: company,
        address: address,
        phone: phone,
        year: year,
        ekate: ekate,
        farming: farming,
        bulstat: bulstat,
        has_zerro_lpad: zerroLpad,
		plot_number_format: plotNumberFormat,
        split_legal_grounds: splitLegalGrounds,
        is_export_type_xls: false,
        ntp: ntp,
        from_date: from_date,
        to_date: to_date
    };

    if (!egn && exportType !== 'declPML') {
        jQuery.messager.alert('Грешка', 'Полето ЕГН е задължително', 'warning');
        return false;
    }

    var sort = gridOptions.sortName;
    var order = gridOptions.sortOrder;
    if (exportType == 'decl70') {
        jQuery('#win-decl-export-info').window('open');
        if (document_type == 'csv') {
            exportOptions.is_export_type_xls = false;
        } else if (document_type == 'xls') {
            exportOptions.is_export_type_xls = true;
        }
        TF.Rpc.Plots.PlotsDeclarations.createCSVDecl70(exportOptions, null, null, sort, order)
            .done(function (data) {
                winDownload.window('open');
                _pathFile = data.file_path;
                _fileName = data.file_name;
                downloadFile.attr("href", _pathFile);
            })
            .fail();
    } else if (exportType == 'decl69') {
        jQuery('#win-decl-export-info').window('open');
        if (document_type == 'csv') {
            exportOptions.is_export_type_xls = false;
        } else if (document_type == 'xls') {
            exportOptions.is_export_type_xls = true;
        }
        TF.Rpc.Plots.PlotsDeclarations.createCSVDecl69(exportOptions, null, null, sort, order)
            .done(function (data) {
                winDownload.window('open');
                _pathFile = data.file_path;
                _fileName = data.file_name;
                downloadFile.attr("href", _pathFile);
            })
            .fail(function () {
                jQuery.messager.alert('Грешка', 'Възникна грешка при обработка на данните', 'warning');
                return;
            });
    } else if (exportType == 'declPML') {
        jQuery('#win-decl-export-info').window('open');
        if (document_type == 'csv') {
            exportOptions.is_export_type_xls = false;
        } else if (document_type == 'xls') {
            exportOptions.is_export_type_xls = true;
        }
        TF.Rpc.Plots.PlotsDeclarations.createCSVDecPML(exportOptions, null, null, sort, order)
            .done(function (data) {
                winDownload.window('open');
                _pathFile = data.file_path;
                _fileName = data.file_name;
                downloadFile.attr("href", _pathFile);
            })
            .fail();
    } else {
        return false;
    }

    jQuery('#win-decl-export-info').window('close');
}

function exportDeclaration73() {
    var winDownload = jQuery('#win-download').window({
        onClose: onDownloadWindowClose
    });

    var downloadFile = jQuery('#btn-download-file');
    var farming = jQuery('#choose-decl-farming > input').combobox('getValue');
    var withDividendDecl73 = jQuery('#with-dividend-decl-73 > input').prop('checked');
    var yearDecl73 = jQuery('#choose-decl-73-year > input').combobox('getValue');

    var exportOptions = {
        year: yearDecl73,
        farming: farming,
        with_dividends: withDividendDecl73,
    };

    jQuery('#win-decl-export-info').window('open');
    if (document_type == 'xml') {
        TF.Rpc.Plots.PlotsDeclarations.createXmlDecl73(exportOptions, null, null, 'asc', 'data')
            .done(function (data) {
                winDownload.window('open');
                _pathFile = data.file_path;
                _fileName = data.file_name;
                downloadFile.attr("href", _pathFile);
                downloadFile.attr("download", "");
                downloadFile.attr("target", "_blank");
            })
            .fail(function (errorObj){
                return jQuery.messager.alert('Грешка', errorObj.getOriginalMessage(), 'warning');
            });
    } else if (document_type == 'xls') {
        TF.Rpc.Plots.PlotsDeclarations.createXlsDecl73(exportOptions, null, null, 'asc', 'data')
            .done(function (data) {
                winDownload.window('open');
                _pathFile = data.file_path;
                _fileName = data.file_name;
                downloadFile.attr("href", _pathFile);
                downloadFile.attr("download", "");
                downloadFile.attr("target", "_blank");
            })
            .fail();
    }


    jQuery('#win-decl-export-info').window('close');
}

function checkKMSAgreements(ekate, year, farming) {
    var obj = {};
    obj.ekate = ekate;
    obj.year = year;
    obj.farming = farming;

    TF.Rpc.Plots.PlotsDeclarations.checkKMSAgreements(obj)
        .done(function (data) {
            checkKMSAgreementsResult(data);
        })
        .fail(function (data) {
            jQuery.messager.alert('Грешка', 'Възникна грешка при обработка на данните', 'warning');
        });
}

function checkKMSAgreementsResult(response) {
    if (response.data['has_agreements']) {
        jQuery.messager.alert('Внимание', 'За ' + response.year + 'в модул карта са заредени данни в слой "Данни от комасация", но в модул имоти не фигурира споразумение за ползване на земеделски земи, създадено по реда на чл. 37в от ЗСПЗЗ', 'warning');
    }

    initAnketnaKartaChosenGrid(response.data['ekate'], response.data['year'], response.data['farming']);
}

function onDownloadWindowClose() {
    return;
}

function printResultsFromRequests(data) {

    if (data.error){
        jQuery.messager.alert('Грешка', data.error, 'warning');
        return;
    }

    winDownload.window('open');
    _pathFile = data.file_path;
    _fileName = data.file_name;
    downloadFile.attr("href", _pathFile);
    downloadFile.attr("download", "");
    downloadFile.attr("target", "_blank");
}

function fillExcelDeclarationFields(){
	let selectedFarm = getSelectedFarm();
	if(!selectedFarm) return;

	jQuery('#ExportDeclInfoNames').val(selectedFarm.mol);
	jQuery('#export-decl-info-egn > input').textbox('setValue', selectedFarm.mol_egn);
	
    jQuery('#export-decl-info-ekate > input').combobox({
        url: 'index.php?common-rpc=ekate-combobox',
        rpcMethod: 'readAll',
        rpcParams: [
            {
                'farm_uuid': selectedFarm.uuid,
            }
        ],
        valueField: 'ekatte',
        textField: 'text',
        filter: function (q, row) {
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
            {
                return true;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
	
    
    jQuery('#export-decl-info-address>textarea').val(selectedFarm.address);
	jQuery('#ExportDeclInfoPhone').val(selectedFarm.farming_mol_phone);


	jQuery('#ExportDeclInfoCompany').val(selectedFarm.name);
	jQuery('#ExportDeclInfoBulstat').val(selectedFarm.bulstat);
	
}

function getSelectedFarm(){
    let declFarming = jQuery('#choose-decl-farming > input').combobox('getValue');
    let farms = jQuery('#choose-decl-farming > input').combobox('getData');
	var selectedFarm = null;
	jQuery.each(farms, function(index, farm) {
		if(farm.id == declFarming){
			selectedFarm = farm;
			return false;
		}
	});

	return selectedFarm;
}

function getSelectedEkatteName(){
    let ekatteText = jQuery('#choose-decl-ekate > input').combobox('getText');
	return ekatteText.match(/\((.*?)\)/)[1];
}