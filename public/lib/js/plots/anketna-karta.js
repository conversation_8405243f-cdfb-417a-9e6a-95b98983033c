var KVS_NUMBER_FORMAT = 6;
var KK_NUMBER_FORMAT = 8;
function initAnketnaKartaChosenGrid(ekate, year, farming) {

	declYear = year;
	declEkate = ekate;
	declFarming = farming;
	var ALLOWERD_AREA_TYPES = ["1100", "1113", "1700", "1710", "2500"];
	var ADDITIONAL_CONTRACT_TYPES = [1, 2, 3, 5];

	jQuery('#declaration-chosen-tables').datagrid({
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		iconCls: 'icon-edit-geometry',
		title: chosenHeading,
		fit: true,
		fitColumns: true,
		showFooter: true,
		border: false,
		url: 'index.php?plots-rpc=declaration-chosen-grid',
		rpcParams: [{
			ekate: ekate,
			year: year,
			farming: farming,
			action: 'open'
		}],
		rpcMethod: 'anketnaKarta',
		sortName: 'c.id',
		sortOrder: 'asc',
		idField: 'pc_rel_id',
		frozenColumns: [
			[
				{
					field: 'ck',
					checkbox: true
				}
			]
		],
		rowStyler: function(index, row) {
			if(!row || Object.keys(row).length === 0 || row.kad_ident === "<b>ОБЩО</b>") {
				return;
			}
			if (!row.area_type) {
				return "background-color:#ff0000;";
			}
			if (row.is_subleased == true) {
				return 'background-color:#FF9900;';
			}

			var isAllowedAreaType = ALLOWERD_AREA_TYPES.indexOf(row.area_type_code) >= 0;
			var isAdditionalContractType = ADDITIONAL_CONTRACT_TYPES.indexOf(row.nm_usage_rights) >= 0;
			if (!isAllowedAreaType && !isAdditionalContractType) {
				return "background-color:#ff0000;";
			}
		},
		columns: [
			[
				{
					field: 'kad_ident',
					title: '<b>Идентификатор</b>',
					sortable: true,
					width: 130,
					formatter: function (value, row) {
						if (!row) {
							return;
						}
						var isAllowedAreaType = ALLOWERD_AREA_TYPES.indexOf(row.area_type_code) !== -1;
						var isAdditionalContractType = ADDITIONAL_CONTRACT_TYPES.indexOf(row.nm_usage_rights) >= 0;
						if ((!row.area_type || (!isAllowedAreaType && !isAdditionalContractType)) && row.kad_ident != '<b>ОБЩО</b>') {
							return '<a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="showPlotInNewTab('+row.gid+')">'+row.kad_ident+'</a>';
						}
						return value;
					}
				}, {
					field: 'area',
					title: '<b>Площ<br/>(ха)</b>',
					sortable: true,
					width: 50
				}, {
					field: 'irrigated_area',
					title: '<b>Поливна<br/>площ (ха)</b>',
					sortable: false,
					width: 50
				}, {
					field: 'area_type',
					title: '<b>НТП</b>',
					sortable: true,
					width: 150
				}, {
					field: 'c_num',
					title: '<b>Договор</b>',
					sortable: true,
					width: 70
				}, {
					field: 'c_type',
					title: '<b>Тип<br/>договор</b>',
					sortable: true,
					width: 90
				}, {
					field: 'subleases',
					title: '<b>Преотдаден<br/>чрез</b>',
					sortable: true,
					width: 100
				}
			]
		],
		rownumbers: true,
		toolbar: '#anketna-karta-chosen-toolbar',
		onBeforeLoad: function() {
		},
		onLoadSuccess: function(data){
			initAnketnaKartaChooseGrid(ekate, year, farming);
			if (data.hasPlotsWithoutNTP) {
				jQuery.messager.alert('Внимание', 'В анкетната карта са включени имоти, за които няма зададен НТП.', 'warning');
			}
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}
function initAnketnaKartaChooseGrid(ekate, year, farming) {
	jQuery('#declaration-choose-tables').datagrid({
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		iconCls: 'icon-edit-geometry',
		title: chooseHeading,
		pageSize: 10,
		fit: true,
		fitColumns: true,
		showFooter: true,
		url: 'index.php?plots-rpc=declaration-choose-grid',
		rpcParams: [{
			ekate: ekate,
			year: year,
			farming: farming,
			action: 'open'
		}],
		rpcMethod: 'anketnaKarta',
		sortName: 'c.id',
		sortOrder: 'asc',
		idField: 'pc_rel_id',
		frozenColumns: [
			[
				{
					field: 'ck',
					checkbox: true
				}
			]
		],
		columns: [
			[
				{
					field: 'kad_ident',
					title: '<b>Идентификатор</b>',
					sortable: true,
					width: 130
				}, {
					field: 'area',
					title: '<b>Площ<br/>(ха)</b>',
					sortable: true,
					width: 50
				},  {
					field: 'irrigated_area',
					title: '<b>Поливна<br/>площ (ха)</b>',
					sortable: false,
					width: 50
				}, {
					field: 'area_type',
					title: '<b>НТП</b>',
					sortable: true,
					width: 150
				}, {
					field: 'c_num',
					title: '<b>Договор</b>',
					sortable: true,
					width: 70
				}, {
					field: 'c_type',
					title: '<b>Тип<br/>договор</b>',
					sortable: true,
					width: 90
				}, {
					field: 'subleases',
					title: '<b>Преотдаден<br/>чрез</b>',
					sortable: true,
					width: 100
				}
			]
		],
		pagination: true,
		rownumbers: true,
		toolbar: [{
				id: 'btnaddtoanketnakarta',
				text: 'Добавяне',
				iconCls: 'icon-add',
				handler: function() {
					var getChecked = jQuery('#declaration-choose-tables').datagrid('getChecked');
					if (getChecked[0]) {
						var idArray = [];

						for (var i = 0; i < getChecked.length; i++) {
							idArray[i] = getChecked[i]['pc_rel_id'];
						}
							TF.Rpc.Plots.DeclarationChosenGrid.addToDeclaration(idArray)
							.done(function () {
								jQuery('#declaration-choose-tables').datagrid('options').rpcParams[0].action = 'addPlot';
								jQuery('#declaration-chosen-tables').datagrid('options').rpcParams[0].action = 'addPlot';

								jQuery('#declaration-choose-tables').datagrid('loadRpc');
								jQuery('#declaration-chosen-tables').datagrid('loadRpc');
							})
							.fail(function () {
								jQuery.messager.alert('Грешка', 'Възникна грешка при обработката на данните.', 'warning');
							});
					} else {
						jQuery.messager.alert('Грешка', 'Моля изберете имоти, които да бъдат добавени към анкетната карта.');
					}
				}
			}, {
				id: 'btnfilteranketnakartaadd',
				text: 'Филтър',
				iconCls: 'icon-filter',
				handler: function() {
					jQuery('#win-anketna-karta-filter').window('open');
				}
			}, {
				id: 'btnfilteranketnakartaclear',
				text: 'Покажи всички',
				iconCls: 'icon-clear-filter',
				handler: function() {
					clearAnketnaKartaFilter();
				}
			}
		],
		rowStyler: function(index, row) {
			if (row.is_subleased == true) {
				return 'background-color:#FF9900;';
			}
		},
		onBeforeLoad: function() {
			jQuery('#declaration-choose-tables').datagrid('clearChecked');
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function removePlotFromAnketnaKarta() {
	var getChecked = jQuery('#declaration-chosen-tables').datagrid('getChecked');
	if (getChecked[0]) {
		jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да премахнете тези имоти от анкетната карта?', function(r) {
			if (r) {
				var idArray = [];

				for (var i = 0; i < getChecked.length; i++) {
					idArray[i] = getChecked[i]['pc_rel_id'];
				}

				TF.Rpc.Plots.DeclarationChosenGrid.deleteFromDeclaration(idArray)
				.done(function () {
					jQuery('#declaration-choose-tables').datagrid('options').rpcParams[0].action = 'removePlot';
					jQuery('#declaration-chosen-tables').datagrid('options').rpcParams[0].action = 'removePlot';

					jQuery('#declaration-choose-tables').datagrid('loadRpc');
					jQuery('#declaration-chosen-tables').datagrid('loadRpc');
				})
				.fail(function () {
					jQuery.messager.alert('Грешка', 'Възникна грешка при обработката на данните.', 'warning');
				});
			}
		});
	} else {
		jQuery.messager.alert('Грешка', 'Моля изберете имоти, които да бъдат добавени към анкетната карта.');
	}
}
/**
 *
 * @param {KVS_NUMBER_FORMAT|KK_NUMBER_FORMAT|number} exportType
 */
function anketnaKartaExportXLS(exportType) {

	var winDownload = jQuery('#win-download');
	var downloadFile = jQuery('#btn-download-file');

	var year = jQuery('#choose-decl-year > input').combobox('getValue');
	var ekate = jQuery('#choose-decl-ekate > input').combobox('getValue');
	var farming = jQuery('#choose-decl-farming > input').combobox('getValue');
    var ntp = jQuery('#ak-results-ntp > input').combobox('getValues');
	var from_date = jQuery('#ak-results-from-date > input').datebox('getValue');
	var to_date = jQuery('#ak-results-to-date > input').datebox('getValue');

    var gridOptions = jQuery('#declaration-chosen-tables').datagrid('options');

    var exportObject = {
    	year: year,
    	farming: farming,
    	ekate: ekate,
    	ntp: ntp,
    	from_date: from_date,
		to_date: to_date,
		export_type: exportType
    };

	var sort = gridOptions.sortName;
	var order =  gridOptions.sortOrder;

    TF.Rpc.Plots.PlotsDeclarations.createAnketnaKarta(exportObject, sort, order)
    .done(function (data) {
		winDownload.window('open');
    	_pathFile = data.file_path;
    	_fileName = data.file_name;
    	downloadFile.attr("href", _pathFile);
    })
    .fail(function () {
    	jQuery.messager.alert('Грешка', 'Възникна грешка при обработката на данните.', 'warning');
    });
}
