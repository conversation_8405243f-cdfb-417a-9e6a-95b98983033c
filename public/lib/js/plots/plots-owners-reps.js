var repEditIndex = undefined;

function initOwnersRepsGrid() {

    var contractsOwnersRepsDatagrid = jQuery('#contract-owners-reps-tables');
    var isDatagridBound = contractsOwnersRepsDatagrid.data().hasOwnProperty('datagrid');
	
    if (isDatagridBound) {
		contractsOwnersRepsDatagrid.datagrid('reload');
		return;
	}

	contractsOwnersRepsDatagrid.datagrid({
		title: 'Представители',
		iconCls: 'icon-users',
		rownumbers: true,
		singleSelect: true,
		onSelectCheck: true,
		onCheckSelect: true,
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		fit: true,
		fitColumns: true,
		showFooter: true,
		pagination: true,
		height: 250,
		rpcParams:[{}],
		idField: 'id',
		sortName: 'id',
		sortOrder: 'desc',
		url: 'index.php?contracts-rpc=owners-reps-grid',
		frozenColumns: [[
				{
					field: 'ck',
					checkbox: true
				}
			]],
		columns: [[
				{
					field: 'rep_name',
					title: '<b>Име</b>',
					sortable: true,
					width: 200,
					editor: {
						type: 'validatebox',
						options: {
							required: true,
							missingMessage: 'Въведете име'
						}
					}
				}, {
					field: 'rep_surname',
					title: '<b>Презиме</b>',
					sortable: true,
					width: 200,
					editor: {
						type: 'validatebox',
						options: {
							required: true,
							missingMessage: 'Въведете презиме'
						}
					}
				}, {
					field: 'rep_lastname',
					title: '<b>Фамилия</b>',
					sortable: true,
					width: 200,
					editor: {
						type: 'validatebox',
						options: {
							required: true,
							missingMessage: 'Въведете фамилия'
						}
					}
				}, {
					field: 'rep_egn',
					title: '<b>ЕГН</b>',
					sortable: true,
					width: 200,
					editor: {
						type: 'egnValidateBox',
						options: {
							required: true,
							missingMessage: 'Въведете ЕГН'
						}
					}
				}, {
					field: 'rep_lk',
					title: '<b>Лична карта</b>',
					sortable: true,
					width: 200,
					editor: {
						type: 'validatebox',
						options: {
							required: false
						}
					}
				}, {
					field: 'rep_lk_izdavane',
					title: '<b>ЛК издадена на/от</b>',
					sortable: true,
					width: 200,
					editor: {
						type: 'validatebox',
						options: {
							required: false
						}
					}
				}, {
					field: 'rep_address',
					title: '<b>Адрес</b>',
					sortable: true,
					width: 300,
					editor: {
						type: 'validatebox',
						options: {
							required: false
						}
					}
				}, {
					field: 'iban',
					title: '<b>IBAN</b>',
					sortable: false,
					width: 300,
					editor: {
						type: 'validatebox',
						options: {
							required: false
						}
					}
				},
			]],
		toolbar: [{
				id: 'btnadd',
				text: 'Добавяне',
				iconCls: 'icon-add',
				handler: function() {
					appendRepRow();
				}
			}, {
				id: 'btnedit',
				text: 'Редактиране',
				iconCls: 'icon-edit',
				handler: function() {
					var getChecked = jQuery('#contract-owners-reps-tables').datagrid('getChecked');

					if (getChecked[0]) {
						var index = jQuery('#contract-owners-reps-tables').datagrid('getRowIndex', getChecked[0]);

						if (repEditIndex != index) {
							if (repEditingClosed()) {
								jQuery('#contract-owners-reps-tables').datagrid('beginEdit', index);
								repEditIndex = index;
							} else {
								jQuery('#contract-owners-reps-tables').datagrid('selectRow', repEditIndex);
							}
						}
					} else
					{
						jQuery.messager.alert('Грешка', 'Не е избран представител.');
					}
				}
			}, {
				id: 'btndel',
				text: 'Изтриване',
				iconCls: 'icon-remove',
				handler: function() {
					var getChecked = jQuery('#contract-owners-reps-tables').datagrid('getChecked');

					if (getChecked[0]) {
						jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да изтриете този представител?', function(r) {
							if (r) {
								representativeId = jQuery('#contract-owners-reps-tables').datagrid('getSelected').id;
								TF.Rpc.Contracts.ConctractOwnerData.deleteRep(representativeId);
								jQuery('#contract-owners-reps-tables').datagrid('reload');
							}
						});
						repEditIndex = undefined;
					} else
					{
						jQuery.messager.alert('Грешка', 'Моля изберете кой представител искате да премахнете.');
					}
				}
			}, {
				id: 'btnsave',
				text: 'Запази',
				iconCls: 'icon-save',
				handler: function() {
					repEditingClosed();
				}
			}, {
				id: 'btncancel',
				text: 'Отмени',
				iconCls: 'icon-undo',
				handler: function() {
					rejectRepChanges();
				}
			}, {
				id: 'btnaddrepfilter',
				text: 'Филтриране',
				iconCls: 'icon-filter',
				handler: function() {
					jQuery('#win-owner-reps-filter').window('open');
				}
			}, {
				id: 'btnremoverepfilter',
				text: 'Покажи всички',
				iconCls: 'icon-clear-filter',
				handler: function() {
					cleanOwnersRepsFilter();
				}
			}],
		onCheck: function(index, rowData) {
			if (repEditIndex != index && !repEditingClosed()) {
				jQuery('#contract-owners-reps-tables').datagrid('selectRow', repEditIndex);
			}
		},
		onBeforeLoad: function() {
			rejectRepChanges();
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function repEditingClosed() {
	if (repEditIndex == undefined) {
		return true;
	}
	if (jQuery('#contract-owners-reps-tables').datagrid('validateRow', repEditIndex))
	{
		jQuery('#contract-owners-reps-tables').datagrid('endEdit', repEditIndex);
		jQuery('#contract-owners-reps-tables').datagrid('acceptChanges');

		var rows = jQuery('#contract-owners-reps-tables').datagrid('getRows');
        var obj = rows[repEditIndex];

		TF.Rpc.Contracts.ConctractOwnerData.saveRep(obj);

		repEditIndex = undefined;

		jQuery('#contract-owners-reps-tables').datagrid('reload');

		return true;
	} else {
		return false;
	}
}

function appendRepRow() {
	if (repEditingClosed()) {
		repEditIndex = 0;
		jQuery('#contract-owners-reps-tables').datagrid('insertRow',
			{
				index: repEditIndex,
				row: {
					id: false
				}
			});
		jQuery('#contract-owners-reps-tables').datagrid('beginEdit', repEditIndex).datagrid('selectRow', repEditIndex);
	}
}

function rejectRepChanges() {
	jQuery('#contract-owners-reps-tables').datagrid('rejectChanges');
	jQuery('#contract-owners-reps-tables').datagrid('uncheckAll');
	jQuery('#contract-owners-reps-tables').datagrid('unselectAll');

	repEditIndex = undefined;
}

//filter for owners reps grid
function filterOwnersReps() {
	if (jQuery('#search-rep-names > input').val() != ''
			|| jQuery('#search-rep-egn > input').val() != '') {
		jQuery('#contract-owners-reps-tables').datagrid({
			rpcParams: [{
							rep_names: jQuery('#search-rep-names > input').val(),
							rep_egn: jQuery('#search-rep-egn > input').val()
						}]
		});
		jQuery('#win-owner-reps-filter').window('close');
	} else {
		jQuery.messager.alert('Грешка', 'Моля задайте данни за търсене.');
	}
}

function cleanOwnersRepsFilter() {
	jQuery('#search-rep-names > input').val('');
	jQuery('#search-rep-egn > input').val('');

	jQuery('#contract-owners-reps-tables').datagrid({
		rpcParams: [{}]
	});
}
//end of filter for owners reps grid
