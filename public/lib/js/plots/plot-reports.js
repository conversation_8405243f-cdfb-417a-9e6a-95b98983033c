Namespace('TF.Rpc.Plots');
var report_type;
var report_title;
var report_filter;
var WEEK_MS = 604800000;
var date = new Date();
var todayDate = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();
var toDate = new Date(date.getTime() + WEEK_MS);
var toDateStr = toDate.getFullYear() + '-' + (toDate.getMonth() + 1) + '-' + toDate.getDate();

//function handles all reports and resends
function initReportResend(chosen_report) {
    jQuery('#map-report').show();
    report_filter = chosen_report;
    jQuery('#window-choose-report-type').window('open');

    jQuery('#choose-report-date').datebox();
    jQuery('#choose-report-ekate').combobox();
    jQuery('#choose-report-farming').combobox();
    jQuery('#choose-report-ntp').combobox();
    jQuery('#choose-report-category').combobox();
    jQuery('#choose-report-mestnost').combobox();
    jQuery('#choose-irrigation-area').combobox();

    jQuery('#choose-participation-holder').hide();
    jQuery('#win-filter-report').window('resize', {width: 563});
    jQuery('#choose-report-date-from-cont').hide();

    jQuery('#include-subleased-in-report-menu').hide();
    jQuery('#include-subleased-in-report').prop('checked', true);

    //get search filters values
    var obj = getValuesFilterPlots();
    //getting filters ready
    initReportFilters();
    if  (chosen_report == 'report-rented-expiring-contracts') {
        jQuery('#filter-title-placeholder').html('Договори, изтичащи в период');
        jQuery('#choose-report-date-as-of').datebox('disable');
        jQuery('#choose-renewed-holder').show();
    } else {
        jQuery('#filter-title-placeholder').html('Договори, действащи в период');
        jQuery('#choose-report-date-as-of').datebox('enable');
        jQuery('#choose-renewed-holder').hide();
    }

    if (chosen_report == 'report-total-area') {
        var winDownload = jQuery('#win-download').window(),
            reportWindow = jQuery('#win-report-grid-panel');

        reportWindow.window('open');
        reportWindow.window('center');
        jQuery('#win-plots-filter').window('resize', {width: 1160});
        jQuery('#grouping-options-fields').show();
        initTotalAreaReport(true);
    }
    if (chosen_report == 'report-own-plots-detailed' || chosen_report == 'report-own-plots') {
        disableDateBoxes();
        jQuery('#choose-report-date-as-of').datebox('setValue', todayDate);
        if  (chosen_report == 'report-own-plots-detailed') {
            jQuery('#container-choose-inactive-contracts').show();
        }
    } else {
        jQuery('#choose-report-date').datebox('setValue', todayDate);
        jQuery('#choose-report-date-to').datebox('setValue', toDateStr);
        jQuery('#choose-report-date-as-of').datebox('reset');
        jQuery('#container-choose-inactive-contracts').hide();
    }

    //assigns the required report
    if(chosen_report == 'report-subleased-rented-plots')
    {
        jQuery('#choose-arendator').parent().parent().show();
        jQuery(jQuery('#choose-arendator').parent().siblings()[0]).html('Арендатор');
        jQuery('#choose-sublease-type').parent().parent().show();
    }else{
        jQuery('#choose-arendator').parent().parent().hide();
        jQuery('#choose-sublease-type').parent().parent().hide();
    }
    if (chosen_report == 'report-own-plots') {

        initOwnPlotsReportGrid();

        report_type = 'own_plots';
        report_title = 'Справка собствена земя';
        jQuery('#win-plots-report-grid').window('setTitle', report_title);
        removeFooterMessage();
        addDateFilterFooterMessage(report_type);
        jQuery('#win-plots-report-grid').window('open');
    }
    if (chosen_report == 'report-subleased-plots') {

        initSubleasedPlotsReportGrid();

        report_type = 'subleased';
        report_title = 'Справка отдадена собствена земя';
        jQuery('#win-plots-report-grid').window('setTitle', report_title);
        removeFooterMessage();
        addDateFilterFooterMessage(report_type);
        jQuery('#win-plots-report-grid').window('open');
    }
    if (chosen_report == 'report-for-sublease') {

        initForSubleasePlotsReportGrid();

        report_type = 'for_sublease';
        report_title = 'Справка свободна земя за отдаване под наем/аренда';
        jQuery('#win-plots-report-grid').window('setTitle', report_title);
        removeFooterMessage();
        addDateFilterFooterMessage(report_type);
        jQuery('#win-plots-report-grid').window('open');
    }
    if (chosen_report == 'report-hypothecs') {

        initHypothecsReportGrid(toDateStr ,todayDate);

        report_type = 'hypothecs';
        report_title = 'Справка ипотеки и тежести';
        jQuery('#win-plots-report-grid').window('setTitle', report_title);
        removeFooterMessage();
        addDateFilterFooterMessage(report_type);
        jQuery('#win-plots-report-grid').window('open');
    }
    if (chosen_report == 'report-for-hypothec') {

        initForHypothecReportGrid();

        report_type = 'for_hypothec';
        report_title = 'Справка свободна земя за ипотеки';
        jQuery('#win-plots-report-grid').window('setTitle', report_title);
        removeFooterMessage();
        addDateFilterFooterMessage(report_type);
        jQuery('#win-plots-report-grid').window('open');
    }
    if (chosen_report == 'report-rented-plots') {

        initRentedPlotsReportGrid();

        report_type = 'rented_plots';
        report_title = 'Справка наета/арендована земя';
        jQuery('#win-plots-report-grid').window('setTitle', report_title);
        removeFooterMessage();
        addSubleasedFooterMessage();
        addDateFilterFooterMessage(report_type);
        jQuery('#win-plots-report-grid').window('open');
    }
    if (chosen_report == 'report-subleased-rented-plots') {

        initSubleasedRentedPlotsReportGrid();

        report_type = 'subleased_rented_plots';
        report_title = 'Справка преотдадена/пренаета земя';
        jQuery('#win-plots-report-grid').window('setTitle', report_title);
        removeFooterMessage();
        addDateFilterFooterMessage(report_type);
        jQuery('#win-plots-report-grid').window('open');
    }
    if (chosen_report == 'report-rented-expiring-contracts') {

        initRentedExpiringContractsReportGrid();
        initSearchArendator();
        jQuery('#choose-arendator').parent().parent().show();
        jQuery(jQuery('#choose-arendator').parent().siblings()[0]).html('Арендодател/Наемодател');
        report_type = 'rented_expiring_contracts';
        report_title = 'Справка имоти с изтичащи договори';
        jQuery('#win-plots-report-grid').window('setTitle', report_title);
        removeFooterMessage();
        addDateFilterFooterMessage(report_type);
        jQuery('#win-plots-report-grid').window('open');
    }
    if (chosen_report == 'report-plots-in-many-contracts') {

        initPlotsInManyContractsReportGrid();

        report_type = 'plots_in_many_contracts';
        report_title = 'Справка имоти, участващи в повече от един договор';
        jQuery('#win-plots-report-grid').window('setTitle', report_title);
        removeFooterMessage();
        addDateFilterFooterMessage(report_type);
        jQuery('#win-plots-report-grid').window('open');
    }
    if (chosen_report == 'report-used-plots') {

        initUsedPlotsReportGrid();

        report_type = 'used_plots';
        jQuery('#choose-participation-holder').show();
        jQuery('#win-filter-report').window('resize', {width: 583});
        report_title = 'Справка използвана земя';
        jQuery('#win-plots-report-grid').window('setTitle', report_title);
        removeFooterMessage();
        addSubleasedFooterMessage();
        addDateFilterFooterMessage(report_type);
        jQuery('#win-plots-report-grid').window('open');
    }
    if (chosen_report == 'report-contracts-with-ownerless-plots') {

        initContractsWithOwnerlessPlotsReportGrid();

        report_type = 'contracts_with_ownerless_plots';
        report_title = 'Справка за договори с имоти без собственици';
        jQuery('#win-plots-report-grid').window('setTitle', report_title);
        removeFooterMessage();
        addDateFilterFooterMessage(report_type);
        jQuery('#win-plots-report-grid').window('open');
    }
    if (chosen_report == 'report-historical-plots') {

        initHistoricalPlotsReportGrid();

        report_type = 'historical_plots';
        report_title = 'Справка "Архивни имоти"';
        jQuery('#win-plots-report-grid').window('setTitle', report_title);
        removeFooterMessage();
        addDateFilterFooterMessage(report_type);
        jQuery('#win-plots-report-grid').window('open');
    }
    if (chosen_report == 'report-own-plots-detailed') {

        initDetailedOwnPlotsReport();

        jQuery('#map-report').hide();

        report_type = 'own_plots_detailed';
        report_title = 'Собствена земя - по имоти';
        jQuery('#win-plots-report-grid-detail').window('setTitle', report_title);
        removeFooterMessage();
        addDateFilterFooterMessage(report_type);
        jQuery('#win-plots-report-grid-detail').window('open');
    }
}

function initTotalAreaReport( withToolbar ) {
    withToolbar = (withToolbar == undefined) ? true : withToolbar;
    var winDownload = jQuery('#win-download').window();
    jQuery('#report-tables').datagrid({
        iconCls: 'icon-edit-geometry',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        singleSelect: true,
        pageSize: 10,
        fit: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?plots-rpc=plots-report-grid',
        idField: '',
        sortName: 'ekate',
        sortOrder: 'asc',
        border: false,
        rpcParams: [{
            kad_ident: jQuery('#search-kad-ident').val(),
            ekate: jQuery('#search-ekatte').combobox('getValues'),
            masiv: jQuery('#search-masiv').val(),
            number: jQuery('#search-number').val(),
            category: jQuery('#search-category').combobox('getValues'),
            area_type: jQuery('#search-area-type').combobox('getValues'),
            mestnost: jQuery('#search-mestnost').combobox('getValue'),
            irrigated_area: jQuery('#search-irrigated-area').combobox('getValue'),
            cnum: jQuery('#search-cnum').val(),
            contract_type: jQuery('#search-contract-type').combobox('getValues'),
            farming: jQuery('#search-farming').combobox('getValues'),
            contract_status: jQuery('#search-contract-status').combobox('getValue'),
            date_from: jQuery('#search-date-from').datebox('getValue'),
            date_to: jQuery('#search-date-to').datebox('getValue'),
            start_date: jQuery('#search-start-date').datebox('getValue'),
            due_date: jQuery('#search-due-date-to').datebox('getValue'),
            owner_name: jQuery('#search-owner-name').val(),
            owner_egn: jQuery('#search-owner-egn').val(),
            ime_subekt: jQuery('#search-owner-ime-subekt').val(),
            egn_subekt: jQuery('#search-owner-egn-subekt').val(),
            rep_name: jQuery('#search-represent-name').val(),
            rep_egn: jQuery('#search-represent-egn').val(),
            company_name: jQuery('#search-company-name').val(),
            company_eik: jQuery('#search-company-eik').val(),
            participation: jQuery('#search-participation').combobox('getValue'),
            group_by_ekatte: jQuery('#group-by-ekatte').is(':checked'),
            group_by_maisv: jQuery('#group-by-masiv').is(':checked'),
            group_by_farming: jQuery('#group-by-farming').is(':checked'),
            group_by_contract_type: jQuery('#group-by-contract-type').is(':checked'),
            group_by_with_contract: jQuery('#group-by-with-contract').is(':checked'),
            include_subleases: jQuery('#include-subleased-plots-to-report').is(':checked'),
            is_edited: is_edit,
            group_by: 'ekate'
        }],
        pagination: true,
        rownumbers: true,
        toolbar:(!withToolbar) ? null : [{
            id: 'btnprintreport',
            text: 'Отпечатай',
            iconCls: 'icon-print',
            handler: function() {
                var data = jQuery('#report-tables').datagrid('getData');
                if(data.rows.length === 0) {
                    jQuery.messager.alert('Грешка', 'Не може да отпечатате празна справка.');
                    return false;
                }

                var gridOptions = jQuery('#report-tables').datagrid('options');

                var params = jQuery.extend({}, gridOptions.rpcParams);
                var sort = gridOptions.sortName;
                var order = gridOptions.sortOrder;


                TF.Rpc.Plots.PlotReports.read(params[0], '', '',sort,order)
                    .done(function (data){
                        var gridData = data.rows;

                        var html = '<style>@page{size: landscape;}body,table{font-family: Arial;font-size: 10px;}</style>' +
                            '<h2 align="center">' + report_title + '</h2>';

                        var header = {};
                        var columns = gridOptions.columns[0];
                        for(var i=0; i<columns.length; i++)
                        {
                            header[columns[i].field] = columns[i].title;
                        }

                        var rows = gridData;

                        html += Templates.table(header, rows);

                        jQuery('body').append('<iframe id="printf" name="printf"></iframe>');
                        var newWin = window.frames['printf'];
                        newWin.document.write('<body onload=window.print()>'+html+'</body>');
                        newWin.document.close();
                        setTimeout(function () {
                            jQuery('#printf').remove();
                        }, 1000);

                    })
                    .fail(function (errorObj) {
                        jQuery.messager.alert('Грешка', errorObj.getMessage());
                    });

                return false;
            }
        }, {

            id: 'btn_export_excel',
            text: 'Експорт(xls)',
            iconCls: 'icon-csv',
            handler: function() {
                var data = jQuery('#report-tables').datagrid('getData');

                if (data.length === 0) {
                    jQuery.messager.alert('Грешка', 'Не може да експортнете празна справка.', 'error');
                }
                else {
                    var gridOptions = jQuery('#report-tables').datagrid('options');
                    var downloadFile = jQuery('#btn-download-file');
                    var params = gridOptions.rpcParams[0];

                    var sort = gridOptions.sortName;
                    var order = gridOptions.sortOrder;

                    TF.Rpc.Plots.PlotReports.exportToExcelPlotsReportData(params, report_filter, null, null, sort, order)
                        .done(function (dataObj){

                            winDownload.window('open');
                            var path = dataObj.file_path;
                            _fileName = dataObj.file_name;
                            downloadFile.attr("href", path);
                        })
                        .fail(function (errorObj) {});
                }
            }

        }, {
            id: 'btnreportmap',
            text: 'Карта',
            iconCls: 'icon-map',
            handler: function() {
                jQuery('#win-report-map').window('open');

                var obj = {};

                obj.kad_ident = jQuery('#search-kad-ident').val();
                obj.ekate = jQuery('#search-ekatte').combobox('getValues');
                obj.masiv = jQuery('#search-masiv').val();
                obj.number = jQuery('#search-number').val();
                obj.category = jQuery('#search-category').combobox('getValues');
                obj.area_type = jQuery('#search-area-type').combobox('getValues');
                obj.mestnost = jQuery('#search-mestnost').combobox('getValue');
                obj.irrigated_area = jQuery('#search-irrigated-area').combobox('getValue');
                obj.cnum = jQuery('#search-cnum').val();
                obj.contract_type = jQuery('#search-contract-type').combobox('getValues');
                obj.farming = jQuery('#search-farming').combobox('getValues');
                obj.contract_status = jQuery('#search-contract-status').combobox('getValue');
                obj.date_from = jQuery('#search-date-from').datebox('getValue');
                obj.date_to = jQuery('#search-date-to').datebox('getValue');
                obj.start_date = jQuery('#search-start-date').datebox('getValue');
                obj.due_date = jQuery('#search-due-date-to').datebox('getValue');
                obj.owner_name = jQuery('#search-owner-name').val();
                obj.owner_egn = jQuery('#search-owner-egn').val();
                obj.ime_subekt = jQuery('#search-owner-ime-subekt').val();
                obj.egn_subekt = jQuery('#search-owner-egn-subekt').val();
                obj.rep_name = jQuery('#search-represent-name').val();
                obj.rep_egn = jQuery('#search-represent-egn').val();
                obj.company_name = jQuery('#search-company-name').val();
                obj.company_eik = jQuery('#search-company-eik').val();
                obj.participation = jQuery('#search-participation').combobox('getValue');
                obj.group_by_ekatte = jQuery('#group-by-ekatte').is(':checked');
                obj.group_by_maisv = jQuery('#group-by-masiv').is(':checked');
                obj.group_by_farming = jQuery('#group-by-farming').is(':checked');
                obj.group_by_contract_type = jQuery('#group-by-contract-type').is(':checked');
                obj.group_by_with_contract = jQuery('#group-by-with-contract').is(':checked');
                obj.include_subleases = jQuery('#include-subleased-plots-to-report').is(':checked');
                obj.group_by = 'ekate';

                TF.Rpc.Plots.ReportShowMap.mapReports(obj).done(function (dataObj) {
                    initReportMap();
                    var extent = dataObj.extent || "125190.6162, 4573142.7188, 631370.3273, 4887149.5823";
                    reportMap.zoomToExtent(new OpenLayers.Bounds.fromString(extent).transform(
                        new OpenLayers.Projection("EPSG:32635"),
                        reportMap.getProjectionObject())
                    );
                })
                    .fail(function (errorObj) {
                    });
            }
        }, {
            id: 'btnreportfilter',
            text: 'Филтър',
            iconCls: 'icon-filter',
            handler: function() {
                jQuery('#win-plots-filter').window('open')
                    .window('center');
            }
        }, {
            id: 'btnreportclearfilter',
            text: 'Покажи всички',
            iconCls: 'icon-clear-filter',
            handler: function() {
                jQuery('#search-kad-ident').val('');
                jQuery('#search-ekatte').combobox('reset');
                jQuery('#search-masiv').val('');
                jQuery('#search-number').val('');
                jQuery('#search-category').combobox('reset');
                jQuery('#search-area-type').combobox('reset');
                jQuery('#search-mestnost').combobox('getValue');
                jQuery('#search-irrigated-area').combobox('getValue');
                jQuery('#search-cnum').val('');
                jQuery('#search-contract-type').combobox('reset');
                jQuery('#search-farming').combobox('reset');
                jQuery('#search-contract-status').combobox('reset');
                jQuery('#search-date-from').datebox('reset');
                jQuery('#search-date-to').datebox('reset');
                jQuery('#search-start-date').datebox('reset');
                jQuery('#search-due-date-to').datebox('reset');
                jQuery('#search-owner-name').val('');
                jQuery('#search-owner-egn').val('');
                jQuery('#search-owner-ime-subekt').val('');
                jQuery('#search-owner-egn-subekt').val('');
                jQuery('#search-represent-name').val('');
                jQuery('#search-represent-egn').val('');
                jQuery('#search-company-name').val('');
                jQuery('#search-company-eik').val('');
                jQuery('#search-participation').combobox('reset');
                jQuery('#group-by-ekatte').prop('checked', true);
                jQuery('#group-by-masiv').prop('checked', false);
                jQuery('#group-by-farming').prop('checked', false);
                jQuery('#group-by-contract-type').prop('checked', false);
                jQuery('#group-by-with-contract').prop('checked', true);
                jQuery('#include-subleased-plots-to-report').prop('checked', true);

                plotsFilter();
            }
        }],
        onBeforeLoad: function() {
            jQuery('#report-tables').datagrid('clearChecked');
        },
        onLoadSuccess: function() {
            TF.Loading.end();
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initReportFilters() {
    var ekateComboboxData         = ComboboxData.EkateCombobox,
        farmingComboboxData       = ComboboxData.FarmingCombobox,
        plotNTPComboboxData       = ComboboxData.PlotNTPCombobox,
        categoryComboboxData      = ComboboxData.PlotCategoryCombobox,
        irrigatedAreaComboboxData = ComboboxData.IrrigatedAreaCombobox,
        mestnostComboboxData      = ComboboxData.MestnostCombobox,
        subleaseTypeComboboxData  = ComboboxData.SubleaseTypeCombobox;

    ekateComboboxData[0].selected          = true;
    farmingComboboxData[0].selected        = true;
    plotNTPComboboxData[0].selected        = true;
    categoryComboboxData[0].selected       = true;
    irrigatedAreaComboboxData[0].selected  = true;
    subleaseTypeComboboxData[0].selected   = true;

    ekateComboboxData[1].selected          = false;
    farmingComboboxData[1].selected        = false;
    plotNTPComboboxData[1].selected        = false;
    categoryComboboxData[1].selected       = false;
    irrigatedAreaComboboxData[1].selected  = false;
    subleaseTypeComboboxData[1].selected   = false;

    jQuery('#choose-report-mestnost').combobox({
        data: mestnostComboboxData,
        valueField: 'mestnost',
        textField: 'text',
        value: '',
        filter: function(q, row){
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
            {
                return true;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });


    jQuery('#choose-report-date').datebox({
        requred: true,
        missingMessage: 'Моля въведете дата.',
        buttons: dateboxWithClearButton
    });
    jQuery('#choose-report-date').datebox('setValue', todayDate);
    jQuery('#choose-report-date-to').datebox({
        requred: true,
        missingMessage: 'Моля въведете дата.',
        buttons: dateboxWithClearButton
    });
    jQuery('#choose-report-date-to').datebox('setValue', toDateStr);
    jQuery('#choose-report-date-as-of').datebox({
        value: '',
        onChange: function(date) {
            if (date !== '') {
                jQuery('#choose-report-date').datebox('clear').datebox('disable');
                jQuery('#choose-report-date-to').datebox('clear').datebox('disable');
            }
        }
    });
    jQuery('#choose-arendator').val('');
    jQuery('#clear-choose-report-date-as-of').bind('click', function () {
        resetDateBoxes();
    });

    

    jQuery('#choose-report-contract-date').datebox({
        value: '',
        buttons: dateboxWithClearButton
    });

    jQuery('#choose-report-contract-date-to').datebox({
        value: '',
        buttons: dateboxWithClearButton
    });

    jQuery('#choose-report-ekate').combobox({
        data: ekateComboboxData,
        valueField: 'ekate',
        textField: 'text',
        value: '',
        filter: function(q, row){
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
            {
                return true;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#choose-report-farming').combobox({
        data: farmingComboboxData,
        valueField: 'id',
        textField: 'name',
        value: '',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#choose-report-ntp').combobox({
        data: plotNTPComboboxData,
        valueField: 'id',
        textField: 'name',
        value: '',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#choose-report-category').combobox({
        data: categoryComboboxData,
        valueField: 'id',
        textField: 'name',
        value: '',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#choose-irrigation-area').combobox({
        data: irrigatedAreaComboboxData,
        editable: false,
        valueField: 'value',
        textField: 'label',
        multiple: false,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#choose-sublease-type').combobox({
        data: subleaseTypeComboboxData,
        editable: false,
        valueField: 'type',
        textField: 'text',
        multiple: false,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#choose-participation').combobox({
        editable: false,
        data: [{
            "value": '',
            "text": "Всички",
            "selected": true
        }, {
            "value": 'without',
            "text": "Без зададено желание"
        }, {
            "value": 'participate',
            "text": "Да"
        }, {
            "value": 'no_participate',
            "text": "Не"
        }],
        valueField: 'value',
        textField: 'text',
        multiple: false,
    });

    jQuery('#choose-renewed').combobox({
        editable: false,
        data: [{
            "value": '',
            "text": "Всички",
            "selected": true
        }, {
            "value": 'true',
            "text": "Да"
        }, {
            "value": 'false',
            "text": "Не"
        }],
        valueField: 'value',
        textField: 'text',
        multiple: false,
    });

    jQuery('#search-report-kad-ident').textbox('clear');
    jQuery('#search-report-masiv').textbox('clear');
    jQuery('#search-report-plot-number').textbox('clear');
}

function initOwnPlotsReportGrid() {
    jQuery('#plots-report-tables').datagrid({
        iconCls: 'icon-edit-geometry',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 30,
        fit: true,
        fitColumns: false,
        showFooter: true,
        url: 'index.php?plots-rpc=own-plots-report-grid',
        idField: '',
        sortName: 'gid',
        border: false,
        sortOrder: 'asc',
        rpcParams: [{'filters':{
                'report_date': null,
                'report_date_from': null,
                'report_date_as_of': todayDate,
                'reprot_farming':null,
                'report_ekate':null,
                'report_ntp':null,
                'report_category':null,
                'report_irrigation': null,
                'report_exclude_inactive':true
            }}],
        columns: [[{
            field: 'land',
            title: '<b>Землище</b>',
            sortable: false,
            width: 200
        }, {
            field: 'kad_ident',
            title: '<b>Идентификатор</b>',
            sortable: true,
            width: 200
        }, {
            field: 'mestnost',
            title: '<b>Местност</b>',
            sortable: true,
            width: 200
        }, {
            field: 'category',
            title: '<b>Категория</b>',
            sortable: true,
            width: 200
        }, {
            field: 'area_type',
            title: '<b>НТП</b>',
            sortable: true,
            width: 200
        }, {
            field: 'area',
            title: '<b>Площ(дка)</b>',
            sortable: true,
            width: 150
        }, {
            field: 'usable',
            title: '<b>Обработваем</b>',
            sortable: true,
            width: 150
        }, {
            field: 'kvs_allowable_area',
            title: '<b>Обработваема площ(дка)</b>',
            sortable: true,
            width: 150
        }, {
            field: 'c_num',
            title: '<b>Договор №</b>',
            sortable: true,
            width: 200,
            formatter: function (value, row, index) {
                if (row && row.id) {
                    return '<a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="showContractInNewTab(' + row.contract_id + ',' + row.is_annex + ')">' + row.c_num + '</a>';
                }
            }
        }, {
            field: 'active',
            title: '<b>Активен</b>',
            sortable: true,
            width: 80,
            formatter: function (value, row, index) {
                if (row && row.active !== undefined) {
                    return row.active ? 'Да': 'Не';
                }
            }
        }, {
            field: 'farming_id',
            title: '<b>Стопанство</b>',
            sortable: true,
            width: 200
        }, {
            field: 'start_date',
            title: '<b>Дата</b>',
            sortable: true,
            width: 200
        }, {
            field: 'na_num',
            title: '<b>Нотар. акт №</b>',
            sortable: true,
            width: 200
        }, {
            field: 'tom',
            title: '<b>Том</b>',
            sortable: true,
            width: 200
        }, {
            field: 'delo',
            title: '<b>Дело</b>',
            sortable: true,
            width: 200
        }, {
            field: 'court',
            title: '<b>Районен съд</b>',
            sortable: true,
            width: 200
        }, {
            field: 'price_per_acre',
            title: '<b>Цена/дка</b>',
            sortable: true,
            width: 150
        }, {
            field: 'price_sum',
            title: '<b>Сума</b>',
            sortable: true,
            width: 100
        }]],
        pagination: true,
        rownumbers: true,
        toolbar: '#reports_toolbar',
        onBeforeLoad: function() {
            jQuery(this).datagrid('clearChecked');
        },
        onLoadSuccess: function() {
            TF.Loading.end();
        },
        rowStyler: function (index, row) {
            if(row.active != undefined && row.active == false){
                return 'color:#aaa';
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initDetailedOwnPlotsReport() {

    var detailedOwnPlotsGrid = jQuery('#plots-report-tables-detail');
    detailedOwnPlotsGrid.datagrid({
        iconCls: 'icon-edit-geometry',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 30,
        fit: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?plots-rpc=detailed-own-plots-report-grid',
        idField: '',
        sortName: 'gid',
        sortOrder: 'desc',
        border: false,
        singleSelect: true,
        rpcParams: [{'filters':{
                'report_date': null,
                'report_date_from': null,
                'report_date_as_of': todayDate,
                'reprot_farming':null,
                'report_ekate':null,
                'report_ntp':null,
                'report_category':null,
                'report_irrigation': null,
                'report_exclude_inactive': true
            }}],
        view: detailview,
        detailFormatter:function(index,row){
            return '<div style="padding:2px"><table class="ddv"></table></div>';
        },
        columns: [[{
            field: 'land',
            title: '<b>Землище</b>',
            sortable: true,
            width: 200
        }, {
            field: 'kad_ident',
            title: '<b>Идентификатор</b>',
            sortable: true,
            width: 200
        }, {
            field: 'mestnost',
            title: '<b>Местност</b>',
            sortable: true,
            width: 150
        }, {
            field: 'farming_id',
            title: '<b>Собственост</b>',
            sortable: true,
            width: 200
        }, {
            field: 'category',
            title: '<b>Категория</b>',
            sortable: true,
            width: 150
        }, {
            field: 'area_type',
            title: '<b>НТП</b>',
            sortable: true,
            width: 200
        }, {
            field: 'area',
            title: '<b>Площ по договор<br>за собственост (дка)</b>',
            sortable: true,
            width: 150
        }, {
            field: 'kvs_allowable_area',
            title: '<b>Обработваема площ(дка)</b>',
            sortable: true,
            width: 150
        }]],
        rowStyler: function (index, row) {
            if (row.active != undefined && row.active == false) {
                return 'color: #aaa';
            }
        },
        pagination: true,
        rownumbers: true,
        toolbar: '#reports_toolbar',
        onBeforeLoad: function() {
            detailedOwnPlotsGrid.datagrid('clearChecked');
        },
        onLoadSuccess: function() {
            TF.Loading.end();
        },
        onExpandRow: function(index,row){
            var ddv = jQuery(this).datagrid('getRowDetail',index).find('table.ddv');
            ddv.datagrid({
                url:'index.php?plots-rpc=detailed-own-plots-report-grid',
                rpcParams: [row.gid],
                fitColumns:true,
                singleSelect:true,
                rpcMethod: 'plotDetails',
                rownumbers:true,
                loadMsg:'Зареждане на данни',
                height:'auto',
                showFooter: false,
                sortName: 'nm_usage_rights',
                sortOrder: 'desc',
                rowStyler: function (index, row) {
                    if (row.active && row.active == 'Анулиран' ) {
                        return 'color: #aaa';
                    }
                },
                columns:[[
                    {
                        field: 'c_num',
                        title: '<b>Номер дог.</b>',
                        sortable: false,
                        align: 'center',
                        formatter:function(value, row, index){
                            if (row.is_sublease == "Да") {
                                return '<a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="showSubleasedContract('+row.id+')">'+row.c_num+'</a>';
                            } else {
                                return '<a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="showContractInNewTab('+row.id+','+ row.is_annex+')">'+row.c_num+'</a>';
                            }
                        }
                    },{
                        field: 'nm_usage_rights',
                        title: '<b>Ползване</b>',
                        sortable: false,
                        align: 'center'
                    },{
                        field: 'start_date',
                        title: '<b>Начална дата</b>',
                        sortable: false,
                        align: 'center'
                    },{
                        field: 'due_date',
                        title: '<b>Крайна дата</b>',
                        sortable: false,
                        align: 'center'
                    },{
                        field: 'c_date',
                        title: '<b>Дата на сключване</b>',
                        sortable: false,
                        align: 'center'
                    },{
                        field: 'farming_id',
                        title: '<b>Стопанство</b>',
                        sortable: false,
                        align: 'center'
                    },{
                        field: 'contract_area',
                        title: '<b>Площ по дог.<br> (дка.)</b>',
                        sortable: false,
                        align: 'center'
                    },{
                        field: 'price_per_acre',
                        title: '<b>Цена<br>(лв./дка.)</b>',
                        sortable: false,
                        align: 'center'
                    },{
                        field: 'price_sum',
                        title: '<b>Сума <br> (пари)</b>',
                        sortable: false,
                        align: 'center'
                    },{
                        field: 'active',
                        title: '<b>Активен</b>',
                        sortable: false,
                        align: 'center'
                    },{
                        field: 'is_sublease',
                        title: '<b>Преотдаден</b>',
                        sortable: false,
                        align: 'center'
                    }
                ]],
                onResize:function(){
                    jQuery(detailedOwnPlotsGrid).datagrid('fixDetailRowHeight',index);
                },
                onLoadSuccess:function(){
                    setTimeout(function(){
                        jQuery(detailedOwnPlotsGrid).datagrid('fixDetailRowHeight',index);
                    },0);
                },
                loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
            });
            jQuery(detailedOwnPlotsGrid).datagrid('fixDetailRowHeight',index);
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initSubleasedPlotsReportGrid() {

    jQuery('#plots-report-tables').datagrid({
        iconCls: 'icon-edit-geometry',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 30,
        fit: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?plots-rpc=subleased-plots-report-grid',
        idField: '',
        sortName: 'gid',
        border: false,
        sortOrder: 'asc',
        rpcParams: [{'filters':{
                'report_date':  toDateStr,
                'report_date_from': todayDate,
                'reprot_farming':null,
                'report_ekate':null,
                'report_ntp':null,
                'report_category':null,
                'report_irrigation': null
            }}],
        columns: [[{
            field: 'land',
            title: '<b>Землище</b>',
            sortable: true,
            width: 200
        }, {
            field: 'kad_ident',
            title: '<b>Идентификатор</b>',
            sortable: true,
            width: 200
        }, {
            field: 'mestnost',
            title: '<b>Местност</b>',
            sortable: true,
            width: 200
        }, {
            field: 'category',
            title: '<b>Категория</b>',
            sortable: true,
            width: 150
        }, {
            field: 'area_type',
            title: '<b>НТП</b>',
            sortable: true,
            width: 200
        }, {
            field: 'area',
            title: '<b>Площ(дка)</b>',
            sortable: true,
            width: 150
        }, {
            field: 'farming',
            title: '<b>Собственик</b>',
            sortable: true,
            width: 300
        }, {
            field: 'contragent',
            title: '<b>Отдаден на</b>',
            sortable: true,
            width: 300
        }, {
            field: 'egn',
            title: '<b>ЕГН/ЕИК</b>',
            sortable: false,
            width: 150
        }, {
            field: 'c_num',
            title: '<b>Договор</b>',
            sortable: true,
            width: 200,
            formatter:function(value, row){
                if (row && row.sublease_id > 0) {
                    return '<a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="showSubleasedContract('+row.sublease_id+')">'+row.c_num+'</a>';
                }
            }
        }, {
            field: 'start_date',
            title: '<b>Дата</b>',
            sortable: true,
            width: 200
        }, {
            field: 'due_date',
            title: '<b>Валиден до</b>',
            sortable: true,
            width: 200
        }, {
            field: 'renta',
            title: '<b>Рента (пари)</b>',
            sortable: true,
            width: 250
        }]],
        pagination: true,
        rownumbers: true,
        toolbar: '#reports_toolbar',
        onBeforeLoad: function() {
            jQuery(this).datagrid('clearChecked');
        },
        onLoadSuccess: function() {
            TF.Loading.end();
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initForSubleasePlotsReportGrid() {
    jQuery('#plots-report-tables').datagrid({
        iconCls: 'icon-edit-geometry',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 30,
        fit: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?plots-rpc=for-sublease-plots-report-grid',
        idField: '',
        sortName: 'gid',
        border: false,
        sortOrder: 'asc',
        rpcParams: [{'filters':{
                'report_date':  toDateStr,
                'report_date_from': todayDate,
                'reprot_farming':null,
                'report_ekate':null,
                'report_ntp':null,
                'report_category':null,
                'report_irrigation': null
            }}],
        columns: [[{
                    field: 'land',
                    title: '<b>Землище</b>',
                    sortable: false,
                    width: 200
                }, {
                    field: 'kad_ident',
                    title: '<b>Идентификатор</b>',
                    sortable: true,
                    width: 200
                }, {
                    field: 'mestnost',
                    title: '<b>Местност</b>',
                    sortable: true,
                    width: 200
                }, {
                    field: 'category',
                    title: '<b>Категория</b>',
                    sortable: true,
                    width: 180
                }, {
                    field: 'area_type',
                    title: '<b>НТП</b>',
                    sortable: true,
                    width: 200
                }, {
                    field: 'area',
                    title: '<b>Площ(дка)</b>',
                    sortable: true,
                    width: 150
                }, {
                    field: 'kvs_allowable_area',
                    title: '<b>Обработваема площ(дка)</b>',
                    sortable: true,
                    width: 310
                }, {
                    field: 'farming',
                    title: '<b>Собственик</b>',
                    sortable: false,
                    width: 300
                }, {
                    field: 'eik',
                    title: '<b>ЕИК</b>',
                    sortable: false,
                    width: 150
                }, {
                    field: 'c_num',
                    title: '<b>Договор №</b>',
                    sortable: true,
                    width: 150,
                    formatter:function(value, row){
                        if (row && row.c_id > 0) {
                            return '<a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="showContractInNewTab('+row.c_id + ',' + false + ')">'+row.c_num+'</a>';
                        }
                    }
                }, {
                    field: 'start_date',
                    title: '<b>Дата</b>',
                    sortable: true,
                    width: 150
                }, {
                    field: 'na_num',
                    title: '<b>Нотар. акт №</b>',
                    sortable: true,
                    width: 200
                }, {
                    field: 'tom',
                    title: '<b>Том</b>',
                    sortable: true,
                    width: 200
                }, {
                    field: 'delo',
                    title: '<b>Дело</b>',
                    sortable: true,
                    width: 200
                }, {
                    field: 'court',
                    title: '<b>Районен съд</b>',
                    sortable: true,
                    width: 200
                }]],
        pagination: true,
        rownumbers: true,
        toolbar: '#reports_toolbar',
        onBeforeLoad: function() {
            jQuery('#plots-report-tables').datagrid('clearChecked');
        },
        onLoadSuccess: function() {
            TF.Loading.end();
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initHypothecsReportGrid(toDateStr , todayDate ) {
    jQuery('#plots-report-tables').datagrid({
        iconCls: 'icon-edit-geometry',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 30,
        fit: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?plots-rpc=hypothecs-plots-report-grid',
        idField: '',
        sortName: 'gid',
        border: false,
        sortOrder: 'asc',
        rowStyler: function(index, row) {
            if (row.is_active != undefined && row.is_active == false) {
                return 'color: #aaa';
            }
        },
        rpcParams: [{'filters':{
                'report_date':  toDateStr,
                'report_date_from': todayDate,
                'reprot_farming':null,
                'report_ekate':null,
                'report_ntp':null,
                'report_category':null,
                'report_irrigation': null
            }}],
        columns: [[{
            field: 'land',
            title: '<b>Землище</b>',
            sortable: false,
            width: 200
        }, {
            field: 'kad_ident',
            title: '<b>Идентификатор</b>',
            sortable: true,
            width: 200
        }, {
            field: 'mestnost',
            title: '<b>Местност</b>',
            sortable: true,
            width: 200
        }, {
            field: 'category',
            title: '<b>Категория</b>',
            sortable: true,
            width: 200
        }, {
            field: 'area_type',
            title: '<b>НТП</b>',
            sortable: true,
            width: 200
        }, {
            field: 'area',
            title: '<b>Площ(дка)</b>',
            sortable: true,
            width: 200
        }, {
            field: 'num',
            title: '<b>Ипотека №</b>',
            sortable: true,
            width: 300,
            formatter: function (value, row, index) {
                if(row && row.h_id > 0){
                    return '<a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="showHypothecsInNewTab(' + row.h_id +')">' + row.num + '</a>';
                }
            }
        }, {
            field: 'hypothec_area',
            title: '<b>Ипотекирана<br>площ (дка)</b>',
            align: 'center',
            sortable: true,
            width: 300
        }, {
            field: 'start_date',
            title: '<b>Влиза в сила</b>',
            sortable: true,
            width: 200
        }, {
            field: 'due_date',
            title: '<b>Падеж</b>',
            sortable: true,
            width: 200
        }, {
            field: 'creditor',
            title: '<b>Кредитор</b>',
            sortable: true,
            width: 200
        }]],
        pagination: true,
        rownumbers: true,
        toolbar: '#reports_toolbar',
        onBeforeLoad: function() {
            jQuery('#hypothecs-report-tables').datagrid('clearChecked');
        },
        onLoadSuccess: function() {
            TF.Loading.end();
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initForHypothecReportGrid() {
    jQuery('#plots-report-tables').datagrid({
        iconCls: 'icon-edit-geometry',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 30,
        fit: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?plots-rpc=for-hypothec-plots-report-grid',
        idField: '',
        sortName: 'gid',
        border: false,
        sortOrder: 'asc',
        rpcParams: [{'filters':{
                'report_date':  toDateStr,
                'report_date_from': todayDate,
                'reprot_farming':null,
                'report_ekate':null,
                'report_ntp':null,
                'report_category':null,
                'report_irrigation': null
            }}],
        columns: [[{
            field: 'land',
            title: '<b>Землище</b>',
            sortable: false,
            width: 200
        }, {
            field: 'kad_ident',
            title: '<b>Идентификатор</b>',
            sortable: true,
            width: 200
        }, {
            field: 'mestnost',
            title: '<b>Местност</b>',
            sortable: true,
            width: 200
        }, {
            field: 'category',
            title: '<b>Категория</b>',
            sortable: true,
            width: 180
        }, {
            field: 'area_type',
            title: '<b>НТП</b>',
            sortable: true,
            width: 200
        }, {
            field: 'area',
            title: '<b>Площ(дка)</b>',
            sortable: true,
            width: 150
        }, {
            field: 'c_num',
            title: '<b>Договор №</b>',
            sortable: true,
            width: 150,
            formatter: function (value, row) {
                if (row && row.c_id > 0) {
                    if (row.c_a_id) {
                        const regex = /^\d+/gm;
                        return '<a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="showContractInNewTab(' + row.c_id + ',' + row.is_annex + ')">' + regex.exec(row.c_num) + '</a> (Анекс) от </br><a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="showContractInNewTab(' + row.c_a_id + ',false)">' + row.c_a_c_num + '</a> (Договор)';
                    }
                    return '<a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="showContractInNewTab(' + row.c_id + ',' + false + ')">' + row.c_num + '</a>';
                }
            }
        }, {
            field: 'start_date',
            title: '<b>Дата</b>',
            sortable: true,
            width: 150
        }, {
            field: 'na_num',
            title: '<b>Нотар. акт №</b>',
            sortable: true,
            width: 200
        }, {
            field: 'tom',
            title: '<b>Том</b>',
            sortable: true,
            width: 200
        }, {
            field: 'delo',
            title: '<b>Дело</b>',
            sortable: true,
            width: 200
        }, {
            field: 'court',
            title: '<b>Районен съд</b>',
            sortable: true,
            width: 200
        }]],
        pagination: true,
        rownumbers: true,
        toolbar: '#reports_toolbar',
        onBeforeLoad: function() {
            jQuery('#plots-report-tables').datagrid('clearChecked');
        },
        onLoadSuccess: function() {
            TF.Loading.end();
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initRentedPlotsReportGrid() {

    jQuery('#include-subleased-in-report-menu').show();

    jQuery('#plots-report-tables').datagrid({
        iconCls: 'icon-edit-geometry',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 30,
        fit: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?plots-rpc=rented-plots-report-grid',
        idField: '',
        sortName: 'gid',
        border: false,
        sortOrder: 'asc',
        rpcParams: [{'filters':{
                'report_date':  toDateStr,
                'report_date_from': todayDate,
                'reprot_farming':null,
                'report_ekate':null,
                'report_ntp':null,
                'report_category':null,
                'report_irrigation': null,
                'report_include_subleases': true
            }}],
        columns: [[{
            field: 'ekate',
            title: '<b>Землище</b>',
            sortable: false,
            width: 200
        }, {
            field: 'kad_ident',
            title: '<b>Идентификатор</b>',
            sortable: true,
            width: 200
        }, {
            field: 'mestnost',
            title: '<b>Местност</b>',
            sortable: true,
            width: 200
        }, {
            field: 'category',
            title: '<b>Категория</b>',
            sortable: true,
            width: 180
        }, {
            field: 'area_type',
            title: '<b>НТП</b>',
            sortable: true,
            width: 200
        }, {
            field: 'area',
            title: '<b>Площ(дка)</b>',
            sortable: true,
            width: 150
        }, {
            field: 'kvs_allowable_area',
            title: '<b>Обработваема <br/> площ</b>',
            sortable: true,
            width: 220
        }, {
            field: 'c_type',
            title: '<b>Ползване</b>',
            sortable: true,
            width: 150
        }, {
            field: 'lease_type',
            title: '<b>Тип</b>',
            sortable: true,
            width: 150,
            align: 'center'
        }, {
            field: 'farming_id',
            title: '<b>Стопанство</b>',
            sortable: true,
            width: 220
        }, {
            field: 'owner_names',
            title: '<b>Арендодател<br/>/Наемодател</b>',
            sortable: true,
            width: 200
        }, {
            field: 'egn',
            title: '<b>ЕГН/ЕИК</b>',
            sortable: false,
            width: 180
        }, {
            field: 'c_num',
            title: '<b>Договор<br/>№</b>',
            sortable: true,
            width: 200,
            align: 'center',
            formatter:function(value, row){
                if (row.c_id > 0) {
                    if (row.c_a_id) {
                        const cNum = row.c_num;
                        const position = cNum.indexOf('(Анекс)');
                        const aNum = cNum.slice(0, position);
                        return '<a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="showContractInNewTab(' + row.c_id + ',' + row.is_annex + ')">' + aNum + '</a> (Анекс) от </br><a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="showContractInNewTab(' + row.c_a_id + ',false)">' + row.c_a_c_num + '</a> (Договор)';
                    }
                    return '<a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="showContractInNewTab('+row.c_id+')">'+row.c_num+'</a>';
                }
            }
        }, {
            field: 'sublease_num',
            title: '<b>Преотдаден<br/>чрез</b>',
            sortable: true,
            width: 200,
            align: 'center',
            formatter:function(value, row){
                if (row.is_sublease) {
                    return '<a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="showSubleasedContract('+row.sublease_id+')" title="('+row.subl_start_date+' - '+row.subl_due_date+')">'+row.sublease_num+'</a>';
                }
                if (row.has_sublease) {
                    return '<a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="showSubleasedContract('+row.has_sublease+')">'+row.has_sublease_num+'</a>';
                }
            }
        }, {
            field: 'sv_num',
            title: '<b>Вписване<br/>№</b>',
            sortable: true,
            width: 200,
            align: 'center'
        }, {
            field: 'sv_date',
            title: '<b>Дата на<br/>вписване</b>',
            sortable: true,
            width: 180,
            align: 'center'
        }, {
            field: 'due_date',
            title: '<b>Валиден<br/>до</b>',
            sortable: true,
            width: 180,
            align: 'center'
        }, {
            field: 'renta',
            title: '<b>Рента<br/>(пари)</b>',
            sortable: true,
            width: 150,
            align: 'center'
        }]],
        pagination: true,
        rownumbers: true,
        toolbar: '#reports_toolbar',
        onBeforeLoad: function() {
            jQuery('#plots-report-tables').datagrid('clearChecked');
        },
        onLoadSuccess: function() {
            TF.Loading.end();
        },
        rowStyler:function(index,row){
            if (row.is_sublease || row.has_sublease){
                return 'background-color:#56c489;';
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initSubleasedRentedPlotsReportGrid() {

    jQuery('#plots-report-tables').datagrid({
        iconCls: 'icon-edit-geometry',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 30,
        fit: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?plots-rpc=subleased-rented-plots-report-grid',
        idField: '',
        sortName: 'gid',
        border: false,
        sortOrder: 'asc',
        rpcParams: [{'filters':{
                'report_date':  toDateStr,
                'report_date_from': todayDate,
                'reprot_farming':null,
                'report_ekate':null,
                'report_ntp':null,
                'report_category':null,
                'report_irrigation': null
            }}],
        columns: [[{
            field: 'land',
            title: '<b>Землище</b>',
            sortable: true,
            width: 200
        }, {
            field: 'kad_ident',
            title: '<b>Идентификатор</b>',
            sortable: true,
            width: 200
        }, {
            field: 'mestnost',
            title: '<b>Местност</b>',
            sortable: true,
            width: 200
        }, {
            field: 'category',
            title: '<b>Категория</b>',
            sortable: true,
            width: 180
        }, {
            field: 'area_type',
            title: '<b>НТП</b>',
            sortable: true,
            width: 200
        }, {
            field: 'area',
            title: '<b>Площ(дка)</b>',
            sortable: true,
            width: 150
        }, {
            field: 'c_type',
            title: '<b>Ползване</b>',
            sortable: true,
            width: 150
        }, {
            field: 'owner_names',
            title: '<b>Собственик</b>',
            sortable: true,
            width: 200
        }, {
            field: 'egn',
            title: '<b>ЕГН/ЕИК</b>',
            sortable: false,
            width: 200
        }, {
            field: 'farming',
            title: '<b>Арендодател<br/>/Наемодател</b>',
            sortable: true,
            width: 200
        }, {
            field: 'contragent',
            title: '<b>Арендатор<br/>/Наемател</b>',
            sortable: true,
            width: 200
        }, {
            field: 'c_num',
            title: '<b>Договор №</b>',
            sortable: true,
            width: 150,
            formatter: function (value, row) {
                if (row && row.sublease_id > 0) {
                    return '<a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="showSubleasedContract(' + row.sublease_id + ')">' + row.c_num + '</a>';
                }
            }
        }, {
            field: 'sv_num',
            title: '<b>Вписване №</b>',
            sortable: true,
            width: 200
        }, {
            field: 'sv_date',
            title: '<b>Дата на<br/>вписване</b>',
            sortable: true,
            width: 180
        }, {
            field: 'due_date',
            title: '<b>Валиден до</b>',
            sortable: true,
            width: 180
        }, {
            field: 'renta',
            title: '<b>Рента (пари)</b>',
            sortable: true,
            width: 250
        }]],
        pagination: true,
        rownumbers: true,
        toolbar: '#reports_toolbar',
        onBeforeLoad: function() {
            jQuery('#plots-report-tables').datagrid('clearChecked');
        },
        onLoadSuccess: function() {
            TF.Loading.end();
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initRentedExpiringContractsReportGrid() {
    var reportDateFrom = jQuery('#choose-report-date').datebox('getValue');
    var reportDate = jQuery('#choose-report-date-to').datebox('getValue');
    var reportFarming = null;
    var reportEkatte = null;
    var reportNtp = null;
    var reportCategory = null;
    var reportIrrigation = null;

    jQuery('#choose-report-date-from-cont').show();
    jQuery('#plots-report-tables').datagrid({
        iconCls: 'icon-edit-geometry',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 30,
        fitColumns: false,
        showFooter: true,
        url: 'index.php?plots-rpc=expiring-contracts-report-grid',
        idField: 'kad_ident',
        sortName: 'kad_ident',
        border: false,
        sortOrder: 'asc',
        rpcParams: [{
            'filters':{
                'report_date': reportDate,
                'report_date_from':reportDateFrom,
                'reprot_farming':reportFarming,
                'report_ekate':reportEkatte,
                'report_ntp':reportNtp,
                'report_category':reportCategory,
                'report_irrigation': reportIrrigation
            }
        }],
        columns: [[
            {
                title: '<b>Информация за имот</b>',
                colspan: 6
            },{
                title: '<b>Информация за съществуващи договори</b>',
                colspan: 10
            },{
                title: '<b>Информация за нови договори</b>',
                colspan: 3
            }
            ,{
                colspan: 1
            }],
            [{
                field: 'land',
                title: '<b>Землище</b>',
                sortable: false,
                width: 100
            }, {
                field: 'kad_ident',
                title: '<b>Идентификатор</b>',
                sortable: true,
                width: 130
            }, {
                field: 'document_area',
                title: '<b>Пл. по док.(дка)</b>',
                sortable: true,
                width: 120
            },  {
                field: 'mestnost',
                title: '<b>Местност</b>',
                sortable: true,
                width: 90
            }, {
                field: 'category',
                title: '<b>Категория</b>',
                sortable: true,
                width: 100
            }, {
                field: 'area_type',
                title: '<b>НТП</b>',
                sortable: true,
                width: 120
            }, {
                field: 'c_num',
                title: '<b>Договор №</b>',
                sortable: true,
                width: 150,
                formatter: function (value, row) {
                    if (row.c_id > 0) {
                        return '<a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="showContractInNewTab('+row.c_id+','+row.is_annex+')">'+row.c_num+'</a>';
                    }
                }
            },  {
                field: 'due_date',
                title: '<b>Валиден до</b>',
                sortable: true,
                width: 120
            }, {
                field: 'c_type',
                title: '<b>Ползване</b>',
                sortable: true,
                width: 90
            }, {
                field: 'area',
                title: '<b>Площ(дка)</b>',
                sortable: true,
                width: 90
            }, {
                field: 'fraction',
                title: '<b>Ид. част</b>',
                sortable: true,
                width: 80,
                align: 'center',
                formatter:function(value, row){
                    if (value) {
                        var fraction = new Fraction(value);
                        return fraction.simplify().toFraction();
                    }
                }
            }, {
                field: 'farming_id',
                title: '<b>Стопанство</b>',
                sortable: true,
                width: 150
            }, {
                field: 'owner_names',
                title: '<b>Арендодател/Наемодател</b>',
                sortable: true,
                width: 200
            }, {
                field: 'egn',
                title: '<b>ЕГН/ЕИК</b>',
                sortable: false,
                width: 100
            }, {
                field: 'phones',
                title: '<b>Телефон</b>',
                sortable: true,
                width: 150
            }, {
                field: 'sv_num',
                title: '<b>Вписване №</b>',
                sortable: true,
                width: 110
            }, {
                field: 'sv_date',
                title: '<b>Дата на вписване</b>',
                sortable: true,
                width: 150
            }, {
                field: 'renewed',
                title: '<b>Подновен</b>',
                sortable: true,
                width: 90
            }, {
                field: 'new_c_num',
                title: '<b>Нов дог.</b>',
                sortable: false,
                width: 180,
                formatter: function (value, row) {
                    if (value) {
                        return row.new_c_links;
                    }
                }
            }, {
                field: 'new_c_period',
                title: '<b>Период</b>',
                sortable: false,
                align: 'center',
                width: 150
            }
            ]],
        pagination: true,
        rownumbers: true,
        toolbar: '#reports_toolbar',
        onBeforeLoad: function() {
            jQuery('#plots-report-tables').datagrid('clearChecked');
        },
        onLoadSuccess: function () {
            jQuery('#plots-report-tables').datagrid('autoSizeColumn');
            TF.Loading.end();
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function addSubleasedFooterMessage() {
    jQuery('#plots-report-layout').layout('add',{
        region: 'south',
        height: 30,
        noheader: true,
        content: '<div style="width: 15px; height: 15px; border: 1px solid #000; background-color: #56c489; float: left; margin: 0px 10px 5px 10px;"></div> Имотите маркирани в този цвят са включени в договор за преотдаване.',
        bodyCls: 'datagrid-footer-padding',
        border: false,
        style: { 'border-top': '1px solid #000', 'padding-top': '5px'}
    });
}

function addDateFilterFooterMessage(report_type) {

    var layoutObj = jQuery('#plots-report-layout');
    if(report_type === 'own_plots_detailed') {
        layoutObj = jQuery('#plots-report-layout-detail');
    }
    if (layoutObj.data().layout.panels.south.length === 0) {
        layoutObj.layout('add', {
            region: 'south',
            height: 30,
            noheader: true,
            content: '',
            bodyCls: 'datagrid-footer-padding',
            border: false,
            style: {'border-top': '1px solid #000', 'padding-top': '5px'}
        });
    } else {
        layoutObj.layout('panel', 'south').panel('resize', {height: 60});
    }

    var originalContent = '<p>В справката са представени имоти, включени в договори с ';

    var noFilterContent = '<p>В справката са представени имоти, включени в договори независимо от периода им на действие.</p>';
    if(layoutObj.layout('panel', 'south').panel('options').content === noFilterContent)
    {
        layoutObj.layout('panel', 'south').panel('options').content = originalContent;
    }
    var datesContent = originalContent;
    var panelOptions = layoutObj.layout('panel', 'south').panel('options');
    var content = panelOptions.content.split(datesContent)[0];

    var dates = getFilterDates();
    if(dates.fromDate !== '') datesContent += ' <b>начална дата</b> преди '+ dates.fromDate;
    if(dates.toDate !== '') datesContent += ' <b> крайна дата</b> след '+ dates.toDate;
    if(dates.asOfDate !== '') datesContent += ' <b>начална дата</b> преди '+ dates.asOfDate + ' <b>крайна дата</b> след '+ dates.asOfDate;
    if(dates.contractToDate !== '')  datesContent += ' <b>дата на сключване</b> преди '+ dates.contractToDate;
    if(dates.contractFromDate !== '')  datesContent += ' <b>дата на сключване</b> след '+ dates.contractFromDate;

    datesContent += '</p>';

    content += datesContent;
    if (content === originalContent + '</p>')
    {
        content = noFilterContent;
    }

    if (report_type == 'rented_expiring_contracts') {
        content = '<p>В справката са представени имоти, включени в договори и анекси, които <b>изтичат в периода</b> от ' + dates.fromDate + ' до ' + dates.toDate + '</p>';
    }
    layoutObj.layout('panel', 'south').panel('options').content = content;
    layoutObj.layout('panel', 'south').panel();
}

function getFilterDates() {
    var dateObj = {};
    dateObj.fromDate = jQuery('#choose-report-date').datebox('getValue');
    dateObj.toDate = jQuery('#choose-report-date-to').datebox('getValue');
    dateObj.asOfDate = jQuery('#choose-report-date-as-of').datebox('getValue');
    dateObj.contractFromDate = jQuery('#choose-report-contract-date').datebox('getValue');
    dateObj.contractToDate = jQuery('#choose-report-contract-date-to').datebox('getValue');
    return dateObj;
}

function removeFooterMessage() {
    jQuery('#report-layout').layout('remove', 'south');
    jQuery('#plots-report-layout').layout('remove', 'south');
}

function initPlotsInManyContractsReportGrid() {
    jQuery('#plots-report-tables').datagrid({
        iconCls: 'icon-edit-geometry',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 30,
        fit: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?plots-rpc=plots-in-many-contracts-report-grid',
        idField: '',
        sortName: 'gid',
        border: false,
        sortOrder: 'asc',
        rpcParams: [{'filters':{
                'report_date':  toDateStr,
                'report_date_from': todayDate,
                'reprot_farming':null,
                'report_ekate':null,
                'report_ntp':null,
                'report_category':null,
                'report_irrigation': null
            }}],
        columns: [[{
            field: 'land',
            title: '<b>Землище</b>',
            sortable: false,
            width: 180
        }, {
            field: 'kad_ident',
            title: '<b>Идентификатор</b>',
            sortable: true,
            width: 200
        }, {
            field: 'document_area',
            title: '<b>Площ по<br/>документ (дка)</b>',
            sortable: true,
            width: 150
        }, {
            field: 'sum_contract_area',
            title: '<b>Обща площ по<br/>договори (дка)</b>',
            sortable: false,
            width: 150
        }, {
            field: 'contract_area',
            title: '<b>Площ по<br/>договор (дка)</b>',
            sortable: false,
            width: 150
        }, {
            field: 'c_num',
            title: '<b>Договор №</b>',
            sortable: false,
            width: 150,
            formatter: function (value, row) {
                var links = '';
                if (row.c_id != undefined && row.c_id.length > 0) {
                    row.c_id = row.c_id.replace('{', '');
                    row.c_id = row.c_id.replace('}', '');
                    var c_ids = row.c_id.split(',');
                    var c_nums = row.c_num.split('<br/>');

                    for (var i = 0; i < c_nums.length; i++) {
                        links += '<a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="showContractInNewTab(' + c_ids[i] + ',' + false + ')">' + c_nums[i] + '</a><br>';
                    }

                    return links;
                }
            }
        }, {
            field: 'c_date',
            title: '<b>Дата</b>',
            sortable: false,
            width: 180
        }, {
            field: 'c_type',
            title: '<b>Тип</b>',
            sortable: false,
            width: 150
        }, {
            field: 'farming_id',
            title: '<b>Стопанство</b>',
            sortable: false,
            width: 150
        }, {
            field: 'start_date',
            title: '<b>Влиза в сила</b>',
            sortable: false,
            width: 160
        }, {
            field: 'due_date',
            title: '<b>Валиден до</b>',
            sortable: false,
            width: 160
        }, {
            field: 'status',
            title: '<b>Статус</b>',
            sortable: false,
            width: 130
        }]],
        pagination: true,
        rownumbers: true,
        toolbar: '#reports_toolbar',
        onBeforeLoad: function() {
            jQuery(this).datagrid('clearChecked');
        },
        onLoadSuccess: function() {
            TF.Loading.end();
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initUsedPlotsReportGrid() {

    jQuery('#include-subleased-in-report-menu').show();

    jQuery('#plots-report-tables').datagrid({
        iconCls: 'icon-edit-geometry',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 30,
        fit: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?plots-rpc=used-plots-report-grid',
        idField: '',
        sortName: 'gid',
        border: false,
        sortOrder: 'asc',
        rpcParams: [{'filters':{
                'report_date': toDateStr,
                'report_date_from': todayDate,
                'reprot_farming':null,
                'report_ekate':null,
                'report_ntp':null,
                'report_category':null,
                'report_irrigation': null,
                'report_include_subleases': true
            }}],
        columns: [[{
            field: 'land',
            title: '<b>Землище</b>',
            sortable: false,
            width: 200
        }, {
            field: 'kad_ident',
            title: '<b>Идентификатор</b>',
            sortable: true,
            width: 200
        }, {
            field: 'mestnost',
            title: '<b>Местност</b>',
            sortable: true,
            width: 200
        }, {
            field: 'category',
            title: '<b>Категория</b>',
            sortable: true,
            width: 180
        }, {
            field: 'area_type',
            title: '<b>НТП</b>',
            sortable: true,
            width: 200
        }, {
            field: 'area',
            title: '<b>Пл. по дог.(дка)</b>',
            sortable: true,
            align: 'center',
            width: 150
        }, {
            field: 'fraction',
            title: '<b>Ид. част</b>',
            sortable: true,
            width: 150,
            align: 'center',
            formatter:function(value, row){
                if (value) {
                    var fraction = new Fraction(value);
                    return fraction.simplify().toFraction();
                }
            }
        }, {
            field: 'document_area',
            title: '<b>Пл. по док.(дка)</b>',
            sortable: true,
            width: 150
        }, {
            field: 'kvs_allowable_area',
            title: '<b>Обработваема <br/> площ</b>',
            sortable: true,
            width: 220
        }, {
            field: 'c_type',
            title: '<b>Ползване</b>',
            sortable: true,
            width: 150
        }, {
            field: 'lease_type',
            title: '<b>Тип</b>',
            sortable: true,
            width: 150,
            align: 'center'
        }, {
            field: 'farming_name',
            title: '<b>Стопанство</b>',
            sortable: true,
            width: 220
        }, {
            field: 'owner_names',
            title: '<b>Арендодател<br/>/Наемодател</b>',
            sortable: true,
            width: 250
        }, {
            field: 'egn',
            title: '<b>ЕГН/ЕИК</b>',
            sortable: false,
            width: 180
        }, {
            field: 'c_num',
            title: '<b>Договор №</b>',
            sortable: true,
            width: 180,
            align: 'center',
            formatter:function(value, row){
                if (row.c_id > 0) {
                    if (row.c_a_id) {
                        const regex = /^\d+/gm;
                        return '<a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="showContractInNewTab(' + row.c_id + ',' + row.is_annex + ')">' + row.c_num + '</a> (Анекс) от </br><a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="showContractInNewTab(' + row.c_a_id + ',false)">' + row.c_a_c_num + '</a> (Договор)';
                    }
                    return '<a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="showContractInNewTab('+row.c_id+')">'+row.c_num+'</a>';
                }
            }
        }, {
            field: 'sublease_num',
            title: '<b>Преотдаден<br/>чрез</b>',
            sortable: true,
            width: 180,
            align: 'center',
            formatter:function(value, row){
                if (row.is_sublease) {
                    return '<a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="showSubleasedContract('+row.sublease_id+')" title="('+row.subl_start_date+' - '+row.subl_due_date+')">'+row.sublease_num+'</a>';
                }
                if (row.has_sublease) {
                    return '<a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="showSubleasedContract('+row.has_sublease+')">'+row.has_sublease_num+'</a>';
                }
            }
        }, {
            field: 'sv_num',
            title: '<b>Вписване<br/>№</b>',
            sortable: true,
            width: 200,
            align: 'center'
        }, {
            field: 'sv_date',
            title: '<b>Дата на<br/>вписване</b>',
            sortable: true,
            width: 180,
            align: 'center'
        }, {
            field: 'due_date',
            title: '<b>Валиден<br/>до</b>',
            sortable: true,
            width: 200,
            align: 'center'
        }, {
            field: 'renta',
            title: '<b>Рента<br/>(пари)</b>',
            sortable: true,
            width: 150,
            align: 'center'
        }]],
        pagination: true,
        rownumbers: true,
        toolbar: '#reports_toolbar',
        onBeforeLoad: function() {
            jQuery(this).datagrid('clearChecked');
        },
        onLoadSuccess: function() {
            TF.Loading.end();
        },
        rowStyler:function(index,row){
            if (row.is_sublease || row.has_sublease){
                return 'background-color:#56c489;';
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initContractsWithOwnerlessPlotsReportGrid(GET= null) {
    if(GET.to_date) {
        toDateStr = GET.to_date;
    }
    if(GET.report_date_from) {
        todayDate = GET.report_date_from;
    }

    jQuery('#plots-report-tables').datagrid({
        iconCls: 'icon-edit-geometry',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 30,
        fit: true,
        fitColumns: true,
        singleSelect: true,
        showFooter: true,
        url: 'index.php?plots-rpc=contracts-with-ownerless-plots-report-grid',
        idField: '',
        sortName: 'gid',
        border: false,
        sortOrder: 'asc',
        rpcParams: [{'filters':{
                'report_date': toDateStr,
                'report_date_from': todayDate,
                'reprot_farming':null,
                'report_ekate':null,
                'report_ntp':null,
                'report_category':null,
            }}],
        columns: [[{
            field: 'c_num',
            title: '<b>Номер на договор/анекс</b>',
            sortable: true,
            width: 200,
            formatter:function(value, row, index){
                return '<a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="showContractInNewTab('+row.id+','+ row.is_annex+')">'+row.c_num+'</a>';
            }
        }, {
            field: 'nm_usage_rights',
            title: '<b>Тип на договора</b>',
            sortable: true,
            width: 200
        }, {
            field: 'kad_ident',
            title: '<b>Номер на имота</b>',
            sortable: true,
            width: 200,
            formatter:function(value, row, index){
                return '<a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="showPlotInNewTab('+row.gid+')">'+row.kad_ident+'</a>';
            }
        }, {
            field: 'category',
            title: '<b>Категория</b>',
            sortable: true,
            width: 180
        }, {
            field: 'area_type',
            title: '<b>НТП</b>',
            sortable: true,
            width: 200
        }, {
            field: 'contract_area',
            title: '<b>Площ на имота <br/>по договор (дка)</b>',
            sortable: true,
            width: 150
        }, {
            field: 'start_date',
            title: '<b>Начална дата <br/>на договора</b>',
            sortable: true,
            width: 150
        }, {
            field: 'due_date',
            title: '<b>Крайна дата <br/>на договора</b>',
            sortable: true,
            width: 150
        }]],
        pagination: true,
        rownumbers: true,
        toolbar: '#reports_toolbar',
        onBeforeLoad: function() {
            jQuery(this).datagrid('clearChecked');
        },
        onLoadSuccess: function() {
            TF.Loading.end();
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initHistoricalPlotsReportGrid() {
    jQuery('#plots-report-tables').datagrid({
        iconCls: 'icon-edit-geometry',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 30,
        fit: true,
        fitColumns: true,
        singleSelect: true,
        showFooter: true,
        url: 'index.php?plots-rpc=historical-plots-report-grid',
        idField: '',
        sortName: 'gid',
        border: false,
        sortOrder: 'asc',
        rpcParams: [{'filters':{
                'report_date': toDateStr,
                'report_date_from': todayDate,
                'reprot_farming':null,
                'report_ekate':null,
                'report_ntp':null,
                'report_category':null
            }}],
        columns: [[{
            field: 'c_num',
            title: '<b>Номер на договор/анекс</b>',
            sortable: true,
            width: 200,
            formatter:function(value, row, index){
                return '<a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="showContractInNewTab('+row.id+','+ row.is_annex+')">'+row.c_num+'</a>';
            }
        }, {
            field: 'nm_usage_rights',
            title: '<b>Тип на договора</b>',
            sortable: true,
            width: 200
        }, {
            field: 'kad_ident',
            title: '<b>Номер на архивен имот</b>',
            sortable: true,
            width: 200,
            formatter:function(value, row, index){
                return '<a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="showHistoricalPlotInNewTab('+row.gid+')">'+row.kad_ident+'</a>';
            }
        }, {
            field: 'category',
            title: '<b>Категория</b>',
            sortable: true,
            width: 180
        }, {
            field: 'area_type',
            title: '<b>НТП</b>',
            sortable: true,
            width: 200
        }, {
            field: 'contract_area',
            title: '<b>Площ на имота <br/>по договор (дка)</b>',
            sortable: true,
            width: 150
        }, {
            field: 'start_date',
            title: '<b>Начална дата <br/>на договора</b>',
            sortable: true,
            width: 150
        }, {
            field: 'due_date',
            title: '<b>Крайна дата <br/>на договора</b>',
            sortable: true,
            width: 150
        }]],
        pagination: true,
        rownumbers: true,
        toolbar: '#reports_toolbar',
        onBeforeLoad: function() {
            jQuery(this).datagrid('clearChecked');
        },
        onLoadSuccess: function() {
            TF.Loading.end();
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function showContractInNewTab(contract_id, is_annex) {
    if(is_annex === true) {
        window.open("index.php?page=Contracts.Home&contract_id=" + contract_id, '_blank');
    } else{
        window.open("index.php?page=Contracts.Home&contract_id=" + contract_id, '_blank');
    }
}

function showSubleasedContract(sublease_id) {
    window.open("index.php?page=Subleases.Home&sublease_id=" + sublease_id, '_blank');
}

function showPlotInNewTab(plot_id) {
    window.open("index.php?page=Plots.Home&plot_id=" + plot_id, '_blank');
}

function showHypothecsInNewTab(hypothec_id) {
    window.open("index.php?page=Hypothecs.Home&hypothec_id=" + hypothec_id, '_blank');
}

function showHistoricalPlotInNewTab(plot_id) {
    window.open("index.php?page=Plots.Home&is_edited=true&plot_id=" + plot_id, '_blank');
}

function exportToExcelReport()
{

    if (report_type == 'own_plots_detailed') {
        exportOwnPlotsDetailedToExcel();
        return;
    }
    var winDownload = jQuery('#win-download').window({});

    if(report_type == 'total_area') {
        var data = jQuery('#report-tables').datagrid('getData');
    } else {
        var data = jQuery('#plots-report-tables').datagrid('getData');
    }

    if (data.rows.length === 0) {
        return jQuery.messager.alert('Грешка', 'Не може да експортнете празна справка.', 'error');
    }

    var gridOptions = jQuery('#plots-report-tables').datagrid('options');
    var downloadFile = jQuery('#btn-download-file');
    var params = gridOptions.rpcParams[0];

    var sort = gridOptions.sortName;
    var order = gridOptions.sortOrder;

    if (report_filter == 'report-own-plots') {

        TF.Rpc.Plots.PlotReports.exportToExcelOwnPlotsReportData(params, null, null, sort, order)
            .done(function (dataObj) {

                winDownload.window('open');
                var path = dataObj.file_path;
                _fileName = dataObj.file_name;
                downloadFile.attr("href", path);
            })
            .fail(function (errorObj) {});
    }

    if (report_filter == 'report-subleased-plots') {

        TF.Rpc.Plots.PlotReports.exportToExcelSubleasedPlotsReportData(params, null, null, sort, order)
            .done(function (dataObj) {

                winDownload.window('open');
                var path = dataObj.file_path;
                _fileName = dataObj.file_name;
                downloadFile.attr("href", path);
            })
            .fail(function (errorObj) {});
    }

    if (report_filter == 'report-for-sublease') {

        TF.Rpc.Plots.PlotReports.exportToExcelForSubleasePlotsReportData(params, null, null, sort, order)
            .done(function (dataObj) {

                winDownload.window('open');
                var path = dataObj.file_path;
                _fileName = dataObj.file_name;
                downloadFile.attr("href", path);
            })
            .fail(function (errorObj) {});
    }

    if (report_filter == 'report-hypothecs') {

        TF.Rpc.Plots.PlotReports.exportToExcelHypothecsPlotsReportData(params, null, null, sort, order)
            .done(function (dataObj) {

                winDownload.window('open');
                var path = dataObj.file_path;
                _fileName = dataObj.file_name;
                downloadFile.attr("href", path);
            })
            .fail(function (errorObj) {});
    }

    if (report_filter == 'report-for-hypothec') {

        TF.Rpc.Plots.PlotReports.exportToExcelForHypothecPlotsReportData(params, null, null, sort, order)
            .done(function (dataObj) {

                winDownload.window('open');
                var path = dataObj.file_path;
                _fileName = dataObj.file_name;
                downloadFile.attr("href", path);
            })
            .fail(function (errorObj) {});
    }

    if (report_filter == 'report-rented-plots') {

        TF.Rpc.Plots.PlotReports.exportToExcelRentedPlotsReportData(params, null, null, sort, order)
            .done(function (dataObj) {

                winDownload.window('open');
                var path = dataObj.file_path;
                _fileName = dataObj.file_name;
                downloadFile.attr("href", path);
            })
            .fail(function (errorObj) {});
    }

    if (report_filter == 'report-subleased-rented-plots') {

        TF.Rpc.Plots.PlotReports.exportToExcelSubleasedRentedPlotsReportData(params, null, null, sort, order)
            .done(function (dataObj) {

                winDownload.window('open');
                var path = dataObj.file_path;
                _fileName = dataObj.file_name;
                downloadFile.attr("href", path);
            })
            .fail(function (errorObj) {});
    }

    if (report_filter == 'report-rented-expiring-contracts') {

        TF.Rpc.Plots.PlotReports.exportToExcelExpiringContractsReportData(params, null, null, sort, order)
            .done(function (dataObj) {

                winDownload.window('open');
                var path = dataObj.file_path;
                _fileName = dataObj.file_name;
                downloadFile.attr("href", path);
            })
            .fail(function (errorObj) {});
    }

    if (report_filter == 'report-plots-in-many-contracts') {

        TF.Rpc.Plots.PlotReports.exportToExcelPlotsInManyContractsReportData(params, null, null, sort, order)
            .done(function (dataObj) {

                winDownload.window('open');
                var path = dataObj.file_path;
                _fileName = dataObj.file_name;
                downloadFile.attr("href", path);
            })
            .fail(function (errorObj) {});
    }

    if (report_filter == 'report-used-plots') {

        TF.Rpc.Plots.PlotReports.exportToExcelUsedPlotsReportData(params, null, null, sort, order)
            .done(function (dataObj) {

                winDownload.window('open');
                var path = dataObj.file_path;
                _fileName = dataObj.file_name;
                downloadFile.attr("href", path);
            })
            .fail(function (errorObj) {});
    }

    if (report_filter == 'report-contracts-with-ownerless-plots') {

        TF.Rpc.Plots.PlotReports.exportContractsWithOwnerlessPlots(params, null, null, sort, order)
            .done(function (dataObj) {

                winDownload.window('open');
                var path = dataObj.file_path;
                _fileName = dataObj.file_name;
                downloadFile.attr("href", path);
            })
            .fail(function (errorObj) {});
    }

    if (report_filter == 'report-historical-plots') {

        TF.Rpc.Plots.PlotReports.exportToExcelHistoricalPlotsReportData(params, null, null, sort, order)
            .done(function (dataObj) {

                winDownload.window('open');
                var path = dataObj.file_path;
                _fileName = dataObj.file_name;
                downloadFile.attr("href", path);
            })
            .fail(function (errorObj) {});
    }
    if (report_filter == 'report-total-area') {
        var params = jQuery('#report-tables').datagrid('options').rpcParams[0];
        sort = jQuery('#report-tables').datagrid('options').sortName;
        order = jQuery('#report-tables').datagrid('options').sortOrder;
        TF.Rpc.Plots.PlotReports.exportToExcelPlotsReportData(params, null, null, sort, order)
            .done(function (dataObj) {

                winDownload.window('open');
                var path = dataObj.file_path;
                _fileName = dataObj.file_name;
                downloadFile.attr("href", path);
            })
            .fail(function (errorObj) {});
    }
}

jQuery (function (){
    jQuery('#export-report').bind('click', function() {
        exportToExcelReport();
    });

    jQuery('#include-subleased-in-report').bind('click', function() {
        jQuery('#btn-report-filter').trigger('click');
    });
});

function exportOwnPlotsDetailedToExcel()
{
    var winDownload = jQuery('#win-download').window({
    });

    var data = jQuery('#plots-report-tables-detail').datagrid('getData');

    if (data.rows.length === 0) {
        return jQuery.messager.alert('Грешка', 'Не може да експортнете празна справка.', 'error');
    }

    var gridOptions = jQuery('#plots-report-tables-detail').datagrid('options');
    var downloadFile = jQuery('#btn-download-file');
    var params = gridOptions.rpcParams[0];

    var sort = gridOptions.sortName;
    var order = gridOptions.sortOrder;

    if(report_filter == 'report-own-plots-detailed') {

        TF.Rpc.Plots.PlotReports.exportToExcelDetailedOwnPlotsReportData(params, null, null, sort, order)
            .done(function (dataObj) {

                winDownload.window('open');
                var path = dataObj.file_path;
                _fileName = dataObj.file_name;
                downloadFile.attr("href", path);
            })
            .fail(function (errorObj) {});
    }
}

function initSearchArendator() {
    var component = jQuery('#choose-arendator'),
        componentTarget = jQuery('#choose-arendator-typehead'),
        completer = new Bloodhound({
            datumTokenizer: Bloodhound.tokenizers.whitespace('value'),
            queryTokenizer: Bloodhound.tokenizers.whitespace,
            remote: {
                url: 'index.php?owners-rpc=owners-list',
                cache: false,
                prepare: function(query, settings) {
                    settings.query = query;
                    return settings;
                },
                transport: function(settings, onSuccess, onError) {
                    var options = {
                        type: 'POST',
                        dataType: 'json',
                        contentType: "application/json; charset=UTF-8",
                        data: JSON.stringify({
                            "method": "read",
                            "params": ['both', settings.query],
                            "id": 1,
                            "jsonrpc": "2.0"
                        })
                    };

                    jQuery.ajax('index.php?owners-rpc=owners-list', options)
                        .done(done)
                        .fail(fail)
                        .always(always);

                    function done(data, textStatus, request) {
                        onSuccess(data.result);
                    }

                    function fail(request, textStatus, errorThrown) {
                        onError(errorThrown);
                    }

                    function always() {}
                }
            }
        });

    component.typeahead({
        hint: false,
        highlight: false,
        minLength: 1,
        limit: 10,
        menu: componentTarget
    }, {
        name: 'completer',
        displayKey: 'value',
        source: completer.ttAdapter(),
    });

    component.on('typeahead:open', function (e, datum) {
        componentTarget.width(component.width());
        var offset = component.offset();
        componentTarget.offset({
            top: offset.top + component.height(),
            left: offset.left
        });
        componentTarget.css("z-index",9999);
    });

    component.on('typeahead:selected', function (e, datum) {
        component.typeahead('close');
        componentTarget.css("z-index",-1);
    });
    component.on('typeahead:idle', function (e, datum) {
        component.typeahead('close');
        componentTarget.css("z-index",-1);
    });
}
