Namespace('TF.Rpc.Contracts');

var maxOwnership = 100;
var maxOwnershipArea = 0;
var selectedPlotID = false;
var selectedContractID = false;
var isHeritorSelected = false;
var CONTRACT_TYPE_OWN = 1;
var addingNewHeritor = false;
var isEditing = false;
function initPlotsOwnersGrid(plot_id, contract_id, c_type, is_sublease) {

    selectedPlotID = plot_id;
    selectedContractID = contract_id;

	if(c_type == 1) {
		var title = 'Предишни собственици';
	} else {
		var title = 'Собственици';
	}

    jQuery('#owner-info-tables').treegrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        iconCls: 'icon-owners',
        pageSize: 10,
        singleSelect: true,
        fitColumns: true,
        showFooter: true,
		fit: true,
        url: 'index.php?contracts-rpc=contracts-owners-datagrid',
        rpcParams:[{
            plot_id: selectedPlotID,
            contract_id: selectedContractID }],
        sortName: 'name',
        sortOrder: 'desc',
        idField: 'fakeid',
		treeField: 'owner_names',
		border: false,
        columns: [[
				{
                    field: 'owner_names',
                    title: '<b>Собственик</b>',
                    sortable: true,
                    width: 150,
					styler: function(value, row, index) {
						if (!row.is_heritor) {
							return 'font-weight: bold;';
						}
					}
                }, {
                    field: 'rep_names',
                    title: '<b>Представител</b>',
                    sortable: true,
                    width: 150
                }, {
                    field: 'rat_ownage',
                    title: '<b>Собственост(%)</b>',
                    sortable: true,
                    width: 75,
                    formatter: function(value, row){
                        var percent;

                        if(row.numerator && row.denominator) {
                            var percent = new Fraction(row.numerator / row.denominator * 100);
                            var fraction = row.numerator + '/' + row.denominator;
                            value = percent.toString() + '% (' +  fraction + ')';

                            return value;
                        }

                        var fraction = new Fraction(row.percent / 100);
                        var percent = fraction.mul(100);
                        value = percent.toString() + '% (' +  fraction.simplify().toFraction() + ')';

                        return value;
                    }
                }
            ]],
        toolbar: '#contracts-owners-toolbar',
        pagination: false,
        rownumbers: true,
        onBeforeLoad: function() {
            jQuery('#owner-info-tables').treegrid('clearChecked');
        },
		onLoadSuccess: function(row, data) {
			if(data.length == 0)
			{
				if(jQuery('#plots-farming-tables').datagrid('getData').rows.length != 0)
				{
					jQuery('#owners-tabs').tabs('select', 1);
                    jQuery('#owner-info-tables').datagrid('select',0);
				}else {
					jQuery('#owners-tabs').tabs('select', 0);
                    jQuery('#owner-info-tables').datagrid('select',0);
				}
			}
			else
			{
				jQuery('#owners-tabs').tabs('select', 0);
			}

			var sumFr = getTotalOwnersShares();

            if (sumFr.valueOf() < 1) {
                jQuery('#ownership-warning').show();
                return;
            }

            jQuery('#ownership-warning').hide();
		},
		onSelect: function(node) {
			if(node.is_heritor) {
				jQuery('#btnaddcontractowner').linkbutton('disable');
				jQuery('#btndeletecontractowner').linkbutton('disable');
				isHeritorSelected = true;
			}
			else {
				jQuery('#btnaddcontractowner').linkbutton('enable');
				jQuery('#btndeletecontractowner').linkbutton('enable');
				isHeritorSelected = false;
			}
		},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    if(is_sublease)
    {
        jQuery('#btnaddcontractowner').linkbutton('disable');
        jQuery('#btneditownage').linkbutton('disable');
        jQuery('#btndeletecontractowner').linkbutton('disable');
    }
    else
    {
        jQuery('#btnaddcontractowner').linkbutton('enable');
        jQuery('#btneditownage').linkbutton('enable');
        jQuery('#btndeletecontractowner').linkbutton('enable');
    }
}

function initAddOwnersGrid(contract_id, plot_id) {

    jQuery('#owners-add-tables').datagrid({
        iconCls: 'icon-users',
        nowrap: true,
        title: 'Собственици',
        autoRowHeight: true,
        striped: true,
        pageSize: 10,
        fit: true,
        singleSelect: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?contracts-rpc=contracts-owners-datagrid',
        rpcMethod: 'add',
        rpcParams:[{
            plot_id: selectedPlotID,
            contract_id: selectedContractID }],
        sortName: 'id',
        sortOrder: 'desc',
        idField: 'id',
        frozenColumns: [[
                {
                    field: 'ck',
                    checkbox: true
                }
            ]],
        columns: [[
                {
                    field: 'owner_names',
                    title: '<b>Име на собственик</b>',
                    sortable: true,
                    width: 150
                }, {
                    field: 'egn_eik',
                    title: '<b>ЕГН/EIK</b>',
                    sortable: true,
                    width: 150
                }
            ]],
        pagination: true,
        rownumbers: true,
        toolbar: [{
                id: 'btnaddnewcontractowner',
                text: 'Нов собственик',
                iconCls: 'icon-add',
                handler: function() {

                    clearAddOwnerInputDataFields();
                    setAddEditFieldsValidators();
                    onAddownerPanelOpen();
                    displayTables('physical');
                    lastAddedOwner = 0;
                    lastAddedParent = 0;
                    jQuery('#win-add-new-owner').window('open');
                }
            }, {
                id: 'btnaddfilter',
                text: 'Филтриране',
                iconCls: 'icon-filter',
                handler: function() {
                    jQuery('#win-add-owner-filter').window('open');
                }
            }, {
                id: 'btnremovefilter',
                text: 'Покажи всички',
                iconCls: 'icon-clear-filter',
                handler: function() {
                    cleanAddOwnersFilter();
                }
            }, {
                id: 'btninfoowner',
                text: 'Информация',
                iconCls: 'icon-info',
                handler: function() {
                    var getChecked = jQuery('#owners-add-tables').datagrid('getChecked');
                    if (getChecked[0]) {
                        //displayOwnerInfo(getChecked[0]['id']);

						window.open("index.php?page=Owners.Home&owner_id=" + getChecked[0].id, '_blank');
                    } else {
                        jQuery.messager.alert('Грешка', 'Моля изберете потребител, за който да бъде показана информация.');
                    }
                }
            }],
        onBeforeLoad: function() {
            jQuery('#owners-add-tables').datagrid('clearChecked');
        },
        onLoadSuccess: function() {
            initContractOwnerDataFields();
        },
        onSelect: function(rowIndex, rowData) {
            reinitContractOwnerDataFields(rowData);

			if(rowData.is_dead)
			{
				jQuery.messager.alert('Предупреждение', 'Тъй като избраният собственик е обявен като починал, ще се вземат предвид въведените му наследници.');
			}
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initContractOwnerDataFields() {
    var date = new Date();
    var todayDate = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();

    jQuery('#ownage-numerator > input').numberbox({
        value: '',
        min: 0,
        decimalSeparator: ''
    });
    jQuery('#ownage-denominator > input').numberbox({
        value: '',
        min: 1,
        decimalSeparator: ''
    });
    jQuery('#contract-ownage-percent-field > input').numberbox({
        value: '',
        min: 0,
        max: 100,
        precision: 2
    });
    jQuery('#contract-ownage-area-field > input').numberbox({
        value: '',
        min: 0,
        precision: 3
    });

	jQuery('#contract-rep-proxy-num > input').val('');

    jQuery('#contract-rep-proxy-date > input').datebox({
        value: todayDate
    });

    jQuery('#contract-ownage-fraction > input').change(function() {
        jQuery('#contract-ownage-percent-field').hide();
        jQuery('#contract-ownage-area-field').hide();
        jQuery('#contract-ownage-fraction-fields').show();
    });
    jQuery('#contract-ownage-percent > input').change(function() {
        jQuery('#contract-ownage-area-field').hide();
        jQuery('#contract-ownage-fraction-fields').hide();
        jQuery('#contract-ownage-percent-field').show();
    });
    jQuery('#contract-ownage-area > input').change(function() {
        jQuery('#contract-ownage-fraction-fields').hide();
        jQuery('#contract-ownage-percent-field').hide();
        jQuery('#contract-ownage-area-field').show();
    });

    jQuery('#contract-owner-rep-by-another > input').change(function() {
        jQuery('#contract-rep-proxy-message').hide();
        jQuery('#contract-rep-proxy-fields').show();
    });

    jQuery('#contract-owner-rep-by-himself > input').change(function() {
        jQuery('#contract-rep-proxy-fields').hide();
        jQuery('#contract-rep-proxy-message').show();
    });

    jQuery('#contract-owner-rep-by-himself > input').prop('checked', true);
    //display proxy fields
    jQuery('#contract-rep-proxy-message').show();
    jQuery('#contract-rep-proxy-fields').hide();

	if(contragent_type == 'farming')
	{
		jQuery('#contract-owner-rep-by-himself > input').attr('disabled',true);
	}
	else
	{
		jQuery('#contract-owner-rep-by-himself > input').removeAttr('disabled');
	}

    jQuery('#contract-rep-notary-num > input').numberbox({
        filter: function(e) {
            var isNumber = e.which > 47 && e.which < 58;
            return isNumber;
        }
    });
}

//used when new owner is selected
function reinitContractOwnerDataFields(ownerData) {
    var owner_id = ownerData.id;

    jQuery('#contract-ownage-fraction > input').change(function() {
        jQuery('#contract-ownage-percent-field').hide();
        jQuery('#contract-ownage-area-field').hide();
        jQuery('#contract-ownage-fraction-fields').show();
    });
    jQuery('#contract-ownage-percent > input').change(function() {
        jQuery('#contract-ownage-area-field').hide();
        jQuery('#contract-ownage-fraction-fields').hide();
        jQuery('#contract-ownage-percent-field').show();
    });
    jQuery('#contract-ownage-area > input').change(function() {
        jQuery('#contract-ownage-fraction-fields').hide();
        jQuery('#contract-ownage-percent-field').hide();
        jQuery('#contract-ownage-area-field').show();
    });

    jQuery('#contract-owner-rep-by-another > input').change(function() {
        jQuery('#contract-rep-proxy-message').hide();
        jQuery('#contract-rep-proxy-fields').show();
    });

	jQuery('#contract-owner-rep-by-himself > input').unbind();
    jQuery('#contract-owner-rep-by-himself > input').change(function() {
        //if owner is company then should not be allowed to represent himself
        if (jQuery('#contract-owner-rep-by-himself > input').is(':checked')) {
            if (ownerData.owner_type == 0) {
                jQuery('#contract-owner-rep-by-another > input').prop('checked', true);
                jQuery('#contract-rep-proxy-message').hide();
                jQuery('#contract-rep-proxy-fields').show();

                jQuery.messager.alert('Грешка', 'Само физически лица могат да се представляват сами.');
            } else {
                jQuery('#contract-rep-proxy-fields').hide();
                jQuery('#contract-rep-proxy-message').show();
            }
        }
    });

    //if owner is company check represented by another
    if (ownerData.owner_type == 0) {
        jQuery('#contract-owner-rep-by-another > input').prop('checked', true);
        jQuery('#contract-rep-proxy-message').hide();
        jQuery('#contract-rep-proxy-fields').show();
    } else if(ownerData.owner_type == 1) {
        jQuery('#contract-owner-rep-by-himself > input').prop('checked', true);
        jQuery('#contract-rep-proxy-message').show();
        jQuery('#contract-rep-proxy-fields').hide();
    }
}

function initEditOwnageDataFields() {
	jQuery('#edit-ownage-numerator > input').numberbox({
		value: '',
		min: 0,
        decimalSeparator: ''
	});
	jQuery('#edit-ownage-denominator > input').numberbox({
		value: '',
		min: 1,
        decimalSeparator: ''
	});
	jQuery('#edit-ownage-percent-field > input').numberbox({
		value: '',
		min: 0,
		max: 100,
		precision: 2
	});
	jQuery('#edit-ownage-area-field > input').numberbox({
		value: '',
		min: 0,
		precision: 3
	});
	jQuery('#edit-ownage-fraction > input').change(function() {
		jQuery('#edit-ownage-percent-field').hide();
		jQuery('#edit-ownage-area-field').hide();
		jQuery('#edit-ownage-fraction-fields').show();
	});
	jQuery('#edit-ownage-percent > input').change(function() {
		jQuery('#edit-ownage-area-field').hide();
		jQuery('#edit-ownage-fraction-fields').hide();
		jQuery('#edit-ownage-percent-field').show();
	});
	jQuery('#edit-ownage-area > input').change(function() {
		jQuery('#edit-ownage-fraction-fields').hide();
		jQuery('#edit-ownage-percent-field').hide();
		jQuery('#edit-ownage-area-field').show();
	});
}

//function validates if all data is correct and if it is add record
function validateNewPCToOwnerRel() {
    var obj = new Object();

    //check if information is correct
    var ownerData = jQuery('#owners-add-tables').datagrid('getChecked');
    var plotData = jQuery('#plots-tree').tree('getSelected').attributes;
	var contractData = jQuery('#contract-info-tables').datagrid('getChecked');
    //if owner is not selected
    if (ownerData[0] == undefined) {
        jQuery.messager.alert('Грешка', 'Не е избран собственик');
        return false;
    }

    var percent = 0;
    var shares = new Fraction();

    if (jQuery('#contract-ownage-fraction > input').is(':checked'))
    {
        if (jQuery('#ownage-numerator > input').val() == ''
                || jQuery('#ownage-denominator > input').val() == '')
        {
            jQuery.messager.alert('Грешка', 'Не сте въвели данни за собственост.');
			return false;
        } else {
            var numerator = jQuery('#ownage-numerator > input').val();
            var denominator = jQuery('#ownage-denominator > input').val();
            percent = (numerator / denominator) * 100;

            shares = new Fraction(numerator/denominator);
        }
    }

    if (jQuery('#contract-ownage-percent > input').is(':checked'))
    {
        if (jQuery('#contract-ownage-percent-field > input').val() == '')
        {
            jQuery.messager.alert('Грешка', 'Не сте въвели данни за собственост.');
			return false;
        } else {
            percent = parseFloat(jQuery('#contract-ownage-percent-field > input').val());

            shares = new Fraction(percent/100);
        }
    }

    if (jQuery('#contract-ownage-area > input').is(':checked'))
    {
        if (jQuery('#contract-ownage-area-field > input').val() == '')
        {
            jQuery.messager.alert('Грешка', 'Не сте въвели данни за собственост.');
			return false;
        } else {
            var ownershipArea = parseFloat(jQuery('#contract-ownage-area-field > input').val());

            percent = (ownershipArea / contractData[0].contract_area) * 100;

            shares = new Fraction(ownershipArea / contractData[0].contract_area);
        }
    }

    if(maxOwnership.valueOf() < 0) {
        maxOwnership = new Fraction(0);
    }

    if(shares.compare(maxOwnership) > 0) {
        jQuery.messager.alert('Грешка', 'Въвели сте некоректни данни за собственост.');
		return false;
    }

    //check if representative is chosen
    if (jQuery('#contract-owner-rep-by-another > input').is(':checked'))
    {
        var repData = jQuery('#contract-owners-reps-tables').datagrid('getChecked');

        if (repData[0] == undefined) {
            jQuery.messager.alert('Грешка', 'Не е избран представител');
            return false;
        } else if (repData[0].id == false) {
            jQuery.messager.alert('Грешка', 'Не можете да добавяте представител, преди да го запазите.');
            return false;
        }

        //self rep hold if owner represents himself or is represented by another
        obj.self_rep = false;
        obj.rep_id = repData[0].id;
        obj.proxy_num = jQuery('#contract-rep-proxy-num > input').val() || null;
        obj.proxy_date = jQuery('#contract-rep-proxy-date > input').datebox('getValue') || null;
        obj.notary_name = jQuery('#contract-rep-notary-name > input').val() || null;
        obj.notary_number = jQuery('#contract-rep-notary-num > input').numberbox('getValue') || null;
        obj.notary_address = jQuery('#contract-rep-notary-address > input').val() || null;
    }

    if (jQuery('#contract-owner-rep-by-himself > input').is(':checked'))
    {
        obj.self_rep = true;
        obj.rep_id = 0;
        obj.proxy_num = null;
        obj.proxy_date = null;
        obj.notary_name = null;
        obj.notary_number = null;
        obj.notary_address = null;
    }

    if(contragent_type == 'owner')
    {
        obj.owner_id = ownerData[0].id;
    }
    else if(contragent_type == 'farming')
    {
        obj.farming_id = ownerData[0].id;
    }
    obj.contract_id = contractData[0].id;
    obj.plot_id = plotData.gid;
    obj.percent = percent;
    obj.numerator = numerator;
    obj.denominator = denominator;

    TF.Rpc.Contracts.ConctractOwnerData.addNewPlotOwner(obj)
    .done(function (data) {
        closeOwnerAddWindows();
    })
    .fail(function (data) {
        //No Rights to operate with "Договори за собственост"
        if(data.contractType == CONTRACT_TYPE_OWN && !data.hasContractsOwnWriteRights) {
            messagerContractsOwnWriteRights();
            return false;
        }
        if (typeof data === "Exception") {
            if (data.is(TF.Rpc.ExceptionsList.NON_EXISTING_OWNER_ID)) {
                jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.NON_EXISTING_OWNER_ID.message,'warning');
            } else if (data.is(TF.Rpc.ExceptionsList.NON_EXISTING_CONTRACT_PLOT_RELATION)) {
                jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.NON_EXISTING_CONTRACT_PLOT_RELATION.message, 'warning');
            } else if (data.is(TF.Rpc.ExceptionsList.INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS)) {
                jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS.message, 'warning');
            }
        };
    });

    jQuery('#owners-add-tables').datagrid('uncheckAll');
    jQuery('#owners-add-tables').datagrid('unselectAll');

    jQuery('#owner-info-tables').treegrid('uncheckAll')
    jQuery('#owner-info-tables').treegrid('unselectAll')
}

function validateEditPCToOwnerRel() {
	var obj = new Object();

	var contractData = jQuery('#contract-info-tables').datagrid('getChecked');
	var plotData = jQuery('#plots-tree').tree('getSelected').attributes;

	var percent = 0;
    var shares = new Fraction();

	if (jQuery('#edit-ownage-fraction > input').is(':checked'))
	{
		if (jQuery('#edit-ownage-numerator > input').val() == ''
				|| jQuery('#edit-ownage-denominator > input').val() == '')
		{
			jQuery.messager.alert('Грешка', 'Не сте въвели данни за собственост.');
			return false;
		} else {
			var numerator = jQuery('#edit-ownage-numerator > input').val();
			var denominator = jQuery('#edit-ownage-denominator > input').val();
			percent = (numerator / denominator) * 100;

            shares = new Fraction(numerator / denominator);
		}
	}

	if (jQuery('#edit-ownage-percent > input').is(':checked'))
	{
		if (jQuery('#edit-ownage-percent-field > input').val() == '')
		{
			jQuery.messager.alert('Грешка', 'Не сте въвели данни за собственост.');
			return false;
		} else {
			percent = parseFloat(jQuery('#edit-ownage-percent-field > input').val());

            shares = new Fraction(percent / 100);
		}
	}

	if (jQuery('#edit-ownage-area > input').is(':checked'))
	{
		if (jQuery('#edit-ownage-area-field > input').val() == '')
		{
			jQuery.messager.alert('Грешка', 'Не сте въвели данни за собственост.');
			return false;
		} else {
			var ownershipArea = parseFloat(jQuery('#edit-ownage-area-field > input').val());

            percent = (ownershipArea / contractData[0].contract_area) * 100;

            shares = new Fraction(ownershipArea / contractData[0].contract_area);
		}
	}

    if(maxOwnership.valueOf() < 0) {
        maxOwnership = new Fraction(0);
    }

	if(shares.compare(maxOwnership) > 0) {
        jQuery.messager.alert('Грешка', 'Въвели сте некоректни данни за собственост.');
		return false;
    }

    if(contragent_type == 'owner' && isHeritorSelected)
    {
        var heritorData = jQuery('#owner-info-tables').treegrid('getSelected');

        obj.contract_id = contractData[0].id;
        obj.owner_id = heritorData.owner_id;
        obj.plot_id = plotData.gid;
        obj.percent = percent;
        obj.numerator = numerator;
        obj.denominator = denominator;
        obj.path = heritorData.path;
        obj.is_heritor = isHeritorSelected;
    }
    else if(contragent_type == 'owner')
    {
        var po_id = jQuery('#owner-info-tables').treegrid('getSelected').id;

        obj.po_id = po_id;
        obj.percent = percent;
    }
    else if(contragent_type == 'farming')
    {
        var pf_id = jQuery('#plots-farming-tables').datagrid('getSelected').id;

        obj.pf_id = pf_id;
        obj.percent = percent;
    }

    TF.Rpc.Contracts.ConctractOwnerData.editOwnerPercent(obj)
    .done(function (data) {
        closeOwnerAddWindows();
    })
    .fail(function (errorObj) {
        if (errorObj.is(TF.Rpc.ExceptionsList.NON_EXISTING_CONTRACT_PLOT_RELATION)) {
            jQuery.messager.alert('Грешка',TF.Rpc.ExceptionsList.NON_EXISTING_CONTRACT_PLOT_RELATION.message,'warning');
        } else if (errorObj.is(TF.Rpc.ExceptionsList.INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS)) {
            jQuery.messager.alert('Грешка',TF.Rpc.ExceptionsList.INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS.message,'warning');
        }
    });

    jQuery('#owner-info-tables').treegrid('uncheckAll');
    jQuery('#owner-info-tables').treegrid('unselectAll');
}

function closeOwnerAddWindows() {
	jQuery('#win-plot-owner-add').window('close');
    jQuery('#win-edit-contract-owner-data').window('close');

    jQuery('#plots-farming-tables').datagrid('loadRpc');
    jQuery('#owner-info-tables').datagrid('loadRpc');
}

function displayTables(owner_type) {
	if (owner_type == 'physical') {
		jQuery('#company-data').hide();
		jQuery('#owner-data').show();
		jQuery('#is-dead').show();
		jQuery('#is-foreigner').show();
	}
	if (owner_type == 'legal') {
		jQuery('#owner-data').hide();
		jQuery('#is-dead').hide();
		jQuery('#is-foreigner').hide();
		jQuery('#company-data').show();
	}
}

function setAddOwnerFieldsValidators() {
    numLengthbox();
    jQuery('#name > input').validatebox({
        required: true,
        missingMessage: 'Моля въведете име на собственик.'
    });
    jQuery('#surname > input').validatebox({
        required: true,
        missingMessage: 'Моля въведете презиме на собственик.'
    });
    jQuery('#lastname > input').validatebox({
        required: true,
        missingMessage: 'Моля въведете фамилия на собственик.'
    });

    new EgnValidateBox('#egn > input');

	jQuery('#company_name > input').validatebox({
		required: true,
		missingMessage: 'Моля въведете име на фирма.'
	});
	jQuery('#eik > input').numberbox({
		required: true,
		validType: 'setLength[9]',
		missingMessage: 'Моля въведете ЕИК номер.',
        parser: function(s){
            return s;
        }
	});
}

function validateNewOwnerSubmitInfo() {
	if (jQuery('#is_physical input').is(':checked')) {
		if (jQuery('#name > input').validatebox('isValid')
				&& jQuery('#surname > input').validatebox('isValid')
				&& jQuery('#lastname > input').validatebox('isValid')
                && new EgnValidateBox('#egn > input').isValid()) {
			return true;
		} else {
			jQuery.messager.alert('Грешка', 'Моля попълнете задължителните полета.');
			return false;
		}
	} else {
		if (jQuery('#company_name > input').validatebox('isValid')
				&& jQuery('#eik > input').numberbox('isValid')) {
			return true;
		} else {
			jQuery.messager.alert('Грешка', 'Моля попълнете задължителните полета.');
			return false;
		}
	}
}

function displayOwnerInfo(owner_id) {
    EasyUIRPCLoaders.rpcRequest('owner-info', 'read', owner_id).done( function (data) {
        if (typeof data.error == 'undefined') {
            var responseText = data.result;
            if (responseText.owner_type_id == 0) {
                jQuery('#info-panel').window('resize', {
                    width: 400,
                    height: 290
                });

                var html = '<fieldset style="border: 1px solid #000; padding: 5px 10px;">' +
                '<legend style="font-style: italic; font-weight: bold">Информация за собственик</legend>' +
                'Вид собственик: ' + responseText.owner_type + '<br/>' +
                'Фирма: ' + responseText.company_name + '<br/>' +
                'ЕИК: ' + responseText.eik + '<br/>' +
                'МОЛ: ' + responseText.mol + '<br/>' +
                'Адрес: ' + responseText.company_address + '<br/>' +
                '</fieldset>';
            } else {
                jQuery('#info-panel').window('resize', {
                    width: 400,
                    height: 310
                });

                var html = '<fieldset style="border: 1px solid #000; padding: 5px 10px;">' +
                '<legend style="font-style: italic; font-weight: bold">Информация за собственик</legend>' +
                'Вид собственик: ' + responseText.owner_type + '<br/>' +
                'Имена: ' + responseText.name + ' ' + responseText.surname + ' ' + responseText.lastname + '<br/>' +
                'ЕГН: ' + responseText.egn + '<br/>' +
                'Номер на лична карта: ' + responseText.lk_nomer + '<br/>' +
                'Издаване на ЛК: ' + responseText.lk_izdavane + '<br/>' +
                'Починал: ' + responseText.is_dead + '<br/>' +
                '</fieldset>';
            }

            html += '<fieldset style="border: 1px solid #000; padding: 5px 10px; margin-top: 10px;">' +
            '<legend style="font-style: italic; font-weight: bold">Допълнителна информация за собственик</legend>' +
            'Телефон: ' + responseText.phone + '<br/>' +
            'Факс: ' + responseText.fax + '<br/>' +
            'Мобилен телефон: ' + responseText.mobile + '<br/>' +
            'Емейл: ' + responseText.email + '<br/>' +
            'Адрес: ' + responseText.address +
            '</fieldset>';

            jQuery('#info-panel').window('open');
            jQuery('#info-panel').html(html);
    }
    else {
        jQuery.messager.alert('Грешка', 'Не е намерен собственик.');
        }
    });
}

function displayPlotOwnerInfo(data) {
	if (data.owner_type == 0) {
		var html = '<fieldset style="border: 1px solid #000; padding: 5px 10px;">' +
				'<legend style="font-style: italic; font-weight: bold">Информация за собственик</legend>' +
				'Вид собственик: Юридическо лице<br/>' +
				'Фирма: ' + data.company_name + '<br/>' +
				'ЕИК: ' + data.egn_eik + '<br/>' +
				'МОЛ: ' + data.mol + '<br/>' +
				'Адрес: ' + data.company_address + '<br/>' +
				'</fieldset>';
	} else {
		var html = '<fieldset style="border: 1px solid #000; padding: 5px 10px;">' +
				'<legend style="font-style: italic; font-weight: bold">Информация за собственик</legend>' +
				'Вид собственик: Физическо лице<br/>' +
				'Имена: ' + data.owner_names + '<br/>' +
				'ЕГН: ' + data.egn_eik + '<br/>' +
				'Номер на лична карта: ' + data.lk_nomer + '<br/>' +
				'Издаване на ЛК: ' + data.lk_izdavane + '<br/>' +
				'</fieldset>';
	}
	html += '<fieldset style="border: 1px solid #000; padding: 5px 10px; margin-top: 5px;">' +
			'<legend style="font-style: italic; font-weight: bold">Допълнителна информация за собственик</legend>' +
			'Телефон: ' + data.phone + '<br/>' +
			'Факс: ' + data.fax + '<br/>' +
			'Мобилен телефон: ' + data.mobile + '<br/>' +
			'Емейл: ' + data.email + '<br/>' +
			'Адрес: ' + data.address +
			'</fieldset>';
	html += '<fieldset style="border: 1px solid #000; padding: 5px 10px; margin-top: 5px;">' +
			'<legend style="font-style: italic; font-weight: bold">Документ за собственост</legend>' +
			'Тип: ' + data.document_type + '<br/>' +
			'Номер: ' + data.document_number + '<br/>' +
			'Дата: ' + data.document_date + '<br/>' +
			'</fieldset>';
	if (data.self_rep == data.owner_id) {
		jQuery('#info-panel').window('resize', {
			width: 400,
			height: 430
		});
		html += '<fieldset style="border: 1px solid #000; padding: 5px 10px; margin-top: 5px;">' +
				'<legend style="font-style: italic; font-weight: bold">Представлява се</legend>' +
				'Лично<br/>' +
				'</fieldset>';
	} else {
		jQuery('#info-panel').window('resize', {
			width: 400,
			height: 535
		});
		html += '<fieldset style="border: 1px solid #000; padding: 5px 10px; margin-top: 5px;">' +
				'<legend style="font-style: italic; font-weight: bold">Представлява се от</legend>' +
				'Имена: ' + data.rep_names + '<br/>' +
				'ЕГН: ' + data.rep_egn + '<br/>' +
				'Номер на лична карта: ' + data.rep_lk + '<br/>' +
				'Издаване на ЛК: ' + data.rep_lk_izdavane + '<br/>' +
				'Адрес: ' + data.rep_address + '<br/>' +
				'Номер на пълномощно: ' + data.proxy_num + '<br/>' +
				'Дата на пълномощно: ' + data.proxy_date + '<br/>' +
				'</fieldset>';
	}
	jQuery('#info-panel').html(html);
	jQuery('#info-panel').window('open');
}

function displayOwnerHeritorInfo(owner_id) {
    EasyUIRPCLoaders.rpcRequest('owner-info', 'read', owner_id).done( function (data) {
        if (typeof data.error == 'undefined') {
            jQuery('#info-panel').window('open');
            var responseText = data.result;

			jQuery('#info-panel').window('resize', {
				width: 420,
				height: 330
			});
			var html = '<fieldset style="border: 1px solid #000; padding: 5px 10px;">' +
					'<legend style="font-style: italic; font-weight: bold">Информация за собственик</legend>' +
					'Вид собственик: ' + responseText.owner_type + '<br/>' +
					'Имена: ' + responseText.name + ' ' + responseText.surname + ' ' + responseText.lastname + '<br/>' +
					'ЕГН: ' + responseText.egn + '<br/>' +
					'Номер на лична карта: ' + responseText.lk_nomer + '<br/>' +
					'Издаване на ЛК: ' + responseText.lk_izdavane + '<br/>' +
					'Починал: ' + responseText.is_dead + '<br/>' +
					'</fieldset>' +
					'<fieldset style="border: 1px solid #000; padding: 5px 10px; margin-top: 10px;">' +
                    '<legend style="font-style: italic; font-weight: bold">Допълнителна информация за собственик</legend>' +
                    'Телефон: ' + responseText.phone + '<br/>' +
                    'Факс: ' + responseText.fax + '<br/>' +
                    'Мобилен телефон: ' + responseText.mobile + '<br/>' +
                    'Емейл: ' + responseText.email + '<br/>' +
                    'Адрес: ' + responseText.address +
                    '</fieldset>';
            jQuery('#info-panel').html(html);
        }
        else{
            jQuery.messager.alert('Грешка', 'Не е намерен собственик.');
        }
    });
}

function filterAddOwner() {
    var selectedContract = jQuery('#contract-info-tables').datagrid('getSelected');
    var selectedPlot =  jQuery('#plots-tree').tree('getSelected');
    if (jQuery('#search-owner').val() != ''
            || jQuery('#search-egn').val() != ''
            || jQuery('#search-eik').val() != ''
            || jQuery('#search-company').val() != '') {
        jQuery('#owners-add-tables').datagrid({
            rpcParams: [{
                            plot_id: selectedPlot.id,
                            contract_id: selectedContract.id,
                            owner_names: jQuery('#search-owner').val(),
                            egn: jQuery('#search-egn').val(),
                            eik: jQuery('#search-eik').val(),
                            company_name: jQuery('#search-company').val()
                        }]
        });
        jQuery('#win-add-owner-filter').window('close');
    } else {
        jQuery.messager.alert('Грешка', 'Моля задайте данни за търсене.');
    }
}

function cleanAddOwnersFilter() {
    var selectedContract = jQuery('#contract-info-tables').datagrid('getSelected');
    var selectedPlot =  jQuery('#plots-tree').tree('getSelected');
	jQuery('#search-owner').val('');
    jQuery('#search-egn').val('');
    jQuery('#search-eik').val('');
    jQuery('#search-company').val('');
    jQuery('#owners-add-tables').datagrid({
        rpcParams: [{
                    plot_id: selectedPlot.id,
                    contract_id: selectedContract.id
                }]
    });
}

function getTotalOwnersShares() {
    var gridData = jQuery('#owner-info-tables').treegrid('getData');
    var sumFr = new Fraction();

    //calculate total ownership
    if (gridData[0]) {
        for (var i = 0; i < gridData.length; i++) {
            var f;
            if(gridData[i].numerator && gridData[i].denominator) {
                f = new Fraction(gridData[i].numerator / gridData[i].denominator);
            }
            else {
                f = new Fraction(gridData[i].percent / 100);
            }

            sumFr = sumFr.add(f);
        }
    }

    return sumFr;
}

jQuery(function() {

    jQuery('#btnsaveowner').bind('click', function() {
        if (validateNewOwnerSubmitInfo())
        {
            var obj = getAddOwnerInputFieldsData();
            if (!isEditing) {

                TF.Rpc.Contracts.ContractsOwnersGrid.addNewOwner(obj)
                    .done(function (response)
                    {
                        if(lastAddedParent == 0) {
                            lastAddedParent = response;
                        }
                        lastAddedOwner = response;

                        if (jQuery('#owners-add-tables').data().hasOwnProperty('datagrid')) {
                            jQuery('#owners-add-tables').datagrid('reload');
                        };

                        addingOwnerHeritor = false;
                        isEditing = false;

                        if (obj.is_dead) {
                            if(lastAddedParent != lastAddedOwner && lastAddedOwner != 0 && lastAddedParent != 0) {
                                TF.Rpc.Owners.OwnersHeritorsTree.addOwnerHeritor(lastAddedOwner, lastAddedParent)
                                    .done(function (data) {

                                    }).
                                fail(function (errorObj) {
                                    jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
                                });
                            }
                            lastAddedParent = response;
                        } else {
                            if(lastAddedParent != lastAddedOwner && lastAddedOwner != 0 && lastAddedParent != 0) {
                                TF.Rpc.Owners.OwnersHeritorsTree.addOwnerHeritor(lastAddedOwner, lastAddedParent)
                                    .done(function (data) {
                                        jQuery('#win-choose-heritor').window('close');
                                        jQuery('#owners-add-tables').datagrid('loadRpc');
                                        jQuery('#owner-info-tables').treegrid('loadRpc');
                                    }).
                                fail(function (errorObj) {
                                    jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
                                });
                            }
                        }
                        if (!obj.is_dead) {
                            lastAddedOwner = 0;
                            lastAddedParent = 0;
                        } else {
                            addOwnerHeritorHelper(lastAddedParent);
                        }
                    })
                    .fail(function (data) {
                        if (data.getCode() == -33310) {
                            jQuery.messager.alert('Грешка', data.getMessage(), 'warning');
                            return false;
                        } else {
                            RpcErrorHandler.show(data);
                            return false;
                        }
                    });
            } else {
                var selectedOwner = jQuery('#owner-info-tables').treegrid('getSelected');
                if(selectedOwner && obj.is_legal === true) {
                    TF.Rpc.Owners.OwnersTree.editLegalOwner(obj,selectedOwner.owner_id)
                        .done(function (data) {
                            jQuery('#win-plot-owner-add').window('close');
                            isEditing = false;
                        })
                        .fail(function (data) {
                            if (data.getCode() == -33310) {
                                jQuery.messager.alert('Грешка', data.getMessage(), 'warning');
                                return false;
                            } else {
                                RpcErrorHandler.show(data);
                                return false;
                            }
                        });
                }else if (selectedOwner && obj.is_legal === false){
                    TF.Rpc.Owners.OwnersTree.editOwner(obj,selectedOwner.owner_id)
                        .done(function (response) {
                            if(lastAddedParent == 0) {
                            lastAddedParent = response;
                        }
                        lastAddedOwner = response;

                        if (jQuery('#owners-add-tables').data().hasOwnProperty('datagrid')) {
                            jQuery('#owners-add-tables').datagrid('reload');
                        };

                        addingOwnerHeritor = false;
                        isEditing = false;

                        if (obj.is_dead) {
                            if(lastAddedParent != lastAddedOwner && lastAddedOwner != 0 && lastAddedParent != 0) {
                                TF.Rpc.Owners.OwnersHeritorsTree.addOwnerHeritor(lastAddedOwner, lastAddedParent)
                                    .done(function (data) {

                                    }).
                                fail(function (errorObj) {
                                    jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
                                });
                            }
                            lastAddedParent = response;
                        } else {
                            if(lastAddedParent != lastAddedOwner && lastAddedOwner != 0 && lastAddedParent != 0) {
                                TF.Rpc.Owners.OwnersHeritorsTree.addOwnerHeritor(lastAddedOwner, lastAddedParent)
                                    .done(function (data) {
                                        jQuery('#win-choose-heritor').window('close');
                                        jQuery('#owners-add-tables').datagrid('loadRpc');
                                        jQuery('#owner-info-tables').treegrid('loadRpc');
                                    }).
                                fail(function (errorObj) {
                                    jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
                                });
                            }
                        }
                        if (!obj.is_dead) {
                            lastAddedOwner = 0;
                            lastAddedParent = 0;
                        } else {
                            addOwnerHeritorHelper(lastAddedParent);
                        }
                    })
                    .fail(function (data) {
                        jQuery.messager.alert('Грешка', data.getMessage(), 'warning');
                    });
                }
            }
        }
        jQuery('#win-add-new-owner').window('close');
    });

    jQuery('#btn-add-new-owner-heritor').bind('click', function () {
        addingNewHeritor = true;
        clearAddOwnerInputDataFields();
        onAddownerPanelOpen();
        jQuery('#win-add-new-owner').window('open');
    });
});

function clearAddOwnerInputDataFields() {
    jQuery('#name > input').val('');
    jQuery('#surname > input').val('');
    jQuery('#lastname > input').val('');
    new CardIdValidateBox('#lk_nomer > input').setValue('');
    new EgnValidateBox('#egn > input').setValue('');
    jQuery('#lk_izdavane > input').val('');
    jQuery('#company_name > input').val('');
    jQuery('#eik > input').val('');
    jQuery('#mol > input').val('');
    jQuery('#company_address > textarea').val('');
    jQuery('#phone > input').val('');
    jQuery('#fax > input').val('');
    jQuery('#mobile > input').val('');
    jQuery('#email > input').val('');
    jQuery('#iban > input').val('');
    jQuery('#address > textarea').val('');
    jQuery('#remark > textarea').val('');
    jQuery('#is_legal > input').prop('checked', false);
    jQuery('#is_physical > input').prop('checked', true);
    jQuery('#is-dead > input').prop('checked', false);
    jQuery('#is-foreigner > input').prop('checked', false);
    jQuery('#rent-place > input').val('');
    jQuery('#prepiska > input').val('');
    jQuery('#prepiska-row').hide();

    setAddOwnerFieldsValidators();
}

function getAddOwnerInputFieldsData() {
    var fieldsData = new Object();
    fieldsData = {
        name: jQuery('#name > input').val(),
        surname: jQuery('#surname > input').val(),
        lastname: jQuery('#lastname > input').val(),
        egn: new EgnValidateBox('#egn > input').getValue(),
        lk_nomer: new CardIdValidateBox('#lk_nomer > input').getValue(),
        lk_izdavane: jQuery('#lk_izdavane > input').val(),
        company_name: jQuery('#company_name > input').val(),
        eik: jQuery('#eik > input').val(),
        mol: jQuery('#mol > input').val(),
        company_address: jQuery('#company_address > textarea').val(),
        phone: jQuery('#phone > input').val(),
        fax: jQuery('#fax > input').val(),
        mobile: jQuery('#mobile > input').val(),
        email: jQuery('#email > input').val(),
        iban: jQuery('#iban > input').val(),
        address: jQuery('#address > textarea').val(),
        remark: jQuery('#remark > textarea').val(),
        is_legal: jQuery('#is_legal > input').is(':checked') ? true : false,
        is_physical: jQuery('#is_physical > input').is(':checked') ? true : false,
        is_dead: jQuery('#IsDead').is(':checked') ? true : false,
        is_foreigner: jQuery('#is_foreigner').is(':checked') ? true : false,
        rent_place: jQuery('#rent-place > input').combobox('getValue'),
        prepiska: jQuery('#IsDead').is(':checked') ? jQuery('#prepiska > input').val() : null,
        dead_date: jQuery('#IsDead').is(':checked') ? jQuery('#dead-date > input').datebox('getValue') : null
    }
    return fieldsData;
}

function onAddownerPanelOpen() {
    var rentPlaceInput = jQuery('#rent-place input');
    var rentPlaceValue = rentPlaceInput.val();

    rentPlaceInput.combobox({
        url: 'index.php?common-rpc=ekate-combobox',
        rpcParams: [{
            selected:true,
            record_all:true
        }],
        valueField: 'ekate',
        textField: 'text',
        filter: function(q, row){
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
            {
                return true;
            }
        },
        onLoadSuccess: function (data) {
            if (rentPlaceValue) {
                rentPlaceInput.combobox('setValue', rentPlaceValue);
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function addOwnerToPlots() {
    if (!hasPlotRightsRW) {
        messagerPlotsWriteRights();
        return false;
    }
    var plotData = jQuery('#plots-tree').tree('getSelected'),
        contractData = jQuery('#contract-info-tables').datagrid('getSelected'),
        max,
        sum;

    if(!contractData) {
        jQuery.messager.alert('Грешка', 'Няма избран договор!');
    }

    //No Rights to operate with "Договори за собственост"
    if (contractData.c_type == CONTRACT_TYPE_OWN && !hasContractsOwnWriteRights) {
        messagerContractsOwnWriteRights();
        return false;
    }
    if (contractData.from_sublease > 0) {
        messageContractIsFromSublease(contractData);
        return false;
    }
    max = new Fraction(1);

    if (!plotData) {
        jQuery.messager.alert('Грешка', 'Не е избран имот.');
        return;
    }

    sum = getTotalOwnersShares();

    if (sum.valueOf() >= 1) {
        jQuery.messager.alert('Грешка', 'Вече е зададена 100% собственост за този имот.');
        return;
    }

    maxOwnership = max.sub(sum);

    contragent_type = 'owner';

    jQuery('#win-plot-owner-add').window('open');

    //init add owners grid
    initAddOwnersGrid(contractData.id, plotData.attributes.gid);
    //init representatives
    initOwnersRepsGrid();
}

function getTotalOwnersShares() {
    var gridData = jQuery('#owner-info-tables').treegrid('getData');
    var sumFr = new Fraction();

    //calculate total ownership
    if (gridData[0]) {
        for (var i = 0; i < gridData.length; i++) {
            var f;
            if(gridData[i].numerator && gridData[i].denominator) {
                f = new Fraction(gridData[i].numerator / gridData[i].denominator);
            }
            else {
                f = new Fraction(gridData[i].percent / 100);
            }

            sumFr = sumFr.add(f);
        }
    }

    return sumFr;
}

function editPlotsOwnerPercentage() {
    if (!hasPlotRightsRW) {
        messagerPlotsWriteRights();
        return false;
    }
    var getSelected = jQuery('#owner-info-tables').treegrid('getSelected'),
        gridData = jQuery('#owner-info-tables').treegrid('getData'),
        sum = new Fraction(),
        max = new Fraction(1),
        contractData = jQuery('#contract-info-tables').datagrid('getSelected'),
        i,
        f;
    //No Rights to operate with "Договори за собственост"
    if (contractData.c_type == CONTRACT_TYPE_OWN && !hasContractsOwnWriteRights) {
        messagerContractsOwnWriteRights();
        return false;
    }
    if (contractData.from_sublease > 0) {
        messageContractIsFromSublease(contractData);
        return false;
    }

    if (!getSelected) {
        jQuery.messager.alert('Грешка', 'Моля изберете запис, който искате да редактирате.');
        return;
    }


    if (isHeritorSelected) {
        var parent = jQuery('#owner-info-tables').treegrid('getParent', getSelected.fakeid),
            childrens = jQuery('#owner-info-tables').treegrid('getChildren', parent.fakeid),
            directChildrens = [];
        for (i = 0; i < childrens.length; i++) {
            if(childrens[i].level == parent.level + 1) {
                directChildrens.push(childrens[i]);
            }
        }
        gridData = directChildrens;

        if (parent.numerator && parent.denominator) {
            max = new Fraction(parent.numerator / parent.denominator);
        } else {
            max = new Fraction(parent.percent / 100);
        }
    }
    for (i = 0; i < gridData.length; i++) {
        if (getSelected.fakeid != gridData[i].fakeid) {
            if (gridData[i].numerator && gridData[i].denominator) {
                f = new Fraction(gridData[i].numerator / gridData[i].denominator);
            } else {
                f = new Fraction(gridData[i].percent / 100);
            }

            sum = sum.add(f);
        }
    }

    maxOwnership = max.sub(sum);

    contragent_type = 'owner';

    initEditOwnageDataFields();
    jQuery('#win-edit-contract-owner-data').window('open');
}

function deletePlotsOwnerRelation() {
    if (!hasPlotRightsRW) {
        messagerPlotsWriteRights();
        return false;
    }
    var contractData = jQuery('#contract-info-tables').datagrid('getSelected');
    //No Rights to operate with "Договори за собственост"
    if (contractData.c_type == CONTRACT_TYPE_OWN && !hasContractsOwnWriteRights) {
        messagerContractsOwnWriteRights();
        return false;
    }

    if (contractData.from_sublease > 0) {
        messageContractIsFromSublease(contractData);
        return false;
    }
    var getSelected = jQuery('#owner-info-tables').treegrid('getSelected');

    if (getSelected) {

        jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да премахнете този собственик.', function (r) {
            if (r) {
                var plotOwnerRelId = getSelected.id,
                    ownerId = getSelected.owner_id;


                TF.Rpc.Contracts.ConctractOwnerData.deletePlotOwner(plotOwnerRelId, ownerId)
                    .done(function () {
                        jQuery('#owner-info-tables').treegrid('loadRpc');
                    })
                    .fail(function (errorObj) {
                        if (errorObj.is(TF.Rpc.ExceptionsList.NON_EXISTING_CONTRACT_PLOT_RELATION)) {
                            jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.NON_EXISTING_CONTRACT_PLOT_RELATION.message, 'warning');
                        } else if (errorObj.is(TF.Rpc.ExceptionsList.INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS)) {
                            jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS.message, 'warning');
                        }
                    });
            }
        });
    } else {
        jQuery.messager.alert('Грешка', 'Моля изберете собственик, който да бъде премахнат.');
    }
}

function displayPlotsOwnerInformation() {
    var getSelected = jQuery('#owner-info-tables').treegrid('getSelected');
    if (getSelected) {
        window.open("index.php?page=Owners.Home&owner_id=" + getSelected.owner_id, '_blank');
    } else {
        jQuery.messager.alert('Грешка', 'Моля изберете собственик, за който да бъде показана информация.');
    }
}

function editPlotsOwnerData() {

    lastAddedOwner = 0;
    lastAddedParent = 0;
    var getSelected = jQuery('#owner-info-tables').treegrid('getSelected');
    if (getSelected) {
        TF.Rpc.Owners.OwnersTree.markForEdit(getSelected.owner_id)
            .done(function (data) {
                ownerType = data.owner_type;
                isEditing = true;
                jQuery('#win-add-new-owner').window('open');
                setOwnerFieldsDataForEdit(data);
                setAddEditFieldsValidators();
                displayTablesOnCallbackComplete();
            })
            .fail(function (errorObj) {
                jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
            })
    } else {
        jQuery.messager.alert('Грешка', 'Моля изберете собственик, който да бъде редактиран.');
    }
}

function displayTablesOnCallbackComplete(){
    if(ownerType == 0) {
        displayTables('legal');
        jQuery('#is_legal > input').prop('checked', true);
        jQuery('#is_physical > input').prop('checked', false);
    }else{
        displayTables('physical');
        jQuery('#is_legal > input').prop('checked', false);
        jQuery('#is_physical > input').prop('checked', true);
    }
}

function setAddEditFieldsValidators() {
    numLengthbox();
    jQuery('#name > input').validatebox({
        required: true,
        missingMessage: 'Моля въведете име на собственик.'
    });
    jQuery('#surname > input').validatebox({
        required: true,
        missingMessage: 'Моля въведете презиме на собственик.'
    });
    jQuery('#lastname > input').validatebox({
        required: true,
        missingMessage: 'Моля въведете фамилия на собственик.'
    });

    let egnBox = new EgnValidateBox('#egn > input');

    jQuery('#company_name > input').validatebox({
        required: true,
        missingMessage: 'Моля въведете име на фирма.'
    });
    jQuery('#eik > input').numberbox({
        required: true,
        validType: 'setLength[9]',
        missingMessage: 'Моля въведете ЕИК номер.',
        parser: function(s){
            return s;
        }
    });
    jQuery('#mobile > input').validatebox({
        missingMessage: 'Моля въведете мобилен телефон.'
    });
    jQuery('#email > input').validatebox({
        missingMessage: 'Моля въведете имейл на собственика.'
    });

    jQuery('#is_foreigner').on('change', function () {
        if(jQuery('#is_foreigner').is(':checked') || jQuery('#IsDead').is(':checked')) {
            egnBox.disableValidation();
        } else {
            egnBox.enableValidation();
        }
    });

    jQuery('#IsDead').on('change', function () {
        if(jQuery('#is_foreigner').is(':checked') || jQuery('#IsDead').is(':checked')) {
            egnBox.disableValidation();
        } else {
            egnBox.enableValidation();
        }

        if(jQuery('#IsDead').is(':checked')) {
            jQuery('#prepiska-row').show();
        } else {
            jQuery('#prepiska-row').hide();
        }

    });

    jQuery('#is_foreigner').trigger('change');
    jQuery('#IsDead').trigger('change');
}

function setOwnerFieldsDataForEdit(data) {
    jQuery('#name > input').val(data['name']);
    jQuery('#surname > input').val(data['surname']);
    jQuery('#lastname > input').val(data['lastname']);
    new CardIdValidateBox('#lk_nomer > input').setValue(data['lk_nomer']);
    jQuery('#lk_izdavane > input').val(data['lk_izdavane']);
    jQuery('#company_name > input').val(data['company_name']);
    jQuery('#eik > input').numberbox('setValue', data['eik']);
    jQuery('#mol > input').val(data['mol']);
    jQuery('#company_address > textarea').val(data['company_address']);
    jQuery('#phone > input').val(data['phone']);
    jQuery('#fax > input').val(data['fax']);
    jQuery('#mobile > input').val(data['mobile']);
    jQuery('#email > input').val(data['email']);
    jQuery('#iban > input').val(data['iban']);
    jQuery('#address > textarea').val(data['address']);
    jQuery('#remark > textarea').val(data['remark']);
    new EgnValidateBox('#egn > input').setValue(data['egn']);

    if (data['owner_type'] == 0) {
        jQuery('#is_legal > input').prop('checked', true);
        jQuery('#is_physical > input').prop('checked', false);
    }
    else {
        jQuery('#is_legal > input').prop('checked', false);
        jQuery('#is_physical > input').prop('checked', true);
    }

    data['is_dead'] ? jQuery('#is-dead > input').prop('checked', true) : jQuery('#is-dead > input').prop('checked', false);
    data['is_foreigner'] ? jQuery('#is_foreigner').prop('checked', true) : jQuery('#is_foreigner').prop('checked', false);
    data['is_dead'] ? jQuery('#prepiska > input').val(data['prepiska']) : jQuery('#prepiska > input').val('');
    jQuery('#rent-place > input').combobox('setValue', data['rent_place']);

    jQuery('#is_foreigner').trigger('change');
    jQuery('#IsDead').trigger('change');
}
