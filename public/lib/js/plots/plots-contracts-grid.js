/*jslint browser: true*/
/*global jQuery, EasyUIRPCLoaders.EasyUIGridCustomLoader, TF,bufferview, Fraction, RpcErrorHandler, hasPlotRightsRW, CONTRACT_TYPE_OWN, hasContractsOwnWriteRights, messagerContractsOwnWriteRights, messagerPlotsWriteRights */

var editingIndex = null;

function initPlotsContractsGrid(plot_id, rowData) {

    var contractsDatagrid = jQuery('#contract-info-tables'),
        isDatagridBound = contractsDatagrid.data().hasOwnProperty('datagrid');

    if (isDatagridBound) {
        if (plot_id !== 0) {
            contractsDatagrid.datagrid({
                rpcParams: [plot_id],
                url: 'index.php?plots-rpc=plots-contracts-datagrid',
            });
        } else {
            contractsDatagrid.datagrid('loadData', {rows: [], total: 0});
            initPlotsFarmingGrid(0, 0, 0, 0);
            initPlotsOwnersGrid(0,0,0,0);
        }
        return;
    }
    contractsDatagrid.datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        iconCls: 'icon-contract',
        title: 'Договори',
        pageSize: 10,
        singleSelect: true,
        fitColumns: true,
        showFooter: true,
        fit: true,
        sortName: 'id',
        sortOrder: 'desc',
        idField: 'id',
        rowStyler: function (index, row) {
            if (row.active == 0 || row.active_text == 'Изтекъл') {
                return 'color: #aaa';
            }
            if (row.annex_action == 'removed') {
                return 'text-decoration: line-through;';
            }
        },
        columns: [[
            {
                field: 'c_num',
                title: '<b>Номер</b>',
                sortable: true,
                width: 170
            }, {
                field: 'c_date',
                title: '<b>Дата</b>',
                sortable: true,
                width: 170
            }, {
                field: 'active_text',
                title: '<b>Статус</b>',
                sortable: true,
                width: 80
            }, {
                field: 'nm_usage_rights',
                title: '<b>Тип</b>',
                sortable: true,
                width: 170
            }, {
                field: 'start_date',
                title: '<b>Влизане в сила</b>',
                sortable: true,
                width: 170
            }, {
                field: 'farming',
                title: '<b>Стопанство</b>',
                sortable: true,
                width: 200
            }, {
                field: 'due_date',
                title: '<b>Крайна дата</b>',
                sortable: true,
                width: 200
            }, {
                field: 'contract_area',
                title: '<b>Площ по<br/>договор (дка)</b>',
                align: 'center',
                sortable: true,
                width: 120
            }, {
                field: 'price_per_acre',
                title: '<b>Цена/дка</b>',
                align: 'center',
                sortable: true,
                width: 90
            }, {
                field: 'price_sum',
                title: '<b>Сума</b>',
                align: 'center',
                sortable: true,
                width: 90
            }
        ]],
        pagination: true,
        rownumbers: true,
        toolbar: [{
            id: 'btnviewcontractinfo',
            text: 'Информация',
            iconCls: 'icon-info',
            handler: function () {
                var getChecked = jQuery('#contract-info-tables').datagrid('getChecked');
                if (getChecked[0]) {
                    if (getChecked[0].is_annex) {
                        window.open("index.php?page=Contracts.Home&contract_id=" + getChecked[0].id, '_blank');
                    } else if (getChecked[0].is_sublease) {
                        window.open("index.php?page=Subleases.Home&sublease_id=" + getChecked[0].id, '_blank');
                    } else if (getChecked[0].sales_contract_id && getChecked[0].sales_contract_id != '') {
                        window.open("index.php?page=SalesContracts.Home&id=" + getChecked[0].sales_contract_id, '_blank');
                    }else {
                        window.open("index.php?page=Contracts.Home&contract_id=" + getChecked[0].id, '_blank');
                    }
                } else {
                    jQuery.messager.alert('Грешка', 'Моля изберете договор, за който да бъде показана информация.');
                }
            }
        }],
        onLoadSuccess: function () {
            var gridData = jQuery('#contract-info-tables').datagrid('getData'),
                selectedPlot =  jQuery('#plots-tree').tree('getSelected');
            if (!selectedPlot) {
                selectedPlot = {id: 0};
            }
            if (gridData.rows[0]) {
                jQuery('#contract-info-tables').datagrid('selectRow', 0);
                initPlotsFarmingGrid(selectedPlot.id, gridData.rows[0].id, gridData.rows[0].c_type, gridData.rows[0].is_sublease);
                initPlotsOwnersGrid(selectedPlot.id, gridData.rows[0].id, gridData.rows[0].c_type, gridData.rows[0].is_sublease);
            } else {
                initPlotsFarmingGrid(0, 0, 0, 0);
                initPlotsOwnersGrid(0, 0, 0, 0);
            }
        },
        onSelect: function (rowIndex, rowData) {
            if (rowData.nm_usage_rights != 'Собственост') {
                var selectedPlot = jQuery('#plots-tree').tree('getSelected');
                initPlotsFarmingGrid(selectedPlot.id, rowData.id, rowData.c_type, rowData.is_sublease);
                initPlotsOwnersGrid(selectedPlot.id, rowData.id, rowData.c_type, rowData.is_sublease);
                jQuery('#contracts-owners-toolbar').children().menubutton('enable');
                jQuery('#owners-tabs').tabs('enableTab', 0);
                jQuery('#owners-tabs').tabs('enableTab', 1);
            } else {
                initPlotsFarmingGrid(0, 0, 0, 0);
                initPlotsOwnersGrid(0, 0, 0, 0);
                jQuery('#contracts-owners-toolbar').children().menubutton('disable');
                jQuery('#owners-tabs').tabs('disableTab', 0);
                jQuery('#owners-tabs').tabs('disableTab', 1);
            }
        },
        onBeforeLoad: function () {
            jQuery('#contract-info-tables').datagrid('clearChecked');
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function showAlert()
{
    jQuery.messager.alert('Грешка', 'Mоля, селектирайте договор', 'warning');
}

function initAddContractsGrid(plot_id) {

    var addContractsDatagrid =  jQuery('#add-contracts-tables'),
        isAddDatagridBound = addContractsDatagrid.data().hasOwnProperty('datagrid');

    if (isAddDatagridBound) {
        addContractsDatagrid.datagrid({
            rpcParams: [plot_id],
            rpcMethod: "getContractsAddGrid",
        });
        return;
    }

    addContractsDatagrid.datagrid({
        nowrap: true,
        border: false,
        autoRowHeight: true,
        striped: true,
        pageSize: 20,
        fit: true,
        singleSelect: true,
        fitColumns: true,
        showFooter: true,
        rpcParams: [plot_id],
        rpcMethod: "getContractsAddGrid",
        url: 'index.php?plots-rpc=plots-contracts-datagrid',
        sortName: '',
        sortOrder: 'asc',
        idField: '',
        frozenColumns: [[
            {
                field: 'ck',
                checkbox: true
            },
        ]],
        columns: [[
            {
                field: 'c_num',
                title: '<b>Номер</b>',
                sortable: true,
                width: 170
            }, {
                field: 'c_date',
                title: '<b>Дата</b>',
                sortable: true,
                width: 170
            }, {
                field: 'nm_usage_rights',
                title: '<b>Тип</b>',
                sortable: true,
                width: 170
            }, {
                field: 'start_date',
                title: '<b>Влизане в сила</b>',
                sortable: true,
                width: 170
            }, {
                field: 'farming',
                title: '<b>Стопанство</b>',
                sortable: true,
                width: 200
            }, {
                field: 'due_date',
                title: '<b>Крайна дата</b>',
                sortable: true,
                width: 200
            }, {
                field: 'contract_area',
                title: '<b>Площ по<br/>договор (дка)</b>',
                align: 'center',
                sortable: true,
                width: 150,
                editor: {
                    type: 'numberbox',
                    options: {
                        min: 0,
                        required: true,
                        missingMessage: 'Полето е задължително',
                        precision: 3
                    }
                }
            }, {
                field: 'price_per_acre',
                title: '<b>Цена/дка</b>',
                align: 'center',
                sortable: true,
                width: 100,
                editor: {
                    type: 'numberbox',
                    options: {
                        min: 0,
                        precision: 2
                    }
                }
            }, {
                field: 'price_sum',
                title: '<b>Сума</b>',
                align: 'center',
                sortable: true,
                width: 100,
                editor: {
                    type: 'numberbox',
                    options: {
                        min: 0,
                        precision: 2
                    }
                }
            }
        ]],
        pagination: true,
        rownumbers: true,
        toolbar: [{
            id: 'btnaddcontractstoplot',
            text: 'Добави към парцела',
            iconCls: 'icon-add',
            handler: function () {
                var getChecked = jQuery('#add-contracts-tables').datagrid('getChecked'),
                    plotData = jQuery('#plots-tree').tree('getSelected');

                if(!getChecked.length){
                    return showAlert();
                }

                //No Rights to operate with "Договори за собственост"
                if (getChecked[0].c_type == CONTRACT_TYPE_OWN && !hasContractsOwnWriteRights) {
                    messagerContractsOwnWriteRights();
                    return false;
                }


                var index = jQuery('#add-contracts-tables').datagrid('getRowIndex', getChecked[0]);
                jQuery('#add-contracts-tables').datagrid('endEdit', index);

                var plot = new Object();
                plot.plot_id = plotData.id;
                plot.contract_area = getChecked[0].contract_area;
                plot.document_area = plotData.attributes.document_area;
                plot.price_per_acre = getChecked[0].price_per_acre;
                plot.price_sum = getChecked[0].price_sum;
                plot.contract_id = getChecked[0].id;

                TF.Rpc.Plots.PlotsContractsDatagrid.addContractPlotRelation(plot)
                    .done(function () {
                        jQuery('#win-contracts-add').window('close');
                        jQuery('#contract-info-tables').datagrid('loadRpc');
                    })
                    .fail(function (errorObj) {
                        if (errorObj.is(TF.Rpc.ExceptionsList.CONTRACT_AREA_EXCEEDS_PLOT_AREA)) {
                                jQuery('#add-contracts-tables').datagrid('clearChecked');
                                jQuery.messager.confirm('Потвърждение', 'Към имота има добавени договори със съвпадащ период на деиствие или сумарната площ по договори е по-голяма от площта по документ. Сигурни ли сте, че искате да продължите?', function(r) {
                                if (r) {
                                    TF.Rpc.Plots.PlotsContractsDatagrid.addConfirmedContractPlotRelation()
                                        .done(function () {
                                            jQuery('#win-contracts-add').window('close');
                                            jQuery('#contract-info-tables').datagrid('loadRpc');
                                        })
                                        .fail(function (errorObj) {
                                            jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
                                        });
                                }
                            });
                        } else if (errorObj.is(TF.Rpc.ExceptionsList.NON_EXISTING_CONTRACT_OR_PLOT_NUMBER)) {
                            jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.NON_EXISTING_CONTRACT_OR_PLOT_NUMBER.message, 'warning');
                        }
                    });
            }
        }, {
            id: 'contractfilter',
            text: 'Филтър',
            iconCls: 'icon-filter',
            handler: function () {
                jQuery('#win-add-contracts-filter').window('open');
            }
        }, {
            id: 'contractfilterclear',
            text: 'Покажи всички',
            iconCls: 'icon-clear-filter',
            handler: function () {
                clearContractFilter();
            }
        }, {
            id: 'btnviewcontractinfo',
            text: 'Информация',
            iconCls: 'icon-info',
            handler: function () {
                var getChecked = jQuery('#add-contracts-tables').datagrid('getChecked');
                if (getChecked[0]) {

                    if (getChecked[0].is_annex) {
                        window.open("index.php?page=Contracts.Home&contract_id=" + getChecked[0].id, '_blank');
                    } else if (getChecked[0].is_sublease) {
                        window.open("index.php?page=Subleases.Home&sublease_id=" + getChecked[0].id, '_blank');
                    } else {
                        window.open("index.php?page=Contracts.Home&contract_id=" + getChecked[0].id, '_blank');
                    }
                } else {
                    jQuery.messager.alert('Грешка', 'Моля изберете договор, за който да бъде показана информация.');
                }
            }
        }],
        onBeforeLoad: function () {
            jQuery('#add-contracts-tables').datagrid('clearChecked');
        },
        onSelect: function (index) {
            if (editingIndex != null && editingIndex != index) {
                jQuery('#add-contracts-tables').datagrid('endEdit', editingIndex);
                jQuery('#add-contracts-tables').datagrid('beginEdit', index);
                editingIndex = index;
            } else {
                jQuery('#add-contracts-tables').datagrid('beginEdit', index);
                editingIndex = index;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function addContractToPlotResult(rpcResponse) {
    if (rpcResponse.getCode() == -33151) {
        jQuery('#add-contracts-tables').datagrid('clearChecked');

        jQuery.messager.confirm('Потвърждение', 'Към имота има добавени договори със съвпадащ период на деиствие или сумарната площ по договори е по-голяма от площта по документ. Сигурни ли сте, че искате да продължите?', function(r) {
            if (r) {
                TF.Rpc.Plots.PlotsContractsDatagrid.addConfirmedContractPlotRelation()
                    .done(function () {
                        jQuery('#win-contracts-add').window('close');
                        jQuery('#contract-info-tables').datagrid('loadRpc');
                    })
                    .fail(function (errorObj) {
                        jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
                    });
            }
        });
    }
}

function contractFilter() {
    if (jQuery('#search-contract-number > input').val() != ''
        || jQuery('#search-contract-date-from > input').datebox('getValue') != ''
        || jQuery('#search-contract-date-to > input').datebox('getValue') != '') {
        var selectedPlot =  jQuery('#plots-tree').tree('getSelected');
        jQuery('#add-contracts-tables').datagrid({
            rpcParams: [{
                plot_id: selectedPlot.id,
                contract: jQuery('#search-contract-number > input').val(),
                date_from: jQuery('#search-contract-date-from > input').datebox('getValue'),
                date_to: jQuery('#search-contract-date-to > input').datebox('getValue')
            }]
        });
        jQuery('#win-add-contracts-filter').window('close');
        jQuery('#contract-info-tables').datagrid('loadRpc');
    } else {
        jQuery.messager.alert('Грешка', 'Моля задайте данни за филтриране.');
    }
}

function clearContractFilter() {
    jQuery('#search-contract-number > input').val('');
    jQuery('#search-contract-date-from > input').datebox('setValue');
    jQuery('#search-contract-date-to > input').datebox('setValue');
    var selectedPlot =  jQuery('#plots-tree').tree('getSelected');
    jQuery('#add-contracts-tables').datagrid({
        rpcParams: [selectedPlot.id]
    });
}

function dispatchContractInfoRequest(contract_id) {
    jQuery.ajax({
        url: 'index.php?plots-rpc=contract-info',
        type: 'post',
        data: {
            id: contract_id
        },
        complete: function (response) {
            if (response) {
                var responseText = jQuery.parseJSON(response.responseText);
                initContractInfoPanelFromResponse(responseText);
                jQuery('#contract-info-panel').window('open');
            }
        }
    });
}

function initContractInfoPanelFromResponse(responseText) {
    var html = '';
    html += '<fieldset style="border: 1px solid #000; padding: 5px 10px;">';
    html += '<legend style="font-style: italic; font-weight: bold">Основна информация</legend>';
    html += 'Тип: ' + responseText.c_type + '</br>';
    html += 'Номер: ' + responseText.c_num + '</br>';
    html += 'Сключване: ' + responseText.c_date + '</br>';
    html += 'Влизане в сила: ' + responseText.start_date + '</br>';
    html += 'Крайна дата: ' + responseText.due_date + '</br>';
    html += 'Стопанство: ' + responseText.farming + '</br>';
    html += '</fieldset>';

    html += '<fieldset style="border: 1px solid #000; padding: 5px 10px; margin-top: 5px;">';
    html += '<legend style="font-style: italic; font-weight: bold">Служба по вписване</legend>';
    html += 'Номер на вписване: ' + responseText.sv_num + '</br>';
    html += 'Дата на вписване: ' + responseText.sv_date + '</br>';
    html += '</fieldset>';

    html += '<fieldset style="border: 1px solid #000; padding: 5px 10px; margin-top: 5px;">';
    html += '<legend style="font-style: italic; font-weight: bold">Рента</legend>';
    html += 'Сума: ' + responseText.renta + '</br>';
    html += 'В натура - тип: ' + responseText.renta_nat_type + '</br>';
    html += 'В натура - кол.: ' + responseText.renta_nat + '</br>';
    html += '</fieldset>';

    jQuery('#contract-info-panel').html(html);
}
