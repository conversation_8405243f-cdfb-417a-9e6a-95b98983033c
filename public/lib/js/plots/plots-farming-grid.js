var selectedPlotID = false,
    selectedContractID = false,
    CONTRACT_TYPE_OWN = 1;

function initPlotsFarmingGrid(plot_id, contract_id, c_type, is_sublease) {

    selectedPlotID = plot_id;
    selectedContractID = contract_id;
    selectedContractType = c_type;
    selectedContractIsSublease = is_sublease;

    var plotsFarmingDatagrid = jQuery('#plots-farming-tables');
    var isplotsFarmingDatagridBound = plotsFarmingDatagrid.data().hasOwnProperty('datagrid');

    if (isplotsFarmingDatagridBound) {
        if (plot_id > 0 && contract_id > 0) {
            plotsFarmingDatagrid.datagrid({
                rpcParams: [{
                    plot_id: selectedPlotID,
                    contract_id: selectedContractID
                }],
                url: 'index.php?contracts-rpc=contracts-farming-datagrid',
            });
        } else {
            plotsFarmingDatagrid.datagrid('loadData', {rows: [], total: 0});
        }
        return;
    }
    plotsFarmingDatagrid.datagrid({
        iconCls: 'icon-agriculture',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 10,
        fit: true,
        singleSelect: true,
        fitColumns: true,
        showFooter: true,
        border: false,
        sortName: 'id',
        sortOrder: 'desc',
        idField: 'id',
        columns: [[
            {
                field: 'farming',
                title: '<b>Стопанство</b>',
                sortable: false,
                width: 100
            }, {
                field: 'rep_names',
                title: '<b>Представител</b>',
                sortable: false,
                width: 100
            }, {
                field: 'rat_ownage',
                title: '<b>Собственост(%)</b>',
                sortable: false,
                width: 75,
                formatter: function(value, row){
                    var percent;

                    if(row.numerator && row.denominator) {
                        var percent = new Fraction(row.numerator / row.denominator * 100);
                        var fraction = row.numerator + '/' + row.denominator;
                        value = percent.toString() + '% (' +  fraction + ')';

                        return value;
                    }

                    var fraction = new Fraction(row.percent / 100);
                    var percent = fraction.mul(100);
                    value = percent.toString() + '% (' +  fraction.simplify().toFraction() + ')';

                    return value;
                }
            }
        ]],
        pagination:false,
        rownumbers:true,
        toolbar: [
            {
                id: 'btnaddfarmingcontragent',
                text: 'Добавяне',
                iconCls: 'icon-add',
                handler: function() {
                    if (hasPlotRightsRW) {
                        var contractData = jQuery('#contract-info-tables').datagrid('getChecked');
                        var plotData = jQuery('#plots-tree').tree('getSelected').attributes;
                        var max = new Fraction(1);

                        if (!(contractData[0] && plotData)) {
                            jQuery.messager.alert('Грешка', 'Не са избрани имот и договор, към които да се добавят стопанства.');
                            return;
                        }

                        if (contractData[0].from_sublease > 0) {
                            messageContractIsFromSublease(contractData[0]);
                            return false;
                        }

                        //No Rights to operate with "Договори за собственост"
                        if(contractData[0].c_type == CONTRACT_TYPE_OWN && !hasContractsOwnWriteRights) {
                            messagerContractsOwnWriteRights();
                            return false;
                        }

                        var sum = getTotalFarmingShares();

                        if (sum.valueOf() >= 1) {
                            jQuery.messager.alert('Грешка', 'Вече е зададена 100% собственост за този имот.');
                            return;
                        }

                        maxOwnership = max.sub(sum);

                        contragent_type = 'farming';

                        jQuery('#win-plot-owner-add').window('open');

                        //init add farming grid
                        initAddPlotsFarmingGrid(selectedContractID, plotData.gid);
                        //init representatives
                        initOwnersRepsGrid();
                    } else {
                        messagerPlotsWriteRights();
                    }
                }
            }, {
                id: 'btneditfarmingcontragent',
                text: 'Редактиране',
                iconCls: 'icon-edit',
                handler: function() {
                    if (hasPlotRightsRW) {
                        var getSelected = jQuery('#plots-farming-tables').datagrid('getSelected');
                        var gridData = jQuery('#plots-farming-tables').datagrid('getData');
                        var sum = new Fraction();
                        var max = new Fraction(1);

                    var contractData = jQuery('#contract-info-tables').datagrid('getChecked');
                    //No Rights to operate with "Договори за собственост"
                    if(contractData[0].c_type == CONTRACT_TYPE_OWN && !hasContractsOwnWriteRights) {
                        messagerContractsOwnWriteRights();
                        return false;
                    }

                    if (contractData[0].from_sublease > 0) {
                        messageContractIsFromSublease(contractData[0]);
                        return false;
                    }

                    var getSelected = jQuery('#plots-farming-tables').datagrid('getSelected');
                    var gridData = jQuery('#plots-farming-tables').datagrid('getData');
                    var sum = new Fraction();
                    var max = new Fraction(1);

                    if (getSelected) {
                        for (var i = 0; i < gridData['rows'].length; i++) {
                            if(getSelected.id == gridData['rows'][i].id) {
                                continue;
                            }

                            var f;
                            if(gridData['rows'][i].numerator && gridData['rows'][i].denominator) {
                                f = new Fraction(gridData['rows'][i].numerator / gridData['rows'][i].denominator);
                            }
                            else {
                                f = new Fraction(gridData['rows'][i].percent / 100);
                            }

                                sum = sum.add(f);
                            }

                            maxOwnership = max.sub(sum);

                            contragent_type = 'farming';
                            initEditOwnageDataFields();
                            jQuery('#win-edit-contract-owner-data').window('open');
                        }
                        else {
                            jQuery.messager.alert('Грешка', 'Моля изберете запис, който искате да редактирате.');
                        }
                    } else {
                        messagerPlotsWriteRights();
                    }
                }
            }, {
                id: 'btndeletefarmingcontragent',
                text: 'Премахване',
                iconCls: 'icon-delete',
                handler: function() {
                    if (hasPlotRightsRW) {
                    var contractData = jQuery('#contract-info-tables').datagrid('getChecked');
                    //No Rights to operate with "Договори за собственост"
                    if(contractData[0].c_type == CONTRACT_TYPE_OWN && !hasContractsOwnWriteRights) {
                        messagerContractsOwnWriteRights();
                        return false;
                    }

                    if (contractData[0].from_sublease > 0) {
                        messageContractIsFromSublease(contractData[0]);
                        return false;
                    }
                        var getSelectedFarming = jQuery('#plots-farming-tables').datagrid('getSelected');
                        if (getSelectedFarming) {
                            jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да премахнете този запис?', function(r) {
                                if (r) {
                                        TF.Rpc.Contracts.ConctractOwnerData.deletePCToFarmingRelation(getSelectedFarming.id,selectedContractID)
                                        .done(function (data) {
                                            jQuery('#plots-farming-tables').datagrid('loadRpc');
                                        });
                                }
                            });
                        } else {
                            jQuery.messager.alert('Грешка', 'Не е избран запис!');
                        }
                    } else {
                        messagerPlotsWriteRights();
                    }
                }
            }
        ],
        onBeforeLoad: function() {
            jQuery('#plots-farming-tables').datagrid('clearChecked');
        },
        onSelect: function() {
            isHeritorSelected = false;
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    if(selectedContractIsSublease)
    {
        jQuery('#btnaddfarmingcontragent').linkbutton('disable');
        jQuery('#btneditfarmingcontragent').linkbutton('disable');
        jQuery('#btndeletefarmingcontragent').linkbutton('disable');
    }
    else
    {
        jQuery('#btnaddfarmingcontragent').linkbutton('enable');
        jQuery('#btneditfarmingcontragent').linkbutton('enable');
        jQuery('#btndeletefarmingcontragent').linkbutton('enable');
    }
}

function initAddPlotsFarmingGrid(contract_id, plot_id) {

	jQuery('#owners-add-tables').datagrid({
        title:'Стопанства',
        iconCls:'icon-agriculture',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 10,
        fit: true,
		singleSelect: true,
        fitColumns: true,
        showFooter: true,
		border: true,
        url: 'index.php?contracts-rpc=contracts-farming-datagrid',
        rpcMethod: 'add',
        rpcParams:[{
            plot_id: plot_id,
            contract_id: contract_id
        }],
        sortName: 'id',
        sortOrder: 'desc',
        idField:'id',
        frozenColumns:[[
        {
            field:'ck',
            checkbox:true
        }
        ]],
        columns:[[
        {
            field:'name',
            title:'<b>Име</b>',
            sortable:true,
            width:200
        },{
            field:'address',
            title:'<b>Адрес на стопанството</b>',
            sortable:true,
            width:200
        },{
            field:'company',
            title:'<b>Фирма</b>',
            sortable:true,
            width:200
        },{
            field:'bulstat',
            title:'<b>Булстат</b>',
            sortable:true,
            width:200
        },{
            field:'company_address',
            title:'<b>Адрес на фирмата</b>',
            sortable:true,
            width:200
        },{
            field:'mol',
            title:'<b>МОЛ</b>',
            sortable:true,
            width:200
        }
        ]],
        pagination:true,
        rownumbers:true,
		toolbar: null,
		onBeforeLoad: function() {
			jQuery('#owners-add-tables').datagrid('clearChecked');
			initContractOwnerDataFields();
		},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function getTotalFarmingShares() {
    var gridData = jQuery('#plots-farming-tables').datagrid('getData');

    var sumFr = new Fraction();

    if (gridData['rows'][0]) {
        for (var i = 0; i < gridData['rows'].length; i++) {
            var f;
            if(gridData['rows'][i].numerator && gridData['rows'][i].denominator) {
                f = new Fraction(gridData['rows'][i].numerator / gridData['rows'][i].denominator);
            }
            else {
                f = new Fraction(gridData['rows'][i].percent / 100);
            }

            sumFr = sumFr.add(f);
        }
    }

    return sumFr;
}
