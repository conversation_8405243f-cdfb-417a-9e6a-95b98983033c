Namespace('TF.Rpc.Plots');
var showOwnershipOnMap = false;
function showMap(){
    jQuery('#win-map').window('open');
    jQuery('#plot-map-tools').window('open');
    jQuery('#plot-map-legend').window('open');
    initMap();
    jQuery('#map-filter').linkbutton('select');
}

function initMap(){
    if(!map){
        var options = {
            controls: [new OpenLayers.Control.Navigation()],
            projection: new OpenLayers.Projection("EPSG:900913")
        };
        map = new OpenLayers.Map('map', options);
        initMapPad();
        initLayers();
        initMapControls();
    }
    map.render("map");
}

function initMapPad()
{
    var bingApiKey = "AiWEso3-IjWyX1aZMuep9Sjl62D6FUMqv8qQGpV-kgbW0qWk61to4nrqHh-2D5HL";

    var chosenMapType = store.get('map_pad') || 1;
    var layerMapPad;

    switch (chosenMapType)
    {
        case 2:
            layerMapPad = new OpenLayers.Layer.Bing({
                key: bingApiKey,
                type: "Aerial",
                name: "MapPad"
            });
            break;
        case 3:
            layerMapPad = new OpenLayers.Layer.Bing({
                key: bingApiKey,
                type: "Road",
                name: "MapPad"
            });
            break;
        case 4:
            layerMapPad = new OpenLayers.Layer.Google(
                    "MapPad",
                    {type: google.maps.MapTypeId.TERRAIN}
            );
            break;
        case 5:
            layerMapPad = new OpenLayers.Layer.Google(
                    "MapPad",
                    {numZoomLevels: 20}
            );
            break;
        case 6:
            layerMapPad = new OpenLayers.Layer.Google(
                    "MapPad",
                    {type: google.maps.MapTypeId.HYBRID, numZoomLevels: 20}
            );
            break;
        case 7:
            layerMapPad = new OpenLayers.Layer.Google(
                    "MapPad",
                    {type: google.maps.MapTypeId.SATELLITE, numZoomLevels: 22}
            );
            break;
        case 8:
            layerMapPad = new OpenLayers.Layer.OSM();
            break;
        case 9:
            layerMapPad = new OpenLayers.Layer.WMS(
                'MapPad',
                imagesWMSServer + "?map=" + '/var/www/satellite_processor/maps/geo_scan.map',
                //wmsServer + "?map=" + mapPath + groupID + '.map',
                {
                    layers: 'geo_scan',
                },
                {
                    numZoomLevels: 18
                });
            break;
        default: // default is bing aerial with labels
            layerMapPad = new OpenLayers.Layer.Bing({
                key: bingApiKey,
                type: "AerialWithLabels",
                name: "MapPad"
            });
            break;
    }

    map.addLayer(layerMapPad);
}

function initMapLayers () {
    TF.Rpc.Plots.PlotMap.initMapLayers()
    .done(function (data) {
        layers=data;
    })
    .fail(function (data) {

    });
}

function initMapControls(){
    jQuery('#map-filter').bind('click', function(){
        if(!isLoading){
            unselectAllButtons();
            jQuery('#map-filter').linkbutton('select');

            var obj = getValuesFilterPlots();

            TF.Rpc.Plots.PlotMap.initMap(obj)
            .done(function (data) {
                showMap();
                reloadAllLayers();
                initLegend(data);
            })
            .fail(function (data) {
                jQuery.messager.alert('Грешка', 'Възникна грешка при обработката на данните', 'warning');
            });
        }
        return false;
    });

    jQuery('#map-masiv').bind('click', function(){
        if(!isLoading){
            unselectAllButtons();
            jQuery('#map-masiv').linkbutton('select');
            jQuery('#map-contract').linkbutton('unselect');
            jQuery('#map-ownership-parts').linkbutton('unselect');

            var obj = getValuesFilterPlots();

            TF.Rpc.Plots.PlotMap.masivInit(obj)
            .done(function (data) {
                reloadAllLayers();
                initLegend(data);
            })
            .fail(function (data) {
                jQuery.messager.alert('Грешка', 'Възникна грешка при обработката на данните', 'warning');
            });
        }
        return false;
    });

    jQuery('#map-contract').bind('click', function(){
        if(!isLoading)
        {
            unselectAllButtons();

            var obj = getValuesFilterPlots();
            obj.include_ownership = jQuery('#display-ownership-plots').is(':checked');
            jQuery('#map-contract').linkbutton('select');
            jQuery('#map-ownership-parts').linkbutton('unselect');
            jQuery('#map-masiv').linkbutton('unselect');

            TF.Rpc.Plots.PlotMap.contractInit(obj)
            .done(function (data) {
                reloadAllLayers();
                initLegend(data);
            })
            .fail(function (data) {
                jQuery.messager.alert('Грешка', 'Възникна грешка при обработката на данните', 'warning');
            });
        }
        return false;
    });

    jQuery('#map-choose-plot').bind('click', function(){
    	if(!isLoading) {
    		var options = jQuery('#map-choose-plot').linkbutton('options');
        	if(!options.selected) {
        		jQuery('#map-choose-plot').linkbutton('select');
        		if(!options.disabled){
                    map.events.register('click', map, propertyWindowFunction);
                }
        	} else {
        		jQuery('#map-choose-plot').linkbutton('unselect');
        		map.events.unregister('click', map, propertyWindowFunction);
        	}
    	}
    	return false;
    });

	jQuery('#tool-kvs-zoom').bind('click', function() {
        initZoomToKvsPanel();
        jQuery('#win-choose-kvs-zoom').window('open');

        return false;
    });

	jQuery('#btn-choose-kvs-zoom').bind('click', function() {
        if (jQuery('#choose-kvs-zoom-ekate > input').combobox('getValue'))
        {
            var obj = new Object();
            obj.ekate = jQuery('#choose-kvs-zoom-ekate > input').combobox('getValue');

            TF.Rpc.Plots.PlotMap.getKvsExtent(obj)
            .done(function (data) {
                jQuery('#win-choose-kvs-zoom').window('close');
                zoomToKVS(data);
            })
            .fail(function (data) {

            });
        } else {
            jQuery.messager.alert('Грешка', 'Не е намерен КВС слой!');
        }
    });
}

function propertyWindowFunction(e) {
	bounds = map.getExtent();

    x1 = parseInt(e.xy.x);
    y1 = parseInt(e.xy.y);

    var obj = new Object();
    obj = {
        bbox: bounds.toBBOX(),
        x: x1,
        y: y1,
        width: map.size.w,
        height: map.size.h,
        layer: layers[0]['id'],
        plot_info: true
    };

    TF.Rpc.Plots.PlotMap.read(obj)
    .done(function (data) {
            initFillPlotInfo(data['plot_info']);
            initPlotsContractsGrid(data['id']);
            plotID = data['id'];
            jQuery('#win-plot-info').window('open');
    })
    .fail(function (data) {
        jQuery.messager.alert('Грешка',errorObj.getMessage(), 'warning');
    });

}

function initLayers(){

    boundsArray = [];
    for(var i=0;i<layers.length;i++){
        boundsArray[i] = new OpenLayers.Bounds.fromString(layers[i].extent).transform(
            new OpenLayers.Projection("EPSG:32635"),
            map.getProjectionObject()
        );
    }

    layerArray = [];
    for(var i=0;i<layers.length;i++)
    {
        if(layers[i].extent && layers[i].name)
        {
            const isSubUrl = wmsServer.indexOf('?') !== -1;
            const urlSeparator = isSubUrl ? '&' : '?';

            layerArray[i] = new OpenLayers.Layer.WMS(
                layers[i].name,
                `${wmsServer}${urlSeparator}map=${mapPath}${groupID}.map`,
                {
                    layers: layers[i].name,
                    format: 'image/png',
                    transparent: "true"
                //map: mapPath+userid+'.map'
                },{
                    displayInLayerSwitcher: false
                }
            );
        }
    }

    if (layers.length) map.addLayers(layerArray);

    if(layers.length)
    {
        map.zoomToExtent(boundsArray[0]);
    }
    else
    {
        map.zoomToExtent(new OpenLayers.Bounds.fromString("125190.6162, 4573142.7188, 631370.3273, 4887149.5823").transform(
            new OpenLayers.Projection("EPSG:32635"),
            map.getProjectionObject())
        );
    }
}

function reloadAllLayers() {
    for(var i=0;i<layerArray.length;i++) {
        layerArray[i].redraw(true);
    }
}

function initLegend(data)
{
    if(data['extent'])
    {
        if (data['extent'].indexOf(',') === -1) {
            var find = ' ',
                re = new RegExp(find, 'g');

            data['extent'] = data['extent'].replace(re, ', ');
        }
        map.zoomToExtent(new OpenLayers.Bounds.fromString(data['extent']).transform(
                new OpenLayers.Projection("EPSG:32635"),
                map.getProjectionObject()));
    }

    jQuery('#legend-tree').tree({
        data:data['colorarray'],
        formatter: function(node){
            if (node.ownership_field) {
                return "<input type='checkbox' style='float: left; margin-left: -15px; margin-top: 3px;' id='display-ownership-plots' /><div style='width:13px;height:13px;background-color:#"+node.color+";margin-top:3px;float:left;margin-right:3px;'></div>"+node.name;
            } else if(node.color) {
                return "<div style='width:13px;height:13px;background-color:#"+node.color+";margin-top:3px;float:left;margin-right:3px;'></div>"+node.name;
            }
            else {
                return node.text;
            }
        },
        onLoadSuccess: function () {
            jQuery('#display-ownership-plots').prop('checked', showOwnershipOnMap);
            jQuery('#display-ownership-plots').on('change', function () {
                if (jQuery('#display-ownership-plots').is(':checked')) {
                    showOwnershipOnMap = true;
                } else {
                    showOwnershipOnMap = false;
                }
                jQuery('#map-contract').trigger('click');
            });
        }
    });
}

function initZoomToKvsPanel() {
    jQuery('#choose-kvs-zoom-ekate > input').combobox({
        url: 'index.php?common-rpc=ekate-combobox',
        rpcParams: [{
            selected:true
        }],
        textField: 'text',
        valueField: 'ekate',
        editable: false,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#btn-choose-kvs-zoom  > a').linkbutton({iconCls: 'icon-zoom-full'});
}

function zoomToKVS(data) {
    if (data['extent']) {
        map.zoomToExtent(new OpenLayers.Bounds.fromString(data['extent']).transform(
                    new OpenLayers.Projection("EPSG:32635"),
                    map.getProjectionObject())
                );
    }
}
