Namespace('TF.Rpc.Plots');

var editPlotID;
var is_edit = false;
var drawImageObj = {};
var printImageObj = {};
var from_alert = false;

jQuery(function () {
	'use strict';
	jQuery('#kvs-applied-filters').filterWidget({});
	jQuery('#kvs-applied-filters').on('onclosed', 'a.remove-filter', function (e) {
		var filterData = getValuesFilterPlots();
		var target = (e.currentTarget),
			value = jQuery(target).data('value'),
			group = jQuery(target).data('group-ident'),
			oldData,
			position;

		oldData = filtersCriteria[group].split(',');
		position = oldData.indexOf(value.toString());
		oldData.splice(position, 1);
		filtersCriteria[group] = oldData.join();
		var removedTarget = {
			field: group,
			value: value
		};

		filterData.filter_action = 'remove_from_filter';
		filterData.removedTarget = removedTarget;
		initPlotsTree(1, filterData);
		renderFilter(false);

		if(jQuery("#kvs-applied-filters").is(':empty')) {
			collapseFilterArea();
		}
	});
});

function initPlotsTree(pageNumber, filterObj) {
	editPlotID = undefined;

	var page_number = 1;

	if (pageNumber != undefined)
		page_number = pageNumber;

	var plotsTree = jQuery('#plots-tree');

    if((filterObj && filterObj['from_alert'] != undefined && filterObj['from_alert'] == 1) || from_alert){
        delete(filterObj['plot_id']);
        filterObj['from_alert'] = from_alert = true;
    }

	var isTreeBound = plotsTree.data().hasOwnProperty('tree');
	if (isTreeBound) {
		plotsTree.tree({
			rpcParams: [filterObj],
			page: page_number
		});
		return;
	}

	plotsTree.tree({
		url: 'index.php?plots-rpc=plots-tree',
		rpcMethod: 'getTreePlots',
		animate: true,
		lines: true,
		sort: 'kad_ident',
		order: 'asc',
		page: page_number,
		rows: page_number,
		rpcParams: [filterObj],
		onSelect: function(node) {
			initPlotsContractsGrid(node.id, node.attributes);
			initPlotsHypothecsGrid(node.id);
			initPlotsInfo(node.attributes);
			//set widht and height for plot image
			var width = 328;
			var height = jQuery(window).innerHeight() - 412;

			jQuery('#plot-image').attr('src', 'themes/Main/loading.gif');

			drawImageObj = {
				gid: node.id,
				width: width,
				height: height,
				user_id: userid,
				forPrint: false
			};

			printImageObj = {
				gid: node.id,
				width: 615,
				height: 780,
				user_id: userid,
				forPrint: true
			};

			TF.Rpc.Plots.PlotsImage.generateImage(drawImageObj)
			.done(function (data) {
				jQuery('#plot-image').attr('src',data);
			})
			.fail(function (errorObj) {

			});
		},
		onLoadSuccess: function(node, data) {
			// endLoading();
			var roots = plotsTree.tree('getRoots');
			var total = 0;
			var limit = 30;
			if (roots.length) {
				if (editPlotID != undefined) {
					var node = plotsTree.tree('find', editPlotID);
					plotsTree.tree('select', node.target);
				}
				else {
					plotsTree.tree('select', roots[0].target);

				    if(plotsTree.tree('options').rpcParams[0].plot_id !== undefined) {
				    	//Попълване на данни за имот във филтрите, когато си попаднал на страницата чрез бутон "информация за имот"
				    	//за да показва при отваряне на картата само конкретния имот от дървото
		        		var selectedPlot = plotsTree.tree('getSelected');
		        		jQuery('#search-kad-ident').val(selectedPlot.attributes.kad_ident);
		        		plotsTree.tree('options').rpcParams[0].plot_id = undefined;
		    		}
				}

				total = roots[0]['attributes']['pagination']['total'];
				limit = roots[0]['attributes']['pagination']['limit'];
				//init pagination with total contract elements
				initPlotsPagination(total, limit);

				var paginationOptions = jQuery('#plots-tree-pagination').pagination('options');
				if(roots[0]['attributes']['currentFilterCount'] === 0 && paginationOptions.pageNumber === 1) {
					jQuery.messager.alert('Внимание', 'Не са открити записи за въведения филтър', 'warning');
				}
				jQuery('#btnmap').linkbutton('enable');
			}
            else
            {
                jQuery('#plot-image').attr('src', 'themes/Main/loading.gif');
				jQuery('#btnmap').linkbutton('disable');
                initPlotsPagination(1,1);
                jQuery.messager.alert('Внимание', 'Не са открити записи', 'warning');
            }
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function initPlotsPagination(total, limit) {
	jQuery('#plots-tree-pagination').pagination({
		showPageList: false,
		showRefresh: false,
		beforePageText: 'Стр.',
		displayMsg: '',
		total: total,
		pageSize: limit,
		onSelectPage: function(pageNumber, pageSize) {
			var obj = getValuesFilterPlots();
			if(!jQuery("#kvs-applied-filters").is(':empty')) {
				obj.filter_action = "add_to_filter";
			}
			initPlotsTree(pageNumber, obj);
		}
	});
}

function plotsFilter() {
	if(!jQuery("#kvs-applied-filters").is(':empty')) {
		jQuery.messager.confirm('Внимание', 'Резултатите от "Добави към филтър" ще бъдат загубени. Искате ли да продължите?', function (r) {
			if (r) {
				execFilter();
			}
			return false;
		});
	} else {
		execFilter();
	}
}

function execFilter() {
	var obj = getValuesFilterPlots();
	obj.filter_action = 'exec_filter';
	//initPlotsTree will select page 1 without trigger pagination select. Causing doubled request to laod tree plots and etc.
	//jQuery('#plots-tree-pagination').pagination('select', 1)
	filtersCriteria = getTextFilterPlots();
	initPlotsTree(1, obj);
	expandFilterArea();
	renderFilter(true);
}

function addPlotsToFilter() {
	'use strict';
	if(jQuery("#kvs-applied-filters").is(':empty')) {
		jQuery.messager.alert('Внимание', 'Все още няма филтрирани имоти. Използвайте първо бутона "Филтрирай", за да филтрирате имотите.', 'warning');
		return;
	}
	jQuery('#plots-tree-pagination').pagination('select', 1)
	var filterData = getValuesFilterPlots();
	filterData.filter_action = 'add_to_filter';

	initPlotsTree(1, filterData);
	renderFilter(true);
}

function clearPlotsFilter() {
	'use strict';
	var filterData = {};
	clearFilterPlotValues();
	collapseFilterArea();
	jQuery('#plots-tree-pagination').pagination('select', 1)
	filterData.filter_action = 'clear_filter';
	filterData.plot_statuses = ['Active'];
	initPlotsTree(1, filterData);
}

function expandFilterArea() {
	jQuery('#plots-filters-layout').layout('resize', {width: 1070});

	jQuery('#win-plots-filter').window('resize', {
		width: getZoomedWindowWidth(1085)
	}).window('center');
	jQuery("#plots-filters-layout").layout('expand', 'west');
}

function collapseFilterArea() {
	jQuery("#kvs-applied-filters").empty();
	jQuery('#plots-filters-layout').layout('resize', {width: 920});
	jQuery('#win-plots-filter').window('resize', {
		width: getZoomedWindowWidth(935)
	}).window('center');
	jQuery("#plots-filters-layout").layout('collapse', 'west');
}

var filtersCriteria = [];

function renderFilter(add_action) {
	'use strict';
	var filterData = getTextFilterPlots();
	if (add_action) {
		filterData.filter_action = 'add_to_filter';
		appendToFiltersCriteria(filterData);
	}
	let filterValues = getComboboxValuesFilterPlots();
	jQuery('#kvs-applied-filters').filterWidget('populate', filtersCriteria, filterValues);
}

function appendToFiltersCriteria(filterData) {
	'use strict';
	var key,
		filterKeys = [],
		tmp,
		tmpArr;

	for (key in filterData) {
		filterKeys.push(key);
	}

	filterKeys.forEach(function (key) {
		if (typeof filtersCriteria[key] === 'object') {
			if (typeof filterData[key] === 'object') {
				filterData[key].forEach(function (insideKey) {
					if (filterData[key][insideKey] !== '' && filterData[key][insideKey] !== 'Всички') {
						filtersCriteria[key] += ',' + filterData[key][insideKey];
					}
				});
			} else {
				if (filterData[key] !== '') {
					filtersCriteria[key] += ',' + filterData[key];
				}
			}
		} else {
			if (filterData[key] !== '' && filterData[key] !== 'Всички') {
				tmp = filterData[key];
				filtersCriteria[key] += ',' + tmp;
			}
		}
		tmpArr = filtersCriteria[key].split(',');
		filtersCriteria[key] = _.uniq(tmpArr);
		filtersCriteria[key] = _.without(filtersCriteria[key], '');
		filtersCriteria[key] = filtersCriteria[key].join();
	});
}

jQuery(function() {

    jQuery('#win-edit-plot').window({
        onBeforeOpen: function () {
            if (tinymce.editors.length <= 1) {
                tinymce.init({
                    selector: '#ep-comment-textarea',
                    theme: "modern",
                    language: 'bg_BG',
                    invalid_elements: "script",
                    relative_urls: false,
                    statusbar: false,
                    toolbar: "forecolor",
                    menubar: false,
                    style_formats_merge: true,
                    forced_root_block: false,
                    plugins: "textcolor colorpicker"
                }).then(function (editor) {
                    jQuery('#ep-comment-textarea_ifr').height(60);
                    jQuery(editor[0].editorContainer).width(202).height(96)
                    .css('margin-top','5px')
                    .css('padding','1px')
                });
            }
        }
    });

    jQuery('#btnmap').bind('click', function() {
		if (jQuery('#btnmap').linkbutton('options').disabled) {
			return;
		}

		unselectAllButtons();
		var obj = getValuesFilterPlots();
		TF.Rpc.Plots.PlotMap.contractInit(obj)
		.done(function (data) {
			showMap();
			reloadAllLayers();
			initLegend(data);
		})
		.fail(function (data) {
			jQuery.messager.alert('Грешка', 'Възникна грешка при обработката на данните', 'warning');
		});
		jQuery('#map-contract').linkbutton('select');
	});

	jQuery('#btnplotsreports').bind('click', function() {
        window.location = 'index.php?page=Reports.Home';
	});

	jQuery('#edit-plot').bind('click', function() {
		if (hasPlotRightsRW) {

			var getSelected = jQuery('#plots-tree').tree('getSelected');
			if (getSelected) {
				editPlotID = getSelected.id;

				jQuery('#win-edit-plot').window('resize', {
					height: getZoomedWindowHeight(690),
					width: 730
				}).window('center');

				if (getSelected.attributes.payment >0){
                    jQuery.messager.confirm('Потвърждение', 'Извършените промени биха могли да се отразят в справките за изплатени ренти.', function(r) {
                        if (r) {
                            TF.Rpc.Plots.PlotsTree.load(editPlotID)
                                .done(function (dataObj) {
                                    jQuery('#win-edit-plot').window('open');
                                    initEditFields(dataObj);
                                    setEditFieldsData(dataObj)
                                })
                                .fail(function (errorObj) {
                                    RpcErrorHandler.show(errorObj);
                                });
                        	}
                    });
				}else{
                    TF.Rpc.Plots.PlotsTree.load(editPlotID)
                        .done(function (dataObj) {
                            jQuery('#win-edit-plot').window('open');
                            initEditFields(dataObj);
                            setEditFieldsData(dataObj)
                        })
                        .fail(function (errorObj) {
                            RpcErrorHandler.show(errorObj);
                        });
				}


			} else {
				jQuery.messager.alert('Грешка', 'Моля изберете имот.');
			}
		} else {
			messagerPlotsWriteRights();
		}
	});

	jQuery('#multi-edit-plot').bind('click', function() {
		if (hasPlotRightsRW) {
			initMultiEditFields();
			jQuery('#win-multiedit').window('open');
		} else {
			messagerPlotsWriteRights();
		}
	});

	jQuery('#multi-edit-plot-article-37').bind('click', function() {
		if (hasPlotRightsRW) {
			jQuery('#win-multiedit-article-37').window('open');
		} else {
			messagerPlotsWriteRights();
		}
	});

	jQuery('#filter-plot').bind('click', function() {
		jQuery('#grouping-options-fields').hide();
		report_filter = undefined;
		if(!jQuery("#kvs-applied-filters").is(':empty')) {
			jQuery('#win-plots-filter').window('resize', {height: getZoomedWindowHeight(660), width: getZoomedWindowWidth(1085)}).window('open').window('center');
		} else {
			jQuery('#plots-filters-layout').layout('resize', {width: 920});
			jQuery('#win-plots-filter').window('resize', {height: getZoomedWindowHeight(660), width: getZoomedWindowWidth(940)}).window('open').window('center');
		}
	});
	jQuery('#is-edit-plot').bind('click', function() {
        var buttonOptions = jQuery(this).linkbutton('options');
        jQuery('#is-edit-plot .'+buttonOptions.iconCls).animateRotate(360);

		if (typeof(is_edit) == "undefined" || !is_edit){
			is_edit = true;
			jQuery('#is-edit-plot').toggle = true;
		}else{
			is_edit = false;
			jQuery('#is-edit-plot').toggle = false;
		}
		plotsFilter();
	});

    jQuery('#plot-history').bind('click', function() {
        var buttonOptions = jQuery(this).linkbutton('options');
        jQuery('#plot-history .'+buttonOptions.iconCls).animateRotate(360, function(){
            initPlotsHistoryGrid(jQuery('#plots-tree').tree('getSelected').id);
            jQuery('#win-plots-history').window('open');
        });
	});

	jQuery('#cancel-filter-plot').bind('click', function() {
		clearPlotsFilter();
        is_edit = false;
        jQuery('#is-edit-plot').toggle = false;
        jQuery('#is-edit-plot').linkbutton('unselect');

		jQuery('#plots-tree-pagination').pagination('select', 1);
	});

	jQuery('#print-plot').bind('click', function() {
		var request = printImageObj;
		request.width  = 1300;
		request.height = 700;
		request.forPrint = true;
		TF.Rpc.Plots.PlotsImage.generateImage(request)
		.done(function (data) {
			jQuery('#print-plot').attr('href',data);
			printPlot(data);
		})
		.fail(function (errorObj) {

		});

		return false;
	});
});

function printPlot(data) {

	var url = data,
		plotData = jQuery('#plots-tree').tree('getSelected').attributes,
		ekate = plotData.ekate ? plotData.ekate : '-',
		masiv = plotData.masiv ? plotData.masiv : '-',
		number = plotData.number ? plotData.number : '-',
		area_type = plotData.area_type ? plotData.area_type : '-',
		mestnost = plotData.mestnost ? plotData.mestnost : '-',
		category = plotData.category ? plotData.category : '-',
		used_area = plotData.used_area ? plotData.used_area : '-',
		area_kvs = plotData.area_kvs ? plotData.area_kvs : '-',
		ime_subekt = plotData.ime_subekt ? plotData.ime_subekt : '-',

		html = '<style>@page{size: landscape;}</style><center>' +
			'<div style="border: none; width: 1300px; margin: 0 auto;">' +
			'<center>' +
			'<table border="1" width="1302" cellpadding="5" cellspacing="0" style="margin-bottom: 20px;">' +
			'<tr>' +
			'<td style="width: 100px">ЕКАТЕ</td>' +
			'<td style="width: 100px">Масив</td>' +
			'<td style="width: 100px">Имот</td>' +
			'<td style="width: 150px">НТП</td>' +
			'<td style="width: 150px">Местност</td>' +
			'<td style="width: 130px">Категория</td>' +
			'<td style="width: 130px">Изп. площ (дка)</td>' +
			'<td style="width: 130px">Обща площ (дка)</td>' +
			'<td style="width: 230px">Собственик (ОСЗ)</td>' +
			'</tr>' +
			'<tr>' +
			'<td>' + ekate + '</td>' +
			'<td>' + masiv + '</td>' +
			'<td>' + number + '</td>' +
			'<td>' + area_type + '</td>' +
			'<td>' + mestnost + '</td>' +
			'<td>' + category + '</td>' +
			'<td>' + used_area + '</td>' +
			'<td>' + area_kvs + '</td>' +
			'<td>' + ime_subekt + '</td>' +
			'</tr>' +
			'</table>' +
			'<img src="' + url + '" style="border: 1px solid #000000"/>' +
			'</center>' +
			'</div>';

	jQuery('body').append('<iframe id="printf" name="printf"></iframe>');
	var newWin = window.frames['printf'];
	newWin.document.write('<body onload=window.print()>'+html+'</body>');
	newWin.document.close();
	setTimeout(function () {
		jQuery('#printf').remove();
	}, 1000);
}

/**
 * Set the values of the fields in the Edit Plot Window, that are returned from the database
 * @param {array} data
 * @return {void}
 */
function setEditFieldsData(data)
{
	jQuery('#ep-kad-ident > input').val(data.kad_ident);
	jQuery('#ep-old-kad-ident > input').val(data.old_kad_ident);
	jQuery('#ep-ekatte > input').val(data.ekate);
	jQuery('#ep-masiv > input').val(data.masiv);
	jQuery('#ep-imot > input').val(data.number);
	jQuery("#ep-area > input").val(parseFloat(data.document_area).toFixed(3));
	jQuery('#ep-mestnost > input').val(data.mestnost);
	jQuery('#ep-block > input').val(data.block);
	jQuery('#ep-category > input').combobox('setValue', data.category);
	jQuery('#ep-ntp > input').combobox('setValue', data.area_type);
	jQuery('#irrigated-area').prop('checked', data.irrigated_area);
	jQuery('#ep-comment > textarea').val(data.comment);

	let ntpAdditionalCode;
	var ntps = jQuery('#ep-ntp > input').combobox('getData');
	for (let ntp of ntps) {
		if(ntp.id === data.area_type){
			jQuery('#ep-ntp > input').combobox('setValue', data.area_type);
			break;
		}
		if(ntp.additionalCodes && ntp.additionalCodes.includes(data.area_type)){
			ntpAdditionalCode = ntp.id;
		}
	}

	if(ntpAdditionalCode){
		jQuery('#ep-ntp > input').combobox('setValue', ntpAdditionalCode);
	}

	jQuery('#ep-include').prop('checked', false);
	jQuery('#ep-participate').prop('checked', false);
	jQuery('#ep-white-spots').prop('checked', false);

	if (data.usable)
	{
		jQuery('#usable-plot').prop('checked',true);
	}
	else
	{
		jQuery('#unusable-plot').prop('checked',true);
	};

	if(data.include)
	{
		jQuery('#ep-include').prop('checked',true);
	}
	if(data.participate)
	{
		jQuery('#ep-participate').prop('checked',true);
	}
	if(data.white_spots)
	{
		jQuery('#ep-white-spots').prop('checked',true);
	}
}

/**
 * Initiate the fields in the Edit Plot Window and monitor for changes in the fields
 * @param  {array} data
 * @return {void}
 */
function initEditFields(data)
{
	var categoryComboboxData    = ComboboxData.PlotCategoryCombobox,
		plotNTPComboboxData     = ComboboxData.PlotNTPCombobox,
		farmingComboboxData     = ComboboxData.FarmingCombobox,
		farmingYearComboboxData = ComboboxData.FarmingYearCombobox;

    newCategoryComboboxData = [];
    categoryComboboxData.forEach(function (el) {

        if (el.id !== "" && el.id !== "-1") {
            newCategoryComboboxData.push(el);
        }
    });

    newPlotNTPComboboxData = [];
    plotNTPComboboxData.forEach(function (el) {
        if (el.id !== "" && el.id !== "-1") {
            newPlotNTPComboboxData.push(el);
        }
    });

    newFarmingComboboxData = [];
    farmingComboboxData.forEach(function (el) {
        if (el.id !== "") {
            newFarmingComboboxData.push(el);
        }
    });
    newFarmingComboboxData[0].selected = true;

    newFarmingYearComboboxData = [];
    farmingYearComboboxData.forEach(function (el) {
        if (el.id !== "") {
            newFarmingYearComboboxData.push(el);
        }
    });
    newFarmingYearComboboxData[0].selected = true;

	jQuery('#ep-category > input').combobox({
		data: newCategoryComboboxData,
		valueField: 'id',
		textField: 'name',
		editable: false,
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

	jQuery('#ep-ntp > input').combobox({
		data: newPlotNTPComboboxData,
		valueField: 'id',
		textField: 'name',
		editable: false,
		disabled: true,
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});


    numLengthbox();
	jQuery('#ep-ekatte > input').validatebox({
        required: false,
		editable: false,
		validType: 'minLength[1]',
        missingMessage: 'Моля въведете екате.'
    });

	jQuery('#ep-masiv > input').validatebox({
        required: true,
		validType: 'minLength[1]',
        missingMessage: 'Моля въведете масив.'
    });

	jQuery('#ep-imot > input').validatebox({
        required: true,
		validType: 'minLength[1]',
        missingMessage: 'Моля въведете имот.'
    });

	jQuery('#ep-kad-ident > input').on('input', function() {
		var kad_ident = jQuery('#ep-kad-ident > input').val();

		if (kad_ident.length < 9)
		{
			return false;
		}

		var separator = determineKadIdentSeparator(kad_ident);

		var ident_split = kad_ident.split(separator);

		jQuery('#ep-ekatte > input').val(ident_split[0]);
		jQuery('#ep-masiv > input').val(ident_split[1]);
		jQuery('#ep-imot > input').val(ident_split[2]);
	});

	jQuery('#ep-ekatte > input').on('input', function()
	{
		changeEditFieldKadIdentValue();
	});

	jQuery('#ep-masiv > input').on('input', function()
	{
		changeEditFieldKadIdentValue();
	});

	jQuery('#ep-imot > input').on('input', function()
	{
		changeEditFieldKadIdentValue();
	});
}

/**
 * Updates the selected plot with the new parameters
 * @return {void}
 */
function updatePlot() {

	var getSelected = jQuery('#plots-tree').tree('getSelected');
	if (getSelected) {

		//form values
		var params = getFormValuesEditPlots();

		const {ekate,masiv,number} = params;

		if (ekate.length == 0 || masiv.length == 0 || number.length == 0) {
			jQuery.messager.alert('Грешка', 'Екате, имот и масив са задължителни полета.');
			return;
		}

		params.gid = getSelected.id;

		TF.Rpc.Plots.PlotsTree.update(params).done(function(data) {
				jQuery('#plots-tree').tree('reload');
				jQuery('#win-edit-plot').window('close');
			})
		.fail(function (errorObj) {
			RpcErrorHandler.show(errorObj);
		});

	} else {
		jQuery.messager.alert('Грешка', 'Моля изберете имот.');
	}
}

/**
 * Get all the values from the Edit Plot Window to be sent as a parameter to the set values funcion
 * @return {array}
 */
function getFormValuesEditPlots() {

	var params = {
		kad_ident: jQuery('#ep-kad-ident > input').val(),
		old_kad_ident: jQuery('#ep-old-kad-ident > input').val(),
		ekate: jQuery('#ep-ekatte > input').val(),
		masiv: jQuery('#ep-masiv > input').val(),
		number: jQuery('#ep-imot > input').val(),
		document_area: jQuery('#ep-area > input').val(),
		mestnost: jQuery('#ep-mestnost > input').val(),
		category: jQuery('#ep-category > input').combobox('getValue'),
		area_type: jQuery('#ep-ntp > input').combobox('getValue'),
		usable: jQuery('#usable-plot').is(':checked') ? true : false,
		irrigated_area: jQuery('#irrigated-area').is(':checked') ? true : false,
		include: jQuery('#ep-include').is(':checked') ? true : false,
		participate: jQuery('#ep-participate').is(':checked') ? true : false,
		white_spots: jQuery('#ep-white-spots').is(':checked') ? true : false,
		comment: tinyMCE.get('ep-comment-textarea').getContent(),
		block: jQuery('#ep-block > input').val(),
	};
	return params;
}

function determineKadIdentSeparator(kad_ident)
{
	var dash_separator = kad_ident.match(/-/g);
	var dot_separator = kad_ident.match(/\./g);
	var separator = '-';

	//check if fulled info is correct and determine separator type
	if (dash_separator != null)
	{
		if (dash_separator.length < 2)
		{
			return separator;
		}
	} else if (dot_separator != null)
	{
		if (dot_separator.length < 2)
		{
			return separator;
		} else {
			separator = '.';
		}
	} else {
		return separator;
	}

	return separator;
}

function changeEditFieldKadIdentValue()
{
	var ekate = jQuery('#ep-ekatte > input').val();
	var masiv = jQuery('#ep-masiv > input').val();
	var imot = jQuery('#ep-imot > input').val();

	var kad_ident = jQuery('#ep-kad-ident > input').val();
	var separator = determineKadIdentSeparator(kad_ident);
	var ident_split = kad_ident.split(separator);

	ident_split[0] = ekate;
	ident_split[1] = masiv;
	ident_split[2] = imot;
	var ident_merge = ident_split.join(separator);

	jQuery('#ep-kad-ident > input').val(ident_merge);
}

function initMultiEditFields() {
	var categoryComboboxData = ComboboxData.PlotCategoryCombobox,
		plotNTPComboboxData  = ComboboxData.PlotNTPCombobox,
		farmingComboboxData  = ComboboxData.FarmingCombobox,
		farmingYearComboboxData = ComboboxData.FarmingYearCombobox;

	newFarmingComboboxData = [];
    farmingComboboxData.forEach(function (el) {
        if (el.id !== "") {
            newFarmingComboboxData.push(el);
        }
    });
    newFarmingComboboxData[0].selected = true;

    newFarmingYearComboboxData = [];
    farmingYearComboboxData.forEach(function (el) {
        if (el.id !== "") {
            newFarmingYearComboboxData.push(el);
        }
    });

    newFarmingYearComboboxData[0].selected = true;

    newCategoryComboboxData = [];
    categoryComboboxData.forEach(function (el) {
        if (el.id !== "") {
            newCategoryComboboxData.push(el);
        }
    });

    newPlotNTPComboboxData = [];
    plotNTPComboboxData.forEach(function (el) {
        if (el.id !== "") {
            newPlotNTPComboboxData.push(el);
        }
    });

	jQuery('#me-plot-mestnost > input').val(' -');
	jQuery('#me-plot-category > input').combobox({
		data: newCategoryComboboxData,
		textField: 'name',
		valueField: 'id',
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
	jQuery('#me-plot-ntp > input').combobox({
		data: newPlotNTPComboboxData,
		textField: 'name',
		valueField: 'id',
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function getFormValuesMultiEditPlots() {
	var response = {
		filterParams: getValuesFilterPlots(),
		mestnost: jQuery('#me-plot-mestnost > input').val(),
		usable: jQuery('#me-usable-plot').is(':checked'),
		irrigated_area: jQuery('#me-irrigated-area').is(':checked'),
		category: jQuery('#me-plot-category > input').combobox('getValue'),
		area_type: jQuery('#me-plot-ntp > input').combobox('getValue')
	}
	return response;
}

function getFormValuesMultiEditArticle37() {
	var response = {
		filterParams: getValuesFilterPlots(),
		include: jQuery('#me-include').is(':checked'),
		participate: jQuery('#me-participate').is(':checked'),
		white_spots: jQuery('#me-white-spots').is(':checked')
	}
	return response;
}

function multiUpdateConfirmDialog(callback) {
	jQuery.messager.confirm('Потвърждение', 'Това действие ще промени всички данни, показани в таблицата. Сигурни ли сте, че искате да продължите?', function(r) {
		if (r) {
			callback();
		}
	});
}

function multiUpdatePlots() {
	multiUpdateConfirmDialog(multiUpdatePlotsExecute)
}

function multiUpdatePlotsExecute() {
	var params = getFormValuesMultiEditPlots();

	TF.Rpc.Plots.PlotsTree.multiEdit(params).done(function(data) {
		jQuery('#plots-tree').tree('reload');
		jQuery('#win-multiedit').window('close');
	})
	.fail(function (errorObj) {
		RpcErrorHandler.show(errorObj);
	});
}

function multiUpdateArticle37() {
	multiUpdateConfirmDialog(multiUpdatePlotsArticle37Execute);
}

function multiUpdatePlotsArticle37Execute() {
	var params = getFormValuesMultiEditArticle37();
	params.has_filters = false;

	if(!jQuery("#kvs-applied-filters").is(':empty')) {
		params.has_filters = true;
	}

	TF.Rpc.Plots.PlotsTree.multiEdit(params).done(function (data) {
		jQuery('#plots-tree').tree('reload');
		jQuery('#win-multiedit-article-37').window('close');
	})
	.fail(function (errorObj) {
		RpcErrorHandler.show(errorObj);
	});
}

