function initDeclPMLChosenGrid(year, farming) {
    declYear = year;
    declFarming = farming;

	jQuery('#anketna-karta-msg').hide();
	jQuery('#declaration-chosen-tables').datagrid({
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		iconCls: 'icon-edit-geometry',
		title: chosenHeading,
		fit: true,
		fitColumns: true,
		showFooter: true,
		border: false,
		url: 'index.php?plots-rpc=declaration-chosen-grid',
		rpcParams: [{
			year: declYear,
			farming: declFarming
		}],
		rpcMethod: 'declPML',
		sortName: 'c.id',
		sortOrder: 'asc',
		idField: 'pc_rel_id',
		frozenColumns: [
			[
				{
					field: 'ck',
					checkbox: true
				}
			]
		],
		columns: [
			[
				{
					field: 'kad_ident',
					title: '<b>Идентификатор</b>',
					sortable: true,
					width: 130
				}, {
					field: 'area',
					title: '<b>Площ<br/>(дка)</b>',
					sortable: true,
					width: 50
				}, {
					field: 'area_type',
					title: '<b>НТП</b>',
					sortable: true,
					width: 150
				}, {
					field: 'c_num',
					title: '<b>Договор</b>',
					sortable: true,
					width: 70
				}, {
					field: 'c_type',
					title: '<b>Тип<br/>договор</b>',
					sortable: true,
					width: 90
				}
			]
		],
		rownumbers: true,
		toolbar: '#decl70-chosen-toolbar',
		onBeforeLoad: function() {
		},
		onLoadSuccess: function(){
			initDeclPMLChooseGrid(year, farming);
		},
		rowStyler: function(index, row) {
			if (!row.participate && !row.include && !row.white_spots) {
				return 'background-color:#ff0000;';
			}
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}
function initDeclPMLChooseGrid(year, farming) {
    declYear = year;
    declFarming = farming;

	jQuery('#declaration-choose-tables').datagrid({
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		iconCls: 'icon-edit-geometry',
		title: chooseHeading,
		pageSize: 10,
		fit: true,
		fitColumns: true,
		showFooter: true,
		url: 'index.php?plots-rpc=declaration-choose-grid',
		rpcParams: [{
			year: declYear,
			farming: declFarming
		}],
		rpcMethod: 'declPML',
		sortName: 'c.id',
		sortOrder: 'asc',
		idField: 'pc_rel_id',
		frozenColumns: [
			[
				{
					field: 'ck',
					checkbox: true
				}
			]
		],
		columns: [
			[
				{
					field: 'kad_ident',
					title: '<b>Идентификатор</b>',
					sortable: true,
					width: 130
				}, {
					field: 'area',
					title: '<b>Площ<br/>(дка)</b>',
					sortable: true,
					width: 50
				}, {
					field: 'area_type',
					title: '<b>НТП</b>',
					sortable: true,
					width: 150
				}, {
					field: 'c_num',
					title: '<b>Договор</b>',
					sortable: true,
					width: 70
				}, {
					field: 'c_type',
					title: '<b>Тип<br/>договор</b>',
					sortable: true,
					width: 90
				}
			]
		],
		pagination: true,
		rownumbers: true,
		toolbar: [{
				id: 'btnaddtoanketnakarta',
				text: 'Добавяне',
				iconCls: 'icon-add',
				handler: function() {
					var getChecked = jQuery('#declaration-choose-tables').datagrid('getChecked');
					if (getChecked[0]) {
						var idArray = [];

						for (var i = 0; i < getChecked.length; i++) {
							idArray[i] = getChecked[i]['pc_rel_id'];
						}

						TF.Rpc.Plots.DeclarationChosenGrid.addToDeclaration(idArray)
						.done(function () {
							jQuery('#declaration-choose-tables').datagrid('loadRpc');
							jQuery('#declaration-chosen-tables').datagrid('loadRpc');
						})
						.fail();

					} else {
						jQuery.messager.alert('Грешка', 'Моля изберете имоти, които да бъдат добавени към декларацията.');
					}
				}
			}, {
				id: 'btnfilteranketnakartaadd',
				text: 'Филтър',
				iconCls: 'icon-filter',
				handler: function() {
					jQuery('#win-anketna-karta-filter').window('open');
				}
			}, {
				id: 'btnfilteranketnakartaclear',
				text: 'Покажи всички',
				iconCls: 'icon-clear-filter',
				handler: function() {
					clearAnketnaKartaFilter();
				}
			}
		],
		onBeforeLoad: function() {
			jQuery('#declaration-choose-tables').datagrid('clearChecked');
		},
		rowStyler: function(index, row) {
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}
