/*jslint browser: true*/
/*global jQuery, EasyUIRPCLoaders.EasyUIGridCustomLoader, onComboMultiSelect, dateboxWithClearButton,todayDate, ComboboxData */
var is_edit = is_edit || undefined;

function disableMapFilterDateBoxes(keepFarming) {
    'use strict';
    jQuery('#search-date-from').datebox('disable');
    jQuery('#search-date-to').datebox('disable');
    jQuery('#search-start-date').datebox('disable');
    jQuery('#search-due-date-to').datebox('disable');
    jQuery('#search-date-from').datebox('clear');
    jQuery('#search-date-to').datebox('clear');
    jQuery('#search-start-date').datebox('clear');
    jQuery('#search-due-date-to').datebox('clear');
    jQuery('#search-contract-type').combobox('reset');
    jQuery('#search-contract-status').combobox('reset');
    if (!keepFarming) {
        jQuery('#search-farming').combobox('reset');
    }
}
function resetMapFilterDateBoxes() {
    'use strict';
    jQuery('#search-date-from').datebox('enable');
    jQuery('#search-date-to').datebox('enable');
    jQuery('#search-start-date').datebox('enable');
    jQuery('#search-due-date-to').datebox('enable');
}
function initFilterComponents(forMap) {
    'use strict';
    var contractTypeComboboxData   = ComboboxData.ContractTypeCombobox,
        ekateComboboxData          = ComboboxData.EkateCombobox,
        contractStatusComboboxData = ComboboxData.ContractStatusCombobox,
        farmingComboboxData        = ComboboxData.FarmingCombobox,
        plotNTPComboboxData        = ComboboxData.PlotNTPCombobox,
        mestnostComboboxData       = ComboboxData.MestnostCombobox,
        categoryComboboxData       = ComboboxData.PlotCategoryCombobox,
        irrigatedAreaComboboxData  = ComboboxData.IrrigatedAreaCombobox;

    contractTypeComboboxData[0].selected   = true;
    ekateComboboxData[0].selected          = true;
    contractStatusComboboxData[0].selected = true;
    farmingComboboxData[0].selected        = true;
    plotNTPComboboxData[0].selected        = true;
    categoryComboboxData[0].selected       = true;
    irrigatedAreaComboboxData[0].selected  = true;
    /*********************
      Defining dateboxes
     *********************/

    jQuery('#search-date-from').datebox({
        buttons: dateboxWithClearButton
    });
    jQuery('#search-date-to').datebox({
        buttons: dateboxWithClearButton
    });
    jQuery('#search-start-date').datebox({
        buttons: dateboxWithClearButton
    });
    jQuery('#search-due-date-to').datebox({
        buttons: dateboxWithClearButton
    });

    /*********************
      Defining comboboxes
     *********************/

    jQuery('#search-contract-type').combobox({
        editable: false,
        data: contractTypeComboboxData,
        valueField: 'id',
        textField: 'name',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-contract-status').combobox({
        data: contractStatusComboboxData,
        editable: false,
        valueField: 'id',
        textField: 'name',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-farming').combobox({
        data: farmingComboboxData,
        editable: false,
        valueField: 'id',
        textField: 'name',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-area-type').combobox({
        data: plotNTPComboboxData,
        editable: false,
        valueField: 'id',
        textField: 'name',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-mestnost').combobox({
        data: mestnostComboboxData,
        valueField: 'mestnost',
        textField: 'text',
        value: '',
        filter: function (q, row) {
            var opts = jQuery(this).combobox('options'),
                text = row[opts.textField].toLowerCase(),
                value = row[opts.valueField],
                find = q.toLowerCase();
            if (text.indexOf(find) !== -1 || value.indexOf(find) !== -1) {
                return true;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-category').combobox({
        data: categoryComboboxData,
        editable: false,
        valueField: 'id',
        textField: 'name',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-irrigated-area').combobox({
        data: irrigatedAreaComboboxData,
        editable: false,
        valueField: 'value',
        textField: 'label',
        multiple: false,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-ekatte').combobox({
        data: ekateComboboxData,
        valueField: 'ekate',
        textField: 'text',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        filter: function (q, row) {
            var opts = jQuery(this).combobox('options'),
                text = row[opts.textField].toLowerCase(),
                value = row[opts.valueField],
                find = q.toLowerCase();
            if (text.indexOf(find) !== -1 || value.indexOf(find) !== -1) {
                return true;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-participation').combobox({
        editable: false,
        data: [{
            "value": '',
            "text": "Всички",
            "selected": true
        }, {
            "value": 'without',
            "text": "Без зададено желание"
        }, {
            "value": 'participate',
            "text": "Да"
        }, {
            "value": 'no_participate',
            "text": "Не"
        }, {
            "value": 'white_spots',
            "text": "Заявен като бяло петно"
        }],
        valueField: 'value',
        textField: 'text',
        multiple: false
    });


    /****************************
      Defining filter visibility
     ***************************/
    if (forMap) {
        jQuery('#plot-filter-archive-plot').show();
        jQuery('#additional-plot-filter-options-buttons').show();
        jQuery('#plot-filter-select-filtered-plots').show();
        jQuery('#select-filtered-plots-group').show();
        jQuery('#grouping-options-fields').hide();
        jQuery('#win-plots-filter').window('resize', {width: 1060});
    } else {
        jQuery('#plot-filter-archive-plot').hide();
        jQuery('#additional-plot-filter-options-buttons').hide();
        jQuery('#plot-filter-select-filtered-plots').hide();
        jQuery('#select-filtered-plots-group').hide();
        jQuery('#grouping-options-fields').hide();
        jQuery('#win-plots-filter').window('resize', {width: 1060});
        setGroupingCheckboxes();
    }
}

function getValuesFilterPlots() {
    'use strict';
    var obj = {};
    
    let is_edited = is_edit !== undefined ? is_edit : false;
    obj.plot_statuses = is_edited ? ['Archived'] : ['Active'];

    obj.select_filtered = jQuery('#extended-select-filtered-plots').is(':checked');

    if(jQuery('#search-area-type').combobox('getValue')){
        obj.area_type = jQuery('#search-area-type').combobox('getValues');
    }
    if(jQuery('#search-mestnost').combobox('getValue')){
        obj.mestnost = jQuery('#search-mestnost').combobox('getValue');
    }
    if(jQuery("#search-block").textbox("getValue")){
        obj.block = jQuery("#search-block").textbox("getValue");
    }
    if(jQuery('#search-irrigated-area').combobox('getValue')){
        obj.irrigated_area = jQuery('#search-irrigated-area').combobox('getValue')
    }
    if(jQuery('#search-category').combobox('getValue')){
        obj.category = jQuery('#search-category').combobox('getValues');
    }
    if(jQuery('#search-kad-ident').textbox("getValue")){
        obj.kad_ident = jQuery('#search-kad-ident').textbox("getValue");
    }
    if(jQuery('#search-ekatte').combobox('getValue')){
        obj.ekate = jQuery('#search-ekatte').combobox('getValues');
    }
    if(jQuery('#search-masiv').textbox("getValue")){
        obj.masiv = jQuery('#search-masiv').textbox("getValue");
    }
    if(jQuery('#search-number').textbox("getValue")){
        obj.number = jQuery('#search-number').textbox("getValue");
    }
    if(jQuery('#search-cnum').textbox("getValue")){
        obj.cnum = jQuery('#search-cnum').textbox("getValue");
        obj.cnum_exact = jQuery('#search-cnum-exact').is(':checked');
    }
    if(jQuery('#search-contract-type').combobox('getValue')){
        obj.contract_type = jQuery('#search-contract-type').combobox('getValues');
    }
    if(jQuery('#search-contract-status').combobox('getValue')){
        obj.contract_status_text = jQuery('#search-contract-status').combobox('getValue');
    }
    if(jQuery('#search-farming').combobox('getValue')){
        obj.farming = jQuery('#search-farming').combobox('getValues');
    }
    if(jQuery('#search-date-from').datebox('getValue')){
        obj.date_from = jQuery('#search-date-from').datebox('getValue');
    }
    if(jQuery('#search-date-to').datebox('getValue')){
        obj.date_to = jQuery('#search-date-to').datebox('getValue');
    }
    if(jQuery('#search-start-date').datebox('getValue')){
        obj.start_date = jQuery('#search-start-date').datebox('getValue');
    }
    if(jQuery('#search-due-date-to').datebox('getValue')){
        obj.due_date = jQuery('#search-due-date-to').datebox('getValue');
    }
    if(jQuery('#search-owner-name').textbox("getValue")){
        obj.owner_name = jQuery('#search-owner-name').textbox("getValue");
    }
    if(jQuery('#search-owner-egn').textbox("getValue")){
        obj.owner_egn = jQuery('#search-owner-egn').textbox("getValue");
    }
    if(jQuery('#search-heritor-name').textbox("getValue")){
        obj.heritor_name = jQuery('#search-heritor-name').textbox("getValue");
    }
    if(jQuery('#search-heritor-egn').textbox("getValue")){
        obj.heritor_egn = jQuery('#search-heritor-egn').textbox("getValue");
    }
    if(jQuery('#search-owner-ime-subekt').textbox("getValue")) {
        obj.ime_subekt = jQuery('#search-owner-ime-subekt').textbox("getValue");
    }
    if(jQuery('#search-owner-egn-subekt').textbox("getValue")){
        obj.egn_subekt = jQuery('#search-owner-egn-subekt').textbox("getValue");
    }
    if(jQuery('#search-represent-name').textbox("getValue")){
        obj.rep_name = jQuery('#search-represent-name').textbox("getValue");
    }
    if(jQuery('#search-represent-egn').textbox("getValue")){
        obj.rep_egn = jQuery('#search-represent-egn').textbox("getValue");
    }
    if(jQuery('#search-company-name').textbox("getValue")){
        obj.company_name = jQuery('#search-company-name').textbox("getValue");
    }
    if(jQuery('#search-company-eik').textbox("getValue")){
        obj.company_eik = jQuery('#search-company-eik').textbox("getValue");
    }
    if(jQuery('#search-person-name').val()){
        obj.person_name = jQuery('#search-person-name').val();
    }
    if(jQuery('#search-person-egn').val()){
        obj.person_egn = jQuery('#search-person-egn').val();
    }
    if(jQuery('#search-participation').combobox('getValue')){
        obj.participation = jQuery('#search-participation').combobox('getValue');
    }
    if(jQuery('#search-comment').val()){
        obj.comment = jQuery('#search-comment').val();
    }

    return obj;
}

function getComboboxValuesFilterPlots() {
    'use strict';
    var obj = {
        ekate: [],
        category: [],
        area_type: [],
        irrigated_area: [],
        contract_type: [],
        contract_status: [],
        farming: [],
        legal_status: [],
        participation: [],
    };

    let selectedEkattes = jQuery('#search-ekatte').combobox('getValues');
    let selectedCategories = jQuery('#search-category').combobox('getValues');
    let selectedAreaTypes = jQuery('#search-area-type').combobox('getValues');
    let selectedIrrigatedArea = jQuery('#search-irrigated-area').combobox('getValue');
    let selectedContractTypes = jQuery('#search-contract-type').combobox('getValues');
    let selectedContractStatus = jQuery('#search-contract-status').combobox('getValue');
    let selectedFarmings = jQuery('#search-farming').combobox('getValues');
    let selectedParticipation = jQuery('#search-participation').combobox('getValue');

    if(selectedEkattes.length > 0) {
        let ekattes = jQuery('#search-ekatte').combobox('getData');
        for(const selectedEkatte of selectedEkattes){
            for(const ekatte of ekattes){
                if(selectedEkatte == ekatte.ekate){
                    obj.ekate[ekatte.text] = ekatte.ekate;
                }
            }
        }
    }

    if(selectedCategories.length > 0) {
        let categories = jQuery('#search-category').combobox('getData');
        for(const selectedCategory of selectedCategories){
            for(const category of categories){
                if(selectedCategory == category.id){
                    obj.category[category.name] = category.id;
                }
            }
        }
    }

    if(selectedAreaTypes.length > 0) {
        let areaTypes = jQuery('#search-area-type').combobox('getData');
        for(const selectedAreaType of selectedAreaTypes){
            for(const areaType of areaTypes){
                if(selectedAreaType == areaType.id){
                    obj.area_type[areaType.name] = areaType.id;
                }
            }
        }
    }

    if(selectedIrrigatedArea) {
        let irrigatedAreas = jQuery('#search-irrigated-area').combobox('getValue');
        for(const irrigatedArea of irrigatedAreas){
            if(selectedIrrigatedArea == irrigatedArea.value){
                obj.irrigated_area[irrigatedArea.label] = irrigatedArea.value;
            }
        }
    }

    if(selectedContractTypes.length > 0) {
        let contractTypes = jQuery('#search-contract-type').combobox('getData');
        for(const selectedContractType of selectedContractTypes){
            for(const contractType of contractTypes){
                if(selectedContractType == contractType.id){
                    obj.contract_type[contractType.name] = contractType.id;
                }
            }
        }
    }

    if(selectedContractStatus) {
        let contractStatuses = jQuery('#search-contract-status').combobox('getValue');
        for(const contractStatus of contractStatuses){
            if(selectedContractStatus == contractStatuses.id){
                obj.contract_status[contractStatuses.name] = contractStatuses.id;
            }
        }
    }

    if(selectedFarmings.length > 0) {
        let farmings = jQuery('#search-farming').combobox('getValues');
        for(const selectedFarming of selectedFarmings){
            for(const farming of farmings){
                if(selectedFarming == farming.id){
                    obj.farming[farm.name] = farming.id;
                }
            }
        }
    }

    if(selectedParticipation) {
        let participations = jQuery('#search-participation').combobox('getValue');
        for(const participation of participations){
            if(selectedParticipation == participation.value){
                obj.participation[participation.text] = participation.value;
            }
        }
    }

    return obj;
}

function clearFilterPlotValues() {
    'use strict';
    jQuery('#search-kad-ident').textbox('reset');
    jQuery('#search-ekatte').combobox('select',jQuery('#search-ekatte').combobox('getData')[0].ekate);
    jQuery('#search-masiv').textbox('reset');
    jQuery('#search-number').textbox('reset');
    jQuery('#search-category').combobox('reset');
    jQuery('#search-area-type').combobox('reset');
    jQuery('#search-mestnost').combobox('reset');
	jQuery('#search-block').textbox('reset');
    jQuery('#search-cnum').textbox('reset');
    jQuery('#search-cnum-exact').prop('checked',true);
    jQuery('#search-contract-type').combobox('reset');
    jQuery('#search-contract-status').combobox('reset');
    jQuery('#search-farming').combobox('reset');
    jQuery('#search-date-from').datebox('reset');
    jQuery('#search-date-to').datebox('reset');
    jQuery('#search-start-date').datebox('reset');
    jQuery('#search-due-date-to').datebox('reset');
    jQuery('#search-irrigated-area').combobox('loadRpc');
    jQuery('#search-owner-name').textbox('reset');
    jQuery('#search-owner-egn').textbox('reset');
    jQuery('#search-heritor-name').textbox('reset');
    jQuery('#search-heritor-egn').textbox('reset');
    jQuery('#search-owner-ime-subekt').textbox('reset');
    jQuery('#search-owner-egn-subekt').textbox('reset');
    jQuery('#search-represent-name').textbox('reset');
    jQuery('#search-represent-egn').textbox('reset');
    jQuery('#search-company-name').textbox('reset');
    jQuery('#search-company-eik').textbox('reset');
    jQuery('#search-archived-plots').combobox('reset');
    jQuery('#search-participation').combobox('reset');
    jQuery('#extended-select-filtered-plots').prop('checked', false);
    jQuery('#extended-select-filtered-plots').trigger('change');
    jQuery('#search-person-name').val('');
    jQuery('#search-person-egn').val('');
    jQuery('#search-comment').val('');
}

function getTextFilterPlots() {
    'use strict';
    var obj = {};
    obj.kad_ident = jQuery('#search-kad-ident').textbox('getValue');
    obj.ekate = jQuery('#search-ekatte').combobox('getText');
    obj.masiv = jQuery('#search-masiv').textbox('getValue');
    obj.number = jQuery('#search-number').textbox('getValue');
    obj.category = jQuery('#search-category').combobox('getText');
    obj.area_type = jQuery('#search-area-type').combobox('getText');
    obj.mestnost = jQuery('#search-mestnost').combobox('getText');
	obj.block = jQuery('#search-block').textbox('getValue');
    obj.irrigated_area = jQuery('#search-irrigated-area').combobox('getText');
    obj.cnum = jQuery('#search-cnum').textbox('getValue');
    obj.contract_type = jQuery('#search-contract-type').combobox('getText');
    obj.contract_status = jQuery('#search-contract-status').combobox('getText');
    obj.farming = jQuery('#search-farming').combobox('getText');
    obj.date_from = jQuery('#search-date-from').datebox('getText');
    obj.date_to = jQuery('#search-date-to').datebox('getText');
    obj.start_date = jQuery('#search-start-date').datebox('getText');
    obj.due_date = jQuery('#search-due-date-to').datebox('getText');
    obj.owner_name = jQuery('#search-owner-name').textbox('getValue');
    obj.owner_egn = jQuery('#search-owner-egn').textbox('getValue');
    obj.ime_subekt = jQuery('#search-owner-ime-subekt').textbox('getValue');
    obj.egn_subekt = jQuery('#search-owner-egn-subekt').textbox('getValue');
    obj.rep_name = jQuery('#search-represent-name').textbox('getValue');
    obj.rep_egn = jQuery('#search-represent-egn').textbox('getValue');
    obj.company_name = jQuery('#search-company-name').textbox('getValue');
    obj.company_eik = jQuery('#search-company-eik').textbox('getValue');
    obj.is_edited = is_edit !== undefined ? is_edit : false;
    obj.select_filtered = jQuery('#extended-select-filtered-plots').is(':checked');
    obj.participation = jQuery('#search-participation').combobox('getText');
    obj.comment = jQuery('#search-comment').textbox('getValue');

    return obj;
}


function setGroupingCheckboxes() {
    var ekatte = jQuery('#group-by-ekatte'),
        masiv = jQuery('#group-by-masiv'),
        stopanstvo = jQuery('#group-by-farming');

    jQuery(ekatte).prop('checked', true);
    jQuery(ekatte).attr('disabled', true);

    jQuery(masiv).on('change', function() {
        if (masiv.is(':checked')) {
            ekatte.prop('checked', true);
        }
    });

    jQuery(stopanstvo).on('change', function() {
        if (!stopanstvo.is(':checked')) {
            ekatte.prop('checked', true);
        }
    });
}
