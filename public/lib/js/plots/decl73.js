function initDecl73Grid(year, farming, withDividendDecl73) {
    jQuery('#declaration-73').datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        iconCls: 'icon-edit-geometry',
        title: chosenHeading,
        pageSize: 10,
        fit: true,
        showFooter: true,
        border: false,
        url: 'index.php?plots-rpc=declaration-chosen-grid',
        rpcParams: [{
            year: year,
            farming: farming,
            with_dividends: withDividendDecl73,
        }],
        rpcMethod: 'decl73',
        sortName: 'date',
        sortOrder: 'asc',
        idField: 'pc_rel_id',
        frozenColumns: [],
        columns: [
            [
                {
                    field: 'transaction_id',
                    title: '<b>Номер на<br/>транзакция</b>',
                    sortable: true,
                    width: 100
                },
                {
                    field: 'date',
                    title: '<b>Дата<br/>на плащане</b>',
                    sortable: true,
                    width: 100
                },
                {
                    field: 'name',
                    title: '<b>Изплатено на (име)</b>',
                    sortable: true,
                    width: 241,
                },
                {
                    field: 'egn',
                    title: '<b>ЕГН/ЛЧН</b>',
                    sortable: true,
                    width: 100
                },
                {
                    field: 'amount',
                    title: '<b>Сума</b>',
                    sortable: true,
                    width: 100
                },
                {
                    field: 'tax',
                    title: '<b>Удържан<br/>данък</b>',
                    sortable: true,
                    width: 100,
                },
                {
                    field: 'reason',
                    title: '<b>Основание<br/>(рента/<br/>дивидент)</b>',
                    sortable: true,
                    width: 100,
                },
                {
                    field: 'subleases',
                    title: '<b>Държава</b>',
                    sortable: true,
                    width: 100
                },
                {
                    field: 'address',
                    title: '<b>Адрес</b>',
                    sortable: true,
                    width: 180
                },
                {
                    field: 'is_foreigner',
                    title: '<b>Чуждестранно<br/>лице</b>',
                    sortable: true,
                    width: 100,
                }

            ]
        ],
        pagination: true,
        rownumbers: true,
        toolbar: '#decl73-chosen-toolbar',
        onBeforeLoad: function () {
        },

        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function decl73ExportXLS() {
    if (jQuery('#declaration-73').datagrid('getData').rows.length == 0) {
        jQuery.messager.alert('Внимание!', 'Няма информация която да експортнете.');
    } else {
        document_type = 'xls';
        exportDeclaration73();
    }
}

function decl73ExportXML() {
    if (jQuery('#declaration-73').datagrid('getData').rows.length == 0) {
        jQuery.messager.alert('Внимание!', 'Няма информация която да експортнете.');
    } else {
        document_type = 'xml';
        exportDeclaration73();
    }
}
