function initDecl70ChosenGrid(ekate, year, farming) {
    declYear = year;
    declEkate = ekate;
    declFarming = farming;

	jQuery('#plot-number-format-word-70-combobox').combobox({
        data: [
            {
                value: "",
                text: "[Ма<PERSON>ив][Парцел]"
            },
            {
                value: "dot_separator",
                text: "[Масив].[Парцел]"
            },
            {
                value: "underscore_separator",
                text: "[Масив]_[Парцел]",
                selected: true
            }
        ],
        valueField: 'value',
        textField: 'text',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter

    });

	jQuery('#zerro-lpad-word-70-combobox').combobox({
        data: [
            {
                value: "4",
                text: "Кад. формат",
                selected: true
            },
            {
                value: "3",
                text: "КВС формат",
            },
            {
                value: "",
                text: "Без нули",
            }
            
        ],
        valueField: 'value',
        textField: 'text',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

	jQuery('#declaration-chosen-tables').datagrid({
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		iconCls: 'icon-edit-geometry',
		title: chosenHeading,
		fit: true,
		fitColumns: true,
		showFooter: true,
		border: false,
		url: 'index.php?plots-rpc=declaration-chosen-grid',
		rpcParams: [{
			ekate: declEkate,
			year: declYear,
			farming: declFarming,
			action: 'open'
		}],
		rpcMethod: 'decl70',
		sortName: 'c.id',
		sortOrder: 'asc',
		idField: 'pc_rel_id',
		frozenColumns: [
			[
				{
					field: 'ck',
					checkbox: true
				}
			]
		],
		rowStyler: function(index, row) {
			if (!row.include && !row.participate) {
				return 'background-color:#FF0000;';
			}
			if (row.is_subleased) {
				return 'background-color:#FF9900;';
			}
		},
		columns: [
			[
				{
					field: 'kad_ident',
					title: '<b>Идентификатор</b>',
					sortable: true,
					width: 130
				}, {
					field: 'area',
					title: '<b>Площ<br/>(дка)</b>',
					sortable: true,
					width: 50
				}, {
					field: 'area_type',
					title: '<b>НТП</b>',
					sortable: true,
					width: 150
				}, {
					field: 'c_num',
					title: '<b>Договор</b>',
					sortable: true,
					width: 70
				}, {
					field: 'c_type',
					title: '<b>Тип<br/>договор</b>',
					sortable: true,
					width: 90
				}, {
					field: 'subleases',
					title: '<b>Преотдаден<br/>чрез</b>',
					sortable: true,
					width: 100
				}
			]
		],
		rownumbers: true,
		toolbar: '#decl70-chosen-toolbar',
		onBeforeLoad: function() {
		},
		onLoadSuccess: function(){
			initDecl70ChooseGrid(ekate, year, farming);
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}
function initDecl70ChooseGrid(ekate, year, farming) {

    declYear = year;
    declEkate = ekate;
    declFarming = farming;

	jQuery('#declaration-choose-tables').datagrid({
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		iconCls: 'icon-edit-geometry',
		title: chooseHeading,
		pageSize: 10,
		fit: true,
		fitColumns: true,
		showFooter: true,
		url: 'index.php?plots-rpc=declaration-choose-grid',
		rpcParams: [{
			ekate: declEkate,
			year: declYear,
			farming: declFarming,
			ntp: null,
			start_date: null,
			due_date: null,
			action:'open'
		}],
		rpcMethod: 'decl70',
		sortName: 'kad_ident',
		sortOrder: 'asc',
		idField: 'pc_rel_id',
		frozenColumns: [
			[
				{
					field: 'ck',
					checkbox: true
				}
			]
		],
		columns: [
			[
				{
					field: 'kad_ident',
					title: '<b>Идентификатор</b>',
					sortable: true,
					width: 130
				} , {
				field: 'plots_contracts_area',
				title: '<b>Площ по <br>правно основание (дка)</b>',
				sortable: true,
				width: 150
				} , {
				field: 'subleased_area',
				title: '<b>Преотдадена/Налична<br>площ(дка)</b>',
				sortable: true,
				width: 150
				} , {
					field: 'area_type',
					title: '<b>НТП</b>',
					sortable: true,
					width: 150
				}, {
					field: 'c_num',
					title: '<b>Договор</b>',
					sortable: true,
					width: 70
				}, {
					field: 'c_type',
					title: '<b>Тип<br/>договор</b>',
					sortable: true,
					width: 90
				}, {
					field: 'subleases',
					title: '<b>Преотдаден<br/>чрез</b>',
					sortable: true,
					width: 115
				}
			]
		],
		pagination: true,
		rownumbers: true,
		toolbar: [{
				id: 'btnaddtoanketnakarta',
				text: 'Добавяне',
				iconCls: 'icon-add',
				handler: function() {
					var getChecked = jQuery('#declaration-choose-tables').datagrid('getChecked');
					if (getChecked[0]) {
						var idArray = [];

						for (var i = 0; i < getChecked.length; i++) {
							idArray[i] = getChecked[i]['pc_rel_id'];
						}

						TF.Rpc.Plots.DeclarationChosenGrid.addToDeclaration(idArray)
						.done(function () {
							jQuery('#declaration-choose-tables').datagrid('options').rpcParams[0].action = 'addPlot';
							jQuery('#declaration-chosen-tables').datagrid('options').rpcParams[0].action = 'addPlot';

							jQuery('#declaration-choose-tables').datagrid('loadRpc');
							jQuery('#declaration-chosen-tables').datagrid('loadRpc');
						})
						.fail();

					} else {
						jQuery.messager.alert('Грешка', 'Моля изберете имоти, които да бъдат добавени към декларацията.');
					}
				}
			}, {
				id: 'btnfilteranketnakartaadd',
				text: 'Филтър',
				iconCls: 'icon-filter',
				handler: function() {
					jQuery('#win-anketna-karta-filter').window('open');
				}
			}, {
				id: 'btnfilteranketnakartaclear',
				text: 'Покажи всички',
				iconCls: 'icon-clear-filter',
				handler: function() {
					clearAnketnaKartaFilter();
				}
			}
		],
		onBeforeLoad: function() {
			jQuery('#declaration-choose-tables').datagrid('clearChecked');
		},
		rowStyler: function(index, row) {
			if (row.is_subleased) {
				return 'background-color:#FF9900;';
			}

			if(row.subleased_area > 0) {
				return 'font-weight: bold;'
			}
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

jQuery(function () {
	jQuery('#print-decl-70-word').bind('click', function () {
		var winDownload = jQuery('#win-download'),
			downloadFile = jQuery('#btn-download-file'),
			year = jQuery('#choose-decl-year > input').combobox('getValue'),
			ekate = jQuery('#choose-decl-ekate > input').combobox('getValue'),
			ntp = jQuery('#ak-results-ntp > input').combobox('getValues'),
			from_date = jQuery('#ak-results-from-date > input').datebox('getValue'),
			to_date = jQuery('#ak-results-to-date > input').datebox('getValue'),
			farming = jQuery('#choose-decl-farming > input').combobox('getValue'),
			gridOptions = jQuery('#declaration-chosen-tables').datagrid('options'),
			splitLegalGrounds = jQuery('#export-decl-split-legal-grounds > input').is(':checked'),
			plotNumberFormat = jQuery('#plot-number-format-word-70-combobox').combobox('getValue'),
			zerroLpad = jQuery('#zerro-lpad-word-70-combobox').combobox('getValue'),
			
			exportObject = {
				farming: farming,
				year: year,
				ntp: ntp,
				from_date: from_date,
				to_date: to_date,
				has_zerro_lpad: zerroLpad,
				plot_number_format: plotNumberFormat,
				split_legal_grounds: splitLegalGrounds,
				titlePageInfo: getDecl70TitlePageInfo(),
				exportType: exportType
			},
			sort = gridOptions.sortName,
			order = gridOptions.sortOrder;

		if(exportType !== 'declPML') {
			exportObject.ekate = ekate;
		}

		TF.Rpc.Plots.PlotsDeclarations.createDecl70(exportObject,null,null,sort,order)
			.done(function (data)
			{
				jQuery('#win-declaration-70-info').window('close');
				winDownload.window('open');
				var path = data.word_file_location;
				_pathFile = path;
				_fileName = data.file_name;
				downloadFile.attr("href", path);
			})
			.fail(function () {
				jQuery.messager.alert('Грешка', 'Възникна грешка при обработка на данните', 'warning');
			});
	});
	jQuery('#clear-decl-70-word').bind('click', function () {
		jQuery('#decl-70-osz-city').val('');
		jQuery('#decl-70-osz-oblast').val('');
		jQuery('#decl-70-owner-names').val('');
		jQuery('#decl-70-owner-egn').val('');
		jQuery('#decl-70-company-name').val('');
		jQuery('#decl-70-company-bulstat').val('');
		jQuery('#decl-70-company-city').val('');
		jQuery('#decl-70-company-municipality').val('');
		jQuery('#decl-70-company-region').val('');
		jQuery('#decl-70-company-address').val('');
		jQuery('#decl-70-company-phone').val('');
		jQuery('#decl-70-company-mail').val('');
		jQuery('#decl-70-palnomoshntnik-name').val('');
		jQuery('#decl-70-palnomoshntnik-egn').val('');
		jQuery('#decl-70-palnomoshntnik-city').val('');
		jQuery('#decl-70-palnomoshntnik-municipality').val('');
		jQuery('#decl-70-palnomoshntnik-region').val('');
		jQuery('#decl-70-palnomoshntnik-address').val('');
		jQuery('#decl-70-palnomoshntno-number').val('');
		jQuery('#decl-70-palnomoshntno-date').val('');
		jQuery('#decl-70-palnomoshntno-zaverka').val('');
	});
});

function getDecl70TitlePageInfo() {
	return {
		osz_city: jQuery('#decl-70-osz-city').val(),
		osz_oblast: jQuery('#decl-70-osz-oblast').val(),
		farming_year: jQuery('#decl-70-farming-year').html(),
		owner_names: jQuery('#decl-70-owner-names').val(),
		owner_egn: jQuery('#decl-70-owner-egn').val(),
		company_name: jQuery('#decl-70-company-name').val(),
		company_bulstat: jQuery('#decl-70-company-bulstat').val(),
		company_city: jQuery('#decl-70-company-city').val(),
		company_municipality: jQuery('#decl-70-company-municipality').val(),
		company_region: jQuery('#decl-70-company-region').val(),
		company_address: jQuery('#decl-70-company-address').val(),
		company_phone: jQuery('#decl-70-company-phone').val(),
		company_mail: jQuery('#decl-70-company-mail').val(),
		palnomoshntnik_name: jQuery('#decl-70-palnomoshntnik-name').val(),
		palnomoshntnik_egn: jQuery('#decl-70-palnomoshntnik-egn').val(),
		palnomoshntnik_city: jQuery('#decl-70-palnomoshntnik-city').val(),
		palnomoshntnik_municipality: jQuery('#decl-70-palnomoshntnik-municipality').val(),
		palnomoshntnik_region: jQuery('#decl-70-palnomoshntnik-region').val(),
		palnomoshntnik_address: jQuery('#decl-70-palnomoshntnik-address').val(),
		palnomoshntno_number: jQuery('#decl-70-palnomoshntno-number').val(),
		palnomoshntno_date: jQuery('#decl-70-palnomoshntno-date').val(),
		palnomoshntno_zaverka: jQuery('#decl-70-palnomoshntno-zaverka').val()
	}
}


function removePlotFromDecl70(){
	var getChecked = jQuery('#declaration-chosen-tables').datagrid('getChecked');
	if (getChecked[0]) {
		jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да премахнете тези имоти от декларацията?', function(r) {
			if (r) {
				var idArray = [];

				for (var i = 0; i < getChecked.length; i++) {
					idArray[i] = getChecked[i]['pc_rel_id'];
				}

				TF.Rpc.Plots.DeclarationChosenGrid.deleteFromDeclaration(idArray)
				.done(function () {
					jQuery('#declaration-choose-tables').datagrid('options').rpcParams[0].action = 'removePlot';
					jQuery('#declaration-chosen-tables').datagrid('options').rpcParams[0].action = 'removePlot';

					jQuery('#declaration-chosen-tables').datagrid('loadRpc');
					jQuery('#declaration-choose-tables').datagrid('loadRpc');
				})
				.fail();
				}
			});
	} else {
		jQuery.messager.alert('Грешка', 'Моля изберете имоти, които да бъдат добавени към декларацията.');
	}
}

function decl70ExportWord() {
	if (jQuery('#declaration-chosen-tables').datagrid('getData').rows.length == 0) {
		jQuery.messager.alert('Внимание!','Няма включени имоти в заявлението по чл. 70.');
	}else{
		var farmingYearText = jQuery('#choose-decl-year > input').combobox('getText');
		jQuery('#decl-70-farming-year').html(farmingYearText.substr(10));
		fillWordDeclaration70Fields();
		jQuery('#win-declaration-70-info').window('open');
		jQuery('#win-declaration-70-info').window('center');
	}
}

function fillWordDeclaration70Fields(){
	let selectedFarm = getSelectedFarm();
	if(!selectedFarm) return;

	jQuery('#decl-70-owner-names').val(selectedFarm.mol);
	jQuery('#decl-70-owner-egn').val(selectedFarm.mol_egn);
	jQuery('#decl-70-company-name').val(selectedFarm.name);
	jQuery('#decl-70-company-bulstat').val(selectedFarm.bulstat);
	jQuery('#decl-70-company-address').val(selectedFarm.address);
	jQuery('#decl-70-company-phone').val(selectedFarm.farming_mol_phone);
}

function decl70ExportCSV() {
	if (jQuery('#declaration-chosen-tables').datagrid('getData').rows.length == 0) {
		jQuery.messager.alert('Внимание!','Няма включени имоти в заявлението по чл. 70.');
	}else{
		jQuery('#win-decl-export-info').window('open');
		document_type = 'csv';
	}
}

function decl70ExportXLS() {
	if (exportType === 'declPML') {
		if (jQuery('#declaration-chosen-tables').datagrid('getData').rows.length == 0) {
			jQuery.messager.alert('Внимание!', 'Няма включени имоти в Заявление ПМЛ.');
		} else {
			exportDeclaration();
		}
	} else {
		if (jQuery('#declaration-chosen-tables').datagrid('getData').rows.length == 0) {
			jQuery.messager.alert('Внимание!', 'Няма включени имоти в Заявлението по чл. 70.');
		} else {
			fillExcelDeclarationFields();
			jQuery('#win-decl-export-info').window('open');
		}
	}
	document_type = 'xls';
}
