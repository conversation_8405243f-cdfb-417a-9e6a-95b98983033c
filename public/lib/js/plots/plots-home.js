
var isSuperAdmin=false;
var plotID;
var map;
var boundsArray;
var layerArray;
var renderer = OpenLayers.Util.getParameters(window.location.href).renderer;
var WEEK_MS = 604800000;
var ComboboxData;

if (renderer) {
    OpenLayers.Layer.Vector.prototype.renderers = [renderer];
}
var contragent_type;

jQuery(function () {
    initSearchOnEnter();
    setUserRights();
    
    TF.Rpc.Common.CombinedComboboxData.read(null, null, {selected: 'current', farming_year_record_all: false})
        .done(function (data) {
            ComboboxData = data;
            initFilterComponents(false);
            initjQueryControls();
        })
        .fail(function (error) {
            if(error) jQuery.messager.alert('Грешка', error.getMessage(), 'warning');
        });
    var date = new Date(),
        todayDate = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate(),
        toDate = new Date(date.getTime() + WEEK_MS),
        toDateStr = toDate.getFullYear() + '-' + (toDate.getMonth() + 1) + '-' + toDate.getDate();

    //retrieve GET parameters
    var GET = {};
    location.search.substr(1).split("&").forEach(function(item) {GET[decodeURIComponent(item.split("=")[0])] = decodeURIComponent(item.split("=")[1])});
    //change URL without refresh if it's posible

    if (history && history.replaceState){
        history.replaceState(null, null, 'index.php?page=' + GET.page);
    }

    if (GET.is_edited == "true") {
        is_edit = true;
        jQuery('#is-edit-plot').toggle = true;
        jQuery('#is-edit-plot').linkbutton('select');
    } else {
        GET.is_edited == false;
    }

    let filterParams = {
        plot_status: ['Active'],
        ...(GET.plot_id && { plot_id: GET.plot_id }),
    };

    initMapLayers();
    initPlotsTree(1, filterParams);
    initPlotsContractsGrid(0);
    initPlotsHypothecsGrid(0);

    jQuery('#eik > input').numberbox({});
    new EgnValidateBox('#egn > input');
});

function unselectAllButtons()
{
    jQuery('#map-filter').linkbutton('unselect');
    jQuery('#map-masiv').linkbutton('unselect');
    jQuery('#map-contract').linkbutton('unselect');
}

function initjQueryControls() {
	var ekateComboboxData   = ComboboxData.EkateCombobox,
        farmingComboboxData = ComboboxData.FarmingCombobox,
        farmingYearComboboxData = ComboboxData.FarmingYearCombobox;

    newFarmingComboboxData = [];
    farmingComboboxData.forEach(function (el) {
        if (el.id !== "") {
            newFarmingComboboxData.push(el);
        }
    });
    newFarmingComboboxData[0].selected = true;

    newEkateComboboxData = [];
    ekateComboboxData.forEach(function (el) {
        if (el.ekate !== "") {
            newEkateComboboxData.push(el);
        }
    });
    if( newEkateComboboxData[0]){
        newEkateComboboxData[0].selected = true;
    }

    jQuery('#btn-edit-plot > a').linkbutton({iconCls: 'icon-save'});
    jQuery('#btn-multiedit > a').linkbutton({iconCls: 'icon-save'});
    jQuery('#btn-choose-report-type > a').linkbutton({iconCls: 'icon-ok'});
    jQuery('#btn-submit-declarant-info > a').linkbutton({iconCls: 'icon-export'});

    jQuery('#search-contract-date-from > input').datebox();
    jQuery('#search-contract-date-to > input').datebox();

    jQuery('#choose-decl-ekate > input').combobox({
        data: newEkateComboboxData,
    	valueField: 'ekate',
    	textField: 'text',
		filter: function(q, row){
			var opts = jQuery(this).combobox('options');
			var text = row[opts.textField].toLowerCase();
			var value = row[opts.valueField];
			var find = q.toLowerCase();
			if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
			{
				return true;
			}
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#choose-decl-year > input').combobox({
        data: farmingYearComboboxData,
    	valueField: 'id',
    	textField: 'farming_year',
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#choose-decl-73-year > input').combobox({
        data: farmingYearComboboxData,
        valueField: 'id',
        textField: 'title',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#choose-decl-farming > input').combobox({
        data: newFarmingComboboxData,
    	valueField: 'id',
    	textField: 'name',
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

	jQuery('#print-report-page').bind('click', function() {
		printReport(true);
	});

	jQuery('#print-report').bind('click', function() {
		printReport(false);
	});

	jQuery('#filter-report').bind('click', function(){
		jQuery('#win-filter-report').window('open');

	});

    jQuery('#rent-place > input').combobox({
        data: ComboboxData.EkateCombobox,
        valueField: 'ekate',
        textField: 'text',
        filter: function(q, row){
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
            {
                return true;
            }
        },
        onLoadSuccess: function (data) {
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

	jQuery('#btn-report-filter').bind('click', function(){

		var params = [{'filters':{
			report_date_from : jQuery('#choose-report-date').datebox('getValue'),
            report_date : jQuery('#choose-report-date-to').datebox('getValue'),
			report_date_as_of : jQuery('#choose-report-date-as-of').datebox('getValue'),
            report_contract_date : jQuery('#choose-report-contract-date').datebox('getValue'),
            report_contract_date_to : jQuery('#choose-report-contract-date-to').datebox('getValue'),
            report_ekate : jQuery('#choose-report-ekate').combobox('getValue'),
            report_ntp : jQuery('#choose-report-ntp').combobox('getValue'),
            report_category : jQuery('#choose-report-category').combobox('getValue'),
            report_farming : jQuery('#choose-report-farming').combobox('getValue'),
            report_mestnost :  jQuery('#choose-report-mestnost').combobox('getValue'),
			report_irrigation :  jQuery('#choose-irrigation-area').combobox('getValue'),
            report_arendator : jQuery('#choose-arendator').val(),
            report_sublease_type : jQuery('#choose-sublease-type').combobox('getValue'),
            report_include_subleases: jQuery('#include-subleased-in-report').prop('checked'),
            report_exclude_inactive: jQuery('#exclude-inactive-contracts-checkbox').prop('checked'),
            report_choose_participation: jQuery('#choose-participation').combobox('getValue'),
            report_choose_renewed: jQuery('#choose-renewed').combobox('getValue'),
		}}];

        if (report_type == 'own_plots_detailed') {
            jQuery('#plots-report-tables-detail').datagrid('loadRpc', params);
            jQuery('#win-filter-report').window('close');
        } else {
          jQuery('#plots-report-tables').datagrid('loadRpc', params);
          jQuery('#win-filter-report').window('close');
        }

        addDateFilterFooterMessage(report_type);
	});

	jQuery('#clear-filter-report').bind('click', function(){

        resetDateBoxes();

        jQuery('#choose-report-date').datebox('setValue', todayDate);
        jQuery('#choose-report-date-to').datebox('setValue', toDateStr);
        jQuery('#choose-report-date-as-of').datebox('reset');
	    jQuery('#choose-report-ekate').combobox('reset');
	    jQuery('#choose-report-farming').combobox('reset');
	    jQuery('#choose-report-ntp').combobox('reset');
	    jQuery('#choose-report-category').combobox('reset');
        jQuery('#choose-report-mestnost').combobox('reset');
	    jQuery('#choose-irrigation-area').combobox('loadRpc');
        jQuery('#choose-report-contract-date').datebox('reset');
        jQuery('#choose-report-contract-date-to').datebox('reset');
	    jQuery('#choose-arendator').val('');
        jQuery('#choose-sublease-type').combobox('reset');
        jQuery('#include-subleased-in-report').prop('checked', true);
        jQuery('#choose-participation').combobox('reset');
        jQuery('#choose-renewed').combobox('reset');
	    var params = [{'filters':{
			report_date_from : jQuery('#choose-report-date').datebox('getValue'),
			report_date : jQuery('#choose-report-date-to').datebox('getValue'),
            report_date_as_of : jQuery('#choose-report-date-as-of').datebox('getValue'),
            report_contract_date :  jQuery('#choose-report-contract-date').datebox('getValue'),
            report_contract_date_to :  jQuery('#choose-report-contract-date-to').datebox('getValue'),
			report_ekate : jQuery('#choose-report-ekate').combobox('getValue'),
			report_ntp : jQuery('#choose-report-ntp').combobox('getValue'),
			report_category : jQuery('#choose-report-category').combobox('getValue'),
			report_farming : jQuery('#choose-report-farming').combobox('getValue'),
            report_mestnost :  jQuery('#choose-report-mestnost').combobox('getValue'),
			report_irrigation :  jQuery('#choose-irrigation-area').combobox('getValue'),
            report_arendator : jQuery('#choose-arendator').val(),
            report_sublease_type : jQuery('#choose-sublease-type').combobox('getValue'),
            report_include_subleases: jQuery('#include-subleased-in-report').prop('checked'),
            report_choose_participation: jQuery('#choose-participation').combobox('getValue'),
            report_choose_renewed: jQuery('#choose-renewed').combobox('getValue'),
		}}];

        if (report_type == 'own_plots_detailed' || report_type == 'own_plots') {

            jQuery('#choose-report-date').datebox('clear');
            jQuery('#choose-report-date-to').datebox('clear');
            jQuery('#choose-report-date-as-of').datebox('setValue', todayDate);

            detailParams = [{'filters': {
                report_date_from : null,
                report_date : null,
                report_date_as_of : todayDate,
                report_exclude_inactive: true
            }}];
            jQuery('#plots-report-tables-detail').datagrid('loadRpc', detailParams);
        } else {
            jQuery('#plots-report-tables').datagrid('loadRpc', params);
        }
        jQuery('#win-filter-report').window('close');
        addDateFilterFooterMessage(report_type);
	});

	jQuery('#map-report').bind('click', function(e) {
		e.preventDefault();
		jQuery('#win-report-map').window('open');
		var obj = {};
		obj.type = report_type;
		obj.report_date = jQuery('#choose-report-date').datebox('getValue');
        obj.report_date_to = jQuery('#choose-report-date-to').datebox('getValue');
		obj.report_date_as_of = jQuery('#choose-report-date-as-of').datebox('getValue');
		obj.report_ekate = jQuery('#choose-report-ekate').combobox('getValue');
        obj.report_farming = jQuery('#choose-report-farming').combobox('getValue');
		obj.report_mestnost = jQuery('#choose-report-mestnost').combobox('getValue');
        obj.report_contract_date =  jQuery('#choose-report-contract-date').datebox('getValue');
        obj.report_contract_date_to =  jQuery('#choose-report-contract-date-to').datebox('getValue');
        obj.report_ntp = jQuery('#choose-report-ntp').combobox('getValue');
        obj.report_category = jQuery('#choose-report-category').combobox('getValue');
        obj.report_irrigation =  jQuery('#choose-irrigation-area').combobox('getValue');
        obj.report_arendator = jQuery('#choose-arendator').val();
        obj.report_sublease_type = jQuery('#choose-sublease-type').combobox('getValue');
        obj.report_choose_participation = jQuery('#choose-participation').combobox('getValue');
        obj.report_choose_renewed = jQuery('#choose-renewed').combobox('getValue');
		TF.Rpc.Plots.ReportShowMap.mapReportsByType(obj).done(function (dataObj) {

            initReportMap();

            reloadAllReportMapLayers(dataObj.extent);
        })
        .fail(function (errorObj) {
        	if (errorObj) {
               location.replace(errorObj.getData());
            }
        });
	});

	jQuery('#win-plots-report-grid').window({
		onClose: function() {
            report_filter = undefined;
    		jQuery('#choose-report-date-to-cont').hide();
		}
	});

    fieldSets = jQuery('#win-plots-filter').find('fieldset.mainPlotFilterFieldset').css('height', 550);
    jQuery(fieldSets[0]).find('p').css('marginBottom', 4);
    jQuery(fieldSets[1]).find('p').css('marginTop', 4);
    jQuery(fieldSets[1]).find('p').css('marginBottom', 4);
}

function initPlotsInfo(plot){
    if(!plot)
    {
        jQuery('#info-plot-ekate').html('');
        jQuery('#info-plot-masiv').html('');
        jQuery('#info-plot-number').html('');
        jQuery('#info-plot-land').html('');
        jQuery('#info-plot-category').html('');
        jQuery('#info-plot-area-type').html('');
        jQuery('#info-plot-area-kvs').html('');
        jQuery('#info-plot-used-area').html('');
        jQuery('#info-plot-document-area').html('');
        jQuery('#info-plot-mestnost').html('');
		jQuery('#info-plot-blok').html('');
        jQuery('#info-plot-used-area-by').html('');
        jQuery('#info-plot-chlen37v').html('');
        jQuery('#info-plot-edit-active-from-text').html('');
        jQuery('#info-plot-edit-active-from').html('');
        jQuery('#info-plot-ime-subekt').html('');
        jQuery('#info-plot-comment').html('');
        jQuery('#info-plot-old-kadident').html('');
        return false;
    }

    jQuery('#info-plot-ekate').html(plot.ekate);
    jQuery('#info-plot-masiv').html(plot.masiv);
    jQuery('#info-plot-number').html(plot.number);
	jQuery('#info-plot-land').html(plot.land);
	jQuery('#info-plot-category').html(plot.category);
    jQuery('#info-plot-area-type').html(plot.area_type);
    jQuery('#info-plot-area-kvs').html(plot.area_kvs);
    jQuery('#info-plot-used-area').html(plot.used_area);
	jQuery('#info-plot-document-area').html(plot.document_area);
    jQuery('#info-plot-mestnost').html(plot.mestnost);
	jQuery("#info-plot-block").html(plot.block);
    jQuery('#info-plot-irrigated-area').html(plot.irrigated_area ? 'Дa' : 'Не');
	jQuery('#info-plot-ime-subekt').html(plot.ime_subekt);
    jQuery('#info-plot-comment').html(plot.comment);
    jQuery('#info-plot-old-kadident').html(plot.old_kad_ident);

	if(plot.used_area_by == 1){
		jQuery('#info-plot-used-area-by').html('Използваната площ е изчислена според геометрията на имота');
	}
	if(plot.used_area_by == 2){
		jQuery('#info-plot-used-area-by').html('Използваната площ е изчислена според географско пресичане със слой земеделски парцели за стопанска <b>'
												+ plot.area_year + '</b> и стопанство <b>' + plot.area_farming + '</b>');
	}
	if(plot.used_area_by == 3){
		jQuery('#info-plot-used-area-by').html('Използваната площ е изчислена според сумарен процент на всички собственици за стопанска ' + plot.area_year);
	}
	if(plot.include || plot.participate || plot.white_spots) {
		if(plot.include){
			jQuery('#info-plot-chlen37v').html('НЕ ЖЕЛАЯ да участвам с имота в масиви за ползване по чл. 37в от ЗСПЗЗ');
		}
		if(plot.participate){
			jQuery('#info-plot-chlen37v').html('ЖЕЛАЯ да участвам с имота в масиви за ползване по чл. 37в от ЗСПЗЗ');
		}
		if(plot.white_spots){
			jQuery('#info-plot-chlen37v').html('ЖЕЛАЯ имотът да бъде включен в масиви за ползване като имот по чл. 37в, ал. 3, т. 2 от ЗСПЗЗ, (имоти - бели петна)');
		}
	}
	else {
		jQuery('#info-plot-chlen37v').html('');
	}

    if(plot.is_edited)
    {
        jQuery('#info-plot-edit-active-from-text').html('Дата на<br/>заличаване');
    }
    else if(plot.edit_active_from)
    {
        jQuery('#info-plot-edit-active-from-text').html('Дата на<br/>създаване');
    }
    else
    {
        jQuery('#info-plot-edit-active-from-text').html('');
    }
    jQuery('#info-plot-edit-active-from').html(plot.edit_active_from);
}
function formatDate(date) {
	return date.getFullYear()+"-"+(date.getMonth()+1)+"-"+date.getDate();
}

/**
 * Prints grid information in new tab
 * @param {Boolean} currentPage true for current page records
 * @returns {undefined}
 */
function printReport(currentPage) {
    if (report_type == 'own_plots_detailed') {
        printDetailedReport(currentPage);
        return;
    }
    var data = jQuery('#plots-report-tables').datagrid('getData');

    if(data['rows'].length == 0) {
        jQuery.messager.alert('Грешка', 'Не може да отпечатате празна справка.');
        return;
    }

    var gridOptions = jQuery('#plots-report-tables').datagrid('options');

    var url = gridOptions.url.split("=");
    var rpcApiId = url[1];

    var pageNumber = null;
    var pageSize = null;

    if(currentPage) {
        pageNumber = gridOptions.pageNumber;
        pageSize = gridOptions.pageSize;
    }

    var params=gridOptions.rpcParams[0];
    var page=pageNumber;
    var rows=pageSize;
    var sort=gridOptions.sortName;
    var order=gridOptions.sortOrder;

    if (rpcApiId == 'own-plots-report-grid') {
        TF.Rpc.Plots.OwnPlotsReportGrid.read(params,page,rows,sort,order)
        .done(function (data) {
            printResultsFromRequests(data);
        })
        .fail(function (errorObj) {

        });
    }

    if (rpcApiId == 'subleased-plots-report-grid') {
        TF.Rpc.Plots.SubleasedPlotsReportGrid.read(params,page,rows,sort,order)
        .done(function (data) {
            printResultsFromRequests(data);
        })
        .fail(function (errorObj) {

        });
    }

    if (rpcApiId == 'for-sublease-plots-report-grid') {
        TF.Rpc.Plots.ForSubleasePlotsReportGrid.read(params,page,rows,sort,order)
        .done(function (data) {
            printResultsFromRequests(data);
        })
        .fail(function (errorObj) {

        });
    }

    if (rpcApiId == 'hypothecs-plots-report-grid') {
        TF.Rpc.Plots.HypothecsPlotsReportGrid.read(params,page,rows,sort,order)
        .done(function (data) {
            printResultsFromRequests(data);
        })
        .fail(function (errorObj) {

        });
    }

    if (rpcApiId == 'for-hypothec-plots-report-grid') {
        TF.Rpc.Plots.ForHypothecPlotsReportGrid.read(params,page,rows,sort,order)
        .done(function (data) {
            printResultsFromRequests(data);
        })
        .fail(function (errorObj) {

        });
    }

    if (rpcApiId == 'rented-plots-report-grid') {
        TF.Rpc.Plots.RentedPlotsReportGrid.read(params,page,rows,sort,order)
        .done(function (data) {
            printResultsFromRequests(data);
        })
        .fail(function (errorObj) {

        });
    }

    if (rpcApiId == 'subleased-rented-plots-report-grid') {
        TF.Rpc.Plots.SubleasedRentedPlotsReportGrid.read(params,page,rows,sort,order)
        .done(function (data) {
            printResultsFromRequests(data);
        })
        .fail(function (errorObj) {

        });
    }

    if (rpcApiId == 'expiring-contracts-report-grid') {
        TF.Rpc.Plots.ExpiringContractsReportGrid.read(params,page,rows,sort,order)
        .done(function (data) {
            printResultsFromRequests(data);
        })
        .fail(function (errorObj) {

        });
    }

    if (rpcApiId == 'plots-in-many-contracts-report-grid') {
        TF.Rpc.Plots.PlotsInManyContractsReportGrid.read(params,page,rows,sort,order)
        .done(function (data) {
            printResultsFromRequests(data);
        })
        .fail(function (errorObj) {

        });
    }

    if (rpcApiId == 'used-plots-report-grid') {
        TF.Rpc.Plots.UsedPlotsReportGrid.read(params,page,rows,sort,order)
        .done(function (data) {
            printResultsFromRequests(data);
        })
        .fail(function (errorObj) {

        });
    }

    if (rpcApiId == 'contracts-with-ownerless-plots-report-grid') {
        TF.Rpc.Plots.ContractsWithOwnerlessPlotsReportGrid.read(params,page,rows,sort,order)
        .done(function (data) {
            printResultsFromRequests(data);
        })
        .fail(function (errorObj) {

        });
    }

     if (rpcApiId == 'historical-plots-report-grid') {
        TF.Rpc.Plots.HistoricalPlotsReportGrid.read(params,page,rows,sort,order)
        .done(function (data) {
            printResultsFromRequests(data);
        })
        .fail(function (errorObj) {

        });
    }

}
/**
 * Prints grid information in new tab
 * @param {Boolean} currentPage true for current page records
 * @returns {undefined}
 */
function printDetailedReport(currentPage) {
    var data = jQuery('#plots-report-tables-detail').datagrid('getData');

    if(data['rows'].length == 0) {
        jQuery.messager.alert('Грешка', 'Не може да отпечатате празна справка.');
        return;
    }

    var gridOptions = jQuery('#plots-report-tables-detail').datagrid('options');

    var url = gridOptions.url.split("=");
    var rpcApiId = url[1];

    var pageNumber = null;
    var pageSize = null;

    if(currentPage) {
        pageNumber = gridOptions.pageNumber;
        pageSize = gridOptions.pageSize;
    }

    var params=gridOptions.rpcParams[0];
    var page=pageNumber;
    var rows=pageSize;
    var sort=gridOptions.sortName;
    var order=gridOptions.sortOrder;

    TF.Rpc.Plots.DetailedOwnPlotsReportGrid.read(params,page,rows,sort,order)
    .done(function (data) {
        printResultsFromRequests(data);
    })
    .fail(function (errorObj) {

    });
}

function printResultsFromRequests(data) {

    var gridOptions;
    if (report_type == 'own_plots_detailed') {
        gridOptions = jQuery('#plots-report-tables-detail').datagrid('options');
    } else {
        gridOptions = jQuery('#plots-report-tables').datagrid('options');
    }
    var report_date = jQuery('#choose-report-date').datebox('getValue');
    var report_date_to = jQuery('#choose-report-date-to').datebox('getValue');
    var report_date_as_of = jQuery('#choose-report-date-as-of').datebox('getValue');

    var date = new Date(report_date);
    report_date = date.getDate() + '.' + (date.getMonth()+1)  + '.' + date.getFullYear();

    var date_to = new Date(report_date_to);
    report_date_to = date_to.getDate() + '.' + (date_to.getMonth()+1)  + '.' + date_to.getFullYear();

    var report_title_date;
    var gridData = data.rows;
    if (report_date_as_of != '') {
        var date_as_of = new Date(report_date_as_of);
        report_date_as_of = date_as_of.getDate() + '.' + (date_as_of.getMonth()+1)  + '.' + date_as_of.getFullYear();
        report_title_date = report_date_as_of;
    } else {
        report_title_date = report_date_to;
    }
    var html = '<style>@page{size: landscape;}body,table{font-family: Arial;font-size: 10px;}</style>' +
    '<h2 align="center">' + report_title + ' към дата ' + report_title_date + '</h2>';

    if (gridOptions.rpcParams.length == 4)
    {
        var date = new Date(gridOptions.rpcParams[0]);
        report_date_to = date.getDate() + '.' + (date.getMonth()+1)  + '.' + date.getFullYear();

        var html = '<style>@page{size: landscape;}body,table{font-family: Arial;font-size: 10px;}</style>' +
        '<h2 align="center">' + report_title + ' от дата ' + report_date + ' до дата '+ report_date_to +'</h2>';
    }

    var header = {};
    var columns = gridOptions.columns[0];

    if (report_type == 'rented_expiring_contracts') {
        columns = gridOptions.columns[1];
        var html = '<style>@page{size: landscape;}body,table{font-family: Arial;font-size: 10px;}</style>' +
            '<h2 align="center">' + report_title + ' в периода от дата ' + report_date + ' до дата '+ report_date_to +'</h2>';
    }
    for(var i=0; i<columns.length; i++)
    {
        header[columns[i].field] = columns[i].title;
    }

    var rows = gridData;

    html += Templates.table(header, rows);

    jQuery('body').append('<iframe id="printf" name="printf"></iframe>');
    var newWin = window.frames['printf'];
    newWin.document.write('<body onload=window.print()>'+html+'</body>');
    newWin.document.close();
    setTimeout(function () {
        jQuery('#printf').remove();
    }, 1000);
}

function resetDateBoxes() {
    jQuery('#choose-report-date').datebox('enable');
    jQuery('#choose-report-date-to').datebox('enable');
    jQuery('#choose-report-date').datebox('setValue', todayDate);
    jQuery('#choose-report-date-to').datebox('setValue', toDateStr);
    jQuery('#choose-report-date-as-of').datebox('reset');
}

function disableDateBoxes () {

    jQuery('#choose-report-date').datebox('clear');
    jQuery('#choose-report-date-to').datebox('clear');

    jQuery('#choose-report-date').datebox('disable');
    jQuery('#choose-report-date-to').datebox('disable');
}


function messageContractIsFromSublease(contract) {

    var message = 'Не може да редактирате избрания договор. Той е създаден автоматично от подмодул "Преотдадени". За да направите промени по този договор трябва да редактирате';
    message += ' договор за преотдаване <a href="index.php?page=Subleases.Home&sublease_id=' + contract.attributes.from_sublease + '" target="_blank">№: '+contract.attributes.c_num+'</a>';

    jQuery.messager.alert('Внимание', message, 'warning');
}

function initSearchOnEnter() {
    jQuery("#win-plots-filter").off("keyup").on("keyup", function (event) {
		// Listen for enter key
        if (13 !== event.keyCode) {
			return;
		}

        if (jQuery("#kvs-applied-filters").is(':empty')) {
            jQuery("#btn-filter-plots").click()
        } else {
            jQuery("#btn-add-to-filter").click()
        }

    });
}
