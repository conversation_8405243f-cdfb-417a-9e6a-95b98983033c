function initPlotsHistoryGrid(plot_id) {
    jQuery('#plots-history-grid').treegrid({
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		pageSize: 10,
		fit: true,
		singleSelect: true,
		fitColumns: false,
		showFooter: true,
        border: false,
		url: 'index.php?plots-rpc=plots-history-grid',
		rpcParams: [{
			plot_id: plot_id
		}],
		sortName: 'gid',
		sortOrder: 'asc',
		idField: 'operation_id',
        treeField: 'kad_ident',
		columns: [[
				{
					field: 'kad_ident',
					title: '<b>Идентификатор</b>',
					sortable: true
				}, {
					field: 'ekate_name',
					title: '<b>Землище</b>',
					sortable: true
				}, {
					field: 'status',
					title: '<b>Статус</b>',
					align: 'center',
					sortable: true
				},
                {
                    field: 'edit_date',
                    title: '<b>Дата на<br/> операцията</b>',
                    align: 'center',
                    sortable: false
                }
			]],
		pagination: false,
		rownumbers: true,
		toolbar: [
			{
                id: 'btnviewplotinfo',
                text: 'Информация',
                iconCls: 'icon-info',
                handler: function() {
                    var getSelected = jQuery('#plots-history-grid').treegrid('getSelected');
                    var isPlotHistorical = false;
                    if (getSelected.level == 1) {
                    	isPlotHistorical = true;
                    }
                    if (getSelected) {
						window.open("index.php?page=Plots.Home&plot_id=" + getSelected.gid+"&is_edited="+isPlotHistorical, '_blank');
                    } else {
                        jQuery.messager.alert('Грешка', 'Моля изберете имот, за който да бъде показана информация.');
                    }
                }
            }
		],
		onBeforeLoad: function() {
			jQuery(this).treegrid('clearChecked');
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}
