function initCooperatorDividendGrid(cooperator_id) {
    jQuery('#cooperator-dividend-table').datagrid({
        title: 'Изплатени дивиденти',
        iconCls: 'icon-rents',
        nowrap: false,
        autoRowHeight: true,
        striped: true,
        pageSize: 20,
        fit: true,
        fitColumns: true,
        singleSelect:true,
        showFooter: true,
        url: 'index.php?cooperators-rpc=cooperator-dividend',
        idField: 'id',
        sortName: 'dar.id',
        border: true,
        sortOrder: 'desc',
        rpcParams: [cooperator_id],
        pagination: true,
        rownumbers: true,
        columns:[[
            {
                  field: 'period',
                  title: '<b>Период</b>',
                  width: 80
              },{
                  field: 'current_capital',
                  title: '<b>Дялов капитал</b>',
                  width: 80
              },{
                  field: 'dividend',
                  title: '<b>Дивидент</b>',
                  sortable: true,
                  width: 80
              },{
                  field: 'tax',
                  title: '<b>Данък</b>',
                  sortable: true,
                  width: 80
              },{
                  field: 'cashout',
                  title: '<b>Изплатен дивидент</b>',
                  width: 80,
                  align: 'center',
              }
        ]]
    });

    //custom pager
    var pager = jQuery('#cooperator-dividend-table').datagrid('getPager');
    pager.pagination({
      beforePageText: 'Стр.',
      displayMsg: 'От {from} до {to} от {total}'
    });
}   