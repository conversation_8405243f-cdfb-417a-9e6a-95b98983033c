var _fileName = "";

function initPrintCooperatorTemplatesGrid(cooperator_id) {

    var winDownload = jQuery('#win-download').window({
        onClose: onDowbloadWindowClose
    });

    var downloadFile = jQuery('#btn-download-file');
    var cancelDownloadFile = jQuery('#btn-download-file-close');

    jQuery('#templates-cooperators-tables').datagrid({
        iconCls: 'icon-template',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 50,
        fit: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?cooperators-rpc=cooperators-templates-grid',
        sortName: 'id',
        sortOrder: 'desc',
        border: false,
        idField: 'id',
        singleSelect: true,
        frozenColumns: [[
                {
                    field: 'ck',
                    checkbox: true
                }
            ]],
        columns: [[
                {
                    field: 'title',
                    title: '<b>Име</b>',
                    sortable: true,
                    width: 150
                }, {
                    field: 'add_date',
                    title: '<b>Дата на добавяне</b>',
                    sortable: true,
                    width: 150
                }
            ]],
        pagination: true,
        rownumbers: true,
        toolbar: [
			{
                id: 'btnexportpdfblank',
                text: 'Отпечатай pdf',
                iconCls: 'icon-pdf',
                handler: function() {
                    var getChecked = jQuery('#templates-cooperators-tables').datagrid('getChecked');

                    if (getChecked[0]) {
                        winDownload.window('open');

                        TF.Rpc.Cooperators.ExportCooperatorsBlank.expCooperatorsBlank(getChecked[0].id, cooperator_id, 'pdf')
                    	.done(function (data)
            			{
                    		var path = data.path;
                            _fileName = data.file_name;
                            downloadFile.attr("href", path); 
                        });
                    } else {
                        jQuery.messager.alert('Грешка', 'Моля изберете бланка, върху която да разпечатате!');
                    }
                }
            },{
                id: 'btnexportdocblank',
                text: 'Отпечатай Word',
                iconCls: 'icon-word',
                handler:function() {
                    var getChecked = jQuery('#templates-cooperators-tables').datagrid('getChecked');

                    if (getChecked[0]) {
                        winDownload.window('open');

                        TF.Rpc.Cooperators.ExportCooperatorsBlank.expCooperatorsBlank(getChecked[0].id, cooperator_id, 'doc')
                        .done(function(data) {
                            var path = data.path;
                            _fileName = data.file_name;
                            downloadFile.attr("href", path); 
                        });
                    } else {
                        jQuery.messager.alert('Грешка', 'Моля изберете бланка, върху която да разпечатате!');
                    }
                }
            }],
        onBeforeLoad: function() {
            jQuery('#templates-cooperators-tables').datagrid('clearChecked');
        }
    });
}

function onDowbloadWindowClose() {
    return;
}