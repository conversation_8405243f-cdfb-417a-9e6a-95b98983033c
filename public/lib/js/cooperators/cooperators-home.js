jQuery.extend(jQuery.fn.datagrid.defaults, {
	loader: function (params, succ, error) {
		var options = jQuery(this).datagrid('options');
		var rpcQuery = options.rpcParams || [];	

		var paging = [
			params.page,
			params.rows,
			params.sort,
			params.order
		] 
		var rpcAllParams = rpcQuery.concat(paging);

		var params = JSON.stringify({
			"method": "read", 
			"params": rpcAllParams, 
			"id": 1,
			"jsonrpc": "2.0"
		});
		var url = options.url;
		jQuery.ajax({
			url: url,
			data: params,
			accepts: 'application/json',
			contentType: 'application/json',
			method: 'post',
			dataType: 'json',
			processData: false
		})
		.done(succ);
	},
	loadFilter: function (data) {
		if(typeof data.result === 'undefined'){
			return data || [];			
		}else{
			return data.result || [];			
		}
	}
});

jQuery.extend(jQuery.fn.datagrid.methods, {
	loadRpc: function(element, params) {
		jQuery(element).datagrid({
			rpcParams: params
		});
	},
});

jQuery.extend(jQuery.fn.combobox.defaults, {
	loader: function (params, succ, error) {
		var options = jQuery(this).combobox('options');
		var rpcQuery = options.rpcParams || [];	

		var params = JSON.stringify({
			"method": "read", 
			"params": rpcQuery, 
			"id": 1,
			"jsonrpc": "2.0"
		});
		var url = options.url;
		jQuery.ajax({
			url: url,
			data: params,
			accepts: 'application/json',
			contentType: 'application/json',
			method: 'post',
			dataType: 'json',
			processData: false
		})
		.done(succ);
	},
	loadFilter: function (data) {
		if(typeof data.result === 'undefined'){
			return data || [];			
		}else{
			return data.result || [];			
		}
	}
});

jQuery.extend(jQuery.fn.tree.defaults, {
	loader: function (filterParams, succ, error) {
		var options = jQuery(this).tree('options');
		var rpcParams = options.rpcParams || [];

		rpcParams.push(filterParams);
		rpcParams.push(options.page);
		rpcParams.push(options.sort);	
		rpcParams.push(options.order);

		var params = JSON.stringify({
			"method": "read", 
			"params": rpcParams, 
			"id": 1,
			"jsonrpc": "2.0"
		});
		var url = options.url;
		jQuery.ajax({
			url: url,
			data: params,
			accepts: 'application/json',
			contentType: 'application/json',
			method: 'post',
			dataType: 'json',
			processData: false
		})
		.done(succ);
	},
	loadFilter: function (data) {
		return data.result || [];
	}
});

jQuery(function(){
    setUserLastLogin();
    
	//retrieve GET parameters
	var GET = {};
	location.search.substr(1).split("&").forEach(function(item) {GET[decodeURIComponent(item.split("=")[0])] = decodeURIComponent(item.split("=")[1])});

	//change URL without refresh if it's posible
	if (history && history.replaceState){
		history.replaceState(null, null, 'index.php?page=' + GET.page);
	}

	initCooperatorsTree(1, GET);

});

function initCooperatorInfo(cooperator){
    if(!cooperator)
    {
        jQuery('#info-cooperator-name').html('');
        jQuery('#info-cooperator-surname').html('');
        jQuery('#info-cooperator-lastname').html('');
        jQuery('#info-cooperator-egn').html('');
        jQuery('#info-cooperator-lk-nomer').html('');
        jQuery('#info-cooperator-lk-izdavane').html('');
        jQuery('#info-cooperator-dead').html('');
        jQuery('#info-cooperator-book-number').html('');
        jQuery('#info-cooperator-partida-number').html('');
        jQuery('#info-cooperator-date-entry').html('');
        jQuery('#info-cooperator-paid-in-capital').html('');
        jQuery('#info-cooperator-current-capital').html('');
        jQuery('#info-cooperator-excluded').html('');
        jQuery('#info-cooperator-date-excluded').html('');
        
        return false;
    }
    
    if(cooperator.excluded){
    	excluded_text = "Да";
    }else{
    	excluded_text = "Не";
    }

    if(cooperator.is_dead){
    	is_dead_text = "Да";
    }else{
    	is_dead_text = "Не";
    }

    jQuery('#info-cooperator-name').html(cooperator.name);
    jQuery('#info-cooperator-surname').html(cooperator.surname);
    jQuery('#info-cooperator-lastname').html(cooperator.lastname);
    jQuery('#info-cooperator-egn').html(cooperator.egn);
    jQuery('#info-cooperator-lk-nomer').html(cooperator.lk_nomer);
    jQuery('#info-cooperator-lk-izdavane').html(cooperator.lk_izdavane);
    jQuery('#info-cooperator-dead').html(is_dead_text);
    jQuery('#info-cooperator-book-number').html(cooperator.book_number);
    jQuery('#info-cooperator-partida-number').html(cooperator.partida_number);
    jQuery('#info-cooperator-date-entry').html(cooperator.dateЕntry);
    jQuery('#info-cooperator-paid-in-capital').html(cooperator.paid_in_capital + ' лв.');
    jQuery('#info-cooperator-current-capital').html(cooperator.current_capital + ' лв.');
    jQuery('#info-cooperator-excluded').html(excluded_text);
    jQuery('#info-cooperator-date-excluded').html(cooperator.dateЕxcluded);
}