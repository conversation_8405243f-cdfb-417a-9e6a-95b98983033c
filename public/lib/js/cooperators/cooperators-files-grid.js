function initCooperatorsFilesGrid(cooperator_id) {
	jQuery('#cooperators-files-tables').datagrid({
		iconCls: 'icon-files',
		title: 'Документи',
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		fit: true,
		fitColumns: true,
		showFooter: false,
		url: 'index.php?cooperators-rpc=cooperators-files-maingrid',
		rpcParams: [cooperator_id],
		idField: 'id',
		singleSelect: true,
		frozenColumns: [[
				{
					field: 'ck',
					checkbox: true
				}
			]],
		columns: [[
				{
					field: 'filename',
					title: '<b>Файл</b>',
					sortable: false,
					width: 100
				}, {
					field: 'date',
					title: '<b>Дата</b>',
					sortable: false,
					width: 100
				}
			]],
		rownumbers: true,
		toolbar: [{
				id: 'btnaddcooperatorsfile',
				text: 'Добавяне',
				iconCls: 'icon-add',
				handler: function() {
					if (cooperator_id) {
						initFileUploads(cooperator_id);
						jQuery('#win-add-file').window('open');
					} else {
						jQuery.messager.alert('Грешка', 'Не е избран договор.');
					}
				}
			}, {
				id: 'btndeletecooperatorsfile',
				text: 'Изтриване',
				iconCls: 'icon-remove',
				handler: function() {
					var getChecked = jQuery('#cooperators-files-tables').datagrid('getChecked');

					if (getChecked[0]) {
						jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да премахнете този запис?', function(r) {
							if (r) {
								TF.Rpc.Cooperators.CooperatorsFilesGrid.delete(getChecked[0].id)
						        .done();

								jQuery('#cooperators-files-tables').datagrid('reload');
								jQuery('#cooperators-files-tables').datagrid('uncheckAll');
								jQuery('#cooperators-files-tables').datagrid('unselectAll');
							}
						});
					} else {
						jQuery.messager.alert('Грешка', 'Моля изберете договори, който да бъдат премахнати.');
					}
				}
			}, {
				id: 'btndownloadcooperatorsfile',
				text: 'Изтегляне',
				iconCls: 'icon-export',
				handler: function() {
					var getChecked = jQuery('#cooperators-files-tables').datagrid('getChecked');

					if (getChecked[0]) {
						var file_id = getChecked[0]['id'];
						window.location.assign("index.php?page=Cooperators.Home&attached=1&file_id=" + file_id);
					} else {
						jQuery.messager.alert('Грешка', 'Моля изберете файл, който искате да изтеглите.');
					}
				}
			}],
		onBeforeLoad: function() {
			jQuery('#cooperators-files-tables').datagrid('clearChecked');
		}
	});
}

function initFileUploads(cooperator_id)
{
    const url  = "index.php?json=cooperators-upload"; 

	jQuery("#uploader").pluploadQueue({
		// General settings
		runtimes: 'gears,html5,flash,silverlight,browserplus',
		url: url,
		multipart_params : {
            "cooperator_id" : cooperator_id,
        },
		max_file_size: '100mb',
		unique_names: true,
		// Flash settings
		flash_swf_url: 'lib/js_external/fileupload/plupload.flash.swf'
	});

	var uploader = jQuery('#uploader').pluploadQueue();
	uploader.bind('UploadComplete', function() {
		jQuery('#win-add-file').window('close');
		jQuery('#cooperators-files-tables').datagrid('reload');
	});
}