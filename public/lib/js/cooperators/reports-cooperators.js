var _fileName = "";

function initSummaryReportGrid() {
  var winDownload = jQuery('#win-download');
  var downloadFile = jQuery('#btn-download-file');
  var cancelDownloadFile = jQuery('#btn-download-file-close');

  winDownload.window({
      onClose: onDowbloadSummaryWindowClose
  }); 

  jQuery('#summary-report-table').datagrid({
      nowrap: false,
      autoRowHeight: true,
      striped: true,
      pageSize: 30,
      fit: true,
      fitColumns: true,
      singleSelect:true,
      showFooter: true,
      url: 'index.php?cooperators-rpc=summary-report-cooperators',
      idField: '',
      sortName: 'id',
      border: true,
      sortOrder: 'desc',
      rpcParams: [null],
      columns: [[{
                  field: 'cooperator_names',
                  title: '<b>Име</b>',
                  width: 60
              },{
                  field: 'egn',
                  title: '<b>ЕГН</b>',
                  width: 60
              },{
                  field: 'book_number',
                  title: '<b>Номер <br>на книга</b>',
                  width: 60
              },{
                  field: 'partida_number',
                  title: '<b>Номер <br>на партида</b>',
                  width: 60
              },{
                  field: 'date_entry',
                  title: '<b>Дата <br>на вписване</b>',
                  sortable: true,
                  width: 60
              },{
                  field: 'paid_in_capital',
                  title: '<b>Внесен <br>капитал (лв.)</b>',
                  sortable: true,
                  width: 60
              },{
                  field: 'current_capital',
                  title: '<b>Текущ <br>капитал (лв.)</b>',
                  width: 60
              },{
                  field: 'excluded',
                  title: '<b>Изключен от <br>кооперацията</b>',
                  width: 60
              },{
                  field: 'date_excluded',
                  title: '<b>Дата на <br>изключване</b>',
                  sortable: true,
                  width: 60
              },{
                  field: 'is_dead',
                  title: '<b>Починал</b>',
                  width: 60
              }

              ]],
      toolbar: [
              {
                  id: 'btn-exp',
                  text: 'Експорт',
                  iconCls: 'icon-csv',
                  handler: function() {
                      var getRows = jQuery('#summary-report-table').datagrid('getRows');

                      if (getRows.length > 0) {
                          var filterObj = filterOnlySummaryReportsCooperators();

                          TF.Rpc.Cooperators.SummaryReportCooperatorsGrid.expSummaryReport(filterObj)
                          .done(function(data) {
                              winDownload.window('open');
                              var path = data.path;
                              _fileName = data.file_name;
                              downloadFile.attr("href", path); 
                          });
                      }else
                      {
                          jQuery.messager.alert('Грешка', 'Липсват кооператори за експорт.');
                      }
                  }
              },
              {
                id: 'btn-summary-report-print',
                text: 'Отпечатай',
                iconCls: 'icon-print',
                handler: function() {
                    printSummaryReport();
                }
              }
      ], 
      pagination: true,
      rownumbers: true,
  });

  //custom pager
  var pager = jQuery('#summary-report-table').datagrid('getPager');
  pager.pagination({
      beforePageText: 'Стр.',
      displayMsg: 'От {from} до {to} от {total}'
  });
}

jQuery(function() {

  jQuery('#btn-search-summary-report').bind('click', filterSummaryReportsCooperators); 
  jQuery('#btn-clear-search-summary-report').bind('click', clearSummaryReportsCooperatorsFilter); 

  jQuery('#date-excluded-from').datebox();
  jQuery('#date-excluded-to').datebox();
  jQuery('#date-entry-from').datebox();
  jQuery('#date-entry-to').datebox();

  jQuery('#btn-search-excluded-report').bind('click', filterExcludedReportsCooperators); 
  jQuery('#btn-clear-search-excluded-report').bind('click', clearExcludedReportsCooperatorsFilter); 

  jQuery('#search-excluded-report-date-entry-from').datebox();
  jQuery('#search-excluded-report-date-entry-to').datebox();
  jQuery('#search-excluded-report-date-excluded-from').datebox();
  jQuery('#search-excluded-report-date-excluded-to').datebox();

});


function filterSummaryReportsCooperators() {
  var filterObj = filterOnlySummaryReportsCooperators();

  jQuery('#summary-report-table').datagrid('loadRpc', [filterObj]);
}

function filterOnlySummaryReportsCooperators() {
  var filterObj = new Object();

  filterObj.name = jQuery('#search-cooperator-summary-report-name').val();
  filterObj.egn = jQuery('#search-cooperator-summary-report-egn').val();
  filterObj.dateExcludedFrom = jQuery('#date-excluded-from').datebox('getValue');
  filterObj.dateExcludedTo = jQuery('#date-excluded-to').datebox('getValue');
  filterObj.dateEntryFrom = jQuery('#date-entry-from').datebox('getValue');
  filterObj.dateEntryTo = jQuery('#date-entry-to').datebox('getValue');
  filterObj.excluded = jQuery('#search-excluded').combo('getValue');
  filterObj.paidInCapitalFrom = jQuery('#paid-in-capital-from').val();
  filterObj.paidInCapitalTo = jQuery('#paid-in-capital-to').val();

  return filterObj;
}

function clearSummaryReportsCooperatorsFilter() {

  jQuery('#search-cooperator-summary-report-name').val('');
  jQuery('#search-cooperator-summary-report-egn').val('');

  jQuery('#date-excluded-from').datebox('setValue', '');
  jQuery('#date-excluded-to').datebox('setValue', '');
  jQuery('#date-entry-from').datebox('setValue', '');
  jQuery('#date-entry-to').datebox('setValue', '');

  jQuery('#search-excluded').combobox('setValue', -1);
  
  jQuery('#paid-in-capital-from').val('');
  jQuery('#paid-in-capital-to').val('');

  jQuery('#summary-report-table').datagrid('loadRpc', [null]);
}

/* ExcludedReport */

function initExcludedReportGrid() {
  var winDownload = jQuery('#win-download');
  var downloadFile = jQuery('#btn-download-file');
  var cancelDownloadFile = jQuery('#btn-download-file-close');

  winDownload.window({
      onClose: onDowbloadExcludedWindowClose
  });

  jQuery('#excluded-report-table').datagrid({
      nowrap: false,
      autoRowHeight: true,
      striped: true,
      pageSize: 30,
      fit: true,
      fitColumns: true,
      singleSelect:true,
      showFooter: true,
      url: 'index.php?cooperators-rpc=excluded-report-cooperators',
      idField: '',
      sortName: 'id',
      border: true,
      sortOrder: 'desc',
      rpcParams: [null],
      columns: [[{
                  field: 'cooperator_names',
                  title: '<b>Име</b>',
                  width: 60
              },{
                  field: 'egn',
                  title: '<b>ЕГН</b>',
                  width: 60
              },{
                  field: 'book_number',
                  title: '<b>Номер <br>на книга</b>',
                  width: 60
              },{
                  field: 'partida_number',
                  title: '<b>Номер <br>на партида</b>',
                  width: 60
              },{
                  field: 'date_entry',
                  title: '<b>Дата <br>на вписване</b>',
                  sortable: true,
                  width: 60
              },{
                  field: 'paid_in_capital',
                  title: '<b>Внесен <br>капитал (лв.)</b>',
                  sortable: true,
                  width: 60
              },{
                  field: 'current_capital',
                  title: '<b>Текущ <br>капитал (лв.)</b>',
                  width: 60
              },{
                  field: 'excluded',
                  title: '<b>Изключен от <br>кооперацията</b>',
                  width: 60
              },{
                  field: 'date_excluded',
                  title: '<b>Дата на <br>изключване</b>',
                  sortable: true,
                  width: 60
              },{
                  field: 'is_dead',
                  title: '<b>Починал</b>',
                  width: 60
              }
              ]],
      toolbar: [
              {
                  id: 'btn-exp',
                  text: 'Експорт',
                  iconCls: 'icon-csv',
                  handler: function() {
                      var getRows = jQuery('#excluded-report-table').datagrid('getRows');
                      var winDownload = jQuery('#win-download');
                      var downloadFile = jQuery('#btn-download-file');
                      var cancelDownloadFile = jQuery('#btn-download-file-close');

                      if (getRows.length > 0) {
                          var filterObj = filterOnlyExcludedReportsCooperators();

                      	  TF.Rpc.Cooperators.ExcludedReportCooperatorsGrid.expExcludedReport(filterObj)
                          .done(function(data) {
                              winDownload.window('open');
                              var path = data.path;
                              _fileName = data.file_name;
                              downloadFile.attr("href", path); 
                          });
                      }else
                      {
                          jQuery.messager.alert('Грешка', 'Липсват кооператори за експорт.');
                      }
                  }
              },
              {
                id: 'btn-excluded-report-print',
                text: 'Отпечатай',
                iconCls: 'icon-print',
                handler: function() {
                    printExcludedReport();
                }
              }
      ],        
      pagination: true,
      rownumbers: true,
  });

  //custom pager
  var pager = jQuery('#excluded-report-table').datagrid('getPager');
  pager.pagination({
      beforePageText: 'Стр.',
      displayMsg: 'От {from} до {to} от {total}'
  });
}

function filterExcludedReportsCooperators() {
  var filterObj = filterOnlyExcludedReportsCooperators();

  jQuery('#excluded-report-table').datagrid('loadRpc', [filterObj]);
}

function filterOnlyExcludedReportsCooperators(){
    var filterObj = new Object();

    filterObj.name = jQuery('#search-excluded-report-name').val();
    filterObj.egn = jQuery('#search-excluded-report-egn').val();
    filterObj.dateExcludedFrom = jQuery('#search-excluded-report-date-excluded-from').datebox('getValue');
    filterObj.dateExcludedTo = jQuery('#search-excluded-report-date-excluded-to').datebox('getValue');
    filterObj.dateEntryFrom = jQuery('#search-excluded-report-date-entry-from').datebox('getValue');
    filterObj.dateEntryTo = jQuery('#search-excluded-report-date-entry-to').datebox('getValue');
    filterObj.excluded = jQuery('#search-excluded').combo('getValue');
    filterObj.paidInCapitalFrom = jQuery('#search-excluded-report-paid-in-capital-from').val();
    filterObj.paidInCapitalTo = jQuery('#search-excluded-report-paid-in-capital-to').val();
    filterObj.paidOutCapital = jQuery('input[name=search-excluded-report-paid-out-capital]:checked').val()

    return filterObj;
}

function clearExcludedReportsCooperatorsFilter() {

  jQuery('#search-excluded-report-name').val('');
  jQuery('#search-excluded-report-egn').val('');

  jQuery('#search-excluded-report-date-excluded-from').datebox('setValue', '');
  jQuery('#search-excluded-report-date-excluded-to').datebox('setValue', '');
  jQuery('#search-excluded-report-date-entry-from').datebox('setValue', '');
  jQuery('#search-excluded-report-date-entry-to').datebox('setValue', '');

  jQuery('#search-excluded').combobox('setValue', -1);
  
  jQuery('#paid-in-capital-from').val('');
  jQuery('#paid-in-capital-to').val('');
  jQuery('input[name=search-excluded-report-paid-out-capital]').prop("checked", false);

  jQuery('#excluded-report-table').datagrid('loadRpc', [null]);
}

function onDowbloadExcludedWindowClose() {
    return;
}

function onDowbloadSummaryWindowClose() {
    return;
}

function printSummaryReport() {
  var data = jQuery('#summary-report-table').datagrid('getData');
  var options = jQuery('#summary-report-table').datagrid('options');

  if (data.length == 0) {
    jQuery.messager.alert('Грешка', 'Не може да отпечатате празна справка.');
  }
  else {
      var filterObj = filterOnlySummaryReportsCooperators();
      var sort = options.sortName;
      var order = options.sortOrder;
      
      TF.Rpc.Cooperators.SummaryReportCooperatorsGrid.read(filterObj, null, null, sort, order)
      .done(function(data){
    	  var gridData = data.rows;
    	  var footerData = data.footer;

    	  var  html = '<style>@page{size: landscape;}body,table{font-family: Arial;font-size: 10px;}</style>' +
    	  '<h2 align="center">Обща справка кооператори</h2>';
   
    	  if(filterObj.excluded == "-1"){
    		  filterObj.excluded = "-";
    	  }

	        html += '<b>Филтър:</b><br/>';
	        html += 'Име: <b>' + filterObj.name + '</b><br/>';
	        html += 'ЕГН: <b>' + filterObj.egn + '</b><br/>';
	        html += 'Дата на вписване:<br/>';
	        html += 'От дата: <b>' + filterObj.dateEntryFrom + '</b><br/>';
	        html += 'До дата: <b>' + filterObj.dateEntryTo + '</b><br/>';
	        html += 'Изключен от кооперацията: ' + filterObj.excluded + '<br/>';
	        html += 'Дата на изключване:<br/>';
	        html += 'От дата: <b>' + filterObj.dateExcludedFrom + '</b><br/>';
	        html += 'До дата: <b>' + filterObj.dateExcludedTo + '</b><br/>';
	        html += 'Внесен капитал:<br/>';
	        html += 'Над: <b>' + filterObj.paidInCapitalFrom + 'лв.</b><br/>';
	        html += 'Под: <b>' + filterObj.paidInCapitalTo + 'лв.</b><br/><br/>';
   
	        var header = {};
	        var columns = options.columns[0];
	        for(var i=0; i<columns.length; i++)
	        {
	            header[columns[i].field] = columns[i].title;
	        }
   
	        var rows = gridData;
	        rows.push(footerData[0]);
	        html += Templates.table(header, rows);

          jQuery('body').append('<iframe id="printf" name="printf"></iframe>');
          var newWin = window.frames['printf'];
          newWin.document.write('<body onload=window.print()>'+html+'</body>');
          newWin.document.close();
          setTimeout(function () {
              jQuery('#printf').remove();
          }, 1000);
      });

      return false;
  }
}

function printExcludedReport(){
  var data = jQuery('#excluded-report-table').datagrid('getData');
  var options = jQuery('#excluded-report-table').datagrid('options');

  if (data.length == 0) {
    jQuery.messager.alert('Грешка', 'Не може да отпечатате празна справка.');
  }
  else {
      var filterObj = filterOnlyExcludedReportsCooperators();
      var sort = options.sortName;
      var order = options.sortOrder;

  	TF.Rpc.Cooperators.ExcludedReportCooperatorsGrid.read(filterObj, null, null, sort, order)
    .done(function(data){
    	var gridData = data.rows;
        var footerData = data.footer;

        var  html = '<style>@page{size: landscape;}body,table{font-family: Arial;font-size: 10px;}</style>' +
        '<h2 align="center">Справка за изключени член - кооператори</h2>';
   
        if(filterObj.paidOutCapital == undefined){
          filterObj.paidOutCapital = "-";
        }
        else if(filterObj.paidOutCapital == 'yes'){
          filterObj.paidOutCapital = "Да";
        } else {
           filterObj.paidOutCapital = "Не";
        }

        html += '<b>Филтър:</b><br/>';
        html += 'Име: <b>' + filterObj.name + '</b><br/>';
        html += 'ЕГН: <b>' + filterObj.egn + '</b><br/>';
        html += 'Дата на вписване:<br/>';
        html += 'От дата: <b>' + filterObj.dateEntryFrom + '</b><br/>';
        html += 'До дата: <b>' + filterObj.dateEntryTo + '</b><br/>';
        html += 'Дата на изключване:<br/>';
        html += 'От дата: <b>' + filterObj.dateExcludedFrom + '</b><br/>';
        html += 'До дата: <b>' + filterObj.dateExcludedTo + '</b><br/>';
        html += 'Внесен капитал:<br/>';
        html += 'Над: <b>' + filterObj.paidInCapitalFrom + 'лв.</b><br/>';
        html += 'Под: <b>' + filterObj.paidInCapitalTo + 'лв.</b><br/><br/>';
        html += 'Изплатен дялов капитал: ' + filterObj.paidOutCapital + '<br/>';

        var header = {};
        var columns = options.columns[0];
        for(var i=0; i<columns.length; i++)
        {
            header[columns[i].field] = columns[i].title;
        }
        
        var rows = gridData;
        rows.push(footerData[0]);
        html += Templates.table(header, rows);

        jQuery('body').append('<iframe id="printf" name="printf"></iframe>');
        var newWin = window.frames['printf'];
        newWin.document.write('<body onload=window.print()>'+html+'</body>');
        newWin.document.close();
        setTimeout(function () {
            jQuery('#printf').remove();
        }, 1000);
    });

    return false;
  }
}