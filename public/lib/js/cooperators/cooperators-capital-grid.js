var documentEditIndex = undefined;

function initCooperatorCapitalGrid(cooperator_id) {
    jQuery('#cooperator-capital-table').datagrid({
        title: 'Изплащане на дялов капитал',
        iconCls: 'icon-rents',
        nowrap: false,
        autoRowHeight: true,
        striped: true,
        pageSize: 30,
        fit: true,
        fitColumns: true,
        singleSelect:true,
        showFooter: true,
        url: 'index.php?cooperators-rpc=cooperator-capital',
        idField: '',
        sortName: 'cc.id',
        border: true,
        sortOrder: 'desc',
        rpcParams: [cooperator_id],
        pagination: true,
        rownumbers: true,
        columns:[[
            {
                  field: 'cooperator_names',
                  title: '<b>Име</b>',
                  width: 60
              },{
                  field: 'egn',
                  title: '<b>ЕГН</b>',
                  width: 60
              },{
                  field: 'date_excluded',
                  title: '<b>Дата на <br>изключване</b>',
                  sortable: true,
                  width: 60
              },{
                  field: 'owe_capital',
                  title: '<b>Дължим дялов <br>капитал (лв.)</b>',
                  sortable: true,
                  width: 60
              },{
                  field: 'cashout',
                  title: '<b>Изплатен дялов <br>капитал (лв.)</b>',
                  sortable: true,
                  width: 60
              },{
                  field: 'pay_date',
                  title: '<b>Дата на <br>изплащане</b>',
                  sortable: true,
                  width: 60
              }
        ]],
        toolbar: [
            {
                id: 'btnpay',
                text: 'Изплащане',
                iconCls: 'icon-payments',
                handler: function() {
                    var getSelected = jQuery('#cooperators-tree').tree('getSelected');
                    
                    if (getSelected) {

                        //Open Изплащане
                        jQuery('#btnpay').bind('click', openAddCapital);
                        
                    } else
                    {
                        jQuery.messager.alert('Грешка', 'Моля изберете кооператор.');
                    }
                }
            }, {
                id: 'btn_reversal',
                text: 'Сторниране',
                iconCls: 'icon-payments',
                handler: function() {
                    var getSelected = jQuery('#cooperators-tree').tree('getSelected');
                    
                    if (getSelected) {

                        //Open Сторниране
                        jQuery('#btn_reversal').bind('click', openAddCapitalReversal);
                        
                    } else
                    {
                        jQuery.messager.alert('Грешка', 'Моля изберете кооператор.');
                    }
                }
            }
        ]
    });

    //custom pager
    var pager = jQuery('#cooperator-capital-table').datagrid('getPager');
    pager.pagination({
      beforePageText: 'Стр.',
      displayMsg: 'От {from} до {to} от {total}'
    });
}

function openAddCapitalReversal() {

    initRequiredFieldsCapitalReversal(_currentCapital); 

    jQuery('#owe-capital-reversal').html(_currentCapital);

    jQuery('#win-cooperator-add-capital-reversal').window('open');
}

function openAddCapital() {

    initRequiredFieldsCapital(); 

    jQuery('#win-add-cooperator-capital').window('setTitle', 'Изплащане на капитал');
    jQuery('#btn-add-cooperator-capital > a').linkbutton({text:'Изплати'});

    jQuery('#owe-capital').html(_currentCapital);
    
    resetAddCapitalForm();

    jQuery('#win-cooperator-add-capital').window('open');
}

function resetAddCapitalForm() {

    jQuery('#cashout-capital').numberbox('setValue', '');
    jQuery('#capital-representative').val('');
    jQuery('#capital-iban').val('');

    jQuery('#capital-generate-order').removeAttr('checked');
    jQuery('#capital-generate-payment').removeAttr('checked');
    jQuery('#capital-payment-method-cash').removeAttr('checked');
    jQuery('#capital-payment-method-bank').removeAttr('checked');

    jQuery('#capital-pay-date').datebox({
        value: _todayDate
    });
}

jQuery(function(){  

    jQuery('#capital-payment-method-cash').change(function() {
        if (jQuery('#capital-payment-method-cash').is(':checked') == true)
        {
            jQuery('#bank-account-row').hide();
        }
    });

    jQuery('#capital-payment-method-bank').change(function() {
        if (jQuery('#capital-payment-method-bank').is(':checked') == true)
        {
            jQuery('#bank-account-row').show();
        }
    });

    jQuery('#capital-pay-date').datebox({
        requred: true,
        missingMessage: 'Моля въведете дата.',
        value: _todayDate
    });

    jQuery('#capital-pay-date-reversal').datebox({
        requred: true,
        missingMessage: 'Моля въведете дата.',
        value: _todayDate
    });

    initRequiredFieldsCapital();

    //Create capital
    jQuery('#btn-add-capital').bind('click', createCapital);

    //Close capital
    jQuery('#btn-close-capital').bind('click', closeCapital);

    //Create capital Reversal
    jQuery('#btn-add-capital-reversal').bind('click', createCapitalReversal);

    //Close capital Reversal
    jQuery('#btn-close-capital-reversal').bind('click', closeCapitalReversal);

});    

function closeCapital() {

    jQuery('#win-cooperator-add-capital').window('close');
}

function closeCapitalReversal() {

    jQuery('#win-cooperator-add-capital-reversal').window('close');
}      

function createCapital() {

    if(!validateSubmitInfoCreateCapital())
    {
        return;
    }

    //form values
    var params = getFormValuesCapital();
    params.cooperator_id = _cooperatorId;
    var cooperatorId = _cooperatorId;
    var cashout = params.cashout;
    var payDate = params.pay_date;
    var iban = params.iban;
    var generateOrder = params.generate_order;
    var generatePayment = params.generate_payment;
    var representative = params.representative;

    TF.Rpc.Cooperators.CooperatorsCapitalGrid.create(params)
    .done();
    jQuery('#cooperator-capital-table').datagrid('reload');

    TF.Rpc.Cooperators.CooperatorsTree.load(_cooperatorId)
    .done(function(data) {
        _currentCapital = data.current_capital;

        initCooperatorInfo(data);
    });    

    var winDownload = jQuery('#win-download');
    var downloadFile = jQuery('#btn-download-file');
    var cancelDownloadFile = jQuery('#btn-download-file-close');

    if(generateOrder){
        TF.Rpc.Cooperators.ExportCooperatorPaymentsBlanks.expCooperatorOrder(cooperatorId, payDate, cashout)
        .done(function(data) {
            winDownload.window('open');
            var path = data.pdf_blank_file;
            downloadFile.attr("href", path); 
        });
    }  
    if(generatePayment){
    	TF.Rpc.Cooperators.ExportCooperatorPaymentsBlanks.expCooperatorPayment(cooperatorId, cashout, iban, representative)
        .done(function(data) {
            winDownload.window('open');
            var path = data.pdf_blank_file;
            downloadFile.attr("href", path); 
        });
    }  

    jQuery('#win-cooperator-add-capital').window('close');
}

function getFormValuesCapital() {
    var cashout = jQuery('#cashout-capital').val();
    
    if(cashout <= 0){
        return;
    }

    var params = {
        cashout: cashout,
        iban: jQuery('#capital-iban').val(),
        representative: jQuery('#capital-representative').val(),
        pay_date: jQuery('#capital-pay-date').datebox('getValue'),
    };

    if(jQuery('#capital-generate-order').is(":checked")) { 
        params.generate_order = true; 
    }else{
        params.generate_order = false; 
    }
    if(jQuery('#capital-generate-payment').is(":checked")) { 
        params.generate_payment = true; 
    }else{
        params.generate_payment = false; 
    }

    return params;
}

function validateSubmitInfoCreateCapital() {

    if (jQuery('#cashout-capital').numberbox('isValid') && 
        jQuery('#capital-representative').validatebox('isValid')) {

        if(jQuery('#cashout-capital').numberbox('getValue') > 0.00)
        {
            return true;    
        }else{
            jQuery.messager.alert('Грешка', 'Сума за изплащане трябва де е по-голяма от 0.00 лв.');
        return false;    
        }    
        
    } else {
        jQuery.messager.alert('Грешка', 'Моля попълнете задължителните полета.');
        return false;
    }
}

function validateSubmitInfoCreateCapitalReversal() {

    if (jQuery('#cashout-capital-reversal').numberbox('isValid')) {
        return true;
    } else {
        jQuery.messager.alert('Грешка', 'Моля попълнете задължителните полета.');
        return false;
    }
}

function initRequiredFieldsCapital() {
    numLengthbox();
    
    jQuery('#cashout-capital').numberbox({
        required: true,
        precision: 2,
        min: 0.01,
        missingMessage: 'Моля въведете Сума за изплащане.'
    });
    jQuery('#capital-representative').validatebox({
        required: true,
        missingMessage: 'Моля въведете Представител.'
    });
    
}

function createCapitalReversal() {

    if(!validateSubmitInfoCreateCapitalReversal())
    {
        return;
    }

    //form values
    var params = getFormValuesCapitalReversal();

    params.cooperator_id = _cooperatorId;

    TF.Rpc.Cooperators.CooperatorsCapitalGrid.createReversal(params)
    .done();
    jQuery('#cooperator-capital-table').datagrid('reload');

    TF.Rpc.Cooperators.CooperatorsTree.load(_cooperatorId)
    .done(function (data){

        _currentCapital = data.current_capital;

        initCooperatorInfo(data);
    });        

    jQuery('#win-cooperator-add-capital-reversal').window('close');

}

function getFormValuesCapitalReversal() {
    var cashoutReversal = jQuery('#cashout-capital-reversal').val();

    var params = {
        cashout: cashoutReversal,
        pay_date: jQuery('#capital-pay-date-reversal').datebox('getValue'),
    };

    return params;
}

function initRequiredFieldsCapitalReversal(maxSum) {
    numLengthbox();
    
    jQuery('#cashout-capital-reversal').numberbox({
        required: true,
        precision: 2,
        min: 0.01,
        max: parseFloat(maxSum),
        missingMessage: 'Моля въведете Сума за изплащане.'
    });
    
}