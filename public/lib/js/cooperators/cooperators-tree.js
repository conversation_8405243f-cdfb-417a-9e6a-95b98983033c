var _cooperatorId;
var _currentCapital;

function initCooperatorsTree(pageNumber, filterObj) {
	_cooperatorId = undefined;
	var page_number = 1;

	if (pageNumber != undefined)
		page_number = pageNumber;

	jQuery('#cooperators-tree').tree({
		url: 'index.php?cooperators-rpc=cooperators-tree',
		animate: true,
		lines: true,
		sort: 'id',
		order: 'desc',
		page: page_number,
		rpcParams:[filterObj],
		onBeforeLoad: function(node, param) {

			initCooperatorInfo(0);
			initCooperatorsFilesGrid(0);
			initCooperatorHeritorsTree(0);
			initCooperatorCapitalGrid(0);
			initCooperatorDividendGrid('0');
		},

		onSelect: function(node) {
			TF.Rpc.Cooperators.CooperatorsTree.load(node.id)
			.done(function(data) {

				_currentCapital = data.current_capital;

				initCooperatorInfo(data);
			});
			var cooperatorId = node.id;

			//init tree and grid
			initCooperatorsFilesGrid(cooperatorId);
			initCooperatorHeritorsTree(cooperatorId);
			initCooperatorCapitalGrid(cooperatorId);
			initCooperatorDividendGrid(cooperatorId);
			_cooperatorId = cooperatorId;
		},
		onLoadSuccess: function() {
			var roots = jQuery('#cooperators-tree').tree('getRoots');
			var total = 0;
			var limit = 10;

			if (roots.length) {
				if (_cooperatorId != undefined) {
					var node = jQuery('#cooperators-tree').tree('find', _cooperatorId);
					jQuery('#cooperators-tree').tree('select', node.target);
				}
				else {
					jQuery('#cooperators-tree').tree('select', roots[0].target);
				}
				total = roots[0]['attributes']['pagination']['total'];
				limit = roots[0]['attributes']['pagination']['limit'];
			}
            else
            {
                jQuery('#content-layout').layout('remove', 'west');
            }

			//init pagination with total elements
			initCooperatorsPagination(total, limit);

			//endLoading();
		},
		formatter: function(node) {
			if (node.attributes.excluded)
				node.text = '<font color="#aaa">' + node.text + '</font>';

			return node.text;
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function initCooperatorsPagination(total, limit) {
	jQuery('#cooperators-tree-pagination').pagination({
		showPageList: false,
		showRefresh: false,
		displayMsg: '',
		total: total,
		pageSize: limit,
		onSelectPage: function(pageNumber, pageSize) {
			var obj = new Object();
			obj.cooperator_names = jQuery('#search-cooperator-name').val();
			obj.egn = jQuery('#search-cooperator-egn').val();
			initCooperatorsTree(pageNumber, obj);
		}
	});
}

var date = new Date();
var _todayDate = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();

jQuery(function() {
	numLengthbox();
	jQuery('#date_entry').datebox({
		requred: true,
		missingMessage: 'Моля въведете дата.',
		value: _todayDate
	});

	new CardIdValidateBox('#lk_nomer');

	jQuery('#egn').numberbox({
		required: false,
		validType: 'setLength[10]',
		missingMessage: 'Моля въведете ЕГН на кооператор.',
		precision: 0,
		decimalSeparator: '',
		parser: function (number) {
			return number;
		}
    });

 	jQuery('#is-heritor').click(function(){
	    if(jQuery(this).is(':checked')){
	    	if(jQuery('#win-cooperator-add').window('panel').find('.panel-title').html() !== 'Добавяне на наследник')
	    	{
		        jQuery('#paid_in_capital').numberbox({
					required: false
				});
				jQuery('#paid_in_capital').numberbox('disable');
			}
	        jQuery('#excluded').combobox('disable');
	        jQuery('#is_dead').combobox('disable');
	        jQuery('#excluded_reason').textbox('disable');
	        jQuery('#date_excluded').datebox('disable');
	    } else {
	    	if(jQuery('#win-cooperator-add').window('panel').find('.panel-title').html() !== 'Добавяне на наследник')
	    	{
	    		jQuery('#paid_in_capital').numberbox({
					required: true
				});
				jQuery('#paid_in_capital').numberbox('enable');
	    	}

	        jQuery('#excluded').combobox('enable');
	        jQuery('#is_dead').combobox('enable');
	        if(jQuery('#excluded').combobox('getValue') === 1)
	        {
				jQuery('#excluded_reason').textbox('enable');
		        jQuery('#date_excluded').datebox('enable');
	        }
	    }
	});

	initRequiredFieldsCooperator();

	jQuery('#excluded').combobox({
		onSelect: function(record){
			var value = record.value;
			var selected = record.selected;

			var valueDateExcluded = '';
			if(value == 0) {
				valueDateExcluded = '';
				jQuery('#excluded_reason').textbox({
					disabled: selected,
					value: ''
				});
			}else{
				valueDateExcluded = _todayDate;
			}

			jQuery('#excluded_reason').textbox({
				disabled: selected
			});
			jQuery('#date_excluded').datebox({
				disabled: selected,
				value: valueDateExcluded
			});

		}
	});

	//Open Add Cooperator
	jQuery('#add-cooperator').bind('click', openAddCooperator);

	//Close Add Cooperator
	jQuery('#btn-close-cooperator').bind('click', closeAddCooperator);

	//Create cooperator
	jQuery('#btn-add-cooperator').bind('click', createCooperator);

	//Load cooperator
	jQuery('#load-cooperator').bind('click', loadCooperator);

	//Update cooperator
	jQuery('#btn-update-cooperator').bind('click', updateCooperator);

	//Delete cooperator
	jQuery('#delete-cooperator').bind('click', deleteCooperator);

	//Open Filter Cooperator
	jQuery('#filter-cooperators').bind('click', openFilterCooperator);

	//Execute Filter Cooperators
	jQuery('#btn-search-filter-cooperators').bind('click', executeFilterCooperators);

	//Close Filter Cooperators
	jQuery('#btn-cancel-filter-cooperators').bind('click', closeFilterCooperators);

	//Reset Filter Cooperators
	jQuery('#cancel-filter-cooperators').bind('click', resetFilterCooperators);

	//Отваряне на "Избор на вид справка"
	jQuery('#reports-cooperators').bind('click', openReportChoose);

	//Click на Обобщена справка
	jQuery('#summary-report').bind('click', openSummaryReport);

	//Click на Справка за изключени чл. кооператори
	jQuery('#excluded-report').bind('click', openExcludedReport);

	//Click на Разпечатка на бланка
	jQuery('#btn-print-cooperator').bind('click', openCooperatorBlank);
});

function changeValuePaidInCapital(){
	alert(jQuery(this).val());
	//jQuery('#current_capital > input').val(this.val());
}

function openCooperatorBlank(){
	var getSelected = jQuery('#cooperators-tree').tree('getSelected');
		if (getSelected) {
			initPrintCooperatorTemplatesGrid(getSelected.id);
			jQuery('#win-cooperators-templates').window('open');
		} else {
			jQuery.messager.alert('Грешка', 'Моля изберете кооператор!');
		}
		return false;
}

function openExcludedReport(){
	jQuery('#win-choose-report-type').window('close');

    initExcludedReportGrid();
    clearExcludedReportsCooperatorsFilter();

    jQuery('#win-excluded-report').window('open');
}

function openSummaryReport(){
	jQuery('#win-choose-report-type').window('close');

    initSummaryReportGrid();

    jQuery('#win-summary-report').window('open');
}

function openReportChoose() {
	jQuery('#win-choose-report-type').window('open');
}

function resetFilterCooperators() {

	jQuery('#search-cooperator-name').val('');
	jQuery('#search-cooperator-egn').val('');
	initCooperatorsTree();
	jQuery('#cooperators-tree-pagination').pagination('select', 1);
}

function closeFilterCooperators() {

	jQuery('#win-cooperators-filter').window('close');
}

function executeFilterCooperators() {
	var obj = new Object();
	obj.cooperator_names = jQuery('#search-cooperator-name').val();
	obj.egn = jQuery('#search-cooperator-egn').val();

	initCooperatorsTree(1, obj);
	closeFilterCooperators();
}

function openFilterCooperator() {
	jQuery('#win-cooperators-filter').window('open');
}

function openAddCooperator() {
	resetFormFieldsValues();
	initRequiredFieldsCooperator();
	jQuery('#win-cooperator-add').window({title:'Добавяне на кооператор'});
	jQuery('#paid_in_capital_text').html('Внесен капитал (лв.)');

	jQuery('#win-cooperator-add').window('open');
	jQuery('#btn-add-cooperator').show();
	jQuery('#btn-update-cooperator').hide();
}

function closeAddCooperator() {
	jQuery('#win-cooperator-add').window('close');
}

function loadCooperator() {

	var getSelected = jQuery('#cooperators-tree').tree('getSelected');
	if (getSelected) {

		//php function params
		var params = getSelected.id;

		 TF.Rpc.Cooperators.CooperatorsTree.load(params)
		    .done(function (data){

			//set window title
			jQuery('#win-cooperator-add').window({title:'Редактиране на кооператор'});

			//set values to the form fields
			setFormFieldsValues(data);

			initRequiredFieldsCooperator();

			if(data.excluded){

				jQuery('#excluded_reason').textbox({
					disabled: false,
					value: data.excluded_reason
				});
				jQuery('#date_excluded').datebox({
					requred: true,
					missingMessage: 'Моля въведете дата.',
					disabled: false,
					value: data.date_excluded
				});
			}

			jQuery('#win-cooperator-add').window('open');

			jQuery('#btn-update-cooperator').show();
			jQuery('#btn-add-cooperator').hide();
	    });

	} else {
		jQuery.messager.alert('Грешка', 'Моля изберете кооператор.');
	}
}

function resetFormFieldsValues()
{
	jQuery('#name').val('');
	jQuery('#surname').val('');
	jQuery('#lastname').val('');
	new EgnValidateBox('#egn').setValue('');
	new CardIdValidateBox('#lk_nomer').setValue('');
	jQuery('#lk_izdavane').val('');
	jQuery('#book_number').val('');
	jQuery('#partida_number').val('');
	jQuery('#date_entry').datebox({
		requred: true,
		missingMessage: 'Моля въведете дата.',
		value: _todayDate
	});
	jQuery('#paid_in_capital').numberbox('setValue', '');
	jQuery('#current_capital').numberbox('setValue', '');
	jQuery('#excluded').combobox('setValue', 0);
	jQuery('#excluded_reason').val('');
	jQuery('#date_excluded').datebox('reset');
	jQuery('#paid_in_capital').numberbox({
		required: true,
		disabled: false,
		missingMessage: 'Моля въведете сума за внесен капитал.',
		precision: 2
    });
	jQuery('#is_dead').combobox('setValue', 0);
	jQuery('#is-heritor').prop('checked', false);
}

function setFormFieldsValues(data)
{
	jQuery('#name').val(data.name);
	jQuery('#surname').val(data.surname);
	jQuery('#lastname').val(data.lastname);
	new EgnValidateBox('#egn').setValue(data.egn);
	new CardIdValidateBox('#lk_nomer').setValue(data.lk_nomer);
	jQuery('#lk_izdavane').val(data.lk_izdavane);
	jQuery('#book_number').val(data.book_number);
	jQuery('#partida_number').val(data.partida_number);
	jQuery('#date_entry').datebox({
		value: data.date_entry
	});

	jQuery('#paid_in_capital').numberbox('setValue', data.paid_in_capital);
	jQuery('#current_capital').numberbox('setValue', data.current_capital);
	jQuery('#excluded').combobox('setValue', data.excluded);
	jQuery('#excluded_reason').val(data.excluded_reason);
	jQuery('#date_excluded').datebox({
		value: data.date_excluded
	});
	jQuery('#is_dead').combobox('setValue', data.is_dead);
	jQuery('#is-heritor').prop('checked', data.heritor_only);

}

function updateCooperator() {

	var getSelected = jQuery('#cooperators-tree').tree('getSelected');
	if (getSelected) {

		if(!validateSubmitInfo())
		{
			return;
		}

		//form values
		var params = getFormValuesCooperator();
		params.id = getSelected.id;

		TF.Rpc.Cooperators.CooperatorsTree.update(params)
		.done(function (data){
			jQuery('#cooperators-tree').tree('reload');
			jQuery('#win-cooperator-add').window('close');
		}).fail(function(data){
			jQuery.messager.alert('Грешка', 'Системна грешка при редактиране на кооператор!', 'warning');
			return;
		});

	} else {
		jQuery.messager.alert('Грешка', 'Моля изберете кооператор.');
	}
}

function deleteCooperator() {

	var getSelected = jQuery('#cooperators-tree').tree('getSelected');
	if (getSelected) {
		jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да изтриете този кооператор?', function(r) {
			if (r) {
				_cooperatorId = undefined;

				//params
				var params = getSelected.id;

				TF.Rpc.Cooperators.CooperatorsTree.delete(params)
				.done();
				jQuery('#cooperators-tree').tree('reload');
			}
		});
	} else {
		jQuery.messager.alert('Грешка', 'Моля изберете кооператор.');
	}
}

function getFormValuesCooperator() {

	var params = {
		name: jQuery('#name').val(),
		surname: jQuery('#surname').val(),
		lastname: jQuery('#lastname').val(),
		egn: new EgnValidateBox('#egn').getValue(),
		lk_nomer: new CardIdValidateBox('#lk_nomer').getValue(),
		lk_izdavane: jQuery('#lk_izdavane').val(),
		book_number: jQuery('#book_number').val(),
		partida_number: jQuery('#partida_number').val(),
		date_entry: jQuery('#date_entry').datebox('getValue'),

		paid_in_capital: jQuery('#paid_in_capital').val(),
		current_capital: jQuery('#current_capital').val(),
		excluded: jQuery('#excluded').combo('getValue'),
		excluded_reason: jQuery('#excluded_reason').val(),
		date_excluded: jQuery('#date_excluded').datebox('getValue'),
		is_dead: jQuery('#is_dead').combo('getValue'),
		heritor_only: jQuery('#is-heritor').is(':checked')
	};

	return params;
}

function createCooperator() {

	if(!validateSubmitInfo())
	{
		return;
	}

	//form values
	var params = getFormValuesCooperator();
	var paidInCapital = jQuery('#paid_in_capital').numberbox('options');

	if(paidInCapital.disabled){
		params.current_capital = 0;
	}

	TF.Rpc.Cooperators.CooperatorsTree.create(params)
	.done(function (data) {

		var heritorCombobox = jQuery('#choose-heritor');
		var heritorId = data.cooperator_id;
		var warning = data.warning;

		if(paidInCapital.disabled &&
			jQuery('#win-cooperator-add').window('panel').find('.panel-title').html() === 'Добавяне на наследник'){
			heritorCombobox.combobox('reload');
			heritorCombobox.combobox('setValue', heritorId);
		}

		jQuery('#cooperators-tree').tree('reload');

		jQuery('#win-cooperator-add').window('close');

		if(warning === 'report_existed_for_coop_join_date'){
			jQuery.messager.alert("Грешка", 'За периода на включване на член - кооператора има дефиниран ' +
	                                         'отчет за изплащане на дивиденти. За да актуализирате отчета, ' +
                                           'използвайте бутон Редакция на страница „Дивиденти“.', "warning");
		}
    }).fail(function(data){
    	jQuery.messager.alert('Грешка', 'Системна грешка при добавяне на кооператор!', 'warning');
		return;
    });
}

function validateSubmitInfo() {
	if (jQuery('#name').validatebox('isValid') &&
		jQuery('#surname').validatebox('isValid') &&
		jQuery('#lastname').validatebox('isValid') &&
		(jQuery('#is-heritor').is(':checked') ||
		(jQuery('#is-heritor').is(':checked') === false &&
		jQuery('#paid_in_capital').val().length > 0))
		) {
		return true;
	} else {
		jQuery.messager.alert('Грешка', 'Моля попълнете задължителните полета.');
		return false;
	}
}

function initRequiredFieldsCooperator() {
	numLengthbox();
	var currentCapital = jQuery('#current_capital');
	var excluded = jQuery('#excluded');

	jQuery('#name').validatebox({
		required: true,
		missingMessage: 'Моля въведете име на кооператор.'
	});
	jQuery('#surname').validatebox({
		required: true,
		missingMessage: 'Моля въведете презиме на кооператор.'
	});
	jQuery('#lastname').validatebox({
		required: true,
		missingMessage: 'Моля въведете фамилия на кооператор.'
	});

	new EgnValidateBox('#egn');
	new CardIdValidateBox('#lk_nomer');

	jQuery('#paid_in_capital').numberbox({
		required: true,
		precision: 2,
		missingMessage: 'Моля въведете цифри.',
		onChange: function (newValue, oldValue) {
			currentCapital.numberbox('setValue', newValue);
		}
    });
	currentCapital.numberbox({
		required: false,
		disabled: false,
		precision: 2
    });
	jQuery('#excluded_reason').textbox({
		disabled: true
	});
	jQuery('#date_excluded').datebox({
		requred: true,
		missingMessage: 'Моля въведете дата.',
		disabled: true,
		value: ''
	});
}
