var root_id;
var tmpPath;
var reloadTarget;

function initCooperatorHeritorsTree(cooperator_id) {
	root_id = cooperator_id;
	tmpPath = cooperator_id;

	jQuery('#cooperator-heritors-tree').tree({
		url: 'index.php?cooperators-rpc=cooperator-heritors-tree',
		animate: true,
		lines: true,
		sort: 'id',
		order: 'desc',
		page: 1,
		onBeforeLoad: function(node, param) {
			//sending filter parameters with POST method
			param.path = tmpPath + '.*{1}';
		},
		onBeforeExpand: function(node) {
			tmpPath = node['attributes'].path;
		},
		onLoadSuccess: function() {
			
		}
	});
}

jQuery(function() {

	//Add cooperator heritor
	jQuery('#add_cooperator_heritor').bind('click', addCooperatorHeritor);	

	//Delete cooperator heritor
	jQuery('#delete_cooperator_heritor').bind('click', deleteCooperatorHeritor);	

	//Info cooperator heritor
	jQuery('#info_cooperator_heritor').bind('click', showCooperatorHeritorInfo);	

	jQuery('#btn-save-cooperator-heritor').bind('click', function() {
		var choosedHeritor = jQuery('#choose-heritor').combobox('getValue');	

		var obj = new Object;
		obj.parent = root_id;

		var getSelected = jQuery('#cooperator-heritors-tree').tree('getSelected');
		if(getSelected)
		{
			obj.parent = getSelected['attributes'].cooperator_id;
		}
		
		if(!choosedHeritor)
		{
			jQuery.messager.alert('Грешка', 'Моля изберете наследник.');
			return false;
		}
		obj.cooperator_id = choosedHeritor;

	    if (jQuery('#inherited-capital').numberbox('isValid')) {
	    	var inheritedCapital = jQuery('#inherited-capital').numberbox('getValue');

	        if(jQuery('#inherited-capital').numberbox('getValue') < 0.00)
	        {
	            jQuery.messager.alert('Грешка', 'Сума за наследен капитал трябва де е по-голяма от 0.00 лв.');
	        	return false;     
	        }  
	        obj.heritor_current_capital = inheritedCapital;
	    } 
	    else {
	        jQuery.messager.alert('Грешка', 'Моля попълнете сума за наследен капитал.');
	        return false;
	    }

		if(getSelected)
		{
			tmpPath = getSelected['attributes'].path;
			reloadTarget = getSelected;
		}
		else
		{
			tmpPath = root_id;
			reloadTarget = undefined;
		}

		TF.Rpc.Cooperators.CooperatorHeritorsTree.create(obj)
        .done(function (data)
        {
			initCooperatorHeritorsTree(root_id);
		});  

		jQuery('#win-choose-heritor').window('close');  

		
	});
	
	jQuery('#btn-search-cooperator-heritor').bind('click', function() {

		var egn = jQuery('#search-cooperator-heritor-by-egn').val();

		jQuery('#choose-heritor').combobox({
			url: 'index.php?cooperators-rpc=cooperators-heritors-combobox',
			valueField: 'id',
			textField: 'cooperator_names',
			rpcParams: [root_id, egn, true],
			required: true,
			missingMessage: 'Моля изберете наследник.',
			filter: function(q, row){
				var opts = jQuery(this).combobox('options');
				var text = row[opts.textField].toLowerCase();
				var find = q.toLowerCase();
				if(text.indexOf(find) != -1)
				{
					return true;
				}
			}
		});	
	});
	
	jQuery('#btn-add-new-cooperator-heritor').bind('click', openAddCooperatorHeritor);
});	

function openAddCooperatorHeritor() {
	var inheritedCapital = jQuery('#inherited-capital').numberbox('getValue');

	resetFormFieldsValues();
	initRequiredFieldsCooperator();
	jQuery('#win-cooperator-add').window({title:'Добавяне на наследник'}); 
	jQuery('#paid_in_capital_text').html('Наследен капитал (лв.)');

	jQuery('#paid_in_capital').numberbox({
		required: true,
		disabled: true,
		precision: 2,
		value: inheritedCapital
    });	

    jQuery('#current_capital').numberbox({
		value: inheritedCapital
    });	

	jQuery('#win-cooperator-add').window('open');
	jQuery('#btn-add-cooperator').show();
	jQuery('#btn-update-cooperator').hide();
}

function addCooperatorHeritor() {
	var getSelected = jQuery('#cooperators-tree').tree('getSelected');
	var selectedHeritor = jQuery('#cooperator-heritors-tree').tree('getSelected');
	if(getSelected)
	{
		if(!getSelected['attributes'].is_dead)
		{
			jQuery.messager.alert('Грешка', 'Не може да добавите наследници на жив човек.');
		}
		else if(selectedHeritor && !selectedHeritor['attributes'].is_dead)
		{
			jQuery.messager.alert('Грешка', 'Не може да добавите наследници на жив човек.', 'warning');
		}
		else
		{
			//check "current_capital" of the dead person(in su_cooperators) with sum of the "heritor_current_capital"
			TF.Rpc.Cooperators.CooperatorHeritorsTree.allowAddingHeritor(root_id)
	        .done(function (data)
	        {
				var allowAddingHeritor = data;

				if(!allowAddingHeritor) {
					jQuery.messager.alert('Грешка', 'Не може да добавите наследник, защото "Текущият капитал" на избраният кооператор вече е наследен на 100% от сегашните наследници.', 'warning');
				}else {
					jQuery('#search-cooperator-heritor-by-egn').val('');

					jQuery('#choose-heritor').combobox({
						url: 'index.php?cooperators-rpc=cooperators-heritors-combobox',
						valueField: 'id',
						textField: 'cooperator_names',
						rpcParams: [root_id, null, null],
						required: true,
						missingMessage: 'Моля изберете наследник.',
						filter: function(q, row){
							var opts = jQuery(this).combobox('options');
							var text = row[opts.textField].toLowerCase();
							var find = q.toLowerCase();
							if(text.indexOf(find) != -1)
							{
								return true;
							}
						}
					});

					initCooperatorHeritorsInheritedCapital();

					jQuery('#win-choose-heritor').window('open');	
				}
			});  

			
			
		}
	}
	else
	{
		jQuery.messager.alert('Грешка', 'Моля изберете кооператор.');
	}
}

function initCooperatorHeritorsInheritedCapital() {
	var getSelectedCooperator = jQuery('#cooperators-tree').tree('getSelected');
	var getCooperatorHeritors = jQuery('#cooperator-heritors-tree').tree('getRoots');
	var currentCapitalCooperator = parseFloat(getSelectedCooperator.attributes.current_capital);
	var inheritedCapital = jQuery('#inherited-capital');

	inheritedCapital.numberbox({
		required: true,
		missingMessage: 'Моля въведете наследен капитал.',
		min:0.01,
    	precision:2
	});

	if(getCooperatorHeritors.length > 0){
		currentCapitalCooperatorHeritors = 0;

		jQuery.each(getCooperatorHeritors, function(i, heritor) {
			var currentCapitalHeritor = heritor.attributes.current_capital;

		  	currentCapitalCooperatorHeritors += parseFloat(heritor.attributes.current_capital);
		});
		
		currentCapitalCooperator -= currentCapitalCooperatorHeritors;		
		inheritedCapital.numberbox('setValue', currentCapitalCooperator);
	}
	else{
		inheritedCapital.numberbox('setValue', currentCapitalCooperator);	
	}	

	inheritedCapital.numberbox({max: currentCapitalCooperator});
}

function deleteCooperatorHeritor() {
	var obj = new Object;

	var getSelected = jQuery('#cooperator-heritors-tree').tree('getSelected');
	if(getSelected)
	{			
		jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да изтриете този наследник?', function(r) {
			if (r) {
				var parent = jQuery('#cooperator-heritors-tree').tree('getParent', getSelected.target);
				if(parent)
				{
					obj.path = parent['attributes'].cooperator_id + '.' + getSelected['attributes'].cooperator_id;
				}
				else
				{
					obj.path = root_id + '.' + getSelected['attributes'].cooperator_id;
				}
				
				if(parent)
				{
					tmpPath = parent['attributes'].path;
					reloadTarget = parent;
				}
				else
				{
					tmpPath = root_id;
					reloadTarget = undefined;
				}

				TF.Rpc.Cooperators.CooperatorHeritorsTree.delete(obj)
		        .done(function (data) {

		        })
		        .fail(function (errorObj) {
		        	jQuery.messager.alert('Грешка',errorObj.getMessage(), 'warning');
		        });

 				initCooperatorHeritorsTree(root_id); 
			}
		});
	}
	else
	{
		jQuery.messager.alert('Грешка', 'Моля изберете наследник.');
	}
}

function showCooperatorHeritorInfo() {
	var getSelected = jQuery('#cooperator-heritors-tree').tree('getSelected');
	if(getSelected)
	{
		window.open("index.php?page=Cooperators.Home&cooperator_id=" + getSelected['attributes'].cooperator_id, '_blank');
	}
	else
	{
		jQuery.messager.alert('Грешка', 'Моля изберете наследник.');
	}
}