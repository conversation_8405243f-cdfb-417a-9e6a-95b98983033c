function initLayersTree() {
    jQuery('#crop-layers-tree').tree({
        url: 'index.php?croprotation-rpc=crop-layers-tree',
        animate: true,
        lines: true,
        onBeforeSelect: function(node) {
            var isLeaf = jQuery('#crop-layers-tree').tree('isLeaf', node.target);

            if (!isLeaf)
                return false;
        },
        onSelect: function(node) {
            var isLeaf = jQuery('#crop-layers-tree').tree('isLeaf', node.target);
            var sampleFile = jQuery('#sample-files-tree').tree('getSelected');
            if (isLeaf) {
                initCropRotationGrid(node.id, node.attributes.year);
            } else {
                initCropRotationGrid(0 , 0);
            }

            if (sampleFile) {
                jQuery('#sample-files-tree').tree('select', sampleFile.target);
            }
        },
        onLoadSuccess: function() {
            var level_1 = jQuery('#crop-layers-tree').tree('getRoots');

            if (level_1.length == 0) {
                initCropRotationGrid(0, 0);
            } else {
                var level_2 = jQuery('#crop-layers-tree').tree('getChildren', level_1[0].target);
                var level_3 = jQuery('#crop-layers-tree').tree('getChildren', level_2[0].target);

                jQuery('#crop-layers-tree').tree('select', level_3[0].target);
            }
        },
        onAfterEdit: function(node) {
            var renameObj = {
                layer_name: node.text,
                layer_id: node.id
            };
            TF.Rpc.CropRotation.CropLayersTree.rename(renameObj)
            .done(function (data) {

            })
            .fail(function (errorObj) {

            });
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    //init controls for layers tree
    jQuery('#add-ref-layer').bind('click', function() {

        jQuery('#save-crop-layer-button').show();
        jQuery('#crop-layer-report-button').hide();
        jQuery('#add-window').window({
            title: 'Създаване на референтен слой',
            iconCls: 'icon-add'
        });
        jQuery('#add-window').window('open');

        return false;
    });

    jQuery('#delete-ref-layer').bind('click', function() {

        var getSelected = jQuery('#crop-layers-tree').tree('getSelected');

        if (getSelected.id) {
            jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да премахнете този слой?', function(r) {
                if (r) {
                    TF.Rpc.CropRotation.CropLayersTree.delete(getSelected.id)
                    .done(function (data) {
                        jQuery('#crop-layers-tree').tree('reload');
                        jQuery('#layer-tables').datagrid('uncheckAll');
                        jQuery('#layer-tables').datagrid('reload');
                        jQuery('#crop-tables').datagrid('reload');
                    })
                    .fail(function (errorObj) {

                    });
                }
            });
        }

        return false;
    });

    jQuery('#upload-ref-layer-data').bind('click', function() {

        var getSelected = jQuery('#crop-layers-tree').tree('getSelected');

        if (getSelected.id) {
            initNewFileUploads(getSelected.id);
            jQuery('#win-add-file').window('open');
        } else {
            jQuery.messager.alert('Грешка', 'Моя изберете слой, към който искате да добавите данни.');
        }

        return false;
    });
}
