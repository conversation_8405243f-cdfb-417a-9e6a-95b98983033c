var editIndex = undefined;

function initCropRotationGrid(id, year) {
    initMultiEditWindow();
    var cropTable = jQuery('#crop-tables').datagrid({
        title: 'Парцели от референтен слой',
        iconCls: 'icon-edit-geometry',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 20,
        fit: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?croprotation-rpc=layer-data-grid',
        rpcParams: [{
            layer_id: id
        }],
        sortName: 'id',
        sortOrder: 'asc',
        idField: 'id',
        singleSelect: true,
        selectOnCheck: false,
        frozenColumns: [[{
                    field: 'ck',
                    checkbox: true
                }]],
        rowStyler: function(index, row) {
            if (row.has_number == 0) {
                return 'background-color:#ff0000;';
            }
        },
        columns: [[
                {
                    field: 'plot',
                    title: '<b>Поле</b>',
                    sortable: true,
                    width: 50,
                    rowspan: 2,
                    align: 'center'
                }, {
                    field: 'isak_number',
                    title: '<b>Номер</b>',
                    sortable: true,
                    width: 100,
                    rowspan: 2,
                    align: 'center'
                }, {
                    field: 'area',
                    title: '<b>Площ<br/>(ха)</b>',
                    sortable: true,
                    width: 60,
                    rowspan: 2,
                    align: 'center'
                }, {
                    title: '<b>Година I</b>',
                    colspan: 2
                }, {
                    title: '<b>Година II</b>',
                    colspan: 2
                }, {
                    title: '<b>Година III</b>',
                    colspan: 2
                }, {
                    title: '<b>Година IV</b>',
                    colspan: 2
                }, {
                    title: '<b>Година V</b>',
                    colspan: 2
                }],
            [{
                    field: 'culture1',
                    title: '<b>Култура</b>',
                    sortable: true,
                    width: 125,
                    editor: {
                        type: 'combobox',
                        options: {
                            url: 'index.php?common-rpc=culture-combobox',
                            rpcParams: [{
                                year: year,
                                without_crops: true,
                                crop_type: 'obrabotvaemazemya'
                            }],
                            valueField: 'id',
                            textField: 'name',
                            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
                        }
                    }
                }, {
                    field: 'yield1',
                    title: '<b>Реализиран<br/>добив<br/>(кг/дка)</b>',
                    sortable: true,
                    width: 50,
                    align: 'center',
                    editor: {
                        type: 'numberbox',
                        options: {precision: 2}
                    }
                }, {
                    field: 'culture2',
                    title: '<b>Култура</b>',
                    sortable: true,
                    width: 125,
                    editor: {
                        type: 'combobox',
                        options: {
                            url: 'index.php?common-rpc=culture-combobox',
                            rpcParams: [{
                                year: (year + 1),
                                without_crops: true,
                                crop_type: 'obrabotvaemazemya'
                            }],
                            valueField: 'id',
                            textField: 'name',
                            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
                        }
                    }
                }, {
                    field: 'yield2',
                    title: '<b>Очакван<br/>добив<br/>(кг/дка)</b>',
                    sortable: true,
                    width: 50,
                    align: 'center',
                    editor: {
                        type: 'numberbox',
                        options: {precision: 2}
                    }
                }, {
                    field: 'culture3',
                    title: '<b>Култура</b>',
                    sortable: true,
                    width: 125,
                    editor: {
                        type: 'combobox',
                        options: {
                            url: 'index.php?common-rpc=culture-combobox',
                            rpcParams: [{
                                without_crops: true,
                                year: 6
                            }],
                            valueField: 'id',
                            textField: 'name',
                            onLoadSuccess: function() {
                                var grid = jQuery('#crop-tables');
                                var selectedRow = grid.datagrid('getSelected');
                                var rowIndex = grid.datagrid('getRowIndex', selectedRow);

                                var culture4Editor = grid.datagrid('getEditor', {index: rowIndex, field: 'culture4'});
                                culture4Editor.target.combobox('loadData', jQuery(this).combobox('getData'));
                                var culture5Editor = grid.datagrid('getEditor', {index: rowIndex, field: 'culture5'});
                                culture5Editor.target.combobox('loadData', jQuery(this).combobox('getData'));
                            },
                            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
                        }
                    }
                }, {
                    field: 'yield3',
                    title: '<b>Очакван<br/>добив<br/>(кг/дка)</b>',
                    sortable: true,
                    width: 50,
                    align: 'center',
                    editor: {
                        type: 'numberbox',
                        options: {precision: 2}
                    }
                }, {
                    field: 'culture4',
                    title: '<b>Култура</b>',
                    sortable: true,
                    width: 125,
                    editor: {
                        type: 'combobox',
                        options: {
                            valueField: 'id',
                            textField: 'name',
                        }
                    }
                }, {
                    field: 'yield4',
                    title: '<b>Очакван<br/>добив<br/>(кг/дка)</b>',
                    sortable: true,
                    width: 50,
                    align: 'center',
                    editor: {
                        type: 'numberbox',
                        options: {precision: 2}
                    }
                }, {
                    field: 'culture5',
                    title: '<b>Култура</b>',
                    sortable: true,
                    width: 125,
                    editor: {
                        type: 'combobox',
                        options: {
                            valueField: 'id',
                            textField: 'name',
                        }
                    }
                }, {
                    field: 'yield5',
                    title: '<b>Очакван<br/>добив<br/>(кг/дка)</b>',
                    sortable: true,
                    width: 50,
                    align: 'center',
                    editor: {
                        type: 'numberbox',
                        options: {precision: 2}
                    }
                }]],
        pagination: true,
        rownumbers: false,
        toolbar: [{
                id: 'btnadd',
                text: 'Добавяне',
                iconCls: 'icon-add',
                handler: function() {
                    initCropComboboxData();
                    unsetOldValues();
                    jQuery('#win-add-crop-data').window('open');
                }
            }, {
                id: 'btndel',
                text: 'Изтриване',
                iconCls: 'icon-delete',
                handler: function() {
                    var getChecked = jQuery('#crop-tables').datagrid('getChecked');
                    if (getChecked[0]) {
                        jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да изтриете тези записи?', function(r) {
                            if (r) {
                                TF.Rpc.CropRotation.LayerDataGrid.delete(getChecked)
                                .done(function (data) {
                                    jQuery('#crop-tables').datagrid('uncheckAll');
                                    jQuery('#crop-tables').datagrid('reload');
                                })
                                .fail(function (errorObj) {

                                });
                            }
                        });
                    } else {
                        jQuery.messager.alert('Грешка', 'Моля изберете данни, които искате да премахнете.');
                    }
                }
            }, '-',{
                id: 'btnmultiedit',
                text: 'Мултиредакция',
                iconCls: 'icon-multi-edit',
                handler: function() {
                    var getChecked = jQuery('#crop-tables').datagrid('getChecked');
                    if (getChecked.length) {
                       /*//jQuery('#multi-edit-year').combobox('reset');
                       jQuery('#multi-edit-old-crop > input').combobox('loadData', []);
                       jQuery('#multi-edit-new-crop > input').combobox('reset');
                       jQuery('#multi-edit-dobiv > input').val('');*/

                       jQuery('#multiedit-window').window('open');
                    } else {
                        jQuery.messager.alert('Грешка', 'Моля изберете парцел/и!');
                    }
                }
            }, '-', {
                id: 'btnadd',
                text: 'Проверка за припокриване',
                iconCls: 'icon-intersection',
                handler: function() {
                    jQuery('#save-crop-layer-button').hide();
                    jQuery('#crop-layer-report-button').show();
                    reportType = 1;
                    jQuery('#add-window').window({
                        title: 'Избор на стопанство и година',
                        iconCls: 'icon-intersection'
                    });
                    jQuery('#add-window').window('open');
                }
            }, {
                id: 'btnadd',
                text: 'Проверка на култури',
                iconCls: 'icon-culture',
                handler: function() {
                    jQuery('#save-crop-layer-button').hide();
                    jQuery('#crop-layer-report-button').show();
                    reportType = 2;
                    jQuery('#add-window').window({
                        title: 'Избор на стопанство и година',
                        iconCls: 'icon-intersection'
                    });
                    jQuery('#add-window').window('open');
                }
            }, {
                id: 'btnshowsoilsamples',
                text: 'Aнализ на почвите',
                iconCls: 'icon-planting',
                handler: function() {
                    var layerData = jQuery('#crop-layers-tree').tree('getSelected');

                    if (layerData.id) {
                        initSoilSamplesGrid(layerData.id);
                        jQuery('#win-soil-samples').window('open');
                    } else {
                        jQuery.messager.alert('Грешка', 'Моля изберете референтен слой!');
                    }
                }
            }, {
                id: 'btncreatesoilsamplenorm',
                text: 'План за балансирано торене',
                iconCls: 'icon-plan',
                handler: function() {
                    var layerData = jQuery('#crop-layers-tree').tree('getSelected');

                    if (!isTrial) {
                        if (layerData.id) {
                            layerYearData = jQuery('#crop-layers-tree').tree('getParent', layerData.target);

                            jQuery('#choose-norm-year > input').combobox({
                                url: 'index.php?common-rpc=sample-norm-year-combobox',
                                rpcParams: [{
                                    selected: true,
                                    year: layerYearData.id
                                }],
                                textField: 'title',
                                valueField: 'id',
                                loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                                loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
                            });

                            jQuery('#win-choose-norm-type').window('open');
                        } else {
                            jQuery.messager.alert('Грешка', 'Не е намерен референтен слой, за който да бъдат изготвени норми.');
                        } // end data is found IF
                    } //end trial IF
                }
            }, {
                id: 'btnexportcroprotationdata',
                text: 'Експорт(CSV)',
                iconCls: 'icon-csv',
                handler: function() {
                    var layer_id = id;
                    var exportObj = {
                        layer_id: layer_id
                    };
                    TF.Rpc.CropRotation.CropRotationExports.exportCropRotationData(exportObj)
                    .done(function (data) {
                        createDownloadVariables();
                        winDownloadDiaryReport.window('open');
                        var path = data.file_path;
                        _pathFile = path;
                        _fileName = data.file_name;
                        downloadFileDiaryReport.attr("href", path);
                    })
                    .fail(function (errorObj) {

                    });
                }
            }],
        onBeforeLoad: function() {
            jQuery('#crop-tables').datagrid('clearChecked');

            if (isTrial) {
                jQuery('#btncreatesoilsamplenorm').linkbutton('disable');
            }
        },
        onLoadSuccess: function(){
            editIndex = undefined;
        },
        onClickRow: function(rowIndex, rowData) {
            if (editIndex == undefined) {
                editIndex = rowIndex;
                jQuery('#crop-tables').datagrid('beginEdit', editIndex);
            } else {
                //check if the same row is not requested for edit
                if (editIndex != rowIndex) {
                    jQuery('#crop-tables').datagrid('endEdit', editIndex);
                } else {
                    jQuery('#crop-tables').datagrid('endEdit', rowIndex);
                }
                editIndex = undefined;
            }
        },
        onAfterEdit: function(rowIndex, rowData, changes) {
            var obj = new Object();
            obj.data = changes;
            obj.id = rowData.id;

            TF.Rpc.CropRotation.LayerDataGrid.saveChanges(obj)
            .done(function (data) {
                jQuery('#crop-tables').datagrid('unselectAll');
                jQuery('#crop-tables').datagrid('uncheckAll');
                jQuery('#crop-tables').datagrid('reload');
                editIndex = undefined;
            })
            .fail(function (errorObj) {
                RpcErrorHandler.show(errorObj);
            });
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}
