function initSoilSampleNormGrid(layer_id, chosen_year) {
    jQuery('#soil-norms-tables').datagrid({
        iconCls: 'icon-edit-geometry',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 20,
        fit: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?croprotation-rpc=soil-samples-norm-grid',
        rpcParams: [{
            cl_id: layer_id,
            year: chosen_year
        }],
        sortName: 'isak_number',
        sortOrder: 'asc',
        idField: 'id',
        singleSelect: false,
        border: false,
        frozenColumns: [[{
                    field: 'ck',
                    checkbox: false
                }]],
        rowStyler: function(index, row) {
            if (!row.azot_content)
                row.azot_content = '-';
            if (!row.fosfor_content)
                row.fosfor_content = '-';
            if (!row.kalii_content)
                row.kalii_content = '-';

            if (!row.azot_norm)
                row.azot_norm = '-';
            if (!row.fosfor_norm)
                row.fosfor_norm = '-';
            if (!row.kalii_norm)
                row.kalii_norm = '-';

            if (row.has_number == 0) {
                return 'background-color:#ff0000;';
            }

        },
        columns: [[
                {
                    title: '<b>Информация за проба</b>',
                    colspan: 3,
                    align: 'center'
                }, {
                    title: '<b>Запасеност на почвата кг/дка</b>',
                    colspan: 3,
                    align: 'center'
                }, {
                    title: '<b>Норма на торене кг/дка</b>',
                    colspan: 3,
                    align: 'center'
                }], [
                {
                    field: 'plot',
                    title: '<b>Поле</b>',
                    sortable: true,
                    align: 'center'
                }, {
                    field: 'sample_num',
                    title: '<b>Номер</b>',
                    sortable: true,
                    align: 'center'
                }, {
                    field: 'isak_number',
                    title: '<b>ИСАК</b>',
                    sortable: true,
                    width: 100,
                    align: 'center'
                }, {
                    field: 'azot_access',
                    title: '<b>Азот<br/>(NH<sub>4</sub>-N + NO<sub>3</sub>-N)</b>',
                    sortable: false,
                    align: 'center'
                }, {
                    field: 'fosfor_access',
                    title: '<b>Фосфор <br/>(P<sub>2</sub>O<sub>5</sub>)</b>',
                    sortable: false,
                    align: 'center'
                }, {
                    field: 'kalii_access',
                    title: '<b>Калий <br/>(K<sub>2</sub>O)</b>',
                    sortable: false,
                    align: 'center'
                }, {
                    field: 'azot_norm',
                    title: '<b>Азот<br/>(NH<sub>4</sub>-N + NO<sub>3</sub>-N)</b>',
                    sortable: false,
                    align: 'center'
                }, {
                    field: 'fosfor_norm',
                    title: '<b>Фосфор <br/>(P<sub>2</sub>O<sub>5</sub>)</b>',
                    sortable: false,
                    align: 'center'
                }, {
                    field: 'kalii_norm',
                    title: '<b>Калий <br/>(K<sub>2</sub>O)</b>',
                    sortable: false,
                    align: 'center'
                }]],
        pagination: true,
        rownumbers: false,
        toolbar: [{
                id: 'btnfiltersoilnorm',
                text: 'Филтър',
                iconCls: 'icon-filter',
                handler: function() {
                    var layerData = jQuery('#crop-layers-tree').tree('getSelected');

                    if (layerData.id) {
                        initSoilNormFilterPanel(layerData.id)
                        jQuery('#win-soil-norm-filter').window('open');
                    }
                }
            }, {
                id: 'btnclearsoilnormfilter',
                text: 'Покажи всички',
                iconCls: 'icon-clear-filter',
                handler: function() {
                    clearSoilNormFilter();
                }
            }, {
                id: 'btnshowavgsamplenorm',
                text: 'Усреднена норма',
                iconCls: 'icon-reports',
                handler: function() {
                    initAVGSoilSampleNormGrid(layer_id, chosen_year);
                    jQuery('#win-avg-soil-norms').window('open');
                }
            }, {
                id: 'btnexportsoilnorms',
                text: 'ДФЗ експорт',
                iconCls: 'icon-pdf',
                handler: function() {
                    var bnf = jQuery('#choose-norm-benefecient > input').val();
                    var urn = jQuery('#choose-norm-urn > input').val();

                    var exportObj = {
                        cl_id: layer_id,
                        year: chosen_year,
                        bnf: bnf,
                        urn: urn
                    };

                    TF.Rpc.CropRotation.CropRotationExports.exportSampleNorm(exportObj)
                    .done(function (data) {
                        jQuery('body').append('<iframe id="printf" name="printf"></iframe>');
                        var newWin = window.frames['printf'];
                        newWin.document.write('<body onload=window.print()>'+data+'</body>');
                        newWin.document.close();
                        setTimeout(function () {
                            jQuery('#printf').remove();
                        }, 1000);
                    })
                    .fail(function (errorObj) {

                    });
                }
            }],
        onBeforeLoad: function() {
            jQuery('#soil-norms-tables').datagrid('clearChecked');
        },
        onLoadSuccess: function() {

        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initSoilNormFilterPanel(cl_id)
{
    jQuery('#snf-isak > input').combobox({
        url: 'index.php?croprotation-rpc=crop-data-isak-combobox',
        rpcParams: [{
            cl_id: cl_id
        }],
        valueField: 'id',
        textField: 'name',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function displaySampleNormGrid(layer_id, cl_year, chosen_year)
{
    var yieldColumn = chosen_year - cl_year + 1;
    var correct = true;

    var cropLayerData = jQuery('#crop-tables').datagrid('getData')['rows'];
    //console.info(cropLayerData);

    for (var i = 0; i < cropLayerData.length; i++) {
        if (cropLayerData[i]['yield' + yieldColumn] <= 0) {
            correct = false;
            break;
        }
    }

    if (correct == false) {
        jQuery.messager.alert('Грешка', 'Някои записи за избраната година са без попълена информация за очакван добив. Тази информация е нужна за изчисляване на нормите. Моля редактирайте невалидните записи.');
    } else {
        initSoilSampleNormGrid(layer_id, chosen_year);

        jQuery('#win-choose-norm-type').window('close');
        jQuery('#win-soil-norms').window('open');
    }
}

function soilNormFilter()
{
    jQuery('#soil-norms-tables').datagrid({
        queryParams: {
            isak: jQuery('#snf-isak > input').combobox('getText'),
            plot: jQuery('#snf-plot > input').val()
        }
    });

    jQuery('#win-soil-norm-filter').window('close');
}

function clearSoilNormFilter() {
    jQuery('#soil-norms-tables').datagrid({
        queryParams: {}
    });

    jQuery('#snf-isak > input').val('');
    jQuery('#snf-plot > input').val('');
}
