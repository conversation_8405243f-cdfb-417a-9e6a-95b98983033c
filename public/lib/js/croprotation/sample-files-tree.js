function initSampleFilesTree()
{
    jQuery('#sample-files-tree').tree({
        url: 'index.php?croprotation-rpc=sample-files-tree',
        animate: true,
        lines: true,
        onSelect: function(node) {
            var isLeaf = jQuery('#crop-layers-tree').tree('isLeaf', node.target);
            var layerData = jQuery('#crop-layers-tree').tree('getSelected');

            if (isLeaf && layerData) {
                initSoilSampleFileContentGrid(node.id, layerData.id)
            }
        },
        onLoadSuccess: function() {
            var roots = jQuery('#sample-files-tree').tree('getRoots');

            if(roots.total == 0) {
                initSoilSampleFileContentGrid(0, 0)
            } else {
                jQuery('#sample-files-tree').tree('select', roots[0].target);
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#upload-sample-file').bind('click', function() {

        //No Rights
        if(!hasSubsidyRightsRW) {
            EasyUIRPCLoaders.messagerNoRightsRW();
            return false;
        }

        if(jQuery('#crop-layers-tree').tree('getSelected') === null)
        {
            jQuery.messager.alert('Внимание', 'За да добавите файл за почвени проби трябва да имате поне един референтен слой!');
            return false;
        }

        initSoilSampleUpload();
        jQuery('#win-soil-sample-uploader').window('open');
        return false;
    });

    jQuery('#delete-sample-file').bind('click', function() {

        var getSelected = jQuery('#sample-files-tree').tree('getSelected');

        if (getSelected.id) {
            jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да премахнете този файл?', function(r) {
                if (r) {
                    TF.Rpc.CropRotation.SoilSampleFilesTree.deleteSoilSampleFile(getSelected.id)
                    .done(function (data) {
                        jQuery('#sample-files-tree').tree('reload');
                        jQuery('#soil-sample-contents-tables').datagrid('reload')
                    })
                    .fail(function (errorObj) {

                    });
                }
            });
        } else {
            jQuery.messager.alert('Грешка', 'Не е избран файл!');
        }
        return false;
    });

    jQuery('#apply-sample-file').bind('click', function() {

        var fileData = jQuery('#sample-files-tree').tree('getSelected');
        var fileRows = jQuery('#soil-sample-contents-tables').datagrid('getData');

        if (fileData)
        {
            if (fileRows['rows'].length != 0)
            {
                initApplySoilSampleFilePanel();
                jQuery('#win-apply-ss-file').window('open');
            }
            else
            {
                jQuery.messager.alert('Грешка', 'Невалидна структура на файла.');
            }
        }
        else
        {
            jQuery.messager.alert('Грешка', 'Не е избран файл, от който да бъдат приложени проби!');
        }

        return false;
    });
}

function initApplySoilSampleFilePanel() {

    jQuery('#apply-ssf-farming > input').combobox({
        url: 'index.php?croprotation-rpc=apply-ssf-combobox',
        rpcParams: [{
            request: 'farmings',
            selected: true
        }],
        valueField: 'id',
        textField: 'name',
        onLoadSuccess: function() {
            var value = jQuery('#apply-ssf-farming > input').combobox('getValue');

            initASSFYearsCombobox(value);
        },
        onSelect: function(record) {
            initASSFYearsCombobox(record.id);
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

}

function initASSFYearsCombobox(farming) {
    jQuery('#apply-ssf-year > input').combobox({
        url: 'index.php?croprotation-rpc=apply-ssf-combobox',
        rpcParams: [{
            request: 'years',
            farming: farming,
            selected: true
        }],
        valueField: 'id',
        textField: 'name',
        onLoadSuccess: function() {
            var value = jQuery('#apply-ssf-year > input').combobox('getValue');

            initASSFLayersCombobox(farming, value);
        },
        onSelect: function(record) {
            initASSFLayersCombobox(farming, record.id);
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initASSFLayersCombobox(farming, year) {
    jQuery('#apply-ssf-layer > input').combobox({
        url: 'index.php?croprotation-rpc=apply-ssf-combobox',
        rpcParams: [{
            request: 'layers',
            year: year,
            farming: farming,
            selected: true
        }],
        valueField: 'id',
        textField: 'name',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function validateSSFileApply() {
    var fileData = jQuery('#sample-files-tree').tree('getSelected');
    var cropLayerID = jQuery('#apply-ssf-layer > input').combobox('getValue');

    jQuery.messager.confirm('Потвърждение', 'Записите, оцветени в червен цвят, са с ненамерен ИСАК номер и няма да бъдат приложени към почвените проби. Сигурни ли сте, че искате да продължите?', function(r) {
        if (r) {
            var obj = new Object();
            obj.file_id = fileData.id;
            obj.crop_layer_id = cropLayerID;

            TF.Rpc.CropRotation.SoilSampleFilesTree.applyFile(obj)
            .done(function (data) {
                jQuery('#win-apply-ss-file').window('close');
            })
            .fail(function (errorObj) {

            });
        }
    });

    return false;
}
