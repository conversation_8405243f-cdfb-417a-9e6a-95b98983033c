var date = new Date();
var todayDate = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();
var cropLayerDataID = 0;

//variables for soil sample export
var exportIsak = '';
var exportDateFrom = '';
var exportDateTo = '';
var crop_layer_data_id;
//cld_id - crop data id
function initSoilSamplesGrid(layer_id) {
    crop_layer_data_id = layer_id;
    jQuery('#tables-soil-samples').datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 20,
        fit: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?croprotation-rpc=soil-samples-datagrid',
        rpcParams: [{
            cl_id: layer_id
        }],
        sortName: 'ss.id',
        sortOrder: 'asc',
        idField: 'ss.id',
        singleSelect: true,
        border: false,
        frozenColumns: [[{
                    field: 'ck',
                    checkbox: true
                }]],
        columns: [[
                {
                    title: '<b>Информация за проба</b>',
                    colspan: 3,
                    align: 'center'
                }, {
                    title: '<b>Съдържание на достъпни форми мг/кг</b>',
                    colspan: 3,
                    align: 'center'
                }, {
                    field: 'ph',
                    title: '<b>pH</b>',
                    sortable: true,
                    rowspan: 2,
                    align: 'center'
                }, {
                    title: '<b>Запасеност на почвата кг/дка</b>',
                    colspan: 3,
                    align: 'center'
                }, {
                    field: 'meh_sastav',
                    title: '<b>Механичен<br/>състав</b>',
                    sortable: true,
                    rowspan: 2,
                    align: 'center'
                }, {
                    field: 'sud_humus',
                    title: '<b>Хумусно<br/>съдържание</b>',
                    sortable: true,
                    rowspan: 2,
                    align: 'center'
                }], [
                {
                    field: 'sample_num',
                    title: '<b>Номер</b>',
                    sortable: true,
                    align: 'center'
                }, {
                    field: 'sample_date',
                    title: '<b>Дата</b>',
                    sortable: true,
                    align: 'center'
                }, {
                    field: 'isak_number',
                    title: '<b>ИСАК</b>',
                    sortable: true,
                    width: 110,
                    align: 'center'
                }, {
                    field: 'azot_mg',
                    title: '<b>Азот<br/>(NH<sub>4</sub>-N + NO<sub>3</sub>-N)</b>',
                    sortable: true,
                    width: 90,
                    align: 'center'
                }, {
                    field: 'fosfor_mg',
                    title: '<b>Фосфор <br/>(P<sub>2</sub>O<sub>5</sub>)</b>',
                    sortable: true,
                    width: 90,
                    align: 'center'
                }, {
                    field: 'kalii_mg',
                    title: '<b>Калий <br/>(K<sub>2</sub>O)</b>',
                    sortable: true,
                    width: 90,
                    align: 'center'
                }, {
                    field: 'azot',
                    title: '<b>Азот<br/>(NH<sub>4</sub>-N + NO<sub>3</sub>-N)</b>',
                    sortable: true,
                    align: 'center'
                }, {
                    field: 'fosfor',
                    title: '<b>Фосфор<br/>(P<sub>2</sub>O<sub>5</sub>)</b>',
                    sortable: true,
                    align: 'center'
                }, {
                    field: 'kalii',
                    title: '<b>Калий<br/>(K<sub>2</sub>O)</b>',
                    sortable: true,
                    align: 'center'
                }]],
        pagination: true,
        rownumbers: false,
        toolbar: [{
                id: 'btnaddsoilsample',
                text: 'Добавяне',
                iconCls: 'icon-add',
                handler: function() {
                    initAddSoilSampleFields();
                    resetAddEditSoilSampleData();
                    jQuery('#tables-soil-samples').datagrid('clearChecked');
                    jQuery('#win-add-soil-sample').window('open');
                }
            }, {
                id: 'btneditsoilsample',
                text: 'Редактирне',
                iconCls: 'icon-edit',
                handler: function() {
                    initAddSoilSampleFields();
                    var getChecked = jQuery('#tables-soil-samples').datagrid('getChecked');
                    if (getChecked[0]) {
                        TF.Rpc.CropRotation.SoilSamplesGrid.markForEdit(getChecked[0]['id'])
                        .done(function (data) {
                            initEditFields();
                            resetAddEditSoilSampleData();
                            setAddEditSoilSampleDataFields(data);
                            jQuery('#win-add-soil-sample').window('open');
                        })
                        .fail(function (errorObj) {

                        });
                    } else {
                        jQuery.messager.alert('Грешка', 'Моля изберете запис, който искате да редактирате.')
                    }
                }
            }, {
                id: 'btndeletesoilsample',
                text: 'Изтриване',
                iconCls: 'icon-delete',
                handler: function() {
                    var getChecked = jQuery('#tables-soil-samples').datagrid('getChecked');
                    if (getChecked[0]) {
                        jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да изтриете този запис?', function(r) {
                            if (r) {
                                TF.Rpc.CropRotation.SoilSamplesGrid.delete(getChecked[0]['id'])
                                .done(function (data) {
                                    jQuery('#tables-soil-samples').datagrid('reload');
                                })
                                .fail(function (errorObj) {

                                });
                            }
                        })
                    } else {
                        jQuery.messager.alert('Грешка', 'Моля изберете запис, който искате да премахнете.');
                    }
                }
            }, {
                id: 'btnsoilsamplefilter',
                text: 'Филтър',
                iconCls: 'icon-filter',
                handler: function() {
                    var layerData = jQuery('#crop-layers-tree').tree('getSelected');

                    if (layerData.id) {
                        initSoilSampleFilterPanel(layerData.id);
                        jQuery('#win-soil-sample-filter').window('open');
                    }
                }
            }, {
                id: 'btnsoilsamplefilterclear',
                text: 'Покажи всички',
                iconCls: 'icon-clear-filter',
                handler: function() {
                    clearSoilSampleFilter();
                }
            }, {
                id: 'btnexportsoilsample',
                text: 'ДФЗ експорт',
                iconCls: 'icon-pdf',
                handler: function() {
                    jQuery('#win-soil-sample-export').window('open');
                }
            }],
        onBeforeLoad: function() {
            jQuery('#tables-soil-samples').datagrid('clearChecked');
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initSoilSampleFilterPanel(cl_id) {
    jQuery('#ssf-isak > input').combobox({
        url: 'index.php?croprotation-rpc=crop-data-isak-combobox',
        rpcParams: [{
            cl_id: cl_id
        }],
        valueField: 'id',
        textField: 'name',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#ssf-date-from > input, #ssf-date-to > input').datebox({});
}

function initAddSoilSampleFields() {
    var layerData = jQuery('#crop-layers-tree').tree('getSelected');

    jQuery('#soil-sample-date > input').datebox({
        value: todayDate,
    });
    jQuery('#soil-sample-azot > input').numberbox({
        precision: 2
    });
    jQuery('#soil-sample-fosfor > input').numberbox({
        precision: 2
    });
    jQuery('#soil-sample-kalii > input').numberbox({
        precision: 2
    });
    jQuery('#soil-sample-ph > input').numberbox({
        precision: 2
    });
    jQuery('#soil-sample-meh-sastav > input').combobox({
        url: 'index.php?common-rpc=soil-types-combobox',
        rpcParams: [{
            selected: true
        }],
        valueField: 'id',
        textField: 'name',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    jQuery('#soil-sample-humus > input').combobox({
        url: 'index.php?common-rpc=soil-humus-combobox',
        rpcParams: [{
            selected: true
        }],
        valueField: 'id',
        textField: 'name',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    jQuery('#soil-sample-isak > input').combobox({
        url: 'index.php?croprotation-rpc=crop-data-isak-combobox',
        rpcParams: [{
            cl_id: layerData.id
        }],
        valueField: 'id',
        textField: 'name',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initEditFields() {
    var layerData = jQuery('#crop-layers-tree').tree('getSelected');

    jQuery('#soil-sample-date > input').datebox();
    jQuery('#soil-sample-azot > input').numberbox({
        precision: 2
    });
    jQuery('#soil-sample-fosfor > input').numberbox({
        precision: 2
    });
    jQuery('#soil-sample-kalii > input').numberbox({
        precision: 2
    });
    jQuery('#soil-sample-ph > input').numberbox({
        precision: 2
    });
    jQuery('#soil-sample-meh-sastav > input').combobox({
        url: 'index.php?common-rpc=soil-types-combobox',
        valueField: 'id',
        textField: 'name',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    jQuery('#soil-sample-humus > input').combobox({
        url: 'index.php?common-rpc=soil-humus-combobox',
        valueField: 'id',
        textField: 'name',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    jQuery('#soil-sample-isak > input').combobox({
        url: 'index.php?croprotation-rpc=crop-data-isak-combobox',
        rpcParams: [{
            cl_id: layerData.id
        }],
        valueField: 'id',
        textField: 'name',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function addEditSoilSample() {
    var selectedSample = jQuery('#tables-soil-samples').datagrid('getSelected');
    var cld_id = jQuery('#crop-layers-tree').tree('getSelected');
    var crop_layer_data_found = false;

    if (isInt(cld_id.id)) {
        crop_layer_data_found = true;
    }

    if (jQuery('#soil-sample-date > input').datebox('getValue') != ''
            && jQuery('#soil-sample-num > input').val()
            && jQuery('#soil-sample-azot > input').numberbox('getValue') != ''
            && jQuery('#soil-sample-fosfor > input').numberbox('getValue') != ''
            && jQuery('#soil-sample-kalii > input').numberbox('getValue') != ''
            && jQuery('#soil-sample-ph > input').numberbox('getValue') != ''
            && jQuery('#soil-sample-meh-sastav > input').combobox('getValue')
            && jQuery('#soil-sample-humus > input').combobox('getValue')
            && crop_layer_data_found)
    {
        var requestObj = getAddEditSoilSampleData();
        if (selectedSample) {
            requestObj.sample_id = selectedSample.id;
        };
        requestObj.crop_layer_id = cld_id.id;

        TF.Rpc.CropRotation.SoilSamplesGrid.add(requestObj)
        .done(function (data) {
            jQuery('#win-add-soil-sample').window('close');
            jQuery('#tables-soil-samples').datagrid('reload');
        })
        .fail(function (errorObj) {
            if (errorObj.is(TF.Rpc.ExceptionsList.EXISTING_SOIL_SAMPLE_NUMBER)) {
                jQuery.messager.alert('Грешка',TF.Rpc.ExceptionsList.EXISTING_SOIL_SAMPLE_NUMBER.message,'warning');
            }
            RpcErrorHandler.show(errorObj);
        });
    } else {
        jQuery.messager.alert('Грешка', 'Моля попълнете всички полета');
    }
}

function soilSampleFilter() {
    jQuery('#win-soil-sample-filter').window('close');

    jQuery('#tables-soil-samples').datagrid({
        rpcParams: [{
            cl_id: crop_layer_data_id,
            isak: jQuery('#ssf-isak > input').combobox('getText'),
            date_from: jQuery('#ssf-date-from > input').datebox('getValue'),
            date_to: jQuery('#ssf-date-to > input').datebox('getValue')
        }]
    });

    exportIsak = jQuery('#ssf-isak > input').combobox('getText');
    exportDateFrom = jQuery('#ssf-date-from > input').datebox('getValue');
    exportDateTo = jQuery('#ssf-date-to > input').datebox('getValue');
}

function clearSoilSampleFilter() {
    jQuery('#tables-soil-samples').datagrid({
        rpcParams: [{
            cl_id: crop_layer_data_id
        }]
    });

    exportIsak = '';
    exportDateFrom = '';
    exportDateTo = '';
}

function isInt(value) {
    var er = /^[0-9]+$/;
    return (er.test(value)) ? true : false;
}

function getAddEditSoilSampleData() {
    var returnObj = {
        sample_date: jQuery("#soil-sample-date > input").datebox('getValue'),
        sample_num: jQuery("#soil-sample-num > input").val(),
        isak_number: jQuery("#soil-sample-isak > input").combobox('getValue'),
        azot: jQuery("#soil-sample-azot > input").numberbox('getValue'),
        fosfor: jQuery("#soil-sample-fosfor > input").numberbox('getValue'),
        kalii: jQuery("#soil-sample-kalii > input").numberbox('getValue'),
        ph: jQuery("#soil-sample-ph > input").numberbox('getValue'),
        meh_sastav: jQuery("#soil-sample-meh-sastav > input").combobox('getValue'),
        sud_humus: jQuery("#soil-sample-humus > input").combobox('getValue')
    };
    return returnObj;
}

function resetAddEditSoilSampleData() {
    jQuery("#soil-sample-date > input").datebox('reset');
    jQuery("#soil-sample-num > input").val('');
    jQuery("#soil-sample-isak > input").combobox('setValue');
    jQuery("#soil-sample-azot > input").numberbox('setValue');
    jQuery("#soil-sample-fosfor > input").numberbox('setValue');
    jQuery("#soil-sample-kalii > input").numberbox('setValue');
    jQuery("#soil-sample-ph > input").numberbox('setValue');
    jQuery("#soil-sample-meh-sastav > input").combobox('setValue');
    jQuery("#soil-sample-humus > input").combobox('setValue');
}

function setAddEditSoilSampleDataFields(data) {
    jQuery("#soil-sample-date > input").datebox('setValue',data.sample_date);
    jQuery("#soil-sample-num > input").val(data.sample_num);
    jQuery("#soil-sample-isak > input").combobox('setValue',data.crop_layer_data_id);
    jQuery("#soil-sample-azot > input").numberbox('setValue',data.azot);
    jQuery("#soil-sample-fosfor > input").numberbox('setValue',data.fosfor);
    jQuery("#soil-sample-kalii > input").numberbox('setValue',data.kalii);
    jQuery("#soil-sample-ph > input").numberbox('setValue',data.ph);
    jQuery("#soil-sample-meh-sastav > input").combobox('setValue',data.meh_sastav);
    jQuery("#soil-sample-humus > input").combobox('setValue',data.sud_humus);
}
