function initAVGSoilSampleNormGrid(layer_id, chosen_year) {
    jQuery('#soil-avg-norms-tables').datagrid({
        iconCls: 'icon-edit-geometry',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 20,
        fit: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?croprotation-rpc=avg-soil-samples-norm-grid',
        rpcParams: [{
            cl_id: layer_id,
            year:chosen_year
        }],
        sortName: '',
        sortOrder: '',
        idField: '',
        singleSelect: false,
        border: false,
        rowStyler: function(index, row) {
            if (row.has_number == 0) {
                return 'background-color:#ff0000;';
            }
        },
        frozenColumns: [[{
                    field: 'ck',
                    checkbox: false
                }]],
        columns: [[
                {
                    field: 'isak_number',
                    title: '<b>ИСАК номер</b>',
                    sortable: false,
                    width: 220,
                    align: 'center',
                    rowspan: 2
                }, {
                    field: 'sample_count',
                    title: '<b>Брой<br/>проби</b>',
                    sortable: false,
                    width: 100,
                    align: 'center',
                    rowspan: 2
                }, {
                    title: '<b>Усреднени стойности</b>',
                    colspan: 3
                }],
            [{
                    field: 'azot_norm',
                    title: '<b>Норма на<br/>торене с азот<br/>(кг/дка)</b>',
                    sortable: false,
                    width: 180,
                    align: 'center'
                }, {
                    field: 'fosfor_norm',
                    title: '<b>Норма на<br/>торене с фосфор<br/>(кг/дка)</b>',
                    sortable: false,
                    width: 200,
                    align: 'center'
                }, {
                    field: 'kalii_norm',
                    title: '<b>Норма на<br/>торене с калий<br/>(кг/дка)</b>',
                    sortable: false,
                    width: 180,
                    align: 'center'
                }]],
        toolbar: [{
                id: 'btnexportsoilnorms',
                text: 'Експорт',
                iconCls: 'icon-pdf',
                handler: function() {

                    var exportObj = {
                        cl_id: layer_id,
                        year: chosen_year
                    };

                    TF.Rpc.CropRotation.CropRotationExports.exportAVGSampleNorm(exportObj)
                    .done(function (data) {
                        createDownloadVariables();
                        winDownloadDiaryReport.window('open');
                        var path = data.file_path;
                        _pathFile = path;
                        _fileName = data.file_name;
                        downloadFileDiaryReport.attr("href", path);
                    })
                    .fail(function (errorObj) {

                    });
                }
            }],
        pagination: false,
        rownumbers: false,
        onBeforeLoad: function() {
            jQuery('#soil-norms-tables').datagrid('clearChecked');
        },
        onLoadSuccess: function() {

        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}
