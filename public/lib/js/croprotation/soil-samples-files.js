function initSoilSampleUploadsGrid(crop_layer_id) {
	jQuery('#soil-sample-uploads-tables').datagrid({
        title:'Файлов архив за почвени проби',
        iconCls:'icon-files',
        nowrap: true,
        singleSelect: true,
        fit: true,
        fitColumns: true,
        showFooter: true,
        url:'index.php?croprotation-rpc=soil-samples-files-grid',
        rpcParams: [{
            cl_id: crop_layer_id
        }],
        sortName: 'id',
        sortOrder: 'desc',
        idField:'id',
        frozenColumns:[[
        {
            field:'ck',
            checkbox:true
        },
        ]],
        columns:[[
        {
            field: 'filename',
            title: '<b>Име на файл</b>',
            sortable: true,
            width: 250
        },{
            field: 'uploaded',
            title: '<b>Дата на добавяне</b>',
            sortable: true,
            width: 250
        },{
            field: 'status',
            title: '<b>Статус</b>',
            sortable: true,
            width: 150
        }
        ]],
        onLoadSuccess: function(data){
            if(data.total > 0)
            	jQuery('#soil-sample-uploads-tables').datagrid('selectRow',0);
            else
            	initSoilSampleFileContentGrid(0, crop_layer_id);
        },
        onSelect: function(){
            if(jQuery('#soil-sample-uploads-tables').datagrid('getSelected')) {

            	var file_id = jQuery('#soil-sample-uploads-tables').datagrid('getSelected').id;
            	initSoilSampleFileContentGrid(file_id, crop_layer_id);
        	}
        },
        onBeforeLoad: function(){
        	jQuery('#soil-sample-uploads-tables').datagrid('clearChecked');
        },
        rownumbers:false,
        toolbar:[{
            id:'btnssuupload',
            text:'Качване',
            iconCls:'icon-upload',
            handler:function(){
	            initSoilSampleUpload();
	            jQuery('#win-soil-sample-uploader').window('open');
            }
        },{
            id:'btnssuremove',
            text:'Изтриване',
            iconCls:'icon-delete',
            handler:function(){
            	var getChecked = jQuery('#soil-sample-uploads-tables').datagrid('getChecked');
            	if(getChecked[0]) {
            		jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да премахнете този файл?', function(r){
            			if(r) {
            				TF.Rpc.CropRotation.SoilSampleFilesTree.deleteSoilSampleFile(getChecked[0].id)
                            .done(function (data) {
                                jQuery('#sample-files-tree').tree('reload');
                            })
            				.fail(function (errorObj) {

                            });
            			}
            		});
            	} else {
            		jQuery.messager.alert('Грешка', 'Не е избран файл!');
            	}
            }
        },{
            id:'btnssuapplytolayer',
            text:'Прилагане към слой',
            iconCls:'icon-copy',
            handler:function(){
            	var fileData = jQuery('#soil-sample-uploads-tables').datagrid('getChecked');
            	var fileRows = jQuery('#soil-sample-contents-tables').datagrid('getData');

            	if(fileRows['rows'].length != 0) {
	            	if(fileData[0]){
	            		if(!fileData[0].apply_status) {
		            		jQuery.messager.confirm('Потвърждение', 'Записите, оцветени в червен цвят, са с ненамерен ИСАК номер и няма да бъдат приложени към почвените проби. Сигурни ли сте, че искате да продължите?', function(r){
		            			if(r){
		            				var obj = new Object();
		        	            	obj.file_id = fileData[0]['id'];
		        	            	obj.crop_layer_id = crop_layer_id;

                                    TF.Rpc.CropRotation.SoilSampleFilesTree.applyFile(obj)
                                    .done(function (data) {
                                        jQuery('#win-apply-ss-file').window('close');
                                    })
                                    .fail(function (errorObj) {

                                    });
		            			}
		            		});
	            		} else {
	            			jQuery.messager.alert('Грешка', 'Този файл е вече прилаган за избраният референтен слой.');
	            		}
	            	} else {
	            		jQuery.messager.alert('Грешка', 'Не е избран файл, който да бъде приложен.');
	            	}
            	} else {
            		jQuery.messager.alert('Грешка', 'Невалидна структура на файла.');
            	}
            }
        }],
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initSoilSampleFileContentGrid(file_id, crop_layer_id){
    jQuery('#soil-sample-contents-tables').datagrid({
        title:'Данни за почвени проби',
        iconCls:'icon-flask',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 20,
        fit: true,
        fitColumns: true,
        showFooter: true,
        url:'index.php?croprotation-rpc=soil-samples-files-datagrid',
        rpcParams: [{
            file_id: file_id,
            cl_id: crop_layer_id
        }],
        sortName: '',
        sortOrder: '',
        idField:'',
        singleSelect: false,
        frozenColumns:[[{
            field:'ck',
            checkbox:true
        }]],
        rowStyler: function(index,row){
			if (row.found == 0){
				return 'background-color:#ff0000; color: #fff';
			}
        },
        columns:[[
        {
            field:'ss_num',
            title:'<b>Проба №</b>',
            sortable:true,
            width:100,
            align: 'center'
        },{
            field:'isak',
            title:'<b>ИСАК номер</b>',
            sortable:true,
            width:130,
            align: 'center'
        },{
            field:'ph_h2o',
            title:'<b>pH<br/>(H<sub>2</sub>O)</b>',
            sortable:true,
            width:60,
            align: 'center'
        },{
            field:'no3',
            title:'<b>Съдържание<br/>на азот(NO<sub>3</sub>-N)<br/>мг/кг</b>',
            sortable:true,
            width:110,
            align: 'center'
        },{
            field:'nh4',
            title:'<b>Съдържание<br/>на азот(NH<sub>4</sub>-N)<br/>мг/кг</b>',
            sortable:true,
            width:110,
            align: 'center'
        },{
            field:'p2o5',
            title:'<b>Запас на<br/>фосфор(P<sub>2</sub>O<sub>5</sub>)<br/>мг/кг</b>',
            sortable:false,
            width:90,
            align: 'center'
        },{
            field:'k2o',
            title:'<b>Запас на<br/>калий(K<sub>2</sub>O)<br/>мг/кг</b>',
            sortable:false,
            width:90,
            align: 'center'
        },{
            field:'meh_sastav',
            title:'<b>Механичен<br/>състав</b>',
            sortable:false,
            width: 100,
            align: 'center'
        },{
            field:'sud_humus',
            title:'<b>Хумусно<br/>съдържание</b>',
            sortable:false,
            width: 110,
            align: 'center'
        }]],
        pagination:true,
        rownumbers:false,
        toolbar:[{
            id:'btnssfiledataedit',
            text:'Редактиране',
            iconCls:'icon-edit',
            handler:function(){
            	var getChecked = jQuery('#soil-sample-contents-tables').datagrid('getChecked');
            	if(getChecked[0]) {
            		jQuery('#win-edit-ss-file-data').window('open');
            	} else {
            		jQuery.messager.alert('Грешка', 'Не е избран запис!');
            	}
            }
        }],
        onBeforeLoad: function() {
        	jQuery('#soil-sample-contents-tables').datagrid('clearChecked');
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initSoilSampleUpload() {
	var url  = "index.php?json=soil-samples-upload";

    jQuery("#ss-uploader").pluploadQueue({
        // General settings
        runtimes : 'gears,html5,flash,silverlight,browserplus',
        url : url,
        max_file_size : '100mb',
        unique_names : true,

        //restrictions
        filters : [{title : "CSV Файлове", extensions : "csv"}],

        // Flash settings
        flash_swf_url : 'lib/js_external/fileupload/plupload.flash.swf'

    });

    var uploader = jQuery('#ss-uploader').pluploadQueue();
    uploader.bind('UploadComplete', function() {
        jQuery('#win-soil-sample-uploader').window('close');
        jQuery('#sample-files-tree').tree('reload');
    });
}

function editSoilSampleFileRowData(){
	var fileData = jQuery('#sample-files-tree').tree('getSelected');
	var rowData = jQuery('#soil-sample-contents-tables').datagrid('getChecked');

	if(fileData.id) {
		var obj = new Object();
		var row_num_array = new Array();

		for(var i = 0; i < rowData.length; i++)
		{
			row_num_array.push(rowData[i].row_number);
		}

		obj.file_id = fileData.id;
		obj.row_num_array = row_num_array;
		obj.editRowText = jQuery('#ss-file-data-isak > input').val();

        jQuery('#soil-sample-contents-tables').datagrid('reload');
		TF.Rpc.CropRotation.SoilSampleFilesTree.editRow(obj)
        .done(function (data) {
            jQuery('#ss-file-data-isak > input').val('');
            jQuery('#soil-sample-contents-tables').datagrid('reload');
    		jQuery('#win-edit-ss-file-data').window('close');
        })
        .fail(function (errorObj) {
            RpcErrorHandler.show(errorObj);
        });

	}
}
