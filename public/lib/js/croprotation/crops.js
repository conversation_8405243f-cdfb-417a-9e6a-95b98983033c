var requestDelete;
var reportType;
var _fileName;
jQuery(function() {
	setUserRights();

	jQuery('#farming > input').combobox({
		url: 'index.php?common-rpc=farming-combobox',
		rpcParams: [{
			selected: true
		}],
		valueField: 'id',
		textField: 'name',
		onLoadSuccess: function() {
			jQuery('#farming-year > input').combobox({
				url: 'index.php?common-rpc=farming-year-combobox',
				rpcParams: [{
					selected: 'current'
				}],
				valueField: 'id',
				textField: 'title',
				loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
				loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
			});
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

	jQuery('#save-crop-data-button').bind('click', function() {
		var getChecked = jQuery('#crop-layers-tree').tree('getSelected');
		if (getChecked.id) {
			if (initAddDataValidators()) {
				var obj = getCropLayerDataFields();
				obj.cl_id = getChecked.id;

				TF.Rpc.CropRotation.LayerDataGrid.add(obj)
				.done(function (data) {
					jQuery('#win-add-crop-data').window('close');
					jQuery('#crop-tables').datagrid('reload');
				})
				.fail(function (errorObj) {

				});
			} else {
				jQuery.messager.alert('Грешка', 'Моля попълнете всички полета.');
			}
		} else {
			jQuery.messager.alert('Грешка', 'Моля изберете референтен слой, за който да добавите запис.');
		}
	});

	jQuery('#save-crop-layer-button').bind('click', function() {
		var farming = jQuery('#farming > input').combobox('getValue');
		var year = jQuery('#farming-year > input').combobox('getValue');
		var addLayerObj = {year: year, farming: farming};

		TF.Rpc.CropRotation.CropLayersTree.add(addLayerObj)
		.done(function (data) {
			jQuery('#add-window').window('close');
			jQuery('#crop-layers-tree').tree('reload');
		})
		.fail(function (errorObj) {

		});

	});

	jQuery('#copy-ref-layer').bind('click', function(e) {
		e.preventDefault();
		var getChecked = jQuery('#crop-layers-tree').tree('getSelected');

		if (getChecked.id) {
			jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да копирате референтния слой?', function(r) {
	                if (r) {
	                    var farming = getChecked.attributes.farming;
						var year = getChecked.attributes.year;
						var id = getChecked.id;

						var copyLayerObject = {
							farming: farming,
							year: year,
							copy: "copyLayer",
							crop_layer_id: id
						};
						TF.Rpc.CropRotation.CropLayersTree.add(copyLayerObject)
						.done(function (data) {
							jQuery('#crop-layers-tree').tree('reload');
						})
						.fail(function (errorObj) {

						});
	                }
	            });
		}
	});

	jQuery('#named-ref-layer').bind('click', function(e) {
		e.preventDefault();
		var getChecked = jQuery('#crop-layers-tree').tree('getSelected');

		if (getChecked.id) {
			jQuery('#crop-layers-tree').tree('beginEdit', getChecked.target);
		}
	});

	jQuery('#crop-layer-report-button').bind('click', function() {
		var getChecked = jQuery('#crop-layers-tree').tree('getSelected');
		if (getChecked.id) {
			initReport(getChecked.id)
		} else {
			jQuery.messager.alert('Грешка', 'Моля изберете референтен слой, за който да добавите запис.');
		}
	});

	jQuery('#crop-field > input').validatebox({
		required: true,
		missingMessage: 'Моля въведете номер на полето.'
	});
	jQuery('#crop-number > input').validatebox({
		required: true,
		missingMessage: 'Моля въведете номер.'
	});

	jQuery('#save-crop-layer-button').linkbutton({iconCls: 'icon-save'});

	//edit soil sample file row data
	jQuery('#soil-sample-edit-file-data-save').bind('click', function() {
		if (jQuery('#ss-file-data-isak > input').val()) {
			editSoilSampleFileRowData();
		} else {
			jQuery.messager.alert('Грешка', 'Не е въведен ИСАК номер.');
			return false;
		}
	});
	//end of edit soil sample file row data

	//sample norm logic
	jQuery('#btn-choose-norm-type > a').linkbutton({iconCls: 'icon-ok'});

	jQuery('#btn-choose-norm-type > a').bind('click', function() {
		var layerData = jQuery('#crop-layers-tree').tree('getSelected');
		var chosen_year = jQuery('#choose-norm-year > input').combobox('getValue');

		displaySampleNormGrid(layerData.id, layerData.year, chosen_year);
	});
	//end of sample norm logic

	//soil sample export
	jQuery('#btn-soil-sample-export').bind('click', function() {

		jQuery('#win-soil-sample-export').window('close');

		var bnf = jQuery('#sse-benefecient > input').val();
		var urn = jQuery('#sse-urn > input').val();
		var layer_id = jQuery('#crop-layers-tree').tree('getSelected').id;
		var isak = exportIsak;
		var date_from = exportDateFrom;
		var date_to = exportDateTo;
		//console.info(isak, date_from, date_to);

		var gridData = jQuery('#tables-soil-samples').datagrid('getData');
		var record_count = gridData.total;

		var exportObj = {
			cl_id: layer_id,
			bnf: bnf,
			urn: urn,
			isak: isak,
			date_from: date_from,
			date_to: date_to,
		};

		if (record_count <= 100) {
			exportObj.pdf_export = true;
			TF.Rpc.CropRotation.CropRotationExports.exportSoilSample(exportObj)
			.done(function (data) {
				createDownloadVariables();
				winDownloadDiaryReport.window('open');
				var path = data.file_path;
				_pathFile = path;
				_fileName = data.file_name;
				downloadFileDiaryReport.attr("href", path);
			})
			.fail(function (errorObj) {

			});
		} else {
			exportObj.pdf_export = false;
			TF.Rpc.CropRotation.CropRotationExports.exportSoilSample(exportObj)
			.done(function (data) {
				jQuery('body').append('<iframe id="printf" name="printf"></iframe>');
				var newWin = window.frames['printf'];
				newWin.document.write('<body onload=window.print()>'+data+'</body>');
				newWin.document.close();
				setTimeout(function () {
					jQuery('#printf').remove();
				}, 1000);
			})
			.fail(function (errorObj) {

			});
		}
	});
	//end of soil sample export

	jQuery('#generate-full-norm-plan').bind('click', function(){
		var layer_id = jQuery('#crop-layers-tree').tree('getSelected').id;
		var bnf = jQuery('#choose-norm-benefecient > input').val();
		var urn = jQuery('#choose-norm-urn > input').val();

		var exportObj = {
			cl_id: layer_id,
			bnf: bnf,
			urn: urn
		};

		TF.Rpc.CropRotation.CropRotationExports.exportFullSampleNorm(exportObj)
		.done(function (data) {
			jQuery('body').append('<iframe id="printf" name="printf"></iframe>');
			var newWin = window.frames['printf'];
			newWin.document.write('<body onload=window.print()>'+data+'</body>');
			newWin.document.close();
			setTimeout(function () {
				jQuery('#printf').remove();
			}, 1000);
		})
		.fail(function (errorObj) {

		});
	});

	//crop layer tree
	initLayersTree();
	initSampleFilesTree();
});

function initCropComboboxData() {
	var layerData = jQuery('#crop-layers-tree').tree('getSelected');

	if (layerData.id) {
		jQuery('#crop-culture-1 > input').combobox({
			url: 'index.php?common-rpc=culture-combobox',
			valueField: 'id',
			textField: 'name',
			onLoadSuccess: function() {
				jQuery('#crop-culture-2 > input, #crop-culture-3 > input, #crop-culture-4 > input, #crop-culture-5 > input').combobox({
					data: jQuery('#crop-culture-1 > input').combobox('getData'),
					valueField: 'id',
					textField: 'name',
				});
			},
			loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
			loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
		});

		jQuery('#crop-number > input').combobox({
			url: 'index.php?croprotation-rpc=crop-layer-isak-combobox',
			rpcParams: [{
				cl_id: layerData.id
			}],
			valueField: 'id',
			textField: 'name',
			onSelect: function(record) {
				jQuery('#crop-area > input').val(record.area);
			},
			loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
			loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
		});
	} else {
		jQuery.messager.alert('Грешка', 'Не е намерен референтен слой!');
	}
}

function initFileUploads() {
	farming = jQuery('#farming > input').combobox('getValue');
	year = jQuery('#farming-year > input').combobox('getValue');

	const url  = "index.php?json=crop-layers-upload";

	jQuery("#uploader").pluploadQueue({
		// General settings
		runtimes: 'gears,html5,flash,silverlight,browserplus',
		url: url,
		max_file_size: '100mb',
		unique_names: true,
		multipart_params : {
			farming: farming,
			year: year
        },
		// Flash settings
		flash_swf_url: 'lib/js_external/fileupload/plupload.flash.swf'
	});

	var uploader = jQuery('#uploader').pluploadQueue();
	uploader.bind('UploadComplete', function() {
		jQuery('#win-add-file').window('close');
		jQuery('#layer-tables').datagrid('reload');
	});
}

function initAddDataValidators() {
	if (jQuery('#crop-field > input').validatebox('isValid')
			&& !isNaN(jQuery('#crop-area > input').val())
			&& jQuery('#crop-number > input').combobox('getText')
			&& jQuery('#crop-culture-1 > input').combobox('getValue').length == 6
			&& jQuery('#crop-culture-2 > input').combobox('getValue').length == 6
			&& jQuery('#crop-culture-3 > input').combobox('getValue').length == 6
			&& jQuery('#crop-culture-4 > input').combobox('getValue').length == 6
			&& jQuery('#crop-culture-5 > input').combobox('getValue').length == 6) {
		return true;
	} else {
		return false;
	}
}

function unsetOldValues() {
	jQuery('#crop-field > input').val('');
	jQuery('#crop-area > input').val('');
	jQuery('#crop-number > input').val('');
	jQuery('#crop-culture-1 > input').val('');
	jQuery('#crop-culture-2 > input').val('');
	jQuery('#crop-culture-3 > input').val('');
	jQuery('#crop-culture-4 > input').val('');
	jQuery('#crop-culture-5 > input').val('');
}

function initNewFileUploads(layer_id) {

	jQuery("#uploader").pluploadQueue({
		// General settings
		runtimes: 'gears,html5,flash,silverlight,browserplus',
		url: 'files/fileupload_crop_layers.php?uid=' + groupID + '&sid=' + sessionid + '&layer_id=' + layer_id,
		max_file_size: '100mb',
		unique_names: true,
		// Flash settings
		flash_swf_url: 'lib/js_external/fileupload/plupload.flash.swf'

	});

	var uploader = jQuery('#uploader').pluploadQueue();
	uploader.bind('UploadComplete', function() {
		jQuery('#win-add-file').window('close');
		jQuery('#layer-tables').datagrid('reload');
	});
}

function initReport(id) {
	jQuery('#add-window').window('close');
	var farming = jQuery('#farming > input').combobox('getValue');
	var year = jQuery('#farming-year > input').combobox('getValue');
	var yearText = jQuery('#farming-year > input').combobox('getText');

	if (reportType == 1)
		initOverlapReportGrid(id, farming, year, yearText);
	else if (reportType == 2)
		initCultureReportGrid(id, farming, year, yearText);
}

function initOverlapReportGrid(id, farming, year, yearText) {
	jQuery('#win-overlap-report').window('open');
	jQuery('#tables-overlap-reports').datagrid({
		nowrap: true,
		autoRowHeight: true,
		striped: true,
        pageList: [10,20,50,100,150],
        pageSize: 150,
		fit: true,
		fitColumns: true,
		showFooter: true,
		url: 'index.php?croprotation-rpc=croprotation-overlap-report',
		rpcParams: [{
			layer_id: id,
			farming: farming,
			year: year
		}],
		sortName: 'isak_number',
		sortOrder: 'asc',
		border: false,
		rowStyler: function(index, row) {
			if (row.intersect_percent < 90) {
				return 'background-color:#ff0000; color: #fff';
			}
		},
		columns: [
			[
				{
					title: '<b>Референтен слой</b>',
					colspan: 2
				},
				{
					title: '<b>Земеделски парцели за ' + yearText + '</b>',
					colspan: 2
				}, {
					field: 'intersect_percent',
					title: '<b>Припокриване (%)</b>',
					sortable: true,
					align: 'center',
					width: 150,
					rowspan: 2
				}, {
					field: 'intersect_area',
					title: '<b>Площ припокриване<br/>(дка)</b>',
					sortable: true,
					align: 'center',
					width: 200,
					rowspan: 2
				}
			], [
				{
					field: 'prc_uin',
					title: '<b>ИСАК номер</b>',
					sortable: true,
					align: 'center',
					width: 150
				}, {
					field: 'isak_area',
					title: '<b>Площ ИСАК<br/>(дка)</b>',
					sortable: true,
					align: 'center',
					width: 150
				}, {
					field: 'isak_number',
					title: '<b>Парцел</b>',
					sortable: true,
					align: 'center',
					width: 150
				}, {
					field: 'zp_area',
					title: '<b>Площ<br/>(дка)</b>',
					sortable: true,
					align: 'center',
					width: 200
				}
			]
		],
		pagination: true,
		rownumbers: false,
		toolbar: [{
				id: 'btnexportcroprotationintesect',
				text: 'Експорт',
				iconCls: 'icon-export',
				handler: function() {

					var exportObj = {
						layer_id: id,
						farming: farming,
						year: year
					};

					TF.Rpc.CropRotation.CropRotationExports.exportOverlapData(exportObj)
					.done(function (data) {
						createDownloadVariables();
						winDownloadDiaryReport.window('open');
						var path = data.file_path;
						_pathFile = path;
						_fileName = data.file_name;
						downloadFileDiaryReport.attr("href", path);
					})
					.fail(function (errorObj) {

					});
				}
			}],
			loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
			loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function initCultureReportGrid(id, farming, year, yearText) {
	jQuery('#win-culture-report').window('open');

	jQuery('#tables-culture-reports').datagrid({
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		pageSize: 20,
		fit: true,
		fitColumns: true,
		showFooter: true,
		url: 'index.php?croprotation-rpc=croprotation-culture-report',
		rpcParams: [{
			layer_id: id,
			farming: farming,
			year: year
		}],
		sortName: 'isak_number',
		sortOrder: 'asc',
		border: false,
		rowStyler: function(index, row) {
			if (row.status == 0) {
				return 'background-color:#ff0000; color: #fff';
			}
		},
		columns: [
			[
				{
					title: '<b>Референтен слой</b>',
					colspan: 3
				},
				{
					title: '<b>Земеделски парцели за ' + yearText + '</b>',
					colspan: 2
				}
			], [
				{
					field: 'isak_number',
					title: '<b>ИСАК номер</b>',
					sortable: true,
					width: 200
				}, {
					field: 'culture',
					title: '<b>Култура по план</b>',
					sortable: true,
					width: 150
				}, {
					field: 'area',
					title: '<b>Площ (xa)</b>',
					sortable: true,
					width: 100
				}, {
					field: 'isak_prc_uin',
					title: '<b>Парцел</b>',
					sortable: true,
					width: 150
				}, {
					field: 'zp_culture',
					title: '<b>Култура</b>',
					sortable: true,
					width: 150
				}
			]
		],
		pagination: true,
		rownumbers: false,
		toolbar: [{
				id: 'btnexportcroprotationculture',
				text: 'Експорт',
				iconCls: 'icon-export',
				handler: function() {

					var exportObj = {
						layer_id: id,
						farming: farming,
						year: year
					};

					TF.Rpc.CropRotation.CropRotationExports.exportCultureData(exportObj)
					.done(function (data) {
						createDownloadVariables();
						winDownloadDiaryReport.window('open');
						var path = data.file_path;
						_pathFile = path;
						_fileName = data.file_name;
						downloadFileDiaryReport.attr("href", path);
					})
					.fail(function (errorObj) {

					});
				}
			}],
		onBeforeLoad: function() {
			jQuery('#tables-culture-reports').datagrid('clearChecked');
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function initMultiEditWindow() {
	var cropLayer = jQuery('#crop-layers-tree');
	var cropChecked = cropLayer.tree('getChecked');
	var multiEditWin = jQuery('#multiedit-window');
	var newCrop = jQuery('#multi-edit-new-crop > input');
	var oldCrop = jQuery('#multi-edit-old-crop > input');
	var selected = jQuery('#crop-layers-tree').tree('getSelected').target;
	var parent = jQuery('#crop-layers-tree').tree('getParent', selected);
	var dobiv = jQuery('#multi-edit-dobiv > input');
	var year = parent.id;

	multiEditWin.window({
		onClose: function () {
			jQuery('#multi-edit-year').combobox('reset');
			newCrop.combobox('reset');
			oldCrop.combobox('reset');
			oldCrop.combobox('loadData', []);
			dobiv.val('');
		}
	})
	if(!cropChecked){
		return;
	}

	oldCrop.combobox({
		url: '',
		data: [],
		valueField: 'crop_id',
		textField: 'crop_name',
		editable: false,
		width: 150
	});

	newCrop.combobox({
		url: 'index.php?common-rpc=culture-combobox',
		rpcParams: [{
			year: 6,
			without_crops: true,
			crop_rotation: true
		}],
		valueField: 'id',
		textField: 'name',
		editable: true,
		width: 150,
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});


	jQuery('#multi-edit-year').combobox({
		url: 'index.php?common-rpc=crop-year-combobox',
		rpcParams: [{
			year: year
		}],
		valueField: 'id',
		textField: 'name',
		editable: false,
		multiple: true,
		width: 150,
		onHidePanel: afterSelectCropYear,
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});



}

function afterSelectCropYear () {
	var checkedCrops = jQuery('#crop-tables').datagrid('getChecked');
	var oldCropsArr = [];
	var oldCrop = jQuery('#multi-edit-old-crop > input');
	var cropYears = jQuery('#multi-edit-year').combobox('getValues');

	for (var i = 0; i < checkedCrops.length; i++) {
		for (var j = 0; j < cropYears.length; j++) {
			var comboItem = {};
			var cropYear = cropYears[j];
			var checkedCrop = checkedCrops[i];
			var culture = 'culture' + cropYear;
			var culture_code = 	culture + '_code';

			//check if exist in array or didn't enter year(cropYear is null)
			if (cultureExists(checkedCrop[culture_code]) || cropYear == "") {
				continue;
			}

			if(checkedCrop[culture] == null){
				checkedCrop[culture] = 'Без култура';
				checkedCrop[culture_code] = '0';
			}
			comboItem.crop_id = checkedCrop[culture_code];
			comboItem.crop_name = checkedCrop[culture];

			oldCropsArr.push(comboItem);
		};
	};

	oldCrop.combobox('loadData', oldCropsArr);

	function cultureExists(crop_id) {
	  for (var i = 0; i < oldCropsArr.length; i++) {
	  	if(crop_id == oldCropsArr[i].crop_id){
	  		return true;
	  	}
	  };
	  return false;
	}
}

function executeMultiEdit() {

	jQuery.messager.confirm('Потвърждение', 'Това действие ще промени всички данни, показани в таблицата. Сигурни ли сте, че искате да продължите?', function(r) {
		if (r) {
			var obj = new Object();
			var oldCrop = jQuery('#multi-edit-old-crop > input').combobox('getValues');
			var newCrop = jQuery('#multi-edit-new-crop > input').combobox('getValues');
			var years = jQuery('#multi-edit-year').combobox('getValues');
			var dobiv = jQuery('#multi-edit-dobiv > input').val();
			var checkedCrops = jQuery('#crop-tables').datagrid('getChecked');
			var ids = [];

			for (var i = 0; i < checkedCrops.length; i++) {
				ids.push(checkedCrops[i].id);
			};

			if(years.length){
				if(oldCrop[0] != ""){
					obj.oldcrop = oldCrop[0];
	           		obj.newcrop = newCrop;
				}
				if(dobiv.length && jQuery.isNumeric(dobiv)){
					obj.dobiv = dobiv;
				}

	            obj.years = years;
	            obj.ids = ids;

	            if(obj.oldcrop == undefined &&  obj.dobiv == undefined){
	            	jQuery.messager.alert('Грешка', 'Моля, въведете данни за мултиредакция!');
	            	return;
	            }

	            TF.Rpc.CropRotation.LayerDataGrid.multiEdit(obj)
	            .done(function (data) {
					jQuery('#multiedit-window').window('close');
					jQuery('#crop-tables').datagrid('reload');
	            })
	            .fail(function (errorObj) {

	            });

			}
			else{
				jQuery.messager.alert('Грешка', 'Моля, изберете година/и!');
			}
		}
	});
}

function getCropLayerDataFields() {
	return {
		field: jQuery('#crop-field > input').val(),
		area: jQuery('#crop-area > input').val(),
		isak: jQuery('#crop-number > input').combobox('getText'),
		culture_1: jQuery('#crop-culture-1 > input').combobox('getValue'),
		culture_2: jQuery('#crop-culture-2 > input').combobox('getValue'),
		culture_3: jQuery('#crop-culture-3 > input').combobox('getValue'),
		culture_4: jQuery('#crop-culture-4 > input').combobox('getValue'),
		culture_5: jQuery('#crop-culture-5 > input').combobox('getValue')
	}
}


function createDownloadVariables() {
    winDownloadDiaryReport = jQuery('#win-download').window({
        onClose: onDownloadCropReportWindowClose
    });

    downloadFileDiaryReport = jQuery('#btn-download-file');
    cancelDownloadFileDiaryReport = jQuery('#btn-download-file-close');
}

function onDownloadCropReportWindowClose() {
   return;
}
