var DIVIDE_PROPORTIONAL = 1;
var DIVIDE_BY_PLOT = 2;

function personalUsePlotsGrid(type, divide_type, personal_use_plots, personal_use_value, used_area) {

    var tableId = '#personal-use-plots-grid';
    if (type == 'add') {
        tableId = '#edit-personal-use-plots-grid';
    }
    var personalUsePlotsGrid = jQuery(tableId);
    var personalUsePlotsWindow = jQuery('#win-personal-use-plots');
    var activeIndex = null;

    personalUsePlotsGrid.datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        fit: true,
        fitColumns: false,
        border: false,
        columns: [
            [{
                field: 'ekate',
                title: '<b>Землище</b>',
                align: 'center',
                sortable: true,
                width: 100
            }, {
                field: 'kad_ident',
                title: '<b>Идентификатор</b>',
                align: 'center',
                sortable: true,
                width: 100
            }, {
                field: 'category',
                title: '<b>Категория</b>',
                align: 'center',
                sortable: true,
                width: 100
            }, {
                field: 'area_type',
                title: '<b>НТП</b>',
                align: 'center',
                sortable: true,
                width: 100
            }, {
                field: 'contract_area',
                title: '<b>Площ по<br/>договор (дка)</b>',
                align: 'center',
                sortable: true,
                width: 90,
                formatter: function(value, row, index) {
                    if (value) {
                        return parseFloat(value).toFixed(3);
                    }
                }
            }, {
                field: 'personal_use_area',
                title: '<b>Площ за<br/>лич.полз. (дка)</b>',
                align: 'center',
                sortable: true,
                width: 100,
                formatter: function(value) {
                    if (value) {
                        return parseFloat(value).toFixed(3);
                    }
                },
                editor: {
                    type: 'numberbox',
                    options: {
                        required: true,
                        min: 0,
                        precision: 3,
                        inputEvents:jQuery.extend({},jQuery.fn.numberbox.defaults.inputEvents,{
                            keyup:function(){
                                let newValue = this.value;
                                let treatments_price_editor = personalUsePlotsGrid.datagrid('getEditor', {index: activeIndex, field: 'treatments_price'});
                                let sum_editor = personalUsePlotsGrid.datagrid('getEditor', {index: activeIndex, field: 'price_sum'});
                                let treatments_price_value = jQuery(treatments_price_editor.target).numberbox('getValue');
                                if(treatments_price_value !== '' && treatments_price_value !== null && newValue !== '' && newValue !== null) {
                                    jQuery(sum_editor.target).numberbox('setValue', (newValue * treatments_price_value));
                                }
                            }
                        })
                    }
                }
            }, {
                field: 'renta_type',
                title: '<b>Култура</b>',
                align: 'center',
                sortable: true,
                width: 100,
                editor: {
                    type: 'combobox',
                    options: {
                        data: ComboboxData.RentaTypesCombobox,
                        value: 0,
                        valueField: 'id',
                        textField: 'name',
                        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
                        onClick: function(newValue) {
                            let average_yield_editor = personalUsePlotsGrid.datagrid('getEditor', {index: activeIndex, field: 'average_yield'});
                            let treatments_price_editor = personalUsePlotsGrid.datagrid('getEditor', {index: activeIndex, field: 'treatments_price'});
                            let sum_editor = personalUsePlotsGrid.datagrid('getEditor', {index: activeIndex, field: 'price_sum'});
                            let personal_use_area_editor = personalUsePlotsGrid.datagrid('getEditor', {index: activeIndex, field: 'personal_use_area'});
                            let personal_use_unit_value_editor = personalUsePlotsGrid.datagrid('getEditor', {index: activeIndex, field: 'unit_value'});
                            let personal_use_area_value = jQuery(personal_use_area_editor.target).numberbox('getValue');
                            jQuery(average_yield_editor.target).numberbox('setValue', newValue.avg_yield);
                            jQuery(treatments_price_editor.target).numberbox('setValue', newValue.treatment_price);
                            jQuery(personal_use_unit_value_editor.target).numberbox('setValue', newValue.unit_value);
                            if(personal_use_area_value !== null && personal_use_area_value !== '' && newValue.treatment_price !== null && newValue.treatment_price !== '') {
                                jQuery(sum_editor.target).numberbox('setValue', (personal_use_area_value * newValue.treatment_price));
                            } else {
                                jQuery(sum_editor.target).numberbox('setValue', '');
                            }
                        },
                    }
                },
                formatter: function(value, row, index) {
                    var rowRentaType = ComboboxData.RentaTypesCombobox.find(function(element) {
                        return element.id == value;
                    });

                    if (rowRentaType) {
                        return rowRentaType.name;
                    } else {
                        return '-'
                    }
                }
            }, {
                field: 'average_yield',
                title: '<b>Среден добив<br/>дка</b>',
                align: 'center',
                sortable: true,
                width: 100,
                editor: {
                    type: 'numberbox',
                    options: {
                        min: 0,
                        precision: 3,
                        required:false
                    }
                }
            }, {
                field: 'treatments_price',
                title: '<b>Обработки<br/>лв/дка</b>',
                align: 'center',
                sortable: true,
                width: 100,
                editor: {
                    type: 'numberbox',
                    options: {
                        min: 0,
                        precision: 3,
                        required:false,
                        inputEvents:jQuery.extend({},jQuery.fn.numberbox.defaults.inputEvents,{
                            keyup:function(){
                                let newValue = this.value;
                                let personal_use_area_editor = personalUsePlotsGrid.datagrid('getEditor', {index: activeIndex, field: 'personal_use_area'});
                                let sum_editor = personalUsePlotsGrid.datagrid('getEditor', {index: activeIndex, field: 'price_sum'});
                                let personal_use_area_value = jQuery(personal_use_area_editor.target).numberbox('getValue');

                                if(personal_use_area_value !== '' && personal_use_area_value !== null && newValue !== '' && newValue !== null) {
                                    jQuery(sum_editor.target).numberbox('setValue', (newValue * personal_use_area_value));
                                }
                            }
                        })
                    }
                }
            }, {
                field: 'price_sum',
                title: '<b>Обща сума</b>',
                align: 'center',
                sortable: true,
                width: 100,
                editor: {
                    type: 'numberbox',
                    options: {
                        precision: 2
                    }
                }
            }, {
                field: 'renta_per_dka',
                title: '<b>Количество<br/>натура/дка</b>',
                align: 'center',
                sortable: true,
                width: 100,
                editor: {
                    type: 'numberbox',
                    options: {
                        min: 0,
                        precision: 3,
                        required:false
                    }
                }
            }, {
                field: 'unit_value',
                title: '<b>Единична<br/>стойност(лв)</b>',
                align: 'center',
                sortable: true,
                width: 100,
                editor: {
                    type: 'numberbox',
                    options: {
                        min: 0,
                        precision: 2
                    }
                }
            }, ]
        ],
        pagination: false,
        rownumbers: true,
        onClickRow: function(index, row) {
            activeIndex = index;
            let rows = personalUsePlotsGrid.datagrid('getRows');
            jQuery.each(rows, function (i, row){
                if(index !== i) {
                    personalUsePlotsGrid.datagrid('unselectRow', i);
                    personalUsePlotsGrid.datagrid('endEdit', i);
                }
            });
            personalUsePlotsGrid.datagrid('beginEdit', index);
            let sum_editor = personalUsePlotsGrid.datagrid('getEditor', {index: index, field: 'price_sum'});
            jQuery(sum_editor.target).textbox('disable');
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    //check which button was clicked
    if (type == 'info' && divide_type == DIVIDE_PROPORTIONAL) {
        personalUsePlotsWindow.window('setTitle', 'Информация за пропорционалното разделение на личното ползване по имот');

        personalUsePlotsGrid.datagrid({
            toolbar: null
        });

        var personal_use_plots_temp = JSON.parse(JSON.stringify(personal_use_plots));
        var personal_use_plots_info = changePersonalUseValueByDivision(personal_use_plots_temp, personal_use_value, used_area);

        //load data in datagrid
        personalUsePlotsGrid.datagrid('loadData', {
            total: personal_use_plots_info.length,
            rows: personal_use_plots_info
        });
        return;
    } else if (type == 'add' && divide_type == DIVIDE_BY_PLOT) {
        personalUsePlotsWindow.window('setTitle', 'Добавяне на лично ползване по имот');
    } else if (type == 'info' && divide_type == DIVIDE_BY_PLOT) {
        personalUsePlotsWindow.window('setTitle', 'Информация за съществуващото лично ползване по имот');

        personalUsePlotsGrid.datagrid({
            toolbar: null
        });
    }

    //load data in datagrid
    personalUsePlotsGrid.datagrid('loadData', {
        total: personal_use_plots.length,
        rows: personal_use_plots
    });
}
