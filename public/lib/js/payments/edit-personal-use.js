var personal_use_edit = false;
var personal_use_edit_index = undefined;
var personal_use_divide = undefined;
var personal_use_value = undefined;
var used_area = undefined;

var is_personal_use_proportionally_distributed = true;
var selected_personal_use_row;
var DIVIDE_BY_PLOT = 2;
var selectedOwnerId;
var selectedContractId;

function openPersonalUseRenta(owner_id, isEdit = true) {
    jQuery('.js-personal-use-multirent-row').remove();
    getPersonalUseData(owner_id, isEdit);
    jQuery('#win-personal-use-rents').window('open');
}

function getPersonalUseData(owner_id, isEdit = false) {
    let selected = jQuery('#contracts-tree').tree('getSelected');
    selectedContractId = selected.id;
    selectedOwnerId = owner_id;
    if(selected.attributes.annex_id != null){
        selectedContractId = selected.attributes.annex_id;
    }

    let params = {
        'contract_id': selectedContractId,
        'owner_id': owner_id,
        'year': jQuery('#search-year').combobox('getValue')
    }

    TF.Rpc.Payments
       .PersonalUseGrid
       .getPersonalUseData(params)
       .done(function (dataObj) {
           is_personal_use_proportionally_distributed = dataObj.is_proportionally_distributed;
           jQuery( "#personal-use-auto-crops-divide" ).prop( "checked", (dataObj.auto_crops_divide || dataObj.auto_crops_divide === null));
           jQuery('#kvs-used-area > input').val(dataObj.total_owned_area.toFixed(3));

           initPersonalUseAreaFields(dataObj);
           if(dataObj.rents.length > 0) {
               dataObj.rents.forEach(function(element, index) {
                   addPersonalUseRentaField(element, index);
               });
           } else {
                addPersonalUseRentaField();
           }
           personalUsePlotsGrid('add', DIVIDE_BY_PLOT, dataObj.rows, selected.personal_area, dataObj.total_owned_area);
           if(isEdit) {
               jQuery('#personal-use-divide > input').combobox('disable');
           } else {
               jQuery('#personal-use-divide > input').combobox('enable');
           }
       })
       .fail(function (errorObj) {
            jQuery('#win-personal-use-rents').window('close');
       });
}

function initPersonalUseAreaFields(record) {
    jQuery('#kvs-used-area > input').prop('disabled', true);

    jQuery('#personal-use-area > input').numberspinner({
        required: true,
        min:0,
        precision:3,
        width:130,
        onChange: function (newValue, oldValue) {
            let used_area = parseFloat(jQuery('#kvs-used-area > input').val());
            if(newValue > used_area) {
                jQuery(this).numberspinner('setValue', oldValue);
                jQuery.messager.alert('Внимание', 'Площта за лично ползване надвишава общата площ!', 'warning');
                return false;
            }
            var rentaRows = jQuery('#js-personal-use-multirent-table').find('.js-personal-use-multirent-row').length;
            let cropsCount = rentaRows;
            for (let i = 0;; i++) {
                if(rentaRows === 0) {
                    return false;
                }
                if(jQuery('#personal-use-area-' + (i)).length === 0) continue;
                rentaRows--;

                let treatments_price_value = jQuery('#personal-use-treatments-price-' + (i)).numberspinner('getValue');
                if(treatments_price_value !== '' && treatments_price_value !== null && newValue !== '' && newValue !== null) {
                    let treatmentsSum;
                    if(jQuery('#personal-use-auto-crops-divide').is(':checked')){
                        treatmentsSum = ((newValue / cropsCount) * treatments_price_value);
                    } else {
                        let personalUseArea = jQuery('#personal-use-area-' + (i)).numberspinner('getValue');
                        treatmentsSum = personalUseArea * treatments_price_value;
                    }
                    jQuery('#personal-use-price-sum-' + (i)).numberspinner('setValue', treatmentsSum);
                } else {
                    jQuery('#personal-use-price-sum-' + (i)).numberspinner('setValue', '');
                }

                if(jQuery('#personal-use-auto-crops-divide').is(':checked')){
                    jQuery('#personal-use-area-' + (i)).numberspinner('setValue', '');
                }
            }
        }
    });

    jQuery('#personal-use-area > input').numberspinner('setValue', record.total_personal_use_area);

    jQuery('#personal-use-auto-crops-divide').change(function() {
        var rentaRows = jQuery('#js-personal-use-multirent-table').find('.js-personal-use-multirent-row').length;
        if(this.checked) {
            jQuery('#personal-use-area > input').numberspinner('enable');
            for (let i = 0; i < rentaRows; i++) {
                jQuery('#personal-use-area-' + (i)).numberspinner('disable');
            }
        } else {
            jQuery('#personal-use-area > input').numberspinner('disable');
            for (let i = 0; i < rentaRows; i++) {
                jQuery('#personal-use-area-' + (i)).numberspinner('enable');
            }
        }
        reCalculatePersonalUsePriceSum();
    });

    jQuery('#personal-use-divide > input').combobox({
        valueField: 'id',
        textField: 'text',
        required: true,
        editable: false,
        missingMessage: 'Моля, въведете начин на разпределение на личното ползване!',
        data: [{
            id: 1,
            text: 'Пропорционално',
            selected: is_personal_use_proportionally_distributed
        }, {
            id: 2,
            text: 'По имот',
            selected: !is_personal_use_proportionally_distributed
        }],
        onChange: function(newValue, oldValue) {
            if (newValue == undefined || newValue == '') {
                jQuery(this).combobox('select', 1);
                return;
            }
            //set which option is selected in Division plots
            personal_use_divide = newValue;

            if (newValue == 1) {
                jQuery('#edit-personal-use-accordion').accordion('getPanel', 'Пропорционално').panel('expand');
                var rentaPanelOptions = jQuery('#edit-personal-use-accordion').accordion('getPanel', 'Пропорционално').panel('options');
                rentaPanelOptions.collapsible = true;
                jQuery('#edit-personal-use-accordion').accordion('getPanel', 'Пропорционално').panel('options', rentaPanelOptions);

                jQuery('#edit-personal-use-accordion').accordion('getPanel', 'По имот').panel('collapse');
                var rentaPanelOptions = jQuery('#edit-personal-use-accordion').accordion('getPanel', 'По имот').panel('options');
                rentaPanelOptions.collapsible = false;
                jQuery('#edit-personal-use-accordion').accordion('getPanel', 'По имот').panel('options', rentaPanelOptions);

                var plotsGrid = jQuery('#edit-personal-use-plots-grid');
                var isDatagridBound = plotsGrid.data().hasOwnProperty('datagrid');

                if(isDatagridBound) {
                    jQuery('#edit-personal-use-plots-grid').datagrid('rejectChanges');
                }
            } else if (newValue == 2) {
                jQuery('#edit-personal-use-accordion').accordion('getPanel', 'Пропорционално').panel('collapse');
                var rentaPanelOptions = jQuery('#edit-personal-use-accordion').accordion('getPanel', 'Пропорционално').panel('options');
                rentaPanelOptions.collapsible = false;
                jQuery('#edit-personal-use-accordion').accordion('getPanel', 'Пропорционално').panel('options', rentaPanelOptions);

                jQuery('#edit-personal-use-accordion').accordion('getPanel', 'По имот').panel('expand');
                var rentaPanelOptions = jQuery('#edit-personal-use-accordion').accordion('getPanel', 'По имот').panel('options');
                rentaPanelOptions.collapsible = true;
                jQuery('#edit-personal-use-accordion').accordion('getPanel', 'По имот').panel('options', rentaPanelOptions);
            }
        }
    });
}

function initPersonalUseRents() {

    jQuery('.js-personal-use-multirent-row').remove();

    var rentaRows = jQuery('#js-personal-use-multirent-table').find('.js-personal-use-multirent-row').length;
    var comboHTML = '<tr class="js-personal-use-multirent-row"><td style="padding:0px 0px 10px 15px"><select id="js-personal-use-renta-type-cb-' + (rentaRows) + '"></select></td>';
    comboHTML += '<td style="padding:0px 0px 10px 10px"><input class="js-personal-use-renta-value" id="personal-use-area-' + (rentaRows) + '"></td>';
    comboHTML += '<td style="padding:0px 0px 10px 10px"><input class="js-personal-use-renta-value" id="personal-use-renta-value-' + (rentaRows) + '"></td>';
    comboHTML += '<td style="padding:0px 0px 10px 10px"><input class="js-personal-use-unit-price" id="personal-use-unit-price-' + (rentaRows) + '"></td>';
    comboHTML += '<td style="padding:0px 0px 10px 10px"><a id="js-personal-use-remove-btn-' + (rentaRows) + '" href="javaScript:void(0)" class="easyui-linkbutton" onClick="removePersonalUseCrop('+rentaRows+');" data-options="iconCls:\'icon-cancel\'">&nbsp;</a></td></tr>';

    jQuery('#js-personal-use-multirent-table').append(comboHTML);
    var rentaNaturaSpinner = jQuery('#personal-use-renta-value-' + (rentaRows));

    jQuery('#personal-use-unit-price-0').numberspinner({
        min: 0,
        disabled: true,
        missingMessage: 'Моля задайте Единична стойност(лв).',
        width: 95,
        precision: 2
    });

    jQuery('#personal-use-renta-value-0').numberspinner({
        min: 0,
        disabled: true,
        required: false,
        missingMessage: 'Моля задайте количество на горепосочената рента в натура(количество за декар).',
        width: 95,
        parser: function(value) {
            if (value == '-') {
                return value;
            }
            if (jQuery.isNumeric(value)) {
                var output = parseFloat(value).toFixed(3);
                if (output < 0) {
                    return parseFloat(0).toFixed(3);
                }
                return output;
            }
        }
    });

    jQuery('#js-personal-use-remove-btn-' + (rentaRows)).linkbutton({
        iconCls: 'icon-cancel',
        width:32
    });

    jQuery('#js-personal-use-renta-type-cb-' + (rentaRows)).combobox({
        data:ComboboxData.RentaTypesCombobox,
        valueField: 'id',
        textField: 'name',
        width: 205,
        editable: false,
        onSelect: function(rec) {
            var rowArr = jQuery('.js-personal-use-multirent-row');
            var rowVars = [];

            for (var i = 0; i < rowArr.length; i++) {
                rowVars[i] = jQuery('#js-personal-use-renta-type-cb-' + i).combobox('getValue');
            }

            var inRes = jQuery.inArray(String(rec.id), rowVars);
            if (inRes != -1 && jQuery.inArray(String(rec.id), rowVars, inRes + 1) != -1) {
                jQuery('#js-personal-use-renta-type-cb-0').combobox('clear');
            }
        },
        onChange: function(newValue, oldValue) {
            onChangePersonalUseRentaTypesCombobox(this, newValue);
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function onChangePersonalUseRentaTypesCombobox(element, newValue) {
    var renta_nat_unit_id = element.id.split("-");
    var id = renta_nat_unit_id[renta_nat_unit_id.length - 1];
    var data = jQuery('#js-personal-use-renta-type-cb-' + id).combobox('getData');
    var natura_price = jQuery('#personal-use-unit-price-' + id);
    var natura_amount = jQuery('#personal-use-renta-value-' + id);

    //clear value
    natura_price.numberbox('clear');

    //get unit value for renta type and set to field
    jQuery.each(data, function(key, value) {
        if ((!value.name || value.name == '-') && value.id == newValue) {
            natura_amount.numberspinner({
                required: false,
                disabled: true,
                min: 0
            });
            natura_amount.numberspinner('clear');
            return;
        } else if (value.id == newValue) {
            natura_price.numberspinner({
                value: value.unit_value,
                disabled: false,
                precision: 2,
            });

            natura_amount.numberspinner({
                required: false,
                disabled: false,
                min: 0
            });


            var natura_amount_value = natura_price.numberbox('getValue');
            if (natura_amount_value == '') {
                natura_amount.numberspinner('clear');
            }
            return;
        }
    });
}

function addPersonalUseRentaField(dataNatura, count) {
    var rentaRows = jQuery('#js-personal-use-multirent-table').find('.js-personal-use-multirent-row').length;

    // if(rentaRows === 1) {
    //     if(jQuery('#js-personal-use-renta-type-cb-0').combobox('getValue') == ''){
    //         jQuery.messager.alert('Грешка', 'Моля изберете тип на натурата.', 'error');
    //         return false;
    //     }
    // }

    var comboHTML = '<tr class="js-personal-use-multirent-row"><td style="padding:0px 0px 10px 15px"><select id="js-personal-use-renta-type-cb-' + (rentaRows) + '"></select></td>';
    comboHTML += '<td style="padding:0px 0px 10px 10px"><input class="js-personal-use-renta-value" id="personal-use-area-' + (rentaRows) + '"></td>';
    comboHTML += '<td style="padding:0px 0px 10px 10px"><input class="js-personal-use-renta-value" id="personal-use-average-yield-' + (rentaRows) + '"></td>';
    comboHTML += '<td style="padding:0px 0px 10px 10px"><input class="js-personal-use-renta-value" id="personal-use-treatments-price-' + (rentaRows) + '"></td>';
    comboHTML += '<td style="padding:0px 0px 10px 10px"><input class="js-personal-use-renta-value" id="personal-use-price-sum-' + (rentaRows) + '"></td>';
    comboHTML += '<td style="padding:0px 0px 10px 10px"><input class="js-personal-use-renta-value" id="personal-use-renta-value-' + (rentaRows) + '"></td>';
    comboHTML += '<td style="padding:0px 0px 10px 10px"><input class="js-personal-use-unit-price" id="personal-use-unit-price-' + (rentaRows) + '"></td>';
    if(rentaRows > 0) {
        comboHTML += '<td style="padding:0px 0px 10px 10px"><a id="js-personal-use-remove-btn-' + (rentaRows) + '" href="javaScript:void(0)" class="easyui-linkbutton" onClick="removePersonalUseCrop('+rentaRows+');" data-options="iconCls:\'icon-cancel\'">&nbsp;</a></td>';
    }
    comboHTML += '</tr>';
    if (count != rentaRows - 1) {
        jQuery('#js-personal-use-multirent-table').append(comboHTML);
    }

    jQuery('#js-personal-use-remove-btn-' + (rentaRows)).linkbutton({
        iconCls: 'icon-cancel',
        width:32
    });

    jQuery('#personal-use-renta-value-' + (rentaRows)).numberspinner({
        disabled: true,
        missingMessage: 'Моля задайте количество на горепосочената рента в натура(количество за декар).',
        width: 130,
        min: 0,
        parser: function(value) {
            if (value == '-') {
                return value;
            }
            if (jQuery.isNumeric(value)) {
                var output = parseFloat(value).toFixed(3);
                if(output < 0) {
                    return parseFloat(0).toFixed(3);
                }
                return output;
            }
        }
    });

    jQuery('#personal-use-unit-price-' + (rentaRows)).numberspinner({
        min: 0,
        disabled: false,
        precision: 2,
        missingMessage: 'Моля задайте единична стойност(лв).',
        width: 130,
    });

    jQuery('#personal-use-area-' + (rentaRows)).numberspinner({
        min: 0,
        disabled: (jQuery('#personal-use-auto-crops-divide').is(':checked')),
        precision: 3,
        missingMessage: 'Моля задайте площ за лично ползване (дка).',
        width: 80,
        onChange: function (newValue, oldValue) {
            if(newValue !== "") {
                calculatePersonalUseSumArea(oldValue, this);
            }
        }
    });

    jQuery('#personal-use-average-yield-' + (rentaRows)).numberspinner({
        min: 0,
        disabled: false,
        precision: 2,
        missingMessage: 'Моля задайте единична стойност(лв).',
        width: 130,
    });

    jQuery('#personal-use-treatments-price-' + (rentaRows)).numberspinner({
        min: 0,
        disabled: false,
        precision: 2,
        missingMessage: 'Моля задайте единична стойност(лв).',
        width: 130,
        onChange: function (newValue) {
            var cropsCount = jQuery('#js-personal-use-multirent-table').find('.js-personal-use-multirent-row').length;
            if(newValue !== '' && newValue !== null) {
                let treatmentsSum;
                let personal_use_area = jQuery('#personal-use-area > input').numberspinner('getValue');
                if(jQuery('#personal-use-auto-crops-divide').is(':checked')){
                    treatmentsSum = ((personal_use_area / cropsCount) * newValue);
                } else {
                    let personalUseArea = jQuery('#personal-use-area-' + rentaRows).numberspinner('getValue');
                    treatmentsSum = personalUseArea * newValue;
                }
                jQuery('#personal-use-price-sum-' + rentaRows).numberspinner('setValue', treatmentsSum);
            } else {
                jQuery('#personal-use-price-sum-' + rentaRows).numberspinner('setValue', '');
            }
        }
    });

    jQuery('#personal-use-price-sum-' + (rentaRows)).numberspinner({
        min: 0,
        disabled: true,
        precision: 2,
        missingMessage: 'Моля задайте единична стойност(лв).',
        width: 130,
    });

    jQuery('#js-personal-use-renta-type-cb-' + (rentaRows)).combobox({
        data: ComboboxData.RentaTypesCombobox,
        valueField: 'id',
        textField: 'name',
        width: 205,
        onSelect: function(rec) {
            let oldValue = jQuery('#js-personal-use-renta-type-cb-'+(rentaRows)).combobox('getValue');
            var rowArr = jQuery('.js-personal-use-multirent-row');
            var rentaTypesArr = [];

            for (var i = 0; i < rowArr.length; i++) {
                var rentaType = jQuery('#js-personal-use-renta-type-cb-' + i).combobox('getValue');
                rentaTypesArr.push(rentaType);
            }
            var inRes = jQuery.inArray(String(rec.id), rentaTypesArr);

            if(inRes !== -1) {
                jQuery.messager.alert('Внимание', 'Типът на натурата вече е избран!', 'warning');
                setTimeout(function(){
                    jQuery('#js-personal-use-renta-type-cb-'+(rentaRows)).combobox('select', oldValue);
                },0);
                return false;
            }


        },
        onChange: function(newValue, oldValue) {
            onChangePersonalUseRentaTypesCombobox(this, newValue);
        },
        onClick: function(newValue) {
            if(newValue.avg_yield > 0){
                jQuery('#personal-use-average-yield-' + (rentaRows)).numberbox('setValue', newValue.avg_yield);
            }
            if(newValue.treatment_price > 0){
                jQuery('#personal-use-treatments-price-' + (rentaRows)).numberbox('setValue', newValue.treatment_price);
            }

            var cropsCount = jQuery('#js-personal-use-multirent-table').find('.js-personal-use-multirent-row').length;
            if(newValue !== '' && newValue !== null) {
                let treatmentsSum;
                let personal_use_area = jQuery('#personal-use-area > input').numberspinner('getValue');
                if(jQuery('#personal-use-auto-crops-divide').is(':checked')){
                    treatmentsSum = ((personal_use_area / cropsCount) * newValue.treatment_price);
                } else {
                    let personalUseArea = jQuery('#personal-use-area-' + rentaRows).numberspinner('getValue');
                    treatmentsSum = personalUseArea * newValue.treatment_price;
                }
                jQuery('#personal-use-price-sum-' + rentaRows).numberspinner('setValue', treatmentsSum);
            } else {
                jQuery('#personal-use-price-sum-' + rentaRows).numberspinner('setValue', '');
            }
        },
        onLoadSuccess: function() {
            if (typeof(dataNatura) != 'undefined') {
                var personalUseRentaType = jQuery('#js-personal-use-renta-type-cb-' + rentaRows);
                var personalUseRentaValue = jQuery('#personal-use-renta-value-' + rentaRows);

                personalUseRentaType.combobox('select', dataNatura['type']);

                var prec = 3;
                if (dataNatura['unit'] === 3) {
                    prec = 0;
                }

                if (dataNatura['area']) {
                    jQuery('#personal-use-area-' + rentaRows).numberspinner('setValue', dataNatura['area']);
                }

                if (dataNatura['unit_value']) {
                    jQuery('#personal-use-unit-price-' + rentaRows).numberspinner('setValue', dataNatura['unit_value']);
                }

                if (dataNatura['average_yield']) {
                    jQuery('#personal-use-average-yield-' + rentaRows).numberspinner('setValue', dataNatura['average_yield']);
                }

                if (dataNatura['treatments_price']) {
                    jQuery('#personal-use-treatments-price-' + rentaRows).numberspinner('setValue', dataNatura['treatments_price']);
                }

                if (dataNatura['price_sum']) {
                    jQuery('#personal-use-price-sum-' + rentaRows).numberspinner('setValue', dataNatura['price_sum']);
                }

                personalUseRentaValue.numberspinner({
                    precision: prec,
                    value: dataNatura['amount'],
                    min:0
                });
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    reCalculatePersonalUsePriceSum();
}

function checkForSelectedNatureType() {
}

function reCalculatePersonalUsePriceSum(){
    let rows = jQuery('#js-personal-use-multirent-table').find('.js-personal-use-multirent-row').length;
    let personal_use_area = jQuery('#personal-use-area > input').numberspinner('getValue');

    for (let i = 0; i < rows; i++){
        let treatmentPrice = jQuery('#personal-use-treatments-price-' + i).numberspinner('getValue');
        let personalUsePriceSum = null;
        if(jQuery('#personal-use-auto-crops-divide').is(':checked')) {
            personalUsePriceSum = ((personal_use_area / rows) * treatmentPrice);
        } else{
            let personal_use_area = jQuery('#personal-use-area-' + i).numberspinner('getValue');
            if(personal_use_area){
                personalUsePriceSum = personal_use_area * treatmentPrice;
            } else {
                personalUsePriceSum = null;
            }
        }
        jQuery('#personal-use-price-sum-' + i).numberspinner('setValue', personalUsePriceSum);
    }
}

function removePersonalUseCrop(rowNum){
    let parent = jQuery('#js-personal-use-remove-btn-' + rowNum).parent().parent();
    parent.detach();

    calculatePersonalUseSumArea();
    reCalculatePersonalUsePriceSum();
}

function calculatePersonalUseSumArea(oldValue = null, row = null){
    let used_area = parseFloat(jQuery('#kvs-used-area > input').val());
    let rentaRows = jQuery('#js-personal-use-multirent-table').find('.js-personal-use-multirent-row').length;
    let personalUsePerNature = 0;

    for (let i = 0;; i++) {
        if(!rentaRows) break;
        if(jQuery('#personal-use-area-' + (i)).length === 0) continue;

        // let rentaType = jQuery('#js-personal-use-renta-type-cb-' + (i)).combobox('getValue');
        // if(rentaType == 0 || rentaType == ""){
        //     jQuery('#personal-use-area-' + (i)).numberspinner('setValue', '');
        //     jQuery.messager.alert('Внимание', 'Моля, изберете Тип натура!', 'warning');
        //     return false;
        // }

        let natureArea = parseFloat(jQuery('#personal-use-area-' + (i)).numberspinner('getValue'));
        if(!isNaN(parseFloat(natureArea)) && isFinite(natureArea)){
            personalUsePerNature += natureArea;
        }

        rentaRows--;
    }

    if(personalUsePerNature > used_area) {
        jQuery(row).numberspinner('setValue', oldValue);
        jQuery.messager.alert('Внимание', 'Площта за лично ползване надвишава общата площ!', 'warning');
        return false;
    } else {
        if(!jQuery('#personal-use-auto-crops-divide').is(':checked')){
            jQuery('#personal-use-area > input').numberspinner('setValue', personalUsePerNature);
        }
    }
}

function getMultiEditContractFields() {
    var obj = {},
        naturesCount = jQuery('.js-personal-use-multirent-row').length,
        i,
        renta_type,
        renta_amount,
        renta_unit_price,
        average_yield,
        treatments_price,
        price_sum,
        area;
    obj.rents = [];
    let hasRentaType = true;

    for (i = 0;; i++) {
        if(!naturesCount) break;
        if(jQuery('#js-personal-use-renta-type-cb-' + i).length === 0) continue;
        naturesCount--;

        renta_type = jQuery('#js-personal-use-renta-type-cb-' + i).combobox('getValue');

        if(renta_type == 0 || renta_type == ""){
            if(!hasRentaType){
                jQuery.messager.alert('Внимание', 'Може да имате само един запис без избран тип натура.', 'warning');
                return false;
            }
            hasRentaType = false;
        }

        renta_amount = jQuery('#personal-use-renta-value-' + i).numberspinner('getValue');
        renta_unit_price = jQuery('#personal-use-unit-price-' + i).numberspinner('getValue');
        average_yield = jQuery('#personal-use-average-yield-' + i).numberspinner('getValue');
        treatments_price = jQuery('#personal-use-treatments-price-' + i).numberspinner('getValue');
        price_sum = jQuery('#personal-use-price-sum-' + i).numberspinner('getValue');
        area = jQuery('#personal-use-area-' + i).numberspinner('getValue');

        //in case of added an empty value instead of 0.00
        if(renta_amount === '') renta_amount = '0.00';

        if (!jQuery('#personal-use-renta-value-' + i).numberspinner('isValid')) {
            jQuery.messager.alert('Грешка', 'Моля попълнете всички задължителни полета.', 'error');
            return false;
        }

        obj.rents[i] = {
            type: parseInt(renta_type, 10),
            amount: renta_amount,
            unit_value: renta_unit_price,
            average_yield:average_yield,
            treatments_price:treatments_price,
            price_sum:price_sum,
            area:area,
        };
    }

    return obj.rents;
}

function savePersonalUse() {
    var obj = new Object();

    var plots = jQuery('#edit-personal-use-plots-grid').datagrid('getRows');
    obj.contract_id = selectedContractId
    obj.owner_id = selectedOwnerId;
    obj.year = jQuery('#search-year').combobox('getValue');
    var distribution = jQuery('#personal-use-divide > input').combobox('getValue');

    let selectedPersonalUse = jQuery('#personal-use-tables').datagrid('getSelected');
    if(selectedPersonalUse) {
        obj.pu_ids = selectedPersonalUse.pu_ids.split(',');
    }

    if(distribution == DIVIDE_BY_PLOT) {
        if(!validatePlotPersonalUse(plots)) {
            jQuery.messager.alert('Грешка', 'Моля попълнете всички задължителни полета.', 'error');
        }else {
            jQuery('#edit-personal-use-plots-grid').datagrid('acceptChanges');
            for (let i= 0; i < plots.length; i++) {

                if (parseFloat(plots[i].personal_use_area).toFixed(3) === parseFloat(plots[i].contract_area).toFixed(3)) {
                    plots[i].personal_use_area = plots[i].contract_area;
                    continue;
                }
                
                if(plots[i].personal_use_area > 0 && parseFloat(plots[i].personal_use_area) > parseFloat(plots[i].contract_area)) {
                    jQuery.messager.alert('Грешка', 'Площта за лично ползване не може да надвишава площта по договор.', 'error');
                    return false;
                }
            }
            obj.personal_use_plots = plots;
            obj.is_personal_use_proportionally_distributed = false;
            savePlotPersonalUseRequest(obj)
        }
    } else{
        personal_area = parseFloat(jQuery('#personal-use-area > input').val());
        if(personal_area === '' || isNaN(personal_area)) {
            jQuery.messager.alert('Грешка', 'Площта за лично ползване е задължително поле.', 'error');
            return false;
        }
        
        used_area = parseFloat(jQuery('#kvs-used-area > input').val());

        if (parseFloat(personal_area).toFixed(3) === parseFloat(used_area).toFixed(3)) {
            personal_area = used_area;
        }

        if(personal_area > 0 && personal_area > used_area) {
            jQuery.messager.alert('Грешка', 'Площта за лично ползване не може да надвишава площта по договор.', 'error');
            return false;
        }

        obj.auto_crops_divide = jQuery('#personal-use-auto-crops-divide').is(':checked');
        obj.rents = getMultiEditContractFields();
        if(obj.rents === false) {
            return false;
        }
        obj.personal_area = personal_area;
        obj.used_area = used_area;
        // obj.personal_use_plots = changePersonalUseValueByDivision(plots, personal_area, used_area, obj.rents.length);
        obj.personal_use_plots = plots;

        if(obj.rents !== false) {
            obj.is_personal_use_proportionally_distributed = true;
            savePlotPersonalUseRequest(obj);
        }
    }
}

function validatePlotPersonalUse(plots) {
    for (var i = 0; i<plots.length; i++) {
        if (!jQuery('#edit-personal-use-plots-grid').datagrid('validateRow', i))
        {
            return false;
        }
    }

    return true;
}

function savePlotPersonalUseRequest(obj) {
    TF.Rpc.Payments
    .PersonalUseGrid
    .savePersonalUse(obj)
    .done(function (dataObj) {
        jQuery('#personal-use-tables').datagrid('reload');
        jQuery('#contract-payments-tables').treegrid('reload');
        jQuery('#win-personal-use-rents').window('close');
        jQuery('#win-personal-use-owners-list').window('close');
    })
    .fail(function (errorObj) {
        jQuery('#win-personal-use-rents').window('close');
        let msg = errorObj.getMessage();
        if (errorObj.is(TF.Rpc.ExceptionsList.PERSONAL_USE_COLLECTIONS_EXIST)) {
            let selectedRow = jQuery('#personal-use-tables').datagrid('getSelected');
            let selectedContract = jQuery('#contracts-tree').tree('getSelected');
            msg += ' - <a href="index.php?page=Collections.Home&c_id=' + selectedContract.id + '&fy=' + obj.year + '" target="_blank">Тук</a>';
        }
        jQuery.messager.alert('Грешка', msg, 'error');
    });
}
