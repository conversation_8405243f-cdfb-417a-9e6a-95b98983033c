function initWeighingNoteGrid(transaction_id) {
    jQuery('#weighing-note-table').datagrid({
        nowrap: true,
        singleSelect: false,
        pageSize: 30,
        fit: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?payments-rpc=weighing-note-grid',
        sortName: 'transaction_id',
        sortOrder: 'desc',
        border: false,
        idField: '',
        frozenColumns: [[
                {
                    field: 'ck',
                    checkbox: true
                },
            ]],
        columns: [[
                {
                    field: 'transaction_id',
                    title: '<b>Транзакция<br/>№</b>',
                    sortable: true,
                    width: 80,
                    align: 'center'
                }, {
                    field: 'c_num',
                    title: '<b>Договор</b>',
                    sortable: true,
                    width: 130,
                    align: 'center'
                }, {
                    field: 'owner',
                    title: '<b>Собственик</b>',
                    sortable: true,
                    width: 150,
                    align: 'center'
                }, {
                    field: 'rep_names',
                    title: '<b>Представител</b>',
                    sortable: true,
                    width: 150,
                    align: 'center'
                }, {
                    field: 'date',
                    title: '<b>Дата</b>',
                    sortable: true,
                    width: 80,
                    align: 'center'
                }, {
                    field: 'paid_from_text',
                    title: '<b>Изплащане на</b>',
                    sortable: false,
                    width: 150,
                    align: 'center'
                }, {
                    field: 'paid_in_text',
                    title: '<b>Изплащане чрез</b>',
                    sortable: false,
                    width: 150,
                    align: 'center'
                }, {
                    field: 'farming_year',
                    title: '<b>Стопанска година</b>',
                    sortable: true,
                    width: 150
                }
            ]],
        pagination: true,
        rownumbers: false,
        toolbar: [
            {
                id: 'btndownloadweighingnote',
                text: 'Изтегли',
                iconCls: 'icon-pdf',
                handler: function() {
                    var winDownload = jQuery('#win-download');
                    var downloadFile = jQuery('#btn-download-file');
                    var getChecked = jQuery('#weighing-note-table').datagrid('getChecked');

                    if (getChecked[0]) {
						var payment_id_string = '';

						for(var i=0; i<getChecked.length; i++)
						{
							if(payment_id_string == '')
							{
								payment_id_string = getChecked[i].transaction_id;
							}
							else {
								payment_id_string += ',' + getChecked[i].transaction_id;
							}
						}

                        TF.Rpc.Payments
                            .ExportPayment
                            .exportToPdfWeighingNote(payment_id_string)
                            .done(function (dataObj) {
                                winDownload.window('open');
                                _pathFile = dataObj.file_path;
                                var path = _pathFile;
                                _fileName = dataObj.file_name;
                                downloadFile.attr("href", path);
                            })
                            .fail(function (errorObj) {});
                    } else {
                        jQuery.messager.alert('Грешка', 'Моля изберете транзакция!', 'error');
                    }
                }
            }
        ],
        onBeforeLoad: function() {
            jQuery('#weighing-note-table').datagrid('clearChecked');
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}
