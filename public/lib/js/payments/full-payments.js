var unpaid_renta_natura_sum = 0;
const RKO_MANUAL_TEXT_OPTION = 0;
const RKO_REASON_MAX_LEN = 72;
const RKO_CASH_REASON_LEN = 235;

var PostPaymentOwner = new PostPaymentFields('owner');
var PostPaymentSender = new PostPaymentFields('sender'); 
var paymentsTable,ownerPaymentsTable;
var unpaid = 0;
let rowIndex = 1;
let old_outstanding_rent = 0;
let total_renta_by_contract = 0;

/**
 * [initPaymentFromMoneyPanel description]
 *
 * @param   {[type]}  farmingIdStr  [farmingIdStr description]
 *
 * @return  {[type]}                [return description]
 */
function initPaymentFromMoneyPanel(farmingIdStr) {
    unpaid = 0;
    clearRkoNumbers();
	var date = new Date();
	var todayDate = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();
	paymentsTable = jQuery('#contract-payments-tables');
	ownerPaymentsTable = jQuery('#contracts-owner-payments-tables');
    let showDeadMsg = false;
    let enableCombinedPayment = false;    
	if(paymentsTable.length) {
		var getSelected = paymentsTable.treegrid('getSelections');
        jQuery('#combine-payment-document-container').hide();

	}
	else {
		var getSelected = ownerPaymentsTable.datagrid('getSelections');
        var selectedOwner = jQuery('#owners-contracts-tree').tree('getSelected');

        jQuery('#pko_exists').hide();
        jQuery('#btn-add-payment-with-deduction').hide();

        if (selectedOwner != undefined) {
            if (selectedOwner.attributes.is_dead == true) {
                showDeadMsg = true;
            }
        }

        // Cannot generate one RKO for different farmings - each farming must have its separate RKO
        const hasDifferentFarmings = getSelected.length > 1 && getSelected.some((owner) => owner.farming_id !== getSelected[0].farming_id);
        
        if (getSelected.length > 1 && !hasDifferentFarmings) {
            enableCombinedPayment = true;
            jQuery('#combine-payment-document-container').show();
        } else {
            jQuery('#combine-payment-document-container').hide();
        }
	}

    jQuery('#payment-order-checkbox, #payment-order-checkbox-money-to-nat').change(function() {
        if (jQuery(this).is(':checked')) {
            jQuery('#rko-type-row').show();
            jQuery("#rko_standart").prop('checked',true);
        } else {
            jQuery('#rko-type-row').hide();
        }
    });


	jQuery("#payment-date-checkbox").prop('checked',false);
	jQuery("#payment-order-checkbox").prop('checked',false);
	jQuery("#payment-order-checkbox-money-to-nat").prop('checked',false);
	jQuery("#weighing-note-checkbox").prop('checked',false);
	jQuery("#np-weighing-note-checkbox").prop('checked',false);
    jQuery('#rko_card_input').show();
	jQuery("#collect-all-payment-amounts").prop('checked',false);

    jQuery("#representative-money-payment-option").hide();
    jQuery("#representative-money-payment-checkbox").prop('checked',false);

    jQuery('#payment-subjects-row').show();
    jQuery('#payment-subjects-text-row').hide();
    jQuery('#rko-type-row').show();

    jQuery('#payment-recipient-egn').parent('tr').show();
    jQuery('#payment-recipient-address').parent('tr').show();
    jQuery('#payment-recipient-proxy').parent('tr').show();
    jQuery('#payment-recipient-lk').parent('tr').show();

    jQuery('#post-payment-sender-head').hide();
    jQuery('#post-payment-owner-head').hide();
    PostPaymentOwner.hidePostPaymentFields();
    PostPaymentSender.hidePostPaymentFields();
    jQuery("#combine-payment-document").prop('checked',false);
    jQuery('#money-to-natura-container').hide();
    jQuery('#money-to-natura-weighing-note').hide();
    jQuery('#money-to-money').show();
    jQuery('#payment-subjects-text').val('');
    jQuery("#rko_standart").prop('checked',true);
    jQuery('#rko_type_declaration_row').hide();
    
    //total rent values involving payments
    var by_contract = 0;
    var charged = 0;
    var paid = 0;
    var pu_price_sum = 0;
  
    for (var i = 0; i < getSelected.length; i++)
	{
        by_contract += parseFloat(getSelected[i]['renta'].toString());
        if (getSelected[i]['charged_renta'] != null) {
            charged += parseFloat(getSelected[i]['charged_renta'].toString());
        }

        if(getSelected[i].hasOwnProperty('is_dead')  && getSelected[i]['is_dead'] == true) {
            showDeadMsg = true;
        }

        paid += parseFloat(getSelected[i]['paid_renta'].toString());
        unpaid += parseFloat(getSelected[i]['unpaid_renta'].toString());
        if(getSelected[i].personal_use_unpaid_treatments_arr){
            pu_price_sum += parseFloat((getSelected[i].personal_use_unpaid_treatments_arr.reduce((partialSum, a) => partialSum + parseFloat(a), 0) ?? 0).toString()) ;
        }
	}
    total_renta_by_contract = by_contract;

    if (showDeadMsg == true) {
        jQuery('#dead-owner-message').show();
    } else {
        jQuery('#dead-owner-message').hide();
    } 

    if(paymentsTable.length > 0 && getSelected.length == 1 && pu_price_sum > 0 && unpaid >0) {
        jQuery('#btn-add-payment-with-deduction').show();
    } else {
        jQuery('#btn-add-payment-with-deduction').hide();
    }

	//Зануляваме начислената рента в лева, ако няма начисление
    if(isNaN(charged)) {
		charged = 0;
	}

	jQuery('#payment-renta-by-contract').html(by_contract.toFixed(2) + ' лв.');
	jQuery('#payment-renta-charged').html(charged.toFixed(2) + ' лв.');
	jQuery('#payment-renta-paid').html(paid.toFixed(2) + ' лв.');
    jQuery('#personal-use-price-sum').html(pu_price_sum.toFixed(2) + ' лв.');
	jQuery('#payment-renta-unpaid').html(unpaid.toFixed(2) + ' лв.');
    resetPaymentRecipientFields();
    fillPayByMoneyOwnerInfo(getSelected[0]);

    if(getSelected.length == 1){
        if(getSelected[0].rep_names && getSelected[0].rep_names.trim().length > 0) {
            jQuery("#representative-money-payment-option").show();
            jQuery("#representative-money-payment-checkbox").change(function () {
                if(jQuery(this).is(':checked')) {
                    fillPayByMoneyRepInfo(getSelected[0]);
                } else {
                    fillPayByMoneyOwnerInfo(getSelected[0]);
                }
            });
        }
    }

    if (jQuery('#payment-amount > input').data().hasOwnProperty('numberbox')) {
        jQuery('#payment-amount > input').numberbox('reset');
    } else {
        jQuery('#payment-amount > input').val('');
    }

	jQuery('#payment-type-money > input').off('change').change(function() {
		if (jQuery('#payment-type-money > input').is(':checked') == true) {

			jQuery("#payment-date-checkbox").prop('checked',false);
            clearRkoNumbers();
            jQuery('#payment-amount-text').numberbox('reset');
            jQuery('#payment-natura-price-text').numberbox('reset');
            jQuery('#payment-natura-amount-text').numberbox('reset');
            jQuery('#payment-order-checkbox').prop('checked', true);
            jQuery('#payment-order-checkbox-money-to-nat').prop('checked', false);
			jQuery("#weighing-note-checkbox").prop('checked',false);
			jQuery("#np-weighing-note-checkbox").prop('checked',false);


            if (jQuery('#payment-method-cash > input').is(':checked') == true) {
                jQuery('#payment-subjects-row').show();
            } else if(jQuery('#payment-method-post-order > input').is(':checked') == true ) {
                PostPaymentOwner.showPostPaymentFields();
                PostPaymentSender.showPostPaymentFields();
            } else {
                showRkoTextField(true);
            }

			jQuery('#money-to-natura-container').hide();
            jQuery('#money-to-natura-weighing-note').hide();
			jQuery('#money-to-money').show();

			jQuery('#win-add-payment').window('resize', {
				height: jQuery('#btn-add-payment').offset().top - jQuery('#addPaymentBeginningMarker').offset().top + 75
			});
			jQuery('#win-add-payment').window('center');
            jQuery('#payment-method-cash > input').trigger('change');
            jQuery('#payment-method-bank > input').trigger('change');
            jQuery('#payment-method-post-order > input').trigger('change');
		}

		showRkoTextField();
	});

	jQuery('#payment-type-natura > input').off('change').change(function() {

		if (jQuery('#payment-type-natura > input').is(':checked') == true) {

            jQuery('#payment-container').empty();
            rowIndex = 1;
            old_outstanding_rent = 0;
            addRow();

			jQuery("#payment-date-checkbox").prop('checked',false);
            clearRkoNumbers();
            jQuery('#payment-order-checkbox').prop('checked', false);
            jQuery('#payment-order-checkbox-money-to-nat').prop('checked', false);
            jQuery('#payment-subjects-row').hide();
            showRkoTextField(true);
            jQuery('#payment-amount-text').numberbox('reset');
            jQuery('#payment-natura-price-text').numberbox('reset');
            jQuery('#payment-natura-amount-text').numberbox('reset');
			jQuery("#weighing-note-checkbox").prop('checked',false);
			jQuery("#np-weighing-note-checkbox").prop('checked',false);
            jQuery("#weighing-note-checkbox-money-to-nat").prop('checked',true);

            jQuery('#payment-natura-price-text').textbox('clear');
            jQuery('#payment-natura-amount-text').textbox('clear');

			jQuery('#money-to-money').hide();
			jQuery('#money-to-natura-container').show();
            jQuery('#money-to-natura-weighing-note').show();
            
            jQuery('#rko-type-row').hide();
            jQuery('#rko_type_declaration_row').hide();
            jQuery("#rko_type_declaration").prop('checked',false);

            jQuery('#payment-recipient-egn').parent('tr').show();
            jQuery('#payment-recipient-address').parent('tr').show();
            jQuery('#payment-recipient-proxy').parent('tr').show();
            jQuery('#payment-recipient-lk').parent('tr').show();
            jQuery('#post-payment-owner-head').hide();

            var px = 0;
            if (jQuery('#win-add-payment').height() > 600) {
                px = -150;
            }

			jQuery('#win-add-payment').window('resize', {
				height: jQuery('#btn-add-payment').offset().top - jQuery('#addPaymentBeginningMarker').offset().top + 230 + px
			});

            PostPaymentOwner.hidePostPaymentFields();
            PostPaymentSender.hidePostPaymentFields();

			jQuery('#win-add-payment').window('center');
		}
	});

	jQuery('#bank-account-row').hide();
	jQuery('#orderer-bank-account-row').hide();

	jQuery('#payment-method-cash > input').off('change').change(function() {
		if (jQuery('#payment-method-cash > input').is(':checked') == true) {
            jQuery('label[for="payment-order-checkbox"]').html(
                "Генерирай разходен касов ордер"
            );
			jQuery('#bank-account-row').hide();
            jQuery('#orderer-bank-account-row').hide();
            jQuery('#rkoNumberingFields').show();
            jQuery('#payment-subjects-row').show();
            jQuery('#payment-subjects-text-row').hide();
            jQuery('#post-payment-owner-head').hide();
            jQuery('#post-payment-sender-head').hide();
            jQuery('#rko-type-row').show();

            jQuery('#payment-recipient-egn').parent('tr').show();
            jQuery('#payment-recipient-address').parent('tr').show();
            jQuery('#payment-recipient-proxy').parent('tr').show();
            jQuery('#payment-recipient-lk').parent('tr').show();

            jQuery("#payment-date-checkbox").prop("checked", false);
            jQuery('#payment-order-checkbox').prop('checked', true);
            jQuery("#weighing-note-checkbox-money-to-nat").prop('checked',false);


            var px = 0;
            if (jQuery('#win-add-payment').height() > 660) {
                px = -300;
            }

            if(getSelected.length == 1 && pu_price_sum > 0 && unpaid >0) {
                jQuery('#pko_exists').show();
                jQuery('#btn-add-payment-with-deduction').show();
            }
            
            jQuery('#win-add-payment').window('resize', {
                height: 710
            });

            jQuery('#win-add-payment').window('center');

            PostPaymentOwner.hidePostPaymentFields();
            PostPaymentSender.hidePostPaymentFields();
            if (enableCombinedPayment === true) {
                jQuery('#combine-payment-document-container').show();
            }
        }

		showRkoTextField();
    });

    jQuery('#payment-method-bank > input').off('change').change(function() {
        if (jQuery('#payment-method-bank > input').is(':checked') == true) {
            jQuery('label[for="payment-order-checkbox"]').html(
                "Генерирай платежно нареждане"
            );
            clearRkoNumbers();

            jQuery('#bank-account-row').show();
            jQuery('#orderer-bank-account-row').show();
            jQuery('#payment-order-checkbox').prop('checked', true);
            jQuery('#payment-order-checkbox-money-to-nat').prop('checked', false);
            jQuery('#rkoNumberingFields').hide();
            jQuery('#payment-subjects-row').show();
            jQuery('#post-payment-owner-head').hide();
            jQuery('#post-payment-sender-head').hide();

            jQuery('#payment-subjects-text-row').hide();
            jQuery('#payment-recipient-egn').parent('tr').show();
            jQuery('#payment-recipient-address').parent('tr').show();
            jQuery('#payment-recipient-proxy').parent('tr').show();
            jQuery('#payment-recipient-lk').parent('tr').show();
            jQuery('#rko-type-row').hide();
   
            var px = 0;
            
            if (jQuery('#win-add-payment').height() > 660) {
                px = -300;
            }
            jQuery('#pko_exists').hide();
            jQuery('#btn-add-payment-with-deduction').hide();

            jQuery('#win-add-payment').window('resize', {
				height: 700
			});

			jQuery('#win-add-payment').window('center');
            
            showRkoTextField();

            PostPaymentOwner.hidePostPaymentFields();
            PostPaymentSender.hidePostPaymentFields();
            if (enableCombinedPayment === true) {
                jQuery('#combine-payment-document-container').show();
            }
		}
	});

    jQuery('#payment-method-post-order > input').off('change').change(function() {
        if (jQuery('#payment-method-post-order > input').is(':checked') == true) {
            jQuery('label[for="payment-order-checkbox"]').html(
                "Генерирай пощенски запис"
            );

            clearRkoNumbers();
            jQuery('#post-payment-owner-head').show();
            jQuery('#post-payment-sender-head').show();

            jQuery('#bank-account-row').show();
            jQuery('#orderer-bank-account-row').hide();
            jQuery('#payment-order-checkbox').prop('checked', true);
            jQuery('#payment-order-checkbox-money-to-nat').prop('checked', false);
            jQuery('#rkoNumberingFields').hide();
            jQuery('#payment-subjects-row').hide();
            jQuery('#payment-subjects-text-row').hide();
            
            jQuery('#payment-recipient-egn').parent('tr').hide();
            jQuery('#payment-recipient-address').parent('tr').hide();
            jQuery('#payment-recipient-proxy').parent('tr').hide();
            jQuery('#payment-recipient-lk').parent('tr').hide();
            jQuery('#rko-type-row').hide();

            jQuery('#pko_exists').hide();
            jQuery('#btn-add-payment-with-deduction').hide();

            var senderData = getSelected[0].farm_post_payment_fields;
            var ownerData = getSelected[0].owner_post_payment_fields;

            PostPaymentSender.showPostPaymentFields(senderData);
            PostPaymentOwner.showPostPaymentFields(ownerData);

            showRkoTextField(false);
			jQuery('#win-add-payment').window('resize', {
				height: jQuery('#btn-add-payment').offset().top - jQuery('#addPaymentBeginningMarker').offset().top + 79
			});
			jQuery('#win-add-payment').window('center');
            jQuery('#combine-payment-document-container').hide();
        }
    });


	jQuery('#payment-type-money-radio').prop('checked',true);
    jQuery('#payment-method-cash-radio').prop('checked',true);

    jQuery('#payment-amount > input').numberbox({
        min: 0,
        precision: 2,
        required: true,
        missingMessage: 'Това поле е задължително!',
        value: unpaid,
        onChange: function(newValue, oldValue) {
            const getSelected = paymentsTable.length ? paymentsTable.treegrid('getSelections') : ownerPaymentsTable.datagrid('getSelections');

            if (newValue < unpaid  && getSelected.length > 1) {
                jQuery('#contract-payment-warning').show();
            } else {
                jQuery('#contract-payment-warning').hide();
            }

            clearRkoNumbers();
        },
        inputEvents:jQuery.extend({},jQuery.fn.numberbox.defaults.inputEvents,{
            keyup:function(){
                let newValue = this.value;
                var getSelections = jQuery('#contract-payments-tables').treegrid('getSelections');
                jQuery('#sumAfterDeduction').text((parseFloat(newValue - getSelections[0].personal_use_unpaid_treatments_arr.reduce((partialSum, a) => partialSum + parseFloat(a), 0)).toFixed(2)));
            }
        })
    });

	jQuery('#payment-date > input').datebox({
		required: true,
		value: todayDate,
        editable: false
	});

    jQuery('#payment-orderer-bank-account > input').combobox({
        url: 'index.php?farming-rpc=farming-iban',
        valueField: 'value',
        textField: 'text',
        multiple: false,
        editable: false,
        rpcParams: [farmingIdStr],
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#payment-subjects-combobox').combobox({
        url: 'index.php?common-rpc=payment-subjects-combobox',
        valueField: 'id',
        textField: 'name',
        multiple: false,
        editable: false,
        rpcParams: [{
        	selected: true
        }],
        onChange: function(newValue, oldValue) {
            const reason = jQuery('#payment-subjects-text').val();
            const { methodCash, methodBank } = getPaymentMethods();

            if (newValue === RKO_MANUAL_TEXT_OPTION || isReasonLengthExceeded(methodCash, methodBank, reason)) {
                showRkoTextField(true);
            } else {
                jQuery('#payment-subjects-text-row').hide();
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery("#combine-payment-document").change(function() {
        showRkoTextField();
        clearRkoNumbers();
    });

    jQuery('#payment-subjects-text').on('input', function() {
        const { methodCash, methodBank } = getPaymentMethods();
        const textLength = jQuery(this).val().length;

        if ((methodCash && textLength > RKO_CASH_REASON_LEN) || (methodBank && textLength > RKO_REASON_MAX_LEN)) {
            jQuery("#rko-text-warning").show();
        } else {
            jQuery("#rko-text-warning").hide();
        }
    });

    initializeCheckboxFields();

    // Инициализираме логиката за първия ред
    initializeRowLogic(1);
    jQuery(`#payment-add-row-1`).linkbutton('disable');
}

            function addRow() {
                if(rowIndex > 1) {
                    jQuery(`#payment-natura-type--${rowIndex} input`).textbox('readonly', true);
                    jQuery(`#payment-natura-price--${rowIndex} input`).textbox('readonly', true);
                    jQuery(`#payment-natura-amount--${rowIndex} input`).textbox('readonly', true);
                }

                var newRow = `
                <div class="payment-row" id="payment-row-${rowIndex}" style="margin-bottom: 10px;">
                    <input id="payment-natura-type-${rowIndex}" class="easyui-combobox" style="width:105px;" placeholder="Тип рента">
                    <input id="payment-natura-price-${rowIndex}" class="easyui-numberbox" style="width:60px;" placeholder="Цена">
                    <input id="payment-natura-amount-${rowIndex}" class="easyui-numberbox" style="width:90px;" placeholder="Количество">
                    <input id="payment-natura-total-value-${rowIndex}" class="easyui-numberbox" style="width:90px;" placeholder="Стойност">
                    <a href="javascript:void(0)" id="payment-add-row-${rowIndex}" onClick="addRow()" class="easyui-linkbutton" data-options="iconCls:'icon-add'" data-row-index="${rowIndex}"></a>
                    <a href="javaScript:void(0)" id="payment-remove-row-${rowIndex}" onClick="removeRow(${rowIndex})" class="easyui-linkbutton" style="display: none;" data-options="iconCls:'icon-cancel'" data-row-index="${rowIndex}"></a>
                    </div>`;
                
                jQuery('#payment-container').append(newRow);
                initializeRowLogic(rowIndex);

                if(rowIndex > 1) {
                    var prevRow = rowIndex - 1;
                    jQuery(`#payment-add-row-${prevRow}`).hide();
                    jQuery(`#payment-remove-row-${prevRow}`).show();
                    jQuery(`#payment-natura-type-${prevRow}`).combobox('disable');
                    jQuery(`#payment-natura-price-${prevRow}`).numberbox('disable');
                    jQuery(`#payment-natura-amount-${prevRow}`).numberbox('disable');
                    jQuery(`#payment-natura-total-value-${prevRow}`).numberbox('disable');
                }

                // Initialize the EasyUI components for the new row
                jQuery.parser.parse(`#payment-row-${rowIndex}`);

                jQuery(`#payment-add-row-${rowIndex}`).linkbutton('disable');
                rowIndex++;
            }

            function removeRow(index) {
                jQuery(`#payment-row-${index}`).remove();
            }

            function initializeRowLogic(index) {
                jQuery(`#payment-natura-type-${index}`).combobox({
                    url: 'index.php?common-rpc=renta-types-combobox',
                    textField: 'name',
                    valueField: 'id',
                    editable: false,
                    onSelect: function(newType){
                        let oldType = jQuery(`#payment-natura-type-${index}`).combobox('getValue');
                        var selectedNaturaTypesArr = getSelectedNaturaTypes();
                        var isNaturaTypeSelected = jQuery.inArray(String(newType.id), selectedNaturaTypesArr);

                        if(isNaturaTypeSelected !== -1) {
                            jQuery.messager.alert('Внимание', 'Вече има въведена натура от избраният тип. Моля изберете друг тип натура!', 'warning');

                        setTimeout(function(){
                            jQuery(`#payment-natura-type-${index}`).combobox('select', oldType);
                        },0);

                            return false;
                        }
                    },
                    onChange: function(newType){
                        var data = jQuery(`#payment-natura-type-${index}`).combobox('getData');
                        var natura_amount = jQuery(`#payment-natura-amount-${index}`);
                        var natura_price = jQuery(`#payment-natura-price-${index}`);
                        var unit_value = 0;

                        //clear amount and price
                        natura_amount.numberbox('clear');
                        natura_price.numberbox('clear');

                        jQuery.each(data, function(key, value) {
                            if(value.unit_value && value.id == newType) {
                                unit_value = value.unit_value;
                                natura_price.numberbox('setValue', unit_value);
                            }
                        });

                        var isRequiredValue =  0 != unit_value ? true : false
                        natura_price.numberbox({required: isRequiredValue});
                        natura_amount.numberbox({required: isRequiredValue});
                    },
                    loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                    loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
                });

                jQuery(`#payment-natura-price-${index}`).numberbox({
                    min: 0.1,
                    precision: 2,
                    required: false,
                    missingMessage: 'Това поле е задължително!',
                    onChange: function(newPrice, oldPrice) {
                        newPrice = parseFloat(newPrice);
                        oldPrice = parseFloat(oldPrice);
                        var total = jQuery(`#payment-natura-total-value-${index}`);
                        var natura_amount = jQuery(`#payment-natura-amount-${index}`);
                        var outstanding_rent = index !== 1 ? calculateOutstandingRent() : unpaid;

                        unpaid_renta_natura_sum = newPrice != 0 && outstanding_rent > 0 ? parseFloat((outstanding_rent / newPrice).toFixed(3)) : 0;

                        if(unpaid_renta_natura_sum != 0) {
                            var total_value = parseFloat((unpaid_renta_natura_sum * newPrice).toFixed(2));
                            natura_amount.numberbox('setValue', unpaid_renta_natura_sum);
                            total.numberbox('setValue', total_value);
    
                            cacheOutstandingRent();
                        }
                    }
                });

                jQuery(`#payment-natura-amount-${index}`).numberbox({
                    min: 0.001,
                    precision: 3,
                    required: false,
                    missingMessage: 'Това поле е задължително!',
                    onChange: function(newAmount, oldAmount) {
                        newAmount = parseFloat(newAmount);
                        oldAmount = parseFloat(oldAmount);
                        
                        if(newAmount === oldAmount) {
                            return;
                        }

                        var natura_price = parseFloat(jQuery(`#payment-natura-price-${index}`).numberbox('getValue').toString());
                        var total = jQuery(`#payment-natura-total-value-${index}`);
                        
                        var total_value =  parseFloat((newAmount * natura_price).toFixed(2)); 

                        total.numberbox('setValue', total_value);
                        cacheOutstandingRent();
                    }
                });

                jQuery(`#payment-natura-total-value-${index}`).numberbox({
                    min: 0,
                    precision: 2,
                    readonly: true,
                    onChange: function(newAmount) {
                        newAmount = parseFloat(newAmount);

                        var btnStatus = newAmount > 0 ? 'enable' : 'disable';
                        jQuery(`#payment-add-row-${index}`).linkbutton(btnStatus);
                    }
                });
            }

            function cacheOutstandingRent() {
                // This logis is needed when change the value of natura-amount or natura-price
                old_outstanding_rent = calculateOutstandingRent();
            }

            function calculateOutstandingRent() {
                var paidRent = 0
                jQuery('.payment-row').each(function () {
                    let rowId = jQuery(this).attr('id').split('-')[2];
                    let price = jQuery(`#payment-natura-price-${rowId}`).textbox('getValue');
                    let amount = jQuery(`#payment-natura-amount-${rowId}`).textbox('getValue');
                    price = parseFloat(price);
                    amount = parseFloat(amount);

                    if(amount && price) {
                        paidRent +=  parseFloat((amount * price).toFixed(2));
                    }
                });
        
                return unpaid - paidRent;
            }


            function getNaturaData() {
                let data = [];
                jQuery('.payment-row').each(function () {
                    let rowId = jQuery(this).attr('id').split('-')[2];
                    let type = jQuery(`#payment-natura-type-${rowId}`).textbox('getValue');
                    let price = jQuery(`#payment-natura-price-${rowId}`).textbox('getValue');
                    let amount = jQuery(`#payment-natura-amount-${rowId}`).textbox('getValue');

                    if(!["",0,'0'].includes(type)){
                        data.push({
                            type,
                            price,
                            amount
                        });
                    }
                });
        
                return data;
            }

            function getSelectedNaturaTypes() {
                const rowArr = jQuery('.payment-row');
                    return Array.from(rowArr).map((row) =>  {
                        let rowId = row.id.split('-')[2]
                        return jQuery(`#payment-natura-type-${rowId}`).combobox('getValue')
                    }
                );
            }

/**
 * [fillPayByMoneyOwnerInfo description]
 *
 * @param   {[type]}  data  [data description]
 *
 * @return  {[type]}        [return description]
 */
function fillPayByMoneyOwnerInfo(data) {
    jQuery('#payment-recipient > input').val(data.owner_names);
    jQuery('#payment-bank-account > input').val(data.iban);
    jQuery('#payment-recipient-egn > input').val(data.egn_eik);
    jQuery('#payment-recipient-address > input').val(data.address);
    jQuery('#payment-recipient-lk > input').val(
        (data.lk_nomer && data.lk_nomer.length > 0 ? data.lk_nomer : '') +
        (data.lk_nomer && data.lk_izdavane && data.lk_izdavane.length > 0 ? ', ' + data.lk_izdavane : '')
    );
}

/**
 * [fillPayByMoneyRepInfo description]
 *
 * @param   {[type]}  data  [data description]
 *
 * @return  {[type]}        [return description]
 */
function fillPayByMoneyRepInfo(data) {
    jQuery('#payment-recipient > input').val(data.rep_names);
    jQuery('#payment-recipient-egn > input').val(data.rep_egn);
    jQuery('#payment-bank-account > input').val(data.rep_iban);
    jQuery('#payment-recipient-address > input').val(data.rep_address);
    jQuery('#payment-recipient-lk > input').val(
        (data.rep_lk && data.rep_lk.length > 0 ? data.rep_lk : '') +
        (data.rep_lk && data.rep_lk_izdavane && data.rep_lk_izdavane.length > 0 ? ', ' + data.rep_lk_izdavane : '')
    );
}

/**
 * [initializeCheckboxFields description]
 *
 * @return  {[type]}  [return description]
 */
function initializeCheckboxFields() {
    var method_chash = jQuery('#payment-method-cash > input').is(":checked"),
        method_bank  = jQuery('#payment-method-bank > input').is(":checked"),
        type_money   = jQuery('#payment-type-money > input').is(":checked"),
        type_natura  = jQuery('#payment-type-natura > input').is(":checked");


        if (method_chash && type_money && !method_bank && !type_natura) {
            jQuery('#payment-order-checkbox').prop('checked', true);
        }

       showRkoTextField();
}

/**
 * [showRkoTextField description]
 *
 * @param   {[type]}  force  [force description]
 * @param   {[type]}  false  [false description]
 *
 * @return  {[type]}         [return description]
 */
function showRkoTextField(force = false) {
    const $ = jQuery;
    let validationPassed = false;
    let contractsText = "";
    const { methodCash, methodBank } = getPaymentMethods();
    const rkoManualSelected  = parseInt(jQuery('#payment-subjects-combobox').combobox('getValue')) === RKO_MANUAL_TEXT_OPTION;

    let defaultReason = jQuery('#payment-subjects-combobox').combobox('getText');
    if (rkoManualSelected === true) {
        defaultReason = "";
    } 

    jQuery("#rko-text-warning").hide();
    jQuery('#payment-subjects-text').val("");

    const getSelected = paymentsTable.length ? paymentsTable.treegrid('getSelections') : ownerPaymentsTable.datagrid('getSelections');
    const selectedContract = jQuery('#contracts-tree').tree('getSelected');

    if(paymentsTable.length) {
        contractsText = selectedContract.attributes.c_num;
    } else if (getSelected.length > 1 && jQuery('#combine-payment-document').is(':checked')) {
        contractsText = getSelected.map(element => element.c_num).join(', ');
    } else {
        contractsText = getSelected[0].c_num;
    }

    const reason = `${defaultReason} ${contractsText} за ${jQuery('#search-year').combobox('getText')}`;

    if (force !== true) {
        if (rkoManualSelected || isReasonLengthExceeded(methodCash, methodBank, reason)) {
            validationPassed = true;
        }
    }

    if (force || validationPassed) {
        if (isReasonLengthExceeded(methodCash, methodBank, reason)) {
            jQuery("#rko-text-warning").show();
        }
        
        jQuery('#payment-subjects-text').val(reason);    
        jQuery('#payment-subjects-text-row').show();
    } else {
        jQuery('#payment-subjects-text').val(reason);    
        jQuery('#payment-subjects-text-row').hide();
    }
}

function getPaymentMethods() {
    const methodCash = jQuery('#payment-method-cash > input').is(":checked");
    const methodBank = jQuery('#payment-method-bank > input').is(":checked");
    const postPayment = jQuery('#payment-method-post-order > input').is(":checked");
    
    return { methodCash, methodBank, postPayment };
}

function isReasonLengthExceeded(methodCash, methodBank, reason) {
    return (methodCash && reason.length > RKO_CASH_REASON_LEN) || (methodBank && reason.length > RKO_REASON_MAX_LEN);
}

function validateNewPayment(deduction = false) {
    var object = getPaymentInputObject();
    var data = getPaymentInputData();

    data.deduction = deduction;

    if(total_renta_by_contract === 0 && data.payment_amount <= 0) {
        jQuery.messager.alert('Грешка','Моля въведете сума за изплащане','warning');
        return;
    }

    if(deduction === true){
        for (var i = 0; i < object.owner_array.length; i++) {
            for (var j = 0; j < object.owner_array[i].personal_use.length; j++) {
                if (object.owner_array[i].personal_use[j].renta_type === '' || object.owner_array[i].personal_use[j].renta_type === null) {
                    jQuery.messager.alert('Грешка', 'Операцията няма да бъде извършена, защото няма въведена култура за лично ползване. Моля въведете култура от панела за Лично ползване.','warning');
                    return;
                }
            }
        }
    }

    var paymentsTable = jQuery('#contract-payments-tables');
    var ownerPaymentsTable = jQuery('#contracts-owner-payments-tables');

	if (data.payment_type_money)
	{
		if (data.payment_amount && data.payment_date)
		{
			TF.Rpc.Payments
		    .AddPayment
		    .savePayment(object, data)
		    .done(function (dataObj) {
		    	completeSavePayment(dataObj);

		    	jQuery('#win-add-payment').window('close');

		    	if(paymentsTable.length > 0) {
		    		paymentsTable.treegrid('loadRpc');
		    	}
		    	else {
		    		ownerPaymentsTable.datagrid('loadRpc');
		    	}

		    })
		    .fail(function (errorObj) {
		        if(errorObj && errorObj.getMessage()) {
                    jQuery.messager.alert('Грешка',errorObj.getMessage(),'warning');
                }
		    });
		} else {
           jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.REQUIRED_FIELDS.message ,'error');
        }
	}
	else if (data.payment_type_natura)
	{
        var canSendData = true;

        if(data.payments_natura.length === 0) {
            jQuery.messager.alert('Грешка', 'Моля, въведете поне една натура.', 'warning');
            canSendData = false;
            return;
        }

        data.payments_natura.forEach(function (natura) {
            if (!['',0,'0'].includes(natura.type.trim())  && (natura.price.trim() === '' || natura.amount.trim() === '')) {
                jQuery.messager.alert('Грешка', 'Моля, попълнете всички задължителни полета.', 'warning');
                canSendData = false;
                return;
            }   
        });

        if(canSendData){
            {
                TF.Rpc.Payments
                .AddPayment
                .savePayment(object, data)
                .done(function (dataObj) {
                    completeSavePayment(dataObj);

                    jQuery('#win-add-payment').window('close');
    
                    if(paymentsTable.length > 0) {
                        paymentsTable.treegrid('loadRpc');
                    }
                    else {
                        ownerPaymentsTable.datagrid('loadRpc');
                    }
    
                })
                .fail(function (errorObj) {
                    jQuery.messager.alert('Грешка',errorObj.getMessage(),'warning');
                });
            }
        }
	}
}

function getPaymentInputData() {
    var data = {};
    let contractData = jQuery('#contracts-tree').tree('getSelected');
    //data from add payment form
    var payment_type_money = jQuery('#payment-type-money > input').is(':checked');
    var payment_type_natura = jQuery('#payment-type-natura > input').is(':checked');
    var payment_amount = jQuery('#payment-amount > input').val();
    var payment_date = jQuery('#payment-date > input').datebox('getValue');
    let collections_recipient_name = jQuery('#collections_recipient_name').val();
    let collections_recipient_address = jQuery('#collections_recipient_address').val();
    let collections_recipient_egn = jQuery('#collections_recipient_egn').val();
    let collections_recipient_lk = jQuery('#collections_recipient_lk').val();

    data.payment_type_money = payment_type_money;
    data.payment_type_natura = payment_type_natura;
    data.payment_amount = payment_amount;
    data.payment_date = payment_date;
    data.payments_natura = getNaturaData();
    data.collections_recipient_name = collections_recipient_name;
    data.collections_recipient_address = collections_recipient_address;
    data.collections_recipient_egn = collections_recipient_egn;
    data.collections_recipient_lk = collections_recipient_lk;
    data.collections_rko_text = '';
    
    let rkoData = jQuery('#payment-subjects-combobox').combobox('getData');
    for(let i = 0; i < rkoData.length; i++){
        if(rkoData.id === 1){
            data.collections_rko_text = rkoData.fulltext;
        }
    }

    if(jQuery('#contract-payments-tables').length){
        let contractData = jQuery('#contracts-tree').tree('getSelected');
        let ownerData = jQuery('#contract-payments-tables').treegrid('getSelected');
        data.farming_id = contractData.attributes.farming_id;
        data.treatments_amount = ownerData.personal_use_treatments_sum;
        data.collections_rko_text = data.collections_rko_text.replace(/\[\[nomer_na_dogovor\]\]/g, contractData.attributes.c_num).replace(/\[\[stopanska_godina\]\]/g, jQuery("#search-year").combobox("getText"));
    }
    
    data.payment_method_cash = jQuery('#payment-method-cash > input').is(':checked');
    data.payment_method_bank = jQuery('#payment-method-bank > input').is(':checked');
    data.payment_method_post_order = jQuery('#payment-method-post-order > input').is(':checked');
    
    data.payment_order = jQuery('#payment-order > input').is(':checked');
    data.weighing_note = jQuery('#weighing-note > input').is(':checked');
    data.payment_recipient = jQuery('#payment-recipient > input').val();
    data.payment_recipient_egn = jQuery('#payment-recipient-egn > input').val();
    data.payment_recipient_proxy = jQuery('#payment-recipient-proxy > input').val();
    data.payment_recipient_address = jQuery('#payment-recipient-address-text').val();
    data.payment_recipient_lk = jQuery('#payment-recipient-lk-text').val();
    data.payment_bank_account = jQuery('#payment-bank-account > input').val();
    data.payment_numbering = getPaymentNumbering();
    data.post_payment_sender_fields = PostPaymentSender.getPostPaymentValues();

    return data;
}

/**
 * [getPaymentInputObject description]
 *
 * @return  {[type]}  [return description]
 */
function getPaymentInputObject() {
    var owner_array = [];
    var ownerData = [];
    var object = {};
    var i = 0;

    var paymentsTable = jQuery('#contract-payments-tables');
    var ownerPaymentsTable = jQuery('#contracts-owner-payments-tables');

    if(paymentsTable.length){
        ownerData = paymentsTable.treegrid('getSelections');
        let contractData = jQuery('#contracts-tree').tree('getSelected');

        for (i = 0; i < ownerData.length; i++) {
            var obj = {};
            obj.owner_id = ownerData[i].owner_id;
            obj.charged_renta = ownerData[i].charged_renta;
            obj.renta = ownerData[i].unpaid_renta;
            obj.paid_renta = ownerData[i].paid_renta;
            obj.contract_id = ownerData[i].contract_id;
            obj.annex_id = contractData.attributes.annex_id;
            obj.path = ownerData[i].path;
            obj.is_heritor = ownerData[i].is_heritor;
            obj.personal_use = [];

            for (let key in ownerData[i].personal_use_nat_type_id) {
                obj.personal_use[key] = {};
                obj.personal_use[key].renta_type = ownerData[i].personal_use_nat_type_id[key];
                obj.personal_use[key].renta_type_name = ownerData[i].personal_use_nat_types_names_arr[key];
                obj.personal_use[key].personal_use_paid_treatments = ownerData[i].personal_use_paid_treatments_arr[key];
                obj.personal_use[key].personal_use_unpaid_treatments = ownerData[i].personal_use_unpaid_treatments_arr[key];
            }

            obj.payment_meta_data = getPaymentMetaData(ownerData[i]);
            obj.uuid = ownerData[i].uuid;
            
            owner_array.push(obj);
        }
    }
    else {
        ownerData = ownerPaymentsTable.datagrid('getSelections');

        for (i = 0; i < ownerData.length; i++) {

            var ownerIdRenta = ownerData[i].id_renta_natura;
            var ownerPathRenta = ownerData[i].path_renta_natura;
            var farmingId = ownerData[i].farming_id;
            //if exist owners
            if(ownerIdRenta != undefined) {
                //put in owner_array all users who are owners
                jQuery.each(ownerIdRenta, function(key, value) {
                    var obj = {};
                    obj.owner_id = key;
                    obj.contract_id = value.contract_id;
                    obj.owner_area = value.area;
                    obj.charged_renta = value.charged_renta;
                    obj.renta = value.renta;
                    obj.unpaid_renta = value.unpaid_renta;
                    obj.unpaid_renta_nat = value.unpaid_renta_nat;
                    obj.is_heritor = false;
                    obj.farming_id = farmingId;
                    obj.payment_meta_data = getPaymentMetaData(ownerData[i], 'owner_payments');
                    obj.uuid = value.uuid;
                    owner_array.push(obj);
                });
            }

            //if exist heritors
            if(ownerPathRenta != undefined) {
                //put in owner_array all users who are heritors
                jQuery.each(ownerPathRenta, function(key, value) {
                    var obj = {};
                    obj.owner_id = value.owner_id;
                    obj.contract_id = value.contract_id;
                    obj.owner_area = value.area;
                    obj.charged_renta = value.charged_renta;
                    obj.renta = value.renta;
                    obj.unpaid_renta = value.unpaid_renta;
                    obj.unpaid_renta_nat = value.unpaid_renta_nat;
                    obj.path = key;
                    obj.is_heritor = true;
                    obj.farming_id = farmingId;
                    obj.payment_meta_data = getPaymentMetaData(ownerData[i], 'owner_payments');
                    obj.uuid = value.uuid;
                    owner_array.push(obj);
                });
            }
        }

        object.payment_type = 'owner_payments';
    }

    object.rko_key = 'uuid'; // generate rko numbers by uuid
    object.owner_array = owner_array;
    object.year = jQuery('#search-year').combobox('getValue');
    object.payment_reduce = payment_reduce;
    object.post_payment_owner_fields = PostPaymentOwner.getPostPaymentValues();
    object.combine_payment_document = jQuery('#combine-payment-document').is(':checked');

    return object;
}

function getPaymentMetaData(data, payment_type = null) { 
    let payment_meta_data = {};

    payment_meta_data.payment_area = data.owner_area;
    payment_meta_data.plots_data = data.plots_contracts_area_array_gids;

    payment_meta_data.personal_use = [];

    for (let key in data.personal_use_nat_type_id) {
        payment_meta_data.personal_use[key] = {};
        payment_meta_data.personal_use[key].renta_type = data.personal_use_nat_type_id[key];
        payment_meta_data.personal_use[key].renta_type_name = data.personal_use_nat_types_names_arr[key];
        payment_meta_data.personal_use[key].personal_use_paid_treatments = data.personal_use_paid_treatments_arr[key];
        payment_meta_data.personal_use[key].personal_use_unpaid_treatments = data.personal_use_unpaid_treatments_arr[key];
    }

    return payment_meta_data;
}

/**

 * @param   {int}  index      
 *
 * @return  {void}             
 */
function clearRkoNumbering(index) {
    jQuery(`#rko-${index}`).val('');
}

/**
 * [clearRkoNumbers description]
 *
 * @return  {[type]}  [return description]
 */
function clearRkoNumbers() {
    jQuery('#rko-numbering').html('');
}

/**
 * [resetPaymentRecipientFields description]
 *
 * @return  {[type]}  [return description]
 */
function resetPaymentRecipientFields() {
    jQuery('#payment-recipient > input').val('');
    jQuery('#payment-bank-account > input').val('');
    jQuery('#payment-recipient-egn > input').val('');
    jQuery('#payment-recipient-address > input').val('');
    jQuery('#payment-recipient-lk > input').val('');
}

/**
 * [generateRkoNumbers description]
 *
 * @return  {[type]}  [return description]
 */
function generateRkoNumbers() {

    var payment_by_money = (jQuery('#payment-type-money-radio').is(':checked') == true);
    var payment_by_natura = (jQuery('#payment-type-natura-radio').is(':checked') == true);

    if (payment_by_money) {
        var payment_amount = jQuery('#payment-amount > input').val();
        if (payment_amount == '' || payment_amount == 0) {
            return jQuery.messager.alert('Внимание', 'Моля въведете сума за изплащане.', 'warning');
        }
    }

    if (payment_by_natura) {
        var price = jQuery('#payment-natura-price-text').val();
        var amount = jQuery('#payment-natura-amount-text').val();
        if (price === '' || amount === '') {
            return jQuery.messager.alert('Внимание', 'Моля въведете ед.цена и количество', 'warning');
        }
    }
    if (payment_by_money || payment_by_natura) {
        var object = getPaymentInputObject();
        var data = getPaymentInputData();

        TF.Rpc.Payments.AddPayment.generateRkoNumbers(object, data)
        .done(function (data) {
            generateNumberingTemplate(data);
        })
        .fail(function (errorObj) {
            jQuery.messager.alert('Внимание', errorObj.getMessage(), 'warning');
        });
    }
}

/**
 * [generateNumberingTemplate description]
 *
 * @param   {[type]}  rows  [rows description]
 *
 * @return  {[type]}        [return description]
 */
function generateNumberingTemplate(rows) {
    clearRkoNumbers();

    if (jQuery('#payment-type-money-radio').is(':checked') == true) {
        jQuery('#payment-order-checkbox').prop('checked', true);
    } else {
        jQuery('#payment-order-checkbox').prop('checked', false);
    }
    if (jQuery('#payment-type-natura-radio').is(':checked') == true) {
        jQuery('#payment-order-checkbox-money-to-nat').prop('checked', true);
    } else {
        jQuery('#payment-order-checkbox-money-to-nat').prop('checked', false);
    }

    var parent = jQuery('#rko-numbering');
    let firstRkoNumber = null;
    const combinePaymentDocument =  jQuery('#combine-payment-document').is(':checked');
    Object.entries(rows).forEach(([rkoKey, rkoNumber], index) => {
        const rkoCounter = index + 1;
        var tr = jQuery('<tr></tr>');

        // RKO label
        tr.addClass('rkoNumberingRow');
        var td1 = jQuery('<td></td>');
        td1.css({"text-align": "right", "width": "125px"});
        td1.html(`Плащане №${rkoCounter}`);
        tr.append(td1);

        if (rkoCounter === 1) {
            firstRkoNumber = rkoNumber;
        }

        if (combinePaymentDocument && rkoCounter > 1 && firstRkoNumber) {
            // When 'Генерирай общ документ' is checked, hide all RKO number inputs except the first
            // and use the first RKO number as value for all payments
            tr.css('display', 'none');
            rkoNumber = firstRkoNumber;
        }

        // RKO value
        var td2 = jQuery('<td></td>');
        td2.append(
            rkoNumber
            ? `<input type="text" id=rko-${rkoCounter} data-rko-key="${rkoKey}" value="${rkoNumber}">`
            : `<input type="text" id=rko-${rkoCounter} data-rko-key="${rkoKey}">`
        );
        tr.append(td2);

        // RKO clear icon
        var td3 = jQuery('<td></td>');
        td3.css({"padding-right":"10px"});
        var clearBtn = jQuery(`<a 
                href="javaScript:void(0)" 
                onClick="clearRkoNumbering(${rkoCounter})" 
                title="Изчисти полето" 
                class="easyui-linkbutton" 
                data-options="iconCls:\'icon-cancel\'"
                style="width: 30px;"
                id="clearRko-${rkoCounter}"
            >&nbsp;</a>
        `)
        .appendTo(td3);
        clearBtn.linkbutton();
        tr.append(td3);
        parent.append(tr);
    });

    var windowHeight = jQuery('#btn-add-payment').offset().top - jQuery('#addPaymentBeginningMarker').offset().top + 72;
    jQuery('#win-add-payment').window('resize', {
        height: windowHeight
    });

}

/**
 * [getPaymentNumbering description]
 *
 * @return  {[type]}  [return description]
 */
function getPaymentNumbering() {
    var rows = jQuery('.rkoNumberingRow'),
        output = {};

    for (var i = 0; i < rows.length; i++) {
        const rkoKey = jQuery('#rko-'+(i+1)).data('rko-key');
        const rkoNumber = jQuery('#rko-'+(i+1)).val()
        
        output[rkoKey] = rkoNumber;
    }

    return output;
}