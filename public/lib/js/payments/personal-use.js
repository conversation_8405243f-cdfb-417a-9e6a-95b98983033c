var personal_use_edit = false;
var personal_use_edit_index = undefined;
var personal_use_divide = undefined;
var personal_use_value = undefined;
var used_area = undefined;
var DIVIDE_BY_PLOT = 2;

function initPersonalUseGrid(contract_id, annex_id, year_id)
{
    var personalUseGrid = jQuery('#personal-use-tables');
    let contractAnnexId = annex_id ? annex_id : contract_id;

    personalUseGrid.datagrid({
        nowrap: true,
        singleSelect: true,
		title: 'Лично ползване',
		iconCls: 'icon-planting',
        fit: true,
        fitColumns: true,
        showFooter: true,
        border: true,
		rownumbers: true,
        sortName: 'owner_id',
        sortOrder: 'asc',
        url: 'index.php?payments-rpc=personal-use-grid',
        rpcParams: [contractAnnexId, year_id],
        toolbar: [
            {
                id: 'btnaddcontractfile',
                text: 'Добавяне',
                iconCls: 'icon-add',
                handler: function () {
                    jQuery('#win-personal-use-owners-list').window('open');
                    initPersonalUseOwnersGrid(contract_id, annex_id, year_id);
                }
            },{
                id: 'btninnerdeletecontractplot',
                text: 'Премахване',
                iconCls: 'icon-delete',
                handler: function () {
                    let selectedRow = personalUseGrid.datagrid('getSelected');

                    if(!selectedRow) {
                        jQuery.messager.alert('Грешка', 'Не е избран собственик!', 'error');
                        return;
                    }

                    jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да премахнете този запис?', function (r) {
                        if (r) {
                            let params =   {
                                'owner_id': selectedRow.owner_id,
                                'contract_id': contract_id,
                                'year': year_id,
                                'pu_ids': selectedRow.pu_ids.split(',')
                            }
                            TF.Rpc.Payments
                                .PersonalUseGrid
                                .deletePersonalUse(params)
                                .done(function () {
                                    personalUseGrid.datagrid('reload');
                                })
                                .fail(function (errorObj) {
                                    jQuery('#win-personal-use-rents').window('close');
                                    let msg = errorObj.getMessage();
                                    if (errorObj.is(TF.Rpc.ExceptionsList.PERSONAL_USE_COLLECTIONS_EXIST)) {
                                        let selectedContract = jQuery('#contracts-tree').tree('getSelected');
                                        msg += ' - <a href="index.php?page=Collections.Home&c_id=' + selectedContract.id + '&fy=' + year_id + '" target="_blank">Тук</a>';
                                    }
                                    jQuery.messager.alert('Грешка', msg, 'error');
                                });
                        }
                    });
                }
            }
        ],
        columns: [
            [
                {
                    field: 'owner_names',
                    title: '<b>Собственик</b>',
                    sortable: true,
                    width: 345
                }, {
                    field: 'rep_names',
                    title: '<b>Представител</b>',
                    sortable: true,
                    width: 345
                }, {
                    field: 'owner_area',
                    title: '<b>Обща площ<br/>(дка)</b>',
                    sortable: true,
                    width: 120,
					align: 'center',
                }, {
                    field: 'personal_area',
                    title: '<b>Лично ползване<br/>(дка)</b>',
                    sortable: true,
                    width: 120,
					align: 'center',
                    editor: {
                        type: 'numberbox',
                        options: {
                            min: 0,
                            precision: 3,
                            onChange: function(newValue, oldValue) {
                                if(newValue == undefined || newValue == '') {
                                    return;
                                }

                                //set new value of personal use in public variable
                                personal_use_value = newValue;
                            }
                        }
                    }
                },{
                    field:'btn_rents',
                    title: '<b>Редакция</b>',
                    align: 'center',
                    width: 100,
                    hidden: false,
                    formatter: function(value,row) {
                        return '<a class="easyui-linkbutton" href="javaScript:void(0)" data-options="iconCls:\'icon-edit\', disabled: false", title="Редактиране лично ползване", onclick="openPersonalUseRenta(' + row.owner_id + ', true)"></a>';
                    }
                }
            ]
        ],
        onBeforeLoad: function() {
            personalUseGrid.datagrid('clearChecked');
        },
        onSelect: function(rowIndex, rowData) {
            personal_use_edit_index = rowIndex;
        },
        onLoadSuccess: function() {
            personal_use_edit_index = undefined;
            personal_use_divide = undefined;
            personal_use_value = undefined;
            jQuery(this).datagrid('getPanel').find('a.easyui-linkbutton').linkbutton();
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}


function initPersonalUseOwnersGrid(contract_id, annex_id, year_id)
{
    let params = {
        'contract_id': contract_id,
        'annex_id': annex_id,
        'year': year_id,
    }

    if(jQuery('#personal-use-owners-table').data().hasOwnProperty('treegrid')){
        jQuery('#personal-use-owners-table').treegrid({
            rpcParams: [params],
        });
    } else {
        jQuery('#personal-use-owners-table').treegrid({
            nowrap: true,
            singleSelect: false,
            title: 'Лично ползване',
            iconCls: 'icon-planting',
            pageSize: 50,
            fit: true,
            fitColumns: true,
            showFooter: true,
            sortName: 'owner_id',
            sortOrder: 'asc',
            idField: 'id',
            treeField: 'owner_names',
            url: 'index.php?payments-rpc=personal-use-grid',
            rpcMethod: "readPersonalUseOwners",
            rpcParams: [params],
            pagination: false,
            rownumbers: true,
            selectOnCheck: false,
            checkOnSelect: false,
            columns: [
                [
                    {
                        field: 'owner_names',
                        title: '<b>Име</b>',
                        sortable: true,
                        width: 200,
                        styler: function(value, row, index) {
                            if (!row.is_heritor) {
                                return 'font-weight: bold;';
                            }
                        }
                    },
                    {
                        field: 'rep_names',
                        title: '<b>Представител</b>',
                        sortable: true,
                        width: 200
                    },
                    {
                        field: 'kvs_used_area',
                        title: '<b>Използвана площ<br/>(дка)</b>',
                        sortable: true,
                        width: 80,
                        align: 'center',
                    },
                    {
                        field:'btn_add_owner',
                        title: '<b>Добавяне</b>',
                        align: 'center',
                        width:50,
                        formatter: function(value,row) {
                            if (!row.is_dead) {
                                return '<a class="easyui-linkbutton" style="border: none;" href="javaScript:void(0)" data-options="iconCls:\'icon-add\'", title="Добавяне на лично ползване", onclick="openPersonalUseRenta(' + row.owner_id + ', false)"></a>';
                            }
                        }
                    }
                ]
            ],
            onLoadSuccess: function() {
                jQuery(this).datagrid('getPanel').find('a.easyui-linkbutton').linkbutton();
            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }

}

function personalUsePlotsCheck(type, rowIndex) {
    var selected = jQuery('#personal-use-tables').datagrid('getRows')[rowIndex];

    if(selected) {
        jQuery('#win-personal-use-plots').window('open');
        personalUsePlotsGrid(type, personal_use_divide, selected.personal_use_plots, personal_use_value, used_area)
    }
    else {
        jQuery.messager.alert('Грешка', 'Не е избран собственик!', 'error');
    }
}
