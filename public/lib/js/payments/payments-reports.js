var report_type;
var report_filter;
var date = new Date();
var todayDate = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();
var weekAgo = new Date(date.setDate(date.getDate() - 7));

//function handles all reports and resends
function initReportResend(chosen_report) {
    // After closing the window, the datagrid is automatically resized to {width: 0, height: 0} and is not visible
    // That's why we need to resize it manually after the window is opened by calling .datagrid('resize')

    //assigns the requred report
    if (chosen_report == 'summary-report-by-ekate-money') {
        jQuery('#win-choose-report-type').window('close');

        report_type = 'summary-report-by-ekate-money';
        initSummaryReportByEkateMoneyGrid();

        jQuery('#win-payments-report-grid-panel').window('open');
        jQuery('#payment-report-tables').datagrid('resize');
    }

    if (chosen_report == 'summary-report-by-ekate-renta-natura') {
        jQuery('#win-choose-report-type').window('close');

        report_type = 'summary-report-by-ekate-renta-natura';
        initSummaryReportByEkateRentaNaturaGrid();

        jQuery('#win-payments-report-renta-grid-panel').window('open');
        jQuery('#payment-report-renta-natura-tables').datagrid('resize');
    }

    if (chosen_report == 'summary-report-by-ekate') {
        jQuery('#win-choose-report-type').window('close');

        report_type = 'summary-report-by-ekate';
        initSummaryReportByEkateGrid();

        jQuery('#win-report-grid-panel').window('setTitle', 'Обобщена справка по ЕКАТТЕ');
        jQuery('#win-payments-report-grid-panel').window('open');
        jQuery('#payment-report-tables').datagrid('resize');
    }

    if (chosen_report == 'detail-report-by-owner') {
        jQuery('#win-choose-report-type').window('close');

        report_type = 'detail-report-by-owner';
        initDetailReportByOwnerGrid();

        jQuery('#win-report-grid-panel').window('setTitle', 'Подробна справка по собственици');
        jQuery('#win-payments-report-grid-panel').window('open');
        jQuery('#payment-report-tables').datagrid('resize');
    }

    if (chosen_report == 'summary-report-by-date') {
        jQuery('#win-choose-report-type').window('close');

        report_type = 'summary-report-by-date';
        initSummaryReportByDateGrid();

        jQuery('#win-payments-report-by-date-grid-panel').window('open');
        jQuery('#payment-report-by-date-tables').datagrid('resize');
    }

    if (chosen_report == 'summary-paid-by-bank') {
        jQuery('#win-choose-report-type').window('close');

        report_type = 'summary-paid-by-bank';
        initPaidByBankReportFilters();
        initPaidByBankReport();

        jQuery('#win-payments-paid-by-bank').window('open');
        jQuery('#paid-by-bank-report-tables').datagrid('resize');
    }

    if (chosen_report == 'summary-paid-by-bank-and-natura') {
        jQuery('#win-choose-report-type').window('close');

        report_type = 'summary-paid-by-bank-and-natura';
        initPaidByBankReportFilters();
        initPaidByBankAndNaturaReport();

        jQuery('#win-payments-paid-by-bank-and-natura').window('open');
        jQuery('#paid-by-bank-and-natura-report-tables').datagrid('resize');
    }
}

function initPaidByBankAndNaturaReport() {
    jQuery('#paid-by-bank-and-natura-report-tables').datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 30,
        fit: true,
        singleSelect: true,
        showFooter: true,
        url: 'index.php?payments-rpc=payments-bank-and-natura-payment-report',
        idField: '',
        sortName: 'owner_name',
        sortOrder: 'asc',
        border: true,
        rpcParams: [{
            report_type: report_type,
            pbbn_date_from: jQuery('#pbb-date-from > input').datebox('getValue'),
            pbbn_date_to: jQuery('#pbb-date-to > input').datebox('getValue')
        }],
        rowStyler: function (index, row) {
            if (row.participate_in_contract == false) {
                return 'background-color:#ff7f6b;';
            }
        },
        columns: columnsFilter(),
        pagination: true,
        rownumbers: true,
        toolbar: '#report_bank_and_natura_payment_toolbar',
        onBeforeLoad: function () {
            jQuery('#plots-report-tables').datagrid('clearChecked');
        },
        onLoadSuccess: function() {
            TF.Loading.end();
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function columnsFilter() {
    var retnaTypes = ComboboxData.RentaTypesCombobox;
    var returnValues = [];
    var dataPush = [
        {
            field: 'owner_name',
            title: '<b>Собственик</b>',
            sortable: true,
            width: 200,
            align: 'left'
        },
        {
            field: 'c_num',
            title: '<b>Номер на <br> договора</b>',
            sortable: true,
            width: 100,
            align: 'center'
        },
        {
            field: 'money',
            title: '<b>В лева</b>',
            sortable: true,
            width: 100,
            align: 'right'
        }
    ];

    retnaTypes.forEach(function (retnaType) {
        retnaType = retnaType.name.split('(');
        if (retnaType[0] !== '-')
            dataPush.push({
                field: retnaType[0],
                title: '<b>' + retnaType[0] + ' (' + retnaType[1] + '</b>',
                sortable: true,
                width: 150,
                align: 'center'
            });
    });

    returnValues.push(dataPush);

    return returnValues;
};

function paidByBankAndNaturaFilter() {
    jQuery('#paid-by-bank-and-natura-report-tables').datagrid({
        rpcParams: [
            getPaidByBankAndNaturaFilterParams()
        ],
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function clearPaidByBankAndNaturaFilter() {
    initPaidByBankReportFilters(true);

    jQuery('#paid-by-bank-and-natura-report-tables').datagrid({
        rpcParams: [{}],
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function getPaidByBankAndNaturaFilterParams() {
    return {
        pbbn_year: jQuery('#pbb-year > input').combobox('getValue'),
        pbbn_date_from: jQuery('#pbb-date-from > input').datebox('getValue'),
        pbbn_date_to: jQuery('#pbb-date-to > input').datebox('getValue'),
        pbbn_payment_type: jQuery('#pbb-payment-type > input').combobox('getValue'),
        pbbn_renta_type: jQuery('#pbb-renta-type > input').combobox('getValue'),
        pbbn_paid_to: jQuery('#pbb-paid-to > input').val(),
        pbbn_paid_by: jQuery('#pbb-paid-by > input').val(),
        pbbn_bank_acc: jQuery('#pbb-bank-acc > input').val(),
        pbbn_farm: jQuery('#pbb-farm > input').combobox('getValue'),
        pbbn_owner_type: jQuery('#pbb-owner-type > input').combobox('getValue'),
        pbbn_ekattes: jQuery('#pbb-ekatte > input').combobox('getValues'),
        pbbn_place: jQuery('#pbb-place > input').val(),
        pbbn_cnum: jQuery('#pbb-cnum > input').val(),
    }
}

function initSummaryReportByDateGrid() {
    jQuery('#payment-report-by-date-tables').datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 30,
        fit: true,
        fitColumns: true,
        singleSelect: true,
        showFooter: true,
        url: 'index.php?payments-rpc=payments-reports-by-date-grid',
        idField: '',
        sortName: 'date',
        border: true,
        sortOrder: 'desc',
        rpcParams: [{
            report_type: report_type,
            year: jQuery('#prf-year-natura > input').combobox('getValue')
        }],
        columns: [[{
            field: 'date',
            title: '<b>Дата</b>',
            sortable: true,
            width: 80
        }, {
            field: 'ekate',
            title: '<b>ЕКАТТЕ</b>',
            sortable: true,
            width: 60
        }, {
            field: 'farming',
            title: '<b>Стопанство</b>',
            sortable: false,
            width: 100
        }, {
            field: 'farming_year',
            title: '<b>Стопанска <br/>година</b>',
            sortable: false,
            width: 100
        }, {
            field: 'paid_renta',
            title: '<b>Изплатена <br/>сума (пари)</b>',
            sortable: false,
            width: 80,
            align: 'center'
        }, {
            field: 'unpaid_renta',
            title: '<b>Остатък за <br/>плащане (пари)</b>',
            sortable: true,
            width: 80,
            align: 'center'
        }, {
            field: 'paid_renta_nat',
            title: '<b>Изплатена <br/>натура</b>',
            sortable: false,
            width: 80,
            align: 'center'
        }, {
            field: 'unpaid_renta_nat',
            title: '<b>Оставаща <br/>натура</b>',
            sortable: false,
            width: 80,
            align: 'center'
        }]],

        pagination: true,
        rownumbers: true,
        toolbar: [
            {
                id: 'btnpaymentsprintreport',
                text: 'Отпечатай',
                iconCls: 'icon-print',
                handler: function () {
                    printSummaryReportRentaByDate();
                }
            }, {
                id: 'btnpaymentsreportsexportexcel',
                text: 'Експорт(xls)',
                iconCls: 'icon-csv',
                handler: function () {
                    var data = jQuery('#payment-report-by-date-tables').datagrid('getData');

                    if (data.length == 0) {
                        jQuery.messager.alert('Грешка', 'Не може да експортнете празна справка.', 'error');
                    }
                    else {
                        var gridOptions = jQuery('#payment-report-by-date-tables').datagrid('options');
                        var winDownload = jQuery('#win-download');
                        var downloadFile = jQuery('#btn-download-file');
                        var params = gridOptions.rpcParams[0];

                        params.sort = gridOptions.sortName;
                        params.order = gridOptions.sortOrder;

                        TF.Rpc.Payments
                            .PaymentsReportsByDateGridExportAndPrint
                            .exportToExcelPaymentsReportsByData(params)
                            .done(function (dataObj) {
                                winDownload.window('open');
                                var path = dataObj;
                                _pathFile = path;
                                downloadFile.attr("href", path);
                            })
                            .fail(function (errorObj) {
                            });
                    }
                }
            }, {
                text: 'Подробна справка',
                iconCls: 'icon-reports',
                handler: function () {
                    var selected = jQuery('#payment-report-by-date-tables').datagrid('getSelected');

                    if (selected != null) {

                        showDetailedReportByDate(selected);
                    }
                    else {
                        jQuery.messager.alert('Грешка', 'Не е избран запис, за който да бъде показана подробна справка.', 'error');
                    }
                }
            }
        ],
        onBeforeLoad: function () {
            jQuery('#plots-report-tables').datagrid('clearChecked');
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function showDetailedReportByDate(selected) {
    var winTitle = 'Изплатена сума/' + selected.date + '/' + selected.ekate_name + '/' + selected.farming_year;

    initDetailedReportByDate(selected, winTitle);

    jQuery('#win-report-renta-by-date-detailed').window('setTitle', winTitle);

    jQuery('#win-report-renta-by-date-detailed').window('open');
}

function initDetailedReportByDate(selected, winTitle) {
    jQuery('#detailed-payment-report-by-date-tables').datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 30,
        fit: true,
        fitColumns: true,
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        showFooter: true,
        url: 'index.php?payments-rpc=payments-reports-by-date-grid',
        idField: '',
        sortName: 'date',
        border: true,
        sortOrder: 'desc',
        rpcParams: [{
            report_type: 'detailed-report-by-date',
            contracts: selected.contracts,
            date_compare: selected.date_compare,
            ekate: selected.ekate,
            farming_id: selected.farming_id,
            year: selected.farming_year_id
        }],
        frozenColumns: [[
            {
                field: 'contract_id',
                checkbox: true
            }
        ]],
        columns: [[{
            field: 'contract_name',
            title: '<b>Договор №</b>',
            sortable: false,
            width: 100
        }, {
            field: 'area_size',
            title: '<b>Площ в <br/>съответното <br/>землище <br>(дка)</b>',
            sortable: true,
            width: 70
        }, {
            field: 'paid_renta',
            title: '<b>Изплатена рента (пари)</b>',
            sortable: false,
            width: 70,
            align: 'center'
        }, {
            field: 'unpaid_renta',
            title: '<b>Остатък за <br/>плащане (пари)</b>',
            sortable: true,
            width: 80,
            align: 'center'
        }, {
            field: 'paid_renta_nat',
            title: '<b>Изплатена <br/>натура</b>',
            sortable: false,
            width: 80,
            align: 'center'
        }, {
            field: 'unpaid_renta_nat',
            title: '<b>Оставаща <br/>натура</b>',
            sortable: false,
            width: 80,
            align: 'center'
        }]],
        pagination: true,
        rownumbers: true,
        toolbar: [
            {
                id: 'btnpaymentsprintreport-by-date-detailed',
                text: 'Отпечатай',
                iconCls: 'icon-print',
                handler: function () {
                    printDetailedReportRentaByDate(winTitle);
                }
            }, {
                id: 'btnpaymentsreportsexportexcel-detailed',
                text: 'Експорт(xls)',
                iconCls: 'icon-csv',
                handler: function () {
                    var data = jQuery('#payment-report-by-date-tables').datagrid('getData');

                    if (data.length == 0) {
                        jQuery.messager.alert('Грешка', 'Не може да експортнете празна справка.', 'error');
                    }
                    else {
                        var gridOptions = jQuery('#detailed-payment-report-by-date-tables').datagrid('options');
                        var winDownload = jQuery('#win-download');
                        var downloadFile = jQuery('#btn-download-file');
                        var params = gridOptions.rpcParams[0];

                        params.sort = gridOptions.sortName;
                        params.order = gridOptions.sortOrder;

                        TF.Rpc.Payments
                            .PaymentsReportsByDateGridExportAndPrint
                            .exportToExcelDetailedReportRentaByDate(params)
                            .done(function (dataObj) {
                                winDownload.window('open');
                                var path = dataObj;
                                _pathFile = path;
                                downloadFile.attr("href", path);
                            })
                            .fail(function (errorObj) {
                            });
                    }
                }
            }, {
                id: 'btnviewcontractinfo',
                text: 'Информация',
                iconCls: 'icon-info',
                handler: function () {
                    var getChecked = jQuery('#detailed-payment-report-by-date-tables').datagrid('getChecked');

                    if (getChecked.length > 0) {

                        var url_string = '&contract_ids=';
                        var year_string = '&year=' + getChecked[0].farming_year;

                        for (i = 0; i < getChecked.length; i++) {
                            if (i == (getChecked.length - 1)) {
                                url_string += getChecked[i]['contract_id'] + '';
                            } else {
                                url_string += getChecked[i]['contract_id'] + ',';
                            }
                        }

                        window.open("index.php?page=Payments.Home" + url_string + year_string, '_blank');

                    } else {
                        jQuery.messager.alert('Грешка', 'Моля изберете договор, за който да бъде показана информация.', 'error');
                    }
                }
            }
        ],
        onBeforeLoad: function () {
            jQuery('#payment-report-by-date-tables').datagrid('clearChecked');
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function printDetailedReportRentaByDate(winTitle) {
    var data = jQuery('#detailed-payment-report-by-date-tables').datagrid('getData');
    var options = jQuery('#detailed-payment-report-by-date-tables').datagrid('options');

    if (data.length == 0) {
        jQuery.messager.alert('Грешка', 'Не може да отпечатате празна справка.', 'error');
    }
    else {
        var params = options.rpcParams[0];
        params.sort = options.sortName;
        params.order = options.sortOrder;

        TF.Rpc.Payments
            .PaymentsReportsByDateGridExportAndPrint
            .printPaymentsReportsByData(params)
            .done(function (dataObj) {
                var gridData = dataObj.rows;
                var footerData = dataObj.footer;

                var html = '<style>@page{size: landscape;}body,table{font-family: Arial;font-size: 10px;}</style>' +
                    '<h2 align="center">' + winTitle + '</h2>';

                var header = {};
                var columns = options.columns[0];
                for (var i = 0; i < columns.length; i++) {
                    header[columns[i].field] = columns[i].title;
                }

                var rows = gridData;
                rows.push(footerData[0]);
                html += Templates.table(header, rows);

                jQuery('body').append('<iframe id="printf" name="printf"></iframe>');
                var newWin = window.frames['printf'];
                newWin.document.write('<body onload=window.print()>'+html+'</body>');
                newWin.document.close();
                setTimeout(function () {
                    jQuery('#printf').remove();
                }, 1000);
            })
            .fail(function (errorObj) {
            });
    }
}

function printSummaryReportRentaByDate() {
    var data = jQuery('#payment-report-by-date-tables').datagrid('getData');
    var options = jQuery('#payment-report-by-date-tables').datagrid('options');

    if (data.length == 0) {
        jQuery.messager.alert('Грешка', 'Не може да отпечатате празна справка.', 'error');
    }
    else {
        var params = options.rpcParams[0];
        params.sort = options.sortName;
        params.order = options.sortOrder;

        TF.Rpc.Payments
            .PaymentsReportsByDateGridExportAndPrint
            .printPaymentsReportsByData(params)
            .done(function (dataObj) {
                var gridData = dataObj.rows;
                var footerData = dataObj.footer;

                var html = '<style>@page{size: landscape;}body,table{font-family: Arial;font-size: 10px;}</style>' +
                    '<h2 align="center">Справка - Изплатени ренти в лева по дата</h2>';

                var filter = new Object();
                filter.year = jQuery('#prf-year-by-date > input').combobox('getText') || '-';

                filter.ekate = jQuery('#prf-plot-ekatte-by-date > input').combobox('getText') || '-';

                filter.contract_type = jQuery('#prf-contract-type-natura > input').combobox('getText') || '-';

                filter.farming = jQuery('#prf-farming-by-date > input').combobox('getText') || '-';

                filter.date_from = jQuery('#report-by-date-from').combobox('getText') || '-';

                filter.date_to = jQuery('#report-by-date-to').combobox('getText') || '-';

                html += '<b>Филтър:</b><br/>';
                html += 'Година: <b>' + filter.year + '</b><br/>';
                html += 'Землище: <b>' + filter.ekate + '</b><br/>';
                html += 'Тип на договор: <b>' + filter.contract_type + '</b><br/>';
                html += 'Стопанство: <b>' + filter.farming + '</b><br/>';
                html += 'Период: <b>' + filter.date_from + ' до ' + filter.date_to + '</b><br/><br/>';

                var header = {};
                var columns = options.columns[0];
                for (var i = 0; i < columns.length; i++) {
                    header[columns[i].field] = columns[i].title;
                }

                var rows = gridData;
                rows.push(footerData[0]);
                html += Templates.table(header, rows);

                jQuery('body').append('<iframe id="printf" name="printf"></iframe>');
                var newWin = window.frames['printf'];
                newWin.document.write('<body onload=window.print()>'+html+'</body>');
                newWin.document.close();
                setTimeout(function () {
                    jQuery('#printf').remove();
                }, 1000);
            })
            .fail(function (errorObj) {
            });
    }
}

function paymentsReportsFilterByDate() {
    jQuery('#payment-report-by-date-tables').datagrid({
        rpcParams: [{
            report_type: report_type,
            year: jQuery('#prf-year-by-date > input').combobox('getValues'),
            ekate: jQuery('#prf-plot-ekatte-by-date > input').combobox('getValues'),
            contract_type: jQuery('#prf-contract-type-by-date > input').combobox('getValue'),
            farming: jQuery('#prf-farming-by-date > input').combobox('getValues'),
            date_from: jQuery('#report-by-date-from').datebox('getValue'),
            date_to: jQuery('#report-by-date-to').datebox('getValue')
        }],
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    })
}

function clearPaymentsReportsFilterByDate() {
    jQuery('#prf-year-by-date > input').combobox('reload');
    jQuery('#prf-year-by-date > input').combobox({
        onLoadSuccess: function () {
            jQuery('#payment-report-by-date-tables').datagrid({
                rpcParams: [{
                    report_type: report_type,
                    year: jQuery('#prf-year-by-date > input').combobox('getValue'),
                }],
                loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
            });
        }
    });

    jQuery('#prf-plot-ekatte-by-date > input').combobox('reset');
    jQuery('#prf-contract-type-by-date > input').combobox('reset');
    jQuery('#prf-farming-by-date > input').combobox('reset');
    jQuery('#report-by-date-from').datebox('clear');
    jQuery('#report-by-date-to').datebox('clear');
}

function initSummaryReportByEkateRentaNaturaGrid() {
    jQuery('#payment-report-renta-natura-tables').datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 30,
        fit: true,
        fitColumns: true,
        singleSelect: true,
        showFooter: true,
        url: 'index.php?payments-rpc=payments-reports-renta-natura-grid',
        idField: '',
        sortName: 'ekate',
        border: true,
        sortOrder: 'asc',
        rpcParams: [{
            report_type: report_type,
            year: jQuery('#prf-year-natura > input').combobox('getValue')
        }],
        columns: [[{
            field: 'land',
            title: '<b>Землище</b>',
            sortable: false,
            width: 100
        }, {
            field: 'ekate',
            title: '<b>ЕКАТТЕ</b>',
            sortable: true,
            width: 60
        }, {
            field: 'farming',
            title: '<b>Стопанство</b>',
            sortable: false,
            width: 100
        }, {
            field: 'farming_year',
            title: '<b>Стопанска <br/>година</b>',
            sortable: false,
            width: 100
        }, {
            field: 'renta_nat_type',
            title: '<b>Тип натура <br/>рента</b>',
            sortable: false,
            width: 80,
            align: 'center'
        }, {
            field: 'renta_nat',
            title: '<b>Дължимо <br/>количество по <br/>договор (т/бр./л.)</b>',
            sortable: false,
            width: 60,
            align: 'center'
        }, {
            field: 'charged_renta_nat',
            title: '<b>Начислено <br/>количество <br/>(т/бр./л.)</b>',
            sortable: false,
            width: 80,
            align: 'center'
        }, {
            field: 'paid_renta_nat',
            title: '<b>Изплатено<br/>количество </br>(т/бр./л.)</b>',
            sortable: true,
            width: 60,
            align: 'center'
        }, {
            field: 'unpaid_renta_nat',
            title: '<b>Остатък за <br/>изплащане <br/>(т/бр./л.)</b>',
            sortable: false,
            width: 60,
            align: 'center'
        }, {
            field: 'overpaid_renta_nat',
            title: '<b>Наддадено <br/>(т/бр./л.)</b>',
            sortable: false,
            width: 60,
            align: 'center'
        }]],
        pagination: true,
        rownumbers: true,
        toolbar: [
            {
                id: 'btnpaymentsprintreport-natura',
                text: 'Отпечатай',
                iconCls: 'icon-print',
                handler: function () {

                    printSummaryReportRentaNatura();
                }
            }, {
                id: 'btnpaymentsreportsexportexcel',
                text: 'Експорт(xls)',
                iconCls: 'icon-csv',
                handler: function () {
                    var gridOptions = jQuery('#payment-report-renta-natura-tables').datagrid('options');
                    var winDownload = jQuery('#win-download');
                    var downloadFile = jQuery('#btn-download-file');

                    var params = gridOptions.rpcParams[0];
                    params.sort = gridOptions.sortName;
                    params.order = gridOptions.sortOrder;

                    TF.Rpc.Payments
                        .PaymentsReportsRentaNaturaGridExportAndPrint
                        .exportToExcelPaymentsReportsRentaNaturaGrid(params)
                        .done(function (dataObj) {
                            winDownload.window('open');
                            var path = dataObj;
                            _pathFile = path;
                            downloadFile.attr("href", path);
                        })
                        .fail(function (errorObj) {
                        });
                }
            }, {
                text: 'Подробна справка',
                iconCls: 'icon-reports',
                handler: function () {
                    var selected = jQuery('#payment-report-renta-natura-tables').datagrid('getSelected');

                    if (selected != null) {
                        displayDetailedReportNaturaByContractsAndNaturaType(selected);
                    }
                    else {
                        jQuery.messager.alert('Грешка', 'Не е избран запис, за който да бъде показана подробна справка.', 'error');
                    }
                }
            }
        ],
        onBeforeLoad: function () {
            jQuery('#plots-report-tables').datagrid('clearChecked');
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function displayDetailedReportNaturaByContractsAndNaturaType(selected) {
    report_type = 'detailed-report-renta-natura';

    var winTitle = 'Подробна справка - Ренти в натура: ' + selected.land + '/' + selected.farming_year + '/' + selected.renta_nat_type;

    initDetailedReportNaturaGrid(selected, winTitle);

    jQuery('#win-report-renta-natura-detailed-report').window('setTitle', winTitle);

    jQuery('#win-report-renta-natura-detailed-report').window('open');
}

function initDetailedReportNaturaGrid(selected, winTitle) {

    jQuery('#detailed-payment-report-renta-natura-table').datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 30,
        fit: true,
        fitColumns: true,
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        showFooter: true,
        url: 'index.php?payments-rpc=payments-reports-renta-natura-grid',
        idField: '',
        sortName: 'ekate',
        border: true,
        sortOrder: 'asc',
        rpcParams: [{
            report_type: report_type,
            contracts: selected.contracts,
            nat_type: selected.nat_type,
            ekate: selected.ekate
        }],
        frozenColumns: [[
            {
                field: 'contract_id',
                checkbox: true
            }
        ]],
        columns: [[{
            field: 'contract_name',
            title: '<b>Договор №</b>',
            sortable: false,
            width: 100
        }, {
            field: 'farming_year',
            title: '<b>Стопанска <br/>година</b>',
            sortable: false,
            width: 100
        }, {
            field: 'farming',
            title: '<b>Стопанство</b>',
            sortable: false,
            width: 90
        }, {
            field: 'area_size',
            title: '<b>Площ в <br/>съответното <br/>землище <br>(дка)</b>',
            sortable: true,
            width: 70
        }, {
            field: 'renta_nat_type',
            title: '<b>Тип натура <br/>рента</b>',
            sortable: false,
            width: 70,
            align: 'center'
        }, {
            field: 'renta_value',
            title: '<b>Рента по<br/> договор <br/>(кг.;бр.;л./дка)</b>',
            sortable: false,
            width: 80,
            align: 'center'
        }, {
            field: 'renta_nat',
            title: '<b>Дължимо <br/>количество <br/>по договор <br/>(т/бр./л.)</b>',
            sortable: false,
            width: 70,
            align: 'center'
        }, {
            field: 'charged_renta_nat_stavka',
            title: '<b>Начислено <br/>количество <br/>(ставка)</br>(кг.;бр.;л./дка)</b>',
            sortable: false,
            width: 80,
            align: 'center'
        }, {
            field: 'charged_renta_nat',
            title: '<b>Начислено <br/>количество <br/>(т/бр./л.)</b>',
            sortable: false,
            width: 80,
            align: 'center'
        }, {
            field: 'paid_renta_nat',
            title: '<b>Изплатено<br/>количество </br>(т/бр./л.)</b>',
            sortable: true,
            width: 60,
            align: 'center'
        }, {
            field: 'unpaid_renta_nat',
            title: '<b>Остатък за <br/>изплащане <br/>(т/бр./л.)</b>',
            sortable: false,
            width: 60,
            align: 'center'
        }, {
            field: 'overpaid_renta_nat',
            title: '<b>Наддадено <br/>(т/бр./л.)</b>',
            sortable: false,
            width: 60,
            align: 'center'
        }]],

        pagination: true,
        rownumbers: true,
        toolbar: [
            {
                id: 'btnpaymentsprintreport-natura-detailed',
                text: 'Отпечатай',
                iconCls: 'icon-print',
                handler: function () {
                    printDetailedReportRentaNatura(winTitle);
                }
            }, {
                id: 'btnpaymentsreportsexportexcel-detailed',
                text: 'Експорт(xls)',
                iconCls: 'icon-csv',
                handler: function () {
                    var gridOptions = jQuery('#detailed-payment-report-renta-natura-table').datagrid('options');
                    var winDownload = jQuery('#win-download');
                    var downloadFile = jQuery('#btn-download-file');

                    var params = gridOptions.rpcParams[0];
                    params.sort = gridOptions.sortName;
                    params.order = gridOptions.sortOrder;

                    TF.Rpc.Payments
                        .PaymentsReportsRentaNaturaGridExportAndPrint
                        .exportToDetailedExcelPaymentsReportsRentaNaturaGrid(params)
                        .done(function (dataObj) {
                            winDownload.window('open');
                            var path = dataObj;
                            _pathFile = path;
                            downloadFile.attr("href", path);
                        })
                        .fail(function (errorObj) {
                        });
                }
            }, {
                id: 'btnviewcontractinfo',
                text: 'Информация',
                iconCls: 'icon-info',
                handler: function () {
                    var getChecked = jQuery('#detailed-payment-report-renta-natura-table').datagrid('getChecked');

                    if (getChecked.length > 0) {

                        var url_string = '&contract_ids=';
                        var year_string = '&year=' + getChecked[0].farming_year_id;
                        for (i = 0; i < getChecked.length; i++) {
                            if (i == (getChecked.length - 1)) {
                                url_string += getChecked[i]['contract_id'] + '';
                            } else {
                                url_string += getChecked[i]['contract_id'] + ',';
                            }
                        }

                        window.open("index.php?page=Payments.Home" + url_string + year_string, '_blank');

                    } else {
                        jQuery.messager.alert('Грешка', 'Моля изберете договор, за който да бъде показана информация.', 'error');
                    }
                }
            }
        ],
        onBeforeLoad: function () {

        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function printDetailedReportRentaNatura(winTitle) {
    var data = jQuery('#detailed-payment-report-renta-natura-table').datagrid('getData');
    var options = jQuery('#detailed-payment-report-renta-natura-table').datagrid('options');

    if (data.length == 0) {
        jQuery.messager.alert('Грешка', 'Не може да отпечатате празна справка.', 'error');
    }
    else {
        var params = options.rpcParams[0];
        params.sort = options.sortName;
        params.order = options.sortOrder;

        TF.Rpc.Payments
            .PaymentsReportsRentaNaturaGridExportAndPrint
            .printPaymentsReportsRentaNaturaGrid(params)
            .done(function (dataObj) {
                var rows = dataObj.rows;
                var footerData = dataObj.footer;

                var html = '<style>@page{size: landscape;}body,table{font-family: Arial;font-size: 10px;}</style>' +
                    '<h2 align="center">' + winTitle + '</h2>';

                var header = {};
                var columns = options.columns[0];

                for (var i = 0; i < columns.length; i++) {
                    header[columns[i].field] = columns[i].title;
                }

                html += Templates.table(header, rows);

                jQuery('body').append('<iframe id="printf" name="printf"></iframe>');
                var newWin = window.frames['printf'];
                newWin.document.write('<body onload=window.print()>'+html+'</body>');
                newWin.document.close();
                setTimeout(function () {
                    jQuery('#printf').remove();
                }, 1000);
            })
            .fail(function (errorObj) {
            });
    }
}

function printSummaryReportRentaNatura() {
    var data = jQuery('#payment-report-renta-natura-tables').datagrid('getData');
    var options = jQuery('#payment-report-renta-natura-tables').datagrid('options');

    if (data.length == 0) {
        jQuery.messager.alert('Грешка', 'Не може да отпечатате празна справка.', 'error');
    }
    else {
        var params = options.rpcParams[0];
        params.sort = options.sortName;
        params.order = options.sortOrder;

        TF.Rpc.Payments
            .PaymentsReportsRentaNaturaGridExportAndPrint
            .printPaymentsReportsRentaNaturaGrid(params)
            .done(function (dataObj) {
                var gridData = dataObj.rows;
                var footerData = dataObj.footer;

                var html = '<style>@page{size: landscape;}body,table{font-family: Arial;font-size: 10px;}</style>' +
                    '<h2 align="center">Обобщена справка "Ренти в натура" по землище</h2>';

                var filter = new Object();

                filter.year = jQuery('#prf-year-natura > input').combobox('getText') || '-';
                filter.renta_nat_type = jQuery('#prf-type-natura > input').combobox('getText') || '-';
                filter.ekate = jQuery('#prf-plot-ekatte-natura > input').combobox('getText') || '-';
                filter.farming = jQuery('#prf-farming-natura > input').combobox('getText') || '-';
                filter.contract_type = jQuery('#prf-contract-type-natura > input').combobox('getText') || '-';
                filter.area_type = jQuery('#prf-plot-area-type-natura > input').combobox('getText') || '-';

                html += '<b>Филтър:</b><br/>';
                html += 'Година: <b>' + filter.year + '</b><br/>';
                html += 'Тип натура рента: <b>' + filter.renta_nat_type + '</b><br/>';
                html += 'Землище: <b>' + filter.ekate + '</b><br/>';
                html += 'Тип на договор: <b>' + filter.contract_type + '</b><br/>';
                html += 'Стопанство: <b>' + filter.farming + '</b><br/>';
                html += 'НТП: <b>' + filter.area_type + '</b><br/>';

                var header = {};
                var columns = options.columns[0];
                for (var i = 0; i < columns.length; i++) {
                    header[columns[i].field] = columns[i].title;
                }

                var rows = gridData;
                html += Templates.table(header, rows);

                jQuery('body').append('<iframe id="printf" name="printf"></iframe>');
                var newWin = window.frames['printf'];
                newWin.document.write('<body onload=window.print()>'+html+'</body>');
                newWin.document.close();
                setTimeout(function () {
                    jQuery('#printf').remove();
                }, 1000);
            })
            .fail(function (errorObj) {
            });
    }
}

function initSummaryReportByEkateMoneyGrid() {
    jQuery('#payment-report-tables').datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 30,
        fit: true,
        fitColumns: true,
        singleSelect: true,
        showFooter: true,
        url: 'index.php?payments-rpc=payments-reports-grid',
        idField: '',
        sortName: 'ekate',
        border: true,
        sortOrder: 'asc',
        rpcParams: [{
            report_type: report_type,
            year: jQuery('#prf-year > input').combobox('getValue')
        }],
        columns: [[{
            field: 'land',
            title: '<b>Землище</b>',
            sortable: false,
            width: 100
        }, {
            field: 'ekate',
            title: '<b>ЕКАТТЕ</b>',
            sortable: true,
            width: 80
        }, {
            field: 'farming',
            title: '<b>Стопанство</b>',
            sortable: false,
            width: 120
        }, {
            field: 'farming_year',
            title: '<b>Стопанска <br/>година</b>',
            sortable: false,
            width: 120
        }, {
            field: 'renta',
            title: '<b>Дължима сума <br/> по договор (пари)</b>',
            sortable: true,
            width: 120
        }, {
            field: 'charged_renta',
            title: '<b>Начислена сума (пари)</b>',
            sortable: false,
            width: 100
        }, {
            field: 'paid_renta',
            title: '<b>Изплатена сума (пари)</b>',
            sortable: false,
            width: 100
        }, {
            field: 'unpaid_renta',
            title: '<b>Остатък за плащане (пари)</b>',
            sortable: false,
            width: 100
        },
            {
                field: 'overpaid',
                title: '<b>Надплатено (пари)</b>',
                sortable: false,
                width: 100
            }]],

        pagination: true,
        rownumbers: true,
        toolbar: '#toolbar_summary-report-by-ekate-money',
        onBeforeLoad: function () {
            jQuery('#plots-report-tables').datagrid('clearChecked');

            TF.Rpc.Payments
                .InfoSummaryReportByEkateMoney
                .loadInfo('summary-report-by-ekate-money')
                .done(function (dataObj) {
                    var status = dataObj.status;
                    var date = dataObj.date;

                    if (!status || status == 'Processing') {
                        jQuery.messager.alert('Информация', 'Справката се зарежда...Това може да продължи няколко минути. Моля изчакайте...', 'info', function () {
                            jQuery('#payment-report-tables').datagrid({
                                rpcParams: [{
                                    report_type: report_type,
                                    year: jQuery('#prf-year > input').combobox('getValues'),
                                }],
                                loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                                loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
                            });
                        });
                    } else {

                        if (date) {
                            jQuery('#current_date').html(date);
                        }
                        else {
                            var date = new Date();
                            var a = date.toU;
                            var todayDate = date.getDate() + '.' + (date.getMonth() + 1) + '.' + date.getFullYear() + ' ' + date.getHours() + ':' + date.getMinutes();

                            jQuery('#current_date').html(todayDate);
                        }
                    }
                })
                .fail(function (errorObj) {
                });
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function printSummaryReport() {
    var data = jQuery('#payment-report-tables').datagrid('getData');
    var options = jQuery('#payment-report-tables').datagrid('options');

    if (data.length == 0) {
        jQuery.messager.alert('Грешка', 'Не може да отпечатате празна справка.', 'error');
    }
    else {
        var params = options.rpcParams[0];
        params.sort = options.sortName;
        params.order = options.sortOrder;

        TF.Rpc.Payments
            .PaymentsReportsGridExportAndPrint
            .printPaymentsSummaryReports(params)
            .done(function (dataObj) {
                var gridData = dataObj.rows;
                var footerData = dataObj.footer;

                var html = '<style>@page{size: landscape;}body,table{font-family: Arial;font-size: 10px;}</style>' +
                    '<h2 align="center">Обобщена справка "Ренти в лева" по землище</h2>';

                var filter = new Object();
                filter.year = jQuery('#prf-year > input').combobox('getText') || '-';
                filter.contract_type = jQuery('#prf-contract-type > input').combobox('getText') || '-';
                filter.farming = jQuery('#prf-farming > input').combobox('getText') || '-';
                filter.plot_ekate = jQuery('#prf-plot-ekatte > input').combobox('getText') || '-';
                filter.plot_area_type = jQuery('#prf-plot-area-type > input').combobox('getText') || '-';

                html += '<b>Филтър:</b><br/>';
                html += 'Година: <b>' + filter.year + '</b><br/>';
                html += 'Тип на договор: <b>' + filter.contract_type + '</b><br/>';
                html += 'Стопанство: <b>' + filter.farming + '</b><br/>';
                html += 'Землище: <b>' + filter.plot_ekate + '</b><br/>';
                html += 'НТП: <b>' + filter.plot_area_type + '</b><br/>';

                var header = {};
                var columns = options.columns[0];
                for (var i = 0; i < columns.length; i++) {
                    header[columns[i].field] = columns[i].title;
                }

                var rows = gridData;
                rows.push(footerData[0]);
                html += Templates.table(header, rows);

                jQuery('body').append('<iframe id="printf" name="printf"></iframe>');
                var newWin = window.frames['printf'];
                newWin.document.write('<body onload=window.print()>'+html+'</body>');
                newWin.document.close();
                setTimeout(function () {
                    jQuery('#printf').remove();
                }, 1000);
            })
            .fail(function (errorObj) {
            });
    }
}

function exportToExcelSummaryReport() {
    var gridOptions = jQuery('#payment-report-tables').datagrid('options');
    var winDownload = jQuery('#win-download');
    var downloadFile = jQuery('#btn-download-file');

    var params = gridOptions.rpcParams[0];
    params.sort = gridOptions.sortName;
    params.order = gridOptions.sortOrder;

    TF.Rpc.Payments
        .PaymentsReportsGridExportAndPrint
        .exportToExcelPaymentsSummaryReports(params)
        .done(function (dataObj) {
            winDownload.window('open');
            var path = dataObj;
            _pathFile = path;
            downloadFile.attr("href", path);
        })
        .fail(function (errorObj) {
        });
}

function initSummaryReportByEkateGrid() {
    function formatLand(value) {
        var tableRows = jQuery('#payment-report-tables').datagrid('getData').rows;

        for (var i = 0; i < tableRows.length; i++) {
            if (tableRows[i].land == value) {
                return tableRows[i].land;
            }
        }

        return value;
    }

    jQuery('#payment-report-tables').datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 30,
        fit: true,
        fitColumns: true,
        singleSelect: true,
        showFooter: true,
        url: 'index.php?payments-rpc=payments-reports-grid',
        idField: '',
        sortName: 'ekate',
        border: false,
        sortOrder: 'asc',
        rpcParams: [{
            report_type: report_type,
            year: jQuery('#prf-year > input').combobox('getValue')
        }],
        columns: [[{
            field: 'land',
            title: '<b>Землище</b>',
            sortable: false,
            formatter: formatLand,
            width: 100
        }, {
            field: 'ekate',
            title: '<b>ЕКАТТЕ</b>',
            sortable: true,
            width: 100
        }, {
            field: 'renta',
            title: '<b>Дължима сума</b>',
            sortable: true,
            width: 100
        }, {
            field: 'charged_renta',
            title: '<b>Начислена сума</b>',
            sortable: true,
            width: 100
        }, {
            field: 'renta_nat_text',
            title: '<b>Дължима натура</b>',
            sortable: false,
            width: 200
        }, {
            field: 'charged_renta_nat_text',
            title: '<b>Начислена натура</b>',
            sortable: false,
            width: 200
        }]],
        pagination: true,
        rownumbers: true,
        toolbar: [
            {
                id: 'btnpaymentsreportsfilter',
                text: 'Филтър',
                iconCls: 'icon-filter',
                handler: function () {
                    jQuery('#win-payments-reports-filter').window('open');
                }
            }, {
                id: 'btnpaymentsreportsclearfilter',
                text: 'Покажи всички',
                iconCls: 'icon-clear-filter',
                handler: function () {
                    clearPaymentsReportsFilter();
                }
            }
        ],
        onBeforeLoad: function () {
            jQuery('#plots-report-tables').datagrid('clearChecked');
        },
        onLoadSuccess: function () {
            var tableRows = jQuery('#payment-report-tables').datagrid('getData').rows;

            for (var i = 0; i < tableRows.length; i++) {
                var mergeRows = 0;

                for (var j = 0; j < tableRows.length; j++) {
                    if (tableRows[i].land == tableRows[j].land) {
                        mergeRows++;
                    }
                }

                if (mergeRows > 1) {
                    jQuery('#payment-report-tables').datagrid('mergeCells', {
                        index: i,
                        field: 'land',
                        rowspan: mergeRows
                    });

                    i = i + mergeRows - 1;
                }
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initDetailReportByOwnerGrid() {
    jQuery('#payment-report-tables').datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 30,
        fit: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?payments-rpc=payments-reports-grid',
        idField: '',
        sortName: 'owner_names',
        border: false,
        sortOrder: 'asc',
        rpcParams: [{
            report_type: report_type,
            year: jQuery('#prf-year > input').combobox('getValue')
        }],
        columns: [[{
            field: 'owner_names',
            title: '<b>Собственик</b>',
            sortable: true,
            width: 100
        }, {
            field: 'c_num',
            title: '<b>Договор №</b>',
            sortable: true,
            width: 100
        }, {
            field: 'land',
            title: '<b>Землище</b>',
            sortable: false,
            width: 100
        }, {
            field: 'renta',
            title: '<b>Дължима сума</b>',
            sortable: true,
            width: 100
        }, {
            field: 'charged_renta',
            title: '<b>Начислена сума</b>',
            sortable: true,
            width: 100
        }, {
            field: 'renta_nat_text',
            title: '<b>Дължима натура</b>',
            sortable: false,
            width: 200
        }, {
            field: 'charged_renta_nat_text',
            title: '<b>Начислена натура</b>',
            sortable: false,
            width: 200
        }]],

        pagination: true,
        rownumbers: true,
        toolbar: [
            {
                id: 'btnpaymentsreportsfilter',
                text: 'Филтър',
                iconCls: 'icon-filter',
                handler: function () {
                    jQuery('#win-payments-reports-filter').window('open');
                }
            }, {
                id: 'btnpaymentsreportsclearfilter',
                text: 'Покажи всички',
                iconCls: 'icon-clear-filter',
                handler: function () {
                    clearPaymentsReportsFilter();
                }
            }
        ],
        onBeforeLoad: function () {
            jQuery('#plots-report-tables').datagrid('clearChecked');
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function paymentsReportsRentaNaturaFilter() {
    jQuery('#payment-report-renta-natura-tables').datagrid({
        rpcParams: [{
            report_type: 'summary-report-by-ekate-renta-natura',
            year: jQuery('#prf-year-natura > input').combobox('getValues'),
            renta_nat_type: jQuery('#prf-type-natura > input').combobox('getValues'),
            ekate: jQuery('#prf-plot-ekatte-natura > input').combobox('getValues'),
            farming: jQuery('#prf-farming-natura > input').combobox('getValues'),
            contract_type: jQuery('#prf-contract-type-natura > input').combobox('getValue'),
            area_type: jQuery('#prf-plot-area-type-natura > input').combobox('getValue'),
        }],
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function clearPaymentsReportsRentaNaturaFilter() {
    jQuery('#prf-year-natura > input').combobox('reload');
    jQuery('#prf-year-natura > input').combobox({
        onLoadSuccess: function () {
            jQuery('#payment-report-renta-natura-tables').datagrid({
                rpcParams: [{
                    report_type: report_type,
                    year: jQuery('#prf-year-natura > input').combobox('getValue'),
                }],
                loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
            });
        }
    });
    jQuery('#prf-type-natura > input').combobox('reset');
    jQuery('#prf-plot-ekatte-natura > input').combobox('reset');
    jQuery('#prf-farming-natura > input').combobox('reset');
    jQuery('#prf-contract-type-natura > input').combobox('reset');
    jQuery('#prf-plot-area-type-natura > input').combobox('reset');
}

function refreshSummaryReportByEkateMoney() {
    TF.Rpc.Payments
        .InfoSummaryReportByEkateMoney
        .refreshReport('summary-report-by-ekate-money')
        .done(function (dataObj) {
            jQuery('#payment-report-tables').datagrid({
                rpcParams: [{
                    report_type: report_type,
                    year: jQuery('#prf-year > input').combobox('getValues'),
                    filter_clicked: true,
                }],
                loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
            });
        })
        .fail(function (errorObj) {
        });

    var pager = jQuery('#payment-report-tables').datagrid('getPager');

    pager.pagination({
        onBeforeRefresh: function () {

            jQuery('#payment-report-tables').datagrid({
                rpcParams: [{
                    report_type: report_type,
                    year: jQuery('#prf-year > input').combobox('getValues'),
                }],
                loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
            });

            return true;
        }

    });
}

function paymentsReportsFilter() {
    jQuery('#payment-report-tables').datagrid({
        rpcParams: [{
            report_type: report_type,
            year: jQuery('#prf-year > input').combobox('getValues'),
            //plots filter
            ekate: jQuery('#prf-plot-ekatte > input').combobox('getValues'),
            area_type: jQuery('#prf-plot-area-type > input').combobox('getValue'),
            //contract filter
            contract_type: jQuery('#prf-contract-type > input').combobox('getValue'),
            farming: jQuery('#prf-farming > input').combobox('getValues'),
            filter_clicked: true
        }],
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function clearPaymentsReportsFilter() {
    jQuery('#prf-year > input').combobox('reload');
    jQuery('#prf-year > input').combobox({
        onLoadSuccess: function () {
            jQuery('#payment-report-tables').datagrid({
                rpcParams: [{
                    report_type: report_type,
                    year: jQuery('#prf-year > input').combobox('getValue'),
                }],
                loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
            });
        }
    });
    jQuery('#prf-plot-ekatte > input').combobox('reset');
    jQuery('#prf-plot-masiv > input').val();
    jQuery('#prf-plot-category > input').combobox('reset');
    jQuery('#prf-plot-area-type > input').combobox('reset');
    jQuery('#prf-contract-type > input').combobox('reset');
    jQuery('#prf-farming > input').combobox('reset');
    jQuery('#prf-all-contracts input').prop('checked', true);


    //clear payments reports filter fields
    jQuery('#prf-kad-ident > input').val('');
    jQuery('#prf-plot-masiv > input').val('');
    jQuery('#prf-plot-number > input').val('');
    jQuery('#prf-cnum > input').val('');
    jQuery('#prf-owner-name > input').val('');
    jQuery('#prf-owner-egn > input').val('');
    jQuery('#prf-rep-name > input').val('');
    jQuery('#prf-rep-egn > input').val('');
    jQuery('#prf-company-name > input').val('');
    jQuery('#prf-company-eik > input').val('');
}

function initPaidByBankReport() {
    jQuery('#paid-by-bank-report-tables').datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 30,
        fit: true,
        fitColumns: true,
        singleSelect: true,
        showFooter: true,
        url: 'index.php?payments-rpc=payments-bank-payment-report',
        idField: '',
        sortName: 'transaction_id',
        sortOrder: 'desc',
        border: true,
        rpcParams: [{
            report_type: report_type,
            year: jQuery('#pbb-year > input').combobox('getValue')
        }],
        rowStyler: function (index, row) {
            if (row.participate_in_contract == false) {
                return 'background-color:#ff7f6b;';
            }
        },
        columns: [[{
            field: 'transaction_id',
            title: '<b>Номер на<br/>транзакция</b>',
            sortable: true,
            width: 60,
            align: 'center'
        },
        {
            field: 'c_num',
            title: '<b>Номер на<br/>договора</b>',
            sortable: true,
            width: 60,
            align: 'center'
        },{
            field: 'date',
            title: '<b>Дата</b>',
            sortable: true,
            width: 60,
            align: 'center'
        }, {
            field: 'payment_type',
            title: '<b>Начин на<br/>плащане</b>',
            sortable: false,
            width: 70,
            align: 'center'
        }, {
            field: 'recipient',
            title: '<b>Изплатено на</b>',
            sortable: true,
            width: 100,
            align: 'center'
        }, {
            field: 'payer_name',
            title: '<b>Изплатено от</b>',
            sortable: true,
            width: 80,
            align: 'center'
        }, {
            field: 'farming_year',
            title: '<b>Стопанска <br/>година</b>',
            sortable: true,
            width: 65,
            align: 'center'
        }, {
            field: 'bank_acc',
            title: '<b>Банкова<br/>сметка</b>',
            sortable: false,
            width: 80,
            align: 'center'
        }, {
            field: 'unit_value',
            title: '<b>Единична<br/>цена</b>',
            sortable: false,
            width: 65,
            align: 'center'
        }, {
            field: 'amount',
            title: '<b>Сума<br/>(пари)</b>',
            sortable: false,
            width: 80,
            align: 'center'
        }, {
            field: 'nat_amount',
            title: '<b>Количество<br/>натура</b>',
            sortable: false,
            width: 60,
            align: 'center'
        }, {
            field: 'full_renta',
            title: '<b>Тип<br/>натура</b>',
            sortable: false,
            width: 70,
            align: 'center'
        }]],

        pagination: true,
        rownumbers: true,
        toolbar: '#report_bank_payment_toolbar',
        onBeforeLoad: function () {
            jQuery('#plots-report-tables').datagrid('clearChecked');
        },
        onLoadSuccess: function() {
            TF.Loading.end();
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initPersonalUseReport(toolbar = '#report_personal_use_toolbar', rpcParams = {}, selectedRow = 0) {
    jQuery('#personal-use-report-tables').datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 30,
        fit: true,
        singleSelect:true,
        showFooter: true,
        url: 'index.php?payments-rpc=personal-use-report',
        idField: 'id',
        sortName: 'id',
        sortOrder: 'desc',
        border: true,
        rpcParams: [rpcParams],
        columns: [[{
                    field: 'owner_names',
                    title: '<b>Име</b>',
                    sortable: true,
                    width: 220,
                    align: 'center'
                },
                {
                    field: 'egn_eik',
                    title: '<b>ЕГН</b>',
                    sortable: false,
                    width: 120,
                    align: 'center'
                },{
                    field: 'farming_name',
                    title: '<b>Стопанство</b>',
                    sortable: false,
                    width: 180,
                    align: 'center'
                },
                {
                    field: 'c_num',
                    title: '<b>Договор</b>',
                    sortable: false,
                    width: 150,
                    align: 'center'
                },
                {
                    field: 'farming_year',
                    title: '<b>Стопанска <br>година</b>',
                    sortable: false,
                    width: 120,
                    align: 'center'
                },{
                    field: 'renta_type_name',
                    title: '<b>Култура</b>',
                    sortable: false,
                    width: 90,
                    align: 'center'
                },
                {
                    field: 'personal_use_area',
                    title: '<b>Площ за <br> лично ползване (дка)</b>',
                    sortable: false,
                    width: 90,
                    align: 'center',
                },
                {
                    field: 'average_yield',
                    title: '<b>Среден <br>добив (кг./дка)</b>',
                    sortable: false,
                    width: 100,
                    align: 'center',
                    formatter: function (value){
                        if(value){
                            return  parseFloat(value).toFixed(2);
                        }
                    }
                },
                {
                    field: 'renta_per_dka',
                    title: '<b>Количество <br>натура (кг./дка)</b>',
                    sortable: false,
                    width: 100,
                    align: 'center',
                    formatter: function (value){
                        if(value){
                            return  parseFloat(value).toFixed(2);
                        }
                    }
                },
                {
                    field: 'personal_use_rent_quantity',
                    title: '<b>Дължимо <br>Количество</b>',
                    sortable: false,
                    width: 90,
                    align: 'center',
                    formatter: function (value, row){
                        if(value) {
                            return parseFloat(value).toFixed(2);
                        }
                    }
                },
                {
                    field: 'personal_use_paid_rent_quantity',
                    title: '<b>Изплатено <br>количество</b>',
                    sortable: false,
                    width: 90,
                    align: 'center',
                    formatter: function (value, row){
                        if(value){
                            return  parseFloat(value).toFixed(2);
                        }
                    }
                },
                {
                    field: 'personal_use_unpaid_rent_quantity',
                    title: '<b>Оставащо <br>количество</b>',
                    sortable: false,
                    width: 90,
                    align: 'center',
                    formatter: function (value, row){
                        if(value){
                            return  parseFloat(value).toFixed(2);
                        }
                    }
                },
                {
                    field: 'treatments_price',
                    title: '<b>Обработки <br> на дка</b>',
                    sortable: false,
                    width: 90,
                    align: 'center',
                },
                {
                    field: 'personal_use_treatments_sum',
                    title: '<b>Сума (пари)</b>',
                    sortable: false,
                    width: 90,
                    align: 'center',
                },
                {
                    field: 'personal_use_paid_treatments',
                    title: '<b>Внесена <br>сума (пари)</b>',
                    sortable: false,
                    width: 90,
                    align: 'center',
                },
                {
                    field: 'personal_use_unpaid_treatments',
                    title: '<b>Остатък <br>сума (пари)</b>',
                    sortable: false,
                    width: 90,
                    align: 'center',
                }
            ]
        ],

        pagination: true,
        rownumbers: true,
        toolbar: toolbar,
        onBeforeLoad: function() {
        },
        onSelect: function (index,row){},
        onLoadSuccess: function() {
            TF.Loading.end();
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initPersonalUseFilter() {
    jQuery('#search-renta-nat-types').combobox({
        editable: false,
        url: 'index.php?common-rpc=renta-types-combobox',
        valueField: 'id',
        textField: 'name',
        rpcParams: [{
            allValuesOption: true,
            typeTextLabel: true
        }],
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    let formattedFarmingYears = [];
    const farmingYearsLength = ComboboxData.FarmingYearCombobox.length;
    for(let i = 0; i < farmingYearsLength; i++) {
        if(ComboboxData.FarmingYearCombobox[i].id === ''){
            continue;
        }
        if(ComboboxData.FarmingYearCombobox[i].id === ComboboxData.CurrentFarmingYear.id){
            ComboboxData.FarmingYearCombobox[i].selected = true;
        }
        formattedFarmingYears.push(ComboboxData.FarmingYearCombobox[i]);
    }

    jQuery('#search-farming-year').combobox({
        data: formattedFarmingYears,
        valueField: 'id',
        textField: 'farming_year',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-personal-use-owner-name').val('');
    jQuery('#search-personal-use-owner-egn').val('');

    initSearchOwnerFieldComplete();
}

function personalUseFilter() {
    let searchFarmingYearId = 'search-farming-year';
    jQuery('#personal-use-report-tables').datagrid({
        rpcParams: [
            getPersonalUseFilterParams(searchFarmingYearId)
        ],
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function getPersonalUseFilterParams(searchFarmingYearId = 'search-farming-year') {

    return {
        pu_farming_year: jQuery('#' + searchFarmingYearId).combobox('getValue'),
        owner_name: jQuery('#search-personal-use-owner-name').val(),
        owner_egn: jQuery('#search-personal-use-owner-egn').val(),
        renta_type: jQuery('#search-renta-nat-types').combobox('getValue'),
        c_num: jQuery('#search-personal-use-c-num').val()
    };
}

function clearPersonalUseFilter () {
    initPersonalUseFilter();
    initPersonalUseReport('#report_personal_use_toolbar', {pu_farming_year: jQuery('#search-farming-year').combobox('getValue')});
}

function initPaidByBankReportFilters() {
    var farmingYearComboboxData = ComboboxData.FarmingYearCombobox;
    //Get all the filter fields with one DOM search for each field
    var pbb_year = jQuery('#pbb-year > input');
    var pbb_date_from = jQuery('#pbb-date-from > input');
    var pbb_date_to = jQuery('#pbb-date-to > input');
    var pbb_payment_type = jQuery('#pbb-payment-type > input');
    var pbb_renta_type = jQuery('#pbb-renta-type > input');
    var pbb_paid_to = jQuery('#pbb-paid-to > input');
    var pbb_paid_by = jQuery('#pbb-paid-by > input');
    var pbb_bank_acc = jQuery('#pbb-bank-acc > input');
    var pbb_farm = jQuery('#pbb-farm > input');
    var pbb_owner_type = jQuery('#pbb-owner-type > input');
    var pbb_ekatte = jQuery('#pbb-ekatte > input');

    //Check if the field is initialized
    //and if not - initalize
    //if it is already initialized - reset to default value
    if (!pbb_year.data().hasOwnProperty('combobox')) {
        pbb_year.combobox({
            data: farmingYearComboboxData,
            editable: false,
            valueField: 'id',
            textField: 'farming_year',
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    } else {
        pbb_year.combobox('reset');
    }

    //Check if the field is initialized
    //and if not - initalize
    //if it is already initialized - reset to default value
    if (!pbb_date_from.data().hasOwnProperty('datebox')) {
        pbb_date_from.datebox({
            value: '',
            validator: function (date) {
                var now = new Date();
                var d1 = new Date('01/01/2016');
                return d1 <= date;
            }
        });
    } else {
        pbb_date_from.datebox('reset');
    }

    pbb_date_from.datebox().datebox('calendar').calendar({
        validator: function (date) {
            var d1 = new Date('01/01/2016');
            return d1 <= date;
        }
    });

    //Check if the field is initialized
    //and if not - initalize
    //if it is already initialized - reset to default value
    if (!pbb_date_to.data().hasOwnProperty('datebox')) {
        pbb_date_to.datebox({
            value: ''
        });
    } else {
        pbb_date_to.datebox('reset');
    }

    pbb_date_to.datebox().datebox('calendar').calendar({
        validator: function (date) {
            var d1 = new Date(pbb_date_from.datebox('getValue'));
            var minDate = new Date(d1.getFullYear() + '-' + (d1.getMonth() + 1) + '-' + d1.getDate());
            return minDate <= date;
        }
    });

    //Check if the field is initialized
    //and if not - initalize
    //if it is already initialized - reset to default value
    if (!pbb_payment_type.data().hasOwnProperty('combobox')) {
        pbb_payment_type.combobox({
            valueField: 'label',
            textField: 'value',
            data: [{
                label: '',
                value: 'Всички',
                selected: true
            }, {
                label: 'bank',
                value: 'Банков път'
            }, {
                label: 'post',
                value: 'Пощенски запис'
            }, {
                label: 'false',
                value: 'В брой'
            }],
            editable: false,
        })
    } else {
        pbb_payment_type.combobox('reset');
    }

    //Check if the field is initialized
    //and if not - initalize
    //if it is already initialized - reset to default value
    if (!pbb_renta_type.data().hasOwnProperty('combobox')) {
        pbb_renta_type.combobox({
            url: 'index.php?common-rpc=renta-types-combobox',
            textField: 'name',
            valueField: 'id',
            editable: false,
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    } else {
        pbb_renta_type.combobox('reset');
    }

    //Check if the field is initialized
    //and if not - initalize
    //if it is already initialized - reset to default value
    if (!pbb_farm.data().hasOwnProperty('combobox')) {
        pbb_farm.combobox({
            data: ComboboxData.FarmingCombobox,
            textField: 'name',
            valueField: 'id',
            editable: false,
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    } else {
        pbb_farm.combobox('reset');
    }

    //Check if the field is initialized
    //and if not - initalize
    //if it is already initialized - reset to default value
    if (!pbb_owner_type.data().hasOwnProperty('combobox')) {
        pbb_owner_type.combobox({
            data: ComboboxData.OwnerTypesCombobox,
            textField: 'name',
            valueField: 'id',
            editable: false,
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    } else {
        pbb_owner_type.combobox('reset');
    }

    if (!pbb_ekatte.data().hasOwnProperty('combobox')) {
        pbb_ekatte.combobox({
            data: ComboboxData.EkateCombobox,
            valueField: 'ekate',
            textField: 'text',
            multiple: true,
            onHidePanel: onHidePanelMultiSelect,
            onSelect: onComboMultiSelect,
            filter: function (q, row) {
                var opts = jQuery(this).combobox('options'),
                    text = row[opts.textField].toLowerCase(),
                    value = row[opts.valueField],
                    find = q.toLowerCase();
                if (text.indexOf(find) !== -1 || value.indexOf(find) !== -1) {
                    return true;
                }
            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    } else {
        pbb_ekatte.combobox('reset');
    }

    jQuery('#pbb-paid-to > input').val('');
    jQuery('#pbb-paid-by > input').val('');
    jQuery('#pbb-bank-acc > input').val('');
    jQuery('#pbb-place > input').val('');
    jQuery('#pbb-cnum > input').val('');
}

function paidByBankHubFilter() {
    if(report_type === 'summary-paid-by-bank'){
        paidByBankFilter()
    } else if (report_type === 'summary-paid-by-bank-and-natura'){
        paidByBankAndNaturaFilter()
    }
}

function clearPaidByBankHubFilter() {
    if(report_type === 'summary-paid-by-bank'){
        clearPaidByBankFilter()
    } else if (report_type === 'summary-paid-by-bank-and-natura'){
        clearPaidByBankAndNaturaFilter()
    }
}

function paidByBankFilter() {
    jQuery('#paid-by-bank-report-tables').datagrid({
        rpcParams: [
            getPaidByBankFilterParams()
        ],
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function clearPaidByBankFilter() {
    initPaidByBankReportFilters();
    jQuery('#paid-by-bank-report-tables').datagrid({
        rpcParams: [{}],
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function getPaidByBankFilterParams() {
    return {
        pbb_year: jQuery('#pbb-year > input').combobox('getValue'),
        pbb_date_from: jQuery('#pbb-date-from > input').datebox('getValue'),
        pbb_date_to: jQuery('#pbb-date-to > input').datebox('getValue'),
        pbb_payment_type: jQuery('#pbb-payment-type > input').combobox('getValue'),
        pbb_renta_type: jQuery('#pbb-renta-type > input').combobox('getValue'),
        pbb_paid_to: jQuery('#pbb-paid-to > input').val(),
        pbb_paid_by: jQuery('#pbb-paid-by > input').val(),
        pbb_bank_acc: jQuery('#pbb-bank-acc > input').val(),
        pbb_farm: jQuery('#pbb-farm > input').combobox('getValue'),
        pbb_owner_type: jQuery('#pbb-owner-type > input').combobox('getValue'),
        pbb_ekattes: jQuery('#pbb-ekatte > input').combobox('getValues'),
        pbb_place: jQuery('#pbb-place > input').val(),
        pbb_cnum: jQuery('#pbb-cnum > input').val(),
    }
}

jQuery(function () {
    jQuery('#print-report-bank-payment-page').bind('click', function () {
        printReportBankPayment(true);
    });

    jQuery('#print-report-bank-payment').bind('click', function () {
        printReportBankPayment();
    });
    jQuery('#print-report-bank-and-natura-payment-page').bind('click', function () {
        printReportBankAndNaturaPayment(true);
    });

    jQuery('#print-report-bank-and-natura-payment').bind('click', function () {
        printReportBankAndNaturaPayment();
    });
    jQuery('#export-payment-bank-excel').bind('click', function () {
        exportReportBankPayment();
    });
    jQuery('#export-payment-bank-natura-excel').bind('click', function () {
        exportReportBankAndNAturaPayment();
    });
});

function printReportBankAndNaturaPayment(currentPage) {
    var data = jQuery('#paid-by-bank-and-natura-report-tables').datagrid('getData');

    if (data.length == 0) {
        jQuery.messager.alert('Грешка', 'Не може да експортнете празна справка.', 'error');
    }
    else {
        var gridOptions = jQuery('#paid-by-bank-and-natura-report-tables').datagrid('options');
        var winDownload = jQuery('#win-download');
        var downloadFile = jQuery('#btn-download-file');
        var params = gridOptions.rpcParams[0];

        var pageNumber = null;
        var pageSize = null;

        if (currentPage) {
            pageNumber = gridOptions.pageNumber;
            pageSize = gridOptions.pageSize;
        }

        var sort = gridOptions.sortName;
        var order = gridOptions.sortOrder;
        var page = pageNumber;
        var rows = pageSize;

        TF.Rpc.Payments.PaymentsBankAndNaturaPaymentReport.read(params, page, rows, sort, order)
            .done(function (dataObj) {
                var gridData = dataObj.rows;
                var footer = dataObj.footer;
                var html = '<style>@page{size: landscape;}body,table{font-family: Arial;font-size: 10px;}</style>' +
                    '<h2 align="center">Справка - Изплатено в брой/по банков път, и в натура</h2>';

                var header = {};
                var columns = gridOptions.columns[0];
                for (var i = 0; i < columns.length; i++) {
                    header[columns[i].field] = columns[i].title;
                }

                var rows = gridData;
                if (currentPage) {
                    rows = rows.concat(footer);
                } else {
                    rows = rows.concat(footer[1]);
                }

                html += Templates.table(header, rows);

                jQuery('body').append('<iframe id="printf" name="printf"></iframe>');
                var newWin = window.frames['printf'];
                newWin.document.write('<body onload=window.print()>'+html+'</body>');
                newWin.document.close();
                setTimeout(function () {
                    jQuery('#printf').remove();
                }, 1000);
            })
            .fail(function (errorObj) {

            });
    }
}

function printReportBankPayment(currentPage) {
    var data = jQuery('#paid-by-bank-report-tables').datagrid('getData');

    if (data.length == 0) {
        jQuery.messager.alert('Грешка', 'Не може да експортнете празна справка.', 'error');
    }
    else {
        var gridOptions = jQuery('#paid-by-bank-report-tables').datagrid('options');
        var winDownload = jQuery('#win-download');
        var downloadFile = jQuery('#btn-download-file');
        var params = gridOptions.rpcParams[0];

        var pageNumber = null;
        var pageSize = null;

        if (currentPage) {
            pageNumber = gridOptions.pageNumber;
            pageSize = gridOptions.pageSize;
        }

        var sort = gridOptions.sortName;
        var order = gridOptions.sortOrder;
        var page = pageNumber;
        var rows = pageSize;

        TF.Rpc.Payments.PaymentsBankPaymentReport.read(params, page, rows, sort, order)
            .done(function (dataObj) {
                var gridData = dataObj.rows;
                var footer = dataObj.footer;
                var html = '<style>@page{size: landscape;}body,table{font-family: Arial;font-size: 10px;}</style>' +
                    '<h2 align="center">Справка - Изплатено в брой/по банков път</h2>';

                var header = {};
                var columns = gridOptions.columns[0];
                for (var i = 0; i < columns.length; i++) {
                    header[columns[i].field] = columns[i].title;
                }

                var rows = gridData;
                if (currentPage) {
                    rows = rows.concat(footer);
                } else {
                    rows = rows.concat(footer[1]);
                }

                html += Templates.table(header, rows);

                jQuery('body').append('<iframe id="printf" name="printf"></iframe>');
                var newWin = window.frames['printf'];
                newWin.document.write('<body onload=window.print()>'+html+'</body>');
                newWin.document.close();
                setTimeout(function () {
                    jQuery('#printf').remove();
                }, 1000);
            })
            .fail(function (errorObj) {

            });
    }
}

function printPersonalUseReport(currentPage){
    var data = jQuery('#personal-use-report-tables').datagrid('getData');

    if (data.length == 0) {
        jQuery.messager.alert('Грешка', 'Не може да експортнете празна справка.', 'error');
    }
    else {
        var gridOptions = jQuery('#personal-use-report-tables').datagrid('options');
        var winDownload = jQuery('#win-download');
        var downloadFile = jQuery('#btn-download-file');
        var params = gridOptions.rpcParams[0];

        var pageNumber = null;
        var pageSize = null;

        if(currentPage) {
            pageNumber = gridOptions.pageNumber;
            pageSize = gridOptions.pageSize;
        }

        var sort = gridOptions.sortName;
        var order = gridOptions.sortOrder;
        var page = pageNumber;
        var rows = pageSize;

        TF.Rpc.Payments.PersonalUseReport.read(params,page,rows,sort,order)
            .done(function (dataObj) {

                var gridData = dataObj.rows;
                var footer = dataObj.footer;
                var html = '<style>@page{size: landscape;}body,table{font-family: Arial;font-size: 10px;}</style>' +
                    '<h2 align="center">Справка - Лично ползване</h2>';

                var header = {};
                var columns = gridOptions.columns[0];
                for(var i=0; i<columns.length; i++)
                {
                    header[columns[i].field] = columns[i].title;
                }

                var rows = gridData;
                if(!currentPage) {
                    rows = rows.concat(footer[0]);
                }

                html += Templates.table(header, rows);

                jQuery('body').append('<iframe id="printf" name="printf"></iframe>');
                var newWin = window.frames['printf'];
                newWin.document.write('<body onload=window.print()>'+html+'</body>');
                newWin.document.close();
                setTimeout(function () {
                    jQuery('#printf').remove();
                }, 1000);
            })
            .fail(function (errorObj) {

            });
    }
}

function exportReportBankAndNAturaPayment() {
    var data = jQuery('#paid-by-bank-and-natura-report-tables').datagrid('getData');

    if (data.length == 0) {
        jQuery.messager.alert('Грешка', 'Не може да експортнете празна справка.', 'error');
    }
    else {
        var gridOptions = jQuery('#paid-by-bank-and-natura-report-tables').datagrid('options');
        var winDownload = jQuery('#win-download');
        var downloadFile = jQuery('#btn-download-file');
        var params = gridOptions.rpcParams[0];

        var sort = gridOptions.sortName;
        var order = gridOptions.sortOrder;

        TF.Rpc.Payments.PaymentsBankAndNaturaPaymentReport.export(params, sort, order)
            .done(function (dataObj) {
                winDownload.window('open');
                var path = dataObj.file_path;
                _pathFile = path;
                _fileName = dataObj.file_name;
                downloadFile.attr("href", path);
            })
            .fail(function (errorObj) {

            });
    }
}

function exportReportBankPayment() {
    var data = jQuery('#paid-by-bank-report-tables').datagrid('getData');

    if (data.length == 0) {
        jQuery.messager.alert('Грешка', 'Не може да експортнете празна справка.', 'error');
    }
    else {
        var gridOptions = jQuery('#paid-by-bank-report-tables').datagrid('options');
        var winDownload = jQuery('#win-download');
        var downloadFile = jQuery('#btn-download-file');
        var params = gridOptions.rpcParams[0];

        var sort = gridOptions.sortName;
        var order = gridOptions.sortOrder;

        TF.Rpc.Payments.PaymentsBankPaymentReport.export(params, sort, order)
            .done(function (dataObj) {
                winDownload.window('open');
                var path = dataObj.file_path;
                _pathFile = path;
                _fileName = dataObj.file_name;
                downloadFile.attr("href", path);
            })
            .fail(function (errorObj) {

            });
    }
}

function initRestReport() {
    var columns = function () {
        var rentaTypes = ComboboxData.RentaTypesCombobox;
        var returnValues = [];
        var dataPush = [
            {
                field: 'owner_names',
                title: '<b>Собственик</b>',
                sortable: true,
                width: 70,
                align: 'center'
            },
            {
                field: 'egn_eik',
                title: '<b>ЕГН/ЕИК</b>',
                sortable: true,
                width: 55,
                align: 'center'
            },
            {
                field: 'farming_year',
                title: '<b>Стопанска година</b>',
                sortable: true,
                width: 55,
                align: 'center',
            },
            {
                field: 'unpaid_leva',
                title: '<b>В лева</b>',
                sortable: true,
                width: 55,
                align: 'center',
            }
        ];
        rentaTypes.forEach(function (rentaType) {
            if (rentaType.name !== '-')
                dataPush.push(
                    {
                        field: "unpaid_renta_nat_arr_" + rentaType.id,
                        title: '<b>' + rentaType.name + '</b>',
                        sortable: true,
                        width: 60,
                        align: 'center',
                    }
                );
        });

        returnValues.push(dataPush);
        return returnValues;
    };

    var farmingYearComboboxData = ComboboxData.FarmingYearCombobox;
    var currentYear = '';

    farmingYearComboboxData.forEach(function (each) {
        if (each.selected) {
            currentYear = each;
        }
    });

    jQuery("#rest-report-tables").datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 30,
        fit: true,
        fitColumns: true,
        singleSelect: true,
        showFooter: true,
        url: "index.php?payments-rpc=unpaid-rented-report-grid",
        idField: "owner_id",
        sortName: "owner_names",
        sortOrder: "asc",
        border: true,
        rpcMethod: "read",
        rpcParams: [getRestFilterParams()],
        pagination: true,
        rownumbers: true,
        toolbar: "#report_rest_toolbar",
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
        columns: columns()
    });
}

function initRestReportFilters() {
    var farmingComboboxData = ComboboxData.FarmingCombobox;
    var ekateComboboxData = ComboboxData.EkateCombobox;
    var farmingYearComboboxData = ComboboxData.FarmingYearCombobox;
    var farmingYears = [];

    farmingYearComboboxData.forEach(function (year) {
        if (year.id != "") {
            farmingYears.push(year);
        }
    });

    let [currentYear] = farmingYears.filter(year => year.year == new Date().getFullYear());
    const currentYearId = currentYear.id;

    farmingYears.forEach(function (each) {
        if (currentYear.id == each.id) {
            each.selected = true;
        }
    });

    var rest_year = jQuery('#rest-year > input');
    var rest_farming = jQuery('#rest-farming > input');
    var rest_ekate = jQuery('#rest-ekatte > input');


    rest_year.combobox({
        data: farmingYears,
        editable: false,
        valueField: 'id',
        textField: 'farming_year',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
    });

    rest_farming.combobox({
        data: farmingComboboxData,
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        editable: false,
        valueField: 'id',
        textField: 'name',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    rest_ekate.combobox({
        data: ekateComboboxData,
        valueField: 'ekate',
        textField: 'text',
        filter: function (q, row) {
            var opts = jQuery(this).combobox('options'),
                text = row[opts.textField].toLowerCase(),
                value = row[opts.valueField],
                find = q.toLowerCase();
            if (text.indexOf(find) !== -1 || value.indexOf(find) !== -1) {
                return true;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#rest-egn > input').val('');
    jQuery('#rest-eik > input').val('');
    jQuery('#rest-owner-names > input').val('');
}

function clearRestFilter() {
    var farmingYearComboboxData = ComboboxData.FarmingYearCombobox;
    var currentYear = '';

    farmingYearComboboxData.forEach(function (each) {
        if (each.selected) {
            currentYear = each;
        }
    });

    initRestReportFilters();

    if (jQuery('#rest-report-tables').data().hasOwnProperty('datagrid')) {
        jQuery('#rest-report-tables').datagrid('options').rpcParams = [getRestFilterParams()];
        jQuery('#rest-report-tables').datagrid('reload');
    }else{
        initRestReport();
    }

}

function applyFilter() {
    jQuery('#rest-report-tables').datagrid('options').rpcParams = [getRestFilterParams()];
    jQuery('#rest-report-tables').datagrid('reload');
}

function getRestFilterParams() {
    var farmingYearComboboxData = ComboboxData.FarmingYearCombobox;
    var selectedYear = '';
    var comboYear = jQuery('#rest-year > input').combobox('getValue');

    farmingYearComboboxData.forEach(function (year) {
        if (comboYear == year.id) {
            selectedYear = year;
        }
    });
    return {
        payroll_farming_year: comboYear,
        payroll_from_date: selectedYear.start_date,
        payroll_to_date: selectedYear.end_date,
        payroll_farming: jQuery('#rest-farming > input').combobox('getValues'),
        payroll_ekate: jQuery('#rest-ekatte > input').combobox('getValue'),
        egn: jQuery('#rest-egn > input').val(),
        eik: jQuery('#rest-eik > input').val(),
        owner_names: jQuery('#rest-owner-names > input').val(),
        for_export:false
    }
}

function exportReportRest() {
    var data = jQuery('#rest-report-tables').datagrid('getData');

    if (data.length == 0) {
        jQuery.messager.alert('Грешка', 'Не може да експортнете празна справка.', 'error');
    }
    else {
        var gridOptions = jQuery('#rest-report-tables').datagrid('options');
        var winDownload = jQuery('#win-download');
        var downloadFile = jQuery('#btn-download-file');
        var params = gridOptions.rpcParams[0];
        params.for_export = true;

        TF.Rpc.Payments.UnpaidReantaReportGrid.exportExcel(
            params,
            "",
            "",
            gridOptions.sortName,
            gridOptions.sortOrder
        )
            .done(function(dataObj) {
                winDownload.window("open");
                var path = dataObj;
                _pathFile = path;
                downloadFile.attr("href", path);
            })
            .fail(function(errorObj) {});
    }
}

function printReportRest(currentPage) {
    var data = jQuery('#rest-report-tables').datagrid('getData');

    if (data.length == 0) {
        jQuery.messager.alert('Грешка', 'Не може да експортнете празна справка.', 'error');
    }
    else {
        var gridOptions = jQuery('#rest-report-tables').datagrid('options');
        var params = gridOptions.rpcParams[0];
        params.for_export = true;

        var page = '';
        var rows = '';
        if (currentPage) {
            page = gridOptions.pageNumber;
            rows = gridOptions.pageSize;
        }

        TF.Rpc.Payroll.LightPayrollGrid.print(params, page, rows, gridOptions.sortName, gridOptions.sortOrder)
            .done(function (dataObj) {
                jQuery('body').append('<iframe id="printf" name="printf"></iframe>');
                var newWin = window.frames['printf'];
                newWin.document.write('<body onload=window.print()>'+dataObj+'</body>');
                newWin.document.close();
                setTimeout(function () {
                    jQuery('#printf').remove();
                }, 1000);
            })
            .fail(function (errorObj) {
            });
    }
}

function exportPersonalUseReport() {
    var data = jQuery('#personal-use-report-tables').datagrid('getData');

    if (data.length == 0) {
        jQuery.messager.alert('Грешка', 'Не може да експортнете празна справка.', 'error');
    }
    else {
        var gridOptions = jQuery('#personal-use-report-tables').datagrid('options');
        var winDownload = jQuery('#win-download');
        var downloadFile = jQuery('#btn-download-file');
        var params = gridOptions.rpcParams[0];

        var sort = gridOptions.sortName;
        var order = gridOptions.sortOrder;

        TF.Rpc.Payments.PersonalUseReport.export(params,sort,order)
        .done(function (dataObj) {
            winDownload.window('open');
            var path = dataObj.file_path;
            _pathFile = path;
            _fileName = dataObj.file_name;
            downloadFile.attr("href", path);
        })
        .fail(function (errorObj) {

        });
    }
}

function initSearchOwnerFieldComplete() {
    var owners = new Bloodhound({
        datumTokenizer: Bloodhound.tokenizers.whitespace('value'),
        queryTokenizer: Bloodhound.tokenizers.whitespace,
        remote: {
            url: 'index.php?owners-rpc=owners-list',
            cache: false,
            prepare: function (query, settings) {
                settings.query = query;
                return settings;
            },
            transport: function (settings, onSuccess, onError) {
                options = {
                    type: 'POST',
                    dataType: 'json',
                    contentType: "application/json; charset=UTF-8",
                    data: JSON.stringify({
                        "method": "read",
                        "params": ["owners", settings.query],
                        "id": 1,
                        "jsonrpc": "2.0"
                    })
                };

                jQuery.ajax('index.php?owners-rpc=owners-list', options)
                .done(done)
                .fail(fail)
                .always(always);

                function done(data, textStatus, request) {
                    onSuccess(data.result);
                }
                function fail(request, textStatus, errorThrown) {
                    onError(errorThrown);
                }
                function always() {
                }
            },
        },
    });

    jQuery('#search-personal-use-owner-name').typeahead(
    {
        hint: false,
        highlight: false,
        minLength: 1,
        limit: 10,
        menu:jQuery('#search-owner-name-typeahead-target'),
    },
    {
        name: 'owners',
        displayKey: 'value',
        source: owners.ttAdapter(),
    });

    jQuery('#search-personal-use-owner-name').on('typeahead:open', function (e, datum) {
        jQuery('#search-owner-name-typeahead-target').width(jQuery('#search-personal-use-owner-name').width());
        var offset = jQuery('#search-personal-use-owner-name').offset();
        jQuery("#search-owner-name-typeahead-target").offset({
                top: offset.top + jQuery('#search-personal-use-owner-name').height(),
                left: offset.left
            });
        jQuery('#search-owner-name-typeahead-target').css("z-index",9999);
    });

    jQuery('#search-personal-use-owner-name').on('typeahead:selected', function (e, datum) {
        jQuery('#search-personal-use-owner-name').typeahead('close');
        jQuery('#search-owner-name-typeahead-target').css("z-index",-1);
    });

    jQuery('#search-personal-use-owner-name').on('typeahead:idle', function (e, datum) {
            jQuery('#search-personal-use-owner-name').typeahead('close');
            jQuery('#search-owner-name-typeahead-target').css("z-index",-1);
    });
}
