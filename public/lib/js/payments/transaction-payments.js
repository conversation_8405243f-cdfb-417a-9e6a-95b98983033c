function initTransactionPaymentsGrid(transaction_id) {
    jQuery('#transaction-payments-tables').datagrid({
        nowrap: true,
        singleSelect: true,
        pageSize: 30,
        fit: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?payments-rpc=transactions-payments-grid',
        rpcParams: [transaction_id],
        sortName: '',
        sortOrder: '',
        border: false,
        idField: '',
        columns: [[
                {
                    field: 'c_num',
                    title: '<b>Договор</b>',
                    sortable: true,
                    width: 130,
                    align: 'center'
                }, {
                    field: 'farming_id',
                    title: '<b>Стопанство</b>',
                    sortable: true,
                    width: 130,
                    align: 'center'
                }, {
                    field: 'owner',
                    title: '<b>Собственик</b>',
                    sortable: true,
                    width: 150,
                    align: 'center'
                }, {
                    field: 'rep_names',
                    title: '<b>Представител</b>',
                    sortable: true,
                    width: 150,
                    align: 'center'
                }, {
                    field: 'date',
                    title: '<b>Дата</b>',
                    sortable: true,
                    width: 80,
                    align: 'center'
                }, {
                    field: 'paid_from_text',
                    title: '<b>Изплащане на</b>',
                    sortable: true,
                    width: 150,
                    align: 'center'
                }, {
                    field: 'paid_in_text',
                    title: '<b>Изплащане чрез</b>',
                    sortable: true,
                    width: 150,
                    align: 'center'
                }
            ]],
        pagination: true,
        rownumbers: false,
        onBeforeLoad: function() {
            jQuery('#transaction-payments-tables').datagrid('clearChecked');
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}
