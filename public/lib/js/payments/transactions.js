var selectedTransactionForGeneratingDocument,
    initialNumbersEmpty = false;

const TRANSACTION_RKO_MANUAL_REASON = 0;    
function initTransactionsGrid(filterObj, pageNumber) {
    var page_number = 1;

    if (pageNumber != undefined) {
        page_number = pageNumber;
    }

    jQuery('#transactions-tables').datagrid({
        nowrap: true,
        singleSelect: true,
        pageSize: 30,
        fit: true,
        showFooter: false,
        url: 'index.php?payments-rpc=transactions-grid',
        sortName: 'id',
        sortOrder: 'desc',
        idField: 'id',
        border: false,
        rpcParams : [filterObj],
        pageNumber: page_number,
        frozenColumns: [[
            {
                field: 'ck',
                checkbox: true
            }
        ]],
        rowStyler: function (index, row) {
            var style = [];
            if (row.status == false) {
                style.push('text-decoration: line-through');
                style.push('color: #aaa');
            }

            return style.join(';');
        },
        columns: [[
                {
                    field: 'id',
                    title: '<b>№ на <br/>транзакция</b>',
                    sortable: true,
                    width: 75,
                    align: 'center'
                }, {
                    field: 'date',
                    title: '<b>Дата</b>',
                    sortable: true,
                    width: 80,
                    align: 'center'
                }, {
                    field: 'ttype',
                    title: '<b>Вид на<br/>транзакция</b>',
                    sortable: false,
                    width: 130,
                    align: 'center'
                }, {
                    field: 'paid_from_text',
                    title: '<b>Изплащане на</b>',
                    sortable: true,
                    width: 150,
                    align: 'center'
                }, {
                    field: 'paid_in_text',
                    title: '<b>Изплащане чрез</b>',
                    sortable: true,
                    width: 150,
                    align: 'center'
                }, {
                    field: 'payer_name',
                    title: '<b>Изплатено от</b>',
                    sortable: true,
                    width: 150,
                    align: 'center'
                }, {
                    field: 'farmings',
                    title: '<b>Стопанство</b>',
                    sortable: true,
                    width: 180,
                    align: 'center'
                }, {
                    field: 'recipient',
                    title: '<b>Изплатено на</b>',
                    sortable: true,
                    width: 200,
                    align: 'center'
                },{
                    field: 'recipient_egn',
                    title: '<b>ЕГН</b>',
                    sortable: true,
                    width: 100,
                    align: 'center'
                },{
                    field: 'recipient_proxy',
                    title: '<b>Пълномощно №/дата</b>',
                    sortable: true,
                    width: 200,
                    align: 'center'
                }, {
                    field: 'bank_acc',
                    title: '<b>Банкова сметка</b>',
                    sortable: true,
                    width: 150
                }, {
                    field: 'farming_year',
                    title: '<b>Стопанска<br/>година</b>',
                    sortable: true,
                    width: 150,
                    align: 'center'
                }, {
                    field: 'c_num',
                    title: '<b>Договори<br/>',
                    sortable: false,
                    width: 150
                }, {
                    field: 'cancelled_by',
                    title: '<b>Анулирано<br/>от<b/>',
                    sortable: false,
                    width: 150,
                align: 'center'
                }
            ]],
        pagination: true,
        rownumbers: false,
        toolbar: [
            {
                id: 'btnpaymentinfo',
                text: 'Подробна информация',
                iconCls: 'icon-info',
                handler: function() {
                    var getChecked = jQuery('#transactions-tables').datagrid('getChecked');

                    if (getChecked[0]) {
                        jQuery('#win-transaction-payments').window('open')
                        initTransactionPaymentsGrid(getChecked[0].id);
                    } else {
                        jQuery.messager.alert('Грешка', 'Моля изберете транзакция, за която да бъде показана подробна информация!', 'error');
                    }
                }
            },
            {
                id: 'btn-open-transactions-filter',
                text: 'Филтриране',
                iconCls: 'icon-filter',
                handler: function() {
                   jQuery('#win-transactions-filter').window('open');
                }
            },
            {
                id: 'btn-clear-transactions-filter',
                text: 'Покажи всички',
                iconCls: 'icon-clear-filter',
                handler: function() {
                   clearTransactionFilterFields();
                }
            },
            {
                id: 'btngeneratepaymentorder',
                text: 'Генериране платежни документи',
                iconCls: 'icon-payments',
                handler: function() {
                    var getSelected = jQuery('#transactions-tables').datagrid('getSelected');
                    if (!getSelected) {
                        jQuery.messager.alert('Грешка', 'Моля изберете транзакция, за която да бъде генерирано платежно нареждане!', 'error');
                        return false;
                    }
                    if (getSelected.status == false) {
                        jQuery.messager.alert('Грешка', 'Не е възможно да се генерират платежни документи за анулирани транзакции!', 'error');
                        return false;
                    }

                    selectedTransactionForGeneratingDocument = getSelected;
                    generatePaymentOrderFromTransactionGrid(getSelected);
                }
            },
            {
                id: 'btn-disable-transaction',
                text: 'Анулиране на транзакция',
                iconCls: 'icon-cancel',
                handler: function() {
                    var getSelected = jQuery('#transactions-tables').datagrid('getSelected');

                    if (getSelected) {
                        disableTransaction(getSelected, filterObj);
                    } else {
                        jQuery.messager.alert('Грешка', 'Моля изберете транзакция, за която да бъде анулирана!', 'error');
                    }
                }
            },
            {
                id: 'btntransactionsexportexcel',
                text: 'Експорт(xls)',
                iconCls: 'icon-csv',
                handler: function() {
                    var transactionGrid = jQuery('#transactions-tables');
                    var winDownload = jQuery('#win-download');
                    var downloadFile = jQuery('#btn-download-file');
                    var gridOptions = transactionGrid.datagrid('options');
                    var data = transactionGrid.datagrid('getData');

                    var params = {};
                    params.data = gridOptions.rpcParams[0];
                    params.sort = gridOptions.sortName;
                    params.order = gridOptions.sortOrder;

                    if (data.length == 0) {
                        jQuery.messager.alert('Грешка', 'Няма налични трансакции!', 'error');
                    }
                    else {
                        TF.Rpc.Payments
                            .TransactionsGrid
                            .exportToExcelTransactionsGrid(params)
                            .done(function (dataObj) {
                                winDownload.window('open');
                                var path = dataObj.file_path;
                                _pathFile = path;
                                _fileName = dataObj.file_name;
                                downloadFile.attr("href", path);
                            })
                            .fail(function (errorObj) {});
                        }
                    }
            }
        ],
        onBeforeLoad: function() {
            if(jQuery(this).datagrid('options').data !== null){
                jQuery(this).datagrid('clearChecked');
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

jQuery(function () {

    var date = new Date();
    var todayDate = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();

    jQuery('#payment-order-selected-date').datebox({
        required: true,
        missingMessage: 'Моля изберете дата на платежното нареждане.',
        value: todayDate
    });

    jQuery('#include-date-in-payment-documents-date').datebox({
        required: true,
        missingMessage: 'Моля изберете дата на документ.',
        value: todayDate
    });

    jQuery('#payment-oder-with-date-checkbox').change(function () {
        if (jQuery('#payment-oder-with-date-checkbox').prop('checked')) {
            jQuery('#payment-order-selected-date').datebox('enable');
        } else {
            jQuery('#payment-order-selected-date').datebox('disable');
        };
    });

    jQuery('#include-date-in-payment-documents-checkbox').change(function () {
        if (jQuery('#include-date-in-payment-documents-checkbox').prop('checked')) {
            jQuery('#include-date-in-payment-documents-date').datebox('enable');
        } else {
            jQuery('#include-date-in-payment-documents-date').datebox('disable');
        };
    });

    jQuery('#createDocumentWithoutRkoNumbering').change(function () {
        if (jQuery('#createDocumentWithoutRkoNumbering').prop('checked')) {
            clearRkoTransNumbers();
            jQuery('#generateTransRkoNumbers').linkbutton('disable');
            jQuery('#clearTransRkoNumbers').linkbutton('disable');
        } else {
            generateRkoTransNumbers();
            jQuery('#generateTransRkoNumbers').linkbutton('enable');
            jQuery('#clearTransRkoNumbers').linkbutton('enable');
        }
    });

    jQuery('#transaction-generate-rko').change(function (e) {
        e.preventDefault();
        var rkoChckBox = jQuery('#transaction-generate-rko').is(':visible'),
            checkedRKO = jQuery('#transaction-generate-rko').is(':checked'),
            wnChckBox = jQuery('#transaction-generate-rko-weighing-note').is(':visible');
        if (rkoChckBox && wnChckBox) {
            if (!checkedRKO) {
                jQuery('#createDocumentWithoutRkoNumbering').prop('checked', true);
                jQuery('#createDocumentWithoutRkoNumbering').trigger('change');
                jQuery('#rkoTransactionNumberingFields').hide();
                jQuery('#transaction-payment-subject-dropdown').combobox('reset');
                jQuery('#transaction-payment-subject-dropdown').combobox('disable');
                jQuery('#createDocumentWithoutRkoNumbering').prop('checked', false);
                jQuery('#tr-collect-all-payment-amounts').prop('disabled', true);

                jQuery('#win-generate-payment-document-from-transaction').window('resize', {
                    height: jQuery('#btn-payment-order-generate').offset().top - jQuery('#transactionWindowTop').offset().top + 89
                });
            } else {
                jQuery('#rkoTransactionNumberingFields').show();
                jQuery('#transaction-payment-subject-dropdown').combobox('select', -1);
                jQuery('#transaction-payment-subject-dropdown').combobox('enable');
                jQuery('#createDocumentWithoutRkoNumbering').prop('checked', false);
                jQuery('#createDocumentWithoutRkoNumbering').trigger('change');
                jQuery('#tr-collect-all-payment-amounts').prop('disabled', false);
            }
        }
        if (rkoChckBox && !wnChckBox && !checkedRKO) {
            jQuery('#transaction-generate-rko').prop('checked',true);
        }
    });

    jQuery('#transaction-bank-payment-order').change(function (e) {
        e.preventDefault();
        if (jQuery('#transaction-bank-payment-order').is(':visible')) {
            jQuery('#transaction-bank-payment-order').prop('checked', true);
        };
    });

    jQuery('#payment-order-farming-iban').combobox({
        url: 'index.php?farming-rpc=farming-iban',
        valueField: 'value',
        textField: 'text',
        multiple: false,
        editable: false,
        rpcParams: [null],
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#transaction-payment-subject-dropdown').combobox({
        url: 'index.php?common-rpc=payment-subjects-combobox',
        valueField: 'id',
        textField: 'name',
        multiple: false,
        editable: false,
        rpcParams: [{
            selected: false

        }],
        onChange: function (newValue, oldValue) {
            if (newValue === TRANSACTION_RKO_MANUAL_REASON) {
                jQuery('#transactions-payment-subjects-text-row').show();
                jQuery('#win-generate-payment-document-from-transaction').window('resize', {
                    height: jQuery('#btn-payment-order-generate').offset().top - jQuery('#transactionWindowTop').offset().top + 89
                });
            } else {
                jQuery('#transactions-payment-subjects-text-row').hide();
                jQuery('#transactions-payment-subjects-text').val('');
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

});

function disableTransaction(transaction, filterObj) {
    if (transaction.id) {
        TF.Rpc.Payments.TransactionsGrid.disableTransaction(transaction.id)
            .done(function (data) {
                initTransactionsGrid(filterObj);
                jQuery('#search-transaction-disabled-by').combobox('reload');
            })
            .fail(function (errorObj) {
                jQuery.messager.alert('Внимание', errorObj.getMessage(), 'warning');
            });
    };
}

function generatePaymentOrderFromTransactionGrid(transaction) {

    if (transaction.status == false) {
        jQuery.messager.alert('Грешка', 'Моля изберете транзакция, за която да бъде генерирано платежно нареждане!', 'error');
        return false;
    }

    var bankPaymentOrder = jQuery('#transaction-bank-payment-order');
    var bankPaymentOrderWeighingNote = jQuery('#transaction-bank-payment-order-weighing-note');
    var generateRko = jQuery('#transaction-generate-rko');
    var generateRkoWeighingNote = jQuery('#transaction-generate-rko-weighing-note');

    jQuery(bankPaymentOrder).prop('checked', false);
    jQuery(bankPaymentOrderWeighingNote).prop('checked', false);
    jQuery(generateRko).prop('checked', false);
    jQuery(generateRkoWeighingNote).prop('checked', false);

    if (transaction.bank_payment && transaction.bank_payment_type == 0) {
        jQuery('#payment-order-farming-iban').combobox('loadRpc', transaction.farming_ids);
        jQuery('#generate-bank-payment-documents').show();
        jQuery('#generate-non-bank-payment-documents').hide();
        jQuery('#generate-bank-payment-documents-payment-subjects-input').hide();
        jQuery('#generate-bank-payment-documents-payment-subjects-free-text-input').show();
        jQuery('#rkoTransactionNumberingFields').hide();
        jQuery("#post-recipient-iban").hide();  

        jQuery("#sender-iban").show();
        jQuery("#transaction-date").show();
        
    } else if(transaction.bank_payment && transaction.bank_payment_type == 1) {
        jQuery('#generate-post-payment').show();
        jQuery("#post-recipient-iban").show();  


        jQuery('#generate-non-bank-payment-documents').hide();
        jQuery('#generate-bank-payment-documents-payment-subjects-input').hide();
        jQuery('#generate-bank-payment-documents-payment-subjects-free-text-input').hide();
        jQuery('#rkoTransactionNumberingFields').hide();
        bankPaymentOrder.hide();
        jQuery('#generate-bank-payment-documents-bank').hide();
        jQuery('#generate-bank-payment-documents-rko-weighing').hide();
        jQuery('#generate-bank-payment-documents-bank-weighing').hide();
        jQuery("#sender-iban").hide();
        jQuery("#transaction-date").hide();  
    } else {
        jQuery('#generate-bank-payment-documents').hide();
        jQuery("#post-recipient-iban").hide();  

        jQuery('#generate-non-bank-payment-documents').show();
        jQuery('#rkoTransactionNumberingFields').show();
        jQuery('#generate-bank-payment-documents-payment-subjects-input').show();
        jQuery("#transaction-date").show();

    }

    if (transaction.bank_payment_type == 0) {
        if (transaction.paid_from == 1) {
            if (transaction.paid_in == 1) {
                jQuery('#generate-bank-payment-documents-bank-weighing').hide();
                jQuery('#generate-bank-payment-documents-rko-weighing').hide();
                jQuery('#generate-bank-payment-documents-bank').show();
                jQuery('#generate-bank-payment-documents-rko').show();
                // jQuery('#tr-rko-type-row').show();
            } else {
                jQuery('#generate-bank-payment-documents-bank-weighing').show();
                jQuery('#generate-bank-payment-documents-rko-weighing').show();
                jQuery('#generate-bank-payment-documents-bank').hide();
                jQuery('#generate-bank-payment-documents-rko').show();
                // jQuery('#tr-rko-type-row').show();
            }
        } else {
            if (transaction.paid_in == 1) {
                jQuery('#generate-bank-payment-documents-bank-weighing').show();
                jQuery('#generate-bank-payment-documents-rko-weighing').show();
                jQuery('#generate-bank-payment-documents-bank').show();
                jQuery('#generate-bank-payment-documents-rko').show();
                // jQuery('#tr-rko-type-row').show();
                jQuery('#generate-bank-payment-documents-payment-subjects-input').show();
            } else {
                jQuery('#generate-bank-payment-documents-bank-weighing').show();
                jQuery('#generate-bank-payment-documents-rko-weighing').show();
                jQuery('#generate-bank-payment-documents-bank').hide();
                jQuery('#generate-bank-payment-documents-rko').hide();
                // jQuery('#tr-rko-type-row').hide();
                jQuery('#generate-bank-payment-documents-payment-subjects-input').hide();
                jQuery('#rkoTransactionNumberingFields').hide();
            }
        }
    }

    clearRkoTransNumbers();
    generateRkoTransNumbers();
}

function generatePaymentDocumentFromTransactionGrid() {
    if (!jQuery('#transaction-generate-rko').is(':visible')) {
        createExportDocuments();
    } else {
        if (jQuery('#transaction-generate-rko').is(':checked')) {
            var rko_numbers = getPaymentTransNumbering();
            TF.Rpc.Payments
            .ExportPayment
            .updatePaymentRkoNumbers(rko_numbers)
            .done(function (dataObj) {
                createExportDocuments();
            })
            .fail(function () {});
        } else {
            createExportDocuments();
        }
    }
}

/*
    Комбинации от платежни документи:
        Плащане по банков път:
            1 - Генериране на платежно нареждане
            2 - Генериране на кантарна бележка
            3 - Генериране на платежно нареждане и кантарна бележка

        Плащане в брой:
            4 - Генериране на РКО
            5 - Генериране на кантарна бележка
            6 - Генериране на РКО и кантарна бележка
 */
function createExportDocuments() {
    var winDownload = jQuery('#win-download'),
        downloadFile = jQuery('#btn-download-file'),

        transaction = selectedTransactionForGeneratingDocument,
        documentType = 0,

        includeDate = jQuery('#include-date-in-payment-documents-checkbox').prop('checked'),
        selectedDate = jQuery('#include-date-in-payment-documents-date').datebox('getValue'),


        bankPaymentOrder = jQuery('#transaction-bank-payment-order'),
        bankPaymentOrderWeighingNote = jQuery('#transaction-bank-payment-order-weighing-note'),
        generateRko = jQuery('#transaction-generate-rko'),
        generateRkoWeighingNote = jQuery('#transaction-generate-rko-weighing-note'),

        generateWithoutRkoNumbering = jQuery('#createDocumentWithoutRkoNumbering').prop('checked');
        collectAllPaymentAmounts = jQuery('#tr-collect-all-payment-amounts').prop('checked');

    if (jQuery('#transactions-tables').datagrid('getSelected').bank_payment) {
        paymentSubjectId = null;
        paymentSubjectText = jQuery('#transaction-payment-subject-free-text').val();
    }else {
        paymentSubjectId = jQuery('#transaction-payment-subject-dropdown').combobox('getValue');
        paymentSubjectText = jQuery('#transactions-payment-subjects-text').val();
    }

    selectedDate = includeDate ? selectedDate : includeDate;

    if (transaction.bank_payment && transaction.bank_payment_type == 0) {

        var farmingIban = jQuery('#payment-order-farming-iban').combobox('getValue');
        var bankPayment = jQuery(bankPaymentOrder).prop('checked');
        var bankPaymentWeighingNote = jQuery(bankPaymentOrderWeighingNote).prop('checked');

        if (bankPayment && !bankPaymentWeighingNote)
            documentType = 1;
        if (!bankPayment && bankPaymentWeighingNote)
            documentType = 2;
        if (bankPayment && bankPaymentWeighingNote)
            documentType = 3;

    } else if (transaction.bank_payment && transaction.bank_payment_type == 1) {
        documentType = 7;
    } else {
        farmingIban = null;
        var rko = jQuery(generateRko).is(':checked');
        var rkoWeighingNote = jQuery(generateRkoWeighingNote).is(':checked');

        if (!jQuery(generateRko).is(':visible')) {
            rko = false;
        }

        if (!jQuery(generateRkoWeighingNote).is(':visible')) {
            rkoWeighingNote = false;
        }

        if (rko && !rkoWeighingNote)
            documentType = 4;
        if (!rko && rkoWeighingNote)
            documentType = 5;
        if (rko && rkoWeighingNote)
            documentType = 6;
    };
    switch (documentType) {
        case 1:
            //Генериране на платежно нареждане
            TF.Rpc.Payments
                .ExportPayment
                .exportToPdfBankPaymentOrder(transaction.id, selectedDate, farmingIban, paymentSubjectId, paymentSubjectText)
                .done(function (dataObj) {
                    winDownload.window('open');
                    var path = dataObj.file_path;
                    _pathFile = path;
                    _fileName = dataObj.file_name;
                    downloadFile.attr("href", path);
            })
            .fail(function (errorObj) {});
            break;
        case 2:
            //Генериране на кантарна бележка
            TF.Rpc.Payments
                .ExportPayment
                .exportToPdfWeighingNote(transaction.id, selectedDate)
                .done(function (dataObj) {
                    winDownload.window('open');
                    var path = dataObj.file_path;
                    _pathFile = path;
                    _fileName = dataObj.file_name;
                    downloadFile.attr("href", path);
                })
                .fail(function (errorObj) {

                });
            break;
        case 3:
            //Генериране на платежно нареждане и кантарна бележка
            TF.Rpc.Payments
                .ExportPayment
                .exportToPdfBankCombined(transaction.id, selectedDate, farmingIban, paymentSubject, $paymentSubjectText)
                .done(function (dataObj) {
                    winDownload.window('open');
                    var path = dataObj.file_path;
                    _pathFile = path;
                    _fileName = dataObj.file_name;
                    downloadFile.attr("href", path);
                })
                .fail(function (errorObj) {

                });
            break;
        case 4:

            var rko_type = jQuery('input[name="tr_rko_type"]:checked').val();
            var rko_type_declaration = jQuery('#tr_rko_type_declaration').is(':checked');

            //Генериране на РКО
            TF.Rpc.Payments
                .ExportPayment
                .exportToPdfPaymentOrder(
                    transaction.id,
                    null,
                    selectedDate, 
                    paymentSubjectId, 
                    paymentSubjectText, 
                    generateWithoutRkoNumbering, 
                    false, 
                    [], 
                    false, 
                    collectAllPaymentAmounts, 
                    rko_type, 
                    rko_type_declaration, 
                    ''
                )
                .done(function (dataObj) {
                    winDownload.window('open');
                    var path = dataObj.file_path;
                    _pathFile = path;
                    _fileName = dataObj.file_name;
                    downloadFile.attr("href", path);
                })
                .fail(function (errorObj) {});
            break;
        case 5:
            //Генериране на кантарна бележка
            TF.Rpc.Payments
                .ExportPayment
                .exportToPdfWeighingNote(transaction.id, selectedDate)
                .done(function (dataObj) {
                    winDownload.window('open');
                    var path = dataObj.file_path;
                    _pathFile = path;
                    _fileName = dataObj.file_name;
                    downloadFile.attr("href", path);
                })
                .fail(function (errorObj) {

                });
            break;
        case 6:
            //Генериране на РКО и кантарна бележка
            TF.Rpc.Payments
                .ExportPayment
                .exportToPdfCombined(transaction.id,selectedDate, paymentSubjectId, paymentSubjectText, generateWithoutRkoNumbering)
                .done(function (dataObj) {
                    winDownload.window('open');
                    var path = dataObj.file_path;
                    _pathFile = path;
                    _fileName = dataObj.file_name;
                    downloadFile.attr("href", path);
                })
                .fail(function (errorObj) {

                });
            break;
        case 7:

            const senderData = JSON.parse(transaction.sender_post_payment_fields);
            const recipientData =  JSON.parse(transaction.owner_post_payment_fields);
            recipientData.name = transaction.recipient;
            recipientData.iban = jQuery("#post-recipient-iban-value").val();
            TF.Rpc.Payments.ExportPayment.exportToPdfPostPaymentOrder(
                transaction.id,
                selectedDate,
                senderData,
                recipientData
            )
                .done(function(dataObj) {
                    winDownload.window("open");
                    var path = dataObj.file_path;
                    _pathFile = path;
                    _fileName = dataObj.file_name;
                    downloadFile.attr("href", path);
                })
                .fail(function(errorObj) {});
                break;
        default:
            jQuery.messager.alert('Грешка', 'Изберете тип на документ за генериране', 'warning');
            break;
    }
}

function clearRkoTrnasNumbering(element) {
    var id = parseInt(element.id.split('-')[1]);
    jQuery('#rko-trans-number-'+id).val('');
}

function clearRkoTransNumbers() {
    jQuery('#rko-trans-numbering').html('');
    jQuery('#win-generate-payment-document-from-transaction').window('resize', {
        height: jQuery('#btn-payment-order-generate').offset().top - jQuery('#transactionWindowTop').offset().top + 89
    });
    jQuery('#win-generate-payment-document-from-transaction').window('center');
}

function generateRkoTransNumbers() {
    TF.Rpc.Payments.ExportPayment.getTransactionPaymentNumbers(selectedTransactionForGeneratingDocument.id)
    .done(function (data) {
        jQuery('#win-generate-payment-document-from-transaction').window('open');

        setDisabledButtons();

        generateTransNumberingTemplate(data);
        setSubjectText();
    })
    .fail(function (errorObj) {
        jQuery.messager.alert('Внимание', errorObj.getMessage(), 'warning');
    });
}

function generateTransNumberingTemplate(rows) {
    clearRkoTransNumbers();
    var originalHeight = jQuery('#win-generate-payment-document-from-transaction').height();
    var originalWidth = jQuery('#win-generate-payment-document-from-transaction').width();
    var parent = jQuery('#rko-trans-numbering');
    for (var i = 1; i <= rows.length; i++) {
        var tr = jQuery('<tr></tr>');
        tr.addClass('rkoTransNumberingRow');
        var td1 = jQuery('<td></td>');
        td1.id = "payment-trans-number-" + i;
        td1.css({"text-align": "right", "width": "125px"});
        td1.html('Плащане №'+i);
        tr.append(td1);

        var td2 = jQuery('<td></td>');
        td2.append('<input type="text" id="rko-trans-number-'+i+'" data-paymentId="0">');

        tr.append(td2);

        var td3 = jQuery('<td></td>');
        td3.css({"padding-right":"10px"});
        var clearBtn = jQuery('<a href="javaScript:void(0)" onClick="clearRkoTrnasNumbering(this)" title="Изчисти полето" class="easyui-linkbutton" data-options="iconCls:\'icon-cancel\'" style="width: 24px;" id="clearRko-'+i+'">&nbsp;</a>')
        .appendTo(td3);
        clearBtn.linkbutton();
        tr.append(td3);
        parent.append(tr);
    }

    populateRkoTranFields(rows);

    jQuery('#win-generate-payment-document-from-transaction').window('resize', {
        height: jQuery('#btn-payment-order-generate').offset().top - jQuery('#transactionWindowTop').offset().top + 89
    });
    jQuery('#win-generate-payment-document-from-transaction').window('resize', {
        height: jQuery('#btn-payment-order-generate').offset().top - jQuery('#transactionWindowTop').offset().top + 89
    });
    jQuery('#win-generate-payment-document-from-transaction').window('center');
}

function populateRkoTranFields(rows) {
    var allEmpty = true;
    for (var i = 0; i < rows.length; i++) {
        jQuery('#rko-trans-number-'+(i+1)).val(rows[i]['rko_number']);
        jQuery('#rko-trans-number-'+(i+1)).data('id', rows[i]['id']);
        jQuery('#rko-trans-number-'+(i+1)).data('originalRkoNum', rows[i]['rko_number']);
        if (rows[i]['rko_number'] != '' && rows[i]['rko_number'] != null) {
            allEmpty = false;
        };
    };
    jQuery('#transaction-generate-rko').prop('checked', true);

    if (allEmpty) {
       TF.Rpc.Payments.ExportPayment.getNewTransactionRkoNumbers(selectedTransactionForGeneratingDocument.id)
           .done(function (data) {
            initialNumbersEmpty = true;
            generateTransNumberingTemplate(data);
        })
       .fail(function (errorObj) {
            jQuery.messager.alert('Внимание', errorObj.getMessage(), 'warning');
        });
    };
}
function getPaymentTransNumbering() {
    var rows = jQuery('.rkoTransNumberingRow'),
        output = [];

    for (var i = 0; i < rows.length; i++) {
        var obj = {};
        obj['rko_number'] = jQuery('#rko-trans-number-'+(i+1)).val();
        obj['id'] = jQuery('#rko-trans-number-'+(i+1)).data('id');
        output.push(obj);
    };

    return output;
}

function emptyRkoTransNumbers() {
    var rows = jQuery('.rkoTransNumberingRow');

    for (var i = 0; i < rows.length; i++) {
        jQuery('#rko-trans-number-'+(i+1)).val('');
    };
}

function setSubjectText() {
    jQuery('#transaction-payment-subject-free-text').val('');    
}

function setDisabledButtons() {
    var rko = jQuery('#transaction-generate-rko'),
        note = jQuery('#transaction-generate-rko-weighing-note'),
        bank = jQuery('#transaction-bank-payment-order'),
        collectPayments = jQuery('#tr-collect-all-payment-amounts'),
        rkoVisible = rko.is(':visible'),
        noteVisible = note.is(':visible'),
        bankVisible = bank.is(':visible');

    rko.attr('disabled', false);
    note.attr('disabled', false);
    bank.attr('disabled', false);

    if (rkoVisible && !bankVisible && !noteVisible) {
        rko.attr('disabled', true);
    }

    if (bankVisible && !rkoVisible && !noteVisible) {
        clearRkoTransNumbers();
        bank.prop('checked', true);
        bank.attr('disabled', true);
    }

    if (rkoVisible && !bankVisible && noteVisible) {
        rko.prop('checked', true);
        note.prop('checked', false);
        collectPayments.prop('disabled', false);
        jQuery('#createDocumentWithoutRkoNumbering').prop('checked', false);
        jQuery('#generateTransRkoNumbers').linkbutton('enable');
        jQuery('#clearTransRkoNumbers').linkbutton('enable');
        jQuery('#transaction-payment-subject-dropdown').combobox('enable');
        jQuery('#transaction-payment-subject-dropdown').combobox('select', -1);
    }

    if (!rkoVisible && !bankVisible && noteVisible) {
        rko.prop('checked', false);
        bank.prop('checked', false);
        note.prop('checked', true);
        note.attr('disabled', true);
        collectPayments.prop('disabled', true);
        collectPayments.prop('checked', false);
    }
}
