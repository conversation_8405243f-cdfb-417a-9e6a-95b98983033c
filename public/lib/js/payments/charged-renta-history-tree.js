function initChargedRentaHistoryTree(pageNumber, filterObj) {
	selectContractID = undefined;

	var page_number = pageNumber|| 1;
	var chargedRentaTree = jQuery('#charged-renta-history-tree');
	var chargedRentaGrid = jQuery('#charged-ranta-history-tables');
	var isTreeBound = chargedRentaTree.data().hasOwnProperty('tree');

	if (isTreeBound) {
		chargedRentaTree.tree({
			rpcParams: [filterObj],
			page: page_number
		});
		return;
	}

	//duplicate request
	initChargedRentaHistoryGrid(0, '1');

	chargedRentaTree.tree({
		url: 'index.php?payments-rpc=charged-renta-history-tree',
		animate: false,
		lines: true,
		sort: 'scrp.id',
		order: 'desc',
		page: page_number,
		rows: 20,
		pagination: true,
		rpcParams: [filterObj],
		onSelect: function(node) {
			//fill charged renta history info
			fillChargedRentaHistoryInfo(node.attributes);
			initChargedRentaHistoryGrid(node.attributes.id, 1);
		},
		onLoadSuccess: function(node, data) {
			var roots = chargedRentaTree.tree('getRoots');
			var options = jQuery(this).tree('options');

			var total = 0;
			var limit = options.rows;

			if (roots.length) {

				var getSelected = chargedRentaTree.tree('getSelected');

				if(!getSelected) {
					chargedRentaTree.tree('select', roots[0].target);
				}

				total = roots[0].attributes.pagination.total;
				limit = roots[0].attributes.pagination.limit;
			}
            else
            {
                //fill charged renta history info
				initChargedRentaHistoryGrid(0,1);
                fillChargedRentaHistoryInfo();
            }

			//init pagination with total charged renta history elements
			initChargedRentaHistoryTreePagination(total, limit);
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function fillChargedRentaHistoryInfo(data)
{
    if(!data)
    {
        jQuery('#icr-type').html('');
        jQuery('#icr-year').html('');
        jQuery('#icr-owner-name').html('');
        jQuery('#icr-owner-egn').html('');
        jQuery('#icr-rep-name').html('');
        jQuery('#icr-rep-egn').html('');
        jQuery('#icr-company-name').html('');
        jQuery('#icr-company-eik').html('');
        jQuery('#icr-cnum').html('');
        jQuery('#icr-contract-type').html('');
        jQuery('#icr-contract-group').html('');
        jQuery('#icr-farming').html('');
        jQuery('#icr-natura').html('');
        jQuery('#icr-natura-type').html('');
        jQuery('#icr-plot').html('');
        jQuery('#icr-plot-ekatte').html('');
        jQuery('#icr-plot-masiv').html('');
        jQuery('#icr-plot-number').html('');
        jQuery('#icr-plot-category').html('');
        jQuery('#icr-plot-area-type').html('');
        jQuery('#icr-renta').html('');
        jQuery('#icr-type-col-natura').html('');
        jQuery('#icr-with-overall-renta').html('');
        jQuery('#icr-owner-type').html('');

        return false;
    }

    var tree = jQuery('#charged-renta-history-tree').tree('getSelected');
    var natura = tree.data.natura;
	var categories = tree.data.category;
	var renta = tree.data.renta;
	var dataNatura = tree.dataNatura || '';

	if(!renta)
	{
		renta = 'невъведена';
	}

	jQuery('#icr-type').html(data.type);
	jQuery('#icr-year').html(data.farming_year);

	jQuery('#icr-owner-name').html(data.owner_id);
	jQuery('#icr-owner-egn').html(tree.data.owner_egn);

	jQuery('#icr-rep-name').html(data.rep_id);
	jQuery('#icr-rep-egn').html(tree.data.rep_egn);

	jQuery('#icr-company-name').html(data.company_id);
	jQuery('#icr-company-eik').html(data.company_eik);

	jQuery('#icr-cnum').html(tree.data.c_num);

	jQuery('#icr-contract-type').html(tree.attributes.c_type);
	jQuery('#icr-contract-group').html(tree.attributes.c_group_name);
	jQuery('#icr-farming').html(data.farming_id);

	jQuery('#icr-natura').html(data.natura);
    jQuery('#icr-natura-type').html(data.natura_type);

    jQuery('#icr-plot').html(data.plot);
    jQuery('#icr-plot-ekatte').html(data.ekate);
    jQuery('#icr-plot-masiv').html(data.masiv);
    jQuery('#icr-plot-number').html(data.number);
    jQuery('#icr-plot-category').html(data.category);
    jQuery('#icr-plot-area-type').html(data.area_type);
    jQuery('#icr-renta').html(renta);
    jQuery('#icr-type-col-natura').html(data.renta_type_amount);
    jQuery('#icr-with-overall-renta').html(data.with_overall_renta ? 'Да' : 'Не');

    switch (data.owner_type) {
        case 0:
            jQuery('#icr-owner-type').html('Юридически лица');
            break;
        case 1:
            jQuery('#icr-owner-type').html('Физически лица');
            break;
        default:
            jQuery('#icr-owner-type').html('-');
    }

}

function initChargedRentaHistoryTreePagination(total, limit) {
	jQuery('#charged-renta-history-tree-pagination').pagination({
		showPageList: false,
		showRefresh: false,
		displayMsg: '',
		total: total,
		pageSize: limit,
		onSelectPage: function(pageNumber, pageSize) {
			var obj = getRentaHistoryFilterParams();
			initChargedRentaHistoryTree(pageNumber, obj);
		}
	});
}
