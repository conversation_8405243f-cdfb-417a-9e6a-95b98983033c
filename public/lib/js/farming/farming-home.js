Namespace('TF.Rpc.Farming');

var masterformid;
var editPlotID;
var representativeId = null;
var isSuperAdmin = false;
var downloadPath = null;

jQuery(function(){
    setFarmingUsers();

    jQuery('#farming-name > input').textbox({
        required: true,
        missingMessage: 'Въведете име на стопанството! (пример: Източно стопанство)'
    });

    numLengthbox();

    new EgnValidateBox('#farming-mol-egn > input');

    setUserRights();

    jQuery('#farming-tables').datagrid({
        title:'Стопанства',
        iconCls:'icon-agriculture',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        singleSelect: true,
        pageSize: 20,
        fit: true,
        fitColumns: true,
        showFooter: true,
        url:'index.php?farming-rpc=farming-grid',
        sortName: 'name',
        sortOrder: 'asc',
        rpcParams: [{}],
        idField:'id',
        frozenColumns:[[
        {
            field:'ck',
            checkbox:true
        },
        ]],
        columns:[[
        {
            field:'name',
            title:'<b>Име</b>',
            sortable:true,
            width:200
        },{
            field:'address',
            title:'<b>Адрес на стопанството</b>',
            sortable:true,
            width:200
        },{
            field:'company',
            title:'<b>Фирма</b>',
            sortable:true,
            width:200
        },{
            field:'bulstat',
            title:'<b>Булстат</b>',
            sortable:true,
            width:200
        },{
            field:'company_address',
            title:'<b>Адрес на фирмата</b>',
            sortable:true,
            width:200
        },{
            field:'mol',
            title:'<b>МОЛ</b>',
            sortable:true,
            width:200
        },{
            field:'mol_egn',
            title:'<b>МОЛ ЕГН</b>',
            sortable:true,
            width:200
        },{
            field:'iban_text',
            title:'<b>Банкови сметки</b>',
            sortable:false,
            width:400
        }
        ]],
        onBeforeLoad: function(){
        	jQuery('#farming-tables').datagrid('clearChecked');
        },
        onLoadSuccess: function(){
            if(!hasPlotRightsR) {
                jQuery('#farming-add-mol').tooltip({
                    position: 'right',
                    content: 'Нямате права за подмодул Собственици',
                });
                jQuery('#farming-add-mol').linkbutton('disable')
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
        pagination:true,
        rownumbers:true,
        toolbar: [{
            id:'btnedit',
            text:'Редактиране',
            iconCls:'icon-edit',
            handler:function(){
                var getSelected =jQuery('#farming-tables').datagrid('getSelected');
                if(getSelected){
                    setEditFarmingFields(getSelected);
                    jQuery('#btn-edit-farming').show();
                    jQuery('#btn-add-farming').hide();
                    jQuery('#win-add-farming').window('open');
                } else {
                	jQuery.messager.alert('Грешка', 'Моля изберете стопанство, което искате да редактирате.');
                }
            }
        },{
            id:'btnexport',
            text:'Експорт на договори',
            iconCls:'icon-csv',
            handler:function(){
                var getSelected =jQuery('#farming-tables').datagrid('getSelected');
                if(getSelected){
                    exportFarmingToExcel();
                } else {
                    jQuery.messager.alert('Грешка', 'Моля изберете стопанство, което искате да редактирате.');
                }
            }
        }]
    });

    jQuery('#farming-add-mol').linkbutton({
        onClick: function () {
            initOwnersRepsGrid();
            jQuery('#win-set-owner-rep').window('open');
        }
    });

    jQuery('#btn-add-farming').bind('click', function() {
        if(!validateFarmingFields()) {
            jQuery.messager.alert('Грешка','Моля попълнете всички задължителни полета.');
            return;
        }

        var obj = getAddEditFarmingFields();


        for(var bank of obj.iban_arr) {
            if(bank.bic.length > 0 && bank.bic.length !== 8){
                jQuery.messager.alert('Грешка', "Полето BIC трябва да е 8 символа. Вашата стойност: " + bank.bic, 'warning');
                return false;
            }
        }

        TF.Rpc.Farming.FarmingGrid.add(obj)
        .done(function (data) {
            jQuery('#win-add-farming').window('close');
            jQuery('#farming-tables').datagrid('loadRpc');
        })
        .fail(function (data) {
            if (data.getCode() == -33040) {
                jQuery.messager.alert('Грешка','Достигнат е максимален брой стопанства за потребителя.','warning');
            } else {
                jQuery.messager.alert('Грешка','Възникна грешка при обработка на данните.');
            }
        });
    });

    jQuery('#btn-edit-farming').bind('click', function() {
        var getSelected =jQuery('#farming-tables').datagrid('getSelected');
        editObj = getAddEditFarmingFields();

        for(var bank of editObj.iban_arr) {
            if(bank.bic.length > 0 && bank.bic.length !== 8){
                jQuery.messager.alert('Грешка', "Полето BIC трябва да е 8 символа. Вашата стойност: " + bank.bic, 'warning');
                return false;
            }
        }

        editObj.id = getSelected.id;

        if(!validateFarmingFields()) {
            jQuery.messager.alert('Грешка','Моля попълнете всички задължителни полета.');
            return;
        }

        TF.Rpc.Farming.FarmingGrid.edit(editObj)
        .done(function (data) {
            jQuery('#win-add-farming').window('close');
            jQuery('#farming-tables').datagrid('loadRpc');
        })
        .fail(function (data) {
            jQuery.messager.alert('Грешка','Възникна грешка при обработка на данните.');
        });
    });

    jQuery('#add-iban-btn').on('click', onAddIbanClick);
    jQuery(document).on('click', '.remove-iban-btn', onRemoveIbanClick);
});

/**
 *
 * @param   {[? integer]}  farmingId 
 *
 * @return  {[void]} 
 */
function setFarmingUsers(farmingId = null) 
{
    let userLevelNum = this.getUserLevel();
    let disableCombo = false;
    if ([USERS_NORMAL, USERS_ADMIN_FLAG].includes(userLevelNum)) {    
        if (userLevelNum === USERS_NORMAL) {
            disableCombo = true;
        }
        jQuery('#farming-users').parent().show();
    } else {
        jQuery('#farming-users').parent().hide();
        return;
    }

    TF.Rpc.Farming.FarmingGrid.getFarmingUsers({'farmingId': farmingId})
    .done(function (data) {
        initUsersCombo(data, farmingId, disableCombo);
    })
    .fail(function (data) {
        jQuery('#farming-users').parent().hide();
    });
}

/**
 *
 * @param   {[array]}  subUsers   [session subject sub users]
 * @param   {[? integer]}  farmingId 
 *
 * @return  {[void]}
 */
function initUsersCombo(subUsers, farmingId = null, disableCombo = false) 
{
    subUsers.unshift({'id':'', 'username': 'Всички', selected: (farmingId ? false: true)});
    jQuery('#farming-users').combobox({
        data: subUsers,
        editable: false,
        valueField: 'id',
        textField: 'username',
        multiple: true,
        disabled: disableCombo,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        onLoadSuccess: function(data) {
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function exportFarmingToExcel(data){
    var winDownload = jQuery('#win-download').window({
        onClose: onDownloadWindowClose
    });
    var downloadFile = jQuery('#btn-download-file');
    var cancelDownloadFile = jQuery('#btn-download-file-close');

    var getChecked =jQuery('#farming-tables').datagrid('getChecked');
    var id = getChecked[0].id;

   TF.Rpc.Farming.FarmingGrid.export2xls(id)
   .done(function (data) {
        winDownload.window('open');
        var path = data;
        _pathFile = path;
        downloadFile.attr("href", path);
   }).fail(function (errorObj) {
        jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
    });
}

function getFarmingUsers() {
    try {
        let users = jQuery('#farming-users').combobox('getValues');

        if (users && users.length == 1 && users[0] == '') {
            //if selected option is All, get all user ids to grant permission
            users = jQuery('#farming-users').combobox('getData')
             .filter((record) => {
                return record.id != '';
             }).map(record => {
                 return record.id;
             });
        }
        
        return users;
    } catch (error) {
        return null;
    }
}

function getAddEditFarmingFields() {
    var banksFields = jQuery('#banks-info>div');
    var banksArr = jQuery.map(banksFields, function(field) {
        return {
            'name': jQuery(field).find('.bank-name-field').textbox('getValue'),
            'iban': jQuery(field).find('.bank-iban-field').textbox('getValue'),
            'bic': jQuery(field).find('.bank-bic-field').textbox('getValue'),
            'bank_branch': jQuery(field).find('.bank-branch-field').textbox('getValue'),
            'bank_branch_address': jQuery(field).find('.bank-branch-address-field').textbox('getValue'),
        }
    });
    let farmingUsers = this.getFarmingUsers();    
    let postPaymentFields =  (new PostPaymentFields('sender')).getPostPaymentValues();

    var returnObj = {
        name: jQuery('#farming-name > input').textbox('getValue'),
        address: jQuery('#farming-address > input').val(),
        company: jQuery('#farming-company > input').val(),
        bulstat: jQuery('#farming-bulstat > input').val(),
        company_address: jQuery('#farming-company-address > input').val(),
        company_ekatte: jQuery('#farming-company-ekatte > input').combobox('getValue'),
        farming_mol_phone: jQuery('#farming-mol-phone > input').val(),
        mol: jQuery('#farming-mol > input').val(),
        mol_egn: new EgnValidateBox('#farming-mol-egn > input').getValue(),
        representative_id: representativeId,
        iban_arr: banksArr,
        farmingUsers: farmingUsers,
        post_payment_fields: postPaymentFields
    };
    return returnObj;
}


function setEditFarmingFields(data) {
    jQuery('#farming-company-ekatte > input').combobox({
        url: 'index.php?common-rpc=ekate-combobox',
        rpcMethod: 'readAll',
        rpcParams: [
            {
                'farm_uuid': data.uuid,
            }
        ],
        valueField: 'ekatte',
        textField: 'text',
        filter: function (q, row) {
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
            {
                return true;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter

    });

    jQuery('#farming-name > input').textbox('setValue', data.name);
    jQuery('#farming-address > input').val(data.address);
    jQuery('#farming-company > input').val(data.company);
    jQuery('#farming-bulstat > input').val(data.bulstat);
    jQuery('#farming-company-address > input').val(data.company_address);
    jQuery('#farming-mol-phone > input').val(data.farming_mol_phone),
    jQuery('#farming-mol > input').val(data.mol);
    new EgnValidateBox('#farming-mol-egn > input').setValue(data.mol_egn);
    representativeId = data.rep_id || null;

    jQuery('#dinamic-iban-fields').html('');
    var banks = JSON.parse(data.iban_arr);
    resetBanksFields(banks[0]);

    for(var i=1; i<banks.length; i++) {
        appendIbanField(banks[i]);
    }

    setFarmingUsers(data.id);

    const postPayment = new PostPaymentFields('sender');
	postPayment.fillPostPaymentInputs(data.post_payment_fields);
}

function resetBanksFields(bank) {
    var mainBank = jQuery('#info-row-1');
    jQuery(mainBank).find('.bank-name-field').textbox('setValue', bank && bank['name'] ? bank['name'] : '');
    jQuery(mainBank).find('.bank-iban-field').textbox('setValue', bank && bank['iban'] ? bank['iban'] : '');
    jQuery(mainBank).find('.bank-bic-field').textbox('setValue', bank && bank['bic'] ? bank['bic'] : '');

    jQuery(mainBank).find('.bank-branch-field').textbox('setValue', bank && bank['bank_branch'] ? bank['bank_branch'] : '');
    jQuery(mainBank).find('.bank-branch-address-field').textbox('setValue', bank && bank['bank_branch_address'] ? bank['bank_branch_address'] : '');

    jQuery('.additional-bank-info').remove();
}

function clearFarmingFields() {
    jQuery('#farming-name > input').textbox('clear');
    jQuery('#farming-address > input').val('');
    jQuery('#farming-company > input').val('');
    jQuery('#farming-bulstat > input').val('');
    jQuery('#farming-company-address > input').val('');
    jQuery('#farming-mol-phone > input').val(''),
    jQuery('#farming-mol > input').val('');
    new EgnValidateBox('#farming-mol-egn > input').clear();
    if (jQuery('#farming-users').is(':visible') === true || jQuery('#farming-users').combobox('options').disabled === true) {
        jQuery('#farming-users').combobox('clear');
        jQuery('#farming-users').combobox('select',''); //select all
    }

    resetBanksFields();

    var ibanFields = jQuery('.iban-field');
    jQuery(ibanFields[0]).textbox('clear');
    representativeId = null;
}

function validateFarmingFields() {
    if(jQuery('#farming-name > input').textbox('isValid')) {
        return true;
    }

    return false;
}

function onDownloadWindowClose()
{
    return;
}

function onAddIbanClick(e) {
    appendIbanField();
}

function onRemoveIbanClick(e) {
    jQuery(this).parent().remove();
}

var bankInfoRowNumber = 1;
function appendIbanField(data) {
    bankInfoRowNumber++;
    const name = data && data.name ? data.name : "";
    const iban = data && data.iban ? data.iban : "";
    const bic = data && data.bic ? data.bic : "";
    const branch = data && data.bank_branch ? data.bank_branch : "";
    const address = data && data.bank_branch_address ? data.bank_branch_address : "";

    var html = '<div id="info-row-'+bankInfoRowNumber+'" class="additional-bank-info" style="margin-top: 15px; overflow: auto;">\n' +
                    '<div class="col-2-12 padding-5"><label>Банка:</label></div>\n' +
                    '<div class="col-10-12 padding-5"><input class="bank-name-field easyui-textbox" type="text" style="width:90%;" value="'+name+'"/></div>\n' +
                    '<div class="col-2-12 padding-5"><label>IBAN:</label></div>\n' +
                    '<div class="col-10-12 padding-5"><input class="bank-iban-field easyui-textbox" style="width:90%;" value="'+iban+'" /></div>\n' +
                    '<div class="col-2-12 padding-5"><label>BIC:</label></div>\n' +
                    '<div class="col-10-12 padding-5"><input class="bank-bic-field easyui-textbox" style="width:90%;" value="'+bic+'"/></div>\n' +
                    '<div class="col-2-12 padding-5"><label>Клон:</label></div>\n' +
                    '<div class="col-10-12 padding-5"><input class="bank-branch-field easyui-textbox" style="width:90%;" value="'+branch+'"/></div>\n' +
                    '<div class="col-2-12 padding-5"><label>Адрес:</label></div>\n' +
                    '<div class="col-10-12 padding-5"><input class="bank-branch-address-field easyui-textbox" style="width:90%;" value="'+address+'"/></div>\n' +
                    '<a class="remove-iban-btn">Премахни банката</a>' +
                '</div>';
    jQuery('#banks-info').append(html);

    jQuery('#info-row-'+bankInfoRowNumber).find('.bank-name-field').textbox();
    jQuery('#info-row-'+bankInfoRowNumber).find('.bank-iban-field').textbox();
    jQuery('#info-row-'+bankInfoRowNumber).find('.bank-bic-field').textbox();
    jQuery('#info-row-'+bankInfoRowNumber).find('.bank-branch-field').textbox();
    jQuery('#info-row-'+bankInfoRowNumber).find('.bank-branch-address-field').textbox();

    jQuery('.remove-iban-btn').linkbutton({iconCls: 'icon-remove'});
}

function saveSubleaseContragent() {
    var repData = jQuery('#contract-owners-reps-tables').datagrid('getChecked');

    if (repData[0] == undefined) {
        jQuery.messager.alert('Грешка', 'Не е избран представител');
        return false;
    } else if (repData[0].id == false) {
        jQuery.messager.alert('Грешка', 'Не можете да добавяте представител, преди да го запазите.');
        return false;
    }

    //self rep hold if owner represents himself or is represented by another
    var obj = repData[0];
    representativeId = obj.id;

    jQuery('#farming-mol > input').val(obj.rep_name + ' ' + obj.rep_surname + ' ' + obj.rep_lastname);
    new EgnValidateBox('#farming-mol-egn > input').setValue(obj.rep_egn);
    jQuery('#farming-mol > input').attr('disabled', true);
    jQuery('#farming-mol-egn > input').attr('disabled', true);
    jQuery('#win-set-owner-rep').window('close');
}
