var currentLayerName;
function initIsakGrid(layer_name) {
    currentLayerName = layer_name;
    jQuery('#isak-table').datagrid({
        title: 'ИСАК',
        iconCls: 'icon-edit-geometry',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 30,
        fit: true,
        singleSelect: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?isak-rpc=isak-maingrid',
        rpcParams: [{
            layer_name:currentLayerName
        }],
        sortName: 'prc_uin',
        sortOrder: 'asc',
        queryParams: {},
        columns: [
            [
                {
                    field: 'prc_uin',
                    title: '<b>ИСАК номер</b>',
                    sortable: true,
                    width: 60
                }, {
                    field: 'ekatte',
                    title: '<b>ЕКАТТЕ</b>',
                    sortable: true,
                    width: 50
                }, {
                    field: 'culture',
                    title: '<b>Култура</b>',
                    sortable: true,
                    width: 100
                }, {
                    field: 'schemata',
                    title: '<b>Схема / Мярка</b>',
                    sortable: true,
                    width: 100
                }, {
                    field: 'campaign',
                    title: '<b>Кампания</b>',
                    sortable: true,
                    width: 100
                }, {
                    field: 'area',
                    title: '<b>Площ (ха)</b>',
                    sortable: true,
                    width: 50
                }
            ]
        ],
        pagination: true,
        rownumbers: true,
        toolbar: [
            {
                id: 'btnfilterzp',
                text: 'Филтър',
                iconCls: 'icon-filter',
                handler: function() {
                    jQuery('#win-filter-isak').window('open');
                }
            }, {
                id: 'btnclearzpfilter',
                text: 'Покажи всички',
                iconCls: 'icon-clear-filter',
                handler: function() {
                    clearIsakFilter();
                }
            }, '-', {
                id: 'btnshowkvsplots',
                text: 'Пресичане с допустим слой',
                iconCls: 'icon-intersection',
                handler: function() {
                    var layerData = jQuery('#layers-tree').tree('getSelected');

                    initIsakDiffAllowableGrid(layerData.attributes.layer_name);

                    jQuery('#win-isak-diff-allowable').window('move', {left: 10, top: (innerHeight - 360)});
                    jQuery('#win-isak-diff-allowable').window('open');
                }
            }
        ],
        onBeforeLoad: function() {
            jQuery('#isak-tables').datagrid('clearChecked');
        },
        onLoadSuccess: function() {
            initReportGrid(layer_name);
        },
        onSelect: function(rowIndex, rowData) {
			panToFeatureSelection(rowData.st_astext);
		},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function filterIsak() {
    jQuery('#isak-table').datagrid({
        rpcParams: [{
            layer_name: currentLayerName,
            prc_uin: jQuery('#isak-filter-prc-uin').val(),
            ekate: jQuery('#isak-filter-ekate').combobox('getValue'),
            schemata: jQuery('#isak-filter-schemata').val(),
            culture: jQuery('#isak-filter-culture').combobox('getValue')
        }]
    });

    jQuery('#win-filter-isak').window('close');
}

function clearIsakFilter() {
    jQuery('#isak-table').datagrid({
        rpcParams: [{
            layer_name: currentLayerName
        }]
    });

    jQuery('#isak-filter-prc-uin').val('');
    jQuery('#isak-filter-ekate').combobox('clear');
    jQuery('#isak-filter-schemata').val('');
    jQuery('#isak-filter-culture').combobox('clear');
}
