function initReportGrid(layer_name) {
    jQuery('#report-tables').datagrid({
        title: 'Обща площ по схема/мярка',
        iconCls: 'icon-measure-polygon',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        fit: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?isak-rpc=isak-report-grid',
        border: false,
        rpcParams: [{
            layer_name: layer_name,
            prc_uin: jQuery('#isak-filter-prc-uin').val(),
            ekate: jQuery('#isak-filter-ekate').combobox('getValue'),
            schemata: jQuery('#isak-filter-schemata').val(),
            culture: jQuery('#isak-filter-culture').combobox('getValue')
        }],
        columns: [
            [
                {
                    field: 'schemata',
                    title: '<b>Схема / Мярка</b>',
                    sortable: false,
                    width: 95
                }, {
                    field: 'area',
                    title: '<b>Площ(ха)</b>',
                    sortable: false,
                    width: 40
                }
            ]
        ],
        onBeforeLoad: function() {
            jQuery('#report-tables').datagrid('clearChecked');
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initIsakDiffAllowableGrid(layer_name)
{
    jQuery('#isak-diff-allowable-tables').datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        singleSelect: true,
        fit: true,
        fitColumns: true,
        showFooter: true,
        rownumbers: true,
        url: 'index.php?isak-rpc=isak-diff-allowable-grid',
        rpcParams: [{
            layer_name: layer_name
        }],
        border: false,
        pagination: true,
        columns: [
            [
                {
                    field: 'prc_uin',
                    title: '<b>ИСАК номер</b>',
                    sortable: false,
                    width: 40
                }, {
                    field: 'area',
                    title: '<b>Площ<br/>(ха)</b>',
                    sortable: false,
                    width: 20,
                    align: 'center'
                }, {
                    field: 'outside_area',
                    title: '<b>Площ извън слой<br/>допустимост<br/>(ха)</b>',
                    sortable: false,
                    width: 40,
                    align: 'center',
                	styler: function(value,row,index){
    					return 'color: red;';
        			}
                }, {
                    field: 'inside_area',
                    title: '<b>Площ в слой<br/>допустимост<br/>(ха)</b>',
                    sortable: false,
                    width: 30,
                    align: 'center'
                }
            ]
        ],
        onBeforeLoad: function() {
            jQuery('#isak-diff-allowable-tables').datagrid('clearChecked');
        },
        onSelect: function(rowIndex, rowData) {
			panToFeatureSelection(rowData.st_astext);
		},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    //custom pager
	var pager = jQuery('#isak-diff-allowable-tables').datagrid('getPager');
	pager.pagination({
		beforePageText: 'Стр.',
		displayMsg: 'От {from} до {to} от {total}'
	});
}
