var isSuperAdmin = false;
var map;
var renderer = OpenLayers.Util.getParameters(window.location.href).renderer;
if (renderer) {
    OpenLayers.Layer.Vector.prototype.renderers = [renderer];
}

jQuery(function() {
    initMap();
    initIsakFilterPanel();
    setUserLastLogin();

    jQuery('#layer-allowable').change(function(){
        if(jQuery('#layer-allowable').is(':checked'))
        {
            loadMapLayerAllowable();
        }
        else
        {
            removeLayerByName('layer_allowable');
        }
    });

    jQuery('#isak-diff-allowable').change(function(){
        if(jQuery('#isak-diff-allowable').is(':checked'))
        {
            var selected = jQuery('#layers-tree').tree('getSelected');

            var obj = new Object();
            obj.layer_name = selected.attributes.layer_name

            TF.Rpc.Isak.IsakDiffAllowableGrid.init(obj)
            .done(function (data) {
                loadMapLayer('isak_diff_allowable');
            })
            .fail(function (data) {

            });
        }
        else
        {
            removeLayerByName('isak_diff_allowable');
        }
    });

    //init components
    initLayersTree();
});

function initMap() {

    if (!map) {
        var options = {
            controls: [new OpenLayers.Control.Navigation()],
            projection: new OpenLayers.Projection("EPSG:900913")
        };
        map = new OpenLayers.Map('map', options);
        initMapPad();
    }

    map.render("map");
}

function loadMapLayerIsak(layer_name, extent) {
    for (var i = 1; i < map.layers.length; i++)
    {
        if(map.layers[i].name.indexOf('layer_isak') != -1)
        {
            map.removeLayer(map.layers[i]);
        }
    }

	if (layer_name && extent)
	{
		var layerExtent = new OpenLayers.Bounds.fromString(extent).transform(
				new OpenLayers.Projection("EPSG:32635"),
				map.getProjectionObject()
				);
		var layerData = new OpenLayers.Layer.WMS(
				layer_name,
				wmsServer + "?map=" + mapPath + groupID + '.map',
				{
					layers: layer_name,
					format: 'image/png',
					transparent: "true"
				});
		map.addLayer(layerData);
		layerData.redraw(true);

        map.zoomToExtent(layerExtent);
	}
}

function loadMapLayerAllowable() {
    var layerData = new OpenLayers.Layer.WMS(
            'layer_allowable',
            login3WmsServer + "?map=" + login3MapPath + 'layer_allowable_draft.map',
            {
                layers: 'layer_allowable_draft',
                format: 'image/png',
                transparent: "true"
            });
    map.addLayer(layerData);
    layerData.redraw(true);
}

function loadMapLayer(layer_name) {
    if (layer_name)
	{
        var layerData = new OpenLayers.Layer.WMS(
                layer_name,
                wmsServer + "?map=" + mapPath + groupID + '.map',
                {
                    layers: layer_name,
                    format: 'image/png',
                    transparent: "true"
                });
        map.addLayer(layerData);
        layerData.redraw(true);
    }
}

function removeLayerByName(layer_name) {
	for (var i = 0; i < map.layers.length; i++)
	{
		if (map.layers[i].name == layer_name)
			map.removeLayer(map.layers[i]);
	}
}

function panToFeatureSelection(geom)
{
	var in_options = {
		'internalProjection': map.baseLayer.projection,
		'externalProjection': new OpenLayers.Projection("EPSG:32635")
	};
	var features = new OpenLayers.Format.WKT(in_options).read(geom);

	var bounds;

	if (features) {
		if (features.constructor != Array) {
			features = [features];
		}
		for (var i = 0; i < features.length; ++i) {
			if (!bounds) {
				bounds = features[i].geometry.getBounds();
			} else {
				bounds.extend(features[i].geometry.getBounds());
			}
		}

		var b = bounds.getCenterLonLat();
		var lonlat = new OpenLayers.LonLat(b.lon, b.lat);

		map.zoomToExtent(bounds);
        map.panTo(lonlat);
	}
}

function initIsakFilterPanel() {
    jQuery('#isak-filter-culture').combobox({
        url: 'index.php?common-rpc=culture-combobox',
        valueField: 'id',
        textField: 'name',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#isak-filter-ekate').combobox({
        url: 'index.php?common-rpc=ekate-combobox',
        valueField: 'ekate',
        textField: 'text',
		filter: function(q, row){
			var opts = jQuery(this).combobox('options');
			var text = row[opts.textField].toLowerCase();
			var value = row[opts.valueField];
			var find = q.toLowerCase();
			if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
			{
				return true;
			}
		},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

var bingApiKey = "AiWEso3-IjWyX1aZMuep9Sjl62D6FUMqv8qQGpV-kgbW0qWk61to4nrqHh-2D5HL";

function initMapPad(specific_map_type)
{
    var chosenMapType;

    //on init map type will not be specified
    if (specific_map_type == undefined)
    {
        chosenMapType = store.get('map_pad') || 1;
    }
    //when map type is changed specific_map_type will have the value of map type
    else {
        chosenMapType = parseInt(specific_map_type);
    }

    var layerMapPad;

    switch (chosenMapType)
    {
        case 2:
            layerMapPad = new OpenLayers.Layer.Bing({
                key: bingApiKey,
                type: "Aerial",
                name: "MapPad"
            });
            break;
        case 3:
            layerMapPad = new OpenLayers.Layer.Bing({
                key: bingApiKey,
                type: "Road",
                name: "MapPad"
            });
            break;
        case 4:
            layerMapPad = new OpenLayers.Layer.Google(
                    "MapPad",
                    {type: google.maps.MapTypeId.TERRAIN}
            );
            break;
        case 5:
            layerMapPad = new OpenLayers.Layer.Google(
                    "MapPad",
                    {numZoomLevels: 20}
            );
            break;
        case 6:
            layerMapPad = new OpenLayers.Layer.Google(
                    "MapPad",
                    {type: google.maps.MapTypeId.HYBRID, numZoomLevels: 20}
            );
            break;
        case 7:
            layerMapPad = new OpenLayers.Layer.Google(
                    "MapPad",
                    {type: google.maps.MapTypeId.SATELLITE, numZoomLevels: 22}
            );
            break;
        case 8:
            layerMapPad = new OpenLayers.Layer.OSM();
            break;
        case 9:
            layerMapPad = new OpenLayers.Layer.WMS(
                'MapPad',
                imagesWMSServer + "?map=" + '/var/www/satellite_processor/maps/geo_scan.map',
                //wmsServer + "?map=" + mapPath + groupID + '.map',
                {
                    layers: 'geo_scan'
                },
                {
                    numZoomLevels: 18
                });
            break;
        default: // default is bing aerial with labels
            layerMapPad = new OpenLayers.Layer.Bing({
                key: bingApiKey,
                type: "AerialWithLabels",
                name: "MapPad"
            });
            break;
    }

    //specific map type will be given only on reload
    //on first load(init) specific map type should be undefined
    if (specific_map_type == undefined)
    {
        map.addLayer(layerMapPad);
    }
    else {
        map.addLayer(layerMapPad);
        map.setLayerIndex(map.layers[map.layers.length - 1], 0);
        map.removeLayer(map.layers[1]);
        map.layers[0].redraw(true);
    }
}
