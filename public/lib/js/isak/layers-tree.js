function initLayersTree() {
    jQuery('#layers-tree').tree({
        url: 'index.php?isak-rpc=isak-layers-tree',
        rpcParams: [{
            years:5
        }],
        animate: true,
        lines: true,
        onBeforeSelect: function(node) {
            var isLeaf = jQuery('#layers-tree').tree('isLeaf', node.target);

            if (!isLeaf)
                return false;
        },
        onSelect: function(node) {
            //clear filter fields
            jQuery('#isak-filter-prc-uin').val('');
            jQuery('#isak-filter-ekate').combobox('clear');
            jQuery('#isak-filter-schemata').val('');
            jQuery('#isak-filter-culture').combobox('clear');

            initIsakGrid(node.attributes.layer_name);

            loadMapLayerIsak(node.attributes.layer_name, node.attributes.extent);

            jQuery('#isak-diff-allowable').prop('checked', false);
            removeLayerByName('isak_diff_allowable');
            jQuery('#win-isak-diff-allowable').window('close');
        },
        onLoadSuccess: function() {
            var roots = jQuery('#layers-tree').tree('getRoots');
            var years = jQuery('#layers-tree').tree('getChildren', roots[0].target);

            jQuery('#layers-tree').tree('select', years[0].target);
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}
