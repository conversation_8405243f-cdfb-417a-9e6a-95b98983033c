var repEditIndex = undefined;

function initRepsGrid() {
	jQuery('#contragent-reps-tables').datagrid({
		title: 'Представители',
		iconCls: 'icon-users',
		rownumbers: true,
		singleSelect: true,
		onSelectCheck: true,
		onCheckSelect: true,
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		fit: true,
		fitColumns: true,
		showFooter: true,
		pagination: true,
		height: 250,
		idField: 'id',
		sortName: 'id',
		sortOrder: 'desc',
		url: 'index.php?contracts-rpc=owners-reps-grid',
		rpcParams:[{}],
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
		columns: [[
				{
					field: 'rep_name',
					title: '<b>Име</b>',
					sortable: true,
					width: 200,
					editor: {
						type: 'validatebox',
						options: {
							required: true,
							missingMessage: 'Въведете име'
						}
					}
				}, {
					field: 'rep_surname',
					title: '<b>Презиме</b>',
					sortable: true,
					width: 200,
					editor: {
						type: 'validatebox',
						options: {
							required: true,
							missingMessage: 'Въведете презиме'
						}
					}
				}, {
					field: 'rep_lastname',
					title: '<b>Фамилия</b>',
					sortable: true,
					width: 200,
					editor: {
						type: 'validatebox',
						options: {
							required: true,
							missingMessage: 'Въведете фамилия'
						}
					}
				}, {
					field: 'rep_egn',
					title: '<b>ЕГН</b>',
					sortable: true,
					width: 200,
					editor: {
						type: 'egnValidateBox',
						options: {
							required: true,
							missingMessage: 'Въведете ЕГН'
						}
					}
				}, {
					field: 'rep_lk',
					title: '<b>Лична карта</b>',
					sortable: true,
					width: 200,
					editor: {
						type: 'validatebox',
						options: {
							required: false
						}
					}
				}, {
					field: 'rep_lk_izdavane',
					title: '<b>ЛК издадена на/от</b>',
					sortable: true,
					width: 200,
					editor: {
						type: 'validatebox',
						options: {
							required: false
						}
					}
				}, {
					field: 'rep_phone',
					title: '<b>Телефон</b>',
					sortable: true,
					width: 280,
					editor: {
						type: 'validatebox',
						options: {
							required: false
						}
					}
				}, {
					field: 'rep_address',
					title: '<b>Адрес</b>',
					sortable: true,
					width: 300,
					editor: {
						type: 'validatebox',
						options: {
							required: false
						}
					}
				}, {
					field: 'iban',
					title: '<b>IBAN</b>',
					sortable: false,
					width: 300,
					editor: {
						type: 'validatebox',
						options: {
							required: false
						}
					}
				}
			]],
		toolbar: [{
				id: 'btnadd',
				text: 'Добавяне',
				iconCls: 'icon-add',
				handler: function() {
					if (!hasPlotRightsRW) {
						messagerPlotsWriteRights();
						return false;
					};
					appendRepRow();
				}
			}, {
				id: 'btnedit',
				text: 'Редактиране',
				iconCls: 'icon-edit',
				handler: function() {
					if (!hasPlotRightsRW) {
						messagerPlotsWriteRights();
						return false;
					};
					var getChecked = jQuery('#contragent-reps-tables').datagrid('getChecked');

					if (getChecked[0]) {
						var index = jQuery('#contragent-reps-tables').datagrid('getRowIndex', getChecked[0]);

						if (repEditIndex != index) {
							if (repEditingClosed()) {
								jQuery('#contragent-reps-tables').datagrid('beginEdit', index);
								repEditIndex = index;
							} else {
								jQuery('#contragent-reps-tables').datagrid('selectRow', repEditIndex);
							}
						}
					} else
					{
						jQuery.messager.alert('Грешка', 'Не е избран представител.');
					}
				}
			}, {
				id: 'btndel',
				text: 'Премахване',
				iconCls: 'icon-remove',
				handler: function() {
					if (!hasPlotRightsRW) {
						messagerPlotsWriteRights();
						return false;
					};
					var getChecked = jQuery('#contragent-reps-tables').datagrid('getChecked');

					if (getChecked[0]) {
						jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да изтриете този представител?', function(r) {
							if (r) {
								representativeId = jQuery('#contragent-reps-tables').datagrid('getSelected').id;
								TF.Rpc.Contracts.ConctractOwnerData.deleteRep(representativeId);
								jQuery('#contragent-reps-tables').datagrid('reload');
							}
						});
						repEditIndex = undefined;
					} else
					{
						jQuery.messager.alert('Грешка', 'Моля изберете кой представител искате да премахнете.');
					}
				}
			}, {
				id: 'btnsave',
				text: 'Запази',
				iconCls: 'icon-save',
				handler: function() {
					repEditingClosed();
				}
			}, {
				id: 'btncancel',
				text: 'Отмени',
				iconCls: 'icon-undo',
				handler: function() {
					rejectRepChanges();
				}
			}, {
				id: 'btnaddrepfilter',
				text: 'Филтриране',
				iconCls: 'icon-filter',
				handler: function() {
					jQuery('#win-owner-reps-filter').window('open');
				}
			}, {
				id: 'btnremoverepfilter',
				text: 'Покажи всички',
				iconCls: 'icon-clear-filter',
				handler: function() {
					cleanOwnersRepsFilter();
				}
			}],
		onCheck: function(index, rowData) {
			if (repEditIndex != index && !repEditingClosed()) {
				jQuery('#contragent-reps-tables').datagrid('selectRow', repEditIndex);
			}
		},
		onBeforeLoad: function() {
			rejectRepChanges();
		}
	});
}

function repEditingClosed() {
	if (repEditIndex == undefined) {
		return true;
	}
	var contractOwnersRepsTables = jQuery('#contragent-reps-tables');
	var rowEditors = contractOwnersRepsTables.datagrid('getEditors', repEditIndex);
	var editorsByField = {};

	let isFormDataValid = true;
	for (var i = 0; i < rowEditors.length; i++) {
		var rowEditor = rowEditors[i];
		var rowEditorType = rowEditor.type;
		editorsByField[rowEditor.field] = rowEditor;
		//skipping the EGN validation
		const isValid = rowEditor.field === 'rep_egn' 
			? rowEditor.target.validatebox('isValid') 
			: rowEditor.target[rowEditorType]('isValid');

		if (!isValid) {
			isFormDataValid = false;
		}
	}

	if (!isFormDataValid) {
		jQuery.messager.alert('Грешка', 'Моля попълнете задължителните полета.');
		return false;
	}

	contractOwnersRepsTables.datagrid('endEdit', repEditIndex);
	contractOwnersRepsTables.datagrid('acceptChanges');

	var rows = contractOwnersRepsTables.datagrid('getRows');
    var obj = rows[repEditIndex];

    TF.Rpc.Contracts.ConctractOwnerData.saveRep(obj);

	repEditIndex = undefined;

	contractOwnersRepsTables.datagrid('reload');

	return true;
}

function appendRepRow() {
	if (repEditingClosed()) {
		repEditIndex = 0;
		jQuery('#contragent-reps-tables').datagrid('insertRow',
				{
					index: repEditIndex,
					row: {
						id: false
					}
				});
		jQuery('#contragent-reps-tables').datagrid('beginEdit', repEditIndex).datagrid('selectRow', repEditIndex);
	}
}

function rejectRepChanges() {
	jQuery('#contragent-reps-tables').datagrid('rejectChanges');
	jQuery('#contragent-reps-tables').datagrid('uncheckAll');
	jQuery('#contragent-reps-tables').datagrid('unselectAll');

	repEditIndex = undefined;
}

//filter for owners reps grid
function filterOwnersReps() {
	if (jQuery('#search-rep-names > input').val() != ''
			|| jQuery('#search-rep-egn > input').val() != '') {
		jQuery('#contragent-reps-tables').datagrid({
			rpcParams: [{
							rep_names: jQuery('#search-rep-names > input').val(),
							rep_egn: jQuery('#search-rep-egn > input').val()
						}]
		});
		jQuery('#win-owner-reps-filter').window('close');
	} else {
		jQuery.messager.alert('Грешка', 'Моля задайте данни за търсене.');
	}
}

function cleanOwnersRepsFilter() {
	jQuery('#search-rep-names > input').val('');
	jQuery('#search-rep-egn > input').val('');

	jQuery('#contragent-reps-tables').datagrid({
		rpcParams: [{}]
	});
}
//end of filter for owners reps grid
