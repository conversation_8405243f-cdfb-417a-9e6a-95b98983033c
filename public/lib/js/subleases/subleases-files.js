function initFilesGrid(sublease_id) {

    var subleasesFilesGrid = jQuery('#sublease-files-tables'),
        isDatagridBound = subleasesFilesGrid.data().hasOwnProperty('datagrid');

    if (isDatagridBound) {
        if (sublease_id !== 0) {
            subleasesFilesGrid.datagrid({
                url: 'index.php?contracts-rpc=contracts-files-maingrid',
                rpcParams: [{
                    contract_id: sublease_id
                }],
            });
        } else {
            subleasesFilesGrid.datagrid({data: {rows: [], total: 0}});
        }
        return;
    }

    subleasesFilesGrid.datagrid({
        iconCls: 'icon-files',
        title: 'Архив файлове',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        fit: true,
        fitColumns: true,
        showFooter: false,
        idField: 'id',
        singleSelect: true,
        frozenColumns: [[
            {
                field: 'ck',
                checkbox: true
            }
        ]],
        columns: [[
            {
                field: 'filename',
                title: '<b>Файл</b>',
                sortable: false,
                width: 100
            }, {
                field: 'date',
                title: '<b>Дата</b>',
                sortable: false,
                width: 100
            }
        ]],
        rownumbers: true,
        toolbar: [{
            id: 'btnaddcontractfile',
            text: 'Добавяне',
            iconCls: 'icon-add',
            handler: function () {
                sublease_id = jQuery('#subleases-tree').tree('getSelected').id;
                if (sublease_id) {

                    //No Rights
                    if (!hasPlotRightsRW) {
                        EasyUIRPCLoaders.messagerNoRightsRW();
                        return false;
                    }

                    initFileUploads(sublease_id);
                    jQuery('#win-add-file').window('open');
                } else {
                    jQuery.messager.alert('Грешка', 'Не е избран договор.');
                }
            }
        }, {
            id: 'btndeletecontractfile',
            text: 'Премахване',
            iconCls: 'icon-delete',
            handler: function () {
                if (!hasPlotRightsRW) {
                    messagerPlotsWriteRights();
                    return false;
                }
                var sublease_id = jQuery('#subleases-tree').tree('getSelected').id;
                var getChecked = subleasesFilesGrid.datagrid('getChecked');

                if (getChecked[0]) {
                    jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да премахнете този запис?', function (r) {
                        if (r) {
                            var file_params = {
                                'sublease_id': sublease_id,
                                'file_id': getChecked[0].file_id
                            };
                            TF.Rpc.Subleases.SubleasesFiles.deleteSubleasesFile(file_params)
                                .done(function (data) {
                                    subleasesFilesGrid.datagrid('loadRpc');
                                });
                        }
                    });
                } else {
                    jQuery.messager.alert('Грешка', 'Не е избран файл!');
                }
            }
        }, {
            id: 'btndownloadcontractfile',
            text: 'Изтегляне',
            iconCls: 'icon-export',
            handler: function () {
                var getChecked = subleasesFilesGrid.datagrid('getChecked');

                if (getChecked[0]) {
                    var file_id = getChecked[0].id;
                    TF.Rpc.Subleases.SubleasesFiles.downloadAttached(file_id)
                        .done(function (data) {
                            jQuery('#win-download').window('open');
                            var path = data,
                                _pathFile = path;
                                jQuery('#btn-download-file').attr("href", path);
                        });
                } else {
                    jQuery.messager.alert('Грешка', 'Моля изберете файл, който искате да изтеглите.');
                }
            }
        }],
        onBeforeLoad: function () {
            subleasesFilesGrid.datagrid('clearChecked');
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initFileUploads(sublease_id) {
    const url  = "index.php?json=contract-upload"; 
    jQuery("#uploader").pluploadQueue({
        // General settings
        runtimes: 'gears,html5,flash,silverlight,browserplus',
        url: url,
        max_file_size: '100mb',
        multipart_params : {
            "contract_id" : sublease_id,
        },
        unique_names: true,
        // Flash settings
        flash_swf_url: 'lib/js_external/fileupload/plupload.flash.swf'
    });

    var uploader = jQuery('#uploader').pluploadQueue();
    uploader.bind('UploadComplete', function () {
        jQuery('#win-add-file').window('close');
        jQuery('#sublease-files-tables').datagrid('reload');
    });
}
