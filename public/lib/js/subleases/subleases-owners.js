function initSubleaseOwnersGrid(pc_rel_id, c_type) {
	if(c_type == 1) {
		var title = 'Предишни собственици';
	} else {
		var title = 'Собственици';
	}

	var subleasesOwnersGrid = jQuery('#sublease-owners-tables'),
        isDatagridBound = subleasesOwnersGrid.data().hasOwnProperty('datagrid');

    if (isDatagridBound) {
        if (pc_rel_id !== 0) {
            subleasesOwnersGrid.treegrid({
            	url: 'index.php?subleases-rpc=sublease-plot-owners-grid',
            	rpcParams:[{
            		type: 'view',
            		pc_rel_id: pc_rel_id
            	}],
            });
        } else {
            subleasesOwnersGrid.treegrid('loadData',
                {
                    rows: [],
                    total: 0,
                }
            );
        }
        return;
    }

	subleasesOwnersGrid.treegrid({
		iconCls: 'icon-owners',
		title: title,
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		pageSize: 10,
		fit: true,
		singleSelect: true,
		fitColumns: true,
		showFooter: false,
		border: true,
		loader : EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter : EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
		sortName: 'name',
		sortOrder: 'asc',
		idField: 'id',
		treeField: 'owner_names',
		columns: [[
				{
					field: 'owner_names',
					title: '<b>Собственик</b>',
					sortable: false,
					width: 150,
					styler: function(value, row, index) {
						if (!row.is_heritor) {
							return 'font-weight: bold;';
						}
					}
				}, {
					field: 'rep_names',
					title: '<b>Представител</b>',
					sortable: false,
					width: 150
				}, {
					field: 'rat_ownage',
					title: '<b>Собственост(%)</b>',
					sortable: false,
					width: 75,
                    formatter: function(value, row){
                        var percent;

                        if(row.numerator && row.denominator) {
                            var percent = new Fraction(row.numerator / row.denominator * 100);
                            var fraction = row.numerator + '/' + row.denominator;
                            value = percent.toString() + '% (' +  fraction + ')';

                            return value;
                        }

                        var fraction = new Fraction(row.percent / 100);
                        var percent = fraction.mul(100);
                        value = percent.toString() + '% (' +  fraction.simplify().toFraction() + ')';

                        return value;
                    }
				}
			]],
		pagination: false,
		rownumbers: true,
		toolbar: [{
			id: 'btninfoowner',
			text: 'Информация',
			iconCls: 'icon-info',
			handler: function() {
				var getSelected = jQuery('#sublease-owners-tables').treegrid('getSelected');
				if (getSelected) {
					window.open("index.php?page=Owners.Home&owner_id=" + getSelected.owner_id, '_blank');
				} else {
					jQuery.messager.alert('Грешка', 'Моля изберете собственик, за който да бъде показана информация.');
				}
			}
		}],
		onBeforeLoad: function() {
			jQuery('#sublease-owners-tables').treegrid('clearChecked');
		}
	});
}
