Namespace('TF.Rpc.Subleases');
var editSubleaseID;
var page_number = 1;

function initSubleasesTree(pageNumber, filterObj) {
    editSubleaseID = undefined;
    page_number = (pageNumber != undefined)  ?  pageNumber : page_number;
    var subLeaseTree = jQuery('#subleases-tree');
    if (subLeaseTree.data().hasOwnProperty('tree')) {
        subLeaseTree.tree('options').page = page_number;
        subLeaseTree.tree('options').rpcParams = [filterObj];
        return subLeaseTree.tree('reload');
    }

    subLeaseTree.tree({
        url: 'index.php?subleases-rpc=subleases-tree',
        animate: false,
        lines: true,
        page:page_number,
        rpcParams: [filterObj],
        sort: 'c.id',
        order: 'desc',
        rows: 30,
        onSelect: function(node) {
            initFilesGrid(node.id);
            initSubleasesPlotsGrid(node.id);
            initFarmingContragentsGrid(node.id);

            //fill sublease info
            fillSubleaseInfo(node.attributes);
            if (node.attributes.c_type == 1 || node.attributes.c_type == 4) {
                jQuery('#info-pd-date').hide();
                jQuery('#info-pd-date-label').hide();
            }
            else {
                jQuery('#info-pd-date').show();
                jQuery('#info-pd-date-label').show();
            }
        },
        onLoadSuccess: function() {
            var roots = jQuery('#subleases-tree').tree('getRoots');
            var total = 0;
            var limit = 10;
            if (roots.length) {
                if (editSubleaseID != undefined) {
                    var node = jQuery('#subleases-tree').tree('find', editSubleaseID);
                    jQuery('#subleases-tree').tree('select', node.target);
                }
                else {
                    jQuery('#subleases-tree').tree('select', roots[0].target);
                }
                total = roots[0]['attributes']['pagination']['total'];
                limit = roots[0]['attributes']['pagination']['limit'];
            }
            else
            {
                initFilesGrid(0);
                initSubleasesPlotsGrid(0);
                initFarmingContragentsGrid(0);
                initSubleasesContractsGrid(0,0);
                initSubleaseOwnersGrid(0,0);
                //fill sublease info
                fillSubleaseInfo();
                jQuery.messager.alert('Внимание', 'Не са открити записи', 'warning');
            }

            //init pagination with total contract elements
            initContractsPagination(total, limit);
        },
        onBeforeLoad: function(node, param) {
            //sending filter parameters with POST method
            if (filterObj)  {
                param.sublease_id = filterObj.sublease_id;
                param.c_num = filterObj.c_num;
                param.date_from = filterObj.date_from;
                param.date_to = filterObj.date_to;
                param.due_date_from = filterObj.due_date_from;
                param.due_date_to = filterObj.due_date_to;
                param.farming = filterObj.farming;
                param.farming_year = filterObj.farming_year;
                param.renta_types = filterObj.renta_types;
                param.c_type = filterObj.c_type;
                param.c_status = filterObj.c_status;
                param.ekate = filterObj.ekate;
                param.kad_ident = filterObj.kad_ident;
                param.masiv = filterObj.masiv;
                param.number = filterObj.number;
                param.category = filterObj.category;
                param.area_type = filterObj.area_type;
                param.subleaser_name = filterObj.subleaser_name;
                param.subleaser_egn = filterObj.subleaser_egn;
                param.rep_name = filterObj.rep_name;
                param.rep_egn = filterObj.rep_egn;
                param.company_name = filterObj.company_name;
                param.company_eik = filterObj.company_eik;
            }
            param.page_number = page_number;
        },
        formatter: function(node) {
            if (node.attributes.active_text == 'Анулиран')
                node.text = '<font color="#aaa"><strike>' + node.text + '</strike></font>';
            if (node.attributes.active_text == 'Изтекъл')
                node.text = '<font color="#aaa">' + node.text + '</font>';
            return node.text;
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initContractsPagination(total, limit) {
    jQuery('#subleases-tree-pagination').pagination({
        showPageList: false,
        showRefresh: false,
        displayMsg: '',
        total: total,
        pageSize: limit,
        onSelectPage: function(pageNumber, pageSize) {
            var obj = {};
            obj.c_num = jQuery('#search-sublease > input').val();
            obj.c_status = jQuery('#search-sublease-status').combobox('getValue');
            obj.date_from = jQuery('#search-date-from > input').datebox('getValue');
            obj.date_to = jQuery('#search-date-to > input').datebox('getValue');
            obj.due_date_from = jQuery('#search-due-date-from > input').datebox('getValue');
            obj.due_date_to = jQuery('#search-due-date-to > input').datebox('getValue');
            obj.farming = jQuery('#search-farming > input').combobox('getValues');
            obj.farming_year = jQuery('#search-farming-year').combobox('getValue');

            initSubleasesTree(pageNumber, obj);
        }
    });
}

function fillSubleaseInfo(data) {
    if(!data)
    {
        jQuery('#info-type').html('');
        jQuery('#info-number').html('');
        jQuery('#info-date').html('');
        jQuery('#info-start-date').html('');
        jQuery('#info-due-date').html('');
        jQuery('#info-comment').html('');
        jQuery('#info-renta').html('');
        jQuery('#info-renta-nat-type').html('');
        jQuery('#info-renta-nat').html('');
        jQuery('#info-sv-num').html('');
        jQuery('#info-sv-date').html('');
        jQuery('#info-pd-date').html('');
        jQuery('#info-farming').html('');
        jQuery('#info-active-text').html('');
        jQuery('.js-renta-additional-row').remove();
        jQuery('#is-declaration-subleased').html('');

        return false;
    }
    jQuery('#info-type').html(data.nm_usage_rights);
    jQuery('#info-number').html(data.c_num);
    jQuery('#info-date').html(data.c_date);
    jQuery('#info-start-date').html(data.start_date);
    jQuery('#info-due-date').html(data.due_date);
    jQuery('#info-comment').html(data.comment);
    jQuery('#info-renta').html(data.renta_text);
    jQuery('#info-renta-nat-type').html(data.renta_nat_type);
    jQuery('#info-renta-nat').html(data.renta_nat_text);
    jQuery('#info-sv-num').html(data.sv_num);
    jQuery('#info-sv-date').html(data.sv_date);
    jQuery('#is-declaration-subleased').html(data.is_declaration_subleased_text);

    var paymonth = '';
    if (months[data.paymonth] != undefined) {
        paymonth = months[data.paymonth]['label'];
    }
    jQuery('#info-pd-date').html(data.payday + ' ' + paymonth);

    jQuery('#info-farming').html(data.farming);
    jQuery('#info-active-text').html(data.active_text);

    var rowHTML;
    if (data.additionalRentas) {
        jQuery('.js-renta-additional-row').remove();

        for(var i = 0; i < data.additionalRentas.length; i++) {
            rowHTML = '<tr class="js-renta-additional-row"><td>' + data.additionalRentas[i]['renta_nat_type'] + '</td><td  style="padding-left:200px;">' + data.additionalRentas[i]['renta_nat_text'] + '</td></tr>';
            jQuery('#js-sublease-info-table').append(rowHTML);
        }
    };
}
