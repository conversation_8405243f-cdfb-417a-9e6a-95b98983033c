/*jslint browser: true */
/*global jQuery, EasyUIRPCLoaders.EasyUIGridCustomLoader, TF, hasPlotRightsRW, messagerPlotsWriteRights*/

var contragent_type;
function initContragentsGrid(sublease_id) {

    var subleasesContragentsTable = jQuery('#sublease-contragents-tables'),
        isDatagridBound = subleasesContragentsTable.data().hasOwnProperty('datagrid');

    if (isDatagridBound) {
        if (sublease_id !== 0) {
            subleasesContragentsTable.datagrid({
                url: 'index.php?subleases-rpc=sublease-contragents-grid',
                rpcParams: [{
                    type: 'view',
                    sublease_id: sublease_id
                }],
            });
        } else {
            subleasesContragentsTable.datagrid('loadData', {rows: [], total: 0});
        }
        return;
    }

    subleasesContragentsTable.datagrid({
        iconCls: 'icon-users',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        fit: true,
        fitColumns: true,
        showFooter: false,
        border: false,
        rpcMethod: "readContragents",
        url: 'index.php?subleases-rpc=sublease-contragents-grid',
        rpcParams: [{
            type: 'view',
            sublease_id: sublease_id
        }],
        idField: 'id',
        singleSelect: true,
        loader : EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter : EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
        columns: [[
            {
                field: 'owner_names',
                title: '<b>Собственик</b>',
                sortable: false,
                width: 100
            }, {
                field: 'rep_names',
                title: '<b>Представител</b>',
                sortable: false,
                width: 100
            }
        ]],
        rownumbers: true,
        toolbar: [
            {
                id: 'btnaddsubleasecontragent',
                text: 'Добавяне',
                iconCls: 'icon-add',
                handler: function () {
                    if (!hasPlotRightsRW) {
                        messagerPlotsWriteRights();
                        return false;
                    }

                    sublease_id = jQuery('#subleases-tree').tree('getSelected').id;

                    if (sublease_id) {
                        contragent_type = 'owner';
                        jQuery('#win-add-contragent').window('open');

                        initAddContragentsGrid(sublease_id);
                        //grid is used for owners and contragents
                        //grid ID should be passed
                        initRepsGrid();
                    } else {
                        jQuery.messager.alert('Грешка', 'Не е избран договор.');
                    }
                }
            }, {
                id: 'btndeletesubleasecontragent',
                text: 'Премахване',
                iconCls: 'icon-delete',
                handler: function () {
                    if (!hasPlotRightsRW) {
                        messagerPlotsWriteRights();
                        return false;
                    }

                    var getChecked = jQuery('#sublease-contragents-tables').datagrid('getChecked');

                    if (getChecked[0]) {
                        var contragentId = getChecked[0].id,
                            contragentType = 'owner';

                        jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да премахнете този запис?', function (r) {
                            if (r) {
                                TF.Rpc.Subleases.SubleaseContragentData.deleteSubleaseContragent(contragentId, contragentType)
                                    .done(function () {
                                        jQuery('#win-add-contragent').window('close');
                                        jQuery('#sublease-contragents-tables').datagrid('reload');
                                        jQuery('#farming-contragents-tables').datagrid('reload');

                                    });
                            }
                        });
                    } else {
                        jQuery.messager.alert('Грешка', 'Не е избрана страна по договор!');
                    }
                }
            }, {
                id: 'btninfoowner',
                text: 'Информация',
                iconCls: 'icon-info',
                handler: function () {
                    var getChecked = jQuery('#sublease-contragents-tables').datagrid('getChecked');
                    if (getChecked[0]) {
                        window.open("index.php?page=Owners.Home&owner_id=" + getChecked[0].owner_id, '_blank');
                    } else {
                        jQuery.messager.alert('Грешка', 'Моля изберете собственик, за който да бъде показана информация.');
                    }
                }
            }
        ],
        onBeforeLoad: function () {
            subleasesContragentsTable.datagrid('clearChecked');
        },
        onLoadSuccess: function (data) {
            if (data.rows.length == 0) {
                if (jQuery('#farming-contragents-tables').datagrid('getData').rows.length != 0) {
                    jQuery('#contragents-tabs').tabs('select', 1);
                } else {
                    jQuery('#contragents-tabs').tabs('select', 0);
                }
            } else {
                jQuery('#contragents-tabs').tabs('select', 0);
            }
        }
    });
}

function initAddContragentsGrid(sublease_id) {
    jQuery('#contragents-add-tables').datagrid({
        iconCls: 'icon-users',
        nowrap: true,
        title: 'Собственици',
        autoRowHeight: true,
        striped: true,
        pageSize: 10,
        fit: true,
        singleSelect: true,
        fitColumns: true,
        showFooter: true,
        border: true,
        url: 'index.php?subleases-rpc=sublease-contragents-grid',
        rpcParams: [{
            type:'add',
            sublease_id:sublease_id
        }],
        rpcMethod: "readContragents",
        sortName: 'id',
        sortOrder: 'desc',
        idField: 'id',
        loader : EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter : EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
        columns: [[
                {
                    field: 'owner_names',
                    title: '<b>Име на собственик</b>',
                    sortable: true,
                    width: 150
                }, {
                    field: 'egn_eik',
                    title: '<b>ЕГН/EIK</b>',
                    sortable: true,
                    width: 150
                }
            ]],
        pagination: true,
        rownumbers: true,
        toolbar: [{
                id: 'btnaddnewcontractowner',
                text: 'Нов собственик',
                iconCls: 'icon-add',
                handler: function() {
                    clearAddOwnerInputDataFields();
                    jQuery('#win-add-new-owner').window('open');
                }
            }, {
                id: 'btnaddfilter',
                text: 'Филтриране',
                iconCls: 'icon-filter',
                handler: function() {
                    jQuery('#win-add-contragent-filter').window('open');
                }
            }, {
                id: 'btnremovefilter',
                text: 'Покажи всички',
                iconCls: 'icon-clear-filter',
                handler: function() {
                    clearAddContragentFilter();
                }
            }
        ],
        onBeforeLoad: function() {
            jQuery('#contragents-add-tables').datagrid('clearChecked');
            initAddContragentDataFields();
        },
        onSelect: function() {
            reinitAddContragentDataFields();
        }
    });
}

function initFarmingContragentsGrid(sublease_id) {
    jQuery('#farming-contragents-tables').datagrid({
        //title:'Стопанства',
        iconCls:'icon-agriculture',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 10,
        fit: true,
        singleSelect: true,
        fitColumns: true,
        showFooter: true,
        border: false,
        url: 'index.php?subleases-rpc=sublease-farming-contragents-grid',
        rpcParams: [{
            type:'view',
            sublease_id:sublease_id
        }],
        rpcMethod: "read",
        loader : EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter : EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
        sortName: 'name',
        sortOrder: 'asc',
        idField:'id',
        columns: [[
            {
                field: 'farming',
                title: '<b>Стопанство</b>',
                sortable: false,
                width: 100
            }, {
                field: 'rep_names',
                title: '<b>Представител</b>',
                sortable: false,
                width: 100
            }
        ]],
        pagination:false,
        rownumbers:true,
        toolbar: [
            {
                id: 'btnaddsubleasecontragent',
                text: 'Добавяне',
                iconCls: 'icon-add',
                handler: function() {
                    if (!hasPlotRightsRW) {
                        messagerPlotsWriteRights();
                        return false;
                    };
                    if (sublease_id) {
                        var gridData = jQuery('#farming-contragents-tables').datagrid('getData');
                        if (gridData.total > 0) {
                            jQuery.messager.alert('Внимание', 'Не може да добавите повече от едно стопанства като контрагент към един договор за преотдаване.', 'warning');
                            return false;
                        }
                        contragent_type = 'farming';
                        initAddFarmingContragentsGrid(sublease_id);
                        jQuery('#win-add-contragent').window('open');
                        //grid is used for owners and contragents
                        //grid ID should be passed
                        initRepsGrid();
                    } else {
                        jQuery.messager.alert('Грешка', 'Не е избран договор.');
                    }
                }
            }, {
                id: 'btndeletesubleasecontragent',
                text: 'Премахване',
                iconCls: 'icon-delete',
                handler: function() {
                    if (!hasPlotRightsRW) {
                        messagerPlotsWriteRights();
                        return false;
                    };
                    var getChecked = jQuery('#farming-contragents-tables').datagrid('getChecked');

                    if (getChecked[0]) {
                        var contragentId = getChecked[0].id;
                        var contragentType = 'farming';
                        jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да премахнете този запис?', function(r) {
                            if (r) {
                                TF.Rpc.Subleases.SubleaseContragentData.deleteSubleaseContragent(contragentId, contragentType)
                                .done(function (data)
                                {
                                    jQuery('#win-add-contragent').window('close');
                                    jQuery('#sublease-contragents-tables').datagrid('reload');
                                    jQuery('#farming-contragents-tables').datagrid('reload');

                                });
                            }
                        });
                    } else {
                        jQuery.messager.alert('Грешка', 'Не е избрана страна по договор!');
                    }
                }
            }
        ],
        onBeforeLoad: function() {
            jQuery('#farming-contragents-tables').datagrid('clearChecked');
        },
        onLoadSuccess: function() {
            initContragentsGrid(sublease_id);
        }
    });
}

function initAddFarmingContragentsGrid(sublease_id) {
    jQuery('#contragents-add-tables').datagrid({
        title:'Стопанства',
        iconCls:'icon-agriculture',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 10,
        fit: true,
        singleSelect: true,
        fitColumns: true,
        showFooter: true,
        border: true,
        url: 'index.php?subleases-rpc=sublease-farming-contragents-grid',
        rpcParams: [{
            type:'add',
            sublease_id:sublease_id
        }],
        rpcMethod: "read",
        loader : EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter : EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
        sortName: 'name',
        sortOrder: 'asc',
        idField:'id',
        columns:[[
        {
            field:'name',
            title:'<b>Име</b>',
            sortable:true,
            width:200
        },{
            field:'address',
            title:'<b>Адрес на стопанството</b>',
            sortable:true,
            width:200
        },{
            field:'company',
            title:'<b>Фирма</b>',
            sortable:true,
            width:200
        },{
            field:'bulstat',
            title:'<b>Булстат</b>',
            sortable:true,
            width:200
        },{
            field:'company_address',
            title:'<b>Адрес на фирмата</b>',
            sortable:true,
            width:200
        },{
            field:'mol',
            title:'<b>МОЛ</b>',
            sortable:true,
            width:200
        }
        ]],
        pagination:true,
        rownumbers:true,
        toolbar: null,
        onBeforeLoad: function() {
            jQuery('#contragents-add-tables').datagrid('clearChecked');
            initAddContragentDataFields();
        },
        onSelect: function() {
            reinitAddContragentDataFields();
        }
    });
}

function initAddContragentDataFields() {
    if(contragent_type == 'farming') {
        jQuery('#contragent-rep-by-himself > input').attr('disabled',true);
        jQuery('#contragent-rep-by-another > input').prop('checked', true);
        jQuery('#contragent-rep-proxy-message').hide();
        jQuery('#contragent-rep-proxy-fields').show();
    }
    else {
        jQuery('#contragent-rep-by-himself > input').attr('disabled',false);
        jQuery('#contragent-rep-by-himself > input').prop('checked', true);
        jQuery('#contragent-rep-proxy-message').show();
        jQuery('#contragent-rep-proxy-fields').hide();
    }

    jQuery('#contragent-rep-proxy-date > input').datebox({'editable': false});

    jQuery('#contragent-rep-proxy-num > input').val('');
    jQuery('#contragent-rep-proxy-date > input').datebox('setValue');

    jQuery('#contragent-rep-by-another > input').change(function() {
        jQuery('#contragent-rep-proxy-message').hide();
        jQuery('#contragent-rep-proxy-fields').show();
    });

    jQuery('#contragent-rep-by-himself > input').change(function() {
        jQuery('#contragent-rep-proxy-fields').hide();
        jQuery('#contragent-rep-proxy-message').show();
    });

    jQuery('#contragent-rep-by-himself > input').prop('checked', true);
    //display proxy fields
    jQuery('#contragent-rep-proxy-message').show();
    jQuery('#contragent-rep-proxy-fields').hide();

    jQuery('#contragent-rep-notary-num > input').numberbox({
        filter: function(e) {
            var isNumber = e.which > 47 && e.which < 58;
            return isNumber;
        }
    });
}

function reinitAddContragentDataFields() {
    var ownerData = jQuery('#contragents-add-tables').datagrid('getChecked');

    if(contragent_type == 'farming' || (ownerData.length && ownerData[0].owner_type == 0)) {
        jQuery('#contragent-rep-by-himself > input').attr('disabled',true);
        jQuery('#contragent-rep-by-another > input').prop('checked', true);
        jQuery('#contragent-rep-proxy-message').hide();
        jQuery('#contragent-rep-proxy-fields').show();
    }
    else {
        jQuery('#contragent-rep-by-himself > input').attr('disabled',false);
        jQuery('#contragent-rep-by-himself > input').prop('checked', true);
        jQuery('#contragent-rep-proxy-message').show();
        jQuery('#contragent-rep-proxy-fields').hide();
    }

    jQuery('#contragent-rep-proxy-date > input').datebox({});

    jQuery('#contragent-rep-by-another > input').change(function() {
        jQuery('#contragent-rep-proxy-message').hide();
        jQuery('#contragent-rep-proxy-fields').show();
    });

    jQuery('#contragent-rep-by-himself > input').change(function() {
        jQuery('#contragent-rep-proxy-fields').hide();
        jQuery('#contragent-rep-proxy-message').show();
    });
}

function validateNewSubleaseContragentRel() {
    var obj = new Object();

    obj.sublease_id = jQuery('#subleases-tree').tree('getSelected').id;

    var contragentData = jQuery('#contragents-add-tables').datagrid('getSelected');
    if (!contragentData) {
        jQuery.messager.alert('Грешка', 'Не е избрана страна по договора!');
        return false;
    }
    if(contragent_type == 'owner') {
        obj.owner_id = contragentData.id;
    }
    if(contragent_type == 'farming') {
        obj.farming_id = contragentData.id;
    }
    var hasRepresentative = jQuery('#contragent-rep-by-another > input').is(':checked');
    var repData = jQuery('#contragent-reps-tables').datagrid('getSelected');

    if ((hasRepresentative && !repData) || (contragent_type == 'farming' && !repData)) {
        jQuery.messager.alert('Грешка', 'Не е избран представител!');
        return false;
    }
    if (repData && repData.id == false) {
        jQuery.messager.alert('Грешка', 'Не можете да добавяте представител, преди да го запазите.');
        return false;
    }
    obj.self_rep = true;
    if (repData) {
        obj.self_rep = false;
        obj.rep_id = repData.id;
    }

    obj.proxy_date = jQuery('#contragent-rep-proxy-date > input').datebox('getValue') || null;
    obj.proxy_num = jQuery('#contragent-rep-proxy-num > input').val() || null;
    obj.notary_name = jQuery('#contragent-rep-notary-name > input').val() || null;
    obj.notary_number = jQuery('#contragent-rep-notary-num > input').numberbox('getValue') || null;
    obj.notary_address = jQuery('#contragent-rep-notary-address > input').val() || null;

    TF.Rpc.Subleases.SubleaseContragentData.addSubleaseContragent(obj)
        .done(function (data) {
            jQuery('#win-add-contragent').window('close');
            jQuery('#sublease-contragents-tables').datagrid('reload');
            jQuery('#farming-contragents-tables').datagrid('reload');

        })
        .fail(function (errorObj) {
            jQuery.messager.alert('Внимание', errorObj.getMessage(), 'warning');
        });
}

//filter for add owners grid
function filterAddContragent() {
    if (jQuery('#search-contragent-name > input').val() != ''
            || jQuery('#search-contragent-egn > input').val() != ''
            || jQuery('#search-contragent-eik > input').val() != ''
            || jQuery('#search-contragent-company > input').val() != '') {
        jQuery('#contragents-add-tables').datagrid({
            rpcParams: [{
                type: 'add',
                sublease_id: jQuery('#subleases-tree').tree('getSelected').id,
                filters:{
                    contragent_name: jQuery('#search-contragent-name > input').val(),
                    egn: jQuery('#search-contragent-egn > input').val(),
                    eik: jQuery('#search-contragent-eik > input').val(),
                    company_name: jQuery('#search-contragent-company > input').val()
                }
            }]
        });
        jQuery('#win-add-contragent-filter').window('close');
    } else {
        jQuery.messager.alert('Грешка', 'Моля задайте данни за търсене.');
    }
}

function clearAddContragentFilter() {
    jQuery('#search-contragent-name > input').val('');
    jQuery('#search-contragent-egn > input').val('');
    jQuery('#search-contragent-eik > input').val('');
    jQuery('#search-contragent-company > input').val('');

    jQuery('#contragents-add-tables').datagrid({
        rpcParams: [{
            type: 'add',
            sublease_id: jQuery('#subleases-tree').tree('getSelected').id,
        }]
    });
}
//end of filter for add owners grid

function displayTables(owner_type) {
    if (owner_type == 'physical') {
        jQuery('#company-data').hide();
        jQuery('#owner-data').show();
        jQuery('#is-dead').show();
        jQuery('#is-foreigner').show();
    }
    if (owner_type == 'legal') {
        jQuery('#owner-data').hide();
        jQuery('#company-data').show();
        jQuery('#is-dead').hide();
        jQuery('#is-foreigner').hide();
    }
}

function setAddOwnerFieldsValidators() {
    numLengthbox();
    jQuery('#name > input').validatebox({
        required: true,
        missingMessage: 'Моля въведете име на собственик.'
    });
    jQuery('#surname > input').validatebox({
        required: true,
        missingMessage: 'Моля въведете презиме на собственик.'
    });
    jQuery('#lastname > input').validatebox({
        required: true,
        missingMessage: 'Моля въведете фамилия на собственик.'
    });

    let egnBox = new EgnValidateBox('#egn > input');
    new CardIdValidateBox('#lk_nomer > input');

    jQuery('#lk_izdavane > input').validatebox({});
    jQuery('#company_name > input').validatebox({
        required: true,
        missingMessage: 'Моля въведете име на фирма.'
    });
    jQuery('#eik > input').numberbox({
        required: true,
        validType: 'setLength[9]',
        missingMessage: 'Моля въведете ЕИК номер.',
        parser: function(s){
            return s;
        }
    });
    jQuery('#mol > input').validatebox({
        missingMessage: 'Моля въведете МОЛ.'
    });
    jQuery('#company_address > input').validatebox({
        missingMessage: 'Моля въведете адрес на компанията.'
    });
    jQuery('#mobile > input').validatebox({
        missingMessage: 'Моля въведете мобилен телефон.'
    });
    jQuery('#email > input').validatebox({
        missingMessage: 'Моля въведете имейл на собственика.'
    });

    jQuery('#is_foreigner').on('change', function () {
        if (jQuery('#is_foreigner').is(':checked')) {
            egnBox.disableValidation();
        } else {
            egnBox.enableValidation();
        }
    });

    jQuery('#is-dead > input').on('change', function () {
        if (jQuery('#is_foreigner').is(':checked') && jQuery('#is-dead > input').is(':checked')) {
            egnBox.disableValidation();
        } else {
            egnBox.enableValidation();
        }

        if (jQuery('#is-dead > input').is(':checked')) {
            jQuery('#prepiska-row').show();
        } else {
            jQuery('#prepiska-row').hide();
        }
    });

    jQuery('#is_foreigner').trigger('change');
    jQuery('#is-dead > input').trigger('change');

    var ekateComboboxData = ComboboxData.EkateCombobox;

    ekateComboboxData[0].selected = true;

    jQuery('#rent-place input').combobox({
        data: ekateComboboxData,
        valueField: 'ekate',
        textField: 'text',
        value: '',
        filter: function (q, row) {
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if (text.indexOf(find) != -1 || value.indexOf(find) != -1) {
                return true;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function validateNewOwnerSubmitInfo() {
    if (jQuery('#is_physical input').is(':checked')) {
        if (jQuery('#name > input').validatebox('isValid')
                && jQuery('#surname > input').validatebox('isValid')
                && jQuery('#lastname > input').validatebox('isValid')
                && new EgnValidateBox('#egn > input').isValid()) {
            return true;
        } else {
            jQuery.messager.alert('Грешка', 'Моля попълнете задължителните полета.');
            return false;
        }
    } else {
        if (jQuery('#company_name > input').validatebox('isValid')
                && jQuery('#eik > input').numberbox('isValid')) {
            return true;
        } else {
            jQuery.messager.alert('Грешка', 'Моля попълнете задължителните полета.');
            return false;
        }
    }
}

jQuery(function() {
    jQuery('#btnsaveowner').bind('click', function() {
        if (!hasPlotRightsRW) {
            messagerPlotsWriteRights();
            return false;
        };
        if (validateNewOwnerSubmitInfo())
        {
            var obj = getAddOwnerInputFieldsData();
            TF.Rpc.Contracts.ContractsOwnersGrid.addNewOwner(obj)
            .done(function (data)
            {
                jQuery('#win-add-new-owner').window('close');
                jQuery('#contragents-add-tables').datagrid('reload');
                if (obj.is_dead) {
                    addOwnerHeritorHelper(data);
                    newlyAddedOwnerId = data;
                }else {
                    newlyAddedOwnerId = 0;
                }

            })
            .fail(function (data) {
                RpcErrorHandler.show(data);
            });
        }
    });
});

function clearAddOwnerInputDataFields() {
    jQuery('#name > input').val('');
    jQuery('#surname > input').val('');
    jQuery('#lastname > input').val('');
    new CardIdValidateBox('#lk_nomer > input').setValue('');
    jQuery('#lk_izdavane > input').val('');
    jQuery('#company_name > input').val('');
    jQuery('#mol > input').val('');
    jQuery('#company_address > textarea').val('');
    jQuery('#phone > input').val('');
    jQuery('#fax > input').val('');
    jQuery('#mobile > input').val('');
    jQuery('#email > input').val('');
    jQuery('#iban > input').val('');
    jQuery('#address > textarea').val('');
    jQuery('#is_legal > input').prop('checked', false);
    jQuery('#is_physical > input').prop('checked', true);
    jQuery('#is-dead > input').prop('checked', false);
    jQuery('#rent-place > input').val('');
    jQuery('#prepiska > input').val('');
    displayTables('physical');
    new EgnValidateBox('#egn > input').setValue('');
    if (jQuery('#eik > input').data().hasOwnProperty('numberbox')) {
        jQuery('#eik > input').numberbox('setValue');
    } else {
        jQuery('#eik > input').val('');
    }

    jQuery('#prepiska-row').hide();

    setAddOwnerFieldsValidators();
}

function getAddOwnerInputFieldsData() {
    var fieldsData = new Object();
    fieldsData = {
        name: jQuery('#name > input').val(),
        surname: jQuery('#surname > input').val(),
        lastname: jQuery('#lastname > input').val(),
        egn: new EgnValidateBox('#egn > input').getValue(),
        lk_nomer: new CardIdValidateBox('#lk_nomer > input').getValue(),
        lk_izdavane: jQuery('#lk_izdavane > input').val(),
        company_name: jQuery('#company_name > input').val(),
        eik: jQuery('#eik > input').val(),
        mol: jQuery('#mol > input').val(),
        company_address: jQuery('#company_address > textarea').val(),
        phone: jQuery('#phone > input').val(),
        fax: jQuery('#fax > input').val(),
        mobile: jQuery('#mobile > input').val(),
        email: jQuery('#email > input').val(),
        iban: jQuery('#iban > input').val(),
        address: jQuery('#address > textarea').val(),
        is_legal: jQuery('#is_legal > input').is(':checked') ? true : false,
        is_physical: jQuery('#is_physical > input').is(':checked') ? true : false,
        is_dead: jQuery('#is-dead > input').is(':checked') ? true : false,
        rent_place: jQuery('#rent-place > input').combobox('getValue'),
        prepiska: jQuery('#is-dead > input').is(':checked') ? jQuery('#prepiska > input').val() : null
    }
    return fieldsData;
}
