function initSubleasesContractsGrid(sublease_id, plot_id) {
	jQuery("#sublease-contracts-tables").datagrid({
        iconCls: "icon-contract",
        title: "Договори",
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 10,
        fit: true,
        singleSelect: true,
        fitColumns: true,
        showFooter: false,
        border: false,
        url: "index.php?subleases-rpc=sublease-contracts-grid",
        rpcParams: [
            {
                sublease_id: sublease_id,
                plot_id: plot_id
            }
        ],
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
        sortName: "name",
        sortOrder: "asc",
        idField: "id",
        columns: [
            [
                {
                    field: "c_num",
                    title: "<b>Договор</b>",
                    sortable: false,
                    width: 75
                },
                {
                    field: "c_date",
                    title: "<b>Дата</b>",
                    sortable: false,
                    width: 75
                },
                {
                    field: "c_type",
                    title: "<b>Тип договор</b>",
                    sortable: false,
                    width: 75
                },
                {
                    field: "farming",
                    title: "<b>Стопанство</b>",
                    sortable: false,
                    width: 100
                },
                {
                    field: "contract_area",
                    title: "<b>Площ по договор(дка)</b>",
                    sortable: false,
                    width: 100
                }
            ]
        ],
        pagination: false,
        rownumbers: true,
        toolbar: [
            {
                id: "btnviewcontractinfo",
                text: "Информация",
                iconCls: "icon-info",
                handler: function() {
                    var getChecked = jQuery(
                        "#sublease-contracts-tables"
                    ).datagrid("getChecked");
                    if (getChecked[0]) {
                        window.open(
                            "index.php?page=Contracts.Home&contract_id=" +
                                getChecked[0]["id"],
                            "_blank"
                        );
                    } else {
                        jQuery.messager.alert(
                            "Грешка",
                            "Моля изберете договор, за който да бъде показана информация."
                        );
                    }
                }
            }
        ],
        onBeforeLoad: function() {
            jQuery("#sublease-contracts-tables").datagrid("clearChecked");
        },
        onLoadSuccess: function() {
            var plotData = jQuery("#sublease-contracts-tables").datagrid(
                "getData"
            );

            if (plotData["rows"][0]) {
                jQuery("#sublease-contracts-tables").datagrid(
                    "selectRow",
                    0
                );
            } else {
                initSubleaseOwnersGrid(0, 0);
            }
        },
        onSelect: function(rowIndex, rowData) {
            initSubleaseOwnersGrid(
                rowData.pc_rel_id,
                rowData.nm_usage_rights
            );
        },
        rowStyler: function(index, row) {
            if (!row.active) {
                return "color: #aaa";
            }
        }
    });
}
