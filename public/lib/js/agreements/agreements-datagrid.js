var agreementID = 0;

function initAgreementsDataGrid(id) {
    agreementID = id;
    doRequest();

    jQuery('#data-tables').datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 20,
        fit: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?agreements-rpc=agreements-data-datagrid',
        rpcParams: [{
            agreement_id: id,
        }],
        sortName: 'ekate',
        sortOrder: 'asc',
        idField: 'id',
        border: false,
        singleSelect: true,
        rowStyler: function(index, row) {
            if (row.has_match == 0) {
                return 'background-color:#ff0000; color: #fff';
            }
        },
        columns: [[
                {
                    field: 'land',
                    title: '<b>Землище</b>',
                    sortable: true,
                    width: 300
                }, {
                    field: 'ekate',
                    title: '<b>ЕКАТТЕ</b>',
                    sortable: true,
                    width: 300
                }, {
                    field: 'masiv',
                    title: '<b>Масив</b>',
                    sortable: true,
                    width: 300
                }, {
                    field: 'imot',
                    title: '<b>Имот</b>',
                    sortable: true,
                    width: 300
                }, {
                    field: 'area',
                    title: '<b>Площ по споразумение(дка)</b>',
                    sortable: true,
                    width: 500
                }]],
        pagination: true,
        rownumbers: false,
        toolbar: [{
                id: 'btnaddagreementdata',
                text: 'Добавяне',
                iconCls: 'icon-add',
                handler: function() {
                    if (!hasPlotRightsRW) {
                        messagerPlotsWriteRights();
                        return false;
                    };
                    if (agreementID != 0) {
                        clearAddAgreementDataFields();
                        jQuery('#win-add-data').window('open');
                    } else {
                        jQuery.messager.alert('Грешка', 'Моля добавете споразумение.');
                    }
                }
            }, {
                id: 'btndeleteagreementdata',
                text: 'Изтриване',
                iconCls: 'icon-delete',
                handler: function() {
                    if (!hasPlotRightsRW) {
                        messagerPlotsWriteRights();
                        return false;
                    };
                    var agreementData = jQuery('#data-tables').datagrid('getChecked');
                    var agreementLayerData = jQuery('#agreement-layers-tree').tree('getSelected');

                    if (agreementData[0]) {
                        jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да премахнете този запис?', function(r) {
                            if (r) {
                                var obj = new Object();
                                obj.agreement_id = agreementLayerData.id;
                                obj.plot_id = agreementData[0]['gid'];
                                obj.agreement_data_id = agreementData[0]['id'];
                                obj.has_match = agreementData[0]['has_match'];

                                TF.Rpc.Agreements.AgreementsDataGrid.deleteAgreementData(obj)
                                .done(function (data) {
                                    jQuery('#data-tables').datagrid('loadRpc');
                                })
                                .fail(function (errorObj) {

                                });
                            }
                        });
                    } else {
                        jQuery.messager.alert('Грешка', 'Моля изберете запис, който искате да премахнете.');
                    }
                }
            }, {
                id: 'btnfilteragreementdata',
                text: 'Филтриране',
                iconCls: 'icon-filter',
                handler: function() {
                    jQuery('#win-agreement-filter').window('open');
                    initFilterFields(id);
                }
            }, {
                id: 'btnclearfilteragreementdata',
                text: 'Покажи всички',
                iconCls: 'icon-clear-filter',
                handler: function() {
                    clearFilterAgreementData();
                }
            }],
        onSelect: function(rowIndex, rowData) {

        },
        onLoadSuccess: function() {
            doRequest();
        },
        onBeforeLoad: function() {
            jQuery('#data-tables').datagrid('clearChecked');
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function AddAgreementDataHandler() {

    if (jQuery('#agreement-ekate > input').val()
            && jQuery('#agreement-masiv > input').val()
            && jQuery('#agreement-imot > input').val()) {

        dataObj = new Object();
        dataObj.ekate = jQuery('#agreement-ekate > input').val();
        dataObj.masiv = jQuery('#agreement-masiv > input').val();
        dataObj.imot = jQuery('#agreement-imot > input').val();
        dataObj.area = jQuery('#agreement-area > input').val();
        dataObj.agreement_id = jQuery('#agreement-layers-tree').tree('getSelected').id;

        TF.Rpc.Agreements.AgreementsDataGrid.addAgreementData(dataObj)
        .done(function (data) {
            displayAgreementAddResult(data);
        })
        .fail(function (errorObj) {
            jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
        });
    } else {
        jQuery.messager.alert('Грешка', 'Моля попълнете всички полета!');
    }
}

function displayAgreementAddResult(params) {
    if (params) {
        jQuery('#win-add-data').window('close');

        jQuery('#agreement-ekate > input').val('');
        jQuery('#agreement-masiv > input').val('');
        jQuery('#agreement-imot > input').val('');
        jQuery('#agreement-area > input').val('');

        jQuery('#data-tables').datagrid('reload');
    } else {
        jQuery.messager.alert('Грешка', 'Не е намерен имот с такива данни.', 'warning');
        //endLoading();
    }
}

function initFilterFields(agreement_id) {
    jQuery('#agreement-filter-ekate > input').combobox({
        url: 'index.php?agreements-rpc=agreements-ekate-combobox',
        rpcParams: [{
            agreement_id: agreement_id
        }],
        valueField: 'ekate',
        textField: 'text',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        filter: function(q, row){
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
            {
                return true;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    if (!jQuery('#agreement-filter-masiv > input').data().hasOwnProperty('numberbox')) {
        jQuery('#agreement-filter-masiv > input').numberbox({});
    } else {
        jQuery('#agreement-filter-masiv > input').numberbox('reset');
    }

    if (!jQuery('#agreement-filter-imot > input').data().hasOwnProperty('numberbox')) {
        jQuery('#agreement-filter-imot > input').numberbox({});
    } else {
        jQuery('#agreement-filter-imot > input').numberbox('reset');
    }

}

function clearFilterAgreementData() {
    jQuery('#data-tables').datagrid({
        rpcParams: [{
            agreement_id:  jQuery('#agreement-layers-tree').tree('getSelected').id
        }],
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function clearAddAgreementDataFields() {

    if (!jQuery('#agreement-ekate > input').data().hasOwnProperty('numberbox')) {
        jQuery('#agreement-ekate > input').numberbox({});
    } else {
        jQuery('#agreement-ekate > input').numberbox('reset');
    }
    if (!jQuery('#agreement-masiv > input').data().hasOwnProperty('numberbox')) {
        jQuery('#agreement-masiv > input').numberbox({});
    } else {
        jQuery('#agreement-masiv > input').numberbox('reset');
    }
    if (!jQuery('#agreement-imot > input').data().hasOwnProperty('numberbox')) {
        jQuery('#agreement-imot > input').numberbox({});
    } else {
        jQuery('#agreement-imot > input').numberbox('reset');
    }
    if (!jQuery('#agreement-area > input').data().hasOwnProperty('numberbox')) {
        jQuery('#agreement-area > input').numberbox({});
    } else {
        jQuery('#agreement-area > input').numberbox('reset');
    }
}
