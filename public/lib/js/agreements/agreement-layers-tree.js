function initAgreementLayersTree() {
    jQuery('#agreement-layers-tree').tree({
        url: 'index.php?agreements-rpc=agreements-layers-tree',
        animate: true,
        lines: true,
        onBeforeSelect: function(node) {
            var isLeaf = jQuery('#agreement-layers-tree').tree('isLeaf', node.target);

            if (!isLeaf)
                return false;
        },
        onSelect: function(node) {
            var isLeaf = jQuery('#agreement-layers-tree').tree('isLeaf', node.target);
            var layerData = jQuery('#agreement-layers-tree').tree('getSelected');

            if (layerData.id) {

                initAgreementsDataGrid(layerData.id);
            } else {
                initAgreementsDataGrid(0);
            }
        },
        onLoadSuccess: function() {
            var level_1 = jQuery('#agreement-layers-tree').tree('getRoots');

            if (level_1.length == 0) {
                initAgreementsDataGrid(0);
            } else {
                var level_2 = jQuery('#agreement-layers-tree').tree('getChildren', level_1[0].target);
                var level_3 = jQuery('#agreement-layers-tree').tree('getChildren', level_2[0].target);

                jQuery('#agreement-layers-tree').tree('select', level_3[0].target);
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#add-agreement-layer').bind('click', function() {
        if (!hasPlotRightsRW) {
            messagerPlotsWriteRights();
            return false;
        };
        jQuery('#add-window').window('open');
        return false;
    });

    jQuery('#delete-agreement-layer').bind('click', function() {
        if (!hasPlotRightsRW) {
            messagerPlotsWriteRights();
            return false;
        };
        var getSelected = jQuery('#agreement-layers-tree').tree('getSelected');
        if (getSelected.id) {
            jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте че искате да премахнете този запис?', function(r) {
                if (r) {
                    TF.Rpc.Agreements.AgreementsLayersTree.deleteAgreement(getSelected.id)
                    .done(function (data) {
                         jQuery('#agreement-layers-tree').tree('loadRpc');
                    })
                    .fail(function (errorObj) {

                    });
                }
            });
        } else {
            jQuery.messager.alert('Грешка', 'Моля изберете запис, който да бъде премахнат.');
        }

        return false;
    });

    jQuery('#upload-agreement-file').bind('click', function() {
        if (!hasPlotRightsRW) {
            messagerPlotsWriteRights();
            return false;
        };

        var getSelected = jQuery('#agreement-layers-tree').tree('getSelected');

        var agreementData = jQuery('#data-tables').datagrid('getData');

        if (agreementData.total == 0) {
            initFileUploader(getSelected.id);
            jQuery('#win-add-file').window('open');
            return false;
        }
        var recreatedAgreementId = 0;
        var confirmation_message = 'При зареждане на данни от файл, наличните данни в избраното споразумение ще бъдат премахнати и ще бъдат заместени с данните от извършената операция. Сигурни ли сте че искате да продължите?';
        jQuery.messager.confirm('Потвърждение', confirmation_message, function(r) {
            if (r) {
                if (getSelected.id) {
                    TF.Rpc.Agreements.AgreementsLayersTree.recreateAgreement(getSelected.id)
                    .done(function (data) {
                        initFileUploader(data);
                    })
                    .fail(function (errorObj) {
                        initFileUploader(getSelected.id);
                    });
                    jQuery('#win-add-file').window('open');
                    jQuery('#agreement-layers-tree').tree('loadRpc');
                } else {
                    jQuery.messager.alert('Грешка', 'Моля изберете запис, за който да добавите файл.');
                }
            }
        });
        return false;
    });
}
