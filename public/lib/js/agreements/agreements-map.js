var isSuperAdmin = false;
var layersKvs = false;
var renderer = OpenLayers.Util.getParameters(window.location.href).renderer;
if (renderer) {
    OpenLayers.Layer.Vector.prototype.renderers = [renderer];
}

var mapKVS;
var boundsKvsArray;
var layerKvsArray;

jQuery(function() {
    TF.Rpc.Agreements.AgreementsMap.initMap()
    .done(function (data) {
        layersKvs = data;
    })
    .fail(function (errorObj) {

    });
});

function showMapOverlaps() {
    initMapKvs();
}

function initMapKvs() {
    if (!mapKVS) {
        var options = {
            controls: [new OpenLayers.Control.Navigation(), new OpenLayers.Control.PanZoomBar(), new OpenLayers.Control.ScaleLine({bottomInUnits: 'km'})],
            projection: new OpenLayers.Projection("EPSG:900913")
        };
        mapKVS = new OpenLayers.Map('map', options);
        var apiKey = "AiWEso3-IjWyX1aZMuep9Sjl62D6FUMqv8qQGpV-kgbW0qWk61to4nrqHh-2D5HL";
        var bhyb = new OpenLayers.Layer.Bing({
            name: "Bing",
            key: apiKey,
            type: "AerialWithLabels"
        });

        mapKVS.addLayer(bhyb);
    }

    if (layerKvsArray)
    {
        for (var i = 0; i < layerKvsArray.length; i++)
        {
            mapKVS.removeLayer(layerKvsArray[i])
        }
    }

    boundsKvsArray = [];
    for (var i = 0; i < layersKvs.length; i++) {
        boundsKvsArray[i] = new OpenLayers.Bounds.fromString(layersKvs[i].extent).transform(
                new OpenLayers.Projection("EPSG:32635"),
                mapKVS.getProjectionObject()
                );
    }

    layerKvsArray = [];
    for (var i = 0; i < layersKvs.length; i++)
    {
        if (layersKvs[i].extent && layersKvs[i].name)
        {
            layerKvsArray[i] = new OpenLayers.Layer.WMS(
                    layersKvs[i].name,
                    wmsServer + "?map=" + mapPath + groupID + '.map',
                    {
                        layers: layersKvs[i].name,
                        format: 'image/png',
                        transparent: "true"
                                //map: mapPath+userid+'.map'
                    }, {
                displayInLayerSwitcher: false
            }
            );
        }
    }

    if (layersKvs.length)
        mapKVS.addLayers(layerKvsArray);

    if (layersKvs.length)
    {
        mapKVS.zoomToExtent(boundsKvsArray[0]);
    }
    else
    {
        mapKVS.zoomToExtent(new OpenLayers.Bounds.fromString("125190.6162, 4573142.7188, 631370.3273, 4887149.5823").transform(
                new OpenLayers.Projection("EPSG:32635"),
                mapKVS.getProjectionObject())
                );
    }

    mapKVS.render("map");
    reloadAllKvsLayers();
}

function reloadAllKvsLayers() {
    for (var i = 0; i < layerKvsArray.length; i++) {
        layerKvsArray[i].redraw(true);
    }
}

function doRequest() {
    var agreementData = jQuery('#agreement-layers-tree').tree('getSelected');


    if (agreementData == null) {
        TF.Rpc.Agreements.AgreementsMap.initKvs()
        .done(function (data) {
            showMapOverlaps();
            initLegend(data);
        })
        .fail(function (errorObj) {

        });
    } else {
        var yearData = jQuery('#agreement-layers-tree').tree('getParent', agreementData.target);
        var farmingData = jQuery('#agreement-layers-tree').tree('getParent', yearData.target);

        if (agreementData.id) {
            var obj = new Object();
            obj.agreement_id = agreementData.id;
            obj.farming = farmingData.id;
            obj.year = yearData.id;
            TF.Rpc.Agreements.AgreementsMap.initKvs(obj)
            .done(function (data) {
                showMapOverlaps();
                initLegend(data);
            })
            .fail(function (errorObj) {

            });
        }
    }
}

function initLegend(data)
{
    if (data['extent'])
    {
        mapKVS.zoomToExtent(new OpenLayers.Bounds.fromString(data['extent']).transform(
                new OpenLayers.Projection("EPSG:32635"),
                mapKVS.getProjectionObject()));
    }
}