jQuery(function() {
    setUserRights();

    TF.Rpc.Common.CombinedComboboxData.read(null, null, {selected: 'current'})
        .done(function (data) {
            initComboboxes(data);
        })
        .fail(function (error) {
            jQuery.messager.alert('Грешка', error.getMessage(), 'warning');
        });

    jQuery('#btn-add-agreement').bind('click', function() {
        if (!hasPlotRightsRW) {
            messagerPlotsWriteRights();
            return false;
        };
        if (jQuery('#agreement-name > input').val() &&
                jQuery('#agreement-farming > input').combobox('getValue') &&
                jQuery('#agreement-farming-year > input').combobox('getValue')) {

            var obj = new Object();

            obj.name = jQuery('#agreement-name > input').val();
            obj.farming = jQuery('#agreement-farming > input').combobox('getValue');
            obj.year = jQuery('#agreement-farming-year > input').combobox('getValue');
            obj.agg_type = jQuery('#agreement-type > input').combobox('getValue');

            TF.Rpc.Agreements.AgreementsLayersTree.addAgreement(obj)
            .done(function (data) {
                jQuery('#add-window').window('close');
                jQuery('#agreement-layers-tree').tree('loadRpc');
            })
            .fail(function (errorObj) {
                RpcErrorHandler.show(errorObj);
            });

            jQuery('#agreement-name > input').val('');
        } else {
            jQuery.messager.alert('Грешка', 'Моля попълнете всички полета!');
        }
    });

    jQuery('#save-agreement-data').bind('click', function() {
        if (jQuery('#agreement-ekate > input').val() &&
                jQuery('#agreement-masiv > input').val() &&
                jQuery('#agreement-imot > input').val() &&
                jQuery('#agreement-area > input').val()) {
            AddAgreementDataHandler();
        } else {
            jQuery.messager.alert('Грешка', 'Моля попълнете всички полета!');
        }
    });

    jQuery('#bnt-filter-agreement-data').bind('click', function () {

        jQuery('#win-agreement-filter').window('close');

        var filterObj = {};
        filterObj.ekate = jQuery('#agreement-filter-ekate > input').combobox('getValues');
        filterObj.masiv = jQuery('#agreement-filter-masiv > input').val();
        filterObj.imot = jQuery('#agreement-filter-imot > input').val();
        filterObj.agreement_id =  jQuery('#agreement-layers-tree').tree('getSelected').id;

        jQuery('#data-tables').datagrid({
            rpcParams: [
                filterObj
            ],
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    });

    //agreement data fields
    jQuery('#agreement-ekate > input').numberbox({
        decimalSeparator: '',
        parser: function (number) {
            return number;
        }
    });

    jQuery('#agreement-masiv > input').numberbox({});
    jQuery('#agreement-imot > input').numberbox({});
    jQuery('#agreement-area > input').numberbox({
        precision: 3
    });

    jQuery('#fullscreen-win').window({
        title: 'Карта',
        iconCls: "icon-map",
        fit: true,
        resizable: false,
        collapsible: false,
        minimizable: false,
        maximizable: false,
        closed: true,
        shadow: false,
        draggable: false,
        onClose: function() {
            mapKVS.render("map");
        }
    });

    initAgreementLayersTree();

    jQuery('#agreements-kms-intersect').bind('click', function() {
        var confirmation_message = 'При зареждане на данни чрез пресичане на КВС имоти с "Данни от комасация",\nналичните данни в избраното споразумение ще бъдат премахнати и ще бъдат заместени\nс резултатите от извършената операция. Сигурни ли сте че искате да продължите?';
        jQuery.messager.confirm('Потвърждение', confirmation_message , function(r) {
            if (r) {
                var agreement_id = jQuery('#agreement-layers-tree').tree('getSelected').id;
                TF.Rpc.Agreements.AgreementsLayersTree.kmsIntersection(agreement_id)
                .done(function (data) {
                    jQuery('#data-tables').datagrid('loadRpc');
                    jQuery('#agreement-layers-tree').tree('loadRpc');
                    return false;
                })
                .fail(function (errorObj) {
                    jQuery.messager.alert('Грешка',errorObj.getMessage(),'warning');
                });
            }
        });
        return false;
    });

    function initComboboxes(ComboboxData){
        var newFarmingComboboxData = [];
        let selectedOptionId = 0;
            ComboboxData.FarmingCombobox.forEach(function (el) {
            if (el.id !== "") {
                el.selected = false;
                let hasPermission = Object.values(ComboboxData.UserFarmingPermissions).includes(el.id);
                if(!hasPermission) {
                    el.disabled = true;
                } else {
                    if (selectedOptionId == 0) {
                        selectedOptionId = el.id;
                        el.selected = true;
                    }
                }

                newFarmingComboboxData.push(el);
            }
        });

        jQuery('#agreement-farming > input').combobox({
            data: newFarmingComboboxData,
            rpcParams: [{
                selected: true
            }],
            valueField: 'id',
            textField: 'name',
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });

        jQuery('#agreement-farming-year > input').combobox({
            data: ComboboxData.FarmingYearCombobox,
            valueField: 'id',
            textField: 'title',
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });

        jQuery('#agreement-type > input').combobox({
            data: ComboboxData.ContractAggTypesCombobox,
            rpcParams: [{
                selected: true
            }],
            textField: 'name',
            valueField: 'id' ,
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });

    }

});
function initFileUploader(agreement_id) {
    const url  = "index.php?json=agreements-upload"; 

    jQuery("#uploader").pluploadQueue({
        // General settings
        runtimes: 'gears,html5,flash,silverlight,browserplus',
        url: url,
        max_file_size: '100mb',
        unique_names: true,
        multipart_params : {
            "agreement_id" : agreement_id,
        },
        // Flash settings
        flash_swf_url: 'lib/js_external/fileupload/plupload.flash.swf',
        filters: [
            {
                title: "CSV Файлове",
                extensions: "csv"
            },
        ]

    });

    var uploader = jQuery('#uploader').pluploadQueue();
    uploader.bind('UploadComplete', function() {
        jQuery('#win-add-file').window('close');
        jQuery('#agreements-tables').datagrid('reload');
        jQuery('#agreement-layers-tree').tree('loadRpc');
    });
}
