function initOwnersInheritedPlotstGrid(owner_id) {

    var ownersInheritedPlotsTables = jQuery('#owners-inherited-plots-tables');
    var isDatagridBound = ownersInheritedPlotsTables.data().hasOwnProperty('datagrid');

    if (isDatagridBound) {
        if (owner_id !== 0) {
            ownersInheritedPlotsTables.datagrid({
                url: 'index.php?owners-rpc=owners-inherited-plots-grid',
                rpcParams: [owner_id, "", [], []],
            });
        } else {
            ownersInheritedPlotsTables.datagrid({data: {rows: [], total: 0}});
            initOwnersDocumentsGrid();
        }
        return;
    }
    ownersInheritedPlotsTables.datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 10,
        fit: true,
        fitColumns: false,
        showFooter: true,
        sortName: 'owner',
        sortOrder: 'asc',
        idField: 'id',
        singleSelect: true,
        border: false,
        columns: [[
                {
                    field: 'land',
                    title: '<b>Землище</b>',
                    sortable: true,
                    width: 120
                }, {
                    field: 'kad_ident',
                    title: '<b>Имот</b>',
                    sortable: true,
                    width: 120
                }, {
                    field: 'ownage',
                    title: '<b>Площ по дог.(дка)</b>',
                    sortable: true,
                    width: 100
                }, {
                    field: 'percent',
                    title: '<b>Собственост(%)</b>',
                    sortable: false,
                    width: 100,
                    formatter: function(value, row){
                        if (!row.isFooter) {
                            var percent;
                            if(row.numerator && row.denominator) {
                                var percent = new Fraction(row.numerator / row.denominator * 100);
                                var fraction = row.numerator + '/' + row.denominator;
                                value = percent.toString() + '% (' +  fraction + ')';

                                return value;
                            }

                            var fraction = new Fraction(row.percent / 100);
                            var percent = fraction.mul(100);
                            value = percent.toString() + '% (' +  fraction.simplify().toFraction() + ')';

                            return value;
                        }
                    }
                }, {
                    field: 'owner',
                    title: '<b>Наследен от</b>',
                    sortable: true,
                    width: 150
                }, {
                    field: 'ntp',
                    title: '<b>НТП</b>',
                    sortable: true,
                    width: 120
                }, {
                    field: 'category',
                    title: '<b>Категория</b>',
                    sortable: true,
                    width: 120
                }, {
                	field: 'farm_name',
                    title: '<b>Стопанство</b>',
                    sortable: true,
                    width: 150
                }, {
                    field: 'c_num',
                    title: '<b>Договор</b>',
                    sortable: true,
                    width: 100
                }, {
                    field: 'c_date',
                    title: '<b>Сключен</b>',
                    sortable: true,
                    width: 100
                }, {
                    field: 'start_date',
                    title: '<b>Влиза в сила</b>',
                    sortable: true,
                    width: 100
                }, {
                    field: 'due_date',
                    title: '<b>Изтича</b>',
                    sortable: true,
                    width: 100
                }
            ]],
        pagination: true,
        rownumbers: true,
		toolbar: [{
			id: 'btnaddfilter',
			text: 'Филтриране',
			iconCls: 'icon-filter',
			handler: function() {
				jQuery('#win-owners-plots-filter').window('open');
			}
		}, {
			id: 'btninfoplot',
			text: 'Информация за имот',
			iconCls: 'icon-info',
			handler: function() {
				var getSelected = jQuery('#owners-inherited-plots-tables').datagrid('getSelected');

				if (getSelected) {
					window.open("index.php?page=Plots.Home&plot_id=" + getSelected.gid, '_blank');
				} else {
					jQuery.messager.alert('Грешка', 'Моля изберете имот, за който да бъде показана информация.');
				}
			}
		}, {
			id: 'btninfocontract',
			text: 'Информация за договор',
			iconCls: 'icon-info',
			handler: function() {
				var getSelected = jQuery('#owners-inherited-plots-tables').datagrid('getSelected');

				if (getSelected) {
					window.open("index.php?page=Contracts.Home&contract_id=" + getSelected.contract_id, '_blank');
				} else {
					jQuery.messager.alert('Грешка', 'Моля изберете запис, за който да бъде показана информация за договора.');
				}
			}
		}],
        onClickRow: function() {
           initOwnersDocumentsGrid();
        },
        onLoadSuccess: function(data) {
            if(data.rows.length === 0) {
                jQuery('#owners-plots-tabs').tabs('disableTab', 1)
            } else {
                jQuery('#owners-plots-tabs').tabs('enableTab', 1)
            }
                            
            jQuery('#owners-inherited-plots-tables').datagrid('unselectAll');
            jQuery('#owners-inherited-plots-tables').datagrid('uncheckAll');

            if(jQuery('#owners-plots-tabs').tabs('getTabIndex', jQuery('#owners-plots-tabs').tabs('getSelected')) === 1)
            {
                initOwnersDocumentsGrid();
            }
        },
        rowStyler: function(index, row){
            if (!row.operational && row.active){
                return 'color:#aaa;';
            } else if (!row.active) {
                return 'color:#aaa; text-decoration: line-through';
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
    });
}
