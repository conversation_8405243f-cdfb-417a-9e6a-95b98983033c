function initOwnersDocumentsGrid(owner_id) {
    var selectedOwner = jQuery('#owners-tree').tree('getSelected');
    owner_id = 0;

    if (selectedOwner !== null) {
        owner_id = selectedOwner.id;
    }

    var isOwnersPlotsDatagridBound = jQuery('#owners-plots-tables').data().hasOwnProperty('datagrid');
    var isOwnersInheritedPlotsDatagridBound = jQuery('#owners-inherited-plots-tables').data().hasOwnProperty('datagrid');
    var tabIndex = jQuery('#owners-plots-tabs').tabs('getTabIndex', jQuery('#owners-plots-tabs').tabs('getSelected'));
    var selected_plot;
    var selected_plot_id;
    if(tabIndex === 0 && isOwnersPlotsDatagridBound === true)
    {
        selected_plot = jQuery('#owners-plots-tables').datagrid('getSelected');
    }
    else if(tabIndex === 1 && isOwnersInheritedPlotsDatagridBound === true)
    {
        selected_plot = jQuery('#owners-inherited-plots-tables').datagrid('getSelected');
    }

    if(selected_plot === null || selected_plot === undefined)
    {
        selected_plot_id = null;
    }else{
        selected_plot_id = selected_plot.gid;
    }

    jQuery('#owners-documents-tables').datagrid({
        rownumbers: true,
        singleSelect: true,
        onSelectCheck: true,
        onCheckSelect: true,
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        fit: true,
        fitColumns: true,
        showFooter: true,
        pagination: false,
        height: 250,
        idField: 'id',
        sortName: 'id',
        sortOrder: 'asc',
        url: 'index.php?owners-rpc=owners-documents',
        rpcParams:[{
            type: 'grid',
            owner_id: owner_id,
            plot_id: selected_plot_id
        }],
        columns:[[
            {
                field:'type_id',
                title:'Тип на документа',
                sortable:true,
                width: 200,
                formatter:function(value,row){
                                return row.name;
                },
                editor: {
                            type: 'combobox',
                            options: {
                                valueField: 'type_id',
                                textField: 'name',
                                data: ComboboxData.DSTypeCombobox,
                                required: true,
                                missingMessage: 'Изберете типа на документа',
                                tipPosition: 'left',
                                loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                                loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
                            }
                        }
            },{
                field:'number',
                title:'Номер на документа',
                sortable:true,
                width: 200,
                editor: {
                            type: 'validatebox',
                            options: {
                                required: true,
                                missingMessage: 'Въведете номер на документа',
                                tipPosition: 'left'
                            }
                        }
            },{
                field:'date',
                title:'Дата',
                sortable:true,
                width: 150,
                editor: {
                            type: 'datebox',
                            options: {
                                editable: true,
                                required: true,
                                missingMessage: 'Въведете датата на документа',
                                tipPosition: 'left'
                            }
                        }
            },{
                field:'kad_ident',
                title:'Имот',
                sortable:false,
                width: 200
            }, {
                field:'attachment',
                title:'Прикачен файл',
                sortable:false,
                width: 200,
                formatter: function (index, row) {
                    if (row.id && row.attachment) {
                        return row.attachment.length > 20 ? row.attachment.substring(0,20) + '...' : row.attachment;
                    }
                }
            }, {
                field:'attachment_actions',
                sortable:false,
                width: 150,
                align: 'center',
                formatter: function (index, row) {
                    if (row.id) {
                        return createDocumentsAttachmentButtons(row)
                    }
                }
            }
        ]],
        toolbar: [
            {
                id: 'btnadd',
                text: 'Добавяне',
                iconCls: 'icon-add',
                handler: function() {
                    if (!hasPlotRightsRW) {
                        messagerPlotsWriteRights();
                        return;
                    }
                    var getSelected = jQuery('#owners-tree').tree('getSelected');

                    if (getSelected) {
                        if (documentEditingClosed()) {
                            documentEditIndex = 0;

                            var tabIndex = jQuery('#owners-plots-tabs').tabs('getTabIndex', jQuery('#owners-plots-tabs').tabs('getSelected'));
                            var selected_plot;
                            if(tabIndex === 0)
                            {
                                selected_plot = jQuery('#owners-plots-tables').datagrid('getSelected');
                            }
                            else if(tabIndex === 1)
                            {
                                selected_plot = jQuery('#owners-inherited-plots-tables').datagrid('getSelected');
                            }

                            if(selected_plot === null || selected_plot === undefined)
                            {
                                jQuery.messager.alert('Грешка', 'Моля изберете имот към документа.');
                                return false;
                            }

                            jQuery('#owners-documents-tables').datagrid('insertRow',
                            {
                                index: documentEditIndex,
                                row: {
                                    id: false,
                                    owner_id: owner_id
                                }
                            });
                            jQuery('#owners-documents-tables').datagrid('beginEdit', documentEditIndex).datagrid('selectRow', documentEditIndex);
                        }
                    } else
                    {
                        jQuery.messager.alert('Грешка', 'Моля изберете собственик.');
                    }
                }
            }, {
                id: 'btnedit',
                text: 'Редактиране',
                iconCls: 'icon-edit',
                handler: function() {
                    if (!hasPlotRightsRW) {
                        messagerPlotsWriteRights();
                        return;
                    }
                    var getChecked = jQuery('#owners-documents-tables').datagrid('getChecked');

                    if (getChecked[0]) {
                        var index = jQuery('#owners-documents-tables').datagrid('getRowIndex', getChecked[0]);

                        if (documentEditIndex != index) {
                            if (documentEditingClosed()) {
                                jQuery('#owners-documents-tables').datagrid('beginEdit', index);
                                documentEditIndex = index;
                            } else {
                                jQuery('#owners-documents-tables').datagrid('selectRow', documentEditIndex);
                            }
                        }
                    } else
                    {
                        jQuery.messager.alert('Грешка', 'Моля изберете документ.');
                    }
                }
            }, {
                id: 'btndel',
                text: 'Изтриване',
                iconCls: 'icon-remove',
                handler: function() {
                    if (!hasPlotRightsRW) {
                        messagerPlotsWriteRights();
                        return;
                    }
                    var getChecked = jQuery('#owners-documents-tables').datagrid('getChecked');

                    if (getChecked[0]) {
                        if(getChecked[0].id){
                            jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да изтриете този документ?', function(r) {
                                if (r) {

                                    TF.Rpc.Owners.OwnersDocuments.deleteOwnerDocument(getChecked[0].id).done(function (dataObj) {
                                    })
                                    .fail(function (errorObj) {
                                    });

                                    jQuery('#owners-documents-tables').datagrid('reload');
                                }
                            });
                            documentEditIndex = undefined;
                        }
                    } else
                    {
                        jQuery.messager.alert('Грешка', 'Моля изберете документ.');
                    }
                }
            }, {
                id: 'btnsave',
                text: 'Запази',
                iconCls: 'icon-save',
                handler: function() {
                    if (!hasPlotRightsRW) {
                        messagerPlotsWriteRights();
                        return;
                    }
                    documentEditingClosed();
                }
            }, {
                id: 'btncancel',
                text: 'Отмени',
                iconCls: 'icon-undo',
                handler: function() {
                    if (!hasPlotRightsRW) {
                        messagerPlotsWriteRights();
                        return;
                    }
                    rejectDocumentChanges();
                }
            }
        ],
        onCheck: function(index, rowData) {
            if (documentEditIndex != index) {
                documentEditingClosed();
            }
        },
        onBeforeLoad: function() {
            jQuery('#owners-documents-tables').datagrid('uncheckAll');
            jQuery('#owners-documents-tables').datagrid('unselectAll');

            documentEditIndex = undefined;
        },
        onLoadSuccess: function() {
            jQuery(this).datagrid('getPanel').find('a.easyui-linkbutton').linkbutton();
            jQuery(this).datagrid('getPanel').find('a.easyui-tooltip').tooltip();
            jQuery(this).datagrid('fixRowHeight');
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function documentEditingClosed() {
    if (documentEditIndex === undefined) {
        return true;
    }

    var tabIndex = jQuery('#owners-plots-tabs').tabs('getTabIndex', jQuery('#owners-plots-tabs').tabs('getSelected'));
    var selected_plot;
    if(tabIndex === 0)
    {
        selected_plot = jQuery('#owners-plots-tables').datagrid('getSelected');
    }
    else if(tabIndex === 1)
    {
        selected_plot = jQuery('#owners-inherited-plots-tables').datagrid('getSelected');
    }

    if (jQuery('#owners-documents-tables').datagrid('validateRow', documentEditIndex))
    {
        jQuery('#owners-documents-tables').datagrid('endEdit', documentEditIndex);
        jQuery('#owners-documents-tables').datagrid('acceptChanges');

        var rows = jQuery('#owners-documents-tables').datagrid('getRows');
        var obj = rows[documentEditIndex];
        obj.plot_id = (selected_plot !== null) ? selected_plot.gid : rows[documentEditIndex].plot_id;

        TF.Rpc.Owners.OwnersDocuments.saveOwnerDocument(obj).done(function (dataObj) {
        })
        .fail(function (errorObj) {
        });

        documentEditIndex = undefined;

        jQuery('#owners-documents-tables').datagrid('reload');

        return true;
    } else {
        return false;
    }
}

function rejectDocumentChanges() {
    jQuery('#owners-documents-tables').datagrid('rejectChanges');
    jQuery('#owners-documents-tables').datagrid('uncheckAll');
    jQuery('#owners-documents-tables').datagrid('unselectAll');

    documentEditIndex = undefined;
}

function currentDate(){
    var date = new Date();
    var y = date.getFullYear();
    var m = date.getMonth()+1;
    var d = date.getDate();
    return y+'-'+m+'-'+d;
}


function initOwnerDocumentUpload(document_id) {
    jQuery('#win-add-file').window('open');
    var url  = "index.php?json=owners-upload";
    var uploader;

    jQuery("#uploader").pluploadQueue({
        // General settings
        runtimes: 'gears,html5,flash,silverlight,browserplus',
        url: url,
        max_file_size: '100pmb',
        unique_names: true,
        multipart_params : {
			document_id: document_id
		},
        // Flash settings
        flash_swf_url: 'lib/js_external/fileupload/plupload.flash.swf'
    });

    uploader = jQuery('#uploader').pluploadQueue();
    uploader.bind('UploadComplete', function () {
        jQuery('#win-add-file').window('close');
        jQuery('#owners-documents-tables').datagrid('loadRpc');
    });
}

function initDocumentDownload(document_id) {
    TF.Rpc.Owners.OwnersDocuments.downloadAttachment(document_id)
        .done(function (data) {
            jQuery('#btn-download-file').attr("href", data);
            jQuery('#win-download').window('open');
        })
        .fail(function (errorObj) {
            jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
        });
}

function deleteCurrentDocumentFile(document_id) {
    TF.Rpc.Owners.OwnersDocuments.deleteAttachment(document_id)
        .done(function () {
            jQuery('#owners-documents-tables').datagrid('loadRpc');
        })
        .fail(function (errorObj) {
            jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
        });
}

function createDocumentsAttachmentButtons(row) {
    var html = '',
        attachmentAvaliable = row.attachment ? true : false;
    html += '<a href="javascript:void(0);" onClick="initOwnerDocumentUpload('+row.id+')" class="easyui-linkbutton easyui-tooltip" data-options="iconCls: \'icon-add\', position: \'top\', content: \'Качване на прикачен файл\', plain: true, disabled: '+attachmentAvaliable+'"></a>&nbsp';
    html += '<a href="javascript:void(0);" onClick="initDocumentDownload('+row.id+')" class="easyui-linkbutton easyui-tooltip" data-options="iconCls: \'icon-export\', position: \'top\', content: \'Сваляне на прикачен файл\', plain: true, disabled: '+!attachmentAvaliable+'"></a>&nbsp';
    html += '<a href="javascript:void(0);" onClick="deleteCurrentDocumentFile('+row.id+')" class="easyui-linkbutton easyui-tooltip" data-options="iconCls: \'icon-delete\', position: \'top\', content: \'Изтриване на прикачен файл\', plain: true, disabled: '+!attachmentAvaliable+'"></a>';
    return html;
}
