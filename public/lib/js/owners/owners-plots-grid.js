function initOwnersPlotsGrid(owner_id) {

    var ownersPlotsTables = jQuery('#owners-plots-tables');
    var isDatagridBound = ownersPlotsTables.data().hasOwnProperty('datagrid');

    if (isDatagridBound) {
        if (owner_id !== 0) {
            ownersPlotsTables.datagrid({
                url: 'index.php?owners-rpc=owners-plots-grid',
                rpcParams: [{
                    owner_id: owner_id,
                    ekate: null,
                    farm: null,
                    year: null
                }],
            });
        } else {
            ownersPlotsTables.datagrid('loadData', { "total": 0, "rows": []});
            initOwnersDocumentsGrid();
        }
        return;
    }
    ownersPlotsTables.datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 10,
        fit: true,
        fitColumns: true,
        showFooter: true,
        sortName: 'owner',
        sortOrder: 'desc',
        idField: 'id',
        singleSelect: true,
        border: false,
        columns: [[
                {
                    field: 'land',
                    title: '<b>Землище</b>',
                    sortable: false,
                    width: 150
                }, {
                    field: 'kad_ident',
                    title: '<b>Имот</b>',
                    sortable: true,
                    width: 150
                }, {
                    field: 'ownage',
                    title: '<b>Площ по дог.(дка)</b>',
                    sortable: true,
                    width: 90
                }, {
                    field: 'percent',
                    title: '<b>Собственост(%)</b>',
                    sortable: false,
                    width: 70
                }, {
                    field: 'ntp',
                    title: '<b>НТП</b>',
                    sortable: true,
                    width: 100
                }, {
                    field: 'category',
                    title: '<b>Категория</b>',
                    sortable: true,
                    width: 80
                }, {
                	field: 'farm_name',
                    title: '<b>Стопанство</b>',
                    sortable: true,
                    width: 150
                }, {
                    field: 'c_num',
                    title: '<b>Договор</b>',
                    sortable: true,
                    width: 90
                }, {
                    field: 'active_text',
                    title: '<b>Статус</b>',
                    sortable: false,
                    width: 90
                }, {
                    field: 'c_date',
                    title: '<b>Сключен</b>',
                    sortable: true,
                    width: 120
                }, {
                    field: 'start_date',
                    title: '<b>Влиза в сила</b>',
                    sortable: true,
                    width: 120
                }, {
                    field: 'due_date',
                    title: '<b>Изтича</b>',
                    sortable: true,
                    width: 130
                }
            ]],
        pagination: true,
        rownumbers: true,
		toolbar: [{
			id: 'btnaddfilter',
			text: 'Филтриране',
			iconCls: 'icon-filter',
			handler: function() {
				jQuery('#win-owners-plots-filter').window('open');
			}
		}, {
			id: 'btninfoplot',
			text: 'Информация за имот',
			iconCls: 'icon-info',
			handler: function() {
				var getSelected = jQuery('#owners-plots-tables').datagrid('getSelected');

				if (getSelected) {
					window.open("index.php?page=Plots.Home&plot_id=" + getSelected.gid, '_blank');
				} else {
					jQuery.messager.alert('Грешка', 'Моля изберете имот, за който да бъде показана информация.');
				}
			}
		}, {
			id: 'btninfocontract',
			text: 'Информация за договор',
			iconCls: 'icon-info',
			handler: function() {
				var getSelected = jQuery('#owners-plots-tables').datagrid('getSelected');
				if (!getSelected) {
                    return jQuery.messager.alert('Грешка', 'Моля изберете запис, за който да бъде показана информация за договора.');
				}
				if(getSelected['is_annex']){
                    window.open("index.php?page=Contracts.Home&contract_id=" + getSelected.contract_id, '_blank');
                }else{
                    window.open("index.php?page=Contracts.Home&contract_id=" + getSelected.contract_id, '_blank');
                }
            }
		}],
        onClickRow: function() {
           initOwnersDocumentsGrid();
        },
        onLoadSuccess: function(data) {
            if(data.rows.length === 0) {
                jQuery('#owners-plots-tabs').tabs('disableTab', 0);
                for(btn of ownersPlotsTables.datagrid('options').toolbar){
                    jQuery('#'+btn.id).linkbutton({disabled:true});
                }
            }else {
                jQuery('#owners-plots-tabs').tabs('enableTab', 0);
                for(btn of ownersPlotsTables.datagrid('options').toolbar){
                    jQuery('#'+btn.id).linkbutton({disabled:false});
                }
            }
            jQuery('#owners-plots-tables').datagrid('unselectAll');
            jQuery('#owners-plots-tables').datagrid('uncheckAll');
            if(jQuery('#owners-plots-tabs').tabs('getTabIndex', jQuery('#owners-plots-tabs').tabs('getSelected')) === 0)
            {
                initOwnersDocumentsGrid();
            }
        },
        rowStyler: function(index, row){
            if (!row.operational && row.active){
                return 'color:#aaa;';
            } else if (!row.active) {
                return 'color:#aaa; text-decoration: line-through';
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
    });
}
