Namespace('TF.Rpc.Owners');
var ownerID;

function initOwnersTree(pageNumber, filterObj) {
	var page_number = 1;

	if (pageNumber != undefined)
		page_number = pageNumber;

	var ownersTree = jQuery('#owners-tree');
	var isTreeBound = ownersTree.data().hasOwnProperty('tree');

	if (isTreeBound) {
		ownersTree.tree({
			rpcParams: [filterObj],
			page: page_number
		});
		return;
	}

	ownersTree.tree({
		url: 'index.php?owners-rpc=owners-tree',
		animate: true,
		lines: true,
		page: page_number,
		sort: 'id',
		order: 'desc',
		rpcParams: [filterObj],
		onBeforeLoad: function(node, param) {

			if (filterObj) {
				if(filterObj.owner_ids) {
					if(!jQuery.isArray(filterObj.owner_ids)){
						filterObj.owner_ids = filterObj.owner_ids.split(",");
					}
				}
			}
		},
		onSelect: function(node) {
			if (!node) {
				return false;
			}
			initOwnersPaymentsGrid(node.id);
			initOwnersInfo(node.attributes);
			canRentBePaid(node.attributes);


			jQuery('#search-year').combobox('reload');
			jQuery('#search-plot-ekate').combobox('reset');
			jQuery('#search-plot-farm').combobox('reset');

			if (node.attributes.owner_type === 0) {
				jQuery('#content-layout').layout('remove', 'west');
			}
			else {
				var content = '<div class="easyui-panel" data-options="title: \'Наследници\', iconCls: \'icon-owners\', fit: true">' +
                                    '<div style="background:#fafafa;">' +
                                        '<a href="javascript:void(0)" onClick="addOwnerHeritor();" class="easyui-linkbutton" data-options="iconCls: \'icon-add\', plain: true">Добавяне</a>' +
                                        '<a href="javascript:void(0)" onClick="deleteOwnerHeritor();" class="easyui-linkbutton" data-options="iconCls: \'icon-delete\', plain: true">Премахване</a>' +
                                        '<a href="javascript:void(0)" onClick="showOwnerHeritorInfo();" class="easyui-linkbutton" data-options="iconCls: \'icon-info\', plain: true">Информация</a>' +
                                        '<hr style="margin: 0px; border: none; height: 1px; background: #ddd;">' +
                                    '</div>' +
                                    '<div id="owners-heritors-tree"></div>' +
                                '</div>';

				var options = {
					region: 'west',
					width: 315,
					border: false,
					content: content,
					style: {padding:'0px 5px 5px 0px'}
				};

				jQuery('#content-layout').layout('add', options);
				initOwnersHeritorsTree(node.id);
			}

            initOwnersPlotsGrid(node.id);
			initOwnersInheritedPlotstGrid(node.id);
			initOwnersRepresentedGrid(node.id);
			initOwnersFilesGrid();
		},
		onLoadSuccess: function() {
			var roots = jQuery('#owners-tree').tree('getRoots');
			var total = 0;
			var limit = 30;
			if (roots.length) {
				if (ownerID !== undefined) {
					var node = jQuery('#owners-tree').tree('find', ownerID);
					jQuery('#owners-tree').tree('select', node.target);
				}
				else {
					jQuery('#owners-tree').tree('select', roots[0].target);
				}
				total = roots[0].attributes.pagination.total;
				limit = roots[0].attributes.pagination.limit;
			}
            else
            {
                initOwnersPlotsGrid(0);
                initOwnersInheritedPlotstGrid(0);
                initOwnersPaymentsGrid(0);
                initOwnersInfo();

                jQuery('#content-layout').layout('remove', 'west');
                jQuery.messager.alert('Внимание', 'Не са открити записи', 'warning');
            }

			//init pagination with total contract elements
			initOwnersPagination(total, limit);
		},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

jQuery(function() {
    tinymce.init({
        selector: '#remark_input',
        height : 10,
        theme: "modern",
        language: 'bg_BG',
        invalid_elements: "script",
        relative_urls: false,
        statusbar: false,
        toolbar: "forecolor",
        menubar: false,
        style_formats_merge: true,
        forced_root_block: false,
        plugins: "textcolor colorpicker"
    }).then(function(editor) {
        jQuery(editor[0].editorContainer).width(185)
		.css('margin','5px 5px 5px 0px')
		.css('padding-right','1px')
		.css('padding-bottom','1px');
        jQuery('#remark_input_ifr').height(70);
    });
    jQuery('#add-owner').bind('click', function() {
		if (!hasPlotRightsRW) {
			messagerPlotsWriteRights();
			return;
		}
		ownerID = undefined;
		lastAddedOwner = 0;
		lastAddedParent = 0;
		jQuery('#win-plot-owner-add').window('open');
		setAddEditFieldsValidators();
		clearAddOwnerInputDataFields();
		displayTablesOnCallbackComplete();
		onAddownerPanelOpen();
		onCountriesPanelOpen();
		displayTables('physical');
		jQuery('#is_physical > input').prop('checked', true);
	});

	jQuery('#delete-owner').bind('click', function() {
		if (!hasPlotRightsRW) {
			messagerPlotsWriteRights();
			return;
		}
		var getSelected = jQuery('#owners-tree').tree('getSelected');
		if (getSelected) {
			jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да изтриете този собственик?', function(r) {
				if (r) {
					ownerID = undefined;
					TF.Rpc.Owners.OwnersTree.deleteOwner(getSelected.id)
					.done(function (data) {
						jQuery('#owners-tree').tree('reload');
					})
					.fail(function (errorObj) {
						if (errorObj.is(TF.Rpc.ExceptionsList.USER_HAS_CONTRACTS)) {
							jQuery('#win-delete-owner-contracts-error').window('open');  
							initUnsuccessfullyDeleteOwner(
								errorObj.getOriginalMessage()
							);
							return false;               
						} else {
							RpcErrorHandler.show(errorObj);  
						}
					});
				}
			});
		} else {
			jQuery.messager.alert('Грешка', 'Моля изберете собственик.');
		}
	});

	jQuery('#edit-owner').bind('click', function() {
		if (!hasPlotRightsRW) {
			messagerPlotsWriteRights();
			return;
		}

		lastAddedOwner = 0;
		lastAddedParent = 0;
		var getSelected = jQuery('#owners-tree').tree('getSelected');
		if (getSelected) {
			isEditing = true;
			ownerID = getSelected.id;
			ownerType = getSelected.attributes.owner_type;
			TF.Rpc.Owners.OwnersTree.markForEdit(ownerID)
			.done(function (data) {
				jQuery('#win-plot-owner-add').window('open');
				displayTablesOnCallbackComplete();
				onAddownerPanelOpen();
				onCountriesPanelOpen();
				setOwnerFieldsDataForEdit(data);
				setAddEditFieldsValidators();
			})
			.fail(function (data) {
				jQuery.messager.alert('Грешка', 'Възникна грешка при избор на собственик.');
			});
		} else {
			jQuery.messager.alert('Грешка', 'Моля изберете собственик.');
		}
	});

	jQuery('#filter-owner').bind('click', function() {
		jQuery('#win-owners-filter').window('open');
	});

	jQuery('#cancel-filter-owner').bind('click', function() {
		jQuery('#search-owner').val('');
		jQuery('#search-egn').val('');
		jQuery('#search-company').val('');
		jQuery('#search-eik').val('');
		jQuery('#search-mol').val('');
		jQuery('#search-rep-name').val('');
		jQuery('#search-rep-egn').val('');
		jQuery('#search-kad-ident').val('');
		jQuery('#search-masiv').val('');
		jQuery('#search-imot').val('');
		jQuery('#search-c-num').val('');
		jQuery('#search-ekate').combobox('reset');
		jQuery('#search-c-type').combobox('reset');
		jQuery('#search-owner-note').val('');
		jQuery('#search-owner-phone').val('');

		initOwnersTree();
		jQuery('#owners-tree-pagination').pagination('select', 1);
	});

	jQuery('#owner-parents').bind('click', function() {
		var getSelected = jQuery('#owners-tree').tree('getSelected');
		if (getSelected) {

			TF.Rpc.Owners.OwnersTree.getOwnerParents(getSelected.id)
			.done(function (data) {
				initOwnersParentsTabs(data);
			})
			.fail(function (errorObj) {
				if (errorObj.is(TF.Rpc.ExceptionsList.OWNER_IS_NOT_HERITOR)) {
					jQuery.messager.alert('Съобщение',TF.Rpc.ExceptionsList.OWNER_IS_NOT_HERITOR.message, 'info');
				};
			});

		} else {
			jQuery.messager.alert('Грешка', 'Моля изберете собственик.');
		}
	});
});

function initOwnersPagination(total, limit) {
	jQuery('#owners-tree-pagination').pagination({
		showPageList: false,
		showRefresh: false,
		displayMsg: '',
		total: total,
		pageSize: limit,
		onSelectPage: function(pageNumber, pageSize) {
			var obj = new Object();
			obj.owner_names = jQuery('#search-owner').val();
			obj.egn = jQuery('#search-egn').val();
			obj.company_name = jQuery('#search-company').val();
			obj.eik = jQuery('#search-eik').val();
			obj.mol = jQuery('#search-mol').val();
			obj.rep_name = jQuery('#search-rep-name').val();
			obj.rep_egn = jQuery('#search-rep-egn').val();
			obj.kad_ident = jQuery('#search-kad-ident').val();
			obj.masiv = jQuery('#search-masiv').val();
			obj.imot = jQuery('#search-imot').val();
			obj.c_num = jQuery('#search-c-num').val();
			obj.ekate = jQuery('#search-ekate').combobox('getValues');
			obj.c_type = jQuery('#search-c-type').combobox('getValues');
			obj.owner_note = jQuery('#search-owner-note').val();
			obj.owner_phone = jQuery('#search-owner-phone').val();

			initOwnersTree(pageNumber, obj);
		}
	});
}

function ownersFilter() {
	var obj = new Object();
	obj.owner_names = jQuery('#search-owner').val();
	obj.egn = jQuery('#search-egn').val();
	obj.company_name = jQuery('#search-company').val();
	obj.eik = jQuery('#search-eik').val();
	obj.mol = jQuery('#search-mol').val();
	obj.rep_name = jQuery('#search-rep-name').val();
	obj.rep_egn = jQuery('#search-rep-egn').val();
	obj.kad_ident = jQuery('#search-kad-ident').val();
	obj.masiv = jQuery('#search-masiv').val();
	obj.imot = jQuery('#search-imot').val();
	obj.c_num = jQuery('#search-c-num').val();
	obj.ekate = jQuery('#search-ekate').combobox('getValues');
	obj.c_type = jQuery('#search-c-type').combobox('getValues');
	
	obj.owner_note = jQuery('#search-owner-note').val();
	if(obj.owner_note.length > 0 && obj.owner_note.length < 3) {
		jQuery.messager.alert('Грешка', 'Моля въведете поне 3 символа за търсене в полето "Забележка"', 'warning');
		return;
	}
	
	obj.owner_phone = jQuery('#search-owner-phone').val();
	if(obj.owner_phone.length > 0 && obj.owner_phone.length < 5) {
		jQuery.messager.alert('Грешка', 'Моля въведете поне 5 символа за търсене в полето "Телефон/Мобилен"', 'warning');
		return;
	}

	initOwnersTree(1, obj);
	
	jQuery('#win-owners-filter').window('close');
}

function initOwnersParentsTabs(result) {

	for(var i=0; i<result.length; i++) {
		jQuery('#parents-tabs').tabs('add',{
			content: '<div id="owners-parents-tree-' + result[i] + '" style="padding: 3px 0px;"></div>',
			iconCls:'icon-tree'
		});
	}

	initOwnersParentsTree(result);

	jQuery('#win-parents').window('open');
}

function initOwnersParentsTree(roots) {
	for(var i=0; i<roots.length; i++) {
		var tmpPath = roots[i];
		var getSelected = jQuery('#owners-tree').tree('getSelected');

		jQuery('#owners-parents-tree-' + roots[i]).tree({
			url: 'index.php?owners-rpc=owners-parents-tree',
			animate: true,
			lines: true,
			rpcParams: [{
				root_id: tmpPath
			}],
			onBeforeLoad: function(node, param) {

			},
			onBeforeExpand: function(node) {
				tmpPath = node['attributes'].path;
			},
			onLoadSuccess: function() {
				var target = jQuery(this).tree('find', getSelected.id).target;
				jQuery(this).tree('select', target);
			},
			loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
			loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
		});
	}
}

function closeAllTabs() {
	var tabs = jQuery('#parents-tabs').tabs('tabs');

	for(var i=tabs.length; i>0; i--) {
		jQuery('#parents-tabs').tabs('close', 0);
	}
}

function onAddownerPanelOpen() {
	jQuery('#rent-place > input').combobox({
		data: ComboboxData.EkateCombobox,
    	valueField: 'ekate',
    	textField: 'text',
		filter: function(q, row){
			var opts = jQuery(this).combobox('options');
			var text = row[opts.textField].toLowerCase();
			var value = row[opts.valueField];
			var find = q.toLowerCase();
			if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
			{
				return true;
			}
		},
		onLoadSuccess: function (data) {
		},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function onCountriesPanelOpen() {
	jQuery('#country > input').combobox({
		data: ComboboxData.CountriesCombobox,
    	valueField: 'iso_alpha_2_code',
    	textField: 'bg_name',
		filter: function(q, row){
			var opts = jQuery(this).combobox('options');
			var text = row[opts.textField].toLowerCase();
			var value = row[opts.valueField];
			var find = q.toLowerCase();
			if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
			{
				return true;
			}
		},
		onLoadSuccess: function (data) {
		},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function setOwnerFieldsDataForEdit(data) {
	jQuery('#name > input').val(data['name']);
    jQuery('#surname > input').val(data['surname']);
    jQuery('#lastname > input').val(data['lastname']);
	new CardIdValidateBox('#lk_nomer > input').setValue(data['lk_nomer']);
    jQuery('#lk_izdavane > input').val(data['lk_izdavane']);
    jQuery('#company_name > input').val(data['company_name']);
    jQuery('#eik > input').numberbox('setValue', data['eik']);
    jQuery('#mol > input').val(data['mol']);
    jQuery('#company_address > textarea').val(data['company_address']);
    jQuery('#phone > input').val(data['phone']);
    jQuery('#fax > input').val(data['fax']);
    jQuery('#mobile > input').val(data['mobile']);
    jQuery('#email > input').val(data['email']);
    jQuery('#iban > input').val(data['iban']);
    jQuery('#bank_name > input').val(data['bank_name']);
    jQuery('#bic > input').val(data['bic']);
    jQuery('#address > textarea').val(data['address']);
    jQuery('#remark > textarea').val(data['remark']);
	new EgnValidateBox('#egn > input').setValue(data['egn']);
	jQuery('#is-dead > input').prop('checked', data['is_dead']);
	jQuery('#is_foreigner').prop('checked', data['is_foreigner']);
	jQuery('#dead-date > input').datebox({value: data.dead_date});
	jQuery('#country > input').combobox({value: data.country});
	jQuery('#birthday > input').datebox({value:  data.birthday});

    if(data['remark']!= null) {
        tinyMCE.get('remark_input').setContent(data['remark']);
    } else {
        tinyMCE.get('remark_input').setContent('');
    }

    if (data['owner_type'] == 0) {
    	jQuery('#is_legal > input').prop('checked', true);
	    jQuery('#is_physical > input').prop('checked', false);
    }
    else {
		jQuery('#is_legal > input').prop('checked', false);
	    jQuery('#is_physical > input').prop('checked', true);
    }

    if(data['is_dead']) {
    	jQuery('#prepiska > input').val(data['prepiska']);
    }else {
    	jQuery('#prepiska > input').val('');
    }
    jQuery('#rent-place > input').combobox('setValue', data['rent_place']);

	const postPayment = new PostPaymentFields('owner');
	postPayment.fillPostPaymentInputs(data['post_payment_fields']);
}


/**
 * 
 * @param {*} data 
 */
function initUnsuccessfullyDeleteOwner(data) {
    var values = Object.keys(data).map(e => data[e])
    jQuery('#unsuccessfully-delete-owners-data-tables').datagrid({    
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		border: false,
        fit: true,
		singleSelect: true,
		fitColumns: true,
		showFooter: true,
		data: values,
		sortName: 'owner_id',
		sortOrder: 'desc',
		idField: 'owner_id',
		columns: [[
                {
                    field: 'c_num',
                    title: '<b>Договор</b>',
                    width: 100,
                    formatter: function(value,row,index){   
						if (row.contract_id > 0) {
                            return '<a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="openContractInNewTab('+row.contract_id+','+ row.is_sublease+')">'+value+'</a>';                        
                        }
					}
                },
				{
					field: 'contract_type',
					title: '<b>Тип на договора</b>',
					width: 100
				},
                {
					field: 'start_date',
					title: '<b>Начална дата</b>',
					width: 100
				},
                {
					field: 'due_date',
					title: '<b>Крайна дата</b>',
					width: 100
				},
			]],
		pagination: true,
		rownumbers: true,
		rowStyler:function(index,row){
			return 'background-color:#ff4c4c;';
			
		},
		onLoadSuccess: function (data) {
	
		},
		onClickRow:function(index){
			jQuery(this).datagrid('unselectRow',index);
		},
	});
}

/**
 * 
 * @param {*} contractId 
 * @param {*} isAnnex 
 */
function openContractInNewTab(contractId, isSublease) {
	if(isSublease === true) {
		window.open("index.php?page=Subleases.Home&sublease_id=" + contractId, '_blank');
	} else {
		window.open("index.php?page=Contracts.Home&contract_id=" + contractId, '_blank');
	}
}