function initOwnersFilesGrid() {
    var selectedOwner = jQuery('#owners-tree').tree('getSelected');

    owner_id = selectedOwner.id;

    jQuery('#owners-files-tables').datagrid({
        rownumbers: true,
        singleSelect: true,
        onSelectCheck: true,
        onCheckSelect: true,
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        fit: true,
        fitColumns: true,
        showFooter: true,
        pagination: false,
        height: 250,
        idField: 'id',
        sortName: 'id',
        sortOrder: 'desc',
        url: 'index.php?owners-rpc=owners-files',
        rpcParams:[{
            owner_id: owner_id
        }],
        columns:[[
            {
                field:'attachment',
                title:'Прикачен файл',
                sortable:false,
                width: 200,
                formatter: function (index, row) {
                    if (row.id && row.attachment) {
                        return row.attachment.length > 50 ? row.attachment.substring(0,50) + '...' : row.attachment;
                    }
                }
            },{
                field:'date',
                title:'Дата',
                sortable:true,
                width: 150,
                editor: {
                    type: 'datebox',
                    options: {
                        editable: true,
                        required: false
                    }
                }
            }, {
                field:'note',
                title:'Бележка',
                width: 300,
                editor: {
                    type: 'text',
                    options: {
                        editable: true,
                        required: false,
                    }
                }
            }, {
                field:'attachment_actions',
                sortable:false,
                width: 150,
                align: 'center',
                formatter: function (index, row) {
                    return createAttachmentButtons(row)
                }
            }
        ]],
        toolbar: [
            {
                id: 'btnaddfile',
                text: 'Добавяне',
                iconCls: 'icon-add',
                handler: function() {
                    if (!hasPlotRightsRW) {
                        messagerPlotsWriteRights();
                        return;
                    }
                    var getSelected = jQuery('#owners-tree').tree('getSelected');

                    if (getSelected) {
                        if (fileEditingClosed()) {
                            fileEditIndex = 0;
                            jQuery('#owners-files-tables').datagrid('insertRow',
                                {
                                    index: fileEditIndex,
                                    row: {
                                        id: false,
                                        owner_id: owner_id
                                    }
                                });
                            jQuery('#owners-files-tables').datagrid('beginEdit', fileEditIndex).datagrid('selectRow', fileEditIndex);
                        }
                    } else
                    {
                        jQuery.messager.alert('Грешка', 'Моля изберете собственик.');
                    }
                }
            }, {
                id: 'btneditfile',
                text: 'Редактиране',
                iconCls: 'icon-edit',
                handler: function() {
                    if (!hasPlotRightsRW) {
                        messagerPlotsWriteRights();
                        return;
                    }
                    var getChecked = jQuery('#owners-files-tables').datagrid('getChecked');

                    if (getChecked[0]) {
                        var index = jQuery('#owners-files-tables').datagrid('getRowIndex', getChecked[0]);

                        if (fileEditIndex != index) {
                            if (fileEditingClosed()) {
                                jQuery('#owners-files-tables').datagrid('beginEdit', index);
                                fileEditIndex = index;
                            } else {
                                jQuery('#owners-files-tables').datagrid('selectRow', fileEditIndex);
                            }
                        }
                    } else
                    {
                        jQuery.messager.alert('Грешка', 'Моля изберете документ.');
                    }
                }
            }, {
                id: 'btndelfile',
                text: 'Изтриване',
                iconCls: 'icon-remove',
                handler: function() {
                    if (!hasPlotRightsRW) {
                        messagerPlotsWriteRights();
                        return;
                    }
                    var getChecked = jQuery('#owners-files-tables').datagrid('getChecked');

                    if (getChecked[0]) {
                        if(getChecked[0].id){
                            jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да изтриете този документ?', function(r) {
                                if (r) {

                                    TF.Rpc.Owners.OwnersFiles.deleteOwnerFile(getChecked[0].id).done(function (dataObj) {
                                    })
                                        .fail(function (errorObj) {
                                        });

                                    jQuery('#owners-files-tables').datagrid('reload');
                                }
                            });
                            fileEditIndex = undefined;
                        }
                    } else
                    {
                        jQuery.messager.alert('Грешка', 'Моля изберете документ.');
                    }
                }
            }, {
                id: 'btnsavefile',
                text: 'Запази',
                iconCls: 'icon-save',
                handler: function() {
                    if (!hasPlotRightsRW) {
                        messagerPlotsWriteRights();
                        return;
                    }
                    fileEditingClosed();
                }
            }, {
                id: 'btncancelfile',
                text: 'Отмени',
                iconCls: 'icon-undo',
                handler: function() {
                    if (!hasPlotRightsRW) {
                        messagerPlotsWriteRights();
                        return;
                    }
                    rejectFilesChanges();
                }
            }
        ],
        onCheck: function(index, rowData) {
            if (fileEditIndex != index) {
                fileEditingClosed();
            }
        },
        onBeforeLoad: function() {
            jQuery('#owners-files-tables').datagrid('uncheckAll');
            jQuery('#owners-files-tables').datagrid('unselectAll');

            fileEditIndex = undefined;
        },
        onLoadSuccess: function() {
            jQuery(this).datagrid('getPanel').find('a.easyui-linkbutton').linkbutton();
            jQuery(this).datagrid('getPanel').find('a.easyui-tooltip').tooltip();
            jQuery(this).datagrid('fixRowHeight');
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function fileEditingClosed() {
    if (fileEditIndex === undefined) {
        return true;
    }
    
    if (jQuery('#owners-files-tables').datagrid('validateRow', fileEditIndex))
    {
        jQuery('#owners-files-tables').datagrid('endEdit', fileEditIndex);
        jQuery('#owners-files-tables').datagrid('acceptChanges');

        var rows = jQuery('#owners-files-tables').datagrid('getRows');
        var obj = rows[fileEditIndex];

        TF.Rpc.Owners.OwnersFiles.saveOwnerFile(obj).done(function (dataObj) {
        })
        .fail(function (errorObj) {
        });

        fileEditIndex = undefined;

        jQuery('#owners-files-tables').datagrid('reload');
        jQuery('#owners-documents-tables').datagrid('reload');

        return true;
    } else {
        return false;
    }
}

function rejectFilesChanges() {
    jQuery('#owners-files-tables').datagrid('rejectChanges');
    jQuery('#owners-files-tables').datagrid('uncheckAll');
    jQuery('#owners-files-tables').datagrid('unselectAll');

    fileEditIndex = undefined;
}

function currentDate(){
    var date = new Date();
    var y = date.getFullYear();
    var m = date.getMonth()+1;
    var d = date.getDate();
    return y+'-'+m+'-'+d;
}


function initOwnerFileUpload(document_id) {
    jQuery('#win-add-file').window('open');
    const url  = "index.php?json=owners-upload"; 
    var uploader;

    jQuery("#uploader").pluploadQueue({
        // General settings
        runtimes: 'gears,html5,flash,silverlight,browserplus',
        url: url,
        max_file_size: '100pmb',
        unique_names: true,
        multipart_params : {
            "document_id" : document_id,
            "is_owner_file" : 1
        },
        // Flash settings
        flash_swf_url: 'lib/js_external/fileupload/plupload.flash.swf',
    });

    uploader = jQuery('#uploader').pluploadQueue();
    uploader.bind('UploadComplete', function () {
        jQuery('#win-add-file').window('close');
        jQuery('#owners-documents-tables').datagrid('reload');
        jQuery('#owners-files-tables').datagrid('reload');
    });
}

function initFileDownload(document_id) {
    TF.Rpc.Owners.OwnersFiles.downloadAttachment(document_id)
        .done(function (data) {
            jQuery('#btn-download-file').attr("href", data);
            jQuery('#win-download').window('open');
        })
        .fail(function (errorObj) {
            jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
        });
}

function deleteCurrentFile(document_id) {
    TF.Rpc.Owners.OwnersFiles.deleteAttachment(document_id)
        .done(function () {
            jQuery('#owners-files-tables').datagrid('reload');
            jQuery('#owners-documents-tables').datagrid('reload');
        })
        .fail(function (errorObj) {
            jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
        });
}

function createAttachmentButtons(row) {
    var html = '',
        attachmentAvaliable = row.attachment ? true : false;
    html += '<a href="javascript:void(0);" onClick="initOwnerFileUpload('+row.id+')" class="easyui-linkbutton easyui-tooltip" data-options="iconCls: \'icon-add\', position: \'top\', content: \'Качване на прикачен файл\', plain: true, disabled: '+attachmentAvaliable+'"></a>&nbsp';
    html += '<a href="javascript:void(0);" onClick="initFileDownload('+row.id+')" class="easyui-linkbutton easyui-tooltip" data-options="iconCls: \'icon-export\', position: \'top\', content: \'Сваляне на прикачен файл\', plain: true, disabled: '+!attachmentAvaliable+'"></a>&nbsp';
    html += '<a href="javascript:void(0);" onClick="deleteCurrentFile('+row.id+')" class="easyui-linkbutton easyui-tooltip" data-options="iconCls: \'icon-delete\', position: \'top\', content: \'Изтриване на прикачен файл\', plain: true, disabled: '+!attachmentAvaliable+'"></a>';
    return html;
}
