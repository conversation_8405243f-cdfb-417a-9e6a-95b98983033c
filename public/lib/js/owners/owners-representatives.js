var isEditingRep = false;

jQuery(function () {
    var addRepresentativeCallback = null;

    jQuery("#add-representative").bind('click', function () {
        isEditingRep = false;
        jQuery('#win-add-edit-representative').window('open');
        clearRepresentativeAddEditData();
        addRepresentativeCallback = jQuery("#add-representative").data( "callback") ? jQuery("#add-representative").data( "callback") : null;
    });
    jQuery("#delete-representative").bind('click', function () {
        var getSelected = jQuery('#representatives-tree').tree('getSelected').id;
        if (!getSelected) {
            jQuery.messager.alert('Грешка', 'Моля изберете представител, който да бъде изтрит.', 'warning');
            return;
        }
        var confirmationMsg = 'Сигурни ли сте, че искате да изтриете този представител?';
        confirmationMsg += ' След изтриването му, всички собственици, които е представлявал, ще се представляват лично.';
        confirmationMsg += ' Ако този представител е представлявал юридически лица, е необходимо да се избере нов представител на негово място.';
        jQuery.messager.confirm('Потвърждение', confirmationMsg, function(r) {
            if (r) {
                TF.Rpc.Owners.RepresentativesTree.deleteRepresentative(getSelected)
                    .done(function () {
                        jQuery('#representatives-tree').tree('reload');
                    })
                    .fail(function (data) {
                        if (data.getCode() == -33306) {
                            var msg = 'Не може да изтриете избраният представител, защото представлява юридически лица.';
                            msg += ' Необходимо е първо да изберете нов представител за всички юридически лица.';
                            jQuery.messager.alert('Грешка', msg, 'warning');
                        } else {
                            RpcErrorHandler.show(data);
                        }
                    });
            }
        });
    });
    jQuery("#edit-representative").bind('click', function () {

        initAddEditRepresentativeFields();
        jQuery('#win-add-edit-representative').window('open');
        isEditingRep = true;
        var rep_id = jQuery('#representatives-tree').tree('getSelected').id;

        TF.Rpc.Owners.RepresentativesTree.markForEdit(rep_id)
            .done(function (data) {
                setEditRepresentativeFields(data);
                initAddEditRepresentativeFields();
            })
            .fail(function (errorObj) {
                jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
            });
    });

    jQuery("#filter-representative").bind('click', function () {
        jQuery('#win-representatives-filter').window('open');
    });

    jQuery("#cancel-filter-representative").bind('click', function () {
        clearRepresentativesFilter();
    });
    jQuery('#btn-save-representative').bind('click', function () {
        var repData = getRepresentativeAddEditData();

        if (
            jQuery('#rep-ae-name > input').validatebox('isValid') &&
            jQuery('#rep-ae-surname > input').validatebox('isValid') &&
            jQuery('#rep-ae-lastname > input').validatebox('isValid')
        ) {
            if (! (new EgnValidateBox('#rep-ae-egn > input')).isValid()) {
                jQuery.messager.alert('Грешка', 'Полето за ЕГН е задължително и не трябва да превишава 10 цифри.');
                return false;
            }
        } else {
            jQuery.messager.alert('Грешка', 'Моля попълнете задължителните полета.');
            return false;
        }

        if (isEditingRep) {
            var rep_id = jQuery('#representatives-tree').tree('getSelected').id;
            TF.Rpc.Owners.RepresentativesTree.editRepresentative(repData, rep_id)
                .done(function () {
                    jQuery('#representatives-tree').tree('reload');
                    jQuery('#win-add-edit-representative').window('close');
                    isEditingRep = false;
                })
                .fail(function (errorObj) {
                    jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
                });
        } else {
            TF.Rpc.Owners.RepresentativesTree.addRepresentative(repData)
                .done(function (data) {
                    jQuery('#representatives-tree').tree('reload');
                    jQuery('#win-add-edit-representative').window('close');
                    isEditingRep = false;
                    let fn = window[addRepresentativeCallback];
                    if(typeof fn === 'function') {
                        repData.id = data;
                        fn(repData);
                    }
                })
                .fail(function (errorObj) {
                    jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
                });
        }

        clearRepresentativeAddEditData();
    });


});

function initOwnerRepresentativesTree(pageNumber, filterObj) {

	var page_number = 1;

	if (pageNumber != undefined)
		page_number = pageNumber;

	var representativesTree = jQuery('#representatives-tree');
	var isTreeBound = representativesTree.data().hasOwnProperty('tree');

	if (isTreeBound) {
		representativesTree.tree({
			rpcParams: [filterObj],
			page: page_number
		});
		return;
	}

	representativesTree.tree({
		url: 'index.php?owners-rpc=representatives-tree',
		animate: true,
		lines: true,
		page: page_number,
		sort: 'id',
		order: 'desc',
		rpcParams: [filterObj],
		onSelect: function(node) {
			if (!node) {
                initRepresentativeInfoPanel(0);
				return false;
			}

            initRepresentativePlotGrid(node.id);
            initRepresentativeInfoPanel(node);
		},
		onLoadSuccess: function() {
			var roots = jQuery('#representatives-tree').tree('getRoots');
			var total = 0;
			var limit = 20;
			//endLoading();
			if (roots.length) {
				jQuery('#representatives-tree').tree('select', roots[0].target);
				total = roots[0].attributes.pagination.total;
				limit = roots[0].attributes.pagination.limit;
			}
			else
			{
                initRepresentativePlotGrid(0);
			}

			//init pagination with total contract elements
			initRepresentativesPagination(total, limit);
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}


function initRepresentativesPagination(total, limit) {
	jQuery('#representatives-tree-pagination').pagination({
		showPageList: false,
		showRefresh: false,
		displayMsg: '',
		total: total,
		pageSize: limit,
		onSelectPage: function(pageNumber, pageSize) {
			var obj = new Object();
			obj.owner_names = jQuery('#search-owner').val();
			obj.egn = jQuery('#search-egn').val();
			obj.company_name = jQuery('#search-company').val();
			obj.eik = jQuery('#search-eik').val();

			initOwnerRepresentativesTree(pageNumber, obj);
		}
	});
}

function initRepresentativeInfoPanel(repInfo) {
    if (!repInfo) {
        jQuery('#rep-info-rep-name').html('-');
        jQuery('#rep-info-rep-surname').html('-');
        jQuery('#rep-info-rep-lastname').html('-');
        jQuery('#rep-info-rep-egn').html('-');
        jQuery('#rep-info-rep-lk').html('-');
        jQuery('#rep-info-rep-lk-izdavane').html('-');
        jQuery('#rep-info-rep-address').html('-');
        jQuery('#rep-info-rep-phone').html('-');
        jQuery('#rep-info-rep-rent-place').html('-');
        jQuery('#rep-info-rep-iban').html('-');
    } else {
        jQuery('#rep-info-rep-name').html(repInfo.attributes.rep_name);
        jQuery('#rep-info-rep-surname').html(repInfo.attributes.rep_surname);
        jQuery('#rep-info-rep-lastname').html(repInfo.attributes.rep_lastname);
        jQuery('#rep-info-rep-egn').html(repInfo.attributes.egn);
        jQuery('#rep-info-rep-lk').html(repInfo.attributes.lk_nomer);
        jQuery('#rep-info-rep-lk-izdavane').html(repInfo.attributes.lk_izdavane);
        jQuery('#rep-info-rep-address').html(repInfo.attributes.address);
        jQuery('#rep-info-rep-phone').html(repInfo.attributes.rep_phone);
        jQuery('#rep-info-rep-rent-place').html(repInfo.attributes.land);
        jQuery('#rep-info-rep-iban').html(repInfo.attributes.iban);
    }
}

function initRepresentativePlotGrid(id) {

    jQuery('#representatives-plots-grid').datagrid({
        url: 'index.php?owners-rpc=representatives-plots-grid',
        rpcParams: [{
            rep_id: id,
            ekate: null,
            farm: null,
            year: null
        }],
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		pageSize: 10,
		fit: true,
		fitColumns: true,
		showFooter: true,
		sortName: 'kad_ident',
		sortOrder: 'asc',
		idField: 'id',
		singleSelect: true,
		border: false,
		columns: [[
			{
				field: 'land',
				title: '<b>Землище</b>',
				sortable: false,
				width: 150
			}, {
				field: 'kad_ident',
				title: '<b>Имот</b>',
				sortable: true,
				width: 150
			}, {
				field: 'owner',
				title: '<b>Собственик</b>',
				sortable: true,
				width: 150
			}, {
				field: 'owner_type',
				title: '<b>Тип</b>',
				sortable: true,
				width: 50
			}, {
				field: 'farm_name',
				title: '<b>Стопанство</b>',
				sortable: true,
				width: 150
			}, {
				field: 'c_num',
				title: '<b>Договор</b>',
				sortable: true,
				width: 90
			}, {
				field: 'nm_usage_rights',
				title: '<b>Тип дог.</b>',
				sortable: true,
				width: 90
			}, {
				field: 'active_text',
				title: '<b>Статус</b>',
				sortable: false,
				width: 90
			}, {
				field: 'c_date',
				title: '<b>Сключен</b>',
				sortable: true,
				width: 120
			}, {
				field: 'start_date',
				title: '<b>Влиза в сила</b>',
				sortable: true,
				width: 120
			}, {
				field: 'due_date',
				title: '<b>Изтича</b>',
				sortable: true,
				width: 130
			}
		]],
		pagination: true,
		rownumbers: true,
		toolbar: [{
			id: 'btninfoowner',
			text: 'Информация за собственик',
			iconCls: 'icon-info',
			handler: function() {
				var getSelected = jQuery('#representatives-plots-grid').datagrid('getSelected');

				if (getSelected) {
                    initOwnersTree(1, {owner_id: getSelected.o_id});
                    jQuery('#win-representatives-tree').window('close');
				} else {
					jQuery.messager.alert('Грешка', 'Моля изберете имот, за който да бъде показана информация.');
				}
			}
		}, {
			id: 'btninfoplot',
			text: 'Информация за имот',
			iconCls: 'icon-info',
			handler: function() {
				var getSelected = jQuery('#representatives-plots-grid').datagrid('getSelected');

				if (getSelected) {
					window.open("index.php?page=Plots.Home&plot_id=" + getSelected.gid, '_blank');
				} else {
					jQuery.messager.alert('Грешка', 'Моля изберете имот, за който да бъде показана информация.');
				}
			}
		}, {
			id: 'btninfocontract',
			text: 'Информация за договор',
			iconCls: 'icon-info',
			handler: function() {
				var getSelected = jQuery('#representatives-plots-grid').datagrid('getSelected');

				if (getSelected) {
					window.open("index.php?page=Contracts.Home&contract_id=" + getSelected.c_id, '_blank');
				} else {
					jQuery.messager.alert('Грешка', 'Моля изберете запис, за който да бъде показана информация за договора.');
				}
			}
		}],
		onLoadSuccess: function () {
			jQuery('#representatives-plots-grid').datagrid('resize');
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
	});
}

function initAddEditRepresentativeFields() {
    jQuery('#rep-ae-name > input').validatebox({
        required: true,
        missingMessage: 'Моля въведете име на представител.'
    });
    jQuery('#rep-ae-surname > input').validatebox({
        required: true,
        missingMessage: 'Моля въведете презиме на представител.'
    });
    jQuery('#rep-ae-lastname > input').validatebox({
        required: true,
        missingMessage: 'Моля въведете фамилия на представител.'
    });

    new EgnValidateBox('#rep-ae-egn > input');

    var ekateComboboxData = ComboboxData.EkateCombobox;

    if (!jQuery('#rep-ae-rent-place > input').data().hasOwnProperty('combobox')) {
        ekateComboboxData[0].selected    = true;
        jQuery('#rep-ae-rent-place > input').combobox({
            data: ekateComboboxData,
            valueField: 'ekate',
            textField: 'text',
            editable: false,
            multiple: false,
            onHidePanel: onHidePanelMultiSelect,
            filter: function(q, row){
                var opts = jQuery(this).combobox('options');
                var text = row[opts.textField].toLowerCase();
                var value = row[opts.valueField];
                var find = q.toLowerCase();
                if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
                {
                    return true;
                }
            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }

    jQuery('#search-rt-ekate').combobox({
        data: ekateComboboxData,
        valueField: 'ekate',
        textField: 'text',
        editable: false,
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        filter: function(q, row){
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
            {
                return true;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-rt-c-type').combobox({
        data: ComboboxData.ContractTypeCombobox,
        valueField: 'id',
        textField: 'name',
        editable: false,
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}


function setEditRepresentativeFields(repInfo) {

    jQuery('#rep-ae-name > input').val(repInfo.rep_name);
    jQuery('#rep-ae-surname > input').val(repInfo.rep_surname);
    jQuery('#rep-ae-lastname > input').val(repInfo.rep_lastname);
    new EgnValidateBox('#rep-ae-egn > input').setValue(repInfo.egn);
    new CardIdValidateBox('#rep-ae-lk-nomer > input').setValue(repInfo.lk_nomer);
    jQuery('#rep-ae-lk-izdavane > input').val(repInfo.lk_izdavane);
    jQuery('#rep-ae-address > textarea').val(repInfo.address);
    jQuery('#rep-ae-phone > input').val(repInfo.rep_phone);
    jQuery('#rep-ae-rent-place > input').combobox('setValue', repInfo.rent_place);
    jQuery('#rep-ae-iban > input').val(repInfo.iban);
}

function getRepresentativeAddEditData() {
    return {
        rep_name    : jQuery('#rep-ae-name > input').val(),
        rep_surname : jQuery('#rep-ae-surname > input').val(),
        rep_lastname: jQuery('#rep-ae-lastname > input').val(),
        egn         : new EgnValidateBox('#rep-ae-egn > input').getValue(),
        lk_nomer    : new CardIdValidateBox('#rep-ae-lk-nomer > input').getValue(),
        lk_izdavane : jQuery('#rep-ae-lk-izdavane > input').val(),
        address     : jQuery('#rep-ae-address > textarea').val(),
        rep_phone   : jQuery('#rep-ae-phone > input').val(),
        rent_place  : jQuery('#rep-ae-rent-place > input').combobox('getValue'),
        iban        : jQuery('#rep-ae-iban > input').val()
    }
}

function clearRepresentativeAddEditData() {
    jQuery('#rep-ae-name > input').val('');
    jQuery('#rep-ae-surname > input').val('');
    jQuery('#rep-ae-lastname > input').val('');
    new EgnValidateBox('#rep-ae-egn > input').clear();
    new CardIdValidateBox('#rep-ae-lk-nomer > input').setValue('');
    jQuery('#rep-ae-lk-izdavane > input').val('');
    jQuery('#rep-ae-address > textarea').val('');
    jQuery('#rep-ae-phone > input').val('');
    jQuery('#rep-ae-rent-place > input').combobox('reset');
    jQuery('#rep-ae-iban > input').val('');
}

function initRepresentativesFilter() {
    var obj = {
        rep_name : jQuery('#search-rt-rep-name').val(),
        rep_egn  : jQuery('#search-rt-rep-egn').val(),
        owner    : jQuery('#search-rt-owner').val(),
        egn      : jQuery('#search-rt-egn').val(),
        company  : jQuery('#search-rt-company').val(),
        eik      : jQuery('#search-rt-eik').val(),
        mol      : jQuery('#search-rt-mol').val(),
        kad_ident: jQuery('#search-rt-kad-ident').val(),
        ekate    : jQuery('#search-rt-ekate').combobox('getValues'),
        masiv    : jQuery('#search-rt-masiv').val(),
        imot     : jQuery('#search-rt-imot').val(),
        c_num    : jQuery('#search-rt-c-num').val(),
        c_type   : jQuery('#search-rt-c-type').combobox('getValues'),
    }

    initOwnerRepresentativesTree(1, obj);
}

function clearRepresentativesFilter() {
    jQuery('#search-rt-rep-name').val('');
    jQuery('#search-rt-rep-egn').val('');
    jQuery('#search-rt-owner').val('');
    jQuery('#search-rt-egn').val('');
    jQuery('#search-rt-company').val('');
    jQuery('#search-rt-eik').val('');
    jQuery('#search-rt-mol').val('');
    jQuery('#search-rt-kad-ident').val('');
    jQuery('#search-rt-masiv').val('');
    jQuery('#search-rt-imot').val('');
    jQuery('#search-rt-c-num').val('');

    if (jQuery('#search-rt-ekate').data().hasOwnProperty('combobox')) {
        jQuery('#search-rt-ekate').combobox('reset');
    } else {
        jQuery('#search-rt-ekate').val('');
    }

    if (jQuery('#search-rt-c-type').data().hasOwnProperty('combobox')) {
        jQuery('#search-rt-c-type').combobox('reset');
    } else {
        jQuery('#search-rt-c-type').val('');
    }

    initRepresentativesFilter(1,{});
}

function initOwnersRepresentedGrid(ownerId) {
    var ownerRepresentedPlotsGrid = jQuery('#owners-represented-plots-tables');
    var isDatagridBound = ownerRepresentedPlotsGrid.data().hasOwnProperty('datagrid');

    if (isDatagridBound) {
        if (ownerId !== 0) {
            ownerRepresentedPlotsGrid.datagrid({
                url: 'index.php?owners-rpc=owners-represented-grid',
                rpcParams: [{
                    owner_id: ownerId,
                }],
            });
        } else {
            ownerRepresentedPlotsGrid.datagrid({data: {rows: [], total: 0}});
        }
        return;
    }
    ownerRepresentedPlotsGrid.datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 10,
        fit: true,
        fitColumns: true,
        showFooter: true,
        sortName: 'rep_owner_names',
        sortOrder: 'desc',
        idField: 'id',
        singleSelect: true,
        border: false,
        columns: [[
            {
                field: 'kad_ident',
                title: '<b>Имот</b>',
                sortable: true,
                width: 150
            }, {
                field: 'rep_owner_names',
                title: '<b>Представител</b>',
                sortable: true,
                width: 150
            }, {
                field: 'rep_egn',
                title: '<b>ЕГН</b>',
                sortable: false,
                width: 150
            }, {
                field: 'farm_name',
                title: '<b>Стопанство</b>',
                sortable: true,
                width: 150
            }, {
                field: 'c_num',
                title: '<b>Договор</b>',
                sortable: true,
                width: 90
            }, {
                field: 'nm_usage_rights',
                title: '<b>Тип дог.</b>',
                sortable: true,
                width: 90
            }, {
                field: 'active_text',
                title: '<b>Статус</b>',
                sortable: false,
                width: 90
            }, {
                field: 'c_date',
                title: '<b>Сключен</b>',
                sortable: true,
                width: 120
            }, {
                field: 'start_date',
                title: '<b>Влиза в сила</b>',
                sortable: true,
                width: 120
            }, {
                field: 'due_date',
                title: '<b>Изтича</b>',
                sortable: true,
                width: 130
            }
        ]],
        pagination: true,
        rownumbers: true,
        toolbar: [{
            id: 'btnorepfilterplot',
            text: 'Филтър',
            iconCls: 'icon-filter',
            handler: function() {
                jQuery('#win-owner-reprsented-filters-fields').window('open');
            }
        }, {
            id: 'btnorepclearfilterplot',
            text: 'Всички',
            iconCls: 'icon-clear-filter',
            handler: function() {
                clearOwnerRepresentedFilters();
                var owner_id = jQuery('#owners-tree').tree('getSelected').id;
                jQuery('#owners-represented-plots-tables').datagrid({
                    url: 'index.php?owners-rpc=owners-represented-grid',
                    rpcParams: [{
                        owner_id: owner_id,
                    }],
                });
            }
        }, {
            id: 'btnorepaddfilter',
            text: 'Представител',
            iconCls: 'icon-info',
            handler: function() {
                var getSelected = jQuery('#owners-represented-plots-tables').datagrid('getSelected');

                if (getSelected) {
                    jQuery('#win-representatives-tree').window('open');
                    initOwnerRepresentativesTree(1,{rep_id: getSelected.id});
                } else {
                    jQuery.messager.alert('Грешка', 'Моля изберете представител, за когото да бъде показана информация.');
                }
            }
        }, {
            id: 'btnorepinfoplot',
            text: 'Имот',
            iconCls: 'icon-info',
            handler: function() {
                var getSelected = jQuery('#owners-represented-plots-tables').datagrid('getSelected');

                if (getSelected) {
                    window.open("index.php?page=Plots.Home&plot_id=" + getSelected.gid, '_blank');
                } else {
                    jQuery.messager.alert('Грешка', 'Моля изберете имот, за който да бъде показана информация.');
                }
            }
        }, {
            id: 'btnorepinfocontract',
            text: 'Договор',
            iconCls: 'icon-info',
            handler: function() {
                var getSelected = jQuery('#owners-represented-plots-tables').datagrid('getSelected');

                if (getSelected) {
                    window.open("index.php?page=Contracts.Home&contract_id=" + getSelected.c_id, '_blank');
                } else {
                    jQuery.messager.alert('Грешка', 'Моля изберете запис, за който да бъде показана информация за договора.');
                }
            }
        }, {
            id: 'btnorepinfocontract',
            text: 'Премахване на представител',
            iconCls: 'icon-delete',
            handler: function() {
                var getSelectedOwner = jQuery('#owners-tree').tree('getSelected');
                if (getSelectedOwner.attributes.owner_type == 0) {
                    jQuery.messager.alert('Грешка', 'Не може да премахнете представител на юридическо лице.', 'warning');
                    return false;
                }

                var getSelected = jQuery('#owners-represented-plots-tables').datagrid('getSelected');

                if (getSelected) {
                    var obj = {
                        rel_id: getSelected.rel_id,
                        owner_id: getSelected.owner_id,
                        rep_id: null,
                        contract_id: getSelected.c_id
                    };
                    var confirmationMsg = 'След премахване на избрания представител, собственика на избрания имот ще се представлява лично за избрания договор.';
                    confirmationMsg += ' Сигурни ли сте, че искате да продължите?';
                    jQuery.messager.confirm('Потвърждение', confirmationMsg, function(r) {
                        if (r) {
                            TF.Rpc.Owners.OwnersRepresentedGrid.changeOwnerRepresentative(obj)
                                .done(function () {
                                    jQuery('#owners-represented-plots-tables').datagrid('reload');
                                })
                                .fail(function (errorObj) {
                                    jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
                                });
                        }
                    });
                } else {
                    jQuery.messager.alert('Грешка', 'Моля изберете запис, за който да бъде показана информация за договора.');
                }
            }
        }],
        onLoadSuccess: function(data) {
            if(data.rows.length === 0) {
                jQuery('#owners-plots-tabs').tabs('disableTab', 2)
            } else {
                jQuery('#owners-plots-tabs').tabs('enableTab', 2)
            }
        },
        rowStyler: function(index, row){
            if (!row.operational && row.active){
                return 'color:#aaa;';
            } else if (!row.active) {
                return 'color:#aaa; text-decoration: line-through';
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
    });

    initOwnerRepresentedFilters();
}

function initOwnerRepresentedFilters() {

    var ekateComboboxData        = ComboboxData.EkateCombobox,
        farmingComboboxData      = ComboboxData.FarmingCombobox,
        contractTypeComboboxData = ComboboxData.ContractTypeCombobox;

    ekateComboboxData[0].selected        = true;
    farmingComboboxData[0].selected      = true;
    contractTypeComboboxData[0].selected = true;

    jQuery('#o-rep-ekate').combobox({
        data: ekateComboboxData,
        valueField: 'ekate',
        textField: 'text',
        editable: false,
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        filter: function(q, row){
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
            {
                return true;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#o-rep-nm-usage-rights').combobox({
        data: contractTypeComboboxData,
        valueField: 'id',
        textField: 'name',
        editable: false,
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#o-rep-farming').combobox({
        data: farmingComboboxData,
        valueField: 'id',
        textField: 'name',
        editable: false,
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function getOwnerRepresentedFilters() {
    return {
        rep_names : jQuery("#o-rep-name").val(),
        rep_egn   : jQuery("#o-rep-egn").val(),
        c_num     : jQuery("#o-rep-c-num").val(),
        c_type    : jQuery("#o-rep-nm-usage-rights").combobox('getValues'),
        kad_ident : jQuery("#o-rep-kad-ident").val(),
        ekate     : jQuery("#o-rep-ekate").combobox('getValues'),
        masiv     : jQuery("#o-rep-masiv").val(),
        imot      : jQuery("#o-rep-imot").val(),
        owner_id  : jQuery('#owners-tree').tree('getSelected').id,
        farming   : jQuery('#o-rep-farming').combobox('getValues')
    }
}

function clearOwnerRepresentedFilters() {
    jQuery("#o-rep-name").val('');
    jQuery("#o-rep-egn").val('');
    jQuery("#o-rep-c-num").val('');
    jQuery("#o-rep-nm-usage-rights").combobox('reset');
    jQuery("#o-rep-kad-ident").val('');
    jQuery("#o-rep-ekate").combobox('reset');
    jQuery("#o-rep-masiv").val('');
    jQuery("#o-rep-imot").val('');
    jQuery('#o-rep-farming').combobox('reset');
}

function initOwnerRepGridFilter() {
    jQuery('#win-owner-reprsented-filters-fields').window('close');

    jQuery('#owners-represented-plots-tables').datagrid({
        url: 'index.php?owners-rpc=owners-represented-grid',
        rpcParams: [getOwnerRepresentedFilters()],
    });
}
