Namespace('TF.Rpc.Owners');
var ownerType,
    isEditing = false,
    ownerIdForInformation,
    ComboboxData,
	lastAddedOwner = 0,
	lastAddedParent = 0;
    PostPaymentOwner = new PostPaymentFields('owner');


jQuery(function () {
	setUserRights();
    TF.Rpc.Common.CombinedComboboxData.read()
        .done(function (data) {
            ComboboxData = data;
            initComboboxItems();
			initOwnersDocumentsGrid(0);
			initOwnersPlotsGrid(0);
			initOwnersInheritedPlotstGrid(0);
			initOwnersRepresentedGrid(0);
			initOwnersPaymentsGrid(0);
            initOwnersTree(1, GET);
        })
        .fail(function (error) {
            jQuery.messager.alert('Грешка', error.getMessage(), 'warning');
        });
	//retrieve GET parameters
	var GET = {};
	location.search.substr(1).split("&").forEach(function(item) {GET[decodeURIComponent(item.split("=")[0])] = decodeURIComponent(item.split("=")[1])});
	if (GET.owner_id) {
		ownerIdForInformation = GET.owner_id;
	};

	//change URL without refresh if it's posible
	if (history && history.replaceState){
		history.replaceState(null, null, 'index.php?page=' + GET.page);
	}

	if(GET.rep_id) {
		jQuery('#win-representatives-tree').window('open');
		initOwnerRepresentativesTree(1,{rep_id: GET.rep_id});
	}

    jQuery('#win-plot-owner-add').window({
        onClose: function () {
          isEditing = false;
        }
    });

	jQuery('#btn-owner-filter').bind('click', function(){
		ownersFilter();
	});

	jQuery('#btn-owners-plots-filter').bind('click', function(){
        var dataObj = jQuery('#owners-plots-tables').datagrid('options').rpcParams[0];
        jQuery('#owners-plots-tables').datagrid('loadRpc',
			[{
				owner_id: dataObj.owner_id,
				year: jQuery('#search-year').combobox('getValue'),
				ekate: jQuery('#search-plot-ekate').combobox('getValues'),
				farm: jQuery('#search-plot-farm').combobox('getValues')
			}]);


        var dataArr = jQuery('#owners-inherited-plots-tables').datagrid('options').rpcParams;
        dataArr[1] = jQuery('#search-year').combobox('getValue');
        dataArr[2] = jQuery('#search-plot-ekate').combobox('getValues');
        dataArr[3] = jQuery('#search-plot-farm').combobox('getValues');

		jQuery('#owners-inherited-plots-tables').datagrid('loadRpc', dataArr);
		jQuery('#win-owners-plots-filter').window('close');
	});

	jQuery('#btn-save-owner').bind('click', function() {
		let page = 1;	

		if(validateSubmitInfo())
		{
			var obj = getAddOwnerInputFieldsData(),
				selectedOwner = jQuery('#owners-tree').tree('getSelected');
			if (!isEditing) {
				if(jQuery('#IsLegal').prop('checked')) {
					TF.Rpc.Owners.OwnersTree.addLegalOwner(obj)
					.done(function (data) {
						jQuery('#win-plot-owner-add').window('close');
						if (!addingOwnerHeritor) {
							jQuery('#owners-tree').tree('select', selectedOwner.target);
						}
						addingOwnerHeritor = false;
						isEditing = false;
						jQuery('#owners-tree').tree('loadRpc');
					})
					.fail(function (data) {
						if (data.getCode() == -33310) {
							jQuery.messager.alert('Грешка', data.getMessage(), 'warning');
							return false;
						} else {
							RpcErrorHandler.show(data);
							return false;
						}
					});
				} else {
					TF.Rpc.Owners.OwnersTree.addOwner(obj)
					.done(function (response) {
						[ownerId, page] = response; 

						if(lastAddedParent == 0) {
							lastAddedParent = ownerId;
						}
						lastAddedOwner = ownerId;

						jQuery('#win-plot-owner-add').window('close');
						if (!addingOwnerHeritor) {
							jQuery('#owners-tree').tree('select', selectedOwner.target);
						} else {
							jQuery('#choose-heritor').combobox('loadRpc');
                        }
						addingOwnerHeritor = false;
						isEditing = false;
						if (obj.is_dead) {
							if(lastAddedParent != lastAddedOwner && lastAddedOwner != 0 && lastAddedParent != 0) {
								TF.Rpc.Owners.OwnersHeritorsTree.addOwnerHeritor(lastAddedOwner, lastAddedParent)
									.done(function (data) {
									}).
								fail(function (errorObj) {
									jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
								});
							}
							lastAddedParent = ownerId;
						} else {
							if(lastAddedParent != lastAddedOwner && lastAddedOwner != 0 && lastAddedParent != 0) {
								TF.Rpc.Owners.OwnersHeritorsTree.addOwnerHeritor(lastAddedOwner, lastAddedParent)
									.done(function (data) {
										jQuery('#win-choose-heritor').window('close');
										ownerID = selectedOwner.attributes.id;
										jQuery('#owners-tree').tree('loadRpc');
										selectParentById(selectedOwner.attributes.id);
									}).
								fail(function (errorObj) {
									jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
								});
							}
						}
						if (!obj.is_dead) {
							lastAddedOwner = 0;
							lastAddedParent = 0;
						} else {
							addOwnerHeritorHelperIfDead(lastAddedParent, obj.is_dead);
						}

						// will trigger initTreeLoad and updade pagination page number
						// owner id will be used in initTreeLoad to select last added record
						ownerID = ownerId
						jQuery('#owners-tree-pagination').pagination('select', page)
						
					})
					.fail(function (data) {
						if (data.getCode() == -33310) {
							jQuery.messager.alert('Грешка', data.getMessage(), 'warning');
							return false;
						} else {
							RpcErrorHandler.show(data);
							return false;
						}
					});
				}
			} else {
				var selectedOwner = jQuery('#owners-tree').tree('getSelected');
				if(selectedOwner && obj.is_legal === true) {
					TF.Rpc.Owners.OwnersTree.editLegalOwner(obj,selectedOwner.id)
					.done(function (data) {
						jQuery('#win-plot-owner-add').window('close');
						jQuery('#owners-tree').tree('reload');
						isEditing = false;
					})
					.fail(function (data) {
						if (data.getCode() == -33310) {
							jQuery.messager.alert('Грешка', data.getMessage(), 'warning');
							return false;
						} else {
							RpcErrorHandler.show(data);
							return false;
						}
					});
				} else if (selectedOwner && obj.is_legal === false){
                    var selectedOwner = jQuery('#owners-tree').tree('getSelected');
					TF.Rpc.Owners.OwnersTree.editOwner(obj,selectedOwner.id)
					.done(function (response) {
						if(lastAddedParent == 0) {
							lastAddedParent = response;
						}
						lastAddedOwner = response;

						jQuery('#win-plot-owner-add').window('close');
						if (!addingOwnerHeritor) {
							jQuery('#owners-tree').tree('reload');
							jQuery('#owners-tree').tree('select', selectedOwner.target);
						} else {
                            data = selectedOwner.id;
                        }
						addingOwnerHeritor = false;
						isEditing = false;
						if (obj.is_dead) {
							if(lastAddedParent != lastAddedOwner && lastAddedOwner != 0 && lastAddedParent != 0) {
								TF.Rpc.Owners.OwnersHeritorsTree.addOwnerHeritor(lastAddedOwner, lastAddedParent)
									.done(function (data) {
									}).
								fail(function (errorObj) {
									jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
								});
							}
							lastAddedParent = response;
						} else {
							if(lastAddedParent != lastAddedOwner && lastAddedOwner != 0 && lastAddedParent != 0) {
								TF.Rpc.Owners.OwnersHeritorsTree.addOwnerHeritor(lastAddedOwner, lastAddedParent)
									.done(function (data) {
										jQuery('#win-choose-heritor').window('close');
										ownerID = selectedOwner.attributes.id;
										jQuery('#owners-tree').tree('loadRpc');
										selectParentById(selectedOwner.attributes.id);
									}).
								fail(function (errorObj) {
									jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
								});
							}
						}
						if (!obj.is_dead) {
							lastAddedOwner = 0;
							lastAddedParent = 0;
						} else {
							addOwnerHeritorHelperIfDead(lastAddedParent, obj.is_dead);
						}
						jQuery('#owners-tree').tree('loadRpc');
					})
					.fail(function (data) {
						jQuery.messager.alert('Грешка', data.getMessage(), 'warning');
					});
				}
			}
		}
	});

	jQuery('#representatives-tree-btn').bind('click', function () {
			jQuery('#win-representatives-tree').window('open');
			initOwnerRepresentativesTree(1,{});
		
	});

	let egnBox = new EgnValidateBox('#egn > input');
	jQuery('#eik > input').numberbox();

	jQuery('#is_foreigner').on('change', function () {
		if(jQuery('#is_foreigner').is(':checked')) {
			egnBox.disableValidation();
			jQuery('#egn-row').hide();
			jQuery('#surname > input').validatebox('disableValidation');
			jQuery('#birthday-row').show();
			jQuery('#country > input').combobox({
				required: true,
			});

		} else {
			egnBox.enableValidation();
			jQuery('#egn-row').show();
			jQuery('#surname > input').validatebox('enableValidation');
			jQuery('#birthday-row').hide();
			jQuery('#country > input').combobox({
				required: false,
			});
		}
	});

	jQuery('#is_dead').on('change', function () {
		if(jQuery('#is_foreigner').is(':checked')) {
			egnBox.disableValidation();
		} else {
			egnBox.enableValidation();
		}
		
        if(jQuery('#is_dead').is(':checked')) {
            jQuery('#prepiska-row').show();
			jQuery('#dead-date-row').show();
        } else {
            jQuery('#prepiska-row').hide();
			jQuery('#dead-date-row').hide();
        }
	});
});

function initComboboxItems() {
	var ekateComboboxData  		= ComboboxData.EkateCombobox,
		farmingYearComboboxData = ComboboxData.FarmingYearCombobox,
		farmNameComboboxData    = ComboboxData.FarmNameCombobox;

	ekateComboboxData[0].selected    = true;
	farmNameComboboxData[0].selected = true;
	jQuery('#search-plot-ekate').combobox({
		data: ekateComboboxData,
		valueField: 'ekate',
		textField: 'text',
		editable: false,
		multiple: true,
        onHidePanel: onHidePanelMultiSelect,
		onSelect: onComboMultiSelect,
		filter: function(q, row){
			var opts = jQuery(this).combobox('options');
			var text = row[opts.textField].toLowerCase();
			var value = row[opts.valueField];
			var find = q.toLowerCase();
			if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
			{
				return true;
			}
		},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

	jQuery('#search-ekate').combobox({
		data: ekateComboboxData,
		valueField: 'ekate',
		textField: 'text',
		editable: false,
		multiple: true,
        onHidePanel: onHidePanelMultiSelect,
		onSelect: onComboMultiSelect,
		filter: function(q, row){
			var opts = jQuery(this).combobox('options');
			var text = row[opts.textField].toLowerCase();
			var value = row[opts.valueField];
			var find = q.toLowerCase();
			if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
			{
				return true;
			}
		},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

	jQuery('#search-c-type').combobox({
		data: ComboboxData.ContractTypeCombobox,
		valueField: 'id',
		textField: 'name',
		editable: false,
		multiple: true,
        onHidePanel: onHidePanelMultiSelect,
		onSelect: onComboMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

	jQuery('#search-year').combobox({
		data: farmingYearComboboxData,
		valueField: 'id',
		textField: 'farming_year',
		editable: false,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

	jQuery('#search-plot-farm').combobox({
		data: farmNameComboboxData,
		valueField: 'id',
		textField: 'title',
		editable: false,
		multiple: true,
        onHidePanel: onHidePanelMultiSelect,
		onSelect: onComboMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

	initAddEditRepresentativeFields();
}

function setAddEditFieldsValidators() {
	numLengthbox();
	jQuery('#name > input').validatebox({
		required: true,
		missingMessage: 'Моля въведете име на собственик.'
	});
	jQuery('#surname > input').validatebox({
		required: true,
		missingMessage: 'Моля въведете презиме на собственик.'
	});
	jQuery('#lastname > input').validatebox({
		required: true,
		missingMessage: 'Моля въведете фамилия на собственик.'
	});

	new EgnValidateBox('#egn > input');

	jQuery('#birthday > input').datebox({
		required: true
	});

	jQuery('#company_name > input').validatebox({
		required: true,
		missingMessage: 'Моля въведете име на фирма.'
	});
	jQuery('#eik > input').numberbox({
		required: true,
		validType: 'setLength[9]',
		missingMessage: 'Моля въведете ЕИК номер.',
        parser: function(s){
            return s;
        }
	});
	jQuery('#mobile > input').validatebox({
		missingMessage: 'Моля въведете мобилен телефон.'
	});
	jQuery('#email > input').validatebox({
		missingMessage: 'Моля въведете имейл на собственика.'
	});

	jQuery('#dead-date > input').datebox({
		required: true
	});

	jQuery('#is_foreigner').trigger('change');
	jQuery('#is_dead').trigger('change');

	jQuery('#country > input').combobox({
		required: false,
	});
}

function validateSubmitInfo() {
	var bic = jQuery('#bic > input').val();
	if(bic && bic.length !== 8) {
		jQuery.messager.alert('Грешка', 'Дължината на BIC трябва да е 8 символа.');
		return false;
	}

	if (jQuery('#is_dead').is(':checked')) {
		const deadDate = jQuery('#dead-date > input').datebox('getValue');
		if(!deadDate) {
			jQuery.messager.alert('Грешка', 'Моля попълнете задължителните полета.');
			return false;
		}
	}

	if (jQuery('#is_foreigner').is(':checked')) {
		const birthday = jQuery('#birthday > input').datebox('getValue');
		const country = jQuery('#country > input').combobox('getValue');

		if(!birthday || !country) {
			jQuery.messager.alert('Грешка', 'Моля попълнете задължителните полета.');
			return false;
		}
	}

	if(jQuery('#phone input').val().length && jQuery('#phone input').val().length < 5) {
		jQuery.messager.alert('Грешка', 'Моля въведете поне 5 символа в полето "Телефон"', 'warning');
		return;
	}

	if(jQuery('#mobile input').val().length && jQuery('#mobile input').val().length < 5) {
		jQuery.messager.alert('Грешка', 'Моля въведете поне 5 символа в полето "Мобилен"', 'warning');
		return;
	}

	// setAddEditFieldsValidators();
	if (jQuery('#is_physical input').is(':checked')){
		if (jQuery('#name > input').validatebox('isValid')
				&& jQuery('#surname > input').validatebox('isValid')
				&& jQuery('#lastname > input').validatebox('isValid')
                && new EgnValidateBox('#egn > input').isValid()) {
			return true;
		} else {
			jQuery.messager.alert('Грешка', 'Моля попълнете задължителните полета.');
			return false;
		}
	} else {
		if (jQuery('#company_name > input').validatebox('isValid')
				&& jQuery('#eik > input').numberbox('isValid')) {
			return true;
		} else {
			jQuery.messager.alert('Грешка', 'Моля попълнете задължителните полета.');
			return false;
		}
	}

	
}

function displayTables(owner_type){
	if(owner_type == 'physical') {
		jQuery('#company-data').hide();
		jQuery('#owner-data').show();
		jQuery('#is-dead').show();
		jQuery('#is-foreigner').show();
	}
	if(owner_type == 'legal') {
		jQuery('#owner-data').hide();
		jQuery('#is-dead').hide();
		jQuery('#is-foreigner').hide();
		jQuery('#company-data').show();
	}
}

function displayTablesOnCallbackComplete(){
	if(ownerType == 0) {
		displayTables('legal');

		jQuery('#is_legal > input').prop('checked', true);
		jQuery('#is_physical > input').prop('checked', false);
	}else{
		displayTables('physical');

		jQuery('#is_legal > input').prop('checked', false);
		jQuery('#is_physical > input').prop('checked', true);
	}
}

function initOwnersInfo(owner){
    if(!owner)
    {
        jQuery('#info-owner-type').html('');
        jQuery('#info-owner-names').html('');
        jQuery('#info-owner-egn').html('');
        jQuery('#info-owner-lk-nomer').html('');
        jQuery('#info-owner-lk-izdavane').html('');
        jQuery('#info-owner-phone').html('');
        jQuery('#info-owner-fax').html('');
        jQuery('#info-owner-mobile').html('');
        jQuery('#info-owner-email').html('');
        jQuery('#info-owner-bank-name').html('');
        jQuery('#info-owner-iban').html('');
        jQuery('#info-owner-bic').html('');
        jQuery('#info-owner-remark').html('');
        jQuery('#info-owner-address').html('');
        jQuery('#info-owner-is-dead').html('');
		jQuery('#info-owner-dead-date').html('');	
        jQuery('#info-owner-is-foreigner').html('');
        jQuery('#info-company-name').html('');
        jQuery('#info-company-eik').html('');
        jQuery('#info-company-mol').html('');
        jQuery('#info-company-address').html('');
		
        return false;
    }
    if(owner.owner_type == 0){
        jQuery('#info-owner').hide();
        jQuery('#info-company').show();
    }
    else if(owner.owner_type == 1){
        jQuery('#info-owner').show();
        jQuery('#info-company').hide();
    }

    jQuery('#info-owner-type').html(owner.owner_type_name);
    jQuery('#info-owner-names').html(owner.owner_names);
    jQuery('#info-owner-egn').html(owner.egn);
    jQuery('#info-owner-lk-nomer').html(owner.lk_nomer);
    jQuery('#info-owner-lk-izdavane').html(owner.lk_izdavane);
    jQuery('#info-owner-phone').html(owner.phone);
    jQuery('#info-owner-fax').html(owner.fax);
    jQuery('#info-owner-mobile').html(owner.mobile);
    jQuery('#info-owner-email').html(owner.email);
    jQuery('#info-owner-bank-name').html(owner.bank_name);
    jQuery('#info-owner-iban').html(owner.iban);
    jQuery('#info-owner-bic').html(owner.bic);
    jQuery('#info-owner-address').html(owner.address);
	jQuery('#info-owner-is-dead').html(owner.is_dead_text);
	jQuery('#info-owner-dead-date').html(owner.dead_date_text);

    jQuery('#info-owner-remark').html(owner.remark);
	jQuery('#info-is-foreigner').html(owner.is_foreigner);
    jQuery('#info-company-name').html(owner.company_name);
    jQuery('#info-company-eik').html(owner.eik);
    jQuery('#info-company-mol').html(owner.mol);
    jQuery('#info-company-address').html(owner.company_address);
}

function canRentBePaid(owner){
	if(!owner.is_dead){
		jQuery('#allow-transfer-to-payments').linkbutton('enable');
		return;
	}

	var currentFarmingYear = getFarmingYearId();
	var ownerDeadYear = owner.is_dead && owner.dead_date ? getFarmingYearId(owner.dead_date) : null;

	if(currentFarmingYear === ownerDeadYear) {
		jQuery('#allow-transfer-to-payments').linkbutton('enable');
		return;
	} 

	jQuery('#allow-transfer-to-payments').linkbutton('disable');;
}


function getAddOwnerInputFieldsData() {
    var fieldsData = {
        name: jQuery('#name > input').val(),
        surname: jQuery('#surname > input').val(),
        lastname: jQuery('#lastname > input').val(),
        egn: new EgnValidateBox('#egn > input').getValue(),
        birthday: jQuery('#is_foreigner').is(':checked') ? jQuery('#birthday > input').datebox('getValue') : null,
        lk_nomer: new CardIdValidateBox('#lk_nomer > input').getValue(),
        lk_izdavane: jQuery('#lk_izdavane > input').val(),
        company_name: jQuery('#company_name > input').val(),
        eik: jQuery('#eik > input').numberbox('getValue'),
        mol: jQuery('#mol > input').val(),
        company_address: jQuery('#company_address > textarea').val(),
        remark: tinyMCE.get('remark_input').getContent(),
        phone: jQuery('#phone > input').val(),
        fax: jQuery('#fax > input').val(),
        mobile: jQuery('#mobile > input').val(),
        email: jQuery('#email > input').val(),
        bank_name: jQuery('#bank_name > input').val(),
        iban: jQuery('#iban > input').val(),
        bic: jQuery('#bic > input').val(),
        address: jQuery('#address > textarea').val(),
        is_legal: jQuery('#is_legal > input').is(':checked') ? true : false,
        is_physical: jQuery('#is_physical > input').is(':checked') ? true : false,
        is_dead: jQuery('#is-dead > input').is(':checked') ? true : false,
		dead_date: jQuery('#is-dead > input').is(':checked') ? jQuery('#dead-date > input').datebox('getValue') : null,
        is_foreigner: jQuery('#is_foreigner').is(':checked') ? true : false,
        rent_place: jQuery('#rent-place > input').combobox('getValue'),
		country: jQuery('#country > input').combobox('getValue'),
        prepiska: jQuery('#is-dead > input').is(':checked') ? jQuery('#prepiska > input').val() : null,
		post_payment_fields: (new PostPaymentFields('owner')).getPostPaymentValues()

    };

    return fieldsData;
}

function clearAddOwnerInputDataFields() {
    jQuery('#name > input').val('');
    jQuery('#surname > input').val('');
    jQuery('#lastname > input').val('');
	new CardIdValidateBox('#lk_nomer > input').setValue('');
    jQuery('#lk_izdavane > input').val('');
    jQuery('#company_name > input').val('');
    jQuery('#eik > input').numberbox('clear');
    jQuery('#mol > input').val('');
    jQuery('#company_address > textarea').val('');
    jQuery('#phone > input').val('');
    jQuery('#fax > input').val('');
    jQuery('#mobile > input').val('');
    jQuery('#email > input').val('');
    jQuery('#bank_name > input').val('');
    jQuery('#iban > input').val('');
    jQuery('#bic > input').val('');
    jQuery('#address > textarea').val('');
    jQuery('#remark > textarea').val('');
    tinyMCE.get('remark_input').setContent('');
    jQuery('#is_legal > input').prop('checked', false);
    jQuery('#is_physical > input').prop('checked', true);
    jQuery('#is-dead > input').prop('checked', false);
	jQuery('#is_foreigner').prop('checked', false);
    jQuery('#rent-place > input').val('');
    jQuery('#country > input').val('');
	new EgnValidateBox('#egn > input').setValue('');
	PostPaymentOwner.clearPostPaymentInputs();

    setAddEditFieldsValidators();
}


function addOwnerHeritorHelperIfDead(owner_id, isDead) {
	if (!hasPlotRightsRW) {
		messagerPlotsWriteRights();
		return;
	}
	var selectedHeritor = jQuery('#owners-heritors-tree').tree('getSelected');
	if(owner_id)
	{
		if(selectedHeritor && !selectedHeritor['attributes'].is_dead)
		{
			jQuery.messager.alert('Грешка', 'Не може да добавите наследници на жив човек.');
		}
		else
		{
			jQuery('#search-heritor-by-egn').val('');

			jQuery('#choose-heritor').combobox({
				url: 'index.php?owners-rpc=owners-heritors-combobox',
				valueField: 'id',
				textField: 'owner_names',
				required: true,
				missingMessage: 'Моля изберете наследник.',
				rpcParams: [owner_id, null, null],
				filter: function(q, row){
					var opts = jQuery(this).combobox('options');
					var text = row[opts.textField].toLowerCase();
					var find = q.toLowerCase();
					if(text.indexOf(find) != -1)
					{
						return true;
					}
				},
				onLoadSuccess: function () {
					jQuery(this).combobox('reset');

					if(isDead) {
						jQuery('#win-choose-heritor').window('open');
					}
				},
				loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
			});

		}
	}
	else
	{
		jQuery.messager.alert('Грешка', 'Моля изберете собственик.');
	}
}

function selectParentById(id) {
	var ownersTree = jQuery('#owners-tree'),
		data = ownersTree.tree('getRoots'),
		target;

	data.forEach(function (owner) {
		if (owner.attributes.id == id) {
			target = owner.target;
		};
	});

	ownersTree.tree('select', target);
}

function displayRentaInfoForOwner() {
	var selectedOwner = jQuery('#owners-tree').tree('getSelected');
	var ownerName =  selectedOwner.attributes.owner_names;
	var companyName = selectedOwner.attributes.company_name;

	 // This code is commented in order to cover the case from this task: GPS-4225
    // var farmingYear = getFarmingYearId();
	
	var farmingYear = getFarmingYearByCalendarYear(new Date().getFullYear());
	if(ownerName.trim().length > 0) {
		window.open("index.php?page=OwnerPayments.Home&owner_name=" + ownerName.trim() + "&farming_year=" + farmingYear.year_id, '_blank');
	} else {
		window.open("index.php?page=OwnerPayments.Home&company_name=" + companyName + "&farming_year=" + farmingYear.year_id, '_blank');
	}
}
