/*jslint browser: true */
/*global jQuery, EasyUIRPCLoaders.EasyUIGridCustomLoader, initOwnersDocumentsGrid*/
function initOwnersPaymentsGrid(owner_id) {
    'use strict';
    var ownersPaymentsTables = jQuery('#owners-payments-tables'),
        isDatagridBound = ownersPaymentsTables.data().hasOwnProperty('datagrid');

    if (isDatagridBound) {
        if (owner_id !== 0) {
            ownersPaymentsTables.datagrid({
                url: 'index.php?owners-rpc=owners-payments',
                rpcParams: [owner_id],
            });
        } else {
            ownersPaymentsTables.datagrid({data: {rows: [], total: 0}});
            initOwnersDocumentsGrid();
        }
        return;
    }
    ownersPaymentsTables.datagrid({
        iconCls: 'icon-rents',
        title: 'Изплатени ренти',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 10,
        fit: true,
        border: true,
        fitColumns: true,
        showFooter: true,
        singleSelect: true,
        url: 'index.php?owners-rpc=owners-payments',
        rpcParams: [owner_id],
        idField: 'transaction_id',
        sortName: 'transaction_id',
        sortOrder: 'desc',
        columns: [[
            {
                field: 'c_num',
                title: '<b>Договор</b>',
                sortable: true,
                width: 100,
            }, {
                field: 'paid_from_text',
                title: '<b>Изплащане на</b>',
                sortable: false,
                width: 150,
                align: 'center'
            }, {
                field: 'paid_in_text',
                title: '<b>Изплащане чрез</b>',
                sortable: false,
                width: 150,
                align: 'center'
            }, {
                field: 'transaction_id',
                title: '<b>Номер на<br/>транзакцията</b>',
                sortable: true,
                width: 70
            }, {
                field: 'payment_date',
                title: '<b>Дата</b>',
                sortable: true,
                width: 70
            }, {
                field: 'farming_year',
                title: '<b>Стопанска година</b>',
                sortable: true,
                width: 70
            }, {
                field: 'recipient',
                title: '<b>Изплатено на</b>',
                sortable: true,
                width: 150
            }, {
                field: 'payer_name',
                title: '<b>Изплатено от</b>',
                sortable: true,
                width: 150
            }, {
                field: 'bank_acc',
                title: '<b>Банкова сметка</b>',
                sortable: true,
                width: 150
            }
        ]],
        pagination: true,
        rownumbers: true,
        onBeforeLoad: function () {
            jQuery('#owners-payments-tables').datagrid('clearChecked');
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
    });
}
