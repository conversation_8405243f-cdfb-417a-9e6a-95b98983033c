Namespace('TF.Rpc.Collections');
var _todayDate = new Date();
var is_reverse_payment = true;

function initCollectionPaymentsGrid(record) {

    jQuery('#collection-payments-table').datagrid({
        url: 'index.php?collections-rpc=collection-payments-grid',
        iconCls: 'icon-edit-geometry',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 20,
        fit: true,
        fitColumns: true,
        showFooter: true,
        singleSelect: true,
        idField: 'id',
        sortName: 'id',
        border: true,
        sortOrder: 'desc',
        rpcParams: [{
            sublease_id: record.id,
            farming_year: record.farming_year
        }],
        view: bufferview,
        columns: [
            [{
                field: 'payment_type',
                title: '<b>Тип</b>',
                sortable: true,
                align: 'center',
                width: 100,
            }, {
                field: 'date',
                title: '<b>Дата</b>',
                sortable: true,
                align: 'center',
                width: 100,
            }, {
                field: 'amount_txt',
                title: '<b>Сума</b>',
                sortable: true,
                align: 'center',
                width: 100,
            }, {
                field: 'bank_payment',
                title: '<b>Получено по</b>',
                align: 'center',
                width: 100,
                formatter: function(value, row, index) {

                    if (row.payment_type == 'Сторниране') {
                        return '-';
                    } else {
                        if (value === true) {
                            return 'Банков път';
                        } else {
                            return 'В брой';
                        }
                    }
                }
            }, {
                field: 'recieved_from',
                title: '<b>Представител</b>',
                align: 'center',
                sortable: true,
                width: 100,
            }, {
                field: 'user_name',
                title: '<b>Въвел</b>',
                align: 'center',
                width: 100,
            }, {
                field: 'payment_data',
                title: '<b>Платежно нареждане</b>',
                align: 'center',
                formatter: function(value, row, index) {
                    if (isValidCollectionToPrint(row,value)) {
                        try {
                            value = JSON.parse(value)
                            if (value.hasOwnProperty('recipient')) {

                                return '<button  type="button" class="icon-print" onclick="return printPaymentCollection(' + row.id +','+ value.payment_method_bank +','+ value.rko_number +',\''+ value.rko_text +'\')" style="width:16px;height:16px;border:0"></button> ';
                            }
                            return '-'
                        } catch (e) {
                            return '-';
                        }
                    } else {
                        return '-';
                    }
                }
            }]
        ],
        pagination: false,
        rownumbers: true,
        onBeforeLoad: function() {},
        onLoadSuccess: function() {},
        onSelect: function(record) {},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initCollectionPersonalUseGrid(record) {
    let collectionPersonalUseTable = jQuery('#collection-personal-use-table');
    collectionPersonalUseTable.datagrid({
        url: 'index.php?collections-rpc=collection-payments-grid',
        rpcMethod: 'getPersonalUseCollections',
        iconCls: 'icon-edit-geometry',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 20,
        fit: true,
        fitColumns: true,
        showFooter: true,
        singleSelect: true,
        idField: 'sc.id',
        sortName: 'sc.id',
        border: true,
        sortOrder: 'desc',
        rpcParams: [{
            contract_id: record.contract_id,
            owner_id: record.owner_id,
            farming_year: record.farming_year,
            renta_type_id: record.renta_type_id,
        }],
        view: bufferview,
        columns: [
            [{
                field: 'c_num',
                title: '<b>Договор</b>',
                align: 'center',
                width: 100,
            },{
                field: 'date',
                title: '<b>Дата на плащането</b>',
                sortable: true,
                align: 'center',
                width: 100,
            },{
                field: 'owner_names',
                title: '<b>Собственик</b>',
                sortable: true,
                align: 'center',
                width: 100,
            }, {
                field: 'egn_eik',
                title: '<b>ЕГН/ЕИК</b>',
                sortable: true,
                align: 'center',
                width: 100,
            }, {
                field: 'renta_type_name',
                title: '<b>Тип натура</b>',
                sortable: true,
                align: 'center',
                width: 100,
            }, {
                field: 'amount_txt',
                title: '<b>Получена сума</b>',
                sortable: true,
                align: 'center',
                width: 100
            }]
        ],
        toolbar: [
            {
                id: 'btnaddpersonaluse',
                text: 'Изплащане на обработки',
                iconCls: 'icon-payments',
                handler: function() {
                    var selectedOwner = jQuery('#collect-treatments-tables').treegrid('getSelected');
                    if (selectedOwner === null) {
                        jQuery.messager.alert('Грешка', 'Моля изберете собственик!', 'error');
                    }
                    if(selectedOwner.renta_type_id === null){
                        jQuery.messager.alert('Грешка', 'Операцията няма да бъде извършена, защото няма въведена култура за лично ползване. Моля въведете култура от панела за Лично ползване.', 'warning');
                        return false;
                    }
                    //maping values in order to be compatible with initAddCollectionFields
                    let params = {
                        id : selectedOwner.contract_id,
                        farming_year : selectedOwner.farming_year,
                        subleasing_farm_company_address : selectedOwner.farming_address,
                        subleasing_farm_id : selectedOwner.farming_id,
                        subleasing_farm_mol : selectedOwner.farming_mol,
                        subleasing_farm_mol_egn : selectedOwner.farming_mol_egn,
                        c_num : selectedOwner.c_num,
                        owner_names : selectedOwner.owner_names,
                        unpaid: selectedOwner.personal_use_unpaid_treatments,
                        unpaid_txt: selectedOwner.personal_use_unpaid_treatments_txt,
                        owner_id: selectedOwner.owner_id,
                        owner_bank_name: selectedOwner.owner_bank_name,
                        owner_iban: selectedOwner.owner_iban,
                        owner_bic: selectedOwner.owner_bic,
                        type: 2,
                        renta_type_name: selectedOwner.renta_type_name,
                        renta_type_id: selectedOwner.renta_type_id
                    };
                    initAddCollectionFields(params);
                    openAddCollectionPayment(params, 'personal_use');
                    jQuery('#win-add-collection').window('open');
                }
            },
            {
                id: 'btnaddpersonaluse',
                text: 'Анулиране на транзакция',
                iconCls: 'icon-payments',
                handler: function() {
                    let selectedTransaction = jQuery('#collection-personal-use-table').treegrid('getSelected');
                    if (selectedTransaction === null) {
                        jQuery.messager.alert('Грешка', 'Моля изберете транзакция, която да бъде анулирана.', 'error');
                        return false;
                    }
                    jQuery('#win-cancel-personal-use-collection').window('open');
                }
            }
        ],
        pagination: false,
        rownumbers: true,
        onBeforeLoad: function() {},
        onLoadSuccess: function() {},
        onSelect: function(record) {},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function isValidCollectionToPrint(row, value)
{
    return row.payment_type == 'Сторниране' || (value && value !== undefined && typeof value !== 'undefined');
}

function cancellationPersonalUseCollection()
{
    let selectedTransaction = jQuery('#collection-personal-use-table').treegrid('getSelected');
    TF.Rpc.Collections.CollectionPaymentsGrid.cancellationPersonalUseCollection({
        collection_id: selectedTransaction.collection_id,
        reason:jQuery('#cancel-personal-use-collection-reason').val()
    })
    .done(function(data) {
        jQuery('#win-cancel-personal-use-collection').window('close');
        jQuery.messager.alert('Съобщение', 'Успешно анулиране на транзакция.');
        personalUseCollectionFilter();
    })
    .fail(function(errorObj) {
        jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
        jQuery('#win-cancel-personal-use-collection').window('close');
    });
}

function printPaymentCollection(collectionId, isBankPayment, rkoNum, rkoText) {
    if (isBankPayment === true) {
        return exportBankCollection(collectionId, null , null ,null ,rkoText);
    }

    return exportOrderCollection(collectionId, null, rkoNum);
}

/**
 *
 * @returns {*}
 */
isCollectionPaymentAllowed = ($type) => {
    let selectedRow = null;

    if($type === 2){
        selectedRow = jQuery('#collect-treatments-tables').datagrid('getSelected');
    } else {
        selectedRow = jQuery('#collection-contracts-table').datagrid('getSelected');
    }

    return selectedRow.active;
}

/**
 *
 * @returns {boolean}
 */
isCollectionAmountValid = () => {
    const amount = jQuery('#recieved-amount > input').numberspinner('getValue');

    return parseFloat(amount) > 0 ? true : false
}

hasCollectMoney = () => {
    const selectedSublease = jQuery('#collection-contracts-table').datagrid('getSelected');

    return selectedSublease.collected_money > 0 ? true : false;
}

isValidReversedPaymentAmount = () =>
{
    const reversedAmount = parseFloat(jQuery('#recieved-amount > input').numberspinner('getValue'));
    const selectedSublease = jQuery('#collection-contracts-table').datagrid('getSelected');

    const collectedMoney = parseFloat(selectedSublease.collected_money);

    if (reversedAmount > collectedMoney) {
        return false;
    }

    return true;
}

var collectionFieldData;
function initAddCollectionFields(data = {}) {
    collectionFieldData = data;

    const {
        subleasing_farm_company_address,
        subleasing_farm_id,
        subleasing_farm_mol,
        subleasing_farm_mol_egn,
        c_num,
        farming_id,
        owner_names,
    } = data;

    jQuery('#payment-subjects-row').show();
    jQuery('#payment-subjects-text-row').hide();
    jQuery('#payment-subjects-text').val('Изплащане по договор ' + c_num +' за '  + jQuery('#collections-search-year').combobox('getText'));


    jQuery('#payment-subjects-combobox').combobox({
        url: 'index.php?common-rpc=payment-subjects-combobox',
        valueField: 'id',
        textField: 'name',
        multiple: false,
        editable: false,
        rpcParams: [{
            selected: true
        }],
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    if(data.type === 2){
        jQuery('#payment-subjects-combobox').combobox('select', 1);
    }

    jQuery('#bank-account-row').hide();
    jQuery('#orderer-bank-account-row').hide();

    jQuery('#payment-method-cash > input').change(function() {
        if (jQuery('#payment-method-cash > input').is(':checked') == true) {
            jQuery('label[for="payment-order-checkbox"]').html(
                "Генерирай разходен касов ордер"
            );
            jQuery('#bank-account-row').hide();
            jQuery('#orderer-bank-account-row').hide();
            jQuery('#rkoNumberingFields').show();
            if(!is_reverse_payment) {
                jQuery('#payment-subjects-row').show();
            }

            jQuery('#payment-subjects-text-row').hide();
            jQuery("#payment-date-checkbox").prop("checked", false);
            jQuery('#payment-order-checkbox').prop('checked', true);
            jQuery("#weighing-note-checkbox-money-to-nat").prop('checked',false);
            jQuery('#win-add-collection').window('resize', {
                height: jQuery('#btn-add-collection-payment').offset().top - jQuery('#addPaymentBeginningMarker').offset().top + 72
            });
            jQuery('#win-add-collection').window('center');
            jQuery("#recieved-from-text").val(owner_names);
        }
    });

    jQuery('#payment-method-bank > input').change(function() {
        if (jQuery('#payment-method-bank > input').is(':checked') == true) {
            jQuery('label[for="payment-order-checkbox"]').html(
                "Генерирай платежно нареждане"
            );
            clearRkoNumbers();
            jQuery('#bank-account-row').show();
            jQuery('#orderer-bank-account-row').show();
            jQuery('#payment-order-checkbox').prop('checked', true);
            jQuery('#rkoNumberingFields').hide();
            jQuery('#payment-subjects-row').hide();

            if(is_reverse_payment == false){
                jQuery('#payment-subjects-text-row').show();
            }

            jQuery('#win-add-collection').window('resize', {
                height: jQuery('#btn-add-collection-payment').offset().top - jQuery('#addPaymentBeginningMarker').offset().top + 79
            });
            jQuery('#win-add-collection').window('center');
        }
    });

    if (farming_id == null) {
        jQuery('#payment-orderer-bank-account > input').combobox({
            data: prepareOrdererBankAccountOwnerData(data),
            valueField: 'value',
            textField: 'text',
            multiple: false,
            editable: false,
        });
    } else {
        jQuery('#payment-orderer-bank-account > input').combobox({
            url: 'index.php?farming-rpc=farming-iban',
            valueField: 'value',
            textField: 'text',
            multiple: false,
            editable: false,
            rpcParams: [farming_id],
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }

    jQuery('#payment-bank-account > input').combobox({
        url: 'index.php?farming-rpc=farming-iban',
        valueField: 'value',
        textField: 'text',
        multiple: false,
        editable: false,
        rpcParams: [subleasing_farm_id],
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#payment-recipient > input').val(subleasing_farm_mol);
    jQuery('#payment-order-checkbox').prop('checked', true);
    jQuery('#payment-recipient-egn > input').val(subleasing_farm_mol_egn);
    jQuery('#payment-recipient-address > input').val(subleasing_farm_company_address);
    jQuery('#date-recieved > input').datebox({
        required: true,
        missingMessage: 'Моля въведете дата.',
        value: _todayDate
    });

    jQuery('#payment-method-cash-radio').prop('checked', true);
    jQuery("#payment-order-row").hide();
    jQuery("#recieved-from-text").val(owner_names);
    jQuery("#recieved-from-row").show();
    jQuery('#payment-order > input').val('');
}

function prepareOrdererBankAccountOwnerData(data) {
    let formattedData = [{
        'value': '',
        'text' : '-',
        'selected': true,
    }]

    const paymentData = {
        'name': data.owner_bank_name,
        'iban': data.owner_iban,
        'bic':  data.owner_bic,
    }
    formattedData.push(
        {
            value: JSON.stringify(paymentData),
            text:  data.owner_bank_name + ': ' + data.owner_iban,
        }
    );

    return formattedData
}

function clearRkoNumbers() {
    jQuery('#rko-numbering').html('');
    jQuery('#payment-order-checkbox').prop('checked', false);
    jQuery('#payment-order-checkbox-money-to-nat').prop('checked', false);
}

function openAddCollectionPaymentReversal() {
    var selectedSublease = jQuery('#collection-contracts-table').datagrid('getSelected');

    jQuery('#amount-to-recieve').html(selectedSublease.money_to_collect_txt);
    jQuery('#amount-recieved').html(selectedSublease.collected_money_txt);
    jQuery('#amount-recieved-row').show();
    jQuery('#gerate-payment-order-row').hide();
    jQuery('#payment-subjects-row').hide();
    jQuery('#payment-subjects-text-row').hide();
    clearRkoNumbers();


    jQuery('#recieved-amount-label').html('Сума за сторниране');
    jQuery('#win-add-collection').window('setTitle', 'Сторниране');
    jQuery('#btn-add-collection-payment > a').linkbutton({
        text: 'Сторнирай'
    });

    is_reverse_payment = true;

    jQuery('#recieved-amount > input').numberspinner({
        value: '0',
        min: 0,
        precision: 2
    });
}

function openAddCollectionPayment(selectedSublease, type = 'rents') {
    jQuery('#amount-to-recieve').html(selectedSublease.unpaid ? selectedSublease.unpaid_txt : '');
    jQuery('#amount-recieved-row').hide();
    jQuery('#gerate-payment-order-row').show();

    jQuery('#recieved-amount-label').html('Получена сума');
    jQuery('#btn-add-collection-payment > a').linkbutton({
        text: 'Изплати'
    });

    if(type === 'rents'){
        jQuery('#win-add-collection').window('setTitle', 'Изплащане');
        jQuery('#addPaymentBeginningMarker').html('Информация за плащане');
        jQuery('#person-name-label').html('Име получател');
        jQuery('#person-egn-label').html('ЕГН на получател');
        jQuery('#person-address-label').html('Адрес на получател');
        jQuery('#person-lk-label').html('Лична карта получател');
        jQuery('#date-label').html('Изплатено на:');
        jQuery('#subjects-label').html('Основание в РКО');
    } else if(type === 'personal_use') {
        jQuery('#win-add-collection').window('setTitle', 'Внасяне');
        jQuery('#addPaymentBeginningMarker').html('Информация за внесена сума');
        jQuery('#person-name-label').html('Име на вносител');
        jQuery('#person-egn-label').html('ЕГН на вносител');
        jQuery('#person-address-label').html('Адрес на вносител');
        jQuery('#person-lk-label').html('Лична карта вносител');
        jQuery('#date-label').html('Получено на:');
        jQuery('#subjects-label').html('Основание в ПКО');
    }

    is_reverse_payment = false;

    jQuery('#recieved-amount > input').numberspinner({
        value: selectedSublease.unpaid,
        min: 0,
        max: parseFloat(selectedSublease.unpaid),
        precision: 2
    });
}

jQuery(function() {
    jQuery('#btn-add-collection-payment > a').bind('click', function() {
        if (!isCollectionPaymentAllowed(collectionFieldData.type)) {
            messagerCollectionsPaymentsRights();
            return;
        }

        if (!isCollectionAmountValid()) {
            return  jQuery.messager.alert('Грешка', 'Не може да се запази плащане със сума 0лв. Моля въведете сума по-голяма от нула.', 'warning');
        }

        if (is_reverse_payment) {
            if (!isValidReversedPaymentAmount()) {
                return  jQuery.messager.alert('Грешка', 'Сумата за сторниране не може да надвишава изплатената сума.', 'warning');
            }
        }

        if (hasCollectionsRightsRW) {
            const selectedSublease = collectionFieldData;
            let paymentData = generateCollectionPaymentData(selectedSublease);

            const rpcParams = {
                sublease_id: selectedSublease.id,
                amount: jQuery('#recieved-amount > input').numberspinner('getValue'),
                is_reverse_payment: is_reverse_payment,
                recieved_from: jQuery('#recieved-from > input').val(),
                date_recieved: jQuery('#date-recieved > input').datebox('getValue'),
                bank_payment: jQuery("#payment-method-bank-radio").prop('checked'),
                payment_order: jQuery("#payment-order > input").val(),
                payment_order_generate: jQuery('#payment-order-checkbox').is(':checked'),
                farming_year: selectedSublease.farming_year,
                payment_data: paymentData,
                type: selectedSublease.type,
            };

            TF.Rpc.Collections.CollectionPaymentsGrid.addCollectionPayment(rpcParams)
            .done(function(data) {
                jQuery('#win-add-collection').window('close');
                
                selectedContractRowId = selectedSublease.id;

                if(selectedSublease.type === 2){
                    jQuery('#collect-treatments-tables').datagrid('reload');
                } else {
                    jQuery('#collection-contracts-table').datagrid('reload');
                }

                completeSavePayment(data);
            })
            .fail(function(errorObj) {
            });
        } else {
            messagerCollectionsWriteRights();
        }
    });

    jQuery('#payment-method-cash-radio').change(function() {
        processCheckBoxPaymentOrder();
    });

    jQuery('#payment-method-bank-radio').change(function() {
        processCheckBoxPaymentOrder();
    });
});

function generateCollectionPaymentData(selectedSublease) {
    let farmingData = jQuery("#payment-orderer-bank-account > input").combobox("getValue");
    let subleasingFarmPaymentData = jQuery("#payment-bank-account > input").combobox("getValue");
    let recipientCompanyName = selectedSublease.subleasing_farm_company;

    if (farmingData) {
        farmingData = JSON.parse(farmingData);
    }

    if(subleasingFarmPaymentData){
        subleasingFarmPaymentData = JSON.parse(subleasingFarmPaymentData);
    }

    const data = {
        payment_method_cash: jQuery('#payment-method-cash > input').is(':checked'),
        payment_method_bank: jQuery('#payment-method-bank > input').is(':checked'),
        recipient: jQuery('#payment-recipient > input').val(),
        recipient_company: recipientCompanyName,
        recipient_egn: jQuery('#payment-recipient-egn > input').val(),
        recipient_proxy: jQuery('#payment-recipient-proxy > input').val(),
        recipient_address: jQuery('#payment-recipient-address-text').val(),
        recipient_lk: jQuery('#payment-recipient-lk-text').val(),
        rko_number: jQuery("#payment-subjects-combobox").combobox("getValue"),
        rko_text: jQuery("#payment-subjects-text").val(),
        orderer_iban: farmingData,
        subleasing_farm_iban: subleasingFarmPaymentData,
        owner_id: selectedSublease.owner_id,
        owner_names: selectedSublease.owner_names,
        farming_id: selectedSublease.farming_id,
        subleasing_farm_id: selectedSublease.subleasing_farm_id,
        renta_type_id: selectedSublease.renta_type_id,
        renta_type_name: selectedSublease.renta_type_name,
    };

    return data;
}


function generatePaymentDocument(data) {
    var orderWithDate = true,
        rko = false

    if (data) {
        const collection_id = data.collection_id

        if (jQuery("#payment-date-checkbox").prop("checked")) {
            orderWithDate = false;
        }


        if (data.payment_type == "cash") {
            if (jQuery("#payment-order-checkbox").prop("checked")) {
                rko = true;
            }

            paymentSubjectData = jQuery("#payment-subjects-combobox").combobox(
                "getValue"
            );
            paymentSubjectText = null;

            if (rko) {
                exportOrderCollection(collection_id, orderWithDate, paymentSubjectData, paymentSubjectText)
            }
        } else if (data.payment_type == "bank") {

            const farmingIban = jQuery("#payment-orderer-bank-account > input").combobox("getValue");
            paymentSubjectData = null;
            paymentSubjectText = jQuery("#payment-subjects-text").val();

            exportBankCollection(collection_id, farmingIban, orderWithDate, paymentSubjectData, paymentSubjectText);

        }
    }
}

function exportBankCollection(collection_id, farmingIban = null, orderWithDate = null, paymentSubjectData = null, paymentSubjectText = null) {
    const winDownload = jQuery("#win-download")
    const downloadFile = jQuery("#btn-download-file");

    TF.Rpc.Collections.ExportCollection.exportToPdfBankCollectionOrder(
        collection_id,
        orderWithDate,
        farmingIban,
        paymentSubjectData,
        paymentSubjectText
    )
        .done(function(dataObj) {
            winDownload.window("open");
            var path = dataObj.file_path;
            _pathFile = path;
            _fileName = dataObj.file_name;
            downloadFile.attr("href", path);
        })
        .fail(function(errorObj) {});

    return;
}

function exportOrderCollection(collection_id, orderWithDate = null, paymentSubjectData = null, paymentSubjectText = null) {
    const winDownload = jQuery("#win-download")
    const downloadFile = jQuery("#btn-download-file");
    TF.Rpc.Collections.ExportCollection.exportToPdfCollectionOrder(
        collection_id,
        orderWithDate,
        paymentSubjectData,
        paymentSubjectText
    ).done(function(dataObj) {
        winDownload.window("open");
        var path = dataObj.file_path;
        _pathFile = path;
        _fileName = dataObj.file_name;
        downloadFile.attr("href", path);
    });
    return;
}

function completeSavePayment(data) {
    generatePaymentDocument(data);
}

function processCheckBoxPaymentOrder() {

    if (jQuery("#payment-method-cash-radio").prop('checked')) {
        jQuery("#payment-order-row").hide();
        jQuery("#recieved-from-row").show();
        jQuery('#payment-order > input').val('');
        jQuery("#payment-orderer-bank-account > input").combobox("clear");
        jQuery("#payment-bank-account > input").combobox("clear");
    } else {
        jQuery("#payment-order-row").show();
    }
}
