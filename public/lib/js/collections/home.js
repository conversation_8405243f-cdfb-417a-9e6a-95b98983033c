var ComboboxData;
var currentFarmingYear = null;
var selectedTabIndex = null;

jQuery(function() {
    initSearchOnEnter();
    initRentaTypesCombobox();
    setUserLastLogin();

    TF.Rpc.Common.CombinedComboboxData.read(null, null, {selected: 'current'})
        .done(function (data) {
			ComboboxData = data;
			jQuery(window).trigger('comboboxdataloaded', [data]);
            initComboboxItems();
        })
        .fail(function (error) {
            jQuery.messager.alert('Грешка', error.getMessage(), 'warning');
        });
    jQuery('#btn-open-contracts-filter').bind('click', function() {
        jQuery('#win-collections-filter').window('open');
    });
    jQuery('#btn-clear-contracts-filter').bind('click', function() {
        cleanCollectionsFilter();
    });
    jQuery('#btn-print-contracts').bind('click', function() {
        var obj = getCollectionContractsFilters();
        TF.Rpc.Collections.CollectionsContractsGrid.printCollectionContracts(obj)
            .done(function(data) {
                jQuery('#btn-download-file').attr('href', data);
                jQuery('#win-download').window('open');
            })
            .fail(function(errorObj) {
                jQuery.messager.alert('Грешка', errorObj.getMessage());
            });
    });
    jQuery('#btn-export-contracts').bind('click', function() {
        // Export Rents
        var obj = getCollectionContractsFilters();
        TF.Rpc.Collections.CollectionsContractsGrid.exportCollectionContracts(obj)
            .done(function(data) {
                jQuery('#btn-download-file').attr('href', data);
                jQuery('#win-download').window('open');
            })
            .fail(function(errorObj) {
                jQuery.messager.alert('Грешка', errorObj.getMessage());
            });
    });

    jQuery("#btn-add-nat-payment > a").bind("click", function() {
        validatePersonalUsePayment();
    });

    jQuery('#search-date-from  > input').datebox();
    jQuery('#search-date-to  > input').datebox();
    jQuery('#search-due-date-from  > input').datebox();
    jQuery('#search-due-date-to  > input').datebox();

    jQuery('#btn-filter-subleases').bind('click', function() {
        var obj = getCollectionContractsFilters();
        //set selected id to undefined
        selectedContractRowId = undefined;
        jQuery('#collection-contracts-table').datagrid('options').rpcParams = [obj];
        jQuery('#collection-contracts-table').datagrid('reload');
        jQuery('#win-collections-filter').window('close');
    });

    jQuery('#btn-pay').bind('click', function() {
        if (isCollectionPaymentAllowed()) {
            const selectedSublease = jQuery('#collection-contracts-table').datagrid('getSelected');

            selectedSublease.type = 1;
            initAddCollectionFields(selectedSublease);
            openAddCollectionPayment(selectedSublease);

            jQuery('#win-add-collection').window('open');
        } else {
           messagerCollectionsPaymentsRights();
        }


    });

    jQuery('#btn-unpay').bind('click', function() {
        if(hasCollectMoney()){
            const selectedSublease = jQuery('#collection-contracts-table').datagrid('getSelected');
            selectedSublease.type = 1;
            initAddCollectionFields(selectedSublease);
            openAddCollectionPaymentReversal();

            jQuery('#win-add-collection').window('open');
        } else {
            return  jQuery.messager.alert('Грешка', 'Няма изплатена сума за сторниране.', 'warning');
        }
    });

    jQuery('#btn-pu-open-contracts-filter').bind('click', function() {
        jQuery('#win-personal-use-filters').window('open');
    });
    jQuery('#btn-pu-clear-contracts-filter').bind('click', function() {
        clearPersonalUseCollectionFilter();
    });
    jQuery('#btn-pu-print-contracts').bind('click', function() {
        var obj = getCollectionContractsFilters();
        TF.Rpc.Collections.CollectionsContractsGrid.printCollectionContracts(obj)
            .done(function(data) {
                jQuery('#btn-download-file').attr('href', data);
                jQuery('#win-download').window('open');
            })
            .fail(function(errorObj) {
                jQuery.messager.alert('Грешка', errorObj.getMessage());
            });
    });
    jQuery('#btn-pu-export-contracts').bind('click', function() {
        // Export Personal Use
        let obj = getPersonalUseCollectionFilter();
        TF.Rpc.Collections.CollectionsContractsGrid.exportCollectionPersonalUse(obj)
            .done(function(data) {
                jQuery('#btn-download-file').attr('href', data);
                jQuery('#win-download').window('open');
            })
            .fail(function(errorObj) {
                jQuery.messager.alert('Грешка', errorObj.getMessage());
            });
    });
    jQuery('#collections-payments-tabs').tabs('disableTab', 1); //Disable tab Personal use on init
    jQuery("#collections-tabs").tabs({
        onSelect: function(title, index) {
            selectedTabIndex = index;
            if(index === 0) { //Rents
                jQuery('#collections-payments-tabs').tabs('disableTab', 1);
                jQuery('#collections-payments-tabs').tabs('enableTab', 0);
                jQuery('#collections-payments-tabs').tabs('select', 0);
            } else if(index === 1) { //Personal use
                jQuery('#collections-payments-tabs').tabs('disableTab', 0);
                jQuery('#collections-payments-tabs').tabs('enableTab', 1);
                jQuery('#collections-payments-tabs').tabs('select', 1);
                initCollectTreatments({pu_farming_year: jQuery('#pu-collections-search-year').combobox('getValue')});
            }
        }
    });
});

function initRentaTypesCombobox() {
    jQuery('#search-renta-nat-types').combobox({
        editable: false,
        url: 'index.php?common-rpc=renta-types-combobox',
        valueField: 'id',
        textField: 'name',
        rpcParams: [{
            allValuesOption: true,
            typeTextLabel: true
        }],
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function getPersonalUseCollectionFilter(){
    return {
        pu_farming_year: jQuery('#pu-collections-search-year').combobox('getValue'),
        c_num: jQuery('#search-personal-use-c-num').val(),
        c_id: jQuery('#search-personal-use-c-id').val(),
        owner_names: jQuery('#search-personal-use-owner-name').val(),
        egn_eik: jQuery('#search-personal-use-owner-egn').val(),
        renta_type: jQuery('#search-renta-nat-types').combobox('getValue')
    }
}

function personalUseCollectionFilter(){
    const filterParams = getPersonalUseCollectionFilter();
    initCollectTreatments(filterParams);
    jQuery('#win-personal-use-filters').window('close');
}
function clearPersonalUseCollectionFilter(){
    jQuery('#search-renta-nat-types').combobox('select', '-1'); // select Всички култури
    jQuery('#search-personal-use-c-num').val('');
    jQuery('#search-personal-use-c-id').val('');
    jQuery('#search-personal-use-owner-name').val('');
    jQuery('#search-personal-use-owner-egn').val('');
    jQuery('#pu-collections-search-year').combobox('select', currentFarmingYear);
    personalUseCollectionFilter();
}

function cleanCollectionsFilter() {

    jQuery('#search-sublease-num > input').val('');
    jQuery('#search-sublease-complete-match').prop('checked', true);
    jQuery('#search-type > input').combobox('reset');
    jQuery('#search-sublease-status').combobox('reset');
    jQuery('#search-farming > input').combobox('reset');
    jQuery('#collections-search-year').combobox('select', currentFarmingYear);
    jQuery('#search-renta-types').combobox('reset');
    jQuery('#search-date-from > input').datebox('reset');
    jQuery('#search-date-to > input').datebox('reset');
    jQuery('#search-due-date-from > input').datebox('reset');
    jQuery('#search-due-date-to > input').datebox('reset');

    jQuery('#search-kad-ident').val('');
    jQuery('#search-ekatte').combobox('reset');
    jQuery('#search-masiv').val('');
    jQuery('#search-number').val('');
    jQuery('#search-category').combobox('reset');
    jQuery('#search-area-type').combobox('reset');
    jQuery('#search-irrigated-area').combobox('reset');
    jQuery('#search-subleaser-name').val('');
    jQuery('#search-subleaser-egn').val('');
    jQuery('#search-represent-name').val('');
    jQuery('#search-represent-egn').val('');
    jQuery('#search-company-name').val('');
    jQuery('#search-company-eik').val('');
    jQuery('#search-person-name').val('');
    jQuery('#search-person-egn').val('');

    var obj = getCollectionContractsFilters();
    //reinit subleasesTable with query params
    //set selected id to undefined
    selectedContractRowId = undefined;
    jQuery('#collection-contracts-table').datagrid({
        rpcParams: [obj],
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    return false;
}

function getCollectionContractsFilters() {
    var obj = {};

    obj.c_num = jQuery('#search-sublease-num > input').val();
    obj.c_num_complete_match = false;
    if (jQuery('#search-sublease-complete-match').is(':checked')) {
        obj.c_num_complete_match = true;
    }
    obj.c_type = jQuery('#search-type > input').combobox('getValues');
    obj.c_status = jQuery('#search-sublease-status').combobox('getValue');
    obj.farming = jQuery('#search-farming > input').combobox('getValues');
    obj.collections_search_year = jQuery('#collections-search-year').combobox('getValue');
    if(obj.collections_search_year == '') {
        obj.collections_search_year = currentFarmingYear;
    }
    obj.renta_types = jQuery('#search-renta-types').combobox('getValues');
    obj.date_from = jQuery('#search-date-from > input').datebox('getValue');
    obj.date_to = jQuery('#search-date-to > input').datebox('getValue');
    obj.due_date_from = jQuery('#search-due-date-from > input').datebox('getValue');
    obj.due_date_to = jQuery('#search-due-date-to > input').datebox('getValue');

    obj.kad_ident = jQuery('#search-kad-ident').val();
    obj.ekate = jQuery('#search-ekatte').combobox('getValues');
    obj.masiv = jQuery('#search-masiv').val();
    obj.number = jQuery('#search-number').val();
    obj.category = jQuery('#search-category').combobox('getValues');
    obj.ntp = jQuery('#search-area-type').combobox('getValues');
    obj.irrigated_area = jQuery('#search-irrigated-area').combobox('getValue');
    obj.owner_name = jQuery('#search-subleaser-name').val();
    obj.owner_egn = jQuery('#search-subleaser-egn').val();
    obj.rep_name = jQuery('#search-represent-name').val();
    obj.rep_egn = jQuery('#search-represent-egn').val();
    obj.company_name = jQuery('#search-company-name').val();
    obj.company_eik = jQuery('#search-company-eik').val();
    obj.person_name = jQuery('#search-person-name').val();
    obj.person_egn = jQuery('#search-person-egn').val();

    return obj;
}

function initComboboxItems() {
    var contractTypeComboboxData   = ComboboxData.ContractTypeCombobox,
        farmingComboboxData        = ComboboxData.FarmingCombobox,
        contractStatusComboboxData = ComboboxData.ContractStatusCombobox,
        ekateComboboxData          = ComboboxData.EkateCombobox,
        irrigatedAreaComboboxData  = ComboboxData.IrrigatedAreaCombobox,
        categoryComboboxData       = ComboboxData.PlotCategoryCombobox,
        plotNTPComboboxData        = ComboboxData.PlotNTPCombobox,
        farmingYearComboboxData    = ComboboxData.FarmingYearCombobox,
        newFarmingYearComboboxData = [];

    jQuery('#search-type > input').combobox({
        data: contractTypeComboboxData,
        editable: false,
        valueField: 'id',
        textField: 'name',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-farming > input').combobox({
        data: farmingComboboxData,
        editable: false,
        valueField: 'id',
        textField: 'name',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-sublease-status').combobox({
        data: contractStatusComboboxData,
        editable: false,
        valueField: 'id',
        textField: 'name',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-ekatte').combobox({
        data: ekateComboboxData,
        valueField: 'ekate',
        textField: 'text',
        multiple: true,
        onSelect: onComboMultiSelect,
        onHidePanel: onHidePanelMultiSelect,
        onLoadSuccess: function() {
            jQuery('#search-plot-ekate > input').combobox({
                data: jQuery('#search-ekatte').combobox('getData'),
                valueField: 'ekate',
                textField: 'text',
                filter: function(q, row) {
                    var opts = jQuery(this).combobox('options');
                    var text = row[opts.textField].toLowerCase();
                    var value = row[opts.valueField];
                    var find = q.toLowerCase();
                    if (text.indexOf(find) != -1 || value.indexOf(find) != -1) {
                        return true;
                    }
                }
            });
        },
        filter: function(q, row) {
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if (text.indexOf(find) != -1 || value.indexOf(find) != -1) {
                return true;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    irrigatedAreaComboboxData[0].selected  = true;
    jQuery('#search-irrigated-area').combobox({
        data: irrigatedAreaComboboxData,
        editable: false,
        valueField: 'value',
        textField: 'label',
        multiple: false,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-category').combobox({
        data: categoryComboboxData,
        valueField: 'id',
        textField: 'name',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-renta-types').combobox({
        url: 'index.php?common-rpc=renta-types-combobox',
        rpcParams: [{
            as_list: true
        }],
        valueField: 'id',
        textField: 'name',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-area-type').combobox({
        data: plotNTPComboboxData,
        editable: false,
        valueField: 'id',
        textField: 'name',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    farmingYearComboboxData.forEach(function (el) {
        if (el.id !== '') {
            newFarmingYearComboboxData.push(el);
        }
    });

    jQuery('#collections-search-year').combobox({
        data: newFarmingYearComboboxData,
        valueField: 'id',
        textField: 'farming_year',
        editable: false,
        onSelect: function(option) {
            const obj = getCollectionContractsFilters();
            // on select event is called before the value of selected option in combobox is updated
            // jQuery('#collections-search-year').combobox('getValue') value is not same as option.id
            // set it with current selected value
            obj.collections_search_year = option.id;

            jQuery('#collection-contracts-table').datagrid({
                rpcParams: [obj],
                loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
            });
        },
        onLoadSuccess: function() {
            currentFarmingYear = jQuery('#collections-search-year').combobox('getValue');
            initCollectionsContracts();
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#pu-collections-search-year').combobox({
        data: newFarmingYearComboboxData,
        valueField: 'id',
        textField: 'farming_year',
        editable: false,
        onSelect: function(option) {
            let GET = getQueryParams();
            if (selectedTabIndex === 1) { 
                // Personal use tab is selected
                if(GET.c_id && GET.fy){
                    jQuery('#pu-collections-search-year').combobox('select', GET.fy);
                    jQuery("#collections-tabs").tabs('select', 1);
                    jQuery('#search-personal-use-c-id').val(GET.c_id);
                    jQuery('#search-personal-use-owner-name').val(GET.owner_names);
                    initRentaTypesCombobox();
                    personalUseCollectionFilter();
                } else {
                    initRentaTypesCombobox();
                    initCollectTreatments({pu_farming_year: option.id});
                }
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initSearchOnEnter() {
    jQuery("#win-collections-filter").off("keyup").on("keyup", function (event) {
		// Listen for enter key
        if (13 !== event.keyCode) {
			return;
		}
		jQuery("#btn-filter-subleases").click()
    });
}
