(function(root, factory) {
    if (typeof define === "function" && define.amd) {
        // AMD. Register as an anonymous module.
        define([
            "jquery",
            "tinyMCE",
            "js/main/EasyUIRPCLoaders",
            "js/utilities/payment-subjects",
            "TF/Rpc/Common/TemplatesGrid",
            "TF/Rpc/Common/ContractTemplateVariables",
            "TF/Rpc/Contracts/ContractsExports",
            "TF/Rpc/SalesContracts/SalesContractsExports",
            "",
            "easyui"
        ], factory);
    } else {
        // Browser globals (root is window)
        root.contracts_templates = factory(
            jQuery,
            tinyMCE,
            EasyUIRPCLoaders,
            payment_subjects,
            TF.Rpc.Common.TemplatesGrid,
            TF.Rpc.Common.ContractTemplateVariables,
            TF.Rpc.Contracts.ContractsExports,
            TF.Rpc.SalesContracts.SalesContractsExports,
            undefined
        );
    }
})(typeof self !== "undefined" ? self : this, function(
    jQuery,
    tinyMCE,
    EasyUIRPCLoaders,
    payment_subjects,
    TemplatesGrid,
    ContractTemplateVariables,
    ContractsExports,
    SalesContractsExports,
    ComboboxData
) {
    var editTemplateRequested = false;
    var _fileName;
    var lastSelectedTemplate = null;

    function init() {
        initTinyMCE();
        payment_subjects.onClosePlotsDetailedWindow();
        payment_subjects.onCloseRentaNaturaDetailedWindow();

        jQuery("#js-contract-templates-save-btn").on(
            "click",
            validateSaveTemplateRouter
        );
    }

    function initTemplatesGrid() {
        jQuery("#templates-tables").datagrid({
            iconCls: "icon-template",
            nowrap: true,
            autoRowHeight: true,
            striped: true,
            pageSize: 50,
            fit: true,
            fitColumns: true,
            showFooter: true,
            url: "index.php?common-rpc=templates-grid",
            rpcParams: [{}],
            sortName: "id",
            sortOrder: "desc",
            border: false,
            idField: "id",
            singleSelect: true,
            frozenColumns: [
                [
                    {
                        field: "ck",
                        checkbox: true
                    }
                ]
            ],
            columns: [
                [
                    {
                        field: "title",
                        title: "<b>Име</b>",
                        sortable: true,
                        width: 150
                    },
                    {
                        field: "add_date",
                        title: "<b>Дата на добавяне</b>",
                        sortable: true,
                        width: 150
                    }
                ]
            ],
            pagination: true,
            rownumbers: true,
            toolbar: [
                {
                    id: "btnaddtemplate",
                    text: "Добавяне",
                    iconCls: "icon-add",
                    handler: function() {
                        editTemplateRequested = false;
                        setTemplateVariables();
                        initAddEditTemplateFields();
                        jQuery("#win-add-edit-template").window("open");
                    }
                },
                {
                    id: "btnedittemplate",
                    text: "Редактиране",
                    iconCls: "icon-edit",
                    handler: function() {
                        var getChecked = jQuery("#templates-tables").datagrid(
                            "getChecked"
                        );

                        if (getChecked[0]) {
                            editTemplateRequested = true;

                            TemplatesGrid.markForEdit(getChecked[0].id)
                                .done(function(data) {
                                    jQuery("#win-add-edit-template").window(
                                        "open"
                                    );
                                    setTemplateVariables(data);
                                })
                                .fail(function(data) {
                                    RpcErrorHandler.show(data);
                                });
                        } else {
                            jQuery.messager.alert(
                                "Грешка",
                                "Моля изберете бланка, която искате да редактирате!"
                            );
                        }
                    }
                },
                {
                    id: "btndeletetemplate",
                    text: "Изтриване",
                    iconCls: "icon-delete",
                    handler: function() {
                        var getChecked = jQuery("#templates-tables").datagrid(
                            "getChecked"
                        );

                        if (getChecked[0]) {
                            jQuery.messager.confirm(
                                "Потвърждение",
                                "Сигурни ли сте, че искате да премахнете тази бланка?",
                                function(r) {
                                    if (r) {
                                        TemplatesGrid.delete(getChecked[0].id)
                                            .done(function(data) {
                                                jQuery(
                                                    "#templates-tables"
                                                ).datagrid("loadRpc");
                                            })
                                            .fail(function(data) {
                                                RpcErrorHandler.show(data);
                                            });
                                    }
                                }
                            );
                        } else {
                            jQuery.messager.alert(
                                "Грешка",
                                "Моля изберете бланка, която да бъде премахната!"
                            );
                        }
                    }
                }
            ],
            onBeforeLoad: function() {
                jQuery("#templates-tables").datagrid("clearChecked");
            },
            onLoadSuccess: function() {
                if (
                    !jQuery("#template-variables > input")
                        .data()
                        .hasOwnProperty("combobox")
                ) {
                    initAddEditTemplateFields();
                }
            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }

    function initPrintTemplatesGrid(
        contract_id,
        contractsData,
        parrent_id,
        attributes,
        page = 1
    ) {
        jQuery("#templates-tables").datagrid({
            iconCls: "icon-template",
            nowrap: true,
            autoRowHeight: true,
            striped: true,
            pageSize: 50,
            fit: true,
            fitColumns: true,
            showFooter: true,
            url: "index.php?common-rpc=templates-grid",
            rpcParams: [{}],
            sortName: "id",
            sortOrder: "asc",
            border: false,
            idField: "id",
            singleSelect: true,
            frozenColumns: [
                [
                    {
                        field: "ck",
                        checkbox: true
                    }
                ]
            ],
            columns: [
                [
                    {
                        field: "title",
                        title: "<b>Име</b>",
                        sortable: true,
                        width: 150
                    },
                    {
                        field: "add_date",
                        title: "<b>Дата на добавяне </b>",
                        sortable: true,
                        width: 150
                    }
                ]
            ],
            pagination: true,
            rownumbers: true,
            toolbar: [
                {
                    id: "btnexportpdfblank",
                    text: "Отпечатай pdf",
                    iconCls: "icon-pdf",
                    handler: function() {
                        var getChecked = jQuery("#templates-tables").datagrid(
                            "getChecked"
                        );

                        if (getChecked[0]) {
                            var obj = {
                                template_id: getChecked[0].id,
                                contract_id: contract_id,
                                contractsData: contractsData,
                                parrent_id: parrent_id,
                                attributes: attributes,
                                blank_type: "pdf"
                            };
                            if(contractsData && contractsData.length > 0) {
                                jQuery.messager.confirm('Потвърждение', 'Страница ' + page + ' ще бъде разпечатана. Сигурни ли сте, че искате да продължите?', function (r) {
                                    if (r) {
                                        ContractsExports.exportContractBlank(obj)
                                            .done(function(data) {
                                                createContractDownloadVariables();
                                                winDownloadContract.window("open");
                                                _pathFile = data.file_path;
                                                _fileName = data.file_name;
                                                downloadFileContract.attr(
                                                    "href",
                                                    _pathFile
                                                );
                                            })
                                            .fail(function(errorObj) {});
                                    }
                                });
                            } else {
                                ContractsExports.exportContractBlank(obj)
                                    .done(function(data) {
                                        createContractDownloadVariables();
                                        winDownloadContract.window("open");
                                        _pathFile = data.file_path;
                                        _fileName = data.file_name;
                                        downloadFileContract.attr(
                                            "href",
                                            _pathFile
                                        );
                                    })
                                    .fail(function(errorObj) {});
                            }

                        } else {
                            jQuery.messager.alert(
                                "Грешка",
                                "Моля изберете бланка, върху която да разпечатате!"
                            );
                        }
                    }
                },
                {
                    id: "btnexportdocblank",
                    text: "Отпечатай Word",
                    iconCls: "icon-word",
                    handler: function() {
                        var getChecked = jQuery("#templates-tables").datagrid(
                            "getChecked"
                        );

                        if (getChecked[0]) {
                            var obj = {
                                template_id: getChecked[0].id,
                                contract_id: contract_id,
                                contractsData: contractsData,
                                blank_type: "doc"
                            };

                            if(contractsData && contractsData.length > 0) {
                                jQuery.messager.confirm('Потвърждение', 'Страница ' + page + ' ще бъде разпечатана. Сигурни ли сте, че искате да продължите?', function (r) {
                                    if (r) {
                                        ContractsExports.exportContractBlank(obj)
                                            .done(function(data) {
                                                createContractDownloadVariables();
                                                winDownloadContract.window("open");
                                                _pathFile = data.file_path;
                                                _fileName = data.file_name;
                                                downloadFileContract.attr(
                                                    "href",
                                                    _pathFile
                                                );
                                            })
                                            .fail(function(errorObj) {});
                                    }
                                });
                            } else {
                                ContractsExports.exportContractBlank(obj)
                                    .done(function (data) {
                                        createContractDownloadVariables();
                                        winDownloadContract.window("open");
                                        _pathFile = data.file_path;
                                        _fileName = data.file_name;
                                        downloadFileContract.attr(
                                            "href",
                                            _pathFile
                                        );
                                    })
                                    .fail(function (errorObj) {
                                    });
                            }
                        } else {
                            jQuery.messager.alert(
                                "Грешка",
                                "Моля изберете бланка, върху която да разпечатате!"
                            );
                        }
                    }
                },
                {
                    id: "btnedittemplate",
                    text: "Редактиране",
                    iconCls: "icon-edit",
                    handler: function() {
                        var getChecked = jQuery("#templates-tables").datagrid(
                            "getChecked"
                        );

                        if (getChecked[0]) {
                            editTemplateRequested = true;

                            TemplatesGrid.markForEdit(getChecked[0].id)
                                .done(function(data) {
                                    jQuery("#win-add-edit-template").window(
                                        "open"
                                    );
                                    setTemplateVariables(data);
                                })
                                .fail(function(data) {
                                    RpcErrorHandler.show(data);
                                });
                        } else {
                            jQuery.messager.alert(
                                "Грешка",
                                "Моля изберете бланка, която искате да редактирате!"
                            );
                        }
                    }
                }
            ],
            onBeforeLoad: function() {
                jQuery("#templates-tables").datagrid("clearChecked");
            },
            onLoadSuccess: function() {
                if (
                    !jQuery("#template-variables > input")
                        .data()
                        .hasOwnProperty("combobox")
                ) {
                    initAddEditTemplateFields();
                }
            },
            onSelect: function(index, row) {
                lastSelectedTemplate = index;
            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
        if (lastSelectedTemplate !== null) {
            jQuery("#templates-tables").datagrid(
                "selectRow",
                lastSelectedTemplate
            );
        }
    }

    function initPrintTemplatesSalesContracts(sale_contract_id) {
        jQuery("#templates-tables").datagrid({
            iconCls: "icon-template",
            nowrap: true,
            autoRowHeight: true,
            striped: true,
            pageSize: 50,
            fit: true,
            fitColumns: true,
            showFooter: true,
            url: "index.php?common-rpc=templates-grid",
            rpcParams: [{}],
            sortName: "id",
            sortOrder: "asc",
            border: false,
            idField: "id",
            singleSelect: true,
            frozenColumns: [
                [
                    {
                        field: "ck",
                        checkbox: true
                    }
                ]
            ],
            columns: [
                [
                    {
                        field: "title",
                        title: "<b>Име</b>",
                        sortable: true,
                        width: 150
                    },
                    {
                        field: "add_date",
                        title: "<b>Дата на добавяне</b>",
                        sortable: true,
                        width: 150
                    }
                ]
            ],
            pagination: true,
            rownumbers: true,
            toolbar: [
                {
                    id: "btnexportpdfblank",
                    text: "Отпечатай pdf",
                    iconCls: "icon-pdf",
                    handler: function() {
                        var getChecked = jQuery("#templates-tables").datagrid(
                            "getChecked"
                        );

                        if (getChecked[0]) {
                            var obj = {
                                template_id: getChecked[0].id,
                                sale_contract_id: sale_contract_id,
                                blank_type: "pdf"
                            };
                            SalesContractsExports.exportSaleContractBlank(obj)
                                .done(function(data) {
                                    createSalesContractDownloadVariables();
                                    winDownloadContract.window("open");
                                    var path = data.file_path;
                                    _pathFile = path;
                                    _fileName = data.file_name;
                                    downloadFileContract.attr("href", path);
                                })
                                .fail(function(errorObj) {});
                        } else {
                            jQuery.messager.alert(
                                "Грешка",
                                "Моля изберете бланка, върху която да разпечатате!"
                            );
                        }
                    }
                },
                {
                    id: "btnexportdocblank",
                    text: "Отпечатай Word",
                    iconCls: "icon-word",
                    handler: function() {
                        var getChecked = jQuery("#templates-tables").datagrid(
                            "getChecked"
                        );

                        if (getChecked[0]) {
                            var obj = {
                                template_id: getChecked[0].id,
                                sale_contract_id: sale_contract_id,
                                blank_type: "doc"
                            };
                            SalesContractsExports.exportSaleContractBlank(obj)
                                .done(function(data) {
                                    createSalesContractDownloadVariables();
                                    winDownloadContract.window("open");
                                    var path = data.file_path;
                                    _pathFile = path;
                                    _fileName = data.file_name;
                                    downloadFileContract.attr("href", path);
                                })
                                .fail(function(errorObj) {});
                        } else {
                            jQuery.messager.alert(
                                "Грешка",
                                "Моля изберете бланка, върху която да разпечатате!"
                            );
                        }
                    }
                },
                {
                    id: "btnedittemplate",
                    text: "Редактиране",
                    iconCls: "icon-edit",
                    handler: function() {
                        var getChecked = jQuery("#templates-tables").datagrid(
                            "getChecked"
                        );

                        if (getChecked[0]) {
                            editTemplateRequested = true;

                            TemplatesGrid.markForEdit(getChecked[0].id)
                                .done(function(data) {
                                    jQuery("#win-add-edit-template").window(
                                        "open"
                                    );
                                    setTemplateVariables(data);
                                })
                                .fail(function(data) {
                                    RpcErrorHandler.show(data);
                                });
                        } else {
                            jQuery.messager.alert(
                                "Грешка",
                                "Моля изберете бланка, която искате да редактирате!"
                            );
                        }
                    }
                }
            ],
            onBeforeLoad: function() {
                jQuery("#templates-tables").datagrid("clearChecked");
            },
            onLoadSuccess: function() {
                if (
                    !jQuery("#template-variables > input")
                        .data()
                        .hasOwnProperty("combobox")
                ) {
                    initAddEditTemplateFields();
                }
            },
            onSelect: function(index, row) {
                lastSelectedTemplate = index;
            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
        if (lastSelectedTemplate !== null) {
            jQuery("#templates-tables").datagrid(
                "selectRow",
                lastSelectedTemplate
            );
        }
    }

    function initAddEditTemplateFields() {
        if (typeof ComboboxData == "undefined") {
            ContractTemplateVariables.read({})
                .done(function(data) {
                    ComboboxData = {};
                    ComboboxData.ContractTemplateVariables = data;
                    jQuery("#template-variables > input").combobox({
                        data: data,
                        valueField: "code",
                        textField: "title",
                        groupField: "group",
                        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                        loadFilter:
                            EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
                    });
                })
                .fail(function(errorObj) {
                    jQuery.messager.alert(
                        "Грешка",
                        errorObj.getMessage(),
                        "warning"
                    );
                });
        } else {
            jQuery("#template-variables > input").combobox({
                data: ComboboxData.ContractTemplateVariables,
                valueField: "code",
                textField: "title",
                groupField: "group",
                loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
            });
        }
    }

    function initShowPageNumbersCombobox(){
        jQuery('#show-page-numbers > input').combobox({
            data: [
                {
                    value: '',
                    title: 'Без номерация',
                    selected: true
                },
                {
                    value: 'left',
                    title: 'Отляво'
                },
                {
                    value: 'right',
                    title: 'Отдясно'
                },
                {
                    value: 'center',
                    title: 'В центъра'
                }
            ],
            valueField: 'value',
            textField: 'title',
            loader: EasyUIRPCLoaders.loader,
            loadFilter: EasyUIRPCLoaders.loadFilter
        });
    }

    function validateSaveTemplateRouter(e) {
        e.preventDefault();
        if (
            jQuery("#win-payment-subjects").window("options").closed === false
        ) {
            payment_subjects.validateSavePaymentTemplate();
        } else if (
            jQuery("#win-print-templates").length > 0 &&
            jQuery("#win-print-templates").data() !== null &&
            jQuery("#win-print-templates").window("options").closed === false
        ) {
            validateSaveHypothecsTemplate();
        } else {
            validateSaveContractTemplate();
        }
    }

    function validateSaveContractTemplate() {
        if (!jQuery("#template-name > input").val()) {
            jQuery.messager.alert("Грешка", "Моля попълнете име на бланката.");
            return false;
        }

        var addEditObj = getTemplateVariables();

        if (editTemplateRequested) {
            var getChecked = jQuery("#templates-tables").datagrid("getChecked");
            addEditObj["id"] = getChecked[0].id;

            TemplatesGrid.edit(addEditObj)
                .done(function(data) {
                    jQuery("#win-add-edit-template").window("close");
                    jQuery("#templates-tables").datagrid("loadRpc");
                })
                .fail(function(data) {
                    RpcErrorHandler.show(data);
                });
        } else {
            TemplatesGrid.add(addEditObj)
                .done(function(data) {
                    jQuery("#win-add-edit-template").window("close");
                    jQuery("#templates-tables").datagrid("loadRpc");
                })
                .fail(function(data) {
                    RpcErrorHandler.show(data);
                });

            return true;
        }
    }

    function getTemplateVariables() {
        return {
            template_name: jQuery("#template-name > input").val(),
            template_body: tinyMCE.activeEditor.getContent(),
            show_page_numbers: jQuery('#show-page-numbers > input').combobox("getValue")
        };
    }

    function setTemplateVariables(data) {
        jQuery("#template-variables > input").val("");
        jQuery("#template-name > input").val("");
        initShowPageNumbersCombobox();

        var editor = tinyMCE.get("template-content");
        editor.setContent("");

        if (data) {
            jQuery("#template-name > input").val(data[0]["title"]);
            jQuery('#show-page-numbers > input').combobox("select", data[0]['show_page_numbers'] ? data[0]['show_page_numbers'] : '' );
            editor.setContent(data[0]["html"]);
        }
    }

    function createContractDownloadVariables() {
        winDownloadContract = jQuery("#win-download").window({
            onClose: onDownloadContractWindowClose
        });

        downloadFileContract = jQuery("#btn-download-file");
        cancelDownloadFileContract = jQuery("#btn-download-file-close");
    }

    function onDownloadContractWindowClose() {
        return;
    }

    function createSalesContractDownloadVariables() {
        winDownloadContract = jQuery("#win-download").window({
            onClose: onDownloadSalesContractWindowClose
        });

        downloadFileContract = jQuery("#btn-download-file");
        cancelDownloadFileContract = jQuery("#btn-download-file-close");
    }

    function onDownloadSalesContractWindowClose() {
        return;
    }

    function initTinyMCE(data) {
        tinyMCE.init({
            selector: "#template-content",
            theme: "modern",
            height: 295,
            language: "bg_BG",
            invalid_elements: "script",
            relative_urls: false,
            style_formats: [
                {
                    title: "Font Family",
                    items: [
                        {
                            title: "Arial",
                            inline: "span",
                            styles: { "font-family": "arial" }
                        },
                        {
                            title: "Times New Roman",
                            inline: "span",
                            styles: { "font-family": "times new roman,times" }
                        }
                    ]
                }
            ],
            style_formats_merge: true,
            auto_focus: "template-content",
            forced_root_block: false,
            plugins: [
                "advlist autolink link jbimages image lists charmap print preview hr anchor pagebreak",
                "searchreplace wordcount visualblocks visualchars code fullscreen insertdatetime media nonbreaking",
                "save table contextmenu directionality emoticons template paste textcolor"
            ],
            toolbar:
                "insertfile undo redo | styleselect | fontsizeselect | bold italic | forecolor backcolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link print preview fullpage jbimages"
        });
    }

    function myCustomOnInit(data) {
        jQuery("#win-add-edit-template").window("open");
    }

    function markForEdit() {
        editTemplateRequested = true;
    }

    return {
        init,
        initPrintTemplatesSalesContracts,
        initAddEditTemplateFields,
        validateSaveTemplateRouter,
        validateSaveContractTemplate,
        getTemplateVariables,
        setTemplateVariables,
        createContractDownloadVariables,
        onDownloadContractWindowClose,
        createSalesContractDownloadVariables,
        onDownloadSalesContractWindowClose,
        initTinyMCE,
        myCustomOnInit,
        initTemplatesGrid,
        initPrintTemplatesGrid,
        markForEdit
    };
});
