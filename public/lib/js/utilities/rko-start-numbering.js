/*jslint browser: true*/
/*global jQuery, EasyUIRPCLoaders.EasyUIGridCustomLoader, TF, tinyMCE, RpcErrorHandler */
var user_farmings = [];

function setRkoNumberingStart() {
    'use strict';
    var farmsRko =  jQuery('#rko-start-numbering-table').propertygrid('getData');
    TF.Rpc.Payments.AddPayment.setRkoNumberingStart(farmsRko).done(function (data) {
        jQuery('#win-payment-numbering').window('close');
    }).fail(function (errorObj) {
        jQuery.messager.alert('Внимание', errorObj.getMessage(), 'warning');
    });
}

function initRkoNumberingField() {
    'use strict';
    jQuery('#rko-start-numbering-table').propertygrid({
        url: 'index.php?payments-rpc=contract-add-payment',
        rpcParams: [],
        rpcMethod: 'getLastNumber',
        showGroup: false,
        scrollbarSize: 10,
        resizable: true,
        columns:[[
            {
                field:'name',
                title:'Стопанство',
                width:100
            },
            {
                field:'value',
                title:'Начален номер',
                width:80
            },
        ]],
        fixed: true,
        onLoadSuccess: function(data) {
            jQuery('#win-payment-numbering').window('resize', { height:  data.total * 40 + 100 });
            jQuery('#rko-start-numbering-table').propertygrid('resize',{height:  data.total * 40 + 20});

        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}
