var renta_type_edit = false;
var renta_type_edit_index = undefined;

function initRentaTypeGrid() {
    var rentaTypesGrid = jQuery('#renta-type-tables');

	rentaTypesGrid.datagrid({
        nowrap: true,
        singleSelect: true,
        pageSize: 30,
        fit: true,
        fitColumns: true,
        showFooter: true,
        url:'index.php?common-rpc=renta-type-grid',
        sortName: 'id',
        sortOrder: 'asc',
        idField: 'id',
        border: false,
        frozenColumns:[[
        {
            field:'ck',
            checkbox:true
        }
        ]],
        columns:[[
        {
            field:'name',
            title:'<b>Наименование</b>',
            sortable:true,
            width:230
        },{
            field:'unit',
            title:'<b>Мерна</br>единица</b>',
            sortable:false,
            width:190,
            align: 'center'
        },{
            field:'unit_value',
            title:'<b>Единична</br>стойност</b>',
            sortable:false,
            width:220,
            align: 'center',
            editor: {
                type: 'numberbox',
                options: {
                    min: 0,
                    precision: 2
                }
            }
        },{
            field:'avg_yield',
            title:'<b>Среден добив</br>(дка)</b>',
            sortable:false,
            width:220,
            align: 'center',
            editor: {
                type: 'numberbox',
                options: {
                    min: 0,
                    precision: 2
                }
            }
        },{
            field:'treatment_price',
            title:'<b>Обработка</br>(лв/дка)</b>',
            sortable:false,
            width:220,
            align: 'center',
            editor: {
                type: 'numberbox',
                options: {
                    min: 0,
                    precision: 2
                }
            }
        },{
            field:'comment',
            title:'<b>Забележка</b>',
            sortable:false,
            align: 'center',
            width:220,
            editor: {
                type: 'textbox',
            }
        }
        ]],
        rownumbers:false,
        toolbar:[{
            id:'btnaddrentatype',
            text:'Добавяне',
            iconCls:'icon-add',
            handler:function(){
            	initAddRentaTypePanel();
            	jQuery('#win-add-renta-type').window('open');
            }
        },
        {
            id: 'btn_edit_rentatype',
            text: 'Редактиране',
            iconCls: 'icon-edit',
            handler: function() {
                var selected = rentaTypesGrid.datagrid('getSelected');

                if(selected) {
                    rentaTypesGrid.datagrid('beginEdit', renta_type_edit_index);
                }
                else {
                    jQuery.messager.alert('Грешка', 'Не е избрана тип рента!', 'error');
                }
            }
        },
        {
            id: 'btn_save_renta_types',
            text: 'Запази',
            iconCls: 'icon-save',
            handler: function() {
                rentaTypesGrid.datagrid('endEdit', renta_type_edit_index);
            }
        },
        {
            id: 'btn_reject_renta_types_changes',
            text: 'Отмени',
            iconCls: 'icon-undo',
            handler: function() {
                renta_type_edit = false;
                renta_type_edit_index = undefined;
                rentaTypesGrid.datagrid('rejectChanges');
            }
        }],
        onBeforeLoad: function(){
        	rentaTypesGrid.datagrid('clearChecked');
        },
        onLoadSuccess: function() {
            rentaTypesGrid.datagrid('resize');
        },
        onSelect: function(rowIndex, rowData) {
            if(renta_type_edit_index != undefined && renta_type_edit)
            {
                renta_type_edit = false;
                jQuery.messager.confirm('Потвърждение', 'Искате ли да запазите промените?', function(r){
                    if(r) {
                        rentaTypesGrid.datagrid('endEdit', renta_type_edit_index);
                    }
                    else {
                        rentaTypesGrid.datagrid('rejectChanges');
                        renta_type_edit = false;
                        renta_type_edit_index = undefined;
                    }
                });
            }
            else {
                renta_type_edit_index = rowIndex;
            }

        },
        onAfterEdit: function(rowIndex, rowData, changes) {
            renta_type_edit = false;
            if(jQuery.isEmptyObject(changes))
            {
                return;
            }

            saveRentaTypeChanges(rowData);
            renta_type_edit_index = undefined;
        },
        onBeforeEdit: function(rowIndex, rowData) {
            renta_type_edit = true;
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function saveRentaTypeChanges(rowData) {
    var rentaTypesGrid = jQuery('#renta-type-tables');
    renta_type_edit = false;
    let obj = {
        unit_value: rowData.unit_value,
        avg_yield_value: rowData.avg_yield,
        treatment_price_value: rowData.treatment_price,
        comment_value: rowData.comment,
        renta_type_id: rowData.id
    }
    TF.Rpc.Common
        .RentaTypeGrid
        .editRenta(obj)
        .done(function (dataObj) {
            rentaTypesGrid.datagrid('reload');
        })
        .fail(function (errorObj) {});
}

function initAddRentaTypePanel() {
	jQuery('#renta-type-unit > input').combobox({
		url: 'index.php?common-rpc=renta-units-combobox',
        rpcParams: [{
            selected: true
        }],
		editable: false,
		textField: 'fullname',
		valueField: 'id',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
	});

	jQuery('#renta-type-name > input').val('');
}

function validateAddNewRentaType(){
	if(jQuery('#renta-type-name > input').val()){

        var addRentaObj = {
            renta_type_name: jQuery('#renta-type-name > input').val(),
            renta_type_unit: jQuery('#renta-type-unit > input').combobox('getValue'),
            renta_type_unit_value: jQuery('#renta-type-unit-value > input').val(),
            avg_yield_value: jQuery('#avg-yield-value > input').val().replace(",", "."),
            treatment_price_value: jQuery('#treatment-price-value > input').val().replace(",", "."),
            comment_value: jQuery('#comment-value > input').val(),
        }

        if(isNaN(addRentaObj.avg_yield_value)){
            jQuery.messager.alert('Грешка', 'Полето "Среден добив" трябва да е число!');
            return false;
        }
        if(isNaN(addRentaObj.treatment_price_value)){
            jQuery.messager.alert('Грешка', 'Полето "Обработка" трябва да е число!');
            return false;
        }
        if(addRentaObj.avg_yield_value && addRentaObj.avg_yield_value < 0){
            jQuery.messager.alert('Грешка', 'Полето "Среден добив" не може да бъде отрицателно число!');
            return false;
        }
        if(addRentaObj.treatment_price_value && addRentaObj.treatment_price_value < 0){
            jQuery.messager.alert('Грешка', 'Полето "Обработка" не може да бъде отрицателно число!');
            return false;
        }

        TF.Rpc.Common.RentaTypeGrid.addRenta(addRentaObj)
        .done(function (data) {
            jQuery('#win-add-renta-type').window('close');
            jQuery('#renta-type-tables').datagrid('loadRpc');
        })
        .fail(function (data) {
            if(data.is(TF.Rpc.ExceptionsList.RENTA_TYPE_ALREADY_EXISTS))
            {
                jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.RENTA_TYPE_ALREADY_EXISTS.message, 'warning');
            }

        });

		return true;
	} else {
		jQuery.messager.alert('Грешка', 'Моля въведете име!', 'error');

		return false;
	}
}
