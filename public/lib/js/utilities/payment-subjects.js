(function(root, factory) {
    if (typeof define === "function" && define.amd) {
        // AMD. Register as an anonymous module.
        define([
            "jquery",
            "tinyMCE",
            "js/main/EasyUIRPCLoaders",
            "TF/Rpc/Common/PaymentSubjectsGrid",
            "js/main/rpc_errors_handler",
            "easyui"
        ], factory);
    } else {
        // Browser globals (root is window)
        root.payment_subjects = factory(
            jQuery,
            tinyMCE,
            EasyUIRPCLoaders,
            TF.Rpc.Common.PaymentSubjectsGrid,
            RpcErrorHandler
        );
    }
})(typeof self !== "undefined" ? self : this, function(
    jQuery,
    tinyMCE,
    EasyUIRPCLoaders,
    PaymentSubjectsGrid,
    RpcErrorHandler
) {
    var editPaymentSubjectRequested = false;
    var IMOTI_PODROBNO = "[[imoti_podrobno]]";
    var RENTA_NATURA = "[[renta_v_natura]]";
    var KONTRAGENT = "[[kontragent]]";
    var KONTRAGENT_REP = "[[kontragent_rep]]";
    var FARM = "[[stopanstvo]]";
    const OWNER_DETAILED = "[[owner_detailed]]";
    const CONTRACT_SIGNER =  "[[contract_signer]]";

    
    function init() {
        bindEventsListeners();
    }

    function bindEventsListeners() {
        jQuery("#btn_add_template_variable").on("click", addTemplateVariable);
        jQuery("#js-add-new-payment-subject").on(
            "click",
            validateAddNewPaymentSubject
        );
    }
    function clearPaymentSubjectFields() {
        "use strict";
        jQuery("#template-variables > input").val("");
        jQuery("#template-name > input").val("");
        var editor = tinyMCE.get("template-content");
        editor.setContent("");
    }

    function initAddEditPaymentSubjectFields() {
        "use strict";
        jQuery("#template-variables > input").combobox({
            url: "index.php?common-rpc=payment-subjects-combobox",
            rpcMethod: "getVariables",
            valueField: "code",
            textField: "title",
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }

    function setPaymentSubjectVariables(data) {
        "use strict";
        jQuery("#template-name > input").val(data[0].name);
        var editor = tinyMCE.get("template-content");
        editor.setContent(data[0].fulltext);
    }

    function initPaymentSubjectsGrid() {
        "use strict";
        var paymentSubjectsGrid = jQuery("#payment-subject-tables"),
            selected_subject_index,
            subject_type_edit;
        paymentSubjectsGrid.datagrid({
            nowrap: true,
            singleSelect: true,
            pageSize: 30,
            fit: true,
            fitColumns: true,
            showFooter: true,
            url: "index.php?common-rpc=payment-subjects-grid",
            sortName: "id",
            sortOrder: "asc",
            idField: "id",
            border: false,
            frozenColumns: [
                [
                    {
                        field: "ck",
                        checkbox: true
                    }
                ]
            ],
            columns: [
                [
                    {
                        field: "name",
                        title: "<b>Наименование</b>",
                        sortable: true,
                        width: 150,
                        editor: {
                            type: "validatebox",
                            options: {
                                required: true,
                                missingMessage:
                                    "Въведете наименование на основанието.",
                                tipPosition: "left"
                            }
                        }
                    },
                    {
                        field: "fulltext",
                        title: "<b>Пълен текст</b>",
                        sortable: false,
                        width: 350,
                        editor: {
                            type: "validatebox",
                            options: {
                                required: true,
                                missingMessage:
                                    "Въведете пълния текст на основанието.",
                                tipPosition: "left"
                            }
                        }
                    }
                ]
            ],
            rownumbers: false,
            toolbar: [
                {
                    id: "btnaddpaymentsubject",
                    text: "Добавяне",
                    iconCls: "icon-add",
                    handler: function() {
                        editPaymentSubjectRequested = false;

                        clearPaymentSubjectFields();

                        initAddEditPaymentSubjectFields();
                        jQuery("#win-add-edit-template").window("open");
                    }
                },
                {
                    id: "btn_edit_paymentsubject",
                    text: "Редактиране",
                    iconCls: "icon-edit",
                    handler: function() {
                        var getChecked = paymentSubjectsGrid.datagrid(
                            "getChecked"
                        );

                        if (getChecked[0]) {
                            editPaymentSubjectRequested = true;

                            PaymentSubjectsGrid.markForEdit(getChecked[0].id)
                                .done(function(data) {
                                    clearPaymentSubjectFields();
                                    jQuery("#win-add-edit-template").window(
                                        "open"
                                    );
                                    setPaymentSubjectVariables(data);
                                    initAddEditPaymentSubjectFields();
                                })
                                .fail(function(data) {
                                    RpcErrorHandler.show(data);
                                });
                        } else {
                            jQuery.messager.alert(
                                "Грешка",
                                "Моля изберете бланка, която искате да редактирате!"
                            );
                        }
                    }
                },
                {
                    id: "btn_delete_paymnet_subject",
                    text: "Изтриване",
                    iconCls: "icon-delete",
                    handler: function() {
                        var selected = paymentSubjectsGrid.datagrid(
                            "getSelected"
                        );
                        if (selected) {
                            jQuery.messager.confirm(
                                "Потвърждение",
                                "Искате ли да изтриете избраното основание?",
                                function(r) {
                                    if (r) {
                                        PaymentSubjectsGrid.deletePaymentSubject(
                                            selected.id
                                        )
                                            .done(function() {
                                                paymentSubjectsGrid.datagrid(
                                                    "loadRpc"
                                                );
                                            })
                                            .fail(function(errorObj) {
                                                jQuery.messager.alert(
                                                    "Грешка",
                                                    errorObj.getMessage(),
                                                    "warning"
                                                );
                                            });
                                    }
                                }
                            );
                        }
                    }
                }
            ],
            onBeforeLoad: function() {
                paymentSubjectsGrid.datagrid("clearChecked");
            },
            onSelect: function(rowIndex) {
                if (selected_subject_index !== undefined && subject_type_edit) {
                    jQuery.messager.confirm(
                        "Потвърждение",
                        "Искате ли да запазите промените?",
                        function(r) {
                            if (r) {
                                paymentSubjectsGrid.datagrid(
                                    "endEdit",
                                    selected_subject_index
                                );
                            } else {
                                paymentSubjectsGrid.datagrid("rejectChanges");
                                subject_type_edit = false;
                                selected_subject_index = undefined;
                            }
                        }
                    );
                } else {
                    selected_subject_index = rowIndex;
                }
            },
            onAfterEdit: function(rowIndex) {
                if (paymentSubjectsGrid.datagrid("validateRow", rowIndex)) {
                    var rows = paymentSubjectsGrid.datagrid("getRows"),
                        obj = rows[rowIndex];

                    PaymentSubjectsGrid.editPaymentSubject(obj)
                        .done(function() {
                            paymentSubjectsGrid.datagrid("loadRpc");
                        })
                        .fail(function(errorObj) {
                            jQuery.messager.alert(
                                "Грешка",
                                errorObj.getMessage(),
                                "warning"
                            );
                        });

                    selected_subject_index = undefined;
                    subject_type_edit = false;
                    paymentSubjectsGrid.datagrid("clearSelections");
                }
            },
            onBeforeEdit: function() {
                subject_type_edit = true;
            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }

    function validateAddNewPaymentSubject(e) {
        e.preventDefault();
        var data = {};
        data.name = jQuery("#payment-subject-name").val();
        data.fulltext = tinyMCE.activeEditor.getContent();
        if (data.name.length > 0 && data.fulltext.length > 0) {
            PaymentSubjectsGrid.addPaymentSubject(data)
                .done(function() {
                    jQuery("#payment-subject-tables").datagrid("loadRpc");
                    jQuery("#win-add-payment-subject").window("close");
                })
                .fail(function(errorObj) {
                    jQuery.messager.alert(
                        "Грешка",
                        errorObj.getMessage(),
                        "warning"
                    );
                });
        }
        return true;
    }

    function addTemplateVariable() {
        var templateVariable = jQuery("#template-variables > input").combobox(
            "getValue"
        );

        switch (templateVariable) {
            case IMOTI_PODROBNO:
                initDragAndDropColumns("#target", "#source");
                jQuery("#win-plots-detailed").window("open");
                break;
            case RENTA_NATURA:
                initRentaNaturaDragItems(function() {
                    initDragAndDropColumns(
                        "#renta-natura-target",
                        "#renta-natura-source"
                    );
                    jQuery("#win-renta-natura-detailed").window("open");
                });
                break;
            case KONTRAGENT:
                initDragAndDropColumns(
                    "#kontragent-target",
                    "#kontragent-source"
                );
                jQuery("#win-kontragent-detailed").window("open");
                break;
            case KONTRAGENT_REP:
                initDragAndDropColumns(
                    "#kontragent-rep-target",
                    "#kontragent-rep-source"
                );
                jQuery("#win-kontragent-rep-detailed").window("open");
                break;
            case FARM:
                initDragAndDropColumns("#farm-target", "#farm-source");
                jQuery("#win-farm-detailed").window("open");
                break;
            case OWNER_DETAILED:
                initDragAndDropColumns(
                    "#owner-detailed-target",
                    "#owner-detailed-source"
                );
                jQuery("#win-owner-detailed").window("open");
                break;   
            case CONTRACT_SIGNER:
                initDragAndDropColumns(
                    "#contract-signer-target",
                    "#contract-signer-source"
                );
                jQuery("#win-contract-signer").window("open");
                break;         
            default:
                var editor = tinyMCE.get("template-content");

                editor.selection.collapse(true);
                editor.execCommand("mceInsertContent", false, templateVariable);
                break;
        }
    }

    function initDragAndDropColumns(target, source) {
        jQuery(".drag").draggable({
            proxy: "clone",
            revert: true,
            cursor: "pointer",
            onStartDrag: function() {
                jQuery(this).draggable("options").cursor = "not-allowed";
                jQuery(this)
                    .draggable("proxy")
                    .addClass("dp");
            },
            onStopDrag: function() {
                jQuery(this).draggable("options").cursor = "pointer";
            }
        });

        jQuery(`${target}, ${source}`).droppable({
            accept: ".drag",
            onDragEnter: function(e, source) {
                jQuery(source).draggable("options").cursor = "pointer";
                jQuery(source)
                    .draggable("proxy")
                    .css("border", "1px solid #8fcf47");
                jQuery(this).addClass("over");
            },
            onDragLeave: function(e, source) {
                jQuery(source).draggable("options").cursor = "not-allowed";
                jQuery(source)
                    .draggable("proxy")
                    .css("border", "1px solid #ccc");
                jQuery(this).removeClass("over");
            },
            onDrop: function(e, source) {
                jQuery(this).append(source);
                jQuery(this).removeClass("over");
            }
        });
    }

    function addColumnsPlotsDetailed() {
        addColumns("imoti_podrobno", "#target", "#win-plots-detailed", false);
    }

    function addColumnsOwnerDetailed() {
        addColumns(
            "owner_detailed",
            "#owner-detailed-target",
            "#win-owner-detailed",
            false
        );
    }

    function addColumnsRentaNaturaDetailed() {
        addColumns(
            "renta_v_natura",
            "#renta-natura-target",
            "#win-renta-natura-detailed",
            true
        );
    }

    function addColumnsKontragent() {
        addColumns(
            "kontragent",
            "#kontragent-target",
            "#win-kontragent-detailed",
            true
        );
    }

    function addColumnsContractSigner() {
        addColumns(
            "contract_signer",
            "#contract-signer-target",
            "#win-contract-signer",
            true
        );
    }

    function addColumnsKontragentRep() {
        addColumns(
            "kontragent_rep",
            "#kontragent-rep-target",
            "#win-kontragent-rep-detailed",
            true
        );
    }

    function addColumnsFarm() {
        addColumns(
            "stopanstvo",
            "#farm-target",
            "#win-farm-detailed",
            true
        );
    }

    function addColumns(placeholder, targetSel, windowsSel, addSpaceIfColumns) {
        var columns = jQuery(targetSel).find(".drag");

        var str_elements = `[[${placeholder}`;

        jQuery.each(columns, function(index, value) {
            str_elements += " " + jQuery(value).attr("data");
        });
        if (addSpaceIfColumns && columns.length > 0) {
            str_elements += " ";
        }
        str_elements += "]]";

        var editor = tinyMCE.get("template-content");
        editor.selection.collapse(true);
        editor.execCommand("mceInsertContent", false, str_elements);
        jQuery(windowsSel).window("close");
    }
    // Ascending sort
    function asc_sort(a, b) {
        return parseInt(jQuery(b).attr("data-position")) <
            parseInt(jQuery(a).attr("data-position"))
            ? 1
            : -1;
    }

    function onClosePlotsDetailedWindow() {
        jQuery("#win-plots-detailed").window({
            onClose: function() {
                var columns = jQuery("#target .drag");

                jQuery.each(columns, function(index, value) {
                    // Cut and move element to the first div
                    jQuery(value).appendTo("#source");
                });

                // Sort columns from the first div by text
                jQuery("#source .drag")
                    .sort(asc_sort)
                    .appendTo("#source");
            }
        });
    }

    function onCloseRentaNaturaDetailedWindow() {
        jQuery("#win-renta-natura-detailed").window({
            onClose: function() {
                var columns = jQuery("#target .drag");

                jQuery.each(columns, function(index, value) {
                    // Cut and move element to the first div
                    jQuery(value).appendTo("#source");
                });

                // Sort columns from the first div by text
                jQuery("#source .drag")
                    .sort(asc_sort)
                    .appendTo("#source");
            }
        });
    }

    function getPaymentSubjectVariables() {
        "use strict";
        return {
            name: jQuery("#template-name > input").val(),
            fulltext: tinyMCE.activeEditor.getContent()
        };
    }

    function validateSavePaymentTemplate() {
        "use strict";
        if (jQuery("#template-name > input").val()) {
            var addEditObj = getPaymentSubjectVariables();

            if (editPaymentSubjectRequested) {
                var getChecked = jQuery("#payment-subject-tables").datagrid(
                    "getChecked"
                );
                addEditObj.id = getChecked[0].id;

                PaymentSubjectsGrid.editPaymentSubject(addEditObj)
                    .done(function() {
                        jQuery("#win-add-edit-template").window("close");
                        clearPaymentSubjectFields();
                        jQuery("#payment-subject-tables").datagrid("loadRpc");
                    })
                    .fail(function(data) {
                        RpcErrorHandler.show(data);
                    });
            } else {
                PaymentSubjectsGrid.addPaymentSubject(addEditObj)
                    .done(function() {
                        jQuery("#win-add-edit-template").window("close");
                        clearPaymentSubjectFields();
                        jQuery("#payment-subject-tables").datagrid("loadRpc");
                    })
                    .fail(function(data) {
                        RpcErrorHandler.show(data);
                    });

                return true;
            }
        } else {
            jQuery.messager.alert("Грешка", "Моля попълнете име на бланката.");

            return false;
        }
    }

    function initRentaNaturaDragItems(onInitCallback) {
        TF.Rpc.Common.RentaTypeGrid.read()
            .done(function(data) {
                if (jQuery("#renta-natura-source").children().length === 0) {
                    for (var i = data.rows.length - 1; i >= 0; i--) {
                        jQuery("#renta-natura-source").append(
                            '<div data-position="' +
                                (i + 1) +
                                '" data="' +
                                data.rows[i]["id"] +
                                '" class="drag dragable-el-backg">' +
                                data.rows[i]["name"] +
                                "</div>"
                        );
                    }
                }
                onInitCallback();
            })
            .fail(function(data) {
                RpcErrorHandler.show(data);
            });
    }

    return {
        init,
        onClosePlotsDetailedWindow,
        initPaymentSubjectsGrid,
        validateSavePaymentTemplate,
        onCloseRentaNaturaDetailedWindow,
        addColumnsPlotsDetailed,
        addColumnsRentaNaturaDetailed,
        addColumnsKontragent,
        addColumnsKontragentRep,
        addColumnsFarm,
        addColumnsOwnerDetailed,
        addColumnsContractSigner
    };
});
