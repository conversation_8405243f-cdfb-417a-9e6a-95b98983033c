var payment_reduce = false;
var payment_type = 'rent';
function initContractPaymentsGrid(contract_id, annex_id, year_id) {
	if(annex_id == undefined)
	{
		annex_id = 0;
	}

	var params = {
			owner_id: contract_id,
			annex_id: annex_id,
			year: year_id,
			by_heritor: ( jQuery('#search-heirs-name').val() !== '' || jQuery('#search-heirs-egn').val() !== '')
	};

	let firstselected = false;
	jQuery('#contracts-owner-payments-tables').datagrid({
		nowrap: true,
		singleSelect: false,
		title: 'Ренти',
		iconCls: 'icon-rents',
		pageSize: 50,
		fit: true,
		fitColumns: false,
		showFooter: true,
		url: 'index.php?owner-payments-rpc=contracts-owner-payments-grid',
		rpcParams: [params],
		sortName: 'c.c_num',
		sortOrder: 'asc',
		selectOnCheck: true,
		checkOnSelect: true,
		rowStyler: function(index, row) {
			if (row.has_rent_per_plot == true) {
				return 'background-color:#FF9900;';
			}

			if (row.has_heritor_overpaid && row.has_heritor_overpaid == true) {
				jQuery('#contracts-yellow-color-legend').show();
				return 'background-color:#ff9900; color:#000;';
			}
		},
		columns: [[
				{
                    field: 'for_payment_checkbox',
                    title: '<b>За <br/>Информация</b>',
                    sortable: false,
                    width: 70,
                    align: 'center',
                    rowspan: 2,
                    checkbox: true,
                    formatter: function(value, row, index) {

						return '<input data-row-index="' + index + '" data-owner-id="'+row.owner_id+'" data-charged-renta="'
								+row.charged_renta+'" data-contract-id="'+row.contract_id+'" data-path="'+row.path+'" data-is-heritor="'+row.is_heritor+'" type="checkbox"/>';
                    }
                },{
					title: '<b>Информация за договор</b>',
					colspan: 5
				}, {
					field: 'all_owner_area',
					title: '<b>Притежавана<br/>площ<br/>(дка)</b>',
					sortable: false,
					align: 'center',
					rowspan: 2,
					width: 100,
					formatter: function(value, row, index) {
						if (row.all_owner_no_rounded && row.all_owner_no_rounded.length > row.all_owner_area.length) {
							return setTootip('Цяла стойност: ' + row.all_owner_no_rounded, row.all_owner_area);
						}
						return row.all_owner_area;
					}
				},
				{
					field: 'pu_area',
					title: '<b>Площ за<br/>лично<br/>ползване(дка)</b>',
					sortable: false,
					align: 'center',
					rowspan: 2,
					width: 100
				},
				{
					field: 'owner_area',
					title: '<b>Използвана<br/>площ<br/>(дка)</b>',
					sortable: false,
					align: 'center',
					rowspan: 2,
					width: 100
				},{
					title: '<b>Информация за рента в лева</b>',
					colspan: 6
				},{
					title: '<b>Информация за рента в натура</b>',
					colspan: 9
				},{
					title: '<b>Информация за лично ползване</b>',
					colspan: 7
				}
			],
				[{
					field: 'c_num',
					title: '<b>Номер</b>',
					sortable: false,
					nowrap: false,
					width: 150
				},{
					field: 'rep_names',
					title: '<b>Представител</b>',
					sortable: false,
					nowrap: false,
					width: 150
				}, {
					field: 'timespan',
					title: '<b>Период</b>',
					sortable: false,
					width: 150,
					align: 'center'
				},{
					field: 'contract_type',
					title: '<b>Тип</b>',
					sortable: false,
					width: 100,
					align: 'center'
				},{
					field: 'farming_name',
					title: '<b>Стопанство</b>',
					sortable: false,
					width: 150,
					align: 'center'
				}, {
					field: 'renta_txt',
					title: '<b>Сума по<br/>договор</b>',
					sortable: false,
					width: 100,
					align: 'center'
				},{
					field: 'charged_renta_txt',
					title: '<b>Начислена<br/>сума</b>',
					sortable: false,
					width: 100,
					align: 'center'
				}, {
					field: 'paid_renta_txt',
					title: '<b>Изплатена<br/>сума</b>',
					sortable: false,
					width: 100,
					align: 'center',
					styler: function(value, row, index) {
						if (parseFloat(value) < 0) {
							return 'background-color:red;';
						}
					}
				},
				{
					field: 'paid_renta_by',
					title: '<b>чрез</b>',
					align: 'center',
					sortable: false,
					width: 145
				}, {
					field: 'unpaid_renta_txt',
					title: '<b>Оставаща<br/>сума</b>',
					sortable: false,
					width: 100,
					align: 'center',
					formatter: function(value, row, index) {
						if(row.personal_use_price_sum && row.personal_use_price_sum > 0){
							return '<span style="text-decoration: underline" title="От оставащата сума са приспаднати ' + row.personal_use_price_sum + ' лв. за обработки." class="easyui-tooltip">' + value + '</span>';
						} else {
							return value;
						}
					}
				},
				{
					field: 'over_paid_txt',
					title: '<b>Надплатена<br/>сума</b>',
					align: 'center',
					sortable: false,
					width: 100
				},{
					field: 'renta_nat_type',
					title: '<b>Тип натура</b>',
					sortable: false,
					width: 100,
					align: 'center'
				},{
					field: 'renta_nat_text',
					title: '<b>Дължимо<br/>количество</b>',
					sortable: false,
					width: 100,
					align: 'center'
				}, {
					field: 'charged_renta_nat_text',
					title: '<b>Начислено<br/>количество</b>',
					sortable: false,
					width: 100,
					align: 'center'
				}, {
					field: 'paid_renta_nat',
					title: '<b>Изплатено<br/>количество</b>',
					sortable: false,
					width: 100,
					align: 'center'
				},
				{
					field: 'paid_renta_nat_by',
					title: '<b>чрез</b>',
					align: 'center',
					sortable: false,
					width: 145
				},
				{
					field: 'paid_renta_nat_by_detailed',
					title: '<b>детайлно</b>',
					align: 'center',
					sortable: false,
					width: 145
				}, {
					field: 'unpaid_renta_nat_text',
					title: '<b>Оставащо<br/>количество</b>',
					sortable: false,
					width: 100,
					align: 'center'
				},
				{
					field: 'unpaid_renta_nat_unit_value',
					title: '<b>в лева (ед. ст.)</b>',
					align: 'center',
					sortable: false,
					width: 100
				},
				{
					field: 'over_paid_nat_text',
					title: '<b>Надплатена натура</b>',
					align: 'center',
					sortable: false,
					width: 145
				},
				{
					field: 'personal_use_nat_types_names',
					title: '<b>Тип натура<br/>за лично ползване</b>',
					sortable: false,
					width: 120,
					align: 'center'
				},
				{
					field: 'personal_use_amount',
					title: '<b>Дължимо<br/>количество</b>',
					sortable: false,
					width: 100,
					align: 'center'
				},
				{
					field: 'personal_use_paid',
					title: '<b>Изплатено<br/>количество</b>',
					sortable: false,
					width: 100,
					align: 'center'
				},
				{
					field: 'personal_use_unpaid',
					title: '<b>Оставащо<br/>количество</b>',
					sortable: false,
					width: 100,
					align: 'center'
				},
				{
					field: 'personal_use_treatments_sum',
					title: '<b>Сума за<br/>обработки (лв.)</b>',
					sortable: false,
					width: 110,
					align: 'center'
				},
				{
					field: 'personal_use_paid_treatments',
					title: '<b>Изплатени сума <br/>за обработки (лв.)</b>',
					sortable: false,
					width: 110,
					align: 'center'
				},
				{
					field: 'personal_use_unpaid_treatments',
					title: '<b>Остатъчна сума <br/>за обработки (лв.)</b>',
					sortable: false,
					width: 110,
					align: 'center'
				}
			]],
		pagination: true,
		rownumbers: true,
		toolbar: [{
				id: 'btnpayrenta',
				text: 'Изплащане на рента',
				iconCls: 'icon-payments',
				handler: function() {
					var getSelections = jQuery('#contracts-owner-payments-tables').datagrid('getSelections');

					for(var i = 0; i < getSelections.length; i++) {
						if(getSelections[i].have_children_paids && getSelections[i].have_children_paids == true) {
							jQuery.messager.alert('Грешка', 'Не може да изплатите рента на починалият собственик след като има плащане към наследниците му.', 'warning');
							return;
						}
					}

					jQuery('#btn-add-payment-with-deduction').hide();
					if (getSelections[0]) {
						//payment request type is for single payment
						//used on payment validation
						paymentType = 'single';
						payment_reduce = false;
						jQuery('#win-add-payment').window('setTitle', 'Изплащане на рента');
						jQuery('#btn-add-payment > a').linkbutton({text:'Изплати'});

                        var farmingIds = jQuery.map(getSelections, function(el) {
                            return el.farming_id;
                        });
                        farmingIds = jQuery.unique(farmingIds);
                        var farmingId = farmingIds.join(',');
						initPaymentFromMoneyPanel(farmingId);

						jQuery('#win-add-payment').window('resize', {
							height: 710
						});

						jQuery('#win-add-payment').window('open');
					} else {
						jQuery.messager.alert('Грешка', 'Не е избран договор!', 'error');
					}
				}
			},{
				id: 'btnaddnatpayment',
				text: 'Изплащане на натура',
				iconCls: 'icon-payments',
				handler: function() {
					var getSelections = jQuery('#contracts-owner-payments-tables').datagrid('getSelections');

					if (getSelections[0]) {
						//check if renta nat type is the same for all selected rows
						var renta_flag = true;

						var main_selection_index = 0;
						for (var i = 0; i < getSelections.length; i++)
						{
							if(getSelections[i]['nat_type_ids'].length != 0){
								main_selection_index = i;
								break;
							}
							else
							{
								jQuery.messager.alert('Грешка', 'Селектираният договор няма рента в натура!', 'error');
								return;
							}
						}
						for (var i = 0; i < getSelections.length; i++)
						{

							if(getSelections[i].allow_owner_payment == true && getSelections[i].have_children_paids && getSelections[i].have_children_paids == true) {
								jQuery.messager.alert('Грешка', 'Не може да изплатите рента на починалият собственик след като има плащане към наследниците му.', 'warning');
								return;
							}

							for (var k = 0; k < getSelections[main_selection_index]['nat_type_ids'].length; k++){
								if (getSelections[main_selection_index]['nat_type_ids'][k] != getSelections[i]['nat_type_ids'][k]) {
									renta_flag = false;
									break;
								}
							}
						}

						if (renta_flag) {
							//payment type shows if single contract payment is requested or multi
							//used on validation
							paymentType = 'single';
							payment_reduce = false;
							jQuery('#win-add-nat-payment').window('setTitle', 'Изплащане на натура');
							jQuery('#btn-add-nat-payment > a').linkbutton({text:'Изплати'});

							clearNaturaPayment();
                            var farmingIds = jQuery.map(getSelections, function(el) {
                                return el.farming_id;
                            });

							farmingIds = jQuery.unique(farmingIds);
                            var farmingId = farmingIds.join(',');
							initPaymentFromNaturaPanel(farmingId);
							jQuery('#win-add-nat-payment').window('open');
						} else {
							jQuery.messager.alert('Грешка', 'Не можете да изплащате натура от различни типове, едновременно!', 'error');
						}
					} else {
						jQuery.messager.alert('Грешка', 'Не е избран договор!', 'error');
					}
				}
			},{
                id:'btnviewinfo',
                text:'Информация',
                iconCls:'icon-info',
                handler:function(){
					var selectedContract = jQuery('#contracts-owner-payments-tables').datagrid('getSelected');
					if(selectedContract === null) {
						jQuery.messager.alert('Грешка', 'Моля изберете договор, за който да бъде показана информация.', 'error');
						return;
					}
					window.open(
						"index.php?page=Contracts.Home&contract_id=" + selectedContract.contract_id,
						"_blank"
					);
                }
            }],
		onLoadSuccess: function (data) {
			let selected = null;
			if(data.rows.length == 1) {
				selected = data.rows[0];
			}
			if(selected) {
				firstselected = true;
				jQuery(this).datagrid('selectRow', 0);
			}

			firstselected = false;

			//Forget to select all rows.. this is necessary because you cannot select a parent and child at the same time
			jQuery(this).closest('.datagrid-view').find('.datagrid-header-check input[type="checkbox"]').hide();
		},
		onBeforeLoad: function() {
			jQuery('#contracts-yellow-color-legend').hide();
			jQuery('#contracts-owner-payments-tables').datagrid('clearChecked');
			jQuery('#contracts-owner-payments-tables').datagrid('clearSelections');
		},
		onSelect: function(index, row) {
			enableDisablePaidRentBtn(!firstselected);
			firstselected = false;
		},
		onUnselect: function(index, row) {
			enableDisablePaidRentBtn();
		},
		onSelectAll: function() {
			firstselected = false;
			enableDisablePaidRentBtn();
		},
		onUnselectAll: function() {
			enableDisablePaidRentBtn();
		},
		
		loader : EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter : EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

}

function enableDisablePaidRentBtn(showMsg = true) {
	var selections = jQuery('#contracts-owner-payments-tables').datagrid('getSelections');
	jQuery('#btnpayrenta').linkbutton({disabled:false});
	jQuery('#btnaddnatpayment').linkbutton({disabled:false});
	
	if(selections.length === 1) {
		return false;
	}

	for (var owner of selections){

		if(showMsg){
			var renta_nat = null;
			if(owner.unpaid_renta_nat_arr && owner.unpaid_renta_nat_arr != '-') {
				jQuery.each(owner.unpaid_renta_nat_arr, function(key, value) {
					renta_nat += value;
				  });
			}
	
			if((parseFloat(owner.unpaid_renta) == 0.00) || (owner.farming_id != selections[0].farming_id)) {
				jQuery('#btnpayrenta').linkbutton({disabled:true});
			}
			if((owner.unpaid_renta_nat_arr && renta_nat == 0.00) || (owner.farming_id != selections[0].farming_id)) {
				jQuery('#btnaddnatpayment').linkbutton({disabled:true});
			}

			if(renta_nat !== null && parseFloat(renta_nat) === 0 && parseFloat(owner.unpaid_renta) === 0) {
				jQuery.messager.alert('Предупреждение', 'Избрали сте договор, който няма оставаща сума и оставащо количество.', 'warning');
				return;
			} else if(renta_nat !== null && parseFloat(renta_nat) === 0.00){
				jQuery.messager.alert('Предупреждение', 'Избрали сте договор, който няма оставащо количество.', 'warning');
				return;
			} else if(parseFloat(owner.unpaid_renta) === 0.00) {
				jQuery.messager.alert('Предупреждение', 'Избрали сте договор, който няма оставаща сума.', 'warning');
				return;
			} else if(owner.farming_id != selections[0].farming_id) {
				jQuery.messager.alert('Предупреждение', 'Моля, селектирайте договори, само от едно стопанство.', 'warning');
				return;
			}
		}
	}
}

function initUnpaidRentGrid(contract_id, year_id) {
	var unpaied_renta_params = {
		owner_id: contract_id,
		year: year_id,
	};

	jQuery('#unpaid-rent-owner-tables').datalist({
		fit: false,
        checkbox:false,
        height: "121",
        autoRowHeight: false,
        fitColumns: false,
        singleSelect: false,
		url: 'index.php?owner-payments-rpc=contracts-owner-payments-grid',
		rpcMethod: 'unpaidRentByOwner',
		rpcParams: [unpaied_renta_params],
        pagination: false,
        border: false,
		rowStyler: function(index, row) {
				return 'text-align: center; height: auto;';
		},
        textField: 'name',
        valueField:'name',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: function(data) {
			if (data.result && data.result.rows) {
				var combinedRows = data.result.rows.map(function(row) {
					return {
						...row,
						name: row.farming_year + ' - ' + row.unpaid_rent
					};
				});

				return {
					total: data.result.total,
					rows: combinedRows
				};
			} else {
				return {
					total: 0,
					rows: []
				};
			}
		},
		onSelect: function(index, row) {
			// Отменяне на селектирането
			jQuery('#unpaid-rent-owner-tables').datagrid('unselectRow', index);
		},
	});

}
