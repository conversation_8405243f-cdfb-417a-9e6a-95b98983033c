jQuery(function() {
	initSearchOnEnter();
	
	jQuery('#btn-open-contracts-filter-owners').bind('click', function()
	{
		jQuery('#win-contracts-filter').window('resize', {
			height: getZoomedWindowHeight(420),
			width: 700
		});
		jQuery('#win-contracts-filter').window('open');
		return false;
	});

	jQuery('#owners-info-button').bind('click', function() {
		var getSelected = jQuery('#owners-contracts-tree').tree('getSelected');

		if (getSelected.id) {
			var url_string = '&owner_ids=';
			window.open("index.php?page=Owners.Home" + url_string + getSelected.id, '_blank');
		}
		else {
			jQuery.messager.alert('Грешка', 'Моля изберете договор.', 'error');
		}
	});

	jQuery('#btn-owner-payments-add-charged-renta').bind('click', function() {
		if (!hasPlotRightsRW) {
			messagerPlotsWriteRights();
			return false;
		}

        jQuery.messager.alert({
            title: 'Информация',
            msg: 'С това действие се начислява рента само на имотите с рента по договор, като не се взимат в предвид имотите с въведена индивидуална рента.',
            fn: function() {
                var getSelections = jQuery('#contracts-owner-payments-tables').datagrid('getSelections');

                jQuery('#win-add-charged-renta-history').window('open');
                initChargeRentaEkatte();
            }
        });

		return false;
	});

	jQuery('#btn-info-charged-renta').bind('click', function() {

		jQuery('#win-info-charged-renta').window('open');
	});

	jQuery('#info-charged-renta-btn').bind('click', function()
	{
		jQuery('#win-info-charged-renta-btn').window('open');
		return false;
	});
});

function initChargeRentaEkatte(){
	ekateComboboxData = ComboboxData.EkateCombobox;

	jQuery('#acr-plot-ekatte > input').combobox({
		data: ekateComboboxData,
		valueField: 'ekate',
		textField: 'text',
		onChange: function(newValue, oldValue) {
			updateMestnostCombobox(newValue, "#acr-plot-mestnost");
		},
		filter: function(q, row){
			var opts = jQuery(this).combobox('options');
			var text = row[opts.textField].toLowerCase();
			var value = row[opts.valueField];
			var find = q.toLowerCase();
			if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
			{
				return true;
			}
		},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

	jQuery('#acr-plot-area-type > input').combobox({
		url: 'index.php?common-rpc=plot-ntp-combobox&record_all=true',
		valueField: 'id',
		textField: 'name',
		editable: false,
		multiple: true,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

	jQuery('#acr-plot-category > input').combobox({
		url: 'index.php?common-rpc=plot-category-combobox&record_all=true',
		valueField: 'id',
		textField: 'name',
		multiple: true,
		onHidePanel: onHidePanelMultiSelect,
		editable: false,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function completeSavePayment(sender){
	generatePaymentDocument(sender);

	jQuery('#contracts-owner-payments-tables').datagrid('reload');
}

function completeSaveNatPayment(sender){
	generatePaymentNatDocument(sender);

	jQuery('#contracts-owner-payments-tables').datagrid('reload');
}

function initSearchOnEnter() {
    jQuery("#win-contracts-filter").off("keyup").on("keyup", function (event) {
		// Listen for enter key
        if (13 !== event.keyCode) {
			return;
		}
		jQuery("#btn-filter-contracts-tree").click()
    });
}

