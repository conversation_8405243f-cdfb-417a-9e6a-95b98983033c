var editContractID;
var pageNumber = 1;

function initContractsTree(page, filterObj) {
	selectContractID = undefined;
    pageNumber = page ?? 1;


    var ownersContractsTree = jQuery('#owners-contracts-tree');
    var isTreeBound = ownersContractsTree.data().hasOwnProperty('tree');

    if (isTreeBound) {
        ownersContractsTree.tree({
            page: pageNumber,
            rpcParams: [filterObj]
        });
        return;
    }
    var param = {};

    if (filterObj) {
        var contract_ids = null;
        if (filterObj.contract_ids) {
            contract_ids = filterObj.contract_ids.split(",");
            param.contract_ids = contract_ids;
        }

        param.year = filterObj.year;
        param.cnum = filterObj.cnum;
        param.contract_type = filterObj.contract_type;
        param.farming = filterObj.farming;
        param.owner_name = filterObj.owner_name;
        param.owner_egn = filterObj.owner_egn;
        param.heirs_name = filterObj.heirs_name;
        param.heirs_egn = filterObj.heirs_egn;
        param.rep_name = filterObj.rep_name;
        param.rep_egn = filterObj.rep_egn;
        param.company_name = filterObj.company_name;
        param.company_eik = filterObj.company_eik;
        param.with_renta_nat = filterObj.with_renta_nat;
        param.with_bank_acc = filterObj.with_bank_acc;
        param.person_name = filterObj.person_name;
        param.person_egn = filterObj.person_egn;
    }

    param.page_number = pageNumber;
    ownersContractsTree.tree(
        {
            url: 'index.php?owner-payments-rpc=owners-contracts-tree',
            animate: false,
            lines: true,
            rpcParams: [param],
            sort: 'true_id',
            order: 'desc', 
            page: pageNumber,
            rows: 30,
            onSelect: function (node) {
                if( !jQuery('#search-year').data().hasOwnProperty('combobox')) return false;
                var year_id = jQuery('#search-year').combobox('getValue');
                // fill annex info
                fillContractInfo(node.attributes);
                initContractPaymentsGrid(node.id, node.attributes.annex_id, year_id);
                initPersonalUseGrid(node.id, year_id);
                initUnpaidRentGrid(node.id, year_id);
            },
            onLoadSuccess: function () {
                var roots = jQuery(this).tree('getRoots');
                var total = 0;
                var limit = 30;
                if (roots.length) {
                    var getSelected = jQuery(this).tree('getSelected');
                    if (!getSelected) {
                        if (editContractID != undefined) {
                            var node = jQuery(this).tree('find', editContractID);
                            jQuery(this).tree('select', node.target);
                        } else {
                            jQuery(this).tree('select', roots[0].target);
                        }
                    }
                    total = roots[0]['attributes']['pagination']['total'];
                    limit = roots[0]['attributes']['pagination']['limit'];
                } else {
                    // fill annex info
                    fillContractInfo();
                    jQuery('#contracts-owner-payments-tables').datagrid('loadData', []);
                    jQuery('#personal-use-tables').datagrid('loadData', []);

                    if (jQuery('#unpaid-rent-owner-tables').data('datalist')) {
                        jQuery('#unpaid-rent-owner-tables').datalist('loadData', { rows: [], total: 0 });
                    }

                    var farmYear = jQuery('#search-year').combobox('getText');
                    var msg = 'Не са открити записи за ' + farmYear.replace('Стопанска', 'стопанска година');
                    jQuery.messager.alert('Внимание', msg, 'warning');
                }
                // init pagination with total contract elements
                initContractsPagination(total, limit, pageNumber);
            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });

    jQuery('.js-charged-multirent-row').remove();

    var rentaRows = jQuery('#js-charged-multirent-table').find('.js-charged-multirent-row').length;

    var comboHTML = '<tr class="js-charged-multirent-row"><td style="padding:0 0 10px 15px"><select id="js-charged-renta-type-cb-'
        + (rentaRows) + '"></select></td>';
    comboHTML += '<td style="padding:0 0 10px 10px"><input class="js-charged-renta-value" id="charged-renta-value-'
        + (rentaRows) + '"></td>';
        comboHTML += '<td style="padding:0 0 10px 10px"><input class="js-charged-unit-price" id="charged-unit-price-'
        + (rentaRows) + '"></td>';
    comboHTML += '<td style="padding:0 0 10px 10px"><input type="checkbox" class="js-renta-nat-is-converted" id="renta-nat-is-converted-'
        + (rentaRows) + '"></td>';

    jQuery('#js-charged-multirent-table').append(comboHTML);

    var rentaNaturaSpinner = jQuery('#charged-renta-value-' + (rentaRows));

    jQuery('#renta-nat-is-converted-0').change(function () {
        if (jQuery('#renta-nat-is-converted-0').is(':checked')) {
            jQuery('#charged-unit-price-0').numberspinner({required: true, disabled: false});
            //set '0' cause id end with 0 for the first fields
            checkChargedRentaInLeva(0);
        } else {
            jQuery('#charged-unit-price-0').numberspinner({required: false, disabled: true});
        }
    });
    jQuery('#charged-unit-price-0').numberspinner({
        min: 0,
        disabled: true,
        missingMessage: 'Моля задайте Единична стойност(лв).',
        width: 95,
        precision: 3
    });

    jQuery('#charged-renta-value-0').numberspinner({
        min: 0,
        disabled: true,
        required: false,
        missingMessage: 'Моля задайте количество на горепосочената рента в натура(количество за декар).',
        width: 95,
        parser: function (value) {
            if (value == '-') {
                return value;
            }
            if (jQuery.isNumeric(value)) {
                return parseFloat(value).toFixed(3);
            }
        }
    });

    jQuery('#js-charged-renta-type-cb-' + (rentaRows)).combobox({
        url: 'index.php?common-rpc=renta-types-combobox',
        valueField: 'id',
        textField: 'name',
        width: 205,
        editable: false,
        onSelect: function (rec) {
            var rowArr = jQuery('.js-charged-multirent-row');
            var rowVars = [];
            for (var i = 0; i < rowArr.length; i++) {
                rowVars[i] = jQuery(                    '#js-charged-renta-type-cb-' + i).combobox('getValue');
            }
            var inRes = jQuery.inArray(String(rec.id), rowVars);
            if (inRes != -1 && jQuery.inArray(String(rec.id), rowVars, inRes + 1) != -1) {
                jQuery('#js-charged-renta-type-cb-0').combobox('clear');
            }
        },
        onChange: function (newValue) {
            onChangeRentaTypesCombobox(this, newValue);
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function onChangeRentaTypesCombobox(element, newValue) {
    var renta_nat_unit_id = element.id.split("-");
    var id = renta_nat_unit_id[renta_nat_unit_id.length - 1];
    var natura_price_check = jQuery('#renta-nat-is-converted-' + id).is(':checked');
    var data = jQuery('#js-charged-renta-type-cb-' + id).combobox('getData');
    var natura_price = jQuery('#charged-unit-price-' + id);
    var natura_amount = jQuery('#charged-renta-value-' + id);

    //clear value
    natura_price.numberbox('clear');

    //get unit value for renta type and set to field
    jQuery.each(data, function (key, value) {
        if ((!value.name || value.name == '-') && value.id == newValue) {
            natura_amount.numberspinner({required: false, disabled: true});
            natura_amount.numberspinner('clear');
            return;
        }
        else if (value.id == newValue) {
            if (natura_price_check) {
                natura_price.numberbox('setValue', value.unit_value);
            }

            natura_amount.numberspinner({required: true, disabled: false});
            var natura_amount_value = natura_price.numberbox('getValue');
            if (natura_amount_value == '') {
                natura_amount.numberspinner('clear');
            }
            return;
        }
    });
}

function checkChargedRentaInLeva(id) {
    var rentaTypesCombobox = jQuery('#js-charged-renta-type-cb-' + id);
    var data = rentaTypesCombobox.combobox('getData');
    var selectedData = rentaTypesCombobox.combobox('getValue');
    var natura_price = jQuery('#charged-unit-price-' + id);

    //clear value
    natura_price.numberbox('clear');

    //get unit value for renta type and set to field
    jQuery.each(data, function (key, value) {
        if (value.unit_value && value.id == selectedData) {
            natura_price.numberbox('setValue', value.unit_value);
        }
    });
}

function initContractsPagination(total, limit, currentPage) {
    jQuery('#owners-contracts-tree-pagination').pagination({
        showPageList: false,
        showRefresh: false,
        displayMsg: '',
        total: total,
        pageSize: limit,
        ...(currentPage !== undefined && { pageNumber: currentPage }),
        onSelectPage: function (page, pageSize) {
            const obj = getOwnersContractsTreeFilter();
            initContractsTree(page, obj);
        }
    });
}

function fillContractInfo(owner) {

    var info_elements = [
        {el: '#info-owner-type', field: 'owner_type_name'},
        {el: '#info-owner-names', field: 'owner_names'},
        {el: '#info-owner-egn', field: 'egn'},
        {el: '#info-owner-lk-nomer', field: 'lk_nomer'},
        {el: '#info-owner-lk-izdavane', field: 'lk_izdavane'},
        {el: '#info-owner-remark', field: 'remark'},
        {el: '#info-owner-phone', field: 'phone'},
        {el: '#info-owner-fax', field: 'fax'},
        {el: '#info-owner-mobile', field: 'mobile'},
        {el: '#info-owner-email', field: 'email'},
        {el: '#info-owner-iban', field: 'iban'},
        {el: '#info-owner-address', field: 'address'},
        {el: '#info-owner-is-dead', field: 'is_dead_text'},
        {el: '#info-company-name', field: 'company_name'},
        {el: '#info-company-eik', field: 'eik'},
        {el: '#info-company-mol', field: 'mol'},
        {el: '#info-company-address', field: 'company_address'}
    ];
    var count = 0;
    if (!owner) {
        for(count = 0 ; count < info_elements.length ; count++){
            jQuery(info_elements[count].el).html('');
        }
        return false;
    }

    if (owner.owner_type == 0) {
        jQuery('#info-owner').hide();
        jQuery('#info-company').show();
    } else if (owner.owner_type == 1) {
        jQuery('#info-owner').show();
        jQuery('#info-company').hide();
    }
    for(count = 0 ; count < info_elements.length ; count++){
        var value = owner[info_elements[count].field];
        jQuery(info_elements[count].el).html(value);
    }
}

function contractsFilter() {
    var obj = {};
    obj.year = jQuery('#search-year').combobox('getValue');
    obj.cnum = jQuery('#search-cnum').val();
    obj.contract_type = jQuery('#search-contract-type').combobox('getValues');
    obj.farming = jQuery('#search-farming').combobox('getValues');
    obj.owner_name = jQuery('#search-owner-name').val();
    obj.owner_egn = jQuery('#search-owner-egn').val();
    obj.heirs_name = jQuery('#search-heirs-name').val();
    obj.heirs_egn = jQuery('#search-heirs-egn').val();
    obj.company_name = jQuery('#search-company-name').val();
    obj.company_eik = jQuery('#search-company-eik').val();
    obj.rep_name = jQuery('#search-represent-name').val();
    obj.rep_egn = jQuery('#search-represent-egn').val();
    obj.person_name = jQuery('#search-person-name').val();
    obj.person_egn = jQuery('#search-person-egn').val();
    
    obj.owner_note = jQuery('#search-owner-note').val();
    if(obj.owner_note.length > 0 && obj.owner_note.length < 3) {
		jQuery.messager.alert('Грешка', 'Моля въведете поне 3 символа за търсене в полето "Забележка"', 'warning');
		return;
	}
    
    obj.owner_phone = jQuery('#search-owner-phone').val();
    if(obj.owner_phone.length > 0 && obj.owner_phone.length < 5) {
		jQuery.messager.alert('Грешка', 'Моля въведете поне 5 символа за търсене в полето "Телефон/Мобилен"', 'warning');
		return;
	}

    if(jQuery('#search-contract-group').combobox('getValues') != '') {
		obj.contract_group = jQuery('#search-contract-group').combobox('getValues');
	}

    if (jQuery('#search-contracts-with-nat input').is(':checked')) {
        jQuery('#acr-contracts-with-nat input').prop('checked', true);
        obj.with_renta_nat = 1;
    }

    if (jQuery('#search-all-contracts-with-bank-acc').is(':checked')) {
        obj.with_bank_acc = 1;
    }

    if (jQuery('#search-contracts-without-nat input').is(':checked')) {
        jQuery('#acr-contracts-without-nat input').prop('checked', true);
        obj.with_renta_nat = 0;
    }

    if (jQuery('#search-all-contracts input').is(':checked')) {
        jQuery('#acr-all-contracts input').prop('checked', true);
    }

    initContractsTree(1, obj);

    jQuery('#acr-year > input').combobox('setValue', obj.year);
    jQuery('#acr-cnum > input').val(obj.cnum);
    jQuery('#acr-contract-type > input').combobox('setValue', obj.contract_type);
    jQuery('#acr-contract-group > input').combobox('setValue', obj.contract_group);
    jQuery('#acr-farming > input').combobox('setValue', obj.farming);
    jQuery('#acr-owner-name > input').val(obj.owner_name);
    jQuery('#acr-owner-egn > input').val(obj.owner_egn);
    jQuery('#acr-company-name > input').val(obj.company_name);
    jQuery('#acr-company-eik > input').val(obj.company_eik);

    jQuery('#win-contracts-filter').window('close');
}
