var personal_use_edit = false;
var personal_use_edit_index = undefined;
var personal_use_divide = undefined;
var personal_use_value = undefined;
var used_area = undefined;
var DIVIDE_BY_PLOT = 2;

function initPersonalUseGrid(owner_id, year_id)
{
    var personalUseGrid = jQuery('#personal-use-tables');
    personalUseGrid.datagrid({
        nowrap: true,
        singleSelect: true,
		title: 'Лично ползване',
		iconCls: 'icon-planting',
        fit: true,
        fitColumns: true,
        showFooter: true,
        border: true,
		rownumbers: true,
        sortName: 'c.c_num',
        sortOrder: 'asc',
		url : 'index.php?owner-payments-rpc=owner-personal-use-grid',
        rpcParams: [{
            owner_id: owner_id,
            year: year_id
        }],
        columns: [
            [
                {
                    field: 'c_num',
                    title: '<b>Номер на договор/анекс</b>',
                    sortable: true,
                    width: 150
                }, {
                    field: 'total_owned_area',
                    title: '<b>Обща площ<br/>(дка)</b>',
                    sortable: true,
                    width: 100,
                    align: 'center',
                }, {
                    field: 'total_personal_use_area',
                    title: '<b>Лично ползване<br/>(дка)</b>',
                    sortable: true,
                    width: 100,
                    align: 'center',
                    editor: {
                        type: 'numberbox',
                        options: {
                            min: 0,
                            precision: 3,
                            onChange: function(newValue, oldValue) {
                                if(newValue == undefined || newValue == '') {
                                    return;
                                }

                                //set new value of personal use in public variable
                                personal_use_value = newValue;
                            }
                        }
                    }
                }
            ]
        ],
        onBeforeLoad: function() {
            jQuery('#personal-use-tables').datagrid('clearChecked');
        },
		onSelect: function(rowIndex, rowData) {
			personal_use_edit_index = rowIndex;
		},
        onLoadSuccess: function() {
            personal_use_edit_index = undefined;
            personal_use_divide = undefined;
            personal_use_value = undefined;
            jQuery(this).datagrid('getPanel').find('a.easyui-linkbutton').linkbutton();
        },
        loader : EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter : EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function personalUsePlotsCheck(type, rowIndex) {
    var selected = jQuery('#personal-use-tables').datagrid('getRows')[rowIndex];

    if(selected) {
        jQuery('#win-personal-use-plots').window('open');
        personalUsePlotsGrid(type, personal_use_divide, selected.personal_use_plots, personal_use_value, used_area)
    }
    else {
        jQuery.messager.alert('Грешка', 'Не е избран договор!', 'error');
    }
}
