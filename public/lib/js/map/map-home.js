//init global variables
var map,
    editLayerID, //id of chosen layer for personalization
    controlDraw, //control handles new polygon drawing
    controlSelect, //control handles polygon selection
    controlSnapping, //control handles polygon snapping
    controlModify, //control handles modify of selected feature with controlSelect
    controlSplit,
    controlDrawSplitLine,
    controlDrawHole,
    controlNavigation,
    controlScaleLine,
    controlLineMeasure,
    controlPolygonMeasure,
    controlZoomIn,
    controlZoomOut,
    //used on split, merge and hole draw
    replacePolygonRequired,
    replacePolygonIdArray = [],
    replacePolygonIdName,
    replacePolygonAction,
    //used whenever attr info tables are requested
    //does not matter from context menu or from global button
    attrInfoForLayerID,
    attrInfoForLayerName,
    attrInfoForLayerType,

    LAYER_ZP_PLOTS = 1,
    LAYER_TEMP_DATA = 2,
    LAYER_KOMAS_PLOTS = 4,
    LAYER_KVS_PLOTS = 5,
    LAYER_ISAK_PLOTS = 6,
    LAYER_FOR_ISAK_PLOTS = 9,
    LAYER_TYPE_LFA = 10,
    LAYER_TYPE_NATURA_2000 = 11,
    LAYER_TYPE_PERMANETELY_GREEN_AREAS = 12,
    LAYER_TYPE_VPS_PASISHTA = 13,
    LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI = 14,
    LAYER_TYPE_VPS_GASKI_ZIMNI = 15,
    LAYER_TYPE_VPS_LIVADEN_BLATAR = 16,
    LAYER_TYPE_VPS_ORLI_LESHOYADI = 17,
    LAYER_TYPE_WORK_LAYER = 19,
    ComboboxData,
	selectedSystem = '900913',
	fromAttributeTables = false,
    allRowsChecked = false,

    MANUAL_SELECT_LAYERS = ['layer_allowable_final', 'layer_allowable', 'layer_lfa', 'layer_pzp', 'layer_natura_2000',
        'layer_vps_orli_leshoyadi', 'layer_vps_livaden_blatar', 'layer_vps_gaski_zimni', 'layer_vps_gaski_chervenogushi',
        'layer_vps_merg', LAYER_TYPE_LFA, LAYER_TYPE_NATURA_2000, LAYER_TYPE_PERMANETELY_GREEN_AREAS, LAYER_TYPE_PERMANETELY_GREEN_AREAS,
        LAYER_TYPE_VPS_PASISHTA, LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI, LAYER_TYPE_VPS_GASKI_ZIMNI, LAYER_TYPE_VPS_LIVADEN_BLATAR,
        LAYER_TYPE_VPS_ORLI_LESHOYADI
        ],
    bingApiKey = "AiWEso3-IjWyX1aZMuep9Sjl62D6FUMqv8qQGpV-kgbW0qWk61to4nrqHh-2D5HL",
    renderer = OpenLayers.Util.getParameters(window.location.href).renderer,
	_flagRightButtonClicked = false;
	_flagMiddleButtonClicked = 0;

	navigationControlDblClickExecuted = false;

if (renderer) {
	OpenLayers.Layer.Vector.prototype.renderers = [renderer];
}

jQuery(function () {
	setUserRights();
    jQuery.fn.combobox.defaults.loader =
        EasyUIRPCLoaders.EasyUIGridCustomLoader.loader;
    jQuery.fn.combobox.defaults.loadFilter =
        EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter;
});

var vectorOptions = {
	name: "drawhole",
	handlerOptions: {
		style: {
			cursor: "pointer",
			pointRadius: 5,
			graphicName: 'square',
			fillColor: '#cccccc',
			fillOpacity: 0.3,
			strokeWidth: 2,
			strokeColor: '#000000',
			strokeOpacity: 1
		}
	}
};

OpenLayers.ProxyHost = Settings.OPEN_LAYERS_PROXY;

//on before unload
jQuery(window).on('beforeunload',function(){
    saveZoomMapCenterLS();
	saveCheckSelectLayersLS();
});

//on body load
jQuery(function(){
    TF.Rpc.Common.CombinedComboboxData.read(true)
        .done(function (data) {
            ComboboxData = data;
			jQuery(window).trigger("comboboxdataloaded", [data]);

            allowable_ekate_cb = data.AllowableEkateCombobox;
            pzp_cb = data.PZPEkateCombobox;
            lfa_cb = data.LFAEkateCombobox;
            initFilterComponents(true);
            initTopicLayerFields();
			initControls();
        })
        .fail(function (error) {
            jQuery.messager.alert('Грешка', error.getMessage(), 'warning');
        });

	initMapPad();
	initDefaultMapControls();
	initPositionDisplay();
	initScaleDisplay();
	initAllLayersTree();
	initMapTools();
	zoomToSavedExtent();
	//init layer for layer features selection
	initVectorLayer();
	initPolygonAddControl();
	// initPolygonImmediateDraw();
	initPolygonModifyControl();
	initPolygonHoleControl();
	initPolygonSplitControl();
	initAllowableLayerClick();
	//init clipping tool
	initClipTools();
	//initPolygonSnappingControl();
	initLayerChangeTabs();

	//disable polygon edit, save, delete
	jQuery('#tool-save').linkbutton('disable');
	jQuery('#tool-edit-geometry').linkbutton('disable');
	jQuery('#tool-delete').linkbutton('disable');
	jQuery('#tool-draw').linkbutton('disable');
	jQuery('#tool-draw-hole').linkbutton('disable');
	jQuery('#tool-merge').linkbutton('disable');
	jQuery('#tool-split').linkbutton('disable');
	jQuery('#tool-auto-split').linkbutton('disable');
	jQuery('#tool-split-polygon').menubutton('disable');
	jQuery('#tool-remove-holes').linkbutton('disable');
	jQuery('#tool-delete-all').linkbutton('disable');
	jQuery('#tool-cross-layers').linkbutton('disable');
	jQuery('#tool-copy-to-layer').menubutton('disable');

	//http://www.jeasyui.com/forum/index.php?topic=2821.0
	jQuery.extend(jQuery.fn.tree.methods,{
		unselect:function(jq,target){
			return jq.each(function(){
				var opts = jQuery(this).tree('options');
				jQuery(target).removeClass('tree-node-selected');
				if (opts.onUnselect){
					opts.onUnselect.call(this, jQuery(this).tree('getNode',target));
				}
			});
		}
	});
	
	updateTotalArea(0);
});

function initMapPad(specific_map_type){
	var chosenMapType,
		tmpFeatures = [];

	//init all map features
	var options = {
		controls: [],
		projection: new OpenLayers.Projection("EPSG:900913")
	};

	//on init map type will not be specified
	if (specific_map_type == undefined)
	{
		chosenMapType = store.get('map_pad') || 1;

		//init map
		map = new OpenLayers.Map('map', options);
	}
	//when map type is changed specific_map_type will have the value of map type
	else {
		chosenMapType = parseInt(specific_map_type);
		store.set('map_pad', chosenMapType);
	}

	var layerMapPad;

	switch (chosenMapType)
	{
		case 2:
			layerMapPad = new OpenLayers.Layer.Bing({
				key: bingApiKey,
				type: "Aerial",
				name: "MapPad"
			});
			break;
		case 3:
			layerMapPad = new OpenLayers.Layer.Bing({
				key: bingApiKey,
				type: "Road",
				name: "MapPad"
			});
			break;
		case 4:
			layerMapPad = new OpenLayers.Layer.Google(
					"MapPad",
					{type: google.maps.MapTypeId.TERRAIN}
			);
			break;
		case 5:
			layerMapPad = new OpenLayers.Layer.Google(
					"MapPad",
					{numZoomLevels: 20}
			);
			break;
		case 6:
			layerMapPad = new OpenLayers.Layer.Google(
					"MapPad",
					{type: google.maps.MapTypeId.HYBRID, numZoomLevels: 20}
			);
			break;
		case 7:
			layerMapPad = new OpenLayers.Layer.Google(
					"MapPad",
					{type: google.maps.MapTypeId.SATELLITE, numZoomLevels: 20}
			);
			break;
		case 8:
			layerMapPad = new OpenLayers.Layer.OSM();
			break;
        case 9:
            layerMapPad = new OpenLayers.Layer.WMS(
				'MapPad',
				imagesWMSServer + "?map=" + '/var/www/satellite_processor/maps/geo_scan.map',
                //wmsServer + "?map=" + mapPath + groupID + '.map',
				{
					layers: 'geo_scan',
				},
                {
                    numZoomLevels: 18
                });
			break;
		default: // default is bing aerial with labels
			layerMapPad = new OpenLayers.Layer.Bing({
				key: bingApiKey,
				type: "AerialWithLabels",
				name: "MapPad"
			});
			break;
	}

	//specific map type will be given only on reload
	//on first load(init) specific map type should be undefined
	if (specific_map_type == undefined)
	{
		map.addLayer(layerMapPad);
	}
	else {
		if (vectors.features.length > 0) {
			tmpFeatures = vectors.features;
			vectors.removeAllFeatures();
		}
		map.addLayer(layerMapPad);
		map.setLayerIndex(map.layers[map.layers.length - 1], 0);
		map.removeLayer(map.layers[1]);
		map.layers[0].redraw(true);
		
		if (tmpFeatures.length > 0) {
			vectors.addFeatures(tmpFeatures);
		}
	}

	map.events.register('mouseup', map, onMapMouseUp);
}

/**
 * 
 * @param OpenLayers.Event 
 */
function onMapMouseUp(e) 
{	
	if (true === OpenLayers.Event.isRightClick(e)) {
		if(isEditToolActive() === true) {
			return;
		}

		setTimeout(function() {
			// executed in timeout function because navigation controll has zoom out on double right click
			if (false === navigationControlDblClickExecuted) {
				let combotree = jQuery('#all-layers-tree').combotree('tree');
				let layerData = combotree.tree('getSelected');
			
				if (!layerData)
				{
					jQuery.messager.alert('Грешка', 'Не е избран активен слой за да видите информация за обект');
					return;
				}

				propertyWindowFunction(e);
			}
		},300);	
	}
}

function isEditToolActive() {
	var options = jQuery('#tool-edit-geometry').linkbutton('options');
	if (options.disabled)
	{
		return false;
	}

	return true;
}

function initDefaultMapControls()
{
	controlNavigation = new OpenLayers.Control.Navigation({
		name: "navigation",
		handleRightClicks: true,
		defaultDblRightClick: function (e) {	
			map.zoomOut();
			navigationControlDblClickExecuted = true;
		},
		documentDrag: false,
		dragPanOptions: {
			panMapStart: function() {
				if(this.kinetic) {
					this.kinetic.begin();
					var viewPort = map.getViewport();
					var mapElement = jQuery(viewPort).find('svg');
					jQuery(mapElement).mousedown(function (evt) {
						if (evt.which == 2 && controlModify.active) {
							controlNavigation.activate();
							vectors.redraw();
						};
					});
					jQuery(mapElement).mouseup(function (evt) {
						if (evt.which == 2 && controlModify.active) {
							controlNavigation.deactivate();
							vectors.redraw();
						};
					});
				}
			}
		}
	});
	map.addControl(controlNavigation);

	map.div.oncontextmenu = noContextMenu;

	controlScaleLine = new OpenLayers.Control.ScaleLine({
		name: "scaleline",
		bottomInUnits: 'km'
	});
	map.addControl(controlScaleLine);

	controlLineMeasure = new OpenLayers.Control.DynamicMeasure(OpenLayers.Handler.Path, {
		name: 'linemeasure',
		persist: true,
		geodesic: true
	});
	map.addControl(controlLineMeasure);

	controlPolygonMeasure = new OpenLayers.Control.DynamicMeasure(OpenLayers.Handler.Polygon, {
		name: "polygonmeasure",
		persist: true,
		geodesic: true
	});
	map.addControl(controlPolygonMeasure);

	controlZoomIn = new OpenLayers.Control.ZoomBox({
		name: "zoomin",
		title: "Zoom in box",
		out: false
	})
	map.addControl(controlZoomIn);

	controlZoomOut = new OpenLayers.Control.ZoomBox({
		name: "zoomout",
		title: "Zoom out box",
		out: true
	});
	map.addControl(controlZoomOut);
}

function noContextMenu(e) {
	if(!e){ //dear IE...
		var e = window.event;
		e.returnValue = false;
	}

	if (!_flagRightButtonClicked && controlSelect !== undefined && controlSelect.active) {
		_flagRightButtonClicked = true;
		controlSelect.deactivate();
		controlNavigation.activate();
		jQuery('#tool-select').linkbutton('unselect');
		jQuery('#tool-panzoom').linkbutton('select');
	} else if (_flagRightButtonClicked && controlNavigation.active && !controlSelect.active) {
		_flagRightButtonClicked = false;
		controlNavigation.deactivate();
		controlSelect.activate();
		jQuery('#tool-select').linkbutton('select');
		jQuery('#tool-panzoom').linkbutton('unselect');
	}

	return false; //Prevent display of browser context menu
}

function zoomToBulgaria()
{
	map.zoomToExtent(new OpenLayers.Bounds.fromString("125190.6162, 4573142.7188, 631370.3273, 4887149.5823").transform(
			new OpenLayers.Projection("EPSG:32635"),
			map.getProjectionObject()));
}



function initPositionDisplay()
{
	jQuery('#select-coord-system-for-map').combobox({
        editable: false,
        data: [{
            "value": '900913',
            "text": "WGS 84",
            "selected": true
        }, {
            "value": '32635',
            "text": "UTM",
        }],
        valueField: 'value',
        textField: 'text',
        multiple: false,
        onChange: function (value) {
            selectedSystem = value;
        }
    });
	map.events.register("mousemove", map, function(e)
	{
		var position = map.getLonLatFromViewPortPx(e.xy);
		if ('900913' == selectedSystem) {
			OpenLayers.Util.getElement("coords").innerHTML = 'x: ' + position.lat + ', y: ' + position.lon;
		} else {
			newPosition = position.clone();
			newPosition.transform('EPSG:900913', 'EPSG:4326');
			OpenLayers.Util.getElement("coords").innerHTML = 'x: ' + newPosition.lat.toPrecision(15) + ', y: ' + newPosition.lon.toPrecision(15);
		}
	});
}

function initScaleDisplay()
{
	map.events.register("zoomend", map, function(e)
	{
		var currentScale = Math.round(map.getScale());

		var scale = jQuery("#scale-denominator input").val(currentScale);
	});
}

var VECTOR_LAYER_STYLE = {
	cursor: "pointer",
	graphicName: 'square',
	pointRadius: 10,
	fillColor: '#cccccc',
	strokeColor: "#4fffff",
	strokeWidth: 3,
	fillOpacity: 0.25,
	label: '${label}',
	labelXOffset: "${xOffset}",
	labelYOffset: "${yOffset}"
};

function initVectorLayer()
{
	var renderer = OpenLayers.Util.getParameters(window.location.href).renderer;
	renderer = (renderer) ? [renderer] : OpenLayers.Layer.Vector.prototype.renderers;

	vectors = new OpenLayers.Layer.Vector("Vector Layer", {
		styleMap: new OpenLayers.StyleMap(new OpenLayers.Style(VECTOR_LAYER_STYLE, {
            isDefault: true,
			context: {
				label: function (feature) {
					if(!feature.attributes.hasOwnProperty('label')) {
						return '';
					}
					return feature.attributes.label;
				}
			}
		})),
		renderers: renderer
	});

	map.addLayer(vectors);

	vectors.events.register("featureadded", this, function(e)
	{
		var combotree = jQuery('#all-layers-tree').combotree('tree');
    	var layerData = combotree.tree('getSelected');

		if (layerData['attributes'].layer_type == LAYER_TEMP_DATA || layerData['attributes'].layer_type == LAYER_TYPE_WORK_LAYER)
		{
			jQuery('#tool-edit-geometry').linkbutton('enable');
			jQuery('#tool-delete').linkbutton('enable');
			jQuery('#tool-draw-hole').linkbutton('enable');
			jQuery('#tool-merge').linkbutton('enable');
			jQuery('#tool-split').linkbutton('enable');
			jQuery('#tool-save').linkbutton('enable');

			if (e.feature.geometry.CLASS_NAME === "OpenLayers.Geometry.MultiPolygon") {
				jQuery('#tool-remove-holes').linkbutton('enable');
			}
		}

		if (layerData['attributes'].layer_type == LAYER_FOR_ISAK_PLOTS)
		{
            jQuery('#tool-draw-hole').linkbutton('enable');
            jQuery('#tool-merge').linkbutton('enable');
            jQuery('#tool-split').linkbutton('enable');
            jQuery('#tool-delete-all').linkbutton('enable');
            jQuery('#tool-edit-geometry').linkbutton('enable');

            if (e.feature.geometry.CLASS_NAME === "OpenLayers.Geometry.MultiPolygon") {
                jQuery('#tool-remove-holes').linkbutton('enable');
            }

			jQuery('#tool-delete').linkbutton('enable');
			jQuery('#tool-save').linkbutton('enable');
			jQuery('#tool-draw').linkbutton('enable');
		}

		if (layerData['attributes'].layer_type == LAYER_ZP_PLOTS)
		{
			jQuery('#tool-edit-geometry').linkbutton('disable');
			jQuery('#tool-delete').linkbutton('enable');
			jQuery('#tool-draw-hole').linkbutton('enable');
			jQuery('#tool-merge').linkbutton('disable');
			jQuery('#tool-split').linkbutton('disable');
			jQuery('#tool-save').linkbutton('disable');

			if (e.feature.geometry.CLASS_NAME === "OpenLayers.Geometry.MultiPolygon") {
				jQuery('#tool-remove-holes').linkbutton('disable');
			}
		}

		if (layerData['attributes'].layer_type == LAYER_KOMAS_PLOTS)
		{
			jQuery('#tool-delete').linkbutton('enable');
		}

		
	});

	vectors.events.register("featuresremoved", this, function(e)
	{
		var combotree = jQuery('#all-layers-tree').combotree('tree');
    	var layerData = combotree.tree('getSelected');
			
		if (vectors.features.length === 0
			&& (layerData != null && layerData['attributes'].layer_type == LAYER_TEMP_DATA))
		{
			jQuery('#tool-save').linkbutton('disable');
			jQuery('#tool-edit-geometry').linkbutton('disable');
			jQuery('#tool-delete').linkbutton('disable');
			jQuery('#tool-draw').linkbutton('enable');
			jQuery('#tool-draw-hole').linkbutton('disable');
			jQuery('#tool-merge').linkbutton('disable');
			jQuery('#tool-split').linkbutton('disable');
			jQuery('#tool-remove-holes').linkbutton('disable');
		}
		if (vectors.features.length === 0) {
			updateTotalArea(0);
		}
	});
}

function deactivateAllControls()
{
	for (var i = 0; i < map.options.controls.length; i++)
	{
		map.options.controls[i].deactivate();
	}

	_flagRightButtonClicked = false;
}

function unselectAll()
{
	jQuery('#tool-panzoom').linkbutton('unselect');
	jQuery('#tool-zoomin').linkbutton('unselect');
	jQuery('#tool-zoomout').linkbutton('unselect');
	jQuery('#tool-measure-line').linkbutton('unselect');
	jQuery('#tool-measure-polygon').linkbutton('unselect');
	jQuery('#tool-edit-geometry').linkbutton('unselect');
	jQuery('#tool-draw').linkbutton('unselect');
	jQuery('#tool-select').linkbutton('unselect');
	jQuery('#tool-draw-hole').linkbutton('unselect');
	jQuery('#tool-split').linkbutton('unselect');

	jQuery('#layer-A-tool-select').linkbutton('unselect');
	jQuery('#layer-B-tool-select').linkbutton('unselect');

	_flagRightButtonClicked = false;
}

function clearPolygonReplaceVariables()
{
	replacePolygonRequired = false;
	replacePolygonIdArray = [];
	replacePolygonIdName = '';
	replacePolygonAction = '';
}

function initPolygonAddControl()
{
	var options = {
		name: "drawpolygon",
		handlerOptions: {
			style: {
				cursor: "pointer",
				pointRadius: 5,
				graphicName: 'square',
				fillColor: '#cccccc',
				fillOpacity: 0.3,
				strokeWidth: 2,
				strokeColor: '#000000',
				strokeOpacity: 1
			},
			holeModifier: "altKey"
		}
	};

	controlDraw = new OpenLayers.Control.DrawFeature(vectors, OpenLayers.Handler.Polygon, options);
	map.addControl(controlDraw);

	controlDraw.events.register("featureadded", this, function(e) {
		//contral draw deactivate
		//control navigation activate
		//disable geometry tools
		//enable save
		jQuery('#tool-save').linkbutton('enable');
		jQuery('#tool-edit-geometry').linkbutton('disable');
		jQuery('#tool-delete').linkbutton('disable');
		jQuery('#tool-draw-hole').linkbutton('disable');
		jQuery('#tool-merge').linkbutton('disable');
		jQuery('#tool-split').linkbutton('disable');
		jQuery('#tool-draw').linkbutton('disable');

		chooseControl('navigation');
		unselectAll();
		jQuery('#tool-panzoom').linkbutton('select');

		var combotree = jQuery('#all-layers-tree').combotree('tree');
    	var layerData = combotree.tree('getSelected');

		if (layerData['attributes'].layer_type == LAYER_TEMP_DATA)
		{
			var featuresCount = vectors.features.length;
			var polygon = vectors.features[featuresCount - 1];
			var area = (polygon.geometry.getGeodesicArea(map.getProjection()) / 1000).toFixed(3);

	        polygon.attributes.label = area + ' дка';
	    	vectors.redraw();
		}
	});

	var winPolygonArea = jQuery('#win-polygon-area');

	winPolygonArea.window({draggable: false});

	var windowArea = jQuery('.panel.window').has(winPolygonArea);
	var shadowWindowArea = windowArea.next();

	vectors.events.register("sketchstarted", this, function() {
		if(controlDraw.active == null || controlDraw.active == false)
		{
			winPolygonArea.window('close');
			return;
		}
		winPolygonArea.window('open');
		winPolygonArea.window('move', {
			top: 80,
			left: 50
		});
	});

	vectors.events.register("sketchmodified", this, handleMeasurements);

	vectors.events.register("sketchcomplete", this, function() {
		if(controlDraw.active == null || controlDraw.active == false)
		{
			return;
		}
		winPolygonArea.window('close');
	});
}

function handleMeasurements(event) {
	if(controlDraw.active == null || controlDraw.active == false)
		{
			return;
		}
	var polygon = event.feature;
	var element = jQuery('#win-polygon-area > input');
	var area = (polygon.geometry.getGeodesicArea(map.getProjection()) / 1000).toFixed(3) + ' дка';

	element.val(area);
	vectors.redraw();
}

function handleModifyMeasurements(event) {
	if(controlModify.active == null || controlModify.active == false)
		{
			return;
		}
	var polygon = event.feature;
	var tmpFeature = polygon.clone();
	var element = jQuery('#win-polygon-area > input');
	var area = (tmpFeature.geometry.transform('EPSG:900913', 'EPSG:32635').getArea() / 1000).toFixed(3) + ' дка';

	element.val(area);
	vectors.redraw();
}

function initPolygonHoleControl()
{
	controlDrawHole = new OpenLayers.Control.DrawFeature(vectors, OpenLayers.Handler.Polygon, vectorOptions);
	map.addControl(controlDrawHole);

	controlDrawHole.events.register("featureadded", this, function(e)
	{
		var featuresCount = vectors.features.length;
		var hole = vectors.features[featuresCount - 1];
		var holeVertices = hole.geometry.getVertices();

		if (holeVertices.length < 3)
		{
			jQuery.messager.alert('Грешка', 'Дупката е неправилна геометрия!');
			vectors.removeFeatures([vectors.features[featuresCount - 1]]);
			return false;
		}

		var currentFeature = vectors.features[0];

		var intersects = true;
		for (var j = 0; j < holeVertices.length; j++)
		{
			if (!currentFeature.geometry.intersects(holeVertices[j]))
			{
				intersects = false;
			}
		}

		if (!intersects)
		{
			jQuery.messager.alert('Грешка', 'Границите на дупката излизат от границите на обекта!');
			//remove last vector feature
			vectors.removeFeatures([vectors.features[featuresCount - 1]]);
			return false;
		}

		var combotree = jQuery('#all-layers-tree').combotree('tree');
    	var activeLayer = combotree.tree('getSelected');

		//save info for polygon replacement in global variable
		replacePolygonRequired = true;
		replacePolygonAction = 'drawhole';

		if (activeLayer.attributes.layer_type == LAYER_KVS_PLOTS || activeLayer.attributes.layer_type == LAYER_TEMP_DATA || activeLayer.attributes.layer_type == LAYER_ISAK_PLOTS || activeLayer.attributes.layer_type == LAYER_FOR_ISAK_PLOTS || activeLayer.attributes.layer_type == LAYER_TYPE_WORK_LAYER)
		{
			replacePolygonIdName = 'gid';
			replacePolygonIdArray.push(currentFeature.attributes.gid);
		}
		else {
			replacePolygonIdName = 'id';
			replacePolygonIdArray.push(currentFeature.attributes.id);
		}

		var reader = new jsts.io.WKTReader();

		var a = reader.read(vectors.features[0].geometry.toString());
		var b = reader.read(vectors.features[featuresCount - 1].geometry.toString());

		var union = a.difference(b);

		var parser = new jsts.io.OpenLayersParser();

		union = parser.write(union);

		vectors.removeAllFeatures();

		var unionOutput = new OpenLayers.Feature.Vector(union, null, {
			fillColor: '#cccccc',
			strokeColor: "#00ff00",
			strokeWidth: 2,
			fillOpacity: 0.25
		});

		vectors.addFeatures(unionOutput);

		jQuery('#tool-save').linkbutton('enable');
		jQuery('#tool-edit-geometry').linkbutton('disable');
		jQuery('#tool-delete').linkbutton('disable');
	});
}

function initPolygonSelectControl(layer_index)
{
	if (controlSelect === undefined)
	{
		//if control has not yet been defined
		//layer will be changed on every tree node selection
		controlSelect = new OpenLayers.Control.GetFeature({
			name: 'selectfeature',
			protocol: OpenLayers.Protocol.WFS.fromWMSLayer(map.layers[layer_index], {
				geometryName: "geom"
			}),
			box: true,
			single: false,
			multiple: false,
			//multipleKey: "ctrlKey",
			click: true,
			clickout: true,
			maxFeatures: 1500,
			clickTolerance: 5
		});
		controlSelect.events.register("featureselected", this, onAddVectorFeatures);
		controlSelect.events.register("featuresselected", this, onAddFeaturesSelected);
		controlSelect.events.register("beforefeaturesselected", this, onBeforeAddVectorFeatures);
		map.addControl(controlSelect);
	} else
	{
		//if control was already initialized only layer should be changed
		//replace wfs layer with currently active layer
		var wfs = OpenLayers.Protocol.WFS.fromWMSLayer(map.layers[layer_index], {
			geometryName: "geom"
		});

		controlSelect.protocol = wfs;
	}

	//Местене на картата със среден бутон на мишката
	jQuery('#map svg').mousedown(function (evt) {
		//Click на среден бутон на мишката
		if (evt.which == 2 && controlSelect.active) {

			_flagMiddleButtonClicked = 1;

			controlSelect.deactivate();
			controlNavigation.activate();
		};

		//Click на ляв бутон на мишката
		if (evt.which == 1 && _flagMiddleButtonClicked == 1){

				controlSelect.activate();
				_flagMiddleButtonClicked = 0;
		}
	});
}

function onBeforeAddVectorFeatures (e) {
	const layer = jQuery('#all-layers-tree').combotree('tree').tree('getSelected');
	
	var confirmation_message = 'Селектирането на нови имоти от картата премахва филтрираните резултати в "Атрибутна информация". При експорт ще се вземат предвид избраните имоти от карта, а не филтрираните от "Атрибутна информация". Желаете ли да продължите?';

    var layer_id = layer.id;
	if (_filteredPlotGidsObj.hasOwnProperty(layer_id) && _filteredPlotGidsObj[layer_id].length > 0 && !selectedPlotsAlertShown) {
		jQuery.messager.confirm('Внимание', confirmation_message, function (r) {
			if (r) {

				jQuery('#extended-select-filtered-plots').prop('checked', false);
				jQuery('#extended-select-filtered-plots').trigger('change');

				fromAttributeTables = false;
				clearCheckedPlotsInAttributeTables();

				e.features.forEach(function (feature) {
					onAddVectorFeatures(feature,true);
				});
				_filteredPlotGidsObj[layer_id] = [];
				addToFilterUsed = false;
				selectedPlotsAlertShown = true;
			} else {
				return false;
			}
		});
	} else {
		return true;
	}
	return false;
}

var selectedFeatureData;
function onAddVectorFeatures(e, multiple) {
	if (multiple) {
		e.feature = e;
	}
	selectedFeatureData = e;
	if(typeof e.feature.attributes.gid !== 'undefined') {
		var existedFeatureGid = vectors.getFeaturesByAttribute('gid', e.feature.attributes.gid);
		if (existedFeatureGid.length) {
			vectors.removeFeatures(existedFeatureGid);
		}
		else
		{
			vectors.addFeatures([e.feature]);
		}
	}
	if(typeof e.feature.attributes.id !== 'undefined') {
		var existedFeatureId = vectors.getFeaturesByAttribute('id', e.feature.attributes.id);
		if (existedFeatureId.length) {
			vectors.removeFeatures(existedFeatureId);
		}
		else{
			vectors.addFeatures([e.feature]);
		}
	}

	var combotree = jQuery('#all-layers-tree').combotree('tree');
    var layerData = combotree.tree('getSelected');

	// HACK _clippingZoom заради пресичане на слоеве
	if (layerData['attributes'].layer_type == LAYER_TEMP_DATA && _clippingZoom === true) {
		selectedFeatureData.feature.attributes.label = (selectedFeatureData.feature.geometry.getGeodesicArea(map.getProjection()) / 1000).toFixed(3) + ' дка';
		selectedFeatureData.feature.attributes.yOffset = 15;
		vectors.redraw();
	};
	
	if(!jQuery('#win-layer-attr-info').window('options').closed){
		jQuery('#tool-attr-info').trigger('click');
	}
}

/**
 *
 * @param e
 */
function onAddFeaturesSelected(e)
{
	const layer = jQuery('#all-layers-tree').combotree('tree').tree('getSelected');
	const features = vectors.features;
	layerInAttrTable = null;

	if (layer.attributes.layer_type == LAYER_KVS_PLOTS) {
		// if layer kvs is selected we will use document area alse geometry area
		initSelectedFeatureTotalDocumentArea(features);
	} else {
		initSelectedFeatureTotalGeometryArea(features);
	}
}

/**
 *
 * @param features
 */
function initSelectedFeatureTotalDocumentArea(features)
{
	layerInAttrTable = null;
	if (!features.length) {
		updateTotalArea(0);

		return;
	}

	let gids = [];
	let selectedFeaturesTotalArea = 0;
	
	features.forEach(function (feature) {
		gids.push(feature.attributes.gid);
	});
	
	let rpcParams = {
		filter_action: 'exec_filter',
		plot_id: gids
	};
	
	TF.Rpc.Map.KVSGrid.read(rpcParams)
	.done(function (data) {
		selectedFeaturesTotalArea += parseFloat(data.footer[0].document_area.toString().replace(',', ''));
		updateTotalArea(parseFloat(selectedFeaturesTotalArea));
	})
	.fail(function (errorObj) {
		updateTotalArea(0);
	});
}

/**
 *
 * @param features
 */
function initSelectedFeatureTotalGeometryArea(features)
{
	let selectedFeaturesTotalArea = 0;
	features.forEach(function (feature) {
		try {
			const tmpFeature = feature.clone();
			const featureArea = parseFloat((tmpFeature.geometry.transform('EPSG:900913', 'EPSG:32635').getArea() / 1000));
			selectedFeaturesTotalArea += featureArea;
		} catch (error) {
			// clear area if no feature is provided or error
			selectedFeaturesTotalArea = 0;
		}
		
		updateTotalArea(selectedFeaturesTotalArea);
	});
}

/**
 *
 * @param area
 */
function updateTotalArea(area)
{
	jQuery('#total-selected-area').text(area.toFixed(3) + ' дка');
}

function initPolygonModifyControl()
{
	controlModify = new OpenLayers.Control.ModifyFeature(vectors, {
		name: 'modifyfeature',
		deferDelete: true,
		clickout: false,
		onModificationStart: function (evt) {
			map.div.oncontextmenu = function (e) {
				controlModify.handleKeypress({ keyCode: 46 });
			}
		},
		onModificationEnd: function (evt) {
			map.div.oncontextmenu = noContextMenu;
		},
		createVertices: true,
		mode: 110,
		tools: [// custom tools
			{
				// to rotate the "angle" attribute of a point by steps of 15 degrees
				geometryTypes: ['OpenLayers.Geometry.Point',
					'OpenLayers.Geometry.MultiPoint'],
				dragAction: function(feature, initialAtt, escale, rotation) {
					var angle = ((initialAtt.angle || 0) - rotation) % 360;
					// force steps of 15 degrres
					angle = Math.floor(angle / 15) * 15;
					feature.attributes.angle = angle;
				},
				style: OpenLayers.Control.ModifyFeature_styles.rotate
			}, {
				// to resize the pointRadius.
				geometryTypes: ['OpenLayers.Geometry.Point',
					'OpenLayers.Geometry.MultiPoint'],
				dragAction: function(feature, initialAtt, escale, rotation) {
					var radius = (initialAtt.radius || 6) * escale;
					feature.attributes.radius = Math.max(6, radius);
				},
				style: OpenLayers.Control.ModifyFeature_styles.resize
			}, {
				// to close a lineString as a ring
				geometryTypes: ['OpenLayers.Geometry.LineString'],
				pressingAction: function(feature) {
					var geometry = feature.geometry;
					geometry.addComponent(geometry.components[0].clone());
				},
				style: {
					label: 'ring',
					title: 'press to close as a ring',
					cursor: "pointer",
					fontSize: '8px',
					fontColor: '#222',
					pointRadius: 10,
					fillColor: '#cccccc',
					strokeColor: '#444444'
				}
			}]
	});

	var winPolygonArea = jQuery('#win-polygon-area');

	winPolygonArea.window({draggable: false});

	var windowArea = jQuery('.panel.window').has(winPolygonArea);
	var shadowWindowArea = windowArea.next();

	vectors.events.register("beforefeaturemodified", this, function() {
		if(controlModify.active == null || controlModify.active == false)
		{
			winPolygonArea.window('close');
			return;
		}

		winPolygonArea.window('open');
		winPolygonArea.window('move', {
			top: 80,
			left: 50
		});
	});

	winPolygonArea.window({
		onOpen: function () {
			if (vectors.features.length > 0) {
				var element = jQuery('#win-polygon-area > input');
				var tmpGeom = vectors.features[0].clone();
				var area = ((tmpGeom.geometry.transform('EPSG:900913', 'EPSG:32635').getArea() / 1000)).toFixed(3) + ' дка';
				element.val(area);
				jQuery('#win-polygon-area').parent().next().css('display', 'none');
			}
		}
	});
	vectors.events.register("featuremodified", this, handleModifyMeasurements);

	vectors.events.register("afterfeaturemodified", this, function() {
		winPolygonArea.window('close');
	});

	map.addControl(controlModify);
}

function initPolygonSplitControl()
{
	controlDrawSplitLine = new OpenLayers.Control.DrawFeature(vectors, OpenLayers.Handler.Path, vectorOptions);
	map.addControl(controlDrawSplitLine);

	controlDrawSplitLine.events.register("featureadded", this, function(e)
	{
		//last feature will be the split line - (featuresCount - 1)
		var reader = new jsts.io.WKTReader();
		var featuresCount = vectors.features.length;
		var isMultiPolygon = false;
		var targetOuterBoundary;
		var holesCount = 0;
        var combotree = jQuery('#all-layers-tree').combotree('tree');
    	var activeLayer = combotree.tree('getSelected');

        //save gid of original plot if active layer is kvs
        var beforeSplitGid;
        if (activeLayer.attributes.layer_type == 5)
        {
            beforeSplitGid = vectors.features[0].attributes.gid;
        }

        //create a helper vector layers
        var renderer = OpenLayers.Util.getParameters(window.location.href).renderer;
        renderer = (renderer) ? [renderer] : OpenLayers.Layer.Vector.prototype.renderers;

        var helpVectors = new OpenLayers.Layer.Vector("Temporary Help Vector Layer", {
            renderers: renderer
        });

        var tempVectors = new OpenLayers.Layer.Vector("Temporary Vector Layer", {
            renderers: renderer
        });

        for(var z=0; z<(featuresCount - 1); z++)
        {
            //convert polygon/multipolygon to jsts geometry
            var target = reader.read(vectors.features[z].geometry.toString());
            var targetBoundaries = target.getBoundary();
            tempVectors.removeAllFeatures();

            //create a helper vector layer

            var holeVectors = new OpenLayers.Layer.Vector("Temporary Hole Vector Layer", {
                renderers: renderer
            });

            //if object has geometries it is MiltiPolygon
            if (targetBoundaries.geometries)
            {
                isMultiPolygon = true;
                holesCount = targetBoundaries.geometries.length - 1;
                targetOuterBoundary = targetBoundaries.geometries[0];

                holes: for (var i = 0; i < target.geometries[0].holes.length; i++)
                {
                    var points = [];
                    points_coords: for (var j = 0; j < target.geometries[0].holes[i].points.length; j++)
                    {
                        var currentPoint = target.geometries[0].holes[i].points[j];
                        points.push(new OpenLayers.Geometry.Point(currentPoint.x, currentPoint.y));
                    }
                    var linearRing = new OpenLayers.Geometry.LinearRing(points);
                    var polygon = new OpenLayers.Feature.Vector(
                            new OpenLayers.Geometry.Polygon([linearRing])
                            );
                    holeVectors.addFeatures(polygon);
                }
            } else
            {
                targetOuterBoundary = targetBoundaries;
            }

            //get target outer boundary and create vector openlayers feature from it
            var boundaryPoints = [];
            boundary_points: for (var i = 0; i < targetOuterBoundary.points.length; i++)
            {
                var currentPoint = targetOuterBoundary.points[i];
                boundaryPoints.push(new OpenLayers.Geometry.Point(currentPoint.x, currentPoint.y));
            }
            var boundaryLinearRing = new OpenLayers.Geometry.LinearRing(boundaryPoints);
            var boundaryPolygon = new OpenLayers.Feature.Vector(
                    new OpenLayers.Geometry.Polygon([boundaryLinearRing])
                    );

            //convect split line to jsts geometry
            var splitLine = reader.read(vectors.features[featuresCount - 1].geometry.toString());
            var splitResult = targetOuterBoundary.union(splitLine);

            var polygonizer = new jsts.operation.polygonize.Polygonizer();

            polygonizer.add(splitResult);

            var parser = new jsts.io.OpenLayersParser();

            //iterate all result polygons
            var polygons = polygonizer.getPolygons();
            for (var i = polygons.iterator(); i.hasNext(); ) {
                var polygon = i.next();

                var feature = new OpenLayers.Feature.Vector(parser.write(polygon));

                var featureVertices = feature.geometry.getVertices();
                var isInsideTarget = true;
                //foreach feature: check if feature is in target polygon outer boundry
                vertices: for (var j = 0; j < featureVertices.length; j++)
                {
                    //check if vertice does not intersect target polygon or is not close to it
                    //if vertice is outside polygon will not be added to vector layer
                    if (boundaryPolygon.geometry.intersects(featureVertices[j]) === false
                            && boundaryPolygon.geometry.distanceTo(featureVertices[j]) > 0.1)
                    {
                        isInsideTarget = false;
                        break vertices;
                    }

                    if(j < featureVertices.length - 1)
                    {
                        //create point in the middle of current and next points
                        var x = (featureVertices[j].x + featureVertices[j+1].x) / 2;
                        var y = (featureVertices[j].y + featureVertices[j+1].y) / 2;
                        var middlePoint = new OpenLayers.Geometry.Point(x, y);
                    }
                    else
                    {
                        //craete point in the middle of current and first points
                        var x = (featureVertices[j].x + featureVertices[0].x) / 2;
                        var y = (featureVertices[j].y + featureVertices[0].y) / 2;
                        var middlePoint = new OpenLayers.Geometry.Point(x, y);
                    }

                    //check if middle point is inside or close to boundary polygon boundaries
                    if (boundaryPolygon.geometry.intersects(middlePoint) === false
                            && boundaryPolygon.geometry.distanceTo(middlePoint) > 0.1)
                    {
                        isInsideTarget = false;
                        break vertices;
                    }
                }

                //only if polygon is inside
                if (isInsideTarget === true)
                {
                    tempVectors.addFeatures([feature]);
                    isInsideTarget = true;
                }
            }

            //save info for polygon replacement in global variable
            replacePolygonRequired = true;
            replacePolygonAction = 'split';
            if (activeLayer.attributes.layer_type == LAYER_KVS_PLOTS || activeLayer.attributes.layer_type == LAYER_TEMP_DATA || activeLayer.attributes.layer_type == LAYER_ISAK_PLOTS || activeLayer.attributes.layer_type == LAYER_FOR_ISAK_PLOTS || activeLayer.attributes.layer_type == LAYER_TYPE_WORK_LAYER)
            {
                replacePolygonIdName = 'gid';
                replacePolygonIdArray.push(vectors.features[z].attributes.gid);
            }
            else {
                replacePolygonIdName = 'id';
                replacePolygonIdArray.push(vectors.features[z].attributes.id);
            }

            //###
            //Cut Holes
            //###
            var tempFeatures = new Array();

            holes: for (var i = 0; i < holeVectors.features.length; i++)
            {
                tempFeatures = new Array();

                features: for (var j = 0; j < tempVectors.features.length; j++)
                {
                    var targetFeature = reader.read(tempVectors.features[j].geometry.toString());
                    var holeFeature = reader.read(holeVectors.features[i].geometry.toString());
                    var diff = targetFeature.difference(holeFeature);
                    diff = parser.write(diff);

                    var diffOutput = new OpenLayers.Feature.Vector(diff);
                    tempFeatures.push(diffOutput);
                }

                tempVectors.removeAllFeatures();
                tempVectors.addFeatures(tempFeatures);
            }

            //###
            //End of Cut Holes
            //###

            tempFeatures = new Array();
            for(var i = 0; i < tempVectors.features.length; i++)
            {
                var geometry = tempVectors.features[i].geometry;

                if(geometry.CLASS_NAME == 'OpenLayers.Geometry.MultiPolygon')
                {
                    var jstsPolygon = reader.read(geometry.toString());
                    var polygonizer = new jsts.operation.polygonize.Polygonizer();

                    polygonizer.add(jstsPolygon);

                    var parser = new jsts.io.OpenLayersParser();

                    //iterate all result polygons
                    var polygons = polygonizer.getPolygons();
                    for (var j = polygons.iterator(); j.hasNext(); ) {
                        var polygon = j.next();
                        var feature = new OpenLayers.Feature.Vector(parser.write(polygon));
                        tempFeatures.push(feature);
                    }
                } else {
                    tempFeatures.push(tempVectors.features[i]);
                }
            }

            tempVectors.removeAllFeatures();
            tempVectors.addFeatures(tempFeatures);

            helpVectors.addFeatures(tempVectors.features);
        }

        vectors.removeAllFeatures();

		for(var i = 0; i < helpVectors.features.length; i++)
        {
        	helpVectors.features[i].attributes.label = (helpVectors.features[i].geometry.getGeodesicArea('EPSG:900913')/1000).toFixed(3).toString() + ' дка';
        }

        vectors.addFeatures(helpVectors.features);

		if (vectors.features.length > 0)
		{
            if (activeLayer.attributes.layer_type == 5)
            {
                vectors.features[0].attributes.kvs_action = 'split';
                vectors.features[0].attributes.gid = beforeSplitGid;
            }
            else
            {
                //deactivate control split
                controlDrawSplitLine.deactivate();
//              controlSnapping.deactivate();
                unselectAll();
                chooseControl('navigation');
                jQuery('#tool-split').linkbutton('disable');
            }

			//activate save button and deactivate other buttons
			jQuery('#tool-save').linkbutton('enable');
			jQuery('#tool-edit-geometry').linkbutton('disable');
			jQuery('#tool-delete').linkbutton('disable');

			jQuery('#tool-draw').linkbutton('disable');
			jQuery('#tool-draw-hole').linkbutton('disable');

			jQuery('#tool-merge').linkbutton('disable');

		}
	});
}

function reloadLayerAfterFeaturesChange(data)
{
    var combotree = jQuery('#all-layers-tree').combotree('tree');
    var selected = combotree.tree('getSelected');
    saveZoomMapCenterLS();
	saveCheckSelectLayersLS();
	if (data.error) {
        return false;
	}

    combotree.tree('update', {
        target: selected.target,
        attributes: {
            extent: data.extent,
            is_system: selected['attributes'].is_system,
            layer_name: selected['attributes'].layer_name,
            layer_type: selected['attributes'].layer_type,
            level: selected['attributes'].level,
            name: selected['attributes'].name,
            farming: selected['attributes'].farming,
            farming_id: selected['attributes'].farming_id,
            year: selected['attributes'].year,
            year_id: selected['attributes'].year_id
        }
	});

    reloadLayerByName(selected['attributes'].layer_name);
    vectors.removeAllFeatures();
	_flagRightButtonClicked = false;
}

function reloadLayerByName(layer_name)
{
	for (var i = 0; i < map.layers.length; i++)
	{
		if (map.layers[i].name.indexOf(layer_name) != -1)
		{
			map.layers[i].redraw(true);
		}
	}
}

function getLayerIndexByName(layer_name)
{
	var find;

	for (var i = 0; i < map.layers.length; i++)
	{
		if (map.layers[i].name.indexOf(layer_name) != -1)
		{
			return i;
		}
	}

	//case no layer was found
	return false;
}

function showKVSEditNotification(sender, parameter)
{
	if(parameter)
    {
        jQuery.messager.alert('Съобщение', 'Редактираните имоти участват в следните договори: ' + parameter.c_num + '. Моля проверете ги.<br/>'
                                + '<a href="index.php?page=Contracts.Home&contract_id=' + parameter.c_id + '" target="_blank">Отиди в договори</a>');
    }
}

function onCopyComplete(data) {

 	if (data['existing_prc_name']) {
 		jQuery.messager.alert('Грешка', 'Парцел с име "'+parameter.existing_prc_name+'" вече съществува!','warning');
 		return false;
 	}
	saveCheckSelectLayersLS();
	jQuery('#all-layers-tree').combotree('loadRpc');

	var pager = jQuery('#plots-tables').datagrid('getPager');
	pager.pagination('select', 1);
}

function initAllowableLayerClick() {
	var selectButton = jQuery('#tool-select');
	var selectButtonA = jQuery('#layer-A-tool-select');
	var selectButtonB = jQuery('#layer-B-tool-select');
	var clippingLayerA = jQuery('#layer-A');
	var clippingLayerB = jQuery('#layer-B');
	var clippingWindow = jQuery('#win-clipping');

	map.events.register('click', map, function (e) {

		var clippingWindowOpts = clippingWindow.window('options');
		var combotree = jQuery('#all-layers-tree').combotree('tree');
    	var selectedLayer = combotree.tree('getSelected');
		var selectButtonOpts = selectButton.linkbutton('options');
		var selectButtonAOpts = selectButtonA.linkbutton('options');
		var selectButtonBOpts = selectButtonB.linkbutton('options');
		var selectedLayerType = null;
		var selectedLayerName = '';

		if (!clippingWindowOpts.closed) {
			if (selectButtonAOpts.selected) {
				selectedLayerType = clippingLayerA.combobox('getValue');
			}

			if (selectButtonBOpts.selected) {
				selectedLayerType = clippingLayerB.combobox('getValue');
			}
			selectedLayerName = selectedLayerType;
		} else if (selectedLayer) {
			selectedLayerType = selectedLayer.attributes.layer_type;
			selectedLayerName = selectedLayer.attributes.parent_layer_name ?? selectedLayer.attributes.layer_name;
		}

		var manulaSelectLayers = ['layer_allowable_final', 'layer_allowable', 'layer_lfa', 'layer_pzp', 'layer_natura_2000',
                    'layer_vps_orli_leshoyadi', 'layer_vps_livaden_blatar', 'layer_vps_gaski_zimni', 'layer_vps_gaski_chervenogushi', 'layer_vps_merg',
		 LAYER_TYPE_LFA, LAYER_TYPE_NATURA_2000, LAYER_TYPE_PERMANETELY_GREEN_AREAS, LAYER_TYPE_PERMANETELY_GREEN_AREAS,
                 LAYER_TYPE_VPS_PASISHTA = 13, LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI, LAYER_TYPE_VPS_GASKI_ZIMNI, LAYER_TYPE_VPS_LIVADEN_BLATAR, LAYER_TYPE_VPS_ORLI_LESHOYADI];

		if (manulaSelectLayers.indexOf(selectedLayerType) == -1) {
			if ((selectButtonOpts.selected || selectButtonAOpts.selected || selectButtonBOpts.selected) && !controlSelect.active) {
				controlSelect.activate();
			}

			return;
		}

		if (!(selectButtonOpts.selected || selectButtonAOpts.selected || selectButtonBOpts.selected)) {
			return;
		}

		if (controlSelect.active) {
			controlSelect.deactivate();
		}

		controlSelect.protocol  = null;

		var clickPoint = map.getLonLatFromPixel(e.xy).transform('EPSG:900913', 'EPSG:32635');

		var requestObject = new Object();
		requestObject = {
			lat: clickPoint.lat,
			lon: clickPoint.lon,
			extent: map.getExtent().transform('EPSG:900913','EPSG:32635').toArray(),
			table_name: selectedLayerName
		};

		TF.Rpc.Map.MapTools.selectPolygon(requestObject)
		.done(function (data) {
			selectPlotFromLayer(data);
		})
		.fail(function (data) {

		});
	});
}




function selectPlotFromLayer(data) {

	if (!data) {
		return;
	}
	var in_options = {
		'internalProjection': map.baseLayer.projection,
		'externalProjection': new OpenLayers.Projection("EPSG:32635")
	};

	var wktFromatter = new OpenLayers.Format.WKT(in_options);
	var feature = wktFromatter.read(data.geom);

	var idField = 'gid';
	if (data.hasOwnProperty('id')) {
		idField = 'id';
	}
	var alreadySelFeatures = vectors.getFeaturesByAttribute(idField, data[idField]);
	if (alreadySelFeatures.length) {
		vectors.removeFeatures(alreadySelFeatures);
	}
	else
	{
		feature.attributes[idField] = data[idField];
		vectors.addFeatures(feature);
	}

	if(!jQuery('#win-layer-attr-info').dialog('options').closed){
		jQuery('#tool-attr-info').trigger('click');
	}
}

function initLayerChangeTabs() {
	jQuery('#layerCustomizationTabs').tabs({
		border:false,
		onSelect:function(title, index){
			if(index === 0)
			{
				jQuery('#layer-change-win').window('resize',{
	                height: 465
	            });
			}
			else{
				jQuery('#layer-change-win').window('resize',{
	                height: 612
	            });
				initTopicLayerGrid();
				initTopicLayerFields();
			}
		}
	});
}

function initTopicLayerGrid() {
	jQuery('#topic-layer-grid').datagrid({
        singleSelect: false,
        checkOnSelect: true,
        selectOnCheck: true,
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		iconCls: 'icon-edit-geometry',
		pageSize: 50,
		fitColumns: true,
		border: false,
		sortName: 'area',
		sortOrder: 'desc',
		idField: 'ime_subekt',
		columns: [
			[
                {
                    field: 'egn_subekt',
                    checkbox: true,
                    styler: function(value, row, index) {
                        return 'background-color:'+row.color+'; color: #fff';
                    }
                },
				{
					field: 'rowColor',
					title: '<b>Цвят</b>',
					sortable: true,
					width: 75,
					styler: function(value, row, index) {
						return 'background-color:'+row.color+'; color: #fff';
					}
				}, {
					field: 'ime_subekt',
					title: '<b>Име субект</b>',
					sortable: true,
					width: 350
				}, {
					field: 'number',
					title: '<b>Брой<br/>имоти</b>',
					sortable: true,
					width: 90
				}, {
					field: 'area',
					title: '<b>Площ<br/>(дка)</b>',
					sortable: true,
					width: 150
				}
			]
		],
		pagination: false,
		rownumbers: true,
		onLoadSuccess: function () {
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function initTopicLayerFields() {
    var topicLayerStylerComboboxData = ComboboxData.TopicLayerStylerCombobox,
        oszEkateComboboxData = ComboboxData.OSZEkateCombobox;

	jQuery('#topic-layer-styler').combobox({
        data: topicLayerStylerComboboxData,
		editable: false,
		textField: 'name',
		valueField: 'id',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

	jQuery('#topic-layer-ekate').combobox({
        data: oszEkateComboboxData,
		editable: false,
        valueField: 'ekate',
        textField: 'text',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function initTopicLayerSearch() {
	var selectedType = jQuery('#topic-layer-styler').combobox('getValue');
	var selectedEkate = jQuery('#topic-layer-ekate').combobox('getValue');

	if (selectedEkate == 0) {
		return;
	}
	var banId = [];

    var selected = jQuery('#topic-layer-grid').datagrid('getSelections');
    if (selected.length > 0) {
        for (var i = 0; i < selected.length; i++) {
            banId.push(JSON.stringify(selected[i]));
        }
	}

    jQuery('#topic-layer-grid').datagrid('uncheckAll');
	jQuery('#topic-layer-grid').datagrid({
		rpcParams: [{
			type: selectedType,
			ekate: selectedEkate,
            selected: banId
		}],
		url: 'index.php?map-rpc=map-tools',
		rpcMethod: 'initTopicLayer',
		onLoadSuccess: function (data) {
            jQuery('#filterSelected').linkbutton({
                text:'Зареди'
            });
			initTopicLayerLegend(data);
			zoomToTopicLayerExtent(data);
			jQuery('#topic-layer-legend').window('expand',true);
			loadTopicKVSLayer(selectedType,selectedEkate, data.total);
			jQuery('#tool-export-map-view-submenu').show();
		},
        onClickRow: function (index,row) {
            var selected = jQuery(this).datagrid('getSelections');
            jQuery('#filterSelected').linkbutton({
                text: (selected.length > 0) ? 'Филтър' : 'Зареди'
            });
        }
	});
}

function initTopicLayerLegend(data) {
	if (data.total > 0) {
		var windowWidth = jQuery(window).width();
		var legendWindow = jQuery('#topic-layer-legend');

		legendWindow.window('open');
		legendWindow.window('move',{
			top:80,
			left: windowWidth - 230 - 20
		});
	}

	jQuery('#topic-layer-legend-datagrid').datagrid({
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		iconCls: 'icon-edit-geometry',
		pageSize: 50,
		fitColumns: true,
		border: false,
		data: data,
		rpcMethod: 'initTopicLayer',
		sortName: 'area',
		sortOrder: 'desc',
		idField: 'egn_subekt',
		columns: [
			[
				{
					field: 'rowColor',
					title: '<b>Цвят</b>',
					sortable: false,
					width: 110,
					styler: function(value, row, index) {
						return 'background-color:'+row.color+'; color: #fff';
					}
				}, {
					field: 'ime_subekt',
					title: '<b>Име субект</b>',
					sortable: false,
					width: 360
				}
			]
		],
		pagination: false,
		rownumbers: false
	});
}

function removeAllLayers() {
	var mLayer = map.layers;
	var remLayers = [];

	for (var i=0; i<mLayer.length; i++) {
		if(mLayer[i].name != "MapPad" && mLayer[i].name != "Vector Layer"){
			remLayers.push(mLayer[i]);
		}
	}

	for (var i=0; i<remLayers.length; i++) {
		map.removeLayer(remLayers[i]);
	}
}

function loadTopicKVSLayer(type, ekate, total) {

	removeAllLayers();
	var legendWindow = jQuery('#topic-layer-legend');
	var layer_type = "";

	switch (type) {
		case '1':
			layer_type = 'topic_layer_kvs_by_owner_name';
			legendWindow.window('setTitle','Легенда - Име собственик');
		break;
		case '2':
			layer_type = 'topic_layer_kvs_by_tenant_name';
			legendWindow.window('setTitle','Легенда - Име арендатор');
		break;
		case '3':
			layer_type = 'topic_layer_kvs_by_agreement';
			legendWindow.window('setTitle','Легенда - Име собственик');
		break;
		case '4':
			layer_type = 'topic_layer_kvs_by_category';
			legendWindow.window('setTitle','Легенда - Категории');
		break;
		case '5':
			layer_type = 'topic_layer_kvs_by_ntp';
			legendWindow.window('setTitle','Легенда - НТП');
		break;
		case '6':
			layer_type = 'topic_layer_kvs_by_ownership';
			legendWindow.window('setTitle','Легенда - Тип собственост');
		break;
	}

	var layerData = new OpenLayers.Layer.WMS(
		'layer_kvs',
		wmsServer + "?map=" + mapPath + groupID + '.map',
		{
			layers: 'layer_kvs',
			format: 'image/png',
			transparent: "true"
		});
	map.addLayer(layerData);
	layerData.redraw(true);

	pushVectorLayerToTopPosition();

	if (total > 0) {
		var layerData = new OpenLayers.Layer.WMS(
			layer_type,
			wmsServer + "?map=" + mapPath + groupID + '.map',
			{
				layers: layer_type,
				format: 'image/png',
				transparent: "true"
			});
		map.addLayer(layerData);
		layerData.redraw(true);

		pushVectorLayerToTopPosition();
	}
}

function manualRefreshTopicLayerKVSData() {
	TF.Rpc.Map.MapTools.refreshTopicLayerKVSViews()
	.done(function (data) {

	})
	.fail(function (errorObj) {
		jQuery.messager.alert('Грешка',errorObj.getMessage(),'warning');
	});
}

jQuery(function () {
	jQuery('#property-tabs').tabs({
		border:false,
		onSelect:function(title){
		}
	})
	.tabs('disableTab', 1); //Disabling OSZ tab by default.
});


function removeTopicKVSLayers() {
	var combotree = jQuery('#all-layers-tree').combotree('tree');
    var layerData = combotree.tree('getSelected');
	var mLayer = map.layers;
	var remLayers = [];

	for (var i=0; i<mLayer.length; i++) {
		if(mLayer[i].name != "MapPad" && mLayer[i].name != "Vector Layer" && (mLayer[i].name.indexOf("topic_layer_kvs") >= 0)){
			remLayers.push(mLayer[i]);
		}
	}

	for (var i=0; i<remLayers.length; i++) {
		map.removeLayer(remLayers[i]);

	}

	if(layerData) {
		loadMapLayer(layerData['attributes'].layer_name, layerData['attributes'].extent);
	}

    jQuery('#tool-export-map-view-submenu').hide();
}

function zoomToTopicLayerExtent(data) {

	if (data.total > 0) {
		map.zoomToExtent(new OpenLayers.Bounds.fromString(data['rows'][0]['extent']).transform(
			new OpenLayers.Projection("EPSG:32635"),
			map.getProjectionObject()));
	} else {
		zoomToBulgaria();
	}
}

function saveZoomMapCenterLS() {
	var zoom = map.getZoom();
	var map_center = map.getCenter();

	store.set('zoom', zoom);
	store.set('map_center', map_center);
}

function zoomToSavedExtent() {
	var zoom = store.get('zoom');
	var lonLatCenter = store.get('map_center');

	if(zoom && lonLatCenter) {
		var lon = lonLatCenter.lon;
		var lat = lonLatCenter.lat;
		var lonlat = new OpenLayers.LonLat(lon, lat);
		var zoomIn = zoom + 1;

		map.setCenter(lonlat, zoomIn);
		return;
	}

	zoomToBulgaria();
}