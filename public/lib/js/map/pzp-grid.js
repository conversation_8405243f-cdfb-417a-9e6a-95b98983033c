function initPZPGrid(layer_id)
{
	var checkedElement;
	var array = [];

	for (var i=0; i<vectors.features.length; i++) {
		array.push(vectors.features[i].attributes.gid);
	};

	jQuery('#plots-tables').datagrid({
		url: 'index.php?map-rpc=remote-layer-datagrid',
		rpcParams: [{
			layer_id: layer_id,
			layer_type: 12,
			gids: array
		}],
		singleSelect: true,
        checkOnSelect: false,
        selectOnCheck: false,
		iconCls: 'icon-plot',
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		pageSize: 20,
		fit: true,
		showFooter: true,
        sortName: 'gid',
		sortOrder: 'asc',
		idField: 'gid',
		pagination: true,
		rownumbers: true,
		width: '100%',
		border: false,

		frozenColumns: [[
				{
					field: 'ck',
					checkbox: true
				}
			]],
		columns: [[
				{
					field: 'imotcode',
					title: '<b>Идентификатор</b>',
					sortable: true,
					width: 110
				},
				{
					field: 'zeml',
					title: '<b>Землище</b>',
					sortable: true,
					width: 115
				},
				{
					field: 'sharea',
					title: '<b>Площ на имота(дка)</b>',
					sortable: true,
					width: 125
				},
				{
					field: 'pzp_area',
					title: '<b>Площ на имота в ПЗП(дка)</b>',
					sortable: true,
					width: 165
				},
			]],
		onLoadSuccess: function() {
			jQuery('#plots-tables').datagrid('uncheckAll');
            jQuery('#select-filtered-plots').trigger('change');
		},
		onUnselect: function(rowIndex, rowData) {

		},
		onSelect: function(rowIndex, rowData) {
			checkedElement = rowData;
			displayFeatureSelection(rowData.st_astext, rowData);
		},
		toolbar: '#attr-tables-toolbar',
		onBeforeLoad: function() {
			jQuery('#plots-tables').datagrid('clearChecked');
		},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}
