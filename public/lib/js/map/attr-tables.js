var zp_layer_type = {
		  layer_type: LAYER_ZP_PLOTS
};
var for_isak_layer_type = {
		  layer_type: LAYER_FOR_ISAK_PLOTS
};

var tmp_layer_type = {
	layer_type: 2
};
var work_layer_type = {
	layer_type: LAYER_TYPE_WORK_LAYER
};

var array = [];

var _filteredRpcParams = {};

var _filteredPlotGidsObj = {};
var selectedPlotsAlertShown = false;
var pageNumber = 0;

var checkAllFiltered = false;

jQuery(function () {
	for (var i=0; i<vectors.features.length; i++) {
		array.push(vectors.features[i].attributes.gid);
	}

	jQuery('#attr-tables-copy-submenu').on('click', '.js-copy-layer-filtered-btn', copyFilteredLayer);
	jQuery('#attr-tables-copy-submenu').on('click', '.js-copy-layer-btn', copyCheckedLayer);
	jQuery('#attr-tables-copy-submenu').on('click', '.js-copy-layer-btn-for-isak', for_isak_layer_type, loadLayerSelection);
	jQuery('#attr-tables-copy-submenu').on('click', '.js-copy-layer-btn-filtered-for-isak', for_isak_layer_type, loadLayerSelection);
	jQuery('#attr-tables-copy-submenu').on('click', '.js-copy-layer-btn-zp', zp_layer_type, loadLayerSelection);
	jQuery('#attr-tables-copy-submenu').on('click', '.js-copy-layer-btn-filtered-zp', zp_layer_type, loadLayerSelection);
	jQuery('#attr-tables-copy-submenu').on('click', '.js-copy-layer-btn-work-layer', work_layer_type, loadLayerSelection);
	jQuery('#attr-tables-copy-submenu').on('click', '.js-copy-layer-btn-filtered-work-layer', work_layer_type, loadLayerSelection);

	sessionStorage.removeItem('checkedPlots');

	jQuery('#plots-tables').datagrid({
		onLoadSuccess: function(data) {
			if(data.total !== undefined && data.total === 0){
				jQuery.messager.alert('Внимание', 'Не са открити записи.', 'warning');
			}

			pageNumber = jQuery(this).datagrid('options').pageNumber;

			var pager = jQuery('#plots-tables').datagrid('getPager');
			pager.pagination({
				onChangePageSize: function () {
					sessionStorage.removeItem('checkedPlots');
				}
			});

			if(data['rows'] && data['rows'][0] && data['rows'][0]['currentFilterCount'] === 0 && pageNumber === 1) {
				jQuery.messager.alert('Внимание', 'Не са открити записи за въведения филтър', 'warning');
			}
			jQuery('#main-kvs-table-layout').layout('resize');
		},
		onCheck: function (node, row) {
			var session = sessionStorage.checkedPlots ? JSON.parse(sessionStorage.checkedPlots) : {};
			var idField = (attrInfoForLayerType == LAYER_ZP_PLOTS) ?  'id' :  'gid' ;

			if(!session[pageNumber]) session[pageNumber] = {};

			session[pageNumber][node] = row[idField];
			sessionStorage.setItem('checkedPlots', JSON.stringify(session));
		},
		onUncheck: function (node) {
			var checks = sessionStorage.getItem('checkedPlots');
			if(checks) {
				 var obj = JSON.parse(checks);
				 if(obj[pageNumber]) {
					 if(obj[pageNumber][node]) delete obj[pageNumber][node];
					 if(jQuery.isEmptyObject(obj[pageNumber])) delete obj[pageNumber];
					 sessionStorage.setItem('checkedPlots', JSON.stringify(obj));
				 }
			}
		},
		onUncheckAll: function (){
			unCheckAll();
		},
		onCheckAll: function (rows) {
			const rowsCount = rows.length;
			let pageNum = pageNumber;

			const data = jQuery('#plots-tables').datagrid('getData');
			if (checkAllFiltered === true && data.filtered_gids.length) {
				// replace page rows with filtered plots in.
				// checkAll method checks only datagrid page records
				// filtered plots will contain all records in datagrid, not only page related
				rows = data.filtered_gids;
			}

			let session = sessionStorage.checkedPlots ? JSON.parse(sessionStorage.checkedPlots) : {};

			rows.forEach(function(row, index) {
				var idField = (attrInfoForLayerType == LAYER_ZP_PLOTS) ?  'id' :  'gid' ;
				if (index !=0 && index % rowsCount == 0) {
					pageNum ++;
				}

				if(!session[pageNum]) session[pageNum] = {};
				if(!session[pageNum][index]) session[pageNum][index] = row[idField] ? row[idField] : row;
			});

			sessionStorage.setItem('checkedPlots', JSON.stringify(session));
		},
		onBeforeLoad: function (rows) {
			sessionStorage.removeItem('checkedPlots');
		}
	});

	function unCheckAll(){
		var checks = sessionStorage.getItem('checkedPlots');
		if (checks) {
			var obj = JSON.parse(sessionStorage.checkedPlots);
			if (obj[pageNumber]) {
				delete obj[pageNumber];
				sessionStorage.setItem('checkedPlots', JSON.stringify(obj));
			}
		}
	}

	jQuery('#btn-save-layer-copy').bind('click', saveLayerCopy);
	jQuery('#attr-cols-edit-btn').bind('click', initWorkLayerColumnsDefinitions);
	jQuery('#btn-save-layer-definitions').bind('click', saveWorkLayerColumnsDefinitions);

	jQuery('#attr-tables-kvs-filter-btn').bind('click', function (){
		clearFilterPlotValues();
		const isFilterEmpty = checkForEmptyAppliedFilters();
		if(isFilterEmpty) {
			jQuery('#addToFilterBtn').linkbutton('disable');
			jQuery('#clearFilterBtn').linkbutton('disable');
		} else {
			jQuery('#addToFilterBtn').linkbutton('enable');
			jQuery('#clearFilterBtn').linkbutton('enable');
		}
		jQuery('#plots-filters-layout').layout('resize', {width: 870});
		jQuery('#win-plots-filter').window('resize', {width: 935}).window('center');

		jQuery('#win-plots-filter').window('open');
		jQuery('#chooseAllCheckBox').prop('checked',false);

		chooseAll(jQuery('#chooseAllCheckBox'));
	});

    jQuery('#select-filtered-plots').change(function() {
        if(this.checked) {
            var gridOptions = jQuery('#plots-tables').datagrid('options');

            var url = gridOptions.url.split("=");
            var rpcApiId = url[1];

            var reqObj = new TF.Rpc('map-rpc');

            reqObj.request(rpcApiId, 'read', gridOptions.rpcParams)
            .done(function(data) {
                selectFilteredPlots(data.rows);
            });
        }
        else {
            var layer = map.getLayersByName('Filtered plots');

            if(layer[0]) {
                map.removeLayer(layer[0]);
            }
        }
	});
	jQuery(window).bind("comboboxdataloaded", initModemWindow);
});

function initModemWindow(e, comboboxData) {
	var modemsCombobox = jQuery("#select-modem").combobox({
    data: comboboxData.ModemsCombobox,
    valueField: "value",
    textField: "name",
    loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
    loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
  });
	jQuery("#btn-export-modem").bind("click", jQuery.proxy(exportLayerForTfModem, window, modemsCombobox));
}

function exportLayerForTfModem(modemsCombobox, e) {
	var modemId = modemsCombobox.combobox("getValue");
	if (!modemId) {
		jQuery.messager.alert('Грешка', 'Моля изберете устройство.');
		return;
	}

	var obj = prepareLayerExportData("exportTrimble", false, false, false);
	if(!obj) return;

	obj.device_id = modemId;
	TF.Rpc.Map
        .MapExportLayer
        .exporToModem(obj)
        .done(function (dataObj) {
			jQuery.messager.alert("Информация", "Експортът беше завършен успешно!", "info", function (params) {
				var win = jQuery("#win-choose-modem-devices");
        		win.window("close");
			});
        })
        .fail(function (errorObj) {
            jQuery.messager.alert('Грешка', errorObj.getMessage());
        });
}

function showExportToModemWin(params) {
	var win = jQuery("#win-choose-modem-devices");
  	win.window("open");
}

function filterAttrTables(clear_filter){
	var combotree = jQuery('#all-layers-tree').combotree('tree');
	var layer = combotree.tree('getSelected');
	const pageNumber = 1; //always reset page when filter is applied
	vectors.removeAllFeatures();
	jQuery('#plots-tables').datagrid('unselectAll');
	jQuery('#plots-tables').datagrid("clearChecked");
	const gridOptions = jQuery('#plots-tables').datagrid('options');
	sessionStorage.removeItem('checkedPlots');
	var action = 'exec_filter';
	if (clear_filter === false)	{
		action = 'add_to_filter';
	}

	switch (layer.attributes.layer_type)
	{
		case LAYER_ZP_PLOTS: //zp
			if (jQuery('#zp-filter-ekate').combobox('getValue').length !== 0 ||
			 jQuery('#zp-filter-obrabotki').val().length !== 0 ||
			 jQuery('#zp-filter-dobivi').val().length !== 0 ||
			 jQuery('#zp-filter-napoqvane').val().length !== 0 ||
			 jQuery('#zp-filter-polivki').val().length !== 0 ||
			 jQuery('#zp-filter-polzvatel').val().length !== 0 ||
			 jQuery('#zp-filter-isak').val().length !== 0 ||
			 jQuery('#zp-filter-culture').combobox('getValue').length !== 0) {

				_filteredRpcParams = {
					layer_id: layer.id,
					layer_type:1,
					ekatte: jQuery('#zp-filter-ekate').combobox('getValue'),
					culture: jQuery('#zp-filter-culture').combobox('getValue'),
					obrabotki: jQuery('#zp-filter-obrabotki').val(),
					dobivi: jQuery('#zp-filter-dobivi').val(),
					napoqvane: jQuery('#zp-filter-napoqvane').val(),
					polivki: jQuery('#zp-filter-polivki').val(),
					polzvatel: jQuery('#zp-filter-polzvatel').val(),
					isak_prc_uin: jQuery('#zp-filter-isak').val(),
					action: action
				};

				jQuery('#plots-tables').datagrid({
					rpcParams: [_filteredRpcParams],
					pageNumber: pageNumber
				});
			} else
			{
				jQuery.messager.alert('Грешка', 'Моля въведете параметри за филтириране.');
			}

			break;

		case LAYER_KOMAS_PLOTS: //kms
			if (jQuery('#kms-filter-prc-bzz').val().length !== 0 ||
				jQuery('#kms-filter-ekate').combobox('getValue').length !== 0 ||
				jQuery('#kms-filter-culture').combobox('getValue').length !== 0) {

				_filteredRpcParams = {
					layer_type:4,
					layer_id: layer.id,
					ekatte: jQuery('#kms-filter-ekate').combobox('getValue'),
					crop_code: jQuery('#kms-filter-culture').combobox('getValue'),
					bzz: jQuery('#kms-filter-prc-bzz').val(),
					action: action
				};
				
				jQuery('#plots-tables').datagrid({
					rpcParams: [_filteredRpcParams],
					pageNumber: pageNumber
				});
			} else
			{
				jQuery.messager.alert('Грешка', 'Моля въведете параметри за филтириране.');
			}
			break;
		case LAYER_ISAK_PLOTS: //isak
				_filteredRpcParams = {
					gids: array,
					layer_type: 6,
					layer_id: layer.id,
					ekatte: jQuery('#isak-filter-ekate').combobox('getValue'),
					cropcode: jQuery('#isak-filter-culture').combobox('getValues'),
					prc_uin: jQuery('#isak-filter-prc-uin').val(),
					urn: jQuery('#isak-filter-urn').val(),
					action: action
				};
				jQuery('#plots-tables').datagrid({
					rpcParams: [_filteredRpcParams],
					pageNumber: pageNumber
				});
			break;
		case LAYER_FOR_ISAK_PLOTS: //for isak
				_filteredRpcParams = {
					gids: array,
					layer_type: 9,
					layer_id: layer.id,
					ekatte: jQuery('#for-isak-filter-ekate').combobox('getValue'),
					prc_name: jQuery('#for-isak-filter-prc-name').val(),
					action: action,
					edited: jQuery('#for-isak-for-editing').combobox('getValue'),
					cropcode: jQuery('#select-for-isak-filter-culture').combobox('getValue')
				};

				jQuery('#plots-tables').datagrid({
					rpcParams: [_filteredRpcParams],
					pageNumber: pageNumber
				});
			break;
		case LAYER_TEMP_DATA: //for isak
			if (jQuery('#gps-filter-plot-name').val().length != 0
					|| jQuery('#gps-filter-comment').val().length != 0) {

				_filteredRpcParams = {
					gids: array,
					layer_type: 9,
					layer_id: layer.id,
					plot_name: jQuery('#gps-filter-plot-name').val(),
					plot_info: jQuery('#gps-filter-comment').val(),
					action: action
				};

				jQuery('#plots-tables').datagrid({
					rpcParams: [_filteredRpcParams],
					pageNumber: pageNumber
				});
			} else
			{
				jQuery.messager.alert('Грешка', 'Моля въведете параметри за филтириране.');
			}

			break;
		case 'layer_allowable': //allowable
			if (jQuery('#allowable-filter-ident').val().length != 0
					|| jQuery('#allowable-filter-fbident').val().length != 0
					|| jQuery('#allowable-filter-ekate').combobox('getValue').length != 0) {

				_filteredRpcParams = {
					gids: array,
					layer_type: 'layer_allowable',
					layer_id: layer.id,
					fbident: jQuery('#allowable-filter-fbident').val(),
					ekate: jQuery('#allowable-filter-ekate').combobox('getValue'),
					ident: jQuery('#allowable-filter-ident').val(),
					action: action
				};

				jQuery('#plots-tables').datagrid({
					rpcParams: [_filteredRpcParams],
					pageNumber: pageNumber
				});
			} else
			{
				jQuery.messager.alert('Грешка', 'Моля въведете параметри за филтириране.');
			}

			break;
		case LAYER_TYPE_LFA: //lfa
			if (jQuery('#lfa-filter-ekate').combobox('getValue').length != 0
					|| jQuery('#lfa-filter-area-type').combobox('getValue').length != 0) {

				_filteredRpcParams = {
					layer_type:10,
					gids: array,
					layer_id: layer.id,
					ekate: jQuery('#lfa-filter-ekate').combobox('getValue'),
					area_type: jQuery('#lfa-filter-area-type').combobox('getValue'),
					action: action,
					layer: LAYER_TYPE_LFA
				};

				jQuery('#plots-tables').datagrid({
					rpcParams: [_filteredRpcParams],
					pageNumber: pageNumber
				});
			} else
			{
				jQuery.messager.alert('Грешка', 'Моля въведете параметри за филтириране.');
			}

			break;
			case LAYER_TYPE_NATURA_2000: //nat-2000
			if (jQuery('#natura-2000-filter-name').combobox('getValue').length != 0) {

				_filteredRpcParams = {
					gids: array,
					layer_type: 11,
					layer_id: layer.id,
					gid: jQuery('#natura-2000-filter-name').combobox('getValue'),
					action: action,
					layer: LAYER_TYPE_NATURA_2000
				};

				jQuery('#plots-tables').datagrid({
					rpcParams: [_filteredRpcParams],
					pageNumber: pageNumber
				});
			} else
			{
				jQuery.messager.alert('Грешка', 'Моля въведете параметри за филтириране.');
			}

			break;
		case LAYER_TYPE_PERMANETELY_GREEN_AREAS: //pzp
			if (jQuery('#pzp-filter-ekate').combobox('getValue').length != 0
					|| jQuery('#pzp-filter-identifier').val().length != 0) {

				_filteredRpcParams = {
					layer_type: 12,
					gids: array,
					layer_id: layer.id,
					ekate: jQuery('#pzp-filter-ekate').combobox('getValue'),
					identifier: jQuery('#pzp-filter-identifier').val(),
					action: action,
					layer: LAYER_TYPE_PERMANETELY_GREEN_AREAS
				};

				jQuery('#plots-tables').datagrid({
					rpcParams: [_filteredRpcParams],
					pageNumber: pageNumber
				});
			} else
			{
				jQuery.messager.alert('Грешка', 'Моля въведете параметри за филтириране.');
			}

			break;
		case LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI:
			if (jQuery('#vps-chervenogushi-filter-ekate').combobox('getValue').length != 0) {

				_filteredRpcParams = {
					gids: array,
					layer_type: 14,
					layer_id: layer.id,
					ekate: jQuery('#vps-chervenogushi-filter-ekate').combobox('getValue'),
					action: action,
					layer: LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI
				};

				jQuery('#plots-tables').datagrid({
					rpcParams: [_filteredRpcParams],
					pageNumber: pageNumber
				});
			} else
			{
				jQuery.messager.alert('Грешка', 'Моля въведете параметри за филтириране.');
			}

			break;
		case LAYER_TYPE_VPS_GASKI_ZIMNI:
			if (jQuery('#vps-zimni-filter-ekate').combobox('getValue').length != 0) {

				_filteredRpcParams = {
					gids: array,
					layer_id: layer.id,
					layer_type: 15,
					ekate: jQuery('#vps-zimni-filter-ekate').combobox('getValue'),
					action: action,
					layer: LAYER_TYPE_VPS_GASKI_ZIMNI
				};

				jQuery('#plots-tables').datagrid({
					rpcParams: [_filteredRpcParams],
					pageNumber: pageNumber
				});
			} else
			{
				jQuery.messager.alert('Грешка', 'Моля въведете параметри за филтириране.');
			}

			break;
		case LAYER_TYPE_VPS_LIVADEN_BLATAR:
			if (jQuery('#vps-livaden-blatar-filter-ime').combobox('getValue').length != 0) {

				_filteredRpcParams = {
					gids: array,
					layer_type: 16,
					layer_id: layer.id,
					ime: jQuery('#vps-livaden-blatar-filter-ime').combobox('getValue'),
					action: action,
					layer: LAYER_TYPE_VPS_LIVADEN_BLATAR
				};

				jQuery('#plots-tables').datagrid({
					rpcParams: [_filteredRpcParams],
					pageNumber: pageNumber
				});
			} else
			{
				jQuery.messager.alert('Грешка', 'Моля въведете параметри за филтириране.');
			}

			break;
		case LAYER_TYPE_VPS_ORLI_LESHOYADI:
			if (jQuery('#vps-orli-leshoyadi-filter-zemlishte').combobox('getValue').length !== 0
					|| jQuery('#vps-orli-leshoyadi-filter-fb').val().length !== 0) {

				_filteredRpcParams = {
					gids: array,
					layer_type: 17,
					layer_id: layer.id,
					ekatte: jQuery('#vps-orli-leshoyadi-filter-zemlishte').combobox('getValue'),
					blockuin: jQuery('#vps-orli-leshoyadi-filter-fb').val(),
					action: action,
					layer: LAYER_TYPE_VPS_ORLI_LESHOYADI
				};

				jQuery('#plots-tables').datagrid({
					rpcParams: [_filteredRpcParams],
					pageNumber: pageNumber
				});
			} else
			{
				jQuery.messager.alert('Грешка', 'Моля въведете параметри за филтириране.');
			}

			break;
		case LAYER_TYPE_WORK_LAYER:
			var inputs = jQuery('#work-layer-filter-fields').find('input'),
				searchObj = {},
				tmpTitle,
				element,
				workLayerFilter = false;
			inputs.each(function (input, el) {
				element = jQuery(el);
				if (element.val() !== ''){
					tmpTitle = element.data('title');
					searchObj[tmpTitle] = element.val();
					workLayerFilter = true;
				}
			});

			if (workLayerFilter) {
				_filteredRpcParams = {
					gids: array,
					layer_type: 19,
					layer_id: layer.id,
					action: action,
					filterParams: searchObj,
				};

				jQuery('#plots-tables').datagrid({
					rpcParams: [_filteredRpcParams],
					pageNumber: pageNumber
				});
			} else
			{
				jQuery.messager.alert('Грешка', 'Моля въведете параметри за филтириране.');
			}

			break;
		default:
			break;
	}
}

function clearAttrTablesFilter(){
	var combotree = jQuery('#all-layers-tree').combotree('tree');
	var layer = combotree.tree('getSelected');
	jQuery('#select-filtered-plots').prop('checked', false);
	jQuery('#select-filtered-plots').trigger('change');
	sessionStorage.removeItem('checkedPlots');
	vectors.removeAllFeatures();
	switch (layer.attributes.layer_type)
	{
		case LAYER_ZP_PLOTS: //zp
			jQuery('#zp-filter-ekate').combobox('reset');
			jQuery('#zp-filter-obrabotki').val('');
			jQuery('#zp-filter-dobivi').val('');
			jQuery('#zp-filter-napoqvane').val('');
			jQuery('#zp-filter-polivki').val('');
			jQuery('#zp-filter-polzvatel').val('');
			jQuery('#zp-filter-isak').val('');
			jQuery('#zp-filter-culture').combobox('reset');

			break;
		case LAYER_KOMAS_PLOTS: //kms
			jQuery('#kms-filter-ekate').combobox('reset');
			jQuery('#kms-filter-culture').combobox('reset');
            jQuery('#kms-filter-prc-bzz').val('');

			break;
		case LAYER_ISAK_PLOTS: //isak
			jQuery('#isak-filter-prc-uin').val('');
			jQuery('#isak-filter-ekate').combobox('reset');
			jQuery('#isak-filter-urn').val('');
			jQuery('#isak-filter-culture').combobox('clear');

			break;
		case LAYER_FOR_ISAK_PLOTS: //for isak
			jQuery('#for-isak-filter-prc-name').val('');
			jQuery('#for-isak-filter-ekate').combobox('reset');
			jQuery('#for-isak-for-editing').combobox('reset');

			break;
		case LAYER_TEMP_DATA: //gps
			jQuery('#gps-filter-plot-name').val('');
			jQuery('#gps-filter-comment').val('');

			break;
		case 'layer_allowable': //allowable
			jQuery('#allowable-filter-ident').val('');
			jQuery('#allowable-filter-ekate').combobox('reset');
			jQuery('#allowable-filter-fbident').val('');
			break;
		case LAYER_TYPE_LFA: //lfa
			jQuery('#lfa-filter-ekate').combobox('reset');
			jQuery('#lfa-filter-area-type').combobox('reset');
			break;
		case LAYER_TYPE_NATURA_2000: //natura-2000
			jQuery('#natura-2000-filter-name').combobox('reset');
			break;
		case LAYER_TYPE_PERMANETELY_GREEN_AREAS: //pzp
			jQuery('#pzp-filter-ekate').combobox('reset');
			jQuery('#pzp-filter-identifier').val('');
			break;
		case LAYER_TYPE_VPS_ORLI_LESHOYADI:
			jQuery('#vps-orli-leshoyadi-filter-zemlishte').combobox('reset');
			jQuery('#vps-orli-leshoyadi-filter-fb').val('');
			break;
		default:
			break;
	}

	_filteredRpcParams = {};

    jQuery('#plots-tables').datagrid({
    	rpcParams: [{
    		layer_id: layer.id,
    		clear_filter: true
    	}]
    });
}

function displayFeatureSelection(geom, rowData){
	//remove all previous features
	vectors.removeAllFeatures();

	var in_options = {
		'internalProjection': map.baseLayer.projection,
		'externalProjection': new OpenLayers.Projection("EPSG:32635")
	};
	var features = new OpenLayers.Format.WKT(in_options).read(geom);
	var bounds;

	if (features) {
		if (features.constructor != Array) {
			features = [features];
		}
		for (var i = 0; i < features.length; ++i) {
			if (!bounds) {
				bounds = features[i].geometry.getBounds();
			} else {
				bounds.extend(features[i].geometry.getBounds());
			}

			if (typeof rowData.gid !== 'undefined') {
				features[i].attributes.gid = rowData.gid;
			}
			if (typeof rowData.id !== 'undefined') {
				features[i].attributes.id = rowData.id;
			}
		}

		vectors.addFeatures(features);
		var b = bounds.getCenterLonLat();
		var lonlat = new OpenLayers.LonLat(b.lon, b.lat);
		map.panTo(lonlat);
		map.updateSize();
	}
}

function zoomToFeatureSelection(geom) {
    //remove all previous features
    vectors.removeAllFeatures();

    var in_options = {
        'internalProjection': map.baseLayer.projection,
        'externalProjection': new OpenLayers.Projection("EPSG:32635")
    };
    var features = new OpenLayers.Format.WKT(in_options).read(geom);

    var bounds;
    var purpleStyle = OpenLayers.Util.applyDefaults(purpleStyle, OpenLayers.Feature.Vector.style['default']);
    purpleStyle.strokeColor = "#FF00ED";
    purpleStyle.strokeWidth = 1.9;
    purpleStyle.fill = false;

    if (features) {
        if (features.constructor != Array) {
            features = [features];
        }
        for (var i = 0; i < features.length; ++i) {
            if (!bounds) {
                bounds = features[i].geometry.getBounds();
            } else {
                bounds.extend(features[i].geometry.getBounds());
            }
            features[i].style = purpleStyle;
        }
        vectors.addFeatures(features);
        bounds = vectors.getDataExtent();
        map.zoomToExtent(bounds);
    }
}

function pushVectorLayerToTopPosition() {
	var layers = map.layers;
	var layers_count = layers.length;

	if (map.layers[layers_count - 1].name != "Vector Layer")
	{
		for (var i = 0; i < map.layers.length; i++)
		{
			if (layers[i].name == "Vector Layer")
			{
				map.setLayerIndex(map.layers[i], layers.length - 1);
			}
		}
	}
}

jQuery(function() {
	jQuery('#win-layer-attr-info').window({
		onClose: function () {
			jQuery('.disabled-overlay').remove();
			sessionStorage.removeItem('checkedPlots');
			jQuery('#chooseAllCheckBox').prop('checked', false);
			allRowsChecked = false;
			jQuery('#plots-tables').datagrid('uncheckAll');

			if (jQuery('#plots-tables').datagrid('options').rpcParams[0].layer_id != undefined) {
				var layer_id = jQuery('#plots-tables').datagrid('options').rpcParams[0].layer_id;
				_filteredPlotGidsObj[layer_id] = jQuery('#plots-tables').datagrid('getData').filtered_gids;
			}

			selectedPlotsAlertShown = false;
		}
	});
});

/**
 * Exports layer to .shp
 * @param {String} exportType
 * @param {Boolean} useFilter
 * @param {Boolean} isUnited
 * @param {Boolean} isOld
 * @param {String} outputDevice
 * @returns {undefined}
 */
function exportLayer(exportType, isUnited, isOld, outputDevice, exportStructureType = null){
    var obj = prepareLayerExportData(exportType, isUnited, isOld, outputDevice, exportStructureType);

	if(!obj) return;
    TF.Rpc.Map
        .MapExportLayer
        .export(obj)
        .done(function (dataObj) {
            jQuery('#btn-download-file').attr('href', dataObj);
            jQuery('#win-download').window('open');
        })
        .fail(function (errorObj) {
            jQuery.messager.alert('Грешка', errorObj.getMessage());
        });
}

function prepareLayerExportData(exportType, isUnited, isOld, outputDevice, exportStructureType = null) {

	var obj = {};
    obj.export_type = exportType;
    obj.united = isUnited;
    obj.export_old = isOld;
	obj.export_structure_type = exportStructureType;
    obj.output_device = outputDevice;
	let allPlotsChecked = jQuery('#chooseAllCheckBox').is(':checked');

    var combotree = jQuery('#all-layers-tree').combotree('tree');
	var layerData = combotree.tree('getSelected');
	var returnUrl = returnUrl || false;
    var checkedPlots = [];
    var checkedPlotIDS = [];
	if (!layerData)
	{
		jQuery.messager.alert('Грешка', 'Не е избран активен слой');
		return;
	}

    if(!isLayerExportable(layerData.attributes.layer_type)) {
        jQuery.messager.alert('Грешка', 'Този слой не може да бъде експортван');
        return;
    }
    if (jQuery('#plots-tables').data().hasOwnProperty('datagrid')) {
    	var gridLayerId = jQuery('#plots-tables').datagrid('options').rpcParams[0].layer_id;

    	_filteredPlotGidsObj[gridLayerId] = jQuery('#plots-tables').datagrid('getData').filtered_gids;
		checkedPlots = sessionStorage.checkedPlots ? JSON.parse(sessionStorage.checkedPlots) : {};
    } else {
    	checkedPlots = [];
    }
    
    obj.layer_name = layerData['attributes'].layer_name;
    obj.layer_type = layerData['attributes'].layer_type;
	obj.layer_id = layerData.id;
	obj.selected_ids = [];

	var array = [];
	for (var i=0; i<vectors.features.length; i++) {
        var featuresAttr = vectors.features[i].attributes;
        if (obj.layer_type != LAYER_ISAK_PLOTS && featuresAttr.hasOwnProperty('id')) {
            array.push(featuresAttr.id);
        } else if (featuresAttr.hasOwnProperty('gid')) {
            array.push(featuresAttr.gid);
        }
	}

	if(allPlotsChecked) {
		return obj;
	}

	if (Object.keys(checkedPlots).length > 0) {
		jQuery.each(checkedPlots,function (pageId, pageData) {
			jQuery.each(pageData, function (index, plotId) {
				checkedPlotIDS.push(plotId);
			})
		});
		obj.selected_ids = checkedPlotIDS;
	} else {
		jQuery.messager.alert('Грешка', 'Моля маркирайте желаните парцели чрез отметка пред тях.', 'warning');
		return;
	}

	return obj;
}

function exportMapView(format){
	var osz_layer_names = [
			'topic_layer_kvs_by_owner_name',
			'topic_layer_kvs_by_tenant_name',
			'topic_layer_kvs_by_agreement',
			'topic_layer_kvs_by_category',
			'topic_layer_kvs_by_ntp',
			'topic_layer_kvs_by_ownership'
	];
	for (var i = map.layers.length - 1; i >= 0; i--) {
		if (osz_layer_names.indexOf(map.layers[i].name) > -1) {

			var data = {};
			data.layer_name = map.layers[i].name;
			data.ekate = jQuery('#topic-layer-ekate').combobox('getValue');
			data.extent = map.calculateBounds().transform(map.projection, new OpenLayers.Projection("EPSG:32635")).toBBOX();
			data.legend = jQuery('#topic-layer-legend-datagrid').datagrid('getRows');

            TF.Rpc.Map
		    .MapExportLayerGraphically
		    .exportTopicLayer(data, format)
		    .done(function (dataObj) {
		        jQuery('#win-download').window('open');
		        jQuery('#btn-download-file').attr('href', dataObj['pdf_blank_file']);
		    })
		    .fail(function (errorObj) {
		        jQuery.messager.alert('Грешка', 'Грешка');
		    });
            return;
        }
	}
}

function exportMap(format, mapVisible = false){
	var osz_layer_names = [
			'topic_layer_kvs_by_owner_name',
			'topic_layer_kvs_by_tenant_name',
			'topic_layer_kvs_by_agreement',
			'topic_layer_kvs_by_category',
			'topic_layer_kvs_by_ntp',
			'topic_layer_kvs_by_ownership'
	];
	for (var i = map.layers.length - 1; i >= 0; i--) {
		if (osz_layer_names.indexOf(map.layers[i].name) > -1) {

			var data = {};
			data.layer_name = map.layers[i].name;
			data.ekate = jQuery('#topic-layer-ekate').combobox('getValue');
			data.legend = jQuery('#topic-layer-legend-datagrid').datagrid('getRows');

            TF.Rpc.Map
		    .MapExportLayerGraphically
		    .exportTopicLayer(data, format)
		    .done(function (dataObj) {
		        jQuery('#win-download').window('open');
		        jQuery('#btn-download-file').attr('href', dataObj['pdf_blank_file']);
		    })
		    .fail(function (errorObj) {
		        jQuery.messager.alert('Грешка', 'Грешка');
		    });
            return;
        }
	}

	var combotree = jQuery('#all-layers-tree').combotree('tree');
	var selected_layer = combotree.tree('getSelected');
	var checked = combotree.tree('getChecked');

	if (!selected_layer)
    {
        jQuery.messager.alert('Грешка', 'Не е избран активен слой!');
        return false;
    }

 	if (jQuery('#plots-tables').data().hasOwnProperty('datagrid')) {
    	_filteredPlotGidsObj[selected_layer.parent_id ?? selected_layer.id] = jQuery('#plots-tables').datagrid('getData').filtered_gids;
    };
	var array = [];

	for (var i=0; i<vectors.features.length; i++) {
        var featuresAttr = vectors.features[i].attributes;
        if (featuresAttr.hasOwnProperty('id')) {
            array.push(featuresAttr.id);
        } else if (featuresAttr.hasOwnProperty('gid')) {
            array.push(featuresAttr.gid);
        }
	};

	var selected_plots_ids = array;
	let selected_layer_id = selected_layer.parent_id ?? selected_layer.id;
	if (array.length === 0 && _filteredPlotGidsObj[selected_layer_id] != undefined && _filteredPlotGidsObj[selected_layer_id].length > 0) {
		selected_plots_ids = _filteredPlotGidsObj[selected_layer_id];
	}

    var j = 0;
    var checked_layers = [];
    for (var i=0; i<checked.length; i++) {	
    	if(checked[i].children == undefined){
    		checked_layers[j] = checked[i];
			if (selected_layer.parent_id) {
				if (selected_layer.id === checked_layers[j].id) {
					checked_layers[j].selected = true;
					checked_layers[j].selected_plots = selected_plots_ids;
				} else {
					checked_layers[j].selected = false;
				}
			} else {
				checked_layers[j].selected = false;
			}
    		j++;
    	}
    }

	map.layers.forEach((layer) => {
		checked_layers.forEach((checkedLayer) => {
			if (checkedLayer.attributes.layer_name == layer.name) {
				checkedLayer.z_index = layer.getZIndex();
				checkedLayer.style  = getMapExportAttr(checkedLayer);
			}
		})
	});

    if (checked_layers.length == 0)
	{
		jQuery.messager.alert('Грешка', 'Моля изберете слой!');
		return false;
	}

	let extent = (mapVisible === true)
				? map.calculateBounds().transform(map.projection, new OpenLayers.Projection("EPSG:32635")).toBBOX() 
				: null;
	
	//solve circular reference problem
	const cloneChecked = JSON.parse(JSON.stringify(checked_layers, replacerFunc()));
 
	TF.Rpc.Map
    .MapExportLayerGraphically
    .export(cloneChecked, format, extent)
    .done(function (dataObj) {
		mapPdfPreview(dataObj['pdf_blank_file']);
    })
    .fail(function (errorObj) {
        jQuery.messager.alert('Грешка', 'Грешка');
    });
}

/**
 * Prepare styles object as same as in backend
 *
 * @param   {object}  layer
 *
 * @return  {object}        
 */
function getMapExportAttr(layer)
{
	const layerId = layer.id;
	const transparencyVal = jQuery('#preview-transparency-'+layerId).numberspinner('getValue');
	const transparency = transparencyVal ? (100 - transparencyVal) : 0;

	let attributes = {
		tags : jQuery('#preview-tags-'+layerId).is(':checked') ? 1 : 0,
		border_only: jQuery('#preview-only-border-'+layerId).is(':checked') ? 1 : 0,
		label_name: jQuery('#preview-label-name-'+layerId).combobox('getValues'),
		transparency: transparency
	}

	if (layer.attributes.layer_type == LAYER_KVS_PLOTS) {
		return JSON.stringify({[layerId]: attributes});
	}

	return JSON.stringify((attributes));
}


/**
 * Prepare and build map preview html and attributes
 *
 * @return  {void}
 */
function buildPreviewAttrPanel(exportableLayers)
{
	apppendMapPreviewHtml(exportableLayers)
	initMapPreviewAttr(exportableLayers);	
}

/**
 * Appends map attributes html based on selected eportable layers
 *
 * @param   {array}  layers   array of map layers
 *
 * @return  {void}
 */
 function apppendMapPreviewHtml(layers) {

	let str ='';
	layers.forEach((layer) => {
		str += '<fieldset style="border: 1px solid #000; margin: 5px;">';
		str += '<legend style="margin-left: 10px; font-style: italic; font-weight: bold;">'+layer.text+'</legend>';
		str += '<table style="padding:5px; margin: 5px 10px 0px 10px;">';

		str += 	'<tr>'+
					'<td style="padding:5px;width:150px;">'+
						'<div style="float:left">'+
							'<input type="checkbox" id="preview-tags-'+layer.id+'" style="" />'+
							'<label for="preview-tags-'+layer.id+'">Покажи етикети</label>'+
						'</div>'+
					'</td>'+
					'<td>'+
						'<div style="padding-left:30px;">'+
							'<label for="preview-label-name-'+layer.id+'">Използвай етикет:</label>'+
							'<input type="combobox" id="preview-label-name-'+layer.id+'" style="width:150px" />'+
						'</div>'+
					'</td>'+
				'</tr>';
		str += '<tr>'+
					'<td style="padding:5px; width:150px;" colspan="1">'+
						'<div style="width: 150px;">'+
							'<label for="preview-transparency-'+layer.id+'">Прозрачност:</label>'+
							'<input id="preview-transparency-'+layer.id+'" type="text" style="width: 150px;" />'+
						'</div>'+
					'</td>'+
					'<td colspan="1" style="padding-left:30px;">'+
						'<div style="float:left;">'+
							'<input type="checkbox" id="preview-only-border-'+layer.id+'" style="" />'+      
							'<label for="preview-only-border-'+layer.id+'">Покажи само граници</label>'+
						'</div>'+
					'</td>'+
				'</tr>';				
		str += '</table>';		
		str += '</fieldset>';
	});

	jQuery('#map-export-attr').empty().append(str);
	jQuery('#map-pdf-preview').empty();
	jQuery('#map-pdf-preview').append('<p style="margin-top:40%; margin-left:auto;margin-right:auto; text-align:center; font-weight: bold;">Моля, изберете настройки за печат и натиснете Преглед.<p>');
}

/**
 * Initialize dynamic map preview attributes
 *
 * @param   {object}  exportableLayers  exporable map layerds
 *
 * @return  {void}
 */
function initMapPreviewAttr(exportableLayers)
{
	exportableLayers.forEach((layer) => {
		jQuery('#preview-label-name-'+layer.id).combobox({
			url: 'index.php?common-rpc=label-names-combobox',
			rpcParams: [{
				selected: true,
				layer_type: layer.attributes.layer_type,
				layer_id: layer.id
			}],
			valueField: 'key',
			textField: 'name',
			multiple: true,
			loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
			loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
		});

		TF.Rpc.Map.MapLayerChange.markForEdit(layer.parent_id ?? layer.id)
        .done(function (data) {		

			if(layer.attributes.layer_type == LAYER_KVS_PLOTS){
				var styles = JSON.parse(data.style)[layer.id];			
			} else {
				var styles = JSON.parse(data.style);
			}

			for (var i = 0; i < styles.label_name.length; i++) {
				jQuery('#preview-label-name-'+layer.id).combobox('select', styles.label_name[i]);
			}

			if (styles.tags) {
				jQuery('#preview-tags-'+layer.id).prop('checked', true);
			} else {
				jQuery('#preview-tags-'+layer.id).prop('checked', false);
			}

			if (styles.border_only) {
				jQuery('#preview-only-border-'+layer.id).prop('checked', true);
			} else {
				jQuery('#preview-only-border-'+layer.id).prop('checked', false);
			}
			jQuery('#preview-transparency-'+layer.id).numberspinner({
				min: 0,
				max: 100,
				editable: true
			});

			let transparency = (styles && styles.transparency) ? (100 - styles.transparency) : 0;
			jQuery('#preview-transparency-'+layer.id).numberspinner('setValue', transparency);
        })
        .fail(function (errorObj) {
            if (errorObj.is(TF.Rpc.ExceptionsList.MAP_REQUESTED_LAYER_NOT_FOUND)) {
                jQuery.messager.alert('Грешка',errorObj.getMessage);
            }
        });
	});

	initMapFormatSwitchButtons();
}


/**
 * Add map format switchbuttons to preview window
 *
 * @return  {void}
 */
function initMapFormatSwitchButtons()
{
	jQuery('#export-visible').switchbutton({
		checked: true
	});

	for (var i = 0; i <= 4; i++) {
		let swintchOn = false;
		if (i == 0) {
			//default value of buttons will be A0
			swintchOn = true;
		}
		jQuery('#export-a'+i).switchbutton({
			checked: swintchOn,
			onChange: function(checked){
				const checkedId = jQuery(this).attr('id');
				//prevents recursively ucheck call
				if (checked === true) {
					uncheckMapFormat(checkedId);
				}
				
			}
		});
	}
}

/**
 * Makes switch buttons to behave like radio button.
 *
 * @param   {int}  checkedId  currently checked id
 *
 * @return  {void}            
 */
function uncheckMapFormat(checkedId)
{
	jQuery('input[id^="export-a"]').each(function() {
		let elemendId = jQuery(this).attr('id');
		if (elemendId != checkedId) {
			if (jQuery(this).switchbutton('options').checked === true) {
				jQuery(this).switchbutton('uncheck');
			}
		}
			
	});
}

/**
 * Begins the process of map export and preview.
 *
 * @return  {void}
 */
function previewMap()
{
	let exportVisible = jQuery('#export-visible').switchbutton('options').checked;

	let isChecked = false;
	let format = null;
	jQuery('input[id^="export-a"]').each(function() {
		isChecked = jQuery(this).switchbutton('options').checked;
		if(isChecked === true){
			format = jQuery(this).switchbutton('options').onText;
			return false;
		}
	});

	if (!format) {
		jQuery.messager.alert('Грешка', 'Моля изберете формат за ескпорт');
	}

	exportMap(format, exportVisible);
}

/**
 * Export map pdf preview.
 * Based on https://pdfobject.com/
 * 
 *
 * @param   {string}  filepath  path to file
 *
 * @return  {void}
 */
function mapPdfPreview(filePath) 
{
	if (!PDFObject.supportsPDFs) {
		jQuery('#win-download').window('open');
        jQuery('#btn-download-file').attr('href', filePath);
	}

	let pdfOpenParams = {
        view: 'FitH'
    }

	PDFObject.embed("../../../"+filePath, "#map-pdf-preview", pdfOpenParams);
}

/**
 * Adobe embeded pdf preview.
 * Currently not used.
 * To enable should include <script src="https://documentcloud.adobe.com/view-sdk/main.js"></script>
 * And provide registered key https://www.adobe.io/apis/documentcloud/dcsdk/gettingstarted.html
 * 
 * @param   {string}  filepath  path to file
 *
 * @return  {void}
 */
function adobePdfPreview(filepath)
{
	let adobeDCView = new AdobeDC.View({
		clientId: '<YOUR_CLIENT_ID>',
		divId: "map-pdf-preview",
	});

	adobeDCView.previewFile({
		content: {location: {url: buildExportUrl(filePath)}},
		metaData:{fileName: "map_export"}
	}, {embedMode: "SIZED_CONTAINER"});
}

/**
 * Build url to exported pdf file
 *
 * @param   {string}  filepath  path to file
 *
 * @return  {string}
 */
function buildExportUrl(filePath)
{
	return window.location.protocol + '//' + window.location.hostname + ':' + window.location.port + '/' + filePath;
}

function initManualLayerDataEdit(){
	var combotree = jQuery('#all-layers-tree').combotree('tree');
	var selectedLayer = combotree.tree('getSelected');

	attrInfoForLayerType = selectedLayer.attributes.layer_type;
	//No MapRightsRW
	if(!hasMapRightsRW) {
		messagerMapRightsRW();
		return false;
	}

	var selectedElement = jQuery('#plots-tables').datagrid('getSelected');

	if (!selectedElement)
	{
		jQuery.messager.alert('Грешка', 'Моля изберете запис!');
		return false;
	}

	var idName;
	if (attrInfoForLayerType == 1)
	{
		idName = 'id';
	}
	else {
		idName = 'gid';
	}

	initManualPropertyGrid(attrInfoForLayerID, selectedElement[idName], attrInfoForLayerType);
}

function initWorkLayerColumnsDefinitions(){
	let layer = jQuery('#all-layers-tree').combotree('tree').tree('getSelected');
	TF.Rpc.Map.MapLayerChange.getWorkLayerColumnsDefinitions(layer.id)
		.done(function (definitions) {
			initColRenameFields(definitions);
			jQuery('#win-work-layer-col-editing').window('open');
		})
		.fail(function (errorObj) {
			jQuery.messager.alert('Грешка',errorObj.getMessage());
		});
}

function initColRenameFields(columns) {
	jQuery("#colNames").empty();
	for (let col in columns){
		if(isNaN(parseInt(col))) {
			continue;
		}
		jQuery("#colNames").append('' +
			'<div style="padding-top: 15px;" id="' + columns[col].col_name + '">' +
			'<input type="checkbox" class="columnVisible" style="width:55px" ' + (columns[col].col_visible === true ? "checked" : "") + ' />' +
			'<input type="text" class="columnName" style="width:120px" value="' + columns[col].col_name + '" disabled />' +
			'<input type="text" class="columnTitle" style="width:120px; margin-left: 15px;" value="' + columns[col].col_title + '" />' +
			'</div>'
		);
	}
}

function saveWorkLayerColumnsDefinitions(){
	let columns = jQuery("#colNames > div");
	let layer = jQuery('#all-layers-tree').combotree('tree').tree('getSelected');
	let saveLayerObject = {
		'layer_id': layer.id,
		'definitions': []
	};

	for(let col in columns) {
		if(isNaN(parseInt(col))) {
			continue;
		}
		let col_name = jQuery(columns[col]).find(".columnName").val();
		let col_title = jQuery(columns[col]).find(".columnTitle").val();
		let col_visible = jQuery(columns[col]).find(".columnVisible").is(":checked");

		if(!col_title) {
			jQuery.messager.alert('Грешка', 'Името на колона ' + col_name + ' е празно!');
			jQuery('#layerCustomizationTabs').tabs('select',2);
			return false;
		}
		saveLayerObject.definitions.push({
			'col_name': col_name,
			'col_title': col_title,
			'col_visible': col_visible
		});
	}

	TF.Rpc.Map.MapLayerChange.updateWorkLayerColumnsDefinitions(saveLayerObject)
		.done(function () {
			jQuery('#plots-tables').datagrid('loadRpc');
			jQuery('#win-work-layer-col-editing').window('close');
		})
		.fail(function (errorObj) {
			jQuery.messager.alert('Грешка',errorObj.getMessage());
		});
}

function initManualLayerDataMultiEdit() {

    var combotree = jQuery('#all-layers-tree').combotree('tree');
    var layer_id = combotree.tree('getSelected').id;

    jQuery('#me-culture').combobox({
        url: 'index.php?common-rpc=culture-combobox',
        rpcParams: [{
            layer_id: layer_id,
            year: 6
        }],
        valueField: 'id',
        textField: 'name',
        width: 200,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });


    jQuery('#me-ekatte').combobox({
        data: ComboboxData.EkateCombobox,
        valueField: 'ekate',
        textField: 'text',
        width: 200,
        filter: function (q, row) {
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if (text.indexOf(find) != -1 || value.indexOf(find) != -1)
            {
                return true;
            }
        },
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function executeMultiEdit() {
	var culture_combobox_node = (jQuery('#me-culture').length) ? jQuery('#me-culture').combobox('getValue'): false;
    if (culture_combobox_node || jQuery('#me-ekatte').combobox('getValue')) {
        jQuery.messager.confirm('Потвърждение', 'Това действие ще промени всички данни, които са показани в таблицата.', function(r) {
            if (r) {
            	var combotree = jQuery('#all-layers-tree').combotree('tree');
				selectedLayer = combotree.tree('getSelected');

                var year = selectedLayer.year_id;
                var farming = selectedLayer.id;
                var layer_name = selectedLayer.attributes.layer_name;

                //new values
                var culture = culture_combobox_node;
            	var ekatte = jQuery('#me-ekatte').combobox('getValue');
                var common_cultures = false;

                var checked_rows = jQuery('#plots-tables').datagrid('getChecked');

                var plotIds = [];
                for (var i = 0; i < checked_rows.length; i++) {
                	plotIds.push(checked_rows[i].gid);
                };

                var requestObj = {
                	layer_name: layer_name,
                	ekatte: ekatte,
                	common_cultures: common_cultures,
                	culture: culture,
                	plotIds: plotIds,
                };

                TF.Rpc.Map.MapAttributeTables.executeMultiEdit(requestObj)
                .done(function (data) {
                	jQuery('#win-for-isak-multi-edit').window('close');
                	jQuery('#win-multi-edit').window('close');
                	jQuery('#plots-tables').datagrid('loadRpc');
                	clearAttrTablesFilter();
                })
                .fail(function (errorObj) {
                	if (errorObj.is(TF.Rpc.ExceptionsList.MAP_REQUESTED_LAYER_NOT_FOUND)) {
                		jQuery.messager.alert('Грешка',TF.Rpc.ExceptionsList.MAP_REQUESTED_LAYER_NOT_FOUND.message,'warning');
                	};
                });
            }
        });
    } else {
        jQuery.messager.alert('Грешка', 'Моля задайте данни, който да бъдат променени.');
    }
}

function zoomToFilteredObjects() {
    var rcpParams = {};
    rcpParams.tablename = attrInfoForLayerName;
    rcpParams.layer_type = attrInfoForLayerType;
    rcpParams.id_name = (attrInfoForLayerType == LAYER_ZP_PLOTS) ? 'id' : 'gid';

    var gids = [];
    var gid = null;
    var i = 0;
    var attributeTable = jQuery('#plots-tables');
    var checkedPlots = attributeTable.datagrid('getChecked');
    var selectedPlot = attributeTable.datagrid('getSelected');

    if (checkedPlots.length > 0) {
        for (i = 0; i < checkedPlots.length; i++) {
            gid = checkedPlots[i][rcpParams.id_name];
            if (gids.indexOf(gid)!= -1) {
                continue;
            }
            gids.push(gid);
        }
    }

    if (selectedPlot && checkedPlots.length === 0 && !isNaN(selectedPlot[rcpParams.id_name])) {
        gid = selectedPlot[rcpParams.id_name];
        if (gids.indexOf(gid) == -1) {
            gids.push(gid);
        }
    }

    rcpParams.gids = gids;

    TF.Rpc.Map.MapAttributeTables.getMaxExtent(rcpParams)
    .done(function (data) {
        zoomToExtent(data);
    })
    .fail(function (data) {

    });
}

function chooseAll(elem) {
    var overlay = jQuery('<div class="disabled-overlay" style="position:absolute; background: #f5f5f58c; width: 100%; height: 100%; z-index: 1000;">');
    var window = jQuery(elem).closest( ".easyui-window" );
    
    if (jQuery(elem).prop("checked")) {
		window.find('#attr-tables-toolbar').css({
            position: 'relative',
            'z-index': 9999
        });
        overlay.clone().prependTo(window.find('.easyui-layout'));
		allRowsChecked = true;
		jQuery('#plots-tables').datagrid('checkAll');
    } else {
		window.find('.disabled-overlay').remove();
		checkAllFiltered = false;
		sessionStorage.removeItem('checkedPlots');
		jQuery('#plots-tables').datagrid('uncheckAll');
		jQuery('#plots-tables').data('datagrid').checkedRows = [];
        allRowsChecked = false;
    }
}

function copyFilteredLayer() {
    var obj = {};
    var plotsGridData = jQuery('#plots-tables').datagrid('getData');

    if (!plotsGridData['rows'].length) {
        jQuery.messager.alert('Грешка', 'Не са намерени записи за копиране!');
        return false;
    }

    obj.ekate = jQuery('#search-ekatte').combobox('getValues')[0];
    obj.use_filter = 1; //use 0 for false and 1 for true
    obj.layer_name = attrInfoForLayerName;
    obj.layer_type = attrInfoForLayerType;
    obj.layer_id = attrInfoForLayerID;

    var combotree = jQuery('#all-layers-tree').combotree('tree');
    var systemNode = combotree.tree('find', 0);
    var systemLayers = combotree.tree('getChildren', systemNode.target);
    var tmpLayer = jQuery.grep(systemLayers, function (e) {
        if (e.attributes.layer_type == LAYER_TEMP_DATA) {
            return true;
        }
    });

    obj.layertmp_id = tmpLayer[0].id;
    obj.layertmp_name = tmpLayer[0].attributes.layer_name;
    obj.layertmp_type = tmpLayer[0].attributes.layer_type;

    TF.Rpc.Map.MapTools.copyLayerItems(obj)
    .done(function (data) {
        reloadLayer(data);
        onCopyComplete(data);
    })
    .fail(function (data) {

    });
}

function loadLayerSelection(e) {

    if (allRowsChecked) {
        _copyFiltered = 1;
    } else {
        _copyFiltered = 0;
    }

	var checkedPlotsData = jQuery('#plots-tables').datagrid('getChecked');

	if(!checkedPlotsData.length && !_copyFiltered)
	{
		jQuery.messager.alert('Грешка', 'Моля маркирайте желаните парцели чрез отметка пред тях.','warning');
		return false;
	}

	initLayerDropDown(e.data.layer_type);
}

function initLayerDropDown(layer_type) {
	var combotree = jQuery('#all-layers-tree').combotree('tree'),
		layerData = combotree.tree('getSelected'),
		layerID = layerData.id,
		params = {
			layer_type:layer_type
		};
	if(layer_type == LAYER_TYPE_WORK_LAYER) {
		params.layer_id = layerID;
	}

	//init layer dropdown
	jQuery('#select-for-isak-layer').combobox({
		url: 'index.php?common-rpc=layer-selection-combobox',
		rpcParams: [params],
		valueField: 'layer_id',
		textField: 'layer_path',
		groupField: 'farming_name',
		required: true,
		editable: false,
		filter: function(q, row) {
			var opts = jQuery(this).combobox('options');
			var text = row[opts.textField].toLowerCase();
			var value = row[opts.valueField];
			var find = q.toLowerCase();
			if (text.indexOf(find) != -1 || value.indexOf(find) != -1)
			{
				return true;
			}
		},
		onLoadSuccess: function(data)
		{
            if(layer_type === 2)
            {
                jQuery('#select-for-isak-layer').combobox('select', 0);
                saveLayerCopy();
            }else{
                jQuery('#win-layer-copy').window('open');
            }
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function onCopyToLayerButtonClick(e) {

	if (vectors.features.length == 0) {
		jQuery.messager.alert('Грешка', 'Трябва да сте селектирали поне един или повече парцели, които искате да копирате!','warning');
		return false;
	}

	_copyToolMapSelected = 1;
	_copyFiltered = 0;
	initLayerDropDown(e.data.layer_type);
}

function copyToForIsak(layer_id, data) {

    var obj = {};

    var combotree = jQuery('#all-layers-tree').combotree('tree');
    var layerData = combotree.tree('getSelected');
    var layerType = layerData['attributes'].layer_type;

    var plotIds = [];
	var checkedPlotsData = sessionStorage.checkedPlots ? JSON.parse(sessionStorage.checkedPlots) : {};
	jQuery.each(checkedPlotsData,function (pageId, pageData) {
		jQuery.each(pageData, function (index, plotId) {
			plotIds.push(plotId);
		})
	});

	if (plotIds.length === 0) {
		jQuery.messager.alert('Грешка', 'Моля селектирайте поне един или повече парцели, които искате да копирате в Слой: За ИСАК!','warning');
		jQuery('#win-layer-copy').window('close');
		return false;
	}

	obj.plot_ids = plotIds;
	obj.use_filter = 0;
	obj.layer_name = layerData['attributes'].layer_name;
	obj.farming_year = layerData['attributes'].year_id;
	obj.layer_type = layerType;
	obj.layer_id = layerData.id;

	//"To" parameters
	obj.layertmp_id = layer_id;
	obj.layertmp_type = LAYER_FOR_ISAK_PLOTS;
	obj.layertmp_data = data;

	TF.Rpc.Map.MapTools.copyLayerItems(obj)
	.done(function (data) {
		reloadLayer(data);
		onCopyComplete(data);
	})
	.fail(function (data) {

	});
}

function copyMapSelectedLayer(layer_id, data, layer_type){
	var obj = {};
	var combotree = jQuery('#all-layers-tree').combotree('tree');
    var layerData = combotree.tree('getSelected');
    var layerType = layerData['attributes'].layer_type;
    var plotIds = vectors.features.map(
        feature => feature.attributes.id || feature.attributes.gid
    );

	if (plotIds.length === 0) {
		jQuery.messager.alert('Грешка', 'Моля селектирайте поне един или повече парцели, които искате да копирате!','warning');
		jQuery('#win-layer-copy').window('close');
		return false;
	}

    obj.plot_ids = plotIds;
	obj.use_filter = 0; //use 0 for false and 1 for true
	obj.layer_name = layerData['attributes'].layer_name;
	obj.layer_type = layerType;
	obj.layer_id = layerData.id;
	obj.farming_year = layerData['attributes'].year_id;

	//"To" parameters
	obj.layertmp_id = layer_id;
	obj.layertmp_type = layer_type;
	obj.layertmp_data = data;

	TF.Rpc.Map.MapTools.copyLayerItems(obj)
	.done(function (data) {
		reloadLayer(data);
		onCopyComplete(data);
	})
	.fail(function (data) {

	});
}

function copyCheckedLayerCustom(layer_id, data, layer_type, use_filter) {
	var obj = {};
	obj.plot_ids = [];

	obj.use_filter = use_filter; //use 0 for false and 1 for true
	
	if (obj.use_filter != 1) {
		var checkedPlotsData = sessionStorage.checkedPlots ? JSON.parse(sessionStorage.checkedPlots) : {};
		
		jQuery.each(checkedPlotsData,function (pageId, pageData) {
			jQuery.each(pageData, function (index, plotId) {
				obj.plot_ids.push(plotId);
			})
		});
	} else {
		var ekatteData = getLayerEkatteFilter(attrInfoForLayerType);
		obj.ekate = ekatteData.length > 1 ? ekatteData[1].ekate : ekatteData[0].ekate;
	}

	var combotree = jQuery('#all-layers-tree').combotree('tree');
    var layerData = combotree.tree('getSelected');

	obj.layer_name = attrInfoForLayerName;
	obj.layer_type = attrInfoForLayerType;
	obj.layer_id = attrInfoForLayerID;
	obj.farming_year = layerData['attributes'].year_id;

	//"To" parameters
	obj.layertmp_id = layer_id;
	obj.layertmp_type = layer_type;
	obj.layertmp_data = data;
	
	TF.Rpc.Map.MapTools.copyLayerItems(obj)
	.done(function (data) {
		reloadLayer(data);
		onCopyComplete(data);
		jQuery('.disabled-overlay').remove();
		sessionStorage.removeItem('checkedPlots');
		jQuery('#chooseAllCheckBox').prop('checked', false);
	})
	.fail(function (data) {

	});
}

function copyCheckedLayer(){
    if (allRowsChecked) {
        copyFilteredLayer();
        return;
    }

    var obj = {};
    var plotIds = [];

	var checkedPlotsData = sessionStorage.checkedPlots ? JSON.parse(sessionStorage.checkedPlots) : {};
	jQuery.each(checkedPlotsData,function (pageId, pageData) {
		jQuery.each(pageData, function (index, plotId) {
			plotIds.push(plotId);
		})
	});

	if(!plotIds.length) {
		jQuery.messager.alert('Грешка', 'Моля маркирайте желаните парцели чрез отметка пред тях.','warning');
		return false;
	}

    obj.plot_ids = plotIds;
	obj.use_filter = 0; //use 0 for false and 1 for true
	obj.layer_name = attrInfoForLayerName;
	obj.layer_type = attrInfoForLayerType;
	obj.layer_id = attrInfoForLayerID;

    var combotree = jQuery('#all-layers-tree').combotree('tree');
    var systemNode = combotree.tree('find', 0);
    var systemLayers = combotree.tree('getChildren', systemNode.target);

    var tmpLayer = jQuery.grep(systemLayers, function(e){
        if(e.attributes.layer_type == LAYER_TEMP_DATA)
        {
            return true;
        }
    });

	obj.layertmp_id = tmpLayer[0].id;
	obj.layertmp_name = tmpLayer[0].attributes.layer_name;
	obj.layertmp_type = tmpLayer[0].attributes.layer_type;

	TF.Rpc.Map.MapTools.copyLayerItems(obj)
	.done(function (data) {
		reloadLayer(data);
		onCopyComplete(data);
	})
	.fail(function (data) {

	});
}

function reloadLayer(data){
	var combotree = jQuery('#all-layers-tree').combotree('tree');

	if(data['layertmp_type'] == LAYER_TEMP_DATA) {
	    var systemNode = combotree.tree('find', 0);
	    var systemLayers = combotree.tree('getChildren', systemNode.target);

	    var tmpLayer = jQuery.grep(systemLayers, function(e){
	        if(e.attributes.layer_type == LAYER_TEMP_DATA)
	        {
	            return true;
	        }
	    });

		combotree.tree('update', {
			target: tmpLayer[0].target,
			attributes: {
				extent: data['extent'],
				fill_color: tmpLayer[0].attributes.fill_color,
				is_system: tmpLayer[0].attributes.is_system,
				layer_name: tmpLayer[0].attributes.layer_name,
				layer_type: tmpLayer[0].attributes.layer_type,
				level: tmpLayer[0].attributes.level,
				name: tmpLayer[0].attributes.name
			}
		});
	}else{

	    var farmingId = data['farming_id'];
	    var yearId = data['year_id'];
	    var layertmp_id = data['layertmp_id'];
	    var node_farming = combotree.tree('find', farmingId);

	    if(!node_farming) {
	    	return false;
	    }

		for (var i = 0; i < node_farming.children.length; i++) {

	    	if(node_farming.children[i].id == yearId){
	    		node_year = node_farming.children[i];
	    	}
	    };

	    if(typeof node_year === 'undefined' || !node_year.children) {
	    	return false;
	    }

	    for (var i = 0; i < node_year.children.length; i++) {

	    	if(node_year.children[i].id == layertmp_id){
	    		node_layer = node_year.children[i];
	    	}
	    };

	    if (typeof node_layer !== 'undefined') {
	    	node_layer.attributes.extent = ''+data['extent']+'';
	    }
	}

	if (data['to_layer_name']) {
		reloadLayerByName(data['to_layer_name']);
	}
}

function isLayerExportable(layer_type) {

    if (layer_type == LAYER_TYPE_NATURA_2000
            || layer_type == LAYER_TYPE_LFA
            || layer_type == LAYER_TYPE_VPS_LIVADEN_BLATAR
            || layer_type == LAYER_TYPE_VPS_GASKI_ZIMNI
            || layer_type == LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI
            || layer_type == LAYER_TYPE_VPS_PASISHTA) {
        return false;
    }

    return true;
}

/**
 * Selects plots from rows array on the map
 * @param {Array} rows
 * @returns {undefined}
 */
function selectFilteredPlots(rows){
    var layer = addFilteredPlotsLayer();

    if(!layer) {
        return;
    }

	var in_options = {
		'internalProjection': map.baseLayer.projection,
		'externalProjection': new OpenLayers.Projection("EPSG:32635")
	};

    var features = [];

    for(var i = 0; i < rows.length; ++i) {
        var feature = new OpenLayers.Format.WKT(in_options).read(rows[i].st_astext);

        if (typeof rows[i].gid !== 'undefined') {
            feature.attributes.gid = rows[i].gid;
        }
        if (typeof rows[i].id !== 'undefined') {
            feature.attributes.id = rows[i].id;
        }

        features.push(feature);
    }

    layer.addFeatures(features);
    var extent = layer.getDataExtent();
    if(extent) {
        map.zoomToExtent(extent);
    }
}

/**
 * Adds vector layer for filtered plots to map
 * @returns {Boolean|OpenLayers.Layer.Vector}
 */
function addFilteredPlotsLayer() {
    var layer = map.getLayersByName('Filtered plots');

    if(layer[0]) {
        map.removeLayer(layer[0]);
    }

    var combotree = jQuery('#all-layers-tree').combotree('tree');
	var selectedLayer = combotree.tree('getSelected');

    if(!selectedLayer) {
        return false;
    }

    var layerColor = selectedLayer.attributes.fill_color;
    var color = 'f7ff00';

	if (layerColor) {
		while (isNeighborColor(hexToRgb(color), hexToRgb(layerColor)) || isNeighborColor(hexToRgb(color), hexToRgb('4fffff'))) {
			color = Math.floor(Math.random()*16777215).toString(16);
		}
	}

    var renderer = OpenLayers.Util.getParameters(window.location.href).renderer;
	renderer = (renderer) ? [renderer] : OpenLayers.Layer.Vector.prototype.renderers;

	var layer = new OpenLayers.Layer.Vector("Filtered plots", {
		styleMap: new OpenLayers.StyleMap(new OpenLayers.Style({
            cursor: "pointer",
            graphicName: 'square',
            pointRadius: 10,
            strokeDashstyle: "8 8",
            strokeColor: '#' + color,
            strokeWidth: 5,
            fillColor: '#' + color,
            fillOpacity: 0.5
        }, {
            isDefault: true
		})),
		renderers: renderer
	});

	map.addLayer(layer);

    pushVectorLayerToTopPosition();

    return layer;
}

function exportToExcelData(){
	var combotree = jQuery('#all-layers-tree').combotree('tree');
	var selected = combotree.tree('getSelected');

	var array = [];

	for (var i=0; i<vectors.features.length; i++) {
		array.push(vectors.features[i].attributes.gid);
	}

	var rpcParams = {
		layer_id: selected.id,
		layer_type:selected.attributes.layer_type
	};

	if(Object.keys(_filteredRpcParams).length &&
		!array.length &&
		_filteredRpcParams.layer_type === selected.attributes.layer_type) {
		rpcParams = _filteredRpcParams;
	}

	if(selected.attributes.layer_type === LAYER_KVS_PLOTS)
	{
		rpcParams = getValuesFilterPlots();

		if (!allRowsChecked) rpcParams.gids = getCheckedPlots();
		if(!allRowsChecked && !rpcParams.gids) return false;
		TF.Rpc.Map.KVSGrid.exportToExcel(rpcParams)
			.done(function (data) {
				jQuery('#btn-download-file').attr('href', data);
		        jQuery('#win-download').window('open');
			})
			.fail(function (data) {
				jQuery.messager.alert('Грешка', data.getMessage());
			});
	} else if(selected.attributes.layer_type === LAYER_KOMAS_PLOTS)
	{
		if (!allRowsChecked) rpcParams.gids = getCheckedPlots();
		if(!allRowsChecked && !rpcParams.gids) return false;
		 TF.Rpc.Map.KMSGrid.exportToExcel(rpcParams)
	    .done(function (data) {
	        jQuery('#btn-download-file').attr('href', data);
	        jQuery('#win-download').window('open');
	    })
	    .fail(function (data) {
	        jQuery.messager.alert('Грешка', data.getMessage());
	    });
	} else if(selected.attributes.layer_type === LAYER_ZP_PLOTS)
	{

		var options = jQuery('#plots-tables').datagrid('options');
		var page = undefined;
		var rows = undefined;
		var sort = options.sortName;
		var order = options.sortOrder;

		if (!allRowsChecked) rpcParams.gids = getCheckedPlots();
		if(!allRowsChecked && !rpcParams.gids) return false;
		 TF.Rpc.Map.ZPGrid.exportToExcel(rpcParams, page, rows, sort, order)
	    .done(function (data) {
	        jQuery('#btn-download-file').attr('href', data);
	        jQuery('#win-download').window('open');
	    })
	    .fail(function (data) {
	        jQuery.messager.alert('Грешка', data.getMessage());
	    });
	} else if(selected.attributes.layer_type === LAYER_ISAK_PLOTS)
	{
		var options = jQuery('#plots-tables').datagrid('options');
		var page = undefined;
		var rows = undefined;
		var sort = options.sortName;
		var order = options.sortOrder;

		if (!allRowsChecked) rpcParams.gids = getCheckedPlots();
		if(!allRowsChecked && !rpcParams.gids) return false;
		TF.Rpc.Map.IsakGrid.exportToExcel(rpcParams, page, rows, sort, order)
	    .done(function (data) {
	        jQuery('#btn-download-file').attr('href', data);
	        jQuery('#win-download').window('open');
	    })
	    .fail(function (data) {
	        jQuery.messager.alert('Грешка', data.getMessage());
	    });
	} else if(selected.attributes.layer_type === LAYER_TYPE_WORK_LAYER) {
		if (!allRowsChecked) rpcParams.gids = getCheckedPlots();
		if(!allRowsChecked && !rpcParams.gids) return false;
        TF.Rpc.Map.WorkLayerGrid.exportToExcel(rpcParams)
            .done(function (data) {
                jQuery('#btn-download-file').attr('href', data);
                jQuery('#win-download').window('open');
            })
            .fail(function (data) {
                jQuery.messager.alert('Грешка', data.getMessage());
            });
	} else if(selected.attributes.layer_type === LAYER_TEMP_DATA) {
        if (!allRowsChecked) rpcParams.gids = getCheckedPlots();
		if(!allRowsChecked && !rpcParams.gids) return false;
        TF.Rpc.Map.GpsGrid.exportToExcel(rpcParams)
            .done(function (data) {
                jQuery('#btn-download-file').attr('href', data);
                jQuery('#win-download').window('open');
            })
            .fail(function (data) {
                jQuery.messager.alert('Грешка', data.getMessage());
            });
    }
}

function exportToExcelCadastralMapData(){
	var combotree = jQuery('#all-layers-tree').combotree('tree');
	var selected = combotree.tree('getSelected');

	var array = [];

	for (var i=0; i<vectors.features.length; i++) {
		array.push(vectors.features[i].attributes.gid);
	}

	var rpcParams = {
		layer_id: selected.id,
		layer_type:selected.attributes.layer_type
	};

	if(Object.keys(_filteredRpcParams).length &&
		!array.length &&
		_filteredRpcParams.layer_type === selected.attributes.layer_type) {
		rpcParams = _filteredRpcParams;
	}

	if(selected.attributes.layer_type === LAYER_KVS_PLOTS)
	{
		rpcParams = getValuesFilterPlots();

		if (!allRowsChecked) rpcParams.gids = getCheckedPlots();
		if(!allRowsChecked && !rpcParams.gids) return false;
		TF.Rpc.Map.KVSGrid.exportToExcelCadastralMap(rpcParams)
			.done(function (data) {
				jQuery('#btn-download-file').attr('href', data);
				jQuery('#win-download').window('open');
			})
			.fail(function (data) {
				jQuery.messager.alert('Грешка', data.getMessage());
			});
	}
}

function getCheckedPlots() {
	checkedPlotIDS = [];
	var checkedPlotsData = sessionStorage.checkedPlots ? JSON.parse(sessionStorage.checkedPlots) : {};

	if(Object.keys(checkedPlotsData).length === 0) {
		jQuery.messager.alert('Грешка', 'Моля маркирайте желаните парцели чрез отметка пред тях.','warning');
		return;
	}

	jQuery.each(checkedPlotsData,function (pageId, pageData) {
		jQuery.each(pageData, function (index, plotId) {
			checkedPlotIDS.push(plotId);
		})
	});

	return checkedPlotIDS;
}

function saveLayerCopy(){
    if(jQuery('#select-for-isak-layer').combobox('getData')[0].layer_id !== '')
    {
        if (jQuery('#select-for-isak-layer').combobox('isValid'))
        {
            var to_layer_id = jQuery('#select-for-isak-layer').combobox('getValue');
            var to_data = jQuery('#select-for-isak-layer').combobox('getData');
            if (typeof _copyToolMapSelected !== 'undefined') {

                copyMapSelectedLayer(to_layer_id, to_data, to_data[0].layer_type);

                jQuery('#win-layer-copy').window('close');
            }else {
                copyCheckedLayerCustom(to_layer_id, to_data, to_data[0].layer_type, _copyFiltered);

                jQuery('#win-layer-copy').window('close');
            }

        }else {
            jQuery.messager.alert('Грешка', 'Моля изберете слой, в който да копирате данните!','warning');
            return false;
        }
    }
}

function initMultiEditLayer() {
	var combotree = jQuery('#all-layers-tree').combotree('tree'),
		selected = combotree.tree('getSelected');

	if (selected.attributes.layer_type === LAYER_ZP_PLOTS) {
		jQuery('#win-multi-zp-edit').window('open');
		initZPLayerMultiEdit();
	};
}

function getLayerEkatteFilter(layerType){
    var ekatteData = {
        0 : {
            ekate:"",
            text:"Всички"
        }
    };

    switch (layerType)
    {
        case LAYER_KVS_PLOTS:
            ekatteData =  jQuery('#search-ekatte').combobox('getData');
            break;
    }

    return ekatteData;
}

function ClippingWithKVS(){
	var combotree = jQuery('#all-layers-tree').combotree('tree');
	var layerData = combotree.tree('getSelected');
	var params = {
		'layerA': layerData.attributes.layer_name,
		'layerType': layerData.attributes.layer_type,
	};

	jQuery.messager.confirm('Потвърждение', "Тази операция може да отнеме няколко минути." , function(r) {
		if (r) {
			TF.Rpc.Map.MapTools.clippingWithKVSAndExportExcel(params)
				.done(function (result) {
					if(result) {
						jQuery('#btn-download-file').attr('href', result);
						jQuery('#win-download').window('open');
					} else {
						jQuery.messager.alert('Грешка', "Не бяха открити парцели за пресичане.");
					}
				})
				.fail(function (result) {
					jQuery.messager.alert('Грешка', "Нещо се обърка. Моля опитайте по-късно.");
				});
		}
	});
}