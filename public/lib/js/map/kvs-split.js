jQuery(function () {
    jQuery.fn.combobox.defaults.loader = EasyUIRPCLoaders.EasyUIGridCustomLoader.loader;
    jQuery.fn.combobox.defaults.loadFilter = EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter;
});

function initKVSSplitPrGrid(beforeSplitGid) {
    var featuresIds = new Array();
    var featureDocumentAreas = new Array();
    var featureNumbers = new Array();

    for(let i=0; i<vectors.features.length; i++)
    {
        featuresIds.push(vectors.features[i].id.substr(26));
        if (vectors.features[i].hasOwnProperty('attributes') && vectors.features[i].attributes.hasOwnProperty('document_area')) {
            var area = (vectors.features[i].attributes.document_area / 1000).toFixed(3);
            var plotNumber = vectors.features[i].attributes.number;

            featureDocumentAreas.push(area);
            featureNumbers.push(plotNumber);
        } else {
            var area = (vectors.features[i].geometry.getGeodesicArea(map.getProjection()) / 1000).toFixed(3);

        }

        vectors.features[i].attributes.label = 'Имот ' + (i+1) + '\n' + area + ' дка';
    }

    featuresIds = featuresIds.join();
    vectors.redraw();

	jQuery('#kvs-split-pr-grid').propertygrid({
		url: 'index.php?map-rpc=kvs-split-propertygrid',
        rpcParams: [{
            gid: beforeSplitGid,
            featuresIds: featuresIds,
            featureDocumentAreas: featureDocumentAreas,
            featureNumbers: featureNumbers,
        }],
		showGroup: true,
		resizable: false,
        border: false,
		columns: [[
				{
					field: 'text',
					title: 'Параметър',
					width: 100
				},
				{
					field: 'value',
					title: 'Стойност',
					width: 100
				}
			]],
		fixed: true,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

    var date = new Date();
	var todayDate = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();

	jQuery('#kvs-split-active-from').datebox({
		value: todayDate,
	});
}

function saveKVSSplit()
{
    var editActiveFrom = jQuery('#kvs-split-active-from').datebox('getValue');
    var gridData = jQuery('#kvs-split-pr-grid').propertygrid('getData');
    var newPlots = [];
    var plotNumbers = [];
    var obj = new Object();

    for(var i=0; i<gridData.rows.length; i++)
    {
        var row = gridData.rows[i];

        if(typeof newPlots[row.groupId] == 'undefined')
        {
            newPlots[row.groupId] = new Object();
        }
        newPlots[row.groupId][row.name] = row.value;
        newPlots[row.groupId]['featureId'] = row.featureId;
        obj.editingGid = row.editingGid;

        if(row.name == 'number')
        {
            plotNumbers.push(row.value);
        }

        if(row.name == 'area_kvs' && row.value == null)
        {
            jQuery.messager.alert('Грешка', 'Моля уверете се, че сте въвели площ по документ за всички имоти.');
            return false;
        }
    }

    var out = [];
    var object = {};

    for (var i=0; i<plotNumbers.length; i++) {
        if(plotNumbers[i] != '')
        {
            object[plotNumbers[i]]=0;
        }
    }
    for (var i in object) {
        out.push(i);
    }

    if(out.length < plotNumbers.length)
    {
        jQuery.messager.alert('Грешка', 'Моля уверете се, че сте въвели различни номера за всички имоти.');
        return false;
    }

    obj.features = [];
    obj.newPlots = newPlots;
    obj.editActiveFrom = editActiveFrom;

    for (i = 0; i < vectors.features.length; i++)
    {
        obj.features[i] = new Object();

        obj.features[i].id = vectors.features[i].id.substr(26);

        var geometryObj = vectors.features[i].geometry.clone();

        obj.features[i].geometry = geometryObj.transform(
                new OpenLayers.Projection("EPSG:900913"),
                new OpenLayers.Projection("EPSG:32635")
                ).toString();
    }

    TF.Rpc.Map.MapTools.saveKVSSplit(obj)
    .done(function (data) {
        splitResult(data);
        vectors.removeAllFeatures();
        reloadLayerByName('layer_kvs');
    })
    .fail(function (errorObj) {

    });
}

function splitResult(data)
{
    if(data && data.type == 'existNums')
    {
        jQuery.messager.alert('Грешка', 'Следните номера: ' + data.existNums + ' вече съществуват в базата данни', 'error');

        return false;
    }

    if(data && data.type == 'contracts')
    {
        jQuery.messager.alert('Съобщение', 'Редактираните имоти участват в следните договори: ' + data.contracts.c_num + '. Моля проверете ги.<br/>'
                                + '<a href="index.php?page=Contracts.Home&contract_id=' + data.contracts.c_id + '" target="_blank">Отиди в договори</a>');
    }

    jQuery('#win-kvs-split').window('close');
    reloadLayerByName('layer_kvs');
    vectors.removeAllFeatures();
    toggleGeometryTools(LAYER_KVS_PLOTS);
}
