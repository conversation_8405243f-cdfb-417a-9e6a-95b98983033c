function initZPGrid(layer_id)
{
	var checkedElement;
	var array = [];

	for (var i=0; i<vectors.features.length; i++) {
		array.push(vectors.features[i].attributes.id);
	}

	jQuery('#plots-tables').datagrid({
		url: 'index.php?map-rpc=zp-datagrid',
		rpcParams: [{
			layer_id: layer_id,
			layer_type:1,
			ids: array,
		}],
		singleSelect: true,
        checkOnSelect: false,
        selectOnCheck: false,
		iconCls: 'icon-plot',
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		pageSize: 20,
		fit: true,
		showFooter: true,
		sortName: 'isak_prc_uin',
		sortOrder: 'asc',
		idField: 'id',
		pagination: true,
		rownumbers: true,
		width: '100%',
		border: false,
		frozenColumns: [[
				{
					field: 'ck',
					checkbox: true
				}
			]],
		columns: [[
				{
					field: 'area_name',
					title: '<b>Име на парцела</b>',
					sortable: true,
					width: 120
				},
				{
					field: 'isak_prc_uin',
					title: '<b>ИСАК номер</b>',
					sortable: true,
					width: 120
				},
				{
					field: 'ekatte',
					title: '<b>ЕКАТТЕ</b>',
					sortable: true,
					width: 60
				},
				{
					field: 'culture',
					title: '<b>Култура</b>',
					sortable: true,
					width: 150
				}, {
					field: 'obrabotki',
					title: '<b>Обработки</b>',
					sortable: true,
					width: 70
				}, {
					field: 'dobivi',
					title: '<b>Добиви</b>',
					sortable: true,
					width: 70
				}, {
					field: 'napoqvane',
					title: '<b>Напояване</b>',
					sortable: true,
					width: 70
				}, {
					field: 'polivki',
					title: '<b>Поливки</b>',
					sortable: true,
					width: 70
				}, {
					field: 'polzvatel',
					title: '<b>Ползвател</b>',
					sortable: true,
					width: 130
				}, {
					field: 'area_zp',
					title: '<b>Площ(дка)</b>',
					sortable: true,
					width: 66
				}
			]],
        onUnselect: function(rowIndex, rowData) {

        },
        onSelect: function(rowIndex, rowData) {
            checkedElement = rowData;
            displayFeatureSelection(rowData.st_astext, rowData);
        },
		toolbar: '#attr-tables-toolbar',
		onBeforeLoad: function() {
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function initZPLayerMultiEdit() {

	jQuery('#me-zp-ekatte > input').combobox({
        data: ComboboxData.EkateCombobox,
    	valueField: 'ekate',
    	textField: 'text',
		filter: function(q, row){
			var opts = jQuery(this).combobox('options');
			var text = row[opts.textField].toLowerCase();
			var value = row[opts.valueField];
			var find = q.toLowerCase();
			if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
			{
				return true;
			}
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

	jQuery('#me-zp-culture > input').combobox({
        data: ComboboxData.CultureCombobox,
    	valueField: 'id',
    	textField: 'name',
		filter: function(q, row){
			var opts = jQuery(this).combobox('options');
			var text = row[opts.textField].toLowerCase();
			var value = row[opts.valueField];
			var find = q.toLowerCase();
			if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
			{
				return true;
			}
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    clearZpMultiEditFields();

    jQuery('#btn-zp-multi-edit').bind('click', function (e) {
    	e.preventDefault();
    	e.stopImmediatePropagation();
    	e.stopPropagation();
    	var requestData = getZPMultiEditFields(),
    		combotree = jQuery('#all-layers-tree').combotree('tree'),
			selected = combotree.tree('getSelected');
		requestData.layer_name = selected.attributes.layer_name;
    	jQuery.messager.confirm('Внимание', 'Ако не са приложени никакви филтри, то действието ще има ефект върху всички парцели за избраната стопанска година и избраното стопанство. Сигурни ли сте, че искате да продължите?', function (ans) {
    		if (ans) {
    			TF.Rpc.Map.ZPGrid.multiEdit(requestData)
		    		.done(function () {
		    			jQuery('#win-multi-zp-edit').window('close');
		    			jQuery('#plots-tables').datagrid('loadRpc');
		    		})
		    		.fail(function (errorObj) {
		    			jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
		    		});
	    		return true;
	    	} else {
	    		return false;
		    }
    	});
    	return false;
    });
}

function getZPMultiEditFields () {
	return {
		ekate: jQuery("#me-zp-ekatte > input").combobox('getValue'),
		culture: jQuery("#me-zp-culture > input").combobox('getValue'),
		obrabotki: jQuery("#me-zp-obrabotki > input").val(),
		dobivi: jQuery("#me-zp-dobivi > input").val(),
		napoqvane: jQuery("#me-zp-napoqvane > input").val(),
		polivki: jQuery("#me-zp-polivki > input").val(),
		polzvatel: jQuery("#me-zp-polzvatel > input").val()
	}
}

function clearZpMultiEditFields() {
	jQuery("#me-zp-ekatte > input").combobox('reset');
	jQuery("#me-zp-culture > input").combobox('reset');
	jQuery("#me-zp-obrabotki > input").val('');
	jQuery("#me-zp-dobivi > input").val('');
	jQuery("#me-zp-napoqvane > input").val('');
	jQuery("#me-zp-polivki > input").val('');
	jQuery("#me-zp-polzvatel > input").val('');
}
