function initOszPropertyGrid(data) {
	jQuery('#osz-property-grid').propertygrid({
		data: data,
		showGroup: true,
		scrollbarSize: 0,
		resizable: true,
		width: 320,
		height: 327,
		columns: [[
		{
			field: 'name',
			title: 'Параметър',
			width: 120,
		},
		{
			field: 'value',
			title: 'Стойност',
			width: 180,
		},
		]],
		groupFormatter: function(group, rows) {
			for (var i = 0; i < rows.length; i++) {
				if (rows[i].group == "Активни договори") {
					rows[i].value = '<a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="showContractInNewTab('+rows[i].id+','+ rows[i].is_annex+')">'+rows[i].value+'</a>';
				};
			};
			return group;
		}
	});
}

function initManualPropertyGrid(layer_id, plot_id, layer_type) {
	if (layer_type == LAYER_KVS_PLOTS) {
		jQuery('#property-win').window('resize', {width: 500, height: 560});
		jQuery('#property-grid').propertygrid({
			width: '100%',
			height: 472
		});
	}
	if (layer_type == LAYER_ISAK_PLOTS) {
		jQuery('#property-win').window('resize', {height: 377});
		jQuery('#property-grid').propertygrid({
			width: 317,
			height: 277
		});
	}
	if (layer_type == LAYER_ZP_PLOTS) {
		jQuery('#property-win').window('resize', {width: 390,height: 450});
		jQuery('#property-grid').propertygrid({
			width: 360,
			height: 355,
		});
	}
	if (layer_type == LAYER_FOR_ISAK_PLOTS) {
		jQuery('#property-win').window('resize', {width: 390, height:450});
		jQuery('#property-grid').propertygrid({
			width: 360,
			height: 355,
		});
	}
	if (layer_type == LAYER_KOMAS_PLOTS) {
		jQuery('#property-win').window('resize', {width: 360, height: 365});
		jQuery('#property-grid').propertygrid({
			width: '100%',
			height: 280
		});
	}
	if (layer_type == LAYER_TEMP_DATA) {
		jQuery('#property-win').window('resize', {height: 308});
		jQuery('#property-grid').propertygrid({
			width: 336,
			height: 210
		});
	}
	if (layer_type == LAYER_TYPE_WORK_LAYER) {
		jQuery('#property-win').window('resize', {width: 370, height: 400});
		jQuery('#property-grid').propertygrid({
			width: 340,
			height: 300
		});
	}
	window.TF.Loading.start();
	jQuery('#property-win').window('open');
	jQuery('#property-win').window('center');

	jQuery('#property-grid').propertygrid({
		url: 'index.php?map-rpc=layer-pg',
		rpcParams: [{
			request: 'manual',
			layer: layer_id,
			plot_id: plot_id
		}],
		showGroup: true,
		resizable: false,
		columns: [[
				{
					field: 'name',
					title: 'Параметър',
				},
				{
					field: 'value',
					title: 'Стойност',
					width: 150
				},
			]],
		fixed: true,
		onLoadSuccess: function(data)
		{
			if(data.rows[0].attributes != undefined)
			{
				jQuery('#property-tabs').tabs('enableTab',1);
				initOszPropertyGrid(data.rows[0].attributes.oszData);
			}

			jQuery('#property-tabs').tabs('disableTab',1);
			jQuery('#property-tabs').tabs('select', 1);
			jQuery('#property-tabs').tabs('select', 0);
			setTimeout(function (){
				window.TF.Loading.end();
			}, 500);
		},
		onLoadError: function(data) {
			window.TF.Loading.end();
		},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function propertyWindowFunction(e)
{
	var combotree = jQuery('#all-layers-tree').combotree('tree');
	var layerData = combotree.tree('getSelected');
	var layer_id = layerData.parent_id ?? layerData.id;

	bounds = map.getExtent();

	x1 = parseInt(e.xy.x);
	y1 = parseInt(e.xy.y);

	jQuery('#property-grid').propertygrid({
		url: 'index.php?map-rpc=layer-pg',
		rpcParams: [{
			bbox: bounds.toBBOX(),
			x: x1,
			y: y1,
			width: map.size.w,
			height: map.size.h,
			layer: layer_id,
			combineOszInfo: true
		}],
		showGroup: true,
		scrollbarSize: 0,
		resizable: true,
		columns: [[
				{
					field: 'name',
					title: 'Параметър',
					width: 120
				},
				{
					field: 'value',
					title: 'Стойност',
					width: 180
				},
			]],
		onLoadSuccess: function(data)
		{
			if (data.total > 0) {
				var rowCount = data['rows'].length;
				var layerType = data['rows'][rowCount - 1]['layer_type'];
				jQuery('#property-win').window('open');

				if (layerType !== LAYER_KVS_PLOTS) {
					jQuery('#property-tabs').tabs('disableTab', 1);
				}

				if (layerType == LAYER_KVS_PLOTS) {
					jQuery('#property-win').window('resize', {height: 560});
					jQuery('#property-grid').propertygrid('resize', {
						height: 447
					});
					jQuery('#property-tabs').tabs('enableTab', 1);
				}
				if (layerType == LAYER_ISAK_PLOTS) {
					jQuery('#property-win').window('resize', {height: 470});
					jQuery('#property-grid').propertygrid('resize', {
						height: 350
					});
				}
				if (layerType == LAYER_FOR_ISAK_PLOTS) {
					jQuery('#property-win').window('resize', {height: 470});
					jQuery('#property-grid').propertygrid('resize', {
						height: 350
					});
				}
				if (layerType == LAYER_ZP_PLOTS) {
					jQuery('#property-win').window('resize', {height: 540});
					jQuery('#property-grid').propertygrid('resize', {
						height: 425
					});
				}
				if (layerType == LAYER_KOMAS_PLOTS) {
					jQuery('#property-win').window('resize', {height: 420});
					jQuery('#property-grid').propertygrid('resize', {
						height: 300
					});
				}
				if (layerType == LAYER_TEMP_DATA) {
					jQuery('#property-win').window('resize', {height: 370});
					jQuery('#property-grid').propertygrid('resize', {
						height: 250
					});
				}
				jQuery('#property-tabs').tabs('select', 1);
				jQuery('#property-tabs').tabs('select', 0);

				let oszTab = jQuery('#property-tabs').tabs('getTab', 1);
				oszTab.panel('options').tab.hide();
				oszTab.panel('close');
				jQuery('#property-win').window('center');
			} else {
				jQuery.messager.alert('Грешка','Не е избран обект, за който да бъде показана информация.');
			};
		},
		onLoadError: function(data) {
			//endLoading();
		},
		groupFormatter: function(group, rows) {	
				for (var i = 0; i < rows.length; i++) {
					if (rows[i].group == "Активни договори" && rows[i].value != null) {
						rows[i].value = '<a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="showContractInNewTab('+rows[i].id+','+ rows[i].is_annex+')">'+rows[i].value+'</a>';
					};
				};
			
	
			return group;
		},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function showContractInNewTab(contract_id, is_annex) {
    if(is_annex == true) {
       window.open("index.php?page=Contracts.Home&contract_id=" + contract_id, '_blank');
    } else{
        window.open("index.php?page=Contracts.Home&contract_id=" + contract_id, '_blank');
    }
}
