var contextLayer     	= undefined,
	lastSelectedLayer   = undefined,
	lastTransferedLayer = undefined;
	skipOnCheckEvent    = false;
	kvsLayerEkatesToCheckCount = 0;
function initAllLayersTree()
{
	var isTreeDataLoaded = false,
        selectedLayerId,
        tmpData,
        tmpEl,
        checkedLayerIds = [],
	    allLayersTree = jQuery('#all-layers-tree').combotree({
		url: 'index.php?map-rpc=all-layers-tree',
		rpcParams:[{
			allowable:true,
			vps:true
		}],
		panelHeight: 410,
		panelWidth: 400,
		width: 150,
		height: 27,
		iconCls: 'icon-edit-geometry',
		iconAlign:'left',
		value: 'Не е избран',
    	checkbox: true,
    	multiple: true,
    	cascadeCheck: true,
    	animate: true,
		lines: true,
		dnd: true,
		onClick: function(node){
			setComboTreeText(node, allLayersTree);

			var isLeaf = checkLeaf(node);
			if(!isLeaf) {
				return false;
			}



			var selected = combotree.tree('getSelected');
			var checked = combotree.tree('getChecked');
			var lastSelectedLayerId;

			if(lastSelectedLayer) {
				lastSelectedLayerId = lastSelectedLayer.id
			}

			if(!selected && !node.checked && node.id != lastSelectedLayerId) {
				combotree.tree('select', node.target);
				triggerTreeCheck(node, false);
			}
		},
		onBeforeCheck: function (node) {
			var isLeaf = checkLeaf(node);
			var children = combotree.tree('getChildren', node.target);

			if (children.length == 0 && node.attributes.is_farming) {
				return false;
			}
		},
		onBeforeSelect: function (node) {
			var isLeaf = checkLeaf(node);
			var children = combotree.tree('getChildren', node.target);
			if (children.length == 0 && node.attributes.is_farming) {
				return false;
			}

			if((!isLeaf || node.attributes.is_farming) && node.attributes.layer_type !== LAYER_KVS_PLOTS) {
				return false;
			}

			//save last selected layer
			lastSelectedLayer = combotree.tree('getSelected')
		},
		onSelect: function(node) {
    		var children = combotree.tree('getChildren', node.target);

    		if (children.length == 0 && node.attributes.is_farming) {
    			return false;
    		}

    		layerInAttrTable = null;

			//remove all selection
			vectors.removeAllFeatures();

			unselectAll();
			deactivateAllControls();
			clearCheckedPlotsInAttributeTables();
			chooseControl('navigation');
			jQuery('#win-polygon-area').window('close');
			toggleGeometryTools(node['attributes'].layer_type);
			setComboTreeText(node, allLayersTree);
		},
		onCheck: function(node, isChecked) {
			if (!isTreeDataLoaded) {
				return false;
			}

			if (skipOnCheckEvent === true && node['attributes'].layer_type == LAYER_KVS_PLOTS) {
				return false;
			}

			var selected = combotree.tree('getSelected');

			if (!isChecked && selected && selected.id == node.id) {
				removeLayerByName(node['attributes'].layer_name);
				

				combotree.tree('unselect', node.target);
				setComboTreeText(node, allLayersTree);
				checkToolCrossLayers();

				if(node['attributes'].layer_type !== LAYER_KVS_PLOTS) {
					return;
				}
				
			}

			var children = combotree.tree('getChildren', node.target);
			if (!isChecked && children.length === 0) {
				removeLayerByName(node['attributes'].layer_name);
				setComboTreeText(node, allLayersTree);
				checkToolCrossLayers();
			}

			if (children.length) {
				for (var i=0; i < children.length; i++) {
					if(isChecked)
					{
						loadMapLayers(children[i]);
					} else {
						removeLayerByName(children[i]['attributes'].layer_name);

						if(selected && selected.id == children[i].id) {
							combotree.tree('unselect', children[i].target);
							setComboTreeText(children[i], allLayersTree);
						}
					}
				}
			}

			let checkedLayers = [];
			if(node['attributes'].layer_type === LAYER_KVS_PLOTS) { //Only for KVS
				removeLayerByName('layer_kvs');
				if(!isChecked && node.children && node.children.length > 0) {
					for(const child of node.children) {
						combotree.tree('unselect', child.target);
					}
				}

				checkedLayers = getCheckedElements(node);
				if(checkedLayers.length === 0) {
					zoomToBulgaria();
					return;
				}

				loadMapLayerKvs(
					'layer_kvs',
					checkedLayers.map(layer => layer.attributes.extent),
					checkedLayers.map(layer => layer.attributes.ekatte.toString())
				);

				setComboTreeText(node, allLayersTree);
				tryActivatePolygonSelectControl(selected);
				return;
			}

			var parent = combotree.tree('getParent', node.target);
			if (parent && node['attributes'].layer_name !== 'layer_kvs') {
				loadMapLayers(node);
			}

			tryActivatePolygonSelectControl(selected);
			setComboTreeText(node, allLayersTree);
			checkToolCrossLayers();
		},
		onContextMenu: function(e, node) {
			e.preventDefault();
			if (!node.checked) {
				triggerTreeCheck(node, false);
			}
			if ((node['attributes'].is_system == false && node['attributes'].level == 3) || (node['attributes'].is_system == true && node['attributes'].level == 2))
			{
				jQuery('#all-layers-tree-cm').menu('show', {
					left: e.pageX,
					top: e.pageY
				});
			}
		},
		onLoadSuccess: function () {
			isTreeDataLoaded = true;
            tmpData = combotree.tree('getChildren');

            var selectedLayerStorage = store.get('selectedLayerStorage') || null;
            var checkedLayerStorage = store.get('checkedLayerStorage') || [];
			setkvsLayerEkatesToCheckCount(checkedLayerStorage);

			tmpData.forEach(function (el) {
				if(checkedLayerStorage.length > 0) {
					checkedLayerStorage.forEach(function (element) {
						if(element.uuid && el.uuid == element.uuid && !element.children) {
							triggerTreeCheck(el, true);
						}
					});
				}
			});
			
            tmpData.every(function (el) {
				if (selectedLayerStorage != null) {
					if(el.uuid == selectedLayerStorage.uuid) {
						combotree.tree('select', el.target);
						initPolygonSelectControl(getLayerIndexByName(el.attributes.layer_name));
						return false;
					}
				}
				return true;
            });

			if (lastTransferedLayer) {
				var newTarget = combotree.tree('find', lastTransferedLayer);
				combotree.tree('expandTo', newTarget.target);
				combotree.tree('select', newTarget.target);
				lastTransferedLayer = undefined;
			}

            checkToolCrossLayers();
            map.layers.forEach(function (layer) { layer.redraw(true)});
        },
		onBeforeDrag: function (node) {
			if (node.attributes.layer_type !== 19) {
				return false;
			}

		},
		onDragOver: function (target) {
			var node = combotree.tree('getNode', target);

			if (!node.attributes.is_year && !node.attributes.is_work_layers) {
				return false;
			}
		},
		onDrop: function (target,source,point) {
			var node = combotree.tree('getNode', target),
				parent = combotree.tree('getParent', target),
				year = node.id,
				farming,
				layerID = source.id,
				updateObj = {};

			if (parent) {
				farming = parent.id;
			}

			updateObj.farming = farming;
			updateObj.year = year;
			updateObj.layerID = layerID;
			lastTransferedLayer = layerID;
			TF.Rpc.Map.MapLayerChange.updateWorkLayerAttributes(updateObj)
				.done(function () {
				    saveZoomMapCenterLS();
					saveCheckSelectLayersLS();
					jQuery('#all-layers-tree').combotree('loadRpc');
				})
				.fail(function (errorObj) {
					if (errorObj.is(TF.Rpc.ExceptionsList.MAP_LAYER_EDIT_FAILED)) {
						jQuery.messager.alert('Грешка',errorObj.getMessage);
					}
				});
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});


	var combotree = jQuery('#all-layers-tree').combotree('tree');
}

function tryActivatePolygonSelectControl(selected) {
	if(selected) {
		//init active layer for polygon selection
		var layer_map_index = getLayerIndexByName(selected['attributes'].layer_name);
		if (layer_map_index) {
			initPolygonSelectControl(layer_map_index);
		}
	}
}

/**
 * [Used as wrapper on onCheck event]
 *
 * @param   {[object}  node                   [Easyu Ui tree object]
 * @param   {[boolen]}  forceSkipCheckEvents  [used to skip logic in onCheck event to executes]
 *
 */
function triggerTreeCheck(node, forceSkipCheckEvents = false) 
{
	let combotree = jQuery('#all-layers-tree').combotree('tree');
	if (kvsLayerEkatesToCheckCount > 0) {		
		skipOnCheckEvent = forceSkipCheckEvents;
		if (skipOnCheckEvent === true) {
			if (node.attributes.layer_type === LAYER_KVS_PLOTS) {
				kvsLayerEkatesToCheckCount --;
		
				if (kvsLayerEkatesToCheckCount == 0) {
					skipOnCheckEvent = false;
					combotree.tree('check', node.target);
					return ;
				}
			}
		}
	}

	combotree.tree('check', node.target);
}

/**
 * [set KVS ekates count to be checked]
 *
 * @param   {[object]}  checkedLayerStorage  [checked Layers session storage object]
 *
 * @return  {[void]}
 */
function setkvsLayerEkatesToCheckCount(checkedLayerStorage) 
{
	const ekates = checkedLayerStorage.filter(function (element){
		if (element.hasOwnProperty('parent_id') && element.attributes.layer_type === LAYER_KVS_PLOTS) {
			return element;
		}
	});
	kvsLayerEkatesToCheckCount = ekates.length;
}

function checkLeaf(node) {
	var combotree = jQuery('#all-layers-tree').combotree('tree');
	var isLeaf = combotree.tree('isLeaf', node.target);

	if (!isLeaf) {
		return false;
	}

	return true;
}

function loadMapLayers(node) {
	if (node['attributes'].layer_name && node.checked) {
        if(node['attributes'].layer_type == 'layer_allowable'
        	|| node['attributes'].layer_type == LAYER_TYPE_LFA
        	|| node['attributes'].layer_type == LAYER_TYPE_NATURA_2000
        	|| node['attributes'].layer_type == LAYER_TYPE_PERMANETELY_GREEN_AREAS
        	|| node['attributes'].layer_type == LAYER_TYPE_VPS_PASISHTA
        	|| node['attributes'].layer_type == LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI
        	|| node['attributes'].layer_type == LAYER_TYPE_VPS_GASKI_ZIMNI
        	|| node['attributes'].layer_type == LAYER_TYPE_VPS_LIVADEN_BLATAR
        	|| node['attributes'].layer_type == LAYER_TYPE_VPS_ORLI_LESHOYADI)
        {
        	if(node['attributes'].layer_name == 'layer_allowable')
    		{
        		loadMapLayerAllowable();
    		} else if(node['attributes'].layer_name == 'layer_allowable_final')
    		{
    			loadMapLayerAllowableFinal();
    		} else {
    			loadMapRemoteLayer(node['attributes'].layer_name);
    		}
        } else if(node['attributes'].layer_name.startsWith('layer_kvs')) {
			return;
		}
        else
        {
            loadMapLayer(node['attributes'].layer_name, node['attributes'].extent);
        }
	}
}

function searchInObjecsArray(nameKey, myArray){
    for (var i=0; i < myArray.length; i++) {
        if (myArray[i].node_id === nameKey) {
            return true;
        }
    }
    return false;
}

function loadMapLayer(layer_name, extent) {
	var layers = map.getLayersByName(layer_name);

	if (layer_name && extent && layers.length == 0) {
		var layerExtent = new OpenLayers.Bounds.fromString(extent).transform(
				new OpenLayers.Projection("EPSG:32635"),
				map.getProjectionObject()
				);

		var layerData = new OpenLayers.Layer.WMS(
				layer_name,
				wmsServer + "?map=" + mapPath + groupID + '.map',
				{
					layers: layer_name,
					format: 'image/png',
					transparent: "true"
				});
		map.addLayer(layerData);
		layerData.redraw(true);

		//vector layer should always be the top layer
		pushVectorLayerToTopPosition();
	}
}

function loadMapLayerKvs(layer_name, extents, ekatte = []) {
	var params = {
		layers: 'layer_kvs',
		ekatte: ekatte.join(','),
		format: 'image/png',
		transparent: "true"
	};
	var layerData = new OpenLayers.Layer.WMS(
		layer_name,
		wmsServer + "?map=" + mapPath + groupID + '.map', params);
	map.addLayer(layerData);
	layerData.redraw(true);

	zoomToExtent(getMapExtent(extents));

	//vector layer should always be the top layer
	pushVectorLayerToTopPosition();
}

function loadMapLayerAllowable() {
	var layers = map.getLayersByName('layer_allowable');

	if(layers.length > 0) {
		return;
	}

    var layerData = new OpenLayers.Layer.WMS(
            'layer_allowable',
            login3WmsServer + "?map=" + login3MapPath + 'layer_allowable_draft.map',
            {
                layers: 'layer_allowable_draft',
                format: 'image/png',
                transparent: "true"
            });
    map.addLayer(layerData);
    layerData.redraw(true);

    //vector layer should always be the top layer
    pushVectorLayerToTopPosition();
}

function loadMapLayerAllowableFinal() {
	var layers = map.getLayersByName('layer_allowable_final');

	if(layers.length > 0) {
		return;
	}

    var layerData = new OpenLayers.Layer.WMS(
            'layer_allowable_final',
            login3WmsServer + "?map=" + login3MapPath + 'layer_allowable_final.map',
            {
                layers: 'layer_allowable_final',
                format: 'image/png',
                transparent: "true"
            });
    map.addLayer(layerData);
    layerData.redraw(true);

    //vector layer should always be the top layer
    pushVectorLayerToTopPosition();
}

function loadMapRemoteLayer(layer_name) {
	var layers = map.getLayersByName(layer_name);

	if(layers.length > 0) {
		return;
	}

    var layerData = new OpenLayers.Layer.WMS(
    		layer_name,
            login3WmsServer + "?map=" + login3MapPath + layer_name + '.map',
            {
                layers: layer_name,
                format: 'image/png',
                transparent: "true"
            });
    map.addLayer(layerData);
    layerData.redraw(true);

    //vector layer should always be the top layer
    pushVectorLayerToTopPosition();
}

function removeLayerByName(layer_name) {
	if(!layer_name) {
		return;
	}

	var layers = map.getLayersByName(layer_name);

	for (var i = 0; i < layers.length; i++)
	{
		map.removeLayer(layers[i]);
	}
}

function removeAllTopicLayers() {
	for (var i = 0; i < map.layers.length; i++)
	{
		if (map.layers[i].name.substr(0,19) == 'topic_layer_kvs_by_')
			map.removeLayer(map.layers[i]);
	}
}

function zoomToLayerExtent()
{
	map.zoomToExtent(new OpenLayers.Bounds.fromString(contextLayer['attributes'].extent).transform(
			new OpenLayers.Projection("EPSG:32635"),
			map.getProjectionObject())
			);
}

function toggleGeometryTools(layer_type)
{
	jQuery('#tool-export').menubutton('enable');
	jQuery('#tool-split-polygon').menubutton({
		menu: "#map-tool-split-submenu",
		onClick: () => {}
	}).tooltip("destroy");

	switch(layer_type) {
		case LAYER_TYPE_PERMANETELY_GREEN_AREAS:
			jQuery('#tool-export').menubutton('disable');
			jQuery('#tool-cross-layers').linkbutton('disable');
			break;
		case LAYER_TYPE_NATURA_2000:
			jQuery('#tool-export').menubutton('disable');
			jQuery('#tool-cross-layers').linkbutton('disable');
			break;
		case LAYER_TYPE_LFA:
			jQuery('#tool-export').menubutton('disable');
			jQuery('#tool-cross-layers').linkbutton('disable');
			break;
		case 'layer_allowable':
			jQuery('#tool-export').menubutton('disable');
			jQuery('#tool-cross-layers').linkbutton('disable');
            jQuery('#tool-copy-to-layer').menubutton('disable');
            break;
		case LAYER_TEMP_DATA:
            jQuery('#tool-edit-geometry').linkbutton('disable');
			jQuery('#tool-save').linkbutton('disable');
			jQuery('#tool-delete').linkbutton('disable');
			jQuery('#tool-draw').linkbutton('enable');
			jQuery('#tool-draw-hole').linkbutton('enable');
			jQuery('#tool-merge').linkbutton('enable');
			jQuery('#tool-split').linkbutton('enable');
			jQuery('#tool-split-polygon').menubutton('enable');
			jQuery('#tool-split-polygon').menubutton('enable');
			jQuery('#tool-auto-split').linkbutton('enable');
			jQuery('#tool-delete-all').linkbutton('enable');
			jQuery('#tool-cross-layers').linkbutton('disable');
			jQuery('#tool-copy-to-layer').menubutton('enable');
			jQuery('.js-copy-layer-btn-zp').show();
            jQuery('.js-copy-layer-btn-for-isak').show();
            jQuery('.js-copy-layer-btn').hide();
			break;
		case LAYER_FOR_ISAK_PLOTS:
            jQuery('#tool-edit-geometry').linkbutton('disable');
			jQuery('#tool-save').linkbutton('disable');
			jQuery('#tool-delete').linkbutton('disable');
			jQuery('#tool-draw').linkbutton('enable');
			jQuery('#tool-copy-to-layer').menubutton('enable');
			jQuery('#tool-cross-layers').linkbutton('disable');
			jQuery('.js-copy-layer-btn-zp').show();
            jQuery('.js-copy-layer-btn-for-isak').hide();
            jQuery('.js-copy-layer-btn').show();
            jQuery('#tool-draw-hole').linkbutton('enable');
            jQuery('#tool-merge').linkbutton('enable');
            jQuery('#tool-split').linkbutton('enable');
			jQuery('#tool-auto-split').linkbutton('enable');
			jQuery('#tool-split-polygon').menubutton('enable');
            jQuery('#tool-delete-all').linkbutton('enable');
			break;
		case LAYER_ISAK_PLOTS:
            jQuery('#tool-edit-geometry').linkbutton('disable');
			jQuery('#tool-save').linkbutton('disable');
			jQuery('#tool-delete').linkbutton('disable');
			jQuery('#tool-draw').linkbutton('disable');
			jQuery('#tool-draw-hole').linkbutton('disable');
			jQuery('#tool-merge').linkbutton('disable');
			jQuery('#tool-split').linkbutton('disable');
			jQuery('#tool-split-polygon').menubutton('disable');
			jQuery('#tool-auto-split').linkbutton('disable');
			jQuery('#tool-delete-all').linkbutton('enable');
			jQuery('#tool-cross-layers').linkbutton('disable');
			jQuery('#tool-copy-to-layer').menubutton('enable');
			jQuery('.js-copy-layer-btn-zp').show();
            jQuery('.js-copy-layer-btn-for-isak').show();
            jQuery('.js-copy-layer-btn').show();
            jQuery('.js-copy-layer-btn-work-layer').show();
			break;
		case LAYER_KOMAS_PLOTS:
            jQuery('#tool-edit-geometry').linkbutton('disable');
			jQuery('#tool-save').linkbutton('disable');
			jQuery('#tool-delete').linkbutton('disable');
			jQuery('#tool-draw').linkbutton('disable');
			jQuery('#tool-draw-hole').linkbutton('disable');
			jQuery('#tool-merge').linkbutton('disable');
			jQuery('#tool-split').linkbutton('disable');
			jQuery('#tool-split-polygon').menubutton('disable');
			jQuery('#tool-auto-split').linkbutton('disable');
			jQuery('#tool-delete-all').linkbutton('enable');
			jQuery('#tool-cross-layers').linkbutton('disable');
			jQuery('#tool-copy-to-layer').menubutton('enable');
			jQuery('.js-copy-layer-btn-zp').show();
            jQuery('.js-copy-layer-btn-for-isak').show();
            jQuery('.js-copy-layer-btn').show();
            jQuery('.js-copy-layer-btn-work-layer').show();
			break;
		case LAYER_KVS_PLOTS:
			jQuery('#tool-edit-geometry').linkbutton('disable');
			jQuery('#tool-save').linkbutton('disable');
			jQuery('#tool-delete').linkbutton('disable');
			jQuery('#tool-draw').linkbutton('disable');
			jQuery('#tool-draw-hole').linkbutton('disable');
			jQuery('#tool-merge').linkbutton('enable');
			jQuery('#tool-split').linkbutton('disable');
			jQuery('#tool-split-polygon').menubutton({
				menu: null,
				onClick: function(){
					jQuery.messager.alert('Информация', 'Тази функционалност е достъпна в Agrimi!');
				}
			}).tooltip({
				showEvent: 'mouseenter',
				content: 'Разделяне на обект',
				position: 'right',
			});


			jQuery('#tool-auto-split').linkbutton('disable');
			jQuery('#tool-delete-all').linkbutton('disable');
			jQuery('#tool-cross-layers').linkbutton('disable');
			jQuery('#tool-copy-to-layer').menubutton('enable');
			jQuery('.js-copy-layer-btn-zp').show();
            jQuery('.js-copy-layer-btn-for-isak').show();
            jQuery('.js-copy-layer-btn').show();
            jQuery('.js-copy-layer-btn-work-layer').show();
			break;
		case LAYER_ZP_PLOTS:
			jQuery('#tool-edit-geometry').linkbutton('disable');
			jQuery('#tool-save').linkbutton('disable');
			jQuery('#tool-delete').linkbutton('disable');
			jQuery('#tool-draw').linkbutton('disable');
			jQuery('#tool-draw-hole').linkbutton('enable');
			jQuery('#tool-merge').linkbutton('disable');
			jQuery('#tool-split').linkbutton('disable');
			jQuery('#tool-auto-split').linkbutton('disable')
			jQuery('#tool-split-polygon').menubutton('disable');;
			jQuery('#tool-delete-all').linkbutton('enable');
			jQuery('#tool-cross-layers').linkbutton('disable');
			jQuery('#tool-copy-to-layer').menubutton('enable');
			jQuery('.js-copy-layer-btn-zp').hide();
            jQuery('.js-copy-layer-btn-for-isak').show();
            jQuery('.js-copy-layer-btn').show();
            jQuery('.js-copy-layer-btn-work-layer').show();
			break;
		case LAYER_TYPE_WORK_LAYER:
			jQuery('#tool-edit-geometry').linkbutton('disable');
			jQuery('#tool-save').linkbutton('disable');
			jQuery('#tool-delete').linkbutton('disable');
			jQuery('#tool-draw').linkbutton('enable');
			jQuery('#tool-draw-hole').linkbutton('enable');
			jQuery('#tool-merge').linkbutton('enable');
			jQuery('#tool-split').linkbutton('enable');
			jQuery('#tool-auto-split').linkbutton('enable');
			jQuery('#tool-split-polygon').menubutton('enable');
			jQuery('#tool-delete-all').linkbutton('enable');
			jQuery('#tool-cross-layers').linkbutton('enable');
			jQuery('#tool-copy-to-layer').menubutton('enable');
			jQuery('.js-copy-layer-btn-zp').show();
			jQuery('.js-copy-layer-btn-for-isak').show();
			jQuery('.js-copy-layer-btn').show();
            jQuery('.js-copy-layer-btn-work-layer').show();
			break;
		default:
			jQuery('#tool-edit-geometry').linkbutton('disable');
			jQuery('#tool-save').linkbutton('disable');
			jQuery('#tool-delete').linkbutton('disable');
			jQuery('#tool-draw').linkbutton('disable');
			jQuery('#tool-draw-hole').linkbutton('disable');
			jQuery('#tool-merge').linkbutton('disable');
			jQuery('#tool-split').linkbutton('disable');
			jQuery('#tool-auto-split').linkbutton('disable');
			jQuery('#tool-split-polygon').menubutton('disable');
			jQuery('#tool-delete-all').linkbutton('disable');
			jQuery('#tool-cross-layers').linkbutton('disable');
			jQuery('#tool-copy-to-layer').menubutton('disable');
			jQuery('.js-copy-layer-btn-zp').hide();
            jQuery('.js-copy-layer-btn-for-isak').hide();
            jQuery('.js-copy-layer-btn').hide();
            jQuery('.js-copy-layer-btn-work-layer').hide();
	}
}

function checkToolCrossLayers() {
	//get checked and selected
	var combotree = jQuery('#all-layers-tree').combotree('tree');
    var getChecked = combotree.tree('getChecked');
    var getSelected = combotree.tree('getSelected');

    if(getSelected && getChecked.length > 0 && getSelected.attributes.layer_type !== LAYER_KVS_PLOTS) {
    	jQuery('#tool-cross-layers').linkbutton('enable');
    } else {
    	jQuery('#tool-cross-layers').linkbutton('disable');
    }
}

function initLayerEditFields()
{
    jQuery('#border-color > input').spectrum({
        showInput: false,
        chooseText: "Избери",
        cancelText: "Отказ"
    });

    jQuery('#layer-color > input').spectrum({
        showInput: false,
        chooseText: "Избери",
        cancelText: "Отказ"
    });

	jQuery('#transparency > input').numberspinner({
		min: 0,
		max: 100,
		editable: true
	});

	var combotree = jQuery('#all-layers-tree').combotree('tree');
    var layer_type = combotree.tree('getSelected').attributes.layer_type;
    var layer_id = combotree.tree('getSelected').id;

	jQuery('#labelName').combobox({
        url: 'index.php?common-rpc=label-names-combobox',
        rpcParams: [{
        	selected: true,
        	layer_type: layer_type,
        	layer_id: layer_id
        }],
        valueField: 'key',
        textField: 'name',
        multiple: true,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#label-size > input').numberspinner({
        min: 1,
        max: 25,
        precision:1,
        editable: true
    });
}

function setComboTreeText(node, allLayersTree, parent) {
	var combotree = jQuery('#all-layers-tree').combotree('tree');
	var selected = combotree.tree('getSelected');
	var value;

	if(!node['attributes'].name) {
		if(selected) {
			value = selected['attributes'].name;

			if (!selected['attributes'].is_system) {
            	value = selected['attributes'].farming + ' / ' + selected['attributes'].year + ' / ' + selected['attributes'].name;
			}
		} else {
			value = 'Не е избран';

		}

		allLayersTree.combotree('setText', value);
		return;
	}

	if(node && selected) {
		value = selected['attributes'].name;

		if (!selected['attributes'].is_system) {
        	value = selected['attributes'].farming + ' / ' + selected['attributes'].year + ' / ' + selected['attributes'].name;
		}
	}

	if(!value) {
		value = 'Не е избран';
	}

	allLayersTree.combotree('setText', value);

	var span = jQuery('<span />').html(value);
	var combotreeWidth = span.textWidth() + 80;

	allLayersTree.combotree('resize', combotreeWidth);
}

function deepClone(obj, hash = new WeakMap()) {
	// Do not try to clone primitives or functions
	if (Object(obj) !== obj || obj instanceof Function) return obj;
	if (hash.has(obj)) return hash.get(obj); // Cyclic reference
	try { // Try to run constructor (without arguments, as we don't know them)
		var result = new obj.constructor();
	} catch(e) { // Constructor failed, create object without running the constructor
		result = Object.create(Object.getPrototypeOf(obj));
	}
	// Optional: support for some standard constructors (extend as desired)
	if (obj instanceof Map)
		Array.from(obj, ([key, val]) => result.set(deepClone(key, hash),
			deepClone(val, hash)) );
	else if (obj instanceof Set)
		Array.from(obj, (key) => result.add(deepClone(key, hash)) );
	// Register in hash
	hash.set(obj, result);
	// Clone and assign enumerable own properties recursively
	return Object.assign(result, ...Object.keys(obj).map (
		key => ({ [key]: deepClone(obj[key], hash) }) ));
}

const replacerFunc = () => {
	return (key, value) => {
		if (value instanceof Node) return 'Node';
		if (value instanceof Window) return 'Window';
		return value;
	};
};

function saveCheckSelectLayersLS() {
	var combotree = jQuery('#all-layers-tree').combotree('tree');
	var selected = combotree.tree('getSelected');
	var checked = combotree.tree('getChecked');

	const cloneSelected = JSON.parse(JSON.stringify(selected, replacerFunc()));
	const cloneChecked = JSON.parse(JSON.stringify(checked, replacerFunc()));
	//save selected layer in LocalStorage
	store.set('selectedLayerStorage', cloneSelected);

	//save checked layers in LocalStorage
	store.set('checkedLayerStorage', cloneChecked);
}

function clearCheckedPlotsInAttributeTables() {
	allRowsChecked = false;
	if (jQuery('#plots-tables').data().hasOwnProperty('datagrid')) {
    	jQuery('#plots-tables').datagrid('clearSelections');
    	jQuery('#plots-tables').datagrid('clearChecked');
    }
}

function getMapExtent(extents) {
	let extent = '';

	for (const ext of extents) {
		let bounds = new OpenLayers.Bounds.fromString(ext);
		if(extent === '') {
			extent = bounds;
		} else {
			extent.extend(bounds);
		}
	}

	return extent.toString();
}

function getCheckedElements(data) {
	let combotree = jQuery('#all-layers-tree').combotree('tree');
	let checked = combotree.tree('getChecked');
	let parent_id = data.parent_id ?? data.id;

	return checked.filter(el => el.parent_id === parent_id);
}