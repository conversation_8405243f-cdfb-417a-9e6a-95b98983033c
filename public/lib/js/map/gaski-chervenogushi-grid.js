function initVPSGaskiChervenogushiGrid(layer_id)
{
	var checkedElement;
    var array = [];

	for (var i=0; i<vectors.features.length; i++) {
		array.push(vectors.features[i].attributes.gid);
	};

	jQuery('#plots-tables').datagrid({
		url: 'index.php?map-rpc=layer-vps-gaski-chervenogushi-datagrid',
		rpcParams: [{
			layer_id: layer_id,
			layer_type: 14,
			gids: array
		}],
		singleSelect: true,
        checkOnSelect: false,
        selectOnCheck: false,
		iconCls: 'icon-plot',
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		pageSize: 20,
		fit: true,
		showFooter: true,
        sortName: 'gid',
        sortOrder: 'asc',
		idField: 'gid',
		pagination: true,
		rownumbers: true,
		width: '100%',
		border: false,
		frozenColumns: [[
				{
					field: 'ck',
					checkbox: true
				}
			]],
		columns: [[
				{
					field: 'name',//използва се колона NAME, като се маха ЕКАТТЕ-то и запетаята
					title: '<b>Име на землище</b>',
					sortable: true,
					width: 200
				},
				{
					field: 'ekatte',
					title: '<b>ЕКАТТЕ</b>',
					sortable: true,
					width: 150
				},
				{
					field: 'vps_type',//"Земи с ВПС в местообитания на червеногушата гъска"
					title: '<b>Вид ВПС</b>',
					sortable: false,
					width: 325
				},
			]],
		onLoadSuccess: function() {
			jQuery('#plots-tables').datagrid('uncheckAll');
            jQuery('#select-filtered-plots').trigger('change');
		},
		onUnselect: function(rowIndex, rowData) {

		},
		onSelect: function(rowIndex, rowData) {
			checkedElement = rowData;
			zoomToFeatureSelection(rowData.st_astext);
		},
		toolbar: '#attr-tables-toolbar',
		onBeforeLoad: function() {
			jQuery('#plots-tables').datagrid('clearChecked');
		},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}
