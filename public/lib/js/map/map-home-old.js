var map;
var info;
var isIntersectActive = false;
var layerTMP;
var layer_selection = 0;
var controlSelect;
var controlDraw;
var controlModify;
var selectVectorLayer;
var addPolygonLayer;
var editKVS = false;
var KVSLayerID = false;

// style the sketch fancy
//var sketchSymbolizers = {
//	"Point": {
//		pointRadius: 4,
//		graphicName: "square",
//		fillColor: "white",
//		fillOpacity: 1,
//		strokeWidth: 1,
//		strokeOpacity: 1,
//		strokeColor: "#333333"
//	},
//	"Line": {
//		strokeWidth: 1,
//		strokeOpacity: 1,
//		strokeColor: "#ff0000"
//	}
//};
//var style = new OpenLayers.Style();
//style.addRules([
//	new OpenLayers.Rule({
//		symbolizer: sketchSymbolizers
//	})
//]);
//var styleMap = new OpenLayers.StyleMap({
//	"default": style
//});

//var mapControls = {
//	line: new OpenLayers.Control.DynamicMeasure(OpenLayers.Handler.Path, {
//		persist: false,
//		geodesic: true
//	}),
//	polygon: new OpenLayers.Control.DynamicMeasure(OpenLayers.Handler.Polygon, {
//		persist: false,
//		geodesic: true
//	}),
//	splitline: new OpenLayers.Control.Measure(
//			OpenLayers.Handler.Path, {
//				persist: false,
//				handlerOptions: {
//					layerOptions: {
//						renderers: renderer,
//						styleMap: styleMap
//					}
//				}
//			}),
//	zoomin: new OpenLayers.Control.ZoomBox(
//			{
//				title: "Zoom in box",
//				out: false
//			}
//	),
//	zoomout: new OpenLayers.Control.ZoomBox(
//			{
//				title: "Zoom out box",
//				out: true
//			}
//	)
//};

var renderer = OpenLayers.Util.getParameters(window.location.href).renderer;
if (renderer) {
	OpenLayers.Layer.Vector.prototype.renderers = [renderer];
}

OpenLayers.ProxyHost = "/cgi-bin/proxy.cgi?url=";

var wfsLayer;
var bridgeLayer;
var vectors;

jQuery(window).load(function()
{
	onBodyLoad();

	//init vectors
	var renderer = OpenLayers.Util.getParameters(window.location.href).renderer;
	renderer = (renderer) ? [renderer] : OpenLayers.Layer.Vector.prototype.renderers;

	vectors = new OpenLayers.Layer.Vector("Vector Layer", {
		displayInLayerSwitcher: false,
		styleMap: new OpenLayers.StyleMap({
			strokeColor: "#ff0000",
			strokeWidth: 5,
			fillOpacity: 0
		}),
		renderers: renderer
	});

	map.addLayers([vectors]);

	initjQueryControls();
	initAllLayersTree();

	jQuery('#tool-delete').linkbutton('disable');
	jQuery('#tool-edit-geometry').linkbutton('disable');
	jQuery('#tool-save').linkbutton('disable');
	//jQuery('#tool-split').linkbutton('disable');

});

//function addLayers()
//{
//	boundsArray = [];
//	for (var i = 0; i < layers.length; i++)
//	{
//		boundsArray[i] = new OpenLayers.Bounds.fromString(layers[i].extent).transform(
//				new OpenLayers.Projection("EPSG:32635"),
//				map.getProjectionObject());
//	}
//
//	layerArray = [];
//	for (var i = 0; i < layers.length; i++)
//	{
//		if (layers[i].extent && layers[i].name)
//			layerArray[i] = new OpenLayers.Layer.WMS(
//					layers[i].name,
//					wmsServer + "?map=" + mapPath + groupID + '.map',
//					{
//						layers: layers[i].name,
//						format: 'image/png',
//						transparent: "true"
//								//map: mapPath+userid+'.map'
//					},
//			{
//				displayInLayerSwitcher: false,
//				animationEnabled: false
//			}
//			);
//		layerArray[i].setVisibility(false);
//	}
//
//	if (layers.length)
//		map.addLayers(layerArray);
//
//	if (layers.length)
//	{
//		for (i = 0; i < layers.length; i++)
//		{
//			if (layers[i].layer_type == 5)
//			{
//				find = i;
//				break;
//			}
//		}
//		find = (find);
//
//		map.zoomToExtent(boundsArray[find]);
//	}
//	else
//	{
//		map.zoomToExtent(new OpenLayers.Bounds.fromString("125190.6162, 4573142.7188, 631370.3273, 4887149.5823").transform(
//				new OpenLayers.Projection("EPSG:32635"),
//				map.getProjectionObject()));
//	}
//
//	map.events.register("mousemove", map, function(e) {
//		var position = this.events.getMousePosition(e);
//
//		var pixel = new OpenLayers.Pixel(e.xy.x, e.xy.y);
//		var lonlat = map.getLonLatFromPixel(pixel);
//
//		var position = map.getLonLatFromViewPortPx(e.xy);
//
//		OpenLayers.Util.getElement("coords").innerHTML = 'x: ' + position.lat + ', y: ' + position.lon;
//
//	});
//}

function onBodyLoad()
{
	var options = {
		controls: [new OpenLayers.Control.Navigation(), new OpenLayers.Control.ScaleLine({
				bottomInUnits: 'km'
			})],
		projection: new OpenLayers.Projection("EPSG:900913")
	};

	map = new OpenLayers.Map('map', options);
	var apiKey = "AiWEso3-IjWyX1aZMuep9Sjl62D6FUMqv8qQGpV-kgbW0qWk61to4nrqHh-2D5HL";
	var bhyb = new OpenLayers.Layer.Bing({
		name: "Bing",
		key: apiKey,
		type: "AerialWithLabels"
	});
	/*	
	 map = new OpenLayers.Map('map', {
	 projection: 'EPSG:900913',
	 layers: [
	 new OpenLayers.Layer.Google(
	 "Google Hybrid",
	 {type: google.maps.MapTypeId.HYBRID, numZoomLevels: 20}
	 )
	 ],
	 zoom: 5
	 });
	 */
	map.addLayer(bhyb);

	//addLayers();

	addSelectItem();
	addLayersItem();

	var control;
	for (var key in mapControls) {
		control = mapControls[key];
		map.addControl(control);
	}

	map.zoomToExtent(new OpenLayers.Bounds.fromString("125190.6162, 4573142.7188, 631370.3273, 4887149.5823").transform(
			new OpenLayers.Projection("EPSG:32635"),
			map.getProjectionObject()));
}

function addLayersItem()
{
	addPolygonLayer = new OpenLayers.Layer.Vector("AddPolygon", {
		displayInLayerSwitcher: false,
		style: {
			cursor: "pointer",
			fontSize: '8px',
			fontColor: '#222',
			pointRadius: 10,
			fillColor: '#cccccc',
			strokeColor: '#000000',
			fillOpacity: 0.25
		}
	});
	map.addLayer(addPolygonLayer);

	var options = {
		handlerOptions: {
			style: {
				cursor: "pointer",
				pointRadius: 5,
				graphicName: 'square',
				fillColor: '#cccccc',
				fillOpacity: 0.3,
				strokeWidth: 1,
				strokeColor: '#000000',
				strokeOpacity: 1
			}
		}
	};

	controlDraw = new OpenLayers.Control.DrawFeature(addPolygonLayer, OpenLayers.Handler.Polygon, options);
	map.addControl(controlDraw);
}

function chooseControl(name)
{
	jQuery('#tool-save').linkbutton('disable');
	jQuery('#tool-edit-geometry').linkbutton('disable');
	jQuery('#tool-delete').linkbutton('disable');
	jQuery('#tool-split').linkbutton('disable');

	for (key in mapControls) {
		var control = mapControls[key];
		if (name == key) {
			control.activate();
		} else {
			control.deactivate();
		}
	}
	map.events.unregister('click', map, propertyWindowFunction);

	if (name == 'drawpolygon')
	{
		controlDraw.activate();

		controlDraw.events.register("featureadded", this, function(e) {
			jQuery('#tool-save').linkbutton('enable');
			jQuery('#tool-edit-geometry').linkbutton('disable');
			jQuery('#tool-delete').linkbutton('disable');
		});
	}
	else
	{
		addPolygonLayer.removeAllFeatures();
		controlDraw.deactivate();
//		controlSnapping.deactivate();
	}


	if (name == 'selectitem')
	{
		selectVectorLayer.removeAllFeatures();
		map.addLayer(selectVectorLayer);
		controlSelect.activate();

		selectVectorLayer.events.register("featureadded", this, function(e) {
			jQuery('#tool-save').linkbutton('enable');
			jQuery('#tool-edit-geometry').linkbutton('enable');
			jQuery('#tool-delete').linkbutton('enable');
			jQuery('#tool-split').linkbutton('enable');
		});
	}
	else
	{
		if (name != 'splitline')
		{
			if (selectVectorLayer.drawn)
				map.removeLayer(selectVectorLayer);
			controlSelect.deactivate();
		}
	}
}

function deactivateSelectLayer() {
	if (selectVectorLayer.drawn)
		map.removeLayer(selectVectorLayer);
	controlSelect.deactivate();
}

function saveItemCompleteEvent()
{
	selectVectorLayer.removeAllFeatures();
	addPolygonLayer.removeAllFeatures();

	jQuery('#tool-save').linkbutton('disable');
	jQuery('#tool-edit-geometry').linkbutton('disable');
	jQuery('#tool-delete').linkbutton('disable');

	enableAllbuttons();
	if (controlModify)
	{
		controlModify.deactivate();
		map.removeControl(controlModify);
	}

	var optionsselect = jQuery('#tool-draw').linkbutton('options');
	if (!optionsselect.selected)
		controlSelect.activate();

	jQuery('#tool-edit-geometry').linkbutton('unselect');

	reloadAllLayers();

	//endLoading();
}

function deleteItemCompleteEvent()
{
	selectVectorLayer.removeAllFeatures();

	jQuery('#tool-save').linkbutton('disable');
	jQuery('#tool-edit-geometry').linkbutton('disable');
	jQuery('#tool-delete').linkbutton('disable');

	enableAllbuttons();
	if (controlModify)
	{
		controlModify.deactivate();
		map.removeControl(controlModify);
	}
	controlSelect.activate();

	jQuery('#tool-edit-geometry').linkbutton('unselect');

	reloadAllLayers();

	//endLoading();
}

function activeGeometryEdit()
{
	controlModify = new OpenLayers.Control.ModifyFeature(selectVectorLayer, {
		deferDelete: true,
		tools: [// custom tools
			{
				// to rotate the "angle" attribute of a ponit by steps of 15 degrees
				geometryTypes: ['OpenLayers.Geometry.Point',
					'OpenLayers.Geometry.MultiPoint'],
				dragAction: function(feature, initialAtt, escale, rotation) {
					var angle = ((initialAtt.angle || 0) - rotation) % 360;
					// force steps of 15 degrres
					angle = Math.floor(angle / 15) * 15;
					feature.attributes.angle = angle;
				},
				style: OpenLayers.Control.ModifyFeature_styles.rotate
			}, {
				// to resize the pointRadius.
				geometryTypes: ['OpenLayers.Geometry.Point',
					'OpenLayers.Geometry.MultiPoint'],
				dragAction: function(feature, initialAtt, escale, rotation) {
					var radius = (initialAtt.radius || 6) * escale;
					feature.attributes.radius = Math.max(6, radius);
				},
				style: OpenLayers.Control.ModifyFeature_styles.resize
			}, {
				// to close a lineString as a ring
				geometryTypes: ['OpenLayers.Geometry.LineString'],
				pressingAction: function(feature) {
					var geometry = feature.geometry;
					geometry.addComponent(geometry.components[0].clone());
				},
				style: {
					label: 'ring',
					title: 'press to close as a ring',
					cursor: "pointer",
					fontSize: '8px',
					fontColor: '#222',
					pointRadius: 10,
					fillColor: '#cccccc',
					strokeColor: '#444444'
				}
			}]
	});

	controlModify.createVertices = true;
	controlModify.mode = 110;
	map.addControl(controlModify);
	controlModify.activate();
	controlSelect.deactivate();
}

var saveStrategy;
var wfs;

function addSelectItem()
{
	selectVectorLayer = new OpenLayers.Layer.Vector("Selection", {
		styleMap: new OpenLayers.StyleMap({
			'default': OpenLayers.Util.applyDefaults({
				strokeWidth: 3,
				graphicName: 'triangle',
				pointRadius: '${radius}',
				rotation: '${angle}'
			}, OpenLayers.Feature.Vector.style['default']
					),
			'select': OpenLayers.Util.applyDefaults({
				pointRadius: '${radius}'
			}, OpenLayers.Feature.Vector.style['select']
					)
		}),
		displayInLayerSwitcher: false
	});

	// to ensure that the points have radius
	selectVectorLayer.events.on({
		'beforefeatureadded': function(e) {
			e.feature.attributes.radius = 6;
		}
	});

	var find;
	for (i = 0; i < layers.length; i++)
	{
		if (layers[i].layer_type == 2)
		{
			find = i;
			break;
		}
	}
	find = (find);
	//console.info(find);
	controlSelect = new OpenLayers.Control.GetFeature({
		protocol: OpenLayers.Protocol.WFS.fromWMSLayer(layerArray[find], {
			geometryName: "geom"
		}),
		box: true,
		single: false,
		click: true,
		clickout: true,
		maxFeatures: 1500,
		clickTolerance: 5
	})
	//console.info(controlSelect);
	controlSelect.events.register("featureselected", this, function(e) {

		if (layerArray[find].getVisibility()) {
			//alert('aaa');
			selectVectorLayer.addFeatures([e.feature]);
		}
	});
	controlSelect.events.register("featureunselected", this, function(e) {
		selectVectorLayer.removeFeatures([e.feature]);
	});

	map.addControl(controlSelect);
}

function unselectAll()
{
	jQuery('#tool-split').linkbutton('unselect');
	jQuery('#tool-zoomin').linkbutton('unselect');
	jQuery('#tool-zoomout').linkbutton('unselect');
	jQuery('#tool-measure-line').linkbutton('unselect');
	jQuery('#tool-measure-polygon').linkbutton('unselect');
	jQuery('#tool-panzoom').linkbutton('unselect');
	jQuery('#tool-draw').linkbutton('unselect');
	jQuery('#tool-select').linkbutton('unselect');
}

function disableAllbuttons()
{
	jQuery('#tool-split').linkbutton('disable');
	jQuery('#tool-zoomin').linkbutton('disable');
	jQuery('#tool-zoomout').linkbutton('disable');
	jQuery('#tool-measure-line').linkbutton('disable');
	jQuery('#tool-measure-polygon').linkbutton('disable');
	jQuery('#tool-panzoom').linkbutton('disable');
	jQuery('#tool-draw').linkbutton('disable');
	jQuery('#tool-select').linkbutton('disable');
	jQuery('#tool-extent').linkbutton('disable');
	jQuery('#tool-attr-into').linkbutton('disable');
	jQuery('#tool-fullscreen').linkbutton('disable');
	jQuery('#tool-extent-kvs').linkbutton('disable');
	jQuery('#tool-attr-kvs').linkbutton('disable');
}

function enableAllbuttons()
{
	//jQuery('#tool-info').linkbutton('enable');
	jQuery('#tool-zoomin').linkbutton('enable');
	jQuery('#tool-zoomout').linkbutton('enable');
	jQuery('#tool-measure-line').linkbutton('enable');
	jQuery('#tool-measure-polygon').linkbutton('enable');
	jQuery('#tool-panzoom').linkbutton('enable');
	jQuery('#tool-draw').linkbutton('enable');
	jQuery('#tool-select').linkbutton('enable');
	jQuery('#tool-extent').linkbutton('enable');
	jQuery('#tool-attr-into').linkbutton('enable');
	jQuery('#tool-fullscreen').linkbutton('enable');
	jQuery('#tool-extent-kvs').linkbutton('enable');
	jQuery('#tool-attr-kvs').linkbutton('enable');
}

//function chooseButtonClicked() {
//	if (layer_selection != 0) {
//		initPlotsGrid(layer_selection);
//		jQuery('#map-layout').layout('expand', 'south');
//	} else {
//		jQuery.messager.alert('Грешка', 'Моля изберете данни.');
//		return false;
//	}
//}

//function reloadAllLayers()
//{
//	for (var i = 0; i < layerArray.length; i++)
//	{
//		layerArray[i].redraw(true);
//	}
//}

function unselectButtons() {
	jQuery('#btneditplotdata').linkbutton('unselect');
	editKVS = false;
	KVSLayerID = false;
}



