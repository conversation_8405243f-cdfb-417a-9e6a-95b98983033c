function initGPSGrid(layer_id)
{
	var checkedElement;
	var gids_arr = [];

	for (var i = 0; i < vectors.features.length; i++) {
		gids_arr.push(vectors.features[i].attributes.gid);
	};

	jQuery('#plots-tables').datagrid({
		url: 'index.php?map-rpc=gps-datagrid',
		rpcParams: [{
			layer_id: layer_id,
			layer_type: 2,
			gids: gids_arr
		}],
		singleSelect: true,
        checkOnSelect: false,
        selectOnCheck: false,
		iconCls: 'icon-plot',
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		pageSize: 20,
		fit: true,
		showFooter: true,
		sortName: 'gid',
		sortOrder: 'asc',
		idField: 'gid',
		pagination: true,
		rownumbers: true,
		width: '100%',
		border: false,
		frozenColumns: [[
				{
					field: 'ck',
					checkbox: true
				}
			]],
		columns: [[
				{
					field: 'plot_name',
					title: '<b>Име на парцел</b>',
					sortable: true,
					width: 200
				},{
					field: 'area',
					title: '<b>Площ (дка)</b>',
					sortable: true,
					width: 150
				},{
					field: 'plot_info',
					title: '<b>Коментар</b>',
					sortable: true,
					width: 345
				}
			]],
		onUnselect: function(rowIndex, rowData) {

		},
		onSelect: function(rowIndex, rowData) {
			checkedElement = rowData;
			displayFeatureSelection(rowData.st_astext, rowData);
		},
		toolbar: '#attr-tables-toolbar',
		onBeforeLoad: function() {
		},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}
