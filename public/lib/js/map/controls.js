Namespace('TF.Rpc.Map');
var allowable_ekate_cb;
var date = new Date();
var todayDate = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();
var ComboboxData = ComboboxData || undefined;
var lfa_cb;
var pzp_cb;

let nonPrintableMapLayers = [
	LAYER_TYPE_VPS_PASISHTA,
	LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI,
	LAYER_TYPE_VPS_GASKI_ZIMNI,
	LAYER_TYPE_VPS_LIVADEN_BLATAR,
	LAYER_TYPE_VPS_ORLI_LESHOYADI,
	LAYER_TYPE_PERMANETELY_GREEN_AREAS,
	LAYER_TYPE_NATURA_2000,
	LAYER_TYPE_LFA,
	'layer_allowable',	
];
function initControls() {

    if (ComboboxData == undefined) {
        TF.Rpc.Common.CombinedComboboxData.read(true)
            .done(function (data) {
                ComboboxData = data;
                initVectorLayer();
                initVectorLayer2();
            })
            .fail(function (error) {
                jQuery.messager.alert('Грешка', error.getMessage(), 'warning');
            });
    }

    var ekateComboboxData = ComboboxData.EkateCombobox,
        newEkateComboboxData = [];

    for (var i = 0; i < ekateComboboxData.length; i++) {
        if (i == 0) {
            newEkateComboboxData.push(ekateComboboxData[i]);
        }
        if (i == 1) {
            newEkateComboboxData.push({ekate: 'null',text: 'Без ЕКАТТЕ'});
            newEkateComboboxData.push(ekateComboboxData[i]);
        }
        if (i > 1) {
            newEkateComboboxData.push(ekateComboboxData[i]);
        }
    }

    jQuery('#property-win').window({
        width: 350,
        height: 335,
        title: 'Информация',
        iconCls: "icon-info",
        modal: true,
        closed: true,
        resizable: false,
        collapsible: false,
        minimizable: false,
        maximizable: false,
    });
    //set linkbuttons icons
    jQuery('#copy-button > a').linkbutton({iconCls: 'icon-save'});
    jQuery('#layer-change-btn > a').linkbutton({iconCls: 'icon-save'});
    jQuery('#choose-selection-btn > a').linkbutton({iconCls: 'icon-ok'});
    jQuery('#filter-btn > a').linkbutton({iconCls: 'icon-filter'});
    jQuery('#add-plot-owner-button > a').linkbutton({iconCls: 'icon-ok'});
    jQuery('#btn-set-export-type > a').linkbutton({iconCls: 'icon-save'});

    //open attribute info window and load it's data
    jQuery('#tool-attr-info').bind('click', showAttrInfo);

    jQuery('#tool-preview-map-export').bind('click', previewMapExport);

    //init filter fields
    jQuery('#zp-filter-ekate').combobox({
        data: newEkateComboboxData,
        valueField: 'ekate',
        textField: 'text',
        filter: function(q, row) {
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if (text.indexOf(find) != -1 || value.indexOf(find) != -1)
            {
                return true;
            }
        },
        onLoadSuccess: function(data)
        {
            jQuery('#isak-filter-ekate').combobox({
                data: data,
                valueField: 'ekate',
                textField: 'text',
                filter: function(q, row) {
                    var opts = jQuery(this).combobox('options');
                    var text = row[opts.textField].toLowerCase();
                    var value = row[opts.valueField];
                    var find = q.toLowerCase();
                    if (text.indexOf(find) != -1 || value.indexOf(find) != -1)
                    {
                        return true;
                    }
                },
            });

            jQuery('#kvs-filter-ekate').combobox({
                data: data,
                valueField: 'ekate',
                textField: 'text',
                filter: function(q, row) {
                    var opts = jQuery(this).combobox('options');
                    var text = row[opts.textField].toLowerCase();
                    var value = row[opts.valueField];
                    var find = q.toLowerCase();
                    if (text.indexOf(find) != -1 || value.indexOf(find) != -1)
                    {
                        return true;
                    }
                }
            });
            jQuery('#for-isak-for-editing').combobox({
                data: [
                    {text: '-', value: ''},
                    {text: 'Да', value: true},
                    {text: 'Не', value: false}
                ],
                valueField: 'value',
                textField: 'text',
            });

            var cloneData = data.slice(0);
            cloneData[0] = {ekate: '', text: ''};

            jQuery('#select-ekatte-for-isak').combobox({
                data: cloneData,
                valueField: 'ekate',
                textField: 'text',
                required: true,
                editable: true,
                filter: function(q, row) {
                    var opts = jQuery(this).combobox('options');
                    var text = row[opts.textField].toLowerCase();
                    var value = row[opts.valueField];
                    var find = q.toLowerCase();
                    if (text.indexOf(find) != -1 || value.indexOf(find) != -1)
                    {
                        return true;
                    }
                },
                loadFilter: function (d) {
                    return jQuery(d).filter(function (i, el) {
                        return !(el.ekate == "" || el.ekate == "null");
                    })
                }
            });
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    jQuery('#zp-filter-culture').combobox({
        data: ComboboxData.CultureCombobox,
        valueField: 'id',
        textField: 'name',
        onLoadSuccess: function()
        {
            jQuery('#isak-filter-culture').combobox({
                data: jQuery('#zp-filter-culture').combobox('getData'),
                multiple: true,
                onHidePanel: onHidePanelMultiSelect,
                onSelect: onComboMultiSelect,
                valueField: 'id',
                textField: 'name'
            });
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#vps-chervenogushi-filter-ekate').combobox({
        data: ComboboxData.VPSChervenogushiGaskiEkateCombobox,
        valueField: 'ekate',
        textField: 'text',
        filter: function(q, row) {
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if (text.indexOf(find) != -1 || value.indexOf(find) != -1)
            {
                return true;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#vps-zimni-filter-ekate').combobox({
        data: ComboboxData.VPSZimniGaskiEkateCombobox,
        valueField: 'ekate',
        textField: 'text',
        filter: function(q, row) {
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if (text.indexOf(find) != -1 || value.indexOf(find) != -1)
            {
                return true;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#vps-orli-leshoyadi-filter-zemlishte').combobox({
        data: ComboboxData.VPSOrliLeshoyadiEkateCombobox,
        valueField: 'ekate',
        textField: 'text',
        filter: function(q, row) {
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if (text.indexOf(find) != -1 || value.indexOf(find) != -1)
            {
                return true;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#vps-livaden-blatar-filter-ime').combobox({
        data: ComboboxData.VPSLivadenBlatarNameCombobox,
        valueField: 'ime',
        textField: 'text',
        filter: function(q, row) {
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if (text.indexOf(find) != -1 || value.indexOf(find) != -1)
            {
                return true;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#kvs-filter-ekate').combobox({
        data: newEkateComboboxData,
        valueField: 'ekate',
        textField: 'text',
        filter: function(q, row) {
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if (text.indexOf(find) != -1 || value.indexOf(find) != -1)
            {
                return true;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#kms-filter-ekate').combobox({
        //url: 'index.php?common-rpc=kms-ekate-combobox',
        //rpcParams: [{record_all: true}],
        valueField: 'ekate',
        textField: 'text',
        filter: function(q, row) {
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if (text.indexOf(find) != -1 || value.indexOf(find) != -1)
            {
                return true;
            }
        }
    });

    jQuery('#kms-filter-culture').combobox({
        data: ComboboxData.CultureCombobox,
        valueField: 'id',
        textField: 'name',
        onLoadSuccess: function()
        {
            jQuery('#isak-filter-culture').combobox({
                data: jQuery('#kms-filter-culture').combobox('getData'),
                multiple: true,
                onHidePanel: onHidePanelMultiSelect,
                onSelect: onComboMultiSelect,
                valueField: 'id',
                textField: 'name'
            });
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#natura-2000-filter-name').combobox({
        data: ComboboxData.Natura2000NameCombobox,
        valueField: 'gid',
        textField: 'text',
        filter: function(q, row) {
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if (text.indexOf(find) != -1 )
            {
                return true;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#lfa-filter-area-type').combobox({
        valueField: 'type',
        textField: 'text',
        data:[{
            text: 'НР-1',
            type: 1
        },{
            text: 'НР-2',
            type: 2
        }],
        filter: function(q, row) {
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if (text.indexOf(find) != -1 || value.indexOf(find) != -1)
            {
                return true;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    //init layer edit button
    jQuery('#tool-edit-layer').bind('click', function()
    {
        jQuery.messager.alert('Внимание', 'Персонализирането на слой е достъпно в модул Аднинистративна карта. Може да влезете в Агрими на <a href="https://app.agrimi.com">https://app.agrimi.com</a>. ', 'warning');
        return false;
    });

    //select layer object button
    jQuery('#tool-choose-layer-object').bind('click', function()
    {
        var combotree = jQuery('#all-layers-tree').combotree('tree');
        var selectedLayer = combotree.tree('getSelected');

        if (!selectedLayer)
        {
            jQuery.messager.alert('Грешка', 'Не е избран активен слой!');
            return false;
        }

        var options = jQuery('#tool-choose-layer-object').linkbutton('options');
        if (!options.selected)
        {
            jQuery('#tool-choose-layer-object').linkbutton('select');
            if (!options.disabled)
            {
                unselectAll();
                chooseControl('none');
                map.events.register('click', map, propertyWindowFunction);
            }
        }
        else
        {
            jQuery('#tool-choose-layer-object').linkbutton('unselect');
            map.events.unregister('click', map, propertyWindowFunction);
        }
    });

    //save information from propertygrid
    jQuery('#btn-save-propertygrid').bind('click', function()
    {
        var propData = jQuery('#property-grid').propertygrid('getData');
        var rows = propData['rows'].filter(function (row) {
            return !row.is_virtual;
        });

        let values = {};
        rows.forEach(function (element, index) { 
            values[element.prop_name] = element;
        })

        TF.Rpc.Map.MapTools.savePropertyGrid(values)
        .done(function (data) {
        })
        .fail(function (errorObj) {
            jQuery.messager.alert('Грешка',errorObj.getMessage());
            return;
        });

        jQuery('#property-win').window('close');
        jQuery('#plots-tables').datagrid('loadRpc');
    });

    //save information from propertygrid
    jQuery('#btn-clipping').bind('click', function()
    {
        var params = {
          'ekate': jQuery('#ekate').combobox('getValue'),
          'ntp': jQuery('#allowable_layer_ntp').combobox('getValue')
        };

        if(!params.ekate) {
            return jQuery.messager.alert('Грешка', 'Не е избрано землище!');
        }

        if(!params.ntp) {
            return jQuery.messager.alert('Грешка', 'Не е избран тип допустим слой!');
        }

        jQuery.messager.confirm('Потвърждение', "Тази операция може да отнеме няколко минути." , function(r) {
            if (r) {
                TF.Rpc.Map.MapTools.clippingKVSByNTPAndUpdateAreas(params)
                    .done(function (result) {
                        var message = 'Площите на ' + result.updatedPlots + ' имота бяха обновени';
                        if(result.updatedPlots === 0){
                            message = 'Не бяха открити имоти за пресичане';
                        } else if (result.updatedPlots === 1){
                            message = 'Площта на един имот беше обновена';
                        }

                        jQuery.messager.alert('Пресичане',message);
                        jQuery('#win-kvs-clipping').window('close');
                        jQuery('#plots-tables').datagrid('loadRpc');
                    })
                    .fail(function (result) {
                        jQuery.messager.alert('Грешка', "Нещо се обърка. Моля опитайте по-късно.");
                    });
            }
        });
    });

    jQuery('#btn-save-layer-personalization').bind('click', function() {
        var saveLayerObject = getLayerEditFieldsData();
        if (saveLayerObject.layer_type) {
            lastTransferedLayer = saveLayerObject.id;
            if(saveLayerObject.layer_year && saveLayerObject.layer_farming == ""){
                return jQuery.messager.alert('Грешка', 'Не е избранo стопанство!');
            }
            if(saveLayerObject.layer_farming != "" && !saveLayerObject.layer_year){
                return jQuery.messager.alert('Грешка', 'Не е избрана година!');
            }
        }
        if (saveLayerObject.ekatte.length > 1) {
            var confirmation_message = 'Направените промени ще се отразят на повече от едно землище. Искате ли да продължите?';
            jQuery.messager.confirm('Потвърждение', confirmation_message , function(r) {
                if (r) {
                    TF.Rpc.Map.MapLayerChange.save(saveLayerObject)
                    .done(function (data) {
                        jQuery('#layer-change-win').window('close');
                        reloadLayerAfterFeaturesChange(data);
                        jQuery('#all-layers-tree').combotree('loadRpc');
                    })
                    .fail(function (errorObj) {
                        if (errorObj.is(TF.Rpc.ExceptionsList.MAP_LAYER_EDIT_FAILED)) {
                            jQuery.messager.alert('Грешка',errorObj.getMessage());
                        }
                    });
                }
            });
        } else {
            TF.Rpc.Map.MapLayerChange.save(saveLayerObject)
            .done(function (data) {
                jQuery('#layer-change-win').window('close');
                reloadLayerAfterFeaturesChange(data);
                jQuery('#all-layers-tree').combotree('loadRpc');
            })
            .fail(function (errorObj) {
                if (errorObj.is(TF.Rpc.ExceptionsList.MAP_LAYER_EDIT_FAILED)) {
                    jQuery.messager.alert('Грешка',errorObj.getMessage());
                }
            });
        }
        jQuery('#layer-change-win').window('close');
    });

    //handle KVS filter radio buttons
    jQuery('#kvs-filter-with-contracts').change(function()
    {
        if (jQuery('#kvs-filter-with-contracts').is(':checked'))
        {
            jQuery('#kvs-filter-contract-farming').combobox('enable');
            jQuery('#kvs-filter-contract-date').datebox('enable');
        }
    });

    jQuery('#kvs-filter-without-contracts').change(function()
    {
        if (jQuery('#kvs-filter-without-contracts').is(':checked'))
        {
            jQuery('#kvs-filter-contract-farming').combobox('enable');
            jQuery('#kvs-filter-contract-date').datebox('enable');
        }
    });

    jQuery('#kvs-filter-free-contracts').change(function()
    {
        if (jQuery('#kvs-filter-free-contracts').is(':checked'))
        {
            jQuery('#kvs-filter-contract-farming').combobox('enable');
            jQuery('#kvs-filter-contract-date').datebox('enable');
        }
    });

    jQuery('#kvs-filter-all-plots').change(function()
    {
        if (jQuery('#kvs-filter-all-plots').is(':checked'))
        {
            jQuery('#kvs-filter-contract-farming').combobox('disable');
            jQuery('#kvs-filter-contract-date').datebox('disable');
        }
    });
    //end of: handle KVS filter radio buttons

    //init map types combobox
    jQuery('#map-types-combobox > input').combobox({
        data: ComboboxData.MapTypesCombobox,
        valueField: 'id',
        textField: 'name',
        onSelect: function(record)
        {
            initMapPad(record.id);
        },
        onLoadSuccess: function() {
            jQuery('#map-types-combobox > input').combobox('select', store.get('map_pad') || 1);
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
    });
}

function chooseControl(control_name)
{
    var mapControls = map.options.controls;
    for (var i = 0; i < mapControls.length; i++)
    {
        var control = mapControls[i];
        if (mapControls[i].name === control_name)
        {
            control.activate();
        } else
        {
            control.deactivate();
        }
    }
}

layerInAttrTable = null;

function showAttrInfo() {
    var combotree = jQuery('#all-layers-tree').combotree('tree');
    var selected = combotree.tree('getSelected'),
        filterPanel,
        filterPanelTitle,
        fieldSets;

    if (!selected)
    {
        jQuery.messager.alert('Грешка', 'Не е избран активен слой!');
        jQuery('#tool-auto-split').linkbutton('enable');
        return false;
    }

    if(selected.id === layerInAttrTable) {
        jQuery('#win-layer-attr-info').window('open');
        return;
    }

    filterPanel = jQuery('#main-kvs-table-layout').data('layout').panels['expandWest'];  // the west expand panel

    jQuery('#att-tables-multi-edit-btn').hide();
    jQuery('#work-layer-filter-fields').hide();
    jQuery('#clip-kvs-btn-wrapper').hide();
    jQuery('#clip-with-kvs-btn-wrapper').hide();

    if([LAYER_ISAK_PLOTS, LAYER_KOMAS_PLOTS, LAYER_ZP_PLOTS].includes(selected['attributes'].layer_type)) {
        jQuery('#clip-with-kvs-btn-wrapper').show();
    }

    if (selected['attributes'].layer_type == LAYER_KVS_PLOTS) {
        jQuery('#attr-tables-kvs-filter-btn').show();
        jQuery('#attr-info-tables-filter-buttons').hide();
        jQuery('#select-filtered-plots-group').show();
        jQuery('#clip-kvs-btn-wrapper').show();
        filterPanelTitle = 'Приложени филтри';
        jQuery('#win-plots-filter').find('fieldset.mainPlotFilterFieldset')
			.eq(0).find('p').css('marginBottom', 5).end();
    } else {
        jQuery('#attr-tables-kvs-filter-btn').hide();
        jQuery('#attr-info-tables-filter-buttons').show();
        jQuery('#select-filtered-plots-group').hide();
        jQuery('#clip-kvs-btn-wrapper').hide();
        filterPanelTitle = 'Филтри';
    }

    filterPanel.html('<p style="transform: rotate(90deg);padding:6px 2px; font-weight: bold; width:200px; float: left; margin-left: -85px; margin-top:90px;text-rendering: optimizeLegibility;">' + filterPanelTitle + '</p>');

    jQuery("#main-kvs-table-layout").layout('collapse', 'west');

    jQuery("#attr-cols-edit-btn").hide();
    attrInfoForLayerID = selected.parent_id ?? selected.id;
    attrInfoForLayerName = selected['attributes'].parent_layer_name ?? selected['attributes'].layer_name;
    attrInfoForLayerType = selected['attributes'].layer_type;

    jQuery('#attr-tables-export-excel-cadastral-map-btn').hide();    
    jQuery('#win-layer-attr-info').window('open');

    layerInAttrTable = selected.id;
    
    switch (parseInt(selected['attributes'].layer_type))
    {
        case LAYER_ZP_PLOTS: //zp
            jQuery('#kvs-attr-info').hide();
            jQuery('#isak-attr-info').hide();
            jQuery('#attr-tables-export-btn').show();
            jQuery('#export-xls-btn').show();
            jQuery('#attr-tables-edit-btn').show();
            jQuery('#kms-attr-info').hide();
            jQuery('#gps-attr-info').hide();
            jQuery('#for-isak-attr-info').hide();
            jQuery('#for-isak-for-editing .combobox-f').hide();

            jQuery('#att-tables-multi-edit-btn').show();

            jQuery('#zp-attr-info').show();
            jQuery('#allowable-attr-info').hide();
            jQuery('#lfa-attr-info').hide();
            jQuery('#natura-2000-attr-info').hide();
            jQuery('#pzp-attr-info').hide();
            jQuery('#vps-chervenogushi-attr-info').hide();
            jQuery('#vps-zimni-attr-info').hide();
            jQuery('#vps-livaden-blatar-attr-info').hide();
            jQuery('#vps-orli-leshoyadi-attr-info').hide();
            jQuery('#copy-items').show();
            jQuery('.js-copy-layer-btn-zp').hide();
            jQuery('.js-copy-layer-btn-filtered-zp').hide();
            jQuery('.js-copy-layer-btn-for-isak').show();
            jQuery('.js-copy-layer-btn-filtered-for-isak').show();
            jQuery('.js-copy-layer-btn').show();
            jQuery('.js-copy-layer-filtered-btn').show();
            jQuery('#plots-tables-fieldset').height('572px');
            initZPGrid(selected.id);
            jQuery('#inactive-cultures-text').hide();
            jQuery('#attr-tables-multiedit-btn').hide();
            jQuery('#attr-tables-export-excell-btn').show();
            break;
        case LAYER_KVS_PLOTS: //kvs
            jQuery('#isak-attr-info').hide();
            jQuery('#zp-attr-info').hide();
            jQuery('#kms-attr-info').hide();
            jQuery('#for-isak-attr-info').hide();
            jQuery('#gps-attr-info').hide();
            jQuery('#for-isak-for-editing .combobox-f').hide();

            jQuery('#kvs-attr-info').show();
            jQuery('#attr-tables-export-btn').show();
            jQuery('#export-xls-btn').show();
            jQuery('#attr-tables-export-excel-cadastral-map-btn').show();
            jQuery('#attr-tables-edit-btn').show();
            jQuery('#copy-items').show();
            jQuery('.js-copy-layer-btn-zp').show();
            jQuery('.js-copy-layer-btn-filtered-zp').show();
            jQuery('.js-copy-layer-btn-for-isak').show();
            jQuery('.js-copy-layer-btn-filtered-for-isak').show();
            jQuery('.js-copy-layer-btn').show();
            jQuery('.js-copy-layer-filtered-btn').show();

            jQuery('#allowable-attr-info').hide();
            jQuery('#lfa-attr-info').hide();
            jQuery('#natura-2000-attr-info').hide();
            jQuery('#pzp-attr-info').hide();
            jQuery('#vps-chervenogushi-attr-info').hide();
            jQuery('#vps-zimni-attr-info').hide();
            jQuery('#vps-livaden-blatar-attr-info').hide();
            jQuery('#vps-orli-leshoyadi-attr-info').hide();
            jQuery('#plots-tables-fieldset').height('572px');
            prepareKvsGrid();
            jQuery('#inactive-cultures-text').hide();
            jQuery('#attr-tables-multiedit-btn').hide();
            jQuery('#attr-tables-export-excell-btn').show();
            break;
        case LAYER_ISAK_PLOTS: //isak
            jQuery('#zp-attr-info').hide();
            jQuery('#kvs-attr-info').hide();
            jQuery('#kms-attr-info').hide();
            jQuery('#for-isak-attr-info').hide();
            jQuery('#gps-attr-info').hide();
            jQuery('#for-isak-for-editing .combobox-f').hide();

            jQuery('#isak-attr-info').show();
            jQuery('#attr-tables-export-btn').show();
            jQuery('#export-xls-btn').show();
            jQuery('#attr-tables-edit-btn').show();
            jQuery('#allowable-attr-info').hide();
            jQuery('#lfa-attr-info').hide();
            jQuery('#natura-2000-attr-info').hide();
            jQuery('#pzp-attr-info').hide();
            jQuery('#vps-chervenogushi-attr-info').hide();
            jQuery('#vps-zimni-attr-info').hide();
            jQuery('#vps-livaden-blatar-attr-info').hide();
            jQuery('#vps-orli-leshoyadi-attr-info').hide();
            jQuery('#copy-items').show();
            jQuery('.js-copy-layer-btn-zp').show();
            jQuery('.js-copy-layer-btn-filtered-zp').show();
            jQuery('.js-copy-layer-btn-for-isak').show();
            jQuery('.js-copy-layer-btn-filtered-for-isak').show();
            jQuery('.js-copy-layer-btn').show();
            jQuery('.js-copy-layer-filtered-btn').show();
            jQuery('#plots-tables-fieldset').height('572px');
            initISAKGrid(selected.id);
            jQuery('#inactive-cultures-text').hide();
            jQuery('#attr-tables-multiedit-btn').hide();
            jQuery('#attr-tables-export-excell-btn').show();
            break;
        case LAYER_KOMAS_PLOTS: //kms
            jQuery('#zp-attr-info').hide();
            jQuery('#kvs-attr-info').hide();
            jQuery('#isak-attr-info').hide();
            jQuery('#for-isak-attr-info').hide();
            jQuery('#gps-attr-info').hide();
            jQuery('#for-isak-for-editing .combobox-f').hide();
            jQuery('#kms-attr-info').show();
            jQuery('#allowable-attr-info').hide();
            jQuery('#lfa-attr-info').hide();
            jQuery('#attr-tables-export-btn').show();
            jQuery('#export-xls-btn').show();
            jQuery('#attr-tables-edit-btn').show();
            jQuery('#natura-2000-attr-info').hide();
            jQuery('#pzp-attr-info').hide();
            jQuery('#vps-chervenogushi-attr-info').hide();
            jQuery('#vps-zimni-attr-info').hide();
            jQuery('#vps-livaden-blatar-attr-info').hide();
            jQuery('#vps-orli-leshoyadi-attr-info').hide();
            jQuery('#copy-items').show();
            jQuery('.js-copy-layer-btn-zp').show();
            jQuery('.js-copy-layer-btn-filtered-zp').show();
            jQuery('.js-copy-layer-btn-for-isak').show();
            jQuery('.js-copy-layer-btn-filtered-for-isak').show();
            jQuery('.js-copy-layer-btn').show();
            jQuery('.js-copy-layer-filtered-btn').show();
            jQuery('#plots-tables-fieldset').height('572px');
            initKmsGrid(selected.id);
            jQuery('#inactive-cultures-text').hide();
            jQuery('#attr-tables-multiedit-btn').hide();
            jQuery('#attr-tables-export-excell-btn').show();
            break;
        case LAYER_FOR_ISAK_PLOTS: //for isak
            jQuery('#zp-attr-info').hide();
            jQuery('#kvs-attr-info').hide();
            jQuery('#kms-attr-info').hide();
            jQuery('#allowable-attr-info').hide();
            jQuery('#gps-attr-info').hide();
            jQuery('#isak-attr-info').hide();
            jQuery('#for-isak-attr-info').show();
            jQuery('#for-isak-for-editing .combobox-f').show();
            jQuery('#lfa-attr-info').hide();
            jQuery('#attr-tables-export-btn').show();
            jQuery('#attr-tables-edit-btn').show();
            jQuery('#natura-2000-attr-info').hide();
            jQuery('#pzp-attr-info').hide();
            jQuery('#vps-chervenogushi-attr-info').hide();
            jQuery('#vps-zimni-attr-info').hide();
            jQuery('#vps-livaden-blatar-attr-info').hide();
            jQuery('#vps-orli-leshoyadi-attr-info').hide();
            jQuery('#copy-items').show();
            jQuery('.js-copy-layer-btn-zp').show();
            jQuery('.js-copy-layer-btn-filtered-zp').show();
            jQuery('.js-copy-layer-btn-for-isak').hide();
            jQuery('.js-copy-layer-btn-filtered-for-isak').hide();
            jQuery('.js-copy-layer-btn').show();
            jQuery('.js-copy-layer-filtered-btn').show();
            jQuery('.js-attr-tables-edit').hide();
            initFORISAKGrid(selected.id);
            jQuery('#inactive-cultures-text').show();
            jQuery('#plots-tables-fieldset').height('533px');
            jQuery('#attr-tables-edit-btn').show();
            jQuery('#attr-tables-multiedit-btn').show();
            jQuery('#attr-tables-export-excell-btn').hide();
            break;
        case LAYER_TEMP_DATA: //gps
            jQuery('#zp-attr-info').hide();
            jQuery('#kvs-attr-info').hide();
            jQuery('#kms-attr-info').hide();
            jQuery('#allowable-attr-info').hide();
            jQuery('#isak-attr-info').hide();
            jQuery('#gps-attr-info').show();
            jQuery('#lfa-attr-info').hide();
            jQuery('#for-isak-attr-info').hide();
            jQuery('#attr-tables-export-btn').show();
            jQuery('#export-xls-btn').show();
            jQuery('#attr-tables-edit-btn').show();
            jQuery('#natura-2000-attr-info').hide();
            jQuery('#pzp-attr-info').hide();
            jQuery('#vps-chervenogushi-attr-info').hide();
            jQuery('#vps-zimni-attr-info').hide();
            jQuery('#vps-livaden-blatar-attr-info').hide();
            jQuery('#vps-orli-leshoyadi-attr-info').hide();
            jQuery('#copy-items').show();
            jQuery('.js-copy-layer-btn-zp').show();
            jQuery('.js-copy-layer-btn-filtered-zp').show();
            jQuery('.js-copy-layer-btn-for-isak').show();
            jQuery('.js-copy-layer-btn-filtered-for-isak').show();
            jQuery('.js-copy-layer-btn').hide();
            jQuery('.js-copy-layer-filtered-btn').hide();
            jQuery('.js-attr-tables-edit').hide();
            initGPSGrid(selected.id);
            jQuery('#inactive-cultures-text').hide();
            jQuery('#plots-tables-fieldset').height('572px');
            jQuery('#attr-tables-edit-btn').show();
            jQuery('#attr-tables-export-excell-btn').show();
            break;
        case LAYER_TYPE_WORK_LAYER: //gps
            jQuery("#attr-cols-edit-btn").show();
            jQuery('#work-layer-filter-fields').show();
            jQuery('#zp-attr-info').hide();
            jQuery('#kvs-attr-info').hide();
            jQuery('#kms-attr-info').hide();
            jQuery('#allowable-attr-info').hide();
            jQuery('#isak-attr-info').hide();
            jQuery('#gps-attr-info').hide();
            jQuery('#lfa-attr-info').hide();
            jQuery('#for-isak-attr-info').hide();
            jQuery('#att-tables-multi-edit-btn').hide();
            jQuery('#attr-tables-export-btn').show();
            jQuery('#export-xls-btn').show();
            jQuery('#attr-tables-edit-btn').show();
            jQuery('#natura-2000-attr-info').hide();
            jQuery('#pzp-attr-info').hide();
            jQuery('#vps-chervenogushi-attr-info').hide();
            jQuery('#vps-zimni-attr-info').hide();
            jQuery('#vps-livaden-blatar-attr-info').hide();
            jQuery('#vps-orli-leshoyadi-attr-info').hide();
            jQuery('#copy-items').show();
            jQuery('.js-copy-layer-btn-zp').show();
            jQuery('.js-copy-layer-btn-filtered-zp').show();
            jQuery('.js-copy-layer-btn-for-isak').show();
            jQuery('.js-copy-layer-btn-filtered-for-isak').show();
            jQuery('.js-copy-layer-btn').show();
            jQuery('.js-copy-layer-filtered-btn').show();
            jQuery('.js-attr-tables-edit').show();
            initWorkLayerGrid(selected.id);
            jQuery('#inactive-cultures-text').hide();
            jQuery('#plots-tables-fieldset').height('572px');
            jQuery('#attr-tables-edit-btn').show();
            jQuery('#attr-tables-export-excell-btn').show();
            jQuery('#attr-tables-multiedit-btn').hide();
            break;
        case 'layer_allowable':
            initAllowableEkateCombobox();
            jQuery('#zp-attr-info').hide();
            jQuery('#kvs-attr-info').hide();
            jQuery('#kms-attr-info').hide();
            jQuery('#isak-attr-info').hide();
            jQuery('#for-isak-attr-info').hide();
            jQuery('#gps-attr-info').hide();
            jQuery('#for-isak-for-editing .combobox-f').hide();
            jQuery('#allowable-attr-info').show();
            jQuery('#natura-2000-attr-info').hide();
            jQuery('#pzp-attr-info').hide();
            jQuery('#vps-chervenogushi-attr-info').hide();
            jQuery('#lfa-attr-info').hide();
            jQuery('#vps-zimni-attr-info').hide();
            jQuery('#vps-livaden-blatar-attr-info').hide();
            jQuery('#vps-orli-leshoyadi-attr-info').hide();
            jQuery('#copy-items').hide();
            jQuery('#attr-tables-export-btn').hide();
            jQuery('#export-xls-btn').hide();
            jQuery('#attr-tables-edit-btn').hide();
            jQuery('#plots-tables-fieldset').height('572px');
            initAllowableGrid(selected.id);
            jQuery('#inactive-cultures-text').hide();
            jQuery('#attr-tables-multiedit-btn').hide();
            jQuery('#attr-tables-export-excell-btn').hide();
            break;
        case LAYER_TYPE_LFA: //neoblagodetelstvani raioni
            initLFAEkateCombobox();

            jQuery('#zp-attr-info').hide();
            jQuery('#kvs-attr-info').hide();
            jQuery('#kms-attr-info').hide();
            jQuery('#allowable-attr-info').hide();
            jQuery('#gps-attr-info').hide();
            jQuery('#isak-attr-info').hide();
            jQuery('#for-isak-attr-info').hide();
            jQuery('#for-isak-for-editing .combobox-f').hide();
            jQuery('#pzp-attr-info').hide();
            jQuery('#vps-chervenogushi-attr-info').hide();
            jQuery('#natura-2000-attr-info').hide();
            jQuery('#attr-tables-export-btn').hide();
            jQuery('#export-xls-btn').hide();
            jQuery('#attr-tables-edit-btn').hide();
            jQuery('#lfa-attr-info').show();
            jQuery('#copy-items').hide();
            jQuery('#vps-zimni-attr-info').hide();
            jQuery('#vps-livaden-blatar-attr-info').hide();
            jQuery('#vps-orli-leshoyadi-attr-info').hide();
            jQuery('#plots-tables-fieldset').height('572px');
            initLFAGrid(selected.id);
            jQuery('#inactive-cultures-text').hide();
            jQuery('#attr-tables-multiedit-btn').hide();
            jQuery('#attr-tables-export-excell-btn').hide();
            break;
        case LAYER_TYPE_NATURA_2000: //natura 2000
            jQuery('#zp-attr-info').hide();
            jQuery('#kvs-attr-info').hide();
            jQuery('#gps-attr-info').hide();
            jQuery('#kms-attr-info').hide();
            jQuery('#allowable-attr-info').hide();
            jQuery('#isak-attr-info').hide();
            jQuery('#for-isak-attr-info').hide();
            jQuery('#for-isak-for-editing .combobox-f').hide();
            jQuery('#pzp-attr-info').hide();
            jQuery('#vps-chervenogushi-attr-info').hide();
            jQuery('#natura-2000-attr-info').show();
            jQuery('#lfa-attr-info').hide();
            jQuery('#attr-tables-export-btn').hide();
            jQuery('#export-xls-btn').hide();
            jQuery('#attr-tables-edit-btn').hide();
            jQuery('#vps-zimni-attr-info').hide();
            jQuery('#vps-livaden-blatar-attr-info').hide();
            jQuery('#vps-orli-leshoyadi-attr-info').hide();
            jQuery('#plots-tables-fieldset').height('572px');
            jQuery('#copy-items').hide();
            initNatura_2000_Grid(selected.id);
            jQuery('#inactive-cultures-text').hide();
            jQuery('#attr-tables-multiedit-btn').hide();
            jQuery('#attr-tables-export-excell-btn').hide();
            break;
        case LAYER_TYPE_PERMANETELY_GREEN_AREAS: //pzp(postoqnno zatreveni ploshti)
            initPZPEkateCombobox();

            jQuery('#zp-attr-info').hide();
            jQuery('#kvs-attr-info').hide();
            jQuery('#kms-attr-info').hide();
            jQuery('#gps-attr-info').hide();
            jQuery('#allowable-attr-info').hide();
            jQuery('#isak-attr-info').hide();
            jQuery('#for-isak-attr-info').hide();
            jQuery('#for-isak-for-editing .combobox-f').hide();
            jQuery('#lfa-attr-info').hide();
            jQuery('#natura-2000-attr-info').hide();
            jQuery('#attr-tables-export-btn').hide();
            jQuery('#export-xls-btn').hide();
            jQuery('#attr-tables-edit-btn').hide();
            jQuery('#pzp-attr-info').show();
            jQuery('#vps-chervenogushi-attr-info').hide();
            jQuery('#vps-zimni-attr-info').hide();
            jQuery('#vps-livaden-blatar-attr-info').hide();
            jQuery('#vps-orli-leshoyadi-attr-info').hide();
            jQuery('#plots-tables-fieldset').height('572px');
            jQuery('#copy-items').show();
            jQuery('.js-copy-layer-btn-zp').show();
            jQuery('.js-copy-layer-btn-filtered-zp').show();
            jQuery('.js-copy-layer-btn-for-isak').show();
            jQuery('.js-copy-layer-btn-filtered-for-isak').show();
            jQuery('.js-copy-layer-btn').show();
            jQuery('.js-copy-layer-filtered-btn').show();
            initPZPGrid(selected.id);
            jQuery('#inactive-cultures-text').hide();
            jQuery('#attr-tables-multiedit-btn').hide();
            jQuery('#attr-tables-export-excell-btn').hide();
            break;
        case LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI:
            jQuery('#zp-attr-info').hide();
            jQuery('#kvs-attr-info').hide();
            jQuery('#kms-attr-info').hide();
            jQuery('#allowable-attr-info').hide();
            jQuery('#gps-attr-info').hide();
            jQuery('#isak-attr-info').hide();
            jQuery('#for-isak-attr-info').hide();
            jQuery('#for-isak-for-editing .combobox-f').hide();
            jQuery('#lfa-attr-info').hide();
            jQuery('#natura-2000-attr-info').hide();
            jQuery('#attr-tables-export-btn').hide();
            jQuery('#export-xls-btn').hide();
            jQuery('#attr-tables-edit-btn').hide();
            jQuery('#pzp-attr-info').hide();
            jQuery('#vps-chervenogushi-attr-info').show();
            jQuery('#vps-zimni-attr-info').hide();
            jQuery('#vps-livaden-blatar-attr-info').hide();
            jQuery('#vps-orli-leshoyadi-attr-info').hide();
            jQuery('#plots-tables-fieldset').height('572px');
            jQuery('#copy-items').hide();
            jQuery('#inactive-cultures-text').hide();
            initVPSGaskiChervenogushiGrid(selected.id);
            jQuery('#attr-tables-multiedit-btn').hide();
            jQuery('#attr-tables-export-excell-btn').hide();
            break;
        case LAYER_TYPE_VPS_GASKI_ZIMNI:
            jQuery('#zp-attr-info').hide();
            jQuery('#kvs-attr-info').hide();
            jQuery('#kms-attr-info').hide();
            jQuery('#gps-attr-info').hide();
            jQuery('#allowable-attr-info').hide();
            jQuery('#isak-attr-info').hide();
            jQuery('#for-isak-attr-info').hide();
            jQuery('#for-isak-for-editing .combobox-f').hide();
            jQuery('#lfa-attr-info').hide();
            jQuery('#natura-2000-attr-info').hide();
            jQuery('#attr-tables-export-btn').hide();
            jQuery('#export-xls-btn').hide();
            jQuery('#attr-tables-edit-btn').hide();
            jQuery('#pzp-attr-info').hide();
            jQuery('#vps-chervenogushi-attr-info').hide();
            jQuery('#vps-zimni-attr-info').show();
            jQuery('#vps-livaden-blatar-attr-info').hide();
            jQuery('#vps-orli-leshoyadi-attr-info').hide();
            jQuery('#copy-items').hide();
            jQuery('#plots-tables-fieldset').height('572px');
            initVPSGaskiZimniGrid(selected.id);
            jQuery('#inactive-cultures-text').hide();
            jQuery('#attr-tables-multiedit-btn').hide();
            jQuery('#attr-tables-export-excell-btn').hide();
            break;
        case LAYER_TYPE_VPS_LIVADEN_BLATAR:
            jQuery('#zp-attr-info').hide();
            jQuery('#kvs-attr-info').hide();
            jQuery('#kms-attr-info').hide();
            jQuery('#gps-attr-info').hide();
            jQuery('#allowable-attr-info').hide();
            jQuery('#isak-attr-info').hide();
            jQuery('#for-isak-attr-info').hide();
            jQuery('#for-isak-for-editing .combobox-f').hide();
            jQuery('#lfa-attr-info').hide();
            jQuery('#natura-2000-attr-info').hide();
            jQuery('#attr-tables-export-btn').hide();
            jQuery('#export-xls-btn').hide();
            jQuery('#attr-tables-edit-btn').hide();
            jQuery('#pzp-attr-info').hide();
            jQuery('#vps-chervenogushi-attr-info').hide();
            jQuery('#vps-zimni-attr-info').hide();
            jQuery('#vps-livaden-blatar-attr-info').show();
            jQuery('#vps-orli-leshoyadi-attr-info').hide();
            jQuery('#copy-items').hide();
            jQuery('#plots-tables-fieldset').height('572px');
            initVPSLivadenBlatarGrid(selected.id);
            jQuery('#inactive-cultures-text').hide();
            jQuery('#attr-tables-multiedit-btn').hide();
            jQuery('#attr-tables-export-excell-btn').hide();
            break;
        case LAYER_TYPE_VPS_ORLI_LESHOYADI:
            jQuery('#zp-attr-info').hide();
            jQuery('#kvs-attr-info').hide();
            jQuery('#kms-attr-info').hide();
            jQuery('#gps-attr-info').hide();
            jQuery('#allowable-attr-info').hide();
            jQuery('#isak-attr-info').hide();
            jQuery('#for-isak-attr-info').hide();
            jQuery('#for-isak-for-editing .combobox-f').hide();
            jQuery('#lfa-attr-info').hide();
            jQuery('#natura-2000-attr-info').hide();
            jQuery('#attr-tables-export-btn').show();
            jQuery('#export-xls-btn').show();
            jQuery('#attr-tables-edit-btn').hide();
            jQuery('#pzp-attr-info').hide();
            jQuery('#vps-chervenogushi-attr-info').hide();
            jQuery('#vps-zimni-attr-info').hide();
            jQuery('#vps-livaden-blatar-attr-info').hide();
            jQuery('#vps-orli-leshoyadi-attr-info').show();
            jQuery('#copy-items').show();
            jQuery('.js-copy-layer-btn-zp').show();
            jQuery('.js-copy-layer-btn-filtered-zp').show();
            jQuery('.js-copy-layer-btn-for-isak').show();
            jQuery('.js-copy-layer-btn-filtered-for-isak').show();
            jQuery('.js-copy-layer-btn').show();
            jQuery('.js-copy-layer-filtered-btn').show();
            jQuery('#plots-tables-fieldset').height('572px');
            initVPSOrliLeshoyadiGrid(selected.id);
            jQuery('#inactive-cultures-text').hide();
            jQuery('#attr-tables-multiedit-btn').hide();
            jQuery('#attr-tables-export-excell-btn').hide();
            break;
        default:
            break;
    }
    
    return false;

}

/**
 * Preview map export
 *
 * @return  {void}
 */
function previewMapExport() {

    let combotree = jQuery('#all-layers-tree').combotree('tree');
	let selectedLayer = combotree.tree('getSelected');
    let checkedLayers = combotree.tree('getChecked');

	
	if (!selectedLayer)
    {
        jQuery.messager.alert('Грешка', 'Не е избран активен слой!');
        return false;
    }

    containsNonPrintableLayer = false;
	let exportableLayers = checkedLayers.filter((layer) => {
		try {
			if (nonPrintableMapLayers.includes(layer.attributes.layer_type)) {
                containsNonPrintableLayer = true;          
				return false;
			}

            if(layer.children != undefined) {
                return false;
            }
		} catch (error) {
			return false;
		}

		return true;
	});

    if (true === containsNonPrintableLayer) {
		jQuery.messager.alert('Грешка', 'Имате селектиран слой, който не може да бъде принтиран, моля премахнете Допустим слой, ВПС слой, Натура 2000, Необлагодетелствени райони или Постоянно затревени площи');
		return false;
	}

    buildPreviewAttrPanel(exportableLayers);
    jQuery('#win-map-export-preview').window('open');
}

function prepareKvsGrid() {
    clearFilterPlotValues();
    jQuery('#kvs-applied-filters').filterWidget('populate', []);
    jQuery('#search-ekatte').combobox('clear');
    let ekattesCombobox = ComboboxData.EkateCombobox;
    if(ekattesCombobox[1]) {
        ekattesCombobox[0].selected = false;
        ekattesCombobox[1].selected = true;
    }
    jQuery('#search-ekatte').combobox('loadData', ekattesCombobox);
    let combotree = jQuery('#all-layers-tree').combotree('tree');
    let selectedLayer = combotree.tree('getSelected');

    if(selectedLayer.attributes.ekatte) {
        jQuery('#search-ekatte').combobox('setValue', selectedLayer.attributes.ekatte);
        let ekattes = jQuery('#search-ekatte').combobox('getData');
        let ekatte = ekattes.filter(e => e.ekate === selectedLayer.attributes.ekatte);
        ekatte[0].selected = true;
        jQuery('#search-ekatte').combobox('loadData', ekatte);
        filtersCriteria = getTextFilterPlots();
        renderFilter(false);
    }

    initKVSGrid();
}

function setLayerEditFieldsData(data) {
    var combotree = jQuery('#all-layers-tree').combotree('tree');
    var selected = combotree.tree('getSelected');

    jQuery('#layer-change-ekatte').parent().show();

    if(selected.attributes.layer_type == LAYER_KVS_PLOTS){

        jQuery('#layer-change-ekatte > input').combobox({
            url: 'index.php?common-rpc=ekate-combobox',
            rpcParams: [{record_all: false}],
            valueField: 'ekate',
            textField: 'text',
            required: true,
            multiple: true,
            filter: function(q, row){
                var opts = jQuery(this).combobox('options');
                var text = row[opts.textField].toLowerCase();
                var value = row[opts.valueField];
                var find = q.toLowerCase();
                if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
                {
                    return true;
                }
            },
            onSelect: function(record){
                if(record === undefined) return;
                var ekate_style = JSON.parse(data.style)[record.ekate];

                jQuery('#label-size > input').numberspinner('setValue', ekate_style.label_size);
                jQuery("#layer-color > input").spectrum("set", ekate_style.color);
                jQuery("#border-color > input").spectrum("set", ekate_style.border_color);

                jQuery("#label-name > input").combobox('clear');
                for (var i = 0; i < ekate_style.label_name.length; i++) {
                    jQuery("#label-name > input").combobox('select', ekate_style.label_name[i]);
                }

                if (ekate_style.tags) {
                    jQuery('#tags > input').prop('checked', true);
                }else{
                    jQuery('#tags > input').prop('checked', false);
                }

                if (ekate_style.border_only) {
                    jQuery('#onlyborder > input').prop('checked', true);
                }else{
                    jQuery('#onlyborder > input').prop('checked', false);
                }
                jQuery('#transparency > input').numberspinner('setValue', 100 - ekate_style.transparency);
            },
            onLoadSuccess: function(data){
                if(selected.attributes.ekatte){
                    selected_ekatte = selected.attributes.ekatte;
                }
                jQuery('#layer-change-ekatte > input').combobox('select', selected_ekatte ?? data[0].ekate);
            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter

        });
    } else {
        jQuery('#layer-change-ekatte').parent().hide();
        var layer_style = JSON.parse(data.style);
        if(layer_style){
            for (const key in layer_style.label_name) {
                if (!layer_style.label_name.hasOwnProperty(key)) continue;
                if(layer_style.label_name[key]) {
                    jQuery("#label-name > input").combobox('select', layer_style.label_name[key]);
                }
            }
        }

    }

    var style = JSON.parse(data.style);
    var labelSize = (style && style.label_size) ? style.label_size : 8;
    var layerColor = (style && style.color) ? style.color : 0;
    var borderColor = (style && style.border_color) ? style.border_color : '111111';
    jQuery('#label-size > input').numberspinner('setValue', labelSize);
    jQuery("#layer-color > input").spectrum("set", layerColor);
    jQuery("#border-color > input").spectrum("set", borderColor);

    if (style && style.tags) {
        jQuery('#tags > input').prop('checked', true);
    }else{
        jQuery('#tags > input').prop('checked', false);
    }

    if (style && style.border_only) {
        jQuery('#onlyborder > input').prop('checked', true);
    }else{
        jQuery('#onlyborder > input').prop('checked', false);
    }

    var transparency = (style && style.transparency) ? (100 - style.transparency) : 0;
    jQuery('#transparency > input').numberspinner('setValue', transparency);

    if (selected.attributes.layer_type == LAYER_TYPE_WORK_LAYER) {
        jQuery('#work-layer-year').combobox({
            url: 'index.php?common-rpc=farming-year-combobox',
            textField: 'title',
            valueField: 'id',
            rpcParams: [{selected: 'current'}],
            onLoadSuccess: function () {
                jQuery('#work-layer-year').combobox('setValue', data.year);
            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });

        jQuery('#work-layer-farming').combobox({
            url: 'index.php?common-rpc=farming-combobox',
            rpcParams: [{
                record_all:true
            }],
            valueField: 'id',
            textField: 'name',
            disabled: false,
            onLoadSuccess: function () {
                jQuery('#work-layer-farming').combobox('setValue', data.farming);
            },
            formatter: function (row) {
                if(row.name == 'Всички') {
                    row.name = 'Извън стопанство';
                }
                return row.name
            },
            onSelect: function (row) {
                if (row.id == "") {
                    jQuery('#work-layer-year').combobox('clear');
                    jQuery('#work-layer-year').combobox('disable');
                } else {
                    jQuery('#work-layer-year').combobox('enable');
                    jQuery('#work-layer-year').combobox('reset');
                    jQuery('#work-layer-year').combobox('select', 13);
                }
            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });

        jQuery('#work-layer-name-fields').show();
        jQuery('#work-layer-name > input').val(data.name);
        jQuery('#work-layer-name > input').validatebox({
            required: true,
            validType: 'minLength[3]',
            missingMessage: 'Моля въведете име на работния слой',
            delay: 100
        });
    } else {
        jQuery('#work-layer-name-fields').hide();
    }
}

function getLayerEditFieldsData() {

    var combotree = jQuery('#all-layers-tree').combotree('tree');
    var selected = combotree.tree('getSelected');
    var selected_ekatte = [];

    if (selected.attributes.layer_type == LAYER_KVS_PLOTS)
    {
        if(jQuery('#layer-change-ekatte > input').combobox('isValid') === false)
        {
            jQuery.messager.alert('Грешка', 'Не е избранo ЕКАТТЕ!');
            return false;
        }else{
            selected_ekatte = jQuery('#layer-change-ekatte > input').combobox('getValues');
        }
    }

    var layerEditObject = {
        id: selected.parent_id ?? selected.id,
        color: jQuery('#layer-color > input').spectrum("get").toHex(),
        border_color: jQuery('#border-color > input').spectrum("get").toHex(),
        transparency: jQuery('#transparency > input').numberspinner('getValue'),
        tags: jQuery('#tags > input').is(':checked') ? true : false,
        border_only: jQuery('#onlyborder > input').is(':checked') ? true : false,
        label_name: jQuery('#labelName').combobox('getValues'),
        label_size: jQuery('#label-size > input').numberspinner('getValue'),
        ekatte: selected_ekatte
    };

    if (selected.attributes.layer_type == LAYER_TYPE_WORK_LAYER) {
        layerEditObject.layer_type    = LAYER_TYPE_WORK_LAYER;
        layerEditObject.layer_name    = jQuery('#work-layer-name > input').val();
        layerEditObject.layer_farming = jQuery('#work-layer-farming').combobox('getValue');
        layerEditObject.layer_year    = jQuery('#work-layer-year').combobox('getValue');
    }

    return layerEditObject;
}

function initAllowableEkateCombobox() {

    jQuery('#allowable-filter-ekate').combobox({
        data: allowable_ekate_cb,
        valueField: 'ekate',
        textField: 'text',
        filter: function(q, row) {
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if (text.indexOf(find) != -1 || value.indexOf(find) != -1)
            {
                return true;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initLFAEkateCombobox() {

    jQuery('#lfa-filter-ekate').combobox({
        data:lfa_cb,
        valueField: 'ekate',
        textField: 'text',
        filter: function(q, row) {
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if (text.indexOf(find) != -1 || value.indexOf(find) != -1)
            {
                return true;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initPZPEkateCombobox() {
    jQuery('#pzp-filter-ekate').combobox({
        data: pzp_cb,
        valueField: 'ekate',
        textField: 'text',
        filter: function(q, row) {
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if (text.indexOf(find) != -1 || value.indexOf(find) != -1)
            {
                return true;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initLayerEdit() {
    jQuery('#tool-edit-layer').trigger('click');
}
