function initKVSMergeFields() {
    var featureGeometry = vectors.features[0].geometry;
    var area = (featureGeometry.getGeodesicArea(map.getProjection()) / 1000).toFixed(3);

//    jQuery('#kvs-merge-area > input').val(area);

    vectors.features[0].attributes.label = area + ' дка';
    vectors.redraw();

    var editingIds = jQuery('#kvs-merge-editing-ids > input').val().split(',');

    jQuery('#kvs-merge-area > input').numberbox({
		min: 0.001,
        precision: 3,
        required: true
	});

    jQuery('#kvs-merge-number > input').combobox({
		url: 'index.php?common-rpc=kvs-free-numbers',
        rpcParams: [{
            gid: editingIds[0]
        }],
		valueField: 'id',
		textField: 'name',
		editable: true,
        required: true,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

    jQuery('#kvs-merge-ntp > input').combobox({
		url: 'index.php?common-rpc=plot-ntp-combobox',
		valueField: 'id',
		textField: 'name',
		editable: false,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

	jQuery('#kvs-merge-category > input').combobox({
		url: 'index.php?common-rpc=plot-category-combobox',
		valueField: 'id',
		textField: 'name',
		editable: false,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

    jQuery('#kvs-merge-usable > input').combobox({
		data: [{id: 1, name: 'Да', selected: true}, {id: 0, name: 'Не'}],
		valueField: 'id',
		textField: 'name',
		editable: false
	});

    var date = new Date();
	var todayDate = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();

	jQuery('#kvs-merge-active-from > input').datebox({
		value: todayDate
	});
}

function saveKVSMerge()
{
    var ekate = jQuery('#kvs-merge-ekate > input').val();
    var masiv = jQuery('#kvs-merge-masiv > input').val();
    var number = jQuery('#kvs-merge-number > input').combobox('getValue');
    var area = jQuery('#kvs-merge-area > input').numberbox('getValue');

    if(!ekate)
    {
        jQuery.messager.alert('Грешка', 'Моля въведете ЕКАТТЕ.');
        return false;
    }

    if(!masiv)
    {
        jQuery.messager.alert('Грешка', 'Моля въведете масив.');
        return false;
    }

    if(!number)
    {
        jQuery.messager.alert('Грешка', 'Моля изберете номер на имота.');
        return false;
    }

    if(!area)
    {
        jQuery.messager.alert('Грешка', 'Моля въведете площ по документ.');
        return false;
    }

    var featureGeometry = vectors.features[0].geometry.clone();
    jQuery('#kvs-merge-geom > input').val(featureGeometry.transform(new OpenLayers.Projection("EPSG:900913"), new OpenLayers.Projection("EPSG:32635")).toString());

    var mergeObject = getKVSMergeFields();

    TF.Rpc.Map.KVSMerge.saveKVSMerge(mergeObject)
    .done(function (data) {
        mergeResult(data);
        vectors.removeAllFeatures();
        reloadLayerByName('layer_kvs');
    })
    .fail(function (errorObj) {

    });
}

function mergeResult(data)
{
    if(data && data.type == 'existNum')
    {
        jQuery.messager.alert('Грешка', 'Въведеният номер на имот вече съществува в базата данни!', 'error');

        return false;
    }

    if(data && data.type == 'contracts')
    {
        jQuery.messager.alert('Съобщение', 'Редактираните имоти участват в следните договори: ' + data.contracts.c_num + '. Моля проверете ги.<br/>'
                                + '<a href="index.php?page=Contracts.Home&contract_id=' + data.contracts.c_id + '" target="_blank">Отиди в договори</a>');
    }

    jQuery('#win-kvs-merge').window('close');
    reloadLayerByName('layer_kvs');
    vectors.removeAllFeatures();
    toggleGeometryTools(LAYER_KVS_PLOTS);
}

function getKVSMergeFields() {
    var returnObj = new Object();

    returnObj = {
        editingIds: jQuery('#kvs-merge-editing-ids > input').val(),
        mergeGeom: jQuery('#kvs-merge-geom > input').val(),
        ekate: jQuery('#kvs-merge-ekate > input').val(),
        masiv: jQuery('#kvs-merge-masiv > input').val(),
        number: jQuery('#kvs-merge-number > input').combobox('getValue'),
        area: jQuery('#kvs-merge-area > input').val(),
        mestnost: jQuery('#kvs-merge-mestnost > input').val(),
        category: jQuery('#kvs-merge-category > input').combobox('getValue'),
        ntp: jQuery('#kvs-merge-ntp > input').combobox('getValue'),
        usable: jQuery('#kvs-merge-usable > input').combobox('getValue'),
        active: jQuery('#kvs-merge-active-from > input').datebox('getValue')
    };

    return returnObj;
}
