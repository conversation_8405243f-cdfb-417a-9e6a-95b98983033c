/*jslint browser: true*/
/*global jQuery, map, OpenLayers,getValuesFilterPlots, displayFeatureSelection, EasyUIRPCLoaders.EasyUIGridCustomLoader,vectors,getTextFilterPlots, clearFilterPlotValues,_, _filteredPlotGids*/
var addToFilterUsed = false,
    addToFilterConfirmationMsg = 'Резултатите от "Добави към филтър" ще бъдат загубени. Искате ли да продължите?',
    filtersCriteria = [],
    removedTarget;

function initKVSGrid() {
    'use strict';
    var array = [],
        rpcParams,
        i;

    jQuery('#clip-kvs-btn-container').css('visibility', 'visible');
    jQuery('#plots-filters-layout').layout('remove', 'west');

    rpcParams = getValuesFilterPlots();

    let plot_ids = [];
    if (!addToFilterUsed) {
        for (i = 0; i < vectors.features.length; i = i + 1) {
            plot_ids.push(vectors.features[i].attributes.gid);
        }
    }
    if(plot_ids.length > 0) {
        rpcParams.plot_id = plot_ids;
    }

    rpcParams.filter_action = 'exec_filter';

    jQuery('#plots-tables').datagrid({
        url: 'index.php?map-rpc=kvs-datagrid',
        rpcParams: [rpcParams],
        iconCls: 'icon-plot',
        nowrap: true,
        singleSelect: true,
        checkOnSelect: false,
        selectOnCheck: false,
        autoRowHeight: true,
        striped: true,
        pageSize: 20,
        fit: true,
        fitColumns: true,
        showFooter: true,
        sortName: 'gid',
        sortOrder: 'asc',
        idField: 'gid',
        pagination: true,
        rownumbers: true,
        border: false,
        frozenColumns: [
            [{
                field: 'ck',
                checkbox: true
            }]
        ],
        columns: [
            [{
                field: 'kad_ident',
                title: '<b>Идентификатор</b>',
                sortable: true,
                width: "10.00%"
            }, {
                field: 'mestnost',
                title: '<b>Местност</b>',
                sortable: true,
                width: "12.05%"
            }, {
                field: 'category',
                title: '<b>Категория</b>',
                sortable: true,
                width: "12.05%"
            }, {
                field: 'area_type',
                title: '<b>НТП</b>',
                sortable: true,
                width: "10.00%"
            }, {
                field: 'owners_osz',
                title: '<b>Собственици<br/>от ОСЗ</b>',
                align: 'center',
                width: "10.00%"
            }, {
                field: 'document_area',
                title: '<b>Площ по<br/>док. (дка)</b>',
                sortable: true,
                align: 'center',
                width: "9.64%"
            }, {
                field: 'allowable_area',
                title: '<b>Площ по<br/>сечение (дка)</b>',
                sortable: true,
                align: 'center',
                width: "12.05%"
            },
                {
                    field: 'allow_prec',
                    title: '<b> Процент </b>',
                    sortable: true,
                    align: 'center',
                    width: "12.05%"
                }]
        ],
        onSelect: function (rowIndex, rowData) {
            displayFeatureSelection(rowData.st_astext, rowData);
        },
        toolbar: '#attr-tables-toolbar',
        onBeforeLoad: function () {
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function appendToFiltersCriteria(filterData) {
    'use strict';
    var key,
        filterKeys = [],
        tmp,
        tmpArr;

    for (key in filterData) {
        filterKeys.push(key);
    }

    filterKeys.forEach(function (key) {
        if (typeof filtersCriteria[key] === 'object') {
            if (typeof filterData[key] === 'object') {
                filterData[key].forEach(function (insideKey) {
                    if (filterData[key][insideKey] !== '' && filterData[key][insideKey] !== 'Всички') {
                        filtersCriteria[key] += ',' + filterData[key][insideKey];
                    }
                });
            } else {
                if (filterData[key] !== '') {
                    filtersCriteria[key] += ',' + filterData[key];
                }
            }
        } else {
            if (filterData[key] !== '' && filterData[key] !== 'Всички') {
                tmp = filterData[key];
                filtersCriteria[key] += ',' + tmp;
            }
        }
        tmpArr = filtersCriteria[key].split(',');
        filtersCriteria[key] = _.uniq(tmpArr);
        filtersCriteria[key] = _.without(filtersCriteria[key], '');
        filtersCriteria[key] = filtersCriteria[key].join();
    });
}


function renderFilter(add_action) {
    'use strict';
    var filterData = getTextFilterPlots();
    if (add_action) {
        filterData.filter_action = 'add_to_filter';
        appendToFiltersCriteria(filterData);
    }
    let filterValues = getComboboxValuesFilterPlots();
    jQuery('#kvs-applied-filters').filterWidget('populate', filtersCriteria, filterValues);
}

function plotsFilter(remove_from_filter, initialGridLoad) {
    'use strict';
    var filterData = getValuesFilterPlots(),
        isFilterEmpty = checkForEmptyKVSFilter(filterData);


    jQuery('#extended-select-filtered-plots').prop('checked', false);
    jQuery('#extended-select-filtered-plots').trigger('change');
    sessionStorage.removeItem('checkedPlots');

    if (isFilterEmpty) {
        filterData.filter_action = 'clear_filter';
        addToFilterUsed = false;
        filtersCriteria = [];
        jQuery("#main-kvs-table-layout").layout('collapse', 'west');
    } else {
        filterData.filter_action = 'exec_filter';
    }
    
    if (remove_from_filter) {
        filterData.filter_action = 'remove_from_filter';
        filterData.removedTarget = removedTarget;
    } else {
        filtersCriteria = getTextFilterPlots();
    }
    if (addToFilterUsed) {
        jQuery.messager.confirm('Внимание', addToFilterConfirmationMsg, function (r) {
            if (r) {
                if (jQuery("#main-kvs-table-layout").layout('panel', 'west').data().panel.options.collapsed) {
                    jQuery("#main-kvs-table-layout").layout('expand', 'west');
                }
                jQuery('#win-plots-filter').window('close');
                jQuery('#plots-tables').datagrid({
                    pageNumber: 1,
                    rpcParams: [filterData]
                });
                addToFilterUsed = false;
                renderFilter(false);
            }
            return false;
        });
    } else {
        if (jQuery("#main-kvs-table-layout").layout('panel', 'west').data().panel.options.collapsed && !initialGridLoad && !isFilterEmpty) {
            jQuery("#main-kvs-table-layout").layout('expand', 'west');
        }
        jQuery('#win-plots-filter').window('close');
        jQuery('#plots-tables').datagrid({
            pageNumber: 1,
            rpcParams: [filterData]
        });
        addToFilterUsed = false;
        renderFilter(false);
    }
}

function addPlotsToFilter() {
    'use strict';
    var filterData = getValuesFilterPlots();
    filterData.filter_action = 'add_to_filter';
    if (jQuery("#main-kvs-table-layout").layout('panel', 'west').data().panel.options.collapsed) {
        jQuery("#main-kvs-table-layout").layout('expand', 'west');
    }

    jQuery('#extended-select-filtered-plots').prop('checked', false);
    jQuery('#extended-select-filtered-plots').trigger('change');

    jQuery('#plots-tables').datagrid({
        rpcParams: [filterData]
    });
    addToFilterUsed = true;
    renderFilter(true);
}

function clearPlotsFilter() {
    'use strict';
    clearFilterPlotValues();

    jQuery("#main-kvs-table-layout").layout('collapse', 'west');
    plotsFilter(false, true);
}

function zoomToExtent(data) {
    'use strict';
    if (!data) {
        return false;
    }

    var bounds = new OpenLayers.Bounds.fromString(data).transform(new OpenLayers.Projection("EPSG:32635"), map.getProjectionObject()),
        b = bounds.getCenterLonLat(),
        lonlat = new OpenLayers.LonLat(b.lon, b.lat);
    map.zoomToExtent(bounds);
    map.panTo(lonlat);
}

jQuery(function () {
    'use strict';

    jQuery('#kvs-applied-filters').filterWidget({});
    
    jQuery('#kvs-applied-filters').on('onclosed', 'a.remove-filter', function (e) {
        var target = (e.currentTarget),
        value = jQuery(target).data('value'),
        text = jQuery(target).data('text'),
        group = jQuery(target).data('group-ident'),
        target = jQuery(target).data('target'),
        oldData,
        position;

        oldData = filtersCriteria[group].split(',');
        position = oldData.indexOf(text.toString());
        if(group === 'ekate' && oldData.length === 1) {
            jQuery.messager.alert('Внимание', 'Във филтъра трябва да има поне едно землище!', 'warning');
            return;
        }

        oldData.splice(position, 1);
        filtersCriteria[group] = oldData.join();
        removedTarget = {
            field: target,
            value: value
        };
     
        jQuery.messager.confirm('Внимание', addToFilterConfirmationMsg, function (r) {
            if (r) {
                renderFilter(false);

                let filtedData = {
                    filter_action: 'remove_from_filter',
                    removedTarget: removedTarget,
                };
    
                let isEmptyAppliedFilters = checkForEmptyAppliedFilters();
                if(isEmptyAppliedFilters){
                    clearPlotsFilter();
                    filtedData.filter_action = 'clear_filter';
                    filtedData = getValuesFilterPlots();
                }

                jQuery('#plots-tables').datagrid({
                    rpcParams: [filtedData]
                });
                if(isEmptyAppliedFilters) {
                    jQuery("#main-kvs-table-layout").layout('collapse', 'west');
                }
            }
        });
        
    });
    jQuery('#extended-select-filtered-plots').bind('change', function () {
        if (jQuery('#extended-select-filtered-plots').is(':checked')) {
            jQuery('#select-filtered-plots').prop('checked', true);
            jQuery('#select-filtered-plots').trigger('change');
        } else {
            jQuery('#select-filtered-plots').prop('checked', false);
            jQuery('#select-filtered-plots').trigger('change');
        }
    });
});

function checkForEmptyKVSFilter(data) {
    'use strict';

    if (data.kad_ident !== "") {
        return false;
    }
    if (data.ekate[0] !== "" && data.ekate[0] != jQuery('#search-ekatte').combobox('getData')[0].ekate) {
        return false;
    }
    if (data.masiv !== "") {
        return false;
    }
    if (data.number !== "") {
        return false;
    }
    if (data.category[0] !== "") {
        return false;
    }
    if (data.area_type[0] !== "") {
        return false;
    }
    if (data.mestnost !== "") {
        return false;
    }
    if (data.irrigated_area !== "all") {
        return false;
    }
    if (data.cnum !== "") {
        return false;
    }
    if (data.contract_type[0] !== "") {
        return false;
    }
    if (data.contract_status !== "") {
        return false;
    }
    if (data.farming[0] !== "") {
        return false;
    }
    if (data.date_from !== "") {
        return false;
    }
    if (data.date_to !== "") {
        return false;
    }
    if (data.start_date !== "") {
        return false;
    }
    if (data.due_date !== "") {
        return false;
    }
    if (data.date_as_of !== "") {
        return false;
    }
    if (data.owner_name !== "") {
        return false;
    }
    if (data.owner_egn !== "") {
        return false;
    }
    if (data.ime_subekt !== "") {
        return false;
    }
    if (data.egn_subekt !== "") {
        return false;
    }
    if (data.rep_name !== "") {
        return false;
    }
    if (data.rep_egn !== "") {
        return false;
    }
    if (data.company_name !== "") {
        return false;
    }
    if (data.company_eik !== "") {
        return false;
    }
    if (data.legal_status !== "") {
        return false;
    }
    if (data.plot_id) {
        if (data.plot_id.length === 0) {
            return false;
        }
    }
    if (data.participation !=="") {
        return false;
    }

    return true;
}

function checkForEmptyAppliedFilters(data) {
    let groups = jQuery('#kvs-applied-filters fieldset>ul');
    if(groups.length != 1) {
        return false;
    }

    let ekatteGroup = jQuery("#kvs-applied-filters fieldset>ul[data-list-name='ekate']");
    if(ekatteGroup.length != 1) {
        return false;
    }

    let ekattesLinks = jQuery(ekatteGroup).find('a');
    if(ekattesLinks.length != 1) {
        return false;
    }

    if (jQuery(ekattesLinks).data('value') != jQuery('#search-ekatte').combobox('getValue')) {
        return false;
    }

    return true;
}

function KVSClipping(){
    if (!jQuery('#ekate').data().hasOwnProperty('combobox')) {
        jQuery('#ekate').combobox({
            data: ComboboxData.EkateCombobox.filter(ekatte => ekatte.ekate != ""),
            valueField: 'ekate',
            textField: 'text',
            required: true,
            onLoadSuccess: function () {
                jQuery('#search-plot-ekate > input').combobox({
                    data: jQuery('#search-ekatte').combobox('getData'),
                    valueField: 'ekate',
                    textField: 'text',
                    filter: function (q, row) {
                        var opts = jQuery(this).combobox('options');
                        var text = row[opts.textField].toLowerCase();
                        var value = row[opts.valueField];
                        var find = q.toLowerCase();
                        if (text.indexOf(find) != -1 || value.indexOf(find) != -1) {
                            return true;
                        }
                    }
                });
            },
            filter: function (q, row) {
                var opts = jQuery(this).combobox('options');
                var text = row[opts.textField].toLowerCase();
                var value = row[opts.valueField];
                var find = q.toLowerCase();
                if (text.indexOf(find) != -1 || value.indexOf(find) != -1) {
                    return true;
                }
            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }

    if (!jQuery('#allowable_layer_ntp').data().hasOwnProperty('combobox')) {
        jQuery('#allowable_layer_ntp').combobox({
            url: 'index.php?common-rpc=allowable-layer-ntp-combobox',
            rpcParams: [{}],
            valueField: 'value',
            textField: 'text',
            required: true,
            formatter: function (row) {
                return row.value + ': ' + row.text
            },
            onSelect: function (row) {
                jQuery(this).combobox('setText', row.value + ': ' + row.text);
            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }
    jQuery('#win-kvs-clipping').window('open');
}