function initAllowableGrid(layer_id)
{
	var checkedElement;
	var array = [];

	for (var i=0; i<vectors.features.length; i++) {
		array.push(vectors.features[i].attributes.gid);
	};

	jQuery('#plots-tables').datagrid({
		url: 'index.php?map-rpc=allowable-datagrid',
		rpcParams: [{
			layer_id: layer_id,
			layer_type: 'layer_allowable',
			gids: array
		}],
		singleSelect: true,
        checkOnSelect: false,
        selectOnCheck: false,
		iconCls: 'icon-plot',
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		pageSize: 20,
		fit: true,
		showFooter: true,
        sortName: 'gid',
        sortOrder: 'asc',
		idField: 'gid',
		pagination: true,
		rownumbers: true,
		width: '100%',
		border: false,
		frozenColumns: [[
				{
					field: 'ck',
					checkbox: true
				}
			]],
		columns: [[
				{
					field: 'ident',
					title: '<b>Идентификатор</b>',
					sortable: false,
					width: 110
				},
				{
					field: 'elgarea',
					title: '<b>Площ(ха)</b>',
					sortable: true,
					width: 80
				},
				{
					field: 'area_zp',
					title: '<b>Землище</b>',
					sortable: false,
					width: 125
				},
				{
					field: 'fbident',
					title: '<b>Физически блок</b>',
					sortable: true,
					width: 120
				},
				{
					field: 'fb_area',
					title: '<b>Площ на физически<br/> блок(ха)</b>',
					sortable: true,
					width: 130
				},
			]],
		onLoadSuccess: function() {
			jQuery('#plots-tables').datagrid('uncheckAll');
            jQuery('#select-filtered-plots').trigger('change');
		},
		onUnselect: function(rowIndex, rowData) {

		},
		onSelect: function(rowIndex, rowData) {
			checkedElement = rowData;
			displayFeatureSelection(rowData.st_astext, rowData);
		},
		toolbar: '#attr-tables-toolbar',
		onBeforeLoad: function() {
			jQuery('#plots-tables').datagrid('clearChecked');
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}
