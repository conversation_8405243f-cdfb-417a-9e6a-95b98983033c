function initWorkLayerGrid(layer_id) {
	var checkedElement;
	var gids_arr = [];

    jQuery('#att-tables-multi-edit-btn').hide();

	for (var i = 0; i < vectors.features.length; i++) {
		gids_arr.push(vectors.features[i].attributes.gid);
	}

	jQuery("#plots-tables").datagrid({
        url: "index.php?map-rpc=work-layer-datagrid",
        rpcParams: [
            {
                layer_id: layer_id,
                layer_type: LAYER_TYPE_WORK_LAYER,
                gids: gids_arr
            }
        ],
        singleSelect: true,
        checkOnSelect: false,
        selectOnCheck: false,
        iconCls: "icon-plot",
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 20,
        fitColumns: true,
        showFooter: true,
        sortName: "gid",
        sortOrder: "asc",
        idField: "gid",
        pagination: true,
        rownumbers: true,
        border: false,
        frozenColumns: [
            [
                {
                    field: "ck",
                    checkbox: true
                }
            ]
        ],
        onUnselect: function(rowIndex, rowData) {},
        onSelect: function(rowIndex, rowData) {
            checkedElement = rowData;
            displayFeatureSelection(rowData.st_astext, rowData);
        },
        onLoadSuccess: function(data) {
            pageNumber = jQuery(this).datagrid('options').pageNumber;
            if(attrInfoForLayerType === LAYER_TYPE_WORK_LAYER) {
                if(!data || !data.columns || !data.columns[0]){
                    jQuery.messager.alert('Грешка', 'Няма намерени парцели.');
                    return;
                }
                var clearFilter = jQuery("#plots-tables").datagrid('options').rpcParams[0].clear_filter;
                populateWorkLayerFilters(data.columns[0], clearFilter === true);
            } else {
                if(data.total !== undefined && data.total === 0){
                    jQuery.messager.alert('Внимание', 'Не са открити записи.', 'warning');
                }
            }
            jQuery('#main-kvs-table-layout').layout('resize');
        },
        toolbar: "#attr-tables-toolbar",
        onBeforeLoad: function() {
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function populateWorkLayerFilters(columns, force = false) {
    var html = '';
    columns.forEach(function (col) {
        if(!force && jQuery('#work-filter-' + col.field).length > 0) return;
        if (col.field !== 'area') {
            html += col.title + '<br/>' + '<input id="work-filter-' + col.field + '" type="text" style="width: 150px;" data-title="' + col.field + '"/> <br/>';
        }
    });
    if(html.length > 0){
        jQuery('#work-layer-filter-fields').html(html);
    }
}
