function initISAKGrid(layer_id)
{
    var checkedElement;

    var array = [];

    for (var i=0; i<vectors.features.length; i++) {
        array.push(vectors.features[i].attributes.gid);
    }
    var isakFilterEkatte = jQuery('#isak-filter-ekate');

    if (isakFilterEkatte.data().hasOwnProperty('combobox')){
        isakFilterEkatte.combobox('loadData',ComboboxData.IsakEkatteCombobox[attrInfoForLayerName] || []);
    }else{
        isakFilterEkatte.combobox({
            data: ComboboxData.IsakEkatteCombobox[attrInfoForLayerName] || [],
            valueField: 'ekate',
            textField: 'text',
            filter: function(q, row) {
                var opts = jQuery(this).combobox('options');
                var text = row[opts.textField].toLowerCase();
                var value = row[opts.valueField];
                var find = q.toLowerCase();
                if (text.indexOf(find) != -1 || value.indexOf(find) != -1)
                {
                    return true;
                }
            }
        });
    }
    jQuery('#plots-tables').datagrid({
        url: 'index.php?map-rpc=isak-datagrid',
        rpcParams: [{
            layer_id: layer_id,
            layer_type: 6,
            gids: array
        }],
        singleSelect: true,
        checkOnSelect: false,
        selectOnCheck: false,
        iconCls: 'icon-plot',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 20,
        fit: true,
        showFooter: true,
        sortName: 'prc_uin',
        sortOrder: 'asc',
        idField: 'gid',
        pagination: true,
        rownumbers: true,
		border: false,
        frozenColumns: [[
                {
                    field: 'ck',
                    checkbox: true,
                }
            ]],
        columns: [[
                {
                    field: 'prc_uin',
                    title: '<b>Идентификатор</b>',
                    sortable: true,
                    width: 120
                },
                {
                    field: 'area',
                    title: '<b>Площ(ха)</b>',
                    sortable: true,
                    width: 70
                },
                {
                    field: 'watering',
                    title: '<b>Напояване</b>',
                    sortable: true,
                    width: 80
                },
                {
                    field: 'culture',
                    title: '<b>Култура</b>',
                    sortable: true,
                    width: 190
                },
                {
                    field: 'ekatte',
                    title: '<b>ЕКАТТЕ</b>',
                    sortable: true,
                    width: 50
                }
            ]],
        onUnselect: function(rowIndex, rowData) {

        },
        onSelect: function(rowIndex, rowData) {
            checkedElement = rowData;
            displayFeatureSelection(rowData.st_astext, rowData);
        },
        toolbar: '#attr-tables-toolbar',
        onBeforeLoad: function() {
            jQuery(this).datagrid('clearChecked');
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}
