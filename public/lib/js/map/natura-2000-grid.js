function initNatura_2000_Grid(layer_id)
{
	var checkedElement;
	var array = [];

	for (var i=0; i<vectors.features.length; i++) {
		array.push(vectors.features[i].attributes.gid);
	};

	jQuery('#plots-tables').datagrid({
		url: 'index.php?map-rpc=remote-layer-datagrid',
		rpcParams: [{
			layer_id: layer_id,
			layer_type: 11,
			gids: array
		}],
		singleSelect: true,
        checkOnSelect: false,
        selectOnCheck: false,
		iconCls: 'icon-plot',
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		pageSize: 20,
		fit: true,
		showFooter: true,
        sortName: 'gid',
		sortOrder: 'asc',
		idField: 'gid',
		pagination: true,
		rownumbers: true,
		width: '100%',
		border: false,

		frozenColumns: [[
				{
					field: 'ck',
					checkbox: true
				}
			]],
		columns: [[
				{
					field: 'zone_name',
					title: '<b>Име на зона</b>',
					sortable: true,
					width: 135
				},
				{
					field: 'area_z_dka',
					title: '<b>Площ(дка)</b>',
					sortable: true,
					width: 90
				},
				{
					field: 'zapoved_no',
					title: '<b>Заповед за <br/>обявяване на зоната</b>',
					sortable: true,
					width: 130
				},
				{
					field: 'dv',
					title: '<b>Държавен вестник</b>',
					sortable: true,
					width: 140
				},
				{
					field: 'sitecode',
					title: '<b> Код на зоната</b>',
					sortable: true,
					width: 90
				},
				{
					field: 'bans',
					title: '<b>Подпомагани забрани</b>',
					sortable: true,
					width: 775
				},
			]],
		onLoadSuccess: function() {
			jQuery('#plots-tables').datagrid('uncheckAll');
            jQuery('#select-filtered-plots').trigger('change');
		},
		onUnselect: function(rowIndex, rowData) {

		},
		onSelect: function(rowIndex, rowData) {
			checkedElement = rowData;
			displayFeatureSelection(rowData.st_astext, rowData);
		},
		toolbar: '#attr-tables-toolbar',
		onBeforeLoad: function() {
			jQuery('#plots-tables').datagrid('clearChecked');
		},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}
