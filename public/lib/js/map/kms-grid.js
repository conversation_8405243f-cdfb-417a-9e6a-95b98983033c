function initKmsGrid(layer_id) {

    jQuery('#kms-filter-ekate').combobox({
        url: 'index.php?common-rpc=kms-ekate-combobox',
        rpcParams: [{
            layer_id: layer_id
        }],
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    var checkedElement;

    var array = [];
    for (var i = 0; i < vectors.features.length; i++) {
        array.push(vectors.features[i].attributes.gid);
    };

    jQuery('#plots-tables').datagrid({
        url: 'index.php?map-rpc=kms-datagrid',
        rpcParams: [{
            layer_id: layer_id,
            layer_type:4,
            gids: array

        }],
        singleSelect: true,
        checkOnSelect: false,
        selectOnCheck: false,
        iconCls: 'icon-plot',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 20,
        fit: true,
        showFooter: true,
        sortName: 'ekatte',
        sortOrder: 'desc',
        idField: 'gid',
        pagination: true,
        rownumbers: true,
        border: false,
        frozenColumns: [[
                {
                    field: 'ck',
                    checkbox: true
                }
            ]],
        columns: [[
                {
                    field: 'name',
                    title: '<b>Номер БЗЗ</b>',
                    sortable: true,
                    width: 75
                },
                {
                    field: 'ekatte',
                    title: '<b>Землище</b>',
                    sortable: true,
                    width: 150
                },
                {
                    field: 'crop_code',
                    title: '<b>Земеделска култура</b>',
                    sortable: true,
                    width: 150
                },
                {
                    field: 'area',
                    title: '<b>Площ (дка)</b>',
                    sortable: true,
                    width: 77
                }
            ]],

        onUnselect: function(rowIndex, rowData) {
        },
        onSelect: function(rowIndex, rowData) {
            checkedElement = rowData;
            displayFeatureSelection(rowData.st_astext, rowData);
        },
        toolbar: '#attr-tables-toolbar',
        onBeforeLoad: function () {
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

}
