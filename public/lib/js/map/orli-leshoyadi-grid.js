function initVPSOrliLeshoyadiGrid(layer_id)
{
	var checkedElement;
    var array = [];

	for (var i=0; i<vectors.features.length; i++) {
		array.push(vectors.features[i].attributes.gid);
	};

	jQuery('#plots-tables').datagrid({
		url: 'index.php?map-rpc=layer-vps-orli-leshoyadi-datagrid',
		rpcParams: [{
			layer_id: layer_id,
			layer_type: 17,
			gids: array
		}],
		singleSelect: true,
        checkOnSelect: false,
        selectOnCheck: false,
		iconCls: 'icon-plot',
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		pageSize: 20,
		fit: true,
		showFooter: true,
        sortName: 'gid',
		sortOrder: 'asc',
		idField: 'gid',
		pagination: true,
		rownumbers: true,
		width: '100%',
		border: false,

		frozenColumns: [[
				{
					field: 'ck',
					checkbox: true
				}
			]],
		columns: [[
				{
					field: 'blockuin',
					title: '<b>Номер на <br/>физически блок </b>',
					sortable: true,
					width: 105
				},
				{
					field: 'ekatte_name',
					title: '<b>Землище </b>',
					sortable: true,
					width: 200
				},
				{
					field: 'ntp',
					title: '<b>НТП</b>',
					sortable: false,
					width: 35
				},
				{
					field: 'vps_type',
					title: '<b>Вид ВПС</b>',
					sortable: false,
					width: 330
				},
			]],
		onLoadSuccess: function() {
			jQuery('#plots-tables').datagrid('uncheckAll');
            jQuery('#select-filtered-plots').trigger('change');
		},
		onUnselect: function(rowIndex, rowData) {

		},
		onSelect: function(rowIndex, rowData) {
			checkedElement = rowData;
			zoomToFeatureSelection(rowData.st_astext);
		},
		toolbar: '#attr-tables-toolbar',
		onBeforeLoad: function() {
			jQuery('#plots-tables').datagrid('clearChecked');
		},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}
