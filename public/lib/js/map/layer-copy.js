function initCopyLayerFields() {
    jQuery('#layer-farming-from > input').combobox({
        editable: false,
    	url: 'index.php?common-rpc=farming-combobox',
    	valueField: 'id',
    	textField: 'name',
    	onLoadSuccess: function() {
    		var farmingData = jQuery('#layer-farming-from > input').combobox('getData');
    		if(farmingData[0]) {
    			jQuery('#layer-farming-from > input').combobox('setValue', farmingData[0]['id']);
    			jQuery('#layer-year-from > input').combobox({
    		    	url: 'index.php?common-rpc=farming-year-combobox',
    		    	valueField: 'id',
    		    	textField: 'title',
    		    	onLoadSuccess: function() {
    		    		var yearData = jQuery('#layer-year-from > input').combobox('getData');
    		    		if(yearData[0]) {
    		    			jQuery('#layer-year-from > input').combobox('setValue', yearData[0]['id']);
    		    		}
    		    		loadDefaultValues(farmingData, yearData);
    		    	},
                    loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                    loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    		    });
    		}
    	},
    	onSelect: function(record) {
    		var farming = record.id
    		jQuery('#layer-year-from > input').combobox({
    	    	url: 'index.php?common-rpc=farming-year-combobox',
    	    	valueField: 'id',
    	    	textField: 'title',
    	    	onSelect: function(record) {
    	    		jQuery('#layer-name-from > input').combobox({
	    	    		url: 'index.php?common-rpc=copy-layer-combobox',
                        rpcParams: [{
                            farming: farming,
                            farming_year: record.id
                        }],
	        	    	valueField: 'id',
	        	    	textField: 'name',
                        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    	    		});
    	    	},
                loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    	    });
    	},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#layer-farming-to > input').combobox({
        editable: false,
    	url: 'index.php?common-rpc=farming-combobox',
    	valueField: 'id',
    	textField: 'name',
    	onLoadSuccess: function() {
    		var getData = jQuery('#layer-farming-to > input').combobox('getData');
    		if(getData[0]) {
    			jQuery('#layer-farming-to > input').combobox('setValue', getData[0]['id'])
	    		jQuery('#layer-year-to > input').combobox({
	    	    	url: 'index.php?common-rpc=farming-year-combobox',
	    	    	valueField: 'id',
	    	    	textField: 'title',
	    	    	onLoadSuccess: function() {
	    	    		var getData = jQuery('#layer-year-to > input').combobox('getData');
	    	    		if(getData[0]) {
	    	    			jQuery('#layer-year-to > input').combobox('setValue', getData[0]['id']);
	    	    		}
	    	    	},
                    loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                    loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	    		});
    		}
    	},
    	onSelect: function(record) {
    		var farming = record.id
    		jQuery('#layer-year-to > input').combobox({
    	    	url: 'index.php?common-rpc=farming-year-combobox',
    	    	valueField: 'id',
    	    	textField: 'title',
                loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    		});
    	},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#layer-year-from > input').combobox({editable: false});
    jQuery('#layer-name-from > input').combobox({editable: false});
    jQuery('#layer-year-to > input').combobox({editable: false});
}

function initCopyLayerToTMPFields() {
	jQuery('#layertmp-farming-from > input').combobox({
    	url: 'index.php?common-rpc=farming-combobox',
    	valueField: 'id',
    	textField: 'name',
    	onLoadSuccess: function() {
    		var farmingData = jQuery('#layertmp-farming-from > input').combobox('getData');
    		if(farmingData[0]) {
    			jQuery('#layertmp-farming-from > input').combobox('setValue', farmingData[0]['id']);
    			jQuery('#layertmp-year-from > input').combobox({
    		    	url: 'index.php?common-rpc=farming-year-combobox',
    		    	valueField: 'id',
    		    	textField: 'title',
                    onLoadSuccess: function() {
	    	    		var getData = jQuery('#layertmp-year-from > input').combobox('getData');
	    	    		if(getData[0]) {
	    	    			jQuery('#layertmp-year-from > input').combobox('setValue', getData[0]['id']);
	    	    		}
	    	    	},
                    loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                    loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    		    });
    		}
    	},
    	onSelect: function(record) {
    		var farming = record.id;
    		jQuery('#layertmp-year-from > input').combobox({
    	    	url: 'index.php?common-rpc=farming-year-combobox',
    	    	valueField: 'id',
    	    	textField: 'title',
    	    	onSelect: function(record) {
    	    		jQuery('#layertmp-name-from > input').combobox({
	    	    		url: 'index.php?common-rpc=copy-layer-combobox',
                        rpcParams: [{
                            farming: farming,
                            farming_year: record.id
                        }],
	        	    	valueField: 'id',
	        	    	textField: 'name',
                        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    	    		});
    	    	},
                loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    	    });
    	},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

	jQuery('#layertmp-farming-from > input').combobox({editable: false});
    jQuery('#layertmp-year-from > input').combobox({editable: false});
}

function loadDefaultValues(farmingData, yearData) {
	var farming = farmingData[0]['id'];
	var year = yearData[0]['id'];
    jQuery('#layer-name-from > input').combobox({
		url: 'index.php?common-rpc=copy-layer-combobox',
        rpcParams: [{
            farming: farming,
            farming_year: year
        }],
    	valueField: 'id',
    	textField: 'name',
    	onLoadSuccess: function() {
    		var getData = jQuery('#layer-name-from > input').combobox('getData');
    		if(getData[0]) {
    			jQuery('#layer-name-from > input').combobox('setValue', getData[0]['id']);
    		}
    	},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function validateBeforeSubmit() {
	if(!jQuery('#layer-name-from > input').combobox('getValue')) {
		jQuery.messager.alert('Грешка', 'Моля попълнете всички полета');
		return false;
	}
	if(!jQuery('#layer-year-to > input').combobox('getValue')) {
		jQuery.messager.alert('Грешка', 'Моля попълнете всички полета');
		return false;
	}
}

function validateTMPBeforeSubmit() {
	if(!jQuery('#layertmp-farming-from > input').combobox('getValue')) {
		jQuery.messager.alert('Грешка', 'Моля попълнете всички полета');
		return false;
	}
	if(!jQuery('#layertmp-year-from > input').combobox('getValue')) {
		jQuery.messager.alert('Грешка', 'Моля попълнете всички полета');
		return false;
	}
}
