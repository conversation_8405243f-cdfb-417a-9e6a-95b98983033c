var NOT_SELECTED_CLIPPING_LAYER = 0;
var SELECTED_CLIPPING_LAYER_A = 1;
var SELECTED_CLIPPING_LAYER_B = 2;

var OPERATION_CUT = 0;
var OPERATION_SPLIT = 1;
var OPERATION_DELETE = 2;

function onLayerClipButtonClick() {

	var options = jQuery(this).linkbutton('options');
	if (options.disabled) {
		return false;
	}

	//get checked and selected
	var combotree = jQuery('#all-layers-tree').combotree('tree');
    var getChecked = combotree.tree('getChecked');
    var getSelected = combotree.tree('getSelected');

    if(!getSelected) {
		jQuery.messager.alert('Грешка', 'Трябва да сте селектирали поне един слой от "Активен слой"!','warning');
		return false;
	}

	//get layersA and layersB checked items
	var layersAItems = [];
	var layersBItems = [];
	for (var i=0; i < getChecked.length; i++) {

		if(!getChecked[i].children)
		{
			var getParent = combotree.tree('getParent', getChecked[i].target);

			getCheckedLayers(getChecked[i], layersAItems, getParent.text, [LAYER_TEMP_DATA, LAYER_FOR_ISAK_PLOTS, LAYER_TYPE_WORK_LAYER], false);
			getCheckedLayers(getChecked[i], layersBItems, getParent.text, [LAYER_TEMP_DATA, LAYER_FOR_ISAK_PLOTS, LAYER_TYPE_WORK_LAYER], true);
		}
	}

	if(layersAItems.length == 0 || layersBItems.length == 0) {
		jQuery.messager.alert('Грешка', 'Трябва да сте избрали поне два слоя от "Активен слой" като единият от тях задължително трябва да е слой "Временни данни", "Работен слой" или "За ИСАК"','warning');
		return false;
	}

	jQuery('#layer-A').combobox('loadData', layersAItems);
	jQuery('#layer-B').combobox('loadData', layersBItems);

	jQuery("#win-clipping").window({
	  left:36,
	  top: 70
	});
	jQuery('#win-clipping').window('open');
	vectors.events.register("featureremoved", this, function(e) {

		var currentGid = e.feature.attributes.gid;
		var currentId = e.feature.attributes.id;

		var featuresA = featuresLayerA;
		var featuresB = featuresLayerB;

		if(typeof currentGid !== 'undefined' && featuresA.length > 0 && featuresA.indexOf(currentGid) != -1) {
			featuresLayerA.splice(featuresA.indexOf(currentGid), 1);

			jQuery('#layer-A-count').text(featuresA.length);
		}
		if(typeof currentId !== 'undefined' && featuresA.length > 0 && featuresA.indexOf(currentId) != -1) {
			featuresLayerA.splice(featuresA.indexOf(currentId), 1);

			jQuery('#layer-A-count').text(featuresA.length);
		}

		if(typeof currentGid !== 'undefined' && featuresB.length > 0 && featuresB.indexOf(currentGid) != -1) {
			featuresLayerB.splice(featuresB.indexOf(currentGid), 1);

			jQuery('#layer-B-count').text(featuresB.length);
		}
		if(typeof currentId !== 'undefined' && featuresB.length > 0 && featuresB.indexOf(currentId) != -1) {
			featuresLayerB.splice(featuresB.indexOf(currentId), 1);

			jQuery('#layer-B-count').text(featuresB.length);
		}
	});

	resetCountsAndVars();
	controlSelect.deactivate();
	vectors.removeAllFeatures();
	setStrokeColorStyle();

	jQuery('#tool-draw').linkbutton('disable');
	jQuery('#tool-select').linkbutton('disable');
	jQuery('#tool-edit-geometry').linkbutton('disable');
	jQuery('#tool-delete').linkbutton('disable');
	jQuery('#tool-draw-hole').linkbutton('disable');
	jQuery('#tool-merge').linkbutton('disable');
	jQuery('#tool-remove-holes').linkbutton('disable');
	jQuery('#tool-split').linkbutton('disable');
	jQuery('#tool-auto-split').linkbutton('disable');

	var layerBCombobox = jQuery('#layer-B');
	var layerBValue = layerBCombobox.combobox('getValue');
	var layerAValue = jQuery('#layer-A').combobox('getValue');

	if(layerBValue === layerAValue){
		for (var i = 0; i < layersBItems.length; i++) {
			if(layersBItems[i].value != layerBValue) {
				layerBCombobox.combobox('select', layersBItems[i].value);
				return;
			}
		}
	}
}

function resetCountsAndVars() {
	jQuery('#layer-A-count').text(0);
	jQuery('#layer-B-count').text(0);

	featuresLayerA = [];
	featuresLayerB = [];

	jQuery('#layer-A-tool-select').linkbutton('unselect');
	jQuery('#layer-B-tool-select').linkbutton('unselect');
}

function resetCountsAndVarsA() {

	jQuery('#layer-A-count').text(0);

	featuresLayerA = [];

	jQuery('#layer-A-tool-select').linkbutton('unselect');
}

function resetCountsAndVarsB() {

	jQuery('#layer-B-count').text(0);

	featuresLayerB = [];

	jQuery('#layer-B-tool-select').linkbutton('unselect');
}

function initClipTools() {

	jQuery('#layer-A-tool-select').bind('click', toolSelectLayerA);
	jQuery('#layer-B-tool-select').bind('click', toolSelectLayerB);
}

_clippingZoom = false;
var _selectedLayer = NOT_SELECTED_CLIPPING_LAYER;
var featuresLayerA = [];
var featuresLayerB = [];

function toolSelectLayerA() {
	_selectedLayer = SELECTED_CLIPPING_LAYER_A;
	//remove all previous selections and drawed polygons
	map.events.unregister('click', map, propertyWindowFunction);
	//deactivate control modify bofore removing features
	//if control is still active it will trigger an error
	controlModify.deactivate();

	unselectAll();
	//select button
	jQuery('#layer-A-tool-select').linkbutton('select');
	jQuery('#layer-B-tool-select').linkbutton('unselect');

	var layer_name = jQuery('#layer-A').combobox('getValue');

	//init active layer for polygon selection
	var layer_map_index = getLayerIndexByName(layer_name);
	if (layer_map_index) {
		initPolygonSelectControl(layer_map_index);
	}

	//Allow selection of multiple geometries.
	controlSelect.multiple = true;

	chooseControl('selectfeature');

}

function setStrokeColorStyle() {
	//set StrokeColor style
	var style = OpenLayers.Util.extend( {}, VECTOR_LAYER_STYLE);
	style.strokeColor = '${getStrokeColor}';
	var olStyle = new OpenLayers.Style( style, {
	  context: {
	    getStrokeColor: function(feature) {
	    	if(_clippingZoom == true) {
	    		var feature_id;
	    		if (feature.attributes.gid){
	    			feature_id = feature.attributes.gid;
	    		} else if(feature.attributes.id){
	    			feature_id = feature.attributes.id;
	    		}
	    		if(jQuery.inArray(feature_id,featuresLayerA) != -1) {
	    			return '#f73838';
	    		}else if(jQuery.inArray(feature_id,featuresLayerB) != -1) {
	    			return '#00ff00';
	    		} else {
	    			_clippingZoom = false;
	    		}
	    	}
	    	if(_clippingZoom == false) {
		        if (_selectedLayer == SELECTED_CLIPPING_LAYER_A) {

		        	if (feature.attributes.gid && featuresLayerA.indexOf(feature.attributes.gid) == -1) {
						featuresLayerA.push(feature.attributes.gid);
					}
					else if (featuresLayerA.indexOf(feature.attributes.id) == -1){
						featuresLayerA.push(feature.attributes.id);
					}
		        	jQuery('#layer-A-count').text(featuresLayerA.length);
			        return '#f73838';
		        }
		        if (_selectedLayer == SELECTED_CLIPPING_LAYER_B) {

		        	if (feature.attributes.gid && featuresLayerB.indexOf(feature.attributes.gid) == -1) {
						featuresLayerB.push(feature.attributes.gid);
					}
					else if (featuresLayerB.indexOf(feature.attributes.id) == -1){
						featuresLayerB.push(feature.attributes.id);
					}
		        	jQuery('#layer-B-count').text(featuresLayerB.length);
			        return '#00ff00';
		        }
	    	}
	    },
	    label: function (feature) {
			if(!feature.attributes.hasOwnProperty('label')) {
				return '';
			}
			return feature.attributes.label;
		}
	  }
	});
	var styleMap = new OpenLayers.StyleMap({ default: olStyle });
	vectors.styleMap = styleMap;
}

function helpCutClipping () {
	jQuery('#win-operation-cut-help').window('open');
}

function helpSplitClipping () {
	jQuery('#win-operation-split-help').window('open');
}

function helpDeleteClipping () {
	jQuery('#win-operation-delete-help').window('open');
}

function removeFeaturesA() {

	var featuresA = featuresLayerA;

	for (i = 0; i < featuresA.length; i++)
	{
		var featureGID = vectors.getFeaturesByAttribute('gid',featuresA[i]);
		var featureID = vectors.getFeaturesByAttribute('id',featuresA[i]);

		if(featureGID.length > 0) {
			vectors.removeFeatures(featureGID[0]);
		}

		if(featureID.length > 0) {
			vectors.removeFeatures(featureID[0]);
		}
	}
}

function removeFeaturesB() {

	var featuresB = featuresLayerB;

	for (i = 0; i < featuresB.length; i++)
	{
		var featureGID = vectors.getFeaturesByAttribute('gid',featuresB[i]);
		var featureID = vectors.getFeaturesByAttribute('id',featuresB[i]);

		if(featureGID.length > 0) {
			vectors.removeFeatures(featureGID[0]);
		}

		if(featureID.length > 0) {
			vectors.removeFeatures(featureID[0]);
		}
	}
}

_selectedLayerTypeA = 0;

jQuery(function () {

	jQuery('#operation-cut-help').bind('click', helpCutClipping);
	jQuery('#operation-split-help').bind('click', helpSplitClipping);
	jQuery('#operation-delete-help').bind('click', helpDeleteClipping);

	jQuery('#win-clipping').window({
		onClose: function () {
		var olStyle = new OpenLayers.Style(VECTOR_LAYER_STYLE, {
			context: {
				label: function (feature) {
					return '';
				}
			}
		});
		var styleMap = new OpenLayers.StyleMap({ default: olStyle });
		vectors.styleMap = styleMap;
			vectors.removeAllFeatures();
	        unselectAll();
	        deactivateAllControls();
	        chooseControl('navigation');

	        var combotree = jQuery('#all-layers-tree').combotree('tree');
    		var activeLayer = combotree.tree('getSelected');

	        //init active layer for polygon selection
			var layer_map_index = getLayerIndexByName(activeLayer.attributes.layer_name);
			if (layer_map_index) {
				initPolygonSelectControl(layer_map_index);
			}

	        toggleGeometryTools(activeLayer.attributes.layer_type);
	        jQuery('#tool-draw').linkbutton('enable');
	        jQuery('#tool-select').linkbutton('enable');
	        jQuery('#tool-edit-geometry').linkbutton('enable');
	        jQuery('#tool-delete').linkbutton('enable');
	        jQuery('#tool-draw-hole').linkbutton('enable');
	        jQuery('#tool-merge').linkbutton('enable');
	        jQuery('#tool-remove-holes').linkbutton('enable');
	        jQuery('#tool-split').linkbutton('enable');

	        checkToolCrossLayers();
	    }
	});

	jQuery('#layer-A').combobox({
		valueField: 'value',
		textField: 'name',
		editable: false,
		required: true,
		onSelect: function(node) {

			if(node.value == jQuery('#layer-B').combobox('getValue')){
				jQuery.messager.alert('Грешка', 'Не може да сте избрали един и същи слой А и слой Б!','warning');
				jQuery('#layer-A').combobox('unselect',node.value);
                return false;
			}

			_selectedLayerTypeA = node.layer_type;

			//remove all previous selections and drawed polygons
			map.events.unregister('click', map, propertyWindowFunction);
			//deactivate control modify bofore removing features
			//if control is still active it will trigger an error
			controlModify.deactivate();
			//remove features and unselect buttons
			//vectors.removeAllFeatures();
			removeFeaturesA();
			unselectAll();

			controlSelect.deactivate();
		}
	});

	jQuery('#layer-B').combobox({
		valueField: 'value',
		textField: 'name',
		editable: false,
		required: true,
		onSelect: function(node) {
			if(node.value == jQuery('#layer-A').combobox('getValue')){
				jQuery.messager.alert('Грешка', 'Не може да сте избрали един и същи слой А и слой Б!','warning');
				jQuery('#layer-B').combobox('unselect',node.value);
				return false;
			}
			//remove all previous selections and drawed polygons
			map.events.unregister('click', map, propertyWindowFunction);
			//deactivate control modify bofore removing features
			//if control is still active it will trigger an error
			controlModify.deactivate();
			//remove features and unselect buttons
			//vectors.removeAllFeatures();
			removeFeaturesB();

			unselectAll();

			controlSelect.deactivate();
		}
	});

	jQuery('#btn-save-clipping').bind('click', function() {

		if(!jQuery('#layer-A').combobox('getValue') || !jQuery('#layer-B').combobox('getValue')) {
                    jQuery.messager.alert('Грешка', 'Трябва да сте избрали слой А и слой Б!','warning');
                    return false;
		}

        if (featuresLayerA.length == 0) {
            jQuery.messager.alert('Грешка', 'Трябва да сте избрали поне по един парцел от слой А!', 'warning');
            return false;
        }

		var obj = new Object();
		obj.layerDataA = jQuery('#layer-A').combobox('getData');
		obj.featuresGidA = featuresLayerA;
		obj.getSelectedA = jQuery('#layer-A').combobox('getValue');

		obj.layerDataB = jQuery('#layer-B').combobox('getData');
		obj.featuresGidB = featuresLayerB;
		obj.getSelectedB = jQuery('#layer-B').combobox('getValue');

		//Изрязване
		if (jQuery('#layer-operation-cut input').is(':checked'))
		{
			obj.operation = OPERATION_CUT;
		}
		//Разцепване
		if (jQuery('#layer-operation-split input').is(':checked'))
		{
			obj.operation = OPERATION_SPLIT;
		}
		//Изтриване
		if (jQuery('#layer-operation-delete input').is(':checked'))
		{
			obj.operation = OPERATION_DELETE;
		}

		TF.Rpc.Map.MapTools.saveClipping(obj)
		.done(function (data) {
			reloadLayerByName(jQuery('#layer-A').combobox('getValue'));
			vectors.removeAllFeatures();
		})
		.fail(function (errorObj) {
			jQuery.messager.alert('Грешка',errorObj.getMessage(),'warning');
		});

	});

	//Click на Изрязване
	jQuery('#layer-operation-cut input').on('change', function() {
	    if (this.checked) {
	        jQuery('#operation-split-image').hide();
	        jQuery('#operation-delete-image').hide();
	        jQuery('#operation-cut-image').show();
	        jQuery('#operation-text-clipping').html('Изрязване');
	    }
	});
	//Click на Разцепване
	jQuery('#layer-operation-split input').on('change', function() {
	    if (this.checked) {
	        jQuery('#operation-cut-image').hide();
	        jQuery('#operation-delete-image').hide();
	        jQuery('#operation-split-image').show();
	        jQuery('#operation-text-clipping').html('Разцепване');
	    }
	});
	//Click на Отрязване
	jQuery('#layer-operation-delete input').on('change', function() {
	    if (this.checked) {
	        jQuery('#operation-cut-image').hide();
	        jQuery('#operation-split-image').hide();
	        jQuery('#operation-delete-image').show();
	        jQuery('#operation-text-clipping').html('Отрязване');
	    }
	});

});

function toolSelectLayerB() {

	_selectedLayer = SELECTED_CLIPPING_LAYER_B;
	//remove all previous selections and drawed polygons
	map.events.unregister('click', map, propertyWindowFunction);
	//deactivate control modify bofore removing features
	//if control is still active it will trigger an error
	controlModify.deactivate();

	unselectAll();
	//select button
	jQuery('#layer-B-tool-select').linkbutton('select');
	jQuery('#layer-A-tool-select').linkbutton('unselect');

	var layer_name = jQuery('#layer-B').combobox('getValue');

	if(layer_name.length == 0) {
		jQuery.messager.alert('Грешка', 'Моля изберете слой Б!','warning');
		return false;
	}

	//init active layer for polygon selection
	var layer_map_index = getLayerIndexByName(layer_name);
	if (layer_map_index) {
		initPolygonSelectControl(layer_map_index);
	}

	//Allow selection of multiple geometries.
	controlSelect.multiple = true;

	chooseControl('selectfeature');
	if (MANUAL_SELECT_LAYERS.indexOf(layer_name) != -1) {
		controlSelect.handlers.box.deactivate(); //Deactivating drag selection.
	} else {
		controlSelect.handlers.box.activate(); //For all other layers activate it.
	}
}

/**
 * [getCheckedLayers description]
 * @param  {[Array]}
 * @param  {[Array]}
 * @param  {[String]}
 * @param  {[Array]}
 * @param  {[Boolean]}
 * @return {}
 */
function getCheckedLayers(node, layers, path, onlyLayers, isBItems) {
	var id_name = 'gid';

	if(node.checked && node.attributes.layer_type) {

		if (node.attributes.layer_type == LAYER_ZP_PLOTS) {
			id_name = 'id';
		}
		else {
			id_name = 'gid';
		}

		if (node.attributes.farming) {
			var comboItem = {
				name: node.attributes.farming + '/' + path + '/' + node.attributes.name,
				value: node.attributes.layer_name,
				layer_type: node.attributes.layer_type,
				layer_id: node.id,
				id_name: id_name
			};
		}else {
			if (onlyLayers.indexOf(LAYER_TEMP_DATA) != -1 || onlyLayers.indexOf(LAYER_FOR_ISAK_PLOTS) != -1  || onlyLayers.indexOf(LAYER_TYPE_WORK_LAYER) != -1) {
				var comboItem = {
					name: path + '/' + node.attributes.name,
					value: node.attributes.layer_name,
					selected : true,
					layer_type: node.attributes.layer_type,
					layer_id: node.id,
					id_name: id_name
				};
			}else{
				var comboItem = {
					name: path + '/' + node.attributes.name,
					value: node.attributes.layer_name,
					layer_type: node.attributes.layer_type,
					layer_id: node.id,
					id_name: id_name
				};
			}
		}

		if (onlyLayers && onlyLayers.length) {
			if (isBItems) {
				layers.push(comboItem);
			} else {
				if (onlyLayers.indexOf(node.attributes.layer_type) != -1) {
					layers.push(comboItem);
				}
			}
			return;
		}

		layers.push(comboItem);
	}
}
