function initFORISAKGrid(layer_id)
{
	var checkedElement;
	var array = [];

	for (var i=0; i<vectors.features.length; i++) {
		array.push(vectors.features[i].attributes.gid);
	}
    var forIsakFilterEkatte = jQuery('#for-isak-filter-ekate');

    if (forIsakFilterEkatte.data().hasOwnProperty('combobox')){
        forIsakFilterEkatte.combobox('loadData',ComboboxData.ForIsakEkateCombobox[attrInfoForLayerName] || []);
    }else{
        forIsakFilterEkatte.combobox({
            data: ComboboxData.ForIsakEkateCombobox[attrInfoForLayerName] || [],
            valueField: 'ekate',
            textField: 'text',
            filter: function(q, row) {
                var opts = jQuery(this).combobox('options');
                var text = row[opts.textField].toLowerCase();
                var value = row[opts.valueField];
                var find = q.toLowerCase();
                if (text.indexOf(find) != -1 || value.indexOf(find) != -1)
                {
                    return true;
                }
            }
        });
    }
	jQuery('#plots-tables').datagrid({
		url: 'index.php?map-rpc=for-isak-datagrid',
		rpcParams: [{
			layer_id: layer_id,
			layer_type: 9,
			gids: array
		}],
		singleSelect: true,
        checkOnSelect: false,
        selectOnCheck: false,
		iconCls: 'icon-plot',
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		pageSize: 20,
		fit: true,
		showFooter: true,
		sortName: 'prc_uin',
		sortOrder: 'asc',
		idField: 'gid',
		pagination: true,
		rownumbers: true,
		width: '100%',
		border: false,
		frozenColumns: [[
				{
					field: 'ck',
					checkbox: true
				}
			]],
		columns: [[
				{
                    field: 'land',
                    title: '<b>Землище</b>',
                    sortable: false,
                    width: 100
                },{
					field: 'prc_name',
					title: '<b>Име на парцел</b>',
					sortable: true,
					width: 150
				},{
                    field: 'cropname',
                    title: '<b>Култура</b>',
                    sortable: true,
                    width: 60,
                    align: 'center',
                    styler: function (value, row, index) {
	                	if(row.is_active_culture == false){
	                		return 'background-color:#F69232;';
	                    }
                    }
                },{
					field: 'schema',
					title: '<b>Схеми/Mерки</b>',
					sortable: true,
					width: 100
				},{
					field: 'area',
					title: '<b>Площ(ха)</b>',
					sortable: true,
					width: 90
				},{
					field: 'ekatte',
					title: '<b>ЕКАТТЕ</b>',
					sortable: true,
					width: 90
				},{
					field: 'edited',
					title: '<b>За редактиране</b>',
					sortable: true,
					width: 90,
					formatter: function (value, row, index) {
						if (value === false) {
							return 'Не';
						}
						return 'Да';
					}
				},{
					field: 'comment',
					title: '<b>Коментар</b>',
					sortable: true,
					width: 90
				}
			]],
		onUnselect: function(rowIndex, rowData) {

		},
		onSelect: function(rowIndex, rowData) {
			checkedElement = rowData;
			displayFeatureSelection(rowData.st_astext, rowData);
		},
		toolbar: '#attr-tables-toolbar',
		onBeforeLoad: function() {
		},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

	var combotree = jQuery('#all-layers-tree').combotree('tree');
    var layerData = combotree.tree('getSelected');

	jQuery('#select-for-isak-filter-culture').combobox({

        url: 'index.php?common-rpc=culture-combobox-by-layername',
        rpcParams: [{
        	without_crops:1,
        	layer_name:layerData.attributes.layer_name
        }],
        valueField: 'id',
        textField: 'name',
        filter: function(q, row) {
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if (text.indexOf(find) != -1 || value.indexOf(find) != -1)
            {
                return true;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}
