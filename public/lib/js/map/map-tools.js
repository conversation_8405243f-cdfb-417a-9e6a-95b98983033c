var _toolOperation = 0;
var TOOL_OPERATION_EDIT_GEOMETRY = 1;
var TOOL_OPERATION_DRAW_HOLE = 2;
var TOOL_OPERATION_MERGE = 3;
var TOOL_OPERATION_REMOVE_HOLES = 4;
var TOOL_OPERATION_SPLIT = 5;
var TOOL_OPERATION_SAVE = 6;
var TOOL_OPERATION_DRAW = 7;
var TOOL_OPERATION_SAVE_WITH_INFO = 8;

function initMapTools()
{
	var allLayersTree = jQuery('#all-layers-tree');
	//init map tools
	jQuery('#tool-set-scale').bind('click', function()
	{
		var scale = jQuery("#scale-denominator input").val();

		map.zoomToScale(scale);
	});

	jQuery('#tool-panzoom').bind('click', function()
	{
		_clippingZoom = true;
		var options = jQuery('#tool-panzoom').linkbutton('options');
		if (options.disabled)
		{
			return false;
		}

		//stop choose layer object event if active
		map.events.unregister('click', map, propertyWindowFunction);
		chooseControl('navigation');
		//unselect all buttons
		unselectAll();
		jQuery('#tool-choose-layer-object').linkbutton('unselect');
		//select navigation button
		jQuery('#tool-panzoom').linkbutton('select');
		_flagRightButtonClicked = false;
	});

	jQuery('#tool-zoomin').bind('click', function()
	{
		_clippingZoom = true;
		unselectAll();
		map.events.unregister('click', map, propertyWindowFunction);

		chooseControl('zoomin');
		jQuery('#tool-zoomin').linkbutton('select');
	});

	jQuery('#tool-zoomout').bind('click', function()
	{
		_clippingZoom = true;
		unselectAll();
		map.events.unregister('click', map, propertyWindowFunction);

		chooseControl('zoomout');
		jQuery('#tool-zoomout').linkbutton('select');
	});

	jQuery('#tool-measure-line').bind('click', function()
	{
		unselectAll();
		map.events.unregister('click', map, propertyWindowFunction);

		chooseControl('linemeasure');

		var viewPort = map.getViewport();
		var mapElement = jQuery(viewPort).find('svg');
		jQuery(mapElement).mousedown(function (evt) {
			if (evt.which == 2 && controlLineMeasure.active) {
				controlNavigation.activate();
			};
		});
		jQuery(mapElement).mouseup(function (evt) {
			if (evt.which == 2 && controlLineMeasure.active) {
				var layersToRerender = map.getLayersByClass("OpenLayers.Layer.Vector");
				for (var i = layersToRerender.length - 1; i >= 0; i--) {
					layersToRerender[i].redraw();
				}
				controlNavigation.deactivate();
			}
		});

		jQuery('#tool-measure-line').linkbutton('select');
	});

	jQuery('#tool-measure-polygon').bind('click', function()
	{
		unselectAll();
		map.events.unregister('click', map, propertyWindowFunction);

		chooseControl('polygonmeasure');

		var viewPort = map.getViewport();
		var mapElement = jQuery(viewPort).find('svg');
		jQuery(mapElement).mousedown(function (evt) {
			if (evt.which == 2 && controlPolygonMeasure.active) {
				controlNavigation.activate();
			}
		});
		jQuery(mapElement).mouseup(function (evt) {

			if (evt.which == 2 && controlPolygonMeasure.active) {
				var layersToRerender = map.getLayersByClass("OpenLayers.Layer.Vector");
				for (var i = layersToRerender.length - 1; i >= 0; i--) {
					layersToRerender[i].redraw();
				}
				controlNavigation.deactivate();
			}
		});
		jQuery('#tool-measure-polygon').linkbutton('select');
	});

	jQuery('#tool-clear-selection').bind('click', function()
	{
		layerInAttrTable = null;
		if (controlModify.active) {
			controlNavigation.deactivate();
			chooseControl('navigation');
		}

		var options = jQuery('#tool-clear-selection').linkbutton('options');
		if (options.disabled)
		{
			return false;
		}
		vectors.removeAllFeatures();
		updateTotalArea(0);
        unselectAll();
        deactivateAllControls();
        chooseControl('navigation');

        var combotree = jQuery('#all-layers-tree').combotree('tree');
    	var activeLayer = combotree.tree('getSelected');

		if(activeLayer != null)
		{
        	toggleGeometryTools(activeLayer.attributes.layer_type);
		}
		_flagRightButtonClicked = false;
	});

	jQuery('#tool-draw').bind('click', function(e, options)
	{
		var options = jQuery('#tool-draw').linkbutton('options');
		if (options.disabled)
		{
			return false;
		}

		clearPolygonReplaceVariables();

		//remove all selected buttons and features
		map.events.unregister('click', map, propertyWindowFunction);
		if (!options || options.removeFeatures) {
			vectors.removeAllFeatures();
		}
		unselectAll();
		//select button
		jQuery('#tool-draw').linkbutton('select');

		_toolOperation = TOOL_OPERATION_DRAW;

		//choose navigation control first to keep it active while drawing
		chooseControl('navigation');
		controlDraw.activate();
	});

	jQuery('#tool-select').bind('click', function()
	{
		var combotree = allLayersTree.combotree('tree');
		var selLayer = combotree.tree('getSelected');
		if (!selLayer) {
			return false;
		}
		if (controlModify.active) {
			controlNavigation.deactivate();
			chooseControl('navigation');
		}
		var options = jQuery('#tool-select').linkbutton('options');
		if (options.disabled)
		{
			return false;
		}
		//remove all previous selections and drawed polygons
		map.events.unregister('click', map, propertyWindowFunction);
		//deactivate control modify bofore removing features
		//if control is still active it will trigger an error
		controlModify.deactivate();
		//remove features and unselect buttons
		vectors.removeAllFeatures();
		unselectAll();
		//select button
		jQuery('#tool-select').linkbutton('select');

		chooseControl('selectfeature');
		var selLayer = combotree.tree('getSelected');
		var layerName = selLayer.attributes.layer_name;
		if (MANUAL_SELECT_LAYERS.indexOf(layerName) != -1) {
			controlSelect.handlers.box.deactivate(); //Deactivating drag selection.
		} else {
			controlSelect.handlers.box.activate(); //For all other layers activate it.
		}
		
		_flagRightButtonClicked = false;
	});

	//zoom to active layer extent
	jQuery('#tool-zoom-layer').bind('click', function() {
		var combotree = jQuery('#all-layers-tree').combotree('tree');
    	var selected = combotree.tree('getSelected');

		if (!selected)
		{
			jQuery.messager.alert('Грешка', 'Не е избран активен слой!');
			return false;
		}

		map.zoomToExtent(new OpenLayers.Bounds.fromString(selected['attributes'].extent).transform(
				new OpenLayers.Projection("EPSG:32635"),
				map.getProjectionObject())
				);

		return false;
	});

	jQuery('#tool-edit-geometry').bind('click', function()
	{
		var combotree = allLayersTree.combotree('tree');
		var selLayer = combotree.tree('getSelected');
		if (!selLayer) {
			return false;
		}
		var options = jQuery('#tool-edit-geometry').linkbutton('options');
		if (options.disabled)
		{
			return false;
		}

		clearPolygonReplaceVariables();

		//remove info selection
		map.events.unregister('click', map, propertyWindowFunction);
		//unselect all buttons
		unselectAll();

		if (vectors.features.length > 0)
		{
			chooseControl('navigation');
			controlModify.activate();
		} else
		{
			jQuery.messager.alert('Грешка', 'Не са селектирани обекти!');
		}

		_toolOperation = TOOL_OPERATION_EDIT_GEOMETRY;

		jQuery('#tool-edit-geometry').linkbutton('select');

		//activate save button
		jQuery('#tool-save').linkbutton('enable');
		_flagRightButtonClicked = false;
	});

	//click button to save for isak plot
	jQuery('#btn-save-for-isak-info').bind('click', function()
	{

		if (jQuery('#plot-name-for-isak').textbox('isValid') && jQuery('#select-ekatte-for-isak').combobox('isValid')) {

			jQuery('#win-add-for-isak-info').window('close');
			_toolOperation = TOOL_OPERATION_SAVE_WITH_INFO;

			jQuery('#tool-save').click();
		}else {
			jQuery.messager.alert('Грешка', 'Моля въведете "Име" и "ЕКАТТЕ"!','warning');
			return false;
		}

	});

	//click button to save temp data plot
	jQuery('#btn-save-temp-data-info').bind('click', function()
	{
		if (jQuery('#plot-name-temp-data').textbox('isValid')) {

			jQuery('#win-add-temp-data-info').window('close');
			_toolOperation = TOOL_OPERATION_SAVE_WITH_INFO;

			jQuery('#tool-save').click();
		}
		else {
			jQuery.messager.alert('Грешка', 'Моля въведете "Име"!','warning');
			return false;
		}
	});

	jQuery('#tool-save').bind('click', function()
	{
		if (controlModify.active) {
			controlNavigation.deactivate();
			chooseControl('navigation');
		}

		var options = jQuery('#tool-save').linkbutton('options');
		if (options.disabled)
		{
			return false;
		}

		//deactivate modify control
		//otherwise incorrect result will be returned for edited polygons geometry
		controlModify.deactivate();
		var obj = {};
		obj.features = [];

		if (!vectors.features.length)
		{
			jQuery.messager.alert('Грешка', 'Не са намерени промени, които да бъдат запазени!');
			return false;
		}

		var combotree = jQuery('#all-layers-tree').combotree('tree');
    	var layerData = combotree.tree('getSelected');
		var layerType = layerData.attributes.layer_type;
		var featuresCnt = vectors.features.length;
		var featuresForSave = vectors.features;
		var lastFeature = vectors.features[featuresCnt-1];

        //if active layer is kvs
        if (layerType == LAYER_KVS_PLOTS)
        {
            saveKVSChanges();

            return false;
        }

        //show popup to fill the name of the added object
        if (layerType == LAYER_FOR_ISAK_PLOTS && _toolOperation == TOOL_OPERATION_DRAW) {
        	jQuery('#win-add-for-isak-info').window('open');
        	return false;
        }

        //show popup to fill the name and information about the the added object
        if (layerType == LAYER_TEMP_DATA && _toolOperation == TOOL_OPERATION_DRAW) {
        	jQuery('#win-add-temp-data-info').window('open');
        	return false;
        }

		obj.tablename = layerData.attributes.layer_name;
		obj.layer_type = layerData.attributes.layer_type;
		obj.layer_id = layerData.id;
		obj.tool_operation = _toolOperation;

		if (layerType == LAYER_FOR_ISAK_PLOTS) {
			var plot_name = jQuery('#plot-name-for-isak').textbox('getValue');
			var ekatte = jQuery('#select-ekatte-for-isak').combobox('getValue');
			if (plot_name.length) {
				obj.plot_name = plot_name;
			}
			if (ekatte.length) {
				obj.ekatte = ekatte;
			}
		}

		if (layerType == LAYER_TEMP_DATA) {
			var plot_name = jQuery('#plot-name-temp-data').textbox('getValue');
			var plot_info = jQuery('#plot-info-temp-data').val();

			if (plot_name.length) {
				obj.plot_name = plot_name;
			}
			if (plot_info.length) {
				obj.plot_info = plot_info;
			}
		}

		jQuery('#plot-name-for-isak').textbox('setValue','');

		if (_toolOperation == TOOL_OPERATION_SAVE_WITH_INFO) {
			featuresForSave = [lastFeature];
		}

		for (i = 0; i < featuresForSave.length; i++)
		{
			//ensure that geometry has at least 3 points(is polygon)
			if (featuresForSave[i].geometry && featuresForSave[i].geometry.getVertices().length > 2)
			{
				obj.features[i] = new Object();

				if (layerType == LAYER_ZP_PLOTS || layerType == LAYER_TYPE_LFA) {
					obj.features[i].id_name = 'id';
					obj.features[i].id = featuresForSave[i].attributes.id;
				}
				else {
					obj.features[i].id_name = 'gid';
					obj.features[i].id = featuresForSave[i].attributes.gid;
				}

				obj.features[i].geometry = featuresForSave[i].geometry.transform(
						new OpenLayers.Projection("EPSG:900913"),
						new OpenLayers.Projection("EPSG:32635")
						).toString();
			}
		}

		_toolOperation = TOOL_OPERATION_SAVE;

		if (obj.features.length > 0)
		{
			//used on merge, split and hole drawing
			//show which polygon/s should be replaced with the new one
			obj.replacePolygonVariables = new Object();
			obj.replacePolygonVariables.replace_reqired = replacePolygonRequired;
			obj.replacePolygonVariables.action = replacePolygonAction;
			obj.replacePolygonVariables.id_name = replacePolygonIdName;
			obj.replacePolygonVariables.id_array = replacePolygonIdArray;

			TF.Rpc.Map.MapTools.saveItem(obj)
			.done(function (data) {
				reloadLayerAfterFeaturesChange(data);
			})
			.fail(function (errorObj) {
				if (errorObj.is(TF.Rpc.ExceptionsList.MAP_EXISTING_NAME)) {
					jQuery.messager.alert('Грешка', 'Вече съществува парцел с това име!','warning');
				};
			});

			//unselect all buttons
			unselectAll();
			//activate default panzoom(navigation) control
			chooseControl('navigation');
			jQuery('#tool-panzoom').linkbutton('select');

			//disable save and delete buttons
			jQuery('#tool-draw').linkbutton('enable');
			jQuery('#tool-edit-geometry').linkbutton('disable');
			jQuery('#tool-save').linkbutton('disable');
		}
		else {
			jQuery.messager.alert('Грешка', 'Не са намерени промени, които да бъдат запазени!');
			return false;
		}
		_flagRightButtonClicked = false;
		clearPolygonReplaceVariables();
		return false;
	});

	jQuery('#tool-delete-all').bind('click', function()
	{
		var options = jQuery('#tool-delete-all').linkbutton('options');
		var combotree = jQuery('#all-layers-tree').combotree('tree');
    	var layerData = combotree.tree('getSelected');

		if (options.disabled) {
			return false;
		}

		jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да премахнете всички обекти в слой "'+layerData['attributes'].name+'"?', function(r)
		{
			if (r)
			{
				clearPolygonReplaceVariables();

				obj = new Object();

				var layerType = layerData['attributes'].layer_type;
				obj.layer_type = layerType;
				obj.layer_id = layerData.id;
				obj.tablename = layerData['attributes'].layer_name;

				if (layerType == LAYER_ZP_PLOTS || layerType == LAYER_TYPE_LFA) {
					obj.id_name = 'id';
				}
				else {
					obj.id_name = 'gid';
				}

				unselectAll();
				//activate default panzoom(navigation) control
				chooseControl('navigation');
				jQuery('#tool-panzoom').linkbutton('select');

				TF.Rpc.Map.MapTools.deleteAllItemsByLayer(obj)
				.done(function (data) {
					reloadLayerAfterFeaturesChange(data);
                    initAllLayersTree();
					layerInAttrTable = null;
				})
				.fail(function (errorObj) {
					jQuery.messager.alert('Грешка',errorObj.getMessage(),'warning');
				});
			}
		});
	});

	jQuery('#tool-cross-layers').bind('click', onLayerClipButtonClick);
	jQuery('#map-copy-submenu').on('click', '.js-copy-layer-btn', tmp_layer_type, onCopyToLayerButtonClick);
	jQuery('#map-copy-submenu').on('click', '.js-copy-layer-btn-for-isak', for_isak_layer_type, onCopyToLayerButtonClick);
	jQuery('#map-copy-submenu').on('click', '.js-copy-layer-btn-zp', zp_layer_type, onCopyToLayerButtonClick);
	jQuery('#map-copy-submenu').on('click', '.js-copy-layer-btn-work-layer', work_layer_type, onCopyToLayerButtonClick);

	jQuery('#tool-delete').bind('click', function()
	{
		var options = jQuery('#tool-delete').linkbutton('options');

		if (options.disabled) {
			return false;
		}

		jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да премахнете тези обекти?', function(r)
		{
			if (r)
			{
				clearPolygonReplaceVariables();

				obj = new Object();
				obj.features = new Array();

				if (vectors.features.length === 0)
				{
					jQuery.messager.alert('Грешка', 'Не са избрани обекти, които да бъдат премахнати!')
					return false;
				}

				//get curently active layer data
				var combotree = jQuery('#all-layers-tree').combotree('tree');
    			var layerData = combotree.tree('getSelected');
				var layerType = layerData['attributes'].layer_type;
				obj.layer_type = layerType;
				obj.layer_id = layerData.id;
				obj.tablename = layerData['attributes'].layer_name;

				if (layerType == LAYER_ZP_PLOTS || layerType == LAYER_TYPE_LFA) {
					obj.id_name = 'id';
				}
				else {
					obj.id_name = 'gid';
				}

				for (i = 0; i < vectors.features.length; i++)
				{
					if (vectors.features[i].attributes.gid || vectors.features[i].attributes.id)
					{
						if (typeof vectors.features[i].attributes.id !== 'undefined') {
							obj.features.push(vectors.features[i].attributes.id);
						} else {
							obj.features.push(vectors.features[i].attributes.gid);
						}
					}
				}

				if (obj.features.length == 0)
				{
					jQuery.messager.alert('Грешка', 'Не са избрани обекти, които да бъдат премахнати!');
					return false;
				}

				//deactivate delete button
				jQuery('#tool-delete').linkbutton('disable');
				//unselect all buttons
				unselectAll();
				//activate default panzoom(navigation) control
				chooseControl('navigation');
				jQuery('#tool-panzoom').linkbutton('select');

				TF.Rpc.Map.MapTools.deleteItem(obj)
				.done(function (data) {
					reloadLayerAfterFeaturesChange(data);
				})
				.fail(function(data) {

				});
			}
		});

	});

	jQuery('#tool-draw-hole').bind('click', function()
	{
		var combotree = allLayersTree.combotree('tree');
		var selLayer = combotree.tree('getSelected');
		if (!selLayer) {
			return false;
		};
		var options = jQuery('#tool-draw-hole').linkbutton('options');
		if (options.disabled)
		{
			return false;
		}

		clearPolygonReplaceVariables();

		if (vectors.features.length == 0)
		{
			jQuery.messager.alert('Грешка', 'Не са избрани обекти, за които да бъде очертана дупка! ');
			return false;
		}
		if (vectors.features.length > 1)
		{
			jQuery.messager.alert('Грешка', 'Не може да се чертае дупка на няколко обекта едновременно!');
			return false;
		}

		_toolOperation = TOOL_OPERATION_DRAW_HOLE;

		unselectAll();

		controlDrawHole.activate();

		var viewPort = map.getViewport();
		var mapElement = jQuery(viewPort).find('svg');
		jQuery(mapElement).mousedown(function (evt) {
			if (evt.which == 2 && controlDrawHole.active) {
				controlNavigation.activate();
			};
		});
		jQuery(mapElement).mouseup(function (evt) {

			if (evt.which == 2 && controlDrawHole.active) {
				controlNavigation.deactivate();
			};
		});

		jQuery('#tool-draw-hole').linkbutton('select');
	});

	jQuery('#tool-merge').bind('click', function()
	{
		var combotree = allLayersTree.combotree('tree');
		var selLayer = combotree.tree('getSelected');
		if (!selLayer) {
			return false;
		};
		var options = jQuery('#tool-merge').linkbutton('options');
		if (options.disabled)
		{
			return false;
		}

		clearPolygonReplaceVariables();

		var combotree = jQuery('#all-layers-tree').combotree('tree');
    	var layerData = combotree.tree('getSelected');
		var layerType = layerData.attributes.layer_type;
		var layerName = layerData.attributes.layer_name;
		var idName;

		if (layerType == LAYER_KVS_PLOTS)
		{
			jQuery.messager.alert('Информация', 'Тази функционалност е достъпна в Agrimi!');
			return false;
		}

		var featuresCount = vectors.features.length;
		if (featuresCount == 0)
		{
			jQuery.messager.alert('Грешка', 'Не са избрани обекти!');
			return false;
		}


		//last element will always be the union feature
		//at first iteration last element will be taken as union feature
		var unionFeature = vectors.features[featuresCount - 1];
		//add last feature ID into merge array with merge ID
		replacePolygonRequired = true;
		replacePolygonAction = 'merge';

		if (layerType == LAYER_ZP_PLOTS || layerType == LAYER_TYPE_LFA) {
			replacePolygonIdArray.push(unionFeature.attributes.id);
			idName = 'id';
		}
		else {
			replacePolygonIdArray.push(unionFeature.attributes.gid);
			idName = 'gid';
		}

		replacePolygonIdName = idName;

		for (var i = 0; i < vectors.features.length - 1; i++) {
			if (layerType == LAYER_ZP_PLOTS || layerType == LAYER_TYPE_LFA) {
				replacePolygonIdArray.push(vectors.features[i].attributes.id);
			}
			else {
				replacePolygonIdArray.push(vectors.features[i].attributes.gid);
			}
		};

		TF.Rpc.Map.MapTools.getCombinedGeometry(replacePolygonIdArray, replacePolygonIdName, layerName)
			.done(function (data) {

				if (data.substring(0,12) == 'MULTIPOLYGON') {
					jQuery.messager.alert('Грешка', 'Избраните обекти не могат да бъдат обединени!');
					return;
				}

				var in_options = {
					'internalProjection': map.baseLayer.projection,
					'externalProjection': new OpenLayers.Projection("EPSG:32635")
				},
					feature = new OpenLayers.Format.WKT(in_options).read(data);

					var combotree = jQuery('#all-layers-tree').combotree('tree');
			    	var layerData = combotree.tree('getSelected');
					var layerType = layerData.attributes.layer_type;

					if(layerType == LAYER_KVS_PLOTS) {
						feature.attributes.kvs_action = 'merge';
						var tmpGids = [];
						for (var i = 0; i < vectors.features.length; i++) {
							tmpGids.push(vectors.features[i].attributes.gid);
						}
						feature.attributes.gid = tmpGids.join();
					}

					vectors.removeAllFeatures();
					if(layerType == LAYER_KVS_PLOTS) {
						//@HACK added secon feature to the layer so that the save function recognises a merge action.
						// see saveKVSChanges()
						vectors.addFeatures([feature, new OpenLayers.Format.WKT(in_options).read(data)]);
					}else {
						vectors.addFeatures([feature]);
					}
					_toolOperation = TOOL_OPERATION_MERGE;

					//activate save button and deactivate other buttons
					jQuery('#tool-save').linkbutton('enable');
					jQuery('#tool-edit-geometry').linkbutton('disable');
					jQuery('#tool-delete').linkbutton('disable');

					jQuery('#tool-draw').linkbutton('disable');
					jQuery('#tool-draw-hole').linkbutton('disable');
					jQuery('#tool-split').linkbutton('disable');
					//jQuery('#tool-merge').linkbutton('disable');
			})
			.fail(function (errorObj) {
				jQuery.messager.alert('Грешка', 'Избраните обекти не могат да бъдат обединени!');
				return;
			});
	});

	jQuery('#tool-remove-holes').bind('click', function (e) {
		var combotree = allLayersTree.combotree('tree');
		var selLayer = combotree.tree('getSelected');
		if (!selLayer) {
			return false;
		};

		var options = jQuery('#tool-remove-holes').linkbutton('options');
		if (options.disabled) {
			return false;
		}

		var selectedFeatures = vectors.features;
		if (selectedFeatures.length !== 1) {
			jQuery.messager.alert('Грешка', 'Само един парцел трябва да бъде селектиран.');
			return;
		}

		_toolOperation = TOOL_OPERATION_REMOVE_HOLES;

		//Activates the Draw tool.
		jQuery('#tool-draw').trigger('click', {removeFeatures: false});

		controlDraw.events.register("featureadded", this, onRemoveHolesPatch);
	});

	function onRemoveHolesPatch(e) {
		var combotree = jQuery('#all-layers-tree').combotree('tree');
    	var layerData = combotree.tree('getSelected');
		var layerType = layerData.attributes.layer_type;
		var selectedFeatures = vectors.features;
		var polyWithHoles;
		var polyPatch;

		controlDraw.events.unregister("featureadded", this, onRemoveHolesPatch);

		if (selectedFeatures[0].geometry.CLASS_NAME === 'OpenLayers.Geometry.MultiPolygon') {
			polyWithHoles = selectedFeatures[0];
			polyPatch = selectedFeatures[1];
		} else {
			polyWithHoles = selectedFeatures[1];
			polyPatch = selectedFeatures[0];
		}
		replacePolygonRequired = true;
		replacePolygonAction = 'merge';

		if (layerType == LAYER_ZP_PLOTS || layerType == LAYER_TYPE_LFA) {
			replacePolygonIdName = 'id';
		}
		else {
			replacePolygonIdName = 'gid';
		}

		replacePolygonIdArray.push(polyWithHoles.attributes[replacePolygonIdName]);

		for (var j = 0; j < polyPatch.geometry.getVertices().length; j++) {
			var point = polyPatch.geometry.getVertices()[j];

			if (!point.intersects(polyWithHoles.geometry)) {
				jQuery.messager.alert('Грешка', 'Всички точки на покриващият полигон трябва да са в парцела.');
				jQuery('#tool-save').linkbutton('disable');
				clearPolygonReplaceVariables();
				vectors.removeAllFeatures();
				return;
			}
		}
		var reader = new jsts.io.WKTReader();
		var polyWithHolesJSTS = reader.read(polyWithHoles.geometry.toString());
		var polyPatchJSTS = reader.read(polyPatch.geometry.toString());

		var union = polyWithHolesJSTS.union(polyPatchJSTS);
		var parser = new jsts.io.OpenLayersParser();

		union = parser.write(union);
		vectors.removeAllFeatures();
		var unionFeature = new OpenLayers.Feature.Vector(union, null, {
			fillColor: '#' + layerData.attributes.fill_color,
			strokeColor: "#00ff00",
			strokeWidth: 2,
			fillOpacity: 1
		});

		unionFeature.attributes[replacePolygonIdName] = polyWithHoles.attributes[replacePolygonIdName];
		replacePolygonIdArray.push(unionFeature.attributes[replacePolygonIdName]);
		vectors.addFeatures([unionFeature], {silent: true});
		jQuery('#tool-save').linkbutton('enable');
	}
	jQuery('#tool-split').bind('click', function()
	{
		var combotree = allLayersTree.combotree('tree');
		var selLayer = combotree.tree('getSelected');
		if (!selLayer) {
			return false;
		};
		var options = jQuery('#tool-split').linkbutton('options');
		if (options.disabled)
		{
			return false;
		}

        if (vectors.features.length == 0)
		{
			jQuery.messager.alert('Грешка', 'Не са избрани обекти, които да бъдат разделени! ');
			return false;
		}

		if (vectors.features.length > 1)
		{
			jQuery.messager.alert('Грешка', 'Не може да разцепвате няколко обекта едновременно!');
			return false;
		}
        clearPolygonReplaceVariables();
		_toolOperation = TOOL_OPERATION_SPLIT;

		unselectAll();
		controlDrawSplitLine.activate();
		var viewPort = map.getViewport();
		var mapElement = jQuery(viewPort).find('svg');
		jQuery(mapElement).mousedown(function (evt) {
			if (evt.which == 2 && controlDrawSplitLine.active) {
				controlNavigation.activate();
			};
		});
		jQuery(mapElement).mouseup(function (evt) {
			if (evt.which == 2 && controlDrawSplitLine.active) {
				controlNavigation.deactivate();
			};
		});

		jQuery('#tool-split').linkbutton('select');
	});



	jQuery('#tool-auto-split').bind('click', function()
	{
		if (vectors.features.length == 0)
		{
			jQuery.messager.alert('Грешка', 'Не са избрани обекти, които да бъдат разделени! ');
			return false;
		}

		if (vectors.features.length > 1)
		{
			jQuery.messager.alert('Грешка', 'Не може да разделяте няколко обекта едновременно!');
			return false;
		}

		jQuery('#tool-auto-split').linkbutton('select');

		let geometry = vectors.features[0].geometry;  // simplifyFeatureGeometry(vectors.features[0]);
		let gid = vectors.features[0].attributes.gid;
		
		const tmpFeature = vectors.features[0].clone();

		Proj4js.defs["EPSG:8122"] = "+proj=lcc +lat_0=42.6678756833333 +lon_0=25.5 +lat_1=42 +lat_2=43.3333333333333 +x_0=500000 +y_0=4725824.3591 +ellps=GRS80 +towgs84=0,0,0,0,0,0,0 +units=m +no_defs";

		let featureArea = parseFloat((tmpFeature.geometry.transform('EPSG:900913', 'EPSG:32635').getArea()));
		
		let parser = new jsts.io.OpenLayersParser();
		let jstsGeom = parser.read(geometry);
		let olExtentGeom = parser.write(jstsGeom.getEnvelope());
		let jstsGeomBuffered = jstsGeom.buffer(-5);
		let olGeomBuffered = parser.write(jstsGeomBuffered);

		let feature = new OpenLayers.Feature.Vector(olExtentGeom);
		const featureCopy = feature.clone();

		let bufferedFeature = new OpenLayers.Feature.Vector(olGeomBuffered);

		vectors.removeAllFeatures();

		let geoms = [];

		feature.geometry.transform(
			new OpenLayers.Projection("EPSG:900913"),
			new OpenLayers.Projection("EPSG:32635")
		);

		let polygon = {
			'geom': feature.geometry.toString(),
			'projection': 'EPSG:32635'
		};
		geoms.push(polygon);
		featureCopy.geometry.transform(
			new OpenLayers.Projection("EPSG:900913"),
			new OpenLayers.Projection("EPSG:8122")
		);

		polygon = {
			'geom': featureCopy.geometry.toString(),
			'projection': 'EPSG:8122',
		};

		geoms.push(polygon);


		let requestObject = {
			geoms: geoms,
			gid: gid,
		};

		TF.Rpc.Map.MapTools.splitPolygon(requestObject)
			.done(function (data) {
				addGeomToLayer(data, gid, bufferedFeature, featureArea);
			})
			.fail(function (errorObj) {
				jQuery('#tool-auto-split').linkbutton('unselect');
				if (errorObj.is(TF.Rpc.ExceptionsList.SPLIT_TOOL_NO_SPLIT_PLOTS_FOUND)) {
					jQuery.messager.alert('Грешка', 'Не са намерени критерии за промяна по посочения от Вас имот.','warning');
				} else {
					jQuery.messager.alert('Грешка', 'Грешка при опит за автоматично разделяне на геометрия.','warning');
				}
			});
	});
}

function addGeomToLayer(data, beforeSplitGid, bufferedFeature, totalAreaBeforeSplit) {
	if (!data) {
		return;
	}
	
	let totalArea = 0;
	let toralAreaReached = false;
	let combotree = jQuery('#all-layers-tree').combotree('tree');
	let activeLayer = combotree.tree('getSelected');
	
	for (let ii=0; ii < data.length; ii++) {
		let record = data[ii];

		if (record.responseSource == "KAIS") {
			var projection = "EPSG:8122";
			let reader = new jsts.io.WKTReader();
			let polyJSTS = reader.read(record.geometry);
			let parser = new jsts.io.OpenLayersParser();
			let polyOl = parser.write(polyJSTS);
			var feature = new OpenLayers.Feature.Vector(polyOl);
			feature.attributes.document_area = polyJSTS.getArea();
			feature.attributes.jsts_area = polyJSTS.getArea();
			feature.geometry.transform('EPSG:8122','EPSG:900913');
			feature.attributes.ol_area = feature.geometry.getArea();
			feature.attributes.number = record.properties.number;
		} else {
			var projection = "EPSG:32635";
			let in_options = {
				'internalProjection': map.baseLayer.projection,
				'externalProjection': new OpenLayers.Projection(projection)
			};

			let features = new OpenLayers.Format.GeoJSON(in_options).read(record);
		    var feature = features.shift();
		}

		try {
			if (!feature.geometry.intersects(bufferedFeature.geometry)) {
				continue;
			}
		} catch (error) {
			// Cases with long and thin plots have problem with buffered geometry
			// map layers array len out of range. Object components len is 0.
			continue;
		}
		
		feature.attributes.label = (feature.attributes.document_area / 1000).toFixed(3).toString() + ' дка';
		vectors.addFeatures(feature);
		
		let tmpFeature = feature.clone();
		totalArea += parseFloat(tmpFeature.geometry.transform('EPSG:900913', 'EPSG:32635').getArea());

		if (parseInt(totalAreaBeforeSplit) <= parseInt(totalArea) && toralAreaReached === false) {
			// mark record for remove and continue // this should be the case when plot was split twice
			toralAreaReached = true;
			continue;
		}
		
		if(toralAreaReached === true) {
			// execute this part of code only if plot was split twice and the first split is not in our system
			// if more records exists after totalAreaReached, removed previous one
			let tmpFeatureToRemove =  vectors.features[ii-1].clone();
			totalArea -= tmpFeatureToRemove.geometry.transform('EPSG:900913', 'EPSG:32635').getArea();
			vectors.removeFeatures(vectors.features[ii-1]);
			toralAreaReached = false;
		}
	}

	if (vectors.features.length > 0) {
		if (activeLayer.attributes.layer_type == 5) {
			vectors.features[0].attributes.kvs_action = 'split';
			vectors.features[0].attributes.gid = beforeSplitGid;
		} else {
			controlDrawSplitLine.deactivate();
			unselectAll();
			chooseControl('navigation');
			jQuery('#tool-split').linkbutton('disable');
		}

		//activate save button and deactivate other buttons
		jQuery('#tool-save').linkbutton('enable');
		jQuery('#tool-edit-geometry').linkbutton('disable');
		jQuery('#tool-delete').linkbutton('disable');

		jQuery('#tool-draw').linkbutton('disable');
		jQuery('#tool-draw-hole').linkbutton('disable');

		jQuery('#tool-merge').linkbutton('disable');
	} else {
		jQuery('#tool-auto-split').linkbutton('unselect');
		jQuery.messager.alert('Грешка', 'Не са намерени критерии за промяна по посочения от Вас имот.','warning');
	}
}

function simplifyFeatureGeometry(feature) {
	let vertices = feature.geometry.getVertices();
	let lineString = new OpenLayers.Geometry.LineString(vertices);
	let simplifiedLineVertices = lineString.simplify(10).getVertices();
	let linearRing = new OpenLayers.Geometry.LinearRing(simplifiedLineVertices);
	let geometry = new OpenLayers.Geometry.Polygon([linearRing]);

	return geometry;
}

function saveKVSChanges()
{
    if(vectors.features[0].attributes.kvs_action == 'merge')
    {
        if (vectors.features.length > 1)
        {
            jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да продължите?', function(r) {
                if (r) {
                    for(var i=1; i<vectors.features.length; i++)
                    {
                        vectors.removeFeatures(vectors.features[i]);
                    }

                    TF.Rpc.Map.KVSMerge.setMergeFields(vectors.features[0].attributes.gid)
                    .done(function (data) {
                    	jQuery('#kvs-merge-editing-ids > input').val(vectors.features[0].attributes.gid);
                    	initKVSMergeFieldsData(data);
                    	jQuery('#win-kvs-merge').window('open');
                    	initKVSMergeFields();
                    })
                    .fail(function (errorObj) {

                    });
                }
            });
        }
        else
        {
        	jQuery('#kvs-merge-editing-ids > input').val(vectors.features[0].attributes.gid);
        	TF.Rpc.Map.KVSMerge.setMergeFields(vectors.features[0].attributes.gid)
        	.done(function (data) {
        		initKVSMergeFieldsData(data);
        		jQuery('#win-kvs-merge').window('open');
        		initKVSMergeFields();
        	})
        	.fail(function (errorObj) {

        	});
        }
    }
    else if(vectors.features[0].attributes.kvs_action == 'split')
    {
		jQuery('#win-kvs-split').window('open');
        initKVSSplitPrGrid(vectors.features[0].attributes.gid);

    }
}

function messagerMapRightsRW() {
	jQuery.messager.alert('Грешка', 'Нямате права да оперирате с тази функционалност!', 'warning');
}

function initKVSMergeFieldsData(data) {
	jQuery('#kvs-merge-ekate > input').val(data['ekate']);
	jQuery('#kvs-merge-masiv > input').val(data['masiv']);
	jQuery('#kvs-merge-mestnost > input').val(data['mestnost']);

}
