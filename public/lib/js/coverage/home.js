jQuery(function() {
    initPageControls();
    initButtonControls();
    initMap();
    
    initZPLayersTree();
    initFilesTree();
    setUserRights();
    
});

function initCoverageUpliad() {
    const url  = "index.php?json=coverage-upload"; 

    jQuery("#uploader").pluploadQueue({
        // General settings
        runtimes: 'gears,html5,flash,silverlight,browserplus',
        url: url,
        max_file_size: '100mb',
        unique_names: true,
        multipart_params : {
        },
        // Flash settings
        flash_swf_url: 'lib/js_external/fileupload/plupload.flash.swf',
        filters : [
            {title : 'ZIP файлове', extensions : 'zip'}
        ],
    });

    var uploader = jQuery('#uploader').pluploadQueue();
    uploader.bind('UploadComplete', function() {
        jQuery('#win-add-file').window('close');
        jQuery('#files-tree').tree('reload');
    });
}