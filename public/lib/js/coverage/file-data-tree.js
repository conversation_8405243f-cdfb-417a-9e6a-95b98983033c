function initFileStructureTree(file_id) {
    jQuery('#structure-tree').tree({
        url: 'index.php?coverage-rpc=coverage-file-structure-tree',
        rpcParams: [{
            file_id: file_id,
        }],
        animate: true,
        lines: true,
        formatter: function(node) {
            if (node.color)
                return "<div style='width:13px;height:13px;background-color:#" + node.color + ";margin-top:3px;float:left;margin-right:3px;'></div>" + node.text;
            else
                return node.text;
        },
        onLoadSuccess: function() {
            var level_1 = jQuery('#structure-tree').tree('getRoots');

            if (level_1.length) {
                var level_2 = jQuery('#structure-tree').tree('getChildren', level_1[0].target);
                var level_3 = jQuery('#structure-tree').tree('getChildren', level_2[0].target);
                var level_4 = jQuery('#structure-tree').tree('getChildren', level_3[0].target);
            } else {
                //case no event is found
                initZPDataTree(0, 0, 0);
            }
        },
        onBeforeSelect: function(node) {
            var isLeaf = jQuery('#structure-tree').tree('isLeaf', node.target);

            if (isLeaf) {
                return true;
            } else {
                return false;
            }
        },
        onSelect: function(node) {
            clearCoverage();

            var obj = new Object();
            obj.event_id = node.id;

            var zpYearData = jQuery('#zp-layers-tree').tree('getSelected');
            if (zpYearData) {
                var zpFarmingData = jQuery('#zp-layers-tree').tree('getParent', zpYearData.target);

                obj.zp_year = zpYearData.id;
                obj.zp_farming = zpFarmingData.id;
            } else {
                obj.zp_year = 0;
                obj.zp_farming = 0;
            }

            var zpItemData = jQuery('#zp-data-tree').tree('getSelected');
            if(zpItemData)
                obj.plot_id = zpItemData.id;
            else
                obj.plot_id = 0;

            //reload zp item tree
            initZPDataTree(obj.event_id, obj.zp_farming, obj.zp_year);

            //select/unselect map buttons
            jQuery('#map-altitude').linkbutton('unselect');
            jQuery('#map-coverage').linkbutton('select');

            TF.Rpc.Coverage.CoverageMap.initMap(obj)
            .done(function (data) {
                displayMap(data);
            })
            .fail(function (errorObj) {

            });
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initEventEditFields() {

    jQuery('#event-color > input').spectrum({
        showInput: false,
        chooseText: "Избери",
        cancelText: "Отказ"
    });

    jQuery('#border-color > input').spectrum({
        showInput: false,
        chooseText: "Избери",
        cancelText: "Отказ"
    });

    jQuery('#transparency > input').numberspinner({
        min: 0,
        max: 100,
        editable: true
    });
}

function reloadEventLayer() {
    var obj = new Object();

    var eventData = jQuery('#structure-tree').tree('getSelected');
    obj.file_id = eventData.id;

    var zpYearData = jQuery('#zp-layers-tree').tree('getSelected');
    if (zpYearData) {
        var zpFarmingData = jQuery('#zp-layers-tree').tree('getParent', zpYearData.target);

        obj.zp_year = zpYearData.id;
        obj.zp_farming = zpFarmingData.id;
    } else {
        obj.zp_year = 0;
        obj.zp_farming = 0;
    }

    TF.Rpc.Coverage.CoverageMap.initMap(obj)
    .done(function (data) {
        displayMap(data);
    })
    .fail(function (errorObj) {

    });
}

function clearCoverage(){
    removeLayerByName('topic_cov_layer');
    removeLayerByName('topic_cov_layer_1');
}
