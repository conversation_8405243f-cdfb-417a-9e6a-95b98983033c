var map;
var boundsArray;
var layerArray;

var mapControls = {
    line: new OpenLayers.Control.DynamicMeasure(OpenLayers.Handler.Path, {
        persist: false,
        geodesic: true
    }),
    polygon: new OpenLayers.Control.DynamicMeasure(OpenLayers.Handler.Polygon, {
        persist: false,
        geodesic: true
    }),
    zoomin: new OpenLayers.Control.ZoomBox(
            {
                title: "Zoom in box",
                out: false
            }
    ),
    zoomout: new OpenLayers.Control.ZoomBox(
            {
                title: "Zoom out box",
                out: true
            }
    )
};

function initMap() {
    if (!map) {
        var options = {
            controls: [new OpenLayers.Control.Navigation(), new OpenLayers.Control.ScaleLine({bottomInUnits: 'km'})],
            projection: new OpenLayers.Projection("EPSG:900913")
        };
        map = new OpenLayers.Map('map', options);

        initMapPad();
        map.zoomToExtent(new OpenLayers.Bounds.fromString("125190.6162, 4573142.7188, 631370.3273, 4887149.5823").transform(
                new OpenLayers.Projection("EPSG:32635"),
                map.getProjectionObject())
                );
    }

    var control;
    for (var key in mapControls) {
        control = mapControls[key];
        map.addControl(control);
    }
}

function displayMap(data) {
     initAreaLegend(data['area_legend']);

    if (data['remove_layers']) 
    {
        for(var i = 0; i < data['remove_layers'].length; i++) 
        {
            removeLayerByName(data['remove_layers'][i]);
        }
    }
    
    if (data['layers']) {
        //get new layers data
        layers = data['layers'];
        for (var i = 0; i < layers.length; i++) {
            var layerExtent = new OpenLayers.Bounds.fromString(layers[i].extent).transform(
                    new OpenLayers.Projection("EPSG:32635"),
                    map.getProjectionObject()
                    );

            const isSubUrl = wmsServer.indexOf('?') !== -1;
            const urlSeparator = isSubUrl ? '&' : '?';        

            var layerData = new OpenLayers.Layer.WMS(
                    layers[i].name,
                    `${wmsServer}${urlSeparator}map=${mapPath}${groupID}.map`,
                    {
                        layers: layers[i].name,
                        format: 'image/png',
                        transparent: "true"
                    }
            );
            map.addLayer(layerData);
            map.zoomToExtent(layerExtent);
            layerData.redraw(true);
        }
    }

    if (data['color_array']) {
        jQuery('#height-legend').tree({
            data: data['color_array'],
            formatter: function(node) {
                if (node.color)
                    return "<div style='width:13px;height:13px;background-color:#" + node.color + ";margin-top:3px;float:left;margin-right:3px;'></div>" + node.name;
                else if (node.text)
                    return node.text;
            }
        });
        jQuery('#altitude-legend').panel('open');
    } else {
        jQuery('#altitude-legend').panel('close');
    }
}

function removeLayersByNamePart(name_string)
{
//remove old layers
    var iterator = 1;
    for (var i = 0; i < map.layers.length; i++)
    {
        var layerName = map.layers[iterator].name;
        if (layerName.indexOf(name_string) != -1) {
            map.removeLayer(map.layers[iterator]);
        } else {
            iterator++;
        }
    }
}

function removeLayerByName(layer_name) {
    for (var i = 0; i < map.layers.length; i++)
    {
        if (map.layers[i].name == layer_name) 
            map.removeLayer(map.layers[i]);
    }
}

function initAreaLegend(area_data) {
    jQuery('#area-legend-grid').datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        fitColumns: true,
        data: area_data,
        border: true,
        scrollbarSize: 0,
        singleSelect: true,
        border: false,
                fit: true,
        columns: [
            [
                {
                    field: 'name',
                    title: '<b>Вид</b>',
                    sortable: true,
                    width: 150
                }, {
                    field: 'area',
                    title: '<b>дка</b>',
                    sortable: true,
                    width: 100
                }
            ]
        ]
    });
}

var bingApiKey = "AiWEso3-IjWyX1aZMuep9Sjl62D6FUMqv8qQGpV-kgbW0qWk61to4nrqHh-2D5HL";

function initMapPad(specific_map_type)
{
    var chosenMapType;

    //on init map type will not be specified
    if (specific_map_type == undefined)
    {
        chosenMapType = store.get('map_pad') || 1;
    }
    //when map type is changed specific_map_type will have the value of map type
    else {
        chosenMapType = parseInt(specific_map_type);
    }

    var layerMapPad;

    switch (chosenMapType)
    {
        case 2:
            layerMapPad = new OpenLayers.Layer.Bing({
                key: bingApiKey,
                type: "Aerial",
                name: "MapPad"
            });
            break;
        case 3:
            layerMapPad = new OpenLayers.Layer.Bing({
                key: bingApiKey,
                type: "Road",
                name: "MapPad"
            });
            break;
        case 4:
            layerMapPad = new OpenLayers.Layer.Google(
                    "MapPad",
                    {type: google.maps.MapTypeId.TERRAIN}
            );
            break;
        case 5:
            layerMapPad = new OpenLayers.Layer.Google(
                    "MapPad",
                    {numZoomLevels: 20}
            );
            break;
        case 6:
            layerMapPad = new OpenLayers.Layer.Google(
                    "MapPad",
                    {type: google.maps.MapTypeId.HYBRID, numZoomLevels: 20}
            );
            break;
        case 7:
            layerMapPad = new OpenLayers.Layer.Google(
                    "MapPad",
                    {type: google.maps.MapTypeId.SATELLITE, numZoomLevels: 22}
            );
            break;
        case 8:
            layerMapPad = new OpenLayers.Layer.OSM();
            break;
        case 9:
            layerMapPad = new OpenLayers.Layer.WMS(
                'MapPad',
                imagesWMSServer + "?map=" + '/var/www/satellite_processor/maps/geo_scan.map',
                //wmsServer + "?map=" + mapPath + groupID + '.map',
                {
                    layers: 'geo_scan'
                },
                {
                    numZoomLevels: 18
                });
            break;
        default: // default is bing aerial with labels
            layerMapPad = new OpenLayers.Layer.Bing({
                key: bingApiKey,
                type: "AerialWithLabels",
                name: "MapPad"
            });
            break;
    }

    //specific map type will be given only on reload
    //on first load(init) specific map type should be undefined
    if (specific_map_type == undefined)
    {
        map.addLayer(layerMapPad);
    }
    else {
        map.addLayer(layerMapPad);
        map.setLayerIndex(map.layers[map.layers.length - 1], 0);
        map.removeLayer(map.layers[1]);
        map.layers[0].redraw(true);
    }
}
