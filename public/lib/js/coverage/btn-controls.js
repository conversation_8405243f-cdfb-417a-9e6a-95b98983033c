var markedForEditZPLayerID;
function initButtonControls() {
    jQuery('#add-coverage-layer').linkbutton({
        iconCls: 'icon-add',
        plain: true
    });
    jQuery('#add-coverage-layer-usb').linkbutton({
        iconCls: 'icon-tractor',
        plain: true
    });

    jQuery('#delete-coverage-file').linkbutton({
        iconCls: 'icon-delete',
        plain: true
    });

    jQuery('#edit-event').linkbutton({
        iconCls: 'icon-tools',
        plain: true
    });

    jQuery('#edit-zp').linkbutton({
        iconCls: 'icon-tools',
        plain: true
    });

    jQuery('#event-edit-btn').linkbutton({
        iconCls: 'icon-save'
    });
    jQuery('#zp-edit-btn').linkbutton({
        iconCls: 'icon-save'
    });

    jQuery('#add-coverage-layer').bind('click', function() {

        //No Rights
        if(!hasAgroRightsRW) {
            EasyUIRPCLoaders.messagerNoRightsRW();
            return false;
        }

        initCoverageUpliad();
        jQuery('#win-add-file').window('open');
        //jQuery('#win-add-usb-file').window('open');

        return false;
    });
    jQuery('#add-coverage-layer-usb').bind('click', function() {

        //No Rights
        if(!hasAgroRightsRW) {
            EasyUIRPCLoaders.messagerNoRightsRW();
            return false;
        }
        jQuery('#win-add-file-usb').window('open');
        initModemFilesGrid();
        return false;
    });

    jQuery('#edit-event').bind('click', function() {
        var eventData = jQuery('#structure-tree').tree('getSelected');

        if (eventData) {
           TF.Rpc.Coverage.FileStructureTree.markForEdit(eventData.id)
           .done(function (data) {
            initEventEditFields();
            setEventEditFields(data);
            jQuery('#win-edit-event').window('open');
           })
           .fail(function (errorObj) {

           });
        } else {
            jQuery.messager.alert('Грешка', 'Не е намерен запис за редактиране.');
        }

        return false;
    });

    jQuery('#edit-zp').bind('click', function() {
        var yearData = jQuery('#zp-layers-tree').tree('getSelected');
        var found = false;

        if (yearData) {
            var farmingData = jQuery('#zp-layers-tree').tree('getParent', yearData.target);

            var obj = new Object();
            obj.farming = farmingData.id;
            obj.year = yearData.id;

            if (farmingData) {
                TF.Rpc.Coverage.ZPLayersTree.markZPForEdit(obj)
                .done(function (data) {
                    markedForEditZPLayerID = data.id;
                    initZPEditFields();
                    setZPEditFieldsData(data);
                    jQuery('#win-edit-zp').window('open');
                })
                .fail(function (errorObj) {

                });

                found = true;
            } else {
                found = false;
            }
        } else {
            found = false;
        }

        if(found == false)
            jQuery.messager.alert('Грешка', 'Не е намерен запис за редактиране.');

        return false;
    });

    jQuery('#zp-edit-btn').bind('click', function() {
        var plotData = jQuery('#zp-data-tree').tree('getSelected');
        var obj = getZPEditFieldsData();

        if (plotData)
            obj.plot = plotData.id;
        else
            obj.plot = 0;

        TF.Rpc.Coverage.ZPLayersTree.saveZP(obj)
        .done(function (data) {
            jQuery('#zp-layers-tree').tree('loadRpc');
            jQuery('#win-edit-zp').window('close');
            redrawLayerZP(data);
        })
        .fail(function (errorObj) {

        });
    });

    jQuery('#event-edit-btn').bind('click', function() {
        var editObj = getEventEditFields();
        TF.Rpc.Coverage.FileStructureTree.saveEvent(editObj)
        .done(function (data) {
            reloadEventLayer();
            jQuery('#structure-tree').tree('loadRpc');
            jQuery('#win-edit-event').window('close');
        })
        .fail(function (data) {

        });
    });

    jQuery('#map-altitude').bind('click', function() {
        jQuery('#map-altitude').linkbutton('select');
        jQuery('#map-coverage').linkbutton('unselect');

        var obj = new Object();
        var eventData = jQuery('#structure-tree').tree('getSelected');

        if (eventData)
            obj.event_id = eventData.id;
        else
            obj.event_id = 0;

        var zpYearData = jQuery('#zp-layers-tree').tree('getSelected');
        if (zpYearData) {
            var zpFarmingData = jQuery('#zp-layers-tree').tree('getParent', zpYearData.target);

            obj.zp_year = zpYearData.id;
            obj.zp_farming = zpFarmingData.id;
        } else {
            obj.zp_year = 0;
            obj.zp_farming = 0;
        }

        TF.Rpc.Coverage.CoverageMap.initAltitudeMap(obj)
        .done(function (data) {
            displayMap(data);
        })
        .fail(function (errorObj) {

        });

        return false;
    });

    jQuery('#map-coverage').bind('click', function() {
        jQuery('#map-coverage').linkbutton('select');
        jQuery('#map-altitude').linkbutton('unselect');

        var getSelected = jQuery('#structure-tree').tree('getSelected');

        if (getSelected) {
            var obj = new Object();

            obj.event_id = getSelected.id;

            var zpYearData = jQuery('#zp-layers-tree').tree('getSelected');
            if (zpYearData) {
                var zpFarmingData = jQuery('#zp-layers-tree').tree('getParent', zpYearData.target);

                obj.zp_year = zpYearData.id;
                obj.zp_farming = zpFarmingData.id;
            } else {
                obj.zp_year = 0;
                obj.zp_farming = 0;
            }

            TF.Rpc.Coverage.CoverageMap.initMap(obj)
            .done(function (data) {
                displayMap(data);
            })
            .fail(function (errorObj) {

            });
        }

        return false;
    });

    jQuery('#tool-panzoom').bind('click', function() {
        var options = jQuery('#tool-panzoom').linkbutton('options');
        if (!options.disabled)
        {
            unselectToolbarButtons();
            jQuery('#tool-panzoom').linkbutton('select');
            chooseControl('none');
        }
        return false;
    });

    jQuery('#tool-measure-line').bind('click', function() {
        var options = jQuery('#tool-measure-line').linkbutton('options');
        if (!options.disabled)
        {
            unselectToolbarButtons();
            jQuery('#tool-measure-line').linkbutton('select');
            chooseControl('line');
        }
        return false;
    });

    jQuery('#tool-measure-polygon').bind('click', function() {
        var options = jQuery('#tool-measure-polygon').linkbutton('options');
        if (!options.disabled)
        {
            unselectToolbarButtons();
            jQuery('#tool-measure-polygon').linkbutton('select');
            chooseControl('polygon');
        }
        return false;
    });

    jQuery('#tool-zoomout').bind('click', function() {
        var options = jQuery('#tool-zoomout').linkbutton('options');
        if (!options.disabled)
        {
            unselectToolbarButtons();
            jQuery('#tool-zoomout').linkbutton('select');
            chooseControl('zoomout');
        }
        return false;
    });

    jQuery('#tool-zoomin').bind('click', function() {
        var options = jQuery('#tool-zoomin').linkbutton('options');
        if (!options.disabled)
        {
            unselectToolbarButtons();
            jQuery('#tool-zoomin').linkbutton('select');
            chooseControl('zoomin');
        }
        return false;
    });
}

function unselectToolbarButtons()
{
    jQuery('#tool-zoomin').linkbutton('unselect');
    jQuery('#tool-zoomout').linkbutton('unselect');
    jQuery('#tool-measure-line').linkbutton('unselect');
    jQuery('#tool-measure-polygon').linkbutton('unselect');
    jQuery('#tool-panzoom').linkbutton('unselect');
}

function chooseControl(name)
{
    for (key in mapControls) {
        var control = mapControls[key];
        if (name == key) {
            control.activate();
        } else {
            control.deactivate();
        }
    }
}

function setEventEditFields(data) {
    jQuery("#event-color > input").spectrum("set", data.color);
    jQuery("#border-color > input").spectrum("set", data.border_color);

    if (data.border_only) {
        jQuery('#onlyborder > input').prop('checked', true);
    };
    jQuery('#transparency > input').numberspinner('setValue', data.transparency);
}


function getEventEditFields() {
    var eventEditObject = new Object();
    var selected = jQuery('#structure-tree').tree('getSelected');

    eventEditObject = {
        event_id: selected.id,
        color: jQuery('#event-color > input').spectrum("get").toHex(),
        border_color: jQuery('#border-color > input').spectrum("get").toHex(),
        transparency: jQuery('#transparency > input').numberspinner('getValue'),
        border_only: jQuery('#onlyborder > input').is(':checked') ? true : false
    };

    return eventEditObject;
}

function setZPEditFieldsData(data) {
    var style = JSON.parse(data.style);

    jQuery("#zp-color > input").spectrum("set", style.color);
    jQuery("#zp-border-color > input").spectrum("set", style.border_color);

    if (style.border_only) {
        jQuery('#zp-onlyborder > input').prop('checked', true);
    }else{
        jQuery('#zp-onlyborder > input').prop('checked', false);
    }

    if (style.tags) {
        jQuery('#tags > input').prop('checked', true);
    }else{
        jQuery('#tags > input').prop('checked', false);
    }

    jQuery('#zp-transparency > input').numberspinner('setValue', style.transparency);

    jQuery('#labelName').combobox({
        url: 'index.php?common-rpc=label-names-combobox',
        rpcParams: [{
            selected: true,
            layer_type: data.layer_type,
            layer_id: data.id
        }],
        valueField: 'key',
        textField: 'name',
        multiple: true,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
    });

    jQuery('#label-size > input').numberspinner({
        min: 1,
        max: 25,
        precision:1,
        editable: true
    });
}

function getZPEditFieldsData() {
    var layerEditObject = new Object();

    layerEditObject = {
        editZPID: markedForEditZPLayerID,
        color: jQuery('#zp-color > input').spectrum("get").toHex(),
        border_color: jQuery('#zp-border-color > input').spectrum("get").toHex(),
        transparency: jQuery('#zp-transparency > input').numberspinner('getValue'),
        border_only: jQuery('#zp-onlyborder > input').is(':checked') ? true : false,
        tags: jQuery('#tags > input').is(':checked') ? true : false,
        label_name: jQuery('#labelName').combobox('getValues'),
        label_size: jQuery('#label-size > input').numberspinner('getValue'),
    };

    return layerEditObject;
}
