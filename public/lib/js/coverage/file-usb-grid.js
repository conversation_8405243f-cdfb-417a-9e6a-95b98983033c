function initModemFilesGrid(){
    var modemFilesList = jQuery('#modem-uploader');
modemFilesList.datagrid({
    autoRowHeight: true,
    checkbox:true,
    autoRowWidth: true,
    url: 'index.php?coverage-rpc=coverage-files-tree',
    rpcMethod:  "getModemFiles",
    singleSelect: true,
    pagination: true,
    idField: "id",
    fit:  true,
    border: false,
    rpcParams: [{
        user_id:userID
    }],
    frozenColumns:[[
        {
            field:'ck',
            checkbox:true
        }
    ]],
    columns: [[
        {
            field: 'device',
            title: '<b>Устройство</b>',
            sortable: true,
            width: 230,
            sortable:true
        },
        {
            field: 'filename',
            title: '<b>Файл</b>',
            sortable: true,
            width: 220,
            sortable:true
        },
        {
            field: 'created_at',
            title: '<b>Дата</b>',
            sortable: true,
            width: 150,
            sortable:true
        },
    ]],
    toolbar: [{
        id: 'btn_add_devices',
        text: 'Зареди обработка',
        class: 'right',
        iconCls: 'icon-add',
        handler: function() {
            var selectedItems = modemFilesList.datalist('getChecked');
            if(selectedItems && selectedItems.length) {
               var selected = selectedItems[0];
               TF.Rpc.Coverage.FilesTree.loadModemFiles(
                    selected
                ).done(function (rowData) {
                   modemFilesList.datalist('uncheckAll');
                   initFilesTree();
                });
            }
            else {
                jQuery.messager.alert('Грешка', 'Не е избран файл!', 'error');
            }
        }
    }
    ],
    onSelect: function(rowIndex, rowData) {
        // removeDevice(rowData);
    },
    onLoadSuccess:function() {
    },
    loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
    loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
});
    modemFilesList.datagrid().find('.easyui-linkbutton').each(function(){
    alert($(this).attr('row-id'));
});
}
