function initZPDataTree(event, farming, year) {
    jQuery('#zp-data-tree').tree({
        url: 'index.php?coverage-rpc=coverage-zp-data-tree',
        rpcParams: [{
            event_id: event,
            farming: farming,
            year: year
        }],
        animate: true,
        lines: true,
        onLoadSuccess: function() {
            if(jQuery('#zp-data-tree').tree('getData') == null)
            {
                var obj = new Object();
                obj.event = event;

                TF.Rpc.Coverage.ZPLayerDataTree.requestZPLayerData(obj)
                .done(function (data) {
                    displayMap(data);
                })
                .fail(function (errorObj) {

                });
            }
        },
        onBeforeSelect: function(node) {
            var isLeaf = jQuery('#structure-tree').tree('isLeaf', node.target);

            if (isLeaf) {
                return true;
            } else {
                return false;
            }
        },
        onSelect: function(node) {
            var obj = new Object();

            obj.farming = farming;
            obj.year = year;
            obj.event = event;
            obj.plot = node.id;
            TF.Rpc.Coverage.ZPLayerDataTree.requestZPLayerData(obj)
            .done(function (data) {
                displayMap(data);
            })
            .fail(function (errorObj) {

            });
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}
