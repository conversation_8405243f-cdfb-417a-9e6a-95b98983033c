function initZPLayersTree() {
    jQuery('#zp-layers-tree').tree({
        url: 'index.php?coverage-rpc=coverage-zp-layers-tree',
        animate: true,
        lines: true,
        formatter: function(node) {
            if (node.color)
                return "<div style='width:13px;height:13px;background-color:#" + node.color + ";margin-top:3px;float:left;margin-right:3px;'></div>" + node.text;
            else
                return node.text;
        },
        onBeforeSelect: function(node) {
            var isLeaf = jQuery('#zp-layers-tree').tree('isLeaf', node.target);

            if (!isLeaf) {
                return false;
            }
        },
        onSelect: function(node) {
            var obj = new Object();

            obj.year = node.id;
            var farmingData = jQuery('#zp-layers-tree').tree('getParent', node.target);
            obj.farming = farmingData.id;

            var eventData = jQuery('#structure-tree').tree('getSelected');
            if (eventData) {
                obj.event = eventData.id;
            } else {
                obj.event = 0;
            }

            initZPDataTree(obj.event, obj.farming, obj.year);
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function addZPLayer(data) {

    //init legend
    initAreaLegend(data['area_legend']);

    if (data['extent'] != '' && data['name'] != '')
    {
        removeLayersByNamePart(data['name']);

        var layerExtent = new OpenLayers.Bounds.fromString(data['extent']).transform(
                new OpenLayers.Projection("EPSG:32635"),
                map.getProjectionObject()
                );

        var layerBounds = new OpenLayers.Layer.WMS(
                data['name'],
                wmsServer + "?map=" + mapPath + groupID + '.map',
                {
                    layers: data['name'],
                    format: 'image/png',
                    transparent: "true"
                }
        );

        map.addLayer(layerBounds);
        map.zoomToExtent(layerExtent);

        layerBounds.redraw(true);
    }
}

function initZPEditFields() {

    jQuery('#zp-color > input').spectrum({
        showInput: false,
        chooseText: "Избери",
        cancelText: "Отказ"
    });


    jQuery('#zp-border-color > input').spectrum({
        showInput: false,
        chooseText: "Избери",
        cancelText: "Отказ"
    });

    jQuery('#zp-transparency > input').numberspinner({
        min: 0,
        max: 100,
        editable: true
    });
}

function redrawLayerZP(data) {

    var layers = map.layers;
    var find = false;

    for(var i = 0; i < layers.length; i++)
    {
        if(layers[i]['name'] == data['layer_name'])
        {
            console.info(map.layers[i])
            map.layers[i].redraw(false);
            break;
        }
    }
}
