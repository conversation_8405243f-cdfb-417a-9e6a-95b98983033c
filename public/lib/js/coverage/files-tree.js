function initFilesTree() {
    jQuery('#files-tree').tree({
        url: 'index.php?coverage-rpc=coverage-files-tree',
        animate: true,
        lines: true,
        onSelect: function(node) {
            initFileStructureTree(node.id);
            clearCoverage();
        },
        onLoadSuccess: function() {},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#delete-coverage-file').bind('click', function() {
        var fileData = jQuery('#files-tree').tree('getSelected');

        if (fileData) {
            jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да премахнете този файл?', function(r) {
                if (r) {
                    TF.Rpc.Coverage.FilesTree.deleteCoverageFile(fileData.id)
                    .done(function (data) {
                        jQuery('#files-tree').tree('reload');
                        clearCoverage();
                        jQuery('#structure-tree').tree('reload');
                    })
                    .fail(function (errorObj) {

                    });
                }
            });
        } else {
            jQuery.messager.alert('Грешка', 'Не е намерен файл.')
        }

        return false;
    })
}
