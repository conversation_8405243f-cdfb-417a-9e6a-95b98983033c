function iniExpensesGrid () {
	var expensesGrid = jQuery('#diary-configs-expenses');
	var isGridInited = expensesGrid.data().hasOwnProperty('datagrid');
	var winAddEditExpenses = jQuery('#win-add-edit-expenses');

	var diaryExpensesActivity = jQuery('#diary-expenses-activity');
	var diaryExpensesPerformer = jQuery('#diary-expenses-performer');
	var diaryExpensesValidFrom = jQuery('#diary-expenses-valid-from');
	var diaryExpensesValidTo = jQuery('#diary-expenses-valid-to');
	var diaryExpensesPrice = jQuery('#diary-expenses-price');

	if (isGridInited) {
		expensesGrid.datagrid('loadRpc');
		return;
	}

	expensesGrid.datagrid({
		nowrap: true,
		singleSelect: true,
		pageSize: 20,
		fit: true,
		fitColumns: true,
		showFooter: true,
		border: false,
		url: 'index.php?diary-rpc=diary-expenses-grid',
		idField: 'id',
		columns: [[
			{
				field: 'activity',
				title: '<b>Вид дейност</b>',
				width: 145
			},
			{
				field: 'performer',
				title: '<b>Изпълнител</b>',
				width: 145
			},
			{
				field: 'valid_from',
				title: '<b>Валидност от</b>',
				width: 145
			},
			{
				field: 'valid_to',
				title: '<b>Валидност до</b>',
				width: 145
			},
			{
				field: 'price',
				title: '<b>Лева/дка</b>',
				width: 145
			},
		]],
		toolbar: [{
			id: 'btn_add_expenses',
			text: 'Нов',
			iconCls: 'icon-add',
			handler: function() {
                is_edit = false;
                diaryExpensesActivity.combobox('reload');
                diaryExpensesPerformer.combobox('reload');
				winAddEditExpenses.window('open');
				winAddEditExpenses.window('setTitle', 'Добавяне на разход');
				winAddEditExpenses.data('isEditWin', false);
			}
		},
		{
			id: 'btn_edit_expenses',
			text: 'Редакция',
			iconCls: 'icon-edit',
			handler: function() {
				var selected = expensesGrid.datagrid('getSelected');
                if(!selected) {
                    jQuery.messager.alert('Грешка', 'Моля изберете запис.');
                    return;
                }
                is_edit = true;

				var expenseId = selected.id;

                diaryExpensesActivity.combobox('reload');
                diaryExpensesPerformer.combobox('reload');
				winAddEditExpenses.window('open');
				winAddEditExpenses.window('setTitle', 'Редактиране на разход');
				winAddEditExpenses.data('isEditWin', true);

				TF.Rpc.Diary.DiaryExpenses.loadExpenses(expenseId)
					.done(function (data) {
						diaryExpensesActivity.combobox('setValue', data.activity);
						diaryExpensesPerformer.combobox('setValue', data.performer);
						diaryExpensesValidFrom.datebox('setValue', data.valid_from);
						diaryExpensesValidTo.datebox('setValue', data.valid_to);
						diaryExpensesPrice.numberbox('setValue', data.price);
					});
			}
		}, {
			id: 'btn_remove_expenses',
			text: 'Изтриване',
			iconCls: 'icon-delete',
			handler: function() {
				var selected = expensesGrid.datagrid('getSelected');
				var expenseId = selected.id;
				TF.Rpc.Diary.DiaryExpenses.deleteExpenses(expenseId)
					.done(function (data) {
						winAddEditExpenses.window('close');
						expensesGrid.datagrid('loadRpc');
					});
			}
		}],
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}
