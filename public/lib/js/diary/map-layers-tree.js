var context_layer = undefined;

function initAllLayersTree() {
    jQuery('#all-layers-tree').tree({
        url: 'index.php?diary-rpc=all-layers-tree',
        animate: true,
        lines: true,
        checkbox: true,
        dnd: false,
        cascadeCheck: false,
        fit: true,
        rpcParams: [{
            allowable: false,
            system_only: true,
            vps: true
        }],
        onBeforeSelect: function (node) {
            if ((!node['attributes'].is_system && node['attributes'].level != 3) || (node['attributes'].is_system && node['attributes'].level != 2)) {
                return false;
            }
        },
        onSelect: function (node) {
            if (node.checked == false || node.checked == undefined) {
                jQuery(this).tree('check', node.target);
            }
            if (!node['attributes'].is_system) {
                jQuery('#active-layer-menubutton > span .l-btn-text').html(node['attributes'].farming + ' / ' + node['attributes'].year + ' / ' + node['attributes'].name);
            } else {
                jQuery('#active-layer-menubutton > span .l-btn-text').html(node['attributes'].name);
            }
        },
        onBeforeCheck: function (node) {
            var selected = jQuery(this).tree('getSelected');

            if (selected && selected.id == node.id && node.checked == true) {
                jQuery.messager.alert('Грешка', 'Не можете да премахнете активен слой!');
                return false;
            }
        },
        onCheck: function (node) {
            updateKSVMenu();
            if (node['attributes'].level == 3 || (node['attributes'].is_system && node['attributes'].level == 2)) {
                if (node.checked == true) {
                    loadMapLayers(node);
                } else {
                    removeLayerByName(node['attributes'].layer_name);
                }
            }

        },
        onContextMenu: function (e, node) {
            e.preventDefault();
            context_layer = node.id;
            if (!node.checked) {
                jQuery(this).tree('check', node.target);
            }
            if ((node['attributes'].is_system == false && node['attributes'].level == 3) || (node['attributes'].is_system == true && node['attributes'].level == 2)) {
                jQuery('#all-layers-tree-cm').menu('show', {
                    left: e.pageX,
                    top: e.pageY
                });
            }
        },
        onLoadSuccess: function () {
            updateKSVMenu();
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function updateKSVMenu() {
    var active_layer = jQuery('#all-layers-tree').tree('getSelected');
    var menu = jQuery('#kvs-filter-menu-btn');
    if (active_layer == null) {
        menu.menubutton('disable');
        return false;
    }
    if (active_layer.attributes.layer_name === 'layer_kvs') {
        menu.menubutton('enable');
    }else{
        menu.menubutton('disable');
    }
}
