Namespace('TF.Rpc.Diary');

var EVENT_PLANNED = 1;
var EVENT_COMPLETE = 2;

var is_edit = false;
var trackMachineID;
var trackDate;
var trackTimeFrom;
var trackTimeTo;
var product_datagrid;
var produceDatagrid;
var warehouseQuantity = 0.00;
var editingRow = false;
var selectedIndex = null;
var productRequiredFields = [];

jQuery(function () {

    jQuery('#diary-plots-report-by-fuel').bind('click', function() {
        displayDiaryPlotsByFuelReport();
    });

    jQuery('#diary-plots-report-by-seeds').bind('click', function() {
        displayDiaryPlotsByProductReport('Справка по семена', ['seeds']);
    });

    jQuery('#diary-plots-report-by-substance').bind('click', function() {
        displayDiaryPlotsByProductReport('Справка по препарати', ['chemical_treatment', 'fertilizer'])
    });

    jQuery('#diary-plots-report-by-other-products').bind('click', function() {
        displayDiaryPlotsByProductReport('Справка по други продукти', ['other'])
    });

    jQuery('#diary-plots-report-by-produces').bind('click', function() {
        displayDiaryPlotsReportByProduce()
    });

    jQuery('#btn-diary-reports').bind('click', function() {
        initPlotsReportFields();
        jQuery('#win-choose-diary-report').window('open');
    });


    jQuery('#export-xls-report').bind('click', function() {
        exportXLSDiaryReport(false);
    });

    jQuery('#export-xls-report-page').bind('click', function() {
        exportXLSDiaryReport(true);
    });

    var product_grid_data = null;
    /* we make an ajax call to get data from diary_config table, to be used in the product datagrid */
    initEditorsValuesForGrid();
    initZPTree();
    initComboTree();
    initMap();
    initAllLayersTree();
    initVectorLayers();
    setUserLastLogin();
    checkForPendingProducts();

    function checkForPendingProducts() {
        var requestObj = {status: 'pending', request_type: 7};
        TF.Rpc.Diary.DiaryConfigs.getConfigs(requestObj)
            .done(function (data) {
                if(data.rows.length > 0) {
                    jQuery('#diary-settings-pending-substance-types').show();
                } else {
                    jQuery('#diary-settings-pending-substance-types').hide();
                }
            })
            .fail(function (errorObj) {
                jQuery.messager.alert('Грешка', errorObj.getMessage());
            });
    }

    /* array of window widths for each tab */
    var widths = [440, 440, 1170, 660, 740, 680];

    if(hasWarehouseRights){
        widths[2] = 1250; //Change the width because there is one more column
    }

    //MAIN WINDOW
    jQuery('#win-add-edit-event').window({
        onLoad: function () {
            initEventTypeCombobox();
        },
        onOpen: function () {
            var tab = jQuery('#events_win_tabs');
            tab.tabs('select', 0);
            var index = tab.tabs('getTabIndex', tab.tabs('getSelected'));
            if (jQuery(this).window('options').top < 0) jQuery(this).window('move', {top: 50});
            tab.tabs('resize', {width: "auto", height: "auto"});
            jQuery(this).window('resize', {width: widths[index], height: "auto"});
            jQuery(this).window('center');
        },
        onClose: function () {
            selectedIndex = null;
        }
    });

    //TABS
    jQuery('#events_win_tabs').tabs({
        border: false,
        onSelect: function (title, index) {
            var win = jQuery('#win-add-edit-event');
            win.window('resize', {width: widths[index], height: "auto"});
            jQuery(this).tabs('resize', {width: "auto", height: "auto"});
            if ([2,3].includes(index)) jQuery('#events_win_tabs').find('.datagrid-wrap').height(240)
            win.window('resize', {width: "auto", height: "auto"});
            win.window('center');
        }
    });

    var diaryExpensesActivity = jQuery('#diary-expenses-activity');
    var diaryExpensesPerformer = jQuery('#diary-expenses-performer');
    var diaryExpensesValidFrom = jQuery('#diary-expenses-valid-from');
    var diaryExpensesValidTo = jQuery('#diary-expenses-valid-to');
    var diaryExpensesPrice = jQuery('#diary-expenses-price');
    var winAddEditExpenses = jQuery('#win-add-edit-expenses');
    var expensesGrid = jQuery('#diary-configs-expenses');
    var diaryExpensesReportBtn = jQuery('#diary-expenses-report');
    var diarySettingsWin = jQuery('#win-diary-settings');
    var plotsTrackWindow = jQuery('#win-diary-plot-tracks');
    var diaryConfigsGrid = jQuery('#diary-configs-tables');
    plotsTrackWindow.window({closed: true});
    winAddEditExpenses.window({
        onClose: function () {
            diaryExpensesActivity.combobox("reset");
            diaryExpensesPerformer.combobox("reset");
            diaryExpensesValidFrom.datebox("reset");
            diaryExpensesValidTo.datebox("reset");
            diaryExpensesPrice.numberbox("reset");
        }
    });
    diarySettingsWin.window({
        onBeforeOpen: function () {
            diaryConfigsGrid.datagrid('clearSelections');
            var options = diaryConfigsGrid.datagrid('options');
            if (!options.rpcParams || !options.rpcParams[0]) return;
            var frozen_cols = diaryConfigsGrid.datagrid('getColumnFields', true);
            if (options.rpcParams[0].request_type == 7) {
                //hide ck boxes for substance_types
                for (var i = 0; i < frozen_cols.length; i++) {
                    if (frozen_cols[i] == 'ck') {
                        diaryConfigsGrid.datagrid('hideColumn', 'ck');
                        return true;
                    }
                }
            } else {
                if (frozen_cols.length > 0) diaryConfigsGrid.datagrid('showColumn', 'ck');
            }
        }
    });
    if (isAdmin) {
        diaryExpensesReportBtn.show();
    }

    if (!isAdmin) {
		jQuery('#diary-settings-expenses').hide().prev('.menu-sep').hide();
	}

    //event types
    jQuery('#diary-settings-event-types').bind('click', function () {
        //change window options
        diarySettingsWin.window('resize', {
            width: 300,
            height: 400
        });
        diarySettingsWin.window('center');
        diarySettingsWin.window('setTitle', 'Типове дейности');
        //init grid
        initEventTypesGrid();
        //open window
        diarySettingsWin.window('open');
    });
    jQuery('#btn-save-event-type').bind('click', function () {
        var getChecked = diaryConfigsGrid.datagrid('getChecked');

        var requestObj = {};

        requestObj.name = jQuery('#event-type-name > input').val();
        requestObj.request_type = 1;

        if (is_edit) {
            requestObj.id = getChecked[0].id;
        }
        if (requestObj.name) {
            TF.Rpc.Diary.DiaryAuxiliaryItems.saveItem(requestObj)
                .done(function () {
                    diaryConfigsGrid.datagrid('clearChecked');
                    jQuery('#win-add-edit-event-type').window('close');
                    diaryConfigsGrid.datagrid('reload');
                })
                .fail(function (errorObj) {
                    jQuery.messager.alert('Грешка', errorObj.getMessage());
                });
        } else {
            jQuery.messager.alert('Грешка', 'Моля задайте тип');
        }
    });

    //event subtypes
    jQuery('#diary-settings-event-subtypes').bind('click', function () {
        //change window options
        diarySettingsWin.window('resize', {
            width: 500,
            height: 400
        });
        diarySettingsWin.window('center');
        diarySettingsWin.window('setTitle', 'Видове дейности');
        //init grid
        initEventSubtypesGrid();
        //open window
        diarySettingsWin.window('open');
    });
    jQuery('#btn-save-event-subtype').bind('click', function () {
        if (jQuery('#event-subtype-name > input').val()
            && jQuery('#event-subtype-type > input').combobox('getValue')) {

            var getChecked = diaryConfigsGrid.datagrid('getChecked');
            var requestObj = getEventSubtypeFields();
            requestObj.request_type = 2;
            if (is_edit) {
                requestObj.id = getChecked[0].id;
            }
            TF.Rpc.Diary.DiaryAuxiliaryItems.saveItem(requestObj)
                .done(function (data) {
                    diaryConfigsGrid.datagrid('clearChecked');
                    jQuery('#win-add-edit-event-subtype').window('close');
                    diaryConfigsGrid.datagrid('reload');
                })
                .fail(function (errorObj) {
                    jQuery.messager.alert('Грешка', errorObj.getMessage());
                });
        } else {
            jQuery.messager.alert('Грешка', 'Моля попълнете всички полета!');
        }
    });

    //machine types
    jQuery('#diary-settings-machine-types').bind('click', function () {
        //change window options
        diarySettingsWin.window('resize', {
            width: 500,
            height: 400
        });
        diarySettingsWin.window('center');
        diarySettingsWin.window('setTitle', 'Типове машини');
        //init grid
        initMachineTypesGrid();
        //open window
        diarySettingsWin.window('open');
    });
    jQuery('#btn-save-machine-type').bind('click', function () {
        var getChecked = diaryConfigsGrid.datagrid('getChecked');

        var requestObj = {};

        requestObj.name = jQuery('#machine-type-name > input').val();
        requestObj.request_type = 3;

        if (is_edit) {
            requestObj.id = getChecked[0].id;
        }

        if (requestObj.name) {
            TF.Rpc.Diary.DiaryAuxiliaryItems.saveItem(requestObj)
                .done(function (data) {
                    diaryConfigsGrid.datagrid('clearChecked');
                    jQuery('#win-add-edit-machine-type').window('close');
                    diaryConfigsGrid.datagrid('reload');
                })
                .fail(function (errorObj) {
                    jQuery.messager.alert('Грешка', errorObj.getMessage());
                });
        } else {
            jQuery.messager.alert('Грешка', 'Моля попълнете всички полета!');
        }
    });

    //machines
    jQuery('#diary-settings-machines').bind('click', function () {
        //change window options
        diarySettingsWin.window('resize', {
            width: 1024,
            height: 400
        });
        diarySettingsWin.window('center');
        diarySettingsWin.window('setTitle', 'Машини');
        //init grid
        initMachinesGrid();
        //open window
        diarySettingsWin.window('open');
    });
    jQuery('#btn-save-machine').bind('click', function () {

        var getChecked = diaryConfigsGrid.datagrid('getChecked');

        var requestObj = getMachineEditData();

        requestObj.request_type = 4;

        if (is_edit) {
            requestObj.id = getChecked[0].id;
        }

        if (requestObj.number && requestObj.type_id) {
            TF.Rpc.Diary.DiaryAuxiliaryItems.saveItem(requestObj)
                .done(function (data) {
                    diaryConfigsGrid.datagrid('clearChecked');
                    jQuery('#win-add-edit-machine').window('close');
                    diaryConfigsGrid.datagrid('reload');
                    initMachinesCombobox();
                })
                .fail(function (errorObj) {
                    jQuery.messager.alert('Грешка', errorObj.getMessage());
                });
        } else {
            jQuery.messager.alert('Грешка', 'Полетата за номер и тип са задължителни!');
        }
    });

    //attachment types
    jQuery('#diary-settings-attachment-types').bind('click', function () {
        //change window options

        diarySettingsWin.window('resize', {
            width: 300,
            height: 400
        });
        diarySettingsWin.window('center');
        diarySettingsWin.window('setTitle', 'Типове прикачен инвентар');
        //init grid
        initAttachmentTypesGrid();
        //open window
        diarySettingsWin.window('open');
    });
    jQuery('#btn-save-attachment-type').bind('click', function () {
        var getChecked = diaryConfigsGrid.datagrid('getChecked');

        var requestObj = {};

        requestObj.name = jQuery('#attachment-type-name > input').val();
        requestObj.request_type = 5;

        if (is_edit) {
            requestObj.id = getChecked[0].id;
        }

        if (requestObj.name) {
            TF.Rpc.Diary.DiaryAuxiliaryItems.saveItem(requestObj)
                .done(function (data) {
                    diaryConfigsGrid.datagrid('clearChecked');
                    jQuery('#win-add-edit-attachment-type').window('close');
                    diaryConfigsGrid.datagrid('reload');
                })
                .fail(function (errorObj) {
                    jQuery.messager.alert('Грешка', errorObj.getMessage());
                });
        } else {
            jQuery.messager.alert('Грешка', 'Моля задайте тип');
        }
    });

    //attachments
    jQuery('#diary-settings-attachments').bind('click', function () {
        //change window options
        diarySettingsWin.window('resize', {
            width: 1024,
            height: 400
        });
        diarySettingsWin.window('center');
        diarySettingsWin.window('setTitle', 'Прикачени инвентари');
        //init grid
        initAttachmentsGrid();
        //open window
        diarySettingsWin.window('open');
    });
    jQuery('#btn-save-attachment').bind('click', function () {

        var getChecked = diaryConfigsGrid.datagrid('getChecked');

        var requestObj = getAttachmentEditData();

        requestObj.request_type = 6;

        if (is_edit) {
            requestObj.id = getChecked[0].id;
        }

        if (requestObj.number && requestObj.type_id) {
            TF.Rpc.Diary.DiaryAuxiliaryItems.saveItem(requestObj)
                .done(function (data) {
                    diaryConfigsGrid.datagrid('clearChecked');
                    jQuery('#win-add-edit-attachment').window('close');
                    diaryConfigsGrid.datagrid('reload');
                    initMachineAttachmentCombobox();
                })
                .fail(function (errorObj) {
                    jQuery.messager.alert('Грешка', errorObj.getMessage());
                });
        } else {
            jQuery.messager.alert('Грешка', 'Полетата за номер и тип са задължителни!');
        }
    });

    //substance technics
    jQuery('#diary-settings-substance-technics').bind('click', function () {
        //change window options

        diarySettingsWin.window('resize', {
            width: 300,
            height: 400
        });
        diarySettingsWin.window('center');
        diarySettingsWin.window('setTitle', 'Техники за прилагане на ПРЗ');
        //init grid
        initSubstanceTechnicsGrid();
        //open window
        diarySettingsWin.window('open');
    });
    jQuery('#btn-save-substance-technic').bind('click', function () {
        var getChecked = diaryConfigsGrid.datagrid('getChecked');

        var requestObj = {};

        requestObj.name = jQuery('#substance-technic-name > input').val();
        requestObj.request_type = 8;

        if (is_edit) {
            requestObj.id = getChecked[0].id;
        }
        if (requestObj.name) {
            TF.Rpc.Diary.DiaryAuxiliaryItems.saveItem(requestObj)
                .done(function (data) {
                    jQuery('#win-add-edit-substance-technic').window('close');
                    diaryConfigsGrid.datagrid('reload');
                    initEditorsValuesForGrid();
                })
                .fail(function (errorObj) {
                    jQuery.messager.alert('Грешка', errorObj.getMessage());
                });
        } else {
            jQuery.messager.alert('Грешка', 'Моля задайте тип');
        }
    });

    jQuery('#substance-types > input').combobox({
        data:[
            {key: 'fertilizer', label: 'Тор'},
            {key: 'chemical_treatment', label: 'ПРЗ'},
            {key: 'seeds', label: 'Семена'},
            {key: 'other', label: 'Други'}
        ],
        editable: false,
        textField: 'label',
        valueField: 'key',
        value:''
    });

    //substance types
    jQuery('#diary-settings-substance-types').bind('click', function () {
        //change window options
        diarySettingsWin.window('resize', {
            width: 300,
            height: 400
        });
        diarySettingsWin.window('center');
        diarySettingsWin.window('setTitle', 'Продукти');
        //init grid
        initSubstanceTypesGrid();
        //open window
        diarySettingsWin.window('open');
    });

    //pending substance types
    jQuery('#diary-settings-pending-substance-types').bind('click', function () {
        //change window options
        diarySettingsWin.window('resize', {
            width: 300,
            height: 400
        });
        diarySettingsWin.window('center');
        diarySettingsWin.window('setTitle', 'Чакащи продукти');
        //init grid
        initSubstanceTypesGrid('pending');
        //open window
        diarySettingsWin.window('open');
    });

    jQuery('#btn-save-substance-type').bind('click', function () {
        var row = jQuery('#diary-configs-tables').datagrid('getSelected');
        var product_type = jQuery('#substance-types > input').combobox('getValue');
        var name = jQuery('#substance-type-name > input').val();
        if(!name){
            jQuery.messager.alert('Грешка', "Моля задайте име на продукта");
            return;
        }
        if(!product_type){
            jQuery.messager.alert('Грешка', "Моля изберете тип на продукта");
            return;
        }
        var requestObj = {};
        requestObj.name = name
        requestObj.product_type = product_type
        requestObj.request_type = 7;

        if (is_edit) {
            requestObj.id = row.id;
        }

        TF.Rpc.Diary.DiaryAuxiliaryItems.saveItem(requestObj)
            .done(function () {
                jQuery('#win-add-edit-substance-type').window('close');
                diaryConfigsGrid.datagrid('reload');
                initEditorsValuesForGrid();
            })
            .fail(function (errorObj) {
                jQuery.messager.alert('Грешка', errorObj.getMessage());
            });

    });

    jQuery('#btn-warehouse-items-sync').bind('click', function () {
        var requestObj = {
            date: jQuery('#transaction-date').datebox('getValue'),
            farm: jQuery('#transaction-farm').combobox('getValue')
        };

        if(!requestObj.date) {
            jQuery.messager.alert('Грешка', "Моля изберете дата на изписване");
            return;
        }

        TF.Rpc.Diary.DiaryAuxiliaryItems.syncWarehouseItems(requestObj)
            .done(function (result) {
                var msg = 'Синхронизирани са:' + result.length + ' артикули';
                if(result.length === 1) msg = 'Синхронизиран e:' + result.length + ' артикул';
                jQuery.messager.alert('Успешна операция',msg , 'info');
                jQuery('#win-warehouse-items-sync').window('close');
                jQuery('#diary-configs-tables').datagrid('reload')
            })
            .fail(function (errorObj) {
               jQuery.messager.alert('Грешка', errorObj.getMessage());
            });
    });

    //performers
    jQuery('#diary-settings-performers').bind('click', function () {
        //change window options
        diarySettingsWin.window('resize', {
            width: 600,
            height: 400
        });
        diarySettingsWin.window('center');
        diarySettingsWin.window('setTitle', 'Изпълнители');
        //init grid
        initPerformersGrid();
        //open window
        diarySettingsWin.window('open');
    });
    jQuery('#btn-save-performer').bind('click', function () {
        var getChecked = diaryConfigsGrid.datagrid('getChecked');

        var requestObj = getEditPerformetFieldsData();
        requestObj.request_type = 9;

        if (is_edit) {
            requestObj.id = getChecked[0].id;
        }

        if (requestObj.name && requestObj.perf_title) {

            TF.Rpc.Diary.DiaryAuxiliaryItems.saveItem(requestObj)
                .done(function (data) {
                    jQuery('#win-add-edit-performer').window('close');
                    diaryConfigsGrid.datagrid('reload');
                    initEventPerformerCombobox();
                })
                .fail(function (errorObj) {
                    jQuery.messager.alert('Грешка', errorObj.getMessage());
                });
        } else {
            jQuery.messager.alert('Грешка', 'Полетата за имена и длъжност са задължителни!');
        }
    });

    //units
    jQuery('#diary-settings-units').bind('click', function () {
        //change window options
        diarySettingsWin.window('resize', {
            width: 300,
            height: 400
        });
        diarySettingsWin.window('center');
        diarySettingsWin.window('setTitle', 'Мерни единици');
        //init grid
        initSubstanceUnitsGrid();
        //open window
        diarySettingsWin.window('open');
    });
    jQuery('#btn-save-unit').bind('click', function () {
        var getChecked = diaryConfigsGrid.datagrid('getChecked');

        var requestObj = {};

        requestObj.name = jQuery('#substance-unit-name').val() + '/дка';
        requestObj.request_type = 10;

        if (is_edit) {
            requestObj.id = getChecked[0].id;
        }
        if (requestObj.name) {
            TF.Rpc.Diary.DiaryAuxiliaryItems.saveItem(requestObj)
                .done(function (data) {
                    jQuery('#win-add-edit-units').window('close');
                    diaryConfigsGrid.datagrid('reload');
                    initEditorsValuesForGrid();
                })
                .fail(function (errorObj) {
                    jQuery.messager.alert('Грешка', errorObj.getMessage());
                });
        } else {
            jQuery.messager.alert('Грешка', 'Моля задайте тип');
        }
    });

    //expenses
    jQuery('#diary-settings-expenses').bind('click', function () {
        //change window options
        var diaryExpensesWindow = jQuery('#win-diary-expenses');
        diaryExpensesWindow.window('resize', {
            width: 600,
            height: 400
        });
        diaryExpensesWindow.window('center');
        diaryExpensesWindow.window('setTitle', 'Разходи');
        //init grid
        iniExpensesGrid();
        //open window
        diaryExpensesWindow.window('open');
    });
    jQuery('#diary-expenses-valid-from').datebox({});
    jQuery('#diary-expenses-valid-to').datebox({});
    jQuery('#diary-expenses-performer').combobox({
        url: 'index.php?diary-rpc=diary-configs-combobox',
        rpcParams: [{
            request_type: 9
        }],
        editable: true,
        textField: 'name',
        valueField: 'id',
        groupField: 'group',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    jQuery('#diary-expenses-activity').combobox({
        url: 'index.php?diary-rpc=diary-configs-combobox',
        rpcParams: [{
            request_type: 2
        }],
        editable: true,
        textField: 'name',
        valueField: 'id',
        groupField: 'group',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    jQuery('#diary-expenses-price').numberbox({
        precision: 2,
        min: 0
    });
    jQuery('#diary-expenses-cancel').on('click', function (e) {
        e.preventDefault();
        winAddEditExpenses.window('close');
    });
    jQuery('#diary-expenses-save').on('click', function (e) {
        e.preventDefault();

        var expensesData = {
            activity_id: diaryExpensesActivity.combobox('getValue'),
            performer_id: diaryExpensesPerformer.combobox('getValue'),
            valid_from: diaryExpensesValidFrom.datebox('getValue'),
            valid_to: diaryExpensesValidTo.datebox('getValue'),
            price: diaryExpensesPrice.numberbox('getValue')
        };


        var editWarningMessage = 'Разходите за всички мероприятия свързани'
            + ' с редактираните стойности ще бъдат променени.';

        if (winAddEditExpenses.data('isEditWin')) {
            jQuery.messager.confirm('Внимание', editWarningMessage, function (r) {
                if (!r) {
                    return;
                }
                saveExpenses(expensesData, true);
            });
            return;
        }
        saveExpenses(expensesData);
    });

    /**
     *
     * @param expensesData
     * @param expenseId
     */
    function validateExpensesData(expensesData, expenseId = null) {
        const checkValidFromYmd = new Date(expensesData.valid_from);
        const checkValidToYmd = new Date(expensesData.valid_to);
        const expensesGrid = jQuery('#diary-configs-expenses').datagrid('getData');

        try {
            expensesGrid.rows.forEach(function ({id, performer_id, activity_id, valid_from, valid_to }) {

                //skip row if is edited one
                if (expenseId !== null && expenseId == id) {
                    return false
                }

                const recordValidFromYmd = new Date(valid_from);
                const recordValidToYmd = new Date(valid_to);

                if (performer_id == expensesData.performer_id
                    &&
                    activity_id == expensesData.activity_id
                    &&
                    (
                        (recordValidFromYmd <= checkValidFromYmd && checkValidFromYmd <= recordValidToYmd)
                        ||
                        (recordValidFromYmd <= checkValidToYmd && checkValidToYmd <= recordValidToYmd)
                        ||
                        (checkValidFromYmd <= recordValidFromYmd && recordValidFromYmd <= checkValidToYmd)
                    )

                ) {
                    throw new Error();
                }
            })
        } catch (e) {
            jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.EXPENSE_ALREADY_EXISTS.message, 'error')
        }
    }

    function saveExpenses(expensesData, isUpdate) {

        var expenseId;

        if (isUpdate) {
            var selected = expensesGrid.datagrid('getSelected');
            expenseId = selected.id;

            validateExpensesData(expensesData, expenseId)

            TF.Rpc.Diary.DiaryExpenses.updateExpenses(expensesData, expenseId)
                .done(function (data) {
                    winAddEditExpenses.window('close');
                    expensesGrid.datagrid('loadRpc');
                })
                .fail(function (errorObj) {
                    if (errorObj.is(TF.Rpc.ExceptionsList.EXPENSE_ALREADY_EXISTS)) {
                        jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.EXPENSE_ALREADY_EXISTS.message, 'error')
                    }
                });
        } else {
            validateExpensesData(expensesData)
            TF.Rpc.Diary.DiaryExpenses.createExpenses(expensesData, expenseId)
                .done(function (data) {
                    winAddEditExpenses.window('close');
                    expensesGrid.datagrid('loadRpc');
                })
                .fail(function (errorObj) {
                    if (errorObj.is(TF.Rpc.ExceptionsList.EXPENSE_ALREADY_EXISTS)) {
                        jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.EXPENSE_ALREADY_EXISTS.message, 'error')
                    }
                });
        }
    }

    //filters
    jQuery('#search-ekate > input').combobox({
        url: 'index.php?common-rpc=ekate-combobox',
        valueField: 'ekate',
        textField: 'text',
        filter: function (q, row) {
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if (text.indexOf(find) != -1 || value.indexOf(find) != -1) {
                return true;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    jQuery('#search-culture > input').combobox({
        url: 'index.php?common-rpc=culture-combobox',
        valueField: 'id',
        textField: 'name',
        editable: false,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    jQuery('#search-event-phase > input').combobox({
        url: 'index.php?diary-rpc=diary-event-phase-combobox',
        editable: false,
        textField: 'name',
        valueField: 'id',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    jQuery('#search-event-subtype > input').combobox({
        url: 'index.php?diary-rpc=diary-configs-combobox',
        rpcParams: [{
            request_type: 2,
            event_type: 0
        }],
        editable: false,
        textField: 'name',
        valueField: 'id',
        disabled: true,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    jQuery('#search-event-type > input').combobox({
        url: 'index.php?diary-rpc=diary-configs-combobox',
        rpcParams: [{
            request_type: 1
        }],
        editable: false,
        textField: 'name',
        valueField: 'id',
        onSelect: function (record) {
            //init subtype combobox
            jQuery('#search-event-subtype > input').combobox({
                url: 'index.php?diary-rpc=diary-configs-combobox',
                rpcParams: [{
                    request_type: 2,
                    event_type: record.id
                }],
                editable: false,
                textField: 'name',
                valueField: 'id',
                disabled: false,
                loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
            });
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    jQuery('#search-event-performer > input').combobox({
        url: 'index.php?diary-rpc=diary-configs-combobox',
        rpcParams: [{
            request_type: 9
        }],
        editable: false,
        textField: 'name',
        valueField: 'id',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    jQuery('#search-event-date-from > input').datebox();
    jQuery('#search-event-date-to > input').datebox();
    /** @deprecated */
    jQuery('#btn-get-wialon-message').bind('click', function () {
        var obj = {};

        trackMachineID = jQuery('#wialon-message-machine').find('> input').combobox('getValue');
        trackDate = jQuery('#wialon-message-date').find('> input').datebox('getValue');

        if (trackMachineID == "") {
            jQuery.messager.alert('Грешка', 'Не сте избрали машина!');
            return;
        }

        if (trackDate == "") {
            jQuery.messager.alert('Грешка', 'Не сте избрали дата!');
            return;
        }

        trackTimeFrom = jQuery('#wialon-message-time-from input').timespinner('getValue');
        trackTimeTo = jQuery('#wialon-message-time-to input').timespinner('getValue');
        var trackName = jQuery('#wialon-message-machine > input').combobox('getText') + ' / ' + jQuery('#wialon-message-date > input').datebox('getText');

        //change button name
        jQuery('#btn-get-tracks > span > span').html(trackName);

        obj.machine_id = trackMachineID;
        obj.date = trackDate;
        obj.time_from = trackTimeFrom;
        obj.time_to = trackTimeTo;

        TF.Rpc.Diary.WialonActions.getMessages(obj)
            .done(function (data) {
                loadMapMessages(data);
                jQuery('#win-set-message-params').window('close');
            })
            .fail(function (errorObj) {
                if (errorObj !== undefined && errorObj.is(TF.Rpc.ExceptionsList.INVALID_OR_EXPIRED_TOKEN)) {
                    jQuery('#win-get-wialon-token').window('open');
                    return;
                }
                jQuery.messager.alert('Грешка', errorObj.getMessage());
            });
    });
    jQuery('#btn-get-tracks').bind('click', function () {
        var opts = plotsTrackWindow.window('options');
        if (opts.closed == true) plotsTrackWindow.window('open');
        plotsTrackWindow.window('resize', {width: 840, height: 280});
        plotsTrackWindow.window('center');
        return false;
    });
    //tools
    jQuery('#tool-zoom-to-layer').bind('click', function () {
        var currentLayer = ZP_TREE_CTRL.tree('getSelected');
        if (currentLayer) {
            map.zoomToExtent(new OpenLayers.Bounds.fromString(currentLayer['attributes'].extent).transform(
                new OpenLayers.Projection("EPSG:32635"),
                map.getProjectionObject())
            );
            return;
        }
        currentLayer = COMBO_TREE_CTRL.combotree('tree').tree('getSelected');
        if (currentLayer) {
            map.zoomToExtent(new OpenLayers.Bounds.fromString(currentLayer['attributes'].extent).transform(
                new OpenLayers.Projection("EPSG:32635"),
                map.getProjectionObject())
            );
        }
    });
    jQuery('#tool-measure-line').bind('click', function () {
        var options = jQuery('#tool-measure-line').linkbutton('options');
        if (!options.disabled) {
            unselectAll();
            jQuery('#tool-measure-line').linkbutton('select');
            chooseControl('line');
        }
        return false;
    });
    jQuery('#tool-measure-polygon').bind('click', function () {
        var options = jQuery('#tool-measure-polygon').linkbutton('options');
        if (!options.disabled) {
            unselectAll();
            jQuery('#tool-measure-polygon').linkbutton('select');
            chooseControl('polygon');
        }
        return false;
    });
    jQuery('#tool-zoomout').bind('click', function () {
        var options = jQuery('#tool-zoomout').linkbutton('options');
        if (!options.disabled) {
            unselectAll();
            jQuery('#tool-zoomout').linkbutton('select');
            chooseControl('zoomout');
        }
        return false;
    });
    jQuery('#tool-zoomin').bind('click', function () {
        var options = jQuery('#tool-zoomin').linkbutton('options');
        if (!options.disabled) {
            unselectAll();
            jQuery('#tool-zoomin').linkbutton('select');
            chooseControl('zoomin');
        }
        return false;
    });
    jQuery('#tool-panzoom').bind('click', function () {
        var options = jQuery('#tool-panzoom').linkbutton('options');
        if (!options.disabled) {
            unselectAll();
            jQuery('#tool-panzoom').linkbutton('select');
            chooseControl('none');
        }
        return false;
    });
    jQuery('#tool-select-zplot').bind('click', function () {
        var options = jQuery(this).linkbutton('options');
        if (options.disabled) {
            return false;
        }
        if (ZPselected.table) {
            unselectAll();
            jQuery(this).linkbutton('select');
            chooseControl('none');
            map.events.register('click', map, getZPlotData);
        } else {
            jQuery.messager.alert('Грешка', 'Избор на земеделски парцели от карта може да се изпълни само когато активният слой е от тип "Земеделски парцели"!');
        }
        return false;
    });
    jQuery('#tool-clear-selection').bind('click', function () {
        var options = jQuery('#tool-clear-selection').linkbutton('options');
        if (options.disabled) {
            return false;
        }
        vectors.removeAllFeatures();
        unselectAll();
        chooseControl('none');
    });

    jQuery('#btn-choose-diary-type').bind('click', function () {
        jQuery('#win-choose-diary-type').window('open');
    });
    jQuery('#btn-save-event').bind('click', function () {
        var evntValidator = isEventValid();
        if (!evntValidator.valid) {
            if (!evntValidator.details.timeFrame_greater_zero) {
                return jQuery.messager.alert('Грешка', 'Началният и крайният час не може да съвпадат');
            }
            return jQuery.messager.alert('Грешка', 'Моля, попълнете всички задължителни полета.', 'warning');
        }
        //set final callback params
        var product_data = product_datagrid.datagrid('getData');
        if (product_data.rows && product_data.rows.length > 0) {
            var row = product_datagrid.datagrid('getSelected');
            if (row && row.editing === true && selectedIndex != null) {
                product_datagrid.datagrid('endEdit', selectedIndex);
            }
            //validate products before save or return;
            var products_valid = validateProductsAndProduces(product_datagrid, productRequiredFields);
            if (products_valid !== true) {
                jQuery('#events_win_tabs').tabs('select', 2);
                return jQuery.messager.alert('Грешка', 'Моля, попълнете всички задължителни полета в продуктовата таблица.', 'warning');
            };
        }

        var produces_data = produceDatagrid.datagrid('getData');
        if (produces_data.rows && produces_data.rows.length > 0) {
            var produceRow = produceDatagrid.datagrid('getSelected');
            if (produceRow && produceRow.editing === true && selectedIndex != null) {
                produceDatagrid.datagrid('endEdit', selectedIndex);
            }
            var produces = validateProductsAndProduces(produceDatagrid, ['culture', 'area', 'produce', 'produce_per_dka']);
            if (!produces) {
                jQuery('#events_win_tabs').tabs('select', 3);
                return jQuery.messager.alert('Грешка', 'Моля, попълнете всички задължителни полета в таблицата за добиви.', 'warning');
            };
        }

        var eventData = jQuery('#zplot-events-tables').datagrid('getSelected');
        var obj = getEditEventFields();
        obj.event_id = eventData ? eventData.id : null;
        obj.farming_id = ZPselected.farming;
        obj.year_id = ZPselected.year;
        obj.plot_id = ZPselected.plot_id;

        TF.Rpc.Diary.ZPlotEventsGrid.saveEvent(obj)
            .done(function () {
                jQuery('#win-add-edit-event').window('close');
                jQuery('#zplot-events-tables').datagrid('reload');
            })
            .fail(function (errorObj) {
                jQuery.messager.alert('Грешка', errorObj.getMessage());
            });

    });
    initKvsFilters();
});

function enableDisableTabs(selector, isValid) {
    var enabled = (isValid) ? 'enableTab' : 'disableTab';
    var enabledProduce = 'disableTab';
    var enabledProduct = enabled;

    var eventSubTypesCombobox = jQuery('#event-subtype > input')
    var subTypes = eventSubTypesCombobox.combobox('getData');
    var selected = eventSubTypesCombobox.combobox('getValue');
    var recordData = subTypes.filter(subType => subType.sub_type_id == selected);
    if(recordData && recordData[0] && recordData[0].sub_type_options){
        var options = JSON.parse(recordData[0].sub_type_options);
        if(options && options.hasProduce){
            enabledProduce = 'enableTab';
            enabledProduct = 'disableTab';
        }
    }

    var produces = jQuery('#produce-grid').datagrid('getData');
    if(produces.total > 0){
        enabledProduce = 'enableTab';
        enabledProduct = 'disableTab';
    }

    jQuery(selector).tabs(enabled, 1);
    jQuery(selector).tabs(enabledProduct, 2);
    jQuery(selector).tabs(enabledProduce, 3);
    jQuery(selector).tabs(enabled, 4);
    jQuery(selector).tabs(enabled, 5);
}

function isDate(dateString) {
    var minDate = new Date('1970-01-01 00:00:01');
    var maxDate = new Date('2038-01-19 03:14:07');
    var date = new Date(dateString);
    return (date !== "Invalid Date") && !isNaN(date) && (date > minDate && date < maxDate);
}

function initPlotTracksGrid() {
    var toolbar = null;
    var searchBtn = null;
    if(jQuery('#track_grid_toolbar').length == 0){
        toolbar = jQuery('<div id="track_grid_toolbar" style="padding:2px 26px"></div>').appendTo('body');
        var dateFrom = jQuery('<input>', {
            id: "track_dateFrom", "class": "easyui-datebox datebox-f combo-f textbox-f", "style": "width: 100px; margin-right:5px"
        }).appendTo(toolbar);
        var timeFrom = jQuery('<input>', {
            id: "track_timeFrom", "class": "easyui-timespinner", "value": "00:00", "style": "width: 80px;"
        }).appendTo(toolbar);
        var dateTo = jQuery('<input>', {
            id: "track_dateTo", "class": "easyui-datebox datebox-f combo-f textbox-f", "style": "width: 100px;"
        }).appendTo(toolbar);
        var timeTo = jQuery('<input>', {
            id: "track_timeTo", "class": "easyui-timespinner","value": "23:59", "style": "width: 80px;"
        }).appendTo(toolbar);
        searchBtn = jQuery('<a>', {
            id: "track_search", "class": "easyui-linkbutton", "value": "Search", "style": "width: 100px;"
        }).appendTo(toolbar);
        dateFrom.datebox({value: new Date().toString('dd-MMM-yyyy'), required: true});
        dateTo.datebox({value: new Date().toString('dd-MMM-yyyy'), required: true});
        timeFrom.timespinner({value: '00:00', required: true});
        timeTo.timespinner({value: '23:59', required: true});
        searchBtn.linkbutton({iconCls: 'icon-search', text: 'Търси'});
    }else{
        toolbar = jQuery('#track_grid_toolbar');
        searchBtn = jQuery('#track_search');
    }

    ZP_TRACK_GRID.treegrid({
        url: 'index.php?diary-rpc=wialon-actions',
        rpcMethod: 'getAll',
        rpcParams: [{
            farming: ZPselected.farming,
            year: ZPselected.year,
            plot_id: ZPselected.plot_id
        }],
        idField:'_key',
        treeField: 'Grouping',
        rownumbers:false,
        maximized:true,
        nowrap: true,
        sortName: '_key',
        striped: true,
        toolbar: toolbar,
        noheader: false,
        singleSelect: true,
        fitColumns: true,
        showFooter: false,
        showHeader: true,
        border: true,
        pagination: false,
        autoRowHeight: true,
        autoSizeColumn: false,
        fit: true,
        columns: [
            [
                {field: '_key', title: '<b>id</b>', hidden: true},
                {field: 'unit_id', title: '<b>unit_id</b>', hidden: true},
                {field: 'Grouping', align: 'left', title: '<b>Машина</b>',
                    formatter: function (value, row) {
                        return '<b>' + value + '</b>'
                    }
                },
                {field: 'image', align: 'center', title: '<b>Икона</b>', width: 40,fixed:true,
                    formatter: function (value, row) {
                        if (!row.image) return;
                        return '<img style="vertical-align:text-bottom;width:24px;height:20px;" src="' + row.image + '">';
                    }
                },
                {field: 'Time Start', align: 'center', title: '<b>Време<br>в</b>',
                    formatter: function (value, row) {
                        if (value == undefined || value.v == undefined) return;
                        var d = new Date(value['v'] * 1000);
                        return ("0" + d.getDate()).slice(-2) + "-" + ("0" + (d.getMonth() + 1)).slice(-2) + "-" + d.getFullYear() + " " + ("0" + d.getHours()).slice(-2) + ":" + ("0" + d.getMinutes()).slice(-2);
                    }
                },
                {field: 'Time End', align: 'center', title: '<b>Време<br>извън</b>',
                    formatter: function (value, row) {
                        if (value == undefined || value.v == undefined) return;
                        var d = new Date(value['v'] * 1000);
                        return ("0" + d.getDate()).slice(-2) + "-" + ("0" + (d.getMonth() + 1)).slice(-2) + "-" + d.getFullYear() + " " + ("0" + d.getHours()).slice(-2) + ":" + ("0" + d.getMinutes()).slice(-2);
                    }
                },
                {field: 'Total time', align: 'center', title: '<b>Oбщо<br>време</b>'},
                {field: 'Mileage', align: 'center', title: '<b>Изминати<br>километри</b>', width: 80,fixed:true},
                {field: 'fuel_spent', align: 'center', title: '<b>Разход<br>на гориво</b>', width: 64},
                {field: 'show_track', align: 'center', title: '<b>Покажи<br>следата</b>', width: 60,fixed:true,
                    formatter: function (value, row) {
                        if(row['_grouping'] == 'unit') return;
                        return '<a class="l-btn button" href="javascript:show_selected_wialonTrack()">' +
                            '<span class="l-btn-left l-btn-icon-left">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="l-btn-icon icon-curve">&nbsp;</span></span></a>' ;

                    }
                },
                {field: 'enter_event', align: 'center', title: '<b>Въведи<br>мероприятие</b>', width: 80, fixed:true,
                    formatter: function (value, row) {
                        if(row['_table'] == 'Trips') return;
                        return '<a class="l-btn button" href="javascript:enter_event()">' +
                            '<span class="l-btn-left l-btn-icon-left">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="l-btn-icon icon-event">&nbsp;</span></span></a>' ;
                    }
                }

            ]
        ],
        onBeforeLoad: function () {
            if (!ZPselected || (ZPselected.farming == 0 || ZPselected.year == 0 || ZPselected.plot_id == 0)) {
                return false;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery(searchBtn).bind('click', function () {
        var params = get_ZP_TRACK_window_data();
        var d1 = new Date(params.date_from);
        var d2 = new Date(params.date_to);
        var diff = Math.abs(d1 - d2) / 86400000;
        ZP_TRACK_GRID.treegrid('options').rpcParams = [params];
        if (diff > 7) {
            jQuery.messager.confirm('Потвърждение', 'Тази функция може да отнеме няколко минути при периоди повече от една седмица. Жалаете ли да продължите?', function (r) {
                if (r != false) {
                    ZP_TRACK_GRID.treegrid('reload');
                }
            });
        } else {
            ZP_TRACK_GRID.treegrid('reload');
        }
    });
}

function initEditorsValuesForGrid() {
    jQuery.ajax('index.php?diary-rpc=diary-configs-combobox', {
        type: 'POST',
        dataType: 'json',
        contentType: "application/json; charset=UTF-8",
        data: JSON.stringify({
            "method": "getAll",
            "params": [null],
            "id": 1,
            "jsonrpc": "2.0"
        })
    }).done(function (data) {
        product_grid_data = data.result;
        initAddEditEventsWindow();
    })
}

function getEventTime(){
    var event = {};
    var ephase = jQuery('#event-phase').find('> input').combobox('getValue');
    if (ephase == undefined || ephase == '' ) {
        return null;
    }
    event.from = '';
    event.to = '';
    if (ephase == EVENT_PLANNED) {
        var plan_date_from = jQuery("#event-plan-date-from > input").datebox('getValue');
        var plan_time_from = jQuery("#event-plan-time-from > input").timespinner('getValue');
        var plan_date_to = jQuery("#event-plan-date-to > input").datebox('getValue');
        var plan_time_to = jQuery("#event-plan-time-to > input").timespinner('getValue');
        event.from = plan_date_from + " " + plan_time_from + ':00';
        event.to = plan_date_to + " " + plan_time_to + ':59';
    }
    if (ephase == EVENT_COMPLETE) {
        var complete_date_from = jQuery("#event-complete-date-from > input").datebox('getValue');
        var complete_time_from = jQuery("#event-complete-time-from > input").timespinner('getValue');
        var complete_date_to = jQuery("#event-complete-date-to > input").datebox('getValue');
        var complete_time_to = jQuery("#event-complete-time-to > input").timespinner('getValue');
        event.from = complete_date_from + " " + complete_time_from + ':00';
        event.to = complete_date_to + " " + complete_time_to + ':59';
    }
    if (!isDate(event.from) || !isDate(event.to)) return null;
    return event;
}

function isValidExpense(expense, event) {
    if (!expense || !event) return false;
    return expense!== null && event !== null && expense.price !== undefined && ZPselected.area !== 0 &&
        isDate(expense.valid_from) && isDate(expense.valid_to) &&
        (expense.valid_from <= event.to && expense.valid_to >= event.from );
}
//init subtype combobox
var last_sub_type_expense_query;
/**
 *
 * @param params {
 *     use_type_id: bool , if true return where query with activity id = event-type combobox value
 *     subtype_id: bool , if true return where query where sub-activity id = subtype_id
 *  }
 */

function loadSubtypeExpense(params){
    var subTypeCombo = jQuery('#event-subtype > input');
    var init = (subTypeCombo.data().hasOwnProperty('combobox'));

    var rpcParams = {};
    if (params && params.use_type_id) {
        rpcParams.type_id = jQuery('#event-type > input').combobox('getValue');
    }
    if (params && params.subtype_id) {
        rpcParams.subtype_id = params.subtype_id;
    }
    if (init == false) {
        subTypeCombo.combobox({
            url: 'index.php?diary-rpc=diary-zplot-events-grid',
            rpcMethod: 'loadExpensesForEvent',
            rpcParams: [rpcParams],
            editable: false,
            required: true,
            textField: 'sub_type_name',
            valueField: 'sub_type_id',
            groupField: 'group',
            onLoadSuccess: function (data) {
                if(data.length === 0) {
                    subTypeCombo.combobox('disable');
                    if(rpcParams.type_id) {
                        jQuery.messager.alert('Грешка', "За избрания тип дейност няма въведени видове дейности. Можете да въведете вид дейност от Настройки.");
                    }
                } else if(data.length === 1) {
                    jQuery(this).combobox('setValue', data[0].sub_type_id);
                    data[0].selected = true;
                    subTypeCombo.combobox('disable');
                } else {
                    jQuery(this).combobox('enable');
                    var event = getEventTime();

                    for (var i = 0; i < data.length; i++) {
                        var expense = data[i];
                        var eventTreatableArea = jQuery("#event-treatable_area").val();
                        var treatedArea = Math.min(
                            eventTreatableArea,
                            ZPselected.area
                        );
                        var totalExpensesCost =
                            expense.price * treatedArea;
                        if (expense.selected == true) {
                            if (isValidExpense(expense, event)) {
                                jQuery(
                                    "#event-expense_cost"
                                ).val(totalExpensesCost);
                            }else{
                                jQuery("#event-expense_cost").val(0);
                            }
                            if (data[i].performer_id !== undefined) {
                                jQuery("#event-performer > input").combobox('setValue', expense.performer_id);
                                jQuery("#event-performer > input").combobox('setText', expense.performer_name);
                            }
                        }
                    }
                }
                var event_validation_obj = isEventValid();
                enableDisableTabs('#events_win_tabs', event_validation_obj.valid);
                updateTotalField();

            },
            onLoadError: function (data) {
                jQuery(this).combobox('enable');
                updateTotalField();
            },
            onChange: function (record) {
                var event_validation_obj = isEventValid();
                enableDisableTabs('#events_win_tabs', event_validation_obj.valid);
                var event = getEventTime();
                var eventTreatableArea = jQuery(
                    "#event-treatable_area"
                ).val();
                var treatedArea = Math.min(
                    eventTreatableArea,
                    ZPselected.area
                );
                if(!event || !record){
                    jQuery("#event-expense_cost").val(0);
                    return updateTotalField();
                }
                if (isValidExpense(record, event)) {
                    jQuery("#event-expense_cost").val(
                        treatedArea
                    );
                }else{
                    jQuery("#event-expense_cost").val(0);
                }
                if (record.performer_id !== undefined) {
                    jQuery("#event-performer > input").combobox('setValue', record.performer_id);
                    jQuery("#event-performer > input").combobox('setText', record.performer_name);
                }
                updateTotalField();
            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    } else {
        subTypeCombo.combobox('loadRpc', [rpcParams]);
    }
}

function initAddEditEventsWindow() {
    product_datagrid = jQuery('#dg');
    produceDatagrid = jQuery('#produce-grid');

    var substanceUnitTypeCombobox;
    var productsCombobox;
    //SET THE OPTION FOR THE COMBOBOX EDITORS IN THE DATAGRID
    function getDataForEditors(config_type) {
        return jQuery.grep(product_grid_data, function (n, i) {
            return n.config_type == config_type;
        });
    }

    //SET THE OPTION FOR THE COMBOBOX EDITORS IN THE DATAGRID
    function getSubstancePrz(config_type, substance_id) {
        return jQuery.grep(product_grid_data, function (n, i) {
            return n.config_type == config_type && n.id == substance_id;
        });
    }

    function update_substance_sub_total(dose, treated_area, unit_price) {
        var sub_total = 0;
        var consumed_qty = 0;
        var price_editor = product_datagrid.datagrid('getEditor', {index: selectedIndex, field: 'price_per_area'});
        var consumed_editor = product_datagrid.datagrid('getEditor', {index: selectedIndex, field: 'substance_consumed'});
        if (dose > 0 && treated_area > 0 && unit_price > 0) {
            sub_total = (unit_price * dose * treated_area);
        }
        if(dose > 0 && treated_area > 0){
            consumed_qty = (dose * treated_area);
        }
        jQuery(price_editor.target).numberbox('setValue', sub_total);
        jQuery(consumed_editor.target).numberbox('setValue', consumed_qty);
        update_products_total_cost(selectedIndex, sub_total);
    }

    function update_produce_per_dka(produce, area) {
        var produce_per_dka = null;
        var produce_per_dka_editor = produceDatagrid.datagrid('getEditor', {index: selectedIndex, field: 'produce_per_dka'});
        if(produce > 0 && area > 0){
            produce_per_dka = (produce / area);
        }
        jQuery(produce_per_dka_editor.target).numberbox('setValue', produce_per_dka);
    }

    var columns = [
        {
            field: 'substance_id',
            title: '<b>Продукт</b>',
            width: 160,
            align: 'left',
            editor: {
                type: 'combobox',
                options: {
                    url: 'index.php?diary-rpc=diary-configs-combobox',
                    rpcParams: [{
                        request_type: 7
                    }],
                    valueField: 'id',
                    textField: 'name',
                    required: true,
                    align: 'left',
                    onShowPanel: function () {
                        var target = jQuery(this).parent();
                        product_datagrid.datagrid('selectRow', getRowIndex(target));
                    },
                    onSelect: function (product) {
                        if(product && product.options){
                            productsSettings(product, false);
                        }
                        if(hasWarehouseRights && product && !isNaN(parseInt(product.id))) {
                            var row = product_datagrid.datagrid('getSelected');
                            if(isProductSelected(product.id)){
                                jQuery.messager.alert('Грешка', "Вече сте избрали този продукт.");
                                jQuery(this).combobox("clear");
                                return true;
                            };

                            if(parseInt(product.id) !== parseInt(row.values.substance_id)){
                                var target = jQuery(this).parent();

                                var substance_unit_price_editor = product_datagrid.datagrid('getEditor', {index: selectedIndex, field: 'substance_unit_price'});
                                var warehouse_quantity_editor = product_datagrid.datagrid('getEditor', {index: selectedIndex, field: 'warehouse_quantity'});

                                jQuery(substance_unit_price_editor.target).numberbox('setValue', "");
                                jQuery(warehouse_quantity_editor.target).textbox('setValue', "");

                                manageUtilsMeasures(product);
                                checkProductAvailability(product, 'substance_choice', getRowIndex(target));
                            }

                        }
                    },
                    onLoadSuccess: function () {
                        productsCombobox = jQuery(this);
                        var id = productsCombobox.combobox('getValue');

                        var product = jQuery.grep(productsCombobox.combobox('getData'), function(row){
                            if(row.id == id){
                                return true;
                            } else {
                                return false;
                            }
                        });

                        if(product[0] !== undefined){
                            productsSettings(product[0], false);
                        }
                    },
                    filter: function(q, row){
                        var opts = jQuery(this).combobox('options');
                        var text = row[opts.textField].toLowerCase();
                        var find = q.toLowerCase();
                        if(text.indexOf(find) != -1)
                        {
                            return true;
                        }
                    },
                    loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                    loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
                }
            }
        },
        {
            field: 'substance_dose',
            title: '<b>Разходна<br>норма<b>',
            align: 'center',
            editor: {
                type: 'numberbox',
                options: {
                    disabled: true,
                    precision: 4,
                    min: 0,
                    align: 'center',
                    onChange: function (dose) {
                        var row = product_datagrid.datagrid('getSelected');
                        if (selectedIndex != null && selectedIndex >= 0 && row) {
                            var treated_area = product_datagrid.datagrid('getEditor', {
                                index: selectedIndex,
                                field: 'treated_area'
                            }).target.numberbox('getValue');
                            var unit_price = product_datagrid.datagrid('getEditor', {
                                index: selectedIndex,
                                field: 'substance_unit_price'
                            }).target.numberbox('getValue');

                            update_substance_sub_total(dose, treated_area, unit_price);
                        }
                    }
                }
            }
        },
        {
            field: 'substance_unit_type',
            align: 'left',
            title: '<b>Мерна<br>единица<b>',
            width: 120,
            editor: {
                type: 'combobox',
                options: {
                    disabled: true,
                    editable: false,
                    textField: 'name',
                    valueField: 'id',
                    required: true,
                    data: getDataForEditors(10),
                    align: 'left',
                    onShowPanel: function () {
                        var target = jQuery(this).parent();
                        product_datagrid.datagrid('selectRow', getRowIndex(target));
                    },
                    onLoadSuccess: function () {
                        substanceUnitTypeCombobox = jQuery(this);
                    }
                }
            }
        }, {
            field: 'substance_unit_price',
            title: '<b>Единична<br>стойност (лв.)<b>',
            align: 'center',
            editor: {
                type: 'numberbox',
                options: {
                    disabled: true,
                    precision: 2,
                    min: 0,
                    align: 'center',
                    onChange: function (unit_price, old_val) {
                        var row = product_datagrid.datagrid('getSelected');
                        if (selectedIndex != null && selectedIndex >= 0 && row) {
                            var treated_area = product_datagrid.datagrid('getEditor', {
                                index: selectedIndex,
                                field: 'treated_area'
                            }).target.numberbox('getValue');
                            var dose = product_datagrid.datagrid('getEditor', {
                                index: selectedIndex,
                                field: 'substance_dose'
                            }).target.numberbox('getValue');
                            update_substance_sub_total(dose, treated_area, unit_price);
                        }
                    }
                }
            }
        }, {
            field: 'treated_area',
            title: '<b>Площ<b>',
            width: 80,
            align: 'center',
            editor: {
                type: 'numberbox',
                options: {
                    disabled: true,
                    precision: 3,
                    min: 0,
                    align: 'center',
                    onChange: function (treated_area, old_val) {
                        var row = product_datagrid.datagrid('getSelected');
                        if (selectedIndex != null && selectedIndex >= 0 && row) {
                            var unit_price = product_datagrid.datagrid('getEditor', {
                                index: selectedIndex,
                                field: 'substance_unit_price'
                            }).target.numberbox('getValue');
                            var dose = product_datagrid.datagrid('getEditor', {
                                index: selectedIndex,
                                field: 'substance_dose'
                            }).target.numberbox('getValue');
                            update_substance_sub_total(dose, treated_area, unit_price);
                        }
                    }
                }
            }
        }, {
            /* this is the COST field, calculated for each row*/
            field: 'price_per_area',
            title: '<b>Отчетен<br>разход (лв.)<b>',
            align: 'center',
            editor: {
                type: 'numberbox',
                options: {
                    precision: 2,
                    min: 0,
                    readonly: true,
                    disabled: true,
                    value: 0,
                    align: 'center'
                }
            }
        }, {
            field: 'pest_name',
            title: '<b>Вредител<b>',
            width: 100,
            align: 'center',
            editor: {
                type: 'textbox',
                align: 'center',
                options: {
                    disabled: true
                }
            },
            formatter: function (value, row, index) {
                if (value) return value.substring(0, 100);
            }
        }, {
            field: 'substance_technic_id',
            title: '<b>Приложение<b>',
            width: 120,
            align: 'left',
            editor: {
                type: 'combobox',
                options: {
                    align: 'left',
                    disabled: true,
                    editable: false,
                    valueField: 'id',
                    textField: 'name',
                    required: true,
                    data: getDataForEditors(8),
                    onShowPanel: function () {
                        var target = jQuery(this).parent();
                        product_datagrid.datagrid('selectRow', getRowIndex(target));
                    }
                }
            }
        }, {
            field: 'quarantine_period',
            title: '<b>Карантинен<br>срок<b>',
            width: 100,
            align: 'center',
            editor: {
                type: 'numberbox',
                options: {
                    disabled: true,
                    precision: 0,
                    min: 0,
                    align: 'center'
                }
            }
        },{
            field: 'substance_consumed',
            title: '<b>Употребено<br>количество<b>',
            width: 100,
            align: 'center',
            editor: {
                type: 'numberbox',
                options: {
                    precision: 2,
                    min: 0,
                    align: 'center',
                    disabled: true,
                    onChange: function (dose) {
                        var row = product_datagrid.datagrid('getSelected');
                        if (selectedIndex != null && selectedIndex >= 0 && row) {
                            if(hasWarehouseRights){
                               var productsComboboxData = product_datagrid.datagrid('getEditor', {index: selectedIndex, field: 'substance_id'}).target.combobox('getData');
                               var selectedProductId = product_datagrid.datagrid('getEditor', {index: selectedIndex, field: 'substance_id'}).target.combobox('getValue');

                                var selectedProduct = jQuery.grep(productsComboboxData, function (item) {
                                    if(item.id === parseInt(selectedProductId)) return item;
                                });

                                if(selectedProduct.length === 0) return;
                                checkProductAvailability(selectedProduct[0], 'dose_choice', selectedIndex);
                            }
                        }
                    }
                }
            }
        },
        {
            field: 'sort',
            title: '<b>Сорт<b>',
            width: 100,
            align: 'center',
            editor: {
                type: 'textbox',
                align: 'center',
                options: {
                    disabled: true
                }
            }
        }
    ];

    if(hasWarehouseRights) {
        columns.push({
            field: 'warehouse_quantity',
            align: 'center',
            title: '<b>Налично <br>количество<b>',
            width: 80,
            editor: {
                type: 'textbox',
                options: {
                    disabled: true,
                    align: 'center'
                }
            },
            formatter: function (value) {
                return Math.round(value * 100) / 100
            }
        });
    }

    //PRODUCT DATAGRID ON TAB 3
    product_datagrid.datagrid({
        title: 'Информация за препарати',
        singleSelect: true,
        fitColumns: false,
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        fit: true,
        border: true,
        rownumbers: true,
        showFooter: true,
        toolbar: [{
            id: 'event-product-btn_add', text: 'Добавяне', iconCls: 'icon-add',
            handler: function () {
                if(hasWarehouseRights){
                    warehouseQuantity = jQuery('#warehouseItemQuantity').val();
                }
                var new_row = {
                    editing: true,
                    substance_id: null,
                    substance_dose: null,
                    substance_unit_type: null,
                    substance_unit_price: null,
                    treated_area: ZPselected.area,
                    price_per_area: null,
                    pest_name: '',
                    substance_technic_id: null,
                    quarantine_period: null,
                    substance_consumed: null,
                    sort: null
                };

                if(hasWarehouseRights) {
                    new_row['warehouse_quantity'] = null;
                }
                var hasEditingRow = false;
                var products = product_datagrid.datagrid('getData');
                for(var product of products.rows) {
                    if(product.editing) {
                        hasEditingRow = true;
                        break;
                    }
                }

                if (hasEditingRow) {
                    jQuery.messager.alert('Грешка', 'Моля запазете текущия ред', 'warning');
                    return false;
                };

                product_datagrid.datagrid('insertRow', {index: 0, row: new_row});
                product_datagrid.datagrid('selectRow', 0);
                product_datagrid.datagrid('beginEdit', 0);
            }
        }, {
            id: 'event-product-btn_edit', text: 'Редактиране', iconCls: 'icon-edit',
            handler: function () {
                if (selectedIndex != null) {
                    if(hasWarehouseRights){
                        editingRow = true;
                    }
                    product_datagrid.datagrid('beginEdit', selectedIndex);
                    product_datagrid.datagrid('selectRow', selectedIndex);
                }
            }
        }, {
            id: 'event-product-btn-apply', text: 'Запази', iconCls: 'icon-ok',
            handler: function () {
                var row = product_datagrid.datagrid('getSelected');
                if (!row) return false;
                if (row.editing === true && selectedIndex != null) {

                    product_datagrid.datagrid('endEdit', selectedIndex);
                    update_products_total_cost();
                }
            }
        }, {
            id: 'event-product-btn_cancel', text: 'Откажи', iconCls: 'icon-redirect',
            handler: function () {
                var row = product_datagrid.datagrid('getSelected');
                if (!row) return false;
                if (row.editing == true && selectedIndex != null) {
                    product_datagrid.datagrid('cancelEdit', selectedIndex);
                    product_datagrid.datagrid('selectRow', selectedIndex);
                }
            }
        }, {
            id: 'event-product-btn_delete', text: 'Премахване', iconCls: 'icon-delete',
            handler: function () {
                if (selectedIndex != null && selectedIndex >= 0) {
                    jQuery.messager.confirm('Confirm', 'Сигурни ли сте?', function (r) {
                        if (r) {
                            product_datagrid.datagrid('deleteRow', selectedIndex);
                            update_products_total_cost();
                        }
                    });
                }
            }
        }],
        columns: [columns],
        onBeginEdit: function (index, row) {
            if (!row.values) row.values = row;
            // we update here data from row.values to each editor for each column
            var columns = product_datagrid.datagrid('getColumnFields');
            for (var i = 0; i < columns.length; i++) {
                var editor = product_datagrid.datagrid('getEditor', {index: index, field: columns[i]});
                var column = columns[i];
                var value = row.values[column];
                if (!editor) continue;
                if (editor.type === 'checkbox') {
                    jQuery(editor.target).prop("checked", value);
                } else if (editor.type === 'combobox') {
                    jQuery(editor.target).combobox('select', value);
                    if(column === 'substance_id' && row.substance_name) {
                        jQuery(editor.target).combobox('setText', row.substance_name);
                    }
                } else {
                    jQuery(editor.target)[editor.type]('setValue', value);
                }
            }
        },
        onEndEdit: function (index, row) {
            var columns = product_datagrid.datagrid('getColumnFields');
            row.values = {}; // we save and store here data values from the editor
            for (var i = 0; i < columns.length; i++) {
                var field = columns[i];
                var editor = product_datagrid.datagrid('getEditor', {index: index, field: field});
                var value = null;
                var text = null;
                if (!editor) continue;
                if (editor.type === 'checkbox') {
                    value = jQuery(editor.target).is(':checked');
                    text = (value) ? 'Да' : 'Не';
                } else {
                    value = jQuery(editor.target)[editor.type]('getValue');
                    text = jQuery(editor.target)[editor.type]('getText');
                }
                row[field] = text;
                row.values[field] = value;
            }
        },
        onLoadSuccess: function (data) {
        },
        onBeforeEdit: function (index, row) {
            if (!row) return false;
            row.editing = true;
            selectedIndex = index;
            jQuery(this).datagrid('refreshRow', index);
        },
        onAfterEdit: function (index, row) {
            row.editing = false;
            jQuery(this).datagrid('refreshRow', index);
        },
        onCancelEdit: function (index, row) {
            row.editing = false;
            jQuery(this).datagrid('refreshRow', index);
            if(hasWarehouseRights) {
                editingRow = false;
                jQuery("#warehouseItemQuantity").val(warehouseQuantity);
                warehouseQuantity = 0;
            }
        },
        onSelect: function (index, row) {
            selectedIndex = index;
            if (!row) {
                jQuery('#event-product-btn_edit').linkbutton('disable');
                return;
            }
            if (row && row.editing === true) {
                jQuery('#event-product-btn-apply').linkbutton('enable');
                jQuery('#event-product-btn_cancel').linkbutton('enable');
                jQuery('#event-product-btn_edit').linkbutton('disable');

            } else {
                jQuery('#event-product-btn-apply').linkbutton('disable');
                jQuery('#event-product-btn_cancel').linkbutton('disable');
                jQuery('#event-product-btn_edit').linkbutton('enable');
            }
        },
        onBeforeLoad: function () {
            return ZPselected.plot_id !== 0;
        },
        onClickRow: function (index, row) {
            selectedIndex = index;
            jQuery(this).datagrid('selectRow', index);
        },
        onClickCell: function (index, field, value) {
            selectedIndex = index;
        },
        onDblClickRow: function (index, row) {
            selectedIndex = index;
            jQuery(this).datagrid('beginEdit', index);
            jQuery(this).datagrid('selectRow', index);
        },
        loadFilter: function (data) {
            if (!data || data.error) {
                return {"total": 0, "rows": []};
            } else {
                if (!data.total && !data.rows) {
                    return {"total": data.length, "rows": data};
                } else {
                    return data;
                }

            }
        }
    });

    //PRODUCE DATAGRID OT TAB 4
    var produceColumns = [
        {
            field: 'culture',
            title: '<b>Култура</b>',
            width: 180,
            align: 'left',
            editor: {
                type: 'combobox',
                options: {
                    disabled: true,
                    valueField: 'id',
                    textField: 'name',
                    required: true,
                    align: 'left',
                    onShowPanel: function () {
                        var target = jQuery(this).parent();
                        produceDatagrid.datagrid('selectRow', getRowIndex(target));
                    },
                    loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                    loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
                }
            }
        }, {
            field: 'sort',
            title: '<b>Сорт<b>',
            width: 140,
            align: 'center',
            editor: {
                type: 'textbox',
                align: 'center',
                options: {
                    disabled: false
                }
            }
        },
        {
            field: 'area',
            title: '<b>Площ<b>',
            width: 80,
            align: 'center',
            editor: {
                type: 'numberbox',
                options: {
                    disabled: false,
                    precision: 3,
                    min: 0,
                    align: 'center',
                    onChange: function (area) {
                        var row = produceDatagrid.datagrid('getSelected');
                        if (selectedIndex != null && selectedIndex >= 0 && row) {
                            var produce = produceDatagrid.datagrid('getEditor', {
                                index: selectedIndex,
                                field: 'produce'
                            }).target.numberbox('getValue');
                            update_produce_per_dka(produce, area);
                        }
                    }
                }
            }
        },
        {
            field: 'produce',
            title: '<b>Добив<b>',
            align: 'center',
            width: 80,
            editor: {
                type: 'numberbox',
                options: {
                    disabled: false,
                    precision: 4,
                    min: 0,
                    align: 'center',
                    onChange: function (produce) {
                        var row = produceDatagrid.datagrid('getSelected');
                        if (selectedIndex != null && selectedIndex >= 0 && row) {
                            var area = produceDatagrid.datagrid('getEditor', {
                                index: selectedIndex,
                                field: 'area'
                            }).target.numberbox('getValue');

                            update_produce_per_dka(produce, area);

                        }
                    }
                }
            }
        },
        {
            field: 'produce_per_dka',
            title: '<b>Добив на дка<b>',
            width: 100,
            align: 'center',
            editor: {
                type: 'numberbox',
                options: {
                    disabled: true,
                    precision: 4,
                    min: 0,
                    align: 'center',
                }
            }
        }
    ];

    produceDatagrid.datagrid({
        title: 'Информация за добиви',
        singleSelect: true,
        fitColumns: false,
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        fit: true,
        border: true,
        rownumbers: true,
        showFooter: true,
        toolbar: [{
            id: 'event-product-btn_add', text: 'Добавяне', iconCls: 'icon-add',
            handler: function () {
                var new_row = {
                    editing: true,
                    culture: ZPselected.culture,
                    culture_name: ZPselected.culture_name,
                    sort: null,
                    area: ZPselected.area,
                    produce: null,
                    produce_per_dka: null
                };

                var hasEditingRow = false;
                var products = produceDatagrid.datagrid('getData');
                for(var product of products.rows) {
                    if(product.editing) {
                        hasEditingRow = true;
                        break;
                    }
                }

                if (hasEditingRow) {
                    jQuery.messager.alert('Грешка', 'Моля запазете текущия ред', 'warning');
                    return false;
                };

                produceDatagrid.datagrid('insertRow', {index: 0, row: new_row});
                produceDatagrid.datagrid('selectRow', 0);
                produceDatagrid.datagrid('beginEdit', 0);
            }
        }, {
            id: 'event-product-btn_edit', text: 'Редактиране', iconCls: 'icon-edit',
            handler: function () {
                if (selectedIndex != null) {
                    if(hasWarehouseRights){
                        editingRow = true;
                    }
                    produceDatagrid.datagrid('beginEdit', selectedIndex);
                    produceDatagrid.datagrid('selectRow', selectedIndex);
                }
            }
        }, {
            id: 'event-product-btn-apply', text: 'Запази', iconCls: 'icon-ok',
            handler: function () {
                var row = produceDatagrid.datagrid('getSelected');
                if (!row) return false;
                if (row.editing === true && selectedIndex != null) {

                    produceDatagrid.datagrid('endEdit', selectedIndex);
                }
            }
        }, {
            id: 'event-product-btn_cancel', text: 'Откажи', iconCls: 'icon-redirect',
            handler: function () {
                var row = produceDatagrid.datagrid('getSelected');
                if (!row) return false;
                if (row.editing == true && selectedIndex != null) {
                    produceDatagrid.datagrid('cancelEdit', selectedIndex);
                    produceDatagrid.datagrid('selectRow', selectedIndex);
                }
            }
        }, {
            id: 'event-product-btn_delete', text: 'Премахване', iconCls: 'icon-delete',
            handler: function () {
                if (selectedIndex != null && selectedIndex >= 0) {
                    jQuery.messager.confirm('Confirm', 'Сигурни ли сте?', function (r) {
                        if (r) {
                            produceDatagrid.datagrid('deleteRow', selectedIndex);
                        }
                    });
                }
            }
        }],
        columns: [produceColumns],
        onBeginEdit: function (index, row) {
            if (!row.values) row.values = row;
            // we update here data from row.values to each editor for each column
            var columns = produceDatagrid.datagrid('getColumnFields');
            for (var i = 0; i < columns.length; i++) {
                var editor = produceDatagrid.datagrid('getEditor', {index: index, field: columns[i]});
                var column = columns[i];
                var value = row.values[column];
                if (!editor) continue;
                if (editor.type === 'checkbox') {
                    jQuery(editor.target).prop("checked", value);
                } else if (editor.type === 'combobox') {
                    jQuery(editor.target).combobox('select', value);
                    if(column === 'culture' && row.culture_name) {
                        jQuery(editor.target).combobox('setText', row.culture_name);
                    }
                } else {
                    jQuery(editor.target)[editor.type]('setValue', value);
                }
            }
        },
        onEndEdit: function (index, row) {
            var columns = produceDatagrid.datagrid('getColumnFields');
            row.values = {}; // we save and store here data values from the editor
            for (var i = 0; i < columns.length; i++) {
                var field = columns[i];
                var editor = produceDatagrid.datagrid('getEditor', {index: index, field: field});
                var value = null;
                var text = null;
                if (!editor) continue;
                if (editor.type === 'checkbox') {
                    value = jQuery(editor.target).is(':checked');
                    text = (value) ? 'Да' : 'Не';
                } else {
                    value = jQuery(editor.target)[editor.type]('getValue');
                    text = jQuery(editor.target)[editor.type]('getText');
                }
                row[field] = text;
                row.values[field] = value;
            }
        },
        onLoadSuccess: function (data) {
            //trigger edit on rows so we can trigger values injection in editors
            if (data.rows.length > 0) {
                var count = data.rows.length;
                for (var i = 0; i < count; i++) {
                    jQuery(produceDatagrid).datagrid('beginEdit', i);
                    jQuery(produceDatagrid).datagrid('endEdit', i);
                }
            }
            jQuery(this).datagrid('fitColumns');
        },
        onBeforeEdit: function (index, row) {
            if (!row) return false;
            row.editing = true;
            selectedIndex = index;
            jQuery(this).datagrid('refreshRow', index);
        },
        onAfterEdit: function (index, row) {
            row.editing = false;
            jQuery(this).datagrid('refreshRow', index);
        },
        onCancelEdit: function (index, row) {
            row.editing = false;
            jQuery(this).datagrid('refreshRow', index);
        },
        onSelect: function (index, row) {
            selectedIndex = index;
            if (!row) {
                jQuery('#event-product-btn_edit').linkbutton('disable');
                return;
            }
            if (row && row.editing === true) {
                jQuery('#event-product-btn-apply').linkbutton('enable');
                jQuery('#event-product-btn_cancel').linkbutton('enable');
                jQuery('#event-product-btn_edit').linkbutton('disable');

            } else {
                jQuery('#event-product-btn-apply').linkbutton('disable');
                jQuery('#event-product-btn_cancel').linkbutton('disable');
                jQuery('#event-product-btn_edit').linkbutton('enable');
            }
        },
        onBeforeLoad: function () {
            return ZPselected.plot_id !== 0;
        },
        onClickRow: function (index, row) {
            selectedIndex = index;
            jQuery(this).datagrid('selectRow', index);
        },
        onClickCell: function (index, field, value) {
            selectedIndex = index;
        },
        onDblClickRow: function (index, row) {
            selectedIndex = index;
            jQuery(this).datagrid('beginEdit', index);
        },
        loadFilter: function (data) {
            if (!data || data.error) {
                return {"total": 0, "rows": []};
            } else {
                if (!data.total && !data.rows) {
                    return {"total": data.length, "rows": data};
                } else {
                    return data;
                }

            }
        }
    });



    var total_cost_products = jQuery('<td><label for="event-substance-total-money"><b>Обща стойност </b></label><input readonly disabled id="event-substance-total-money" type="text" style="width:80px;text-align: center;"/></td>');
    jQuery('#events_win_tabs .tabs-panels>div:nth-child(3) .datagrid-toolbar table tr:last').append(total_cost_products);

    /*--------------DATE-TIME FIELDS-------------------*/
    jQuery('#event-plan-date-from').find('input').datebox({
        value: new Date().toString('dd-MMM-yyyy'),
        required: true,
        onChange: onEventTimeRangeChange,
        onSelect: onEventTimeRangeChange,
    });
    jQuery('#event-plan-date-to').find('input').datebox({
        value: new Date().toString('dd-MMM-yyyy'),
        required: true,
        onChange: onEventTimeRangeChange,
        onSelect: onEventTimeRangeChange,
    });
    jQuery('#event-plan-time-from').find('> input').timespinner({
        value: '00:00:00',
        showSeconds: true,
        required: true,
        onChange: onEventTimeRangeChange,
        onSpinUp: onEventTimeRangeChange,
        onSpinDown: onEventTimeRangeChange
    });
    jQuery('#event-plan-time-to').find('> input').timespinner({
        value: '23:59:59',
        showSeconds: true,
        required: true,
        onChange: onEventTimeRangeChange,
        onSpinUp: onEventTimeRangeChange,
        onSpinDown: onEventTimeRangeChange
    });
    jQuery("#event-complete-date-from").find("> input").datebox({
        value: new Date().toString('dd-MMM-yyyy'),
        required: true,
        onChange: onEventTimeRangeChange,
        onSelect: onEventTimeRangeChange,
    });
    jQuery("#event-complete-date-to").find("> input").datebox({
        value: new Date().toString('dd-MMM-yyyy'),
        required: true,
        onChange: onEventTimeRangeChange,
        onSelect: onEventTimeRangeChange,
    });
    jQuery("#event-complete-time-from").find('> input').timespinner({
        value: '00:00:00',
        showSeconds: true,
        required: true,
        onChange: onEventTimeRangeChange,
        onSpinUp: onEventTimeRangeChange,
        onSpinDown: onEventTimeRangeChange
    });
    jQuery("#event-complete-time-to").find('> input').timespinner({
        value: '23:59:59',
        showSeconds: true,
        required: true,
        onChange: onEventTimeRangeChange,
        onSpinUp: onEventTimeRangeChange,
        onSpinDown: onEventTimeRangeChange
    });

    jQuery("#event-processed-area > input").numberbox();

    function manageUtilsMeasures(product) {
        var productsMeasuresComboboxData = substanceUnitTypeCombobox.combobox("getData");
        if(product.warehouse_item_id) {
            var warehouseItemData = JSON.parse(product.options);
            var materialUnitType;
            for(var norm of productsMeasuresComboboxData) {
                // Transform unit norm to unit measure by removing /дка from the norm. For example кг/дка becomes кг.
                // It is needed because the warehouse API works with measures but there are norms in techofarm
                var measure = norm.name.replace("/дка", "");
                if(measure.toLowerCase() === warehouseItemData.measure_name.toLowerCase()){
                    materialUnitType = norm.id; break;
                } else if(measure.toLowerCase() === warehouseItemData.measure_short_name.replace(/\.+$/g, '').toLowerCase()) {
                    materialUnitType = norm.id; break;
                }
            }

            if(!materialUnitType) {
                jQuery.messager.alert('Грешка', "Нещо се обърка. Моля пробвайте да синхонизирате продуктите отново.");
            }

            substanceUnitTypeCombobox.combobox("select", materialUnitType);
            substanceUnitTypeCombobox.combobox("disable");

        } else {
            substanceUnitTypeCombobox.combobox("select", "");
            substanceUnitTypeCombobox.combobox("enable");
        }
    }

    function checkProductAvailability(product, callFunctionFrom = null, rowIndex = null) {
        if(!product.warehouse_item_id) return true;

        var eventPhase = jQuery('#event-phase').find('> input').combobox("getValue")
        if(parseInt(eventPhase) !== 2) return;  //Use warehouse quantity only if event phase is Изпълнена (2)

        var plotData = ZP_TREE_CTRL.tree('getSelected');
        var requestData = {
            date: jQuery("#event-complete-date-from").find("> input").datebox("getValue"),
            company: plotData.attributes.farming,
            item: {
                productId: product.id,
                productWarehouseId: product.warehouse_item_id,
            }
        };

        TF.Rpc.Diary.DiaryAuxiliaryItems.checkProductAvailability(requestData)
            .done(function (data) {
                if(!data.haveItems && callFunctionFrom === 'substance_choice') {
                    jQuery.messager.alert('Предупреждение', 'Избрали сте артикул от склада, но такъв не е изписан за съответната дата.');
                    product_datagrid.datagrid('beginEdit', rowIndex);
                    var a = product_datagrid.datagrid('getEditor', {index: rowIndex, field: 'warehouse_quantity'});
                    jQuery(a.target).textbox('setValue', 0)
                    return;
                }
                var productsData = product_datagrid.datagrid("getData");
                for(var i = 0; i < productsData.rows.length; i++) {
                        var consumed_editor = product_datagrid.datagrid('getEditor', {index: i, field: 'substance_consumed'});
                        if(rowIndex !== null && rowIndex === i) {
                            var qty = 0;
                            if(consumed_editor) {
                                var q = parseFloat(jQuery(consumed_editor.target).numberbox('getValue'));
                                if(!isNaN(q)) {
                                    qty = q;
                                }
                            }
                            var currentProductQuantity = 0;
                            if (editingRow){
                                currentProductQuantity = data.totalUsedQuantity;
                            } else {
                                currentProductQuantity = data.totalUsedQuantity + (isNaN(parseFloat(consumed_editor.oldHtml)) ? 0 : parseFloat(consumed_editor.oldHtml)) - qty;
                            }

                            var productQuantity = Math.round((currentProductQuantity) * 100) / 100;
                            if(productQuantity <= 0) {
                                jQuery.messager.alert('Предупреждение', 'Превишавате изписаното от склада количество.');
                                return false;
                            }

                            product_datagrid.datagrid('beginEdit', i);
                            var warehouse_quantity = product_datagrid.datagrid('getEditor', {index: i, field: 'warehouse_quantity'});
                            var warehouse_single_price = product_datagrid.datagrid('getEditor', {index: i, field: 'substance_unit_price'});

                            jQuery(warehouse_quantity.target).textbox('setValue', productQuantity)
                            jQuery(warehouse_single_price.target).textbox('setValue', data.warehouseProductPrice)
                        }
                    }
                editingRow = false;
            })
            .fail(function (errorObj) {
                jQuery.messager.alert('Грешка', errorObj.getMessage());
            });
    }

    function isProductSelected(product) {
        if(isNaN(parseInt(product))) return false;
        var productsData = product_datagrid.datagrid("getData");
        for(var i = 0; i < productsData.rows.length; i++) {
            if(productsData.rows[i].editing) continue;
            var substance_id = productsData.rows[i].values.substance_id;
            if(parseInt(product) === parseInt(substance_id)) {
                return true;
            };
        }

        return false;
    }

    function onEventTimeRangeChange(){
        if (!jQuery('#win-add-edit-event').window('options').closed) {
            var expense_id = jQuery('#event-subtype > input').combobox('getValue');
            var event = getEventTime();
            if (expense_id == undefined || expense_id == '' && event !== null) {
                return;
            }
            var all_expense_data = jQuery('#event-subtype > input').combobox('getData');

            for(var i = 0 ; i< all_expense_data.length ; i++){
                var expense = all_expense_data[i];
                if (isValidExpense(expense, event)){
                    all_expense_data[i].group = 'Разходи';
                }else{
                    all_expense_data[i].group = 'Вид';
                }
            }
            jQuery("#event-subtype > input").combobox('loadData', all_expense_data);
        }
    }

    jQuery('#event-amortization').find('input').numberbox({
        precision: 2, min: 0,
        onChange: function (new_val, old_val) {
            updateTotalField();
        }
    });
    jQuery("#event-rent-cost input[type='text']").numberbox({
        disabled: true, precision: 2, min: 0,
        onChange: function (new_val, old_val) {
            updateTotalField();
        }
    });
    jQuery("#event-total input[type='text']").numberbox({disabled: true, precision: 2, min: 0});
    jQuery("#event-movement-fuel input[type='text']").numberbox({disabled: true, precision: 2, min: 0});
    jQuery("#event-parking-fuel input[type='text']").numberbox({disabled: true, precision: 2, min: 0});
    jQuery("#event-start-fuel-level input[type='text']").numberbox({disabled: true, precision: 2, min: 0});
    jQuery("#event-end-fuel-level input[type='text']").numberbox({disabled: true, precision: 2, min: 0});
    jQuery("#event-avg-engine-revs input[type='text']").numberbox({disabled: true, precision: 2, min: 0});
    jQuery("#event-avg-speed input[type='text']").numberbox({disabled: true, precision: 2, min: 0});
    jQuery("#event-mileage input[type='text']").numberbox({disabled: true, precision: 2, min: 0});
    jQuery('#event-time-in input').timespinner({disabled: true, showSeconds: true});
    jQuery('#event-time-out input').timespinner({disabled: true, showSeconds: true});
    jQuery('#event-total-time-in input').timespinner({disabled: true, showSeconds: true});
    jQuery('#event-movement-time input').timespinner({disabled: true, showSeconds: true});
    jQuery('#event-parking-time input').timespinner({disabled: true, showSeconds: true});
    // keep for backward compatibilty
    jQuery("#event-plan-fuel-cost").find("input").numberbox({precision: 2, min: 0});
    jQuery("#event-final-fuel-cost").find("input").numberbox({precision: 2, min: 0});
    jQuery("#event-processed-area > input").numberbox({
        disabled: false,
        precision: 2,
        min: 0,
        onChange: function (processed_area, old_val) {
            var calculate_by = jQuery('#event-fuel-unit').combobox('getValue');
            var cost_per_liter = jQuery("#event-per-litre-fuel-cost").numberbox('getValue');
            var fuel_consumed = jQuery("#event-fuel-consumed-qty > input").numberbox('getValue');
            if (cost_per_liter > 0 && fuel_consumed > 0 && processed_area > 0 && calculate_by == 2) {
                jQuery("#event-fuel-cost > input").numberbox('setValue', (cost_per_liter * fuel_consumed * processed_area));
            }
            updateTotalField();
        }
    });

    jQuery("#event-fuel-consumed-qty input[type='text']").numberbox({
        disabled: false,
        precision: 2,
        min: 0,
        onChange: function (fuel_consumed, old_val) {
            var calculate_by = jQuery('#event-fuel-unit').combobox('getValue');

            var cost_per_liter = jQuery("#event-per-litre-fuel-cost").numberbox('getValue');
            if (fuel_consumed > 0 && cost_per_liter > 0 && calculate_by == 1) {
                jQuery("#event-fuel-cost > input").numberbox('setValue', (fuel_consumed * cost_per_liter));
            }
            var processed_area = jQuery("#event-processed-area > input").numberbox('getValue');
            if (cost_per_liter > 0 && fuel_consumed > 0 && processed_area > 0 && calculate_by == 2) {
                jQuery("#event-fuel-cost > input").numberbox('setValue', (cost_per_liter * fuel_consumed * processed_area));
            }
            updateTotalField();
        }
    });
    jQuery("#event-per-litre-fuel-cost").numberbox({
        precision: 2,
        min: 0,
        onChange: function (cost_per_liter, old_val) {
            var calculate_by = jQuery('#event-fuel-unit').combobox('getValue');

            var fuel_consumed = jQuery("#event-fuel-consumed-qty > input").numberbox('getValue');
            if (cost_per_liter > 0 && fuel_consumed > 0 && calculate_by == 1) {
                jQuery("#event-fuel-cost > input").numberbox('setValue', (cost_per_liter * fuel_consumed));
            }
            var processed_area = jQuery("#event-processed-area > input").numberbox('getValue');
            if (cost_per_liter > 0 && fuel_consumed > 0 && processed_area > 0 && calculate_by == 2) {
                jQuery("#event-fuel-cost > input").numberbox('setValue', (cost_per_liter * fuel_consumed * processed_area));
            }
            updateTotalField();
        }
    });
    jQuery('#event-fuel-unit').combobox({
        textField: 'name',
        valueField: 'id',
        required: true,
        data: [
            {id: 1, name: 'литра', selected: true},
            {id: 2, name: 'Литър / дка'},
        ],
        onSelect: function (record) {
            if (!record) return;
            if (record.id == 1) {
                jQuery('#event-fuel-unit-label').text('Общ отчетен разход (л)');
                jQuery('#processed-area-label').css('visibility', 'hidden');
                jQuery('#event-processed-area').css('visibility', 'hidden');
            }
            if (record.id == 2) {
                jQuery('#event-fuel-unit-label').text('Отчетен разход (л/дка)');
                jQuery('#processed-area-label').css('visibility', 'visible');
                jQuery('#event-processed-area').css('visibility', 'visible');
            }
            var calculate_by = record.id;
            var cost_per_liter = jQuery("#event-per-litre-fuel-cost").numberbox('getValue');
            var fuel_consumed = jQuery("#event-fuel-consumed-qty > input").numberbox('getValue');
            if (cost_per_liter > 0 && fuel_consumed > 0 && calculate_by == 1) {
                jQuery("#event-fuel-cost > input").numberbox('setValue', (cost_per_liter * fuel_consumed));
                updateTotalField();
            }
            var processed_area = jQuery("#event-processed-area > input").numberbox('getValue');
            if (cost_per_liter > 0 && fuel_consumed > 0 && processed_area > 0 && calculate_by == 2) {
                jQuery("#event-fuel-cost > input").numberbox('setValue', (cost_per_liter * fuel_consumed * processed_area));
                updateTotalField();
            }
        },
        onLoadSuccess: function (record) {
            if (record == undefined) return;
            if (record.id == 1 || record.id == undefined) {
                jQuery('#event-fuel-unit-label').text('Общ отчетен разход (л)');
                jQuery('#processed-area-label').css('visibility', 'hidden');
                jQuery('#event-processed-area').css('visibility', 'hidden');
            }
            if (record.id == 2) {
                jQuery('#event-fuel-unit-label').text('Отчетен разход (л/дка)');
                jQuery('#processed-area-label').css('visibility', 'visible');
                jQuery('#event-processed-area').css('visibility', 'visible');
            }

        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery("#event-fuel-cost > input").numberbox({disabled: true, precision: 2, min: 0});

    initEventPhaseCombobox();

    jQuery('#event-treatable_area').numberbox({
        disabled: false,
        precision: 2,
        min: 0,
        max: ZPselected.area || 0,
        onChange: function (new_val, old_val) {
            var products = product_datagrid.datagrid('getData');
            if (products && products.rows.length && new_val != '') {
                for (var i = 0; i < products.rows.length; i++) {
                    jQuery(product_datagrid).datagrid('beginEdit', i);
                    var editor = product_datagrid.datagrid('getEditor', {index: i, field: 'treated_area'});
                    jQuery(editor.target).numberbox('options').max = parseFloat(new_val);
                    if (jQuery(editor.target).numberbox('getValue') > parseFloat(new_val)) {
                        jQuery(editor.target).numberbox('setValue', parseFloat(new_val));
                    }
                    jQuery(product_datagrid).datagrid('endEdit', i);
                }
            }
        }
    });

    function resetGeofenceCrossingFields() {
        jQuery("#event-time-in > input").timespinner('setValue');
        jQuery("#event-time-out > input").timespinner('setValue');
        jQuery("#event-parking-time > input").timespinner('setValue');
    }

    jQuery('#event-machine-rented > input').change(function () {
        if (jQuery('#event-machine-rented > input').is(':checked')) {
            jQuery("#event-rent-cost .numberbox-f").numberbox('enable');
        } else {
            jQuery("#event-rent-cost .numberbox-f").numberbox('disable');
            jQuery("#event-rent-cost .numberbox-f").numberbox('setValue');
        }
    });
    if (jQuery('#event-machine-rented > input').is(':checked')) {
        jQuery("#event-rent-cost .numberbox-f").numberbox('enable');
    }
}

function initEventPhaseCombobox(){
    jQuery('#event-phase').find('> input').combobox({
        data:[{id:1,name:"Планирана"},{id:2,name:"Изпълнена"}],
        editable: false,
        textField: 'name',
        required: true,
        valueField: 'id',
        onLoadSuccess: function (data) {
            if(data.result) data = data.result;
            var value =  jQuery(this).combobox('getValue');
            if (value == '') {
                jQuery(this).combobox('setValue',EVENT_PLANNED);
            }
            //handle case EVENT TYPE IS COMPLETED (id 2)
            if (value == EVENT_COMPLETE) {
                jQuery('#wialon_reload_btn').linkbutton('enable');
                //enable final fields
                jQuery("#event-avg-engine-revs .numberbox-f").numberbox('enable');
                jQuery("#event-avg-speed .numberbox-f").numberbox('enable');
                jQuery("#event-mileage .numberbox-f").numberbox('enable');

                jQuery('#event-complete-time-from .timespinner-f').timespinner('enable');
                jQuery('#event-complete-time-to .timespinner-f').timespinner('enable');

                jQuery('#event-time-in .timespinner-f').timespinner('enable');
                jQuery('#event-time-out .timespinner-f').timespinner('enable');
                jQuery('#event-total-time-in .timespinner-f').timespinner('enable');
                jQuery('#event-movement-time .timespinner-f').timespinner('enable');
                jQuery('#event-parking-time .timespinner-f').timespinner('enable');
            } else {
                jQuery('#wialon_reload_btn').linkbutton('disable');
            }
        },
        onSelect: function (record) {
            if (!record) return;
            // IN CASE EVENT TYPE == COMPLETED (id:2), WE CAN MAKE CALCULATION ON TRIP'S FUEL.
            var enable = (record.id == EVENT_COMPLETE) ? 'enable' : 'disable';
            if (enable == 'enable') {
                //show correct dates
                jQuery('#plan-dates').hide();
                jQuery('#plan-times').hide();
                jQuery('#complete-dates').show();
                jQuery('#complete-times').show();
            } else {
                jQuery('#complete-dates').hide();
                jQuery('#complete-times').hide();
                jQuery('#plan-dates').show();
                jQuery('#plan-times').show();
            }
            jQuery('#wialon_reload_btn').linkbutton(enable);
            //enable final fields
            jQuery("#event-avg-engine-revs .numberbox-f").numberbox(enable);
            jQuery("#event-avg-speed .numberbox-f").numberbox(enable);
            jQuery("#event-mileage .numberbox-f").numberbox(enable);

            jQuery('#event-complete-time-from .timespinner-f').timespinner(enable);
            jQuery('#event-complete-time-to .timespinner-f').timespinner(enable);

            jQuery('#event-time-in .timespinner-f').timespinner(enable);
            jQuery('#event-time-out .timespinner-f').timespinner(enable);
            jQuery('#event-total-time-in .timespinner-f').timespinner(enable);
            jQuery('#event-movement-time .timespinner-f').timespinner(enable);
            jQuery('#event-parking-time .timespinner-f').timespinner(enable);
            resetFuelTab();
            resetFarmTrackTab();
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initEventPerformerCombobox(){
    jQuery('#event-performer > input').combobox({
        url: 'index.php?diary-rpc=diary-configs-combobox',
        rpcParams: [{
            request_type: 9
        }],
        editable: false,
        textField: 'name',
        valueField: 'id',
        groupField: 'group',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initEventTypeCombobox() {
    jQuery('#event-type > input').combobox({
        url: 'index.php?diary-rpc=diary-configs-combobox',
        rpcParams: [{
            request_type: 1
        }],
        editable: false,
        required: true,
        textField: 'name',
        valueField: 'id',
        onChange: function(){
            loadSubtypeExpense({use_type_id: true});
            var event_validation_obj = isEventValid();
            enableDisableTabs('#events_win_tabs', event_validation_obj.valid);
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initMachinesCombobox() {
    jQuery('#event-machine').find('> input').combobox({
        url: 'index.php?diary-rpc=diary-configs-combobox',
        rpcParams: [{
            request_type: 34,
            record_none: true
        }],
        groupField: 'group',
        groupFormatter: function (group) {
            if (group.indexOf('FarmTrack') !== -1) {
                return '<span style="color:#e22625"><b>' + group + '</b></span>';
            }
            return '<span style="color:#3b4454"><b>' + group + '</b></span>';
        },
        editable: false,
        textField: 'name',
        valueField: 'id',
        onSelect: function (selection) {
            var fuel_report = null;
            var geofence_report = null;
            var row = null;
            if (
                !selection ||
                !selection.name ||
                selection.name === '-' ||
                selection.group.indexOf("FarmTrack") === -1
            )
                return;
            for (var key in fuel_report_obj.data.unit_group_zones_visit) {
                if (!fuel_report_obj.data.unit_group_zones_visit.hasOwnProperty(key)) {
                    continue;
                }
                row = fuel_report_obj.data.unit_group_zones_visit[key];
                //grouping is the unit name from the wialon template report
                if (row.Grouping != selection.name) {
                    continue;
                }
                geofence_report = row;
                break;

            }
            for (key in fuel_report_obj.data.unit_group_engine_hours) {
                if (!fuel_report_obj.data.unit_group_engine_hours.hasOwnProperty(key)) {
                    continue;
                }
                row = fuel_report_obj.data.unit_group_engine_hours[key];
                //grouping is the unit name from the wialon template report
                if (row.Grouping != selection.name) {
                    continue;
                }
                fuel_report = row;
                break;
            }
            if (fuel_report != null) {
                setFuelFields(fuel_report);
            } else {
                resetFuelFields();
            }
            if (geofence_report != null) {
                resetFarmTrackTab();
                resetFuelFields();
                var time_in = geofence_report['Time in'].v;
                var time_out = geofence_report['Time out'].v;
                time_in = new Date(time_in * 1000);
                time_out = new Date(time_out * 1000); // guarda se e un oggetto
                var parking = geofence_report['Parkings duration'];
                jQuery("#event-avg-engine-revs > input").numberbox('setValue', geofence_report['Avg engine revs']);
                jQuery("#event-avg-speed > input").numberbox('setValue', geofence_report['Avg speed']);
                jQuery("#event-mileage > input").numberbox('setValue', parseFloat(geofence_report['Mileage']));
                jQuery("#event-time-in").find("> input").timespinner('setValue', time_in.getHours() + ':' + time_in.getMinutes() + ':' + time_in.getSeconds());
                jQuery("#event-movement-fuel > input").numberbox('setValue', geofence_report['Consumed']);
                jQuery("#event-time-out").find("> input").timespinner('setValue', time_out.getHours() + ':' + time_out.getMinutes() + ':' + time_out.getSeconds());
                jQuery("#event-total-time-in > input").timespinner('setValue',geofence_report['Total time']);
                jQuery("#event-movement-time > input").timespinner('setValue',geofence_report['Total time']);
                jQuery("#event-parking-time").find("> input").timespinner('setValue', parking);
            } else {
                resetGeofenceCrossingFields();
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initMachineAttachmentCombobox() {
    jQuery('#event-attachment > input').combobox({
        url: 'index.php?diary-rpc=diary-configs-combobox',
        rpcParams: [{
            request_type: 6,
            record_none: true
        }],
        editable: false,
        textField: 'name',
        valueField: 'id',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

// grid method
function getRowIndex(target) {
    var tr = jQuery(target).closest('tr.datagrid-row');
    return parseInt(tr.attr('datagrid-row-index'));
}

function get_ZP_TRACK_window_data() {
    var params = {};
    params.date_from = jQuery('#track_dateFrom').datebox('getValue');
    params.date_to = jQuery('#track_dateTo').datebox('getValue');
    params.time_from = jQuery('#track_timeFrom').timespinner('getValue');
    params.time_to = jQuery('#track_timeTo').timespinner('getValue');
    params.farming = ZPselected.farming;
    params.year = ZPselected.year;
    params.plot_id = ZPselected.plot_id;
    return params;
}

function show_selected_wialonTrack() {
    var track_data = ZP_TRACK_GRID.treegrid('getSelected');
    if (!track_data) return;
    var obj = {};
    obj.machine_id = track_data['unit_id'];
    var d = new Date(track_data['Time Start']['v'] * 1000);
    obj.time_from = ("0" + d.getDate()).slice(-2) + "-" + ("0" + (d.getMonth() + 1)).slice(-2) + "-" + d.getFullYear() + " " + ("0" + d.getHours()).slice(-2) + ":" + ("0" + d.getMinutes()).slice(-2);
    d = new Date(track_data['Time End']['v'] * 1000);
    obj.time_to = ("0" + d.getDate()).slice(-2) + "-" + ("0" + (d.getMonth() + 1)).slice(-2) + "-" + d.getFullYear() + " " + ("0" + d.getHours()).slice(-2) + ":" + ("0" + d.getMinutes()).slice(-2);
    TF.Rpc.Diary.WialonActions.getMessages(obj)
        .done(function (data) {
            loadMapMessages(data);
            map.updateSize();
        })
        .fail(function (errorObj) {
            if (errorObj.is(TF.Rpc.ExceptionsList.INVALID_OR_EXPIRED_TOKEN)) {
                jQuery('#win-get-wialon-token').window('open');
                return;
            }
            jQuery.messager.alert('Грешка', errorObj.getMessage());
        });
}

function getZPlotData(e) {
    var layerData = COMBO_TREE_CTRL.combotree('tree').tree('getSelected');
    if (layerData.attributes.table) {
        var obj = {};
        obj.layer = layerData.attributes.table;
        obj.x = parseInt(e.xy.x);
        obj.y = parseInt(e.xy.y);
        obj.bbox = map.getExtent().toBBOX();
        obj.width = map.size.w;
        obj.height = map.size.h;
        TF.Rpc.Diary.DiaryMap.getMapZPlotInfo(obj)
            .done(function (data) {
                selectZPlotTreeNode(data);
            })
            .fail(function (errorObj) {
                if (errorObj.is(TF.Rpc.ExceptionsList.MAP_EMPTY_AREA)) {
                    jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.MAP_EMPTY_AREA.message, 'error');
                }
            })
    }
}

function populate_units_combobox(data) {
    var combo = jQuery('#event-machine').find('> input');
    var combo_data = combo.combobox('getData');
    var temp = [];
    // colelct units from group 'Всички машини'
    for (var i = 0; i < combo_data.length; i++) {
        if (combo_data[i].group == 'Всички машини') {
            temp.push(combo_data[i]);
        }
    }
    if (data == undefined || data.farm_track_units == undefined) {
        combo.combobox('loadData', temp);
        return;
    }
    //keep reference of fuel report so we can
    //fill the form on machine selection without calling the api
    fuel_report_obj = data;
    var wialon_grp = [];
    var machines_not_in_db = [];
    /*
     here we need to assign the correct ID to the wialon's machine.
     we use the ones that we already have in the category all machines, from the db.
     If there is a new machine coming from the report, and we don't have and id for it,
     an alert is displayed to the user
     */
    for (var key in data.farm_track_units) {
        if (!data.farm_track_units.hasOwnProperty(key)) continue;
        row = data.farm_track_units[key];
        row.group = 'Данни от FarmTrack';
        row.id = row.wialon_id;
        delete(row.wialon_id);
        wialon_grp.push(row);
    }
    /* here we merge the two categories */
    if (machines_not_in_db.length > 0) jQuery.messager.alert('внимание', 'Моля, синхронизирайте Вашите машини преди изпълнение на тази функция');
    combo.combobox('loadData', wialon_grp.concat(temp));
}

function resetUnitsComboBox() {
    fuel_report_obj = null;
    var combo = jQuery('#event-machine').find('> input');
    if(!combo.data().hasOwnProperty('combobox')){
        initMachinesCombobox();
    }
    var combo_data = combo.combobox('getData');
    // here we remove the entries with group "danni ot farmtrack" , to reset the combobox to his basic state
    for (var i = 0; i < combo_data.length; i++) {
        if (combo_data[i].group !== "Всички машини" && combo_data[i].name !== '-') {
            combo_data.splice(i, 1);
        }
    }
    combo.combobox('loadData', combo_data);
}
/* global var to hold report data about unit's fuel */
var fuel_report_obj = null;
function checkTimeFrame(obj) {
    var d1 = null;
    var d2 = null;
    if(obj.date_from !== undefined && obj.date_to !== undefined && obj.time_from !== undefined && obj.time_to !== undefined ){
        d1 = new Date(obj.date_from + ' ' + obj.time_from);
        d2 = new Date(obj.date_to + ' ' + obj.time_to);
    }
    if(obj.complete_date_from !== undefined && obj.complete_date_to !== undefined && obj.complete_time_from !== undefined && obj.complete_time_to !== undefined ){
        d1 = new Date(obj.complete_date_from + ' ' + obj.complete_time_from);
        d2 = new Date(obj.complete_date_to + ' ' + obj.complete_time_to);
    }
    if(obj.plan_date_from !== undefined && obj.plan_date_to !== undefined && obj.plan_time_from !== undefined && obj.plan_time_to !== undefined ){
        d1 = new Date(obj.plan_date_from + ' ' + obj.plan_time_from);
        d2 = new Date(obj.plan_date_to + ' ' + obj.plan_time_to);
    }
    if (d2 - d1 <= 0) {
        return false;
    }
    return true;
}
/* whne pressing the wialon button */
function getFuelUnitsPlot() {

    var plot = ZP_TREE_CTRL.tree('getSelected');
    if (!plot) return jQuery.messager.alert('Грешка', "Няма избран парцел!");
    var event_form_data = getEditEventFields();
    var obj = {};

    obj.farming_id = plot['attributes'].farming;
    obj.year_id = plot['attributes'].year;
    obj.plot_id = plot.id;
    obj.date_from = event_form_data.complete_date_from;
    obj.date_to = event_form_data.complete_date_to;
    obj.time_from = event_form_data.complete_time_from;
    obj.time_to = event_form_data.complete_time_to;

    obj.timezoneOffset = new Date(obj.date_from + " " + obj.time_from).getTimezoneOffset();
    if(checkTimeFrame(obj) === false){
        return jQuery.messager.alert('Грешка', 'Началният и крайният час не може да съвпадат');
    }
    TF.Rpc.Diary.WialonActions.getFuelUnitsPlot(obj)
        .done(function (data) {
            jQuery('#win-add-edit-event').window('open');
            populate_units_combobox(data);
        })
        .fail(function (errorObj) {
            if (errorObj.is(TF.Rpc.ExceptionsList.INVALID_OR_EXPIRED_TOKEN)) {
                jQuery('#win-get-wialon-token').window('open');
                return;
            }
            jQuery.messager.alert('Грешка', errorObj.getMessage());
        });
}

function findUnitByName(name) {
    var combo = jQuery('#event-machine').find('> input');
    var combo_data = combo.combobox('getData');
    for (var i = 0; i < combo_data.length; i++) {
        if (combo_data[i].name == name) {
            return combo_data[i].id;
        }
    }
    return null;
}
/* when creating an event from the track window */
function enter_event() {
    var track_data = ZP_TRACK_GRID.treegrid('getSelected');
    if (!track_data) return;
    var obj = {};
    obj.phase_id = EVENT_COMPLETE;
    var d = new Date(track_data['Time Start']['v'] * 1000);
    obj.complete_date_from = d.getFullYear() + "-" + ("0" + (d.getMonth() + 1)).slice(-2) + "-" + ("0" + d.getDate()).slice(-2);
    obj.complete_time_from = ("0" + d.getHours()).slice(-2) + ":" + ("0" + d.getMinutes()).slice(-2) + ":" + ("0" + d.getSeconds()).slice(-2);
    d = new Date(track_data['Time End']['v'] * 1000);
    obj.complete_date_to = d.getFullYear() + "-" + ("0" + (d.getMonth() + 1)).slice(-2) + "-" + ("0" + d.getDate()).slice(-2);
    obj.complete_time_to = ("0" + d.getHours()).slice(-2) + ":" + ("0" + d.getMinutes()).slice(-2) + ":" + ("0" + d.getSeconds()).slice(-2);
    //db id, not wialon id
    obj.machine_id = findUnitByName(track_data['unit_name']);
    obj.time_in = obj.complete_time_from;
    obj.time_out = obj.complete_time_to;
    obj.total_time_in = track_data['Total time'];
    obj.completed_mileage = parseFloat(track_data['Mileage']);
    obj.movement_time = track_data['movement_duration'];
    obj.parking_time =  track_data['Parkings duration'];
    obj.start_fuel = (track_data._table === 'Trips') ? track_data['Initial fuel level'] : 'n.a.';
    obj.end_fuel = (track_data._table === 'Trips') ? track_data['Final fuel level'] : 'n.a.';
    obj.movement_fuel = track_data['fuel_spent'];
    obj.total_fuel_cost = track_data['fuel_spent'];
    obj.parking_fuel = 0;
    obj.avg_engine_revs = track_data['Avg engine revs'];
    obj.avg_speed = track_data['Avg speed'];
    obj.fuel_unit = 1;
    setEditEventFields(obj);
    jQuery('#win-add-edit-event').window('open');
}

function unixTimeStampToString(unix_timestamp){
    var date = new Date(unix_timestamp*1000);
    var hours = date.getHours();
    var minutes = "0" + date.getMinutes();
    var seconds = "0" + date.getSeconds();
    var formattedTime = hours + ':' + minutes.substr(-2) + ':' + seconds.substr(-2);
    return formattedTime;
}

function adjustForTimezone(date){
    var timeOffsetInMS = date.getTimezoneOffset() * 60000;
    date.setTime(date.getTime() - timeOffsetInMS);
    return date
}

function updateTotalField() {
    var total_cost = 0;
    var rent_cost = parseFloat(jQuery("#event-rent-cost > input").numberbox('getValue'));
    var amortization_cost = parseFloat(jQuery("#event-amortization > input").numberbox('getValue'));
    var fuel_cost = parseFloat(jQuery("#event-fuel-cost > input").numberbox('getValue'));
    var products_cost = parseFloat(jQuery("#event-substance-total-money").val());
    var expense_cost = parseFloat(jQuery("#event-expense_cost").val());
    if (!isNaN(rent_cost)) total_cost += rent_cost;
    if (!isNaN(amortization_cost)) total_cost += amortization_cost;
    if (!isNaN(fuel_cost)) total_cost += fuel_cost;
    if (!isNaN(products_cost)) total_cost += products_cost;
    if (!isNaN(expense_cost)) total_cost += expense_cost;
    jQuery("#event-total > input").numberbox('setValue', isNaN(total_cost) ? 0 : total_cost );
}
/** @deprecated */
function getWialonReportData() {
    if (trackMachineID == undefined) {
        jQuery.messager.alert('Грешка', 'Не е намерена активна следа!');
    } else {
        var obj = {};
        var plotData = ZP_TREE_CTRL.tree('getSelected');

        obj.farming_id = plotData['attributes'].farming;
        obj.year_id = plotData['attributes'].year;
        obj.plot_id = plotData.id;
        /* DATA FROM OLD TRACK WINDOW */
        obj.machine_id = trackMachineID;
        obj.track_date = trackDate;
        obj.track_time_from = trackTimeFrom;
        obj.track_time_to = trackTimeTo;
        TF.Rpc.Diary.WialonActions.getWialonReportData(obj)
            .done(function (data) {
                fillInfoFromWialon(data);
            })
            .fail(function (errorObj) {
                if (errorObj.is(TF.Rpc.ExceptionsList.INVALID_OR_EXPIRED_TOKEN)) {
                    jQuery('#win-get-wialon-token').window('open');
                    return;
                }
                jQuery.messager.alert('Грешка', errorObj.getMessage());
            });
    }
}
/** @deprecated */
function fillInfoFromWialon(data) {
    if (data) {
        var timeInDate = new Date(data.time_in * 1000);
        var timeInHours = timeInDate.getHours();
        var timeInMinutes = timeInDate.getMinutes();
        var timeInSeconds = timeInDate.getSeconds();
        var timeInStr = timeInHours + ':' + timeInMinutes + ':' + timeInSeconds;
        var timeOutDate = new Date(data.time_out * 1000);
        var timeOutHours = timeOutDate.getHours();
        var timeOutMinutes = timeOutDate.getMinutes();
        var timeOutSeconds = timeOutDate.getSeconds();
        var timeOutStr = timeOutHours + ':' + timeOutMinutes + ':' + timeOutSeconds;
        jQuery('#event-complete-date-from').find('> input').datebox('setValue', data.complete_date_from);
        jQuery('#event-complete-date-to').find('> input').datebox('setValue', data.complete_date_to);
        jQuery("#event-fuel-cost").find(".numberbox-f").numberbox('setValue', data.consumed_fuel);
        jQuery("#event-avg-engine-revs .numberbox-f").numberbox('setValue', data.avg_engine_revs);
        jQuery("#event-avg-speed .numberbox-f").numberbox('setValue', data.avg_speed);
        jQuery("#event-mileage .numberbox-f").numberbox('setValue', data.mileage);

        jQuery('#event-time-in .timespinner-f').timespinner('setValue', timeInStr);
        jQuery('#event-time-out .timespinner-f').timespinner('setValue', timeOutStr);
        jQuery('#event-total-time-in .timespinner-f').timespinner('setValue', data.duration_in);
        jQuery('#event-movement-time .timespinner-f').timespinner('setValue', data.movement_duration);
        jQuery('#event-parking-time .timespinner-f').timespinner('setValue', data.parking_duration);

        jQuery('#event-complete-time-from .timespinner-f').timespinner('setValue', data.track_time_from);
        jQuery('#event-complete-time-to .timespinner-f').timespinner('setValue', data.track_time_to);

        var price_per_litre = jQuery('#event-per-litre-fuel-cost').numberbox('getValue');
        if (price_per_litre > 0) {
            jQuery('#event-fuel-cost .numberbox-f').numberbox('setValue', price_per_litre * data.consumed_fuel);
        }
        else {
            jQuery("#event-fuel-cost > input").numberbox('setValue');
        }

        //set machine
        jQuery('#event-machine > input').combobox('setValue', data.machine_id);
    }
}

function productsSettings(product, resetFields = true) {
    var options = JSON.parse(product.options)
    if(options.product_type === 'fertilizer' || options.product_type === 'chemical_treatment') {
        productRequiredFields[product.id] = ['substance_id', 'substance_unit_type', 'substance_technic_id'];
        manageProductsColumns(['sort'], productRequiredFields[product.id], ['treated_area'], resetFields);
    } else if(options.product_type === 'seeds') {
        productRequiredFields[product.id] = ['substance_id', 'substance_unit_type'];
        manageProductsColumns(['pest_name', 'substance_technic_id', 'quarantine_period'], ['substance_unit_type'], ['treated_area'], resetFields);
    } else {
        productRequiredFields[product.id] = ['substance_id', 'substance_unit_type'];
        manageProductsColumns([], ['substance_unit_type'], ['treated_area'], resetFields);
    }
}

function manageProductsColumns(disabledColumns = [], requiredColumns = [], doNotResetFields = [], resetFields = true) {
    var editors = product_datagrid.datagrid('getEditors', selectedIndex);
    for(var editor of editors){
        if(['substance_id', 'price_per_area', 'warehouse_quantity'].includes(editor.field)) continue;
        var status = disabledColumns.includes(editor.field) ? 'disable' : 'enable';
        var required = requiredColumns.includes(editor.field);
        var reset = !doNotResetFields.includes(editor.field) && resetFields;
        if(editor.type === 'textbox'){
            if(reset) {
                jQuery(editor.target).textbox('reset');
            }
            jQuery(editor.target).textbox(status)
            jQuery(editor.target).textbox('options').required = required;
            jQuery(editor.target).textbox('textbox').validatebox('options').required = required;
            jQuery(editor.target).textbox('isValid');
        }
        else if(editor.type === 'numberbox'){
            if(reset) {
                jQuery(editor.target).numberbox('reset');
            }
            jQuery(editor.target).numberbox(status)
            jQuery(editor.target).numberbox('options').required = required;
            jQuery(editor.target).numberbox('textbox').validatebox('options').required = required;
            jQuery(editor.target).numberbox('isValid');
        }
        else if(editor.type === 'combobox') {
            if(reset) {
                jQuery(editor.target).combobox('reset');
            }
            jQuery(editor.target).combobox(status)
            jQuery(editor.target).combobox('options').required = required;
            jQuery(editor.target).combobox('textbox').validatebox('options').required = required;
            jQuery(editor.target).combobox('isValid');
        }
    }
}

function getWialonToken() {
    var dns = wialonServer;
    // construct login page URL
    var url = dns + "/login.html"; // your site DNS + "/login.html"
    url += "?client_id=" + "Technofarm";	// your application name
    url += "&access_type=" + -1;	// access level"
    url += "&activation_time=" + 0;	// activation time, 0 = immediately; you can pass any UNIX time value
    url += "&duration=" + 0;	// duration, 604800 = one week in seconds
    url += "&flags=" + 0x1;			// options, 0x1 = add username in response
    url += "&lang=" + "bg";
    url += "&redirect_uri=" + dns + "/post_token.html"; // if login succeed - redirect to this page

    // listen message with token from login page window
    window.addEventListener("message", tokenRecieved);

    // finally, open login page in new window
    var top = (jQuery(window).height() - 450) / 2;
    var left = (jQuery(window).width() - 550) / 2;
    window.open(url, "_blank", "width=550, height=450, top=" + top + ", left=" + left);

    function tokenRecieved(e) {
        // get message from login window
        var msg = e.data;
        if (typeof msg == "string" && msg.indexOf("access_token=") >= 0) {
            // get token
            var token = msg.replace("access_token=", "");
            TF.Rpc.Diary.WialonActions.addWialonToken(token)
                .done(function (data) {
                    jQuery('#win-get-wialon-token').window('close');
                    trackToken = token;
                })
                .fail(function (errorObj) {
                    jQuery.messager.alert('Грешка', errorObj.getMessage());
                });

            // remove "message" event listener
            window.removeEventListener("message", tokenRecieved);
        }
    }
}
