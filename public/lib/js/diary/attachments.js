function initAttachmentsGrid()
{
	jQuery('#diary-configs-tables').datagrid({
		width: 500,
		nowrap: true,
		singleSelect: false,
		pageSize: 20,
		fit: true,
		fitColumns: true,
		showFooter: true,
		border: false,
		url: 'index.php?diary-rpc=diary-configs-grid',
		rpcParams: [{request_type: 6}],
		idField: 'id',
		frozenColumns: [[
				{
					field: 'ck',
					checkbox: true
				}
			]],
		columns: [
			[
				{
					field: 'number',
					title: '<b>Рег. номер</b>',
					sortable: false,
					width: 100
				}, {
					field: 'attachment_type',
					title: '<b>Вид</b>',
					sortable: false,
					width: 150
				}, {
					field: 'manufacturer',
					title: '<b>Марка</b>',
					sortable: false,
					width: 150
				}, {
					field: 'model',
					title: '<b>Модел</b>',
					sortable: false,
					width: 80
				}, {
					field: 'description',
					title: '<b>Описание</b>',
					sortable: false,
					width: 250
				}
			]
		],
		rownumbers: true,
		toolbar: [{
				id: 'btn_add_machine_type',
				text: 'Добавяне',
				iconCls: 'icon-add',
				handler: function() {
                    is_edit = false;
					jQuery('#diary-configs-tables').datagrid('clearChecked');
					clearAttachmentEditData();
					initAddEditAttachmentFields();
					jQuery('#win-add-edit-attachment').window('open');
				}
			}, {
				id: 'btn_edit_machine_type',
				text: 'Редактиране',
				iconCls: 'icon-edit',
				handler: function() {
					var getChecked = jQuery('#diary-configs-tables').datagrid('getChecked');

					if (getChecked[0]) {
                        is_edit = true;
						var	requestObj = new Object();
						requestObj = {
							id: getChecked[0].id,
							request_type: 6
						};
						TF.Rpc.Diary.DiaryAuxiliaryItems.itemEditMark(requestObj)
						.done(function (data) {
							initAddEditAttachmentFields();
							setAttachmentEditData(data);
							jQuery('#win-add-edit-attachment').window('open');
						})
						.fail(function (errorObj) {
                            jQuery.messager.alert('Грешка', errorObj.getMessage());
						});
					} else {
						jQuery.messager.alert('Грешка', 'Моля изберете запис, който искате да редактирате!');
					}
				}
			}],
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function initAddEditAttachmentFields()
{
	jQuery('#attachment-type > input').combobox({
		url: 'index.php?diary-rpc=diary-configs-combobox',
		rpcParams: [{request_type: 5}],
		editable: false,
		textField: 'name',
		valueField: 'id',
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function setAttachmentEditData(data) {
	jQuery('#attachment-number > input').val(data.number);
	jQuery('#attachment-type > input').combobox('setValue', data.type_id);
	jQuery('#attachment-manufacturer > input').val(data.manufacturer);
	jQuery('#attachment-model > input').val(data.model);
	jQuery('#attachment-description > textarea').val(data.description);
}

function getAttachmentEditData(data) {
	var returnObj = new Object();
	returnObj = {
		number:jQuery('#attachment-number > input').val(),
		type_id:jQuery('#attachment-type > input').combobox('getValue'),
		manufacturer:jQuery('#attachment-manufacturer > input').val(),
		model:jQuery('#attachment-model > input').val(),
		description:jQuery('#attachment-description > textarea').val(),
	};

	return returnObj;
}

function clearAttachmentEditData(data) {
	jQuery('#attachment-number > input').val('');
	jQuery('#attachment-type > input').val('');
	jQuery('#attachment-manufacturer > input').val('');
	jQuery('#attachment-model > input').val('');
	jQuery('#attachment-description > textarea').val('');
}
