jQuery(function() {
	jQuery('#choose-fuel-diary-farming').combobox({
		url: 'index.php?common-rpc=farming-combobox',
		rpcParams: [{selected: true}],
		valueField: 'id',
		textField: 'name',
		onSelect: function(record) {
			var farming = record.id;
			var year = jQuery('#choose-fuel-diary-year').combobox('getValue');

			initFuelDiaryDatagrid(farming, year);
		},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

	jQuery('#choose-fuel-diary-year').combobox({
		url: 'index.php?common-rpc=farming-year-combobox',
		rpcParams: [{
			selected: 'current',
		}],
		valueField: 'id',
		textField: 'title',
		onSelect: function(record) {
			var farming = jQuery('#choose-fuel-diary-farming').combobox('getValue');
			var year = record.id;

			initFuelDiaryDatagrid(farming, year);
		},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

	jQuery('#print-fuel-diary').bind('click', function() {
		var requestObj = new Object();
		requestObj = {
			farming: jQuery('#choose-fuel-diary-farming').combobox('getValue'),
			year: jQuery('#choose-fuel-diary-year').combobox('getValue')
		};
		TF.Rpc.Diary.DiaryReportsGrid.createFuelDiary(requestObj)
		.done(function (data) {
			jQuery('body').append('<iframe id="printf" name="printf"></iframe>');
			var newWin = window.frames['printf'];
			newWin.document.write('<body onload=window.print()>'+data+'</body>');
			newWin.document.close();
			setTimeout(function () {
				jQuery('#printf').remove();
			}, 1000);
		})
		.fail(function (errorObj) {
            jQuery.messager.alert('Грешка', errorObj.getMessage());
		});
	});
});

function displayFuelDiary()
{
	var farming = jQuery('#choose-fuel-diary-farming').combobox('getValue');
	var year = jQuery('#choose-fuel-diary-year').combobox('getValue');
	jQuery('#win-fuel-diary').window('open');
	initFuelDiaryDatagrid(farming, year);

}

function initFuelDiaryDatagrid(farming, year)
{
	jQuery('#fuel-diary-tables').datagrid({
		nowrap: true,
		singleSelect: true,
		pageSize: 20,
		fit: true,
		fitColumns: true,
		border: false,
		url: 'index.php?diary-rpc=fuel-diary-grid',
		rpcParams: [{
			farming: farming,
			year: year
		}],
		idField: 'id',
		columns: [
			[
				{
					field: 'culture',
					title: '<b>Култура</b>',
					sortable: false,
					width: 150
				},{
					field: 'event_type',
					title: '<b>Вид обработка</b>',
					sortable: false,
					width: 150
				},{
					field: 'machines',
					title: '<b>Техника</b>',
					sortable: false,
					width: 150
				},{
					field: 'rented_machines',
					title: '<b>Наета техника</b>',
					sortable: false,
					width: 150
				},{
					field: 'work_months',
					title: '<b>Период</b>',
					sortable: false,
					width: 150
				},{
					field: 'completed_area',
					title: '<b>Обем извършена дейност</b>',
					sortable: false,
					width: 150
				},{
					field: 'fuel_cost',
					title: '<b>Изразходвано гориво</b>',
					sortable: false,
					width: 150
				},{
					field: 'completed_work',
					title: '<b>Разход</b>',
					sortable: false,
					width: 150
				}
			]
		],
		rownumbers: true,
		pagination: true,
		toolbar: '#fuel-diary-toolbar',
		onBeforeLoad: function() {
			jQuery('#fuel-diary-tables').datagrid('clearChecked');
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}
