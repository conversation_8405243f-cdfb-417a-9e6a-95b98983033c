function initMachinesGrid()
{
    jQuery('#diary-configs-tables').datagrid({
        width: 500,
        nowrap: true,
        singleSelect: false,
        pageSize: 20,
        fit: true,
        fitColumns: true,
        showFooter: true,
        border: false,
        url: 'index.php?diary-rpc=diary-configs-grid',
        rpcParams: [{
            request_type: 4
        }],
        idField: 'id',
        frozenColumns: [[
            {
                field: 'ck',
                checkbox: true
            }
        ]],
        columns: [
            [
                {
                    field: 'number',
                    title: '<b>Рег. номер</b>',
                    sortable: false,
                    width: 100
                }, {
                field: 'machine_type',
                title: '<b>Вид</b>',
                sortable: false,
                width: 150
            }, {
                field: 'manufacturer',
                title: '<b>Марка</b>',
                sortable: false,
                width: 150
            }, {
                field: 'model',
                title: '<b>Модел</b>',
                sortable: false,
                width: 80
            }, {
                field: 'description',
                title: '<b>Описание</b>',
                sortable: false,
                width: 250
            }
            ]
        ],
        rownumbers: true,
        toolbar: [{
            id: 'btn_add_machine_type',
            text: 'Добавяне',
            iconCls: 'icon-add',
            handler: function() {
                is_edit = false;
                jQuery('#diary-configs-tables').datagrid('clearChecked');
                initAddEditMachineFields();
                clearMachineEditData();
                jQuery('#win-add-edit-machine').window('open');

            }
        }, {
            id: 'btn_edit_machine_type',
            text: 'Редактиране',
            iconCls: 'icon-edit',
            handler: function() {
                var getChecked = jQuery('#diary-configs-tables').datagrid('getChecked');

                if (getChecked[0]) {
                    is_edit = true;
                    var	requestObj = new Object();
                    requestObj = {
                        id: getChecked[0].id,
                        request_type: 4
                    };

                    TF.Rpc.Diary.DiaryAuxiliaryItems.itemEditMark(requestObj)
                        .done(function (data) {
                            initAddEditMachineFields();
                            setMachineEditData(data);
                            jQuery('#win-add-edit-machine').window('open');
                        })
                        .fail(function (errorObj) {
                            jQuery.messager.alert('Грешка', errorObj.getMessage());
                        });
                } else {
                    jQuery.messager.alert('Грешка', 'Моля изберете запис, който искате да редактирате!');
                }
            },
        }, {
            id: 'btn_wialon_sync_machines',
            text: 'Синхронизиране',
            iconCls: 'icon-reload',
            handler: function() {
                TF.Rpc.Diary.WialonActions.getWialonMachines()
                    .done(function (data) {
                        jQuery('#diary-configs-tables').datagrid('reload');
                    })
                    .fail(function (errorObj) {
                        if (errorObj.is(TF.Rpc.ExceptionsList.INVALID_OR_EXPIRED_TOKEN)) {
                            jQuery('#win-get-wialon-token').window('open');
                            return;
                        }
                        jQuery.messager.alert('Грешка', errorObj.getMessage());
                    });
            }
        }],
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initAddEditMachineFields()
{
    jQuery('#machine-type > input').combobox({
        url: 'index.php?diary-rpc=diary-configs-combobox',
        rpcParams: [{
            request_type: 3
        }],
        editable: false,
        textField: 'name',
        valueField: 'id',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function setMachineEditData(data) {
    jQuery('#machine-number > input').val(data.number);
    jQuery('#machine-type > input').combobox('setValue', data.type_id);
    jQuery('#machine-manufacturer > input').val(data.manufacturer);
    jQuery('#machine-model > input').val(data.model);
    jQuery('#machine-description > textarea').val(data.description);
}

function getMachineEditData(data) {
    return {
        number:jQuery('#machine-number > input').val(),
        type_id:jQuery('#machine-type > input').combobox('getValue'),
        manufacturer:jQuery('#machine-manufacturer > input').val(),
        model:jQuery('#machine-model > input').val(),
        description:jQuery('#machine-description > textarea').val()
    };
}

function clearMachineEditData(data) {
    jQuery('#machine-number > input').val('');
    jQuery('#machine-type > input').val('');
    jQuery('#machine-manufacturer > input').val('');
    jQuery('#machine-model > input').val('');
    jQuery('#machine-description > textarea').val('');
}
