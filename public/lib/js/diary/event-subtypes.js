function initEventSubtypesGrid()
{
	jQuery('#diary-configs-tables').datagrid({
		width: 500,
		nowrap: true,
		singleSelect: true,
		pageSize: 20,
		fit: true,
		fitColumns: true,
		showFooter: true,
		border: false,
		url: 'index.php?diary-rpc=diary-configs-grid',
		rpcParams: [{
			request_type: 2
		}],
		idField: 'id',
		frozenColumns: [[
				{
					field: 'ck',
					checkbox: true
				}
			]],
		columns: [
			[
				{
					field: 'name',
					title: '<b>Номенклатура</b>',
					sortable: false,
					width: 150
				},{
					field: 'event_type',
					title: '<b>Тип</b>',
					sortable: false,
					width: 150
				}
			]
		],
		rownumbers: true,
		toolbar: [{
				id: 'btn_add_machine_type',
				text: 'Добавяне',
				iconCls: 'icon-add',
				handler: function() {
					//clear old data
                    is_edit = false;
					jQuery('#diary-configs-tables').datagrid('clearChecked');
					initAddEditSubtypeFields();
					clearEventSubtypeFields();
					jQuery('#win-add-edit-event-subtype').window('open');
				}
			}, {
				id: 'btn_edit_machine_type',
				text: 'Редактиране',
				iconCls: 'icon-edit',
				handler: function() {
					var getChecked = jQuery('#diary-configs-tables').datagrid('getChecked');
					var	requestObj = new Object();
					if (getChecked[0]) {
                        is_edit = true;
						requestObj.id = getChecked[0].id;
						TF.Rpc.Diary.DiaryAuxiliaryItems.itemEditMark(requestObj)
						.done(function (data) {
							initAddEditSubtypeFields();
							setEventSubtypeFields(data);
							jQuery('#win-add-edit-event-subtype').window('open');
						})
						.fail(function (errorObj) {
                            jQuery.messager.alert('Грешка', errorObj.getMessage());
						});
					} else {
						jQuery.messager.alert('Грешка', 'Моля изберете запис, който искате да редактирате!');
					}
				}
			}],
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function initAddEditSubtypeFields() {
	jQuery('#event-subtype-type > input').combobox({
		url: 'index.php?diary-rpc=diary-configs-combobox',
		rpcParams: [{
			request_type: 1
		}],
		editable: false,
		textField: 'name',
		valueField: 'id',
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}


function clearEventSubtypeFields() {
	jQuery('#event-subtype-name > input').val('');
	jQuery('#event-subtype-type > input').combobox('reset');
	jQuery('#event-type-is-chemical > input').prop('checked',false);
	jQuery('#event-type-has-produce > input').prop('checked',false);
}

function getEventSubtypeFields() {
    return {
		name: jQuery('#event-subtype-name > input').val(),
		type_id: jQuery('#event-subtype-type > input').combobox('getValue'),
		is_chemical_treatment: jQuery('#event-type-is-chemical > input').prop('checked'),
		has_produce: jQuery('#event-type-has-produce > input').prop('checked')
	};
}

function setEventSubtypeFields(data) {
	jQuery('#event-subtype-name > input').val(data.name);
	jQuery('#event-subtype-type > input').combobox('setValue', data.type_id);
	jQuery('#event-type-is-chemical > input').prop('checked', data.is_chemical_treatment);
	jQuery('#event-type-has-produce > input').prop('checked', data.options.hasProduce);
}
