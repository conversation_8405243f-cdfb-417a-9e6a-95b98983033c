var winDownloadDiaryReport;
var downloadFileDiaryReport;
var cancelDownloadFileDiaryReport;
var _pathFile;
var _fileName;
var report;


function displaySummaryReportByPerformer(){
    initSummaryReportByPerformerFields();
    initDetailedReportByPerformerFields();
    initSummaryReportByPerformerGrid();
    jQuery('#win-diary-reports').window('open');
}

function createDownloadVariables() {
    winDownloadDiaryReport = jQuery('#win-download').window({
        onClose: onDownloadDiaryReportWindowClose
    });

    downloadFileDiaryReport = jQuery('#btn-download-file');
    cancelDownloadFileDiaryReport = jQuery('#btn-download-file-close');
}

function displayDetailedReportByPerformer(performer_id, isExpenses){
    jQuery('#win-diary-detailed-report').window('open');

    initDetailedReportByPerformerFields();
    initDetailedReportByPerformerGrid('0', isExpenses);

    jQuery('#search-detailed-report-performer').combobox({
		url: 'index.php?diary-rpc=diary-configs-combobox',
        rpcParams: [{
            request_type: 9
        }],
		editable: true,
		textField: 'name',
		valueField: 'id',
        groupField: 'group',
        filter: function(q, row){
			var opts = jQuery(this).combobox('options');
			var text = row[opts.textField].toLowerCase();
			var find = q.toLowerCase();
			if(text.indexOf(find) != -1)
			{
				return true;
			}
		},
        onLoadSuccess: function(){
            var select;
            if(performer_id)
            {
                select = performer_id;
            }
            else
            {
                var data = jQuery(this).combobox('getData');

                if(data.length > 0) {
                    select = data[0].id;
                }
            }

            if(select) {
                jQuery(this).combobox('select', select);
                return;
            }

            TF.Loading.end();
        },
        onSelect: function(record){
            initDetailedReportByPerformerGrid(record.id, isExpenses);

            var title = 'Подробна справка по механизатор/служител - ';
            if(isExpenses) {
                title = 'Подробна справка за заработки по механизатор/служител - ';
            }
            jQuery('#win-diary-detailed-report').window('setTitle', title + record.name);
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

}

function initSummaryReportByPerformerGrid() {
    jQuery('#diary-report-tables').datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 10,
        fit: true,
        fitColumns: true,
        showFooter: true,
        singleSelect: true,
        url: 'index.php?diary-rpc=diary-reports-grid',
        rpcMethod: 'summaryByPerformer',
        rpcParams: [{}],
        idField: 'id',
        sortName: 'id',
        border: true,
        sortOrder: 'asc',
        columns: [[{
                    field: 'name',
                    title: '<b>Механизатор/служител</b>',
                    sortable: true,
                    width: 200,
                }, {
                    field: 'moto_hours',
                    title: '<b>Изработени<br/>моточасове<br/>за периода</b>',
                    sortable: true,
                    width: 150,
                    align: 'center'
                }, {
                    field: 'completed_area',
                    title: '<b>Обща обработена<br/>площ (дка)</b>',
                    sortable: true,
                    width: 150,
                    align: 'center'
                }, {
                    field: 'zp_area',
                    title: '<b>Обща площ на<br/>парцелите (дка)</b>',
                    sortable: true,
                    width: 150,
                    align: 'center'
                }]],
        pagination: true,
        rownumbers: true,
        toolbar: '#summary-report-performer-toolbar',
        onLoadSuccess: function() {
            TF.Loading.end();
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initSummaryReportByPerformerFields(){
    jQuery('#search-summary-report-performer').combobox({
		url: 'index.php?diary-rpc=diary-configs-combobox',
        rpcParams: [{
            request_type: 9
        }],
		editable: true,
		textField: 'name',
		valueField: 'id',
        groupField: 'group',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

    jQuery('#search-summary-report-farming').combobox({
		editable: true,
		url: 'index.php?common-rpc=farming-combobox',
		valueField: 'id',
		textField: 'name',
		multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

    jQuery('#search-summary-report-ekate').combobox({
		editable: true,
		url: 'index.php?common-rpc=zp-ekate-combobox',
		valueField: 'ekate',
		textField: 'text',
		multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

    jQuery('#search-summary-report-date-from').datebox();
	jQuery('#search-summary-report-date-to').datebox();

    jQuery('#search-summary-report-event-type').combobox({
		url: 'index.php?diary-rpc=diary-configs-combobox',
        rpcParams: [{
            request_type: 1
        }],
		editable: true,
		textField: 'name',
		valueField: 'id',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onChange:function(){
            var data = jQuery(this).combobox('getValues');
            summaryEventKind.combobox({rpcParams: [{request_type: 2, event_type: data}]})
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

    var summaryEventKind = jQuery('#search-summary-report-event-kind');
    summaryEventKind.combobox({
        url: 'index.php?diary-rpc=diary-configs-combobox',
        rpcParams: [{
            request_type: 2
        }],
        editable: true,
        textField: 'name',
        valueField: 'id',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-summary-report-machine').combobox({
		url: 'index.php?diary-rpc=diary-configs-combobox',
        rpcParams: [{
            request_type: 4
        }],
		editable: true,
		textField: 'name',
		valueField: 'id',
        groupField: 'group',
		multiple: true,
        onHidePanel: onHidePanelMultiSelect ,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

	jQuery('#search-summary-report-attachment').combobox({
		url: 'index.php?diary-rpc=diary-configs-combobox',
        rpcParams: [{
            request_type: 6
        }],
		editable: true,
		textField: 'name',
		valueField: 'id',
		multiple: true,
        onHidePanel: onHidePanelMultiSelect ,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function filterSummaryReportByPerformer(){
    jQuery('#diary-report-tables').datagrid({
        rpcParams: [{
            performer: jQuery('#search-summary-report-performer').combobox('getValues'),
            farming: jQuery('#search-summary-report-farming').combobox('getValues'),
            zp_ekate: jQuery('#search-summary-report-ekate').combobox('getValues'),
            date_from: jQuery('#search-summary-report-date-from').datebox('getValue'),
            date_to: jQuery('#search-summary-report-date-to').datebox('getValue'),
            event_type: jQuery('#search-summary-report-event-type').combobox('getValues'),
            event_kind: jQuery('#search-summary-report-event-kind').combobox('getValues'),
            machine: jQuery('#search-summary-report-machine').combobox('getValues'),
            attachment: jQuery('#search-summary-report-attachment').combobox('getValues'),
            zp_name: jQuery('#search-summary-report-zp-name').val()
        }]
    });
}

function clearSummaryReportByPerformerFilter(){
    jQuery('#search-summary-report-performer').combobox('clear');
    jQuery('#search-summary-report-farming').combobox('clear');
    jQuery('#search-summary-report-ekate').combobox('clear');
    jQuery('#search-summary-report-date-from').datebox('clear');
    jQuery('#search-summary-report-date-to').datebox('clear');
    jQuery('#search-summary-report-event-type').combobox('clear');
    jQuery('#search-summary-report-event-kind').combobox('clear');
    jQuery('#search-summary-report-machine').combobox('clear');
    jQuery('#search-summary-report-attachment').combobox('clear');
    jQuery('#search-summary-report-zp-name').val('');

    jQuery('#diary-report-tables').datagrid({
        rpcParams: [{

        }]
    });
}

function getSummaryReportByPerformerFilterText(){
    var obj = {};
    obj.performer = jQuery('#search-summary-report-performer').combobox('getText') || '-';
    obj.farming = jQuery('#search-summary-report-farming').combobox('getText') || '-';
    obj.ekate = jQuery('#search-summary-report-ekate').combobox('getText') || '-';
    obj.dateFrom = jQuery('#search-summary-report-date-from').datebox('getText') || '-';
    obj.dateTo = jQuery('#search-summary-report-date-to').datebox('getText') || '-';
    obj.eventType = jQuery('#search-summary-report-event-type').combobox('getText') || '-';
    obj.machine = jQuery('#search-summary-report-machine').combobox('getText') || '-';
    obj.attachment = jQuery('#search-summary-report-attachment').combobox('getText') || '-';
    obj.zpName = jQuery('#search-summary-report-zp-name').val() || '-';

    return obj;
}

function initDetailedReportByPerformerGrid(performer_id, isExpenses) {
    jQuery('#diary-detailed-report-table').datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 10,
        fit: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?diary-rpc=diary-reports-grid',
        rpcMethod: 'detailedByPerformer',
        rpcParams: [{
            performer_id: performer_id
        }],
        idField: 'id',
        sortName: 'id',
        border: true,
        sortOrder: 'asc',
        columns: [[{
                    field: 'date',
                    title: '<b>Дата</b>',
                    sortable: true
                }, {
                    field: 'machine',
                    title: '<b>Машина</b>',
                    sortable: true
                }, {
                    field: 'attachment',
                    title: '<b>Прикачен инвентар</b>',
                    sortable: true
                }, {
                    field: 'zp_name',
                    title: '<b>Парцел</b>',
                    sortable: true
                }, {
                    field: 'zp_area',
                    title: '<b>Обща площ на<br/>парцела (дка)</b>',
                    sortable: true,
                    align: 'center'
                }, {
                    field: 'farming',
                    title: '<b>Стопанство</b>',
                    sortable: true
                }, {
                    field: 'year',
                    title: '<b>Година</b>',
                    sortable: true
                }, {
                    field: 'event_type',
                    title: '<b>Тип обработка</b>',
                    sortable: true
                }, {
                    field: 'moto_hours',
                    title: '<b>Моточасове</b>',
                    sortable: true
                }, {
                    field: 'completed_area',
                    title: '<b>Обработена<br/>площ (дка)</b>',
                    sortable: true,
                    align: 'center'
                }, {
                    field: 'price',
                    title: '<b>Разход в лв</b>'
                }]],
        pagination: true,
        rownumbers: true,
        toolbar: '#diary-detailed-report-toolbar',
        onLoadSuccess: function () {
            jQuery('#plots-report-tables').datagrid('autoSizeColumn');
            TF.Loading.end();
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function printDetailedReport(currentPage){
    var data = jQuery('#diary-detailed-report-table').datagrid('getData');
    if(data.rows.length === 0) {
        return jQuery.messager.alert('Грешка', 'Не може да отпечатате празна справка.');
    }

    var gridOptions = jQuery('#diary-detailed-report-table').datagrid('options');

    var params = jQuery.extend({}, gridOptions.rpcParams);
    var page     = (currentPage) ? gridOptions.pageNumber : null;
    var pageSize = (currentPage) ? gridOptions.pageSize : null;
    var sort = gridOptions.sortName;
    var order = gridOptions.sortOrder;

    TF.Rpc.Diary.DiaryReportsGrid.detailedByPerformer(params[0],page, pageSize, sort, order)
    .done(function (data) {
        var gridData = data.rows;
        var footerData = data.footer;
        var filter = getDetailedReportByPerformerFilterText();

        var html = '<style>@page{size: landscape;}body,table{font-family: Arial;font-size: 10px;}</style>' +
        '<h2 align="center">Подробна справка по механизатор/служител - ' + filter.performer + '</h2>';

        html += '<b>Филтър:</b><br/>';
        html += 'Стопанство: <b>' + filter.farming + '</b><br/>';
        html += 'Землище: <b>' + filter.ekate + '</b><br/>';
        html += 'Период от ÷ до: <b>' + filter.dateFrom + '</b> ÷ <b>' + filter.dateTo + '</b><br/>';
        html += 'Вид обработка: <b>' + filter.eventType + '</b><br/>';
        html += 'Машина: <b>' + filter.machine + '</b><br/>';
        html += 'Прикачен инвентар: <b>' + filter.attachment + '</b><br/>';
        html += 'Парцел: <b>' + filter.zpName + '</b><br/><br/>';

        var header = {};
        var columns = gridOptions.columns[0];
        for(var i=0; i<columns.length; i++)
        {
            header[columns[i].field] = columns[i].title;
        }

        var rows = gridData;
        rows.push(footerData[1]);

        html += Templates.table(header, rows);

        jQuery('body').append('<iframe id="printf" name="printf"></iframe>');
        var newWin = window.frames['printf'];
        newWin.document.write('<body onload=window.print()>'+html+'</body>');
        newWin.document.close();
        setTimeout(function () {
            jQuery('#printf').remove();
        }, 1000);

    })
    .fail(function (errorObj) {
        jQuery.messager.alert('Грешка', errorObj.getMessage());
    });

    return false;
}

function exportXLSDetailedReport(exportCurrentPage) {
    var gridOptions = jQuery('#diary-detailed-report-table').datagrid('options');

    var page = null;
    var pageSize = null;
    if(exportCurrentPage === true)
    {
        page = gridOptions.pageNumber;
        pageSize = gridOptions.pageSize;
    }
    var params = jQuery.extend({}, gridOptions.rpcParams);
    var sort = gridOptions.sortName;
    var order = gridOptions.sortOrder;
    params[0].filter = getDetailedReportByPerformerFilterText();
    createDownloadVariables();

    TF.Rpc.Diary.DiaryReportsGrid.exportPerformerDetailedReportXLS(params[0],page,pageSize,sort,order)
    .done(function (data) {
        winDownloadDiaryReport.window('open');
        var path = data.path;
        _fileName = data.name;
        _pathFile = path;
        downloadFileDiaryReport.attr("href", path);
    })
    .fail(function (errorObj) {
        jQuery.messager.alert('Грешка', errorObj.getMessage());
    });
}

function initDetailedReportByPerformerFields(){
    jQuery('#search-detailed-report-farming').combobox({
		url: 'index.php?common-rpc=farming-combobox',
		valueField: 'id',
		textField: 'name',
		multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        editable: true,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

    jQuery('#search-detailed-report-ekate').combobox({
		editable: true,
		url: 'index.php?common-rpc=zp-ekate-combobox',
		valueField: 'ekate',
		textField: 'text',
		multiple: true,
        onHidePanel: onHidePanelMultiSelect ,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

    jQuery('#search-detailed-report-date-from').datebox();
	jQuery('#search-detailed-report-date-to').datebox();

    jQuery('#search-detailed-report-event-type').combobox({
		url: 'index.php?diary-rpc=diary-configs-combobox',
        rpcParams: [{
            request_type: 1
        }],
		editable: true,
		textField: 'name',
		valueField: 'id',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onChange:function(){
            var data = jQuery(this).combobox('getValues');
            detailedEventKind.combobox({rpcParams: [{request_type: 2, event_type: data}]})
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

    var detailedEventKind = jQuery('#search-detailed-report-event-kind');
    detailedEventKind.combobox({
        url: 'index.php?diary-rpc=diary-configs-combobox',
        rpcParams: [{
            request_type: 2
        }],
        editable: true,
        textField: 'name',
        valueField: 'id',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-detailed-report-machine').combobox({
		url: 'index.php?diary-rpc=diary-configs-combobox',
        rpcParams: [{
            request_type: 4
        }],
		editable: true,
		textField: 'name',
		valueField: 'id',
		groupField: 'group',
		multiple: true,
        onHidePanel: onHidePanelMultiSelect ,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

	jQuery('#search-detailed-report-attachment').combobox({
		url: 'index.php?diary-rpc=diary-configs-combobox',
        rpcParams: [{
            request_type: 6
        }],
		editable: true,
		textField: 'name',
		valueField: 'id',
		multiple: true,
        onHidePanel: onHidePanelMultiSelect ,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function filterDetailedReportByPerformer(){
    jQuery('#diary-detailed-report-table').datagrid({
        rpcParams: [{
            performer_id: jQuery('#search-detailed-report-performer').combobox('getValue'),
            farming: jQuery('#search-detailed-report-farming').combobox('getValues'),
            zp_ekate: jQuery('#search-detailed-report-ekate').combobox('getValues'),
            date_from: jQuery('#search-detailed-report-date-from').datebox('getValue'),
            date_to: jQuery('#search-detailed-report-date-to').datebox('getValue'),
            event_type: jQuery('#search-detailed-report-event-type').combobox('getValues'),
            event_kind: jQuery('#search-detailed-report-event-kind').combobox('getValues'),
            machine: jQuery('#search-detailed-report-machine').combobox('getValues'),
            attachment: jQuery('#search-detailed-report-attachment').combobox('getValues'),
            zp_name: jQuery('#search-detailed-report-zp-name').val()
        }]
    });
}

function clearDetailedReportByPerformerFilter(){
    jQuery('#search-detailed-report-farming').combobox('clear');
    jQuery('#search-detailed-report-ekate').combobox('clear');
    jQuery('#search-detailed-report-date-from').datebox('clear');
    jQuery('#search-detailed-report-date-to').datebox('clear');
    jQuery('#search-detailed-report-event-type').combobox('clear');
    jQuery('#search-detailed-report-event-kind').combobox('clear');
    jQuery('#search-detailed-report-machine').combobox('clear');
    jQuery('#search-detailed-report-attachment').combobox('clear');
    jQuery('#search-detailed-report-zp-name').val('');

    jQuery('#diary-detailed-report-table').datagrid({
        rpcParams: [{
            performer_id: jQuery('#search-detailed-report-performer').combobox('getValue')
        }]
    })
}

function getDetailedReportByPerformerFilterText(){
    var obj = {};
    obj.performer = jQuery('#search-detailed-report-performer').combobox('getText');
    obj.farming = jQuery('#search-detailed-report-farming').combobox('getText') || '-';
    obj.ekate = jQuery('#search-detailed-report-ekate').combobox('getText') || '-';
    obj.dateFrom = jQuery('#search-detailed-report-date-from').datebox('getText') || '-';
    obj.dateTo = jQuery('#search-detailed-report-date-to').datebox('getText') || '-';
    obj.eventType = jQuery('#search-detailed-report-event-type').combobox('getText') || '-';
    obj.machine = jQuery('#search-detailed-report-machine').combobox('getText') || '-';
    obj.attachment = jQuery('#search-detailed-report-attachment').combobox('getText') || '-';
    obj.zpName = jQuery('#search-detailed-report-zp-name').val() || '-';

    return obj;
}

function displayDetailedReportByDate(group_by, group_by_text){
    var performer_id = jQuery('#search-detailed-report-performer').combobox('getValue');

    jQuery('#detailed-reports-by-grid').datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 10,
        fit: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?diary-rpc=diary-reports-grid',
        rpcMethod: 'detailedBy',
        rpcParams: [{
            performer_id: performer_id,
            group_by: group_by,
            farming: jQuery('#search-detailed-report-farming').combobox('getValues'),
            zp_ekate: jQuery('#search-detailed-report-ekate').combobox('getValues'),
            date_from: jQuery('#search-detailed-report-date-from').datebox('getValue'),
            date_to: jQuery('#search-detailed-report-date-to').datebox('getValue'),
            event_type: jQuery('#search-detailed-report-event-type').combobox('getValues'),
            event_kind: jQuery('#search-detailed-report-event-kind').combobox('getValues'),
            machine: jQuery('#search-detailed-report-machine').combobox('getValues'),
            attachment: jQuery('#search-detailed-report-attachment').combobox('getValues'),
            zp_name: jQuery('#search-detailed-report-zp-name').val()
        }],
        sortName: group_by,
        border: false,
        sortOrder: 'asc',
        columns: [[{
                    field: group_by,
                    title: '<b>' + group_by_text + '</b>',
                    sortable: true,
                    width: 200
                }, {
                    field: 'moto_hours',
                    title: '<b>Моточасове</b>',
                    sortable: true,
                    width: 100
                }, {
                    field: 'completed_area',
                    title: '<b>Обработена<br/>площ (дка)</b>',
                    sortable: true,
                    align: 'center',
                    width: 100
                }]],
        pagination: true,
        rownumbers: true,
        toolbar: '#detailed-reports-by-toolbar',
        onBeforeLoad: function() {
            jQuery('#detailed-reports-by-grid').datagrid('clearChecked');
        },
        onLoadSuccess: function() {
            TF.Loading.end();
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    var performer = jQuery('#search-detailed-report-performer').combobox('getText');
    jQuery('#win-diary-detailed-reports-by').window('setTitle', 'Индивидулана справка за ' + performer + ' по ' + group_by_text.toLowerCase());
    jQuery('#win-diary-detailed-reports-by').window('open');
}

function displayDiaryPlotsByProductReport(title, product_types) {
    var columns = [
        {
            field: 'product_name',
            title: '<b>Продукт</b>',
            sortable: true,
            width: 100,
            align: 'center'
        }
    ];

    if(product_types.includes("seeds")){
        columns.push(
            {
                field: 'sort',
                title: '<b>Сорт</b>',
                sortable: true,
                width: 100,
                align: 'center'
            }
        );
    }

    columns = jQuery.merge(columns, [{
        field: 'product_dose',
        title: '<b>Разходна норма</b>',
        sortable: true,
        width: 100,
        align: 'center'
    },
        {
            field: 'product_measure',
            title: '<b>Мерна единица</b>',
            sortable: true,
            width: 100,
            align: 'center'
        },
        {
            field: 'product_consumed',
            title: '<b>Употребено количество</b>',
            sortable: true,
            width: 100,
            align: 'center'
        },
        {
            field: 'single_price',
            title: '<b>Единична стойност</b>',
            sortable: true,
            width: 100,
            align: 'center'
        },
        {
            field: 'event_price',
            title: '<b>Обща стойност</b>',
            sortable: true,
            width: 100,
            align: 'center'
        }
    ]);

    diaryPlotsReport('getReportByProduct', title, columns, {'product_type': product_types});

}

function displayDiaryPlotsByFuelReport() {
    var columns = [
        {
            field: 'fuel_quantity',
            title: '<b>Общ отчетен разход (л)</b>',
            sortable: true,
            width: 100,
            align: 'center'
        },
        {
            field: 'single_price',
            title: '<b>Единична стойност на гориво(лв/л)</b>',
            sortable: true,
            width: 100,
            align: 'center'
        },
        {
            field: 'event_price',
            title: '<b>Обща стойност на горивото (пари)</b>',
            sortable: true,
            width: 100,
            align: 'center'
        }
    ];

    diaryPlotsReport('getReportByFuel', 'Справка по гориво', columns);
}

function displayDiaryPlotsReport(){
    var columns = [
        {
            field: 'event_price',
            title: '<b>Стойност (пари)</b>',
            sortable: true,
            width: 100,
            align: 'center'
        }
    ];

    diaryPlotsReport('getReportByPlot', 'Справка по парцел', columns);
}


function displayDiaryPlotsReportByProduce(){
    var columns = [
        {
            field: 'produce',
            title: '<b>Добив</b>',
            sortable: true,
            width: 100,
            align: 'center'
        },
        {
            field: 'produce_per_dka',
            title: '<b>Добив от дка</b>',
            sortable: true,
            width: 100,
            align: 'center'
        }
    ];

    diaryPlotsReport('getReportByProduce', 'Справка по добив', columns);
}

function diaryPlotsReport(reportName, gridTitle, additionalColumns, rpcParams = {}) {
    initPlotsReportFields();

    var grid = jQuery('#diary-plots-report-tables');
    report = reportName;
    manageReportFilters(rpcParams, reportName);
    var columns = [{
        field: 'zp_name',
        title: '<b>Име на парцел</b>',
        sortable: true,
        width: 100
    },
        {
            field: 'isak_prc_uin',
            title: '<b>ИСАК номер</b>',
            sortable: true,
            width: 100
        }, {
            field: 'complete_date',
            title: '<b>Дата на приключване</b>',
            sortable: true,
            width: 100,
            align: 'center'
        }, {
            field: 'performer',
            title: '<b>Механизатор</b>',
            sortable: true,
            width: 100,
            align: 'center'
        }, {
            field: 'machine',
            title: '<b>Машина</b>',
            sortable: true,
            width: 100,
            align: 'center'
        }, {
            field: 'attachment',
            title: '<b>Прикачен инвентар</b>',
            sortable: true,
            width: 100,
            align: 'center'
        }, {
            field: 'zp_area',
            title: '<b>Обща площ (дка)</b>',
            sortable: true,
            width: 100,
            align: 'center'
        }, {
            field: 'farming',
            title: '<b>Стопанство</b>',
            sortable: true,
            width: 100,
            align: 'center'
        }, {
            field: 'year',
            title: '<b>Година</b>',
            sortable: true,
            width: 100,
            align: 'center'
        },{
            field: 'zp_culture',
            title: '<b>Култура</b>',
            sortable: false,
            width: 100,
            align: 'center'
        }];

    if(['getReportByProduce'].includes(reportName)) {
        columns.push({
            field: 'sort',
            title: '<b>Сорт</b>',
            sortable: false,
            width: 100,
            align: 'center'
        });
    }

    columns = jQuery.merge(columns, [{
            field: 'type',
            title: '<b>Тип обработка</b>',
            sortable: true,
            width: 100,
            align: 'center'
        }, {
            field: 'moto_hours',
            title: '<b>Моточасове</b>',
            sortable: true,
            width: 100,
            align: 'center'
        }, {
            field: 'completed_area',
            title: '<b>Обработена площ (дка)</b>',
            sortable: true,
            width: 100,
            align: 'center'
        }]);

    //Add additional columns
    if(additionalColumns.length > 0) {
        for (var column of additionalColumns) {
            columns.push(column);
        }
    }

    var filters = {...rpcParams, ...getFilters()};
    grid.datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 10,
        fit: true,
        fitColumns: false,
        showFooter: true,
        singleSelect: true,
        url: 'index.php?diary-rpc=diary-reports-grid',
        rpcMethod: reportName,
        rpcParams: [filters],
        idField: 'id',
        sortName: 'id',
        border: true,
        sortOrder: 'asc',
        columns: [columns],
        pagination: true,
        rownumbers: true,
        toolbar: '#diary_reports_toolbar',
        onLoadSuccess: function () {
            jQuery('#plots-report-tables').datagrid('autoSizeColumn');
            TF.Loading.end();
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#win-diary-plots-report').window('setTitle', gridTitle);
    jQuery('#win-diary-plots-report').window('open');

    jQuery('#print-report').unbind('click').bind('click', function() {
        printDiaryReport(null, gridTitle);
    });
}

function manageReportFilters(params = {}, reportName = ''){
    if(params.product_type && (params.product_type.includes('seeds') ||
        params.product_type.includes('fertilizer') ||
        params.product_type.includes('chemical_treatment') ||
        params.product_type.includes('other'))){
        jQuery('#product-name-field').show();
    } else {
        jQuery('#product-name-field').hide();
    }

    jQuery('#search-plots-report-sort').textbox();

    if(['getReportByProduct'].includes(reportName)){
        jQuery('#product-name-field').show();
    } else {
        jQuery('#product-name-field').hide();
    }

    if(['getReportByProduce'].includes(reportName)) {
        jQuery('#sort-name-field').show();
    } else {
        jQuery('#sort-name-field').hide();
    }

    var searchPlotsReportProductName = jQuery('#search-plots-report-product-name');
    searchPlotsReportProductName.combobox({
        url: 'index.php?diary-rpc=diary-configs-combobox',
        rpcParams: [{
            request_type: 7,
            product_type: params.product_type ? params.product_type : ''
        }],
        hidden:true,
        editable: true,
        textField: 'name',
        valueField: 'id',
        onHidePanel: onHidePanelMultiSelect ,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}
var farming_years;
function initPlotsReportFields(){
    jQuery('#search-plots-report-zp-name').val('');
    jQuery('#search-plots-report-isak-prc-uin').val('');

    var searchPlotsReportFarmingYear = jQuery('#search-plots-report-farming-year');
    if(searchPlotsReportFarmingYear.data().hasOwnProperty('combobox')){
        searchPlotsReportFarmingYear.combobox('clear').combobox('loadData', farming_years);
    } else {
        searchPlotsReportFarmingYear.combobox({
            editable: true,
            url: 'index.php?common-rpc=farming-year-combobox',
            valueField: 'id',
            textField: 'farming_year',
            multiple: true,
            onHidePanel: onHidePanelMultiSelect,
            onLoadSuccess:function(data){
                farming_years = data;
            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }

    var searchPlotsReportPerformer = jQuery('#search-plots-report-performer');
    if(searchPlotsReportPerformer.data().hasOwnProperty('combobox')){
        searchPlotsReportPerformer.combobox('clear');
    } else {
        searchPlotsReportPerformer.combobox({
            url: 'index.php?diary-rpc=diary-configs-combobox',
            rpcParams: [{
                request_type: 9
            }],
            editable: true,
            textField: 'name',
            valueField: 'id',
            groupField: 'group',
            multiple: true,
            onHidePanel: onHidePanelMultiSelect,
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }

    var searchPlotsReportFarming = jQuery('#search-plots-report-farming');
    if(searchPlotsReportFarming.data().hasOwnProperty('combobox')){
        searchPlotsReportFarming.combobox('clear');
    } else {
        searchPlotsReportFarming.combobox({
            editable: true,
            url: 'index.php?common-rpc=farming-combobox',
            valueField: 'id',
            textField: 'name',
            multiple: true,
            onHidePanel: onHidePanelMultiSelect,
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }

    var searchPlotsReportCrop = jQuery('#search-plots-report-crop');
    if(searchPlotsReportCrop.data().hasOwnProperty('combobox')){
        searchPlotsReportCrop.combobox('clear');
    } else {
        searchPlotsReportCrop.combobox({
            editable: true,
            url: 'index.php?common-rpc=culture-combobox',
            valueField: 'id',
            textField: 'name',
            multiple: true,
            onHidePanel: onHidePanelMultiSelect,
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }

    var searchPlotsReportEkate = jQuery('#search-plots-report-ekate');
    if(searchPlotsReportEkate.data().hasOwnProperty('combobox')){
        searchPlotsReportEkate.combobox('clear');
    } else {
        jQuery('#search-plots-report-ekate').combobox({
            editable: true,
            url: 'index.php?common-rpc=zp-ekate-combobox',
            valueField: 'ekate',
            textField: 'text',
            multiple: true,
            onHidePanel: onHidePanelMultiSelect,
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }


    jQuery('#search-plots-report-date-from').datebox();
    jQuery('#search-plots-report-date-to').datebox();

    var searchPlotsReportEventType = jQuery('#search-plots-report-event-type');
    if(searchPlotsReportEventType.data().hasOwnProperty('combobox')) {
        searchPlotsReportEventType.combobox('clear');
    } else {
        searchPlotsReportEventType.combobox({
            url: 'index.php?diary-rpc=diary-configs-combobox',
            rpcParams: [{
                request_type: 1
            }],
            editable: true,
            textField: 'name',
            valueField: 'id',
            multiple: true,
            onHidePanel: onHidePanelMultiSelect,
            onChange:function(){
                var data = jQuery(this).combobox('getValues');
                plotEventKind.combobox({rpcParams: [{request_type: 2, event_type: data}]})
            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }


    var plotEventKind = jQuery('#search-plots-report-event-kind');
    if(plotEventKind.data().hasOwnProperty('combobox')) {
        plotEventKind.combobox('clear');
    } else {
        plotEventKind.combobox({
            url: 'index.php?diary-rpc=diary-configs-combobox',
            rpcParams: [{
                request_type: 2
            }],
            editable: true,
            textField: 'name',
            valueField: 'id',
            multiple: true,
            onHidePanel: onHidePanelMultiSelect,
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }

    var searchPlotsReportMachine = jQuery('#search-plots-report-machine');
    if(searchPlotsReportMachine.data().hasOwnProperty('combobox')){
        searchPlotsReportMachine.combobox('clear');
    } else {
        jQuery('#search-plots-report-machine').combobox({
            url: 'index.php?diary-rpc=diary-configs-combobox',
            rpcParams: [{
                request_type: 4
            }],
            editable: true,
            textField: 'name',
            valueField: 'id',
            groupField: 'group',
            multiple: true,
            onHidePanel: onHidePanelMultiSelect,
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }

    var searchPlotsReportAttachment = jQuery('#search-plots-report-attachment');
    if(searchPlotsReportAttachment.data().hasOwnProperty('combobox')) {
        searchPlotsReportAttachment.combobox('clear');
    } else {
        searchPlotsReportAttachment.combobox({
            url: 'index.php?diary-rpc=diary-configs-combobox',
            rpcParams: [{
                request_type: 6
            }],
            editable: true,
            textField: 'name',
            valueField: 'id',
            multiple: true,
            onHidePanel: onHidePanelMultiSelect ,
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }
}

function filterPlotsReport() {
    var options = jQuery('#diary-plots-report-tables').datagrid('options');
    var filters = getFilters();
    filters.product_type = options.rpcParams[0].product_type;

    jQuery('#diary-plots-report-tables').datagrid({
        rpcParams: [filters]
    });
}

function getFilters() {
    return {
        performer: jQuery('#search-plots-report-performer').combobox('getValues'),
        farming: jQuery('#search-plots-report-farming').combobox('getValues'),
        farming_year: jQuery('#search-plots-report-farming-year').combobox('getValues'),
        zp_ekate: jQuery('#search-plots-report-ekate').combobox('getValues'),
        date_from: jQuery('#search-plots-report-date-from').datebox('getValue'),
        date_to: jQuery('#search-plots-report-date-to').datebox('getValue'),
        event_type: jQuery('#search-plots-report-event-type').combobox('getValues'),
        event_kind: jQuery('#search-plots-report-event-kind').combobox('getValues'),
        machine: jQuery('#search-plots-report-machine').combobox('getValues'),
        attachment: jQuery('#search-plots-report-attachment').combobox('getValues'),
        zp_name: jQuery('#search-plots-report-zp-name').val(),
        isak_prc_uin: jQuery('#search-plots-report-isak-prc-uin').val(),
        zp_crops: jQuery('#search-plots-report-crop').combobox('getValues'),
        product_id: jQuery('#search-plots-report-product-name').combobox('getValue'),
        sort: jQuery('#search-plots-report-sort').textbox('getValue'),
    };
}

function clearPlotsReportFilter(){
    jQuery('#search-plots-report-performer').combobox('clear');
    jQuery('#search-plots-report-farming').combobox('clear');
    jQuery('#search-plots-report-farming-year').combobox('clear');
    jQuery('#search-plots-report-ekate').combobox('clear');
    jQuery('#search-plots-report-date-from').datebox('clear');
    jQuery('#search-plots-report-date-to').datebox('clear');
    jQuery('#search-plots-report-event-type').combobox('clear');
    jQuery('#search-plots-report-event-kind').combobox('clear');
    jQuery('#search-plots-report-machine').combobox('clear');
    jQuery('#search-plots-report-attachment').combobox('clear');
    jQuery('#search-plots-report-zp-name').val('');
    jQuery('#search-plots-report-isak-prc-uin').val('');
    jQuery('#search-plots-report-crop').combobox('clear');
    jQuery('#search-plots-report-product-name').combobox('clear');
    jQuery('#search-plots-report-sort').textbox('clear');

    var options = jQuery('#diary-plots-report-tables').datagrid('options');
    jQuery('#diary-plots-report-tables').datagrid({
        rpcParams: [{
            product_type: options.rpcParams[0].product_type
        }]
    });
}

function getPlotsReportFilterText(){
    var obj = {};
    obj.performer = jQuery('#search-plots-report-performer').combobox('getText') || '-';
    obj.farming = jQuery('#search-plots-report-farming').combobox('getText') || '-';
    obj.farmingYear = jQuery('#search-plots-report-farming-year').combobox('getText') || '-';
    obj.ekate = jQuery('#search-plots-report-ekate').combobox('getText') || '-';
    obj.dateFrom = jQuery('#search-plots-report-date-from').datebox('getText') || '-';
    obj.dateTo = jQuery('#search-plots-report-date-to').datebox('getText') || '-';
    obj.eventType = jQuery('#search-plots-report-event-type').combobox('getText') || '-';
    obj.machine = jQuery('#search-plots-report-machine').combobox('getText') || '-';
    obj.attachment = jQuery('#search-plots-report-attachment').combobox('getText') || '-';
    obj.zpName = jQuery('#search-plots-report-zp-name').val() || '-';
    obj.isakPrcUin = jQuery('#search-plots-report-isak-prc-uin').val() || '-';
    obj.zpCrops = jQuery('#search-plots-report-crop').combobox('getText') || '-';
    obj.sort = jQuery('#search-plots-report-sort').textbox('getValue') || '-';
    obj.product_name = jQuery('#search-plots-report-product-name').combobox('getText') || '-';

    return obj;
}

function onDownloadDiaryReportWindowClose() {
   return;
}

function printDiaryReport(currentPage, reportTitle) {
    var data = jQuery('#diary-plots-report-tables').datagrid('getData');
    if(data.rows.length === 0) {
        jQuery.messager.alert('Грешка', 'Не може да отпечатате празна справка.');
        return false;
    }

    const gridOptions = jQuery('#diary-plots-report-tables').datagrid('options');
    const reportMethod = gridOptions.rpcMethod ?? 'getReportByPlot';
    const columns = gridOptions.columns[0];

    var params = jQuery.extend({}, gridOptions.rpcParams);
    var page = (currentPage) ? gridOptions.pageNumber : null;
    var pageSize = (currentPage) ? gridOptions.pageSize : null;
    var sort = gridOptions.sortName;
    var order = gridOptions.sortOrder;

    TF.Rpc.Diary.DiaryReportsGrid.call(reportMethod, params[0],page, pageSize, sort, order)
    .done(function (data) {
        var gridData = data.rows;
        var footerData = data.footer;
        reportTitle = reportTitle ?? 'Справка по парцел';

        var html = '<style>@page{size: landscape;}body,table{font-family: Arial;font-size: 10px;}</style>' +
        `<h2 align="center">${reportTitle}</h2>`;

        var filter = getPlotsReportFilterText();
        html += '<b>Филтър:</b><br/>';
        html += 'Име на парцел: <b>' + filter.zpName + '</b><br/><br/>';
        html += 'ИСАК номер: <b>' + filter.isakPrcUin + '</b><br/><br/>';
        html += 'Стопанство: <b>' + filter.farming + '</b><br/>';
        html += 'Стопанска година: <b>' + filter.farmingYear + '</b><br/>';
        html += 'Землище: <b>' + filter.ekate + '</b><br/>';
        html += 'Механизатор/Служител: <b>' + filter.performer + '</b><br/>';
        html += 'Период от ÷ до: <b>' + filter.dateFrom + '</b> ÷ <b>' + filter.dateTo + '</b><br/>';
        html += 'Вид обработка: <b>' + filter.eventType + '</b><br/>';
        html += 'Машина: <b>' + filter.machine + '</b><br/>';
        html += 'Прикачен инвентар: <b>' + filter.attachment + '</b><br/>';

        var header = {};
        for(var i=0; i<columns.length; i++)
        {
            header[columns[i].field] = columns[i].title;
        }

        var rows = gridData;
        rows.push(footerData[1]);
        html += Templates.table(header, rows);

        jQuery('body').append('<iframe id="printf" name="printf"></iframe>');
        var newWin = window.frames['printf'];
        newWin.document.write('<body onload=window.print()>'+html+'</body>');
        newWin.document.close();
        setTimeout(function () {
            jQuery('#printf').remove();
        }, 1000);

    })
    .fail(function (errorObj) {
        jQuery.messager.alert('Грешка', errorObj.getMessage());
    });

    return false;
}

function exportXLSDiaryReport(exportCurrentPage) {
    var gridOptions = jQuery('#diary-plots-report-tables').datagrid('options');

    var params = jQuery.extend({}, gridOptions.rpcParams);
    var page = null;
    var pageSize = null;
    if(exportCurrentPage === true)
    {
        page = gridOptions.pageNumber;
        pageSize = gridOptions.pageSize;
    }

    var sort = gridOptions.sortName;
    var order = gridOptions.sortOrder;
    params[0].filter = getPlotsReportFilterText();
    params[0].reportName = report;
    createDownloadVariables();

    TF.Rpc.Diary.DiaryReportsGrid.exportPlotsReportXLS(params[0], page, pageSize, sort, order)
    .done(function (data) {
        winDownloadDiaryReport.window('open');
        var path = data.path;
        _fileName = data.name;
        _pathFile = path;
        downloadFileDiaryReport.attr("href", path);
    })
    .fail(function (errorObj) {
        jQuery.messager.alert('Грешка', errorObj.getMessage());
    });
}

function printSummaryDiaryReport(currentPage) {
    var data = jQuery('#diary-report-tables').datagrid('getData');
    if(data.rows.length === 0) {
        jQuery.messager.alert('Грешка', 'Не може да отпечатате празна справка.');
        return false;
    }

    var gridOptions = jQuery('#diary-report-tables').datagrid('options');

    var params = jQuery.extend({}, gridOptions.rpcParams);
    var page     = (currentPage) ? gridOptions.pageNumber : null;
    var pageSize = (currentPage) ? gridOptions.pageSize : null;
    var sort = gridOptions.sortName;
    var order = gridOptions.sortOrder;

    TF.Rpc.Diary.DiaryReportsGrid.summaryByPerformer(params[0],page, pageSize, sort, order)
    .done(function (data) {
        var gridData = data.rows;
        var footerData = data.footer;

        var html = '<style>@page{size: landscape;}body,table{font-family: Arial;font-size: 10px;}</style>' +
        '<h2 align="center">Обобщена справка по механизатор/служител</h2>';

        var filter = getSummaryReportByPerformerFilterText();

        html += '<b>Филтър:</b><br/>';
        html += 'Механизатор/Служител: <b>' + filter.performer + '</b><br/>';
        html += 'Стопанство: <b>' + filter.farming + '</b><br/>';
        html += 'Землище: <b>' + filter.ekate + '</b><br/>';
        html += 'Период от ÷ до: <b>' + filter.dateFrom + '</b> ÷ <b>' + filter.dateTo + '</b><br/>';
        html += 'Вид обработка: <b>' + filter.eventType + '</b><br/>';
        html += 'Машина: <b>' + filter.machine + '</b><br/>';
        html += 'Прикачен инвентар: <b>' + filter.attachment + '</b><br/>';
        html += 'Парцел: <b>' + filter.zpName + '</b><br/><br/>';

        var header = {};
        var columns = gridOptions.columns[0];
        for(var i=0; i<columns.length; i++)
        {
            header[columns[i].field] = columns[i].title;
        }

        var rows = gridData;
        rows.push(footerData[1]);

        html += Templates.table(header, rows);

        jQuery('body').append('<iframe id="printf" name="printf"></iframe>');
        var newWin = window.frames['printf'];
        newWin.document.write('<body onload=window.print()>'+html+'</body>');
        newWin.document.close();
        setTimeout(function () {
            jQuery('#printf').remove();
        }, 1000);

    })
    .fail(function (errorObj) {
        jQuery.messager.alert('Грешка', errorObj.getMessage());
    });

    return false;
}

function exportXLSSummaryDiaryReport(exportCurrentPage) {
    var gridOptions = jQuery('#diary-report-tables').datagrid('options');

    var page = null;
    var pageSize = null;
    if(exportCurrentPage === true)
    {
        page = gridOptions.pageNumber;
        pageSize = gridOptions.pageSize;
    }

    var params = jQuery.extend({}, gridOptions.rpcParams);
    var sort = gridOptions.sortName;
    var order = gridOptions.sortOrder;
    params[0].filter = getSummaryReportByPerformerFilterText();
    createDownloadVariables();

    TF.Rpc.Diary.DiaryReportsGrid.exportPerformerSummaryReportXLS(params[0],page,pageSize,sort,order)
    .done(function (data) {

        winDownloadDiaryReport.window('open');
        var path = data.path;
        _fileName = data.name;
        _pathFile = path;
        downloadFileDiaryReport.attr("href", path);
    })
    .fail(function (errorObj) {
        jQuery.messager.alert('Грешка', errorObj.getMessage());
    });
}

function viewDetailedReportByPerformer(){

    var selected = jQuery('#diary-report-tables').datagrid('getSelected');

    if(selected) {
        displayDetailedReportByPerformer(selected.id);
    }
    else
    {
        jQuery.messager.alert('Грешка', 'Не е избран запис, за който да бъде показана подробна справка.');
    }
}

function printDetailedReportsBy() {
    var data = jQuery('#detailed-reports-by-grid').datagrid('getData');
    if(data.rows.length === 0) {
        jQuery.messager.alert('Грешка', 'Не може да отпечатате празна справка.');
        return false;
    }

    var gridOptions = jQuery('#detailed-reports-by-grid').datagrid('options');

    var params = jQuery.extend({}, gridOptions.rpcParams);
    var page = gridOptions.page;
    var pageSize = gridOptions.pageSize;
    var sort = gridOptions.sortName;
    var order = gridOptions.sortOrder;

    var group_by_text = gridOptions.columns[0][0].title;
    TF.Rpc.Diary.DiaryReportsGrid.detailedBy(params[0],page, pageSize, sort, order)
    .done(function (data) {
        var gridData = data.rows;
        var footerData = data.footer;

        var filter = getDetailedReportByPerformerFilterText();

        var html = '<style>@page{size: portrait;}body,table{font-family: Arial;font-size: 10px;}</style>' +
        '<h2 align="center">Индивидулана справка за ' + filter.performer + ' по ' + group_by_text.toLowerCase() + '</h2>';

        html += '<b>Филтър:</b><br/>';
        html += 'Стопанство: <b>' + filter.farming + '</b><br/>';
        html += 'Землище: <b>' + filter.ekate + '</b><br/>';
        html += 'Период от ÷ до: <b>' + filter.dateFrom + '</b> ÷ <b>' + filter.dateTo + '</b><br/>';
        html += 'Вид обработка: <b>' + filter.eventType + '</b><br/>';
        html += 'Машина: <b>' + filter.machine + '</b><br/>';
        html += 'Прикачен инвентар: <b>' + filter.attachment + '</b><br/>';
        html += 'Парцел: <b>' + filter.zpName + '</b><br/><br/>';

        var header = {};
        var columns = gridOptions.columns[0];
        for(var i=0; i<columns.length; i++)
        {
            header[columns[i].field] = columns[i].title;
        }

        var rows = gridData;
        rows.push(footerData[1]);

        html += Templates.table(header, rows);

        jQuery('body').append('<iframe id="printf" name="printf"></iframe>');
        var newWin = window.frames['printf'];
        newWin.document.write('<body onload=window.print()>'+html+'</body>');
        newWin.document.close();
        setTimeout(function () {
            jQuery('#printf').remove();
        }, 1000);

    })
    .fail(function (errorObj) {
        jQuery.messager.alert('Грешка', errorObj.getMessage());
    });

    return false;
}

function exportXLSDetailedReportsBy(exportCurrentPage) {
    var gridOptions = jQuery('#detailed-reports-by-grid').datagrid('options');

    var params = jQuery.extend({}, gridOptions.rpcParams);
    params[0].filter = getDetailedReportByPerformerFilterText();
    params[0].filter.group_by_text =  gridOptions.columns[0][0].title;

    var page = null;
    var pageSize = null;
    if (exportCurrentPage === true)
    {
        page = gridOptions.pageNumber;
        pageSize = gridOptions.pageSize;
    }
    var sort = gridOptions.sortName;
    var order = gridOptions.sortOrder;

    createDownloadVariables();

    TF.Rpc.Diary.DiaryReportsGrid.exportPerformerDetailedReportByXLS(params[0],page,pageSize,sort,order)
    .done(function (data) {
        winDownloadDiaryReport.window('open');
        var path = data.path;
        _fileName = data.name;
        _pathFile = path;
        downloadFileDiaryReport.attr("href", path);
    })
    .fail(function (errorObj) {
        jQuery.messager.alert('Грешка', errorObj.getMessage());
    });
}
