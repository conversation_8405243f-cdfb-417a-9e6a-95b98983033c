function initSubstanceTypesGrid(productStatus = 'active') {
    var productTypes = {
        fertilizer: 'Тор',
        chemical_treatment: 'ПРЗ',
        seeds: 'Семена',
        other: 'Други',
    };

    var toolbar = [{
        id: 'btn_add_substance_type',
        text: 'Добавяне',
        iconCls: 'icon-add',
        handler: function () {
            //clear old data
            is_edit = false;
            jQuery('#substance-type-name').find('> input').val('');
            jQuery('#substance-types > input').combobox('clear')
            jQuery('#substance-types > input').combobox('enable');
            jQuery('#win-add-edit-substance-type').window('resize', {
                width: 300,
                height: 140
            }).window('open');
        }
    }, {
        id: 'btn_edit_substance_type',
        text: 'Редактиране',
        iconCls: 'icon-edit',
        handler: function () {
            var row = jQuery('#diary-configs-tables').datagrid('getSelected');
            if (!row) return jQuery.messager.alert('Грешка', 'Моля изберете запис, който искате да редактирате!');
            is_edit = true;
            var requestObj = {
                id: row.id,
                request_type: 7
            };
            TF.Rpc.Diary.DiaryAuxiliaryItems.itemEditMark(requestObj)
                .done(function (data) {
                    jQuery('#substance-type-name').find('> input').val(data.name);

                    jQuery('#substance-types > input').combobox('setValue',data.options.product_type);
                    if(productStatus === 'active'){
                        jQuery('#substance-types > input').combobox('disable');
                    } else {
                        jQuery('#substance-types > input').combobox('enable');
                    }

                    jQuery('#win-add-edit-substance-type').window('resize', {
                        width: 300,
                        height: 140
                    }).window('open');
                })
                .fail(function (errorObj) {
                    jQuery.messager.alert('Грешка', errorObj.getMessage());
                });

        }
    }];

    if(productStatus === 'pending') {
        toolbar = [toolbar[1]]; //Get only edit button from the toolbar
    }

    jQuery('#diary-configs-tables').datagrid({
        width: 500,
        nowrap: false,
        singleSelect: true,
        pageSize: 20,
        fit: true,
        fitColumns: true,
        showFooter: true,
        border: false,
        checkbox: false,
        url: 'index.php?diary-rpc=diary-configs-grid',
        rpcParams: [{
            request_type: 7,
            status: productStatus
        }],
        idField: 'id',
        rownumbers: true,
        columns: [[
            {
                field: 'name',
                title: '<b>Име</b>',
                sortable: false,
                width: 150
            },
            {
                field: 'options',
                width: 150,
                title: '<b>Тип</b>',
                formatter: function (value) {
                    if(value){
                        return productTypes[value.product_type]
                    }
                }
            }
        ]],
        toolbar: toolbar,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}
