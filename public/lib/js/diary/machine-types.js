function initMachineTypesGrid()
{
	jQuery('#diary-configs-tables').datagrid({
		width: 700,
		nowrap: true,
		singleSelect: false,
		pageSize: 20,
		fit: true,
		fitColumns: true,
		showFooter: true,
		border: false,
		url: 'index.php?diary-rpc=diary-configs-grid',
		rpcParams: [{
			request_type: 3
		}],
		idField: 'id',
		frozenColumns: [[
				{
					field: 'ck',
					checkbox: true
				}
			]],
		columns: [
			[
				{
					field: 'name',
					title: '<b>Номенклатура</b>',
					sortable: false,
					width: 150
				}
			]
		],
		rownumbers: true,
		toolbar: [{
				id: 'btn_add_machine_type',
				text: 'Добавяне',
				iconCls: 'icon-add',
				handler: function() {
					//clear old data
                    is_edit = false;
					jQuery('#machine-type-name > input').val('');
					jQuery('#win-add-edit-machine-type').window('open');
				}
			}, {
				id: 'btn_edit_machine_type',
				text: 'Редактиране',
				iconCls: 'icon-edit',
				handler: function() {

					var getChecked = jQuery('#diary-configs-tables').datagrid('getChecked');
					if (getChecked[0]) {
                        is_edit = true;
						var	requestObj = new Object();
						requestObj = {
							id: getChecked[0].id,
							request_type: 3
						};

						TF.Rpc.Diary.DiaryAuxiliaryItems.itemEditMark(requestObj)
						.done(function (data) {
							jQuery('#machine-type-name > input').val(data.name);
							jQuery('#win-add-edit-machine-type').window('open');
						})
						.fail(function (errorObj) {
                            jQuery.messager.alert('Грешка', errorObj.getMessage());
						});
					} else {
						jQuery.messager.alert('Грешка', 'Моля изберете запис, който искате да редактирате!');
					}
				}
			},{
				id: 'btn_sync_machine_types',
				text: 'Синхронизиране',
				iconCls: 'icon-reload',
				handler: function() {
					TF.Rpc.Diary.WialonActions.getWialonMachineTypes()
					.done(function (data) {
						jQuery('#diary-configs-tables').datagrid('reload');
					})
					.fail(function (errorObj) {
                        if (errorObj.is(TF.Rpc.ExceptionsList.INVALID_OR_EXPIRED_TOKEN)) {
                            jQuery('#win-get-wialon-token').window('open');
                            return;
                        }
                        jQuery.messager.alert('Грешка', errorObj.getMessage());
					});
				}
			}],
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}
