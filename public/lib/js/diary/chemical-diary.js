jQuery(function() {
	jQuery('#choose-chemical-diary-farming').combobox({
		url: 'index.php?common-rpc=farming-combobox',
		valueField: 'id',
		textField: 'name',
		onSelect: function(record) {
			var farming = record.id;
			var year = jQuery('#choose-chemical-diary-year').combobox('getValue');

			if (farming && year) {
				initSubstancesDiaryDatagrid(farming, year);
				initFertilizersDiaryDatagrid(farming, year);
			}
			
		},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

	jQuery('#choose-chemical-diary-year').combobox({
		url: 'index.php?common-rpc=farming-year-combobox',
		valueField: 'id',
		textField: 'title',
		onSelect: function(record) {
			var farming = jQuery('#choose-chemical-diary-farming').combobox('getValue');
			var year = record.id;

			if (farming && year) {
				initSubstancesDiaryDatagrid(farming, year);
				initFertilizersDiaryDatagrid(farming, year);
			}
			
		},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

	jQuery('#print-chemical-diary').bind('click', function() {
		var	requestObj = {
			farming: jQuery('#choose-chemical-diary-farming').combobox('getValue'),
			year: jQuery('#choose-chemical-diary-year').combobox('getValue')
		};

		TF.Rpc.Diary.DiaryReportsGrid.createChemicalDiary(requestObj)
		.done(function (data) {
			jQuery('body').append('<iframe id="printf" name="printf"></iframe>');
			var newWin = window.frames['printf'];
			newWin.document.write('<body onload=window.print()>'+data+'</body>');
			newWin.document.close();
			setTimeout(function () {
				jQuery('#printf').remove();
			}, 1000);
		})
		.fail(function (errorObj) {
            jQuery.messager.alert('Грешка', errorObj.getMessage());
		});
    });

    jQuery('#export-chemical-diary-word').bind('click', function () {

		const farming = jQuery('#choose-chemical-diary-farming').combobox('getValue');
    	const year = jQuery('#choose-chemical-diary-year').combobox('getValue');

		if (!year || !farming) {
			jQuery.messager.alert('Грешка','Моля изберете стопанство и година, за която да се генерира Дневник');
			return;
		}
		
        var requestObj = {
            farming: farming,
            year: year
        };

        TF.Rpc.Diary.DiaryReportsGrid.createChemicalDiaryWord(requestObj)
        .done(function (data) {
            jQuery('#btn-download-file').attr('href', data.path)
            jQuery('#win-download').window('open');

        })
        .fail(function (errorObj) {
            jQuery.messager.alert('Грешка', errorObj.getMessage());
        });
    });
});

function displayChemicalDiary()
{
	var farming = null;
	var year = null;
	if (ZPselected.farming !== 0 && ZPselected.year !== 0) {
		farming = ZPselected.farming;
		year = ZPselected.year;
	} else {
		farming = jQuery('#choose-chemical-diary-farming').combobox('getValue');
		year = jQuery('#choose-chemical-diary-year').combobox('getValue');
	}

	jQuery('#win-chemical-diary').window('open');
	
	initSubstancesDiaryDatagrid(farming, year);
	initFertilizersDiaryDatagrid(farming, year);
}

function initSubstancesDiaryDatagrid(farming, year)
{
	let url,params;
	if (farming && year) {
		url = 'index.php?diary-rpc=substances-diary-grid';
		params = {
			farming: farming,
			year: year
		}
	}

	jQuery('#substances-diary-tables').datagrid({
		title: 'Проведени химични обработки',
		nowrap: true,
		singleSelect: true,
		pageSize: 20,
		fit: true,
		fitColumns: true,
		border: false,
		url: url,
		rpcParams: [params],
		idField: 'id',
		columns: [
			[
				{
					field: 'isak',
					title: '<b>Парцел (ИСАК)</b>',
					sortable: false,
					width: 150
				},{
					field: 'date_from',
					title: '<b>Начална дата</b>',
					sortable: false,
					width: 150
				},{
					field: 'date_to',
					title: '<b>Крайна дата</b>',
					sortable: false,
					width: 150
				},{
					field: 'pest_name',
					title: '<b>Вредител</b>',
					sortable: false,
					width: 150
				},{
					field: 'substance_name',
					title: '<b>Търговско<br>наименование<br>на ПРЗ</b>',
					sortable: false,
					width: 150
				},{
					field: 'substance_dose',
					title: '<b>Доза на дка</b>',
					sortable: false,
					width: 150,
					formatter: function (value, row) {
						if(row['unit_type'] == undefined) return value;
						return value + ' ' + row['unit_type'];
					}
				},{
					field: 'treated_area',
					title: '<b>Третирани площи</b>',
					sortable: false,
					width: 150,
					formatter: function (value, row) {
						if (value == undefined) return undefined;
						return value + ' дка';
					}
				}, {
					field: 'apply_technic',
					title: '<b>Техника за приложение</b>',
					sortable: false,
					width: 150
				},{
					field: 'quarantine_period',
					title: '<b>Карантинен срок</b>',
					sortable: false,
					width: 150,
					formatter: function (value, row) {
						if(!value) return value;
						return value + ' Дни';
					}
				}
				,{
					field: 'quarantine_until',
					title: '<b>Най-ранна дата<br/>за прибиране</b>',
					sortable: false,
					width: 150
				}
			]
		],
		rownumbers: true,
		pagination: true,
		onBeforeLoad: function() {
			jQuery('#substances-diary-tables').datagrid('clearChecked');
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function initFertilizersDiaryDatagrid(farming, year)
{
	let url,params;
	if (farming && year) {
		url = 'index.php?diary-rpc=fertilizers-diary-grid';
		params = {
			farming: farming,
			year: year
		}
	}

	jQuery('#fertilizers-diary-tables').datagrid({
		title: 'Употребени минерални и органични торове',
		nowrap: true,
		singleSelect: true,
		pageSize: 20,
		fit: true,
		fitColumns: true,
		border: false,
		url: url,
		rpcParams: [params],
		idField: 'id',
		columns: [
			[
				{
					field: 'isak',
					title: '<b>Парцел (ИСАК)</b>',
					sortable: false,
					width: 150
				},{
					field: 'date_from',
					title: '<b>Начална дата</b>',
					sortable: false,
					width: 150
				},{
					field: 'date_to',
					title: '<b>Крайна дата</b>',
					sortable: false,
					width: 150
				},{
					field: 'culture',
					title: '<b>Култура</b>',
					sortable: false,
					width: 150
				},{
					field: 'substance_name',
					title: '<b>Търговско<br>наименование</b>',
					sortable: false,
					width: 150
				},{
					field: 'used_material_avg',
					title: '<b>Употребено количество<br/> в натура (норма/дка)</b>',
					sortable: false,
					width: 150
				}, {
					field: 'treated_area',
					title: '<b>Наторени площи</b>',
					sortable: false,
					width: 150,
					formatter: function (value, row) {
						if (value == undefined) return undefined;
						return value + ' дка';
					}
				}
			]
		],
		rownumbers: true,
		pagination: true,
		onBeforeLoad: function() {
			jQuery('#fertilizers-diary-tables').datagrid('clearChecked');
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}
