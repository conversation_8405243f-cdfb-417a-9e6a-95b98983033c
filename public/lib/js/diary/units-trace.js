UnitsTrace = (function(){
    var markers = [];

    var unitsLayer;
    var unitsTrackLayer;

    var unitsTrack = [];

    var drawTrackEnabled = false;

    // Print message to log
    function msg(text) {
        console.log(text);
    }

    function initUnits() {

        unitsLayer = new OpenLayers.Layer.Markers('units');
        map.addLayer(unitsLayer);

        var sess = wialon.core.Session.getInstance(); // get instance of current Session
        // specify what kind of data should be returned
        var flags = wialon.item.Item.dataFlag.base | wialon.item.Unit.dataFlag.lastMessage;

        sess.loadLibrary("itemIcon"); // load Icon Library
        sess.updateDataFlags( // load items to current session
            [{type: "type", data: "avl_unit", flags: flags,mode: 0}], // Items specification
            function (code) { // updateDataFlags callback
                if (code) { msg(wialon.core.Errors.getErrorText(code)); return; } // exit if error code

                var units = sess.getItems("avl_unit"); // get loaded 'avl_resource's items
                if (!units || !units.length){ msg("No units found"); return; } // check if units found
                for (var i = 0; i < units.length; i++) { // construct Select list using found resources
                    showUnit(units[i].getId());
                }

                jQuery('#tool-units-truck').linkbutton('enable');
        });
    }

    function initTrack() {
        unitsTrackLayer = new OpenLayers.Layer.Vector('unitsTrackLayer');
        map.addLayer(unitsTrackLayer);

        var units = wialon.core.Session.getInstance().getItems();

        if(!units) {
            return;
        }

        unitsTrack = [];
        for(var i = 0; i < units.length; i++) {
            var unit = units[i];
            unitsTrack[unit.getId()] = new OpenLayers.Feature.Vector(
                new OpenLayers.Geometry.LineString(),
                {},
                {
                    strokeWidth: 3,
                    strokeColor: '#0000ff'
                }
            );

            unitsTrackLayer.addFeatures([unitsTrack[unit.getId()]]);
        }
    }

    function showUnit(unitId){

        var unit = wialon.core.Session.getInstance().getItem(unitId); // get unit by id
        if(!unit) return; // exit if no unit
        var pos = unit.getPosition(); // get unit position
        if(!pos) return; // exit if no position

        if (map) { // check if map created
            var lonlat = new OpenLayers.LonLat(pos.x, pos.y).transform('EPSG:4326', 'EPSG:900913');
            var size = new OpenLayers.Size(32, 32);
            var offset = new OpenLayers.Pixel(-(size.w / 2),-(size.h / 2));
            var icon = new OpenLayers.Icon(unit.getIconUrl(32), size, offset);
            var marker = new OpenLayers.Marker(lonlat, icon);
            markers[unit.getId()] = marker;
            marker.attributes = {};
            marker.attributes.unitId = unit.getId();
            unitsLayer.addMarker(marker);

            marker.events.register('mouseover', marker, function(e) {

                openPopup(marker);
            });

            marker.events.register('mouseout', marker, closePopup);

            unit.addListener("messageRegistered", redrawUnit); // register event when we will receive message
        }
    }

    function redrawUnit(event) {
        var unit = event.getTarget();
        var pos = event.getData().pos; // get data from event
        if (!pos) return; // exit if no position 
        var marker = markers[event.getTarget().getId()];

        if(marker.lonlat.equals(lonlat)) {
            return;
        }

        var lonlat = new OpenLayers.LonLat(pos.x, pos.y).transform('EPSG:4326', 'EPSG:900913');
        marker.lonlat = lonlat;
        unitsLayer.redraw(true);

        if(drawTrackEnabled && unitsTrack[unit.getId()]) {
            var point = new OpenLayers.Geometry.Point(pos.x, pos.y).transform('EPSG:4326', 'EPSG:900913');
            unitsTrack[unit.getId()].geometry.addPoint(point);
            unitsTrackLayer.redraw(true);
        }
    }

    function openPopup(marker) {
        var lonlat = marker.lonlat;

        var unit = wialon.core.Session.getInstance().getItem(marker.attributes.unitId); // get unit by id

        // A popup with some information about our location
        var popup = new OpenLayers.Popup("Popup", 
            lonlat,
            null,
            '<div style="padding: 5px 0 0 5px;">' + unit.getName() + '</div>',
            null
        );

        marker.attributes.popup = popup;

        popup.autoSize = true;

        // and add the popup to it.
        map.addPopup(popup);
    }

    function closePopup(e) {
        this.attributes.popup.hide();
    }

    // execute when DOM ready
    jQuery(document).ready(function () {
        map.events.register('addlayer', map, function(evt) {
            if(unitsTrackLayer) {
                map.raiseLayer(unitsTrackLayer, map.layers.length);
            }

            if(unitsLayer) {
                map.raiseLayer(unitsLayer, map.layers.length);
            }
        });

        jQuery('#tool-units').linkbutton({'onClick': function() {
            var button = jQuery(this);
            var selected = jQuery(this).linkbutton('options').selected;
            if(selected) {
                wialon.core.Session.getInstance().initSession(wialonServer); // init session
                wialon.core.Session.getInstance().loginToken(trackToken, // try to login
                    function (code) { // login callback
                        if (code === 4 || code === 8) {
                            jQuery('#win-get-wialon-token').window('open');
                            button.linkbutton('unselect');
                            return;
                        }
                        // if error code - print error message
                        if (code) {
                            msg(wialon.core.Errors.getErrorText(code));
                            button.linkbutton('unselect');
                            return;
                        }
                        msg("Logged successfully");
                        initUnits(); // when login suceed then run init() function
                });
            }
            else
            {
                wialon.core.Session.getInstance().logout();
                if(unitsLayer) {
                    map.removeLayer(unitsLayer);
                    unitsLayer = undefined;
                }
                if(unitsTrackLayer) {
                    unitsTrackLayer.removeAllFeatures();
                    map.removeLayer(unitsTrackLayer);
                    unitsTrackLayer = undefined;
                }

                drawTrackEnabled = false;
                jQuery('#tool-units-truck').linkbutton('disable');
                jQuery('#tool-units-truck').linkbutton('unselect');
            }
        }});

        jQuery('#tool-units-truck').linkbutton({'onClick': function() {
            var selected = jQuery(this).linkbutton('options').selected;
            if(selected) {
                drawTrackEnabled = true;
                initTrack();
            }
            else
            {
                drawTrackEnabled = false;
                if(unitsTrackLayer) {
                    unitsTrackLayer.removeAllFeatures();
                    map.removeLayer(unitsTrackLayer);
                    unitsTrackLayer = undefined;
                }
            }
        }});

    });
}());