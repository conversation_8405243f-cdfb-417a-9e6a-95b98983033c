function initPerformersGrid()
{
	jQuery('#diary-configs-tables').datagrid({
		width: 500,
		nowrap: true,
		singleSelect: false,
		pageSize: 20,
		fit: true,
		fitColumns: true,
		showFooter: true,
		border: false,
		url: 'index.php?diary-rpc=diary-configs-grid',
		rpcParams: [{
			request_type: 9
		}],
		idField: 'id',
		frozenColumns: [[
				{
					field: 'ck',
					checkbox: true
				}
			]],
		columns: [
			[
				{
					field: 'name',
					title: '<b>Имена</b>',
					sortable: false,
					width: 100
				}, {
					field: 'perf_title',
					title: '<b>Длъжност</b>',
					sortable: false,
					width: 150
				}, {
					field: 'description',
					title: '<b>Коментар</b>',
					sortable: false,
					width: 150
				}
			]
		],
		rownumbers: true,
		toolbar: [{
				id: 'btn_add_performer',
				text: 'Добавяне',
				iconCls: 'icon-add',
				handler: function() {
                    is_edit = false;
					jQuery('#diary-configs-tables').datagrid('clearChecked');
					clearEditPerformetFieldsData();
					jQuery('#win-add-edit-performer').window('open');
				}
			}, {
				id: 'btn_edit_performer',
				text: 'Редактиране',
				iconCls: 'icon-edit',
				handler: function() {
					var getChecked = jQuery('#diary-configs-tables').datagrid('getChecked');

					if (getChecked[0]) {
                        is_edit = true;
						var	requestObj = new Object();
						requestObj = {
							id: getChecked[0].id,
							request_type: 9
						};
						TF.Rpc.Diary.DiaryAuxiliaryItems.itemEditMark(requestObj)
						.done(function (data) {
							setEditPerformetFieldsData(data);
							jQuery('#win-add-edit-performer').window('open');
						})
						.fail(function (errorObj) {
                            jQuery.messager.alert('Грешка', errorObj.getMessage());
						});
					} else {
						jQuery.messager.alert('Грешка', 'Моля изберете запис, който искате да редактирате!');
					}
				}
			},{
				id: 'btn_sync_performers',
				text: 'Синхронизиране',
				iconCls: 'icon-reload',
				handler: function() {
					TF.Rpc.Diary.WialonActions.getWialonDrivers()
					.done(function (data) {
						jQuery('#diary-configs-tables').datagrid('reload');
					})
					.fail(function (errorObj) {
                        if (errorObj.is(TF.Rpc.ExceptionsList.INVALID_OR_EXPIRED_TOKEN)) {
                            jQuery('#win-get-wialon-token').window('open');
                            return;
                        }
                        jQuery.messager.alert('Грешка', errorObj.getMessage());
					});
				}
			}],
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function setEditPerformetFieldsData(data) {
	jQuery('#perf-names > input').val(data.name);
	jQuery('#perf-title > input').val(data.perf_title);
	jQuery('#perf-description > textarea').val(data.description);
}

function getEditPerformetFieldsData() {
	var	returnObj = new Object();
	returnObj = {
		name: jQuery('#perf-names > input').val(),
		perf_title: jQuery('#perf-title > input').val(),
		description: jQuery('#perf-description > textarea').val()
	};

	return returnObj;
}

function clearEditPerformetFieldsData() {
	jQuery('#perf-names > input').val('');
	jQuery('#perf-title > input').val('');
	jQuery('#perf-description > textarea').val('');
}
