Namespace('TF.Rpc.Diary');

//variables hold farming and year and plot_id for expanded node
var ZPselected = {
    farming: 0,
    year: 0,
    plot_id: 0
};

var lastLoadedMap = null;
//filter params
var filterIsak,
    filterEkate,
    filterCulture,
    filterEventPhase,
    filterEventType,
    filterEventSubtype,
    filterEventPerformer,
    filterEventDateFrom,
    filterEventDateTo;

var COMBO_TREE_CTRL;
var ZP_TREE_CTRL;
var ZP_EDIT_WIN;
var ZP_TRACK_GRID;
//init buttons for zplots tree
jQuery(function () {

    //declaration of selectors
    ZP_EDIT_WIN = jQuery('#win-edit-zp');
    ZP_TRACK_GRID = jQuery('#diary-configs-plot-tracks');

    jQuery('#btn-edit-zplot').bind('click', function () {
        var selected = ZP_TREE_CTRL.tree('getSelected');
        if (!selected) {
            jQuery.messager.alert('Грешка', 'Не е избран земеделски парцел');
            return false;
        }
        var obj = {};
        obj.farming_id = selected['attributes'].farming;
        obj.year_id = selected['attributes'].year;
        obj.zplot_id = selected.id;
        TF.Rpc.Diary.ZPTree.markForEdit(obj)
            .done(function (data) {
                initZPEditComponents();
                clearZPEditFields();
                setZPEditFields(data);
                ZP_EDIT_WIN.window('open');
            })
            .fail(function (errorObj) {
                jQuery.messager.alert('Грешка', errorObj.getMessage());
            });
        return false;
    });

    jQuery('#btn-filter-event').bind('click', function () {
        jQuery('#win-tree-filter').window('open');
        return false;
    });

    //execute filter inside window
    jQuery('#btn-filter-zp-tree').bind('click', function () {
        initZPlotsFilterValues();
        ZP_TREE_CTRL.tree('reload');
        jQuery('#win-tree-filter').window('close');
    });

    jQuery('#btn-clear-filter-event').bind('click', function () {
        clearZPlotsFilter();
        ZP_TREE_CTRL.tree('reload');
        return false;
    });
});

function initComboTree() {
    COMBO_TREE_CTRL = jQuery('#zp-comboTree');
    COMBO_TREE_CTRL.combotree({
        url: 'index.php?diary-rpc=diary-zp-tree',
        animate: true,
        lines: true,
        multiple: false,
        editable: false,
        panelHeight: 'auto',
        rpcMethod: 'read',
        rpcParams: [{
            farming: 0,
            year: 0,
            plot_id: 0
        }],
        onLoadSuccess: function () {
            COMBO_TREE_CTRL.combotree('setText', 'Стопанство / Година');
            map.updateSize();
        },
        onBeforeSelect: function (node) {
            if (node.children !== undefined) return;
            //we are on the last element
            var farmNode = COMBO_TREE_CTRL.combotree('tree').tree('getParent', node.target);
            if (!farmNode) return false;
            ZPselected.farming = farmNode.id;
            ZPselected.year = node.id;
            ZPselected.plot_id = 0;
            // load plots on tree
            ZP_TREE_CTRL.tree('options').rpcParams = [ZPselected];
            ZP_TREE_CTRL.tree('options').url = 'index.php?diary-rpc=diary-zp-tree';
            ZP_TREE_CTRL.tree('reload');
        },
        onClick: function (node) {
            var farmNode = COMBO_TREE_CTRL.combotree('tree').tree('getParent', node.target);
            if (!farmNode) {
                COMBO_TREE_CTRL.combotree('setText', node.text);
                return false;
            }
            COMBO_TREE_CTRL.combotree('setText', farmNode.text + ' / ' + node.text);
        },
        onSelect: function (node) {
            var farmNode = COMBO_TREE_CTRL.combotree('tree').tree('getParent', node.target);
            if (!farmNode) return false;
            ZPselected.table = node.attributes.table;
            ZPselected.extent = node.attributes.extent;
            if (ZPselected.table && lastLoadedMap !== ZPselected.table) { //and not already loaded
                if (lastLoadedMap != undefined) {
                    removeLayerByName(lastLoadedMap); //remove old one
                }
                lastLoadedMap = ZPselected.table; //keep track of last selected map
                loadMapLayer(ZPselected.table, ZPselected.extent); //load new map layer
            }
            var bounds = new OpenLayers.Bounds.fromString(ZPselected.extent).transform(new OpenLayers.Projection("EPSG:32635"), map.getProjectionObject());
            var b = bounds.getCenterLonLat();
            var LonLat = new OpenLayers.LonLat(b.lon, b.lat);
            map.zoomToExtent(bounds);
            map.panTo(LonLat);
            map.updateSize();
        },
        onBeforeExpand: function () {
            return false;
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initZPTree() {
    ZP_TREE_CTRL = jQuery('#zp-tree');
    ZP_TREE_CTRL.tree({
        animate: true,
        lines: true,
        rpcMethod: 'read',
        rpcParams: [{
            farming: ZPselected.farming,
            year: ZPselected.year,
            plot_id: ZPselected.plot_id
        }],
        checkbox: false,
        onLoadSuccess: function (node, data) {
            //endLoading();
            if (data.total !== undefined && data.total === 0) {
                jQuery.messager.alert('Внимание', 'Не са открити записи', 'warning');
            }
        },
        onSelect: function (node) {
            if (!node) return;
            initZplotEventsGrid(node['attributes'].farming, node['attributes'].year, node.id);
            displayFeatureSelection(node['attributes'].geometry, node['attributes'].label);
            map.updateSize();
            jQuery('#right-layout').layout('expand','south');
            var params = get_ZP_TRACK_window_data();
            var d1 = new Date(params.date_from);
            var d2 = new Date(params.date_to);
            var diff = Math.abs(d1 - d2) / 86400000;
            ZP_TRACK_GRID.treegrid('options').rpcParams = [params];
            if (diff > 7) {
                jQuery.messager.confirm('Потвърждение', 'Тази функция може да отнеме няколко минути при периоди повече от една седмица. Жалаете ли да продължите?', function (r) {
                    if (r != false) {
                        ZP_TRACK_GRID.treegrid('reload');
                    }
                });
            } else {
                if (diff == 0) ZP_TRACK_GRID.treegrid('reload');
            }
            jQuery('#win-diary-plot-tracks').window('setTitle', 'Данни от FarmTrack За парцел: ' + node.text);
        },
        onBeforeExpand: function (node) {
            ZPselected.farming = node.attributes.farming;
            ZPselected.year = node.attributes.year;
        },
        onBeforeSelect: function (node) {
            ZPselected.farming = node.attributes.farming;
            ZPselected.year = node.attributes.year;
            ZPselected.area = +node.attributes.area;
            ZPselected.plot_id = node.id;
            ZPselected.extent = node.attributes.extent;
            ZPselected.culture = node.attributes.culture;
            ZPselected.culture_name = node.attributes.culture_name;
            var bounds = new OpenLayers.Bounds.fromString(ZPselected.extent).transform(new OpenLayers.Projection("EPSG:32635"), map.getProjectionObject());
            var b = bounds.getCenterLonLat();
            var LonLat = new OpenLayers.LonLat(b.lon, b.lat);
            map.zoomToExtent(bounds);
            map.panTo(LonLat);
            map.updateSize();
        },
        onBeforeLoad: function (node, param) {
            //here we filter the plots
            if (ZPselected.farming !== 0 && ZPselected.year !== 0) {
                param.farming = ZPselected.farming;
                param.year = ZPselected.year;
            }
            if (ZPselected.plot_id !== 0) {
                param.plot_id = ZPselected.plot_id;
            }
            var filter = {};
            //adding filter params
            filter.zp_isak = filterIsak;
            filter.zp_ekate = filterEkate;
            filter.zp_culture = filterCulture;
            filter.event_phase = filterEventPhase;
            filter.event_type = filterEventType;
            filter.event_subtype = filterEventSubtype;
            filter.event_performer = filterEventPerformer;
            filter.event_date_from = filterEventDateFrom;
            filter.event_date_to = filterEventDateTo;
            param.filter = filter;

            ZP_TREE_CTRL.tree('options').rpcParams = [param];
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initZPEditComponents() {
    jQuery('#edit-zp-ekate-input').combobox({
        url: 'index.php?common-rpc=ekate-combobox',
        valueField: 'ekate',
        textField: 'ekate',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    jQuery('#edit-zp-culture-input').combobox({
        url: 'index.php?common-rpc=culture-combobox',
        valueField: 'id',
        textField: 'name',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function saveZPlotEdit() {
    var requestObj = getZPEditFields();
    var selected = ZP_TREE_CTRL.tree('getSelected');
    requestObj.farming_id = selected['attributes'].farming;
    requestObj.year_id = selected['attributes'].year;
    requestObj.zplot_id = selected.id;

    TF.Rpc.Diary.ZPTree.saveEdit(requestObj)
        .done(function () {
            finishZPlotSaveEdit();
        })
        .fail(function (errorObj) {
            jQuery.messager.alert('Грешка', errorObj.getMessage());
        });
}

function finishZPlotSaveEdit() {
    ZP_EDIT_WIN.window('close');
    ZP_TREE_CTRL.tree('reload');
}

function initZPlotsFilterValues() {
    filterIsak = jQuery('#search-isak-num > input').val();
    filterEkate = jQuery('#search-ekate-input').combobox('getValue');
    filterCulture = jQuery('#search-culture-input').combobox('getValue');
    filterEventPhase = jQuery('#search-event-phase-input').combobox('getValue');
    filterEventType = jQuery('#search-event-type-input').combobox('getValue');
    filterEventSubtype = jQuery('#search-event-subtype-input').combobox('getValue');
    filterEventPerformer = jQuery('#search-event-performer-input').combobox('getValue');
    filterEventDateFrom = jQuery('#search-event-date-from-input').datebox('getValue');
    filterEventDateTo = jQuery('#search-event-date-to-input').datebox('getValue');
}

function clearZPlotsFilter() {
    filterIsak = undefined;
    filterEkate = undefined;
    filterCulture = undefined;
    filterEventPhase = undefined;
    filterEventType = undefined;
    filterEventSubtype = undefined;
    filterEventPerformer = undefined;
    filterEventDateFrom = undefined;
    filterEventDateTo = undefined;
    ZPselected.plot_id = undefined;
    jQuery('#search-isak-num > input').val('');
    jQuery('#search-ekate-input').combobox('reset');
    jQuery('#search-culture-input').combobox('reset');
    jQuery('#search-event-phase-input').combobox('reset');
    jQuery('#search-event-type-input').combobox('reset');
    jQuery('#search-event-subtype-input').combobox('reset');
    jQuery('#search-event-performer-input').combobox('reset');
    jQuery('#search-event-date-from-input').datebox('reset');
    jQuery('#search-event-date-to-input').datebox('reset');
}

function clearZPEditFields() {
    jQuery('#edit-zp-isak-input').val('');
    jQuery('#edit-zp-ekate-input').combobox('reset');
    jQuery('#search-culture-input').combobox('reset');
    jQuery('#edit-zp-obrabotki-input').val('');
    jQuery('#edit-zp-dobivi-input').val('');
    jQuery('#edit-zp-napoqvane-input').val('');
    jQuery('#edit-zp-polivki-input').val('');
    jQuery('#edit-zp-polzvatel-input').val('');
}

function setZPEditFields(data) {
    if (data) {
        jQuery('#edit-zp-isak-input').val(data.isak_prc_uin);
        jQuery('#edit-zp-ekate-input').combobox('select', data.ekatte);
        jQuery('#edit-zp-culture-input').combobox('select', data.culture);
        jQuery('#edit-zp-obrabotki-input').val(data.obrabotki);
        jQuery('#edit-zp-dobivi-input').val(data.dobivi);
        jQuery('#edit-zp-napoqvane-input').val(data.napoqvane);
        jQuery('#edit-zp-polivki-input').val(data.polivki);
        jQuery('#edit-zp-polzvatel-input').val(data.polzvatel);
    }
}

function getZPEditFields() {
    return {
        isak: jQuery('#edit-zp-isak-input').val(),
        ekate: jQuery('#edit-zp-ekate-input').combobox('getValue'),
        culture: jQuery('#edit-zp-culture-input').combobox('getValue'),
        obrabotki: jQuery('#edit-zp-obrabotki-input').val(),
        dobivi: jQuery('#edit-zp-dobivi-input').val(),
        napoqvane: jQuery('#edit-zp-napoqvane-input').val(),
        polivki: jQuery('#edit-zp-polivki-input').val(),
        polzvatel: jQuery('#edit-zp-polzvatel-input').val()
    };
}
