function initSubstanceUnitsGrid() {
    jQuery('#diary-configs-tables').datagrid({
        width: 500,
        nowrap: true,
        singleSelect: true,
        pageSize: 20,
        fit: true,
        fitColumns: true,
        showFooter: true,
        border: false,
        url: 'index.php?diary-rpc=diary-configs-grid',
        rpcParams: [{
            request_type: 10
        }],
        idField: 'id',
        columns: [
            [
                {
                    field: 'name',
                    title: '<b>Номенклатура</b>',
                    sortable: false,
                    width: 150
                }
            ]
        ],
        rownumbers: true,
        toolbar: [{
            id: 'btn_add_units_type',
            text: 'Добавяне',
            iconCls: 'icon-add',
            handler: function () {
                //clear old data
                is_edit = false;
                jQuery('#diary-configs-tables').datagrid('clearChecked');
                jQuery('#substance-unit-name').val('');
                var edit_win = jQuery('#win-add-edit-units');
                edit_win.window('resize', {width: '300', height: '120'});
                edit_win.window('open');
            }
        }, {
            id: 'btn_edit_units_type',
            text: 'Редактиране',
            iconCls: 'icon-edit',
            handler: function () {
                var getChecked = jQuery('#diary-configs-tables').datagrid('getChecked');
                if (getChecked[0]) {
                    is_edit = true;
                    var requestObj = {
                        id: getChecked[0].id,
                        request_type: 7
                    };
                    TF.Rpc.Diary.DiaryAuxiliaryItems.itemEditMark(requestObj)
                        .done(function (data) {
                            var pos =  data.name.indexOf('/');
                            if( pos !== -1) data.name = data.name.substring(0, pos);
                            jQuery('#substance-unit-name').val(data.name);
                            var edit_win = jQuery('#win-add-edit-units');
                            edit_win.window('resize', {width: '300', height: '120'});
                            edit_win.window('open');
                        })
                        .fail(function (errorObj) {
                            jQuery.messager.alert('Грешка', errorObj.getMessage());
                        });
                } else {
                    jQuery.messager.alert('Грешка', 'Моля изберете запис, който искате да редактирате!');
                }
            }
        }],
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: function (data) {
            if (!data || !data.result || !data.result.rows ) return;
            for (var i = 0; i < data.result.rows.length; i++) {
                var name = data.result.rows[i].name;
                var pos =  name.indexOf('/');
                if( pos !== -1) name = name.substring(0, pos);
                data.result.rows[i].name = name;
            }
            return data.result;
        }
    });
}
