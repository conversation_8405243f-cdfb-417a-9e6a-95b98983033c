Namespace('TF.Rpc.Plots');

var totalContractArea = 0;

function initSalesContractsPlotsGrid(contract_id) {

    selectedContractId = contract_id;
    var contractPlotsDatagrid = jQuery('#sales-contract-plots-tables');
    var isDatagridBound = contractPlotsDatagrid.data().hasOwnProperty('datagrid');

    if (isDatagridBound) {
        if (selectedContractId !== 0) {
            contractPlotsDatagrid.datagrid({
                url: 'index.php?sales-contracts-rpc=sales-contracts-plots-datagrid',
                rpcParams: [{
                    contract_id: selectedContractId
                }],
            });
        } else {
            contractPlotsDatagrid.datagrid({data: {rows: [], total: 0}});
        }
        return;
    }

    contractPlotsDatagrid.datagrid({
        iconCls: 'icon-edit-geometry',
        nowrap: true,
        title: 'Имоти',
        autoRowHeight: true,
        striped: true,
        pageSize: 10,
        fit: true,
        singleSelect: true,
        fitColumns: false,
        showFooter: true,
        sortName: 'gid',
        sortOrder: 'asc',
        idField: 'gid',
        rowStyler: function (index, row) {
            var style = [];
            if (row.is_edited == true) {
                style.push('color: #aaa');
            }
            return style.join(';');
        },
        columns: [[
            {
                field: 'land',
                title: '<b>Землище</b>',
                sortable: true,
                width: 100
            }, {
                field: 'kad_ident',
                title: '<b>Идентификатор</b>',
                sortable: true,
                width: 100
            }, {
                field: 'contract_area_for_sale',
                title: '<b>Продадена площ<br> (дка)</b>',
                align: 'center',
                sortable: true,
                width: 150
            }, {
                field: 'price_per_acre',
                title: '<b>Цена/дка </b>',
                align: 'center',
                sortable: true,
                width: 90
            }, {
                field: 'price_sum',
                title: '<b>Сума</b>',
                align: 'center',
                sortable: true,
                width: 90
            }, {
                field: 'category',
                title: '<b>Категория</b>',
                sortable: true,
                width: 100
            }, {
                field: 'area_type',
                title: '<b>НТП</b>',
                sortable: true,
                width: 100
            }
        ]],
        pagination: true,
        rownumbers: true,
        toolbar: [
            {
                id: 'btninneraddcontractplot',
                text: 'Добавяне',
                iconCls: 'icon-add',
                handler: function () {

                    var contractSelected = jQuery('#salescontracts-tree').tree('getSelected');

                    if (contractSelected) {
                        jQuery('#win-plots-add').window('open');
                        initSalesContractsPlotsAddGrid(contractSelected.id, contractSelected.attributes.contract_start_date, contractSelected.attributes.farming_id);
                    } else {
                        jQuery.messager.alert('Грешка', 'Не е избран договор');
                    }
                }
            }, {
                id: 'btninnerdeletecontractplot',
                text: 'Премахване',
                iconCls: 'icon-delete',
                handler: function () {
                    var getChecked = jQuery('#sales-contract-plots-tables').datagrid('getChecked');
                    var contractData = jQuery('#salescontracts-tree').tree('getSelected');

                    if (contractData) {
                        if (getChecked[0]) {
                            jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да премахнете този имот?', function (r) {
                                if (r) {

                                    //getChecked[0].contract_id is the contract from which the plots was sold
                                    //contractData.id is the is of the sale contract
                                    TF.Rpc.Plots.PlotsSalesContractsDatagrid.deleteSalesContractPlotRelation(getChecked[0].gid, getChecked[0].id, getChecked[0].contract_id)
                                    .done(function (data) {
                                        jQuery('#sales-contract-plots-tables').datagrid('loadRpc');
                                        jQuery('#sales-contract-plots-tables').datagrid('uncheckAll');
                                    })
                                    .fail(function (errorObj) {
                                        jQuery.messager.alert('Грешка', errorObj.getMessage());
                                    });


                                }
                            });
                        } else {
                            jQuery.messager.alert('Грешка', 'Моля изберете имот, който искате да премахнете!');
                        }
                    } else {
                        jQuery.messager.alert('Грешка', 'Не е избран договор!');
                    }
                }
            }, {
                id: 'btnviewplotinfo',
                text: 'Информация',
                iconCls: 'icon-info',
                handler: function () {
                    var getChecked = jQuery('#sales-contract-plots-tables').datagrid('getChecked');
                    if (getChecked[0]) {

                        window.open("index.php?page=Plots.Home&plot_id=" + getChecked[0].gid, '_blank');
                    } else {
                        jQuery.messager.alert('Грешка', 'Моля изберете имот, за който да бъде показана информация.');
                    }
                }
            }
        ],
        onBeforeLoad: function () {
            jQuery('#sales-contract-plots-tables').datagrid('clearChecked');
        },
        onLoadSuccess: function () {
            var plotData = jQuery('#sales-contract-plots-tables').datagrid('getData');

            if (plotData['rows'][0]) {
                jQuery('#sales-contract-plots-tables').datagrid('selectRow', 0);
            } else {
                //initContractsOwnersGrid(0, contract_id, c_type);
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    //custom pager
    var pager = jQuery('#sales-contract-plots-tables').datagrid('getPager');
    pager.pagination({
        beforePageText: 'Стр.',
        displayMsg: 'От {from} до {to} от {total} записа'
    });
}

function initSalesContractsPlotsAddGrid(contract_id, start_date, farming_id) {
    var contractAddPlotsDatagrid = jQuery('#sales-contracts-plots-add-tables');
    var isDatagridBound = contractAddPlotsDatagrid.data().hasOwnProperty('datagrid');

    if (isDatagridBound) {
        contractAddPlotsDatagrid.datagrid({
        rpcParams: [{
            contract_id: contract_id,
			start_date: start_date,
			farming_id: farming_id
        }]
        });
        return;
    }

    contractAddPlotsDatagrid.datagrid({
        iconCls: 'icon-plots',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 10,
        fit: true,
        border: false,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?sales-contracts-rpc=sales-contracts-add-plots-datagrid',
        rpcParams: [{
            contract_id: contract_id,
            start_date: start_date,
			farming_id: farming_id
        }],
        sortName: 'gid',
        sortOrder: 'asc',
        idField: 'id',
        rowStyler: function (index, row) {
            var style = [];
            if (row.is_edited == true) {
                style.push('color: #aaa');
            }

            return style.join(';');
        },
        frozenColumns: [[
                {
                    field: 'ck',
                    checkbox: true
                }
            ]],
        columns: [[
                {
                    field: 'land',
                    title: '<b>Землище</b>',
                    sortable: true,
                    width: 150
                }, {
                    field: 'kad_ident',
                    title: '<b>Идентификатор</b>',
                    sortable: true,
                    width: 150
                }, {
                    field: 'category',
                    title: '<b>Категория</b>',
                    sortable: true,
                    width: 150
                }, {
                    field: 'area_type',
                    title: '<b>НТП</b>',
                    sortable: true,
                    width: 150
                }, {
                    field: 'used_area',
                    title: '<b>Изп. площ<br/>(дка)</b>',
                    align: 'center',
                    sortable: true,
                    width: 150
                }, {
                    field: 'c_num',
                    title: '<b>Номер на<br> договор за<br> собственост</b>',
                    align: 'center',
                    sortable: true,
                    width: 150
                }, {
                    field: 'contract_area',
                    title: '<b>Площ по<br/>договор (дка)</b>',
                    align: 'center',
                    sortable: true,
                    width: 150
                }, {
                    field: 'contract_area_for_sale',
                    title: '<b>Площ за<br/>продаване<br> (дка)</b>',
                    align: 'center',
                    sortable: true,
                    width: 150,
                    editor: {
                        type: 'numberbox',
                        options: {
                            precision: 3
                        }
                    }
                }, {
                    field: 'sublease_contract_name',
                    title: '<b>Договори за<br> преотдаване</b>',
                    align: 'center',
                    sortable: true,
                    width: 140
                }, {
                    field: 'price_procurement',
                    title: '<b>Стойност на<br> придобиване<br>(лв.)</b>',
                    align: 'center',
                    sortable: true,
                    width: 140
                }, {
                    field: 'price_sum',
                    title: '<b>Сума<br> (лв.)</b>',
                    align: 'center',
                    sortable: true,
                    width: 100,
                    editor: {
                        type: 'numberbox',
                        options: {
                            precision: 2
                        }
                    }
                }
            ]],
        pagination: true,
        rownumbers: true,
        toolbar: [{
                id: 'btnaddcontractplotrelation',
                text: 'Добавяне',
                iconCls: 'icon-add',
                handler: function () {
                    var getChecked = jQuery('#sales-contracts-plots-add-tables').datagrid('getChecked');
                    if (getChecked[0])
                    {
                        var plot_ids = [];
                        var is_selected_historical_plot = false;

                        for (var i = 0; i < getChecked.length; i++)
                        {
                            index = jQuery('#sales-contracts-plots-add-tables').datagrid('getRowIndex', getChecked[i]);
                            jQuery('#sales-contracts-plots-add-tables').datagrid('endEdit', index);

                            if (parseFloat(getChecked[i].contract_area) < parseFloat(getChecked[i].contract_area_for_sale)) {
                                jQuery.messager.alert('Грешка', 'Площта за продажба не може да е по-голяма от площта по договор.', 'error');
                                jQuery('#sales-contracts-plots-add-tables').datagrid('beginEdit', index);
                                return;
                            }else{
                                jQuery('#sales-contracts-plots-add-tables').datagrid('beginEdit', index);
                            }

                            plot_ids[i] = {
                                'gid': getChecked[i].gid,
                                'cp_rel': getChecked[i].id,
                                'start_date': start_date
                            };

                            if(getChecked[i].is_edited == true && is_selected_historical_plot == false) {
                                is_selected_historical_plot = true;
                            }
                        }

                        if (is_selected_historical_plot == true) {
                            jQuery.messager.confirm('Потвърждение', 'Избраните имоти включват исторически имоти,'+
                            ' които не са част от актуалната КВС/КК. Желаете ли да продължите?', function (r) {
                                if (r) {
                                    jQuery('#price-per-acre-checked').val('');

                                    jQuery('#win-save-plots-add').window('open');

                                    initSalesContractsSubleasedPlotsGrid(plot_ids);

                                    jQuery('#win-plots-add').window('close');
                                }
                            });
                        }else
                        {
                            jQuery('#price-per-acre-checked').val('');

                            jQuery('#win-save-plots-add').window('open');

                            initSalesContractsSubleasedPlotsGrid(plot_ids);

                            jQuery('#win-plots-add').window('close');
                        }
                    } else
                    {
                        jQuery.messager.alert('Грешка', 'Моля изберете парцели, който да бъдат добавени към договора.');
                    }
                }
            }, {
                id: 'btnaddcontractplotfilter',
                text: 'Филтриране',
                iconCls: 'icon-filter',
                handler: function () {

                    resetAddPlotGridFilterFields();

                    jQuery('#win-add-plots-filter').window('open');
                }
            }, {
                id: 'btnaddcontractplotfilterclear',
                text: 'Покажи всички',
                iconCls: 'icon-clear-filter',
                handler: function () {
                    clearAddPlotGridFilter();
                }
            }],
        onBeforeLoad: function () {
            jQuery('#sales-contracts-plots-add-tables').datagrid('clearChecked');

        },
        onSelect: function (index) {
            jQuery('#sales-contracts-plots-add-tables').datagrid('beginEdit', index);
        },
        onUnselect: function (index) {
            jQuery('#sales-contracts-plots-add-tables').datagrid('cancelEdit', index);
        },
        onCheckAll: function (rows) {
            var index;
            for(var i=0; i<rows.length; i++)
            {
                index = jQuery(this).datagrid('getRowIndex', rows[i]);
                jQuery(this).datagrid('beginEdit', index);
            }
        },
        onUncheckAll: function (rows) {
            var index;
            for(var i=0; i<rows.length; i++)
            {
                index = jQuery(this).datagrid('getRowIndex', rows[i]);
                jQuery(this).datagrid('cancelEdit', index);
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

jQuery(function () {

    jQuery('#search-add-plot-grid').bind('click', filterAddPlotGrid);

    jQuery('#close-win-add-plots-filter').bind('click', closeAddPlotGrid);

    jQuery('#btn-save-plot-add-salescontract').bind('click', savePlotAddSalesContract);

    jQuery('#btn-close-plot-add-salescontract').bind('click', closePlotAddSalesContract);

});

function savePlotAddSalesContract() {

    var getChecked = jQuery('#sales-contracts-plots-add-tables').datagrid('getChecked');
    if (getChecked[0])
    {
        var price_per_acre_checked = jQuery('#price-per-acre-checked').val();
        var getSelected = jQuery('#salescontracts-tree').tree('getSelected');
        var contract_id = getSelected.id;

        if (isNaN(price_per_acre_checked) || price_per_acre_checked < 0) {
            jQuery.messager.alert('Грешка','Моля въведете положителна стойност в поле "Лева на декар"','warning');
            return false;
        };

        const requests = [];
        for (var i = 0; i < getChecked.length; i++)
        {
            var index = jQuery('#sales-contracts-plots-add-tables').datagrid('getRowIndex', getChecked[i]);
            jQuery('#sales-contracts-plots-add-tables').datagrid('endEdit', index);

            var plot = new Object();
            plot.plot_id = getChecked[i].gid;
            plot.contract_area = getChecked[i].contract_area;
            plot.contract_area_for_sale = getChecked[i].contract_area_for_sale;
            plot.document_area = getChecked[i].document_area;
            plot.price_per_acre = price_per_acre_checked;
            plot.price_sum = getChecked[i].price_sum;
            plot.sales_contract_id = contract_id;
            plot.contract_id = getChecked[i].contract_id;
            plot.sublease_contract_id = getChecked[i].sublease_contract_id;

            requests.push(TF.Rpc.Plots.PlotsSalesContractsDatagrid.addSalesContractPlotRelation(plot));
        }

        Promise.all(requests).then(function () {
            // Refresh plots grid after all reques for adding plots are finished
            jQuery('#sales-contract-plots-tables').datagrid('loadRpc');
        })

        jQuery('#win-save-plots-add').window('close');
        jQuery('#win-plots-add').window('close');

        jQuery('#sales-contracts-plots-add-tables').datagrid('uncheckAll');
        jQuery('#sales-contracts-plots-add-tables').datagrid('unselectAll');
    }
}

function closePlotAddSalesContract() {
    jQuery('#win-save-plots-add').window('close');
}

function closeAddPlotGrid() {
    jQuery('#win-add-plots-filter').window('close');
}

function filterAddPlotGrid() {

    var contractSelected = jQuery('#salescontracts-tree').tree('getSelected');

    jQuery('#sales-contracts-plots-add-tables').datagrid({
        rpcParams: [{
            type:'add',
            contract_id: contractSelected.id,
            farming_id: contractSelected.attributes.farming_id,
            start_date: contractSelected.attributes.contract_start_date,
            ekate: jQuery('#search-plot-ekate').combobox('getValue'),
            masiv: jQuery('#search-plot-masiv').val(),
            number: jQuery('#search-plot-number').val(),
            category: jQuery('#search-category').combobox('getValues'),
            ntp: jQuery('#search-area-type').combobox('getValues'),
            contract_number: jQuery('#search-contract-number').val(),
            with_hypothec: jQuery('#with-hypothec').combobox('getValue'),
            with_sublease: jQuery('#with-sublease').combobox('getValue'),
        }],
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    jQuery('#win-add-plots-filter').window('close');
}

function clearAddPlotGridFilter() {

    var contractSelected = jQuery('#salescontracts-tree').tree('getSelected');

    resetAddPlotGridFilterFields();

    jQuery('#sales-contracts-plots-add-tables').datagrid({
        rpcParams: [{
            contract_id: contractSelected.id,
            farming_id: contractSelected.attributes.farming_id,
            start_date: contractSelected.attributes.contract_start_date
        }],
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function resetAddPlotGridFilterFields() {
    jQuery('#search-plot-ekate').combobox('reset');
    jQuery('#search-plot-masiv').val('');
    jQuery('#search-plot-number').val('');
    jQuery('#search-category').combobox('reset');
    jQuery('#search-area-type').combobox('reset');
    jQuery('#search-contract-number').val('');
    jQuery('#with-hypothec').combobox('reset');
    jQuery('#with-sublease').combobox('reset');
}
