function initSalesContractsReportGrid() {
    jQuery('#report-salescontracts-tables').datagrid({
        iconCls: 'icon-edit-geometry',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 10,
        fit: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?sales-contracts-rpc=report-sales-contracts-grid',
        idField: '',
        sortName: 'id',
        border: false,
        sortOrder: 'desc',
        rpcParams: [{

        }],
        columns: [[{
					field: 'land',
					title: '<b>Землище</b>',
					sortable: false,
					width: 100
				}, {
					field: 'kad_ident',
					title: '<b>Идентификатор</b>',
					sortable: false,
					width: 100
				}, {
					field: 'contract_area',
					title: '<b>Площ по дог.</b>',
					sortable: false,
					width: 100
				}, {
					field: 'contract_area_for_sale',
					title: '<b>Продадена<br> площ</b>',
					sortable: false,
					width: 100
				}, {
					field: 'c_num',
					title: '<b>Договор за<br> покупка</b>',
					sortable: false,
					width: 100
				}, {
					field: 'c_num_sale',
					title: '<b>Договор за<br> продажба</b>',
					sortable: false,
					width: 100
				}, {
					field: 'price_per_acre_buy',
					title: '<b>Цена на дка<br> за придобиване</b>',
					sortable: false,
					width: 100
				}, {
					field: 'price_procurement',
					title: '<b>Обща цена за<br> придобиване</b>',
					sortable: false,
					width: 100
				}, {
					field: 'price_per_acre',
					title: '<b>Цена на дка<br> за продажба</b>',
					sortable: false,
					width: 100
				}, {
					field: 'price_sum',
					title: '<b>Обща цена за<br> продажба</b>',
					sortable: false,
					width: 100
				}, {
					field: 'difference',
					title: '<b>Разлика</b>',
					sortable: false,
					width: 100,
					formatter: function (value, row, index) {
                        if (value < 0) {
                            return '<span style="color:red">'+value+'</span>';
                        }
                        else {
                        	return '<span style="color:green">'+value+'</span>';
                        }
                    }
				}]],
        pagination: true,
        rownumbers: true,
		toolbar: [{
			id: 'btnprintreport',
			text: 'Отпечатай',
			iconCls: 'icon-print',
			handler: function() {

				printReportSalesContracts();
			}
		},{
			id: 'btn-export-xls-report',
			text: 'Експорт(xls)',
			iconCls: 'icon-csv',
			handler: function() {

				exportToExcelReport();
			}
		}, {
			id: 'btnreportfilter',
			text: 'Филтър',
			iconCls: 'icon-filter',
			handler: function() {
				jQuery('#win-report-filter').window('open');
			}
		}, {
			id: 'btnreportclearfilter',
			text: 'Покажи всички',
			iconCls: 'icon-clear-filter',
			handler: function() {
				clearReportGridFilter();
			}
		}],
        onBeforeLoad: function() {
            jQuery('#report-salescontracts-tables').datagrid('clearChecked');
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function exportToExcelReport()
{
    var winDownload = jQuery('#win-download').window({
    });

    var data = jQuery('#report-salescontracts-tables').datagrid('getData');

    if (data.rows.length == 0) {
        jQuery.messager.alert('Грешка', 'Не може да експортнете празна справка.', 'error');
    }
    else {
        var gridOptions = jQuery('#report-salescontracts-tables').datagrid('options');
        var downloadFile = jQuery('#btn-download-file');
        var params = gridOptions.rpcParams[0];

        var sort = gridOptions.sortName;
        var order = gridOptions.sortOrder;

        TF.Rpc.SalesContracts.ReportSalesContractsGrid.exportToExcelReportSalesContractsData(params, null, null, sort, order)
        .done(function (dataObj){

            winDownload.window('open');
            var path = dataObj.file_path;
            _fileName = dataObj.file_name;
            downloadFile.attr("href", path);
        })
        .fail(function (errorObj) {});
    }
}

function printReportSalesContracts() {
  var data = jQuery('#report-salescontracts-tables').datagrid('getData');
  var options = jQuery('#report-salescontracts-tables').datagrid('options');

  if (data.length == 0) {
    jQuery.messager.alert('Грешка', 'Не може да отпечатате празна справка.');
  }
  else {
		var filterObj = {
			ekate: jQuery('#search-report-ekate').combobox('getValue'),
			masiv: jQuery('#search-report-masiv').val(),
			number: jQuery('#search-report-number').val(),
			c_num: jQuery('#search-report-c-num').val(),
			c_num_sale: jQuery('#search-report-c-num-sale').val()
		}
		var sort = options.sortName;
		var order = options.sortOrder;

	  	TF.Rpc.SalesContracts.ReportSalesContractsGrid.read(filterObj, null, null, sort, order).done(function (data) {

			var gridData = data.rows;
			var footerData = data.footer;
			var filteredEkateName = data.filteredEkateName;

			var  html = '<style>@page{size: landscape;}body,table{font-family: Arial;font-size: 10px;}</style>' +
			'<h2 align="center">Справка - Договори за продажба</h2>';


			html += '<b>Филтър:</b><br/>';
			html += 'EKATTE: <b>' + filteredEkateName + '</b><br/>';
			html += 'Масив: <b>' + filterObj.masiv + '</b><br/>';
			html += 'Имот: <b>' + filterObj.number + '</b><br/>';
			html += 'Договор за покупка: <b>' + filterObj.c_num + '</b><br/>';
			html += 'Договор за продажба: <b>' + filterObj.c_num_sale + '</b><br/><br/>';

			var header = {};
			var columns = options.columns[0];
			for(var i=0; i<columns.length; i++)
			{
			    header[columns[i].field] = columns[i].title;
			}

			var rows = gridData;
			rows.push(footerData[0]);
			html += Templates.table(header, rows);

			jQuery('body').append('<iframe id="printf" name="printf"></iframe>');
			var newWin = window.frames['printf'];
			newWin.document.write('<body onload=window.print()>'+html+'</body>');
			newWin.document.close();
			setTimeout(function () {
				jQuery('#printf').remove();
			}, 1000);
		})
		.fail(function (errorObj) {
            jQuery.messager.alert('Грешка', errorObj.getMessage());
		});

    return false;
  }
}

jQuery(function() {

	jQuery('#show-report-salescontracts').bind('click', showReportSalesContracts);

	jQuery('#search-report-grid').bind('click', filterReportGrid);

	jQuery('#close-win-report-filter').bind('click', closeReportGrid);

});

function closeReportGrid() {
	jQuery('#win-report-filter').window('close');
}

function filterReportGrid() {

	jQuery('#report-salescontracts-tables').datagrid({
		rpcParams: [{
			ekate: jQuery('#search-report-ekate').combobox('getValue'),
			masiv: jQuery('#search-report-masiv').val(),
			number: jQuery('#search-report-number').val(),
			c_num: jQuery('#search-report-c-num').val(),
			c_num_sale: jQuery('#search-report-c-num-sale').val()
		}],
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
	jQuery('#win-report-filter').window('close');
}

function clearReportGridFilter() {

	jQuery('#search-report-ekate').combobox('reset');
	jQuery('#search-report-masiv').val('');
	jQuery('#search-report-number').val('');
	jQuery('#search-report-c-num').val('');
	jQuery('#search-report-c-num-sale').val('');
	jQuery('#report-salescontracts-tables').datagrid({
		rpcParams: [{
		}],
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function showReportSalesContracts() {

	initSalesContractsReportGrid();

	jQuery('#win-report-salescontracts-panel').window('open');
}
