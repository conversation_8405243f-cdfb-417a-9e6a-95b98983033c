var editContractID;

function initSalesContractsTree(pageNumber, filterObj) {
	editContractID = undefined;
	var page_number = 1;

	if (pageNumber != undefined)
		page_number = pageNumber;

	var contractsTree = jQuery('#salescontracts-tree');

	contractsTree.tree({
		url: 'index.php?sales-contracts-rpc=sales-contracts-tree',
		animate: false,
		lines: true,
		sort: 'id',
		order: 'desc',
		rpcParams: [filterObj],
		page: page_number,
		onSelect: function(node) {
			initSalesContractInfo(node.attributes);
			initFilesGrid(node.id);
			initSalesContractsPlotsGrid(node.id);
			initSalesContractsBuyersGrid(node.id);
		},
		onLoadSuccess: function() {
			var roots = jQuery('#salescontracts-tree').tree('getRoots');
			var total = 0;
			var limit = 10;

			if (roots.length) {
				if (editContractID != undefined) {
					var node = jQuery('#salescontracts-tree').tree('find', editContractID);
					jQuery('#salescontracts-tree').tree('select', node.target);
				}
				else {
					jQuery('#salescontracts-tree').tree('select', roots[0].target);
				}
				total = roots[0]['attributes']['pagination']['total'];
				limit = roots[0]['attributes']['pagination']['limit'];
			}
            else
            {
                initSalesContractInfo();
               	initFilesGrid(0);
                initSalesContractsPlotsGrid(0);
                initSalesContractsBuyersGrid(0);

                jQuery('#contract-info-layout').layout('remove', 'south');
                jQuery.messager.alert('Внимание', 'Не са открити записи', 'warning');
            }

			//init pagination with total contract elements
			initSalesContractsPagination(total, limit);

			//endLoading();
		},
		onBeforeLoad: function(node, param) {
			//sending filter parameters with POST method
			if (filterObj) {
				param.c_num = filterObj.c_num;
				param.farming = filterObj.farming;

				param.date_from = filterObj.date_from;
				param.date_to = filterObj.date_to;
				param.due_date_from = filterObj.due_date_from;
				param.due_date_to = filterObj.due_date_to;
			}
			param.page_number = page_number;
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

var date = new Date();
var _todayDate = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();

jQuery(function() {

	//Open Form
	jQuery('#add-salescontract').bind('click', openAddSalesContract);

	//Close Form
	jQuery('#btn-close-salescontract').bind('click', closeAddSalesContract);

	//Create
	jQuery('#btn-add-salescontract').bind('click', createSalesContract);

	//Load
	jQuery('#load-salescontract').bind('click', loadSalesContract);

	//Update
	jQuery('#btn-update-salescontract').bind('click', updateSalesContract);

	//Delete
	jQuery('#delete-salescontract').bind('click', deleteSalesContract);

	//Open Filter
	jQuery('#filter-salescontracts').bind('click', openFilterSalesContract);

	//Execute Filter
	jQuery('#btn-search-filter-salescontracts').bind('click', executeFilterSalesContract);

	//Close Filter
	jQuery('#btn-cancel-filter-salescontracts').bind('click', closeFilterSalesContract);

	//Reset Filter
	jQuery('#cancel-filter-salescontracts').bind('click', resetFilterSalesContract);

});

function resetFilterSalesContract() {

	resetFilterSalesContractFields();

	//reinit tree with query params
	initSalesContractsTree(1);
	jQuery('#win-salescontract-filter').window('close');

	return false;
}

function resetFilterSalesContractFields() {

	jQuery('#search-cnum').val('');
 	jQuery('#search-farming').combobox('reset');
 	jQuery('#search-farming-year').combobox('reset');
 	jQuery('#search-date-to').datebox('reset');
 	jQuery('#search-due-date-from').datebox('reset');
 	jQuery('#search-due-date-to').datebox('reset');
 	jQuery('#search-kad-ident').val('');
	jQuery('#search-ekatte').combobox('reset');
	jQuery('#search-masiv').val('');
	jQuery('#search-number').val('');
	jQuery('#search-buyer-name').val('');
	jQuery('#search-cnum-complete-match').prop('checked', true);
}

function executeFilterSalesContract() {

	var obj = new Object();
	obj.c_num = jQuery('#search-cnum').val();
	obj.farming = jQuery('#search-farming').combobox('getValues');
	obj.date_from = jQuery('#search-date-from').datebox('getValue');
	obj.date_to = jQuery('#search-date-to').datebox('getValue');
	obj.due_date_from = jQuery('#search-due-date-from').datebox('getValue');
	obj.due_date_to = jQuery('#search-due-date-to').datebox('getValue');
	obj.kad_ident = jQuery('#search-kad-ident').val();
	obj.ekatte = jQuery('#search-ekatte').combobox('getValues');
	obj.masiv = jQuery('#search-masiv').val();
	obj.number = jQuery('#search-number').val();
	obj.buyer_name = jQuery('#search-buyer-name').val();

    obj.c_num_complete_match = false;
    if (jQuery('#search-cnum-complete-match').is(':checked'))
    {
        obj.c_num_complete_match = true;
    }

	//reinit tree with query params
	initSalesContractsTree(1, obj);
	jQuery('#win-salescontract-filter').window('close');
}

function openFilterSalesContract() {

	resetFilterSalesContractFields();

	jQuery('#win-salescontract-filter').window('open');
}

function closeFilterSalesContract() {
	jQuery('#win-salescontract-filter').window('close');
}

function deleteSalesContract() {

	var getSelected = jQuery('#salescontracts-tree').tree('getSelected');
	if (getSelected) {
		jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да изтриете този договор?', function(r) {
			if (r) {

				var contractId = getSelected.id;

				TF.Rpc.SalesContracts.SalesContractsTree.deleteSalesContract(contractId).done(function (dataObj) {

					jQuery('#salescontracts-tree').tree('reload');
				})
			    .fail(function (errorObj) {
                    jQuery.messager.alert('Грешка', errorObj.getMessage());
			    });
			}
		});
	} else {
		jQuery.messager.alert('Грешка', 'Моля изберете договор.');
	}
}

function updateSalesContract() {

	var getSelected = jQuery('#salescontracts-tree').tree('getSelected');
	if (getSelected) {

		if(!validateSubmitInfoUpdate())
		{
			return;
		}

		updateSalesContractRpc();

	} else {
		jQuery.messager.alert('Грешка', 'Моля изберете договор.');
	}
}

function setFormFieldsValues(data)
{
	jQuery('#contract-number').val(data.c_num);
	jQuery('#contract-start-date').datebox({
		value: data.start_date
	});
	jQuery('#contract-date').datebox({
		value: data.c_date
	});
	jQuery('#contract-farming').combobox('setValue', data.farming_id);
	jQuery('#tom').val(data.tom);
	jQuery('#na-num').val(data.na_num);
	jQuery('#delo').val(data.delo);
	jQuery('#court').val(data.court);
	jQuery('#contract-comment').val(data.comment);
}

function loadSalesContract() {

	var getSelected = jQuery('#salescontracts-tree').tree('getSelected');
	if (getSelected) {

		var contractId = getSelected.id;

		TF.Rpc.SalesContracts.SalesContractsTree.loadSalesContract(contractId).done(function (dataObj) {

			//set window title
			jQuery('#win-salescontract-add').window({title:'Редактиране на Договор за продажба'});

			resetFormFieldsValues(false);

			initRequiredFieldsSalesContracts();

			//set values to the form fields
			setFormFieldsValues(dataObj);

			jQuery('#win-salescontract-add').window('open');

			jQuery('#btn-update-salescontract').show();
			jQuery('#btn-add-salescontract').hide();
		})
	    .fail(function (errorObj) {
            jQuery.messager.alert('Грешка', errorObj.getMessage());
	    });

	} else {
		jQuery.messager.alert('Грешка', 'Моля изберете Договор.');
	}
}

function checkForExistence() {

	var c_num = jQuery('#contract-number').val();
	var farming = jQuery('#contract-farming').combobox('getValue');

	TF.Rpc.SalesContracts.SalesContractsTree.checkForExistence(c_num, farming).done(function (dataObj) {

		if (dataObj) {
			jQuery.messager.confirm('Потвърждение', 'Вече съществува договор с такъв номер! Сигурни ли сте, че искате да продължите?', function(r) {
				if (r) {
					addSalesContractRpc();
				}
			});
		}
		else {
			addSalesContractRpc();
		}
    })
    .fail(function (errorObj) {
        jQuery.messager.alert('Грешка', errorObj.getMessage());
    });
}

function getFormValuesSalesContract() {

	var params = {
		c_num: jQuery('#contract-number').val(),
		contract_start_date: jQuery('#contract-start-date').datebox('getValue'),
		contract_date: jQuery('#contract-date').datebox('getValue'),
		farming: jQuery('#contract-farming').combobox('getValue'),
		tom: jQuery('#tom').val(),
		na_num: jQuery('#na-num').val(),
		delo: jQuery('#delo').val(),
		court: jQuery('#court').val(),
		contract_comment: jQuery('#contract-comment').val(),
	};

	return params;
}

function validateSubmitInfoCreate() {

	if (jQuery('#contract-number').val() != ''
			&& jQuery('#contract-date').datebox('getValue') != ''
			&& jQuery('#contract-start-date').datebox('getValue') != ''
			&& jQuery('#contract-farming').combobox('getValue') != '') {

		checkForExistence();

		return true;
	} else {
		jQuery.messager.alert('Грешка', 'Моля попълнете всички задължителни полета.');

		return false;
	}
}

function validateSubmitInfoUpdate() {

	if (jQuery('#contract-number').val() != ''
			&& jQuery('#contract-date').datebox('getValue') != ''
			&& jQuery('#contract-start-date').datebox('getValue') != ''
			&& jQuery('#contract-farming').combobox('getValue') != '') {

		return true;
	} else {
		jQuery.messager.alert('Грешка', 'Моля попълнете всички задължителни полета.');

		return false;
	}
}

function updateSalesContractRpc() {

	//form values
	var params = getFormValuesSalesContract();

	var getSelected = jQuery('#salescontracts-tree').tree('getSelected');
	var contractId = getSelected.id;

	TF.Rpc.SalesContracts.SalesContractsTree.updateSalesContract(contractId ,params).done(function (dataObj) {

		jQuery('#win-salescontract-add').window('close');
		jQuery('#salescontracts-tree').tree('reload');
	})
    .fail(function (errorObj) {
        jQuery.messager.alert('Грешка', errorObj.getMessage());
    });
}

function addSalesContractRpc() {

	//form values
	var params = getFormValuesSalesContract();

	TF.Rpc.SalesContracts.SalesContractsTree.addSalesContract(params).done(function (dataObj) {

		jQuery('#win-salescontract-add').window('close');
		jQuery('#salescontracts-tree').tree('reload');
	})
    .fail(function (errorObj) {
        jQuery.messager.alert('Грешка', errorObj.getMessage());
    });
}

function createSalesContract() {

	validateSubmitInfoCreate();
}

function closeAddSalesContract() {
	jQuery('#win-salescontract-add').window('close');
}

function openAddSalesContract() {
	resetFormFieldsValues(true);
	initRequiredFieldsSalesContracts();
	jQuery('#win-salescontract-add').window({title:'Добавяне на Договор за продажба'});

	jQuery('#win-salescontract-add').window('open');
	jQuery('#btn-add-salescontract').show();
	jQuery('#btn-update-salescontract').hide();
}

function initRequiredFieldsSalesContracts() {

	jQuery('#contract-number').validatebox({
		required: true,
		missingMessage: 'Моля въведете номер на договор.'
	});

	jQuery('#contract-start-date').datebox({
		required: true,
		missingMessage: 'Моля изберете дата на влизане в сила за договор.',
		value: _todayDate
	});

	jQuery('#contract-date').datebox({
		required: true,
		missingMessage: 'Моля изберете дата на договор.',
		value: _todayDate
	});
}

function resetFormFieldsValues(selected)
{
	jQuery('#contract-number').val('');

	jQuery('#contract-start-date').datebox({
		requred: true,
		missingMessage: 'Моля въведете дата.',
		value: _todayDate
	});

	jQuery('#contract-date').datebox({
		required: true,
		missingMessage: 'Моля изберете дата на договор.',
		value: _todayDate
	});

	var farmingComboboxData = ComboboxData.FarmingCombobox;
	let userFarmingPermissions = ComboboxData.UserFarmingPermissions;
	let selectedOptionId = 0;
	var newFarmingComboboxData = [];

	farmingComboboxData.forEach(function (el) {
		if (el.id !== "") {
			el.selected = false;
			let hasPermission = Object.values(userFarmingPermissions).includes(el.id);
			if(!hasPermission) {
				el.disabled = true;
			} else {
				if (selectedOptionId == 0) {
					selectedOptionId = el.id;
					el.selected = true;
				}
			}

			newFarmingComboboxData.push(el);
		}
	});

	jQuery('#contract-farming').combobox({
		data: newFarmingComboboxData,
		valueField: 'id',
		textField: 'name',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

	jQuery('#tom').val('');
	jQuery('#na-num').val('');
	jQuery('#delo').val('');
	jQuery('#court').val('');
	jQuery('#contract-comment').val('');
}




function initSalesContractsPagination(total, limit) {
	jQuery('#salescontracts-tree-pagination').pagination({
		showPageList: false,
		showRefresh: false,
		displayMsg: '',
		total: total,
		pageSize: 30,
		onSelectPage: function(pageNumber, pageSize) {
			//cover case of filtered results
			var obj = new Object();

			obj.c_num = jQuery('#search-cnum').val();
			obj.farming = jQuery('#search-farming').combobox('getValues');
			obj.date_from = jQuery('#search-date-from').datebox('getValue');
			obj.date_to = jQuery('#search-date-to').datebox('getValue');
			obj.due_date_from = jQuery('#search-due-date-from').datebox('getValue');
			obj.due_date_to = jQuery('#search-due-date-to').datebox('getValue');
			obj.kad_ident = jQuery('#search-kad-ident').val();
			obj.ekatte = jQuery('#search-ekatte').combobox('getValues');
			obj.masiv = jQuery('#search-masiv').val();
			obj.number = jQuery('#search-number').val();
			obj.buyer_name = jQuery('#search-buyer-name').val();

			initSalesContractsTree(pageNumber, obj);
		}
	});
}

function initSalesContractInfo(contractData) {
    if(!contractData)
    {
        jQuery('#info-contract-type').html('');
        jQuery('#info-contract-number').html('');
        jQuery('#info-contract-date').html('');
        jQuery('#info-start-date').html('');
        jQuery('#info-due-date').html('');
        jQuery('#info-farming').html('');
        jQuery('#info-na-num').html('');
        jQuery('#info-tom').html('');
        jQuery('#info-delo').html('');
        jQuery('#info-court').html('');
        jQuery('#info-comment').html('');
        jQuery('#info-renta').html('');
        jQuery('#info-renta-nat-type').html('');
        jQuery('#info-renta-nat').html('');
        jQuery('#info-sv-num').html('');
        jQuery('#info-sv-date').html('');
        jQuery('#info-pd-date').html('');
        jQuery('#info-active-text').html('');
        jQuery('.js-renta-additional-row').remove();

        return false;
    }


	jQuery('#info-contract-type').html(contractData.nm_usage_rights);
	jQuery('#info-contract-number').html(contractData.c_num);
	jQuery('#info-contract-date').html(contractData.c_date);
	jQuery('#info-start-date').html(contractData.start_date);
	jQuery('#info-due-date').html(contractData.due_date);
	jQuery('#info-farming').html(contractData.farming);
	jQuery('#info-na-num').html(contractData.na_num);
	jQuery('#info-tom').html(contractData.tom);
	jQuery('#info-delo').html(contractData.delo);
	jQuery('#info-court').html(contractData.court);
	jQuery('#info-comment').html(contractData.comment);
	jQuery('#info-renta').html(contractData.renta_text);
	jQuery('#info-renta-nat-type').html(contractData.renta_nat_type);
	jQuery('#info-renta-nat').html(contractData.renta_nat_text);
	jQuery('#info-sv-num').html(contractData.sv_num);
	jQuery('#info-sv-date').html(contractData.sv_date);
	var paymonth = '';
	if (months[contractData.paymonth] != undefined) {
		paymonth = months[contractData.paymonth]['label'];
	}
	jQuery('#info-pd-date').html(contractData.payday + ' ' + paymonth);
	jQuery('#info-active-text').html(contractData.active_text);

	var rowHTML;
	jQuery('.js-renta-additional-row').remove();

}
