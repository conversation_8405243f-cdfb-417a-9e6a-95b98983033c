var buyerEditIndex = undefined;

function initAddBuyersGrid(sales_contract_id) {
    var selectedContract = jQuery('#salescontracts-tree').tree('getSelected');
    sales_contract_id = 0;

    if (selectedContract != null) {
        sales_contract_id = selectedContract.id
    };

    jQuery('#buyers-add-table').datagrid({
        title: 'Купувачи',
        iconCls: 'icon-users',
        rownumbers: true,
        singleSelect: true,
        onSelectCheck: true,
        onCheckSelect: true,
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        fit: true,
        fitColumns: true,
        showFooter: true,
        pagination: true,
        height: 240,
        idField: 'id',
        sortName: 'id',
        sortOrder: 'desc',
        url: 'index.php?sales-contracts-rpc=buyers-sales-contracts-grid',
        rpcParams:[sales_contract_id],
        columns:[[
            {
                field:'name',
                title:'Име',
                sortable:true,
                width: 200,
                editor: {
                            type: 'validatebox',
                            options: {
                                required: true,
                                missingMessage: 'Въведете име на купувача',
                                tipPosition: 'left'
                            }
                        }
            },{
                field:'contacts',
                title:'Контакти',
                sortable:true,
                width: 200,
                editor: {
                            type: 'validatebox',
                            options: {
                                required: true,
                                missingMessage: 'Въведете контакти за купувача',
                                tipPosition: 'left'
                            }
                        }
            }
        ]],
        toolbar: [
            {
                id: 'btnadd',
                text: 'Добавяне',
                iconCls: 'icon-add',
                handler: function() {
                    var getSelected = jQuery('#salescontracts-tree').tree('getSelected');

                    if (getSelected) {
                        if (buyerEditingClosed()) {
                            buyerEditIndex = 0;
                            jQuery('#buyers-add-table').datagrid('insertRow',
                            {
                                index: buyerEditIndex,
                                row: {
                                    id: false,
                                    sales_contract_id: sales_contract_id
                                }
                            });
                            jQuery('#buyers-add-table').datagrid('beginEdit', buyerEditIndex).datagrid('selectRow', buyerEditIndex);
                        }
                    } else
                    {
                        jQuery.messager.alert('Грешка', 'Моля изберете собственик.');
                    }
                }
            }, {
                id: 'btnedit',
                text: 'Редактиране',
                iconCls: 'icon-edit',
                handler: function() {
                    var getChecked = jQuery('#buyers-add-table').datagrid('getChecked');

                    if (getChecked[0]) {
                        var index = jQuery('#buyers-add-table').datagrid('getRowIndex', getChecked[0]);

                        if (buyerEditIndex != index) {
                            if (buyerEditingClosed()) {
                                jQuery('#buyers-add-table').datagrid('beginEdit', index);
                                buyerEditIndex = index;
                            } else {
                                jQuery('#buyers-add-table').datagrid('selectRow', buyerEditIndex);
                            }
                        }
                    } else
                    {
                        jQuery.messager.alert('Грешка', 'Моля изберете купувач.');
                    }
                }
            }, {
                id: 'btndel',
                text: 'Изтриване',
                iconCls: 'icon-remove',
                handler: function() {
                    var getChecked = jQuery('#buyers-add-table').datagrid('getChecked');

                    if (getChecked[0]) {
                        if(getChecked[0].id){
                            jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да изтриете този купувач?', function(r) {
                                if (r) {

                                    TF.Rpc.SalesContracts.BuyersGrid.deleteBuyer(getChecked[0].id).done(function (dataObj) {
                                    })
                                    .fail(function (errorObj) {
                                        jQuery.messager.alert('Грешка', errorObj.getMessage());
                                    });

                                    jQuery('#buyers-add-table').datagrid('reload');
                                }
                            });
                            buyerEditIndex = undefined;
                        }
                    } else
                    {
                        jQuery.messager.alert('Грешка', 'Моля изберете купувач.');
                    }
                }
            }, {
                id: 'btnsave',
                text: 'Запази',
                iconCls: 'icon-save',
                handler: function() {
                    buyerEditingClosed();
                }
            }, {
                id: 'btncancel',
                text: 'Отмени',
                iconCls: 'icon-undo',
                handler: function() {
                    rejectChanges();
                }
            }
        ],
        onCheck: function(index, rowData) {
            if (buyerEditIndex != index) {
                buyerEditingClosed();
            }
        },
        onBeforeLoad: function() {
            jQuery('#buyers-add-table').datagrid('uncheckAll');
            jQuery('#buyers-add-table').datagrid('unselectAll');

            buyerEditIndex = undefined;
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function buyerEditingClosed() {
    if (buyerEditIndex == undefined) {
        return true;
    }
    if (jQuery('#buyers-add-table').datagrid('validateRow', buyerEditIndex))
    {
        jQuery('#buyers-add-table').datagrid('endEdit', buyerEditIndex);
        jQuery('#buyers-add-table').datagrid('acceptChanges');

        var rows = jQuery('#buyers-add-table').datagrid('getRows');
        var obj = rows[buyerEditIndex];

        if(obj.id)
        {
        	TF.Rpc.SalesContracts.BuyersGrid.updateBuyer(obj.id, obj.name, obj.contacts).done(function (dataObj) {
	        })
	        .fail(function (errorObj) {
                jQuery.messager.alert('Грешка', errorObj.getMessage());
	        });
        }else{

        	TF.Rpc.SalesContracts.BuyersGrid.addBuyer(obj.name, obj.contacts).done(function (dataObj) {
	        })
	        .fail(function (errorObj) {
                jQuery.messager.alert('Грешка', errorObj.getMessage());
	        });
        }




        buyerEditIndex = undefined;

        jQuery('#buyers-add-table').datagrid('reload');

        return true;
    } else {
        return false;
    }
}

function rejectChanges() {
    jQuery('#buyers-add-table').datagrid('rejectChanges');
    jQuery('#buyers-add-table').datagrid('uncheckAll');
    jQuery('#buyers-add-table').datagrid('unselectAll');

    buyerEditIndex = undefined;
}

jQuery(function() {

	jQuery('#add-buyer').bind('click', addBuyer);

	jQuery('#delete-buyer').bind('click', deleteBuyerRel);

	jQuery('#add-buyer-sales-contracts').bind('click', addBuyerSalesContracts);

	jQuery('#close-buyer-sales-contracts').bind('click', closeBuyerSalesContracts);
});

function addBuyer() {
	var selectedContract = jQuery('#salescontracts-tree').tree('getSelected');
	var sales_contract_id = selectedContract.id;
    jQuery('#win-add-buyer').window('open');
	initAddBuyersGrid(sales_contract_id);
}

function deleteBuyerRel() {

	var getChecked = jQuery('#buyers-sales-contracts-relation-grid').datagrid('getChecked');

    if (getChecked[0]) {
        if(getChecked[0].id){
            jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да премахнете този купувач от договора за продажба?', function(r) {
                if (r) {

                	var relation_id = getChecked[0].id;

                    TF.Rpc.SalesContracts.BuyersSalesContractsRelationGrid.deleteBuyerRel(relation_id).done(function (dataObj) {
                    })
                    .fail(function (errorObj) {
                        jQuery.messager.alert('Грешка', errorObj.getMessage());
                    });

					jQuery('#buyers-sales-contracts-relation-grid').datagrid('reload');
                }
            });
        }
    } else
    {
        jQuery.messager.alert('Грешка', 'Моля изберете купувач.');
    }
}

function addBuyerSalesContracts() {

	var getChecked = jQuery('#buyers-add-table').datagrid('getChecked');

    if (getChecked[0]) {
        if(getChecked[0].id){
            jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да добавите този купувач към договора?', function(r) {
                if (r) {

                	var getSelected = jQuery('#salescontracts-tree').tree('getSelected');
                	var sales_contract_id = getSelected.id;
                	var buyer_id = getChecked[0].id;

                    TF.Rpc.SalesContracts.BuyersGrid.addRelationBuyerSalesContract(buyer_id, sales_contract_id).done(function (dataObj) {
                        jQuery('#buyers-sales-contracts-relation-grid').datagrid('reload');
                    })
                    .fail(function (errorObj) {
                        jQuery.messager.alert('Грешка', errorObj.getMessage());
                    });

                    closeBuyerSalesContracts();
                }
            });
            buyerEditIndex = undefined;
        }
    } else
    {
        jQuery.messager.alert('Грешка', 'Моля изберете купувач.');
    }

}

function closeBuyerSalesContracts() {
	jQuery('#win-add-buyer').window('close');
}


function initSalesContractsBuyersGrid(sales_contract_id) {

    var salesContractsBuyersGrid = jQuery('#buyers-sales-contracts-relation-grid');
    var isDatagridBound = salesContractsBuyersGrid.data().hasOwnProperty('datagrid');

    if (isDatagridBound) {
        if (sales_contract_id !== 0) {
            salesContractsBuyersGrid.datagrid({
                url: 'index.php?sales-contracts-rpc=buyers-sales-contracts-relation-grid',
                rpcParams: [sales_contract_id],
            });
        } else {
            salesContractsBuyersGrid.datagrid({data: {rows: [], total: 0}});
        }
        return;
    }

	salesContractsBuyersGrid.datagrid({
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		fit: true,
		fitColumns: true,
		showFooter: false,
		idField: 'id',
		pageSize: 10,
		sortName: 'id',
		sortOrder: 'desc',
		singleSelect: true,
		pagination: true,
		columns: [[
				{
					field: 'name',
					title: '<b>Име</b>',
					sortable: false,
					width: 100
				}, {
					field: 'contacts',
					title: '<b>Контакти</b>',
					sortable: false,
					width: 100
				}
			]],
		rownumbers: true,
		onBeforeLoad: function() {
			salesContractsBuyersGrid.datagrid('clearChecked');
		},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

