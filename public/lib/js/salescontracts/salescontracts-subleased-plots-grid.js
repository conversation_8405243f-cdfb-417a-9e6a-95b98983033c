var APPLY_TO_ALL = 1;
var APPLY_TO_FILTERED = 2;
var APPLY_TO_SELECTED = 3;

function initSalesContractsSubleasedPlotsGrid(plot_ids)
{
	var SalesContractsSubleasedPlotsGrid = jQuery('#salescontracts-subleased-plots-grid');

	SalesContractsSubleasedPlotsGrid.datagrid({
		iconCls: 'icon-plots',
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		pageSize: 10,
		fit: false,
		border: true,
		fitColumns: true,
		showFooter: true,
		url: 'index.php?sales-contracts-rpc=sales-contracts-subleased-plots-grid',
		rpcParams: [plot_ids,{}],
		sortName: 'gid',
		sortOrder: 'asc',
		idField: 'gid',
		columns: [[
				{
					field: 'land',
					title: '<b>Землище</b>',
					sortable: true,
					width: 150
				}, {
					field: 'kad_ident',
					title: '<b>Идентификатор</b>',
					sortable: false,
					width: 150
				}, {
					field: 'area_type',
					title: '<b>НТП</b>',
					sortable: false,
					width: 150
				}, {
					field: 'c_num',
					title: '<b>Договор за<br> преотдаване</b>',
					align: 'center',
					sortable: true,
					width: 150
				}, {
					field: 'due_date',
					title: '<b>Действа до</b>',
					align: 'center',
					sortable: false,
					width: 150
				}, {
					field: 'due_date_to',
					title: '<b>Дата за<br> изключване</b>',
					align: 'center',
					sortable: false,
					width: 150
				}
			]],
		pagination: true,
		rownumbers: true,
		toolbar: [{
				id: 'btn-filter',
				text: 'Филтриране',
				iconCls: 'icon-filter',
				handler: function() {

					resetSalesContractsSubleasedPlotsFilter();

					jQuery('#win-filter-salescontracts-subleased-plots').window('open');
				}
			}, {
				id: 'btn-clear-filter',
				text: 'Покажи всички',
				iconCls: 'icon-clear-filter',
				handler: function() {
					clearSalesContractsSubleasedPlotsFilter();
				}
			}, {
				id: 'btn-info',
				text: 'Информация',
				iconCls: 'icon-info',
				handler: function() {
					var getChecked = SalesContractsSubleasedPlotsGrid.datagrid('getChecked');
                    if (getChecked[0]) {

						window.open("index.php?page=Plots.Home&plot_id=" + getChecked[0].gid, '_blank');
                    } else {
                        jQuery.messager.alert('Грешка', 'Моля изберете имот, за който да бъде показана информация.');
                    }
				}
			}
			],
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

}

function clearSalesContractsSubleasedPlotsFilter() {

	var SalesContractsSubleasedPlotsGrid = jQuery('#salescontracts-subleased-plots-grid');

	var getChecked = jQuery('#sales-contracts-plots-add-tables').datagrid('getChecked');
	var plot_ids = [];
	if (getChecked[0])
	{
		for (var i = 0; i < getChecked.length; i++)
		{
			plot_ids[i] = {
                'gid': getChecked[i].gid,
                'cp_rel': getChecked[i].id
            };
		}
	}

	resetSalesContractsSubleasedPlotsFilter();

	SalesContractsSubleasedPlotsGrid.datagrid({
		rpcParams: [plot_ids,{}],
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function resetSalesContractsSubleasedPlotsFilter() {

	jQuery('#search-subleased-plot-ekate').combobox('reset');
	jQuery('#search-subleased-plot-masiv').val('');
	jQuery('#search-subleased-plot-number').val('');
	jQuery('#search-area-type-subleased').combobox('reset');
	jQuery('#search-subleased-due-date-from').datebox('reset');
	jQuery('#search-subleased-due-date-to').datebox('reset');
}

function closeSalesContractsSubleasedPlot() {
	jQuery('#win-filter-salescontracts-subleased-plots').window('close');
}

function filterSalesContractsSubleasedPlot() {

	var SalesContractsSubleasedPlotsGrid = jQuery('#salescontracts-subleased-plots-grid');

	var getChecked = jQuery('#sales-contracts-plots-add-tables').datagrid('getChecked');
	var plot_ids = [];
	if (getChecked[0])
	{
		for (var i = 0; i < getChecked.length; i++)
		{
			plot_ids[i] = {
                'gid': getChecked[i].gid,
                'cp_rel': getChecked[i].id
            };
		}
	}

	SalesContractsSubleasedPlotsGrid.datagrid({
		rpcParams: [plot_ids, {
			ekate: jQuery('#search-subleased-plot-ekate').combobox('getValue'),
			masiv: jQuery('#search-subleased-plot-masiv').val(),
			number: jQuery('#search-subleased-plot-number').val(),
			ntp: jQuery('#search-area-type-subleased').combobox('getValues'),
			due_date_from: jQuery('#search-subleased-due-date-from').datebox('getValue'),
			due_date_to: jQuery('#search-subleased-due-date-to').datebox('getValue'),
		}],
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
	jQuery('#win-filter-salescontracts-subleased-plots').window('close');
}

jQuery(function() {

	jQuery('#btn-search-subleased-plot-filter').bind('click', filterSalesContractsSubleasedPlot);

	jQuery('#btn-close-subleased-plot-filter').bind('click', closeSalesContractsSubleasedPlot);

	jQuery('#btn-save-subleased-plot-date').bind('click', saveSubleasedPlotDate);

	jQuery('#btn-close-subleased-plot-date').bind('click', closeSubleasedPlotDate);

});

function saveSubleasedPlotDate() {

	var date_option = jQuery('#salescontracts-date-option').combobox('getValue');

	var apply_to = APPLY_TO_ALL;

	if(jQuery('#apply-to-all').is(":checked")) {
        apply_to = APPLY_TO_ALL;
    }

	if(jQuery('#apply-to-filtered').is(":checked")) {
        apply_to = APPLY_TO_FILTERED;
    }

    if(jQuery('#apply-to-selected').is(":checked")) {
        apply_to = APPLY_TO_SELECTED;
    }

    var start_date_salescontract = jQuery('#salescontracts-tree').tree('getSelected').attributes.start_date;

    var plotIds = [];
    var	requestObj = {
    	start_date_salescontract: start_date_salescontract,
    	date_option: date_option,
    	apply_to: apply_to
    };

    if(apply_to == APPLY_TO_SELECTED) {

    	var SalesContractsSubleasedPlotsGrid = jQuery('#salescontracts-subleased-plots-grid');
    	var getChecked = SalesContractsSubleasedPlotsGrid.datagrid('getChecked');
    	if (getChecked[0]) {

			var idField = 'gid';

			jQuery.grep(getChecked, function(e){
				plotIds.push(e[idField]);
			});

			requestObj.plotIds = plotIds;
			makeSaveSubleasedPlotDateRequest(requestObj);

        } else {
            jQuery.messager.alert('Грешка', 'Няма селектирани имоти!');
			closeSubleasedPlotDate();
        }

    }else{

    	makeSaveSubleasedPlotDateRequest(requestObj);
    }

}

function makeSaveSubleasedPlotDateRequest(requestObj) {

	TF.Rpc.SalesContracts.SalesContractsSubleasedPlotsGrid.saveSubleasedPlotDate(requestObj).done(function (dataObj) {

		closeSubleasedPlotDate();

		jQuery('#salescontracts-subleased-plots-grid').datagrid('loadRpc');
	})
    .fail(function (errorObj) {
        jQuery.messager.alert('Грешка', errorObj.getMessage());
    });
}

function closeSubleasedPlotDate() {
	jQuery('#win-salescontracts-choose-date').window('close');
}
