var ComboboxData;

jQuery(function () {
    initSearchOnEnter();
    setUserLastLogin();

    TF.Rpc.Common.CombinedComboboxData.read()
        .done(function (data) {
            ComboboxData = data;
            initSearchFields();
        })
        .fail(function (error) {
            jQuery.messager.alert('Грешка', error.getMessage(), 'warning');
        });
    //retrieve GET parameters
    var GET = {};
    location.search.substr(1).split("&").forEach(function (item) {GET[decodeURIComponent(item.split("=")[0])] = decodeURIComponent(item.split("=")[1])});

    //change URL without refresh if it's posible
    if (history && history.replaceState){
        history.replaceState(null, null, 'index.php?page=' + GET.page);
    }

    initSalesContractsTree(1, GET);
    initFilesGrid(0);
    initSalesContractsPlotsGrid(0);
    initSalesContractsBuyersGrid(0);

    jQuery('#btn-print-salescontract').bind('click', function() {
        var getSelected = jQuery('#salescontracts-tree').tree('getSelected');

        if (getSelected) {

            //contracts_templates.initPrintTemplatesGrid(getSelected.id);
            contracts_templates.initPrintTemplatesSalesContracts(getSelected.id);
            jQuery('#win-contracts-templates').window('open');

        } else {
            jQuery.messager.alert('Грешка', 'Моля изберете договор!');
        }
        return false;
    });

});

function initCooperatorInfo(cooperator){
    if(!cooperator)
    {
        jQuery('#info-cooperator-name').html('');
        jQuery('#info-cooperator-surname').html('');
        jQuery('#info-cooperator-lastname').html('');
        jQuery('#info-cooperator-egn').html('');
        jQuery('#info-cooperator-lk-nomer').html('');
        jQuery('#info-cooperator-lk-izdavane').html('');
        jQuery('#info-cooperator-dead').html('');
        jQuery('#info-cooperator-book-number').html('');
        jQuery('#info-cooperator-partida-number').html('');
        jQuery('#info-cooperator-date-entry').html('');
        jQuery('#info-cooperator-paid-in-capital').html('');
        jQuery('#info-cooperator-current-capital').html('');
        jQuery('#info-cooperator-excluded').html('');
        jQuery('#info-cooperator-date-excluded').html('');

        return false;
    }

    if(cooperator.excluded){
        excluded_text = "Да";
    }else{
        excluded_text = "Не";
    }

    if(cooperator.is_dead){
        is_dead_text = "Да";
    }else{
        is_dead_text = "Не";
    }

    jQuery('#info-cooperator-name').html(cooperator.name);
    jQuery('#info-cooperator-surname').html(cooperator.surname);
    jQuery('#info-cooperator-lastname').html(cooperator.lastname);
    jQuery('#info-cooperator-egn').html(cooperator.egn);
    jQuery('#info-cooperator-lk-nomer').html(cooperator.lk_nomer);
    jQuery('#info-cooperator-lk-izdavane').html(cooperator.lk_izdavane);
    jQuery('#info-cooperator-dead').html(is_dead_text);
    jQuery('#info-cooperator-book-number').html(cooperator.book_number);
    jQuery('#info-cooperator-partida-number').html(cooperator.partida_number);
    jQuery('#info-cooperator-date-entry').html(cooperator.dateЕntry);
    jQuery('#info-cooperator-paid-in-capital').html(cooperator.paid_in_capital + ' лв.');
    jQuery('#info-cooperator-current-capital').html(cooperator.current_capital + ' лв.');
    jQuery('#info-cooperator-excluded').html(excluded_text);
    jQuery('#info-cooperator-date-excluded').html(cooperator.dateЕxcluded);
}

function initSearchFields(){
    var farmingComboboxData        = ComboboxData.FarmingCombobox,
        ekateComboboxData          = ComboboxData.EkateCombobox,
        plotNTPComboboxData        = ComboboxData.PlotNTPCombobox,
        categoryComboboxData       = ComboboxData.PlotCategoryCombobox;

    jQuery('#search-farming').combobox({
        data: farmingComboboxData,
        editable: false,
        valueField: 'id',
        textField: 'name',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-ekatte').combobox({
        data: ekateComboboxData,
        editable: false,
        valueField: 'ekate',
        textField: 'text',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-date-from').datebox();
    jQuery('#search-date-to').datebox();
    jQuery('#search-due-date-from').datebox();
    jQuery('#search-due-date-to').datebox();
    jQuery('#search-subleased-due-date-from').datebox();
    jQuery('#search-subleased-due-date-to').datebox();

    jQuery('#search-area-type').combobox({
        data: plotNTPComboboxData,
        editable: false,
        valueField: 'id',
        textField: 'name',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        onLoadSuccess: function() {

            jQuery('#search-area-type-subleased').combobox({
                data: jQuery('#search-area-type').combobox('getData'),
                valueField: 'id',
                textField: 'name',
                filter: function(q, row){
                    var opts = jQuery(this).combobox('options');
                    var text = row[opts.textField].toLowerCase();
                    var value = row[opts.valueField];
                    var find = q.toLowerCase();
                    if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
                    {
                        return true;
                    }
                }
            });
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-category').combobox({
        data: categoryComboboxData,
        valueField: 'id',
        textField: 'name',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        onLoadSuccess: function() {

        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-plot-ekate').combobox({
        data: ekateComboboxData,
        valueField: 'ekate',
        textField: 'text',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        onLoadSuccess: function() {


            jQuery('#search-subleased-plot-ekate').combobox({
                data: jQuery('#search-plot-ekate').combobox('getData'),
                valueField: 'ekate',
                textField: 'text',
                filter: function(q, row){
                    var opts = jQuery(this).combobox('options');
                    var text = row[opts.textField].toLowerCase();
                    var value = row[opts.valueField];
                    var find = q.toLowerCase();
                    if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
                    {
                        return true;
                    }
                }
            });

            jQuery('#search-report-ekate').combobox({
                data: jQuery('#search-plot-ekate').combobox('getData'),
                valueField: 'ekate',
                textField: 'text',
                filter: function(q, row){
                    var opts = jQuery(this).combobox('options');
                    var text = row[opts.textField].toLowerCase();
                    var value = row[opts.valueField];
                    var find = q.toLowerCase();
                    if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
                    {
                        return true;
                    }
                }
            });
        },
        filter: function(q, row){
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
            {
                return true;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initSearchOnEnter() {
    jQuery("#win-salescontract-filter").off("keyup").on("keyup", function (event) {
		// Listen for enter key
        if (13 !== event.keyCode) {
			return;
		}
		jQuery("#btn-search-filter-salescontracts").click()
    });
}
