function initFilesGrid(contract_id) {
    'use strict';
    var salesContractsFilesGrid = jQuery('#contract-files-tables'),
        isDatagridBound = salesContractsFilesGrid.data().hasOwnProperty('datagrid');

    if (isDatagridBound) {
        if (contract_id !== 0) {
            salesContractsFilesGrid.datagrid({
                url: 'index.php?sales-contracts-rpc=sales-contracts-files-maingrid',
                rpcParams: [contract_id],
            });
        } else {
            salesContractsFilesGrid.datagrid({data: {rows: [], total: 0}});
        }
        return;
    }

    salesContractsFilesGrid.datagrid({
        iconCls: 'icon-files',
        title: 'Архив файлове',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        fit: true,
        fitColumns: true,
        showFooter: false,
        idField: 'id',
        pageSize: 10,
        sortName: 'id',
        sortOrder: 'asc',
        singleSelect: true,
        frozenColumns: [[
            {
                field: 'ck',
                checkbox: true
            }
        ]],
        columns: [[
            {
                field: 'filename',
                title: '<b>Файл</b>',
                sortable: false,
                width: 100
            }, {
                field: 'date',
                title: '<b>Дата</b>',
                sortable: false,
                width: 100
            }
        ]],
        rownumbers: true,
        toolbar: [{
            id: 'btnaddcontractfile',
            text: 'Добавяне',
            iconCls: 'icon-add',
            handler: function () {
                contract_id = jQuery('#salescontracts-tree').tree('getSelected').id;
                if (contract_id) {
                    initFileUploads(contract_id);
                    jQuery('#win-add-file').window('open');
                } else {
                    jQuery.messager.alert('Грешка', 'Не е избран договор.');
                }
            }
        }, {
            id: 'btndeletecontractfile',
            text: 'Изтриване',
            iconCls: 'icon-remove',
            handler: function () {
                var getChecked = salesContractsFilesGrid.datagrid('getChecked');

                if (getChecked[0]) {

                    jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да премахнете този запис?', function(r) {
                        if (r) {

                            TF.Rpc.SalesContracts.SalesContractsFilesGrid.delete(getChecked[0].id)
                            .done(function (data) {
                                salesContractsFilesGrid.datagrid('reload');
                                salesContractsFilesGrid.datagrid('uncheckAll');
                                salesContractsFilesGrid.datagrid('unselectAll');
                            })
                            .fail(function (data) {
                                RpcErrorHandler.show(data);
                            });
                        }
                    });
                } else {
                    jQuery.messager.alert('Грешка', 'Моля изберете договори, който да бъдат премахнати.');
                }
            }
        }, {
            id: 'btndownloadcontractfile',
            text: 'Изтегляне',
            iconCls: 'icon-export',
            handler: function() {
                var getChecked = salesContractsFilesGrid.datagrid('getChecked');

                if (getChecked[0]) {

                    var file_id = getChecked[0]['id'];

                    TF.Rpc.SalesContracts.SalesContractsExports.downloadAttached(file_id)
                    .done(function (data) {
                        createContractDownloadAttachmentVariables();
                        winDownloadContractAttachment.window('open');
                        downloadFileContractAttachment.attr("href", data);
                    })
                    .fail(function (errorObj) {
                        jQuery.messager.alert('Грешка', errorObj.getMessage());
                    });
                } else {
                    jQuery.messager.alert('Грешка', 'Моля изберете файл, който искате да изтеглите.');
                }
            }
        }],
        onBeforeLoad: function() {
            salesContractsFilesGrid.datagrid('clearChecked');
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initFileUploads(contract_id)
{
    const url  = "index.php?json=contract-upload"; 

    jQuery("#uploader").pluploadQueue({
        // General settings
        runtimes: 'gears,html5,flash,silverlight,browserplus',
        url: url,
        multipart_params : {
            "contract_id" : contract_id,
            "type": "sales"
        },
        max_file_size: '100mb',
        unique_names: true,
        // Flash settings
        flash_swf_url: 'lib/js_external/fileupload/plupload.flash.swf'
    });

    var uploader = jQuery('#uploader').pluploadQueue();
    uploader.bind('UploadComplete', function() {
        jQuery('#win-add-file').window('close');
        jQuery('#contract-files-tables').datagrid('reload');
    });
}

function createContractDownloadAttachmentVariables() {
    winDownloadContractAttachment = jQuery('#win-download');
    downloadFileContractAttachment = jQuery('#btn-download-file');
    cancelDownloadFileContractAttachment = jQuery('#btn-download-file-close');
}
