Namespace('TF.Rpc.Common');

function initPlotsContractsGrid(plot_id) {
    jQuery('#contract-info-tables').datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        iconCls: 'icon-contract',
        title: 'Договори',
        pageSize: 10,
        fit: true,
        singleSelect: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?zplots-rpc=kvs-contracts-datagrid',
        rpcParams: [{
            type: 'view',
            plot_id: plot_id
        }],
        sortName: '',
        sortOrder: 'asc',
        idField: '',
        frozenColumns: [[
                {
                    field: 'ck',
                    checkbox: true
                },
            ]],
        columns: [[{
                    field: 'c_num',
                    title: '<b>Номер</b>',
                    sortable: true,
                    width: 170
                }, {
                    field: 'c_date',
                    title: '<b>Дата</b>',
                    sortable: true,
                    width: 170
                }, {
                    field: 'nm_usage_rights',
                    title: '<b>Тип</b>',
                    sortable: true,
                    width: 170
                }, {
                    field: 'start_date',
                    title: '<b>Влизане в сила</b>',
                    sortable: true,
                    width: 170
                }, {
                    field: 'farming',
                    title: '<b>Стопанство</b>',
                    sortable: true,
                    width: 200
                }, {
                    field: 'due_date',
                    title: '<b>Крайна дата</b>',
                    sortable: true,
                    width: 200
                }
            ]],
        pagination: true,
        rownumbers: true,
        toolbar: [{
                id: 'btnownerinfo',
                text: 'Информация',
                iconCls: 'icon-info',
                handler: function() {
                    var getChecked = jQuery('#contract-info-tables').datagrid('getChecked');
                    if (getChecked[0]) {
                        dispatchContractInfoRequest(getChecked[0]['id']);
                    } else {
                        jQuery.messager.alert('Грешка', 'Моля изберете собственик, за който да бъде показана информация.');
                    }
                }
            }],
        onLoadSuccess: function() {
            var gridData = jQuery('#contract-info-tables').datagrid('getData');
            if (gridData['rows'][0]) {
                jQuery('#contract-info-tables').datagrid('selectRow', 0);
                initPlotsOwnersGrid(plot_id, gridData['rows'][0]['id']);
            } else {
                initPlotsOwnersGrid(plot_id, 0);
            }
        },
        onSelect: function(rowIndex, rowData) {
            initPlotsOwnersGrid(plot_id, rowData['id']);
        },
        onBeforeLoad: function() {
            jQuery('#contract-info-tables').datagrid('clearChecked');
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initPlotsOwnersGrid(plot_id, contract_id) {
    jQuery('#owner-info-tables').datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        iconCls: 'icon-owners',
        title: 'Собственици',
        pageSize: 10,
        fit: true,
        singleSelect: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?zplots-rpc=kvs-owners-datagrid',
        rpcParams: [{
            type:'view',
            contract_id: contract_id,
            plot_id: plot_id
        }],
        sortName: '',
        sortOrder: 'asc',
        idField: '',
        frozenColumns: [[
                {
                    field: 'ck',
                    checkbox: true
                },
            ]],
        columns: [[
                {
                    field: 'rep_names',
                    title: '<b>Имена на представител</b>',
                    sortable: true,
                    width: 150
                }, {
                    field: 'owner_names',
                    title: '<b>Имена на физическо лице</b>',
                    sortable: true,
                    width: 150
                }, {
                    field: 'company_name',
                    title: '<b>Фирма</b>',
                    sortable: true,
                    width: 150
                }, {
                    field: 'rat_ownage',
                    title: '<b>Собственост(%)</b>',
                    sortable: true,
                    width: 75
                }
            ]],
        pagination: true,
        rownumbers: true,
        toolbar: [{
                id: 'btnownerinfo',
                text: 'Информация',
                iconCls: 'icon-info',
                handler: function() {
                    var getChecked = jQuery('#owner-info-tables').datagrid('getChecked');
                    if (getChecked[0]) {
                        dispatchOwnerInfoRequest(getChecked[0]['id']);
                    } else {
                        jQuery.messager.alert('Грешка', 'Моля изберете собственик, за който да бъде показана информация.');
                    }
                }
            }],
        onBeforeLoad: function() {
            jQuery('#owner-info-tables').datagrid('clearChecked');
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initKVSInfoControls() {
    jQuery('#map-select-kvs').bind('click', function() {
        if (!isLoading) {
            var options = jQuery('#map-select-kvs').linkbutton('options');
            if (!options.selected) {
                jQuery('#map-select-kvs').linkbutton('select');
                if (!options.disabled) {
                    mapKVS.events.register('click', mapKVS, KVSPropertyWindowFunction);
                }
            } else {
                jQuery('#map-select-kvs').linkbutton('unselect');
                mapKVS.events.unregister('click', mapKVS, KVSPropertyWindowFunction);
            }
        }
    });

    jQuery('#kvs-tree').tree({
        checkbox: true,
        onlyLeafCheck: true,
        onCheck: function(node, checked) {

            if (node.id)
            {
                for (var i = 0; i < layerKvsArray.length; i++)
                {
                    if ((layerKvsArray[i].name == 'layer_kvs') && (node.id == 'kvs4'))
                    {
                        layerKvsArray[i].setVisibility(checked);
                        break;
                    }

                    if ((layerKvsArray[i].name == 'topic_kvs_zp_layer') && (node.id == 'kvs3'))
                    {
                        layerKvsArray[i].setVisibility(checked);
                        break;
                    }

                    if ((layerKvsArray[i].name == 'topic_kvs_layer') && (node.id == 'kvs2' || node.id == 'kvs1'))
                    {
                        layerKvsArray[i].setVisibility(checked);
                        break;
                    }
                }
            }
            else
            {
                for (var i = 0; i < layerKvsArray.length; i++)
                {
                    layerKvsArray[i].setVisibility(checked);
                }
            }
        }
    });

}

function KVSPropertyWindowFunction(e) {
    bounds = mapKVS.getExtent();

    x1 = parseInt(e.xy.x);
    y1 = parseInt(e.xy.y);

    var requestObj = new Object();
    requestObj = {
        bbox: bounds.toBBOX(),
        x: x1,
        y: y1,
        width: mapKVS.size.w,
        height: mapKVS.size.h,
    };

    TF.Rpc.ZPlots.KVSZpMapInfo.read(requestObj)
    .done(function (data) {
        initPlotInfoFill(data);
        initPlotsContractsGrid(data);
        jQuery('#win-kvs-info').window('open');
    })
    .fail(function (errorObj) {
        if (errorObj.is(TF.Rpc.ExceptionsList.MAP_EMPTY_AREA)) {
            jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.MAP_EMPTY_AREA.message);
        };
    });
}

function initPlotInfoFill(plot_id) {
    var requestObj = new Object();
    requestObj = {
        plot_id: plot_id
    };
    TF.Rpc.Common.InfoRequest.plotInfo(requestObj)
    .done(function (data) {
        fillPlotInfoFields(data);
    });
}

function fillPlotInfoFields(data) {
    jQuery('#info-ident').html(data['kad_ident']);
    jQuery('#info-category').html(data['category']);
    jQuery('#info-ntp').html(data['area_type']);
    jQuery('#info-ekatte').html(data['ekate']);
    jQuery('#info-masiv').html(data['masiv']);
    jQuery('#info-imot').html(data['number']);

    jQuery('#info-include').hide();
    jQuery('#info-participate').hide();
    jQuery('#info-white-spots').hide();
    jQuery('#info-missing-wishes').hide();

    if (data['include'] == true) {
        jQuery('#info-include').show();
    }
    if (data['participate'] == true) {
        jQuery('#info-participate').show();
    }
    if (data['white_spots'] == true) {
        jQuery('#info-white-spots').show();
    }
    if (data['include'] == false && data['participate'] == false && data['white_spots'] == false) {
        jQuery('#info-missing-wishes').show();
    }
}

function dispatchOwnerInfoRequest(owner_id) {

   var requestObj = new Object();
    requestObj = {
        id: owner_id
    };

    TF.Rpc.Common.InfoRequest.ownerInfo(requestObj)
    .done(function (data) {
        initOwnersInfoPanelFromResponse(data);
        jQuery('#owner-info-panel').window('open');
    });
}

function initOwnersInfoPanelFromResponse(responseText) {
    var owner_owner_type = responseText.owner_type || '-';
    var owner_company_name = responseText.company_name || '-';
    var owner_eik = responseText.eik || '-';
    var owner_owner_type = responseText.owner_type || '-';
    var owner_name = responseText.name || '-';
    var owner_surname = responseText.surname || '-';
    var owner_lastname = responseText.lastname || '-';
    var owner_egn = responseText.egn || '-';
    var owner_lk_nomer = responseText.lk_nomer || '-';
    var owner_lk_izdavane = responseText.lk_izdavane || '-';
    var owner_rep_name = responseText.rep_name || '-';
    var owner_rep_surname = responseText.rep_surname || '-';
    var owner_rep_lastname = responseText.rep_lastname || '-';
    var owner_rep_egn = responseText.rep_egn || '-';
    var owner_rep_lk_nomer = responseText.rep_lk_nomer || '-';
    var owner_rep_lk_izdavane = responseText.rep_lk_izdavane || '-';
    var owner_phone = responseText.phone || '-';
    var owner_fax = responseText.fax || '-';
    var owner_mobile = responseText.mobile || '-';
    var owner_email = responseText.email || '-';
    var owner_address = responseText.address || '-';
    if (responseText.owner_type_id == 0) {
        jQuery('#owner-info-panel').window('resize', {
            width: 420,
            height: 370
        });
        var html = '<fieldset style="border: 1px solid #000; padding: 5px 10px;">' +
                '<legend style="font-style: italic; font-weight: bold">Информация за собственик</legend>' +
                'Вид собственик: ' + owner_owner_type + '<br/>' +
                'Фирма: ' + owner_company_name + '<br/>' +
                'ЕИК: ' + owner_eik + '<br/>' +
                '</fieldset>';
    } else {
        jQuery('#owner-info-panel').window('resize', {
            width: 420,
            height: 405
        });
        var html = '<fieldset style="border: 1px solid #000; padding: 5px 10px;">' +
                '<legend style="font-style: italic; font-weight: bold">Информация за собственик</legend>' +
                'Вид собственик: ' + owner_owner_type + '<br/>' +
                'Имена: ' + owner_name + ' ' + owner_surname + ' ' + owner_lastname + '<br/>' +
                'ЕГН: ' + owner_egn + '<br/>' +
                'Номер на лична карта: ' + owner_lk_nomer + '<br/>' +
                'Издаване на ЛК: ' + owner_lk_izdavane + '<br/>' +
                '</fieldset>';
    }
    html += '<fieldset style="border: 1px solid #000; padding: 5px 10px; margin-top: 10px;">' +
            '<legend style="font-style: italic; font-weight: bold">Информация за представител</legend>' +
            'Имена: ' + owner_rep_name + ' ' + owner_rep_surname + ' ' + owner_rep_lastname + '<br/>' +
            'ЕГН: ' + owner_rep_egn + '<br/>' +
            'Номер на ЛК: ' + owner_rep_lk_nomer + '<br/>' +
            'Издаване на ЛК: ' + owner_rep_lk_izdavane + '<br/>' +
            '</fieldset>' + '<fieldset style="border: 1px solid #000; padding: 5px 10px; margin-top: 10px;">' +
            '<legend style="font-style: italic; font-weight: bold">Допълнителна информация за собственик</legend>' +
            'Телефон: ' + owner_phone + '<br/>' +
            'Факс: ' + owner_fax + '<br/>' +
            'Мобилен телефон: ' + owner_mobile + '<br/>' +
            'Емейл: ' + owner_email + '<br/>' +
            'Адрес: ' + owner_address +
            '</fieldset>';
    jQuery('#owner-info-panel').html(html);
}

function dispatchContractInfoRequest(contract_id) {

    var requestObj = new Object();
    requestObj = {
        id: contract_id
    };

    TF.Rpc.Common.InfoRequest.contractInfo(requestObj)
    .done(function (data) {
        initContractInfoPanelFromResponse(data);
        jQuery('#contract-info-panel').window('open');
    });
}

function initContractInfoPanelFromResponse(responseText) {
    var contract_c_type = responseText.c_type || '-';
    var contract_c_num = responseText.c_num || '-';
    var contract_c_date = responseText.c_date || '-';
    var contract_start_date = responseText.start_date || '-';
    var contract_due_date = responseText.due_date || '-';
    var contract_farming = responseText.farming || '-';
    var contract_ds_type = responseText.ds_type || '-';
    var contract_ds_num_date = responseText.ds_num_date || '-';
    var contract_sv_num = responseText.sv_num || '-';
    var contract_sv_date = responseText.sv_date || '-';
    var contract_renta = responseText.renta || '-';
    var contract_renta_nat_type = responseText.renta_nat_type || '-';
    var contract_renta_nat = responseText.renta_nat || '-';
    var contract_cont_name = responseText.cont_name || '-';
    var contract_cont_number = responseText.cont_number || '-';
    var contract_cont_land = responseText.cont_land || '-';
    var contract_cont_address = responseText.cont_address || '-';
    var html = '';
    html += '<fieldset style="border: 1px solid #000; padding: 5px 10px;">';
    html += '<legend style="font-style: italic; font-weight: bold">Основна информация</legend>';
    html += 'Тип: ' + contract_c_type + '</br>';
    html += 'Номер: ' + contract_c_num + '</br>';
    html += 'Сключване: ' + contract_c_date + '</br>';
    html += 'Влизане в сила: ' + contract_start_date + '</br>';
    html += 'Крайна дата: ' + contract_due_date + '</br>';
    html += 'Стопанство: ' + contract_farming + '</br>';
    html += '</fieldset>';

    html += '<fieldset style="border: 1px solid #000; padding: 5px 10px; margin-top: 5px;">';
    html += '<legend style="font-style: italic; font-weight: bold">Документ за собственост</legend>';
    html += 'Тип: ' + contract_ds_type + '</br>';
    html += 'Номер и дата: ' + contract_ds_num_date + '</br>';
    html += '</fieldset>';

    html += '<fieldset style="border: 1px solid #000; padding: 5px 10px; margin-top: 5px;">';
    html += '<legend style="font-style: italic; font-weight: bold">Служба по вписване</legend>';
    html += 'Номер на вписване: ' + contract_sv_num + '</br>';
    html += 'Номер на вписване: ' + contract_sv_date + '</br>';
    html += '</fieldset>';

    html += '<fieldset style="border: 1px solid #000; padding: 5px 10px; margin-top: 5px;">';
    html += '<legend style="font-style: italic; font-weight: bold">Рента</legend>';
    html += 'Сума: ' + contract_renta + '</br>';
    html += 'В натура - тип: ' + contract_renta_nat_type + '</br>';
    html += 'В натура - кол.: ' + contract_renta_nat + '</br>';
    html += '</fieldset>';

    html += '<fieldset style="border: 1px solid #000; padding: 5px 10px; margin-top: 5px; height: 110px;">';
    html += '<legend style="font-style: italic; font-weight: bold">Информация за контрагент</legend>';
    html += 'Име: ' + contract_cont_name + '</br>';
    html += 'Номер: ' + contract_cont_number + '</br>';
    html += 'Землище: ' + contract_cont_land + '</br>';
    html += 'Адрес: ' + contract_cont_address + '</br>';
    html += '</fieldset>';

    jQuery('#contract-info-panel').html(html);
}
