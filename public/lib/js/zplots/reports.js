function initReportGrid(groupBy, farming, year) {
    jQuery('#report-tables').datagrid({
        title: 'Обща площ по култури',
        iconCls: 'icon-measure-polygon',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        fit: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?zplots-rpc=zplots-report-grid',
        border: false,
        rpcParams: [{
            farming: farming,
            year: year,
            ekatte: jQuery('#zp-filter-ekate > input').combobox('getValue'),
            isak_prc_uin: jQuery('#zp-filter-isak > input').val(),
            culture: jQuery('#zp-filter-culture > input').combobox('getValue')
        }],
        columns: [
            [
                {
                    field: 'culture',
                    title: '<b>Култура</b>',
                    sortable: false,
                    width: 95
                }, {
                    field: 'area',
                    title: '<b>Площ(дка)</b>',
                    sortable: false,
                    width: 40
                }
            ]
        ],
        onBeforeLoad: function() {
            jQuery('#report-tables').datagrid('clearChecked');
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}
