function InitKVSGrid(farming, year) {

    var winDownload = jQuery('#win-download').window({
        onClose: onDownloadWindowClose
    });

    var downloadFile = jQuery('#btn-download-file');
    var cancelDownloadFile = jQuery('#btn-download-file-close');


    jQuery('#kvs-plots-tables').datagrid({
        rpcParams: [{
            farming: farming,
            year: year,
            ekatte: jQuery('#zp-filter-ekate > input').combobox('getValue'),
            isak_prc_uin: jQuery('#zp-filter-isak > input').val(),
            culture: jQuery('#zp-filter-culture > input').combobox('getValue')
        }],
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 20,
        fit: true,
        singleSelect: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?zplots-rpc=zp-kvs-report',
        sortName: 'gid',
        sortOrder: 'asc',
        border: false,
        columns: [
            [
                {
                    title: '<b>Информация за имот</b>',
                    colspan: 10
                }, {
                    field: 'area_intersect',
                    title: '<b>Припокриване<br/>(дка)</b>',
                    sortable: true,
                    width: 130,
                    rowspan: 2,
                    align: 'center'
                }, {
                    field: 'isak_prc_uin',
                    title: '<b>ИСАК номер</b>',
                    sortable: true,
                    width: 120,
                    rowspan: 2
                }],
            [{
                    field: 'ekate',
                    title: '<b>ЕКАТТЕ</b>',
                    sortable: true,
                    width: 70,
                    align: 'center'
                }, {
                    field: 'kad_ident',
                    title: '<b>Идентификатор</b>',
                    sortable: true,
                    width: 120
                }, {
                    field: 'masiv',
                    title: '<b>Масив</b>',
                    sortable: true,
                    width: 50,
                    align: 'center'
                }, {
                    field: 'number',
                    title: '<b>Имот</b>',
                    sortable: true,
                    width: 50,
                    align: 'center'
                }, {
                    field: 'category',
                    title: '<b>Категория</b>',
                    sortable: true,
                    width: 100,
                    align: 'center'
                }, {
                    field: 'area_type',
                    title: '<b>НТП</b>',
                    sortable: true,
                    width: 130
                }, {
                    field: 'has_contracts',
                    title: '<b>Има<br/>договори</b>',
                    sortable: true,
                    width: 70,
                    align: 'center'
                }, {
                    field: 'contract_name',
                    title: '<b>Номер на<br/>договора</b>',
                    sortable: true,
                    width: 255,
                    align: 'center'
                },{
                    field: 'area_kvs',
                    title: '<b>Обща<br/>площ(дка)</b>',
                    sortable: true,
                    width: 100,
                    align: 'center'
                }, {
                    field: 'contract_area',
                    title: '<b>Площ по<br/>договор(дка)</b>',
                    sortable: true,
                    width: 100,
                    align: 'center'
                }
            ]
        ],
        pagination: true,
        rownumbers: true,
        toolbar: [
            {
                id: 'btnshowkvsplots',
                text: 'Карта',
                iconCls: 'icon-map',
                handler: function() {
                    initKVSInfoControls();
                    showMapKVS();
                }
            }, {
                id: 'btndisplaykvsinfo',
                text: 'Подробна информация',
                iconCls: 'icon-info',
                handler: function() {
                    var getChecked = jQuery('#kvs-plots-tables').datagrid('getChecked');

                    if (getChecked[0]) {
                        fillPlotInfoFields(getChecked[0]);
                        initPlotsContractsGrid(getChecked[0]['gid']);
                        jQuery('#win-kvs-info').window('open');
                    } else {
                        jQuery.messager.alert('Грешка', 'Моля изберете имот, за който да бъде показана информация.');
                    }
                }
            }, {
                id: 'btnintersectexport',
                text: 'Експорт',
                iconCls: 'icon-export',
                handler: function() {
                    var ekatte = jQuery('#zp-filter-ekate > input').combobox('getValue');
                    var isak_prc_uin = jQuery('#zp-filter-isak > input').val();
                    var culture = jQuery('#zp-filter-culture > input').combobox('getValue');

                    var yearData = jQuery('#layers-tree').tree('getSelected');
                    var farmingData = jQuery('#layers-tree').tree('getParent', yearData.target);

                    var year = yearData.id;
                    var farming = farmingData.id;

                    var exportObj = new Object();
                    exportObj = {
                        farming: farming,
                        year: year,
                        isak_prc_uin: isak_prc_uin,
                        culture: culture,
                        ekatte: ekatte
                    };

                    TF.Rpc.ZPlots.ZPlotsExport.exportIntersectExcel(exportObj)
                    .done(function (data) {
                        winDownload.window('open');
                        _pathFile = data.path;
                        _fileName = data.name;
                        downloadFile.attr("href", _pathFile);
                    });
                }
            }],
        onBeforeLoad: function() {
            jQuery('#kvs-plots-tables').datagrid('clearChecked');
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function onDownloadWindowClose() {
   return;
}
