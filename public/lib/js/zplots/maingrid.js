function initDataGrid() {


    var winDownload = jQuery('#win-download').window({
        onClose: onDownloadWindowClose
    });

    var downloadFile = jQuery('#btn-download-file');
    var cancelDownloadFile = jQuery('#btn-download-file-close');

    var yearData = jQuery('#layers-tree').tree('getSelected');
    var farmingData = jQuery('#layers-tree').tree('getParent', yearData.target);

    var year = yearData.id;
    var farming = farmingData.id;

    jQuery('#zp-tables').datagrid({
        title: 'Парцели',
        iconCls: 'icon-edit-geometry',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 30,
        fit: true,
        singleSelect: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?zplots-rpc=zplots-maingrid',
        rpcParams: [{
            farming: farming,
            year: year
        }],
        sortName: 'isak_prc_uin',
        sortOrder: 'asc',
        columns: [
            [
                {
                    field: 'isak_prc_uin',
                    title: '<b>ИСАК номер</b>',
                    sortable: true,
                    width: 150
                }, {
                    field: 'ekatte',
                    title: '<b>ЕКАТТЕ</b>',
                    sortable: true,
                    width: 200
                }, {
                    field: 'mestnost',
                    title: '<b>Местност</b>',
                    sortable: true,
                    width: 200
                }, {
                    field: 'culture',
                    title: '<b>Култура</b>',
                    sortable: true,
                    width: 250
                }, {
                    field: 'obrabotki',
                    title: '<b>Обработки</b>',
                    sortable: true,
                    width: 100
                }, {
                    field: 'dobivi',
                    title: '<b>Добиви</b>',
                    sortable: true,
                    width: 100
                }, {
                    field: 'napoqvane',
                    title: '<b>Напояване</b>',
                    sortable: true,
                    width: 100
                }, {
                    field: 'polivki',
                    title: '<b>Поливки</b>',
                    sortable: true,
                    width: 100
                }, {
                    field: 'polzvatel',
                    title: '<b>Ползвател</b>',
                    sortable: true,
                    width: 200
                }, {
                    field: 'area_zp',
                    title: '<b>Площ (дка)</b>',
                    sortable: true,
                    width: 200
                }, {
                    field: 'area_name',
                    title: '<b>Име на парцела</b>',
                    sortable: true,
                    width: 200
                }
            ]
        ],
        pagination: true,
        rownumbers: true,
        toolbar: [
            {
                id: 'btneditzpplots',
                text: 'Редактиране',
                iconCls: 'icon-edit',
                handler: function() {
                    var getChecked = jQuery('#zp-tables').datagrid('getChecked');
                    if (getChecked[0]) {
                        var yearData = jQuery('#layers-tree').tree('getSelected');
                        var farmingData = jQuery('#layers-tree').tree('getParent', yearData.target);
                        var year = yearData.id;
                        var farming = farmingData.id;
                        var requestObj = new Object();
                        requestObj = {
                            year: year,
                            farming: farming,
                            id: getChecked[0].id
                        };
                        TF.Rpc.ZPlots.ZPlotsMaingrid.markForEdit(requestObj)
                        .done(function (data) {
                            initZPEditComponents();
                            setEditZPlotFields(data);
                            jQuery('#win-edit-zp').window('open');
                        })
                    } else {
                        jQuery.messager.alert('Грешка', 'Моля изберете парцел, който искате да редактирате.');
                    }
                }
            }, {
                id: 'btnmultiedit',
                text: 'Мултиредакция',
                iconCls: 'icon-multi-edit',
                handler: function() {
                    initMultiEditFields();
                    jQuery('#win-multi-edit').window('open');
                }
            }, '-', {
                id: 'btnfilterzp',
                text: 'Филтър',
                iconCls: 'icon-filter',
                handler: function() {
                    jQuery('#win-filter-zplots').window('open');
                }
            }, {
                id: 'btnclearzpfilter',
                text: 'Покажи всички',
                iconCls: 'icon-clear-filter',
                handler: function() {
                    clearZPlotsFilter();
                }
            }, {
                id: 'btnzpinfo',
                text: 'Информация',
                iconCls: 'icon-info',
                handler: function() {
                    var getChecked = jQuery('#zp-tables').datagrid('getChecked');

                    if (getChecked[0]) {
                        var yearData = jQuery('#layers-tree').tree('getSelected');
                        var farmingData = jQuery('#layers-tree').tree('getParent', yearData.target);

                        var year = yearData.id;
                        var farming = farmingData.id;

                        initInfoPropertyGrid(getChecked[0]['id'], farming, year);
                        jQuery('#win-info-pg').window('open');
                    } else {
                        jQuery.messager.alert('Грешка', 'Моля изберете запис, за който да бъде показана информация.');
                    }
                }
            }, '-', {
                id: 'btnshowkvsplots',
                text: 'Пресичане с имоти',
                iconCls: 'icon-intersection',
                handler: function() {
                    var yearData = jQuery('#layers-tree').tree('getSelected');
                    var farmingData = jQuery('#layers-tree').tree('getParent', yearData.target);

                    var year = yearData.id;
                    var farming = farmingData.id;

                    InitKVSGrid(farming, year);
                    jQuery('#win-kvs-plots').window('open');
                }
            }, {
                id: 'btnzpexport',
                text: 'Експорт',
                iconCls: 'icon-export',
                handler: function() {
                    var yearData = jQuery('#layers-tree').tree('getSelected');
                    var farmingData = jQuery('#layers-tree').tree('getParent', yearData.target);

                    var year = yearData.id;
                    var farming = farmingData.id;
                    var ekatte = jQuery('#zp-filter-ekate > input').combobox('getValue');
                    var isak_prc_uin = jQuery('#zp-filter-isak > input').val();
                    var culture = jQuery('#zp-filter-culture > input').combobox('getValue');

                    var exportObj = new Object();
                    exportObj = {
                        farming: farming,
                        year: year,
                        isak_prc_uin: isak_prc_uin,
                        culture: culture,
                        ekatte: ekatte
                    };

                    TF.Rpc.ZPlots.ZPlotsExport.exportExcel(exportObj)
                    .done(function (data) {
                        winDownload.window('open');
                        _pathFile = data.path;
                        _fileName = data.name;
                        downloadFile.attr("href", _pathFile);
                    });
                }
            }
        ],
        onDblClickRow: function(rowIndex, rowData) {
            var yearData = jQuery('#layers-tree').tree('getSelected');
            var farmingData = jQuery('#layers-tree').tree('getParent', yearData.target);

            initInfoPropertyGrid(rowData['id'], farmingData.id, yearData.id);
            jQuery('#win-info-pg').window('open');
        },
        onBeforeLoad: function() {
            jQuery('#zp-tables').datagrid('clearChecked');
        },
        onLoadSuccess: function() {
            initReportGrid('culture', farming, year);
        },
        onSelect: function(rowIndex, rowData) {
			checkedElement = rowData;
			//remove all previous features
			vectors.removeAllFeatures();
			displayFeatureSelection(rowData.st_astext);
		},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}
function displayFeatureSelection(geom)
{
	map.addLayer(vectors);

	var in_options = {
		'internalProjection': map.baseLayer.projection,
		'externalProjection': new OpenLayers.Projection("EPSG:32635")
	};
	var features = new OpenLayers.Format.WKT(in_options).read(geom);

	var bounds;

	if (features) {
		if (features.constructor != Array) {
			features = [features];
		}
		for (var i = 0; i < features.length; ++i) {
			if (!bounds) {
				bounds = features[i].geometry.getBounds();
			} else {
				bounds.extend(features[i].geometry.getBounds());
			}
		}
		vectors.addFeatures(features);
		var b = bounds.getCenterLonLat();
		var lonlat = new OpenLayers.LonLat(b.lon, b.lat);
		map.panTo(lonlat);
	}
}

function filterZPlots() {
    var yearData = jQuery('#layers-tree').tree('getSelected');
    var farmingData = jQuery('#layers-tree').tree('getParent', yearData.target);

    var year = yearData.id;
    var farming = farmingData.id;

    jQuery('#zp-tables').datagrid({
        url: 'index.php?zplots-rpc=zplots-maingrid',
        rpcParams: [{
            farming: farming,
            year: year,
            ekatte: jQuery('#zp-filter-ekate > input').combobox('getValue'),
            isak_prc_uin: jQuery('#zp-filter-isak > input').val(),
            culture: jQuery('#zp-filter-culture > input').combobox('getValue')
        }]
    });

    jQuery('#zp-tables').datagrid('uncheckAll');
    jQuery('#zp-tables').datagrid('unselectAll');

    jQuery('#win-filter-zplots').window('close');

    initRequest();
}

function clearZPlotsFilter() {
    var yearData = jQuery('#layers-tree').tree('getSelected');
    var farmingData = jQuery('#layers-tree').tree('getParent', yearData.target);

    var year = yearData.id;
    var farming = farmingData.id;

    jQuery('#zp-tables').datagrid({
        url: 'index.php?zplots-rpc=zplots-maingrid',
        rpcParams: [{
            farming: farming,
            year: year
        }]
    });

    jQuery('#zp-filter-ekate > input').combobox('clear'),
            jQuery('#zp-filter-isak > input').val(''),
            jQuery('#zp-filter-culture > input').combobox('clear')

    jQuery('#zp-tables').datagrid('uncheckAll');
    jQuery('#zp-tables').datagrid('unselectAll');

    initRequest();
}

function initZPEditComponents() {
	jQuery('#edit-zp-ekate > input').combobox({
        data: jQuery('#zp-filter-ekate > input').combobox('getData'),
        valueField: 'ekate',
        textField: 'ekate',
        editable: false
    });

    jQuery('#edit-zp-culture > input').combobox({
        data: jQuery('#zp-filter-culture > input').combobox('getData'),
        valueField: 'id',
        textField: 'name'
    });
}

function initMultiEditFields() {
    jQuery('#me-culture').combobox({
        data: jQuery('#zp-filter-culture > input').combobox('getData'),
        valueField: 'id',
        textField: 'name',
        width: 200
    });
}

function executeMultiEdit() {
    if (jQuery('#me-culture').combobox('getValue') || jQuery('#me-mestnost > input').val()) {
        jQuery.messager.confirm('Потвърждение', 'Това действие ще промени всички данни, които са показани в таблицата.', function(r) {
            if (r) {
                var yearData = jQuery('#layers-tree').tree('getSelected');
                var farmingData = jQuery('#layers-tree').tree('getParent', yearData.target);

                var obj = new Object();
                obj.farming = farmingData.id;
                obj.year = yearData.id;
                //filters
                obj.ekatte = jQuery('#zp-filter-ekate > input').combobox('getValue');
                obj.isak_prc_uin = jQuery('#zp-filter-isak > input').val();
                obj.culture = jQuery('#zp-filter-culture > input').combobox('getValue');
                //new culture
                obj.newCulture = jQuery('#me-culture').combobox('getValue');
                obj.mestnost = jQuery('#me-mestnost > input').val();
                TF.Rpc.ZPlots.ZPlotsMaingrid.saveMultiEdit(obj)
                .done(function (data) {
                    jQuery('#win-multi-edit').window('close');
                    clearZPlotsFilter();
                    jQuery('#zp-tables').datagrid('loadRpc');
                })
                .fail(function (errorObj) {
                    if (errorObj.is(TF.Rpc.ExceptionsList.NO_ZPLOTS_FOUND)) {
                        jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.NO_ZPLOTS_FOUND.message,'warning');
                    }
                });
            }
        });
    } else {
        jQuery.messager.alert('Грешка', 'Моля задайте данни, който да бъдат променени.');
    }
}

function initInfoPropertyGrid(id, farming, year){
	jQuery('#zp-propertygrid').propertygrid({
        rpcParams: [{
            plot_id: id,
            farming: farming,
            year: year
        }],
        url: 'index.php?zplots-rpc=zp-info-pg',
        showGroup: true,
        scrollbarSize: 0,
        resizable: false,
        columns:[[
        {
            field:'name',
            title:'Параметър',
            width:120
        },
        {
            field:'value',
            title:'Стойност',
            width:180
        },
        ]],
        fixed: true,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function saveZPEditFields() {

    var saveObj = new Object();

    var yearData = jQuery('#layers-tree').tree('getSelected');
    var farmingData = jQuery('#layers-tree').tree('getParent', yearData.target);
    var year = yearData.id;
    var farming = farmingData.id;
    var getChecked = jQuery('#zp-tables').datagrid('getChecked');

    saveObj = {
        id: getChecked[0].id,
        farming: farming,
        year: year,
        isak_prc_uin: jQuery('#edit-zp-isak > input').val(),
        ekatte: jQuery('#edit-zp-ekate > input').combobox('getValue'),
        mestnost: jQuery('#edit-zp-mestnost > input').val(),
        culture: jQuery('#edit-zp-culture > input').combobox('getValue'),
        obrabotki: jQuery('#edit-zp-obrabotki > input').val(),
        dobivi: jQuery('#edit-zp-dobivi > input').val(),
        napoqvane: jQuery('#edit-zp-napoqvane > input').val(),
        polivki: jQuery('#edit-zp-polivki > input').val(),
        polzvatel: jQuery('#edit-zp-polzvatel > input').val(),
        area_name: jQuery('#edit-zp-area_name > input').val(),
    };

    TF.Rpc.ZPlots.ZPlotsMaingrid.saveEdit(saveObj)
    .done(function (data) {
        jQuery('#win-edit-zp').window('close');
        jQuery('#zp-tables').datagrid('loadRpc');
    })
    .fail(function (errorObj) {

    });
}

function setEditZPlotFields(data) {
    jQuery('#edit-zp-isak > input').val(data.isak_prc_uin);
    jQuery('#edit-zp-ekate > input').combobox('setValue', data.ekatte);
    jQuery('#edit-zp-mestnost > input').val(data.mestnost);
    jQuery('#edit-zp-culture > input').combobox('setValue', data.culture);
    jQuery('#edit-zp-obrabotki > input').val(data.obrabotki);
    jQuery('#edit-zp-dobivi > input').val(data.dobivi);
    jQuery('#edit-zp-napoqvane > input').val(data.napoqvane);
    jQuery('#edit-zp-polivki > input').val(data.polivki);
    jQuery('#edit-zp-polzvatel > input').val(data.polzvatel);
    jQuery('#edit-zp-area_name > input').val(data.area_name);
}

function onDownloadWindowClose() {
   return;
}
