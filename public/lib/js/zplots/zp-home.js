var isSuperAdmin = false;
var map;
var boundsArray;
var layerArray;
var renderer = OpenLayers.Util.getParameters(window.location.href).renderer;
if (renderer) {
    OpenLayers.Layer.Vector.prototype.renderers = [renderer];
}
var mapKVS;
var boundsKvsArray;
var layerKvsArray;

jQuery(function() {
    initZPFilterPanel();
    setUserLastLogin();

    jQuery('#map-choose-plot').bind('click', function() {
        if (!isLoading) {
            var options = jQuery('#map-choose-plot').linkbutton('options');
            if (!options.selected) {
                jQuery('#map-choose-plot').linkbutton('select');
                if (!options.disabled) {
                    map.events.register('click', map, propertyWindowFunction);
                }
            } else {
                jQuery('#map-choose-plot').linkbutton('unselect');
                map.events.unregister('click', map, propertyWindowFunction);
            }
        }
    });

  //init map types combobox
	jQuery('#map-types-combobox > input').combobox({
		url: 'index.php?common-rpc=map-types-combobox',
        rpcParams: [{selected: true}],
		valueField: 'id',
		textField: 'name',
		onSelect: function(record)
		{
			initMapPad(record.id);
		},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
	});

	  //init map types combobox
	jQuery('#map-types-combobox-kvs > input').combobox({
		url: 'index.php?common-rpc=map-types-combobox',
        rpcParams: [{selected: true}],
		valueField: 'id',
		textField: 'name',
		onSelect: function(record)
		{
			initMapPadKVS(record.id);
		},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
	});

    jQuery('#btn-choose-report-type > a').bind('click', function() {
        resendForReport();
    });

    jQuery('#map-filter').bind('click', function() {
        if (!isLoading) {
            unselectAllButtons();
            jQuery('#map-filter').linkbutton('select');
            initRequest();
        }
        return false;
    });

    jQuery('#map-culture').bind('click', function() {
        if (!isLoading) {
            unselectAllButtons();
            jQuery('#map-culture').linkbutton('select');
            initCultureRequest();
        }
        return false;
    });

    //init buttons
    jQuery('#btn-choose-report-type > a').linkbutton({iconCls: 'icon-ok'});
    jQuery('#btn-edit-zp').linkbutton({iconCls: 'icon-save'});
    jQuery('#btn-multiedit > a').linkbutton({iconCls: 'icon-save'});
    jQuery('#btn-choose-intersect-report-type > a').linkbutton({iconCls: 'icon-ok'});

    //init components
    initLayersTree();
});

function initZPFilterPanel() {
    jQuery('#zp-filter-culture > input').combobox({
        url: 'index.php?common-rpc=culture-combobox',
        valueField: 'id',
        textField: 'name',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#zp-filter-ekate > input').combobox({
        url: 'index.php?common-rpc=ekate-combobox',
        valueField: 'ekate',
        textField: 'text',
		filter: function(q, row){
			var opts = jQuery(this).combobox('options');
			var text = row[opts.textField].toLowerCase();
			var value = row[opts.valueField];
			var find = q.toLowerCase();
			if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
			{
				return true;
			}
		},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initMap() {

    if (!map) {
        var options = {
            controls: [new OpenLayers.Control.Navigation()],
            projection: new OpenLayers.Projection("EPSG:900913")
        };
        map = new OpenLayers.Map('map', options);

        var renderer = OpenLayers.Util.getParameters(window.location.href).renderer;
    	renderer = (renderer) ? [renderer] : OpenLayers.Layer.Vector.prototype.renderers;

    	vectors = new OpenLayers.Layer.Vector("Vector Layer", {
    		styleMap: new OpenLayers.StyleMap(new OpenLayers.Style({
    			cursor: "pointer",
    			graphicName: 'square',
    			pointRadius: 10,
    			fillColor: '#cccccc',
    			strokeColor: "#00ff00",
    			strokeWidth: 2,
    			fillOpacity: 0.25,
    			label: '${label}'
    		}, {
                isDefault: true,
    			context: {
    				label: function (feature) {
    					if(!feature.attributes.hasOwnProperty('label')) {
    						return '';
    					}
    					return feature.attributes.label;
    				}
    			}
    		})),
    		renderers: renderer
    	});

        initMapPad();
    }

    initRequest();
    map.render("map");
}

var bingApiKey = "AiWEso3-IjWyX1aZMuep9Sjl62D6FUMqv8qQGpV-kgbW0qWk61to4nrqHh-2D5HL";

function initMapPad(specific_map_type)
{
	var chosenMapType;

	//init all map features
	var options = {
		controls: [],
		projection: new OpenLayers.Projection("EPSG:900913")
	};

	//on init map type will not be specified
	if (specific_map_type == undefined)
	{
		chosenMapType = store.get('map_pad') || 1;
		//init map
	}
	//when map type is changed specific_map_type will have the value of map type
	else {
		chosenMapType = parseInt(specific_map_type);
	}

	var layerMapPad;

	switch (chosenMapType)
	{
		case 2:
			layerMapPad = new OpenLayers.Layer.Bing({
				key: bingApiKey,
				type: "Aerial",
				name: "MapPad"
			});
			break;
		case 3:
			layerMapPad = new OpenLayers.Layer.Bing({
				key: bingApiKey,
				type: "Road",
				name: "MapPad"
			});
			break;
		case 4:
			layerMapPad = new OpenLayers.Layer.Google(
					"MapPad",
					{type: google.maps.MapTypeId.TERRAIN}
			);
			break;
		case 5:
			layerMapPad = new OpenLayers.Layer.Google(
					"MapPad",
					{numZoomLevels: 20}
			);
			break;
		case 6:
			layerMapPad = new OpenLayers.Layer.Google(
					"MapPad",
					{type: google.maps.MapTypeId.HYBRID, numZoomLevels: 20}
			);
			break;
		case 7:
			layerMapPad = new OpenLayers.Layer.Google(
					"MapPad",
					{type: google.maps.MapTypeId.SATELLITE, numZoomLevels: 22}
			);
			break;
		case 8:
			layerMapPad = new OpenLayers.Layer.OSM();
			break;
        case 9:
            layerMapPad = new OpenLayers.Layer.WMS(
				'MapPad',
                imagesWMSServer + "?map=" + '/var/www/satellite_processor/maps/geo_scan.map',
                //wmsServer + "?map=" + mapPath + groupID + '.map',
				{
					layers: 'geo_scan'
				},
                {
                    numZoomLevels: 18
                });
			break;
		default: // default is bing aerial with labels
			layerMapPad = new OpenLayers.Layer.Bing({
				key: bingApiKey,
				type: "AerialWithLabels",
				name: "MapPad"
			});
			break;
	}

	//specific map type will be given only on reload
	//on first load(init) specific map type should be undefined
	if (specific_map_type == undefined)
	{
		map.addLayer(layerMapPad);
	}
	else {
		map.addLayer(layerMapPad);
		map.setLayerIndex(map.layers[map.layers.length - 1], 0);
		map.removeLayer(map.layers[1]);
		map.layers[0].redraw(true);
	}
}

function initRequest() {
    if (!isLoading) {
        unselectAllButtons();
        jQuery('#map-filter').linkbutton('select');

        var obj = new Object();

        var yearData = jQuery('#layers-tree').tree('getSelected');
        var farmingData = jQuery('#layers-tree').tree('getParent', yearData.target);

        obj.ekatte = jQuery('#zp-filter-ekate > input').combobox('getValue');
        obj.isak_prc_uin = jQuery('#zp-filter-isak > input').val();
        obj.culture = jQuery('#zp-filter-culture > input').combobox('getValue');

        obj.year = yearData.id;
        obj.farming = farmingData.id;

        TF.Rpc.ZPlots.ZPMapInfo.initMap(obj)
        .done(function (data) {
            displayMap(data);
        })
        .fail(function (errorObj) {
            if (errorObj.is(TF.Rpc.ExceptionsList.DATABASE_INVALID_TABLE_NAME)) {
                jQuery.messager.alert('Грешка', errorObj.getMessage());
            };
        });
    }
}

function initCultureRequest()
{
    if (!isLoading) {
        unselectAllButtons();
        jQuery('#map-culture').linkbutton('select');

        var obj = new Object();
        obj.ekatte = jQuery('#zp-filter-ekate > input').combobox('getValue');
        obj.isak_prc_uin = jQuery('#zp-filter-isak > input').val();
        obj.culture = jQuery('#zp-filter-culture > input').combobox('getValue');

        var yearData = jQuery('#layers-tree').tree('getSelected');
        var farmingData = jQuery('#layers-tree').tree('getParent', yearData.target);

        obj.year = yearData.id;
        obj.farming = farmingData.id;

        TF.Rpc.ZPlots.ZPMapInfo.initCulture(obj)
        .done(function (data) {
            displayMap(data);
        })
        .fail(function (errorObj) {
            if (errorObj.is(TF.Rpc.ExceptionsList.DATABASE_INVALID_TABLE_NAME)) {
                jQuery.messager.alert('Грешка', errorObj.getMessage());
            };
        });
    }
}

function displayMap(data)
{
    if (data['layers']) {
        for (var i = 1; i < map.layers.length; i++)
        {
			map.removeLayer(map.layers[i]);
        }

        //get new layers data
        layers = data['layers'];

        for (var i = 0; i < layers.length; i++) {
            var layerExtent = new OpenLayers.Bounds.fromString(layers[i].extent).transform(
                    new OpenLayers.Projection("EPSG:32635"),
                    map.getProjectionObject()
                    );

            var layerData = new OpenLayers.Layer.WMS(
                    layers[i].name,
                    wmsServer + "?map=" + mapPath + groupID + '.map',
                    {
                        layers: layers[i].name,
                        format: 'image/png',
                        transparent: "true"
                    }
            );

            map.addLayer(layerData);
            map.zoomToExtent(layerExtent);
			layerData.redraw(true);
        }
    } else {
        map.zoomToExtent(new OpenLayers.Bounds.fromString("125190.6162, 4573142.7188, 631370.3273, 4887149.5823").transform(
                new OpenLayers.Projection("EPSG:32635"),
                map.getProjectionObject())
                );
    }

    //init legend
    jQuery('#legend-tree').tree({
        data: data['colorarray'],
        formatter: function(node) {
            if (node.color)
                return "<div style='width:13px;height:13px;background-color:#" + node.color + ";margin-top:3px;float:left;margin-right:3px;'></div>" + node.name;
            else if (node.text)
                return node.text;
        }
    });
}

function removeLayerByName(layer_name) {
    for (var i = 0; i < map.layers.length; i++)
    {
        if (map.layers[i].name == layer_name)
            map.removeLayer(map.layers[i]);
    }
}

function showMapKVS()
{
    initMapKvs();
    initKvsRequest();
    jQuery('#win-map-kvs').window('open');

}

function initMapKvs() {
    if (!mapKVS) {
        var options = {
            controls: [new OpenLayers.Control.Navigation()],
            projection: new OpenLayers.Projection("EPSG:900913")
        };
        mapKVS = new OpenLayers.Map('map-kvs', options);
        initMapPadKVS();
        mapKVS.zoomToExtent(new OpenLayers.Bounds.fromString("125190.6162, 4573142.7188, 631370.3273, 4887149.5823").transform(
                new OpenLayers.Projection("EPSG:32635"),
                mapKVS.getProjectionObject())
                );

    }

    mapKVS.render("map-kvs");
}

function initMapPadKVS(specific_map_type)
{
	var chosenMapType;

	//init all map features
	var options = {
		controls: [],
		projection: new OpenLayers.Projection("EPSG:900913")
	};

	//on init map type will not be specified
	if (specific_map_type == undefined)
	{
		chosenMapType = store.get('map_pad') || 1;
		//init map
	}
	//when map type is changed specific_map_type will have the value of map type
	else {
		chosenMapType = parseInt(specific_map_type);
	}

	var layerMapPad;

	switch (chosenMapType)
	{
		case 2:
			layerMapPad = new OpenLayers.Layer.Bing({
				key: bingApiKey,
				type: "Aerial",
				name: "MapPad"
			});
			break;
		case 3:
			layerMapPad = new OpenLayers.Layer.Bing({
				key: bingApiKey,
				type: "Road",
				name: "MapPad"
			});
			break;
		case 4:
			layerMapPad = new OpenLayers.Layer.Google(
					"MapPad",
					{type: google.maps.MapTypeId.TERRAIN}
			);
			break;
		case 5:
			layerMapPad = new OpenLayers.Layer.Google(
					"MapPad",
					{numZoomLevels: 20}
			);
			break;
		case 6:
			layerMapPad = new OpenLayers.Layer.Google(
					"MapPad",
					{type: google.maps.MapTypeId.HYBRID, numZoomLevels: 20}
			);
			break;
		case 7:
			layerMapPad = new OpenLayers.Layer.Google(
					"MapPad",
					{type: google.maps.MapTypeId.SATELLITE, numZoomLevels: 22}
			);
			break;
		case 8:
			layerMapPad = new OpenLayers.Layer.OSM();
			break;
        case 9:
            layerMapPad = new OpenLayers.Layer.WMS(
				'MapPad',
                imagesWMSServer + "?map=" + '/var/www/satellite_processor/maps/geo_scan.map',
                //wmsServer + "?map=" + mapPath + groupID + '.map',
				{
					layers: 'geo_scan',
				});
			break;
		default: // default is bing aerial with labels
			layerMapPad = new OpenLayers.Layer.Bing({
				key: bingApiKey,
				type: "AerialWithLabels",
				name: "MapPad"
			});
			break;
	}

	//specific map type will be given only on reload
	//on first load(init) specific map type should be undefined
	if (specific_map_type == undefined)
	{
		mapKVS.addLayer(layerMapPad);
	}
	else {
		mapKVS.addLayer(layerMapPad);
		mapKVS.setLayerIndex(mapKVS.layers[mapKVS.layers.length - 1], 0);
		mapKVS.removeLayer(mapKVS.layers[1]);
		mapKVS.layers[0].redraw(true);
	}
}
function unselectAllButtons() {
    jQuery('#map-filter').linkbutton('unselect');
    jQuery('#map-culture').linkbutton('unselect');
}

function reloadAllKvsLayers() {
    for (var i = 0; i < layerKvsArray.length; i++) {
        layerKvsArray[i].redraw(true);
    }
}

function initKvsRequest()
{
    if (!isLoading) {
        var obj = new Object();

        var yearData = jQuery('#layers-tree').tree('getSelected');
        var farmingData = jQuery('#layers-tree').tree('getParent', yearData.target);

        obj.ekatte = jQuery('#zp-filter-ekate > input').combobox('getValue');
        obj.isak_prc_uin = jQuery('#zp-filter-isak > input').val();
        obj.culture = jQuery('#zp-filter-culture > input').combobox('getValue');

        obj.year = yearData.id;
        obj.farming = farmingData.id;

        TF.Rpc.ZPlots.ZPMapInfo.initKvs(obj)
        .done(function (data) {
            initLegendKvs(data);
        })
        .fail(function (errorObj) {
            if (errorObj.is(TF.Rpc.ExceptionsList.DATABASE_INVALID_TABLE_NAME)) {
                jQuery.messager.alert('Грешка', errorObj.getMessage());
            };
        });
    }
}

function initLegendKvs(data)
{
    if (data['layers']) {
        //remove old layers
        if (layerKvsArray) {
            for (var i = 0; i < layerKvsArray.length; i++) {
                map.removeLayer(layerKvsArray[i])
            }
        }
        //get new layers data
        layers = data['layers'];
        boundsKvsArray = [];
        layerKvsArray = [];
        var j = 0;

        for (var i = 0; i < layers.length; i++) {
            boundsKvsArray[j] = new OpenLayers.Bounds.fromString(layers[i].extent).transform(
                    new OpenLayers.Projection("EPSG:32635"),
                    mapKVS.getProjectionObject()
                    );

            layerKvsArray[j] = new OpenLayers.Layer.WMS(
                    layers[i].name,
                    wmsServer + "?map=" + mapPath + groupID + '.map',
                    {
                        layers: layers[i].name,
                        format: 'image/png',
                        transparent: "true"
                    }
            );

            mapKVS.addLayer(layerKvsArray[j]);
            mapKVS.zoomToExtent(boundsKvsArray[j]);
        }
    } else {
        mapKVS.zoomToExtent(new OpenLayers.Bounds.fromString("125190.6162, 4573142.7188, 631370.3273, 4887149.5823").transform(
                new OpenLayers.Projection("EPSG:32635"),
                mapKVS.getProjectionObject())
                );
    }

    jQuery('#legend-kvs-tree').tree({
        data: data['colorarray'],
        formatter: function(node) {
            if (node.color)
                return "<div style='width:13px;height:13px;background-color:#" + node.color + ";margin-top:3px;float:left;margin-right:3px;'></div>" + node.name;
            else if (node.text)
                return node.text;
        }
    });
}

function propertyWindowFunction(e) {
    var yearData = jQuery('#layers-tree').tree('getSelected');
    var farmingData = jQuery('#layers-tree').tree('getParent', yearData.target);

    var year = yearData.id;
    var farming = farmingData.id;

    bounds = map.getExtent();

    x1 = parseInt(e.xy.x);
    y1 = parseInt(e.xy.y);

    var requestObj = new Object();
    requestObj = {
        bbox: bounds.toBBOX(),
        x: x1,
        y: y1,
        width: map.size.w,
        height: map.size.h,
        farming: farming,
        year: year
    };
    TF.Rpc.ZPlots.ZPMapInfo.read(requestObj)
    .done(function (data) {
        initInfoPropertyGrid(data, farming, year);
        jQuery('#win-info-pg').window('open');
        jQuery('#win-plot-info').window('open');
    })
    .fail(function (errorObj) {
        if (errorObj.is(TF.Rpc.ExceptionsList.MAP_EMPTY_AREA)) {
            jQuery.messager.alert('Грешка',TF.Rpc.ExceptionsList.MAP_EMPTY_AREA.message);
        };
    });

}
