function initLayersTree() {
    jQuery('#layers-tree').tree({
        url: 'index.php?zplots-rpc=zplots-layers-tree',
        animate: true,
        lines: true,
        onBeforeSelect: function(node) {
            var isLeaf = jQuery('#layers-tree').tree('isLeaf', node.target);

            if (!isLeaf)
                return false;
        },
        onSelect: function(node) {
            var isLeaf = jQuery('#layers-tree').tree('isLeaf', node.target);
            var yearData = jQuery('#layers-tree').tree('getSelected');

            initDataGrid();
            initMap();
        },
        onLoadSuccess: function() {
            var roots = jQuery('#layers-tree').tree('getRoots');
            var years = jQuery('#layers-tree').tree('getChildren', roots[0].target);

            jQuery('#layers-tree').tree('select', years[0].target);
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}
