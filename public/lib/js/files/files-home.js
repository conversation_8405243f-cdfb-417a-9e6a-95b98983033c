var isSuperAdmin=false;
var PARTIALLY_PROCESSED = 17;
var OSZFileType = 18;
var NOT_UPDATED_CONTRACTS = 22;
var LAYER_TYPE_TMP = 1;
var LAYER_TYPE_KMS = 3;
var LAYER_TYPE_ISAK = 5;
var LAYER_TYPE_FOR_ISAK = 8;

jQuery(function(){
    setUserLastLogin();
    jQuery('#search-farming').combobox({
        url: 'index.php?common-rpc=farming-combobox',
        valueField: 'id',
        textField: 'name',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    jQuery('#search-farming-year').combobox({
        url: 'index.php?common-rpc=farming-year-combobox',
        valueField: 'id',
        textField: 'title',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery.fn.datebox.defaults.formatter = function(date){
        var y = date.getFullYear();
        var m = date.getMonth()+1;
        var d = date.getDate();
        return d+'/'+m+'/'+y;
    };

    jQuery.fn.datebox.defaults.parser = function(s){
        if (s) {
            var a = s.split('/');
            var d = Number(a[0]);
            var m = Number(a[1]);
            var y = Number(a[2]);

            var dd = new Date(y, m-1, d);

            return dd;
        } else {
            return new Date();
        }
    };

    jQuery('#files-tables').datagrid({
        title:'Данни слоеве',
        iconCls:'icon-files',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 20,
        fit: true,
        fitColumns: true,
        showFooter: true,
        url:'index.php?files-rpc=files',
        sortName: 'date_uploaded',
        rpcParams: [{}],
        sortOrder: 'desc',
        idField:'id',
        singleSelect: true,
        frozenColumns:[[{
            field:'ck',
            checkbox:true
        }]],
        columns:[[{
            field:'name',
            title:'<b>Име</b>',
            sortable:true,
            width:250
        },{
            field:'status',
            title:'<b>Статус</b>',
            sortable:true,
            width:300
        },{
            field:'date_uploaded',
            title:'<b>Дата</b>',
            sortable:true,
            width:160
        },{
            field:'farming',
            title:'<b>Стопанство</b>',
            sortable:true,
            width:200
        },{
            field:'shape_type',
            title:'<b>Тип</b>',
            sortable:true,
            width:130
        },{
            field:'crs',
            title:'<b>SRID</b>',
            sortable:true,
            width:100
        }]],
        onDblClickRow: function(index, el){

            requestGetLayersData.setCallbackParameter({
                id:el.id
            });
            requestGetLayersData.dispatch();
            jQuery('#'+editLayerID).val(el.id);
            jQuery('#files-tables').datagrid('unselectAll');
        },
        pagination:true,
        rownumbers:true,
        toolbar: [{
            id:'btnadd',
            text:'Добавяне',
            iconCls:'icon-add',
            handler:function(){
                jQuery('#win-srid').window('open');
            }
        },{
            id:'btnadd-kvs',
            text:'Зареждане на КВС',
            iconCls:'icon-add',
            handler:function(){
                initFileUploads(true);
                jQuery('#win-add-file').window('open');
            }
        },{
            id:'btn-import-excel-data-kvs',
            text:'Импорт Ексел',
            iconCls:'icon-csv',
            handler:function(){
                initFileUploads(false, true);
                jQuery('#win-add-file').window('open');
            }
            },{
            id:'btndefinition',
            text:'Дефиниция',
            iconCls:'icon-files',
            handler:function(){
                var getChecked = jQuery('#files-tables').datagrid('getChecked');
                if(getChecked[0]){
                    if(getChecked[0]['shape_type_code'] == 5 && getChecked[0]['status_code'] == 10){
                        jQuery('#definition-full-number > input').combobox({
                            url:'index.php?files-rpc=definition',
                            rpcParams: [{
                                fileId: getChecked[0].id,
                                layerType: getChecked[0].shape_type_code,
                            }],
                            valueField: 'column_name',
                            textField: 'column_name',
                            required: true,
                            editable: false,
                            disabled:true,
                            onLoadSuccess: function(){
                                initKVSDefinition();
                            },
                            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
                        });

                        jQuery('#win-definition').window('open');
                    }else if(getChecked[0].shape_type_code == 4 && getChecked[0].status_code == 10){
                        //danni ot komasaciq
                        //Номер БЗЗ
                        jQuery('#kms-definition-number > input').combobox({
                            url:'index.php?files-rpc=definition',
                            rpcParams: [{
                                fileId: getChecked[0].id,
                                layerType: getChecked[0].shape_type_code,
                            }],
                            valueField: 'column_name',
                            textField: 'column_name',
                            required: true,
                            editable: false,
                            disabled:false,
                            onLoadSuccess: function(){
                                var tmpData = jQuery('#kms-definition-number > input').combobox('getData');
                                for (var i = tmpData.length - 1; i >= 0; i--) {
                                    if(tmpData[i].column_name === 'name' ||
                                        tmpData[i].column_name === 'bzz_name') {
                                        jQuery('#kms-definition-number > input').combobox('select', tmpData[i].column_name);
                                        break;
                                    }
                                }
                                initKMSDefinition();
                            },
                            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
                        });

                        jQuery('#win-kms-definition').window('open');
                    }else if(getChecked[0].shape_type_code == 9 && getChecked[0].status_code == 10){
                        //danni za isak
                        jQuery('#for-isak-definition-number > input').combobox({
                            url:'index.php?files-rpc=definition',
                            rpcParams: [{
                                fileId: getChecked[0].id,
                                layerType: getChecked[0].shape_type_code,
                            }],
                            valueField: 'column_name',
                            textField: 'column_name',
                            required: true,
                            editable: false,
                            disabled:false,
                            onLoadSuccess: function(){
                                var tmpData = jQuery('#for-isak-definition-number > input').combobox('getData');
                                for (var i = tmpData.length - 1; i >= 0; i--) {
                                    if(tmpData[i].column_name === 'prc_name') {
                                        jQuery('#for-isak-definition-number > input').combobox('select', tmpData[i].column_name);
                                        break;
                                    }
                                }
                                initForIsakDefinition();
                            },
                            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
                        });

                        jQuery('#win-for-isak-definition').window('open');
                    } else if(getChecked[0].shape_type_code == 19 && getChecked[0].status_code == 10) {
                        //danni za work
                        jQuery('#for-work-definition-number > input').combobox({
                            url:'index.php?files-rpc=definition',
                            rpcParams: [{
                                fileId: getChecked[0].id,
                                layerType: getChecked[0].shape_type_code,
                            }],
                            valueField: 'column_name',
                            textField: 'column_name',
                            required: true,
                            editable: false,
                            disabled:false,
                            onLoadSuccess: function(){
                                var tmpData = jQuery('#for-work-definition-number > input').combobox('getData');
                                for (var i = tmpData.length - 1; i >= 0; i--) {
                                    if(tmpData[i].column_name === 'prc_name') {
                                        jQuery('#for-work-definition-number > input').combobox('select', tmpData[i].column_name);
                                        break;
                                    }
                                }
                            },
                            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
                        });

                        jQuery('#win-for-work-definition').window('open');
                    } else if(getChecked[0].shape_type_code == 2 && getChecked[0].status_code == 10) {
                        //danni za gps
                        jQuery('#for-gps-definition-number > input').combobox({
                            url:'index.php?files-rpc=definition',
                            rpcParams: [{
                                fileId: getChecked[0].id,
                                layerType: getChecked[0].shape_type_code,
                            }],
                            valueField: 'column_name',
                            textField: 'column_name',
                            required: true,
                            editable: false,
                            disabled:false,
                            onLoadSuccess: function(){
                                var tmpData = jQuery('#for-gps-definition-number > input').combobox('getData');
                                for (var i = tmpData.length - 1; i >= 0; i--) {
                                    if(tmpData[i].column_name === 'prc_name') {
                                        jQuery('#for-gps-definition-number > input').combobox('select', tmpData[i].column_name);
                                        break;
                                    }
                                }
                            },
                            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
                        });

                        jQuery('#win-for-gps-definition').window('open');
                    }
                } else {
                    jQuery.messager.alert('Грешка', 'Моля изберете запис.');
                }
            }
        },{
            id:'btn-kvs-invalid-geomrtry',
            text:'Невалидни геометрии',
            iconCls:'icon-edit-geometry',
            disabled:true,
            handler:function(){
                var selected_row = jQuery('#files-tables').datagrid('getSelected');
                window.location.href = "index.php?page=KVSInvalidGeometry.Home" + "&file_id=" + selected_row.id + "&filename="+selected_row.name + "&date_uploaded="+selected_row.date_uploaded;
            }
        },{
            id: 'btnendupdate',
            text: 'Приключване на актуализация',
            iconCls: 'icon-payments',
            disabled:true,
            handler: function() {
                var file_id = jQuery('#files-tables').datagrid('getSelected').id;
                TF.Rpc.Files.FilesGrid.endUpdate(file_id)
                .done(function(data) {
                    jQuery.messager.alert('Внимание','Успешно приключване на актуализация!','info', function(){
                        jQuery('#files-tables').datagrid('reload');
                    });
               })
                .fail(function(errorObj){
                    var message = TF.Rpc.ExceptionsList.SYSTEM_ERROR.message;
                    if(errorObj.is(TF.Rpc.ExceptionsList.END_UPDATE_KVS_NOT_UPDATED_PLOTS)) {
                        message = TF.Rpc.ExceptionsList.END_UPDATE_KVS_NOT_UPDATED_PLOTS.message;
                    }
                    jQuery.messager.confirm('Внимание', message, function(r){
                        if (r){
                           TF.Rpc.Files.FilesGrid.endUpdate(file_id, true)
                            .done(function(data) {
                               jQuery.messager.alert('Внимание','Успешно приключване на актуализация!','info', function(){
                                    jQuery('#files-tables').datagrid('reload');
                                });
                           });
                        }
                    });
                });
            }
        }],
        onBeforeLoad: function(){
            jQuery('#files-tables').datagrid('clearChecked');
        },
        onSelect: function(index, row){

            if(row.shape_type_code == OSZFileType) {
                if(row.status_code == PARTIALLY_PROCESSED || row.status_code == NOT_UPDATED_CONTRACTS) {
                    jQuery('#btn-kvs-invalid-geomrtry').linkbutton('enable');
                    jQuery('#btnendupdate').linkbutton('enable');
                }else{
                    jQuery('#btn-kvs-invalid-geomrtry').linkbutton('disable');
                    jQuery('#btnendupdate').linkbutton('disable');
                }
            }else{
                jQuery('#btn-kvs-invalid-geomrtry').linkbutton('disable');
                jQuery('#btnendupdate').linkbutton('disable');
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    initProjection();

    jQuery('#button-add > a').linkbutton({
        iconCls: 'icon-save'
    });

    jQuery('#button-edit > a').linkbutton({
        iconCls: 'icon-save'
    });

    jQuery('#btn-save-definition').linkbutton({
        iconCls: 'icon-save'
    });

    jQuery('#btn-search').bind('click', function(){
        jQuery('#files-tables').datagrid({
            pageNumber: 1,
            rpcParams: [{
                name: jQuery('#search-layer').val(),
                farming: jQuery('#search-farming').combobox('getValue'),
                year: jQuery('#search-farming-year').combobox('getValue')
            }]
        });
        return false;
    });

    jQuery('#btn-save-for-work-definition').bind('click', function(){

        var checked = jQuery('#files-tables').datagrid('getChecked');
        var saveObj = {};

        saveObj = {
            crop_name_check: jQuery('#definition-for-work-number-check > input').prop('checked'),
            layer: checked[0],
            crop_name: jQuery('#for-work-definition-number > input').combobox('getValue'),
        };

        TF.Rpc.Files.KVSDefinition.saveForWorkDefinition(saveObj)
            .done(function (data) {
                jQuery('#files-tables').datagrid('reload');
                jQuery('#win-for-work-definition').window('close');
            })
            .fail(function (errorObj) {

            });

        return false;
    });

    jQuery('#btn-save-for-gps-definition').bind('click', function(){

        var checked = jQuery('#files-tables').datagrid('getChecked');
        var saveObj = {};

        saveObj = {
            crop_name_check: jQuery('#definition-for-gps-number-check > input').prop('checked'),
            layer: checked[0],
            crop_name: jQuery('#for-gps-definition-number > input').combobox('getValue'),
        };

        TF.Rpc.Files.KVSDefinition.saveForGpsDefinition(saveObj)
            .done(function (data) {
                jQuery('#files-tables').datagrid('reload');
                jQuery('#win-for-gps-definition').window('close');
            })
            .fail(function (errorObj) {

            });

        return false;
    });

    jQuery('#btn-save-definition').bind('click', function(){

        var checked = jQuery('#files-tables').datagrid('getChecked');
        var saveObj = {
            definition_full_number_check: jQuery('#definition-full-number-check > input').prop('checked'),
            definition_ekatte_check: jQuery('#definition-ekatte-check > input').prop('checked'),
            definition_masiv_check: jQuery('#definition-masiv-check > input').prop('checked'),
            definition_number_check: jQuery('#definition-number-check > input').prop('checked'),
            definition_category_check: jQuery('#definition-category-check > input').prop('checked'),
            definition_waytouse_check: jQuery('#definition-waytouse-check > input').prop('checked'),
            definition_mestnost_check: jQuery('#definition-mestnost-check > input').prop('checked'),
            definition_document_area_check: jQuery('#definition-document-area-check > input').prop('checked'),
            layer: checked[0],
            fullnumber: jQuery('#definition-full-number > input').combobox('getValue'),
            ekatte: jQuery('#definition-ekatte > input').combobox('getValue'),
            masiv: jQuery('#definition-masiv > input').combobox('getValue'),
            number: jQuery('#definition-number > input').combobox('getValue'),
            category: jQuery('#definition-cat > input').combobox('getValue'),
            waytouse: jQuery('#definition-way > input').combobox('getValue'),
            mestnost: jQuery('#definition-mestnost > input').combobox('getValue'),
            document_area: jQuery('#definition-document-area > input').combobox('getValue')
        };

        TF.Rpc.Files.KVSDefinition.saveDefinition(saveObj)
        .done(function (data) {
            jQuery('#files-tables').datagrid('reload');
            jQuery('#win-definition').window('close');
        })
        .fail(function (errorObj) {

        });

        return false;
    });

    jQuery('#btn-save-kms-definition').bind('click', function(){

        var checked = jQuery('#files-tables').datagrid('getChecked');
        var saveObj = {};

        saveObj = {
            ekatte_check: jQuery('#kms-definition-ekatte-check > input').prop('checked'),
            crop_name_check: jQuery('#kms-definition-crop-name-check > input').prop('checked'),
            crop_code_check: jQuery('#kms-definition-crop-code-check > input').prop('checked'),
            layer: checked[0],
            number: jQuery('#kms-definition-number > input').combobox('getValue'),
            ekatte: jQuery('#kms-definition-ekatte > input').combobox('getValue'),
            crop_name: jQuery('#kms-definition-crop-name > input').combobox('getValue'),
            crop_code: jQuery('#kms-definition-crop-code > input').combobox('getValue'),
        };

        TF.Rpc.Files.KVSDefinition.saveKMSDefinition(saveObj)
        .done(function (data) {
            jQuery('#files-tables').datagrid('reload');
            jQuery('#win-kms-definition').window('close');
        })
        .fail(function (errorObj) {

        });

        return false;
    });

    jQuery('#btn-save-for-isak-definition').bind('click', function(){

        var checked = jQuery('#files-tables').datagrid('getChecked');
        var saveObj = {};

        saveObj = {
            number_check: jQuery('#definition-for-isak-number-check > input').prop('checked'),
            ekatte_check: jQuery('#for-isak-definition-ekatte-check > input').prop('checked'),
            crop_name_check: jQuery('#for-isak-definition-crop-name-check > input').prop('checked'),
            crop_code_check: jQuery('#for-isak-definition-crop-code-check > input').prop('checked'),
            layer: checked[0],
            number: jQuery('#for-isak-definition-number > input').combobox('getValue'),
            ekatte: jQuery('#for-isak-definition-ekatte > input').combobox('getValue'),
            crop_name: jQuery('#for-isak-definition-crop-name > input').combobox('getValue'),
            crop_code: jQuery('#for-isak-definition-crop-code > input').combobox('getValue'),
        };

        TF.Rpc.Files.KVSDefinition.saveForIsakDefinition(saveObj)
        .done(function (data) {
            jQuery('#files-tables').datagrid('reload');
            jQuery('#win-for-isak-definition').window('close');
        })
        .fail(function (errorObj) {

        });

        return false;
    });

    jQuery('#win-definition').window({
        onClose: function() {
            jQuery('#definition-full-number-check > input').attr('checked', false);
            jQuery('#definition-ekatte-check > input').attr('checked', false);
            jQuery('#definition-masiv-check > input').attr('checked', false);
            jQuery('#definition-number-check > input').attr('checked', false);
            jQuery('#definition-category-check > input').attr('checked', false);
            jQuery('#definition-waytouse-check > input').attr('checked', false);
            jQuery('#definition-mestnost-check > input').attr('checked', false);
            jQuery('#definition-document-area-check > input').attr('checked', false);
        }
    });

    jQuery('#win-kms-definition').window({
        onClose: function() {
            jQuery('#definition-kms-number-check > input').attr('checked', true);
            jQuery('#kms-definition-ekatte-check > input').attr('checked', true);
            jQuery('#kms-definition-crop-name-check > input').attr('checked', true);
            jQuery('#kms-definition-crop-code-check > input').attr('checked', true);
        }
    });

    jQuery('#win-for-isak-definition').window({
        onClose: function() {
            jQuery('#definition-for-isak-number-check > input').attr('checked', true);
            jQuery('#for-isak-definition-ekatte-check > input').attr('checked', true);
            jQuery('#for-isak-definition-crop-name-check > input').attr('checked', true);
            jQuery('#for-isak-definition-crop-code-check > input').attr('checked', true);
        }
    });

    jQuery('#win-for-work-definition').window({
        onClose: function() {
            jQuery('#definition-for-work-number-check > input').attr('checked', true);
        }
    });

    jQuery('#win-for-gps-definition').window({
        onClose: function() {
            jQuery('#definition-for-gps-number-check > input').attr('checked', true);
        }
    });

    jQuery('#btn-clear').bind('click', function(){
        jQuery('#search-layer').val('');
        jQuery('#search-farming').combobox('reset');
        jQuery('#search-farming-year').combobox('reset');

        jQuery('#files-tables').datagrid({
            pageNumber: 1,
            rpcParams: [{}]
        });

        return false;
    });
    jQuery('#page-tabs').tabs({});
});

function initFileUploads(isKVS, isExcel = false){
    var fileUploadOption = jQuery("#file-upload-option");
    var mainFileUploadOption = jQuery("#main-file-upload-option");
    var projection = jQuery('#layer-projection > input').combobox('getValue');
    var winAddFile = jQuery("#win-add-file");
    var singleFileUpload = false;

    var winSatelliteFiles = winAddFile.window({
        onOpen: function () {
            var obj;

            if(isKVS) {
                obj = [{ value: '1', text: 'Официални данни от ОСЗ', selected: true },
                    { value: '3', text: 'Други данни'}];
            }
            else if(isExcel) {
                obj = [{ value: '5', text: 'Зареди в нов Работен слой', selected: true }];
            }
            else if(projection == LAYER_TYPE_TMP) {
                obj = [{ value: '0', text: 'Заместване на съществуващите', selected: true },
                    { value: '4', text: 'Зареждане към съществуващите' }];
            }
            else if(projection == LAYER_TYPE_KMS || projection == LAYER_TYPE_ISAK || projection == LAYER_TYPE_FOR_ISAK) {
                obj = [{ value: '0', text: '-' },
                    { value: '2', text: 'Други данни от ОСЗ' }];
            }
            else {
                obj = [{ value: '0', text: '-' },
                    { value: '1', text: 'Официални данни от ОСЗ' },
                    { value: '2', text: 'Други данни от ОСЗ' },
                    { value: '3', text: 'Други данни' }];
            }

            fileUploadOption.combobox({
                valueField: 'value',
                textField: 'text',
                editable: false,
                data: obj,
                onChange: function(el){
                    if((el == 0  && projection == LAYER_TYPE_TMP)){
                        singleFileUpload = true;
                        var uploader = jQuery("#uploader").pluploadQueue();
                        if(uploader.files.length > 1) {
                            uploader.splice(0, uploader.files.length);
                        }
                    } else {
                        singleFileUpload = false;
                    }

                }
            });

            mainFileUploadOption.show();

            winAddFile.window('resize', {
                height: 355,
                width: 450
            });
        }
    });

    var url  = "index.php?json=layers-upload";
    let params = {};

    if(!isKVS)
    {
        farming = jQuery('#layer-farming > input').combobox('getValue');
        year = jQuery('#layer-year > input').combobox('getValue');
        device = jQuery('#layer-device > input').combobox('getValue');

        params.projection = projection;
        params.farming = farming;
        params.year = year;
        params.device = device;
    }

    var filters = [
        {title : 'ZIP файлове', extensions : 'zip'},
    ];

    if(isExcel) {
        filters = [
            {title : "Eксел файлове", extensions : "xls,xlsx"}
        ];
    }

    jQuery("#uploader").pluploadQueue({

        // General settings
        runtimes : 'gears,html5,flash,silverlight,browserplus',
        filters : filters,
        url : url,
        max_file_size : '100mb',
        unique_names : true,

        // Flash settings
        flash_swf_url : 'lib/js_external/fileupload/plupload.flash.swf',
        init: {
            BeforeUpload: function (up, file) {
                var isComboboxBound = fileUploadOption.data().hasOwnProperty('combobox');
                up.settings.url = url;

                if(isComboboxBound) {
                    var valueFileUploadOption = fileUploadOption.combobox('getValue');

                    if (valueFileUploadOption == 1) {
                        if(isKVS) {
                            params.projection = 18;
                        }
                    }

                    if (valueFileUploadOption == 2) {
                        params.device = 3;
                        if(isKVS) {
                            params.projection = 4;
                        }
                    }

                    if (valueFileUploadOption == 3) {
                        if(isKVS) {
                            params.projection = 4;
                        }
                    }

                    if (valueFileUploadOption == 4) {
                        if(!isKVS) {
                            params.add_to_existing = true;
                        }
                    }
                    
                }

                if(isExcel) {          
                    params = {};
                    up.settings.url = "index.php?json=excel-upload";
                    params.projection = 23;
                }
                up.settings.multipart_params = params;
            },
            FilesAdded: function (uploader, files) {
                if(uploader.files.length > 1 && singleFileUpload) {
                    uploader.splice(1, files.length);
                    jQuery.messager.alert('Грешка', 'Не може да добавяте повече от един файл при опция: Заместване на съществуващите.');
                }

            }
        }
    });

    var uploader = jQuery('#uploader').pluploadQueue();
    uploader.bind('UploadComplete', function() {
        jQuery('#win-add-file').window('close');
        jQuery('#files-tables').datagrid('reload');
    });
}

function initProjection(){
    var projection_button =  jQuery('#projection-button > a');
    var projection_layer_combo = jQuery('#layer-projection > input');

    projection_button.linkbutton({
        iconCls:'icon-ok'
    });

    projection_button.bind('click', function () {
        var selectedType = projection_layer_combo.combobox('getValue');
        var data = projection_layer_combo.combobox('getData');
        var layer_type = null;

        data.forEach(function (row) {
            if (row.id == selectedType) {
                layer_type = row.layer_type;
            }
        });

        var obj = {
            type: layer_type,
            farming: jQuery('#layer-farming > input').combobox('getValue'),
            year: jQuery('#layer-year > input').combobox('getValue')
        };

        TF.Rpc.Files.FilesGrid.checkForExistingOperations(obj)
            .done(function (data) {
                initFileUploads();
                jQuery('#win-srid').window('close');
                jQuery('#win-add-file').window('open');
            })
            .fail(function (data) {
                jQuery.messager.alert('Грешка', 'В момента имате незавършено зареждане на файлове от избрания тип за избраното стопанство и стопанска година. След завършване на обработката им ще може да продължите със зареждането на нов файл.', 'warning');
                jQuery('#win-srid').window('close');
            });
        return false;
    });

    jQuery('#layer-farming > input').combobox({
        url:'index.php?common-rpc=farming-combobox',
        rpcParams: [{selected:true}],
        valueField: 'id',
        textField: 'name',
        editable: false,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#layer-year > input').combobox({
        url:'index.php?common-rpc=farming-year-combobox',
        rpcParams: [{selected: 'current'}],
        valueField: 'id',
        textField: 'title',
        editable: false,
        onLoadSuccess: function(){
            var data = jQuery(this).combobox('getData');
            jQuery(this).combobox('select', data[0]['id']);

        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#layer-device > input').combobox({
        url: 'index.php?common-rpc=device-type-combo',
        rpcParams: [{selected:true}],
        valueField: 'id',
        textField: 'title',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    projection_layer_combo.combobox({
        url:'index.php?common-rpc=projection',
        valueField: 'id',
        textField: 'name',
        editable: false,
        onLoadSuccess: function(){
            //selecting item so we can enter onSelect function and use the record variable
            var data = jQuery(this).combobox('getData');
            jQuery(this).combobox('select', data[0]['id']);

            if(data[0]['layer_type'] == 2) {
                jQuery('#layer-device > input').combobox('enable');
                jQuery('#layer-year > input').combobox('disable');
                jQuery('#layer-farming > input').combobox('disable');
            } else {
                jQuery('#layer-device > input').combobox('disable');
                jQuery('#layer-year > input').combobox('enable');
                jQuery('#layer-farming > input').combobox('enable');
            }
        },
        onSelect: function(record) {
            if(record['layer_type'] == 2 || record['layer_type'] == 19) {
                jQuery('#layer-device > input').combobox('enable');
                jQuery('#layer-year > input').combobox('disable');
                jQuery('#layer-farming > input').combobox('disable');

            } else if(record['layer_type'] == 9) {
                jQuery('#layer-device > input').combobox('disable');
                jQuery('#layer-farming > input').combobox('enable');

                jQuery('#layer-year > input').combobox({
                    disabled: false,
                    loadFilter: function(data) {
                        return jQuery.grep(data.result, function(el) {
                            return el.id > 5;
                        });
                    }
                });

            } else {
                jQuery('#layer-device > input').combobox('disable');
                jQuery('#layer-farming > input').combobox('enable');

                jQuery('#layer-year > input').combobox({
                    disabled: false,
                    loadFilter: function(data) {
                        return data.result;
                    }
                });
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function setValidateSearchInput() {
    jQuery.extend(jQuery.fn.validatebox.defaults.rules, {
        minLength: {
            validator: function(value, param){
                return value.length >= param[0];
            },
            message: 'Въведете минимум {0} символа.'
        }
    });

    jQuery('#search-layer').validatebox({
        validType:'minLength[3]'
    });

    jQuery('#search-layer').keypress(function(event) {
        if ( event.which == 13 ) {
            jQuery('#layers-tables').datagrid({
                rpcParams: [{
                    name: jQuery('#search-layer').val()
                }]
            });
        }
    });
}

function enableDefinition(sender,id){
    if(sender.checked){

        if(id == 'definition-full-number'){
            jQuery('#definition-ekatte-check > input').attr("disabled", true);
            jQuery('#definition-masiv-check > input').attr("disabled", true);
            jQuery('#definition-number-check > input').attr("disabled", true);
        }
        if(id == 'definition-ekatte' || id == 'definition-masiv' || id == 'definition-number'){
            jQuery('#definition-full-number-check > input').attr("disabled", true);
        }
        jQuery('#'+id+' > input').combobox('enable');
    }
    else {
        if(id == 'definition-full-number'){
            jQuery('#definition-ekatte-check > input').removeAttr("disabled");
            jQuery('#definition-masiv-check > input').removeAttr("disabled");
            jQuery('#definition-number-check > input').removeAttr("disabled");
        }

        if(id == 'definition-ekatte' || id == 'definition-masiv' || id == 'definition-number'){
            var isChekedEkatte = jQuery('#definition-ekatte-check > input').is(":checked");
            var isChekedMasiv = jQuery('#definition-masiv-check > input').is(":checked");
            var isChekedNumber = jQuery('#definition-number-check > input').is(":checked");

            if(!isChekedEkatte && !isChekedMasiv && !isChekedNumber){

                jQuery('#definition-full-number-check > input').removeAttr("disabled");
            }
        }
        jQuery('#'+id+' > input').combobox('disable');
    }
}

function initKVSDefinition(){
    jQuery('#definition-ekatte > input').combobox({
        data: jQuery('#definition-full-number > input').combobox('getData'),
        valueField: 'column_name',
        textField: 'column_name',
        required: true,
        editable: false,
        disabled:true
    });

    jQuery('#definition-masiv > input').combobox({
        data: jQuery('#definition-full-number > input').combobox('getData'),
        valueField: 'column_name',
        textField: 'column_name',
        required: true,
        editable: false,
        disabled:true
    });

    jQuery('#definition-number > input').combobox({
        data: jQuery('#definition-full-number > input').combobox('getData'),
        valueField: 'column_name',
        textField: 'column_name',
        required: true,
        editable: false,
        disabled:true
    });

    jQuery('#definition-cat > input').combobox({
        data: jQuery('#definition-full-number > input').combobox('getData'),
        valueField: 'column_name',
        textField: 'column_name',
        required: true,
        editable: false,
        disabled:true
    });

    jQuery('#definition-way > input').combobox({
        data: jQuery('#definition-full-number > input').combobox('getData'),
        valueField: 'column_name',
        textField: 'column_name',
        required: true,
        editable: false,
        disabled:true
    });

    jQuery('#definition-mestnost > input').combobox({
        data: jQuery('#definition-full-number > input').combobox('getData'),
        valueField: 'column_name',
        textField: 'column_name',
        required: true,
        editable: false,
        disabled:true
    });

    jQuery('#definition-document-area > input').combobox({
        data: jQuery('#definition-full-number > input').combobox('getData'),
        valueField: 'column_name',
        textField: 'column_name',
        required: true,
        editable: false,
        disabled:true
    });
}

function initKMSDefinition(){
    jQuery('#kms-definition-ekatte > input').combobox({
        data: jQuery('#kms-definition-number > input').combobox('getData'),
        valueField: 'column_name',
        textField: 'column_name',
        required: true,
        editable: false,
        disabled: false,
        onLoadSuccess: function() {
            var tmpData = jQuery('#kms-definition-number > input').combobox('getData');
            for (var i = tmpData.length - 1; i >= 0; i--) {
                if(tmpData[i].column_name === 'ekatte') {
                    jQuery('#kms-definition-ekatte > input').combobox('select', tmpData[i].column_name);
                    break;
                }
            }
        }
    });

    jQuery('#kms-definition-crop-name > input').combobox({
        data: jQuery('#kms-definition-number > input').combobox('getData'),
        valueField: 'column_name',
        textField: 'column_name',
        required: true,
        editable: false,
        disabled: false,
        onLoadSuccess: function() {
            var tmpData = jQuery('#kms-definition-number > input').combobox('getData');
            for (var i = tmpData.length - 1; i >= 0; i--) {
                if(tmpData[i].column_name === 'crop_name') {
                    jQuery('#kms-definition-crop-name > input').combobox('select', tmpData[i].column_name);
                    break;
                }
            }
        }
    });

    jQuery('#kms-definition-crop-code > input').combobox({
        data: jQuery('#kms-definition-number > input').combobox('getData'),
        valueField: 'column_name',
        textField: 'column_name',
        required: true,
        editable: false,
        disabled: false,
        onLoadSuccess: function() {
            var tmpData = jQuery('#kms-definition-number > input').combobox('getData');
            for (var i = tmpData.length - 1; i >= 0; i--) {
                if(tmpData[i].column_name === 'crop_code') {
                    jQuery('#kms-definition-crop-code > input').combobox('select', tmpData[i].column_name);
                    break;
                }
            }
        }
    });
}

function initForIsakDefinition(){
    jQuery('#for-isak-definition-ekatte > input').combobox({
        data: jQuery('#for-isak-definition-number > input').combobox('getData'),
        valueField: 'column_name',
        textField: 'column_name',
        required: true,
        editable: false,
        disabled: false,
        onLoadSuccess: function() {
            var tmpData = jQuery('#for-isak-definition-number > input').combobox('getData');
            for (var i = tmpData.length - 1; i >= 0; i--) {
                if(tmpData[i].column_name === 'ekatte') {
                    jQuery('#for-isak-definition-ekatte > input').combobox('select', tmpData[i].column_name);
                    break;
                }
            }
        }
    });

    jQuery('#for-isak-definition-crop-name > input').combobox({
        data: jQuery('#for-isak-definition-number > input').combobox('getData'),
        valueField: 'column_name',
        textField: 'column_name',
        required: true,
        editable: false,
        disabled: false,
        onLoadSuccess: function() {
            var tmpData = jQuery('#for-isak-definition-number > input').combobox('getData');
            for (var i = tmpData.length - 1; i >= 0; i--) {
                if(tmpData[i].column_name === 'cropname') {
                    jQuery('#for-isak-definition-crop-name > input').combobox('select', tmpData[i].column_name);
                    break;
                }
            }
        }
    });

    jQuery('#for-isak-definition-crop-code > input').combobox({
        data: jQuery('#for-isak-definition-number > input').combobox('getData'),
        valueField: 'column_name',
        textField: 'column_name',
        required: true,
        editable: false,
        disabled: false,
        onLoadSuccess: function() {
            var tmpData = jQuery('#for-isak-definition-number > input').combobox('getData');
            for (var i = tmpData.length - 1; i >= 0; i--) {
                if(tmpData[i].column_name === 'cropcode') {
                    jQuery('#for-isak-definition-crop-code > input').combobox('select', tmpData[i].column_name);
                    break;
                }
            }
        }
    });
}
