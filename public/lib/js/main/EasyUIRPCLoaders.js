
(function(root, factory) {
    if (typeof define === "function" && define.amd) {
        // AMD. Register as an anonymous module.
        define(["easyui", 'TF/Rpc/Login/LoginForm'], factory);
    } else {
        // Browser globals (root is window)

        var tmpRpc = { ...root.EasyUIRPCLoaders};
        root.EasyUIRPCLoaders = factory(TF.Rpc.Login.LoginForm);
        Object.assign(root.EasyUIRPCLoaders, tmpRpc);
    }
})(typeof self !== "undefined" ? self : this, function(LoginForm) {
    var EasyUIGridCustomLoader = {
        loader: function(params, succ, error) {
            var boundWidgets = jQuery(this).data();
            var options = {};
            var paging = [];

            if (boundWidgets.hasOwnProperty("tree")) {
                options = jQuery(this).tree("options");
                paging = [
                    options.page,
                    options.rows,
                    options.sort,
                    options.order
                ];
            }
            if (boundWidgets.hasOwnProperty("datagrid")) {
                options = jQuery(this).datagrid("options");
                paging = [
                    params.page,
                    params.rows,
                    params.sort,
                    params.order
                ];
            }

            if (boundWidgets.hasOwnProperty("combobox")) {
                options = jQuery(this).combobox("options");
            }

            if (boundWidgets.hasOwnProperty("treegrid")) {
                options = jQuery(this).treegrid("options");
            }

            if (boundWidgets.hasOwnProperty("combotree")) {
                options = jQuery(this).combotree("options");
            }

            var rpcQuery = options.rpcParams || [];
            var rpcAllParams = rpcQuery.concat(paging);
            var rpcRequestedMethod = options.rpcMethod || "read";
            var params = JSON.stringify({
                method: rpcRequestedMethod,
                params: rpcAllParams,
                id: 1,
                jsonrpc: "2.0"
            });

            var url = options.url;

            if (!url) {
                return false;
            }

            jQuery
                .ajax({
                    url: url,
                    data: params,
                    accepts: "application/json",
                    contentType: "application/json",
                    method: "post",
                    dataType: "json",
                    processData: false
                })
                .done(succ)
                .fail(function(data) {
                    if (data.status === 401) {
                        if(jQuery('#login-win').window('options').closed){
                            LoginForm.appLogout().done(function () {
                                window.location.href = "index.php?page=Home"
                            })
                        }
                    }
                });
        },
        loadFilter: function(data) {
            var boundWidgets = jQuery(this).data();

            if (typeof data.result === "undefined") {
                return data || [];
            } else {
                var isDataGrid = boundWidgets.hasOwnProperty("datagrid");

                if (
                    isDataGrid &&
                    data &&
                    data.result &&
                    data.result.columns !== undefined
                ) {
                    var opts = jQuery(this).datagrid("options");
                    var url = opts.url;
                    jQuery(this).datagrid({
                        columns: data.result.columns,
                        url: null
                    });
                    setTimeout(function() {
                        opts.url = url;
                    }, 0);
                }
                return data.result || [];
            }
        },
        loadRpc: function(element, params) {
            var boundWidgets = jQuery(element).data();

            if (boundWidgets.hasOwnProperty("datagrid")) {
                jQuery(element).datagrid({
                    rpcParams: params
                });
            }
            if (boundWidgets.hasOwnProperty("combobox")) {
                jQuery(element).combobox({
                    rpcParams: params
                });
            }
            if (boundWidgets.hasOwnProperty("tree")) {
                jQuery(element).tree({
                    rpcParams: params
                });
            }
            if (boundWidgets.hasOwnProperty("treegrid")) {
                jQuery(element).treegrid({
                    rpcParams: params
                });
            }
            if (boundWidgets.hasOwnProperty("combotree")) {
                jQuery(element).combotree({
                    rpcParams: params
                });
            }
        }
    };


    jQuery.extend(jQuery.fn.datagrid.methods, {
	    loadRpc: EasyUIGridCustomLoader.loadRpc
    });

    jQuery.extend(jQuery.fn.tree.methods, {
        loadRpc: EasyUIGridCustomLoader.loadRpc
    });

    jQuery.extend(jQuery.fn.combobox.methods, {
        loadRpc: EasyUIGridCustomLoader.loadRpc
    });

    jQuery.extend(jQuery.fn.combotree.methods, {
        loadRpc: EasyUIGridCustomLoader.loadRpc
    });


    /**
     * Makes an RPC formated requests.
     *
     * @param  serviceId string The "id" attribute of the <rpcapi/> from application.xml
     * @param  apiMethod string The name of the method we want to call.
     * @return object           Returns a jQuery Deferred Object.
     * @see  http://api.jquery.com/category/deferred-object/
     */
    function rpcRequest(serviceId, apiMethod) {
        var params = Array.prototype.slice.call(arguments, 2);
        var rpcParams = JSON.stringify({
            "method": apiMethod,
            "params": params,
            "id": 1,
            "jsonrpc": "2.0"
        });

        var promise = jQuery.ajax({
            statusCode: {
                499: function(xhr) {
                    var errMsgs = ERROR_REQUEST_MSGS[xhr.statusText];
                    jQuery.messager.alert('Грешка', errMsgs, 'warning');
                    //endLoading();
                }
            },
            url: "index.php?rpc="+serviceId,
            data: rpcParams,
            accepts: 'application/json',
            contentType: 'application/json',
            method: 'post',
            dataType: 'json',
            processData: false
        }).done(function () {
        });
        return promise;
    }

    /**
     * Makes an RPC formated requests.
     * @param  string  moduleName The "id" attribute of the <service> from application.xml, without "rpc-"
     * @param  integer serviceId  The "id" attribute of the <rpcapi/> from application.xml
     * @param  integer apiMethod  The name of the method we want to call.
     * @return object             Returns a jQuery Deferred Object.
     * @see  http://api.jquery.com/category/deferred-object/
     */
    function rpcModuleRequest(moduleName, serviceId, apiMethod) {
        var params = Array.prototype.slice.call(arguments, 3);
        var rpcParams = JSON.stringify({
            "method": apiMethod,
            "params": params,
            "id": 1,
            "jsonrpc": "2.0"
        });
        var promise = jQuery.ajax({
            url: "index.php?"+moduleName+"-rpc="+serviceId,
            data: rpcParams,
            accepts: 'application/json',
            contentType: 'application/json',
            method: 'post',
            dataType: 'json',
            processData: false
        }).done(function () {
        });
        return promise;
    }



    return {
        ERROR_REQUEST_MSGS: {
            'no_module_rights': 'Нямате права да оперирате с тази функционалност!'
        },
        messagerNoRightsRW: function() {
            var errMsgs = ERROR_REQUEST_MSGS['no_module_rights'];
            jQuery.messager.alert('Грешка', errMsgs, 'warning');
        },
        rpcModuleRequest,
        rpcRequest,
        EasyUIGridCustomLoader
    }
});
