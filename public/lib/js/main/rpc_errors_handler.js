(function(root, factory) {
    if (typeof define === "function" && define.amd) {
        // AMD. Register as an anonymous module.
        define(["jquery", "easyui"], factory);
    } else {
        // Browser globals (root is window)
        root.RpcErrorHandler = factory(jQuery);
    }
})(typeof self !== "undefined" ? self : this, function(jQuery) {
    var fields = {
        ekate: "ЕКАТТЕ",
        masiv: "Масив",
        number: "Имот",
        imot: "Имот",
        document_area: "Площ по дог.",
        mestnost: "Местност",
        kad_ident: "Идентификатор",
        category: "Категория",
        area_type: "НТП",
        area_year: "Година",
        cnum: "Номер на договор",
        contract_type: "Тип на договор",
        contract_status: "Статус",
        farming: "Стопанство",
        owner_name: "<PERSON>ме",
        owner_egn: "ЕГ<PERSON>",
        rep_name: "<PERSON><PERSON><PERSON>",
        rep_egn: "ЕГ<PERSON>",
        company_name: "Име на фирма",
        company_eik: "ЕИК",
        name: "Име(Номер)",
        surname: "Презиме",
        lastname: "Фамилия",
        egn: "ЕГН",
        lk_nomer: "ЛК Номер",
        lk_izdavane: "ЛК Издадена от/на",
        eik: "ЕИК",
        mol: "МОЛ",
        company_address: "Адрес",
        phone: "Телефон",
        fax: "Факс",
        mobile: "Мобилен",
        email: "E-mail",
        iban: "Банкова сметка",
        address: "Адрес",
        rent_place: "Място за получаване на рента",
        renta_type_name: "Име",
        renta_type_unit: "Мерна единица",
        template_name: "Име",
        template_body: "Съдържание",
        sv_date: "Служба по вписванията - дата",
        start_date: "Влизане в сила",
        due_date: "Крайна дата",
        dobivi: "Добиви",
        napoqvane: "Напояване",
        polivki: "Поливки",
        polzvatel: "Ползвател",
        isak: "ИСАК номер",
        obrabotki: "Обработки",
        description: "Коментар",
        perf_title: "Длъжност",
        pest_name: "Вредител"
    };

    var errorCodes = {
        VALIDATION_VALID_FIELD: 200,
        VALIDATION_INVALID_KAD_IDENT: -33000,
        VALIDATION_INVALID_DIGITS_ONLY: -33001,
        VALIDATION_INVALID_NUMBER: -33002,
        VALIDATION_INVALID_TEXT: -33003,
        VALIDATION_INVALID_REQUIRED: -33004,
        VALIDATION_INVALID_NOT_NULL: -33005,
        VALIDATION_INVALID_INTEGER: -33006,
        VALIDATION_INVALID_DATE: -33007,
        VALIDATION_INVALID_BOOLEAN: -33008,
        VALIDATION_INVALID_TIMESTAMP: -33009,
        VALIDATION_INVALID_FARMING_YEAR: -33010,
        VALIDATION_INVALID_CONTRACT_TYPE: -33011,
        DUPLICATION_ERROR: -33011,
        DATABASE_CONNECTION_ERROR: -33101
    };
    var errorMessages = [];
    errorMessages[errorCodes.VALIDATION_INVALID_KAD_IDENT] =
        "може да съдържа само цифри от 0 до 9";
    errorMessages[errorCodes.VALIDATION_INVALID_DIGITS_ONLY] =
        "може да съдържа само цифри от 0 до 9";
    errorMessages[errorCodes.VALIDATION_INVALID_NUMBER] =
        "може да съдържа само неотрицателни числа";
    errorMessages[errorCodes.VALIDATION_INVALID_TEXT] =
        "не може да съдържа специални символи";
    errorMessages[errorCodes.VALIDATION_INVALID_REQUIRED] =
        "не може да бъде празно";
    errorMessages[errorCodes.VALIDATION_INVALID_NOT_NULL] =
        "не може да бъде null";
    errorMessages[errorCodes.VALIDATION_INVALID_INTEGER] =
        "може да съдържа само цели, неотрицателни числа";
    errorMessages[errorCodes.DATABASE_CONNECTION_ERROR] =
        "Грешка при връзка с базата данни";
    errorMessages[errorCodes.VALIDATION_INVALID_DATE] =
        "съдържа невалиден формат за дата";
    errorMessages[errorCodes.VALIDATION_INVALID_FARMING_YEAR] =
        "съдържа невалиден формат за стопанска година";
    errorMessages[errorCodes.VALIDATION_INVALID_CONTRACT_TYPE] =
        "съдържа невалиден формат за тип договор";
    errorMessages[errorCodes.DUPLICATION_ERROR] =
        "съдържа стойност, която вече съществува";

    return {
        show: function(errorObj) {
            if (errorObj.getCode() == errorCodes.DATABASE_CONNECTION_ERROR) {
                jQuery.messager.alert(
                    "Грешка",
                    "Възникна грешка при връзка с базата данни.",
                    "warning"
                );
            } else if (
                errorMessages[errorObj.getCode()] !== undefined &&
                fields[errorObj.getData()] !== undefined
            ) {
                jQuery.messager.alert(
                    "Грешка",
                    'Полето "' +
                        fields[errorObj.getData()] +
                        '" ' +
                        errorMessages[errorObj.getCode()] +
                        ".",
                    "warning"
                );
            } else {
                jQuery.messager.alert(
                    "Грешка",
                    "Възникна грешка при обработката на данните.",
                    "warning"
                );
            }
        }
    };
});
