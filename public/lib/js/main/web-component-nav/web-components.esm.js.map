{"version": 3, "names": ["patchBrowser", "importMeta", "url", "opts", "resourcesUrl", "URL", "href", "promiseResolve", "then", "async", "options", "globalScripts", "bootstrapLazy", "mainNav", "activeRoute", "domain", "expandable", "expanded", "logo", "menuState", "translations", "set<PERSON><PERSON><PERSON><PERSON>", "setActiveRoute", "setDomain", "set<PERSON><PERSON>"], "sources": ["../../../node_modules/@stencil/core/internal/client/patch-browser.js", "@lazy-browser-entrypoint?app-data=conditional"], "sourcesContent": ["/*\n Stencil Client Patch Browser v4.16.0 | MIT Licensed | https://stenciljs.com\n */\nimport { BUILD, NAMESPACE } from '@stencil/core/internal/app-data';\nimport { consoleDevInfo, doc, promiseResolve, H } from '@stencil/core';\nconst patchBrowser = () => {\n    // NOTE!! This fn cannot use async/await!\n    if (BUILD.isDev && !BUILD.isTesting) {\n        consoleDevInfo('Running in development mode.');\n    }\n    if (BUILD.cloneNodeFix) {\n        // opted-in to polyfill cloneNode() for slot polyfilled components\n        patchCloneNodeFix(H.prototype);\n    }\n    const scriptElm = BUILD.scriptDataOpts\n        ? Array.from(doc.querySelectorAll('script')).find((s) => new RegExp(`\\/${NAMESPACE}(\\\\.esm)?\\\\.js($|\\\\?|#)`).test(s.src) ||\n            s.getAttribute('data-stencil-namespace') === NAMESPACE)\n        : null;\n    const importMeta = import.meta.url;\n    const opts = BUILD.scriptDataOpts ? (scriptElm || {})['data-opts'] || {} : {};\n    if (importMeta !== '') {\n        opts.resourcesUrl = new URL('.', importMeta).href;\n    }\n    return promiseResolve(opts);\n};\nconst patchCloneNodeFix = (HTMLElementPrototype) => {\n    const nativeCloneNodeFn = HTMLElementPrototype.cloneNode;\n    HTMLElementPrototype.cloneNode = function (deep) {\n        if (this.nodeName === 'TEMPLATE') {\n            return nativeCloneNodeFn.call(this, deep);\n        }\n        const clonedNode = nativeCloneNodeFn.call(this, false);\n        const srcChildNodes = this.childNodes;\n        if (deep) {\n            for (let i = 0; i < srcChildNodes.length; i++) {\n                // Node.ATTRIBUTE_NODE === 2, and checking because IE11\n                if (srcChildNodes[i].nodeType !== 2) {\n                    clonedNode.appendChild(srcChildNodes[i].cloneNode(true));\n                }\n            }\n        }\n        return clonedNode;\n    };\n};\nexport { patchBrowser };\n", "export { setNonce } from '@stencil/core';\nimport { bootstrapLazy } from '@stencil/core';\nimport { patchBrowser } from '@stencil/core/internal/client/patch-browser';\nimport { globalScripts } from '@stencil/core/internal/app-globals';\npatchBrowser().then(async (options) => {\n  await globalScripts();\n  return bootstrapLazy([/*!__STENCIL_LAZY_DATA__*/], options);\n});\n"], "mappings": "0HAKA,MAAMA,EAAe,KAajB,MAAMC,cAAyBC,IAC/B,MAAMC,EAAqE,GAC3E,GAAIF,IAAe,GAAI,CACnBE,EAAKC,aAAe,IAAIC,IAAI,IAAKJ,GAAYK,IACrD,CACI,OAAOC,EAAeJ,EAAK,ECnB/BH,IAAeQ,MAAKC,MAAOC,UACnBC,IACN,OAAOC,EAAc,8BAA8B,CAAAC,QAAS,KAAAC,YAAA,sBAAAC,OAAA,OAAAC,WAAA,OAAAC,SAAA,OAAAC,KAAA,OAAAC,UAAA,KAAAC,aAAA,KAAAC,WAAA,KAAAC,eAAA,KAAAC,UAAA,KAAAC,QAAA,YAAAV,YAAA,wBAAAD,QAAA,yBAAAH,EAAA"}