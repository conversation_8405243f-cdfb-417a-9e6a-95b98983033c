import{p as a,b as e}from"./p-dcacdc5a.js";export{s as setNonce}from"./p-dcacdc5a.js";import{g as t}from"./p-e1255160.js";const n=()=>{const e=import.meta.url;const t={};if(e!==""){t.resourcesUrl=new URL(".",e).href}return a(t)};n().then((async a=>{await t();return e([["p-4f2dd57a",[[1,"main-nav",{mainNav:[16],activeRoute:[1025,"active-route"],domain:[1025],expandable:[1028],expanded:[1028],logo:[1040],menuState:[32],translations:[32],setMainNav:[64],setActiveRoute:[64],setDomain:[64],setLogo:[64]},null,{activeRoute:["onActiveRouteChange"],mainNav:["onMainNavChange"]}]]]],a)}));
//# sourceMappingURL=web-components.esm.js.map