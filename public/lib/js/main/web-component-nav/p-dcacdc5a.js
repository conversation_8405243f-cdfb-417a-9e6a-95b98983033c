const t="web-components";let e;let n;let s=false;let o=false;const l=t=>{const e=new URL(t,gt.t);return e.origin!==wt.location.origin?e.href:e.pathname};const c=(t,e="")=>{{return()=>{}}};const i=(t,e)=>{{return()=>{}}};const f="{visibility:hidden}.hydrated{visibility:inherit}";const r="slot-fb{display:contents}slot-fb[hidden]{display:none}";const u={};const a="http://www.w3.org/2000/svg";const d="http://www.w3.org/1999/xhtml";const v=t=>t!=null;const p=t=>{t=typeof t;return t==="object"||t==="function"};function h(t){var e,n,s;return(s=(n=(e=t.head)===null||e===void 0?void 0:e.querySelector('meta[name="csp-nonce"]'))===null||n===void 0?void 0:n.getAttribute("content"))!==null&&s!==void 0?s:undefined}const y=(t,e,...n)=>{let s=null;let o=null;let l=false;let c=false;const i=[];const f=e=>{for(let n=0;n<e.length;n++){s=e[n];if(Array.isArray(s)){f(s)}else if(s!=null&&typeof s!=="boolean"){if(l=typeof t!=="function"&&!p(s)){s=String(s)}if(l&&c){i[i.length-1].o+=s}else{i.push(l?m(null,s):s)}c=l}}};f(n);if(e){if(e.key){o=e.key}{const t=e.className||e.class;if(t){e.class=typeof t!=="object"?t:Object.keys(t).filter((e=>t[e])).join(" ")}}}const r=m(t,null);r.l=e;if(i.length>0){r.i=i}{r.u=o}return r};const m=(t,e)=>{const n={v:0,p:t,o:e,h:null,i:null};{n.l=null}{n.u=null}return n};const b={};const w=t=>t&&t.p===b;const $=(t,e)=>{if(t!=null&&!p(t)){if(e&4){return t==="false"?false:t===""||!!t}if(e&1){return String(t)}return t}return t};const g=t=>at(t).$hostElement$;const S=(t,e,n)=>{const s=g(t);return{emit:t=>j(s,e,{bubbles:!!(n&4),composed:!!(n&2),cancelable:!!(n&1),detail:t})}};const j=(t,e,n)=>{const s=gt.ce(e,n);t.dispatchEvent(s);return s};const O=new WeakMap;const k=(t,e,n)=>{let s=bt.get(t);if(jt&&n){s=s||new CSSStyleSheet;if(typeof s==="string"){s=e}else{s.replaceSync(e)}}else{s=e}bt.set(t,s)};const C=(t,e,n)=>{var s;const o=P(e);const l=bt.get(o);t=t.nodeType===11?t:$t;if(l){if(typeof l==="string"){t=t.head||t;let n=O.get(t);let c;if(!n){O.set(t,n=new Set)}if(!n.has(o)){{c=$t.createElement("style");c.innerHTML=l;const e=(s=gt.m)!==null&&s!==void 0?s:h($t);if(e!=null){c.setAttribute("nonce",e)}t.insertBefore(c,t.querySelector("link"))}if(e.v&4){c.innerHTML+=r}if(n){n.add(o)}}}else if(!t.adoptedStyleSheets.includes(l)){t.adoptedStyleSheets=[...t.adoptedStyleSheets,l]}}return o};const M=t=>{const e=t.$;const n=t.$hostElement$;const s=e.v;const o=c("attachStyles",e.S);const l=C(n.shadowRoot?n.shadowRoot:n.getRootNode(),e);if(s&10){n["s-sc"]=l;n.classList.add(l+"-h")}o()};const P=(t,e)=>"sc-"+t.S;const x=(t,e,n,s,o,l)=>{if(n!==s){let c=pt(t,e);let i=e.toLowerCase();if(e==="class"){const e=t.classList;const o=L(n);const l=L(s);e.remove(...o.filter((t=>t&&!l.includes(t))));e.add(...l.filter((t=>t&&!o.includes(t))))}else if(e==="key");else if(!c&&e[0]==="o"&&e[1]==="n"){if(e[2]==="-"){e=e.slice(3)}else if(pt(wt,i)){e=i.slice(2)}else{e=i[2]+e.slice(3)}if(n||s){const o=e.endsWith(U);e=e.replace(R,"");if(n){gt.rel(t,e,n,o)}if(s){gt.ael(t,e,s,o)}}}else{const i=p(s);if((c||i&&s!==null)&&!o){try{if(!t.tagName.includes("-")){const o=s==null?"":s;if(e==="list"){c=false}else if(n==null||t[e]!=o){t[e]=o}}else{t[e]=s}}catch(t){}}if(s==null||s===false){if(s!==false||t.getAttribute(e)===""){{t.removeAttribute(e)}}}else if((!c||l&4||o)&&!i){s=s===true?"":s;{t.setAttribute(e,s)}}}}};const E=/\s/;const L=t=>!t?[]:t.split(E);const U="Capture";const R=new RegExp(U+"$");const A=(t,e,n,s)=>{const o=e.h.nodeType===11&&e.h.host?e.h.host:e.h;const l=t&&t.l||u;const c=e.l||u;{for(s of N(Object.keys(l))){if(!(s in c)){x(o,s,l[s],undefined,n,e.v)}}}for(s of N(Object.keys(c))){x(o,s,l[s],c[s],n,e.v)}};function N(t){return t.includes("ref")?[...t.filter((t=>t!=="ref")),"ref"]:t}const T=(t,o,l,c)=>{const i=o.i[l];let f=0;let r;let u;if(i.o!==null){r=i.h=$t.createTextNode(i.o)}else{if(!s){s=i.p==="svg"}r=i.h=$t.createElementNS(s?a:d,i.p);if(s&&i.p==="foreignObject"){s=false}{A(null,i,s)}if(v(e)&&r["s-si"]!==e){r.classList.add(r["s-si"]=e)}if(i.i){for(f=0;f<i.i.length;++f){u=T(t,i,f);if(u){r.appendChild(u)}}}{if(i.p==="svg"){s=false}else if(r.tagName==="foreignObject"){s=true}}}r["s-hn"]=n;return r};const W=(t,e,s,o,l,c)=>{let i=t;let f;if(i.shadowRoot&&i.tagName===n){i=i.shadowRoot}for(;l<=c;++l){if(o[l]){f=T(null,s,l);if(f){o[l].h=f;i.insertBefore(f,e)}}}};const q=(t,e,n)=>{for(let s=e;s<=n;++s){const e=t[s];if(e){const t=e.h;if(t){t.remove()}}}};const D=(t,e,n,s,o=false)=>{let l=0;let c=0;let i=0;let f=0;let r=e.length-1;let u=e[0];let a=e[r];let d=s.length-1;let v=s[0];let p=s[d];let h;let y;while(l<=r&&c<=d){if(u==null){u=e[++l]}else if(a==null){a=e[--r]}else if(v==null){v=s[++c]}else if(p==null){p=s[--d]}else if(F(u,v,o)){H(u,v,o);u=e[++l];v=s[++c]}else if(F(a,p,o)){H(a,p,o);a=e[--r];p=s[--d]}else if(F(u,p,o)){H(u,p,o);t.insertBefore(u.h,a.h.nextSibling);u=e[++l];p=s[--d]}else if(F(a,v,o)){H(a,v,o);t.insertBefore(a.h,u.h);a=e[--r];v=s[++c]}else{i=-1;{for(f=l;f<=r;++f){if(e[f]&&e[f].u!==null&&e[f].u===v.u){i=f;break}}}if(i>=0){y=e[i];if(y.p!==v.p){h=T(e&&e[c],n,i)}else{H(y,v,o);e[i]=undefined;h=y.h}v=s[++c]}else{h=T(e&&e[c],n,c);v=s[++c]}if(h){{u.h.parentNode.insertBefore(h,u.h)}}}}if(l>r){W(t,s[d+1]==null?null:s[d+1].h,n,s,c,d)}else if(c>d){q(e,l,r)}};const F=(t,e,n=false)=>{if(t.p===e.p){if(!n){return t.u===e.u}return true}return false};const H=(t,e,n=false)=>{const o=e.h=t.h;const l=t.i;const c=e.i;const i=e.p;const f=e.o;if(f===null){{s=i==="svg"?true:i==="foreignObject"?false:s}{{A(t,e,s)}}if(l!==null&&c!==null){D(o,l,e,c,n)}else if(c!==null){if(t.o!==null){o.textContent=""}W(o,null,e,c,0,c.length-1)}else if(l!==null){q(l,0,l.length-1)}if(s&&i==="svg"){s=false}}else if(t.o!==f){o.data=f}};const I=(t,s,o=false)=>{const l=t.$hostElement$;const c=t.j||m(null,null);const i=w(s)?s:y(null,null,s);n=l.tagName;if(o&&i.l){for(const t of Object.keys(i.l)){if(l.hasAttribute(t)&&!["key","ref","style","class"].includes(t)){i.l[t]=l[t]}}}i.p=null;i.v|=4;t.j=i;i.h=c.h=l.shadowRoot||l;{e=l["s-sc"]}H(c,i,o)};const V=(t,e)=>{if(e&&!t.O&&e["s-p"]){e["s-p"].push(new Promise((e=>t.O=e)))}};const _=(t,e)=>{{t.v|=16}if(t.v&4){t.v|=512;return}V(t,t.k);const n=()=>z(t,e);return Et(n)};const z=(t,e)=>{const n=c("scheduleUpdate",t.$.S);const s=t.C;let o;if(e){{o=Y(s,"componentWillLoad")}}n();return B(o,(()=>J(t,s,e)))};const B=(t,e)=>G(t)?t.then(e):e();const G=t=>t instanceof Promise||t&&t.then&&typeof t.then==="function";const J=async(t,e,n)=>{var s;const o=t.$hostElement$;const l=c("update",t.$.S);const i=o["s-rc"];if(n){M(t)}const f=c("render",t.$.S);{K(t,e,o,n)}if(i){i.map((t=>t()));o["s-rc"]=undefined}f();l();{const e=(s=o["s-p"])!==null&&s!==void 0?s:[];const n=()=>Q(t);if(e.length===0){n()}else{Promise.all(e).then(n);t.v|=4;e.length=0}}};const K=(t,e,n,s)=>{try{e=e.render();{t.v&=~16}{t.v|=2}{{{I(t,e,s)}}}}catch(e){ht(e,t.$hostElement$)}return null};const Q=t=>{const e=t.$.S;const n=t.$hostElement$;const s=c("postUpdate",e);const o=t.C;const l=t.k;if(!(t.v&64)){t.v|=64;{Z(n)}{Y(o,"componentDidLoad")}s();{t.M(n);if(!l){X()}}}else{s()}{t.P(n)}{if(t.O){t.O();t.O=undefined}if(t.v&512){xt((()=>_(t,false)))}t.v&=~(4|512)}};const X=e=>{{Z($t.documentElement)}xt((()=>j(wt,"appload",{detail:{namespace:t}})))};const Y=(t,e,n)=>{if(t&&t[e]){try{return t[e](n)}catch(t){ht(t)}}return undefined};const Z=t=>t.classList.add("hydrated");const tt=(t,e)=>at(t).L.get(e);const et=(t,e,n,s)=>{const o=at(t);const l=o.$hostElement$;const c=o.L.get(e);const i=o.v;const f=o.C;n=$(n,s.U[e][0]);const r=Number.isNaN(c)&&Number.isNaN(n);const u=n!==c&&!r;if((!(i&8)||c===undefined)&&u){o.L.set(e,n);if(f){if(s.R&&i&128){const t=s.R[e];if(t){t.map((t=>{try{f[t](n,c,e)}catch(t){ht(t,l)}}))}}if((i&(2|16))===2){_(o,false)}}}};const nt=(t,e,n)=>{var s;const o=t.prototype;if(e.U){if(t.watchers){e.R=t.watchers}const l=Object.entries(e.U);l.map((([t,[s]])=>{if(s&31||n&2&&s&32){Object.defineProperty(o,t,{get(){return tt(this,t)},set(n){et(this,t,n,e)},configurable:true,enumerable:true})}else if(n&1&&s&64){Object.defineProperty(o,t,{value(...e){var n;const s=at(this);return(n=s===null||s===void 0?void 0:s.A)===null||n===void 0?void 0:n.then((()=>{var n;return(n=s.C)===null||n===void 0?void 0:n[t](...e)}))}})}}));if(n&1){const n=new Map;o.attributeChangedCallback=function(t,s,l){gt.jmp((()=>{var c;const i=n.get(t);if(this.hasOwnProperty(i)){l=this[i];delete this[i]}else if(o.hasOwnProperty(i)&&typeof this[i]==="number"&&this[i]==l){return}else if(i==null){const n=at(this);const o=n===null||n===void 0?void 0:n.v;if(o&&!(o&8)&&o&128&&l!==s){const o=n.C;const i=(c=e.R)===null||c===void 0?void 0:c[t];i===null||i===void 0?void 0:i.forEach((e=>{if(o[e]!=null){o[e].call(o,l,s,t)}}))}return}this[i]=l===null&&typeof this[i]==="boolean"?false:l}))};t.observedAttributes=Array.from(new Set([...Object.keys((s=e.R)!==null&&s!==void 0?s:{}),...l.filter((([t,e])=>e[0]&15)).map((([t,e])=>{const s=e[1]||t;n.set(s,t);return s}))]))}}return t};const st=async(t,e,n,s)=>{let o;if((e.v&32)===0){e.v|=32;const s=n.N;if(s){o=mt(n);if(o.then){const t=i();o=await o;t()}if(!o.isProxied){{n.R=o.watchers}nt(o,n,2);o.isProxied=true}const t=c("createInstance",n.S);{e.v|=8}try{new o(e)}catch(t){ht(t)}{e.v&=~8}{e.v|=128}t()}else{o=t.constructor;customElements.whenDefined(n.S).then((()=>e.v|=128))}if(o.style){let t=o.style;const e=P(n);if(!bt.has(e)){const s=c("registerStyles",n.S);k(e,t,!!(n.v&1));s()}}}const l=e.k;const f=()=>_(e,true);if(l&&l["s-rc"]){l["s-rc"].push(f)}else{f()}};const ot=t=>{};const lt=t=>{if((gt.v&1)===0){const e=at(t);const n=e.$;const s=c("connectedCallback",n.S);if(!(e.v&1)){e.v|=1;{let n=t;while(n=n.parentNode||n.host){if(n["s-p"]){V(e,e.k=n);break}}}if(n.U){Object.entries(n.U).map((([e,[n]])=>{if(n&31&&t.hasOwnProperty(e)){const n=t[e];delete t[e];t[e]=n}}))}{st(t,e,n)}}else{if(e===null||e===void 0?void 0:e.C);else if(e===null||e===void 0?void 0:e.T){e.T.then((()=>ot()))}}s()}};const ct=t=>{};const it=async t=>{if((gt.v&1)===0){const e=at(t);if(e===null||e===void 0?void 0:e.C);else if(e===null||e===void 0?void 0:e.T){e.T.then((()=>ct()))}}};const ft=(t,e={})=>{var n;const s=c();const o=[];const l=e.exclude||[];const i=wt.customElements;const u=$t.head;const a=u.querySelector("meta[charset]");const d=$t.createElement("style");const v=[];let p;let y=true;Object.assign(gt,e);gt.t=new URL(e.resourcesUrl||"./",$t.baseURI).href;let m=false;t.map((t=>{t[1].map((e=>{var n;const s={v:e[0],S:e[1],U:e[2],W:e[3]};if(s.v&4){m=true}{s.U=e[2]}{s.R=(n=e[4])!==null&&n!==void 0?n:{}}const c=s.S;const f=class extends HTMLElement{constructor(t){super(t);t=this;vt(t,s);if(s.v&1){{{t.attachShadow({mode:"open"})}}}}connectedCallback(){if(p){clearTimeout(p);p=null}if(y){v.push(this)}else{gt.jmp((()=>lt(this)))}}disconnectedCallback(){gt.jmp((()=>it(this)))}componentOnReady(){return at(this).T}};s.N=t[0];if(!l.includes(c)&&!i.get(c)){o.push(c);i.define(c,nt(f,s,1))}}))}));if(o.length>0){if(m){d.textContent+=r}{d.textContent+=o+f}if(d.innerHTML.length){d.setAttribute("data-styles","");const t=(n=gt.m)!==null&&n!==void 0?n:h($t);if(t!=null){d.setAttribute("nonce",t)}u.insertBefore(d,a?a.nextSibling:u.firstChild)}}y=false;if(v.length){v.map((t=>t.connectedCallback()))}else{{gt.jmp((()=>p=setTimeout(X,30)))}}s()};const rt=t=>gt.m=t;const ut=new WeakMap;const at=t=>ut.get(t);const dt=(t,e)=>ut.set(e.C=t,e);const vt=(t,e)=>{const n={v:0,$hostElement$:t,$:e,L:new Map};{n.A=new Promise((t=>n.P=t))}{n.T=new Promise((t=>n.M=t));t["s-p"]=[];t["s-rc"]=[]}return ut.set(t,n)};const pt=(t,e)=>e in t;const ht=(t,e)=>(0,console.error)(t,e);const yt=new Map;const mt=(t,e,n)=>{const s=t.S.replace(/-/g,"_");const o=t.N;const l=yt.get(o);if(l){return l[s]}
/*!__STENCIL_STATIC_IMPORT_SWITCH__*/return import(`./${o}.entry.js${""}`).then((t=>{{yt.set(o,t)}return t[s]}),ht)};const bt=new Map;const wt=typeof window!=="undefined"?window:{};const $t=wt.document||{head:{}};const gt={v:0,t:"",jmp:t=>t(),raf:t=>requestAnimationFrame(t),ael:(t,e,n,s)=>t.addEventListener(e,n,s),rel:(t,e,n,s)=>t.removeEventListener(e,n,s),ce:(t,e)=>new CustomEvent(t,e)};const St=t=>Promise.resolve(t);const jt=(()=>{try{new CSSStyleSheet;return typeof(new CSSStyleSheet).replaceSync==="function"}catch(t){}return false})();const Ot=[];const kt=[];const Ct=(t,e)=>n=>{t.push(n);if(!o){o=true;if(e&&gt.v&4){xt(Pt)}else{gt.raf(Pt)}}};const Mt=t=>{for(let e=0;e<t.length;e++){try{t[e](performance.now())}catch(t){ht(t)}}t.length=0};const Pt=()=>{Mt(Ot);{Mt(kt);if(o=Ot.length>0){gt.raf(Pt)}}};const xt=t=>St().then(t);const Et=Ct(kt,true);export{l as a,ft as b,S as c,g,y as h,St as p,dt as r,rt as s};
//# sourceMappingURL=p-dcacdc5a.js.map