Namespace('TF.Rpc.Common');

var isNotProccessAjax = true,
	mouseMoveX,
	mouseMoveY,
	tmpTodayDate = new Date(),
	todayDay = tmpTodayDate.getDate(),
	todayMonth = tmpTodayDate.getMonth(),
	todayYear = tmpTodayDate.getFullYear();

const farmingYears = [
	{ start_date: '2009-10-01', end_date: '2010-09-30', year: '2010', year_id: '1' },
	{ start_date: '2010-10-01', end_date: '2011-09-30', year: '2011', year_id: '2' },
	{ start_date: '2011-10-01', end_date: '2012-09-30', year: '2012', year_id: '3' },
	{ start_date: '2012-10-01', end_date: '2013-09-30', year: '2013', year_id: '4' },
	{ start_date: '2013-10-01', end_date: '2014-09-30', year: '2014', year_id: '5' },
	{ start_date: '2014-10-01', end_date: '2015-09-30', year: '2015', year_id: '6' },
	{ start_date: '2015-10-01', end_date: '2016-09-30', year: '2016', year_id: '7' },
	{ start_date: '2016-10-01', end_date: '2017-09-30', year: '2017', year_id: '8' },
	{ start_date: '2017-10-01', end_date: '2018-09-30', year: '2018', year_id: '9' },
	{ start_date: '2018-10-01', end_date: '2019-09-30', year: '2019', year_id: '10' },
	{ start_date: '2019-10-01', end_date: '2020-09-30', year: '2020', year_id: '11' },
	{ start_date: '2020-10-01', end_date: '2021-09-30', year: '2021', year_id: '12' },
	{ start_date: '2021-10-01', end_date: '2022-09-30', year: '2022', year_id: '13' },
	{ start_date: '2022-10-01', end_date: '2023-09-30', year: '2023', year_id: '14' },
	{ start_date: '2023-10-01', end_date: '2024-09-30', year: '2024', year_id: '15' },
	{ start_date: '2024-10-01', end_date: '2025-09-30', year: '2025', year_id: '16' },
	{ start_date: '2025-10-01', end_date: '2026-09-30', year: '2026', year_id: '17' },
	{ start_date: '2026-10-01', end_date: '2027-09-30', year: '2027', year_id: '18' },
	{ start_date: '2027-10-01', end_date: '2028-09-30', year: '2028', year_id: '19' },
	{ start_date: '2028-10-01', end_date: '2029-09-30', year: '2029', year_id: '20' },
	{ start_date: '2029-10-01', end_date: '2030-09-30', year: '2030', year_id: '21' },
	{ start_date: '2030-10-01', end_date: '2031-09-30', year: '2031', year_id: '22' }
];

jQuery.fn.datebox.defaults.formatter = function (date) {
	var pad = '00';
	var y = date.getFullYear();
	var m = '' + (date.getMonth() + 1);
	var d = '' + date.getDate();

	m = pad.substring(0, pad.length - m.length) + m;
	d = pad.substring(0, pad.length - d.length) + d;
	return d + '.' + m + '.' + y;
};

// Overrides default combobox keyHandler to close the panel when 'Enter' is pressed
jQuery.fn.combobox.defaults.keyHandler = {
	...$.fn.combobox.defaults.keyHandler,
	enter: function () {
		jQuery(this).combobox('hidePanel');
	},
};

//Overrides onClick methods to trigger event mouseenter
jQuery.fn.menubutton.defaults = jQuery.extend(jQuery.fn.menubutton.defaults, {}, {
	onClick: function (item) {
		jQuery(this).trigger('mouseenter');
	}
});

jQuery.extend(jQuery.fn.validatebox.defaults.rules, {
	validDate: {
		validator: function (value) {
			var date = jQuery.fn.datebox.defaults.parser(value);
			var s = jQuery.fn.datebox.defaults.formatter(date);
			var y = date.getFullYear();
			return s == value && y > 1969;
		},
		message: 'Моля, въведете валидна дата.'
	}
});

jQuery.fn.datebox.defaults.parser = function (s) {
	//Ако е подаден Date object директно се връща към полето
	if (s instanceof Date && typeof s.getMonth == "function") {
		return s;
	}
	//Ако няма въведена дата или все още не е завършено въвеждането на дата
	//се връша днешна дата (1.1.2015 - 8 символа)
	if (!s || s.length < 8) {
		return new Date(todayYear, todayMonth, todayDay);
	}

	var separator,
		year,
		month,
		day,
		parts,
		date = s;

	//Проверява се какъв е разделителя на въвеждане
	if (date.indexOf('.') != -1) {
		separator = '.';
	} else if (date.indexOf('-') != -1) {
		separator = '-';
	} else if (date.indexOf(',') != -1) {
		separator = ',';
	} else if (date.indexOf('/') != -1) {
		separator = '/';
	} else if (date.indexOf('*') != -1) {
		separator = '*';
	} else if (date.indexOf('\\') != -1) {
		separator = '\\';
	} else if (date.indexOf('+') != -1) {
		separator = '+';
	} else {
		//Ако не е от стандартните се връща днешна дата
		return new Date(todayYear, todayMonth, todayDay)
	}

	parts = date.split(separator);

	if (parts.length == 3) {

		if (isNaN(parts[0]) || parts[0].length > 4 || parts[0].length == 0) {
			return new Date(todayYear, todayMonth, todayDay);
		}
		if (isNaN(parts[1]) || parts[1].length > 2 || parts[1].length == 0) {
			return new Date(todayYear, todayMonth, todayDay);
		}
		if (isNaN(parts[2]) || parts[2].length > 4 || parts[2].length == 0) {
			return new Date(todayYear, todayMonth, todayDay);
		}


		//Годината е първи елемент
		if (parts[0].length > 2) {
			year = parseInt(parts[0], 10);
			month = parseInt(parts[1], 10) - 1;
			day = parseInt(parts[2], 10);
			//Годината е последен елемент
		} else {
			day = parseInt(parts[0], 10);
			month = parseInt(parts[1], 10) - 1;
			year = parseInt(parts[2], 10);
		}
		return new Date(year, month, day);
	}
	//Ако датата не е в подходящ формат или няма нужния брой елементи се връща днешна дата
	return new Date(todayYear, todayMonth, todayDay)
};

jQuery.fn.datebox.methods.getValue = function () {
	var field = arguments[0],
		originalDate = jQuery(field[0]).next().find('.textbox-value').val(),
		dateArray = originalDate.split('.'),
		day = "" + parseInt(dateArray[0], 10),
		month = "" + parseInt(dateArray[1], 10),
		year = "" + parseInt(dateArray[2], 10),
		pad = '00',
		m,
		d;

	m = pad.substring(0, pad.length - month.length) + month;
	d = pad.substring(0, pad.length - day.length) + day;
	if (!m || m == 'NaN') {
		return '';
	}
	return year + '-' + m + '-' + d;
};

jQuery.fn.textWidth = function (text, font) {
	if (!jQuery.fn.textWidth.fakeEl) jQuery.fn.textWidth.fakeEl = jQuery('<span>').hide().appendTo(document.body);
	jQuery.fn.textWidth.fakeEl.text(text || this.val() || this.text()).css('font', font || this.css('font'));
	return jQuery.fn.textWidth.fakeEl.width();
};

//set default first day of week to be Monday for all dateboxes
jQuery.fn.calendar.defaults.firstDay = 1;

jQuery.extend($.fn.validatebox.defaults.rules, {
	egn: {
		validator: function (value, param) {
			var egn = new EGN(value);
			return egn.isValid();
		},
		message: 'Посоченото ЕГН е невалидно!'
	}
});

/**
* Converts a given date object to YYYY-MM-DD string.
* @param  {Object} date The date to convert from.
* @return {String}            The converted date.
*/
function getFormatedDateString(date) {
	var pad = '00';
	var y = date.getFullYear();
	var m = '' + (date.getMonth() + 1);
	var d = '' + date.getDate();

	m = pad.substring(0, pad.length - m.length) + m;
	d = pad.substring(0, pad.length - d.length) + d;
	return y + '-' + m + '-' + d;
}

var months = [{
	label: 'Януари',
	value: '0',
	maxDays: 31
}, {
	label: 'Февруари',
	value: '1',
	maxDays: 28
}, {
	label: 'Март',
	value: '2',
	maxDays: 30
}, {
	label: 'Април',
	value: '3',
	maxDays: 31
}, {
	label: 'Май',
	value: '4',
	maxDays: 31
}, {
	label: 'Юни',
	value: '5',
	maxDays: 30
}, {
	label: 'Юли',
	value: '6',
	maxDays: 31
}, {
	label: 'Август',
	value: '7',
	maxDays: 31
}, {
	label: 'Септември',
	value: '8',
	maxDays: 30
}, {
	label: 'Октомври',
	value: '9',
	maxDays: 30
}, {
	label: 'Ноември',
	value: '10',
	maxDays: 31
}, {
	label: 'Декември',
	value: '11',
	maxDays: 31
}];

var substance_unit_types = [{
	name: 'грамове',
	id: 1
}, {
	name: 'милиграма',
	id: 2
}, {
	name: 'милилитра',
	id: 3
}, {
	name: '%',
	id: 4
}, {
	name: 'литра',
	id: 5
}, {
	name: 'кг/дка',
	id: 6
}, {
	name: 'л/дка',
	id: 7
}, {
	name: 'бр/дка',
	id: 8
}];

var yesNo = [
	{
		name: 'Да',
		id: 1
	}, {
		name: 'Не',
		id: 2
	}
];

var imagesWMSServer = 'http://api.geoscan.bg/mapcache';

function setMouseCoords(e) {
	//var mouseX = Event.pointerX(e),mouseY = Event.pointerY(e);

	var offset = document.viewport.getScrollOffsets();

	var posx = 0, posy = 0;
	var ev = (!e) ? window.event : e;//IE:Moz
	if (ev.pageX) {//Moz
		posx = ev.pageX + window.pageXOffset - offset['left'];
		posy = ev.pageY + window.pageYOffset - offset['top'];
	}
	else if (ev.clientX) {//IE
		posx = ev.clientX + document.body.scrollLeft + offset['left'];
		posy = ev.clientY + document.body.scrollTop + offset['top'];
	}
	else {
		return false
	}//old browsers

	mouseMoveX = posx;
	mouseMoveY = posy;
}

//var IE = document.all?true:false;

//document.onmousemove = setMouseCoords;
//document.onmousedown = setMouseCoords;

/**
 * Javascript functionality to show that ajax request is being processed
 */

/**
 * Shows loading gif when an ajax is being processed
 */
function displayLoading(e) {
	//var mouseX = Event.pointerX(e),	mouseY = Event.pointerY(e);

	var offset = document.viewport.getScrollOffsets();
	//alert(offset);

	var posx = 0, posy = 0;
	var ev = (!e) ? window.event : e;//IE:Moz
	if (ev.pageX) {//Moz
		posx = ev.pageX + window.pageXOffset - offset['left'];
		posy = ev.pageY + window.pageYOffset - offset['top'];
	}
	else if (ev.clientX) {//IE
		posx = ev.clientX + document.body.scrollLeft + offset['left'];
		posy = ev.clientY + document.body.scrollTop + offset['top'];
	}
	else {
		return false
	}//old browsers

	mouseMoveX = posx;
	mouseMoveY = posy;
	jQuery("loading").setStyle({
		left: mouseMoveX + 10 + 'px',
		top: mouseMoveY + 16 + 'px',
		visibility: 'visible'
	})
}

/**
 * Sets the onload event of a starting ajax execution
 */
function setLoadEvent() {
	isNotProccessAjax = false;
	document.onmousemove = displayLoading;
	jQuery("loading").setStyle({
		left: mouseMoveX + 10 + 'px',
		top: mouseMoveY + 16 + 'px',
		visibility: 'visible'
	});
}

/**
 * Sets the end event of an ajax request
 */
function setCompleteEvent() {
	document.onmousemove = setMouseCoords;
	jQuery("loading").setStyle({
		visibility: 'hidden'
	});
	isNotProccessAjax = true;
}

/**
 * Function to check or uncheck all inputs on a repeater
 */
function checkAllInputs(type, sender) {
	var cbs = $$('#' + sender + ' input');

	if (cbs.length == 0) {
		cbs = $$(' input');
	}

	for (i = 0; i < cbs.length; i++) {
		if (!cbs[i].disabled)
			cbs[i].checked = type;
		else
			cbs[i].checked = false;
	}
}

// returns bool if some checkbox is checked
function getCheckedInputs(cssSelector) {
	var checkedInput = false;
	var arr = $$(cssSelector + '[@type=checkbox]');
	for (var i = 0; i < arr.length; i++) {
		if (arr[i].checked) {
			var checkedInput = true;
			break;
		}
	}

	return checkedInput;
}

function setObserveInput(object) {
	document.getElementById(object).observe('change', function () {
		var value = document.getElementById(object).value;
		var newValue = '';
		for (var k = 0; k < value.length; k++) {
			if (value[k] != ",")
				newValue += value[k];
			else
				break;
		}
		document.getElementById(object).value = newValue;
	});
}

function popup(url, winWidth, winHeight, sb) {
	winTop = (screen.height - winHeight) / 2;
	winLeft = (screen.width - winWidth) / 2;
	window.open(url, "", "width=" + winWidth + ",height=" + winHeight + ",top=" + winTop + ",left=" + winLeft + ",resizable=no,location=no,scrollbars=" + sb + ",toolbar=no");
	return false;
}

function isValidEGN(s) {
	var t = [2, 4, 8, 5, 10, 9, 7, 3, 6];
	if (typeof s != 'string')
		return false;
	if (s.length != 10)
		return false;
	var rv;
	var rr = 0;
	for (var i = 0; i < 9; i++) {
		if (s[i] == 0)
			continue;
		rr = rr + (s[i] * t[i]);
	}
	var chs = 0;
	chs = (rr % 11);
	if (chs == 10)
		chs = 0;
	if (s[9] == chs)
		return true;
	else
		return false;
}

jQuery(function ($) {
	if (navigation_menu) {
		navigation_menu.init();
	}
	contracts_templates.init();
	payment_subjects.init();
});
function initiatePasswordChange() {
	if (isPasswordValidForChange) {
		var passwordObject = getPasswordObjectValues();
		TF.Rpc.Common.ChangePassword.changePassword(passwordObject)
			.done(function (data) {
				jQuery('#win-change-password').window('close');
				jQuery('#success-changed-password').window('open');
				setTimeout(function () {
					jQuery('#success-changed-password').window('close');
				}, 2000);
			})
			.fail(function (data) {
				jQuery.messager.alert('Грешка', 'Възникна грешка при промяна на паролата', 'warning');
			});
	}
}
function isPasswordValidForChange() {
	if (
		jQuery('#password > input').validatebox('isValid')
		&& jQuery('#re-password > input').validatebox('isValid')) {
		return true;
	} else {
		return false;
	}
}

function clearPasswordValues() {
	jQuery('#password > input').val('');
	jQuery('#re-password > input').val('');
}

function getPasswordObjectValues() {
	passObj = {
		password: jQuery('#passwordField').val(),
		rePassword: jQuery('#repasswordField').val()
	};

	return passObj;
}

function setValidateChangePassword() {
	jQuery.extend(jQuery.fn.validatebox.defaults.rules, {
		equals: {
			validator: function (value, param) {
				return value == jQuery(param[0]).val();
			},
			message: 'Паролата не съвпада.'
		},
		minLength: {
			validator: function (value, param) {
				return value.length >= param[0];
			},
			message: 'Полето трябва да съдържа минимум {0} символа.'
		}
	});

	jQuery('#password > input').validatebox({
		required: true,
		missingMessage: 'Въведете парола!',
		validType: 'minLength[6]'
	});

	jQuery('#re-password > input').validatebox({
		required: true,
		missingMessage: 'Въведете парола!',
		validType: "equals['#password > input']"
	});

	jQuery('#btn-passwd-change-confirm > a').linkbutton({
		iconCls: 'icon-save'
	});

	jQuery('#btn-passwd-change-close > a').linkbutton({
		iconCls: 'icon-cancel'
	});
}

function validateMobileNumber(s, p) {
	return RegExp('\\+?[0-9]+$').test(p);
}

function validateMinLength(s, p) {
	return p.length >= 20;
}

function validateContactData() {
	var form = [
		{ key: 'mobile', selector: '.contact-data .contact-data__mobile input:first', validator: validateMobileNumber },
		{ key: 'email', selector: '.contact-data .contact-data__email input:first', validator: null },
		{ key: 'message', selector: '.contact-data .contact-data__message textarea:first', validator: validateMinLength },
	];

	var isValid = true;
	var data = {};

	for (var i = 0; i < form.length; i++) {
		var fieldVal = jQuery(form[i].selector).val();

		if (!form[i].validator || form[i].validator(null, fieldVal)) {
			data[form[i].key] = fieldVal;
		} else {
			isValid = false;
			break;
		}
	}

	if (isValid) {
		TF.Rpc.Common.ContactForm.send(data)
			.done(function (data) {
				jQuery.messager.alert('Изпратено', 'Вашето съобщение беше изпратено успешно. Ще се свържем с Вас в най-кратък срок.');
				jQuery('#win-contact-form').window('close');
			})
			.fail(function (errorObj) {
				jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
			})
	} else {
		jQuery.messager.alert('Грешка', 'Имате невалидни полета.');
	}
}

/**
 * The function handles the EasyUI $.combobox onHidePanel event when multiples=true
 * to unselect the filter value
 */
function onHidePanelMultiSelect() {
	var selected_values = jQuery(this).combobox('getValues');
	var data = jQuery(this).combobox('getData');
	var opts = jQuery(this).combobox('options');
	var dataset = [];

	for (var j = data.length - 1; j >= 0; j--) {
		dataset.push(data[j][opts.valueField].toString());
	}
	for (var i = selected_values.length - 1; i >= 0; i--) {

		if (dataset.indexOf(selected_values[i]) === -1) {
			jQuery(this).combobox('unselect', selected_values[i]);
		}
	}
}

/**
 * The function handles the EasyUI $.combobox onSelect event when multiples=true.
 * @param {Object} row The selected row from the combobox.
 */
function onComboMultiSelect(row) {
	let combo = jQuery(this);
	let data = combo.combobox('getData');
	let valueField = combo.combobox('options').valueField;

	if (row[valueField] !== "") {
		//this part of code is in timeout because should be trigered after easyu ui events for select and unselect
		setTimeout(function () {
			combo.combobox('unselect', ""); //Unselecting "Всички".
		}, 70);
		return;
	};

	setTimeout(function () {
		for (var i = 0; i < data.length; i++) {
			if (data[i][valueField] === "") {
				continue;
			}
			combo.combobox('unselect', data[i][valueField]);
		}
	}, 70);

	combo.combobox;
}

/**
 * The function extends the EasyUI $.validatebox default validation rules to check min, max, and set length.
 */
function numLengthbox() {
	jQuery.extend(jQuery.fn.validatebox.defaults.rules, {
		minLength: {
			validator: function (value, param) {
				return value.length >= param[0];
			},
			message: 'Полето трябва да съдържа минимум {0} символа.'
		},
		maxLength: {
			validator: function (value, param) {
				return value.length <= param[0];
			},
			message: 'Полето трябва да съдържа максимум {0} символа.'
		},
		setLength: {
			validator: function (value, param) {
				return value.length == param[0];
			},
			message: 'Полето трябва да съдържа {0} символа.'
		}
	});
}


jQuery.fn.animateRotate = function (angle, duration, easing, complete) {
	var args = jQuery.speed(duration, easing, complete);
	var step = args.step;
	return this.each(function (i, e) {
		args.step = function (now) {
			jQuery.style(e, 'transform', 'rotate(' + now + 'deg)');
			if (step) return step.apply(this, arguments);
		};

		jQuery({ deg: 0 }).animate({ deg: angle }, args);
	});
};

var Templates = {
	/**
	 * Generates html table
	 * @param {array} header
	 * @param {array} rows
	 * @returns {string}
	 */
	table: function (header, rows) {
		if (!(header instanceof Array || rows instanceof Array)) {
			return 'Invalid arguments!';
		}
		var table = '<table border="1" width="100%" cellpadding="5" cellspacing="0">' +
			'<tr>';
		for (key in header) {
			table += '<td>' + header[key] + '</td>';
		}
		table += '</tr>';
		for (var i = 0; i < rows.length; i++) {
			table += '<tr>';
			for (key in header) {
				if (rows[i][key] != undefined) {
					table += '<td>' + rows[i][key] + '</td>';
				}
				else {
					table += '<td></td>';
				}
			}
			table += '</tr>';
		}
		table += '</table>';

		return table;
	}
};

/**
 * Converts hex color to rgb
 * @param {String} hex
 * @returns {Null|Object with r, g and b properties}
 */
function hexToRgb(hex) {
	// Expand shorthand form (e.g. "03F") to full form (e.g. "0033FF")
	var shorthandRegex = /^#?([a-f\d])([a-f\d])([a-f\d])$/i;
	hex = hex.replace(shorthandRegex, function (m, r, g, b) {
		return r + r + g + g + b + b;
	});

	var result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
	return result ? {
		r: parseInt(result[1], 16),
		g: parseInt(result[2], 16),
		b: parseInt(result[3], 16)
	} : null;
}

/**
 * Converts rgb color to hex
 * @param {Object} rgb with r, g and b properties
 * @returns {String}
 */
function rgbToHex(rgb) {
	function componentToHex(c) {
		var hex = c.toString(16);
		return hex.length == 1 ? "0" + hex : hex;
	}

	return "#" + componentToHex(rgb.r) + componentToHex(rgb.g) + componentToHex(rgb.b);
}

/**
 * Compares two rgb colors with given tolerance
 * @param {Object} color1 with r, g and b properties
 * @param {Object} color2 with r, g and b properties
 * @param {int} tolerance range 0-255
 * @returns {Boolean}
 */
function isNeighborColor(color1, color2, tolerance) {
	if (tolerance == undefined) {
		tolerance = 32;
	}

	return Math.abs(color1.r - color2.r) <= tolerance
		&& Math.abs(color1.g - color2.g) <= tolerance
		&& Math.abs(color1.b - color2.b) <= tolerance;
}

function hideFrame() {
	jQuery("#login-win").window('close');
}
//Listen for escape keypress and close the top-most open window/panel that has close button
jQuery(function () {
	var escapePressed = 0;
	jQuery(document).keyup(function (e) {
		if (e.keyCode !== 27) {
			return;
		}

		var windows = jQuery('body > .window:visible').sort(compareCSSZindex);

		if (jQuery(windows[0]).find('.easyui-window').prev().find('.panel-tool-close').length > 0) {
			jQuery(windows[0]).find('.easyui-window').window('close');
		}
	});
});
//Get the element with the highest z-index property
function compareCSSZindex(a, b) {
	return jQuery(b).css('z-index') - jQuery(a).css('z-index');
}

//Set global clear button for datebox fields, that sets the value to an empty string and closes the calendar panel
var dateboxWithClearButton = jQuery.extend([], jQuery.fn.datebox.defaults.buttons);
dateboxWithClearButton.splice(1, 0, {
	text: 'Изчисти',
	handler: function (target) {
		jQuery(target).datebox('setValue', '');
		jQuery(this).closest('div.combo-panel').panel('close');
	}
});

function compareDates(dateField1, dateField2, index) {
	var date1 = new Date(jQuery(dateField1).datebox('getValue')),
		date2 = new Date(jQuery(dateField2).datebox('getValue'));

	if (index == 1) {
		return date1 >= date2;
	} else if (index == 2) {
		return date2 >= date1;
	}

	return false;
}

function getFarmingYearId(d = null) {
	var currentYear = 0;
	var date = new Date();
	if (d !== null) {
		date = new Date(d);
	}
	var today = new Date(date.getFullYear(), date.getMonth(), date.getDate());

	farmingYears.forEach(function (year) {
		var start_year = year.start_date.split('-')[0],
			start_month = year.start_date.split('-')[1] - 1,
			start_day = year.start_date.split('-')[2],
			start_date = new Date(start_year, start_month, start_day);

		var end_year = year.end_date.split('-')[0],
			end_month = year.end_date.split('-')[1] - 1,
			end_day = year.end_date.split('-')[2],
			end_date = new Date(end_year, end_month, end_day);

		if (today >= start_date && today < end_date) {
			currentYear = year.year_id;
		}
	});

	return currentYear;
}

function getCalendarFarmingYearId(d = null) {
	var currentYear = 0;
	var date = new Date();
	if (d !== null) {
		date = new Date(d);
	}

	farmingYears.forEach(function (year) {
		if (date.getFullYear() >= year.year) {
			currentYear = year.year_id;
		}
	});

	return currentYear;
}

function getFarmingYearById(id) {
	if (!id) {
		return null
	}

	return farmingYears.find((farmingYear) =>  farmingYear.year_id == id);
}

function getFarmingYearByCalendarYear(year) {
	if (!year) {
		return null
	}

	return farmingYears.find((farmingYear) =>  farmingYear.year == year);
}


/**

 * Returns the farming from a date.

 * @param {string|Date} date

 */

function getFarmingYearFromDate(date) {
	var year, month, day;
	var farmingYear;

	if (typeof date === 'string') {
		if (/-+/g.test(date)) {
			[year, month, day] = date.split('-');
		}

		if (/\.+/g.test(date)) {
			[day, month, year] = date.split('.');
		}
	}

	if (date instanceof Date) {
		year = date.getFullYear();
		month = date.getMonth() + 1;
		day = date.getDay();
	}

	if (month >= 10 && month <= 12) {
		farmingYear = parseInt(year);
	}

	if (month >= 1 && month < 10) {
		farmingYear = parseInt(year) - 1;
	}

	return farmingYear;
}

function restorePageURL(GET) {
	if (history && history.replaceState && GET.page !== undefined) {
		history.replaceState(null, null, "index.php?page=" + GET.page);
	}
}

function getQueryParams() {
	var GET = {};
	location.search
		.substr(1)
		.split("&")
		.forEach(function (item) {
			GET[decodeURIComponent(item.split("=")[0])] = decodeURIComponent(item.split("=")[1]);
		});
	return GET;
}


function getZoomedWindowHeight(height, correction = 1) {
	let browserWindowHeight = jQuery(window).height();
	let zoomPercent = Math.round(window.devicePixelRatio * 100) / 100;
	let calculatedHeight = (height / zoomPercent) * correction;

	if (calculatedHeight > browserWindowHeight) {
		calculatedHeight = browserWindowHeight - 70;
	}

	return calculatedHeight;
}


function getZoomedWindowWidth(width, correction = 1) {
	let zoomPercent = Math.round(window.devicePixelRatio * 100) / 100;

	return (width / zoomPercent) * correction;
}

function roundTo(number, precision = 2) {
	return +(Math.round(number + "e+" + precision) + "e-" + precision);
}