(function(root, factory) {
    if (typeof define === "function" && define.amd) {
        // AMD. Register as an anonymous module.
        define([
            "jquery",
            "js/notifications/alerts-settings",
            "js/utilities/contracts-templates",
            "TF/Rpc/Login/LoginForm",
            "js/utilities/payment-subjects",
            "js/main/EasyUIRPCLoaders",
            "easyui"
        ], factory);
    } else {
        // Browser globals (root is window)
        root.navigation_menu = factory(
            jQuery,
            alerts_settings,
            contracts_templates,
            TF.Rpc.Login.LoginForm,
            payment_subjects,
            EasyUIRPCLoaders
        );
    }
})(typeof self !== "undefined" ? self : this, function(
    jQuery,
    alerts_settings,
    contracts_templates,
    LoginForm,
    payment_subjects,
    EasyUIRPCLoaders
) {
    function init() {
        jQuery("#settings-menu").menu({
            onClick: function(item) {
                if (item.id == "password-change") {
                    setValidateChangePassword();
                    clearPasswordValues();
                    jQuery("#win-change-password").window("open");
                }
            }
        });

        if (!isGuest) {
            //navigation buttons
            if(legacyMode == true) { 
                jQuery("#" + mapLinkButton).menubutton({
                    iconCls: "icon-map",
                    menu: "#data-menu",
                    showEvent: 'click'
                });
                jQuery("#" + plotLinkButton).menubutton({
                    iconCls: "icon-edit-geometry",
                    menu: "#data-menu-kvs",
                    showEvent: 'click'
                });
                jQuery("#" + EquityLinkButton).menubutton({
                    iconCls: "icon-data",
                    menu: "#data-menu-equity",
                    showEvent: 'click'
                });
                jQuery("#" + subsidyLinkButton).menubutton({
                    iconCls: "icon-edit-geometry",
                    menu: "#data-menu-zp",
                    showEvent: 'click'
                });
                jQuery("#" + agroLinkButton).menubutton({
                    iconCls: "icon-planting",
                    menu: "#data-menu-cov",
                    showEvent: 'click'
                });
                jQuery("#" + WarehouseLinkButton).menubutton({
                    iconCls: "icon-warehouse",
                    menu: "#data-menu-warehouse",
                    showEvent: 'click'
                });

                jQuery("#" + settingLinkButton).menubutton({
                    iconCls: "icon-settings",
                    menu: "#settings-menu",
                    showEvent: 'click',
                    onHover: function() {
                        // jQuery(this).trigger('mouseenter');
                    }
                });
                jQuery("#" + helpLinkButton).menubutton({
                    iconCls: "icon-help",
                    menu: "#help-menu",
                    showEvent: 'click'
                });
                jQuery("#" + SalesContractsLinkButton).linkbutton({
                    iconCls: "icon-cart-go",
                    menu: "#OwnershipGroup",
                    plain: true,
                    width: 165
                });
                jQuery("#" + HypothecsLinkButton).linkbutton({
                    iconCls: "icon-money",
                    menu: "#OwnershipGroup",
                    plain: true
                });
                jQuery("#" + CollectionsLinkButton).linkbutton({
                    iconCls: "icon-money-add",
                    plain: true
                });
                jQuery("#" + ThemeMapsLinkButton).linkbutton({
                    iconCls: "icon-layers",
                    plain: true
                });
            }
            
            if (HideBoth == "true") {
                jQuery("#OwnershipGroup").hide();
                jQuery("#after-ownership-separator").hide();
            } else if (HideBoth == "false") {
                jQuery("#OwnershipGroup").show();
                jQuery("#after-ownership-separator").show();
                jQuery("#ownership-inside-separator").show();
            } else if (HideBoth == "single") {
                jQuery("#OwnershipGroup").show();
                jQuery("#after-ownership-separator").show();
                jQuery("#ownership-inside-separator").hide();
            }

            if (HideThematicSeparator == "true") {
                jQuery("#thematic-maps-separator-div").hide();
            } else {
                jQuery("#thematic-maps-separator-div").show();
            }

            if (userLevel == 3) {
                jQuery("#user-link-button").hide();
            }
            if (!isSuperAdmin) {
                jQuery("#sat-admin-menu").hide();
            }
            //init account settings components
            jQuery("#btn-account-settings-save > a").linkbutton({
                iconCls: "icon-save"
            });

            jQuery("#account-settings").bind("click", function() {
                jQuery("#account-settings-map > input").combobox({
                    url: "index.php?common-rpc=map-types-combobox",
                    rpcParams: [{ selected: false }],
                    textField: "name",
                    valueField: "id",
                    loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                    loadFilter:
                        EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
                });
                jQuery("#win-account-settings").window("open");
            });

            jQuery("#btn-account-settings-save > a").bind("click", function() {
                requestEditAccountSettings.dispatch();
            });

            jQuery("#about-technofarm").bind("click", function() {
                jQuery("#win-about-technofarm").window("open");
            });

            jQuery("#contact-us").bind("click", function() {
                jQuery("#win-contact-form").window("open");
            });

            jQuery("#btn-contracts-templates").bind("click", function() {
                contracts_templates.initTemplatesGrid();
                jQuery("#win-contracts-templates").window("open");
            });

            jQuery("#renta-type").bind("click", function() {
                initRentaTypeGrid();
                jQuery("#win-renta-type").window("open");
            });

            jQuery("#payment-subjects").bind("click", function() {
                payment_subjects.initPaymentSubjectsGrid();
                jQuery("#win-payment-subjects").window("open");
            });

            jQuery("#payment-numbering").bind("click", function() {
                initRkoNumberingField();
                jQuery("#win-payment-numbering").window("open");
            });
            jQuery("#alert-subjects").bind("click", function() {
                alerts_settings.initAlertsGrid();
                jQuery("#win-alert-settings").window("open");
            });
            jQuery("#notification-grid").bind("click", function() {
                initNotificationGrid();
                jQuery("#win-notifications").window("open");
            });
            jQuery("#btn-user-logout").bind("click", function() {
                doUserLogoutActions();
            });
        }
    }

    function clearPasswordValues() {
        jQuery("#password > input").val("");
        jQuery("#re-password > input").val("");
    }

    function setValidateChangePassword() {
        jQuery.extend(jQuery.fn.validatebox.defaults.rules, {
            equals: {
                validator: function(value, param) {
                    return value == jQuery(param[0]).val();
                },
                message: "Паролата не съвпада."
            },
            minLength: {
                validator: function(value, param) {
                    return value.length >= param[0];
                },
                message: "Полето трябва да съдържа минимум {0} символа."
            }
        });

        jQuery("#password > input").validatebox({
            required: true,
            missingMessage: "Въведете парола!",
            validType: "minLength[6]"
        });

        jQuery("#re-password > input").validatebox({
            required: true,
            missingMessage: "Въведете парола!",
            validType: "equals['#password > input']"
        });

        jQuery("#btn-passwd-change-confirm > a").linkbutton({
            iconCls: "icon-save"
        });

        jQuery("#btn-passwd-change-close > a").linkbutton({
            iconCls: "icon-cancel"
        });
    }

    function doUserLogoutActions() {
        LoginForm.logoutUser()
            .done(function () {
               window.location.href = "index.php?page=Home"
            })
    }
    return {
        init
    };
});
