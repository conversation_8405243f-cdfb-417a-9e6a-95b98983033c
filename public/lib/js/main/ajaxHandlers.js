jQuery(document).ajaxError(function(event, request, settings) {
    if(request.status === 401) {
        if(jQuery('#login-win').window('options').closed){
        	var iframe = jQuery('#login-win-frame');
        	iframe.attr('src',"index.php");
        	iframe.css("border-color", "#fff");
			jQuery('#login-win').window('open');
		}
    }   
});

Ajax.Responders.register({
	  onComplete: function(request, xhr) {
		this._logError(request);
		this._logRequest(request);
		this._logNoRights(request);
	  },
	  onException: function(request, xhr) {
		this._logError(request);
		this._logRequest(request);
		this._logNoRights(request);
	  },
		  
	  _logError: function (request) {
		  	if (request.getStatus() >= 500 && console) {
				var error = JSON.parse(request.getHeader('X-PRADO-ERROR'));
				if (console.error) {
					console.error(error.message);
					return;
				}
				if (console.log) {
					console.log(error.message);
					return;
				}
			}				
	  },

	  _logRequest: function (request){
	  	if (request.getStatus() === 401) {
				if(jQuery('#login-win').window('options').closed){
		        	var iframe = jQuery('#login-win-frame');
		        	iframe.attr('src',"index.php");
		        	iframe.css("border-color", "#fff");
					jQuery('#login-win').window('open');
				}
			} 
	  },

	  _logNoRights: function (request) {
		  
			if (request.getStatus() == 499) {
			
				var errMsgs = ERROR_REQUEST_MSGS[request.transport.statusText];

				jQuery.messager.alert('Грешка', errMsgs, 'warning');
			}								
	  },
});



/*Fix the problem with JSON.stringify for JSON-RPC*/
if(window.Prototype) {
    delete Object.prototype.toJSON;
    delete Array.prototype.toJSON;
    delete Hash.prototype.toJSON;
    delete String.prototype.toJSON;
}