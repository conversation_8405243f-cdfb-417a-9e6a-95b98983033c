function initForIsakDiffLfaGrid(layer_name)
{
    var table = jQuery('#for-isak-diff-lfa-tables');

    table.datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        singleSelect: false,
        fit: true,
        fitColumns: true,
        showFooter: true,
        rownumbers: true,
        url: 'index.php?subsidieswizard-rpc=for-isak-diff-lfa-grid',
        rpcParams:[{
        	'layer_name':layer_name
        }],
        border: true,
        pagination: true,
        pageSize: 50,
        sortName: 'gid',
        sortOrder: 'desc',
        frozenColumns: [[
                {
                    field: 'ck',
                    checkbox: true
                }
            ]],
        columns: [
            [
                {
                    field: 'prc_name',
                    title: '<b>Име</b>',
                    sortable: true,
                    width: 60
                }, {
                    field: 'ekate',
                    title: '<b>Землище</b>',
                    sortable: true,
                    width: 60,
                    align: 'center'
                }, {
                    field: 'nm_lfa_lfa',
                    title: '<b>НР</b>',
                    sortable: true,
                    width: 20,
                    align: 'center',
                    formatter: function (value, row, index) {
                        if (value == null) {
                            return '-';
                        }
                        else {
                            return value;
                        }
                    }
                }, {
                    field: 'area',
                    title: '<b>Площ <br/>(ха)</b>',
                    sortable: true,
                    width: 30,
                    align: 'center'
                }, {
                    field: 'inside_area',
                    title: '<b>Площ в <br/>"НР" (ха)</b>',
                    sortable: true,
                    width: 33,
                    align: 'center'
                }, {
                    field: 'outside_area',
                    title: '<b>Площ извън<br/>"НР" (ха)</b>',
                    sortable: true,
                    width: 40,
                    align: 'center',
                    styler: function (value, row, index) {
                        return 'color: red;';
                    }
                }, {
                    field: 'edited',
                    title: '<b>За <br/>редактиране</b>',
                    sortable: true,
                    width: 40,
                    align: 'center',
                    formatter: function (value, row, index) {
                        if (typeof value !== 'undefined') {
                            if (value == true)
                            {
                                return '<input class="for-edit" id="for-isak-diff-checkbox-' + index + '" data-row-index="' + index + '" type="checkbox" checked/>';
                            } else {
                                return '<input class="for-edit" id="for-isak-diff-checkbox-' + index + '" data-row-index="' + index + '" type="checkbox" />';
                            }

                        } else {
                            return '';
                        }
                    }
                }, {
                    field: 'comment',
                    title: '<b>Коментар</b>',
                    sortable: true,
                    width: 40,
                    align: 'center',
                    editor: {
                        type: 'text'
                    }
                }, {
                    field: 'nr',
                    title: '<b>Заявен</b>',
                    sortable: true,
                    width: 25,
                    align: 'center',
                    formatter: function (value, row, index) {
                        if (typeof value !== 'undefined') {

                            if (value == true)
                            {
                                return 'Да';
                            } else {
                                return 'Не';
                            }

                        } else {
                            return '';
                        }
                    }
                }
            ]
        ],
        toolbar: '#for-isak-diff-lfa-tables-toolbar',
        onSelect: function (rowIndex, rowData) {
            checkedElement = rowData;
            displayFeatureSelection(rowData.st_astext);
        },
        onBeforeLoad: function () {
            table.datagrid('clearChecked');
        },
        onClickCell: function (index,field,value){
            if(field == 'comment'){
                if(jQuery('#for-isak-diff-checkbox-' + index).is(':checked')){
                    table.datagrid('beginEdit', index);
                }
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
    	loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    //set comment editable
    jQuery('#win-for-isak-diff-lfa').off().on('change', 'input[type=checkbox].for-edit', function (e) {

        var rowIndex = jQuery(e.currentTarget).data('rowIndex');

        if (jQuery(e.currentTarget).is(':checked')) {

            table.datagrid('updateRow', {
                index: rowIndex,
                row: {
                    edited: true
                }
            });

            table.datagrid('beginEdit', rowIndex);
            var editorComment = table.datagrid('getEditor', {index: rowIndex, field: 'comment'});
            jQuery(editorComment.target).focus();

        } else {

            table.datagrid('updateRow', {
                index: rowIndex,
                row: {
                    edited: false,
                    comment: ''
                }
            });

            table.datagrid('endEdit', rowIndex);
        }

    });
}

function requestLFA() {

    var table = jQuery('#for-isak-diff-lfa-tables');

    var rows = table.datagrid('getSelections');
    var data = {};
    data.nr1 = [];
    data.nr2 = [];
    var tempData = {};
    var gidCounter = [];
    var notInNr = [];
    var inBothNr = [];

    if (rows.length == 0) {
        jQuery.messager.alert('Грешка', 'Моля изберете парцелите от таблицата, които искате да заявите за НР!', 'warning');
        return false;
    }

    rows.forEach(function(obj) {
        if(obj.nm_lfa_lfa && obj.nm_lfa_lfa != '-') {
            if(typeof gidCounter[obj.gid] === 'undefined') {
                gidCounter[obj.gid] = 1;
            }
            else {
                gidCounter[obj.gid]++;
            }
            tempData[obj.gid] = obj;
        }
        else {
            notInNr.push(obj.prc_name);
        }
    });

    gidCounter.forEach(function(value, key) {
        if(value == 1) {
            if(tempData[key].nm_lfa_lfa == 1) {
                data.nr1.push(tempData[key].gid);
            }
            else {
                data.nr2.push(tempData[key].gid);
            }
        }
        else {
            inBothNr.push(tempData[key].prc_name);
        }
    });

    data.layer_name = _selectedLayerData.attributes.layer_name;

    TF.Rpc.SubsidiesWizard.ForIsakDiffLfaGrid.saveForNR(data)
	.done(function (data) {
	}).fail(function(error) {
        jQuery.messager.alert('Грешка', error.getMessage());
    });

    table.datagrid('reload');

    var errorMsg = '';

    if (notInNr.length > 0) {
        errorMsg += '<p>Следните парцели: <b>' + notInNr.join(', ') + '</b> не бяха заявени, тъй като не попадат в нито един от регионите.</p>';
    }

    if (inBothNr.length > 0) {
        errorMsg += '<p>Следните парцели: <b>' + inBothNr.join(', ') + '</b> не бяха заявени, тъй като един и същ парцел не може да бъде заявен и по двата региона.</p>';
    }

    if (errorMsg.length > 0) {
        jQuery.messager.alert('Грешка', errorMsg, 'warning');
    }
}

function offLFA() {
    var table = jQuery('#for-isak-diff-lfa-tables');

    var rows = table.datagrid('getSelections');

    if (rows.length == 0) {
        jQuery.messager.alert('Грешка', 'Моля изберете парцелите от таблицата, които искате да премахнете от НР!', 'warning');
        return false;
    }
    var data = {};
    data.nr1 = [];
    data.nr2 = [];
    data.layer_name = _selectedLayerData.attributes.layer_name;

    jQuery.map(rows, function(element) {
        if(element.nr) {
            data['nr' + element.nm_lfa_lfa].push(element.gid);
        }
    });

    TF.Rpc.SubsidiesWizard.ForIsakDiffLfaGrid.saveOffNR(data)
	.done(function (data) {
	}).fail(function(error) {
        jQuery.messager.alert('Грешка', error.getMessage());
    });

    table.datagrid('reload');
}

function initForIsakLFAFilterPanel() {
    jQuery('#lfa-filter-ekate').combobox({
		url: 'index.php?common-rpc=ekate-combobox',
		rpcParams: [{
			record_all:true,
			selected:true
		}],
		valueField: 'ekate',
		textField: 'text',
		multiple: false,
    	onSelect: onComboMultiSelect,
		filter: function(q, row){
			var opts = jQuery(this).combobox('options');
			var text = row[opts.textField].toLowerCase();
			var value = row[opts.valueField];
			var find = q.toLowerCase();
			if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
			{
				return true;
			}
		},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function lfaReportFilter() {
    var table = jQuery('#for-isak-diff-lfa-tables');

    table.datagrid({
    	 rpcParams: [{
         	'layer_name':_selectedLayerData.attributes.layer_name,
         	'filterObj':{
         		prc_name: jQuery('#lfa-filter-prc-name').val(),
	            ekate: jQuery('#lfa-filter-ekate').combobox('getValue'),
	            comment: jQuery('#lfa-filter-comment').val(),
	            edited: jQuery('#lfa-filter-edited').combobox('getValue'),
	            nm_lfa: jQuery('#lfa-filter-nm-lfa').combobox('getValue'),
	            lfa: jQuery('#lfa-filter-requested').combobox('getValue')
         	}
        }]
    });
}

function clearLfaReportFilter() {

    var table = jQuery('#for-isak-diff-lfa-tables');

    jQuery('#lfa-filter-prc-name').val('');
    jQuery('#lfa-filter-ekate').combobox('reset');
    jQuery('#lfa-filter-comment').val('');
    jQuery('#lfa-filter-edited').combobox('reset');
    jQuery('#lfa-filter-nm-lfa').combobox('reset');
    jQuery('#lfa-filter-requested').combobox('reset');

    table.datagrid({
    	rpcParams: [{
    		'layer_name':_selectedLayerData.attributes.layer_name,
    		'filterObj':{}
    	}]
    });
}
