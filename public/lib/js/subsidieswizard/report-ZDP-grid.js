var _loadedLandscapeElements = false;

function initReportZDPGrid(layer_name)
{
    var table = jQuery('#report-zdp-tables');

    table.datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        singleSelect: false,
        fit: true,
        fitColumns: true,
        showFooter: true,
        rownumbers: true,
        url: 'index.php?subsidieswizard-rpc=for-isak-report-zdp-grid',
        rpcParams: [{
        	'layer_name': layer_name
        }],
        border: false,
        pagination: true,
        sortName: 'for_isak.gid',
        sortOrder: 'desc',
        frozenColumns: [[
            {
                field: 'ck',
                checkbox: true
            }
        ]],
        columns: [
            [
                {
                    field: 'prc_name',
                    title: '<b>Име</b>',
                    sortable: true,
                    width: 50
                }, {
                    field: 'cropname',
                    title: '<b>Култура</b>',
                    sortable: true,
                    width: 60,
                    align: 'center',
                    styler: function (value, row, index) {
	                	if(row.is_active_culture == false){
	                		return 'background-color:#F69232;';
	                    }
                    }
                }, {
                    field: 'crop_short_type',
                    title: '<b>Тип</b>',
                    sortable: true,
                    width: 40,
                    align: 'center'
                }, {
                    field: 'area',
                    title: '<b>Площ <br/>(ха)</b>',
                    sortable: true,
                    width: 30,
                    align: 'center'
                }, {
                    field: 'green_area',
                    title: '<b>Зелена <br/>площ <br/>(ха)</b>',
                    sortable: true,
                    width: 30,
                    align: 'center'
                },{
                    field: 'zdp',
                    title: '<b>Заявен</b>',
                    sortable: true,
                    width: 30,
                    align: 'center',
                    formatter: function(value, row, index) {
                        if(typeof value !== 'undefined') {

                            if (value == true)
                            {
                                return 'Да';
                            }else {
                                return 'Не';
                            }

                        }else {
                            return '';
                        }
                    }
                },{
                    field: 'edited',
                    title: '<b>За <br/>редактиране</b>',
                    sortable: false,
                    width: 50,
                    align: 'center',
                    formatter: function(value, row, index) {
                        if(typeof value !== 'undefined') {
                            if (value == true)
                            {
                                return '<input data-row-index="' + index + '" type="checkbox" checked/>';
                            }else {
                                return '<input data-row-index="' + index + '" type="checkbox" />';
                            }

                        }else {
                            return '';
                        }
                    }
                },{
                    field: 'comment',
                    title: '<b>Коментар</b>',
                    sortable: false,
                    width: 40,
                    align: 'center',
                    editor: {
                        type: 'text'
                    }
                }
            ]
        ],
        toolbar: '#for-isak-report-zdp-toolbar',
        onSelect: function(rowIndex, rowData) {
            checkedElement = rowData;
            displayFeatureSelection(rowData.st_astext);
        },
        onBeforeLoad: function() {
            table.datagrid('clearChecked');

            //load zdp report content
            var layer_name = _selectedLayerData.attributes.layer_name;

            TF.Rpc.SubsidiesWizard.ForIsakZDPReportGrid.getReportZDP(layer_name)
        	.done(function (data) {
        		 jQuery('#report-zdp-content').html(data);
        	});

        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
    	loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    //set comment editable
    jQuery('#win-for-isak-report-zdp').off().on('change', 'input[type=checkbox]', function (e) {

        var rowIndex = jQuery(e.currentTarget).data('rowIndex');

        if (typeof rowIndex !== 'undefined') {

             if(jQuery(e.currentTarget).is(':checked')) {

            table.datagrid('updateRow',{
                index: rowIndex,
                row: {
                    edited: true
                }
            });

            table.datagrid('beginEdit', rowIndex);
            var editorComment = table.datagrid('getEditor', {index:rowIndex,field:'comment'});
            jQuery(editorComment.target).focus();

            }else {

                table.datagrid('updateRow',{
                    index: rowIndex,
                    row: {
                        edited: false,
                        comment: ''
                    }
                });

                table.datagrid('endEdit', rowIndex);
            }
        }

    });

    //custom pager
	var pager = table.datagrid('getPager');
	pager.pagination({
		beforePageText: 'Стр.',
		displayMsg: 'От {from} до {to} от {total}'
	});


    jQuery('#tabs-zdp').tabs({
      onSelect: function(title,index){

        if(index == 1) {

            if(!_loadedLandscapeElements) {
                _loadedLandscapeElements = true;
                loadLandscapeElements();
            }

            jQuery('#zdp-save-button').hide();
            jQuery('#zdp-clear-button').hide();

            jQuery('#zdp-save-button-tab3').hide();
            jQuery('#zdp-clear-button-tab3').hide();

            _reloadZDPGrid = true;
        }else{
            jQuery('#zdp-save-button').show();
            jQuery('#zdp-clear-button').show();

            jQuery('#zdp-save-button-tab3').show();
            jQuery('#zdp-clear-button-tab3').show();
        }

        //Имоти "За ИСАК"
        if(index == 0) {
            jQuery('#filter-zdp-tab1').show();
            jQuery('#filter-zdp-tab3').hide();

            if (typeof _reloadZDPGrid !== 'undefined' && _reloadZDPGrid == true) {
                jQuery('#report-zdp-tables').datagrid('reload');
            }

            _reloadZDPGrid == true;
        }

        //Справка "Постоянно затревени площи"
        if(index == 2) {

            _reloadZDPGrid = true;

            jQuery('#filter-zdp-tab1').hide();
            jQuery('#filter-zdp-tab3').show();
            initForIsakZDPFilterPanelTab3();
            initReportPZPGrid(layer_name);

            selectLayerByName('layer_pzp');

            var layer_name = _selectedLayerData.attributes.layer_name;
            var layer_id = _selectedLayerData.id;
            var is_schema = false;
            TF.Rpc.SubsidiesWizard.ForIsakPZPReportGrid.initForIsakDiffPZP(layer_name, layer_id, is_schema)
        	.done(function (data) {
        		loadMapLayer('for_isak_diff_pzp');
        	});
        }
      }
    });
}

function initReportPZPGrid(layer_name)
{
    var table = jQuery('#report-pzp-tables');

    table.datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        singleSelect: true,
        fit: true,
        fitColumns: false,
        showFooter: true,
        rownumbers: true,
        url: 'index.php?subsidieswizard-rpc=for-isak-report-pzp-grid',
        rpcParams:[{
        	'layer_name':layer_name,
        	'is_schema': 0,
        	'filterObj':{}
        }],
        border: false,
        striped: true,
        width: '100%',
        pagination: true,
        sortName: 'gid',
        sortOrder: 'desc',
        columns: [
            [
                {
                    field: 'prc_name',
                    title: '<b>Име на парцел</b>',
                    sortable: true,
                    width: 95
                }, {
                    field: 'ekatte',
                    title: '<b>Землище</b>',
                    sortable: true,
                    width: 70,
                    align: 'center'
                }, {
                    field: 'cropname',
                    title: '<b>Култура</b>',
                    sortable: true,
                    width: 100,
                    align: 'center'
                }, {
                    field: 'schema',
                    title: '<b>Схеми/мерки</b>',
                    sortable: false,
                    width: 90,
                    align: 'center'
                }, {
                    field: 'area',
                    title: '<b>Площ на <br>парцела <br>(ха)</b>',
                    sortable: true,
                    width: 70,
                    align: 'center'
                }, {
                    field: 'in_area_pzp',
                    title: '<b>Площ на <br>парцела в <br>слой ПЗП <br>(ха)</b>',
                    sortable: true,
                    width: 70,
                    align: 'center',
                    styler: function (value, row, index) {
                        return 'color: red;';
                    }
                },{
                    field: 'imotcode',
                    title: '<b>Засегнати имоти <br>от слой ПЗП</b>',
                    sortable: false,
                    width: 120
                },{
                    field: 'sub_areas',
                    title: '<b>Засегната <br>площ от <br>имота (ха)</b>',
                    sortable: false,
                    width: 70,
                    styler: function (value, row, index) {
                        return 'color: red;';
                    }
                },{
                    field: 'edited',
                    title: '<b>За <br/>редактиране</b>',
                    sortable: false,
                    width: 80,
                    align: 'center',
                    formatter: function(value, row, index) {
                        if(typeof value !== 'undefined') {
                            if (value == true)
                            {
                                return '<input data-row-index="' + index + '" type="checkbox" checked/>';
                            }else {
                                return '<input data-row-index="' + index + '" type="checkbox" />';
                            }
                        }else {
                            return '';
                        }
                    }
                },{
                    field: 'comment',
                    title: '<b>Коментар</b>',
                    sortable: false,
                    width: 70,
                    align: 'center',
                    editor: {
                        type: 'text'
                    }
                }
            ]
        ],
        toolbar: '#for-isak-report-pzp-toolbar',
        onSelect: function(rowIndex, rowData) {
            checkedElement = rowData;
            displayFeatureSelection(rowData.st_astext);
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
    	loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    //set comment editable
    jQuery('#report-pzp-div').off().on('change', 'input[type=checkbox]', function (e) {

        var rowIndex = jQuery(e.currentTarget).data('rowIndex');

        if (typeof rowIndex !== 'undefined') {

             if(jQuery(e.currentTarget).is(':checked')) {

            table.datagrid('updateRow',{
                index: rowIndex,
                row: {
                    edited: true
                }
            });

            table.datagrid('beginEdit', rowIndex);
            var editorComment = table.datagrid('getEditor', {index:rowIndex,field:'comment'});
            jQuery(editorComment.target).focus();

            }else {
                table.datagrid('updateRow',{
                    index: rowIndex,
                    row: {
                        edited: false,
                        comment: ''
                    }
                });

                table.datagrid('endEdit', rowIndex);
            }
        }
    });

    //custom pager
    var pager = table.datagrid('getPager');
    pager.pagination({
        beforePageText: 'Стр.',
        displayMsg: 'От {from} до {to} от {total}'
    });
}

function loadLandscapeElements() {
    //load Елементи на ландшафта
    var layer_name = _selectedLayerData.attributes.layer_name;

    TF.Rpc.SubsidiesWizard.ForIsakZDPReportGrid.editENPData(layer_name)
	.done(function (data) {
        if (data) {
            jQuery('#quantity-terraces > input').val(data.quantityTerraces);
            jQuery('#green-area-terraces > input').val(data.greenAreaTerraces);
            jQuery('#quantity-hedge > input').val(data.quantityHedge);
            jQuery('#green-area-hedge > input').val(data.greenAreaHedge);
            jQuery('#quantity-isolated_tree > input').val(data.quantityIsolatedTree);
            jQuery('#green-area-isolated_tree > input').val(data.greenAreaIsolatedTree);
            jQuery('#quantity-rows_tree > input').val(data.quantityRowsTree);
            jQuery('#green-area-rows_tree > input').val(data.greenAreaRowsTree);
            jQuery('#quantity-group_tree > input').val(data.quantityGroupTree);
            jQuery('#green-area-group_tree > input').val(data.greenAreaGroupTree);
            jQuery('#quantity-field_boundaries > input').val(data.quantityFieldBoundaries);
            jQuery('#green-area-field_boundaries > input').val(data.greenAreaFieldBoundaries);
            jQuery('#quantity-lakes > input').val(data.quantityLakes);
            jQuery('#green-area-lakes > input').val(data.greenAreaLakes);
            jQuery('#quantity-ditch > input').val(data.quantityDitch);
            jQuery('#green-area-ditch > input').val(data.greenAreaDitch);
            jQuery('#quantity-buffer_strips > input').val(data.quantityBufferStrips);
            jQuery('#green-area-buffer_strips > input').val(data.greenAreaBufferStrips);
            jQuery('#quantity-strips_area > input').val(data.quantityStripsArea);
            jQuery('#green-area-strips_area > input').val(data.greenAreaStripsArea);
        }
		initForIsakZDPControls();
	});
}

function reloadZDP(){
    jQuery('#win-for-isak-report-zdp').window('close');

    initForIsakZDPFilterPanel();
    initReportZDPGrid(_selectedLayerData.attributes.layer_name);

    jQuery('#win-for-isak-report-zdp').window('open');

    processEnpGreenAreaFields();
}

var TERRACES_FACTOR_ENP = 2;
var HEDGE_FACTOR_ENP = 10;
var ISOLATED_TREE_FACTOR_ENP = 30;
var ROWS_TREE_FACTOR_ENP = 10;
var GROUP_TREE_FACTOR_ENP = 1.5;
var FIELD_BOUNDARIES_FACTOR_ENP = 9;
var LAKES_BOUNDARIES_FACTOR_ENP = 1.5;
var DITCH_BOUNDARIES_FACTOR_ENP = 6;
var BUFFER_STRIPS_BOUNDARIES_FACTOR_ENP = 9;
var STRIPS_AREA_BOUNDARIES_FACTOR_ENP = 9;

function processEnpGreenAreaFields(){

    var terraces = jQuery('#quantity-terraces > input').val();
    jQuery('#green-area-terraces > input').val(terraces*TERRACES_FACTOR_ENP);

    var hedge = jQuery('#quantity-hedge > input').val();
    jQuery('#green-area-hedge > input').val(hedge*HEDGE_FACTOR_ENP);

    var isolated_tree = jQuery('#quantity-isolated_tree > input').val();
    jQuery('#green-area-isolated_tree > input').val(isolated_tree*ISOLATED_TREE_FACTOR_ENP);

    var rows_tree = jQuery('#quantity-rows_tree > input').val();
    jQuery('#green-area-rows_tree > input').val(rows_tree*ROWS_TREE_FACTOR_ENP);

    var group_tree = jQuery('#quantity-group_tree > input').val();
    jQuery('#green-area-group_tree > input').val(group_tree*GROUP_TREE_FACTOR_ENP);

    var field_boundaries = jQuery('#quantity-field_boundaries > input').val();
    jQuery('#green-area-field_boundaries > input').val(field_boundaries*FIELD_BOUNDARIES_FACTOR_ENP);

    var lakes = jQuery('#quantity-lakes > input').val();
    jQuery('#green-area-lakes > input').val(lakes*LAKES_BOUNDARIES_FACTOR_ENP);

    var ditch = jQuery('#quantity-ditch > input').val();
    jQuery('#green-area-ditch > input').val(ditch*DITCH_BOUNDARIES_FACTOR_ENP);

    var buffer_strips = jQuery('#quantity-buffer_strips > input').val();
    jQuery('#green-area-buffer_strips > input').val(buffer_strips*BUFFER_STRIPS_BOUNDARIES_FACTOR_ENP);

    var strips_area = jQuery('#quantity-strips_area > input').val();
    jQuery('#green-area-strips_area > input').val(strips_area*STRIPS_AREA_BOUNDARIES_FACTOR_ENP);
}

function saveLansacapeData(){

    var year = _selectedLayerData.year_id;
    var farming = _selectedLayerData.id;
    var layer_name = _selectedLayerData.attributes.layer_name;

    var ENP_quantities = {
    		'quantityTerraces': jQuery('#quantity-terraces > input').val(),
    		'quantityHedge': jQuery('#quantity-hedge > input').val(),
    		'quantityIsolatedTree': jQuery('#quantity-isolated_tree > input').val(),
    		'quantityRowsTree': jQuery('#quantity-rows_tree > input').val(),
    		'quantityGroupTree': jQuery('#quantity-group_tree > input').val(),
    		'quantityFieldBoundaries': jQuery('#quantity-field_boundaries > input').val(),
    		'quantityLakes': jQuery('#quantity-lakes > input').val(),
    		'quantityDitch': jQuery('#quantity-ditch > input').val(),
    		'quantityBufferStrips': jQuery('#quantity-buffer_strips > input').val(),
    		'quantityStripsArea': jQuery('#quantity-strips_area > input').val()
    };

    TF.Rpc.SubsidiesWizard.ForIsakZDPReportGrid.addENPData(farming, year, layer_name, ENP_quantities)
	.done(function (data) {
        reloadZDP();
	});
}

function initForIsakZDPControls() {

    jQuery('#btn1-save-landscape-enp').bind('click', function(){

        saveLansacapeData();
    });

    jQuery('#btn2-save-landscape-enp').bind('click', function(){

        saveLansacapeData();
    });

    //Тераси (м)
    jQuery('#quantity-terraces > input').on('keypress', function(e){
        if(isNumberKey(e)){

            jQuery('#quantity-terraces > input').on('keyup', function(e){

                var terraces = jQuery('#quantity-terraces > input').val();
                jQuery('#green-area-terraces > input').val(terraces*TERRACES_FACTOR_ENP);
            });
        }else{
            return false;
        }
    });

    //Жив плет/обрасла с дървесна растителност ивица (м)
    jQuery('#quantity-hedge > input').on('keypress', function(e){
        if(isNumberKey(e)){

            jQuery('#quantity-hedge > input').on('keyup', function(e){

                var hedge = jQuery('#quantity-hedge > input').val();
                jQuery('#green-area-hedge > input').val(hedge*HEDGE_FACTOR_ENP);
            });
        }else{
            return false;
        }
    });

    //Изолирани дървета (бр.)
    jQuery('#quantity-isolated_tree > input').on('keypress', function(e){
        if(isNumberKey(e)){

            jQuery('#quantity-isolated_tree > input').on('keyup', function(e){

                var isolated_tree = jQuery('#quantity-isolated_tree > input').val();
                jQuery('#green-area-isolated_tree > input').val(isolated_tree*ISOLATED_TREE_FACTOR_ENP);
            });
        }else{
            return false;
        }
    });

    //Редици от дървета (м)
    jQuery('#quantity-rows_tree > input').on('keypress', function(e){
        if(isNumberKey(e)){

            jQuery('#quantity-rows_tree > input').on('keyup', function(e){

                var rows_tree = jQuery('#quantity-rows_tree > input').val();
                jQuery('#green-area-rows_tree > input').val(rows_tree*ROWS_TREE_FACTOR_ENP);
            });
        }else{
            return false;
        }
    });

    //Групи от дървета (кв.м)
    jQuery('#quantity-group_tree > input').on('keypress', function(e){
        if(isNumberKey(e)){

            jQuery('#quantity-group_tree > input').on('keyup', function(e){

                var group_tree = jQuery('#quantity-group_tree > input').val();
                jQuery('#green-area-group_tree > input').val(group_tree*GROUP_TREE_FACTOR_ENP);
            });
        }else{
            return false;
        }
    });

    //Синори (полски граници) (м)
    jQuery('#quantity-field_boundaries > input').on('keypress', function(e){
        if(isNumberKey(e)){

            jQuery('#quantity-field_boundaries > input').on('keyup', function(e){

                var field_boundaries = jQuery('#quantity-field_boundaries > input').val();
                jQuery('#green-area-field_boundaries > input').val(field_boundaries*FIELD_BOUNDARIES_FACTOR_ENP);
            });
        }else{
            return false;
        }
    });

    //Езерца (за 1 кв.м)
    jQuery('#quantity-lakes > input').on('keypress', function(e){
        if(isNumberKey(e)){

            jQuery('#quantity-lakes > input').on('keyup', function(e){

                var lakes = jQuery('#quantity-lakes > input').val();
                jQuery('#green-area-lakes > input').val(lakes*LAKES_BOUNDARIES_FACTOR_ENP);
            });
        }else{
            return false;
        }
    });

    //Канавки и открити водни течения (м)
    jQuery('#quantity-ditch > input').on('keypress', function(e){
        if(isNumberKey(e)){

            jQuery('#quantity-ditch > input').on('keyup', function(e){

                var ditch = jQuery('#quantity-ditch > input').val();
                jQuery('#green-area-ditch > input').val(ditch*DITCH_BOUNDARIES_FACTOR_ENP);
            });
        }else{
            return false;
        }
    });

    //Буферни ивици (м)
    jQuery('#quantity-buffer_strips > input').on('keypress', function(e){
        if(isNumberKey(e)){

            jQuery('#quantity-buffer_strips > input').on('keyup', function(e){

                var buffer_strips = jQuery('#quantity-buffer_strips > input').val();
                jQuery('#green-area-buffer_strips > input').val(buffer_strips*BUFFER_STRIPS_BOUNDARIES_FACTOR_ENP);
            });
        }else{
            return false;
        }
    });

    //Ивици допустими площи на границата между обработваеми земи и гори без производство (м)
    jQuery('#quantity-strips_area > input').on('keypress', function(e){
        if(isNumberKey(e)){

            jQuery('#quantity-strips_area > input').on('keyup', function(e){

                var strips_area = jQuery('#quantity-strips_area > input').val();
                jQuery('#green-area-strips_area > input').val(strips_area*STRIPS_AREA_BOUNDARIES_FACTOR_ENP);
            });
        }else{
            return false;
        }
    });

    //Help texts
    jQuery('#help-zdp-terraces').bind('click', helpZdpTerraces);
    jQuery('#help-zdp-hedge').bind('click', helpZdpHedge);
    jQuery('#help-zdp-isolated_tree').bind('click', helpZdpIsolatedTree);
    jQuery('#help-zdp-rows_tree').bind('click', helpZdpRowsTree);
    jQuery('#help-zdp-group_tree').bind('click', helpZdpGroupTree);
    jQuery('#help-zdp-field_boundaries').bind('click', helpZdpFieldBoundaries);
    jQuery('#help-zdp-lakes').bind('click', helpZdpLakes);
    jQuery('#help-zdp-ditch').bind('click', helpZdpDitch);
    jQuery('#help-zdp-buffer_strips').bind('click', helpZdpBufferStrips);
    jQuery('#help-zdp-strips_area').bind('click', helpZdpStripsArea);
}

function isNumberKey(evt)
{
 var charCode = (evt.which) ? evt.which : event.keyCode;
 if (charCode > 31 && (charCode < 48 || charCode > 57))
    return false;

 return true;
}

//Help texts functions
function helpZdpTerraces() {
    jQuery('#win-help-zdp-terraces').window('open');
}

function helpZdpHedge() {
    jQuery('#win-help-zdp-hedge').window('open');
}

function helpZdpIsolatedTree() {
    jQuery('#win-help-zdp-isolated_tree').window('open');
}

function helpZdpRowsTree() {
    jQuery('#win-help-zdp-rows_tree').window('open');
}

function helpZdpGroupTree() {
    jQuery('#win-help-zdp-group_tree').window('open');
}

function helpZdpFieldBoundaries() {
    jQuery('#win-help-zdp-field_boundaries').window('open');
}

function helpZdpLakes() {
    jQuery('#win-help-zdp-lakes').window('open');
}

function helpZdpDitch() {
    jQuery('#win-help-zdp-ditch').window('open');
}

function helpZdpBufferStrips() {
    jQuery('#win-help-zdp-buffer_strips').window('open');
}

function helpZdpStripsArea() {
    jQuery('#win-help-zdp-strips_area').window('open');
}
//Help texts functions

function initForIsakZDPFilterPanel() {

    jQuery('#zdp-filter-cropcode').combobox({
    	url: 'index.php?common-rpc=culture-combobox',
    	rpcParams: [{
    		'year':6,
    	}],
    	valueField: 'id',
    	textField: 'name',
    	loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
    	loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#zdp-filter-crop-short-type').combobox({
        url: 'index.php?common-rpc=culture-short-type-combobox',
        rpcParams:[false],
        valueField: 'id',
        textField: 'name',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
    	loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initForIsakZDPFilterPanelTab3() {

    jQuery('#zdp-filter-cropcode-tab3').combobox({
    	url: 'index.php?common-rpc=culture-combobox',
    	rpcParams: [{
    		'year':6,
    	}],
    	valueField: 'id',
    	textField: 'name',
    	loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
    	loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#zdp-filter-schema-tab3 > input').combobox({
		url: 'index.php?common-rpc=schema-combobox',
		rpcParams: [{
			detailed_lfa:true,
			without_schema:true
		}],
		valueField: 'id',
		textField: 'name',
		editable: false,
		filter: function(q, row){
			var opts = jQuery(this).combobox('options');
			var text = row[opts.textField].toLowerCase();
			var value = row[opts.valueField];
			var find = q.toLowerCase();
			if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
			{
				return true;
			}
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

    jQuery('#zdp-filter-zemlishte-tab3 > input').combobox({
        url: 'index.php?common-rpc=for-isak-ekate-combobox',
		rpcParams: [{
			'layer_id': _selectedLayerData.id,
			'layer_type': 9,
			'without_farm': true
		}],
        valueField: 'ekate',
        textField: 'text',
        filter: function(q, row){
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
            {
                return true;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
    });
}

function reportZDPFilterTab3() {
    var table = jQuery('#report-pzp-tables');

    table.datagrid({
    	rpcParams: [{
            'layer_name': _selectedLayerData.attributes.layer_name,
            'is_schema': 0,
            'filterObj': {
            	prc_name: jQuery('#zdp-filter-prc-name-tab3').val(),
                culture: jQuery('#zdp-filter-cropcode-tab3').combobox('getValue'),
                comment: jQuery('#zdp-filter-comment-tab3').val(),
                edited: jQuery('#zdp-filter-edited-tab3').combobox('getValue'),
                schema: jQuery('#zdp-filter-schema-tab3 > input').combobox('getValue'),
                ekate: jQuery('#zdp-filter-zemlishte-tab3 > input').combobox('getValue'),
            }
        }]
    });
}

function clearZDPReportFilterTab3() {

    var table = jQuery('#report-pzp-tables');

    jQuery('#zdp-filter-prc-name-tab3').val('');
    jQuery('#zdp-filter-cropcode-tab3').combobox('clear');
    jQuery('#zdp-filter-comment-tab3').val('');
    jQuery('#zdp-filter-edited-tab3').combobox('reset');
    jQuery('#zdp-filter-schema-tab3 > input').combobox('clear');
    jQuery('#zdp-filter-zemlishte-tab3 > input').combobox('clear');

    table.datagrid({
        rpcParams: [{
            'layer_name': _selectedLayerData.attributes.layer_name,
            'is_schema': 0,
            'filterObj': {}
        }]
    });
}

function reportZDPFilter() {
    var table = jQuery('#report-zdp-tables');

    table.datagrid({
        rpcParams: [{
        	'layer_name':_selectedLayerData.attributes.layer_name,
        	'filterObj': {
                prc_name: jQuery('#zdp-filter-prc-name').val(),
                culture: jQuery('#zdp-filter-cropcode').combobox('getValue'),
                culture_short_type: jQuery('#zdp-filter-crop-short-type').combobox('getValue'),
                comment: jQuery('#zdp-filter-comment').val(),
                edited: jQuery('#zdp-filter-edited').combobox('getValue'),
                zdp: jQuery('#zdp-filter-requested').combobox('getValue'),
            }
        }]
    });
}

function clearZDPReportFilter() {

    var table = jQuery('#report-zdp-tables');

    jQuery('#zdp-filter-prc-name').val('');
    jQuery('#zdp-filter-cropcode').combobox('clear');
    jQuery('#zdp-filter-crop-short-type').combobox('clear');
    jQuery('#zdp-filter-comment').val('');
    jQuery('#zdp-filter-edited').combobox('reset');
    jQuery('#zdp-filter-requested').combobox('reset');

    table.datagrid({
    	rpcParams: [{
            'layer_name': _selectedLayerData.attributes.layer_name,
            'filterObj': {}
        }]
    });
}

function onReportZDPComplete(sender, parameter) {

    jQuery('#report-zdp-content').html(parameter);
}
