var contextLayer = undefined;
var LAYER_ZP_PLOTS = 1;
var LAYER_TEMP_DATA = 2;
var LAYER_KOMAS_PLOTS = 4;
var LAYER_KVS_PLOTS = 5;
var LAYER_ISAK_PLOTS = 6;
var LAYER_FOR_ISAK_PLOTS = 9;
var LAYER_TYPE_LFA = 10;
var LAYER_TYPE_NATURA_2000 = 11;
var LAYER_TYPE_PERMANETELY_GREEN_AREAS = 12;
var LAYER_TYPE_VPS_PASISHTA = 13;
var LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI = 14;
var LAYER_TYPE_VPS_GASKI_ZIMNI = 15;
var LAYER_TYPE_VPS_LIVADEN_BLATAR = 16;
var LAYER_TYPE_VPS_ORLI_LESHOYADI = 17;

function initAllLayersTree()
{
	var isTreeDataLoaded = false;
	var calledFromSelected = false;
	var allLayersTree = jQuery('#all-layers-tree').tree({
		url: 'index.php?subsidieswizard-rpc=all-layers-tree',
		rpcParams:[{
			allowable:true,
			system_only:true,
			vps:true
		}],
		animate: true,
		lines: true,
		checkbox: true,
		dnd: false,
		cascadeCheck: false,
		onBeforeSelect: function(node) {
                    return false;
		},
		onBeforeCheck: function(node, isChecked) {
			var selected = jQuery('#all-layers-tree').tree('getSelected');

			if(node.state == 'closed' && isChecked) return false;
			if (selected && selected.id == node.id && node.checked == true)
			{
				jQuery.messager.alert('Грешка', 'Не можете да премахнете активен слой!');
				return false;
			}
		},
		onCheck: function(node, isChecked) {
			if (!isTreeDataLoaded) {
				return false;
			}
			var parent = allLayersTree.tree('getParent', node.target);

			var selected = allLayersTree.tree('getSelected');
			if(selected && selected.id == node.id) {
				calledFromSelected = true;
			}
			if (isChecked){
				var children = allLayersTree.tree('getChildren', node.target);
				if (children.length) {
					var hasChecked = false;
					for (var i=0; i < children.length; i++) {
						if(selected && selected.id == children[i].id) {
							continue;
						}
						if (children[i].checked) {
							hasChecked = true;
							break;
						}
					}
					if (!hasChecked && !calledFromSelected) {
						for (var i=0; i < children.length; i++) {
							if(selected && selected.id == children[i].id) {
								continue;
							}
							if (allLayersTree.tree("getParent", children[i].target).state == 'closed') {
								continue;
							}
							allLayersTree.tree('check', children[i].target);
						}
					} else {
						calledFromSelected = false;
					}
				}

				if (parent) {
					if (node['attributes'].layer_name && node.checked) {
                        if(node['attributes'].layer_type == 'layer_allowable'
                        	|| node['attributes'].layer_type == LAYER_TYPE_LFA
                        	|| node['attributes'].layer_type == LAYER_TYPE_NATURA_2000
                        	|| node['attributes'].layer_type == LAYER_TYPE_PERMANETELY_GREEN_AREAS
                        	|| node['attributes'].layer_type == LAYER_TYPE_VPS_GASKI_CHERVENOGUSHI
                        	|| node['attributes'].layer_type == LAYER_TYPE_VPS_GASKI_ZIMNI
                        	|| node['attributes'].layer_type == LAYER_TYPE_VPS_LIVADEN_BLATAR
                        	|| node['attributes'].layer_type == LAYER_TYPE_VPS_PASISHTA
                        	|| node['attributes'].layer_type == LAYER_TYPE_VPS_ORLI_LESHOYADI
                        	)
                        {
                        	if(node['attributes'].layer_name == 'layer_allowable')
                    		{
                        		loadMapLayerAllowable();
                    		}else if(node['attributes'].layer_name == 'layer_allowable_final')
                    		{
                    			loadMapLayerAllowableFinal();
                    		} else {
                    			loadMapRemoteLayer(node['attributes'].layer_name);
                    		}
                        }
                        else
                        {
                            loadMapLayer(node['attributes'].layer_name, node['attributes'].extent);
                        }
					}
					if(!parent.checked){
						allLayersTree.tree('check', parent.target);
					}
				}
			} else {
				var children = allLayersTree.tree('getChildren', node.target);
				if (children.length) {
					for (var i=0; i < children.length; i++) {
						if(selected && selected.id == children[i].id) {
							continue;
						}
						if (children[i].checked) {
							allLayersTree.tree('uncheck', children[i].target);
						}
					}
				}
				if(parent) {
					if (node['attributes'].layer_name && !node.checked) {
						removeLayerByName(node['attributes'].layer_name);
					}
					var siblings = allLayersTree.tree('getChildren', parent.target);
					var hasChecked = false;
					for (var i=0; i < siblings.length; i++) {
						if (siblings[i].checked) {
							hasChecked = true;
							break;
						}
					}
					if (!hasChecked) {
						allLayersTree.tree('uncheck', parent.target);
					}
				}
			}
		},
		onLoadSuccess: function () {
			isTreeDataLoaded = true;
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
	});
}

function zoomToLayerExtent()
{
    map.zoomToExtent(new OpenLayers.Bounds.fromString(contextLayer['attributes'].extent).transform(new OpenLayers.Projection("EPSG:32635"), map.getProjectionObject()));
}

function pushVectorLayerToTopPosition()
{
    var layers = map.layers;
    var layers_count = layers.length;

    if (map.layers[layers_count - 1].name != "Очертания")
    {
        for (var i = 0; i < map.layers.length; i++)
        {
            if (layers[i].name == "Очертания")
            {
                    map.setLayerIndex(map.layers[i], layers.length - 1);
            }
        }
    }
}
