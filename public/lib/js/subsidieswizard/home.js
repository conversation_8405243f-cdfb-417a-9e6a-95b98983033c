var isSuperAdmin = false;
var map;
var pndp_unallowed_cultures = new Array('310000', '311000', '312000', '314000', '315000', '316000', '211000', '171040', '121020', '121021', '121022', '121023','121024');
var renderer = OpenLayers.Util.getParameters(window.location.href).renderer;
if (renderer) {
    OpenLayers.Layer.Vector.prototype.renderers = [renderer];
}

var _selectedLayerData = {};

jQuery(function() {
    jQuery.fn.combobox.defaults.loader = EasyUIRPCLoaders.EasyUIGridCustomLoader.loader;
    jQuery.fn.combobox.defaults.loadFilter = EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter;
	setUserRights();
    //init map types combobox
	jQuery('#map-types-combobox > input').combobox({
		url: 'index.php?common-rpc=map-types-combobox',
		rpcParams: [{selected: true}],
		valueField: 'id',
		textField: 'name',
		onSelect: function(record)
		{
			initMapPad(record.id);
		},
        onLoadSuccess: function()
        {
            jQuery('#map-types-combobox > input').combobox('select', store.get('map_pad') || 1);
        },
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
	});

    initMap();
    initDefaultMapControls();
    initPositionDisplay();
    initScaleDisplay();
    initVectorLayer();
    initMapTools();

    //init components
    initLayersTree();
    initControls();
    initForIsakFilterPanel();
    initAllLayersTree();
    jQuery('#btn-edit-for-isak > a').linkbutton({iconCls: 'icon-save'});
    jQuery('#btn-multiedit > a').linkbutton({iconCls: 'icon-save'});

});

function initMap() {

    if (!map) {
        var options = {
            controls: [new OpenLayers.Control.Navigation()],
            projection: new OpenLayers.Projection("EPSG:900913")
        };
        map = new OpenLayers.Map('map', options);
        var apiKey = "AiWEso3-IjWyX1aZMuep9Sjl62D6FUMqv8qQGpV-kgbW0qWk61to4nrqHh-2D5HL";
        var bhyb = new OpenLayers.Layer.Bing({
            name: "Bing",
            key: apiKey,
            type: "AerialWithLabels"
        });

        map.addLayer(bhyb);
    }

    map.render("map");
}

function initMapPad(specific_map_type)
{
	var bingApiKey = "AiWEso3-IjWyX1aZMuep9Sjl62D6FUMqv8qQGpV-kgbW0qWk61to4nrqHh-2D5HL";
	var chosenMapType;

	//init all map features
	var options = {
		controls: [],
		projection: new OpenLayers.Projection("EPSG:900913")
	};

	//on init map type will not be specified
	if (specific_map_type == undefined)
	{
        chosenMapType = store.get('map_pad') || 1;

		//init map
		map = new OpenLayers.Map('map', options);
	}
	//when map type is changed specific_map_type will have the value of map type
	else {
		chosenMapType = parseInt(specific_map_type);
	}

	var layerMapPad;

	switch (chosenMapType)
	{
		case 2:
			layerMapPad = new OpenLayers.Layer.Bing({
				key: bingApiKey,
				type: "Aerial",
				name: "MapPad"
			});
			break;
		case 3:
			layerMapPad = new OpenLayers.Layer.Bing({
				key: bingApiKey,
				type: "Road",
				name: "MapPad"
			});
			break;
		case 4:
			layerMapPad = new OpenLayers.Layer.Google(
					"MapPad",
					{type: google.maps.MapTypeId.TERRAIN}
			);
			break;
		case 5:
			layerMapPad = new OpenLayers.Layer.Google(
					"MapPad",
					{numZoomLevels: 20}
			);
			break;
		case 6:
			layerMapPad = new OpenLayers.Layer.Google(
					"MapPad",
					{type: google.maps.MapTypeId.HYBRID, numZoomLevels: 20}
			);
			break;
		case 7:
			layerMapPad = new OpenLayers.Layer.Google(
					"MapPad",
					{type: google.maps.MapTypeId.SATELLITE, numZoomLevels: 22}
			);
			break;
		case 8:
			layerMapPad = new OpenLayers.Layer.OSM();
			break;
        case 9:
            layerMapPad = new OpenLayers.Layer.WMS(
				'MapPad',
				imagesWMSServer + "?map=" + '/var/www/satellite_processor/maps/geo_scan.map',
                //wmsServer + "?map=" + mapPath + groupID + '.map',
				{
					layers: 'geo_scan',
				},
                {
                    numZoomLevels: 18
                });
			break;
		default: // default is bing aerial with labels
			layerMapPad = new OpenLayers.Layer.Bing({
				key: bingApiKey,
				type: "AerialWithLabels",
				name: "MapPad"
			});
			break;
	}

	//specific map type will be given only on reload
	//on first load(init) specific map type should be undefined
	if (specific_map_type == undefined)
	{
		map.addLayer(layerMapPad);
	}
	else {
		map.addLayer(layerMapPad);
		map.setLayerIndex(map.layers[map.layers.length - 1], 0);
		map.removeLayer(map.layers[1]);
		map.layers[0].redraw(true);
	}

    store.set('map_pad', chosenMapType || 1);
}
function initMapTools(){

	jQuery('#tool-set-scale').bind('click', function()
	{
		var scale = jQuery("#scale-denominator input").val();

		map.zoomToScale(scale);
	});
}

function unselectAll()
{
	jQuery('#tool-panzoom').linkbutton('unselect');
	jQuery('#tool-zoomin').linkbutton('unselect');
	jQuery('#tool-zoomout').linkbutton('unselect');
	jQuery('#tool-measure-line').linkbutton('unselect');
	jQuery('#tool-measure-polygon').linkbutton('unselect');
	jQuery('#tool-choose-layer-object').linkbutton('unselect');

}

function deactivateAllControls()
{
	for (var i = 0; i < map.options.controls.length; i++)
	{
		map.options.controls[i].deactivate();
	}
}

function loadMapLayerForIsak(layer_name, extent) {
    for (var i = 1; i < map.layers.length; i++)
    {
        if(map.layers[i].name.indexOf('layer_for_isak') != -1)
        {
            map.removeLayer(map.layers[i]);
        }
    }

	if (layer_name && extent)
	{
		var layerExtent = new OpenLayers.Bounds.fromString(extent).transform(
				new OpenLayers.Projection("EPSG:32635"),
				map.getProjectionObject()
				);
		var layerData = new OpenLayers.Layer.WMS(
				layer_name,
				wmsServer + "?map=" + mapPath + groupID + '.map',
				{
					layers: layer_name,
					format: 'image/png',
					transparent: "true"
				});
		map.addLayer(layerData);
		layerData.redraw(true);

        map.zoomToExtent(layerExtent);
	}
}

function initPositionDisplay()
{
	map.events.register("mousemove", map, function(e)
	{
		var position = map.getLonLatFromViewPortPx(e.xy);

		OpenLayers.Util.getElement("coords").innerHTML = 'x: ' + position.lat + ', y: ' + position.lon;
	});
}

function initScaleDisplay()
{
	map.events.register("zoomend", map, function(e)
	{
		var currentScale = Math.round(map.getScale());

		var scale = jQuery("#scale-denominator input").val(currentScale);
	});
}

function initEditControls()
{
    jQuery('#for-isak-edit-culture > input').combobox({
		url: 'index.php?common-rpc=culture-combobox',
		rpcParams: [{
			'year':6,
		}],
		valueField: 'id',
		textField: 'name',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

    jQuery('#for-isak-edit-prc-name > input').textbox({
    });

	jQuery('#for-isak-edit-ekate> input').combobox({
		url: 'index.php?common-rpc=ekate-combobox',
		rpcParams: [{}],
		valueField: 'ekate',
		textField: 'text',
		filter: function(q, row){
			var opts = jQuery(this).combobox('options');
			var text = row[opts.textField].toLowerCase();
			var value = row[opts.valueField];
			var find = q.toLowerCase();
			if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
			{
				return true;
			}
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

	var selected_row = jQuery('#plots-tables').datagrid('getSelected');
	var win_options = jQuery('#win-edit-for-isak').window('options');
	var new_win_height = win_options.height;

	//comment 5 if
	if(selected_row.pndp == false){
		jQuery('#tr-pndp-checkbox').hide();
	}else{
		jQuery('#tr-pndp-checkbox').show();
	}
	if(selected_row.sepp == false){
		jQuery('#tr-sepp-checkbox').hide();
	}else{
		jQuery('#tr-sepp-checkbox').show();
	}
	if(selected_row.zdp == false){
		jQuery('#tr-zdp-checkbox').hide();
	}else{
		jQuery('##tr-zdp-checkbox').show();
	}
	if(selected_row.nr1 == false){
		jQuery('#tr-nr1-checkbox').hide();
	}else{
		jQuery('#tr-nr1-checkbox').show();
	}
	if(selected_row.nr2 == false){
		jQuery('#tr-nr2-checkbox').hide();
	}else{
		jQuery('#tr-nr2-checkbox').show();
	}
	new_win_height += jQuery('#edit-schemas-fieldset').height();

	//comment 1 if
	if(!selected_row.pndp && !selected_row.sepp && !selected_row.zdp && !selected_row.nr1 && !selected_row.nr2){
		jQuery('#edit-schemas-fieldset').hide();
	}else{
		jQuery('#edit-schemas-fieldset').show();
	}

	jQuery('#win-edit-for-isak').window('resize',{
		height: new_win_height
	});

	jQuery('#for-isak-edit-common-cultures > input').combobox({
		editable: false,
		textField: 'name',
		valueField: 'id',
		data: yesNo,
		width: 210
	});
}
function initManualLayerDataEdit()
{
	var selectedElement = jQuery('#plots-tables').datagrid('getSelected');
    if (selectedElement) {

        jQuery('#win-edit-for-isak').window('open');

        initEditControls();
        jQuery('#for-isak-edit-prc-name > input').textbox('setText', selectedElement.prc_name);
        jQuery('#for-isak-edit-culture > input').combobox('select', selectedElement.cropcode);
        jQuery('#for-isak-edit-ekate> input').combobox('select', selectedElement.ekatte);
        jQuery('#for-isak-edit-common-cultures > input').combobox('select', selectedElement.common_cultures);

        jQuery('#for-isak-edit-sepp > input').prop('checked', selectedElement.sepp);
        jQuery('#for-isak-edit-pndp > input').prop('checked', selectedElement.pndp);
        jQuery('#for-isak-edit-zdp > input').prop('checked', selectedElement.zdp);
        jQuery('#for-isak-edit-nr1 > input').prop('checked', selectedElement.nr1);
        jQuery('#for-isak-edit-nr2 > input').prop('checked', selectedElement.nr2);
        _savedPropertyGridCultures = false;
    } else {
        jQuery.messager.alert('Грешка', 'Моля изберете парцел, който искате да редактирате.');
    }
}
function initForIsakFilterPanel() {
    jQuery('#for-isak-filter-culture > input').combobox({
		url: 'index.php?common-rpc=culture-combobox',
		rpcParams: [{
			'year':6,
			'without_crops': true
		}],
		valueField: 'id',
		textField: 'name',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

    jQuery('#for-isak-filter-schema > input').combobox({
		url: 'index.php?common-rpc=schema-combobox',
		rpcParams: [{
			detailed_lfa:true,
			without_schema:true
		}],
		valueField: 'id',
		textField: 'name',
		filter: function(q, row){
			var opts = jQuery(this).combobox('options');
			var text = row[opts.textField].toLowerCase();
			var value = row[opts.valueField];
			var find = q.toLowerCase();
			if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
			{
				return true;
			}
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

    jQuery('#for-isak-filter-is-edited > input').combobox({
		editable: false,
		textField: 'name',
		valueField: 'id',
		data: yesNo
	});

    jQuery('#for-isak-filter-zemlishte > input').combobox({
		url: 'index.php?common-rpc=ekate-combobox',
		rpcParams: [{}],
		valueField: 'ekate',
		textField: 'text',
		filter: function(q, row){
			var opts = jQuery(this).combobox('options');
			var text = row[opts.textField].toLowerCase();
			var value = row[opts.valueField];
			var find = q.toLowerCase();
			if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
			{
				return true;
			}
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function saveEdit()
{
	var intermediate_crop = jQuery('#for-isak-edit-common-cultures > input').combobox('getValue');

	if(intermediate_crop == '1' || intermediate_crop == 'Да'){

		if(!_savedPropertyGridCultures) {
			propertyWindowCulture();
			return false;
		}

	}

	if(jQuery('#for-isak-edit-pndp > input').is(':checked') == true
			&& pndp_unallowed_cultures.indexOf(jQuery('#for-isak-edit-culture > input').combobox('getValue')) != -1){
		jQuery.messager.alert('Грешка', 'Парцели с култура ' + jQuery('#for-isak-edit-culture > input').combobox('getText') + ' са неподходящи за заявяване по ПНДП!', 'warning');
		return false;
	}
	else{
			var year = _selectedLayerData.year_id;
	        var farming = _selectedLayerData.id;
	        var layer_name = _selectedLayerData.attributes.layer_name;

	        var obj = new Object();
	        obj.farming = farming;
	        obj.year = year;
	        //filters
	        var edited_record = jQuery('#plots-tables').datagrid('getSelected');
	        obj.edit_id = edited_record.gid;
	        obj.prc_name = jQuery('#for-isak-filter-prc-name > input').val();
	    	obj.isak = jQuery('#for-isak-filter-isak > input').val();
	    	obj.culture = jQuery('#for-isak-filter-culture > input').combobox('getValue');
	    	obj.ekate = jQuery('#for-isak-filter-zemlishte > input').combobox('getValue');
	    	obj.schema = jQuery('#for-isak-filter-schema > input').combobox('getValue');
	    	obj.is_edited = jQuery('#for-isak-filter-is-edited > input').combobox('getValue');
	    	obj.comment = jQuery('#for-isak-filter-comment > input').val();

	        //new values
	    	var new_obj = new Object();
	    	new_obj.ekatte = jQuery('#for-isak-edit-ekate > input').combobox('getValue');
	    	new_obj.culture = jQuery('#for-isak-edit-culture > input').combobox('getValue');
	    	new_obj.name = jQuery('#for-isak-edit-prc-name > input').textbox('getText');
	    	new_obj.area = edited_record.area;

	        new_obj.pndp = jQuery('#for-isak-edit-pndp > input').is(':checked');
	        new_obj.zdp = jQuery('#for-isak-edit-zdp > input').is(':checked');
	        new_obj.sepp = jQuery('#for-isak-edit-sepp > input').is(':checked');
	        new_obj.nr1 = jQuery('#for-isak-edit-nr1 > input').is(':checked');
	        new_obj.nr2 = jQuery('#for-isak-edit-nr2 > input').is(':checked');
	        new_obj.as_intermediate_crop = jQuery('#for-isak-edit-common-cultures > input').combobox('getValue');

	        TF.Rpc.SubsidiesWizard.ForIsakSubsidiesGrid.editForIsakPlot(obj, new_obj, layer_name)
            .done(function (data) {
            	jQuery('#plots-tables').datagrid('reload');
            	jQuery('#win-edit-for-isak').window('close');
            });

	        jQuery('#win-for-isak-edit').window('close');
	}
};

function initMultiEditFields() {

    jQuery('#me-culture').combobox({
        data: jQuery('#for-isak-filter-culture > input').combobox('getData'),
        valueField: 'id',
        textField: 'name',
        width: 200
    });

    jQuery('#me-ekatte').combobox({
		url: 'index.php?common-rpc=ekate-combobox',
		rpcParams: [{}],
		valueField: 'ekate',
		textField: 'text',
		width: 200,
		filter: function(q, row){
			var opts = jQuery(this).combobox('options');
			var text = row[opts.textField].toLowerCase();
			var value = row[opts.valueField];
			var find = q.toLowerCase();
			if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
			{
				return true;
			}
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

}

function executeMultiEdit() {
    if (jQuery('#me-culture').combobox('getValue') || jQuery('#me-ekatte').combobox('getValue') || jQuery('#me-schemas').combobox('getValue')) {
        jQuery.messager.confirm('Потвърждение', 'Това действие ще промени данните за филтрираните или селектираните от таблицата обекти!', function(r) {
            if (r) {

                var year = _selectedLayerData.year_id;
                var farming = _selectedLayerData.id;
                var layer_name = _selectedLayerData.attributes.layer_name;

                var obj = new Object();
                obj.farming = farming;
                obj.year = year;
                //filters

                obj.prc_name = jQuery('#for-isak-filter-prc-name > input').val();
            	obj.isak = jQuery('#for-isak-filter-isak > input').val();
            	obj.culture = jQuery('#for-isak-filter-culture > input').combobox('getValue');
            	obj.common_culture = false;
            	obj.ekate = jQuery('#for-isak-filter-zemlishte > input').combobox('getValue');
            	obj.schema = jQuery('#for-isak-filter-schema > input').combobox('getValue');
            	obj.is_edited = jQuery('#for-isak-filter-is-edited > input').combobox('getValue');
            	obj.comment = jQuery('#for-isak-filter-comment > input').val();

                //new values
                var culture = jQuery('#me-culture').combobox('getValue');
            	var ekatte = jQuery('#me-ekatte').combobox('getValue');
                var common_cultures = false;

                var rowsChecked = jQuery('#plots-tables').datagrid('getChecked');
                var rows = jQuery('#plots-tables').datagrid('getRows');
                var plotIds = [];

                //check if there are checked rows
                if(rowsChecked.length){
                	for (var i = 0; i < rowsChecked.length; i++) {

                		plotIds.push(rowsChecked[i].gid);
                	}
                }
                else if(!rows.length){
                	plotIds = [];
                }
                else{
                	plotIds = 'getAllPlotIds';
                }

                TF.Rpc.SubsidiesWizard.ForIsakSubsidiesGrid.saveMultiEdit(obj, layer_name, ekatte, common_cultures, culture, plotIds)
                .done(function (data) {
                	jQuery('#win-multi-edit').window('close');
                	clearFilterForIsak();
                	jQuery('#plots-tables').datagrid('reload');
                });

                jQuery('#win-for-isak-multi-edit').window('close');
            }
        });
    } else {
        jQuery.messager.alert('Грешка', 'Моля задайте данни, който да бъдат променени.');
    }
}
function loadMapLayerAllowable() {
    var layerData = new OpenLayers.Layer.WMS(
            'layer_allowable',
            login3WmsServer + "?map=" + login3MapPath + 'layer_allowable_draft.map',
            {
                layers: 'layer_allowable_draft',
                format: 'image/png',
                transparent: "true"
            });
    map.addLayer(layerData);
    layerData.redraw(true);

    //vector layer should always be the top layer
    pushVectorLayerToTopPosition();
}
function loadMapLayerAllowableFinal() {
    var layerData = new OpenLayers.Layer.WMS(
            'layer_allowable_final',
            login3WmsServer + "?map=" + login3MapPath + 'layer_allowable_final.map',
            {
                layers: 'layer_allowable_final',
                format: 'image/png',
                transparent: "true"
            });
    map.addLayer(layerData);
    layerData.redraw(true);
}

function loadMapRemoteLayer(layer_name) {
    var layerData = new OpenLayers.Layer.WMS(
    		layer_name,
            login3WmsServer + "?map=" + login3MapPath + layer_name + '.map',
            {
                layers: layer_name,
                format: 'image/png',
                transparent: "true"
            });
    map.addLayer(layerData);
    layerData.redraw(true);
}

function removeLayerByName(layer_name) {
    var layers = map.getLayersByName(layer_name);

    for (var i = 0; i < layers.length; i++)
    {
        map.removeLayer(layers[i]);
    }
}

function reloadLayerByName(layer_name) {
    var layers = map.getLayersByName(layer_name);

    for (var i = 0; i < layers.length; i++)
    {
        layers[i].redraw(true);
    }
}

function loadMapLayer(layer_name) {
    if (layer_name)
    {
        var layerData = new OpenLayers.Layer.WMS(
                layer_name,
                wmsServer + "?map=" + mapPath + groupID + '.map',
                {
                    layers: layer_name,
                    format: 'image/png',
                    transparent: "true"
                });
        map.addLayer(layerData);
        layerData.redraw(true);
    }
}

var VECTOR_LAYER_STYLE = {
    cursor: "pointer",
    graphicName: 'square',
    pointRadius: 10,
    fillColor: '#cccccc',
    strokeColor: "#4fffff",
    strokeWidth: 4,
    fillOpacity: 0.25,
    label: '${label}'
};

function initVectorLayer()
{
    var renderer = OpenLayers.Util.getParameters(window.location.href).renderer;
    renderer = (renderer) ? [renderer] : OpenLayers.Layer.Vector.prototype.renderers;

    vectors = new OpenLayers.Layer.Vector("Vector Layer", {
        styleMap: new OpenLayers.StyleMap(new OpenLayers.Style(VECTOR_LAYER_STYLE, {
            isDefault: true,
            context: {
                label: function (feature) {
                    if(!feature.attributes.hasOwnProperty('label')) {
                        return '';
                    }
                    return feature.attributes.label;
                }
            }
        })),
        renderers: renderer
    });

    map.addLayer(vectors);
}
