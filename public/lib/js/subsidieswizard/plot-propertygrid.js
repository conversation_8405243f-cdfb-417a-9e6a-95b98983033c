function propertyWindowFunction(e)
{
	var layerTree = jQuery('#layers-tree').combotree('tree');
	var layerData = layerTree.tree('getSelected');
	var layer_id = layerData.id;

	bounds = map.getExtent();

	x1 = parseInt(e.xy.x);
	y1 = parseInt(e.xy.y);

	jQuery('#property-grid').propertygrid({
		rpcParams: [{
			bbox: bounds.toBBOX(),
			x: x1,
			y: y1,
			width: map.size.w,
			height: map.size.h,
			layer: layer_id
		}],
		url: 'index.php?subsidieswizard-rpc=layer-pg',
		showGroup: true,
		scrollbarSize: 0,
		resizable: true,
		columns: [[
				{
					field: 'name',
					title: 'Параметър',
					width: 120
				},
				{
					field: 'value',
					title: 'Стойност',
					width: 180
				},
			]],
		onLoadSuccess: function(data)
		{
			if (data.total > 0) {
				var rowCount = data['rows'].length;
				var layerType = data['rows'][rowCount - 1]['layer_type'];

				if (layerType == LAYER_FOR_ISAK_PLOTS) {
					jQuery('#property-win').window('resize', {height: 336});
				}
				jQuery('#property-win').window('open');
			} else {
				jQuery.messager.alert('Грешка','Не е избран обект, за който да бъде показана информация.');
			};
			endLoading();
		},
		onLoadError: function(data) {
			//endLoading();
		},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

//save information from propertygrid
function savePropertyGrid() {
    var propData = jQuery('#property-grid').propertygrid('getData');
    requestSavePropertyGrid.setCallbackParameter(propData['rows']);
    requestSavePropertyGrid.dispatch();
    jQuery('#property-win').window('close');
    jQuery('#plots-tables').datagrid();
    jQuery('#plots-tables').datagrid('reload');
}
