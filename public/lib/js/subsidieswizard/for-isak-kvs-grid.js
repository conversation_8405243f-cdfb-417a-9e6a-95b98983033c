function initForIsakKVSGrid(farming, year) {
    jQuery('#kvs-plots-tables').datagrid({
    	 rpcParams: [{
             farming: farming,
             year: year,
         }],
        url: 'index.php?subsidieswizard-rpc=for-isak-kvs-report',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 50,
        fit: true,
        singleSelect: true,
        fitColumns: true,
        showFooter: true,

        sortName: 'gid',
        sortOrder: 'asc',
        border: false,
        frozenColumns: [
            [
                {
                    field: 'ck',
                    checkbox: true
                }
            ]
        ],
        columns: [
            [
                {
                    title: '<b>Информация за имот</b>',
                    colspan: 10
                }, {
                    field: 'area_intersect',
                    title: '<b>Припокриване<br/>(дка)</b>',
                    sortable: true,
                    width: 130,
                    rowspan: 2,
                    align: 'center'
                }, {
                    field: 'prc_name',
                    title: '<b>Име на<br/>парцел</b>',
                    sortable: true,
                    width: 120,
                    rowspan: 2,
                    align: 'center'
                }],
            [{
                    field: 'ekate',
                    title: '<b>ЕКАТТЕ</b>',
                    sortable: true,
                    width: 70,
                    align: 'center'
                }, {
                    field: 'kad_ident',
                    title: '<b>Идентификатор</b>',
                    sortable: true,
                    width: 120
                }, {
                    field: 'masiv',
                    title: '<b>Масив</b>',
                    sortable: true,
                    width: 50,
                    align: 'center'
                }, {
                    field: 'number',
                    title: '<b>Имот</b>',
                    sortable: true,
                    width: 50,
                    align: 'center'
                }, {
                    field: 'category',
                    title: '<b>Категория</b>',
                    sortable: true,
                    width: 100,
                    align: 'center'
                }, {
                    field: 'area_type',
                    title: '<b>НТП</b>',
                    sortable: true,
                    width: 130
                }, {
                    field: 'has_contracts',
                    title: '<b>Има<br/>договори</b>',
                    sortable: true,
                    width: 70,
                    align: 'center'
                }, {
                    field: 'contract_name',
                    title: '<b>Номер на<br/>договора</b>',
                    sortable: true,
                    width: 255,
                    align: 'center'
                },{
                    field: 'area_kvs',
                    title: '<b>Обща<br/>площ(дка)</b>',
                    sortable: true,
                    width: 100,
                    align: 'center'
                }, {
                    field: 'contract_area',
                    title: '<b>Площ по<br/>договор(дка)</b>',
                    sortable: true,
                    width: 100,
                    align: 'center'
                }
            ]
        ],
        pagination: true,
        rownumbers: true,
        onBeforeLoad: function() {
            jQuery('#kvs-plots-tables').datagrid('clearChecked');
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

jQuery(function() {
    jQuery('#for-isak-kvs-grid').on('click', function() {
        var year = _selectedLayerData.year_id;
        var farming = _selectedLayerData.attributes.farming_id;

        initForIsakKVSGrid(farming, year);
        jQuery('#win-kvs-plots').window('open');
    });
});
