function propertyWindowCulture(e)
{
	var layerTree = jQuery('#layers-tree').combotree('tree');
	var layerData = layerTree.tree('getSelected');
	var layer_id = layerData.id;

	var layer_name = _selectedLayerData.attributes.layer_name;
	var selected_row = jQuery('#plots-tables').datagrid('getSelected');

	jQuery('#property-grid-intermidiate-cultures').propertygrid({
		url: 'index.php?subsidieswizard-rpc=pg-intermidiate-cultures',
		rpcParams: [layer_name,selected_row.gid],
		showGroup: true,
		scrollbarSize: 0,
		resizable: true,
		columns: [[
				{
					field: 'name',
					title: 'Култура',
					width: 120
				},
				{
					field: 'value',
					title: 'Заяви',
					width: 180
				},
			]],
		onLoadSuccess: function(data)
		{
			//endLoading();
			jQuery('#property-win-intermidiate-cultures').window('open');
		},
		onLoadError: function(data) {
			//endLoading();
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

var _savedPropertyGridCultures = false;

//save information from propertygrid
function savePropertyGridCultures() {

    var propData = jQuery('#property-grid-intermidiate-cultures').propertygrid('getData');

    var layer_name = _selectedLayerData.attributes.layer_name;
	var selected_row = jQuery('#plots-tables').datagrid('getSelected');

	//validate
	var hasWeat = false;
	var hasNonWeat = false;
	var rows = propData['rows'];
	for (var i = 0; i < rows.length; i++) {

		if(rows[i].group == 'Житни култури' && rows[i].value == 'Да') {
			hasWeat = true;
		}

		if(rows[i].group == 'Нежитни култури' && rows[i].value == 'Да') {
			hasNonWeat = true;
		}
	}

	if (!hasWeat || !hasNonWeat) {
		jQuery.messager.alert('Грешка', 'Не сте избрали комбинация от междинни култури!','warning');
		return false;
	}

	var obj = new Object();
    obj.rows = propData['rows'];
    obj.layer_name = layer_name;
    obj.gid = selected_row.gid;

    TF.Rpc.SubsidiesWizard.CulturesPropertyGrid.savePropertyGridCultures(obj)
	.done(function (data) {
	});

    _savedPropertyGridCultures = true;

    jQuery('#property-win-intermidiate-cultures').window('close');
}
