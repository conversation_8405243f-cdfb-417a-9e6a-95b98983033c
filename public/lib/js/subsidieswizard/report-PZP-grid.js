function initReportSchemaPZPGrid(layer_name)
{
    var table = jQuery('#report-schema-pzp-tables');

    table.datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        singleSelect: true,
        fit: true,
        fitColumns: false,
        showFooter: true,
        rownumbers: true,
        url: 'index.php?subsidieswizard-rpc=for-isak-report-pzp-grid',
        rpcParams:[{
        	'layer_name':layer_name,
        	'is_schema': 1,
        	'filterObj':{}
        }],
        border: true,
        striped: true,
        width: '100%',
        pagination: true,
        sortName: 'gid',
        sortOrder: 'desc',
        columns: [
            [
                {
                    field: 'prc_name',
                    title: '<b>Име на парцел</b>',
                    sortable: true,
                    width: 95
                }, {
                    field: 'ekatte',
                    title: '<b>Землище</b>',
                    sortable: true,
                    width: 70,
                    align: 'center'
                }, {
                    field: 'cropname',
                    title: '<b>Култура</b>',
                    sortable: true,
                    width: 100,
                    align: 'center'
                }, {
                    field: 'schema',
                    title: '<b>Схеми/мерки</b>',
                    sortable: false,
                    width: 90,
                    align: 'center'
                }, {
                    field: 'area',
                    title: '<b>Площ на <br>парцела <br>(ха)</b>',
                    sortable: true,
                    width: 70,
                    align: 'center'
                }, {
                    field: 'in_area_pzp',
                    title: '<b>Площ на <br>парцела в <br>слой ПЗП <br>(ха)</b>',
                    sortable: true,
                    width: 70,
                    align: 'center',
                    styler: function (value, row, index) {
                        return 'color: red;';
                    }
                },{
                    field: 'imotcode',
                    title: '<b>Засегнати имоти <br>от слой ПЗП</b>',
                    sortable: false,
                    width: 120
                },{
                    field: 'sub_areas',
                    title: '<b>Засегната <br>площ от <br>имота (ха)</b>',
                    sortable: false,
                    width: 70,
                    styler: function (value, row, index) {
                        return 'color: red;';
                    }
                },{
                    field: 'edited',
                    title: '<b>За <br/>редактиране</b>',
                    sortable: false,
                    width: 80,
                    align: 'center',
                    formatter: function(value, row, index) {
                        if(typeof value !== 'undefined') {
                            if (value == true)
                            {
                                return '<input data-row-index="' + index + '" type="checkbox" checked/>';
                            }else {
                                return '<input data-row-index="' + index + '" type="checkbox" />';
                            }
                        }else {
                            return '';
                        }
                    }
                },{
                    field: 'comment',
                    title: '<b>Коментар</b>',
                    sortable: false,
                    width: 70,
                    align: 'center',
                    editor: {
                        type: 'text'
                    }
                }
            ]
        ],
        toolbar: '#for-isak-report-schema-pzp-toolbar',
        onSelect: function(rowIndex, rowData) {
            checkedElement = rowData;
            displayFeatureSelection(rowData.st_astext);
        },
        onBeforeLoad: function() {
            table.datagrid('clearChecked');

            TF.Rpc.SubsidiesWizard.ForIsakPZPReportGrid.getReportPZP(_selectedLayerData.attributes.layer_name)
        	.done(function (data) {
        		jQuery('#report-pzp-content').html(data);
        	});
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
    });

    //set comment editable
    jQuery('#win-for-isak-report-pzp').off().on('change', 'input[type=checkbox]', function (e) {

        var rowIndex = jQuery(e.currentTarget).data('rowIndex');

        if (typeof rowIndex !== 'undefined') {

            if(jQuery(e.currentTarget).is(':checked')) {

            table.datagrid('updateRow',{
                index: rowIndex,
                row: {
                    edited: true
                }
            });

            table.datagrid('beginEdit', rowIndex);
            var editorComment = table.datagrid('getEditor', {index:rowIndex,field:'comment'});
            jQuery(editorComment.target).focus();

            }else {

                table.datagrid('updateRow',{
                    index: rowIndex,
                    row: {
                        edited: false,
                        comment: ''
                    }
                });

                table.datagrid('endEdit', rowIndex);
            }
        }
    });

    //custom pager
    var pager = table.datagrid('getPager');
    pager.pagination({
        beforePageText: 'Стр.',
        displayMsg: 'От {from} до {to} от {total}'
    });
}

function initForIsakPZPFilterPanel() {

    jQuery('#pzp-filter-cropcode').combobox({
    	url: 'index.php?common-rpc=culture-combobox',
    	rpcParams: [{
    		'year':6,
    	}],
    	valueField: 'id',
    	textField: 'name',
    	loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
    	loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#pzp-filter-schema > input').combobox({
		url: 'index.php?common-rpc=schema-combobox',
		rpcParams: [{
			detailed_lfa:true,
			without_schema:true
		}],
		valueField: 'id',
		textField: 'name',
		editable: false,
		filter: function(q, row){
			var opts = jQuery(this).combobox('options');
			var text = row[opts.textField].toLowerCase();
			var value = row[opts.valueField];
			var find = q.toLowerCase();
			if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
			{
				return true;
			}
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

    jQuery('#pzp-filter-zemlishte > input').combobox({
        url: 'index.php?common-rpc=for-isak-ekate-combobox',
		rpcParams: [{
			'layer_id': _selectedLayerData.id,
			'layer_type': 9,
			'without_farm': true
		}],
        valueField: 'ekate',
        textField: 'text',
        filter: function(q, row){
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
            {
                return true;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function reportPZPFilter() {
    var table = jQuery('#report-schema-pzp-tables');

    table.datagrid({
        rpcParams:[{
        	layer_name:_selectedLayerData.attributes.layer_name,
        	is_schema: 1,
        	filterObj:{
        		prc_name: jQuery('#pzp-filter-prc-name').val(),
                culture: jQuery('#pzp-filter-cropcode').combobox('getValue'),
                comment: jQuery('#pzp-filter-comment').val(),
                edited: jQuery('#pzp-filter-edited').combobox('getValue'),
                schema: jQuery('#pzp-filter-schema > input').combobox('getValue'),
                ekate: jQuery('#pzp-filter-zemlishte > input').combobox('getValue'),
        	}
        }]
    });
}

function clearPZPReportFilter() {

    var table = jQuery('#report-schema-pzp-tables');

    jQuery('#pzp-filter-prc-name').val('');
    jQuery('#pzp-filter-cropcode').combobox('clear');
    jQuery('#pzp-filter-comment').val('');
    jQuery('#pzp-filter-edited').combobox('reset');
    jQuery('#pzp-filter-schema > input').combobox('clear');
    jQuery('#pzp-filter-zemlishte > input').combobox('clear');

    table.datagrid({
    	 rpcParams:[{
         	layer_name:_selectedLayerData.attributes.layer_name,
         	is_schema: 1,
         	filterObj:{}
         }]
    });
}

function onReportPZPComplete(sender, parameter) {

    jQuery('#report-pzp-content').html(parameter);
}
