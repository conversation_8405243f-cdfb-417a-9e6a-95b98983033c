var date = new Date();
var todayDate = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();

var SCHEMA_SEPP = 1;
var SCHEMA_ZDP = 2;
var SCHEMA_PNDP = 3;
var SCHEMA_NR = 4;
var SCHEMA_NATURA_2000 = 5;
var SCHEMA_VPS = 6;
var SCHEMA_PZP = 7;

var SCHEMA_VPS_DETAILED = 60;
var SCHEMA_VPS_PASISHTA = 1;
var SCHEMA_VPS_GASKI_CHERVENOGUSHI = 2;
var SCHEMA_VPS_GASKI_ZIMNI = 3;
var SCHEMA_VPS_LIVADEN_BLATAR = 4;
var SCHEMA_VPS_ORLI_LESHOYADI  = 5;

var selected_vps;

var editIndex = undefined;

function initDefaultMapControls()
{
	controlNavigation = new OpenLayers.Control.Navigation({
		name: "navigation"
	});
	map.addControl(controlNavigation);

	controlScaleLine = new OpenLayers.Control.ScaleLine({
		name: "scaleline",
		bottomInUnits: 'km'
	});
	map.addControl(controlScaleLine);

	controlLineMeasure = new OpenLayers.Control.DynamicMeasure(OpenLayers.Handler.Path, {
		name: 'linemeasure',
		persist: true,
		geodesic: true
	});
	map.addControl(controlLineMeasure);

	controlPolygonMeasure = new OpenLayers.Control.DynamicMeasure(OpenLayers.Handler.Polygon, {
		name: "polygonmeasure",
		persist: true,
		geodesic: true
	});
	map.addControl(controlPolygonMeasure);

	controlZoomIn = new OpenLayers.Control.ZoomBox({
		name: "zoomin",
		title: "Zoom in box",
		out: false
	});
	map.addControl(controlZoomIn);

	controlZoomOut = new OpenLayers.Control.ZoomBox({
		name: "zoomout",
		title: "Zoom out box",
		out: true
	});
	map.addControl(controlZoomOut);
}
function initControls()
{
    //open attribute info window and load it's data
    jQuery('#tool-attr-info').bind('click', function () {

        initFORISAKGrid(_selectedLayerData.id);
        jQuery('#win-layer-attr-info').window('open');
    });

  //select layer object button
    jQuery('#tool-choose-layer-object').bind('click', function()
    {
        var options = jQuery('#tool-choose-layer-object').linkbutton('options');
        if (!options.selected)
        {
            if (!options.disabled)
            {
                unselectAll();
                jQuery('#tool-choose-layer-object').linkbutton('select');
                chooseControl('none');
                map.events.register('click', map, propertyWindowFunction);
            }
        } else
        {
            jQuery('#tool-choose-layer-object').linkbutton('unselect');
            map.events.unregister('click', map, propertyWindowFunction);
        }
    });

    //init filter fields
    jQuery('#select-schema').combobox({
		url: 'index.php?common-rpc=schema-combobox',
		rpcParams: [{
			selected:true,
		}],
		valueField: 'id',
		textField: 'name',
		filter: function(q, row){
			var opts = jQuery(this).combobox('options');
			var text = row[opts.textField].toLowerCase();
			var value = row[opts.valueField];
			var find = q.toLowerCase();
			if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
			{
				return true;
			}
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

    jQuery('#check-schema').bind('click', function () {

        var schema_value = jQuery('#select-schema').combobox('getValue');

        if (schema_value == SCHEMA_SEPP) {
            initForIsakSEPPFilterPanel();
            initForIsakDiffAllowableFinalGrid(_selectedLayerData.attributes.layer_name, _selectedLayerData.id);

            jQuery('#win-for-isak-diff-allowable-final').window('open');

            selectLayerByName('layer_allowable_final');

            var obj = new Object();
            obj.layer_name = _selectedLayerData.attributes.layer_name;
            obj.layer_id = _selectedLayerData.id;

            TF.Rpc.SubsidiesWizard.ForIsakDiffAllowableFinalGrid.initForIsakDiffAllowableFinal(obj)
        	.done(function (data) {
        		loadMapLayer('for_isak_diff_allowable_final');
        	});
        }
        else if (schema_value == SCHEMA_NR) {
            selectLayerByName('layer_lfa');

            initForIsakLFAFilterPanel();
            initForIsakDiffLfaGrid(_selectedLayerData.attributes.layer_name);

            var layer_name = _selectedLayerData.attributes.layer_name;
            var lyaer_id = _selectedLayerData.id;

            TF.Rpc.SubsidiesWizard.ForIsakDiffLfaGrid.initForIsakDiffLfa(layer_name, lyaer_id)
        	.done(function (data) {
        		loadMapLayer('for_isak_diff_lfa');
        	});

            jQuery('#win-for-isak-diff-lfa').window('open');
        }
        else if (schema_value == SCHEMA_PNDP) {

            initForIsakPNDPGrid(_selectedLayerData.attributes.layer_name);

            jQuery('#win-for-isak-pndp').window('open');
        }
        else if (schema_value == SCHEMA_ZDP) {

            initForIsakZDPFilterPanel();
            initReportZDPGrid(_selectedLayerData.attributes.layer_name);

            jQuery('#win-for-isak-report-zdp').window('open');

            //select first tab by default
            _reloadZDPGrid = false;
            jQuery('#tabs-zdp').tabs('select', 0);
        }
        else if (schema_value == SCHEMA_NATURA_2000) {
            selectLayerByName('layer_natura_2000');

            initForIsakNaturaFilterPanel();
            initForIsakDiffNaturaGrid(_selectedLayerData.attributes.layer_name);

            var layer_name = _selectedLayerData.attributes.layer_name;

            TF.Rpc.SubsidiesWizard.ForIsakDiffNaturaGrid.initForIsakDiffNatura(layer_name)
        	.done(function (data) {
        		loadMapLayer('for_isak_diff_natura');
        	});

            jQuery('#win-for-isak-diff-natura').window('open');
        }
        else if(schema_value == SCHEMA_VPS) {
        	initForIsakVPSMainGrid(_selectedLayerData.attributes.layer_name);
    		jQuery('#win-for-isak-vps-main').window('open');
        }
        else if(schema_value == SCHEMA_PZP) {

            initForIsakPZPFilterPanel();
            initReportSchemaPZPGrid(_selectedLayerData.attributes.layer_name);

            jQuery('#win-for-isak-report-pzp').window('open');

            selectLayerByName('layer_pzp');

            var layer_name = _selectedLayerData.attributes.layer_name;
            var layer_id = _selectedLayerData.id;
            var is_schema = true;
            TF.Rpc.SubsidiesWizard.ForIsakPZPReportGrid.initForIsakDiffPZP(layer_name, layer_id, is_schema)
        	.done(function (data) {
        		loadMapLayer('for_isak_diff_pzp');
        	});
        }

        jQuery('#win-layer-attr-info').window('close');
    });

    //ZDP Help Button
    jQuery('#for-isak-zdp-help').bind('click', function () {

        jQuery('#win-for-isak-zdp-help').window('open');
    });

    jQuery('#check-vps-schema').bind('click', function () {

    	initForIsakVPSMainGrid(_selectedLayerData.attributes.layer_name);
		jQuery('#win-for-isak-vps-main').window('open');
  });
}

function selectLayerByName(layer_name) {
    var layersTree = jQuery('#all-layers-tree');
    var root = layersTree.tree('getRoot');
    var rootChilds = layersTree.tree('getChildren', root.target);

    var layers = jQuery.grep(rootChilds, function(element) {
        return layersTree.tree('isLeaf', element.target);
    });

    layers.forEach(function(el) {
        if(el.attributes.layer_name == layer_name) {
            if(!el.checked) {
                layersTree.tree('check', el.target);
            }
        }
        else {
            layersTree.tree('uncheck', el.target);
        }
    });
}

function displayFeatureSelection(geom)
{
    //remove all previous features
    vectors.removeAllFeatures();

    var in_options = {
        'internalProjection': map.baseLayer.projection,
        'externalProjection': new OpenLayers.Projection("EPSG:32635")
    };
    var features = new OpenLayers.Format.WKT(in_options).read(geom);

    var bounds;

    if (features) {
        if (features.constructor != Array) {
            features = [features];
        }
        for (var i = 0; i < features.length; ++i) {
            if (!bounds) {
                bounds = features[i].geometry.getBounds();
            } else {
                bounds.extend(features[i].geometry.getBounds());
            }
        }

        vectors.addFeatures(features);
        var bounds = vectors.getDataExtent();
        map.zoomToExtent(bounds);
    }
}

function initForIsakDiffAllowableFinalGrid(layer_name, layer_id)
{
    var table = jQuery('#for-isak-diff-allowable-final-tables');

    table.datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        singleSelect: false,
        fit: true,
        fitColumns: true,
        showFooter: true,
        rownumbers: true,
        url: 'index.php?subsidieswizard-rpc=for-isak-diff-allowable-final-grid',
        rpcParams:[{
        	'layer_name':layer_name,
        	'layer_id': layer_id,
        }],
        border: true,
        pagination: true,
        sortName: 'gid',
        sortOrder: 'desc',
        frozenColumns: [[
                {
                    field: 'ck',
                    checkbox: true
                }
            ]],
        columns: [
            [
                {
                    field: 'prc_name',
                    title: '<b>Име</b>',
                    sortable: true,
                    width: 60
                }, {
                    field: 'cropname',
                    title: '<b>Култура</b>',
                    sortable: true,
                    width: 60,
                    align: 'center',
                    styler: function (value, row, index) {
                        if(value == null  && row.prc_name != "<b>Общо:</b>"){
                            return 'background-color: #99CCCC;';
                        }
                    }
                }, {
                    field: 'area',
                    title: '<b>Площ <br/>(ха)</b>',
                    sortable: true,
                    width: 40,
                    align: 'center'
                }, {
                    field: 'inside_area',
                    title: '<b>Площ в <br/>"Допустим <br/>слой"(ха)</b>',
                    sortable: true,
                    width: 40,
                    align: 'center'
                }, {
                    field: 'outside_area',
                    title: '<b>Площ извън<br/>"Допустим <br/>слой"(ха)</b>',
                    sortable: true,
                    width: 40,
                    align: 'center',
                    styler: function (value, row, index) {
                        return 'color: red;';
                    }
                }, {
                    field: 'sepp',
                    title: '<b>Заявен</b>',
                    sortable: true,
                    width: 30,
                    align: 'center',
                    formatter: function (value, row, index) {
                        if (typeof value !== 'undefined') {

                            if (value == true)
                            {
                                return 'Да';
                            } else {
                                return 'Не';
                            }

                        } else {
                            return '';
                        }
                    }
                }, {
                    field: 'edited',
                    title: '<b>За <br/>редактиране</b>',
                    sortable: false,
                    width: 50,
                    align: 'center',
                    formatter: function (value, row, index) {
                        if (typeof value !== 'undefined') {
                            if (value == true)
                            {
                                return '<input id="commentCheckBox' + index + '" data-row-index="' + index + '" type="checkbox" checked/>';
                            } else {
                                return '<input id="commentCheckBox' + index + '" data-row-index="' + index + '" type="checkbox" />';
                            }
                        } else {
                            return '';
                        }
                    }
                }, {
                    field: 'comment',
                    title: '<b>Коментар</b>',
                    sortable: false,
                    width: 40,
                    align: 'center',
                    editor: {
                        type: 'text'
                    }
                }
            ]
        ],
        toolbar: '#for-isak-diff-allowable-final-tables-toolbar',
        onSelect: function (rowIndex, rowData) {
            checkedElement = rowData;
            displayFeatureSelection(rowData.st_astext);
        },
        onBeforeLoad: function () {
            table.datagrid('clearChecked');
        },
        onClickCell: function (index,field,value){
            if(field == 'comment'){
                if(jQuery('#commentCheckBox' + index).is(':checked')){
                    table.datagrid('beginEdit', index);
                }
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    //set comment editable
    jQuery('#win-for-isak-diff-allowable-final').off().on('change', 'input[type=checkbox]', function (e) {

        var rowIndex = jQuery(e.currentTarget).data('rowIndex');

        if (typeof rowIndex !== 'undefined') {

            if (jQuery(e.currentTarget).is(':checked')) {

                table.datagrid('updateRow', {
                    index: rowIndex,
                    row: {
                        edited: true
                    }
                });

                table.datagrid('beginEdit', rowIndex);
                var editorComment = table.datagrid('getEditor', {index: rowIndex, field: 'comment'});
                jQuery(editorComment.target).focus();

            } else {

                table.datagrid('updateRow', {
                    index: rowIndex,
                    row: {
                        edited: false,
                        comment: ''
                    }
                });
                table.datagrid('endEdit', rowIndex);
            }
        }
    });

    //custom pager
    var pager = table.datagrid('getPager');
    pager.pagination({
        beforePageText: 'Стр.',
        displayMsg: 'От {from} до {to} от {total}'
    });
}

function saveForEditAndCommentSEPP(tableId) {
    var table;
    if (!tableId) {
        table = jQuery('#for-isak-diff-allowable-final-tables');
    }
    else {
        table = jQuery(tableId);
    }

    var rows = table.datagrid('getRows');

    for (var i = 0; i < rows.length; i++) {
        table.datagrid('endEdit', i);

        rows[i].layer_name = _selectedLayerData.attributes.layer_name;
    };

    TF.Rpc.SubsidiesWizard.ForIsakPZPReportGrid.saveForIsakData(rows)
	.done(function (data) {
	});
}

function requestSEPP(fieldValue) {

    var table = jQuery('#for-isak-diff-allowable-final-tables');

    var rows = table.datagrid('getSelections');

    if (rows.length == 0) {
        jQuery.messager.alert('Грешка', 'Моля изберете парцелите от таблицата, които искате да заявите за СЕПП!', 'warning');
        return false;
    }

    for (var i = 0; i < rows.length; i++) {
        rows[i].layer_name = _selectedLayerData.attributes.layer_name;
        rows[i].field = "sepp";
        rows[i].field_value = fieldValue;
        rows[i].id = 'for-isak-diff-allowable-final-tables';
    }

    TF.Rpc.SubsidiesWizard.ForIsakPZPReportGrid.saveForRequest(rows)
	.done(function (data) {
		  var table = jQuery('#'+data.id+'');

		    table.datagrid('reload');
	}).fail(function(error) {
        jQuery.messager.alert('Грешка', error.getMessage());
    });
}

function saveForEditAndCommentZDP() {

    var table = jQuery('#report-zdp-tables');

    var rows = table.datagrid('getRows');

    for (var i = 0; i < rows.length; i++) {
        table.datagrid('endEdit', i);

        rows[i].layer_name = _selectedLayerData.attributes.layer_name;
    }

    TF.Rpc.SubsidiesWizard.ForIsakPZPReportGrid.saveForIsakData(rows)
	.done(function (data) {
	});
}

function saveForEditAndCommentPZP() {

    var table = jQuery('#report-pzp-tables');

    var rows = table.datagrid('getRows');

    for (var i = 0; i < rows.length; i++) {
        table.datagrid('endEdit', i);

        rows[i].layer_name = _selectedLayerData.attributes.layer_name;
    }

    TF.Rpc.SubsidiesWizard.ForIsakPZPReportGrid.saveForIsakData(rows)
	.done(function (data) {
	});
}

function saveForEditAndCommentSchemaPZP() {

    var table = jQuery('#report-schema-pzp-tables');

    var rows = table.datagrid('getRows');

    for (var i = 0; i < rows.length; i++) {
        table.datagrid('endEdit', i);

        rows[i].layer_name = _selectedLayerData.attributes.layer_name;
    };

    TF.Rpc.SubsidiesWizard.ForIsakPZPReportGrid.saveForIsakData(rows)
	.done(function (data) {
	});
}

function requestZDP(fieldValue) {

    var table = jQuery('#report-zdp-tables');

    var rows = table.datagrid('getSelections');

    if (rows.length == 0) {
        jQuery.messager.alert('Грешка', 'Моля изберете парцелите от таблицата, които искате да заявите за ЗДП!', 'warning');
        return false;
    }

    for (var i = 0; i < rows.length; i++) {

        if (rows[i].is_active_culture == false) {
            jQuery.messager.alert('Грешка', 'Като заявени ще бъдат отбелязани само парцелите, с култура отговаряща на номенклатурата за настоящата година на кандидатстване!', 'warning');
            return false;
        }

        rows[i].layer_name = _selectedLayerData.attributes.layer_name;
        rows[i].field = "zdp";
        rows[i].field_value = fieldValue;
        rows[i].id = 'report-zdp-tables';
    }

    TF.Rpc.SubsidiesWizard.ForIsakPZPReportGrid.saveForRequest(rows)
	.done(function (data) {
		  var table = jQuery('#'+data.id+'');

		    table.datagrid('reload');
	}).fail(function(error) {
        jQuery.messager.alert('Грешка', error.getMessage());
    });
}

function initForIsakSEPPFilterPanel() {

    jQuery('#sepp-filter-cropcode').combobox({
		url: 'index.php?common-rpc=culture-combobox',
		rpcParams: [{
			'year':6,
			'without_crops':true
		}],
		valueField: 'id',
		textField: 'name',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function seppReportFilter() {
    var table = jQuery('#for-isak-diff-allowable-final-tables');

    table.datagrid({
        rpcParams: [{
        	'layer_name':_selectedLayerData.attributes.layer_name,
        	'filterObj':{
		        prc_name: jQuery('#sepp-filter-prc-name').val(),
		        culture: jQuery('#sepp-filter-cropcode').combobox('getValue'),
		        comment: jQuery('#sepp-filter-comment').val(),
		        edited: jQuery('#sepp-filter-edited').combobox('getValue'),
		        sepp: jQuery('#sepp-filter-requested').combobox('getValue'),
		    }
        }]
    });
}

function clearSeppReportFilter() {

    var table = jQuery('#for-isak-diff-allowable-final-tables');

    jQuery('#sepp-filter-prc-name').val('');
    jQuery('#sepp-filter-cropcode').combobox('clear');
    jQuery('#sepp-filter-comment').val('');
    jQuery('#sepp-filter-edited').combobox('reset');
    jQuery('#sepp-filter-requested').combobox('reset');

    table.datagrid({
    	rpcParams: [{
            layer_name: _selectedLayerData.attributes.layer_name,
        }]
    });
}

function saveForIsakPNDPData() {

    var table = jQuery('#for-isak-pndp-tables');

    var rows = table.datagrid('getRows');

    for (var i = 0; i < rows.length; i++) {
        table.datagrid('endEdit', i);

        rows[i].layer_name = _selectedLayerData.attributes.layer_name;
    };

    TF.Rpc.SubsidiesWizard.ForIsakPZPReportGrid.saveForIsakData(rows)
	.done(function (data) {
	});
}

function requestPNDP(fieldValue) {

    var table = jQuery('#for-isak-pndp-tables');

    var rows = table.datagrid('getSelections');

    if (rows.length == 0) {
        jQuery.messager.alert('Грешка', 'Моля изберете парцелите от таблицата, които искате да заявите за ПНДП!', 'warning');
        return false;
    }

    for (var i = 0; i < rows.length; i++) {

        if (rows[i].is_active_culture == false) {
            jQuery.messager.alert('Грешка', 'Като заявени ще бъдат отбелязани само парцелите, с култура отговаряща на номенклатурата за настоящата година на кандидатстване!', 'warning');
            return false;
        }

        if (rows[i].unallowed_crop_type == true || !rows[i].cropcode) {
            jQuery.messager.alert('Грешка', 'Парцели с култура Постоянни пасища, Тютюн, Винени сортове лозя, както и такива без указана култура са неподходящи за заявяване по ПНДП!', 'warning');
            return false;
        }
        rows[i].layer_name = _selectedLayerData.attributes.layer_name;
        rows[i].field = "pndp";
        rows[i].field_value = fieldValue;
        rows[i].id = 'for-isak-pndp-tables';
    }

    TF.Rpc.SubsidiesWizard.ForIsakPZPReportGrid.saveForRequest(rows)
	.done(function (data) {
	  	var table = jQuery('#'+data.id+'');
	    table.datagrid('reload');
	}).fail(function(error) {
        jQuery.messager.alert('Грешка', error.getMessage());
    });
}

function pndpReportFilter() {
    var table = jQuery('#for-isak-pndp-tables');

    table.datagrid({
    	  rpcParams: [{
          	'layer_name':_selectedLayerData.attributes.layer_name,
    		'filterObj': {
    			prc_name: jQuery('#pndp-filter-prc-name').val(),
                cropname: jQuery('#pndp-filter-cropname').combobox('getValue'),
                ekatte: jQuery('#pndp-filter-ekatte').combobox('getValue'),
    		}
        }]
    });
}

function clearPNDPReportFilter() {

    var table = jQuery('#for-isak-pndp-tables');

    jQuery('#pndp-filter-prc-name').val('');
    jQuery('#pndp-filter-cropname').combobox('reset');
    jQuery('#pndp-filter-ekatte').combobox('reset');

    table.datagrid({
    	rpcParams: [{
            'layer_name': _selectedLayerData.attributes.layer_name,
            'filterObj':{}
        }]
    });
}

function reloadDatagrid(sender, parameter) {

    var table = jQuery('#'+parameter.id+'');

    table.datagrid('reload');
}

function initForIsakPNDPGrid(layer_name)
{
    var table = jQuery('#for-isak-pndp-tables');

    table.datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        singleSelect: false,
        fit: true,
        width: 776,
        height: 344,
        fitColumns: false,
        showFooter: true,
        rownumbers: true,
        url: 'index.php?subsidieswizard-rpc=for-isak-report-pndp-grid',
        rpcParams:[{
        	'layer_name':layer_name,
        }],
        border: true,
        pagination: true,
        sortName: 'gid',
        sortOrder: 'desc',
        rowStyler: function (index, row) {

            if (row.unallowed_crop_type == true) {
                return 'background-color:#FF0000;';
            }
        },
        frozenColumns: [[
                {
                    field: 'ck',
                    checkbox: true
                }
            ]],
        columns: [
            [
                {
                    field: 'prc_name',
                    title: '<b>Име</b>',
                    sortable: true,
                    width: 100
                }, {
                    field: 'ekatte',
                    title: '<b>Землище</b>',
                    sortable: true,
                    width: 100
                }, {
                    field: 'cropname',
                    title: '<b>Култура</b>',
                    sortable: true,
                    width: 160,
                    align: 'center',
                    styler: function (value, row, index) {
                    	if(row.is_active_culture == false){
	                		return 'background-color:#F69232;';
	                    }
                        if(value == null  && row.prc_name != "<b>Общо за стр.:</b>"){
                        	return 'background-color: #99CCCC;';
                        }
                    }
                }, {
                    field: 'area',
                    title: '<b>Площ <br/>(ха)</b>',
                    sortable: true,
                    width: 80,
                    align: 'center'
                }, {
                    field: 'is_requested',
                    title: '<b>Заявено</b>',
                    sortable: true,
                    width: 60,
                    align: 'center'
                }, {
                    field: 'edited',
                    title: '<b>За <br/>редактиране</b>',
                    sortable: true,
                    width: 100,
                    align: 'center',
                    formatter: function (value, row, index) {
                        if (typeof value !== 'undefined') {
                            if (value == true)
                            {
                                return '<input id="for-isak-pndp-checkbox-' + index + '" data-row-index="' + index + '" type="checkbox" checked/>';
                            } else {
                                return '<input id="for-isak-pndp-checkbox-' + index + '" data-row-index="' + index + '" type="checkbox" />';
                            }
                        } else {
                            return '';
                        }
                    }
                }, {
                    field: 'comment',
                    title: '<b>Коментар</b>',
                    sortable: true,
                    width: 160,
                    align: 'center',
                    editor: {
                        type: 'text'
                    }
                }
            ]
        ],
        toolbar: '#for-isak-pndp-tables-toolbar',
        onSelect: function (rowIndex, rowData) {
            checkedElement = rowData;
            displayFeatureSelection(rowData.st_astext);
        },
        onBeforeLoad: function () {
            table.datagrid('clearChecked');
        },
        onClickCell: function (index,field,value){
            if(field == 'comment'){
                if(jQuery('#for-isak-pndp-checkbox-' + index).is(':checked')){
                    table.datagrid('beginEdit', index);
                }
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
    	loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    //set comment editable
    jQuery('#win-for-isak-pndp').off().on('change', 'input[type=checkbox]', function (e) {

        var rowIndex = jQuery(e.currentTarget).data('rowIndex');

        if (typeof rowIndex !== 'undefined') {

            if (jQuery(e.currentTarget).is(':checked')) {

                table.datagrid('updateRow', {
                    index: rowIndex,
                    row: {
                        edited: true
                    }
                });

                table.datagrid('beginEdit', rowIndex);
                var editorComment = table.datagrid('getEditor', {index: rowIndex, field: 'comment'});
                jQuery(editorComment.target).focus();

            } else {
                table.datagrid('updateRow', {
                    index: rowIndex,
                    row: {
                        edited: false,
                        comment: ''
                    }
                });

                table.datagrid('endEdit', rowIndex);
            }
        }
    });

    jQuery('#pndp-filter-cropname').combobox({
    	url: 'index.php?common-rpc=culture-combobox',
    	rpcParams: [{
    		'year':6,
    		'without_crops':true
    	}],
    	valueField: 'id',
    	textField: 'name',
    	filter: function (q, row) {
    		var opts = jQuery(this).combobox('options');
    		var text = row[opts.textField].toLowerCase();
    		var value = row[opts.valueField];
    		var find = q.toLowerCase();
    		if (text.indexOf(find) != -1 || value.indexOf(find) != -1)
    		{
    			return true;
    		}
    	},
    	loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
    	loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#pndp-filter-ekatte').combobox({
		url: 'index.php?common-rpc=ekate-combobox',
		rpcParams: [{}],
		valueField: 'ekate',
		textField: 'text',
		filter: function(q, row){
			var opts = jQuery(this).combobox('options');
			var text = row[opts.textField].toLowerCase();
			var value = row[opts.valueField];
			var find = q.toLowerCase();
			if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
			{
				return true;
			}
		},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function chooseControl(control_name)
{
    var mapControls = map.options.controls;
    for (var i = 0; i < mapControls.length; i++)
    {
        var control = mapControls[i];
        if (mapControls[i].name === control_name)
        {
            control.activate();
        } else
        {
            control.deactivate();
        }
    }
}

function exportReportToDocument(schema_type, document_type){

	var obj = new Object();
	var json;
	var filters;
	if(document_type == 'xls') {
		if(schema_type == SCHEMA_ZDP){
			var plotsGridData = jQuery('#report-zdp-tables').datagrid('getData');
			var plotsGridOptions = jQuery('#report-zdp-tables').datagrid('options');

			var rpcParams ={
	         	'layer_name':_selectedLayerData.attributes.layer_name,
	         	'filterObj':{
	         		prc_name: jQuery('#zdp-filter-prc-name').val(),
	         		culture: jQuery('#zdp-filter-cropcode').combobox('getValue'),
		            comment: jQuery('#zdp-filter-comment').val(),
		            edited: jQuery('#zdp-filter-edited').combobox('getValue'),
		            zdp: jQuery('#zdp-filter-requested').combobox('getValue'),
		            culture_short_type: jQuery('#zdp-filter-crop-short-type').combobox('getValue')
	         	},
	         	'layer_type': 9,
	         	'layer_id':_selectedLayerData.id,
	         	'export2document':true,
	        };
			TF.Rpc.SubsidiesWizard.ForIsakZDPReportGrid.exportGridToXLS(rpcParams, plotsGridData.total, plotsGridOptions.sortName, plotsGridOptions.sortOrder)
			.done(function (data) {
				jQuery('#win-download').window('open');
	            var path = data.exl_report_file;
	            _pathFile = path;
	            jQuery('#btn-download-file').attr("href", path);
			});
		}else if(schema_type == SCHEMA_SEPP){
			var plotsGridData = jQuery('#for-isak-diff-allowable-final-tables').datagrid('getData');
			var plotsGridOptions = jQuery('#for-isak-diff-allowable-final-tables').datagrid('options');

			var rpcParams ={
	         	'layer_name':_selectedLayerData.attributes.layer_name,
	         	'filterObj':{
	         		prc_name: jQuery('#sepp-filter-prc-name').val(),
	         		culture: jQuery('#sepp-filter-cropcode').combobox('getValue'),
		            comment: jQuery('#sepp-filter-comment').val(),
		            edited: jQuery('#sepp-filter-edited').combobox('getValue'),
		            sepp: jQuery('#sepp-filter-requested').combobox('getValue')
	         	},
	         	'layer_type': 9,
	         	'layer_id':_selectedLayerData.id,
	         	'export2document':true,
	        };
			TF.Rpc.SubsidiesWizard.ForIsakDiffAllowableFinalGrid.exportGridToXLS(rpcParams, plotsGridData.total, plotsGridOptions.sortName, plotsGridOptions.sortOrder)
			.done(function (data) {
				jQuery('#win-download').window('open');
	            var path = data.exl_report_file;
	            _pathFile = path;
	            jQuery('#btn-download-file').attr("href", path);
			});
		}else if(schema_type == SCHEMA_NR){
			var plotsGridData = jQuery('#for-isak-diff-lfa-tables').datagrid('getData');
			var plotsGridOptions = jQuery('#for-isak-diff-lfa-tables').datagrid('options');

			var rpcParams ={
	         	'layer_name':_selectedLayerData.attributes.layer_name,
	         	'filterObj':{
	         		prc_name: jQuery('#lfa-filter-prc-name').val(),
		            ekate: jQuery('#lfa-filter-ekate').combobox('getValue'),
		            comment: jQuery('#lfa-filter-comment').val(),
		            edited: jQuery('#lfa-filter-edited').combobox('getValue'),
		            nm_lfa: jQuery('#lfa-filter-nm-lfa').combobox('getValue'),
		            lfa: jQuery('#lfa-filter-requested').combobox('getValue')
	         	},
	         	'layer_type': 9,
	         	'layer_id':_selectedLayerData.id,
	         	'export2document':true,
	        };
			TF.Rpc.SubsidiesWizard.ForIsakDiffLfaGrid.exportGridToXLS(rpcParams, plotsGridData.total, plotsGridOptions.sortName, plotsGridOptions.sortOrder)
			.done(function (data) {
				jQuery('#win-download').window('open');
	            var path = data.exl_report_file;
	            _pathFile = path;
	            jQuery('#btn-download-file').attr("href", path);
			});
		} else if(schema_type == SCHEMA_NATURA_2000) {
            var plotsGridData = jQuery('#for-isak-diff-natura-tables').datagrid('getData');
            var plotsGridOptions = jQuery('#for-isak-diff-natura-tables').datagrid('options');

            var rpcParams ={
                'layer_name':_selectedLayerData.attributes.layer_name,
                'filterObj':{
                    comment: jQuery('#natura-filter-comment').val(),
                    cropcode: jQuery('#natura-filter-cropcode').combobox('getValue'),
                    edited: jQuery('#natura-filter-edited').combobox('getValue'),
                    natura: jQuery('#natura-filter-requested').combobox('getValue'),
                    prc_name: jQuery('#natura-filter-prc-name').val(),
                    sitecode_gid: jQuery('#natura-filter-sitecode').combobox('getValue'),
                },
                'layer_type': 9,
                'layer_id':_selectedLayerData.id,
                'export2document':true,
            };

            TF.Rpc.SubsidiesWizard.ForIsakDiffNaturaGrid.exportGridToXLS(rpcParams, plotsGridData.total, plotsGridOptions.sortName, plotsGridOptions.sortOrder)
            .done(function (data) {
                jQuery('#win-download').window('open');
                var path = data.exl_report_file;
                _pathFile = path;
                jQuery('#btn-download-file').attr("href", path);
            });
        } else if(schema_type == SCHEMA_VPS) {
            var plotsGridData = jQuery('#for-isak-diff-vps-main-tables').datagrid('getData');
            var plotsGridOptions = jQuery('#for-isak-diff-vps-main-tables').datagrid('options');

            var rpcParams ={
                'layer_name':_selectedLayerData.attributes.layer_name,
                'filterObj':{
                    prc_name: jQuery('#vps-filter-prc-name').val(),
                    cropcode: jQuery('#vps-filter-cropcode').combobox('getValue'),
                    ekatte: jQuery('#vps-filter-ekatte').combobox('getValue'),
                    vps_type: jQuery('#vps-type-filter-combobox').combobox('getValue'),
                },
                'layer_type': 9,
                'layer_id':_selectedLayerData.id,
                'export2document':true,
            };

            TF.Rpc.SubsidiesWizard.ForIsakDiffVPSMainGrid.exportGridToXLS(rpcParams, plotsGridData.total, plotsGridOptions.sortName, plotsGridOptions.sortOrder)
            .done(function (data) {
                jQuery('#win-download').window('open');
                var path = data.exl_report_file;
                _pathFile = path;
                jQuery('#btn-download-file').attr("href", path);
            });

        } else if(schema_type == SCHEMA_VPS_DETAILED) {

            var plotsGridData = jQuery('#for-isak-diff-vps-detailed-tables').datagrid('getData');
            var plotsGridOptions = jQuery('#for-isak-diff-vps-detailed-tables').datagrid('options');

            var rpcParams ={
                'layer_name':_selectedLayerData.attributes.layer_name,
                'vps_layer_name': vps_layer_name,
                'filterObj':{
                    prc_name: jQuery('#detailed-vps-filter-prc-name').val(),
                    cropname: jQuery('#detailed-vps-filter-cropcode').combobox('getValue'),
                    edited: jQuery('#detailed-vps-filter-edited').combobox('getValue'),
                    comment: jQuery('#detailed-vps-filter-comment').val(),
                },
                'layer_type': 9,
                'layer_id':_selectedLayerData.id,
                'export2document':true,
            };

            TF.Rpc.SubsidiesWizard.ForIsakDiffVPSDetailedGrid.exportGridToXLS(rpcParams, plotsGridData.total, plotsGridOptions.sortName, plotsGridOptions.sortOrder)
            .done(function (data) {
                jQuery('#win-download').window('open');
                var path = data.exl_report_file;
                _pathFile = path;
                jQuery('#btn-download-file').attr("href", path);
            });
        }
        else if(schema_type == SCHEMA_PZP) {
            var plotsGridData = jQuery('#report-schema-pzp-tables').datagrid('getData');
            var plotsGridOptions = jQuery('#report-schema-pzp-tables').datagrid('options');

            var rpcParams ={
                'layer_name':_selectedLayerData.attributes.layer_name,
                'filterObj':{
                    prc_name: jQuery('#pzp-filter-prc-name').val(),
                    culture: jQuery('#pzp-filter-cropcode').combobox('getValue'),
                    comment: jQuery('#pzp-filter-comment').val(),
                    edited: jQuery('#pzp-filter-edited').combobox('getValue'),
                    schema: jQuery('#pzp-filter-schema > input').combobox('getValue'),
                    ekate: jQuery('#pzp-filter-zemlishte > input').combobox('getValue'),
                },
                'layer_type': 9,
                'layer_id':_selectedLayerData.id,
                'export2document':true,
            };

            TF.Rpc.SubsidiesWizard.ForIsakPZPReportGrid.exportGridToXLS(rpcParams, plotsGridData.total, plotsGridOptions.sortName, plotsGridOptions.sortOrder)
            .done(function (data) {
                jQuery('#win-download').window('open');
                var path = data.exl_report_file;
                _pathFile = path;
                jQuery('#btn-download-file').attr("href", path);
            });
        }
		else{
			var plotsGridData = jQuery('#for-isak-pndp-tables').datagrid('getData');
			var plotsGridOptions = jQuery('#for-isak-pndp-tables').datagrid('options');

			var rpcParams ={
	         	'layer_name':_selectedLayerData.attributes.layer_name,
	         	'filterObj':{
	         		prc_name: jQuery('#pndp-filter-prc-name').val(),
	         		cropname: jQuery('#pndp-filter-cropname').combobox('getValue'),
	         		ekatte: jQuery('#pndp-filter-ekatte').combobox('getValue'),
	         	},
	         	'layer_type': 9,
	         	'layer_id':_selectedLayerData.id,
	         	'export2document':true,
	        };
			TF.Rpc.SubsidiesWizard.ForIsakPNDPReportGrid.exportGridToXLS(rpcParams, plotsGridData.total, plotsGridOptions.sortName, plotsGridOptions.sortOrder)
			.done(function (data) {
				jQuery('#win-download').window('open');
	            var path = data.exl_report_file;
	            _pathFile = path;
	            jQuery('#btn-download-file').attr("href", path);
			});
		}
	}else{
		if(schema_type == SCHEMA_ZDP){
			var plotsGridData = jQuery('#report-zdp-tables').datagrid('getData');
			var plotsGridOptions = jQuery('#report-zdp-tables').datagrid('options');

			var rpcParams ={
	         	'layer_name':_selectedLayerData.attributes.layer_name,
	         	'filterObj':{
	         		prc_name: jQuery('#zdp-filter-prc-name').val(),
	         		culture: jQuery('#zdp-filter-cropcode').combobox('getValue'),
		            comment: jQuery('#zdp-filter-comment').val(),
		            edited: jQuery('#zdp-filter-edited').combobox('getValue'),
		            zdp: jQuery('#zdp-filter-requested').combobox('getValue'),
		            culture_short_type: jQuery('#zdp-filter-crop-short-type').combobox('getValue')
	         	},
	         	'layer_type': 9,
	         	'layer_id':_selectedLayerData.id,
	         	'export2document':true,
	        };
			TF.Rpc.SubsidiesWizard.ForIsakZDPReportGrid.exportGridToPDF(rpcParams, plotsGridData.total, plotsGridOptions.sortName, plotsGridOptions.sortOrder)
			.done(function (data) {
				jQuery('#win-download').window('open');
	            var path = data.pdf_report_file;
	            _pathFile = path;
	            jQuery('#btn-download-file').attr("href", path);
			});
		}else if(schema_type == SCHEMA_SEPP){
			var plotsGridData = jQuery('#for-isak-diff-allowable-final-tables').datagrid('getData');
			var plotsGridOptions = jQuery('#for-isak-diff-allowable-final-tables').datagrid('options');

			var rpcParams ={
	         	'layer_name':_selectedLayerData.attributes.layer_name,
	         	'filterObj':{
	         		prc_name: jQuery('#sepp-filter-prc-name').val(),
	         		culture: jQuery('#sepp-filter-cropcode').combobox('getValue'),
		            comment: jQuery('#sepp-filter-comment').val(),
		            edited: jQuery('#sepp-filter-edited').combobox('getValue'),
		            sepp: jQuery('#sepp-filter-requested').combobox('getValue')
	         	},
	         	'layer_type': 9,
	         	'layer_id':_selectedLayerData.id,
	         	'export2document':true,
	        };
			TF.Rpc.SubsidiesWizard.ForIsakDiffAllowableFinalGrid.exportGridToPDF(rpcParams, plotsGridData.total, plotsGridOptions.sortName, plotsGridOptions.sortOrder)
			.done(function (data) {
				jQuery('#win-download').window('open');
	            var path = data.pdf_report_file;
	            _pathFile = path;
	            jQuery('#btn-download-file').attr("href", path);
			});
		}else if(schema_type == SCHEMA_NR){
			var plotsGridData = jQuery('#for-isak-diff-lfa-tables').datagrid('getData');
			var plotsGridOptions = jQuery('#for-isak-diff-lfa-tables').datagrid('options');

			var rpcParams ={
	         	'layer_name':_selectedLayerData.attributes.layer_name,
	         	'filterObj':{
	         		prc_name: jQuery('#lfa-filter-prc-name').val(),
		            ekate: jQuery('#lfa-filter-ekate').combobox('getValue'),
		            comment: jQuery('#lfa-filter-comment').val(),
		            edited: jQuery('#lfa-filter-edited').combobox('getValue'),
		            nm_lfa: jQuery('#lfa-filter-nm-lfa').combobox('getValue'),
		            lfa: jQuery('#lfa-filter-requested').combobox('getValue')
	         	},
	         	'layer_type': 9,
	         	'layer_id':_selectedLayerData.id,
	         	'export2document':true,
	        };
			TF.Rpc.SubsidiesWizard.ForIsakDiffLfaGrid.exportGridToPDF(rpcParams, plotsGridData.total, plotsGridOptions.sortName, plotsGridOptions.sortOrder)
			.done(function (data) {
				jQuery('#win-download').window('open');
	            var path = data.pdf_report_file;
	            _pathFile = path;
	            jQuery('#btn-download-file').attr("href", path);
			});
		} else if(schema_type == SCHEMA_NATURA_2000) {
            var plotsGridData = jQuery('#for-isak-diff-natura-tables').datagrid('getData');
            var plotsGridOptions = jQuery('#for-isak-diff-natura-tables').datagrid('options');

            var rpcParams ={
                'layer_name':_selectedLayerData.attributes.layer_name,
                'filterObj':{
                    comment: jQuery('#natura-filter-comment').val(),
                    cropcode: jQuery('#natura-filter-cropcode').combobox('getValue'),
                    edited: jQuery('#natura-filter-edited').combobox('getValue'),
                    natura: jQuery('#natura-filter-requested').combobox('getValue'),
                    prc_name: jQuery('#natura-filter-prc-name').val(),
                    sitecode_gid: jQuery('#natura-filter-sitecode').combobox('getValue'),
                },
                'layer_type': 9,
                'layer_id':_selectedLayerData.id,
                'export2document':true,
            };

            TF.Rpc.SubsidiesWizard.ForIsakDiffNaturaGrid.exportGridToPDF(rpcParams, plotsGridData.total, plotsGridOptions.sortName, plotsGridOptions.sortOrder)
            .done(function (data) {
                jQuery('#win-download').window('open');
                var path = data.pdf_report_file;
                _pathFile = path;
                jQuery('#btn-download-file').attr("href", path);
            });
        } else if(schema_type == SCHEMA_VPS) {
            var plotsGridData = jQuery('#for-isak-diff-vps-main-tables').datagrid('getData');
            var plotsGridOptions = jQuery('#for-isak-diff-vps-main-tables').datagrid('options');

            var rpcParams ={
                'layer_name':_selectedLayerData.attributes.layer_name,
                'filterObj':{
                    prc_name: jQuery('#vps-filter-prc-name').val(),
                    cropcode: jQuery('#vps-filter-cropcode').combobox('getValue'),
                    ekatte: jQuery('#vps-filter-ekatte').combobox('getValue'),
                    vps_type: jQuery('#vps-type-filter-combobox').combobox('getValue'),
                },
                'layer_type': 9,
                'layer_id':_selectedLayerData.id,
                'export2document':true,
            };

            TF.Rpc.SubsidiesWizard.ForIsakDiffVPSMainGrid.exportGridToPDF(rpcParams, plotsGridData.total, plotsGridOptions.sortName, plotsGridOptions.sortOrder)
            .done(function (data) {
                jQuery('#win-download').window('open');
                var path = data.pdf_report_file;
                _pathFile = path;
                jQuery('#btn-download-file').attr("href", path);
            });
        }else if(schema_type == SCHEMA_PZP) {
            var plotsGridData = jQuery('#report-schema-pzp-tables').datagrid('getData');
            var plotsGridOptions = jQuery('#report-schema-pzp-tables').datagrid('options');

            var rpcParams ={
                'layer_name':_selectedLayerData.attributes.layer_name,
                'filterObj':{
                    prc_name: jQuery('#pzp-filter-prc-name').val(),
                    culture: jQuery('#pzp-filter-cropcode').combobox('getValue'),
                    comment: jQuery('#pzp-filter-comment').val(),
                    edited: jQuery('#pzp-filter-edited').combobox('getValue'),
                    schema: jQuery('#pzp-filter-schema > input').combobox('getValue'),
                    ekate: jQuery('#pzp-filter-zemlishte > input').combobox('getValue'),
                },
                'layer_type': 9,
                'layer_id':_selectedLayerData.id,
                'export2document':true,
            };

            TF.Rpc.SubsidiesWizard.ForIsakPZPReportGrid.exportGridToPDF(rpcParams, plotsGridData.total, plotsGridOptions.sortName, plotsGridOptions.sortOrder)
            .done(function (data) {
                jQuery('#win-download').window('open');
                var path = data.pdf_report_file;
                _pathFile = path;
                jQuery('#btn-download-file').attr("href", path);
            });
        }
        else if(schema_type == SCHEMA_VPS_DETAILED) {

            var plotsGridData = jQuery('#for-isak-diff-vps-detailed-tables').datagrid('getData');
            var plotsGridOptions = jQuery('#for-isak-diff-vps-detailed-tables').datagrid('options');

            var rpcParams ={
                'layer_name':_selectedLayerData.attributes.layer_name,
                'vps_layer_name': vps_layer_name,
                'filterObj':{
                    prc_name: jQuery('#detailed-vps-filter-prc-name').val(),
                    cropname: jQuery('#detailed-vps-filter-cropcode').combobox('getValue'),
                    edited: jQuery('#detailed-vps-filter-edited').combobox('getValue'),
                    comment: jQuery('#detailed-vps-filter-comment').val(),
                },
                'layer_type': 9,
                'layer_id':_selectedLayerData.id,
                'export2document':true,
            };

            TF.Rpc.SubsidiesWizard.ForIsakDiffVPSDetailedGrid.exportGridToPDF(rpcParams, plotsGridData.total, plotsGridOptions.sortName, plotsGridOptions.sortOrder)
            .done(function (data) {
                jQuery('#win-download').window('open');
                var path = data.pdf_report_file;
                _pathFile = path;
                jQuery('#btn-download-file').attr("href", path);
            });
        }
		else{
			var plotsGridData = jQuery('#for-isak-pndp-tables').datagrid('getData');
			var plotsGridOptions = jQuery('#for-isak-pndp-tables').datagrid('options');

			var rpcParams ={
	         	'layer_name':_selectedLayerData.attributes.layer_name,
	         	'filterObj':{
	         		prc_name: jQuery('#pndp-filter-prc-name').val(),
	         		cropname: jQuery('#pndp-filter-cropname').combobox('getValue'),
	         		ekatte: jQuery('#pndp-filter-ekatte').combobox('getValue'),
	         	},
	         	'layer_type': 9,
	         	'layer_id':_selectedLayerData.id,
	         	'export2document':true,
	        };
			TF.Rpc.SubsidiesWizard.ForIsakPNDPReportGrid.exportGridToPDF(rpcParams, plotsGridData.total, plotsGridOptions.sortName, plotsGridOptions.sortOrder)
			.done(function (data) {
				jQuery('#win-download').window('open');
	            var path = data.pdf_report_file;
	            _pathFile = path;
	            jQuery('#btn-download-file').attr("href", path);
			});
		}
	}
	if(!plotsGridData['rows'].length)
	{
		jQuery.messager.alert('Грешка', 'Не са намерени записи за експорт!');
		return false;
	}
}

// main VPS grid
function initForIsakVPSMainGrid(layer_name)
{
    var table = jQuery('#for-isak-diff-vps-main-tables');

    table.datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        singleSelect: false,
        fit: true,
        fitColumns: true,
        showFooter: true,
        rownumbers: true,
        url: 'index.php?subsidieswizard-rpc=for-isak-diff-vps-main-grid',
        rpcParams: [{
        	'layer_name': layer_name
        }],
        border: true,
        pagination: true,
        sortName: 'for_isak.gid',
        sortOrder: 'desc',
        frozenColumns: [[
                {
                    field: 'ck',
                    checkbox: true
                }
            ]],
        columns: [
            [
                {
                    field: 'prc_name',
                    title: '<b>Име</b>',
                    sortable: true,
                    width: 60
                },
                {
                    field: 'zeml',
                    title: '<b>Землище</b>',
                    sortable: true,
                    width: 60
                }, {
                    field: 'cropname',
                    title: '<b>Култура</b>',
                    sortable: true,
                    width: 60,
                    align: 'center'
                },
                {
                    field: 'vps_type',
                    title: '<b>Тип заявено<br/>ВПС</b>',
                    sortable: true,
                    width: 40,
                    align: 'center'
                },{
                    field: 'area',
                    title: '<b>Обща площ <br/>(дка)</b>',
                    sortable: true,
                    width: 40,
                    align: 'center'
                },{
                    field: 'vps_inside_area',
                    title: '<b>Площ в  <br/> ВПС(дка)</b>',
                    sortable: true,
                    width: 40,
                    align: 'center'
                },
                {
                    field: 'edited',
                    title: '<b>За <br/>редактиране</b>',
                    sortable: false,
                    width: 50,
                    align: 'center',
                    formatter: function (value, row, index) {
	                	if (typeof value !== 'undefined') {
	                    	if (value == true)
	                        {
	                        	return '<input id="for-isak-main-vps-checkbox-' + index + '" data-row-index="' + index + '" type="checkbox" checked/>';
	                        } else {
	                            return '<input id="for-isak-main-vps-checkbox-' + index + '" data-row-index="' + index + '" type="checkbox" />';
	                        }
	                    } else {
	                        return '';
	                    }
	                }
                }, {
                    field: 'comment',
                    title: '<b>Коментар</b>',
                    sortable: false,
                    width: 40,
                    align: 'center',
                    editor: {
                        type: 'text'
                    }
                }
            ]
        ],
        toolbar: '#for-isak-vps-main-tables-toolbar',
        onSelect: function (rowIndex, rowData) {
            checkedElement = rowData;
            displayFeatureSelection(rowData.st_astext);
        },
        onBeforeLoad: function () {
            table.datagrid('clearChecked');
        },
        onClickCell: function (index,field,value){
	        if(field == 'comment'){
	            if(jQuery('#for-isak-main-vps-checkbox-' + index).is(':checked')){
	                table.datagrid('beginEdit', index);
	            }
	        }
	    },
	    loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
    	loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

     jQuery('#win-for-isak-vps-main').off().on('change', 'input[type=checkbox]', function (e) {

	        var rowIndex = jQuery(e.currentTarget).data('rowIndex');

	        if (typeof rowIndex !== 'undefined') {

	            if (jQuery(e.currentTarget).is(':checked')) {

	                table.datagrid('updateRow', {
	                    index: rowIndex,
	                    row: {
	                        edited: true
	                    }
	                });

	                table.datagrid('beginEdit', rowIndex);
	                var editorComment = table.datagrid('getEditor', {index: rowIndex, field: 'comment'});
	                jQuery(editorComment.target).focus();

	            } else {

	                table.datagrid('updateRow', {
	                    index: rowIndex,
	                    row: {
	                        edited: false,
	                        comment: ''
	                    }
	                });
	                table.datagrid('endEdit', rowIndex);
	            }
	        }
	    });

    jQuery('#vps-types-combobox > input').combobox({
        url: 'index.php?common-rpc=vps-schema-types-combobox',
        rpcParams:[false],
        valueField: 'id',
        textField: 'name',
        filter: function (q, row) {
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if (text.indexOf(find) != -1 || value.indexOf(find) != -1)
            {
                return true;
            }
        },
        onLoadSuccess: function (data)
        {
        	jQuery('#vps-type-filter-combobox').combobox({
        		data:jQuery('#vps-types-combobox > input').combobox('getData'),
        		valueField: 'id',
                textField: 'name',
                filter: function (q, row) {
                    var opts = jQuery(this).combobox('options');
                    var text = row[opts.textField].toLowerCase();
                    var value = row[opts.valueField];
                    var find = q.toLowerCase();
                    if (text.indexOf(find) != -1 || value.indexOf(find) != -1)
                    {
                        return true;
                    }
                }
            });
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
    	loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#vps-filter-cropcode').combobox({
    	url: 'index.php?common-rpc=culture-combobox',
    	rpcParams: [{
    		'year':6,
    	}],
    	valueField: 'id',
    	textField: 'name',
    	filter: function (q, row) {
    		var opts = jQuery(this).combobox('options');
    		var text = row[opts.textField].toLowerCase();
    		var value = row[opts.valueField];
    		var find = q.toLowerCase();
    		if (text.indexOf(find) != -1 || value.indexOf(find) != -1)
    		{
    			return true;
    		}
    	},
    	loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
    	loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#vps-filter-ekatte').combobox({
		url: 'index.php?common-rpc=ekate-combobox',
		rpcParams: [{}],
		valueField: 'ekate',
		textField: 'text',
		filter: function(q, row){
			var opts = jQuery(this).combobox('options');
			var text = row[opts.textField].toLowerCase();
			var value = row[opts.valueField];
			var find = q.toLowerCase();
			if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
			{
				return true;
			}
		},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function vpsReportFilter() {
    var table = jQuery('#for-isak-diff-vps-main-tables');

    table.datagrid({
        queryParams: {
            prc_name: jQuery('#vps-filter-prc-name').val(),
            cropcode: jQuery('#vps-filter-cropcode').combobox('getValue'),
            ekatte: jQuery('#vps-filter-ekatte').combobox('getValue'),
            vps_type: jQuery('#vps-type-filter-combobox').combobox('getValue')
        }
    });
}

function clearVpsReportFilter() {

    var table = jQuery('#for-isak-diff-vps-main-tables');

    jQuery('#vps-filter-prc-name').val('');
    jQuery('#vps-filter-cropcode').combobox('reset');
    jQuery('#vps-filter-ekatte').combobox('reset');
    jQuery('#vps-type-filter-combobox').combobox('reset');

    table.datagrid({
        queryParams: {}
    });
}
var vps_layer_name = '';
function vpsDetailedWindow() {
	var vps_layer_type = jQuery('#vps-types-combobox > input').combobox('getValue');
	var vpsWin = jQuery('#win-for-isak-detailed-vps-info');
    var vpsInfo = vpsWin.find('.vps-info');
    var obj = {};
    vpsInfo.children().hide();

    if (vps_layer_type == SCHEMA_VPS_PASISHTA) {
        vpsWin.window('setTitle','Справка по ВПС - Пасища');
        selectLayerByName('layer_vps_merg');
        vps_layer_name = 'layer_vps_merg';
        selected_vps = SCHEMA_VPS_PASISHTA;
        vpsInfo.find('.vps-pasishta-info').show();

        obj.layer_name = _selectedLayerData.attributes.layer_name;
        obj.vps_layer_name = vps_layer_name;
    } else if(vps_layer_type == SCHEMA_VPS_GASKI_CHERVENOGUSHI) {
     	vpsWin.window('setTitle','Справка по ВПС - Червеногуши гъски');
     	selectLayerByName('layer_vps_gaski_chervenogushi');
     	vps_layer_name = 'layer_vps_gaski_chervenogushi';
     	selected_vps = SCHEMA_VPS_GASKI_CHERVENOGUSHI;
     	vpsInfo.find('.vps-gaski-chervenogushi-info').show();

        obj.layer_name = _selectedLayerData.attributes.layer_name;
        obj.vps_layer_name = vps_layer_name;
    } else if(vps_layer_type == SCHEMA_VPS_GASKI_ZIMNI) {
        vpsWin.window('setTitle','Справка по ВПС - Зимуващи гъски');
     	selectLayerByName('layer_vps_gaski_zimni');
     	vps_layer_name = 'layer_vps_gaski_zimni';
     	selected_vps = SCHEMA_VPS_GASKI_ZIMNI;
     	vpsInfo.find('.vps-gaski-zimni-info').show();

        obj.layer_name = _selectedLayerData.attributes.layer_name;
        obj.vps_layer_name = vps_layer_name;
    } else if(vps_layer_type == SCHEMA_VPS_LIVADEN_BLATAR) {
        vpsWin.window('setTitle','Справка по ВПС - Ливаден блатар');
     	selectLayerByName('layer_vps_livaden_blatar');
     	vps_layer_name = 'layer_vps_livaden_blatar';
     	selected_vps = SCHEMA_VPS_LIVADEN_BLATAR;
     	vpsInfo.find('.vps-livaden-blatar-info').show();

        obj.layer_name = _selectedLayerData.attributes.layer_name;
        obj.vps_layer_name = vps_layer_name;
    } else if(vps_layer_type == SCHEMA_VPS_ORLI_LESHOYADI) {
        vpsWin.window('setTitle','Справка по ВПС - Орли и Лешояди');
     	selectLayerByName('layer_vps_orli_leshoyadi');
     	vps_layer_name = 'layer_vps_orli_leshoyadi';
     	selected_vps = SCHEMA_VPS_ORLI_LESHOYADI;
     	vpsInfo.find('.vps-orli-leshoyadi-info').show();

        obj.layer_name = _selectedLayerData.attributes.layer_name;
        obj.vps_layer_name = vps_layer_name;
    } else {
        return false;
    }

    TF.Rpc.SubsidiesWizard.ForIsakDiffVPSDetailedGrid.initForIsakDiffVPS(obj.layer_name, obj.vps_layer_name)
	.done(function (data) {
		loadVPSMapLayer();
	});

	 vpsWin.window('open');

	 var table = jQuery('#for-isak-diff-vps-detailed-tables');
	 layer_name = _selectedLayerData.attributes.layer_name;
	    table.datagrid({
	        nowrap: true,
	        autoRowHeight: true,
	        striped: true,
	        singleSelect: false,
	        fit: true,
	        fitColumns: true,
	        showFooter: true,
	        rownumbers: true,
	        url: 'index.php?subsidieswizard-rpc=for-isak-diff-vps-detailed-grid',
	        rpcParams:[{
	        	'layer_name': layer_name,
	        	'vps_layer_name': vps_layer_name,
	        }],
	        border: true,
	        pagination: true,
	        sortName: 'gid',
	        sortOrder: 'desc',
	        frozenColumns: [[
	                {
	                    field: 'ck',
	                    checkbox: true
	                }
	            ]],
	        columns: [
	            [
	                {
	                    field: 'prc_name',
	                    title: '<b>Име</b>',
	                    sortable: true,
	                    width: 60
	                },
	                {
	                    field: 'cropname',
	                    title: '<b>Култура</b>',
	                    sortable: true,
	                    width: 60,
	                    align: 'center',
	                    styler: function (value, row, index) {
	                    	if(row.compatiable_cropcode != undefined && !row.compatiable_cropcode){
	                    		return 'background-color: #99CCCC;';
	                        }
	                    }
	                },
	                {
	                    field: 'vps_type',
	                    title: '<b>Заявен <br/>във ВПС</b>',
	                    sortable: true,
	                    width: 40,
	                    align: 'center'
	                },{
	                    field: 'area',
	                    title: '<b>Площ на <br/>парцела(ха)</b>',
	                    sortable: true,
	                    width: 40,
	                    align: 'center'
	                },{
	                    field: 'vps_inside_area',
	                    title: '<b>Площ на парцела в<br/> даденото ВПС (ха)</b>',
	                    sortable: false,
	                    width: 60,
	                    align: 'center'
	                },{
	                    field: 'vps_outside_area',
	                    title: '<b>Площ на парцела извън<br/> даденото ВПС(ха)</b>',
	                    sortable: false,
	                    width: 70,
	                    align: 'center',
	                    styler: function (value, row, index) {
	                        return 'color: red;';
	                    }
	                },{
	                    field: 'edited',
	                    title: '<b>За <br/>редактиране</b>',
	                    sortable: false,
	                    width: 50,
	                    align: 'center',
	                    formatter: function (value, row, index) {
	                        if (typeof value !== 'undefined') {
	                            if (value == true)
	                            {
	                                return '<input id="for-isak-vps-checkbox-' + index + '" data-row-index="' + index + '" type="checkbox" checked/>';
	                            } else {
	                                return '<input id="for-isak-vps-checkbox-' + index + '" data-row-index="' + index + '" type="checkbox" />';
	                            }
	                        } else {
	                            return '';
	                        }
	                    }
	                }, {
	                    field: 'comment',
	                    title: '<b>Коментар</b>',
	                    sortable: false,
	                    width: 40,
	                    align: 'center',
	                    editor: {
	                        type: 'text'
	                    }
	                }
	            ]
	        ],
	        toolbar: '#for-isak-vps-detailed-toolbar',
	        onSelect: function (rowIndex, rowData) {
	            checkedElement = rowData;
	            displayFeatureSelection(rowData.st_astext);
	        },
	        onBeforeLoad: function () {
	            table.datagrid('clearChecked');
	        },
	        onClickCell: function (index,field,value){
	            if(field == 'comment'){
	                if(jQuery('#for-isak-vps-checkbox-' + index).is(':checked')){
	                    table.datagrid('beginEdit', index);
	                }
	            }
	        },
	        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
	    	loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	    });

	    jQuery('#detailed-vps-filter-cropcode').combobox({
	    	url: 'index.php?common-rpc=culture-combobox',
	    	rpcParams: [{
	    		'year':6,
	    	}],
	    	valueField: 'id',
	    	textField: 'name',
	    	filter: function (q, row) {
	    		var opts = jQuery(this).combobox('options');
	    		var text = row[opts.textField].toLowerCase();
	    		var value = row[opts.valueField];
	    		var find = q.toLowerCase();
	    		if (text.indexOf(find) != -1 || value.indexOf(find) != -1)
	    		{
	    			return true;
	    		}
	    	},
	    	loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
	    	loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	    });

	    jQuery('#win-for-isak-detailed-vps-info').off().on('change', 'input[type=checkbox]', function (e) {

	        var rowIndex = jQuery(e.currentTarget).data('rowIndex');

	        if (typeof rowIndex !== 'undefined') {

	            if (jQuery(e.currentTarget).is(':checked')) {

	                table.datagrid('updateRow', {
	                    index: rowIndex,
	                    row: {
	                        edited: true
	                    }
	                });

	                table.datagrid('beginEdit', rowIndex);
	                var editorComment = table.datagrid('getEditor', {index: rowIndex, field: 'comment'});
	                jQuery(editorComment.target).focus();

	            } else {

	                table.datagrid('updateRow', {
	                    index: rowIndex,
	                    row: {
	                        edited: false,
	                        comment: ''
	                    }
	                });
	                table.datagrid('endEdit', rowIndex);
	            }
	        }
	    });
}

function loadVPSMapLayer(){
	var lname = 'for_isak_diff_'+vps_layer_name;
	loadMapLayer(lname);
}
function removeVPSMapLayer(){
	var lname = 'for_isak_diff_'+vps_layer_name;
	removeLayerByName(lname);
}
function requestVPS(fieldValue) {

    var table = jQuery('#for-isak-diff-vps-detailed-tables');
    var rows = table.datagrid('getSelections');
    var ok_rows = [];
    if (rows.length == 0) {
        jQuery.messager.alert('Грешка', 'Моля изберете парцелите от таблицата, които искате да заявите за ВПС!', 'warning');
        return false;
    }
    var j = 0;
    for (var i = 0; i < rows.length; i++) {
        rows[i].layer_name = _selectedLayerData.attributes.layer_name;
        rows[i].field = "vps_type";
        rows[i].field_value = (fieldValue == 'false') ? 'null' : selected_vps;
        if(fieldValue == 'false') {
        	rows[i].vps_inside_area = 'null';
        }
    	if(fieldValue == 'true' && rows[i].vps_outside_area != 0 && selected_vps != SCHEMA_VPS_PASISHTA) {
    		 jQuery.messager.alert('Внимание', 'Парцелите, обект на заявяване, които не са с цялата си площ в границите на съответния слой ВПС, няма да бъдат заявени по него', 'warning');
    		 continue;
    	}
    	if(fieldValue == 'true' && selected_vps == SCHEMA_VPS_PASISHTA && (rows[i].requested_in_natura2000 || rows[i].inside_natura_2000)) {
	   		 jQuery.messager.alert('Внимание', 'Парцелите, обект на заявяване, които вече са заявени по НАТУРА2000 или попадат в обхвата й с повече от 50 кв. м., няма да бъдат заявени по съответния слой ВПС', 'warning');
	   		 continue;
    	}
    	if(fieldValue == 'true' && !rows[i].compatiable_cropcode) {
	   		 jQuery.messager.alert('Внимание', "Като заявени ще бъдат отбелязани само парцелите, отговарящи на изискванията на съответното ВПС!", 'warning');
	   		 continue;
    	}
    	if(fieldValue == 'true' && rows[i].vps_type != "Няма") {
    		 jQuery.messager.alert('Внимание', "Парцелите, заявени по друго ВПС, които са обект на заявяване чрез настоящата проверка ще бъдат заявени спрямо последния ви избор!", 'warning');
    	}
    	ok_rows[j] = rows[i];
    	j++;
    }

    TF.Rpc.SubsidiesWizard.ForIsakPZPReportGrid.saveForRequest(ok_rows)
	.done(function (data) {
	    table.datagrid('reload');
	}).fail(function(error) {
        jQuery.messager.alert('Грешка', error.getMessage());
    });
}

function saveForIsakVPSData() {

    var table = jQuery('#for-isak-diff-vps-detailed-tables');

    var rows = table.datagrid('getRows');

    for (var i = 0; i < rows.length; i++) {
        table.datagrid('endEdit', i);

        rows[i].layer_name = _selectedLayerData.attributes.layer_name;
    }

    TF.Rpc.SubsidiesWizard.ForIsakPZPReportGrid.saveForIsakData(rows)
	.done(function (data) {
	});
}

function saveForIsakMainVPSData() {

    var table = jQuery('#for-isak-diff-vps-main-tables');

    var rows = table.datagrid('getRows');

    for (var i = 0; i < rows.length; i++) {
        table.datagrid('endEdit', i);

        rows[i].layer_name = _selectedLayerData.attributes.layer_name;
    }

    TF.Rpc.SubsidiesWizard.ForIsakPZPReportGrid.saveForIsakData(rows)
	.done(function (data) {
	});
}
function detailedVPSReportFilter() {
    var table = jQuery('#for-isak-diff-vps-detailed-tables');

    table.datagrid({
    	  rpcParams: [{
          	'layer_name':_selectedLayerData.attributes.layer_name,
          	'vps_layer_name': vps_layer_name,
          	'filterObj':{
          		prc_name: jQuery('#detailed-vps-filter-prc-name').val(),
                cropname: jQuery('#detailed-vps-filter-cropcode').combobox('getValue'),
                edited: jQuery('#detailed-vps-filter-edited').combobox('getValue'),
                comment: jQuery('#detailed-vps-filter-comment').val(),
  		    }
    	  }]
    });
}

function clearDetailedVPSReportFilter() {

    var table = jQuery('#for-isak-diff-vps-detailed-tables');

    jQuery('#detailed-vps-filter-prc-name').val('');
    jQuery('#detailed-vps-filter-cropcode').combobox('reset');
    jQuery('#detailed-vps-filter-edited').combobox('reset');
    jQuery('#detailed-vps-filter-comment').val('');

    table.datagrid({
    	rpcParams:[{
    		layer_name: _selectedLayerData.attributes.layer_name,
            vps_layer_name: vps_layer_name,
            'filterObj':{}
    	}]
    });
}
