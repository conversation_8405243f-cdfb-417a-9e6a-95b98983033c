
function initMapTools()
{
	//init map tools 
	jQuery('#tool-set-scale').bind('click', function()
	{
		var scale = jQuery("#scale-denominator input").val();

		map.zoomToScale(scale);
	});
	
	jQuery('#tool-panzoom').bind('click', function()
	{
		var options = jQuery('#tool-panzoom').linkbutton('options');
		if (options.disabled)
		{
			return false;
		}

		//stop choose layer object event if active
		map.events.unregister('click', map, propertyWindowFunction);
		chooseControl('navigation');
		//unselect all buttons
		unselectAll();
		//select navigation button
		jQuery('#tool-panzoom').linkbutton('select');
	});

	jQuery('#tool-zoomin').bind('click', function()
	{
		unselectAll();
		map.events.unregister('click', map, propertyWindowFunction);

		chooseControl('zoomin');
		jQuery('#tool-zoomin').linkbutton('select');
	});

	jQuery('#tool-zoomout').bind('click', function()
	{
		unselectAll();
		map.events.unregister('click', map, propertyWindowFunction);

		chooseControl('zoomout');
		jQuery('#tool-zoomout').linkbutton('select');
	});
	jQuery('#tool-measure-line').bind('click', function()
	{
		unselectAll();
		map.events.unregister('click', map, propertyWindowFunction);

		chooseControl('linemeasure');
		jQuery('#tool-measure-line').linkbutton('select');
	});

	jQuery('#tool-measure-polygon').bind('click', function()
	{
		unselectAll();
		map.events.unregister('click', map, propertyWindowFunction);

		chooseControl('polygonmeasure');
		jQuery('#tool-measure-polygon').linkbutton('select');
	});

	jQuery('#tool-clear-selection').bind('click', function()
	{
		var options = jQuery('#tool-clear-selection').linkbutton('options');
		if (options.disabled)
		{
			return false;
		}
		vectors.removeAllFeatures();
        unselectAll();
        deactivateAllControls();
        chooseControl('navigation');
        var activeLayer = jQuery('#all-layers-tree').tree('getSelected');
	});
	
	//zoom to active layer extent
	jQuery('#tool-zoom-layer').bind('click', function() {
		var selected = _selectedLayerData;//jQuery('#all-layers-tree').tree('getSelected');
		if (!selected)
		{
			jQuery.messager.alert('Грешка', 'Не е избран "за ИСАК" слой!');
			return false;
		}

		map.zoomToExtent(new OpenLayers.Bounds.fromString(selected['attributes'].extent).transform(
				new OpenLayers.Projection("EPSG:32635"),
				map.getProjectionObject())
				);

		return false;
	});
}