function initFORISAKGrid(layer_id)
{
	initSchemasDropDown();
	var checkedElement;
	jQuery('#plots-tables').datagrid({
		url: 'index.php?subsidieswizard-rpc=for-isak-subsidies-datagrid',
		rpcParams:[{
			'layer_id':layer_id,
			'layer_type':9
			}],
		singleSelect: true,
        checkOnSelect: false,
        selectOnCheck: false,
		iconCls: 'icon-plot',
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		pageSize: 20,
		fit: true,
		showFooter: true,
		sortName: 'prc_uin',
		sortOrder: 'asc',
		idField: 'gid',
		pagination: true,
		rownumbers: true,
		width: '100%',
		border: false,
		frozenColumns: [[
				{
					field: 'ck',
					checkbox: true
				}
			]],
		columns: [[
				{
					field: 'prc_name',
					title: '<b>Име</b>',
					sortable: true,
					width: 150
				},{
					field: 'ekatte',
					title: '<b>ЕКАТТЕ</b>',
					sortable: true,
					width: 60
				},{
					field: 'cropname',
					title: '<b>Култура</b>',
					sortable: true,
					width: 150,
                    styler: function (value, row, index) {
                    	if(row.is_active_culture == false){
                    		return 'background-color:#F69232;';
                        }

                    	if(value == null && row.prc_name != "<b>Общо за стр.:</b>"){
                    		return 'background-color: #99CCCC;';
                        }
                    }
				},{
					field: 'schema',
					title: '<b>Схеми/мерки</b>',
					sortable: true,
					width: 140
				},{
					field: 'area',
					title: '<b>Площ(ха)</b>',
					sortable: true,
					width: 90,
                    styler: function (value, row, index) {
                    	if(value < 0.1){
                    		return 'color: red;';
                        }
                    }
				},{
					field: 'edited',
					title: '<b>За <br/> редактиране</b>',
					sortable: true,
					width: 85
				},{
					field: 'common_cultures',
					title: '<b>Междинна<br/>култура</b>',
					sortable: true,
					width: 70
				},{
					field: 'comment',
					title: '<b>Коментар</b>',
					sortable: true,
					width: 125
				}
			]],
		onLoadSuccess: function() {
			jQuery('#plots-tables').datagrid('uncheckAll');
		},
		onUnselect: function(rowIndex, rowData) {

		},
		onSelect: function(rowIndex, rowData) {
			checkedElement = rowData;
			displayFeatureSelection(rowData.st_astext);
		},
		onBeforeLoad: function() {
			jQuery('#plots-tables').datagrid('clearChecked');

			jQuery('#for-isak-filter-zemlishte > input').combobox({
				url: 'index.php?common-rpc=for-isak-ekate-combobox',
				rpcParams: [{
					'layer_id': layer_id,
					'layer_type': 9,
					'without_farm': true
				}],
				valueField: 'ekate',
				textField: 'text',
				onSelect: function(record)
				{
				},
				loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
				loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
			});

		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
	});

	jQuery('#attr-tables-copy-submenu').on('click', '.js-copy-layer-filtered-btn', copyFilteredLayer);
	jQuery('#attr-tables-copy-submenu').on('click', '.js-copy-layer-btn', copyCheckedLayer);
	jQuery('#attr-tables-copy-submenu').on('click', '.js-copy-layer-btn-zplots', loadZPSelection);
	jQuery('#attr-tables-copy-submenu').on('click', '.js-copy-layer-btn-filtered-zplots', loadZPSelection);

	jQuery('#btn-save-zplots-copy').bind('click', function()
	{
		if (jQuery('#select-zplots-layer').combobox('isValid'))
		{
			var to_layer_id = jQuery('#select-zplots-layer').combobox('getValue');
			var to_data = jQuery('#select-zplots-layer').combobox('getData');

			if (typeof _copyToolToZP !== 'undefined') {

				copyToForIsak(to_layer_id, to_data);

				jQuery('#win-zplots-copy').window('close');
			}else {

				copyCheckedLayerCustom(to_layer_id, to_data, 1, _copyFiltered);

				jQuery('#win-zplots-copy').window('close');
			}

		}else {
			jQuery.messager.alert('Грешка', 'Моля изберете слой, в който да копирате данните!','warning');
			return false;
		}

	});
}

function initSchemasDropDown() {

	jQuery('#select-schema').combobox({
		url: 'index.php?common-rpc=schema-combobox',
		rpcParams: [{
			selected:true,
		}],
		editable: false,
		valueField: 'id',
		textField: 'name',
		filter: function(q, row){
			var opts = jQuery(this).combobox('options');
			var text = row[opts.textField].toLowerCase();
			var value = row[opts.valueField];
			var find = q.toLowerCase();
			if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
			{
				return true;
			}
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function filterForIsak() {
	var obj = new Object();

	obj.prc_name = jQuery('#for-isak-filter-prc-name > input').val();
	obj.isak = jQuery('#for-isak-filter-isak > input').val();
	obj.culture = jQuery('#for-isak-filter-culture > input').combobox('getValue');
	obj.ekate = jQuery('#for-isak-filter-zemlishte > input').combobox('getValue');
	obj.schema = jQuery('#for-isak-filter-schema > input').combobox('getValue');
	obj.is_edited = jQuery('#for-isak-filter-is-edited > input').combobox('getValue');
	obj.comment = jQuery('#for-isak-filter-comment > input').val();

    jQuery('#plots-tables').datagrid({
        url: 'index.php?subsidieswizard-rpc=for-isak-subsidies-datagrid',
        rpcParams:[{
        	'layer_id':_selectedLayerData.id,
        	'layer_type':9,
        	'filterObj':obj
        }]
    });

    jQuery('#plots-tables').datagrid('uncheckAll');
    jQuery('#plots-tables').datagrid('unselectAll');

    jQuery('#win-filter-for-isak').window('close');
}
function clearFilterForIsak() {
	jQuery('#for-isak-filter-prc-name > input').val('');
	jQuery('#for-isak-filter-isak > input').val('');
    jQuery('#for-isak-filter-culture > input').combobox('clear');
    jQuery('#for-isak-filter-schema > input').combobox('clear');
    jQuery('#for-isak-filter-zemlishte > input').combobox('clear');
    jQuery('#for-isak-filter-is-edited > input').combobox('clear');
    jQuery('#for-isak-filter-comment > input').val('');

	 jQuery('#plots-tables').datagrid({
	        url: 'index.php?subsidieswizard-rpc=for-isak-subsidies-datagrid',
	        rpcParams:[{
	        	'layer_id':_selectedLayerData.id,
	        	'layer_type':9,
	        	data: ''
	        }]
	    });
}
function exportDocument(document_type)
{
	var obj = new Object();
	var plotsGridData = jQuery('#plots-tables').datagrid('getData');

	if(!plotsGridData['rows'].length)
	{
		jQuery.messager.alert('Грешка', 'Не са намерени записи за експорт!');
		return false;
	}

	obj.use_filter = 1; //use 0 for false and 1 for true
	obj.layer_name = _selectedLayerData.attributes.layer_name;
	obj.layer_type = 9;
	obj.layer_id = _selectedLayerData.id;

	obj.prc_name = jQuery('#for-isak-filter-prc-name > input').val();
	obj.isak = jQuery('#for-isak-filter-isak > input').val();
	obj.culture = jQuery('#for-isak-filter-culture > input').combobox('getValue');
	obj.ekate = jQuery('#for-isak-filter-zemlishte > input').combobox('getValue');
	obj.schema = jQuery('#for-isak-filter-schema > input').combobox('getValue');
	obj.is_edited = jQuery('#for-isak-filter-is-edited > input').combobox('getValue');
	obj.comment = jQuery('#for-isak-filter-comment > input').val();

	var exportParams = {
		'layer_id':_selectedLayerData.id,
    	'layer_type':9,
    	'export2document':true,
    	'document_type':document_type,
    	'filterObj':{
    		'prc_name':obj.prc_name,
    		//obj.isak,
    		'culture':obj.culture,
    		'ekate':obj.ekate,
    		'schema':obj.schema,
    		'is_edited':obj.is_edited,
    		'comment':obj.comment
    	}
	};

	var plotsGridData = jQuery('#plots-tables').datagrid('getData');
	var plotsGridOptions = jQuery('#plots-tables').datagrid('options');

	if(document_type == 'xls') {

		TF.Rpc.SubsidiesWizard.ForIsakSubsidiesGrid.exportGridToXLS(exportParams, plotsGridData.total, plotsGridOptions.sortName, plotsGridOptions.sortOrder)
		.done(function (data) {
			jQuery('#win-download').window('open');
	        var path = data.exl_report_file;
	        _pathFile = path;
	        jQuery('#btn-download-file').attr("href", path);
		});
	}else{

		TF.Rpc.SubsidiesWizard.ForIsakSubsidiesGrid.exportGridToPDF(exportParams, plotsGridData.total, plotsGridOptions.sortName, plotsGridOptions.sortOrder)
		.done(function (data) {
			jQuery('#win-download').window('open');
	        var path = data.pdf_report_file;
	        _pathFile = path;
	        jQuery('#btn-download-file').attr("href", path);
		});
	}




}

/**
 * Exports layer to .shp
 * @param {String} exportType
 * @param {Boolean} useFilter
 * @param {Boolean} isUnited
 * @param {Boolean} isOld
 * @param {String} outputDevice
 * @returns {undefined}
 */
function exportLayerForIsak(exportType, useFilter, isUnited, isOld, outputDevice, exportStructureType = null)
{
	var obj = new Object();
    obj.export_type = exportType;
    obj.united = isUnited;
    obj.export_old = isOld;
    obj.output_device = outputDevice;
	obj.export_structure_type = exportStructureType;
	var plotsGridData = jQuery('#plots-tables').datagrid('getData');

	if(!plotsGridData['rows'].length)
	{
		jQuery.messager.alert('Грешка', 'Не са намерени записи за експорт');
		return;
	}

	obj.use_filter = (useFilter) ? 1 : 0;
	obj.layer_name = _selectedLayerData.attributes.layer_name;
	obj.layer_type = 9;
	obj.layer_id = _selectedLayerData.id;

    TF.Rpc.Map
        .MapExportLayer
        .export(obj)
        .done(function (dataObj) {
            jQuery('#btn-download-file').attr('href', dataObj);
            jQuery('#win-download').window('open');
        })
        .fail(function (errorObj) {
            jQuery.messager.alert('Грешка', errorObj.getMessage());
        });
}

function copyFilteredLayer()
{
    var obj = new Object();
	var plotsGridData = jQuery('#plots-tables').datagrid('getData');

	if(!plotsGridData['rows'].length)
	{
		jQuery.messager.alert('Грешка', 'Не са намерени записи за копиране!');
		return false;
	}

	obj.use_filter = 1; //use 0 for false and 1 for true
	obj.layer_name = _selectedLayerData.attributes.layer_name;
	obj.layer_type = 9;
	obj.layer_id = _selectedLayerData.id;

	var systemNode = jQuery('#all-layers-tree').tree('find', 0);
    var systemLayers = jQuery('#all-layers-tree').tree('getChildren', systemNode.target);
    var tmpLayer = jQuery.grep(systemLayers, function(e){
        if(e.attributes.layer_type == LAYER_TEMP_DATA)
        {
            return true;
        }
    });

    obj.layertmp_id = tmpLayer[0].id;
	obj.layertmp_name = 'layer_gps';
	obj.layertmp_type = 2;

	TF.Rpc.Map.MapTools.copyLayerItems(obj)
	.done(function (data) {
		reloadLayer(data);
		onCopyComplete(data);
	})
	.fail(function (data) {

	});
}

function copyCheckedLayer()
{
    var obj = new Object();
	var checkedPlotsData = jQuery('#plots-tables').datagrid('getChecked');

	if(!checkedPlotsData.length)
	{
		jQuery.messager.alert('Грешка', 'Не са избрани записи за копиране!');
		return false;
	}

    var idField = 'gid';

    var plotIds = [];
    jQuery.grep(checkedPlotsData, function(e){
        plotIds.push(e[idField]);
    });

    obj.plot_ids = plotIds;
	obj.use_filter = 0; //use 0 for false and 1 for true
	obj.layer_name = _selectedLayerData.attributes.layer_name;
	obj.layer_type = 9;
	obj.layer_id = _selectedLayerData.id;

	var systemNode = jQuery('#all-layers-tree').tree('find', 0);
    var systemLayers = jQuery('#all-layers-tree').tree('getChildren', systemNode.target);
    var tmpLayer = jQuery.grep(systemLayers, function(e){
        if(e.attributes.layer_type == LAYER_TEMP_DATA)
        {
            return true;
        }
    });

    obj.layertmp_id = tmpLayer[0].id;
	obj.layertmp_name = 'layer_gps';
	obj.layertmp_type = 2;

	TF.Rpc.Map.MapTools.copyLayerItems(obj)
	.done(function (data) {
		reloadLayer(data);
		onCopyComplete(data);
	})
	.fail(function (data) {

	});
}

function loadZPSelection(e) {

	if(jQuery(this).data('filtered'))
	{
		_copyFiltered = 1;
	}else{
		_copyFiltered = 0;
	}

	var checkedPlotsData = jQuery('#plots-tables').datagrid('getChecked');

	if(!checkedPlotsData.length && !_copyFiltered)
	{
		jQuery.messager.alert('Грешка', 'Не са избрани записи за копиране!');
		return false;
	}

	initZPLayerDropDown();
}

function initZPLayerDropDown() {

	//init for isak layer dropdown
	jQuery('#select-zplots-layer').combobox({
		url: 'index.php?common-rpc=layer-selection-combobox',
		valueField: 'layer_id',
		textField: 'layer_path',
		groupField: 'farming_name',
		rpcParams: [{
			layer_type: 1
		}],
		required: true,
		editable: false,
		filter: function(q, row) {
			var opts = jQuery(this).combobox('options');
			var text = row[opts.textField].toLowerCase();
			var value = row[opts.valueField];
			var find = q.toLowerCase();
			if (text.indexOf(find) != -1 || value.indexOf(find) != -1)
			{
				return true;
			}
		},
		onLoadSuccess: function(data)
		{
			jQuery('#win-zplots-copy').window('open');
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function onCopyToZPButtonClick() {

	if (vectors.features.length == 0) {
		jQuery.messager.alert('Грешка', 'Трябва да сте селектирали поне един или повече парцели, които искате да копирате в Слой: За ИСАК!','warning');
		return false;
	}

	_copyToolToZP = 1;

	initZPLayerDropDown();
}
function copyToZP(layer_id, data) {

	var obj = new Object();
	var layerType = 9;

	var plotIds = [];
	for (i = 0; i < vectors.features.length; i++)
	{
	plotIds.push(vectors.features[i].attributes.gid);
	}

	if (plotIds.length == 0) {
		jQuery.messager.alert('Грешка', 'Моля селектирайте поне един или повече парцели, които искате да копирате в Слой: За ИСАК!','warning');
		jQuery('#win-zplots-copy').window('close');
		return false;
	}

	obj.plot_ids = plotIds;
	obj.use_filter = 0;
	obj.layer_name = _selectedLayerData.attributes.layer_name;
	obj.layer_type = 9;
	obj.layer_id = _selectedLayerData.id;

	//"To" parameters
	obj.layertmp_id = layer_id;
	obj.layertmp_type = 1;
	obj.layertmp_data = data;

	TF.Rpc.Map.MapTools.copyLayerItems(obj)
	.done(function (data) {
		reloadLayer(data);
		onCopyComplete(data);
	})
	.fail(function (data) {

	});
}
function copyCheckedLayerCustom(layer_id, data, layer_type, use_filter)
{
	var obj = new Object();
	var checkedPlotsData = jQuery('#plots-tables').datagrid('getChecked');

	var idField = 'gid';

    var plotIds = [];
    jQuery.grep(checkedPlotsData, function(e){
        plotIds.push(e[idField]);
    });

    obj.plot_ids = plotIds;
	obj.use_filter = use_filter; //use 0 for false and 1 for true
	obj.layer_name = _selectedLayerData.attributes.layer_name;
	obj.layer_type = 9;
	obj.layer_id = _selectedLayerData.id;

	//"To" parameters
	obj.layertmp_id = layer_id;
	obj.layertmp_type = layer_type;
	obj.layertmp_data = data;

	TF.Rpc.Map.MapTools.copyLayerItems(obj)
	.done(function (data) {
		reloadLayer(data);
		onCopyComplete(data);
	})
	.fail(function (data) {

	});
}
