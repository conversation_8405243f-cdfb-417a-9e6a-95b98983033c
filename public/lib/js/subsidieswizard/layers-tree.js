function initLayersTree() {
    jQuery('#layers-tree').combotree({
        url: 'index.php?subsidieswizard-rpc=for-isak-layers-tree',
        animate: true,
        lines: true,
        width: 240,
        onBeforeSelect: function(node) {
			if ( node['children'] != undefined)
			{
				return false;
			}
        },
        onSelect: function(node) {

            jQuery('#win-for-isak-diff-allowable-final').window('close');
            jQuery('#win-layer-attr-info').window('close');

            _selectedLayerData = node;
            if(node.text.indexOf(node.attributes.farming_name)< 0 ){
            	node.text = node.attributes.farming_name + ' / '+node.text;
            }

            loadMapLayerForIsak(node.attributes.layer_name, node.attributes.extent);
        },
        onLoadSuccess: function() {
            var t = jQuery('#layers-tree').combotree('tree');
            var roots = t.tree('getRoots');
            var years = t.tree('getChildren', roots[0].target);

            t.tree('select', years[2].target);

            jQuery('#layers-tree').combotree('setText', years[2].text);

        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
    });
}
