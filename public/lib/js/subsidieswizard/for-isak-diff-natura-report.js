function initForIsakDiffNaturaGrid(layer_name)
{
    var table = jQuery('#for-isak-diff-natura-tables');

    table.datagrid({
        nowrap: false,
        autoRowHeight: true,
        striped: true,
        singleSelect: false,
        fit: true,
        fitColumns: false,
        showFooter: true,
        rownumbers: true,
        url: 'index.php?subsidieswizard-rpc=for-isak-diff-natura-grid',
        rpcParams:[{
        	'layer_name':layer_name
        }],
        border: true,
        pagination: true,
        pageSize: 50,
        sortName: 'gid',
        sortOrder: 'asc',
        frozenColumns: [[
                {
                    field: 'ck',
                    checkbox: true
                }
            ]],
        columns: [
            [
                {
                    field: 'prc_name',
                    title: '<b>Име</b>',
                    sortable: true,
                    width: 120
                }, {
                    field: 'cropname',
                    title: '<b>Култура</b>',
                    sortable: true,
                    width: 100,
                    align: 'center',
                    formatter: function (value, row, index) {
                        if (value == null) {
                            return '-';
                        }
                        else {
                            return value;
                        }
                    },
                    styler: function (value, row, index) {
	                	if(row.is_active_culture == false){
	                		return 'background-color:#F69232;';
	                    }
                    }
                }, {
                    field: 'sitecode',
                    title: '<b>Зона №</b>',
                    sortable: true,
                    width: 80,
                    align: 'center',
                    formatter: function (value, row, index) {
                        if (value == null) {
                            return '-';
                        }
                        else {
                            return value;
                        }
                    }
                }, {
                    field: 'name_bg',
                    title: '<b>Зона име</b>',
                    sortable: true,
                    width: 80,
                    align: 'center',
                    formatter: function (value, row, index) {
                        if (value == null) {
                            return '-';
                        }
                        else {
                            return value;
                        }
                    }
                }, {
                    field: 'area',
                    title: '<b>Площ <br/>(ха)</b>',
                    sortable: true,
                    width: 80,
                    align: 'center'
                }, {
                    field: 'inside_area',
                    title: '<b>Площ в <br/>"НАТУРА 2000"<br/>(ха)</b>',
                    sortable: true,
                    width: 100,
                    align: 'center'
                }, {
                    field: 'outside_area',
                    title: '<b>Площ извън<br/>"НАТУРА 2000"<br/>(ха)</b>',
                    sortable: true,
                    width: 100,
                    align: 'center',
                    styler: function (value, row, index) {
                        return 'color: red;';
                    }
                }, {
                    field: 'edited',
                    title: '<b>За <br/>редактиране</b>',
                    sortable: true,
                    width: 90,
                    align: 'center',
                    formatter: function (value, row, index) {
                        if (typeof value !== 'undefined') {
                            if (value == true)
                            {
                                return '<input class="for-edit" id="for-isak-diff-checkbox-' + index + '" data-row-index="' + index + '" type="checkbox" checked/>';
                            } else {
                                return '<input class="for-edit" id="for-isak-diff-checkbox-' + index + '" data-row-index="' + index + '" type="checkbox" />';
                            }

                        } else {
                            return '';
                        }
                    }
                }, {
                    field: 'comment',
                    title: '<b>Коментар</b>',
                    sortable: true,
                    width: 120,
                    align: 'center',
                    editor: {
                        type: 'text'
                    }
                }, {
                    field: 'natura_sitecode',
                    title: '<b>Заявен</b>',
                    sortable: false,
                    width: 80,
                    align: 'center',
                    formatter: function (value, row, index) {
                        if (typeof value !== 'undefined') {
                            if(value && value == row.sitecode) {
                                return 'Да';
                            }
                            else {
                                return 'Не';
                            }
                        } else {
                            return '';
                        }
                    }
                }, {
                    field: 'bans',
                    title: '<b>На подпомагане подлежи спазването на следните забрани</b>',
                    sortable: false,
                    width: 600,
                    align: 'center',
                    formatter: function (value, row, index) {
                        if (value == null) {
                            return '-';
                        }
                        else {
                            return value;
                        }
                    }
                }, {
                    field: 'order_dv',
                    title: '<b>Заповед за обявяване</b>',
                    sortable: false,
                    width: 150,
                    align: 'center',
                    formatter: function (value, row, index) {
                        if (value == null) {
                            return '-';
                        }
                        else {
                            return value;
                        }
                    }
                }
            ]
        ],
        toolbar: '#for-isak-diff-natura-tables-toolbar',
        onSelect: function (rowIndex, rowData) {
            checkedElement = rowData;
            displayFeatureSelection(rowData.st_astext);
        },
        onBeforeLoad: function () {
            table.datagrid('clearChecked');
        },
        onClickCell: function (index,field,value){
            if(field == 'comment'){
                if(jQuery('#for-isak-diff-checkbox-' + index).is(':checked')){
                    table.datagrid('beginEdit', index);
                }
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
    	loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    //set comment editable
    jQuery('#win-for-isak-diff-natura').off().on('change', 'input[type=checkbox].for-edit', function (e) {

        var rowIndex = jQuery(e.currentTarget).data('rowIndex');

        if (jQuery(e.currentTarget).is(':checked')) {

            table.datagrid('updateRow', {
                index: rowIndex,
                row: {
                    edited: true
                }
            });

            table.datagrid('beginEdit', rowIndex);
            var editorComment = table.datagrid('getEditor', {index: rowIndex, field: 'comment'});
            jQuery(editorComment.target).focus();

        } else {

            table.datagrid('updateRow', {
                index: rowIndex,
                row: {
                    edited: false,
                    comment: ''
                }
            });

            table.datagrid('endEdit', rowIndex);
        }

    });
}

function requestNatura() {

    var table = jQuery('#for-isak-diff-natura-tables');

    var rows = table.datagrid('getSelections');
    var data = {};
    data.natura = [];
    var tempData = {};
    var gidCounter = [];
    var notInNatura = [];
    var inManyNatura = [];

    if (rows.length == 0) {
        jQuery.messager.alert('Грешка', 'Моля изберете парцелите от таблицата, които искате да заявите за НАТУРА 2000!', 'warning');
        return false;
    }

    rows.forEach(function(obj) {

        if (obj.is_active_culture == false) {
            jQuery.messager.alert('Грешка', 'Като заявени ще бъдат отбелязани само парцелите, с култура отговаряща на номенклатурата за настоящата година на кандидатстване!', 'warning');
            return false;
        }

        if(obj.sitecode && obj.sitecode != '-') {
            if(typeof gidCounter[obj.gid] === 'undefined') {
                gidCounter[obj.gid] = 1;
            }
            else {
                gidCounter[obj.gid]++;
            }
            tempData[obj.gid] = obj;
        }
        else {
            notInNatura.push(obj.prc_name);
        }
    });

    gidCounter.forEach(function(value, key) {
        if(value == 1) {
            data.natura.push({
                gid: tempData[key].gid,
                sitecode: tempData[key].sitecode
            });
        }
        else {
            inManyNatura.push(tempData[key].prc_name);
        }
    });

    data.layer_name = _selectedLayerData.attributes.layer_name;

    TF.Rpc.SubsidiesWizard.ForIsakDiffNaturaGrid.saveNaturaSitecode(data)
    .done(function (data) {
    	 table.datagrid('reload');
    }).fail(function(error) {
        jQuery.messager.alert('Грешка', error.getMessage());
    });

    var errorMsg = '';

    if (notInNatura.length > 0) {
        errorMsg += '<p>Следните парцели: <b>' + notInNatura.join(', ') + '</b> не бяха заявени, тъй като не попадат в нито един от регионите.</p>';
    }

    if (inManyNatura.length > 0) {
        errorMsg += '<p>Следните парцели: <b>' + inManyNatura.join(', ') + '</b> не бяха заявени, тъй като един и същ парцел не може да бъде заявен и по двата региона.</p>';
    }

    if (errorMsg.length > 0) {
        jQuery.messager.alert('Грешка', errorMsg, 'warning');
    }
}

function offNatura() {
    var table = jQuery('#for-isak-diff-natura-tables');

    var rows = table.datagrid('getSelections');

    if (rows.length == 0) {
        jQuery.messager.alert('Грешка', 'Моля изберете парцелите от таблицата, които искате да премахнете от НР!', 'warning');
        return false;
    }
    var data = {};
    data.natura = [];
    data.layer_name = _selectedLayerData.attributes.layer_name;

    jQuery.map(rows, function(element) {
        if(element.natura_sitecode) {
            data.natura.push({
                gid: element.gid,
                sitecode: null
            });
        }
    });

    TF.Rpc.SubsidiesWizard.ForIsakDiffNaturaGrid.saveNaturaSitecode(data)
    .done(function (data) {
    	 table.datagrid('reload');
    }).fail(function(error) {
        jQuery.messager.alert('Грешка', error.getMessage());
    });
}

function initForIsakNaturaFilterPanel() {
    jQuery('#natura-filter-cropcode').combobox({
    	url: 'index.php?common-rpc=culture-combobox',
    	rpcParams: [{
    		'year':6,
    		'without_crops':true
    	}],
    	valueField: 'id',
    	textField: 'name',
    	loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
    	loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#natura-filter-sitecode').combobox({
        url: 'index.php?common-rpc=natura-2000-name-combobox',
        rpcParams: [{}],
        valueField: 'gid',
        textField: 'text',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function naturaReportFilter() {
    var table = jQuery('#for-isak-diff-natura-tables');

    table.datagrid({
        rpcParams:[{
        	'layer_name':_selectedLayerData.attributes.layer_name,
        	'filterObj':{
                prc_name: jQuery('#natura-filter-prc-name').val(),
                cropcode: jQuery('#natura-filter-cropcode').combobox('getValue'),
                comment: jQuery('#natura-filter-comment').val(),
                edited: jQuery('#natura-filter-edited').combobox('getValue'),
                sitecode_gid: jQuery('#natura-filter-sitecode').combobox('getValue'),
                natura: jQuery('#natura-filter-requested').combobox('getValue')
            }
        }]
    });
}

function clearNaturaReportFilter() {

    var table = jQuery('#for-isak-diff-natura-tables');

    jQuery('#natura-filter-prc-name').val('');
    jQuery('#natura-filter-cropcode').combobox('reset');
    jQuery('#natura-filter-comment').val('');
    jQuery('#natura-filter-edited').combobox('reset');
    jQuery('#natura-filter-sitecode').combobox('reset');
    jQuery('#natura-filter-requested').combobox('reset');

    table.datagrid({
        rpcParams:[{
        	'layer_name':_selectedLayerData.attributes.layer_name,
        	'filterObj':{}
        }]
    });
}
