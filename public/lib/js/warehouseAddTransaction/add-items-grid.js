define([
    "jquery",
    "easyui",
    "js/main/EasyUIRPCLoaders",
    "js/warehouse/items-grid",
    "js/warehouseAddTransaction/return-items-grid",
    "js/warehouse/utils",
    "js/warehouse/comboboxes",
    "TF/Rpc/Warehouse/Warehouses",
    "js/warehouse/constants",
    "js/warehouse/dynamic-warehouse-fields",
], function(
    jQuery,
    _,
    EasyUIRPCLoaders,
    ItemsGrid,
    ReturnItemsGrid,
    Utils,
    Comboboxes,
    Warehouses,
    CONSTANTS
    // DynamicFields
    ) {

    var addItemsGrid;
    var item;
    var dds;

    function init() {
        initItemsGrid();
        bindEventsListeners();
    }

    function bindEventsListeners() {
        jQuery('div#rightSlidePanelCloseBtn').on("click", function () {
            Utils.closeRightSlidePanel()
        });

        jQuery(document).on("click", ".selectItemBtn", function () {
            //reload items grid, because inserted item reference problem
            let itemsGrid = jQuery('#items-grid-rp');
            itemsGrid.datagrid('reload')

            var index = jQuery(this).data("row-index");
            item =  jQuery("#items-grid-rp").datagrid("getRows")[index];

            Utils.selectItem(item);
        });

        jQuery(document).on("click", "#edit-item-warehouse-fields", function () {
            item = addItemsGrid.datagrid("getSelected");
            fillItemWarehouseFields("#warehouseFieldsEdit", item);
            jQuery("#win-manage-warehouse-fields").window("close");
        });

        // jQuery("#available-warehouses-combobox").combobox({
        //     onChange:function (warehouseId) {
        //         DynamicFields.initWarehouseItems(warehouseId);
        //     }
        // });

        jQuery(document).on("click", "#add-item-btn", function () {
            let item = JSON.parse(sessionStorage.getItem("selectedItem"));
            if(item === undefined) {
                jQuery.messager.alert("Грешка","Няма избран артикул.");
            }
            var insertedQuantity = jQuery("#quantity").numberbox('getValue');
            if(jQuery("#transaction_type").val() === 'RETURN' && (Math.abs(item.quantity) < insertedQuantity)) {
                jQuery.messager.alert("Грешка","Не може да върнете количество, по-голямо от изписаното.");
                return false;
            }

            item.warehouseQuantity = item.quantity !== undefined ? item.quantity : null;
            item.quantity = insertedQuantity;
            item.note = jQuery("#note").textbox('getValue');
            item.warehouse = jQuery("#available-warehouses-combobox").combobox('getText');
            item.comboboxes = [];

            if(jQuery("#transaction_type").val() !== 'RETURN') {
                item.priceNoDDS = jQuery("#price_no_dds").numberbox('getValue');
                item.batch = jQuery("#batch").textbox('getValue');
                item.expiry_date = jQuery("#expiry_date").datebox('getValue');
                item.warehouseFields = [];
            }

            item.comboboxes.warehouse = {
                "selected": jQuery("#available-warehouses-combobox").combobox('getValue'),
                "data": jQuery("#available-warehouses-combobox").combobox('getData')
            };

            if(item.expiry_date && validateExpireDate(item.expiry_date)) {
                jQuery.messager.alert("Грешка","Срока на годност не може да е по-стар или еднакъв на текущия ден.");
                return false;
            }

            if(!item.warehouse || !item.quantity) {
                jQuery.messager.alert("Грешка","Моля попълнете всички задължителни полета.");
                return false;
            }

            fillItemWarehouseFields('#warehouseFields', item);

            Utils.insertItemInTransaction(item);

            Utils.reloadPrices(dds);

            closeAddItemModal()
        });

        jQuery(document).on("click", "#reject-item-btn", function () {
            closeAddItemModal()
        });
    }

    function fillItemWarehouseFields(wrapper, item) {
        jQuery(".warehouse-field").each(function (index, element) {
            if(element.key === CONSTANTS.WAREHOUSE_SYSTEM_TYPE_KEY) return;//Skip the system files

            var itemField = item.warehouseFields.filter(el => el.key === element.id); //Check is field exists and just update the value if yes or add field data to item if not exist.
            if(itemField.length > 0) {
                itemField[0].value = element.value;
            } else {
                item.warehouseFields.push({
                    key: element.id,
                    label: jQuery('label[for="'+element.id+'"]').text(),
                    value: element.value,
                });
            }
        });

        jQuery(".warehouseFieldsList .container-box").empty();
        jQuery(wrapper).hide();

        return item;
    }


    function initItemsGrid() {
        jQuery("#tr-document-dds").combobox({
            onChange: function () {
                var ddsSelected = jQuery("#tr-document-dds").combobox("getValue");
                var ddsRows = jQuery("#tr-document-dds").combobox("getData");
                for (var ddsRow of ddsRows) {
                    if(ddsRow.data.key === ddsSelected) dds = ddsRow.data.value;
                }
                Utils.reloadPrices(dds);
            }
        });

        jQuery('#rightSlidePanelContent').find('.easyui-panel').panel({
            closable: true,
            onClose: function () {
                Utils.closeRightSlidePanel();
                jQuery(this).panel('open');
            }
        });

        addItemsGrid = jQuery("#added-items-grid");

        var columns = [
            {
                field: "item_name",
                title: "<b>Име</b>",
                width: 240
            },
            {
                field: "measure_name",
                title: "<b>Мярка</b>",
                width: 120,
            },
            {
                field: "printName",
                title: "<b>Име за печат</b>",
                width: 240
            },
            {
                field: "warehouse",
                title: "<b>Склад</b>",
                width: 120,
                editor: jQuery("#transaction_type").val() === 'RETURN' ? false : {
                    type: 'combobox',
                    options: {
                        textField: 'name',
                        valueField: 'id',
                        required: true
                    }
                }
            },
            {
                field: "quantity",
                title: "<b>Количество</b>",
                width: 120,
                align:'right',
                editor: {type: 'text'}
            },
            {
                field: "code",
                title: "<b>Код</b>",
                width: 140
            },
            {
                field: "batch",
                title: "<b>Партида</b>",
                width: 140,
                editor: jQuery("#transaction_type").val() === 'RETURN' ? false : {type: 'text'}
            },
            {
                field: "priceNoDDS",
                title: "<b>Цена без ДДС</b>",
                width: 140,
                align:'right',
                editor: jQuery("#transaction_type").val() === 'RETURN' ? false : { type: 'text'}
            },
            {
                field: "priceWithDDS",
                title: "<b>Цена с ДДС</b>",
                width: 140,
                align:'right',
                formatter: function(value,row){
                    if(dds && row.priceNoDDS !== undefined) {
                        return (((dds * row.priceNoDDS) * 100) / 100).toFixed(5);
                    }
                }
            },
            {
                field: "priceSumNoDDS",
                title: "<b>Общо без ДДС</b>",
                width: 140,
                align:'right',
                formatter: function(value,row) {
                    if(row.priceNoDDS !== undefined) {
                        return (((row.priceNoDDS * row.quantity) * 100) / 100).toFixed(5);
                    }
                }
            },
            {
                field: "expiry_date",
                title: "<b>Срок на годност</b>",
                width: 140,
                editor: jQuery("#transaction_type").val() === 'RETURN' ? false : {
                    type: 'datebox',
                    options: {
                        onSelect: function (value) {
                            if(validateExpireDate(value)) {
                                var target = jQuery(this).parent();
                                var index = getRowIndex(target)
                                var row = addItemsGrid.datagrid('getData')['rows'][index];
                                var ed = addItemsGrid.datagrid('getEditor', {index: index, field: 'expiry_date'});
                                jQuery(ed.target).datebox('setValue', row.expiry_date);

                                jQuery.messager.alert("Грешка","Срока на годност не може да е по-стар или еднакъв на текущия ден.");
                                return false;
                            }
                        }
                    }
                },
            },
            {
                field: "note",
                title: "<b>Забележка</b>",
                width: 140,
                editor: {
                    type: 'text'
                }
            },
            // {
            //     field: "warehouseFields",
            //     title: "<b>Допълнителни полета</b>",
            //     width: 240,
            //     formatter: function(value, row, index) {
            //         if (typeof value !== 'undefined') {
            //             let innerHtml = "";
            //             value.forEach(prop => {
            //                 innerHtml += "<div>"+ prop.label + ": " + prop.value + "</div>";
            //             });
            //
            //             return innerHtml;
            //         }
            //     }
            // }
        ];

        var toolBar = [
            {
                id: "btn_add_event",
                text: "Добавяне",
                iconCls: "icon-add",
                handler: function() {
                    var farm_id = jQuery("#tr-company-id-in").val();
                    if (!farm_id) {

                        jQuery.messager.alert("Грешка","Моля изберете стопанство.");
                        return false;
                    }

                    Utils.openRightSlidePanel('selectItem');
                    jQuery('.datagrid_search_box').val(''); //Reset quick filter

                    if(jQuery("#transaction_type").val() === 'RETURN') {
                        ReturnItemsGrid.initReturnItemsGrid([
                                {
                                    field: "addBtn",
                                    title: "",
                                    width: 40,
                                    formatter: function(value,row,index){
                                        return '<a data-row-index="'+index+'" class="l-btn l-btn-small l-btn-plain selectItemBtn"><span class="l-btn-left l-btn-icon-left"><span class="l-btn-text"></span><span class="l-btn-icon icon-add"></span></span></a>';
                                    }
                                }
                            ],
                            '#items-grid-rp',
                            '#items-toolbar-rp');
                    } else {
                        ItemsGrid.initItemsGrid([
                                {
                                    field: "addBtn",
                                    title: "",
                                    width: 40,
                                    formatter: function(value,row,index){
                                        return '<a data-row-index="'+index+'" class="l-btn l-btn-small l-btn-plain selectItemBtn"><span class="l-btn-left l-btn-icon-left"><span class="l-btn-text"></span><span class="l-btn-icon icon-add"></span></span></a>';
                                    }
                                }
                            ],
                            '#items-grid-rp');
                    }
                }
            },
            {
                id: "btn_save_changes",
                text: "Запази промените",
                iconCls: "icon-save",
                handler: function() {
                    jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да запазите промените?', function (r) {
                            if (r)  {
                                addItemsGrid.datagrid("acceptChanges");
                                Utils.reloadPrices(dds)
                            }
                        });

                }
            },
            {
                id: "btn_reject_changes",
                text: "Откажи промените",
                iconCls: "icon-undo",
                handler: function() {
                    jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да откажете промените?', function (r) {
                        if (r)  {
                            addItemsGrid.datagrid("rejectChanges");
                            Utils.reloadPrices(dds);
                        }
                    });

                }
            },
            {
                id: "btn_remove_item",
                text: "Премахване",
                iconCls: "icon-delete",
                handler: function() {
                    var item = addItemsGrid.datagrid("getSelected");
                    if (!item) {
                        jQuery.messager.alert("Грешка","Моля изберете артикул за изтриване.");
                        return false;
                    }
                    
                    if (item.transaction_status == 0) {
                        jQuery.messager.alert("Грешка","Не можете да анулирате повторно операция!");
                        return false;
                    }
                    
                    var rowIndex = addItemsGrid.datagrid("getRowIndex", item);

                    jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да премахнете избрания артикул?', function (r) {
                        if (r)  addItemsGrid.datagrid("deleteRow", rowIndex);
                    });
                }
            }
            // ,{
            //     id: "btn_warehouse_fields",
            //     text: "Допълнителни полета",
            //     iconCls: "icon-edit",
            //     disabled:true,
            //     handler: function() {
            //         var item = addItemsGrid.datagrid("getSelected");
            //         if (!item) {
            //             jQuery.messager.alert("Грешка","Моля изберете артикул.");
            //             return false;
            //         }
            //
            //         var fieldTemplate = '<div style="padding: 5px;">\n' +
            //             '                <label for="{key}" class="" style="width: 100px; margin-right: 5px; display: inline-block">{label}</label>\n' +
            //             '                <input id="{key}" class="easyui-textbox warehouse-field" data-options="" style="width: 180px;">\n' +
            //             '            </div>';
            //         DynamicFields.drawWarehouseFields(item.warehouseFields, "#warehouseFieldsEdit", fieldTemplate);
            //
            //         jQuery("#win-manage-warehouse-fields").window("open");
            //     }
            // }
        ];

        addItemsGrid.datagrid({
            autoRowHeight: true,
            autoRowWidth: true,
            fit:true,
            showFooter: true,
            data: {
                rows: [],
                total: 0,
                footer: Utils.generateFooter(dds)
            },
            rpcMethod: "read",
            pagination: true,
            border: false,
            singleSelect: true,
            checkbox: true,
            rpcParams: [{}],
            columns: [columns],
            toolbar: toolBar,
            onSelect: function(rowIndex, rowData) {
                jQuery("#btn_warehouse_fields").linkbutton({disabled:(rowData.warehouseFields !== undefined && rowData.warehouseFields.length === 0)});

                jQuery(this).datagrid('beginEdit', rowIndex);

                var editorWarehouse = jQuery(this).datagrid('getEditor', {index:rowIndex, field:'warehouse'});

                if(editorWarehouse) {
                    jQuery(editorWarehouse.target).combobox('loadData', rowData.comboboxes.warehouse.data);
                    jQuery(editorWarehouse.target).combobox('select', rowData.comboboxes.warehouse.selected);
                }
            },
            onLoadSuccess: function() {},
            onEndEdit: function(index,row,changes) {
                if(jQuery("#transaction_type").val() === 'RETURN' && row.quantity > row.warehouseQuantity) {
                    jQuery.messager.alert("Грешка","Не може да върнете количество, по-голямо от изписаното.");
                    addItemsGrid.datagrid("rejectChanges");
                    return false;
                }
                var warehouse = row.comboboxes.warehouse.data.filter(function (m) { return parseInt(m.id) === parseInt(changes.warehouse)});
                if(warehouse.length > 0) {
                    row.comboboxes.warehouse.selected = warehouse[0].id;
                    row.warehouse = warehouse[0].name;
                }
            },
            rowStyler: function(index, row) {
                let style = [];
                if (row.transaction_status === 0) {
                    style.push('text-decoration: line-through');
                    style.push('color: #aaa');
                }
        
                return style.join(';');
            },
            onBeforeEdit: function (index,row) {
                // disable editing on deleted transaction
                if (row.transaction_status === 0) {
                    jQuery.messager.alert("Грешка","Не може да едитвате анулирана операция!");
                    return false;
                } else {
                    return true;
                }
            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }

    function closeAddItemModal() {
        jQuery("#quantity").textbox('clear');
        jQuery("#price_no_dds").textbox('clear');
        jQuery("#batch").textbox('clear');
        jQuery("#note").textbox('clear');
        jQuery("#expiry_date").datebox('clear');

        jQuery("#win-add-transactions").window("close");
    }

    function validateExpireDate(expireDate) {
        var expiryDateArr = expireDate.split(".")
        var expiryDateObj = new Date(parseInt(expiryDateArr[2]), parseInt(expiryDateArr[1]) -1, parseInt(expiryDateArr[0]));
        return expiryDateObj <= new Date();
    }

    // grid method
    function getRowIndex(target) {
        var tr = jQuery(target).closest('tr.datagrid-row');
        return parseInt(tr.attr('datagrid-row-index'));
    }

    return {
        init
    };
});
