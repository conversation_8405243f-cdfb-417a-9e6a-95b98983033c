define([
    "jquery",
    "easyui",
    "js/main/EasyUIRPCLoaders",
    "js/main/rpc_errors_handler",
    "TF/Rpc/Warehouse/Items",
    "js/warehouse/comboboxes",
    "js/warehouse/utils"
], function(jQ<PERSON>y, _, EasyUIRPCLoaders,RpcErrorHandler, Items, Comboboxes, Utils) {
    function init() {}

    var itemsGrid;
    var itemsGridToolBarId;

    function bindEventsListeners() {
        jQuery("#saveItemBtn").on("click", saveItem);

        jQuery(document).find(itemsGridToolBarId).on("keyup", ".datagrid_search_box", function () {
            var filters = {};

            var company = jQuery("#tr-company-id-in").val();
            var tfCompany = jQuery("#tr-company-warehouse-id-in").val();

            if(company.length > 0) filters.company = company;
            if(tfCompany.length > 0) filters.tfCompany = tfCompany;
            filters.document_tr_types = ["SUB_PLOT", "RETURN"];
            
            Utils.quickFilterDataGrid(itemsGrid, itemsGridToolBarId, 'name', filters);
        });
    }

    function initReturnItemsGrid(additionalColumns = [], gridId = '#items-grid-rp', toolbarId = '#items-toolbar-rp') {
        itemsGridToolBarId = toolbarId;
        bindEventsListeners();

        var columns = [
            {
                field: "item_name",
                title: "<b>Име</b>",
                width: 250
            },
            {
                field: "measure_name",
                title: "<b>Мярка</b>",
                align: 'center',
                width: 50
            },
            {
                field: "single_price_no_dds",
                title: "<b>Ед. Цена без ДДС</b>",
                align: 'right',
                width: 130
            },
            {
                field: "quantity",
                title: "<b>Количество</b>",
                align: 'right',
                width: 110,
                formatter: function ($val) {
                    return Math.abs($val).toFixed(3)
                }
            },
            {
                field: "batch",
                title: "<b>Партида</b>",
                align: 'center',
                width: 110
            }
        ];

        //Add additional columns
        if(additionalColumns.length > 0) {
            for (var column of additionalColumns) {
                columns.unshift(column);
            }
        }

        itemsGrid = jQuery(gridId);
        itemsGrid.datagrid({
            autoRowHeight: true,
            autoRowWidth: true,
            url: "index.php?warehouse-rpc=warehouse-transactions",
            rpcMethod: "getTransactionItemsForReturn",
            pagination: true,
            fit: true,
            border: false,
            singleSelect: true,
            rpcParams: [{
                criteries: {
                    company: jQuery("#tr-company-id-in").val(),
                    tfCompany: jQuery("#tr-company-warehouse-id-in").val(),
                    document_tr_types: ["SUB_PLOT", "RETURN"],
                }
            }],
            columns: [columns],
            toolbar: toolbarId,
            onSelect: function (rowIndex, rowData) {
            },
            onLoadSuccess: function () {
            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }

    function saveItem() {
        var item = {};
        var endpoint;

        item.name = jQuery("#item-name").textbox("getValue");
        item.measure = jQuery("#measure").combobox("getValue");
        item.code = jQuery("#item-code").textbox("getValue");

        if (item.name === "" || item.measure === "") {
            jQuery.messager.alert(
                "Грешка",
                "Моля попълнете всички задължителни полета."
            );
            return false;
        }

        if (jQuery("#item-id").val() !== "") {
            item.id = parseInt(jQuery("#item-id").val());
            endpoint = Items.editItem(item);
        } else {
            endpoint = Items.addItem(item);
        }

        endpoint
            .done(function() {
                jQuery("#win-add-edit-item").window("close");
                jQuery("#items-grid").datagrid("reload");
            })
            .fail(function(errorObj) {
                jQuery.messager.alert(
                    "Грешка",
                    errorObj.getMessage(),
                    "warning"
                );
            });
    }

    return {
        init,
        initReturnItemsGrid
    };
});
