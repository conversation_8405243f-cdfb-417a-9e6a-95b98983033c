define([
    "easyui",
    "js/main/EasyUIRPCLoaders",
    "js/main/rpc_errors_handler",
    "js/warehouse/companies-grid",
    "js/warehouseAddTransaction/add-items-grid",
    "TF/Rpc/Warehouse/Transactions",
    "TF/Rpc/Warehouse/Documents",
    "TF/Rpc/Warehouse/Warehouses",
    "js/warehouse/utils",
    "js/warehouse/documents-grid",
    "js/warehouse/items-grid",
    "js/warehouse/measures-grid",
    "js/warehouse/comboboxes",
    "js/warehouse/constants"
], function(
    _,
    EasyUIRPCLoaders,
    RpcErrorHandler,
    CompaniesGrid,
    AddItemsGrid,
    Transactions,
    Documents,
    Warehouses,
    Utils,
    DocumentsGrid,
    ItemsGrid,
    MeasuresGrid,
    Comboboxes,
    CONSTANTS
) {

    var addType;

    function init() {
        AddItemsGrid.init();

        addType = jQuery("#transaction_type").val();

        Comboboxes.constants(
            "#tr-document-type",
            addType === 'RETURN' || addType === 'ADD_PRODUCTION' ? ['SCALES_NOTE'] : [],
            {get: "documents.types." + addType.toLowerCase()},
            addType === 'RETURN' || addType === 'ADD_PRODUCTION',
            true
        );

        Comboboxes.constants(
            "#tr-document-dds",
            ["DDS_STANDARD"],
            {get: "documents.dds"},
            false,
            true
        );

        jQuery("#tr-document-date").datebox('setValue', new Date());

        var document_id = sessionStorage.getItem("document_id");
        if(document_id > 0) {
            var params = {
                criteries: {
                    document_id: document_id
                },
                parameters: {}
            };
            Utils.initEditDocument(params, AddItemsGrid);
        }

        bindEventsListeners();
    }

    function bindEventsListeners() {
        jQuery("#saveTransactionBtn").on("click", function () {
            var requestData = Utils.getTransactionData();
            if(!requestData) return;

            Utils.storeTransaction(requestData);
        });

        jQuery("#saveAndCloseTransactionBtn").on("click", function () {
            jQuery.messager.confirm('Потвърждение', 'Заприходени един път в склада, артикулите не могат да бъдат редактирани. Жалаете ли да добавите избраните артикули?', function (r) {
                if (r) {
                    var requestData = Utils.getTransactionData();
                    if(!requestData) return;
                    requestData.document.closed = 1;
                    Utils.storeTransaction(requestData);
                }
            });
        });

        jQuery('#selectCompanyInBtn').on("click", function () {
            CompaniesGrid.initContragentsGrid(
                {types: [CONSTANTS.COMPANY_TYPE_FARM]},
                [
                    {
                        field: "selectFarm",
                        title: "",
                        width: 40,
                        formatter: function(value,row,index){
                            return '<a data-row-index="'+index+'" data-farm-role="in" class="l-btn l-btn-small l-btn-plain js-selectCompanyInBtn"><span class="l-btn-left l-btn-icon-left"><span class="l-btn-text"></span><span class="l-btn-icon icon-add"></span></span></a>';
                        }
                    }],
                '#farms-grid-rp',
                '#farms-toolbar-rp'
            );
            Utils.openRightSlidePanel('selectFarm');
        });

        jQuery('#selectCompanyOutBtn').on("click", function () {
            CompaniesGrid.initContragentsGrid(
                {types: [CONSTANTS.COMPANY_TYPE_CONTRAGENT]},
                [
                    {
                        field: "selectContragent",
                        title: "",
                        width: 40,
                        formatter: function(value,row,index){
                            return '<a data-row-index="'+index+'" data-farm-role="out" class="l-btn l-btn-small l-btn-plain js-selectCompanyOutBtn"><span class="l-btn-left l-btn-icon-left"><span class="l-btn-text"></span><span class="l-btn-icon icon-add"></span></span></a>';
                        }
                    }],
                '#contragents-grid-rp',
                '#contragents-toolbar-rp'
            );
            Utils.openRightSlidePanel('selectContragent');
        });


        jQuery(document).on("click", ".js-selectCompanyOutBtn", Utils.selectCompanyOut);
        jQuery(document).on("click", ".js-selectCompanyInBtn", Utils.selectCompanyIn);
        jQuery(".closeBtn").on("click", Utils.closePanels);
    }

    return {
        init
    };

});
