define([
    "jquery",
    "easyui",
    "js/main/EasyUIRPCLoaders",
    "js/warehouseSubTransaction/items-grid",
    "js/warehouse/utils",
], function(
    jQuery,
    _,
    EasyUIRPCLoaders,
    ItemsGrid,
    Utils
    ) {

    var item;
    var dds;
    var itemsGrid = "#items-grid-rp";
    var itemsGridToolbar = "#items-toolbar-rp";

    function init() {
        initItemsGrid();
        bindEventsListeners();
    }

    function bindEventsListeners() {
        jQuery('div#rightSlidePanelCloseBtn').on("click", function () {
            Utils.closeRightSlidePanel()
        });

        jQuery(document).on("click", ".selectTransactionsItemBtn", function () {
            jQuery('#price_no_dds').textbox('setValue', '');
            var index = jQuery(this).data("row-index");
            item =  jQuery(itemsGrid).datagrid("getRows")[index];
            jQuery("#price_no_dds").textbox('setValue', item.company_price);
            jQuery("#win-sub-transactions").window("open");
        });

        jQuery(document).on("click", "#add-item-btn", function () {
            item.quantity = jQuery("#quantity").textbox('getValue');
            item.priceNoDDS = jQuery("#price_no_dds").textbox('getValue');

            var note = jQuery("#note").textbox('getValue');
            if(note) item.note = note;

            Utils.insertItemInTransaction(item);
            Utils.reloadPrices(dds);
            closeSubItemModal();
        });

        jQuery(document).on("click", "#reject-item-btn", function () {
            closeSubItemModal();
        });
    }

    function initItemsGrid() {
        jQuery('#rightSlidePanelContent').find('.easyui-panel').panel({
            closable: true,
            onClose: function () {
                Utils.closeRightSlidePanel();
                jQuery(this).panel('open');
            }
        });

        jQuery("#tr-document-dds").combobox({
            onChange: function () {
                var ddsSelected = jQuery("#tr-document-dds").combobox("getValue");
                var ddsRows = jQuery("#tr-document-dds").combobox("getData");
                for (var ddsRow of ddsRows) {
                    if(ddsRow.data.key === ddsSelected) dds = ddsRow.data.value;
                }
                Utils.reloadPrices(dds);
            }
        });

        var addItemsGrid = jQuery("#added-items-grid");

        var columns = [
            {
                field: "item_name",
                title: "<b>Име</b>",
                width: 240
            },
            {
                field: "measure_short_name",
                title: "<b>Мярка</b>",
                width: 120
            },
            {
                field: "warehouse_name",
                title: "<b>Склад</b>",
                width: 120
            },
            {
                field: "quantity",
                title: "<b>Количество</b>",
                width: 120,
                align: 'right',
                editor: {
                    type: 'text'
                }
            },
            {
                field: "priceNoDDS",
                title: "<b>Цена без ДДС</b>",
                width: 140,
                align:'right',
                editor: {
                    type: 'text'
                }
            },
            {
                field: "priceWithDDS",
                title: "<b>Цена с ДДС</b>",
                width: 140,
                align:'right',
                formatter: function(value,row){
                    if(dds && row.priceNoDDS !== undefined) {
                        return (((dds * row.priceNoDDS) * 100) / 100).toFixed(5);
                    }
                }
            },
            {
                field: "priceSumNoDDS",
                title: "<b>Общо без ДДС</b>",
                width: 140,
                align:'right',
                formatter: function(value,row){
                    if(row.priceNoDDS !== undefined) {
                        return (((row.priceNoDDS * row.quantity) * 100) / 100).toFixed(5);
                    }
                }
            },
            {
                field: "note",
                title: "<b>Забележка</b>",
                width: 140,
                editor: {
                    type: 'text'
                }
            },
        ];

        var toolBar = [
            {
                id: "btn_add_event",
                text: "Добавяне",
                iconCls: "icon-add",
                handler: function() {
                    var farm_id = jQuery("#tr-company-id-out").val();
                    if (!farm_id) {
                        jQuery.messager.alert("Грешка","Моля изберете стопанство.");
                        return false;
                    }
                    jQuery('.datagrid_search_box').val(''); //Reset quick filter

                    Utils.openRightSlidePanel('selectItem');

                    ItemsGrid.initWarehousesItemsGrid(
                        [
                            {
                                field: "addBtn",
                                title: "",
                                width: 40,
                                formatter: function(value,row,index){
                                        return '<a data-row-index="'+index+'" class="l-btn l-btn-small l-btn-plain selectTransactionsItemBtn"><span class="l-btn-left l-btn-icon-left"><span class="l-btn-text"></span><span class="l-btn-icon icon-add"></span></span></a>';
                                }
                            }
                        ],
                        itemsGrid,
                        itemsGridToolbar
                    );

                }
            },
            {
                id: "btn_edit_event",
                text: "Запази промените",
                iconCls: "icon-save",
                handler: function() {
                    jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да запазите промените?', function (r) {
                            if (r)  {
                                addItemsGrid.datagrid("acceptChanges");
                                Utils.reloadPrices(dds)
                            }
                        });

                }
            },
            {
                id: "btn_edit_event",
                text: "Откажи промените",
                iconCls: "icon-undo",
                handler: function() {
                    jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да откажете промените?', function (r) {
                        if (r)  addItemsGrid.datagrid("rejectChanges");
                    });

                }
            },
            {
                id: "btn_edit_event",
                text: "Премахване",
                iconCls: "icon-delete",
                handler: function() {
                    var item = addItemsGrid.datagrid("getSelected");
                    if (!item) {
                        jQuery.messager.alert("Грешка","Моля изберете артикул за изтриване.");
                        return false;
                    }
                    var rowIndex = addItemsGrid.datagrid("getRowIndex", item);

                    jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да премахнете избрания артикул?', function (r) {
                        if (r)  addItemsGrid.datagrid("deleteRow", rowIndex);
                    });

                }
            }
        ];

        addItemsGrid.datagrid({
            autoRowHeight: true,
            autoRowWidth: true,
            fitColumns:true,
            showFooter: true,
            data: {
                rows: [],
                total: 0,
                footer: Utils.generateFooter(dds)
            },
            rpcMethod: "read",
            pagination: true,
            border: false,
            singleSelect: true,
            checkbox: true,
            rpcParams: [{}],
            columns: [columns],
            toolbar: toolBar,
            onSelect: function(rowIndex, rowData) {
                jQuery(this).datagrid('beginEdit', rowIndex);
            },
            onLoadSuccess: function() {},
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }

    function closeSubItemModal() {
        jQuery("#quantity").textbox('clear');
        jQuery("#price_no_dds").textbox('clear');
        jQuery("#price_with_dds").textbox('clear');
        jQuery("#note").textbox('clear');

        jQuery("#win-sub-transactions").window("close");
    }

    return {
        init
    };
});
