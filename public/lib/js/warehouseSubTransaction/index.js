require(["../../app"], function (app) {
    require(["js_external/domReady!", "js/warehouse/utils"], function (domReady, Utils) {
        Utils.hidePageLoader();
        require([
            "jquery",
            "easyui",
            "js/warehouseSubTransaction/home",
            "js/main/navigation-menu",
            "js/warehouse/home",
            "js/warehouse/warehouse-navigation",
            "js_external/locale/easyui-lang-bg",
        ], function (
            jQuery,
            _,
            home,
            navigation_menu,
            warehouse_home,
            WarehouseNavigation
        ) {
            jQuery.noConflict();
            home.init();
            warehouse_home.init();
            navigation_menu.init();
            WarehouseNavigation.init();
        });
    });
});
