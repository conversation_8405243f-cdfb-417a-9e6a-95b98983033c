define([
    "jquery",
    "easyui",
    "js/main/EasyUIRPCLoaders",
    "js/main/rpc_errors_handler",
    "TF/Rpc/Warehouse/Items",
    "js/warehouse/comboboxes",
    "js/warehouse/utils",
    "js/warehouse/constants"
], function(jQuery, _, EasyUIRPCLoaders,RpcErrorHandler, Items, Comboboxes, Utils, CONSTANTS) {
    function init() {}

    var itemsGrid;
    var itemsGridToolBarId;
    var rpcParams = {
        criteries: {}
    };

    function bindEventsListeners() {
        jQuery("#saveItemBtn").on("click", saveItem);

        jQuery(document).find(itemsGridToolBarId).on("keyup", ".datagrid_search_box", function () {
            var filters = rpcParams.criteries;
            var companies = jQuery("#tr-company-id-in").val();
            if(companies.length > 0) filters.companies = companies;
            Utils.quickFilterDataGrid(itemsGrid, itemsGridToolBarId, 'item_name_code', filters);
        });
    }

    function initWarehousesItemsGrid(additionalColumns = [], gridId = '#items-grid-rp', toolbarId = '#items-toolbar-rp') {
        itemsGridToolBarId = toolbarId;
        bindEventsListeners();

        rpcParams['criteries'] = {
            company: jQuery("#tr-company-id-out").val(),
            fields: {
                'system': {
                    'machines': jQuery("#transaction_type").val() === 'SUB_MACHINE' ? "true" : "false"
                }
            },
            transactionType: 'SUB'
        };

        var columns = [
            {
                field: "item_name",
                title: "<b>Име</b>",
                width: 120,
                sortable:true
            },
            {
                field: "measure_short_name",
                title: "<b>Мярка</b>",
                width: 50,
                sortable:true
            },
            {
                field: "item_code",
                title: "<b>Код</b>",
                width: 50,
                sortable:true
            },
            {
                field: "warehouse_name",
                title: "<b>Склад</b>",
                width: 120,
                sortable:true
            }
        ];

        if(CONSTANTS.ALLOW_WAREHOUSE_SUM_QUANTITIES) {
            columns.push(
                {
                    field: "company_quantity",
                    title: "<b>Количество<br>за стопанство</b>",
                    width: 90,
                    align: 'right',
                    formatter: function (val) {
                        if (val === null) {
                            return '-';
                        }
                        return parseFloat(val).toFixed(3)
                    }
                },
                {
                    field: "warehouse_quantity",
                    title: "<b>Количество<br>за склад</b></b>",
                    width: 90,
                    align: 'right',
                    formatter: function (val) {
                        return parseFloat(val).toFixed(3)
                    }
                },
                {
                    field: "company_price",
                    title: "<b>Средна цена<br>за стопанство</b>",
                    width: 100,
                    align: 'right',
                    formatter: function (val) {
                        if (val === null) {
                            return '-';
                        }
                        return parseFloat(val).toFixed(5)
                    }
                }
            );
        } else {
            columns.push(
                {
                    field: "quantity",
                    title: "<b>Количество</b>",
                    width: 90,
                    align: 'right',
                    formatter: function (val) {
                        return parseFloat(val).toFixed(3)
                    }
                },
                {
                    field: "priceNoDDS",
                    title: "<b>Цена<br>без ДДС</b>",
                    width: 100,
                    align: 'right',
                    formatter: function (val) {
                        return parseFloat(val).toFixed(5)
                    }
                },
                {
                    field: "priceWithDDS",
                    title: "<b>Цена<br>с ДДС</b>",
                    width: 100,
                    align: 'right',
                    formatter: function (val) {
                        return parseFloat(val).toFixed(5)
                    }
                }
            );
        }

        columns.push(
            {
                field: "expiry_date",
                title: "<b>Годност до</b>",
                width: 90
            }
        );

        //Add additional columns
        if(additionalColumns.length > 0) {
            for (var column of additionalColumns) {
                columns.unshift(column);
            }
        }

        itemsGrid = jQuery(gridId);
        itemsGrid.datagrid({
            autoRowHeight: true,
            autoRowWidth: true,
            url: "index.php?warehouse-rpc=warehouse-articles",
            rpcMethod: "getArticles",
            pagination: true,
            fit: true,
            border: false,
            singleSelect: true,
            rpcParams: [rpcParams],
            columns: [columns],
            toolbar: toolbarId,
            onSelect: function (rowIndex, rowData) {
            },
            onLoadSuccess: function () {
            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }

    function saveItem() {
        var item = {};
        var endpoint;

        item.name = jQuery("#item-name").textbox("getValue");
        item.measure = jQuery("#measure").combobox("getValue");
        item.code = jQuery("#item-code").textbox("getValue");

        if (item.name === "" || item.measure === "") {
            jQuery.messager.alert(
                "Грешка",
                "Моля попълнете всички задължителни полета."
            );
            return false;
        }

        if (jQuery("#item-id").val() !== "") {
            item.id = parseInt(jQuery("#item-id").val());
            endpoint = Items.editItem(item);
        } else {
            endpoint = Items.addItem(item);
        }

        endpoint
            .done(function() {
                jQuery("#win-add-edit-item").window("close");
                jQuery("#items-grid").datagrid("reload");
            })
            .fail(function(errorObj) {
                jQuery.messager.alert(
                    "Грешка",
                    errorObj.getMessage(),
                    "warning"
                );
            });
    }

    return {
        init,
        initWarehousesItemsGrid
    };
});
