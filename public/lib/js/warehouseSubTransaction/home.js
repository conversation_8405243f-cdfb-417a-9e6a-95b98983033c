define([
    "easyui",
    "js/main/EasyUIRPCLoaders",
    "js/main/rpc_errors_handler",
    "js/warehouse/companies-grid",
    "js/warehouseSubTransaction/add-items-grid",
    "TF/Rpc/Warehouse/Transactions",
    "js/warehouse/utils",
    "TF/Rpc/Warehouse/Warehouses",
    "TF/Rpc/Warehouse/Documents",
    "js/warehouse/documents-grid",
    "js/warehouse/items-grid",
    "js/warehouse/measures-grid",
    "js/warehouse/comboboxes",
    "js/warehouse/constants",
], function(
    _,
    EasyUIRPCLoaders,
    RpcErrorHandler,
    CompaniesGrid,
    AddItemsGrid,
    Transactions,
    Utils,
    Warehouses,
    Documents,
    DocumentsGrid,
    ItemsGrid,
    MeasuresGrid,
    Comboboxes,
    CONSTANTS
) {

    var confirmMessages = {
        SUB: 'Артикулите ще бъдат изписани към избрания контрагент. Желаете ли да продължите?',
        SUB_PLOT: 'Артикулите ще бъдат изписани към парцели в Агротехника. Желаете ли да продължите?',
        SUB_MACHINE: 'Артикулите ще бъдат изписани към съответния актив. Желаете ли да продължите?'
    };

    function init() {
        AddItemsGrid.init();
        Comboboxes.constants(
            "#tr-document-type",
            ['WAREHOUSE_NOTE_OUT'],
            {get: "documents.types.sub"},
            true,
            true
        );

        Comboboxes.constants(
            "#tr-document-dds",
            jQuery("#transaction_type").val() !== 'SUB' ? ["DDS_113"] : ["DDS_STANDARD"],
            {get: "documents.dds"},
            jQuery("#transaction_type").val() !== 'SUB',
            true
        );

        jQuery("#tr-document-date").datebox('setValue', new Date());

        var document_id = sessionStorage.getItem("document_id");
        if(document_id > 0) {
            var params = {
                criteries: {
                    document_id: document_id
                },
                parameters: {}
            };
            Utils.initEditDocument(params, AddItemsGrid);
        }

        jQuery("#tr-document-number").textbox({disabled: true});

        bindEventsListeners();
    }

    function bindEventsListeners() {
        jQuery('#selectCompanyInBtn').on("click", function () {
            CompaniesGrid.initContragentsGrid(
                {types: [CONSTANTS.COMPANY_TYPE_CONTRAGENT]},
                [
                    {
                        field: "selectContragent",
                        title: "",
                        width: 40,
                        formatter: function(value,row,index){
                            return '<a data-row-index="'+index+'" data-farm-role="in" class="l-btn l-btn-small l-btn-plain js-selectCompanyInBtn"><span class="l-btn-left l-btn-icon-left"><span class="l-btn-text"></span><span class="l-btn-icon icon-add"></span></span></a>';
                        }
                    }],
                '#contragents-grid-rp',
                '#contragents-toolbar-rp'
            );
            Utils.openRightSlidePanel('selectContragent');
        });

        jQuery('#selectCompanyOutBtn').on("click", function () {
            CompaniesGrid.initContragentsGrid(
                {types: [CONSTANTS.COMPANY_TYPE_FARM]},
                [
                {
                    field: "selectFarm",
                    title: "",
                    width: 40,
                    formatter: function(value,row,index){
                        return '<a data-row-index="'+index+'" data-farm-role="out" class="l-btn l-btn-small l-btn-plain js-selectCompanyOutBtn"><span class="l-btn-left l-btn-icon-left"><span class="l-btn-text"></span><span class="l-btn-icon icon-add"></span></span></a>';
                    }
                }],
                '#farms-grid-rp',
                '#farms-toolbar-rp'
            );
            Utils.openRightSlidePanel('selectFarm');
        });

        jQuery('#selectFarmCompanyInBtn').on("click", function () {
            CompaniesGrid.initContragentsGrid(
                {types: [CONSTANTS.COMPANY_TYPE_FARM]},
                [
                    {
                        field: "selectFarm",
                        title: "",
                        width: 40,
                        formatter: function(value,row,index){
                            return '<a data-row-index="'+index+'" data-farm-role="in" class="l-btn l-btn-small l-btn-plain js-selectCompanyInBtn"><span class="l-btn-left l-btn-icon-left"><span class="l-btn-text"></span><span class="l-btn-icon icon-add"></span></span></a>';
                        }
                    }],
                '#farms-grid-rp',
                '#farms-toolbar-rp'
            );
            Utils.openRightSlidePanel('selectFarm');
        });

        jQuery('#selectMachineBtn').on("click", function () {
            CompaniesGrid.initContragentsGrid(
                {types: [CONSTANTS.COMPANY_TYPE_MACHINE]},
                [
                    {
                        field: "selectMachine",
                        title: "",
                        width: 40,
                        formatter: function(value,row,index){
                            return '<a data-row-index="'+index+'" data-farm-role="in" class="l-btn l-btn-small l-btn-plain js-selectCompanyInBtn"><span class="l-btn-left l-btn-icon-left"><span class="l-btn-text"></span><span class="l-btn-icon icon-add"></span></span></a>';
                        }
                    }],
                '#machines-grid-rp',
                '#machines-toolbar-rp'
            );
            Utils.openRightSlidePanel('selectMachine');
        });

        jQuery("#saveTransactionBtn").on("click", function () {
            var requestData = Utils.getTransactionData();
            if(!requestData) return;

            Utils.storeTransaction(requestData);
        });

        jQuery("#saveAndCloseTransactionBtn").on("click", function () {
            jQuery.messager.confirm('Потвърждение', confirmMessages[jQuery("#transaction_type").val()], function (r) {
                if (r) {
                    var requestData = Utils.getTransactionData();
                    if(!requestData) return;

                    requestData.document.closed = 1;
                    Utils.storeTransaction(requestData);
                }
            });
        });

        jQuery(document).on("click", ".js-selectCompanyInBtn", Utils.selectCompanyIn);
        jQuery(document).on("click", ".js-selectCompanyOutBtn", Utils.selectCompanyOut);
    }

    return {
        init
    };

});
