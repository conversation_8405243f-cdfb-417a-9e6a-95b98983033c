
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Owners/Owners'], factory);
    } else {
        Namespace('TF.Rpc.Owners');
        // Browser globals (root is window)
        root.TF.Rpc.Owners.OwnersTree = factory(TF.Rpc.Owners);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        read: function () {
            return rpcService.request('owners-tree', 'read', arguments);
        },
        addOwner: function() {
            return rpcService.request('owners-tree', 'addOwner', arguments);
        },
        editOwner: function() {
        	return rpcService.request('owners-tree', 'editOwner', arguments);
        },
        addLegalOwner: function() {
            return rpcService.request('owners-tree', 'addLegalOwner', arguments);
        },
        editLegalOwner: function() {
            return rpcService.request('owners-tree', 'editLegalOwner', arguments);
        },
        deleteOwner: function() {
        	return rpcService.request('owners-tree', 'deleteOwner', arguments);
        },
        markForEdit: function() {
            return rpcService.request('owners-tree', 'markForEdit', arguments);
        },
        getOwnerParents: function() {
        	return rpcService.request('owners-tree', 'getOwnerParents', arguments);
        }
    }
    

}));
