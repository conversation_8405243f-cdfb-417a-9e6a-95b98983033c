
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Owners/Owners'], factory);
    } else {
        Namespace('TF.Rpc.Owners');
        // Browser globals (root is window)
        root.TF.Rpc.Owners.OwnersRepresentedGrid = factory(TF.Rpc.Owners);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        read: function () {
            return rpcService.request('owners-represented-grid', 'read', arguments);
        },
        changeOwnerRepresentative: function() {
            return rpcService.request('owners-represented-grid', 'changeOwnerRepresentative', arguments);
        },
    }
    

}));
