
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Owners/Owners'], factory);
    } else {
        Namespace('TF.Rpc.Owners');
        // Browser globals (root is window)
        root.TF.Rpc.Owners.OwnersList = factory(TF.Rpc.Owners);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        /**
         * read Owners List
         * api-method read
         * @param string $ownerType        Owners or Reps or Companies
         * @param  string $searchedName    The searched name.
         * @return array result
         * {
         *     #item string key            The owner id.
         *     #item string value          The owner name.
         * }
         */
        readOwnersList: function (ownerType, searchedName) {
            rpcService.hideLoader();
            return rpcService.request('owners-list', 'read', arguments);
        },
    }

}));
