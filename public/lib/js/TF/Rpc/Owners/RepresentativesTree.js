
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Owners/Owners'], factory);
    } else {
        Namespace('TF.Rpc.Owners');
        // Browser globals (root is window)
        root.TF.Rpc.Owners.RepresentativesTree = factory(TF.Rpc.Owners);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        read: function () {
            return rpcService.request('representatives-tree', 'read', arguments);
        },
        addRepresentative: function() {
            return rpcService.request('representatives-tree', 'addRepresentative', arguments);
        },
        editRepresentative: function() {
        	return rpcService.request('representatives-tree', 'editRepresentative', arguments);
        },
        deleteRepresentative: function() {
        	return rpcService.request('representatives-tree', 'deleteRepresentative', arguments);
        },
        markForEdit: function() {
            return rpcService.request('representatives-tree', 'markForEdit', arguments);
        }
    }
    

}));
