
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Owners/Owners'], factory);
    } else {
        Namespace('TF.Rpc.Owners');
        // Browser globals (root is window)
        root.TF.Rpc.Owners.OwnersFiles = factory(TF.Rpc.Owners);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {
        saveOwnerFile: function (param) {
            return rpcService.request('owners-files', 'saveOwnerFiles', arguments);
        },
        deleteOwnerFile: function (id) {
            return rpcService.request('owners-files', 'deleteOwnerFiles', arguments);
        },
        read: function () {
            return rpcService.request('owners-files', 'read', arguments);
        },
        downloadAttachment: function () {
            return rpcService.request('owners-files', 'downloadAttachment', arguments);
        },
        deleteAttachment: function () {
            return rpcService.request('owners-files', 'deleteAttachment', arguments);
        }
    }

}));
