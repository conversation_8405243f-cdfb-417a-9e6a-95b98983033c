
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Owners/Owners'], factory);
    } else {
        Namespace('TF.Rpc.Owners');
        // Browser globals (root is window)
        root.TF.Rpc.Owners.OwnersHeritorsTree = factory(TF.Rpc.Owners);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {       
       
        addOwnerHeritor: function (ownerId, parent) {
            return rpcService.request('owners-heritors-tree', 'addOwnerHeritor', arguments); 
        },
        deleteOwnerHeritor: function ($path) {
            return rpcService.request('owners-heritors-tree', 'deleteOwnerHeritor', arguments); 
        },
 
    }

}));
