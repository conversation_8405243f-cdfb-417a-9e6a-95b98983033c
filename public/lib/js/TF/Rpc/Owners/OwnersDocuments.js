
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Owners/Owners'], factory);
    } else {
        Namespace('TF.Rpc.Owners');
        // Browser globals (root is window)
        root.TF.Rpc.Owners.OwnersDocuments = factory(TF.Rpc.Owners);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {       
       
        saveOwnerDocument: function (param) {
            return rpcService.request('owners-documents', 'saveOwnerDocument', arguments); 
        },
        deleteOwnerDocument: function (id) {
            return rpcService.request('owners-documents', 'deleteOwnerDocument', arguments); 
        },
        read: function () {
            return rpcService.request('owners-documents', 'read', arguments); 
        },
        downloadAttachment: function () {
            return rpcService.request('owners-documents', 'downloadAttachment', arguments);
        },
        deleteAttachment: function () {
            return rpcService.request('owners-documents', 'deleteAttachment', arguments);
        }
    }

}));
