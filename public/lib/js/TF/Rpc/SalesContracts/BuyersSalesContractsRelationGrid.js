
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/SalesContracts/SalesContracts'], factory);
    } else {
        Namespace('TF.Rpc.SalesContracts');
        // Browser globals (root is window)
        root.TF.Rpc.SalesContracts.BuyersSalesContractsRelationGrid = factory(TF.Rpc.SalesContracts);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        
        read: function(salesContractId, page, rows, sort, order) {
            return rpcService.request('buyers-sales-contracts-relation-grid', 'read', arguments);
        },
        deleteBuyerRel: function(relation_id) {
            return rpcService.request('buyers-sales-contracts-relation-grid', 'deleteBuyerRel', arguments);
        },
    }
    

}));
