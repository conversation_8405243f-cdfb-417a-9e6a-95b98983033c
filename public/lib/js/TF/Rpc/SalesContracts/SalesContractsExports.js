
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/SalesContracts/SalesContracts'], factory);
    } else {
        Namespace('TF.Rpc.SalesContracts');
        // Browser globals (root is window)
        root.TF.Rpc.SalesContracts.SalesContractsExports = factory(TF.Rpc.SalesContracts);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        exportSaleContractBlank: function () {
            return rpcService.request('sales-contracts-exports', 'exportSaleContractBlank', arguments);
        },
        deleteFile: function () {
            return rpcService.request('sales-contracts-exports', 'deleteFile', arguments);
        },
        downloadAttached: function () {
            return rpcService.request('sales-contracts-exports', 'downloadAttached', arguments);
        }
    }

}));
