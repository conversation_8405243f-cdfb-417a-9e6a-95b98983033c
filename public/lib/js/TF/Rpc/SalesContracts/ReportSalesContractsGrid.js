
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/SalesContracts/SalesContracts'], factory);
    } else {
        Namespace('TF.Rpc.SalesContracts');
        // Browser globals (root is window)
        root.TF.Rpc.SalesContracts.ReportSalesContractsGrid = factory(TF.Rpc.SalesContracts);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        
        read: function(filterObj, page, rows, sort, order) {
            return rpcService.request('report-sales-contracts-grid', 'read', arguments);
        },
        exportToExcelReportSalesContractsData: function (filterObj, page, rows, sort, order) {
            return rpcService.request('report-sales-contracts-grid', 'exportToExcelReportSalesContractsData', arguments);
        },
    }
    

}));
