
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/SalesContracts/SalesContracts'], factory);
    } else {
        Namespace('TF.Rpc.SalesContracts');
        // Browser globals (root is window)
        root.TF.Rpc.SalesContracts.SalesContractsTree = factory(TF.Rpc.SalesContracts);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        read: function () {
            return rpcService.request('sales-contracts-tree', 'read', arguments);
        },
        checkForExistence: function (c_num, farming) {
            return rpcService.request('sales-contracts-tree', 'checkForExistence', arguments);
        },
        addSalesContract: function (params) {
            return rpcService.request('sales-contracts-tree', 'addSalesContract', arguments);
        },
        loadSalesContract: function (contractId) {
            return rpcService.request('sales-contracts-tree', 'loadSalesContract', arguments);
        },
        updateSalesContract: function (contractId, params) {
            return rpcService.request('sales-contracts-tree', 'updateSalesContract', arguments);
        },
        deleteSalesContract: function (contractId) {
            return rpcService.request('sales-contracts-tree', 'deleteSalesContract', arguments);
        },
        
    }
    

}));
