
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/SalesContracts/SalesContracts'], factory);
    } else {
        Namespace('TF.Rpc.SalesContracts');
        // Browser globals (root is window)
        root.TF.Rpc.SalesContracts.SalesContractsFilesGrid = factory(TF.Rpc.SalesContracts);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        
        delete: function() {
            return rpcService.request('sales-contracts-files-maingrid', 'delete', arguments);
        },
    }
    

}));
