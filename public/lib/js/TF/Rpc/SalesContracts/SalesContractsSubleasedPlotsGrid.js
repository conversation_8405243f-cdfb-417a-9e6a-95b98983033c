
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/SalesContracts/SalesContracts'], factory);
    } else {
        Namespace('TF.Rpc.SalesContracts');
        // Browser globals (root is window)
        root.TF.Rpc.SalesContracts.SalesContractsSubleasedPlotsGrid = factory(TF.Rpc.SalesContracts);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        read: function () {
            return rpcService.request('sales-contracts-subleased-plots-grid', 'read', arguments);
        },
        saveSubleasedPlotDate: function (plot_ids, start_date_salescontract, date_option, apply_to) {
            return rpcService.request('sales-contracts-subleased-plots-grid', 'saveSubleasedPlotDate', arguments);
        },
        
        
    }
    

}));
