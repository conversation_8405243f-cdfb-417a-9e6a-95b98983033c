
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/SalesContracts/SalesContracts'], factory);
    } else {
        Namespace('TF.Rpc.SalesContracts');
        // Browser globals (root is window)
        root.TF.Rpc.SalesContracts.BuyersGrid = factory(TF.Rpc.SalesContracts);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        
        addBuyer: function(name, contacts) {
            return rpcService.request('buyers-sales-contracts-grid', 'addBuyer', arguments);
        },
        updateBuyer: function(id, name, contacts) {
            return rpcService.request('buyers-sales-contracts-grid', 'updateBuyer', arguments);
        },
        deleteBuyer: function(id) {
            return rpcService.request('buyers-sales-contracts-grid', 'deleteBuyer', arguments);
        },
        addRelationBuyerSalesContract: function(buyer_id, sales_contract_id) {
            return rpcService.request('buyers-sales-contracts-grid', 'addRelationBuyerSalesContract', arguments);
        },
    }
    

}));
