
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/SubsidiesWizard/SubsidiesWizard'], factory);
    } else {
        Namespace('TF.Rpc.SubsidiesWizard');
        // Browser globals (root is window)
        root.TF.Rpc.SubsidiesWizard.CulturesPropertyGrid = factory(TF.Rpc.SubsidiesWizard);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {       
       /**
         * Exports layer in .shp
         * @param  {objects} data {
         *     @item string export_type
         *     @item string layer_name
         *     @item string layer_type
         *     @item string layer_id
         *     @item boolean united
         *     @item boolean export_old
         *     @item boolean use_filter
         *     @item array selected_ids
         * }
         *
         * @return {Diferred}       See http://api.jquery.com/category/deferred-object/.
         */
        read: function () {
            return rpcService.request('pg-intermidiate-cultures', 'read', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        savePropertyGridCultures: function () {
            return rpcService.request('pg-intermidiate-cultures', 'savePropertyGridCultures', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        }
    }

}));
