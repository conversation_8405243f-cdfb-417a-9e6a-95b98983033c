
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/SubsidiesWizard/SubsidiesWizard'], factory);
    } else {
        Namespace('TF.Rpc.SubsidiesWizard');
        // Browser globals (root is window)
        root.TF.Rpc.SubsidiesWizard.ForIsakDiffVPSDetailedGrid = factory(TF.Rpc.SubsidiesWizard);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {       
       /**
         * Exports layer in .shp
         * @param  {objects} data {
         *     @item string export_type
         *     @item string layer_name
         *     @item string layer_type
         *     @item string layer_id
         *     @item boolean united
         *     @item boolean export_old
         *     @item boolean use_filter
         *     @item array selected_ids
         * }
         *
         * @return {Diferred}       See http://api.jquery.com/category/deferred-object/.
         */
        read: function () {
            return rpcService.request('for-isak-diff-vps-detailed-grid', 'read', arguments); 
        },
        initForIsakDiffVPS: function () {
            return rpcService.request('for-isak-diff-vps-detailed-grid', 'initForIsakDiffVPS', arguments); 
        },
        exportGridToXLS: function () {
            return rpcService.request('for-isak-diff-vps-detailed-grid', 'exportGridToXLS', arguments); 
        },
        exportGridToPDF: function () {
            return rpcService.request('for-isak-diff-vps-detailed-grid', 'exportGridToPDF', arguments); 
        }
    };

}));
