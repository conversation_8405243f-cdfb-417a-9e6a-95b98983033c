
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Hypothecs/Hypothecs'], factory);
    } else {
        Namespace('TF.Rpc.Hypothecs');
        // Browser globals (root is window)
        root.TF.Rpc.Hypothecs.HypothecsFilesGrid = factory(TF.Rpc.Hypothecs);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    var apiId = 'hypothecs-files-grid';
    
    return {
        /**
         * Returns list of files for hypothec
         * @param integer $hypothecId
         * @param integer $page
         * @param integer $rows
         * @param string $sort
         * @param string $order
         * @return {Diferred}       See http://api.jquery.com/category/deferred-object/.
         */
        read: function () {
            return rpcService.request(apiId, 'read', arguments);
        },
        /**
         * Deletes file from hypothec
         * @param integer $fileId
         * @param string $fileName
         * @param integer $groupId
         * @param integer $userId
         * @return {Diferred}       See http://api.jquery.com/category/deferred-object/.
         */
        delete: function () {
            return rpcService.request(apiId, 'delete', arguments);
        }
    };

}));
