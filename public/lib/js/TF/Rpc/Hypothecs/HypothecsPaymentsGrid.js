
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Hypothecs/Hypothecs'], factory);
    } else {
        Namespace('TF.Rpc.Hypothecs');
        // Browser globals (root is window)
        root.TF.Rpc.Hypothecs.HypothecsPaymentsGrid = factory(TF.Rpc.Hypothecs);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    var apiId = 'hypothecs-payments-grid';
    
    return {
        /**
         * Returns paymets for hypothec
         * @param integer $hypothecId
         * @param integer $page
         * @param integer $rows
         * @param string $sort
         * @param string $order
         * @return {Diferred}       See http://api.jquery.com/category/deferred-object/.
         */
        read: function () {
            return rpcService.request(apiId, 'read', arguments);
        },
        /**
         * Adds new payment to hypothec
         * @param integer $hypothecId
         * @param float $amount
         * @param string $date
         * @param string $comment
         * @return {Diferred}       See http://api.jquery.com/category/deferred-object/.
         */
        add: function () {
            return rpcService.request(apiId, 'add', arguments);
        },
        /**
         * Edits payment data for hypothec
         * @param integer $paymentId
         * @param float $amount
         * @param string $date
         * @param string $comment
         * @return {Diferred}       See http://api.jquery.com/category/deferred-object/.
         */
        edit: function () {
            return rpcService.request(apiId, 'edit', arguments);
        },
        /**
         * Deletes payment from hypothec
         * @param integer $paymentId
         * @return {Diferred}       See http://api.jquery.com/category/deferred-object/.
         */
        delete: function () {
            return rpcService.request(apiId, 'delete', arguments);
        }
    };

}));
