
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Hypothecs/Hypothecs'], factory);
    } else {
        Namespace('TF.Rpc.Hypothecs');
        // Browser globals (root is window)
        root.TF.Rpc.Hypothecs.HypothecsPlotsGrid = factory(TF.Rpc.Hypothecs);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    var apiId = 'hypothecs-plots-grid';
    
    return {
        /**
         * Returns list of hypothec plots
         * @param integer $hypothecId
         * @param integer $page
         * @param integer $rows
         * @param string $sort
         * @param string $order
         * @return {Diferred}       See http://api.jquery.com/category/deferred-object/.
         */
        read: function () {
            return rpcService.request(apiId, 'read', arguments);
        },
        /**
         * Returns plots available for adding to hypothec
         * @param array $filterParams {
         *   @item integer hypothec_id
         *   @item integer farming_id
         *   @item string h_start_date
         *   @item string c_num
         *   @item string subleased
         *   @item string kad_ident
         *   @item string ekate
         *   @item string masiv
         *   @item string number
         *   @item string category
         *   @item string area_type
         * }
         * @param integer $page
         * @param integer $rows
         * @param string $sort
         * @param string $order
         * @return {Diferred}       See http://api.jquery.com/category/deferred-object/.
         */
        readForAdding: function () {
            return rpcService.request(apiId, 'readForAdding', arguments);
        },
        /**
         * Adds plots to hypothec
         * @param array $plots
         * @return {Diferred}       See http://api.jquery.com/category/deferred-object/.
         */
        add: function () {
            return rpcService.request(apiId, 'add', arguments);
        },
        /**
         * Edits hypothec area for plot
         * @api-method delete
         * @param integer $recordId
         * @param float $hypothecArea
         * @return {Diferred}       See http://api.jquery.com/category/deferred-object/.
         */
        edit: function () {
            return rpcService.request(apiId, 'edit', arguments);
        },
        /**
         * Deletes plot from hypothec
         * @param integer $recordId
         * @return {Diferred}       See http://api.jquery.com/category/deferred-object/.
         */
        delete: function () {
            return rpcService.request(apiId, 'delete', arguments);
        },
        /**
         * Returns maximum allowed hypothec area for plot
         * @param integer $plotId
         * @param integer $farmingId
         * @return {Diferred}       See http://api.jquery.com/category/deferred-object/.
         */
        getMaxHypothecArea: function () {
            return rpcService.request(apiId, 'getMaxHypothecArea', arguments);
        }
    }

}));
