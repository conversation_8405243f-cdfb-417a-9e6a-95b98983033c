
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Hypothecs/Hypothecs'], factory);
    } else {
        Namespace('TF.Rpc.Hypothecs');
        // Browser globals (root is window)
        root.TF.Rpc.Hypothecs.HypothecsTree = factory(TF.Rpc.Hypothecs);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {
        /**
         * Returns list of hypothecs matching filters
         * @param array $filterParams {
         *   @item string hypothec_id
         *   @item string h_num
         *   @item boolean is_active
         *   @item integer farming
         *   @item integer creditor
         *   @item string start_date_from
         *   @item string start_date_to
         *   @item string due_date_from
         *   @item string due_date_to
         *   @item string kad_ident
         *   @item string ekate
         *   @item string masiv
         *   @item string number
         *   @item string category
         *   @item string area_type
         * }
         * @param integer $page
         * @param integer $rows
         * @param string $sort
         * @param string $order
         * @return {Diferred}       See http://api.jquery.com/category/deferred-object/.
         */
        read: function () {
            return rpcService.request('hypothecs-tree', 'read', arguments);
        },
        /**
         * Adds new creditor
         * @param  {object} data {
         *     @item string layer_name
         *     @item string layer_type
         *     @item string layer_id
         *     @item boolean united
         *     @item boolean export_old
         *     @item boolean use_filter
         *     @item array selected_ids
         * }
         * @return {Diferred}       See http://api.jquery.com/category/deferred-object/.
         */
        add: function () {
            return rpcService.request('hypothecs-tree', 'add', arguments);
        },
        /**
         * Adds new creditor
         * @param  {object} data {
         *     @item string layer_name
         *     @item string layer_type
         *     @item string layer_id
         *     @item boolean united
         *     @item boolean export_old
         *     @item boolean use_filter
         *     @item array selected_ids
         * }
         * @return {Diferred}       See http://api.jquery.com/category/deferred-object/.
         */
        edit: function () {
            return rpcService.request('hypothecs-tree', 'edit', arguments);
        },
        deactivate: function () {
            return rpcService.request('hypothecs-tree', 'deactivate', arguments);
        },
        activate: function () {
            return rpcService.request('hypothecs-tree', 'activate', arguments);
        },
        export: function() {
            return rpcService.request('hypothecs-tree', 'export', arguments);
        },
        removeFile: function() {
            return rpcService.request('hypothecs-tree', 'removeFile', arguments);
        }
    };

}));
