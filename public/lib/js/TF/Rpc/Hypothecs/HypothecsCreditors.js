
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Hypothecs/Hypothecs'], factory);
    } else {
        Namespace('TF.Rpc.Hypothecs');
        // Browser globals (root is window)
        root.TF.Rpc.Hypothecs.HypothecsCreditors = factory(TF.Rpc.Hypothecs);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {
        /**
         * Returns list of all creditors
         * @param boolean $selected
         * @param boolean $recordAll
         * @return {Diferred}       See http://api.jquery.com/category/deferred-object/.
         */
        readCombobox: function () {
            return rpcService.request('hypothecs-creditors', 'add', arguments);
        },
        /**
         * Adds new creditor
         * @param string $name
         * @return {Diferred}       See http://api.jquery.com/category/deferred-object/.
         */
        add: function () {
            return rpcService.request('hypothecs-creditors', 'add', arguments);
        }
    }

}));
