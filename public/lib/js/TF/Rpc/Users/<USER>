
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Users/<USER>'], factory);
    } else {
        Namespace('TF.Rpc.Users');
        // Browser globals (root is window)
        root.TF.Rpc.Users.UsersMainGrid = factory(TF.Rpc.Users);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {
        read: function () {
            return rpcService.request('users', 'read', arguments);
        },
        add: function() {
        	return rpcService.request('users', 'add', arguments);
        },
        markForEdit: function() {
        	return rpcService.request('users', 'markForEdit', arguments);
        },
        editUser: function() {
            return rpcService.request('users', 'editUser', arguments);
        },
        deleteSelectedUsers: function() {
            return rpcService.request('users', 'deleteSelectedUsers', arguments);
        },
        changeActiveStatus: function() {
            return rpcService.request('users', 'changeActiveStatus', arguments);
        },
        setUserRights: function() {
            return rpcService.request('users', 'setUserRights', arguments);
        },
        getLastLoginInfo: function() {
            return rpcService.request('users', 'getLastLoginInfo', arguments);
        },
        loginAsUser: function() {
            return rpcService.request('users', 'loginAsUser', arguments);
        },
        getAllModems: function () {
            return rpcService.request('users', 'getAllModems', arguments);
        },
        getUserModems: function () {
            return rpcService.request('users', 'getUserModems', arguments);
        },
        addUserModems: function () {
            return rpcService.request('users', 'addUserModems', arguments);
        },
        removeUserModem: function () {
            return rpcService.request('users', 'removeUserModem', arguments);
        },
        getModemFiles: function () {
            return rpcService.request('users', 'getModemFiles', arguments);
        },
        getAllSubscriptions: function () {
            return rpcService.request('users', 'getAllSubscriptions', arguments);
        },
        getUserSubscriptions: function () {
            return rpcService.request('users', 'getUserSubscriptions', arguments);
        },
        setUserSubscriptions: function () {
            return rpcService.request('users', 'setUserSubscriptions', arguments);
        },
        hasContractsInEktte: function () {
            return rpcService.request('users', 'getEkatteContractsCount', arguments);
        },
        deleteSelectedEktte: function () {
            return rpcService.request('users', 'deleteEkatte', arguments);
        },
        exportToExcelUsersData: function () {
            return rpcService.request('users', 'exportToExcelUsersData', arguments);
        },
        getFarmings: function() {
            return rpcService.request('users', 'getFarmings', arguments);
        }
    }

}));
