
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Rpc'], factory);
    } else {
        Namespace('TF.Rpc.Users');
        // Browser globals (root is window)
        root.TF.Rpc.Users = factory(TF.Rpc);
    }
}(typeof self !== 'undefined' ? self : this, function (Rpc) {
    return new Rpc('users-rpc');
}));
