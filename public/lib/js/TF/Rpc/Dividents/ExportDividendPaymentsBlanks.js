
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Dividends/Dividends'], factory);
    } else {
        Namespace('TF.Rpc.Dividends');
        // Browser globals (root is window)
        root.TF.Rpc.Dividends.ExportDividendPaymentsBanks = factory(TF.Rpc.Dividends);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {     
    	expDividendOrder: function () {
            return rpcService.request('export-dividend-payments-blanks', 'expDividendOrder', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        expDividendPayment: function () {
            return rpcService.request('export-dividend-payments-blanks', 'expDividendPayment', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        }
    };

}));
