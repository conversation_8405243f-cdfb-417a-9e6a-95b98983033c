
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Dividends/Dividends'], factory);
    } else {
        Namespace('TF.Rpc.Dividends');
        // Browser globals (root is window)
        root.TF.Rpc.Dividends.AnnualReportTree = factory(TF.Rpc.Dividends);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {     
    	create: function () {
            return rpcService.request('annual-report-tree', 'create', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        read: function () {
            return rpcService.request('annual-report-tree', 'read', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        update: function () {
            return rpcService.request('annual-report-tree', 'update', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        load: function () {
            return rpcService.request('annual-report-tree', 'load', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        deleteLeaf: function () {
            return rpcService.request('annual-report-tree', 'delete', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        }
    };

}));
