
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Dividends/Dividends'], factory);
    } else {
        Namespace('TF.Rpc.Dividends');
        // Browser globals (root is window)
        root.TF.Rpc.Dividends.DividendsGrid = factory(TF.Rpc.Dividends);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {     
        read: function () {
            return rpcService.request('dividends-grid', 'read', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        createDividend: function () {
            return rpcService.request('dividends-grid', 'createDividend', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        createDividendReversal: function () {
            return rpcService.request('dividends-grid', 'createDividendReversal', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        expDividendReport: function () {
            return rpcService.request('dividends-grid', 'expDividendReport', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        removeFile: function () {
            return rpcService.request('dividends-grid', 'removeFile', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        }
    };

}));
