Namespace("TF.Rpc.Payments");

TF.Rpc.Payments.UnpaidReantaReportGrid = (function(rpcService) {
  return {
    read: function(params, rows, sort, order) {
      return rpcService.request("unpaid-rented-report-grid", "read", arguments);
    },
    exportExcel: function(params) {
      return rpcService.request(
        "unpaid-rented-report-grid",
        "exportExcel",
        arguments
      );
    }
  };
})(TF.Rpc.Payments);
