
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Payments/Payments'], factory);
    } else {
        Namespace('TF.Rpc.Payments');
        // Browser globals (root is window)
        root.TF.Rpc.Payments.ContractPaymentsGrid = factory(TF.Rpc.Payments);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {       
        /**
         * Get contract payments
         * api-method read
         * @param int $contract_id      -The Contract id
         * @param int $annex_id         -The Annex id
         * @param int $year             -The year for contract payments
         * @param integer $page         -The current page number.
         * @param integer $rows         -Rows per page.
         * @param string $sort          -A grid column by which the grid is sorted.
         * @param string $order         -The sort order ASC/DESC
         * @return array
         */
        getContractPayments: function ($contract_id, $annex_id, $year, page, rows, sort, order) {
            return rpcService.request('contract-payments-grid', 'read', arguments);
        }
    }

}));
