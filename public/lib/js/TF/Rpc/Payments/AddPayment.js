
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Payments/Payments'], factory);
    } else {
        Namespace('TF.Rpc.Payments');
        // Browser globals (root is window)
        root.TF.Rpc.Payments.AddPayment = factory(TF.Rpc.Payments);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {
         /**
         * Save payment
         * api-method savePayment
         * @param array $data
         * {
         *     #item array owner_array with data for owner
         *     #item string year
         *     #item boolean payment_reduce
         * }
         * @param array $inputData
         * {
         *     #item boolean payment_type_money
         *     #item boolean payment_type_natura
         *     #item boolean payment_method_cash
         *     #item boolean payment_method_bank
         *     #item boolean payment_order
         *     #item boolean weighing_note
         *     #item string payment_amount
         *     #item string payment_date
         *     #item string payment_natura_type
         *     #item string payment_natura_price
         *     #item string payment_natura_amount
         *     #item string payment_recipient
         *     #item string payment_recipient_egn
         *     #item string payment_recipient_proxy
         *     #item string payment_bank_account
         * }
         * @return bool|array -Bolean must be 'false'
         *{
         *     #item string payment_type     -cash or bank
         *     #item string transaction_id   -transaction id
         *}
         */
        savePayment: function (data, inputData) {
            return rpcService.request('contract-add-payment', 'savePayment', arguments);
        },
        /**
         * Save natura payment
         * api-method saveNaturaPayment
         * @param array $data
         * {
         *     #item array owner_array with data for owner
         *     #item string year
         *     #item boolean payment_reduce
         * }
         * @param array $inputData
         * {
         *     #item boolean payment_type_money
         *     #item boolean payment_type_natura
         *     #item boolean payment_method_cash
         *     #item boolean payment_method_bank
         *     #item boolean payment_order
         *     #item boolean weighing_note
         *     #item string payment_amount
         *     #item string payment_date
         *     #item string payment_natura_type
         *     #item string payment_natura_price
         *     #item string payment_natura_amount
         *     #item string payment_recipient
         *     #item string payment_recipient_egn
         *     #item string payment_recipient_proxy
         *     #item string payment_bank_account
         * }
         * @return array|int    -If Integer the response is 0.
         *{
         *     #item string payment_type     -Cash or bank
         *     #item string transaction_id   -Transaction id
         *}
         */
        saveNaturaPayment: function (data, inputData) {
            return rpcService.request('contract-add-payment', 'saveNaturaPayment', arguments);
        },

        /**
         * Generate RKO numbers
         * @api-method generateRkoNumbers
         * @param array $data
         * {
         *     #item array owner_array with data for owner
         *     {
         *         #item string owner_id
         *         #item int contract_id
         *         #item string owner_area
         *         #item int unpaid_renta
         *         #item boolean is_heritor
         *     }
         *     #item string year
         *     #item boolean payment_reduce
         * }
         * @param array $inputData
         * {
         *     #item boolean payment_type_money
         *     #item boolean payment_type_natura
         *     #item boolean payment_method_cash
         *     #item boolean payment_method_bank
         *     #item boolean payment_order
         *     #item boolean weighing_note
         *     #item string payment_amount
         *     #item string payment_date
         *     #item string payment_natura_type
         *     #item string payment_natura_price
         *     #item string payment_natura_amount
         *     #item string payment_recipient
         *     #item string payment_recipient_egn
         *     #item string payment_recipient_proxy
         *     #item string payment_bank_account
         * }
         * @return array
         *{
         *     #item string -new rko id
         *}
        */
        generateRkoNumbers: function(data, inputData) {
            return rpcService.request('contract-add-payment', 'generateRkoNumbers', arguments);
        },

        /**
         * Generate new natura payment RKO numbers
         * @api-method generateRkoNatNumbers
         * @param array $data
         * {
         *     #item array owner_array with data for owner
         *     #item string year
         *     #item boolean payment_reduce
         * }
         * @param array $inputData
         * {
         *     #item boolean payment_type_money
         *     #item boolean payment_type_natura
         *     #item boolean payment_method_cash
         *     #item boolean payment_method_bank
         *     #item boolean payment_order
         *     #item boolean weighing_note
         *     #item string payment_amount
         *     #item string payment_date
         *     #item string payment_natura_type
         *     #item string payment_natura_price
         *     #item string payment_natura_amount
         *     #item string payment_recipient
         *     #item string payment_recipient_egn
         *     #item string payment_recipient_proxy
         *     #item string payment_bank_account
         * }
         * @return array
         *{
         *     #item string -new rko id
         *}
         */
        generateRkoNatNumbers: function(data, inputData) {
            return rpcService.request('contract-add-payment', 'generateRkoNatNumbers', arguments);
        },
        /**
         * Set the number of the last payment to in order to start new numbering sequence
         * @param array $rpcParams 
         * @return void
         */
        setRkoNumberingStart: function (rpcParams) {
            return rpcService.request('contract-add-payment', 'setRkoNumberingStart', arguments);
        },
        /**
         * Return the last payment number
         * @return string
         */
        getLastNumber: function () {
            return rpcService.request('contract-add-payment', 'getLastNumber', arguments);
        },
         /**
         * Save personal use payment
         * api-method savePersonalUsePayment
         * @param array $data
         * {
         *     #item array owner_array with data for owner
         *     #item string year
         *     #item boolean payment_reduce
         * }
         * @param array $inputData
         * {
         *     #item boolean payment_type_money
         *     #item boolean payment_type_natura
         *     #item boolean payment_method_cash
         *     #item boolean payment_method_bank
         *     #item boolean payment_order
         *     #item boolean weighing_note
         *     #item string payment_amount
         *     #item string payment_date
         *     #item string payment_natura_type
         *     #item string payment_natura_price
         *     #item string payment_natura_amount
         *     #item string payment_recipient
         *     #item string payment_recipient_egn
         *     #item string payment_recipient_proxy
         *     #item string payment_bank_account
         * }
         * @return array|int    -If Integer the response is 0.
         *{
         *     #item string payment_type     -Cash or bank
         *     #item string transaction_id   -Transaction id
         *}
         */
         savePersonalUsePayment: function (data, inputData) {
            return rpcService.request('contract-add-payment', 'savePersonalUsePayment', arguments);
        },
    }

}));
