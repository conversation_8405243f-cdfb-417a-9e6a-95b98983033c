
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Payments/Payments'], factory);
    } else {
        Namespace('TF.Rpc.Payments');
        // Browser globals (root is window)
        root.TF.Rpc.Payments.ExportPayment = factory(TF.Rpc.Payments);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {       
        /**
         * Export payment order to pdf 
         * api-method exportToPdfPaymentOrder
         * @param  string $transactionId    -Transaction id 
         * @param  string $paymentId        -Default null cause it only use by export in transactions
         * @param  boolean $transactionDate -Print with date
         * @return string pdfPath           -Path to the file to pdf export
         */
        exportToPdfPaymentOrder: function (transactionId, paymentId) {
            return rpcService.request('export-payment', 'exportToPdfPaymentOrder', arguments);
        },
        /**
         * Export payment order with added iban to pdf 
         * api-method exportToPdfBankPaymentOrder
         * @param  string $transactionId   -Transaction id
         * @param  boolean $transactionDate -Print with date
         * @return string pdfPath          -Path to the file to pdf export
         */
        exportToPdfBankPaymentOrder: function (transactionId) {
            return rpcService.request('export-payment', 'exportToPdfBankPaymentOrder', arguments);
        },

        /**
         * Export post payment order to pdf 
         * api-method exportToPdfPostPaymentOrder
         * @param  string $transactionId   -Transaction id
         * @param  boolean $transactionDate -Print with date
         * @return string pdfPath          -Path to the file to pdf export
         */
        exportToPdfPostPaymentOrder: function () {
            return rpcService.request('export-payment', 'exportToPdfPostPaymentOrder', arguments);
        },

        /**
         * Export weighing note to pdf 
         * api-method exportToPdfWeighingNote
         * @param  string $transactionId    -Transaction id
         * @param  boolean $transactionDate -Print with date
         * @return string pdfPath           -Path to the file to pdf export
         */
        exportToPdfWeighingNote: function (transactionId) {
            return rpcService.request('export-payment', 'exportToPdfWeighingNote', arguments);
        },
        /**
         * Export weighing note and payment order to pdf 
         * api-method exportToPdfWeighingNote
         * @param  string $transactionId    -Transaction id
         * @param  boolean $transactionDate -Print with date
         * @return string pdfPath           -Path to the file to pdf export
         */
        exportToPdfCombined: function (transactionId) {
            return rpcService.request('export-payment', 'exportToPdfCombined', arguments);
        },
        /**
         * Export weighing note and payment order to pdf 
         * api-method exportToPdfWeighingNote
         * @param  string $transactionId    -Transaction id
         * @param  boolean $transactionDate -Print with date
         * @return string pdfPath           -Path to the file to pdf export
         */
        exportToPdfBankCombined: function (transactionId) {
            return rpcService.request('export-payment', 'exportToPdfBankCombined', arguments);
        },
       /**
         * Get the numbers of the payments, associated with the selected transaction 
         * api-method getTransactionPaymentNumbers
         * @param  string $transactionId    -Transaction id
         * @return array 
         *         [
         *             #item integer id - payment id
         *             #item string rko_number - rko number
         *         ]
         */
        getTransactionPaymentNumbers: function (transactionId) {
            return rpcService.request('export-payment', 'getTransactionPaymentNumbers', arguments);
        },
       /**
         * Get the numbers of the payments, associated with the selected transaction 
         * api-method getTransactionPaymentNumbers
         * @param  string $transactionId    -Transaction id
         * @return array 
         *         [
         *             #item integer id - payment id
         *             #item string rko_number - rko number
         *         ]
         */
        getNewTransactionRkoNumbers: function (transactionId) {
            return rpcService.request('export-payment', 'getNewTransactionRkoNumbers', arguments);
        },
       /**
         * Get the numbers of the payments, associated with the selected transaction 
         * api-method getTransactionPaymentNumbers
         * @param  array $data    
         *    [
         *         #item array
         *            [
         *                #item integer id
         *                #item string rko_number
         *            ]
         *    ]
         * @return boolean
         */
        updatePaymentRkoNumbers: function (data) {
            return rpcService.request('export-payment', 'updatePaymentRkoNumbers', arguments);
        }
    }

}));
