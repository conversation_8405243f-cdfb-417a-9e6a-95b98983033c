
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Payments/Payments'], factory);
    } else {
        Namespace('TF.Rpc.Payments');
        // Browser globals (root is window)
        root.TF.Rpc.Payments.PaymentsBankPaymentReport = factory(TF.Rpc.Payments);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return { 
		/**
	     * Метод за попълване на грида
	     * @param  array $rpcParams
	     *         {
	     *         		#item integer pbb_year
	     *         		#item date pbb_date_from
	     *         		#item date pbb_date_to
	     *         		#item boolean pbb_payment_type
	     *         		#item integer pbb_renta_type
	     *         		#item string pbb_paid_to
	     *         		#item string pbb_paid_by
	     *         		#item string pbb_bank_acc
	     *         }
	     * @param  integer $page      pagination parameter
	     * @param  integer $rows      pagination parameter
	     * @param  string  $sort      pagination parameter
	     * @param  string  $order     pagination parameter
	     * @return array 
	     *         {
	     *         		#item array rows
	     *         			{
	     *         				#item double amount
	     *         				#item string bank_acc
	     *         				#item boolean bank_payment
	     *         				#item date date
	     *         				#item string farming_year
	     *         				#item string full_renta
	     *         				#item double nat_amount
	     *         				#item string nat_name
	     *         				#item integer nat_type
	     *         				#item string nat_unit
	     *         				#item integer paid_from
	     *         				#item integer paid_in
	     *         				#item string payer_name
	     *         				#item string payment_type
	     *         				#item string recipient
	     *         				#item double renta_value
	     *         				#item integer transaction_id
	     *         			}
	     *         		#item integer total
	     *         		#item array footer
	     *         			{
	     *         				#item array 0
	     *         					{
	     *         						#item string bank_acc
	     *         						#item double amount
	     *         						#item double nat_amount
	     *         						#item string full_renta
	     *         					}
	     *         				#item array 1
	     *         					{
	     *         						#item string bank_acc
	     *         						#item double amount
	     *         						#item double nat_amount
	     *         						#item string full_renta
	     *         					}
	     *         			}
	     *         }
	     */
        read: function () {
            return rpcService.request('payments-bank-payment-report', 'read', arguments);
        },
    	/**
	     * Метод за export
	     * @param  array $rpcParams
	     *         {
	     *         		#item integer pbb_year
	     *         		#item date pbb_date_from
	     *         		#item date pbb_date_to
	     *         		#item boolean pbb_payment_type
	     *         		#item integer pbb_renta_type
	     *         		#item string pbb_paid_to
	     *         		#item string pbb_paid_by
	     *         		#item string pbb_bank_acc
	     *         }
	     * @param  integer $page      pagination parameter
	     * @param  integer $rows      pagination parameter
	     * @param  string  $sort      pagination parameter
	     * @param  string  $order     pagination parameter
	     * @return array 
	     *         {
	     *         		#item string path
	     *         		#item string name
	     *         }
	     */
        export: function () {
            return rpcService.request('payments-bank-payment-report', 'export', arguments);
        },
    	/**
	     * Метод за изтриване на експортнатия файл от сървъра
	     * @param string fileName
	     */
        delete: function () {
            return rpcService.request('payments-bank-payment-report', 'delete', arguments);
        }
    }

}));
