
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Payments/Payments'], factory);
    } else {
        Namespace('TF.Rpc.Payments');
        // Browser globals (root is window)
        root.TF.Rpc.Payments.ChargedRentaHistoryGrid = factory(TF.Rpc.Payments);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {       
        /**
	     * Read charged renta history
	     * api-method read
	     * @param string $paramsId      -Charged renta history id
	     * @param integer $page         -The current page number.
	     * @param integer $rows         -Rows per page.
	     * @param string $sort          -A grid column by which the grid is sorted.
	     * @param string $order         -The sort order ASC/DESC
	     * @return array
	     *   #item string total count
	     *   #item array rows
	     */
        readChargedRentaHistory: function (params_id, page, rows, sort, order) {
            return rpcService.request('charged-renta-history-grid', 'read', arguments);
        },
        /**
	     * Export to Excel charged renta history
	     * api-method expChargedRentaHistory
	     * @param  string $exportType    -Export type
	     * @param  string $paramsId      -Charged renta history id
	     * @param  string $type          -Charged renta history name
	     * @return string                -Path to excel file
	     */
        expChargedRentaHistory: function (exportType, paramsId, type) {
            return rpcService.request('charged-renta-history-grid', 'expChargedRentaHistory', arguments);
        }
    }

}));
