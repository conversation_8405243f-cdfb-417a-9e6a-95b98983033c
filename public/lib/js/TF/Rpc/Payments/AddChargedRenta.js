
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Payments/Payments'], factory);
    } else {
        Namespace('TF.Rpc.Payments');
        // Browser globals (root is window)
        root.TF.Rpc.Payments.AddChargedRenta = factory(TF.Rpc.Payments);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {       
    	/**
		* Save charged renta
	    * @api-method saveChargedRenta
	    * @param array $data
	    * {
	    *     #item array data
	    *     {
	    *         #item string year
	    *         #item string ekate
	    *         #item string masiv
	    *         #item string number
	    *         #item string acr_type
	    *         #item array category
	    *         #item string cnum
	    *         #item string contract_type
	    *         #item string farming
	    *         #item string contract_natura
	    *         #item array owner_ids
	    *         #item string owner_egn
	    *         #item array rep_ids
	    *         #item string rep_egn
	    *         #item array company_ids
	    *         #item string company_eik
	    *         #item string renta
	    *         #item int renta_nat
	    *         #item string natura
	    *         #item bool nat_id_converted
	    *         #item array multirents
	    *         {
	    *         	   #item string renta_nat_type
	    *         	   #item string renta_value
		* 			   #item string price_per_unit
	    *         	   #item bool is_converted
	    *         }
	    *     }
	    * }
		* @return void
		*/
        saveChargedRenta: function (data) {
            return rpcService.request('add-charged-renta', 'saveChargedRenta', arguments);
        },
		prevChargedRentaExport: function (data) {
			return rpcService.request('add-charged-renta', 'prevChargedRentaExport', arguments);
		}
    }

}));
