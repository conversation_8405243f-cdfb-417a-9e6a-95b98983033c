
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Payments/Payments'], factory);
    } else {
        Namespace('TF.Rpc.Payments');
        // Browser globals (root is window)
        root.TF.Rpc.Payments.PaymentsReportsRentaNaturaGrid = factory(TF.Rpc.Payments);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return { 
        /**
	     * Read payments reports renta natura grid
	     * api-method read
	     * @param array $data
	     * {
	     *     #item string report_type      -The payment report type
	     *     #item array contracts         -Only for detailed report renta natura.
	     *     #item string nat_type         -Only for detailed report renta natura.
	     *     #item array year              -Only for summary report renta natura.
	     *     #item array renta_nat_type    -Only for summary report renta natura.
	     *     #item array ekate             -For detailed and summary report renta natura.
	     *     #item array farming           -Only for summary report renta natura.
	     *     #item string contract_type    -Only for summary report renta natura.
	     *     #item string area_type        -Only for summary report renta natura.
	     * }
	     * @param  integer $page             -The current page number.
	     * @param  integer $rows             -Rows per page.
	     * @param  string $sort              -A grid column by which the grid is sorted.
	     * @param  string $order             -The sort order ASC/DESC.
	     * @return array result
	     * {
	     *     #item array rows              -The results.
	     *     #item string total            -The count of all results .
	     *     #item array footer            -The footer results.
	     * }        
	     */
        readPaymentsReportsRentaNaturaGrid: function (data, page, rows, sort, order) {
            return rpcService.request('payments-reports-renta-natura-grid', 'read', arguments);
        }
    }

}));
