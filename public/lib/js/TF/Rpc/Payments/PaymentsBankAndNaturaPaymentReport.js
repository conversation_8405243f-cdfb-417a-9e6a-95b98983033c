
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Payments/Payments'], factory);
    } else {
        Namespace('TF.Rpc.Payments');
        // Browser globals (root is window)
        root.TF.Rpc.Payments.PaymentsBankAndNaturaPaymentReport = factory(TF.Rpc.Payments);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {
		/**
	     * Метод за попълване на грида
	     * @param  array $rpcParams
	     * @param  integer $page      pagination parameter
	     * @param  integer $rows      pagination parameter
	     * @param  string  $sort      pagination parameter
	     * @param  string  $order     pagination parameter
	     * @return array
	     */
        read: function () {
            return rpcService.request('payments-bank-and-natura-payment-report', 'read', arguments);
        },
    	/**
	     * Метод за export
	     * @param  array $rpcParams
	     * @param  integer $page      pagination parameter
	     * @param  integer $rows      pagination parameter
	     * @param  string  $sort      pagination parameter
	     * @param  string  $order     pagination parameter
	     * @return array 
	     *         {
	     *         		#item string path
	     *         		#item string name
	     *         }
	     */
        export: function () {
            return rpcService.request('payments-bank-and-natura-payment-report', 'export', arguments);
        },
    	/**
	     * Метод за изтриване на експортнатия файл от сървъра
	     * @param string fileName
	     */
        delete: function () {
            return rpcService.request('payments-bank-and-natura-payment-report', 'delete', arguments);
        }
    }

}));
