
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Payments/Payments'], factory);
    } else {
        Namespace('TF.Rpc.Payments');
        // Browser globals (root is window)
        root.TF.Rpc.Payments.PersonalUseGrid = factory(TF.Rpc.Payments);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {     
		/**
		 * Read personal use grid
		 * api-method read
		 * @param  int $contractId          -The contract id for personal use.
		 * @param  int $year                -The year for personal use.
		 * @param  integer $page            -The current page number.
		 * @param  integer $rows            -Rows per page.
		 * @param  string $sort             -A grid column by which the grid is sorted.
		 * @param  string $order            -The sort order ASC/DESC
		 * @return array result
		 * {
		 *     #item array rows             -The results
		 *     #item string total           -The count of all results
		 * }
		 */
		readPersonalUseGrid: function (contractId, page, rows, sort, order) {
			return rpcService.request('personal-use-grid', 'read', arguments);
		},

	    /**
	     * Read personal use grid
	     * api-method read
	     * @param  int $contractId          -The contract id for personal use.
	     * @param  int $year                -The year for personal use.
	     * @param  integer $page            -The current page number.
	     * @param  integer $rows            -Rows per page.
	     * @param  string $sort             -A grid column by which the grid is sorted.
	     * @param  string $order            -The sort order ASC/DESC
	     * @return array result
	     * {
	     *     #item array rows             -The results
	     *     #item string total           -The count of all results 
	     * }        
	     */
        readPersonalUseOwners: function (contractId, year, page, rows, sort, order) {
            return rpcService.request('personal-use-grid', 'readPersonalUseOwners', arguments);
        },
        /**
	     * Save personal use
	     * api-method savePersonalUse
	     * @param  array $data
	     *  {
	     *     #item int contract_id           -The contract id to save personal use
	     *     #item int owner_id              -The owner id to save personal use
	     *     #item string personal_use       -The personal use amount
	     *     #item string renta              -The renta for personal use
	     *     #item string renta_nat          -The natura for personal use
	     *     #item int renta_nat_type        -The type of natura for personal use
	     *     #item string year               -The year for personal use
	     *  }
	     * @return void       
	     */
	    savePersonalUse: function (data) {
            return rpcService.request('personal-use-grid', 'savePersonalUse', arguments);
        },

		deletePersonalUse: function (data) {
			return rpcService.request('personal-use-grid', 'deletePersonalUse', arguments);
		},
		
        getPersonalUseData: function (data) {
            return rpcService.request('personal-use-grid', 'getPersonalUseData', arguments);
        }
    }

}));
