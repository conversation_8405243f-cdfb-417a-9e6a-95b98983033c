
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Payments/Payments'], factory);
    } else {
        Namespace('TF.Rpc.Payments');
        // Browser globals (root is window)
        root.TF.Rpc.Payments.PaymentsReportsByDateGridExportAndPrint = factory(TF.Rpc.Payments);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return { 
        /**
	     * Print payments reports by data
	     * api-method printPaymentsReportsByData
	      * @param array $data
	     * {
	     *     #item string report_type
	     *     #item array year
	     *     #item array ekate             -Use only for filter.
	     *     #item string contract_type    -Use only for filter.
	     *     #item array farming           -Use only for filter.
	     *     #item string date_from        -Use only for filter.
	     *     #item string date_to          -Use only for filter.
	     * }
	     * @return array result
	     * {
	     *     #item array rows       -The results.
	     *     #item string total     -The count of all results.
	     *     #item array footer     -The footer results.
	     *     {
	     *         #item string ekate             -Ekate
	     *         #item string paid_renta        -The paid renta
	     *         #item string unpaid_renta      -The unpaid renta
	     *         #item string paid_renta_nat    -The paid renta natura
	     *     }
	     * }        
	     */
        printPaymentsReportsByData: function (data) {
            return rpcService.request('payments-reports-by-date-grid-export-print', 'printPaymentsReportsByData', arguments);
        },
        /**
	     * Export to excel payments reports by data
	     * api-method exportToExcelPaymentsReportsByData
	     * @param array $data                -Data for export to excel
	     * {
	     *     #item string report_type
	     *     #item array year
	     *     #item array ekate             -Use only for filter.
	     *     #item string contract_type    -Use only for filter.
	     *     #item array farming           -Use only for filter.
	     *     #item string date_from        -Use only for filter.
	     *     #item string date_to          -Use only for filter.
	     * }        
	     * @return string filePath           -Path to the file to excel export.
	     */
        exportToExcelPaymentsReportsByData: function (data) {
            return rpcService.request('payments-reports-by-date-grid-export-print', 'exportToExcelPaymentsReportsByData', arguments);
        },
        /**
	     * Export to excel detailed report renta by Date
	     * api-method exportToExcelDetailedReportRentaByDate
	     * @param array $data              - data for export to excel
	     * {
	     *     #item string report_type    -The report type
	     *     #item array contracts       -Array with contracts
	     *     #item string date_compare   -The date compare
	     *     #item string ekate          -Ekate
	     *     #item string farming_id     -The farming id            
	     *     #item int year              -The year
	     *     #item string sort           -A grid column by which the grid is sorted.                    
	     *     #item string order          -The sort order ASC/DESC.                 
	     * }        
	     * @return string filePath         - path to the file to excel export
	     */
        exportToExcelDetailedReportRentaByDate: function (data) {
            return rpcService.request('payments-reports-by-date-grid-export-print', 'exportToExcelDetailedReportRentaByDate', arguments);
        }
    }

}));
