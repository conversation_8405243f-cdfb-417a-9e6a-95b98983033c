
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Payments/Payments'], factory);
    } else {
        Namespace('TF.Rpc.Payments');
        // Browser globals (root is window)
        root.TF.Rpc.Payments.InfoSummaryReportByEkateMoney = factory(TF.Rpc.Payments);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {       
        /**
	     * Load Status and Date from Memcached
	     * api-method loadInfo
	     * @param  int $reportId    -The report id
	     * @return array result
	     * {
	     *   #item string status    -The status
	     *   #item string date      -The date
	     * }
	     */
        loadInfo: function (reportId) {
            return rpcService.request('info-summary-report-by-ekate-money', 'loadInfo', arguments);
        },
        /**
	     * Deletes memcached data
	     * api-method refreshReport
	     * @param  int $reportId    -The report id
	     * @return boolean          -True
	     */
        refreshReport: function (reportId) {
            return rpcService.request('info-summary-report-by-ekate-money', 'refreshReport', arguments);
        }
    }

}));
