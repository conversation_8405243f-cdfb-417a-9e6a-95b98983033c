
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Payments/Payments'], factory);
    } else {
        Namespace('TF.Rpc.Payments');
        // Browser globals (root is window)
        root.TF.Rpc.Payments.PaymentsReportsGridExportAndPrint = factory(TF.Rpc.Payments);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {
        /**
	     * Print payments reports by data
	     * api-method printPaymentsSummaryReports
	     * @param array $data
	     * {
	     *     #item string report_type
	     *     #item array year
	     *     #item array ekate             -Use only for filter.
	     *     #item string area_type        -Use only for filter.
	     *     #item string contract_type    -Use only for filter.
	     *     #item array farming           -Use only for filter.
	     *     #item boolean filter_clicked  -Use only for filter.
	     *     #item string sort             -Use only for filter.
	     *     #item string order            -Use only for filter.
	     * }
	     * @return array result
	     * {
	     *     #item array rows              -The results.
	     *     #item string total            -The count of all results .
	     *     #item array footer            -The footer results.
	     *     {
	     *         #item string ekate
	     *         #item string renta
	     *         #item string charged_renta
	     *         #item string paid_renta
	     *         #item string unpaid_renta
	     *         #item string overpaid
	     *     }
	     * }
	     */
        printPaymentsSummaryReports: function (data) {
            return rpcService.request('payments-reports-grid-export-print', 'printPaymentsSummaryReports', arguments);
        },
        /**
	     * Export to excel payments summary reports
	     * api-method exportToExcelPaymentsSummaryReports
	     * @param array $data           - data for export to excel
	     * {
	     *     #item string report_type
	     *     #item array year
	     *     #item array ekate             -Use only for filter.
	     *     #item string area_type        -Use only for filter.
	     *     #item string contract_type    -Use only for filter.
	     *     #item array farming           -Use only for filter.
	     *     #item boolean filter_clicked  -Use only for filter.
	     *     #item string sort             -Use only for filter.
	     *     #item string order            -Use only for filter.
	     * }        
	     * @return string filePath      - path to the file to excel export
	     */
        exportToExcelPaymentsSummaryReports: function (data) {
            return rpcService.request('payments-reports-grid-export-print', 'exportToExcelPaymentsSummaryReports', arguments);
        }
    }

}));
