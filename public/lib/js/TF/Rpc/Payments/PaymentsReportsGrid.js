
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Payments/Payments'], factory);
    } else {
        Namespace('TF.Rpc.Payments');
        // Browser globals (root is window)
        root.TF.Rpc.Payments.PaymentsReportsGrid = factory(TF.Rpc.Payments);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return { 
        /**
	     * Read payments reports grid
	     * api-method read
	     * @param array $data
	     * {
	     *     #item string type               -The payment report type
	     *     #item boolean is_heritor        -The user is heritor or not
	     *     #item string payroll_from_date  -The payroll from date
	     *     #item string payroll_to_date    -The payroll to date
	     *     #item string payroll_ekate      -The payroll ekate
	     *     #item string farming_year       -The farming year
	     *     #item int owner_id              -The owner id
	     * }
	     * @param  integer $page        -The current page number.
	     * @param  integer $rows        -Rows per page.
	     * @param  string $sort         -A grid column by which the grid is sorted.
	     * @param  string $order        -The sort order ASC/DESC
	     * @return array result
	     * {
		 *     #item array rows         -The results
		 *     #item string total       -The count of all results 
		 *     #item array footer       -The footer results
	     * }        
	     */
        readPaymentsReportsGrid: function (data, page, rows, sort, order) {
            return rpcService.request('payments-reports-grid', 'read', arguments);
        }
    }

}));
