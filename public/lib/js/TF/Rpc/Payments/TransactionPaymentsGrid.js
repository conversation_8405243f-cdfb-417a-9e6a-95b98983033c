
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Payments/Payments'], factory);
    } else {
        Namespace('TF.Rpc.Payments');
        // Browser globals (root is window)
        root.TF.Rpc.Payments.TransactionPaymentsGrid = factory(TF.Rpc.Payments);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {       
	    /**
	     * Read transaction payments grid
	     * api-method read
	     * @param string transactionId       -The Transaction id
	 	 * @param  integer $page             -The current page number.
	     * @param  integer $rows             -Rows per page.
	     * @param  string $sort              -A grid column by which the grid is sorted.
	     * @param  string $order             -The sort order ASC/DESC.
	     * @return array result
	     * {
	     *     #item array rows             -The results
	     *     #item string total           -The count of all results 
	     * }        
	     */
        readTransactionPaymentsGrid: function (transactionId, page, rows, sort, order) {
            return rpcService.request('transactions-payments-grid', 'read', arguments);
        }
    }

}));
