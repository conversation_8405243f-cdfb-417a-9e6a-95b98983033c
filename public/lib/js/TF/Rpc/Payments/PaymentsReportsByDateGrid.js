
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Payments/Payments'], factory);
    } else {
        Namespace('TF.Rpc.Payments');
        // Browser globals (root is window)
        root.TF.Rpc.Payments.PaymentsReportsByDateGrid = factory(TF.Rpc.Payments);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return { 
         /**
	     * Read payments reports by date grid
	     * api-method read
	     * @param array $data
	     * {
	     *     #item string report_type
	     *     #item array year
	     *     #item array ekate             -Use only for filter.
	     *     #item string contract_type    -Use only for filter.
	     *     #item array farming           -Use only for filter.
	     *     #item string date_from        -Use only for filter.
	     *     #item string date_to          -Use only for filter.
	     * }
	     * @param  integer $page    -The current page number.
	     * @param  integer $rows    -Rows per page.
	     * @param  string $sort     -A grid column by which the grid is sorted.
	     * @param  string $order    -The sort order ASC/DESC.
	     * @return array result
	     * {
		 *     #item array rows     -The results.
		 *     #item string total   -The count all results.
		 *     #item array footer   -The footer results.
		 *     {
		 *         #item string contract_name   -The contract name
		 *         #item string area_size       -The area size
		 *         #item string paid_renta      -The paid renta
		 *     }
	     * }        
	     */
        readPaymentsReportsByDateGrid: function (data, page, rows, sort, order) {
            return rpcService.request('payments-reports-by-date-grid', 'read', arguments);
        }
    }

}));
