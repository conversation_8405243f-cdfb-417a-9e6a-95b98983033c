
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Payments/Payments'], factory);
    } else {
        Namespace('TF.Rpc.Payments');
        // Browser globals (root is window)
        root.TF.Rpc.Payments.PaymentsReportsRentaNaturaGridExportAndPrint = factory(TF.Rpc.Payments);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return { 
        /**
	     * Print payments reports renta natura grid
	     * api-method printPaymentsReportsRentaNaturaGrid
	     * @param array $data
	     * {
	     *     #item string report_type      -The payment report type
	     *     #item array year              -The year
	     *     #item array renta_nat_type    -Use only for filter.
	     *     #item array ekate             -Use only for filter.
	     *     #item array farming           -Use only for filter.
	     *     #item string contract_type    -Use only for filter.
	     *     #item string area_type        -Use only for filter.
	     *     #item string sort             -A grid column by which the grid is sorted.
	     *     #item string order            -The sort order ASC/DESC.
	     * }
	     * @return array result
	     * {
	     *     #item array rows              -The results.
	     *     #item string total            -The count of all results.
	     * }        
	     */
        printPaymentsReportsRentaNaturaGrid: function (data) {
            return rpcService.request('payments-reports-renta-natura-grid-export-print', 'printPaymentsReportsRentaNaturaGrid', arguments);
        },
        /**
	     * Export to excel payments reports renta natura grid
	     * api-method exportToExcelPaymentsReportsRentaNaturaGrid
	     * @param array $data
	     * {
	     *     #item string report_type      -The payment report type
	     *     #item array year              -The year
	     *     #item array renta_nat_type    -Use only for filter.
	     *     #item array ekate             -Use only for filter.
	     *     #item array farming           -Use only for filter.
	     *     #item string contract_type    -Use only for filter.
	     *     #item string area_type        -Use only for filter.
	     *     #item string sort             -A grid column by which the grid is sorted.
	     *     #item string order            -The sort order ASC/DESC.           
	     * }
	     * @return string filePath           -Path to the file to excel export.
	     */
        exportToExcelPaymentsReportsRentaNaturaGrid: function (data) {
            return rpcService.request('payments-reports-renta-natura-grid-export-print', 'exportToExcelPaymentsReportsRentaNaturaGrid', arguments);
        },
        /**
	    * Export to detailed excel payments reports renta natura grid
	    * api-method exportToDetailedExcelPaymentsReportsRentaNaturaGrid
	    * @param array $data
	    * {
	    *     #item string report_type      -The payment report type.
	    *     #item array contracts         -Array with contracts.
	    *     #item string nat_type         -Natura type.
	    *     #item string ekate            -Ekate.
	    *     #item string sort             -A grid column by which the grid is sorted.
	    *     #item string order            -The sort order ASC/DESC.           
	    * }
	    * @return string filePath           -Path to the file to excel export.
	    */
        exportToDetailedExcelPaymentsReportsRentaNaturaGrid: function (data) {
            return rpcService.request('payments-reports-renta-natura-grid-export-print', 'exportToDetailedExcelPaymentsReportsRentaNaturaGrid', arguments);
        }
    }

}));
