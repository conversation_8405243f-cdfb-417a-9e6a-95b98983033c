
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Payments/Payments'], factory);
    } else {
        Namespace('TF.Rpc.Payments');
        // Browser globals (root is window)
        root.TF.Rpc.Payments.WeighingNoteGrid = factory(TF.Rpc.Payments);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {       
	    /**
	     * Read personal use grid
	     * api-method read
	     * @param  int $contractId          -The contract id for personal use
	     * @param  int $year                -The year for personal use
	     * @param  integer $page            -The current page number.
	     * @param  integer $rows            -Rows per page.
	     * @param  string $sort             -A grid column by which the grid is sorted.
	     * @param  string $order            -The sort order ASC/DESC
	     * @return array result
	     * {
	     *     #item array rows             -The results
	     *     #item string total           -The count of all results  
	     * }        
	     */
        readWeighingNoteGrid: function (page, rows, sort, order) {
            return rpcService.request('weighing-note-grid', 'read', arguments);
        },
    }

}));
