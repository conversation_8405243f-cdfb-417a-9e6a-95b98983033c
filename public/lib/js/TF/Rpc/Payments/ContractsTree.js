
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Payments/Payments'], factory);
    } else {
        Namespace('TF.Rpc.Payments');
        // Browser globals (root is window)
        root.TF.Rpc.Payments.ContractsTree = factory(TF.Rpc.Payments);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {       
        /**
         * Get contracts
         * api-method read
         * @param array $filterParams
         * {
         *     #item string cnum          -The contract number
         *     #item string company_eik   -The company EIK
         *     #item string company_name  -The company name
         *     #item string heritor_egn   -The heritor EGN
         *     #item string heritor_name  -The heritor name
         *     #item string kad_ident     -Kadident(ekate.masiv.number)
         *     #item string masiv         -The masiv
         *     #item string number        -The number
         *     #item string owner_egn     -The owner EGN
         *     #item string owner_name    -The owner name
         *     #item string rep_egn       -The representative EGN
         *     #item string rep_name      -The representative name
         *     #item string year          -The year
         *     #item array area_type      -The area type
         *     #item array category       -The category
         *     #item array contract_type  -The contract type
         *     #item array ekate          -Ekate
         *     #item array farming        -Farming
         * }
         * @param integer $page         -The current page number.
         * @param integer $rows         -Rows per page.
         * @param string $sort          -A grid column by which the grid is sorted.
         * @param string $order         -The sort order ASC/DESC 
         * @return array
         */
        getContracts: function (filterObj, page, rows, sort, order) {
            return rpcService.request('payments-contracts-tree', 'read', arguments);
        }
    }

}));
