
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Payments/Payments'], factory);
    } else {
        Namespace('TF.Rpc.Payments');
        // Browser globals (root is window)
        root.TF.Rpc.Payments.TransactionsGrid = factory(TF.Rpc.Payments);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {       
	    /**
	     * Read transactions grid
	     * api-method read
	     * @param array $data
	     * {
	     *     #item string id                       -The transaction id
	     *     #item string transaction_start_date   -The transaction start date
	     *     #item string transaction_due_date     -The transaction due date
	     *     #item string transaction_type         -The transaction type
	     *     #item string paid_to                  -The transaction paid to
	     *     #item string paid_to_egn              -The transaction paid to egn
	     *     #item string paid_by                  -The transaction paid by
	     *     #item string farming_year             -The farming year
	     * }
	     * @param  integer $page           -The current page number.
	     * @param  integer $rows           -Rows per page.
	     * @param  string $sort            -A grid column by which the grid is sorted.
	     * @param  string $order           -The sort order ASC/DESC.
	     * @return array result
	     * {
	     *     #item array rows             -The results
	     *     #item string total           -The count of all results 
	     * }        
	     */
        readTransactionsGrid: function (data, page, rows, sort, order) {
            return rpcService.request('transactions-grid', 'read', arguments);
        },
        /**
	     * Export to excel transactions grid
	     * api-method exportToExcel
	     * @param array $dataRpc
	     * {
	     *     #item array $data
	     *     {
	     *         #item string id                       -The transaction id
	     *         #item string transaction_start_date   -The transaction start date
	     *         #item string transaction_due_date     -The transaction due date
	     *         #item string transaction_type         -The transaction type
	     *         #item string paid_to                  -The transaction paid to
	     *         #item string paid_to_egn              -The transaction paid to egn
	     *         #item string paid_by                  -The transaction paid by
	     *         #item string farming_year             -The farming year
	     *     }
	     *     #item string sort                         -A grid column by which the grid is sorted.
	     *     #item string order                        -The sort order ASC/DESC.
	     * }
	     * @return string filePath                       -Path to the file to excel export
	     */
        exportToExcelTransactionsGrid: function (dataRpc) {
            return rpcService.request('transactions-grid', 'exportToExcel', arguments);
        },
        disableTransaction: function (dataRpc) {
            return rpcService.request('transactions-grid', 'disableTransaction', arguments);
        }
    }

}));
