
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Payments/Payments'], factory);
    } else {
        Namespace('TF.Rpc.Payments');
        // Browser globals (root is window)
        root.TF.Rpc.Payments.ChargedRentaHistoryTree = factory(TF.Rpc.Payments);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {       
        /**
	     * Read charged renta history tree
	     * api-method read
	     * @param integer $page         -The current page number.
	     * @param integer $rows         -Rows per page.
	     * @param string $sort          -A grid column by which the grid is sorted.
	     * @param string $order         -The sort order ASC/DESC
	     * @return array
	     */
        readChargedRentaHistory: function (page, rows, sort, order) {
            return rpcService.request('charged-renta-history-tree', 'read', arguments);
        }
    }

}));
