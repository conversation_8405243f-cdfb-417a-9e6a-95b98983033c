
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Payroll/Payroll'], factory);
    } else {
        Namespace('TF.Rpc.Payroll');
        // Browser globals (root is window)
        root.TF.Rpc.Payroll.PayrollExportsGrid = factory(TF.Rpc.Payroll);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {
        /**
         * Export payroll grid to excel
         * @param array $data           -Data for export to excel
         * {
	     *     #item string type
	     *     #item string subtype
	     *     #item boolean is_heritor
	     *     #item string payroll_from_date
	     *     #item string payroll_to_date
	     *     #item string payroll_ekate
	     *     #item array payroll_farming
	     *     #item string farming_year
	     *     #item string owner_names
	     *     #item string rent_place
	     *     #item string egn
	     *     #item string eik
	     *     #item string company_name
	     *     #item string rep_names
	     *     #item string rep_egn
	     *     #item string rep_rent_place
	     *     #item string custom_columns
	     *     #item string print_export
	     *     #item string sort
	     *     #item string order
	     * }
         * @return string filePath      -Path to the file to excel export
         */
        read: function (data) {
            return rpcService.request('payroll-exports-grid', 'read', arguments);
        },
        export: function (data) {
            return rpcService.request('payroll-exports-grid', 'export', arguments);
        },
        delete: function (data) {
            return rpcService.request('payroll-exports-grid', 'delete', arguments);
        },
        exportMassPayment: function (data) {
            return rpcService.request('payroll-exports-grid', 'exportMassPayment', arguments);
        },
    }

}));
