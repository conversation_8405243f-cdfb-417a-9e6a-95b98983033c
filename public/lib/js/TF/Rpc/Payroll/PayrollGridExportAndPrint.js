
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Payroll/Payroll'], factory);
    } else {
        Namespace('TF.Rpc.Payroll');
        // Browser globals (root is window)
        root.TF.Rpc.Payroll.PayrollGridExportAndPrint = factory(TF.Rpc.Payroll);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {       
		/**
	     * Export payroll grid to excel
	     * @param array $data           -Data for export to excel
	     * {
	     *     #item string type
	     *     #item string subtype
	     *     #item boolean is_heritor
	     *     #item string payroll_from_date
	     *     #item string payroll_to_date
	     *     #item string payroll_ekate
	     *     #item array payroll_farming
	     *     #item string farming_year
	     *     #item string owner_names
	     *     #item string rent_place
	     *     #item string egn
	     *     #item string eik
	     *     #item string company_name
	     *     #item string rep_names
	     *     #item string rep_egn
	     *     #item string rep_rent_place
	     *     #item string custom_columns
	     *     #item string print_export
	     *     #item string sort
	     *     #item string order
	     * }        
	     * @return string filePath      -Path to the file to excel export
	     */
        exportToExcelPayrollGrid: function (data) {
            return rpcService.request('payroll-grid-export-print', 'exportToExcelPayrollGrid', arguments);
        },
    	 /**
	     * Print payroll grid
	     * @param array $data           -Data for export to excel
	     * {
	     *     #item string type
	     *     #item string subtype
	     *     #item boolean is_heritor
	     *     #item string payroll_from_date
	     *     #item string payroll_to_date
	     *     #item string payroll_ekate
	     *     #item array payroll_farming
	     *     #item string farming_year
	     *     #item string owner_names
	     *     #item string rent_place
	     *     #item string egn
	     *     #item string eik
	     *     #item string company_name
	     *     #item string rep_names
	     *     #item string rep_egn
	     *     #item string rep_rent_place
	     *     #item string custom_columns
	     *     #item string print_export
	     *     #item string sort
	     *     #item string order
	     * }
	     * @return array dataToPrint
	     */
        printPayrollGrid: function (data) {
            return rpcService.request('payroll-grid-export-print', 'printPayrollGrid', arguments);
        },
        /**
	     * Export to excel payroll plots
	     * @param array $data
	     * {
	     *     #item string type               -The payroll type.
	     *     #item boolean is_heritor        -User is heritor ot not.
	     *     #item string payroll_from_date  -The payroll from date.
	     *     #item string payroll_to_date    -The payroll to date.
	     *     #item string payroll_ekate      -The payroll ekate.
	     *     #item string farming_year       -The farming year.
	     *     #item int owner_id              -The owner id.
	     *     #item array all_renta           -Plots contract renta and charged renta
	     * }
	     * @param  integer $page               -The current page number.
	     * @param  integer $rows               -Rows per page.
	     * @param  string $sort                -A grid column by which the grid is sorted.
	     * @param  string $order               -The sort order ASC/DESC.
	     * @return string
	     */
        exportToExcelPayrollPlotsGrid: function (data) {
            return rpcService.request('payroll-grid-export-print', 'exportToExcelPayrollPlotsGrid', arguments);
        } 
    }

}));
