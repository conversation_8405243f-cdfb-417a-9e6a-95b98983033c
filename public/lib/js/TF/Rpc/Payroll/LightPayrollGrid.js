Namespace('TF.Rpc.Payroll');

TF.Rpc.Payroll.LightPayrollGrid = function (rpcService) {
    return {
        /**
         * Read payroll grid
         * api-method read
         * @param data array
         * {
         *     #item string type               -The payroll type.
         *     #item boolean is_heritor        -User is heritor ot not.
         *     #item string payroll_from_date  -The payroll from date.
         *     #item string payroll_to_date    -The payroll to date.
         *     #item string payroll_ekate      -The payroll ekate.
         *     #item string farming_year       -The farming year.
         *     #item int owner_id              -The owner id.
         *     #item int page                  - The page
         * }
         * @param  page integer             -The current page number.
         * @param  rows integer             -Rows per page.
         * @param  sort string               -A grid column by which the grid is sorted.
         * @param  order string              -The sort order ASC/DESC.
         * @return array result
         * {
         *     #item array rows                -The results.
         *     #item string total              -The count of all results.
         *     #item array footer              -The footer results.      
         * }
         */

        read: function (data, page, rows, sort, order) {
            return rpcService.request('light-payroll-grid', 'export', arguments);
        },

        export: function (data, page, rows, sort, order) {
            return rpcService.request('light-payroll-grid', 'export', arguments);
        },

        print: function (data, page, rows, sort, order) {
            return rpcService.request('light-payroll-grid', 'printReport', arguments);
        }
    }
}(TF.Rpc.Payroll);