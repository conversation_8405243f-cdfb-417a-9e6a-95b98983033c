(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/ExceptionsList'], factory);
    } else {
        Namespace('TF.Rpc.Exception');
        // Browser globals (root is window)
        root.TF.Rpc.Exception = factory(TF.Rpc.ExceptionsList);
    }
}(typeof self !== 'undefined' ? self : this, function (ExceptionsList) {
	/*Private properties*/
	var code,
		message,
		data,
		exceptionsByCode = {};

	setExceptionsByCode();
	
	function setExceptionsByCode () {
		for (prop in ExceptionsList) {
			excData = ExceptionsList[prop];
			exceptionsByCode[excData.code] = excData;
		}
	}

	function Exception (errorObj) {
		code = errorObj.code;
		message = errorObj.message;
		data = errorObj.data;
	}

	/**
	 * Check if this exceptions 
	 * @param  {object|TF.Rpc.Exception}  exceptionObj Compares two exceptions. If the exceptionObj 
	 *                                                 is a plain object it needs 'code' a property.
	 * @return {Boolean}
	 */
	Exception.prototype.is = function(exceptionObj) {
		return exceptionObj.code == code;
	};

	Exception.prototype.getMessage = function() {
		return exceptionsByCode[this.getCode()].message;
	};

	Exception.prototype.getCode = function() {
		return code;
	};

	Exception.prototype.getData = function() {
		return data;
	};

	Exception.prototype.getExceptionData = function() {
		return exceptionsByCode[this.getCode()];
	};

	Exception.prototype.getOriginalMessage = function() {
		return message;
	};

	return Exception;


}));