
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Isak/Isak'], factory);
    } else {
        Namespace('TF.Rpc.Isak');
        // Browser globals (root is window)
        root.TF.Rpc.Isak.IsakMaingrid = factory(TF.Rpc.Isak);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {       
        read: function () {
            return rpcService.request('isak-maingrid', 'read', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        }

}));
