
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/GlobalNotifications/GlobalNotifications'], factory);
    } else {
        Namespace('TF.Rpc.GlobalNotifications');
        // Browser globals (root is window)
        root.TF.Rpc.GlobalNotifications.GlobalNotificationsMainGrid = factory(TF.Rpc.GlobalNotifications);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {
        add: function () {
            return rpcService.request('global-notifications-grid', 'add', arguments);
        },
        getById: function () {
            return rpcService.request('global-notifications-grid', 'getById', arguments);
        },
        edit: function () {
            return rpcService.request('global-notifications-grid', 'edit', arguments);
        },
        getActiveNotClosedByUser: function () {
            return rpcService.request('global-notifications-grid', 'getActiveNotClosedByUser', arguments);
        },
        close: function () {
            return rpcService.request('global-notifications-grid', 'close', arguments);
        }
    }

}));
