
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Common/Common'], factory);
    } else {
        Namespace('TF.Rpc.Common');
        // Browser globals (root is window)
        root.TF.Rpc.Common.PaymentsRentPlacesCombobox = factory(TF.Rpc.Common);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {
    	/**
	     * Returns the places where the owners/representetives can receive their rent.
	     * api-method read
	     * @param  boolean $isRepresentative     -If true shows only the representatives place.
	     * @return array
	     * {
	     *     #item string text    -Rent place.
	     * }
	     */
        getRentPlaces: function () {
            return rpcService.request('payments-rent-places-combobox', 'read', arguments);
        }
    }

}));
