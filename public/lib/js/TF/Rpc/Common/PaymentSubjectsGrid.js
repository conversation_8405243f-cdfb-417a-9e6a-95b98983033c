
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Common/Common'], factory);
    } else {
        Namespace('TF.Rpc.Common');
        // Browser globals (root is window)
        root.TF.Rpc.Common.PaymentSubjectsGrid = factory(TF.Rpc.Common);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {
        /**
        * @api-method read
        * @return array
        */
        read: function () {
            return rpcService.request('payment-subjects-grid', 'read', arguments);
        },
        /**
        * Saves new payment subject in the database
        * @api-method add
        * @param  array  $rpcParams  - new payment subject params
        *                {
        *                    #item string shortname
        *                    #item string description
        *                }
        * @return integer
        */
        addPaymentSubject: function () {
            return rpcService.request('payment-subjects-grid', 'add', arguments);
        },
        /**
        * Edit payment subject in the database
        * @api-method edit
        * @param  array  $rpcParams  - new subject parameters
        *                {
        *                    #item integer id
        *                    #item string shortname
        *                    #item string description
        *                }
        * @return void
        */
        editPaymentSubject: function () {
            return rpcService.request('payment-subjects-grid', 'edit', arguments);
        },
        /**
        * Mark for edit payment subject
        * @api-method markForEdit
        * @param  integer id
        * @return void
        */
        markForEdit: function () {
            return rpcService.request('payment-subjects-grid', 'markForEdit', arguments);
        },
        /**
        * Delete payment subject
        * @api-method delete
        * @param  integer id
        * @return void
        */
        deletePaymentSubject: function () {
            return rpcService.request('payment-subjects-grid', 'delete', arguments);
        }
    };

}));
