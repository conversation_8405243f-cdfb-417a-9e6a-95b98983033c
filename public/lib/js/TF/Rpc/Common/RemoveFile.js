
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Common/Common'], factory);
    } else {
        Namespace('TF.Rpc.Common');
        // Browser globals (root is window)
        root.TF.Rpc.Common.RemoveFile = factory(TF.Rpc.Common);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {
    	/**
		 * remove file from the server
		 * api-method remove
		 * @param  string $path 
		 * @return void
		 */
        removeFileFromServer: function (path) {
            return rpcService.request('remove-file', 'remove', arguments);
        }
    }

}));
