
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Common/Common'], factory);
    } else {
        Namespace('TF.Rpc.Common');
        // Browser globals (root is window)
        root.TF.Rpc.Common.CancelledByCombobox = factory(TF.Rpc.Common);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

		return {
		/**
		 * get usernames of the accounts who cancelled transactions.
		 * api-method read
		 * @return {array} $cancelledBy - all usernames who cancelled a transaction.
		 */
		getCancelledByCombobox: function() {
		return rpcService.request("cancelled-by-combobox", "read", arguments);
		}
	};

}));
