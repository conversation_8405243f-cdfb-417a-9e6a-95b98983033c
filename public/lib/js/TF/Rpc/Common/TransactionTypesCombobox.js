
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Common/Common'], factory);
    } else {
        Namespace('TF.Rpc.Common');
        // Browser globals (root is window)
        root.TF.Rpc.Common.TransactionTypesCombobox = factory(TF.Rpc.Common);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {
    	/**
		 * get Transaction Types Combobox
	     * api-method read
		 * @return array $return
		 * {
		 *     #item string id      - id of transaction type
		 *     #item string name    - name of transaction type
	     * }  
		 */
        getTransactionTypesCombobox: function () {
            return rpcService.request('transaction-types-combobox', 'read', arguments);
        }
    }

}));
