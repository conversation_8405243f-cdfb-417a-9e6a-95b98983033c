
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Common/Common'], factory);
    } else {
        Namespace('TF.Rpc.Common');
        // Browser globals (root is window)
        root.TF.Rpc.Common.MestnostCombobox = factory(TF.Rpc.Common);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {
    	/**
		 * get mestnost names combobox
	     * api-method read
		 * @return array $mestnost - all names of mestnost
		 */
        read: function () {
            return rpcService.request('mestnost-combobox', 'read', arguments);
        }
    }

}));
