
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Common/Common'], factory);
    } else {
        Namespace('TF.Rpc.Common');
        // Browser globals (root is window)
        root.TF.Rpc.Common.RentaTypeGrid = factory(TF.Rpc.Common);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {
    	/**
	     * @api-method read
	     * @return array
	     */
        read: function () {
            return rpcService.request('renta-type-grid', 'read', arguments);
        },
         /**
	     * Saves new renta in the database
	     * @api-method add
	     * @param  array  $rpcParams  - new renta parameters
	     *                {
	     *                    #item string renta_type_name
	     *                    #item string renta_type_unit
	     *                    #item string renta_type_unit_value
	     *                }
	     * @return void|array
	     * {
	     *     #item string message       -error message
	     * }
	     */
        addRenta: function() {
        	return rpcService.request('renta-type-grid', 'add', arguments);
        },
        /**
	     * Edit renta type in the database
	     * @api-method edit
	     * @param  array  $rpcParams  - new renta parameters
	     *                {
	     *                    #item string unit_value
	     *                    #item int renta_type_id
	     *                }
	     * @return void
	     */
        editRenta: function() {
        	return rpcService.request('renta-type-grid', 'edit', arguments);
        }
    }

}));
