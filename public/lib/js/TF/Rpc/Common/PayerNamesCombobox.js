
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Common/Common'], factory);
    } else {
        Namespace('TF.Rpc.Common');
        // Browser globals (root is window)
        root.TF.Rpc.Common.PayerNamesCombobox = factory(TF.Rpc.Common);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {
    	/**
		 * get payer names combobox
	     * api-method read
		 * @return array $payers - all names of payers
		 */
        getPayerNamesCombobox: function () {
            return rpcService.request('payer-names-combobox', 'read', arguments);
        }
    }

}));
