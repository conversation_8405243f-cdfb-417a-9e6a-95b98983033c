
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Common/Common'], factory);
    } else {
        Namespace('TF.Rpc.Common');
        // Browser globals (root is window)
        root.TF.Rpc.Common.LabelNamesCombobox = factory(TF.Rpc.Common);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {
    	/**
		 * get label names combobox
	     * api-method read
		 * @return array
		 */
        read: function () {
            return rpcService.request('label-names-combobox', 'read', arguments);
        }
    }

}));
