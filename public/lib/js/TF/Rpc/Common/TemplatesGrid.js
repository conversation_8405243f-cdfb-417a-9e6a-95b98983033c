
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Common/Common'], factory);
    } else {
        Namespace('TF.Rpc.Common');
        // Browser globals (root is window)
        root.TF.Rpc.Common.TemplatesGrid = factory(TF.Rpc.Common);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {
        read: function () {
            return rpcService.request('templates-grid', 'read', arguments);
        },
        add: function() {
        	return rpcService.request('templates-grid', 'add', arguments);
        },
        markForEdit: function() {
        	return rpcService.request('templates-grid', 'markForEdit', arguments);
        },
        edit: function() {
        	return rpcService.request('templates-grid', 'editTemplate', arguments);
        },
        delete: function() {
        	return rpcService.request('templates-grid', 'deleteTemplate', arguments);
        }
    }

}));
