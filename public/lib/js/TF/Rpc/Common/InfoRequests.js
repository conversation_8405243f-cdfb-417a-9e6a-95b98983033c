
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Common/Common'], factory);
    } else {
        Namespace('TF.Rpc.Common');
        // Browser globals (root is window)
        root.TF.Rpc.Common.InfoRequest = factory(TF.Rpc.Common);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {
        ownerInfo: function () {
            return rpcService.request('owners-info', 'read', arguments);
        },
        plotInfo: function() {
        	return rpcService.request('plot-info', 'read', arguments);
        },
        contractInfo: function() {
        	return rpcService.request('contract-info', 'read', arguments);
        }
    }

}));
