
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/CropRotation/CropRotation'], factory);
    } else {
        Namespace('TF.Rpc.CropRotation');
        // Browser globals (root is window)
        root.TF.Rpc.CropRotation.SoilSampleFilesTree = factory(TF.Rpc.CropRotation);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {     
        read: function () {
            return rpcService.request('sample-files-tree', 'read', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },     
        applyFile: function () {
            return rpcService.request('sample-files-tree', 'applyFile', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },     
        editRow: function () {
            return rpcService.request('sample-files-tree', 'editRow', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },     
        deleteSoilSampleFile: function () {
            return rpcService.request('sample-files-tree', 'deleteSoilSampleFile', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        }
    }

}));
