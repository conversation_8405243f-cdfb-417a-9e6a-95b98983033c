
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/CropRotation/CropRotation'], factory);
    } else {
        Namespace('TF.Rpc.CropRotation');
        // Browser globals (root is window)
        root.TF.Rpc.CropRotation.CropLayersTree = factory(TF.Rpc.CropRotation);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {     
        read: function () {
            return rpcService.request('crop-layers-tree', 'read', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },       
        add: function () {
            return rpcService.request('crop-layers-tree', 'add', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },       
        rename: function () {
            return rpcService.request('crop-layers-tree', 'rename', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },       
        delete: function () {
            return rpcService.request('crop-layers-tree', 'delete', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        }
    }

}));
