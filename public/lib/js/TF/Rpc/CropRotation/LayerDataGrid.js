
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/CropRotation/CropRotation'], factory);
    } else {
        Namespace('TF.Rpc.CropRotation');
        // Browser globals (root is window)
        root.TF.Rpc.CropRotation.LayerDataGrid = factory(TF.Rpc.CropRotation);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {     
        read: function () {
            return rpcService.request('layer-data-grid', 'read', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },       
        add: function () {
            return rpcService.request('layer-data-grid', 'add', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },       
        delete: function () {
            return rpcService.request('layer-data-grid', 'delete', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },       
        saveChanges: function () {
            return rpcService.request('layer-data-grid', 'saveChanges', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },       
        multiEdit: function () {
            return rpcService.request('layer-data-grid', 'multiEdit', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        }
    }

}));
