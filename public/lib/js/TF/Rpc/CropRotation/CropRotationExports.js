
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/CropRotation/CropRotation'], factory);
    } else {
        Namespace('TF.Rpc.CropRotation');
        // Browser globals (root is window)
        root.TF.Rpc.CropRotation.CropRotationExports = factory(TF.Rpc.CropRotation);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {     
        exportCropRotationData: function () {
            return rpcService.request('crop-rotation-exports', 'exportCropRotationData', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },     
        exportSoilSample: function () {
            return rpcService.request('crop-rotation-exports', 'exportSoilSample', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },     
        exportFullSampleNorm: function () {
            return rpcService.request('crop-rotation-exports', 'exportFullSampleNorm', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },     
        exportOverlapData: function () {
            return rpcService.request('crop-rotation-exports', 'exportOverlapData', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },     
        exportCultureData: function () {
            return rpcService.request('crop-rotation-exports', 'exportCultureData', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },     
        exportSampleNorm: function () {
            return rpcService.request('crop-rotation-exports', 'exportSampleNorm', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },     
        exportAVGSampleNorm: function () {
            return rpcService.request('crop-rotation-exports', 'exportAVGSampleNorm', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },     
        deleteFile: function () {
            return rpcService.request('crop-rotation-exports', 'deleteFile', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        }
    }

}));
