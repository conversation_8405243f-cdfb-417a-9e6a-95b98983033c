
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Dashboard/Dashboard'], factory);
    } else {
        Namespace('TF.Rpc.Dashboard');
        // Browser globals (root is window)
        root.TF.Rpc.Dashboard.DashboardContracts = factory(TF.Rpc.Dashboard);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {
        read: function () {
            return rpcService.request('dashboard-contracts', 'read', arguments);
        }
    }

}));
