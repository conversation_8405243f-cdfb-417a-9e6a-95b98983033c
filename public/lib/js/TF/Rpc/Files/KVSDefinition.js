
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Files/Files'], factory);
    } else {
        Namespace('TF.Rpc.Files');
        // Browser globals (root is window)
        root.TF.Rpc.Files.KVSDefinition = factory(TF.Rpc.Files);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {       
        read: function () {
            return rpcService.request('definition', 'read', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        saveDefinition: function () {
            return rpcService.request('definition', 'saveDefinition', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        saveKMSDefinition: function () {
            return rpcService.request('definition', 'saveKMSDefinition', arguments); //"arguments" e ключова системна дума и не трябва да се променя!  
        },
        saveForIsakDefinition: function () {
            return rpcService.request('definition', 'saveForIsakDefinition', arguments); //"arguments" e ключова системна дума и не трябва да се променя!  
        },
        saveForWorkDefinition: function () {
            return rpcService.request('definition', 'saveForWorkDefinition', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        saveForGpsDefinition: function () {
            return rpcService.request('definition', 'saveForGpsDefinition', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        }
    };

}));
