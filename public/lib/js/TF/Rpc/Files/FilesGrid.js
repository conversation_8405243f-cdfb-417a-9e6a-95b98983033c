
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Files/Files'], factory);
    } else {
        Namespace('TF.Rpc.Files');
        // Browser globals (root is window)
        root.TF.Rpc.Files.FilesGrid = factory(TF.Rpc.Files);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {       
        read: function () {
            return rpcService.request('files', 'read', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        saveDefinition: function () {
            return rpcService.request('files', 'saveDefinition', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        deleteSelectedFiles: function () {
            return rpcService.request('files', 'deleteSelectedFiles', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        endUpdate: function() {
            return rpcService.request('files', 'endUpdate', arguments);
        },
        checkForExistingOperations: function() {
            return rpcService.request('files', 'checkForExistingOperations', arguments);
        }
    }

}));
