
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/OwnerPayments/OwnerPayments'], factory);
    } else {
        Namespace('TF.Rpc.OwnerPayments');
        // Browser globals (root is window)
        root.TF.Rpc.OwnerPayments.OwnersTree = factory(TF.Rpc.OwnerPayments);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {       
        /**
	     * Returns array of living people, participating in any contracts for the given year 
	     * api-method read
	     * @param array $filterParams
	     * {
	     *     #item string year
	     *     #item int page_number
	     *     #item string owner_name
	     *     #item string owner_egn
	     *     #item string company_name
	     *     #item string company_eik    
	     *     #item string cnum
	     *     #item array coontract_type
	     *     #item array farming
	     *     #item array owner_ids
	     *     #item boolean with_renta_nat
	     * }
	     * @param integer $page         -The current page number.
	     * @param integer $rows         -Rows per page.
	     * @param string $sort          -A grid column by which the grid is sorted.
	     * @param string $order         -The sort order ASC/DESC.
	     * @return void|array
	     */
        readOwners: function (filterParams, page, rows, sort, order) {
            return rpcService.request('owners-contracts-tree', 'read', arguments);
        }
    }

}));
