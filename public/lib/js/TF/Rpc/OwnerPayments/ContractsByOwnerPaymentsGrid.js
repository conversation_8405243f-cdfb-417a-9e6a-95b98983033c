
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/OwnerPayments/OwnerPayments'], factory);
    } else {
        Namespace('TF.Rpc.OwnerPayments');
        // Browser globals (root is window)
        root.TF.Rpc.OwnerPayments.ContractsByOwnerPaymentsGrid = factory(TF.Rpc.OwnerPayments);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {       
        /**
	     * returns all contracts, and payments done for those contracts, that a person paticipates in.
	     * api-method read
	     * @param array $filterParam
	     * {
	     *     #item string year
	     *     #item int owner_id
	     *     #item int annex_id
	     * }
	     * @param integer $page         -The current page number.
	     * @param integer $rows         -Rows per page.
	     * @param string $sort          -A grid column by which the grid is sorted.
	     * @param string $order         -The sort order ASC/DESC.
	     * @return array 
	     */
        readContractsByOwnerPaymentsGrid: function (filterParam, page, rows, sort, order) {
            return rpcService.request('contracts-owner-payments-grid', 'read', arguments);
        }
    }

}));
