
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/OwnerPayments/OwnerPayments'], factory);
    } else {
        Namespace('TF.Rpc.OwnerPayments');
        // Browser globals (root is window)
        root.TF.Rpc.OwnerPayments.PersonalUseGrid = factory(TF.Rpc.OwnerPayments);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {       
        /**
	     * Read personal use grid
	     * api-method read
	     * @param array $filterParam
	     * {
	     *     #item int owner_id       -The owner id
	     *     #item string year        -The year
	     * }
	     * @param integer $page         -The current page number.
	     * @param integer $rows         -Rows per page.
	     * @param string $sort          -A grid column by which the grid is sorted.
	     * @param string $order         -The sort order ASC/DESC.
	     * @return array result
	     * {
	     *     #item array rows         -The results
	     *     #item string total       -The count of all results 
	     * } 
	     */
        readPersonalUseGrid: function (filterParam, page, rows, sort, order) {
            return rpcService.request('owner-personal-use-grid', 'read', arguments);
        },
        /**
	     * Save personal use after editing
	     * api-method savePersonalUse
	     * @param array $data{
	     *     #item int contract_id,
	     *     #item int owner_id,
	     *     #item string renta,
	     *     #item string renta_nat,
	     *     #item string year,
	     *     #item string personal_use,
	     *     #item int renta_nat_type,
	     * }
	     * @return void
	     */
        savePersonalUse: function (data) {
            return rpcService.request('owner-personal-use-grid', 'savePersonalUse', arguments);
        }
    }

}));
