
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/ZPlots/ZPlots'], factory);
    } else {
        Namespace('TF.Rpc.ZPlots');
        // Browser globals (root is window)
        root.TF.Rpc.ZPlots.ZPlotsLayersTree = factory(TF.Rpc.ZPlots);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {       
        read: function () {
            return rpcService.request('zplots-layers-tree', 'read', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        }
    }

}));
