
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/ZPlots/ZPlots'], factory);
    } else {
        Namespace('TF.Rpc.ZPlots');
        // Browser globals (root is window)
        root.TF.Rpc.ZPlots.ZPlotsExport = factory(TF.Rpc.ZPlots);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {       
        exportExcel: function () {
            return rpcService.request('zplots-export', 'exportExcel', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },       
        exportIntersectExcel: function () {
            return rpcService.request('zplots-export', 'exportIntersectExcel', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },       
        removeFile: function () {
            return rpcService.request('zplots-export', 'removeFile', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        }
    }

}));
