
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/ZPlots/ZPlots'], factory);
    } else {
        Namespace('TF.Rpc.ZPlots');
        // Browser globals (root is window)
        root.TF.Rpc.ZPlots.ZPMapInfo = factory(TF.Rpc.ZPlots);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {       
        read: function () {
            return rpcService.request('zp-map-info', 'read', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },       
        initMap: function () {
            return rpcService.request('zp-map-info', 'initMap', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },       
        initKvs: function () {
            return rpcService.request('zp-map-info', 'initKvs', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },       
        initCulture: function () {
            return rpcService.request('zp-map-info', 'initCulture', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        }
    }

}));
