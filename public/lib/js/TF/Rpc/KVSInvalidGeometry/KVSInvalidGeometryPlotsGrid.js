
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/KVSInvalidGeometry/KVSInvalidGeometry'], factory);
    } else {
        Namespace('TF.Rpc.KVSInvalidGeometry');
        // Browser globals (root is window)
        root.TF.Rpc.KVSInvalidGeometry.KVSInvalidGeometryPlotsGrid = factory(TF.Rpc.KVSInvalidGeometry);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {       
        read: function () {
            return rpcService.request('kvs-invalid-plots-grid', 'read', arguments); 
        },
    };

}));
