
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/KVSInvalidGeometry/KVSInvalidGeometry'], factory);
    } else {
        Namespace('TF.Rpc.KVSInvalidGeometry');
        // Browser globals (root is window)
        root.TF.Rpc.KVSInvalidGeometry.KVSInvalidGeometryMapTools = factory(TF.Rpc.KVSInvalidGeometry);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {       
        saveKvsOszInvalidPlotsChanges: function () {
            return rpcService.request('kvs-invalid-plots-map-tools', 'saveKvsOszInvalidPlotsChanges', arguments); 
        },
    };

}));
