(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Collections/Collections'], factory);
    } else {
        Namespace('TF.Rpc.Collections');
        // Browser globals (root is window)
        root.TF.Rpc.Collections.ExportCollection = factory(TF.Rpc.Collections);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {
        /**
         * Export payment order with added iban to pdf
         * api-method exportToPdfBankPaymentOrder
         * @param  string  $collectionId   -Transaction id
         * @param  boolean $collectionDate -Print with date
         * @return string pdfPath          -Path to the file to pdf export
         */
        exportToPdfBankCollectionOrder: function (collectionId) {
            return rpcService.request('export-collection', 'exportToPdfBankCollectionOrder', arguments);
        },

        /**
         * Export payment order with added iban to pdf
         * api-method exportToPdfBankPaymentOrder
         * @param  string  $collectionId   -Transaction id
         * @param  boolean $collectionDate -Print with date
         * @return string pdfPath          -Path to the file to pdf export
         */
        exportToPdfCollectionOrder: function (collectionId) {
            return rpcService.request('export-collection', 'exportToPdfCollectionOrder', arguments);
        }
    }
}));
