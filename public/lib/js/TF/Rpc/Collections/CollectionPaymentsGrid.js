
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Collections/Collections'], factory);
    } else {
        Namespace('TF.Rpc.Collections');
        // Browser globals (root is window)
        root.TF.Rpc.Collections.CollectionPaymentsGrid = factory(TF.Rpc.Collections);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        read: function () {
             return rpcService.request('collection-payments-grid', 'read', arguments);
        },
        addCollectionPayment: function () {
            return rpcService.request('collection-payments-grid', 'addCollectionPayment', arguments);
        },
        cancellationPersonalUseCollection: function () {
            return rpcService.request('collection-payments-grid', 'cancellationPersonalUseCollection', arguments);
        }
    }

}));
