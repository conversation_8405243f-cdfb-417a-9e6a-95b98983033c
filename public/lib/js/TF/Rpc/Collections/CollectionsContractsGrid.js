
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Collections/Collections'], factory);
    } else {
        Namespace('TF.Rpc.Collections');
        // Browser globals (root is window)
        root.TF.Rpc.Collections.CollectionsContractsGrid = factory(TF.Rpc.Collections);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        read: function () {
            return rpcService.request('collections-contracts-grid', 'read', arguments);
        },
        printCollectionContracts: function () {
            return rpcService.request('collections-contracts-grid', 'printCollectionContracts', arguments);
        },
        exportCollectionContracts: function () {
            return rpcService.request('collections-contracts-grid', 'exportCollectionContracts', arguments);
        },
        exportCollectionPersonalUse: function () {
            return rpcService.request('collections-contracts-grid', 'exportCollectionPersonalUse', arguments);
        }
    }

}));
