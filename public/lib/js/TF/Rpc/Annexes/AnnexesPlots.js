
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Annexes/Annexes'], factory);
    } else {
        Namespace('TF.Rpc.Annexes');
        // Browser globals (root is window)
        root.TF.Rpc.Annexes.AnnexesPlots = factory(TF.Rpc.Annexes);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {       
        read: function () {
            return rpcService.request('annexes-plots-grid', 'read', arguments);
        },       
        deleteAnnexPlots: function () {
            return rpcService.request('annexes-plots-grid', 'deleteAnnexPlots', arguments);
        },       
        addAnnexPlotRelation: function () {
            return rpcService.request('annexes-plots-grid', 'addAnnexPlotRelation', arguments);
        }
    }

}));
