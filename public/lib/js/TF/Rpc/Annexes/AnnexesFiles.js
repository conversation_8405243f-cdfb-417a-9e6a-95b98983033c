
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Annexes/Annexes'], factory);
    } else {
        Namespace('TF.Rpc.Annexes');
        // Browser globals (root is window)
        root.TF.Rpc.Annexes.AnnexesFiles = factory(TF.Rpc.Annexes);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {       
        downloadAttached: function () {
            return rpcService.request('annexes-files', 'downloadAttached', arguments);
        },
        deleteAnnexFile: function () {
            return rpcService.request('annexes-files', 'deleteAnnexFile', arguments);
        }
    }

}));
