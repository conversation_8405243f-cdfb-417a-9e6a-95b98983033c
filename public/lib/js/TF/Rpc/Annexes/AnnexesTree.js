
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Annexes/Annexes'], factory);
    } else {
        Namespace('TF.Rpc.Annexes');
        // Browser globals (root is window)
        root.TF.Rpc.Annexes.AnnexesTree = factory(TF.Rpc.Annexes);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {
    	read: function (formData) {
            return rpcService.request('annexes-tree', 'read', arguments); 
        },
        getParentContractData: function () {
            return rpcService.request('annexes-tree', 'getParentContractData', arguments);
        },
        deleteAnnex: function () {
            return rpcService.request('annexes-tree', 'deleteAnnex', arguments);
        },
        deleteAnnexPlots: function () {
            return rpcService.request('annexes-tree', 'deleteAnnexPlots', arguments);
        },
        changeActiveStatus: function () {
            return rpcService.request('annexes-tree', 'changeActiveStatus', arguments);
        },
        markAnnexForEdit: function () {
            return rpcService.request('annexes-tree', 'markAnnexForEdit', arguments);
        },
        editAnnex: function () {
            return rpcService.request('annexes-tree', 'editAnnex', arguments);
        }
    }

}));
