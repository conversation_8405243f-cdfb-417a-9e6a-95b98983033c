
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Login/Login'], factory);
    } else {
        Namespace('TF.Rpc.Login');
        // Browser globals (root is window)
        root.TF.Rpc.Login.ForgottenPassword = factory(TF.Rpc.Login);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {
        initForgottenPasswordAction: function () {
            return rpcService.request('forgotten-password', 'initForgottenPasswordAction', arguments);
        },
        setNewPassword: function () {
            return rpcService.request('forgotten-password', 'setNewPassword', arguments);
        }
    }

}));
