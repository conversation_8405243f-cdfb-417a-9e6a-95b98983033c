
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Login/Login'], factory);
    } else {
        Namespace('TF.Rpc.Login');
        // Browser globals (root is window)
        root.TF.Rpc.Login.LoginForm = factory(TF.Rpc.Login);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {
    return {       
      
        loginUser: function (username, password) {
            return rpcService.request('login-user', 'loginUser', arguments);
        },
        getSessionTime: function (username, password) {
            return rpcService.request('login-user', 'getSessionTime', arguments);
        },
        logoutUser: function () {
            return rpcService.request('login-user', 'logout', arguments);
        },
        appLogout: function () {
            return rpcService.request('login-user', 'appLogout', arguments);
        }

    }

}));
