
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Overlaps/Overlaps'], factory);
    } else {
        Namespace('TF.Rpc.Overlaps');
        // Browser globals (root is window)
        root.TF.Rpc.Overlaps.OverlapsLayersTree = factory(TF.Rpc.Overlaps);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {       
        read: function () {
            return rpcService.request('overlaps-layers-tree', 'read', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },      
        add: function () {
            return rpcService.request('overlaps-layers-tree', 'add', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },      
        delete: function () {
            return rpcService.request('overlaps-layers-tree', 'delete', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        }
    }

}));
