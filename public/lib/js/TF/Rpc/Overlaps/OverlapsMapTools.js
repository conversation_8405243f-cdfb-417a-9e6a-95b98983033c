
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Overlaps/Overlaps'], factory);
    } else {
        Namespace('TF.Rpc.Overlaps');
        // Browser globals (root is window)
        root.TF.Rpc.Overlaps.OverlapsMapTools = factory(TF.Rpc.Overlaps);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {       
        getKVSExtent: function () {
            return rpcService.request('overlaps-map-tools', 'getKVSExtent', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },      
        initKVSMap: function () {
            return rpcService.request('overlaps-map-tools', 'initKVSMap', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },      
        getLayerKVS: function () {
            return rpcService.request('overlaps-map-tools', 'getLayerKVS', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        }
    }

}));
