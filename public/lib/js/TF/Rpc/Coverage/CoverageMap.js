
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Coverage/Coverage'], factory);
    } else {
        Namespace('TF.Rpc.Coverage');
        // Browser globals (root is window)
        root.TF.Rpc.Coverage.CoverageMap = factory(TF.Rpc.Coverage);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        initMap: function () {
            return rpcService.request('coverage-map', 'initMap', arguments);
        },
        initAltitudeMap: function () {
            return rpcService.request('coverage-map', 'initAltitudeMap', arguments);
        }
    }
    

}));
