
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Coverage/Coverage'], factory);
    } else {
        Namespace('TF.Rpc.Coverage');
        // Browser globals (root is window)
        root.TF.Rpc.Coverage.FileStructureTree = factory(TF.Rpc.Coverage);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        read: function () {
            return rpcService.request('coverage-file-structure-tree', 'read', arguments);
        },
        markForEdit: function () {
            return rpcService.request('coverage-file-structure-tree', 'markForEdit', arguments);
        },
        saveEvent: function () {
            return rpcService.request('coverage-file-structure-tree', 'saveEvent', arguments);
        }
    }
    

}));
