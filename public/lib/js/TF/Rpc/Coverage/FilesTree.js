
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Coverage/Coverage'], factory);
    } else {
        Namespace('TF.Rpc.Coverage');
        // Browser globals (root is window)
        root.TF.Rpc.Coverage.FilesTree = factory(TF.Rpc.Coverage);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        read: function () {
            return rpcService.request('coverage-files-tree', 'read', arguments);
        },
        deleteCoverageFile: function () {
            return rpcService.request('coverage-files-tree', 'deleteCoverageFile', arguments);
        },
        getModemFiles: function () {
            return rpcService.request('coverage-files-tree', 'getModemFiles', arguments);
        },
        loadModemFiles: function () {
            return rpcService.request('coverage-files-tree', 'loadModemFiles', arguments);
        }
    }
    

}));
