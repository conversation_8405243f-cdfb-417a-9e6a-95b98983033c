
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Coverage/Coverage'], factory);
    } else {
        Namespace('TF.Rpc.Coverage');
        // Browser globals (root is window)
        root.TF.Rpc.Coverage.ZPLayersTree = factory(TF.Rpc.Coverage);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        read: function () {
            return rpcService.request('coverage-zp-layers-tree', 'read', arguments);
        },
        markZPForEdit: function () {
            return rpcService.request('coverage-zp-layers-tree', 'markZPForEdit', arguments);
        },
        saveZP: function () {
            return rpcService.request('coverage-zp-layers-tree', 'saveZP', arguments);
        }
    }
    

}));
