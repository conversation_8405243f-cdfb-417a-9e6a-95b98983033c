
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Plots/Plots'], factory);
    } else {
        Namespace('TF.Rpc.Plots');
        // Browser globals (root is window)
        root.TF.Rpc.Plots.SubleasedRentedPlotsReportGrid = factory(TF.Rpc.Plots);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        read: function () {
            return rpcService.request('subleased-rented-plots-report-grid', 'read', arguments);
        }
    }

}));
