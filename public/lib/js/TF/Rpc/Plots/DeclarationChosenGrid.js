
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Plots/Plots'], factory);
    } else {
        Namespace('TF.Rpc.Plots');
        // Browser globals (root is window)
        root.TF.Rpc.Plots.DeclarationChosenGrid = factory(TF.Rpc.Plots);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        decl69: function() {
        	return rpcService.request('declaration-chosen-grid', 'decl69', arguments);
        },
        decl70: function() {
        	return rpcService.request('declaration-chosen-grid', 'decl70', arguments);
        },
        anketnaKarta: function() {
        	return rpcService.request('declaration-chosen-grid', 'anketnaKarta', arguments);
        },
        addToDeclaration: function () {
            return rpcService.request('declaration-chosen-grid', 'addToDeclaration', arguments);
        },
        deleteFromDeclaration: function() {
        	return rpcService.request('declaration-chosen-grid', 'deleteFromDeclaration', arguments);
        },
        clearDeclarations: function() {
            return rpcService.request('declaration-chosen-grid', 'clearDeclarations', arguments);
        },
        areaReport: function() {
        	return rpcService.request('declaration-chosen-grid', 'areaReport', arguments);
        }
    }
    

}));
