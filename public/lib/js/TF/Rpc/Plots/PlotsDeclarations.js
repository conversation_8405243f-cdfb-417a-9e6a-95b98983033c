
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Plots/Plots'], factory);
    } else {
        Namespace('TF.Rpc.Plots');
        // Browser globals (root is window)
        root.TF.Rpc.Plots.PlotsDeclarations = factory(TF.Rpc.Plots);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        createDecl69: function () {
            return rpcService.request('plots-declaration-export', 'createDecl69', arguments);
        },
        createDecl70: function() {
        	return rpcService.request('plots-declaration-export', 'createDecl70', arguments);
        },
        createAnketnaKarta: function() {
        	return rpcService.request('plots-declaration-export', 'createAnketnaKarta', arguments);
        },
        createCSVDecl69: function() {
            return rpcService.request('plots-declaration-export', 'createCSVDecl69', arguments);
        },
        createCSVDecl70: function() {
            return rpcService.request('plots-declaration-export', 'createCSVDecl70', arguments);
        },
        createCSVDecPML: function() {
            return rpcService.request('plots-declaration-export', 'createCSVDeclPML', arguments);
        },
        checkKMSAgreements: function() {
            return rpcService.request('plots-declaration-export', 'checkKMSAgreements', arguments);
        },
        removeFile: function() {
            return rpcService.request('plots-declaration-export', 'removeFile', arguments);
        },
        createXmlDecl73: function() {
            return rpcService.request('plots-declaration-export', 'createDecl73XML', arguments);
        },
        createXlsDecl73: function() {
            return rpcService.request('plots-declaration-export', 'createDecl73XLS', arguments);
        },
    }
    

}));
