
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Plots/Plots'], factory);
    } else {
        Namespace('TF.Rpc.Plots');
        // Browser globals (root is window)
        root.TF.Rpc.Plots.PlotReports = factory(TF.Rpc.Plots);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        read: function () {
            return rpcService.request('plots-report-grid', 'read', arguments);
        },

        exportToExcelPlotsReportData: function (params, page, rows, sort, order) {
            return rpcService.request('plots-report-grid', 'exportToExcelPlotsReportData', arguments);
        },

        exportToExcelOwnPlotsReportData: function (params, page, rows, sort, order) {
            return rpcService.request('own-plots-report-grid', 'exportToExcelOwnPlotsReportData', arguments);
        },

        exportToExcelSubleasedPlotsReportData: function (params, page, rows, sort, order) {
            return rpcService.request('subleased-plots-report-grid', 'exportToExcelSubleasedPlotsReportData', arguments);
        },

        exportToExcelForSubleasePlotsReportData: function (params, page, rows, sort, order) {
            return rpcService.request('for-sublease-plots-report-grid', 'exportToExcelForSubleasePlotsReportData', arguments);
        },

        exportToExcelHypothecsPlotsReportData: function (params, page, rows, sort, order) {
            return rpcService.request('hypothecs-plots-report-grid', 'exportToExcelHypothecsPlotsReportData', arguments);
        },

        exportToExcelForHypothecPlotsReportData: function (params, page, rows, sort, order) {
            return rpcService.request('for-hypothec-plots-report-grid', 'exportToExcelForHypothecPlotsReportData', arguments);
        },

        exportToExcelRentedPlotsReportData: function (params, page, rows, sort, order) {
            return rpcService.request('rented-plots-report-grid', 'exportToExcelRentedPlotsReportData', arguments);
        },

        exportToExcelSubleasedRentedPlotsReportData: function (params, page, rows, sort, order) {
            return rpcService.request('subleased-rented-plots-report-grid', 'exportToExcelSubleasedRentedPlotsReportData', arguments);
        },

        exportToExcelExpiringContractsReportData: function (params, page, rows, sort, order) {
            return rpcService.request('expiring-contracts-report-grid', 'exportToExcelExpiringContractsReportData', arguments);
        },

        exportToExcelPlotsInManyContractsReportData: function (params, page, rows, sort, order) {
            return rpcService.request('plots-in-many-contracts-report-grid', 'exportToExcelPlotsInManyContractsReportData', arguments);
        },

        exportToExcelUsedPlotsReportData: function (params, page, rows, sort, order) {
            return rpcService.request('used-plots-report-grid', 'exportToExcelUsedPlotsReportData', arguments);
        },

        exportContractsWithOwnerlessPlots: function (params, page, rows, sort, order) {
            return rpcService.request('contracts-with-ownerless-plots-report-grid', 'exportContractsWithOwnerlessPlots', arguments);
        },

        exportToExcelHistoricalPlotsReportData: function (params, page, rows, sort, order) {
            return rpcService.request('historical-plots-report-grid', 'exportToExcelHistoricalPlotsReportData', arguments);
        },
        exportToExcelDetailedOwnPlotsReportData: function (params, page, rows, sort, order) {
            return rpcService.request('detailed-own-plots-report-grid', 'exportToExcelDetailedOwnPlotsReportData', arguments);
        },
    }

}));
