
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Plots/Plots'], factory);
    } else {
        Namespace('TF.Rpc.Plots');
        // Browser globals (root is window)
        root.TF.Rpc.Plots.PlotsContractsDatagrid = factory(TF.Rpc.Plots);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        read: function () {
            return rpcService.request('plots-contracts-datagrid', 'read', arguments);
        },
        getContractsAddGrid: function() {
        	return rpcService.request('plots-contracts-datagrid', 'getContractsAddGrid', arguments);
        },
        addContractPlotRelation: function() {
        	return rpcService.request('plots-contracts-datagrid', 'addContractPlotRelation', arguments);
        },
        addConfirmedContractPlotRelation: function() {
            return rpcService.request('plots-contracts-datagrid', 'addConfirmedContractPlotRelation', arguments);
        },
        deleteContractPlotRelation: function() {
        	return rpcService.request('plots-contracts-datagrid', 'deleteContractPlotRelation', arguments);
        }
    }
    

}));
