
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Plots/Plots'], factory);
    } else {
        Namespace('TF.Rpc.Plots');
        // Browser globals (root is window)
        root.TF.Rpc.Plots.PlotMap = factory(TF.Rpc.Plots);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        initMap: function () {
            return rpcService.request('plot-map', 'initMap', arguments);
        },
        contractInit: function () {
            return rpcService.request('plot-map', 'contractInit', arguments);
        },
        initReportMap: function () {
            return rpcService.request('plot-map', 'initReportMap', arguments);
        },
        masivInit: function () {
            return rpcService.request('plot-map', 'masivInit', arguments);
        },
        initMapLayers: function () {
            return rpcService.request('plot-map', 'initMapLayers', arguments);
        },
        getKvsExtent: function () {
            return rpcService.request('plot-map', 'getKvsExtent', arguments);
        }
    }

}));
