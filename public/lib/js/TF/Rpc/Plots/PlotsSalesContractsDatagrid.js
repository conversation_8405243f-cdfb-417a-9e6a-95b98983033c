
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Plots/Plots'], factory);
    } else {
        Namespace('TF.Rpc.Plots');
        // Browser globals (root is window)
        root.TF.Rpc.Plots.PlotsSalesContractsDatagrid = factory(TF.Rpc.Plots);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        read: function () {
            return rpcService.request('plots-sales-contracts-datagrid', 'read', arguments);
        },
        getSalesContractsAddGrid: function() {
        	return rpcService.request('plots-sales-contracts-datagrid', 'getSalesContractsAddGrid', arguments);
        },
        addSalesContractPlotRelation: function() {
        	return rpcService.request('plots-sales-contracts-datagrid', 'addSalesContractPlotRelation', arguments);
        },
        deleteSalesContractPlotRelation: function() {
        	return rpcService.request('plots-sales-contracts-datagrid', 'deleteSalesContractPlotRelation', arguments);
        }
    }
    

}));
