
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Plots/Plots'], factory);
    } else {
        Namespace('TF.Rpc.Plots');
        // Browser globals (root is window)
        root.TF.Rpc.Plots.ReportShowMap = factory(TF.Rpc.Plots);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {       
       
        mapReports: function (param) {
            return rpcService.request('reports-show-map', 'mapReports', arguments); 
        },
        mapReportsByType: function (param) {
            return rpcService.request('reports-show-map', 'mapReportsByType', arguments); 
        },
 
    }

}));
