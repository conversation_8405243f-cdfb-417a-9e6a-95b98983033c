
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Notifications/Notifications'], factory);
    } else {
        Namespace('TF.Rpc.Notifications');
        // Browser globals (root is window)
        root.TF.Rpc.Notifications.NotificationsMainGrid = factory(TF.Rpc.Notifications);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {
        read: function () {
            return rpcService.request('notification-grid', 'read', arguments);
        },
        settings: function () {
            return rpcService.request('notification-grid', 'settings', arguments);
        },
        editNotificationSettings: function () {
            return rpcService.request('notification-grid', 'editNotificationSettings', arguments);
        }
    }

}));
