(function(root, factory) {
    if (typeof define === "function" && define.amd) {
        // AMD. Register as an anonymous module.
        define(["TF/Rpc/Warehouse/Warehouse"], factory);
    } else {
        // Browser globals (root is window)
        Namespace("TF.Rpc.Warehouse");
        root.TF.Rpc.Warehouse.Measures = factory(TF.Rpc.Warehouse);
    }
})(typeof self !== "undefined" ? self : this, function(rpcService) {
    return {
        addMeasure: function() {
            return rpcService.request(
                "warehouse-measures",
                "addMeasure",
                arguments
            );
        },
        editMeasure: function() {
            return rpcService.request(
                "warehouse-measures",
                "editMeasure",
                arguments
            );
        },
        deleteMeasure: function() {
            return rpcService.request(
                "warehouse-measures",
                "deleteMeasure",
                arguments
            );
        },
        searchMeasures: function() {
            return rpcService.request(
                "warehouse-measures",
                "searchMeasures",
                arguments
            );
        }
    };
});
