(function(root, factory) {
    if (typeof define === "function" && define.amd) {
        // AMD. Register as an anonymous module.
        define(["TF/Rpc/Warehouse/Warehouse"], factory);
    } else {
        // Browser globals (root is window)
        Namespace("TF.Rpc.Warehouse");
        root.TF.Rpc.Warehouse.Warehouses = factory(TF.Rpc.Warehouse);
    }
})(typeof self !== "undefined" ? self : this, function(rpcService) {
return {
    getWarehouse: function() {
        return rpcService.request("warehouses", "getWarehouse", arguments);
    },
    addWarehouse: function() {
        return rpcService.request("warehouses", "addWarehouse", arguments);
    },
    editWarehouse: function() {
        return rpcService.request("warehouses", "editWarehouse", arguments);
    },
    deleteWarehouse: function() {
        return rpcService.request("warehouses", "deleteWarehouse", arguments);
    },
    getAvailableWarehouses: function() {
        return rpcService.request("warehouses", "getAvailableWarehouses", arguments);
    }
};
});
