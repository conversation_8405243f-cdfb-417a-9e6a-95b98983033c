(function(root, factory) {
    if (typeof define === "function" && define.amd) {
        // AMD. Register as an anonymous module.
        define(["TF/Rpc/Warehouse/Warehouse"], factory);
    } else {
        // Browser globals (root is window)
        Namespace("TF.Rpc.Warehouse");
        root.TF.Rpc.Warehouse.Items = factory(TF.Rpc.Warehouse);
    }
})(typeof self !== "undefined" ? self : this, function(rpcService) {
    return {
        addItem: function() {
            return rpcService.request(
                "warehouse-items",
                "addItem",
                arguments
            );
        },
        editItem: function() {
            return rpcService.request(
                "warehouse-items",
                "editItem",
                arguments
            );
        },
        deleteItem: function() {
            return rpcService.request(
                "warehouse-items",
                "deleteItem",
                arguments
            );
        },
        searchItems: function() {
            return rpcService.request(
                "warehouse-items",
                "searchItems",
                arguments
            );
        }
    };
});
