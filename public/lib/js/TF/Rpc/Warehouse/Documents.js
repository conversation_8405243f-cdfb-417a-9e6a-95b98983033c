(function(root, factory) {
    if (typeof define === "function" && define.amd) {
        // AMD. Register as an anonymous module.
        define(["TF/Rpc/Warehouse/Warehouse"], factory);
    } else {
        // Browser globals (root is window)
        Namespace("TF.Rpc.Warehouse");
        root.TF.Rpc.Warehouse.Documents = factory(TF.Rpc.Warehouse);
    }
})(typeof self !== "undefined" ? self : this, function(rpcService) {
    return {
        getDocuments: function() {
            return rpcService.request(
                "warehouse-documents",
                "read",
                arguments
            );
        },
        getDocument: function() {
            return rpcService.request(
                "warehouse-documents",
                "getDocument",
                arguments
            );
        },
        addDocumentsInvoice: function() {
            return rpcService.request(
                "warehouse-documents",
                "addDocumentsInvoice",
                arguments
            );
        },
        getPdfDocument: function() {
            return rpcService.request(
                "warehouse-documents",
                "getPdfDocument",
                arguments
            );
        },
        removeDocument: function() {
            return rpcService.request(
                "warehouse-documents",
                "removeDocument",
                arguments
            );
        }
    };
});
