(function(root, factory) {
    if (typeof define === "function" && define.amd) {
        // AMD. Register as an anonymous module.
        define(["TF/Rpc/Warehouse/Warehouse"], factory);
    } else {
        // Browser globals (root is window)
        Namespace("TF.Rpc.Warehouse");
        root.TF.Rpc.Warehouse.Companies = factory(TF.Rpc.Warehouse);
    }
})(typeof self !== "undefined" ? self : this, function(rpcService) {
    return {
        getItemGroup: function() {
            return rpcService.request(
                "warehouse-groups",
                "getItemGroup",
                arguments
            );
        },
        getItemGroups: function() {
            return rpcService.request(
                "warehouse-groups",
                "getItemGroups",
                arguments
            );
        },
        addItemGroup: function() {
            return rpcService.request(
                "warehouse-groups",
                "addItemGroup",
                arguments
            );
        },
        editItemGroup: function() {
            return rpcService.request(
                "warehouse-groups",
                "editItemGroup",
                arguments
            );
        },
        deleteItemGroup: function() {
            return rpcService.request(
                "warehouse-groups",
                "deleteItemGroup",
                arguments
            );
        },
        getCompanyGroup: function() {
            return rpcService.request(
                "warehouse-groups",
                "getCompanyGroup",
                arguments
            );
        },
        getCompanyGroups: function() {
            return rpcService.request(
                "warehouse-groups",
                "getCompanyGroups",
                arguments
            );
        },
        addCompanyGroup: function() {
            return rpcService.request(
                "warehouse-groups",
                "addCompanyGroup",
                arguments
            );
        },
        editCompanyGroup: function() {
            return rpcService.request(
                "warehouse-groups",
                "editCompanyGroup",
                arguments
            );
        },
        deleteCompanyGroup: function() {
            return rpcService.request(
                "warehouse-groups",
                "deleteCompanyGroup",
                arguments
            );
        },
    };
});
