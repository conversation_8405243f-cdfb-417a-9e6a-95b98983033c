(function(root, factory) {
    if (typeof define === "function" && define.amd) {
        // AMD. Register as an anonymous module.
        define(["TF/Rpc/Warehouse/Warehouse"], factory);
    } else {
        // Browser globals (root is window)
        Namespace("TF.Rpc.Warehouse");
        root.TF.Rpc.Warehouse.Transactions = factory(TF.Rpc.Warehouse);
    }
})(typeof self !== "undefined" ? self : this, function(rpcService) {
    return {
        createAddTransaction: function() {
            return rpcService.request(
                "warehouse-transactions",
                "createAddTransaction",
                arguments
            );
        },
        updateAddTransaction: function() {
            return rpcService.request(
                "warehouse-transactions",
                "updateAddTransaction",
                arguments
            );
        },
        createSubTransaction: function() {
            return rpcService.request(
                "warehouse-transactions",
                "createSubTransaction",
                arguments
            );
        },
        updateSubTransaction: function() {
            return rpcService.request(
                "warehouse-transactions",
                "updateSubTransaction",
                arguments
            );
        },
        createTransferTransaction: function() {
            return rpcService.request(
                "warehouse-transactions",
                "createTransferTransaction",
                arguments
            );
        },
        updateTransferTransaction: function() {
            return rpcService.request(
                "warehouse-transactions",
                "updateTransferTransaction",
                arguments
            );
        },
        getTransactionsItems: function() {
            return rpcService.request(
                "warehouse-transactions",
                "getTransactionItems",
                arguments
            );
        },
        getTransactionItemsForReturn: function() {
            return rpcService.request(
                "warehouse-transactions",
                "getTransactionItemsForReturn",
                arguments
            );
        },
        removeTransaction: function() {
            return rpcService.request(
                "warehouse-transactions",
                "removeTransaction",
                arguments
            );
        }
    };
});
