(function(root, factory) {
    if (typeof define === "function" && define.amd) {
        // AMD. Register as an anonymous module.
        define(["TF/Rpc/Warehouse/Warehouse"], factory);
    } else {
        // Browser globals (root is window)
        Namespace("TF.Rpc.Warehouse");
        root.TF.Rpc.Warehouse.Reports = factory(TF.Rpc.Warehouse);
    }
})(typeof self !== "undefined" ? self : this, function(rpcService) {
    return {
        getReportData: function(params) {
            return rpcService.request(
                "warehouse-transactions",
                "getReports",
                arguments
            );
        },
        exportReport: function(params) {
            return rpcService.request(
                "warehouse-transactions",
                "exportReports",
                arguments
            );
        },
    };
});
