(function(root, factory) {
    if (typeof define === "function" && define.amd) {
        // AMD. Register as an anonymous module.
        define(["TF/Rpc/Warehouse/Warehouse"], factory);
    } else {
        // Browser globals (root is window)
        Namespace("TF.Rpc.Warehouse");
        root.TF.Rpc.Warehouse.Companies = factory(TF.Rpc.Warehouse);
    }
})(typeof self !== "undefined" ? self : this, function(rpcService) {
    return {
        getCompanies: function() {
            return rpcService.request(
                "warehouse-companies",
                "read",
                arguments
            );
        },
        addCompany: function() {
            return rpcService.request(
                "warehouse-companies",
                "addCompany",
                arguments
            );
        },
        editCompany: function() {
            return rpcService.request(
                "warehouse-companies",
                "editCompany",
                arguments
            );
        },
        deleteCompany: function() {
            return rpcService.request(
                "warehouse-companies",
                "deleteCompany",
                arguments
            );
        },
        getCompanyTypes: function() {
            return rpcService.request(
                "warehouse-companies",
                "getCompanyTypes",
                arguments
            );
        },
        syncCompanies: function() {
            return rpcService.request(
                "warehouse-companies",
                "syncCompanies",
                arguments
            );
        },
    };
});
