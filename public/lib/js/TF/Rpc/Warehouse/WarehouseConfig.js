(function(root, factory) {
    if (typeof define === "function" && define.amd) {
        // AMD. Register as an anonymous module.
        define(["TF/Rpc/Warehouse/Warehouse"], factory);
    } else {
        // Browser globals (root is window)
        Namespace("TF.Rpc.Warehouse");
        root.TF.Rpc.Warehouse.WarehouseConfig = factory(TF.Rpc.Warehouse);
    }
})(typeof self !== "undefined" ? self : this, function(rpcService) {
    return {
        getWarehouseConfigParams: function() {
            return rpcService.request("warehouse-config", "getWarehouseConfigParams", arguments);
        },
        editWarehouseConfigParams: function() {
            return rpcService.request("warehouse-config", "editWarehouseConfigParams", arguments);
        },
    };
});
