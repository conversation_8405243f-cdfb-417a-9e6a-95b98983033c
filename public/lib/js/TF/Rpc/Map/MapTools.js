
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Map/Map'], factory);
    } else {
        Namespace('TF.Rpc.Map');
        // Browser globals (root is window)
        root.TF.Rpc.Map.MapTools = factory(TF.Rpc.Map);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {       
        selectPolygon: function () {
            return rpcService.request('map-tools', 'selectPolygon', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },      
        deleteAllItemsByLayer: function () {
            return rpcService.request('map-tools', 'deleteAllItemsByLayer', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },      
        saveItem: function () {
            return rpcService.request('map-tools', 'saveItem', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },       
        savePropertyGrid: function () {
            return rpcService.request('map-tools', 'savePropertyGrid', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },     
        copyLayerItems: function () {
            return rpcService.request('map-tools', 'copyLayerItems', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },      
        saveClipping: function () {
            return rpcService.request('map-tools', 'saveClipping', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },      
        emptyGPS: function () {
            return rpcService.request('map-tools', 'emptyGPS', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },     
        deleteItem: function () {
            return rpcService.request('map-tools', 'deleteItem', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },      
        saveKVSSplit: function () {
            return rpcService.request('map-tools', 'saveKVSSplit', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        saveItemKvsOsz: function () {
            return rpcService.request('map-tools', 'saveItemKvsOsz', arguments); 
        },
        initTopicLayer: function () {
            return rpcService.request('map-tools', 'initTopicLayer', arguments); 
        },
        refreshTopicLayerKVSViews: function () {
            return rpcService.request('map-tools', 'refreshTopicLayerKVSViews', arguments); 
        },
        getCombinedGeometry: function () {
            return rpcService.request('map-tools', 'getCombinedGeometry', arguments); 
        },
        clippingKVSByNTPAndUpdateAreas: function () {
            return rpcService.request('map-tools', 'clippingKVSByNTPAndUpdateAreas', arguments);
        },
        clippingWithKVSAndExportExcel: function () {
            return rpcService.request('map-tools', 'clippingWithKVSAndExportExcel', arguments);
        },
        splitPolygon: function () {
            return rpcService.request('map-tools', 'splitPolygon', arguments);
        },
    }

}));
