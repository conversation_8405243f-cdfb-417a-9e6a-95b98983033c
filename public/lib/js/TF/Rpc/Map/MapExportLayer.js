
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Map/Map'], factory);
    } else {
        Namespace('TF.Rpc.Map');
        // Browser globals (root is window)
        root.TF.Rpc.Map.MapExportLayer = factory(TF.Rpc.Map);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return { /**
       * Exports layer in .shp
       * @param  {objects} data {
       *     @item string export_type
       *     @item string layer_name
       *     @item string layer_type
       *     @item string layer_id
       *     @item boolean united
       *     @item boolean export_old
       *     @item boolean use_filter
       *     @item array selected_ids
       * }
       *
       * @return {Diferred}       See http://api.jquery.com/category/deferred-object/.
       */
      export: function() {
        return rpcService.request("export-layer", "export", arguments); //"arguments" e ключова системна дума и не трябва да се променя!
	  },

	  /**
       * Exports layer in .shp
       * @param  {objects} data {
       *     @item string export_type
       *     @item string layer_name
       *     @item string layer_type
       *     @item string layer_id
       *     @item boolean united
       *     @item boolean export_old
       *     @item boolean use_filter
       *     @item array selected_ids
	   * 	 @device_id The modem id.
       * }
       *
       * @return {Diferred}       See http://api.jquery.com/category/deferred-object/.
       */
      exporToModem: function() {
        return rpcService.request("export-layer", "exporToModem", arguments); //"arguments" e ключова системна дума и не трябва да се променя!
      } };

}));
