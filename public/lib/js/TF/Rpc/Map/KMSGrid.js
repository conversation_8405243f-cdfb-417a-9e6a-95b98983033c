
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Map/Map'], factory);
    } else {
        Namespace('TF.Rpc.Map');
        // Browser globals (root is window)
        root.TF.Rpc.Map.KMSGrid = factory(TF.Rpc.Map);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {       
        read: function () {
            return rpcService.request('kms-datagrid', 'read', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        exportToExcel: function () {
            return rpcService.request('kms-datagrid', 'exportToExcel', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        }
    };

}));
