
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Map/Map'], factory);
    } else {
        Namespace('TF.Rpc.Map');
        // Browser globals (root is window)
        root.TF.Rpc.Map.GpsData = factory(TF.Rpc.Map);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {    
    	/**
		 * @api-method getAllGpsData
		 * @param  array $plotData  
		 * @param  string $page      
		 * @param  string $rows      
		 * @param  string $sort      
		 * @param  string $order    
		 * @return array
		 *{
		 *   	#item string row_to_json
		 *}
		 */
        getAllGpsData: function () {
            return rpcService.request('gps-data', 'getAllGpsData', arguments);
        }
    }

}));
