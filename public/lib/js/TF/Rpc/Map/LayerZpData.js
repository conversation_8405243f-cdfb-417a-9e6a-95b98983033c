
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Map/Map'], factory);
    } else {
        Namespace('TF.Rpc.Map');
        // Browser globals (root is window)
        root.TF.Rpc.Map.LayerZpData = factory(TF.Rpc.Map);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {    
    	/**
    	 * @api-method getAllZpLayersData
	     * Get all zp layers
	     * @return array all zp layers
	     */
        getAllZpLayersData: function () {
            return rpcService.request('layer-zp-data', 'getAllZpLayersData', arguments);
        }
    }

}));
