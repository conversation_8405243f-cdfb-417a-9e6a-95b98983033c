
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Map/Map'], factory);
    } else {
        Namespace('TF.Rpc.Map');
        // Browser globals (root is window)
        root.TF.Rpc.Map.KVSMerge = factory(TF.Rpc.Map);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {       
        setMergeFields: function () {
            return rpcService.request('kvs-merge', 'setMergeFields', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },       
        saveKVSMerge: function () {
            return rpcService.request('kvs-merge', 'saveKVSMerge', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        }
    }

}));
