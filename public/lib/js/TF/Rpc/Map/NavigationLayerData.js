
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Map/Map'], factory);
    } else {
        Namespace('TF.Rpc.Map');
        // Browser globals (root is window)
        root.TF.Rpc.Map.NavigationLayerData = factory(TF.Rpc.Map);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return { /**
       * Exports layer in .shp
       * @param  {objects} data {
       *     @item string layer_name
       *     @item string layer_type
       *     @item string layer_id
       *     @item boolean use_filter
       *     @item array selected_ids
       * }
       *
       * @return {Diferred} See http://api.jquery.com/category/deferred-object/.
       */
      getLineFeature: function() {
        return rpcService.request("navigation-layer-data", "getLineFeature", arguments); //"arguments" e ключова системна дума и не трябва да се променя!
	  }
    };  
}));
