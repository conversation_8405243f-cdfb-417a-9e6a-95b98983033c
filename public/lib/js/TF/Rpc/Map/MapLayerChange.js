
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Map/Map'], factory);
    } else {
        Namespace('TF.Rpc.Map');
        // Browser globals (root is window)
        root.TF.Rpc.Map.MapLayerChange = factory(TF.Rpc.Map);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {       
        markForEdit: function () {
            return rpcService.request('layer-change', 'markForEdit', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },      
        save: function () {
            return rpcService.request('layer-change', 'save', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        updateWorkLayerAttributes: function () {
            return rpcService.request('layer-change', 'updateWorkLayerAttributes', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        updateWorkLayerColumnsDefinitions: function () {
            return rpcService.request('layer-change', 'updateWorkLayerColumnsDefinitions', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        getWorkLayerColumnsDefinitions: function () {
            return rpcService.request('layer-change', 'getWorkLayerColumnsDefinitions', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        }
    }

}));
