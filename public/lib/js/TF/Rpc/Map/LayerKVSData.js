
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Map/Map'], factory);
    } else {
        Namespace('TF.Rpc.Map');
        // Browser globals (root is window)
        root.TF.Rpc.Map.LayerKVSData = factory(TF.Rpc.Map);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {    
    	/**
         * @api-method getKVSLayerData
         * Get data for kvs layer
         * @return array $layerKvsData 
         * {
         *      #item string layer_table    The name of table
         *      #item string extent         The table extent
         * }
         */
        getKVSLayerData: function () {
            return rpcService.request('layer-kvs-data', 'getKVSLayerData', arguments);
        }
    }

}));
