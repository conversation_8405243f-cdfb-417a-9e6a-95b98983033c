
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Map/Map'], factory);
    } else {
        Namespace('TF.Rpc.Map');
        // Browser globals (root is window)
        root.TF.Rpc.Map.MapAttributeTables = factory(TF.Rpc.Map);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {       
        getMaxExtent: function () {
            return rpcService.request('attribute-tables', 'getMaxExtent', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },      
        executeMultiEdit: function () {
            return rpcService.request('attribute-tables', 'executeMultiEdit', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        }
    }

}));
