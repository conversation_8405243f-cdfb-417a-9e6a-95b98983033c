
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Map/Map'], factory);
    } else {
        Namespace('TF.Rpc.Map');
        // Browser globals (root is window)
        root.TF.Rpc.Map.GpsGrid = factory(TF.Rpc.Map);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {    
    	/**
		 * @api-method read
		 * @param  array  $rpcParams 
		 *         {
		 *         		#item integer layer_id
		 *         		#item string  table_name
		 *         		#item boolean clear_filter
		 *         		#item string  filtered_plots
		 *         		#item string  plot_name
		 *         		#item string  plot_info
		 *         		#item string  action
		 *         		#item array   gids
		 *         }
		 * @param  string $page      pagination parameter
		 * @param  string $rows      pagination parameter
		 * @param  string $sort      pagination parameter
		 * @param  string $order     pagination parameter
		 * @return array
		 *{
		 *   	#item integer gid
		 *   	#item string plot_name
		 *   	#item string plot_info
		 *   	#item string area
		 *   	#item string st_astext
		 *}
		 */   
        getGpsGrid: function () {
            return rpcService.request('gps-datagrid', 'read', arguments);
        },
        exportToExcel: function () {
            return rpcService.request('gps-datagrid', 'exportToExcel', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        }
    }

}));
