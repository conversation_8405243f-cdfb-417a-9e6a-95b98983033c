(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['jquery', 'TF/Rpc/Exception', 'TF/Rpc/ExceptionsList','TF/Loading/Loading'], factory);
    } else {
        Namespace('TF.Rpc');
        // Browser globals (root is window)
        var tmpRpc = { ...root.TF.Rpc};
        root.TF.Rpc = factory(jQuery, TF.Rpc.Exception, TF.Rpc.ExceptionsList, TF.Loading);
        Object.assign(root.TF.Rpc, tmpRpc);
    }
}(typeof self !== 'undefined' ? self : this, function ($, Exception, ExceptionsList, Loading) {
    var _exceptios = {};
    var requests_number = 0;
    var loader_started = false;

    function Rpc (serviceName) {
        this._serviceName = serviceName;
        this._hideLoader = false;
    }
    /**
     * Calls the remote RPC methods.
     * @param serviceId {string} The service name
     * @param method {string} The method name
     * @param args {array} An array of arguments we want to pass.
     * @return {Deferred}
     */
    Rpc.prototype.request = function(serviceId ,method, args) {
        if (typeof this._serviceName === "undefined") {
            throw new Error("Missing RPC service name!");
        }

        requests_number++;
        showLoader();
        var params = Array.prototype.slice.call(args);
        var rpcParams = JSON.stringify({
            "method": method,
            "params": params,
            "id": 1,
            "jsonrpc": "2.0"
        });

        var rpcUrl = 'index.php?' + this._serviceName + "=" + serviceId;
        return promise = $.ajax({
            url: rpcUrl,
            data: rpcParams,
            accepts: 'application/json',
            contentType: 'application/json',
            method: 'post',
            dataType: 'json',
            processData: false,
            async: true
        }).then(function (data) {
            requests_number--;
            hideLoader();

            if (typeof data.error !== "undefined") {
                return $.Deferred()
                    .reject(new Exception(data.error))
                    .promise();
            }
            return data.result;
        }, function (data) {
            requests_number--;
            hideLoader();
            if (data.status === 401) {
                if(jQuery('#login-win').window('options').closed){
                    const logoutParams = JSON.stringify({
                        "method": "appLogout",
                        "params": [],
                        "id": 1,
                        "jsonrpc": "2.0"
                     });
                     
                    const logoutUrl = 'index.php?login-rpc=login-user'
                    $.ajax({
                        url: logoutUrl,
                        data: logoutParams,
                        accepts: 'application/json',
                        contentType: 'application/json',
                        method: 'post',
                        dataType: 'json',
                        processData: false,
                        async: true
                    }).then(function (data) {
                        window.location.href = "index.php?page=Home"
                    });
                }
            }
            if (data.status == ExceptionsList.NO_RIGHTS.code) {
                return $.Deferred()
                    .reject(new Exception({
                        code: data.status,
                        message: data.statusText,
                        data: null
                    }))
                    .promise();
            }
        });
    };

    function showLoader() {
        if (!this._hideLoader && !loader_started) {
            Loading.start();
            loader_started = true;
        }
    }

    function hideLoader(){
        if (!this._hideLoader && requests_number === 0) {
            Loading.end();
            loader_started= false;
        }
    }

    /**
     * @param {string} serviceId
     */
    Rpc.prototype.setServiceId = function(serviceId) {
        this._serviceName = serviceId;
    };

    Rpc.prototype.hideLoader = function() {
        this._hideLoader = true;
    };

    return Rpc;

}));
