
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Cooperators/Cooperators'], factory);
    } else {
        Namespace('TF.Rpc.Cooperators');
        // Browser globals (root is window)
        root.TF.Rpc.Cooperators.CooperatorsTree = factory(TF.Rpc.Cooperators);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        read: function () {
            return rpcService.request('cooperators-tree', 'read', arguments);
        },
        load: function() {
        	return rpcService.request('cooperators-tree', 'load', arguments);
        },
        update: function() {
        	return rpcService.request('cooperators-tree', 'update', arguments);
        },
        create: function() {
        	return rpcService.request('cooperators-tree', 'create', arguments);
        },
        delete: function() {
        	return rpcService.request('cooperators-tree', 'delete', arguments);
        }
    };
    

}));
