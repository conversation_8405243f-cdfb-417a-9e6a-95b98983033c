
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Cooperators/Cooperators'], factory);
    } else {
        Namespace('TF.Rpc.Cooperators');
        // Browser globals (root is window)
        root.TF.Rpc.Cooperators.ExportCooperatorPaymentsBlanks = factory(TF.Rpc.Cooperators);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
    	expCooperatorOrder: function () {
            return rpcService.request('export-cooperator-payments-blanks', 'expCooperatorOrder', arguments);
        },
        expCooperatorPayment: function () {
            return rpcService.request('export-cooperator-payments-blanks', 'expCooperatorPayment', arguments);
        }
    };
    

}));
