
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Cooperators/Cooperators'], factory);
    } else {
        Namespace('TF.Rpc.Cooperators');
        // Browser globals (root is window)
        root.TF.Rpc.Cooperators.SummaryReportCooperatorsGrid = factory(TF.Rpc.Cooperators);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        read: function () {
            return rpcService.request('summary-report-cooperators', 'read', arguments);
        },
        expSummaryReport: function () {
            return rpcService.request('summary-report-cooperators', 'expSummaryReport', arguments);
        },
        removeFile: function () {
            return rpcService.request('summary-report-cooperators', 'removeFile', arguments);
        }
    };
    

}));
