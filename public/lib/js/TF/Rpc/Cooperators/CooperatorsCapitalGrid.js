
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Cooperators/Cooperators'], factory);
    } else {
        Namespace('TF.Rpc.Cooperators');
        // Browser globals (root is window)
        root.TF.Rpc.Cooperators.CooperatorsCapitalGrid = factory(TF.Rpc.Cooperators);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        read: function () {
            return rpcService.request('cooperator-capital', 'read', arguments);
        },
        create: function () {
            return rpcService.request('cooperator-capital', 'create', arguments);
        },
        createReversal: function () {
            return rpcService.request('cooperator-capital', 'createReversal', arguments);
        }
    };
    

}));
