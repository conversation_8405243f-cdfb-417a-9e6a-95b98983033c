
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Cooperators/Cooperators'], factory);
    } else {
        Namespace('TF.Rpc.Cooperators');
        // Browser globals (root is window)
        root.TF.Rpc.Cooperators.ExportCooperatorsBlank = factory(TF.Rpc.Cooperators);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
    	expCooperatorsBlank: function () {
            return rpcService.request('export-cooperators-blank', 'expCooperatorsBlank', arguments);
        },
        removeFile: function () {
            return rpcService.request('export-cooperators-blank', 'removeFile', arguments);
        }
    };
    

}));
