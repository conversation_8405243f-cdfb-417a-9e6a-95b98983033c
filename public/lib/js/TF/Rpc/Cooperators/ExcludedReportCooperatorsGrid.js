
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Cooperators/Cooperators'], factory);
    } else {
        Namespace('TF.Rpc.Cooperators');
        // Browser globals (root is window)
        root.TF.Rpc.Cooperators.ExcludedReportCooperatorsGrid = factory(TF.Rpc.Cooperators);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        read: function () {
            return rpcService.request('excluded-report-cooperators', 'read', arguments);
        },
        expExcludedReport: function () {
            return rpcService.request('excluded-report-cooperators', 'expExcludedReport', arguments);
        },
        removeFile: function () {
            return rpcService.request('excluded-report-cooperators', 'removeFile', arguments);
        }
    };
    

}));
