
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Agreements/Agreements'], factory);
    } else {
        Namespace('TF.Rpc.Agreements');
        // Browser globals (root is window)
        root.TF.Rpc.Agreements.AgreementsGrid = factory(TF.Rpc.Agreements);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {  
    
	    /**
	     * @api-method read
	     * @param  integer $page      pagination parameter
	     * @param  integer $rows      pagination parameter
	     * @param  string  $sort      pagination parameter
	     * @param  string  $order     pagination parameter
	     * @return array 
	     *         {
	     *             #item array rows 
	     *             {
	     *                 #item string  farming_name
	     *                 #item string  status
	     *                 #item integer status_id
	     *                 #item string  agreement_type
	     *             }
	     *             #item integer total
	     *         }
	     */     
        read: function () {
            return rpcService.request('agreements-datagrid', 'read', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        }
    }

}));
