
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Agreements/Agreements'], factory);
    } else {
        Namespace('TF.Rpc.Agreements');
        // Browser globals (root is window)
        root.TF.Rpc.Agreements.AgreementsDataGrid = factory(TF.Rpc.Agreements);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {
        /**
         * @param  array $rpcParams 
         *         {
         *             #item integer agreement_id
         *             #item string imot
         *             #item string masiv
         *             #item string ekate
         *         }
         * @param integer page         paginatino parameter
         * @param integer rows         paginatino parameter
         * @param string  sort         paginatino parameter
         * @param string  order         paginatino parameter
         * @return array {
         *             #item rows array 
         *             {
         *                 #item integer agreement_id
         *                 #item float   area
         *                 #item string  ekate
         *                 #item integer gid
         *                 #item boolean has_match
         *                 #item integer id
         *                 #item string  imot
         *                 #item string  land
         *                 #item string  masiv
         *             }
         *             #item integer total
         *         }
         */       
        read: function () {
            return rpcService.request('agreements-data-datagrid', 'read', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        /**
         * Добавя нов имот към избраното споразумение
         * @param array $rpcParams 
         *        {
         *            #item string  ekate
         *            #item string  masiv
         *            #item string  imot
         *            #item float   area
         *            #item integer agreement_id
         *        }
         * @return boolean
         */       
        addAgreementData: function () {
            return rpcService.request('agreements-data-datagrid', 'addAgreementData', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        }, 
        /**
         * Изтрива избраният имот от споразумението
         * @param  array $rpcParams 
         *         {
         *             #item integer agreement_id
         *             #item integer plot_id
         *             #item integer agreement_data_id
         *             
         *         }
         * @return void
         */     
        deleteAgreementData: function () {
            return rpcService.request('agreements-data-datagrid', 'deleteAgreementData', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        }
    }

}));
