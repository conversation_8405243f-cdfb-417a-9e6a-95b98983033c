
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Agreements/Agreements'], factory);
    } else {
        Namespace('TF.Rpc.Agreements');
        // Browser globals (root is window)
        root.TF.Rpc.Agreements.AgreementsEkateCombobox = factory(TF.Rpc.Agreements);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {
        /**
         * Връща всички ЕКАТТЕ, включени в зареденото споразумение
	     * @param  array filterObj 
	     *         {
	     *           #item integer agreement_id
	     *           #item boolean selected
	     *         }
	     * @return array
	     */       
        read: function () {
            return rpcService.request('agreements-ekate-combobox', 'read', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        }
    }

}));
