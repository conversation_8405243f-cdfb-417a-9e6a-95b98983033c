
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Agreements/Agreements'], factory);
    } else {
        Namespace('TF.Rpc.Agreements');
        // Browser globals (root is window)
        root.TF.Rpc.Agreements.AgreementsLayersTree = factory(TF.Rpc.Agreements);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {   
        /**
         * Връща съществуващите споразумения
         * @return array 
         *         {
         *             #item array children 
         *             {
         *                 #item string  iconCls
         *                 #item integer id
         *                 #item integer status
         *                 #item string  text
         *             }  
         *             #item integer id
         *             #item string  state
         *             #item string  text
         *         }
         */    
        read: function () {
            return rpcService.request('agreements-layers-tree', 'read', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },

        /**
         * Добавя ново споразумение
         * @param array $rpcParams 
         *        {
         *            #item integer year 
         *            #item string  name 
         *            #item integer farming 
         *            #item integer agg_type 
         *        }
         * @return void
         */       
        addAgreement: function () {
            return rpcService.request('agreements-layers-tree', 'addAgreement', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        }, 
        
        /**
         * Изтрива избраното споразумение
         * @param  integer $rpcParam
         * @return void
         */      
        deleteAgreement: function () {
            return rpcService.request('agreements-layers-tree', 'deleteAgreement', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },  
        
        /**
         * Пресъздава споразумението, ако към избраното споразумение има добавени данни
         * @param  integer $agreementId - ID на избраното споразумение, към което да се добавят данни от слой "Комасация"
         * @return integer 200 - статус код 200, ако операцията е била успешна.
         * @throws MTRpcException -33152 NON_EXISTING_CONTRACT_OR_PLOT_NUMBER - aко не е намерено споразумение с това ID
         * @throws MTRpcException -33061 EMPTY_LAYER_TABLE - ако таблицата съществува, но е празна
        */    
        recreateAgreement: function () {
            return rpcService.request('agreements-layers-tree', 'recreateAgreement', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        }, 
        
        /**
         * Добавяне на имоти към споразумение, чрез пресичане със слой "Данни от комасация"
         * @param  integer $agreementId - ID на избраното споразумение, към което да се добавят данни от слой "Комасация"
         * @return integer 200 - статус код 200, ако операцията е била успешна.
         * @throws MTRpcException -33152 NON_EXISTING_CONTRACT_OR_PLOT_NUMBER - aко не е намерено споразумение с това ID
         * @throws MTRpcException -33102 DATABASE_INVALID_TABLE_NAME - ако не съществува таблица за комасация за тази годината и стопанството от споразумението
         * @throws MTRpcException -33061 EMPTY_LAYER_TABLE - ако таблицата съществува, но е празна
         */
        kmsIntersection: function () {
            return rpcService.request('agreements-layers-tree', 'kmsIntersection', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        }
    }

}));
