
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Agreements/Agreements'], factory);
    } else {
        Namespace('TF.Rpc.Agreements');
        // Browser globals (root is window)
        root.TF.Rpc.Agreements.AgreementsMap = factory(TF.Rpc.Agreements);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return { 
    
	    /**
	     * @return array 
	     *         {
	     *             #item array 
	     *             {
	     *                 #item string  color
	     *                 #item string  extent
	     *                 #item integer id
	     *                 #item integer layer_type
	     *                 #item string  name
	     *             }
	     *             #item array 
	     *             {
	     *                 #item string  extent
	     *                 #item integer id
	     *                 #item string  name
	     *             }
	     *         }
	     */
        initMap: function () {
            return rpcService.request('agreements-map', 'initMap', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        /**
	     * @param  array $rpcParams 
	     *         {
	     *             #item integer agreement_id
	     *             #item integer farming
	     *             #item integer year
	     *         }
	     * @return array 
	     *         {
	     *             #item array colorarray 
	     *             {
	     *                 #item string color
	     *                 #item string iconCls
	     *                 #item string name
	     *             }
	     *             #item string extent
	     *         }
	     */       
        initKvs: function () {
            return rpcService.request('agreements-map', 'initKvs', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        }
    }

}));
