(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Diary/Diary'], factory);
    } else {
        Namespace('TF.Rpc.Diary');
        // Browser globals (root is window)
        root.TF.Rpc.Diary.DiaryReportsGrid = factory(TF.Rpc.Diary);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        summaryByPerformer: function () {
            return rpcService.request('diary-reports-grid', 'summaryByPerformer', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        detailedByPerformer: function () {
            return rpcService.request('diary-reports-grid', 'detailedByPerformer', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        detailedBy: function () {
            return rpcService.request('diary-reports-grid', 'detailedBy', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        getReportByPlot: function () {
            return rpcService.request('diary-reports-grid', 'getReportByPlot', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        createFuelDiary: function () {
            return rpcService.request('diary-reports-grid', 'createFuelDiary', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        createChemicalDiary: function () {
            return rpcService.request('diary-reports-grid', 'createChemicalDiary', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        createChemicalDiaryWord: function () {
            return rpcService.request('diary-reports-grid', 'createChemicalDiaryWord', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        exportPerformerSummaryReportXLS: function () {
            return rpcService.request('diary-reports-grid', 'exportPerformerSummaryReportXLS', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        exportPerformerDetailedReportXLS: function () {
            return rpcService.request('diary-reports-grid', 'exportPerformerDetailedReportXLS', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        exportPerformerDetailedReportByXLS: function () {
            return rpcService.request('diary-reports-grid', 'exportPerformerDetailedReportByXLS', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        exportPlotsReportXLS: function () {
            return rpcService.request('diary-reports-grid', 'exportPlotsReportXLS', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        deleteFile: function () {
            return rpcService.request('diary-reports-grid', 'deleteFile', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },

        call: function () {
            const args = Array.from(arguments);
            const methodName = args.shift(); // Gets first item and removes it
            return rpcService.request('diary-reports-grid', methodName, args);
        }
    };

}));
