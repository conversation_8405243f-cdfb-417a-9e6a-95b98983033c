
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Diary/Diary'], factory);
    } else {
        Namespace('TF.Rpc.Diary');
        // Browser globals (root is window)
        root.TF.Rpc.Diary.DiaryAuxiliaryItems = factory(TF.Rpc.Diary);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {     
        itemEditMark: function () {
            return rpcService.request('diary-auxiliary-items', 'itemEditMark', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },       
        saveItem: function () {
            return rpcService.request('diary-auxiliary-items', 'saveItem', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        syncWarehouseItems: function () {
            return rpcService.request('diary-auxiliary-items', 'syncWarehouseItems', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        checkProductAvailability: function () {
            return rpcService.request('diary-auxiliary-items', 'checkProductAvailability', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        }
    }

}));
