
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Diary/Diary'], factory);
    } else {
        Namespace('TF.Rpc.Diary');
        // Browser globals (root is window)
        root.TF.Rpc.Diary.WialonActions = factory(TF.Rpc.Diary);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {
        getFuelUnitsPlot: function () {
            return rpcService.request('wialon-actions', 'getFuelUnitsPlot', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        getAll: function () {
            return rpcService.request('wialon-actions', 'getAll', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        getMessages: function () {
            return rpcService.request('wialon-actions', 'getMessages', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },     
        getWialonReportData: function () {
            return rpcService.request('wialon-actions', 'getWialonReportData', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },     
        getWialonMachines: function () {
            return rpcService.request('wialon-actions', 'getWialonMachines', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },     
        getWialonDrivers: function () {
            return rpcService.request('wialon-actions', 'getWialonDrivers', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },     
        getWialonMachineTypes: function () {
            return rpcService.request('wialon-actions', 'getWialonMachineTypes', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        addWialonToken: function () {
            return rpcService.request('wialon-actions', 'addWialonToken', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        }
    }

}));
