
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Diary/Diary'], factory);
    } else {
        Namespace('TF.Rpc.Diary');
        // Browser globals (root is window)
        root.TF.Rpc.Diary.DiaryExpenses = factory(TF.Rpc.Diary);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {     
        read: function () {
            return rpcService.request('diary-expenses-grid', 'read', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },       
        createExpenses: function () {
            return rpcService.request('diary-expenses-grid', 'createExpenses', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },       
        updateExpenses: function () {
            return rpcService.request('diary-expenses-grid', 'updateExpenses', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },       
        loadExpenses: function () {
            return rpcService.request('diary-expenses-grid', 'loadExpenses', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        deleteExpenses: function () {
            return rpcService.request('diary-expenses-grid', 'deleteExpenses', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        }
    }

}));
