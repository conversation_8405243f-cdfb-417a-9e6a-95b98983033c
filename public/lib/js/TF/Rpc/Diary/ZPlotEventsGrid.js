
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Diary/Diary'], factory);
    } else {
        Namespace('TF.Rpc.Diary');
        // Browser globals (root is window)
        root.TF.Rpc.Diary.ZPlotEventsGrid = factory(TF.Rpc.Diary);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {     
        read: function () {
            return rpcService.request('diary-zplot-events-grid', 'read', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },     
        getEventInfo: function () {
            return rpcService.request('diary-zplot-events-grid', 'getEventInfo', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },     
        singleEditEvent: function () {
            return rpcService.request('diary-zplot-events-grid', 'singleEditEvent', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },     
        saveEvent: function () {
            return rpcService.request('diary-zplot-events-grid', 'saveEvent', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        deleteSingleEvent: function () {
            return rpcService.request('diary-zplot-events-grid', 'deleteSingleEvent', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        loadExpensesForEvent: function () {
            return rpcService.request('diary-expenses-grid', 'loadExpensesForEvent', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        }
    }

}));
