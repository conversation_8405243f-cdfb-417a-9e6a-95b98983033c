
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Diary/Diary'], factory);
    } else {
        Namespace('TF.Rpc.Diary');
        // Browser globals (root is window)
        root.TF.Rpc.Diary.ZPTree = factory(TF.Rpc.Diary);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {     
        read: function () {
            return rpcService.request('diary-zp-tree', 'read', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },     
        markForEdit: function () {
            return rpcService.request('diary-zp-tree', 'markForEdit', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },     
        saveEdit: function () {
            return rpcService.request('diary-zp-tree', 'saveEdit', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        }
    }

}));
