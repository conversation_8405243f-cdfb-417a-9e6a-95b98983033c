Namespace('TF.Rpc.Diary');

TF.Rpc.Diary.DiaryMap = (function (rpcService) {

    return {
        getMapZPlotInfo: function() {
            return rpcService.request(
                "diary-map",
                "getMapZPlotInfo",
                arguments
            ); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        exportAbLines: function() {
            return rpcService.request(
                "diary-map",
                "exportAbLines",
                arguments
            ); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        saveAbLine: function() {
            return rpcService.request(
                "diary-map",
                "saveAbLine",
                arguments
            ); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        delAbLine: function() {
            return rpcService.request(
                "diary-map",
                "delAbLine",
                arguments
            ); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        exportAbLinesTfConnect: function() {
            return rpcService.request(
                "diary-map",
                "exportAbLinesTfConnect",
                arguments
            ); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        editAbLine: function() {
            return rpcService.request(
                "diary-map",
                "editAbLine",
                arguments
            ); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        copyAbLines: function () {
            return rpcService.request(
                "diary-map",
                "copyAbLines",
                arguments
            )
        }
    };
}(TF.Rpc.Diary));
