
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Diary/Diary'], factory);
    } else {
        Namespace('TF.Rpc.Diary');
        // Browser globals (root is window)
        root.TF.Rpc.Diary.DiaryConfigs = factory(TF.Rpc.Diary);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {     
        getConfigs: function () {
            return rpcService.request('diary-configs-grid', 'read', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        }
    }

}));
