
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/ThematicMaps/ThematicMaps'], factory);
    } else {
        Namespace('TF.Rpc.ThematicMaps');
        // Browser globals (root is window)
        root.TF.Rpc.ThematicMaps.ThematicMapsLayers = factory(TF.Rpc.ThematicMaps);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        read: function () {
            return rpcService.request('thematic-maps-layers', 'read', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        print: function () {
            return rpcService.request('thematic-maps-layers', 'print', arguments);
        },
        delete: function () {
            return rpcService.request('thematic-maps-layers', 'delete', arguments);
        }
    };

}));
