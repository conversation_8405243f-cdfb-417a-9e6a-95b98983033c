
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/ThematicMaps/ThematicMaps'], factory);
    } else {
        Namespace('TF.Rpc.ThematicMaps');
        // Browser globals (root is window)
        root.TF.Rpc.ThematicMaps.ThematicMapsResultsGrid = factory(TF.Rpc.ThematicMaps);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        read: function () {
            return rpcService.request('thematic-maps-results-grid', 'read', arguments); //"arguments" e ключова системна дума и не трябва да се променя!
        },
        save: function () {
            return rpcService.request('thematic-maps-results-grid', 'save', arguments);
        },
        load: function () {
            return rpcService.request('thematic-maps-results-grid', 'load', arguments);
        },
    };

}));
