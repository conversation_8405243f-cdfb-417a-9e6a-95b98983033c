
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Contracts/Contracts'], factory);
    } else {
        Namespace('TF.Rpc.Contracts');
        // Browser globals (root is window)
        root.TF.Rpc.Contracts.EditPlotAreas = factory(TF.Rpc.Contracts);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        markForEditPlotAreas: function () {
            return rpcService.request('contracts-edit-plot-areas', 'markForEditPlotAreas', arguments);
        },
        saveEditPlotAreas: function() {
            return rpcService.request('contracts-edit-plot-areas', 'saveEditPlotAreas', arguments);
        }
    }
    

}));
