
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Contracts/Contracts'], factory);
    } else {
        Namespace('TF.Rpc.Contracts');
        // Browser globals (root is window)
        root.TF.Rpc.Contracts.ContractsPlotsGrid = factory(TF.Rpc.Contracts);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        read: function () {
            return rpcService.request('contracts-plots-datagrid', 'read', arguments);
        },
        addContractPlotRelation: function() {
            return rpcService.request('contracts-plots-datagrid', 'addContractPlotRelation', arguments);
        },
        addConfirmedContractPlotRelation: function () {
            return rpcService.request('contracts-plots-datagrid', 'addConfirmedContractPlotRelation', arguments);
        },
        deleteContractPlotRelation: function() {
            return rpcService.request('contracts-plots-datagrid', 'deleteContractPlotRelation', arguments);
        },
        saveContractPrice: function() {
            return rpcService.request('contracts-plots-datagrid', 'saveContractPrice', arguments);
        },
        getContractPrice: function() {
            return rpcService.request('contracts-plots-datagrid', 'getContractPrice', arguments);
        }
    }
    

}));
