
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Contracts/Contracts'], factory);
    } else {
        Namespace('TF.Rpc.Contracts');
        // Browser globals (root is window)
        root.TF.Rpc.Contracts.ContractsTree = factory(TF.Rpc.Contracts);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        read: function () {
            return rpcService.request('contracts-tree', 'read', arguments);
        },
        add: function() {
            return rpcService.request('contracts-tree', 'add', arguments);
        },
        checkForExistance: function() {
            return rpcService.request('contracts-tree', 'checkForExistance', arguments);
        },
        changeActiveStatus: function() {
            return rpcService.request('contracts-tree', 'changeActiveStatus', arguments);
        },
        contractCopy: function() {
            return rpcService.request('contracts-tree', 'contractCopy', arguments);
        },
        markForEdit: function() {
            return rpcService.request('contracts-tree', 'markForEdit', arguments);
        },
        addAnnex: function() {
            return rpcService.request('contracts-tree', 'addAnnex', arguments);
        },
        deleteContracts: function() {
            return rpcService.request('contracts-tree', 'deleteContracts', arguments);
        },
        hasContractEditedPlots: function() {
          return rpcService.request('contracts-tree', 'hasContractEditedPlots', arguments);  
        },
        initAreaEqualize: function() {
          return rpcService.request('contracts-tree', 'initAreaEqualize', arguments);  
        },
        getAllFilteredContracts: function() {
          return rpcService.request('contracts-tree', 'getAllFilteredContracts', arguments);  
        },
        saveMultiEditContracts: function() {
          return rpcService.request('contracts-tree', 'saveMultiEditContracts', arguments);  
        },
        multiCopyContracts: function() {
          return rpcService.request('contracts-tree', 'multiCopyContracts', arguments);
        },
        checkForLongerThanYear: function() {
          return rpcService.request('contracts-tree', 'checkForLongerThanYear', arguments);
        },
        initAnnexRenta: function() {
            return rpcService.request('contracts-tree', 'initAnnexRenta', arguments);
        },
        exportFilteredContractsToXLS: function () {
            return rpcService.request('contracts-tree', 'exportFilteredContractsToXLS', arguments);
        },
    }

}));
