
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Contracts/Contracts'], factory);
    } else {
        Namespace('TF.Rpc.Contracts');
        // Browser globals (root is window)
        root.TF.Rpc.Contracts.ContractsFilesGrid = factory(TF.Rpc.Contracts);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        read: function () {
            return rpcService.request('contracts-files-maingrid', 'read', arguments);
        },
        download: function() {
            return rpcService.request('contracts-files-maingrid', 'download', arguments);
        },
        delete: function() {
            return rpcService.request('contracts-files-maingrid', 'delete', arguments);
        },
    }
    

}));
