(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Contracts/Contracts'], factory);
    } else {
        Namespace('TF.Rpc.Contracts');
        // Browser globals (root is window)
        root.TF.Rpc.Contracts.PlotContractsGrid = factory(TF.Rpc.Contracts);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {
        /**
         * reads the contracts that a plot is involved in.
         *
         * @api-method read
         * @param array $params {
         *     #item integer plot_id            id of the plot
         *     #item integer contract_id        id of the contract
         * }
         * @param  int $page         pagination rpc parameter
         * @param  int $rows         pagination rpc parameter
         * @param  string $sort      pagination rpc parameter
         * @param  string $order     pagination rpc parameter
         * @return array {
         *     #item array rows {
         *         #item array {
         *             #item int id
         *             #item string c_num
         *             #item string c_date
         *             #item string nm_usage_rights
         *             #item string sv_num
         *             #item string sv_date
         *             #item string start_date
         *             #item string renta
         *             #item string due_date
         *             #item string  renta_nat
         *             #item int farming_id
         *             #item string comment
         *             #item string agg_type
         *             #item boolean active
         *             #item int parent_id
         *             #item boolean is_annex
         *             #item int renta_nat_type_id
         *             #item boolean is_sublease
         *             #item string original_due_date
         *             #item string original_renta
         *             #item string original_renta_nat
         *             #item string original_renta_nat_type_id
         *             #item string na_num
         *             #item string tom
         *             #item string delo
         *             #item string court
         *             #item string payday
         *             #item boolean is_declaration_subleased
         *             #item string contract_area
         *             #item string price_per_acre
         *             #item string price_sum
         *             #item string active_text
         *             #item int c_type
         *             #item string farming
         *         }
         *     }
         *     #item int total    Total rows
         * }
         */
        read: function (arguments) {
            return rpcService.request('plot-contracts-grid', 'read', arguments);
        },
    }
}));
