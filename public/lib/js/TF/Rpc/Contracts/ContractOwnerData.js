
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Contracts/Contracts'], factory);
    } else {
        Namespace('TF.Rpc.Contracts');
        // Browser globals (root is window)
        root.TF.Rpc.Contracts.ConctractOwnerData = factory(TF.Rpc.Contracts);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        saveRep: function () {
            return rpcService.request('contract-owner-data', 'saveRep', arguments);
        },
        deleteRep: function() {
            return rpcService.request('contract-owner-data', 'deleteRep', arguments);
        },
        addNewPlotOwner: function() {
            return rpcService.request('contract-owner-data', 'addNewPlotOwner', arguments);
        },
        deletePlotOwner: function() {
            return rpcService.request('contract-owner-data', 'deletePlotOwner', arguments);
        },
        multiplyOwners: function() {
            return rpcService.request('contract-owner-data', 'multiplyOwners', arguments);
        },
        multiplyFarming: function() {
            return rpcService.request('contract-owner-data', 'multiplyFarming', arguments);
        },
        deletePCToFarmingRelation: function() {
            return rpcService.request('contract-owner-data', 'deletePCToFarmingRelation', arguments);
        },
        editOwnerPercent: function() {
            return rpcService.request('contract-owner-data', 'editOwnerPercent', arguments);
        },
        changeIsSigner: function() {
            return rpcService.request('contract-owner-data', 'changeIsSigner', arguments);
        }
    }
    

}));
