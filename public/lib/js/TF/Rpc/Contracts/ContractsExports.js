
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Contracts/Contracts'], factory);
    } else {
        Namespace('TF.Rpc.Contracts');
        // Browser globals (root is window)
        root.TF.Rpc.Contracts.ContractsExports = factory(TF.Rpc.Contracts);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        exportContractBlank: function () {
            return rpcService.request('contracts-exports', 'exportContractBlank', arguments);
        },
        deleteFile: function () {
            return rpcService.request('contracts-exports', 'deleteFile', arguments);
        }
    }

}));
