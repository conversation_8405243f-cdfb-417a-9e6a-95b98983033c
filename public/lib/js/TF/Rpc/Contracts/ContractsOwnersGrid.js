
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Contracts/Contracts'], factory);
    } else {
        Namespace('TF.Rpc.Contracts');
        // Browser globals (root is window)
        root.TF.Rpc.Contracts.ContractsOwnersGrid = factory(TF.Rpc.Contracts);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        read: function () {
            return rpcService.request('contracts-owners-datagrid', 'read', arguments);
        },
        add: function() {
            return rpcService.request('contracts-owners-datagrid', 'add', arguments);
        },
        getOwnerHeritors: function() {
            return rpcService.request('contracts-owners-datagrid', 'getOwnerHeritors', arguments);
        },
        addNewOwner: function() {
            return rpcService.request('contracts-owners-datagrid', 'addNewOwner', arguments);
        }
    }
    

}));
