(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Contracts/Contracts'], factory);
    } else {
        Namespace('TF.Rpc.Contracts');
        // Browser globals (root is window)
        root.TF.Rpc.Contracts.ContractsGroupsGrid = factory(TF.Rpc.Contracts);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {
        createContractsGroup: function () {
            return rpcService.request('contracts-groups-datagrid', 'create', arguments);
        },
        readContractsGroups: function () {
            return rpcService.request('contracts-groups-datagrid', 'read', arguments);
        },
        updateContractsGroup: function () {
            return rpcService.request('contracts-groups-datagrid', 'update', arguments);
        },
        deleteContractsGroup: function () {
            return rpcService.request('contracts-groups-datagrid', 'delete', arguments);
        }
    }
}));
