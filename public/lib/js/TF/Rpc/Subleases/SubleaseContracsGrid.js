
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Subleases/Subleases'], factory);
    } else {
        Namespace('TF.Rpc.Subleases');
        // Browser globals (root is window)
        root.TF.Rpc.Subleases.SubleseContractsGrid = factory(TF.Rpc.Subleases);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {       
       /**
         * Saves an owner representative.
         * @param  {objects} representative {
         *     @item integer id
         *     @item string rep_name
         *     @item string rep_surname
         *     @item string rep_lastname
         *     @item string rep_egn
         *     @item string rep_lk
         *     @item string rep_lk_izdavane
         *     @item string rep_address
         * }
         *
         * @return {Diferred}       See http://api.jquery.com/category/deferred-object/.
         */
    	read: function () {
            return rpcService.request('sublease-contracts-grid', 'read', arguments); 
        },
    };

}));
