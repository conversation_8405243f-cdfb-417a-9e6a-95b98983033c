
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Subleases/Subleases'], factory);
    } else {
        Namespace('TF.Rpc.Subleases');
        // Browser globals (root is window)
        root.TF.Rpc.Subleases.SubleseContragentsGrid = factory(TF.Rpc.Subleases);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {       

    	/**
         * Reads the contragents to a sublreased conract.
         * @param {objects}  params {
         *     @item integer sublease_id
         *     @item string  type
         *     @item array filters {
         *         @item string contragent_name
         *         @item string egn
         *         @item string eik
         *         @item string company_name
         *     }
         * }
         * @param integer page,
         * @param integer rows,
         * @param string sort,
         * @param string order
         * @return array
         */
    	readContragents: function (formData) {
            return rpcService.request('sublease-contragents-grid', 'readContragents', arguments); 
        }
    };

}));
