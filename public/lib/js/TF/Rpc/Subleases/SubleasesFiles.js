
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Subleases/Subleases'], factory);
    } else {
        Namespace('TF.Rpc.Subleases');
        // Browser globals (root is window)
        root.TF.Rpc.Subleases.SubleasesFiles = factory(TF.Rpc.Subleases);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {       
        downloadAttached: function () {
            return rpcService.request('subleases-files', 'downloadAttached', arguments);
        },
        deleteSubleasesFile: function () {
            return rpcService.request('subleases-files', 'deleteSubleasesFile', arguments);
        }
    }

}));
