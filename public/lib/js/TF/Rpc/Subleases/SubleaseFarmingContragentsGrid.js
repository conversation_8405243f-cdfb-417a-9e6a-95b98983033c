
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Subleases/Subleases'], factory);
    } else {
        Namespace('TF.Rpc.Subleases');
        // Browser globals (root is window)
        root.TF.Rpc.Subleases.SubleseFarmingContragentsGrid = factory(TF.Rpc.Subleases);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {       
     
    	/**
         * Reads the farming contragents to a sublreased conract.
         * @param {objects} $params {
         *     @item integer sublease_id
         *     @item string  type
         * }
         * @param integer page,
         * @param integer rows,
         * @param string sort,
         * @param string order
         * @return array
         */
    	read: function (formData) {
            return rpcService.request('sublease-farming-contragents-grid', 'read', arguments);
        }
    };

}));
