
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Subleases/Subleases'], factory);
    } else {
        Namespace('TF.Rpc.Subleases');
        // Browser globals (root is window)
        root.TF.Rpc.Subleases.SubleasesExports = factory(TF.Rpc.Subleases);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {       
        exportContractBlank: function () {
            return rpcService.request('subleases-exports', 'exportContractBlank', arguments);
        },
        deleteFile: function () {
            return rpcService.request('subleases-exports', 'deleteFile', arguments);
        }
    }

}));
