
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Subleases/Subleases'], factory);
    } else {
        Namespace('TF.Rpc.Subleases');
        // Browser globals (root is window)
        root.TF.Rpc.Subleases.SublesePlotOwnersGrid = factory(TF.Rpc.Subleases);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {       
    	
       /**
         * Reads the owners of a plot of a sublreased conract.
         * @param  {objects}  {
         *	   @item integer pc_rel_id
         *     @item string type
         * }
         *
         * @return array 
         */
    	read: function () {
            return rpcService.request('sublease-plot-owners-grid', 'read', arguments); 
        },
    };

}));
