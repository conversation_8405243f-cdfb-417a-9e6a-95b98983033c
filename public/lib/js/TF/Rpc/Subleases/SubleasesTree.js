
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Subleases/Subleases'], factory);
    } else {
        Namespace('TF.Rpc.Subleases');
        // Browser globals (root is window)
        root.TF.Rpc.Subleases.SubleasesTree = factory(TF.Rpc.Subleases);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {   
    	 read: function (formData) {
             return rpcService.request('subleases-tree', 'read', arguments);
         },
         deleteSublease: function () {
            return rpcService.request('subleases-tree', 'deleteSublease', arguments);
        },
        changeActiveStatus: function () {
            return rpcService.request('subleases-tree', 'changeActiveStatus', arguments);
        },
        checkForExistence: function () {
            return rpcService.request('subleases-tree', 'checkForExistence', arguments);
        },
        saveSublease: function () {
            return rpcService.request('subleases-tree', 'saveSublease', arguments);
        },
        preEdit: function () {
            return rpcService.request('subleases-tree', 'preEdit', arguments);
        },
        markSubleaseForEdit: function () {
            return rpcService.request('subleases-tree', 'markSubleaseForEdit', arguments);
        },
        initSubleaseCopy: function () {
            return rpcService.request('subleases-tree', 'initSubleaseCopy', arguments);
        },
        hasSubleaseEditedPlots: function () {
            return rpcService.request('subleases-tree', 'hasSubleaseEditedPlots', arguments);
        },
        loadSublease: function () {
            return rpcService.request('subleases-tree', 'loadSublease', arguments);
        },
        copySublease: function () {
            return rpcService.request('subleases-tree', 'copySublease', arguments);
        }
    };

}));
