
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Subleases/Subleases'], factory);
    } else {
        Namespace('TF.Rpc.Subleases');
        // Browser globals (root is window)
        root.TF.Rpc.Subleases.SubleaseContragentData = factory(TF.Rpc.Subleases);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    return {
    	deleteSubleaseContragent: function (formData) {
            return rpcService.request('sublease-contragent-data', 'deleteSubleaseContragent', arguments); 
        },
        addSubleaseContragent: function (){
        	return rpcService.request('sublease-contragent-data', 'addSubleaseContragent', arguments);
        }
    };

}));
