
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/Farming/Farming'], factory);
    } else {
        Namespace('TF.Rpc.Farming');
        // Browser globals (root is window)
        root.TF.Rpc.Farming.FarmingGrid = factory(TF.Rpc.Farming);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {


    return {
        read: function () {
            return rpcService.request('farming-grid', 'read', arguments);
        },
        add: function() {
        	return rpcService.request('farming-grid', 'add', arguments);
        },
        markForEdit: function() {
        	return rpcService.request('farming-grid', 'markForEdit', arguments);
        },
        edit: function() {
            return rpcService.request('farming-grid', 'edit', arguments);
        },
        delete: function() {
            return rpcService.request('farming-grid', 'delete', arguments);
        },
        export2xls: function() {
            return rpcService.request('farming-grid', 'export2xls', arguments);
        },
        deteleZipArchive: function() {
        	return rpcService.request('farming-grid', 'deteleZipArchive', arguments);
        },
        getFarmingUsers: function() {
            return rpcService.request('farming-grid', 'getFarmingUsers', arguments);
        }
    }
    

}));
