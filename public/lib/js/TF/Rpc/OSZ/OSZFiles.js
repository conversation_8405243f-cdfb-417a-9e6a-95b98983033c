
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/OSZ/OSZ'], factory);
    } else {
        Namespace('TF.Rpc.OSZ');
        // Browser globals (root is window)
        root.TF.Rpc.OSZ.OSZFiles = factory(TF.Rpc.OSZ);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {
        /**
         * Returns list of osz files matching filters
         * @api-method read
         * @param array $filterParams {
         *   #item string land
         *   #item string ekatte
         *   #item string date
         * }
         * @param integer $page
         * @param integer $rows
         * @param string $sort
         * @param string $order
         * @return {Diferred}       See http://api.jquery.com/category/deferred-object/.
         */
        read: function () {
            return rpcService.request('osz-files', 'read', arguments);
        },
        /**
         * Deletes osz file
         * @api-method delete
         * @param integer $recordId
         * @return {Diferred}       See http://api.jquery.com/category/deferred-object/.
         */
        delete: function () {
            return rpcService.request('osz-files', 'delete', arguments);
        }
    };

}));
