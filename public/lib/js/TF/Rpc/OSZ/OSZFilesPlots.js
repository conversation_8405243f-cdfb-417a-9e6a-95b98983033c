
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/OSZ/OSZ'], factory);
    } else {
        Namespace('TF.Rpc.OSZ');
        // Browser globals (root is window)
        root.TF.Rpc.OSZ.OSZFilesPlots = factory(TF.Rpc.OSZ);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {
        /**
         * Returns list of osz files plots
         * @api-method read
         * @param array $filterParams {
         *   #item integer file_id
         *   #item string ekatte
         *   #item string kad_no
         *   #item string ime_subekt
         *   #item string egn_subekt
         *   #item string kategoria
         *   #item string txt_ntp
         *   #item boolean not_in_kvs
         *   #item boolean only_in_kvs
         *   #item boolean different_subekt
         *   #item string action new||add
         * }
         * @param integer $page
         * @param integer $rows
         * @param string $sort
         * @param string $order
         * @return {Diferred}       See http://api.jquery.com/category/deferred-object/.
         */
        read: function () {
            return rpcService.request('osz-files-plots', 'read', arguments);
        },

        /**
         * Returns url for the generated excel file
         * @api-method exportExcel
         * @param array $filterParams {
         *   #item integer file_id
         *   #item string ekatte
         *   #item string kad_no
         *   #item string ime_subekt
         *   #item string egn_subekt
         *   #item string kategoria
         *   #item string txt_ntp
         *   #item boolean not_in_kvs
         *   #item boolean only_in_kvs
         *   #item boolean different_subekt
         *   #item string action new||add
         * }
         */
        exportExcel: function () {
            return rpcService.request('osz-files-plots', 'exportExcel', arguments);
        },
        deleteFile: function () {
            return rpcService.request('osz-files-plots', 'deleteFile', arguments);
        },
        importOwners: function () {
            return rpcService.request('osz-files-plots', 'importOwners', arguments);
        },

    };

}));
