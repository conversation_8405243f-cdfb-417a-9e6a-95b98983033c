
(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['TF/Rpc/KVSContractsUpdate/KVSContractsUpdate'], factory);
    } else {
        Namespace('TF.Rpc.KVSContractsUpdate');
        // Browser globals (root is window)
        root.TF.Rpc.KVSContractsUpdate.KVSContractsPlotsForUpdateGrid = factory(TF.Rpc.KVSContractsUpdate);
    }
}(typeof self !== 'undefined' ? self : this, function (rpcService) {

    
    return {       
       /**
         * Exports layer in .shp
         * @param  {objects} data {
         *     @item string export_type
         *     @item string layer_name
         *     @item string layer_type
         *     @item string layer_id
         *     @item boolean united
         *     @item boolean export_old
         *     @item boolean use_filter
         *     @item array selected_ids
         * }
         *
         * @return {Diferred}       See http://api.jquery.com/category/deferred-object/.
         */
        read: function () {
            return rpcService.request('kvs-contracts-plots-for-update-grid', 'read', arguments); 
        },
        update: function () {
        	return rpcService.request('kvs-contracts-plots-for-update-grid', 'update', arguments);
        }
    };

}));
