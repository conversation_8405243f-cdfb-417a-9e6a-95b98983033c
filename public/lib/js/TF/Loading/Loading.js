(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define(['jquery'], factory);
    } else {
        Namespace('TF.Loading');
        // Browser globals (root is window)
        root.TF.Loading = factory(jQuery);
    }
}(typeof self !== 'undefined' ? self : this, function ($) {
	return {
		start: function () {
			$.messager.progress({
                msg: 'Моля изчакайте',
                text: 'Зареждане...',
                interval: 200,
                openAnimation: 'fade',
                closeAnimation: 'fade',
                shadow: false,
                onOpen: function (){
                    jQuery('.window-mask').hide();
                },
                onBeforeOpen: function (){
                    jQuery('#progress-mask').fadeIn(500);
                },
                onBeforeClose: function () {
                    jQuery('#progress-mask').fadeOut(500);
                }
            });
		},
		end: function () {
			$.messager.progress('close');
		}
	};
}));
