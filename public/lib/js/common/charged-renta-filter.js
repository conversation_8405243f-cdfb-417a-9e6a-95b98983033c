let optionListOwnersFilter = [];
let optionListRepsFilter = [];
let optionListCompaniesFilter = [];

function initFilterButtons() {
    jQuery("#btn-open-renta-history-filter").bind("click", function() {

        jQuery('#win-filter-renta-history').window('resize', {
            height: getZoomedWindowHeight(700),
            width: 670
        });

        jQuery("#win-filter-renta-history").window("open");
        jQuery("#win-filter-renta-history").window("center");
        return false;
    });

    jQuery("#btn-clear-renta-history-filter").bind("click", function() {
        filterRentaHistoryTree(true);
        clearRentaHistoryFilter();
        return false;
    });

    jQuery("#btn-filter-renta-history").bind("click", function() {
        filterRentaHistoryTree();
        jQuery("#win-filter-renta-history").window("close");
        return false;
    });

    jQuery("#clear-acr-tree-filter").bind("click", function() {
        clearRentaHistoryFilter();
    }); 
}

function initRentaHistoryFilter(ComboboxData) {
    const farmingYears = ComboboxData.FarmingYearCombobox.map((element, index) => {
        element.selected = false;
        return element ;
      });

    jQuery("#filter-acr-year > input").combobox({
        data: farmingYears,
        valueField: "id",
        textField: "farming_year",
        editable: false
    });

    jQuery("div#win-filter-renta-history #acr-type > input").validatebox({
        validType: {
            length: [0, 120]
        },
        required: false,
        missingMessage: "Това поле е задължително!"
    });

    jQuery('div#win-filter-renta-history #renta-nat-filter').combobox({
        url: 'index.php?common-rpc=renta-types-combobox',
        rpcParams: [{
            as_list: true
        }],
        valueField: 'id',
        textField: 'name',
        width: 205,
        onLoadSuccess: function () {

        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery("div#win-filter-renta-history #acr-owner-type").combobox({
        data: [
            {
                id: "",
                title: "Всички",
                selected: true
            },
            {
                id: "0",
                title: "Юридически лица"
            },
            {
                id: "1",
                title: "Физически лица"
            }
        ],
        valueField: "id",
        textField: "title",
        editable: false,
        multiple: false,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery("div#win-filter-renta-history #acr-renta-filter > input").textbox({
        min: 0,
        parser: function(value) {
            if (value == "-") {
                return value;
            }
            if (jQuery.isNumeric(value)) {
                return parseFloat(value).toFixed(2);
            }
        }
    });

    jQuery("div#win-filter-renta-history #acr-owner-name-filter > input").fcbkcomplete({
        json_url: "index.php?owners-rpc=owners-list",
        rpcParams: ["owners"],
        cache: true,
        firstselected: true,
        addontab: true,
        complete_text: "Търси собственик...",
        width: 220,
        maxitimes: 20,
        maxshownitems: 20,
        height: 5,
        onselect: function() {
            jQuery("div#win-filter-renta-history #acr-owner-name-text-filter > option:selected").each(function() {
                var id = jQuery(this).val();

                if (jQuery.inArray(id, optionListOwnersFilter) < 0) {
                    optionListOwnersFilter.push(id);
                }
            });
        },
        onremove: function() {
            var id = jQuery("div#win-filter-renta-history #acr-owner-name-text-filter > option:selected").val();
            var index = optionListOwnersFilter.indexOf(id);
            if (index > -1) {
                optionListOwnersFilter.splice(index, 1);
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery("div#win-filter-renta-history #acr-rep-name-filter > input").fcbkcomplete({
        json_url: "index.php?owners-rpc=owners-list",
        rpcParams: ["reps"],
        cache: true,
        firstselected: true,
        addontab: true,
        complete_text: "Търси представител...",
        width: 222,
        maxitimes: 20,
        maxshownitems: 20,
        height: 5,
        onselect: function() {
            jQuery("div#win-filter-renta-history #acr-rep-name-text-filter > option:selected").each(function() {
                var id = jQuery(this).val();
                if (jQuery.inArray(id, optionListRepsFilter) < 0) {
                    optionListRepsFilter.push(id);
                }
            });
        },
        onremove: function() {
            var id = jQuery("div#win-filter-renta-history #acr-rep-name-text-filter > option:selected").val();
            var index = optionListRepsFilter.indexOf(id);

            if (index > -1) {
                optionListRepsFilter.splice(index, 1);
            }
        }
    });

    jQuery("div#win-filter-renta-history #acr-company-name-filter > input").fcbkcomplete({
        json_url: "index.php?owners-rpc=owners-list",
        rpcParams: ["companies"],
        cache: true,
        firstselected: true,
        addontab: true,
        complete_text: "Търси фирма...",
        width: 222,
        maxitimes: 20,
        maxshownitems: 20,
        height: 5,
        onselect: function() {
            jQuery("div#win-filter-renta-history #acr-company-name-text-filter > option:selected").each(function() {
                var id = jQuery(this).val();

                if (jQuery.inArray(id, optionListCompaniesFilter) < 0) {
                    optionListCompaniesFilter.push(id);
                }
            });
        },
        onremove: function() {
            var id = jQuery("div#win-filter-renta-history #acr-company-name-text-filter > option:selected").val();
            var index = optionListCompaniesFilter.indexOf(id);

            if (index > -1) {
                optionListCompaniesFilter.splice(index, 1);
            }
        }
    });

    jQuery('div#win-filter-renta-history #acr-plot-ekatte > input').combobox({
		url: 'index.php?common-rpc=ekate-combobox',
		rpcParams: [{
			record_all: true
		}],
		valueField: 'ekate',
		textField: 'text',
		filter: function(q, row){
			var opts = jQuery(this).combobox('options');
			var text = row[opts.textField].toLowerCase();
			var value = row[opts.valueField];
			var find = q.toLowerCase();
			if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
			{
				return true;
			}
		},
        onSelect: function(selected) {
            if (selected.ekate) {
                updateMestnostCombobox(selected.ekate, "div#win-filter-renta-history #acr-plot-mestnost");
            } else {
                jQuery("div#win-filter-renta-history  #acr-plot-mestnost").combobox({ disabled: true });
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

	jQuery('div#win-filter-renta-history #acr-plot-area-type > input').combobox({
		url: 'index.php?common-rpc=plot-ntp-combobox&record_all=true',
		valueField: 'id',
		textField: 'name',
		editable: false,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

	jQuery('div#win-filter-renta-history #acr-plot-category > input').combobox({
		url: 'index.php?common-rpc=plot-category-combobox&record_all=true',
		valueField: 'id',
		textField: 'name',
		multiple: true,
		onHidePanel: onHidePanelMultiSelect,
		editable: false,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

    jQuery("div#win-filter-renta-history  #acr-plot-mestnost").combobox({ disabled: true });
}

function intFilterFarming(data) {
    jQuery("div#win-filter-renta-history #acr-farming > input").combobox({
        data: data,
        valueField: "id",
        textField: "name",
        editable: false
    });
}

function getRentaHistoryFilterParams() {
    let obj = {};

	let renta = jQuery("div#win-filter-renta-history #acr-renta-filter > input").numberbox('getValue');
    let rentaType = jQuery('div#win-filter-renta-history #renta-nat-filter').combobox('getValue');

   
    obj.renta_type = rentaType;
    obj.renta = null;
    obj.acr_type = jQuery('div#win-filter-renta-history #acr-type > input').val();
	obj.year = jQuery('div#win-filter-renta-history #filter-acr-year > input').combobox('getValue');
	obj.ekate = jQuery('div#win-filter-renta-history #acr-plot-ekatte > input').combobox('getValue');
	obj.masiv = jQuery('div#win-filter-renta-history #acr-plot-masiv-filter > input').val();
	obj.number = jQuery('div#win-filter-renta-history #acr-plot-number-filter > input').val();

    obj.acr_type = jQuery('div#win-filter-renta-history #acr-type > input').val();
	obj.category = jQuery('div#win-filter-renta-history #acr-plot-category > input').combobox('getValues');
	obj.area_type = jQuery('div#win-filter-renta-history #acr-plot-area-type > input').combobox('getValues');

	obj.cnum = jQuery('div#win-filter-renta-history #acr-cnum-filter > input').val();
	obj.contract_type = jQuery('div#win-filter-renta-history #acr-contract-type > input').combobox('getValue');

    if(jQuery('div#win-filter-renta-history #acr-contract-group > input').combobox('getValue') != ''){
        obj.contract_group = jQuery('div#win-filter-renta-history #acr-contract-group > input').combobox('getValue');
    }

    obj.farming = jQuery('div#win-filter-renta-history #acr-farming > input').combobox('getValue');


	obj.owner_ids = optionListOwnersFilter;
	obj.owner_egn = jQuery('div#win-filter-renta-history #acr-owner-egn > input').val();

	obj.rep_ids = optionListRepsFilter;
	obj.rep_egn = jQuery('div#win-filter-renta-history #acr-rep-egn > input').val();
	obj.company_ids = optionListCompaniesFilter;
	obj.company_eik = jQuery('div#win-filter-renta-history #acr-company-eik-filter > input').val();

    obj.mestnost =jQuery('div#win-filter-renta-history #acr-plot-mestnost').combobox('getValues');



	if(obj.contract_type === '0') {
		obj.contract_type = null;
	}

	if(renta)
	{
		obj.renta = renta;
	}

	obj.renta_nat = 1;
	obj.owner_type = jQuery('div#win-filter-renta-history #acr-owner-type').combobox('getValue');

    return obj;
}

function filterRentaHistoryTree(clearFilter = false) {
    if (clearFilter === true) {
        return initChargedRentaHistoryTree(1);
    }

    let filter = getRentaHistoryFilterParams();
    initChargedRentaHistoryTree(1, filter);
}

function clearRentaHistoryFilter() {
    jQuery("div#win-filter-renta-history #filter-acr-year > input").combobox("reset");

    jQuery("div#win-filter-renta-history #acr-type > input").val("");

    var tmpOptionListOwnersFilter = optionListOwnersFilter.slice(0);

    for (var i = 0; i < tmpOptionListOwnersFilter.length; i++) {
        jQuery("#acr-owner-name-filter > input").trigger("removeItem", [
            { value: tmpOptionListOwnersFilter[i] }
        ]);
    }
    optionListOwnersFilter = [];

    jQuery("div#win-filter-renta-history #acr-owner-egn > input").val("");

    var tmpOptionListRepsFilter = optionListRepsFilter.slice(0);
    for (var i = 0; i < tmpOptionListRepsFilter.length; i++) {
        jQuery("#acr-rep-name-filter > input").trigger("removeItem", [
            { value: tmpOptionListRepsFilter[i] }
        ]);
    }
    optionListRepsFilter = [];

    jQuery("div#win-filter-renta-history #acr-rep-egn > input").val("");

    var tpmOptionListCompaniesFilter = optionListCompaniesFilter.slice(0);
    for (var i = 0; i < tpmOptionListCompaniesFilter.length; i++) {
        jQuery("div#win-filter-renta-history #acr-company-name-filter > input").trigger("removeItem", [
            { value: tpmOptionListCompaniesFilter[i] }
        ]);
    }
    optionListCompaniesFilter = [];

    jQuery("div#win-filter-renta-history #acr-company-eik-filter > input").val("");
    jQuery("div#win-filter-renta-history #acr-cnum-filter > input").val("");
    jQuery("div#win-filter-renta-history #acr-contract-type > input").combobox("reset");
    jQuery("div#win-filter-renta-history #acr-contract-group > input").combobox("reset");
    jQuery("div#win-filter-renta-history #acr-farming > input").combobox("reset");
    jQuery("div#win-filter-renta-history #acr-all-contracts > input").click();
    jQuery("div#win-filter-renta-history #acr-contract-natura > input").combobox("reset");
    jQuery("div#win-filter-renta-history #acr-plot-ekatte > input").combobox("reset");
    jQuery("div#win-filter-renta-history #acr-plot-masiv-filter > input").val("");
    jQuery("div#win-filter-renta-history #acr-plot-number-filter > input").val("");
    jQuery("div#win-filter-renta-history #acr-plot-category > input").combobox("reset");
    jQuery("div#win-filter-renta-history #acr-plot-area-type > input").combobox("clear");
    jQuery("div#win-filter-renta-history #acr-renta-filter > input").textbox("setValue", "");
    jQuery("#renta-nat-filter").combobox("reset");
    jQuery("div#win-filter-renta-history #acr-owner-type").combobox("reset");
}