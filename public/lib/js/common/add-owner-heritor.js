
var newlyAddedOwnerId = 0;
var lastAddedParent = 0;
var lastAddedOwner = 0;

function addOwnerHeritorHelper(owner_id) {
    if (!hasPlotRightsRW) {
        messagerPlotsWriteRights();
        return;
    }

    var selectedHeritor = jQuery('#owners-heritors-tree').tree('getSelected');
    if(owner_id)
    {
        if(selectedHeritor && !selectedHeritor['attributes'].is_dead)
        {
            jQuery.messager.alert('Грешка', 'Не може да добавите наследници на жив човек.');
        }
        else
        {
            jQuery('#search-heritor-by-egn').val('');

            jQuery('#choose-heritor').combobox({
                url: 'index.php?owners-rpc=owners-heritors-combobox',
                valueField: 'id',
                textField: 'owner_names',
                required: true,
                missingMessage: 'Моля изберете наследник.',
                rpcParams: [owner_id, null, null],
                filter: function(q, row){
                    var opts = jQuery(this).combobox('options');
                    var text = row[opts.textField].toLowerCase();
                    var find = q.toLowerCase();
                    if(text.indexOf(find) != -1)
                    {
                        return true;
                    }
                },
                onLoadSuccess: function () {
                    jQuery(this).combobox('reset');
                },
                loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
            });

            jQuery('#win-choose-heritor').window('open');
        }
    }
    else
    {
        jQuery.messager.alert('Грешка', 'Моля изберете собственик.');
    }
}
jQuery(function () {
    jQuery('#btn-save-owner-heritor').bind('click', function() {
        var obj = new Object;
        obj.parent = newlyAddedOwnerId;

        var owner_id = jQuery('#choose-heritor').combobox('getValue');
        obj.parent = lastAddedParent ? lastAddedParent : obj.parent;
        if(!owner_id)
        {
            jQuery.messager.alert('Грешка', 'Моля изберете наследник.');
            return false;
        }
        else
        {
            tmpPath = newlyAddedOwnerId;
            reloadTarget = undefined;
        }
        var GET = {};
        location.search.substr(1).split("&").forEach(function (item) {
            GET[decodeURIComponent(item.split("=")[0])] = decodeURIComponent(item.split("=")[1]);
        });

        if(GET.page == 'Contracts.Home') {
            ownerTables = jQuery('#contract-owners-tables');
        } else if (GET.page == 'Annexes.Home'){
            ownerTables = jQuery('#annex-owners-tables');
        } else if (GET.page == 'Plots.Home') {
            ownerTables = jQuery('#owner-info-tables');
        }
        TF.Rpc.Owners.OwnersHeritorsTree.addOwnerHeritor(owner_id, obj.parent)
        .done(function (dataObj) {
            jQuery('#owners-tree').tree('reload');
            jQuery('#owners-heritors-tree').tree('reload');
            ownerTables.treegrid('loadRpc');
        })
        .fail(function (errorObj) {
            jQuery.messager.alert('Грешка',errorObj.getMessage(),'warning');
        });

        jQuery('#win-choose-heritor').window('close');

    	newlyAddedOwnerId = -1;
    });
});
