function PostPaymentFields (ownerType) 
{
    this.ownerType = ownerType;

    this.getFields = function() {
        return  jQuery("input[class^='"+ this.ownerType +"-']");
    }

    this.getPostPaymentValues = function() {
        let postPaymentArr = jQuery.map( this.getFields(), (field) => {
            const element = jQuery(field);

            let [className, ..._] = element.attr("class").split(" ");

            return {
                'key': [className],
                'value': element.textbox('getValue')
            }
        });

        return Object.fromEntries(postPaymentArr.map((t) => [t.key, t.value]));
    }

    this.hidePostPaymentFields = function () {
        jQuery.map( this.getFields(), (field) => {
            jQuery(field).closest('tr').hide();
        });
    }

    this.showPostPaymentFields = function ( values = '') {
        if (values && values.length) {
            this.fillPostPaymentInputs( values );
        }
       
        jQuery.map( this.getFields(), (field) => {
            jQuery(field).closest('tr').show();
        });                                                                                                
    }

    this.clearPostPaymentInputs = function() {
        jQuery.map( this.getFields(), (field) => {
            jQuery(field).textbox('setValue', '');;
        });
    }

    this.fillPostPaymentInputs = function ( jsonValues ) {
        this.clearPostPaymentInputs();

        const postPaymentFields = JSON.parse(jsonValues);

        Object.entries(postPaymentFields).forEach(([inputName, value]) => {
            jQuery('.' + inputName).textbox('setValue', value);
        });
        
    }
}
