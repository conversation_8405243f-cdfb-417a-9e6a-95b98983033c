function getOwnersContractsTreeFilter() {
    var obj = {};
    obj.year = jQuery('#search-year').combobox('getValue');
    obj.cnum = jQuery('#search-cnum').val();
    obj.contract_type = jQuery('#search-contract-type').combobox('getValues');
    obj.farming = jQuery('#search-farming').combobox('getValues');
    obj.owner_name = jQuery('#search-owner-name').val();
    obj.owner_egn = jQuery('#search-owner-egn').val();
    obj.rep_name = jQuery('#search-represent-name').val();
    obj.rep_egn = jQuery('#search-represent-egn').val();
    obj.company_name = jQuery('#search-company-name').val();
    obj.company_eik = jQuery('#search-company-eik').val();
    obj.person_name = jQuery('#search-person-name').val();
    obj.person_egn = jQuery('#search-person-egn').val();
    obj.owner_note = jQuery('#search-owner-note').val();
    obj.owner_phone = jQuery('#search-owner-phone').val();

    if (jQuery('#search-contract-group').combobox('getValues') != '') {
        obj.contract_group = jQuery('#search-contract-group').combobox('getValues');
    }

    if (jQuery('#search-contracts-with-nat input').is(':checked')) {
        obj.with_renta_nat = 1;
    }

    if (jQuery('#search-contracts-without-nat input').is(':checked')) {
        obj.with_renta_nat = 0;
    }

    obj.with_bank_acc = +jQuery("#search-all-contracts-with-bank-acc").is(':checked');
    return obj;
}