function getPaymentsContractsTreeFilter(contractId) {
    var obj = {};

    if (contractId) {
        obj.contract_id = contractId
    }

    obj.year = jQuery('#search-year').combobox('getValue');
    obj.kad_ident = jQuery('#search-kad-ident').val();
    obj.ekate = jQuery('#search-ekatte').combobox('getValues');
    obj.mestnost = jQuery('#search-mestnost').combobox('getValues');
    obj.masiv = jQuery('#search-masiv').val();
    obj.number = jQuery('#search-number').val();
    obj.category = jQuery('#search-category').combobox('getValues');
    obj.area_type = jQuery('#search-area-type').combobox('getValues');
    obj.cnum = jQuery('#search-cnum').val();
    obj.c_num_complete_match = jQuery('#search-cnum-complete-match').is(':checked');
    obj.contract_type = jQuery('#search-contract-type').combobox('getValues');
    obj.farming = jQuery('#search-farming').combobox('getValues');
    obj.irrigated_area = jQuery('#search-irrigated-area').combobox('getValue');
    obj.owner_name = jQuery('#search-owner-name').val();
    obj.owner_egn = jQuery('#search-owner-egn').val();
    obj.rep_name = jQuery('#search-represent-name').val();
    obj.rep_egn = jQuery('#search-represent-egn').val();
    obj.heritor_name = jQuery('#search-heritor-name').val();
    obj.heritor_egn = jQuery('#search-heritor-egn').val();
    obj.company_name = jQuery('#search-company-name').val();
    obj.company_eik = jQuery('#search-company-eik').val();
    obj.person_name = jQuery('#search-person-name').val();
    obj.person_egn = jQuery('#search-person-egn').val();
    obj.na_num = jQuery('#search-na-num').val();
    obj.na_num_complete_match = jQuery('#search-na-num-complete-match').is(':checked');

    obj.owner_note = jQuery('#search-owner-note').val();
    if (obj.owner_note.length > 0 && obj.owner_note.length < 3) {
        jQuery.messager.alert('Грешка', 'Моля въведете поне 3 символа за търсене в полето "Забележка"', 'warning');
        return;
    }

    obj.owner_phone = jQuery('#search-owner-phone').val();
    if (obj.owner_phone.length > 0 && obj.owner_phone.length < 5) {
        jQuery.messager.alert('Грешка', 'Моля въведете поне 5 символа за търсене в полето "Телефон/Мобилен"', 'warning');
        return;
    }

    if (jQuery('#search-contract-group').combobox('getValues') != '') {
        obj.contract_group = jQuery('#search-contract-group').combobox('getValues');
    }

    if (jQuery('#search-is-closed-for-editing').is(':checked')) {
        obj.is_closed_for_editing = true;
    }

    if (jQuery('#search-contracts-with-nat input').is(':checked')) {
        jQuery('#acr-contracts-with-nat-radio').prop('checked', true);
        obj.with_renta_nat = 1;
    }
    if (jQuery('#search-contracts-without-nat input').is(':checked')) {
        jQuery('#acr-contracts-without-nat-radio').prop('checked', true);
        obj.with_renta_nat = 0;
    }
    if (jQuery('#search-all-contracts input').is(':checked')) {
        jQuery('#acr-all-contracts-radio').prop('checked', true);
    }

    return obj;
}