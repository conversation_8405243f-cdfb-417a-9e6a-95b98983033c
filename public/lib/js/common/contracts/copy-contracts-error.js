//assign gids to global variable and clear session storage
var unsuccessfullyCopiedContractGids = [];


function setGidsFromCopy() {    
    if (sessionStorage.getItem('unsuccessfully-copied-gids') === null) {
        return;
    }
    unsuccessfullyCopiedContractGids = sessionStorage.getItem('unsuccessfully-copied-gids');

    if (unsuccessfullyCopiedContractGids.indexOf(',') > -1) { 
        unsuccessfullyCopiedContractGids = unsuccessfullyCopiedContractGids.split(',').map(value => parseInt(value, 10));
    } else {
        unsuccessfullyCopiedContractGids = [parseInt(unsuccessfullyCopiedContractGids, 10)];
    }
    sessionStorage.removeItem('unsuccessfully-copied-gids');
}

/**
 * 
 * @param {*} gids 
 */
function addGidsToStorage(gids) {
    sessionStorage.setItem('unsuccessfully-copied-gids', gids);
}

/**
 * 
 * @param {*} gid 
 * @returns 
 */
function setCopyErrorStyles(gid, style) {
    try {
        // will be with highest priority of color coding rows
        if(unsuccessfullyCopiedContractGids.includes(gid)) {
            jQuery('#contract-copy-problem').show();
            style.push('background-color: #ff4c4c');
        } 
    } catch (error) {
    }
}

/**
 * 
 * @param {*} contractId 
 * @param {*} isAnnex 
 */
 function openContractInNewTab(contractId, isAnnex, contractGids) {

    addGidsToStorage(contractGids);
    if(isAnnex === true) {
        window.open("index.php?page=Contracts.Home&contract_id=" + contractId, '_blank');
    } else{
        window.open("index.php?page=Contracts.Home&contract_id=" + contractId, '_blank');
    }

    setTimeout(function(){
        sessionStorage.removeItem('unsuccessfully-copied-gids');
    },3000);

}


/**
 * 
 * @param {*} contractId 
 * @param {*} isAnnex 
 */
 function openSaleContractInNewTab(contractId) {
    window.open("index.php?page=SalesContracts.Home&id=" + contractId, '_blank');
}