function markPlotAsRemoved(plotsTable, contractsTree)
{
    if (!hasPlotRightsRW) {
        messagerPlotsWriteRights();
        return;
    }
    let getChecked = plotsTable.datagrid('getChecked'),
        annexData = contractsTree.tree('getSelected');

    if (annexData) {
        if (getChecked[0]) {
            if (getChecked[0].annex_action == 'removed') {
                jQuery.messager.alert('Грешка', 'Имотът вече е премахнат от избрания анекс!');
                return false;
            }
            jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да премахнете този имот?', function (r) {
                if (r) {
                    var obj = {};
                    obj.plot_id = getChecked[0].gid;
                    obj.contract_id = annexData.id;
                    obj.pc_rel_id = getChecked[0].pc_rel_id;

                    TF.Rpc.Annexes.AnnexesPlots.deleteAnnexPlots(obj)
                        .done(function () {
                            plotsTable.datagrid('reload');
                            plotsTable.datagrid('uncheckAll');
                        })
                        .fail(function (errorObj) {
                            if (errorObj.is(TF.Rpc.ExceptionsList.CONTRACTS_AVAILABLE_PLOT_AREA_EXCEPTION)) {
                                let {available_area, contracts_info} = errorObj.getOriginalMessage()[0];
                                contracts_info = JSON.parse(contracts_info);

                                jQuery('#win-contracts-available-area-error').window('open');  
                                initContractsAvailablePlotAreaErrorModal(
                                    contracts_info
                                );
                            } else if (errorObj.is(TF.Rpc.ExceptionsList.CONTRACT_PLOT_DUPLICATION)) {
                                jQuery('#win-copy-filtered-contracts-plots-error').window('open');  
                                initUnsuccessfullyCopiedContracts(
                                    errorObj.getOriginalMessage()
                                );
                                return false;               
                            }  else {
                                jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');                           
                            }
                        });
                }
            });
        } else {
            jQuery.messager.alert('Грешка', 'Моля изберете имот, който искате да премахнете!');
        }
    } else {
        jQuery.messager.alert('Грешка', 'Не е избран договор!');
    }
}

function activateRemovedPlot(plotsTable, contractsTree) {

    let plotData = plotsTable.datagrid('getSelected');
    let annexData = contractsTree.tree('getSelected');

    var obj = {},
    plot_data_array = [],
    plot = {};


    plot.gid = plotData.gid;
    plot.pc_rel_id = plotData.pc_rel_id;
    plot.contract_area = plotData.contract_area;
    plot.document_area = plotData.document_area;
    plot.area_for_rent = plotData.area_for_rent;
    plot.rent_per_plot = plotData.rent_per_plot;
    if (plotData.rent_per_plot == '-') {
        plot.rent_per_plot = 0;
    }
    plot_data_array.push(plot);   
    obj.plot_data_array = plot_data_array;
    obj.contract_id = annexData.attributes.parent_id;
    obj.annex_id = annexData.id;


    TF.Rpc.Annexes.AnnexesPlots.addAnnexPlotRelation(obj)
        .done(function () {
            plotsTable.datagrid('reload');
            plotsTable.datagrid('uncheckAll');
        }).fail(function (errorObj) {
            if (errorObj.is(TF.Rpc.ExceptionsList.CONTRACT_PLOT_DUPLICATION)) {
                jQuery('#win-copy-filtered-contracts-plots-error').window('open');  
                initUnsuccessfullyCopiedContracts(
                    errorObj.getOriginalMessage()
                );
                return false;               
            } else {
                jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');  
            }
        });
}