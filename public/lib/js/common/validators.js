// validators.js

const VALIDATION_MESSAGES = {
    EGN_MISSING: 'Моля въведете ЕГН.',
    EGN_INVALID: 'Невалиден формат на ЕГН. Форматът трябва да бъде 5212037891.',
    ID_CARD_MISSING: 'Моля въведете номер на лична карта.',
    ID_CARD_INVALID: 'Невалиден формат на номер на лична карта. Форматът трябва да бъде 123456789 или ID1234567.'
};

class ValidateBox {
    static instances = {};

    constructor(selector) {
        selector = selector.replace(/\s+/g, '');
        if (ValidateBox.instances[selector]) {
            return ValidateBox.instances[selector];
        }

        this.$element = jQuery(selector);
        this.initializeValidation();

        ValidateBox.instances[selector] = this;
    }

    static getInstance(selector) {
        if (!EgnValidateBox.instances[selector]) {
            new EgnValidateBox(selector);
        }
        return EgnValidateBox.instances[selector];
    }

    logCaller() {
        const error = new Error();
        const stack = error.stack.split('\n');
        const caller = stack[2].trim();
    }

    initializeValidation() {
        // To be implemented by subclasses
    }

    disableValidation() {
        this.$element.validatebox('disableValidation');
    }

    enableValidation() {
        this.$element.validatebox('enableValidation');
    }

    isValid() {
        return this.$element.validatebox('isValid');
    }

    getValue() {
        return this.$element.val();
    }

    setValue(value) {
        this.$element.val(value);
    }
    clear() {
        this.$element.val('');
    }
    disable() {
        this.$element.validatebox('disabled');
    }
}

class EgnValidateBox extends ValidateBox {
    constructor(selector) {
        super(selector);
    }

    initializeValidation() {
        jQuery.extend(jQuery.fn.validatebox.defaults.rules, {
            egnValidation: {
                validator: this.egnValidation.bind(this),
                message: VALIDATION_MESSAGES.EGN_INVALID
            }
        });

        this.$element.validatebox({
            required: true,
            validType: 'egnValidation',
            missingMessage: VALIDATION_MESSAGES.EGN_MISSING,
            invalidMessage: VALIDATION_MESSAGES.EGN_INVALID,
        });

        // Trigger validation on input
        this.$element.on('input', function () {
            jQuery(this).validatebox('validate');
        });
    }

    egnValidation(value) {
        // Validate the entire string (10 digits)
        return /^\d{0,10}$/.test(value);
    }
}

class CardIdValidateBox extends ValidateBox {
    constructor(selector) {
        super(selector);
    }

    initializeValidation() {
        jQuery.extend(jQuery.fn.validatebox.defaults.rules, {
            idCardValidation: {
                validator: this.idCardValidation.bind(this),
                message: VALIDATION_MESSAGES.ID_CARD_INVALID
            }
        });

        this.$element.validatebox({
            required: false,
            validType: 'idCardValidation',
            missingMessage: VALIDATION_MESSAGES.ID_CARD_MISSING,
            invalidMessage: VALIDATION_MESSAGES.ID_CARD_INVALID,
        });

        // Trigger validation on input
        this.$element.on('input', function () {
            jQuery(this).validatebox('validate');
        });
    }

    idCardValidation(value) {
        // Validate the entire string (10 digits)
        if (/^[A-Za-z]{2}/.test(value)) {
            return /^[A-Za-z]{2}\d{7}$/.test(value);
        } else if (/^\d{2}/.test(value)) {
            return /^\d{9}$/.test(value);
        }
        return false;
    }
}

// Define the custom editor for EGN
jQuery.extend(jQuery.fn.datagrid.defaults.editors, {
    egnValidateBox: {
        init: function(container, options) {
            var uniqueId = 'egn-input-' + Math.random().toString(36).substring(2, 11);
            var input = jQuery('<input type="text" id="' + uniqueId + '">').appendTo(container);
            EgnValidateBox.getInstance('#' + uniqueId);
            return input;
        },
        destroy: function(target) {
            jQuery(target).validatebox('destroy');
        },
        getValue: function(target) {
            return jQuery(target).val();
        },
        setValue: function(target, value) {
            jQuery(target).val(value);
        },
        resize: function(target, width) {
            jQuery(target)._outerWidth(width);
        }
    },
    cardIdValidateBox: {
        init: function(container, options) {
            var uniqueId = 'idcard-input-' + Math.random().toString(36).substring(2, 11);
            var input = jQuery('<input type="text" id="' + uniqueId + '">').appendTo(container);
            CardIdValidateBox.getInstance('#' + uniqueId);
            return input;
        },
        destroy: function(target) {
            jQuery(target).validatebox('destroy');
        },
        getValue: function(target) {
            return jQuery(target).val();
        },
        setValue: function(target, value) {
            jQuery(target).val(value);
        },
        resize: function(target, width) {
            jQuery(target)._outerWidth(width);
        }
    }
});