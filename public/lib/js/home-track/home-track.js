jQuery(document).ready(function () {
    if (document.getElementById('home-track-get-wialon-token')) {
        initWialonSession();
    }
});

function initWialonSession() {
    wialon.core.Session.getInstance().initSession(wialonServer);
    wialon.core.Session.getInstance().loginToken(trackToken, handleLoginResponse);
}

function handleLoginResponse(code) {
    if ([4, 8].includes(code)) {
        jQuery('#home-track-get-wialon-token').window('open');
        return;
    }
    if (code) {
        console.log(wialon.core.Errors.getErrorText(code));
        return;
    }
    jQuery('#iframe-wialon').css('visibility', 'visible');
}

function getWialonToken() {
    const url = constructLoginUrl();
    openLoginPage(url);
    window.addEventListener("message", tokenReceived);
}

function constructLoginUrl() {
    const dns = wialonServer;
    return `${dns}/login.html?client_id=Technofarm&access_type=-1&activation_time=0&duration=0&flags=0x1&lang=bg&redirect_uri=${dns}/post_token.html`;
}

function openLoginPage(url) {
    const top = (jQuery(window).height() - 450) / 2;
    const left = (jQuery(window).width() - 550) / 2;
    window.open(url, "_blank", `width=550, height=450, top=${top}, left=${left}`);
}

function tokenReceived(e) {
    const msg = e.data;
    if (typeof msg === "string" && msg.includes("access_token=")) {
        const newToken = msg.replace("access_token=", "");
        updateTokenAndCloseWindow(newToken);
        window.removeEventListener("message", tokenReceived);
    }
}

function updateTokenAndCloseWindow(token) {
    TF.Rpc.Diary.WialonActions.addWialonToken(token)
        .done(function () {
            jQuery("#home-track-get-wialon-token").window("close");
            const newIframeSrc = `${wialonServer}?token=${token}&lang=bg`;
            jQuery('#iframe-wialon').attr('src', newIframeSrc).css('visibility', 'visible');
        })
        .fail(function (errorObj) {
            jQuery.messager.alert("Грешка", errorObj.getMessage());
        });
}