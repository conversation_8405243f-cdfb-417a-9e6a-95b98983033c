jQuery(function () {

    jQuery('#search-start-date').datebox({
        value: ''
    });

    jQuery('#search-end-date').datebox({
        value: ''
    });

    initGlobalNotificationsGrid();

    jQuery('#btn-search').bind('click', function (e) {
        e.preventDefault();

        jQuery('#global-notifications-table').datagrid({
            rpcParams: [{
                start_time: jQuery('#search-start-date').datebox('getValue'),
                end_time: jQuery('#search-end-date').datebox('getValue')
            }]
        });

        return false;
    });

    jQuery('#btn-clear').bind('click', function () {
        jQuery('#search-start-date').datebox('clear');
        jQuery('#search-end-date').datebox('clear');


        jQuery('#global-notifications-table').datagrid({
            rpcParams: [{}]
        });

        return false;
    });

    jQuery('#btn-add-new-notification').bind('click', function (e) {
        if (!validateRequiredFields()) {
            jQuery.messager.alert('Грешка', 'Има непопълнено задължително поле!', 'error');
            return;

        }
        var addObject = getAddEditFieldsData();

        TF.Rpc.GlobalNotifications.GlobalNotificationsMainGrid.add(addObject)
            .done(function (data) {
                doAdd();
            })
            .fail(function (data) {
                doAdd();
                jQuery.messager.alert('Грешка');
            });
    });

    jQuery('#btn-edit-selected-notification').bind('click', function (e) {
        if (!validateRequiredFields()) {
            jQuery.messager.alert('Грешка', 'Има непопълнено задължително поле!', 'error');
            return;

        }
        var editObject = getAddEditFieldsData();

        TF.Rpc.GlobalNotifications.GlobalNotificationsMainGrid.edit(editObject)
            .done(function (data) {
                doAdd();
            })
            .fail(function (data) {
                doAdd();
                jQuery.messager.alert('Грешка');
            });
    });
});

function initGlobalNotificationsGrid() {
    var columns = [[
        {
            field: 'title',
            title: '<b>Заглавие</b>',
            sortable: false,
            width: 150
        }, {
            field: 'text',
            title: '<b>Съдържание</b>',
            sortable: false,
            width: 200
        }, {
            field: 'type_name',
            title: '<b>Тип</b>',
            sortable: false,
            width: 50
        }, {
            field: 'is_active',
            title: '<b>Активен</b>',
            sortable: false,
            width: 50,
            formatter: function (value) {
                if (value == true) {
                    return 'Yes'
                }
                else {
                    return 'No';
                }
            }
        }, {
            field: 'is_closable',
            title: '<b>Затваряне</b>',
            sortable: false,
            width: 50,
            formatter: function (value) {
                if (value == true) {
                    return 'Yes'
                }
                else {
                    return 'No';
                }
            }
        }, {
            field: 'start_time',
            title: '<b>Начална дата</b>',
            sortable: false,
            width: 80
        }, {
            field: 'end_time',
            title: '<b>Крайна дата</b>',
            sortable: false,
            width: 80
        }, {
            field: 'created',
            title: '<b>Дата на създаване</b>',
            sortable: false,
            width: 95
        }, {
            field: 'modified',
            title: '<b>Дата на редактиране</b>',
            sortable: false,
            width: 95
        }
    ]];
    jQuery('#global-notifications-table').datagrid({
        title: 'Нотификации',
        iconCls: 'icon-notification',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 20,
        fit: true,
        fitColumns: true,
        showFooter: false,
        url: 'index.php?global-notifications-rpc=global-notifications-grid',
        rpcParams: [{}],
        idField: 'id',
        columns: columns,
        pagination: false,
        rownumbers: false,
        frozenColumns: [[
            {
                field: 'ck',
                checkbox: true
            }
        ]],
        toolbar: [{
            id: 'btnadd',
            text: 'Добавяне',
            iconCls: 'icon-add',
            handler: function () {
                displayAddEditFields();
                resetAddEditFields();
                jQuery('#btn-add-new-notification').show();
                jQuery('#btn-edit-selected-notification').hide();
                jQuery('#win-add').window('open');
            }
        }, {
            id: 'btnedit',
            text: 'Редактиране',
            iconCls: 'icon-edit',
            handler: function () {
                var getSelected = jQuery('#global-notifications-table').datagrid('getSelected');
                if (getSelected) {
                    TF.Rpc.GlobalNotifications.GlobalNotificationsMainGrid.getById(getSelected.id)
                        .done(function (data) {
                            displayAddEditFields();
                            resetAddEditFields();
                            jQuery('#btn-add-new-notification').hide();
                            jQuery('#btn-edit-selected-notification').show();
                            jQuery('#win-add').window('open');
                            setEditNotificationFields(data);
                        })
                        .fail(function (errorObj) {
                            jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
                            jQuery('#global-notifications-table').datagrid('uncheckAll');
                            jQuery('#global-notifications-table').datagrid('loadRpc');
                        });
                } else {
                    jQuery.messager.alert('Грешка', 'Не е избрана нотификация!');
                }
            }
        }],
        onBeforeLoad: function () {
        },
        onLoadSuccess: function () {
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function resetAddEditFields() {
    jQuery('#start-date > input').datebox('reset');
    jQuery('#end-date > input').datebox('reset');
    jQuery('#title > input').val('');
    jQuery('#content > textarea').val('');
    jQuery('#type > input').combobox('reset');
}

function displayAddEditFields() {
    var date                         = new Date(),
        todayDate                    = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();

    jQuery('#start-date > input').datebox({
        required: true,
        missingMessage: 'Моля изберете дата на договор.',
        value: todayDate
    });
    jQuery('#end-date > input').datebox({});
    jQuery('#type > input').combobox({
        url: 'index.php?global-notifications-rpc=global-notifications-types',
        valueField: 'id',
        textField: 'name',
        required: true,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#title > input').validatebox({
        required: true,
        validType: 'minLength[3]',
        missingMessage: 'Моля въведете заглавие',
        delay: 100
    });
    jQuery('#content > textarea').validatebox({
        required: true,
        validType: 'minLength[3]',
        missingMessage: 'Моля въведете съдържание',
        delay: 100
    });
}

function getAddEditFieldsData() {
    var getSelected = jQuery('#global-notifications-table').datagrid('getSelected');
    var obj = {
        title: jQuery('#title > input').val(),
        content: jQuery('#content > textarea').val(),
        type: jQuery('#type > input').combobox('getValue'),
        start_date: jQuery('#start-date > input').datebox('getValue'),
        end_date: jQuery('#end-date input').datebox('getValue'),
        active: jQuery('#active > input').is(':checked'),
        closable: jQuery('#closable > input').is(':checked'),
        id: getSelected ? getSelected.id : -1
    };

    return obj;
}

function setEditNotificationFields(data) {
    jQuery('#start-date > input').datebox('setValue', data.start_time);
    jQuery('#end-date > input').datebox('setValue', data.end_time);
    jQuery('#title > input').val(data.title);
    jQuery('#content > textarea').val(data.text);
    jQuery('#type > input').combobox('select', data.type_id);
    jQuery('#active > input').prop('checked', data.is_active);
    jQuery('#closable > input').prop('checked', data.is_closable);
}

function validateRequiredFields() {
    var isValid = jQuery('#title > input').validatebox('isValid') 
        &&
        jQuery('#content > textarea').validatebox('isValid')
        &&
        jQuery('#type > input').combobox('getValue');

    return isValid;
}

function doAdd() {
    jQuery('#win-add').window('close');
    jQuery('#global-notifications-table').datagrid('uncheckAll');
    jQuery('#global-notifications-table').datagrid('loadRpc');
}
