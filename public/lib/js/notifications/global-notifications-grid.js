jQuery(function () {
    initGlobalNotifications();

    jQuery('#global-notifications-panel').on('click', '#close-notification', function (e) {
        var $this = jQuery(this);
        var notificationId = $this.data('id');

        TF.Rpc.GlobalNotifications.GlobalNotificationsMainGrid.close(notificationId).done(function (dataObj) {
            $this.closest('#global-notification').css('display', 'none');
        });
    });
});


function initGlobalNotifications() {
    TF.Rpc.GlobalNotifications.GlobalNotificationsMainGrid.getActiveNotClosedByUser()
        .done(function (dataObj) {
            var html = '';
            dataObj.forEach(function (each) {
                html += '<div id="global-notification" class="global-notifications-' + each.name + '">' +
                    '<span style="font-weight: bold">' + each.title + '</span>&nbsp;<span>' + each.text + '</span>';

                if (each.is_closable) {
                    html += '<span id="close-notification" data-id="' + each.id + '" style="position: absolute; top: 0; right: 0; padding: .75rem 1.25rem; font-size: 1.5rem; font-weight: 700; cursor: pointer;">x</span>';
                }

                html += '</div>';
            });
            jQuery('#global-notifications-panel').html(html);
        });
}