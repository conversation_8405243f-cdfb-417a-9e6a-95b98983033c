(function(root, factory) {
    if (typeof define === "function" && define.amd) {
        // AMD. Register as an anonymous module.
        define(["jquery", "js/main/EasyUIRPCLoaders", "easyui"], factory);
    } else {
        // Browser globals (root is window)
        root.alerts_settings = factory(jQuery, EasyUIRPCLoaders);
    }
})(typeof self !== "undefined" ? self : this, function(
    jQuery,
    EasyUIRPCLoaders
) {
    var alarm_edit_index = undefined;
    var alarm_type_edit = false;

    function initAlertsGrid() {
        var alertSettingsGrid = jQuery("#alert-tables");

        alertSettingsGrid.datagrid({
            iconCls: "icon-notification",
            nowrap: true,
            autoRowHeight: true,
            striped: true,
            pageSize: 50,
            fit: true,
            fitColumns: true,
            showFooter: true,
            url: "index.php?notifications-rpc=notification-grid",
            rpcMethod: "settings",
            rpcParams: [{}],
            sortName: "id",
            sortOrder: "desc",
            border: false,
            idField: "id",
            singleSelect: true,
            frozenColumns: [
                [
                    {
                        field: "ck",
                        checkbox: false
                    }
                ]
            ],
            columns: [
                [
                    {
                        field: "alert_label",
                        title: "<b>Аларма</b>",
                        sortable: false,
                        width: 150
                    },
                    {
                        field: "alert_interval",
                        title: "<b>Интервал</b>",
                        sortable: false,
                        width: 50,
                        formatter: function(value) {
                            if (value >= 0) {
                                return value + " дни";
                            } else {
                                return value;
                            }
                        },
                        editor: {
                            type: "numberbox",
                            options: {
                                min: 0,
                                max: 365,
                                precision: 0
                            }
                        }
                    },
                    {
                        field: "alert_status",
                        title: "<b>Статус</b>",
                        sortable: false,
                        width: 50,
                        formatter: function(value) {
                            if (value) {
                                return "Активни";
                            } else {
                                return "Не активни";
                            }
                        },
                        editor: {
                            type: "checkbox",
                            options: {
                                on: 1,
                                off: 0
                            }
                        }
                    }
                ]
            ],
            pagination: false,
            rownumbers: false,
            toolbar: [
                {
                    id: "btn_edit_alarm_settings",
                    text: "Редактиране",
                    iconCls: "icon-edit",
                    handler: function() {
                        var selected = alertSettingsGrid.datagrid(
                            "getSelected"
                        );
                        if (selected) {
                            alertSettingsGrid.datagrid(
                                "beginEdit",
                                alarm_edit_index
                            );
                        } else {
                            jQuery.messager.alert(
                                "Грешка",
                                "Не е избрана аларма!",
                                "error"
                            );
                        }
                    }
                },
                {
                    id: "btn_save_alarm_settings",
                    text: "Запази",
                    iconCls: "icon-save",
                    handler: function() {
                        alertSettingsGrid.datagrid(
                            "endEdit",
                            alarm_edit_index
                        );
                    }
                }
            ],
            onBeforeLoad: function() {
                alertSettingsGrid.datagrid("clearChecked");
            },
            onSelect: function(rowIndex, rowData) {
                if (alarm_edit_index != undefined && alarm_type_edit) {
                    jQuery.messager.confirm(
                        "Потвърждение",
                        "Искате ли да запазите промените?",
                        function(r) {
                            if (r) {
                                alertSettingsGrid.datagrid(
                                    "endEdit",
                                    alarm_edit_index
                                );
                            } else {
                                alertSettingsGrid.datagrid(
                                    "rejectChanges"
                                );
                                alarm_edit_index = undefined;
                            }
                        }
                    );
                } else {
                    alarm_edit_index = rowIndex;
                }
            },
            onBeforeEdit: function(rowIndex, rowData) {
                alarm_type_edit = true;
            },
            onAfterEdit: function(rowIndex, rowData, changes) {
                if (jQuery.isEmptyObject(changes)) {
                    alarm_type_edit = false;
                    alarm_edit_index = undefined;
                    alertSettingsGrid.datagrid("selectRow", rowIndex);
                    alertSettingsGrid.datagrid("beginEdit", rowIndex);
                    return;
                }
                if (changes.alert_interval || changes.alert_status) {
                    var obj = new Object();
                    if (changes.alert_interval) {
                        obj.alert_interval = changes.alert_interval;
                    }
                    if (changes.alert_status) {
                        obj.alert_status = changes.alert_status;
                    }
                    obj.alert_id = rowData.id;
                    TF.Rpc.Notifications.NotificationsMainGrid.editNotificationSettings(
                        obj
                    ).done(function(dataObj) {
                        alertSettingsGrid.datagrid("reload");
                    });
                }

                alarm_edit_index = undefined;
                alarm_type_edit = false;
                alertSettingsGrid.datagrid("clearSelections");
            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter:
                EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }
    return {
        initAlertsGrid
    };
});
