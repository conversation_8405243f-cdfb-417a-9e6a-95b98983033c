jQuery(function () {
    initNotificationGrid(true);
});

function initNotificationGrid( showAleart ){
    var showAleart = showAleart || false;

    jQuery('#notification-tables').datagrid({
        iconCls: 'icon-notification',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 50,
        fit: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?notifications-rpc=notification-grid',
        rpcParams: [{}],
        sortName: 'id',
        sortOrder: 'desc',
        border: false,
        idField: 'id',
        singleSelect: true,
        frozenColumns: [[
            {
                field: 'ck',
                checkbox: false
            }
        ]],
        columns: [[
             {
                field: 'notification',
                title: '<b>Съобщение</b>',
                sortable: true,
                width: 250
            },
        ]],
        pagination: false,
        rownumbers: false,
        onBeforeLoad: function () {
            jQuery('#notification-tables').datagrid('clearChecked');
        },
        onLoadSuccess: function (rowData) {
            document.getElementById("notification-grid")?.setAttribute('data-options',"iconCls:'icon-notification'");
            localStorage = window.localStorage;
            var newDate = new Date().toJSON().slice(0, 10);

            var date = localStorage.getItem(userid);
            if (!date || date !== newDate) {
                var note = "<ul class='notification-messages-list'>";
                localStorage.setItem(userid, newDate);
                rowData.rows.forEach(function (alert) {
                    note += "<li>" + alert.notification + "</li>";
                });
                note += "</ul>";
                if (rowData.total !== 0) {
                    jQuery.messager.show(
                        {
                            title: 'Съобщения',
                            msg: note,
                            timeout: 5000,
                            showType: 'slide',
                            style: {
                                left: '',
                                right: 15,
                                top: '',
                                bottom: -document.body.scrollTop - document.documentElement.scrollTop + 45
                            },
                            height: 300,
                            width: 250
                        }
                    );
                }
                }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}
