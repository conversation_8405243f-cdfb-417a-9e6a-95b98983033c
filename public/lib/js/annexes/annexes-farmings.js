/*jslint browser: true */
/*global jQuery, EasyUIRPCLoaders.EasyUIGridCustomLoader, TF, onHidePanelMultiSelect, onComboMultiSelect, initLoadingItems, setUserRights, hasPlotRightsRW, messagerPlotsWriteRights, Fraction, CONTRACT_TYPE_OWN, hasContractsOwnWriteRights, messagerContractsOwnWriteRights, initAddContractsFarmingGrid, initAddContractsFarmingGrid, initOwnersRepsGrid*/

function initAnnexesFarmingGrid(plot_id, contract_id, c_type) {
    'use strict';
    var selectedPlotID = plot_id,
        selectedContractID = contract_id,
        plotsGrid = jQuery('#annex-plots-tables');

    jQuery('#contract-farming-tables').datagrid({
        iconCls: 'icon-agriculture',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        url: 'index.php?contracts-rpc=contracts-farming-datagrid',
        rpcParams: [{
            contract_id: selectedContractID,
            plot_id: selectedPlotID
        }],
        pageSize: 10,
        fit: true,
        singleSelect: true,
        fitColumns: true,
        showFooter: true,
        border: false,
        sortName: 'id',
        sortOrder: 'desc',
        idField: 'id',
        columns: [[
            {
                field: 'farming',
                title: '<b>Стопанство</b>',
                sortable: false,
                width: 100
            }, {
                field: 'rep_names',
                title: '<b>Представител</b>',
                sortable: false,
                width: 100
            }, {
                field: 'rat_ownage',
                title: '<b>Собственост(%)</b>',
                sortable: false,
                width: 75,
                formatter: function (value, row) {
                    var percent,
                        fraction;

                    if (row.numerator && row.denominator) {
                        percent = new Fraction(row.numerator / row.denominator * 100);
                        fraction = row.numerator + '/' + row.denominator;
                        value = percent.toString() + '% (' +  fraction + ')';

                        return value;
                    }

                    fraction = new Fraction(row.percent / 100);
                    percent = fraction.mul(100);
                    value = percent.toString() + '% (' +  fraction.simplify().toFraction() + ')';

                    return value;
                }
            }
        ]],
        pagination: false,
        rownumbers: true,
        toolbar: [
            {
                id: 'btnaddfarmingcontragent',
                text: 'Добавяне',
                iconCls: 'icon-add',
                handler: function () {
                    if (!hasPlotRightsRW) {
                        messagerPlotsWriteRights();
                        return false;
                    }
                    var plotData = plotsGrid.datagrid('getChecked'),
                        contractData = jQuery('#contracts-tree').tree('getSelected'),
                        max,
                        sum,
                        maxOwnership;

                    if (jQuery('#annexes-tree').data().hasOwnProperty('tree')) {
                        contractData = jQuery('#annexes-tree').tree('getSelected');
                    }
                    //No Rights to operate with "Договори за собственост"
                    if (contractData.attributes.c_type == CONTRACT_TYPE_OWN && !hasContractsOwnWriteRights) {
                        messagerContractsOwnWriteRights();
                        return false;
                    }

                    max = new Fraction(1);

                    if (!plotData[0]) {
                        jQuery.messager.alert('Грешка', 'Не е избран имот.');
                    }

                    //No Rights to operate with "Договори за собственост"
                    if (c_type == CONTRACT_TYPE_OWN && !hasContractsOwnWriteRights) {
                        messagerContractsOwnWriteRights();
                        return false;
                    }

                    sum = getTotalFarmingShares();

                    if (sum.valueOf() >= 1) {
                        jQuery.messager.alert('Грешка', 'Вече е зададена 100% собственост за този имот.');
                        return;
                    }

                    maxOwnership = max.sub(sum);

                    contragent_type = 'farming';
                    //init add farming grid
                    if (contractData != null) {
                        initAddContractsFarmingGrid(contractData.id, plotData[0].gid);
                    } else {
                        initAddContractsFarmingGrid(contract_id, plotData[0].gid);
                    }
                    //init representatives
                    initOwnersRepsGrid();

                    jQuery('#win-plot-owner-add').window('open');
                }
            }, {
                id: 'btneditfarmingcontragent',
                text: 'Редактиране',
                iconCls: 'icon-edit',
                handler: function () {
                    if (!hasPlotRightsRW) {
                        messagerPlotsWriteRights();
                        return false;
                    }
                    var contractData = jQuery('#contracts-tree').tree('getSelected'),
                        getSelected,
                        gridData,
                        sum,
                        max,
                        i,
                        f;

                    if (jQuery('#annexes-tree').data().hasOwnProperty('tree')) {
                        contractData = jQuery('#annexes-tree').tree('getSelected');
                    }
                    //No Rights to operate with "Договори за собственост"
                    if (contractData.attributes.c_type == CONTRACT_TYPE_OWN && !hasContractsOwnWriteRights) {
                        messagerContractsOwnWriteRights();
                        return false;
                    }
                    getSelected = jQuery('#contract-farming-tables').datagrid('getSelected');
                    gridData = jQuery('#contract-farming-tables').datagrid('getData');
                    sum = new Fraction();
                    max = new Fraction(1);

                    if (getSelected) {
                        for (i = 0; i < gridData.rows.length; i++) {
                            if (getSelected.id == gridData.rows[i].id) {
                                continue;
                            }
                            if (gridData.rows[i].numerator && gridData.rows[i].denominator) {
                                f = new Fraction(gridData.rows[i].numerator / gridData.rows[i].denominator);
                            } else {
                                f = new Fraction(gridData.rows[i].percent / 100);
                            }
                            sum = sum.add(f);
                        }

                        maxOwnership = max.sub(sum);
                        contragent_type = 'farming';
                        initEditOwnageDataFields();
                        jQuery('#win-edit-contract-owner-data').window('open');
                    } else {
                        jQuery.messager.alert('Грешка', 'Моля изберете запис, който искате да редактирате.');
                    }
                }
            }, {
                id: 'btndeletefarmingcontragent',
                text: 'Премахване',
                iconCls: 'icon-delete',
                handler: function () {
                    if (!hasPlotRightsRW) {
                        messagerPlotsWriteRights();
                        return false;
                    }
                    var getSelected = jQuery('#contract-farming-tables').datagrid('getSelected'),
                        contractData = jQuery('#contracts-tree').tree('getSelected');
                    if (jQuery('#annexes-tree').data().hasOwnProperty('tree')) {
                        contractData = jQuery('#annexes-tree').tree('getSelected');
                    }
                    //No Rights to operate with "Договори за собственост"
                    if (contractData.attributes.c_type == CONTRACT_TYPE_OWN && !hasContractsOwnWriteRights) {
                        messagerContractsOwnWriteRights();
                        return false;
                    }

                    if (getSelected) {

                        jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да премахнете този запис?', function (r) {
                            if (r) {
                                TF.Rpc.Contracts.ConctractOwnerData.deletePCToFarmingRelation(getSelected.id, contract_id)
                                    .done(function () {
                                        jQuery('#contract-farming-tables').datagrid('loadRpc');
                                    })
                                    .fail(function (errorObj) {
                                        if (errorObj.is(TF.Rpc.ExceptionsList.NON_EXISTING_CONTRACT_PLOT_RELATION)) {
                                            jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.NON_EXISTING_CONTRACT_PLOT_RELATION.message, 'warning');
                                        } else if (errorObj.is(TF.Rpc.ExceptionsList.INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS)) {
                                            jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS.message, 'warning');
                                        }
                                    });
                            }
                        });
                    } else {
                        jQuery.messager.alert('Грешка', 'Не е избран запис!');
                    }
                }
            }, {
                id: 'btncopyfarmingcontragent',
                text: 'Копиране',
                iconCls: 'icon-copy',
                handler: function () {
                    if (!hasPlotRightsRW) {
                        messagerPlotsWriteRights();
                        return false;
                    }
                    var selectedContract = jQuery('#contracts-tree').tree('getSelected'),
                        plotData,
                        farmingData,
                        selectedPlot,
                        obj = {};
                    if (jQuery('#annexes-tree').data().hasOwnProperty('tree')) {
                        selectedContract = jQuery('#annexes-tree').tree('getSelected');
                    }
                    //No Rights to operate with "Договори за собственост"
                    if (selectedContract.attributes.c_type == CONTRACT_TYPE_OWN && !hasContractsOwnWriteRights) {
                        messagerContractsOwnWriteRights();
                        return false;
                    }
                    plotData = plotsGrid.datagrid('getData');
                    farmingData = jQuery('#contract-farming-tables').datagrid('getData');
                    selectedPlot = jQuery('#cont-plots-tables').datagrid('getSelected');

                    if (farmingData.rows.length > 0) {
                        if (plotData.rows.length != 1) {

                            jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да добавите тези стопанства към всички имоти за този договор?', function (r) {
                                if (r) {
                                    obj.plot_id = jQuery('#annex-plots-tables').datagrid('getSelected').gid;
                                    obj.contract_id = jQuery('#annexes-tree').tree('getSelected').id;

                                    TF.Rpc.Contracts.ConctractOwnerData.multiplyFarming(selectedContract.id, selectedPlot.gid)
                                        .done(function (data) {
                                            displayMultiplyOwnerResult(data);
                                        })
                                        .fail(function (errorObj) {
                                            if (errorObj.is(TF.Rpc.ExceptionsList.NON_EXISTING_CONTRACT_PLOT_RELATION)) {
                                                jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.NON_EXISTING_CONTRACT_PLOT_RELATION.message, 'warning');
                                            } else if (errorObj.is(TF.Rpc.ExceptionsList.INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS)) {
                                                jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS.message, 'warning');
                                            }
                                        });
                                }
                            });
                        } else {
                            jQuery.messager.alert('Грешка', 'Към договора има добавен само един имот. Моля добавете други имоти, за които искате да бъдат копирани данните за стопанства.');
                        }
                    } else {
                        jQuery.messager.alert('Грешка', 'Няма въведени стопанства. Моля въведете стопанства, които да бъдат копирани.');
                    }
                }
            }
        ],
        onBeforeLoad: function () {
            jQuery('#contract-farming-tables').datagrid('clearChecked');
        },
        onSelect: function () {
            isHeritorSelected = false;
        },
        onLoadSuccess: function () {
            plot_id = jQuery('#annex-plots-tables').datagrid('getSelected').gid;
            contract_id = jQuery('#annexes-tree').tree('getSelected').id;
            initAnnexesOwnersGrid(plot_id, contract_id);
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initAddContractsFarmingGrid(contract_id, plot_id) {

    selectedPlotID = plot_id;
    selectedContractID = contract_id;

    jQuery('#owners-add-tables').datagrid({
        title: 'Стопанства',
        iconCls: 'icon-agriculture',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 10,
        fit: true,
        singleSelect: true,
        fitColumns: true,
        showFooter: true,
        border: true,
        url: 'index.php?contracts-rpc=contracts-farming-datagrid',
        rpcMethod: 'add',
        rpcParams:[{
            contract_id: selectedContractID,
            plot_id: selectedPlotID
            }],
        sortName: 'id',
        sortOrder: 'desc',
        idField:'id',
        columns:[[
        {
            field:'name',
            title:'<b>Име</b>',
            sortable:true,
            width:200
        },{
            field:'address',
            title:'<b>Адрес на стопанството</b>',
            sortable:true,
            width:200
        },{
            field:'company',
            title:'<b>Фирма</b>',
            sortable:true,
            width:200
        },{
            field:'bulstat',
            title:'<b>Булстат</b>',
            sortable:true,
            width:200
        },{
            field:'company_address',
            title:'<b>Адрес на фирмата</b>',
            sortable:true,
            width:200
        },{
            field:'mol',
            title:'<b>МОЛ</b>',
            sortable:true,
            width:200
        }
        ]],
        pagination:true,
        rownumbers:true,
        toolbar: null,
        onBeforeLoad: function () {
            jQuery('#owners-add-tables').datagrid('clearChecked');
            initContractOwnerDataFields();
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function getTotalFarmingShares() {
    var gridData = jQuery('#contract-farming-tables').datagrid('getData');

    var sumFr = new Fraction();

    if (gridData['rows'][0]) {
        for (var i = 0; i < gridData['rows'].length; i++) {
            var f;
            if (gridData['rows'][i].numerator && gridData['rows'][i].denominator) {
                f = new Fraction(gridData['rows'][i].numerator / gridData['rows'][i].denominator);
            }
            else {
                f = new Fraction(gridData['rows'][i].percent / 100);
            }

            sumFr = sumFr.add(f);
        }
    }

    return sumFr;
}
