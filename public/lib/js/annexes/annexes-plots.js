/*jslint browser: true */
/*global jQuery, EasyUIRPCLoaders.EasyUIGridCustomLoader, TF, onHidePanelMultiSelect, onComboMultiSelect, initLoadingItems, setUserRights, hasPlotRightsRW, messagerPlotsWriteRights, bufferview*/

function initAnnexesPlotsGrid(annex_id, contract_id) {

    var annexesPlotsTables = jQuery('#annex-plots-tables'),
        isDatagridBound = annexesPlotsTables.data().hasOwnProperty('datagrid');

    if (isDatagridBound) {
        if (annex_id !== 0) {
            annexesPlotsTables.datagrid({
                url: 'index.php?annexes-rpc=annexes-plots-grid',
                rpcParams: [{
                    type: 'view',
                    contract_id: contract_id,
                    annex_id: annex_id
                }]
            });
        } else {
            annexesPlotsTables.datagrid('loadData', {rows: [], total: 0});
        }
        return;
    }

    annexesPlotsTables.datagrid({
        iconCls: 'icon-edit-geometry',
        nowrap: true,
        title: 'Имоти',
        autoRowHeight: true,
        striped: true,
        pageSize: 30,
        fit: true,
        singleSelect: true,
        fitColumns: false,
        showFooter: true,
        view: bufferview,
        sortName: 'gid',
        sortOrder: 'desc',
        idField: 'gid',
        rowStyler: function (index, row) {
            var style = [];
            if (row.annex_action == 'removed') {
                style.push('text-decoration: line-through');
            }
            if (row.is_edited == true) {
                style.push('color: #aaa');
            }

            setCopyErrorStyles(row.gid, style);
    
            return style.join(';');
        },
        columns: [[
            {
                field: 'land',
                title: '<b>Землище</b>',
                sortable: false,
                width: 100
            }, {
                field: 'kad_ident',
                title: '<b>Идентификатор</b>',
                sortable: true,
                width: 100
            }, {
                field: 'category',
                title: '<b>Категория</b>',
                sortable: true,
                width: 100
            }, {
                field: 'area_type',
                title: '<b>НТП</b>',
                sortable: true,
                width: 100
            }, {
                field: 'contract_area',
                title: '<b>Площ по<br/>договор (дка)</b>',
                align: 'center',
                sortable: true,
                width: 90
            }, {
                field: 'document_area',
                title: '<b>Площ по<br/>документ (дка)</b>',
                align: 'center',
                sortable: true,
                width: 90
            }, {
                field: 'area_for_rent',
                title: '<b>Площ за<br/>рента (дка)</b>',
                align: 'center',
                sortable: true,
                width: 90
            },
            {
                field: 'rent_per_plot',
                title: '<b>Цена/дка</b>',
                align: 'center',
                sortable: true,
                width: 90,
                formatter: function(value, row, index){
                    if (value === "0.00"){
                        return "-";
                    } else {
                        return value;
                    }
                }
            }
        ]],
        pagination: false,
        rownumbers: true,
        toolbar: [
            {
                id: 'btninneraddcontractplot',
                text: 'Добавяне',
                iconCls: 'icon-add',
                handler: function () {
                    if (!hasPlotRightsRW) {
                        messagerPlotsWriteRights();
                        return;
                    }
                    var annexData = jQuery('#annexes-tree').tree('getSelected'),
                        contractID;

                    if (annexData) {
                        contractID = annexData.attributes.parent_id;
                        jQuery('#win-plots-add').window('open');
                        initAnnexesPlotsAddGrid(annexData.id, contractID);

                    } else {
                        jQuery.messager.alert('Грешка', 'Не е избран договор');
                    }
                }
            }, {
                id: 'btninnerdeletecontractplot',
                text: 'Премахване',
                iconCls: 'icon-delete',
                handler: function () {
                    markPlotAsRemoved(
                        annexesPlotsTables,
                        jQuery('#annexes-tree')
                    );
                }
            }, {
                id: 'btnviewplotinfo',
                text: 'Информация',
                iconCls: 'icon-info',
                handler: function () {
                    var getChecked = annexesPlotsTables.datagrid('getChecked');
                    if (getChecked[0]) {
                        window.open("index.php?page=Plots.Home&plot_id=" + getChecked[0].gid, '_blank');
                    } else {
                        jQuery.messager.alert('Грешка', 'Моля изберете имот, за който да бъде показана информация.');
                    }
                }
            }
        ],
        onBeforeLoad: function () {
            annexesPlotsTables.datagrid('clearChecked');
            setGidsFromCopy();
        },
        onLoadSuccess: function () {
            var plotData = annexesPlotsTables.datagrid('getData');
            var gridOpts = annexesPlotsTables.datagrid('options');

            annex_id = jQuery('#annexes-tree').tree('getSelected').id;
            var selectedRow = gridOpts.view.renderedCount - gridOpts.pageSize;
            if (selectedRow >= 0) {
                annexesPlotsTables.datagrid('selectRow', selectedRow);
            } else if (selectedRow < 0) {
                annexesPlotsTables.datagrid("selectRow", 0);
            } else {
                initAnnexesOwnersGrid(0, annex_id);
            }
        },
        onSelect: function (rowIndex, rowData) {
            annex_id = jQuery('#annexes-tree').tree('getSelected').id;
            selectedPlotID = rowData.gid;
            initAnnexesFarmingGrid(rowData.gid, annex_id);

        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    //custom pager
    var pager = annexesPlotsTables.datagrid('getPager');
    pager.pagination({
        beforePageText: 'Стр.',
        displayMsg: 'От {from} до {to} от {total} записа'
    });
}

function initAnnexesPlotsAddGrid(annex_id, contract_id) {
    var hasOverallRenta = false;
    if (jQuery('#annexes-tree').tree('getSelected').attributes.overall_renta != null) {
        hasOverallRenta = true;
     }
    'use strict';
    jQuery('#annex-plots-add-tables').datagrid({
        iconCls: 'icon-plots',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 10,
        fit: true,
        border: false,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?annexes-rpc=annexes-plots-grid',
        rpcParams: [{
            type: 'add',
            contract_id: contract_id,
            annex_id: annex_id
        }],
        sortName: 'gid',
        sortOrder: 'desc',
        idField: 'gid',
        rowStyler: function (index, row) {
            var style = [];
            if (row.is_edited == true) {
                style.push('color: #aaa');
            }

            return style.join(';');
        },
        frozenColumns: [[
            {
                field: 'ck',
                checkbox: true
            }
        ]],
        columns: [[
            {
                field: 'land',
                title: '<b>Землище</b>',
                sortable: false,
                width: 150
            }, {
                field: 'kad_ident',
                title: '<b>Идентификатор</b>',
                sortable: true,
                width: 150
            }, {
                field: 'category',
                title: '<b>Категория</b>',
                sortable: true,
                width: 150
            }, {
                field: 'area_type',
                title: '<b>НТП</b>',
                sortable: true,
                width: 150
            }, {
                field: 'contract_area',
                title: '<b>Площ по<br/>договор (дка)</b>',
                align: 'center',
                sortable: true,
                width: 150,
                editor: {
                    type: 'numberbox',
                    options: {
                        required: true,
                        missingMessage: 'Полето е задължително',
                        precision: 3
                    }
                }
            }, {
                field: 'document_area',
                title: '<b>Площ по<br/>документ (дка)</b>',
                align: 'center',
                sortable: true,
                width: 150,
                editor: {
                    type: 'numberbox',
                    options: {
                        required: true,
                        missingMessage: 'Полето е задължително',
                        precision: 3
                    }
                }
            }, {
                field: 'area_for_rent',
                title: '<b>Площ за<br/>рента (дка)</b>',
                align: 'center',
                sortable: true,
                width: 150,
                editor: {
                    type: 'numberbox',
                    options: {
                        required: true,
                        missingMessage: 'Полето е задължително',
                        precision: 3
                    }
                }
            },
            {
                field: 'rent_per_plot',
                title: '<b>Цена/дка</b>',
                sortable: false,
                halign: 'center',
                width: 100,
                editor: {
                        type: 'numberbox',
                        options: {
                            required: false,
                            missingMessage: 'Полето е задължително',
                            precision: 2,
                            disabled: hasOverallRenta
                        }
                    }
            },

        ]],
        pagination: true,
        rownumbers: true,
        toolbar: [{
            id: 'btnaddcontractplotrelation',
            text: 'Добавяне',
            iconCls: 'icon-add',
            handler: function () {
                var getChecked = jQuery('#annex-plots-add-tables').datagrid('getChecked');
                if (getChecked[0]) {
                    var obj = {},
                        plot_data_array = [],
                        is_selected_historical_plot = false,
                        i,
                        index,
                        plot = {};

                    for (i = 0; i < getChecked.length; i++) {
                        index = jQuery('#annex-plots-add-tables').datagrid('getRowIndex', getChecked[i]);
                        jQuery('#annex-plots-add-tables').datagrid('endEdit', index);

                        plot.gid = getChecked[i].gid;
                        plot.pc_rel_id = getChecked[i].pc_rel_id;
                        plot.contract_area = getChecked[i].contract_area;
                        plot.document_area = getChecked[i].document_area;
                        plot.area_for_rent = getChecked[i].area_for_rent;
                        plot.rent_per_plot = getChecked[i].rent_per_plot;

                        if (getChecked[i].is_edited == true && is_selected_historical_plot == false) {
                            is_selected_historical_plot = true;
                        }
                        plot_data_array.push(plot);
                    }
                    obj.plot_data_array = plot_data_array;
                    obj.contract_id = contract_id;
                    if (is_selected_historical_plot == true) {
                        jQuery.messager.confirm('Потвърждение', 'Избраните имоти включват исторически имоти,' +
                            ' които не са част от актуалната КВС/КК. Желаете ли да продължите?', function (r) {
                                if (r) {
                                    var annexData = jQuery('#annexes-tree').tree('getSelected');
                                    obj.annex_id = annexData.id;

                                    TF.Rpc.Annexes.AnnexesPlots.addAnnexPlotRelation(obj)
                                        .done(function () {
                                            jQuery('#win-plots-add').window('close');
                                            jQuery('#annex-plots-add-tables').datagrid('uncheckAll');
                                            jQuery('#annex-plots-add-tables').datagrid('unselectAll');
                                            jQuery('#annex-plots-tables').datagrid('reload');
                                        });
                                }
                            });
                    } else {
                        var annexData = jQuery('#annexes-tree').tree('getSelected');
                        obj.annex_id = annexData.id;

                        TF.Rpc.Annexes.AnnexesPlots.addAnnexPlotRelation(obj)
                            .done(function () {
                                jQuery('#win-plots-add').window('close');
                                jQuery('#annex-plots-add-tables').datagrid('uncheckAll');
                                jQuery('#annex-plots-add-tables').datagrid('unselectAll');
                                jQuery('#annex-plots-tables').datagrid('reload');
                            });
                    }
                } else {
                    jQuery.messager.alert('Грешка', 'Моля изберете парцели, който да бъдат добавени към договора.');
                }
            }
        }, {
            id: 'btnaddcontractplotfilter',
            text: 'Филтриране',
            iconCls: 'icon-filter',
            handler: function () {
                jQuery('#win-add-plots-filter').window('open');
            }
        }, {
            id: 'btnaddcontractplotfilterclear',
            text: 'Покажи всички',
            iconCls: 'icon-clear-filter',
            handler: function () {
                clearAddPlotGridFilter();
            }
        }],
        onBeforeLoad: function () {
            jQuery(this).datagrid('clearChecked');
        },
        onSelect: function (index) {
            jQuery(this).datagrid('beginEdit', index);
        },
        onUnselect: function (index) {
            jQuery(this).datagrid('cancelEdit', index);
        },
        onCheckAll: function (rows) {
            var index,
                i;
            for (i = 0; i < rows.length; i++) {
                index = jQuery(this).datagrid('getRowIndex', rows[i]);
                jQuery(this).datagrid('beginEdit', index);
            }
        },
        onUncheckAll: function (rows) {
            var index,
                i;
            for (i = 0; i < rows.length; i++) {
                index = jQuery(this).datagrid('getRowIndex', rows[i]);
                jQuery(this).datagrid('cancelEdit', index);
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function filterAddPlotGrid() {
    'use strict';
    if (jQuery('#search-ekatte > input').combobox('getValue')
            || jQuery('#search-masiv > input').val().length != 0
            || jQuery('#search-number > input').val().length != 0) {
        jQuery('#annex-plots-add-tables').datagrid({
            rpcParams: [{
                ekate: jQuery('#search-ekatte > input').combobox('getValue'),
                masiv: jQuery('#search-masiv > input').val(),
                number: jQuery('#search-number > input').val()
            }]
        });
        jQuery('#win-add-plots-filter').window('close');
        return true;
    }
    jQuery.messager.alert('Грешка', 'Моля въведете параметри за филтриране.');
    return false;
}

function clearAddPlotGridFilter() {
    'use strict';
    jQuery('#search-ekatte > input').combobox('reset');
    jQuery('#search-masiv > input').val('');
    jQuery('#search-number > input').val('');
    jQuery('#annex-plots-add-tables').datagrid({
        rpcParams: [{}]
    });
}
