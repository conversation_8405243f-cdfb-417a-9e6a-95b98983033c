/*jslint browser: true */
/*global jQuery, EasyUIRPCLoaders.EasyUIGridCustomLoader, TF, onHidePanelMultiSelect, onComboMultiSelect, initLoadingItems, setUserRights, hasPlotRightsRW, messagerPlotsWriteRights*/
var editAnnexID;
var annexParentStatus;

function initAnnexesTree(pageNumber, filterObj) {
    'use strict';
    editAnnexID = undefined;
    var page_number = 1,
        annexesTree = jQuery('#annexes-tree'),
        isTreeBound = annexesTree.data().hasOwnProperty('tree');

    if (pageNumber != undefined) {
        page_number = pageNumber;
    }


    if (isTreeBound) {
        annexesTree.tree({
            rpcParams: [filterObj],
            page: page_number
        });
        return;
    }

    annexesTree.tree({
        url: 'index.php?annexes-rpc=annexes-tree',
        animate: false,
        lines: true,
        page: page_number,
        sort: 'id',
        order: 'desc',
        rpcParams: [filterObj],
        rows: 30,
        onSelect: function (node) {
            initFilesGrid(node.id);
            initAnnexesPlotsGrid(node.id, node.attributes.parent_id);
            selectedContractID = node;
            //fill annex info
            fillAnnexInfo(node.attributes);

            //fill parent contract data

            TF.Rpc.Annexes.AnnexesTree.getParentContractData(node.attributes.parent_id)
                .done(function (data) {
                    fillAnnexParentInfo(data);
                });
        },
        onLoadSuccess: function () {
            var roots = jQuery('#annexes-tree').tree('getRoots'),
                total = 0,
                limit = 10,
                node;
            if (roots.length) {
                if (editAnnexID != undefined) {
                    node = jQuery('#annexes-tree').tree('find', editAnnexID);
                    jQuery('#annexes-tree').tree('select', node.target);
                } else {
                    jQuery('#annexes-tree').tree('select', roots[0].target);
                }
                total = roots[0].attributes.pagination.total;
                limit = roots[0].attributes.pagination.limit;
            } else {
                initFilesGrid(0);
                initAnnexesPlotsGrid(0, 0);

                //fill annex info
                fillAnnexInfo();
                fillAnnexParentInfo();
            }

            //init pagination with total contract elements
            initAnnexesPagination(total, limit);
        },
        onBeforeLoad: function (node, param) {
        },
        formatter: function (node) {
            if (node.attributes.active_text == 'Анулиран') {
                node.text = '<font color="#aaa"><strike>' + node.text + '</strike></font>';
            }
            if (node.attributes.active_text == 'Изтекъл') {
                node.text = '<font color="#aaa">' + node.text + '</font>';
            }
            return node.text;
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initAnnexesPagination(total, limit) {
    jQuery('#annexes-tree-pagination').pagination({
        showPageList: false,
        showRefresh: false,
        displayMsg: '',
        total: total,
        pageSize: limit,
        onSelectPage: function (pageNumber, pageSize) {
            var obj = {};
            obj.c_num = jQuery('#search-annex > input').val();
            obj.date_from = jQuery('#search-date-from > input').datebox('getValue');
            obj.date_to = jQuery('#search-date-to > input').datebox('getValue');
            obj.due_date_from = jQuery('#search-due-date-from > input').datebox('getValue');
            obj.due_date_to = jQuery('#search-due-date-to > input').datebox('getValue');
            obj.farming = jQuery('#search-farming > input').combobox('getValues');
            obj.renta_types = jQuery('#search-renta-types > input').combobox('getValues');

            initAnnexesTree(pageNumber, obj);
        }
    });
}

function fillAnnexInfo(data) {
    if(!data) {
        jQuery('#info-annex-number').html('');
        jQuery('#info-annex-date').html('');
        jQuery('#info-start-date').html('');
        jQuery('#info-due-date').html('');
        jQuery('#info-comment').html('');
        jQuery('#info-renta').html('');
        jQuery('#info-renta-label').html('');
        jQuery('#info-renta-nat-type').html('');
        jQuery('#info-renta-nat').html('');
        jQuery('#info-sv-num').html('');
        jQuery('#info-sv-date').html('');
        jQuery('#info-osz-num').html('');
        jQuery('#info-osz-date').html('');
        jQuery('#info-pd-date').html('');
        jQuery('#info-active-text').html('');
        jQuery('.js-annex-renta-additional-row').remove();

        return false;
    }

    jQuery('#info-annex-number').html(data.c_num);
    jQuery('#info-annex-date').html(data.c_date);
    jQuery('#info-start-date').html(data.start_date);
    jQuery('#info-due-date').html(data.due_date);
    jQuery('#info-comment').html(data.comment);
    jQuery('#info-renta').html(data.renta_text);
    jQuery('#info-renta-label').html('Рента/дка:');
    jQuery('#info-renta-nat-type').html(data.renta_nat_type);
    jQuery('#info-renta-nat').html(data.renta_nat_text);
    jQuery('#info-sv-num').html(data.sv_num);
    jQuery('#info-sv-date').html(data.sv_date);
    jQuery('#info-osz-num').html(data.osz_num);
    jQuery('#info-osz-date').html(data.osz_date);
    jQuery('#info-pd-date').html(data.payday);

    if (data.overall_renta != null) {
        jQuery('#info-renta').html(data.overall_renta);
        jQuery('#info-renta-label').html('Обща сума за договор:');
    }

    var paymonth = '';
    if (months[data.paymonth] != undefined) {
        paymonth = months[data.paymonth]['label'];
    }
    jQuery('#info-pd-date').html(data.payday + ' ' + paymonth);
    jQuery('#info-active-text').html(data.active_text);

    var rowHTML;
    jQuery('.js-annex-renta-additional-row').remove();
    for(var i = 0; i < data.additionalRentas.length; i++) {
        rowHTML = '<tr class="js-annex-renta-additional-row"><td>' + data.additionalRentas[i]['renta_nat_type'] + '</td><td  style="padding-left:200px;">' + data.additionalRentas[i]['renta_nat_text'] + '</td></tr>';
        jQuery('#js-annex-renta-table').append(rowHTML);
    }
}

function fillAnnexParentInfo(data) {
    if(!data)
    {
        jQuery('#info-contract-type').html('');
        jQuery('#info-contract-number').html('');
        jQuery('#info-contract-date').html('');
        jQuery('#info-contract-start-date').html('');
        jQuery('#info-contract-due-date').html('');
        jQuery('#info-contract-farming').html('');
        jQuery('#info-contract-comment').html('');
        jQuery('#info-contract-renta').html('');
        jQuery('#info-contract-renta-label').html('');
        jQuery('#info-contract-renta-nat-type').html('');
        jQuery('#info-contract-renta-nat').html('');
        jQuery('#info-contract-sv-num').html('');
        jQuery('#info-contract-sv-date').html('');
        jQuery('#info-contract-osz-num').html('');
        jQuery('#info-contract-osz-date').html('');
        jQuery('#info-contract-pd-date').html('');
        jQuery('.js-annex-parent-additional-row').remove();

        return false;
    }

    annexParentStatus = data.active;
    if (data) {
        jQuery('#info-contract-type').html(data.nm_usage_rights);
        jQuery('#info-contract-number').html(data.c_num);
        jQuery('#info-contract-date').html(data.c_date);
        jQuery('#info-contract-start-date').html(data.start_date);
        jQuery('#info-contract-due-date').html(data.due_date);
        jQuery('#info-contract-farming').html(data.farming);
        jQuery('#info-contract-comment').html(data.comment);
        jQuery('#info-contract-renta').html(data.renta_text);
        jQuery('#info-contract-renta-label').html('Рента/дка:');
        jQuery('#info-contract-renta-nat-type').html(data.renta_nat_type);
        jQuery('#info-contract-renta-nat').html(data.renta_nat_text);
        jQuery('#info-contract-sv-num').html(data.sv_num);
        jQuery('#info-contract-sv-date').html(data.sv_date);
        jQuery('#info-contract-osz-num').html(data.osz_num);
        jQuery('#info-contract-osz-date').html(data.osz_date);
        jQuery('#info-contract-pd-date').html(data.payday);

        if (data.overall_renta != null) {
            jQuery('#info-contract-renta-label').html('Обща сума за договор:');
            jQuery('#info-contract-renta').html(data.overall_renta);
        }

        var rowHTML;
        jQuery('.js-annex-parent-additional-row').remove();
        for(var i = 0; i < data.additionalRentas.length; i++) {
            rowHTML = '<tr class="js-annex-parent-additional-row"><td>' + data.additionalRentas[i]['renta_nat_type'] + '</td><td  style="padding-left:200px;">' + data.additionalRentas[i]['renta_nat_text'] + '</td></tr>';
            jQuery('#js-annex-parent-table').append(rowHTML);
        }
    }
}
