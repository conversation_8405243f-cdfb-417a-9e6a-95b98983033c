/*jslint browser: true */
/*global jQuery, EasyUIRPCLoaders.EasyUIGridCustomLoader, TF, onHidePanelMultiSelect, onComboMultiSelect, initLoadingItems, setUserRights, hasPlotRightsRW, messagerPlotsWriteRights, messagerNoRightsRW*/

function initFilesGrid(annex_id) {
    var winDownload = jQuery('#win-download'),
        downloadFile = jQuery('#btn-download-file'),
        annexesFilesTables = jQuery('#annex-files-tables'),
        isDatagridBound = annexesFilesTables.data().hasOwnProperty('datagrid');

    if (isDatagridBound) {
        if (annex_id !== 0) {
            annexesFilesTables.datagrid({
                url: 'index.php?contracts-rpc=contracts-files-maingrid',
                rpcParams: [{
                    contract_id: annex_id
                }],
            });
        } else {
            annexesFilesTables.datagrid('loadData', {rows: [], total: 0});
        }
        return;
    }

    annexesFilesTables.datagrid({
        iconCls: 'icon-files',
        title: 'Архив файлове',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        fit: true,
        fitColumns: true,
        showFooter: false,
        idField: 'id',
        singleSelect: true,
        frozenColumns: [[
            {
                field: 'ck',
                checkbox: true
            }
        ]],
        columns: [[
            {
                field: 'filename',
                title: '<b>Файл</b>',
                sortable: false,
                width: 100
            }, {
                field: 'date',
                title: '<b>Дата</b>',
                sortable: false,
                width: 100
            }
        ]],
        rownumbers: true,
        toolbar: [{
            id: 'btnaddcontractfile',
            text: 'Добавяне',
            iconCls: 'icon-add',
            handler: function () {
                if (!hasPlotRightsRW) {
                    messagerPlotsWriteRights();
                    return;
                }
                annex_id = jQuery('#annexes-tree').tree('getSelected').id;
                if (annex_id) {

                    //No Rights
                    if (!hasPlotRightsRW) {
                        EasyUIRPCLoaders.messagerNoRightsRW();
                        return false;
                    }

                    initFileUploads(annex_id);
                    jQuery('#win-add-file').window('open');
                } else {
                    jQuery.messager.alert('Грешка', 'Не е избран договор.');
                }
            }
        }, {
            id: 'btndeletecontractfile',
            text: 'Изтриване',
            iconCls: 'icon-remove',
            handler: function () {
                if (!hasPlotRightsRW) {
                    messagerPlotsWriteRights();
                    return;
                }
                annex_id = jQuery('#annexes-tree').tree('getSelected').id;
                var getChecked = annexesFilesTables.datagrid('getChecked');

                if (getChecked[0]) {
                    jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да премахнете този запис?', function (r) {
                        if (r) {
                            var file_params = {
                                'annex_id': annex_id,
                                'file_id': getChecked[0].file_id
                            };
                            TF.Rpc.Annexes.AnnexesFiles.deleteAnnexFile(file_params)
                                .done(function () {
                                    annexesFilesTables.datagrid('loadRpc');
                                });
                        }
                    });
                } else {
                    jQuery.messager.alert('Грешка', 'Не е избран файл!');
                }
            }
        }, {
            id: 'btndownloadcontractfile',
            text: 'Изтегляне',
            iconCls: 'icon-export',
            handler: function () {
                var getChecked = annexesFilesTables.datagrid('getChecked');

                if (getChecked[0]) {
                    var file_id = getChecked[0].id;
                    TF.Rpc.Annexes.AnnexesFiles.downloadAttached(file_id)
                        .done(function (data) {
                            winDownload.window('open');
                            downloadFile.attr("href", data);
                        });
                } else {
                    jQuery.messager.alert('Грешка', 'Моля изберете файл, който искате да изтеглите.');
                }
            }
        }],
        onBeforeLoad: function () {
            annexesFilesTables.datagrid('clearChecked');
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initFileUploads(annex_id) {
    const url  = "index.php?json=contract-upload"; 

    jQuery("#uploader").pluploadQueue({
        // General settings
        runtimes: 'gears,html5,flash,silverlight,browserplus',
        url: url,
        multipart_params : {
            "contract_id" : annex_id,
        },
        max_file_size: '100mb',
        unique_names: true,
        // Flash settings
        flash_swf_url: 'lib/js_external/fileupload/plupload.flash.swf'
    });

    var uploader = jQuery('#uploader').pluploadQueue();
    uploader.bind('UploadComplete', function() {
        jQuery('#win-add-file').window('close');
        jQuery('#annex-files-tables').datagrid('reload');
    });
}
