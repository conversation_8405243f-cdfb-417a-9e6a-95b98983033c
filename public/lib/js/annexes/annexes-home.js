/*jslint browser: true */
/*global jQuery, EasyUIRPCLoaders.EasyUIGridCustomLoader, TF, onHidePanelMultiSelect, onComboMultiSelect, initLoadingItems, setUserRights, hasPlotRightsRW, messagerPlotsWriteRights*/
var rentaResult,
    CONTRACT_TYPE_OWN = 1,
    ComboboxData,
    contragent_type;

jQuery(function () {
    'use strict';
    setUserRights();
    TF.Rpc.Common.CombinedComboboxData.read()
        .done(function (data) {
            ComboboxData = data;
            //initComboboxItems();
            initSearchComboboxFields();
        })
        .fail(function (error) {
            if (error) jQuery.messager.alert('Грешка', error.getMessage(), 'warning');
        });
    //retrieve GET parameters
    var GET = {filterUserFarmings: true};
    location.search.substr(1).split("&").forEach(function (item) {GET[decodeURIComponent(item.split("=")[0])] = decodeURIComponent(item.split("=")[1])});

    //change URL without refresh if it's posible
    if (history && history.replaceState) {
        history.replaceState(null, null, 'index.php?page=' + GET.page);
    }

    initAnnexesTree(1, GET);
    initFilesGrid(0);
    initAnnexesPlotsGrid(0, 0);

    jQuery('#eik > input').numberbox({});
   
    new EgnValidateBox('#egn > input');
    
    jQuery('#win-add-edit-annex').window({
        onClose: onWindowAddEditClose
    });

    //init button icons
    jQuery('#save-annex-button > a').linkbutton({iconCls: 'icon-save'});
    jQuery('#add-plots-filter-btn > a').linkbutton({iconCls: 'icon-search'});

    //init button controls
    jQuery('#btn-edit-annex').bind('click', function () {
        if (!hasPlotRightsRW) {
            messagerPlotsWriteRights();
            return false;
        }
        var annexData = jQuery('#annexes-tree').tree('getSelected');

        if (annexData) {
            editAnnexID = annexData.id;
            TF.Rpc.Annexes.AnnexesTree.markAnnexForEdit(editAnnexID)
            .done(function (data) {
                initEditAnnexFields(data);
                resetEditAnnexFields();
                setEditAnnexFieldsData(data);
                jQuery('#win-add-edit-annex').window('open');
            })
            .fail(function (data) {
                jQuery.messager.alert('Грешка', 'Не може да въвеждате анекси със застъпващи се периоди на действие!','warning');
            });

        } else {
            jQuery.messager.alert('Грешка', 'Не е избран договор');
        }
        return false;
    });

    jQuery('#btn-delete-annex').bind('click', function () {
        if (!hasPlotRightsRW) {
            messagerPlotsWriteRights();
            return false;
        }
        var annexData = jQuery('#annexes-tree').tree('getSelected');

        if (annexData) {
            jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да премахнете този анекс?', function (r) {
                if (r) {
                    editAnnexID = undefined;

                    TF.Rpc.Annexes.AnnexesTree.deleteAnnex(annexData.id)
                    .done(function (data) {
                        jQuery('#annexes-tree').tree('loadRpc');
                    });

                }
            });
        } else {
            jQuery.messager.alert('Грешка', 'Не е избран анекс');
        }
        return false;
    });

    jQuery('#btn-filter-annexes').bind('click', function () {
        jQuery('#win-filter-annexes').window('open');
        return false;
    });

    jQuery('#btn-filter-contracts-tree').bind('click', function () {
        let obj = getFilterContractsTreeParams();

        //reinit tree with query params
        initAnnexesTree(1, obj);
        jQuery('#win-filter-annexes').window('close');
    });

    jQuery('#btn-clear-filter-annexes').bind('click', function () {
        return clearContractsFilter(initAnnexesTree);
    });

    jQuery('#btn-change-annex-status').bind('click', function () {
        if (!hasPlotRightsRW) {
            messagerPlotsWriteRights();
            return false;
        }
        var getSelected = jQuery('#annexes-tree').tree('getSelected');
        var comment = '';
        var alert_text = '';
        var obj = new Object;

        if (getSelected) {
            if(getSelected.attributes.comment != '-') {
                comment = getSelected.attributes.comment;
            }

            if (getSelected.attributes.active == 0) {
                alert_text = 'Сигурни ли сте, че искате да маркирате този анекс като действащ?';
                obj.status = true;
            } else {
                alert_text = 'Сигурни ли сте, че искате да маркирате този анекс като анулиран?';
                obj.status = false;
            }

            alert_text += '<table width="100%" cellspacing="0" cellpadding="0" style="margin-top:10px;">';
            alert_text += '<tr>';
            alert_text += '<td>Забележка</td>';
            alert_text += '<td style="padding-left: 10px;">';
            alert_text += '<textarea id="annex-status-comment" style="resize: none; width:190px; height:60px;">' + comment + '</textarea>';
            alert_text += '</td>';
            alert_text += '</tr>';
            alert_text += '</table>';

            if(annexParentStatus) {
                jQuery.messager.confirm('Потвърждение', alert_text, function (r) {
                    if (r) {
                        editAnnexID = getSelected.id;
                        obj.id = getSelected.id;
                        obj.comment = jQuery('#annex-status-comment').val();

                        TF.Rpc.Annexes.AnnexesTree.changeActiveStatus(obj)
                            .done(function (data) {
                                jQuery('#annexes-tree').tree('loadRpc');
                            })
                            .fail(function (errorObj) {
                                jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
                            });
                    }
                });
            }
            else {
                jQuery.messager.alert('Грешка', 'Не може да активирате анекс, чиито договор е анулиран!');
            }
        } else {
            jQuery.messager.alert('Грешка', 'Моля изберете договор, който искате да анулирате!');
        }
        return false;
    });

    jQuery('#btn-print-annex').bind('click', function () {
        var getSelected = jQuery('#annexes-tree').tree('getSelected');

        if (getSelected) {
            if (getSelected.attributes.c_type != 1 && getSelected.attributes.c_type != 4)
            {
                contracts_templates.initPrintTemplatesGrid(getSelected.id);
                jQuery('#win-contracts-templates').window('open');
            } else {
                jQuery.messager.alert('Грешка', 'Договори от тип собственост или споразумение не могат да бъдат разпечатвани!');
            }
        } else {
            jQuery.messager.alert('Грешка', 'Моля изберете договор!');
        }
        return false;
    });

    //init main filter components
    jQuery('#search-date-from  > input').datebox();
    jQuery('#search-date-to  > input').datebox();
    jQuery('#search-due-date-from  > input').datebox();
    jQuery('#search-due-date-to  > input').datebox();
});

function initComboboxItems () {

    jQuery('#search-farming  > input').combobox({
        data: ComboboxData.FarmingCombobox,
        editable: false,
        valueField: 'id',
        textField: 'name',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    jQuery('#search-renta-types > input').combobox({
        editable: false,
        url: 'index.php?common-rpc=renta-types-combobox',
        valueField: 'id',
        textField: 'name',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-ekatte > input').combobox({
        data: ComboboxData.EkateCombobox,
        valueField: 'ekate',
        textField: 'text',
        filter: function (q, row) {
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
            {
                return true;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter

    });

    jQuery('#rent-place > input').combobox({
        data: ComboboxData.EkateCombobox,
        valueField: 'ekate',
        textField: 'text',
        filter: function(q, row){
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
            {
                return true;
            }
        },
        onLoadSuccess: function (data) {
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initEditAnnexFields(data) {
    rentaResult = data.rentaResult;

    jQuery('#annex-number > input').validatebox({
        required: true,
        missingMessage: 'Моля въведете номер на анекс.'
    });
    jQuery('#annex-date > input, #annex-start-date > input, #annex-due-date > input').datebox();
    jQuery('#annex-sv-date > input').datebox({});
    jQuery('#annex-osz-date > input').datebox({});

    var daySpinner = jQuery('#annex-pd-day > input');
    jQuery('#annex-pd-month > input').combobox({
        data: months,
        valueField: 'value',
        textField: 'label',
        onLoadSuccess: function () {
            daySpinner.numberspinner({
                min: 1,
                max: 31,
                precision: 0,
            });
        },
        onSelect: function (rec) {
            var day = daySpinner.numberspinner('getValue');
            if(day > rec.maxDays) {
                day = rec.maxDays;
            }
            daySpinner.numberspinner({
                min: 1,
                max: rec.maxDays,
                precision: 0,
                value:day,
            });
        }
    });

    jQuery('#add-new-annex-renta-btn').linkbutton({
        iconCls: 'icon-add',
        width:26
    });

    jQuery('#remove-annex-renta-btn').linkbutton({
        iconCls: 'icon-cancel',
        width:26
    });

    if(data.annexData.overall_renta != null) {
        jQuery('#annex-overall-renta > input').numberspinner({
            precision: 2,
            min: 0
        });

        jQuery('#annex-overall-renta').show();
        jQuery('#annex-overall-renta-label').show();
        jQuery('#annex-renta').hide();
        jQuery('#annex-renta-label').hide();
    } else {
        jQuery('#annex-renta > input').numberspinner({
            precision: 2,
            min: 0
        });

        jQuery('#annex-overall-renta').hide();
        jQuery('#annex-overall-renta-label').hide();
        jQuery('#annex-renta').show();
        jQuery('#annex-renta-label').show();
    }

    if(rentaResult.length > 0) {
        for(var i = 0; i < rentaResult.length; i++) {
            var addFieldParams = {index:i, data:rentaResult[i]};
            addEditRentaField(addFieldParams);
        }
    } else {
        addEditRentaField();
    }


    jQuery('#renta-error-win').window({
        width:275,
        height:150,
        modal:true,
        closed:true,
        resizable: false,
        collapsible: false,
        minimizable: false,
        maximizable: false,
        title: 'Грешка при избор на тип на рента.'
    });
}

function validateSaveAnnex() {
    if (jQuery('#annex-number > input').val()
            && jQuery('#annex-date > input').datebox('getValue')
            && jQuery('#annex-start-date > input').datebox('getValue')
            && jQuery('#annex-due-date > input').datebox('getValue')) {
        if (!compareDates('#annex-start-date > input', '#annex-due-date > input', 2)) {
            jQuery.messager.alert('Грешка', 'Въведена е грешна дата - крайната дата е по-ранна от датата на влизане в сила на анекса.', 'warning');
            return false;
        }
        var obj = getAnnexData();

        TF.Rpc.Annexes.AnnexesTree.editAnnex(obj)
        .done(function (data) {
            jQuery('#win-add-edit-annex').window('close');
            jQuery('#annexes-tree').tree('loadRpc');
        })
        .fail(function (errorObj) {
            jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
        });
    } else {
        if (jQuery('#annex-number > input').val() == '') {
            jQuery.messager.alert('Грешка', 'Моля задайте номер на анекс!');
        }
        return false;
    }
}

function editAnnexResult(result) {

    if (!result) {
        jQuery.messager.alert('Грешка', 'Не може да въвеждате анекси със застъпващи се периоди на действие!');
    }
    else {
        jQuery('#win-add-edit-annex').window('close');
        jQuery('#annexes-tree').tree('reload');
    }
}

function getAnnexData() {
    var obj = new Object;
    var contractData = jQuery('#annexes-tree').tree('getSelected');

    obj.contractId = contractData.id;
    obj.c_num = jQuery('#annex-number > input').val();
    obj.c_date = jQuery('#annex-date > input').datebox('getValue');
    obj.start_date = jQuery('#annex-start-date > input').datebox('getValue');
    obj.sv_num = jQuery('#annex-sv-num > input').val();
    obj.sv_date = jQuery('#annex-sv-date > input').datebox('getValue');
    obj.osz_num = jQuery('#annex-osz-num > input').val();
    obj.osz_date = jQuery('#annex-osz-date > input').datebox('getValue');
    obj.comment = jQuery('#annex-comment > textarea').val();
    obj.due_date = jQuery('#annex-due-date > input').datebox('getValue');
    obj.pd_day = jQuery('#annex-pd-day > input').numberspinner('getValue');
    obj.pd_month = jQuery('#annex-pd-month > input').combobox('getValue');
    obj.additionalRentas = new Array();

    if (contractData.attributes.overall_renta != null) {
        obj.overall_renta = jQuery('#annex-overall-renta > input').numberspinner('getValue');
    } else {
        obj.renta = jQuery('#annex-renta > input').numberspinner('getValue');
    }

    for (var i = 0; i < jQuery('.js-annex-renta-type-row').length; i++) {
        obj.additionalRentas[i] = {
            type:parseInt(jQuery('#annex-renta-type-cb-' + i).combobox('getValue')),
            value:parseFloat(jQuery('#annex-renta-value-' + i).val())
        }
    }

    return obj;
}

function addEditRentaField(params)
{
    var rows = jQuery('#js-edit-annex-renta-table').find('.js-annex-renta-type-row');
    var rentaRows = rows.length;

    var removeButton = '<a id="remove-annex-renta-row-btn-' + (rentaRows) + '" href="javaScript:void(0)" class="easyui-linkbutton" onClick="removeAnnexRentaField(' + (rentaRows) + ');"data-options="iconCls:\'icon-cancel\'">&nbsp;</a>';
    var addButton = '<a id="add-new-renta-btn" href="javaScript:void(0)" class="easyui-linkbutton" onClick="addEditRentaField();" data-options="iconCls:\'icon-add\'"></a>';

    var comboHTML ='<tr><td style="padding-left:10px"><select class="js-annex-renta-type-row" id="annex-renta-type-cb-'+(rentaRows)+'"></select></td>';

    //first row
    if(rentaRows == 0) {
        comboHTML += '<td><input class="js-renta-value" style="width: 60px;" id="annex-renta-value-'+(rentaRows)+'"></td><td>'+addButton+'</td></tr>';
    } else {
        comboHTML += '<td><input class="js-renta-value" style="width: 60px;" id="annex-renta-value-'+(rentaRows)+'"></td><td>'+removeButton+'</td></tr>';
    }

    jQuery('#js-edit-annex-renta-table').append(comboHTML);
    jQuery('#remove-annex-renta-row-btn-' + (rentaRows)).linkbutton({
        iconCls: 'icon-cancel',
        width:26
    });

    if(rentaRows == 0) {
        jQuery('#add-new-renta-btn').linkbutton({
            iconCls: 'icon-add',
            width:26,
            disabled: true
        });
    } else {
        jQuery('#add-new-renta-btn').linkbutton({
            disabled: true
        });
    }

    jQuery('#annex-renta-type-cb-'+(rentaRows)).combobox({
        url: 'index.php?common-rpc=renta-types-combobox',
        valueField: 'id',
        textField: 'name',
        width: 205,
        onBeforeLoad: function () {
            var renta_id = 0;
            var renta_value;

            if(params) {
                var renta_value = params['data']['renta_value'];
                var renta_id = params['data']['renta_id'];
            }

            jQuery('#annex-renta-value-'+(rentaRows)).numberspinner({
                min: 0,
                precision: 3,
                missingMessage: 'Моля задайте количество на горепосочената рента в натура(количество за декар).',
                width: 95,
                value: renta_value
            });
            jQuery('#annex-renta-type-cb-'+(rentaRows)).combobox('select', renta_id);
        },
        onSelect: function (rec) {
            var rentaNatValue = jQuery('#annex-renta-value-'+(rentaRows)).numberspinner('getValue');
            var prec = 3;
            if(rec['unit'] === 3) {
                prec = 0;
            }
            jQuery('#annex-renta-value-'+(rentaRows)).numberspinner({
                min: 0,
                precision: prec,
                missingMessage: 'Моля задайте количество на горепосочената рента в натура(количество за декар).',
                value: rentaNatValue,
                width: 95,
            });
            if(jQuery('#annex-renta-type-cb-'+(rentaRows)).combobox('getValue') == 0)
            {
                jQuery('#annex-renta-value-'+(rentaRows)).numberspinner('setValue', '0');
            }

            var rowVars=[];
            var tmpVars = jQuery('.js-annex-renta-type-row');

            for (var i = 0; i < tmpVars.length; i++) {
                var natura_el = jQuery('#annex-renta-type-cb-' + i);
                var natura_el_id = jQuery('#annex-renta-type-cb-' + i)[0].id;
                var natura_id = natura_el.combobox('getValue');
                var natura_select_el_id = jQuery(this)[0].id;

                if(jQuery.isNumeric(natura_id) && natura_el_id != natura_select_el_id) {
                    rowVars.push(natura_id);
                }
            }

            if(rec.id != 0)
            {
                var inRes = jQuery.inArray(String(rec.id),rowVars);
                if (inRes != -1 && jQuery.inArray(String(rec.id),rowVars,inRes) != -1) {
                    jQuery('#renta-error-win').window('open');
                    jQuery('#annex-renta-type-cb-'+(rentaRows)).combobox('clear');
                    jQuery('#annex-renta-value-'+(rentaRows)).numberspinner('setValue', '0');
                }
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function removeAnnexRentaField(index) {
    var parent = jQuery('#annex-renta-type-cb-'+index).parent().parent();
    parent.detach();
}

function setEditAnnexFieldsData(data) {
    var result = data.annexData;
    var editAnnexDay;
    var editAnnexMonth;
    var rentaPayment = parseFloat(result['renta']);

    if (result['payday'] != null) {
        var payday = result['payday'].split('-');
        editAnnexDay = parseInt(payday[0])
        editAnnexMonth = parseInt(payday[1])
    }

    jQuery('#annex-number > input').val(result['c_num']);
    jQuery('#annex-start-date > input').datebox('setValue',result['start_date']);
    jQuery('#annex-date > input').datebox('setValue',result['c_date']);
    jQuery('#annex-due-date > input').datebox('setValue',result['due_date']);
    jQuery('#annex-sv-num > input').val(result['sv_num']);
    jQuery('#annex-sv-date > input').datebox('setValue',result['sv_date']);
    jQuery('#annex-osz-num > input').val(result['osz_num']);
    jQuery('#annex-osz-date > input').datebox('setValue',result['osz_date']);
    jQuery('#annex-pd-day > input').numberspinner('setValue',editAnnexDay);
    jQuery('#annex-pd-month > input').combobox('setValue',editAnnexMonth);
    jQuery('#annex-comment > textarea').val(result['comment']);

    if(result.overall_renta != null) {
        jQuery('#annex-overall-renta > input').numberspinner('setValue', parseFloat(result['overall_renta']));
    } else {
        jQuery('#annex-renta > input').numberspinner('setValue', rentaPayment);
    }
}

function resetEditAnnexFields() {
    jQuery('#annex-number > input').val('');
    jQuery('#annex-start-date > input').val('');
    jQuery('#annex-date > input').val('');
    jQuery('#annex-due-date > input').val('');
    jQuery('#annex-sv-num > input').val('');
    jQuery('#annex-sv-date > input').val('');
    jQuery('#annex-osz-num > input').val('');
    jQuery('#annex-osz-date > input').val('');
    jQuery('#annex-renta > input').val('');
    jQuery('#annex-pd-day > input').val('');
    jQuery('#annex-pd-month > input').val('');
    jQuery('#annex-comment > textarea').val('');
}

function onWindowAddEditClose() {
    var rentaAdditionalRows = jQuery('.js-annex-renta-type-row');

    for (var i = 0; i < rentaAdditionalRows.length; i++) {
        removeAnnexRentaField(i);
    }

    resetEditAnnexFields();
}
