/*jslint browser: true */
/*global jQuery, EasyUIRPCLoaders.EasyUIGridCustomLoader, TF, onHidePanelMultiSelect, onComboMultiSelect, initLoadingItems, setUserRights, hasPlotRightsRW, messagerPlotsWriteRights, Fraction*/
var maxOwnership = new Fraction(1);
var lastAddedOwner = 0;
var lastAddedParent = 0;

function initAnnexesOwnersGrid(plot_id, contract_id) {
    'use strict';
    var selectedPlotID = plot_id,
        selectedContractID = contract_id;

    jQuery('#annex-owners-tables').treegrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 10,
        fit: true,
        singleSelect: true,
        fitColumns: true,
        url: 'index.php?contracts-rpc=contracts-owners-datagrid',
        rpcParams: [{
            type: 'view',
            contract_id: selectedContractID,
            plot_id: selectedPlotID
        }],
        border: false,
        showFooter: false,
        sortName: 'name',
        sortOrder: 'desc',
        idField: 'fakeid',
        treeField: 'owner_names',
        columns: [[
            {
                field: 'owner_names',
                title: '<b>Собственик</b>',
                sortable: false,
                width: 150,
                styler: function (value, row, index) {
                    if (!row.is_heritor) {
                        return 'font-weight: bold;';
                    }
                }
            }, {
                field: 'rep_names',
                title: '<b>Представител</b>',
                sortable: false,
                width: 150
            }, {
                field: 'rat_ownage',
                title: '<b>Собственост</b>',
                sortable: false,
                width: 75,
                formatter: function (value, row) {
                    var percent,
                        fraction,
                        value;

                    if (row.numerator && row.denominator) {
                        percent = new Fraction(row.numerator / row.denominator * 100);
                        fraction = row.numerator + '/' + row.denominator;
                        value = percent.toString() + '% (' +  fraction + ')';

                        return value;
                    }

                    fraction = new Fraction(row.percent / 100);
                    percent = fraction.mul(100);
                    value = percent.toString() + '% (' +  fraction.simplify().toFraction() + ')';

                    return value;
                }
            }
        ]],
        pagination: false,
        rownumbers: true,
        toolbar: '#contracts-owners-toolbar',
        onBeforeLoad: function () {
            jQuery('#annex-owners-tables').treegrid('clearChecked');
        },
        onLoadSuccess: function (row, data) {
            var tabsElement = jQuery('#owners-tabs');
            tabsElement.tabs();
            var selectedAnnex = jQuery('#annexes-tree').tree('getSelected')
            if (data && data.rows.length === 0) {
                if (jQuery('#contract-farming-tables').datagrid('getData').rows.length != 0) {
                    tabsElement.tabs('select', 1);
                } else {
                    tabsElement.tabs('select', 0);
                }
            } else {
                tabsElement.tabs('select', 0);
            }

            if([2,3]. includes(selectedAnnex.attributes.c_type) && selectedAnnex.attributes.from_sublease === null) {
                tabsElement.tabs('disableTab', 1);
            } else {
                tabsElement.tabs('enableTab', 1);
            }

            //calculate total ownership
            var sum = 0;
            var i = 0;
            if (data['rows'].length > 0) {
                for (i = 0; i < data['rows'].length; i++) {
                    sum += parseFloat(data['rows'][i].percent);
                }
            }
            if (sum < 100 && sum!= 0) {
                jQuery('#ownership-warning').show();
            } else {
                jQuery('#ownership-warning').hide();
            }
        },
        onSelect: function (node) {
            if (node.is_heritor) {
                jQuery('#btndeletecontractowner').linkbutton('disable');
                jQuery('#btncopyowners').linkbutton('disable');
                isHeritorSelected = true;
            } else {
                jQuery('#btndeletecontractowner').linkbutton('enable');
                jQuery('#btncopyowners').linkbutton('enable');
                isHeritorSelected = false;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function addOwnerToAnnex() {
    if (!hasPlotRightsRW) {
        messagerPlotsWriteRights();
        return false;
    }
    var plotData = jQuery('#annex-plots-tables').datagrid('getChecked'),
        contractData = jQuery('#annexes-tree').tree('getSelected'),
        max,
        sum;
    //No Rights to operate with "Договори за собственост"
    if (contractData.attributes.c_type == CONTRACT_TYPE_OWN && !hasContractsOwnWriteRights) {
        messagerContractsOwnWriteRights();
        return false;
    }
    if (contractData.attributes.from_sublease > 0) {
        messageContractIsFromSublease(contractData);
        return false;
    }
    max = new Fraction(1);

    if (!plotData[0]) {
        jQuery.messager.alert('Грешка', 'Не е избран имот.');
        return;
    }

    sum = getTotalOwnersShares();

    if (sum.valueOf() >= 1) {
        jQuery.messager.alert('Грешка', 'Вече е зададена 100% собственост за този имот.');
        return;
    }

    maxOwnership = max.sub(sum);

    contragent_type = 'owner';
    //init add owners grid
    initAddOwnersGrid(contractData.id, plotData[0].gid);
    //init representatives
    initOwnersRepsGrid();

    jQuery('#win-plot-owner-add').window('open');
}

function getTotalOwnersShares() {
    var gridData = jQuery('#annex-owners-tables').treegrid('getData');
    var sumFr = new Fraction();

    //calculate total ownership
    if (gridData[0]) {
        for (var i = 0; i < gridData.length; i++) {
            var f;
            if(gridData[i].numerator && gridData[i].denominator) {
                f = new Fraction(gridData[i].numerator / gridData[i].denominator);
            }
            else {
                f = new Fraction(gridData[i].percent / 100);
            }

            sumFr = sumFr.add(f);
        }
    }

    return sumFr;
}

function editAnnexOwnerPercentage() {
    if (!hasPlotRightsRW) {
        messagerPlotsWriteRights();
        return false;
    }
    var getSelected = jQuery('#annex-owners-tables').treegrid('getSelected'),
        gridData = jQuery('#annex-owners-tables').treegrid('getData'),
        sum = new Fraction(),
        max = new Fraction(1),
        contractData = jQuery('#annexes-tree').tree('getSelected'),
        i,
        f;
    //No Rights to operate with "Договори за собственост"
    if (contractData.attributes.c_type == CONTRACT_TYPE_OWN && !hasContractsOwnWriteRights) {
        messagerContractsOwnWriteRights();
        return false;
    }
    if (contractData.attributes.from_sublease > 0) {
        messageContractIsFromSublease(contractData);
        return false;
    }

    if (!getSelected) {
        jQuery.messager.alert('Грешка', 'Моля изберете запис, който искате да редактирате.');
        return;
    }


    if (isHeritorSelected) {
        var parent = jQuery('#annex-owners-tables').treegrid('getParent', getSelected.fakeid),
            childrens = jQuery('#annex-owners-tables').treegrid('getChildren', parent.fakeid),
            directChildrens = [];
        for (i = 0; i < childrens.length; i++) {
            if(childrens[i].level == parent.level + 1) {
                directChildrens.push(childrens[i]);
            }
        }
        gridData = directChildrens;

        if (parent.numerator && parent.denominator) {
            max = new Fraction(parent.numerator / parent.denominator);
        } else {
            max = new Fraction(parent.percent / 100);
        }
    }
    for (i = 0; i < gridData.length; i++) {
        if (getSelected.fakeid != gridData[i].fakeid) {
            if (gridData[i].numerator && gridData[i].denominator) {
                f = new Fraction(gridData[i].numerator / gridData[i].denominator);
            } else {
                f = new Fraction(gridData[i].percent / 100);
            }

            sum = sum.add(f);
        }
    }

    maxOwnership = max.sub(sum);

    contragent_type = 'owner';

    initEditOwnageDataFields();
    jQuery('#win-edit-contract-owner-data').window('open');
}

function deleteAnnexPlotOwnerRelation() {
    if (!hasPlotRightsRW) {
        messagerPlotsWriteRights();
        return false;
    }
    var contractData = jQuery('#annexes-tree').tree('getSelected');
    //No Rights to operate with "Договори за собственост"
    if (contractData.attributes.c_type == CONTRACT_TYPE_OWN && !hasContractsOwnWriteRights) {
        messagerContractsOwnWriteRights();
        return false;
    }

    if (contractData.attributes.from_sublease > 0) {
        messageContractIsFromSublease(contractData);
        return false;
    }
    var getSelected = jQuery('#annex-owners-tables').treegrid('getSelected');

    if (getSelected) {

        jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да премахнете този собственик.', function (r) {
            if (r) {
                var plotOwnerRelId = getSelected.id,
                    ownerId = getSelected.owner_id;


                TF.Rpc.Contracts.ConctractOwnerData.deletePlotOwner(plotOwnerRelId, ownerId)
                    .done(function () {
                        jQuery('#annex-owners-tables').treegrid('loadRpc');
                    })
                    .fail(function (errorObj) {
                        if (errorObj.is(TF.Rpc.ExceptionsList.NON_EXISTING_CONTRACT_PLOT_RELATION)) {
                            jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.NON_EXISTING_CONTRACT_PLOT_RELATION.message, 'warning');
                        } else if (errorObj.is(TF.Rpc.ExceptionsList.INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS)) {
                            jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS.message, 'warning');
                        }
                    });
            }
        });
    } else {
        jQuery.messager.alert('Грешка', 'Моля изберете собственик, който да бъде премахнат.');
    }
}

function copyAnnexPlotOwnerRelation() {
    if (!hasPlotRightsRW) {
        messagerPlotsWriteRights();
        return false;
    }
    var plotData = jQuery('#annex-plots-tables').datagrid('getData'),
        ownerData = jQuery('#annex-owners-tables').treegrid('getData'),

        selectedContract = jQuery('#annexes-tree').tree('getSelected'),
        selectedPlot = jQuery('#annex-plots-tables').datagrid('getSelected'),

        contractData = jQuery('#annexes-tree').tree('getSelected');
    //No Rights to operate with "Договори за собственост"
    if (contractData.attributes.c_type == CONTRACT_TYPE_OWN && !hasContractsOwnWriteRights) {
        messagerContractsOwnWriteRights();
        return false;
    }

    if (contractData.attributes.from_sublease > 0) {
        messageContractIsFromSublease(contractData);
        return false;
    }

    if (ownerData.length > 0) {
        if (plotData.rows.length != 1) {


            jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да добавите тези собственици към всички имоти за този договор?', function (r) {
                if (r) {
                    TF.Rpc.Contracts.ConctractOwnerData.multiplyOwners(selectedContract.id, selectedPlot.gid)
                        .done(function (data) {
                            displayMultiplyOwnerResult(data);
                        })
                        .fail(function (errorObj) {
                            if (errorObj.is(TF.Rpc.ExceptionsList.NON_EXISTING_CONTRACT_PLOT_RELATION)) {
                                jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.NON_EXISTING_CONTRACT_PLOT_RELATION.message, 'warning');
                            } else if (errorObj.is(TF.Rpc.ExceptionsList.INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS)) {
                                jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS.message, 'warning');
                            }
                        });
                }
            });
        } else {
            jQuery.messager.alert('Грешка', 'Към договора има добавен само един имот. Моля добавете други имоти, за които искате да бъдат копирани данните за собсвениците.');
        }
    } else {
        jQuery.messager.alert('Грешка', 'Няма въведени собственици. Моля въведете собственици, които да бъдат копирани.');
    }
}

function displayAnnexOwnerInformation() {
    var getSelected = jQuery('#annex-owners-tables').treegrid('getSelected');
    if (getSelected) {
        window.open("index.php?page=Owners.Home&owner_id=" + getSelected.owner_id, '_blank');
    } else {
        jQuery.messager.alert('Грешка', 'Моля изберете собственик, за който да бъде показана информация.');
    }
}

function editAnnexOwnerData() {
    var getSelected = jQuery('#annex-owners-tables').treegrid('getSelected');
    if (getSelected) {
        TF.Rpc.Owners.OwnersTree.markForEdit(getSelected.owner_id)
            .done(function (data) {
                ownerType = data.owner_type;
                isEditing = true;
                jQuery('#win-add-new-owner').window('open');
                setOwnerFieldsDataForEdit(data);
                setAddEditFieldsValidators();
                displayTablesOnCallbackComplete();
            })
            .fail(function (errorObj) {
                jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
            })
    } else {
        jQuery.messager.alert('Грешка', 'Моля изберете собственик, който да бъде редактиран.');
    }
}

function displayTablesOnCallbackComplete(){
    if(ownerType == 0) {
        displayTables('legal');
        jQuery('#is_legal > input').prop('checked', true);
        jQuery('#is_physical > input').prop('checked', false);
    }else{
        displayTables('physical');
        jQuery('#is_legal > input').prop('checked', false);
        jQuery('#is_physical > input').prop('checked', true);
    }
}

function setAddEditFieldsValidators() {
    numLengthbox();
    jQuery('#name > input').validatebox({
        required: true,
        missingMessage: 'Моля въведете име на собственик.'
    });
    jQuery('#surname > input').validatebox({
        required: true,
        missingMessage: 'Моля въведете презиме на собственик.'
    });
    jQuery('#lastname > input').validatebox({
        required: true,
        missingMessage: 'Моля въведете фамилия на собственик.'
    });

    let egnBox = new EgnValidateBox('#egn > input');

    jQuery('#company_name > input').validatebox({
        required: true,
        missingMessage: 'Моля въведете име на фирма.'
    });
    jQuery('#eik > input').numberbox({
        required: true,
        validType: 'setLength[9]',
        missingMessage: 'Моля въведете ЕИК номер.',
        parser: function(s){
            return s;
        }
    });
    jQuery('#mobile > input').validatebox({
        missingMessage: 'Моля въведете мобилен телефон.'
    });
    jQuery('#email > input').validatebox({
        missingMessage: 'Моля въведете имейл на собственика.'
    });

    jQuery('#is_foreigner').on('change', function () {
        if(jQuery('#is_foreigner').is(':checked')) {
            egnBox.disableValidation();
        } else {
            egnBox.enableValidation();
        }
    });

    jQuery('#IsDead').on('change', function () {
        if(jQuery('#is_foreigner').is(':checked') && jQuery('#IsDead').is(':checked')) {
            egnBox.disableValidation();
        } else {
            egnBox.enableValidation();
        }
    });


    jQuery('#is_foreigner').trigger('change');
    jQuery('#IsDead').trigger('change');

}

function setOwnerFieldsDataForEdit(data) {
    jQuery('#name > input').val(data['name']);
    jQuery('#surname > input').val(data['surname']);
    jQuery('#lastname > input').val(data['lastname']);
    new CardIdValidateBox('#lk_nomer > input').setValue(data['lk_nomer']);
    jQuery('#lk_izdavane > input').val(data['lk_izdavane']);
    jQuery('#company_name > input').val(data['company_name']);
    jQuery('#eik > input').numberbox('setValue', data['eik']);
    jQuery('#mol > input').val(data['mol']);
    jQuery('#company_address > textarea').val(data['company_address']);
    jQuery('#phone > input').val(data['phone']);
    jQuery('#fax > input').val(data['fax']);
    jQuery('#mobile > input').val(data['mobile']);
    jQuery('#email > input').val(data['email']);
    jQuery('#iban > input').val(data['iban']);
    jQuery('#address > textarea').val(data['address']);
    jQuery('#remark > textarea').val(data['remark']);
    new EgnValidateBox('#egn > input').setValue(data['egn']);
    if (data['owner_type'] == 0) {
        jQuery('#is_legal > input').prop('checked', true);
        jQuery('#is_physical > input').prop('checked', false);
    }
    else {
        jQuery('#is_legal > input').prop('checked', false);
        jQuery('#is_physical > input').prop('checked', true);
    }

    data['is_dead'] ? jQuery('#is-dead > input').prop('checked', true) : jQuery('#is-dead > input').prop('checked', false);
    data['is_foreigner'] ? jQuery('#is-foreigner > input').prop('checked', true) : jQuery('#is-foreigner > input').prop('checked', false);
    data['is_dead'] ? jQuery('#prepiska > input').val(data['prepiska']) : jQuery('#prepiska > input').val('');
    jQuery('#rent-place > input').combobox('setValue', data['rent_place']);
}

jQuery(function() {
    jQuery('#btnsaveowner').bind('click', function() {
        if (validateNewOwnerSubmitInfo())
        {
            var obj = getAddOwnerInputFieldsData();
            if (!isEditing) {

                TF.Rpc.Contracts.ContractsOwnersGrid.addNewOwner(obj)
                    .done(function (response)
                    {
                        if(lastAddedParent == 0) {
                            lastAddedParent = response;
                        }
                        lastAddedOwner = response;

                        if (jQuery('#owners-add-tables').data().hasOwnProperty('datagrid')) {
                            jQuery('#owners-add-tables').datagrid('reload');
                        };

                        addingOwnerHeritor = false;
                        isEditing = false;

                        jQuery('#win-add-new-owner').window('close');

                        if (obj.is_dead) {
                            if(lastAddedParent != lastAddedOwner && lastAddedOwner != 0 && lastAddedParent != 0) {
                                TF.Rpc.Owners.OwnersHeritorsTree.addOwnerHeritor(lastAddedOwner, lastAddedParent)
                                    .done(function (data) {

                                    }).
                                fail(function (errorObj) {
                                    jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
                                });
                            }
                            lastAddedParent = response;
                        } else {
                            if(lastAddedParent != lastAddedOwner && lastAddedOwner != 0 && lastAddedParent != 0) {
                                TF.Rpc.Owners.OwnersHeritorsTree.addOwnerHeritor(lastAddedOwner, lastAddedParent)
                                    .done(function (data) {
                                        jQuery('#win-choose-heritor').window('close');
                                        jQuery('#owners-add-tables').datagrid('loadRpc');
                                        jQuery('#annex-owners-tables').treegrid('loadRpc');
                                    }).
                                fail(function (errorObj) {
                                    jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
                                });
                            }
                        }
                        if (!obj.is_dead) {
                            lastAddedOwner = 0;
                            lastAddedParent = 0;
                        } else {
                            addOwnerHeritorHelper(lastAddedParent);
                        }
                    })
                    .fail(function (data) {
                        if (data.getCode() == -33310) {
                            jQuery.messager.alert('Грешка', data.getMessage(), 'warning');
                            return false;
                        } else {
                            RpcErrorHandler.show(data);
                            return false;
                        }
                    });
            } else {
                var selectedOwner = jQuery('#annex-owners-tables').treegrid('getSelected');
                if(selectedOwner && obj.is_legal === true) {
                    TF.Rpc.Owners.OwnersTree.editLegalOwner(obj,selectedOwner.owner_id)
                        .done(function (data) {
                            jQuery('#win-plot-owner-add').window('close');
                            isEditing = false;
                        })
                        .fail(function (data) {
                            if (data.getCode() == -33310) {
                                jQuery.messager.alert('Грешка', data.getMessage(), 'warning');
                                return false;
                            } else {
                                RpcErrorHandler.show(data);
                                return false;
                            }
                        });
                }else if (selectedOwner && obj.is_legal === false){
                    TF.Rpc.Owners.OwnersTree.editOwner(obj,selectedOwner.owner_id)
                        .done(function (response) {

                            if(lastAddedParent == 0) {
                                lastAddedParent = response;
                            }
                            lastAddedOwner = response;

                            if (jQuery('#owners-add-tables').data().hasOwnProperty('datagrid')) {
                                jQuery('#owners-add-tables').datagrid('reload');
                            };

                            addingOwnerHeritor = false;
                            isEditing = false;

                            if (obj.is_dead) {
                                if(lastAddedParent != lastAddedOwner && lastAddedOwner != 0 && lastAddedParent != 0) {
                                    TF.Rpc.Owners.OwnersHeritorsTree.addOwnerHeritor(lastAddedOwner, lastAddedParent)
                                        .done(function (data) {

                                        }).
                                    fail(function (errorObj) {
                                        jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
                                    });
                                }
                                lastAddedParent = response;
                            } else {
                                if(lastAddedParent != lastAddedOwner && lastAddedOwner != 0 && lastAddedParent != 0) {
                                    TF.Rpc.Owners.OwnersHeritorsTree.addOwnerHeritor(lastAddedOwner, lastAddedParent)
                                        .done(function (data) {
                                            jQuery('#win-choose-heritor').window('close');
                                            jQuery('#owners-add-tables').datagrid('loadRpc');
                                        }).
                                    fail(function (errorObj) {
                                        jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
                                    });
                                }
                            }
                            if (!obj.is_dead) {
                                lastAddedOwner = 0;
                                lastAddedParent = 0;
                            } else {
                                addOwnerHeritorHelper(lastAddedParent);
                            }
                        })
                        .fail(function (data) {
                            jQuery.messager.alert('Грешка', data.getMessage(), 'warning');
                        });
                }

                jQuery('#win-add-new-owner').window('close');
                jQuery('#annex-owners-tables').treegrid('loadRpc');
            }
        }
    });

    jQuery('#btn-add-new-owner-heritor').bind('click', function () {
        addingNewHeritor = true;
        clearAddOwnerInputDataFields();
        onAddownerPanelOpen();
        jQuery('#win-add-new-owner').window('open');
    });
});


function editAnnexOwnerRepresentative() {
    var getSelected = jQuery('#annex-owners-tables').datagrid('getSelected');

    if (!getSelected) {
        jQuery.messager.alert('Грешка', 'Моля изберете собственик.', 'warning');
        return false;
    }
    var rep_id = getSelected.rep_id;
    var self_rep = getSelected.self_rep;

    jQuery('#win-edit-owner-representative').window('open');

    if(self_rep) {
        jQuery('#self-representing').prop('checked', true);
    } else {
        jQuery('#reprsented-by-representative').prop('checked', true);
    }

    initAnnexOwnerRepresentativesGrid(rep_id);
}

function initAnnexOwnerRepresentativesGrid(rep_id) {
    jQuery("#choose-representatives-grid").datagrid({
        title: 'Представители',
        iconCls: 'icon-users',
        rownumbers: true,
        singleSelect: true,
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        fit: true,
        fitColumns: true,
        showFooter: false,
        pagination: true,
        idField: 'id',
        sortName: 'id',
        sortOrder: 'desc',
        url: 'index.php?owners-rpc=representatives-tree',
        rpcParams:[{
            anti_rep_id: rep_id,
            forGrid: true
        }],
        onBeforeLoad: function () {
            jQuery("#choose-representatives-grid").datagrid('clearSelections');
        },
        columns: [[
            {
                field: 'rep_owner_names',
                title: '<b>Име</b>',
                sortable: false,
                width: 300,
            }, {
                field: 'egn',
                title: '<b>ЕГН</b>',
                sortable: false,
                width: 120,
            }, {
                field: 'address',
                title: '<b>Адрес</b>',
                sortable: false,
                width: 200,
            },
        ]],
        toolbar: [{
            id: 'btnadd',
            text: 'Информация за представител',
            iconCls: 'icon-info',
            handler: function() {
                var getSelected = jQuery("#choose-representatives-grid").datagrid('getSelected');
                window.open("index.php?page=Owners.Home&rep_id=" + getSelected.id, '_blank');
            }
        }],
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

jQuery(function () {

    jQuery('#btn-filter-representatives').bind('click', function () {
        var obj = {
            anti_rep_id: jQuery('#annex-owners-tables').datagrid('getSelected').rep_id,
            forGrid: true,
            rep_names: jQuery('#search-rep-by-name').val(),
            egn: jQuery('#search-rep-by-egn').val()
        }
        jQuery("#choose-representatives-grid").datagrid({
            rpcParams: [obj]
        });
    });

    jQuery('#btn-clear-filter-representatives').bind('click', function () {
        jQuery('#search-rep-by-name').val('');
        jQuery('#search-rep-by-egn').val('');
        jQuery('#btn-filter-representatives').trigger('click');
    });

    jQuery('#btn-save-rep-changes').bind('click', function () {
        var rel_id = jQuery('#annex-owners-tables').datagrid('getSelected').id;
        var owner_id= jQuery('#annex-owners-tables').datagrid('getSelected').owner_id;
        var contract_id = jQuery('#annexes-tree').tree('getSelected').id;
        var new_rep = null;

        if(!jQuery('#self-representing').is(':checked')) {
            new_rep = jQuery("#choose-representatives-grid").datagrid('getSelected').id;
        }

        TF.Rpc.Owners.OwnersRepresentedGrid.changeOwnerRepresentative({
            rel_id: rel_id,
            rep_id: new_rep,
            contract_id: contract_id,
            owner_id: owner_id
        })
        .done(function () {
            jQuery('#win-edit-owner-representative').window('close');
            jQuery('#annex-owners-tables').datagrid('loadRpc');
        })
        .fail(function (errorObj) {
            jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
        });

    });
});
