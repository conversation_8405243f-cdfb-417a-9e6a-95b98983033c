/*jslint browser: true */
/*global jQuery, EasyUIRPCLoaders.EasyUIGridCustomLoader, TF, OpenLayers, Settings, google, vectors, wmsServer, mapPath, groupID, onHidePanelMultiSelect, onComboMultiSelect,messagerThematicMapsRights*/
//init global variables
var map,
    controlNavigation,
    controlScaleLine,
    controlZoomIn,
    controlZoomOut,
    newThematicMapData,
    newThematicMapColorArray,
    bingApiKey = "AiWEso3-IjWyX1aZMuep9Sjl62D6FUMqv8qQGpV-kgbW0qWk61to4nrqHh-2D5HL",
    renderer = OpenLayers.Util.getParameters(window.location.href).renderer,
    selectedThematicMapID,
    lastCreatedFilterField,
    isThematicMapDeleted;

OpenLayers.ProxyHost = Settings.OPEN_LAYERS_PROXY;

if (renderer) {
    OpenLayers.Layer.Vector.prototype.renderers = [renderer];
}

function initMapPad(specific_map_type) {
    'use strict';
    var chosenMapType,
        options = {
            controls: [],
            projection: new OpenLayers.Projection("EPSG:900913"),
            displayProjection: new OpenLayers.Projection("EPSG:32635"),
            restrictedExtent: [2415933.172718, 5006431.061239, 3257351.979964, 5523756.868601],
            maxResolution: 612
        },
        layerMapPad;

    //on init map type will not be specified
    if (specific_map_type === undefined) {
        chosenMapType = undefined;
        //init map
        map = new OpenLayers.Map('map', options);
    } else {
        //when map type is changed specific_map_type will have the value of map type
        chosenMapType = parseInt(specific_map_type, 10);
    }

    switch (chosenMapType) {
    case 2:
        layerMapPad = new OpenLayers.Layer.Bing({
            key: bingApiKey,
            type: "Aerial",
            name: "MapPad"
        });
        break;
    case 3:
        layerMapPad = new OpenLayers.Layer.Bing({
            key: bingApiKey,
            type: "Road",
            name: "MapPad"
        });
        break;
    case 4:
        layerMapPad = new OpenLayers.Layer.Google("MapPad", {
            type: google.maps.MapTypeId.TERRAIN
        });
        break;
    case 5:
        layerMapPad = new OpenLayers.Layer.Google("MapPad", {
            numZoomLevels: 20
        });
        break;
    case 6:
        layerMapPad = new OpenLayers.Layer.Google("MapPad", {
            type: google.maps.MapTypeId.HYBRID,
            numZoomLevels: 20
        });
        break;
    case 7:
        layerMapPad = new OpenLayers.Layer.Google("MapPad", {
            type: google.maps.MapTypeId.SATELLITE,
            numZoomLevels: 22
        });
        break;
    case 8:
        layerMapPad = new OpenLayers.Layer.OSM();
        break;
    case 9:
        layerMapPad = new OpenLayers.Layer.WMS('MapPad',
            imagesWMSServer + "?map=" + '/var/www/satellite_processor/maps/geo_scan.map',
            {
                layers: 'geo_scan',
            }, {
                numZoomLevels: 18
            });
        break;
    default: // default is bing aerial with labels
        layerMapPad = new OpenLayers.Layer.Bing({
            key: bingApiKey,
            type: "AerialWithLabels",
            name: "MapPad"
        });
        break;
    }

    //specific map type will be given only on reload
    //on first load(init) specific map type should be undefined
    if (specific_map_type === undefined) {
        map.addLayer(layerMapPad);
    } else {
        map.addLayer(layerMapPad);
        map.setLayerIndex(map.layers[map.layers.length - 1], 0);
        map.removeLayer(map.layers[1]);
        map.layers[0].redraw(true);
    }
}

function zoomToBulgaria() {
    'use strict';
    map.zoomToExtent(new OpenLayers.Bounds.fromString("125190.6162, 4573142.7188, 631370.3273, 4887149.5823").transform(
        new OpenLayers.Projection("EPSG:32635"),
        map.getProjectionObject()
    ));
}

function zoomToExtent(extent) {
    'use strict';
    if (extent) {
        if (extent.indexOf(',') === -1) {
            var find = ' ',
                re = new RegExp(find, 'g');

            extent = extent.replace(re, ', ');
        }
        map.zoomToExtent(new OpenLayers.Bounds.fromString(extent).transform(
            new OpenLayers.Projection("EPSG:32635"),
            map.getProjectionObject()
        ));
    } else {
        zoomToBulgaria();
    }
}

function initPositionDisplay() {
    jQuery('#select-coord-system-for-map').combobox({
        editable: false,
        data: [{
            "value": '900913',
            "text": "WGS 84",
            "selected": true
        }, {
            "value": '32635',
            "text": "UTM",
        }],
        valueField: 'value',
        textField: 'text',
        multiple: false,
        onChange: function (value) {
            selectedSystem = value;
        }
    });
    map.events.register("mousemove", map, function(e)
    {
        var position = map.getLonLatFromViewPortPx(e.xy);
        if ('900913' == selectedSystem) {
            OpenLayers.Util.getElement("coords").innerHTML = 'x: ' + position.lat + ', y: ' + position.lon;
        } else {
            newPosition = position.clone();
            newPosition.transform('EPSG:900913', 'EPSG:4326');
            OpenLayers.Util.getElement("coords").innerHTML = 'x: ' + newPosition.lat.toPrecision(15) + ', y: ' + newPosition.lon.toPrecision(15);
        }
    });
}

function initScaleDisplay() {
    'use strict';
    var currentScale,
        scale,
        field = jQuery("#scale-denominator input");
    map.events.register("zoomend", map, function () {
        currentScale = Math.round(map.getScale());
        field.val(currentScale);
    });

    jQuery('#tool-set-scale').bind('click', function()
    {
        scale = field.val();
        map.zoomToScale(scale);
    });

    field.on('keyup', function (e) {
        if (e.which == 13) {
            scale = field.val();
            map.zoomToScale(scale);
        };
    });
}

function removeAllLayers() {
    'use strict';
    var mLayer = map.layers,
        remLayers = [],
        i = 0;

    for (i = 0; i < mLayer.length; i = i + 1) {
        if (mLayer[i].name !== "MapPad" && mLayer[i].name !== "Vector Layer") {
            remLayers.push(mLayer[i]);
        }
    }

    for (i = 0; i < remLayers.length; i = i + 1) {
        map.removeLayer(remLayers[i]);
    }
}

function initDefaultMapControls() {
    'use strict';
    controlNavigation = new OpenLayers.Control.Navigation({
        name: "navigation",
        handleRightClicks: true,
        documentDrag: false,
    });
    map.addControl(controlNavigation);

    controlScaleLine = new OpenLayers.Control.ScaleLine({
        name: "scaleline",
        bottomInUnits: 'km'
    });
    map.addControl(controlScaleLine);

    controlZoomIn = new OpenLayers.Control.ZoomBox({
        name: "zoomin",
        title: "Zoom in box",
        out: false
    });
    map.addControl(controlZoomIn);

    controlZoomOut = new OpenLayers.Control.ZoomBox({
        name: "zoomout",
        title: "Zoom out box",
        out: true
    });
    map.addControl(controlZoomOut);
}

function pushVectorLayerToTopPosition() {
    'use strict';

    var layers = map.layers,
        layers_count = layers.length,
        i = 0;

    if (map.layers[layers_count - 1].name !== "Очертания") {
        for (i = 0; i < map.layers.length; i = i + 1) {
            if (layers[i].name === "Очертания") {
                map.setLayerIndex(map.layers[i], layers.length - 1);
            }
        }
    }
}

function loadThematicLayer() {
    'use strict';

    removeAllLayers();
    var layer_type = 'thematic_map',
        layerData = new OpenLayers.Layer.WMS(
            layer_type,
            wmsServer + "?map=" + mapPath + groupID + '.map',
            {
                layers: layer_type,
                format: 'image/png',
                transparent: "true"
            }
        );
    map.addLayer(layerData);
    layerData.redraw(true);

    pushVectorLayerToTopPosition();
}

function initThematicMapLegend(data) {
    'use strict';
    var plots_total_preliminary = 0,
        area_total_preliminary = 0,
        plots_total = 0,
        area_total = 0;
    if (data.total > 0) {
        jQuery('#thematic-maps-accordion').accordion('panels')[2].panel('expand', true);
        jQuery('#open-legend-button').prop('checked', true);
    }

    data.rows.forEach(function (row) {
        plots_total_preliminary += parseInt(row.plot_count, 10);
        area_total_preliminary += parseFloat(row.area);
    });

    data.rows.forEach(function (row) {
        if ((parseInt(row.plot_count, 10) / plots_total_preliminary) > 0.01) {
            plots_total += parseInt(row.plot_count, 10);
        }
        if ((parseFloat(row.area) / area_total_preliminary) > 0.01) {
            area_total += parseFloat(row.area);
        }
    });
    jQuery('#thematic-map-legend-datagrid').datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        iconCls: 'icon-edit-geometry',
        pageSize: 50,
        fitColumns: true,
        border: false,
        data: data,
        columns: [
            [
                {
                    field: 'color_field',
                    title: '<b>Цвят</b>',
                    sortable: false,
                    width: 50,
                    align: 'center',
                    styler: function (value) {
                        return 'background-color:' + value + '; color: ' + value;
                    },
                    formatter: function () {
                        return '';
                    }
                }, {
                    field: 'value_field',
                    title: '<b>Стойност</b>',
                    sortable: false,
                    width: 220
                }, {
                    field: 'plot_count',
                    title: '<b>Бр. имоти</b>',
                    sortable: false,
                    width: 120,
                    align: 'center',
                    formatter: function (value) {
                        var percent = (value / plots_total * 100).toFixed(1),
                            html;
                        if (percent > 1) {
                            html = '<div style="width:100%; display:inline-block" title="' + percent + '%">' + value + '</div>';
                        } else {
                            html = '<div style="width:100%; display:inline-block" title="< 1%">' + value + '</div>';
                        }
                        return html;
                    }
                }, {
                    field: 'area',
                    title: '<b>Площ</b>',
                    sortable: false,
                    width: 130,
                    align: 'center',
                    formatter: function (value) {
                        var percent = (value / area_total * 100).toFixed(1),
                            html;
                        if (percent > 1) {
                            html = '<div style="width:100%; display:inline-block" title="' + percent + '%">' + value + '</div>';
                        } else {
                            html = '<div style="width:100%; display:inline-block" title="< 1%">' + value + '</div>';
                        }
                        return html;
                    }
                },

            ]
        ],
        onBeforeSelect: function () {
            return false;
        },
        pagination: false,
        rownumbers: false,
    });
}

function drawPieChart(colorSet, criteria) {
    'use strict';

    var colors = [],
        data,
        criteriaType = parseInt(criteria, 10) === 1 ? ' - брой имоти' : ' - обща площ',
        criteriaText = parseInt(criteria, 10) === 1 ? ' Брой' : ' Площ',
        options = {
            title: 'Критерий: ' + newThematicMapData.criteriaText + criteriaType,
            is3D: false,
            legend: 'none',
            width: 360,
            height: 253,
            pieResidueSliceLabel: 'Други',
            pieSliceText: 'value',
            tooltip: {
                showColorCode: true
            },
            pieSliceTextStyle: {
                color: '#333333'
            },
            chartArea: {
                left: 20,
                top: 30,
                width: '90%',
                height: '80%'
            },
            titleTextStyle: {
                color: 'black',
                fontSize: 13
            },
            sliceVisibilityThreshold: 1 / 100
        },

        chart = new google.visualization.PieChart(document.getElementById('thematic-map-chart-datagrid')),
        slices = [],
        slicesObj = {},
        i = 0;

    colors = [['Критерий', criteriaText]];

    colorSet.rows.forEach(function (el) {
        var tmpArr = [];
        tmpArr.push(el.value_field);
        if (parseInt(criteria, 10) === 1) {
            tmpArr.push(parseInt(el.plot_count, 10));
        } else {
            tmpArr.push(parseFloat(el.area));
        }
        colors.push(tmpArr);
        slices.push({color: el.color_field});
    });

    for (i = 0; i < slices.length; i = i + 1) {
        slicesObj[i] = slices[i];
    }

    options.slices = slicesObj;
    data = google.visualization.arrayToDataTable(colors);

    chart.draw(data, options);

    if (slices.length > 0) {
        jQuery('#thematic-maps-accordion').accordion('panels')[1].panel('expand', true);
        jQuery('#open-diagram-button').prop('checked', true);
    }
}

function drawColumnChart(colorSet, criteria) {
    'use strict';

    var colors = [],
        data,
        criteriaType = parseInt(criteria, 10) === 1 ? ' - брой имоти' : ' - обща площ',
        criteriaText = parseInt(criteria, 10) === 1 ? ' Брой' : ' Площ',
        options = {
            title: 'Критерий: ' + newThematicMapData.criteriaText + criteriaType,
            legend: 'none',
            width: 360,
            height: 253,
            tooltip: {
                showColorCode: true
            },
            chartArea: {
                left: 45,
                top: 30,
                width: '100%',
                height: '75%'
            },
            titleTextStyle: {
                color: 'black',
                fontSize: 13
            },
            hAxis: {
                minTextSpacing: 20,
                slantedText: true,
                slantedTextAngle: 45,
                allowContainerBoundaryTextCufoff: true,
            },
            vAxis: {
                minTextSpacing: 20,
            }
        },
        view,
        chart = new google.visualization.ColumnChart(document.getElementById('thematic-map-chart-datagrid'));

    colors = [['Критерий', criteriaText, {role: 'style'}]];

    colorSet.rows.forEach(function (el) {
        var tmpArr = [];
        tmpArr.push(el.value_field);
        if (parseInt(criteria, 10) === 1) {
            tmpArr.push(parseInt(el.plot_count, 10));
        } else {
            tmpArr.push(parseFloat(el.area));
        }
        tmpArr.push(el.color_field);
        colors.push(tmpArr);
    });
    data = google.visualization.arrayToDataTable(colors);
    view = new google.visualization.DataView(data);
    chart.draw(view, options);
    if (colors.length > 0) {
        jQuery('#thematic-maps-accordion').accordion('panels')[1].panel('expand', true);
        jQuery('#open-diagram-button').prop('checked', true);
    }
}

function populateSettingsPanel(clear) {
    'use strict';

    var html = '',
        text;
    jQuery('#thematic-maps-currently-applied-filters').html('');

    if (clear) {
        jQuery('#settings-panel-container').html('');
        jQuery('#thematic-map-name').html('');
        jQuery('#thematic-map-criteria').html('');
        return true;
    }

    newThematicMapData.filters.forEach(function (el) {
        text = el.filterValueText !== undefined ? el.filterValueText : el.filterValue;
        text = text.replace(/,/g, '<br>');
        html += '<span class="filter-bubble non-closable" style="max-width: 225px; word-wrap: break-word;" data-column="' + el.column + '"><b>' + el.filterText + ':</b> ' + text + '</span>';
    });

    jQuery('#settings-panel-container').html(html);
    jQuery('#thematic-map-name').html(newThematicMapData.mapName);
    jQuery('#thematic-map-criteria').html(newThematicMapData.criteriaText);

    if (parseInt(newThematicMapData.chart_type, 10) === 2) {
        jQuery('#bar-diagram-type').prop('checked', true);
    } else {
        jQuery('#circular-diagram-type').prop('checked', true);
    }

    if (parseInt(newThematicMapData.chart_criteria, 10) === 2) {
        jQuery('#total-area-diagram-criteria').prop('checked', true);
    } else {
        jQuery('#plot-count-diagram-criteria').prop('checked', true);
    }
}

function bindColorPickers() {
    'use strict';
    var datagrid = jQuery('#new-thematic-maps-results-grid');

    jQuery(".input-field-for-color").spectrum({
        preferredFormat: 'hex',
        showInput: false,
        hide: function (tinycolor) {
            var index = jQuery(this).data().index,
                rows = datagrid.datagrid('getRows'),
                currentRow = rows[index];

            currentRow.color_field = tinycolor.toHexString();
            datagrid.datagrid('updateRow', {index: index, row: currentRow});
            bindColorPickers();
        }
    });
    datagrid.datagrid('fixRowHeight');
}

function updateWithNewPaletteColors(colors) {
    'use strict';
    var datagrid = jQuery('#new-thematic-maps-results-grid');
    try{
        var rows = datagrid.datagrid('getRows');
    }catch(err){
        return false;
    }
    rows.forEach(function (row, index) {
        row.color_field = colors[index % colors.length].hex;
        jQuery(".input-field-for-color").eq(index).spectrum("set", row.color_field);
        datagrid.datagrid('updateRow', {index: index, row: row});
    });
    bindColorPickers();
}

function initColorPalettesCombobox() {
    'use strict';

    jQuery('#new-thematic-maps-color-palettes').combobox({
        url: 'index.php?thematic-maps-rpc=thematic-maps-color-palettes',
        rpcParams: [],
        valueField: 'selector',
        textField: 'name',
        showItemIcon: true,
        onSelect: function (record) {
            updateWithNewPaletteColors(record.colors);
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

//Първа стъпка при генериране на нова тематична карта
function generateNewLayer() {
    'use strict';

    newThematicMapData = {};

    jQuery('#new-thematic-maps-name').val('');
    jQuery('#win-new-thematic-maps').window('open');

    jQuery('#new-thematic-maps-name').validatebox({
        required: true,
        missingMessage: 'Моля въведете име на на тематичната карта.',
        tipPosition: 'top'
    });
}

//Валидира задължителните полета при запис на нова тематична карта
function isStep1Valid() {
    'use strict';

    var mapName = jQuery('#new-thematic-maps-name').val(),
        mapLayer = jQuery('#new-thematic-maps-main-layer').combobox('getValue');

    if (mapName !== '' && mapLayer !== '') {
        newThematicMapData.mapName = mapName;
        newThematicMapData.mapLayer = mapLayer;
        newThematicMapData.filters = [];
        return true;
    }

    return false;
}
function recreateFilterValueField() {
    'use strict';
    var component = jQuery('#main-layer-columns-value');
    if (component.data().hasOwnProperty('combobox') || component.data().hasOwnProperty('datebox')) {
        component.combobox('destroy');
        jQuery('#equals-sign-span').after('<input type="text" id="main-layer-columns-value" style="width: 90px" /> ');
    } else if(component.hasClass("tt-input")) {
        component.typeahead('destroy');
        component.val('');
    }else {
        component.val('');
    }
}

function initFilterValueField(field) {
    'use strict';
    var component = jQuery('#main-layer-columns-value');

    if (field.type === 'boolean') {
        if (component.data().hasOwnProperty('combobox') || component.data().hasOwnProperty('datebox')  || component.hasClass( "tt-input" )) {
            recreateFilterValueField();
        }
        jQuery('#main-layer-columns-value').combobox({
            data: [
                {text: 'Да', value: 'true', selected: true},
                {text: 'Не', value: 'false'}
            ],
            editable: false
        });
    } else if (field.type === 'datebox') {
        if (component.data().hasOwnProperty('combobox') || component.hasClass( "tt-input" )) {
            recreateFilterValueField();
            component = jQuery('#main-layer-columns-value');
        }
        component.datebox();
    } else if(field.id == 'owner_names' || field.id == 'company_name') {
        recreateFilterValueField();
        component = jQuery('#main-layer-columns-value');
        var  completeParam;
        var componentTarget;
        switch (field.id) {
            case 'owner_names':
                completeParam = 'owners';
                componentTarget = jQuery('#search-owner-name-typeahead-target');
                break;
            case 'company_name':
                completeParam = 'companies';
                componentTarget = jQuery('#search-company-name-typeahead-target');
                break;
            default:
                completeParam = 'owners';
                componentTarget = jQuery('#search-owner-name-typeahead-target');
            break;
        }
        var completer = new Bloodhound({
            datumTokenizer: Bloodhound.tokenizers.whitespace('value'),
            queryTokenizer: Bloodhound.tokenizers.whitespace,
            remote: {
                url: 'index.php?owners-rpc=owners-list',
                cache: false,
                prepare: function(query, settings) {
                    settings.query = query;
                    return settings;
                },
                transport: function(settings, onSuccess, onError) {
                    var options = {
                        type: 'POST',
                        dataType: 'json',
                        contentType: "application/json; charset=UTF-8",
                        data: JSON.stringify({
                            "method": "read",
                            "params": [completeParam, settings.query],
                            "id": 1,
                            "jsonrpc": "2.0"
                        })
                    };

                    jQuery.ajax('index.php?owners-rpc=owners-list', options)
                        .done(done)
                        .fail(fail)
                        .always(always);

                    function done(data, textStatus, request) {
                        onSuccess(data.result);
                    }

                    function fail(request, textStatus, errorThrown) {
                        onError(errorThrown);
                    }

                    function always() {}
                }
            }
        });

        component.typeahead({
            hint: false,
            highlight: false,
            minLength: 1,
            limit: 10,
            menu: componentTarget
        }, {
            name: 'completer',
            displayKey: 'value',
            source: completer.ttAdapter(),
        });

        component.on('typeahead:open', function(e, datum) {
        componentTarget.width(component.width());
        var offset = component.offset();
        componentTarget.offset({
                    top: offset.top + component.height(),
                    left: offset.left
                });
            componentTarget.css("z-index",9999);
        });

        component.on('typeahead:selected', function (e, datum) {
            component.typeahead('close');
            componentTarget.css("z-index",-1);
        });
        component.on('typeahead:idle', function (e, datum) {
            component.typeahead('close');
            componentTarget.css("z-index",-1);
        });
    }else {
        if (component.data().hasOwnProperty('combobox')) {
            component.combobox('clear');
        } else if (component.data().hasOwnProperty('datebox')) {
            component.datebox('reset');
        } else {
            component.val('');
        }
        if (newThematicMapData.mapLayer == 'layer_kvs') {
            createLayerKVSFields(component, field);
        } else if (newThematicMapData.mapLayer.substr(0,11) == 'layer_isak_') {
            createLayerIsakFields(component, field);
        }
        // jQuery('#main-layer-columns-value').combobox('removeme');
    }
}

function generateNewLayerStep2() {
    'use strict';

    recreateFilterValueField();

    if (jQuery('#main-layer-columns').data().hasOwnProperty('combobox')) {
        jQuery('#main-layer-columns').combobox('reset');
    } else {
        jQuery('#main-layer-columns').val('');
    }

    jQuery('#thematic-maps-currently-applied-filters').html('');

    jQuery('#win-new-thematic-maps-step2').window('open');
    jQuery('#win-new-thematic-maps').window('close');

    jQuery('#main-layer-columns').combobox({
        url: 'index.php?thematic-maps-rpc=main-layer-columns-combobox',
        rpcParams: [
            {
                layer_name: newThematicMapData.mapLayer,
                grouping: false
            }
        ],
        textField: 'name',
        valueField: 'id',
        editable: true,
        onSelect: function (row) {
            jQuery('#thematic-filter-tooltip').tooltip('update', row.description);
            initFilterValueField(row);
        },
        onBeforeLoad: function () {
            TF.Loading.start();
        },
        onLoadSuccess: function () {
            TF.Loading.end();
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    visualizeFilterValues();
}

function generateNewLayerStep3() {
    'use strict';

    jQuery('#win-new-thematic-maps-step3').window('open');
    jQuery('#win-new-thematic-maps-step2').window('close');

    jQuery('#new-thematic-map-color-criteria').combobox({
        url: 'index.php?thematic-maps-rpc=main-layer-columns-combobox',
        rpcParams: [
            {
                layer_name: newThematicMapData.mapLayer,
                grouping: true
            }
        ],
        textField: 'name',
        valueField: 'id',
        required: true,
        editable: true,
        onBeforeLoad: function () {
            TF.Loading.start();
        },
        onLoadSuccess: function () {
            TF.Loading.end();
        },
        missingMessage: 'Моля изберете критерий за оцветяване',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

}

function isStep3Valid() {
    'use strict';

    var criteria = jQuery('#new-thematic-map-color-criteria').combobox('getValue'),
        criteriaText = jQuery('#new-thematic-map-color-criteria').combobox('getText'),
        data = jQuery('#new-thematic-map-color-criteria').combobox('getData'),
        prefix;

    data.forEach(function (field) {
        if (field.id === criteria) {
            prefix = field.prefix;
        }
    });

    if (criteria !== '') {
        newThematicMapData.criteria = criteria;
        newThematicMapData.criteriaText = criteriaText;
        newThematicMapData.criteriaPrefix = prefix;

        return true;
    }

    return false;
}

function generateNewLayerStep4() {
    'use strict';
    var datagrid = jQuery('#new-thematic-maps-results-grid');

    jQuery('#win-new-thematic-maps-step3').window('close');
    jQuery('#win-new-thematic-maps-step4').window('open');

    datagrid.datagrid({
        url: 'index.php?thematic-maps-rpc=thematic-maps-results-grid',
        rpcParams: [newThematicMapData],
        iconCls: 'icon-plot',
        singleSelect: true,
        striped: true,
        autoRowHeight: true,
        pageSize: 50,
        fit: true,
        fitColumns: true,
        showFooter: false,
        sortName: 'area',
        sortOrder: 'desc',
        pagination: false,
        rownumbers: true,
        border: false,
        columns: [
            [{
                field: 'value_field',
                title: '<b>Стойност</b>',
                sortable: true,
                width: 135
            }, {
                field: 'plot_count',
                title: '<b>Бр. имоти</b>',
                sortable: true,
                width: 70,
                align: 'right'
            }, {
                field: 'area',
                title: '<b>Обща площ</b>',
                sortable: true,
                width: 100,
                align: 'right'
            }, {
                field: 'color_field',
                title: '<b>Цвят</b>',
                sortable: false,
                width: 70,
                formatter: function (rowValue, field, index) {
                    return '<input type="text" class="input-field-for-color" data-index="' + index + '" value="' + rowValue + '"/>';
                }
            }]
        ],
        onBeforeLoad: function () {
            jQuery(this).datagrid('clearChecked');
        },
        onLoadSuccess: function () {
            var total = datagrid.datagrid('getData').total;

            if (total > 50) {
                jQuery('#warning-msg-for-many-results').show();
                jQuery('#thematic-results-grid-layout').layout('panel', 'south').panel('move', {top: 460});
                jQuery('#thematic-results-grid-layout').layout('panel', 'south').panel('resize', {height: 110});
                jQuery('#thematic-results-grid-layout').layout('panel', 'center').panel('resize', {height: 330});
            } else {
                if (jQuery('#warning-msg-for-many-results').is(':visible')) {
                    jQuery('#thematic-results-grid-layout').layout('panel', 'south').panel('resize', {height: 40});
                    jQuery('#thematic-results-grid-layout').layout('panel', 'south').panel('move', {top: 515});
                    jQuery('#thematic-results-grid-layout').layout('panel', 'center').panel('resize', {height: 390});
                }
                jQuery('#warning-msg-for-many-results').hide();
            }
            if (total > 0) {
                bindColorPickers();
                datagrid.datagrid('fixRowHeight');
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function generateNewLayerStep5() {
    'use strict';
    jQuery('#win-new-thematic-maps-step4').window('close');
    jQuery('#win-new-thematic-maps-step5').window('open');
}

function generateNewLayerStep6() {
    'use strict';

    var data = {};
    if (jQuery('#new-diagram-bar-chart-radio').is(':checked')) {
        newThematicMapData.chart_type = 2;
    } else {
        newThematicMapData.chart_type = 1;
    }

    if (jQuery('#new-diagram-area-radio').is(':checked')) {
        newThematicMapData.chart_criteria = 2;
    } else {
        newThematicMapData.chart_criteria = 1;
    }

    newThematicMapColorArray = jQuery('#new-thematic-maps-results-grid').datagrid('getData');
    data.colorSet = newThematicMapColorArray;
    data.layerData = newThematicMapData;

    TF.Rpc.ThematicMaps.ThematicMapsResultsGrid.save(data)
        .done(function (response) {
            selectedThematicMapID = response.id;
            jQuery('#win-new-thematic-maps-step5').window('close');
            jQuery('#win-thematic-maps-accordion').window('collapse', true);
            jQuery('#thematic-maps-grid').datagrid('loadRpc');
            loadThematicLayer();
            initThematicMapLegend(data.colorSet);
            if (parseInt(data.layerData.chart_type, 10) === 1) {
                drawPieChart(data.colorSet, newThematicMapData.chart_criteria);
            } else {
                drawColumnChart(data.colorSet, newThematicMapData.chart_criteria);
            }
            populateSettingsPanel();
            zoomToExtent(response.extent);
        })
        .fail(function (error) {
            jQuery.messager.alert('Грешка', error.getMessage(), 'warning');
        });
}

function visualizeFilterValues() {
    'use strict';

    var html = '',
        text = '',
        textColumns = ['nm_usage_rights', 'farming_id', 'cropcode', 'schemata'];
    jQuery('#thematic-maps-currently-applied-filters').html('');
    if (newThematicMapData.filters.length === 0) {
        html = '&nbsp;Няма зададени филтри';
    } else {
        newThematicMapData.filters.forEach(function (el) {
            text = '';
            if (el.filterValue[0] === "true") {
                text = 'Да';
            }
            if (el.filterValue[0] === "false") {
                text = 'Не';
            }
            if (textColumns.indexOf(el.column) > -1) {
                text = el.filterValueText;
            }
            if (text === '') {
                text = el.filterValue;
            }

            html += '<span class="filter-bubble" data-column="' + el.column + '">' + el.filterText + ': ' + text + '<a class="remove-filter" data-ownership="map-init" data-column="' + el.column + '" data-filterValue="' + el.filterValue + '" ></a></span>';
        });
    }

    jQuery('#thematic-maps-currently-applied-filters').html(html);

    jQuery('a[data-ownership="map-init"]').bind('click', function () {
        removeCurrentFilterElement(this);
    });
}

function removeCurrentFilterElement(element) {
    'use strict';
    var index = -1,
        i = 0,
        column = jQuery(element).data('column'),
        value = jQuery(element).data('filtervalue');

    for (i = 0; i < newThematicMapData.filters.length; i = i + 1) {
        if (newThematicMapData.filters[i].column.toString() === column.toString() && newThematicMapData.filters[i].filterValue.toString() === value.toString()) {
            index = i;
        }
    }

    newThematicMapData.filters.splice(index, 1);
    visualizeFilterValues();
}

function addNewThematicMapsFilter() {
    'use strict';

    var filter = {},
        data = jQuery('#main-layer-columns').combobox('getData');

    filter.column = jQuery('#main-layer-columns').combobox('getValue');
    filter.filterText = jQuery('#main-layer-columns').combobox('getText');

    data.forEach(function (field) {
        if (field.id === filter.column) {
            filter.prefix = field.prefix;
        }
    });

    if (jQuery('#main-layer-columns-value').data().hasOwnProperty('combobox')) {
        filter.filterValue = jQuery('#main-layer-columns-value').combobox('getValues');
        filter.filterValueText = jQuery('#main-layer-columns-value').combobox('getText');
    } else if (jQuery('#main-layer-columns-value').data().hasOwnProperty('datebox')) {
        filter.filterValue = jQuery('#main-layer-columns-value').datebox('getValue');
    } else {
        filter.filterValue = jQuery('#main-layer-columns-value').val();
    }

    if (filter.column === '' || filter.filterValue === '') {
        jQuery.messager.alert('Грешка', 'Моля въведете данни за всички полета.', 'warning');
    } else {
        newThematicMapData.filters.push(filter);
        visualizeFilterValues();
    }

    jQuery('#main-layer-columns').combobox('reset');
    recreateFilterValueField();
}

function displayExistingLayer(thematicMap) {
    'use strict';
    var colorSet = {};
    TF.Rpc.ThematicMaps.ThematicMapsResultsGrid.load(thematicMap.id)
        .done(function (data) {
            selectedThematicMapID = data.layerData.id;
            newThematicMapData = data.layerData;
            colorSet.rows = data.colorSet.rows;
            colorSet.total = data.colorSet.rows.length;
            newThematicMapColorArray = {};
            newThematicMapColorArray = colorSet;
            loadThematicLayer();
            initThematicMapLegend(colorSet);
            if (parseInt(data.layerData.chart_type, 10) === 1) {
                drawPieChart(colorSet, data.layerData.chart_criteria);
            } else {
                drawColumnChart(colorSet, data.layerData.chart_criteria);
            }
            populateSettingsPanel();
            zoomToExtent(newThematicMapData.extent);

            jQuery('#thematic-maps-accordion').accordion('panels')[0].panel('collapse', false);
            jQuery('#thematic-maps-accordion').accordion('panels')[1].panel('expand', false);
            jQuery('#thematic-maps-accordion').accordion('panels')[2].panel('expand', false);
        })
        .fail(function (error) {
            jQuery.messager.alert('Грешка', error.getMessage(), 'warning');
        });
}

function initMainLayerCombobox() {
    'use strict';

    jQuery('#new-thematic-maps-main-layer').combobox({
        url: 'index.php?thematic-maps-rpc=thematic-maps-main-layer-combobox',
        valueField: 'layer_id',
        textField: 'name',
        groupField: 'farming_name',
        required: true,
        editable: false,
        missingMessage: 'Моля изберете основен слой',
        onBeforeLoad: function () {
            TF.Loading.start();
        },
        onLoadSuccess: function () {
            TF.Loading.end();
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initThematicMapsGrid() {
    'use strict';
    var datagrid = jQuery('#thematic-maps-grid'),
        id;
    datagrid.datagrid({
        url: 'index.php?thematic-maps-rpc=thematic-maps-layers',
        rpcParams: [],
        iconCls: 'icon-plot',
        nowrap: true,
        singleSelect: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 20,
        fit: true,
        fitColumns: true,
        showFooter: false,
        sortName: 'id',
        sortOrder: 'asc',
        idField: 'id',
        pagination: false,
        rownumbers: true,
        border: false,
        columns: [
            [{
                field: 'name',
                title: '<b>Име</b>',
                sortable: false,
                width: "45%"
            }, {
                field: 'description',
                title: '<b>Основен слой</b>',
                sortable: false,
                width: "55%"
            }]
        ],
        toolbar: [{
            id: 'btndisplaythematicmap',
            text: 'Визуализиране',
            iconCls: 'icon-map',
            handler: function () {
                var rowData = datagrid.datagrid('getSelected');
                if (!rowData) {
                    jQuery.messager.alert('Грешка', 'Моля изберете карта, която да бъде визуализирана, или създайте нова.', 'info');
                    return false;
                }
                displayExistingLayer(rowData);
            }
        }, {
            id: 'btnaddnewthematicmap',
            text: 'Добавяне',
            iconCls: 'icon-add',
            handler: function () {
                if (!hasThematicMapsRightsRW) {
                    messagerThematicMapsRights();
                    return false;
                }
                generateNewLayer();
            }
        }, {
            id: 'btndeleteexistingthematicmap',
            text: 'Изтриване',
            iconCls: 'icon-delete',
            handler: function () {
                if (!hasThematicMapsRightsRW) {
                    messagerThematicMapsRights();
                    return false;
                }
                id = datagrid.datagrid('getSelected').id;
                TF.Rpc.ThematicMaps.ThematicMapsLayers.delete(id)
                    .done(function () {
                        isThematicMapDeleted = true;
                        datagrid.datagrid('loadRpc');
                        removeAllLayers();
                        jQuery('#thematic-map-legend-datagrid').datagrid('loadData', {"total":0,"rows":[]});
                        jQuery('#thematic-map-chart-datagrid').html('');
                        populateSettingsPanel(true);
                        jQuery('#win-thematic-maps-accordion').window('open');
                        jQuery('#thematic-maps-accordion').accordion('panels')[0].panel('expand', true);
                    })
                    .fail(function (error) {
                        jQuery.messager.alert('Грешка', error.getMessage(), 'warning');
                    });
            }
        }],
        onBeforeLoad: function () {
            jQuery(this).datagrid('clearChecked');
        },
        onLoadSuccess: function () {
            jQuery(this).datagrid('fitColumns');
            jQuery('#win-thematic-maps-accordion').window({
                left: 20,
                top: 87
            });
            jQuery('#win-thematic-maps-accordion').window('open');
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function printThematicMap(size) {
    'use strict';
    if (selectedThematicMapID === undefined) {
        jQuery.messager.alert('Внимание', 'Моля изберете тематична карта, която да бъде отпечатана или създайте нова.', 'warning');
        return false;
    }
    var extent = map.calculateBounds().transform(map.projection, map.displayProjection).toBBOX();
    TF.Rpc.ThematicMaps.ThematicMapsLayers.print(selectedThematicMapID, size, extent)
        .done(function (dataObj) {
            jQuery('#btn-download-file').attr('href', dataObj);
            jQuery('#win-download').window('open');
        })
        .fail(function (errorObj) {
            jQuery.messager.alert('Грешка', errorObj.getMessage());
        });
}

function printFullThematicMap(size) {
    'use strict';
    if (selectedThematicMapID === undefined) {
        jQuery.messager.alert('Внимание', 'Моля изберете тематична карта, която да бъде отпечатана или създайте нова.', 'warning');
        return false;
    }
    TF.Rpc.ThematicMaps.ThematicMapsLayers.print(selectedThematicMapID, size)
        .done(function (dataObj) {
            jQuery('#btn-download-file').attr('href', dataObj);
            jQuery('#win-download').window('open');
        })
        .fail(function (errorObj) {
            jQuery.messager.alert('Грешка', errorObj.getMessage());
        });
}

function initThematicMapsAccordion() {
    'use strict';
    jQuery('#thematic-maps-accordion').accordion({
        animate: true,
        multiple: true,
    });
}

jQuery(function () {
    'use strict';
    setUserRights();

    var filterPanel = jQuery('#inner-thematic-layout').data('layout').panels['expandEast'];

    filterPanel.html('<p style="transform: rotate(270deg);padding:6px 2px; font-weight: bold; width:200px; float: left; margin-left: -90px; margin-top: -40px;text-rendering: optimizeLegibility;">Настройки</p>');

    jQuery('#map-types-combobox > input').combobox({
        url: 'index.php?common-rpc=map-types-combobox',
        rpcParams: [{selected: true}],
        valueField: 'id',
        textField: 'name',
        onSelect: function (record) {
            initMapPad(record.id);
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
    });

    initMapPad();
    initDefaultMapControls();
    zoomToBulgaria();
    initPositionDisplay();
    initScaleDisplay();
    initMainLayerCombobox();
    initThematicMapsAccordion();
    initThematicMapsGrid();
    initColorPalettesCombobox();

    jQuery('#thematic-maps-proceed-to-step-2').bind('click', function () {
        if (isStep1Valid()) {
            generateNewLayerStep2();
        } else {
            jQuery.messager.alert('Грешка', 'Моля въведете данни за всички полета.', 'warning');
        }
    });

    jQuery('#close-win-new-thematic-maps').bind('click', function () {
        jQuery('#win-new-thematic-maps').window('close');
    });

    jQuery('#add-new-thematic-maps-filter').bind('click', function () {
        addNewThematicMapsFilter();
    });

    jQuery('#close-win-new-thematic-maps-step2').bind('click', function () {
        jQuery('#win-new-thematic-maps-step2').window('close');
        jQuery('#win-new-thematic-maps').window('open');
    });

    jQuery('#close-win-new-thematic-maps-step3').bind('click', function () {
        jQuery('#win-new-thematic-maps-step3').window('close');
        jQuery('#win-new-thematic-maps-step2').window('open');
    });

    jQuery('#thematic-maps-proceed-to-step-3').bind('click', function () {
        generateNewLayerStep3();
    });

    jQuery('#close-win-new-thematic-maps-step4').bind('click', function () {
        jQuery('#win-new-thematic-maps-step4').window('close');
        jQuery('#win-new-thematic-maps-step3').window('open');
    });

    jQuery('#thematic-maps-proceed-to-step-4').bind('click', function () {
        if (isStep3Valid()) {
            generateNewLayerStep4();
        } else {
            jQuery.messager.alert('Грешка', 'Моля изберете поле за оцветяване.', 'warning');
        }
    });

    jQuery('#close-win-new-thematic-maps-step5').bind('click', function () {
        jQuery('#win-new-thematic-maps-step5').window('close');
        jQuery('#win-new-thematic-maps-step4').window('open');
    });

    jQuery('#thematic-maps-proceed-to-step-5').bind('click', function () {
        var data = jQuery('#new-thematic-maps-results-grid').datagrid('getData');
        if (data.total > 0) {
            generateNewLayerStep5();
        } else {
            jQuery.messager.alert('Грешка', 'Не може да създадете тематична карта без да има имоти, които да отговарят на критериите за филтриране. Моля изберете по-подходящи критерии за филтриране.', 'warning');
            return false;
        }
    });

    jQuery('#thematic-maps-proceed-to-step-6').bind('click', function () {
        generateNewLayerStep6();
    });

    google.charts.load('current', {'packages': ['corechart']});

    jQuery('#open-legend-button').on('change', function () {
        if (jQuery('#open-legend-button').prop('checked')) {
            jQuery('#win-thematic-maps-accordion').window('expand', true);
            jQuery('#thematic-maps-accordion').accordion('panels')[2].panel('expand', true);
        } else {
            jQuery('#thematic-maps-accordion').accordion('panels')[2].panel('collapse', true);
        }
    });

    jQuery('#open-diagram-button').on('change', function () {
        if (jQuery('#open-diagram-button').prop('checked')) {
            jQuery('#win-thematic-maps-accordion').window('expand', true);
            jQuery('#thematic-maps-accordion').accordion('panels')[1].panel('expand', true);
        } else {
            jQuery('#thematic-maps-accordion').accordion('panels')[1].panel('collapse', true);
        }
    });

    jQuery('#open-thematic-maps-button').on('change', function () {
        if (jQuery('#open-thematic-maps-button').prop('checked')) {
            jQuery('#win-thematic-maps-accordion').window('expand', true);
            jQuery('#thematic-maps-accordion').accordion('panels')[0].panel('expand', true);
        } else {
            jQuery('#thematic-maps-accordion').accordion('panels')[0].panel('collapse', true);
        }
    });

    jQuery('#win-thematic-maps-accordion').window({
        onClose: function () {
            jQuery('#open-thematic-maps-tools-button').prop('checked', false);
        },
        onBeforeExpand: function () {
            jQuery('#win-thematic-maps-accordion').parent().next().css('display', 'none');
        }
    });

    jQuery('#open-thematic-maps-tools-button').bind('change', function () {
        if (jQuery('#open-thematic-maps-tools-button').is(':checked')) {
            jQuery('#win-thematic-maps-accordion').window('open');
        } else {
            jQuery('#win-thematic-maps-accordion').window('close');
        }
    });

    jQuery('#thematic-maps-accordion').accordion('panels')[0].panel({
        onBeforeCollapse: function () {
            jQuery('#open-thematic-maps-button').prop('checked', false);
            jQuery('#win-thematic-maps-accordion').parent().next().css('display', 'none');
        },
        onBeforeExpand: function () {
            jQuery('#open-thematic-maps-button').prop('checked', true);
            jQuery('#thematic-maps-accordion').accordion('panels')[1].panel('collapse', true);
            jQuery('#thematic-maps-accordion').accordion('panels')[2].panel('collapse', true);
        }
    });

    jQuery('#thematic-maps-accordion').accordion('panels')[2].panel({
        onBeforeCollapse: function () {
            jQuery('#open-legend-button').prop('checked', false);
            jQuery('#win-thematic-maps-accordion').parent().next().css('display', 'none');
        },
        onBeforeExpand: function () {
            jQuery('#open-legend-button').prop('checked', true);
            jQuery('#thematic-maps-accordion').accordion('panels')[0].panel('collapse', true);
        }
    });

    jQuery('#thematic-maps-accordion').accordion('panels')[1].panel({
        onBeforeCollapse: function () {
            jQuery('#open-diagram-button').prop('checked', false);
            jQuery('#win-thematic-maps-accordion').parent().next().css('display', 'none');
        },
        onBeforeExpand: function () {
            jQuery('#open-diagram-button').prop('checked', true);
            jQuery('#thematic-maps-accordion').accordion('panels')[0].panel('collapse', true);
        }
    });

    jQuery('#print-thematic-map-button>div').bind('click', function (e) {
        var size = jQuery(e.target).parent().data('size');
        printThematicMap(size);
    });

    jQuery('#print-thematic-map-button-full>div').bind('click', function (e) {
        var size = jQuery(e.target).parent().data('size');
        printFullThematicMap(size);
    });

    jQuery('#total-area-diagram-criteria').bind('change', function () {
        if (jQuery('#total-area-diagram-criteria').is(':checked')) {
            if (jQuery('#bar-diagram-type').is(':checked')) {
                drawColumnChart(newThematicMapColorArray, 2);
            } else {
                drawPieChart(newThematicMapColorArray, 2);
            }
        }
    });

    jQuery('#plot-count-diagram-criteria').bind('change', function () {
        if (jQuery('#plot-count-diagram-criteria').is(':checked')) {
            if (jQuery('#bar-diagram-type').is(':checked')) {
                drawColumnChart(newThematicMapColorArray, 1);
            } else {
                drawPieChart(newThematicMapColorArray, 1);
            }
        }
    });

    jQuery('#circular-diagram-type').bind('change', function () {
        if (jQuery('#circular-diagram-type').is(':checked')) {
            if (jQuery('#total-area-diagram-criteria').is(':checked')) {
                drawPieChart(newThematicMapColorArray, 2);
            } else {
                drawPieChart(newThematicMapColorArray, 1);
            }
        }
    });

    jQuery('#bar-diagram-type').bind('change', function () {
        if (jQuery('#bar-diagram-type').is(':checked')) {
            if (jQuery('#total-area-diagram-criteria').is(':checked')) {
                drawColumnChart(newThematicMapColorArray, 2);
            } else {
                drawColumnChart(newThematicMapColorArray, 1);
            }
        }
    });
});

function createLayerKVSFields(component, field) {
    switch (field.id) {
        case 'ekate':
            component.combobox({
                url: 'index.php?common-rpc=ekate-combobox',
                valueField: 'ekate',
                textField: 'text',
                multiple: true,
                onHidePanel: onHidePanelMultiSelect,
                onSelect: onComboMultiSelect,
                filter: function (q, row) {
                    var opts = jQuery(this).combobox('options'),
                        text = row[opts.textField].toLowerCase(),
                        value = row[opts.valueField],
                        find = q.toLowerCase();
                    if (text.indexOf(find) !== -1 || value.indexOf(find) !== -1) {
                        return true;
                    }
                },
                onBeforeLoad: function () {
                    TF.Loading.start();
                },
                onLoadSuccess: function () {
                    TF.Loading.end();
                },
                loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
            });
            break;
        case 'category':
            component.combobox({
                url: 'index.php?common-rpc=plot-category-combobox',
                valueField: 'id',
                textField: 'name',
                multiple: true,
                onHidePanel: onHidePanelMultiSelect,
                onSelect: onComboMultiSelect,
                onBeforeLoad: function () {
                    TF.Loading.start();
                },
                onLoadSuccess: function () {
                    TF.Loading.end();
                },
                loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
            });
            break;
        case 'area_type':
            component.combobox({
                url: 'index.php?common-rpc=plot-ntp-combobox',
                valueField: 'id',
                textField: 'name',
                multiple: true,
                onHidePanel: onHidePanelMultiSelect,
                onSelect: onComboMultiSelect,
                onBeforeLoad: function () {
                    TF.Loading.start();
                },
                onLoadSuccess: function () {
                    TF.Loading.end();
                },
                loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
            });
            break;
        case 'mestnost':
            component.combobox({
                url: 'index.php?common-rpc=mestnost-combobox',
                valueField: 'mestnost',
                textField: 'text',
                multiple: true,
                onHidePanel: onHidePanelMultiSelect,
                onSelect: onComboMultiSelect,
                onBeforeLoad: function () {
                    TF.Loading.start();
                },
                onLoadSuccess: function () {
                    TF.Loading.end();
                },
                loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
            });
            break;
        case 'nm_usage_rights':
            component.combobox({
                url: 'index.php?common-rpc=contract-type-combobox',
                valueField: 'id',
                textField: 'name',
                multiple: true,
                onHidePanel: onHidePanelMultiSelect,
                onSelect: onComboMultiSelect,
                onBeforeLoad: function () {
                    TF.Loading.start();
                },
                onLoadSuccess: function () {
                    TF.Loading.end();
                },
                loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
            });
            break;
        case 'farming_id':
            component.combobox({
                url: 'index.php?common-rpc=farming-combobox',
                valueField: 'id',
                textField: 'name',
                multiple: true,
                onHidePanel: onHidePanelMultiSelect,
                onSelect: onComboMultiSelect,
                onBeforeLoad: function () {
                    TF.Loading.start();
                },
                onLoadSuccess: function () {
                    TF.Loading.end();
                },
                loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
            });
            break;
        default:
            recreateFilterValueField();
            break;
    }
}

function createLayerIsakFields(component, field) {
    switch (field.id) {
        case 'cropcode':
            component.combobox({
                url: 'index.php?common-rpc=isak-culture-combobox',
                valueField: 'cropcode',
                textField: 'cropname',
                multiple: true,
                rpcParams: [{
                    layer_name: newThematicMapData.mapLayer
                }],
                onHidePanel: onHidePanelMultiSelect,
                onSelect: onComboMultiSelect,
                filter: function (q, row) {
                    var opts = jQuery(this).combobox('options'),
                        text = row[opts.textField].toLowerCase(),
                        value = row[opts.valueField],
                        find = q.toLowerCase();
                    if (text.indexOf(find) !== -1 || value.indexOf(find) !== -1) {
                        return true;
                    }
                },
                onBeforeLoad: function () {
                    TF.Loading.start();
                },
                onLoadSuccess: function () {
                    TF.Loading.end();
                },
                loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
            });
            break;
        case 'ekatte':
            component.combobox({
                url: 'index.php?common-rpc=isak-ekatte-combobox',
                valueField: 'ekatte',
                textField: 'name',
                rpcParams: [{
                    layer_name: newThematicMapData.mapLayer
                }],
                multiple: true,
                onHidePanel: onHidePanelMultiSelect,
                onSelect: onComboMultiSelect,
                onBeforeLoad: function () {
                    TF.Loading.start();
                },
                onLoadSuccess: function () {
                    TF.Loading.end();
                },
                loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
            });
            break;
        case 'schemata':
            component.combobox({
                url: 'index.php?common-rpc=schema-combobox',
                valueField: 'id',
                textField: 'name',
                multiple: true,
                rpcParams: [{
                    layer_name: newThematicMapData.mapLayer
                }],
                onHidePanel: onHidePanelMultiSelect,
                onSelect: onComboMultiSelect,
                onBeforeLoad: function () {
                    TF.Loading.start();
                },
                onLoadSuccess: function () {
                    TF.Loading.end();
                },
                loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
            });
            break;
        case 'campaign':
            component.combobox({
                url: 'index.php?common-rpc=isak-campaign-combobox',
                valueField: 'campaign',
                textField: 'campaign',
                rpcParams: [{
                    layer_name: newThematicMapData.mapLayer
                }],
                multiple: true,
                onHidePanel: onHidePanelMultiSelect,
                onSelect: onComboMultiSelect,
                onBeforeLoad: function () {
                    TF.Loading.start();
                },
                onLoadSuccess: function () {
                    TF.Loading.end();
                },
                loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
            });
            break;
        case 'nm_usage_rights':
            component.combobox({
                url: 'index.php?common-rpc=contract-type-combobox',
                valueField: 'id',
                textField: 'name',
                multiple: true,
                onHidePanel: onHidePanelMultiSelect,
                onSelect: onComboMultiSelect,
                onBeforeLoad: function () {
                    TF.Loading.start();
                },
                onLoadSuccess: function () {
                    TF.Loading.end();
                },
                loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
            });
            break;
        case 'farming_id':
            component.combobox({
                url: 'index.php?common-rpc=farming-combobox',
                valueField: 'id',
                textField: 'name',
                multiple: true,
                onHidePanel: onHidePanelMultiSelect,
                onSelect: onComboMultiSelect,
                onBeforeLoad: function () {
                    TF.Loading.start();
                },
                onLoadSuccess: function () {
                    TF.Loading.end();
                },
                loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
            });
            break;
        default:
            recreateFilterValueField();
            break;
    }
}
