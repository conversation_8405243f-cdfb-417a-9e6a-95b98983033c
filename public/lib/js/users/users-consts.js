const SUBSCRIPTION_MAP = 'map';
const SUBSCRIPTION_PLOTS = 'plots';
const SUBSCRIPTION_AGRO = 'agro';


/**
 * USERS FLAGS as defined in /protected/Common/Config.php
 *
 * @type {number}
 */
const USERS_SUPER_ADMIN_FLAG = 1;
const USERS_ADMIN_FLAG = 2;
const USERS_NORMAL = 3;
const USERS_OPERATOR_FLAG = 4; // Probably not in use.
const USERS_SUPPORT_FLAG = 5;
const USERS_SALES_FLAG = 6;

/**
 * Returns columns configuration based on user's level.
 *
 * @param {number/string} userLevel
 * @returns {*[][]} - Columns configuration
 */
function columnsConfig(userLevel) {
    switch (parseInt(userLevel)) {
        case USERS_SUPER_ADMIN_FLAG:
            return [[
                {
                    field: 'username',
                    title: '<b>Потребител</b>',
                    sortable: true,
                    width: 150
                }, {
                    field: 'name',
                    title: '<b>Име</b>',
                    sortable: true,
                }, {
                    field: 'identity_number',
                    title: '<b>ЕИК</b>',
                    sortable: true,
                }, {
                    field: 'email',
                    title: '<b>Email</b>',
                    sortable: true,
                }, {
                    field: 'level',
                    title: '<b>Тип</b>',
                    sortable: true,
                    width: 80
                }, {
                    field: 'active',
                    title: '<b>Статус</b>',
                    sortable: true,
                    width: 120
                }, {
                    field: 'is_trial',
                    title: '<b>Временен</b>',
                    sortable: true,
                    width: 120,
                    formatter: function (val) {
                        return val ? 'Да' : 'Не';
                    }
                }, {
                    field: 'start_date',
                    title: '<b>Начална</b></br><b>дата</b>',
                    sortable: true,
                    width: 120
                }, {
                    field: 'due_date',
                    title: '<b>Крайна</b></br><b>дата</b>',
                    sortable: true,
                    width: 120
                }, {
                    field: 'creation_date',
                    title: '<b>Дата на</b></br><b>създаване</b>',
                    sortable: true,
                    width: 120
                }, {
                    field: 'paid_support',
                    title: '<b>ГАП</b>',
                    sortable: true,
                    width: 75
                }, {
                    field: 'paid_support_due_date',
                    title: '<b>Платен до</b>',
                    sortable: true,
                    width: 140,
                    formatter: function (val, row) {
                        return val;
                    }
                }, {
                    field: 'parent_username',
                    title: '<b>Родителски</b></br><b>акаунт</b>',
                    sortable: true,
                    width: 150
                }, {
                    field: 'ekatte_count',
                    title: '<b>Брой</b></br><b>землища</b>',
                    sortable: true,
                    width: 100
                },{
                    field: 'category_map',
                    title: '<b>Категория</br>карта</b>',
                    width: 170,
                    align: 'center'
                }, {
                    field: 'user_rights',
                    title: '<b>Използвани модули</b>',
                    sortable: true,
                    formatter: function (val) {
                        var ul = '<ul>';

                        if (val) {
                            var items = val.split(', ');
                            var itemsCount = items.length;

                            for(var i = 0; i < itemsCount; i++) {
                                ul += '<li>' + items[i] + '</li>';
                            }
                        }

                        ul += '</ul>';

                        return ul;
                    }
                }, {
                    field: 'total_plot_area',
                    title: '<b>Обща площ(дка)</b>',
                    sortable: true,
                    width: 170
                }, {
                    field: 'category_plots',
                    title: '<b>Категория</br>Имоти</b>',
                    width: 170,
                    align: 'center'
                }, {
                    field: 'last_login_date',
                    title: '<b>Последно влизане</b>',
                    sortable: true,
                    width: 140,
                    formatter: function (val, row) {
                        return val;
                    }
                }, {
                    field: 'login_as_user',
                    title: '<b>Вход</b>',
                    sortable: false,
                    width: 150,
                    align: 'center',
                    formatter: function (val, row) {
                        if(row.username) {
                            return '<span href="#" class="easyui-linkbutton login-user easyui-tooltip" data-options="iconCls:\'icon-redirect\', position: \'top\', content: \'Влизане като потребител\', plain: true" onClick="loginAsUser(\''+row.username+'\')">Вход</span>';
                        }
                    }
                }
            ]];
        case USERS_SUPPORT_FLAG:
            return [[
                {
                    field: 'username',
                    title: '<b>Потребител</b>',
                    sortable: true,
                    width: 150
                }, {
                    field: 'name',
                    title: '<b>Име</b>',
                    sortable: true
                }, {
                    field: 'email',
                    title: '<b>Email</b>',
                    sortable: true,
                }, {
                    field: 'level',
                    title: '<b>Тип</b>',
                    sortable: true,
                    width: 80
                }, {
                    field: 'active',
                    title: '<b>Статус</b>',
                    sortable: true,
                    width: 120
                }, {
                    field: 'is_trial',
                    title: '<b>Временен</b>',
                    sortable: true,
                    width: 120,
                    formatter: function (val) {
                        return val ? 'Да' : 'Не';
                    }
                }, {
                    field: 'start_date',
                    title: '<b>Начална</b></br><b>дата</b>',
                    sortable: true,
                    width: 120
                }, {
                    field: 'due_date',
                    title: '<b>Крайна</b></br><b>дата</b>',
                    sortable: true,
                    width: 120
                }, {
                    field: 'creation_date',
                    title: '<b>Дата на</b></br><b>създаване</b>',
                    sortable: true,
                    width: 120
                }, {
                    field: 'paid_support',
                    title: '<b>ГАП</b>',
                    sortable: true,
                    width: 95
                }, {
                    field: 'paid_support_due_date',
                    title: '<b>Платен до</b>',
                    sortable: true,
                    width: 140,
                    formatter: function (val, row) {
                        return val;
                    }
                }, {
                    field: 'parent_username',
                    title: '<b>Родителски</b></br><b>акаунт</b>',
                    sortable: true,
                    width: 150
                }, {
                    field: 'ekatte_count',
                    title: '<b>Брой</b></br><b>землища</b>',
                    sortable: true,
                    width: 100
                }, {
                    field: 'category_map',
                    title: '<b>Категория</br>карта</b>',
                    width: 170,
                    align: 'center'
                }, {
                    field: 'user_rights',
                    title: '<b>Използвани модули</b>',
                    sortable: true,
                    formatter: function (val) {
                        var ul = '<ul>';

                        if (val) {
                            var items = val.split(', ');
                            var itemsCount = items.length;

                            for(var i = 0; i < itemsCount; i++) {
                                ul += '<li>' + items[i] + '</li>';
                            }
                        }

                        ul += '</ul>';

                        return ul;
                    }
                }, {
                    field: 'total_plot_area',
                    title: '<b>Обща площ(дка)</b>',
                    sortable: true,
                    width: 170
                }, {
                    field: 'category_plots',
                    title: '<b>Категория</br>Имоти</b>',
                    width: 170,
                    align: 'center'
                }, {
                    field: 'last_login_date',
                    title: '<b>Последно влизане</b>',
                    sortable: true,
                    width: 140,
                    formatter: function (val, row) {
                        return val;
                    }
                }, {
                    field: 'login_as_user',
                    title: '<b>Вход</b>',
                    sortable: false,
                    width: 150,
                    align: 'center',
                    formatter: function (val, row) {
                        if(row.username) {
                            return '<span href="#" class="easyui-linkbutton login-user easyui-tooltip" data-options="iconCls:\'icon-redirect\', position: \'top\', content: \'Влизане като потребител\', plain: true" onClick="loginAsUser(\''+row.username+'\')">Вход</span>';
                        }
                    }
                }
            ]];
        case USERS_SALES_FLAG:
            return [[
                {
                    field: 'username',
                    title: '<b>Потребител</b>',
                    sortable: true,
                    width: 150
                }, {
                    field: 'name',
                    title: '<b>Име</b>',
                    sortable: true
                }, {
                    field: 'identity_number',
                    title: '<b>ЕИК</b>',
                    sortable: true,
                },{
                    field: 'email',
                    title: '<b>Email</b>',
                    sortable: true,
                }, {
                    field: 'level',
                    title: '<b>Тип</b>',
                    sortable: true,
                    width: 80
                }, {
                    field: 'active',
                    title: '<b>Статус</b>',
                    sortable: true,
                    width: 120
                }, {
                    field: 'is_trial',
                    title: '<b>Временен</b>',
                    sortable: true,
                    width: 120,
                    formatter: function (val) {
                        return val ? 'Да' : 'Не';
                    }
                }, {
                    field: 'creation_date',
                    title: '<b>Дата на</b></br><b>създаване</b>',
                    sortable: true,
                    width: 120
                }, {
                    field: 'paid_support',
                    title: '<b>ГАП</b>',
                    sortable: true,
                    width: 95
                }, {
                    field: 'paid_support_due_date',
                    title: '<b>Платен до</b>',
                    sortable: true,
                    width: 140,
                    formatter: function (val, row) {
                        return val;
                    }
                }, {
                    field: 'ekatte_count',
                    title: '<b>Брой</b></br><b>землища</b>',
                    sortable: true,
                    width: 100
                }, {
                    field: 'category_map',
                    title: '<b>Категория</br>карта</b>',
                    width: 170,
                    align: 'center'
                }, {
                    field: 'user_rights',
                    title: '<b>Използвани модули</b>',
                    sortable: true,
                    formatter: function (val) {
                        var ul = '<ul>';

                        if (val) {
                            var items = val.split(', ');
                            var itemsCount = items.length;

                            for(var i = 0; i < itemsCount; i++) {
                                ul += '<li>' + items[i] + '</li>';
                            }
                        }

                        ul += '</ul>';

                        return ul;
                    }
                }, {
                    field: 'total_plot_area',
                    title: '<b>Обща площ(дка)</b>',
                    sortable: true,
                    width: 170
                }, {
                    field: 'category_plots',
                    title: '<b>Категория</br>имоти</b>',
                    width: 170,
                    align: 'center'
                }, {
                    field: 'last_login_date',
                    title: '<b>Последно влизане</b>',
                    sortable: true,
                    width: 140,
                    formatter: function (val, row) {
                        return val;
                    }
                }, {
                    field: 'login_as_user',
                    title: '<b>Вход</b>',
                    sortable: false,
                    width: 150,
                    align: 'center',
                    formatter: function (val, row) {
                        if(row.username) {
                            return '<span href="#" class="easyui-linkbutton login-user easyui-tooltip" data-options="iconCls:\'icon-redirect\', position: \'top\', content: \'Влизане като потребител\', plain: true" onClick="loginAsUser(\''+row.username+'\')">Вход</span>';
                        }
                    }
                }
            ]];
        default: // User
            return [[
                {
                    field: 'username',
                    title: '<b>Потребител</b>',
                    sortable: true,
                    width: 150
                }, {
                    field: 'name',
                    title: '<b>Име</b>',
                    sortable: true
                }, {
                    field: 'email',
                    title: '<b>Email</b>',
                    sortable: true,
                }, {
                    field: 'level',
                    title: '<b>Тип</b>',
                    sortable: true,
                    width: 80
                }, {
                    field: 'active',
                    title: '<b>Статус</b>',
                    sortable: true,
                    width: 120
                }, {
                    field: 'creation_date',
                    title: '<b>Дата на</b></br><b>създаване</b>',
                    sortable: true,
                    width: 120
                }, {
                    field: 'paid_support',
                    title: '<b>ГАП</b>',
                    sortable: true,
                    width: 95
                }, {
                    field: 'parent_username',
                    title: '<b>Родителски</b></br><b>акаунт</b>',
                    sortable: true,
                    width: 150
                }, {
                    field: 'login_as_user',
                    title: '<b>Вход</b>',
                    sortable: false,
                    width: 150,
                    align: 'center',
                    formatter: function (val, row) {
                        if(row.username) {
                            return '<span href="#" class="easyui-linkbutton login-user easyui-tooltip" data-options="iconCls:\'icon-redirect\', position: \'top\', content: \'Влизане като потребител\', plain: true" onClick="loginAsUser(\''+row.username+'\')">Вход</span>';
                        }
                    }
                }
            ]];
    }
}
