jQuery(function(){
    let urlparams = new URLSearchParams(window.location.search);

    jQuery('#username').validatebox({
        required: true,
        validType: 'username',
        missingMessage: 'Въведете потребителско име!'
    });

    jQuery('#password').validatebox({
        required: true,
        validType: 'password',
        missingMessage: 'Въведете парола за достъп!'
    });

    jQuery('#new-password').validatebox({
        required: true,
        validType: 'password',
        missingMessage: 'Въведете парола за достъп!'
    });

    jQuery('#email').validatebox({
        required: true,
        validType: 'email'
    });
    jQuery('#forgotten-password-username').validatebox({
        required: true
    });

    jQuery('#re-password').validatebox({
        required: true,
        validType: 'password',
        missingMessage: 'Моля повторете паролата!'
    });

    jQuery('#btn-forgotten-send').linkbutton({
        onClick: function(){
            if(jQuery('#email').validatebox('isValid')) {
                var data = {
                    email: jQuery('#email').val(),
                    username: jQuery('#forgotten-password-username').val()
                };
                TF.Rpc.Login.ForgottenPassword.initForgottenPasswordAction(data)
                .done(function (data) {
                    forgottenPassword(data);
                })
                .fail(function (errorObj) {

                });
            }else{
                jQuery.messager.alert('Грешка!', 'Невалиден имейл!');
            }
        }
    });

    jQuery('#link-forgotten-pass-login').bind('click', function(){
        jQuery('#win-forgotten-password').window('open');
    });

    jQuery('#btn-change-password').bind('click', function(){
        if(!validateNewPassword()) {
            jQuery.messager.alert('Невалидна информация', 'Паролите не съвпадат');
            return;
        }

        if(!urlparams.get('user_id') || !urlparams.get('hash')) {
            jQuery.messager.alert('Невалидна информация', 'Грешни данни');
            return;
        }

        let params = {
            'user_id': urlparams.get('user_id'),
            'hash': urlparams.get('hash'),
            'password': jQuery('#new-password').val()
        };
        TF.Rpc.Login.ForgottenPassword.setNewPassword(params)
            .done(function (data) {
                jQuery.messager.alert('Успешна промяна', 'Паролата Ви беше променена. Ще бъдете пренасочени към страницата за вход.');
                window.location.replace(window.location.origin);
            })
            .fail(function (errorObj) {
                if (errorObj.is(TF.Rpc.ExceptionsList.WRONG_USERNAME_OR_PASSWORD) || errorObj.is(TF.Rpc.ExceptionsList.ACCOUNT_IS_NOT_ACTIVE)) {
                    jQuery.messager.alert('Warning',errorObj.getMessage(),'info');
                }
            });
    });

    jQuery('#username').on('keyup', function (e) {
        if (e.which == 13) {
            doUserLoginActions();
        };
    });

    jQuery('#password').on('keyup', function (e) {
        if (e.which == 13) {
            doUserLoginActions();
        };
    });
});

function doUserLoginActions(){

    if(!validateForm())
    {
        return;
    }

    //form values
    var username = jQuery('#username').val();
    var password = jQuery('#password').val();

    TF.Rpc.Login.LoginForm.loginUser(username, password)
    .done(function (loginData) {
        if(!loginData) {
            jQuery.messager.alert({
                title: 'Нова версия',
                msg: 'За да се логнете успешно, моля използвайте новате версия на софтуера с адрес: <p style="text-align: center"><strong>https://app.technofarm.bg</strong></p>Натискайки ОК ще бъдете пренасочен автоматично. <br>За допълнителна помощ се свържете с центъра за обслужване на клиенти.',
                icon: 'info',
                fn: function(){
                    window.location.href = "https://app.technofarm.bg";
                }
            });
            return;
        }
        hideFrameParent();
        ga('set', 'userId', loginData.user_id);
        heap.identify(loginData.name);
        heap.addUserProperties ({
            'FullName':loginData.fullname,
            'GAP': loginData.year,
            'Email': loginData.email
        });

        location.replace(loginData.redirect_url);
    })
    .fail(function (errorObj) {
        if (errorObj.is(TF.Rpc.ExceptionsList.WRONG_USERNAME_OR_PASSWORD) || errorObj.is(TF.Rpc.ExceptionsList.ACCOUNT_IS_NOT_ACTIVE)) {
            jQuery.messager.alert('Warning',errorObj.getMessage(),'info');
        }
    });
}
function hideFrameParent(){

    this.parent.hideFrame && this.parent.hideFrame();
    return false;
}

function validateForm()
{
    var res = jQuery('form').form('validate');
    return res;
}

function validateNewPassword() {
	if(jQuery('#new-password').validatebox('isValid')
			&& jQuery('#re-password').validatebox('isValid')
			&& jQuery('#new-password').val() == jQuery('#re-password').val()) {
		return true;
	} else {
		return false;
	}
}
