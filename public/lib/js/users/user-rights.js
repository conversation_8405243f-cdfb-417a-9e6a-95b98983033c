
var hasMapRightsR = false;
var hasMapRightsRW = false;
var hasPlotRightsR = false;
var hasPlotRightsRW = false;
var hasHypothecsRightsR = false;
var hasHypothecsRightsRW = false;
var hasContractsOwnWriteRights = false;
var hasEquityRights = false;
var hasSubsidyRights = false;
var hasSubsidyRightsRW = false;
var hasAgroRights = false;
var hasAgroRightsRW = false;
var hasSalesContractsRightsR = false;
var hasSalesContractsRightsRW = false;
var hasCollectionsRights = false;
var hasCollectionsRightsRW = false;
var hasGeoscanMapRights = false;
var hasThematicMapsRightsR = false;
var hasThematicMapsRightsRW = false;
var hasDashboardRights = false;
var hasWarehouseRights = false;
var hasKvsCuttingRights = false;
var isSuperAdmin = false;
var userLevel = 0;
var originalUserLevel = 0;

function setUserRights() {
    return new Promise((res, rej) => {
        TF.Rpc.Users.UsersMainGrid.setUserRights()
            .done(function (data) {
                hasMapRightsR = data.has_map_rights;
                hasMapRightsRW = data.has_map_rights_rw;

                hasPlotRightsR = data.has_plot_rights;
                hasPlotRightsRW = data.has_plot_rights_rw;

                hasHypothecsRightsR = data.has_hypothecs_rights_r;
                hasHypothecsRightsRW = data.has_hypothecs_rights_rw;

                hasContractsOwnWriteRights = data.has_contracts_own_write_rights;

                hasEquityRights = data.has_equity_rights;

                hasSubsidyRights = data.has_subsidy_rights;
                hasSubsidyRightsRW = data.has_subsidy_rights_rw;

                hasAgroRights = data.has_agro_rights;
                hasAgroRightsRW = data.has_agro_rights_rw;

                hasSalesContractsRightsR = data.has_sales_contracts_rights_r;
                hasSalesContractsRightsRW = data.has_sales_contracts_rights_rw;

                hasThematicMapsRightsR = data.has_thematic_maps_rights;
                hasThematicMapsRightsRW = data.has_thematic_maps_rights_rw;

                hasCollectionsRights = data.has_collections_rights;
                hasCollectionsRightsRW = data.has_collections_rights_rw;

                hasGeoscanMapRights = data.has_geoscan_map_rights;

                hasDashboardRights = data.has_dashboard_rights;

                hasWarehouseRights = data.has_warehouse_rights;
                hasWarehouseAdminRights = data.has_warehouse_admin_rights;
                HasWarehouseEditorRights = data.has_warehouse_editor_rights;

                hasKvsCuttingRights = data.has_kvs_cutting_rights;
                hasExportMassPaymentRights = data.has_kvs_cutting_rights;

                hasSlopeRights = data.has_slope_rights;
                hasCadastreRights = data.has_cadastre_rights;


                isSuperAdmin = data.is_superadmin;


                userLevel = data.user_level;
                originalUserLevel = data.original_user_level;

                setUserLastLogin();
                res(data);
            })
            .fail(rej);
    })
}


function messagerContractsOwnWriteRights() {
    
    jQuery.messager.alert('Грешка', 'Нямате права да оперирате с Договори от тип:  Собственост!', 'warning');
}

function messagerPlotsWriteRights() {
    
    jQuery.messager.alert('Грешка', 'Нямате права да правите промени в модул Имоти', 'warning');
}

function messagerMapWriteRights() {
    
    jQuery.messager.alert('Грешка', 'Нямате права да правите промени в модул Карта', 'warning');
}

function messagerThematicMapsRights() {
    jQuery.messager.alert('Грешка', 'Нямате права да правите промени в подмодул Тематични карти', 'warning');
}

function messagerCollectionsWriteRights() {
    jQuery.messager.alert('Грешка', 'Нямате права да правите промени в модул Вземания', 'warning');
}

function messagerCollectionsPaymentsRights() {
    jQuery.messager.alert('Грешка', 'Нямате право да добавяте плащане по неактивен договор', 'warning');
}

function setUserLastLogin () {
    // var userid = userid || userID;
    TF.Rpc.Users.UsersMainGrid.getLastLoginInfo(userid || userID)
    .done(function (data) {
        var lastLoginTooltip = jQuery('#last-login-tooltip');
        lastLoginTooltip.on('click', function (e) {
            e.preventDefault();
        });
        if (!(data.last_login_ip && data.last_login_date))
        {
            lastLoginTooltip.hide();
            return;
        }
        var content = 'Последно влизане от <br /> IP: '
                    + data.last_login_ip 
                    + '<br /> на ' + data.last_login_date;

        lastLoginTooltip.tooltip('update', content);
    });
}