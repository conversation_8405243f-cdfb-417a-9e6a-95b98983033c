Namespace('TF.Rpc.Users');

function initUsersSearch() {
    jQuery('#btn-search').bind('click', function(){
        jQuery('#users-tables').datagrid({
            pageNumber: 1,
            rpcParams: [{
                username: j<PERSON><PERSON><PERSON>('#search-username').val(),
                email: jQ<PERSON><PERSON>('#search-email').val(),
                paid_support_start_date: jQuery('#search-paid-support-start-date').datebox('getValue'),
                paid_support_due_date: jQuery('#search-paid-support-due-date').datebox('getValue'),
                name: jQ<PERSON><PERSON>('#search-names').val(),
                modules: jQ<PERSON>y('#search-modules').combobox('getValues'),
                sales_person: jQuery('#account-sales-person').combobox('getValues'),
                active: jQuery('#search-status').combobox('getValue'),
                is_trial: jQuery('#search-trial').combobox('getValue'),
                parent_account: jQ<PERSON>y('#search-parent-account').val(),
                start_date: jQ<PERSON>y('#search-start-date').datebox('getValue'),
                due_date: jQuery('#search-due-date').datebox('getValue'),
                creation_date_from: jQuery('#search-created-date-from').datebox('getValue'),
                creation_date_to: jQuery('#search-created-date-to').datebox('getValue'),
                usb_modem: jQuery('#search-usb-modem').val(),
                user_role: jQuery('#search-user-roles').combobox('getValue')
            }]
        });

        return false;
    });

    jQuery('#btn-clear').bind('click', function(){
        jQuery('#search-username').val('');
        jQuery('#search-email').val('');
        jQuery('#search-names').val('');
        jQuery('#search-modules').combobox('clear');
        jQuery('#account-sales-person').combobox('clear');
        jQuery('#search-status').combobox('reset');
        jQuery('#search-user-roles').combobox('clear');
        jQuery('#search-trial').combobox('reset');
        jQuery('#search-start-date').datebox('clear');
        jQuery('#search-due-date').datebox('clear');
        jQuery('#search-created-date-from').datebox('clear');
        jQuery('#search-created-date-to').datebox('clear');
        jQuery('#search-parent-account').val('');
        jQuery('#search-paid-support-start-date').datebox('clear');
        jQuery('#search-paid-support-due-date').datebox('clear');

        jQuery('#users-tables').datagrid({
            pageNumber: 1,
            rpcParams: [{
                username: jQuery('#search-username').val(),
                email: jQuery('#search-email').val(),
                paid_support_start_date: jQuery('#search-paid-support-start-date').datebox('getValue'),
                paid_support_due_date: jQuery('#search-paid-support-due-date').datebox('getValue'),
                name: jQuery('#search-names').val(),
                modules: jQuery('#search-modules').combobox('getValue'),
                sales_person: jQuery('#account-sales-person').combobox('getValues'),
                active: jQuery('#search-status').combobox('getValue'),
                is_trial: jQuery('#search-trial').combobox('getValue'),
                parent_account: jQuery('#search-parent-account').val(),
                start_date: jQuery('#search-start-date').datebox('getValue'),
                due_date: jQuery('#search-due-date').datebox('getValue'),
                creation_date_from: jQuery('#search-created-date-from').datebox('getValue'),
                creation_date_to: jQuery('#search-created-date-to').datebox('getValue'),
                user_role: jQuery('#search-user-roles').combobox('getValue'),

            }]
        });

        return false;
    });

    jQuery('#search-modules').combobox({
        url: 'index.php?common-rpc=tf-modules-combobox',
        valueField: 'id',
        textField: 'name',
        rpcParams: [{}],
        multiple: true,
        editable: true,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#account-sales-person').combobox({
        url: 'index.php?common-rpc=salesman-list',
        valueField: 'id',
        textField: 'name',
        multiple: true,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
    });
    
    jQuery('#search-user-roles').combobox({
        url: 'index.php?common-rpc=roles-list-full',
        valueField: 'id',
        textField: 'name',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
    });

    jQuery('#search-status').combobox({
        data: [
            {
                value: '',
                text: 'Всички',
                selected: true,
            }, {
                value: 'false',
                text: 'Неактивен'
            }, {
                value: 'true',
                text: 'Активен'
            }
        ],
        valueField: 'value',
        textField: 'text',
    });

    jQuery('#search-trial').combobox({
        data: [
            {
                value: '',
                text: 'Всички',
                selected: true,
            }, {
                value: 'false',
                text: 'Не'
            }, {
                value: 'true',
                text: 'Да'
            }
        ],
        valueField: 'value',
        textField: 'text',
    });

    jQuery('#search-start-date').datebox({
        value: '',
        buttons: dateboxWithClearButton
    });

    jQuery('#search-due-date').datebox({
        value: '',
        buttons: dateboxWithClearButton
    });

    jQuery('#search-created-date-from').datebox({
        value: '',
        buttons: dateboxWithClearButton
    });

    jQuery('#search-created-date-to').datebox({
        value: '',
        buttons: dateboxWithClearButton
    });

    jQuery('#search-paid-support-start-date').datebox({
        value: '',
        buttons: dateboxWithClearButton
    });

    jQuery('#search-paid-support-due-date').datebox({
        value: '',
        buttons: dateboxWithClearButton
    });

    initSearchUsername();
    initSearchParentAccout();
    initSearchByClientName();
    initSearchByEmail();

    jQuery('#add-ekatte-button > a').linkbutton({iconCls: 'icon-save'});
    jQuery('#addbutton > a').linkbutton({iconCls: 'icon-ok'});
    jQuery('#editbutton > a').linkbutton({iconCls: 'icon-ok'});
    jQuery('#canclebutton > a').linkbutton({iconCls: 'icon-cancel'});
}

function initSearchUsername() {
    var component = jQuery('#search-username'),
        componentTarget = jQuery('#search-username-typehead'),
        completer = new Bloodhound({
            datumTokenizer: Bloodhound.tokenizers.whitespace('value'),
            queryTokenizer: Bloodhound.tokenizers.whitespace,
            remote: {
                url: 'index.php?users-rpc=users',
                cache: false,
                prepare: function (query, settings) {
                    settings.query = query;
                    return settings;
                },
                transport: function (settings, onSuccess, onError) {
                    var options = {
                        type: 'POST',
                        dataType: 'json',
                        contentType: "application/json; charset=UTF-8",
                        data: JSON.stringify({
                            "method": "getUsernames",
                            "params": [settings.query],
                            "id": 1,
                            "jsonrpc": "2.0"
                        })
                    };

                    jQuery.ajax('index.php?users-rpc=users', options)
                        .done(done)
                        .fail(fail)
                        .always(always);

                    function done(data, textStatus, request) {
                        onSuccess(data.result);
                    }

                    function fail(request, textStatus, errorThrown) {
                        onError(errorThrown);
                    }

                    function always() {
                    }
                },
            },
        });

    component.typeahead({
        hint: false,
        highlight: false,
        minLength: 1,
        limit: 10,
        menu: componentTarget
    }, {
        name: 'completer',
        displayKey: 'username',
        source: completer.ttAdapter(),
    });

    component.on('typeahead:open', function (e, datum) {
        componentTarget.width(component.width());
        var offset = component.offset();
        componentTarget.offset({
            top: offset.top + component.height(),
            left: offset.left
        })
        componentTarget.css("z-index",9999);
    });

    component.on('typeahead:selected', function (e, datum) {
        component.typeahead('close');
        componentTarget.css("z-index",-1);
    });
    component.on('typeahead:idle', function (e, datum) {
        component.typeahead('close');
        componentTarget.css("z-index",-1);
    });
}

function initSearchParentAccout() {
    var component = jQuery('#search-parent-account'),
        componentTarget = jQuery('#search-parent-account-typehead'),
        completer = new Bloodhound({
            datumTokenizer: Bloodhound.tokenizers.whitespace('value'),
            queryTokenizer: Bloodhound.tokenizers.whitespace,
            remote: {
                url: 'index.php?users-rpc=users',
                cache: false,
                prepare: function (query, settings) {
                    settings.query = query;
                    return settings;
                },
                transport: function (settings, onSuccess, onError) {
                    var options = {
                        type: 'POST',
                        dataType: 'json',
                        contentType: "application/json; charset=UTF-8",
                        data: JSON.stringify({
                            "method": "getUsernames",
                            "params": [settings.query],
                            "id": 1,
                            "jsonrpc": "2.0"
                        })
                    };

                    jQuery.ajax('index.php?users-rpc=users', options)
                        .done(done)
                        .fail(fail)
                        .always(always);

                    function done(data, textStatus, request) {
                        onSuccess(data.result);
                    }

                    function fail(request, textStatus, errorThrown) {
                        onError(errorThrown);
                    }

                    function always() {
                    }
                },
            },
        });

    component.typeahead({
        hint: false,
        highlight: false,
        minLength: 1,
        limit: 10,
        menu: componentTarget
    }, {
        name: 'completer',
        displayKey: 'username',
        source: completer.ttAdapter(),
    });

    component.on('typeahead:open', function (e, datum) {
        componentTarget.width(component.width());
        var offset = component.offset();
        componentTarget.offset({
            top: offset.top + component.height(),
            left: offset.left
        })
        componentTarget.css("z-index",9999);
    });

    component.on('typeahead:selected', function (e, datum) {
        component.typeahead('close');
        componentTarget.css("z-index",-1);
    });
    component.on('typeahead:idle', function (e, datum) {
        component.typeahead('close');
        componentTarget.css("z-index",-1);
    });
}

function initSearchByClientName() {
    var component = jQuery('#search-names'),
        componentTarget = jQuery('#search-names-typehead'),
        completer = new Bloodhound({
            datumTokenizer: Bloodhound.tokenizers.whitespace('value'),
            queryTokenizer: Bloodhound.tokenizers.whitespace,
            remote: {
                url: 'index.php?users-rpc=users',
                cache: false,
                prepare: function (query, settings) {
                    settings.query = query;
                    return settings;
                },
                transport: function (settings, onSuccess, onError) {
                    var options = {
                        type: 'POST',
                        dataType: 'json',
                        contentType: "application/json; charset=UTF-8",
                        data: JSON.stringify({
                            "method": "getNames",
                            "params": [settings.query],
                            "id": 1,
                            "jsonrpc": "2.0"
                        })
                    };

                    jQuery.ajax('index.php?users-rpc=users', options)
                        .done(done)
                        .fail(fail)
                        .always(always);

                    function done(data, textStatus, request) {
                        onSuccess(data.result);
                    }

                    function fail(request, textStatus, errorThrown) {
                        onError(errorThrown);
                    }

                    function always() {
                    }
                },
            },
        });

    component.typeahead({
        hint: false,
        highlight: false,
        minLength: 1,
        limit: 10,
        menu: componentTarget
    }, {
        name: 'completer',
        displayKey: 'name',
        source: completer.ttAdapter(),
    });

    component.on('typeahead:open', function (e, datum) {
        componentTarget.width(component.width());
        var offset = component.offset();
        componentTarget.offset({
            top: offset.top + component.height(),
            left: offset.left
        })
        componentTarget.css("z-index",9999);
    });

    component.on('typeahead:selected', function (e, datum) {
        component.typeahead('close');
        componentTarget.css("z-index",-1);
    });
    component.on('typeahead:idle', function (e, datum) {
        component.typeahead('close');
        componentTarget.css("z-index",-1);
    });
}

function initSearchByEmail() {
    var component = jQuery('#search-email'),
        componentTarget = jQuery('#search-email-typehead'),
        completer = new Bloodhound({
            datumTokenizer: Bloodhound.tokenizers.whitespace('value'),
            queryTokenizer: Bloodhound.tokenizers.whitespace,
            remote: {
                url: 'index.php?users-rpc=users',
                cache: false,
                prepare: function (query, settings) {
                    settings.query = query;
                    return settings;
                },
                transport: function (settings, onSuccess, onError) {
                    var options = {
                        type: 'POST',
                        dataType: 'json',
                        contentType: "application/json; charset=UTF-8",
                        data: JSON.stringify({
                            "method": "getEmails",
                            "params": [settings.query],
                            "id": 1,
                            "jsonrpc": "2.0"
                        })
                    };

                    jQuery.ajax('index.php?users-rpc=users', options)
                        .done(done)
                        .fail(fail)
                        .always(always);

                    function done(data, textStatus, request) {
                        onSuccess(data.result);
                    }

                    function fail(request, textStatus, errorThrown) {
                        onError(errorThrown);
                    }

                    function always() {
                    }
                },
            },
        });

    component.typeahead({
        hint: false,
        highlight: false,
        minLength: 1,
        limit: 10,
        menu: componentTarget
    }, {
        name: 'completer',
        displayKey: 'email',
        source: completer.ttAdapter(),
    });

    component.on('typeahead:open', function (e, datum) {
        componentTarget.width(component.width());
        var offset = component.offset();
        componentTarget.offset({
            top: offset.top + component.height(),
            left: offset.left
        })
        componentTarget.css("z-index",9999);
    });

    component.on('typeahead:selected', function (e, datum) {
        component.typeahead('close');
        componentTarget.css("z-index",-1);
    });
    component.on('typeahead:idle', function (e, datum) {
        component.typeahead('close');
        componentTarget.css("z-index",-1);
    });
}

function setValidateSearchInput(){
    jQuery.extend(jQuery.fn.validatebox.defaults.rules, {
        minLength: {
            validator: function(value, param){
                return value.length >= param[0];
            },
            message: 'Въведете минимум {0} символа.'
        }
    });

    jQuery('#search-username').validatebox({
        validType:'minLength[3]'
    });

    jQuery('#search-username').keypress(function(event) {
        if ( event.which == 13 ) {
            jQuery('#users-tables').datagrid({
                rpcParams: [{
                    username: jQuery('#search-username').val(),
                    email: jQuery('#search-email').val()
                }]
            });
        }
    });

    jQuery('#search-email').validatebox({
        invalidMessage: 'Въведете валиден email адрес!'
    });

    jQuery('#search-email').keypress(function(event) {
        if ( event.which == 13 ) {
            jQuery('#users-tables').datagrid({
                rpcParams: [{
                    username: jQuery('#search-username').val(),
                    email: jQuery('#search-email').val()
                }]
            });
        }
    });
}
