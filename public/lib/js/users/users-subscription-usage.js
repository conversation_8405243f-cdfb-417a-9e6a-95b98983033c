let subscriptionUsages = [];

const initSubscriptionUsages = () =>
    new Promise((res, rej) => {
        TF.Rpc.Users.UsersMainGrid.getAllSubscriptions()
            .done(data => {
                const types = Object.keys(data);
                types.map(t => {
                    switch (t) {
                        case SUBSCRIPTION_MAP: {
                            subscriptionUsages[t] = data[t].map(m => {
                                return {
                                    ...m,
                                    label: m.max_plots !== null ? `${m.min_plots}-${m.max_plots} землища` : `над ${m.min_plots} землища`,
                                };
                            });
                            break;
                        }
                        case SUBSCRIPTION_PLOTS: {
                            subscriptionUsages[t] = data[t].map(p => {
                                return {
                                    ...p,
                                    label: p.max_area_dka !== null ? `от ${p.min_area_dka} до ${p.max_area_dka} декари` : `над ${p.min_area_dka} декари`,
                                };
                            });
                            break;
                        }
                        case SUBSCRIPTION_AGRO: {
                            subscriptionUsages[t] = data[t].map(a => {
                                return {
                                    ...a,
                                    label: a.with_farmtrack ? 'с FarmTrack' : 'без FarmTrack',
                                };
                            });
                            break;
                        }
                        default: break;
                    }
                });

                res(subscriptionUsages);
            })
            .fail(rej);
    });

const initSubscriptionUsageFields = () => {
    jQuery('#subscription-usage-map > input').combobox({
        valueField: 'id',
        textField: 'label',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
        data: subscriptionUsages[SUBSCRIPTION_MAP],
    });

    jQuery('#subscription-usage-plots > input').combobox({
        valueField: 'id',
        textField: 'label',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
        data: subscriptionUsages[SUBSCRIPTION_PLOTS],
    });

    jQuery('#subscription-usage-agro > input').combobox({
        valueField: 'id',
        textField: 'label',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
        data: subscriptionUsages[SUBSCRIPTION_AGRO],
    });
};

const resetSubscriptionUsageFields = () => {
    jQuery('#subscription-usage-map > input').combobox('reset');
    jQuery('#subscription-usage-plots > input').combobox('reset');
    jQuery('#subscription-usage-agro > input').combobox('reset');
};

const populateSubscriptionUsageFields = userData => {
    jQuery('#subscription-usage-map > input').combobox('select', userData.subscription_usage_map);
    jQuery('#subscription-usage-plots > input').combobox('select', userData.subscription_usage_plots);
    jQuery('#subscription-usage-agro > input').combobox('select', userData.subscription_usage_agro);
};

const getSubscriptionUsageValues = () => {
    const subscriptions = {};

    const map = jQuery('#subscription-usage-map > input').combobox('getValue');
    const plots = jQuery('#subscription-usage-plots > input').combobox('getValue');
    const agro = jQuery('#subscription-usage-agro > input').combobox('getValue');

    if (!map && !plots && !agro) {
        return null;
    }

    if (map) {
        subscriptions[SUBSCRIPTION_MAP] = map;
    }
    if (plots) {
        subscriptions[SUBSCRIPTION_PLOTS] = plots;
    }
    if (agro) {
        subscriptions[SUBSCRIPTION_AGRO] = agro;
    }

    return subscriptions;
};
