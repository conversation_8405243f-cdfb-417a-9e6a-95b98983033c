Namespace('TF.Rpc.Users');

var requestGetData;
var requestAddData;
var editUserID;
var userUsername;
var userPassword;
var userEmail;
var userName;

var hasMapRightsR = false;
var hasMapRightsRW = false;
var hasPlotRightsR = false;
var hasPlotRightsRW = false;
var hasHypothecsRightsR = false;
var hasHypothecsRightsRW = false;
var hasContractsOwnWriteRights = false;
var hasEquityRights = false;
var hasSubsidyRights = false;
var hasSubsidyRightsRW = false;
var hasAgroRights = false;
var hasAgroRightsRW = false;
var hasSalesContractsRightsR = false;
var hasSalesContractsRightsRW = false;
var hasCollectionsRights = false;
var hasCollectionsRightsRW = false;
var hasGeoscanMapRights = false;
var isSuperAdmin = false;
var hasThematicMapsRightsR = false;
var hasThematicMapsRightsRW = false;

var hasSlopeRights = false;
var hasCadastreRights = false;

jQuery(function(){
    setUserRights()
        .finally(initSubscriptionUsages)
        .finally(initUsersGrid);
    initUsersSearch();
});

function setValidateInputs(isEdit){
    if(isEdit){
        jQuery('#username > input').validatebox({
            novalidate:true
        });

        jQuery('#password > input').validatebox({
            novalidate:true
        });
        jQuery('#email > input').validatebox({
            novalidate:false,
            required: true,
            validType: 'email',
            missingMessage: 'Въведете email! (пример: <EMAIL>)',
        });

        jQuery('#name > input').validatebox({
            novalidate:false,
            required: true,
            missingMessage: 'Въведете име! (пример: Име Презиме Фамилия)'
        });
    } else {
        jQuery('#username > input').validatebox({
            novalidate:false,
            required: true,
            missingMessage: 'Въведете потребителско име!'
        });

        jQuery('#password > input').validatebox({
            novalidate:false,
            required: true,
            missingMessage: 'Въведете парола!'
        });
        jQuery('#email > input').validatebox({
            required: true,
            novalidate:false,
            validType: 'email',
            missingMessage: 'Въведете email! (пример: <EMAIL>)',
        });

        jQuery('#name > input').validatebox({
            novalidate:false,
            required: true,
            missingMessage: 'Въведете име! (пример: Име Презиме Фамилия)'
        });
    }

}


function setUserRights() {

    TF.Rpc.Users.UsersMainGrid.setUserRights()
    .done(function (data) {
        hasMapRightsR = data.has_map_rights;
        hasMapRightsRW = data.has_map_rights_rw;

        hasPlotRightsR = data.has_plot_rights;
        hasPlotRightsRW = data.has_plot_rights_rw;

        hasHypothecsRightsR = data.has_hypothecs_rights_r;
        hasHypothecsRightsRW = data.has_hypothecs_rights_rw;
        
        hasContractsOwnWriteRights = data.has_contracts_own_write_rights;

        hasEquityRights = data.has_equity_rights;

        hasSubsidyRights = data.has_subsidy_rights;
        hasSubsidyRightsRW = data.has_subsidy_rights_rw;

        hasAgroRights = data.has_agro_rights;
        hasAgroRightsRW = data.has_agro_rights_rw;
        
        hasSalesContractsRightsR = data.has_sales_contracts_rights_r;
        hasSalesContractsRightsRW = data.has_sales_contracts_rights_rw;

        hasCollectionsRights = data.has_collections_rights;
        hasCollectionsRightsRW = data.has_collections_rights_rw;
        
        hasGeoscanMapRights = data.has_geoscan_map_rights;
        isSuperAdmin = data.is_superadmin;
    });
}