function initUserDevices(curentUserId){
    var userDeviceList = jQuery('#device-user-tables');
    var allDeviceList = jQuery('#device-tables');
    allDeviceList.datalist({
		fit: true,
        checkbox:true,
        height: "350",
        autoRowHeight: true,
        fitColumns: true,
        singleSelect: false,
        url:'index.php?users-rpc=users',
        rpcMethod:  "allModems",
        rpcParams: [{
        }],
        pagination: true,
        border: false,
        textField: 'name',
        valueField:'serial',
        toolbar: [{
            id: 'btn_add_devices',
            text: 'Добави устройства',
            class: 'west',
			iconCls: 'icon-add',
            handler: function() {
				var devices = allDeviceList.datalist("getChecked") || [];
				if(!devices.length) {
					jQuery.messager.alert("Грешка", "Моля изберете устройство.", "warning");
					return;
				}
                var selected = {
					data: devices,
					id: curentUserId
				};
				TF.Rpc.Users.UsersMainGrid.addUserModems(
					selected,
					curentUserId
				).done(function (rowData) {
					allDeviceList.datalist('uncheckAll');
					allDeviceList.datalist('loadRpc');
					userDeviceList.datagrid('loadRpc');
				});
            }
        }
        ],
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
	jQuery('#device-tables').datagrid('getPager')
		.pagination({
			layout: ['first', 'list', 'last', 'refresh'],
			showPageInfo: false,
			displayMsg: ''
		});

    userDeviceList.datagrid({
      autoRowHeight: true,
      autoRowWidth: true,
      fitColumns: true,
      url: "index.php?users-rpc=users",
      fit: true,
      rpcMethod: "modems",
      singleSelect: true,
      pagination: true,
      idField: "id",
      border: false,
      rpcParams: [
        {
          user_id: curentUserId
        }
      ],
      columns: [
        [
          {
            field: "machine",
            title: "<b>Устройство</b>",
            sortable: true,
            width: 220
          },
          {
            field: "date",
            title: "<b>Дата на редакция</b>",
            sortable: true,
            width: 150
          },
          {
            field: "status",
            title: "<b>Статус</b>",
            sortable: true,
            width: 80
          },
          {
            field: "id",
            title: "<b>Премахване</b>",
            sortable: false,
            width: 70,
            align: "center",
            formatter: function(val, row) {
              return (
                '<span href="javascript:void(0)" onclick="removeDevice(' +
                row.id +
                ')" class="l-btn-left l-btn-icon-left"><span class="l-btn-text l-btn-empty" >&nbsp;</span><span class="l-btn-icon icon-delete">&nbsp;</span></span>'
              );
            }
          }
        ]
      ],
      onLoadSuccess: function() {},
      loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
      loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    userDeviceList.datagrid().find('.easyui-linkbutton').each(function(){
        alert($(this).attr('row-id'));
    });
}

function removeDevice(id){
    jQuery.messager.confirm('Потвърждение', 'Искате ли да премахнете това устройство?', function(r) {
       if(r){
           TF.Rpc.Users.UsersMainGrid.removeUserModem(id).done(function (rowData) {
               jQuery('#device-user-tables').datalist('loadRpc');
           });
       }
    });
}
function formatDetail(value,row){

}
function removeLink(){
}
