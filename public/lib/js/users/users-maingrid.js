Namespace('TF.Rpc.Users');
var UR_TAB_BASIC_INFO = 'Основна информация';
var UR_TAB_ADDITIONAL_INFO = 'Допълнителна информация';
var UR_TAB_MODULES_RIGHT = 'Модули Права';

function toolbarConfig(userLevel) {
    var usersRightsTabs = jQuery('#users-rights-tabs');

    if([USERS_SALES_FLAG, USERS_SUPPORT_FLAG].includes(userLevel)) {
        return [{
            id: 'btn_export_excel',
            text: 'Експорт(xls)',
            iconCls: 'icon-csv',
            handler: function() {
                var filters = getUsersFilters();
                TF.Rpc.Users.UsersMainGrid.exportToExcelUsersData(filters)
                    .done(function (dataObj){
                        jQuery('#win-download').window('open');
                        jQuery('#btn-download-file').attr("href", dataObj.file_path);
                    })
                    .fail(function (errorObj) {});
            }
        },{
            id: 'btnedit',
            text: 'Редактиране',
            iconCls: 'icon-edit',
            handler: function() {
                var getSelected = getCheckedUsers(true);
                var selected = getSelected.length ? getSelected[0] : null;
                if (selected) {
                    TF.Rpc.Users.UsersMainGrid.markForEdit(selected.id)
                        .done(function (data) {
                            addEditFieldsDisplay(false);
                            resetAddEditFields();
                            jQuery('#btn-add-new-user-to-grid').hide();
                            jQuery('#btn-edit-selected-user').show();
                            jQuery('#win-add').window('open');
                            setEditUserFieldsData(data);
                            setValidateInputs(true);
                            if(isSuperAdmin){
                                initUserDevices(data.userInfo.id);
                            }
                        })
                        .fail(function (errorObj) {
                            var errorMsg = errorObj && errorObj.getMessage() || 'Моля опитайте по-късно';
                            jQuery.messager.alert('Грешка', errorMsg, 'warning');
                            jQuery('#users-tables').datagrid('uncheckAll');
                            jQuery('#users-tables').datagrid('loadRpc');
                        });
                } else {
                    jQuery.messager.alert('Грешка', 'Не е избран потребител!');
                }
            }
        }, {
            id: 'btnsetaccoutfrozen',
            text: 'Активиране/Деактивиране',
            iconCls: 'icon-cancel',
            handler: function() {
                var getChecked = getCheckedUsers(true);
                var checked = getChecked.length ? getChecked[0] : null;
                if (checked) {
                    var message = `Сигурни ли сте че искате да ${checked.active_status ? 'деактивирате' : 'активирате'} акаунт ${checked.username}`;

                    jQuery.messager.confirm('Потвърждение', message, function(r) {
                        if (r) {
                            var obj = {
                                user_level: checked.user_level,
                                user_id: checked.id,
                                group_id: checked.group_id,
                                active: checked.active_status,
                            };

                            TF.Rpc.Users.UsersMainGrid.changeActiveStatus(obj)
                                .done(function (data) {
                                    jQuery.messager.alert('Успешна промяна', 'Успешна промяна на статуса на потребител '+ checked.username+'.');
                                    jQuery('#users-tables').datagrid('uncheckAll');
                                    jQuery('#users-tables').datagrid('loadRpc');
                                })
                                .fail(function (errorObj) {
                                    jQuery.messager.alert('Грешка', errorObj.getMessage(),'warning');
                                });
                        }
                    });
                } else {
                    jQuery.messager.alert('Грешка', 'Не е избран потребител.');
                }
            }
        }];
    }

    return [{
        id: 'btnadd',
        text: 'Добавяне',
        iconCls: 'icon-add',
        handler: function() {
            addEditFieldsDisplay(true);
            resetAddEditFields();
            jQuery('#username > input').attr("disabled", false);
            jQuery('#btn-add-new-user-to-grid').show();
            jQuery('#btn-edit-selected-user').hide();
            jQuery('#win-add').window('open');
            usersRightsTabs.tabs('select', UR_TAB_BASIC_INFO);
            setValidateInputs(false);
        }
    }, {
        id: 'btndel',
        text: 'Изтриване',
        iconCls: 'icon-remove',
        handler: function() {
            var getChecked = getCheckedUsers();
            if (getChecked && getChecked.length) {
                jQuery.messager.confirm('Потвърждение', 'Това действие е необратимо! Сигурни ли сте, че искате да премахнете избраните потребители?', function(r) {
                    if (r) {
                        var userIDS = getChecked.map(user => user.id);
                        TF.Rpc.Users.UsersMainGrid.deleteSelectedUsers(userIDS)
                            .done(function (data) {
                                jQuery.messager.alert('Успешно изтриване','Успешно изтриване');
                                jQuery('#users-tables').datagrid('uncheckAll');
                                jQuery('#users-tables').datagrid('loadRpc');
                            })
                            .fail(function (errorObj) {
                                jQuery.messager.alert('Грешка', errorObj.getMessage(),'warning');
                            });
                    }
                });
            } else {
                jQuery.messager.alert('Грешка', 'Моля изберете потребител.');
            }
        }
    },
        {
            id: 'btnedit',
            text: 'Редактиране',
            iconCls: 'icon-edit',
            handler: function() {
                var getSelected = getCheckedUsers(true);
                var selected = getSelected.length ? getSelected[0] : null;
                if (selected) {
                    TF.Rpc.Users.UsersMainGrid.markForEdit(selected.id)
                        .done(function (data) {
                            addEditFieldsDisplay(false);
                            resetAddEditFields();
                            jQuery('#btn-add-new-user-to-grid').hide();
                            jQuery('#btn-edit-selected-user').show();
                            jQuery('#win-add').window('open');
                            setEditUserFieldsData(data);
                            setValidateInputs(true);
                            if(isSuperAdmin){
                                initUserDevices(data.userInfo.id);
                            }
                        })
                        .fail(function (errorObj) {
                            var errorMsg = errorObj && errorObj.getMessage() || 'Моля опитайте по-късно';
                            jQuery.messager.alert('Грешка', errorMsg, 'warning');
                            jQuery('#users-tables').datagrid('uncheckAll');
                            jQuery('#users-tables').datagrid('loadRpc');
                        });
                } else {
                    jQuery.messager.alert('Грешка', 'Не е избран потребител!');
                }
            }
        }, {
            id: 'btnsetaccoutfrozen',
            text: 'Активиране/Деактивиране',
            iconCls: 'icon-cancel',
            handler: function() {
                var getChecked = getCheckedUsers(true);
                var checked = getChecked.length ? getChecked[0] : null;
                if (checked) {
                    var message = `Сигурни ли сте че искате да ${checked.active_status ? 'деактивирате' : 'активирате'} акаунт ${checked.username}`;

                    jQuery.messager.confirm('Потвърждение', message, function(r) {
                        if (r) {
                            var obj = {
                                user_level: checked.user_level,
                                user_id: checked.id,
                                group_id: checked.group_id,
                                active: checked.active_status,
                            };

                            TF.Rpc.Users.UsersMainGrid.changeActiveStatus(obj)
                                .done(function (data) {
                                    jQuery.messager.alert('Успешна промяна', 'Успешна промяна на статуса на потребител '+ checked.username+'.');
                                    jQuery('#users-tables').datagrid('uncheckAll');
                                    jQuery('#users-tables').datagrid('loadRpc');
                                })
                                .fail(function (errorObj) {
                                    jQuery.messager.alert('Грешка', errorObj.getMessage(),'warning');
                                });

                        }
                    });
                } else {
                    jQuery.messager.alert('Грешка', 'Не е избран потребител.');
                }
            }
        }, {
            id: 'btn_export_excel',
            text: 'Експорт(xls)',
            iconCls: 'icon-csv',
            handler: function() {
                var filters = getUsersFilters();
                TF.Rpc.Users.UsersMainGrid.exportToExcelUsersData(filters)
                    .done(function (dataObj){
                        jQuery('#win-download').window('open');
                        jQuery('#btn-download-file').attr("href", dataObj.file_path);
                    })
                    .fail(function (errorObj) {});
            }
        }];
}

function getUsersFilters() {
    return {
        username: jQuery('#search-username').val(),
        email: jQuery('#search-email').val(),
        paid_support_start_date: jQuery('#search-paid-support-start-date').datebox('getValue'),
        paid_support_due_date: jQuery('#search-paid-support-due-date').datebox('getValue'),
        name: jQuery('#search-names').val(),
        modules: jQuery('#search-modules').combobox('getValues'),
        sales_person: jQuery('#account-sales-person').combobox('getValues'),
        active: jQuery('#search-status').combobox('getValue'),
        is_trial: jQuery('#search-trial').combobox('getValue'),
        parent_account: jQuery('#search-parent-account').val(),
        start_date: jQuery('#search-start-date').datebox('getValue'),
        due_date: jQuery('#search-due-date').datebox('getValue'),
        creation_date_from: jQuery('#search-created-date-from').datebox('getValue'),
        creation_date_to: jQuery('#search-created-date-to').datebox('getValue')
    }
}

function initUsersGrid() {
    var viewAsLevel = originalUserLevel != null && originalUserLevel !== userLevel ? originalUserLevel : userLevel;
	var userLevelNum = parseInt(viewAsLevel); // userLevel is received as a string here.

    if ([USERS_SUPER_ADMIN_FLAG, USERS_SUPPORT_FLAG, USERS_SALES_FLAG].includes(userLevelNum)) {
        jQuery("div[id*='admin-search-fields']").each(function (i, el) {
            el.show();
        });
    } else {
        jQuery("div[id*='admin-search-fields']").each(function (i, el) {
            el.hide();
        });
    }
	jQuery('#users-tables').datagrid({
		title: 'Потребители',
		iconCls: 'icon-users',
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		pageSize: 20,
		fit: true,
		fitColumns: true,
		showFooter: true,
		url: 'index.php?users-rpc=users',
		sortName: 'username',
		sortOrder: 'desc',
		rpcParams: [{}],
		idField: 'username',
		frozenColumns: [[
				{
					field: 'ck',
					checkbox: true
				},
			]],
		columns: columnsConfig(userLevelNum),
		pagination: true,
		rownumbers: false,
		toolbar: toolbarConfig(userLevelNum),
		onBeforeLoad: function() {
			jQuery('#users-tables').datagrid('clearChecked');
		},
		onLoadSuccess: function () {
			jQuery(this).datagrid('getPanel').find('span.login-user').linkbutton();
			jQuery(this).datagrid('getPanel').find('span.login-user').tooltip();
			jQuery(this).datagrid('fixRowHeight');

},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function rightsControl() {
	if (!hasMapRightsR) {
		jQuery('#map-rights-r').hide();
	}
	if (!hasMapRightsRW) {
		jQuery('#map-rights-rw').hide();
	}

	if (!hasThematicMapsRightsR) {
		jQuery('#thematic-maps-rights-r').hide();
	}
	if (!hasThematicMapsRightsRW) {
		jQuery('#thematic-maps-rights-rw').hide();
	}

	if (!hasPlotRightsR) {
		jQuery('#plot-rights-r').hide();
	}
	if (!hasPlotRightsRW) {
		jQuery('#plot-rights-rw').hide();
	}

    if (!hasHypothecsRightsR) {
		jQuery('#hypothecs-rights-r').hide();
	}
	if (!hasHypothecsRightsRW) {
		jQuery('#hypothecs-rights-rw').hide();
	}

	if (!hasContractsOwnWriteRights) {
		jQuery('#contracts-own-write-rights').hide();
	}

	if (!hasEquityRights) {
		jQuery('#equity-rights').hide();
	}

	if (!hasSubsidyRights) {
		jQuery('#subsidy-rights-r').hide();
	}
	if (!hasSubsidyRightsRW) {
		jQuery('#subsidy-rights-rw').hide();
	}

	if (!hasAgroRights) {
		jQuery('#agro-rights-r').hide();
	}
	if (!hasAgroRightsRW) {
		jQuery('#agro-rights-rw').hide();
	}
	if (!hasSalesContractsRightsR) {
		jQuery('#sales-contracts-rights-r').hide();
	}
	if (!hasSalesContractsRightsRW) {
		jQuery('#sales-contracts-rights-rw').hide();
	}

    if (!hasCollectionsRights) {
        jQuery('#collections-rights-r').hide();
    }
    if (!hasCollectionsRightsRW) {
        jQuery('#collections-rights-rw').hide();
    }
    if (!hasGeoscanMapRights) {
		jQuery('#geoscan-map-rights').hide();
	}
    if (!hasWarehouseRights){
        jQuery('#warehouse-rights-r').hide();
    }
    if (!hasDashboardRights) {
        jQuery('#dashboard-rights-r').hide();
    }
    if (!hasKvsCuttingRights) {
        jQuery('#kvs-cutting').hide();
    }
    if (!hasExportMassPaymentRights) {
        jQuery('#mass-payment').hide();
    }
    if (!hasSlopeRights) {
        jQuery('#slope').hide();
    }

    if (!hasCadastreRights) {
        jQuery('#cadastre').hide();
    }
}

function managePaidSupport(newUser) {
    var $psYearly = jQuery('#paid-support-for > input');
    var $psCheckbox = jQuery('#paid-support-dates-checkbox input');
    var $psStart = jQuery('#paid-support-start-date > input');
    var $psEnd = jQuery('#paid-support-due-date > input');

    //handle allowed paid support dates checks
    var enableDateBoxes = $psStart && $psEnd || $psCheckbox.is(':checked');

    $psYearly.combobox({ disabled: enableDateBoxes });
    $psStart.datebox({ disabled: !enableDateBoxes });
    $psEnd.datebox({ disabled: !enableDateBoxes });

    //handle date entries
    $psCheckbox.change(function() {
        if ($psCheckbox.is(':checked')) {
            $psYearly.combobox('disable');
            $psYearly.combobox('reset');

            $psStart.datebox('enable');
            $psStart.datebox('setValue', new Date());

            $psEnd.datebox('enable');
            $psEnd.datebox('setValue', new Date());
        } else {
            $psYearly.combobox('enable');

            $psStart.datebox('disable');
            $psStart.datebox('clear');

            $psEnd.datebox('disable');
            $psEnd.datebox('clear');
        }
    });

    // Ugly, but the alternative is uglier (separating Add and Edit) and redundant at this point.
	// TODO: If Add and Edit are ever separated, come back to this!
    jQuery('#account-server-config').css('display', newUser ? 'none' : 'table-row');
    jQuery('#paid-support-dates-checkbox-wrapper').css('display', newUser ? 'none' : 'table-row');
}

function initUserFarmings(userFarmings = null) 
{
    let userLevelNum = this.getUserLevel();

    if (userLevelNum !== USERS_ADMIN_FLAG) {
        jQuery('#user-farmings').parent().hide();
        return;
    } else {
        jQuery('#user-farmings').parent().show();
    }

    if (userFarmings === null) {
        TF.Rpc.Users.UsersMainGrid.getFarmings()
        .done(function (data) {
            return setUserFarmings(data);
        })
        .fail(function (errorObj) {
            let errorMsg = errorObj && errorObj.getMessage() || 'Моля опитайте по-късно';
            jQuery.messager.alert('Грешка', errorMsg, 'warning');          
        });
    } else {
        return setUserFarmings(userFarmings);
    }  
}

/**
 * 
 * @return  {[intger]}  [return sessionSubject userLevel]
 */
function getUserLevel() {
	let userLevelNum = parseInt(userLevel);

    return userLevelNum;
}

function getUserFarmings() 
{
    if (this.getUserLevel() === USERS_SUPER_ADMIN_FLAG ||
        this.getUserLevel() === USERS_SUPPORT_FLAG ||
        this.getUserLevel() === USERS_SALES_FLAG) {
        return null;
    }

   let farmings = jQuery('#user-farmings > input').combobox('getValues');
   if (farmings && farmings.length == 1 && farmings[0] == '') {
        farmings = jQuery('#user-farmings > input').combobox('getData')
        .filter((record) => {
           return record.id != '';
        }).map(record => {
            return record.id;
        });
   } 

   return farmings;
}

function setUserFarmings(userFarmings) 
{
    jQuery('#user-farmings > input').combobox({
        data: userFarmings,
        editable: false,
        valueField: 'id',
        textField: 'name',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function addEditFieldsDisplay(newUser) {
	var usersRightsTabs = jQuery('#users-rights-tabs');
    if (newUser) {
        initUserFarmings();
    }   

	if (!isSuperAdmin) {
		jQuery('#admin-rights').hide();
		disableTFConnect();

		jQuery('#user-roles-area').hide();
	}

	if (!isSuperAdmin && userLevel != USERS_SUPPORT_FLAG && userLevel != USERS_SALES_FLAG) {
		usersRightsTabs.tabs('close', UR_TAB_ADDITIONAL_INFO);
		rightsControl();

	} else {

		jQuery('#sub-user-count-rights input').numberbox({});
		jQuery('#paid-support-for > input').combobox({
			url: 'index.php?common-rpc=farming-year-combobox',
			valueField: 'id',
			textField: 'title',
			loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
			loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,

		});
        jQuery('#account-sales-person > input').combobox({
            url: 'index.php?common-rpc=salesman-list',
            valueField: 'id',
            textField: 'name',
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
            required: true,
			/*rpcMethod: 'salespersons'*/
		});

		jQuery('#user-roles > input').combobox({
            url: 'index.php?common-rpc=roles-list',
			valueField: 'id',
			textField: 'name',
			loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
			loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
			required: true,
			onSelect: function(record) {
				manageRolesRightsRadioButtons();
			}
		});

		//handle allowed checks
		if (jQuery('#account-date-limit-checkbox input').is(':checked')) {
			jQuery('#account-start-date > input').datebox({
				disabled: false
			});
			jQuery('#account-due-date > input').datebox({
				disabled: false
			});
		} else {
			jQuery('#account-start-date > input').datebox({
				disabled: true
			});
			jQuery('#account-due-date > input').datebox({
				disabled: true
			});
		}

		//handle entries checks
		if (jQuery('#account-allowed-checkbox > input').is(':checked')) {
			jQuery('#account-entries input').numberbox({
				disabled: false
			});
		} else {
			jQuery('#account-entries input').numberbox({
				disabled: true
			});
		}

		//handle allowed entries
		jQuery('#account-allowed-checkbox input').change(function() {
			if (jQuery('#account-allowed-checkbox input').is(':checked')) {
				jQuery('#account-entries > input').numberbox('enable');
			} else {
				jQuery('#account-entries > input').numberbox('disable');
			}
		});

		//handle date entries
		jQuery('#account-date-limit-checkbox input').change(function() {
			if (jQuery('#account-date-limit-checkbox input').is(':checked')) {
				jQuery('#account-start-date > input').datebox('enable');
				jQuery('#account-start-date > input').datebox('setValue', new Date());
				jQuery('#account-due-date > input').datebox('enable');
				jQuery('#account-due-date > input').datebox('setValue', new Date());

			} else {
				jQuery('#account-start-date > input').datebox('disable');
				jQuery('#account-start-date > input').datebox('clear');
				jQuery('#account-due-date > input').datebox('disable');
				jQuery('#account-due-date > input').datebox('clear');
			}
		});

		var getChecked = getCheckedUsers(true);
		var checked = getChecked && getChecked.length ? getChecked[0] : null;
		if (checked && (checked.user_level == USERS_NORMAL || (checked.original_user_level !== null && checked.original_user_level == USERS_NORMAL))) {
			jQuery('#additional-account-fields').hide();
			//win_height -= 60;
		} else {
			jQuery('#additional-account-fields').show();
		}
		initSubscriptionUsageFields();
	}

	initRightsRadioButtons();
	manageMapRightsRadioButtons();
	managePlotRightsRadioButtons();
	managePlotRightsRWRadioButtons();
    manageHypothecsRightsRadioButtons();
	manageSubsidyRightsRadioButtons();
	manageAgroRightsRadioButtons();
    manageCollectionsRightsRadioButtons();
	manageSalesContractsRightsRadioButtons();
	manageThematicMapsRightsRadioButtons();
	manageRolesRightsRadioButtons();
    managePaidSupport(newUser);
}
function disableTFConnect() {
	var tabsEl = jQuery("#users-rights-tabs");
	var index = tabsEl.tabs("getTabIndex", jQuery("#usb-modems"));
	tabsEl.tabs("disableTab", index);
}
function initRightsRadioButtons(){
    jQuery('#map-rights-r input').change(manageMapRightsRadioButtons);
    jQuery('#thematic-maps-rights-r input').change(manageThematicMapsRightsRadioButtons);
	jQuery('#plot-rights-r input').change(managePlotRightsRadioButtons);
	jQuery('#plot-rights-rw input').change(managePlotRightsRWRadioButtons);
	jQuery('#hypothecs-rights-r input').change(manageHypothecsRightsRadioButtons);
	jQuery('#subsidy-rights-r input').change(manageSubsidyRightsRadioButtons);
	jQuery('#agro-rights-r input').change(manageAgroRightsRadioButtons);
    jQuery('#yes-collections-rights-r input').change(manageCollectionsRightsRadioButtons);
	jQuery('#sales-contracts-rights-r input').change(manageSalesContractsRightsRadioButtons);
}

function manageAgroRightsRadioButtons(){

	//Agro Rights
	if(jQuery('#no-agro-rights-r input').is(':checked')) {

        jQuery("#yes-agro-rights-rw input").attr('disabled',true);
        jQuery("#yes-agro-rights-rw input").prop("checked", false);

        jQuery("#no-agro-rights-rw input").prop("checked", true);
        jQuery("#no-agro-rights-rw input").attr('disabled',true);

    }else{
        jQuery("#yes-agro-rights-rw input").attr('disabled',false);
        jQuery("#no-agro-rights-rw input").attr('disabled',false);
    }
}

function manageSubsidyRightsRadioButtons(){

	//Subsidy Rights
	if(jQuery('#no-subsidy-rights-r input').is(':checked')) {

        jQuery("#yes-subsidy-rights-rw input").attr('disabled',true);
        jQuery("#yes-subsidy-rights-rw input").prop("checked", false);

        jQuery("#no-subsidy-rights-rw input").prop("checked", true);
        jQuery("#no-subsidy-rights-rw input").attr('disabled',true);

    }else{
        jQuery("#yes-subsidy-rights-rw input").attr('disabled',false);
        jQuery("#no-subsidy-rights-rw input").attr('disabled',false);
    }
}

function manageCollectionsRightsRadioButtons(){

    //Collections Rights
    if(jQuery('#no-collections-rights-r input').is(':checked')) {

        jQuery("#yes-collections-rights-rw input").attr('disabled',true);
        jQuery("#yes-collections-rights-rw input").prop("checked", false);

        jQuery("#no-collections-rights-rw input").prop("checked", true);
        jQuery("#no-collections-rights-rw input").attr('disabled',true);

    }else{
        jQuery("#yes-collections-rights-rw input").attr('disabled',false);
        jQuery("#no-collections-rights-rw input").attr('disabled',false);
    }
}

function managePlotRightsRadioButtons(){

	//Plot Rights
	if(jQuery('#no-plot-rights-r input').is(':checked')) {

        jQuery("#yes-plot-rights-rw input").attr('disabled',true);
        jQuery("#yes-plot-rights-rw input").prop("checked", false);

        jQuery("#no-plot-rights-rw input").prop("checked", true);
        jQuery("#no-plot-rights-rw input").attr('disabled',true);


        jQuery("#ContractsOwnWriteRights").attr("disabled",true);
        jQuery("#ContractsOwnWriteRights").prop("checked",false);
        jQuery("#NoContractsOwnWriteRights").prop("checked",true);

        //set sales contracts, hypothecs and ownership contracts to disabled
        jQuery("#yes-hypothecs-rights-r input").prop("checked", false);
        jQuery("#yes-hypothecs-rights-r input").attr('disabled', false);

        jQuery("#no-hypothecs-rights-r input").prop("checked", true);
        jQuery("#no-hypothecs-rights-r input").attr('disabled', false);

        jQuery("#yes-hypothecs-rights-rw input").attr("disabled", true);
        jQuery("#yes-hypothecs-rights-rw input").prop("checked", false);
        jQuery("#no-hypothecs-rights-rw input").attr('disabled', true);
        jQuery("#no-hypothecs-rights-rw input").prop("checked", true);
        //sales conracts
        jQuery("#yes-sales-contracts-rights-r input").prop("checked", false);
        jQuery("#yes-sales-contracts-rights-r input").attr('disabled', false);

        jQuery("#no-sales-contracts-rights-r input").prop("checked", true);
        jQuery("#no-sales-contracts-rights-r input").attr('disabled', false);

        jQuery("#yes-sales-contracts-rights-rw input").attr("disabled", true);
        jQuery("#yes-sales-contracts-rights-rw input").prop("checked", false);
        jQuery("#no-sales-contracts-rights-rw input").attr('disabled', true);
        jQuery("#no-sales-contracts-rights-rw input").prop("checked", true);

    }else{
        jQuery("#yes-plot-rights-rw input").attr('disabled',false);
        jQuery("#no-plot-rights-rw input").attr('disabled',false);

        //set sales contracts, hypothecs and ownership contracts to enabled

        jQuery("#yes-hypothecs-rights-r input").prop("checked", true);
        jQuery("#yes-hypothecs-rights-r input").attr('disabled', false);

        jQuery("#no-hypothecs-rights-r input").prop("checked", false);
        jQuery("#no-hypothecs-rights-r input").attr('disabled', false);

        jQuery("#yes-hypothecs-rights-rw input").attr("disabled", false);
        jQuery("#no-hypothecs-rights-rw input").attr('disabled', false);
        //sales conracts
        jQuery("#yes-sales-contracts-rights-r input").prop("checked", true);
        jQuery("#yes-sales-contracts-rights-r input").attr('disabled', false);

        jQuery("#no-sales-contracts-rights-r input").prop("checked", false);
        jQuery("#no-sales-contracts-rights-r input").attr('disabled', false);

        jQuery("#yes-sales-contracts-rights-rw input").attr("disabled", false);
        jQuery("#no-sales-contracts-rights-rw input").attr('disabled', false);
    }
}

function managePlotRightsRWRadioButtons(){

	//Plot Rights
	if (jQuery("#yes-plot-rights-rw input").is(':checked')) {
        	jQuery("#ContractsOwnWriteRights").attr("disabled",false);
        	jQuery("#NoContractsOwnWriteRights").attr("disabled",false);
            jQuery("#ContractsOwnWriteRights").prop("checked", true);
            jQuery("#NoContractsOwnWriteRights").prop("checked",false);

            //set sales contracts and hypothecs to enabled
            if(jQuery("#yes-hypothecs-rights-r input").is(':checked'))
            {
                jQuery("#yes-hypothecs-rights-rw input").attr("disabled", false);
                jQuery("#no-hypothecs-rights-rw input").attr('disabled', false);
                jQuery("#yes-hypothecs-rights-rw input").prop("checked", true);
                jQuery("#no-hypothecs-rights-rw input").prop('checked', false);
            }

            //sales conracts
            if(jQuery("#yes-sales-contracts-rights-r input").is(':checked'))
            {
                jQuery("#yes-sales-contracts-rights-rw input").attr("disabled", false);
                jQuery("#no-sales-contracts-rights-rw input").attr('disabled', false);
                jQuery("#yes-sales-contracts-rights-rw input").prop("checked", true);
                jQuery("#no-sales-contracts-rights-rw input").prop('checked', false);
            }
        } else {
        	jQuery("#NoContractsOwnWriteRights").attr("disabled",true);
        	jQuery("#ContractsOwnWriteRights").attr("disabled",true);
        	jQuery("#NoContractsOwnWriteRights").prop("checked",true);
        	jQuery("#ContractsOwnWriteRights").prop("checked",false);

            //set sales contracts and hypothecs to disabled
            if(jQuery("#no-hypothecs-rights-r input").is(':checked'))
            {
                jQuery("#yes-hypothecs-rights-rw input").attr("disabled", true);
                jQuery("#no-hypothecs-rights-rw input").attr('disabled', true);
            }else{
                jQuery("#yes-hypothecs-rights-rw input").attr("disabled", false);
                jQuery("#no-hypothecs-rights-rw input").attr('disabled', false);
            }

            jQuery("#yes-hypothecs-rights-rw input").prop("checked", false);
            jQuery("#no-hypothecs-rights-rw input").prop('checked', true);
            //sales conracts
            if(jQuery("#no-sales-contracts-rights-r input").is(':checked'))
            {
                jQuery("#yes-sales-contracts-rights-rw input").attr("disabled", true);
                jQuery("#no-sales-contracts-rights-rw input").attr('disabled', true);
            }else{
                jQuery("#yes-sales-contracts-rights-rw input").attr("disabled", false);
                jQuery("#no-sales-contracts-rights-rw input").attr('disabled', false);
            }

            jQuery("#yes-sales-contracts-rights-rw input").prop("checked", false);
            jQuery("#no-sales-contracts-rights-rw input").prop('checked', true);
        }
}

function manageHypothecsRightsRadioButtons(){

	//SalesContracts Rights
	if(jQuery('#no-hypothecs-rights-r input').is(':checked')) {

        jQuery("#yes-hypothecs-rights-rw input").attr('disabled',true);
        jQuery("#yes-hypothecs-rights-rw input").prop("checked", false);

        jQuery("#no-hypothecs-rights-rw input").prop("checked", true);
        jQuery("#no-hypothecs-rights-rw input").attr('disabled',true);

    }else{
        jQuery("#yes-hypothecs-rights-rw input").attr('disabled',false);
        jQuery("#no-hypothecs-rights-rw input").attr('disabled',false);
    }
}

function manageSalesContractsRightsRadioButtons(){

	//SalesContracts Rights
	if(jQuery('#no-sales-contracts-rights-r input').is(':checked')) {

        jQuery("#yes-sales-contracts-rights-rw input").attr('disabled',true);
        jQuery("#yes-sales-contracts-rights-rw input").prop("checked", false);

        jQuery("#no-sales-contracts-rights-rw input").prop("checked", true);
        jQuery("#no-sales-contracts-rights-rw input").attr('disabled',true);

    }else{
        jQuery("#yes-sales-contracts-rights-rw input").attr('disabled',false);
        jQuery("#no-sales-contracts-rights-rw input").attr('disabled',false);
    }
}

function manageMapRightsRadioButtons(){

	//Map Rights
	if(jQuery('#no-map-rights-r input').is(':checked')) {

        jQuery("#yes-map-rights-rw input").attr('disabled',true);
        jQuery("#yes-map-rights-rw input").prop("checked", false);

        jQuery("#no-map-rights-rw input").prop("checked", true);
        jQuery("#no-map-rights-rw input").attr('disabled',true);

        jQuery("#yes-thematic-maps-rights-r input").attr('disabled',true);
        jQuery("#yes-thematic-maps-rights-r input").prop("checked", false);

        jQuery("#no-thematic-maps-rights-r input").prop("checked", true);
        jQuery("#no-thematic-maps-rights-r input").attr('disabled',true);

        jQuery("#yes-thematic-maps-rights-rw input").attr('disabled',true);
        jQuery("#yes-thematic-maps-rights-rw input").prop("checked", false);

        jQuery("#no-thematic-maps-rights-rw input").prop("checked", true);
        jQuery("#no-thematic-maps-rights-rw input").attr('disabled',true);

    }else{
        jQuery("#yes-map-rights-rw input").attr('disabled',false);
        jQuery("#no-map-rights-rw input").attr('disabled',false);
        jQuery("#yes-thematic-maps-rights-r input").attr('disabled',false);
        jQuery("#no-thematic-maps-rights-r input").attr('disabled',false);
        jQuery("#yes-thematic-maps-rights-rw input").attr('disabled',false);
        jQuery("#no-thematic-maps-rights-rw input").attr('disabled',false);
    }
}

function manageRolesRightsRadioButtons() {

	if (!isSuperAdmin && userLevel != USERS_SUPPORT_FLAG && userLevel != USERS_SALES_FLAG) return;
	//Support Rights
    var selected = jQuery('#user-roles > input').combobox('getValue');
    var modificationForbidden = selected == USERS_SUPPORT_FLAG || selected == USERS_SALES_FLAG;

	if (modificationForbidden) {
        jQuery('#yes-map-rights-r input').prop('checked', false);
        jQuery('#no-map-rights-r input').prop('checked', true);
        jQuery('#yes-map-rights-rw input').prop('checked', false);
        jQuery('#no-map-rights-rw input').prop('checked', true);
        jQuery('#yes-thematic-maps-rights-r input').prop('checked', false);
        jQuery('#no-thematic-maps-rights-r input').prop('checked', true);
        jQuery('#yes-thematic-maps-rights-rw input').prop('checked', false);
        jQuery('#no-thematic-maps-rights-rw input').prop('checked', true);
        jQuery('#yes-plot-rights-r input').prop('checked', false);
        jQuery('#no-plot-rights-r input').prop('checked', true);
        jQuery('#yes-plot-rights-rw input').prop('checked', false);
        jQuery('#no-plot-rights-rw input').prop('checked', true);
        jQuery('#yes-hypothecs-rights-r input').prop('checked', false);
        jQuery('#no-hypothecs-rights-r input').prop('checked', true);
        jQuery('#yes-hypothecs-rights-rw input').prop('checked', false);
        jQuery('#no-hypothecs-rights-rw input').prop('checked', true);
        jQuery('#ContractsOwnWriteRights').prop('checked', false);
        jQuery('#NoContractsOwnWriteRights').prop('checked', true);
        jQuery('#EquityRights').prop('checked', false);
        jQuery('#NoEquityRights').prop('checked', true);
        jQuery('#yes-subsidy-rights-r input').prop('checked', false);
        jQuery('#no-subsidy-rights-r input').prop('checked', true);
        jQuery('#yes-subsidy-rights-rw input').prop('checked', false);
        jQuery('#no-subsidy-rights-rw input').prop('checked', true);
        jQuery('#yes-agro-rights-r input').prop('checked', false);
        jQuery('#no-agro-rights-r input').prop('checked', true);
        jQuery('#yes-agro-rights-rw input').prop('checked', false);
        jQuery('#no-agro-rights-rw input').prop('checked', true);
        jQuery('#yes-sales-contracts-rights-r input').prop('checked', false);
        jQuery('#no-sales-contracts-rights-r input').prop('checked', true);
        jQuery('#yes-sales-contracts-rights-rw input').prop('checked', false);
        jQuery('#no-sales-contracts-rights-rw input').prop('checked', true);
        jQuery('#yes-collections-rights-r input').prop('checked', false);
        jQuery('#no-collections-rights-r input').prop('checked', true);
        jQuery('#yes-collections-rights-rw input').prop('checked', false);
        jQuery('#no-collections-rights-rw input').prop('checked', true);
        jQuery('#GeoscanMapRights').prop('checked', false);
        jQuery('#NoGeoscanMapRights').prop('checked', true);
        jQuery('#KVSCuttingRights').prop('checked', false);
        jQuery('#NoKVSCuttingRights').prop('checked', true);
        jQuery('#ExportMassPaymentRights').prop('checked', false);
        jQuery('#NoExportMassPaymentRights').prop('checked', true);
    }

    jQuery('#yes-map-rights-r input').attr('disabled', modificationForbidden);
    jQuery('#no-map-rights-r input').attr('disabled', modificationForbidden);
    jQuery('#yes-map-rights-rw input').attr('disabled', modificationForbidden);
    jQuery('#no-map-rights-rw input').attr('disabled', modificationForbidden);
    jQuery('#yes-thematic-maps-rights-r input').attr('disabled', modificationForbidden);
    jQuery('#no-thematic-maps-rights-r input').attr('disabled', modificationForbidden);
    jQuery('#yes-thematic-maps-rights-rw input').attr('disabled', modificationForbidden);
    jQuery('#no-thematic-maps-rights-rw input').attr('disabled', modificationForbidden);
    jQuery('#yes-plot-rights-r input').attr('disabled', modificationForbidden);
    jQuery('#no-plot-rights-r input').attr('disabled', modificationForbidden);
    jQuery('#yes-plot-rights-rw input').attr('disabled', modificationForbidden);
    jQuery('#no-plot-rights-rw input').attr('disabled', modificationForbidden);
    jQuery('#yes-hypothecs-rights-r input').attr('disabled', modificationForbidden);
    jQuery('#no-hypothecs-rights-r input').attr('disabled', modificationForbidden);
    jQuery('#yes-hypothecs-rights-rw input').attr('disabled', modificationForbidden);
    jQuery('#no-hypothecs-rights-rw input').attr('disabled', modificationForbidden);
    jQuery('#ContractsOwnWriteRights').attr('disabled', modificationForbidden);
    jQuery('#NoContractsOwnWriteRights').attr('disabled', modificationForbidden);
    jQuery('#EquityRights').attr('disabled', modificationForbidden);
    jQuery('#NoEquityRights').attr('disabled', modificationForbidden);
    jQuery('#yes-subsidy-rights-r input').attr('disabled', modificationForbidden);
    jQuery('#no-subsidy-rights-r input').attr('disabled', modificationForbidden);
    jQuery('#yes-subsidy-rights-rw input').attr('disabled', modificationForbidden);
    jQuery('#no-subsidy-rights-rw input').attr('disabled', modificationForbidden);
    jQuery('#yes-agro-rights-r input').attr('disabled', modificationForbidden);
    jQuery('#no-agro-rights-r input').attr('disabled', modificationForbidden);
    jQuery('#yes-agro-rights-rw input').attr('disabled', modificationForbidden);
    jQuery('#no-agro-rights-rw input').attr('disabled', modificationForbidden);
    jQuery('#yes-sales-contracts-rights-r input').attr('disabled', modificationForbidden);
    jQuery('#no-sales-contracts-rights-r input').attr('disabled', modificationForbidden);
    jQuery('#yes-sales-contracts-rights-rw input').attr('disabled', modificationForbidden);
    jQuery('#no-sales-contracts-rights-rw input').attr('disabled', modificationForbidden);
    jQuery('#yes-collections-rights-r input').attr('disabled', modificationForbidden);
    jQuery('#no-collections-rights-r input').attr('disabled', modificationForbidden);
    jQuery('#yes-collections-rights-rw input').attr('disabled', modificationForbidden);
    jQuery('#no-collections-rights-rw input').attr('disabled', modificationForbidden);
    jQuery('#GeoscanMapRights').attr('disabled', modificationForbidden);
    jQuery('#NoGeoscanMapRights').attr('disabled', modificationForbidden);
    jQuery('#KVSCuttingRights').attr('disabled', modificationForbidden);
    jQuery('#NoKVSCuttingRights').attr('disabled', modificationForbidden);
    jQuery('#ExportMassPaymentRights').attr('disabled', modificationForbidden);
    jQuery('#NoExportMassPaymentRights').attr('disabled', modificationForbidden);
}

function setEditUserFieldsData(data) {
	let	userInfo = data.userInfo;
	let userRights = data.userRights;

    initUserFarmings(data.userFarmings);

	jQuery('#username > input').val(userInfo.username);
	jQuery('#username > input').attr("disabled", true);
	jQuery('#email > input').val(userInfo.email);
	jQuery('#name > input').val(userInfo.name);
	jQuery('#phone input').val(userInfo.phone);
	jQuery('#address > input').val(userInfo.address);
	jQuery('#comment > textarea').val(userInfo.comment);

	if (isSuperAdmin || userLevel == USERS_SUPPORT_FLAG) {
		jQuery('#sub-user-count-rights > input').numberbox('setValue', userInfo.can_create);
		jQuery('#account-allowed-farmings > input').val(userInfo.allowed_farmings);
        jQuery('#paid-support-for > input').combobox({
            onLoadSuccess: function () {
                if (userInfo.paid_support_start_date && userInfo.paid_support_due_date) {
                    jQuery(this).combobox('clear');
                } else if (userInfo.paid_support) {
                    jQuery(this).combobox('select', userInfo.paid_support);
                }
            }
        });
        jQuery('#account-sales-person > input').combobox('select', userInfo.salesperson_id);
        jQuery('#user-roles > input').combobox({
            onLoadSuccess: function () {
                jQuery(this).combobox('select', userInfo.level);
            }
        });

		jQuery('#paid-support-dates-checkbox > input').prop('checked', userInfo.paid_support_allow_dates);
        jQuery('#paid-support-dates-checkbox > input').trigger('change');
        jQuery('#paid-support-start-date > input').datebox('setValue', userInfo.paid_support_start_date);
        jQuery('#paid-support-due-date > input').datebox('setValue', userInfo.paid_support_due_date);
		jQuery('#account-sales-person > input').combobox('select', userInfo.salesperson_id);
        jQuery('#account-date-limit-checkbox > input').prop('checked', userInfo.date_flag);
        jQuery('#account-date-limit-checkbox > input').trigger('change');
		jQuery('#account-start-date > input').datebox('setValue', userInfo.start_date);
		jQuery('#account-due-date > input').datebox('setValue', userInfo.due_date);
        jQuery('#account-allowed-checkbox > input').prop('checked', userInfo.entry_flag);
        jQuery('#account-allowed-checkbox > input').trigger('change');
        jQuery('#account-entries > input').numberbox('setValue', userInfo.entries_left);
        jQuery('#account-trial-checkbox > input').prop('checked', userInfo.is_trial);
	}

	if (isSuperAdmin && userInfo.level === USERS_ADMIN_FLAG) {
        jQuery('#account-ekatte').show();
        jQuery('#account-ekatte > input').val(userInfo.database);
        jQuery('#account-ekatte-dropdown > input')
            .combobox({
                    url: 'index.php?common-rpc=ekate-combobox',
                    rpcParams: [[], jQuery('#account-ekatte > input').val()],
                    valueField: 'ekate',
                    textField: 'text',
                    onLoadSuccess: function(data) {
                        jQuery('#btn-delete-selected-ekatte').linkbutton('disable');
                        if(data.result.length > 0){
                            var first = data.result[0];
                            jQuery(this).combobox('select', first.ekate);
                        }
                        jQuery('#btn-delete-selected-ekatte').linkbutton(first ? 'enable' : 'disable');
                    },
                    loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                    loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
                }
            );
    }

    if (isSuperAdmin || userLevel == USERS_SUPPORT_FLAG || userLevel == USERS_SALES_FLAG) {
        populateSubscriptionUsageFields(userInfo);
    }

	if (userRights.MAP_RIGHTS_R) {
		jQuery('#yes-map-rights-r input').prop('checked', true);
		jQuery('#no-map-rights-r input').prop('checked', false);
	}
	if (userRights.MAP_RIGHTS_RW) {
		jQuery('#yes-map-rights-rw input').prop('checked', true);
		jQuery('#no-map-rights-rw input').prop('checked', false);
	}

	manageMapRightsRadioButtons();

	if (userRights.THEMATIC_MAPS_RIGHTS_R) {
		jQuery('#yes-thematic-maps-rights-r input').prop('checked', true);
		jQuery('#no-thematic-maps-rights-r input').prop('checked', false);
	}
	if (userRights.THEMATIC_MAPS_RIGHTS_RW) {
		jQuery('#yes-thematic-maps-rights-rw input').prop('checked', true);
		jQuery('#no-thematic-maps-rights-rw input').prop('checked', false);
	}

	manageThematicMapsRightsRadioButtons();

	if (userRights.PLOT_RIGHTS_R) {
		jQuery('#yes-plot-rights-r input').prop('checked', true);
		jQuery('#no-plot-rights-r input').prop('checked', false);
	}
	if (userRights.PLOT_RIGHTS_RW) {
		jQuery('#yes-plot-rights-rw input').prop('checked', true);
		jQuery('#no-plot-rights-rw input').prop('checked', false);
	}

	managePlotRightsRadioButtons();
	managePlotRightsRWRadioButtons();

    if (userRights.HYPOTHECS_RIGHTS_R) {
		jQuery('#yes-hypothecs-rights-r input').prop('checked', true);
		jQuery('#no-hypothecs-rights-r input').prop('checked', false);
	}
	if (userRights.HYPOTHECS_RIGHTS_RW) {
		jQuery('#yes-hypothecs-rights-rw input').prop('checked', true);
		jQuery('#no-hypothecs-rights-rw input').prop('checked', false);
	}

	manageHypothecsRightsRadioButtons();

	if (userRights.SALES_CONTRACTS_RIGHTS_R) {
		jQuery('#yes-sales-contracts-rights-r input').prop('checked', true);
		jQuery('#no-sales-contracts-rights-r input').prop('checked', false);
	}
	if (userRights.SALES_CONTRACTS_RIGHTS_RW) {
		jQuery('#yes-sales-contracts-rights-rw input').prop('checked', true);
		jQuery('#no-sales-contracts-rights-rw input').prop('checked', false);
	}
	if(userRights.DASHBOARD_RIGHTS){
        jQuery('#yes-dashboard-rights-r input').prop('checked',true);
        jQuery('#no-dashboard-rights-r input').prop('checked',false);
	}
    if(userRights.WAREHOUSE_USER_RIGHTS){
        jQuery('#yes-warehouse-rights-r input').prop('checked',true);
        jQuery('#no-warehouse-rights-r input').prop('checked',false);
    }
    if(userRights.WAREHOUSE_ADMIN_RIGHTS){
        jQuery('#yes-warehouse-rights-w input').prop('checked',true);
        jQuery('#no-warehouse-rights-w input').prop('checked',false);
    }

    if(userRights.WAREHOUSE_EDITOR_RIGHTS){
        jQuery('#yes-warehouse-editor-rights-w input').prop('checked',true);
        jQuery('#no-warehouse-editor-rights-w input').prop('checked',false);
    }


	manageSalesContractsRightsRadioButtons();

	if (userRights.CONTRACTS_OWN_WRITE_RIGHTS) {
		jQuery('#ContractsOwnWriteRights').prop('checked', true);
		jQuery('#NOContractsOwnWriteRights').prop('checked', false);
	} else {
		jQuery('#ContractsOwnWriteRights').prop('checked', false);
		jQuery('#NOContractsOwnWriteRights').prop('checked', true);
	}

	if (userRights.EQUITY_RIGHTS) {
		jQuery('#EquityRights').prop('checked', true);
		jQuery('#NoEquityRights').prop('checked', false);
	} else {
		jQuery('#EquityRights').prop('checked', false);
		jQuery('#NoEquityRights').prop('checked', true);
	}

	if (userRights.SUBSIDY_RIGHTS) {
		jQuery('#yes-subsidy-rights-r input').prop('checked', true);
		jQuery('#no-subsidy-rights-r input').prop('checked', false);
	}
	if (userRights.SUBSIDY_RIGHTS_RW) {
		jQuery('#yes-subsidy-rights-rw input').prop('checked', true);
		jQuery('#no-subsidy-rights-rw input').prop('checked', false);
	}

	manageSubsidyRightsRadioButtons();

	if (userRights.AGRO_RIGHTS) {
		jQuery('#yes-agro-rights-r input').prop('checked', true);
		jQuery('#no-agro-rights-r input').prop('checked', false);
	}
	if (userRights.AGRO_RIGHTS_RW) {
		jQuery('#yes-agro-rights-rw input').prop('checked', true);
		jQuery('#no-agro-rights-rw input').prop('checked', false);
	}

	manageAgroRightsRadioButtons();

    if (userRights.COLLECTIONS_RIGHTS) {
        jQuery('#yes-collections-rights-r input').prop('checked', true);
        jQuery('#no-collections-rights-r input').prop('checked', false);
    }
    if (userRights.COLLECTIONS_RIGHTS_RW) {
        jQuery('#yes-collections-rights-rw input').prop('checked', true);
        jQuery('#no-collections-rights-rw input').prop('checked', false);
    }

    manageCollectionsRightsRadioButtons();

    if (userRights.KVS_CUTTING_RIGHTS) {
        jQuery('#KVSCuttingRights').prop('checked', true);
        jQuery('#NoKVSCuttingRights').prop('checked', false);
    } else {
        jQuery('#KVSCuttingRights').prop('checked', false);
        jQuery('#NoKVSCuttingRights').prop('checked', true);
    }

    if (userRights.EXPORT_MASS_PAYMENT_RIGHTS) {
        jQuery('#ExportMassPaymentRights').prop('checked', true);
        jQuery('#NoExportMassPaymentRights').prop('checked', false);
    } else {
        jQuery('#ExportMassPaymentRights').prop('checked', false);
        jQuery('#NoExportMassPaymentRights').prop('checked', true);
    }

	manageRolesRightsRadioButtons();

	if(userRights.DASHBOARD_RIGHTS){
        jQuery('#yes-dashboard-rights-r input').prop('checked', true);
        jQuery('#no-dashboard-rights-r input').prop('checked', false);
	}

    if(userRights.WAREHOUSE_USER_RIGHTS){
        jQuery('#yes-warehouse-rights-r input').prop('checked', true);
        jQuery('#no-warehouse-rights-r input').prop('checked', false);
    }

    if(userRights.WAREHOUSE_ADMIN_RIGHTS){
        jQuery('#yes-warehouse-rights-w input').prop('checked', true);
        jQuery('#no-warehouse-rights-w input').prop('checked', false);
    }

    if(userRights.WAREHOUSE_EDITOR_RIGHTS){
        jQuery('#yes-warehouse-editor-rights-w input').prop('checked', true);
        jQuery('#no-warehouse-editor-rights-w input').prop('checked', false);
    }

    if(userRights.CADASTRE_RIGHTS){
        jQuery('#has-cadastre-rights input').prop('checked', true);
        jQuery('#no-cadastre-rights input').prop('checked', false);
    }

    if(userRights.SLOPE_RIGHTS){
        jQuery('#has-slope-rights input').prop('checked', true);
        jQuery('#no-slope-rights input').prop('checked', false);
    }

}

function resetAddEditFields(){
	jQuery('#username > input').val('');
	jQuery('#password > input').val('');
	jQuery('#email > input').val('');
	jQuery('#name > input').val('');
	jQuery('#phone input').val('');
	jQuery('#address > input').val('');
	jQuery('#comment > textarea').val('');

    if(isSuperAdmin || userLevel == USERS_SUPPORT_FLAG) {
        jQuery('#tracking-username > input').val('');
        jQuery('#tracking-password > input').val('');
        jQuery('#sub-user-count-rights > input').val('');
        jQuery('#account-allowed-farmings > input').val('');
        jQuery('#paid-support-for > input').combobox('reset');
        jQuery('#paid-support-dates-checkbox > input').prop('checked', false);
        jQuery('#paid-support-start-date > input').datebox('reset');
        jQuery('#paid-support-due-date > input').datebox('reset');
        jQuery('#account-date-limit-checkbox > input').prop('checked',false);
        jQuery('#account-start-date > input').datebox('reset');
        jQuery('#account-due-date > input').datebox('reset');
        jQuery('#account-allowed-checkbox > input').prop('checked', false);
        jQuery('#account-entries > input').val('');
		jQuery('#account-trial-checkbox > input').prop('checked', false);
		jQuery('#user-roles > input').combobox('select', 2);
    }

    if (isSuperAdmin || userLevel == USERS_SUPPORT_FLAG || userLevel == USERS_SALES_FLAG) {
        resetSubscriptionUsageFields();
    }

	jQuery('#yes-map-rights-r input').prop('checked', false);
	jQuery('#no-map-rights-r input').prop('checked', true);
	jQuery('#yes-map-rights-rw input').prop('checked', false);
	jQuery('#no-map-rights-rw input').prop('checked', true);
	jQuery('#yes-thematic-maps-rights-r input').prop('checked', false);
	jQuery('#no-thematic-maps-rights-r input').prop('checked', true);
	jQuery('#yes-thematic-maps-rights-rw input').prop('checked', false);
	jQuery('#no-thematic-maps-rights-rw input').prop('checked', true);
	jQuery('#yes-plot-rights-r input').prop('checked', false);
	jQuery('#no-plot-rights-r input').prop('checked', true);
	jQuery('#yes-plot-rights-rw input').prop('checked', false);
	jQuery('#no-plot-rights-rw input').prop('checked', true);
	jQuery('#yes-hypothecs-rights-r input').prop('checked', false);
	jQuery('#no-hypothecs-rights-r input').prop('checked', true);
	jQuery('#yes-hypothecs-rights-rw input').prop('checked', false);
	jQuery('#no-hypothecs-rights-rw input').prop('checked', true);
	jQuery('#ContractsOwnWriteRights').prop('checked', false);
	jQuery('#NoContractsOwnWriteRights').prop('checked', true);
	jQuery('#EquityRights').prop('checked', false);
	jQuery('#NoEquityRights').prop('checked', true);
	jQuery('#yes-subsidy-rights-r input').prop('checked', false);
	jQuery('#no-subsidy-rights-r input').prop('checked', true);
	jQuery('#yes-subsidy-rights-rw input').prop('checked', false);
	jQuery('#no-subsidy-rights-rw input').prop('checked', true);
	jQuery('#yes-agro-rights-r input').prop('checked', false);
	jQuery('#no-agro-rights-r input').prop('checked', true);
	jQuery('#yes-agro-rights-rw input').prop('checked', false);
	jQuery('#no-agro-rights-rw input').prop('checked', true);
	jQuery('#yes-sales-contracts-rights-r input').prop('checked', false);
	jQuery('#no-sales-contracts-rights-r input').prop('checked', true);
	jQuery('#yes-sales-contracts-rights-rw input').prop('checked', false);
    jQuery('#yes-dashboard-rights-r input').prop('checked',false);
	jQuery('#no-sales-contracts-rights-rw input').prop('checked', true);
    jQuery('#yes-collections-rights-r input').prop('checked', false);
    jQuery('#no-collections-rights-r input').prop('checked', true);
    jQuery('#yes-collections-rights-rw input').prop('checked', false);
    jQuery('#no-collections-rights-rw input').prop('checked', true);
	jQuery('#GeoscanMapRights').prop('checked', true);
	jQuery('#NoGeoscanMapRights').prop('checked', false);
    jQuery('#account-ekatte').hide();
    jQuery('#KVSCuttingRights').prop('checked', false);
    jQuery('#NoKVSCuttingRights').prop('checked', true);

	manageMapRightsRadioButtons();
	managePlotRightsRadioButtons();
	manageHypothecsRightsRadioButtons();
	manageSubsidyRightsRadioButtons();
	manageAgroRightsRadioButtons();
    manageCollectionsRightsRadioButtons();
	manageSalesContractsRightsRadioButtons();
	manageThematicMapsRightsRadioButtons();
	manageRolesRightsRadioButtons();
}

function manageThematicMapsRightsRadioButtons(){

	//Thematic Maps Rights
	if(jQuery('#no-thematic-maps-rights-r input').is(':checked')) {

        jQuery("#yes-thematic-maps-rights-rw input").attr('disabled',true);
        jQuery("#yes-thematic-maps-rights-rw input").prop("checked", false);

        jQuery("#no-thematic-maps-rights-rw input").prop("checked", true);
        jQuery("#no-thematic-maps-rights-rw input").attr('disabled',true);

    }else{
        jQuery("#yes-thematic-maps-rights-rw input").attr('disabled',false);
        jQuery("#no-thematic-maps-rights-rw input").attr('disabled',false);
    }
}

jQuery(function(){
    jQuery('#btn-edit-selected-user').bind('click', function() {
    	if (!validateRequiredFields()) {
        	jQuery.messager.alert('Грешка', 'Има непопълнено задължително поле!', 'error');
        	return;

    	}
        let editObject = getAddEditFieldsData(true);

        TF.Rpc.Users.UsersMainGrid.editUser(editObject)
        .done(function (data) {
            jQuery('#win-add').window('close');
            jQuery('#users-tables').datagrid('uncheckAll');
            jQuery('#users-tables').datagrid('loadRpc');
        })
        .fail(function (errorObj) {
        	jQuery.messager.alert('Грешка', errorObj.getMessage(),'warning');
        });
    });

    jQuery('#btn-delete-selected-ekatte').bind('click', function() {
        if(jQuery(this).linkbutton('options').disabled) return;

        var warning = jQuery.messager.confirm('Потвърждение', 'ЕКАТТЕ ще бъде безвъзвратно изтрито. Сигурни ли сте, че искате да продължите?', function (r) {
            if (r) {
                TF.Rpc.Users.UsersMainGrid.hasContractsInEktte({
                    ekatte : jQuery('#account-ekatte-dropdown > input').combobox('getValue'),
                    db     : jQuery('#account-ekatte > input').val()
                })
                .done(function (data) {
                    if(data > 0) {
                        jQuery.messager.alert('Грешка', 'За ЕКАТТЕ, който сте избрали има сключени ' + data + ' договор(а) и не може да бъде изтрит!', 'error');
                    } else {
                        TF.Rpc.Users.UsersMainGrid.deleteSelectedEktte({
                            ekatte : jQuery('#account-ekatte-dropdown > input').combobox('getValue'),
                            db     : jQuery('#account-ekatte > input').val()
                        })
                            .done(function () {
                                jQuery('#account-ekatte-dropdown > input').combobox('reload');
                                jQuery.messager.alert('Успешно изтриване','Записът е успешно изтрит.');
                            })
                            .fail(function (errorObj) {
                                jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
                            });

                    }
                })
                .fail(function (errorObj) {
                    jQuery.messager.alert('Грешка', errorObj.getMessage(),'warning');
                });
            }
        });
        warning.find('.messager-icon').removeClass('messager-question').addClass('messager-warning');
    });

    jQuery('#btn-add-new-user-to-grid').bind('click', function() {
        if (!validateRequiredFields()) {
        	jQuery.messager.alert('Грешка', 'Има непопълнено задължително поле!', 'error');
        	return;

    	}
        var addObject = getAddEditFieldsData(false);
        TF.Rpc.Users.UsersMainGrid.add(addObject)
        .done(function (data) {
        	doAddUser(data);
        })
        .fail(function (errorObj) {
            if (errorObj.is(TF.Rpc.ExceptionsList.USERNAME_NOT_VALID)) {
                jQuery.messager.alert('Информация',TF.Rpc.ExceptionsList.USERNAME_NOT_VALID.message,'info');
                return;
            }

            doAddUser(errorObj);
        });
    });
});

function doAddUser(data){
    if(!data.uservalidate || !data.can_create){
    	if(!data.uservalidate) {
    		jQuery.messager.alert('Информация','Потребителя съществува!','info');
        } else {
        	jQuery.messager.alert('Информация','Вие нямате право да създавате нови потребители!','info');
        }
    } else {
        jQuery('#win-add').window('close');
        jQuery('#users-tables').datagrid('uncheckAll');
        jQuery('#users-tables').datagrid('loadRpc');
    }
}

function getAddEditFieldsData(isEdit) {
	var getSelected = jQuery('#users-tables').datagrid('getSelected');
    let userFarmings = getUserFarmings();
	var pwd = jQuery('#pwd');
    var obj = {
		username: jQuery('#username > input').val(),
		password: pwd.val(),
		email: jQuery('#email > input').val(),
		name: jQuery('#name > input').val(),
		phone: jQuery('#phone input').val(),
		address: jQuery('#address > input').val(),
		comment: jQuery('#comment > textarea').val(),

		MAP_RIGHTS_R: jQuery('#yes-map-rights-r input').is(':checked'),
		MAP_RIGHTS_RW: jQuery('#yes-map-rights-rw input').is(':checked'),
		THEMATIC_MAPS_RIGHTS_R: jQuery('#yes-thematic-maps-rights-r input').is(':checked'),
		THEMATIC_MAPS_RIGHTS_RW: jQuery('#yes-thematic-maps-rights-rw input').is(':checked'),
		PLOT_RIGHTS_R: jQuery('#yes-plot-rights-r input').is(':checked'),
		PLOT_RIGHTS_RW: jQuery('#yes-plot-rights-rw input').is(':checked'),
		HYPOTHECS_RIGHTS_R: jQuery('#yes-hypothecs-rights-r input').is(':checked'),
		HYPOTHECS_RIGHTS_RW: jQuery('#yes-hypothecs-rights-rw input').is(':checked'),
		CONTRACTS_OWN_WRITE_RIGHTS: jQuery('#ContractsOwnWriteRights').is(':checked'),
		EQUITY_RIGHTS: jQuery('#EquityRights').is(':checked'),
		SUBSIDY_RIGHTS: jQuery('#yes-subsidy-rights-r input').is(':checked'),
		SUBSIDY_RIGHTS_RW: jQuery('#yes-subsidy-rights-rw input').is(':checked'),
		AGRO_RIGHTS: jQuery('#yes-agro-rights-r input').is(':checked'),
		AGRO_RIGHTS_RW: jQuery('#yes-agro-rights-rw input').is(':checked'),
        COLLECTIONS_RIGHTS: jQuery('#yes-collections-rights-r input').is(':checked'),
        COLLECTIONS_RIGHTS_RW: jQuery('#yes-collections-rights-rw input').is(':checked'),
		SALES_CONTRACTS_RIGHTS_R: jQuery('#yes-sales-contracts-rights-r input').is(':checked'),
		SALES_CONTRACTS_RIGHTS_RW: jQuery('#yes-sales-contracts-rights-rw input').is(':checked'),
		KVS_CUTTING_RIGHTS: jQuery('#KVSCuttingRights').is(':checked'),
		EXPORT_MASS_PAYMENT_RIGHTS: jQuery('#ExportMassPaymentRights').is(':checked'),
		DASHBOARD_RIGHTS: jQuery('#yes-dashboard-rights-r input').is(':checked'),
		WAREHOUSE_USER_RIGHTS: jQuery('#yes-warehouse-rights-r input').is(':checked'),
        WAREHOUSE_ADMIN_RIGHTS: jQuery('#yes-warehouse-rights-w input').is(':checked'),
        WAREHOUSE_EDITOR_RIGHTS: jQuery('#yes-warehouse-editor-rights-w input').is(':checked'),
        SLOPE_RIGHTS: jQuery('#has-slope-rights input').is(':checked'),
        CADASTRE_RIGHTS: jQuery('#has-cadastre-rights input').is(':checked'),

		editUserID: getSelected ? getSelected.id : -1,
        userFarmings: userFarmings

	};

    if(isSuperAdmin || userLevel == USERS_SUPPORT_FLAG) {
        var adminObj = {
            tracking_username: jQuery('#tracking-username > input').val(),
            tracking_password: jQuery('#tracking-password > input').val(),
            sub_user_count_rights: jQuery('#sub-user-count-rights > input').val(),
            account_allowed_farmigns: jQuery('#account-allowed-farmings > input').val(),
            paid_support_start_date: jQuery('#paid-support-start-date > input').datebox('getValue'),
            paid_support_due_date: jQuery('#paid-support-due-date > input').datebox('getValue'),
            account_date_limit: jQuery('#account-date-limit-checkbox > input').is(':checked'),
            account_start_date: jQuery('#account-start-date > input').datebox('getValue'),
            account_due_date: jQuery('#account-due-date > input').datebox('getValue'),
            entry_flag: jQuery('#account-allowed-checkbox > input').is(':checked'),
            account_entries: jQuery('#account-entries > input').val(),
            account_is_trial: jQuery('#account-trial-checkbox > input').is(':checked'),
			salesperson_id: jQuery('#account-sales-person > input').combobox('getValue'),
			level: jQuery('#user-roles > input').combobox('getValue')
        };

        if (isEdit) { // New users cannot subscribe for yearly paid support.
            jQuery.extend(adminObj, {
                paid_support: jQuery('#paid-support-for > input').combobox('getValue'),
            });
        }

        jQuery.extend(obj, adminObj);
    }

    if (isSuperAdmin || userLevel == USERS_SUPPORT_FLAG || userLevel == USERS_SALES_FLAG) {
        var subscriptions = getSubscriptionUsageValues();
        if (subscriptions) {
            jQuery.extend(obj, { subscriptions });
        }
    }

	return obj;
}

function paidSupportIsValid() {
    return jQuery('#paid-support-for > input').combobox('isValid') ||
        jQuery('#paid-support-dates-checkbox > input').is(':checked') &&
        jQuery('#paid-support-start-date > input').datebox({ required: true }) &&
        jQuery('#paid-support-due-date > input').datebox({ required: true });
}

function validateRequiredFields() {
	var isAddInfoTabVisible = jQuery('#users-rights-tabs').tabs('getTab', UR_TAB_ADDITIONAL_INFO);
	var isValid = jQuery('#username > input').validatebox('isValid') &&
        jQuery('#pwd').validatebox('isValid') &&
        jQuery('#email > input').validatebox('isValid') &&
        jQuery('#name > input').validatebox('isValid') &&
        (!isAddInfoTabVisible || paidSupportIsValid()) &&
        (!isAddInfoTabVisible || jQuery('#account-sales-person > input').combobox('isValid'));

    return isValid;
}

function loginAsUser(username) {
	TF.Rpc.Users.UsersMainGrid.loginAsUser(username)
		.done(function (data) {
			if (data === 200) {
				var baseUrl = jQuery('base').attr('href');
				location.replace(baseUrl);
			}
		})
		.fail(function (errorObj) {
			jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
		});
}

function getCheckedUsers(selectOne = false) {
    const getChecked = jQuery('#users-tables').datagrid('getChecked');

    if (getChecked && getChecked.length) {
        return selectOne ? [ getChecked[0] ] : getChecked;
    }

    return [];
}

