var payrollEgnFilters = [];
var payrollEIKFilters = [];
var businessYear;
var newFarmingYearComboboxData  = [];

jQuery(function () {
    TF.Rpc.Common.CombinedComboboxData.read(
        false, ['getFarmingYearWithCalendarYearSelectedCombobox'], {selected: 'current'}
    ).done(function (data) {

        data.getFarmingYearWithCalendarYearSelectedCombobox.forEach(function (el) {
            if (el.selected === true) {
                businessYear = el.year;
            }
            if (el.id !== '') {
                newFarmingYearComboboxData.push(el);
            }
        });
        initOwnersPayrollFields();
    }).fail(function (error) {
        jQuery.messager.alert('Грешка', error.getMessage(), 'warning');
    });
    setUserLastLogin();
});

//filter for owners payroll grid
function filterOwnersPayroll() {
    let filters = getPayrollFilters();
    jQuery('#payroll-tables').treegrid({
        rpcParams: [filters],
        onLoadSuccess: function(row, data) {
            if(data.total !== undefined && data.total === 0){
                jQuery.messager.alert('Внимание', 'Няма намерени записи', 'warning');
            }          
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function getPayrollFilters() {
    let farming_year = getPayrollFarmingYear();

    return {
        type: 'owners',
        payroll_from_date: farming_year.start_date,
        payroll_to_date: farming_year.end_date,
        farming_year: farming_year.id,
        payroll_ekate: jQuery('#choose-payroll-ekate').combobox('getValues'),
        payroll_farming: jQuery('#choose-payroll-farming').combobox('getValues'),
        owner_type: jQuery('#search-payroll-owner-type').combobox('getValue'),
        owner_names: jQuery('#search-payroll-owner-name').val(),
        egn: jQuery('#search-payroll-owner-egn').val(),
        eik: jQuery('#search-payroll-company-eik').val(),
        company_name: jQuery('#search-payroll-company-name').val(),
        rep_names: jQuery('#search-payroll-represent-name').val(),
        rep_egn: jQuery('#search-payroll-represent-egn').val(),
        heritor_names: jQuery('#search-payroll-heritor-name').val(),
        heritor_egn: jQuery('#search-payroll-heritor-egn').val(),
        rep_rent_place: jQuery('#search-payroll-represent-renta-place').combobox('getValue'),
        rent_place: jQuery('#search-payroll-owner-renta-place').combobox('getValue'),
        owner_egns: arrayColumn(payrollEgnFilters, 'value'),
        company_eiks: arrayColumn(payrollEIKFilters, 'value')
    };
}

function getPayrollFarmingYear() {
    let farming_year = {};
    newFarmingYearComboboxData.forEach(function (el) {
		if (el.id == jQuery('#payroll-search-year').combobox('getValue')) {
			farming_year = el;
		}
	});
    return farming_year;
}

function cleanOwnersPayrollFilter() {
    
    jQuery('#choose-payroll-ekate').combobox('reset');
    jQuery('#choose-payroll-farming').combobox('setValue', '');
    jQuery('#search-payroll-owner-type').combobox('reset');
    jQuery('#search-payroll-owner-name').val('');
    jQuery('#search-payroll-owner-egn').val('');
    jQuery('#search-payroll-company-eik').val('');
    jQuery('#search-payroll-company-name').val('');
    jQuery('#search-payroll-represent-name').val('');
    jQuery('#search-payroll-represent-egn').val('');
    jQuery('#search-payroll-heritor-name').val('');
    jQuery('#search-payroll-heritor-egn').val('');
    jQuery('#search-payroll-represent-renta-place').combobox('reset');
    jQuery('#search-payroll-owner-renta-place').combobox('reset');

    payrollEgnFilters = [];
    visualizeEGNFilterValues();

    payrollEIKFilters = [];
    visualizeEIKFilterValues();

    let farming_year = getCalendarFarmingYearId();
    let currentSelectedFarmingYear = jQuery("#payroll-search-year").combobox("getValue");
    if(farming_year == currentSelectedFarmingYear) {
        initOwnersPayrollGrid()
    } else {
        jQuery("#payroll-search-year").combobox("select", farming_year);
    }
}

function initOwnersPayrollFields() {
    var dataTo = businessYear + '-09-30';
    var dataFrom = (businessYear - 1) + '-10-01';

    jQuery('#choose-payroll-from-date').datebox({
        requred: true,
        missingMessage: 'Моля въведете дата.',
        value: dataFrom
    });

    jQuery('#choose-payroll-to-date').datebox({
        requred: true,
        missingMessage: 'Моля въведете дата.',
        value: dataTo
    });

    jQuery('#search-payroll-owner-type').combobox({
        data: [
            {
                id: '0,1',
                title: 'Всички',
                selected: true
            }, {
                id: '0',
                title: 'Юридически лица',
            }, {
                id: '1',
                title: 'Физически лица',
            },
        ],
        valueField: 'id',
        textField: 'title',
        editable: false,
        multiple: false,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-payroll-represent-renta-place').combobox({
        url: 'index.php?common-rpc=ekate-combobox',
        valueField: 'ekate',
        textField: 'text',
        value: '',
        rpcParams: [{
            record_all: true,
            selected: true
        }],
        filter: function (q, row) {
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if (text.indexOf(find) != -1 || value.indexOf(find) != -1) {
                return true;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-payroll-owner-renta-place').combobox({
        url: 'index.php?common-rpc=ekate-combobox',
        valueField: 'ekate',
        textField: 'text',
        value: '',
        rpcParams: [{
            record_all: true,
            selected: true
        }],
        filter: function (q, row) {
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if (text.indexOf(find) != -1 || value.indexOf(find) != -1) {
                return true;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#choose-payroll-farming').combobox({
        editable: false,
        url: 'index.php?common-rpc=farming-combobox',
        valueField: 'id',
        textField: 'name',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        rpcParams: [{
            record_all: true,
            selected: true
        }],
        onSelect: onComboMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#choose-payroll-ekate').combobox({
        url: 'index.php?common-rpc=ekate-combobox',
        valueField: 'ekate',
        textField: 'text',
        multiple: true,
        value: '',
        rpcParams: [{
            record_all: true,
            selected: true
        }],
        filter: function (q, row) {
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if (text.indexOf(find) != -1 || value.indexOf(find) != -1) {
                return true;
            }
        },
        onSelect: onComboMultiSelect,
        onChange: function (newValue) {
            if (newValue.length >= 1 && newValue[0] !== '') {
                // jQuery('#ekatteFilterMsg').show();
            } else {
                // jQuery('#ekatteFilterMsg').hide();
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    
    initPayrollExportDragAndDrop();

    var payrol_print = jQuery('#win-choose-payroll-print');
	var payrol_print_accept = jQuery('#print_export_payroll');
    jQuery('#btnfilterpayroll').bind('click', function() {
        jQuery('#win-payroll-filter').window('open');
    });
    jQuery('#btnclearfilterpayroll').bind('click', function() {
        cleanOwnersPayrollFilter();
    });
    jQuery('#btnprintpayroll').bind('click', function() {
        payrol_print.window({iconCls: 'icon-print', title: 'Отпечатване на ведомост'});
					payrol_print_accept.linkbutton({
						iconCls: 'icon-print', text: 'Отпечатай', onClick: function(){ printOrExportPayroll('print') }
					});
					payrol_print.window('open');
    });
    jQuery('#btnexportpayroll').bind('click', function() {
        initPayrollExportTables();
    });
    jQuery('#btntotalpayroll').bind('click', function() {
        jQuery('#win-owners-payroll-total-grid').window('open');
		initOwnersPayrollTotalGrid();
    });
    jQuery('#payroll-search-year').combobox({
        data: newFarmingYearComboboxData,
        valueField: 'id',
        textField: 'farming_year',
        editable: false,
        onChange: function(newValue,oldValue) {
            if(newValue && oldValue){
                initOwnersPayrollGrid();
            }
        },
        onLoadSuccess: function() {
            initOwnersPayrollGrid();
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initPayrollExportDragAndDrop() {

    jQuery('.drag').draggable({
        proxy: 'clone',
        revert: true,
        cursor: 'pointer',
        onStartDrag: function () {
            jQuery(this).draggable('options').cursor = 'not-allowed';
            jQuery(this).draggable('proxy').addClass('dp');
        },
        onStopDrag: function () {
            jQuery(this).draggable('options').cursor = 'pointer';
        }
    });

    jQuery('#payroll-export-drop-target, #payroll-export-drop-source').droppable({
        accept: '.drag',
        onDragEnter: function (e, source) {
            jQuery(source).draggable('options').cursor = 'pointer';
            jQuery(source).draggable('proxy').css('border', '1px solid #8fcf47');
            jQuery(this).addClass('over');
        },
        onDragLeave: function (e, source) {
            jQuery(source).draggable('options').cursor = 'not-allowed';
            jQuery(source).draggable('proxy').css('border', '1px solid #ccc');
            jQuery(this).removeClass('over');
        },
        onDrop: function (e, source) {
            jQuery(this).append(source);
            jQuery(this).removeClass('over');
        }
    });
}

function initPayrollExportDragItems() {
    TF.Rpc.Common.RentaTypeGrid.read()
        .done(function (data) {
            if (jQuery('#renta-natura-source').children().length === 0) {
                for (var i = data.rows.length - 1; i >= 0; i--) {
                    jQuery('#renta-natura-source').append('<div data-position="' + (i + 1) + '" data="' + data.rows[i]['id'] + '" class="drag dragable-el-backg">' + data.rows[i]['name'] + '</div>');
                }
            }
            initRentaNaturaDragAndDropColumns();
        })
        .fail(function (data) {
            RpcErrorHandler.show(data);
        });
}

function addColumnsPlotsDetailed() {
    var columns = jQuery('#payroll-export-drop-target .drag');

    if (columns.length == 0) {
        jQuery.messager.alert('Грешка', 'Моля преместете желаните колони в десния списък.', 'error');
        return;
    }

    var str_elements = '[[imoti_podrobno';
    jQuery.each(columns, function (index, value) {
        str_elements += ' ' + jQuery(value).attr('data');
    });
    str_elements += ']]';

    var editor = tinyMCE.get('template-content');
    editor.selection.collapse(true);
    editor.execCommand("mceInsertContent", false, str_elements);

    jQuery('#win-plots-detailed').window('close');
}

function getTypeAheadOwnersDataSource(type) {
    return new Bloodhound({
        datumTokenizer: Bloodhound.tokenizers.whitespace("value"),
        queryTokenizer: Bloodhound.tokenizers.whitespace,
        remote: {
            url: "index.php?owners-rpc=owners-list",
            cache: false,
            prepare: function (query, settings) {
                settings.query = query;
                return settings;
            },
            transport: function (settings, onSuccess, onError) {
                TF.Rpc.Owners.OwnersList.readOwnersList(type, settings.query)
                    .done(data => onSuccess(data))
                    .fail(fail);

                function fail(request, textStatus, errorThrown) {
                    onError(errorThrown);
                }
            }
        }
    });
}

function initPayrollEGNFilters() {
    jQuery('#win-payroll-egn-filter').window('open');
    jQuery('#search-payroll-egn-owner-egn').val('');
    var lastSelectedOwner;
    var hasSelectedValue = false;

    var searchPayrollByOwnerEgnEl = jQuery('#search-payroll-egn-owner-egn');
    var addPayrollOwnerEgn = jQuery("#add-payroll-owner-egn");

    searchPayrollByOwnerEgnEl
        .typeahead(
            {
                hint: true,
                highlight: false,
                minLength: 1,
                limit: 10,
                //   menu: jQuery("#search-payroll-egn-owner-egn-typeahead-target")
            },
            {
                name: "owners",
                displayKey: "value",
                source: getTypeAheadOwnersDataSource(
                    "owners_egn",
                ).ttAdapter()
            }
        )
        .unbind("typeahead:open")
        .unbind("typeahead:active")
        .unbind("typeahead:selected")
        .unbind("typeahead:asyncreceive");

    searchPayrollByOwnerEgnEl.on("typeahead:select", function (
        e,
        datum
    ) {
        lastSelectedOwner = datum;
        hasSelectedValue = true;
    });

    searchPayrollByOwnerEgnEl.on("typeahead:close", function (
        e
    ) {
        if (!lastSelectedOwner) {
            searchPayrollByOwnerEgnEl.typeahead('val', '');
        }
        hasSelectedValue = false;
    });
    addPayrollOwnerEgn.off("click");
    addPayrollOwnerEgn.on("click", addCurrentOwnerToFilter);

    function addCurrentOwnerToFilter() {
        if (!lastSelectedOwner) {
            return;
        }
        var egn = lastSelectedOwner.egn;
        var temp = {};
        temp.value = egn;
        temp.label = lastSelectedOwner.value;
        payrollEgnFilters.push(temp);
        lastSelectedOwner = null;
        jQuery('#search-payroll-egn-owner-egn').val('');
        jQuery('#search-payroll-egn-owner-egn').typeahead('val', '');
        jQuery('#search-payroll-owner-egn').val('От списък');
        visualizeEGNFilterValues();
    }
}


function clearPayrollEgnFilters() {
    payrollEgnFilters = [];
    visualizeEGNFilterValues();
}

function visualizeEGNFilterValues() {
    'use strict';

    var html = '';
    var text = '';
    jQuery('#payroll-egn-fields').html('');
    if (payrollEgnFilters.length === 0) {
        html = '&nbsp;Няма зададени филтри';
        jQuery('#search-payroll-owner-egn').val('');
    } else {
        payrollEgnFilters.forEach(function (el) {
            html += '<span class="filter-bubble">' + el.label +
                '<a class="remove-filter" data-ownership="payroll-init" data-filterValue="' +
                el.value + '" ></a></span>';
        });
    }

    jQuery('#payroll-egn-fields').html(html);

    jQuery('a[data-ownership="payroll-init"]').bind('click', function () {
        removeCurrentEGNFilterElement(this);
    });
}

function initPayrollEIKFilters() {
    jQuery('#win-payroll-eik-filter').window('open');
    jQuery('#search-payroll-eik-owner-egn').val('');
    var addPayrollOwnerEik = jQuery("#add-payroll-owner-eik");
    var searchPayrollEikOwnerEik = jQuery("#search-payroll-eik-owner-eik");
    var lastSelectedOwner;
    var hasSelectedValue = false;

    searchPayrollEikOwnerEik.typeahead(
        {
            hint: false,
            highlight: false,
            minLength: 1,
            limit: 10,
            // menu: jQuery("#search-payroll-eik-company-typeahead-target")
        },
        {
            name: "owners",
            displayKey: "value",
            source: getTypeAheadOwnersDataSource('companies_eik').ttAdapter()
        }
    );
    searchPayrollEikOwnerEik.on("typeahead:select", function (
        e,
        datum
    ) {
        lastSelectedOwner = datum;
        hasSelectedValue = true;
    });

    searchPayrollEikOwnerEik.on("typeahead:close", function (
        e
    ) {
        if (!lastSelectedOwner) {
            searchPayrollEikOwnerEik.typeahead('val', '');
        }
        hasSelectedValue = false;
    });
    addPayrollOwnerEik.off("click");
    addPayrollOwnerEik.on("click", addCurrentCompanyToFilter);

    function addCurrentCompanyToFilter() {
        if (!lastSelectedOwner) {
            return;
        }
        var temp = {};
        temp.value = lastSelectedOwner.eik;
        temp.label = lastSelectedOwner.value;
        payrollEIKFilters.push(temp);
        lastSelectedOwner = null;
        jQuery('#search-payroll-eik-owner-eik').val('');
        jQuery('#search-payroll-eik-owner-eik').typeahead('val', '');
        jQuery('#search-payroll-company-eik').val('От списък');
        visualizeEIKFilterValues();
    }

}

function clearPayrollEikFilters() {
    payrollEIKFilters = [];
    visualizeEIKFilterValues();
}

function visualizeEIKFilterValues() {
    'use strict';

    var html = '',
        text = '';
    jQuery('#payroll-eik-fields').html('');
    if (payrollEIKFilters.length === 0) {
        html = '&nbsp;Няма зададени филтри';
        jQuery('#search-payroll-company-eik').val('');
    } else {
        payrollEIKFilters.forEach(function (el) {
            html += '<span class="filter-bubble">' + el.label + '<a class="remove-filter" data-ownership="payroll-eik-init" data-filterValue="' + el.value + '" ></a></span>';
        });
    }

    jQuery('#payroll-eik-fields').html(html);

    jQuery('a[data-ownership="payroll-eik-init"]').bind('click', function () {
        removeCurrentEIKFilterElement(this);
    });
}

function removeCurrentEIKFilterElement(element) {
    'use strict';
    var index = -1,
        i = 0;

    for (i = 0; i < payrollEIKFilters.length; i = i + 1) {
        if (payrollEIKFilters[i] === jQuery(element).data('filtervalue')) {
            index = i;
        }
    }

    payrollEIKFilters.splice(index, 1);
    visualizeEIKFilterValues();
}

function removeCurrentEGNFilterElement(element) {
    'use strict';
    var index = -1,
        i = 0;

    for (i = 0; i < payrollEgnFilters.length; i = i + 1) {
        if (payrollEgnFilters[i].value == jQuery(element).data('filtervalue')) {
            index = i;
        }
    }

    payrollEgnFilters.splice(index, 1);
    visualizeEGNFilterValues();
}

function arrayColumn(array, columnName) {
    return array.map(function (value, index) {
        return value[columnName];
    })
}
