const MASS_PAYMENT_TYPE_DSK = "dskxml";
const MASS_PAYMENT_TYPE_BULBANK = "bulbanktxt";

function initOwnersPayrollGrid() {	
	let farming_year = getPayrollFarmingYear();

	jQuery('#payroll-tables').treegrid({
		url: 'index.php?payroll-rpc=payroll-grid',
		iconCls: 'icon-edit-geometry',
		nowrap: true,
		autoRowHeight: true,
		title: 'Ведомост по собственици',
		striped: true,
		pageSize: 30,
		fit: true,
		fitColumns: false,
		showFooter: true,
		singleSelect: true,
		idField: 'id',
		sortName: 'owner_names',
		border: true,
		sortOrder: 'asc',
		treeField: 'owner_names',
		rpcParams: [{
			type: 'owners',
			payroll_from_date: farming_year.start_date,
			payroll_to_date: farming_year.end_date,
			payroll_ekate: '',
			farming_year: farming_year.id
		}],
		frozenColumns: [[
				{
					field: 'ck',
					checkbox: true
				}
			]],
		columns: [
			[
				{
					field: 'owner_names',
					title: '<b>Собственик</b>',
					sortable: true,
					width: 300,
					rowspan: 2
				}, {
					field: 'egn_eik',
					title: '<b>ЕГН/ЕИК</b>',
					sortable: true,
					width: 100,
					rowspan: 2
				}, {
					title: '<b>Място за <br/>получаване</b>',
					align: 'center',
					field: 'rent_place_name',
					width: 100,
					rowspan: 2
				}, {
					field: 'all_owner_area',
					title: '<b>Притежавана<br/>площ(дка)</b>',
					align: 'center',
					sortable: true,
					width: 80,
					rowspan: 2
				}, 
				{
					field: 'pu_area',
					title: '<b>Площ за лично<br/>ползване(дка)</b>',
					align: 'center',
					sortable: true,
					width: 100,
					rowspan: 2
				}, 
				{
					field: 'owner_area',
					title: '<b>Използвана<br/>площ(дка)</b>',
					align: 'center',
					sortable: true,
					width: 100,
					rowspan: 2
				}, 
				{
				field: 'cultivated_area',
				title: '<b>Обработваема  <br/>площ</b>',
				align: 'center',
				sortable: true,
				width: 100,
				rowspan: 2
				},
				{
					title: '<b>Рента по договор</b>',
					align: 'center',
					colspan: 2
				}, {
					title: '<b>Рента начислена</b>',
					align: 'center',
					colspan: 2
				}, {
					title: '<b>Платена рента</b>',
					align: 'center',
					colspan: 5
				}, {
					title: '<b>Остатък</b>',
					align: 'center',
					colspan: 3
				}, {
					title: '<b>Надплатено</b>',
					align: 'center',
					colspan: 2
				}
			],
			[
				{
					field: 'renta_txt',
					title: '<b>в пари</b>',
					align: 'center',
					sortable: false,
					width: 105
				},
				{
					field: 'renta_nat_with_type',
					title: '<b>в натура</b>',
					align: 'center',
					sortable: false,
					width: 145
				},
				{
					field: 'charged_renta_txt',
					title: '<b>в пари</b>',
					align: 'center',
					sortable: false,
					width: 105
				},
				{
					field: 'charged_renta_nat_with_type',
					title: '<b>в натура</b>',
					align: 'center',
					sortable: false,
					width: 145
				},
				{
					field: 'paid_renta_txt',
					title: '<b>в пари</b>',
					align: 'center',
					sortable: false,
					width: 75,
					styler: function(value, row, index) {
						if (parseFloat(value) < 0) {
							return 'background-color:red;';
						}
					}
				},
				{
					field: 'paid_renta_by_txt',
					title: '<b>чрез</b>',
					align: 'center',
					sortable: false,
					width: 145
				},
				{
					field: 'paid_renta_nat_with_type',
					title: '<b>в натура</b>',
					align: 'center',
					sortable: false,
					width: 145
				},
				{
					field: 'paid_renta_nat_by',
					title: '<b>чрез</b>',
					align: 'center',
					sortable: false,
					width: 145
				},
				{
					field: 'paid_renta_nat_by_detailed',
					title: '<b>детайлно</b>',
					align: 'center',
					sortable: false,
					width: 145
				},
				{
					field: 'unpaid_renta_txt',
					title: '<b>в пари</b>',
					align: 'center',
					sortable: false,
					width: 80
				},
				{
					field: 'unpaid_renta_nat',
					title: '<b>в натура</b>',
					align: 'center',
					sortable: false,
					width: 145
				},
				{
					field: 'unpaid_renta_nat_unit_value',
					title: '<b>в пари (ед. ст.)</b>',
					align: 'center',
					sortable: false,
					width: 100
				},
				{
					field: 'over_paid_txt',
					title: '<b>в пари</b>',
					align: 'center',
					sortable: false,
					width: 80
				},
				{
					field: 'over_paid_nat',
					title: '<b>в натура</b>',
					align: 'center',
					sortable: false,
					width: 145
				}
			]
		],
		pagination: true,
		rownumbers: true,
		toolbar: '#payroll-toolbar',
		onBeforeLoad: function() {
			jQuery(this).treegrid('clearChecked');
		},
		onLoadSuccess: function() {
			var ownerData = jQuery(this).treegrid('getData');

			if (ownerData[0])
			{
                jQuery(this).treegrid('select', ownerData[0]['id']);
			} else {
				initPayrollByOwnerGrid(0, false);
			}

            jQuery(this).treegrid('resize')
		},
		onSelect: function(node) {
			all_renta = {};
			all_renta.plots_contracts_renta_down_grid = node.plots_contracts_renta_down_grid;
			all_renta.plots_contracts_charged_renta_down_grid = node.plots_contracts_charged_renta_down_grid;
			all_renta.plots_contracts_renta_nat_down_grid = node.plots_contracts_renta_nat_down_grid;
			all_renta.plots_contracts_charged_renta_nat_down_grid = node.plots_contracts_charged_renta_nat_down_grid;

			var total_area = node.total_area_contract;

			initPayrollByOwnerGrid(node.owner_id, node.is_heritor, node.root_id, node.path, all_renta, total_area);
		},
		// view: footerOwnersPayrollView,
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function initOwnersPayrollTotalGrid() {
	let farming_year;
	farming_year = getPayrollFarmingYear();

	const rpcParams = {
		type: 'sums',
		farming_year: farming_year.id,
		payroll_from_date: farming_year.start_date,
		payroll_to_date: farming_year.end_date,
		payroll_ekate: ''
	};

	const payrollEkatte = jQuery('#choose-payroll-ekate').combobox('getValues');
	if (payrollEkatte && payrollEkatte.length && payrollEkatte[0] !== '') {
		rpcParams.payroll_ekate = payrollEkatte;
	}

	const payrollFarming =  jQuery('#choose-payroll-farming').combobox('getValues')
	if (payrollFarming && payrollFarming.length && payrollFarming[0] !== '') {
		rpcParams.payroll_farming = payrollFarming;
	}

	const ownerType = jQuery('#search-payroll-owner-type').combobox('getValue');
	if (ownerType) {
		rpcParams.owner_type = ownerType;
	}

	const ownerNames = jQuery('#search-payroll-owner-name').val();
	if (ownerNames) {
		rpcParams.owner_names = ownerNames;
	}

	const egn = jQuery('#search-payroll-owner-egn').val();
	if (egn) {
		rpcParams.egn = egn;
	}

	const eik = jQuery('#search-payroll-company-eik').val();
	if (eik) {
		rpcParams.eik = eik;
	}

	const companyName = jQuery('#search-payroll-company-name').val();
	if (companyName) {
		rpcParams.company_name = companyName;
	}

	const repNames = jQuery('#search-payroll-represent-name').val();
	if (repNames) {
		rpcParams.rep_names = repNames;
	}

	const repEgn = jQuery('#search-payroll-represent-egn').val();
	if (repEgn) {
		rpcParams.rep_egn = repEgn;
	}

	const heritorNames = jQuery('#search-payroll-heritor-name').val();
	if (heritorNames) {
		rpcParams.heritor_names = heritorNames;
	}

	const heritorEgn = jQuery('#search-payroll-heritor-egn').val();
	if (heritorEgn) {
		rpcParams.heritor_egn = heritorEgn;
	}

	const repRentPlace = jQuery('#search-payroll-represent-renta-place').combobox('getValue');
	if (repRentPlace) {
		rpcParams.rep_rent_place = repRentPlace;
	}

	const rentPlace = jQuery('#search-payroll-owner-renta-place').combobox('getValue');
	if (rentPlace) {
		rpcParams.rent_place = rentPlace;
	}

	const ownerEgns = arrayColumn(payrollEgnFilters, 'value')
	if (ownerEgns && ownerEgns.length) {
		rpcParams.owner_egns = ownerEgns;
	}

	const companyEiks = arrayColumn(payrollEIKFilters, 'value');
	if (companyEiks && companyEiks.length) {
		rpcParams.company_eiks = companyEiks;
	}

	jQuery('#owners-payroll-total-grid').datagrid({
		iconCls: 'icon-sum',
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		fit: true,
		fitColumns: false,
		idField: '',
		border: true,
		url: 'index.php?payroll-rpc=payroll-grid',
		rpcParams: [rpcParams],
		columns: [
			[
				{
					field: 'area_type',
					title: '<b></b>',
					align: 'center',
					width: 140,
					rowspan: 2
				}, 
				{
					field: 'all_owner_area',
					title: '<b>Притежавана площ<br/>(дка)</b>',
					sortable: false,
					width: 80,
					rowspan: 2
				}, {
					field: 'pu_area',
					title: '<b>Площ за лично<br/>ползване (дка)</b>',
					sortable: false,
					width: 100,
					rowspan: 2
				}, {
					field: 'owner_area',
					title: '<b>Изплозвана площ<br/>(дка)</b>',
					sortable: false,
					width: 80,
					rowspan: 2
				},  
				{
					field: 'plot_rent_txt',
					title: '<b>Цена/дка</b>',
					sortable: true,
					width: 80,
					rowspan: 2
				}, {
					title: '<b>Рента по договор</b>',
					align: 'center',
					colspan: 2
				}
				, {
					title: '<b>Рента начислена</b>',
					align: 'center',
					colspan: 3
				}
				, {
					title: '<b>Данни за договори</b>',
					align: 'center',
					colspan: 5
				}
				, {
					title: '<b>Платена рента за договор</b>',
					align: 'center',
					colspan: 5
				}
				, {
					title: '<b>Остатък за договор</b>',
					align: 'center',
					colspan: 3
				}
				, {
					title: '<b>Надплатено</b>',
					align: 'center',
					colspan: 2
				}
			], [
				{
					field: 'renta_txt',
					title: '<b>в пари</b>',
					align: 'center',
					sortable: false,
					width: 80
				}, {
					field: 'renta_nat_with_type',
					title: '<b>в натура</b>',
					align: 'center',
					sortable: false,
					width: 120
				}
				, {
					field: 'contract_renta_txt',
					title: '<b>в пари</b>',
					align: 'center',
					sortable: false,
					width: 80
				}, {
					field: 'charged_renta_nat_text',
					title: '<b>в натура</b>',
					align: 'center',
					sortable: false,
					width: 120
				} 
				,{
					field: 'renta_nat_type',
					title: '<b>тип</b>',
					align: 'center',
					sortable: false,
					width: 150
				}
				, {
					field: 'c_num',
					title: '<b>Договор №</b>',
					align: 'center',
					sortable: true,
					width: 110
				},
				{
					field: 'contract_type',
					title: '<b>Ползване</b>',
					align: 'center',
					sortable: false,
					width: 100
				}
				,{
					field: 'contract_due_date',
					title: '<b>Валиден до</b>',
					align: 'center',
					sortable: true,
					width: 100
				}
				, {
					field: 'osz_num',
					title: '<b>Вписване<br/>№</b>',
					align: 'center',
					sortable: true,
					width: 80
				}, 
				{
					field: 'osz_date',
					title: '<b>Дата на<br/>вписване</b>',
					align: 'center',
					sortable: true,
					width: 100
				} 
				, {
					field: 'paid_renta_txt',
					title: '<b>в пари</b>',
					align: 'center',
					sortable: false,
					width: 75,
					styler: function(value, row, index) {
						if (parseFloat(value) < 0) {
							return 'background-color:red;';
						}
					}
				},
				{
					field: 'paid_renta_by_txt',
					title: '<b>чрез</b>',
					align: 'center',
					sortable: false,
					width: 145
				},
				{
					field: 'paid_renta_nat',
					title: '<b>в натура</b>',
					align: 'center',
					sortable: false,
					width: 145
				},
				{
					field: 'paid_renta_nat_by',
					title: '<b>чрез</b>',
					align: 'center',
					sortable: false,
					width: 145
				},
				{
					field: 'paid_renta_nat_by_detailed',
					title: '<b>детайлно</b>',
					align: 'center',
					sortable: false,
					width: 145
				},
				{
					field: 'unpaid_renta_txt',
					title: '<b>в пари</b>',
					align: 'center',
					sortable: false,
					width: 80
				},
				{
					field: 'unpaid_renta_nat',
					title: '<b>в натура</b>',
					align: 'center',
					sortable: false,
					width: 145
				},
				{
					field: 'unpaid_renta_nat_unit_value',
					title: '<b>в пари (ед. ст.)</b>',
					align: 'center',
					sortable: false,
					width: 100
				},
				{
					field: 'over_paid_txt',
					title: '<b>в пари</b>',
					align: 'center',
					sortable: false,
					width: 145
				},
				{
					field: 'over_paid_nat_text',
					title: '<b>в натура</b>',
					align: 'center',
					sortable: false,
					width: 100
				},
			]
		],
		// columns: [
		// 	[
		// 		{
		// 			field: 'type',
		// 			title: '<b>Вид</b>',
		// 			align: 'center',
		// 			sortable: true,
		// 			width: 90,
		// 			rowspan: 2
		// 		},
		// 		{
		// 			title: '<b>Обща площ</b>',
		// 			align: 'center',
		// 			width: 120,
		// 			colspan: 2
		// 		}, {
		// 			title: '<b>Рента в лева</b>',
		// 			align: 'center',
		// 			colspan: 2
		// 		}, {
		// 			title: '<b>Рента в натура</b>',
		// 			align: 'center',
		// 			colspan: 2
		// 		}, {
		// 			title: '<b>Платена рента</b>',
		// 			align: 'center',
		// 			colspan: 5
		// 		}, {
		// 			title: '<b>Остатък</b>',
		// 			align: 'center',
		// 			colspan: 3
		// 		}, {
		// 			title: '<b>Надплатено</b>',
		// 			align: 'center',
		// 			colspan: 2
		// 		}
		// 	],
		// 	[
		// 		{
		// 			field: 'area_by_owners',
		// 			title: '<b>по собственици</b>',
		// 			align: 'center',
		// 			sortable: false,
		// 		},
		// 		{
		// 			field: 'area_by_heritors',
		// 			title: '<b>по наследници</b>',
		// 			align: 'center',
		// 			sortable: false,
		// 		},
		// 		{
		// 			field: 'renta',
		// 			title: '<b>по договор</b>',
		// 			align: 'center',
		// 			sortable: false,
		// 			width: 80
		// 		},
		// 		{
		// 			field: 'charged_renta',
		// 			title: '<b>начислена</b>',
		// 			align: 'center',
		// 			sortable: false,
		// 			width: 80
		// 		},
		// 		{
		// 			field: 'renta_nat_text',
		// 			title: '<b>по договор</b>',
		// 			align: 'center',
		// 			sortable: false,
		// 			width: 140
		// 		},
		// 		{
		// 			field: 'charged_renta_nat_text',
		// 			title: '<b>начислена</b>',
		// 			align: 'center',
		// 			sortable: false,
		// 			width: 140
		// 		},
		// 		{
		// 			field: 'paid_renta',
		// 			title: '<b>в лева</b>',
		// 			align: 'center',
		// 			sortable: false,
		// 			width: 70,
		// 			styler: function(value, row, index) {
		// 				if (parseFloat(value) < 0) {
		// 					return 'background-color:red;';
		// 				}
		// 			}
		// 		},
		// 		{
		// 			field: 'paid_renta_by',
		// 			title: '<b>чрез</b>',
		// 			align: 'center',
		// 			sortable: false,
		// 			width: 145
		// 		},
		// 		{
		// 			field: 'paid_renta_nat',
		// 			title: '<b>в натура</b>',
		// 			align: 'center',
		// 			sortable: false,
		// 			width: 145
		// 		},
		// 		{
		// 			field: 'paid_renta_nat_by',
		// 			title: '<b>чрез</b>',
		// 			align: 'center',
		// 			sortable: false,
		// 			width: 145
		// 		},
		// 		{
		// 			field: 'paid_renta_nat_by_detailed',
		// 			title: '<b>детайлно</b>',
		// 			align: 'center',
		// 			sortable: false,
		// 			width: 145
		// 		},
		// 		{
		// 			field: 'unpaid_renta',
		// 			title: '<b>в лева</b>',
		// 			align: 'center',
		// 			sortable: false,
		// 			width: 80
		// 		},
		// 		{
		// 			field: 'unpaid_renta_nat',
		// 			title: '<b>в натура</b>',
		// 			align: 'center',
		// 			sortable: false,
		// 			width: 145
		// 		},
		// 		{
		// 			field: 'unpaid_renta_nat_unit_value',
		// 			title: '<b>в лева (ед. ст.)</b>',
		// 			align: 'center',
		// 			sortable: false,
		// 			width: 100
		// 		},
		// 		{
		// 			field: 'over_paid_renta',
		// 			title: '<b>в лева</b>',
		// 			align: 'center',
		// 			sortable: false,
		// 			width: 80
		// 		},
		// 		{
		// 			field: 'over_paid_renta_nat',
		// 			title: '<b>в натура</b>',
		// 			align: 'center',
		// 			sortable: false,
		// 			width: 145
		// 		}
		// 	]
		// ],
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function getPayrollExportColums() {
	let exportedColumns = {};
	exportedColumns.ownerEgnEik = jQuery('#ownerEgnEik').is(':checked');
	exportedColumns.inheritorOf = jQuery('#inheritorOf').is(':checked');
	exportedColumns.ownerPhone = jQuery('#ownerPhone').is(':checked');
	exportedColumns.ownerIban = jQuery('#ownerIban').is(':checked');
	exportedColumns.repNames = jQuery('#repNames').is(':checked');
	exportedColumns.repIban = jQuery('#repIban').is(':checked');
	exportedColumns.rentPlace = jQuery('#rentPlace').is(':checked');
	exportedColumns.contracts = jQuery('#contracts').is(':checked');
	exportedColumns.plots = jQuery('#plots').is(':checked');
	exportedColumns.plotDetailed = jQuery('#plotDetailed').is(':checked');
	exportedColumns.natsDetailed = jQuery('#natsDetailed').is(':checked');
	exportedColumns.contractArea = jQuery('#contractArea').is(':checked');
	exportedColumns.arableArea = jQuery('#arableArea').is(':checked');
	exportedColumns.personalUseArea = jQuery('#personalUseArea').is(':checked');

	localStorage.setItem("payrollExportColums", JSON.stringify(exportedColumns));

	return exportedColumns;
}

function initPayrollExportColums() {
	let exportedColumns = {};
	let exportedColumnsJson = localStorage.getItem("payrollExportColums");
	if (exportedColumnsJson) {
		exportedColumns = JSON.parse(exportedColumnsJson);
	}

	jQuery('#ownerEgnEik').prop('checked', exportedColumns.ownerEgnEik ?? false);
	jQuery('#inheritorOf').prop('checked', exportedColumns.inheritorOf ?? false);
	jQuery('#ownerPhone').prop('checked', exportedColumns.ownerPhone ?? false);
	jQuery('#ownerIban').prop('checked', exportedColumns.ownerIban ?? false);
	jQuery('#repNames').prop('checked', exportedColumns.repNames ?? false);
	jQuery('#repIban').prop('checked', exportedColumns.repIban ?? false);
	jQuery('#rentPlace').prop('checked', exportedColumns.rentPlace ?? false);
	jQuery('#contracts').prop('checked', exportedColumns.contracts ?? false);
	jQuery('#plots').prop('checked', exportedColumns.plots ?? false);
	jQuery('#plotDetailed').prop('checked', exportedColumns.plotDetailed ?? false);
	jQuery('#natsDetailed').prop('checked', exportedColumns.natsDetailed ?? false);
	jQuery('#contractArea').prop('checked', exportedColumns.contractArea) ?? false;
	jQuery('#arableArea').prop('checked', exportedColumns.arableArea ?? false);
	jQuery('#personalUseArea').prop('checked', exportedColumns.personalUseArea ?? false);
}

jQuery(document).on('click', '#saveExportPayroll', function() {
		let exeportedColumns = getPayrollExportColums();
		let exportParams = {
			columns: exeportedColumns,
			filters: getPayrollFilters()
		}

		TF.Rpc.Payroll
		.PayrollExportsGrid
		.export(exportParams)
		.done(function () {
			jQuery('#win-choose-payroll-print').window('close');
			jQuery('#payroll-export-tables-grid').datagrid('loadRpc');

		})
		.fail(function (errorObj) {});

		jQuery('#win-choose-payroll-print').window('close');
	});

jQuery(document).on('click', '#plotDetailed', function() {
	if(jQuery('#plotDetailed').is(':checked')) {
		jQuery('#plots').prop('checked', false);
		jQuery("#plots").attr("disabled", true);
	} else {
		jQuery("#plots").attr("disabled", false);
	}
});

jQuery(document).on('click', '#plots', function() {
	if(jQuery('#plots').is(':checked')) {
		jQuery('#plotDetailed').prop('checked', false);
		jQuery("#plotDetailed").attr("disabled", true);
	} else {
		jQuery("#plotDetailed").attr("disabled", false);
	}
});

function initPayrollExportTables() {
	var payrol_print = jQuery('#win-choose-payroll-print'),
		payrol_print_accept = jQuery('#print_export_payroll');
		date = new Date(),
        todayDate = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();

	jQuery('#win-payroll-export-tables').window('open');
	let  exportToolbar = [
		{
			id: 'btnexportpayrollqueue',
			text: 'Експорт',
			iconCls: 'icon-add',
			handler: function() {
				var data = jQuery('#payroll-export-tables-grid').datagrid('getData');

				if(data.export_pending) {
					jQuery.messager.alert('Грешка', 'Моля изчакайте да приключат всички стартирани операции, преди да започнете нова.', 'warning');
					return false;
				}

				initPayrollExportColums();
				
				payrol_print.window('open');
			}
		}, {
			id: 'btndeletepayrollexport',
			text: 'Изтриване',
			iconCls: 'icon-delete',
			handler: function() {
				var selected = jQuery('#payroll-export-tables-grid').datagrid('getSelected');

				if (selected) {
					TF.Rpc.Payroll.PayrollExportsGrid.delete(selected.id)
						.done(function (data) {
							jQuery('#payroll-export-tables-grid').datagrid('loadRpc');
						})
						.fail(function (errorObj) {
							jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
						});
				} else {
					jQuery.messager.alert('Грешка', 'Моля изберете запис, който да бъде изтрит.', 'warning');
				}
			}
		}
	];
	if(hasExportMassPaymentRights){
		exportToolbar.push(
			{
				id: 'btnexportpayrollmasspayment',
				text: 'Експорт на масов файл',
				iconCls: 'icon-export',
				handler: function () {
					var data = jQuery('#payroll-export-tables-grid').datagrid('getData');
					if (data.export_pending) {
						jQuery.messager.alert('Грешка', 'Моля изчакайте да приключат всички стартирани операции, преди да започнете нова.', 'warning');
						return false;
					}
	
					jQuery('#export-mass-payment-farming').combobox({
						url: 'index.php?common-rpc=farming-combobox',
						valueField: 'id',
						textField: 'company',
						rpcParams: [{ selected: true }],
						onSelect: function (value) {
							jQuery('#export-mass-payment-iban').combobox({
								rpcParams: [value.id, true],
							});
						},
						onLoadSuccess: function (data) {
							if (data.length){
								var opts = jQuery(this).combobox('options');
								jQuery(this).combobox('select', data[0][opts.valueField]);
							}
						},
						loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
						loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
					});
					jQuery('#export-mass-payment-iban').combobox({
						url: 'index.php?farming-rpc=farming-iban',
						rpcParams: [jQuery('#export-mass-payment-farming').combobox('getValue'), true],
						valueField: 'value',
						textField: 'text',
						loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
						loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
					});
	
					jQuery('#export-mass-payment-date').datebox({
						required: true,
						missingMessage: 'Моля изберете дата на плащане.',
						value: todayDate
					});
	
					jQuery('#win-payroll-export-mass-payment').window('open');
				}
			}
		);
	}

	jQuery('#payroll-export-tables-grid').datagrid({
		url: 'index.php?payroll-rpc=payroll-exports-grid',
		iconCls: 'icon-sum',
		pagination: true,
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		fit: true,
		fitColumns: true,
		pageSize: 20,
		showFooter: true,
		sortName: 'export_date',
		sortOrder: 'desc',
		singleSelect:true,
		idField: '',
		border: true,
		rpcParams: [{}],
		rownumbers: true,
		columns: [
			[
				{
					field: 'export_date',
					title: '<b>Дата</b>',
					align: 'center',
					sortable: false,
					width: 145
				}, {
					field: 'status_text',
					title: '<b>Статус</b>',
					align: 'center',
					sortable: false,
					width: 145,
					formatter: function (value, row) {
						if(row.status === 'validation_error' || (row.status === 'processed' && row.message != null)) {
							return '<a href="javascript:void(0)" class="validationErrorsInfoBtn" data-validation-error-info=\''+row.message+'\' onclick="showValidationErrorInfo(this)">'+value+'</a>';
						} else {
							return value;
						}
					}
				},
				{
					field: 'filter_params',
					title: '<b>Филтри</b>',
					align: 'center',
					sortable: false,
					width: 80,
					formatter: function (value, row) {
						return '<a href="javascript:void(0)" onclick="displayPayrollInfoPanel('+row.id+')" class="easyui-linkbutton easyui-tooltip" data-options="iconCls: \'icon-info\', position: \'top\', content: \'Зададени филтри\', plain: true"></a>';
					}
				},
				{
					field: 'link',
					title: '<b>Сваляне</b>',
					align: 'center',
					sortable: false,
					width: 145,
					formatter: function (value, row) {
						if (!row.masspayment_type) {
							var html = '',
								attachmentAvaliable = row.status != 'processed' ? true : false;
							return '<a href="'+row.link+'" target="_new" class="easyui-linkbutton easyui-tooltip" data-options="iconCls: \'icon-download-file\', position: \'top\', content: \'Сваляне на прикачен файл\', plain: true, disabled: '+attachmentAvaliable+'" download>Сваляне</a>';
						} else {
							var stringLength = row.link.length,
								xlsLink = row.link.substring(0, stringLength - 3) + 'xlsx',
								fileName = row.link.substring(row.link.length - 30),
								attachmentAvaliable = row.status != 'processed' ? true : false,
								text = '<a href="' + row.link + '" download="' + fileName + '" target="_new" class="easyui-linkbutton easyui-tooltip" data-options="iconCls: \'icon-download-file\', position: \'top\', content: \'Сваляне на прикачен файл\', plain: true, disabled: ' + attachmentAvaliable + '">Сваляне</a>';
							
							if (row.message == 'INVALID_IBAN_LENGTH') {
								attachmentAvaliable = false;
							}

							text += '<a href="' + xlsLink + '"  target="_new" class="easyui-linkbutton easyui-tooltip" data-options="iconCls: \'icon-csv\', position: \'top\', content: \'Сваляне на прикачен файл\', plain: true, disabled: ' + attachmentAvaliable + '">Преглед</a>';

							return text;
						}

					}
				}
			]
		],
		toolbar: exportToolbar,
		onLoadSuccess: function () {
			jQuery(this).datagrid('getPanel').find('a.easyui-linkbutton').linkbutton();
			jQuery(this).datagrid('getPanel').find('a.easyui-tooltip').tooltip();
			jQuery(this).datagrid('fixRowHeight');
			var inProgress = jQuery(this).datagrid('getData').export_pending;
			if (inProgress) {
				jQuery('#btnexportpayrollqueue').linkbutton('disable');
				jQuery('#payroll-export-tables-layout').layout('add',{
						region: 'south',
						height: 30,
						noheader: true,
						content: '&nbsp;&nbsp;&nbsp;&nbsp;В момента имате заявен експорт. След обработката му ще може да заявите нов.',
						bodyCls: 'datagrid-footer-padding',
						border: false,
						style: { 'border-top': '1px solid #000', 'padding-top': '5px'}
					});
			} else {
				jQuery('#btnexportpayrollqueue').linkbutton('enable');
				jQuery('#payroll-export-tables-layout').layout('remove', 'south');
			}
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function initFileDownload(link) {
	jQuery('#btn-download-file').attr("href", link);
	jQuery('#win-download').window('open');
}

function displayPayrollInfoPanel(id) {
	var data = jQuery('#payroll-export-tables-grid').datagrid('getData'),
		filters = {};

	data.rows.forEach(function (el) {
		if (el.id == id) {
			filters = el.export_params.filters;
		}
	});
	jQuery('#ipe-year').html(filters.farming_year + ' г.');
	jQuery('#ipe-owner-name').html(filters.owner_names);
	jQuery('#ipe-owner-egn').html(filters.egn);
	jQuery('#ipe-owner-place').html(filters.rent_place);
	jQuery('#ipe-rep-name').html(filters.rep_names);
	jQuery('#ipe-rep-egn').html(filters.rep_egn);
	jQuery('#ipe-rep-place').html(filters.rep_rent_place);
	jQuery('#ipe-heritor-name').html(filters.heritor_names);
	jQuery('#ipe-heritor-egn').html(filters.heritor_egn);
	jQuery('#ipe-company-name').html(filters.company_name);
	jQuery('#ipe-company-eik').html(filters.eik);
	jQuery('#ipe-start-date').html(filters.payroll_from_date);
	jQuery('#ipe-end-date').html(filters.payroll_to_date);
	jQuery('#ipe-plot-ekatte').html(filters.payroll_ekate);
	jQuery('#ipe-farmings').html(filters.payroll_farming.join());

	jQuery('#win-payroll-export-info').window('open');

	if (filters.owner_egns.length) {
		var html = jQuery('#ipe-owner-egn').html();
		html += '<a href="javascript:void(0)" style="margin-top: -2px;" onclick="displayPayrollEGNFilters('+id+')" class="easyui-linkbutton" data-options="iconCls: \'icon-info\', plain: true"></a>';
		jQuery('#ipe-owner-egn').html(html);
		jQuery('#ipe-owner-egn > a').linkbutton();

	}

	if (filters.company_eiks.length) {
		var html = jQuery('#ipe-company-eik').html();
		html += '<a href="javascript:void(0)" style="margin-top: -2px;" onclick="displayPayrollEIKFilters('+id+')" class="easyui-linkbutton" data-options="iconCls: \'icon-info\', plain: true"></a>';
		jQuery('#ipe-company-eik').html(html);
		jQuery('#ipe-company-eik > a').linkbutton();

	}

	jQuery('#win-payroll-export-info').window('open');
}

function showValidationErrorInfo(btn) {
	let data = jQuery(btn).data('validation-error-info');

	let message = '<ul style="height: 200px;">';
	for(const msg of data){
		message += '<li>'+msg+'</li>';
	}
	message += '</ul>';

	jQuery.messager.alert({
		title: 'Грешка',
		msg: message,
		icon: 'warning',
		width: 700
	});
}



function displayPayrollEGNFilters(id) {
	var data = jQuery('#payroll-export-tables-grid').datagrid('getData'),
		filters = [];

	data.rows.forEach(function (el) {
		if (el.id == id) {
			for (egn of el.filter_params.owner_egns) {
				filters.push(egn.label);
			}
		}
	});

	if (filters) {
		jQuery('#payroll-filter-egn-panel').html(filters.join(', '));
		jQuery('#win-payroll-filter-egn-panel').window('open');
	}
}

function displayPayrollEIKFilters(id) {
	var data = jQuery('#payroll-export-tables-grid').datagrid('getData'),
		filters = {};

	data.rows.forEach(function (el) {
		if (el.id == id) {
			filters = el.filter_params.company_eiks;
		}
	});

	if (filters) {
		jQuery('#payroll-filter-eik-panel').html(filters.join(', '));
		jQuery('#win-payroll-filter-eik-panel').window('open');
	}
}
