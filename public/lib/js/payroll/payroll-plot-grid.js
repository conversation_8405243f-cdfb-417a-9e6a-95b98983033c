var footerPlotsPayrollView = jQuery.extend({}, jQuery.fn.datagrid.defaults.view, {
	renderFooter: function(target, container, frozen){
		var rows = jQuery.data(target, 'datagrid').footer || [];

		initPlotsPayrollTotalGrid(rows);
	}
});

function initPayrollByOwnerGrid(owner_id, is_heritor, root_id, path, all_renta, total_area) {
	let farming_year = getPayrollFarmingYear();

	jQuery('#payroll-by-owner-tables').datagrid({
		iconCls: 'icon-edit-geometry',
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		pageSize: 30,
		fit: true,
		fitColumns: false,
		showFooter: true,
		url: 'index.php?payroll-rpc=payroll-grid',
		idField: '',
		sortName: 'gid',
		border: true,
		sortOrder: 'asc',
		rpcParams: [{
			type: 'payroll_by_owner',
			is_heritor: is_heritor,
			root_id: root_id,
			payroll_from_date: farming_year.start_date,
			payroll_to_date: farming_year.end_date,
			payroll_ekate: jQuery('#choose-payroll-ekate').combobox('getValues'),
			payroll_farming: jQuery('#choose-payroll-farming').combobox('getValues'),
			owner_id: owner_id,
			path: path,
			all_renta: all_renta,
			total_area: total_area,
			farming_year: farming_year.id
		}],
		columns: [
			[
				{
					field: 'ekatte_name',
					title: '<b>Землище</b>',
					sortable: false,
					width: 120,
					rowspan: 2
				}, {
					field: 'kad_ident',
					title: '<b>Идентификатор</b>',
					sortable: true,
					width: 120,
					rowspan: 2
				}, 
				{
					field: 'mestnost',
					title: '<b>Местност</b>',
					sortable: true,
					width: 150,
					rowspan: 2
				}, 
				{
					field: 'category',
					title: '<b>Категория</b>',
					sortable: true,
					width: 140,
					rowspan: 2
				}, 
				{
					field: 'area_type',
					title: '<b>НТП</b>',
					sortable: true,
					width: 140,
					rowspan: 2
				}, 
				{
					field: 'all_owner_area',
					title: '<b>Притежавана площ<br/>(дка)</b>',
					sortable: false,
					width: 80,
					rowspan: 2
				}, {
					field: 'pu_area',
					title: '<b>Площ за лично<br/>ползване (дка)</b>',
					sortable: false,
					width: 100,
					rowspan: 2
				}, {
					field: 'owner_area',
					title: '<b>Изплозвана площ<br/>(дка)</b>',
					sortable: false,
					width: 80,
					rowspan: 2
				},
				{
					field: 'cultivated_area',
					title: '<b>Обработваема площ<br/>(дка)</b>',
					sortable: false,
					width: 80,
					rowspan: 2
				},  
				{
					field: 'rep_names',
					title: '<b>Представител</b>',
					sortable: true,
					width: 110,
					rowspan: 2
				}, 
				{
					field: 'plot_rent_txt',
					title: '<b>Цена/дка</b>',
					sortable: true,
					width: 80,
					rowspan: 2
				}, {
					title: '<b>Рента по договор</b>',
					align: 'center',
					colspan: 2
				}
				, {
					title: '<b>Рента начислена</b>',
					align: 'center',
					colspan: 3
				}
				, {
					title: '<b>Данни за договори</b>',
					align: 'center',
					colspan: 5
				}
				, {
					title: '<b>Платена рента за договор</b>',
					align: 'center',
					colspan: 5
				}
				, {
					title: '<b>Остатък за договор</b>',
					align: 'center',
					colspan: 3
				}
				, {
					title: '<b>Надплатено</b>',
					align: 'center',
					colspan: 2
				}
			], [
				{
					field: 'renta_txt',
					title: '<b>в пари</b>',
					align: 'center',
					sortable: false,
					width: 80
				}, {
					field: 'renta_nat_with_type',
					title: '<b>в натура</b>',
					align: 'center',
					sortable: false,
					width: 120
				}
				, {
					field: 'charged_renta_txt',
					title: '<b>в пари</b>',
					align: 'center',
					sortable: false,
					width: 80
				}, {
					field: 'charged_renta_nat_text',
					title: '<b>в натура</b>',
					align: 'center',
					sortable: false,
					width: 120
				} 
				,{
					field: 'renta_nat_type',
					title: '<b>тип</b>',
					align: 'center',
					sortable: false,
					width: 150
				}
				, {
					field: 'c_num',
					title: '<b>Договор №</b>',
					align: 'center',
					sortable: true,
					width: 110
				},
				{
					field: 'contract_type',
					title: '<b>Ползване</b>',
					align: 'center',
					sortable: false,
					width: 100
				}
				,{
					field: 'contract_due_date',
					title: '<b>Валиден до</b>',
					align: 'center',
					sortable: true,
					width: 100
				}
				, {
					field: 'sv_num',
					title: '<b>Вписване<br/>№</b>',
					align: 'center',
					sortable: true,
					width: 80
				}, 
				{
					field: 'sv_date',
					title: '<b>Дата на<br/>вписване</b>',
					align: 'center',
					sortable: true,
					width: 100
				} 
				, {
					field: 'paid_renta_txt',
					title: '<b>в пари</b>',
					align: 'center',
					sortable: false,
					width: 75,
					styler: function(value, row, index) {
						if (parseFloat(value) < 0) {
							return 'background-color:red;';
						}
					}
				},
				{
					field: 'paid_renta_by',
					title: '<b>чрез</b>',
					align: 'center',
					sortable: false,
					width: 145
				},
				{
					field: 'paid_renta_nat_with_type',
					title: '<b>в натура</b>',
					align: 'center',
					sortable: false,
					width: 145
				},
				{
					field: 'paid_renta_nat_by',
					title: '<b>чрез</b>',
					align: 'center',
					sortable: false,
					width: 145
				},
				{
					field: 'paid_renta_nat_by_detailed',
					title: '<b>детайлно</b>',
					align: 'center',
					sortable: false,
					width: 145
				},
				{
					field: 'unpaid_renta_txt',
					title: '<b>в пари</b>',
					align: 'center',
					sortable: false,
					width: 80
				},
				{
					field: 'unpaid_renta_nat',
					title: '<b>в натура</b>',
					align: 'center',
					sortable: false,
					width: 145
				},
				{
					field: 'unpaid_renta_nat_unit_value',
					title: '<b>в пари (ед. ст.)</b>',
					align: 'center',
					sortable: false,
					width: 100
				},
				{
					field: 'over_paid_txt',
					title: '<b>в пари</b>',
					align: 'center',
					sortable: false,
					width: 145
				},
				{
					field: 'over_paid_nat_text',
					title: '<b>в натура</b>',
					align: 'center',
					sortable: false,
					width: 100
				},
			]
		],
		pagination: true,
		rownumbers: true,
		onBeforeLoad: function() {
			jQuery('#payroll-by-owner-tables').datagrid('clearChecked');
		},
		onLoadSuccess: function(data) {
            mergeCellsByField(jQuery(this), 'contract_id',  ['c_num', 'contract_type', 'contract_due_date', 'osz_num', 'osz_date', 'paid_renta', 'unpaid_renta', 'paid_renta_by', 'paid_renta_nat', 'paid_renta_nat_by', 'paid_renta_nat_by_detailed', 'unpaid_renta_nat', 'unpaid_renta_nat_unit_value', 'over_paid', 'over_paid_nat_text', 'paid_renta_nat_with_type']);
        },
		view: footerPlotsPayrollView,
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function initPlotsPayrollTotalGrid(rows) {
	//add data to first column at datagrid
	if(rows.length > 0)
	{
		rows[0]['type'] = 'Общо за стр.';
	}

	var grid = jQuery('#plots-payroll-total-grid');
	var isDatagridBound = grid.data().hasOwnProperty('datagrid');

	if (isDatagridBound) {
		grid.datagrid({
			data: rows
		});
		return;
	}

	grid.datagrid({
		iconCls: 'icon-sum',
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		fit: true,
		fitColumns: false,
		idField: '',
		border: true,
		data: rows,
		columns: [
			[
				{
					field: 'type',
					title: '<b>Вид</b>',
					align: 'center',
					sortable: true,
					width: 90,
					rowspan: 2
				},
				{
					field: 'area',
					title: '<b>Площ</b>',
					align: 'center',
					sortable: true,
					width: 80,
					rowspan: 2
				},{
					field: 'pu_area',
					title: '<b>Площ за лично<br/>ползване(дка)</b>',
					align: 'center',
					sortable: true,
					width: 100,
					rowspan: 2
				}, {
					title: '<b>Рента в лева</b>',
					align: 'center',
					colspan: 2
				}, {
					title: '<b>Рента в натура</b>',
					align: 'center',
					colspan: 3
				}, {
					title: '<b>Платена рента</b>',
					align: 'center',
					colspan: 5
				}, {
					title: '<b>Остатък</b>',
					align: 'center',
					colspan: 3
				}, {
					title: '<b>Общо платена рента</b>',
					align: 'center',
					colspan: 2
				}
			],
			[
				{
					field: 'renta',
					title: '<b>по договор</b>',
					align: 'center',
					sortable: false,
					width: 80
				},
				{
					field: 'charged_renta',
					title: '<b>начислена</b>',
					align: 'center',
					sortable: false,
					width: 80
				},
				{
					field: 'renta_nat',
					title: '<b>по договор</b>',
					align: 'center',
					sortable: false,
					width: 140
				},
				{
					field: 'charged_renta_nat',
					title: '<b>начислена</b>',
					align: 'center',
					sortable: false,
					width: 140
				}, {
					field: 'renta_nat_type',
					title: '<b>тип</b>',
					align: 'center',
					sortable: false,
					width: 80
				},
				{
					field: 'paid_renta',
					title: '<b>в лева</b>',
					align: 'center',
					sortable: false,
					width: 70,
					styler: function(value, row, index) {
						if (parseFloat(value) < 0) {
							return 'background-color:red;';
						}
					}
				},
				{
					field: 'paid_renta_by',
					title: '<b>чрез</b>',
					align: 'center',
					sortable: false,
					width: 145
				},
				{
					field: 'paid_renta_nat',
					title: '<b>в натура</b>',
					align: 'center',
					sortable: false,
					width: 145
				},
				{
					field: 'paid_renta_nat_by',
					title: '<b>чрез</b>',
					align: 'center',
					sortable: false,
					width: 145
				},
				{
					field: 'paid_renta_nat_by_detailed',
					title: '<b>детайлно</b>',
					align: 'center',
					sortable: false,
					width: 145
				},
				{
					field: 'unpaid_renta',
					title: '<b>в лева</b>',
					align: 'center',
					sortable: false,
					width: 80
				},
				{
					field: 'unpaid_renta_nat',
					title: '<b>в натура</b>',
					align: 'center',
					sortable: false,
					width: 145
				},
				{
					field: 'unpaid_renta_nat_unit_value',
					title: '<b>в лева (ед. ст.)</b>',
					align: 'center',
					sortable: false,
					width: 100
				},
				{
					field: 'total_by_renta',
					title: '<b>в лева</b>',
					align: 'center',
					sortable: false,
					width: 80
				},
				{
					field: 'total_by_renta_nat',
					title: '<b>в натура</b>',
					align: 'center',
					sortable: false,
					width: 145
				}
			]
		]
	});
}

function mergeCellsByField(datagrid, mainField, otherFields) {
	var rows = datagrid.datagrid('getRows');
	var startIndex = 0;
	var span = 1;

	for (var i = 1; i <= rows.length; i++) {
		if (i === rows.length || rows[i][mainField] !== rows[startIndex][mainField]) {
			if (span > 1) {
				// Обединяване на основната колона
				datagrid.datagrid('mergeCells', {
					index: startIndex,
					field: mainField,
					rowspan: span
				});
				// Обединяване на другите колони
				otherFields.forEach(function(field) {
					datagrid.datagrid('mergeCells', {
						index: startIndex,
						field: field,
						rowspan: span
					});
				});
			}
			startIndex = i;
			span = 1;
		} else {
			span++;
		}
	}
}