jQuery(function () {
	jQuery('#exportMassPayment').bind('click', function () {
		exportMassPayment();
	});
});



var _pathFile = "";

function printOrExportPayroll(type_print_export) { 
	var data = jQuery('#payroll-tables').treegrid('getData');
	if (data.length == 0) {
		return jQuery.messager.alert('Грешка', 'Не може да отпечатате празна ведомост.', 'error');
	}

	var customColumnsData = jQuery('#payroll-export-drop-source .drag');
	var customColumnsString = '';

	for(var i = 0; i < customColumnsData.length; i++) {
		if(customColumnsString == '') {
			customColumnsString = jQuery(customColumnsData[i]).data('column-name');
		} else {
			customColumnsString += ':sep:' + jQuery(customColumnsData[i]).data('column-name');
		}
	}

	var params = getPayrollParams();
	params.custom_columns = "{" + customColumnsString + "}";
	params.subtype = jQuery("input[name='print-payroll']:checked").val();
	params.print_export = type_print_export;

	if(type_print_export === 'export'){
		TF.Rpc.Payroll
			.PayrollExportsGrid
			.export(params)
			.done(function () {
				jQuery('#win-choose-payroll-print').window('close');
				jQuery('#payroll-export-tables-grid').datagrid('loadRpc');

			})
			.fail(function (errorObj) {});
	}
	else if(type_print_export === 'print'){
		TF.Rpc.Payroll
			.PayrollGridExportAndPrint
			.printPayrollGrid(params)
			.done(function (dataObj) {
				jQuery('body').append('<iframe id="printf" name="printf"></iframe>');
				var newWin = window.frames['printf'];
				newWin.document.write('<body onload=window.print()>'+dataObj+'</body>');
				newWin.document.close();
				setTimeout(function () {
					jQuery('#printf').remove();
				}, 1000);
			})
			.fail(function (errorObj) {});
	}
}

function exportMassPayment() {
	var bankInfoString = jQuery('#export-mass-payment-iban').combobox('getValue');
	if(!bankInfoString) {
		jQuery.messager.alert('Грешка', 'Моля изберете IBAN на наредителя!', 'warning');
		return false;
	}
	var bankInfo = JSON.parse(bankInfoString);
	if (!bankInfo.iban) {
		jQuery.messager.alert('Грешка', 'Невалиден IBAN. Моля уверете се, че избрания е валиден!', 'warning');
		return false;
	}

	let params = {
		columns: [],
		filters: getPayrollFilters()
	}

	params.order_date = jQuery('#export-mass-payment-date').datebox('getValue');
	params.farming_id = jQuery('#export-mass-payment-farming').combobox('getValue');
	params.payer_iban = bankInfo.iban;
	
	params.type = 'owners';
	params.subtype = 'summary';
	params.print_export = 'export';
	params.masspayment_type = jQuery("#masspayment_type input[name='masspayment_type']:checked").val();
	params.generate_transactions = jQuery("#generate-payment-transactions").is(':checked');

	TF.Rpc.Payroll
	.PayrollExportsGrid
	.exportMassPayment(params)
	.done(function () {
		jQuery('#payroll-export-tables-grid').datagrid('loadRpc');
		jQuery('#win-payroll-export-mass-payment').window('close');
	})
	.fail(function (errorObj) {
		jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
	});
}

function getPayrollParams() {
	var date = new Date();
	var currentYear = date.getFullYear();
	var lastYear = currentYear - 1;

	let farming_year = getPayrollFarmingYear();

	var params = {};
	params.type = 'owners';
	params.payroll_from_date = farming_year.start_date;
	params.payroll_to_date = farming_year.end_date;
	params.payroll_ekate = jQuery('#choose-payroll-ekate').combobox('getValues');;
	params.payroll_farming = jQuery('#choose-payroll-farming').combobox('getValues');;
	params.farming_year = farming_year.id;
	params.separated_natura = jQuery("#export-separated-naturas-chkbox").is(':checked');
	params.owner_names = jQuery('#search-payroll-owner-name').val();
	params.rent_place = jQuery('#search-payroll-owner-renta-place').combobox('getValue');
	params.egn = jQuery('#search-payroll-owner-egn').val();
	params.eik = jQuery('#search-payroll-company-eik').val();
	params.company_name = jQuery('#search-payroll-company-name').val();
	params.rep_names = jQuery('#search-payroll-represent-name').val();
	params.rep_egn = jQuery('#search-payroll-represent-egn').val();
	params.rep_rent_place = jQuery('#search-payroll-represent-renta-place').combobox('getValue');
	params.heritor_names = jQuery('#search-payroll-heritor-name').val();
	params.heritor_egn = jQuery('#search-payroll-heritor-egn').val();
	params.sort = jQuery('#payroll-tables').datagrid('options').sortName;
	params.order = jQuery('#payroll-tables').datagrid('options').sortOrder;
	params.owner_egns = payrollEgnFilters;
	params.company_eiks = payrollEIKFilters;
	params.owner_type = jQuery('#search-payroll-owner-type').combobox('getValue');


	return params;
}
