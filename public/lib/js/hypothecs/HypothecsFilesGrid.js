Namespace('TF.Hypothecs.HypothecsFilesGrid');

TF.Hypothecs.HypothecsFilesGrid = (function ($) {
    var hypothecsFilesGrid = $('#hypothecs-files-grid');
    
    hypothecsFilesGrid.datagrid({
		iconCls: 'icon-files',
		title: 'Архив файлове',
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		fit: true,
		fitColumns: true,
		showFooter: false,
		url: 'index.php?hypothecs-rpc=hypothecs-files-grid',
        rpcParams: [null],
		idField: 'id',
		singleSelect: true,
		frozenColumns: [[
				{
					field: 'ck',
					checkbox: true
				}
			]],
		columns: [[
				{
					field: 'filename',
					title: '<b>Файл</b>',
					sortable: false,
					width: 100
				}, {
					field: 'date_text',
					title: '<b>Дата</b>',
					sortable: false,
					width: 100
				}
			]],
		rownumbers: true,
		toolbar: [{
				id: 'btnaddcontractfile',
				text: 'Добавяне',
				iconCls: 'icon-add',
				handler: function() {
                    var hypothec = TF.Hypothecs.HypothecsTree.tree.tree('getSelected');
                    
                    if(!hypothec) {
                        $.messager.alert('Грешка', 'Моля изберете договор за ипотека.');
                    }

                    initFileUploads(hypothec.id);
                    $('#win-add-file').window('open');
				}
			}, {
				id: 'btndeletecontractfile',
				text: 'Изтриване',
				iconCls: 'icon-remove',
				handler: function() {
					var selectedFile = hypothecsFilesGrid.datagrid('getSelected');
                    
                    if(!selectedFile) {
                        $.messager.alert('Грешка', 'Моля изберете файл.');
                        return;
                    }

					$.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да премахнете този запис?', function(r) {
                        if (r) {
                            TF.Rpc.Hypothecs.HypothecsFilesGrid.delete(selectedFile.id, selectedFile.filename, selectedFile.group_id, selectedFile.user_id)
                                .done(function(data) {
                                    hypothecsFilesGrid.datagrid('reload');
                                })
                                .fail(function(error) {
                                    $.messager.alert('Грешка', error.getMessage());
                                });
                        }
                    });
				}
			}, {
				id: 'btndownloadcontractfile',
				text: 'Изтегляне',
				iconCls: 'icon-export',
				handler: function() {
					var selectedFile = hypothecsFilesGrid.datagrid('getSelected');
                    
                    if(!selectedFile) {
                        $.messager.alert('Грешка', 'Моля изберете файл.');
                        return;
                    }
                    
                    var link = document.createElement('a');
                    link.href = 'files/hypothecs_files/' + selectedFile.group_id + '/' + selectedFile.user_id + '/' + selectedFile.id + '.' + selectedFile.filename.split('.').pop();
                    link.download = selectedFile.filename;
                    link.target = '_blank';
                    document.body.appendChild(link);
                    link.click();
                    link.remove();
				}
			}],
		onLoadSuccess: function() {
			hypothecsFilesGrid.datagrid('clearChecked');
		}
	});

    function initFileUploads(hypothec_id)
    {
		var url  = "index.php?json=hypothecs-upload";
        
        $("#uploader").pluploadQueue({
            // General settings
            runtimes: 'gears,html5,flash,silverlight,browserplus',
            url: url,
            max_file_size: '100mb',
            unique_names: true,
			multipart_params : {
				hypothec_id: hypothec_id
			},
            // Flash settings
            flash_swf_url: 'lib/js_external/fileupload/plupload.flash.swf'
        });

        var uploader = $("#uploader").pluploadQueue();

        uploader.bind('UploadComplete', function() {
            $('#win-add-file').window('close');
            hypothecsFilesGrid.datagrid('reload');
        });
    }

    return {
        grid: hypothecsFilesGrid
    };
}(jQuery));