Namespace('TF.Hypothecs.PrintTemplatesGrid');
var _pathFile ="";
var _fileName ="";

var winDownload = jQuery('#win-download').window({
    onClose: onDownloadWindowClose
});

var downloadFile = jQuery('#btn-download-file');
var cancelDownloadFile = jQuery('#btn-download-file-close');

TF.Hypothecs.PrintTemplatesGrid = (function ($) {

    var printTemplatesGrid = $('#print-templates-grid');

    printTemplatesGrid.datagrid({
        iconCls: 'icon-template',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 50,
        fit: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?common-rpc=templates-grid',
        rpcParams: [null],
        sortName: 'id',
        sortOrder: 'asc',
        border: false,
        idField: 'id',
        singleSelect: true,
        frozenColumns: [[
                {
                    field: 'ck',
                    checkbox: true
                }
            ]],
        columns: [[
                {
                    field: 'title',
                    title: '<b>Име</b>',
                    sortable: true,
                    width: 150
                }, {
                    field: 'add_date',
                    title: '<b>Дата на добавяне</b>',
                    sortable: true,
                    width: 150
                }
            ]],
        pagination: true,
        rownumbers: true,
        toolbar: [
			{
                id: 'btnexportpdfblank',
                text: 'Отпечатай pdf',
                iconCls: 'icon-pdf',
                handler: function() {
                    var selectedRow = printTemplatesGrid.datagrid('getSelected');

                    if(!selectedRow) {
                        $.messager.alert('Грешка', 'Моля изберете бланка, върху която да разпечатате!');
                        return;
                    }

                    var hypothec = TF.Hypothecs.HypothecsTree.tree.tree('getSelected');

                    TF.Rpc.Hypothecs.HypothecsTree.export(hypothec.id, selectedRow.id, 'pdf')
                    .done(function(data) {
                        winDownload.window('open');
                        var path = data.file_url;
                        _pathFile = path;
                        _fileName = data.filename;
                        downloadFile.attr("href", path);
                    })
                    .fail(function(error) {
                        $.messager.alert('Грешка', error.getMessage());
                    });
                }
            },{
                id: 'btnexportdocblank',
                text: 'Отпечатай Word',
                iconCls: 'icon-word',
                handler:function() {
                    var selectedRow = printTemplatesGrid.datagrid('getSelected');

                    if(!selectedRow) {
                        $.messager.alert('Грешка', 'Моля изберете бланка, върху която да разпечатате!');
                        return;
                    }

                    var hypothec = TF.Hypothecs.HypothecsTree.tree.tree('getSelected');

                    TF.Rpc.Hypothecs.HypothecsTree.export(hypothec.id, selectedRow.id, 'doc')
                    .done(function(data) {
                        winDownload.window('open');
                        var path = data.file_url;
                        _pathFile = path;
                        _fileName = data.filename;
                        downloadFile.attr("href", path);
                    })
                    .fail(function(error) {
                        $.messager.alert('Грешка', error.getMessage());
                    });
                }
            }, {
                id: 'btnedittemplate',
                text: 'Редактиране',
                iconCls: 'icon-edit',
                handler: function() {
                    var getChecked = printTemplatesGrid.datagrid('getChecked');

                    if (getChecked[0]) {
                        editTemplateRequested = true;

                        TF.Rpc.Common.TemplatesGrid.markForEdit(getChecked[0].id)
                        .done(function (data) {
                            jQuery('#win-add-edit-template').window('open');
                            contracts_templates.setTemplateVariables(data);
                            contracts_templates.initAddEditTemplateFields();
                        })
                        .fail(function (data) {
                            RpcErrorHandler.show(data);
                        });
                    } else {
                        jQuery.messager.alert('Грешка', 'Моля изберете бланка, която искате да редактирате!');
                    }
                }
            }],
        onBeforeLoad: function() {
            printTemplatesGrid.datagrid('clearChecked');
        }
    });

    return {
        grid: printTemplatesGrid
    };

}(jQuery));


function onDownloadWindowClose() {
   return;
}

function validateSaveHypothecsTemplate() {
    if (jQuery('#template-name > input').val()) {

        var addEditObj = contracts_templates.contracts_templates.contracts_templates.getTemplateVariables();

        if (editTemplateRequested) {
            var getChecked = jQuery('#print-templates-grid').datagrid('getChecked');
            addEditObj['id'] = getChecked[0].id;

            TF.Rpc.Common.TemplatesGrid.edit(addEditObj)
            .done(function (data) {
                jQuery('#win-add-edit-template').window('close');
                jQuery('#print-templates-grid').datagrid('loadRpc');
            })
            .fail(function (data) {
                RpcErrorHandler.show(data);
            });
        } else {

            TF.Rpc.Common.TemplatesGrid.add(addEditObj)
            .done(function (data) {
                jQuery('#win-add-edit-template').window('close');
                jQuery('#print-templates-grid').datagrid('loadRpc');
            })
            .fail(function (data) {
                RpcErrorHandler.show(data);
            });

            return true;
        }
    } else {
        jQuery.messager.alert('Грешка', 'Моля попълнете име на бланката.');

        return false;
    }
}
