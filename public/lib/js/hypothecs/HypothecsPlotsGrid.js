Namespace('TF.Hypothecs.HypothecsPlotsGrid');

TF.Hypothecs.HypothecsPlotsGrid = (function ($) {
    var hypothecsPlotsGrid = $('#hypothecs-plots-grid');
    var hypothecsAddPlotsGrid = $('#hypothecs-add-plots-grid');

    //filter fields
    var searchSoldCb = $('#search-ap-sold');
    var searchSubleasedCb = $('#search-ap-subleased');
    var searchEkateCb = $('#search-ap-ekate');
    var searchCategoryCb = $('#search-ap-category');
    var searchAreaTypeCb = $('#search-ap-area-type');
    var searchIrrigatedAreaCb = $('#search-ap-irrigated-area');

    var winFilterPlots = $('#win-add-plots-filter');

    $('#btn-filter-add-plots-grid').on('click', function(e) {
        e.preventDefault();

        var data = hypothecsAddPlotsGrid.datagrid('options').rpcParams[0];

        data.kad_ident = $('#search-ap-kad-ident').val();
        data.ekate = searchEkateCb.combobox('getValue');
        data.masiv = $('#search-ap-masiv').val();
        data.irrigated_area = searchIrrigatedAreaCb.combobox('getValue');
        data.number = $('#search-ap-number').val();
        data.category = searchCategoryCb.combobox('getValue');
        data.area_type = searchAreaTypeCb.combobox('getValue');
        data.c_num = $('#search-ap-cnum').val();
        data.sold = searchSoldCb.combobox('getValue');
        data.subleased = searchSubleasedCb.combobox('getValue');

        hypothecsAddPlotsGrid.datagrid('reload');
        winFilterPlots.window('close');
    });

    $('#btn-save-edit-plots-data').on('click', function(e) {
        e.preventDefault();

        var plot = hypothecsPlotsGrid.datagrid('getSelected');

        if(!$('#edit-hypothec-area').numberbox('isValid')) {
            $('#edit-hypothec-area').numberbox('textbox').focus();
            return;
        }

        TF.Rpc.Hypothecs.HypothecsPlotsGrid.edit(plot.id, $('#edit-hypothec-area').numberbox('getValue'))
            .done(function(maxArea) {
                maxHypothecArea = maxArea;

                hypothecsPlotsGrid.datagrid('reload');

                $('#win-edit-plots-data').window('close');
            })
            .fail(function(error) {
                $.messager.alert('Грешка', error.getMessage());
            });

    });

    hypothecsPlotsGrid.datagrid({
		iconCls: 'icon-edit-geometry',
		nowrap: true,
		title: 'Имоти',
		autoRowHeight: true,
		striped: true,
		pageSize: 10,
		fit: true,
		singleSelect: true,
		fitColumns: false,
		showFooter: true,
		url: 'index.php?hypothecs-rpc=hypothecs-plots-grid',
        rpcParams: [null],
		sortName: 'gid',
		sortOrder: 'asc',
		idField: 'gid',
        rowStyler: function(index, row) {
            var style = [];
            if (row.is_edited == true) {
                style.push('color: #aaa');
            }

            return style.join(';');
        },
		columns: [[
				{
					field: 'land',
					title: '<b>Землище</b>',
					sortable: true,
					width: 100
				}, {
					field: 'kad_ident',
					title: '<b>Идентификатор</b>',
					sortable: true,
					width: 100
				}, {
					field: 'hypothec_area',
					title: '<b>Ипотекирана<br/>площ (дка)</b>',
					align: 'center',
					sortable: true,
					width: 90,
                    formatter: function(value){
                        if (!value){
                            return '-';
                        }

                        return parseFloat(value).toFixed(3);
                    }
				}, {
					field: 'category',
					title: '<b>Категория</b>',
					sortable: true,
					width: 100
				}, {
					field: 'area_type',
					title: '<b>НТП</b>',
					sortable: true,
					width: 100
				}
			]],
		pagination: true,
		rownumbers: true,
		toolbar: [
			{
				id: 'btninneraddcontractplot',
				text: 'Добавяне',
				iconCls: 'icon-add',
				handler: function() {
                    var hypothec = TF.Hypothecs.HypothecsTree.tree.tree('getSelected');

                    if(!hypothec) {
                        $.messager.alert('Грешка', 'Моля изберете изпотека.');
                        return;
                    }

                    hypothecsAddPlotsGrid.datagrid('options').rpcParams = [{
                        hypothec_id: hypothec.id,
                        farming_id: hypothec.attributes.farming_id,
                        h_start_date: hypothec.attributes.start_date
                    }];

                    hypothecsAddPlotsGrid.datagrid('reload');
                    clearAddPlotsGridFilter();
                    $('#win-add-plots').window('open');
				}
			}, {
				id: 'btninnereditcontractplot',
				text: 'Редактиране',
				iconCls: 'icon-edit',
				handler: function() {
                    var hypothec = TF.Hypothecs.HypothecsTree.tree.tree('getSelected');

                    if(!hypothec) {
                        $.messager.alert('Грешка', 'Моля изберете изпотека.');
                        return;
                    }

                    var plot = hypothecsPlotsGrid.datagrid('getSelected');

                    if(!plot) {
                        $.messager.alert('Грешка', 'Моля изберете имот.');
                        return;
                    }

                    TF.Rpc.Hypothecs.HypothecsPlotsGrid.getMaxHypothecArea(plot.gid, hypothec.attributes.farming_id)
                        .done(function(maxArea) {

                            $('#edit-hypothec-area').numberbox({
                                required: true,
                                precision: 3,
                                min: 0.001,
                                max: parseFloat(maxArea)
                            });

                            $('#win-edit-plots-data').window('open');
                        })
                        .fail(function(error) {
                            $.messager.alert('Грешка', error.getMessage());
                        });

				}
			}, {
				id: 'btninnerdeletecontractplot',
				text: 'Премахване',
				iconCls: 'icon-delete',
				handler: function() {
					var selectedRow = hypothecsPlotsGrid.datagrid('getSelected');

                    if (!selectedRow) {
                        $.messager.alert('Грешка', 'Моля изберете имот.');
                        return;
                    }

					$.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да премахнете този имот?', function(r) {
                        if (r) {
                            TF.Rpc.Hypothecs.HypothecsPlotsGrid.delete(selectedRow.id)
                                .done(function(data) {
                                    hypothecsPlotsGrid.datagrid('reload');
                                })
                                .fail(function(error) {
                                    $.messager.alert('Грешка', error.getMessage());
                                });
                        }
                    });
				}
			}, {
                id: 'btnviewplotinfo',
                text: 'Информация',
                iconCls: 'icon-info',
                handler: function() {
                    var selectedRow = hypothecsPlotsGrid.datagrid('getSelected');

                    if (!selectedRow) {
                        $.messager.alert('Грешка', 'Моля изберете имот.');
                        return;
                    }

                    window.open("index.php?page=Plots.Home&plot_id=" + selectedRow.gid, '_blank');
                }
            }
		],
		onLoadSuccess: function() {
			hypothecsPlotsGrid.datagrid('clearChecked');
		}
	});

	//custom pager
	var pager = hypothecsPlotsGrid.datagrid('getPager');
	pager.pagination({
		beforePageText: 'Стр.',
		displayMsg: 'От {from} до {to} от {total} записа'
	});

    hypothecsAddPlotsGrid.datagrid({
		iconCls: 'icon-edit-geometry',
		nowrap: true,
        border: false,
		autoRowHeight: true,
		striped: true,
		pageSize: 10,
		fit: true,
		singleSelect: false,
		fitColumns: true,
		showFooter: true,
		url: 'index.php?hypothecs-rpc=hypothecs-plots-grid',
		sortName: 'gid',
		sortOrder: 'asc',
        rpcMethod: 'readForAdding',
        rpcParams: [null],
		idField: 'gid',
        pagination: true,
		rownumbers: true,
        rowStyler: function(index, row) {
            var style = [];
            if (row.is_edited == true) {
                style.push('color: #aaa');
            }

            return style.join(';');
        },
		frozenColumns: [[{
					field: 'ck',
					checkbox: true
				}]],
		columns: [[
				{
					field: 'land',
					title: '<b>Землище</b>',
					sortable: true,
					width: 100
				}, {
					field: 'kad_ident',
					title: '<b>Идентификатор</b>',
					sortable: true,
					width: 100
				}, {
					field: 'category',
					title: '<b>Категория</b>',
					sortable: true,
					width: 100
				}, {
					field: 'area_type',
					title: '<b>НТП</b>',
					sortable: true,
					width: 100
				}, {
					field: 'document_area',
					title: '<b>Площ по<br/>док. (дка)</b>',
					align: 'center',
					sortable: true,
					width: 90
				}, {
					field: 'contracts',
					title: '<b>Договори за<br/>собственост</b>',
                    align: 'center',
					sortable: true,
					width: 100
				}, {
					field: 'contract_area',
					title: '<b>Площ по<br/>договори (дка)</b>',
					align: 'center',
					sortable: true,
					width: 90
				}, {
					field: 'hypothec_area',
					title: '<b>Ипотекирана<br/>площ (дка)</b>',
					align: 'center',
					sortable: true,
					width: 90,
					editor: {
                            type: 'numberbox',
                            options: {
                                required: true,
                                missingMessage: 'Полето е задължително',
								precision: 3,
                                min: 0.001
                            }
                        }
				}, {
					field: 'subleases',
					title: '<b>Договори за<br/>преотдаване</b>',
					align: 'center',
					sortable: true,
					width: 90
				}, {
					field: 'is_soled',
					title: '<b>В договор<br/>за продажба</b>',
					align: 'center',
					sortable: true,
					width: 90,
                    formatter: function(value){
                        if (value){
                            return 'Да';
                        }

                        return 'Не';
                    }
				}
			]],
		toolbar: [
			{
				text: 'Добавяне',
				iconCls: 'icon-add',
				handler: function() {
                    var hypothec = TF.Hypothecs.HypothecsTree.tree.tree('getSelected');
                    var getChecked = hypothecsAddPlotsGrid.datagrid('getChecked');
                    var plots = [];
                    var is_selected_historical_plot = false;

                    for (var i = 0; i < getChecked.length; i++) {
                        var index = hypothecsAddPlotsGrid.datagrid('getRowIndex', getChecked[i]);
                        var editor = hypothecsAddPlotsGrid.datagrid('getEditor', {
                            index: index,
                            field: 'hypothec_area'
                        });

                        if(editor) {
                            if(!$(editor.target).numberbox('isValid')) {
                                $(editor.target).numberbox('textbox').focus();
                                return;
                            }

                            var hypothecArea = $(editor.target).numberbox('getValue');
                            $(editor.target).numberbox('setValue', Math.min(getChecked[i].contract_area, hypothecArea));
                            hypothecsAddPlotsGrid.datagrid('endEdit', index);
                        }

                        if(getChecked[i].is_edited == true && is_selected_historical_plot == false) {
                            is_selected_historical_plot = true;
                        }

                        plots.push({
                            hypothec_id: hypothec.id,
                            plot_id: getChecked[i].gid,
                            hypothec_area: getChecked[i].hypothec_area
                        });
                    }

                    if (is_selected_historical_plot == true) {
                        jQuery.messager.confirm('Потвърждение', 'Избраните имоти включват исторически имоти,'+
                        ' които не са част от актуалната КВС/КК. Желаете ли да продължите?', function(r) {
                            if (r) {
                                TF.Rpc.Hypothecs.HypothecsPlotsGrid.add(plots)
                                .done(function(data) {
                                    hypothecsAddPlotsGrid.datagrid('reload');
                                    hypothecsPlotsGrid.datagrid('reload');
                                })
                                .fail(function(error) {
                                    $.messager.alert('Грешка', error.getMessage());
                                });
                            }
                        });
                    }else
                    {
                        TF.Rpc.Hypothecs.HypothecsPlotsGrid.add(plots)
                        .done(function(data) {
                            hypothecsAddPlotsGrid.datagrid('reload');
                            hypothecsPlotsGrid.datagrid('reload');
                        })
                        .fail(function(error) {
                            $.messager.alert('Грешка', error.getMessage());
                        });
                    }
				}
			}, {
				text: 'Филтриране',
				iconCls: 'icon-filter',
				handler: function() {
                    initAddPlotsFilterFields();
					winFilterPlots.window('open');
				}
			}, {
				text: 'Покажи всички',
				iconCls: 'icon-clear-filter',
				handler: function() {
					var data = hypothecsAddPlotsGrid.datagrid('options').rpcParams[0];
                    var hypothecId = data.hypothec_id;
                    var hStartDate= data.h_start_date;

                    hypothecsAddPlotsGrid.datagrid('options').rpcParams = [{
                        hypothec_id: hypothecId,
                        h_start_date: hStartDate
                    }];

                    hypothecsAddPlotsGrid.datagrid('reload');
                    clearAddPlotsGridFilter();
				}
			}
		],
		onLoadSuccess: function() {
            $(this).datagrid('clearChecked');
		},
		onSelect: function(rowIndex) {
			$(this).datagrid('beginEdit', rowIndex);
		}
	});

    function initAddPlotsFilterFields() {
        var ekateComboboxData          = ComboboxData.EkateCombobox,
            categoryComboboxData       = ComboboxData.PlotCategoryCombobox
            plotNTPComboboxData        = ComboboxData.PlotNTPCombobox,
            irrigatedAreaComboboxData  = ComboboxData.IrrigatedAreaCombobox;

        if(!searchSoldCb.data().hasOwnProperty('combobox')) {
            searchSoldCb.combobox({
                data: [
                    {
                        id: 1,
                        name: 'Да'
                    },
                    {
                        id: 0,
                        name: 'Не'
                    }
                ],
                valueField: 'id',
                textField: 'name',
                required: false
            });
            searchSoldCb.combobox('clear');
        }

        if(!searchSubleasedCb.data().hasOwnProperty('combobox')) {
            searchSubleasedCb.combobox({
                data: [
                    {
                        id: 1,
                        name: 'Да'
                    },
                    {
                        id: 0,
                        name: 'Не'
                    }
                ],
                valueField: 'id',
                textField: 'name',
                required: false
            });
            searchSubleasedCb.combobox('clear');
        }

        newEkateComboboxData = [];
        ekateComboboxData.forEach(function (el) {
            if (el.ekate !== "") {
                newEkateComboboxData.push(el);
            }
        });

        if(!searchEkateCb.data().hasOwnProperty('combobox')) {
            searchEkateCb.combobox({
                data: newEkateComboboxData,
                valueField: 'ekate',
                textField: 'text',
                required: false,
                filter: function(q, row){
					var opts = $(this).combobox('options');
					var text = row[opts.textField].toLowerCase();
					var value = row[opts.valueField];
					var find = q.toLowerCase();
					if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
					{
						return true;
					}
				}
            });
        }

        newPlotCategoryCombobox = [];
        categoryComboboxData.forEach(function (el) {
            if (el.id !== "") {
                newPlotCategoryCombobox.push(el);
            }
        });

        if(!searchCategoryCb.data().hasOwnProperty('combobox')) {
            searchCategoryCb.combobox({
                data: newPlotCategoryCombobox,
                valueField: 'id',
                textField: 'name',
                required: false
            });
        }

        if(!searchAreaTypeCb.data().hasOwnProperty('combobox')) {
            searchAreaTypeCb.combobox({
                data: plotNTPComboboxData,
                valueField: 'id',
                textField: 'name',
                required: false
            });
        }

        if (!searchIrrigatedAreaCb.data().hasOwnProperty('combobox')) {
            searchIrrigatedAreaCb.combobox({
                data: irrigatedAreaComboboxData,
                editable: false,
                valueField: 'value',
                textField: 'label',
                multiple: false,
                loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
            });
        }

    }

    function clearAddPlotsGridFilter() {
        $('#search-ap-kad-ident').val('');
        $('#search-ap-masiv').val('');
        $('#search-ap-number').val('');
        $('#search-ap-cnum').val('');

        if(searchEkateCb.data().hasOwnProperty('combobox')) {
            searchEkateCb.combobox('clear');
        }
        if(searchCategoryCb.data().hasOwnProperty('combobox')) {
            searchCategoryCb.combobox('clear');
        }
        if(searchAreaTypeCb.data().hasOwnProperty('combobox')) {
            searchAreaTypeCb.combobox('clear');
        }
        if(searchSoldCb.data().hasOwnProperty('combobox')) {
            searchSoldCb.combobox('clear');
        }
        if(searchSubleasedCb.data().hasOwnProperty('combobox')) {
            searchSubleasedCb.combobox('clear');
        }
        if(searchIrrigatedAreaCb.data().hasOwnProperty('combobox')) {
            searchIrrigatedAreaCb.combobox('loadRpc');
        }
    };


    return {
        grid: hypothecsPlotsGrid,
        gridAdding: hypothecsAddPlotsGrid
    };
}(jQuery));
