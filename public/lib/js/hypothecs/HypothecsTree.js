Namespace('TF.Hypothecs.HypothecsTree');

TF.Hypothecs.HypothecsTree = (function ($) {
    var hypothecsTree = $('#hypothecs-tree'),
        hFarmingCb = $('#hypothec-farming'),
        hCreditorCb = $('#hypothec-creditor'),
        hDateDb = $('#hypothec-date'),
        hStartDateDb = $('#hypothec-start-date'),
        hDueDateDb = $('#hypothec-due-date'),
        creditorNameTb = $('#creditor-name'),
        hNumTb = $('#hypothec-number'),
        editID,

    //filter fields
        searchFarmingCb = $('#search-farming'),
        searchCreditorCb = $('#search-creditor'),
        searchStatusCb = $('#search-status'),
        searchEkateCb = $('#search-ekate'),
        searchCategoryCb = $('#search-category'),
        searchAreaTypeCb = $('#search-area-type'),
        searchStartDateFromDb = $('#search-start-date-from'),
        searchStartDateToDb = $('#search-start-date-to'),
        searchDueDateFromDb = $('#search-due-date-from'),
        searchDueDateToDb = $('#search-due-date-to'),
        searchIrrigatedArea = $('#search-irrigated-area'),

        date = new Date(),
        todayDate = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();

    hypothecsTree.tree({
		url: 'index.php?hypothecs-rpc=hypothecs-tree',
		animate: true,
		lines: true,
		sort: 'id',
		order: 'desc',
        rows: 50,
        page: 1,
		rpcParams: [{
            hypothec_id: GET.hypothec_id || null
        }],
        formatter: function(node) {
			if (!node.attributes.is_active) {
				node.text = '<font color="#aaa">' + node.text + '</font>';
            }

			return node.text;
		},
		onSelect: function(node) {
            showHypothecInfo(node.attributes);

            TF.Hypothecs.HypothecsPlotsGrid.grid.datagrid('loadRpc', [node.id]);
            TF.Hypothecs.HypothecsPaymentsGrid.grid.datagrid('loadRpc', [node.id]);
            TF.Hypothecs.HypothecsFilesGrid.grid.datagrid('loadRpc', [node.id]);
		},
		onLoadSuccess: function() {
			var roots = hypothecsTree.tree('getRoots');
			var total = 0;
			var limit = 30;

			if (roots.length) {
				if (editID != undefined) {
					var node = hypothecsTree.tree('find', editID);
					hypothecsTree.tree('select', node.target);
				}
				else {
					hypothecsTree.tree('select', roots[0].target);
				}

				total = roots[0]['attributes']['pagination']['total'];
				limit = roots[0]['attributes']['pagination']['limit'];
			}
            else
            {
                showHypothecInfo();

                TF.Hypothecs.HypothecsPlotsGrid.grid.datagrid('loadRpc', [null]);
                TF.Hypothecs.HypothecsPaymentsGrid.grid.datagrid('loadRpc', [null]);
                TF.Hypothecs.HypothecsFilesGrid.grid.datagrid('loadRpc', [null]);
            }

			//init pagination with total contract elements
			initPagination(total, limit);
		}
	});

    creditorNameTb.textbox({
        required: true,
        missingMessage: 'Това поле е задължително.'
    });


    $('#add-hypothec-btn').on('click', function(e) {
		e.preventDefault();

        editID = undefined;
        initAddEditFields();
        clearAddFields();
        $('#win-add-edit-hypothec').window('open');
	});

    $('#edit-hypothec-btn').on('click', function(e) {
		e.preventDefault();

        var hypothec = hypothecsTree.tree('getSelected');

        if(!hypothec) {
            $.messager.alert('Грешка', 'Моля изберете запис.');
            return;
        }

        editID = hypothec.id;
        initAddEditFields();
        populateEditFields(hypothec.attributes);
        $('#win-add-edit-hypothec').window('open');
	});

    $('#filter-hypothecs-btn').on('click', function(e) {
		e.preventDefault();

        initFilterFields();
        $('#win-hypothecs-filter').window('open');
	});

    $('#clear-filter-hypothecs-btn').on('click', function(e) {
		e.preventDefault();

        $('#search-hnum').val('');
        $('#search-kad-ident').val('');
        $('#search-masiv').val('');
        $('#search-number').val('');
        jQuery('#search-hnum-complete-match').prop('checked', true);


        if(searchStatusCb.data().hasOwnProperty('combobox')) {
            searchStatusCb.combobox('reset');
        }
        if(searchFarmingCb.data().hasOwnProperty('combobox')) {
            searchFarmingCb.combobox('reset');
        }
        if(searchCreditorCb.data().hasOwnProperty('combobox')) {
            searchCreditorCb.combobox('reset');
        }
        if(searchStartDateFromDb.data().hasOwnProperty('datebox')) {
            searchStartDateFromDb.datebox('clear');
        }
        if(searchStartDateToDb.data().hasOwnProperty('datebox')) {
            searchStartDateToDb.datebox('clear');
        }
        if(searchDueDateFromDb.data().hasOwnProperty('datebox')) {
            searchDueDateFromDb.datebox('clear');
        }
        if(searchDueDateToDb.data().hasOwnProperty('datebox')) {
            searchDueDateToDb.datebox('clear');
        }
        if(searchEkateCb.data().hasOwnProperty('combobox')) {
            searchEkateCb.combobox('reset');
        }
        if(searchCategoryCb.data().hasOwnProperty('combobox')) {
            searchCategoryCb.combobox('reset');
        }
        if(searchAreaTypeCb.data().hasOwnProperty('combobox')) {
            searchAreaTypeCb.combobox('reset');
        }

        if (searchIrrigatedArea.data().hasOwnProperty('combobox')) {
            searchIrrigatedArea.combobox('loadRpc');
        };

        hypothecsTree.tree('options').rpcParams = [null];
        hypothecsTree.tree('reload');
	});

    $('#btn-filter-hypothecs-tree').on('click', function(e) {
		var data = {};

        data.h_num = $('#search-hnum').val();
        data.is_active = searchStatusCb.combobox('getValue');
        data.farming = searchFarmingCb.combobox('getValue');
        data.creditor = searchCreditorCb.combobox('getValue');
        data.start_date_from = searchStartDateFromDb.datebox('getValue');
        data.start_date_to = searchStartDateToDb.datebox('getValue');
        data.due_date_from = searchDueDateFromDb.datebox('getValue');
        data.due_date_to = searchDueDateToDb.datebox('getValue');

        data.h_num_complete_match = false;
        if (jQuery('#search-hnum-complete-match').is(':checked'))
        {
            data.h_num_complete_match = true;
        }

        data.kad_ident = $('#search-kad-ident').val();
        data.ekate = searchEkateCb.combobox('getValue');
        data.masiv = $('#search-masiv').val();
        data.number = $('#search-number').val();
        data.category = searchCategoryCb.combobox('getValue');
        data.area_type = searchAreaTypeCb.combobox('getValue');
        data.irrigated_area = searchIrrigatedArea.combobox('getValue');

        hypothecsTree.tree('options').rpcParams = [data];
        hypothecsTree.tree('reload');
        $('#win-hypothecs-filter').window('close');
	});

    $('#change-hypothec-status-btn').on('click', function(e) {
		e.preventDefault();

        var hypothec = hypothecsTree.tree('getSelected');

        if(!hypothec) {
            $.messager.alert('Грешка', 'Моля изберете запис.');
            return;
        }

        editID = hypothec.id;

        if(hypothec.attributes.is_active) {
            $('#deactivate-date').datebox({value: todayDate});
            $('#deactivate-date').datebox('reset');
            $('#deactivate-num').val('');
            $('#deactivate-comment').val('');
            $('#win-deactivate-hypothec').window('open');
        }
        else {
            $.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да активирате тази ипотека?', function(r) {
                if (r) {
                    TF.Rpc.Hypothecs.HypothecsTree.activate(hypothec.id)
                        .done(function(data) {
                            hypothecsTree.tree('reload');
                        })
                        .fail(function(error) {
                            $.messager.alert('Грешка', error.getMessage());
                        });
                }
            });
        }
	});

    $('#btn-deactivate-hypothec').on('click', function(e) {
		e.preventDefault();

        var hypothec = hypothecsTree.tree('getSelected');

        if(!hypothec) {
            return;
        }

        var date = $('#deactivate-date').datebox('getValue');
        var num = $('#deactivate-num').val();
        var comment = hypothec.attributes.comment + '\n' + $('#deactivate-comment').val();

        TF.Rpc.Hypothecs.HypothecsTree.deactivate(hypothec.id, date, num, comment)
            .done(function(data) {
                hypothecsTree.tree('reload');
                $('#win-deactivate-hypothec').window('close');
            })
            .fail(function(error) {
                $.messager.alert('Грешка', error.getMessage());
            });
	});

    jQuery('#print-hypothec-btn').on('click', function(e) {
		e.preventDefault();

        var hypothec = hypothecsTree.tree('getSelected');

        if(!hypothec) {
            $.messager.alert('Грешка', 'Моля изберете запис.');
            return;
        }

        jQuery('#win-print-templates').window('open');
	});


    $('#add-new-creditor-btn').on('click', function(e) {
		e.preventDefault();

        creditorNameTb.textbox('clear');
        $('#win-add-new-creditor').window('open');
	});

    $('#save-creditor-btn').on('click', function(e) {
		e.preventDefault();

        if(!creditorNameTb.textbox('isValid')) {
            creditorNameTb.textbox('textbox').focus();
            return;
        }

        TF.Rpc.Hypothecs.HypothecsCreditors.add(creditorNameTb.textbox('getValue'))
            .done(function(data) {
                hCreditorCb.combobox('reload');
                $('#win-add-new-creditor').window('close');
            })
            .fail(function(error) {
                $.messager.alert('Грешка', error.getMessage());
            });
	});

    $('#save-hypothec-btn').on('click', function(e) {
		e.preventDefault();

        if(!hNumTb.textbox('isValid')) {
            hNumTb.textbox('textbox').focus();
            return;
        }

        if(!hDateDb.datebox('isValid')) {
            hDateDb.datebox('textbox').focus();
            return;
        }

        if(!hStartDateDb.datebox('isValid')) {
            hStartDateDb.datebox('textbox').focus();
            return;
        }

        if(!hDueDateDb.datebox('isValid')) {
            hDueDateDb.datebox('textbox').focus();
            return;
        }

        if($.grep(hFarmingCb.combobox('getData'), function(e){ return e.id == hFarmingCb.combobox('getValue'); }).length == 0) {
            hFarmingCb.combobox('textbox').focus();
            return;
        }

        if($.grep(hCreditorCb.combobox('getData'), function(e){ return e.id == hCreditorCb.combobox('getValue'); }).length == 0) {
            hCreditorCb.combobox('textbox').focus();
            return;
        }

        if (!compareDates('#hypothec-start-date', '#hypothec-due-date', 2)) {
            jQuery.messager.alert('Грешка', 'Въведена е грешна дата - крайната дата е по-ранна от датата на влизане в сила на договора.', 'warning');
            return false;
        }
        var data = {
            num: hNumTb.textbox('getValue'),
            date: hDateDb.datebox('getValue'),
            start_date: hStartDateDb.datebox('getValue'),
            due_date: hDueDateDb.datebox('getValue'),
            farming_id: hFarmingCb.combobox('getValue'),
            creditor_id: hCreditorCb.combobox('getValue'),
            tom: $('#hypothec-tom').val(),
            na_num: $('#hypothec-na-num').val(),
            delo: $('#hypothec-delo').val(),
            court: $('#hypothec-court').val(),
            comment: $('#hypothec-comment').val()
        };

        if(editID) {
            data.id = editID;

            TF.Rpc.Hypothecs.HypothecsTree.edit(data)
            .done(function(data) {
                hypothecsTree.tree('reload');
                $('#win-add-edit-hypothec').window('close');
            })
            .fail(function(error) {
                $.messager.alert('Грешка', error.getMessage());
            });
        }
        else
        {
            TF.Rpc.Hypothecs.HypothecsTree.add(data)
            .done(function(data) {
                hypothecsTree.tree('reload');
                $('#win-add-edit-hypothec').window('close');
            })
            .fail(function(error) {
                $.messager.alert('Грешка', error.getMessage());
            });
        }
	});


    function initPagination(total, limit) {
        $('#hypothecs-tree-pagination').pagination({
            showPageList: false,
            showRefresh: false,
            beforePageText: 'Стр.',
            displayMsg: '',
            total: total,
            pageSize: limit,
            onSelectPage: function(pageNumber, pageSize) {
                hypothecsTree.tree('options').page = pageNumber;
                hypothecsTree.tree('reload');
            }
        });
    }

    function showHypothecInfo(hypothec){
        if(!hypothec)
        {
            $('#info-creditor').html('');
            $('#info-number').html('');
            $('#info-active').html('');
            $('#info-date').html('');
            $('#info-farming').html('');
            $('#info-start-date').html('');
            $('#info-due-date').html('');
            $('#info-comment').html('');
            $('#info-tom').html('');
            $('#info-na-num').html('');
            $('#info-delo').html('');
            $('#info-court').html('');
            $('#info-deactivate-num').html('');
            $('#info-deactivate-date').html('');

            return;
        }

        var comment = hypothec.comment || '-';

        $('#info-creditor').html(hypothec.creditor || '-');
        $('#info-number').html(hypothec.num || '-');
        $('#info-active').html(hypothec.active_text || '-');
        $('#info-date').html(hypothec.date_text || '-');
        $('#info-farming').html(hypothec.farming || '-');
        $('#info-start-date').html(hypothec.start_date_text || '-');
        $('#info-due-date').html(hypothec.due_date_text || '-');
        $('#info-comment').html(comment.replace('\n', '<br>'));
        $('#info-tom').html(hypothec.tom || '-');
        $('#info-na-num').html(hypothec.na_num || '-');
        $('#info-delo').html(hypothec.delo || '-');
        $('#info-court').html(hypothec.court || '-');
        $('#info-deactivate-num').html(hypothec.deactivate_num || '-');
        $('#info-deactivate-date').html(hypothec.deactivate_date_text || '-');
    }

    function initAddEditFields(){
        var farmingComboboxData        = ComboboxData.FarmingCombobox;

        if(!hCreditorCb.data().hasOwnProperty('combobox')) {
            hCreditorCb.combobox({
                url: 'index.php?hypothecs-rpc=hypothecs-creditors',
                rpcMethod: 'readCombobox',
                rpcParams: [null, null],
                valueField: 'id',
                textField: 'name',
                required: true
            });
        }

        newFarmingComboboxData = [];
        farmingComboboxData.forEach(function (el) {
            if (el.id !== "") {
                newFarmingComboboxData.push(el);
            }
        });
        newFarmingComboboxData[0].selected = true;

        if(!hFarmingCb.data().hasOwnProperty('combobox')) {
            hFarmingCb.combobox({
                data: newFarmingComboboxData,
                valueField: 'id',
                textField: 'name',
                required: true
            });
        }

        if(!hDateDb.data().hasOwnProperty('datebox')) {
            hDateDb.datebox({
                required: true,
                value: todayDate
            });
        }

        if(!hStartDateDb.data().hasOwnProperty('datebox')) {
            hStartDateDb.datebox({
                required: true,
                value: todayDate
            });
        }

        if(!hDueDateDb.data().hasOwnProperty('datebox')) {
            hDueDateDb.datebox({
                required: true,
                value: todayDate
            });
        }

        if(!hNumTb.data().hasOwnProperty('textbox')) {
            hNumTb.textbox({
                required: true,
                missingMessage: 'Това поле е задължително.'
            });
        }
    }

    function clearAddFields() {
        hNumTb.textbox('reset');
        hDateDb.datebox('reset');
        hStartDateDb.datebox('reset');
        hDueDateDb.datebox('reset');
        hFarmingCb.combobox('reset');
        hCreditorCb.combobox('reset');
        $('#hypothec-tom').val('');
        $('#hypothec-na-num').val('');
        $('#hypothec-delo').val('');
        $('#hypothec-court').val('');
        $('#hypothec-comment').val('');
    }

    function populateEditFields(hypothec) {
        hNumTb.textbox('setValue', hypothec.num);
        hDateDb.datebox('setValue', hypothec.date);
        hStartDateDb.datebox('setValue', hypothec.start_date);
        hDueDateDb.datebox('setValue', hypothec.due_date);
        hFarmingCb.combobox('setValue', hypothec.farming_id);
        hCreditorCb.combobox('setValue', hypothec.creditor_id);
        $('#hypothec-tom').val(hypothec.tom);
        $('#hypothec-na-num').val(hypothec.na_num);
        $('#hypothec-delo').val(hypothec.delo);
        $('#hypothec-court').val(hypothec.court);
        $('#hypothec-comment').val(hypothec.comment);
    }

    function initFilterFields() {

        if(!searchCreditorCb.data().hasOwnProperty('combobox')) {
            searchCreditorCb.combobox({
                url: 'index.php?hypothecs-rpc=hypothecs-creditors',
                rpcMethod: 'readCombobox',
                rpcParams: [null, true],
                valueField: 'id',
                textField: 'name',
                required: false
            });
        }

        if(!searchFarmingCb.data().hasOwnProperty('combobox')) {
            searchFarmingCb.combobox({
                url: 'index.php?common-rpc=farming-combobox',
                rpcParams: [{
                    record_all: true
                }],
                valueField: 'id',
                textField: 'name',
                required: false
            });
        }

        if(!searchStatusCb.data().hasOwnProperty('combobox')) {
            searchStatusCb.combobox({
                data: [
                    {
                        id: '',
                        name: 'Всички'
                    },
                    {
                        id: 1,
                        name: 'Активен'
                    },
                    {
                        id: 0,
                        name: 'Прекратен'
                    }
                ],
                valueField: 'id',
                textField: 'name',
                required: false
            });
        }

        if(!searchStartDateFromDb.data().hasOwnProperty('datebox')) {
            searchStartDateFromDb.datebox({});
        }

        if(!searchStartDateToDb.data().hasOwnProperty('datebox')) {
            searchStartDateToDb.datebox({});
        }

        if(!searchDueDateFromDb.data().hasOwnProperty('datebox')) {
            searchDueDateFromDb.datebox({});
        }

        if(!searchDueDateToDb.data().hasOwnProperty('datebox')) {
            searchDueDateToDb.datebox({});
        }

        if(!searchEkateCb.data().hasOwnProperty('combobox')) {
            searchEkateCb.combobox({
                url: 'index.php?common-rpc=ekate-combobox',
                rpcParams: [{
                    record_all: true
                }],
                valueField: 'ekate',
                textField: 'text',
                required: false,
                filter: function(q, row){
					var opts = jQuery(this).combobox('options');
					var text = row[opts.textField].toLowerCase();
					var value = row[opts.valueField];
					var find = q.toLowerCase();
					if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
					{
						return true;
					}
				}
            });
        }

        if(!searchCategoryCb.data().hasOwnProperty('combobox')) {
            searchCategoryCb.combobox({
                url: 'index.php?common-rpc=plot-category-combobox',
                rpcParams: [{
                    record_all: true
                }],
                valueField: 'id',
                textField: 'name',
                required: false
            });
        }

        if(!searchAreaTypeCb.data().hasOwnProperty('combobox')) {
            searchAreaTypeCb.combobox({
                url: 'index.php?common-rpc=plot-ntp-combobox',
                rpcParams: [{
                    record_all: true
                }],
                valueField: 'id',
                textField: 'name',
                required: false
            });
        }

        if (!searchIrrigatedArea.data().hasOwnProperty('combobox')) {
            searchIrrigatedArea.combobox({
                editable: false,
                url: 'index.php?common-rpc=irrigated-area-combobox',
                rpcParams: [{
                    include_all: true,
                    selected: true
                }],
                valueField: 'value',
                textField: 'label',
                multiple: false,
                loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
            });
        }
    }

    return {
        tree: hypothecsTree
    };
}(jQuery));
