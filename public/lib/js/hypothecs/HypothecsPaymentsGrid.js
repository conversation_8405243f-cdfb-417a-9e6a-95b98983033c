Namespace('TF.Hypothecs.HypothecsPaymentsGrid');

TF.Hypothecs.HypothecsPaymentsGrid = (function ($) {
    var hypothecsPaymentsGrid = $('#hypothecs-payments-grid');
    var paymentAmountNb = $('#payment-amount');
    var paymentDateDb = $('#payment-date');
    var paymentCommentTa = $('#payment-comment');
    var editID;
    
    var date = new Date();
    var todayDate = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();
    
    paymentAmountNb.numberbox({
        required: true,
        precision: 2,
        min: 0.01
    });

    paymentDateDb.datebox({
        required: true,
        value: todayDate
    });
    
    $('#btn-save-payment').on('click', function(e) {
        e.preventDefault();
        
        if(!paymentAmountNb.numberbox('isValid')) {
            paymentAmountNb.textbox('textbox').focus();
            return;
        }
        
        if(!paymentDateDb.datebox('isValid')) {
            paymentDateDb.datebox('textbox').focus();
            return;
        }
        
        var amount = paymentAmountNb.numberbox('getValue');
        var date = paymentDateDb.datebox('getValue');
        var comment = paymentCommentTa.val();
        
        if(editID) {
            TF.Rpc.Hypothecs.HypothecsPaymentsGrid.edit(editID, amount, date, comment)
            .done(function(data) {
                hypothecsPaymentsGrid.datagrid('reload');
                $('#win-add-edit-payment').window('close');
            })
            .fail(function(error) {
                $.messager.alert('Грешка', error.getMessage());
            });
        }
        else
        {
            var hypothecId = TF.Hypothecs.HypothecsTree.tree.tree('getSelected').id;
            
            TF.Rpc.Hypothecs.HypothecsPaymentsGrid.add(hypothecId, amount, date, comment)
            .done(function(data) {
                hypothecsPaymentsGrid.datagrid('reload');
                $('#win-add-edit-payment').window('close');
            })
            .fail(function(error) {
                $.messager.alert('Грешка', error.getMessage());
            });
        }
	});
    
    hypothecsPaymentsGrid.datagrid({
		iconCls: 'icon-money',
		title: 'Плащания',
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		fit: true,
		fitColumns: true,
		showFooter: false,
		url: 'index.php?hypothecs-rpc=hypothecs-payments-grid',
        rpcParams: [null],
		idField: 'id',
		singleSelect: true,
        pagination: true,
		rownumbers: true,
		columns: [[
				{
					field: 'amount',
					title: '<b>Сума</b>',
					sortable: false,
					width: 100
				}, {
					field: 'date_text',
					title: '<b>Дата</b>',
					sortable: false,
					width: 100
				}, {
					field: 'comment',
					title: '<b>Забележка</b>',
					sortable: false,
					width: 100
				}
			]],
		toolbar: [{
				text: 'Добавяне',
				iconCls: 'icon-add',
				handler: function() {
                    var hypothec = TF.Hypothecs.HypothecsTree.tree.tree('getSelected');
                    
                    if(!hypothec) {
                        $.messager.alert('Грешка', 'Моля изберете изпотека.');
                        return;
                    }
                    
                    editID = undefined;
                    clearAddEditFields();
					$('#win-add-edit-payment').window('open');
				}
			}, {
				text: 'Редактиране',
				iconCls: 'icon-edit',
				handler: function() {
					var selectedPayment = hypothecsPaymentsGrid.datagrid('getSelected');
                    
                    if(!selectedPayment) {
                        $.messager.alert('Грешка', 'Моля изберете запис.');
                        return;
                    }
                    
                    editID = selectedPayment.id;
                    populateAddEditFields(selectedPayment);
					$('#win-add-edit-payment').window('open');
				}
			}, {
				text: 'Изтриване',
				iconCls: 'icon-remove',
				handler: function() {
					var selectedPayment = hypothecsPaymentsGrid.datagrid('getSelected');
                    
                    if(!selectedPayment) {
                        $.messager.alert('Грешка', 'Моля изберете запис.');
                        return;
                    }
                    
					$.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да премахнете този запис?', function(r) {
                        if (r) {
                            TF.Rpc.Hypothecs.HypothecsPaymentsGrid.delete(selectedPayment.id)
                                .done(function(data) {
                                    hypothecsPaymentsGrid.datagrid('reload');
                                })
                                .fail(function(error) {
                                    $.messager.alert('Грешка', error.getMessage());
                                });
                        }
                    });
				}
			}],
		onLoadSuccess: function() {
			hypothecsPaymentsGrid.datagrid('clearChecked');
		}
	});
    
    //custom pager
	var pager = hypothecsPaymentsGrid.datagrid('getPager');
	pager.pagination({
		beforePageText: 'Стр.',
		displayMsg: 'От {from} до {to} от {total} записа'
	});
    
    function clearAddEditFields() {
        paymentAmountNb.numberbox('reset');
        paymentDateDb.datebox('reset');
        paymentCommentTa.val('');
    }
    
    function populateAddEditFields(payment) {
        paymentAmountNb.numberbox('setValue', payment.amount);
        paymentDateDb.datebox('setValue', payment.date);
        paymentCommentTa.val(payment.comment);
    }

    return {
        grid: hypothecsPaymentsGrid
    };
}(jQuery));