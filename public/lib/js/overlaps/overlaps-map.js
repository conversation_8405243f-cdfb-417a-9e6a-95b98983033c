var isSuperAdmin = false;
var layersKvs = false;

var renderer = OpenLayers.Util.getParameters(window.location.href).renderer;
if (renderer) {
    OpenLayers.Layer.Vector.prototype.renderers = [renderer];
}

var mapKVS;
var boundsKvsArray;
var layerKvsArray;


jQuery(function() {
    initiateKVSLegend();

    jQuery('#btn-choose-kvs-zoom').bind('click', function() {
        if (jQuery('#choose-kvs-zoom-ekate > input').combobox('getValue'))
        {
            var obj = new Object();
            obj.ekate = jQuery('#choose-kvs-zoom-ekate > input').combobox('getValue');

            TF.Rpc.Overlaps.OverlapsMapTools.getKVSExtent(obj)
            .done(function (data) {
                jQuery('#win-choose-kvs-zoom').window('close');
                zoomToKVS(data);
            })
            .fail(function (errorObj) {

            });
        } else {
            jQuery.messager.alert('Грешка', 'Не е намерен КВС слой!');
        }
    });
});

function showMapOverlaps() {
    //jQuery('#win-map').window('open');
    initMapKvs();
}

function initiateKVSLegend() {

    TF.Rpc.Overlaps.OverlapsMapTools.getLayerKVS()
    .done(function (data) {
        layersKvs = data;
        var kvsPlotsLegend = jQuery('#kvs-plots-legend-row');
        var tmpKvsPlotsStyle = kvsPlotsLegend.attr('style');
        var newKvsPlotLegendColorText = 'background-color: #'+data[0]['color']+';';
        tmpKvsPlotsStyle = tmpKvsPlotsStyle.concat(newKvsPlotLegendColorText);
        kvsPlotsLegend.attr('style',tmpKvsPlotsStyle);
    })
    .fail(function (errorObj) {

    });
}

var vectors;
function initMapKvs() {
    if (!mapKVS) {
        var options = {
            controls: [new OpenLayers.Control.Navigation(), new OpenLayers.Control.PanZoomBar(), new OpenLayers.Control.ScaleLine({bottomInUnits: 'km'})],
            projection: new OpenLayers.Projection("EPSG:900913")
        };
        mapKVS = new OpenLayers.Map('map', options);
        initMapPad();
    }

    if (layerKvsArray)
    {
        for (var i = 0; i < layerKvsArray.length; i++)
        {
            mapKVS.removeLayer(layerKvsArray[i])
        }
    }

    boundsKvsArray = [];
    for (var i = 0; i < layersKvs.length; i++) {
        boundsKvsArray[i] = new OpenLayers.Bounds.fromString(layersKvs[i].extent).transform(
                new OpenLayers.Projection("EPSG:32635"),
                mapKVS.getProjectionObject()
                );
    }

    layerKvsArray = [];
    for (var i = 0; i < layersKvs.length; i++)
    {
        if (layersKvs[i].extent && layersKvs[i].name)
        {
            layerKvsArray[i] = new OpenLayers.Layer.WMS(
                    layersKvs[i].name,
                    wmsServer + "?map=" + mapPath + groupID + '.map',
                    {
                        layers: layersKvs[i].name,
                        format: 'image/png',
                        transparent: "true"
                                //map: mapPath+userid+'.map'
                    }, {
                displayInLayerSwitcher: false
            }
            );
        }
    }

    if (layersKvs.length)
        mapKVS.addLayers(layerKvsArray);

    if (layersKvs.length)
    {
        mapKVS.zoomToExtent(boundsKvsArray[0]);
    }
    else
    {
        mapKVS.zoomToExtent(new OpenLayers.Bounds.fromString("125190.6162, 4573142.7188, 631370.3273, 4887149.5823").transform(
                new OpenLayers.Projection("EPSG:32635"),
                mapKVS.getProjectionObject())
                );
    }

    mapKVS.render("map");
    reloadAllKvsLayers();
}

function reloadAllKvsLayers() {
    for (var i = 0; i < layerKvsArray.length; i++) {
        layerKvsArray[i].redraw(true);
    }
}

function initLegend(data)
{
    if (data['extent'])
    {
        mapKVS.zoomToExtent(new OpenLayers.Bounds.fromString(data['extent']).transform(
                new OpenLayers.Projection("EPSG:32635"),
                mapKVS.getProjectionObject()));
    }

    jQuery('#legend-tree').tree({
        data: data['colorarray'],
        formatter: function(node) {
            if (node.color)
                return "<div style='width:13px;height:13px;background-color:#" + node.color + ";margin-top:3px;float:left;margin-right:3px;'></div>" + node.name;
            else if (node.text)
                return node.text;
        }
    });
}

function propertyWindowFunction(e) {
    bounds = mapKVS.getExtent();

    x1 = parseInt(e.xy.x);
    y1 = parseInt(e.xy.y);

    var mapObj = {
        bbox: bounds.toBBOX(),
        x: x1,
        y: y1,
        width: mapKVS.size.w,
        height: mapKVS.size.h,
        layer: layersKvs[0]['id'],
        plot_info: true
    };

    TF.Rpc.Plots.PlotMap.read(mapObj)
    .done(function (data) {
        initFillPlotInfo(data['plot_info']);
        initPlotsContractsGrid(data['id']);
        plotID = data['id'];
        jQuery('#win-plot-info').window('open');
    })
    .fail(function (errorObj) {
        jQuery.messager.alert('Грешка',errorObj.getMessage(), 'warning');
    });
}

function initMapTools() {
    jQuery('#tool-map-info').bind('click', function() {
        var options = jQuery('#tool-map-info').linkbutton('options');
        if (!options.selected) {
            jQuery('#tool-map-info').linkbutton('select');
            if (!options.disabled) {
                mapKVS.events.register('click', mapKVS, propertyWindowFunction);
            }
        } else {
            jQuery('#tool-map-info').linkbutton('unselect');
            mapKVS.events.unregister('click', mapKVS, propertyWindowFunction);
        }

        return false;
    });

    jQuery('#tool-kvs-zoom').bind('click', function() {
        initZoomToKvsPanel();
        jQuery('#win-choose-kvs-zoom').window('open');

        return false;
    });
}

function initZoomToKvsPanel() {
    jQuery('#choose-kvs-zoom-ekate > input').combobox({
        url: 'index.php?common-rpc=ekate-combobox',
        rpcParams: [{
            selected: true
        }],
        textField: 'ekate',
        valueField: 'ekate',
        editable: false,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function zoomToKVS(data) {
    if (data['extent']) {
        mapKVS.zoomToExtent(new OpenLayers.Bounds.fromString(data['extent']).transform(
                    new OpenLayers.Projection("EPSG:32635"),
                    mapKVS.getProjectionObject())
                );
    }
}

var bingApiKey = "AiWEso3-IjWyX1aZMuep9Sjl62D6FUMqv8qQGpV-kgbW0qWk61to4nrqHh-2D5HL";

function initMapPad(specific_map_type)
{
    var chosenMapType;

    //init all map features
    var options = {
        controls: [],
        projection: new OpenLayers.Projection("EPSG:900913")
    };

    //on init map type will not be specified
    if (specific_map_type == undefined)
    {
        chosenMapType = store.get('map_pad') || 1;
        //init map
    }
    //when map type is changed specific_map_type will have the value of map type
    else {
        chosenMapType = parseInt(specific_map_type);
    }

    var layerMapPad;

    switch (chosenMapType)
    {
        case 2:
            layerMapPad = new OpenLayers.Layer.Bing({
                key: bingApiKey,
                type: "Aerial",
                name: "MapPad"
            });
            break;
        case 3:
            layerMapPad = new OpenLayers.Layer.Bing({
                key: bingApiKey,
                type: "Road",
                name: "MapPad"
            });
            break;
        case 4:
            layerMapPad = new OpenLayers.Layer.Google(
                    "MapPad",
                    {type: google.maps.MapTypeId.TERRAIN}
            );
            break;
        case 5:
            layerMapPad = new OpenLayers.Layer.Google(
                    "MapPad",
                    {numZoomLevels: 20}
            );
            break;
        case 6:
            layerMapPad = new OpenLayers.Layer.Google(
                    "MapPad",
                    {type: google.maps.MapTypeId.HYBRID, numZoomLevels: 20}
            );
            break;
        case 7:
            layerMapPad = new OpenLayers.Layer.Google(
                    "MapPad",
                    {type: google.maps.MapTypeId.SATELLITE, numZoomLevels: 22}
            );
            break;
        case 8:
            layerMapPad = new OpenLayers.Layer.OSM();
            break;
        case 9:
            layerMapPad = new OpenLayers.Layer.WMS(
                'MapPad',
                imagesWMSServer + "?map=" + '/var/www/satellite_processor/maps/geo_scan.map',
                //wmsServer + "?map=" + mapPath + groupID + '.map',
                {
                    layers: 'geo_scan'
                },
                {
                    numZoomLevels: 18
                });
            break;
        default: // default is bing aerial with labels
            layerMapPad = new OpenLayers.Layer.Bing({
                key: bingApiKey,
                type: "AerialWithLabels",
                name: "MapPad"
            });
            break;
    }

    //specific map type will be given only on reload
    //on first load(init) specific map type should be undefined
    if (specific_map_type == undefined)
    {
        mapKVS.addLayer(layerMapPad);
    }
    else {
        mapKVS.addLayer(layerMapPad);
        mapKVS.setLayerIndex(mapKVS.layers[mapKVS.layers.length - 1], 0);
        mapKVS.removeLayer(mapKVS.layers[1]);
        mapKVS.layers[0].redraw(true);
    }
}
