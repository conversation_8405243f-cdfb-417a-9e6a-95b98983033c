function initSideReports(overlap_id) {
    jQuery('#side-report-tables').datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        fit: true,
        fitColumns: true,
        url: 'index.php?overlaps-rpc=overlaps-area-report-grid',
        rpcParams: [{
            overlap_id: overlap_id
        }],
        idField: '',
        sortName: '',
        sortOrder: 'asc',
        scrollbarSize: 20,
        border: false,
        showFooter: true,
        columns: [
            [
                {
                    field: 'isak_prc_uin',
                    title: '<b>ИСАК</b>',
                    sortable: true,
                    align: 'center',
                    width: 80
                }, {
                    field: 'zp_area',
                    title: '<b>Площ <br/>(дка)</b>',
                    sortable: true,
                    align: 'center',
                    width: 50
                }, {
                    field: 'area',
                    title: '<b>Застъпени<br/>КВС имоти<br/>(дка)</b>',
                    sortable: true,
                    align: 'center',
                    width: 60
                }
            ]
        ],
        onBeforeLoad: function() {
            jQuery('#side-report-tables').datagrid('clearChecked');
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}
