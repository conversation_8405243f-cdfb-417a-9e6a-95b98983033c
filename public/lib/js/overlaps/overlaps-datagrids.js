var currentOverlapId;

function initOverlapsDataGrid(id){
    currentOverlapId = id;
    jQuery('#data-tables').datagrid({
        nowrap: true,
        border: false,
        autoRowHeight: true,
        striped: true,
        pageSize: 20,
        fit: true,
        fitColumns: true,
        showFooter: true,
        url:'index.php?overlaps-rpc=overlaps-data-datagrid',
        rpcParams: [{
            overlap_id: id
        }],
        sortName: '',
        sortOrder: 'asc',
        idField:'id',
        singleSelect: true,
        rowStyler: function(index,row){
            if (row.has_match == 0){
                return 'background-color:#ff0000; color: #fff';
            }
        },
        columns:[[
        {
            field:'ekatte',
            title:'<b>ЕКАТТЕ</b>',
            sortable:true,
            width:250
        },{
            field:'land',
            title:'<b>Землище</b>',
            sortable:true,
            width:350
        },{
            field:'bzs',
            title:'<b>БЗС номер</b>',
            sortable:true,
            width:250
        },{
            field:'kad_ident',
            title:'<b>Идентификатор</b>',
            sortable:true,
            width:250
        },{
            field:'area',
            title:'<b>Площ</b>',
            sortable:true,
            width:250
        }]],
        pagination:true,
        rownumbers:false,
        toolbar: [{
            id:'btndatafilter',
            text:'Филтър',
            iconCls:'icon-filter',
            handler:function(){
                jQuery('#win-data-filter').window('open');
            }
        },{
            id:'btncleardatafilter',
            text:'Покажи всички',
            iconCls:'icon-clear-filter',
            handler:function(){
                clearOverlapFilter();
            }
        }],
        onSelect: function(rowIndex, rowData) {
            if(rowData.has_match) {
                jQuery('#btnkvsinfo').linkbutton('enable');
            } else {
                jQuery('#btnkvsinfo').linkbutton('disable');
            }
        },
        onLoadSuccess: function() {
            jQuery('#btnkvsinfo').linkbutton('disable');
            jQuery('#data-tables').datagrid('uncheckAll');
            jQuery('#data-tables').datagrid('unselectAll');
        },
        onBeforeLoad: function(){
            jQuery('#data-tables').datagrid('clearChecked');
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initAddOverlapFields() {
    var farmingData = jQuery('#overlap-farming > input').combobox('getData');
    var yearData = jQuery('#overlap-farming-year > input').combobox('getData');

    jQuery('#overlap-farming > input').combobox('setValue', farmingData[0]['id']);
    jQuery('#overlap-farming-year > input').combobox('setValue', yearData[0]['id']);

    jQuery('#overlap-name > input').val('');
}

function initPlotInfoFill(plot_id){
    var plotObj = {
        plot_id: plot_id
    };
    TF.Rpc.Common.PlotInfo.read(plotObj)
    .done(function (data) {
        fillPlotInfoFields(data);
    })
    .fail(function (errorObj) {

    });
}

function fillPlotInfoFields(data) {
    jQuery('#info-ident').html(data['kad_ident']);
    jQuery('#info-category').html(data['category']);
    jQuery('#info-ntp').html(data['area_type']);
    jQuery('#info-ekatte').html(data['ekate']);
    jQuery('#info-masiv').html(data['masiv']);
    jQuery('#info-imot').html(data['number']);

    jQuery('#info-include').hide();
    jQuery('#info-participate').hide();
    jQuery('#info-white-spots').hide();
    jQuery('#info-missing-wishes').hide();

    if(data['include'] == true) {
        jQuery('#info-include').show();
    }
    if(data['participate'] == true) {
        jQuery('#info-participate').show();
    }
    if(data['white_spots'] == true){
        jQuery('#info-white-spots').show();
    }
    if(data['include'] == false && data['participate'] == false && data['white_spots'] == false) {
        jQuery('#info-missing-wishes').show();
    }
}

function overlapFilter(){
    var withContracts = '';
    if(jQuery('#with-contracts input').is(':checked')) {
        withContracts = true;
    } else if(jQuery('#without-contracts input').is(':checked')) {
        withContracts = false;
    }

    var hasMatch = '';
    if(jQuery('#has-match input').is(':checked')) {
        hasMatch = true;
    } else if(jQuery('#has-no-match input').is(':checked')){
        hasMatch = false;
    }

    jQuery('#data-tables').datagrid({
        rpcParams: [{
            overlap_id: currentOverlapId,
            has_contracts: withContracts,
            has_match: hasMatch
        }]
    });
    jQuery('#win-data-filter').window('close');
}

function clearOverlapFilter(){
    jQuery('#data-tables').datagrid({
        pageNumber: 1,
        rpcParams: [{
            overlap_id: currentOverlapId,
            has_contracts: '',
            has_match: ''
        }]
    });
}
