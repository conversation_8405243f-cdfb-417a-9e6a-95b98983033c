jQuery(function(){
	setUserLastLogin();

	jQuery('#overlap-farming > input').combobox({
		url: 'index.php?common-rpc=farming-combobox',
		rpcParams: [{
			selected:true
		}],
		valueField: 'id',
		textField: 'name',
		editable: false,
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

	jQuery('#overlap-farming-year > input').combobox({
		url: 'index.php?common-rpc=farming-year-combobox',
		rpcParams: [{
			selected: 'current'
		}],
		valueField: 'id',
		textField: 'title',
		editable: false,
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

	jQuery('#btn-add-overlap').bind('click', function(){
		var addOverlapObj = getAddNewOverlapFieldsData();

		TF.Rpc.Overlaps.OverlapsLayersTree.add(addOverlapObj)
		.done(function (data) {
			jQuery('#add-window').window('close');
			jQuery('#overlap-layers-tree').tree('reload');
		})
		.fail(function (errorObj) {
			RpcErrorHandler.show(errorObj);
		});
	});

	jQuery('#btn-data-filter').bind('click', function(){
		overlapFilter();
	});

	initOverlapLayersTree();
	initMapTools();
});

function initFileUploader(overlap_id) {
	var url  = "index.php?json=overlaps-upload";

	jQuery("#uploader").pluploadQueue({
        // General settings
        runtimes : 'gears,html5,flash,silverlight,browserplus',
        url : url,
        max_file_size : '100mb',
        unique_names : true,
		multipart_params : {
			overlap_id: overlap_id
		},
        // Flash settings
        flash_swf_url : 'lib/js_external/fileupload/plupload.flash.swf',
        filters : [
	        {title : "CSV Файлове", extensions : "csv"},
	    ]

    });

    var uploader = jQuery('#uploader').pluploadQueue();
    uploader.bind('UploadComplete', function() {
    	jQuery('#win-add-file').window('close');
    	jQuery('#overlaps-tables').datagrid('reload');
    });
}

function getAddNewOverlapFieldsData() {
	return {
		name: jQuery('#overlap-name > input').val(),
		farming: jQuery('#overlap-farming > input').combobox('getValue'),
		year: jQuery('#overlap-farming-year > input').combobox('getValue'),
	}
}
