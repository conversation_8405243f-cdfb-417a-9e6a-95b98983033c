function initOverlapLayersTree() {
    jQuery('#overlap-layers-tree').tree({
        url: 'index.php?overlaps-rpc=overlaps-layers-tree',
        animate: true,
        lines: true,
        onBeforeSelect: function(node) {
            var isLeaf = jQuery('#overlap-layers-tree').tree('isLeaf', node.target);

            if (!isLeaf)
                return false;
        },
        onSelect: function(node) {
            var isLeaf = jQuery('#overlap-layers-tree').tree('isLeaf', node.target);
            var layerData = jQuery('#overlap-layers-tree').tree('getSelected');

            if (layerData.id) {

                initOverlapsDataGrid(layerData.id);
                initSideReports(layerData.id);

                var yearData = jQuery('#overlap-layers-tree').tree('getParent', layerData.target);
                var farmingData = jQuery('#overlap-layers-tree').tree('getParent', yearData.target);

                var obj = new Object();
                obj.overlap_id = layerData.id;
                obj.farming = farmingData.id;
                obj.year = yearData.id;

                TF.Rpc.Overlaps.OverlapsMapTools.initKVSMap(obj)
                .done(function (data) {
                    showMapOverlaps();
                    initLegend(data);
                })
                .fail(function (errorObj) {

                });
            } else {
                initOverlapsDataGrid(0);
            }
        },
        onLoadSuccess: function() {
            var level_1 = jQuery('#overlap-layers-tree').tree('getRoots');

            if (level_1.length == 0) {
                //init data datagrid
                initOverlapsDataGrid(0);
                initSideReports(0);
                //init map without params for any layers
                TF.Rpc.Overlaps.OverlapsMapTools.initKVSMap()
                .done(function (data) {
                    showMapOverlaps();
                    initLegend(data);
                })
                .fail(function (errorObj) {

                });
            } else {
                var level_2 = jQuery('#overlap-layers-tree').tree('getChildren', level_1[0].target);
                var level_3 = jQuery('#overlap-layers-tree').tree('getChildren', level_2[0].target);

                jQuery('#overlap-layers-tree').tree('select', level_3[0].target);
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#add-overlap-layer').bind('click', function() {
        initAddOverlapFields();
        jQuery('#add-window').window('open');

        return false;
    });

    jQuery('#delete-overlap-layer').bind('click', function() {
        var getSelected = jQuery('#overlap-layers-tree').tree('getSelected');

        if (getSelected.id) {
            jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да изтриете този запис?', function(r) {
                if (r) {
                    TF.Rpc.Overlaps.OverlapsLayersTree.delete(getSelected.id)
                    .done(function (data) {
                        jQuery('#overlap-layers-tree').tree('reload');
                        jQuery('#overlaps-tables').datagrid('uncheckAll');
                        jQuery('#overlaps-tables').datagrid('unselectAll');
                    })
                    .fail(function (errorObj) {

                    });
                }
            })
        } else {
            jQuery.messager.alert('Грешка', 'Моля изберете запис, който искате да изтриете.');
        }

        return false;
    });

    jQuery('#upload-overlap-file').bind('click', function() {
        var getSelected = jQuery('#overlap-layers-tree').tree('getSelected');
  
        if (getSelected && getSelected.id) {
            initFileUploader(getSelected.id);
            jQuery('#win-add-file').window('open');
        } else {
            jQuery.messager.alert('Грешка', 'Моля изберете запис, за който да добавите данни.');
        }

        return false;
    });
}
