function initPlotsContractsGrid(plot_id) {
	jQuery('#contract-info-tables').datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        iconCls: 'icon-contract',
        title: 'Договори',
        pageSize: 10,
        fit: true,
        singleSelect: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?zplots-rpc=kvs-contracts-datagrid',
        rpcParams: [{
            type: 'view',
            plot_id: plot_id
        }],
        sortName: '',
        sortOrder: 'asc',
        idField: '',
        frozenColumns:[[
        {
            field:'ck',
            checkbox:true
        },
        ]],
        columns:[[{
            field:'c_num',
            title:'<b>Номер</b>',
            sortable:true,
            width: 170
        },{
            field:'c_date',
            title:'<b>Дата</b>',
            sortable:true,
            width: 170
        },{
            field:'nm_usage_rights',
            title:'<b>Тип</b>',
            sortable:true,
            width: 170
        },{
            field:'start_date',
            title:'<b>Влизане в сила</b>',
            sortable:true,
            width: 170
        },{
            field:'farming',
            title:'<b>Стопанство</b>',
            sortable:true,
            width: 200
        },{
            field:'due_date',
            title:'<b>Крайна дата</b>',
            sortable:true,
            width: 200
        }
        ]],
        pagination:true,
        rownumbers:true,
        toolbar:[{
            id:'btnownerinfo',
            text:'Информация',
            iconCls:'icon-info',
            handler:function(){
            	var getChecked = jQuery('#contract-info-tables').datagrid('getChecked');
            	if(getChecked[0]) {
            		dispatchContractInfoRequest(getChecked[0]['id']);
            	} else {
            		jQuery.messager.alert('Грешка', 'Моля изберете собственик, за който да бъде показана информация.');
            	}
            }
        }],
        onLoadSuccess: function() {
        	var gridData = jQuery('#contract-info-tables').datagrid('getData');
        	if(gridData['rows'][0]){
        		jQuery('#contract-info-tables').datagrid('selectRow', 0);
        		initPlotsOwnersGrid(plot_id, gridData['rows'][0]['id']);
        	} else {
        		initPlotsOwnersGrid(plot_id, 0);
        	}
        },
        onSelect: function(rowIndex, rowData) {
        	initPlotsOwnersGrid(plot_id, rowData['id']);
        },
        onBeforeLoad: function(){
        	jQuery('#contract-info-tables').datagrid('clearChecked');
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initPlotsOwnersGrid(plot_id, contract_id) {
	jQuery('#owner-info-tables').datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        iconCls: 'icon-owners',
        title: 'Собственици',
        pageSize: 10,
        fit: true,
        singleSelect: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?zplots-rpc=kvs-owners-datagrid',
        rpcParams: [{
            type: 'view',
            contract_id: contract_id,
            plot_id: plot_id
        }],
        sortName: '',
        sortOrder: 'asc',
        idField: '',
        frozenColumns:[[
        {
            field:'ck',
            checkbox:true
        },
        ]],
        columns:[[
  		{
		    field:'rep_names',
		    title:'<b>Имена на представител</b>',
		    sortable:true,
		    width: 150
		},{
		    field:'owner_names',
		    title:'<b>Имена на физическо лице</b>',
		    sortable:true,
		    width: 150
		},{
		    field:'company_name',
		    title:'<b>Фирма</b>',
		    sortable:true,
		    width: 150
		},{
		    field:'rat_ownage',
		    title:'<b>% собственост</b>',
		    sortable:true,
		    width: 75
		}
	    ]],
        pagination:true,
        rownumbers:true,
        toolbar:[{
            id:'btnownerinfo',
            text:'Информация',
            iconCls:'icon-info',
            handler:function(){
            	var getChecked = jQuery('#owner-info-tables').datagrid('getChecked');
            	if(getChecked[0]) {
            		dispatchOwnerInfoRequest(getChecked[0]['id']);
            	} else {
            		jQuery.messager.alert('Грешка', 'Моля изберете собственик, за който да бъде показана информация.');
            	}
            }
        }],
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function dispatchOwnerInfoRequest(owner_id) {

    TF.Rpc.Common.OwnersInfo.read({id: owner_id})
    .done(function (data) {
        initOwnersInfoPanelFromResponse(data);
        jQuery('#owner-info-panel').window('open');
    })
    .fail(function (errorObj) {

    });
}

function initOwnersInfoPanelFromResponse(responseText){
	if (responseText.owner_type_id == 0) {
    	jQuery('#owner-info-panel').window('resize', {
    		width: 420,
    		height: 370
    	});
    	var html = '<fieldset style="border: 1px solid #000; padding: 5px 10px;">' +
    	'<legend style="font-style: italic; font-weight: bold">Информация за собственик</legend>' +
    	'Вид собственик: ' + responseText.owner_type + '<br/>' +
    	'Фирма: ' + responseText.company_name + '<br/>' +
        'ЕИК: ' + responseText.eik + '<br/>' +
        '</fieldset>';
    } else {
    	jQuery('#owner-info-panel').window('resize', {
    		width: 420,
    		height: 405
    	});
    	var html = '<fieldset style="border: 1px solid #000; padding: 5px 10px;">' +
    	'<legend style="font-style: italic; font-weight: bold">Информация за собственик</legend>' +
    	'Вид собственик: ' + responseText.owner_type + '<br/>' +
        'Имена: ' + responseText.name + ' ' + responseText.surname + ' ' + responseText.lastname + '<br/>' +
        'ЕГН: ' + responseText.egn + '<br/>' +
        'Номер на лична карта: ' + responseText.lk_nomer + '<br/>' +
        'Издаване на ЛК: ' + responseText.lk_izdavane + '<br/>' +
        '</fieldset>';
    }
    html += '<fieldset style="border: 1px solid #000; padding: 5px 10px; margin-top: 10px;">' +
    '<legend style="font-style: italic; font-weight: bold">Информация за представител</legend>' +
    'Имена: '  + responseText.rep_name + ' ' + responseText.rep_surname + ' ' + responseText.rep_lastname + '<br/>' +
    'ЕГН: ' + responseText.rep_egn + '<br/>' +
    'Номер на ЛК: ' + responseText.rep_lk_nomer + '<br/>' +
    'Издаване на ЛК: ' + responseText.rep_lk_izdavane + '<br/>' +
    '</fieldset>' + '<fieldset style="border: 1px solid #000; padding: 5px 10px; margin-top: 10px;">' +
    '<legend style="font-style: italic; font-weight: bold">Допълнителна информация за собственик</legend>' +
    'Телефон: ' + responseText.phone + '<br/>' +
    'Факс: ' + responseText.fax + '<br/>' +
    'Мобилен телефон: ' + responseText.mobile + '<br/>' +
    'Емейл: ' + responseText.email + '<br/>' +
    'Адрес: ' + responseText.address +
    '</fieldset>';
    jQuery('#owner-info-panel').html(html);
}

function dispatchContractInfoRequest(contract_id) {


    var requestObj = {id: contract_id};
    TF.Rpc.Common.ContractInfo.read(requestObj)
    .done(function (data) {
        initContractInfoPanelFromResponse(data);
        jQuery('#contract-info-panel').window('open');
    })
    .fail(function (errorObj) {

    });
}

function initContractInfoPanelFromResponse(responseText) {
	var html = '';
	html += '<fieldset style="border: 1px solid #000; padding: 5px 10px;">';
	html += '<legend style="font-style: italic; font-weight: bold">Основна информация</legend>';
	html += 'Тип: ' + responseText.c_type + '</br>';
	html += 'Номер: ' + responseText.c_num + '</br>';
	html += 'Сключване: ' + responseText.c_date + '</br>';
	html += 'Влизане в сила: ' + responseText.start_date + '</br>';
	html += 'Крайна дата: ' + responseText.due_date + '</br>';
	html += 'Стопанство: ' + responseText.farming + '</br>';
	html += '</fieldset>';

	html += '<fieldset style="border: 1px solid #000; padding: 5px 10px; margin-top: 5px;">';
	html += '<legend style="font-style: italic; font-weight: bold">Документ за собственост</legend>';
	html += 'Тип: ' + responseText.ds_type + '</br>';
	html += 'Номер и дата: ' + responseText.ds_num_date + '</br>';
	html += '</fieldset>';

	html += '<fieldset style="border: 1px solid #000; padding: 5px 10px; margin-top: 5px;">';
	html += '<legend style="font-style: italic; font-weight: bold">Служба по вписване</legend>';
	html += 'Номер на вписване: ' + responseText.sv_num + '</br>';
	html += 'Номер на вписване: ' + responseText.sv_date + '</br>';
	html += '</fieldset>';

	html += '<fieldset style="border: 1px solid #000; padding: 5px 10px; margin-top: 5px;">';
	html += '<legend style="font-style: italic; font-weight: bold">Рента</legend>';
	html += 'Сума: ' + responseText.renta + '</br>';
	html += 'В натура - тип: ' + responseText.renta_nat_type + '</br>';
	html += 'В натура - кол.: ' + responseText.renta_nat + '</br>';
	html += '</fieldset>';

	html += '<fieldset style="border: 1px solid #000; padding: 5px 10px; margin-top: 5px; height: 110px;">';
	html += '<legend style="font-style: italic; font-weight: bold">Информация за контрагент</legend>';
	html += 'Име: ' + responseText.cont_name + '</br>';
	html += 'Номер: ' + responseText.cont_number + '</br>';
	html += 'Землище: ' + responseText.cont_land + '</br>';
	html += 'Адрес: ' + responseText.cont_address + '</br>';
	html += '</fieldset>';

	jQuery('#contract-info-panel').html(html);
}

function initFillPlotInfo(data) {
	jQuery('#info-ident').html(data['kad_ident']);
    jQuery('#info-category').html(data['category']);
    jQuery('#info-ntp').html(data['area_type']);
    jQuery('#info-ekatte').html(data['ekate']);
    jQuery('#info-masiv').html(data['masiv']);
    jQuery('#info-imot').html(data['number']);

    jQuery('#info-include').hide();
    jQuery('#info-participate').hide();
    jQuery('#info-white-spots').hide();
    jQuery('#info-missing-wishes').hide();

    if(data['include'] == true) {
            jQuery('#info-include').show();
    }
    if(data['participate'] == true) {
            jQuery('#info-participate').show();
    }
    if(data['white_spots'] == true){
            jQuery('#info-white-spots').show();
    }
    if(data['include'] == false && data['participate'] == false && data['white_spots'] == false) {
		jQuery('#info-missing-wishes').show();
    }
}
