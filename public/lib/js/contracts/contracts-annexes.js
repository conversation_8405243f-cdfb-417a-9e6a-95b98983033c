var rentaResult;
function initAnnexesGrid(contract_id) {
	jQuery('#contract-annexes-tables').datagrid({
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		fit: true,
		singleSelect: true,
		fitColumns: true,
		rownumbers: true,
		rowStyler: function(index, row) {
			if (row.active == 0) {
				return 'color: #aaa';
			}
		},
		url: 'index.php?contracts-rpc=contracts-annexes-grid',
		rpcParams: [{
			 contract_id: contract_id
		}],
		columns: [
			[
				{
					field: 'c_num',
					title: '<b>Номер</b>',
					sortable: true,
					align: 'center',
					width: 80
				}, {
					field: 'c_date',
					title: '<b>Дата</b>',
					sortable: true,
					align: 'center',
					width: 100
				}, {
					field: 'active_text',
					title: '<b>Статус</b>',
					sortable: true,
					align: 'center',
					width: 100
				}, {
					field: 'start_date',
					title: '<b>Начална дата</b>',
					sortable: true,
					align: 'center',
					width: 100
				}, {
					field: 'due_date',
					title: '<b>Крайна дата</b>',
					sortable: true,
					align: 'center',
					width: 100
				}, {
					field: 'sv_num',
					title: '<b>Номер на<br/> вписване</b>',
					sortable: true,
					align: 'center',
					width: 150
				}, {
					field: 'sv_date',
					title: '<b>Дата на<br/> вписване</b>',
					sortable: true,
					align: 'center',
					width: 100
				}, {
					field: 'renta_txt',
					title: '<b>Рента</b>',
					sortable: true,
					align: 'center',
					width: 50
				}, {
					field: 'renta_nat_type',
					title: '<b>Вид на<br/>рента в натура</b>',
					sortable: true,
					align: 'center',
					width: 150
				}, {
					field: 'renta_nat',
					title: '<b>Количество на<br/>рента в натура</b>',
					sortable: true,
					align: 'center',
					width: 100
				}
			]
		],
		toolbar: [{
			id: 'btnannexinfo',
			text: 'Информация',
			iconCls: 'icon-info',
			handler: function() {
				var getSelected = jQuery('#contract-annexes-tables').datagrid('getSelected');
				if (getSelected) {
					window.open("index.php?page=Contracts.Home&contract_id=" + getSelected.id, '_blank');
				} else {
					jQuery.messager.alert('Грешка', 'Моля изберете анекс, за който да бъде показана информация.');
				}
			}
		}],
		onBeforeLoad: function() {
			jQuery('#contract-annexes-tables').datagrid('clearChecked');
		},
        onLoadSuccess: function() {
            const rows =jQuery('#contract-annexes-tables').datagrid('getRows');

            jQuery('#annex-tab-counter').text(rows.length);
        },
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function initAddAnnexFields() {
	jQuery('#win-add-edit-annex').window({
        onClose: removeAnnexRentaFields
    });

	var date = new Date();
	var todayDate = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();

	jQuery('#add-new-annex-renta-btn').linkbutton({
	    iconCls: 'icon-add',
	    width:26
	});
	jQuery('#remove-annex-renta-btn').linkbutton({
	    iconCls: 'icon-cancel',
	    width:26
	});
	jQuery('#annex-number > input').validatebox({
		required: true,
		missingMessage: 'Моля въведете номер на анекс.'
	});

	jQuery('#annex-date > input').datebox({
		value: todayDate,
	});

	jQuery('#annex-start-date > input').datebox({
		value: businessStartDate,
	});

	jQuery('#annex-due-date > input').datebox({
		value: businessEndDate,
	});

	jQuery('#annex-sv-date > input').datebox({});
	jQuery('#annex-osz-date > input').datebox({});

	var daySpinner = jQuery('#annex-pd-day > input');
	jQuery('#annex-pd-month > input').combobox({
		data: months,
		valueField: 'value',
		textField: 'label',
		onLoadSuccess: function() {
			daySpinner.numberspinner({
				min: 1,
				max: 31,
				precision: 0,
			});
		},
		onSelect: function(rec){
			var day = daySpinner.numberspinner('getValue');
			if(day > rec.maxDays) {
				day = rec.maxDays;
			}
			daySpinner.numberspinner({
				min: 1,
				max: rec.maxDays,
				precision: 0,
				value:day,
			});
		}
	});

	jQuery('#annex-renta > input').numberspinner({
		precision: 2,
		min: 0
	});

    jQuery('#annex-overall-renta > input').numberspinner({
        precision: 2,
        min: 0
    });

	jQuery('#annex-renta-error-win').window({
	    width:275,
	    height:150,
	    modal:true,
	    closed:true,
	    resizable: false,
	    collapsible: false,
	    minimizable: false,
	    maximizable: false,
	    title: 'Грешка при избор на тип на рента.'
	});
}

function validateAnnexDates() {
    return jQuery('#annex-number > input').val()
        && jQuery('#annex-date > input').datebox('getValue')
        && jQuery('#annex-start-date > input').datebox('getValue')
        && jQuery('#annex-due-date > input').datebox('getValue');
}

function validateSaveAnnex() {
    if (!validateAnnexDates()) {
        if (jQuery('#annex-number > input').val() == '') {
            jQuery.messager.alert('Грешка', 'Моля задайте номер на анекс!');
            return false;
        }
    }

    if (!compareDates('#annex-start-date > input', '#annex-due-date > input', 2)) {
        jQuery.messager.alert('Грешка', 'Въведена е грешна дата - крайната дата е по-ранна от датата на влизане в сила на анекса.', 'warning');
        return false;
    }

    var annexPlotsTable = jQuery('#annex-plots-tables');
    var addedPlotsData = [];
    var removedPlotsGids = [];

    var allPlotsData = annexPlotsTable.treegrid('getData');

    if (allPlotsData.length == 0) {
        return jQuery.messager.alert('Грешка', 'Не може да анексирате договор, в който не са включени имоти!');
    }

    let selectedParents = [];
	var allGidsStr = '';

	for (let i = 0; i < allPlotsData.length; i++) {
		if(allPlotsData[i].children) {
			let gidsArr = allPlotsData[i].gid.toString().split(',');
			if(!gidsArr && allPlotsData[i].gid) {
				gidsArr = allPlotsData[i].gid;
			}

			for(const child of allPlotsData[i].children){
				allGidsStr += child.gid + ',';
			}
			selectedParents = [...selectedParents, ...gidsArr];

			for(let gid in allPlotsData[i].areas) {
				let areaObj = {};
				areaObj.gid = gid;
				areaObj.contract_area = allPlotsData[i].areas[gid].contract_area;
				areaObj.document_area = allPlotsData[i].areas[gid].document_area;
				areaObj.area_for_rent = allPlotsData[i].areas[gid].area_for_rent;
                areaObj.kvs_allowable_area = allPlotsData[i].areas[gid].kvs_allowable_area;
				areaObj.rent_per_plot = allPlotsData[i].areas[gid].rent_per_plot;
			}
		}
	}

    for (var i = 0; i < allPlotsData.length; i++) {
    	if (parseFloat(allPlotsData[i].area_for_rent) > parseFloat(allPlotsData[i].contract_area)) {
			return jQuery.messager.alert('Грешка', 'Площа по рента не трябва да надвишава площа по договор! Идентификатор:' + allPlotsData[i].kad_ident);
		}
        allGidsStr += allPlotsData[i].gid + ',';
    }

    var allGids = allGidsStr.replace(/,\s*$/, "").split(',');
    var selectedPlotsData = annexPlotsTable.treegrid('getChecked');
    var selectedGids = [];
    for (var i = 0; i < selectedPlotsData.length; i++) {
        //end editing first
        var row = selectedPlotsData[i];
        annexPlotsTable.treegrid('endEdit', row.index);
        var contractAreaEditor = annexPlotsTable.treegrid('getEditor', {id: row.index, field: 'contract_area'});
        var documentAreaEditor = annexPlotsTable.treegrid('getEditor', {id: row.index, field: 'document_area'});
        if (contractAreaEditor || documentAreaEditor) {
            return jQuery.messager.alert('Грешка', 'Моля попълнете всички задължителни полета!');
        }

        var children = annexPlotsTable.treegrid('getChildren', row.index);

        for (var j = 0; j < children.length; j++) {
            annexPlotsTable.treegrid('endEdit', children[j].index);
        }
        if (!row.is_checkable) {
            continue;
        }
        selectedGids.push(row.gid.toString());
        var plotObj = {};
        plotObj.plot_id = row.gid;
        plotObj.contract_area = row.contract_area;
        plotObj.document_area = row.document_area;
        plotObj.area_for_rent = row.area_for_rent;
        plotObj.rent_per_plot = row.rent_per_plot;
        plotObj.kvs_allowable_area = row.kvs_allowable_area;
        addedPlotsData.push(plotObj);
    }
    removedPlotsGids = jQuery(allGids).not(selectedParents).not(selectedGids).get();
    //######

    var obj = getAddAnnexData();
    obj.added_plots_data = addedPlotsData;
    obj.removed_plots_gids = removedPlotsGids;

    if (allPlotsData.length > 0) {
        if (typeof allPlotsData[0].annex_id != undefined) {
            obj.annex_id = allPlotsData[0].annex_id;
        }
    }

    TF.Rpc.Contracts.ContractsTree.addAnnex(obj)
		.done(function (data) {
			jQuery('#win-add-edit-annex').window('close');
			jQuery('#contract-annexes-tables').datagrid('reload');
            jQuery('#contracts-tree').tree('reload');
			annexPlotsTable.treegrid('unselectAll');
    	})
		.fail(function (errorObj) {
			if (errorObj && errorObj.is(TF.Rpc.ExceptionsList.ANNEXES_INVALID_ANNEX_DATE)) {
				jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.ANNEXES_INVALID_ANNEX_DATE.message, 'warning');
			} else if (errorObj && errorObj.is(TF.Rpc.ExceptionsList.WRONG_PLOT_AREA_FOR_RENT_TOO_LARGE)) {
				jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.WRONG_PLOT_AREA_FOR_RENT_TOO_LARGE.message, 'warning');
			} else if (errorObj && errorObj.is(TF.Rpc.ExceptionsList.INVALID_ANNEX_CONTRACTS)) {
				jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.INVALID_ANNEX_CONTRACTS.message, 'warning'); 
            } else if(errorObj.is(TF.Rpc.ExceptionsList.CONTRACT_AREA_EXCEEDS_PLOT_AREA)) {
				return jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.CONTRACT_AREA_EXCEEDS_PLOT_AREA.message, 'error');
			} else if(errorObj.is(TF.Rpc.ExceptionsList.WRONG_PLOT_AREA)) {
				return jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.WRONG_PLOT_AREA.message, 'error');
			} else if (errorObj.is(TF.Rpc.ExceptionsList.CONTRACT_PLOT_DUPLICATION)) {
				jQuery('#win-copy-filtered-contracts-plots-error').window('open');  
				initUnsuccessfullyCopiedContracts(
					errorObj.getOriginalMessage()
				);
			} else if (errorObj.is(TF.Rpc.ExceptionsList.CONTRACTS_HAS_PAYMENTS_EXCEPTION)) {        
                jQuery('#win-has-payments-error').window('open');  
                initUnsuccessfullyEditContract(
                    errorObj.getOriginalMessage()
                );
            }
			else {
				RpcErrorHandler.show(errorObj);
			}
		}
    );
}

function initContractsAnnexesPlotsGrid() {
    var contract_id = jQuery('#contracts-tree').tree('getSelected').id;
    var annexPlotsTables = jQuery('#annex-plots-tables');
    annexPlotsTables.treegrid({
        iconCls: 'icon-edit-geometry',
        nowrap: true,
        title: 'Имоти',
        autoRowHeight: true,
        striped: true,
        animate: true,
        pageSize: 10,
        fit: true,
        singleSelect: true,
        selectOnCheck: false,
        checkOnSelect: false,
        fitColumns: true,
        showFooter: false,
        url: 'index.php?contracts-rpc=contracts-annexes-plots-datagrid',
        rpcParams: [{
            contract_id: contract_id
        }],
        sortName: 'kad_ident',
        sortOrder: 'asc',
        idField: 'index',
        treeField: 'kad_ident',
        pagination: false,
        rownumbers: true,
        rowStyler: function (row) {
            if (!row.is_checkable) {
                return 'color: #897f7f;background:#f7f7f7';
            }
        },
        frozenColumns: [[
            {
                field: 'ck',
                checkbox: true,
                styler: function (value, row) {
                    if (!row.is_checkable) {
                        return 'visibility: hidden;';
                    }
                }
            }]],
        columns: [[
            {
                field: 'kad_ident',
                title: '<b>Идентификатор</b>',
                sortable: true,
                width: 200
            }, {
                field: 'ekatte_name',
                title: '<b>Землище</b>',
                sortable: true,
                width: 100,
            }, {
                field: 'category',
                title: '<b>Категория</b>',
                sortable: true,
                width: 100
            }, {
                field: 'area_type',
                title: '<b>НТП</b>',
                sortable: true,
                width: 100
            }, {
                field: 'contract_area',
                title: '<b>Площ по<br/>договор (дка)</b>',
                sortable: true,
                halign: 'center',
                width: 110,
                editor: {
                    type: 'numberbox',
                    options: {
                        required: true,
                        missingMessage: 'Полето е задължително',
                        precision: 3
                    }
                }
            }, {
                field: 'document_area',
                title: '<b>Площ по<br/>документ (дка)</b>',
                sortable: true,
                halign: 'center',
                width: 100,
                editor: {
                    type: 'numberbox',
                    options: {
                        required: true,
                        missingMessage: 'Полето е задължително',
                        precision: 3
                    }
                }
            }, {
                field: 'area_for_rent',
                title: '<b>Площ за<br/>рента (дка)</b>',
                sortable: true,
                halign: 'center',
                width: 100,
                editor: {
                    type: 'numberbox',
                    options: {
                        required: true,
                        missingMessage: 'Полето е задължително',
                        precision: 3
                    }
                }
            }, {
                field: 'kvs_allowable_area',
                title: '<b>Обработваема <br/> площ (дка)</b>',
                sortable: true,
                halign: 'center',
                width: 100,
                editor: {
                    type: 'numberbox',
                    options: {
                        required: true,
                        missingMessage: 'Полето е задължително',
                        precision: 3
                    }
                }
            },{
                field: 'rent_per_plot',
                title: '<b>Цена/дка</b>',
                sortable: true,
                halign: 'center',
                width: 110,
                editor: {
                    type: 'numberbox',
                    options: {
                        required: false,
                        precision: 2
                    }
                }
            },
            {
                field: 'action',
                title: '',
                sortable: false,
                halign: 'center',
				align: 'center',
                width: 60,
                formatter: function (value, row) {
                    if (!row.hasOwnProperty('gid')) {
                        return;
                    }
                    return '<a onclick="onEditAnnexPlot(' + row.index + ' , ' + row.is_checkable + ');" class="easyui-linkbutton l-btn l-btn-small l-btn-plain" group=""><span class="l-btn-left l-btn-icon-left"><span class="l-btn-text"></span><span class="l-btn-icon icon-edit" title="Редактиране">&nbsp;</span></span></a>';
                }
            }
        ]],
        onBeforeCheck: function (row) {
            if (!row.hasOwnProperty("is_checkable")) {
                return;
            }
            return row.is_checkable;
        },
        onDblClickRow: function (row) {
            if (!row.hasOwnProperty("index") || row.index < 0) {
                return;
            }
            var editors = jQuery(this).treegrid("getEditors", row.index);
            var hasEditor = editors.length > 0;
            var mode = hasEditor ?  "endEdit" :"beginEdit";
            jQuery(this).treegrid(mode, row.index);
        },
        onLoadSuccess: function (row, data) {
            jQuery(this).treegrid('clearChecked');
            jQuery(this).treegrid('clearSelections');
            jQuery(this).treegrid('uncheckAll');
            jQuery(this).treegrid('checkAll');

			for (var i = 0; i < data.rows.length; i++) {
				if(data.rows[i].annex_action === 'removed') {
					jQuery(this).treegrid('uncheckRow', i);
				}
			}
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function onEditAnnexPlot(index , is_checkable) {
	if(index < 0 || !is_checkable){
		return false;
	}
    var editors = jQuery("#annex-plots-tables").treegrid("getEditors", index);
	var inEditMode = editors.length > 0;
	var mode = "beginEdit";
	if (inEditMode) {
		mode = "endEdit";
	}
	jQuery("#annex-plots-tables").treegrid(mode, index);
	return false;
}

function addAnnexRentaField(params)
{
	var rows = jQuery('#annex-renta-table').find('.js-annex-renta-type-row');
	var rentaRows = rows.length;

	var removeButton = '<a id="remove-annex-renta-row-btn-' + (rentaRows) + '" href="javaScript:void(0)" class="easyui-linkbutton" onClick="removeAnnexRentaField(' + (rentaRows) + ');"data-options="iconCls:\'icon-cancel\'">&nbsp;</a>';
	var addButton = '<a id="add-new-renta-btn" href="javaScript:void(0)" class="easyui-linkbutton" onClick="addAnnexRentaField();" data-options="iconCls:\'icon-add\'"></a>';
	var comboHTML ='<tr><td style="padding-left:10px"><select class="js-annex-renta-type-row" style="width: 60px;" id="annex-renta-type-cb-'+(rentaRows)+'"></select></td>';

	//first row
	if(rentaRows == 0) {
		comboHTML += '<td><input class="js-renta-value" style="width: 60px;" id="annex-renta-value-'+(rentaRows)+'"></td><td>'+addButton+'</td></tr>';
	} else {
		comboHTML += '<td><input class="js-renta-value" style="width: 60px;" id="annex-renta-value-'+(rentaRows)+'"></td><td>'+removeButton+'</td></tr>';
	}

	jQuery('#annex-renta-table').append(comboHTML);
	jQuery('#remove-annex-renta-row-btn-' + (rentaRows)).linkbutton({
		iconCls: 'icon-cancel',
	    width:26
	});

	if(rentaRows == 0) {
		jQuery('#add-new-renta-btn').linkbutton({
			iconCls: 'icon-add',
		    width:26
		});
	}

	jQuery('#annex-renta-type-cb-'+(rentaRows)).combobox({
		url: 'index.php?common-rpc=renta-types-combobox',
    	valueField: 'id',
    	rpcParams: [{
    		as_list: true
    	}],
    	textField: 'name',
    	width: 220,
    	onLoadSuccess: function() {
			var renta_id = 0;
            var renta_value = 0;

            if(params) {
                renta_value = params['data']['renta_value'];
                renta_id = params['data']['renta_id'];
            }
            jQuery('#annex-renta-type-cb-'+(rentaRows)).combobox('select', renta_id);

    		jQuery('#annex-renta-value-'+(rentaRows)).numberspinner({
				min: 0,
				precision: 3,
				missingMessage: 'Моля задайте количество на горепосочената рента в натура(количество за декар).',
				width: 100,
				value: renta_value
			});
		},
		onSelect: function(rec){
			const rowArr = jQuery('.js-annex-renta-type-row');
            const rentaTypesArr = Array.from(rowArr).map((row) => 
                jQuery(`#${row.id}`).combobox('getValue')
            );

            let oldValue = jQuery(`#annex-renta-type-cb-${rentaRows}`).combobox('getValue');
            var isRentaTypeSelected = jQuery.inArray(String(rec.id), rentaTypesArr);

            if(isRentaTypeSelected !== -1) {
                jQuery.messager.alert('Внимание', 'Вече има въведена натура от избраният тип. Моля изберете друг тип натура!', 'warning');
                setTimeout(function(){
                    jQuery(`#annex-renta-type-cb-${rentaRows}`).combobox('select', oldValue);
                },0);
                return false;
            }

			setTimeout(function(){
                const currentValue  = jQuery('#annex-renta-type-cb-'+(rentaRows)).combobox('getValue');
                const currnetРentaTypesArr = Array.from(rowArr).map((row) => 
                    jQuery(`#${row.id}`).combobox('getValue')
                );

                if(0 !== currentValue && !currnetРentaTypesArr.includes('0')) {
                    jQuery('#add-new-renta-btn').linkbutton({
                        disabled: false
                    });
                } else {
                    jQuery('#add-new-renta-btn').linkbutton({
                        disabled: true
                    });
                }
            },0);

            try {
                var rentaNatValue = jQuery('#annex-renta-value-' + (rentaRows)).numberspinner('getValue');
            } catch (err) {
            	return;
            }

			var prec = 3;
			if(rec['unit'] === 3){
				prec = 0;
			}

			jQuery('#annex-renta-value-'+(rentaRows)).numberspinner({
				min: 0,
				precision: prec,
				value: rentaNatValue,
				missingMessage: 'Моля задайте количество на горепосочената рента в натура(количество за декар).',
				width: 100
			});

			if(jQuery('#annex-renta-type-cb-'+(rentaRows)).combobox('getValue') == 0)
			{
				jQuery('#annex-renta-value-'+(rentaRows)).numberspinner('setValue', '0');
			}

    		var rowVars=[];
    		var tmpVars = jQuery('.js-annex-renta-type-row');
    		for (var i = 0; i < tmpVars.length; i++) {
				var natura_el = jQuery('#annex-renta-type-cb-' + i);
    			var natura_id = natura_el.combobox('getValue');

    			rowVars.push(natura_id);
			}

    		if(rec.id != 0) {
				var inRes = jQuery.inArray(String(rec.id),rowVars);

				if (inRes != -1 && jQuery.inArray(String(rec.id),rowVars,inRes+1) != -1) {
					jQuery('#annex-renta-error-win').window('open');
				 	jQuery('#annex-renta-type-cb-'+(rentaRows)).combobox('clear');
				 	jQuery('#annex-renta-value-'+(rentaRows)).numberspinner('setValue', '0');
				}
			}
    	},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function removeAnnexRentaFields() {
    var total = jQuery('.js-annex-renta-type-row').length;
    for (var i = 0 ; i <= total; i++) {
        removeAnnexRentaField(i);
    }
}

function removeAnnexRentaField(index){
	var parent = jQuery('#annex-renta-type-cb-'+index).parent().parent();
	parent.detach();

	const rowArr = jQuery('.js-annex-renta-type-row');
    const rentaTypesArr = Array.from(rowArr).map((row) => 
        jQuery(`#${row.id}`).combobox('getValue')
    );

    if(!rentaTypesArr.includes('0')) {
        jQuery('#add-new-renta-btn').linkbutton({
            disabled: false
        });
    } else {
        jQuery('#add-new-renta-btn').linkbutton({
            disabled: true
        });
    }
}

function getAddAnnexData() {
    var returnObj = {
		annex_parent_id: jQuery('#contracts-tree').tree('getSelected').id,
        annex_num: jQuery("#annex-number > input").val(),
        annex_start_date: jQuery("#annex-start-date > input").datebox('getValue'),
        annex_date: jQuery("#annex-date > input").datebox('getValue'),
        annex_due_date: jQuery("#annex-due-date > input").datebox('getValue'),
        annex_sv_num: jQuery("#annex-sv-num > input").val(),
        annex_sv_date: jQuery("#annex-sv-date > input").datebox('getValue'),
        annex_osz_num: jQuery("#annex-osz-num > input").val(),
        annex_osz_date: jQuery("#annex-osz-date > input").datebox('getValue'),
        annex_pd_day: jQuery("#annex-pd-day > input").numberspinner('getValue'),
        annex_pd_month: jQuery("#annex-pd-month > input").combobox('getValue'),
        annex_comment: jQuery("#annex-comment > textarea").val()
    };

    var contractData = jQuery('#contracts-tree').tree('getSelected');

    if (contractData.attributes.overall_renta != null) {
        returnObj.overall_renta = jQuery("#annex-overall-renta > input").numberspinner('getValue');
    } else {
        returnObj.annex_renta = jQuery("#annex-renta > input").numberspinner('getValue');
    }

    returnObj.additionalRentas = [];

    for (var i = 0; i < jQuery('.js-annex-renta-type-row').length; i++) {
        var natId = parseInt(jQuery('#annex-renta-type-cb-' + i).combobox('getValue'));
        var natQt = parseFloat(jQuery('#annex-renta-value-' + i).val());
        if (natId == 0 || isNaN(natQt)) {
            continue;
        }
        returnObj.additionalRentas[i] = {
            type: natId,
            value: natQt
        }
    }
    return returnObj;
}
