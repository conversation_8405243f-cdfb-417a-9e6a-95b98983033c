/*jslint browser: true*/
/*global jQuery, EasyUIRPCLoaders.EasyUIGridCustomLoader, TF,bufferview, Fraction, months, hasContractsOwnWriteRights, messagerContractsOwnWriteRights */
var editContractID;
var copyContractID;
var from_alert = false;

function initContractsTree(pageNumber, filterObj) {
    editContractID = undefined;
    copyContractID = undefined;

    var page_number = (pageNumber !== undefined) ? pageNumber : 1;
    var sort_by = jQuery('input[name=sortcontractsgroup]:checked').val();
    var order_by = jQuery('input[name=sortcontractstypegroup]:checked').val();
    var contractsTree = jQuery('#contracts-tree');
    var isTreeBound = contractsTree.data().hasOwnProperty('tree');

    if (!sort_by) {
        sort_by = 'id';
    }
    if (!order_by) {
        order_by = 'desc';
    }

    if((filterObj && filterObj['from_alert'] != undefined && filterObj['from_alert'] == 1) || from_alert){
        delete(filterObj['contract_id']);
        filterObj['from_alert'] = from_alert = true;
    }

    if (isTreeBound) {
        contractsTree.tree({
            rpcParams: [filterObj],
            page: page_number,
            sort: sort_by,
            order: order_by
        });
        return;
    }
 
    contractsTree.tree({
        url: 'index.php?contracts-rpc=contracts-tree',
        animate: false,
        lines: true,
        page: page_number,
        sort: sort_by,
        order: order_by,
        rows: 30,
        rpcParams: [filterObj],
        onSelect: function (node) {
            initContractInfo(node.attributes);
            initFilesGrid(node.id);
            initContractsPlotsGrid(node.id, node.attributes.c_type, node.attributes.hasPayment);

            if (node.attributes.c_type == 1 || node.attributes.c_type == 4 || node.attributes.is_annex == true) {
                jQuery('#contracts-tabs').tabs('disableTab', 2);
                if (node.attributes.is_annex == true ) {
                    jQuery('#contracts-tabs').tabs('select', 0);
                    jQuery('#contracts-tabs').tabs('enableTab', 1);
                } else {
                    jQuery('#contracts-tabs').tabs('disableTab', 1);
                }
                

                jQuery('#contracts-tabs').tabs('enableTab', 0);
                jQuery('#contracts-tabs').tabs('enableTab', 3);
            } else {
                jQuery('#contracts-tabs').tabs('enableTab', 0);
                jQuery('#contracts-tabs').tabs('enableTab', 1);
                jQuery('#contracts-tabs').tabs('enableTab', 2);
                jQuery('#contracts-tabs').tabs('enableTab', 3);
            }

            if (node.attributes.c_type == 4 || node.attributes.is_annex == true) {
                jQuery('#info-pd-date').hide();
                jQuery('#info-pd-date-label').hide();
            } else {
                jQuery('#info-pd-date').show();
                jQuery('#info-pd-date-label').show();
                initAnnexesGrid(node.id);
            }

            if (node.attributes.c_type == 1) {
                jQuery('#contract-price-per-plot').show();
            } else {
                jQuery('#contract-price-per-plot').hide();
            }

            if (node.attributes.c_type == 2 || node.attributes.c_type == 3 || node.attributes.c_type == 5) {
                jQuery('#allow-transfer-to-payments').linkbutton('enable');
            } else {
                jQuery('#allow-transfer-to-payments').linkbutton('disable');
            }
        },
        onLoadSuccess: function () {
            var roots = jQuery(this).tree('getRoots'),
                total = 0,
                limit = 30;
            if (roots.length) {
                if (editContractID != undefined) {
                    var node = jQuery('#contracts-tree').tree('find', editContractID);
                    jQuery('#contracts-tree').tree('select', node.target);
                } else {
                    jQuery('#contracts-tree').tree('select', roots[0].target);
                }
                total = roots[0].attributes.pagination.total;
                limit = roots[0].attributes.pagination.limit;
            } else {
                jQuery('#contract-info-layout').layout('remove', 'south');
                jQuery.messager.alert('Внимание', 'Не са открити записи', 'warning');
            }

            //init pagination with total contract elements
            initContractsPagination(total, limit);
        },
        formatter: function (node) {
            if (node.attributes.active_text === 'Анулиран') {
                node.text = '<font color="#aaa"><strike>' + node.text + '</strike></font>';
            }
            if (node.attributes.active_text === 'Изтекъл') {
                node.text = '<font color="#aaa">' + node.text + '</font>';
            }
            return node.text;
        },
        onBeforeLoad: function () {
            if (jQuery('#contract-plots-tables').data().hasOwnProperty('datagrid')) {
                jQuery('#contract-plots-tables').datagrid('loadData', {
                    "rows": [],
                    "total": 0,
                    "footer": [{"contract_area": 0, "document_area": 0}]
                });
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initContractsPagination(total, limit) {
    jQuery('#contracts-tree-pagination').pagination({
        showPageList: false,
        showRefresh: true,
        displayMsg: '',
        total: total,
        pageSize: limit,
        onSelectPage: function (pageNumber, pageSize) {
            //cover case of filtered results
            var obj = getFilterContractsTreeParams();
            initContractsTree(pageNumber, obj);
        }
    });
}

function initContractInfo(contractData) {
    if (!contractData || !contractData.c_type) {
        jQuery('#info-contract-type').html('');
        jQuery('#info-contract-number').html('');
        jQuery('#info-contract-group').html('');
        jQuery('#info-contract-date').html('');
        jQuery('#info-start-date').html('');
        jQuery('#info-due-date').html('');
        jQuery('#info-farming').html('');
        jQuery('#info-na-num').html('');
        jQuery('#info-tom').html('');
        jQuery('#info-delo').html('');
        jQuery('#info-court').html('');
        jQuery('#info-comment').html('');
        jQuery('#info-renta').html('');
        jQuery('#info-renta-label').html('');
        jQuery('#info-renta-nat-type').html('');
        jQuery('#info-renta-nat').html('');
        jQuery('#info-sv-num').html('');
        jQuery('#info-sv-date').html('');
        jQuery('#info-osz-num').html('');
        jQuery('#info-osz-date').html('');
        jQuery('#info-pd-date').html('');
        jQuery('#info-active-text').html('');
        jQuery('.js-renta-additional-row').remove();
        jQuery('#info-is-closed-for-editing').html('');
        jQuery('#is-declaration-subleased').html('');

        return false;
    }


    if (contractData.c_type == 1) {
        jQuery('#extra-info-fieldset').show();
        jQuery('#renta-info-fieldset').hide();
        jQuery('#extra-osz-fieldset').hide();
    } else {
        jQuery('#extra-info-fieldset').hide();
        jQuery('#renta-info-fieldset').show();
        jQuery('#extra-osz-fieldset').show();
    }

    if (contractData.has_rent_per_plot === true) {
        jQuery('#rent-per-plot-info').show();
    } else {
        jQuery('#rent-per-plot-info').hide();
    }


    if (contractData.from_sublease > 0) {
        jQuery('#from-sublease-fields-group').show();
        var link = '<a class="easyui-linkbutton" href="index.php?page=Subleases.Home&sublease_id=' + contractData.from_sublease + '" target="_blank" data-options="iconCls: \'icon-redirect\', plain:true">' + contractData.c_num + '</a>';
        jQuery('#info-from-sublease').html(link);
        jQuery('#info-from-sublease > a').linkbutton();
    } else {
        jQuery('#from-sublease-fields-group').hide();
    }

    jQuery('#info-contract-type').html(contractData.nm_usage_rights);
    jQuery('#info-contract-number').html(contractData.c_num);
    jQuery('#info-contract-group').html(contractData.group_name);
    jQuery('#info-contract-id').html(contractData.ao_c_num);
    jQuery('#info-contract-date').html(contractData.c_date);
    jQuery('#info-start-date').html(contractData.start_date);
    jQuery('#info-due-date').html(contractData.due_date);
    jQuery('#info-farming').html(contractData.farming);
    jQuery('#info-na-num').html(contractData.na_num);
    jQuery('#info-tom').html(contractData.tom);
    jQuery('#info-delo').html(contractData.delo);
    jQuery('#info-court').html(contractData.court);
    jQuery('#info-comment').html(contractData.comment);
    jQuery('#info-renta').html(contractData.renta_text);
    jQuery('#info-renta-label').html('Рента/дка:');
    jQuery('#info-renta-nat-type').html(contractData.renta_nat_type);
    jQuery('#info-renta-nat').html(contractData.renta_nat_text);
    jQuery('#info-sv-num').html(contractData.sv_num);
    jQuery('#info-sv-date').html(contractData.sv_date);
    jQuery('#info-osz-num').html(contractData.osz_num);
    jQuery('#info-osz-date').html(contractData.osz_date);
    jQuery('#info-is-closed-for-editing').html(contractData.is_closed_for_editing_text);
    jQuery('#is-declaration-subleased').html(contractData.is_declaration_subleased_text);

    if (contractData.overall_renta != null) {
        jQuery('#info-renta').html(contractData.overall_renta);
        jQuery('#info-renta-label').html('Обща сума за договор:');
    }

    if(contractData.ao_c_num) {
        jQuery('.showHideSystemId').show();
    } else {
        jQuery('.showHideSystemId').hide();
    }

    var paymonth = '',
        rowHTML,
        i;
    if (months[contractData.paymonth] != undefined) {
        paymonth = months[contractData.paymonth].label;
    }
    jQuery('#info-pd-date').html(contractData.payday + ' ' + paymonth);
    jQuery('#info-active-text').html(contractData.active_text);

    jQuery('.js-renta-additional-row').remove();
    for (i = 0; i < contractData.additionalRentas.length; i++) {
        rowHTML = '<tr class="js-renta-additional-row"><td>' + contractData.additionalRentas[i].renta_nat_type + '</td><td  style="padding-left:200px;">' + contractData.additionalRentas[i].renta_nat_text + '</td></tr>';
        jQuery('#renta-info-fieldset table').append(rowHTML);
    }
}

function validateSaveContract() {
    var typeID = jQuery('#contract-type > input').combobox('getValue');
    if (typeID) {
        if (typeID == 1) {
            //No Rights to operate with "Договори за собственост"
            if (!hasContractsOwnWriteRights) {
                messagerContractsOwnWriteRights();
                return false;
            }
            if (jQuery('#contract-number > input').val() !== ''
                && jQuery('#contract-date > input').datebox('getValue') !== ''
                && jQuery('#contract-start-date > input').datebox('getValue') !== ''
                && jQuery('#contract-farming > input').combobox('getValue') !== '') {
                if (!validateContractDate(typeID)){
                    return jQuery.messager.alert('Грешка', 'Моля, въведете валидна дата.');
                }
                checkForExistance();
            } else {
                jQuery.messager.alert('Грешка', 'Моля попълнете всички задължителни полета.');
            }
        } else if (typeID == 2 || typeID == 3) {
            if (jQuery('#contract-number > input').val() !== ''
                && jQuery('#contract-date > input').datebox('getValue') !== ''
                && jQuery('#contract-start-date > input').datebox('getValue') !== ''
                && jQuery('#contract-farming > input').combobox('getValue') !== ''
                && jQuery('#contract-due-date > input').datebox('getValue') !== ''
                && jQuery('#renta_price').numberspinner('getValue') >= 0) {
                if (!validateContractDate(typeID)){
                    return jQuery.messager.alert('Грешка', 'Моля, въведете валидна дата.');
                }
                if (!compareDates('#contract-start-date > input', '#contract-due-date > input', 2)) {
                    jQuery.messager.alert('Грешка', 'Въведена е грешна дата - крайната дата е по-ранна от датата на влизане в сила на договора.', 'warning');
                    return false;
                }
                checkForExistance();
            } else {
                jQuery.messager.alert('Грешка', 'Моля попълнете всички задължителни полета.');
            }
            return false;
        } else if (typeID == 5) {
            if (jQuery('#contract-number > input').val() !== ''
                && jQuery('#contract-date > input').datebox('getValue') !== ''
                && jQuery('#contract-start-date > input').datebox('getValue') !== ''
                && jQuery('#contract-farming > input').combobox('getValue') !== ''
                && jQuery('#contract-due-date > input').datebox('getValue') !== ''
                && jQuery('#renta_price').numberspinner('getValue') >= 0) {
                if (!validateContractDate(typeID)){
                    return jQuery.messager.alert('Грешка', 'Моля, въведете валидна дата.');
                }
                if (!compareDates('#contract-start-date > input', '#contract-due-date > input', 2)) {
                    jQuery.messager.alert('Грешка', 'Въведена е грешна дата - крайната дата е по-ранна от датата на влизане в сила на договора.', 'warning');
                    return false;
                }
                checkForExistance();
            } else {
                jQuery.messager.alert('Грешка', 'Моля попълнете всички задължителни полета.');
            }
        } else {
            if (jQuery('#contract-number > input').val() !== ''
                && jQuery('#contract-date > input').datebox('getValue') !== ''
                && jQuery('#contract-start-date > input').datebox('getValue') !== ''
                && jQuery('#contract-farming > input').combobox('getValue') !== ''
                && jQuery('#contract-due-date > input').datebox('getValue') !== ''
                && jQuery('#contract-agg-type > input').combobox('getValue') > 0) {
                if (!validateContractDate(typeID)){
                    return jQuery.messager.alert('Грешка', 'Моля, въведете валидна дата.');
                }
                if (!compareDates('#contract-start-date > input', '#contract-due-date > input', 2)) {
                    jQuery.messager.alert('Грешка', 'Въведена е грешна дата - крайната дата е по-ранна от датата на влизане в сила на договора.', 'warning');
                    return false;
                }
                checkForExistance();
            } else {
                jQuery.messager.alert('Грешка', 'Моля попълнете всички задължителни полета.');
            }
        }
    } else {
        jQuery.messager.alert('Грешка', 'Моля попълнете всички задължителни полета.');
    }
}

function validateContractDate(usageRight){
    var cDateValid = jQuery('#contract-date > input').datebox('isValid');
    var startValid = jQuery('#contract-start-date > input').datebox('isValid');
    var svValid = jQuery('#sv-date > input').datebox('isValid');
    var oszValid =jQuery('#osz-date > input').datebox('isValid');
    var endValid = false;
    if(usageRight != CONTRACT_TYPE_OWN){
        endValid = jQuery('#contract-due-date > input').datebox('isValid');
    }else{
        endValid = true;
    }
    return cDateValid && startValid && endValid && svValid && oszValid;
}

function initMultiEditContracts() {
    const rentToSection = jQuery('#multiedit-area-for-rent-checkbox');
    const rentToContract = jQuery("#multiedit-area-for-rent-to-contract-checkbox");

    rentToSection.change(function() {
        if (rentToSection.is(':checked') == true) {
            rentToContract.prop('checked',false);
        }
    })

    rentToContract.change(function() {
        if (rentToContract.is(':checked') == true) {
            rentToSection.prop('checked',false);
        }
    })
}

function executeMultiEditContracts() {
    var contracts = jQuery('#contracts-tree').tree('getRoots'),
        data = getMultiEditContractFields(),
        obj = getFilterContractsTreeParams(false);

    if (contracts.length === 0) {
        jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.NO_CONTRACTS_FOR_MULTIEDIT.message, 'error');
        return;
    }

    if (!data) {
        return;
    }

    if (data.renta === '' && data.additionalRentas.length === 0 && data.setAreaForRentToAllowableArea === false && data.setAreaForRentToContractArea === false && data.setAreaForRentToArableArea === false) {
        jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.NO_MULTIEDIT_VALUES.message, 'error');
        return;
    }

    //get all filtered contracts

    TF.Rpc.Contracts.ContractsTree.getAllFilteredContracts(obj)
        .done(function (contracts) {
            if (contracts.length === 0) {
                jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.NO_CONTRACTS_FOR_MULTIEDIT.message, 'error');
                return;
            }

            TF.Rpc.Contracts.ContractsTree.saveMultiEditContracts(contracts, data)
                .done(function () {
                    jQuery('#win-multiedit-contracts').window('close');
                    jQuery('#contracts-tree').tree('loadRpc');
                })
                .fail(function (errorObj) {
                    if (errorObj.is(TF.Rpc.ExceptionsList.NO_CONTRACTS_FOR_MULTIEDIT)) {
                        jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.NO_CONTRACTS_FOR_MULTIEDIT.message, 'error');
                    } else if (errorObj.is(TF.Rpc.ExceptionsList.NO_MULTIEDIT_VALUES)) {
                        jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.NO_MULTIEDIT_VALUES.message, 'error');
                    }
                    else if (errorObj.is(TF.Rpc.ExceptionsList.CONTRACTS_HAS_PAYMENTS_EXCEPTION)) {        
                        jQuery('#win-has-payments-error').window('open');  
                        initUnsuccessfullyEditContract(
                            errorObj.getOriginalMessage()
                        );
                        jQuery('#win-multiedit-contracts').window('close');
                    }
                });
        })
        .fail(function (errorObj) {
            if (errorObj.is(TF.Rpc.ExceptionsList.INCORRECT_CONTRACTS_TYPE)) {
                jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.INCORRECT_CONTRACTS_TYPE.message, 'error');
            } else if (errorObj.is(TF.Rpc.ExceptionsList.NO_CONTRACTS_FOR_MULTIEDIT)) {

                jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.NO_CONTRACTS_FOR_MULTIEDIT.message, 'error');
            }
            jQuery('#win-multiedit-contracts').window('close');
        });
}

function checkForExistance() {
    var obj = getAddEditContractFields();
    var filterObj = getFilterContractsTreeParams();

    TF.Rpc.Contracts.ContractsTree.checkForExistance(obj)
        .done(function () {
            TF.Rpc.Contracts.ContractsTree.add(obj)
                .done(function () {
                    jQuery('#win-add-edit-contracts').window('close');
                    initContractsTree(jQuery('.pagination-num').val(), filterObj);
                    jQuery('#contracts-tabs').tabs('select', 0);
                    initContractInfo();
                    initFilesGrid(0);
                    initContractsPlotsGrid(0);
                    initContractsFarmingGrid(0);
                    initPlotContractsGrid(0);
                    copyContractID = undefined;
                })
                .fail(function (errorObj) {
                    if (errorObj.is(TF.Rpc.ExceptionsList.CONTRACT_PLOT_DUPLICATION)) {        
                        jQuery('#win-copy-filtered-contracts-plots-error').window('open');  
                        initUnsuccessfullyCopiedContracts(
                            errorObj.getOriginalMessage()
                        );
                       
                        jQuery('#contracts-tree').tree('loadRpc');
                        return false;
                    }
                    if (errorObj.is(TF.Rpc.ExceptionsList.CONTRACTS_HAS_PAYMENTS_EXCEPTION)) {        
                        jQuery('#win-has-payments-error').window('open');  
                        initUnsuccessfullyEditContract(
                            errorObj.getOriginalMessage()
                        );
                       
                        jQuery('#contracts-tree').tree('loadRpc');
                        return false;
                    }
                    if (errorObj.is(TF.Rpc.ExceptionsList.INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS)) {
                        messagerContractsOwnWriteRights();
                        return false;
                    }
                    if (errorObj.is(TF.Rpc.ExceptionsList.CANNOT_MODIFY_CONTRACT_DATES)) {
                        jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.CANNOT_MODIFY_CONTRACT_DATES.message, 'error');
                        return false;
                    }
                    if (errorObj.is(TF.Rpc.ExceptionsList.SYSTEM_ERROR)) {
                        jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.SYSTEM_ERROR.message, 'error');
                        return false;
                    }
                    if (errorObj.is(TF.Rpc.ExceptionsList.ANNEXES_INVALID_ANNEX_DATE)) {
                        jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.ANNEXES_INVALID_ANNEX_DATE.message, 'error');
                        return false;
                    }
                    if (errorObj.is(TF.Rpc.ExceptionsList.CANNOT_MODIFY_FROM_SUBLEASE_CONTRACT)) {
                        jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.CANNOT_MODIFY_FROM_SUBLEASE_CONTRACT.message, 'error');
                        return false;
                    }
                    if (errorObj.is(TF.Rpc.ExceptionsList.CHANGE_FARMING_SUBLEASE_ERROR)) {
                        jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.CHANGE_FARMING_SUBLEASE_ERROR.message, 'warning');
                        return false;
                    }
                    if (errorObj.is(TF.Rpc.ExceptionsList.CONTRACT_TYPE_MODIFY_DENIED_SALES_RELATION)) {
                        jQuery.messager.alert('Грешка', generateSalesContractExistMsg(errorObj.getOriginalMessage()),'error');
                        return false;
                    }
                });
        })
        .fail(function (errorObj) {
            if (errorObj.is(TF.Rpc.ExceptionsList.EXISTING_CONTRACT_NUMBER)) {
                checkForExistanceResult(true);
            } else {
                RpcErrorHandler.show(errorObj);
            }
        });
}

function checkForExistanceResult(data) {
    if (data) {
        jQuery.messager.confirm('Потвърждение', 'Вече съществува договор с такъв номер! Сигурни ли сте, че искате да продължите?', function (r) {
            if (r) {
                var obj = getAddEditContractFields();
                TF.Rpc.Contracts.ContractsTree.add(obj)
                    .done(function () {
                        jQuery('#win-add-edit-contracts').window('close');
                        jQuery('#contracts-tree').tree('loadRpc');
                        copyContractID = undefined;
                    })
                    .fail(function (errorObj) {
                        if (errorObj.is(TF.Rpc.ExceptionsList.CONTRACT_PLOT_DUPLICATION)) {
                            jQuery('#win-copy-filtered-contracts-plots-error').window('open');  
                            initUnsuccessfullyCopiedContracts(
                                errorObj.getOriginalMessage()
                            );
                            jQuery('#contracts-tree').tree('loadRpc');
                        } else {
                            jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');                           
                        }
                    });
            }
        });
    } else {
        jQuery('#win-add-edit-contracts').window('close');
        jQuery('#contracts-tree').tree('loadRpc');
    }
}

/**
 * 
 * @param {*} data 
 */
function initUnsuccessfullyCopiedContracts(data) {
    var values = Object.keys(data).map(e => data[e])
    jQuery('#unsuccessfully-copied-contracts-data-tables').datagrid({    
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		border: false,
        fit: true,
		singleSelect: true,
		fitColumns: true,
		showFooter: true,
		data: values,
		sortName: 'c_id',
		sortOrder: 'desc',
		idField: 'c_id',
		columns: [[
                {
                    field: 'c_num',
                    title: '<b>Договор</b>',
                    sortable: true,
                    width: 100,
                    formatter: function(value,row,index){   
						if (row.c_id > 0) {
                            return '<a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="openContractInNewTab('+row.c_id+','+ row.is_annex+',\''+ row.gids +'\')">'+value+'</a>';                        
                        }
					}
                },
                {
					field: 'ekatte_name',
					title: '<b>Землище</b>',
					sortable: true,
					width: 100
				},
				{
					field: 'plot_kad_idents',
					title: '<b>Идентификатор</b>',
					sortable: true,
					width: 100,
                    formatter: function(value, row, index) {     
                        try {
                            if (value.indexOf(',') > -1) { 
                                return value.split(',').join('<p></p>');
                            }

                            return value;
                        } catch (e) {
                            return '-';
                        }                     
                    }
				},
                {
					field: 'contract_area',
					title: '<b>Площ по договор</b>',
					width: 100,
                    formatter: function(value, row, index) { 
                        if(value == null || value == undefined || value == '' || value.length == 0){
                            return '-';
                        }   

                        if (typeof value === "string" && value.indexOf(',') > -1) { 
                            return value.split(',').join('<p></p>');
                        }

                        return value;
                    }
				},
			]],
		pagination: true,
		rownumbers: true,
		rowStyler:function(index,row){
			return 'background-color:#ff4c4c;';
			
		},
		onLoadSuccess: function (data) {
	
		},
		onClickRow:function(index){
			jQuery(this).datagrid('unselectRow',index);
		},
		//loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		//loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

/**
 * 
 * @param {*} data 
 */
function initSoldPlotsContractGrid(data) {
    var values = Object.keys(data).map(e => data[e])
    jQuery('#sold-plots-contract-data-tables').datagrid({    
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		border: false,
        fit: true,
		singleSelect: true,
		fitColumns: true,
		showFooter: true,
		data: values,
		sortName: 'c_id',
		sortOrder: 'desc',
		idField: 'c_id',
		columns: [[
                {
                    field: 'sale_contract_c_num',
                    title: '<b>Договор за продажба</b>',
                    sortable: true,
                    width: 100,
                    formatter: function(value,row,index){   
                        return '<a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="openSaleContractInNewTab('+row.sale_contract_id+')">'+value+'</a>';                        
					}
                },
                {
                    field: 'start_date',
                    title: '<b>Дата на влизане в сила</b>',
                    sortable: true,
                    width: 100,
                },
                {
					field: 'contract_area_for_sale',
					title: '<b>Продадена площ (дка)</b>',
					width: 100
				},
			]],
		pagination: true,
		rownumbers: true,
		rowStyler:function(index,row){
			return 'background-color:#ff4c4c;';
		},
		onClickRow:function(index){
			jQuery(this).datagrid('unselectRow',index);
		},
	});
}

function initContractsAvailablePlotAreaErrorModal(contract_data) {
    jQuery('#conficted-contracts-table').datagrid({    
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		border: false,
        fit: true,
		singleSelect: true,
		fitColumns: true,
		showFooter: true,
		data: contract_data,
		sortName: 'c_id',
		sortOrder: 'desc',
		idField: 'c_id',
		columns: [[
                {
                    field: 'c_num',
                    title: '<b>Договор</b>',
                    sortable: true,
                    width: 70,
                    formatter: function(value,row,index){   
						if (row.contract_id > 0) {
                           return '<a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="openContractInNewTab('+row.contract_id+','+ row.is_annex+',\''+ row.plot_id +'\')">'+value+'</a>';                        
                        }
					}
                },
                {
					field: 'ekatte_name',
					title: '<b>Землище</b>',
					sortable: true,
					width: 70
				},
				{
					field: 'kad_ident',
					title: '<b>Идентификатор</b>',
					sortable: true,
					width: 70,
                    formatter: function(value, row, index) {     
                        try {
                            if (value.indexOf(',') > -1) { 
                                return value.split(',').join('<p></p>');
                            }

                            return value;
                        } catch (e) {
                            return '-';
                        }                     
                    }
				},
                {
                    field: 'contract_area',
					title: '<b>Площ по договор</b>',
					sortable: true,
					width: 70,
                }
			]],
		pagination: true,
		rownumbers: true,
		rowStyler:function(index,row){
			return 'background-color:#ff4c4c;';
			
		},
		onLoadSuccess: function (data) {
	
		},
		onClickRow:function(index){
			jQuery(this).datagrid('unselectRow',index);
		},
	});
}

/**
 * 
 * @param {*} data 
 */
function initUnsuccessfullyEditContract(data) {
    var values = Object.keys(data).map(e => data[e])
    jQuery('#unsuccessfully-edit-contracts-data-tables').datagrid({    
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		border: false,
        fit: true,
		singleSelect: true,
		fitColumns: true,
		showFooter: true,
		data: values,
		sortName: 'c_id',
		sortOrder: 'desc',
		idField: 'c_id',
		columns: [[
                {
					field: 'transaction_id',
					title: '<b>№ на транзакцията</b>',
                    align: 'center',
					sortable: true,
					width: 100
				},
                {
					field: 'transaction_type',
					title: '<b>Вид транзакция</b>',
                    align: 'center',
					sortable: true,
					width: 100,
                    formatter: function(value){   
						if (value == 'collection') {
                           return 'Обработки/Вземания';                        
                        } else {
                            return "Плащания";
                        }
					}
				},
                {
					field: 'farming_year',
					title: '<b>Стопанска година</b>',
                    align: 'center',
					sortable: true,
					width: 100,
				},
                {
					field: 'recipient',
					title: '<b>Платено на/от</b>',
                    align: 'center',
					sortable: true,
					width: 100
				},
                {
					field: 'amount',
					title: '<b>Платена сума</b>',
                    align: 'center',
					width: 100,
                    formatter: function(value,row,index){   
						if (row.nat_payments != null) {
                           return '';                        
                        } else {
                            return value;
                        }
					}
				},
                {
					field: 'nat_payments',
					title: '<b>Платена натура</b>',
                    align: 'center',
					width: 100,
				},
			]],
		rowStyler:function(index,row){
			return 'background-color:#ff4c4c;';
			
		},
		onLoadSuccess: function (data) {
	
		},
		onClickRow:function(index){
			jQuery(this).datagrid('unselectRow',index);
		},
	});
}

function getMultiEditContractFields() {
    var obj = {},
        naturesCount = jQuery('.js-multi-renta-type-row').length + 1,
        i,
        renta_type,
        renta_natura,
        renta_natura_value;

    if (jQuery("#multiedit-area-for-rent-checkbox").prop('checked')) {
        obj.setAreaForRentToAllowableArea = true;
    } else {
        obj.setAreaForRentToAllowableArea = false;
    }

    if (jQuery("#multiedit-area-for-rent-to-contract-checkbox").prop('checked')) {
        obj.setAreaForRentToContractArea = true;
    } else {
        obj.setAreaForRentToContractArea = false;
    }

    obj.setAreaForRentToArableArea = jQuery("#multiedit-area-for-rent-to-arable-checkbox").prop('checked');

    if (jQuery("#multiedit-include-annex").prop('checked')) {
        obj.includeAnnexes = true;
    } else {
        obj.includeAnnexes = false;
    }

    obj.renta = jQuery('#multi-renta > input').numberspinner('getValue');
    obj.additionalRentas = [];

    for (i = 0; i < naturesCount; i++) {
        renta_type = jQuery('#multi-renta-type-cb-' + i).combobox('getValue');
        renta_natura = jQuery('#multi-renta-natura-' + i);
        renta_natura_value = renta_natura.numberspinner('getValue');

        if (!renta_natura.numberspinner('isValid')) {
            jQuery.messager.alert('Грешка', 'Моля попълнете всички задължителни полета.', 'error');
            return;
        }
        if (renta_type == 0 || renta_natura_value == '') {
            continue;
        }
        obj.additionalRentas[i] = {
            type: parseInt(renta_type, 10),
            value: renta_natura_value
        };
    }

    return obj;
}

function getAddEditContractFields() {
    var obj = {
        record_id: editContractID,
        copy_id: copyContractID,
        contract_type: jQuery('#contract-type > input').combobox('getValue'),
        contract_number: jQuery('#contract-number > input').val(),
        contract_start_date: jQuery('#contract-start-date > input').datebox('getValue'),
        contract_due_date: jQuery('#contract-due-date > input').datebox('getValue'),
        contract_date: jQuery('#contract-date > input').datebox('getValue'),
        contract_farming: jQuery('#contract-farming > input').combobox('getValue'),
        tom: jQuery('#tom > input').val(),
        delo: jQuery('#delo > input').val(),
        court: jQuery('#court > input').val(),
        na_num: jQuery('#na-num > input').val(),
        sv_num: jQuery('#sv-num > input').val(),
        sv_date: jQuery('#sv-date > input').datebox('getValue'),
        osz_num: jQuery('#osz-num > input').val(),
        osz_date: jQuery('#osz-date > input').datebox('getValue'),
        comment: tinyMCE.get('contract-comment-textarea').getContent()
    };

    if (copyContractID !== undefined) {
        obj.copy_contract_files = jQuery('#copy-contract-files-check').is(':checked');
    } else {
        obj.copy_contract_files = false;
    }
    if (obj.contract_type == 2 || obj.contract_type == 3 || obj.contract_type == 5) {
        obj.renta = jQuery('#renta_price').numberspinner('getValue');
        if (jQuery('#use-overall-renta').is(':checked')) {
            obj.overall_renta = jQuery('#overall_renta_price').numberspinner('getValue');
        }
        obj.pd_day = jQuery('#pd-day > input').numberspinner('getValue');
        obj.pd_month = jQuery('#pd-month > input').combobox('getValue');

        if (obj.contract_type != 5) {
            obj.is_declaration_subleased = jQuery('#contract-is-subleased').is(':checked');
            obj.is_closed_for_editing = jQuery('#is-closed-for-editing').is(':checked');
        }
    }
    if ((obj.contract_type == 2 || obj.contract_type == 3)) {
        obj.contract_group = jQuery('#contract-group > input').combobox('getValue');
    }

    if (obj.contract_type == 4) {
        obj.contract_agg_type = jQuery('#contract-agg-type > input').combobox('getValue');
    }

    obj.additionalRentas = [];

    for (var i = 0; i < jQuery('.js-renta-type-row').length; i++) {
        obj.additionalRentas[i] = {
            type: parseInt(jQuery('#renta-type-cb-' + i).combobox('getValue')),
            value: parseFloat(jQuery('#renta-value-' + i)[0].value)
        }
    }

    return obj;
}

function multiCopyContracts() {
    var contracts = jQuery('#contracts-tree').tree('getRoots'),
        obj = getFilterContractsTreeParams();

    if (contracts.length === 0) {
        jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.NO_CONTRACTS_FOR_MULTICOPY.message, 'error');
        return;
    }

    //get all filtered contracts
    jQuery('#win-copy-filtered-contracts').window('close');

    TF.Rpc.Contracts.ContractsTree.getAllFilteredContracts(obj)
        .done(function (contracts) {

            if (contracts.length === 0) {
                jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.NO_CONTRACTS_FOR_MULTICOPY.message, 'error');
                return false;
            }
            var contractIDs = {
                contract_ids: [],
                copy_contract_files: true
            };
            if (jQuery('#copy-filtered-with-files-checkbox').is(':checked')) {
                contractIDs.copy_contract_files = true;
            } else {
                contractIDs.copy_contract_files = false;
            }
            contracts.forEach(function (contract) {
                contractIDs.contract_ids.push(contract.id);
            });
            TF.Rpc.Contracts.ContractsTree.multiCopyContracts(contractIDs)
                .done(function () {
                    jQuery.messager.alert('Успешно копиране', 'Филтрираните договори са копирани успешно.');
                    jQuery('#btn-clear-filter-contracts').trigger('click');
                })
                .fail(function (errorObj) {
                    if (errorObj.is(TF.Rpc.ExceptionsList.CONTRACT_PLOT_DUPLICATION)) {
                        jQuery('#win-copy-filtered-contracts-plots-error').window('open'); 

                        initUnsuccessfullyCopiedContracts(
                            errorObj.getOriginalMessage()
                        );
                        jQuery('#btn-clear-filter-contracts').trigger('click'); 
                    } else {
                        jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');                           
                    }
                });
        })
        .fail(function (errorObj) {
            if (errorObj.is(TF.Rpc.ExceptionsList.INCORRECT_CONTRACTS_TYPE)) {
                jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.INCORRECT_CONTRACTS_TYPE.message, 'error');
            } else if (errorObj.is(TF.Rpc.ExceptionsList.NO_CONTRACTS_FOR_MULTIEDIT)) {

                jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.NO_CONTRACTS_FOR_MULTIEDIT.message, 'error');
            }
            jQuery('#win-multiedit-contracts').window('close');
            jQuery('#win-copy-filtered-contracts').window('close');
        });
}
