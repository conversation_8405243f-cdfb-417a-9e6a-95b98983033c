function initContractsGroups() {
    jQuery('#contracts-groups-table').datagrid({
        iconCls: 'icon-contract-group',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 50,
        fit: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?contracts-rpc=contracts-groups-datagrid',
        rpcMethod: 'read',
        method: 'post',
        border: false,
        idField: 'id',
        singleSelect: true,
        frozenColumns: [
            [
                {
                    field: 'ck',
                    checkbox: true
                }
            ]
        ],
        columns: [
            [
                {
                    field: 'name',
                    title: '<b>Име</b>',
                    sortable: true,
                    width: 150
                }
            ]
        ],
        pagination: true,
        rownumbers: true,
        toolbar: [
            {
                id: 'btnAddContractGroup',
                text: 'Добавяне',
                iconCls: 'icon-add',
                handler: function () {
                    openWinCreateContractsGroup();
                }
            }, {
                id: 'btnEditContractGroup',
                text: 'Редактиране',
                iconCls: 'icon-edit',
                handler: function () {
                    var [getChecked] = jQuery('#contracts-groups-table').datagrid('getChecked');

                    if (!getChecked?.name?.length) {
                        jQuery.messager.alert('Грешка', 'Моля изберете група за редактиране.');
                        return;
                    }

                    openWinEditContractsGroup(getChecked);

                }
            },
            {
                id: 'btnDeleteContractGroup',
                text: 'Изтриване',
                iconCls: 'icon-delete',
                handler: function () {
                    var [getChecked] = jQuery('#contracts-groups-table').datagrid('getChecked');

                    if (!getChecked?.name?.length || !getChecked.id) {
                        jQuery.messager.alert('Грешка', 'Моля изберете група за редактиране.');
                        return;
                    }


                    jQuery.messager.confirm({
                        title: 'Изтриване на група',
                        msg: `Сигурни ли сте, че искате да изтриете група '${getChecked.name}' ?`,
                        ok: 'Изтрий',
                        cancel: 'Откажи',
                        fn: function (r) {
                            if (r) {
                                deleteContractsGroup(getChecked);
                            }
                        }
                    });
                }
            }

        ],
        onBeforeLoad: function (param) {
            jQuery('#contracts-groups-table').datagrid('clearChecked');
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}


function createContractsGroup() {
    if (jQuery('#win-create-contracts-group #contracts-group-name > input').val() == '') {
        jQuery.messager.alert('Грешка', 'Не сте въвели име на група.');

        return false;
    } else {
        const obj = {
            name: jQuery('#win-create-contracts-group #contracts-group-name > input').val()
        };

        TF.Rpc.Contracts.ContractsGroupsGrid.createContractsGroup(obj)
            .done(function (response) {
                closeWinCreateContractsGroup();
                jQuery('#contracts-groups-table').datagrid('reload');

                // Add the new group to the combobox so it can be selected when create/edit contracts
                if (response?.group?.id && response?.group?.name) {
                    ComboboxData.ContractGroupsCombobox.push(response.group);
                }
            })
            .fail(function (data) {
                if (data.is(TF.Rpc.ExceptionsList.CONTRACT_GROUP_ALREADY_EXISTS)) {
                    jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.CONTRACT_GROUP_ALREADY_EXISTS.message, 'warning');
                    return;
                }

                jQuery.messager.alert('Грешка', 'Грешка при създаване на група.', 'error');
                closeWinCreateContractsGroup();
            });
    }
}


function updateContractsGroup() {
    if (jQuery('#win-edit-contracts-group #contracts-group-name > input').val() == '') {
        jQuery.messager.alert('Грешка', 'Не сте въвели име на група.');

        return false;
    } else {
        const obj = {
            id: jQuery('#win-edit-contracts-group #contracts-group-name > input').data('group-id'),
            newName: jQuery('#win-edit-contracts-group #contracts-group-name > input').val()
        };
        TF.Rpc.Contracts.ContractsGroupsGrid.updateContractsGroup(obj)
            .done(function () {
                closeWinEditContractsGroup();
                jQuery('#contracts-groups-table').datagrid('reload');

                // Update the combobox data
                ComboboxData.ContractGroupsCombobox = ComboboxData.ContractGroupsCombobox.map(
                    item => item.id === obj.id ? { id: obj.id, name: obj.newName } : item
                );

                // Reload the contracts tree so it updates the group name in the selected contract's info
                jQuery('#contracts-tree')?.tree('loadRpc');
            })
            .fail(function (data) {
                if (data.is(TF.Rpc.ExceptionsList.CONTRACT_GROUP_ALREADY_EXISTS)) {
                    jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.CONTRACT_GROUP_ALREADY_EXISTS.message, 'warning');
                    return;
                }

                jQuery.messager.alert('Грешка', 'Грешка при редактиране на група.', 'error');
                closeWinEditContractsGroup();
            });
    }
}

function deleteContractsGroup(group) {
    if (!group?.id) {
        return;
    }

    const obj = {
        id: group.id
    };

    TF.Rpc.Contracts.ContractsGroupsGrid.deleteContractsGroup(obj)
        .done(function () {
            jQuery('#contracts-groups-table').datagrid('reload');

            // Remove the group from the combobox data
            ComboboxData.ContractGroupsCombobox = ComboboxData.ContractGroupsCombobox.filter(
                item => item.id !== group.id
            );
        })
        .fail(function (data) {
            if (data.is(TF.Rpc.ExceptionsList.CONTRACT_GROUP_HAS_CONTRACTS)) {
                jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.CONTRACT_GROUP_HAS_CONTRACTS.message, 'warning');
                return;
            }

            jQuery.messager.alert('Грешка', 'Грешка при изтриване на група.', 'error');
        });
}

function openWinCreateContractsGroup() {
    jQuery('#win-create-contracts-group #contracts-group-name > input').val('');
    jQuery('#win-create-contracts-group #btn-save-contracts-group').off('click').on('click', createContractsGroup);
    jQuery('#win-create-contracts-group #btn-cancel-contracts-group').off('click').on('click', closeWinCreateContractsGroup);
    jQuery('#win-create-contracts-group').window('open');

}

function closeWinCreateContractsGroup() {
    jQuery('#win-create-contracts-group #contracts-group-name > input').val('');
    jQuery('#win-create-contracts-group').window('close');
}


function openWinEditContractsGroup(group) {
    if (!group?.name?.length || !group.id) {
        return;
    }

    jQuery('#win-edit-contracts-group #contracts-group-name > input').val(group.name);
    jQuery('#win-edit-contracts-group #contracts-group-name > input').data('group-id', group.id);
    jQuery('#win-edit-contracts-group #btn-save-contracts-group').off('click').on('click', updateContractsGroup);
    jQuery('#win-edit-contracts-group #btn-cancel-contracts-group').off('click').on('click', closeWinEditContractsGroup);
    jQuery('#win-edit-contracts-group').window('open');
}

function closeWinEditContractsGroup() {
    jQuery('#win-edit-contracts-group #contracts-group-name > input').val('');
    jQuery('#win-edit-contracts-group #contracts-group-name > input').data('group-id', '');
    jQuery('#win-edit-contracts-group').window('close');
}