

function editOwnerRepresentative() {
	var getSelected = jQuery('#contract-owners-tables').datagrid('getSelected');

	if (!getSelected) {
		jQuery.messager.alert('Грешка', 'Моля изберете собственик.', 'warning');
		return false;
	}
	var rep_id = getSelected.rep_id;
	var self_rep = getSelected.self_rep;

	jQuery('#win-edit-owner-representative').window('open');

	if(self_rep) {
		jQuery('#self-representing').prop('checked', true);
	} else {
		jQuery('#reprsented-by-representative').prop('checked', true);
	}

	initOwnerRepresentativesGrid(rep_id);
	initAddEditRepresentativeFields();
}

function initOwnerRepresentativesGrid(rep_id) {
	jQuery("#choose-representatives-grid").datagrid({
		title: 'Представители',
		iconCls: 'icon-users',
		rownumbers: true,
		singleSelect: true,
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		fit: true,
		fitColumns: true,
		showFooter: false,
		pagination: true,
		idField: 'id',
		sortName: 'id',
		sortOrder: 'desc',
		url: 'index.php?owners-rpc=representatives-tree',
		rpcParams:[{
			anti_rep_id: rep_id,
			forGrid: true
		}],
		onBeforeLoad: function () {
			jQuery("#choose-representatives-grid").datagrid('clearSelections');
		},
		columns: [[
			{
				field: 'rep_owner_names',
				title: '<b>Име</b>',
				sortable: false,
				width: 300,
			}, {
				field: 'egn',
				title: '<b>ЕГН</b>',
				sortable: false,
				width: 120,
			}, {
				field: 'address',
				title: '<b>Адрес</b>',
				sortable: false,
				width: 200,
			},
		]],
		toolbar: [{
			id: 'btnadd',
			text: 'Информация за представител',
			iconCls: 'icon-info',
			handler: function() {
				var getSelected = jQuery("#choose-representatives-grid").datagrid('getSelected');
				window.open("index.php?page=Owners.Home&rep_id=" + getSelected.id, '_blank');
			}
		}],
		onSelect: function () {
			jQuery('#reprsented-by-representative').prop('checked', true);
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function refreshRepsGrid(data) {
	var repsGrid = jQuery("#choose-representatives-grid");
	if(repsGrid.data().hasOwnProperty('datagrid')){
		repsGrid.datagrid('insertRow',{
			index: 0,
			row: {
				id: data.id,
				rep_owner_names: data.rep_name + ' ' + data.rep_surname + ' ' + data.rep_lastname,
				egn: data.egn,
				address: data.address
			}
		});
	}
}

jQuery(function () {

	jQuery('#btn-filter-representatives').bind('click', function () {
		var obj = {
			anti_rep_id: jQuery('#contract-owners-tables').datagrid('getSelected').rep_id,
			forGrid: true,
			rep_names: jQuery('#search-rep-by-name').val(),
            rep_egn: jQuery('#search-rep-by-egn').val()
        };
        jQuery("#choose-representatives-grid").datagrid('options').rpcParams = [obj];
        jQuery("#choose-representatives-grid").datagrid('options').pageNumber = 1;
        jQuery("#choose-representatives-grid").datagrid('reload');
	});

	jQuery('#btn-clear-filter-representatives').bind('click', function () {
		jQuery('#search-rep-by-name').val('');
		jQuery('#search-rep-by-egn').val('');
		jQuery('#btn-filter-representatives').trigger('click');
	});

	jQuery('#btn-save-rep-changes').bind('click', function () {
		var rel_id = jQuery('#contract-owners-tables').datagrid('getSelected').id;
		var owner_id= jQuery('#contract-owners-tables').datagrid('getSelected').owner_id;
		var contract_id = jQuery('#contracts-tree').tree('getSelected').id;
		var new_rep = null;

		if(!jQuery('#self-representing').is(':checked')) {
			new_rep = jQuery("#choose-representatives-grid").datagrid('getSelected').id;
		}

		TF.Rpc.Owners.OwnersRepresentedGrid.changeOwnerRepresentative({
			rel_id: rel_id,
			rep_id: new_rep,
			owner_id: owner_id,
			contract_id: contract_id
		})
		.done(function () {
			jQuery('#win-edit-owner-representative').window('close');
			jQuery('#contract-owners-tables').datagrid('loadRpc');
		})
		.fail(function (errorObj) {
			jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
		});
	});
});
