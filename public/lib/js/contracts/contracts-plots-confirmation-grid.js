function initContractsPlotsConfirmationGrid() {

	jQuery('#confirmation-contracts-data-tables').datagrid({
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		border: false,
		pageSize: 10,
		fit: true,
		singleSelect: true,
		fitColumns: false,
		showFooter: true,
		url: 'index.php?contracts-rpc=contracts-plots-confirmation-datagrid',
		sortName: 'gid',
		rpcParams: [{}],
		sortOrder: 'desc',
		idField: 'gid',
		columns: [[
				{
					field: 'kad_ident',
					title: '<b>Идентификатор</b>',
					sortable: true,
					width: 100
				}, {
					field: 'c_num',
					title: '<b>Договор №</b>',
					sortable: true,
					width: 90,
					align: 'center',
					formatter: function (txt, data) {
						if (data.contract_id) {
							return '<a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="showContractInNewTab('+data.contract_id+')">'+txt+'</a>';
						} else if(data.sale_contract_id) {
							return '<a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="showSalesContract('+data.sale_contract_id+')">'+txt+'</a>';
						} else {
							return txt ? txt + '(Текущ)' : '';
						}
					}
				}, {
					field: 'farming',
					title: '<b>Стопанство</b>',
					sortable: true,
					width: 130
				}, {
					field: 'start_date',
					title: '<b>Влизане в сила</b>',
					sortable: true,
					align: 'center',
					width: 100,
					formatter: function (row) {
						if(row < 0) {
							return '<span style="color: red" title="Сумарната площ по договори на този имот надхвърля площта му по документи.">'+row+'</span>';
						} else {
							return row;
						}
					}
				}, {
					field: 'due_date',
					title: '<b>Крайна дата</b>',
					sortable: true,
				align: 'center',
					width: 80
				}, {
					field: 'contract_area',
					title: '<b>Площ по<br/>договор (дка)</b>',
					align: 'center',
					sortable: true,
					width: 90
				}, {
					field: 'document_area',
					title: '<b>Площ по<br/>док. (дка)</b>',
					align: 'center',
					sortable: true,
					width: 80
				}
			]],
		pagination: true,
		rownumbers: true,
		toolbar: [
			{
                id: 'btnviewplotinfo',
                text: 'Информация',
                iconCls: 'icon-info',
                handler: function() {
                    var getChecked = jQuery('#confirmation-contracts-data-tables').datagrid('getChecked');
                    if (getChecked[0]) {
						window.open("index.php?page=Contracts.Home&contract_id=" + getChecked[0].contract_id, '_blank');
                    } else {
                        jQuery.messager.alert('Грешка', 'Моля изберете договор, за който да бъде показана информация.');
                    }
                }
            }
		],
		rowStyler:function(index,row){
			if (row.newContract) {
				return 'background-color:#56c489;';
			}
		},
		onLoadSuccess: function (data) {
		},
		onClickRow:function(index){
			jQuery(this).datagrid('unselectRow',index);
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function initSubleasesPlotsConfirmationGrid() {
	jQuery('#confirmation-subleases-data-tables').datagrid({
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		border: false,
		pageSize: 10,
		fit: true,
		singleSelect: true,
		fitColumns: false,
		showFooter: true,
		url: 'index.php?contracts-rpc=subleases-plots-confirmation-datagrid',
		sortName: 'gid',
		rpcParams: [{}],
		sortOrder: 'asc',
		idField: 'gid',
		frozenColumns: [[{
					field: 'ck',
					checkbox: true
				}]],
		columns: [[
				{
					field: 'kad_ident',
					title: '<b>Идентификатор</b>',
					sortable: true,
					width: 100
				}, {
					field: 'c_num',
					title: '<b>Договор №</b>',
					sortable: true,
					width: 90
				}, {
					field: 'farming',
					title: '<b>Стопанство</b>',
					sortable: true,
					width: 130
				}, {
					field: 'start_date',
					title: '<b>Влизане в сила</b>',
					align: 'center',
					sortable: true,
					width: 100,
					formatter: function (row) {
						if(row < 0) {
							return '<span style="color: red" title="Сумарната площ по договори на този имот надхвърля площта му по документи.">'+row+'</span>';
						} else {
							return row;
						}
					}
				}, {
					field: 'due_date',
					title: '<b>Крайна дата</b>',
				align: 'center',
					sortable: true,
					width: 80
				}, {
					field: 'contract_area',
					title: '<b>Площ по<br/>договор (дка)</b>',
					align: 'center',
					sortable: true,
					width: 90
				}, {
					field: 'document_area',
					title: '<b>Площ по<br/>док. (дка)</b>',
					align: 'center',
					sortable: true,
					width: 80
				}
			]],
		pagination: true,
		rownumbers: true,
		toolbar: [
			{
                id: 'btnviewplotinfo',
                text: 'Информация',
                iconCls: 'icon-info',
                handler: function() {
                    var getChecked = jQuery('#confirmation-subleases-data-tables').datagrid('getChecked');
                    if (getChecked[0]) {
						window.open("index.php?page=Subleases.Home&sublease_id=" + getChecked[0].contract_id, '_blank');
                    } else {
                        jQuery.messager.alert('Грешка', 'Моля изберете договор, за който да бъде показана информация.');
                    }
                }
            }
		],
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}
