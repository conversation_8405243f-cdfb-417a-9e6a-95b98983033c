/*jslint browser: true*/
/*global jQuery, EasyUIRPCLoaders.EasyUIGridCustomLoader, TF,bufferview, Fraction, RpcErrorHandler, hasPlotRightsRW, messagerPlotsWriteRights, CONTRACT_TYPE_OWN, hasContractsOwnWriteRights, messagerContractsOwnWriteRights */

var maxOwnership = new Fraction(1),
    isHeritorSelected = false,
    selectedContractID = null,
    addingNewHeritor = false,
    isEditing = null;
    PostPaymentOwner = new PostPaymentFields('owner');

function initContractsOwnersGrid(plot_id, contract_id, c_type) {
    var obj = { id: 0},
        title;

    selectedPlotID = plot_id;
    selectedContractID = jQuery('#contracts-tree').tree('getSelected') || obj;

    if(selectedContractID.id === 0) {
        return;
    }

    if (c_type == 1) {
        title = 'Предишни собственици';
    } else {
        title = 'Собственици';
    }

    var contractsOwnersTreegrid = jQuery('#contract-owners-tables'),
        isDatagridBound = contractsOwnersTreegrid.data().hasOwnProperty('treegrid');

    if (isDatagridBound) {
        if (selectedPlotID !== 0 && selectedContractID.id != 0) {
            contractsOwnersTreegrid.treegrid({
                url: 'index.php?contracts-rpc=contracts-owners-datagrid',
                rpcParams: [{
                    plot_id: plot_id,
                    contract_id: selectedContractID.id
                }],
            });
        } else {
            contractsOwnersTreegrid.treegrid('loadData', {"rows": [], "total": 0});
        }
        return;
    }

    contractsOwnersTreegrid.treegrid({
        iconCls: 'icon-owners',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 10,
        fit: true,
        singleSelect: true,
        url: 'index.php?contracts-rpc=contracts-owners-datagrid',
        rpcParams: [{
            plot_id: plot_id,
            contract_id: selectedContractID.id
        }],
        fitColumns: true,
        showFooter: false,
        sortName: 'name',
        sortOrder: 'desc',
        idField: 'fakeid',
        treeField: 'owner_names',
        border: true,
        rowStyler:function(row){
            if (row.is_signer) {
                return 'background-color:#56c489;';
            }
        },
        columns: [[
            {
                field: 'owner_names',
                title: '<b>Собственик</b>',
                sortable: false,
                width: 150,
                styler: function (value, row) {
                    if (!row.is_heritor) {
                        return 'font-weight: bold;';
                    }
                }
            }, {
                field: 'rep_names',
                title: '<b>Представител</b>',
                sortable: false,
                width: 100
            }, {
                field: 'rat_ownage',
                title: '<b>Собственост</b>',
                sortable: false,
                width: 125,
                formatter: function (value, row) {
                    var percent,
                        fraction,
                        dka;

                    dka = parseFloat(row.contract_area) * (parseFloat(row.percent) / 100);

                    if (row.numerator && row.denominator) {
                        percent = new Fraction(row.numerator / row.denominator * 100);
                        fraction = row.numerator + '/' + row.denominator;
                        
                        value = parseFloat(row.percent).toFixed(1) + '% (' +  fraction + ')' + ' ' + dka.toFixed(3) + ' дка';

                        return value;
                    }

                    fraction = new Fraction(row.percent / 100);
                    percent = fraction.mul(100);
                    value = parseFloat(row.percent).toFixed(1) + '% (' +  fraction.simplify().toFraction() + ')' + ' ' + dka.toFixed(3) + ' дка';

                    return value;
                }
            }
        ]],
        pagination: false,
        rownumbers: true,
        toolbar: '#contracts-owners-toolbar',
        onBeforeLoad: function () {
            jQuery('#contract-owners-tables').treegrid('clearChecked');
        },
        onLoadSuccess: function (row, data) {
            var tabsElement = jQuery('#owners-tabs');
            tabsElement.tabs();

            if(
                selectedContractID.hasOwnProperty('attributes')
                && selectedContractID.attributes.from_sublease !== null
            ) {
                tabsElement.tabs('enableTab', 1);
            } else {
                tabsElement.tabs('disableTab', 1);
            }

            let contractFarmingsTable = jQuery('#contract-farming-tables');
            let isFarmingsBound = contractFarmingsTable.data().hasOwnProperty('datagrid');

            if (data && data.rows.length === 0) {
                if (isFarmingsBound && contractFarmingsTable.datagrid('getData').rows.length !== 0) {
                    tabsElement.tabs('select', 1);
                } else {
                    tabsElement.tabs('select', 0);
                }
            } else {
                tabsElement.tabs('select', 0);
                var contractsTreeFilterObj = jQuery('#contracts-tree').data().tree.options.rpcParams[0];
                var selected = getSelectedOwnerByFilter(data.rows, contractsTreeFilterObj, false);
                if(selected) contractsOwnersTreegrid.treegrid('select', selected.fakeid);
            }


            var sumFr = getTotalOwnersShares();
            if (sumFr.valueOf() < 1) {
                jQuery('#ownership-warning').show();
            } else {
                jQuery('#ownership-warning').hide();
            }

            if (checkForSigners(data.rows)) {
                jQuery('#signer-legend').show();
            } else {
                jQuery('#signer-legend').hide();
            }
        },
        onSelect: function (node) {
            if (node.is_heritor) {
                jQuery('#btnrelatecontractowner').linkbutton('disable');
                jQuery('#btndeletecontractowner').linkbutton('disable');
                jQuery('#btncopyowners').linkbutton('disable');
                isHeritorSelected = true;
            } else {
                jQuery('#btnrelatecontractowner').linkbutton('enable');
                jQuery('#btndeletecontractowner').linkbutton('enable');
                jQuery('#btncopyowners').linkbutton('enable');
                isHeritorSelected = false;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function checkForSigners(owners) {
    var isSigner = false;
    for (var owner of owners) {
        if (owner.is_signer) return true;
        if (owner.children && owner.children.length > 0) isSigner = checkForSigners(owner.children);
        if(isSigner) return isSigner;
    }
    return isSigner;
}

function getSelectedOwnerByFilter(data, filter, selected = false) {
    if(!filter) return false;
    if(selected) return selected;

    for (var i = 0; i < data.length; i++) {
        if (
            (!!filter.person_name && (!!data[i].owner_names && data[i].owner_names.toLowerCase().includes(filter.person_name.toLowerCase()) || !!data[i].rep_names && data[i].rep_names.toLowerCase().includes(filter.person_name.toLowerCase()))) ||
            (!!filter.person_egn && (!!data[i].egn_eik && data[i].egn_eik.includes(filter.person_egn) ||
                !!data[i].rep_egn && data[i].rep_egn.includes(filter.person_egn))) ||
            (!!data[i].egn_eik && !!filter.owner_egn && data[i].level === 1 && data[i].egn_eik.includes(filter.owner_egn)) ||
            (!!filter.owner_name && !!data[i].owner_names && data[i].level === 1 && data[i].owner_names.toLowerCase().includes(filter.owner_name.toLowerCase())) ||
            (!!data[i].egn_eik && !!filter.owner_egn && data[i].level === 1 && data[i].egn_eik.includes(filter.owner_egn)) ||
            (!!filter.heritor_name && !!data[i].owner_names && data[i].level > 1 && data[i].owner_names.toLowerCase().includes(filter.heritor_name.toLowerCase())) ||
            (!!data[i].egn_eik && !!filter.heritor_egn && data[i].level > 1 && data[i].egn_eik.includes(filter.heritor_egn)) ||
            (!!filter.rep_name && !!data[i].rep_names && data[i].rep_names.toLowerCase().includes(filter.rep_name.toLowerCase())) ||
            (!!filter.rep_egn && !!data[i].rep_egn && data[i].rep_egn.includes(filter.rep_egn)) ||
            (!!filter.company_name && !!data[i].company_name && data[i].company_name.toLowerCase().includes(filter.company_name.toLowerCase())) ||
            (!!filter.company_eik && !!data[i].eik && data[i].eik.includes(filter.company_eik))
        ) {
            selected = data[i];
            selected.index = i + 1;
        }
        if (data[i].children) {
            selected = getSelectedOwnerByFilter(data[i]['children'], filter, selected);
        }
    }

    return selected;
}

function initAddOwnersGrid(contract_id, plot_id) {
    jQuery('#owners-add-tables').datagrid({
        iconCls: 'icon-users',
        nowrap: true,
        title: 'Собственици',
        autoRowHeight: true,
        striped: true,
        pageSize: 10,
        fit: true,
        singleSelect: true,
        fitColumns: true,
        showFooter: true,
        url: 'index.php?contracts-rpc=contracts-owners-datagrid',
        rpcParams: [{
            plot_id: plot_id,
            contract_id: contract_id
        }],
        rpcMethod: 'add',
        sortName: 'id',
        sortOrder: 'desc',
        idField: 'id',
        columns: [[
            {
                field: 'owner_names',
                title: '<b>Име на собственик</b>',
                sortable: true,
                width: 150
            }, {
                field: 'egn_eik',
                title: '<b>ЕГН/EIK</b>',
                sortable: true,
                width: 150
            }
        ]],
        pagination: true,
        rownumbers: true,
        toolbar: [{
            id: 'btnaddnewcontractowner',
            text: 'Нов собственик',
            iconCls: 'icon-add',
            handler: function () {
                isEditing = false;
                lastAddedOwner = 0;
                lastAddedParent = 0;
                clearAddOwnerInputDataFields();
                onAddownerPanelOpen();
                displayTables('physical');
                jQuery('#win-add-new-owner').window('open');
            }
        }, {
            id: 'btnaddfilter',
            text: 'Филтриране',
            iconCls: 'icon-filter',
            handler: function () {
                gridTableID = '#owners-add-tables';
                jQuery('#win-add-owner-filter').window('open');
            }
        }, {
            id: 'btnremovefilter',
            text: 'Покажи всички',
            iconCls: 'icon-clear-filter',
            handler: function () {
                cleanAddOwnersFilter('#owners-add-tables');
            }
        }, {
            id: 'btninfoowner',
            text: 'Информация',
            iconCls: 'icon-info',
            handler: function () {
                var getChecked = jQuery('#owners-add-tables').datagrid('getChecked');
                if (getChecked[0]) {
                    //displayOwnerInfo(getChecked[0]['id']);

                    window.open("index.php?page=Owners.Home&owner_id=" + getChecked[0].id, '_blank');
                } else {
                    jQuery.messager.alert('Грешка', 'Моля изберете потребител, за който да бъде показана информация.');
                }
            }
        }],
        onBeforeLoad: function () {
            jQuery('#owners-add-tables').datagrid('clearChecked');
        },
        onLoadSuccess: function () {
            initContractOwnerDataFields();
        },
        onSelect: function (rowIndex, rowData) {
            reinitContractOwnerDataFields(rowData);

            if (rowData.is_dead) {
                jQuery.messager.alert('Предупреждение', 'Тъй като избраният собственик е обявен като починал, ще се вземат предвид въведените му наследници.');
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initContractOwnerDataFields() {
    var date = new Date(),
        todayDate = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();

    jQuery('#ownage-numerator > input').numberbox({
        value: '',
        min: 0,
        decimalSeparator: ''
    });
    jQuery('#ownage-denominator > input').numberbox({
        value: '',
        min: 1,
        decimalSeparator: ''
    });
    jQuery('#contract-ownage-percent-field > input').numberbox({
        value: '',
        min: 0,
        max: 100,
        precision: 2
    });
    jQuery('#contract-ownage-area-field > input').numberbox({
        value: '',
        min: 0,
        precision: 3
    });

    jQuery('#contract-rep-proxy-num > input').val('');

    jQuery('#contract-rep-proxy-date > input').datebox({
        value: todayDate
    });

    jQuery('#contract-ownage-fraction > input').change(function () {
        jQuery('#contract-ownage-percent-field').hide();
        jQuery('#contract-ownage-area-field').hide();
        jQuery('#contract-ownage-fraction-fields').show();
    });
    jQuery('#contract-ownage-percent > input').change(function () {
        jQuery('#contract-ownage-area-field').hide();
        jQuery('#contract-ownage-fraction-fields').hide();
        jQuery('#contract-ownage-percent-field').show();
    });
    jQuery('#contract-ownage-area > input').change(function () {
        jQuery('#contract-ownage-fraction-fields').hide();
        jQuery('#contract-ownage-percent-field').hide();
        jQuery('#contract-ownage-area-field').show();
    });

    jQuery('#contract-owner-rep-by-another > input').change(function () {
        jQuery('#contract-rep-proxy-message').hide();
        jQuery('#contract-rep-proxy-fields').show();
    });

    jQuery('#contract-owner-rep-by-himself > input').change(function () {
        jQuery('#contract-rep-proxy-fields').hide();
        jQuery('#contract-rep-proxy-message').show();
    });

    jQuery('#contract-owner-rep-by-himself > input').prop('checked', true);
    //display proxy fields
    jQuery('#contract-rep-proxy-message').show();
    jQuery('#contract-rep-proxy-fields').hide();

    if (contragent_type == 'farming') {
        jQuery('#contract-owner-rep-by-himself > input').attr('disabled', true);
    } else {
        jQuery('#contract-owner-rep-by-himself > input').removeAttr('disabled');
    }

    jQuery('#contract-rep-notary-num > input').numberbox({
        filter: function (e) {
            var isNumber = e.which > 47 && e.which < 58;
            return isNumber;
        }
    });

    jQuery('#is-foreigner > input').on('change', function () {
        if(jQuery('#is-foreigner > input').is(':checked') || jQuery('#is-dead > input').is(':checked')) {
           egnBox.disableValidation();
        } else {
            egnBox.enableValidation();
        }
    });

    jQuery('#is-dead > input').on('change', function () {
        if(jQuery('#is-foreigner > input').is(':checked') || jQuery('#is-dead > input').is(':checked')) {
            egnBox.disableValidation();
        } else {
            egnBox.enableValidation();
        }

        if(jQuery('#is-dead > input').is(':checked')) {
            jQuery('#prepiska-row').show();
            jQuery('#dead-date-row').show();
        } else {
            jQuery('#prepiska-row').hide();
            jQuery('#dead-date-row').hide();
        }

    });
}

//used when new owner is selected
function reinitContractOwnerDataFields(ownerData) {
    var owner_id = ownerData.id;

    jQuery('#contract-ownage-fraction > input').change(function () {
        jQuery('#contract-ownage-percent-field').hide();
        jQuery('#contract-ownage-area-field').hide();
        jQuery('#contract-ownage-fraction-fields').show();
    });
    jQuery('#contract-ownage-percent > input').change(function () {
        jQuery('#contract-ownage-area-field').hide();
        jQuery('#contract-ownage-fraction-fields').hide();
        jQuery('#contract-ownage-percent-field').show();
    });
    jQuery('#contract-ownage-area > input').change(function () {
        jQuery('#contract-ownage-fraction-fields').hide();
        jQuery('#contract-ownage-percent-field').hide();
        jQuery('#contract-ownage-area-field').show();
    });

    jQuery('#contract-owner-rep-by-another > input').change(function () {
        jQuery('#contract-rep-proxy-message').hide();
        jQuery('#contract-rep-proxy-fields').show();
    });

    jQuery('#contract-owner-rep-by-himself > input').unbind();
    jQuery('#contract-owner-rep-by-himself > input').change(function () {
        //if owner is company then should not be allowed to represent himself
        if (jQuery('#contract-owner-rep-by-himself > input').is(':checked')) {
            if (ownerData.owner_type == 0) {
                jQuery('#contract-owner-rep-by-another > input').prop('checked', true);
                jQuery('#contract-rep-proxy-message').hide();
                jQuery('#contract-rep-proxy-fields').show();

                jQuery.messager.alert('Грешка', 'Само физически лица могат да се представляват лично.');
            } else {
                jQuery('#contract-rep-proxy-fields').hide();
                jQuery('#contract-rep-proxy-message').show();
            }
        }
    });

    //if owner is company check represented by another
    if (ownerData.owner_type == 0) {
        jQuery('#contract-owner-rep-by-another > input').prop('checked', true);
        jQuery('#contract-rep-proxy-message').hide();
        jQuery('#contract-rep-proxy-fields').show();
	} else if(ownerData.owner_type == 1) {
        jQuery('#contract-owner-rep-by-himself > input').prop('checked', true);
        jQuery('#contract-rep-proxy-message').show();
        jQuery('#contract-rep-proxy-fields').hide();
    }
}

function initEditOwnageDataFields() {
    jQuery('#edit-ownage-numerator > input').numberbox({
        value: '',
        min: 0,
        decimalSeparator: ''
    });
    jQuery('#edit-ownage-denominator > input').numberbox({
        value: '',
        min: 1,
        decimalSeparator: ''
    });
    jQuery('#edit-ownage-percent-field > input').numberbox({
        value: '',
        min: 0,
        max: 100,
        precision: 2
    });
    jQuery('#edit-ownage-area-field > input').numberbox({
        value: '',
        min: 0,
        precision: 3
    });
    jQuery('#edit-ownage-fraction > input').change(function () {
        jQuery('#edit-ownage-percent-field').hide();
        jQuery('#edit-ownage-area-field').hide();
        jQuery('#edit-ownage-fraction-fields').show();
    });
    jQuery('#edit-ownage-percent > input').change(function () {
        jQuery('#edit-ownage-area-field').hide();
        jQuery('#edit-ownage-fraction-fields').hide();
        jQuery('#edit-ownage-percent-field').show();
    });
    jQuery('#edit-ownage-area > input').change(function () {
        jQuery('#edit-ownage-fraction-fields').hide();
        jQuery('#edit-ownage-percent-field').hide();
        jQuery('#edit-ownage-area-field').show();
    });
}

//filter for add owners grid
function filterAddOwner(tableID) {
    if (jQuery('#search-owner > input').val() != ''
            || jQuery('#search-egn > input').val() != ''
            || jQuery('#search-eik > input').val() != ''
            || jQuery('#search-company > input').val() != '') {
        jQuery(tableID).datagrid({
            rpcParams: [{
                plot_id: selectedPlotID,
                contract_id: selectedContractID.id,
                owner_names: jQuery('#search-owner > input').val(),
                egn: jQuery('#search-egn > input').val(),
                eik: jQuery('#search-eik > input').val(),
                company_name: jQuery('#search-company > input').val()
            }]
        });
        jQuery('#win-add-owner-filter').window('close');
    } else {
        jQuery.messager.alert('Грешка', 'Моля задайте данни за търсене.');
    }
}

function cleanAddOwnersFilter(tableID) {
    jQuery('#search-owner > input').val('');
    jQuery('#search-egn > input').val('');
    jQuery('#search-eik > input').val('');
    jQuery('#search-company > input').val('');

    jQuery(tableID).datagrid({
        rpcParams: [{
            plot_id: selectedPlotID,
            contract_id: selectedContractID.id
        }]
    });
}
//end of filter for add owners grid

function displayOwnerInfo(owner_id) {
    jQuery.ajax({
        url: 'index.php?rpc=owner-info',
        type: 'post',
        rpcParams: {
            id: owner_id
        },
        complete: function(response) {
            var responseText = jQuery.parseJSON(response.responseText);
            if (responseText.owner_type_id == 0) {
                jQuery('#info-panel').window('resize', {
                    width: 420,
                    height: 290
                });
                var html = '<fieldset style="border: 1px solid #000; padding: 5px 10px;">' +
                        '<legend style="font-style: italic; font-weight: bold">Информация за собственик</legend>' +
                        'Вид собственик: ' + responseText.owner_type + '<br/>' +
                        'Фирма: ' + responseText.company_name + '<br/>' +
                        'ЕИК: ' + responseText.eik + '<br/>' +
                        'МОЛ: ' + responseText.mol + '<br/>' +
                        'Адрес: ' + responseText.company_address + '<br/>' +
                        '</fieldset>';
            } else {
                jQuery('#info-panel').window('resize', {
                    width: 420,
                    height: 310
                });
                var html = '<fieldset style="border: 1px solid #000; padding: 5px 10px;">' +
                        '<legend style="font-style: italic; font-weight: bold">Информация за собственик</legend>' +
                        'Вид собственик: ' + responseText.owner_type + '<br/>' +
                        'Имена: ' + responseText.name + ' ' + responseText.surname + ' ' + responseText.lastname + '<br/>' +
                        'ЕГН: ' + responseText.egn + '<br/>' +
                        'Номер на лична карта: ' + responseText.lk_nomer + '<br/>' +
                        'Издаване на ЛК: ' + responseText.lk_izdavane + '<br/>' +
                        'Починал: ' + responseText.is_dead + '<br/>' +
                        '</fieldset>';
            }
            html += '<fieldset style="border: 1px solid #000; padding: 5px 10px; margin-top: 10px;">' +
                    '<legend style="font-style: italic; font-weight: bold">Допълнителна информация за собственик</legend>' +
                    'Телефон: ' + responseText.phone + '<br/>' +
                    'Факс: ' + responseText.fax + '<br/>' +
                    'Мобилен телефон: ' + responseText.mobile + '<br/>' +
                    'Емейл: ' + responseText.email + '<br/>' +
                    'Адрес: ' + responseText.address +
                    '</fieldset>';

            jQuery('#info-panel').window('open');
            jQuery('#info-panel').html(html);
        }
    });
}

//function validates if all data is correct and if it is add record
function validateNewPCToOwnerRel() {
    var obj = new Object();

    //retrieve GET parameters
    var GET = {};
    location.search.substr(1).split("&").forEach(function(item) {
        GET[decodeURIComponent(item.split("=")[0])] = decodeURIComponent(item.split("=")[1])
    });

    var contractData;
    var plotData;
    if(GET.page == 'Annexes.Home')
    {
        contractData = jQuery('#annexes-tree').tree('getSelected');
        plotData = jQuery('#annex-plots-tables').datagrid('getChecked');
    }
    else if(GET.page == 'Contracts.Home')
    {
        contractData = jQuery('#contracts-tree').tree('getSelected');
        plotData = jQuery('#contract-plots-tables').datagrid('getChecked');
    }

    //check if information is correct
    var ownerData = jQuery('#owners-add-tables').datagrid('getChecked');

    //if owner is not selected
    if (ownerData[0] == undefined) {
        jQuery.messager.alert('Грешка', 'Не е избран собственик');
        return false;
    }

    var percent = 0;
    var shares = new Fraction();

    if (jQuery('#contract-ownage-fraction > input').is(':checked'))
    {
        if (jQuery('#ownage-numerator > input').val() == ''
                || jQuery('#ownage-denominator > input').val() == '')
        {
            jQuery.messager.alert('Грешка', 'Не сте въвели данни за собственост.');
            return false;
        } else {
            var numerator = jQuery('#ownage-numerator > input').val();
            var denominator = jQuery('#ownage-denominator > input').val();
            percent = (numerator / denominator) * 100;

            shares = new Fraction(numerator/denominator);
        }
    }

    if (jQuery('#contract-ownage-percent > input').is(':checked'))
    {
        if (jQuery('#contract-ownage-percent-field > input').val() == '')
        {
            jQuery.messager.alert('Грешка', 'Не сте въвели данни за собственост.');
            return false;
        } else {
            percent = parseFloat(jQuery('#contract-ownage-percent-field > input').val());

            shares = new Fraction(percent/100);
        }
    }

    if (jQuery('#contract-ownage-area > input').is(':checked'))
    {
        if (jQuery('#contract-ownage-area-field > input').val() == '')
        {
            jQuery.messager.alert('Грешка', 'Не сте въвели данни за собственост.');
            return false;
        } else {
            var ownershipArea = parseFloat(jQuery('#contract-ownage-area-field > input').val());

            percent = (ownershipArea / plotData[0].contract_area) * 100;

            shares = new Fraction(ownershipArea / plotData[0].contract_area);
        }
    }

    if(maxOwnership.valueOf() < 0) {
        maxOwnership = new Fraction(0);
    }

    if(shares.compare(maxOwnership) > 0) {
        jQuery.messager.alert('Грешка', 'Въвели сте некоректни данни за собственост.');
        return false;
    }

    //check if representative is chosen
    if (jQuery('#contract-owner-rep-by-another > input').is(':checked'))
    {
        var repData = jQuery('#contract-owners-reps-tables').datagrid('getChecked');
        var repId = null;

        if(contragent_type == 'owner') {
            if (repData[0] == undefined) {
                jQuery.messager.alert('Грешка', 'Не е избран представител');
                return false;
            } else if (repData[0].id == false) {
                jQuery.messager.alert('Грешка', 'Не можете да добавяте представител, преди да го запазите.');
                return false;
            }
            repId = repData[0].id;
        }

        //self rep hold if owner represents himself or is represented by another
        obj.self_rep = false;
        obj.rep_id = repId;
        obj.proxy_num = jQuery('#contract-rep-proxy-num > input').val();
        obj.proxy_date = jQuery('#contract-rep-proxy-date > input').datebox('getValue') || null;
        obj.notary_name = jQuery('#contract-rep-notary-name > input').val() || null;
        obj.notary_number = jQuery('#contract-rep-notary-num > input').numberbox('getValue') || null;
        obj.notary_address = jQuery('#contract-rep-notary-address > input').val() || null;
    }

    if (jQuery('#contract-owner-rep-by-himself > input').is(':checked'))
    {
        obj.self_rep = true;
        obj.rep_id = 0;
        obj.proxy_num = null;
        obj.proxy_date = null;
        obj.notary_name = null;
        obj.notary_number = null;
        obj.notary_address = null;
    }

    if(contragent_type == 'owner')
    {
        obj.owner_id = ownerData[0].id;
    }
    else if(contragent_type == 'farming')
    {
        obj.farming_id = ownerData[0].id;
    }

    obj.contract_id = contractData.id;
    obj.plot_id = plotData[0].gid;
    obj.percent = percent;
    obj.numerator = numerator;
    obj.denominator = denominator;


    TF.Rpc.Contracts.ConctractOwnerData.addNewPlotOwner(obj)
    .done(function (data) {
        closeOwnerAddWindows();
    })
    .fail(function (data) {
        //No Rights to operate with "Договори за собственост"
        if(data.contractType == CONTRACT_TYPE_OWN && !data.hasContractsOwnWriteRights) {
            messagerContractsOwnWriteRights();
            return false;
        }
        if (data.is(TF.Rpc.ExceptionsList.CONTRACTS_HAS_PAYMENTS_EXCEPTION)) {        
            jQuery('#win-has-payments-error').window('open');  
            initUnsuccessfullyEditContract(
                data.getOriginalMessage()
            );
            jQuery('#win-plot-owner-add').window('close');
        }
        if (typeof data === "Exception") {
            if (data.is(TF.Rpc.ExceptionsList.NON_EXISTING_OWNER_ID)) {
                jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.NON_EXISTING_OWNER_ID.message,'warning');
            } else if (data.is(TF.Rpc.ExceptionsList.NON_EXISTING_CONTRACT_PLOT_RELATION)) {
                jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.NON_EXISTING_CONTRACT_PLOT_RELATION.message, 'warning');
            } else if (data.is(TF.Rpc.ExceptionsList.INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS)) {
                jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS.message, 'warning');
            }
        };
    });

    jQuery('#owners-add-tables').datagrid('uncheckAll');
    jQuery('#owners-add-tables').datagrid('unselectAll');

    jQuery('#contract-owners-tables').treegrid('uncheckAll');
    jQuery('#contract-owners-tables').treegrid('unselectAll');
}

function validateEditPCToOwnerRel() {
    var obj = new Object();

    //retrieve GET parameters
    var GET = {};
    location.search.substr(1).split("&").forEach(function(item) {
        GET[decodeURIComponent(item.split("=")[0])] = decodeURIComponent(item.split("=")[1])
    });

    var contractData;
    var plotData;
    var heritorData;
    var ownerData;
    var farmingData = jQuery('#contract-farming-tables').datagrid('getSelected');

    if(GET.page == 'Annexes.Home')
    {
        contractData = jQuery('#annexes-tree').tree('getSelected');
        plotData = jQuery('#annex-plots-tables').datagrid('getChecked');
        heritorData = jQuery('#annex-owners-tables').treegrid('getSelected');
        ownerData = jQuery('#annex-owners-tables').treegrid('getSelected');
    }
    else if(GET.page == 'Contracts.Home')
    {
        contractData = jQuery('#contracts-tree').tree('getSelected');
        plotData = jQuery('#contract-plots-tables').datagrid('getChecked');
        heritorData = jQuery('#contract-owners-tables').treegrid('getSelected');
        ownerData = jQuery('#contract-owners-tables').treegrid('getSelected');
    }

    var percent = 0;
    var shares = new Fraction();

    if (jQuery('#edit-ownage-fraction > input').is(':checked'))
    {
        if (jQuery('#edit-ownage-numerator > input').val() == ''
                || jQuery('#edit-ownage-denominator > input').val() == '')
        {
            jQuery.messager.alert('Грешка', 'Не сте въвели данни за собственост.');
            return false;
        } else {
            var numerator = jQuery('#edit-ownage-numerator > input').val();
            var denominator = jQuery('#edit-ownage-denominator > input').val();
            percent = (numerator / denominator) * 100;

            shares = new Fraction(numerator / denominator);
        }
    }

    if (jQuery('#edit-ownage-percent > input').is(':checked'))
    {
        if (jQuery('#edit-ownage-percent-field > input').val() == '')
        {
            jQuery.messager.alert('Грешка', 'Не сте въвели данни за собственост.');
            return false;
        } else {
            percent = parseFloat(jQuery('#edit-ownage-percent-field > input').val());

            shares = new Fraction(percent / 100);
        }
    }

    if (jQuery('#edit-ownage-area > input').is(':checked'))
    {
        if (jQuery('#edit-ownage-area-field > input').val() == '')
        {
            jQuery.messager.alert('Грешка', 'Не сте въвели данни за собственост.');
            return false;
        } else {
            var ownershipArea = parseFloat(jQuery('#edit-ownage-area-field > input').val());

            percent = (ownershipArea / plotData[0].contract_area) * 100;

            shares = new Fraction(ownershipArea / plotData[0].contract_area);
        }
    }

    if(maxOwnership.valueOf() < 0) {
        maxOwnership = new Fraction(0);
    }

    if(shares.compare(maxOwnership) > 0) {
        jQuery.messager.alert('Грешка', 'Въвели сте некоректни данни за собственост.');
        return false;
    }

    if(contragent_type == 'owner' && isHeritorSelected)
    {
        obj.contract_id = contractData.id;
        obj.owner_id = heritorData.owner_id;
        obj.plot_id = plotData[0].gid;
        obj.percent = percent;
        obj.path = heritorData.path;
        obj.is_heritor = isHeritorSelected;
        obj.numerator = numerator;
        obj.denominator = denominator;
    }
    else if(contragent_type == 'owner')
    {
        obj.contract_id = contractData.id;
        obj.po_id = ownerData.id;
        obj.percent = percent;
        obj.numerator = numerator;
        obj.denominator = denominator;
    }
    else if(contragent_type == 'farming')
    {
        obj.contract_id = contractData.id;
        obj.pf_id = farmingData.id;
        obj.percent = percent;
        obj.numerator = numerator;
        obj.denominator = denominator;
    }

    TF.Rpc.Contracts.ConctractOwnerData.editOwnerPercent(obj)
        .done(function (data) {
            closeOwnerAddWindows();
        })
        .fail(function (errorObj) {
            if (errorObj.is(TF.Rpc.ExceptionsList.NON_EXISTING_CONTRACT_PLOT_RELATION)) {
                jQuery.messager.alert('Грешка',TF.Rpc.ExceptionsList.NON_EXISTING_CONTRACT_PLOT_RELATION.message,'warning');
            } 
            else if (errorObj.is(TF.Rpc.ExceptionsList.CONTRACTS_HAS_PAYMENTS_EXCEPTION)) {        
                jQuery('#win-has-payments-error').window('open');  
                initUnsuccessfullyEditContract(
                    errorObj.getOriginalMessage()
                );
                jQuery('#win-edit-contract-owner-data').window('close');
            }
            else if (errorObj.is(TF.Rpc.ExceptionsList.INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS)) {
                jQuery.messager.alert('Грешка',TF.Rpc.ExceptionsList.INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS.message,'warning');
            }
        });

    jQuery('#contract-owners-tables').treegrid('uncheckAll');
    jQuery('#contract-owners-tables').treegrid('unselectAll');
}

function closeOwnerAddWindows() {
    jQuery('#win-plot-owner-add').window('close');
    jQuery('#win-edit-contract-owner-data').window('close');
    reloadContractPlotsTable();

    jQuery('#contract-farming-tables').datagrid('loadRpc');
}

function reloadContractPlotsTable() {
    let row = jQuery('#contract-plots-tables').datagrid('getSelected');
    let index = jQuery('#contract-plots-tables').datagrid('getRowIndex', row);
    jQuery('#contract-plots-tables').datagrid('loadRpc');
    jQuery('#contract-plots-tables').datagrid('selectRow', index);
}

function displayTables(owner_type) {
    if (owner_type == 'physical') {
        jQuery('#company-data').hide();
        jQuery('#owner-data').show();
        jQuery('#is-dead').show();
        jQuery('#is-foreigner').show();
    }
    if (owner_type == 'legal') {
        jQuery('#owner-data').hide();
        jQuery('#is-dead').hide();
        jQuery('#is-foreigner').hide();
        jQuery('#company-data').show();
    }
}


function setAddOwnerFieldsValidators() {
    numLengthbox();
    jQuery('#name > input').validatebox({
        required: true,
        missingMessage: 'Моля въведете име на собственик.'
    });
    jQuery('#surname > input').validatebox({
        required: true,
        missingMessage: 'Моля въведете презиме на собственик.'
    });
    jQuery('#lastname > input').validatebox({
        required: true,
        missingMessage: 'Моля въведете фамилия на собственик.'
    });

    new EgnValidateBox('#egn > input');

    jQuery('#company_name > input').validatebox({
        required: true,
        missingMessage: 'Моля въведете име на фирма.'
    });
    jQuery('#eik > input').numberbox({
        required: true,
        validType: 'setLength[9]',
        missingMessage: 'Моля въведете ЕИК номер.',
        parser: function(s){
            return s;
        }
    });

    var ekateComboboxData = ComboboxData.EkateCombobox;

    ekateComboboxData[0].selected = true;

    jQuery('#rent-place input').combobox({
        data: ekateComboboxData,
        valueField: 'ekate',
        textField: 'text',
        value: '',
        filter: function(q, row){
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
            {
                return true;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function validateNewOwnerSubmitInfo() {
    var bic = jQuery('#info-owner-bic').val();
    if(bic && bic.length !== 8) {
        jQuery.messager.alert('Грешка', 'Дължината на BIC трябва да е 8 символа.');
        return false;
    };

    if (jQuery('#IsDead').is(':checked')) {
		const deadDate = jQuery('#dead-date > input').datebox('getValue');
		if(!deadDate) {
			jQuery.messager.alert('Грешка', 'Моля попълнете задължителните полета.');
			return false;
		}
	}

    if(jQuery('#phone input').val().length && jQuery('#phone input').val().length < 5) {
		jQuery.messager.alert('Грешка', 'Моля въведете поне 5 символа в полето "Телефон"', 'warning');
		return;
	}

	if(jQuery('#mobile input').val().length && jQuery('#mobile input').val().length < 5) {
		jQuery.messager.alert('Грешка', 'Моля въведете поне 5 символа в полето "Мобилен"', 'warning');
		return;
	}

    if (jQuery('#is_physical input').is(':checked')) {
        if (jQuery('#name > input').validatebox('isValid')
                && jQuery('#surname > input').validatebox('isValid')
                && jQuery('#lastname > input').validatebox('isValid')
                && egnBox.isValid()) {
            return true;
        } else {
            jQuery.messager.alert('Грешка', 'Моля попълнете задължителните полета.');
            return false;
        }
    } else {
        if (jQuery('#company_name > input').validatebox('isValid')
                && jQuery('#eik > input').numberbox('isValid')) {
            return true;
        } else {
            jQuery.messager.alert('Грешка', 'Моля попълнете задължителните полета.');
            return false;
        }
    }
    
}

function validateRepresentativeFields() {
    if (jQuery('#rep_name > input').validatebox('isValid')
            && jQuery('#rep_surname > input').validatebox('isValid')
            && jQuery('#rep_lastname > input').validatebox('isValid')
            && new EgnValidateBox('#rep_egn > input').isValid()
            && new CardIdValidateBox('#rep_lk_nomer > input').isValid()
            && jQuery('#rep_lk_izdavane > input').validatebox('isValid')) {
        return true;
    } else {
        return false;
    }
}

function displayMultiplyOwnerResult(message) {
    if (message == '200') {
        jQuery.messager.alert('Информация', 'Собствениците бяха успешно копирани.');
    };
}

function displayPlotOwnerInfo(data) {
    if (data.owner_type == 0) {
        var html = '<fieldset style="border: 1px solid #000; padding: 5px 10px;">' +
                '<legend style="font-style: italic; font-weight: bold">Информация за собственик</legend>' +
                'Вид собственик: Юридическо лице<br/>' +
                'Фирма: ' + data.company_name + '<br/>' +
                'ЕИК: ' + data.egn_eik + '<br/>' +
                'МОЛ: ' + data.mol + '<br/>' +
                'Адрес: ' + data.company_address + '<br/><br/>' +
                '</fieldset>';
    } else {
        var html = '<fieldset style="border: 1px solid #000; padding: 5px 10px;">' +
                '<legend style="font-style: italic; font-weight: bold">Информация за собственик</legend>' +
                'Вид собственик: Физическо лице<br/>' +
                'Имена: ' + data.owner_names + '<br/>' +
                'ЕГН: ' + data.egn_eik + '<br/>' +
                'Номер на лична карта: ' + data.lk_nomer + '<br/>' +
                'Издаване на ЛК: ' + data.lk_izdavane + '<br/>' +
                'Починал: ' + data.is_dead + '<br/>' +
                '</fieldset>';
    }
    html += '<fieldset style="border: 1px solid #000; padding: 5px 10px; margin-top: 5px;">' +
            '<legend style="font-style: italic; font-weight: bold">Допълнителна информация за собственик</legend>' +
            'Телефон: ' + data.phone + '<br/>' +
            'Факс: ' + data.fax + '<br/>' +
            'Мобилен телефон: ' + data.mobile + '<br/>' +
            'Емейл: ' + data.email + '<br/>' +
            'Адрес: ' + data.address +
            '</fieldset>';
    html += '<fieldset style="border: 1px solid #000; padding: 5px 10px; margin-top: 5px;">' +
            '<legend style="font-style: italic; font-weight: bold">Документ за собственост</legend>' +
            'Тип: ' + data.document_type + '<br/>' +
            'Номер: ' + data.document_number + '<br/>' +
            'Дата: ' + data.document_date + '<br/>' +
            '</fieldset>';
    if (data.self_rep == data.owner_id) {
        jQuery('#info-panel').window('resize', {
            width: 400,
            height: 445
        });
        html += '<fieldset style="border: 1px solid #000; padding: 5px 10px; margin-top: 5px;">' +
                '<legend style="font-style: italic; font-weight: bold">Представлява се</legend>' +
                'Лично<br/>' +
                '</fieldset>';
    } else {
        jQuery('#info-panel').window('resize', {
            width: 400,
            height: 550
        });
        html += '<fieldset style="border: 1px solid #000; padding: 5px 10px; margin-top: 5px;">' +
                '<legend style="font-style: italic; font-weight: bold">Представлява се от</legend>' +
                'Имена: ' + data.rep_names + '<br/>' +
                'ЕГН: ' + data.rep_egn + '<br/>' +
                'Номер на лична карта: ' + data.rep_lk + '<br/>' +
                'Издаване на ЛК: ' + data.rep_lk_izdavane + '<br/>' +
                'Адрес: ' + data.rep_address + '<br/>' +
                'Номер на пълномощно: ' + data.proxy_num + '<br/>' +
                'Дата на пълномощно: ' + data.proxy_date + '<br/>' +
                '</fieldset>';
    }
    jQuery('#info-panel').html(html);
    jQuery('#info-panel').window('open');
}

function displayOwnerHeritorInfo(owner_id) {
    jQuery.ajax({
        url: 'index.php?rpc=owner-info',
        type: 'post',
        rpcParams: {
            id: owner_id
        },
        complete: function(response) {
            jQuery('#info-panel').window('open');
            var responseText = jQuery.parseJSON(response.responseText);

            jQuery('#info-panel').window('resize', {
                width: 420,
                height: 330
            });
            var html = '<fieldset style="border: 1px solid #000; padding: 5px 10px;">' +
                    '<legend style="font-style: italic; font-weight: bold">Информация за собственик</legend>' +
                    'Вид собственик: ' + responseText.owner_type + '<br/>' +
                    'Имена: ' + responseText.name + ' ' + responseText.surname + ' ' + responseText.lastname + '<br/>' +
                    'ЕГН: ' + responseText.egn + '<br/>' +
                    'Номер на лична карта: ' + responseText.lk_nomer + '<br/>' +
                    'Издаване на ЛК: ' + responseText.lk_izdavane + '<br/>' +
                    'Починал: ' + responseText.is_dead + '<br/>' +
                    '</fieldset>' +
                    '<fieldset style="border: 1px solid #000; padding: 5px 10px; margin-top: 10px;">' +
                    '<legend style="font-style: italic; font-weight: bold">Допълнителна информация за собственик</legend>' +
                    'Телефон: ' + responseText.phone + '<br/>' +
                    'Факс: ' + responseText.fax + '<br/>' +
                    'Мобилен телефон: ' + responseText.mobile + '<br/>' +
                    'Емейл: ' + responseText.email + '<br/>' +
                    'Адрес: ' + responseText.address +
                    '</fieldset>';
            jQuery('#info-panel').html(html);
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function getTotalOwnersShares() {
    var gridData = jQuery('#contract-owners-tables').treegrid('getData');
    var sumFr = new Fraction();

    //calculate total ownership
    if (gridData[0]) {
        for (var i = 0; i < gridData.length; i++) {
            var f;
            if(gridData[i].numerator && gridData[i].denominator) {
                f = new Fraction(gridData[i].numerator / gridData[i].denominator);
            }
            else {
                f = new Fraction(gridData[i].percent / 100);
            }

            sumFr = sumFr.add(f);
        }
    }

    return sumFr;
}

function clearAddOwnerInputDataFields() {
    jQuery('#name > input').val('');
    jQuery('#surname > input').val('');
    jQuery('#lastname > input').val('');
    jQuery('#lk_izdavane > input').val('');
    jQuery('#company_name > input').val('');
    jQuery('#eik > input').val('');
    jQuery('#mol > input').val('');
    jQuery('#company_address > textarea').val('');
    jQuery('#phone > input').val('');
    jQuery('#fax > input').val('');
    jQuery('#mobile > input').val('');
    jQuery('#email > input').val('');
    jQuery('#address > textarea').val('');
    jQuery('#remark > textarea').val('');
    jQuery('#is_legal > input').prop('checked', false);
    jQuery('#is_physical > input').prop('checked', true);
    jQuery('#is-dead > input').prop('checked', false);
    jQuery('#is-foreigner > input').prop('checked', false);
    jQuery('#rent-place > input').val('');
    jQuery('#prepiska > input').val('');
    jQuery('#info-owner-bank-name').val('');
    jQuery('#info-owner-iban').val('');
    jQuery('#info-owner-bic').val('');
    new EgnValidateBox('#egn > input').setValue('');
    new CardIdValidateBox('#lk_nomer > input').setValue('');

    jQuery('#prepiska-row').hide();

    PostPaymentOwner.clearPostPaymentInputs();

    setAddOwnerFieldsValidators();
}

function onAddownerPanelOpen() {
    var rentPlaceInput = jQuery('#rent-place input'),
        rentPlaceValue = rentPlaceInput.val(),
        ekateComboboxData = ComboboxData.EkateCombobox;

    ekateComboboxData[0].selected = true;
    rentPlaceInput.combobox({
        data: ekateComboboxData,
        valueField: 'ekate',
        textField: 'text',
        filter: function(q, row){
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
            {
                return true;
            }
        },
        onLoadSuccess: function (data) {
            if (rentPlaceValue) {
                rentPlaceInput.combobox('setValue', rentPlaceValue);
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#dead-date > input').datebox({
		required: true
	});

    if(jQuery('#IsDead').is(':checked')) {
        jQuery('#prepiska-row').show();
        jQuery('#dead-date-row').show();
    } else {
        jQuery('#prepiska-row').hide();
        jQuery('#dead-date-row').hide();
    }
    
}

function getAddOwnerInputFieldsData() {
    return {
        name: jQuery('#name > input').val(),
        surname: jQuery('#surname > input').val(),
        lastname: jQuery('#lastname > input').val(),
        egn: new EgnValidateBox('#egn > input').getValue(),
        lk_nomer: new CardIdValidateBox('#lk_nomer > input').getValue(),
        lk_izdavane: jQuery('#lk_izdavane > input').val(),
        company_name: jQuery('#company_name > input').val(),
        eik: jQuery('#eik > input').val(),
        mol: jQuery('#mol > input').val(),
        company_address: jQuery('#company_address > textarea').val(),
        phone: jQuery('#phone > input').val(),
        fax: jQuery('#fax > input').val(),
        mobile: jQuery('#mobile > input').val(),
        email: jQuery('#email > input').val(),
        address: jQuery('#address > textarea').val(),
        remark: jQuery('#remark > textarea').val(),
        is_legal: jQuery('#is_legal > input').is(':checked') ? true : false,
        is_physical: jQuery('#is_physical > input').is(':checked') ? true : false,
        is_dead: jQuery('#IsDead').is(':checked') ? true : false,
        dead_date: jQuery('#IsDead').is(':checked') ? jQuery('#dead-date > input').datebox('getValue') : null,
        is_foreigner: jQuery('#is-foreigner > input').is(':checked') ? true : false,
        rent_place: jQuery('#rent-place > input').combobox('getValue'),
        prepiska: jQuery('#IsDead').is(':checked') ? jQuery('#prepiska > input').val() : null,
        bank_name: jQuery('#info-owner-bank-name').val(),
        iban: jQuery('#info-owner-iban').val(),
        bic: jQuery('#info-owner-bic').val(),
        post_payment_fields: PostPaymentOwner.getPostPaymentValues()
    };
}

function addOwnerToContract() {
    if (!hasPlotRightsRW) {
        messagerPlotsWriteRights();
        return false;
    }

    jQuery('#win-plot-owner-add').window('resize', {
        height: getZoomedWindowHeight(630),
        width: 1010
    });

    var plotData = jQuery('#contract-plots-tables').datagrid('getChecked'),
        contractData = jQuery('#contracts-tree').tree('getSelected'),
        max,
        sum;
    //No Rights to operate with "Договори за собственост"
    if (contractData.attributes.c_type == CONTRACT_TYPE_OWN && !hasContractsOwnWriteRights) {
        messagerContractsOwnWriteRights();
        return false;
    }

    if (contractData.attributes.from_sublease > 0) {
        messageContractIsFromSublease(contractData);
        return false;
    }

    if(contractData.attributes.is_closed_for_editing == true) {
        jQuery.messager.alert('Грешка', 'Договора е заключен за редакция.', 'warning');
        return false;
    }

    max = new Fraction(1);

    if (!plotData[0]) {
        jQuery.messager.alert('Грешка', 'Не е избран имот.');
        return;
    }

    sum = getTotalOwnersShares();

    if (sum.valueOf() >= 1) {
        jQuery.messager.alert('Грешка', 'Вече е зададена 100% собственост за този имот.');
        return;
    }
    maxOwnership = max.sub(sum);

    contragent_type = 'owner';
    if(contractData.attributes.hasPayment){
        jQuery.messager.confirm('Потвърждение', "По този договор са извършени плащания. Промените ще се отразят в справката за изплатени ренти!", function(r) {
            if(r){
                jQuery('#win-plot-owner-add').window('open');
                //init add owners grid
                initAddOwnersGrid(contractData.id, plotData[0].gid);
                //init representatives
                initOwnersRepsGrid();
            }
        });

    }else {
        jQuery('#win-plot-owner-add').window('open');
        //init add owners grid
        initAddOwnersGrid(contractData.id, plotData[0].gid);
        //init representatives
        initOwnersRepsGrid();
    }

}

function editOwnerPercentage() {
    if (!hasPlotRightsRW) {
        messagerPlotsWriteRights();
        return false;
    }

    var getSelected = jQuery('#contract-owners-tables').treegrid('getSelected'),
        gridData = jQuery('#contract-owners-tables').treegrid('getData'),
        sum = new Fraction(),
        max = new Fraction(1),
        contractData = jQuery('#contracts-tree').tree('getSelected'),
        i,
        f;
    //No Rights to operate with "Договори за собственост"
    if (contractData.attributes.c_type == CONTRACT_TYPE_OWN && !hasContractsOwnWriteRights) {
        messagerContractsOwnWriteRights();
        return false;
    }
    if (contractData.attributes.from_sublease > 0) {
        messageContractIsFromSublease(contractData);
        return false;
    }

    if (!getSelected) {
        jQuery.messager.alert('Грешка', 'Моля изберете запис, който искате да редактирате.');
        return;
    }

    if(contractData.attributes.is_closed_for_editing == true) {
        jQuery.messager.alert('Грешка', 'Договора е заключен за редакция.', 'warning');
        return false;
    }


    if (isHeritorSelected) {
        var parent = jQuery('#contract-owners-tables').treegrid('getParent', getSelected.fakeid),
            childrens = jQuery('#contract-owners-tables').treegrid('getChildren', parent.fakeid),
            directChildrens = [];
        for (i = 0; i < childrens.length; i++) {
            if(childrens[i].level == parent.level + 1) {
                directChildrens.push(childrens[i]);
            }
        }
        gridData = directChildrens;

        if (parent.numerator && parent.denominator) {
            max = new Fraction(parent.numerator / parent.denominator);
        } else {
            max = new Fraction(parent.percent / 100);
        }
    }
    for (i = 0; i < gridData.length; i++) {
        if (getSelected.fakeid != gridData[i].fakeid) {
            if (gridData[i].numerator && gridData[i].denominator) {
                f = new Fraction(gridData[i].numerator / gridData[i].denominator);
            } else {
                f = new Fraction(gridData[i].percent / 100);
            }

            sum = sum.add(f);
        }
    }

    maxOwnership = max.sub(sum);

    contragent_type = 'owner';
    if(contractData.attributes.hasPayment){
        jQuery.messager.confirm('Потвърждение', "По този договор са извършени плащания. Промените ще се отразят в справката за изплатени ренти!", function(r) {
            if(r){
                initEditOwnageDataFields();
                jQuery('#win-edit-contract-owner-data').window('open');
            }
        });

    }else {
        initEditOwnageDataFields();
        jQuery('#win-edit-contract-owner-data').window('open');
    }}

function deletePlotOwnerRelation() {
    if (!hasPlotRightsRW) {
        messagerPlotsWriteRights();
        return false;
    }
    var contractData = jQuery('#contracts-tree').tree('getSelected');
    //No Rights to operate with "Договори за собственост"
    if (contractData.attributes.c_type == CONTRACT_TYPE_OWN && !hasContractsOwnWriteRights) {
        messagerContractsOwnWriteRights();
        return false;
    }

    if (contractData.attributes.from_sublease > 0) {
        messageContractIsFromSublease(contractData);
        return false;
    }

    if(contractData.attributes.is_closed_for_editing == true) {
        jQuery.messager.alert('Грешка', 'Договора е заключен за редакция.', 'warning');
        return false;
    }

    var getSelected = jQuery('#contract-owners-tables').treegrid('getSelected');

    if(contractData.attributes.hasPayment){
        var confurm_text = "По този договор са извършени плащания. Промените ще се отразят в справката за изплатени ренти!" +
            "Сигурни ли сте, че искате да премахнете този собственик.";
    }else{
        var confurm_text = "Сигурни ли сте, че искате да премахнете този собственик.";
    }

    if (getSelected) {

        if(getSelected.is_heritor) {
            jQuery.messager.alert('Грешка', 'Наследник може да изтриете от менюто Имоти->Собственици.');
            return;
        }

        jQuery.messager.confirm('Потвърждение',
            confurm_text, function (r) {
                if (r) {
                    var plotOwnerRelId = getSelected.id,
                        ownerId = getSelected.owner_id;
                    TF.Rpc.Contracts.ConctractOwnerData.deletePlotOwner(plotOwnerRelId, ownerId)
                        .done(function () {
                            jQuery('#contract-owners-tables').treegrid('loadRpc');
                            reloadContractPlotsTable();
                        })
                        .fail(function (errorObj) {
                            if (errorObj.is(TF.Rpc.ExceptionsList.NON_EXISTING_CONTRACT_PLOT_RELATION)) {
                                jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.NON_EXISTING_CONTRACT_PLOT_RELATION.message, 'warning');
                            } 
                            else if (errorObj.is(TF.Rpc.ExceptionsList.CONTRACTS_HAS_PAYMENTS_EXCEPTION)) {        
                                jQuery('#win-has-payments-error').window('open');  
                                initUnsuccessfullyEditContract(
                                    errorObj.getOriginalMessage()
                                );
                            }
                            else if (errorObj.is(TF.Rpc.ExceptionsList.INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS)) {
                                jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS.message, 'warning');
                            }
                        });
                }
            });
    } else {
        jQuery.messager.alert('Грешка', 'Моля изберете собственик, който да бъде премахнат.');
    }
}

function copyPlotOwnerRelation() {
    if (!hasPlotRightsRW) {
        messagerPlotsWriteRights();
        return false;
    }
    var plotData = jQuery('#contract-plots-tables').datagrid('getData'),
        ownerData = jQuery('#contract-owners-tables').treegrid('getData'),

        selectedContract = jQuery('#contracts-tree').tree('getSelected'),
        selectedPlot = jQuery('#contract-plots-tables').datagrid('getSelected'),

        contractData = jQuery('#contracts-tree').tree('getSelected');
    //No Rights to operate with "Договори за собственост"
    if (contractData.attributes.c_type == CONTRACT_TYPE_OWN && !hasContractsOwnWriteRights) {
        messagerContractsOwnWriteRights();
        return false;
    }

    if (contractData.attributes.from_sublease > 0) {
        messageContractIsFromSublease(contractData);
        return false;
    }

    if(contractData.attributes.is_closed_for_editing == true) {
        jQuery.messager.alert('Грешка', 'Договора е заключен за редакция.', 'warning');
        return false;
    }

    if (ownerData.length > 0) {
        if (plotData.rows.length != 1) {


            jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да добавите тези собственици към всички имоти за този договор?', function (r) {
                if (r) {
                    TF.Rpc.Contracts.ConctractOwnerData.multiplyOwners(selectedContract.id, selectedPlot.gid)
                        .done(function (data) {
                            displayMultiplyOwnerResult(data);
                            reloadContractPlotsTable();
                        })
                        .fail(function (errorObj) {
                            if (errorObj.is(TF.Rpc.ExceptionsList.NON_EXISTING_CONTRACT_PLOT_RELATION)) {
                                jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.NON_EXISTING_CONTRACT_PLOT_RELATION.message, 'warning');
                            } else if (errorObj.is(TF.Rpc.ExceptionsList.INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS)) {
                                jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS.message, 'warning');
                            } else if (errorObj.is(TF.Rpc.ExceptionsList.CONTRACTS_HAS_PAYMENTS_EXCEPTION)) {        
                                jQuery('#win-has-payments-error').window('open');  
                                initUnsuccessfullyEditContract(
                                    errorObj.getOriginalMessage()
                                );
                            }
                        });
                }
            });
        } else {
            jQuery.messager.alert('Грешка', 'Към договора има добавен само един имот. Моля добавете други имоти, за които искате да бъдат копирани данните за собсвениците.');
        }
    } else {
        jQuery.messager.alert('Грешка', 'Няма въведени собственици. Моля въведете собственици, които да бъдат копирани.');
    }
}

function changeIsSigner() {
    if (!hasPlotRightsRW) {
        messagerPlotsWriteRights();
        return false;
    }
    var contractData = jQuery('#contracts-tree').tree('getSelected');
    //No Rights to operate with "Договори за собственост"
    if (contractData.attributes.c_type == CONTRACT_TYPE_OWN && !hasContractsOwnWriteRights) {
        messagerContractsOwnWriteRights();
        return false;
    }

    if (contractData.attributes.from_sublease > 0) {
        messageContractIsFromSublease(contractData);
        return false;
    }

    if(contractData.attributes.is_closed_for_editing == true) {
        jQuery.messager.alert('Грешка', 'Договора е заключен за редакция.', 'warning');
        return false;
    }

    var getSelected = jQuery('#contract-owners-tables').treegrid('getSelected');

    if (!getSelected) {
        jQuery.messager.alert('Грешка', 'Моля избетере собственик.');
        return false;
    }

    if(getSelected.owner_type === 0) {
        jQuery.messager.alert('Грешка', 'Тази опция не е валидна за собственик тип  Стопанство.');
        return false;
    }

    if (getSelected.dead) {
        jQuery.messager.alert('Грешка', 'Избрали сте починал собственик.');
        return false;
    }

    var confurm_text = "Сигурни ли сте, че искате да <b>маркирате избраният собственик</b> като подписал договора?"
    if(getSelected.is_signer){
        confurm_text = "Сигурни ли сте, че искате да <b>премахнете избрания собственик</b>, от хората които са подписали договора?";
    }

    jQuery.messager.confirm('Потвърждение',
        confurm_text, function (r) {
            if (r) {
                var is_signer = getSelected.is_signer ? null : 1;
                TF.Rpc.Contracts.ConctractOwnerData.changeIsSigner(getSelected.id, is_signer)
                    .done(function () {
                        jQuery('#contract-owners-tables').treegrid('loadRpc');
                    })
                    .fail(function (errorObj) {
                        if (errorObj.is(TF.Rpc.ExceptionsList.NON_EXISTING_CONTRACT_PLOT_RELATION)) {
                            jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.NON_EXISTING_CONTRACT_PLOT_RELATION.message, 'warning');
                        } else if (errorObj.is(TF.Rpc.ExceptionsList.INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS)) {
                            jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS.message, 'warning');
                        }
                    });
            }
        });

}

function displayOwnerInformation() {
    var getSelected = jQuery('#contract-owners-tables').treegrid('getSelected');
    if (getSelected) {
        window.open("index.php?page=Owners.Home&owner_id=" + getSelected.owner_id, '_blank');
    } else {
        jQuery.messager.alert('Грешка', 'Моля изберете собственик, за който да бъде показана информация.');
    }
}

function editOwnerData() {

    lastAddedOwner = 0;
    lastAddedParent = 0;

    var getSelected = jQuery('#contract-owners-tables').treegrid('getSelected');

    if (getSelected) {
        TF.Rpc.Owners.OwnersTree.markForEdit(getSelected.owner_id)
            .done(function (data) {
                ownerType = data.owner_type;
                isEditing = true;
                jQuery('#win-add-new-owner').window('open');
                setOwnerFieldsDataForEdit(data);
                setAddEditFieldsValidators();
                displayTablesOnCallbackComplete(data);
            })
            .fail(function (errorObj) {
                jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
            })    
    } else {
        jQuery.messager.alert('Грешка', 'Моля изберете собственик, който да бъде редактиран.');
    }
}

function displayTablesOnCallbackComplete(data){
    if(ownerType == 0) {
        displayTables('legal');
        jQuery('#is_legal > input').prop('checked', true);
        jQuery('#is_physical > input').prop('checked', false);
    }else{
        displayTables('physical');
        jQuery('#is_legal > input').prop('checked', false);
        jQuery('#is_physical > input').prop('checked', true);
    }

    jQuery('#dead-date > input').datebox({value: data.dead_date});
}

function setAddEditFieldsValidators() {
    numLengthbox();
    jQuery('#name > input').validatebox({
        required: true,
        missingMessage: 'Моля въведете име на собственик.'
    });
    jQuery('#surname > input').validatebox({
        required: true,
        missingMessage: 'Моля въведете презиме на собственик.'
    });
    jQuery('#lastname > input').validatebox({
        required: true,
        missingMessage: 'Моля въведете фамилия на собственик.'
    });

    let egnBox = new EgnValidateBox('#egn > input');

    if(jQuery('#is-dead input').is(':checked')) {
       egnBox.disableValidation();
    } else {
        egnBox.enableValidation();
    }

    jQuery('#company_name > input').validatebox({
        required: true,
        missingMessage: 'Моля въведете име на фирма.'
    });
    jQuery('#eik > input').numberbox({
        required: true,
        validType: 'setLength[9]',
        missingMessage: 'Моля въведете ЕИК номер.',
        parser: function(s){
            return s;
        }
    });
    jQuery('#mobile > input').validatebox({
        missingMessage: 'Моля въведете мобилен телефон.'
    });
    jQuery('#email > input').validatebox({
        missingMessage: 'Моля въведете имейл на собственика.'
    });

    jQuery('#is_foreigner').on('change', function () {
        if(jQuery('#is_foreigner').is(':checked')) {
            egnBox.disableValidation();
        } else {
            egnBox.enableValidation();
        }
    });

    jQuery('#IsDead').on('change', function () {
        if(jQuery('#is_foreigner').is(':checked') && jQuery('#IsDead').is(':checked')) {
           egnBox.disableValidation();
        } else {
           egnBox.enableValidation();
        }

        if(jQuery('#IsDead').is(':checked')) {
            jQuery('#prepiska-row').show();
            jQuery('#dead-date-row').show();
        } else {
            jQuery('#prepiska-row').hide();
            jQuery('#dead-date-row').hide();
        }
    });
    
	jQuery('#dead-date > input').datebox({
		required: true
	});

    jQuery('#is_foreigner').trigger('change');
    jQuery('#IsDead').trigger('change');
}

function setOwnerFieldsDataForEdit(data) {
    jQuery('#name > input').val(data['name']);
    jQuery('#surname > input').val(data['surname']);
    jQuery('#lastname > input').val(data['lastname']);
    jQuery('#lk_izdavane > input').val(data['lk_izdavane']);
    jQuery('#company_name > input').val(data['company_name']);
    jQuery('#eik > input').numberbox('setValue', data['eik']);
    jQuery('#mol > input').val(data['mol']);
    jQuery('#company_address > textarea').val(data['company_address']);
    jQuery('#phone > input').val(data['phone']);
    jQuery('#fax > input').val(data['fax']);
    jQuery('#mobile > input').val(data['mobile']);
    jQuery('#email > input').val(data['email']);
    jQuery('#address > textarea').val(data['address']);
    jQuery('#remark > textarea').val(data['remark']);
    egnBox.setValue(data['egn']);
    cardIdBox.setValue(data['lk_nomer']);
    jQuery('#info-owner-bank-name').val(data['bank_name']);
    jQuery('#info-owner-iban').val(data['iban']);
    jQuery('#info-owner-bic').val(data['bic']);

    if (data['owner_type'] == 0) {
        jQuery('#is_legal > input').prop('checked', true);
        jQuery('#is_physical > input').prop('checked', false);
    }
    else {
        jQuery('#is_legal > input').prop('checked', false);
        jQuery('#is_physical > input').prop('checked', true);
    }

    data['is_dead'] ? jQuery('#IsDead').prop('checked', true) : jQuery('#IsDead').prop('checked', false);
    data['is_foreigner'] ? jQuery('#is-foreigner > input').prop('checked', true) : jQuery('#is-foreigner > input').prop('checked', false);
    data['is_dead'] ? jQuery('#prepiska > input').val(data['prepiska']) : jQuery('#prepiska > input').val('');
    jQuery('#rent-place > input').combobox('setValue', data['rent_place']);
    PostPaymentOwner.showPostPaymentFields(data.post_payment_fields);
}
