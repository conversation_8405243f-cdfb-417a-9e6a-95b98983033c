
/*jslint browser: true*/
/*global jQuery, EasyUIRPCLoaders.EasyUIGridCustomLoader, TF,bufferview, Fraction */

var totalContractArea = 0,
    contractType,
    ownershipContractId = 1,
    editIndex,
    checkedPlotsForAddition;

function initContractsPlotsGrid(contract_id, c_type) {
    var contractType = c_type,
        selectedContractId = contract_id,
        contractPlotsDatagrid = jQuery('#contract-plots-tables'),
        isDatagridBound = contractPlotsDatagrid.data().hasOwnProperty('datagrid'),
        contractsTreeFilterObj = jQuery('#contracts-tree').data().tree.options.rpcParams[0];

    if (isDatagridBound) {
        if (contract_id !== 0) {
            contractPlotsDatagrid.datagrid({
                url: 'index.php?contracts-rpc=contracts-plots-datagrid',
                rpcParams: [{
                    type: 'view',
                    contract_id: selectedContractId,
                    contracts_tree_filter: contractsTreeFilterObj
                }],
                onLoadSuccess: function (data) {
                    var rent_per_plot = false;
                    for(var row of data.rows) {
                        if(!isNaN(parseFloat(row.rent_per_plot))) {
                            rent_per_plot = true;
                            break;
                        }
                    }
                    if (rent_per_plot === true) {
                        jQuery('#rent-per-plot-info').show();
                    } else {
                        jQuery('#rent-per-plot-info').hide();
                    }

                    jQuery('#contract-plots-tables').datagrid('getPager').pagination({
                        layout: ['list', 'refresh'],
                        displayMsg: 'От {from} до ' + jQuery('#contract-plots-tables').datagrid('getRows').length + ' от {total} записа'
                    });

                    selectedRowByFilter = getSelectedPlotByFilter();
                    select_row_index = selectedRowByFilter;
                    if (selectedRowByFilter != 0) {
                        select_row_index = selectedRowByFilter;
                        if (contractPlotsDatagrid.datagrid('options').view.renderedCount <= contractPlotsDatagrid.datagrid('options').pageSize) {
                            var table_target = jQuery('#contract-plots-tables')[0];
                            while(selectedRowByFilter > (contractPlotsDatagrid.datagrid('options').view.renderedCount - 1)) {
                                contractPlotsDatagrid.datagrid('options').view.getRows(table_target, function (rows) {
                                    contractPlotsDatagrid.datagrid('options').view.rows = rows;
                                    contractPlotsDatagrid.datagrid('options').view.populate(table_target);
                                });
                            }

                            contractPlotsDatagrid.datagrid('selectRow', select_row_index);
                        }
                    }
                    var selectedRow = contractPlotsDatagrid.datagrid('getSelected');
                    if (selectedRow === null) {
                        selectedRow = select_row_index;
                    } else {
                        if (contractPlotsDatagrid.datagrid('options').view.renderedCount > select_row_index
                            && contractPlotsDatagrid.datagrid('options').view.renderedCount - 2 * contractPlotsDatagrid.datagrid('options').pageSize < select_row_index) {
                            selectedRow = contractPlotsDatagrid.datagrid('getRowIndex', selectedRow);
                        } else {
                            selectedRow = contractPlotsDatagrid.datagrid('options').view.renderedCount - contractPlotsDatagrid.datagrid('options').pageSize;
                        }
                    }
                    if (selectedRow < 0) {
                        selectedRow = 0;
                    }
                    contractPlotsDatagrid.datagrid('selectRow', selectedRow);
                }
            });
        } else {
            contractPlotsDatagrid.datagrid('loadData', {"rows": [], "total": 0, "footer": [{"contract_area": 0, "document_area": 0}]});
            initContractsFarmingGrid(0, 0, 0, 0);
            initPlotContractsGrid(0, 0);
        }
        return;
    }

    function clearOwnersGrid() {
        let contractsOwnersTreegrid = jQuery('#contract-owners-tables');
        let isOwnersBound = contractsOwnersTreegrid.data().hasOwnProperty('treegrid');
        if (isOwnersBound === true) {
            contractsOwnersTreegrid.treegrid('loadData', {"rows": [], "total": 0});
        }
    }

    contractPlotsDatagrid.datagrid({
        iconCls: 'icon-edit-geometry',
        nowrap: true,
        title: 'Имоти',
        autoRowHeight: true,
        striped: true,
        pageSize: 30,
        fit:true,
        singleSelect: true,
        fitColumns: false,
        showFooter: true,
        view: bufferview,
        sortName: 'gid',
        sortOrder: 'asc',
        idField: 'gid',
        rowStyler: function (index, row) {
            if(jQuery.isEmptyObject(row)) return;
            let style = [];
            if (row.is_edited == true) {
                style.push('color: #aaa');
            }

            if (row.annex_action == 'removed') {
                style.push('text-decoration: line-through');
            }
            
            let selectedContract = jQuery('#contracts-tree').tree('getSelected');
            if(selectedContract.attributes.c_type !== 1 && ((row.owner_ids !== undefined && !row.owner_ids) && (row.farming_ids !== undefined && !row.farming_ids))) {
                style.push('background-color: #ff7f6b');
                jQuery('#ownerless-warning').show();
            }

            

            setCopyErrorStyles(row.gid, style);
            return style.join(';');
        },
        columns: [[
            {
                field: 'ekatte_name',
                title: '<b>Землище</b>',
                sortable: true,
                width: 100
            }, {
                field: 'kad_ident',
                title: '<b>Идентификатор</b>',
                sortable: true,
                width: 100
            }, {
                field: 'contract_area',
                title: '<b>Площ по<br/>договор (дка)</b>',
                align: 'center',
                sortable: true,
                width: 90
            }, {
                field: 'document_area',
                title: '<b>Площ по<br/>док. (дка)</b>',
                align: 'center',
                sortable: true,
                width: 90
            }, {
                field: 'kvs_allowable_area',
                title: '<b>Обработваема<br/>площ (дка)</b>',
                align: 'center',
                sortable: true,
                width: 90
            }, {
                field: 'price_per_acre',
                title: '<b>Цена/дка</b>',
                align: 'center',
                sortable: true,
                width: 90
            }, {
                field: 'price_sum',
                title: '<b>Сума/лв.</b>',
                align: 'center',
                sortable: true,
                width: 90
            }, {
                field: 'category',
                title: '<b>Категория</b>',
                sortable: true,
                width: 100
            }, {
                field: 'area_for_rent',
                title: '<b>Площ за <br/>рента(дка)</b>',
                sortable: false,
                width: 100
            },
            {
                field: 'allowable_area',
                title: '<b>Площ по <br/>сечение(дка)</b>',
                sortable: true,
                width: 100
            }, {
                field: 'area_type',
                title: '<b>НТП</b>',
                sortable: true,
                width: 100
            },
            {
                field: 'rent_per_plot_txt',
                title: '<b>Индивидуална рента/дка</b>',
                sortable: true,
                width: 100
            },
            {
                field: 'comment',
                title: '<b>Забележка</b>',
                sortable: false,
                width: 100
            }
        ]],
        pagination: false,
        rownumbers: true,
        toolbar: '#contract-plot-relation-toolbar',
        onBeforeLoad: function () {
            clearOwnersGrid();

            jQuery('#contract-plots-tables').datagrid('clearChecked');
            var selectedContract = jQuery('#contracts-tree').tree('getSelected');
            if(selectedContract && selectedContract.attributes.c_type !== 1) {
                clearOwnersGrid();
            }

            if (selectedContract && selectedContract.attributes.c_type == 1) {
                jQuery('#contract-plots-tables').datagrid('showColumn', 'price_per_acre');
                jQuery('#contract-plots-tables').datagrid('showColumn', 'price_sum');
            } else {
                jQuery('#contract-plots-tables').datagrid('hideColumn', 'price_per_acre');
                jQuery('#contract-plots-tables').datagrid('hideColumn', 'price_sum');
            }

            if (selectedContract && (selectedContract.attributes.c_type == 2 || selectedContract.attributes.c_type == 3 || selectedContract.attributes.c_type == 5)) {
                jQuery('#contract-plots-tables').datagrid('showColumn', 'area_for_rent');
                jQuery('#contract-plots-tables').datagrid('showColumn', 'allowable_area');
                jQuery('#contract-plots-tables').datagrid('showColumn', 'rent_per_plot_txt');
            } else {
                jQuery('#contract-plots-tables').datagrid('hideColumn', 'area_for_rent');
                jQuery('#contract-plots-tables').datagrid('hideColumn', 'allowable_area');
                jQuery('#contract-plots-tables').datagrid('hideColumn', 'rent_per_plot_txt');
            }
                    
            setGidsFromCopy();
        },
        onLoadSuccess: function () {
            jQuery('#contract-plots-tables').datagrid('getPager').pagination({
                layout: ['list', 'refresh'],
                displayMsg: 'От {from} до ' + jQuery('#contract-plots-tables').datagrid('getRows').length + ' от {total} записа'
            });

            selectedRowByFilter = getSelectedPlotByFilter();
            select_row_index = selectedRowByFilter;
            if (selectedRowByFilter != 0) {
                select_row_index = selectedRowByFilter;
                if (contractPlotsDatagrid.datagrid('options').view.renderedCount <= contractPlotsDatagrid.datagrid('options').pageSize) {
                    var table_target = jQuery('#contract-plots-tables')[0];
                    while(selectedRowByFilter > (contractPlotsDatagrid.datagrid('options').view.renderedCount - 1)) {
                        contractPlotsDatagrid.datagrid('options').view.getRows(table_target, function (rows) {
                            contractPlotsDatagrid.datagrid('options').view.rows = rows;
                            contractPlotsDatagrid.datagrid('options').view.populate(table_target);
                        });
                    }

                    contractPlotsDatagrid.datagrid('selectRow', select_row_index);
                }
            }
            var selectedRow = contractPlotsDatagrid.datagrid('getSelected');
            if (selectedRow === null) {
                selectedRow = select_row_index;
            } else {
                if (contractPlotsDatagrid.datagrid('options').view.renderedCount > select_row_index
                        && contractPlotsDatagrid.datagrid('options').view.renderedCount - 2 * contractPlotsDatagrid.datagrid('options').pageSize < select_row_index) {
                    selectedRow = contractPlotsDatagrid.datagrid('getRowIndex', selectedRow);
                } else {
                    selectedRow = contractPlotsDatagrid.datagrid('options').view.renderedCount - contractPlotsDatagrid.datagrid('options').pageSize;
                }
            }
            if (selectedRow < 0) {
                selectedRow = 0;
            }
            contractPlotsDatagrid.datagrid('selectRow', selectedRow);
        },
        customFooterHandler: function (target) {
            var rows = jQuery.data(target, 'datagrid').data.rows || [];
            var footer_rows = jQuery.data(target, 'datagrid').data.footer || [];

            if (rows.length === 0) {
                var contractInfo = jQuery('#contracts-tree').tree('getSelected');

                if (!contractInfo) {
                    return footer_rows;
                }

                contractType = contractInfo.attributes.c_type;

                if (contractType == ownershipContractId) {
                    jQuery('#plot-contracts-grid').show();
                    jQuery('#owners-tabs').hide();
                    initContractsFarmingGrid(0, 0, contractType);
                    initContractsOwnersGrid(0, 0, 0);
                    initPlotContractsGrid(0, 0);
                }else {
                    jQuery('#plot-contracts-grid').hide();
                    jQuery('#owners-tabs').show();
                    initContractsFarmingGrid(0, 0, contractType);
                    initContractsOwnersGrid(0, 0, 0);
                    initPlotContractsGrid(0, 0);
                }

            }

            return footer_rows;
        },
        onSelect: function (rowIndex, rowData) {
            var contractInfo = jQuery('#contracts-tree').tree('getSelected');
            if (!contractInfo) {
                return;
            }
            contractType = contractInfo.attributes.c_type;
            selectedContractId = contractInfo.id;

            const annexActionBtn = jQuery('#plot-add-remove > span > span');
            if (rowData.annex_action == 'removed') {
                jQuery(annexActionBtn[0]).text('Включване');
                jQuery(annexActionBtn[1]).attr('class', "l-btn-icon icon-tick");
            } else {
                jQuery(annexActionBtn[0]).text('Премахване');
                jQuery(annexActionBtn[1]).attr('class', "l-btn-icon icon-delete");
            }

            if (contractType == ownershipContractId) {
                jQuery('#plot-contracts-grid').show();
                jQuery('#owners-tabs').hide();

                if (rowData) {
                    initPlotContractsGrid(rowData.gid, selectedContractId);
                    initContractsOwnersGrid(0, 0, 0);
                } else {
                    initPlotContractsGrid(0);
                }
            }else {
                jQuery('#plot-contracts-grid').hide();
                jQuery('#owners-tabs').show();
                if (rowData) {
                    initContractsFarmingGrid(rowData.gid, selectedContractId, contractType);
                } else {
                    initContractsOwnersGrid(0,0,0);
                    initContractsFarmingGrid(0);
                }
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

}

function initContractsPlotsAddGrid(contract_id, c_type) {
    jQuery('#win-plots-add').window('resize', {
        height: getZoomedWindowHeight(530),
        width: 900
    });
    jQuery('#win-plots-add').window('center')

    var contractAddPlotsDatagrid = jQuery('#contracts-plots-add-tables'),
        isDatagridBound = contractAddPlotsDatagrid.data().hasOwnProperty('datagrid');

    if (isDatagridBound) {
        contractAddPlotsDatagrid.datagrid({
            rpcParams: [{
                type: 'add',
                contract_id: contract_id
            }]
        });
        return;
    }

    contractAddPlotsDatagrid.datagrid({
        iconCls: 'icon-plots',
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        pageSize: 10,
        fit: true,
        border: false,
        fitColumns: false,
        showFooter: true,
        url: 'index.php?contracts-rpc=contracts-plots-datagrid',
        rpcParams: [{
            type: 'add',
            contract_id: contract_id,
            contracts_tree_filter: ''
        }],
        sortName: 'gid',
        sortOrder: 'asc',
        idField: 'gid',
        rowStyler: function (index, row) {
            var style = [];
            if (row.is_edited == true) {
                style.push('color: #aaa');
            }

            return style.join(';');
        },
        frozenColumns: [[
            {
                field: 'ck',
                checkbox: true
            }
        ]],
        columns: [[
            {
                field: 'ekatte_name',
                title: '<b>Землище</b>',
                sortable: true,
                width: 120
            }, {
                field: 'kad_ident',
                title: '<b>Идентификатор</b>',
                sortable: true,
                width: 105
            }, {
                field: 'category',
                title: '<b>Категория</b>',
                sortable: true,
                width: 90
            }, {
                field: 'area_type',
                title: '<b>НТП</b>',
                sortable: true,
                width: 120
            }, {
                field: 'contract_area',
                title: '<b>Площ по<br/>договор (дка)</b>',
                align: 'center',
                sortable: false,
                width: 100,
                editor: {
                    type: 'numberbox',
                    options: {
                        required: true,
                        missingMessage: 'Полето е задължително',
                        precision: 3,
                    }
                },
            }, {
                field: 'document_area',
                title: '<b>Площ по<br/>документ (дка)</b>',
                align: 'center',
                sortable: true,
                width: 100
            }, {
                field: 'price_per_acre',
                title: '<b>Цена/дка</b>',
                align: 'center',
                sortable: true,
                width: 100,
                editor: {
                    type: 'numberbox',
                    options: {
                        precision: 2
                    }
                }
            }, {
                field: 'price_sum',
                title: '<b>Сума</b>',
                align: 'center',
                sortable: true,
                width: 100,
                editor: {
                    type: 'numberbox',
                    options: {
                        precision: 2
                    }
                }
            },
            {
                field: 'area_for_rent',
                title: '<b>Площ за <br/>рента (дка)</b>',
                align: 'center',
                sortable: false,
                width: 100,
                editor: {
                    type: 'numberbox',
                    options: {
                        required: true,
                        missingMessage: 'Полето е задължително',
                        precision: 3,
                    }
                }
            },
            {
                field: 'kvs_allowable_area',
                title: '<b>Обработваема <br/> площ</b>',
                align: 'center',
                sortable: false,
                width: 100,
                editor: {
                    type: 'numberbox',
                    options: {
                        required: false,
                        min:0,
                        precision: 3,
                    }
                }
            }, {
                field: 'allowable_area',
                title: '<b>Площ по <br/>сечение (дка)</b>',
                align: 'center',
                sortable: true,
                width: 100
            }, {
                field: 'area',
                title: '<b>Обща площ<br/>(дка)</b>',
                align: 'center',
                sortable: true,
                width: 100
            }, {
                field: 'rent_per_plot_txt',
                title: '<b>рента/дка</b>',
                align: 'center',
                sortable: true,
                width: 100,
            },
            {
                field: 'renta_sum',
                title: '<b>Сума</b>',
                align: 'center',
                sortable: true,
                width: 100
            }
        ]],
        pagination: true,
        rownumbers: true,
        toolbar: getAddPlotToolbar(),
        onBeforeLoad: function () {
            clearGridStyles();

            jQuery('#contracts-plots-add-tables').datagrid('clearChecked');

            var selectedContract = jQuery('#contracts-tree').tree('getSelected');

            if (selectedContract && selectedContract.attributes.c_type == 1) {
                jQuery('#contracts-plots-add-tables').datagrid('showColumn', 'price_per_acre');
                jQuery('#contracts-plots-add-tables').datagrid('showColumn', 'price_sum');
            } else {
                jQuery('#contracts-plots-add-tables').datagrid('hideColumn', 'price_per_acre');
                jQuery('#contracts-plots-add-tables').datagrid('hideColumn', 'price_sum');
            }

            if (selectedContract && (selectedContract.attributes.c_type == 2 || selectedContract.attributes.c_type == 3 || selectedContract.attributes.c_type == 5)) {
                jQuery('#contracts-plots-add-tables').datagrid('showColumn', 'area_for_rent');
            } else {
                jQuery('#contracts-plots-add-tables').datagrid('hideColumn', 'area_for_rent');
            }
        },
        onSelect: function (index) {
            jQuery('#contracts-plots-add-tables').datagrid('beginEdit', index);
        },
        onUnselect: function (index) {
            jQuery('#contracts-plots-add-tables').datagrid('cancelEdit', index);
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

/**
 * [processAddPlot handle add plot logic]
 *
 * @param   boolean  triggerAreaCalculation  [trigger logic for area ideal parts and percentage calculation ]
 *
 * @return  [void]
 */
function processAddPlot(triggerAreaCalculation = true)
{
    const getChecked = jQuery('#contracts-plots-add-tables').datagrid('getChecked');
    const contract = jQuery('#contracts-tree').tree('getSelected');
    const c_type = contract.attributes.c_type;

    if (getChecked[0]) {
        //No Rights to operate with "Договори за собственост"
        if (c_type == CONTRACT_TYPE_OWN && !hasContractsOwnWriteRights) {
            messagerContractsOwnWriteRights();
            return false;
        }

        if (triggerAreaCalculation === true) {
            resetOwnershipFields();
            jQuery('#win-add-plots-ownership-contract').window('open');
            return false;
        }

        checkedPlotsForAddition = getChecked;
        addContractPlotRelation(getChecked);
    } else {
        jQuery.messager.alert('Грешка', 'Моля изберете парцели, който да бъдат добавени към договора.');
    }
}

function getAddPlotToolbar()
{
    const documentType = jQuery('#contracts-tree').tree('getSelected').attributes.c_type;

    const toolbar = [
        {
            id: 'btnaddcontractplotrelation',
            text: 'Добавяне',
            iconCls: 'icon-add',
            handler: function () {
                processAddPlot(triggerAreaCalc = false);
            }
        },{
            id: 'btnaddcontractplotrelation',
            text: 'Добави с идеални части',
            iconCls: 'icon-add',
            handler: function () {
                processAddPlot(triggerAreaCalc = true);
            }
        },{
            id: 'btnaddcontractplotfilter',
            text: 'Филтриране',
            iconCls: 'icon-filter',
            handler: function () {
                jQuery('#win-add-plots-filter').window('open');
            }
        }, {
            id: 'btnaddcontractplotfilterclear',
            text: 'Покажи всички',
            iconCls: 'icon-clear-filter',
            handler: function () {
                clearAddPlotGridFilter();
            }
        }
    ];

    return toolbar;
}

function addContractPlotRelation(getChecked, ownershipFields) {
    'use strict';
    clearGridStyles();
    var obj = {},
        plot_data_array = [],
        is_selected_historical_plot = false,
        i,
        index,
        plot = {},
        contractsPlotsAddTables = jQuery('#contracts-plots-add-tables'),
        hasError = false;

    for (i = 0; i < getChecked.length; i++) {
        plot = {};
        let plotErrors = [];
        index = contractsPlotsAddTables.datagrid('getRowIndex', getChecked[i]);

        let row_contract_area = true;
        let row_document_area = true;
        let row_area_for_rent = true;

        let c_type = jQuery('#contracts-tree').tree('getSelected').attributes.c_type;
        
        let contract_area = contractsPlotsAddTables.datagrid('getEditor', {
            index: index,
            field: 'contract_area'
        });
        if(contract_area){
            row_contract_area = jQuery(contract_area.target).combobox('getText');
        }

        let document_area = contractsPlotsAddTables.datagrid('getEditor', {
            index: index,
            field: 'document_area'
        });
        if(document_area){
            row_document_area = jQuery(document_area.target).combobox('getText');
        }

        let area_for_rent = contractsPlotsAddTables.datagrid('getEditor', {
            index: index,
            field: 'area_for_rent'
        });

        if(area_for_rent) {
            row_area_for_rent = jQuery(area_for_rent.target).combobox('getText');
        }

        if (!row_contract_area) {
            plotErrors.push(
                {
                    index: index,
                    key: 'contract_area',
                    message: 'Площ по договор e задължителнo поле.'
                }
            );
        }

        if (!row_document_area) {
            plotErrors.push(
                {
                    index: index,
                    key: 'document_area',
                    message: 'Площ по документ e задължителнo поле.'
                }
            );
        }

        if (!row_area_for_rent) {
            plotErrors.push(
                {
                    index: index,
                    key: 'area_for_rent',
                    message: 'Площ за рента e задължителнo поле.'
                }
            );
        }

        contractsPlotsAddTables.datagrid('endEdit', index);

        if (parseFloat(getChecked[i].document_area) < parseFloat(getChecked[i].contract_area)) {
            plotErrors.push(
                {
                    index: index,
                    key: 'contract_area',
                    message: TF.Rpc.ExceptionsList.WRONG_PLOT_AREA.message
                }
            );
        }


        if((c_type == 2 || c_type == 3 || c_type == 5) && parseFloat(getChecked[i].document_area) < parseFloat(getChecked[i].area_for_rent)) {
            plotErrors.push(
                {
                    index: index,
                    key: 'area_for_rent',
                    message: TF.Rpc.ExceptionsList.WRONG_PLOT_AREA_FOR_RENT.message
                }
            );
        }

        if((c_type == 2 || c_type == 3 || c_type == 5) && parseFloat(getChecked[i].contract_area) < parseFloat(getChecked[i].area_for_rent)) {
            plotErrors.push(
                {
                    index: index,
                    key: 'area_for_rent',
                    message: TF.Rpc.ExceptionsList.WRONG_PLOT_AREA_FOR_RENT_TOO_LARGE.message
                }
            );
        }
        
        plot.plot_id = getChecked[i].gid;
        plot.contract_area = getChecked[i].contract_area;
        plot.document_area = getChecked[i].document_area;
        plot.area_for_rent = getChecked[i].area_for_rent;
        plot.price_per_acre = (getChecked[i].price_per_acre === "-")
            ? ""
            : getChecked[i].price_per_acre;
        plot.price_sum = (getChecked[i].price_sum === "-")
            ? ""
            : getChecked[i].price_sum;
        plot.kvs_allowable_area = getChecked[i].kvs_allowable_area;

        if (getChecked[i].is_edited == true && is_selected_historical_plot == false) {
            is_selected_historical_plot = true;
        }

        if (plotErrors.length > 0) {
            hasError = true
        }
        plot.errors = plotErrors;
        plot_data_array.push(plot);
    }

    obj.plot_data_array = plot_data_array;
    const contract = jQuery('#contracts-tree').tree('getSelected');
    obj.contract_id = contract.id;
    obj.contract_start_date = contract.attributes.start_date_db_format;
    obj.contract_due_date = contract.attributes.due_date_db_format;
    obj.is_annex = contract.attributes.is_annex;
    obj.annex_parent_id = contract.attributes.parent_id;
    obj.ownership = ownershipFields;

    if (hasError === true) {
        showPlotValidationFailedModal(obj.plot_data_array);
        return;
    }

    if (is_selected_historical_plot == true) {
        jQuery.messager.confirm('Потвърждение', 'Избраните имоти включват исторически имоти,' +
            ' които не са част от актуалната КВС/КК. Желаете ли да продължите?', function (r) {
                if (r) {
                    TF.Rpc.Contracts.ContractsPlotsGrid.addContractPlotRelation(obj)
                        .done(function () {
                            jQuery('#win-contracts-add').window('close');
                            jQuery('#contract-plots-tables').datagrid('loadRpc');
                        })
                        .fail(function (errorObj) {
                            if (errorObj.is(TF.Rpc.ExceptionsList.CONTRACT_AREA_EXCEEDS_PLOT_AREA)) {
                                jQuery('#add-contracts-tables').datagrid('clearChecked');
                                addPlotsToContractResult(errorObj);
                            } else {
                                jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
                            }
                        });

                    jQuery('#win-plots-add').window('close');
                    jQuery('#win-add-plots-ownership-contract').window('close');

                    jQuery('#contracts-plots-add-tables').datagrid('uncheckAll');
                    jQuery('#contracts-plots-add-tables').datagrid('unselectAll');

                    jQuery('#contract-plots-tables').datagrid('reload');
                    jQuery('#contracts-plots-add-tables').datagrid('reload');
                }
            });
    } else {
        TF.Rpc.Contracts.ContractsPlotsGrid.addContractPlotRelation(obj)
            .done(function () {
                jQuery('#win-contracts-add').window('close');
                jQuery('#contract-plots-tables').datagrid('loadRpc');
            })
            .fail(function (errorObj) {
                if (errorObj.is(TF.Rpc.ExceptionsList.CONTRACT_AREA_EXCEEDS_PLOT_AREA)) {
                    jQuery('#add-contracts-tables').datagrid('clearChecked');
                    addPlotsToContractResult(errorObj);
                } 
                else if (errorObj.is(TF.Rpc.ExceptionsList.CONTRACTS_HAS_PAYMENTS_EXCEPTION)) {        
                    jQuery('#win-has-payments-error').window('open');  
                    initUnsuccessfullyEditContract(
                        errorObj.getOriginalMessage()
                    );
                    jQuery('#win-plots-add').window('close');
                } else if (errorObj.is(TF.Rpc.ExceptionsList.CONTRACT_PLOT_DUPLICATION)) {
                    jQuery('#win-copy-filtered-contracts-plots-error').window('open');  
                    initUnsuccessfullyCopiedContracts(
                        errorObj.getOriginalMessage()
                    );
                    return false;               
                }
                else {
                    jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
                }
            });

        jQuery('#win-add-plots-ownership-contract').window('close');

        jQuery('#contracts-plots-add-tables').datagrid('uncheckAll');
        jQuery('#contracts-plots-add-tables').datagrid('unselectAll');

        jQuery('#contract-plots-tables').datagrid('reload');
        jQuery('#contracts-plots-add-tables').datagrid('reload');
    }
}

function showPlotValidationFailedModal(plots)
{
    const plotErrors = plots.map((plot) => plot.errors);

    const messages = plots
        .map( function (plot) { return plot.errors.map(( (error) => error.message )) });

    const uniqueMsg =  [...new Set(Array.prototype.concat.apply([], [...messages]))];

    const contractsPlotsAddTables = jQuery('#contracts-plots-add-tables');

    plotErrors.forEach(function(errors) {
        errors.forEach(function(error) {
            let col = contractsPlotsAddTables.datagrid('getColumnOption',error.key);
            if (col) {
                col.styler = function(value,row,index){
                    if (index == error.index) {
                        return 'background-color:#ff7f6b';
                    }
                };
            }
            contractsPlotsAddTables.datagrid('refreshRow',error.index);
        });
    });


    jQuery.messager.alert(
        'Грешка',
        'Оцветените в червено имоти не отговарят на следните изисквания: </br></br></br>'+ uniqueMsg.join(' </br>'),
        'warning'
    )
}

function clearGridStyles()
{
    const validationColNames = [
        'allowable_area', 'contract_area', 'document_area', 'kvs_allowable_area', 'area_for_rent'
    ];
    const contractsPlotsAddTables = jQuery('#contracts-plots-add-tables');
    validationColNames.forEach(function(elementName) {
        let col = contractsPlotsAddTables.datagrid('getColumnOption',elementName);
        col.styler = function (value,row,index) {
                return 'background-color:';           
        };
    });
}


function initAddPlotOwnershipContractChecks() {
    jQuery('#contract-ownership-fraction > input').change(function () {
        jQuery('#contract-ownership-percent-field').hide();
        jQuery('#contract-ownership-fraction-fields').show();
    });
    jQuery('#contract-ownership-percent > input').change(function () {
        jQuery('#contract-ownership-fraction-fields').hide();
        jQuery('#contract-ownership-percent-field').show();
    });

    jQuery("input[name='ownership-type']").on("click", function () {
        jQuery('#contract-ownership-fraction-fields').hide();
        jQuery('#contract-ownership-percent-field').hide();
    
        jQuery("input[name='ownership-type']").on("click", function () {
            var selectedValue = jQuery(this).attr('id');
    
            if (selectedValue === 'contract-ownership-fraction-radio') {
                jQuery('#contract-ownership-percent-field').hide();
                jQuery('#contract-ownership-fraction-fields').show();
            } else if (selectedValue === 'contract-ownership-percent-radio') {
                jQuery('#contract-ownership-fraction-fields').hide();
                jQuery('#contract-ownership-percent-field').show();
            }
        });
    });

    jQuery('#ownership-nomerator > input').numberbox({
        value: '',
        min: 0,
        decimalSeparator: '',
        required: true,
        missingMessage: 'Моля въведете стойност за числител.'
    });
    jQuery('#ownership-denominator > input').numberbox({
        value: '',
        min: 1,
        decimalSeparator: '',
        required: true,
        missingMessage: 'Моля въведете стойност за знаменател.'
    });
    jQuery('#contract-ownership-percent-field > input').numberbox({
        value: '',
        min: 0,
        max: 100,
        precision: 2,
        required: true,
        missingMessage: 'Моля въведете процент собственост.'
    });
}

function onClickSavePlotOwnCont() {
    var contract_ownership_fraction_radio = jQuery('#contract-ownership-fraction-radio'),
        contract_ownership_percent_radio = jQuery('#contract-ownership-percent-radio');

    jQuery("#btn-save-plots-ownership-contract").on("click", function () {
        var getChecked = jQuery('#contracts-plots-add-tables').datagrid('getChecked'),
            obj = {},
            denominator,
            nomerator,
            nomeratorVal,
            denominatorVal,
            fraction,
            fractionVal,
            percent,
            percentVal;

        if (contract_ownership_fraction_radio.is(':checked')) {
            denominator = jQuery('#ownership-denominator-input');
            nomerator = jQuery('#ownership-nomerator-input');

            if (!denominator.numberbox('isValid') && !nomerator.numberbox('isValid')) {
                jQuery.messager.alert('Грешка', 'Моля въведете стойности за числител и знаменател.', 'error');
                return false;
            }
            if (!denominator.numberbox('isValid')) {
                jQuery.messager.alert('Грешка', 'Моля въведете стойност за знаменател.', 'error');
                return false;
            }
            if (!nomerator.numberbox('isValid')) {
                jQuery.messager.alert('Грешка', 'Моля въведете стойност за числител.', 'error');
                return false;
            }

            nomeratorVal = nomerator.numberbox('getValue');
            denominatorVal = denominator.numberbox('getValue');

            if (parseInt(denominatorVal, 10) < parseInt(nomeratorVal, 10)) {
                jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.WRONG_FRACTION.message, 'error');
                return false;
            }

            fraction = new Fraction(nomeratorVal / denominatorVal);
            fractionVal = fraction.simplify().toFraction();

            obj.fraction = fractionVal;
            obj.percent = (nomeratorVal / denominatorVal) * 100;
        } else if (contract_ownership_percent_radio.is(':checked')) {
            percent = jQuery('#ownership-percent-field');

            if (!percent.numberbox('isValid')) {
                jQuery.messager.alert('Грешка', 'Моля въведете процент собственост.', 'error');
                return false;
            }

            percentVal = percent.numberbox('getValue');
            fraction = new Fraction(percentVal / 100);
            fractionVal = fraction.simplify().toFraction();

            obj.fraction = fractionVal;
            obj.percent = percentVal;
        }

        addContractPlotRelation(getChecked, obj);
    });
}

function filterAddPlotGrid() {
    if (jQuery('#search-plot-ekate > input').combobox('getValue')
            || jQuery('#search-plot-masiv > input').val().length != 0
            || jQuery('#search-plot-number > input').val().length != 0
            || jQuery('#search-plot-kad-idents').val().length != 0)
    {
        let kadIdents = jQuery('#search-plot-kad-idents').val().trim().split("\n");
        if(kadIdents.length > 50) {
            jQuery.messager.alert('Грешка', 'Не може да търсите по повече от 50 идентификатора');
            return false;
        }

        jQuery('#contracts-plots-add-tables').datagrid({
            rpcParams: [{
                type:'add',
                contract_id: jQuery('#contracts-tree').tree('getSelected').id,
                ekate: jQuery('#search-plot-ekate > input').combobox('getValue'),
                masiv: jQuery('#search-plot-masiv > input').val(),
                number: jQuery('#search-plot-number > input').val(),
                kad_idents: kadIdents
            }]
        });
        jQuery('#win-add-plots-filter').window('close');
        return true;
    } else {
        jQuery.messager.alert('Грешка', 'Моля въведете параметри за филтриране.');
        return false;
    }
}

function clearAddPlotGridFilter() {
    jQuery('#search-plot-ekate > input').combobox('reset');
    jQuery('#search-plot-masiv > input').val('');
    jQuery('#search-plot-number > input').val('');
    jQuery('#search-plot-kad-idents').val('');
    jQuery('#contracts-plots-add-tables').datagrid({
        rpcParams: [{
            type:'add',
            contract_id: jQuery('#contracts-tree').tree('getSelected').id,
        }]
    });
}

function initFillContractInfo(data) {
    jQuery('#info-contract-type').html(data['nm_usage_rights']);
    jQuery('#info-contract-number').html(data['c_num']);
    jQuery('#info-contract-date').html(data['c_date']);
    jQuery('#info-start-date').html(data['start_date']);
    jQuery('#info-due-date').html(data['due_date']);
    jQuery('#info-farming').html(data['farming']);
    jQuery('#info-ds-type').html(data['ds_type']);
    jQuery('#info-ds-num').html(data['ds_num_date']);
    jQuery('#info-sv-num').html(data['sv_num']);
    jQuery('#info-sv-date').html(data['sv_date']);
    jQuery('#info-renta').html(data['renta']);
    jQuery('#info-renta-nat-type').html(data['renta_nat_type']);
    jQuery('#info-renta-nat').html(data['renta_nat']);
    jQuery('#info-cont-name').html(data['contragent_name']);
    jQuery('#info-cont-number').html(data['contragent_number']);
    jQuery('#info-cont-land').html(data['contragent_land']);
    jQuery('#info-cont-address').html(data['contragent_address']);
}

function initFillSubleasedContractInfo(data) {
    jQuery('#sublease-info-contract-type').html(data['nm_usage_rights']);
    jQuery('#sublease-info-contract-number').html(data['c_num']);
    jQuery('#sublease-info-contract-date').html(data['c_date']);
    jQuery('#sublease-info-start-date').html(data['start_date']);
    jQuery('#sublease-info-due-date').html(data['due_date']);
    jQuery('#sublease-info-farming').html(data['farming']);
    jQuery('#sublease-info-ds-type').html(data['ds_type']);
    jQuery('#sublease-info-ds-num').html(data['ds_num_date']);
    jQuery('#sublease-info-sv-num').html(data['sv_num']);
    jQuery('#sublease-info-sv-date').html(data['sv_date']);
    jQuery('#sublease-info-renta').html(data['renta']);
    jQuery('#sublease-info-renta-nat-type').html(data['renta_nat_type']);
    jQuery('#sublease-info-renta-nat').html(data['renta_nat']);
    jQuery('#sublease-info-cont-name').html(data['contragent_name']);
    jQuery('#sublease-info-cont-number').html(data['contragent_number']);
    jQuery('#sublease-info-cont-land').html(data['contragent_land']);
    jQuery('#sublease-info-cont-address').html(data['contragent_address']);
}

function addPlotsToContractResult(errorObj) {

    if (errorObj.is(TF.Rpc.ExceptionsList.CONTRACT_AREA_EXCEEDS_PLOT_AREA)) {
        initContractsPlotsConfirmationGrid();
        initSubleasesPlotsConfirmationGrid();
        jQuery('#win-confirm-plots').window('open');
    };
    //No Rights to operate with "Договори за собственост"
    if(contractType == CONTRACT_TYPE_OWN && !hasContractsOwnWriteRights) {
        messagerContractsOwnWriteRights();
        return false;
    }
}

function confirmContractPlotRelation(){
    TF.Rpc.Contracts.ContractsPlotsGrid.addConfirmedContractPlotRelation()
    .done(function (data) {
        jQuery('#win-confirm-plots').window('close');
        jQuery('#contract-plots-tables').datagrid('loadRpc');
        jQuery('#contracts-plots-add-tables').datagrid('loadRpc');
    })
    .fail(function (errorObj) {

    });
}

function initEditPlotDataFields(sender) {
    var result = sender.transport.response.split('X-PRADO-DATA')[1].substring(3).replace('<!--//', '').evalJSON();
    totalContractArea = result;

    var getChecked = jQuery('#contract-plots-tables').datagrid('getChecked');

    jQuery('#edit-contract-area > input').numberbox({
        value: getChecked[0].contract_area,
        min: 0.001,
        precision: 3,
        required: true
    });
    jQuery('#edit-document-area > input').numberbox({
        value: getChecked[0].document_area,
        min: 0.001,
        precision: 3,
        required: true,
        disabled: true
    });
    jQuery('#edit-worked-area > input').numberbox({
        value: getChecked[0].kvs_allowable_area,
        min: 0.000,
        precision: 3,
        required: false
    });

    jQuery('#win-edit-contract-plot-data').window('open');
    jQuery('#edit-contract-area > input').numberbox('validate');
    jQuery('#edit-document-area > input').numberbox('validate');
    jQuery('#edit-worked-area > input').numberbox('validate');
}

/**
 * Return formatted Y-m-d
 * 
 */
formatYMD = (date) => {
    return date.getFullYear() +'-'+ (date.getMonth() + 1) +'-'+ date.getDate() 
}

/**
 * 
 * Returns d.m.Y to Y.m.d
 */
 transformDMYtoYMD = (date) => {
    const datePartsArr = date.split('.');
    
    return datePartsArr[2]+'.'+datePartsArr[1]+'.'+datePartsArr[0];
}

function validateEditPlotAreas() {
    if (jQuery('#edit-contract-area > input').numberbox('isValid')
        && jQuery('#edit-document-area > input').numberbox('isValid')
        && jQuery('#edit-worked-area > input').numberbox('isValid')) {

        if(parseFloat(jQuery('#edit-contract-area > input').numberbox('getValue')) <= parseFloat(jQuery('#edit-document-area > input').numberbox('getValue')))
        {
            if(jQuery('#edit-area-for-rent > input').numberbox('options').disabled == true
                || (jQuery('#edit-area-for-rent > input').numberbox('options').disabled == false
                    && parseFloat(jQuery('#edit-area-for-rent > input').numberbox('getValue')) <= parseFloat(jQuery('#edit-document-area > input').numberbox('getValue'))
                    && parseFloat(jQuery('#edit-area-for-rent > input').numberbox('getValue')) <= parseFloat(jQuery('#edit-contract-area > input').numberbox('getValue'))
                )
            ){

                var getChecked = jQuery('#contract-plots-tables').datagrid('getChecked');
                var contractData = jQuery('#contracts-tree').tree('getSelected');
                let contractStartDate = new Date(transformDMYtoYMD(contractData.attributes.start_date));
                let contractDueDate   = new Date(transformDMYtoYMD(contractData.attributes.due_date));

                var obj = {};
                obj.contract_id = contractData.id;
                obj.plot_id = getChecked[0].gid;
                obj.contract_area = jQuery('#edit-contract-area > input').numberbox('getValue');
                obj.document_area = jQuery('#edit-document-area > input').numberbox('getValue');
                obj.kvs_allowable_area = jQuery('#edit-worked-area > input').numberbox('getValue');
                obj.category = jQuery('#edit-category-contracts').combobox('getValue');
                obj.ntp = jQuery('#edit-ntp-contracts').combobox('getValue');
                obj.mestnost = jQuery('#edit-mestnost').val();
                obj.c_type = contractData.attributes.c_type;
                obj.contractStartDate = formatYMD(contractStartDate);
                obj.contractDueDate = formatYMD(contractDueDate);
                obj.farmingId = contractData.attributes.farming_id;

                if (obj.c_type == CONTRACT_TYPE_OWN) {
                    obj.price_per_acre = jQuery('#price-per-acre > input').val();
                } else {
                    if(contractData.attributes.overall_renta == null) {
                        obj.rent_per_plot = jQuery('#price-per-acre > input').val();
                    }
                }

                obj.area_for_rent = jQuery('#edit-area-for-rent > input').numberbox('getValue');
                obj.comment = tinyMCE.get('edit-plot-comment-contracts').getContent();

                TF.Rpc.Contracts.EditPlotAreas.saveEditPlotAreas(obj)
                .done(function (data) {
                    jQuery('#win-edit-contract-plot-data').window('close');
                    jQuery('#contract-plots-tables').datagrid('reload');
                })
                .fail(function (errorObj)
                {
                    if(errorObj.is(TF.Rpc.ExceptionsList.CONTRACT_AREA_EXCEEDS_PLOT_AREA)) {                      
                       return jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.CONTRACT_AREA_EXCEEDS_PLOT_AREA.message, 'error');                     
                    }

                    if(errorObj.is(TF.Rpc.ExceptionsList.CONTRACT_PERIOD_OVERLAP_WITH_ANNEX)) {                      
                        return jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.CONTRACT_PERIOD_OVERLAP_WITH_ANNEX.message, 'error');                     
                    }

                    if (errorObj.is(TF.Rpc.ExceptionsList.CONTRACTS_HAS_PAYMENTS_EXCEPTION)) {        
                        jQuery('#win-has-payments-error').window('open');  
                        initUnsuccessfullyEditContract(
                            errorObj.getOriginalMessage()
                        );
                        return false;
                    }

                    if (errorObj.is(TF.Rpc.ExceptionsList.CONTRACT_PLOT_DUPLICATION)) {        
                        jQuery('#win-copy-filtered-contracts-plots-error').window('open');  
                        initUnsuccessfullyCopiedContracts(
                            errorObj.getOriginalMessage()
                        );
                        return false;
                    }

                    if (errorObj.is(TF.Rpc.ExceptionsList.CONTRACTS_HAS_SOLD_PLOTS_EXCEPTION)) {        
                        jQuery('#win-sold-contracts-plots-error').window('open');  
                        initSoldPlotsContractGrid(
                            errorObj.getOriginalMessage()
                        );
                        return false;
                    }

                    return jQuery.messager.alert('Грешка', 'Възникна грешка при редактиране на имот');
                });

                return true;
            } else {
                jQuery.messager.alert('Грешка', 'Въведената площ за рента е по-голяма от максимално допустимата.', 'error');
                return false;
            }
        }
        else
        {
            jQuery.messager.alert('Грешка', 'Въведената площ по договор е по-голяма от максимално допустимата.', 'error');
            return false;
        }
    } else {
        jQuery.messager.alert('Грешка', 'Моля попълнете задължителните полета.', 'error');
        return false;
    }
}

function filterContractPlotsGrid() {
    jQuery('#contract-plots-tables').datagrid({
        rpcParams: [{
            type:'view',
            contract_id: jQuery('#contracts-tree').tree('getSelected').id,
            kad_ident: jQuery('#search-contract-plot-kad-ident > input').val(),
            ekate: jQuery('#search-contract-plot-ekate > input').combobox('getValue'),
            category: jQuery('#search-contract-plot-category > input').combobox('getValues'),
            masiv: jQuery('#search-contract-plot-masiv > input').val(),
            number: jQuery('#search-contract-plot-number > input').val(),
        }],
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    jQuery('#win-contract-plot-filter').window('close');
}

function filterClearContractPlotsGrid() {
    jQuery('#search-contract-plot-category > input').combobox('reset');
    jQuery('#search-contract-plot-ekate > input').combobox('reset');
    jQuery('#search-contract-plot-masiv > input').val('');
    jQuery('#search-contract-plot-number > input').val('');
    jQuery('#search-contract-plot-kad-ident > input').val('');

    jQuery('#contract-plots-tables').datagrid({
        rpcParams: [{
            type:'view',
            contract_id: jQuery('#contracts-tree').tree('getSelected').id,
            kad_ident: null,
            category: null,
            ekate: null,
            masiv: null,
            number: null,
        }],
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function getSelectedPlotByFilter() {
    var rows = jQuery('#contract-plots-tables').datagrid('getRows');
    for(var i = 0; i < rows.length; i++)
    {
        if(rows[i]['selected']!= undefined && rows[i]['selected'] == true)
        {
            return i;
        }
    }
    return 0;
}

function resetOwnershipFields() {
    jQuery('#contract-ownership-fraction-radio').prop('checked', false);
    jQuery('#contract-ownership-percent-radio').prop('checked', false);
    jQuery('#contract-ownership-percent-field').hide();
    jQuery('#contract-ownership-fraction-fields').hide();
    jQuery('#ownership-nomerator > input').numberbox('reset');
    jQuery('#ownership-denominator > input').numberbox('reset');
    jQuery('#contract-ownership-percent-field > input').numberbox('reset');
}

function saveContractPrice() {
    'use strict';

    var price = jQuery('#contract-price-input-field').val(),
        selectedContract = jQuery('#contracts-tree').tree('getSelected').id,
        singlePrice;
    if (price < 0) {
        jQuery.messager.alert('Грешка', 'Не може да въведете отрицателна стойност за цена на договор', 'warning');
        return;
    }

    TF.Rpc.Contracts.ContractsPlotsGrid.saveContractPrice(selectedContract, price).done(function (data) {
        singlePrice = parseFloat(data).toFixed(2);
        jQuery('#single-plot-price').html(singlePrice);
        jQuery('#contract-plots-tables').datagrid('loadRpc');
        jQuery('#win-contract-price').window('close');
    })
    .fail(function (errorObj) {
        jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
    });

}

function populateContractPriceGrid() {
    'use strict';

    var selectedContract = jQuery('#contracts-tree').tree('getSelected'),
        singlePrice;

    if (selectedContract.attributes.c_type !== 1) {
        jQuery.messager.alert('Съобщение', 'Може да задавате цена на договор само на договор от тип "Собственост."', 'info');
        return false;
    }
    TF.Rpc.Contracts.ContractsPlotsGrid.getContractPrice(selectedContract.id).done(function (data) {
        singlePrice = parseFloat(data.single_price).toFixed(2);

        if (isNaN(singlePrice)) {
            singlePrice = 0;
        }

        jQuery('#contract-price-input-field').val(data.contract_price);
        jQuery('#single-plot-price').html(singlePrice);
        jQuery('#win-contract-price').window('open');
    })
    .fail(function (errorObj) {
        jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
    });
}

function addPlotToContract() {
    if (!hasPlotRightsRW) {
        messagerPlotsWriteRights();
        return false;
    }
    var contractData = jQuery('#contracts-tree').tree('getSelected');
    //No Rights to operate with "Договори за собственост"
    if(contractData.attributes.c_type == CONTRACT_TYPE_OWN && !hasContractsOwnWriteRights) {
        messagerContractsOwnWriteRights();
        return false;
    }
    if(contractData.attributes.is_closed_for_editing == true) {
        jQuery.messager.alert('Грешка', 'Договора е заключен за редакция.', 'warning');
        return false;
    }

    var contractSelected = jQuery('#contracts-tree').tree('getSelected');

    if (contractSelected.attributes.from_sublease > 0) {
        messageContractIsFromSublease(contractSelected);
        return false;
    }
    if (contractSelected) {
        if(contractSelected.attributes.hasPayment){
            jQuery.messager.confirm('Потвърждение', "По този договор са извършени плащания. Промените ще се отразят в справката за изплатени ренти!", function(r) {
                if(r){
                    jQuery('#win-plots-add').window('open');
                    initContractsPlotsAddGrid(contractSelected.id, contractType);
                }
            });
        }else{
            jQuery('#win-plots-add').window('open');
            initContractsPlotsAddGrid(contractSelected.id, contractType);
        }

    } else {
        jQuery.messager.alert('Грешка', 'Не е избран договор');
    }
}

function editContractPlotRelation() {
    if (!hasPlotRightsRW) {
        messagerPlotsWriteRights();
        return false;
    };
    var getChecked = jQuery('#contract-plots-tables').datagrid('getChecked');
    var contractData = jQuery('#contracts-tree').tree('getSelected');
    //No Rights to operate with "Договори за собственост"
    if(contractData.attributes.c_type == CONTRACT_TYPE_OWN && !hasContractsOwnWriteRights) {
        messagerContractsOwnWriteRights();
        return false;
    }

    if (contractData.attributes.from_sublease > 0) {
        messageContractIsFromSublease(contractData);
        return false;
    }

    if(contractData.attributes.is_closed_for_editing == true) {
        jQuery.messager.alert('Грешка', 'Договора е заключен за редакция.', 'warning');
        return false;
    }

    if (getChecked[0]) {

        //No Rights to operate with "Договори за собственост"
        if(contractData.attributes.c_type == CONTRACT_TYPE_OWN && !hasContractsOwnWriteRights) {
            messagerContractsOwnWriteRights();
            return false;
        }

        var obj = new Object();
        var contract_id = contractData.id;
        var plot_id = getChecked[0].gid;
        TF.Rpc.Contracts.EditPlotAreas.markForEditPlotAreas(contract_id, plot_id)
        .done(function(data) {

            //No Rights to operate with "Договори за собственост"
            if(data.contractType == CONTRACT_TYPE_OWN && !data.hasContractsOwnWriteRights) {
                messagerContractsOwnWriteRights();
                return false;
            }

            totalContractArea = data.total_contract_area;

            var getChecked = jQuery('#contract-plots-tables').datagrid('getChecked');

            jQuery('#edit-contract-area > input').numberbox({
                value: getChecked[0].contract_area,
                min: 0.001,
                precision: 3,
                required: true
            });
            jQuery('#edit-document-area > input').numberbox({
                value: getChecked[0].document_area,
                min: 0.001,
                precision: 3,
                required: true,
                disabled: true
            });
            jQuery('#edit-worked-area > input').numberbox({
                value: getChecked[0].kvs_allowable_area,
                min: 0.000,
                precision: 3,
                required: true
            });

            jQuery('#price-per-acre > input').numberbox({
                value: getChecked[0].price_per_acre,
                min: 0.00,
                precision: 2,
                required: false
            });
            // price_per_acre
            if(contractData.attributes.c_type == CONTRACT_TYPE_OWN)
            {
                jQuery('#price-per-acre > input').numberbox('enable');
            }else{
                if(contractData.attributes.overall_renta != null) {
                    jQuery('#price-per-acre > input').numberbox('disable');
                }
                else {
                    jQuery('#price-per-acre > input').numberbox('enable');
                }
                jQuery('#price-per-acre > input').numberbox('setValue', getChecked[0].rent_per_plot);
            }


            if(contractData.attributes.c_type == 2 || contractData.attributes.c_type == 3 || contractData.attributes.c_type == 5){
                jQuery('#edit-area-for-rent > input').numberbox({
                    value: getChecked[0].area_for_rent,
                    min: 0.00,
                    precision: 3,
                    required: true
                });
                jQuery('#edit-area-for-rent > input').numberbox('enable');
            } else {
                jQuery('#edit-area-for-rent > input').numberbox({
                    value: null,
                    min: 0.00,
                    precision: 3,
                    required: false
                });
                jQuery('#edit-area-for-rent > input').numberbox('disable');
            }

            if(!data.category || data.category == '' || data.category == '0') {
                data.category = '-1';
            }

            if(contractData.attributes.hasPayment){

                jQuery.messager.confirm('Потвърждение', "По този договор са извършени плащания. Промените ще се отразят в справката за изплатени ренти!", function(r) {
                    if(r){
                        initSelectedValues(data);
                    }
                });
                win.find('.messager-icon').removeClass('messager-question').addClass('messager-warning');
            }else{
                initSelectedValues(data);
            }

            if (tinymce.EditorManager.editors['edit-plot-comment-contracts'] != undefined) {
                tinymce.EditorManager.editors['edit-plot-comment-contracts'].getBody().innerHTML = getChecked[0].comment;
            }

        });
    } else {
        jQuery.messager.alert('Грешка', 'Моля изберете имот, който искате да редактирате!');
    }
}

function addOrDeletePlot() {
    const contractData = jQuery('#contracts-tree').tree('getSelected');
    const plotData = jQuery('#contract-plots-tables').datagrid('getSelected');

    if (contractData.attributes.is_annex == false) {
        return deleteContractPlotRelation();
    }

    if (plotData.annex_action == 'removed') {
        return activateRemovedPlot(
            jQuery('#contract-plots-tables'),
            jQuery('#contracts-tree')
        );
    }

    return markPlotAsRemoved(
        jQuery('#contract-plots-tables'),
        jQuery('#contracts-tree')
    );
}



function deleteContractPlotRelation() {
    if (!hasPlotRightsRW) {
        messagerPlotsWriteRights();
        return false;
    };
    var getChecked = jQuery('#contract-plots-tables').datagrid('getChecked');
    var contractData = jQuery('#contracts-tree').tree('getSelected');
    //No Rights to operate with "Договори за собственост"
    if(contractData.attributes.c_type == CONTRACT_TYPE_OWN && !hasContractsOwnWriteRights) {
        messagerContractsOwnWriteRights();
        return false;
    }

    if(contractData.attributes.is_closed_for_editing == true) {
        jQuery.messager.alert('Грешка', 'Договора е заключен за редакция.', 'warning');
        return false;
    }

    if (contractData) {

        if (contractData.attributes.from_sublease > 0) {
            messageContractIsFromSublease(contractData);
            return false;
        }
        if(contractData.attributes.hasPayment){
            var confurm_text = "По този договор са извършени плащания. Промените ще се отразят в справката за изплатени ренти!" +
                'Сигурни ли сте, че искате да премахнете този имот?';
        }else{
            var confurm_text = 'Сигурни ли сте, че искате да премахнете този имот?';
        }
        if (getChecked[0]) {
            jQuery.messager.confirm('Потвърждение', confurm_text, function(r) {
                if (r) {
                    deletePlotFromContract(getChecked[0].gid, contractData.id)
                }
            });
        } else
        {
            jQuery.messager.alert('Грешка', 'Моля изберете имот, който искате да премахнете!');
        }
    } else {
        jQuery.messager.alert('Грешка', 'Не е избран договор!');
    }
}

function deletePlotFromContract(contract_id, plot_id, confirm = false) {
    TF.Rpc.Contracts.ContractsPlotsGrid.deleteContractPlotRelation(contract_id, plot_id, confirm)
        .done(function (data) {
            jQuery('#contract-plots-tables').datagrid('loadRpc');
            jQuery('#contract-plots-tables').datagrid('uncheckAll');
        })
        .fail(function (errorObj) {
            if (errorObj.getCode() == -33215) {
                jQuery.messager.alert('Грешка', errorObj.getOriginalMessage(),'warning');
            } 
            else if (errorObj.is(TF.Rpc.ExceptionsList.CONTRACTS_HAS_PAYMENTS_EXCEPTION)) {        
                jQuery('#win-has-payments-error').window('open');  
                initUnsuccessfullyEditContract(
                    errorObj.getOriginalMessage()
                );
            } else if (errorObj.is(TF.Rpc.ExceptionsList.CONTRACT_TYPE_MODIFY_DENIED_SALES_RELATION)) {
                jQuery.messager.alert('Грешка', generateSalesContractExistMsg(errorObj.getOriginalMessage()),'error');
                return false;
            } else {
                jQuery.messager.alert('Грешка', errorObj.getMessage(),'warning');
            }
        });
}


function displayPlotInformation() {
    var getChecked = jQuery('#contract-plots-tables').datagrid('getChecked');
    if (getChecked[0]) {

        window.open("index.php?page=Plots.Home&plot_id=" + getChecked[0].gid, '_blank');
    } else {
        jQuery.messager.alert('Грешка', 'Моля изберете имот, за който да бъде показана информация.');
    }
}

function initSelectedValues(data) {
    jQuery('#win-edit-contract-plot-data').window('open');
    jQuery('#edit-contract-area > input').numberbox('validate');
    jQuery('#edit-document-area > input').numberbox('validate');
    jQuery('#edit-worked-area > input').numberbox('validate');
    jQuery('#edit-category-contracts').combobox('select', data.category);
    jQuery('#edit-mestnost').val(data.mestnost);

    const ntpList = jQuery('#edit-ntp-contracts').combobox('getData')
    for(let i = 0; i < ntpList?.length; i++) {
        const ntp = ntpList[i];

        if (ntp.id == data.area_type) {
            jQuery('#edit-ntp-contracts').combobox('select', ntp.id);
            break;
        }

        if (ntp.additionalCodes?.includes(data.area_type)) {
            jQuery('#edit-ntp-contracts').combobox('select', ntp.id);
            break;
        }
    }
    

}
