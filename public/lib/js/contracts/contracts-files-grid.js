/*jslint browser: true*/
/*global jQuery, EasyUIRPCLoaders.EasyUIGridCustomLoader, TF,bufferview, Fraction, RpcErrorHandler, hasPlotRightsRW, messagerPlotsWriteRights, CONTRACT_TYPE_OWN, hasContractsOwnWriteRights, messagerContractsOwnWriteRights */

function initFilesGrid(contract_id) {
    var contractsFilesGrid = jQuery('#contract-files-tables'),
        isDatagridBound = contractsFilesGrid.data().hasOwnProperty('datagrid');

    if (isDatagridBound) {
        if (contract_id !== 0) {
            contractsFilesGrid.datagrid({
                url: 'index.php?contracts-rpc=contracts-files-maingrid',
                rpcParams: [{
                    contract_id: contract_id
                }],
            });
        } else {
            contractsFilesGrid.datagrid('loadData', {rows: [], total: 0});
        }
        return;
    }

    contractsFilesGrid.datagrid({
        nowrap: true,
        autoRowHeight: true,
        striped: true,
        fit: true,
        fitColumns: true,
        showFooter: false,
        url: 'index.php?contracts-rpc=contracts-files-maingrid',
        rpcParams: [{
            contract_id: contract_id
        }],
        idField: 'id',
        pageSize: 10,
        sortName: 'id',
        sortOrder: 'desc',
        singleSelect: true,
        frozenColumns: [[
            {
                field: 'ck',
                checkbox: true
            }
        ]],
        columns: [[
            {
                field: 'filename',
                title: '<b>Файл</b>',
                sortable: false,
                width: 100
            }, {
                field: 'date',
                title: '<b>Дата</b>',
                sortable: false,
                width: 100
            }
        ]],
        rownumbers: true,
        toolbar: [{
            id: 'btnaddcontractfile',
            text: 'Добавяне',
            iconCls: 'icon-add',
            handler: function () {
                if (!hasPlotRightsRW) {
                    messagerPlotsWriteRights();
                    return false;
                }
                contract_id = jQuery('#contracts-tree').tree('getSelected').id;
                if (contract_id) {

                    var getSelected = jQuery('#contracts-tree').tree('getSelected');

                    //No Rights to operate with "Договори за собственост"
                    if (getSelected.attributes.c_type == CONTRACT_TYPE_OWN && !hasContractsOwnWriteRights) {
                        messagerContractsOwnWriteRights();
                        return false;
                    }

                    initFileUploads(contract_id);
                    jQuery('#win-add-file').window('open');
                } else {
                    jQuery.messager.alert('Грешка', 'Не е избран договор.');
                }
            }
        }, {
            id: 'btndeletecontractfile',
            text: 'Изтриване',
            iconCls: 'icon-remove',
            handler: function () {
                if (!hasPlotRightsRW) {
                    messagerPlotsWriteRights();
                    return false;
                }
                var getChecked = jQuery('#contract-files-tables').datagrid('getChecked'),
                    getSelected;

                if (getChecked[0]) {

                    getSelected = jQuery('#contracts-tree').tree('getSelected');

                    //No Rights to operate with "Договори за собственост"
                    if (getSelected.attributes.c_type == CONTRACT_TYPE_OWN && !hasContractsOwnWriteRights) {
                        messagerContractsOwnWriteRights();
                        return false;
                    }

                    jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да премахнете този запис?', function (r) {
                        if (r) {
                            var obj = {};
                            obj.file_id = getChecked[0].file_id;
                            obj.contract_id = jQuery('#contracts-tree').tree('getSelected').id;
                            TF.Rpc.Contracts.ContractsFilesGrid.delete(obj)
                                .done(function () {
                                    jQuery('#contract-files-tables').datagrid('reload');
                                    jQuery('#contract-files-tables').datagrid('uncheckAll');
                                    jQuery('#contract-files-tables').datagrid('unselectAll');
                                })
                                .fail(function (data) {
                                    RpcErrorHandler.show(data);
                                });
                        }
                    });
                } else {
                    jQuery.messager.alert('Грешка', 'Моля изберете файлове, който да бъдат премахнати.');
                }
            }
        }, {
            id: 'btndownloadcontractfile',
            text: 'Изтегляне',
            iconCls: 'icon-export',
            handler: function () {
                var getChecked = jQuery('#contract-files-tables').datagrid('getChecked'),
                    file_id;

                if (getChecked[0]) {
                    file_id = getChecked[0].id;
                    createContractDownloadAttachmentVariables();

                    TF.Rpc.Contracts.ContractsFilesGrid.download(file_id)
                        .done(function (data) {
                            winDownloadContractAttachment.window('open');
                            var path = data;
                            _pathFile = path;
                            downloadFileContractAttachment.attr("href", path);
                        })
                        .fail(function (errorObj) {
                            jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
                        });
                } else {
                    jQuery.messager.alert('Грешка', 'Моля изберете файл, който искате да изтеглите.');
                }
            }
        }],
        onBeforeLoad: function () {
            jQuery('#contract-files-tables').datagrid('clearChecked');
        },
        onLoadSuccess: function() {
            const rows = jQuery('#contract-files-tables').datagrid('getRows');

            jQuery('#files-tab-counter').text(rows.length);
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initFileUploads(contract_id) {
    const url  = "index.php?json=contract-upload"; 
    jQuery("#uploader").pluploadQueue({
        // General settings
        runtimes: 'gears,html5,flash,silverlight,browserplus',
        url: url,
        multipart_params : {
            "contract_id" : contract_id,
        },
        max_file_size: '100mb',
        unique_names: true,
        // Flash settings
        flash_swf_url: 'lib/js_external/fileupload/plupload.flash.swf'
        
    });

    uploader = jQuery('#uploader').pluploadQueue();
    uploader.bind('UploadComplete', function () {
        jQuery('#win-add-file').window('close');
        jQuery('#contract-files-tables').datagrid('reload');
    });
}

function createContractDownloadAttachmentVariables() {
    winDownloadContractAttachment = jQuery('#win-download').window({
        onClose: winDownloadContractAttachmentWindowClose
    });
    downloadFileContractAttachment = jQuery('#btn-download-file');
    cancelDownloadFileContractAttachment = jQuery('#btn-download-file-close');
}

function winDownloadContractAttachmentWindowClose() {
    return;
}
