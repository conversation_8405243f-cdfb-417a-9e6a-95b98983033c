function initPlotContractsGrid(plot_id, contract_id) {
	jQuery('#plot-contracts-grid').datagrid({
		iconCls: 'icon-contract',
		title: 'Договори',
		nowrap: true,
		autoRowHeight: true,
		striped: true,
		// pageSize: 10,
		fit: true,
		singleSelect: true,
		fitColumns: true,
		showFooter: false,
		border: true,
		url: 'index.php?contracts-rpc=plot-contracts-grid',
		rpcParams:[{
			plot_id: plot_id,
			contract_id: contract_id
		}],
		loader : EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter : EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
		sortName: 'name',
		sortOrder: 'asc',
		idField: 'id',
		rowStyler: function(index, row) {
			if (row.active_text == 'Анулиран' ||
				   row.active_text == 'Изтекъл') {
				return 'color: #aaa';
			}
		},
		columns: [[
				{
					field: 'c_num',
					title: '<b>Договор</b>',
					sortable: false,
					width: 75,
					formatter: function(value,row,index){
						if (row.id > 0) {
							var c_num = row.c_num;

							if(row.active_text == 'Анулиран') {
								c_num = '<strike>' + c_num + '</strike>';
							}
                            if (row.c_type == CONTRACT_TYPE_SALES) {
                                return '<a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="showSalesContract(' + row.id + ')">' + c_num + '</a>';
                            }

							if (row.is_sublease) {
                                return '<a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="showSubleasedContract('+row.id+')">'+c_num+'</a>';
                            } else {
                                return '<a class="easyui-linkbutton datagrid-cell-linkbutton" onclick="showContractInNewTab('+row.id+','+ row.is_annex+')">'+c_num+'</a>';
                            }
                        }
					}
				}, {
					field: 'c_date',
					title: '<b>Дата</b>',
					sortable: false,
					width: 75
				}, {
					field: 'nm_usage_rights',
					title: '<b>Тип договор</b>',
					sortable: false,
					width: 75
				}, {
					field: 'farming',
					title: '<b>Стопанство</b>',
					sortable: false,
					width: 100
				}, {
					field: 'contract_area',
					title: '<b>Площ по договор(дка)</b>',
					sortable: false,
					width: 100
				}
			]],
		pagination: false,
		rownumbers: true,
		onBeforeLoad: function() {
			jQuery('#plot-contracts-grid').datagrid('clearChecked');
		},
		onLoadSuccess: function() {
			var plotData = jQuery('#plot-contracts-grid').datagrid('getData');

			if (plotData['rows'][0])
			{
				jQuery('#plot-contracts-grid').datagrid('selectRow', 0);
			}
		}
	});
}

function showContractInNewTab(contract_id, is_annex) {
    if(is_annex === true) {
       window.open("index.php?page=Contracts.Home&contract_id=" + contract_id, '_blank');
    } else{
        window.open("index.php?page=Contracts.Home&contract_id=" + contract_id, '_blank');
    }
}

function showSubleasedContract(sublease_id) {
    window.open("index.php?page=Subleases.Home&sublease_id=" + sublease_id, '_blank');
}

function showSalesContract(sale_id) {
    window.open("index.php?page=SalesContracts.Home&id=" + sale_id, '_blank');
}
