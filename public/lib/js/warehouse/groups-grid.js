define([
    "jquery",
    "easyui",
    "js/main/EasyUIRPCLoaders",
    "js/main/rpc_errors_handler",
    "TF/Rpc/Warehouse/Groups",
    "js/warehouse/utils",
    "js/warehouse/comboboxes",
], function(jQuery, _, EasyUIRPCLoaders, RpcErrorHandler, Groups, Utils, Comboboxes) {

    function init() {
    }

    var gt;
    var groupTypes = {
        items:{
            rpcMethod: "getItemGroups",
            editEndpoint: Groups.editItemGroup,
            addEndpoint: Groups.addItemGroup,
            deleteEndpoint: Groups.deleteItemGroup,
            parentsCombobox: Comboboxes.itemGroups
        },
        companies: {
            rpcMethod: "getCompanyGroups",
            editEndpoint: Groups.editCompanyGroup,
            addEndpoint: Groups.addCompanyGroup,
            deleteEndpoint: Groups.deleteCompanyGroup,
            parentsCombobox: Comboboxes.companyGroups
        }
    };

    var groupsGrid;
    var isBindedEventsListeners = false;

    function bindEventsListeners() {
        isBindedEventsListeners = true;
        jQuery(document).find("#groups-toolbar").on("keyup", ".datagrid_search_box", function () {
            Utils.quickFilterDataGrid(groupsGrid, "#groups-toolbar", 'name')
        });

        jQuery('#datagrid_add_group').on("click", function () {
            fillGroupForm();
            jQuery("#win-add-edit-group").window("open");
        });

        jQuery('#datagrid_edit_group').on("click", function () {
            var group = groupsGrid.datagrid("getSelected");

            if (!group) {
                jQuery.messager.alert(
                    "Грешка",
                    "Моля изберете група за редактиране."
                );
                return false;
            }
            fillGroupForm(group);
            jQuery("#win-add-edit-group").window("open");
        });

        jQuery('#datagrid_delete_group').on("click", function () {
            var group = groupsGrid.datagrid("getSelected");

            if (!group) {
                jQuery.messager.alert(
                    "Грешка",
                    "Моля изберете група за изтриване."
                );
                return false;
            }

            jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да изтриете групата?', function (r) {
                if (r) deleteGroup(group);
            });
        });

        jQuery("#saveGroupBtn").on("click", saveGroup);
    }

    function initGroupsGrid(groupType, additionalColumns = []) {
        gt = groupType;
        if(!isBindedEventsListeners) bindEventsListeners();

        var columns = [
            {
                field: "name",
                title: "<b>Име</b>",
                width: 180
            },
            {
                field: "parent_name",
                title: "<b>Главна група</b>",
                width: 180
            }
        ];

        //Add additional columns
        if(additionalColumns.length > 0) {
            for (var column of additionalColumns) {
                columns.unshift(column);
            }
        }

        groupsGrid = jQuery("#groups-grid");
        groupsGrid.datagrid({
            autoRowHeight: true,
            autoRowWidth: true,
            url: "index.php?warehouse-rpc=warehouse-groups",
            rpcMethod: groupTypes[groupType].rpcMethod,
            pagination: true,
            fit: true,
            border: false,
            singleSelect: true,
            rpcParams: [{
                criteries: {}
            }],
            columns: [columns],
            toolbar: '#groups-toolbar',
            onSelect: function(rowIndex, rowData) {},
            onLoadSuccess: function() {},
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }

    function saveGroup() {
        var group = {};
        var endpoint;
        group.id = parseInt(jQuery("#group-id").val());
        group.name = jQuery("#group-name").textbox("getValue");
        group.parent = jQuery("#group-parent").combobox("getValue");

        if(group.id) {
            endpoint = groupTypes[gt].editEndpoint
        } else {
            endpoint = groupTypes[gt].addEndpoint
        }

        endpoint(group)
            .done(function() {
                jQuery("#win-add-edit-group").window("close");
                groupsGrid.datagrid("reload");
            })
            .fail(function(errorObj) {
                jQuery.messager.alert(
                    "Грешка",
                    errorObj.getMessage(),
                    "warning"
                );
            });
    }

    function deleteGroup(group) {
        groupTypes[gt].deleteEndpoint(group)
            .done(function() {
                groupsGrid.datagrid("reload");
            })
            .fail(function(errorObj) {
                jQuery.messager.alert(
                    "Грешка",
                    errorObj.getMessage(),
                    "warning"
                );
            });
    }

    function fillGroupForm(group = null) {
        jQuery("#group-id").val(group && group.id ? group.id : "");

        jQuery("#group-name").textbox(
            "setValue",
            group && group.name ? group.name : ""
        );

        groupTypes[gt].parentsCombobox('#group-parent', group && group.parent_id ? group.parent_id : null, {parents:true});
    }

    return {
        init,
        initGroupsGrid,
    };
});
