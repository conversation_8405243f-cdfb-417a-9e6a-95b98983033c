define([
    "jquery",
    "easyui",
    "js/main/EasyUIRPCLoaders",
    "js/main/rpc_errors_handler",
    "TF/Rpc/Warehouse/Companies",
    "js/warehouse/utils",
    "js/warehouse/comboboxes",
    "js/warehouse/constants",
], function(jQuery, _, EasyUIRPCLoaders, RpcErrorHandler, Companies, Utils, Comboboxes, CONSTANTS) {
    function init() {}

    var contragentsGrid;
    var tfGrid;
    var contragentsToolbarId;
    var contragentGridId;
    var contragentAdditionalColumns;
    var contragentCriteries;
    var isBindedEventsListeners = false;
    var quickFilterTimer;

    function bindEventsListeners() {
        isBindedEventsListeners = true;
        jQuery(document).on("click", "#saveContragentBtn", saveContragent);
        jQuery(document).on("click", "#saveFarmBtn", saveFarm);
        jQuery(document).on("click", "#saveMachineBtn", saveMachine);

        jQuery(document).on("click", "#contragents_btn_delete_event", deleteCompany);
        jQuery(document).on("click", "#farms_btn_delete_event", deleteCompany);
        jQuery(document).on("click", "#machines_btn_delete_event", deleteCompany);

        jQuery(document).on("click", '#contragents_btn_add_event', function () {
            fillContragentForm();
            jQuery("#win-add-edit-contragent").window("open");
        });

        jQuery(document).on("click", '#farms_btn_add_event', function () {
            fillFarmForm();
            jQuery("#win-add-edit-farm").window("open");
        });

        jQuery(document).on("click", '#machines_btn_add_event', function () {
            fillMachineForm();
            jQuery("#win-add-edit-machine").window("open");
        });

        jQuery(document).on("click", "#contragents_btn_edit_event", function () {
            var contragent = contragentsGrid.datagrid("getSelected");

            if (!contragent) {
                jQuery.messager.alert(
                    "Грешка",
                    "Моля изберете доставчик за редактиране."
                );
                return false;
            }
            fillContragentForm(contragent);
            jQuery("#win-add-edit-contragent").window("open");
        });

        jQuery(document).on("click", "#farms_btn_edit_event", function () {
            var farm = contragentsGrid.datagrid("getSelected");

            if (!farm) {
                jQuery.messager.alert(
                    "Грешка",
                    "Моля изберете стопанство за редактиране."
                );
                return false;
            }
            fillFarmForm(farm);
            jQuery("#win-add-edit-farm").window("open");
        });

        jQuery(document).on("click", "#machines_btn_edit_event", function () {
            var machine = contragentsGrid.datagrid("getSelected");

            if (!machine) {
                jQuery.messager.alert(
                    "Грешка",
                    "Моля изберете актив за редактиране."
                );
                return false;
            }
            fillMachineForm(machine);
            jQuery("#win-add-edit-machine").window("open");
        });

        jQuery(document).on("click", "#farms_btn_sync_event", function () {
            initTfGrid();
            jQuery("#win-sync-farms").window("open");
            return false;
        });

        jQuery(document).find("#tf-farms-toolbar").on("keyup", ".datagrid_search_box", function () {
            Utils.quickFilterDataGrid(tfGrid, "#tf-farms-toolbar", 'company_name')
        });

        jQuery('#sync_tf_farms').on("click", function () {
            var companies = tfGrid.datagrid('getSelections');
            if (!companies) {
                jQuery.messager.alert(
                    "Грешка",
                    "Моля изберете стопанство/а за синхронизиране."
                );
                return false;
            }

            syncCompanies(companies);
        });

        quickFilterCompanies();
    }

    function quickFilterCompanies() {
        var currentValue = "";
        jQuery(document).find(contragentsToolbarId).on("focus", ".datagrid_search_box", function () {
            currentValue = jQuery(this).val();
        });

        jQuery(document).find(contragentsToolbarId).on("keyup", ".datagrid_search_box", function () {
            if(currentValue === jQuery(this).val()) return true;

            var params = Utils.quickFilterDataGridPrepareParams(contragentsToolbarId, 'name_eik_number', contragentCriteries);
            clearTimeout(quickFilterTimer);
            if(params){
                quickFilterTimer = setTimeout(
                    function () {
                        initContragentsGrid(params.criteries, contragentAdditionalColumns, contragentsGrid, contragentsToolbarId, 10, 1, function() {
                            jQuery(".datagrid_search_box").focus();
                        })
                    }, 500);
            }
        });
    }

    function initContragentsGrid(criteries, additionalColumns = [],  gridId = '#contragents-grid', toolbarId ='#contragents-toolbar', rows = 10, page = 1, callback = null) {
        if(toolbarId !== contragentsToolbarId) {
            contragentsToolbarId = toolbarId;
            quickFilterCompanies()
        }

        contragentsToolbarId = toolbarId;

        if(!isBindedEventsListeners) {
            bindEventsListeners();
        }

        contragentCriteries = criteries;
        contragentGridId = gridId;
        contragentAdditionalColumns = additionalColumns;
        contragentsGrid = jQuery(contragentGridId);

        var columns = [
            {
                field: "name",
                title: "<b>Име</b>",
                width: 170
            },
            {
                field: "code",
                title: "<b>Код</b>",
                width: 90
            },
            {
                field: "group_name",
                title: "<b>Група</b>",
                width: 70
            }
        ];

        //Add additional columns
        if(additionalColumns.length > 0) {
            for (var column of additionalColumns) {
                columns.unshift(column);
            }
        }
        var p1 = new Promise(function (resolve) {
            Companies.getCompanyTypes({criteries: criteries}).done(function (data) {
                resolve(data.rows[0]);
            });
        });

        var p2 = new Promise(function (resolve) {
            Companies.getCompanies({criteries: criteries}, page, rows).done(function (data) {
                for(var company of data.rows){
                    if(company.fields === null) continue;
                    for(var field in company.fields){
                        if(!company.fields[field]) continue;
                        //Custom fields relations
                        if(typeof company.fields[field] === 'object' && company.fields[field] !== null) {
                            company[field] = company.fields[field].name;
                        } else {
                            company[field] = company.fields[field];
                        }
                    }
                }
                resolve(data);
            });
        });

        Promise.all([p1, p2]).then(function (values) {
            var companyTypes, companies;

            [companyTypes, companies] = values;
            if(companyTypes && companies){
                for(var field in companyTypes.fields){
                    if(companyTypes.fields[field].system) continue;
                    columns.push(
                        {
                            field: field,
                            title: "<b>"+companyTypes.fields[field].label+"</b>",
                            width: 150
                        });
                }
            }

            contragentsGrid.datagrid({
                autoRowHeight: true,
                autoRowWidth: true,
                data: companies,
                rpcMethod: "read",
                pagination: true,
                fit: true,
                border: false,
                singleSelect: true,
                columns: [columns],
                toolbar: toolbarId,
                onSelect: function(rowIndex, rowData) {},
                onLoadSuccess: function(data) {
                    if(callback !== null) callback();
                },
                loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
            });

            if (gridId === "#contragents-grid") {
                jQuery("#win-manage-contragents").window("open");
            } else if (gridId === "#farms-grid") {
                jQuery("#win-manage-farms").window("open");
            } else if (gridId === "#machines-grid") {
                jQuery("#win-manage-machines").window("open");
            }

                var pager = contragentsGrid.datagrid('getPager');
                pager.pagination({
                    pageSize: rows,
                    pageNumber: page,
                    onRefresh:function(page, rows){
                        initContragentsGrid(criteries, additionalColumns, gridId, toolbarId, rows, page)
                    },
                    onSelectPage:function (page, rows) {
                        initContragentsGrid(criteries, additionalColumns, gridId, toolbarId, rows, page)
                    },
                    onChangePageSize:function (rows) {
                        initContragentsGrid(criteries, additionalColumns, gridId, toolbarId, rows)
                    }
                })
            });
    }

    function saveContragent() {
        var contragent = {};
        contragent.id = parseInt(jQuery("#contragent-id").val());
        contragent.name = jQuery("#contragent-name").textbox("getValue");
        contragent.mol = jQuery("#contragent-mol").textbox("getValue");
        contragent.eik = jQuery("#contragent-eik").textbox("getValue");
        contragent.iban = jQuery("#contragent-iban").textbox("getValue");
        contragent.zdds = jQuery("#contragent-zdds").textbox("getValue");
        contragent.phone = jQuery("#contragent-phone").textbox("getValue");
        contragent.email = jQuery("#contragent-email").textbox("getValue");
        contragent.code = jQuery("#contragent-code").textbox("getValue");
        contragent.address = jQuery("#contragent-address").textbox("getValue");
        contragent.group = jQuery("#contragent-group").combobox('getValue');
        contragent.type = CONSTANTS.COMPANY_TYPE_CONTRAGENT;

        if ( contragent.name === "") {
            jQuery.messager.alert(
                "Грешка",
                "Моля попълнете всички задължителни полета."
            );
            return false;
        }

        saveOrEdit(contragent);
    }

    function saveFarm() {
        var farm = {};
        farm.id = parseInt(jQuery("#farm-id").val());
        farm.name = jQuery("#farm-name").textbox("getValue");
        farm.mol = jQuery("#farm-mol").textbox("getValue");
        farm.code = jQuery("#farm-code").textbox("getValue");
        farm.eik = jQuery("#farm-eik").textbox("getValue");
        farm.address = jQuery("#farm-address").textbox("getValue");
        farm.type = CONSTANTS.COMPANY_TYPE_FARM;
        farm.group = jQuery("#farm-group").combobox('getValue');

        if ( farm.name === "" || farm.mol === "") {
            jQuery.messager.alert(
                "Грешка",
                "Моля попълнете всички задължителни полета."
            );
            return false;
        }

        saveOrEdit(farm);
    }

    function saveMachine() {
        var machine = {};
        machine.id = parseInt(jQuery("#machine-id").val());
        machine.name = jQuery("#machine-name").textbox("getValue");
        machine.type = CONSTANTS.COMPANY_TYPE_MACHINE;
        machine.farm = jQuery("#machine-farm").combobox("getValue")
        machine.number = jQuery("#machine-number").textbox("getValue");
        machine.group = jQuery("#machine-group").combobox('getValue');

        if (machine.name === "") {
            jQuery.messager.alert(
                "Грешка",
                "Моля попълнете всички задължителни полета."
            );
            return false;
        }

        saveOrEdit(machine);
    }

    function saveOrEdit(data) {
        var endpoint;

        if (data.id > 0) {
            endpoint = Companies.editCompany(data);
        } else {
            endpoint = Companies.addCompany(data);
        }
        endpoint
            .done(function() {
                if(data.type === CONSTANTS.COMPANY_TYPE_CONTRAGENT){
                    jQuery("#win-add-edit-contragent").window("close");
                } else if(data.type === CONSTANTS.COMPANY_TYPE_FARM){
                    jQuery("#win-add-edit-farm").window("close");
                } else if(data.type === CONSTANTS.COMPANY_TYPE_MACHINE){
                    jQuery("#win-add-edit-machine").window("close");
                }

                initContragentsGrid(contragentCriteries, contragentAdditionalColumns, contragentGridId, contragentsToolbarId)
            })
            .fail(function(errorObj) {
                jQuery.messager.alert(
                    "Грешка",
                    errorObj.getMessage(),
                    "warning"
                );
            });
    }

    function deleteCompany() {
        var company = contragentsGrid.datagrid("getSelected");
        if (!company) {
            jQuery.messager.alert(
                "Грешка",
                "Моля изберете обект за изтриване."
            );
            return false;
        }

        Companies.deleteCompany(company)
            .done(function() {
                initContragentsGrid(contragentCriteries, [], contragentGridId, contragentsToolbarId)
            })
            .fail(function(errorObj) {
                jQuery.messager.alert(
                    "Грешка",
                    errorObj.getMessage(),
                    "warning"
                );
            });
    }

    function fillContragentForm(contragent = null) {
        jQuery("#contragent-name").textbox(
            "setValue",
            contragent && contragent.name ? contragent.name : ""
        );
        jQuery("#contragent-mol").textbox(
            "setValue",
            contragent && contragent.mol ? contragent.mol : ""
        );
        jQuery("#contragent-eik").textbox(
            "setValue",
            contragent && contragent.eik ? contragent.eik : ""
        );
        jQuery("#contragent-iban").textbox(
            "setValue",
            contragent && contragent.iban ? contragent.iban : ""
        );
        jQuery("#contragent-zdds").textbox(
            "setValue",
            contragent && contragent.zdds ? contragent.zdds : ""
        );
        jQuery("#contragent-phone").textbox(
            "setValue",
            contragent && contragent.phone ? contragent.phone : ""
        );
        jQuery("#contragent-email").textbox(
            "setValue",
            contragent && contragent.email ? contragent.email : ""
        );
        jQuery("#contragent-code").textbox(
            "setValue",
            contragent && contragent.code ? contragent.code : ""
        );
        jQuery("#contragent-code").textbox({disabled: CONSTANTS.AUTO_GENERATE_CODES ? true : contragent && contragent.code !== ""});

        jQuery("#contragent-address").textbox(
            "setValue",
            contragent && contragent.address ? contragent.address : ""
        );
        jQuery("#contragent-id").val(
            contragent && contragent.id ? contragent.id : ""
        );
        jQuery("#contragent-reset-datagrid").val(
            contragent && contragent.resetDatagrid ? contragent.resetDatagrid : ""
        );
        Comboboxes.companyGroups(
            "#contragent-group",
            contragent && contragent.group_id ? contragent.group_id : [],
            {});
    }

    function fillFarmForm(farm = null) {
        jQuery("#farm-name").textbox(
            "setValue",
            farm && farm.name ? farm.name : ""
        );
        jQuery("#farm-mol").textbox(
            "setValue",
            farm && farm.mol ? farm.mol : ""
        );
        jQuery("#farm-eik").textbox(
            "setValue",
            farm && farm.eik ? farm.eik : ""
        );

        jQuery("#farm-code").textbox({disabled: CONSTANTS.AUTO_GENERATE_CODES ? true : farm && farm.code !== ""});
        jQuery("#farm-code").textbox(
            "setValue",
            farm && farm.code ? farm.code : ""
        );
        jQuery("#farm-address").textbox(
            "setValue",
            farm && farm.address ? farm.address : ""
        );
        jQuery("#farm-id").val(
            farm && farm.id ? farm.id : ""
        );

        Comboboxes.companyGroups(
            "#farm-group",
            farm && farm.group_id ? farm.group_id : [],
            {});
    }

    function fillMachineForm(machine = null) {
        jQuery("#machine-name").textbox(
            "setValue",
            machine && machine.name ? machine.name : ""
        );

        jQuery("#machine-code").textbox({disabled: CONSTANTS.AUTO_GENERATE_CODES ? true : machine && machine.code !== ""});
        jQuery("#machine-code").textbox(
            "setValue",
            machine && machine.code ? machine.code : ""
        );

        jQuery("#machine-number").textbox(
            "setValue",
            machine && machine.fields && machine.fields.number ? machine.fields.number : ""
        );

        var selectedFarm = "";
        if(machine && machine.fields && machine.fields.farm){
            if(typeof machine.fields['farm'] === 'object' && machine.fields['farm'] !== null) {
                selectedFarm = machine.fields['farm'].id;
            } else {
                selectedFarm = machine.fields['farm'];
            }
        }

        Comboboxes.companies(
            "#machine-farm",
            selectedFarm,
            {types:[CONSTANTS.COMPANY_TYPE_FARM]},
            false,
            true
        );

        Comboboxes.companyGroups(
            "#machine-group",
            machine && machine.group_id ? machine.group_id : [],
            {});

        jQuery("#machine-id").val(
            machine && machine.id ? machine.id : ""
        );
    }

    function initTfGrid(additionalColumns = []) {
        var columns = [
            {
                field: "company",
                title: "<b>Фирма</b>",
                width: 200
            },
            {
                field: "eik",
                title: "<b>ЕИК</b>",
                width: 120
            },
            {
                field: "mol",
                title: "<b>МОЛ</b>",
                sortable: true,
                width: 170
            }

        ];

        //Add additional columns
        if(additionalColumns.length > 0) {
            for (var column of additionalColumns) {
                columns.unshift(column);
            }
        }

        tfGrid = jQuery("#tf-farms-grid");
        tfGrid.datagrid({
            autoRowHeight: true,
            autoRowWidth: true,
            url: "index.php?warehouse-rpc=warehouse-companies",
            rpcMethod: "getTechnofarmCompanies",
            pagination: true,
            fit: true,
            border: false,
            singleSelect: false,
            rpcParams: [{
                criteries: {}
            }],
            columns: [columns],
            toolbar: '#tf-farms-toolbar',
            onSelect: function(rowIndex, rowData) {},
            onLoadSuccess: function() {},
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }


    function syncCompanies(companies, confirmed = false) {
        var convertedFarms = [];
        for(var farm of companies) {
            convertedFarms.push({
                'name': farm.company,
                'eik': farm.eik,
                'mol': farm.mol,
                'tf': farm.id,
                'type': CONSTANTS.COMPANY_TYPE_FARM
            });
        }
        Companies.syncCompanies(convertedFarms, confirmed)
            .done(function(data) {
                if(data.exist){
                    jQuery.messager.confirm('Confirm',data.existCompaniesNames.join(',')+' вече същестува/т в склада. При синхронизация данните ще бъдат презаписани с тези от Технофарм. Сигурни ли сте, че искате да изпълните операцията?',function(r){
                        if (r){
                            syncCompanies(companies, true);
                        }
                    });
                    return;
                }
                initContragentsGrid(
                    {types: [CONSTANTS.COMPANY_TYPE_FARM]},
                    [],
                    '#farms-grid',
                    '#farms-toolbar'
                );
                jQuery("#win-sync-farms").window("close");
            })
            .fail(function(errorObj) {
                jQuery.messager.alert(
                    "Грешка",
                    errorObj.getMessage(),
                    "warning"
                );
            });
    }


    return {
        init,
        initContragentsGrid,
        fillContragentForm,
        fillFarmForm,
        deleteCompany
    };
});
