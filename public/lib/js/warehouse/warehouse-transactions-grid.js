define(["jquery",
        "easyui",
        "js/main/EasyUIRPCLoaders",
        "TF/Rpc/Warehouse/Transactions",
    ], function(
    jQuery,
    _,
    EasyUIRPCLoaders,
    Transactions
) {
    function init() {
    }

    function bindEventsListeners() {
        jQuery(document).on("click", "#btnAddTransaction", function () {
            window.location.href = "index.php?page=WarehouseAddTransaction.Home";
        });
    
        jQuery(document).on("click", "#btnRemoveTransaction", removeTransaction);

        jQuery(document).on("click", "#btnSubContragentsTransaction", function () {
            window.location.href = "index.php?page=WarehouseSubContragentsTransaction.Home";
        });

        jQuery(document).on("click", "#btnSubPlotsTransaction", function () {
            window.location.href = "index.php?page=WarehouseSubPlotsTransaction.Home";
        });

        jQuery(document).on("click", "#btnSubMachinesTransaction", function () {
            window.location.href = "index.php?page=WarehouseSubMachinesTransaction.Home";
        });

        jQuery(document).on("click", "#btnTransferTransaction", function () {
            window.location.href = "index.php?page=WarehouseTransferTransaction.Home";
        });
    }

    function initWarehouseTransactionsGrid(id = null) {
        bindEventsListeners();

        var modemFilesList = jQuery("#transactions-grid");
        modemFilesList.datagrid({
            autoRowHeight: true,
            autoRowWidth: true,
            url: "index.php?warehouse-rpc=warehouse-transactions",
            rpcMethod: "read",
            pagination: true,
            singleSelect: true,
            fit: true,
            border: false,
            rpcParams: [
                {
                    criteries: {
                        warehouses: id ? [id] : []
                    }
                }
            ],
            columns: [
                [
                    {
                        field: "item_code",
                        title: "<b>Номенклатурен №</b>",
                        width: 120
                    },
                    {
                        field: "typeName",
                        title: "<b>Вид</b>",
                        sortable: true,
                        width: 150
                    },
                    {
                        field: "item_name",
                        title: "<b>Артикул</b>",
                        sortable: true,
                        width: 150
                    },
                    {
                        field: "measure_name",
                        title: "<b>Мярка</b>",
                        width: 70,
                    },
                    {
                        field: "quantity",
                        title: "<b>Количество</b>",
                        width: 100,
                        align: 'right',
                    },
                    {
                        field: "document_date",
                        title: "<b>Дата</b>",
                        width: 100
                    },
                    {
                        field: "document_number",
                        title: "<b>Документ №</b>",
                        width: 120
                    },
                    {
                        field: "company_in_name",
                        title: "<b>Получател</b>",
                        sortable: true,
                        width: 180
                    },
                    {
                        field: "company_out_name",
                        title: "<b>Доставчик</b>",
                        width: 150
                    },
                    {
                        field: "warehouse_name",
                        title: "<b>Склад</b>",
                        width: 100
                    },
                    {
                        field: "single_price_no_dds",
                        title: "<b>Цена без ДДС</b>",
                        width: 100,
                        align: 'right'
                    },
                    {
                        field: "single_price_with_dds",
                        title: "<b>Цена с ДДС</b>",
                        width: 100,
                        align: 'right'
                    }
                ]
            ],
            // toolbar: '#transactions-grid-toolbar',
            onSelect: function(rowIndex, rowData) {},
            onLoadSuccess: function() {
                endLoading();
            },
            rowStyler: function(index, row) {
                let style = [];
                if (row.transaction_status === 0) {
                    style.push('text-decoration: line-through');
                    style.push('color: #aaa');
                }
    
                if (row.doc_closed_at == null) {
                    style.push('background-color:#fb9d37');
                }
    
                return style.join(';');
            },
            onLoadSuccess: function() {
                //endLoading();
            },
            onLoadError: function() {
                //endLoading();
            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }
    
    function removeTransaction()
    {
        const selectedRow = jQuery('#transactions-grid').datagrid('getSelected');

        if (selectedRow.transaction_id) {
            jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте че искате да премахнете този запис?', function(r) {
                if (r) {
                    Transactions.removeTransaction(
                        {
                            transactionId: selectedRow.transaction_id
                        }
                    )
                    .done(function (data) {
                        jQuery('#transactions-grid').datagrid('reload');
                    })
                    .fail(function (errorObj) {
                        jQuery.messager.alert('Грешка', errorObj.getOriginalMessage(), 'warning');
                    });
                }
            });
        } else {
            jQuery.messager.alert('Грешка', 'Моля изберете транзакция за изтриване.', 'warning');
            return false;
        }
    }

    return {
        init,
        initWarehouseTransactionsGrid,
    };
});
