define([
    "jquery",
    "easyui",
    "js/main/EasyUIRPCLoaders",
    "js/main/rpc_errors_handler",
    "TF/Rpc/Warehouse/Users",
    "js/warehouse/utils",
], function(jQ<PERSON><PERSON>, _, EasyUIRPCLoaders, Rp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Users, Utils) {
    function init() {}

    var userPermissionsGrid;
    var roles = {
        ROLE_USER: 'Права за четене',
        ROLE_EDITOR: 'Права за четене и редакция',
        ROLE_ADMIN: 'Права за четене, редакция и администриране'
    };
    var rpcParams = {
        criteries: {}
    };

    function bindEventsListeners() {
        jQuery(document).find('#user-permissions-toolbar').on("keyup", ".datagrid_search_box", function () {
            var filters = rpcParams.criteries;
            Utils.quickFilterDataGrid(userPermissionsGrid, '#user-permissions-toolbar', 'username', filters);
        });

        jQuery("#savePermissionsBtn").on("click", savePermissions);

        jQuery('#users_btn_permissions_event').on("click", function () {
            var user = userPermissionsGrid.datagrid("getSelected");

            if (!user) {
                jQuery.messager.alert(
                    "Грешка",
                    "Моля изберете потребител за редактиране."
                );
                return false;
            }

            fillPermissionsForm(user);
            jQuery("#win-edit-permissions").window("open");
        });
    }

    function initUserPermissionsGrid(additionalColumns = []) {
        bindEventsListeners();

        var columns = [
            {
                field: "username",
                title: "<b>Потребител</b>",
                width: 150
            },
            {
                field: "name",
                title: "<b>Име</b>",
                width: 150
            },
            {
                field: "roles",
                title: "<b>Права</b>",
                width: 300,
                formatter: function (value) {
                    if(value !== undefined) return roles[value[0]];
                }
            },
        ];

        //Add additional columns
        if(additionalColumns.length > 0) {
            for (var column of additionalColumns) {
                columns.unshift(column);
            }
        }

        rpcParams['criteries']['roles'] = ["ROLE_EDITOR", "ROLE_USER", "ROLE_ADMIN"];

        userPermissionsGrid = jQuery("#user-permissions-grid");
        userPermissionsGrid.datagrid({
            autoRowHeight: true,
            autoRowWidth: true,
            url: "index.php?warehouse-rpc=warehouse-users",
            rpcMethod: "read",
            pagination: true,
            fit: true,
            border: false,
            singleSelect: true,
            rpcParams: [rpcParams],
            columns: [columns],
            toolbar: '#user-permissions-toolbar',
            onSelect: function(rowIndex, rowData) {},
            onLoadSuccess: function() {},
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }

    function fillPermissionsForm(user = null) {
        jQuery("#user-name").textbox(
            "setValue",
            user && user.name ? user.name : ""
        );

        jQuery("#user-username").textbox(
            "setValue",
            user && user.username ? user.username : ""
        );

        var perimissions = [];
        for(var role in roles) {
            perimissions.push({
                label: roles[role],
                value: role,
                selected: role === user.roles[0]
            })
        }
        jQuery("#user-permissions").combobox({
                valueField: 'value',
                textField: 'label',
                data: perimissions,
                loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
            }
        );

        jQuery("#user-id").val(user && user.id ? user.id : "");
    }

    function savePermissions() {
        var user = {};
        user.id = parseInt(jQuery("#user-id").val());
        user.roles = [jQuery("#user-permissions").combobox("getValue")]; //must be array
        user.username = jQuery("#user-username").textbox("getValue");

        Users.editUserRole(user)
            .done(function() {
                jQuery("#win-edit-permissions").window("close");
                userPermissionsGrid.datagrid("reload");
            })
            .fail(function(errorObj) {
                jQuery.messager.alert(
                    "Грешка",
                    errorObj.getMessage(),
                    "warning"
                );
            });
    }

    return {
        init,
        initUserPermissionsGrid,
    };
});
