define([
    "jquery",
    "easyui",
    "js/main/EasyUIRPCLoaders",
    "js/main/rpc_errors_handler",
    "TF/Rpc/Warehouse/Measures",
], function(jQuery, _, EasyUIRPCLoaders, <PERSON>pc<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Measures) {
    function init() {}

    var measuresGrid;
    var isBindedEventsListeners = false;

    function bindEventsListeners() {
        isBindedEventsListeners = true;
        jQuery('#measures_search_box').keyup(function(){
            var value = jQuery('#measures_search_box').val()

            setTimeout(function(){
                if(value === jQuery('#measures_search_box').val()) {
                    measuresGrid.datagrid('options').rpcParams = [{
                        criteries: {
                            "measure_name": value
                        }
                    }];
                    measuresGrid.datagrid('reload');
                }
            }, 800);
        });

        jQuery("#saveMeasureBtn").on("click", saveMeasure);

        jQuery('#measures_btn_add_event').on("click", function () {
            fillMeasureForm();
            jQuery("#win-add-edit-measure").window("open");
        });

        jQuery('#measures_btn_edit_event').on("click", function () {
            var measure = measuresGrid.datagrid("getSelected");

            if (!measure) {
                jQuery.messager.alert(
                    "Грешка",
                    "Моля изберете мярка за редактиране."
                );
                return false;
            }

            fillMeasureForm(measure);
            jQuery("#win-add-edit-measure").window("open");
        });

        jQuery('#measures_btn_delete_event').on("click", function () {
            var measure = measuresGrid.datagrid("getSelected");
            if (!measure) {
                jQuery.messager.alert(
                    "Грешка",
                    "Моля изберете мярка за изтриване."
                );
                return false;
            }
            deleteMeasure(measure);
        });
    }

    function initMeasureGrid(additionalColumns = []) {
        if(!isBindedEventsListeners) bindEventsListeners();

        var columns = [
            {
                field: "name",
                title: "<b>Име</b>",
                width: 220
            },
            {
                field: "short_name",
                title: "<b>Съкратено име</b>",
                width: 120
            },
        ];

        //Add additional columns
        if(additionalColumns.length > 0) {
            for (var column of additionalColumns) {
                columns.unshift(column);
            }
        }

        measuresGrid = jQuery("#measures-grid");
        measuresGrid.datagrid({
            autoRowHeight: true,
            autoRowWidth: true,
            url: "index.php?warehouse-rpc=warehouse-measures",
            rpcMethod: "read",
            pagination: true,
            fit: true,
            border: false,
            singleSelect: true,
            rpcParams: [{
                criteries: {}
            }],
            columns: [columns],
            toolbar: '#measures-toolbar',
            onSelect: function(rowIndex, rowData) {},
            onLoadSuccess: function() {},
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }

    function fillMeasureForm(measure = null) {
        jQuery("#measure-name").textbox(
            "setValue",
            measure && measure.name ? measure.name : ""
        );

        jQuery("#measure-short-name").textbox(
            "setValue",
            measure && measure.short_name ? measure.short_name : ""
        );

        jQuery("#measure-id").val(measure && measure.id ? measure.id : "");
    }

    function saveMeasure() {
        var measure = {};
        var endpoint;

        measure.name = jQuery("#measure-name").textbox("getValue");
        measure.short_name = jQuery("#measure-short-name").textbox("getValue");

        if (measure.name === "" || measure.short_name === "") {
            jQuery.messager.alert(
                "Грешка",
                "Моля попълнете всички задължителни полета."
            );
            return false;
        }

        if (jQuery("#measure-id").val() !== "") {
            measure.id = parseInt(jQuery("#measure-id").val());
            endpoint = Measures.editMeasure(measure);
        } else {
            endpoint = Measures.addMeasure(measure);
        }

        endpoint
            .done(function() {
                jQuery("#win-add-edit-measure").window("close");
                jQuery("#measures-grid").datagrid("reload");
            })
            .fail(function(errorObj) {
                jQuery.messager.alert(
                    "Грешка",
                    errorObj.getMessage(),
                    "warning"
                );
            });
    }

    function deleteMeasure(measure) {
        Measures.deleteMeasure(measure)
            .done(function() {
                measureGrid.datagrid("reload");
            })
            .fail(function(errorObj) {
                jQuery.messager.alert(
                    "Грешка",
                    errorObj.getMessage(),
                    "warning"
                );
            });
    }

    return {
        init,
        initMeasureGrid,
    };
});
