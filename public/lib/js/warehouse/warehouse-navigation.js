define([
    "easyui",
    "js/main/EasyUIRPCLoaders",
    "js/main/rpc_errors_handler",
    "js/warehouse/constants",
    "js/warehouse/comboboxes",
    "js/warehouse/utils",
    
    "js/warehouse/companies-grid",
    "js/warehouse/documents-grid",
    "js/warehouse/items-grid",
    "js/warehouse/measures-grid",
    "TF/Rpc/Warehouse/Warehouses",
    "TF/Rpc/Warehouse/Transactions",
    "js/warehouse/user-permissions-grid",
    "js/warehouse/groups-grid",
    "js/warehouse/config-params"
], function(
    _,
    EasyUIRPCLoaders,
    RpcErrorHandler,
    CONSTANTS,
    Comboboxes,
    Utils,

    companiesGrid,
    documents_grid,
    items_grid,
    measures_grid,
    Warehouses,
    Transactions,
    UserPermissionsGrid,
    groupsGrid,
    warehouseConfigParams
) {

    function init() {
        navigation();
    }

    function navigation() {
        jQuery("#btn-add-warehouse").on("click", addWarehouse);
        jQuery("#btn-edit-warehouse").on("click", editWarehouse);
        jQuery("#btn-delete-warehouse").on("click", deleteWarehouse);
        jQuery("#saveWarehouseBtn").on("click", saveWarehouse);

        jQuery("#btn-manage-contragents").on("click", function() {
            companiesGrid.initContragentsGrid(
                {types: [CONSTANTS.COMPANY_TYPE_CONTRAGENT]},
                [],
                '#contragents-grid',
                '#contragents-toolbar'
            );
            return false;
        });

        jQuery("#btn-manage-machines").on("click", function() {
            companiesGrid.initContragentsGrid(
                {types: [CONSTANTS.COMPANY_TYPE_MACHINE]},
                [],
                '#machines-grid',
                '#machines-toolbar'
            );
            return false;
        });

        jQuery("#btn-manage-farms").on("click", function() {
            companiesGrid.initContragentsGrid(
                {types: [CONSTANTS.COMPANY_TYPE_FARM]},
                [],
                '#farms-grid',
                '#farms-toolbar'
            );
            return false;
        });

        jQuery("#btn-manage-items").on("click", function() {
            items_grid.initItemsGrid();
            jQuery("#win-manage-items").window("open");
            return false;
        });

        jQuery("#btn-manage-documents").on("click", function() {
            documents_grid.initDocumentsGrid();
            jQuery("#win-manage-documents").window("open");
            return false;
        });

        jQuery("#btn-manage-measures").on("click", function() {
            measures_grid.initMeasureGrid();
            jQuery("#win-manage-measures").window("open");
            return false;
        });

        jQuery("#btn-manage-user-permissions").on("click", function() {
            UserPermissionsGrid.initUserPermissionsGrid();
            jQuery("#win-manage-user-permissions").window("open");
            return false;
        });

        jQuery("#btn-manage-items-groups").on("click", function() {
            groupsGrid.initGroupsGrid("items");
            jQuery("#win-manage-groups").window("open");
            return false;
        });

        jQuery("#btn-manage-contragents-groups").on("click", function() {
            groupsGrid.initGroupsGrid("companies");
            jQuery("#win-manage-groups").window("open");
            return false;
        });

        jQuery("#addNewWarehouseFieldBtn").on("click", function() {
            addWarehouseField();
            return false;
        });
    
        jQuery("#btn-manage-warehouse-config-params").on("click", function() {
            warehouseConfigParams.init();
            return false;
        });

        jQuery(".closeBtn").on("click", Utils.closePanels);
    }

    function addWarehouse() {
        jQuery('#addNewWarehouseFieldBtn').show();
        jQuery(".additionalField").remove();
        jQuery("#warehouse-name").textbox("clear");
        jQuery("#warehouse-code").textbox("clear");
        jQuery("#warehouse-code").textbox({disabled: CONSTANTS.AUTO_GENERATE_CODES});
        jQuery("#warehouse-id").val("");

        Comboboxes.mainWarehouses("#parent-warehouses")

        Comboboxes.companies("#warehouse-companies", [], {types:[CONSTANTS.COMPANY_TYPE_FARM]}, false, true, true);

        jQuery(".js-field-warehouse").prop('checked', false);
        jQuery(".js-field-warehouse").prop('disabled', false);

        jQuery(".js-field-key").textbox({value: "", disabled: true});
        jQuery(".js-field-label").textbox({value: "", disabled: false});
        // Comboboxes.fieldTypes(".js-field-type", ['varchar']);

        jQuery("#win-add-edit-warehouses").window("open");

        return false;
    }

    function editWarehouse() {
        jQuery('#addNewWarehouseFieldBtn').hide();
        var data = jQuery("#warehouses-tree").tree("getSelected");

        if (!data) {
            jQuery.messager.alert(
                "Грешка",
                "Моля изберете склад за редактиране"
            );
            return false;
        }
        jQuery("#warehouse-name").textbox("setValue", data.name);
        jQuery("#warehouse-id").val(data.id);

        jQuery("#warehouse-code").textbox("setValue", data.code);
        jQuery("#warehouse-code").textbox({disabled: true});
        jQuery(".js-field-warehouse").prop('disabled', true);

        Comboboxes.mainWarehouses("#parent-warehouses", data.parentId)

        Comboboxes.companies(
            "#warehouse-companies",
            data.companies.map(company => company.id),
            {types:[CONSTANTS.COMPANY_TYPE_FARM]},
            false,
            true,
            true
        );

        // jQuery(".additionalField").remove();
        // Comboboxes.fieldTypes(".js-field-type", ['varchar'], {}, true);
        // jQuery(".js-field-key").textbox({value: "", disabled: true});
        // jQuery(".js-field-label").textbox({value: "", disabled: true});
        //
        if(data.fields) {
            var counter = 1;
            jQuery.each(data.fields, function (key, field) {
                if(key === CONSTANTS.WAREHOUSE_SYSTEM_TYPE_KEY) {
                    //TODO:: make better
                    if(field[CONSTANTS.WAREHOUSE_TYPE_MACHINES] === "true") {
                        jQuery(".js-field-warehouse").prop('checked', true);
                    } else {
                        jQuery(".js-field-warehouse").prop('checked', false);
                        return;
                    }
                    return;
                }
                addWarehouseField(key, field.label, field.type, counter);
                counter++;
            });
        }

        jQuery("#win-add-edit-warehouses").window("open");

        return false;
    }

    function deleteWarehouse() {
        var data = jQuery("#warehouses-tree").tree("getSelected");

        if (!data) {
            jQuery.messager.alert(
                "Грешка",
                "Моля изберете склад"
            );
            return false;
        }

        Warehouses.deleteWarehouse(data)
            .done(function() {
                jQuery("#warehouses-tree").tree("reload");
            })
            .fail(function(errorObj) {
                jQuery.messager.alert(
                    "Грешка",
                    errorObj.getMessage(),
                    "warning"
                );
            });

        return false;
    }

    function saveWarehouse() {
        var warehouse = {};
        var endpoint;

        warehouse.parent_id = parseInt(
            jQuery("#parent-warehouses").combobox("getValue")
        );
        warehouse.name = jQuery("#warehouse-name").textbox("getValue");
        warehouse.code = jQuery("#warehouse-code").textbox("getValue");
        warehouse.companies = Utils.clearEmptyStringArray(jQuery("#warehouse-companies").combobox("getValues"));
        warehouse.fields = [];

        if (warehouse.name === "") {
            jQuery.messager.alert(
                "Грешка",
                "Моля попълнете всички задължителни полета."
            );
            return false;
        }

        // jQuery(".field-row").each(function(index, element) {
        //     var label = jQuery(element).find('.js-field-label').textbox('getValue');
        //     if(label === "") return true;
        //     warehouse.fields.push({
        //         key: jQuery(element).find('.js-field-key').textbox('getValue'),
        //         label: label,
        //         type: jQuery(element).find('.js-field-type').combobox('getValue'),
        //     })
        // });

        if (jQuery("#warehouse-id").val() !== "") {
            warehouse.id = parseInt(jQuery("#warehouse-id").val());
            endpoint = Warehouses.editWarehouse(warehouse);

        } else {
            var isMachinesWarehouse = jQuery(".js-field-warehouse").prop('checked');
            warehouse.fields.push({
                key: CONSTANTS.WAREHOUSE_SYSTEM_TYPE_KEY,
                name: CONSTANTS.WAREHOUSE_TYPE_MACHINES,
                value: isMachinesWarehouse ? "true" : "false",
            });

            endpoint = Warehouses.addWarehouse(warehouse);
        }

        endpoint
            .done(function() {
                jQuery("#win-add-edit-warehouses").window("close");
                jQuery("#warehouses-tree").tree("reload");
            })
            .fail(function(errorObj) {
                jQuery.messager.alert(
                    "Грешка",
                    errorObj.getMessage(),
                    "warning"
                );
            });
    }

    function addWarehouseField(key = '', label = '', type = 'varchar', row = 0){
        var rowExists = jQuery("div[data-field-row='"+row+"']");
        var disabled = row;
        if(!rowExists.length){
            var count = jQuery('.field-row').length;
            var fieldRow = jQuery('.field-row').first().clone();
            row = count + 1;
            jQuery(fieldRow).addClass("additionalField");
            jQuery(fieldRow).attr('data-field-row', row);

            fieldRow.find('.textbox').remove();
            fieldRow.appendTo('.field-rows');
        }

        Comboboxes.fieldTypes("div[data-field-row='"+row+"'] .js-field-type", [type], {}, disabled);
        jQuery("div[data-field-row='"+row+"'] .js-field-key").textbox({
            value: key,
            disabled: true
        });
        jQuery("div[data-field-row='"+row+"'] .js-field-label").textbox({value: label});
    }

    return {
        init,
    };
});
