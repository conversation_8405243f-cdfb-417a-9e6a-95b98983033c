define([
    "jquery",
    "easyui",
    "js/main/EasyUIRPCLoaders",
    "js/warehouse/warehouse-transactions-grid",
    "js/warehouse/groups-grid"
], function(jQuery, _, EasyUIRPCLoaders, warehouses_transactions_grid, groupsGrid) {
    function init() {
    }
    function initWarehousesTree(file_id) {
        jQuery("#warehouses-tree").tree({
            url: "index.php?warehouse-rpc=warehouses",
            rpcParams: [{}],
            animate: true,
            lines: true,
            formatter: function(node) {
                if (node.color)
                    return (
                        "<div style='width:13px;height:13px;background-color:#" +
                        node.color +
                        ";margin-top:3px;float:left;margin-right:9px;'></div>" +
                        node.name
                    );
                else return node.name;
            },
            onLoadSuccess: function() {
                //endLoading();
            },
            onLoadError: function() {
                //endLoading();
            },
            onBeforeSelect: function(node) {},
            onSelect: function(node) {
                warehouses_transactions_grid.initWarehouseTransactionsGrid(
                    node.id
                );
            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });

        jQuery("#sub-nav-btn-manage-items-groups").on("click", function() {
            groupsGrid.initGroupsGrid("items");
            jQuery("#win-manage-groups").window("open");
            return false;
        });

        jQuery("#sub-nav-btn-manage-contragents-groups").on("click", function() {
            groupsGrid.initGroupsGrid("companies");
            jQuery("#win-manage-groups").window("open");
            return false;
        });
    }

    return {
        init,
        initWarehousesTree
    };
});
