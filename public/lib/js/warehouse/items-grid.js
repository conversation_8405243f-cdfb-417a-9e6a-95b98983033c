define([
    "jquery",
    "easyui",
    "js/main/EasyUIRPCLoaders",
    "js/main/rpc_errors_handler",
    "TF/Rpc/Warehouse/Items",
    "js/warehouse/comboboxes",
    "js/warehouse/utils",
    "js/warehouse/constants",
], function(
            jQ<PERSON>y,
            _,
            EasyUIRPCLoaders,
            RpcErrorHandler,
            Items,
            Comboboxes,
            Utils,
            CONSTANTS
) {
    function init() {}

    var itemsGrid;
    var itemsGridToolBarId;
    var isBindedEventsListeners = false;
    var rpcParams = {
        criteries: {}
    };


    function bindEventsListeners() {
        isBindedEventsListeners = true;
        jQuery("#saveItemBtn").on("click", saveItem);

        jQuery(document).on("click", "#items_btn_add_event", function () {
            jQuery("#item-code").closest("td").show();

            let item = {
                company_id: jQuery("#tr-company-id-in").val()
            }
            fillItemForm(item);
            jQuery("#win-add-edit-item").window("open");
        });

        jQuery(document).on("click", "#items_btn_edit_event", function () {
            var item = itemsGrid.datagrid("getSelected");

            if (!item) {
                jQuery.messager.alert(
                    "Грешка",
                    "Моля изберете артикул за редактиране."
                );
                return false;
            }

            fillItemForm(item);
            jQuery("#win-add-edit-item").window("open");
        });

        jQuery('#btn_delete_event').on("click", function () {
            var item = itemsGrid.datagrid("getSelected");
            if (!item) {
                jQuery.messager.alert(
                    "Грешка",
                    "Моля изберете артикул за изтриване."
                );
                return false;
            }
            deleteItem(item);
        });


        jQuery(document).find(itemsGridToolBarId).on("keyup", ".datagrid_search_box", function () {
            var filters = rpcParams.criteries;

            if(jQuery("#farm-warehouses-combobox").length > 0) filters.warehouse = jQuery("#farm-warehouses-combobox").combobox('getValue');
            if(jQuery("#tr-farm-id").length > 0) filters.farm = jQuery("#tr-farm-id").val();
            Utils.quickFilterDataGrid(itemsGrid, itemsGridToolBarId, 'item_name_code', filters);
        });
    }

    function initItemsGrid(additionalColumns = [], gridId = '#items-grid', toolbarId = '#items-toolbar') {
        itemsGridToolBarId = toolbarId;
        if(!isBindedEventsListeners) bindEventsListeners();

        var columns = [
            {
                field: "item_name",
                title: "<b>Име</b>",
                width: 200,
                sortable: true
            },
            {
                field: "printName",
                title: "<b>Име за печат</b>",
                width: 120
            },
            {
                field: "measure_name",
                title: "<b>Мярка</b>",
                width: 60,
                sortable: true
            },
            {
                field: "code",
                title: "<b>Код</b>",
                width: 80,
                sortable: true
            },
            {
                field: "minQuantity",
                title: "<b>Мин. кол.</b>",
                width: 100,
                align: 'right',
            },
            {
                field: "group_name",
                title: "<b>Група</b>",
                width: 120,
                sortable: true
            }
        ];

        if(CONSTANTS.ITEMS_WITH_WAREHOUSE) {
            columns.push({
                field: "warehouse_name",
                title: "<b>Склад</b>",
                width: 120,
                sortable: true
            });

            if(gridId === '#items-grid-rp') {
                rpcParams['criteries']['companies'] = [jQuery("#tr-company-id-in").val()];
            }

        }

        //Add additional columns
        if(additionalColumns.length > 0) {
            for (var column of additionalColumns) {
                columns.unshift(column);
            }
        }
        
        itemsGrid = jQuery(gridId);
        itemsGrid.datagrid({
            autoRowHeight: true,
            autoRowWidth: true,
            url: "index.php?warehouse-rpc=warehouse-items",
            rpcMethod: "read",
            pagination: true,
            fit: true,
            border: false,
            singleSelect: true,
            rpcParams: [rpcParams],
            columns: [columns],
            toolbar: toolbarId,
            onSelect: function(rowIndex, rowData) {},
            onLoadSuccess: function() {
                jQuery(gridId).datagrid('resize');
            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });

    }

    function fillItemForm(item = null) {
        jQuery("#item-name").textbox(
            "setValue",
            item && item.item_name ? item.item_name : ""
        );
        let criteries = {};

        if(item.company_id !== undefined){
            criteries = {
                companies: [item.company_id]
            };
        }

        if(CONSTANTS.ITEMS_WITH_WAREHOUSE) {
            Comboboxes.availableWarehouses(
                "#warehouse",
                item && item.warehouse_id ? item.warehouse_id : [],
                criteries,
                !!item.id);
        }

        Comboboxes.measures(
            "#measure",
            item && item.measure_id ? item.measure_id : [],
            {},
            !!item.id);

        Comboboxes.itemGroups(
            "#item-group",
            item && item.group_id ? item.group_id : [],
            {});

        var code  = item && item.code ? item.code : "";

        jQuery("#item-code").textbox("setValue", code);
        jQuery("#item-code").textbox({"disabled": CONSTANTS.AUTO_GENERATE_CODES ? true : code !== ""});

        jQuery("#item-print-name").textbox(
            "setValue",
            item && item.printName ? item.printName : ""
        );

        jQuery("#item-min-quantity").numberbox(
            "setValue",
            item && item.minQuantity ? item.minQuantity : ""
        );

        jQuery("#item-id").val(item && item.id ? item.id : "");
    }

    function saveItem() {
        var item = {};
        var endpoint;

        item.name = jQuery("#item-name").textbox("getValue");
        item.code = jQuery("#item-code").textbox("getValue");
        item.measure = jQuery("#measure").combobox('getValue');
        item.group = jQuery("#item-group").combobox('getValue');
        item.min_quantity = jQuery("#item-min-quantity").numberbox('getValue');
        item.print_name = jQuery("#item-print-name").textbox('getValue');

        if(CONSTANTS.ITEMS_WITH_WAREHOUSE) {
            item.warehouse = jQuery("#warehouse").combobox('getValue');
        }

        if (item.name === "" || item.measure === "" || (CONSTANTS.ITEMS_WITH_WAREHOUSE && item.warehouse === "")) {
            jQuery.messager.alert(
                "Грешка",
                "Моля попълнете всички задължителни полета."
            );
            return false;
        }

        if (jQuery("#item-id").val() !== "") {
            item.id = parseInt(jQuery("#item-id").val());
            endpoint = Items.editItem(item);
        } else {
            endpoint = Items.addItem(item);
        }

        endpoint
            .done(function(data) {
                data.result.warehouse_id = data.result.warehouse.id;
                data.result.item_name = data.result.name;
                data.result.measure_name = data.result.measure.name;
                Utils.selectItem(data.result);
                jQuery("#win-add-edit-item").window("close");
                itemsGrid.datagrid("reload");
            })
            .fail(function(errorObj) {
                jQuery.messager.alert(
                    "Грешка",
                    errorObj.getMessage(),
                    "warning"
                );
            });
    }

    function deleteItem(item) {
        Items.deleteItem(item)
            .done(function() {
                itemsGrid.datagrid("reload");
            })
            .fail(function(errorObj) {
                jQuery.messager.alert(
                    "Грешка",
                    errorObj.getMessage(),
                    "warning"
                );
            });
    }

    return {
        init,
        initItemsGrid
    };
});
