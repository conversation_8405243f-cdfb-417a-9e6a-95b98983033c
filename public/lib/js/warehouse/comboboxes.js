define([
    "easyui",
    "js/main/EasyUIRPCLoaders",
    "js/main/rpc_errors_handler",
    "js/warehouse/dynamic-warehouse-fields",
], function(
    _,
    EasyUIRPCLoaders,
    RpcErrorHandler
    // DynamicFields
) {
    function init(){}

    function companies(selector, selected = [], criteries = {}, disabled = false, required = false, multiple = false) {
        jQuery(selector).combobox({
            url: 'index.php?common-rpc=warehouse-companies-combobox',
            valueField: 'id',
            textField: 'name',
            disabled: disabled,
            required: required,
            rpcParams: [{
                criteries: criteries
            }],
            multiple: multiple,
            onLoadSuccess:function(){
                if (selected) {
                    if (multiple){
                        jQuery(this).combobox('setValues', selected);
                    } else {
                        jQuery(this).combobox('select', selected);
                    }
                }
            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }

    function companyGroups(selector, selected = [], criteries = {}, disabled = false, required = false, multiple = false) {
        jQuery(selector).combobox({
            url: 'index.php?common-rpc=warehouse-company-groups-combobox',
            valueField: 'id',
            textField: 'name',
            disabled: disabled,
            required: required,
            rpcParams: [{
                criteries: criteries
            }],
            multiple: multiple,
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
            onLoadSuccess: function() {
                if (selected) jQuery(this).combobox('select', selected)
            }
        });
    }

    function constants(selector, selected = [], criteries = {}, disabled = false, required = false, multiple = false) {
        jQuery(selector).combobox({
            url: "index.php?common-rpc=warehouse-constants-combobox",
            valueField: "key",
            textField: "text",
            editable: false,
            disabled: disabled,
            required: required,
            rpcParams: [criteries],
            multiple: multiple,
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter:function(data){
                return jQuery.map(data.result, function(el){
                        return {key:el.key,text:el.text, data:el}
                });
            },
            onLoadSuccess: function () {
                if (selected) jQuery(this).combobox('select', selected)
            },

        });
    }

    function allWarehouses(selector, selected = [], criteries = {}, disabled = false, required = false, multiple = false) {
        jQuery(selector).combobox({
            url: 'index.php?common-rpc=warehouse-warehouses-combobox',
            valueField: 'id',
            textField: 'name',
            disabled: disabled,
            required: required,
            rpcParams: [{
                criteries: criteries
            }],
            multiple: multiple,
            // onSelect: function(rec){
            //     if (rec.fields) {
            //         DynamicFields.initDynamicFields(rec.fields);
            //     }
            // },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter

        });
    }

    function mainWarehouses(selector, selected = [], criteries = {}, disabled = false, required = false, multiple = false) {
        jQuery(selector).combobox({
            url: "index.php?common-rpc=parent-warehouses-combobox",
            valueField: "id",
            textField: "name",
            disabled: disabled,
            required: required,
            multiple: multiple,
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: function(data) {
                var opts = jQuery(this).combobox("options");
                var emptyRow = {};
                emptyRow[opts.valueField] = "";
                emptyRow[opts.textField] = "Създай основен";
                data.result.unshift(emptyRow);
                return data.result;
            },
            onLoadSuccess: function() {
                if (selected) jQuery(this).combobox('select', selected)
                // jQuery(this).combobox("select", "");
            }
        });
    }

    function availableWarehouses(selector, selected = [], criteries = {}, disabled = false, required = false, multiple = false) {
        jQuery(selector).combobox({
            url: "index.php?common-rpc=warehouse-available-warehouses-combobox",
            valueField: "id",
            textField: "name",
            disabled: disabled,
            required: required,
            rpcParams: [{
                criteries: criteries
            }],
            multiple: multiple,
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
            onLoadSuccess: function() {
                if (selected) jQuery(this).combobox('select', selected)
            }
        });
    }

    function items(selector, selected = [], criteries = {}, disabled = false, required = false, multiple = false) {
        jQuery('#items').combobox({
            url: 'index.php?common-rpc=warehouse-items-combobox',
            valueField: 'id',
            textField: 'name',
            disabled: disabled,
            required: required,
            rpcParams: [{
                criteries: criteries
            }],
            multiple: multiple,
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
            onLoadSuccess: function() {
                if (selected) jQuery(this).combobox('select', selected)
            }
        });
    }

    function itemGroups(selector, selected = [], criteries = {}, disabled = false, required = false, multiple = false) {
        jQuery(selector).combobox({
            url: 'index.php?common-rpc=warehouse-item-groups-combobox',
            valueField: 'id',
            textField: 'name',
            disabled: disabled,
            required: required,
            rpcParams: [{
                criteries: criteries
            }],
            multiple: multiple,
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
            onLoadSuccess: function() {
                if (selected) jQuery(this).combobox('select', selected)
            }
        });
    }

    function measures(selector, selected = [], criteries = {}, disabled = false, required = false, multiple = false) {
        jQuery("#measure").combobox({
            url: "index.php?common-rpc=warehouse-measures-combobox",
            valueField: "id",
            textField: "name",
            disabled: disabled,
            required: required,
            multiple: multiple,
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
            onLoadSuccess: function () {
                if (selected) jQuery(this).combobox('select', selected)
            },

        });
    }

    function transactionTypes(selector, selected = [], criteries = {}, disabled = false, required = false, multiple = false) {
        jQuery(selector).combobox({
            data:[
                {
                    id:'ADD',
                    name:'Заприхождаване на артикули',
                },
                {
                    id:'ADD_PRODUCTION',
                    name:'Заприхождаване готова продуцкия',
                },
                {
                    id:'SUB',
                    name:'Изписване към контрагенти',
                },
                {
                    id:'SUB_PLOT',
                    name:'Изписване към парцели',
                },
                {
                    id:'SUB_MACHINE',
                    name:'Изписване към активи',
                },
                {
                    id:'RETURN',
                    name:'Връщане от парцел',
                },
                {
                    id:'TRANSFER',
                    name:'Прехвърляне',
                }
            ],
            valueField: "id",
            textField: "name",
            disabled: disabled,
            required: required,
            multiple: multiple,
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
            onLoadSuccess: function () {
                if (selected) jQuery(this).combobox('select', selected)
            },

        });
    }

    function fieldTypes(selector, selected = [], criteries = {}, disabled = false, required = false, multiple = false) {
        jQuery(selector).combobox({
            data:[
                {
                    value:'varchar',
                    text:'Текст',
                },
                {
                    value:'numeric',
                    text:'Число',
                }
            ],
            valueField: "value",
            textField: "text",
            disabled: disabled,
            required: required,
            multiple: multiple,
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
            onLoadSuccess: function () {
                if (selected) jQuery(this).combobox('select', selected)
            },

        });
    }

    return {
        companies,
        constants,
        mainWarehouses,
        availableWarehouses,
        items,
        measures,
        allWarehouses,
        transactionTypes,
        itemGroups,
        companyGroups,
        fieldTypes
    };
});
