define(function () {
    var constants = {};

    Object.defineProperty(constants, "COMPANY_TYPE_FARM", {
        value: 1
    });

    Object.defineProperty(constants, "COMPANY_TYPE_CONTRAGENT", {
        value: 2
    });

    Object.defineProperty(constants, "COMPANY_TYPE_MACHINE", {
        value: 3
    });

    // BEGIN System fields
    Object.defineProperty(constants, "WAREHOUSE_SYSTEM_TYPE_KEY", {
        value: 'system'
    });

    Object.defineProperty(constants, "WAREHOUSE_TYPE_MACHINES", {
        value: 'machines'
    });

    // END System fields

    // Configurations

    Object.defineProperty(constants, "ALLOW_EDIT_TRANSACTIONS", {
        value: true
    });

    Object.defineProperty(constants, "ALLOW_WAREHOUSE_SUM_QUANTITIES", {
        value: true
    });

    Object.defineProperty(constants, "ITEMS_WITH_WAREHOUSE", {
        value: true
    });

    Object.defineProperty(constants, "AUTO_GENERATE_CODES", {
        value: true
    });

    Object.defineProperty(constants, "MIN_QUANTITY_NOTIFICATION_PERCENT", {
        value: 25
    });

    Object.defineProperty(constants, "EXPIRY_DATE_NOTIFICATION_DAYS", {
        value: 30
    });
    
    Object.defineProperty(constants, "DOCUMENT_STATUS_ACTIVE", {
        value: 1
    });
    
    Object.defineProperty(constants, "DOCUMENT_STATUS_DELETED", {
        value: 0
    });
    
    Object.defineProperty(constants, "TRANSACTION_STATUS_ACTIVE", {
        value: 1
    });
    
    Object.defineProperty(constants, "TRANSACTION_STATUS_DELETED", {
        value: 0
    });
    
    Object.defineProperty(constants, "HISTORY_CLASS_ARTICLE", {
        value: 'Article'
    });

    Object.defineProperty(constants, "DOCUMENT_PRINT_TYPE_WEIGHT_NOTE", {
        value: 0
    });

    Object.defineProperty(constants, "DOCUMENT_PRINT_TYPE_ROAD_RECEIPT", {
        value: 1
    });

    Object.defineProperty(constants, "DOCUMENT_PRINT_TYPE_REQUEST_FOR_ITEMS", {
        value: 2
    });
    
    return constants;
});