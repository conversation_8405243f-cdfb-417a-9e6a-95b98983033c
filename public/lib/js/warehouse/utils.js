define([
    "easyui",
    "js/main/EasyUIRPCLoaders",
    "js/main/rpc_errors_handler",
    "TF/Rpc/Warehouse/Documents",
    "TF/Rpc/Warehouse/Transactions",
    "js/warehouse/comboboxes",
    "TF/Rpc/Warehouse/Warehouses",
    "js/warehouse/constants",
    "TF/Rpc/Warehouse/Utils",
    "js_external/locale/easyui-lang-bg",
], function(
    _,
    EasyUIRPCLoaders,
    RpcErrorHandler,
    Documents,
    Transactions,
    Comboboxes,
    Warehouses,
    Constants,
    RequestUtils
) {

    var addedItemsGrid = jQuery("#added-items-grid");
    var quickFilterTimer;
    let documentId = null;

    function init() {}

    function getPdfDocument(doc_id, showAfterSaveTransactionModal = true, printType = null) {
        var params = {
            document_id: doc_id,
            print_type: printType
        };

        Documents.getPdfDocument(params)
            .done(function (data) {
                window.open(data.result.file_path, '_blank');
                if(showAfterSaveTransactionModal) {
                    afterSaveTransactionModal();
                }
            })
            .fail(function (errorObj) {
                jQuery.messager.alert(
                    "Грешка",
                    errorObj.getMessage(),
                    "warning"
                );
            });
    }

    function openRightSlidePanel(openElement) {
        jQuery('.window-mask').first().show();
        jQuery("#" + openElement).show().panel({
            border: false,
            fit: true
        });

        const maskZindex = parseInt(jQuery('.window-mask').first().css('z-index'));

        jQuery('div#rightSlidePanel').css('z-index', maskZindex + 1);

        jQuery('div#rightSlidePanel').animate({
            right: '0px'
        });
        jQuery('div#rightSlidePanelCloseBtn').fadeIn(500);
    }

    function closeRightSlidePanel() {
        jQuery('.window-mask').first().hide();
        jQuery('div#rightSlidePanel').animate({
            right: '-825px'
        });

        jQuery("#rightSlidePanelContent ").children().hide();
        jQuery('div#rightSlidePanelCloseBtn').fadeOut(500);
    }

    function hidePageLoader() {
        // TODO: find a way to load css with r.js
        setTimeout(() => {
            jQuery("#body-init-loading").hide();
            jQuery("#bodydiv").css("visibility", "visible");
        }, 500);
    }

    function closePanels() {
        jQuery(this).closest('.window-body').window('close');
    }

    function quickFilterDataGrid(grid, toolbar, searchField, filters = {}){
        var params = quickFilterDataGridPrepareParams(toolbar, searchField, filters);
        clearTimeout(quickFilterTimer);

        if(params) {
            grid.datagrid('options').rpcParams = [params];
            quickFilterTimer = setTimeout(
                function () {
                    grid.datagrid('reload');
                }, 500);
        }
    }

    function quickFilterDataGridPrepareParams(toolbar, searchField, filters) {
        var value = jQuery(toolbar).find('.datagrid_search_box').val();
        var params = {
            criteries: {}
        };

        if(!jQuery.isEmptyObject(filters)) {
            for(filter in filters) {
                params.criteries[filter] = filters[filter];
            }
        }

        if(value.length > 1 || value.length === 0) {
            if(typeof searchField === "object") { //It is used for searching in dynamic json fields
                params.criteries.fields = {};
                for (var prop in searchField) {
                    if(value.length > 0) params.criteries.fields[prop] = value;
                    break;
                }
            } else {
                params.criteries[searchField] = value;
            }

            return params;
        }

        return false;
    }

    function storeTransaction(requestData) {
        if(!requestData) return;
        var endpoint;

        var document_id = requestData.document.document_id;
        switch (requestData.document.transaction_type) {
            case 'TRANSFER':
                if(document_id){
                    endpoint = Transactions.updateTransferTransaction(requestData);
                } else {
                    endpoint = Transactions.createTransferTransaction(requestData)
                }
                break;
            case 'ADD':
                if(document_id){
                    endpoint = Transactions.updateAddTransaction(requestData);
                } else {
                    endpoint = Transactions.createAddTransaction(requestData)
                }
                break;
            case 'ADD_PRODUCTION':
                if(document_id){
                    endpoint = Transactions.updateAddTransaction(requestData);
                } else {
                    endpoint = Transactions.createAddTransaction(requestData)
                }
                break;
            case 'RETURN':
                if(document_id){
                    endpoint = Transactions.updateAddTransaction(requestData);
                } else {
                    endpoint = Transactions.createAddTransaction(requestData)
                }
                break;
            case 'SUB':
                if(document_id){
                    endpoint = Transactions.updateSubTransaction(requestData);
                } else {
                    endpoint = Transactions.createSubTransaction(requestData)
                }
                break;
            case 'SUB_PLOT':
                if(document_id){
                    endpoint = Transactions.updateSubTransaction(requestData);
                } else {
                    endpoint = Transactions.createSubTransaction(requestData)
                }
                break;
            case 'SUB_MACHINE':
                if(document_id){
                    endpoint = Transactions.updateSubTransaction(requestData);
                } else {
                    endpoint = Transactions.createSubTransaction(requestData)
                }
                break;
            default: return;
        }

        endpoint
            .done(function (data) {
                if(data.closedDate !== undefined && data.closedDate !== null) {
                    const message = 'Искате ли да генерирате документ за печат ?';
                    documentId = data.docId;
                    if (['SUB', 'SUB_PLOT', 'SUB_MACHINE'].includes(requestData.document.transaction_type)) {
                        jQuery('#btn-sub-doc-complete-export').unbind().on('click',completeSubDocumentExport);
                        jQuery('#sub-document-export-close').unbind().on('click',closeSubDocumentExport);
                        jQuery('#sub-document-message').text(message)
                        jQuery('#win-choose-sub-doc-export-type').window('open');

                        return;
                    }

                    jQuery.messager.confirm('Потвърждение', message ,function(r){
                        if (r){
                            getPdfDocument(data.docId)
                        } else {
                            afterSaveTransactionModal()
                        }
                    });
                } else {
                    afterSaveTransactionModal()
                }
            })
            .fail(function (errorObj) {
                var exceptionData = errorObj.getExceptionData();
                jQuery.messager.alert(
                    "Грешка",
                    exceptionData.showOriginalMessage && exceptionData.showOriginalMessage === true ? errorObj.getOriginalMessage() : exceptionData.message,
                    "warning"
                );
            });
    }

    function afterSaveTransactionModal() {
        jQuery('#win-save-transaction-options').window("open");
        jQuery("#newTransactionBtn").on("click", function () {
            window.location.reload();
        });
        jQuery("#newSimilarTransactionBtn").on("click", function () {
            jQuery("#added-items-grid").datagrid('loadData', {total:0, rows:[], footer:generateFooter(0)});
            jQuery("#tr-document-number").textbox('reset');

            let transaction_type = jQuery("#transaction_type").val();
            if(transaction_type === 'SUB' || transaction_type === 'SUB_PLOT' || transaction_type === 'SUB_MACHINE') {
                let companyOutId = jQuery('#tr-company-id-out').val();
                initDocumentNumber(companyOutId);
            }

            jQuery('#win-save-transaction-options').window("close");
        });
        jQuery("#goToHomepageBtn").on("click", function () {
            window.location.href = "index.php?page=Warehouse.Home";
        });
    }

    function getTransactionData() {
        var company_out = jQuery("#tr-company-id-out").val();
        var document_id = jQuery("#document-id").val();
        var transaction_type = jQuery("#transaction_type").val();
        var company_in = jQuery("#tr-company-id-in").val();

        if(transaction_type === 'RETURN' || transaction_type === 'ADD_PRODUCTION') {
            company_out = company_in;
        }

        if(!company_out){
            jQuery.messager.alert('Грешка', 'Моля изберете доставчик.');
            return;
        }

        if(!company_in){
            var msg = 'Моля изберете стопанство.';
            if(transaction_type === 'SUB_MACHINE') msg = 'Моля изберете актив.';
            jQuery.messager.alert('Грешка', msg);

            return;
        }

        var type = jQuery("#tr-document-type").combobox('getValue');
        var dds = jQuery("#tr-document-dds").combobox('getValue');
        var date = jQuery("#tr-document-date").datebox('getValue');
        var number = jQuery("#tr-document-number").textbox('getValue');
        var vehicle_number = jQuery("#tr-vehicle-number").textbox('getValue');
        var driver_name = jQuery("#tr-driver-name").textbox('getValue');
        var plotName = jQuery("#tr-plot-name").length > 0 ? jQuery("#tr-plot-name").textbox('getValue') : null;
        var area = jQuery("#tr-area").length > 0 ? jQuery("#tr-area").textbox('getValue') : null;

        if(!type || !date || !number || !dds){
            jQuery.messager.alert('Грешка', 'Моля попълнете всички задължителни за документа полета.');
            return;
        }

        var addedItems = addedItemsGrid.datagrid("getData");

        if(addedItems.total === 0){
            jQuery.messager.alert('Грешка', 'Няма избрани артикули');
            return;
        }

        var requestData = {
            "document": {},
            "transactions": []
        };

        requestData.document = {
            "document_id": parseInt(document_id),
            "transaction_type": transaction_type,
            "company_out": parseInt(company_out),
            "company_in": parseInt(company_in),
            "type": type,
            "dds": dds,
            "number": number,
            "date": date,
            "vehicle_number": vehicle_number,
            "driver_name": driver_name,
            "plotName": plotName,
            "area": area,
        };

        for(var item of addedItems.rows) {
            if (item.transaction_status == 0) {
                // skip marked as deleted rows
                continue;
            }
            
            var warehouseFields = {};
            if(Array.isArray(item.warehouseFields)) {
                for (var field of item.warehouseFields) {
                    warehouseFields[field.key] = field.value;
                }
            }

            requestData.transactions.push({
                "transaction_id": parseInt(item.transaction_id),
                "item_id": parseInt(item.item_id),
                "article_id": item.article ? parseInt(item.article.id) : parseInt(item),
                "warehouse_id": parseInt(item.warehouse_id ? item.warehouse_id : item.comboboxes.warehouse.selected),
                "quantity": item.quantity,
                "single_price_no_dds": item.priceNoDDS,
                "single_price_with_dds": item.priceWithDDS,
                "batch": item.batch,
                "expiry_date": item.expiry_date,
                "note": item.note,
                "warehouseFields": warehouseFields,
            });
        }

        return requestData;
    }

    function setTransactionData(data) {
        if(data.transactionType === 'TRANSFER'){
            initTransferWarehousesCombobox('in', data.companyIn.id, data.warehouse_in, false);
            initTransferWarehousesCombobox('out', data.companyOut.id, data.warehouse_out, true);
            jQuery("#js-selected-farm-in").text(data.companyIn.name);
            jQuery("#js-selected-farm-out").text(data.companyOut.name);
        } else {
            //Contragent fields
            jQuery("#tr-company-name-out").textbox('setValue', data.companyOut.name);
            jQuery("#tr-company-eik-out").textbox('setValue', data.companyOut.fields.eik);
            jQuery("#tr-company-mol-out").textbox('setValue', data.companyOut.fields.mol);

            //Farm Fields
            jQuery("#tr-company-name-in").textbox('setValue', data.companyIn.name);
            jQuery("#tr-company-eik-in").textbox('setValue', data.companyIn.fields.eik);
            jQuery("#tr-company-mol-in").textbox('setValue', data.companyIn.fields.mol);
        }

        jQuery("#tr-company-id-out").val(data.companyOut.id);
        jQuery("#tr-company-id-in").val(data.companyIn.id);

        //Document fields
        Comboboxes.constants(
            "#tr-document-type",
            [data.type],
            {get: "documents.types." + data.transactionType.toLowerCase()},
            false,
            true
        );

        Comboboxes.constants(
            "#tr-document-dds",
            [data.ddsType],
            {get: "documents.dds"},
            false,
            true
        );

        jQuery("#document-id").val(data.id);
        jQuery("#transaction_type").val(data.transactionType);
        jQuery("#tr-document-date").datebox('setValue', new Date(data.date));
        jQuery("#tr-document-number").textbox('setValue', data.number);
        jQuery("#tr-vehicle-number").textbox('setValue', data.vehicleNumber);
        jQuery("#tr-driver-name").textbox('setValue', data.driverName);

        jQuery("#tr-plot-name").textbox('setValue', data.plotName);
        jQuery("#tr-area").textbox('setValue', data.area);

        var params = {
            criteries:{
                companies: data.companyIn.id
            }
        };

        Warehouses.getAvailableWarehouses(params)
            .done(function (res) {
                jQuery.each(data.transactions, function (index, transaction) {
                    var item = {};
                    if(transaction.item === undefined) return true;

                    item.transaction_id = transaction.id;
                    item.item_id = transaction.item.id;
                    item.item_name = transaction.item.name;
                    item.print_name = transaction.item.printName;
                    item.measure_name = transaction.item.measure.name;
                    item.quantity = transaction.quantity;
                    item.batch = transaction.batch;
                    item.expiryDate = transaction.expiryDate;
                    item.code = transaction.item.code;
                    item.note = transaction.note;
                    item.warehouse = transaction.warehouse.name;
                    item.article = transaction.article;
                    if(data.transactionType !== 'RETURN') {
                        item.priceWithDDS = transaction.priceWithDDS;
                        item.priceNoDDS = transaction.priceNoDDS;
                    }
                    item.comboboxes = [];
                    item.comboboxes.warehouse = {
                        "selected": transaction.warehouse.id,
                        "data": res.items
                    };

                    item.warehouseFields = [];
                    if(transaction.article.fields) {
                        jQuery.each(transaction.warehouse.fields, function (index, field) {
                            field['key'] = index;
                            field['value'] = transaction.article.fields[index]
                            item.warehouseFields.push(field)
                        })
                    }
                    item.transaction_status = transaction.status

                    insertItemInTransaction(item);
                });

                reloadPrices(data.ddsTypeValue)
            })
            .fail(function (errorObj) {
                jQuery.messager.alert(
                    "Грешка",
                    errorObj.getMessage(),
                    "warning"
                );
            });

        if(data) sessionStorage.removeItem("document_id");
    }

    function selectCompanyIn() {
        var index = jQuery(this).data("row-index");
        var datagrid =  jQuery(this).closest(".datagrid").find('.datagrid-f').attr('id');
        var rowData =  jQuery("#"+datagrid).datagrid("getRows")[index];

        jQuery("#tr-company-name-in").textbox('setValue', rowData.name);
        jQuery("#tr-company-eik-in").textbox('setValue', rowData.eik);
        jQuery("#tr-company-mol-in").textbox('setValue', rowData.mol);
        jQuery("#tr-company-id-in").val(rowData.id);

        if(rowData.fields && rowData.fields.tf) {
            jQuery("#tr-company-warehouse-id-in").val(rowData.fields.tf);
        }

        if(rowData.type_id === Constants.COMPANY_TYPE_MACHINE) {
            jQuery("#tr-company-number-in").textbox('setValue', rowData.number);
            jQuery("#tr-company-farm-in").textbox('setValue', rowData.farm);
        }

        closeRightSlidePanel();
    }

    function selectCompanyOut() {
        var index = jQuery(this).data("row-index");
        var datagrid =  jQuery(this).closest(".datagrid").find('.datagrid-f').attr('id');
        var rowData =  jQuery("#"+datagrid).datagrid("getRows")[index];
        var transaction_type = jQuery("#transaction_type").val();

        jQuery("#tr-company-name-out").textbox('setValue', rowData.name);
        jQuery("#tr-company-eik-out").textbox('setValue', rowData.eik);
        jQuery("#tr-company-mol-out").textbox('setValue', rowData.mol);
        jQuery("#tr-company-id-out").val(rowData.id);

        if(transaction_type === 'SUB' || transaction_type === 'SUB_PLOT' || transaction_type === 'SUB_MACHINE') {
            initDocumentNumber(rowData.id)
        }

        closeRightSlidePanel();
    }

    function selectCompany(role) {
        var index = jQuery(this).data("row-index");
        var datagrid =  jQuery(this).closest(".datagrid").find('.datagrid-f').attr('id');
        var rowData =  jQuery("#"+datagrid).datagrid("getRows")[index];

        jQuery("#tr-company-name-" + role).textbox('setValue', rowData.name);
        jQuery("#tr-company-eik-" + role).textbox('setValue', rowData.eik);
        jQuery("#tr-company-mol-" + role).textbox('setValue', rowData.mol);
        jQuery("#tr-company-id-" + role).val(rowData.id);

        closeRightSlidePanel();
    }

    function initTransferWarehousesCombobox(role, company, select = null, disabled = false) {
        Comboboxes.availableWarehouses("#farm-warehouses-combobox-"+role, select,{companies: [company]}, disabled);
        jQuery("#farm-warehouses-combobox-"+role).combobox({
            onSelect: function (selected) {
                if(Constants.ITEMS_WITH_WAREHOUSE && role === 'out') {
                    jQuery("#js-selected-farm-in").text('');
                    jQuery("#tr-company-id-in").val('');
                    initTransferWarehousesCombobox('in', company, selected.id, true);
                }
            }
        })
    }

    function insertItemInTransaction(item) {
        addedItemsGrid.datagrid('insertRow', {
            row: item
        });
        addedItemsGrid.datagrid("acceptChanges");
    }

    function initEditDocument(params) {
        Documents.getDocument(params)
            .done(function (data) {
                if (data) setTransactionData(data)
            })
            .fail(function (errorObj) {
                jQuery.messager.alert(
                    "Грешка",
                    errorObj.getMessage(),
                    "warning"
                );
            });
    }

    function clearEmptyStringArray(value) {
        var result = [];
        for (var element of value) {
            if(element !== "") result.push(element);
        }
        return result;
    }

    function reloadPrices(dds) {
        var priceSumNoDDS = 0;

        var datagridData = addedItemsGrid.datagrid("getRows");
        for(var row in datagridData) {
            if(isNaN(parseFloat(datagridData[row].priceNoDDS))) continue;
            priceSumNoDDS = priceSumNoDDS + (parseFloat(datagridData[row].priceNoDDS) * parseFloat(datagridData[row].quantity));

            addedItemsGrid.datagrid("updateRow", {index: row});
        }

        addedItemsGrid.datagrid('reloadFooter', generateFooter(dds, priceSumNoDDS));
    }

    function generateFooter(dds, priceSumNoDDS = null) {
        var priceSumWithDDS = priceSumNoDDS ? (((priceSumNoDDS * dds) * 100) / 100).toFixed(5) : 0.00000;
        var ddsValue = priceSumWithDDS ? (((priceSumWithDDS -  priceSumNoDDS) * 100) / 100).toFixed(5) : "";

        return [
            {
                item_name: "<b>Общо без ДДС:</b>",
                measure_name: priceSumNoDDS ? ((priceSumNoDDS * 100) / 100).toFixed(5) : '',
            },
            {
                item_name: "<b>ДДС:</b>",
                measure_name: ddsValue,
            },
            {
                item_name: "<b>Обща сума:</b>",
                measure_name: priceSumWithDDS,
            }
        ];
    }

    function initDocumentNumber(companyId) {
        var RequestUtilsParams = {
            transactionType: jQuery("#transaction_type").val(),
            documentType: jQuery("#tr-document-type").combobox('getValue'),
            companyId: companyId
        };

        RequestUtils.getDocumentNumber(RequestUtilsParams)
            .done(function (res) {
                jQuery("#tr-document-number").textbox({
                    value: res,
                    disabled: true
                });
            })
            .fail(function (errorObj) {
                jQuery.messager.alert(
                    "Грешка",
                    errorObj.getMessage(),
                    "warning"
                );
            });
    }

    function selectItem(itemData) {
        let selectedWarehouse = [];
        let disabledWarehouse = false;

        if(jQuery("#transaction_type").val() === "RETURN" || Constants.WAREHOUSE_TYPE_MACHINES) {
            selectedWarehouse = itemData.warehouse_id;
            disabledWarehouse = true;
        }

        Comboboxes.availableWarehouses(
            "#available-warehouses-combobox",
            selectedWarehouse,
            {companies: jQuery("#tr-company-id-in").val()},
            disabledWarehouse
        );

        //rename the id key to item_id to avoid possible problems
        if(!itemData.item_id){
            itemData['item_id'] = itemData.id;
            delete itemData['id'];
        }

        sessionStorage.setItem("selectedItem", JSON.stringify(itemData));

        //Reset Warehouse Fields
        jQuery(".warehouseFieldsList .container-box").empty();
        jQuery("#warehouseFields").hide();

        jQuery("#win-add-transactions").window("open");
    }

    function getSelectedSubDocumentExportType() {
        let exportType = null;
        if (jQuery('#choose-sub-doc-export-type-note > input').is(':checked') == true) {
            exportType = Constants.DOCUMENT_PRINT_TYPE_WEIGHT_NOTE;
        }

        if (jQuery('#choose-sub-doc-export-type-drive > input').is(':checked') == true) {
            exportType = Constants.DOCUMENT_PRINT_TYPE_ROAD_RECEIPT
        }

        if (jQuery('#choose-sub-doc-export-type-request > input').is(':checked') == true) {
            exportType = Constants.DOCUMENT_PRINT_TYPE_REQUEST_FOR_ITEMS
        }

        return exportType;
    }

    function completeSubDocumentExport() {
        const exportType = getSelectedSubDocumentExportType();

        getPdfDocument(documentId, true, exportType);

        jQuery('#win-choose-sub-doc-export-type').window('close');
    }

    function closeSubDocumentExport() {
        afterSaveTransactionModal();
        jQuery('#win-choose-sub-doc-export-type').window('close');
    }
    

    return {
        init,
        getPdfDocument,
        openRightSlidePanel,
        closeRightSlidePanel,
        hidePageLoader,
        closePanels,
        quickFilterDataGrid,
        quickFilterDataGridPrepareParams,
        storeTransaction,
        getTransactionData,
        insertItemInTransaction,
        initTransferWarehousesCombobox,
        initEditDocument,
        clearEmptyStringArray,
        selectCompanyIn,
        selectCompanyOut,
        selectCompany,
        reloadPrices,
        generateFooter,
        initDocumentNumber,
        selectItem,
        getSelectedSubDocumentExportType
    };
});
