define([
    "easyui",
    "js/main/EasyUIRPCLoaders",
    "js/main/rpc_errors_handler",
    "js/warehouse/companies-grid",
    "js/warehouse/documents-grid",
    "js/warehouse/items-grid",
    "js/warehouse/measure-grid",
    "js/warehouse/farms-grid",
    "js/warehouse/constants"
], function(
    _,
    EasyUIRPCLoaders,
    RpcErrorHandler,
    companies_grid,
    documents_grid,
    items_grid,
    measure_grid,
    farms_grid,
    CONSTANTS
) {
    function init() {
        bindEventsListeners();
    }

    function bindEventsListeners() {

        jQuery("#btn-manage-items").on("click", function() {
            items_grid.initItemsGrid();
            jQuery("#win-manage-items").window("open");

            return false;
        });

        jQuery("#btn-manage-documents").on("click", function() {
            documents_grid.initDocumentsGrid();
            jQuery("#win-manage-documents").window("open");

            return false;
        });

        jQuery("#btn-manage-measures").on("click", function() {
            measure_grid.initMeasureGrid();
            jQuery("#win-manage-measures").window("open");

            return false;
        });

        jQuery("#btn-manage-contragents").on("click", function() {
            companies_grid.initContragentsGrid({types: [CONSTANTS.COMPANY_TYPE_CONTRAGENT]});
            jQuery("#win-manage-contragents").window("open");

            return false;
        });

        jQuery("#btn-manage-farms").on("click", function() {
            farms_grid.initFarmsGrid();
            jQuery("#win-manage-farms").window("open");

            return false;
        });
    }


    return {
        init
    };
});
