define([
    "easyui",
    "js/main/EasyUIRPCLoaders",
    "js/main/rpc_errors_handler",
    "js/warehouse/constants",
    "TF/Rpc/Warehouse/Warehouses",
], function(
    _,
    EasyUIRPCLoaders,
    RpcError<PERSON>andler,
    CONSTANTS,
    Warehouses
) {
    function init() {

    }

    function getFieldsValues() {
        let item = [];
        let filterValueFound = false;
        jQuery(".warehouse-field").each(function (index, element) {
            if(element.key === CONSTANTS.WAREHOUSE_SYSTEM_TYPE_KEY) return;//Skip the system files

            if(element.value.length){
                filterValueFound = true
                item.push({
                    key: element.id,
                    label: jQuery('label[for="'+element.id+'"]').text(),
                    value: element.value,
                });
            }
        });

        if (filterValueFound === false) {
            return '';
        }

        return item;
    }

    function initWarehouseItems(warehouseId) {
        Warehouses.getWarehouse({
            id: warehouseId
        })
            .done(function (data) {
                jQuery(".warehouseFieldsList .container-box").empty();
                jQuery("#warehouseFields").hide();

                if(data.fields) {
                    initDynamicFields(data.fields, 'col-3');
                }
            })
            .fail(function (errorObj) {
                jQuery.messager.alert(
                    "Грешка",
                    errorObj.getMessage(),
                    "warning"
                );
            });
    }

    function initDynamicFields(fields, className = '') {
        var fieldTemplate = '<div class="'+className+'">' +
                                '<label for="{key}" class="label-above">{label}</label>' +
                                '<input id="{key}" class="easyui-textbox warehouse-field" data-options="" style="width: 171px;">' +
                            '</div>';

        var fieldsArr = [];
        //Convert data in order to be proper for fields drawer
        jQuery.each(fields, function (index, value) {
            let el = value;
            el['key'] = index;
            fieldsArr.push(el)
        });

        drawWarehouseFields(fieldsArr, "#warehouseFields", fieldTemplate);
    }

    function drawWarehouseFields(fields, wrapper, fieldTemplate) {
        jQuery(".warehouseFieldsList .container-box").empty();
        var customFieldsCount = 0;
        jQuery.each(fields, function (index, field) {
            if(field.key === CONSTANTS.WAREHOUSE_SYSTEM_TYPE_KEY) return; //Skip the system files

            var res = fieldTemplate.replace(/{\w+}/g, function(placeholder) {
                var v = placeholder.replace(/[{}]/g, '');
                return v in field ? field[v] : placeholder;
            });

            jQuery(res).appendTo(wrapper + " .container-box");
            jQuery('#'+field.key).textbox({
                value: field.value ? field.value : '',
            });
            customFieldsCount++;
        });

        if(customFieldsCount > 0) {
            jQuery(wrapper).show();
        }
    }

    return {
        initWarehouseItems,
        initDynamicFields,
        drawWarehouseFields,
        getFieldsValues
    };
});