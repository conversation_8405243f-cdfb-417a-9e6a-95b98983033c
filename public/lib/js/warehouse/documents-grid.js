define([
    "jquery",
    "easyui",
    "js/main/EasyUIRPCLoaders",
    "js/main/rpc_errors_handler",
    "TF/Rpc/Warehouse/Documents",
    "js/warehouse/utils",
    "js/warehouse/comboboxes",
    "js/warehouse/constants",
    "js/users/users-consts",
], function(
    jQuery,
    _,
    EasyUIRPCLoaders,
    RpcErrorHandler,
    Documents,
    Utils,
    Comboboxes,
    CONSTANTS,
    UserConsts
) {

    var documentsGrid;

    "use strict";
    function init() {
    }


    function bindEventsListeners() {
        jQuery('#btn-document-filter').on('click', documentsFilter);
        jQuery('#addInvoiceBtn').on('click', addDocumentsInvoice);
        jQuery('#btn-sub-doc-complete-export').on('click',selectedSubDocumentExport);
    }

    function selectedSubDocumentExport()
    {
        const exportType = Utils.getSelectedSubDocumentExportType();
        const selectedRow = documentsGrid.datagrid('getSelected');
        Utils.getPdfDocument(selectedRow.id, false, exportType);

        jQuery('#win-choose-sub-doc-export-type').window('close');
    }

    function initDocumentsGrid() {
        bindEventsListeners();
        initFilterFields();
        documentsGrid = jQuery("#documents-grid");
        documentsGrid.datagrid({
            autoRowHeight: true,
            autoRowWidth: true,
            checkOnSelect: false,
            selectOnCheck: false,
            url: "index.php?warehouse-rpc=warehouse-documents",
            rpcMethod: "read",
            pagination: true,
            fit: true,
            border: false,
            singleSelect: true,
            rpcParams: [
                {
                    criteries: {}
                }
            ],
            frozenColumns: [[
                {
                    field: 'ck',
                    checkbox: true
                }
            ]],
            columns: [
                [
                    {
                        field: "transaction_type_name",
                        title: "<b>Операция</b>",
                        width: 120
                    },
                    {
                        field: "number",
                        title: "<b>Номер</b>",
                        width: 140
                    },
                    {
                        field: "company_in_name",
                        title: "<b>Получател</b>",
                        width: 160
                    },
                    {
                        field: "company_out_name",
                        title: "<b>Доставчик</b>",
                        width: 160
                    },
                    {
                        field: "typeName",
                        title: "<b>Тип на документа</b>",
                        width: 120
                    },
                    {
                        field: "document_date",
                        title: "<b>Док. дата</b>",
                        width: 100
                    },
                    {
                        field: "closed_at",
                        title: "<b>Дата на прикл.</b>",
                        width: 130
                    },
                    {
                        field: "invoice_number",
                        title: "<b>Номер на фактура</b>",
                        width: 130
                    },
                    {
                        field: "invoice_date",
                        title: "<b>Дата на фактура</b>",
                        width: 130
                    }
                ]
            ],
            toolbar: [
                {
                    id: "btn_print_event",
                    text: "Преглед и печат",
                    iconCls: "icon-print",
                    handler: function() {
                        var selectedRow = documentsGrid.datagrid('getSelected');
                        if(!selectedRow) jQuery.messager.alert('Предупреждение', 'Не сте избрали документ.');
                        if(selectedRow.closed_at) {
                            if (selectedRow.type == 'WAREHOUSE_NOTE_OUT') {
                                jQuery('#win-choose-sub-doc-export-type').window('open');
                                return;
                            }

                            Utils.getPdfDocument(selectedRow.id, false, null)
                        } else {
                            jQuery.messager.alert('Предупреждение', 'Избраният документ не е приключен и не може да бъде разпечатан.');
                        }

                    }
                },
                {
                    id: "btn_doc_edit_event",
                    text: "Редактиране на документ",
                    iconCls: "icon-edit",
                    handler: function() {
                        var selectedRow = documentsGrid.datagrid('getSelected');
                        if(!selectedRow) jQuery.messager.alert('Предупреждение', 'Не сте избрали документ.');
                            if((selectedRow.closed_at || !CONSTANTS.ALLOW_EDIT_TRANSACTIONS) && userLevel != USERS_ADMIN_FLAG) {
                                jQuery.messager.alert('Предупреждение', 'Не могат да бъдат редактирани приключени документи');
                                return;
                            }
                            
                            if (selectedRow.document_status === CONSTANTS.DOCUMENT_STATUS_DELETED) {
                                jQuery.messager.alert('Предупреждение', 'Не могат да бъдат редактирани анулирани документи');
                                return;
                            }
                            
                            sessionStorage.setItem("document_id", selectedRow.id);

                            switch (selectedRow.transaction_type) {
                                case 'ADD': window.location.href = "index.php?page=WarehouseAddTransaction.Home";break;
                                case 'ADD_PRODUCTION': window.location.href = "index.php?page=WarehouseAddProductionTransaction.Home";break;
                                case 'SUB': window.location.href = "index.php?page=WarehouseSubContragentsTransaction.Home";break;
                                case 'SUB_MACHINE': window.location.href = "index.php?page=WarehouseSubMachinesTransaction.Home";break;
                                case 'SUB_PLOT': window.location.href = "index.php?page=WarehouseSubPlotsTransaction.Home";break;
                                case 'TRANSFER': window.location.href = "index.php?page=WarehouseTransferTransaction.Home";break;
                                case 'RETURN': window.location.href = "index.php?page=WarehouseReturnTransaction.Home";break;
                                default: jQuery.messager.alert('Предупреждение', 'Невалидна операция');
                            }
                    }
                },
                {
                    id: "btn_doc_add_invoice",
                    text: "Добавяне на фактура",
                    iconCls: "icon-agreements",
                    handler: function() {
                        var selectedRows = documentsGrid.datagrid('getChecked');
                        if(selectedRows.length === 0) {
                            jQuery.messager.alert('Предупреждение', 'Не сте избрали документ. Поставете отметка пред един и или повече документи, към които искате да добавите фактура');
                            return;
                        }

                        for (const row of selectedRows) {
                            if(row.invoice_number) {
                                jQuery.messager.alert('Предупреждение', 'Документ с номер ' + row.number + ' вече има добавена фактура.');
                                return;
                            }
                        }

                        openDocumentsInvoiceModal();
                    }
                },
                {
                    id: "btn_doc_edit_invoice",
                    text: "Редактиране на данни за фактура",
                    iconCls: "icon-agreements",
                    handler: function() {
                        var selectedRow = documentsGrid.datagrid('getChecked');
                        if(selectedRow.length === 0) {
                            jQuery.messager.alert('Предупреждение', 'Не сте избрали документ. Поставете отметка пред документа, за който искате да редактирате данните за фактурата');
                            return;
                        }

                        if(selectedRow.length > 1) {
                            jQuery.messager.alert('Предупреждение', 'Не може да редактирате данните за фактура на повече от един документ.');
                            return;
                        }
                        if(!selectedRow[0].invoice_number) {
                            jQuery.messager.alert('Предупреждение', 'Документът няма добавена фактура.');
                            return;
                        }

                        openDocumentsInvoiceModal(selectedRow[0]);
                    }
                },
                {
                    id: "btn_doc_remove",
                    text: "Изтриване на документ",
                    iconCls: "icon-delete",
                    handler: function() {
                        let selectedRow = documentsGrid.datagrid('getSelected');
                        if(!selectedRow) {
                            jQuery.messager.alert('Предупреждение', 'Не сте избрали документ.');
                            return;
                        }

                        if(selectedRow.document_status === 0){
                            jQuery.messager.alert('Предупреждение', 'Не може да анулирате документ повторно.');
                            return;
                        }
            
                        removeDocument(selectedRow);
                    }
                },
                {
                    id: "btn_doc_filter_event",
                    text: "Филтър",
                    iconCls: "icon-filter",
                    handler: function() {
                        jQuery("#win-doc-filter").window("open");
                    }
                },
                {
                    id: "btn_doc_clear_filter_event",
                    text: "Покажи всички",
                    iconCls: "icon-clear-filter",
                    handler: function() {
                        documentsClearFilter();
                    }
                },
                {
                    id: 'btn_info_event',
                    text: 'Легенда',
                    iconCls: 'icon-legend',
                    handler: function () {
                        jQuery.messager.alert({
                                title: 'Легенда',
                                width: 700,
                                msg:'<ul>' +
                                    '<li><div style="width: 30px; height: 10px; background-color: #fb9d37; float: left; margin-top: 4px;margin-right: 5px;"></div>Документите маркирани с този цвят са не приключени документи.</li>' +
                                    '</ul>'
                            }
                        );
                    }
                }
            ],
            rowStyler: function(index, row) {
                let style = [];
                if (!row.closed_at){
                    style.push('background-color:#fb9d37');
                }
                
                if (row.document_status === 0) {
                    style.push('text-decoration: line-through');
                    style.push('color: #aaa');
                }
        
                return style.join(';');
            },
            onBeforeSelect: function(rowIndex, rowData) {},
            onBeforeCheck: function(rowIndex, rowData) {
                if(rowData.transaction_type !== 'ADD') {
                    jQuery.messager.alert("Грешка","Тази операция е позволена само за документи от тип Заприхождаване","warning");
                    return false
                };
            },
            onLoadSuccess:function(data){
                jQuery(this).datagrid('getPanel').find('div.datagrid-header input[type=checkbox]').attr('disabled','disabled');
            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter:
                EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }

    function documentsFilter() {
        documentsGrid.datagrid('options').rpcParams = [{
            criteries: {
                number:  jQuery("#doc_number").textbox("getValue") ? jQuery("#doc_number").textbox("getValue") : '',
                invoiceNumber:  jQuery("#invoice_number_filter").textbox("getValue") ? jQuery("#invoice_number_filter").textbox("getValue") : '',
                transaction_types: Utils.clearEmptyStringArray(jQuery("#transactionTypes").combobox("getValues")),
                companiesIn: Utils.clearEmptyStringArray(jQuery("#document-company-in").combobox("getValues")),
                companiesOut: Utils.clearEmptyStringArray(jQuery("#document-company-out").combobox("getValues")),
                beforeDocDate: jQuery("#before_doc_date").datebox("getValue") ? jQuery("#before_doc_date").datebox("getValue") : "",
                afterDocDate: jQuery("#after_doc_date").datebox("getValue") ? jQuery("#after_doc_date").datebox("getValue") : "",
                beforeInvoiceDate: jQuery("#before_invoice_date").datebox("getValue") ? jQuery("#before_invoice_date").datebox("getValue") : "",
                afterInvoiceDate: jQuery("#after_invoice_date").datebox("getValue") ? jQuery("#after_invoice_date").datebox("getValue") : "",
                beforeCreatedAt: jQuery("#before_created_at").datebox("getValue") ? jQuery("#before_created_at").datebox("getValue") : "",
                afterCreatedAt: jQuery("#after_created_at").datebox("getValue") ? jQuery("#after_created_at").datebox("getValue") : "",
                beforeUpdatedAt: jQuery("#before_updated_at").datebox("getValue") ? jQuery("#before_updated_at").datebox("getValue") : "",
                afterUpdatedAt: jQuery("#after_updated_at").datebox("getValue") ? jQuery("#after_updated_at").datebox("getValue") : "",
                withInvoice: jQuery("#withInvoice").prop('checked'),
                noInvoice: jQuery("#noInvoice").prop('checked'),
                documentStatus: jQuery("#documentStatus").prop('checked') ? CONSTANTS.DOCUMENT_STATUS_DELETED : CONSTANTS.DOCUMENT_STATUS_ACTIVE,
            }
        }];
        documentsGrid.datagrid('reload');
        jQuery("#win-doc-filter").window("close");
    }

    function documentsClearFilter() {
        documentsGrid.datagrid('options').rpcParams = [{
            criteries: {}
        }];
        documentsGrid.datagrid('reload');
        initFilterFields();
    }

    function initFilterFields(){
        Comboboxes.companies("#document-company-in");
        
        Comboboxes.companies(
            "#document-company-out",
            [],
            {types:[CONSTANTS.COMPANY_TYPE_CONTRAGENT, CONSTANTS.COMPANY_TYPE_FARM]},
        );
        Comboboxes.transactionTypes("#transactionTypes");
        jQuery("#doc_number").textbox("clear");
        jQuery("#invoice_number_filter").textbox("clear");
        jQuery("#after_doc_date").datebox("setValue", "");
        jQuery("#before_doc_date").datebox("setValue", "");
        jQuery("#after_created_at").datebox("setValue", "");
        jQuery("#before_created_at").datebox("setValue", "");
        jQuery("#after_updated_at").datebox("setValue", "");
        jQuery("#before_updated_at").datebox("setValue", "");
        jQuery("#after_invoice_date").datebox("setValue", "");
        jQuery("#before_invoice_date").datebox("setValue", "");
        jQuery("#withInvoice").prop('checked', false);
        jQuery("#noInvoice").prop('checked', false);
        jQuery("#documentStatus").prop('checked', false);
    }

    function addDocumentsInvoice() {
        var selectedDocuments = [];
        let documents =  documentsGrid.datagrid('getChecked');

        for(const document of documents){
            selectedDocuments.push(document.id)
        }

        var invoiceNumber = jQuery("#invoice_number").textbox("getValue");
        if(!invoiceNumber) {
            jQuery.messager.alert("Грешка","Не сте попълнили номера на фактурата","warning");
            return;
        }

        var invoiceDate = jQuery("#invoice_date").datebox("getValue");
        if(!invoiceDate) {
            jQuery.messager.alert("Грешка","Не сте попълнили датата на фактурата","warning");
            return;
        }

        var params = {
            "invoiceNumber": invoiceNumber,
            "invoiceDate": invoiceDate,
            "selectedDocuments": selectedDocuments,
        };

        jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да извършите тази операция?', function (r) {
            if (r) {
                Documents.addDocumentsInvoice(params)
                    .done(function () {
                        jQuery("#win-add-invoice").window("close");
                        documentsGrid.datagrid("reload");
                    })
                    .fail(function (errorObj) {
                        jQuery.messager.alert(
                            "Грешка",
                            errorObj.getMessage(),
                            "warning"
                        );
                    });
            }
        });


    }

    function openDocumentsInvoiceModal(document) {
        jQuery("#invoice_number").textbox("setValue", document && document.invoice_number ? document.invoice_number : "");
        jQuery("#invoice_date").datebox("setValue", document && document.invoice_date ? document.invoice_date : "");

        jQuery("#win-add-invoice").window("open");
    }
    
    function removeDocument(row)
    {
        jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да изтриете документа и всички операции към него?', function (r) {
            if (r) {
                Documents.removeDocument({
                    documentId: row.id
                })
                .done(function () {
                    documentsGrid.datagrid("reload");
                })
                .fail(function (errorObj) {
                    jQuery.messager.alert(
                        "Грешка",
                        errorObj.getOriginalMessage(),
                        "warning"
                    );
                });
            }
        });
    }

    return {
        init,
        initDocumentsGrid
    };
});
