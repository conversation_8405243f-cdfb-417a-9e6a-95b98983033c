define([
    "easyui",
    "js/main/EasyUIRPCLoaders",
    "js/main/rpc_errors_handler",
    "TF/Rpc/Warehouse/HistoryLog"
], function(
    _,
    EasyUIRPCLoaders,
    RpcError<PERSON>andler,
    HistoryLog
) {
    
    function init() {
    
    }
    
    /**
     *
     * @param className
     */
    function bindClickEvents( className )
    {
        jQuery('[id^="history-button-"]').each(function (){
            let objectId = this.id.split('-').pop();
 
            jQuery(this).on('click', {className: className, objectId: objectId}, function (event) {
                loadHistoryDatagrid.call(this, event.data.className,  parseInt(event.data.objectId));
            });
        });
    }
    
    /**
     *
     * @param value
     * @param row
     * @param index
     * @returns {string}
     */
    function formatLoggableData(value, row, index) {
        if (typeof value === 'undefined' || value === null) return;
        
        let innerHtml = "";
    
        Object.keys(value).forEach(key => {
            innerHtml += "<div>"+key + ": " + value[key] + "</div>";
        });
        
        return innerHtml
    }
    
    function formatLoggableDate(value, row, index) {
        if (typeof value === 'undefined' || value === null) return;

        let date = new Date(value.timestamp * 1000);
        
        return date.toLocaleString();
    }
    
    /**
     *
     * @param className
     * @param objectId
     */
    function loadHistory( className, objectId )
    {
        HistoryLog.loadHistory([{className, objectId}])
            .done(function(data) {
            
            })
            .fail(function (error) {
                jQuery.messager.alert('Грешка', 'Възникна грешка при обработката на данните', 'warning');
            });

    }
    
    function loadHistoryDatagrid(className, objectId)
    {
        jQuery("#win-history-log").window("open");
        let historyGrid = jQuery('#history-log-table');
        
        historyGrid.datagrid({
            iconCls: 'icon-edit-geometry',
            nowrap: true,
            autoRowHeight: true,
            striped: true,
            pageSize: 30,
            fit: true,
            fitColumns: true,
            showFooter: true,
            url: 'index.php?warehouse-rpc=warehouse-history',
            rpcMethod: 'loadHistory',
            idField: '',
            sortName: 'id',
            sortOrder: 'desc',
            border: false,
            singleSelect: true,
            rpcParams: [{className,objectId}],
            columns: [[{
                field: 'action',
                title: '<b>Действие</b>',
                sortable: true,
                width: 200
            }, {
                field: 'loggedAt',
                title: '<b>Дата</b>',
                sortable: true,
                width: 220,
                formatter: formatLoggableDate
            }, {
                field: 'version',
                title: '<b>Версия</b>',
                sortable: true,
                width: 150
            }, {
                field: 'username',
                title: '<b>Потребител</b>',
                sortable: true,
                width: 200
            }, {
                field: 'data',
                title: 'Исторически данни',
                sortable: true,
                width: 180,
                formatter: formatLoggableData
            }]],
            pagination: false,
            rownumbers: false,
            toolbar: '#reports_toolbar',
            onBeforeLoad: function() {
            
            },
            onLoadSuccess: function () {
            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });
    }
    
    return {
        bindClickEvents,
        loadHistory
    };
})