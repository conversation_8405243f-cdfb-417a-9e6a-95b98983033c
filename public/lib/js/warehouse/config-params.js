define([
    "easyui",
    "js/main/EasyUIRPCLoaders",
    "js/main/rpc_errors_handler",
    "TF/Rpc/Warehouse/WarehouseConfig"
], function(
    _,
    EasyUIRPCLoaders,
    RpcErrorHandler,
    WarehouseConfig
) {
    let configParamsFields = [];
    
    function init()
    {
        jQuery('#btn-save-warehouse-config-params').unbind('click', saveData).bind('click', saveData);
        loadData();
    }
    
    function loadData()
    {
        configParamsFields = [];
        WarehouseConfig.getWarehouseConfigParams()
            .done(function(data) {
                data.forEach(function (param) {
                    if (param.parameterValue === true) {
                        jQuery("#yes-warehouse-"+ param.parameterName.replaceAll('_','-') +" input").prop("checked", true);
                    }

                    configParamsFields.push(param);
                });
                jQuery("#win-edit-warehouse-config-params").window("open");
            })
            .fail(function (error) {
                jQuery.messager.alert('Грешка', 'Възникна грешка при обработката на данните', 'warning');
                jQuery("#win-edit-warehouse-config-params").window("close");
            });
    }
    
    function saveData()
    {
        configParamsFields.forEach(function (param) {
            param.parameterValue = jQuery("#yes-warehouse-"+ param.parameterName.replaceAll('_','-') +" input").is(':checked');
        });

        WarehouseConfig.editWarehouseConfigParams(configParamsFields)
            .done(function () {
                jQuery.messager.alert('Информация','Конфигурационните параметри са обработени успешно','info');
            })
            .fail(function (error) {
                jQuery.messager.alert('Грешка', 'Възникна грешка при обработката на данните', 'warning');
            });
    }
    
    return {
        init,
        loadData,
        saveData
    };
});