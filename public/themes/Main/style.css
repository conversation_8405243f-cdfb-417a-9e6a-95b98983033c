@CHARSET "UTF-8";
body {
    font: 12px/16px <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>erd<PERSON>;
}

h1 {
    margin-right: 20px;
    float: right;
    margin-top: 56px;
    font-size: 14px;
}

input {
    border: 1px solid #ddd;
    padding: 2px;
    border-radius: 5px;
}

textarea {
    border-radius: 5px;
    font-family: Arial, sans-serif;
}

fieldset, textarea {
    border: 1px solid #ddd !important;
}

input[type='text'], input[type='password'] {
    height: 28px;
}

.fon {
    background: url(fon2.jpg);
}

#fksu {
    padding: 10px 0px;
    font-weight: bold;
    font-size: 18px;
}

#topic {
    padding: 10px 0px;
    font-size: 15px;
}

#holder {
    width: 100%;
    text-align: center;
}

#footer {
    margin: auto;
    width: 960px;
    padding-top: 0px;
    padding-bottom: 20px;
}

.login-holder {
    width: 430px;
    margin: 0 auto;
    margin-top: 30px;
    margin-bottom: 30px;
}

.nort-regeion {
    height: 65px;
    background: #F1F1F1;
    color: #222222;
    padding: 10px;
    text-align: center;
}

.south-region {
    height: 40px !important;
    background: #f1f1f1;
    color: #222222;
    padding: 13px;
}

.login-panel {
    border: 1px solid #004a71;
    padding: 20px;
    background: #e8eef4;
    color: #000000;
}

.locker-icon {
    width: 355px;
    height: 22px;
    background: url(locker-icon.jpg) no-repeat;
    border-bottom: 1px solid #004a71;
    margin-bottom: 10px;
    padding-left: 30px;
    font-size: 16px;
    font-weight: bold;
    text-align: left;
    padding-bottom: 10px;
    padding-top: 5px;
}

.title {
    border-bottom: 1px solid #004a71;
    margin-top: 5px;
    margin-bottom: 20px;
    padding-bottom: 5px;
    height: 45px;
}

.logo {
    background: url(logo.png) no-repeat;
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 10000;
    width: 265px;
    height: 75px;
}

.logo-login {
    background: url(logo.png) no-repeat;
    z-index: 10000;
    float: left;
    width: 265px;
    height: 75px;
    margin-top: 10px;
    margin-left: 10px;
}

.toshel-logo {
    background: url(toshel.png) no-repeat;
    float: right;
    width: 163px;
    height: 45px;
    margin-top: 10px;
}

.user-info {
    float: right;
    width: 250px;
    height: 40px;
}

.info {
    float: left;
    font-size: 18px;
    width: 300px;
    text-align: left;
    margin-top: 20px;
    margin-left: 10px;
}

.info-inner {
    float: left;
    font-size: 20px;
    width: 180px;
    height: 33px;
    padding-top: 7px;
    text-align: center;
    background: #fff;

}

.required {
    color: #c60038;
}

.filters td {
    padding: 5px 5px 5px 0px;
    text-align: left;
}

.filters-min td {
    padding: 4px 5px 4px 0px;
    text-align: left;
}

.dropdowlist {
    border: 1px solid #A4BED4;
    padding: 1px;
    background: #ffffff;
    font-size: 12px;
}

.spacer20 {
    height: 20px;
    display: block;
}

.copyright {
    float: left;
    color: #0b2000;
}

.toshel {
    float: right;
    color: #0b2000;
    font-weight: bold;
}

.loading {
    width: 700px;
    height: 500px;
    text-align: center;
    padding-top: 250px;
    position: absolute;
}

.map {
    float: left;
    width: 700px;
    height: 500px;
    position: absolute;
}

.map-left {
    float: left;
    width: 700px;
    height: 500px;
    margin: 7px 5px;
}

.map-info {
    float: left;
    width: 190px;
    margin: 0px;
}

.fs {
    float: left;
    width: 190px;
}

.fs label {
    clear: both;
    float: left;
    width: 60px;
}

.clear {
    clear: both;
}

.fs span {
    float: left;
    font-weight: bold;
    margin-left: 10px;
}

.hr-separator {
    clear: both;
    margin-bottom: 5px;
    width: 100%;
    height: 5px;
    border-bottom: 1px solid #ddd;
}

.button, .button-selected, .dropdown {
    cursor: pointer;
    font-family: Arial, sans-serif;
    font-size: 12px;
    line-height: 160%;
    -moz-border-radius: 2px;
    box-shadow: 2px 2px 3px rgba(0, 0, 0, 0.35);
    -moz-box-shadow: 2px 2px 3px rgba(0, 0, 0, 0.35);
    -webkit-box-shadow: 2px 2px 3px rgba(0, 0, 0, 0.35);
    -webkit-user-select: none;
    -moz-user-select: none;
}

.button, .button-selected {
    padding: 0 6px;
    border-radius: 2px;
    margin: 5px;
    text-align: center;
    overflow: hidden;
}

.button {
    color: #000;
    border: 1px solid #A9BBDF;

    background: #FEFEFE;

    background: -moz-linear-gradient(top, #FEFEFE 0%, #F3F3F3 100%);

    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #FEFEFE), color-stop(100%, #F3F3F3));

    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FEFEFE', endColorstr='#F3F3F3', GradientType=0);
}

.button-selected, .button:hover {
    border: 1px solid #678AC7;
}

.button-selected {
    font-weight: bold;
    color: #fff;

    background: #6D8ACC;

    background: -moz-linear-gradient(top, #6D8ACC 0%, #7B98D9 100%);

    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #6D8ACC), color-stop(100%, #7B98D9));

    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#6D8ACC', endColorstr='#7B98D9', GradientType=0);
}

#dropdown-holder {
    margin: 5px;
}

#dropdown-holder .button, #dropdown-holder .button-selected {
    margin: 0;
}

#loading {
    background: url(loading.gif) no-repeat;
    width: 20px;
    height: 20px;
    position: absolute;
    visibility: hidden;
    z-index: 9999;
}

.login-win {

    margin: 0px auto !important;
}

.infowindow {

    font-size: 14px;
}

.color-legend {

}

.olImageLoadError {
    /* when OL encounters a 404, don't display the pink image */
    display: none !important;
}

.no-padding {
    width: 1px !important;
}

.datagrid-footer-padding {
    padding-left: 3px;
}

.satellite-orders-farm {
    margin: 5px;
}

.satellite-orders-farm tr {
    padding-bottom: 5px
}

.satellite-orders-farm td {
    padding: 5px 5px 5px 0px;
}

.satellite-orders-note {
    margin: 5px;
}

.satellite-orders-note textarea {
    width: 362px;
    height: 135px;
}

.satellite-orders-note .satellite-order-btn {
    display: block;
    text-align: center;
}

.map-bing {
    overflow: hidden !important;
}

.satellite-base-win {
    width: 250px;
    height: 400px;
    padding: 10px 10px 0 10px
}

.satellite-base-win table {
    margin-bottom: 5px;
}

.satellite-base-win td {
    padding-bottom: 5px;
    padding-right: 5px;
}

.sat-filter-label {
    text-align: right;
}

.sat-plots-history li {
    display: inline-block;
    text-align: center;
    margin: 3px;
    background-color: #CCCCCC;
}

.sat-plots-history li a {
    text-decoration: none;
    display: inline-block;
}

.sat-plots-history li div {
    color: #000;
    font-weight: bold;
}

.sat-legend {
    width: 89px;
    position: absolute;
    right: 10px;
    bottom: 40px;
    border: 1px solid #000;
    background-color: rgba(255, 255, 255, 0.7);
    padding: 5px;
    font-weight: bold;
    font-size: 14px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.sat-legend-item {
    width: 20px;
    height: 15px;
    display: inline-block;
    border: 1px solid;
}

.sat-legend-item-color-1 {
    background-color: #d7191c;
}

.sat-legend-item-color-2 {
    background-color: #e65437;
}

.sat-legend-item-color-3 {
    background-color: #f59053;
}

.sat-legend-item-color-4 {
    background-color: #fdbe74;
}

.sat-legend-item-color-5 {
    background-color: #fede9a;
}

.sat-legend-item-color-6 {
    background-color: #ffffc0;
}

.sat-legend-item-color-7 {
    background-color: #dbef9d;
}

.sat-legend-item-color-8 {
    background-color: #b7e07b;
}

.sat-legend-item-color-9 {
    background-color: #89cb61;
}

.sat-legend-item-color-10 {
    background-color: #51b051;
}

.sat-legend-item-color-11 {
    background-color: #1a9641;
}

/*OL Controls Styles Overrides*/
.tfs-controls .tfs-zoom {
    position: relative;
    display: inline-block;
    vertical-align: bottom;
    line-height: 14px;
    background: none;
    padding: 0px;
    margin-bottom: 15px;
}

.tfs-controls .tfs-zoom:hover {
    background: none;
}

.tfs-controls button.tfs-zoom-in, .tfs-controls button.tfs-zoom-out {
    display: block;
    width: 16px;
    height: 16px;
    border-radius: 24px;
    -webkit-box-shadow: -2px 3px 5px -1px rgba(54, 54, 54, 0.43);
    -moz-box-shadow: -2px 3px 5px -1px rgba(54, 54, 54, 0.43);
    box-shadow: -2px 3px 5px -1px rgba(54, 54, 54, 0.43);
    cursor: pointer;
}

.tfs-controls .tfs-zoom button > span {
    display: none !important;
}

.tfs-controls button.tfs-zoom-in {
    background: url('icons/zoom_in.png') 14px 14px no-repeat #e2e2e2 !important;
    /*margin-right: 8px;*/
    width: 48px;
    height: 48px;
    margin-bottom: 15px;
}

.tfs-controls button.tfs-zoom-out {
    background: url('icons/zoom_out.png') 14px 14px no-repeat #e2e2e2 !important;
    width: 48px;
    height: 48px;
}

.ol-measure-overlay {
    position: relative;
    background: url('images/measure-bubble.png') no-repeat;
    width: 86px;
    height: 39px;
    padding: 7px 10px 6px 10px;
    margin-left: 8px;
    display: none;
}

/*END*/

.satellite-payments-win label {
    display: block
}

.satellite-payments-win textarea {
    resize: none;
}

div.win-confirm-order {
    height: 435px;
    width: 360px;
    overflow: auto;
    padding: 5px;
    position: relative;
}

.win-confirm-order-tos {
    position: absolute;
    bottom: 3px;
}

.win-confirm-order-tos input {
    vertical-align: text-bottom;
}

.fieldset-base {
    border: 1px solid #000;
    margin: 0px 0px 5px 5px;
    padding: 5px;
}

.legend-base {
    font-style: italic;
    font-weight: bold;
    margin-left: 10px;
}

.win-confirm-order-fs {
    height: 66%;
}

.win-confirm-order-fs-tos {
    height: 10%;
    position: relative;
}

.win-confirm-order .message-content {
    overflow: auto;
    height: 218px;
}

.win-confirm-order .message-content table td {
    padding: 3px;
}

.win-confirm-order .conf-order-label {
    font-weight: bold;
}

.win-confirm-order .conf-order-farm, .win-confirm-order .conf-order-company-name {
    margin-right: 10px;
}

div.logo-geoscan {
    background-image: url(images/GEOSCAN.png);
    width: 205px;
}

.tfs-menu-button .m-btn .l-btn-left .l-btn-text {
    margin-right: 15px;
}

#tfs-controls .tfs-circlebutton, .tfs-submenu .tfs-circlebutton {
    background: #e2e2e2;
    color: #000000;
    display: block;
}

#tfs-controls .tfs-circlebutton-small, .tfs-submenu .tfs-circlebutton {
    border-radius: 38px !important;
    -webkit-box-shadow: -2px 3px 5px -1px rgba(54, 54, 54, 0.43);
    -moz-box-shadow: -2px 3px 5px -1px rgba(54, 54, 54, 0.43);
    box-shadow: -2px 3px 5px -1px rgba(54, 54, 54, 0.43);
}

#tfs-controls .tfs-circlebutton-big {
    margin-bottom: 15px;
    border-radius: 48px;
    -webkit-box-shadow: -2px 3px 5px -1px rgba(54, 54, 54, 0.43);
    -moz-box-shadow: -2px 3px 5px -1px rgba(54, 54, 54, 0.43);
    box-shadow: -2px 3px 5px -1px rgba(54, 54, 54, 0.43);
}

.tfs-submenu, .tfs-submenu + div.menu-shadow {
    background: none !important;
    box-shadow: none !important;
    border: none !important;
}

#menubutton1-submenu {
    padding-left: 50px !important;
    top: 58px !important;
    width: 260px !important;
}

#menubutton2-submenu {
    padding-left: 50px !important;
    top: 440px !important;
    width: 260px !important;
}

.tfs-submenu > .easyui-tooltip {
    width: 38px !important;
    display: inline-block;
}

.left-filter-padding-top {
    padding-top: 5px;
}

#operation-cut-image {
    width: 70px;
    height: 103px;
    background: url('images/operation-cut.png') no-repeat center center;
}

#operation-split-image {
    width: 70px;
    height: 103px;
    background: url('images/operation-split.png') no-repeat center center;
    display: none;
}

#operation-delete-image {
    width: 70px;
    height: 103px;
    background: url('images/operation-delete.png') no-repeat center center;
    display: none;
}

#all-layers-tree {
    text-align: left;
    width: 400px !important;
    height: 700px !important;
    overflow: scroll;
    overflow-x: hidden;
}

.decimal-list {
    list-style-type: decimal;
    padding-left: 40px;
}

.vps-info {
    height: 100px;
}

.form-field {
    width: 185px;
}

.filter-width {
    width: 240px;
}

.payroll-filter {
    width: 337px;
    border: none;
    padding-left: 5px;
}

.payroll-filter .payroll-filter-fs {
    float: left;
    width: 320px;
    padding: 5px;
    margin-left: 4px;
}

.payroll-filter .payroll-filter-fs legend {
    font-weight: bold;
    font-style: italic;
    margin-left: 5px;
}

.payroll-filter .payroll-filter-fs fieldset {
    padding: 3px 10px 8px 10px;
}

.payroll-filter .payroll-filter-fs fieldset > div {
    margin-bottom: 4px;
}

#pin-images img {
    cursor: pointer;
    margin-right: 5px;
}

.datagrid-cell-linkbutton {
    text-decoration: underline;
    cursor: pointer;
    plain: true;
    color: blue;
}

.filter-bubble {
    position: relative;
    padding: 5px;
    border: 1px solid #aaa;
    display: inline-block;
    border-radius: 5px;
    background-color: #fff;
    background: -webkit-gradient(linear, 50% 0%, 50% 100%, color-stop(20%, #ffffff), color-stop(50%, #f6f6f6), color-stop(52%, #eeeeee), color-stop(100%, #f4f4f4));
    background: -webkit-linear-gradient(#ffffff 20%, #f6f6f6 50%, #eeeeee 52%, #f4f4f4 100%);
    background: -moz-linear-gradient(#ffffff 20%, #f6f6f6 50%, #eeeeee 52%, #f4f4f4 100%);
    background: -o-linear-gradient(#ffffff 20%, #f6f6f6 50%, #eeeeee 52%, #f4f4f4 100%);
    background: linear-gradient(#ffffff 20%, #f6f6f6 50%, #eeeeee 52%, #f4f4f4 100%);
    background-clip: padding-box;
    box-shadow: 0 0 3px white inset, 0 1px 1px rgba(0, 0, 0, 0.1);
    color: #444;
    text-decoration: none;
    white-space: nowrap;
    line-height: 16px;
    margin: 5px 0 5px 5px;
    padding-right: 15px;
}

.non-closable {
    padding-right: 5px;
}

.filter-identifier {
    display: inline-block;
}

.filter-multiselect-border {
    border: 1px solid #ddd;
}

.remove-filter {
    position: absolute;
    top: 7px;
    right: 2px;
    display: block;
    width: 12px;
    height: 12px;
    background: url('chosen-sprite.png') -42px 1px no-repeat;
}

.remove-filter:hover {
    background-position: -42px -10px;
    cursor: pointer;
}

.layer-change-color-pickers input {
    display: none !important;
}

@media (max-width: 1255px) {
    .greet-name {
        display: none;
    }
}

@media (max-width: 1160px) {
    .annex-plots-tables-panel {
        width: 500px;
    }

    .subleases-plots-table-panel {
        width: 500px;
    }
}

@media (min-width: 1160px) {
    .annex-plots-tables-panel {
        width: 700px;
    }

    .subleases-plots-table-panel {
        width: 700px;
    }
}

@media (max-height: 764px) {
    #win-add-charged-renta {
        height: 450px;
        width: 650px;
    }
}

/*
 *  Стилове за toggle радио бутони
 *  http://www.htmllion.com/css3-toggle-switch-button.html
 */

.switch {
    position: relative;
    display: block;
    vertical-align: top;
    width: 50px;
    height: 20px;
    padding: 0px;
    margin: 0 10px 0 0;
    background: linear-gradient(to bottom, #eeeeee, #FFFFFF 25px);
    background-image: -webkit-linear-gradient(top, #eeeeee, #FFFFFF 25px);
    border-radius: 4px;
    box-shadow: inset 0 -1px white, inset 0 1px 1px rgba(0, 0, 0, 0.05);
    cursor: pointer;
}

.switch-input {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
}

.switch-label {
    position: relative;
    display: block;
    height: inherit;
    font-size: 10px;
    text-transform: uppercase;
    background: #eceeef;
    border-radius: inherit;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.12), inset 0 0 2px rgba(0, 0, 0, 0.15);
}

.switch-label:before, .switch-label:after {
    position: absolute;
    top: 50%;
    margin-top: -.5em;
    line-height: 1;
    -webkit-transition: inherit;
    -moz-transition: inherit;
    -o-transition: inherit;
    transition: inherit;
}

.switch-label:before {
    content: attr(data-off);
    right: 0px;
    color: #aaaaaa;
    text-shadow: 0 1px rgba(255, 255, 255, 0.5);
}

.switch-label:after {
    content: attr(data-on);
    left: 4px;
    color: #FFFFFF;
    text-shadow: 0 1px rgba(0, 0, 0, 0.2);
    opacity: 0;
}

.switch-input:checked ~ .switch-label {
    background: #73C400;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.15), inset 0 0 3px rgba(0, 0, 0, 0.2);
}

.switch-input:checked ~ .switch-label:before {
    opacity: 0;
}

.switch-input:checked ~ .switch-label:after {
    opacity: 1;
}

.switch-handle {
    position: absolute;
    top: 1px;
    left: 1px;
    width: 18px;
    height: 18px;
    background: linear-gradient(to bottom, #FFFFFF 40%, #f0f0f0);
    background-image: -webkit-linear-gradient(top, #FFFFFF 40%, #f0f0f0);
    border-radius: 4px;
    box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.2);
}

.switch-input:checked ~ .switch-handle {
    left: 31px;
    box-shadow: -1px 1px 5px rgba(0, 0, 0, 0.2);
}

/* Transition
========================== */
.switch-label, .switch-handle {
    transition: All 0.3s ease;
    -webkit-transition: All 0.3s ease;
    -moz-transition: All 0.3s ease;
    -o-transition: All 0.3s ease;
}

hr.fading-line {
    border: 0;
    height: 1px;
    background: #333;
    background-image: linear-gradient(to right, #ccc, #333, #ccc);
}

/*
 * Стилове, свързани със създаване на тематични карти
 */
.palette-1 {
    background: url('palette1.png') no-repeat !important;
}

.palette-2 {
    background: url('palette2.png') no-repeat !important;
}

.palette-3 {
    background: url('palette3.png') no-repeat !important;
}

.pie-chart-small {
    background: url('pie-chart-small.png') 0 0/30px 30px no-repeat !important;
    background-size: 30px 30px;
    float: left;
    width: 30px;
    height: 30px;
    vertical-align: middle;
}

.colorfull-rows {
    background: url('colorfull_rows.png') 0 0/30px 30px no-repeat !important;
    background-size: 30px 30px;
    float: left;
    width: 30px;
    height: 30px;
    vertical-align: middle;
}

.empty-filter {
    background: url('empty_filter.png') 0 0/30px 30px no-repeat !important;
    background-size: 30px 30px;
    float: left;
    width: 30px;
    height: 30px;
    vertical-align: middle;
}

h2.thematic-title {
    font-size: 16px;
    font-weight: bold;
    margin: 6px 0;
}

.input-field-for-color {
    display: none !important;
}

.layer-color-pickers input {
    display: none !important;
}

.radio-sort {
    margin-bottom: 5px;
}

.radio-sort input {
    position: relative;
    top: 3px;
}

.radio-order {
    padding: 1px;
}

/*typeahead styles*/

.typeahead {
    background-color: #fff;
}

.typeahead, .tt-hint, .tt-input, .tt-menu {
    width: 100%;
}

.tt-input,
.tt-hint {
    outline: none;
}

.tt-input {
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}

.tt-hint {
    color: #999
}

.tt-menu {
    background-color: #fff;
    border: 1px solid #ccc;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-bottom-left-radius: 6px;
    border-bottom-right-radius: 6px;
    box-shadow: 0 5px 10px rgba(0, 0, 0, .2);
}

/*.tt-dropdown-menu {
  top: auto !important;
  bottom: 0px;
}*/

.tt-suggestion {
    padding: 3px 20px;
}

.tt-suggestion:hover {
    cursor: pointer;
    color: #fff;
    background-color: #0097cf;
}

.tt-suggestion.tt-cursor {
    color: #fff;
    background-color: #0097cf;
}

.tt-suggestion.tt-cursor {
    color: #fff;
    background-color: #0097cf;
}

.tt-suggestion p {
    margin: 0;
}

.tt-cursor {
    color: #fff;
    background-color: #0097cf;
}

.tt-highlight {
    color: #fff;
    background-color: #0097cf;
}

.clearfix:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
}

html .clearfix {
    zoom: 1;
}

/* IE6 */

:first-child + html .clearfix {
    zoom: 1;
}

/* Vertical singul column form START */
.form-vsc-container {
    padding: 10px;
}
.form-vsc-container > fieldset {
    margin-bottom: 10px;
}
.form-vsc-container .fields-list {
    padding: 5px;
}
.form-vsc-container .fields-list > div {
    margin-bottom: 3px;
}
.form-vsc-container .label-above {
    display: block;
}
.form-vsc-container .fields-list label {
    font-weight: bold;
}
.form-vsc-container legend {
    font-weight: bold;
    font-style: italic;
}

.form-vsc-container .action-btns {
    width: 100%;
    text-align: center;
}
.form-vsc-container .action-btns > span {
    margin-right: 3px;
}
.form-vsc-container .action-btns > span:last-child {
    margin-right: 0;
}
/* END */

#win-abline-shift .ab-line-shift-info {
    padding: 5px;
    font-style: italic;
}


/* Begin Global Styles*/

.padding-5 {
    padding: 5px;
}

#contracts-tree-pagination.pagination {
    padding: 0;
}

.panel .panel-header {
    background: #fafafa;
}

.panel .tabs-header {
    background: #fafafa;
}

.panel .tabs li .tabs-inner {
    border-radius: 0;
    background: transparent;
    color: #000;
    border-bottom: 2px solid transparent;
    transition: 0.3s;
}

.panel .tabs li.tabs-selected .tabs-inner {
    border-color: transparent;
    color: #39c;
    border-bottom: 2px solid #39c;
    border-radius: 0;
    font-weight: normal;
    transition: 0.3s;
}

.panel .tabs li .tabs-inner:hover {
    background: #fff;
    border-color: transparent;
    color: #39c;
    border-bottom: 2px solid #39c;
}

.l-btn-plain {
    border: 0!important;
    padding: 0!important;
    transition: 0.3s;
}

.l-btn-plain:hover {
    border: 0!important;
    padding: 0!important;
}

.tree-node, .tree-node-hovered {
    transition: 0.4s;
}

.l-btn-plain {
    background: transparent!important;
}
.l-btn-plain-selected,
.l-btn-plain-selected:hover {
    background: #ddd!important;
}

div.window {
    background-color: #fff;
    background: -webkit-linear-gradient(top,#ffffff 0,#ffffff 20%);
    background: -moz-linear-gradient(top,#ffffff 0,#ffffff 20%);
    background: -o-linear-gradient(top,#ffffff 0,#ffffff 20%);
    background: linear-gradient(to bottom,#ffffff 0,#ffffff 20%);
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#ffffff,endColorstr=#ffffff,GradientType=0);
}

a.l-btn {
    color: #444;
    background: #f5f5f5;
    background-repeat: repeat-x;
    border: 1px solid #dbdbdb;
    background: -webkit-linear-gradient(top,#ffffff 0,#e6e6e6 100%);
    background: -moz-linear-gradient(top,#ffffff 0,#e6e6e6 100%);
    background: -o-linear-gradient(top,#ffffff 0,#e6e6e6 100%);
    background: linear-gradient(to bottom,#ffffff 0,#ffffff 100%);
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#ffffff,endColorstr=#e6e6e6,GradientType=0);
    -moz-border-radius: 5px 5px 5px 5px;
    -webkit-border-radius: 5px 5px 5px 5px;
    border-radius: 5px 5px 5px 5px;
}

/* START Login page */

.login-form-logo {
    width: 232px;
    height: 66px;
    margin-bottom: 30px;
}

.login-form-logo img{
    height: 100%;
    width: 100%;
}

#login-tf-bg {
    height: 160px;
    width: 100%;
    position: absolute;
    background-image: url(images/tf_logo_no_shadow.png);
    background-size: contain;
    top: 520px;
    background-repeat: no-repeat;
    background-position: center;
}
.login-backround
{
    background-image: url(../../images/tf4_bg.jpg);
    background-size: cover;
    background-repeat: no-repeat;
    border: none;
    height: 100vh!important;
}

.login-backround-inner {
    display: inline-block;
}

#login-win {
    background-color: #fff;
    border-radius: 8px;;
}

.login-win {
    margin-left:268px!important;
    margin-top: 40%!important;
}

#login-win {
    width: 432px;
    height: 457px;
}

.login-win .panel-header{
    display: none;
}

#login-footer {
    width: 99%;
    padding: 16px;
    position: fixed;
    background: #fff;
    opacity: 0.8;
    bottom: 10px;
    border-radius: 8px;
    font-size: 10px;
    line-height: 16px;
    left: 50%;
    transform: translateX(-50%);
}

#login-footer .copyright {
    color: #5B5B5C;
}

.login-form {
    padding: 50px 50px 66px;
}

.login-form input {
    height: 35px;
    border: 1px solid #D9D9D9;
    border-radius: 5px;
    padding: 5px 12px;
}

.login-form label,
.forgotten-password-form label {
    font-size: 14px;
    font-weight: 400;
    color: #5B5B5C;
    margin-bottom: 5px;
    line-height: 20px;
}

.login-form input:-webkit-autofill,
.login-form input:-webkit-autofill:hover,
.login-form input:-webkit-autofill:focus,
.forgotten-password-form input:-webkit-autofill,
.forgotten-password-form input:-webkit-autofill:hover,
.forgotten-password-form input:-webkit-autofill:focus {
    -webkit-text-fill-color: #040405;
    -webkit-box-shadow: 0 0 0px 1000px rgb(255, 255, 255) inset;
}

.login-form input:focus-visible,
.forgotten-password-form input:focus-visible {
    outline: none;
}

.login-form #btn-login,
.login-form input,
#login-button-container #btn-forgotten-send,
#btn-change-password {
    width: 100%;
    margin-top: 5px;
}
.login-form a#btn-login,
#login-button-container #btn-forgotten-send,
#btn-change-password {
    height: 35px;
    color: #fff;
    background: #759851;
    border-radius: 100px!important;
    border: none;
    transition: 0.3s;
}

.login-form #btn-login .l-btn-text,
#login-button-container #btn-forgotten-send,
#btn-change-password {
    line-height: 35px;
}

.login-form #btn-login:hover,
#login-button-container #btn-forgotten-send:hover,
#btn-change-password:hover {
    background: #58585a;
}

.username-container,
.password-container,
.email-container,
.new-password-container,
.re-password-container {
    margin-bottom: 16px;
}

.forgotten-password-container {
    margin-bottom: 35px;
}

.forgotten-password-container a{
    color: #759851!important;
    text-decoration: none;
    font-size: 14px;
    transition: 0.3s;
}

.forgotten-password-container a:hover{
    color: #58585a!important;
}

#win-forgotten-password {
    padding: 20px;
    border: none;
}

.forgotten-password-form label{
    display: block;
}

.forgotten-password-form input{
    width: 100%;
    height: 35px;
}

.forgotten-password-btns {
    text-align: center;
}

.forgotten-password-btns a{
    line-height: 35px;
    width: 100px;
}



/* END Login page */



/*Start Main Menu*/

#mainmenu-logo {
    margin-top: 6px;
    margin-left: 6px;
}

#tf-main-menu a span.l-btn-text{
    line-height: 36px;

}

.menu-shadow {
    border-radius: 0!important;
    box-shadow: none!important;
}

.l-btn {
    border-radius: 0!important;
}

.menu-item {
    border-radius: 0;
}

.menu-line {
    display: none;
}

.menu-sep {
    margin: 0!important;
    font-size: 1px;
    border-bottom: 0!important;
}

.menu .menu-active {
    border-radius: 0!important;
    background-color: #e6e6e6;
    border-color: #e6e6e6;
    color:#000;
    transition: 0.3s;
}

.menu .menu-item {
    height: 35px!important;
}

.menu .menu-text {
    height: 35px!important;
    line-height: 35px!important;
}

/*End Main Menu*/

/*Start notifications*/
ul.notification-messages-list {
    list-style: circle;
}
.notification-messages-list li{
    margin-bottom: 15px;
    list-style-position: inside;
}
/*End notifications*/


/*Start Fixes*/

.plupload_filelist_footer {
    box-sizing: content-box;
}

/*End Fixes*/


.menu-top .easyui-linkbutton, .menu .easyui-linkbutton {
    width: 100%;;
}

.l-btn-text {
    vertical-align: middle!important;
}

.messager-window {
    z-index: 9999!important;
}

.login-form-logo {
    background-image: url(tf_logo.svg);
    background-repeat: no-repeat;
    background-size: 100%;
}

@media only screen and (max-width: 1600px) {
    .login-win {
        margin-left: 210px!important;
        margin-top: 150px!important;
    }

    #login-win {
        width: 330px;
        height: 380px;
    }

    .login-form {
        padding: 35px;
    }

    .username-container,
    .password-container,
    .email-container,
    .new-password-container,
    .confirm-password-container,
    .login-form-logo {
        margin-bottom: 10px;
    }
}

@media only screen and (max-width: 1200px) {
    .login-win {
        margin-left: 180px!important;
        margin-top: 100px!important;
    }

    #login-win {
        width: 280px;
        height: 330px;
    }

    .login-form {
        padding: 25px;
    }

    .username-container,
    .password-container,
    .email-container,
    .new-password-container,
    .re-password-container,
    .login-form-logo {
        margin-bottom: 10px;
    }

    .login-form-logo {
        width: 175px;
        height: 37px;
    }
}

/* Fix main-nav submenu to be shown on top of the other elements. */
body > form > div.layout-panel-west {
    overflow: visible;
    border-width: 0px !important;
}
/* End Global Styles*/

.note {margin-top: 10px;margin-bottom: 10px;margin-left:-8px;background-color:#ffffcc;}