.ol-popup {
    display: none;
    position: absolute;
    background-color: white;
    -moz-box-shadow: 0 1px 4px rgba(0,0,0,0.2);
    -webkit-filter: drop-shadow(0 1px 4px rgba(0,0,0,0.2));
    filter: drop-shadow(0 1px 4px rgba(0,0,0,0.2));
    padding: 15px;
    border-radius: 10px;
    border: 1px solid #cccccc;
    top: 8px;
    left: -22px;
}
.ol-popup:after, .ol-popup:before {
    bottom: 100%;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
}
.ol-popup:after {
    border-bottom-color: white;
    border-width: 7px;
    left: 20px;
    margin-left: -7px;
}
.ol-popup:before {
    border-bottom-color: #cccccc;
    border-width: 8px;
    left: 20px;
    margin-left: -8px;
}
.ol-popup-closer {
    text-decoration: none;
    position: absolute;
    top: 1px;
    right: 5px;
}
.ol-popup-closer:after {
    content: "✖";
}
.ol-popup #popup-content {
    white-space: nowrap;
    cursor: pointer;
}