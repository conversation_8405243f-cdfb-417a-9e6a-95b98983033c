body,div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,pre,form,fieldset,input,textarea,p,blockquote,th,td { 
	margin:0;
	padding:0;
}
table {
	border-collapse:collapse;
	border-spacing:0;
}
fieldset,img { 
	border:0;
}
address,caption,cite,code,dfn,em,strong,th,var {
	font-style:normal;
	font-weight:normal;
}
ol,ul {
	list-style:none;
}
caption,th {
	text-align:left;
}
h1,h2,h3,h4,h5,h6 {
	font-size:100%;
	font-weight:normal;
}
q:before,q:after {
	content:'';
}
abbr,acronym { border:0;
}
html, body {
	background-color: #fff;
	font-family: Arial, Helvetica, sans-serif;
	font-size: 12px;
	line-height: 18px;
	color: #52697E;
}
body {
	/*text-align: center;*/
	overflow: auto;
}
.wrapper {
	width: 700px;
	margin: 0 auto;
	text-align: left;
}
h1 {
	font-size: 21px;
	height: 47px;
	line-height: 47px;
	text-transform: uppercase;
}
.navigationTabs {
	height: 23px;
	line-height: 23px;
	border-bottom: 1px solid #ccc;
}
.navigationTabs li {
	float: left;
	height: 23px;
	line-height: 23px;
	padding-right: 3px;
}
.navigationTabs li a{
	float: left;
	dispaly: block;
	height: 23px;
	line-height: 23px;
	padding: 0 10px;
	overflow: hidden;
	color: #52697E;
	background-color: #eee;
	position: relative;
	text-decoration: none;
}
.navigationTabs li a:hover {
	background-color: #f0f0f0;
}
.navigationTabs li a.active {
	background-color: #fff;
	border: 1px solid #ccc;
	border-bottom: 0px solid;
}
.tabsContent {
	border: 1px solid #ccc;
	border-top: 0px solid;
	width: 698px;
	overflow: hidden;
}
.tab {
	padding: 16px;
	display: none;
}
.tab h2 {
	font-weight: bold;
	font-size: 16px;
}
.tab h3 {
	font-weight: bold;
	font-size: 14px;
	margin-top: 20px;
}
.tab p {
	margin-top: 16px;
	clear: both;
}
.tab ul {
	margin-top: 16px;
	list-style: disc;
}
.tab li {
	margin: 10px 0 0 35px;
}
.tab a {
	color: #8FB0CF;
}
.tab strong {
	font-weight: bold;
}
.tab pre {
	font-size: 11px;
	margin-top: 20px;
	width: 668px;
	overflow: auto;
	clear: both;
}
.tab table {
	width: 100%;
}
.tab table td {
	padding: 6px 10px 6px 0;
	vertical-align: top;
}
.tab dt {
	margin-top: 16px;
}

#colorSelector {
	position: relative;
	width: 36px;
	height: 36px;
	background: url(../images/select.png);
}
#colorSelector div {
	position: absolute;
	top: 3px;
	left: 3px;
	width: 30px;
	height: 30px;
	background: url(../images/select.png) center;
}
#colorSelector2 {
	position: absolute;
	top: 0;
	left: 0;
	width: 36px;
	height: 36px;
	background: url(../images/select2.png);
}
#colorSelector2 div {
	position: absolute;
	top: 4px;
	left: 4px;
	width: 28px;
	height: 28px;
	background: url(../images/select2.png) center;
}
#colorpickerHolder2 {
	top: 32px;
	left: 0;
	width: 356px;
	height: 0;
	overflow: hidden;
	position: absolute;
}
#colorpickerHolder2 .colorpicker {
	background-image: url(../images/custom_background.png);
	position: absolute;
	bottom: 0;
	left: 0;
}
#colorpickerHolder2 .colorpicker_hue div {
	background-image: url(../images/custom_indic.gif);
}
#colorpickerHolder2 .colorpicker_hex {
	background-image: url(../images/custom_hex.png);
}
#colorpickerHolder2 .colorpicker_rgb_r {
	background-image: url(../images/custom_rgb_r.png);
}
#colorpickerHolder2 .colorpicker_rgb_g {
	background-image: url(../images/custom_rgb_g.png);
}
#colorpickerHolder2 .colorpicker_rgb_b {
	background-image: url(../images/custom_rgb_b.png);
}
#colorpickerHolder2 .colorpicker_hsb_s {
	background-image: url(../images/custom_hsb_s.png);
	display: none;
}
#colorpickerHolder2 .colorpicker_hsb_h {
	background-image: url(../images/custom_hsb_h.png);
	display: none;
}
#colorpickerHolder2 .colorpicker_hsb_b {
	background-image: url(../images/custom_hsb_b.png);
	display: none;
}
#colorpickerHolder2 .colorpicker_submit {
	background-image: url(../images/custom_submit.png);
}
#colorpickerHolder2 .colorpicker input {
	color: #778398;
}
#customWidget {
	position: relative;
	height: 36px;
}
