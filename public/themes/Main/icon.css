.icon-blank{
	background:url('icons/blank.gif') no-repeat center center;
}
.icon-add{
	background:url('icons/edit_add.png') no-repeat center center;
}
.icon-app{
    background:url('icons/application.png') no-repeat center center;
}
.icon-box{
    background:url('icons/box.png') no-repeat center center;
}
.icon-edit{
	background:url('icons/pencil.png') no-repeat center center;
}
.icon-multi-edit{
	background:url('icons/multi-edit.png') no-repeat center center;
}
.icon-tick{
    background:url('icons/tick.png') no-repeat center center;
}
.icon-remove{
	background:url('icons/edit_remove.png') no-repeat center center;
}
.icon-save{
	background:url('icons/filesave.png') no-repeat center center;
}
.icon-cut{
	background:url('icons/cut.png') no-repeat center center;
}
.icon-ok{
	background:url('icons/ok.png') no-repeat center center;
}
.icon-no{
	background:url('icons/no.png') no-repeat center center;
}
.icon-cancel{
	background:url('icons/cancel.png') no-repeat center center;
}
.icon-reload{
	background:url('icons/reload.png') no-repeat center center;
}
.icon-search{
	background:url('icons/search.png') no-repeat center center;
}
.icon-print{
	background:url('icons/print.png') no-repeat center center;
}
.icon-print-filtered{
    background:url('icons/print-filtered.png') no-repeat center center;
}
.icon-help{
	background:url('icons/help.png') no-repeat center center;
}
.icon-undo{
	background:url('icons/undo.png') no-repeat center center;
}
.icon-redo{
	background:url('icons/redo.png') no-repeat center center;
}
.icon-back-blue{
    background:url('icons/back.png') no-repeat center center;
}
.icon-forward{
	background:url('icons/forward.png') no-repeat center center;
}
.icon-sum{
	background:url('icons/sum.png') no-repeat center center;
}
.icon-tip{
	background:url('icons/tip.png') no-repeat center center;
}

.icon-mini-add{
	background:url('icons/mini_add.png') no-repeat center center;
}
.icon-mini-edit{
	background:url('icons/mini_edit.png') no-repeat center center;
}
.icon-mini-refresh{
	background:url('icons/mini_refresh.png') no-repeat center center;
}

.icon-edit-geometry{
	background:url('icons/edit_geometry.png') no-repeat center center;
}

.icon-payments{
	background:url('icons/payments.png') no-repeat center center;
}


.icon-logout{
	background:url('icons/logout.png') no-repeat;
}

.icon-plot{
	background:url('icons/polygon.png') no-repeat;
}

.icon-users{
	background:url('icons/users.png') no-repeat;
}

.icon-settings{
	background:url('icons/settings.png') no-repeat;
}

.icon-map{
	background:url('icons/map.png') no-repeat !important;
}

.icon-datagrid{
	background:url('icons/datagrid.png') no-repeat;
}

.icon-trash{
	background:url('icons/trash.png') no-repeat;
}

.icon-password{
	background:url('icons/password.png') no-repeat;
}

.icon-details{
        background:url('icons/details.gif') no-repeat;
}

.icon-files{
    background:url('icons/files.gif') no-repeat;
}

.icon-rents{
    background:url('icons/rents.gif') no-repeat;
}

.icon-owners{
    background:url('icons/owners.gif') no-repeat;
}

.icon-leases{
    background:url('icons/leases.png') no-repeat;
}

.icon-deed{
    background:url('icons/deed.gif') no-repeat;
}

.icon-neighbor{
    background:url('icons/neighbor.png') no-repeat;
}

.icon-planting{
    background:url('icons/planting.png') no-repeat;
}

.icon-download-file{
    background:url('icons/download.png') no-repeat;
}

.icon-select-info{
    background:url('icons/select-query.png') no-repeat;
}

.icon-nav-pan{
    background:url('icons/nav_pan.png') no-repeat;
}

.icon-zoom-full{
    background:url('icons/zoom-full.png') no-repeat;
}

.icon-zoom-in{
    background:url('icons/zoom_in.png') no-repeat;
}

.icon-zoom-out{
    background:url('icons/zoom_out.png') no-repeat;
}

.icon-measure-line{
    background:url('icons/measure-line.png') no-repeat;
}

.icon-measure-polygon{
    background:url('icons/measure-polygon.png') no-repeat;
}

.icon-intersection{
    background:url('icons/intersection.png') no-repeat;
}

.icon-back{
    background:url('icons/back_map.png') no-repeat;
}

.icon-tools{
    background:url('icons/tools.png') no-repeat;
}

.icon-progress{
    background:url('icons/progress.png') no-repeat;
}

.icon-info{
    background:url('icons/info.png') no-repeat;
}

.icon-layers{
    background:url('icons/layers.png') no-repeat;
}

.icon-agriculture{
    background:url('icons/agriculture.png') no-repeat;
}

.icon-data{
    background:url('icons/data.png') no-repeat;
}

.icon-documents{
    background:url('icons/documents.png') no-repeat;
}

.icon-pdf{
    background:url('icons/pdf.png') no-repeat;
}

.icon-word{
    background:url('icons/word.png') no-repeat;
}
.icon-csv{
    background:url('icons/csv.png') no-repeat;
}
.icon-xml{
    background:url('icons/xml.png') no-repeat;
}
.icon-fullscreen{
    background:url('icons/fullscreen.png') no-repeat;
}

.icon-agreements{
    background:url('icons/agreements.png') no-repeat;
}

.icon-filter{
    background:url('icons/filter.gif') no-repeat;
}

.icon-clear-filter{
    background:url('icons/clear-filter.gif') no-repeat;
}

.icon-draw{
    background:url('icons/draw.png') no-repeat;
}

.icon-delete{
    background:url('icons/delete.png') no-repeat;
}

.icon-select{
    background:url('icons/select.png') no-repeat;
}

.icon-owners{
    background:url('icons/owners.gif') no-repeat;
}

.icon-contract{
    background:url('icons/contract.png') no-repeat !important;
}

.icon-contract-group{
    background:url('icons/layers.png') no-repeat !important;
}

.icon-satelite{
    background:url('icons/satelite.png') no-repeat !important;
}

.icon-event{
    background:url('icons/event.png') no-repeat !important;
}

.icon-data-machine{
    background:url('icons/data-machine.png') no-repeat !important;
}

.icon-copy{
    background:url('icons/copy.png') no-repeat;
}

.icon-upload{
    background:url('icons/upload.png') no-repeat;
}

.icon-legend{
    background:url('icons/legend.png') no-repeat;
}

.icon-height{
    background:url('icons/height.png') no-repeat;
}

.icon-case{
    background:url('icons/case.png') no-repeat;
}

.icon-dolar{
    background:url('icons/dollar.png') no-repeat;
}

.icon-clear{
    background:url('icons/clear.png') no-repeat;
}

.icon-export{
    background:url('icons/export.png') no-repeat;
}
.icon-mini-add{
	background:url('icons/mini_add.png') no-repeat 2px 2px;
}
.icon-mini-edit{
	background:url('icons/mini_edit.png') no-repeat 2px 2px;
}
.icon-mini-refresh{
	background:url('icons/mini_refresh.png') no-repeat 3px 2px;
}
.icon-reports{
    background:url('icons/report.png') no-repeat 3px 2px;
}
.icon-flask{
    background:url('icons/flask.png') no-repeat 0px 0px;
}
.icon-culture{
    background:url('icons/culture.png') no-repeat 3px 2px;
}
.icon-template{
    background:url('icons/template.png') no-repeat 3px 2px;
}

.icon-plan{
    background:url('icons/plan.png') no-repeat 3px 2px;
}

.no-background{
    background:none !important;
}

.icon-tree-files{
    background:url('icons/files.gif') no-repeat !important;
}

.icon-tree-edit-geometry{
    background:url('icons/edit_geometry.png') no-repeat !important;
}

.icon-tree-user{
    background:url('icons/user.png') no-repeat !important;
}

.icon-tree-user-rip{
    background:url('icons/user-rip.png') no-repeat !important;
}

.icon-tree-users{
    background:url('icons/users.png') no-repeat !important;
}

.icon-tree-document{
    background:url('icons/document.png') no-repeat !important;
}

.icon-contract-type-lease{
    background:url('icons/icon-contract-type-lease.png') no-repeat !important;
}

.icon-contract-type-own{
    background:url('icons/icon-contract-type-own.png') no-repeat !important;
}

.icon-contract-type-rent{
    background:url('icons/icon-contract-type-rent.png') no-repeat !important;
}

.icon-contract-type-sublease {
    background:url('icons/icon-contract-type-sublease.png') no-repeat !important;
}

.icon-tree-calendar{
    background:url('icons/calendar.png') no-repeat !important;
}

.icon-tree-csv{
    background:url('icons/csv.png') no-repeat !important;
}

.icon-tree-layers{
    background:url('icons/layers.png') no-repeat !important;
}

.icon-tree-intersection{
    background:url('icons/intersection.png') no-repeat !important;
}

.icon-tree-planting{
    background:url('icons/planting.png') no-repeat !important;
}

.icon-add-filter{
    background:url('icons/add-filter.gif') no-repeat !important;
}

.icon-split-polygon{
	background:url('icons/split-polygon.png') no-repeat !important;
}

.icon-polygon-hole{
	background:url('icons/polygon-hole.png') no-repeat !important;
}

.icon-calculator{
	background:url('icons/calculator.png') no-repeat !important;
}

.icon-tree-add-column{
	background:url('icons/add-column.png') no-repeat !important;
}

.icon-parting-contract{
    background:url('icons/parting_contract.png') no-repeat !important;
}

.icon-tree{
	background:url('icons/tree.png') no-repeat !important;
}

.icon-remove-holes{
	background:url('icons/shape_move_backwards.png') no-repeat !important;
}

.icon-clipping{
    background:url('icons/shape_move_back.png') no-repeat !important;
}

.icon-weighing-machine{
	background:url('icons/weighing-machine.png') no-repeat !important;
}

.icon-hourglass{
	background:url('icons/hourglass.png') no-repeat !important;
}

.icon-tree-edit-geometry{
	background:url('icons/edit_geometry.png') no-repeat center center !important;
}

.icon-satellite{
    background:url('icons/satellite.png') no-repeat !important;
}
.icon-cart {
    background:url('icons/cart.png') no-repeat !important;
}
.icon-cart-add {
    background:url('icons/cart_add.png') no-repeat !important;
}

.icon-cart-edit {
    background:url('icons/cart_edit.png') no-repeat !important;
}
.icon-cart-error {
    background:url('icons/cart_error.png') no-repeat !important;
}

.icon-cart-go {
    background:url('icons/cart_go.png') no-repeat !important;
}

.icon-cart-put {
    background:url('icons/cart_put.png') no-repeat !important;
}

.icon-cart-remove {
    background:url('icons/cart_remove.png') no-repeat !important;
}
.icon-admin {
    background:url('icons/user_gray.png') no-repeat !important;
}
.icon-sat-select {
    background:url('icons/color-picker-small.png') no-repeat !important;
}

.icon-money {
    background:url('icons/money.png') no-repeat !important;
}
.icon-money-add {
    background:url('icons/money_add.png') no-repeat !important;
}
.icon-chart {
    background:url('icons/chart_bar.png') no-repeat !important;
}
.icon-image {
    background:url('icons/image.png') no-repeat !important;
}
.icon-geoscan {
    background:url('icons/gs-icon.png') no-repeat !important;
}
.icon-rm-index {
    background:url('icons/rm-index-layer.png') no-repeat !important;
}
.icon-pin {
    background:url('icons/pin.png') no-repeat !important;
}
.icon-tractor {
    background:url('icons/tractor.png') no-repeat !important;
}
.icon-curve {
    background:url('icons/curve.png') no-repeat !important;
}
.icon-colorfull-menu {
    background: url('icons/colorfull_menu.png') no-repeat !important;
}
.tree-folder-tf {
    background: url('bootstrap/images/tree_icons.png') no-repeat -208px 0 !important;
}
.icon-manual {
    background: url('icons/manual.png') no-repeat !important;
}
.icon-video-manual {
    background: url('icons/video_manual.png') no-repeat !important;
}
.icon-sort {
    background: url('icons/sort.png') no-repeat !important;
}
.icon-sort-asc {
    background: url('icons/sort-asc.png') no-repeat !important;
}
.icon-sort-desc {
    background: url('icons/sort-desc.png') no-repeat !important;
}
.icon-redirect {
    background: url('icons/redirect.png') no-repeat !important;
}
.icon-notification{
    background: url('icons/notification.png') no-repeat !important;
}
.icon-warehouse{
    background: url('icons/warehouse.png') no-repeat !important;
}
.icon-warehouse-add{
    background: url('icons/warehouse_add.png') no-repeat !important;
}
.icon-warehouse-sub{
    background: url('icons/warehouse_sub.png') no-repeat !important;
}
.icon-warehouse-transf{
    background: url('icons/warehouse_transf.png') no-repeat !important;
}

.icon-abline-add {
    background: url('icons/vector_add.png') no-repeat !important;
}

.icon-abline-save {
    background: url('icons/vector_save.png') no-repeat !important;
}

.icon-abline-delete {
    background: url('icons/vector_delete.png') no-repeat !important;
}
.icon-abline-shift {
    background: url('icons/vector_shift.png') no-repeat !important;
}
.icon-abline {
    background: url('icons/vector.png') no-repeat !important;
}

.tree-join-h {
    background: url('icons/line-conn.png') no-repeat 0 !important;
}

.icon-more {
    background: url('icons/more.png') no-repeat 0 !important;
}