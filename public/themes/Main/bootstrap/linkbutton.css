.l-btn {
  text-decoration: none;
  display: inline-block;
  overflow: hidden;
  margin: 0;
  padding: 0;
  cursor: pointer;
  outline: none;
  text-align: center;
  vertical-align: middle;
  line-height: normal;
}
.l-btn-plain {
  border-width: 0;
  padding: 1px;
}
.l-btn-left {
  display: inline-block;
  position: relative;
  overflow: hidden;
  margin: 0;
  padding: 0;
  vertical-align: top;
}
.l-btn-text {
  display: inline-block;
  vertical-align: top;
  width: auto;
  line-height: 28px;
  font-size: 12px;
  padding: 0;
  margin: 0 6px;
}
.l-btn-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  line-height: 16px;
  position: absolute;
  top: 50%;
  margin-top: -8px;
  font-size: 1px;
}
.l-btn span span .l-btn-empty {
  display: inline-block;
  margin: 0;
  width: 16px;
  height: 24px;
  font-size: 1px;
  vertical-align: top;
}
.l-btn span .l-btn-icon-left {
  padding: 0 0 0 20px;
  background-position: left center;
}
.l-btn span .l-btn-icon-right {
  padding: 0 20px 0 0;
  background-position: right center;
}
.l-btn-icon-left .l-btn-text {
  margin: 0 6px 0 26px;
}
.l-btn-icon-left .l-btn-icon {
  left: 6px;
}
.l-btn-icon-right .l-btn-text {
  margin: 0 26px 0 6px;
}
.l-btn-icon-right .l-btn-icon {
  right: 6px;
}
.l-btn-icon-top .l-btn-text {
  margin: 20px 4px 0 4px;
}
.l-btn-icon-top .l-btn-icon {
  top: 4px;
  left: 50%;
  margin: 0 0 0 -8px;
}
.l-btn-icon-bottom .l-btn-text {
  margin: 0 4px 20px 4px;
}
.l-btn-icon-bottom .l-btn-icon {
  top: auto;
  bottom: 4px;
  left: 50%;
  margin: 0 0 0 -8px;
}
.l-btn-left .l-btn-empty {
  margin: 0 6px;
  width: 16px;
}
.l-btn-plain:hover {
  padding: 0;
}
.l-btn-focus {
  outline: #0000FF dotted thin;
}
.l-btn-large .l-btn-text {
  line-height: 44px;
}
.l-btn-large .l-btn-icon {
  width: 32px;
  height: 32px;
  line-height: 32px;
  margin-top: -16px;
}
.l-btn-large .l-btn-icon-left .l-btn-text {
  margin-left: 40px;
}
.l-btn-large .l-btn-icon-right .l-btn-text {
  margin-right: 40px;
}
.l-btn-large .l-btn-icon-top .l-btn-text {
  margin-top: 36px;
  line-height: 24px;
  min-width: 32px;
}
.l-btn-large .l-btn-icon-top .l-btn-icon {
  margin: 0 0 0 -16px;
}
.l-btn-large .l-btn-icon-bottom .l-btn-text {
  margin-bottom: 36px;
  line-height: 24px;
  min-width: 32px;
}
.l-btn-large .l-btn-icon-bottom .l-btn-icon {
  margin: 0 0 0 -16px;
}
.l-btn-large .l-btn-left .l-btn-empty {
  margin: 0 6px;
  width: 32px;
}
.l-btn {
  color: #444;
  background: #f5f5f5;
  background-repeat: repeat-x;
  border: 1px solid #bbb;
  background: -webkit-linear-gradient(top,#ffffff 0,#e6e6e6 100%);
  background: -moz-linear-gradient(top,#ffffff 0,#e6e6e6 100%);
  background: -o-linear-gradient(top,#ffffff 0,#e6e6e6 100%);
  background: linear-gradient(to bottom,#ffffff 0,#e6e6e6 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#ffffff,endColorstr=#e6e6e6,GradientType=0);
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.l-btn:hover {
  background: #e6e6e6;
  color: #00438a;
  border: 1px solid #ddd;
  filter: none;
}
.l-btn-plain {
  background: transparent;
  border-width: 0;
  filter: none;
}
.l-btn-outline {
  border-width: 1px;
  border-color: #ddd;
  padding: 0;
}
.l-btn-plain:hover {
  background: #e6e6e6;
  color: #00438a;
  border: 1px solid #ddd;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
.l-btn-disabled,
.l-btn-disabled:hover {
  opacity: 0.5;
  cursor: default;
  background: #f5f5f5;
  color: #444;
  background: -webkit-linear-gradient(top,#ffffff 0,#e6e6e6 100%);
  background: -moz-linear-gradient(top,#ffffff 0,#e6e6e6 100%);
  background: -o-linear-gradient(top,#ffffff 0,#e6e6e6 100%);
  background: linear-gradient(to bottom,#ffffff 0,#e6e6e6 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#ffffff,endColorstr=#e6e6e6,GradientType=0);
}
.l-btn-disabled .l-btn-text,
.l-btn-disabled .l-btn-icon {
  filter: alpha(opacity=50);
}
.l-btn-plain-disabled,
.l-btn-plain-disabled:hover {
  background: transparent;
  filter: alpha(opacity=50);
}
.l-btn-selected,
.l-btn-selected:hover {
  background: #ddd;
  filter: none;
}
.l-btn-plain-selected,
.l-btn-plain-selected:hover {
  background: #ddd;
}
