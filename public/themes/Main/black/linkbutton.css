a.l-btn {
  background-position: right 0;
  text-decoration: none;
  display: inline-block;
  zoom: 1;
  height: 24px;
  padding-right: 18px;
  cursor: pointer;
  outline: none;
}
a.l-btn-plain {
  padding-right: 5px;
  border: 0;
  padding: 1px 6px 1px 1px;
}
a.l-btn-disabled {
  color: #ccc;
  opacity: 0.5;
  filter: alpha(opacity=50);
  cursor: default;
}
a.l-btn span.l-btn-left {
  display: inline-block;
  background-position: 0 -48px;
  padding: 4px 0px 4px 18px;
  line-height: 16px;
  height: 16px;
}
a.l-btn-plain span.l-btn-left {
  padding-left: 5px;
}
a.l-btn span span.l-btn-text {
  display: inline-block;
  vertical-align: baseline;
  width: auto;
  height: 16px;
  line-height: 16px;
  font-size: 12px;
  padding: 0;
  margin: 0;
}
a.l-btn span span.l-btn-icon-left {
  padding: 0 0 0 20px;
  background-position: left center;
}
a.l-btn span span.l-btn-icon-right {
  padding: 0 20px 0 0;
  background-position: right center;
}
a.l-btn span span span.l-btn-empty {
  display: inline-block;
  margin: 0;
  padding: 0;
  width: 16px;
}
a:hover.l-btn {
  background-position: right -24px;
  outline: none;
  text-decoration: none;
}
a:hover.l-btn span.l-btn-left {
  background-position: 0 bottom;
}
a:hover.l-btn-plain {
  padding: 0 5px 0 0;
}
a:hover.l-btn-disabled {
  background-position: right 0;
}
a:hover.l-btn-disabled span.l-btn-left {
  background-position: 0 -48px;
}
a.l-btn .l-btn-focus {
  outline: #0000FF dotted thin;
}
a.l-btn {
  color: #fff;
  background-image: url('images/linkbutton_bg.png');
  background-repeat: no-repeat;
  background: #777;
  background-repeat: repeat-x;
  border: 1px solid #555;
  background: -webkit-linear-gradient(top,#919191 0,#6a6a6a 100%);
  background: -moz-linear-gradient(top,#919191 0,#6a6a6a 100%);
  background: -o-linear-gradient(top,#919191 0,#6a6a6a 100%);
  background: linear-gradient(to bottom,#919191 0,#6a6a6a 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#919191,endColorstr=#6a6a6a,GradientType=0);
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
a.l-btn span.l-btn-left {
  background-image: url('images/linkbutton_bg.png');
  background-repeat: no-repeat;
  background-image: none;
}
a:hover.l-btn {
  background: #777;
  color: #fff;
  border: 1px solid #555;
  filter: none;
}
a.l-btn-plain,
a.l-btn-plain span.l-btn-left {
  background: transparent;
  border: 0;
  filter: none;
}
a:hover.l-btn-plain {
  background: #777;
  color: #fff;
  border: 1px solid #555;
  -moz-border-radius: 5px 5px 5px 5px;
  -webkit-border-radius: 5px 5px 5px 5px;
  border-radius: 5px 5px 5px 5px;
}
a.l-btn-disabled,
a:hover.l-btn-disabled {
  color: #fff;
  filter: alpha(opacity=50);
  background: #777;
  color: #fff;
  background: -webkit-linear-gradient(top,#919191 0,#6a6a6a 100%);
  background: -moz-linear-gradient(top,#919191 0,#6a6a6a 100%);
  background: -o-linear-gradient(top,#919191 0,#6a6a6a 100%);
  background: linear-gradient(to bottom,#919191 0,#6a6a6a 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#919191,endColorstr=#6a6a6a,GradientType=0);
  filter: alpha(opacity=50) progid:DXImageTransform.Microsoft.gradient(startColorstr=#919191,endColorstr=#6a6a6a,GradientType=0);
}
a.l-btn-plain-disabled,
a:hover.l-btn-plain-disabled {
  background: transparent;
  filter: alpha(opacity=50);
}
a.l-btn-selected,
a:hover.l-btn-selected {
  background-position: right -24px;
  background: #000;
  filter: none;
}
a.l-btn-selected span.l-btn-left,
a:hover.l-btn-selected span.l-btn-left {
  background-position: 0 bottom;
  background-image: none;
}
a.l-btn-plain-selected,
a:hover.l-btn-plain-selected {
  background: #000;
}
