@CHARSET "UTF-8";

#global-notification {
    position: relative;
    margin-top: 5px;
    padding: .75rem 1.25rem;
}

.global-notifications-info {
    background: #2d9b1f;
    background: -moz-linear-gradient(top, #2d9b1f 0%, #29db2f 50%, #2989d8 50%, #21d34b 51%, #7eea85 100%);
    background: -webkit-linear-gradient(top, #2d9b1f 0%,#29db2f 50%,#2989d8 50%,#21d34b 51%,#7eea85 100%);
    background: linear-gradient(to bottom, #2d9b1f 0%,#29db2f 50%,#2989d8 50%,#21d34b 51%,#7eea85 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#2d9b1f', endColorstr='#7eea85',GradientType=0 );
}

.global-notifications-warning {
    background: #fceabb;
    background: -moz-linear-gradient(top, #fceabb 0%, #fccd4d 50%, #f8b500 51%, #fbdf93 100%);
    background: -webkit-linear-gradient(top, #fceabb 0%,#fccd4d 50%,#f8b500 51%,#fbdf93 100%);
    background: linear-gradient(to bottom, #fceabb 0%,#fccd4d 50%,#f8b500 51%,#fbdf93 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#fceabb', endColorstr='#fbdf93',GradientType=0 );
}

.global-notifications-danger {
    background: #fcbdbd;
    background: -moz-linear-gradient(top, #fcbdbd 0%, #ff5a4f 50%, #f72d00 53%, #f9a193 100%);
    background: -webkit-linear-gradient(top, #fcbdbd 0%,#ff5a4f 50%,#f72d00 53%,#f9a193 100%);
    background: linear-gradient(to bottom, #fcbdbd 0%,#ff5a4f 50%,#f72d00 53%,#f9a193 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#fcbdbd', endColorstr='#f9a193',GradientType=0 );
}