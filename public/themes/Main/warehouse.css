.color-red {
    color: #FF0000;
}

.warehouse-style {
    height: 800px;
}

.warehouse-style > .north-region {
    height: 38px;
}

.warehouse-style .panel-nav {
    background-color: #f1f0f0;
}

.warehouse-style .warehouse-mainmenu {
    height: 32px;
}

.js-field-warehouse {
    margin-top: 23px;
    vertical-align: sub;
}

.warehouse-style .warehouse-mainmenu .panel-body {
    background: #0080ff24;
}

.warehouse-field-key {
    display: none!important;
}

.warehouse-style .warehouse-mainmenu a {
    border-radius: 0;
    -webkit-transition: background-color 300ms ease-out;
    -moz-transition: background-color 300ms ease-out;
    -o-transition: background-color 300ms ease-out;
    transition: background-color 300ms ease-out;
}
.warehouse-style .warehouse-mainmenu a:hover {
    background: #afcfef;
    border-radius: 0;
    border-color: transparent;
}

.warehouse-style .layout-split-north,
.warehouse-style .layout-split-west{
    border-color: #fff;
}

#win-add-edit-provider {
    height: 260px;
}

#win-add-edit-document {
    height: 160px;
}

#win-add-transaction {
    height: 600px;
}

#win-add-transaction h3 {
    font-size: 16px!important;
    font-weight: bold;
    color: #5a5959;
}

#win-add-edit-provider label,
#win-add-edit-contragent label,
#win-add-edit-item label,
#win-add-edit-document label,
#win-add-transactions label,
#win-sub-transactions label,
#win-transaction-details label,
#win-filter-report label,
#win-add-edit-warehouses label,
#win-transfer-transactions label{
    display: block;
}

#win-transaction-details label{
    font-weight: bold;
}

#win-transaction-details {
    width: 630px;
}

#win-transaction-details legend{
    font-weight: bold;
}

.add-trasaction-windows legend {
    margin-left: 10px; font-weight: bold; font-style: italic;
}

.document-number-block {
    padding: 5px;
}

.add-trasaction-windows input#tr-document-number{
    width: 350px;
}

#details-provider-number {
    width: 350px;
}

div#rightSlidePanel {
    position: absolute;
    z-index: 9007;
    top: 0;
    right: -825px;
    background: #fff;
    bottom: 0;
    width: 800px;
    padding: 10px;
    border-left: 1px solid #ccc;
}

div#rightSlidePanel #rightSlidePanelCloseBtn {
    display: none;
    cursor: pointer;
    position: absolute;
    z-index: 9007;
    background-color: #949393ba;
    font-size: 15px;
    top: 40px;
    left: -78px;
    padding: 7px 10px;
    color: #fff;
}

div#rightSlidePanel #rightSlidePanelCloseBtn:hover {
    opacity: 0.8;
}

div#rightSlidePanel a.l-btn {
    line-height: inherit;
}

#win-add-transactions {
    width: 900px;
}

.add-trasaction-windows,
#win-filter-report,
#win-doc-filter{
    padding: 10px;
}

#win-filter-report {
    width: 500px;
}

#win-filter-report input{
    width: 12%;
}

.action-btns {
    margin-top: 10px;
    text-align: center;
}

#providers-toolbar {
    padding-left: 3px;
}

.datagrid_search_box{
    width: 200px;
    line-height:20px;
    border:1px solid #ccc;
    padding-left: 25px;
}

.datagrid_search_box::placeholder{
    font-size: 11px;
}

.tr-main-info-block {
    width: 100%;
    padding: 10px;
}

.tr-main-info-block fieldset {
    padding: 10px 0;
}

.tr-main-info-block.tr-main-info-block_add-trans {
    height: 160px;
}

#selectProvider {
    margin-bottom: 15px;
}

#rightPanels {
    display: none;
}

#rightPanels > div{
    width: 800px;
    height: 800px;
}

#details-tr-note {
    width: 530px;
}

#farm-warehouses-combobox {
    width: 150px;
    margin-left: 5px;
    margin-right: 5px;
}

#items_btn_add_event {
    margin-left: 10px;
}

.selected-farm {
    font-weight: bold;
    margin-left: 10px;
    font-size: 13px;
    vertical-align: middle;
}

fieldset.farm-info {
    margin-top: 10px;
}

.code-include-group {
    margin-top: 5px;
}

.code-include-group input{
    width: auto!important;
}

.code-include-group label {
    display: inline-block!important;
    vertical-align: text-bottom;
}

input#note {
    width: 455px;
}


/*TEMPLATES BEGIN*/

/*providers grid*/

.providers-grid .datagrid-toolbar {
    background: #c7bbe4;
}

.providers-grid .datagrid-view {
    background: #dbedff;
}

.providers-grid .datagrid-header,
.providers-grid .datagrid-pager {
    background: #bbd0e4;
}

.provider-info {
    margin-bottom: 9px;
}

/*TEMPLATES END*/



/*GRID BEGIN*/
.container-box {
    display: block;
    padding: 5px;
    overflow: auto;
}

.col-1, .col-2, .col-3, .col-4, .col-5, .col-6, .col-7, .col-8, .col-9, .col-10, .col-11, .col-12 {
    float: left;
    padding: 0 5px 0 5px;
    box-sizing: border-box;
}

.col-1 {
    width: 8.33333333%;
}

.col-2 {
    width: 16.66666667%;
}

.col-3 {
    width: 25%;
}

.col-4 {
    width: 33.33333333%;
}

.col-5 {
    width: 41.66666667%;
}

.col-6 {
    width: 50%;
}

.col-7 {
    width: 58.33333333%;
}

.col-8 {
    width: 66.66666667%;
}

.col-9 {
    width: 75%;
}

.col-10 {
    width: 83.33333333%;
}

.col-11 {
    width: 91.66666667%;
}

.col-12 {
    width: 100%;
}

.clear {
    clear: both;
}

.fluid-input {
    width: 100% !important;
}
.fluid-input-50 {
    width: 50% !important;
}
/*GRID END*/

