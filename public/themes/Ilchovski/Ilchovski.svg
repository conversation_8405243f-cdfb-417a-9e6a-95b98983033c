<svg width="1024.33691" height="303.69501" xmlns="http://www.w3.org/2000/svg" xmlns:undefined="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd" version="1.1">

 <g>
  <title>Layer 1</title>
  <path fill-rule="evenodd" fill="#f07d01" id="path2" d="m108.3381,51.1428l13.2519,-5.4083l-11.492,-8.4534l-1.7599,13.8617z"/>
  <path fill-rule="evenodd" fill="#f07d01" id="path4" d="m73.1004,225.65l-8.6478,-11.1282c-0.7358,1.0475 -4.3994,10.57 -5.3135,12.4062l13.9613,-1.278z"/>
  <path fill-rule="evenodd" fill="#f07d01" id="path6" d="m243.1054,77.7271c1.7929,2.7447 5.822,7.9417 8.2341,10.5145c1.0426,-1.9929 4.3078,-10.2808 4.9119,-12.1443l-13.146,1.6298z"/>
  <path fill-rule="evenodd" fill="#f07d01" id="path8" d="m114.6564,255.8706c-2.229,-0.9374 -3.8226,-1.9406 -6.1424,-2.5864l1.5294,12.8744l10.3458,-7.675l-5.7328,-2.613z"/>
  <path fill-rule="evenodd" fill="#f07d01" id="path10" d="m232.427,242.7809c-6.2653,-0.1173 -6.536,-0.8442 -10.6398,2.5873l11.4214,4.5801l-0.7816,-7.1674z"/>
  <path fill-rule="evenodd" fill="#f17e05" id="path12" d="m136.6767,40.0636l-0.2081,1.3229l1.2322,-0.0433c2.3125,-0.0731 3.7672,-0.3776 6.075,-0.6756c1.0281,-0.1325 2.0298,-0.1807 3.0234,-0.3084l3.4957,-0.8619c-0.788,-1.4193 -5.3914,-6.0388 -8.5763,-11.2479c-1.6514,4.1865 -3.8804,8.3707 -5.0419,11.8142z"/>
  <path fill-rule="evenodd" fill="#f17e05" id="path14" d="m35.2256,135.3891c1.5423,1.6322 3.7632,2.6764 5.4187,4.0877c2.405,2.0499 2.711,3.1551 5.3497,4.6837l2.1021,-14.6279c-3.0564,1.3751 -10.3836,4.3014 -12.8705,5.8565z"/>
  <path fill-rule="evenodd" fill="#f17e05" id="path16" d="m45.8333,158.7764c-1.7856,1.6804 -3.6925,3.0604 -5.5946,4.7528c-1.9415,1.7286 -3.8893,2.6789 -5.5721,4.2974l13.1041,5.2428l-1.9374,-14.293z"/>
  <path fill-rule="evenodd" fill="#f17e05" id="path18" d="m55.0169,200.5975l1.3791,0.004l-0.4032,-1.1847l-4.462,-11.4118c-1.3286,1.0611 -7.7691,10.1715 -8.0775,10.9338l11.5636,1.6587z"/>
  <path fill-rule="evenodd" fill="#f17e05" id="path20" d="m265.2646,114.5078c1.3246,-3.625 4.989,-7.6758 7.1072,-10.133l-13.0191,-2.0619c1.9374,2.6347 3.1977,8.6839 5.1537,11.3547c0.0939,0.1285 0.2883,0.3205 0.3694,0.4233c0.082,0.1036 0.2523,0.2852 0.3888,0.4169z"/>
  <path fill-rule="evenodd" fill="#f17e05" id="path22" d="m280.0709,136.5522c-0.3478,-2.1704 0.388,-0.9945 -0.5502,-2.0732c-4.2171,-0.2659 -6.7039,-2.6644 -10.4407,-3.9657l1.6563,12.7869c2.1688,-1.453 6.6694,-6.2067 9.3346,-6.748z"/>
  <path fill-rule="evenodd" fill="#f17e05" id="path24" d="m141.3018,274.1389l7.8533,-9.7723l-12.8905,-1.3944l5.0372,11.1667z"/>
  <path fill-rule="evenodd" fill="#f17e05" id="path26" d="m206.8725,253.9621l-11.6375,4.7561c1.6194,1.5109 3.0917,2.131 4.9424,3.4692c1.8041,1.3037 2.9062,2.7784 5.0308,3.9841l1.6643,-12.2094z"/>
  <path fill-rule="evenodd" fill="#f17e05" id="path28" d="m251.4753,216.1741c-0.1326,0.1109 -0.3053,0.2699 -0.3824,0.3486c-0.0771,0.0771 -0.2803,0.2555 -0.3727,0.3519c-0.0908,0.0955 -0.2538,0.2546 -0.3502,0.3622l-2.9214,3.8885c-1.2587,1.6033 -2.4588,2.7745 -3.5737,4.4886l12.2383,1.5454l-4.6379,-10.9852z"/>
  <path fill-rule="evenodd" fill="#f17e05" id="path30" d="m269.2977,172.7184c4.0781,-1.5326 6.3023,-3.4435 11.1298,-4.3375l-9.6397,-8.3064l-1.4901,12.6439z"/>
  <path fill-rule="evenodd" fill="#f17e05" id="path32" d="m272.0617,199.2192c-1.0113,-2.1511 -1.9439,-2.601 -3.5447,-4.7183c-1.1045,-1.4603 -1.9816,-3.3367 -3.7223,-4.9673l-4.5295,11.1973l11.7965,-1.5117z"/>
  <path fill-rule="evenodd" fill="#c7c7c7" id="path38" d="m152.8878,249.0246c9.7097,0.4016 21.0459,-0.7414 30.5395,-2.0885c4.1215,-0.584 22.1793,-3.4339 25.9562,-6.2452c-8.4382,0.898 -12.7476,3.2475 -28.6583,4.3873c-16.4064,1.1751 -22.8404,-2.3881 -27.8374,3.9464z"/>
  <path fill-rule="evenodd" fill="#c7c7c7" id="path40" d="m182.3775,224.2211c2.3679,0.2176 3.4507,-0.0932 5.854,-0.3663c0.7109,-0.0803 4.7585,-0.3486 5.0324,-1.086c0.0827,-0.2201 0.2972,-0.2153 0.441,-0.3293c-2.2026,-1.9359 -3.6845,-0.8884 -9.672,-0.5455l-1.6554,2.3271z"/>
  <path fill-rule="evenodd" fill="#ee7d01" id="path108" d="m50.9484,44.381c0.2699,1.662 4.7303,20.2716 5.4002,21.5793c3.3897,6.6156 12.9653,8.1433 18.5784,2.3736c5.7544,-5.9159 3.3383,-14.8745 -2.7761,-18.2024c-4.1889,-2.2796 -15.6103,-3.6275 -21.2025,-5.7505z"/>
  <path fill-rule="evenodd" fill="#ee7d01" id="path110" d="m26.7979,227.2686c7.8703,-0.7784 18.7615,2.2499 25.116,-1.041c5.7007,-2.9528 7.8421,-12.2166 2.7351,-17.8c-5.7248,-6.2589 -14.379,-4.7239 -18.441,1.3944l-9.4101,17.4466z"/>
  <path fill-rule="evenodd" fill="#ee7d01" id="path112" d="m304.3017,112.5543c-5.72,-1.3422 -17.7663,-5.993 -22.2363,-5.9416c-8.1434,0.094 -14.3107,8.8799 -9.9667,16.9983c3.3744,6.3071 12.2157,8.3217 18.2843,3.0933c4.5986,-3.9632 3.8588,-6.0806 13.9187,-14.15z"/>
  <path fill-rule="evenodd" fill="#ee7d01" id="path114" d="m288.8448,75.7816c-6.8276,0.6828 -20.4363,-1.1719 -24.6887,0.8466c-5.8548,2.7793 -8.9899,12.6078 -3.0218,18.4996c5.7746,5.6999 14.477,4.1046 18.2835,-1.8538l9.427,-17.4924z"/>
  <path fill-rule="evenodd" fill="#ee7d01" id="path116" d="m81.9722,282.5417c3.1809,-1.5816 4.9834,-3.4283 8.8277,-5.1705c3.3271,-1.5069 6.5738,-2.7536 9.1265,-4.503c6.0927,-4.1761 6.8999,-12.7235 0.9929,-18.1936c-5.7722,-5.3456 -15.236,-2.7463 -17.9912,3.2323c-2.6274,5.7006 -0.5912,17.4489 -0.9559,24.6348z"/>
  <path fill-rule="evenodd" fill="#ee7d01" id="path118" d="m168.4949,284.9836c3.7656,-8.549 -2.8411,-15.9598 -10.8768,-15.8899c-8.0879,0.0699 -13.7476,8.0405 -10.553,16.2361c1.1912,3.0555 3.8234,6.0075 5.6613,8.6614c1.7542,2.5326 3.0748,6.0621 4.8974,8.716l10.8711,-17.7236z"/>
  <path fill-rule="evenodd" fill="#ee7d01" id="path120" d="m157.7001,0c-1.3744,3.8435 -3.1158,6.3408 -5.0774,9.3297c-1.6314,2.4869 -4.4323,5.7681 -5.5552,8.7345c-3.1158,8.2285 2.5832,15.9566 10.5571,16.0337c8.0887,0.0779 14.4046,-8.0124 10.7892,-16.3028l-10.7137,-17.7951z"/>
  <path fill-rule="evenodd" fill="#ee7d01" id="path122" d="m279.1865,209.7023c-4.0443,-6.1143 -12.7419,-7.2814 -18.2152,-1.5607c-5.2782,5.5175 -3.2227,14.9268 2.9543,18.1655c4.7906,2.5126 17.5028,0.5711 24.6228,1.0972l-9.3619,-17.702z"/>
  <path fill-rule="evenodd" fill="#ee7d01" id="path124" d="m196.9556,5.6139c-2.3013,2.0547 -4.0877,4.3897 -6.6252,6.7408c-2.2772,2.1102 -5.5561,4.3448 -7.6549,6.6317c-5.6894,6.201 -2.9616,14.8793 3.0748,18.212c8.0734,4.4564 16.3782,-1.7061 16.9782,-9.2486c0.241,-3.0315 -4.5536,-18.4675 -5.7729,-22.3359z"/>
  <path fill-rule="evenodd" fill="#ee7d01" id="path126" d="m304.2519,190.8341c-6.3842,-4.5416 -8.843,-9.6326 -13.8079,-14.1669c-5.8074,-5.3039 -14.787,-3.9898 -18.3654,3.1045c-4.2838,8.4944 2.041,16.6128 10.0068,16.7493c2.4821,0.0426 16.7349,-4.3495 22.1665,-5.6869z"/>
  <path fill-rule="evenodd" fill="#ee7d01" id="path128" d="m118.4349,5.5649c-0.7896,8.2461 -5.9577,16.0224 -5.6155,22.3255c0.3992,7.3481 8.863,13.6022 16.8079,9.4405c6.5642,-3.4387 8.7402,-11.8599 3.0974,-18.4923l-14.2898,-13.2737z"/>
  <path fill-rule="evenodd" fill="#ee7d01" id="path130" d="m264.6879,44.7795c-5.6244,1.6442 -19.2274,4.2765 -21.4307,5.5464c-16.289,9.3859 4.4605,32.4054 15.5228,16.2168c1.3896,-2.0339 5.0838,-18.2209 5.9079,-21.7632z"/>
  <path fill-rule="evenodd" fill="#ee7d01" id="path132" d="m50.6126,258.625c3.295,-1.0483 6.818,-2.4853 10.8768,-3.1576c3.8436,-0.637 7.9498,-0.788 10.9162,-2.6081c16.4561,-10.0929 -5.5312,-32.6424 -15.873,-15.9196c-1.8692,3.0226 -2.2531,7.1658 -2.8773,10.7595c-0.8112,4.6677 -1.8779,6.8878 -3.0427,10.9258z"/>
  <path fill-rule="evenodd" fill="#ee7d01" id="path134" d="m82.2196,20.4218c-0.0618,5.1657 -0.8988,21.3832 0.5736,24.5368c2.5808,5.5279 11.6575,9.5442 18.408,3.3849c6.1127,-5.577 4.1986,-13.9565 -1.5189,-18.2297c-2.3953,-1.7905 -6.2381,-3.1833 -9.0567,-4.536c-3.6106,-1.7326 -5.5343,-3.5094 -8.406,-5.156z"/>
  <path fill-rule="evenodd" fill="#ee7d01" id="path136" d="m233.5676,20.8756l-17.877,9.3627c-5.479,3.9865 -7.1875,11.9981 -1.4274,17.9076c4.9255,5.054 14.5749,4.172 18.1582,-2.5238c2.6572,-4.9649 0.6137,-17.6185 1.1462,-24.7465z"/>
  <path fill-rule="evenodd" fill="#ee7d01" id="path138" d="m11.6631,190.7931c7.3875,1.1478 17.7036,6.1625 22.8147,5.7504c7.1794,-0.5783 12.8696,-9.3578 9.1803,-16.5782c-3.4829,-6.8148 -12.6174,-8.998 -18.7944,-3.2443c-2.4186,2.2523 -4.3399,5.1906 -6.6886,7.6132c-0.8193,0.8442 -6.0678,5.7408 -6.512,6.4589z"/>
  <path fill-rule="evenodd" fill="#ee7d01" id="path140" d="m291.0569,140.7347c-8.312,-3.111 -15.8392,3.18 -15.7115,10.9675c0.1301,8.0092 7.532,13.9267 15.9629,10.5997l17.4675,-10.5908l-17.7189,-10.9764z"/>
  <path fill-rule="evenodd" fill="#ee7d01" id="path142" d="m11.6664,112.2659c1.3928,1.8676 4.572,3.7882 6.8227,6.52c2.0162,2.4467 4.5103,5.8758 6.5441,7.7393c6.2292,5.7087 15.0384,3.666 18.5125,-3.1977c3.7768,-7.4614 -1.5575,-16.0409 -9.0109,-16.6899c-5.9376,-0.5181 -11.7611,3.0772 -16.8738,4.4323c-1.8483,0.49 -4.2974,0.5543 -5.9946,1.196z"/>
  <path fill-rule="evenodd" fill="#ee7d01" id="path144" d="m196.8014,297.7793c1.9189,-8.32 6.1721,-18.7888 5.8685,-22.9769c-0.547,-7.5497 -9.2028,-12.6945 -16.6144,-8.9707c-6.5705,3.3014 -9.0068,11.9467 -3.2652,18.4924c3.8074,4.3407 7.2019,5.2717 14.0111,13.4552z"/>
  <path fill-rule="evenodd" fill="#ee7d01" id="path146" d="m6.5866,151.7584c8.2887,2.997 12.9411,8.6486 17.955,10.6174c8.0357,3.1559 15.7959,-3.0893 15.7493,-10.6623c-0.0522,-8.6454 -8.2052,-14.2472 -16.5284,-10.7379l-17.1759,10.7828z"/>
  <path fill-rule="evenodd" fill="#ee7d01" id="path148" d="m233.3066,282.4485c-0.0755,-6.0605 1.188,-21.3327 -1.0033,-25.2236c-2.9463,-5.234 -11.774,-7.7947 -17.9124,-2.4844c-5.8886,5.0942 -4.364,13.9114 1.4434,18.2811c2.3222,1.7463 6.1465,3.2082 9.0575,4.5914c3.2346,1.5366 5.5665,3.5439 8.4148,4.8355z"/>
  <path fill-rule="evenodd" fill="#ee7d01" id="path150" d="m27.2984,75.8571c1.0506,3.4211 3.1784,5.393 4.8411,8.8663c1.5294,3.1937 2.4524,6.2958 4.5705,9.0662c4.3231,5.6541 12.4046,7.0028 17.8892,1.1423c5.1183,-5.4694 3.2579,-14.3203 -2.56,-17.833c-4.6452,-2.8057 -17.8104,-0.9855 -24.7408,-1.2418z"/>
  <path fill-rule="evenodd" fill="#ee7d01" id="path152" d="m118.9136,297.9351c4.6139,-7.7601 10.7081,-9.6389 14.3332,-14.191c5.4733,-6.8717 2.0314,-15.4785 -4.4115,-18.2289c-7.9593,-3.3985 -16.0424,2.8267 -16.0561,10.6551c-0.003,1.7543 5.5087,20.8266 6.1344,21.7648z"/>
  <path fill-rule="evenodd" fill="#ee7d01" id="path154" d="m250.9291,242.6885l-13.5557,0.3936c1.8451,12.7652 11.068,11.1692 27.2928,15.522c-1.1607,-4.5022 -2.3134,-7.0525 -3.164,-12.0712c-0.7374,-4.3528 -1.2884,-8.1811 -3.413,-10.9973c-5.0532,-6.6991 -14.7774,-5.1657 -18.8828,1.4924l11.7227,5.6605z"/>
  <path fill-rule="evenodd" fill="#ee7d01" id="path156" d="m65.133,89.2304c1.9261,-2.3471 7.7714,-9.6599 8.573,-11.8278l-14.2769,-1.2635l4.5271,11.4703c0.8193,1.5929 0.3261,0.8483 1.1768,1.621z"/>
  <path fill-rule="evenodd" fill="#ee7d01" id="path158" d="m84.0101,67.2736c1.2394,-0.2305 10.7932,-7.8341 11.5459,-8.7337c-2.6997,-2.021 -9.6768,-3.2515 -13.1275,-5.7754l1.5816,14.5091z"/>
  <path fill-rule="evenodd" fill="#ee7d01" id="path160" d="m43.0782,104.0222c0.8345,1.4081 3.1029,3.3777 4.4773,5.426c1.3422,2 3.5415,4.7046 4.2933,6.536l5.7127,-14.0448l-14.4833,2.0828z"/>
  <path fill-rule="evenodd" fill="#ee7d01" id="path162" d="m174.8944,29.0703l-1.9463,0.09c-1.4161,4.3247 -6.2484,7.6212 -7.3634,10.8704l13.8577,1.4619c-0.8707,-4.2291 -4.5223,-7.4967 -4.548,-12.4223z"/>
  <path fill-rule="evenodd" fill="#ee7d01" id="path164" d="m193.9643,45.206l13.4046,5.4444l-2.0451,-13.6416c-1.0313,0.6226 -3.7262,3.0387 -5.295,4.1038c-2.3406,1.5897 -3.7648,2.3431 -6.0645,4.0934z"/>
  <path fill-rule="evenodd" fill="#ee7d01" id="path166" d="m82.2839,250.1274c3.7688,-2.2676 8.0333,-3.1985 12.4423,-4.9946c-2.9399,-2.6885 -8.9072,-6.3232 -10.847,-8.7795l-1.5953,13.7741z"/>
  <path fill-rule="evenodd" fill="#ee7d01" id="path168" d="m220.6378,58.7495c4.36,1.9407 7.0774,5.6388 11.3386,7.6213l1.1398,-12.8914l-12.4784,5.2701z"/>
  <path fill-rule="evenodd" fill="#ee7d01" id="path170" d="m178.6632,263.0685l-12.3298,1.3905l7.6124,9.5932l4.7174,-10.9837z"/>
  <path fill-rule="evenodd" fill="#3c3c3a" id="path172" d="m171.887,226.4412c3.4837,-1.7061 8.3739,-8.1176 10.5097,-13.2166c8.1257,-19.4065 1.2531,-38.7334 -12.8768,-51.6497c-15.9389,-14.5701 -67.3781,-22.5359 -79.4959,-38.1518c-3.2949,-4.246 -4.8596,-12.0817 -2.4186,-17.9229c8.4912,-20.319 47.3676,-21.3552 69.0601,-9.2671c5.3271,2.968 9.1353,6.2308 12.1941,11.5869c6.5095,11.3989 1.3607,18.151 6.4461,16.8224c1.7036,-1.2362 1.208,-2.1093 1.2852,-4.8468l-1.1045,-38.3141c-9.1394,-3.3745 -29.1643,-5.92 -39.625,-6.4357c-22.9263,-1.1301 -55.2997,3.658 -61.0268,29.9532c-9.4687,43.4742 41.3319,41.8693 75.5615,60.0652c18.4643,9.8149 34.4979,33.4891 21.4909,61.377z"/>
  <path fill-rule="evenodd" fill="#3c3c3a" id="path174" d="m193.5233,100.5602c-1.3864,6.2308 -0.2883,33.228 -0.2883,41.6291c0,19.4491 -2.3094,62.8598 5.3673,77.3697c3.6065,6.818 14.1427,17.6337 24.5818,17.3686l-1.3117,-1.1173c-7.3184,-5.8589 -11.1981,-8.3385 -13.3669,-22.6998c-3.0933,-20.486 0.396,-104.9363 -1.5944,-112.6941l-13.3878,0.1438z"/>
  <path fill-rule="evenodd" fill="#c6c6c6" id="path176" d="m96.3319,219.8281c4.2757,-0.1125 5.4348,0.8378 7.773,3.7431c6.5441,8.1329 10.6752,10.0061 19.3575,16.2963c-5.2267,0.2651 -15.2898,-4.0572 -19.4314,-5.9954c-6.4492,-3.017 -12.3836,-7.2694 -7.6991,-14.044zm12.4102,2.478c4.0443,0.2795 8.8261,1.3696 13.1564,1.9214c18.6345,2.3744 13.2576,-2.3608 19.527,10.3265c2.1446,4.3408 4.2837,6.0549 6.2388,9.7812c-12.2255,-1.0073 -14.1629,-1.5551 -23.0637,-7.2582c-5.8139,-3.7254 -12.8929,-9.8759 -15.8585,-14.7709zm-3.6299,-7.9915c8.2084,-2.4499 16.8168,-6.622 26.5353,-7.312l3.6315,13.1002c-6.6782,-0.1815 -19.6499,-2.6459 -26.6518,-4.442c-0.555,-0.1421 -1.0595,-0.2546 -1.6024,-0.4482l-1.4491,-0.6482c-0.1052,-0.0498 -0.3141,-0.1574 -0.4635,-0.2498zm-27.5177,-2.2427c3.3134,0.972 8.6599,3.4114 11.8038,5.0292l-1.9977,5.0428c-1.2627,2.5519 -0.0795,0.6233 -1.3133,1.796c-3.0941,-4.9737 -5.2613,-5.1223 -8.4928,-11.868zm4.5521,-29.6366c1.9294,4.1937 3.4684,10.5442 5.5335,15.3557c2.1576,5.0283 5.5208,9.806 7.0381,13.562c-6.3103,-1.3294 -16.6835,-7.1641 -20.0868,-10.6502c-8.2285,-8.4269 2.6627,-16.6305 7.5152,-18.2675zm7.0219,2.3093c8.043,1.0748 17.0996,3.9882 29.1507,5.471c1.7439,0.2152 3.4797,0.3919 5.226,0.5558c2.984,0.2803 3.1776,0.053 5.0829,1.0643l1.7639,9.3016c-15.203,1.6419 -29.2543,7.7345 -31.2551,7.8036c-3.1022,-4.2388 -8.8663,-17.9342 -9.9684,-24.1963zm2.6387,-6.5103c0.7045,-0.7607 -0.9558,0.1807 1.486,-1.1262l14.5685,-4.4074c4.1416,-0.9759 14.4175,-3.6507 18.3454,-3.2596l1.5961,16.2923c-6.9746,-0.3863 -30.599,-4.846 -35.996,-7.4991zm-31.3098,-7.1931c2.9688,2.4443 8.3249,5.8115 12.8215,7.622l-2.0307,2.356c-5.058,4.9383 -2.9045,3.6065 -6.1552,7.4662c-1.9945,-2.7134 -5.2171,-13.5075 -4.6356,-17.4442zm-1.335,-28.2213c0.1237,-5.1311 1.4675,-15.5694 3.993,-19.0177c3.0121,6.3183 6.3135,6.8509 9.1514,10.9065l-13.1444,8.1112zm153.5481,-72.6466c5.2709,-0.0835 16.5349,6.855 19.127,9.3563c6.7223,6.4878 -1.3077,14.1444 -7.7835,15.7099c-5.9834,-11.2166 -12.8375,-17.408 -14.787,-21.0788l3.4435,-3.9874zm-5.4404,2.0066c-2.1109,-0.9615 -6.0107,-4.3247 -7.5071,-6.0292c2.335,0.1815 3.8114,0.6233 6.0532,1.3077c1.7808,0.5446 4.0251,0.9438 5.2766,2.1302l-3.8227,2.5913zm-52.2938,185.4547c-7.7,-2.3736 -13.946,-0.6153 -31.8737,-8.1779c-2.4202,-1.0209 -7.0871,-3.5905 -8.7145,-4.164c1.3302,-0.0531 20.2957,3.213 28.9595,3.6933c5.0187,0.2787 6.1095,-0.4635 7.4751,-4.1038c-3.7127,-3.4419 -8.9273,-12.7821 -9.8109,-19.1028c4.8717,0.1212 9.6912,0.2891 14.5717,0.2096c5.7818,-0.094 9.5458,1.3374 11.5371,-3.5014l-27.582,-1.4233c-1.3904,-3.3118 -3.1768,-11.3242 -3.2025,-15.1284c11.6872,-1.1567 21.9511,-2.0876 34.1734,-2.515l-0.2972,-2.7519c-6.7361,-2.0113 -27.1595,-0.1839 -35.249,-0.3582l-0.886,-8.5474c10.9025,0.143 23.1352,1.2266 33.7839,-0.012c-1.5503,-5.1938 -2.3182,-2.9994 -16.2827,-3.6934c-4.752,-0.2361 -14.3187,-0.1727 -18.0996,-2.086l-1.2249,-17.2433c2.4651,-1.372 5.9022,-1.1334 8.7457,-1.7624c-21.1278,-8.2903 -11.5611,-4.8114 -17.5582,-3.2218l-17.0296,2.7086c-7.1112,1.4731 -14.2906,4.519 -20.221,5.7086c-1.0185,-9.8743 -0.939,-17.2818 -1.3784,-27.2517c-2.1985,-1.78 -3.9142,-2.8709 -6.054,-4.1046c-2.1013,14.2769 1.5181,26.4156 1.2514,33.203c-23.6066,-9.6084 -27.994,-22.3712 -2.6057,-33.8593c-0.2177,-0.6828 -5.3256,-8.7008 -6.4654,-9.8896c-2.3302,-2.4314 -7.6284,-4.9472 -7.1577,-9.6759c0.2932,-2.9431 3.6242,-8.7458 4.5303,-11.954c3.3664,-11.9137 4.2203,-20.8338 20.0451,-30.2696c10.4342,-6.2211 10.7547,-8.124 19.8105,-13.5347c27.9393,-16.6963 62.5842,-19.9792 93.5254,-6.8228l10.4414,5.3738c0.1205,0.0963 0.3148,0.2867 0.4112,0.3678c0.098,0.0828 0.2804,0.245 0.4073,0.3808c-4.348,-0.5165 -8.9032,-1.7832 -13.4793,-2.4764c-6.8373,-1.0362 -7.5104,-0.3205 -13.2151,-3.1488c-7.7625,-3.8475 -16.7878,-6.0396 -26.5891,-6.2091c-10.1916,-0.1759 -18.9486,1.7463 -27.4935,5.3007c-5.5368,2.3029 -17.347,9.4253 -19.5856,12.9933c-3.5351,-1.2997 -1.7945,-0.6828 -3.8235,-3.0106c-0.1888,0.2852 -0.9502,-0.4056 0.2659,2.8323c3.7246,0.7647 15.7854,-0.7446 18.5429,-2.421c3.3857,-2.0571 11.3009,-6.2155 15.5373,-6.5561c-0.8828,1.8394 -3.6693,6.0774 -3.3552,8.1642l6.3007,0.5173c1.494,-1.4611 1.6964,-2.5567 2.9945,-4.446c1.1784,-1.7141 1.7198,-2.5158 3.356,-4.1905c6.9336,-7.0991 13.7002,1.4233 17.3911,7.9843c3.552,6.3127 1.0314,4.9415 8.9313,6.6669c-0.4699,-5.1054 -4.589,-9.8591 -6.0725,-14.3789c10.4976,-0.1141 22.8018,7.2565 27.859,12.435c-3.4395,2.7158 -13.9058,3.4106 -19.2595,4.0083c0,5.7046 0.4506,30.8238 1.7358,35.8578c0.294,1.1502 0.9688,4.1511 1.0539,5.299c0.2755,3.7319 -1.384,2.8957 -1.9222,4.4805c2.1383,4.0395 2.1399,-4.0845 3.5391,9.2341c0.4362,4.1552 0.9784,8.1956 0.9736,12.2994c-6.5425,2.1013 -22.7488,1.004 -30.5315,1.4932c1.4017,1.1703 1.3101,0.9551 3.2202,1.8081c12.4512,5.5577 5.0942,6.3722 27.7378,3.9255c1.641,-5.25 0.0972,-29.4029 -0.2193,-36.249c-0.1871,-4.0243 0.9495,-4.6179 0.7101,-8.1955c-0.3229,-4.83 -0.3109,-1.6635 -1.3543,-3.4323c-0.8587,-1.4547 -0.7735,-1.5222 -1.0868,-3.4724c-0.8386,-5.2204 -2.6435,-11.5026 -3.0676,-16.175c3.0226,-1.7527 8.1144,-2.0604 11.7371,-3.2307c3.2596,-1.0539 7.9907,-3.5865 10.7659,-4.16c3.6484,2.7374 14.3605,14.611 15.473,19.6322l-7.0397,2.874l0.0209,7.0011c4.1889,-0.3832 7.5248,-3.1078 11.268,-3.1471c3.1125,4.9456 9.4638,21.7431 9.5458,27.8952c-2.474,-0.2145 -7.0461,-1.9969 -10.0872,-2.7978l-7.7723,-1.8506c-4.5319,0.012 -1.0466,-0.6876 -2.9182,0.9671c4.2388,0.4144 8.6944,1.5053 13.0665,2.5896c5.6709,1.4057 8.4967,0.3326 8.769,6.3031c-7.7047,2.6491 -13.8239,3.36 -21.7543,5.4854l-0.1655,5.5649c3.3054,0.0289 13.9243,-3.2829 17.8771,-4.899c3.5528,-1.4523 4.1174,-1.8611 5.8741,0.7302c0.2643,-5.7481 5.3665,-4.7986 9.251,-7.4333c4.2597,-2.8893 12.599,-9.239 12.648,-15.2143c-2.4652,5.9279 -17.1413,17.2497 -22.1528,11.3193c-1.5848,-2.8499 -2.5125,-11.5507 -3.5431,-15.4577c-1.2161,-4.6082 -4.3978,-11.325 -4.944,-15.1701c2.1005,-2.1672 5.5834,-3.9014 7.9425,-6.0332c3.5279,-3.1889 2.8821,-4.0693 5.3657,-8.2421c8.1385,5.7617 18.747,29.3916 21.8998,42.6814c1.4868,6.2645 1.7639,13.509 3.0459,18.5919c1.4169,-27.3024 -11.9781,-54.4634 -27.684,-71.4706c-32.5757,-35.2738 -88.2954,-45.4309 -131.3246,-20.4619c-70.6336,40.9873 -72.6891,135.7128 -11.6591,180.4786c13.517,9.9161 36.9887,21.213 59.4243,20.0258z"/>
  <path fill-rule="evenodd" fill="#c6c6c6" id="path178" d="m128.7574,105.2062c-12.4977,0.2378 -25.8702,-7.2614 -30.0953,-4.2355c2.6371,4.7263 24.3208,7.2629 29.0439,9.9354l-0.9535,10.6566c-7.2886,2.7793 -18.3052,0.4997 -24.2878,4.3046c9.6695,6.6525 11.8399,2.6283 23.5577,1.7873l-0.3141,8.3505c2.3109,0.7262 4.7641,2.0483 6.985,1.7776l0.682,-10.7209c5.6123,-2.9463 29.2342,-1.4676 32.848,-2.711l0.8699,-2.5631c-4.4588,-2.5905 -25.9682,-0.0129 -32.8255,-0.3872c-0.4474,-2.482 0.3414,-7.0927 1.1551,-9.5731c9.8759,0.4474 19.2715,2.0113 29.7788,1.7864c-0.9543,-2.9174 -2.2885,-5.148 -4.1777,-6.4348l-24.2733,-0.7944c-0.9045,-3.8966 0.8723,-7.5305 1.2233,-11.1571l-6.8485,-0.7599l-2.368,10.7386z"/>
  <path fill-rule="evenodd" fill="#d5d5d4" id="path180" d="m212.9531,169.393c2.0892,1.943 9.5522,2.931 12.9837,3.8917c3.2403,0.9069 9.884,2.5736 12.3845,4.9496l-6.2059,2.2989c-2.1375,0.5936 -4.3761,1.1125 -6.5071,1.6507c-3.4299,0.8667 -10.9636,1.4539 -12.7781,3.5311c4.3118,2.7157 23.7472,-7.4927 29.0366,-6.2734c-0.5864,-1.2732 -0.3735,0.7044 -0.1092,-0.9952c0.016,-0.0988 0.4104,-0.7471 0.5349,-0.9358c-4.7391,0.5574 -4.5271,-0.4378 -8.2405,-2.2146c-3.3704,-1.6129 -18.7904,-7.4445 -21.0989,-5.903z"/>
  <path fill-rule="evenodd" fill="#c9c9c9" id="path182" d="m179.9902,163.9526c1.1117,1.9125 0.6522,1.9671 3.454,2.3615c4.3873,0.6169 3.9592,-0.882 4.0716,-1.0016c-1.2273,-2.6002 -6.4966,-1.8114 -7.5256,-1.3599z"/>
  <path fill-rule="evenodd" fill="#f8bb86" id="path184" d="m137.7008,41.3432l-1.2322,0.0433l0.2081,-1.3229c-0.9631,0.8771 -0.8828,-1.4924 -1.0676,2.2065c2.6957,-0.4032 1.0684,-0.0442 2.0917,-0.9269z"/>
  <path fill-rule="evenodd" fill="#f9b878" id="path186" d="m55.0169,200.5975l2.1494,0.9189c-0.5229,-3.2539 -0.2385,-1.3599 -1.1735,-2.0996l0.4032,1.1847l-1.3791,-0.004z"/>
  <path fill-rule="evenodd" fill="#f0ba8c" id="path188" d="m280.0709,136.5522l1.3952,-0.9639l-1.9454,-1.1093c0.9382,1.0787 0.2024,-0.0972 0.5502,2.0732z"/>
  <path fill-rule="evenodd" fill="#f6ce97" id="path190" d="m172.9481,29.1603l1.9463,-0.09l-0.7229,-1.2884l-1.2234,1.3784z"/>
  <g stroke="null" id="svg_37">
   <path stroke="null" fill-rule="evenodd" fill="#202020" id="path34" d="m721.65808,62.14837c6.04789,5.05254 7.62414,3.08989 8.66439,14.37251l-0.14377,47.13868c-1.24856,11.72493 -6.97395,13.28714 -8.41831,15.08336l14.84245,-1.24317l-0.30523,-74.9665l-14.63954,-0.38489z"/>
   <path stroke="null" fill-rule="evenodd" fill="#202020" id="path36" d="m467.01281,232.358c-11.0851,-22.80809 -11.02272,-31.55581 -1.57452,-56.04419c-18.98123,11.09029 -17.28713,46.0464 1.57452,56.04419z"/>
   <path stroke="null" fill-rule="evenodd" fill="#1c1c1c" id="path42" d="m749.94548,131.3027c-3.94472,-10.52321 -2.21607,-26.27078 -2.24716,-38.54358c-0.04857,-19.58997 -2.275,-20.35802 9.92159,-31.0999l-15.91228,0.76308l0.21133,75.39823l45.54365,0.81489l5.24682,-17.9045c-6.54395,3.40894 -2.94764,8.80665 -16.22097,11.24636c-7.74718,1.42514 -20.60778,1.94192 -26.54299,-0.67458z"/>
   <path stroke="null" fill-rule="evenodd" fill="#1c1c1c" id="path44" d="m806.48897,66.16411c7.5011,7.98118 4.0384,58.62939 3.08126,62.26543c-1.54667,5.87628 -7.20968,9.36855 -7.21141,9.36682c-0.14226,-0.27048 -0.61047,0.50102 -0.88095,0.879l15.01925,-0.59644l-0.40388,-75.55257l-14.8131,-0.86713l5.20883,4.50489z"/>
   <path stroke="null" fill-rule="evenodd" fill="#1c1c1c" id="path46" d="m1007.53839,189.65125c-0.01079,-17.09306 -0.3225,-16.35783 8.99747,-25.31364l-14.80791,0.60852l0.52542,75.40708l15.08357,0.5062c-3.96738,-3.80764 -6.18151,-3.11925 -8.25878,-8.87767c-2.38941,-6.62188 -1.53459,-34.21915 -1.53977,-42.3305z"/>
   <path stroke="null" fill-rule="evenodd" fill="#1e1e1c" id="path48" d="m527.73847,224.81201c-10.1625,6.05135 -20.01307,16.57974 -39.28745,9.63363c-24.77441,-8.92796 -25.56857,-60.48476 6.69484,-65.20788c22.20475,-3.24942 23.44964,8.36434 30.70616,20.17605c2.34602,-4.9731 1.39233,-15.28821 1.5484,-21.86325c-17.32188,-3.48514 -30.9924,-7.97255 -47.13695,1.48429c-26.72345,15.65216 -25.10382,83.91022 34.59541,71.29917c12.53982,-2.64931 11.55677,-2.4777 12.87959,-15.522z"/>
   <path stroke="null" fill-rule="evenodd" fill="#1e1e1c" id="path50" d="m924.72757,61.85004c0.32768,0.22536 0.79762,0.46303 1.0368,0.59298l5.06657,2.67889c10.00146,6.22836 6.51783,30.47044 6.56122,47.42664c-6.43451,-3.36728 -12.5953,-9.45683 -18.07806,-13.95136c-5.69754,-4.67283 -9.99261,-9.24183 -15.65389,-13.68585c-11.13519,-8.74254 -25.54418,-25.62923 -33.68165,-21.5129l73.11653,61.70029l0.04684,-23.53124c0.03454,-34.48618 -2.11375,-29.23936 10.97739,-39.44525l-29.39176,-0.27221z"/>
   <path stroke="null" fill-rule="evenodd" fill="#1e1e1c" id="path52" d="m628.42616,81.3759c3.99156,-3.70554 3.31547,-8.36456 6.91502,-11.26017c4.41984,-3.55638 20.28873,-2.50037 27.618,-2.49864c0,17.28734 3.59113,59.79291 -5.19652,67.48807l-3.6587,2.75876c-0.15758,-0.28429 -0.664,0.46648 -0.95175,0.8028l15.59669,-0.62428l0.01943,-75.38441l-39.11217,-0.77668l-1.23,19.49456z"/>
   <path stroke="null" fill-rule="evenodd" fill="#1e1e1c" id="path54" d="m377.52463,164.39309c11.32256,12.975 8.42845,11.96929 8.42845,39.93936c0,24.46572 2.0913,27.04423 -9.10994,36.45422l15.07472,-0.5688l-0.07447,-75.31512l-14.31876,-0.50966z"/>
   <path stroke="null" fill-rule="evenodd" fill="#1d1d1d" id="path56" d="m672.26188,169.17709c37.28465,-10.90831 39.8718,57.44343 14.59442,65.68818c-20.71528,6.7555 -31.15538,-7.62587 -32.72623,-24.80571c-1.69411,-18.52015 0.42828,-35.70323 18.13181,-40.88248zm-24.3688,43.96374c6.81616,44.8708 70.09766,39.62549 61.15415,-21.79892c-6.49193,-44.58111 -70.57105,-40.19084 -61.15415,21.79892z"/>
   <path stroke="null" fill-rule="evenodd" fill="#1d1d1d" id="path58" d="m592.1837,164.19881l-1.01435,2.64089c9.76358,2.72918 8.90357,18.92058 8.01248,31.35635l-35.6376,-0.02418c-3.66388,-30.69926 6.48157,-29.248 8.71124,-34.22088l-15.10948,1.32109l0.43518,75.02392l15.40608,0.77668c-5.17925,-5.18789 -8.33326,-3.83549 -9.55743,-13.78817c-0.82547,-6.7052 -0.31214,-15.43371 -0.33632,-22.7647l36.25131,-0.11981c0.37453,16.74984 3.10544,28.14169 -9.77934,36.59129l15.6867,-0.56708l-0.17679,-75.46946l-12.89168,-0.75596z"/>
   <path stroke="null" fill-rule="evenodd" fill="#1d1d1d" id="path60" d="m583.46534,68.34197c0.47167,-0.12671 10.60481,-1.48947 20.21426,-0.53405c9.51404,0.94657 8.21885,3.7798 13.59238,9.0944l-0.33826,-14.95168l-43.18878,0.59125l0.36589,75.29958l43.03098,0.38143l4.57591,-16.16722c-1.63863,1.20669 -1.13049,0.81662 -2.64953,2.17441l-4.50122,4.36587c-3.11407,2.54894 -5.12377,3.15746 -9.4795,3.75756c-6.97374,0.96233 -17.60099,1.01262 -24.71548,-0.43518l-0.57377,-31.0183c8.1597,0 23.68019,-1.69411 28.62889,3.20603c3.68978,3.65503 2.51763,8.48415 3.80937,5.82598c0.11614,-0.24285 0.4231,0.76114 0.63464,1.10091l-0.48721,-23.70458c-9.43438,10.13659 -2.81078,8.51372 -32.8251,8.45997l1.51192,-26.51709l2.39459,-0.9293z"/>
   <path stroke="null" fill-rule="evenodd" fill="#1d1d1d" id="path62" d="m847.81893,62.22975c23.22773,16.93699 23.20528,9.11361 23.13944,31.99099c-0.02612,9.11188 0.50275,18.91216 -0.15089,27.93554c-0.94333,13.00954 -3.33792,10.36886 -9.90605,16.15319l27.07531,-0.15089c-5.53284,-4.20117 -6.32528,-0.88246 -9.26256,-7.84411c-3.14882,-7.46634 -2.26809,-38.2541 -1.52595,-45.9648l32.25996,27.09258c6.49171,5.31093 26.43203,24.26475 33.66416,27.26959c-0.21327,-9.90087 -2.76372,-7.70034 -9.18981,-13.60619l-69.59318,-59.60575c-4.22901,-3.69669 -8.66439,-4.22038 -16.51044,-3.27014z"/>
   <path stroke="null" fill-rule="evenodd" fill="#1d1d1d" id="path64" d="m508.23527,114.846c-5.52075,-8.39219 -9.56779,-17.32728 -13.93215,-25.81488c-6.75528,-13.13798 -12.75137,-19.22063 -4.95734,-27.32161l-14.42627,0.78553c0.79935,6.0861 28.55787,56.99616 33.23956,65.40044c3.78865,-4.00538 14.65163,-28.09484 17.83521,-34.9682c15.05745,-32.50259 10.78678,-19.23617 20.54367,-30.89872l-23.03712,0.23939c4.61735,4.27586 6.73456,4.78746 4.31061,13.26103c-0.4544,1.58834 -15.6159,35.15558 -19.57616,39.31702z"/>
   <path stroke="null" fill-rule="evenodd" fill="#1d1d1d" id="path66" d="m773.95529,217.23471c-3.70727,-3.37786 -10.67064,-18.65031 -14.22184,-25.10576c-6.34967,-11.53929 -11.61549,-18.67773 -5.70812,-27.99943l-14.32222,0.65019c2.13965,8.31944 12.61602,24.6097 16.82258,33.50313l16.71854,30.48944c0.10405,-0.23227 0.45958,0.59989 0.71106,0.88073c6.77082,-9.16024 18.59635,-39.28205 25.37775,-51.33639l11.88273,-13.81083l-21.98111,-0.35035l4.39372,7.11621c1.5484,6.76931 -17.61675,42.92348 -19.67308,45.96308z"/>
   <path stroke="null" fill-rule="evenodd" fill="#1d1d1d" id="path68" d="m910.58387,196.99282c-0.8272,-38.38578 5.29021,-26.20149 6.94438,-31.96682c0.16471,-0.57053 0.67458,-0.52542 0.97442,-0.90512l-13.93733,0.73351c-0.34841,22.62763 -0.9606,52.88997 0.59816,75.51609l14.03274,0.72142c-11.18721,-12.24516 -8.47184,-1.13934 -8.71642,-38.75685c5.04045,3.29259 5.83289,6.42415 10.23719,10.8768c4.70241,4.75616 5.50693,5.44455 9.09094,9.98398c3.32734,4.21692 6.04271,5.91449 9.25738,9.75689c5.08405,6.07747 2.65471,7.28782 12.93356,7.39187c-2.75012,-7.13175 -2.42244,-3.02751 -10.32569,-12.88153l-26.11471,-28.16932c-5.83634,-4.52734 -2.83323,3.40527 -4.30543,-2.12929c-0.06757,-0.25494 -0.468,-0.06239 -0.66918,-0.17161z"/>
   <path stroke="null" fill-rule="evenodd" fill="#1d1d1d" id="path70" d="m444.73337,126.48588c2.25773,-2.08245 3.02578,-2.01661 4.18066,-6.43818c0.80971,-3.10544 0.77841,-6.03926 0.15262,-9.17772c-3.22524,-16.19312 -24.52487,-17.90795 -36.28779,-23.95757c-13.69124,-7.03979 -7.90153,-18.91043 7.32063,-19.79116c24.65654,-1.42881 19.51895,11.31047 25.41941,15.93473l-0.68839,-19.33849c-11.07107,-2.31645 -24.27835,-4.71104 -34.87797,-0.31559c-9.19672,3.81477 -15.56905,17.07752 -6.2752,25.25989c15.25346,13.4294 43.85645,4.139 41.05603,37.8241z"/>
   <path stroke="null" fill-rule="evenodd" fill="#1d1d1d" id="path72" d="m405.82239,234.66754c-3.82513,-8.2223 -2.82978,-26.83246 -2.8369,-37.67645c-0.02072,-34.14295 3.13328,-24.09141 9.14146,-33.06967l-15.08357,1.31268l0.44922,75.02543l45.2313,0.645l4.78918,-18.24794c-10.36217,13.40846 -16.57132,13.93388 -41.69068,12.01095z"/>
   <path stroke="null" fill-rule="evenodd" fill="#1d1d1d" id="path74" d="m821.791,237.56683c0.05893,-0.19601 0.24609,0.8218 0.43518,1.20669c10.22488,1.89357 24.19869,5.48103 33.65553,0.57399c6.82306,-3.54062 14.65012,-16.01115 7.19759,-25.70025c-13.45358,-17.48853 -38.41708,-2.37041 -43.06228,-35.53183c-13.67721,32.04992 30.87799,26.22567 38.62172,39.99657c6.62706,11.78213 -7.14211,19.7495 -18.11605,17.9358c-15.72685,-2.60096 -16.84698,-16.13246 -20.01825,-20.16569c-0.91225,10.41053 0.77668,19.9768 0.88591,20.45365c0.07469,0.33114 0.34171,1.4286 0.40065,1.23108z"/>
   <path stroke="null" fill-rule="evenodd" fill="#1d1d1d" id="path76" d="m394.40442,113.35481l1.04889,23.1116c11.8307,1.28483 24.52832,5.91449 35.15385,0.52542c7.0657,-3.58595 13.99626,-16.43597 6.45199,-25.88072c-13.22476,-16.5521 -40.33483,-4.0384 -42.61846,-35.46059c-13.54014,32.06719 30.04389,26.09377 38.2228,39.91497c6.26484,10.58754 -5.25027,20.35997 -18.08993,18.10051c-16.05799,-2.82805 -14.80101,-15.2377 -20.16914,-20.31118z"/>
   <path stroke="null" fill-rule="evenodd" fill="#1d1d1d" id="path78" d="m933.61042,180.48044c-5.08902,3.73662 -12.47722,9.03029 -15.65562,13.79011l30.61248,33.68165c13.68952,14.65163 11.30011,12.81375 25.02416,13.03048c-27.26247,-19.77216 -8.8101,-6.5161 -25.60657,-22.77873c-4.47359,-4.33133 -19.93514,-20.77248 -21.76784,-25.89453c6.16403,-3.03442 14.32222,-10.71576 20.19677,-15.17532c9.56952,-7.2671 19.88657,-10.8632 20.89035,-12.80339l-28.88038,-0.14916c3.5894,5.54514 8.13229,6.79176 -4.81336,16.29889z"/>
   <path stroke="null" fill-rule="evenodd" fill="#1d1d1d" id="path80" d="m680.08354,67.75762c33.90701,-1.31268 25.74191,2.69961 32.21312,12.94543c2.15865,-4.46301 1.4286,-13.05984 1.543,-18.80617l-39.71745,0.59125c-0.02763,18.17671 -1.05062,57.15741 0.23594,75.49537l15.37132,0.74042l-5.41347,-4.85848c-6.89236,-7.39878 -4.21865,-40.36246 -4.22901,-54.31878l-0.00345,-11.78904z"/>
   <path stroke="null" fill-rule="evenodd" fill="#1d1d1d" id="path82" d="m831.34497,175.97922c7.2304,-5.82253 12.36454,-8.30045 23.76178,-5.73597c8.90012,2.00107 10.7313,4.47186 13.35644,9.97016l2.22816,-14.09513c-11.66427,-2.22104 -24.34268,-4.99361 -35.341,-0.27048c-10.0483,4.31579 -15.17899,18.30342 -4.66441,25.83215c18.75781,13.43285 44.23443,5.89355 39.19916,38.55739c11.34673,-12.93852 3.64985,-26.58465 -8.9176,-32.187c-19.1427,-8.53617 -31.19704,-6.42069 -29.62252,-22.07113z"/>
   <path stroke="null" fill-rule="evenodd" fill="#1d1d1d" id="path84" d="m454.94465,61.71664c1.80334,1.8083 -0.45785,-0.10232 2.72055,2.21586l23.44101,35.38094c3.6274,7.41086 17.94616,37.39561 22.58964,40.28777c3.88061,-8.90875 -7.93628,-24.21424 -15.74758,-39.77293c-5.4411,-10.83708 -13.59929,-27.66139 -19.98177,-37.34704l-13.02185,-0.76459z"/>
   <path stroke="null" fill-rule="evenodd" fill="#1d1d1d" id="path86" d="m981.43922,163.93524c10.48694,12.10636 8.86903,5.7759 8.89343,38.92868c0.02245,33.32266 0.34495,28.07563 -9.77243,38.32318l15.70073,-0.77668l-0.22882,-75.49882l-14.59291,-0.97636z"/>
   <path stroke="null" fill-rule="evenodd" fill="#1d1d1d" id="path88" d="m720.81189,164.28559c5.24164,5.4411 7.293,2.65816 15.17877,17.80909c3.3431,6.42436 6.82825,13.31154 10.5979,20.22807c3.25287,5.96997 16.34056,36.42486 22.91732,39.11411c2.47273,-7.96218 -3.81973,-14.5927 -7.48879,-22.21166c-3.67769,-7.6345 -7.21313,-13.74478 -11.18915,-21.09499c-17.30116,-31.98236 -12.06297,-33.92277 -30.01604,-33.84463z"/>
   <path stroke="null" fill-rule="evenodd" fill="#1d1d1d" id="path90" d="m354.89506,189.65125c0.01382,-21.44706 1.46335,-18.18901 8.82046,-25.33458l-14.59982,0.53232l-0.09347,75.4278l15.61741,0.83583c-3.62049,-3.54925 -6.58022,-4.47877 -8.34362,-9.60254c-2.24888,-6.52819 -1.40615,-33.96076 -1.40096,-41.85883z"/>
   <path stroke="null" fill-rule="evenodd" fill="#1d1d1d" id="path92" d="m837.39977,138.49683c-4.28622,-4.6452 -8.11307,-5.94579 -9.09958,-14.88066c-0.73351,-6.64433 -1.94214,-49.4534 2.68061,-56.02498l3.89442,-3.64488c0.23745,-0.19406 0.70566,-0.54959 0.9034,-0.70739l-13.65649,-0.76977l0.19061,75.55775l15.08703,0.46994z"/>
   <path stroke="null" fill-rule="evenodd" fill="#1d1d1d" id="path94" d="m537.37577,164.40863c12.59702,12.91607 8.77189,21.50945 8.76326,51.66775c-0.00432,21.89952 -2.62686,18.80271 -9.48123,24.99114l15.28649,-0.78726l-0.12499,-75.3149l-14.44353,-0.55672z"/>
   <path stroke="null" fill-rule="evenodd" fill="#1d1d1d" id="path96" d="m616.47263,194.05555c0,-6.0289 -0.25666,-12.61969 1.05752,-18.45603c1.4532,-6.4589 4.60376,-7.61378 7.95355,-11.31738l-14.95168,0.54959l0.80626,75.63762l14.76453,0.7143c-12.39756,-13.443 -9.61809,-7.74028 -9.63017,-47.1281z"/>
   <path stroke="null" fill-rule="evenodd" fill="#1d1d1d" id="path98" d="m328.70393,164.22666c0.28257,0.29293 0.70048,0.62407 0.91203,0.80971c10.83362,9.54879 8.06623,14.24256 8.0576,39.29608c-0.00863,32.49396 0.70739,27.20203 -9.67356,36.73161l15.28109,-0.80626l0.19601,-75.32721l-14.77316,-0.70394z"/>
   <path stroke="null" fill-rule="evenodd" fill="#1d1d1d" id="path100" d="m558.24193,66.29233c2.16901,2.30263 2.13447,1.79643 3.25805,5.32323c1.90738,5.98723 1.94365,48.85697 0.42828,55.79098c-1.45299,6.65145 -4.31752,7.48707 -8.06969,11.09029l14.7315,-0.53232l-0.58953,-75.37923l-14.605,-0.78381l4.84639,4.49086z"/>
   <path stroke="null" fill-rule="evenodd" fill="#1d1d1d" id="path102" d="m885.37773,164.33761c4.07122,4.11806 5.33705,1.89681 7.07606,8.07832l0.70588,48.06474c-0.02785,12.13054 0.36568,14.3533 -8.55365,20.46034l14.02756,-0.5306l0.35898,-75.56315l-13.61483,-0.50966z"/>
   <path stroke="null" fill-rule="evenodd" fill="#1d1d1d" id="path104" d="m647.78041,230.97776c-8.57783,-26.15637 -8.29332,-31.69267 0.23227,-57.85077c-10.33238,4.4248 -13.4171,18.75242 -13.28887,29.71944c0.15262,12.94392 4.95216,24.40161 13.0566,28.13132z"/>
   <path stroke="null" fill-rule="evenodd" fill="#1d1d1d" id="path106" d="m708.41087,172.61885c10.69482,15.19798 6.22469,41.41502 0.75423,58.15255c8.04896,-4.06085 13.23512,-17.11379 12.77382,-30.86051c-0.48203,-14.41936 -5.39771,-22.28958 -13.52805,-27.29204z"/>
   <path stroke="null" fill-rule="evenodd" fill="#979795" id="path192" d="m591.16935,166.83971l1.01435,-2.64089l-3.08989,0.76826l2.07554,1.87263z"/>
  </g>
 </g>
</svg>