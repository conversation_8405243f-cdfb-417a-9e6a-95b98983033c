<?php
use TF\Application\Common\MTApplication;
use Noodlehaus\Config;
use TF\Engine\Kernel\Sentry\Sentry;

/**
 * Application index file.
 * 
 * <AUTHOR> Ltd. <<EMAIL>>
 * @link http://www.devision.bg/
 * @copyright Copyright &copy; 2008 Devision Ltd.
 */
$dir = realpath(dirname(__FILE__)) . '/../';

mb_internal_encoding('UTF-8');
mb_regex_encoding('UTF-8');

if(session_id() == '' || !isset($_SESSION)){
    @session_start();
}
/**
 * Requires global configuration file and framework files.
 */
include '../vendor/autoload.php';


try {
	$dotenv = new Dotenv\Dotenv($dir);
	$dotenv->overload();
	$dotenv->required('ENV_NAME')->notEmpty();
	$dotenv->required('APPLICATION_MODE')->allowedValues(['Off', 'Debug', 'Normal', 'Performance']);
	$dotenv->required('DEFAULT_DB_USERNAME')->notEmpty();
	$dotenv->required('DEFAULT_DB_PASSWORD')->notEmpty();
	$dotenv->required('DBLINK_USERNAME')->notEmpty();
	$dotenv->required('DBLINK_PASSWORD')->notEmpty();
} catch (Exception $e) {
	echo $e->getMessage();
	die;
}

require_once($dir.'config/global.config.php');
require_once($dir.'config/templates.config.php');

try {
    Sentry::init(getenv('SENTRY_DSN'), getenv('APPLICATION_MODE'));
} catch (Exception $exception) {
	die($exception->getMessage());
    //let the application to initialize without third party library dependencies
}

if (file_exists($dir.FRAMEWORK_PATH)) require_once($dir.FRAMEWORK_PATH);
else require_once(FRAMEWORK_PATH);
if(isset($_GET['json'])){
    $page = $_POST['page'];
    unset($_POST['page']);
    $_POST['pager'] = $page;
};

$a = Prado::setPathOfAlias('Common', $dir.'protected/Common');

/**
 * Starts application.
 */
$application=new MTApplication('../protected/');
$application->run();