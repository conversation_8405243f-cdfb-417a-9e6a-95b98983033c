ENV_NAME=dev
# APP_ENV=dev
APPLICATION_MODE=Debug
SITE_PATH=/var/www/html/app/
#SITE_BASE_HREF=/
MACHINE_NAME=dev
APP_UNIQUE_KEY=UKR8NwCJzcP9We4E4Uq98qg7GgCWJ2KcfMNtqLxgnw5sfEUJv5
# (seconds). Set 0 if you want to disable Prado expire session manager
USER_SESSION_EXPIRATION_TIME=259200

#DEFAULT_DB_USERNAME=postgres
#DEFAULT_DB_PASSWORD=6nuk23
#DEFAULT_DB_HOST=***********
#DEFAULT_DB_PORT=25432
#DEFAULT_DB_DATABASE=susi_main

#DBLINK_DRIVER=pgsql
#DBLINK_HOST=***********
#DBLINK_PORT=25432
#DBLINK_DATABASE=susi_main
#DBLINK_USERNAME=postgres
#DBLINK_PASSWORD=6nuk23

#AO_DB_CONTRACTS_HOST=***********
#AO_DB_CONTRACTS_USER=
#AO_DB_CONTRACTS_PASS=

#AO_DB_MAP_HOST=***********
#AO_DB_MAP_USER=
#AO_DB_MAP_PASS=

#Copy user account command params

#COPY_USER_SOURCE_DB_HOST=
#COPY_USER_SOURCE_DB_PORT=
##COPY_USER_SOURCE_DB_USER=
#COPY_USER_SOURCE_DB_PASS=
#COPY_USER_COMMAND_SSH_HOST=
#COPY_USER_COMMAND_SSH_USER=

OGRINFO_PATH=/usr/bin/ogrinfo
OGR2OGR_PATH=/usr/bin/ogr2ogr
# CSV2XLS_PATH=/usr/local/bin/

WKHTMLTOPDF_PATH='/usr/bin/wkhtmltopdf'

OPEN_LAYERS_PROXY=
#WMS_SERVER=http://staging.technofarm.bg:6005/mapserv
# WMS_SERVER_INTERNAL is used when calling Mapserver from php from Docker
#WMS_SERVER_INTERNAL=http://technofarm-api-mapserver:5000/mapserv
WMS_MAP_PATH=/var/www/html/app/maps/

#LOGIN3_WMS_SERVER=http://staging.technofarm.bg:6004/mapcache
LOGIN3_WMS_MAP_PATH=/var/www/maps/

MAPSERVER_COMMANDS_PATH=
LOG_PATH=/var/www/html/app/logs/group_logs/

#CSS_CUSTOM_THEME_NAME=
SHAPES_UPLOAD_PATH="shps"

JD_EXPORTER=/var/www/html/app/bin/adapt_converter/ADAPTConverter.exe
JD_EXPORT_PATH=/var/www/html/app/bin/adapt_converter/Output/
# COMMON_SERVICES_API_URL=http://common-services.technofarm.bg

AB_LINES_EXPORT_PATH=/var/www/html/app/bin/smart_converter/SmartConvert.exe
AB_LINES_EXPORTER=/var/www/html/app/bin/smart_converter/Output/
SLOPE_FILE=SLOPE_BG_FULL.tif

SMART_CONVERTER=/var/www/html/app/bin/smart_converter/SmartConvert.exe

#RPI_SERVER=http://**************/
#RPI_USERNAME=tfqa
#RPI_PASSWORD=qwerty

#WAREHOUSE_API_URL=
JWT_SECRET_KEY=/var/www/html/app/config/jwt/private.pem

#PHPMAILER_HOST=
#PHPMAILER_PORT=
#PHPMAILER_USERNAME=
#PHPMAILER_PASSWORD=

TEMPLATE_PATH=/var/www/html/app/template/
DEBUG_LOG_PATH=/var/www/html/app/logs/debug/

SENTRY_DSN=https://<EMAIL>/5620999
DISABLE_SENTRY_LOG_ERROR=false

# CONTAINER_NAME=technofarm-api
API_EXTERNAL_PORT=8066
MAPSERVER_EXTERNAL_PORT=5005
MAPCACHE_EXTERNAL_PORT=5004
USER_ID=1000
GROUP_ID=1000

#GEOSCAN_APP_URL=http://localhost:4200

# KEYKLOACK_LOGIN_REQUIRED=true
# KEYCLOAK_AUTH_SERVER_URL=http://keycloak.geoscan.info:8081
# KEYCLOAK_REALM=geotech
# KEYCLOAK_CLIENT_ID=technofarm-idc
# KEYCLOAK_CLIENT_SECRET=CmKsuvRxUuHQjuDJ5uWY8cmbtgEeZJ88
# KEYCLOAK_REDIRECT_URI=http://localhost:8067/index.php?keycloak=KeycloakLogin
# KEYCLOAK_LOGOUT_REDIRECT_URI=http://localhost:8067
# KEYCLOAK_ALGORYTHM=RS256
# CMS_API_URL=http://localhost:8099
#GEOSCAN_CMS_BASE_URI=http://geoscan-cms-api-nginx/
# SECURED_COOKIES=true

# KEYKLOACK_KVS_STORE_AUTH_SERVER_URL=http://keycloak.geoscan.info:8081
#KEYKLOACK_KVS_STORE_CLIENT_ID=technofarm-app
#KEYKLOACK_KVS_STORE_CLIENT_SECRET=V96GZ8QFd8Ism3Gk9WpKAEwOSrwKgEmh
#KEYKLOACK_KVS_STORE_REALM=kvs-store

#KVS_STORE_URL=http://localhost:8055
#KVS_STORE_CALLBACK_URL=http://technofarm-api-nginx

#KAIS_SESSION_ID=x2lwzykicof0ejes4oya3aml
#KAIS_CA_TOKEN=ZWVpQmJYb3JWRFhRcWFsNW5tZTZYSTArUG9udHhRZ1RLSU9BcmVURkNFUXBLaGxnRzdBc2E3Z3dSWXJFTFpqWjdKTU1mOSt5bWI1SSszRFhuRGZOSXRjYTdOR1hJUGd6bndhM1kyMFo3L2xFUjFIWHFvUm83cXpmbkZPYUNjaUNRN09pWGZKT2pNST0=

PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser
# LEGACY_MODE=false
# MAIN_NAVIGATION_INSTANCE=web

#KEYCLOAK_M2M_CLIENT_ID=technofarm-m2m
#KEYCLOAK_M2M_CLIENT_SECRET=82pEmBMbj92kjEz2YJDE9JQUOWCDSOjl
#POSTHOG_API_KEY=

#Mapserver ENV
# APACHE_RUN_USER=appuser
# APACHE_RUN_GROUP=appgroup
# CONFD_DIRS=/var/www/html/app/maps
# MAPSERVER_CATCH_SEGV=1
# MS_DEBUGLEVEL=5

# MAX_KVS_PROCESSING_FILES=10


# ALARMS_MAIL=