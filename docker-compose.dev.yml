version: "3.7"

services:
    tf-php:
        env_file:
            - .env
        build:
            context: .
            dockerfile: ./.docker/Dockerfile
            args:
                - APP_ENV=${APP_ENV:-dev}
                - USER_ID=${USER_ID}
                - GROUP_ID=${GROUP_ID}
        image: ${CONTAINER_NAME}
        container_name: ${CONTAINER_NAME}
        user: "1000:1000"
        volumes:
            - .:/var/www/html/app
            - maps:/var/www/html/app/maps/
            - keydata:/var/www/html/app/config/jwt
            - tf-mapcache-data:/var/www/html/app/static_maps
            - tf-mapcache-config:/var/www/html/app/.docker/mapcache
        restart: always
        healthcheck:
            test: "exit 0"
        networks:
            - geoscan-net
    tf-crontab:
        image: ${CONTAINER_NAME}
        container_name: ${CONTAINER_NAME}-crontab
        depends_on:
            - tf-php
        volumes:
            - .:/var/www/html/app
        networks:
            - geoscan-net
        restart: always
        healthcheck:
            test: "exit 0"
    tf-mapserver:
        build:
            context: .
            dockerfile: .docker/mapserver/Dockerfile
        environment:
            - MS_MAP_PATTERN=${MS_MAP_PATTERN}
            - MS_DEBUGLEVEL=${MS_DEBUGLEVEL:-0}
            - MIN_PROCESSES=${MIN_PROCESSES:-1}
            - MAX_PROCESSES=${MAX_PROCESSES:-2}
            - APACHE_RUN_USER=${APACHE_RUN_USER:-appuser}
            - APACHE_RUN_GROUP=${APACHE_RUN_GROUP:-appgroup}
            - MAPSERVER_CATCH_SEGV=${MAPSERVER_CATCH_SEGV:-0}
        image: ${CONTAINER_NAME}-mapserver
        container_name: ${CONTAINER_NAME}-mapserver
        networks:
            - geoscan-net
        ports:
            - ${MAPSERVER_EXTERNAL_PORT}:8080
        volumes:
            - maps:/var/www/html/app/maps/
            - tf-mapcache-data:/var/www/html/app/static_maps
        restart: always
        healthcheck:
            test: "exit 0"
    tf-mapcache:
        image: camptocamp/mapcache:1.6
        container_name: ${CONTAINER_NAME}-mapcache
        volumes:
            - tf-mapcache-config:/etc/mapcache
            - tf-mapcache-config:/var/sig/tiles
            - tf-mapcache-data:/var/www/html/app/static_maps
        networks:
            - geoscan-net
        restart: always
        ports:
            - ${MAPCACHE_EXTERNAL_PORT}:80
        healthcheck:
            test: "exit 0"
    tf-web:
        container_name: ${CONTAINER_NAME}-nginx
        image: ${CONTAINER_NAME}-nginx
        build:
            context: .
            dockerfile: ./.docker/nginx/Dockerfile
        working_dir: /etc/nginx
        environment:
            - CONTAINER_NAME=${CONTAINER_NAME}
            - MAPSERVER_EXTERNAL_PORT=${MAPSERVER_EXTERNAL_PORT}
            - MAPCACHE_EXTERNAL_PORT=${MAPCACHE_EXTERNAL_PORT}
        ports:
            - ${API_EXTERNAL_PORT}:80
        volumes:
            - .:/var/www/html/app
        networks:
            - geoscan-net
        depends_on:
            - tf-php
            - tf-mapserver
        restart: always
        healthcheck:
            test: "exit 0"
    tf-gdal:
        build:
            context: ./.docker/gdal
            args:
                - ALPINE_VERSION=3.14
                - GDAL_VERSION=v3.2.1
                - PROJ_VERSION=7.2.1
                - MAPSERVER_VERSION=7-6
        image: ${CONTAINER_NAME}-gdal
        networks:
            - geoscan-net
        container_name: ${CONTAINER_NAME}-gdal
        profiles:
            - tools
networks:
    geoscan-net:
        external: true

volumes:
    app:
        name: ${CONTAINER_NAME}-app
    maps:
        name: ${CONTAINER_NAME}-maps
    keydata:
        name: ${CONTAINER_NAME}-key-data
        external: true
    crons:
        name: ${CONTAINER_NAME}-crons
    tf-mapcache-config:
        name: tf-mapcache-config
        external: true
    tf-mapcache-data:
        name: tf-mapcache
        external: true
